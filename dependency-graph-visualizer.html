<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>依赖关系图可视化</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        #network {
            width: 100%;
            height: 600px;
            border: 1px solid #e5e7eb;
        }
        .highlight-path {
            stroke: #ef4444 !important;
            stroke-width: 3px !important;
        }
        .highlight-node {
            border: 3px solid #ef4444 !important;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto p-6">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h1 class="text-3xl font-bold text-gray-800 mb-4">
                <i class="fas fa-project-diagram text-blue-600 mr-3"></i>
                依赖关系图可视化
            </h1>
            
            <!-- File Upload -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-upload mr-2"></i>选择 JSON 文件
                </label>
                <input type="file" id="fileInput" accept=".json" 
                       class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
            </div>

            <!-- Controls -->
            <div class="flex flex-wrap gap-4 mb-4">
                <button id="resetView" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                    <i class="fas fa-refresh mr-2"></i>重置视图
                </button>
                <button id="fitNetwork" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-expand-arrows-alt mr-2"></i>适应窗口
                </button>
                <button id="togglePhysics" class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                    <i class="fas fa-play mr-2"></i>切换物理引擎
                </button>
            </div>

            <!-- Stats -->
            <div id="stats" class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div class="bg-blue-50 p-3 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600" id="totalModules">0</div>
                    <div class="text-sm text-gray-600">总模块数</div>
                </div>
                <div class="bg-green-50 p-3 rounded-lg">
                    <div class="text-2xl font-bold text-green-600" id="entryPoints">0</div>
                    <div class="text-sm text-gray-600">入口点</div>
                </div>
                <div class="bg-yellow-50 p-3 rounded-lg">
                    <div class="text-2xl font-bold text-yellow-600" id="packages">0</div>
                    <div class="text-sm text-gray-600">包数量</div>
                </div>
                <div class="bg-red-50 p-3 rounded-lg">
                    <div class="text-2xl font-bold text-red-600" id="selectedNodes">0</div>
                    <div class="text-sm text-gray-600">选中节点</div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Entry Points -->
                <div class="bg-white rounded-lg shadow-md p-4 mb-4">
                    <h3 class="text-lg font-semibold text-gray-800 mb-3">
                        <i class="fas fa-door-open text-green-600 mr-2"></i>入口点
                    </h3>
                    <div id="entryPointsList" class="space-y-2 max-h-60 overflow-y-auto">
                        <!-- Entry points will be populated here -->
                    </div>
                </div>

                <!-- Search -->
                <div class="bg-white rounded-lg shadow-md p-4 mb-4">
                    <h3 class="text-lg font-semibold text-gray-800 mb-3">
                        <i class="fas fa-search text-blue-600 mr-2"></i>搜索模块
                    </h3>
                    <input type="text" id="searchInput" placeholder="输入模块名称..." 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <div id="searchResults" class="mt-2 space-y-1 max-h-40 overflow-y-auto">
                        <!-- Search results will be populated here -->
                    </div>
                </div>

                <!-- Color Legend -->
                <div class="bg-white rounded-lg shadow-md p-4 mb-4">
                    <h3 class="text-lg font-semibold text-gray-800 mb-3">
                        <i class="fas fa-palette text-indigo-600 mr-2"></i>颜色图例
                    </h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex items-center">
                            <div class="w-4 h-4 rounded-full bg-green-500 border-2 border-green-600 mr-3"></div>
                            <span>入口点模块</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-4 h-4 rounded-full bg-yellow-500 border-2 border-yellow-600 mr-3"></div>
                            <span>高依赖度模块 (>20)</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-4 h-4 rounded-full bg-blue-500 border-2 border-blue-600 mr-3"></div>
                            <span>叶子节点 (无依赖)</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-4 h-4 rounded-full bg-purple-500 border-2 border-purple-600 mr-3"></div>
                            <span>普通模块</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-4 h-4 rounded-full bg-red-500 border-2 border-red-600 mr-3"></div>
                            <span>高亮选中</span>
                        </div>
                    </div>
                </div>

                <!-- Selected Node Info -->
                <div class="bg-white rounded-lg shadow-md p-4">
                    <h3 class="text-lg font-semibold text-gray-800 mb-3">
                        <i class="fas fa-info-circle text-purple-600 mr-2"></i>节点信息
                    </h3>
                    <div id="nodeInfo" class="text-sm text-gray-600">
                        点击节点查看详细信息
                    </div>
                </div>
            </div>

            <!-- Network Visualization -->
            <div class="lg:col-span-3">
                <div class="bg-white rounded-lg shadow-md p-4">
                    <div class="flex justify-between items-center mb-4">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-800">
                                <i class="fas fa-sitemap text-indigo-600 mr-2"></i>依赖关系图
                            </h3>
                            <p class="text-sm text-gray-600 mt-1">
                                箭头方向：依赖者 → 被依赖者 | 点击入口点高亮依赖链路
                            </p>
                        </div>
                        <div class="flex gap-2">
                            <button id="zoomIn" class="p-2 bg-gray-200 rounded hover:bg-gray-300 transition-colors">
                                <i class="fas fa-plus"></i>
                            </button>
                            <button id="zoomOut" class="p-2 bg-gray-200 rounded hover:bg-gray-300 transition-colors">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <div id="network"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Modal -->
    <div id="loadingModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-6 text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p class="text-gray-700">正在加载依赖关系图...</p>
        </div>
    </div>

    <script>
        let network = null;
        let nodes = null;
        let edges = null;
        let graphData = null;
        let allNodes = [];
        let allEdges = [];

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeNetwork();
            setupEventListeners();
        });

        function initializeNetwork() {
            const container = document.getElementById('network');
            const data = { nodes: [], edges: [] };
            const options = {
                nodes: {
                    shape: 'dot',
                    size: 10,
                    font: { size: 12 },
                    borderWidth: 2,
                    shadow: true
                },
                edges: {
                    width: 1,
                    color: { inherit: 'from' },
                    smooth: { type: 'continuous' },
                    arrows: { to: { enabled: true, scaleFactor: 0.5 } }
                },
                physics: {
                    enabled: true,
                    stabilization: { iterations: 100 }
                },
                interaction: {
                    hover: true,
                    selectConnectedEdges: false
                }
            };

            network = new vis.Network(container, data, options);
            
            // Network event listeners
            network.on('click', function(params) {
                if (params.nodes.length > 0) {
                    const nodeId = params.nodes[0];
                    selectNode(nodeId);
                    showNodeInfo(nodeId);
                }
            });

            network.on('hoverNode', function(params) {
                document.body.style.cursor = 'pointer';
            });

            network.on('blurNode', function(params) {
                document.body.style.cursor = 'default';
            });
        }

        function setupEventListeners() {
            // File input
            document.getElementById('fileInput').addEventListener('change', handleFileSelect);
            
            // Control buttons
            document.getElementById('resetView').addEventListener('click', resetView);
            document.getElementById('fitNetwork').addEventListener('click', () => network.fit());
            document.getElementById('togglePhysics').addEventListener('click', togglePhysics);
            document.getElementById('zoomIn').addEventListener('click', () => network.moveTo({ scale: network.getScale() * 1.2 }));
            document.getElementById('zoomOut').addEventListener('click', () => network.moveTo({ scale: network.getScale() * 0.8 }));
            
            // Search
            document.getElementById('searchInput').addEventListener('input', handleSearch);
        }

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (!file) return;

            showLoading(true);
            const reader = new FileReader();

            reader.onload = function(e) {
                try {
                    graphData = JSON.parse(e.target.result);
                    processGraphData();
                    updateStats();
                    populateEntryPoints();
                    showLoading(false);
                } catch (error) {
                    alert('JSON 文件格式错误: ' + error.message);
                    showLoading(false);
                }
            };

            reader.readAsText(file);
        }

        function processGraphData() {
            if (!graphData || !graphData.modules) return;

            allNodes = [];
            allEdges = [];

            // Create nodes
            Object.keys(graphData.modules).forEach(moduleId => {
                const module = graphData.modules[moduleId];
                const color = getNodeColor(module);

                allNodes.push({
                    id: moduleId,
                    label: module.name,
                    color: color,
                    size: module.isEntryPoint ? 20 : (module.dependents.length > 10 ? 15 : 10),
                    title: createNodeTooltip(module),
                    font: {
                        color: module.isEntryPoint ? '#ffffff' : '#333333',
                        size: module.isEntryPoint ? 14 : 12
                    }
                });
            });

            // Create edges (fix arrow direction: from dependent to dependency)
            Object.keys(graphData.modules).forEach(moduleId => {
                const module = graphData.modules[moduleId];
                module.dependencies.forEach(depId => {
                    allEdges.push({
                        from: moduleId,  // Changed: from the module that depends
                        to: depId,       // Changed: to the dependency
                        color: { color: '#94a3b8', highlight: '#ef4444' }
                    });
                });
            });

            // Update network
            nodes = new vis.DataSet(allNodes);
            edges = new vis.DataSet(allEdges);
            network.setData({ nodes: nodes, edges: edges });
        }

        function getNodeColor(module) {
            if (module.isEntryPoint) {
                return { background: '#10b981', border: '#059669' }; // Green for entry points
            } else if (module.dependents.length > 20) {
                return { background: '#f59e0b', border: '#d97706' }; // Orange for highly depended modules
            } else if (module.dependencies.length === 0) {
                return { background: '#6366f1', border: '#4f46e5' }; // Blue for leaf nodes
            } else {
                return { background: '#8b5cf6', border: '#7c3aed' }; // Purple for regular nodes
            }
        }

        function createNodeTooltip(module) {
            return `
                <div style="max-width: 300px;">
                    <strong>${module.name}</strong><br>
                    包名: ${module.packageName}<br>
                    入口点: ${module.isEntryPoint ? '是' : '否'}<br>
                    依赖数: ${module.dependencies.length}<br>
                    被依赖数: ${module.dependents.length}
                </div>
            `;
        }

        function updateStats() {
            if (!graphData) return;

            document.getElementById('totalModules').textContent = graphData.metadata.totalModules;
            document.getElementById('entryPoints').textContent = graphData.metadata.entryPoints;
            document.getElementById('packages').textContent = graphData.metadata.packagesCreated;
        }

        function populateEntryPoints() {
            if (!graphData) return;

            const entryPointsList = document.getElementById('entryPointsList');
            entryPointsList.innerHTML = '';

            const entryPoints = Object.keys(graphData.modules)
                .filter(id => graphData.modules[id].isEntryPoint)
                .map(id => ({ id, name: graphData.modules[id].name }))
                .sort((a, b) => a.name.localeCompare(b.name));

            entryPoints.forEach(ep => {
                const button = document.createElement('button');
                button.className = 'w-full text-left px-3 py-2 text-sm bg-green-50 hover:bg-green-100 rounded border border-green-200 transition-colors';
                button.innerHTML = `<i class="fas fa-play text-green-600 mr-2"></i>${ep.name}`;
                button.onclick = () => highlightDependencyChain(ep.id);
                entryPointsList.appendChild(button);
            });
        }

        function highlightDependencyChain(nodeId) {
            if (!graphData || !graphData.modules[nodeId]) return;

            resetHighlight();

            const visited = new Set();
            const pathNodes = new Set();
            const pathEdges = new Set();

            // Traverse dependency chain
            function traverse(currentId) {
                if (visited.has(currentId)) return;
                visited.add(currentId);
                pathNodes.add(currentId);

                const module = graphData.modules[currentId];
                if (module) {
                    module.dependencies.forEach(depId => {
                        pathEdges.add(`${currentId}-${depId}`); // Fixed: match the new edge direction
                        traverse(depId);
                    });
                }
            }

            traverse(nodeId);

            // Update node colors
            const updatedNodes = allNodes.map(node => {
                if (pathNodes.has(node.id)) {
                    return {
                        ...node,
                        color: { background: '#ef4444', border: '#dc2626' },
                        borderWidth: 3
                    };
                }
                return { ...node, color: { ...node.color, opacity: 0.3 } };
            });

            // Update edge colors
            const updatedEdges = allEdges.map(edge => {
                const edgeKey = `${edge.from}-${edge.to}`;
                if (pathEdges.has(edgeKey)) {
                    return {
                        ...edge,
                        color: { color: '#ef4444' },
                        width: 3
                    };
                }
                return { ...edge, color: { ...edge.color, opacity: 0.3 } };
            });

            nodes.update(updatedNodes);
            edges.update(updatedEdges);

            // Focus on the selected node
            network.focus(nodeId, { scale: 1.0, animation: true });

            // Update selected nodes count
            document.getElementById('selectedNodes').textContent = pathNodes.size;
        }

        function selectNode(nodeId) {
            network.selectNodes([nodeId]);
        }

        function showNodeInfo(nodeId) {
            if (!graphData || !graphData.modules[nodeId]) return;

            const module = graphData.modules[nodeId];
            const nodeInfo = document.getElementById('nodeInfo');

            nodeInfo.innerHTML = `
                <div class="space-y-3">
                    <div>
                        <h4 class="font-semibold text-gray-800">${module.name}</h4>
                        <p class="text-xs text-gray-500">ID: ${nodeId}</p>
                    </div>

                    <div class="grid grid-cols-2 gap-2 text-xs">
                        <div class="bg-gray-50 p-2 rounded">
                            <div class="font-medium">包名</div>
                            <div class="text-gray-600">${module.packageName}</div>
                        </div>
                        <div class="bg-gray-50 p-2 rounded">
                            <div class="font-medium">入口点</div>
                            <div class="text-gray-600">${module.isEntryPoint ? '是' : '否'}</div>
                        </div>
                    </div>

                    <div class="grid grid-cols-2 gap-2 text-xs">
                        <div class="bg-blue-50 p-2 rounded">
                            <div class="font-medium text-blue-700">依赖数</div>
                            <div class="text-blue-600 font-bold">${module.dependencies.length}</div>
                        </div>
                        <div class="bg-green-50 p-2 rounded">
                            <div class="font-medium text-green-700">被依赖数</div>
                            <div class="text-green-600 font-bold">${module.dependents.length}</div>
                        </div>
                    </div>

                    ${module.dependencies.length > 0 ? `
                        <div>
                            <div class="font-medium text-gray-700 mb-1">依赖模块:</div>
                            <div class="max-h-20 overflow-y-auto text-xs space-y-1">
                                ${module.dependencies.map(dep =>
                                    `<div class="bg-blue-50 px-2 py-1 rounded cursor-pointer hover:bg-blue-100"
                                          onclick="highlightDependencyChain('${dep}')">${graphData.modules[dep]?.name || dep}</div>`
                                ).join('')}
                            </div>
                        </div>
                    ` : ''}

                    ${module.dependents.length > 0 ? `
                        <div>
                            <div class="font-medium text-gray-700 mb-1">被依赖模块:</div>
                            <div class="max-h-20 overflow-y-auto text-xs space-y-1">
                                ${module.dependents.slice(0, 10).map(dep =>
                                    `<div class="bg-green-50 px-2 py-1 rounded cursor-pointer hover:bg-green-100"
                                          onclick="highlightDependencyChain('${dep}')">${graphData.modules[dep]?.name || dep}</div>`
                                ).join('')}
                                ${module.dependents.length > 10 ? `<div class="text-gray-500">... 还有 ${module.dependents.length - 10} 个</div>` : ''}
                            </div>
                        </div>
                    ` : ''}
                </div>
            `;
        }

        function handleSearch(event) {
            const query = event.target.value.toLowerCase().trim();
            const searchResults = document.getElementById('searchResults');

            if (query.length < 2) {
                searchResults.innerHTML = '';
                return;
            }

            if (!graphData) {
                searchResults.innerHTML = '<div class="text-gray-500 text-xs">请先加载 JSON 文件</div>';
                return;
            }

            const matches = Object.keys(graphData.modules)
                .filter(id => {
                    const module = graphData.modules[id];
                    return module.name.toLowerCase().includes(query) ||
                           id.toLowerCase().includes(query) ||
                           module.packageName.toLowerCase().includes(query);
                })
                .slice(0, 10)
                .map(id => ({ id, module: graphData.modules[id] }));

            if (matches.length === 0) {
                searchResults.innerHTML = '<div class="text-gray-500 text-xs">未找到匹配的模块</div>';
                return;
            }

            searchResults.innerHTML = matches.map(match => `
                <div class="px-2 py-1 text-xs bg-gray-50 hover:bg-gray-100 rounded cursor-pointer border border-gray-200"
                     onclick="selectAndFocus('${match.id}')">
                    <div class="font-medium">${match.module.name}</div>
                    <div class="text-gray-500">${match.module.packageName}</div>
                </div>
            `).join('');
        }

        function selectAndFocus(nodeId) {
            selectNode(nodeId);
            showNodeInfo(nodeId);
            network.focus(nodeId, { scale: 1.5, animation: true });

            // Clear search
            document.getElementById('searchInput').value = '';
            document.getElementById('searchResults').innerHTML = '';
        }

        function resetView() {
            resetHighlight();
            network.fit();
            document.getElementById('selectedNodes').textContent = '0';
            document.getElementById('nodeInfo').innerHTML = '点击节点查看详细信息';
        }

        function resetHighlight() {
            if (nodes && edges) {
                nodes.update(allNodes);
                edges.update(allEdges);
            }
        }

        function togglePhysics() {
            const currentPhysics = network.physics.physicsEnabled;
            network.setOptions({ physics: { enabled: !currentPhysics } });

            const button = document.getElementById('togglePhysics');
            const icon = button.querySelector('i');

            if (currentPhysics) {
                icon.className = 'fas fa-play mr-2';
                button.innerHTML = '<i class="fas fa-play mr-2"></i>启用物理引擎';
            } else {
                icon.className = 'fas fa-pause mr-2';
                button.innerHTML = '<i class="fas fa-pause mr-2"></i>禁用物理引擎';
            }
        }

        function showLoading(show) {
            const modal = document.getElementById('loadingModal');
            modal.classList.toggle('hidden', !show);
        }
    </script>
</body>
</html>
