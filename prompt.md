## 任务
cli-format-all.js是一个编译打包后的代码，并且里面包含了很多第三方依赖（npm包），我需要你帮我系统性地理解、分析并重构此代码

## 核心约束
完全保留原始逻辑​​：不改变代码的任何功能行为 

## ​要求
1. 识别构建工具产物​​：找出打包工具（Rollup、esbuild 等）生成的模块包装器和兼容性填充层，这部分是非源码，简化为标准 CommonJS require()和 module.exports模式（源码里面的模块包装器其实只是打包工具（如 Rollup、esbuild 等）为了兼容 CJS 模块而生成的辅助函数。真正的源码其实只需要require() 和 module.exports即可）
2. 识别打包的第三方库​​：定位文件中完整打包的第三方库源码（如 lodash、sentry 等），用标准的导入语句（import/require）替代源码（识别出来了第三方库，比如shell-quote，这个不需要放在源码里，你只需要在重构信息里记录下来让后续的进度可以查得到是使用的这个库就行了，后续的源码直接require即可）
3. ​变量重命名​​：基于上下文将压缩的变量名改为有意义的描述性名称
4. 分析模块边界​​：理解代码段间关系并划分逻辑模块边界
5. ​​拆分为模块化文件​​：将单文件拆解为逻辑模块，放入 src/目录结构

## 操作流程和过程管理
1. 这355224行的代码你不可能一次重构完，为了保证完全完整把代码逻辑重构，你要分多次一步步迭代完成，每次迭代都要保证代码逻辑的正确性，不能有遗漏。比如你一轮分10次，一次重构3000行（不一定固定，具体要看代码逻辑的边界），大概分成10轮。（不一定严格是10，具体情况看迭代情况）。举个例子，你上次已经重构了1023 - 3322 行，那下次就继续从3322行开始。
2. 每次分析或者重构完，需要记录重构的过程和信息以及重构进度，固定记录在REFACTORING_LOG.md文件。举个例子，假设你分析出1-3000行是某个npm包的源码（这个不需要重构，只需要引入），你就记录下1-3000行是xxx这个包的源码，以及导出的方法映射等，假设你重构的1234 - 2345是某个工具函数，你就记录下来1234 - 2345是xxx工具函数，重构前名字是$aW, 重构后是xxx。具体要记录的信息你根据实际情况来决定，原则就是要方便迭代工程中查找和索引。因为你重构完之后，可能后续的重构会引用到。所以要维护依赖映射关系，为避免REFACTORING_LOG.md太大，依赖映射关系单独记录在REFECTING_MAPPING.md文件中。最终分析重构过之后记录的行数总和必须等于355,224行才算重构完成。
3. 重构的时候，每次要先查找之前重构的信息，因为你当前代码行的依赖的函数有可能之前已经重构完或者之前已经重构完的代码依赖了你当前重构的函数之类的。你需要从REFECTING_MAPPING.md中找到对应关系。但可能你重构比如1234 - 4567行的时候，对应的依赖还没重构，此时你需要标注好。同理，在后续重构了某个模块或者方法之后，你要查找之前重构的代码，看是否需要更新。也就是说需要不断的双向同步

