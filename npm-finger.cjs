const NPM_FINGERPRINTS = {
    'shell-quote': {
        score: 0,
        strings: [
            "'",
            '"',
            'op', 'glob', 'pattern', // 来自 parse 函数
            'Bad substitution: ', // 来自 parse 函数
            /([A-Za-z]:)?([#!"$&'()*,:;<=>?@[\\\]^`{|}])/g, // 来自 quote 函数
        ],
        // 模块暴露的函数或变量名
        exports: ['quote', 'parse'],
    },
    '@sentry/utils': {
        score: 0,
        strings: [
            '[object Error]', '[object Exception]', '[object DOMException]',
            '__sentry_template_string__', '__sentry_override_normalization_depth__',
            '[VueViewModel]', '[SyntheticEvent]',
            'sentry-trace', 'sentry-baggage', // 也可能在 @sentry/core 中，但是个好信号
        ],
        exports: [
            'isDOMError', 'isDOMException', 'isString', 'isThenable', 'isPrimitive',
            'truncate', 'safeJoin', 'fill', 'getOriginalFunction', 'normalize',
            'severityFromString', 'timestampInSeconds'
        ],
    },
    '@sentry/core': {
        score: 0,
        strings: [
            'Sentry Logger', 'Invalid Sentry Dsn', 'sentry_key', 'sentry_version',
            '__SENTRY__', 'beforeSend',
        ],
        exports: [
            'addGlobalErrorInstrumentationHandler', 'addGlobalUnhandledRejectionInstrumentationHandler',
            'captureException', 'captureMessage', 'makeDsn', 'SentryError',
            'makeMain', 'getCurrentHub', 'Hub', 'Scope'
        ],
    },
    'commander': {
        score: 0,
        strings: [
            'program', 'createCommand', 'createArgument', 'createOption',
            'CommanderError', 'InvalidArgumentError', 'InvalidOptionArgumentError',
            'Command', 'Argument', 'Option', 'Help'
        ],
        exports: [
            'program', 'createCommand', 'createArgument', 'createOption',
            'CommanderError', 'InvalidArgumentError', 'InvalidOptionArgumentError',
            'Command', 'Argument', 'Option', 'Help'
        ],
    },
    'axios': {
        score: 0,
        strings: [
            'axios/', 'User-Agent', 'Content-Type', 'application/json',
            'XMLHttpRequest', 'fetch', 'FormData', 'AbortController',
            'interceptors', 'request', 'response', 'fulfilled', 'rejected'
        ],
        exports: [
            'get', 'post', 'put', 'delete', 'patch', 'head', 'options',
            'request', 'create', 'defaults', 'interceptors'
        ],
    },
    'chalk': {
        score: 0,
        strings: [
            'red', 'green', 'yellow', 'blue', 'magenta', 'cyan', 'white', 'gray',
            'bold', 'dim', 'italic', 'underline', 'strikethrough'
        ],
        exports: [
            'red', 'green', 'yellow', 'blue', 'magenta', 'cyan', 'white', 'gray',
            'bold', 'dim', 'italic', 'underline', 'strikethrough'
        ],
    },
    'mime-db': {
        score: 0,
        strings: [
            'application/', 'text/', 'image/', 'audio/', 'video/',
            'multipart/', 'message/', 'model/',
            'source: "iana"', 'compressible:', 'extensions:'
        ],
        exports: [],
    },
    'ws': {
        score: 0,
        strings: [
            'WebSocket', 'Sec-WebSocket-Key', 'Sec-WebSocket-Accept',
            'Sec-WebSocket-Protocol', 'Sec-WebSocket-Version',
            'websocket', 'ping', 'pong', 'close', 'open'
        ],
        exports: ['WebSocket', 'WebSocketServer', 'createWebSocketStream'],
    },
    'node-forge': {
        score: 0,
        strings: [
            'forge', 'pki', 'rsa', 'aes', 'sha1', 'sha256', 'md5',
            'asn1', 'der', 'pem', 'pkcs', 'x509', 'certificate'
        ],
        exports: [
            'pki', 'rsa', 'aes', 'sha1', 'sha256', 'md5', 'hmac',
            'asn1', 'der', 'pem', 'pkcs1', 'pkcs7', 'pkcs12'
        ],
    },
    'xmldom': {
        score: 0,
        strings: [
            'DOMParser', 'XMLSerializer', 'Document', 'Element', 'Node',
            'normalize', 'normalizeDocument', 'parseFromString'
        ],
        exports: ['DOMParser', 'XMLSerializer'],
    },
    'he': {
        score: 0,
        strings: [
            'encode', 'decode', 'escape', 'unescape',
            '&amp;', '&lt;', '&gt;', '&quot;', '&#x',
            'encodeNonAscii', 'useNamedReferences'
        ],
        exports: ['encode', 'decode', 'escape', 'unescape'],
    },
    'util': {
        score: 0,
        strings: [
            'isArray', 'isBoolean', 'isNull', 'isNullOrUndefined',
            'isNumber', 'isString', 'isSymbol', 'isUndefined',
            'isRegExp', 'isObject', 'isDate', 'isError', 'isFunction',
            'isPrimitive', 'isBuffer'
        ],
        exports: [
            'isArray', 'isBoolean', 'isNull', 'isNullOrUndefined',
            'isNumber', 'isString', 'isSymbol', 'isUndefined',
            'isRegExp', 'isObject', 'isDate', 'isError', 'isFunction',
            'isPrimitive', 'isBuffer'
        ],
    },
    'crypto': {
        score: 0,
        strings: [
            'randomBytes', 'randomFillSync', 'createHash', 'createHmac',
            'createCipher', 'createDecipher', 'pbkdf2', 'scrypt'
        ],
        exports: [
            'randomBytes', 'randomFillSync', 'createHash', 'createHmac',
            'createCipher', 'createDecipher', 'pbkdf2', 'scrypt'
        ],
    },
    'path': {
        score: 0,
        strings: [
            'join', 'resolve', 'normalize', 'dirname', 'basename', 'extname',
            'isAbsolute', 'relative', 'parse', 'format', 'sep', 'delimiter'
        ],
        exports: [
            'join', 'resolve', 'normalize', 'dirname', 'basename', 'extname',
            'isAbsolute', 'relative', 'parse', 'format', 'sep', 'delimiter'
        ],
    },
    'fs': {
        score: 0,
        strings: [
            'readFile', 'writeFile', 'readFileSync', 'writeFileSync',
            'existsSync', 'statSync', 'readdirSync', 'mkdirSync'
        ],
        exports: [
            'readFile', 'writeFile', 'readFileSync', 'writeFileSync',
            'existsSync', 'statSync', 'readdirSync', 'mkdirSync'
        ],
    },
    'os': {
        score: 0,
        strings: [
            'platform', 'arch', 'release', 'type', 'homedir', 'tmpdir',
            'hostname', 'userInfo', 'cpus', 'totalmem', 'freemem'
        ],
        exports: [
            'platform', 'arch', 'release', 'type', 'homedir', 'tmpdir',
            'hostname', 'userInfo', 'cpus', 'totalmem', 'freemem'
        ],
    },
    'url': {
        score: 0,
        strings: [
            'parse', 'format', 'resolve', 'URL', 'URLSearchParams',
            'protocol', 'hostname', 'port', 'pathname', 'search', 'hash'
        ],
        exports: [
            'parse', 'format', 'resolve', 'URL', 'URLSearchParams'
        ],
    },
    'querystring': {
        score: 0,
        strings: [
            'parse', 'stringify', 'escape', 'unescape'
        ],
        exports: ['parse', 'stringify', 'escape', 'unescape'],
    },
    'events': {
        score: 0,
        strings: [
            'EventEmitter', 'on', 'once', 'emit', 'removeListener',
            'removeAllListeners', 'listeners', 'listenerCount'
        ],
        exports: ['EventEmitter'],
    },
    'stream': {
        score: 0,
        strings: [
            'Readable', 'Writable', 'Duplex', 'Transform', 'PassThrough',
            'pipeline', 'finished'
        ],
        exports: [
            'Readable', 'Writable', 'Duplex', 'Transform', 'PassThrough',
            'pipeline', 'finished'
        ],
    },
    'buffer': {
        score: 0,
        strings: [
            'Buffer', 'from', 'alloc', 'allocUnsafe', 'concat', 'isBuffer',
            'byteLength', 'compare'
        ],
        exports: ['Buffer'],
    },
    'zlib': {
        score: 0,
        strings: [
            'gzip', 'gunzip', 'deflate', 'inflate', 'compress', 'uncompress',
            'createGzip', 'createGunzip', 'createDeflate', 'createInflate'
        ],
        exports: [
            'gzip', 'gunzip', 'deflate', 'inflate', 'compress', 'uncompress',
            'createGzip', 'createGunzip', 'createDeflate', 'createInflate'
        ],
    },
    'http': {
        score: 0,
        strings: [
            'createServer', 'request', 'get', 'Agent', 'IncomingMessage',
            'ServerResponse', 'STATUS_CODES'
        ],
        exports: [
            'createServer', 'request', 'get', 'Agent', 'IncomingMessage',
            'ServerResponse', 'STATUS_CODES'
        ],
    },
    'https': {
        score: 0,
        strings: [
            'createServer', 'request', 'get', 'Agent'
        ],
        exports: ['createServer', 'request', 'get', 'Agent'],
    },
    'net': {
        score: 0,
        strings: [
            'createServer', 'createConnection', 'connect', 'Socket', 'Server'
        ],
        exports: ['createServer', 'createConnection', 'connect', 'Socket', 'Server'],
    },
    'tls': {
        score: 0,
        strings: [
            'createServer', 'createSecureContext', 'connect', 'TLSSocket'
        ],
        exports: ['createServer', 'createSecureContext', 'connect', 'TLSSocket'],
    },
    'dns': {
        score: 0,
        strings: [
            'lookup', 'resolve', 'reverse', 'resolve4', 'resolve6', 'resolveMx'
        ],
        exports: ['lookup', 'resolve', 'reverse', 'resolve4', 'resolve6', 'resolveMx'],
    },
    'child_process': {
        score: 0,
        strings: [
            'spawn', 'exec', 'execFile', 'fork', 'execSync', 'spawnSync'
        ],
        exports: ['spawn', 'exec', 'execFile', 'fork', 'execSync', 'spawnSync'],
    },
    'cluster': {
        score: 0,
        strings: [
            'fork', 'isMaster', 'isWorker', 'worker', 'workers'
        ],
        exports: ['fork', 'isMaster', 'isWorker', 'worker', 'workers'],
    },
    'readline': {
        score: 0,
        strings: [
            'createInterface', 'question', 'close', 'pause', 'resume'
        ],
        exports: ['createInterface'],
    },
    'repl': {
        score: 0,
        strings: [
            'start', 'REPLServer'
        ],
        exports: ['start', 'REPLServer'],
    },
    'vm': {
        score: 0,
        strings: [
            'runInThisContext', 'runInNewContext', 'createContext', 'Script'
        ],
        exports: ['runInThisContext', 'runInNewContext', 'createContext', 'Script'],
    },
    'assert': {
        score: 0,
        strings: [
            'ok', 'equal', 'notEqual', 'deepEqual', 'notDeepEqual',
            'strictEqual', 'notStrictEqual', 'throws', 'doesNotThrow'
        ],
        exports: [
            'ok', 'equal', 'notEqual', 'deepEqual', 'notDeepEqual',
            'strictEqual', 'notStrictEqual', 'throws', 'doesNotThrow'
        ],
    },
    'timers': {
        score: 0,
        strings: [
            'setTimeout', 'clearTimeout', 'setInterval', 'clearInterval',
            'setImmediate', 'clearImmediate'
        ],
        exports: [
            'setTimeout', 'clearTimeout', 'setInterval', 'clearInterval',
            'setImmediate', 'clearImmediate'
        ],
    },
    'tty': {
        score: 0,
        strings: [
            'isatty', 'ReadStream', 'WriteStream'
        ],
        exports: ['isatty', 'ReadStream', 'WriteStream'],
    },
    'dgram': {
        score: 0,
        strings: [
            'createSocket', 'Socket'
        ],
        exports: ['createSocket', 'Socket'],
    },
    'punycode': {
        score: 0,
        strings: [
            'encode', 'decode', 'toUnicode', 'toASCII'
        ],
        exports: ['encode', 'decode', 'toUnicode', 'toASCII'],
    },
    'string_decoder': {
        score: 0,
        strings: [
            'StringDecoder', 'write', 'end'
        ],
        exports: ['StringDecoder'],
    },
    'v8': {
        score: 0,
        strings: [
            'getHeapStatistics', 'getHeapSpaceStatistics', 'serialize', 'deserialize'
        ],
        exports: ['getHeapStatistics', 'getHeapSpaceStatistics', 'serialize', 'deserialize'],
    },
    'worker_threads': {
        score: 0,
        strings: [
            'Worker', 'isMainThread', 'parentPort', 'workerData', 'threadId'
        ],
        exports: ['Worker', 'isMainThread', 'parentPort', 'workerData', 'threadId'],
    },
    'perf_hooks': {
        score: 0,
        strings: [
            'performance', 'PerformanceObserver', 'mark', 'measure'
        ],
        exports: ['performance', 'PerformanceObserver'],
    },
    'async_hooks': {
        score: 0,
        strings: [
            'createHook', 'executionAsyncId', 'triggerAsyncId', 'AsyncLocalStorage'
        ],
        exports: ['createHook', 'executionAsyncId', 'triggerAsyncId', 'AsyncLocalStorage'],
    },
    'lru-cache': {
        score: 0,
        strings: [
            'LRUCache', 'set', 'get', 'has', 'delete', 'clear', 'size',
            'max', 'ttl', 'allowStale', 'updateAgeOnGet', 'noDeleteOnFetchRejection'
        ],
        exports: ['LRUCache'],
    },
    'glob': {
        score: 0,
        strings: [
            'glob', 'sync', 'minimatch', 'Minimatch', 'match',
            'filter', 'makeRe', 'braceExpand'
        ],
        exports: ['glob', 'sync', 'minimatch', 'Minimatch'],
    },
    'minimatch': {
        score: 0,
        strings: [
            'minimatch', 'Minimatch', 'match', 'filter', 'makeRe',
            'braceExpand', 'parse', 'sep'
        ],
        exports: ['minimatch', 'Minimatch'],
    },
    'semver': {
        score: 0,
        strings: [
            'valid', 'clean', 'inc', 'diff', 'major', 'minor', 'patch',
            'prerelease', 'compare', 'rcompare', 'sort', 'rsort',
            'gt', 'gte', 'lt', 'lte', 'eq', 'neq', 'cmp', 'coerce',
            'satisfies', 'validRange', 'outside', 'gtr', 'ltr',
            'intersects', 'simplifyRange', 'subset'
        ],
        exports: [
            'valid', 'clean', 'inc', 'diff', 'major', 'minor', 'patch',
            'prerelease', 'compare', 'rcompare', 'sort', 'rsort',
            'gt', 'gte', 'lt', 'lte', 'eq', 'neq', 'cmp', 'coerce',
            'satisfies', 'validRange', 'outside', 'gtr', 'ltr'
        ],
    },
    'debug': {
        score: 0,
        strings: [
            'debug', 'DEBUG', 'enabled', 'namespace', 'log', 'extend'
        ],
        exports: ['debug'],
    },
    'lodash': {
        score: 0,
        strings: [
            'isArray', 'isObject', 'isString', 'isNumber', 'isBoolean',
            'isFunction', 'isUndefined', 'isNull', 'isEmpty', 'isEqual',
            'clone', 'cloneDeep', 'merge', 'assign', 'pick', 'omit',
            'map', 'filter', 'reduce', 'forEach', 'find', 'findIndex',
            'includes', 'some', 'every', 'sortBy', 'groupBy', 'uniq',
            'flatten', 'flattenDeep', 'compact', 'difference', 'intersection',
            'union', 'zip', 'unzip', 'chunk', 'take', 'drop', 'slice'
        ],
        exports: [
            'isArray', 'isObject', 'isString', 'isNumber', 'isBoolean',
            'isFunction', 'isUndefined', 'isNull', 'isEmpty', 'isEqual',
            'clone', 'cloneDeep', 'merge', 'assign', 'pick', 'omit',
            'map', 'filter', 'reduce', 'forEach', 'find', 'findIndex'
        ],
    },
    'moment': {
        score: 0,
        strings: [
            'moment', 'format', 'parse', 'isValid', 'isBefore', 'isAfter',
            'isSame', 'isBetween', 'add', 'subtract', 'startOf', 'endOf',
            'diff', 'duration', 'locale', 'utc', 'tz', 'timezone'
        ],
        exports: ['moment'],
    },
    'uuid': {
        score: 0,
        strings: [
            'v1', 'v3', 'v4', 'v5', 'uuid', 'validate', 'version', 'parse', 'stringify'
        ],
        exports: ['v1', 'v3', 'v4', 'v5', 'validate', 'version', 'parse', 'stringify'],
    },
    'express': {
        score: 0,
        strings: [
            'express', 'app', 'router', 'middleware', 'get', 'post', 'put',
            'delete', 'patch', 'use', 'listen', 'static', 'json', 'urlencoded'
        ],
        exports: ['express'],
    },
    'react': {
        score: 0,
        strings: [
            'React', 'Component', 'PureComponent', 'createElement', 'Fragment',
            'useState', 'useEffect', 'useContext', 'useReducer', 'useCallback',
            'useMemo', 'useRef', 'useImperativeHandle', 'useLayoutEffect',
            'useDebugValue', 'createContext', 'forwardRef', 'memo'
        ],
        exports: [
            'Component', 'PureComponent', 'createElement', 'Fragment',
            'useState', 'useEffect', 'useContext', 'useReducer', 'useCallback'
        ],
    },
    'statsig-node': {
        score: 0,
        strings: [
            'statsig', 'initialize', 'checkGate', 'getConfig', 'getExperiment',
            'logEvent', 'shutdown', 'getFeatureGate', 'getDynamicConfig',
            'getLayer', 'updateUser', 'overrideGate', 'overrideConfig'
        ],
        exports: [
            'initialize', 'checkGate', 'getConfig', 'getExperiment',
            'logEvent', 'shutdown', 'getFeatureGate', 'getDynamicConfig'
        ],
    },
    'ink': {
        score: 0,
        strings: [
            'render', 'Box', 'Text', 'Newline', 'Spacer', 'useInput',
            'useApp', 'useStdin', 'useStdout', 'useStderr', 'useFocus',
            'useFocusManager', 'measureElement', 'Static'
        ],
        exports: [
            'render', 'Box', 'Text', 'Newline', 'Spacer', 'useInput',
            'useApp', 'useStdin', 'useStdout', 'useStderr'
        ],
    },
    'wrap-ansi': {
        score: 0,
        strings: [
            'wrap', 'hard', 'wordWrap', 'trim', 'truncate', 'truncate-middle',
            'truncate-start', 'truncate-end'
        ],
        exports: ['wrap'],
    },
    'cli-truncate': {
        score: 0,
        strings: [
            'truncate', 'position', 'preferTruncationOnSpace'
        ],
        exports: ['truncate'],
    },
    'figures': {
        score: 0,
        strings: [
            'tick', 'cross', 'star', 'square', 'squareSmall', 'squareSmallFilled',
            'play', 'circle', 'circleFilled', 'circleDotted', 'circleDouble',
            'circleCircle', 'circleCross', 'circlePipe', 'radioOn', 'radioOff',
            'checkboxOn', 'checkboxOff', 'checkboxCircleOn', 'checkboxCircleOff',
            'questionMarkPrefix', 'oneHalf', 'oneThird', 'oneQuarter',
            'oneFifth', 'oneSixth', 'oneEighth', 'twoThirds', 'twoFifths',
            'threeQuarters', 'threeFifths', 'threeEighths', 'fourFifths',
            'fiveSixths', 'fiveEighths', 'sevenEighths'
        ],
        exports: [],
    },
    'form-data': {
        score: 0,
        strings: [
            'FormData', 'append', 'boundary', 'Content-Disposition',
            'form-data', 'filename', 'Content-Type', 'multipart/form-data'
        ],
        exports: ['FormData'],
    },
    'cookie': {
        score: 0,
        strings: [
            'parse', 'serialize', 'expires', 'maxAge', 'domain', 'path',
            'secure', 'httpOnly', 'sameSite'
        ],
        exports: ['parse', 'serialize'],
    },
    'jsonwebtoken': {
        score: 0,
        strings: [
            'sign', 'verify', 'decode', 'JsonWebTokenError', 'TokenExpiredError',
            'NotBeforeError', 'algorithm', 'expiresIn', 'notBefore', 'audience',
            'issuer', 'jwtid', 'subject', 'noTimestamp', 'header', 'keyid'
        ],
        exports: ['sign', 'verify', 'decode'],
    },
    'bcrypt': {
        score: 0,
        strings: [
            'hash', 'hashSync', 'compare', 'compareSync', 'genSalt', 'genSaltSync',
            'getRounds'
        ],
        exports: ['hash', 'hashSync', 'compare', 'compareSync', 'genSalt', 'genSaltSync'],
    },
    'multer': {
        score: 0,
        strings: [
            'multer', 'single', 'array', 'fields', 'none', 'any',
            'diskStorage', 'memoryStorage', 'destination', 'filename'
        ],
        exports: ['multer'],
    },
    'cors': {
        score: 0,
        strings: [
            'cors', 'origin', 'methods', 'allowedHeaders', 'exposedHeaders',
            'credentials', 'maxAge', 'preflightContinue', 'optionsSuccessStatus'
        ],
        exports: ['cors'],
    },
    'helmet': {
        score: 0,
        strings: [
            'helmet', 'contentSecurityPolicy', 'crossOriginEmbedderPolicy',
            'crossOriginOpenerPolicy', 'crossOriginResourcePolicy', 'dnsPrefetchControl',
            'expectCt', 'frameguard', 'hidePoweredBy', 'hsts', 'ieNoOpen',
            'noSniff', 'originAgentCluster', 'permittedCrossDomainPolicies',
            'referrerPolicy', 'xssFilter'
        ],
        exports: ['helmet'],
    },
    'compression': {
        score: 0,
        strings: [
            'compression', 'filter', 'level', 'threshold', 'windowBits',
            'chunkSize', 'memLevel', 'strategy'
        ],
        exports: ['compression'],
    },
    'morgan': {
        score: 0,
        strings: [
            'morgan', 'combined', 'common', 'dev', 'short', 'tiny',
            'compile', 'format', 'token'
        ],
        exports: ['morgan'],
    },
    'body-parser': {
        score: 0,
        strings: [
            'json', 'raw', 'text', 'urlencoded', 'inflate', 'limit',
            'parameterLimit', 'type', 'verify'
        ],
        exports: ['json', 'raw', 'text', 'urlencoded'],
    },
    'express-rate-limit': {
        score: 0,
        strings: [
            'rateLimit', 'windowMs', 'max', 'message', 'statusCode',
            'headers', 'draft_polli_ratelimit_headers', 'standardHeaders',
            'legacyHeaders', 'store', 'keyGenerator', 'skip', 'onLimitReached'
        ],
        exports: ['rateLimit'],
    },
    'express-validator': {
        score: 0,
        strings: [
            'body', 'check', 'cookie', 'header', 'param', 'query',
            'validationResult', 'matchedData', 'sanitize', 'escape',
            'unescape', 'trim', 'ltrim', 'rtrim', 'normalizeEmail'
        ],
        exports: ['body', 'check', 'validationResult', 'matchedData'],
    },
    'passport': {
        score: 0,
        strings: [
            'passport', 'initialize', 'session', 'authenticate', 'authorize',
            'serializeUser', 'deserializeUser', 'use', 'Strategy'
        ],
        exports: ['passport'],
    },
    'passport-local': {
        score: 0,
        strings: [
            'LocalStrategy', 'usernameField', 'passwordField', 'passReqToCallback'
        ],
        exports: ['Strategy'],
    },
    'passport-jwt': {
        score: 0,
        strings: [
            'JwtStrategy', 'ExtractJwt', 'fromAuthHeaderAsBearerToken',
            'fromAuthHeaderWithScheme', 'fromBodyField', 'fromUrlQueryParameter',
            'fromExtractors', 'jwtFromRequest', 'secretOrKey', 'issuer', 'audience'
        ],
        exports: ['Strategy', 'ExtractJwt'],
    },
    'mongoose': {
        score: 0,
        strings: [
            'mongoose', 'Schema', 'model', 'connect', 'connection', 'disconnect',
            'ObjectId', 'Mixed', 'populate', 'aggregate', 'find', 'findOne',
            'findById', 'findOneAndUpdate', 'findByIdAndUpdate', 'save', 'remove',
            'deleteOne', 'deleteMany', 'updateOne', 'updateMany', 'countDocuments'
        ],
        exports: ['mongoose'],
    },
    'sequelize': {
        score: 0,
        strings: [
            'Sequelize', 'DataTypes', 'Model', 'Op', 'Transaction', 'QueryTypes',
            'ValidationError', 'ConnectionError', 'TimeoutError', 'UniqueConstraintError',
            'ForeignKeyConstraintError', 'ExclusionConstraintError', 'ValidationErrorItem'
        ],
        exports: ['Sequelize'],
    },
    'mysql2': {
        score: 0,
        strings: [
            'createConnection', 'createPool', 'createPoolCluster', 'escape',
            'escapeId', 'format', 'raw', 'Connection', 'Pool', 'PoolCluster'
        ],
        exports: ['createConnection', 'createPool', 'createPoolCluster'],
    },
    'pg': {
        score: 0,
        strings: [
            'Client', 'Pool', 'connect', 'query', 'end', 'release',
            'ConnectionParameters', 'defaults', 'types'
        ],
        exports: ['Client', 'Pool'],
    },
    'redis': {
        score: 0,
        strings: [
            'createClient', 'RedisClient', 'get', 'set', 'del', 'exists',
            'expire', 'ttl', 'incr', 'decr', 'lpush', 'rpush', 'lpop', 'rpop',
            'sadd', 'srem', 'smembers', 'hget', 'hset', 'hdel', 'hgetall'
        ],
        exports: ['createClient'],
    },
    'jest': {
        score: 0,
        strings: [
            'describe', 'it', 'test', 'expect', 'beforeEach', 'afterEach',
            'beforeAll', 'afterAll', 'jest', 'mock', 'spyOn', 'mockImplementation',
            'mockReturnValue', 'mockResolvedValue', 'mockRejectedValue', 'clearAllMocks',
            'resetAllMocks', 'restoreAllMocks', 'toBe', 'toEqual', 'toMatch',
            'toContain', 'toHaveLength', 'toThrow', 'toHaveBeenCalled'
        ],
        exports: ['describe', 'it', 'test', 'expect', 'jest'],
    },
    'mocha': {
        score: 0,
        strings: [
            'describe', 'it', 'before', 'after', 'beforeEach', 'afterEach',
            'suite', 'test', 'setup', 'teardown', 'suiteSetup', 'suiteTeardown'
        ],
        exports: ['describe', 'it', 'before', 'after', 'beforeEach', 'afterEach'],
    },
    'chai': {
        score: 0,
        strings: [
            'expect', 'should', 'assert', 'to', 'be', 'been', 'is', 'that',
            'which', 'and', 'has', 'have', 'with', 'at', 'of', 'same',
            'equal', 'deep', 'nested', 'own', 'ordered', 'any', 'all'
        ],
        exports: ['expect', 'should', 'assert'],
    },
    'sinon': {
        score: 0,
        strings: [
            'spy', 'stub', 'mock', 'fake', 'sandbox', 'createSandbox',
            'restore', 'reset', 'resetHistory', 'resetBehavior', 'calledWith',
            'calledOnce', 'calledTwice', 'calledThrice', 'notCalled', 'returned'
        ],
        exports: ['spy', 'stub', 'mock', 'fake', 'sandbox', 'createSandbox'],
    },
    'supertest': {
        score: 0,
        strings: [
            'request', 'agent', 'get', 'post', 'put', 'delete', 'patch',
            'head', 'options', 'send', 'query', 'field', 'attach', 'set',
            'expect', 'end', 'timeout', 'redirects', 'type', 'accept'
        ],
        exports: ['request', 'agent'],
    },
    'nyc': {
        score: 0,
        strings: [
            'nyc', 'coverage', 'instrument', 'report', 'check-coverage',
            'lines', 'functions', 'branches', 'statements'
        ],
        exports: [],
    },
    'eslint': {
        score: 0,
        strings: [
            'ESLint', 'CLIEngine', 'Linter', 'RuleTester', 'SourceCode',
            'lintText', 'lintFiles', 'executeOnText', 'executeOnFiles',
            'getConfigForFile', 'isPathIgnored', 'loadFormatter'
        ],
        exports: ['ESLint', 'CLIEngine', 'Linter'],
    },
    'prettier': {
        score: 0,
        strings: [
            'format', 'check', 'formatWithCursor', 'getFileInfo', 'getSupportInfo',
            'resolveConfig', 'clearConfigCache', 'resolveConfigFile'
        ],
        exports: ['format', 'check', 'formatWithCursor'],
    },
    'webpack': {
        score: 0,
        strings: [
            'webpack', 'Compiler', 'Compilation', 'DefinePlugin', 'HotModuleReplacementPlugin',
            'ProgressPlugin', 'ProvidePlugin', 'IgnorePlugin', 'BannerPlugin',
            'optimize', 'entry', 'output', 'module', 'resolve', 'plugins',
            'devtool', 'target', 'externals', 'performance', 'node', 'stats'
        ],
        exports: ['webpack'],
    },
    'babel-core': {
        score: 0,
        strings: [
            'transform', 'transformFile', 'transformFileSync', 'transformFromAst',
            'parse', 'generate', 'traverse', 'types', 'template'
        ],
        exports: ['transform', 'transformFile', 'parse'],
    },
    'rollup': {
        score: 0,
        strings: [
            'rollup', 'bundle', 'generate', 'write', 'watch', 'Plugin',
            'InputOptions', 'OutputOptions', 'RollupBuild', 'RollupOptions'
        ],
        exports: ['rollup'],
    },
    'vite': {
        score: 0,
        strings: [
            'createServer', 'build', 'preview', 'defineConfig', 'loadEnv',
            'normalizePath', 'mergeConfig', 'searchForWorkspaceRoot'
        ],
        exports: ['createServer', 'build', 'preview', 'defineConfig'],
    },
    'esbuild': {
        score: 0,
        strings: [
            'build', 'buildSync', 'transform', 'transformSync', 'serve',
            'context', 'analyzeMetafile', 'formatMessages', 'initialize'
        ],
        exports: ['build', 'buildSync', 'transform', 'transformSync'],
    },
    'parcel': {
        score: 0,
        strings: [
            'Bundler', 'bundle', 'watch', 'serve', 'getPort', 'Asset',
            'Transformer', 'Packager', 'Resolver'
        ],
        exports: ['Bundler'],
    },
    'gulp': {
        score: 0,
        strings: [
            'src', 'dest', 'watch', 'series', 'parallel', 'task', 'lastRun',
            'tree', 'registry'
        ],
        exports: ['src', 'dest', 'watch', 'series', 'parallel', 'task'],
    },
    'grunt': {
        score: 0,
        strings: [
            'registerTask', 'registerMultiTask', 'initConfig', 'loadNpmTasks',
            'loadTasks', 'renameTask', 'option', 'config', 'template', 'file',
            'log', 'verbose', 'warn', 'fatal', 'fail'
        ],
        exports: [],
    },
    'nodemon': {
        score: 0,
        strings: [
            'nodemon', 'watch', 'ignore', 'ext', 'exec', 'delay', 'env',
            'nodeArgs', 'script', 'verbose', 'quiet', 'exitcrash'
        ],
        exports: [],
    },
    'pm2': {
        score: 0,
        strings: [
            'start', 'stop', 'restart', 'reload', 'delete', 'list', 'describe',
            'monit', 'logs', 'flush', 'reloadLogs', 'sendSignal', 'ping',
            'sendDataToProcessId', 'startLogging', 'stopLogging'
        ],
        exports: [],
    },
    'forever': {
        score: 0,
        strings: [
            'start', 'stop', 'restart', 'list', 'logs', 'cleanlogs',
            'columns', 'load', 'save', 'stopall', 'restartall'
        ],
        exports: [],
    },
    'graphql': {
        score: 0,
        strings: [
            'GraphQL', 'buildSchema', 'execute', 'parse', 'validate',
            'graphql', 'GraphQLSchema', 'GraphQLObjectType', 'GraphQLString',
            'GraphQLInt', 'GraphQLFloat', 'GraphQLBoolean', 'GraphQLID',
            'GraphQLList', 'GraphQLNonNull', 'GraphQLEnumType', 'GraphQLInputObjectType'
        ],
        exports: ['graphql', 'buildSchema', 'execute', 'parse', 'validate'],
    },
    'apollo-server': {
        score: 0,
        strings: [
            'ApolloServer', 'gql', 'makeExecutableSchema', 'addMockFunctionsToSchema',
            'mockServer', 'GraphQLUpload', 'AuthenticationError', 'ForbiddenError',
            'UserInputError', 'ApolloError', 'withFilter', 'PubSub'
        ],
        exports: ['ApolloServer', 'gql'],
    },
    'socket.io': {
        score: 0,
        strings: [
            'Server', 'Socket', 'emit', 'on', 'once', 'off', 'join', 'leave',
            'to', 'in', 'broadcast', 'volatile', 'compress', 'binary',
            'namespace', 'adapter', 'engine', 'handshake', 'rooms'
        ],
        exports: ['Server'],
    },
    'socket.io-client': {
        score: 0,
        strings: [
            'io', 'connect', 'Manager', 'Socket', 'emit', 'on', 'once', 'off',
            'disconnect', 'reconnect', 'forceNew', 'multiplex', 'timeout'
        ],
        exports: ['io', 'connect', 'Manager'],
    },
    'jsonschema': {
        score: 0,
        strings: [
            'validate', 'Validator', 'Schema', 'ValidationError', 'SchemaError',
            'addSchema', 'setSchemas', 'getSchema', 'unresolvedRefs'
        ],
        exports: ['validate', 'Validator'],
    },
    'joi': {
        score: 0,
        strings: [
            'string', 'number', 'boolean', 'date', 'array', 'object', 'binary',
            'alternatives', 'any', 'valid', 'invalid', 'required', 'optional',
            'forbidden', 'strip', 'default', 'description', 'notes', 'tags',
            'example', 'unit', 'min', 'max', 'length', 'regex', 'alphanum'
        ],
        exports: ['string', 'number', 'boolean', 'date', 'array', 'object'],
    },
    'ajv': {
        score: 0,
        strings: [
            'Ajv', 'compile', 'validate', 'addSchema', 'addMetaSchema',
            'validateSchema', 'getSchema', 'removeSchema', 'addFormat',
            'addKeyword', 'getKeyword', 'removeKeyword', 'errorsText'
        ],
        exports: ['Ajv'],
    },
    'zod': {
        score: 0,
        strings: [
            'z', 'string', 'number', 'boolean', 'date', 'array', 'object',
            'union', 'intersection', 'tuple', 'record', 'map', 'set',
            'function', 'lazy', 'literal', 'enum', 'nativeEnum', 'nullable',
            'optional', 'parse', 'safeParse', 'parseAsync', 'safeParseAsync'
        ],
        exports: ['z'],
    },
    'class-validator': {
        score: 0,
        strings: [
            'validate', 'validateSync', 'validateOrReject', 'IsString', 'IsNumber',
            'IsBoolean', 'IsDate', 'IsArray', 'IsObject', 'IsEmail', 'IsUrl',
            'IsUUID', 'IsOptional', 'IsNotEmpty', 'Length', 'Min', 'Max',
            'MinLength', 'MaxLength', 'Matches', 'Contains', 'IsIn', 'IsNotIn'
        ],
        exports: ['validate', 'validateSync', 'validateOrReject'],
    },
    'class-transformer': {
        score: 0,
        strings: [
            'plainToClass', 'classToPlain', 'plainToClassFromExist', 'classToClassFromExist',
            'serialize', 'deserialize', 'Transform', 'Type', 'Exclude', 'Expose',
            'TransformPlainToClass', 'TransformClassToPlain', 'TransformClassToClass'
        ],
        exports: ['plainToClass', 'classToPlain', 'serialize', 'deserialize'],
    },
    'typeorm': {
        score: 0,
        strings: [
            'createConnection', 'getConnection', 'getConnectionManager', 'getRepository',
            'getTreeRepository', 'getMongoRepository', 'getCustomRepository',
            'Entity', 'Column', 'PrimaryGeneratedColumn', 'PrimaryColumn',
            'CreateDateColumn', 'UpdateDateColumn', 'VersionColumn', 'Generated',
            'Index', 'Unique', 'Check', 'Exclusion', 'ManyToOne', 'OneToMany',
            'OneToOne', 'ManyToMany', 'JoinColumn', 'JoinTable', 'RelationId'
        ],
        exports: ['createConnection', 'getConnection', 'getRepository'],
    },
    'prisma': {
        score: 0,
        strings: [
            'PrismaClient', 'prisma', 'findMany', 'findUnique', 'findFirst',
            'create', 'update', 'upsert', 'delete', 'deleteMany', 'updateMany',
            'count', 'aggregate', 'groupBy', 'connect', 'disconnect',
            'transaction', 'executeRaw', 'queryRaw'
        ],
        exports: ['PrismaClient'],
    },
    'knex': {
        score: 0,
        strings: [
            'knex', 'select', 'from', 'where', 'join', 'leftJoin', 'rightJoin',
            'innerJoin', 'outerJoin', 'groupBy', 'orderBy', 'having', 'limit',
            'offset', 'insert', 'update', 'del', 'truncate', 'schema', 'raw'
        ],
        exports: ['knex'],
    },
    'bookshelf': {
        score: 0,
        strings: [
            'Model', 'Collection', 'forge', 'fetchAll', 'fetch', 'save',
            'destroy', 'where', 'query', 'orderBy', 'belongsTo', 'hasOne',
            'hasMany', 'belongsToMany', 'morphOne', 'morphMany', 'morphTo'
        ],
        exports: ['Model', 'Collection'],
    },
    'objection': {
        score: 0,
        strings: [
            'Model', 'QueryBuilder', 'transaction', 'raw', 'ref', 'lit',
            'relationMappings', 'jsonSchema', 'tableName', 'idColumn',
            'query', 'findById', 'insert', 'update', 'patch', 'delete',
            'relate', 'unrelate', 'eager', 'joinEager', 'withGraphFetched'
        ],
        exports: ['Model', 'QueryBuilder', 'transaction'],
    },
    'waterline': {
        score: 0,
        strings: [
            'Waterline', 'Collection', 'Model', 'find', 'findOne', 'create',
            'update', 'destroy', 'count', 'sum', 'average', 'populate',
            'attributes', 'associations', 'validations', 'callbacks'
        ],
        exports: ['Waterline'],
    },
    'massive': {
        score: 0,
        strings: [
            'massive', 'connect', 'query', 'find', 'findOne', 'insert',
            'update', 'destroy', 'save', 'reload', 'listTables', 'listViews',
            'listFunctions', 'listSchemas'
        ],
        exports: ['massive'],
    },
    'node-postgres': {
        score: 0,
        strings: [
            'Client', 'Pool', 'Query', 'Result', 'Connection', 'types',
            'defaults', 'native'
        ],
        exports: ['Client', 'Pool'],
    },
    'mysql': {
        score: 0,
        strings: [
            'createConnection', 'createPool', 'createPoolCluster', 'escape',
            'escapeId', 'format', 'Connection', 'Pool', 'PoolCluster'
        ],
        exports: ['createConnection', 'createPool', 'createPoolCluster'],
    },
    'sqlite3': {
        score: 0,
        strings: [
            'Database', 'Statement', 'verbose', 'cached', 'OPEN_READONLY',
            'OPEN_READWRITE', 'OPEN_CREATE', 'OPEN_FULLMUTEX', 'OPEN_SHAREDCACHE',
            'OPEN_PRIVATECACHE', 'OPEN_URI', 'OPEN_MEMORY'
        ],
        exports: ['Database', 'Statement', 'verbose', 'cached'],
    },
    'better-sqlite3': {
        score: 0,
        strings: [
            'Database', 'Statement', 'prepare', 'exec', 'close', 'pragma',
            'checkpoint', 'backup', 'serialize', 'function', 'aggregate',
            'table', 'loadExtension'
        ],
        exports: ['Database'],
    },
    'ioredis': {
        score: 0,
        strings: [
            'Redis', 'Cluster', 'Command', 'Pipeline', 'get', 'set', 'del',
            'exists', 'expire', 'ttl', 'incr', 'decr', 'lpush', 'rpush',
            'lpop', 'rpop', 'sadd', 'srem', 'smembers', 'hget', 'hset',
            'hdel', 'hgetall', 'zadd', 'zrem', 'zrange', 'zrevrange'
        ],
        exports: ['Redis', 'Cluster'],
    },
    'node_redis': {
        score: 0,
        strings: [
            'createClient', 'RedisClient', 'Multi', 'get', 'set', 'del',
            'exists', 'expire', 'ttl', 'incr', 'decr', 'lpush', 'rpush',
            'lpop', 'rpop', 'sadd', 'srem', 'smembers', 'hget', 'hset'
        ],
        exports: ['createClient'],
    }
};

module.exports = NPM_FINGERPRINTS;
