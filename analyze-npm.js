#!/usr/bin/env node

const fs = require('fs');

// 简化的npm包指纹
const NPM_FINGERPRINTS = {
    'sentry': {
        strings: ['@sentry/', 'Sentry', 'captureException', 'captureMessage', 'configureScope'],
        score: 0
    },
    'react': {
        strings: ['React', 'createElement', 'useState', 'useEffect', 'Component'],
        score: 0
    },
    'commander': {
        strings: ['program', 'createCommand', 'Command', 'option', 'action'],
        score: 0
    },
    'chalk': {
        strings: ['chalk', 'red', 'green', 'blue', 'yellow', 'bold'],
        score: 0
    },
    'lodash': {
        strings: ['lodash', 'isArray', 'isObject', 'forEach', 'map', 'filter'],
        score: 0
    },
    'uuid': {
        strings: ['uuid', 'randomUUID', 'v4', 'v1', 'validate'],
        score: 0
    },
    'axios': {
        strings: ['axios', 'Axios', 'isAxiosError', 'get', 'post'],
        score: 0
    },
    'express': {
        strings: ['Express', 'app.use', 'app.get', 'req.params', 'res.send'],
        score: 0
    },
    'typescript': {
        strings: ['TypeScript', 'typescript', 'tsc', 'tsconfig'],
        score: 0
    },
    'webpack': {
        strings: ['webpack', '__webpack_require__', 'webpack.config'],
        score: 0
    },
    'babel': {
        strings: ['@babel/', 'babel', 'Babel', 'transform'],
        score: 0
    },
    'eslint': {
        strings: ['eslint', 'ESLint', '@typescript-eslint'],
        score: 0
    },
    'prettier': {
        strings: ['prettier', 'Prettier', 'format'],
        score: 0
    },
    'jest': {
        strings: ['jest', 'Jest', 'describe', 'it', 'expect'],
        score: 0
    },
    'mocha': {
        strings: ['mocha', 'Mocha', 'describe', 'it'],
        score: 0
    },
    'chai': {
        strings: ['chai', 'Chai', 'expect', 'should'],
        score: 0
    }
};

function analyzeFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        const results = {};
        
        // 分析每个npm包
        for (const [packageName, fingerprint] of Object.entries(NPM_FINGERPRINTS)) {
            let score = 0;
            const foundStrings = [];
            
            for (const str of fingerprint.strings) {
                const regex = new RegExp(str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
                const matches = content.match(regex);
                if (matches) {
                    score += matches.length;
                    foundStrings.push(`${str} (${matches.length})`);
                }
            }
            
            if (score > 0) {
                results[packageName] = {
                    score,
                    foundStrings: foundStrings.slice(0, 10) // 限制显示数量
                };
            }
        }
        
        return results;
    } catch (error) {
        console.error('Error reading file:', error.message);
        return {};
    }
}

// 主程序
if (require.main === module) {
    const filePath = process.argv[2];
    if (!filePath) {
        console.error('Usage: node analyze-npm.js <file-path>');
        process.exit(1);
    }
    
    console.log(`Analyzing file: ${filePath}`);
    console.log('='.repeat(50));
    
    const results = analyzeFile(filePath);
    
    if (Object.keys(results).length === 0) {
        console.log('No npm packages detected.');
    } else {
        // 按分数排序
        const sorted = Object.entries(results)
            .sort(([,a], [,b]) => b.score - a.score);
        
        console.log('Detected npm packages (sorted by confidence):');
        console.log('-'.repeat(50));
        
        for (const [packageName, data] of sorted) {
            console.log(`\n${packageName}: ${data.score} matches`);
            console.log(`  Found: ${data.foundStrings.join(', ')}`);
        }
        
        console.log('\n' + '='.repeat(50));
        console.log(`Total packages detected: ${Object.keys(results).length}`);
    }
}

module.exports = { analyzeFile, NPM_FINGERPRINTS };
