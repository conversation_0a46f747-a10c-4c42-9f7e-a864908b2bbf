const NPM_FINGERPRINTS = {
    '@sentry/auto-instrumentation': {
        score: 0,
        strings: [
            'shouldDisableAutoInstrumentation',
            'instrumenter',
            'getClient',
            'getOptions',
            '_optionalChain'
        ],
        exports: [ 'shouldDisableAutoInstrumentation' ]
    },
    '@sentry/checkin': {
        score: 0,
        strings: [
            'createCheckInEnvelope',
            'check_in_id',
            'monitor_slug',
            'status',
            'duration',
            'monitor_config',
            'schedule',
            'checkin_margin',
            'max_runtime'
        ],
        exports: [ 'createCheckInEnvelope' ]
    },
    '@sentry/client': {
        score: 0,
        strings: [
            'ServerRuntimeClient',
            'BaseClient',
            'initAndBind',
            'setCurrentClient',
            'eventFromException',
            'eventFromMessage',
            'captureCheckIn',
            'initSessionFlusher',
            '_captureRequestSession'
        ],
        exports: [
            'ServerRuntimeClient',
            'BaseClient',
            'initAndBind',
            'setCurrentClient'
        ]
    },
    '@sentry/core': {
        score: 0,
        strings: [
            'Sentry Logger',
            'Invalid Sentry Dsn',
            'sentry_key',
            'sentry_version',
            '__SENTRY__',
            'beforeSend',
            '@sentry/core',
            'SentryError',
            'captureException',
            'captureMessage',
            'Hub',
            'Scope',
            'getCurrentHub',
            'getIsolationScope',
            'SDK_VERSION',
            '7.120.3',
            'API_VERSION'
        ],
        exports: [
            'addGlobalErrorInstrumentationHandler',
            'addGlobalUnhandledRejectionInstrumentationHandler',
            'captureException',
            'captureMessage',
            'makeDsn',
            'SentryError',
            'makeMain',
            'getCurrentHub',
            'Hub',
            'Scope',
            'getIsolationScope',
            'captureEvent',
            'withScope'
        ]
    },
    '@sentry/debug': {
        score: 0,
        strings: [ 'DEBUG_BUILD', '__SENTRY_DEBUG__', 'typeof __SENTRY_DEBUG__' ],
        exports: [ 'DEBUG_BUILD' ]
    },
    '@sentry/envelope': {
        score: 0,
        strings: [
            'createEventEnvelope',
            'createSessionEnvelope',
            'getEnvelopeEndpointWithUrlEncodedAuth',
            'getReportDialogEndpoint',
            'sent_at',
            'sdk',
            'dsn'
        ],
        exports: [
            'createEventEnvelope',
            'createSessionEnvelope',
            'getEnvelopeEndpointWithUrlEncodedAuth',
            'getReportDialogEndpoint'
        ]
    },
    '@sentry/express': {
        score: 0,
        strings: [
            'ExpressIntegration',
            'shouldDisableAutoInstrumentation',
            'middleware.express',
            'auto.middleware.express',
            'SEMANTIC_ATTRIBUTE_SENTRY_SOURCE',
            'extractPathForTransaction'
        ],
        exports: [ 'ExpressIntegration' ]
    },
    '@sentry/integrations': {
        score: 0,
        strings: [
            'setupIntegrations',
            'addIntegration',
            'installedIntegrations',
            'afterSetupIntegrations',
            'defineIntegration',
            'convertIntegrationFnToClass'
        ],
        exports: [
            'setupIntegrations',
            'addIntegration',
            'defineIntegration',
            'convertIntegrationFnToClass'
        ]
    },
    '@sentry/metrics': {
        score: 0,
        strings: [
            'createMetricEnvelope',
            'serializeMetricBuckets',
            'getBucketKey',
            'sanitizeMetricKey',
            'sanitizeTags',
            'sanitizeUnit',
            'simpleHash'
        ],
        exports: [
            'createMetricEnvelope',
            'serializeMetricBuckets',
            'getBucketKey',
            'sanitizeMetricKey',
            'sanitizeTags'
        ]
    },
    '@sentry/metrics-aggregator': {
        score: 0,
        strings: [
            'MetricsAggregator',
            'CounterMetric',
            'GaugeMetric',
            'DistributionMetric',
            'SetMetric',
            'METRIC_MAP',
            'COUNTER_METRIC_TYPE',
            'GAUGE_METRIC_TYPE',
            'DISTRIBUTION_METRIC_TYPE',
            'SET_METRIC_TYPE',
            'DEFAULT_FLUSH_INTERVAL'
        ],
        exports: [
            'MetricsAggregator',
            'CounterMetric',
            'GaugeMetric',
            'DistributionMetric',
            'SetMetric',
            'METRIC_MAP'
        ]
    },
    '@sentry/module-metadata': {
        score: 0,
        strings: [
            'ModuleMetadata',
            'moduleMetadataIntegration',
            '_sentryModuleMetadata',
            'addMetadataToStackFrames',
            'stripMetadataFromStackFrames',
            'getMetadataForUrl',
            'module_metadata'
        ],
        exports: [
            'ModuleMetadata',
            'moduleMetadataIntegration',
            'addMetadataToStackFrames',
            'stripMetadataFromStackFrames'
        ]
    },
    '@sentry/node': {
        score: 0,
        strings: [
            '__SENTRY__',
            '__sentry_original__',
            '__sentry_captured__',
            '__sentry_instrumentation_handlers__',
            '_sentryId',
            '_sentryCaptured',
            'SentryError',
            'Sentry Logger',
            'Invalid Sentry Dsn',
            'sentryWrapped',
            'SENTRY_XHR_DATA_KEY',
            'SEMANTIC_ATTRIBUTE_SENTRY_OP',
            'SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN',
            'SEMANTIC_ATTRIBUTE_SENTRY_SOURCE',
            'addFetchInstrumentationHandler',
            'addXhrInstrumentationHandler',
            'instrumentFetchRequest',
            'getSentryRelease',
            '@sentry/node',
            'BaseClient',
            'captureException',
            'captureMessage',
            'captureEvent',
            'captureSession',
            'getDsn',
            'getOptions',
            'recordDroppedEvent',
            'sendSession',
            'eventFromException'
        ],
        exports: [
            'init',
            'captureException',
            'captureMessage',
            'addBreadcrumb',
            'configureScope',
            'withScope',
            'Hub',
            'Scope',
            'getCurrentHub',
            'getClient',
            'startTransaction',
            'getCurrentScope',
            'BaseClient',
            'captureEvent',
            'captureSession',
            'getDsn',
            'getOptions'
        ]
    },
    '@sentry/sdk-metadata': {
        score: 0,
        strings: [
            'applySdkMetadata',
            'SDK_VERSION',
            'sentry.javascript',
            '_metadata',
            'packages',
            'npm:@sentry'
        ],
        exports: [ 'applySdkMetadata' ]
    },
    '@sentry/session': {
        score: 0,
        strings: [
            'SessionFlusher',
            'getSessionAggregates',
            'incrementSessionStatusCount',
            '_incrementSessionStatusCount',
            'flushTimeout',
            '_pendingAggregates'
        ],
        exports: [ 'SessionFlusher' ]
    },
    '@sentry/tracing': {
        score: 0,
        strings: [
            '@sentry/tracing',
            'startSpan',
            'startActiveSpan',
            'getActiveSpan',
            'continueTrace',
            'trace',
            'SpanRecorder',
            'TRACE_FLAG_SAMPLED',
            'SEMANTIC_ATTRIBUTE_SENTRY_OP',
            'SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN'
        ],
        exports: [
            'startSpan',
            'startActiveSpan',
            'getActiveSpan',
            'continueTrace',
            'trace',
            'SpanRecorder',
            'Span'
        ]
    },
    '@sentry/transport': {
        score: 0,
        strings: [
            'createTransport',
            'makeOfflineTransport',
            'makeMultiplexedTransport',
            'DEFAULT_TRANSPORT_BUFFER_SIZE',
            'eventFromEnvelope',
            'createSpanEnvelope',
            'send',
            'flush',
            'bufferSize',
            'recordDroppedEvent'
        ],
        exports: [
            'createTransport',
            'makeOfflineTransport',
            'makeMultiplexedTransport',
            'eventFromEnvelope',
            'createSpanEnvelope'
        ]
    },
    '@sentry/types': {
        score: 0,
        strings: [
            '@sentry/types',
            'SpanStatus',
            'Span',
            'Transaction',
            'Breadcrumb',
            'User',
            'Context',
            'Event'
        ],
        exports: [ 'SpanStatus', 'Span', 'Transaction', 'Breadcrumb' ]
    },
    '@sentry/utils': {
        score: 0,
        strings: [
            '[object Error]',
            '[object Exception]',
            '[object DOMException]',
            '__sentry_template_string__',
            '__sentry_override_normalization_depth__',
            '[VueViewModel]',
            '[SyntheticEvent]',
            'sentry-trace',
            'sentry-baggage',
            '@sentry/utils',
            'GLOBAL_OBJ',
            'getGlobalSingleton',
            'uuid4',
            'timestampInSeconds',
            'normalize',
            'dropUndefinedKeys',
            'isError',
            'isEvent',
            'isInstanceOf',
            'logger',
            'consoleSandbox'
        ],
        exports: [
            'isDOMError',
            'isDOMException',
            'isString',
            'isThenable',
            'isPrimitive',
            'truncate',
            'safeJoin',
            'fill',
            'getOriginalFunction',
            'normalize',
            'severityFromString',
            'timestampInSeconds',
            'uuid4',
            'dropUndefinedKeys',
            'isError',
            'isEvent',
            'logger',
            'GLOBAL_OBJ'
        ]
    },
    'abort-controller': {
        score: 0,
        strings: [
            'AbortController',
            'AbortSignal',
            'abort',
            'signal',
            'timeout',
            'clearTimeout',
            'setTimeout'
        ],
        exports: [ 'AbortController', 'AbortSignal' ]
    },
    'ajv': {
        score: 0,
        strings: [
            'Ajv',
            'compile',
            'validate',
            'addSchema',
            'addMetaSchema',
            'validateSchema',
            'getSchema',
            'removeSchema',
            'addFormat',
            'addKeyword',
            'getKeyword',
            'removeKeyword',
            'errorsText'
        ],
        exports: [ 'Ajv' ]
    },
    'ajv-json-schema': {
        score: 0,
        strings: [
            'ajv',
            'validate',
            'compile',
            'addSchema',
            'addMetaSchema',
            'validateSchema',
            'getSchema',
            'removeSchema',
            'addFormat',
            'errorsText',
            'addKeyword',
            'getKeyword',
            'removeKeyword',
            'validateKeyword',
            'ValidationError',
            'MissingRefError',
            '$dataMetaSchema',
            'allOf',
            'anyOf',
            'oneOf',
            'not',
            'if',
            'then',
            'else',
            'const',
            'enum',
            'type',
            'format',
            'pattern',
            'minimum',
            'maximum',
            'exclusiveMinimum',
            'exclusiveMaximum',
            'multipleOf',
            'minLength',
            'maxLength',
            'minItems',
            'maxItems',
            'uniqueItems',
            'minProperties',
            'maxProperties',
            'required',
            'additionalProperties',
            'patternProperties',
            'dependencies'
        ],
        exports: [
            'validate',
            'compile',
            'addSchema',
            'addMetaSchema',
            'validateSchema',
            'getSchema',
            'removeSchema',
            'addFormat',
            'errorsText',
            'addKeyword',
            'ValidationError'
        ]
    },
    'ansi-escapes': {
        score: 0,
        strings: [
            'scrollUp',
            'scrollDown',
            'eraseUp',
            'eraseScreen',
            'clearScreen',
            'cursorTo',
            'cursorMove',
            'cursorUp',
            'cursorDown',
            'cursorLeft',
            'cursorRight'
        ],
        exports: [
            'scrollUp',
            'scrollDown',
            'eraseUp',
            'eraseScreen',
            'clearScreen',
            'cursorTo',
            'cursorMove',
            'cursorUp',
            'cursorDown'
        ]
    },
    'apollo-server': {
        score: 0,
        strings: [
            'ApolloServer',
            'gql',
            'makeExecutableSchema',
            'addMockFunctionsToSchema',
            'mockServer',
            'GraphQLUpload',
            'AuthenticationError',
            'ForbiddenError',
            'UserInputError',
            'ApolloError',
            'withFilter',
            'PubSub'
        ],
        exports: [ 'ApolloServer', 'gql' ]
    },
    'apollo-server-core': {
        score: 0,
        strings: [
            'apollo-server-core',
            '@nestjs/graphql',
            'Apollo',
            'ApolloIntegration',
            'ApolloServerBase',
            'constructSchema',
            'graphql.resolve',
            'auto.graphql.apollo',
            'resolvers',
            'GraphQLFactory'
        ],
        exports: [ 'ApolloServer', 'ApolloServerBase', 'GraphQLFactory' ]
    },
    'asciidoc': {
        score: 0,
        strings: [
            'AsciiDoc',
            'asciidoc',
            'adoc',
            'className: "title"',
            'className: "section"',
            'className: "meta"',
            'className: "quote"'
        ],
        exports: []
    },
    'assert': {
        score: 0,
        strings: [
            'ok',
            'equal',
            'notEqual',
            'deepEqual',
            'notDeepEqual',
            'strictEqual',
            'notStrictEqual',
            'throws',
            'doesNotThrow'
        ],
        exports: [
            'ok',
            'equal',
            'notEqual',
            'deepEqual',
            'notDeepEqual',
            'strictEqual',
            'notStrictEqual',
            'throws',
            'doesNotThrow'
        ]
    },
    'async_hooks': {
        score: 0,
        strings: [
            'createHook',
            'executionAsyncId',
            'triggerAsyncId',
            'AsyncLocalStorage',
            'async_hooks',
            'setHooksAsyncContextStrategy',
            'getCurrentHub',
            'runWithAsyncContext',
            'getStore'
        ],
        exports: [ 'createHook', 'executionAsyncId', 'triggerAsyncId', 'AsyncLocalStorage' ]
    },
    'audio-formats': {
        score: 0,
        strings: [
            'audio/mp3',
            'audio/mp4',
            'audio/mpeg',
            'audio/ogg',
            'audio/wav',
            'audio/webm',
            'audio/x-aac',
            'audio/x-flac',
            'audio/x-m4a',
            'audio/midi',
            'audio/vorbis',
            'audio/opus'
        ],
        exports: [ 'parseAudio', 'getAudioFormat', 'convertAudio' ]
    },
    'aws-bedrock': {
        score: 0,
        strings: [
            'bedrock',
            'BedrockClient',
            'CreateEvaluationJob',
            'CreateCustomModel',
            'CreateGuardrail',
            'CreateInferenceProfile',
            'CreateMarketplaceModelEndpoint',
            'CreateModelCopyJob',
            'CreateModelCustomizationJob',
            'CreateModelImportJob',
            'CreateModelInvocationJob',
            'CreatePromptRouter',
            'CreateProvisionedModelThroughput',
            'GetCustomModel',
            'GetEvaluationJob',
            'GetFoundationModel',
            'GetGuardrail',
            'GetImportedModel',
            'GetInferenceProfile',
            'GetMarketplaceModelEndpoint',
            'ListCustomModels',
            'ListEvaluationJobs',
            'ListFoundationModels',
            'ListGuardrails',
            'ListImportedModels',
            'modelArn',
            'jobArn',
            'guardrailArn',
            'inferenceProfileArn',
            'provisionedModelArn'
        ],
        exports: [
            'BedrockClient',
            'CreateEvaluationJobCommand',
            'CreateCustomModelCommand',
            'CreateGuardrailCommand',
            'GetCustomModelCommand',
            'GetEvaluationJobCommand'
        ]
    },
    'aws-cognito-identity': {
        score: 0,
        strings: [
            'cognito-identity',
            'CognitoIdentity',
            'GetCredentialsForIdentity',
            'GetId',
            'GetOpenIdToken',
            'UnlinkIdentity',
            'defaultCognitoIdentityHttpAuthSchemeProvider',
            'defaultCognitoIdentityHttpAuthSchemeParametersProvider',
            'resolveHttpAuthSchemeConfig',
            'client-cognito-identity',
            '@aws-sdk/client-cognito-identity'
        ],
        exports: [
            'CognitoIdentity',
            'GetCredentialsForIdentity',
            'GetId',
            'GetOpenIdToken'
        ]
    },
    'aws-credential-providers': {
        score: 0,
        strings: [
            'credential-provider',
            'fromEnv',
            'fromSSO',
            'fromStatic',
            'fromHttp',
            'nodeProvider',
            'CredentialsProviderError',
            'TokenProviderError',
            'AWS_ACCESS_KEY_ID',
            'AWS_SECRET_ACCESS_KEY',
            'AWS_SESSION_TOKEN',
            'AWS_CREDENTIAL_EXPIRATION',
            'AWS_CREDENTIAL_SCOPE',
            'AWS_ACCOUNT_ID',
            'sso_start_url',
            'sso_account_id',
            'sso_region',
            'sso_role_name',
            'sso_session',
            'accessKeyId',
            'secretAccessKey',
            'sessionToken',
            'fromIni',
            'fromProcess',
            'fromTokenFile',
            'fromContainerMetadata',
            'fromInstanceMetadata',
            'defaultProvider',
            'credentialsTreatedAsExpired',
            'credentialsWillNeedRefresh',
            'AWS_PROFILE',
            'AWS_EC2_METADATA_DISABLED',
            'web_identity_token_file',
            'role_arn'
        ],
        exports: [
            'fromEnv',
            'fromSSO',
            'fromStatic',
            'fromHttp',
            'nodeProvider',
            'fromIni',
            'fromProcess',
            'fromTokenFile',
            'defaultProvider'
        ]
    },
    'aws-protocols': {
        score: 0,
        strings: [
            'AwsEc2QueryProtocol',
            'AwsRestXmlProtocol',
            'XmlShapeSerializer',
            'XmlCodec',
            'parseXmlBody',
            'parseXmlErrorBody',
            'loadRestXmlErrorCode',
            'HttpBindingProtocol',
            'HttpInterceptingShapeSerializer',
            'HttpInterceptingShapeDeserializer',
            'serializeRequest',
            'deserializeResponse',
            'handleError',
            'xmlNamespace',
            'timestampFormat',
            'httpBindings',
            'AwsJson1_0Protocol',
            'AwsJson1_1Protocol',
            'AwsJsonRpcProtocol',
            'AwsQueryProtocol',
            'AwsRestJsonProtocol',
            'JsonCodec',
            'JsonShapeDeserializer',
            'JsonShapeSerializer',
            'XmlShapeDeserializer',
            '_toBool',
            '_toNum',
            '_toStr',
            'awsExpectUnion',
            'loadRestJsonErrorCode',
            'parseJsonBody',
            'parseJsonErrorBody'
        ],
        exports: [
            'AwsEc2QueryProtocol',
            'AwsRestXmlProtocol',
            'XmlShapeSerializer',
            'XmlCodec',
            'AwsJson1_0Protocol',
            'AwsJson1_1Protocol',
            'AwsJsonRpcProtocol',
            'AwsQueryProtocol',
            'AwsRestJsonProtocol',
            'JsonCodec'
        ]
    },
    'aws-sdk': {
        score: 0,
        strings: [
            '@aws-sdk/',
            'aws-sdk',
            'AWS.',
            'client-sts',
            'client-sso',
            'client-cognito-identity',
            'client-bedrock',
            'credential-provider-web-identity',
            'PolicyArns',
            'RoleArn',
            'WebIdentityToken',
            'AssumeRoleWithWebIdentity',
            'GetCallerIdentity',
            'AWS',
            'smithy',
            'AlgorithmId',
            'EndpointURLScheme',
            'FieldPosition',
            'HttpApiKeyAuthLocation',
            'HttpAuthLocation',
            'IniSectionType',
            'RequestHandlerProtocol',
            'SMITHY_CONTEXT_KEY',
            'getDefaultClientConfiguration',
            'resolveDefaultRuntimeConfig',
            'HttpRequest',
            'HttpResponse',
            'Field',
            'Fields',
            'isValidHostname',
            'getHttpHandlerExtensionConfiguration',
            'resolveHttpHandlerRuntimeConfig'
        ],
        exports: [
            'STSClient',
            'SSOClient',
            'CognitoIdentityClient',
            'BedrockClient',
            'AssumeRoleWithWebIdentityCommand',
            'GetCallerIdentityCommand',
            'getDefaultClientConfiguration',
            'resolveDefaultRuntimeConfig',
            'HttpRequest',
            'HttpResponse',
            'Field',
            'Fields'
        ]
    },
    'aws-sdk-core': {
        score: 0,
        strings: [
            'aws-sdk',
            'core',
            'Client',
            'Command',
            'ServiceException',
            'NoOpLogger',
            'SENSITIVE_STRING',
            'collectBody',
            'convertMap',
            'createAggregatedClient',
            'decorateServiceException',
            'emitWarningIfUnsupportedVersion',
            'extendedEncodeURIComponent',
            'getArrayIfSingleItem',
            'getDefaultClientConfiguration',
            'getDefaultExtensionConfiguration',
            'getValueFromTextNode',
            'isSerializableHeaderValue',
            'loadConfigsForDefaultMode',
            'resolveDefaultRuntimeConfig',
            'resolvedPath',
            'serializeDateTime',
            'serializeFloat',
            'take',
            'throwDefaultError',
            'withBaseException'
        ],
        exports: [
            'Client',
            'Command',
            'ServiceException',
            'NoOpLogger',
            'SENSITIVE_STRING',
            'collectBody',
            'convertMap',
            'createAggregatedClient',
            'decorateServiceException'
        ]
    },
    'aws-sdk-sso-oidc': {
        score: 0,
        strings: [
            'aws-sdk',
            'sso-oidc',
            'SSOOIDC',
            'CreateToken',
            'AccessDeniedException',
            'AuthorizationPendingException',
            'ExpiredTokenException',
            'InternalServerException',
            'InvalidClientException',
            'InvalidGrantException',
            'InvalidRequestException',
            'InvalidScopeException',
            'SlowDownException',
            'UnauthorizedClientException',
            'UnsupportedGrantTypeException',
            'clientId',
            'clientSecret',
            'refreshToken',
            'accessToken',
            'idToken',
            'expiresIn',
            'tokenType'
        ],
        exports: [ 'CreateTokenCommand', 'SSOOIDC', 'SSOOIDCClient' ]
    },
    'aws-sso-client': {
        score: 0,
        strings: [
            'sso',
            'SSOClient',
            'GetRoleCredentials',
            'ListAccountRoles',
            'ListAccounts',
            'Logout',
            'defaultSSOHttpAuthSchemeProvider',
            'defaultSSOHttpAuthSchemeParametersProvider',
            'awsssoportal',
            'client-sso',
            '@aws-sdk/client-sso'
        ],
        exports: [
            'SSOClient',
            'GetRoleCredentials',
            'ListAccountRoles',
            'ListAccounts',
            'Logout'
        ]
    },
    'aws-sts-client': {
        score: 0,
        strings: [
            'sts',
            'STSClient',
            'AssumeRoleWithWebIdentity',
            'GetRoleCredentials',
            'resolveHttpAuthSchemeConfig',
            'defaultSTSHttpAuthSchemeProvider',
            'defaultSTSHttpAuthSchemeParametersProvider',
            'resolveStsAuthConfig',
            'resolveClientEndpointParameters',
            'commonParams',
            'ruleSet',
            'defaultEndpointResolver',
            'getRuntimeConfig',
            'sigv4',
            'noAuth'
        ],
        exports: [ 'STSClient', 'AssumeRoleWithWebIdentity', 'GetRoleCredentials' ]
    },
    'aws-user-agent': {
        score: 0,
        strings: [
            'user-agent',
            'getUserAgentMiddleware',
            'getUserAgentPlugin',
            'resolveUserAgentConfig',
            'userAgentMiddleware',
            'DEFAULT_UA_APP_ID',
            'createDefaultUserAgentProvider',
            'defaultUserAgent',
            'crtAvailability',
            'AWS_SDK_UA_APP_ID',
            'sdk_ua_app_id',
            'aws-sdk-js',
            'nodejs',
            'exec-env',
            'userAgentAppId'
        ],
        exports: [
            'getUserAgentMiddleware',
            'getUserAgentPlugin',
            'resolveUserAgentConfig',
            'createDefaultUserAgentProvider',
            'defaultUserAgent'
        ]
    },
    'axios': {
        score: 0,
        strings: [
            'axios/',
            'User-Agent',
            'Content-Type',
            'application/json',
            'XMLHttpRequest',
            'fetch',
            'FormData',
            'AbortController',
            'interceptors',
            'request',
            'response',
            'fulfilled',
            'rejected',
            'isAxiosError',
            'axios.get',
            'axios.post',
            'axios.put',
            'axios.delete',
            'axios.request',
            'axios.create',
            'axios.defaults',
            'axios.interceptors',
            'response.status',
            'response.data',
            'response.headers',
            'request.timeout'
        ],
        exports: [
            'get',
            'post',
            'put',
            'delete',
            'patch',
            'head',
            'options',
            'request',
            'create',
            'defaults',
            'interceptors',
            'isAxiosError',
            'Cancel',
            'CancelToken'
        ]
    },
    'babel': {
        score: 0,
        strings: [ '@babel/plugin-proposal-private-methods', 'babel', 'Babel' ],
        exports: [
            'transform',
            'transformSync',
            'transformAsync',
            'parse',
            'traverse',
            'generate',
            'template'
        ]
    },
    'babel-core': {
        score: 0,
        strings: [
            'transform',
            'transformFile',
            'transformFileSync',
            'transformFromAst',
            'parse',
            'generate',
            'traverse',
            'types',
            'template'
        ],
        exports: [ 'transform', 'transformFile', 'parse' ]
    },
    'balanced-match': {
        score: 0,
        strings: [
            'balanced-match',
            'range',
            'indexOf',
            'slice',
            'start',
            'end',
            'pre',
            'body',
            'post',
            'match'
        ],
        exports: [ 'balanced', 'range' ]
    },
    'bcrypt': {
        score: 0,
        strings: [
            'hash',
            'hashSync',
            'compare',
            'compareSync',
            'genSalt',
            'genSaltSync',
            'getRounds'
        ],
        exports: [ 'hash', 'hashSync', 'compare', 'compareSync', 'genSalt', 'genSaltSync' ]
    },
    'better-sqlite3': {
        score: 0,
        strings: [
            'Database',
            'Statement',
            'prepare',
            'exec',
            'close',
            'pragma',
            'checkpoint',
            'backup',
            'serialize',
            'function',
            'aggregate',
            'table',
            'loadExtension'
        ],
        exports: [ 'Database' ]
    },
    'body-parser': {
        score: 0,
        strings: [
            'json',
            'raw',
            'text',
            'urlencoded',
            'inflate',
            'limit',
            'parameterLimit',
            'type',
            'verify'
        ],
        exports: [ 'json', 'raw', 'text', 'urlencoded' ]
    },
    'bookshelf': {
        score: 0,
        strings: [
            'Model',
            'Collection',
            'forge',
            'fetchAll',
            'fetch',
            'save',
            'destroy',
            'where',
            'query',
            'orderBy',
            'belongsTo',
            'hasOne',
            'hasMany',
            'belongsToMany',
            'morphOne',
            'morphMany',
            'morphTo'
        ],
        exports: [ 'Model', 'Collection' ]
    },
    'brace-expansion': {
        score: 0,
        strings: [
            'brace-expansion',
            'SLASH',
            'OPEN',
            'CLOSE',
            'COMMA',
            'PERIOD',
            'parseInt',
            'charCodeAt',
            'split',
            'join',
            'substr'
        ],
        exports: [ 'expand' ]
    },
    'buffer': {
        score: 0,
        strings: [
            'Buffer',
            'from',
            'alloc',
            'allocUnsafe',
            'concat',
            'isBuffer',
            'byteLength',
            'compare'
        ],
        exports: [ 'Buffer' ]
    },
    'chai': {
        score: 0,
        strings: [
            'expect',
            'should',
            'assert',
            'to',
            'be',
            'been',
            'is',
            'that',
            'which',
            'and',
            'has',
            'have',
            'with',
            'at',
            'of',
            'same',
            'equal',
            'deep',
            'nested',
            'own',
            'ordered',
            'any',
            'all'
        ],
        exports: [ 'expect', 'should', 'assert' ]
    },
    'chalk': {
        score: 0,
        strings: [
            'red',
            'green',
            'yellow',
            'blue',
            'magenta',
            'cyan',
            'white',
            'gray',
            'bold',
            'dim',
            'italic',
            'underline',
            'strikethrough',
            'F0.bold',
            'F0.red',
            'F0.yellow',
            'F0.green'
        ],
        exports: [
            'red',
            'green',
            'yellow',
            'blue',
            'magenta',
            'cyan',
            'white',
            'gray',
            'bold',
            'dim',
            'italic',
            'underline',
            'strikethrough'
        ]
    },
    'chemical-formats': {
        score: 0,
        strings: [
            'chemical/x-cdx',
            'chemical/x-cif',
            'chemical/x-cmdf',
            'chemical/x-cml',
            'chemical/x-csml',
            'chemical/x-pdb',
            'chemical/x-xyz'
        ],
        exports: [ 'parseChemical', 'getChemicalFormat' ]
    },
    'child_process': {
        score: 0,
        strings: [
            'spawn',
            'exec',
            'execFile',
            'fork',
            'execSync',
            'spawnSync',
            'child_process',
            '/usr/bin/sw_vers',
            'ProductName',
            'ProductVersion',
            'BuildVersion'
        ],
        exports: [ 'spawn', 'exec', 'execFile', 'fork', 'execSync', 'spawnSync' ]
    },
    'class-transformer': {
        score: 0,
        strings: [
            'plainToClass',
            'classToPlain',
            'plainToClassFromExist',
            'classToClassFromExist',
            'serialize',
            'deserialize',
            'Transform',
            'Type',
            'Exclude',
            'Expose',
            'TransformPlainToClass',
            'TransformClassToPlain',
            'TransformClassToClass'
        ],
        exports: [ 'plainToClass', 'classToPlain', 'serialize', 'deserialize' ]
    },
    'class-validator': {
        score: 0,
        strings: [
            'validate',
            'validateSync',
            'validateOrReject',
            'IsString',
            'IsNumber',
            'IsBoolean',
            'IsDate',
            'IsArray',
            'IsObject',
            'IsEmail',
            'IsUrl',
            'IsUUID',
            'IsOptional',
            'IsNotEmpty',
            'Length',
            'Min',
            'Max',
            'MinLength',
            'MaxLength',
            'Matches',
            'Contains',
            'IsIn',
            'IsNotIn'
        ],
        exports: [ 'validate', 'validateSync', 'validateOrReject' ]
    },
    'clean-stack': {
        score: 0,
        strings: [
            'clean-stack',
            'nodeInternals',
            'ignoredPackages',
            'internals',
            'cwd',
            'wrapCallSite',
            'builtinModules',
            'bootstrap_node',
            'node',
            'internal',
            'node-spawn-wrap',
            'captureStackTrace',
            'prepareStackTrace'
        ],
        exports: [
            'CleanStack',
            'nodeInternals',
            'clean',
            'captureString',
            'capture',
            'at',
            'parseLine'
        ]
    },
    'cli-boxes': {
        score: 0,
        strings: [
            'cli-boxes',
            'single',
            'double',
            'round',
            'bold',
            'singleDouble',
            'doubleSingle',
            'classic',
            'arrow',
            'topLeft',
            'top',
            'topRight',
            'right',
            'bottomRight',
            'bottom',
            'bottomLeft',
            'left'
        ],
        exports: [
            'single',
            'double',
            'round',
            'bold',
            'singleDouble',
            'doubleSingle',
            'classic',
            'arrow'
        ]
    },
    'cli-truncate': {
        score: 0,
        strings: [
            'truncate',
            'position',
            'preferTruncationOnSpace',
            'truncatedContent',
            'truncatedText'
        ],
        exports: [ 'truncate' ]
    },
    'cluster': {
        score: 0,
        strings: [ 'fork', 'isMaster', 'isWorker', 'worker', 'workers' ],
        exports: [ 'fork', 'isMaster', 'isWorker', 'worker', 'workers' ]
    },
    'code-analysis': {
        score: 0,
        strings: [
            'analysis',
            'ast',
            'abstract syntax tree',
            'semantic',
            'lexical',
            'syntactic',
            'scope',
            'binding',
            'resolution',
            'type checking',
            'inference',
            'validation',
            'linting'
        ],
        exports: [ 'analyze', 'parse', 'validate', 'lint', 'check' ]
    },
    'combined-stream': {
        score: 0,
        strings: [
            '_getNext',
            '_realGetNext',
            '_pipeNext',
            '_pendingNext',
            '_currentStream',
            '_insideLoop',
            'combined-stream',
            'lD',
            'CombinedStream',
            'append',
            'isStreamLike',
            'pauseStreams',
            '_streams',
            'maxDataSize'
        ],
        exports: [ 'create', 'isStreamLike', 'CombinedStream' ]
    },
    'commander': {
        score: 0,
        strings: [
            'program',
            'createCommand',
            'createArgument',
            'createOption',
            'CommanderError',
            'InvalidArgumentError',
            'InvalidOptionArgumentError',
            'Command',
            'Argument',
            'Option',
            'Help',
            'helpOption',
            'action',
            'description',
            'version',
            'parseAsync'
        ],
        exports: [
            'program',
            'createCommand',
            'createArgument',
            'createOption',
            'CommanderError',
            'InvalidArgumentError',
            'InvalidOptionArgumentError',
            'Command',
            'Argument',
            'Option',
            'Help'
        ]
    },
    'compression': {
        score: 0,
        strings: [
            'compression',
            'filter',
            'level',
            'threshold',
            'windowBits',
            'chunkSize',
            'memLevel',
            'strategy'
        ],
        exports: [ 'compression' ]
    },
    'compression-stream': {
        score: 0,
        strings: [
            'CompressionStream',
            'gzip',
            'TextEncoder',
            'getWriter',
            'getReader',
            'Uint8Array',
            'writable',
            'readable'
        ],
        exports: [ 'CompressionStream', 'DecompressionStream' ]
    },
    'config': {
        score: 0,
        strings: [
            'config',
            'configuration',
            'configurable',
            'configure',
            'configureScope',
            'networkConfig',
            'mcpConfig',
            'dynamicMcpConfig',
            'strictMcpConfig',
            'claude config',
            'config set',
            'config get',
            'config list',
            'config add',
            'config remove',
            'configVersion',
            'configObject',
            'configInstallMethod'
        ],
        exports: [ 'config', 'get', 'set', 'has', 'util' ]
    },
    'cookie': {
        score: 0,
        strings: [
            'parse',
            'serialize',
            'expires',
            'maxAge',
            'domain',
            'path',
            'secure',
            'httpOnly',
            'sameSite'
        ],
        exports: [ 'parse', 'serialize' ]
    },
    'cors': {
        score: 0,
        strings: [
            'cors',
            'origin',
            'methods',
            'allowedHeaders',
            'exposedHeaders',
            'credentials',
            'maxAge',
            'preflightContinue',
            'optionsSuccessStatus',
            'Access-Control-Allow-Origin',
            'Access-Control-Allow-Methods',
            'Access-Control-Allow-Headers',
            'preflight'
        ],
        exports: [ 'cors' ]
    },
    'cross-spawn': {
        score: 0,
        strings: [
            'spawn',
            'spawnSync',
            'cross-spawn',
            'shebang',
            'command',
            'argument',
            'win32',
            'cygwin',
            'msys',
            'PATHEXT',
            'PATH',
            'shell',
            'windowsVerbatimArguments',
            'stdio',
            'env',
            'cwd',
            'uid',
            'gid',
            'detached',
            'windowsHide'
        ],
        exports: [ 'spawn', 'spawnSync', 'shebang', 'command', 'argument', 'shell', 'stdio', 'env' ]
    },
    'crypto': {
        score: 0,
        strings: [
            'randomBytes',
            'randomFillSync',
            'createHash',
            'createHmac',
            'createCipher',
            'createDecipher',
            'pbkdf2',
            'scrypt',
            'randomUUID'
        ],
        exports: [
            'randomBytes',
            'randomFillSync',
            'createHash',
            'createHmac',
            'createCipher',
            'createDecipher',
            'pbkdf2',
            'scrypt',
            'randomUUID'
        ]
    },
    'debug': {
        score: 0,
        strings: [
            'debug',
            'DEBUG',
            'enabled',
            'namespace',
            'log',
            'extend',
            'Instance method `debug.destroy()` is deprecated',
            'next major version of `debug`'
        ],
        exports: [ 'debug' ]
    },
    'delayed-stream': {
        score: 0,
        strings: [
            'delayed-stream',
            'DelayedStream',
            'TU',
            'Stream',
            'util',
            'maxDataSize',
            'pauseStream',
            '_maxDataSizeExceeded',
            '_released',
            '_bufferedEvents',
            'dataSize'
        ],
        exports: [ 'DelayedStream' ]
    },
    'dgram': {
        score: 0,
        strings: [ 'createSocket', 'Socket' ],
        exports: [ 'createSocket', 'Socket' ]
    },
    'dns': {
        score: 0,
        strings: [ 'lookup', 'resolve', 'reverse', 'resolve4', 'resolve6', 'resolveMx' ],
        exports: [ 'lookup', 'resolve', 'reverse', 'resolve4', 'resolve6', 'resolveMx' ]
    },
    'dns-over-https': {
        score: 0,
        strings: [
            'cloudflare-dns.com/dns-query',
            'application/dns-message',
            'DnsTxtFetchError',
            'DnsTxtParseError',
            '_fetchTxtRecords',
            'featureassets.org'
        ],
        exports: [ 'fetchTxtRecords', 'parseDnsResponse' ]
    },
    'domain': {
        score: 0,
        strings: [
            'domain',
            'setDomainAsyncContextStrategy',
            'active',
            'create',
            'bind',
            'ensureHubOnCarrier',
            'getHubFromCarrier'
        ],
        exports: [ 'create', 'active' ]
    },
    'dotenv': {
        score: 0,
        strings: [
            'dotenv',
            '.env',
            'process.env',
            'environment',
            'env',
            'NODE_ENV',
            'ANTHROPIC_',
            'CLAUDE_'
        ],
        exports: [ 'config', 'parse', 'load' ]
    },
    'entities': {
        score: 0,
        strings: [
            'OpenCurlyDoubleQuote',
            'CloseCurlyDoubleQuote',
            'OpenCurlyQuote',
            'CloseCurlyQuote',
            'encode',
            'decode',
            'encodeXML',
            'decodeXML',
            'encodeHTML',
            'decodeHTML'
        ],
        exports: [
            'encode',
            'decode',
            'encodeXML',
            'decodeXML',
            'encodeHTML',
            'decodeHTML'
        ]
    },
    'esbuild': {
        score: 0,
        strings: [
            'build',
            'buildSync',
            'transform',
            'transformSync',
            'serve',
            'context',
            'analyzeMetafile',
            'formatMessages',
            'initialize'
        ],
        exports: [ 'build', 'buildSync', 'transform', 'transformSync' ]
    },
    'escape-string-regexp': {
        score: 0,
        strings: [
            'escape-string-regexp',
            '[|\\\\{}()[\\]^$+*?.-]',
            'replace',
            '\\\\$&',
            'Expected a string'
        ],
        exports: [ 'escapeStringRegexp' ]
    },
    'eslint': {
        score: 0,
        strings: [
            'ESLint',
            'CLIEngine',
            'Linter',
            'RuleTester',
            'SourceCode',
            'lintText',
            'lintFiles',
            'executeOnText',
            'executeOnFiles',
            'getConfigForFile',
            'isPathIgnored',
            'loadFormatter',
            'eslint',
            '@typescript-eslint/eslint-plugin',
            '@typescript-eslint/parser',
            'eslint-config-prettier',
            'eslint-plugin-node',
            'eslint-plugin-prettier',
            '.eslintcache'
        ],
        exports: [ 'ESLint', 'CLIEngine', 'Linter', 'RuleTester' ]
    },
    'event-target-shim': {
        score: 0,
        strings: [
            'EventTarget',
            'addEventListener',
            'removeEventListener',
            'dispatchEvent',
            'Event',
            'CloseEvent',
            'ErrorEvent',
            'MessageEvent',
            'handleEvent'
        ],
        exports: [ 'EventTarget', 'Event', 'CloseEvent', 'ErrorEvent', 'MessageEvent' ]
    },
    'events': {
        score: 0,
        strings: [
            'EventEmitter',
            'on',
            'once',
            'emit',
            'removeListener',
            'removeAllListeners',
            'listeners',
            'listenerCount'
        ],
        exports: [ 'EventEmitter' ]
    },
    'express': {
        score: 0,
        strings: [
            'express',
            'app',
            'router',
            'middleware',
            'get',
            'post',
            'put',
            'delete',
            'patch',
            'use',
            'listen',
            'static',
            'json',
            'urlencoded',
            'Express',
            'ExpressIntegration',
            'middleware.express',
            'Express middleware takes',
            'Express 4',
            'requestDataOptionsFromExpressHandler',
            'app.use',
            'app.get',
            'app.post',
            'req.params',
            'res.send',
            'res.json',
            'process_params',
            'lazyrouter',
            '_router',
            'originalUrl',
            '_reconstructedRoute',
            '_hasParameters',
            'layerRoutePath'
        ],
        exports: [
            'express',
            'Router',
            'static',
            'json',
            'urlencoded',
            'Request',
            'Response',
            'Application',
            'application',
            'request',
            'response'
        ]
    },
    'express-rate-limit': {
        score: 0,
        strings: [
            'rateLimit',
            'windowMs',
            'max',
            'message',
            'statusCode',
            'headers',
            'draft_polli_ratelimit_headers',
            'standardHeaders',
            'legacyHeaders',
            'store',
            'keyGenerator',
            'skip',
            'onLimitReached'
        ],
        exports: [ 'rateLimit' ]
    },
    'express-validator': {
        score: 0,
        strings: [
            'body',
            'check',
            'cookie',
            'header',
            'param',
            'query',
            'validationResult',
            'matchedData',
            'sanitize',
            'escape',
            'unescape',
            'trim',
            'ltrim',
            'rtrim',
            'normalizeEmail'
        ],
        exports: [ 'body', 'check', 'validationResult', 'matchedData' ]
    },
    'fast-xml-parser': {
        score: 0,
        strings: [
            'fast-xml-parser',
            'XMLParser',
            'XMLBuilder',
            'XMLValidator',
            'parseXml',
            'buildXml',
            'validate',
            'preserveOrder',
            'attributeNamePrefix',
            'attributesGroupName',
            'textNodeName',
            'ignoreAttributes',
            'removeNSPrefix',
            'allowBooleanAttributes',
            'parseTagValue',
            'parseAttributeValue',
            'trimValues',
            'cdataPropName',
            'numberParseOptions',
            'tagValueProcessor',
            'attributeValueProcessor',
            'stopNodes',
            'alwaysCreateTextNode',
            'commentPropName',
            'unpairedTags',
            'processEntities',
            'htmlEntities',
            'ignoreDeclaration',
            'ignorePiTags',
            'transformTagName',
            'transformAttributeName'
        ],
        exports: [
            'XMLParser',
            'XMLBuilder',
            'XMLValidator',
            'parseXml',
            'buildXml',
            'validate'
        ]
    },
    'fetch': {
        score: 0,
        strings: [
            'fetch',
            'Request',
            'Response',
            'Headers',
            'AbortController',
            'function fetch',
            'new Request',
            'fetchData',
            'addFetchInstrumentationHandler',
            'supportsFetch',
            'supportsNativeFetch',
            'isNativeFetch'
        ],
        exports: []
    },
    'figures': {
        score: 0,
        strings: [
            'tick',
            'cross',
            'star',
            'square',
            'squareSmall',
            'squareSmallFilled',
            'play',
            'circle',
            'circleFilled',
            'circleDotted',
            'circleDouble',
            'circleCircle',
            'circleCross',
            'circlePipe',
            'radioOn',
            'radioOff',
            'checkboxOn',
            'checkboxOff',
            'checkboxCircleOn',
            'checkboxCircleOff',
            'questionMarkPrefix',
            'oneHalf',
            'oneThird',
            'oneQuarter',
            'oneFifth',
            'oneSixth',
            'oneEighth',
            'twoThirds',
            'twoFifths',
            'threeQuarters',
            'threeFifths',
            'threeEighths',
            'fourFifths',
            'fiveSixths',
            'fiveEighths',
            'sevenEighths',
            'bullet',
            'line',
            'pointer',
            'info',
            'warning',
            'hamburger',
            'smiley',
            'mustache',
            'heart',
            'nodejs',
            'arrowUp',
            'arrowDown',
            'arrowLeft',
            'arrowRight'
        ],
        exports: [
            'tick',
            'cross',
            'star',
            'square',
            'play',
            'circle',
            'bullet',
            'line',
            'pointer',
            'info',
            'warning',
            'arrowUp',
            'arrowDown',
            'arrowLeft',
            'arrowRight'
        ]
    },
    'font-formats': {
        score: 0,
        strings: [
            'font/ttf',
            'font/otf',
            'font/woff',
            'font/woff2',
            'font/collection',
            'font/sfnt',
            'ttf',
            'otf',
            'woff',
            'woff2',
            'ttc'
        ],
        exports: [ 'parseFont', 'getFontFormat', 'loadFont' ]
    },
    'forever': {
        score: 0,
        strings: [
            'start',
            'stop',
            'restart',
            'list',
            'logs',
            'cleanlogs',
            'columns',
            'load',
            'save',
            'stopall',
            'restartall'
        ],
        exports: []
    },
    'form-data': {
        score: 0,
        strings: [
            'FormData',
            'append',
            'boundary',
            'Content-Disposition',
            'form-data',
            'filename',
            'Content-Type',
            'multipart/form-data',
            'getHeaders',
            'submit',
            'Content-Length',
            '_boundary'
        ],
        exports: [ 'FormData', 'append', 'getHeaders', 'submit', 'getLength' ]
    },
    'fs': {
        score: 0,
        strings: [
            'readFile',
            'writeFile',
            'readFileSync',
            'writeFileSync',
            'existsSync',
            'statSync',
            'readdirSync',
            'mkdirSync',
            'fs',
            'readdir',
            'utf8',
            'encoding',
            'package.json'
        ],
        exports: [
            'readFile',
            'writeFile',
            'readFileSync',
            'writeFileSync',
            'existsSync',
            'statSync',
            'readdirSync',
            'mkdirSync',
            'readdir'
        ]
    },
    'fuse.js': {
        score: 0,
        strings: [ 'fuse.js', 'Fuse', 'keys: [{', 'name: "nameKey"', 'weight: 2' ],
        exports: [ 'Fuse', 'search' ]
    },
    'gaxios': {
        score: 0,
        strings: [
            'gaxios',
            'request',
            'get',
            'post',
            'put',
            'delete',
            'patch',
            'head',
            'options',
            'GaxiosError',
            'GaxiosResponse',
            'GaxiosOptions',
            'timeout',
            'retry',
            'retryConfig',
            'validateStatus',
            'transformRequest',
            'transformResponse',
            'paramsSerializer',
            'maxContentLength',
            'maxRedirects',
            'httpAgent',
            'httpsAgent',
            'proxy'
        ],
        exports: [
            'request',
            'get',
            'post',
            'put',
            'delete',
            'patch',
            'head',
            'options',
            'GaxiosError',
            'GaxiosResponse'
        ]
    },
    'glob': {
        score: 0,
        strings: [
            'glob',
            'sync',
            'minimatch',
            'Minimatch',
            'match',
            'filter',
            'makeRe',
            'braceExpand',
            'globSync',
            'globStream',
            'globIterate',
            'globParts',
            'noglobstar',
            'globstar',
            'hasMagic',
            'Glob',
            '--glob'
        ],
        exports: [
            'glob',
            'sync',
            'minimatch',
            'Minimatch',
            'globSync',
            'globStream',
            'stream',
            'globIterate',
            'iterate',
            'Glob',
            'hasMagic',
            'escape',
            'unescape'
        ]
    },
    'global': {
        score: 0,
        strings: [
            'global',
            'globalThis',
            'GLOBAL_OBJ',
            'getGlobalObject',
            'getGlobalSingleton',
            '__REACT_DEVTOOLS_GLOBAL_HOOK__',
            'storeAsGlobal',
            'addGlobalEventProcessor',
            'getGlobalScope'
        ],
        exports: []
    },
    'graphql': {
        score: 0,
        strings: [
            'GraphQL',
            'buildSchema',
            'execute',
            'parse',
            'validate',
            'graphql',
            'GraphQLSchema',
            'GraphQLObjectType',
            'GraphQLString',
            'GraphQLInt',
            'GraphQLFloat',
            'GraphQLBoolean',
            'GraphQLID',
            'GraphQLList',
            'GraphQLNonNull',
            'GraphQLEnumType',
            'GraphQLInputObjectType',
            'GraphQLIntegration',
            'graphql/execution/execute.js',
            'graphql.execute',
            'auto.graphql.graphql'
        ],
        exports: [ 'graphql', 'buildSchema', 'execute', 'parse', 'validate' ]
    },
    'grpc': {
        score: 0,
        strings: [
            '@grpc/grpc-js',
            'grpc',
            'channelz.proto',
            'test_service.proto',
            'grpcLib',
            'proto-loader-gen-types',
            'grpc-js'
        ],
        exports: [
            'loadPackageDefinition',
            'credentials',
            'Server',
            'Client',
            'ServerCredentials',
            'ChannelCredentials'
        ]
    },
    'grpc-server': {
        score: 0,
        strings: [
            'grpc',
            'Server',
            'ServerCredentials',
            'createServer',
            'createSecureServer',
            'bindAsync',
            'addService',
            'removeService',
            'start',
            'tryShutdown',
            'forceShutdown',
            'getChannelzRef',
            'HTTP2_HEADER_PATH',
            'HTTP2_HEADER_CONTENT_TYPE',
            'application/grpc',
            'channelz',
            'interceptors',
            'middleware',
            'unary',
            'clientStream',
            'serverStream',
            'bidi'
        ],
        exports: [
            'Server',
            'ServerCredentials',
            'createServer',
            'createSecureServer',
            'bindAsync',
            'addService',
            'removeService',
            'start',
            'tryShutdown',
            'forceShutdown'
        ]
    },
    'grunt': {
        score: 0,
        strings: [
            'registerTask',
            'registerMultiTask',
            'initConfig',
            'loadNpmTasks',
            'loadTasks',
            'renameTask',
            'option',
            'config',
            'template',
            'file',
            'log',
            'verbose',
            'warn',
            'fatal',
            'fail'
        ],
        exports: []
    },
    'gulp': {
        score: 0,
        strings: [ 'src', 'dest', 'watch', 'series', 'parallel', 'task', 'lastRun', 'tree', 'registry' ],
        exports: [ 'src', 'dest', 'watch', 'series', 'parallel', 'task' ]
    },
    'he': {
        score: 0,
        strings: [
            'encode',
            'decode',
            'escape',
            'unescape',
            '&amp;',
            '&lt;',
            '&gt;',
            '&quot;',
            '&#x',
            'encodeNonAscii',
            'useNamedReferences',
            'he',
            'allowUnsafeSymbols'
        ],
        exports: [ 'encode', 'decode', 'escape', 'unescape' ]
    },
    'helmet': {
        score: 0,
        strings: [
            'helmet',
            'contentSecurityPolicy',
            'crossOriginEmbedderPolicy',
            'crossOriginOpenerPolicy',
            'crossOriginResourcePolicy',
            'dnsPrefetchControl',
            'expectCt',
            'frameguard',
            'hidePoweredBy',
            'hsts',
            'ieNoOpen',
            'noSniff',
            'originAgentCluster',
            'permittedCrossDomainPolicies',
            'referrerPolicy',
            'xssFilter'
        ],
        exports: [ 'helmet' ]
    },
    'highlight.js': {
        score: 0,
        strings: [
            'hljs',
            'highlight',
            'highlightAuto',
            'highlightBlock',
            'APOS_STRING_MODE',
            'QUOTE_STRING_MODE',
            'C_LINE_COMMENT_MODE',
            'C_BLOCK_COMMENT_MODE',
            'C_NUMBER_MODE',
            'BACKSLASH_ESCAPE',
            'REGEXP_MODE',
            'TITLE_MODE',
            'HASH_COMMENT_MODE',
            'highlight.js',
            'className: "string"',
            'className: "number"',
            'className: "comment"',
            'className: "keyword"',
            'className: "function"',
            'name: "Plain text"',
            'name: "JavaScript"',
            'name: "Python"',
            'AutoIt',
            'AVR Assembly',
            'Awk',
            'X++',
            'Bash',
            'BASIC',
            'Backus–Naur Form',
            'Brainfuck',
            'C++',
            'C',
            'C/AL',
            'Cap\'n Proto',
            'Ceylon',
            'Clean',
            'Clojure',
            'Clojure REPL',
            'CMake',
            'CoffeeScript',
            'className',
            'keywords',
            'contains',
            'begin',
            'end',
            'relevance',
            'COMMENT'
        ],
        exports: [
            'highlight',
            'highlightAuto',
            'highlightBlock',
            'configure',
            'initHighlighting',
            'initHighlightingOnLoad',
            'registerLanguage',
            'listLanguages',
            'getLanguage',
            'fixMarkup'
        ]
    },
    'http': {
        score: 0,
        strings: [
            'createServer',
            'request',
            'get',
            'Agent',
            'IncomingMessage',
            'ServerResponse',
            'STATUS_CODES',
            'http.request',
            'http.get',
            'http.createServer',
            'httpIntegration',
            'enableHTTPTimings',
            'http.method',
            'http.url'
        ],
        exports: [
            'createServer',
            'request',
            'get',
            'Agent',
            'IncomingMessage',
            'ServerResponse',
            'STATUS_CODES'
        ]
    },
    'http-client': {
        score: 0,
        strings: [ 'http-client', 'HttpClient', 'httpClient' ],
        exports: [ 'HttpClient', 'get', 'post', 'put', 'delete' ]
    },
    'http-utils': {
        score: 0,
        strings: [
            'parseJsonBody',
            'parseXmlBody',
            'parseJsonErrorBody',
            'parseXmlErrorBody',
            'loadRestJsonErrorCode',
            'loadRestXmlErrorCode',
            'collectBodyString',
            'wellknownHeaderNames',
            'headerNameLowerCasedRecord',
            'TernarySearchTree',
            'isValidUserAgentAppId',
            'userAgentMiddleware',
            'getUserAgentPlugin'
        ],
        exports: [
            'parseJsonBody',
            'parseXmlBody',
            'loadRestJsonErrorCode',
            'userAgentMiddleware'
        ]
    },
    'https': {
        score: 0,
        strings: [
            'createServer',
            'request',
            'get',
            'Agent',
            'https.request',
            'https.get',
            'https.createServer',
            'httpsAgent'
        ],
        exports: [ 'createServer', 'request', 'get', 'Agent' ]
    },
    'https-proxy-agent': {
        score: 0,
        strings: [
            'https-proxy-agent',
            'HttpsProxyAgent',
            'CONNECT',
            'proxy',
            'proxyHeaders',
            'connectOpts',
            'ALPNProtocols',
            'Proxy-Authorization',
            'Proxy-Connection',
            'parseProxyResponse',
            'proxyConnect',
            'secureEndpoint',
            'servername',
            'createSocket',
            'createConnection'
        ],
        exports: [ 'HttpsProxyAgent', 'parseProxyResponse' ]
    },
    'image-formats': {
        score: 0,
        strings: [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
            'image/svg+xml',
            'image/bmp',
            'image/tiff',
            'image/avif',
            'image/heic',
            'image/heif',
            'image/jp2',
            'image/jxr',
            'image/ktx',
            'image/ktx2'
        ],
        exports: [ 'parseImage', 'getImageFormat', 'convertImage', 'resizeImage' ]
    },
    'immediate': {
        score: 0,
        strings: [
            'setImmediate',
            'MutationObserver',
            'WebKitMutationObserver',
            'MessageChannel',
            'onreadystatechange',
            'characterData'
        ],
        exports: [ 'setImmediate', 'clearImmediate' ]
    },
    'ink': {
        score: 0,
        strings: [
            'render',
            'Box',
            'Text',
            'Newline',
            'Spacer',
            'useInput',
            'useApp',
            'useStdin',
            'useStdout',
            'useStderr',
            'useFocus',
            'useFocusManager',
            'measureElement',
            'Static',
            'exitOnCtrlC',
            'ink',
            'rendererPackageName: "ink"',
            'bundleType: 0'
        ],
        exports: [
            'render',
            'Box',
            'Text',
            'Newline',
            'Spacer',
            'useInput',
            'useApp',
            'useStdin',
            'useStdout',
            'useStderr'
        ]
    },
    'inquirer': {
        score: 0,
        strings: [
            'inquirer',
            'prompt',
            'questions',
            'input',
            'confirm',
            'list',
            'checkbox',
            'password',
            'editor'
        ],
        exports: [ 'prompt', 'createPromptModule', 'registerPrompt', 'Separator' ]
    },
    'inspector': {
        score: 0,
        strings: [
            'inspector',
            'Session',
            'Debugger.enable',
            'Debugger.paused',
            'Debugger.resume',
            'setPauseOnExceptions',
            'Runtime.getProperties',
            'getLocalVariables',
            'objectId'
        ],
        exports: [ 'Session', 'open', 'close', 'url' ]
    },
    'ioredis': {
        score: 0,
        strings: [
            'Redis',
            'Cluster',
            'Command',
            'Pipeline',
            'get',
            'set',
            'del',
            'exists',
            'expire',
            'ttl',
            'incr',
            'decr',
            'lpush',
            'rpush',
            'lpop',
            'rpop',
            'sadd',
            'srem',
            'smembers',
            'hget',
            'hset',
            'hdel',
            'hgetall',
            'zadd',
            'zrem',
            'zrange',
            'zrevrange'
        ],
        exports: [ 'Redis', 'Cluster' ]
    },
    'is-stream': {
        score: 0,
        strings: [
            'isStream',
            'writable',
            'readable',
            'duplex',
            'transform',
            'pipe',
            '_write',
            '_read',
            '_transform',
            '_writableState',
            '_readableState',
            'PassThrough'
        ],
        exports: [ 'isStream', 'writable', 'readable', 'duplex', 'transform' ]
    },
    'jest': {
        score: 0,
        strings: [
            'describe',
            'it',
            'test',
            'expect',
            'beforeEach',
            'afterEach',
            'beforeAll',
            'afterAll',
            'jest',
            'mock',
            'spyOn',
            'mockImplementation',
            'mockReturnValue',
            'mockResolvedValue',
            'mockRejectedValue',
            'clearAllMocks',
            'resetAllMocks',
            'restoreAllMocks',
            'toBe',
            'toEqual',
            'toMatch',
            'toContain',
            'toHaveLength',
            'toThrow',
            'toHaveBeenCalled'
        ],
        exports: [ 'describe', 'it', 'test', 'expect', 'jest' ]
    },
    'joi': {
        score: 0,
        strings: [
            'string',
            'number',
            'boolean',
            'date',
            'array',
            'object',
            'binary',
            'alternatives',
            'any',
            'valid',
            'invalid',
            'required',
            'optional',
            'forbidden',
            'strip',
            'default',
            'description',
            'notes',
            'tags',
            'example',
            'unit',
            'min',
            'max',
            'length',
            'regex',
            'alphanum'
        ],
        exports: [ 'string', 'number', 'boolean', 'date', 'array', 'object' ]
    },
    'jsonwebtoken': {
        score: 0,
        strings: [
            'sign',
            'verify',
            'decode',
            'JsonWebTokenError',
            'TokenExpiredError',
            'NotBeforeError',
            'algorithm',
            'expiresIn',
            'notBefore',
            'audience',
            'issuer',
            'jwtid',
            'subject',
            'noTimestamp',
            'header',
            'keyid'
        ],
        exports: [ 'sign', 'verify', 'decode' ]
    },
    'json-serialization': {
        score: 0,
        strings: [
            'JsonReplacer',
            'jsonReviver',
            'JSON.parse',
            'JSON.stringify',
            'NumericValue',
            'bigDecimal',
            'bigint',
            'LazyJsonString',
            'parseRfc3339DateTimeWithOffset',
            'parseRfc7231DateTime',
            'parseEpochTimestamp',
            'dateToUtcString',
            'toISOString',
            'getTime'
        ],
        exports: [ 'JsonReplacer', 'jsonReviver', 'NumericValue', 'LazyJsonString' ]
    },
    'jsonschema': {
        score: 0,
        strings: [
            'validate',
            'Validator',
            'Schema',
            'ValidationError',
            'SchemaError',
            'addSchema',
            'setSchemas',
            'getSchema',
            'unresolvedRefs'
        ],
        exports: [ 'validate', 'Validator' ]
    },
    'knex': {
        score: 0,
        strings: [
            'knex',
            'select',
            'from',
            'where',
            'join',
            'leftJoin',
            'rightJoin',
            'innerJoin',
            'outerJoin',
            'groupBy',
            'orderBy',
            'having',
            'limit',
            'offset',
            'insert',
            'update',
            'del',
            'truncate',
            'schema',
            'raw'
        ],
        exports: [ 'knex' ]
    },
    'language-grammars': {
        score: 0,
        strings: [
            'grammar',
            'lexemes',
            'patterns',
            'rules',
            'tokens',
            'beginKeywords',
            'endKeywords',
            'excludeBegin',
            'excludeEnd',
            'returnBegin',
            'returnEnd',
            'endsWithParent',
            'endsParent',
            'starts',
            'variants',
            'illegal',
            'case_insensitive'
        ],
        exports: [ 'defineGrammar', 'parseGrammar', 'compileGrammar' ]
    },
    'localforage': {
        score: 0,
        strings: [
            'localforage',
            'localForage',
            '_defaultConfig',
            'storeName',
            'setDriver',
            'getItem',
            'setItem',
            'removeItem',
            'clear',
            'indexedDB',
            'webkitIndexedDB',
            'mozIndexedDB',
            'length',
            'key',
            'keys',
            'iterate',
            'dropInstance',
            'asyncStorage'
        ],
        exports: [
            'getItem',
            'setItem',
            'removeItem',
            'clear',
            'length',
            'key',
            'keys',
            'iterate',
            'config',
            'setDriver'
        ]
    },
    'lodash': {
        score: 0,
        strings: [
            'isArray',
            'isObject',
            'isString',
            'isNumber',
            'isBoolean',
            'isFunction',
            'isUndefined',
            'isNull',
            'isEmpty',
            'isEqual',
            'clone',
            'cloneDeep',
            'merge',
            'assign',
            'pick',
            'omit',
            'map',
            'filter',
            'reduce',
            'forEach',
            'find',
            'findIndex',
            'includes',
            'some',
            'every',
            'sortBy',
            'groupBy',
            'uniq',
            'flatten',
            'flattenDeep',
            'compact',
            'difference',
            'intersection',
            'union',
            'zip',
            'unzip',
            'chunk',
            'take',
            'drop',
            'slice',
            '__lodash_hash_undefined__',
            'lodash.templateSources',
            '4.17.21',
            'UNDERSCORE_IDENT_RE',
            'UNDERSCORE_TITLE_MODE',
            'Expected a function',
            'Invalid `variable` option passed into `_.template`',
            'Unsupported core-js use',
            'keys',
            'values'
        ],
        exports: [
            'isArray',
            'isObject',
            'isString',
            'isNumber',
            'isBoolean',
            'isFunction',
            'isUndefined',
            'isNull',
            'isEmpty',
            'isEqual',
            'clone',
            'cloneDeep',
            'merge',
            'assign',
            'pick',
            'omit',
            'map',
            'filter',
            'reduce',
            'forEach',
            'find',
            'findIndex',
            'some',
            'every',
            'keys',
            'values',
            'debounce',
            'throttle',
            'template',
            'escape',
            'unescape'
        ]
    },
    'lodash-full': {
        score: 0,
        strings: [
            'lodash',
            '_',
            'templateSettings',
            'escape',
            'evaluate',
            'interpolate',
            'variable',
            'imports',
            'assign',
            'rest',
            'decorate',
            'param',
            'metadata',
            'awaiter',
            'generator',
            'exportStar',
            'createBinding',
            'values',
            'read',
            'spread',
            'spreadArrays',
            'spreadArray',
            'await',
            'asyncGenerator',
            'asyncDelegator',
            'asyncValues',
            'makeTemplateObject',
            'importStar',
            'importDefault',
            'classPrivateFieldGet',
            'classPrivateFieldSet'
        ],
        exports: [
            'assign',
            'rest',
            'values',
            'read',
            'spread',
            'spreadArrays',
            'spreadArray',
            'createBinding',
            'importStar',
            'importDefault',
            'templateSettings'
        ]
    },
    'lru-cache': {
        score: 0,
        strings: [
            'LRUCache',
            'set',
            'get',
            'has',
            'delete',
            'clear',
            'size',
            'max',
            'ttl',
            'allowStale',
            'updateAgeOnGet',
            'noDeleteOnFetchRejection',
            'lru-cache',
            'LRU',
            'maxAge',
            'length',
            'itemCount',
            'del',
            'reset',
            'keys',
            'values',
            'prune',
            'dump',
            'load'
        ],
        exports: [ 'LRUCache', 'LRU' ]
    },
    'marked': {
        score: 0,
        strings: [
            'marked',
            'blockquote',
            'blockquoteStart',
            'blockquoteSetextReplace',
            'tokenizer',
            'renderer',
            'parser',
            'lexer',
            'walkTokens'
        ],
        exports: [
            'marked',
            'parse',
            'parseInline',
            'Tokenizer',
            'Renderer',
            'TextRenderer',
            'Lexer',
            'Parser',
            'setOptions',
            'getDefaults'
        ]
    },
    'massive': {
        score: 0,
        strings: [
            'massive',
            'connect',
            'query',
            'find',
            'findOne',
            'insert',
            'update',
            'destroy',
            'save',
            'reload',
            'listTables',
            'listViews',
            'listFunctions',
            'listSchemas'
        ],
        exports: [ 'massive' ]
    },
    'mime-db': {
        score: 0,
        strings: [
            'application/',
            'text/',
            'image/',
            'audio/',
            'video/',
            'multipart/',
            'message/',
            'model/',
            'source: "iana"',
            'compressible:',
            'extensions:',
            'mime-db',
            'application/json',
            'application/javascript',
            'application/pdf',
            'application/xml',
            'text/html',
            'text/plain',
            'image/jpeg',
            'image/png',
            'video/mp4',
            'audio/mpeg',
            'compressible',
            'extensions',
            'charset',
            'source'
        ],
        exports: [ 'mime-db' ]
    },
    'mime-types': {
        score: 0,
        strings: [
            'application/vnd.openxmlformats',
            'compressible',
            'source',
            'charset',
            'extensions',
            'mime-types',
            'mime-db',
            'lookup',
            'extension',
            'application/json',
            'text/html',
            'image/jpeg',
            'video/mp4'
        ],
        exports: [ 'lookup', 'contentType', 'extension', 'charset', 'types' ]
    },
    'minimatch': {
        score: 0,
        strings: [
            'minimatch',
            'Minimatch',
            'match',
            'filter',
            'makeRe',
            'braceExpand',
            'parse',
            'sep',
            'brace expression',
            'not in a brace expression'
        ],
        exports: [ 'minimatch', 'Minimatch', 'match', 'filter', 'makeRe' ]
    },
    'mocha': {
        score: 0,
        strings: [
            'describe',
            'it',
            'before',
            'after',
            'beforeEach',
            'afterEach',
            'suite',
            'test',
            'setup',
            'teardown',
            'suiteSetup',
            'suiteTeardown'
        ],
        exports: [ 'describe', 'it', 'before', 'after', 'beforeEach', 'afterEach' ]
    },
    'moment': {
        score: 0,
        strings: [
            'moment',
            'format',
            'parse',
            'isValid',
            'isBefore',
            'isAfter',
            'isSame',
            'isBetween',
            'add',
            'subtract',
            'startOf',
            'endOf',
            'diff',
            'duration',
            'locale',
            'utc',
            'tz',
            'timezone'
        ],
        exports: [ 'moment' ]
    },
    'mongodb': {
        score: 0,
        strings: [
            'mongodb',
            'mongoose',
            'Mongo',
            'MongoIntegration',
            'db.system',
            'db.name',
            'db.operation',
            'db.mongodb.collection',
            'auto.db.mongo',
            'Collection',
            'aggregate',
            'bulkWrite',
            'countDocuments'
        ],
        exports: [ 'MongoClient', 'Collection', 'Db' ]
    },
    'mongoose': {
        score: 0,
        strings: [
            'mongoose',
            'Schema',
            'model',
            'connect',
            'connection',
            'disconnect',
            'ObjectId',
            'Mixed',
            'populate',
            'aggregate',
            'find',
            'findOne',
            'findById',
            'findOneAndUpdate',
            'findByIdAndUpdate',
            'save',
            'remove',
            'deleteOne',
            'deleteMany',
            'updateOne',
            'updateMany',
            'countDocuments'
        ],
        exports: [ 'mongoose' ]
    },
    'morgan': {
        score: 0,
        strings: [ 'morgan', 'combined', 'common', 'dev', 'short', 'tiny', 'compile', 'format', 'token' ],
        exports: [ 'morgan' ]
    },
    'mozilla-xul': {
        score: 0,
        strings: [
            'mozilla',
            'xul',
            'vnd.mozilla.xul+xml',
            'XUL',
            'XML User Interface Language'
        ],
        exports: [ 'parseXUL', 'createXULDocument' ]
    },
    'multer': {
        score: 0,
        strings: [
            'multer',
            'single',
            'array',
            'fields',
            'none',
            'any',
            'diskStorage',
            'memoryStorage',
            'destination',
            'filename'
        ],
        exports: [ 'multer' ]
    },
    'mysql': {
        score: 0,
        strings: [
            'createConnection',
            'createPool',
            'createPoolCluster',
            'escape',
            'escapeId',
            'format',
            'Connection',
            'Pool',
            'PoolCluster',
            'mysql',
            'Mysql',
            'MysqlIntegration',
            'mysql/lib/Connection.js',
            'db.system',
            'auto.db.mysql',
            'createQuery',
            'server.address',
            'server.port',
            'db.user'
        ],
        exports: [
            'createConnection',
            'createPool',
            'createPoolCluster',
            'createQuery'
        ]
    },
    'mysql2': {
        score: 0,
        strings: [
            'createConnection',
            'createPool',
            'createPoolCluster',
            'escape',
            'escapeId',
            'format',
            'raw',
            'Connection',
            'Pool',
            'PoolCluster'
        ],
        exports: [ 'createConnection', 'createPool', 'createPoolCluster' ]
    },
    'nanoid': {
        score: 0,
        strings: [ 'nanoid', 'customAlphabet', 'urlAlphabet' ],
        exports: [ 'nanoid', 'customAlphabet', 'urlAlphabet' ]
    },
    'net': {
        score: 0,
        strings: [ 'createServer', 'createConnection', 'connect', 'Socket', 'Server' ],
        exports: [ 'createServer', 'createConnection', 'connect', 'Socket', 'Server' ]
    },
    'next': {
        score: 0,
        strings: [
            'next',
            'Next.js',
            'nextjs',
            'sentry.javascript.nextjs',
            'nextHopProtocol',
            'nextSibling',
            'nextElementSibling',
            'nextTick',
            'nextWord',
            'nextLine',
            'nextPage',
            'nextState',
            'nextOffset'
        ],
        exports: [
            'default',
            'getServerSideProps',
            'getStaticProps',
            'getStaticPaths',
            'Head',
            'Image',
            'Link',
            'Router',
            'useRouter'
        ]
    },
    'node-fetch': {
        score: 0,
        strings: [
            'fetch',
            'isNativeFetch',
            'supportsFetch',
            'supportsNativeFetch',
            'fetchData',
            'FetchHttpHandler',
            'nativeNodeFetchintegration',
            'node-fetch',
            'Request',
            'Response',
            'Headers',
            'FetchError',
            'AbortError',
            'makeNodeTransport',
            'createGzip',
            'content-encoding',
            'gzip',
            'retry-after',
            'x-sentry-rate-limits',
            'Body',
            'Blob',
            'arrayBuffer',
            'json',
            'text',
            'buffer',
            'stream',
            'clone',
            'bodyUsed',
            'ok',
            'status',
            'statusText',
            'url',
            'redirected',
            'method',
            'headers',
            'redirect',
            'signal',
            'timeout',
            'size',
            'compress',
            'follow',
            'agent',
            'isRedirect'
        ],
        exports: [
            'default',
            'fetch',
            'Headers',
            'Request',
            'Response',
            'FetchError',
            'AbortError',
            'Blob',
            'arrayBuffer',
            'json',
            'text',
            'buffer',
            'stream'
        ]
    },
    'node-forge': {
        score: 0,
        strings: [
            'forge',
            'pki',
            'rsa',
            'aes',
            'sha1',
            'sha256',
            'md5',
            'asn1',
            'der',
            'pem',
            'pkcs',
            'x509',
            'certificate',
            'pss',
            'oaep',
            'mgf',
            'mgf1',
            'pkcs1',
            'pkcs7',
            'pkcs8',
            'pkcs10',
            'pkcs12',
            'des',
            'rc2',
            'sha384',
            'sha512',
            'hmac',
            'pbkdf2',
            'prng',
            'random',
            'util',
            'cipher',
            'tls',
            'ssh',
            'jsbn',
            'oids',
            'privateKey'
        ],
        exports: [
            'pki',
            'rsa',
            'aes',
            'sha1',
            'sha256',
            'md5',
            'hmac',
            'asn1',
            'der',
            'pem',
            'pkcs1',
            'pkcs7',
            'pkcs12',
            'x509',
            'pkcs8',
            'pkcs10',
            'des',
            'pbkdf2',
            'random',
            'util'
        ]
    },
    'node-postgres': {
        score: 0,
        strings: [ 'Client', 'Pool', 'Query', 'Result', 'Connection', 'types', 'defaults', 'native' ],
        exports: [ 'Client', 'Pool' ]
    },
    'node_redis': {
        score: 0,
        strings: [
            'createClient',
            'RedisClient',
            'Multi',
            'get',
            'set',
            'del',
            'exists',
            'expire',
            'ttl',
            'incr',
            'decr',
            'lpush',
            'rpush',
            'lpop',
            'rpop',
            'sadd',
            'srem',
            'smembers',
            'hget',
            'hset'
        ],
        exports: [ 'createClient' ]
    },
    'nodemon': {
        score: 0,
        strings: [
            'nodemon',
            'watch',
            'ignore',
            'ext',
            'exec',
            'delay',
            'env',
            'nodeArgs',
            'script',
            'verbose',
            'quiet',
            'exitcrash'
        ],
        exports: []
    },
    'nyc': {
        score: 0,
        strings: [ 'nyc', 'coverage', 'instrument', 'report', 'check-coverage', 'lines', 'functions', 'branches', 'statements' ],
        exports: []
    },
    'objection': {
        score: 0,
        strings: [
            'Model',
            'QueryBuilder',
            'transaction',
            'raw',
            'ref',
            'lit',
            'relationMappings',
            'jsonSchema',
            'tableName',
            'idColumn',
            'query',
            'findById',
            'insert',
            'update',
            'patch',
            'delete',
            'relate',
            'unrelate',
            'eager',
            'joinEager',
            'withGraphFetched'
        ],
        exports: [ 'Model', 'QueryBuilder', 'transaction' ]
    },
    'office-document': {
        score: 0,
        strings: [
            'openxmlformats-officedocument',
            'vnd.ms-excel',
            'vnd.ms-powerpoint',
            'vnd.ms-word',
            'docx',
            'xlsx',
            'pptx',
            'dotx',
            'xltx',
            'potx',
            'presentation',
            'spreadsheet',
            'wordprocessing',
            'template'
        ],
        exports: [ 'parseDocument', 'extractText', 'getMetadata' ]
    },
    'onetime': {
        score: 0,
        strings: [
            'onetime',
            'callCount',
            'WeakMap',
            'displayName',
            'throw',
            'Function can only be called once',
            'not wrapped by the onetime package'
        ],
        exports: [ 'onetime', 'callCount' ]
    },
    'open-document': {
        score: 0,
        strings: [
            'opendocument',
            'oasis',
            'odt',
            'ods',
            'odp',
            'odg',
            'odf',
            'ott',
            'ots',
            'otp',
            'otg',
            'odft',
            'odb',
            'odi',
            'oti'
        ],
        exports: [ 'parseODT', 'parseODS', 'parseODP' ]
    },
    'opentelemetry': {
        score: 0,
        strings: [
            'opentelemetry.proto.metrics.v1',
            'HistogramDataPoint',
            'ExponentialHistogramDataPoint',
            'opentelemetry.proto.common.v1',
            'trace',
            'span',
            'metric',
            'resource',
            'instrumentation'
        ],
        exports: [ 'trace', 'metrics', 'resources', 'NodeSDK', 'getNodeAutoInstrumentations' ]
    },
    'opentelemetry-core': {
        score: 0,
        strings: [
            'opentelemetry',
            'SDK_INFO',
            'SEMRESATTRS_TELEMETRY_SDK_NAME',
            'SEMRESATTRS_PROCESS_RUNTIME_NAME',
            'SEMRESATTRS_TELEMETRY_SDK_LANGUAGE',
            'SEMRESATTRS_TELEMETRY_SDK_VERSION',
            'TELEMETRYSDKLANGUAGEVALUES_NODEJS',
            'otperformance',
            'timeOrigin',
            'hrTime',
            'hrTimeToMicroseconds',
            'hrTimeToMilliseconds',
            'hrTimeToNanoseconds',
            'hrTimeToTimeStamp',
            'hrTimeDuration',
            'timeInputToHrTime',
            'millisToHrTime',
            'getTimeOrigin',
            'addHrTimes',
            'isTimeInput',
            'isTimeInputHrTime'
        ],
        exports: [
            'SDK_INFO',
            'otperformance',
            'hrTime',
            'hrTimeToMicroseconds',
            'hrTimeToMilliseconds',
            'hrTimeToNanoseconds',
            'hrTimeToTimeStamp',
            'hrTimeDuration',
            'timeInputToHrTime'
        ]
    },
    'opentelemetry-metrics': {
        score: 0,
        strings: [
            'ExportResultCode',
            'AggregatorKind',
            'LastValueAggregator',
            'LastValueAccumulation',
            'SumAggregator',
            'SumAccumulation',
            'HistogramAggregator',
            'HistogramAccumulation',
            'ExponentialHistogramAggregator',
            'ExponentialHistogramAccumulation',
            'DropAggregator',
            'AggregationType',
            'AggregationTemporality',
            'MetricReader',
            'PeriodicExportingMetricReader',
            'toAggregation',
            'createAccumulation',
            'merge',
            'diff',
            'toMetricData',
            'record'
        ],
        exports: [
            'ExportResultCode',
            'LastValueAggregator',
            'SumAggregator',
            'HistogramAggregator',
            'MetricReader',
            'PeriodicExportingMetricReader',
            'AggregationType'
        ]
    },
    'opentelemetry-propagation': {
        score: 0,
        strings: [
            'CompositePropagator',
            'W3CTraceContextPropagator',
            'TraceState',
            'parseTraceParent',
            'TRACE_PARENT_HEADER',
            'TRACE_STATE_HEADER',
            'traceparent',
            'tracestate',
            'validateKey',
            'validateValue',
            'inject',
            'extract',
            'fields',
            'propagators',
            'traceId',
            'spanId',
            'traceFlags',
            'isRemote',
            'serialize',
            'unset'
        ],
        exports: [
            'CompositePropagator',
            'W3CTraceContextPropagator',
            'TraceState',
            'parseTraceParent',
            'TRACE_PARENT_HEADER',
            'TRACE_STATE_HEADER'
        ]
    },
    'opentelemetry-utils': {
        score: 0,
        strings: [
            'isPlainObject',
            'merge',
            'callWithTimeout',
            'TimeoutError',
            'isUrlIgnored',
            'urlMatches',
            'Deferred',
            'BindOnceFuture',
            'diagLogLevelFromString',
            '_export',
            'suppressTracing',
            'isTracingSuppressed',
            'unsuppressTracing',
            'setRPCMetadata',
            'getRPCMetadata',
            'deleteRPCMetadata',
            'RPCType'
        ],
        exports: [
            'isPlainObject',
            'merge',
            'callWithTimeout',
            'TimeoutError',
            'isUrlIgnored',
            'Deferred',
            'BindOnceFuture',
            'diagLogLevelFromString'
        ]
    },
    'os': {
        score: 0,
        strings: [
            'platform',
            'arch',
            'release',
            'type',
            'homedir',
            'tmpdir',
            'hostname',
            'userInfo',
            'cpus',
            'totalmem',
            'freemem',
            'os',
            'uptime',
            'darwin',
            'linux',
            'win32'
        ],
        exports: [
            'platform',
            'arch',
            'release',
            'type',
            'homedir',
            'tmpdir',
            'hostname',
            'userInfo',
            'cpus',
            'totalmem',
            'freemem',
            'uptime'
        ]
    },
    'parcel': {
        score: 0,
        strings: [ 'Bundler', 'bundle', 'watch', 'serve', 'getPort', 'Asset', 'Transformer', 'Packager', 'Resolver' ],
        exports: [ 'Bundler' ]
    },
    'parse5': {
        score: 0,
        strings: [
            'parse5',
            'parse',
            'parseFragment',
            'serialize',
            'serializeOuter',
            'ParserError',
            'TreeAdapter',
            'DefaultTreeAdapter',
            'SAXParser',
            'LocationInfoParserMixin',
            'LocationInfoTreeAdapter',
            'ParserStream',
            'PlainTextConversionStream',
            'SerializerStream',
            'SAXParserStream',
            'treeAdapters',
            'html',
            'htmlparser2'
        ],
        exports: [
            'parse',
            'parseFragment',
            'serialize',
            'serializeOuter',
            'ParserError',
            'TreeAdapter',
            'DefaultTreeAdapter',
            'SAXParser'
        ]
    },
    'passport': {
        score: 0,
        strings: [
            'passport',
            'initialize',
            'session',
            'authenticate',
            'authorize',
            'serializeUser',
            'deserializeUser',
            'use',
            'Strategy'
        ],
        exports: [ 'passport' ]
    },
    'passport-jwt': {
        score: 0,
        strings: [
            'JwtStrategy',
            'ExtractJwt',
            'fromAuthHeaderAsBearerToken',
            'fromAuthHeaderWithScheme',
            'fromBodyField',
            'fromUrlQueryParameter',
            'fromExtractors',
            'jwtFromRequest',
            'secretOrKey',
            'issuer',
            'audience'
        ],
        exports: [ 'Strategy', 'ExtractJwt' ]
    },
    'passport-local': {
        score: 0,
        strings: [ 'LocalStrategy', 'usernameField', 'passwordField', 'passReqToCallback' ],
        exports: [ 'Strategy' ]
    },
    'path': {
        score: 0,
        strings: [
            'join',
            'resolve',
            'normalize',
            'dirname',
            'basename',
            'extname',
            'isAbsolute',
            'relative',
            'parse',
            'format',
            'sep',
            'delimiter',
            'path'
        ],
        exports: [
            'join',
            'resolve',
            'normalize',
            'dirname',
            'basename',
            'extname',
            'isAbsolute',
            'relative',
            'parse',
            'format',
            'sep',
            'delimiter'
        ]
    },
    'perf_hooks': {
        score: 0,
        strings: [ 'performance', 'PerformanceObserver', 'mark', 'measure' ],
        exports: [ 'performance', 'PerformanceObserver' ]
    },
    'pg': {
        score: 0,
        strings: [
            'Client',
            'Pool',
            'connect',
            'query',
            'end',
            'release',
            'ConnectionParameters',
            'defaults',
            'types',
            'pg',
            'postgres',
            'Postgres',
            'PostgresIntegration',
            'pg-native',
            'db.system',
            'postgresql',
            'db.name',
            'server.address',
            'server.port',
            'db.user',
            'auto.db.postgres'
        ],
        exports: [ 'Client', 'Pool', 'native' ]
    },
    'pm2': {
        score: 0,
        strings: [
            'start',
            'stop',
            'restart',
            'reload',
            'delete',
            'list',
            'describe',
            'monit',
            'logs',
            'flush',
            'reloadLogs',
            'sendSignal',
            'ping',
            'sendDataToProcessId',
            'startLogging',
            'stopLogging'
        ],
        exports: []
    },
    'prettier': {
        score: 0,
        strings: [
            'format',
            'check',
            'formatWithCursor',
            'getFileInfo',
            'getSupportInfo',
            'resolveConfig',
            'clearConfigCache',
            'resolveConfigFile',
            'prettier',
            'Prettier',
            '^2.8.8',
            'eslint-config-prettier',
            'eslint-plugin-prettier'
        ],
        exports: [ 'format', 'check', 'formatWithCursor', 'getFileInfo', 'getSupportInfo', 'resolveConfig' ]
    },
    'prisma': {
        score: 0,
        strings: [
            'PrismaClient',
            'prisma',
            'findMany',
            'findUnique',
            'findFirst',
            'create',
            'update',
            'upsert',
            'delete',
            'deleteMany',
            'updateMany',
            'count',
            'aggregate',
            'groupBy',
            'connect',
            'disconnect',
            'transaction',
            'executeRaw',
            'queryRaw',
            'Prisma',
            'PrismaIntegration',
            '$use',
            '_sentryInstrumented',
            'db.prisma',
            'auto.db.prisma',
            '_engineConfig',
            'activeProvider',
            'clientVersion',
            'db.prisma.version'
        ],
        exports: [ 'PrismaClient' ]
    },
    'process': {
        score: 0,
        strings: [
            'process.argv',
            'process.env',
            'process.exit',
            'process.platform',
            'process.version',
            'process.cwd',
            'process.stdin',
            'process.stdout',
            'process.stderr',
            'process.on',
            'process.off'
        ],
        exports: []
    },
    'programming-languages': {
        score: 0,
        strings: [
            'AutoIt',
            'AVR Assembly',
            'Awk',
            'X++',
            'Bash',
            'sh',
            'zsh',
            'BASIC',
            'Backus–Naur Form',
            'BNF',
            'Brainfuck',
            'bf',
            'C++',
            'cc',
            'c++',
            'h++',
            'hpp',
            'hh',
            'hxx',
            'cxx',
            'C',
            'h',
            'C/AL',
            'Cap\'n Proto',
            'capnp',
            'Ceylon',
            'Clean',
            'icl',
            'dcl',
            'Clojure',
            'clj',
            'CMake',
            'cmake.in'
        ],
        exports: [ 'getLanguageDefinition', 'registerLanguage', 'autoDetection' ]
    },
    'protobuf-metrics': {
        score: 0,
        strings: [
            'opentelemetry.proto.metrics.v1',
            'NumberDataPoint',
            'HistogramDataPoint',
            'ExponentialHistogramDataPoint',
            'SummaryDataPoint',
            'Gauge',
            'Sum',
            'Histogram',
            'ExponentialHistogram',
            'Summary',
            'AggregationTemporality',
            'DataPointFlags',
            'Exemplar',
            'encode',
            'decode',
            'verify',
            'fromObject',
            'toObject',
            'toJSON',
            'getTypeUrl',
            'dataPoints',
            'aggregationTemporality',
            'isMonotonic',
            'bucketCounts',
            'explicitBounds',
            'exemplars',
            'attributes'
        ],
        exports: [
            'NumberDataPoint',
            'HistogramDataPoint',
            'Gauge',
            'Sum',
            'Histogram',
            'ExponentialHistogram',
            'Summary',
            'AggregationTemporality'
        ]
    },
    'protobufjs': {
        score: 0,
        strings: [
            'encode',
            'decode',
            'verify',
            'fromObject',
            'toObject',
            'create',
            'uint32',
            'fork',
            'ldelim',
            'pos',
            'len'
        ],
        exports: [
            'load',
            'loadSync',
            'Root',
            'Type',
            'Field',
            'Namespace',
            'ReflectionObject',
            'Enum',
            'Service',
            'Method'
        ]
    },
    'punycode': {
        score: 0,
        strings: [ 'encode', 'decode', 'toUnicode', 'toASCII' ],
        exports: [ 'encode', 'decode', 'toUnicode', 'toASCII' ]
    },
    'querystring': {
        score: 0,
        strings: [ 'parse', 'stringify', 'escape', 'unescape' ],
        exports: [ 'parse', 'stringify', 'escape', 'unescape' ]
    },
    'react': {
        score: 0,
        strings: [
            'React',
            'Component',
            'PureComponent',
            'createElement',
            'Fragment',
            'useState',
            'useEffect',
            'useContext',
            'useReducer',
            'useCallback',
            'useMemo',
            'useRef',
            'useImperativeHandle',
            'useLayoutEffect',
            'useDebugValue',
            'createContext',
            'forwardRef',
            'memo',
            'default.createElement',
            'default.Fragment',
            'react',
            'reconcilerVersion',
            'dispatcherHookName',
            '__REACT_DEVTOOLS_GLOBAL_HOOK__',
            'react-devtools-core',
            'ReactDOM',
            'react-dom',
            'Suspense',
            'StrictMode',
            'Profiler',
            'Children',
            'cloneElement',
            'createRef',
            'useDeferredValue',
            'useId',
            'useInsertionEffect',
            'useSyncExternalStore',
            'useTransition',
            'startTransition',
            'version',
            '18.3.1'
        ],
        exports: [
            'Component',
            'PureComponent',
            'createElement',
            'Fragment',
            'useState',
            'useEffect',
            'useContext',
            'useReducer',
            'useCallback',
            'useLayoutEffect',
            'useMemo',
            'useRef',
            'useImperativeHandle',
            'Suspense',
            'Children',
            'cloneElement',
            'createContext',
            'createRef',
            'forwardRef'
        ]
    },
    'react-component-filters': {
        score: 0,
        strings: [
            'componentFilters',
            'ElementTypeClass',
            'ElementTypeForwardRef',
            'ElementTypeFunction',
            'ElementTypeMemo',
            'isEnabled',
            'RegExp',
            'LOCAL_STORAGE_COMPONENT_FILTER_PREFERENCES_KEY'
        ],
        exports: [ 'getDefaultComponentFilters', 'saveComponentFilters', 'loadComponentFilters' ]
    },
    'react-devtools': {
        score: 0,
        strings: [
            'react-devtools',
            'React::DevTools',
            'connectToDevTools',
            'connectWithCustomMessagingProtocol',
            'defaultTab',
            'componentFilters',
            'lastSelection',
            'openInEditorUrl',
            'parseHookNames',
            'theme',
            'appendComponentStack',
            'showInlineWarningsAndErrors'
        ],
        exports: [ 'connectToDevTools', 'connectWithCustomMessagingProtocol' ]
    },
    'react-devtools-shared': {
        score: 0,
        strings: [
            'react-devtools-shared',
            'getDisplayNameForFiber',
            'getIsProfiling',
            'getLaneLabelMap',
            'currentDispatcherRef',
            'workTagMap',
            'reactVersion',
            'profilingHooks',
            'getTimelineData',
            'toggleProfilingStatus',
            'recordMount',
            'recordUnmount',
            'mountFiberRecursively',
            'updateFiberRecursively'
        ],
        exports: [ 'getDisplayNameForFiber', 'getIsProfiling', 'recordMount', 'recordUnmount' ]
    },
    'react-dom': {
        score: 0,
        strings: [
            'react-dom',
            'ReactDOM',
            'render',
            'hydrate',
            'unmountComponentAtNode',
            'findDOMNode',
            'createPortal',
            'flushSync'
        ],
        exports: [
            'render',
            'hydrate',
            'unmountComponentAtNode',
            'findDOMNode',
            'createPortal',
            'flushSync',
            'unstable_batchedUpdates'
        ]
    },
    'react-element-type': {
        score: 0,
        strings: [
            'react.element',
            'react.transitional.element',
            'react.portal',
            'react.fragment',
            'react.strict_mode',
            'react.profiler',
            'react.provider',
            'react.consumer',
            'react.context',
            'react.forward_ref',
            'react.suspense',
            'react.memo',
            'react.lazy'
        ],
        exports: [ 'isValidElementType', 'getElementType' ]
    },
    'react-error-boundary': {
        score: 0,
        strings: [
            'ErrorBoundary',
            'componentDidCatch',
            'getDerivedStateFromError',
            'componentStack',
            'error.stack',
            'error.message',
            'WeakMap'
        ],
        exports: [ 'ErrorBoundary', 'withErrorBoundary', 'useErrorHandler' ]
    },
    'react-fiber': {
        score: 0,
        strings: [
            'fiber',
            'FiberNode',
            'createFiber',
            'cloneFiber',
            'workInProgress',
            'current',
            'pendingLanes',
            'suspendedLanes',
            'pingedLanes',
            'expiredLanes',
            'mutableReadLanes',
            'finishedLanes'
        ],
        exports: [ 'createFiber', 'cloneFiber', 'createWorkInProgress' ]
    },
    'react-fiber-tree': {
        score: 0,
        strings: [
            'SuspenseComponent',
            'memoizedState',
            'treeBaseDuration',
            'actualDuration',
            'hasOwnProperty',
            '_debugOwner',
            '_debugNeedsRemount',
            'bundleType',
            'StrictMode',
            'ConcurrentMode',
            'ProfileMode'
        ],
        exports: [
            'traverseFiberTree',
            'findFiberByHostInstance',
            'getFiberCurrentPropsFromNode'
        ]
    },
    'react-hooks': {
        score: 0,
        strings: [
            'useState',
            'useEffect',
            'useContext',
            'useReducer',
            'useCallback',
            'useMemo',
            'useRef',
            'useImperativeHandle',
            'useLayoutEffect',
            'useDebugValue',
            'useDeferredValue',
            'useTransition',
            'useId',
            'useSyncExternalStore',
            'useInsertionEffect',
            'useMutableSource'
        ],
        exports: [
            'useState',
            'useEffect',
            'useContext',
            'useReducer',
            'useCallback',
            'useMemo',
            'useRef',
            'useImperativeHandle',
            'useLayoutEffect'
        ]
    },
    'react-profiler': {
        score: 0,
        strings: [
            'profiler',
            'profilingHooks',
            'changeDescriptions',
            'durations',
            'commitTime',
            'maxActualDuration',
            'priorityLevel',
            'updaters',
            'effectDuration',
            'passiveEffectDuration',
            'memoizedInteractions'
        ],
        exports: [ 'startProfiling', 'stopProfiling', 'getProfilingData', 'clearProfilingData' ]
    },
    'react-reconciler': {
        score: 0,
        strings: [
            'reconciler',
            'ReactCurrentOwner',
            'memoizedState',
            'updateQueue',
            'childLanes',
            'pendingProps',
            'stateNode',
            'alternate',
            'flags',
            'subtreeFlags',
            'lanes',
            'return',
            'sibling',
            'child',
            'react-reconciler',
            'Fiber',
            'FiberNode',
            'memoizedProps'
        ],
        exports: [
            'createReconciler',
            'updateContainer',
            'createContainer',
            'getPublicInstance'
        ]
    },
    'react-suspense': {
        score: 0,
        strings: [
            'Suspense',
            'SuspenseList',
            'fallback',
            'dehydrated',
            'retryLane',
            'pingCache',
            'treeContext',
            'baseLanes',
            'cachePool',
            'transitions'
        ],
        exports: [ 'Suspense', 'SuspenseList', 'lazy' ]
    },
    'readline': {
        score: 0,
        strings: [ 'createInterface', 'question', 'close', 'pause', 'resume' ],
        exports: [ 'createInterface' ]
    },
    'redis': {
        score: 0,
        strings: [
            'createClient',
            'RedisClient',
            'get',
            'set',
            'del',
            'exists',
            'expire',
            'ttl',
            'incr',
            'decr',
            'lpush',
            'rpush',
            'lpop',
            'rpop',
            'sadd',
            'srem',
            'smembers',
            'hget',
            'hset',
            'hdel',
            'hgetall'
        ],
        exports: [ 'createClient' ]
    },
    'repl': {
        score: 0,
        strings: [ 'start', 'REPLServer' ],
        exports: [ 'start', 'REPLServer' ]
    },
    'request': {
        score: 0,
        strings: [
            'request',
            'addRequestDataToEvent',
            'addRequestDataToTransaction',
            'extractRequestData',
            'requestDataIntegration',
            'RequestData',
            'shouldCreateSpanForRequest',
            'request_headers',
            'setRequestHeader'
        ],
        exports: [ 'request', 'get', 'post', 'put', 'delete', 'head', 'options' ]
    },
    'rimraf': {
        score: 0,
        strings: [ 'rimraf', '3.0.2', '^3.0.2' ],
        exports: [ 'rimraf', 'sync' ]
    },
    'rollup': {
        score: 0,
        strings: [ 'rollup', 'bundle', 'generate', 'write', 'watch', 'Plugin', 'InputOptions', 'OutputOptions', 'RollupBuild', 'RollupOptions' ],
        exports: [ 'rollup' ]
    },
    'rxjs': {
        score: 0,
        strings: [
            'rxjs',
            'Observable',
            'Subscriber',
            'Subscription',
            'isFunction',
            'UnsubscriptionError',
            'arrRemove',
            'timeoutProvider',
            'setTimeout',
            'clearTimeout',
            'reportUnhandledError',
            'noop',
            'createNotification',
            'nextNotification',
            'errorNotification',
            'COMPLETE_NOTIFICATION'
        ],
        exports: [
            'Observable',
            'Subscriber',
            'Subscription',
            'Subject',
            'BehaviorSubject',
            'ReplaySubject',
            'AsyncSubject',
            'of',
            'from',
            'merge',
            'combineLatest'
        ]
    },
    'rxjs-advanced-operators': {
        score: 0,
        strings: [
            'distinctUntilKeyChanged',
            'elementAt',
            'endWith',
            'every',
            'exhaust',
            'exhaustAll',
            'exhaustMap',
            'expand',
            'filter',
            'finalize',
            'find',
            'findIndex',
            'first',
            'groupBy',
            'ignoreElements',
            'isEmpty',
            'last',
            'map',
            'mapTo',
            'materialize',
            'max',
            'mergeAll',
            'flatMap',
            'mergeMap'
        ],
        exports: [
            'distinctUntilKeyChanged',
            'elementAt',
            'endWith',
            'every',
            'exhaust',
            'exhaustAll',
            'exhaustMap',
            'expand',
            'filter',
            'finalize'
        ]
    },
    'rxjs-buffer-operators': {
        score: 0,
        strings: [
            'audit',
            'auditTime',
            'buffer',
            'bufferCount',
            'bufferTime',
            'bufferToggle',
            'bufferWhen',
            'catchError',
            'scanInternals',
            'reduce',
            'toArray',
            'joinAllInternals',
            'combineLatestAll'
        ],
        exports: [
            'audit',
            'auditTime',
            'buffer',
            'bufferCount',
            'bufferTime',
            'bufferToggle',
            'bufferWhen',
            'catchError',
            'reduce',
            'toArray'
        ]
    },
    'rxjs-combination-operators': {
        score: 0,
        strings: [
            'combineAll',
            'combineLatest',
            'combineLatestWith',
            'concatMap',
            'concatMapTo',
            'concatWith',
            'fromSubscribable',
            'connect',
            'count',
            'debounce',
            'debounceTime'
        ],
        exports: [
            'combineAll',
            'combineLatest',
            'combineLatestWith',
            'concatMap',
            'concatMapTo',
            'concatWith',
            'connect',
            'count',
            'debounce',
            'debounceTime'
        ]
    },
    'rxjs-internal': {
        score: 0,
        strings: [
            'executeSchedule',
            'createOperatorSubscriber',
            'innerFrom',
            'operate',
            'isFunction',
            'popScheduler',
            'popResultSelector',
            'argsOrArgArray',
            'argsArgArrayOrObject',
            'createObject',
            'mapOneOrManyArgs'
        ],
        exports: [ 'executeSchedule', 'createOperatorSubscriber', 'innerFrom', 'operate' ]
    },
    'rxjs-operators': {
        score: 0,
        strings: [
            'combineLatestInit',
            'mergeInternals',
            'mergeMap',
            'mergeAll',
            'concatAll',
            'concat',
            'defer',
            'connectable',
            'forkJoin',
            'fromEvent',
            'fromEventPattern',
            'generate',
            'iif',
            'timer',
            'interval',
            'merge',
            'never',
            'onErrorResumeNext',
            'pairs',
            'partition',
            'race',
            'range',
            'using',
            'zip'
        ],
        exports: [
            'combineLatest',
            'merge',
            'concat',
            'defer',
            'forkJoin',
            'fromEvent',
            'timer',
            'interval',
            'never',
            'race',
            'range',
            'zip'
        ]
    },
    'rxjs-operators-exports': {
        score: 0,
        strings: [
            'Object.defineProperty',
            'enumerable',
            'get',
            'bufferToggle',
            'bufferWhen',
            'catchError',
            'combineAll',
            'combineLatestAll',
            'combineLatestWith',
            'concatAll',
            'concatMap',
            'concatMapTo',
            'concatWith',
            'connect',
            'count',
            'debounce',
            'debounceTime',
            'defaultIfEmpty',
            'delay',
            'delayWhen',
            'dematerialize',
            'distinct',
            'distinctUntilChanged'
        ],
        exports: [
            'audit',
            'auditTime',
            'buffer',
            'bufferCount',
            'bufferTime',
            'bufferToggle',
            'bufferWhen',
            'catchError',
            'combineAll'
        ]
    },
    'rxjs-time-operators': {
        score: 0,
        strings: [
            'sample',
            'sampleTime',
            'scan',
            'sequenceEqual',
            'share',
            'shareReplay',
            'single',
            'skip',
            'skipLast',
            'skipUntil',
            'skipWhile',
            'startWith',
            'subscribeOn',
            'switchAll',
            'switchMap',
            'switchMapTo',
            'switchScan',
            'take',
            'takeLast',
            'takeUntil',
            'takeWhile',
            'tap',
            'throttle'
        ],
        exports: [
            'sample',
            'sampleTime',
            'scan',
            'sequenceEqual',
            'share',
            'shareReplay',
            'single',
            'skip',
            'skipLast',
            'skipUntil',
            'skipWhile',
            'startWith'
        ]
    },
    'rxjs-utility-operators': {
        score: 0,
        strings: [
            'mergeMapTo',
            'mergeScan',
            'mergeWith',
            'min',
            'multicast',
            'observeOn',
            'onErrorResumeNext',
            'pairwise',
            'partition',
            'pluck',
            'publish',
            'publishBehavior',
            'publishLast',
            'publishReplay',
            'race',
            'raceWith',
            'reduce',
            'repeat',
            'repeatWhen',
            'retry',
            'retryWhen',
            'refCount'
        ],
        exports: [
            'mergeMapTo',
            'mergeScan',
            'mergeWith',
            'min',
            'multicast',
            'observeOn',
            'onErrorResumeNext',
            'pairwise',
            'partition',
            'pluck',
            'publish'
        ]
    },
    'rxjs-window-operators': {
        score: 0,
        strings: [
            'throttleTime',
            'throwIfEmpty',
            'timeInterval',
            'timeout',
            'timeoutWith',
            'timestamp',
            'toArray',
            'window',
            'windowCount',
            'windowTime',
            'windowToggle',
            'windowWhen',
            'withLatestFrom',
            'zip',
            'zipAll',
            'zipWith'
        ],
        exports: [
            'throttleTime',
            'throwIfEmpty',
            'timeInterval',
            'timeout',
            'timeoutWith',
            'timestamp',
            'toArray',
            'window',
            'windowCount',
            'windowTime'
        ]
    },
    'semver': {
        score: 0,
        strings: [
            'valid',
            'clean',
            'inc',
            'diff',
            'major',
            'minor',
            'patch',
            'prerelease',
            'compare',
            'rcompare',
            'sort',
            'rsort',
            'gt',
            'gte',
            'lt',
            'lte',
            'eq',
            'neq',
            'cmp',
            'coerce',
            'satisfies',
            'validRange',
            'outside',
            'gtr',
            'ltr',
            'intersects',
            'simplifyRange',
            'subset',
            'semver',
            'parseSemver',
            '^7.6.0',
            '^7.5.8',
            'Invalid argument not valid semver'
        ],
        exports: [
            'valid',
            'clean',
            'inc',
            'diff',
            'major',
            'minor',
            'patch',
            'prerelease',
            'compare',
            'rcompare',
            'sort',
            'rsort',
            'gt',
            'gte',
            'lt',
            'lte',
            'eq',
            'neq',
            'cmp',
            'coerce',
            'satisfies',
            'validRange',
            'outside',
            'gtr',
            'ltr'
        ]
    },
    'sequelize': {
        score: 0,
        strings: [
            'Sequelize',
            'DataTypes',
            'Model',
            'Op',
            'Transaction',
            'QueryTypes',
            'ValidationError',
            'ConnectionError',
            'TimeoutError',
            'UniqueConstraintError',
            'ForeignKeyConstraintError',
            'ExclusionConstraintError',
            'ValidationErrorItem'
        ],
        exports: [ 'Sequelize' ]
    },
    'setimmediate': {
        score: 0,
        strings: [ 'setImmediate', 'typeof setImmediate', 'queueMicrotask' ],
        exports: [ 'setImmediate', 'clearImmediate' ]
    },
    'sharp': {
        score: 0,
        strings: [
            'sharp',
            '@img/sharp-libvips',
            'libvips',
            'imgsharp-libvips',
            'High performance Node.js image processing',
            'resize JPEG, PNG, WebP',
            '<EMAIL>',
            'sharp.pixelplumbing.com'
        ],
        exports: [
            'sharp',
            'format',
            'resize',
            'crop',
            'rotate',
            'flip',
            'flop',
            'blur',
            'sharpen',
            'threshold',
            'negate',
            'normalise'
        ]
    },
    'sharp-image': {
        score: 0,
        strings: [
            'sharp',
            'toFile',
            'toBuffer',
            'keepExif',
            'withExif',
            'withExifMerge',
            'keepIccProfile',
            'withIccProfile',
            'keepMetadata',
            'withMetadata',
            'toFormat',
            'jpeg',
            'jp2',
            'png',
            'webp',
            'tiff',
            'avif',
            'heif',
            'jxl',
            'gif',
            'raw',
            'tile',
            'timeout',
            'resize',
            'crop',
            'rotate',
            'flip',
            'flop',
            'blur',
            'sharpen',
            'gamma',
            'negate',
            'normalise',
            'convolve',
            'threshold',
            'boolean',
            'linear',
            'recomb',
            'modulate'
        ],
        exports: [
            'toFile',
            'toBuffer',
            'keepExif',
            'withExif',
            'toFormat',
            'jpeg',
            'png',
            'webp',
            'tiff',
            'avif',
            'heif',
            'resize',
            'crop',
            'rotate',
            'blur',
            'sharpen'
        ]
    },
    'shell-quote': {
        score: 0,
        strings: [
            "'",
            '"',
            'op',
            'glob',
            'pattern',
            'Bad substitution: ',
            /([A-Za-z]:)?([#!"$&'()*,:;<=>?@[\\\]^`{|}])/g,
            'quote',
            'parse',
            'shell-quote',
            'shellQuote',
            'shell参数解析',
            'escape',
            'comment'
        ],
        exports: [ 'quote', 'parse' ]
    },
    'signal-exit': {
        score: 0,
        strings: [
            'signal-exit',
            'SIGABRT',
            'SIGALRM',
            'SIGHUP',
            'SIGINT',
            'SIGTERM',
            'SIGVTALRM',
            'SIGXCPU',
            'SIGXFSZ',
            'SIGUSR2',
            'SIGTRAP',
            'SIGSYS',
            'SIGQUIT',
            'SIGIOT',
            'SIGIO',
            'SIGPOLL',
            'SIGPWR',
            'SIGSTKFLT',
            'removeListener',
            'emit',
            'reallyExit',
            'listeners',
            'kill',
            'pid'
        ],
        exports: [ 'onExit', 'load', 'unload', 'signals' ]
    },
    'sinon': {
        score: 0,
        strings: [
            'spy',
            'stub',
            'mock',
            'fake',
            'sandbox',
            'createSandbox',
            'restore',
            'reset',
            'resetHistory',
            'resetBehavior',
            'calledWith',
            'calledOnce',
            'calledTwice',
            'calledThrice',
            'notCalled',
            'returned'
        ],
        exports: [ 'spy', 'stub', 'mock', 'fake', 'sandbox', 'createSandbox' ]
    },
    'smithy-client': {
        score: 0,
        strings: [
            'smithy',
            'client',
            'middleware',
            'hostHeaderMiddleware',
            'loggerMiddleware',
            'recursionDetectionMiddleware',
            'deserializerMiddleware',
            'serializerMiddleware',
            'getHostHeaderPlugin',
            'getLoggerPlugin',
            'getRecursionDetectionPlugin',
            'getSerdePlugin',
            'normalizeProvider',
            'getSmithyContext'
        ],
        exports: [
            'hostHeaderMiddleware',
            'loggerMiddleware',
            'recursionDetectionMiddleware',
            'deserializerMiddleware',
            'serializerMiddleware',
            'getSerdePlugin'
        ]
    },
    'smithy-protocols': {
        score: 0,
        strings: [
            'smithy',
            'protocols',
            'RpcProtocol',
            'HttpBindingProtocol',
            'SerdeContextConfig',
            'HttpInterceptingShapeSerializer',
            'HttpInterceptingShapeDeserializer',
            'serializeRequest',
            'deserializeResponse',
            'handleError',
            'getPayloadCodec',
            'setSerdeContext',
            'createSerializer',
            'createDeserializer'
        ],
        exports: [
            'RpcProtocol',
            'HttpBindingProtocol',
            'SerdeContextConfig',
            'HttpInterceptingShapeSerializer',
            'HttpInterceptingShapeDeserializer'
        ]
    },
    'socket.io': {
        score: 0,
        strings: [
            'Server',
            'Socket',
            'emit',
            'on',
            'once',
            'off',
            'join',
            'leave',
            'to',
            'in',
            'broadcast',
            'volatile',
            'compress',
            'binary',
            'namespace',
            'adapter',
            'engine',
            'handshake',
            'rooms'
        ],
        exports: [ 'Server' ]
    },
    'socket.io-client': {
        score: 0,
        strings: [
            'io',
            'connect',
            'Manager',
            'Socket',
            'emit',
            'on',
            'once',
            'off',
            'disconnect',
            'reconnect',
            'forceNew',
            'multiplex',
            'timeout'
        ],
        exports: [ 'io', 'connect', 'Manager' ]
    },
    'spawn-rx': {
        score: 0,
        strings: [
            'spawn-rx',
            'findActualExecutable',
            'spawnDetached',
            'spawn',
            'spawnDetachedPromise',
            'spawnPromise',
            'child_process',
            'path',
            'net',
            'fs',
            'Observable',
            'AsyncSubject',
            'Subscription',
            'PowerShell.exe',
            'cmd.exe',
            'Jobber.exe'
        ],
        exports: [
            'findActualExecutable',
            'spawnDetached',
            'spawn',
            'spawnDetachedPromise',
            'spawnPromise'
        ]
    },
    'sqlite3': {
        score: 0,
        strings: [
            'Database',
            'Statement',
            'verbose',
            'cached',
            'OPEN_READONLY',
            'OPEN_READWRITE',
            'OPEN_CREATE',
            'OPEN_FULLMUTEX',
            'OPEN_SHAREDCACHE',
            'OPEN_PRIVATECACHE',
            'OPEN_URI',
            'OPEN_MEMORY'
        ],
        exports: [ 'Database', 'Statement', 'verbose', 'cached' ]
    },
    'statsig': {
        score: 0,
        strings: [
            'statsig',
            'Statsig',
            'configExposure',
            'gateExposure',
            'layerExposure',
            'configVersion',
            'ruleID',
            'rulePassed',
            'EventLogger',
            'StatsigMetadataProvider',
            'SDK_VERSION',
            '3.12.1',
            'js-mono',
            'StableID',
            'getUUID',
            'SessionID',
            'StatsigSession',
            'ErrorBoundary',
            'NetworkCore',
            'DataAdapterCore',
            'NetworkFallbackResolver',
            'SDKFlags'
        ],
        exports: [
            'initialize',
            'getFeatureGate',
            'getDynamicConfig',
            'getExperiment',
            'getLayer',
            'logEvent',
            'shutdown',
            'EventLogger',
            'StatsigMetadataProvider',
            'StableID',
            'SessionID',
            'StatsigSession',
            'ErrorBoundary',
            'NetworkCore',
            'DataAdapterCore'
        ]
    },
    'statsig-node': {
        score: 0,
        strings: [
            'statsig',
            'initialize',
            'checkGate',
            'getConfig',
            'getExperiment',
            'logEvent',
            'shutdown',
            'getFeatureGate',
            'getDynamicConfig',
            'getLayer',
            'updateUser',
            'overrideGate',
            'overrideConfig'
        ],
        exports: [
            'initialize',
            'checkGate',
            'getConfig',
            'getExperiment',
            'logEvent',
            'shutdown',
            'getFeatureGate',
            'getDynamicConfig'
        ]
    },
    'stream': {
        score: 0,
        strings: [ 'Readable', 'Writable', 'Duplex', 'Transform', 'PassThrough', 'pipeline', 'finished' ],
        exports: [ 'Readable', 'Writable', 'Duplex', 'Transform', 'PassThrough', 'pipeline', 'finished' ]
    },
    'string_decoder': {
        score: 0,
        strings: [ 'StringDecoder', 'write', 'end' ],
        exports: [ 'StringDecoder' ]
    },
    'supertest': {
        score: 0,
        strings: [
            'request',
            'agent',
            'get',
            'post',
            'put',
            'delete',
            'patch',
            'head',
            'options',
            'send',
            'query',
            'field',
            'attach',
            'set',
            'expect',
            'end',
            'timeout',
            'redirects',
            'type',
            'accept'
        ],
        exports: [ 'request', 'agent' ]
    },
    'syntax-highlighting': {
        score: 0,
        strings: [
            'syntax',
            'highlighting',
            'lexer',
            'parser',
            'tokenizer',
            'keywords',
            'built_in',
            'literal',
            'meta',
            'string',
            'number',
            'comment',
            'variable',
            'function',
            'class',
            'title',
            'params',
            'symbol',
            'attribute',
            'subst',
            'regexp',
            'escape'
        ],
        exports: [ 'tokenize', 'parse', 'highlight', 'format' ]
    },
    'text-formats': {
        score: 0,
        strings: [
            'text/html',
            'text/css',
            'text/javascript',
            'text/plain',
            'text/csv',
            'text/xml',
            'text/markdown',
            'text/yaml',
            'text/json',
            'text/rtf'
        ],
        exports: [ 'parseText', 'getTextFormat', 'convertText' ]
    },
    'timers': {
        score: 0,
        strings: [
            'setTimeout',
            'clearTimeout',
            'setInterval',
            'clearInterval',
            'setImmediate',
            'clearImmediate'
        ],
        exports: [
            'setTimeout',
            'clearTimeout',
            'setInterval',
            'clearInterval',
            'setImmediate',
            'clearImmediate'
        ]
    },
    'timestamp-formats': {
        score: 0,
        strings: [
            'TIMESTAMP_DEFAULT',
            'TIMESTAMP_DATE_TIME',
            'TIMESTAMP_HTTP_DATE',
            'TIMESTAMP_EPOCH_SECONDS',
            'timestampFormat',
            'useTrait',
            'default',
            'parseRfc3339DateTimeWithOffset',
            'parseRfc7231DateTime',
            'parseEpochTimestamp',
            'dateToUtcString',
            'toISOString',
            'replace',
            'getTime'
        ],
        exports: [
            'parseRfc3339DateTimeWithOffset',
            'parseRfc7231DateTime',
            'parseEpochTimestamp',
            'dateToUtcString'
        ]
    },
    'tls': {
        score: 0,
        strings: [ 'createServer', 'createSecureContext', 'connect', 'TLSSocket' ],
        exports: [ 'createServer', 'createSecureContext', 'connect', 'TLSSocket' ]
    },
    'tty': {
        score: 0,
        strings: [ 'isatty', 'ReadStream', 'WriteStream' ],
        exports: [ 'isatty', 'ReadStream', 'WriteStream' ]
    },
    'turndown': {
        score: 0,
        strings: [ 'turndown', 'blockquote', 'HTMLQuoteElement', 'filter', 'replacement', 'addRule', 'keep', 'remove' ],
        exports: [ 'turndown', 'addRule', 'keep', 'remove', 'escape', 'use' ]
    },
    'typeorm': {
        score: 0,
        strings: [
            'createConnection',
            'getConnection',
            'getConnectionManager',
            'getRepository',
            'getTreeRepository',
            'getMongoRepository',
            'getCustomRepository',
            'Entity',
            'Column',
            'PrimaryGeneratedColumn',
            'PrimaryColumn',
            'CreateDateColumn',
            'UpdateDateColumn',
            'VersionColumn',
            'Generated',
            'Index',
            'Unique',
            'Check',
            'Exclusion',
            'ManyToOne',
            'OneToMany',
            'OneToOne',
            'ManyToMany',
            'JoinColumn',
            'JoinTable',
            'RelationId'
        ],
        exports: [ 'createConnection', 'getConnection', 'getRepository' ]
    },
    'typescript': {
        score: 0,
        strings: [
            'TypeScript',
            'typescript',
            'ts',
            'tsx',
            '~5.8.3',
            '~5.2.2',
            '^5.3.3',
            'tsc',
            'tsconfig',
            'ts-node',
            'ts-loader',
            '@typescript-eslint'
        ],
        exports: [
            'transpile',
            'createProgram',
            'createCompilerHost',
            'createSourceFile',
            'ScriptTarget',
            'ModuleKind',
            'CompilerOptions'
        ]
    },
    'undici': {
        score: 0,
        strings: [
            'undici',
            'undici:request:create',
            'undici:request:headers',
            'undici:request:error',
            'undici:request:end',
            'nativeNodeFetchintegration',
            'UndiciError',
            'ConnectTimeoutError',
            'HeadersTimeoutError',
            'BodyTimeoutError',
            'ResponseStatusCodeError',
            'InvalidArgumentError',
            'RequestAbortedError',
            'ClientDestroyedError',
            'ClientClosedError',
            'SocketError',
            'NotSupportedError',
            'HTTPParserError',
            'ResponseExceededMaxSizeError',
            'RequestRetryError',
            'kDestroy',
            'kDispatch',
            'kConnect',
            'kQueue',
            'kSocket',
            'kClient'
        ],
        exports: [
            'request',
            'stream',
            'pipeline',
            'connect',
            'upgrade',
            'Client',
            'Pool',
            'Agent',
            'Dispatcher',
            'UndiciError',
            'ConnectTimeoutError',
            'HeadersTimeoutError',
            'BodyTimeoutError',
            'ResponseStatusCodeError',
            'InvalidArgumentError'
        ]
    },
    'url': {
        score: 0,
        strings: [
            'parse',
            'format',
            'resolve',
            'URL',
            'URLSearchParams',
            'protocol',
            'hostname',
            'port',
            'pathname',
            'search',
            'hash'
        ],
        exports: [ 'parse', 'format', 'resolve', 'URL', 'URLSearchParams' ]
    },
    'util': {
        score: 0,
        strings: [
            'isArray',
            'isBoolean',
            'isNull',
            'isNullOrUndefined',
            'isNumber',
            'isString',
            'isSymbol',
            'isUndefined',
            'isRegExp',
            'isObject',
            'isDate',
            'isError',
            'isFunction',
            'isPrimitive',
            'isBuffer',
            'util',
            'promisify',
            'format',
            'readFile',
            'readdir',
            'execFile',
            'sw_vers',
            'ProductName',
            'ProductVersion'
        ],
        exports: [
            'isArray',
            'isBoolean',
            'isNull',
            'isNullOrUndefined',
            'isNumber',
            'isString',
            'isSymbol',
            'isUndefined',
            'isRegExp',
            'isObject',
            'isDate',
            'isError',
            'isFunction',
            'isPrimitive',
            'isBuffer',
            'promisify',
            'format',
            'inspect',
            'deprecate'
        ]
    },
    'util-base64': {
        score: 0,
        strings: [
            'fromBase64',
            'toBase64',
            'base64',
            'fromArrayBuffer',
            'fromString',
            'fromUtf8',
            'toUint8Array',
            'toUtf8',
            'ArrayBuffer',
            'Uint8Array',
            'Buffer',
            'utf8',
            'utf-8'
        ],
        exports: [
            'fromBase64',
            'toBase64',
            'fromArrayBuffer',
            'fromString',
            'fromUtf8',
            'toUint8Array',
            'toUtf8'
        ]
    },
    'util-stream': {
        score: 0,
        strings: [
            'ChecksumStream',
            'isBlob',
            'isReadableStream',
            'stream',
            'Duplex',
            'expectedChecksum',
            'checksum',
            'source',
            'checksumSourceLocation',
            'base64Encoder',
            'pipe',
            '_read',
            '_write',
            '_final'
        ],
        exports: [ 'ChecksumStream', 'isBlob', 'isReadableStream' ]
    },
    'uuid': {
        score: 0,
        strings: [
            'v1',
            'v3',
            'v4',
            'v5',
            'uuid',
            'validate',
            'version',
            'parse',
            'stringify',
            'uuid4',
            'randomUUID',
            'getUUID',
            'crypto.randomUUID',
            'traceId',
            'spanId',
            'sessionID',
            'event_id',
            'checkInId',
            'UUID format',
            'valid UUID',
            'session ID',
            'parentUuid'
        ],
        exports: [ 'v1', 'v3', 'v4', 'v5', 'validate', 'version', 'parse', 'stringify', 'NIL' ]
    },
    'v8': {
        score: 0,
        strings: [ 'getHeapStatistics', 'getHeapSpaceStatistics', 'serialize', 'deserialize' ],
        exports: [ 'getHeapStatistics', 'getHeapSpaceStatistics', 'serialize', 'deserialize' ]
    },
    'video-formats': {
        score: 0,
        strings: [
            'video/mp4',
            'video/mpeg',
            'video/webm',
            'video/ogg',
            'video/quicktime',
            'video/3gpp',
            'video/h264',
            'video/h265',
            'video/av1',
            'video/vp8',
            'video/vp9'
        ],
        exports: [ 'parseVideo', 'getVideoFormat', 'convertVideo' ]
    },
    'vite': {
        score: 0,
        strings: [
            'createServer',
            'build',
            'preview',
            'defineConfig',
            'loadEnv',
            'normalizePath',
            'mergeConfig',
            'searchForWorkspaceRoot'
        ],
        exports: [ 'createServer', 'build', 'preview', 'defineConfig' ]
    },
    'vm': {
        score: 0,
        strings: [ 'runInThisContext', 'runInNewContext', 'createContext', 'Script' ],
        exports: [ 'runInThisContext', 'runInNewContext', 'createContext', 'Script' ]
    },
    'vue': {
        score: 0,
        strings: [ 'Vue', '__isVue', '_isVue', 'isVueViewModel', '[VueViewModel]' ],
        exports: [
            'createApp',
            'ref',
            'reactive',
            'computed',
            'watch',
            'watchEffect',
            'onMounted',
            'onUnmounted',
            'defineComponent',
            'nextTick'
        ]
    },
    'waterline': {
        score: 0,
        strings: [
            'Waterline',
            'Collection',
            'Model',
            'find',
            'findOne',
            'create',
            'update',
            'destroy',
            'count',
            'sum',
            'average',
            'populate',
            'attributes',
            'associations',
            'validations',
            'callbacks'
        ],
        exports: [ 'Waterline' ]
    },
    'web-vitals': {
        score: 0,
        strings: [
            'web-vitals',
            'CLS',
            'FID',
            'INP',
            'LCP',
            'TTFB',
            'PerformanceObserver',
            'layout-shift',
            'first-input',
            'navigation',
            'visibilitychange',
            'pagehide'
        ],
        exports: [ 'onCLS', 'onFID', 'onINP', 'onLCP', 'onTTFB' ]
    },
    'webpack': {
        score: 0,
        strings: [
            'webpack',
            'Compiler',
            'Compilation',
            'DefinePlugin',
            'HotModuleReplacementPlugin',
            'ProgressPlugin',
            'ProvidePlugin',
            'IgnorePlugin',
            'BannerPlugin',
            'optimize',
            'entry',
            'output',
            'module',
            'resolve',
            'plugins',
            'devtool',
            'target',
            'externals',
            'performance',
            'node',
            'stats',
            'webpack-cli',
            'webpack.config',
            '__webpack_require__',
            'webpack:/?',
            'app:///'
        ],
        exports: [ 'webpack', 'DefinePlugin', 'HotModuleReplacementPlugin', 'optimize', 'ProvidePlugin' ]
    },
    'websocket-driver': {
        score: 0,
        strings: [
            'websocket-driver',
            'Hybi',
            'Draft75',
            'Draft76',
            'handshake',
            'frame',
            'mask',
            'unmask',
            'ping',
            'pong',
            'close',
            'text',
            'binary'
        ],
        exports: [ 'http', 'client', 'server' ]
    },
    'websocket-extensions': {
        score: 0,
        strings: [
            'permessage-deflate',
            'x-webkit-deflate-frame',
            'deflate-frame',
            'extensionName',
            'offer',
            'accept',
            'compress',
            'decompress',
            'server_no_context_takeover',
            'client_no_context_takeover'
        ],
        exports: [ 'PerMessageDeflate', 'Extensions' ]
    },
    'websocket-stream': {
        score: 0,
        strings: [
            'websocket-stream',
            'createWebSocketStream',
            'Duplex',
            'stream',
            'objectMode',
            'allowHalfOpen',
            'readable',
            'writable'
        ],
        exports: [ 'createWebSocketStream', 'WebSocketStream' ]
    },
    'whatwg-url': {
        score: 0,
        strings: [
            'URL',
            'URLSearchParams',
            'parseURL',
            'basicURLParse',
            'serializeURL',
            'serializePath',
            'serializeHost',
            'serializeInteger',
            'serializeURLOrigin',
            'setTheUsername',
            'setThePassword',
            'cannotHaveAUsernamePasswordPort',
            'hasAnOpaquePath',
            'percentDecodeString',
            'percentDecodeBytes',
            'href',
            'origin',
            'protocol',
            'username',
            'password',
            'host',
            'hostname',
            'port',
            'pathname',
            'search',
            'searchParams',
            'hash',
            'toJSON',
            'toString',
            'parse',
            'canParse'
        ],
        exports: [
            'URL',
            'URLSearchParams',
            'parseURL',
            'basicURLParse',
            'serializeURL',
            'serializePath',
            'serializeHost',
            'serializeInteger',
            'serializeURLOrigin',
            'setTheUsername',
            'setThePassword'
        ]
    },
    'which-command': {
        score: 0,
        strings: [
            'which',
            'sync',
            'isexe',
            'pathExt',
            'PATHEXT',
            'pathEnv',
            'PATH',
            'ENOENT',
            'nothrow',
            'all',
            'colon',
            'delimiter',
            'win32',
            'cygwin',
            'msys',
            'statSync',
            'isFile',
            'isSymbolicLink',
            'getuid',
            'getgid',
            'mode',
            'uid',
            'gid'
        ],
        exports: [ 'which', 'sync', 'isexe', 'pathExt', 'pathEnv', 'ENOENT', 'nothrow', 'all' ]
    },
    'worker_threads': {
        score: 0,
        strings: [ 'Worker', 'isMainThread', 'parentPort', 'workerData', 'threadId' ],
        exports: [ 'Worker', 'isMainThread', 'parentPort', 'workerData', 'threadId' ]
    },
    'wrap-ansi': {
        score: 0,
        strings: [
            'wrap',
            'hard',
            'wordWrap',
            'trim',
            'truncate',
            'truncate-middle',
            'truncate-start',
            'truncate-end',
            'flexWrap',
            'nowrap',
            'flexFlow'
        ],
        exports: [ 'wrap' ]
    },
    'ws': {
        score: 0,
        strings: [
            'WebSocket',
            'Sec-WebSocket-Key',
            'Sec-WebSocket-Accept',
            'Sec-WebSocket-Protocol',
            'Sec-WebSocket-Version',
            'websocket',
            'ping',
            'pong',
            'close',
            'open',
            'ws',
            'CONNECTING',
            'OPEN',
            'CLOSING',
            'CLOSED',
            'binaryType',
            'bufferedAmount',
            'extensions',
            'protocol',
            'readyState',
            'send',
            'terminate',
            'addEventListener',
            'removeEventListener',
            'onopen',
            'onclose',
            'onerror',
            'onmessage'
        ],
        exports: [ 'WebSocket', 'WebSocketServer', 'createWebSocketStream', 'Server', 'Receiver', 'Sender' ]
    },
    'xml-utils': {
        score: 0,
        strings: [
            'isExist',
            'isEmptyObject',
            'merge',
            'getValue',
            'isName',
            'getAllMatches',
            'nameRegexp',
            'InvalidTag',
            'InvalidAttr',
            'InvalidXml',
            'InvalidChar',
            'DOCTYPE',
            'CDATA',
            'entity',
            'regex',
            'val',
            'buildOptions',
            'defaultOptions'
        ],
        exports: [ 'isExist', 'isEmptyObject', 'merge', 'getValue', 'isName', 'getAllMatches' ]
    },
    'xmldom': {
        score: 0,
        strings: [
            'DOMParser',
            'XMLSerializer',
            'Document',
            'Element',
            'Node',
            'normalize',
            'normalizeDocument',
            'parseFromString',
            'xmldom',
            'NodeList',
            'documentElement',
            'createDocument'
        ],
        exports: [ 'DOMParser', 'XMLSerializer', 'DOMImplementation' ]
    },
    'xss': {
        score: 0,
        strings: [
            'filterXSS',
            'FilterXSS',
            'whiteList',
            'allowList',
            'onTag',
            'onIgnoreTag',
            'onTagAttr',
            'onIgnoreTagAttr',
            'safeAttrValue',
            'escapeHtml',
            'escapeQuote',
            'unescapeQuote',
            'escapeHtmlEntities',
            'escapeDangerHtml5Entities',
            'clearNonPrintableCharacter',
            'friendlyAttrValue',
            'escapeAttrValue',
            'onIgnoreTagStripAll',
            'StripTagBody',
            'stripCommentTag',
            'stripBlankChar',
            'attributeWrapSign',
            'cssFilter'
        ],
        exports: [
            'filterXSS',
            'FilterXSS',
            'whiteList',
            'getDefaultWhiteList',
            'onTag',
            'onIgnoreTag',
            'onTagAttr',
            'onIgnoreTagAttr',
            'safeAttrValue',
            'escapeHtml'
        ]
    },
    'yallist': {
        score: 0,
        strings: [
            'yallist',
            'Yallist',
            'push',
            'pop',
            'shift',
            'unshift',
            'slice',
            'splice',
            'reverse',
            'map',
            'reduce',
            'forEach',
            'toArray',
            'head',
            'tail',
            'length',
            'next',
            'prev'
        ],
        exports: [ 'Yallist', 'Node' ]
    },
    'yargs': {
        score: 0,
        strings: [ 'yargs', 'argv', 'command', 'option', 'positional', 'demandOption', 'choices', 'default', 'describe' ],
        exports: [ 'yargs', 'argv', 'command', 'option', 'positional', 'demandOption', 'choices', 'default', 'describe' ]
    },
    'zlib': {
        score: 0,
        strings: [
            'gzip',
            'gunzip',
            'deflate',
            'inflate',
            'compress',
            'uncompress',
            'createGzip',
            'createGunzip',
            'createDeflate',
            'createInflate'
        ],
        exports: [
            'gzip',
            'gunzip',
            'deflate',
            'inflate',
            'compress',
            'uncompress',
            'createGzip',
            'createGunzip',
            'createDeflate',
            'createInflate'
        ]
    },
    'zod': {
        score: 0,
        strings: [
            'z',
            'string',
            'number',
            'boolean',
            'date',
            'array',
            'object',
            'union',
            'intersection',
            'tuple',
            'record',
            'map',
            'set',
            'function',
            'lazy',
            'literal',
            'enum',
            'nativeEnum',
            'nullable',
            'optional',
            'parse',
            'safeParse',
            'parseAsync',
            'safeParseAsync'
        ],
        exports: [ 'z' ]
    }
};

module.exports = NPM_FINGERPRINTS;