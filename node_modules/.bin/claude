#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/code/ai/claude-code/node_modules/.pnpm/@anthropic-ai+claude-code@1.0.61/node_modules/@anthropic-ai/claude-code/node_modules:/Users/<USER>/code/ai/claude-code/node_modules/.pnpm/@anthropic-ai+claude-code@1.0.61/node_modules/@anthropic-ai/node_modules:/Users/<USER>/code/ai/claude-code/node_modules/.pnpm/@anthropic-ai+claude-code@1.0.61/node_modules:/Users/<USER>/code/ai/claude-code/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/code/ai/claude-code/node_modules/.pnpm/@anthropic-ai+claude-code@1.0.61/node_modules/@anthropic-ai/claude-code/node_modules:/Users/<USER>/code/ai/claude-code/node_modules/.pnpm/@anthropic-ai+claude-code@1.0.61/node_modules/@anthropic-ai/node_modules:/Users/<USER>/code/ai/claude-code/node_modules/.pnpm/@anthropic-ai+claude-code@1.0.61/node_modules:/Users/<USER>/code/ai/claude-code/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/@anthropic-ai+claude-code@1.0.61/node_modules/@anthropic-ai/claude-code/cli.js" "$@"
else
  exec node  "$basedir/../.pnpm/@anthropic-ai+claude-code@1.0.61/node_modules/@anthropic-ai/claude-code/cli.js" "$@"
fi
