# 模块依赖关系映射 (Module Dependency Mapping)

**最后更新**: 2025-01-10
**状态**: 初始分析阶段

## 依赖关系图

### 主文件依赖结构

```
main.cleaned.from.nr1.js
├── 外部分离模块 (.isolated.js)
│   ├── ./nr1.isolated.js
│   ├── ./iIA.isolated.js  
│   ├── ./g.isolated.js
│   ├── ./BT0.isolated.js
│   ├── ./EH0.isolated.js
│   ├── ./eK0.isolated.js
│   ├── ./xPB.isolated.js (图像处理)
│   ├── ./K48.isolated.js
│   ├── ./DC1.isolated.js
│   ├── ./xk0.isolated.js
│   ├── ./W3.isolated.js
│   └── ./x5.isolated.js
├── 聚合模块 (_more.js)
│   ├── ./$K_$X2_A6_AU2_AZ0_more.js (96个导出)
│   ├── ./FP_Jo_M51_QL2__G0_more.js (11个导出)
│   ├── ./$h2_$v2_AS2_Ai2_Aj2_more.js (228个导出)
│   └── ./$I_$X0_A71_AX0_BM_more.js (108个导出)
├── 单一功能模块
│   ├── ./W91.js
│   ├── ./DIA_MN_rFA.js
│   ├── ./WL2_ZL2.js
│   ├── ./XL2.js
│   ├── ./yM2.js
│   └── [其他30+个单一模块]
└── 第三方库 (内嵌)
    ├── axios (HTTP客户端)
    ├── lodash (工具函数库)
    ├── sharp (图像处理)
    ├── React相关库
    └── Node.js内置模块
```

## 重构后目录结构规划

```
src/
├── index.js                 # 主入口文件
├── config/                  # 配置文件
│   ├── constants.js         # 常量定义
│   └── environment.js       # 环境配置
├── utils/                   # 工具函数
│   ├── string.js           # 字符串处理工具
│   ├── file.js             # 文件操作工具
│   ├── validation.js       # 数据验证工具
│   └── helpers.js          # 通用辅助函数
├── services/                # 服务层
│   ├── http.js             # HTTP请求服务
│   ├── auth.js             # 认证服务
│   └── api.js              # API接口封装
├── core/                    # 核心业务逻辑
│   ├── cli.js              # 命令行处理
│   ├── session.js          # 会话管理
│   └── processor.js        # 核心处理逻辑
├── components/              # UI组件 (如果有React组件)
│   ├── common/             # 通用组件
│   └── specific/           # 特定功能组件
└── lib/                     # 第三方库适配器
    └── adapters.js         # 库适配器
```

## 模块重构映射表

### 第三方库替换映射

| 原始代码特征 | 目标导入语句 | 重构状态 |
|-------------|-------------|----------|
| axios相关代码 | `import axios from 'axios';` | 待处理 |
| lodash工具函数 | `import _ from 'lodash';` | 待处理 |
| sharp图像处理 | `import sharp from 'sharp';` | 待处理 |
| React组件 | `import React from 'react';` | 待处理 |
| Node.js模块 | 保持现有ES6导入 | 已识别 |

### 核心模块映射 (待完善)

| 原始模块 | 重构后位置 | 功能描述 | 状态 |
|---------|-----------|---------|------|
| `nr1` | `src/core/` | 核心模块1 | 待分析 |
| `g` 相关函数 | `src/utils/` | 工具函数集合 | 待分析 |
| `xPB` | `src/services/image.js` | 图像处理服务 | 待分析 |
| CLI相关代码 | `src/core/cli.js` | 命令行处理 | 待分析 |

## 变量重命名映射表

### 混淆变量重命名 (待完善)

| 原始名称 | 重构后名称 | 类型 | 功能描述 | 状态 |
|---------|-----------|------|---------|------|
| `nr1` | `coreModule` | 模块 | 核心功能模块 | 待确认 |
| `Tr1` | `transformer` | 函数 | 数据转换器 | 待确认 |
| `iIA` | `imageAdapter` | 模块 | 图像适配器 | 待确认 |
| `xPB` | `sharpProcessor` | 函数 | Sharp图像处理器 | 待确认 |
| `xcB` | `createRequire` | 函数 | 模块加载器 | 已确认 |

### 函数重命名映射 (待完善)

| 原始函数签名 | 重构后函数签名 | 功能描述 | 状态 |
|-------------|---------------|---------|------|
| `E(A, B)` | `createModuleWrapper(moduleFactory, exports)` | 模块包装器 | 待确认 |
| `Mj(A, B)` | `defineModuleExports(target, exports)` | 导出定义器 | 待确认 |
| `gA1(A, B)` | `createLazyLoader(factory, cache)` | 懒加载器 | 待确认 |

## 依赖关系分析

### 循环依赖检测
- **状态**: 待分析
- **方法**: 通过静态分析识别模块间的循环引用

### 模块耦合度分析
- **高耦合模块**: 待识别
- **独立模块**: 待识别
- **工具模块**: 待识别

## 重构优先级

### 第一阶段 (高优先级)
1. 第三方库分离和替换
2. 核心业务逻辑模块识别
3. 工具函数模块重构

### 第二阶段 (中优先级)
1. UI组件模块重构
2. 服务层模块重构
3. 配置模块重构

### 第三阶段 (低优先级)
1. 性能优化
2. 代码风格统一
3. 文档完善

## 注意事项

1. **保持功能等价性**: 每个重构步骤都要确保功能不变
2. **渐进式重构**: 避免一次性大规模改动
3. **测试验证**: 每个模块重构后都要进行功能测试
4. **文档同步**: 及时更新重构映射和依赖关系
