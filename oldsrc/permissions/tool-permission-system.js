/**
 * Tool Permission System - Complete user confirmation and permission management
 * 
 * Refactored version of the tool permission confirmation system from the original code.
 * This handles the complete flow from tool permission checking to user confirmation
 * dialogs and permission rule management.
 * 
 * Original functions:
 * - eBB() at line 346720 - Main permission confirmation component
 * - gBB() at line 345514 - Permission confirmation options generator
 * - rL1() at line 345465 - Permission request tracker
 * - wG4() at line 346714 - Tool-specific confirmation component selector
 * - Various tool-specific confirmation components (vBB, mBB, oL1, etc.)
 */

const React = require('react');
const { useState, useEffect, useMemo } = React;
const { useInput } = require('ink');
const { Box, Text, Newline } = require('ink');
const { trackEvent } = require('../utils/analytics');
const { formatToolName, formatRuleSuggestions } = require('../utils/formatting');
const { getCurrentWorkingDirectory } = require('../utils/file-utils');
const { createPermissionRule, applyPermissionRule } = require('./permission-rules');

/**
 * Main Tool Permission Confirmation Component
 * 
 * This is the main component that handles tool permission confirmation.
 * It displays the permission request and routes to tool-specific confirmation
 * components based on the tool type.
 * 
 * Original function: eBB() at line 346720
 * 
 * @param {Object} props - Component props
 * @param {Object} props.toolUseConfirm - Tool use confirmation data
 * @param {Object} props.toolUseContext - Tool use context
 * @param {Function} props.onDone - Completion callback
 * @param {Function} props.onReject - Rejection callback
 * @param {boolean} props.verbose - Verbose mode flag
 * @param {Function} props.setToolPermissionContext - Permission context setter
 */
function ToolPermissionConfirmation({
    toolUseConfirm,
    toolUseContext,
    onDone,
    onReject,
    verbose,
    setToolPermissionContext
}) {
    // Handle Ctrl+C to cancel
    useInput((input, key) => {
        if (key.ctrl && input === 'c') {
            onDone();
            onReject();
            toolUseConfirm.onReject();
        }
    });
    
    // Get tool display name
    const toolDisplayName = toolUseConfirm.tool.userFacingName(toolUseConfirm.input);
    
    // Set window title
    useEffect(() => {
        setWindowTitle(`Claude needs your permission to use ${toolDisplayName}`);
    }, [toolDisplayName]);
    
    // Select appropriate confirmation component based on tool type
    const ConfirmationComponent = selectConfirmationComponent(toolUseConfirm.tool);
    
    return React.createElement(ConfirmationComponent, {
        toolUseContext,
        toolUseConfirm,
        onDone,
        onReject,
        verbose,
        setToolPermissionContext
    });
}

/**
 * Select Tool-Specific Confirmation Component
 * 
 * Routes to the appropriate confirmation component based on tool type.
 * Each tool type has its own specialized confirmation interface.
 * 
 * Original function: wG4() at line 346714
 * 
 * @param {Object} tool - Tool definition
 * @returns {Function} React component for tool confirmation
 */
function selectConfirmationComponent(tool) {
    switch (tool.name) {
        case "str_replace_editor":
            return StringReplaceConfirmation; // vBB
        case "bash":
            return BashConfirmation; // mBB
        case "create_file":
            return CreateFileConfirmation; // iBB
        case "edit_and_apply":
            return EditAndApplyConfirmation; // oBB
        case "apply_diff":
            return ApplyDiffConfirmation; // tBB
        case "web_search":
            return WebSearchConfirmation; // aBB
        case "jupyter":
            return JupyterConfirmation; // rBB
        default:
            return GenericToolConfirmation; // oL1
    }
}

/**
 * Generate Permission Confirmation Options
 * 
 * Creates the list of options available to the user when confirming
 * tool permissions, including rule-based "don't ask again" options.
 * 
 * Original function: gBB() at line 345514
 * 
 * @param {Object} toolUseConfirm - Tool use confirmation data
 * @returns {Array} Array of confirmation options
 */
function generateConfirmationOptions(toolUseConfirm) {
    const { permissionResult } = toolUseConfirm;
    const options = [];
    
    // Check for rule suggestions
    const ruleSuggestions = permissionResult.behavior !== "allow" ? 
        permissionResult.ruleSuggestions : undefined;
    
    if (ruleSuggestions && ruleSuggestions.length > 0) {
        const ruleContent = extractRuleContent(ruleSuggestions);
        const formattedRules = formatRuleSuggestions(ruleContent);
        
        options.push({
            label: `Yes, and don't ask again for ${formattedRules} commands in ${formatWorkingDirectory()}`,
            value: "yes-dont-ask-again-prefix"
        });
    }
    
    return [
        {
            label: "Yes",
            value: "yes"
        },
        ...options,
        {
            label: `No, and tell Claude what to do differently (esc)`,
            value: "no"
        }
    ];
}

/**
 * Track Permission Request
 * 
 * Tracks analytics events when permission requests are shown to users.
 * 
 * Original function: rL1() at line 345465
 * 
 * @param {Object} assistantMessage - Assistant message containing tool use
 * @param {Object} completionData - Completion tracking data
 */
function trackPermissionRequest(assistantMessage, completionData) {
    useEffect(() => {
        trackEvent("tengu_tool_use_show_permission_request", {
            messageID: assistantMessage.message.id,
            toolName: assistantMessage.tool.name,
            isMcp: assistantMessage.tool.isMcp ?? false
        });
        
        // Track completion data
        Promise.resolve(completionData.language_name).then((languageName) => {
            trackEvent("completion_analytics", {
                completion_type: completionData.completion_type,
                event: "response",
                metadata: {
                    language_name: languageName,
                    message_id: assistantMessage.message.id,
                    platform: process.platform
                }
            });
        });
    }, [assistantMessage, completionData]);
}

/**
 * Generic Tool Confirmation Component
 * 
 * Default confirmation component for tools that don't have specialized interfaces.
 * 
 * Original function: oL1() at line 345724
 */
function GenericToolConfirmation({
    setToolPermissionContext,
    toolUseConfirm,
    onDone,
    onReject,
    verbose
}) {
    const [theme] = useTheme();
    const toolDisplayName = toolUseConfirm.tool.userFacingName(toolUseConfirm.input);
    const toolName = toolDisplayName.endsWith(" (MCP)") ? 
        toolDisplayName.slice(0, -6) : toolDisplayName;
    
    const completionData = useMemo(() => ({
        completion_type: "tool_use_single",
        event: "response",
        metadata: {
            tool_name: toolUseConfirm.tool.name,
            message_id: toolUseConfirm.assistantMessage.message.id
        }
    }), [toolUseConfirm]);
    
    // Track permission request
    trackPermissionRequest(toolUseConfirm.assistantMessage, completionData);
    
    const options = generateConfirmationOptions(toolUseConfirm);
    
    const handleSelection = (value) => {
        switch (value) {
            case "yes":
                handleApproval();
                break;
            case "yes-dont-ask-again-prefix":
                handleApprovalWithRule();
                break;
            case "no":
                handleRejection();
                break;
        }
    };
    
    const handleApproval = () => {
        onDone();
        toolUseConfirm.onApprove();
    };
    
    const handleApprovalWithRule = () => {
        // Create permission rule to avoid future prompts
        const rule = createPermissionRule(toolUseConfirm);
        applyPermissionRule(rule, setToolPermissionContext);
        
        onDone();
        toolUseConfirm.onApprove();
    };
    
    const handleRejection = () => {
        onReject();
        toolUseConfirm.onReject();
    };
    
    return (
        <Box flexDirection="column">
            <Box marginBottom={1}>
                <Text bold color="yellow">
                    🔐 Permission Required
                </Text>
            </Box>
            
            <Box marginBottom={1}>
                <Text>
                    Claude wants to use <Text bold>{toolName}</Text>
                </Text>
            </Box>
            
            {toolUseConfirm.permissionResult.message && (
                <Box marginBottom={1}>
                    <Text color="red">
                        {toolUseConfirm.permissionResult.message}
                    </Text>
                </Box>
            )}
            
            <PermissionOptionsSelector
                options={options}
                onSelect={handleSelection}
                theme={theme}
            />
            
            {verbose && (
                <Box marginTop={1}>
                    <Text dimColor>
                        Tool: {toolUseConfirm.tool.name}
                        <Newline />
                        Input: {JSON.stringify(toolUseConfirm.input, null, 2)}
                    </Text>
                </Box>
            )}
        </Box>
    );
}

/**
 * Bash Tool Confirmation Component
 * 
 * Specialized confirmation for bash command execution.
 * 
 * Original function: mBB() at line 345630
 */
function BashConfirmation({
    setToolPermissionContext,
    toolUseConfirm,
    onDone,
    onReject
}) {
    const [theme] = useTheme();
    const { command, description } = parseBashInput(toolUseConfirm.input);
    const [showDebug, setShowDebug] = useState(false);
    
    const completionData = useMemo(() => ({
        completion_type: "tool_use_single",
        event: "response",
        metadata: {
            tool_name: "bash",
            command_length: command.length,
            has_description: !!description
        }
    }), [command, description]);
    
    // Track permission request
    trackPermissionRequest(toolUseConfirm.assistantMessage, completionData);
    
    // Handle Ctrl+D for debug info
    useInput((input, key) => {
        if (key.ctrl && input === 'd') {
            setShowDebug(!showDebug);
        }
    });
    
    const options = generateConfirmationOptions(toolUseConfirm);
    
    return (
        <Box flexDirection="column">
            <Box marginBottom={1}>
                <Text bold color="yellow">
                    🔐 Bash Command Permission
                </Text>
            </Box>
            
            <Box marginBottom={1}>
                <Text>
                    Claude wants to run this command:
                </Text>
            </Box>
            
            <Box marginBottom={1} paddingLeft={2}>
                <Text backgroundColor="gray" color="white">
                    {command}
                </Text>
            </Box>
            
            {description && (
                <Box marginBottom={1}>
                    <Text dimColor>
                        Purpose: {description}
                    </Text>
                </Box>
            )}
            
            <PermissionOptionsSelector
                options={options}
                onSelect={(value) => handleBashSelection(value, toolUseConfirm, onDone, onReject, setToolPermissionContext)}
                theme={theme}
            />
            
            {showDebug && (
                <Box marginTop={1}>
                    <Text dimColor>
                        Debug Info:
                        <Newline />
                        Working Directory: {getCurrentWorkingDirectory()}
                        <Newline />
                        Command Length: {command.length}
                        <Newline />
                        Has Description: {!!description}
                    </Text>
                </Box>
            )}
            
            <Box marginTop={1}>
                <Text dimColor>
                    Ctrl-D to show debug info
                </Text>
            </Box>
        </Box>
    );
}

/**
 * Permission Options Selector Component
 * 
 * Interactive selector for permission confirmation options.
 */
function PermissionOptionsSelector({ options, onSelect, theme }) {
    const [selectedIndex, setSelectedIndex] = useState(0);
    
    useInput((input, key) => {
        if (key.upArrow) {
            setSelectedIndex(Math.max(0, selectedIndex - 1));
        } else if (key.downArrow) {
            setSelectedIndex(Math.min(options.length - 1, selectedIndex + 1));
        } else if (key.return) {
            onSelect(options[selectedIndex].value);
        } else if (key.escape) {
            onSelect("no");
        }
    });
    
    return (
        <Box flexDirection="column">
            {options.map((option, index) => (
                <Box key={index} marginBottom={index < options.length - 1 ? 1 : 0}>
                    <Text color={index === selectedIndex ? "cyan" : undefined}>
                        {index === selectedIndex ? "→ " : "  "}
                        {option.label}
                    </Text>
                </Box>
            ))}
        </Box>
    );
}

// Utility functions
function extractRuleContent(ruleSuggestions) {
    return ruleSuggestions.flatMap((suggestion) => {
        if (!suggestion.ruleContent) return [];
        return parseRuleContent(suggestion.ruleContent) ?? suggestion.ruleContent;
    });
}

function formatWorkingDirectory() {
    return getCurrentWorkingDirectory().split('/').pop() || 'current directory';
}

function parseBashInput(input) {
    // TODO: Implement bash input parsing
    return {
        command: input.command || "",
        description: input.description || ""
    };
}

function handleBashSelection(value, toolUseConfirm, onDone, onReject, setToolPermissionContext) {
    switch (value) {
        case "yes":
            onDone();
            toolUseConfirm.onApprove();
            break;
        case "yes-dont-ask-again-prefix":
            const rule = createPermissionRule(toolUseConfirm);
            applyPermissionRule(rule, setToolPermissionContext);
            onDone();
            toolUseConfirm.onApprove();
            break;
        case "no":
            onReject();
            toolUseConfirm.onReject();
            break;
    }
}

function setWindowTitle(title) {
    // TODO: Implement window title setting
    process.stdout.write(`\x1b]0;${title}\x07`);
}

function useTheme() {
    // TODO: Implement theme hook
    return [{ name: "dark" }];
}

function parseRuleContent(content) {
    // TODO: Implement rule content parsing
    return content;
}

module.exports = {
    ToolPermissionConfirmation,
    selectConfirmationComponent,
    generateConfirmationOptions,
    trackPermissionRequest,
    GenericToolConfirmation,
    BashConfirmation,
    PermissionOptionsSelector
};
