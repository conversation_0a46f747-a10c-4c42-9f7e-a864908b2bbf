/**
 * MCP Client Manager (T21, kN1, fN1)
 * 
 * Refactored version of the MCP (Model Context Protocol) client management system.
 * This handles connection, initialization, and management of MCP servers with
 * support for multiple transport types (stdio, SSE, HTTP, WebSocket).
 * 
 * Original functions:
 * - T21() at line 314874 - Individual client connection
 * - kN1() at line 315368 - Batch client connection
 * - fN1() at line 315418 - Client initialization wrapper
 */

const { EventSource } = require('eventsource');
const WebSocket = require('ws');
const { SSEClientTransport, StdioClientTransport, WebSocketClientTransport } = require('@modelcontextprotocol/sdk/client/index.js');
const { Client } = require('@modelcontextprotocol/sdk/client/index.js');
const { trackEvent, logDebug, logError } = require('../utils/analytics');
const { getUserAgent, getProxyConfig } = require('../utils/network');
const { createAuthProvider } = require('./mcp-auth');
const { extractMcpTools, extractMcpCommands, extractMcpResources } = require('./mcp-extractors');
const { getMcpServerConfigs } = require('./mcp-config');

// Maximum concurrent connections
const MAX_CONCURRENT_CONNECTIONS = 10;

/**
 * Initialize MCP Clients
 * 
 * Main entry point for initializing all MCP clients. This function:
 * 1. Merges dynamic and static configurations
 * 2. Connects to all servers concurrently
 * 3. Extracts tools, commands, and resources
 * 4. Returns aggregated results
 * 
 * Original function: fN1() at line 315418
 * 
 * @param {Object} dynamicConfig - Dynamic MCP configuration
 * @param {boolean} strictMode - Only use dynamic config if true
 * @returns {Promise<Object>} Object containing clients, tools, commands
 */
async function initializeMcpClients(dynamicConfig, strictMode = false) {
    return new Promise((resolve) => {
        const staticConfig = strictMode ? {} : getMcpServerConfigs();
        const mergedConfig = dynamicConfig ? {
            ...staticConfig,
            ...dynamicConfig
        } : staticConfig;
        
        const totalServers = Object.keys(mergedConfig).length;
        
        if (totalServers === 0) {
            resolve({
                clients: [],
                tools: [],
                commands: []
            });
            return;
        }
        
        const clients = [];
        const tools = [];
        const commands = [];
        let completedConnections = 0;
        
        // Connect to all servers
        connectToMcpServers((result) => {
            clients.push(result.client);
            tools.push(...result.tools);
            commands.push(...result.commands);
            
            completedConnections++;
            if (completedConnections >= totalServers) {
                resolve({
                    clients,
                    tools,
                    commands
                });
            }
        }, dynamicConfig, strictMode);
    });
}

/**
 * Connect to MCP Servers
 * 
 * Connects to multiple MCP servers concurrently with proper error handling
 * and connection statistics tracking.
 * 
 * Original function: kN1() at line 315368
 * 
 * @param {Function} onConnection - Callback for each connection result
 * @param {Object} dynamicConfig - Dynamic configuration
 * @param {boolean} strictMode - Strict mode flag
 */
async function connectToMcpServers(onConnection, dynamicConfig, strictMode = false) {
    const staticConfig = strictMode ? {} : getMcpServerConfigs();
    const mergedConfig = dynamicConfig ? {
        ...staticConfig,
        ...dynamicConfig
    } : staticConfig;
    
    const serverEntries = Object.entries(mergedConfig);
    const totalServers = serverEntries.length;
    
    // Count servers by transport type for analytics
    const transportCounts = {
        stdio: serverEntries.filter(([, config]) => config.type === "stdio").length,
        sse: serverEntries.filter(([, config]) => config.type === "sse").length,
        http: serverEntries.filter(([, config]) => config.type === "http").length,
        sseIde: serverEntries.filter(([, config]) => config.type === "sse-ide").length,
        wsIde: serverEntries.filter(([, config]) => config.type === "ws-ide").length
    };
    
    let hasResourceCapability = false;
    
    // Process servers with concurrency limit
    await processConcurrently(serverEntries, MAX_CONCURRENT_CONNECTIONS, async ([serverName, serverConfig]) => {
        logDebug(`Starting connection attempt for ${serverName}`);
        const startTime = Date.now();
        
        const connectionResult = await connectToMcpServer(serverName, serverConfig, {
            totalServers,
            ...transportCounts
        });
        
        const duration = Date.now() - startTime;
        logDebug(`Connection attempt for ${serverName} completed in ${duration}ms - status: ${connectionResult.type}`);
        
        if (connectionResult.type !== "connected") {
            onConnection({
                client: connectionResult,
                tools: [],
                commands: []
            });
            return;
        }
        
        // Extract capabilities and data
        const supportsResources = !!connectionResult.capabilities?.resources;
        const [tools, commands, resources] = await Promise.all([
            extractMcpTools(connectionResult),
            extractMcpCommands(connectionResult),
            supportsResources ? extractMcpResources(connectionResult) : Promise.resolve([])
        ]);
        
        // Add resource tools if this is the first server with resource capability
        const resourceTools = [];
        if (supportsResources && !hasResourceCapability) {
            hasResourceCapability = true;
            resourceTools.push(
                createResourceListTool(), // TODO: Implement KN1
                createResourceReadTool()  // TODO: Implement EN1
            );
        }
        
        onConnection({
            client: connectionResult,
            tools: [...tools, ...resourceTools],
            commands,
            resources: resources.length > 0 ? resources : undefined
        });
    });
}

/**
 * Connect to Single MCP Server
 * 
 * Establishes connection to a single MCP server based on transport type.
 * Supports stdio, SSE, HTTP, and WebSocket transports.
 * 
 * Original function: T21() at line 314874
 * 
 * @param {string} serverName - Name of the server
 * @param {Object} serverConfig - Server configuration
 * @param {Object} stats - Connection statistics
 * @returns {Promise<Object>} Connection result
 */
async function connectToMcpServer(serverName, serverConfig, stats) {
    try {
        let client;
        
        switch (serverConfig.type) {
            case "sse":
                client = await createSseClient(serverName, serverConfig);
                break;
                
            case "sse-ide":
                client = await createSseIdeClient(serverName, serverConfig);
                break;
                
            case "ws-ide":
                client = await createWebSocketIdeClient(serverName, serverConfig);
                break;
                
            case "http":
                client = await createHttpClient(serverName, serverConfig);
                break;
                
            case "stdio":
                client = await createStdioClient(serverName, serverConfig);
                break;
                
            default:
                throw new Error(`Unsupported transport type: ${serverConfig.type}`);
        }
        
        // Initialize the client
        await initializeClient(client, serverName, serverConfig, stats);
        
        return client;
        
    } catch (error) {
        logError(`Failed to connect to MCP server ${serverName}: ${error.message}`);
        
        return {
            type: "error",
            name: serverName,
            error: error.message,
            config: serverConfig
        };
    }
}

/**
 * Create SSE Client
 * 
 * @param {string} serverName - Server name
 * @param {Object} config - Server configuration
 * @returns {Promise<Object>} SSE client
 */
async function createSseClient(serverName, config) {
    const authProvider = createAuthProvider(serverName, config);
    const proxyConfig = getProxyConfig();
    
    const clientOptions = {
        authProvider,
        requestInit: {
            headers: {
                "User-Agent": getUserAgent(),
                ...config.headers || {}
            },
            signal: AbortSignal.timeout(60000),
            ...proxyConfig
        }
    };
    
    if (config.headers) {
        clientOptions.eventSourceInit = {
            fetch: async (url, init) => {
                const authHeaders = {};
                const tokens = await authProvider.tokens();
                if (tokens) {
                    authHeaders.Authorization = `Bearer ${tokens.access_token}`;
                }
                
                return fetch(url, {
                    ...init,
                    ...proxyConfig,
                    headers: {
                        "User-Agent": getUserAgent(),
                        ...authHeaders,
                        ...init?.headers,
                        ...config.headers,
                        Accept: "text/event-stream"
                    }
                });
            }
        };
    }
    
    return new SSEClientTransport(new URL(config.url), clientOptions);
}

/**
 * Create SSE IDE Client
 * 
 * @param {string} serverName - Server name
 * @param {Object} config - Server configuration
 * @returns {Promise<Object>} SSE IDE client
 */
async function createSseIdeClient(serverName, config) {
    const proxyConfig = getProxyConfig();
    const clientOptions = proxyConfig.dispatcher ? {
        eventSourceInit: {
            fetch: async (url, init) => {
                return fetch(url, {
                    ...init,
                    ...proxyConfig,
                    headers: {
                        "User-Agent": getUserAgent(),
                        ...init?.headers
                    }
                });
            }
        }
    } : {};
    
    return new SSEClientTransport(
        new URL(config.url),
        Object.keys(clientOptions).length > 0 ? clientOptions : undefined
    );
}

/**
 * Create WebSocket IDE Client
 * 
 * @param {string} serverName - Server name
 * @param {Object} config - Server configuration
 * @returns {Promise<Object>} WebSocket IDE client
 */
async function createWebSocketIdeClient(serverName, config) {
    const proxyConfig = getWebSocketProxyConfig(); // TODO: Implement kd0()
    const wsOptions = {
        headers: {
            "User-Agent": getUserAgent(),
            ...config.authToken && {
                "X-Claude-Code-Ide-Authorization": config.authToken
            }
        },
        ...proxyConfig || {}
    };
    
    const websocket = new WebSocket(
        config.url,
        ["mcp"],
        Object.keys(wsOptions).length > 0 ? wsOptions : undefined
    );
    
    return new WebSocketClientTransport(websocket);
}

/**
 * Create HTTP Client
 * 
 * @param {string} serverName - Server name
 * @param {Object} config - Server configuration
 * @returns {Promise<Object>} HTTP client
 */
async function createHttpClient(serverName, config) {
    const authProvider = createAuthProvider(serverName, config);
    const proxyConfig = getProxyConfig();
    
    return new HttpClientTransport(new URL(config.url), {
        authProvider,
        requestInit: {
            ...proxyConfig,
            headers: {
                "User-Agent": getUserAgent(),
                ...config.headers || {}
            }
        }
    });
}

/**
 * Create Stdio Client
 * 
 * @param {string} serverName - Server name
 * @param {Object} config - Server configuration
 * @returns {Promise<Object>} Stdio client
 */
async function createStdioClient(serverName, config) {
    const { spawn } = require('child_process');
    
    // Prepare command and arguments
    const command = config.command;
    const args = config.args || [];
    const env = {
        ...process.env,
        ...config.env || {}
    };
    
    // Spawn the process
    const childProcess = spawn(command, args, {
        env,
        stdio: ['pipe', 'pipe', 'pipe']
    });
    
    return new StdioClientTransport({
        stdin: childProcess.stdin,
        stdout: childProcess.stdout,
        stderr: childProcess.stderr
    });
}

/**
 * Initialize Client
 * 
 * @param {Object} client - MCP client
 * @param {string} serverName - Server name
 * @param {Object} config - Server configuration
 * @param {Object} stats - Connection statistics
 */
async function initializeClient(client, serverName, config, stats) {
    // Track connection attempt
    trackEvent("mcp_connection_attempt", {
        serverName,
        transportType: config.type,
        ...stats
    });
    
    try {
        // Connect and initialize
        await client.connect();
        
        // Track successful connection
        trackEvent("mcp_connection_success", {
            serverName,
            transportType: config.type,
            capabilities: Object.keys(client.capabilities || {})
        });
        
        // Set additional properties
        client.name = serverName;
        client.type = "connected";
        client.config = config;
        
    } catch (error) {
        // Track connection failure
        trackEvent("mcp_connection_failure", {
            serverName,
            transportType: config.type,
            error: error.message
        });
        
        throw error;
    }
}

/**
 * Process items concurrently with limit
 * 
 * @param {Array} items - Items to process
 * @param {number} limit - Concurrency limit
 * @param {Function} processor - Processing function
 */
async function processConcurrently(items, limit, processor) {
    for (let i = 0; i < items.length; i += limit) {
        const batch = items.slice(i, i + limit);
        await Promise.all(batch.map(processor));
    }
}

// Utility functions (TODO: Implement these)
function createResourceListTool() {
    // TODO: Implement KN1
    return {
        name: "mcp_list_resources",
        description: "List available MCP resources"
    };
}

function createResourceReadTool() {
    // TODO: Implement EN1
    return {
        name: "mcp_read_resource",
        description: "Read MCP resource content"
    };
}

function getWebSocketProxyConfig() {
    // TODO: Implement kd0()
    return null;
}

module.exports = {
    initializeMcpClients,
    connectToMcpServers,
    connectToMcpServer,
    createSseClient,
    createSseIdeClient,
    createWebSocketIdeClient,
    createHttpClient,
    createStdioClient,
    MAX_CONCURRENT_CONNECTIONS
};
