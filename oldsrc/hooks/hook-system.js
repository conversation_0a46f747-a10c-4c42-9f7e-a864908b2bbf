/**
 * Hook System - Event-driven hook execution and management
 * 
 * Refactored version of the hook system from the original code.
 * This system provides event-driven hook execution with discovery, matching,
 * execution, and result processing capabilities.
 * 
 * Original functions:
 * - xq1() at line 322899 - Hook executor
 * - Zn2() at line 322833 - Hook discovery
 * - zn2() at line 323177 - User prompt hooks
 * - Vn2() at line 323127 - Pre-tool hooks
 * - Kn2() at line 323137 - Post-tool hooks
 * - Hn2() at line 323169 - Stop hooks
 */

const { executeShellCommand } = require('../shell/command-executor');
const { trackEvent, generateSessionId, logDebug } = require('../utils/analytics');
const { createWarningMessage, createProgressMessage } = require('../core/message-factory');
const { parseJsonOutput, validateHookResult } = require('../utils/hook-utils');
const { getHookSettings } = require('../core/settings');

// Default hook timeout (30 seconds)
const DEFAULT_HOOK_TIMEOUT = 30000;

/**
 * Hook Executor - Core hook execution engine
 * 
 * This is the main hook execution engine that:
 * 1. Discovers matching hooks for events
 * 2. Executes hooks with timeout and abort support
 * 3. Processes hook results and handles errors
 * 4. Provides progress tracking and logging
 * 5. Manages hook approval and blocking logic
 * 
 * Original function: xq1(A, B, Q, I, D=lP) at line 322899
 * 
 * @param {Object} hookContext - Hook execution context
 * @param {string} parentToolUseId - Parent tool use ID for progress tracking
 * @param {string} matchQuery - Query for hook matching (optional)
 * @param {AbortSignal} abortSignal - Abort signal for cancellation
 * @param {number} timeout - Hook timeout in milliseconds
 * @yields {Object} Hook execution results and messages
 */
async function* executeHooks(hookContext, parentToolUseId, matchQuery, abortSignal, timeout = DEFAULT_HOOK_TIMEOUT) {
    const eventName = hookContext.hook_event_name;
    const fullEventName = matchQuery ? `${eventName}:${matchQuery}` : eventName;
    
    logDebug(`Executing hooks for ${fullEventName}`);
    
    // Phase 1: Hook Discovery
    const matchingHooks = discoverHooks(eventName, hookContext).filter(hook => hook.type === "command");
    
    logDebug(`Found ${matchingHooks.length} hook commands to execute`);
    
    if (matchingHooks.length === 0) {
        return;
    }
    
    // Check for abort before starting
    if (abortSignal?.aborted) {
        return;
    }
    
    // Phase 2: Prepare Hook Input
    let hookInputJson;
    try {
        hookInputJson = JSON.stringify(hookContext);
    } catch (error) {
        logError(new Error(`Failed to stringify hook ${fullEventName} input`, { cause: error }));
        
        yield {
            message: createWarningMessage(
                `Failed to prepare hook input: ${error instanceof Error ? error.message : String(error)}`,
                "warning",
                parentToolUseId
            )
        };
        return;
    }
    
    // Track hook execution
    trackEvent("tengu_run_hook", {
        hookName: fullEventName,
        numCommands: matchingHooks.length
    });
    
    // Phase 3: Execute Hooks in Parallel
    const blockingErrors = [];
    
    // Yield progress messages for each hook
    for (const hook of matchingHooks) {
        yield {
            message: {
                type: "progress",
                data: {
                    type: "running_hook",
                    hookName: fullEventName,
                    command: hook.command
                },
                parentToolUseID: parentToolUseId,
                toolUseID: `hook-${generateSessionId()}`,
                timestamp: new Date().toISOString(),
                uuid: generateSessionId()
            }
        };
    }
    
    // Execute all hooks in parallel
    const hookPromises = matchingHooks.map(async (hook) => {
        const hookTimeout = hook.timeout ? hook.timeout * 1000 : timeout;
        let abortController;
        let cleanup;
        
        // Create combined abort signal
        if (abortSignal) {
            const combined = combineAbortSignals(abortSignal, AbortSignal.timeout(hookTimeout));
            abortController = combined.signal;
            cleanup = combined.cleanup;
        } else {
            abortController = AbortSignal.timeout(hookTimeout);
        }
        
        try {
            logDebug(`Executing hook command: ${hook.command} with timeout ${hookTimeout}ms`);
            
            // Execute the hook command
            const result = await executeShellCommand(hook.command, hookInputJson, abortController);
            
            cleanup?.();
            
            logDebug(`Hook command completed with status ${result.status}: ${hook.command}`);
            
            if (result.stdout) {
                logDebug(`Hook stdout: ${result.stdout.substring(0, 200)}...`);
            }
            if (result.stderr) {
                logDebug(`Hook stderr: ${result.stderr}`);
            }
            
            // Handle aborted execution
            if (result.aborted) {
                return {
                    message: createWarningMessage(
                        `${formatEventName(fullEventName)} [${hook.command}] cancelled`,
                        "info",
                        parentToolUseId
                    ),
                    outcome: "cancelled"
                };
            }
            
            // Process hook output
            const { json, plainText, validationError } = parseJsonOutput(result.stdout);
            
            if (validationError) {
                return {
                    message: createWarningMessage(
                        `${formatEventName(fullEventName)} [${hook.command}] JSON validation failed:\n${validationError}`,
                        "warning",
                        parentToolUseId
                    ),
                    outcome: "non_blocking_error"
                };
            }
            
            // Handle JSON output
            if (json) {
                logDebug(`Parsed JSON output from hook: ${JSON.stringify(json)}`);
                
                const processedResult = processHookResult(json, hook.command);
                logDebug(`Processed hook result: ${JSON.stringify(processedResult)}`);
                
                if (!json.suppressOutput && plainText && result.status === 0) {
                    const successMessage = `${formatEventName(fullEventName)} [${hook.command}] completed successfully`;
                    return {
                        ...processedResult,
                        message: processedResult.message || createWarningMessage(successMessage, "info", parentToolUseId),
                        outcome: "success"
                    };
                }
                
                return {
                    ...processedResult,
                    outcome: "success"
                };
            }
            
            // Handle plain text output
            if (result.status === 0) {
                let successMessage = `${formatEventName(fullEventName)} [${hook.command}] completed successfully`;
                if (result.stdout.trim()) {
                    successMessage += `: ${result.stdout.trim()}`;
                }
                
                return {
                    message: createWarningMessage(successMessage, "info", parentToolUseId),
                    outcome: "success"
                };
            }
            
            // Handle blocking errors (status 2)
            if (result.status === 2) {
                return {
                    blockingError: {
                        blockingError: `[${hook.command}]: ${result.stderr || "No stderr output"}`,
                        command: hook.command
                    },
                    outcome: "blocking"
                };
            }
            
            // Handle non-blocking errors
            return {
                message: createWarningMessage(
                    `${formatEventName(fullEventName)} [${hook.command}] failed with non-blocking status code ${result.status}: ${result.stderr || "No stderr output"}`,
                    "warning",
                    parentToolUseId
                ),
                outcome: "non_blocking_error"
            };
            
        } catch (error) {
            cleanup?.();
            
            const errorMessage = error instanceof Error ? error.message : String(error);
            return {
                message: createWarningMessage(
                    `${formatEventName(fullEventName)} [${hook.command}] failed to run: ${errorMessage}`,
                    "warning",
                    parentToolUseId
                ),
                outcome: "non_blocking_error"
            };
        }
    });
    
    // Wait for all hooks to complete
    const hookResults = await Promise.all(hookPromises);
    
    // Phase 4: Process Results
    const outcomeStats = {
        success: 0,
        blocking: 0,
        non_blocking_error: 0,
        cancelled: 0
    };
    
    let preventContinuation = false;
    let stopReason;
    let hookApproved = false;
    let hookApprovalReason;
    
    for (const result of hookResults) {
        if (!result) continue;
        
        // Update outcome statistics
        if (result.outcome) {
            outcomeStats[result.outcome]++;
        }
        
        // Handle continuation prevention
        if ("preventContinuation" in result && result.preventContinuation) {
            preventContinuation = true;
            if ("stopReason" in result && result.stopReason) {
                stopReason = result.stopReason;
            }
        }
        
        // Collect blocking errors
        if ("blockingError" in result && result.blockingError) {
            blockingErrors.push(result.blockingError);
        } else if ("blockingErrors" in result && result.blockingErrors) {
            blockingErrors.push(...result.blockingErrors);
        }
        
        // Yield messages
        if ("message" in result && result.message) {
            yield { message: result.message };
        }
        
        // Handle hook approval
        if ("hookApproved" in result && result.hookApproved) {
            hookApproved = true;
            if ("hookApprovalReason" in result && result.hookApprovalReason) {
                hookApprovalReason = result.hookApprovalReason;
            }
        }
    }
    
    // Track completion
    trackEvent("tengu_repl_hook_finished", {
        hookName: fullEventName,
        numCommands: matchingHooks.length,
        numSuccess: outcomeStats.success,
        numBlocking: outcomeStats.blocking,
        numNonBlockingError: outcomeStats.non_blocking_error,
        numCancelled: outcomeStats.cancelled
    });
    
    // Return final result
    yield {
        blockingErrors,
        preventContinuation,
        stopReason,
        hookApproved,
        hookApprovalReason
    };
}

/**
 * Hook Discovery - Find matching hooks for events
 * 
 * Original function: Zn2(A, B) at line 322833
 * 
 * @param {string} eventName - Hook event name
 * @param {Object} hookContext - Hook context for matching
 * @returns {Array} Array of matching hook commands
 */
function discoverHooks(eventName, hookContext) {
    try {
        const hookMatchers = getHookSettings()?.[eventName] ?? [];
        let matchQuery = undefined;
        
        // Determine match query based on event type
        switch (hookContext.hook_event_name) {
            case "PreToolUse":
            case "PostToolUse":
                matchQuery = hookContext.tool_name;
                break;
            default:
                break;
        }
        
        logDebug(`Getting matching hook commands for ${eventName} with query: ${matchQuery}`);
        logDebug(`Found ${hookMatchers.length} hook matchers in settings`);
        
        // If no match query, return all hooks
        if (!matchQuery) {
            const allHooks = hookMatchers.flatMap(matcher => matcher.hooks);
            logDebug(`No match query, returning all ${allHooks.length} hooks`);
            return allHooks;
        }
        
        // Filter by matcher and return matching hooks
        const matchingHooks = hookMatchers
            .filter(matcher => !matcher.matcher || matchesPattern(matchQuery, matcher.matcher))
            .flatMap(matcher => matcher.hooks);
        
        logDebug(`Matched ${matchingHooks.length} hooks for query "${matchQuery}"`);
        return matchingHooks;
        
    } catch (error) {
        logError(error);
        return [];
    }
}

/**
 * User Prompt Hooks - Execute hooks for user prompt submission
 * 
 * Original function: zn2(A, B, Q=lP) at line 323177
 * 
 * @param {string} prompt - User prompt
 * @param {string} parentToolUseId - Parent tool use ID
 * @param {number} timeout - Hook timeout
 * @yields {Object} Hook execution results
 */
async function* executeUserPromptHooks(prompt, parentToolUseId, timeout = DEFAULT_HOOK_TIMEOUT) {
    const hookContext = {
        ...getBaseHookContext(),
        hook_event_name: "UserPromptSubmit",
        prompt
    };
    
    yield* executeHooks(hookContext, getSessionId(), undefined, undefined, timeout);
}

/**
 * Pre-Tool Hooks - Execute hooks before tool execution
 * 
 * Original function: Vn2(A, B, Q, I, D=lP) at line 323127
 * 
 * @param {string} toolName - Tool name
 * @param {string} toolUseId - Tool use ID
 * @param {Object} toolInput - Tool input
 * @param {AbortSignal} abortSignal - Abort signal
 * @param {number} timeout - Hook timeout
 * @yields {Object} Hook execution results
 */
async function* executePreToolHooks(toolName, toolUseId, toolInput, abortSignal, timeout = DEFAULT_HOOK_TIMEOUT) {
    logDebug(`executePreToolHooks called for tool: ${toolName}`);
    
    const hookContext = {
        ...getBaseHookContext(),
        hook_event_name: "PreToolUse",
        tool_name: toolName,
        tool_input: toolInput
    };
    
    yield* executeHooks(hookContext, toolUseId, toolName, abortSignal, timeout);
}

/**
 * Post-Tool Hooks - Execute hooks after tool execution
 * 
 * Original function: Kn2(A, B, Q, I, D, G=lP) at line 323137
 * 
 * @param {string} toolName - Tool name
 * @param {string} toolUseId - Tool use ID
 * @param {Object} toolInput - Tool input
 * @param {Object} toolOutput - Tool output
 * @param {Object} toolResult - Tool result
 * @param {number} timeout - Hook timeout
 * @yields {Object} Hook execution results
 */
async function* executePostToolHooks(toolName, toolUseId, toolInput, toolOutput, toolResult, timeout = DEFAULT_HOOK_TIMEOUT) {
    const hookContext = {
        ...getBaseHookContext(),
        hook_event_name: "PostToolUse",
        tool_name: toolName,
        tool_input: toolInput,
        tool_output: toolOutput,
        tool_result: toolResult
    };
    
    yield* executeHooks(hookContext, toolUseId, toolName, undefined, timeout);
}

/**
 * Stop Hooks - Execute hooks when stopping execution
 * 
 * Original function: Hn2(A, B=lP, Q=!1, I=!1) at line 323169
 * 
 * @param {AbortSignal} abortSignal - Abort signal
 * @param {number} timeout - Hook timeout
 * @param {boolean} stopHookActive - Whether stop hook is active
 * @param {boolean} isSubagent - Whether this is a subagent stop
 * @yields {Object} Hook execution results
 */
async function* executeStopHooks(abortSignal, timeout = DEFAULT_HOOK_TIMEOUT, stopHookActive = false, isSubagent = false) {
    const hookContext = {
        ...getBaseHookContext(),
        hook_event_name: isSubagent ? "SubagentStop" : "Stop",
        stop_hook_active: stopHookActive
    };
    
    yield* executeHooks(hookContext, getSessionId(), undefined, abortSignal, timeout);
}

// Utility functions
function getBaseHookContext() {
    // TODO: Implement Fp() function
    return {
        timestamp: new Date().toISOString(),
        session_id: getSessionId()
    };
}

function getSessionId() {
    // TODO: Implement fq1() function
    return generateSessionId();
}

function formatEventName(eventName) {
    // TODO: Implement YA.bold() function
    return eventName;
}

function processHookResult(json, command) {
    // TODO: Implement $24() function
    return json;
}

function matchesPattern(query, pattern) {
    // TODO: Implement L24() function
    try {
        const regex = new RegExp(pattern);
        return regex.test(query);
    } catch {
        logDebug(`Invalid regex pattern in hook matcher: ${pattern}`);
        return false;
    }
}

function combineAbortSignals(signal1, signal2) {
    // TODO: Implement Cn2() function
    const controller = new AbortController();
    
    const cleanup1 = () => controller.abort();
    const cleanup2 = () => controller.abort();
    
    signal1.addEventListener("abort", cleanup1);
    signal2.addEventListener("abort", cleanup2);
    
    const cleanup = () => {
        signal1.removeEventListener("abort", cleanup1);
        signal2.removeEventListener("abort", cleanup2);
    };
    
    return {
        signal: controller.signal,
        cleanup
    };
}

function logError(error) {
    console.error(error);
}

module.exports = {
    executeHooks,
    discoverHooks,
    executeUserPromptHooks,
    executePreToolHooks,
    executePostToolHooks,
    executeStopHooks,
    DEFAULT_HOOK_TIMEOUT
};
