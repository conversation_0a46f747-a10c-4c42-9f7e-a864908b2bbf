/**
 * Shell Permission Manager
 * 
 * Manages permissions for shell commands, including rule-based access control,
 * command validation, and security checks.
 */

const path = require('path');
const { extractCommands } = require('./command-parser');

/**
 * Dangerous shell characters that require special handling
 */
const DANGEROUS_CHARS = ["`", "$(", "${", "~[", "(e:", "\n", "\r", ";", "|", "&", "||", "&&", ">", "<", ">>", ">&", ">&2", "<(", ">(", "$", "\\", "#"];

/**
 * Creates a prefix rule for command matching
 * @param {string} prefix - The command prefix
 * @returns {string} The formatted rule
 */
const createPrefixRule = (prefix) => `${prefix}:*`;

/**
 * Creates exact match rules for commands
 * @param {string} command - The exact command
 * @returns {Array} Array of rule objects
 */
function createExactRules(command) {
    return [{
        toolName: 'bash', // TODO: Replace with actual tool name reference
        ruleContent: command
    }];
}

/**
 * Creates prefix-based rules for commands
 * @param {string} prefix - The command prefix
 * @returns {Array} Array of rule objects
 */
function createPrefixRules(prefix) {
    return [{
        toolName: 'bash', // TODO: Replace with actual tool name reference
        ruleContent: createPrefixRule(prefix)
    }];
}

/**
 * Extracts prefix from a rule string
 * @param {string} rule - The rule string
 * @returns {string|null} The extracted prefix or null
 */
function extractPrefixFromRule(rule) {
    return rule.match(/^(.+):\*$/)?.[1] ?? null;
}

/**
 * Parses a rule to determine its type and content
 * @param {string} rule - The rule string
 * @returns {Object} Parsed rule object
 */
function parseRule(rule) {
    const prefix = extractPrefixFromRule(rule);
    if (prefix !== null) {
        return {
            type: "prefix",
            prefix: prefix
        };
    } else {
        return {
            type: "exact",
            command: rule
        };
    }
}

/**
 * Finds matching rules for a command
 * @param {Object} input - Command input object
 * @param {Map} rules - Map of rules
 * @param {string} matchType - Type of matching ("exact" or "prefix")
 * @returns {Array} Array of matching rules
 */
function findMatchingRules(input, rules, matchType) {
    const command = input.command.trim();
    return Array.from(rules.entries()).filter(([ruleKey]) => {
        const parsedRule = parseRule(ruleKey);
        switch (parsedRule.type) {
            case "exact":
                return parsedRule.command === command;
            case "prefix":
                switch (matchType) {
                    case "exact":
                        return parsedRule.prefix === command;
                    case "prefix":
                        return command.startsWith(parsedRule.prefix);
                }
        }
    }).map(([, ruleValue]) => ruleValue);
}

/**
 * Gets matching allow and deny rules for a command
 * @param {Object} input - Command input object
 * @param {Object} permissionContext - Permission context
 * @param {string} matchType - Type of matching
 * @returns {Object} Object with matching rules
 */
function getMatchingRules(input, permissionContext, matchType) {
    // TODO: Implement actual rule retrieval from permission context
    const denyRules = new Map(); // Yh(permissionContext, toolRef, "deny")
    const matchingDenyRules = findMatchingRules(input, denyRules, matchType);
    
    const allowRules = new Map(); // Yh(permissionContext, toolRef, "allow")
    const matchingAllowRules = findMatchingRules(input, allowRules, matchType);
    
    return {
        matchingDenyRules,
        matchingAllowRules
    };
}

/**
 * Validates cd command permissions
 * @param {Object} input - Command input object
 * @param {string} currentDir - Current working directory
 * @param {string} sessionDir - Session working directory
 * @param {Array} allowedDirs - Array of allowed directories
 * @returns {Object} Permission decision
 */
function validateCdCommand(input, currentDir, sessionDir, allowedDirs) {
    const commands = extractCommands(input.command);
    
    for (const command of commands) {
        const [cmd, ...args] = command.split(" ");
        if (cmd === "cd" && args.length > 0) {
            const targetPath = args.join(" ").replace(/^['"]|['"]$/g, "");
            const resolvedPath = path.isAbsolute(targetPath) 
                ? targetPath 
                : path.resolve(currentDir, targetPath);
            
            // TODO: Implement directory validation logic
            const isAllowed = true; // gY(resolvedPath, allowedDirs)
            if (!isAllowed) {
                return {
                    behavior: "default",
                    message: `cd to '${resolvedPath}' was blocked. For security, Claude may only change directories to child directories of the allowed working directories for this session (including '${sessionDir}').`
                };
            }
        }
    }
    
    return {
        behavior: "allow",
        updatedInput: input
    };
}

/**
 * Checks exact command permissions
 * @param {Object} input - Command input object
 * @param {Object} permissionContext - Permission context
 * @returns {Object} Permission decision
 */
function checkExactPermissions(input, permissionContext) {
    const command = input.command.trim();
    const { matchingDenyRules, matchingAllowRules } = getMatchingRules(input, permissionContext, "exact");
    
    if (matchingDenyRules[0] !== undefined) {
        return {
            behavior: "deny",
            message: `Permission to use bash with command ${command} has been denied.`,
            decisionReason: {
                type: "rule",
                rule: matchingDenyRules[0]
            },
            ruleSuggestions: null
        };
    }
    
    if (matchingAllowRules[0] !== undefined) {
        return {
            behavior: "allow",
            updatedInput: input,
            decisionReason: {
                type: "rule",
                rule: matchingAllowRules[0]
            }
        };
    }
    
    // TODO: Implement isReadOnly check
    const isReadOnly = false; // toolRef.isReadOnly(input)
    if (isReadOnly) {
        return {
            behavior: "allow",
            updatedInput: input,
            decisionReason: {
                type: "other",
                reason: "Sandboxed command is allowed"
            }
        };
    }
    
    return {
        behavior: "default",
        message: `Claude requested permissions to use bash, but you haven't granted it yet.`,
        ruleSuggestions: createExactRules(command)
    };
}

/**
 * Checks prefix-based permissions
 * @param {Object} input - Command input object
 * @param {Object} permissionContext - Permission context
 * @returns {Object} Permission decision
 */
function checkPrefixPermissions(input, permissionContext) {
    const command = input.command.trim();
    
    // Handle cd command specially
    if (command.split(" ")[0] === "cd") {
        const cdResult = validateCdCommand(input, process.cwd(), process.cwd(), []);
        if (cdResult.behavior === "allow") {
            return {
                behavior: "allow",
                updatedInput: input,
                decisionReason: {
                    type: "other",
                    reason: "cd command is allowed"
                }
            };
        }
    }
    
    const exactResult = checkExactPermissions(input, permissionContext);
    if (exactResult.behavior === "deny") {
        return exactResult;
    }
    
    const { matchingDenyRules, matchingAllowRules } = getMatchingRules(input, permissionContext, "prefix");
    
    if (matchingDenyRules[0] !== undefined) {
        return {
            behavior: "deny",
            message: `Permission to use bash with command ${command} has been denied.`,
            decisionReason: {
                type: "rule",
                rule: matchingDenyRules[0]
            },
            ruleSuggestions: null
        };
    }
    
    if (exactResult.behavior === "allow") {
        return exactResult;
    }
    
    if (matchingAllowRules[0] !== undefined) {
        return {
            behavior: "allow",
            updatedInput: input,
            decisionReason: {
                type: "rule",
                rule: matchingAllowRules[0]
            }
        };
    }
    
    return {
        behavior: "default",
        message: `Claude requested permissions to use bash, but you haven't granted it yet.`,
        ruleSuggestions: createExactRules(command)
    };
}

/**
 * Main permission check function
 * @param {Object} input - Command input object
 * @param {Object} permissionContext - Permission context
 * @param {Object} prefixInfo - Command prefix information
 * @returns {Object} Permission decision
 */
function checkPermissions(input, permissionContext, prefixInfo) {
    const exactResult = checkExactPermissions(input, permissionContext);
    if (exactResult.behavior === "deny" || exactResult.behavior === "allow") {
        return exactResult;
    }
    
    const prefixResult = checkPrefixPermissions(input, permissionContext);
    if (prefixResult.behavior === "deny") {
        return prefixResult;
    }
    
    // Check for command injection detection bypass
    if (prefixInfo === null || prefixInfo === undefined) {
        const disableCheck = process.env.CLAUDE_CODE_DISABLE_COMMAND_INJECTION_CHECK === 'true';
        if (disableCheck) {
            return prefixResult;
        }
        return {
            behavior: "default",
            message: `Claude requested permissions to use bash, but you haven't granted it yet.`,
            decisionReason: {
                type: "other",
                reason: "Command prefix query failed"
            },
            ruleSuggestions: createExactRules(input.command)
        };
    }
    
    if (prefixInfo.commandInjectionDetected) {
        const disableCheck = process.env.CLAUDE_CODE_DISABLE_COMMAND_INJECTION_CHECK === 'true';
        if (disableCheck) {
            return prefixResult;
        }
        return {
            behavior: "default",
            message: `Claude requested permissions to use bash, but you haven't granted it yet.`,
            decisionReason: {
                type: "other",
                reason: "Potential command injection detected"
            },
            ruleSuggestions: null
        };
    }
    
    if (prefixResult.behavior === "allow") {
        return prefixResult;
    }
    
    const ruleSuggestions = prefixInfo.commandPrefix 
        ? createPrefixRules(prefixInfo.commandPrefix) 
        : createExactRules(input.command);
    
    return {
        ...prefixResult,
        ruleSuggestions
    };
}

/**
 * Normalizes permission decision to standard format
 * @param {Object} decision - Permission decision
 * @returns {Object} Normalized decision
 */
function normalizePermissionDecision(decision) {
    if (decision.behavior === "default") {
        return {
            behavior: "ask",
            message: decision.message,
            decisionReason: decision.decisionReason,
            ruleSuggestions: decision.ruleSuggestions
        };
    }
    return decision;
}

module.exports = {
    createPrefixRule,
    createExactRules,
    createPrefixRules,
    extractPrefixFromRule,
    parseRule,
    findMatchingRules,
    getMatchingRules,
    validateCdCommand,
    checkExactPermissions,
    checkPrefixPermissions,
    checkPermissions,
    normalizePermissionDecision,
    DANGEROUS_CHARS
};
