/**
 * Background Task Manager
 * 
 * Manages background shell processes, providing functionality to create,
 * monitor, kill, and retrieve output from background tasks.
 */

const EventEmitter = require('events');

/**
 * Represents a background shell process
 */
class BackgroundShell {
    constructor(id, command, shellCommand, onComplete) {
        this.id = id;
        this.command = command;
        this.startTime = Date.now();
        this.status = "running";
        this.result = null;
        this.shellCommand = shellCommand;
        this.stdout = "";
        this.stderr = "";
        
        console.log(`BackgroundShell ${id} created for command: ${command}`);
        
        // Start the background process
        const backgroundProcess = shellCommand.background(id);
        if (!backgroundProcess) {
            this.status = "failed";
            this.result = {
                code: 1,
                interrupted: false
            };
        } else {
            // Set up output streams
            backgroundProcess.stdoutStream.on("data", (data) => {
                this.stdout += data.toString();
            });
            
            backgroundProcess.stderrStream.on("data", (data) => {
                this.stderr += data.toString();
            });
            
            // Handle process completion
            shellCommand.result.then((result) => {
                if (result.code === 0) {
                    this.status = "completed";
                } else {
                    this.status = "failed";
                }
                
                this.result = {
                    code: result.code,
                    interrupted: result.interrupted
                };
                
                console.log(`BackgroundShell ${id} completed with code ${result.code} (interrupted: ${result.interrupted})`);
                onComplete(result);
            });
        }
    }
    
    /**
     * Gets and clears the accumulated output
     * @returns {Object} Object with stdout and stderr
     */
    getOutput() {
        const output = {
            stdout: this.stdout,
            stderr: this.stderr
        };
        
        // Clear the buffers after reading
        this.stdout = "";
        this.stderr = "";
        
        return output;
    }
    
    /**
     * Checks if there's new output available
     * @returns {boolean} True if there's new stdout data
     */
    hasNewOutput() {
        return !!this.stdout;
    }
    
    /**
     * Attempts to kill the background process
     * @returns {boolean} True if kill was successful
     */
    kill() {
        try {
            console.log(`BackgroundShell ${this.id} kill requested`);
            this.shellCommand?.kill();
            this.status = "killed";
            return true;
        } catch (error) {
            console.error('Error killing background shell:', error);
            return false;
        }
    }
    
    /**
     * Disposes of the shell command reference
     */
    dispose() {
        this.shellCommand = null;
    }
}

/**
 * Singleton manager for background shell processes
 */
class BackgroundTaskManager extends EventEmitter {
    static instance = null;
    
    constructor() {
        super();
        this.shells = new Map();
        this.shellCounter = 0;
        this.subscribers = new Set();
    }
    
    /**
     * Gets the singleton instance
     * @returns {BackgroundTaskManager} The singleton instance
     */
    static getInstance() {
        if (!BackgroundTaskManager.instance) {
            BackgroundTaskManager.instance = new BackgroundTaskManager();
        }
        return BackgroundTaskManager.instance;
    }
    
    /**
     * Subscribes to task manager updates
     * @param {Function} callback - Callback function to call on updates
     * @returns {Function} Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => {
            this.subscribers.delete(callback);
        };
    }
    
    /**
     * Notifies all subscribers of changes
     */
    notifySubscribers() {
        this.subscribers.forEach(callback => {
            try {
                callback();
            } catch (error) {
                console.error('Error in subscriber callback:', error);
            }
        });
    }
    
    /**
     * Adds a background shell to the manager
     * @param {BackgroundShell} shell - The shell to add
     * @returns {string} The shell ID
     */
    addBackgroundShell(shell) {
        this.shells.set(shell.id, shell);
        this.notifySubscribers();
        return shell.id;
    }
    
    /**
     * Marks a shell as completed
     * @param {string} shellId - The shell ID
     * @param {Object} result - The completion result
     */
    completeShell(shellId, result) {
        const shell = this.shells.get(shellId);
        if (!shell) return;
        
        shell.status = result.code === 0 ? "completed" : "failed";
        console.log(`Shell ${shellId} completed: status=${shell.status}, code=${result.code}, interrupted=${result.interrupted}`);
        
        if (result.code === 143) {
            console.log(`Shell ${shellId} exited with code 143 (SIGTERM) - likely terminated by timeout or explicit kill`);
        }
        
        shell.result = {
            code: result.code,
            interrupted: result.interrupted
        };
        
        this.notifySubscribers();
    }
    
    /**
     * Gets all shells
     * @returns {Array} Array of all shells
     */
    getAllShells() {
        return Array.from(this.shells.values());
    }
    
    /**
     * Gets only active (running) shells
     * @returns {Array} Array of active shells
     */
    getActiveShells() {
        return Array.from(this.shells.values()).filter(shell => shell.status === "running");
    }
    
    /**
     * Gets the count of active shells
     * @returns {number} Number of active shells
     */
    getActiveShellCount() {
        return this.getActiveShells().length;
    }
    
    /**
     * Gets a specific shell by ID
     * @param {string} shellId - The shell ID
     * @returns {BackgroundShell|undefined} The shell or undefined if not found
     */
    getShell(shellId) {
        return this.shells.get(shellId);
    }
    
    /**
     * Gets the output from a specific shell
     * @param {string} shellId - The shell ID
     * @returns {Object} Shell output information
     */
    getShellOutput(shellId) {
        const shell = this.shells.get(shellId);
        if (!shell) {
            return {
                shellId,
                command: "",
                status: "failed",
                exitCode: null,
                stdout: "",
                stderr: "Shell not found"
            };
        }
        
        const exitCode = shell.result ? shell.result.code : null;
        const { stdout, stderr } = shell.getOutput();
        
        return {
            shellId,
            command: shell.command,
            status: shell.status,
            exitCode,
            stdout: stdout.trimEnd(),
            stderr: stderr.trimEnd()
        };
    }
    
    /**
     * Gets information about shells with unread output
     * @returns {Array} Array of shell info objects
     */
    getShellsUnreadOutputInfo() {
        return this.getActiveShells().map(shell => {
            const hasNewOutput = shell.hasNewOutput();
            return {
                id: shell.id,
                command: shell.command,
                hasNewOutput
            };
        });
    }
    
    /**
     * Removes a shell from the manager
     * @param {string} shellId - The shell ID
     * @returns {boolean} True if shell was removed
     */
    removeShell(shellId) {
        const shell = this.shells.get(shellId);
        if (shell) {
            if (shell.status === "running") {
                shell.kill();
                shell.dispose();
            }
            const removed = this.shells.delete(shellId);
            this.notifySubscribers();
            return removed;
        }
        return false;
    }
    
    /**
     * Kills a running shell
     * @param {string} shellId - The shell ID
     * @returns {boolean} True if shell was killed
     */
    killShell(shellId) {
        const shell = this.shells.get(shellId);
        if (shell && shell.status === "running") {
            console.log(`Killing shell ${shellId} (command: ${shell.command})`);
            shell.kill();
            
            // Clean up after 30 minutes
            setTimeout(() => {
                if (this.shells.get(shellId)) {
                    shell.dispose();
                }
            }, 1800000);
            
            this.notifySubscribers();
            return true;
        }
        return false;
    }
    
    /**
     * Moves a command to background execution
     * @param {string} command - The command to run
     * @param {Object} shellCommand - The shell command object
     * @returns {string} The generated shell ID
     */
    moveToBackground(command, shellCommand) {
        const shellId = this.generateShellId();
        console.log(`Moving command to background: ${command} (shellId: ${shellId})`);
        
        const backgroundShell = new BackgroundShell(shellId, command, shellCommand, (result) => {
            this.completeShell(backgroundShell.id, result);
        });
        
        this.addBackgroundShell(backgroundShell);
        return shellId;
    }
    
    /**
     * Generates a unique shell ID
     * @returns {string} A unique shell ID
     */
    generateShellId() {
        return `bash_${++this.shellCounter}`;
    }
}

// Export singleton instance
const backgroundTaskManager = BackgroundTaskManager.getInstance();

module.exports = {
    BackgroundShell,
    BackgroundTaskManager,
    backgroundTaskManager
};
