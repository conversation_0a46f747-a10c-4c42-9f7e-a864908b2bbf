/**
 * Shell Tool Implementation
 * 
 * Main shell tool that provides command execution capabilities with
 * permission checking, sandboxing, and output handling.
 */

const React = require('react');
const { EOL } = require('os');
const { checkPermissions, normalizePermissionDecision } = require('./permission-manager');
const { hasUnsafeMultipleCommands } = require('./command-parser');

// Constants
const MAX_COMMAND_LINES = 2;
const MAX_COMMAND_LENGTH = 160;

/**
 * Sandbox-safe command patterns
 * These commands are considered safe to run without explicit permission
 */
const SANDBOX_SAFE_PATTERNS = [
    /^date\b[^<>()$`]*$/,
    /^cal\b[^<>()$`]*$/,
    /^uptime\b[^<>()$`]*$/,
    /^echo\s+(?:'[^']*'|"[^"$<>]*"|[^|;&`$(){}><#\\\s!]+?)*$/,
    /^claude -h$/,
    /^claude --help$/,
    /^git diff(?!\s+.*--ext-diff)(?!\s+.*--extcmd)[^<>()$`]*$/,
    /^git log[^<>()$`]*$/,
    /^git show[^<>()$`]*$/,
    /^git status[^<>()$`]*$/,
    /^git blame[^<>()$`]*$/,
    /^git reflog[^<>()$`]*$/,
    /^git stash list[^<>()$`]*$/,
    /^git ls-files[^<>()$`]*$/,
    /^git ls-remote[^<>()$`]*$/,
    /^git config --get[^<>()$`]*$/,
    /^git remote -v$/,
    /^git remote show[^<>()$`]*$/,
    /^git tag$/,
    /^git tag -l[^<>()$`]*$/,
    /^git branch$/,
    /^git branch (?:-v|-vv|--verbose)$/,
    /^git branch (?:-a|--all)$/,
    /^git branch (?:-r|--remotes)$/,
    /^git branch (?:-l|--list)(?:\s+"[^"]*"|'[^']*')?$/,
    /^git branch (?:--color|--no-color|--column|--no-column)$/,
    /^git branch --sort=\S+$/,
    /^git branch --show-current$/,
    /^git branch (?:--contains|--no-contains)\s+\S+$/,
    /^git branch (?:--merged|--no-merged)(?:\s+\S+)?$/,
    /^head[^<>()$`]*$/,
    /^tail[^<>()$`]*$/,
    /^wc[^<>()$`]*$/,
    /^stat[^<>()$`]*$/,
    /^file[^<>()$`]*$/,
    /^strings[^<>()$`]*$/,
    /^hexdump[^<>()$`]*$/,
    /^sort(?!\s+.*-o\b)(?!\s+.*--output)[^<>()$`]*$/,
    /^uniq(?:\s+(?:-[a-zA-Z]+|--[a-zA-Z-]+(?:=\S+)?|-[fsw]\s+\d+))*\s*$/,
    /^grep\s+(?:(?:-[a-zA-Z]+|-[ABC](?:\s+)?\d+)\s+)*(?:'[^']*'|".*"|\S+)\s*$/,
    /^rg\s+(?:(?:-[a-zA-Z]+|-[ABC](?:\s+)?\d+)\s+)*(?:'[^']*'|".*"|\S+)\s*$/,
    /^pwd$/,
    /^whoami$/,
    /^id[^<>()$`]*$/,
    /^uname[^<>()$`]*$/,
    /^free[^<>()$`]*$/,
    /^df[^<>()$`]*$/,
    /^du[^<>()$`]*$/,
    /^ps(?!\s+.*-o)[^<>()$`]*$/,
    /^locale[^<>()$`]*$/,
    /^node -v$/,
    /^npm -v$/,
    /^npm list[^<>()$`]*$/,
    /^python --version$/,
    /^python3 --version$/,
    /^pip list[^<>()$`]*$/,
    /^docker ps[^<>()$`]*$/,
    /^docker images[^<>()$`]*$/,
    /^netstat(?!\s+.*-p)[^<>()$`]*$/,
    /^ip addr[^<>()$`]*$/,
    /^ifconfig[^<>()$`]*$/,
    /^man(?!\s+.*-P)(?!\s+.*--pager)[^<>()$`]*$/,
    /^info[^<>()$`]*$/,
    /^help[^<>()$`]*$/,
    /^sleep[^<>()$`]*$/,
    /^tree$/,
    /^which[^<>()$`]*$/,
    /^type[^<>()$`]*$/,
    /^history(?!\s+-c)[^<>()$`]*$/,
    /^alias$/,
    /^jq(?!\s+.*(?:-f\b|--from-file|--rawfile|--slurpfile|--run-tests))(?:\s+(?:-[a-zA-Z]+|--[a-zA-Z-]+(?:=\S+)?))*(?: +(?:'.*'|".*"|[^-\s][^\s]*))?\s*$/
];

/**
 * Command-specific exit code interpreters
 */
const EXIT_CODE_INTERPRETERS = new Map([
    ["grep", (code, stdout, stderr) => ({
        isError: code >= 2,
        message: code === 1 ? "No matches found" : undefined
    })],
    ["rg", (code, stdout, stderr) => ({
        isError: code >= 2,
        message: code === 1 ? "No matches found" : undefined
    })],
    ["find", (code, stdout, stderr) => ({
        isError: code >= 2,
        message: code === 1 ? "Some directories were inaccessible" : undefined
    })],
    ["diff", (code, stdout, stderr) => ({
        isError: code >= 2,
        message: code === 1 ? "Files differ" : undefined
    })],
    ["test", (code, stdout, stderr) => ({
        isError: code >= 2,
        message: code === 1 ? "Condition is false" : undefined
    })],
    ["[", (code, stdout, stderr) => ({
        isError: code >= 2,
        message: code === 1 ? "Condition is false" : undefined
    })]
]);

/**
 * Default exit code interpreter
 */
const defaultExitCodeInterpreter = (code, stdout, stderr) => ({
    isError: code !== 0,
    message: code !== 0 ? `Command failed with exit code ${code}` : undefined
});

/**
 * Gets the appropriate exit code interpreter for a command
 * @param {string} command - The command string
 * @returns {Function} Exit code interpreter function
 */
function getExitCodeInterpreter(command) {
    const commandName = getCommandName(command);
    const interpreter = EXIT_CODE_INTERPRETERS.get(commandName);
    return interpreter !== undefined ? interpreter : defaultExitCodeInterpreter;
}

/**
 * Extracts the command name from a command string
 * @param {string} command - The full command string
 * @returns {string} The command name
 */
function getCommandName(command) {
    return (command.split("|").pop()?.trim() || command).trim().split(/\s+/)[0] || "";
}

/**
 * Interprets command exit code and generates appropriate response
 * @param {string} command - The command that was executed
 * @param {number} exitCode - The exit code
 * @param {string} stdout - Standard output
 * @param {string} stderr - Standard error
 * @returns {Object} Interpretation result
 */
function interpretExitCode(command, exitCode, stdout, stderr) {
    const interpreter = getExitCodeInterpreter(command);
    const result = interpreter(exitCode, stdout, stderr);
    return {
        isError: result.isError,
        message: result.message
    };
}

/**
 * Checks if a command contains dangerous characters
 * @param {string} command - The command to check
 * @returns {boolean} True if command contains dangerous characters
 */
function containsDangerousCharacters(command) {
    const dangerousChars = ["`", "$(", "${", "~[", "(e:", "\n", "\r", ";", "|", "&", "||", "&&", ">", "<", ">>", ">&", ">&2", "<(", ">(", "$", "\\", "#"];
    return dangerousChars.some(char => command.includes(char));
}

/**
 * Tracks git operations for analytics
 * @param {string} command - The command that was executed
 * @param {number} exitCode - The exit code
 */
function trackGitOperations(command, exitCode) {
    if (exitCode !== 0) return;
    
    if (command.match(/^\s*git\s+commit\b/)) {
        // TODO: Implement analytics tracking
        console.log('Git commit operation tracked');
    } else if (command.match(/^\s*gh\s+pr\s+create\b/)) {
        // TODO: Implement analytics tracking
        console.log('GitHub PR create operation tracked');
    }
}

/**
 * Main Shell Tool implementation
 */
const shellTool = {
    name: "bash", // TODO: Replace with actual tool name constant
    
    async description({ description }) {
        return description || "Run shell command";
    },
    
    async prompt() {
        // TODO: Implement prompt generation
        return "Shell command prompt";
    },
    
    isConcurrencySafe(input) {
        return this.isReadOnly(input);
    },
    
    isReadOnly(input) {
        const { command } = input;
        const isSandboxed = ("sandbox" in input ? !!input.sandbox : false);
        
        if (isSandboxed) return true;
        
        // Check if all commands in the input are sandbox-safe
        const commands = command.split('|').map(cmd => cmd.trim());
        return commands.every(cmd => {
            if (containsDangerousCharacters(cmd)) {
                return false;
            }
            
            for (const pattern of SANDBOX_SAFE_PATTERNS) {
                if (pattern.test(cmd)) {
                    return true;
                }
            }
            return false;
        });
    },
    
    userFacingName(input) {
        if (!input) return "Bash";
        const isSandboxed = ("sandbox" in input ? !!input.sandbox : false);
        return isSandboxed ? "SandboxedBash" : "Bash";
    },
    
    isEnabled() {
        return true;
    },
    
    async checkPermissions(input, context) {
        const isSandboxed = ("sandbox" in input ? !!input.sandbox : false);
        if (isSandboxed) {
            return {
                behavior: "allow",
                updatedInput: input
            };
        }
        
        // TODO: Implement full permission checking
        return normalizePermissionDecision(checkPermissions(input, context.getToolPermissionContext(), null));
    },
    
    async validateInput(input, context) {
        // TODO: Implement input validation
        return { result: true };
    },
    
    renderToolUseMessage(input, { verbose }) {
        const { command } = input;
        if (!command) return null;
        
        let displayCommand = command;
        
        // Handle heredoc syntax
        if (command.includes(`"$(cat <<'EOF'`)) {
            const match = command.match(/^(.*?)"?\$\(cat <<'EOF'\n([\s\S]*?)\n\s*EOF\n\s*\)"(.*)$/);
            if (match && match[1] && match[2]) {
                const prefix = match[1];
                const content = match[2];
                const suffix = match[3] || "";
                displayCommand = `${prefix.trim()} "${content.trim()}"${suffix.trim()}`;
            }
        }
        
        if (!verbose) {
            const lines = displayCommand.split('\n');
            const tooManyLines = lines.length > MAX_COMMAND_LINES;
            const tooLong = displayCommand.length > MAX_COMMAND_LENGTH;
            
            if (tooManyLines || tooLong) {
                let truncated = displayCommand;
                if (tooManyLines) {
                    truncated = lines.slice(0, MAX_COMMAND_LINES).join('\n');
                }
                if (truncated.length > MAX_COMMAND_LENGTH) {
                    truncated = truncated.slice(0, MAX_COMMAND_LENGTH);
                }
                return React.createElement('span', null, truncated.trim(), "…");
            }
        }
        
        return displayCommand;
    },
    
    renderToolUseRejectedMessage() {
        return React.createElement('div', null, 'Command execution was rejected');
    },
    
    renderToolUseProgressMessage(progressMessages) {
        const lastMessage = progressMessages.at(-1);
        if (!lastMessage || !lastMessage.data || !lastMessage.data.output) {
            return React.createElement('div', { style: { height: '1em' } }, 
                React.createElement('span', { style: { color: 'gray' } }, "Running…")
            );
        }
        
        const data = lastMessage.data;
        // TODO: Implement progress rendering component
        return React.createElement('div', null, `Progress: ${data.output}`);
    },
    
    renderToolUseQueuedMessage() {
        return React.createElement('div', { style: { height: '1em' } },
            React.createElement('span', { style: { color: 'gray' } }, "Waiting…")
        );
    },
    
    renderToolResultMessage(result, context, { verbose }) {
        // TODO: Implement result rendering component
        return React.createElement('div', null, 'Command result');
    },
    
    mapToolResultToToolResultBlockParam({ interrupted, stdout, stderr, isImage }, toolUseId) {
        if (isImage) {
            const match = stdout.trim().match(/^data:([^;]+);base64,(.+)$/);
            if (match) {
                const mediaType = match[1];
                const data = match[2];
                return {
                    tool_use_id: toolUseId,
                    type: "tool_result",
                    content: [{
                        type: "image",
                        source: {
                            type: "base64",
                            media_type: mediaType || "image/jpeg",
                            data: data || ""
                        }
                    }]
                };
            }
        }
        
        let processedStdout = stdout;
        if (stdout) {
            processedStdout = stdout.replace(/^(\s*\n)+/, "");
            processedStdout = processedStdout.trimEnd();
        }
        
        let processedStderr = stderr.trim();
        if (interrupted) {
            if (stderr) {
                processedStderr += EOL;
            }
            processedStderr += "<e>Command was aborted before completion</e>";
        }
        
        return {
            tool_use_id: toolUseId,
            type: "tool_result",
            content: [processedStdout, processedStderr].filter(Boolean).join('\n'),
            is_error: interrupted
        };
    },
    
    async *call(input, context) {
        // TODO: Implement actual command execution
        // This is a placeholder that would need to integrate with the actual shell execution system
        yield {
            type: "progress",
            toolUseID: "bash-progress-1",
            data: {
                type: "bash_progress",
                output: "Command execution started...",
                elapsedTimeSeconds: 0,
                totalLines: 1
            }
        };
        
        // Simulate command execution
        const result = {
            stdout: "Command output",
            stderr: "",
            code: 0,
            interrupted: false
        };
        
        trackGitOperations(input.command, result.code);
        
        return result;
    }
};

module.exports = {
    shellTool,
    SANDBOX_SAFE_PATTERNS,
    EXIT_CODE_INTERPRETERS,
    getExitCodeInterpreter,
    getCommandName,
    interpretExitCode,
    containsDangerousCharacters,
    trackGitOperations
};
