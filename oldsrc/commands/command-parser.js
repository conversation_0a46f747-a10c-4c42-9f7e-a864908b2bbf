/**
 * Command Parser (F91)
 * 
 * Refactored version of the slash command parsing system from the original code.
 * Handles parsing of slash commands, MCP commands, and command validation.
 * 
 * Original function: F91() at line 337337
 */

// Command prefixes and patterns
const COMMAND_PREFIX = "/";
const MCP_MARKER = "(MCP)";

// Command types
const COMMAND_TYPES = {
    REGULAR: "regular",
    MCP: "mcp",
    CUSTOM: "custom"
};

/**
 * Parses a slash command input
 * Original function: F91(A) at line 337337
 * 
 * @param {string} input - Input string to parse
 * @returns {Object|null} Parsed command object or null if invalid
 */
function parseSlashCommand(input) {
    const trimmedInput = input.trim();
    
    // Check if input starts with command prefix
    if (!trimmedInput.startsWith(COMMAND_PREFIX)) {
        return null;
    }
    
    // Split into tokens
    const tokens = trimmedInput.slice(1).split(" ");
    
    // Must have at least one token (command name)
    if (!tokens[0]) {
        return null;
    }
    
    let commandName = tokens[0];
    let isMcp = false;
    let argsStartIndex = 1;
    
    // Check for MCP marker
    if (tokens.length > 1 && tokens[1] === MCP_MARKER) {
        commandName = `${commandName} ${MCP_MARKER}`;
        isMcp = true;
        argsStartIndex = 2;
    }
    
    // Extract arguments
    const args = tokens.slice(argsStartIndex).join(" ");
    
    return {
        commandName,
        args,
        isMcp,
        type: isMcp ? COMMAND_TYPES.MCP : 
              commandName.includes(":") ? COMMAND_TYPES.CUSTOM : 
              COMMAND_TYPES.REGULAR
    };
}

/**
 * Validates command syntax
 * @param {string} input - Command input to validate
 * @returns {Object} Validation result
 */
function validateCommandSyntax(input) {
    const errors = [];
    
    if (!input || typeof input !== 'string') {
        errors.push("Command input is required");
        return { isValid: false, errors };
    }
    
    const trimmed = input.trim();
    
    if (!trimmed.startsWith(COMMAND_PREFIX)) {
        errors.push(`Commands must start with '${COMMAND_PREFIX}'`);
    }
    
    if (trimmed.length === 1) {
        errors.push("Command name is required after '/'");
    }
    
    // Check for invalid characters
    const invalidChars = /[<>|&;`$(){}[\]]/;
    if (invalidChars.test(trimmed)) {
        errors.push("Command contains invalid characters");
    }
    
    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Extracts command name without prefix and MCP marker
 * @param {string} commandName - Full command name
 * @returns {string} Clean command name
 */
function extractCleanCommandName(commandName) {
    return commandName
        .replace(` ${MCP_MARKER}`, "")
        .trim();
}

/**
 * Checks if a command is an MCP command
 * @param {string} commandName - Command name to check
 * @returns {boolean} True if MCP command
 */
function isMcpCommand(commandName) {
    return commandName.includes(MCP_MARKER);
}

/**
 * Checks if a command is a custom command (contains colon)
 * @param {string} commandName - Command name to check
 * @returns {boolean} True if custom command
 */
function isCustomCommand(commandName) {
    return commandName.includes(":");
}

/**
 * Parses command arguments into structured format
 * @param {string} argsString - Arguments string
 * @returns {Object} Parsed arguments
 */
function parseCommandArguments(argsString) {
    if (!argsString || !argsString.trim()) {
        return {
            raw: "",
            tokens: [],
            flags: {},
            positional: []
        };
    }
    
    const tokens = argsString.trim().split(/\s+/);
    const flags = {};
    const positional = [];
    
    for (let i = 0; i < tokens.length; i++) {
        const token = tokens[i];
        
        if (token.startsWith("--")) {
            // Long flag
            const flagName = token.slice(2);
            if (i + 1 < tokens.length && !tokens[i + 1].startsWith("-")) {
                flags[flagName] = tokens[i + 1];
                i++; // Skip next token
            } else {
                flags[flagName] = true;
            }
        } else if (token.startsWith("-") && token.length > 1) {
            // Short flag(s)
            const flagChars = token.slice(1);
            for (const char of flagChars) {
                flags[char] = true;
            }
        } else {
            // Positional argument
            positional.push(token);
        }
    }
    
    return {
        raw: argsString,
        tokens,
        flags,
        positional
    };
}

/**
 * Formats command for display
 * @param {Object} parsedCommand - Parsed command object
 * @returns {string} Formatted command string
 */
function formatCommandForDisplay(parsedCommand) {
    if (!parsedCommand) return "";
    
    let display = `/${parsedCommand.commandName}`;
    
    if (parsedCommand.args) {
        display += ` ${parsedCommand.args}`;
    }
    
    return display;
}

/**
 * Gets command type description
 * @param {string} type - Command type
 * @returns {string} Type description
 */
function getCommandTypeDescription(type) {
    switch (type) {
        case COMMAND_TYPES.MCP:
            return "MCP Server Command";
        case COMMAND_TYPES.CUSTOM:
            return "Custom Command";
        case COMMAND_TYPES.REGULAR:
            return "Built-in Command";
        default:
            return "Unknown Command";
    }
}

/**
 * Creates command help text
 * @param {Object} command - Command object
 * @returns {string} Help text
 */
function createCommandHelp(command) {
    if (!command) return "No command specified";
    
    const lines = [];
    lines.push(`Command: ${formatCommandForDisplay(command)}`);
    lines.push(`Type: ${getCommandTypeDescription(command.type)}`);
    
    if (command.args) {
        const parsedArgs = parseCommandArguments(command.args);
        if (parsedArgs.positional.length > 0) {
            lines.push(`Arguments: ${parsedArgs.positional.join(", ")}`);
        }
        if (Object.keys(parsedArgs.flags).length > 0) {
            lines.push(`Flags: ${Object.entries(parsedArgs.flags)
                .map(([key, value]) => `--${key}${value === true ? "" : `=${value}`}`)
                .join(", ")}`);
        }
    }
    
    return lines.join("\n");
}

/**
 * Normalizes command name for comparison
 * @param {string} commandName - Command name to normalize
 * @returns {string} Normalized command name
 */
function normalizeCommandName(commandName) {
    return extractCleanCommandName(commandName).toLowerCase().trim();
}

/**
 * Checks if two command names are equivalent
 * @param {string} name1 - First command name
 * @param {string} name2 - Second command name
 * @returns {boolean} True if equivalent
 */
function areCommandNamesEquivalent(name1, name2) {
    return normalizeCommandName(name1) === normalizeCommandName(name2);
}

/**
 * Extracts command suggestions from error
 * @param {string} input - Invalid command input
 * @param {Array} availableCommands - Available command names
 * @returns {Array} Suggested commands
 */
function suggestCommands(input, availableCommands) {
    const parsed = parseSlashCommand(input);
    if (!parsed) return [];
    
    const targetName = normalizeCommandName(parsed.commandName);
    
    return availableCommands
        .filter(cmd => {
            const cmdName = normalizeCommandName(cmd);
            return cmdName.includes(targetName) || targetName.includes(cmdName);
        })
        .slice(0, 5); // Limit to 5 suggestions
}

module.exports = {
    parseSlashCommand,
    validateCommandSyntax,
    extractCleanCommandName,
    isMcpCommand,
    isCustomCommand,
    parseCommandArguments,
    formatCommandForDisplay,
    getCommandTypeDescription,
    createCommandHelp,
    normalizeCommandName,
    areCommandNamesEquivalent,
    suggestCommands,
    COMMAND_PREFIX,
    MCP_MARKER,
    COMMAND_TYPES
};
