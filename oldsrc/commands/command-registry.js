/**
 * Command Registry (W91)
 * 
 * Refactored version of the command registration and lookup system.
 * Handles command discovery, validation, and execution routing.
 * 
 * Original function: W91() at line 343107
 */

const { normalizeCommandName, areCommandNamesEquivalent } = require('./command-parser');

// Built-in commands registry
const BUILT_IN_COMMANDS = new Map();

// MCP commands registry
const MCP_COMMANDS = new Map();

// Custom commands registry
const CUSTOM_COMMANDS = new Map();

/**
 * Finds a command by name or alias
 * Original function: W91(A, B) at line 343107
 * 
 * @param {string} commandName - Command name to find
 * @param {Array} commands - Array of command objects
 * @returns {Object} Found command object
 * @throws {ReferenceError} If command not found
 */
function findCommand(commandName, commands) {
    const normalizedName = normalizeCommandName(commandName);
    
    const command = commands.find(cmd => {
        const cmdName = normalizeCommandName(cmd.userFacingName());
        if (areCommandNamesEquivalent(cmdName, normalizedName)) {
            return true;
        }
        
        // Check aliases
        if (cmd.aliases) {
            return cmd.aliases.some(alias => 
                areCommandNamesEquivalent(normalizeCommandName(alias), normalizedName)
            );
        }
        
        return false;
    });
    
    if (!command) {
        const availableCommands = commands.map(cmd => {
            const name = cmd.userFacingName();
            return cmd.aliases ? `${name} (aliases: ${cmd.aliases.join(", ")})` : name;
        });
        
        throw new ReferenceError(
            `Command ${commandName} not found. Available commands: ${availableCommands.join(", ")}`
        );
    }
    
    return command;
}

/**
 * Checks if a command exists
 * Original function: Y91() at line 343104
 * 
 * @param {string} commandName - Command name to check
 * @param {Array} commands - Array of command objects
 * @returns {boolean} True if command exists
 */
function commandExists(commandName, commands) {
    try {
        findCommand(commandName, commands);
        return true;
    } catch (error) {
        return false;
    }
}

/**
 * Registers a built-in command
 * @param {Object} command - Command object to register
 * @returns {boolean} True if registration successful
 */
function registerBuiltInCommand(command) {
    const validation = validateCommand(command);
    if (!validation.isValid) {
        console.error('Command validation failed:', validation.errors);
        return false;
    }
    
    const name = normalizeCommandName(command.userFacingName());
    
    if (BUILT_IN_COMMANDS.has(name)) {
        console.error(`Built-in command '${name}' already exists`);
        return false;
    }
    
    BUILT_IN_COMMANDS.set(name, command);
    return true;
}

/**
 * Registers an MCP command
 * @param {Object} command - MCP command object to register
 * @returns {boolean} True if registration successful
 */
function registerMcpCommand(command) {
    const validation = validateCommand(command);
    if (!validation.isValid) {
        console.error('MCP command validation failed:', validation.errors);
        return false;
    }
    
    const name = normalizeCommandName(command.userFacingName());
    MCP_COMMANDS.set(name, command);
    return true;
}

/**
 * Registers a custom command
 * @param {Object} command - Custom command object to register
 * @returns {boolean} True if registration successful
 */
function registerCustomCommand(command) {
    const validation = validateCommand(command);
    if (!validation.isValid) {
        console.error('Custom command validation failed:', validation.errors);
        return false;
    }
    
    const name = normalizeCommandName(command.userFacingName());
    CUSTOM_COMMANDS.set(name, command);
    return true;
}

/**
 * Gets all registered commands
 * @returns {Array} Array of all command objects
 */
function getAllCommands() {
    return [
        ...Array.from(BUILT_IN_COMMANDS.values()),
        ...Array.from(MCP_COMMANDS.values()),
        ...Array.from(CUSTOM_COMMANDS.values())
    ];
}

/**
 * Gets commands by type
 * @param {string} type - Command type ('builtin', 'mcp', 'custom')
 * @returns {Array} Array of commands of specified type
 */
function getCommandsByType(type) {
    switch (type.toLowerCase()) {
        case 'builtin':
        case 'built-in':
            return Array.from(BUILT_IN_COMMANDS.values());
        case 'mcp':
            return Array.from(MCP_COMMANDS.values());
        case 'custom':
            return Array.from(CUSTOM_COMMANDS.values());
        default:
            return [];
    }
}

/**
 * Validates a command object
 * @param {Object} command - Command to validate
 * @returns {Object} Validation result
 */
function validateCommand(command) {
    const errors = [];
    
    if (!command) {
        errors.push('Command object is required');
        return { isValid: false, errors };
    }
    
    if (!command.userFacingName || typeof command.userFacingName !== 'function') {
        errors.push('Command must have a userFacingName() method');
    }
    
    if (!command.description || typeof command.description !== 'function') {
        errors.push('Command must have a description() method');
    }
    
    if (!command.execute || typeof command.execute !== 'function') {
        errors.push('Command must have an execute() method');
    }
    
    // Validate aliases if present
    if (command.aliases) {
        if (!Array.isArray(command.aliases)) {
            errors.push('Command aliases must be an array');
        } else {
            for (const alias of command.aliases) {
                if (typeof alias !== 'string') {
                    errors.push('All command aliases must be strings');
                    break;
                }
            }
        }
    }
    
    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Unregisters a command
 * @param {string} commandName - Name of command to unregister
 * @param {string} type - Command type ('builtin', 'mcp', 'custom', 'all')
 * @returns {boolean} True if command was unregistered
 */
function unregisterCommand(commandName, type = 'all') {
    const normalizedName = normalizeCommandName(commandName);
    let removed = false;
    
    if (type === 'all' || type === 'builtin') {
        if (BUILT_IN_COMMANDS.delete(normalizedName)) {
            removed = true;
        }
    }
    
    if (type === 'all' || type === 'mcp') {
        if (MCP_COMMANDS.delete(normalizedName)) {
            removed = true;
        }
    }
    
    if (type === 'all' || type === 'custom') {
        if (CUSTOM_COMMANDS.delete(normalizedName)) {
            removed = true;
        }
    }
    
    return removed;
}

/**
 * Gets command statistics
 * @returns {Object} Command statistics
 */
function getCommandStatistics() {
    return {
        builtIn: BUILT_IN_COMMANDS.size,
        mcp: MCP_COMMANDS.size,
        custom: CUSTOM_COMMANDS.size,
        total: BUILT_IN_COMMANDS.size + MCP_COMMANDS.size + CUSTOM_COMMANDS.size
    };
}

/**
 * Clears all commands of a specific type
 * @param {string} type - Command type to clear
 * @returns {number} Number of commands cleared
 */
function clearCommands(type) {
    let cleared = 0;
    
    switch (type.toLowerCase()) {
        case 'builtin':
        case 'built-in':
            cleared = BUILT_IN_COMMANDS.size;
            BUILT_IN_COMMANDS.clear();
            break;
        case 'mcp':
            cleared = MCP_COMMANDS.size;
            MCP_COMMANDS.clear();
            break;
        case 'custom':
            cleared = CUSTOM_COMMANDS.size;
            CUSTOM_COMMANDS.clear();
            break;
        case 'all':
            cleared = BUILT_IN_COMMANDS.size + MCP_COMMANDS.size + CUSTOM_COMMANDS.size;
            BUILT_IN_COMMANDS.clear();
            MCP_COMMANDS.clear();
            CUSTOM_COMMANDS.clear();
            break;
        default:
            return 0;
    }
    
    return cleared;
}

/**
 * Finds commands matching a pattern
 * @param {string|RegExp} pattern - Pattern to match against
 * @returns {Array} Array of matching commands
 */
function findCommandsMatching(pattern) {
    const allCommands = getAllCommands();
    
    if (typeof pattern === 'string') {
        const lowerPattern = pattern.toLowerCase();
        return allCommands.filter(cmd => {
            const name = cmd.userFacingName().toLowerCase();
            return name.includes(lowerPattern) || 
                   (cmd.aliases && cmd.aliases.some(alias => alias.toLowerCase().includes(lowerPattern)));
        });
    }
    
    if (pattern instanceof RegExp) {
        return allCommands.filter(cmd => {
            const name = cmd.userFacingName();
            return pattern.test(name) || 
                   (cmd.aliases && cmd.aliases.some(alias => pattern.test(alias)));
        });
    }
    
    return [];
}

/**
 * Gets command by exact name
 * @param {string} commandName - Exact command name
 * @returns {Object|null} Command object or null if not found
 */
function getCommandByName(commandName) {
    const normalizedName = normalizeCommandName(commandName);
    
    return BUILT_IN_COMMANDS.get(normalizedName) ||
           MCP_COMMANDS.get(normalizedName) ||
           CUSTOM_COMMANDS.get(normalizedName) ||
           null;
}

/**
 * Creates a command execution context
 * @param {Object} options - Context options
 * @returns {Object} Command execution context
 */
function createCommandContext(options = {}) {
    return {
        findCommand: (name) => findCommand(name, getAllCommands()),
        commandExists: (name) => commandExists(name, getAllCommands()),
        getAllCommands,
        getCommandsByType,
        getCommandStatistics,
        ...options
    };
}

module.exports = {
    findCommand,
    commandExists,
    registerBuiltInCommand,
    registerMcpCommand,
    registerCustomCommand,
    getAllCommands,
    getCommandsByType,
    validateCommand,
    unregisterCommand,
    getCommandStatistics,
    clearCommands,
    findCommandsMatching,
    getCommandByName,
    createCommandContext
};
