/**
 * Command Executor
 * 
 * Handles the execution of parsed commands with proper context,
 * error handling, and result processing.
 */

const { parseSlashCommand, parseCommandArguments } = require('./command-parser');
const { findCommand, getAllCommands } = require('./command-registry');
const { createUserMessage, createSystemMessage } = require('../core/message-processor');

// Execution result types
const EXECUTION_RESULTS = {
    SUCCESS: "success",
    ERROR: "error",
    NOT_FOUND: "not_found",
    PERMISSION_DENIED: "permission_denied",
    INVALID_ARGS: "invalid_args"
};

/**
 * Executes a slash command
 * @param {string} commandName - Command name
 * @param {string} args - Command arguments
 * @param {Function} setToolJSX - Tool JSX setter
 * @param {Object} toolUseContext - Tool use context
 * @param {Array} imageContent - Image content array
 * @returns {Promise<Object>} Execution result
 */
async function executeCommand(commandName, args, setToolJSX, toolUseContext, imageContent = []) {
    try {
        // Get all available commands
        const availableCommands = getAllCommands();
        
        // Find the command
        let command;
        try {
            command = findCommand(commandName, availableCommands);
        } catch (error) {
            return {
                messages: [
                    createUserMessage({ content: `/${commandName} ${args}`.trim() }),
                    createUserMessage({ content: `Unknown command: ${commandName}` })
                ],
                shouldQuery: false,
                result: EXECUTION_RESULTS.NOT_FOUND
            };
        }
        
        // Validate command permissions
        const permissionCheck = await validateCommandPermissions(command, toolUseContext);
        if (!permissionCheck.allowed) {
            return {
                messages: [
                    createUserMessage({ content: `/${commandName} ${args}`.trim() }),
                    createUserMessage({ content: permissionCheck.message })
                ],
                shouldQuery: false,
                result: EXECUTION_RESULTS.PERMISSION_DENIED
            };
        }
        
        // Parse arguments
        const parsedArgs = parseCommandArguments(args);
        
        // Validate arguments
        const argValidation = await validateCommandArguments(command, parsedArgs);
        if (!argValidation.valid) {
            return {
                messages: [
                    createUserMessage({ content: `/${commandName} ${args}`.trim() }),
                    createUserMessage({ content: argValidation.message })
                ],
                shouldQuery: false,
                result: EXECUTION_RESULTS.INVALID_ARGS
            };
        }
        
        // Create execution context
        const executionContext = createExecutionContext({
            command,
            args: parsedArgs,
            setToolJSX,
            toolUseContext,
            imageContent
        });
        
        // Execute the command
        const result = await command.execute(executionContext);
        
        // Process execution result
        return processExecutionResult(result, commandName, args);
        
    } catch (error) {
        console.error('Command execution error:', error);
        
        return {
            messages: [
                createUserMessage({ content: `/${commandName} ${args}`.trim() }),
                createUserMessage({ content: `Command execution failed: ${error.message}` })
            ],
            shouldQuery: false,
            result: EXECUTION_RESULTS.ERROR,
            error
        };
    }
}

/**
 * Validates command permissions
 * @param {Object} command - Command object
 * @param {Object} toolUseContext - Tool use context
 * @returns {Promise<Object>} Permission validation result
 */
async function validateCommandPermissions(command, toolUseContext) {
    // Check if command has permission requirements
    if (command.requiresPermission && typeof command.requiresPermission === 'function') {
        const permissionRequired = await command.requiresPermission(toolUseContext);
        
        if (permissionRequired) {
            const hasPermission = await checkCommandPermission(command, toolUseContext);
            
            if (!hasPermission) {
                return {
                    allowed: false,
                    message: `Permission denied for command '${command.userFacingName()}'. This command requires elevated privileges.`
                };
            }
        }
    }
    
    // Check tool permission context
    const permissionContext = toolUseContext.getToolPermissionContext();
    if (permissionContext && permissionContext.commandRestrictions) {
        const commandName = command.userFacingName();
        
        if (permissionContext.commandRestrictions.denied?.includes(commandName)) {
            return {
                allowed: false,
                message: `Command '${commandName}' is not allowed in the current context.`
            };
        }
        
        if (permissionContext.commandRestrictions.allowed && 
            !permissionContext.commandRestrictions.allowed.includes(commandName)) {
            return {
                allowed: false,
                message: `Command '${commandName}' is not in the allowed commands list.`
            };
        }
    }
    
    return { allowed: true };
}

/**
 * Validates command arguments
 * @param {Object} command - Command object
 * @param {Object} parsedArgs - Parsed arguments
 * @returns {Promise<Object>} Argument validation result
 */
async function validateCommandArguments(command, parsedArgs) {
    // Check if command has argument validation
    if (command.validateArgs && typeof command.validateArgs === 'function') {
        try {
            const validation = await command.validateArgs(parsedArgs);
            
            if (!validation.valid) {
                return {
                    valid: false,
                    message: validation.message || 'Invalid command arguments'
                };
            }
        } catch (error) {
            return {
                valid: false,
                message: `Argument validation failed: ${error.message}`
            };
        }
    }
    
    // Check required arguments
    if (command.requiredArgs && Array.isArray(command.requiredArgs)) {
        for (const requiredArg of command.requiredArgs) {
            if (!parsedArgs.positional.includes(requiredArg) && 
                !parsedArgs.flags.hasOwnProperty(requiredArg)) {
                return {
                    valid: false,
                    message: `Missing required argument: ${requiredArg}`
                };
            }
        }
    }
    
    return { valid: true };
}

/**
 * Creates command execution context
 * @param {Object} options - Context options
 * @returns {Object} Execution context
 */
function createExecutionContext({ command, args, setToolJSX, toolUseContext, imageContent }) {
    return {
        command,
        args,
        setToolJSX,
        toolUseContext,
        imageContent,
        
        // Helper methods
        createUserMessage,
        createSystemMessage,
        
        // Context accessors
        getToolPermissionContext: () => toolUseContext.getToolPermissionContext(),
        getOptions: () => toolUseContext.options,
        getAbortController: () => toolUseContext.abortController,
        
        // Utility methods
        trackEvent: (eventName, data) => {
            // TODO: Implement event tracking
            console.log(`Event: ${eventName}`, data);
        },
        
        log: (message, level = 'info') => {
            console.log(`[${level.toUpperCase()}] ${message}`);
        }
    };
}

/**
 * Processes command execution result
 * @param {Object} result - Raw execution result
 * @param {string} commandName - Command name
 * @param {string} args - Command arguments
 * @returns {Object} Processed result
 */
function processExecutionResult(result, commandName, args) {
    // Ensure result has required properties
    const processedResult = {
        messages: result.messages || [],
        shouldQuery: result.shouldQuery !== undefined ? result.shouldQuery : false,
        allowedTools: result.allowedTools || [],
        skipHistory: result.skipHistory || false,
        maxThinkingTokens: result.maxThinkingTokens,
        result: EXECUTION_RESULTS.SUCCESS,
        ...result
    };
    
    // Add command input message if not present
    const commandInput = `/${commandName} ${args}`.trim();
    const hasInputMessage = processedResult.messages.some(msg => 
        msg.type === 'user' && msg.message.content === commandInput
    );
    
    if (!hasInputMessage) {
        processedResult.messages.unshift(createUserMessage({ content: commandInput }));
    }
    
    return processedResult;
}

/**
 * Checks if a command is available
 * @param {string} commandName - Command name to check
 * @param {Array} commands - Available commands
 * @returns {boolean} True if command is available
 */
function isCommandAvailable(commandName, commands) {
    try {
        findCommand(commandName, commands);
        return true;
    } catch (error) {
        return false;
    }
}

/**
 * Gets command help information
 * @param {string} commandName - Command name
 * @returns {Promise<Object>} Help information
 */
async function getCommandHelp(commandName) {
    try {
        const availableCommands = getAllCommands();
        const command = findCommand(commandName, availableCommands);
        
        const help = {
            name: command.userFacingName(),
            description: await command.description(),
            aliases: command.aliases || [],
            usage: command.usage || `/${command.userFacingName()} [args]`,
            examples: command.examples || []
        };
        
        if (command.getHelp && typeof command.getHelp === 'function') {
            const customHelp = await command.getHelp();
            Object.assign(help, customHelp);
        }
        
        return help;
        
    } catch (error) {
        return {
            error: `Command '${commandName}' not found`,
            availableCommands: getAllCommands().map(cmd => cmd.userFacingName())
        };
    }
}

/**
 * Lists all available commands
 * @param {Object} options - Listing options
 * @returns {Promise<Array>} Array of command information
 */
async function listCommands(options = {}) {
    const commands = getAllCommands();
    const commandList = [];
    
    for (const command of commands) {
        const info = {
            name: command.userFacingName(),
            description: await command.description(),
            aliases: command.aliases || []
        };
        
        if (options.includeDetails) {
            info.usage = command.usage || `/${command.userFacingName()} [args]`;
            info.category = command.category || 'General';
        }
        
        commandList.push(info);
    }
    
    // Sort by name
    commandList.sort((a, b) => a.name.localeCompare(b.name));
    
    return commandList;
}

// Utility function for permission checking (TODO: Implement)
async function checkCommandPermission(command, toolUseContext) {
    // TODO: Implement actual permission checking logic
    return true;
}

module.exports = {
    executeCommand,
    isCommandAvailable,
    getCommandHelp,
    listCommands,
    validateCommandPermissions,
    validateCommandArguments,
    createExecutionContext,
    processExecutionResult,
    EXECUTION_RESULTS
};
