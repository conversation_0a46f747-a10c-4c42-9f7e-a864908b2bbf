/**
 * Release Notes Command
 * 
 * Fetches and displays release notes from the project's changelog.
 * Supports version comparison and caching for better performance.
 */

const semver = require('semver');
const axios = require('axios');

// Configuration constants
const MAX_RELEASE_NOTES = 5;
const CHANGELOG_URL = "https://github.com/anthropics/claude-code/blob/main/CHANGELOG.md";
const CHANGELOG_RAW_URL = "https://raw.githubusercontent.com/anthropics/claude-code/refs/heads/main/CHANGELOG.md";

// Default configuration
const DEFAULT_CONFIG = {
    ISSUES_EXPLAINER: "report the issue at https://github.com/anthropics/claude-code/issues",
    PACKAGE_URL: "@anthropic-ai/claude-code", 
    README_URL: "https://docs.anthropic.com/s/claude-code",
    VERSION: "1.0.53"
};

/**
 * Fetches the latest changelog from the remote repository
 * @returns {Promise<void>}
 */
async function fetchChangelog() {
    try {
        const response = await axios.get(CHANGELOG_RAW_URL);
        if (response.status === 200) {
            const config = getStoredConfig();
            saveConfig({
                ...config,
                cachedChangelog: response.data,
                changelogLastFetched: Date.now()
            });
        }
    } catch (error) {
        console.error('Failed to fetch changelog:', error);
    }
}

/**
 * Gets the cached changelog content
 * @returns {string} The cached changelog or empty string
 */
function getCachedChangelog() {
    return getStoredConfig().cachedChangelog ?? "";
}

/**
 * Parses changelog content into structured release notes
 * @param {string} changelogContent - Raw changelog content
 * @returns {Object} Parsed release notes by version
 */
function parseChangelog(changelogContent) {
    try {
        if (!changelogContent) return {};
        
        const releaseNotes = {};
        const sections = changelogContent.split(/^## /gm).slice(1);
        
        for (const section of sections) {
            const lines = section.trim().split('\n');
            if (lines.length === 0) continue;
            
            const header = lines[0];
            if (!header) continue;
            
            const version = header.split(' - ')[0]?.trim() || "";
            if (!version) continue;
            
            const notes = lines.slice(1)
                .filter(line => line.trim().startsWith('- '))
                .map(line => line.trim().substring(2).trim())
                .filter(Boolean);
                
            if (notes.length > 0) {
                releaseNotes[version] = notes;
            }
        }
        
        return releaseNotes;
    } catch (error) {
        console.error('Failed to parse changelog:', error);
        return {};
    }
}

/**
 * Gets release notes between two versions
 * @param {string} currentVersion - Current version
 * @param {string} previousVersion - Previous version (optional)
 * @param {string} changelogContent - Changelog content (optional)
 * @returns {Array} Array of release notes
 */
function getReleaseNotesBetweenVersions(currentVersion, previousVersion, changelogContent = getCachedChangelog()) {
    try {
        const parsedNotes = parseChangelog(changelogContent);
        const current = semver.coerce(currentVersion);
        const previous = previousVersion ? semver.coerce(previousVersion) : null;
        
        if (!previous || (current && semver.gt(current, previous, { loose: true }))) {
            return Object.entries(parsedNotes)
                .filter(([version]) => !previous || semver.gt(version, previous, { loose: true }))
                .sort(([a], [b]) => semver.gt(a, b, { loose: true }) ? -1 : 1)
                .flatMap(([version, notes]) => notes)
                .filter(Boolean)
                .slice(0, MAX_RELEASE_NOTES);
        }
    } catch (error) {
        console.error('Failed to get release notes:', error);
        return [];
    }
    return [];
}

/**
 * Gets all release notes sorted by version
 * @param {string} changelogContent - Changelog content (optional)
 * @returns {Array} Array of [version, notes] tuples
 */
function getAllReleaseNotes(changelogContent = getCachedChangelog()) {
    try {
        const parsedNotes = parseChangelog(changelogContent);
        return Object.keys(parsedNotes)
            .sort((a, b) => semver.gt(a, b, { loose: true }) ? 1 : -1)
            .map(version => {
                const notes = parsedNotes[version];
                if (!notes || notes.length === 0) return null;
                
                const filteredNotes = notes.filter(Boolean);
                if (filteredNotes.length === 0) return null;
                
                return [version, filteredNotes];
            })
            .filter(item => item !== null);
    } catch (error) {
        console.error('Failed to get release notes:', error);
        return [];
    }
}

/**
 * Checks for release notes and returns formatted result
 * @param {string} currentVersion - Current version
 * @param {string} configVersion - Version from config
 * @returns {Object} Release notes information
 */
function checkReleaseNotes(currentVersion, configVersion = DEFAULT_CONFIG.VERSION) {
    if (currentVersion !== configVersion || !getCachedChangelog()) {
        fetchChangelog().catch(error => 
            console.error('Failed to fetch changelog:', error)
        );
    }
    
    const releaseNotes = getReleaseNotesBetweenVersions(configVersion, currentVersion);
    return {
        hasReleaseNotes: releaseNotes.length > 0,
        releaseNotes
    };
}

/**
 * Formats release notes for display
 * @param {Array} releaseNotesArray - Array of [version, notes] tuples
 * @returns {string} Formatted release notes
 */
function formatReleaseNotes(releaseNotesArray) {
    return releaseNotesArray.map(([version, notes]) => {
        const header = `Version ${version}:`;
        const notesList = notes.map(note => `• ${note}`).join('\n');
        return `${header}\n${notesList}`;
    }).join('\n\n');
}

/**
 * Release Notes command configuration
 */
const releaseNotesCommand = {
    description: "View release notes",
    isEnabled: () => true,
    isHidden: false,
    name: "release-notes",
    type: "local",
    
    userFacingName() {
        return "release-notes";
    },
    
    async call() {
        let releaseNotes = [];
        
        try {
            // Try to fetch latest changelog with timeout
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error("Timeout")), 500);
            });
            
            await Promise.race([fetchChangelog(), timeoutPromise]);
            releaseNotes = getAllReleaseNotes(getCachedChangelog());
        } catch {
            // Ignore timeout/fetch errors, use cached data
        }
        
        if (releaseNotes.length > 0) {
            return formatReleaseNotes(releaseNotes);
        }
        
        // Fallback to cached data
        const cachedNotes = getAllReleaseNotes();
        if (cachedNotes.length > 0) {
            return formatReleaseNotes(cachedNotes);
        }
        
        return `See the full changelog at: ${CHANGELOG_URL}`;
    }
};

// Placeholder functions that need to be implemented based on the storage system
function getStoredConfig() {
    // TODO: Implement based on actual storage mechanism
    return {};
}

function saveConfig(config) {
    // TODO: Implement based on actual storage mechanism
}

module.exports = {
    releaseNotesCommand,
    fetchChangelog,
    parseChangelog,
    getReleaseNotesBetweenVersions,
    getAllReleaseNotes,
    checkReleaseNotes,
    formatReleaseNotes
};
