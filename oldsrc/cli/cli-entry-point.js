/**
 * CLI Entry Point (rF4)
 * 
 * Refactored version of the main CLI entry point from the original code.
 * This is the true starting point when users run the `claude` command.
 * 
 * Original function: rF4() at line 354501
 */

const { program, createCommand, createOption } = require('commander');
const { setupSignalHandlers, setupEnvironment } = require('../core/environment-setup');
const { initializeApplication } = require('../app/application-initializer');
const { processNonInteractive } = require('../agents/non-interactive-processor');
const { renderMainApplication } = require('../app/main-application-renderer');
const { validateSessionId, isSessionInUse } = require('../utils/session-utils');
const { trackEvent, logError } = require('../utils/analytics');
const { parsePermissionMode } = require('../core/permission-manager');
const { parseMcpConfig, initializeMcpClients } = require('../mcp/mcp-manager');
const { performLoginFlow } = require('../auth/login-flow');
const { chalk } = require('../utils/colors');

/**
 * Main CLI Entry Point
 * 
 * This is the function called when users run `claude` command.
 * It handles:
 * 1. Special modes (ripgrep)
 * 2. Environment setup
 * 3. Signal handling
 * 4. Command line parsing
 * 5. Application initialization
 * 
 * @returns {Promise<void>}
 */
async function cliEntryPoint() {
    // Handle special ripgrep mode
    if (process.argv[2] === "--ripgrep") {
        const args = process.argv.slice(3);
        process.exit(executeRipgrep(args)); // TODO: Implement NX2()
    }
    
    // Setup environment
    setupEnvironment();
    setupSignalHandlers();
    
    // Parse command line arguments
    const args = process.argv.slice(2);
    const isPrintMode = args.includes("-p") || args.includes("--print") || !process.stdout.isTTY;
    
    // Setup analytics and telemetry
    setupAnalytics(isPrintMode);
    
    // Determine entrypoint type
    const entrypoint = determineEntrypoint();
    setEntrypoint(entrypoint);
    
    // Initialize core systems
    const shellSnapshot = initializeShellSnapshot();
    if (shellSnapshot instanceof Promise) {
        await shellSnapshot;
    }
    
    // Setup cleanup handlers
    setupCleanupHandlers();
    
    // Set process title
    process.title = "claude";
    
    // Start main application
    await initializeMainApplication();
}

/**
 * Initialize Main Application
 * 
 * Sets up Commander.js and handles all command line options and subcommands.
 */
async function initializeMainApplication() {
    setupPreflightChecks();
    
    const program = createMainProgram();
    
    // Add main command with all options
    setupMainCommand(program);
    
    // Add subcommands
    setupConfigCommand(program);
    setupMcpCommand(program);
    
    // Parse arguments and execute
    await program.parseAsync(process.argv);
    
    return program;
}

/**
 * Create Main Program
 * 
 * @returns {Command} Commander.js program instance
 */
function createMainProgram() {
    const program = new Command();
    
    program
        .name("claude")
        .description(`${getApplicationName()} - starts an interactive session by default, use -p/--print for non-interactive output`)
        .argument("[prompt]", "Your prompt", String)
        .helpOption("-h, --help", "Display help for command")
        .version(getVersionString(), "-v, --version", "Output the version number");
    
    return program;
}

/**
 * Setup Main Command Options
 * 
 * @param {Command} program - Commander.js program instance
 */
function setupMainCommand(program) {
    program
        // Debug and verbose options
        .option("-d, --debug", "Enable debug mode", () => true)
        .option("--verbose", "Override verbose mode setting from config", () => true)
        
        // Output options
        .option("-p, --print", "Print response and exit (useful for pipes)", () => true)
        .addOption(createOption("--output-format <format>", 
            'Output format (only works with --print): "text" (default), "json" (single result), or "stream-json" (realtime streaming)')
            .choices(["text", "json", "stream-json"]))
        .addOption(createOption("--input-format <format>",
            'Input format (only works with --print): "text" (default), or "stream-json" (realtime streaming input)')
            .choices(["text", "stream-json"]))
        
        // Security options
        .option("--dangerously-skip-permissions", "Bypass all permission checks. Recommended only for sandboxes with no internet access.", () => true)
        .option("--allowedTools <tools...>", 'Comma or space-separated list of tool names to allow (e.g. "Bash(git:*) Edit")')
        .option("--disallowedTools <tools...>", 'Comma or space-separated list of tool names to deny (e.g. "Bash(git:*) Edit")')
        .addOption(createOption("--permission-mode <mode>", "Permission mode to use for the session")
            .argParser(String)
            .choices(getPermissionModes())) // TODO: Implement QD1
        
        // MCP options
        .option("--mcp-config <file or string>", "Load MCP servers from a JSON file or string")
        .option("--strict-mcp-config", "Only use MCP servers from --mcp-config, ignoring all other MCP configurations", () => true)
        .option("--mcp-debug", "[DEPRECATED. Use --debug instead] Enable MCP debug mode (shows MCP server errors)", () => true)
        
        // Session options
        .option("-c, --continue", "Continue the most recent conversation", () => true)
        .option("-r, --resume [sessionId]", "Resume a conversation - provide a session ID or interactively select a conversation to resume", (value) => value || true)
        .option("--session-id <uuid>", "Use a specific session ID for the conversation (must be a valid UUID)")
        
        // Model options
        .option("--model <model>", "Model for the current session. Provide an alias for the latest model (e.g. 'sonnet' or 'opus') or a model's full name (e.g. 'claude-sonnet-4-20250514').")
        .option("--fallback-model <model>", "Enable automatic fallback to specified model when default model is overloaded (only works with --print)")
        
        // Advanced options
        .addOption(createOption("--max-turns <turns>", "Maximum number of agentic turns in non-interactive mode. This will early exit the conversation after the specified number of turns. (only works with --print)")
            .argParser(Number)
            .hideHelp())
        .addOption(createOption("--permission-prompt-tool <tool>", "MCP tool to use for permission prompts (only works with --print)")
            .argParser(String)
            .hideHelp())
        .addOption(createOption("--system-prompt <prompt>", "System prompt to use for the session  (only works with --print)")
            .argParser(String)
            .hideHelp())
        .addOption(createOption("--append-system-prompt <prompt>", "Append a system prompt to the default system prompt")
            .argParser(String))
        
        // Directory options
        .option("--add-dir <directories...>", "Additional directories to allow tool access to")
        .option("--ide", "Automatically connect to IDE on startup if exactly one valid IDE is available", () => true)
        
        // Main action handler
        .action(handleMainCommand);
}

/**
 * Handle Main Command Execution
 * 
 * @param {string} prompt - Initial prompt
 * @param {Object} options - Command line options
 */
async function handleMainCommand(prompt, options) {
    try {
        // Extract and validate options
        const {
            debug = false,
            verbose = false,
            print: isPrintMode,
            dangerouslySkipPermissions,
            allowedTools = [],
            disallowedTools = [],
            mcpConfig,
            outputFormat,
            inputFormat,
            permissionMode,
            addDir = [],
            fallbackModel,
            ide: autoConnectIde = false,
            sessionId
        } = options;
        
        const strictMcpConfig = options.strictMcpConfig || false;
        
        // Validate session ID if provided
        if (sessionId) {
            if (options.continue || options.resume) {
                console.error(chalk.red("Error: --session-id cannot be used with --continue or --resume.\n"));
                process.exit(1);
            }
            
            const validSessionId = validateSessionId(sessionId);
            if (!validSessionId) {
                console.error(chalk.red("Error: Invalid session ID. Must be a valid UUID.\n"));
                process.exit(1);
            }
            
            if (isSessionInUse(validSessionId)) {
                console.error(chalk.red(`Error: Session ID ${validSessionId} is already in use.\n`));
                process.exit(1);
            }
        }
        
        // Check for conflicting fallback model
        if (fallbackModel && options.model && fallbackModel === options.model) {
            console.error(chalk.red("Error: Fallback model cannot be the same as the main model. Please specify a different model for --fallback-model.\n"));
            process.exit(1);
        }
        
        // Setup permission mode
        const permissionContext = parsePermissionMode({
            permissionModeCli: permissionMode,
            dangerouslySkipPermissions
        });
        
        // Parse MCP configuration
        let dynamicMcpConfig;
        if (mcpConfig) {
            try {
                dynamicMcpConfig = await parseMcpConfig(mcpConfig);
            } catch (error) {
                console.error(`Error: ${error instanceof Error ? error.message : String(error)}`);
                process.exit(1);
            }
        }
        
        // Check if user is logged in
        const isLoggedIn = await checkLoginStatus();
        if (!isLoggedIn) {
            const didShowOnboarding = await performLoginFlow(permissionContext);
            
            // Handle special /login command
            if (didShowOnboarding && prompt?.trim().toLowerCase() === "/login") {
                prompt = "";
            }
            
            if (!didShowOnboarding) {
                exitWithoutLogin(); // TODO: Implement L$1()
            }
        }
        
        // Setup tool permissions
        const { toolPermissionContext, warnings } = setupToolPermissions({
            allowedToolsCli: allowedTools,
            disallowedToolsCli: disallowedTools,
            permissionMode: permissionContext,
            addDirs: addDir
        });
        
        // Display warnings
        warnings.forEach(warning => console.error(warning));
        
        // Validate input/output formats
        validateFormats(inputFormat, outputFormat);
        
        // Process input
        const processedInput = await processInput(prompt || "", inputFormat ?? "text");
        
        // Get available tools
        const availableTools = getAvailableTools(toolPermissionContext, isFeatureEnabled("todo"));
        
        // Initialize session
        await initializeSession(getCurrentWorkingDirectory(), permissionContext, isPrintMode, false, sessionId ? validateSessionId(sessionId) : undefined);
        
        // Initialize MCP clients and get tools/commands
        const [builtinCommands, mcpData] = await Promise.all([
            getBuiltinCommands(), // TODO: Implement lL1()
            processedInput || isLoggedIn ? initializeMcpClients(dynamicMcpConfig, strictMcpConfig) : {
                clients: [],
                tools: [],
                commands: []
            }
        ]);
        
        // Track initialization
        trackEvent("tengu_init", {
            entrypoint: "claude",
            hasInitialPrompt: Boolean(prompt),
            hasStdin: Boolean(processedInput),
            verbose,
            debug,
            print: isPrintMode,
            outputFormat,
            numAllowedTools: allowedTools.length,
            numDisallowedTools: disallowedTools.length,
            mcpClientCount: Object.keys(getMcpClients()).length, // TODO: Implement UV()
            worktree: false
        });
        
        // Initialize telemetry
        initializeTelemetry(); // TODO: Implement zi2()
        
        // Route to appropriate handler
        if (isLoggedIn) {
            // Non-interactive mode
            await handleNonInteractiveMode({
                input: processedInput,
                toolPermissionContext,
                mcpClients: mcpData.clients,
                builtinCommands,
                mcpCommands: mcpData.commands,
                availableTools,
                mcpTools: mcpData.tools,
                options
            });
        } else {
            // Interactive mode
            await handleInteractiveMode({
                input: processedInput,
                toolPermissionContext,
                mcpClients: mcpData.clients,
                builtinCommands,
                mcpCommands: mcpData.commands,
                availableTools,
                mcpTools: mcpData.tools,
                dynamicMcpConfig,
                autoConnectIde,
                strictMcpConfig,
                options
            });
        }
        
    } catch (error) {
        logError(error instanceof Error ? error : new Error(String(error)));
        process.exit(1);
    }
}

// Utility functions (TODO: Implement these)
function setupEnvironment() {
    if (!process.env.CLAUDE_CODE_ENTRYPOINT) {
        process.env.CLAUDE_CODE_ENTRYPOINT = "cli";
    }
}

function setupSignalHandlers() {
    process.on("exit", () => {
        restoreCursor(); // TODO: Implement AY4()
    });
    
    process.on("SIGINT", () => {
        process.exit(0);
    });
}

function determineEntrypoint() {
    if (process.env.GITHUB_ACTIONS === "true") return "github-action";
    if (process.env.CLAUDE_CODE_ENTRYPOINT === "sdk-ts") return "sdk-typescript";
    if (process.env.CLAUDE_CODE_ENTRYPOINT === "sdk-cli") return "sdk-cli";
    return "cli";
}

function executeRipgrep(args) {
    // TODO: Implement NX2()
    return 0;
}

function setupAnalytics(isPrintMode) {
    // TODO: Implement _FA() and yFA()
}

function setEntrypoint(entrypoint) {
    // TODO: Implement fFA()
}

function initializeShellSnapshot() {
    // TODO: Implement wX2()
    return Promise.resolve();
}

function setupCleanupHandlers() {
    // TODO: Implement vc0()
}

function setupPreflightChecks() {
    // TODO: Implement sF4()
}

function getApplicationName() {
    // TODO: Implement I2
    return "Claude Code";
}

function getVersionString() {
    // TODO: Implement version info
    return "1.0.53";
}

function getPermissionModes() {
    // TODO: Implement QD1
    return ["default", "strict", "permissive"];
}

async function checkLoginStatus() {
    // TODO: Implement a61()
    return false;
}

function exitWithoutLogin() {
    // TODO: Implement L$1()
    process.exit(1);
}

function setupToolPermissions(config) {
    // TODO: Implement ao2()
    return {
        toolPermissionContext: {},
        warnings: []
    };
}

function validateFormats(inputFormat, outputFormat) {
    if (inputFormat && inputFormat !== "text" && inputFormat !== "stream-json") {
        console.error(`Error: Invalid input format "${inputFormat}".`);
        process.exit(1);
    }
    
    if (inputFormat === "stream-json" && outputFormat !== "stream-json") {
        console.error("Error: --input-format=stream-json requires output-format=stream-json.");
        process.exit(1);
    }
}

async function processInput(prompt, format) {
    // TODO: Implement tF4()
    return prompt;
}

function getAvailableTools(toolPermissionContext, todoEnabled) {
    // TODO: Implement DH()
    return [];
}

async function initializeSession(cwd, permissionContext, isPrintMode, isDebug, sessionId) {
    // TODO: Implement US()
}

function getCurrentWorkingDirectory() {
    // TODO: Implement zS()
    return process.cwd();
}

async function getBuiltinCommands() {
    // TODO: Implement lL1()
    return [];
}

function getMcpClients() {
    // TODO: Implement UV()
    return {};
}

function initializeTelemetry() {
    // TODO: Implement zi2()
}

function isFeatureEnabled(feature) {
    // TODO: Implement JA().todoFeatureEnabled
    return false;
}

async function handleNonInteractiveMode(config) {
    // TODO: Implement X6B()
}

async function handleInteractiveMode(config) {
    // TODO: Implement wi() rendering
}

function restoreCursor() {
    // TODO: Implement AY4()
}

module.exports = {
    cliEntryPoint,
    initializeMainApplication,
    handleMainCommand
};
