/**
 * CLI Command Parser
 * 
 * Refactored version of the CLI command parsing logic from the original eF4 function.
 * Handles command line argument parsing, option validation, and command routing.
 * 
 * Original function: eF4() at line 354574
 */

const { Command, Option } = require('commander');
const chalk = require('chalk');

// Application constants
const APP_INFO = {
    name: "claude",
    version: "1.0.53",
    description: "<PERSON> Code - starts an interactive session by default, use -p/--print for non-interactive output"
};

// Permission modes
const PERMISSION_MODES = ["default", "strict", "permissive"];

/**
 * Creates and configures the main CLI command
 * @returns {Command} Configured commander instance
 */
function createMainCommand() {
    const program = new Command();
    
    program
        .name(APP_INFO.name)
        .description(APP_INFO.description)
        .argument("[prompt]", "Your prompt", String)
        .helpOption("-h, --help", "Display help for command")
        .option("-d, --debug", "Enable debug mode", () => true)
        .option("--verbose", "Override verbose mode setting from config", () => true)
        .option("-p, --print", "Print response and exit (useful for pipes)", () => true)
        .addOption(new Option("--output-format <format>", 
            'Output format (only works with --print): "text" (default), "json" (single result), or "stream-json" (realtime streaming)')
            .choices(["text", "json", "stream-json"]))
        .addOption(new Option("--input-format <format>", 
            'Input format (only works with --print): "text" (default), or "stream-json" (realtime streaming input)')
            .choices(["text", "stream-json"]))
        .option("--mcp-debug", "[DEPRECATED. Use --debug instead] Enable MCP debug mode (shows MCP server errors)", () => true)
        .option("--dangerously-skip-permissions", "Bypass all permission checks. Recommended only for sandboxes with no internet access.", () => true)
        .addOption(new Option("--max-turns <turns>", 
            "Maximum number of agentic turns in non-interactive mode. This will early exit the conversation after the specified number of turns. (only works with --print)")
            .argParser(Number)
            .hideHelp())
        .option("--allowedTools <tools...>", 'Comma or space-separated list of tool names to allow (e.g. "Bash(git:*) Edit")')
        .option("--disallowedTools <tools...>", 'Comma or space-separated list of tool names to deny (e.g. "Bash(git:*) Edit")')
        .option("--mcp-config <file or string>", "Load MCP servers from a JSON file or string")
        .addOption(new Option("--permission-prompt-tool <tool>", 
            "MCP tool to use for permission prompts (only works with --print)")
            .argParser(String)
            .hideHelp())
        .addOption(new Option("--system-prompt <prompt>", 
            "System prompt to use for the session  (only works with --print)")
            .argParser(String)
            .hideHelp())
        .addOption(new Option("--append-system-prompt <prompt>", 
            "Append a system prompt to the default system prompt")
            .argParser(String))
        .addOption(new Option("--permission-mode <mode>", 
            "Permission mode to use for the session")
            .argParser(String)
            .choices(PERMISSION_MODES))
        .option("-c, --continue", "Continue the most recent conversation", () => true)
        .option("-r, --resume [sessionId]", "Resume a conversation - provide a session ID or interactively select a conversation to resume", (value) => value || true)
        .option("--model <model>", "Model for the current session. Provide an alias for the latest model (e.g. 'sonnet' or 'opus') or a model's full name (e.g. 'claude-sonnet-4-20250514').")
        .option("--fallback-model <model>", "Enable automatic fallback to specified model when default model is overloaded (only works with --print)")
        .option("--add-dir <directories...>", "Additional directories to allow tool access to")
        .option("--ide", "Automatically connect to IDE on startup if exactly one valid IDE is available", () => true)
        .option("--strict-mcp-config", "Only use MCP servers from --mcp-config, ignoring all other MCP configurations", () => true)
        .option("--session-id <uuid>", "Use a specific session ID for the conversation (must be a valid UUID)")
        .version(`${APP_INFO.version}`, "-v, --version", "Output the version number");
    
    return program;
}

/**
 * Creates the config subcommand
 * @returns {Command} Config command instance
 */
function createConfigCommand() {
    const configCmd = new Command("config")
        .description("Manage configuration (eg. claude config set -g theme dark)")
        .helpOption("-h, --help", "Display help for command");
    
    // config get command
    configCmd.command("get <key>")
        .description("Get a config value")
        .option("-g, --global", "Use global config")
        .helpOption("-h, --help", "Display help for command")
        .action(async (key, options) => {
            // TODO: Implement config get
            console.log(`Getting config: ${key} (global: ${options.global})`);
        });
    
    // config set command
    configCmd.command("set <key> <value>")
        .description("Set a config value")
        .option("-g, --global", "Use global config")
        .helpOption("-h, --help", "Display help for command")
        .action(async (key, value, options) => {
            // TODO: Implement config set
            console.log(`Setting config: ${key} = ${value} (global: ${options.global})`);
        });
    
    // config remove command
    configCmd.command("remove <key> [values...]")
        .alias("rm")
        .description("Remove a config value or items from a config array")
        .option("-g, --global", "Use global config")
        .helpOption("-h, --help", "Display help for command")
        .action(async (key, values, options) => {
            // TODO: Implement config remove
            console.log(`Removing config: ${key} values: ${values} (global: ${options.global})`);
        });
    
    // config list command
    configCmd.command("list")
        .alias("ls")
        .description("List all config values")
        .option("-g, --global", "Use global config", false)
        .helpOption("-h, --help", "Display help for command")
        .action(async (options) => {
            // TODO: Implement config list
            console.log(`Listing config (global: ${options.global})`);
        });
    
    // config add command
    configCmd.command("add <key> <values...>")
        .description("Add items to a config array (space or comma separated)")
        .option("-g, --global", "Use global config")
        .helpOption("-h, --help", "Display help for command")
        .action(async (key, values, options) => {
            // TODO: Implement config add
            const processedValues = values.flatMap(v => v.includes(",") ? v.split(",") : v)
                .map(v => v.trim())
                .filter(v => v.length > 0);
            console.log(`Adding to config: ${key} values: ${processedValues} (global: ${options.global})`);
        });
    
    return configCmd;
}

/**
 * Creates the MCP subcommand
 * @returns {Command} MCP command instance
 */
function createMcpCommand() {
    const mcpCmd = new Command("mcp")
        .description("Configure and manage MCP servers")
        .helpOption("-h, --help", "Display help for command");
    
    // mcp serve command
    mcpCmd.command("serve")
        .description("Start the Claude Code MCP server")
        .helpOption("-h, --help", "Display help for command")
        .option("-d, --debug", "Enable debug mode", () => true)
        .option("--verbose", "Override verbose mode setting from config", () => true)
        .action(async (options) => {
            // TODO: Implement MCP serve
            console.log(`Starting MCP server (debug: ${options.debug}, verbose: ${options.verbose})`);
        });
    
    // mcp add command
    mcpCmd.command("add <name> <commandOrUrl> [args...]")
        .description("Add a server")
        .option("-s, --scope <scope>", "Configuration scope (local, user, or project)", "local")
        .option("-t, --transport <transport>", "Transport type (stdio, sse, http)", "stdio")
        .option("-e, --env <env...>", "Set environment variables (e.g. -e KEY=value)")
        .option("-H, --header <header...>", 'Set HTTP headers for SSE and HTTP transports (e.g. -H "X-Api-Key: abc123" -H "X-Custom: value")')
        .helpOption("-h, --help", "Display help for command")
        .action(async (name, commandOrUrl, args, options) => {
            // TODO: Implement MCP add
            console.log(`Adding MCP server: ${name} command: ${commandOrUrl} args: ${args} options:`, options);
        });
    
    return mcpCmd;
}

/**
 * Validates CLI options
 * @param {Object} options - Parsed CLI options
 * @returns {Object} Validation result with errors if any
 */
function validateOptions(options) {
    const errors = [];
    
    // Validate session ID conflicts
    if (options.sessionId && (options.continue || options.resume)) {
        errors.push("--session-id cannot be used with --continue or --resume");
    }
    
    // Validate fallback model
    if (options.fallbackModel && options.model && options.fallbackModel === options.model) {
        errors.push("Fallback model cannot be the same as the main model");
    }
    
    // Validate input/output format combinations
    if (options.inputFormat && !["text", "stream-json"].includes(options.inputFormat)) {
        errors.push(`Invalid input format "${options.inputFormat}"`);
    }
    
    if (options.inputFormat === "stream-json" && options.outputFormat !== "stream-json") {
        errors.push("--input-format=stream-json requires output-format=stream-json");
    }
    
    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Processes and normalizes CLI options
 * @param {Object} options - Raw CLI options
 * @returns {Object} Processed options
 */
function processOptions(options) {
    return {
        debug: options.debug || false,
        verbose: options.verbose || false,
        print: options.print || false,
        dangerouslySkipPermissions: options.dangerouslySkipPermissions || false,
        allowedTools: options.allowedTools || [],
        disallowedTools: options.disallowedTools || [],
        mcpConfig: options.mcpConfig,
        outputFormat: options.outputFormat || "text",
        inputFormat: options.inputFormat || "text",
        permissionMode: options.permissionMode,
        addDir: options.addDir || [],
        fallbackModel: options.fallbackModel,
        ide: options.ide || false,
        sessionId: options.sessionId,
        strictMcpConfig: options.strictMcpConfig || false,
        continue: options.continue,
        resume: options.resume,
        model: options.model,
        maxTurns: options.maxTurns,
        systemPrompt: options.systemPrompt,
        appendSystemPrompt: options.appendSystemPrompt,
        permissionPromptTool: options.permissionPromptTool
    };
}

module.exports = {
    createMainCommand,
    createConfigCommand,
    createMcpCommand,
    validateOptions,
    processOptions,
    APP_INFO,
    PERMISSION_MODES
};
