/**
 * Bash Tool (l9)
 * 
 * Refactored version of the Bash command execution tool from the original code.
 * <PERSON>les shell command execution with permission checking, sandboxing, and output processing.
 * 
 * Original variable: l9 at line 331313
 */

const React = require('react');
const fs = require('fs');
const path = require('path');

// Tool configuration
const TOOL_NAME = "Bash"; // eU constant
const MAX_DISPLAY_LINES = 10; // _o2 constant
const MAX_DISPLAY_CHARS = 1000; // m3A constant

// Dangerous command patterns
const DANGEROUS_COMMANDS = [
    "rm", "rmdir", "del", "delete", "format", "fdisk",
    "mkfs", "dd", "shred", "wipe", "kill", "killall",
    "shutdown", "reboot", "halt", "poweroff"
];

// Read-only command patterns (regex)
const READ_ONLY_PATTERNS = [
    /^ls(\s|$)/,
    /^cat(\s|$)/,
    /^head(\s|$)/,
    /^tail(\s|$)/,
    /^grep(\s|$)/,
    /^find(\s|$)/,
    /^which(\s|$)/,
    /^whereis(\s|$)/,
    /^pwd(\s|$)/,
    /^echo(\s|$)/,
    /^date(\s|$)/,
    /^whoami(\s|$)/,
    /^id(\s|$)/,
    /^ps(\s|$)/,
    /^top(\s|$)/,
    /^df(\s|$)/,
    /^du(\s|$)/,
    /^free(\s|$)/,
    /^uptime(\s|$)/,
    /^uname(\s|$)/
];

/**
 * Bash Tool Implementation
 */
const BashTool = {
    name: TOOL_NAME,
    
    /**
     * Gets tool description
     * @param {Object} options - Tool options
     * @returns {Promise<string>} Tool description
     */
    async description({ description }) {
        return description || "Run shell command";
    },
    
    /**
     * Gets tool prompt
     * @returns {Promise<string>} Tool prompt
     */
    async prompt() {
        return getToolPrompt(); // TODO: Implement tX2()
    },
    
    /**
     * Checks if tool is concurrency safe
     * @param {Object} input - Tool input
     * @returns {boolean} True if concurrency safe
     */
    isConcurrencySafe(input) {
        return this.isReadOnly(input);
    },
    
    /**
     * Checks if command is read-only
     * @param {Object} input - Tool input
     * @returns {boolean} True if read-only
     */
    isReadOnly(input) {
        const { command } = input;
        const isSandboxed = input.sandbox || false;
        
        if (isSandboxed) {
            return true;
        }
        
        const commandTokens = parseShellCommand(command); // TODO: Implement xf(command)
        
        return commandTokens.every(token => {
            // Check for dangerous commands
            if (containsDangerousCommand(token)) {
                return false;
            }
            
            // Check against read-only patterns
            for (const pattern of READ_ONLY_PATTERNS) {
                if (pattern.test(token)) {
                    return true;
                }
            }
            
            return false;
        });
    },
    
    /**
     * Gets input schema
     */
    get inputSchema() {
        return isLinuxEnvironment() ? getLinuxSchema() : getDefaultSchema(); // TODO: Implement LX1(), dQ4, jo2
    },
    
    /**
     * Gets user-facing tool name
     * @param {Object} input - Tool input
     * @returns {string} User-facing name
     */
    userFacingName(input) {
        if (!input) {
            return "Bash";
        }
        
        const isSandboxed = input.sandbox || false;
        return isSandboxed ? "SandboxedBash" : "Bash";
    },
    
    /**
     * Checks if tool is enabled
     * @returns {boolean} True if enabled
     */
    isEnabled() {
        return true;
    },
    
    /**
     * Checks permissions for command execution
     * @param {Object} input - Tool input
     * @param {Object} context - Tool context
     * @returns {Promise<Object>} Permission check result
     */
    async checkPermissions(input, context) {
        const isSandboxed = input.sandbox || false;
        
        if (isSandboxed) {
            return {
                behavior: "allow",
                updatedInput: input
            };
        }
        
        return checkBashPermissions(input, context); // TODO: Implement u3A(input, context)
    },
    
    /**
     * Validates tool input
     * @param {Object} input - Tool input
     * @param {Object} context - Tool context
     * @returns {Promise<Object>} Validation result
     */
    async validateInput(input, context) {
        const permissionCheck = validateBashInput(
            input,
            getCurrentWorkingDirectory(), // TODO: Implement cA()
            getShellPath(), // TODO: Implement z9()
            context.getToolPermissionContext()
        ); // TODO: Implement b3A()
        
        if (permissionCheck.behavior !== "allow") {
            return {
                result: false,
                message: permissionCheck.message,
                errorCode: 1
            };
        }
        
        return {
            result: true
        };
    },
    
    /**
     * Renders tool use message
     * @param {Object} input - Tool input
     * @param {Object} options - Render options
     * @returns {React.Element|string} Rendered message
     */
    renderToolUseMessage(input, { verbose }) {
        const { command } = input;
        
        if (!command) {
            return null;
        }
        
        let displayCommand = command;
        
        // Handle heredoc syntax
        if (command.includes(`"$(cat <<'EOF'`)) {
            const match = command.match(/^(.*?)"?\$\(cat <<'EOF'\n([\s\S]*?)\n\s*EOF\n\s*\)"(.*)$/);
            if (match && match[1] && match[2]) {
                const prefix = match[1];
                const content = match[2];
                const suffix = match[3] || "";
                displayCommand = `${prefix.trim()} "${content.trim()}"${suffix.trim()}`;
            }
        }
        
        if (!verbose) {
            const lines = displayCommand.split('\n');
            const tooManyLines = lines.length > MAX_DISPLAY_LINES;
            const tooLong = displayCommand.length > MAX_DISPLAY_CHARS;
            
            if (tooManyLines || tooLong) {
                let truncated = displayCommand;
                
                if (tooManyLines) {
                    truncated = lines.slice(0, MAX_DISPLAY_LINES).join('\n');
                }
                
                if (truncated.length > MAX_DISPLAY_CHARS) {
                    truncated = truncated.slice(0, MAX_DISPLAY_CHARS);
                }
                
                return React.createElement('span', null, truncated.trim(), "…");
            }
        }
        
        return displayCommand;
    },
    
    /**
     * Renders tool use rejected message
     * @returns {React.Element} Rejected message
     */
    renderToolUseRejectedMessage() {
        return React.createElement('div', { className: 'tool-rejected' }, 'Command execution was rejected');
    },
    
    /**
     * Renders tool use progress message
     * @param {Array} progressData - Progress data array
     * @returns {React.Element} Progress message
     */
    renderToolUseProgressMessage(progressData) {
        const lastProgress = progressData.at(-1);
        
        if (!lastProgress || !lastProgress.data || !lastProgress.data.output) {
            return React.createElement('div', { style: { height: 1 } },
                React.createElement('span', { style: { color: 'gray' } }, "Running…")
            );
        }
        
        const data = lastProgress.data;
        return React.createElement('div', null, [
            React.createElement('div', { key: 'output' }, data.output),
            React.createElement('div', { key: 'time' }, `Elapsed: ${data.elapsedTimeSeconds}s, Lines: ${data.totalLines}`)
        ]);
    },
    
    /**
     * Renders tool use queued message
     * @returns {React.Element} Queued message
     */
    renderToolUseQueuedMessage() {
        return React.createElement('div', { style: { height: 1 } },
            React.createElement('span', { style: { color: 'gray' } }, "Waiting…")
        );
    },
    
    /**
     * Renders tool result message
     * @param {Object} result - Tool result
     * @param {Object} input - Tool input
     * @param {Object} options - Render options
     * @returns {React.Element} Result message
     */
    renderToolResultMessage(result, input, { verbose }) {
        return React.createElement('div', { className: 'bash-result' }, [
            React.createElement('div', { key: 'stdout' }, result.stdout),
            result.stderr && React.createElement('div', { key: 'stderr', style: { color: 'red' } }, result.stderr)
        ]);
    },
    
    /**
     * Maps tool result to tool result block parameter
     * @param {Object} result - Tool execution result
     * @param {string} toolUseId - Tool use ID
     * @returns {Object} Tool result block parameter
     */
    mapToolResultToToolResultBlockParam({ interrupted, stdout, stderr, isImage }, toolUseId) {
        if (isImage) {
            const imageMatch = stdout.trim().match(/^data:([^;]+);base64,(.+)$/);
            if (imageMatch) {
                const mediaType = imageMatch[1];
                const data = imageMatch[2];
                return {
                    tool_use_id: toolUseId,
                    type: "tool_result",
                    content: [{
                        type: "image",
                        source: {
                            type: "base64",
                            media_type: mediaType || "image/jpeg",
                            data: data || ""
                        }
                    }]
                };
            }
        }
        
        let processedStdout = stdout;
        if (stdout) {
            processedStdout = stdout.replace(/^(\s*\n)+/, "");
            processedStdout = processedStdout.trimEnd();
        }
        
        let processedStderr = stderr.trim();
        if (interrupted) {
            if (stderr) {
                processedStderr += '\n';
            }
            processedStderr += "<e>Command was aborted before completion</e>";
        }
        
        return {
            tool_use_id: toolUseId,
            type: "tool_result",
            content: [processedStdout, processedStderr].filter(Boolean).join('\n'),
            is_error: interrupted
        };
    },
    
    /**
     * Main tool execution function
     * @param {Object} input - Tool input
     * @param {Object} context - Execution context
     * @yields {Object} Execution progress and results
     */
    async* call(input, { abortController, getToolPermissionContext, readFileState, options: { isNonInteractiveSession }, setToolJSX }) {
        let stdout = "";
        let stderr = "";
        let returnCodeInterpretation;
        let progressCounter = 0;
        let interrupted = false;
        let executionResult;
        
        try {
            // Execute the bash command
            const executor = createBashExecutor({
                input,
                abortController,
                setToolJSX,
                isReadOnly: this.isReadOnly(input)
            }); // TODO: Implement nQ4()
            
            let iteratorResult;
            do {
                iteratorResult = await executor.next();
                if (!iteratorResult.done) {
                    const progress = iteratorResult.value;
                    yield {
                        type: "progress",
                        toolUseID: `bash-progress-${progressCounter++}`,
                        data: {
                            type: "bash_progress",
                            output: progress.output,
                            elapsedTimeSeconds: progress.elapsedTimeSeconds,
                            totalLines: progress.totalLines
                        }
                    };
                }
            } while (!iteratorResult.done);
            
            executionResult = iteratorResult.value;
            
            // Track command execution
            trackCommandExecution(input.command, executionResult.code); // TODO: Implement pQ4()
            
            stdout += (executionResult.stdout || "").trimEnd() + '\n';
            returnCodeInterpretation = interpretReturnCode(
                input.command,
                executionResult.code,
                executionResult.stdout || "",
                executionResult.stderr || ""
            ); // TODO: Implement Po2()
            
            if (returnCodeInterpretation.isError) {
                stderr += (executionResult.stderr || "").trimEnd() + '\n';
                if (executionResult.code !== 0) {
                    stderr += `Exit code ${executionResult.code}`;
                }
            } else {
                stdout += (executionResult.stderr || "").trimEnd() + '\n';
            }
            
            // Filter stderr if needed
            if (shouldFilterStderr(getToolPermissionContext())) {
                stderr = filterSensitiveStderr(stderr); // TODO: Implement Qq1()
            }
            
            if (returnCodeInterpretation.isError) {
                throw new BashExecutionError(
                    executionResult.stdout,
                    executionResult.stderr,
                    executionResult.code,
                    executionResult.interrupted
                ); // TODO: Implement gz class
            }
            
            interrupted = executionResult.interrupted;
            
        } finally {
            if (setToolJSX) {
                setToolJSX(null);
            }
        }
        
        // Process file paths and update read file state
        processFilePathsFromOutput(input.command, stdout, isNonInteractiveSession)
            .then(filePaths => {
                for (const filePath of filePaths) {
                    const resolvedPath = path.isAbsolute(filePath) 
                        ? filePath 
                        : path.resolve(getCurrentWorkingDirectory(), filePath);
                    
                    try {
                        if (!fs.existsSync(resolvedPath) || !fs.statSync(resolvedPath).isFile()) {
                            continue;
                        }
                        
                        readFileState[resolvedPath] = {
                            content: fs.readFileSync(resolvedPath, 'utf8'),
                            timestamp: fs.statSync(resolvedPath).mtimeMs
                        };
                    } catch (error) {
                        console.error('Error reading file:', error);
                    }
                }
                
                // Track file processing
                trackEvent("tengu_bash_tool_haiku_file_paths_read", {
                    filePathsExtracted: filePaths.length,
                    readFileStateSize: Object.keys(readFileState).length,
                    readFileStateValuesCharLength: Object.values(readFileState)
                        .reduce((total, file) => total + file.content.length, 0)
                });
            });
        
        // Process and truncate output
        const { truncatedContent: processedStdout, isImage } = processAndTruncateOutput(normalizeOutput(stdout));
        const { truncatedContent: processedStderr } = processAndTruncateOutput(normalizeOutput(stderr));
        
        yield {
            type: "result",
            data: {
                stdout: processedStdout,
                stderr: processedStderr,
                interrupted,
                isImage,
                returnCodeInterpretation: returnCodeInterpretation?.message,
                backgroundTaskId: executionResult.backgroundTaskId
            }
        };
    },
    
    /**
     * Renders tool use error message
     * @param {Object} error - Error object
     * @param {Object} options - Render options
     * @returns {React.Element} Error message
     */
    renderToolUseErrorMessage(error, { verbose }) {
        return React.createElement('div', { className: 'bash-error' }, [
            React.createElement('div', { key: 'message' }, error.message),
            verbose && error.stderr && React.createElement('div', { key: 'stderr' }, error.stderr)
        ]);
    }
};

// Utility functions (TODO: Implement these)
function parseShellCommand(command) {
    // TODO: Implement shell command parsing
    return command.split(/\s+/);
}

function containsDangerousCommand(token) {
    return DANGEROUS_COMMANDS.some(cmd => token.includes(cmd));
}

function getToolPrompt() {
    // TODO: Implement tool prompt generation
    return "Enter a shell command to execute:";
}

function isLinuxEnvironment() {
    // TODO: Implement Linux environment detection
    return process.platform === 'linux';
}

function getLinuxSchema() {
    // TODO: Implement Linux-specific schema
    return {};
}

function getDefaultSchema() {
    // TODO: Implement default schema
    return {};
}

function checkBashPermissions(input, context) {
    // TODO: Implement permission checking
    return { behavior: "allow", updatedInput: input };
}

function validateBashInput(input, cwd, shellPath, permissionContext) {
    // TODO: Implement input validation
    return { behavior: "allow" };
}

function getCurrentWorkingDirectory() {
    return process.cwd();
}

function getShellPath() {
    return process.env.SHELL || '/bin/bash';
}

function createBashExecutor(options) {
    const { spawn } = require('child_process');
    const {
        command,
        timeout = 30000,
        cwd = process.cwd(),
        env = process.env,
        signal = null
    } = options;

    let childProcess = null;
    let stdout = "";
    let stderr = "";
    let finished = false;
    let result = null;

    // Start the process
    const startProcess = () => {
        if (signal?.aborted) {
            finished = true;
            result = {
                stdout: "",
                stderr: "Operation cancelled",
                code: 1,
                interrupted: true
            };
            return;
        }

        try {
            childProcess = spawn(command, [], {
                env,
                cwd,
                shell: true,
                signal
            });

            childProcess.stdout.on("data", (data) => {
                stdout += data.toString();
            });

            childProcess.stderr.on("data", (data) => {
                stderr += data.toString();
            });

            childProcess.on("close", (code) => {
                finished = true;
                result = {
                    stdout,
                    stderr,
                    code: code ?? 1,
                    interrupted: signal?.aborted || false
                };
            });

            childProcess.on("error", (error) => {
                finished = true;
                result = {
                    stdout,
                    stderr: `Error occurred while executing command: ${error instanceof Error ? error.message : String(error)}`,
                    code: 1,
                    interrupted: false
                };
            });

            // Set up timeout if specified
            if (timeout > 0) {
                setTimeout(() => {
                    if (!finished && childProcess) {
                        childProcess.kill('SIGTERM');
                        finished = true;
                        result = {
                            stdout,
                            stderr: stderr + "\nCommand timed out",
                            code: 124, // Standard timeout exit code
                            interrupted: true
                        };
                    }
                }, timeout);
            }

        } catch (error) {
            finished = true;
            result = {
                stdout: "",
                stderr: `Failed to spawn command: ${error instanceof Error ? error.message : String(error)}`,
                code: 1,
                interrupted: false
            };
        }
    };

    // Start the process immediately
    startProcess();

    return {
        async next() {
            // Wait for process to finish
            while (!finished) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            return {
                done: true,
                value: result
            };
        },

        // Allow manual termination
        kill() {
            if (childProcess && !finished) {
                childProcess.kill('SIGTERM');
                finished = true;
                result = {
                    stdout,
                    stderr: stderr + "\nCommand terminated",
                    code: 130, // Standard SIGTERM exit code
                    interrupted: true
                };
            }
        }
    };
}

function trackCommandExecution(command, code) {
    // Track command execution for analytics and debugging
    const { trackEvent } = require('../analytics/analytics-system');

    try {
        // Extract command name for tracking
        const commandName = command.trim().split(/\s+/)[0] || "";

        trackEvent("bash_command_executed", {
            command_name: commandName,
            exit_code: code,
            success: code === 0,
            timestamp: Date.now()
        });

        // Log for debugging
        if (code === 0) {
            console.log(`✓ Command executed successfully: ${commandName}`);
        } else {
            console.log(`✗ Command failed with exit code ${code}: ${commandName}`);
        }
    } catch (error) {
        console.error('Failed to track command execution:', error);
    }
}

function interpretReturnCode(command, code, stdout, stderr) {
    // Interpret bash command return codes based on common conventions
    const commandName = command.trim().split(/\s+/)[0] || "";

    // Standard exit codes
    const exitCodes = {
        0: "Success",
        1: "General error",
        2: "Misuse of shell builtins",
        126: "Command invoked cannot execute",
        127: "Command not found",
        128: "Invalid argument to exit",
        130: "Script terminated by Control-C",
        124: "Command timed out"
    };

    let interpretation = {
        isError: code !== 0,
        code: code,
        message: exitCodes[code] || `Unknown exit code: ${code}`,
        severity: code === 0 ? "success" : "error"
    };

    // Command-specific interpretations
    if (commandName === "grep" && code === 1) {
        // Grep exit code 1 means no match found, not really an error
        interpretation.message = "No match found";
        interpretation.severity = "info";
    } else if (commandName === "test" || commandName === "[") {
        // Test command uses exit codes for logic
        interpretation.message = code === 0 ? "Test condition true" : "Test condition false";
        interpretation.severity = "info";
    }

    // Check for common error patterns in stderr
    if (stderr && code !== 0) {
        if (stderr.includes("Permission denied")) {
            interpretation.message = "Permission denied";
        } else if (stderr.includes("No such file or directory")) {
            interpretation.message = "File or directory not found";
        } else if (stderr.includes("command not found")) {
            interpretation.message = "Command not found";
        }
    }

    return interpretation;
}

function shouldFilterStderr(permissionContext) {
    // Filter stderr in strict permission modes to prevent information leakage
    return permissionContext && permissionContext.mode !== "bypassPermissions";
}

function filterSensitiveStderr(stderr) {
    // Filter sensitive information from stderr output
    if (!stderr) return stderr;

    let filtered = stderr;

    // Filter common sensitive patterns
    const sensitivePatterns = [
        // API keys and tokens
        /[A-Za-z0-9]{20,}/g,
        // File paths that might contain usernames
        /\/Users\/<USER>\/\s]+/g,
        /\/home\/<USER>\/\s]+/g,
        // IP addresses
        /\b(?:\d{1,3}\.){3}\d{1,3}\b/g,
        // Email addresses
        /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g
    ];

    sensitivePatterns.forEach(pattern => {
        filtered = filtered.replace(pattern, '[FILTERED]');
    });

    return filtered;
}

function processFilePathsFromOutput(command, output, isNonInteractive) {
    // Process file paths from command output for further analysis
    return new Promise((resolve) => {
        try {
            const filePaths = [];

            // Skip processing for non-interactive mode or if no output
            if (isNonInteractive || !output) {
                resolve(filePaths);
                return;
            }

            // Extract potential file paths from output
            const lines = output.split('\n');
            const pathPattern = /(?:^|\s)([^\s]+\.[a-zA-Z0-9]+)(?:\s|$)/g;

            lines.forEach(line => {
                let match;
                while ((match = pathPattern.exec(line)) !== null) {
                    const potentialPath = match[1];

                    // Basic validation - check if it looks like a file path
                    if (potentialPath.includes('/') || potentialPath.includes('\\')) {
                        filePaths.push(potentialPath);
                    }
                }
            });

            // Remove duplicates and sort
            const uniquePaths = [...new Set(filePaths)].sort();

            resolve(uniquePaths);
        } catch (error) {
            console.error('Error processing file paths from output:', error);
            resolve([]);
        }
    });
}

function processAndTruncateOutput(output) {
    // TODO: Implement output processing and truncation
    return { truncatedContent: output, isImage: false };
}

function normalizeOutput(output) {
    // TODO: Implement output normalization
    return output;
}

function trackEvent(eventName, data) {
    console.log(`Event: ${eventName}`, data);
}

class BashExecutionError extends Error {
    constructor(stdout, stderr, code, interrupted) {
        super(`Bash execution failed with code ${code}`);
        this.stdout = stdout;
        this.stderr = stderr;
        this.code = code;
        this.interrupted = interrupted;
    }
}

module.exports = {
    BashTool,
    BashExecutionError,
    DANGEROUS_COMMANDS,
    READ_ONLY_PATTERNS
};
