/**
 * Tool Execution Framework (Z44, Dp, a$1)
 * 
 * Refactored version of the 6-stage tool execution pipeline from the original code.
 * This framework handles tool discovery, validation, permission checking, execution,
 * concurrency control, and result processing with proper error handling and isolation.
 * 
 * Original functions:
 * - Z44() at line 335437 - Main tool executor
 * - Dp() at line 320788 - Concurrency controller (max 10 concurrent)
 * - a$1() at line 335470 - Single tool executor
 * - J44() at line 335527 - Tool execution pipeline
 */

const { executeWithConcurrencyLimit } = require('../core/concurrency-controller');
const { executePreToolHooks, executePostToolHooks } = require('../hooks/tool-hooks');
const { createUserMessage, createToolResult } = require('../core/message-factory');
const { trackEvent, logError } = require('../utils/analytics');
const { validateToolInput, formatValidationError } = require('../utils/validation');
const { createCancelledToolResult, CANCELLED_MESSAGE } = require('../utils/tool-utils');

// Maximum concurrent tool executions
const MAX_CONCURRENT_TOOLS = 10;

/**
 * Main Tool Executor - Orchestrates tool execution with concurrency control
 * 
 * This is the entry point for all tool execution. It:
 * 1. Groups tools by concurrency safety
 * 2. Executes safe tools concurrently (up to 10)
 * 3. Executes unsafe tools sequentially
 * 4. Manages execution state and progress tracking
 * 
 * Original function: Z44(A, B, Q, I) at line 335437
 * 
 * @param {Array} toolUses - Array of tool use objects
 * @param {Array} assistantMessages - Assistant messages containing tool uses
 * @param {Object} toolContext - Tool execution context
 * @param {Object} executionContext - Main execution context
 * @yields {Object} Tool execution events and results
 */
async function* executeTools(toolUses, assistantMessages, toolContext, executionContext) {
    // Phase 1: Group tools by concurrency safety
    const toolGroups = groupToolsByConcurrencySafety(toolUses, executionContext);
    
    // Phase 2: Execute each group with appropriate concurrency control
    for (const { isConcurrencySafe, blocks } of toolGroups) {
        if (isConcurrencySafe) {
            // Execute safe tools concurrently
            yield* executeConcurrentTools(blocks, assistantMessages, toolContext, executionContext);
        } else {
            // Execute unsafe tools sequentially
            yield* executeSequentialTools(blocks, assistantMessages, toolContext, executionContext);
        }
    }
}

/**
 * Groups tool uses by their concurrency safety
 * 
 * @param {Array} toolUses - Tool use objects
 * @param {Object} executionContext - Execution context
 * @returns {Array} Array of tool groups with concurrency safety flags
 */
function groupToolsByConcurrencySafety(toolUses, executionContext) {
    return toolUses.reduce((groups, toolUse) => {
        // Find the tool definition
        const tool = executionContext.options.tools.find(t => t.name === toolUse.name);
        
        // Validate input and check concurrency safety
        const inputValidation = tool?.inputSchema.safeParse(toolUse.input);
        const isConcurrencySafe = inputValidation?.success ? 
            Boolean(tool?.isConcurrencySafe(inputValidation.data)) : false;
        
        // Group consecutive safe tools together
        if (isConcurrencySafe && groups[groups.length - 1]?.isConcurrencySafe) {
            groups[groups.length - 1].blocks.push(toolUse);
        } else {
            groups.push({
                isConcurrencySafe,
                blocks: [toolUse]
            });
        }
        
        return groups;
    }, []);
}

/**
 * Executes tools sequentially (one at a time)
 * 
 * @param {Array} toolUses - Tool use objects
 * @param {Array} assistantMessages - Assistant messages
 * @param {Object} toolContext - Tool context
 * @param {Object} executionContext - Execution context
 * @yields {Object} Tool execution results
 */
async function* executeSequentialTools(toolUses, assistantMessages, toolContext, executionContext) {
    for (const toolUse of toolUses) {
        const assistantMessage = findAssistantMessageForTool(toolUse, assistantMessages);
        yield* executeSingleTool(toolUse, assistantMessage, toolContext, executionContext);
    }
}

/**
 * Executes tools concurrently (up to MAX_CONCURRENT_TOOLS)
 * 
 * @param {Array} toolUses - Tool use objects
 * @param {Array} assistantMessages - Assistant messages
 * @param {Object} toolContext - Tool context
 * @param {Object} executionContext - Execution context
 * @yields {Object} Tool execution results
 */
async function* executeConcurrentTools(toolUses, assistantMessages, toolContext, executionContext) {
    const toolExecutors = toolUses.map(toolUse => {
        const assistantMessage = findAssistantMessageForTool(toolUse, assistantMessages);
        return executeSingleTool(toolUse, assistantMessage, toolContext, executionContext);
    });
    
    yield* executeWithConcurrencyLimit(toolExecutors, MAX_CONCURRENT_TOOLS);
}

/**
 * Finds the assistant message that contains a specific tool use
 * 
 * @param {Object} toolUse - Tool use object
 * @param {Array} assistantMessages - Assistant messages
 * @returns {Object} Assistant message containing the tool use
 */
function findAssistantMessageForTool(toolUse, assistantMessages) {
    return assistantMessages.find(message =>
        message.message.content.some(content =>
            content.type === "tool_use" && content.id === toolUse.id
        )
    );
}

/**
 * Removes a tool use ID from the in-progress set
 * 
 * @param {Object} executionContext - Execution context
 * @param {string} toolUseId - Tool use ID to remove
 */
function removeInProgressToolUse(executionContext, toolUseId) {
    executionContext.setInProgressToolUseIDs(currentSet =>
        new Set([...currentSet].filter(id => id !== toolUseId))
    );
}

/**
 * Single Tool Executor - Executes one tool with full 6-stage pipeline
 * 
 * The 6 stages are:
 * 1. Tool Discovery and Registration
 * 2. Parameter Validation and Type Checking
 * 3. Permission Verification and Security Checking
 * 4. Resource Allocation and Environment Preparation
 * 5. Concurrent Execution and State Monitoring
 * 6. Result Collection and Cleanup/Recovery
 * 
 * Original function: a$1(A, B, Q, I) at line 335470
 * 
 * @param {Object} toolUse - Tool use object
 * @param {Object} assistantMessage - Assistant message containing tool use
 * @param {Object} toolContext - Tool context
 * @param {Object} executionContext - Execution context
 * @yields {Object} Tool execution events and results
 */
async function* executeSingleTool(toolUse, assistantMessage, toolContext, executionContext) {
    const toolName = toolUse.name;
    const toolUseId = toolUse.id;
    
    // Stage 1: Tool Discovery and Registration
    const tool = executionContext.options.tools.find(t => t.name === toolName);
    
    // Add to in-progress set
    executionContext.setInProgressToolUseIDs(currentSet => new Set([...currentSet, toolUseId]));
    
    if (!tool) {
        trackEvent("tengu_tool_use_error", {
            error: `No such tool available: ${toolName}`,
            toolName,
            toolUseID: toolUseId,
            isMcp: false
        });
        
        yield createUserMessage({
            content: [createToolResult({
                toolUseId,
                content: `Error: No such tool available: ${toolName}`,
                isError: true
            })],
            toolUseResult: `Error: No such tool available: ${toolName}`
        });
        
        removeInProgressToolUse(executionContext, toolUseId);
        return;
    }
    
    const input = toolUse.input;
    
    try {
        // Check for abort before starting
        if (executionContext.abortController.signal.aborted) {
            trackEvent("tengu_tool_use_cancelled", {
                toolName: tool.name,
                toolUseID: toolUseId,
                isMcp: tool.isMcp ?? false
            });
            
            const cancelledResult = createCancelledToolResult(toolUseId);
            yield createUserMessage({
                content: [cancelledResult],
                toolUseResult: CANCELLED_MESSAGE
            });
            
            removeInProgressToolUse(executionContext, toolUseId);
            return;
        }
        
        // Execute the full tool pipeline
        for await (const result of executeToolPipeline(
            tool,
            toolUseId,
            input,
            executionContext,
            toolContext,
            assistantMessage
        )) {
            yield result;
        }
        
    } catch (error) {
        logError(error instanceof Error ? error : new Error(String(error)));
        
        const errorMessage = error instanceof Error ? error.message : String(error);
        const fullErrorMessage = `Error calling tool${tool ? ` (${tool.name})` : ""}: ${errorMessage}`;
        
        yield createUserMessage({
            content: [createToolResult({
                toolUseId,
                content: fullErrorMessage,
                isError: true
            })],
            toolUseResult: fullErrorMessage
        });
    }
    
    // Stage 6: Cleanup and Recovery
    removeInProgressToolUse(executionContext, toolUseId);
}

/**
 * Tool Execution Pipeline - Implements stages 2-5 of tool execution
 * 
 * Original function: J44(A, B, Q, I, D, G) at line 335527
 * 
 * @param {Object} tool - Tool definition
 * @param {string} toolUseId - Tool use ID
 * @param {Object} input - Tool input
 * @param {Object} executionContext - Execution context
 * @param {Object} toolContext - Tool context
 * @param {Object} assistantMessage - Assistant message
 * @yields {Object} Tool execution results
 */
async function* executeToolPipeline(tool, toolUseId, input, executionContext, toolContext, assistantMessage) {
    // Stage 2: Parameter Validation and Type Checking
    const inputValidation = tool.inputSchema.safeParse(input);
    if (!inputValidation.success) {
        const errorMessage = formatValidationError(tool.name, inputValidation.error);
        
        trackEvent("tengu_tool_use_error", {
            error: "InputValidationError",
            messageID: assistantMessage.message.id,
            toolName: tool.name
        });
        
        yield createUserMessage({
            content: [createToolResult({
                toolUseId,
                content: `InputValidationError: ${errorMessage}`,
                isError: true
            })],
            toolUseResult: `InputValidationError: ${inputValidation.error.message}`
        });
        return;
    }
    
    // Additional input validation if tool provides it
    const customValidation = await tool.validateInput?.(inputValidation.data, executionContext);
    if (customValidation?.result === false) {
        trackEvent("tengu_tool_use_error", {
            messageID: assistantMessage.message.id,
            toolName: tool.name,
            errorCode: customValidation.errorCode
        });
        
        yield createUserMessage({
            content: [createToolResult({
                toolUseId,
                content: customValidation.message,
                isError: true
            })],
            toolUseResult: `Error: ${customValidation.message}`
        });
        return;
    }
    
    // Stage 3: Permission Verification and Security Checking
    let validatedInput = inputValidation.data;
    let hookApproved = false;
    let hookApprovalReason;
    let hookDenied = false;
    let hookDenialMessage;
    let preventContinuation = false;
    let stopReason;
    
    const hookStartTime = Date.now();
    
    try {
        // Execute pre-tool hooks
        const hookResults = executePreToolHooks(tool.name, toolUseId, validatedInput, executionContext.abortController.signal);
        const blockingErrors = [];
        
        for await (const hookResult of hookResults) {
            if (hookResult.message) {
                yield hookResult.message;
            }
            
            if (hookResult.blockingErrors) {
                blockingErrors.push(...hookResult.blockingErrors);
            }
            
            if (hookResult.preventContinuation) {
                preventContinuation = true;
                if (hookResult.stopReason) {
                    stopReason = hookResult.stopReason;
                }
            }
            
            if (hookResult.hookApproved) {
                hookApproved = true;
                if (hookResult.hookApprovalReason) {
                    hookApprovalReason = hookResult.hookApprovalReason;
                }
            }
        }
        
        // Check for abort after hooks
        if (executionContext.abortController.signal.aborted) {
            trackEvent("tengu_pre_tool_hooks_cancelled", {
                toolName: tool.name
            });
            
            yield createUserMessage({
                content: [createCancelledToolResult(toolUseId)],
                toolUseResult: CANCELLED_MESSAGE
            });
            return;
        }
        
        // Handle blocking errors
        if (blockingErrors.length > 0) {
            hookDenied = true;
            hookDenialMessage = formatBlockingErrors(tool.name, blockingErrors);
        }
        
    } catch (error) {
        const duration = Date.now() - hookStartTime;
        
        trackEvent("tengu_pre_tool_hook_error", {
            messageID: assistantMessage.message.id,
            toolName: tool.name,
            isMcp: tool.isMcp ?? false,
            duration
        });
        
        yield createWarningMessage(`Pre-tool hook failed: ${formatError(error)}`, "warning", toolUseId);
        yield createUserMessage({
            content: [createCancelledToolResult(toolUseId)],
            toolUseResult: CANCELLED_MESSAGE
        });
        
        removeInProgressToolUse(executionContext, toolUseId);
        return;
    }
    
    // Determine permission result
    let permissionResult;
    
    if (hookApproved) {
        permissionResult = {
            behavior: "allow",
            updatedInput: validatedInput,
            decisionReason: {
                type: "hook",
                hookName: `PreToolUse:${tool.name}`,
                reason: hookApprovalReason
            }
        };
    } else if (hookDenied) {
        permissionResult = {
            behavior: "deny",
            message: hookDenialMessage,
            ruleSuggestions: null,
            decisionReason: {
                type: "hook",
                hookName: `PreToolUse:${tool.name}`,
                reason: "Hook blocked execution"
            }
        };
    } else {
        // Check standard permissions
        permissionResult = await toolContext(tool, validatedInput, executionContext, toolUseId);
    }
    
    // Handle permission denial
    if (permissionResult.behavior === "deny") {
        yield createUserMessage({
            content: [createToolResult({
                toolUseId,
                content: permissionResult.message || "Permission denied",
                isError: true
            })],
            toolUseResult: permissionResult.message || "Permission denied"
        });
        return;
    }
    
    // Stage 4: Resource Allocation and Environment Preparation
    // Stage 5: Concurrent Execution and State Monitoring
    // Execute the actual tool
    const finalInput = permissionResult.updatedInput || validatedInput;
    
    for await (const toolResult of tool.call(finalInput, executionContext)) {
        yield toolResult;
    }
    
    // Execute post-tool hooks if needed
    try {
        const { executeHooks } = require('../core/hook-system');
        const postToolHookData = {
            hook_event_name: "PostToolUse",
            tool_name: tool.name,
            tool_result: finalResult,
            tool_use_id: toolUseId
        };

        for await (const hookResult of executeHooks(postToolHookData, toolUseId, undefined, abortSignal)) {
            // Process hook results if needed
            if (hookResult.outcome === "block") {
                console.warn(`Post-tool hook blocked: ${hookResult.blockingError}`);
            }
        }
    } catch (error) {
        console.error('Error executing post-tool hooks:', error);
    }
}

// Utility functions (TODO: Implement these)
function formatBlockingErrors(toolName, errors) {
    // Original function: Fn2() at line 322859
    if (!errors || errors.length === 0) {
        return "";
    }

    const errorMessages = errors.map(error => {
        if (typeof error === 'object' && error.blockingError) {
            return `- ${error.blockingError}`;
        } else if (typeof error === 'string') {
            return `- ${error}`;
        } else if (error instanceof Error) {
            return `- ${error.message}`;
        } else {
            return `- ${String(error)}`;
        }
    }).join('\n');

    return `${toolName} operation blocked by hook:\n${errorMessages}`;
}

function formatError(error) {
    return error instanceof Error ? error.message : String(error);
}

function createWarningMessage(content, level, toolUseId, preventContinuation) {
    // Original function: R5() at line 319819
    const { generateUUID } = require('../utils/uuid-utils');

    return {
        type: "system",
        content: content,
        isMeta: false,
        timestamp: new Date().toISOString(),
        uuid: generateUUID(),
        toolUseID: toolUseId,
        level: level,
        ...(preventContinuation && {
            preventContinuation: preventContinuation
        })
    };
}

module.exports = {
    executeTools,
    executeSingleTool,
    executeToolPipeline,
    groupToolsByConcurrencySafety,
    executeSequentialTools,
    executeConcurrentTools,
    MAX_CONCURRENT_TOOLS
};
