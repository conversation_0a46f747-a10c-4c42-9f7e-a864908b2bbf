/**
 * Tool Registry System (DH)
 * 
 * Refactored version of the tool registration and management system.
 * Handles tool discovery, filtering, and availability checking.
 * 
 * Original function: DH() at line 339366
 */

// Import all available tools
const { TaskTool } = require('./task-tool'); // Me2
const { BashTool } = require('./bash-tool'); // l9
const { EditTool } = require('./edit-tool'); // PV
const { ViewTool } = require('./view-tool'); // hf
const { SearchTool } = require('./search-tool'); // _w
const { CreateTool } = require('./create-tool'); // kw

// TODO: Implement remaining tools
const RemoveTool = { name: "Remove", isEnabled: () => true }; // E8
const MoveTool = { name: "Move", isEnabled: () => true }; // mD
const CopyTool = { name: "Copy", isEnabled: () => true }; // FL
const MemoryTool = { name: "Memory", isEnabled: () => true }; // YC
const ReadTool = { name: "Read", isEnabled: () => true }; // d21
const WebSearchTool = { name: "WebSearch", isEnabled: () => true }; // GS
const BrowserTool = { name: "Browser", isEnabled: () => true }; // bW
const IdeConnectorTool = { name: "IdeConnector", isEnabled: () => true }; // kG
const DiagnosticsTool = { name: "Diagnostics", isEnabled: () => true }; // e0B

/**
 * Core tool registry containing all available tools
 */
const CORE_TOOLS = [
    TaskTool,        // Me2 - Task management tool
    BashTool,        // l9 - Bash command execution
    EditTool,        // PV - File editing
    ViewTool,        // hf - File viewing
    SearchTool,      // _w - Code search
    CreateTool,      // kw - File creation
    RemoveTool,      // E8 - File removal
    MoveTool,        // mD - File moving
    CopyTool,        // FL - File copying
    MemoryTool,      // YC - Memory management
    WebSearchTool,   // GS - Web search
    BrowserTool,     // bW - Browser automation
    DiagnosticsTool  // e0B - Code diagnostics
];

/**
 * Conditional tools that may be included based on environment
 */
const CONDITIONAL_TOOLS = {
    // Unified read tool - included when CLAUDE_CODE_ENABLE_UNIFIED_READ_TOOL is not set
    readTool: {
        condition: () => !process.env.CLAUDE_CODE_ENABLE_UNIFIED_READ_TOOL,
        tools: [ReadTool] // d21
    },
    
    // IDE connector tool - included when todo feature is enabled
    ideConnector: {
        condition: (todoFeatureEnabled) => todoFeatureEnabled,
        tools: [IdeConnectorTool] // kG
    }
};

/**
 * Registers and filters tools based on permissions and availability
 * Original function: DH(A, B) at line 339366
 * 
 * @param {Object} toolPermissionContext - Tool permission context
 * @param {boolean} todoFeatureEnabled - Whether todo feature is enabled
 * @returns {Array} Array of enabled and available tools
 */
function registerTools(toolPermissionContext, todoFeatureEnabled = false) {
    // Start with core tools
    let availableTools = [...CORE_TOOLS];
    
    // Add conditional tools based on environment and features
    for (const [key, config] of Object.entries(CONDITIONAL_TOOLS)) {
        if (config.condition(todoFeatureEnabled)) {
            availableTools.push(...config.tools);
        }
    }
    
    // Filter tools based on permission rules
    const permissionRules = getPermissionRules(toolPermissionContext);
    const filteredTools = availableTools.filter(tool => {
        return !isToolDisabledByPermissions(tool, permissionRules);
    });
    
    // Check tool availability and filter enabled tools
    const enabledFlags = filteredTools.map(tool => tool.isEnabled());
    const enabledTools = filteredTools.filter((tool, index) => enabledFlags[index]);
    
    return enabledTools;
}

/**
 * Gets permission rules from the tool permission context
 * @param {Object} toolPermissionContext - Permission context
 * @returns {Array} Array of permission rules
 */
function getPermissionRules(toolPermissionContext) {
    // TODO: Implement Fh(A) function to extract permission rules
    if (!toolPermissionContext) {
        return [];
    }
    
    // Extract rules from permission context
    const rules = [];
    
    // Process always allow rules
    if (toolPermissionContext.alwaysAllowRules) {
        for (const [ruleType, ruleValues] of Object.entries(toolPermissionContext.alwaysAllowRules)) {
            if (Array.isArray(ruleValues)) {
                rules.push(...ruleValues.map(value => ({
                    type: 'allow',
                    ruleType,
                    ruleValue: { toolName: value, ruleContent: undefined }
                })));
            }
        }
    }
    
    // Process always deny rules
    if (toolPermissionContext.alwaysDenyRules) {
        for (const [ruleType, ruleValues] of Object.entries(toolPermissionContext.alwaysDenyRules)) {
            if (Array.isArray(ruleValues)) {
                rules.push(...ruleValues.map(value => ({
                    type: 'deny',
                    ruleType,
                    ruleValue: { toolName: value, ruleContent: undefined }
                })));
            }
        }
    }
    
    return rules;
}

/**
 * Checks if a tool is disabled by permission rules
 * @param {Object} tool - Tool instance
 * @param {Array} permissionRules - Permission rules
 * @returns {boolean} True if tool is disabled
 */
function isToolDisabledByPermissions(tool, permissionRules) {
    return permissionRules.some(rule => {
        return rule.type === 'deny' && 
               rule.ruleValue.toolName === tool.name && 
               rule.ruleValue.ruleContent === undefined;
    });
}

/**
 * Gets a tool by name from the registry
 * @param {string} toolName - Name of the tool
 * @returns {Object|null} Tool instance or null if not found
 */
function getToolByName(toolName) {
    const allTools = [
        ...CORE_TOOLS,
        ...Object.values(CONDITIONAL_TOOLS).flatMap(config => config.tools)
    ];
    
    return allTools.find(tool => tool.name === toolName) || null;
}

/**
 * Gets all available tool names
 * @returns {Array} Array of tool names
 */
function getAllToolNames() {
    const allTools = [
        ...CORE_TOOLS,
        ...Object.values(CONDITIONAL_TOOLS).flatMap(config => config.tools)
    ];
    
    return allTools.map(tool => tool.name);
}

/**
 * Validates tool configuration
 * @param {Object} tool - Tool to validate
 * @returns {Object} Validation result
 */
function validateTool(tool) {
    const errors = [];
    
    if (!tool.name) {
        errors.push('Tool must have a name');
    }
    
    if (!tool.description) {
        errors.push('Tool must have a description');
    }
    
    if (typeof tool.isEnabled !== 'function') {
        errors.push('Tool must have an isEnabled() method');
    }
    
    if (!tool.inputSchema) {
        errors.push('Tool must have an input schema');
    }
    
    if (!tool.call || typeof tool.call !== 'function') {
        errors.push('Tool must have a call() method');
    }
    
    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Registers a custom tool
 * @param {Object} tool - Tool to register
 * @returns {boolean} True if registration successful
 */
function registerCustomTool(tool) {
    const validation = validateTool(tool);
    
    if (!validation.isValid) {
        console.error('Tool validation failed:', validation.errors);
        return false;
    }
    
    // Check for name conflicts
    if (getToolByName(tool.name)) {
        console.error(`Tool with name '${tool.name}' already exists`);
        return false;
    }
    
    // Add to core tools (in a real implementation, this might be a separate registry)
    CORE_TOOLS.push(tool);
    
    return true;
}

/**
 * Gets tool statistics
 * @param {Object} toolPermissionContext - Permission context
 * @param {boolean} todoFeatureEnabled - Todo feature flag
 * @returns {Object} Tool statistics
 */
function getToolStatistics(toolPermissionContext, todoFeatureEnabled = false) {
    const allTools = [
        ...CORE_TOOLS,
        ...Object.values(CONDITIONAL_TOOLS).flatMap(config => config.tools)
    ];
    
    const registeredTools = registerTools(toolPermissionContext, todoFeatureEnabled);
    
    return {
        totalAvailable: allTools.length,
        totalRegistered: registeredTools.length,
        coreTools: CORE_TOOLS.length,
        conditionalTools: Object.keys(CONDITIONAL_TOOLS).length,
        enabledTools: registeredTools.filter(tool => {
            try {
                return tool.isEnabled();
            } catch {
                return false;
            }
        }).length
    };
}

/**
 * Creates a tool context for execution
 * @param {Array} tools - Available tools
 * @param {Object} options - Context options
 * @returns {Object} Tool execution context
 */
function createToolContext(tools, options = {}) {
    return {
        tools,
        options: {
            debug: options.debug || false,
            verbose: options.verbose || false,
            timeout: options.timeout || 30000,
            ...options
        },
        getToolByName: (name) => tools.find(tool => tool.name === name),
        isToolAvailable: (name) => tools.some(tool => tool.name === name),
        validateToolInput: (toolName, input) => {
            const tool = tools.find(t => t.name === toolName);
            if (!tool) return { valid: false, error: 'Tool not found' };
            
            // TODO: Implement input validation against tool schema
            return { valid: true };
        }
    };
}

module.exports = {
    registerTools,
    getToolByName,
    getAllToolNames,
    validateTool,
    registerCustomTool,
    getToolStatistics,
    createToolContext,
    getPermissionRules,
    isToolDisabledByPermissions,
    CORE_TOOLS,
    CONDITIONAL_TOOLS
};
