/**
 * View Tool (hf)
 * 
 * Refactored version of the file viewing tool from the original code.
 * Handles file and directory viewing operations.
 * 
 * Original variable: hf
 */

const ViewTool = {
    name: "View",
    
    async description() {
        return "View files and directories";
    },
    
    async prompt() {
        return "Specify the file or directory to view";
    },
    
    get inputSchema() {
        return {
            type: "object",
            properties: {
                path: {
                    type: "string",
                    description: "Path to the file or directory to view"
                },
                lines: {
                    type: "object",
                    properties: {
                        start: { type: "integer" },
                        end: { type: "integer" }
                    },
                    description: "Line range to view (for files)"
                }
            },
            required: ["path"]
        };
    },
    
    isEnabled() {
        return true;
    },
    
    async* call(input, context) {
        // TODO: Implement file/directory viewing logic
        yield {
            type: "result",
            data: {
                content: `Content of ${input.path}`,
                type: "file"
            }
        };
    }
};

module.exports = { ViewTool };
