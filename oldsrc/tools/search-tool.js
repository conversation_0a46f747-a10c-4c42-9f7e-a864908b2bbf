/**
 * Search Tool (_w)
 * 
 * Refactored version of the code search tool from the original code.
 * Handles searching through files and directories.
 * 
 * Original variable: _w
 */

const SearchTool = {
    name: "Search",
    
    async description() {
        return "Search for text patterns in files";
    },
    
    async prompt() {
        return "Specify the search pattern and location";
    },
    
    get inputSchema() {
        return {
            type: "object",
            properties: {
                pattern: {
                    type: "string",
                    description: "Search pattern (regex supported)"
                },
                path: {
                    type: "string",
                    description: "Path to search in (file or directory)"
                },
                options: {
                    type: "object",
                    properties: {
                        caseSensitive: { type: "boolean" },
                        wholeWord: { type: "boolean" },
                        regex: { type: "boolean" }
                    }
                }
            },
            required: ["pattern"]
        };
    },
    
    isEnabled() {
        return true;
    },
    
    async* call(input, context) {
        // TODO: Implement search logic
        yield {
            type: "result",
            data: {
                matches: [],
                totalMatches: 0
            }
        };
    }
};

module.exports = { SearchTool };
