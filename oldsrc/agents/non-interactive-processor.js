/**
 * Non-Interactive Processor (W6B)
 * 
 * Refactored version of the non-interactive mode processor from the original code.
 * This handles SDK and CLI non-interactive execution with proper session management,
 * turn limiting, cost tracking, and result formatting.
 * 
 * Original function: W6B({commands, permissionContext, prompt, ...}) at line 353247
 */

const { mainAgentLoop } = require('./main-agent-loop');
const { processPromptWithHooks } = require('../core/input-processor');
const { buildSystemPrompts, calculateMaxThinkingTokens } = require('../core/prompt-builder');
const { normalizeMessages, getLastMessage } = require('../core/message-utils');
const { saveConversationHistory } = require('../core/conversation-storage');
const { trackEvent, generateSessionId, getCurrentAgentId } = require('../utils/analytics');
const { getApiKeyInfo, getCostInfo, getApiDuration } = require('../utils/api-utils');
const { setWorkingDirectory } = require('../utils/file-utils');
const { getApplicationState } = require('../core/app-state');

/**
 * Non-Interactive Processor - Handles SDK and CLI non-interactive execution
 * 
 * This processor provides:
 * 1. Session initialization and management
 * 2. Turn-based execution with configurable limits
 * 3. Cost and usage tracking
 * 4. Permission prompt tool integration
 * 5. Structured result formatting
 * 6. Error handling and recovery
 * 
 * @param {Object} config - Configuration object
 * @param {Array} config.commands - Available commands
 * @param {Object} config.permissionContext - Permission context
 * @param {string} config.prompt - User prompt
 * @param {string} config.cwd - Working directory
 * @param {Array} config.tools - Available tools
 * @param {Array} config.mcpClients - MCP clients
 * @param {boolean} config.verbose - Verbose mode
 * @param {number} config.maxTurns - Maximum turns limit
 * @param {Object} config.permissionPromptTool - Permission prompt tool
 * @param {Array} config.initialMessages - Initial messages
 * @param {string} config.customSystemPrompt - Custom system prompt
 * @param {string} config.appendSystemPrompt - Additional system prompt
 * @param {string} config.userSpecifiedModel - User-specified model
 * @param {string} config.fallbackModel - Fallback model
 * @param {Function} config.getQueuedCommands - Get queued commands function
 * @param {Function} config.removeQueuedCommands - Remove queued commands function
 * @param {AbortController} config.abortController - Abort controller
 * @yields {Object} Processing events and results
 */
async function* processNonInteractive({
    commands,
    permissionContext,
    prompt,
    cwd,
    tools,
    mcpClients,
    verbose = false,
    maxTurns,
    permissionPromptTool,
    initialMessages = [],
    customSystemPrompt,
    appendSystemPrompt,
    userSpecifiedModel,
    fallbackModel,
    getQueuedCommands = () => [],
    removeQueuedCommands = () => {},
    abortController
}) {
    // Set entrypoint for SDK mode
    if (!process.env.CLAUDE_CODE_ENTRYPOINT) {
        process.env.CLAUDE_CODE_ENTRYPOINT = "sdk-cli";
    }
    
    // Set working directory
    setWorkingDirectory(cwd);
    
    const startTime = Date.now();
    const sessionId = generateSessionId();
    
    // Determine model
    const model = userSpecifiedModel ? resolveModelAlias(userSpecifiedModel) : getDefaultModel();
    
    // Get system configuration
    const [
        systemPrompts,
        additionalContext,
        contextAnalysis
    ] = await Promise.all([
        buildSystemPrompts(
            tools,
            model,
            Array.from(permissionContext.additionalWorkingDirectories),
            mcpClients
        ),
        getAdditionalContext(),
        getContextAnalysis()
    ]);
    
    // Build final system prompts
    const finalSystemPrompts = [
        ...(customSystemPrompt ? [customSystemPrompt] : systemPrompts),
        ...(appendSystemPrompt ? [appendSystemPrompt] : [])
    ];
    
    // Normalize initial messages
    let messages = normalizeMessages(initialMessages);
    
    // Create initial execution context
    let executionContext = createExecutionContext({
        commands,
        tools,
        mcpClients,
        permissionContext,
        verbose,
        model,
        messages,
        abortController: abortController ?? new AbortController(),
        getQueuedCommands: () => [],
        removeQueuedCommands: () => {}
    });
    
    // Process initial prompt
    const promptResult = await processPromptWithHooks(
        prompt,
        "prompt",
        () => {}, // setToolJSX - not used in non-interactive mode
        {
            ...executionContext,
            messages
        },
        null, // pastedContents
        null, // ideSelection
        undefined, // additionalContext
        undefined, // customInstructions
        messages
    );
    
    messages = [...messages, ...promptResult.messages];
    
    // Update execution context with new thinking tokens
    const maxThinkingTokens = calculateMaxThinkingTokens(messages);
    if (maxThinkingTokens > 0) {
        executionContext = {
            ...executionContext,
            options: {
                ...executionContext.options,
                maxThinkingTokens
            },
            abortController: new AbortController(),
            getQueuedCommands,
            removeQueuedCommands,
            agentId: getCurrentAgentId()
        };
    }
    
    // Create permission handler with prompt tool support
    const permissionHandler = createPermissionHandler(
        permissionPromptTool,
        abortController
    );
    
    // Yield session initialization
    yield {
        type: "system",
        subtype: "init",
        cwd,
        session_id: sessionId,
        tools: tools.map(tool => tool.name),
        mcp_servers: mcpClients.map(client => ({
            name: client.name,
            status: client.type
        })),
        model,
        permissionMode: permissionContext.mode,
        apiKeySource: getApiKeyInfo(true).source
    };
    
    // Initialize usage tracking
    let totalUsage = getInitialUsage();
    let turnCount = 0;
    
    // Main processing loop
    for await (const event of mainAgentLoop(
        messages,
        finalSystemPrompts,
        additionalContext,
        contextAnalysis,
        permissionHandler,
        executionContext,
        undefined, // turnInfo
        fallbackModel
    )) {
        // Update messages for assistant and user events
        if (event.type === "assistant" || event.type === "user") {
            messages.push(event);
            await saveConversationHistory(messages);
        }
        
        // Process different event types
        switch (event.type) {
            case "assistant":
            case "progress":
            case "user":
                yield* formatEventForOutput(event, sessionId);
                break;
                
            case "stream_event":
                if (event.event.type === "message_start") {
                    totalUsage = mergeUsage(totalUsage, event.event.message.usage);
                }
                if (event.event.type === "message_delta") {
                    totalUsage = mergeUsage(totalUsage, event.event.usage);
                }
                break;
                
            case "attachment":
            case "stream_request_start":
            case "system":
                // These events are not yielded in non-interactive mode
                break;
        }
        
        // Check turn limit
        if (event.type === "user" && maxTurns && ++turnCount >= maxTurns) {
            yield {
                type: "result",
                subtype: "error_max_turns",
                duration_ms: Date.now() - startTime,
                duration_api_ms: getApiDuration(),
                is_error: false,
                num_turns: turnCount,
                session_id: sessionId,
                total_cost_usd: getCostInfo(),
                usage: totalUsage
            };
            return;
        }
    }
    
    // Process final results
    const lastMessage = getLastMessage(messages);
    
    if (!lastMessage || lastMessage.type !== "assistant") {
        yield {
            type: "result",
            subtype: "error_during_execution",
            duration_ms: Date.now() - startTime,
            duration_api_ms: getApiDuration(),
            is_error: false,
            num_turns: turnCount,
            session_id: sessionId,
            total_cost_usd: getCostInfo(),
            usage: totalUsage
        };
        return;
    }
    
    // Extract result content
    const firstContent = getLastMessage(lastMessage.message.content);
    if (firstContent?.type !== "text" && 
        firstContent?.type !== "thinking" && 
        firstContent?.type !== "redacted_thinking") {
        throw new Error(
            `Expected first content item to be text or thinking, but got ${JSON.stringify(lastMessage.message.content[0], null, 2)}`
        );
    }
    
    // Yield success result
    yield {
        type: "result",
        subtype: "success",
        is_error: Boolean(lastMessage.isApiErrorMessage),
        duration_ms: Date.now() - startTime,
        duration_api_ms: getApiDuration(),
        num_turns: messages.length - 1,
        result: firstContent.type === "text" ? firstContent.text : "",
        session_id: sessionId,
        total_cost_usd: getCostInfo(),
        usage: totalUsage
    };
}

/**
 * Formats events for non-interactive output
 * @param {Object} event - Event to format
 * @param {string} sessionId - Session ID
 * @yields {Object} Formatted events
 */
function* formatEventForOutput(event, sessionId) {
    switch (event.type) {
        case "assistant":
            for (const normalizedMessage of normalizeMessages([event])) {
                yield {
                    type: "assistant",
                    message: normalizedMessage.message,
                    parent_tool_use_id: null,
                    session_id: sessionId
                };
            }
            return;
            
        case "progress":
            yield {
                type: "progress",
                data: event.data,
                tool_use_id: event.toolUseID,
                session_id: sessionId
            };
            return;
            
        case "user":
            yield {
                type: "user",
                message: event.message,
                session_id: sessionId
            };
            return;
    }
}

/**
 * Creates execution context for non-interactive mode
 * @param {Object} config - Configuration
 * @returns {Object} Execution context
 */
function createExecutionContext(config) {
    return {
        messages: config.messages,
        setMessages: () => {},
        onChangeAPIKey: () => {},
        options: {
            commands: config.commands,
            debug: false,
            tools: config.tools,
            verbose: config.verbose,
            mainLoopModel: config.model,
            maxThinkingTokens: calculateMaxThinkingTokens(config.messages),
            mcpClients: config.mcpClients,
            mcpResources: {},
            ideInstallationStatus: null,
            isNonInteractiveSession: true,
            theme: getApplicationState().theme
        },
        getToolPermissionContext: () => config.permissionContext,
        getQueuedCommands: config.getQueuedCommands,
        removeQueuedCommands: config.removeQueuedCommands,
        abortController: config.abortController,
        readFileState: {},
        setInProgressToolUseIDs: () => {},
        setToolPermissionContext: () => {},
        agentId: getCurrentAgentId()
    };
}

/**
 * Creates permission handler with prompt tool support
 * @param {Object} permissionPromptTool - Permission prompt tool
 * @param {AbortController} abortController - Abort controller
 * @returns {Function} Permission handler function
 */
function createPermissionHandler(permissionPromptTool, abortController) {
    return async (tool, input, context, executionContext, toolUseId) => {
        // First check standard permissions
        const standardResult = await checkStandardPermissions(tool, input, context, executionContext, toolUseId);
        
        if (standardResult.behavior === "allow" || standardResult.behavior === "deny") {
            return standardResult;
        }
        
        // Use permission prompt tool if available
        if (permissionPromptTool) {
            for await (const result of permissionPromptTool.call({
                tool_name: tool.name,
                input,
                tool_use_id: toolUseId
            }, context, createPermissionHandler(permissionPromptTool, abortController), executionContext)) {
                
                if (result.type !== "result") {
                    continue;
                }
                
                if (abortController?.signal.aborted) {
                    return {
                        behavior: "deny",
                        message: "Permission prompt was aborted.",
                        decisionReason: {
                            type: "permissionPromptTool",
                            permissionPromptToolName: tool.name,
                            toolResult: result
                        },
                        ruleSuggestions: null
                    };
                }
                
                const toolResult = permissionPromptTool.mapToolResultToToolResultBlockParam(result.data, "1");
                
                if (!toolResult.content || 
                    !Array.isArray(toolResult.content) || 
                    !toolResult.content[0] || 
                    toolResult.content[0].type !== "text" || 
                    typeof toolResult.content[0].text !== "string") {
                    throw new Error('Permission prompt tool returned an invalid result. Expected a single text block param with type="text" and a string text value.');
                }
                
                return parsePermissionPromptResult(
                    parsePermissionResponse(toolResult.content[0].text),
                    permissionPromptTool.name
                );
            }
        }
        
        return standardResult;
    };
}

// Utility functions (TODO: Implement these)
function resolveModelAlias(alias) {
    // TODO: Implement am() function
    return alias;
}

function getDefaultModel() {
    // TODO: Implement M7() function
    return "claude-3-sonnet";
}

async function getAdditionalContext() {
    // TODO: Implement AY() function
    return [];
}

async function getContextAnalysis() {
    // TODO: Implement iE() function
    return {};
}

function getInitialUsage() {
    // TODO: Implement Ji constant
    return {
        input_tokens: 0,
        output_tokens: 0,
        cache_creation_input_tokens: 0,
        cache_read_input_tokens: 0
    };
}

function mergeUsage(usage1, usage2) {
    // TODO: Implement Ax() function
    return {
        input_tokens: (usage1.input_tokens || 0) + (usage2.input_tokens || 0),
        output_tokens: (usage1.output_tokens || 0) + (usage2.output_tokens || 0),
        cache_creation_input_tokens: (usage1.cache_creation_input_tokens || 0) + (usage2.cache_creation_input_tokens || 0),
        cache_read_input_tokens: (usage1.cache_read_input_tokens || 0) + (usage2.cache_read_input_tokens || 0)
    };
}

async function checkStandardPermissions(tool, input, context, executionContext, toolUseId) {
    // TODO: Implement jR() function
    return { behavior: "allow" };
}

function parsePermissionResponse(text) {
    // TODO: Implement S4() and Z6B.parse()
    return {};
}

function parsePermissionPromptResult(parsed, toolName) {
    // TODO: Implement F6B() function
    return { behavior: "allow" };
}

module.exports = {
    processNonInteractive,
    formatEventForOutput,
    createExecutionContext,
    createPermissionHandler
};
