/**
 * Main Agent Loop Engine (AH)
 * 
 * Refactored version of the core conversation processing engine from the original code.
 * This is the heart of the Agent system - an async generator that orchestrates the entire
 * conversation flow including message processing, tool execution, model fallbacks, and recursion.
 * 
 * Original function: AH(A, B, Q, I, D, G, Z, F, Y) at line 335264
 */

const { processMessageCompaction, autoCompactMessages } = require('../core/message-compaction');
const { buildSystemPrompt, buildToolPrompts } = require('../core/prompt-builder');
const { processAIResponse } = require('../core/ai-response-processor');
const { executeTools } = require('../tools/tool-executor');
const { processQueuedCommands } = require('../commands/command-queue-processor');
const { processStopHooks } = require('../hooks/stop-hook-processor');
const { trackEvent, generateTurnId } = require('../utils/analytics');
const { createInterruptMessage, createWarningMessage } = require('../core/message-factory');

/**
 * Main Agent Loop Engine - Core conversation processing
 * 
 * This is the central nervous system of the entire application. It:
 * 1. Manages message compaction and auto-compaction
 * 2. Orchestrates AI response generation with fallback models
 * 3. Executes tools with proper concurrency control
 * 4. Handles recursive conversation continuation
 * 5. Manages turn tracking and conversation state
 * 
 * @param {Array} messages - Current conversation messages
 * @param {Array} systemPrompts - System prompts array
 * @param {Array} additionalContext - Additional context data
 * @param {Object} contextAnalysis - Context analysis results
 * @param {Object} toolContext - Tool execution context
 * @param {Object} executionContext - Main execution context
 * @param {Object} turnInfo - Turn tracking information
 * @param {string} fallbackModel - Fallback model name
 * @param {boolean} isStopHookActive - Stop hook active flag
 * @yields {Object} Stream of conversation events and results
 */
async function* mainAgentLoop(
    messages,
    systemPrompts,
    additionalContext,
    contextAnalysis,
    toolContext,
    executionContext,
    turnInfo,
    fallbackModel,
    isStopHookActive
) {
    // Signal start of stream processing
    yield { type: "stream_request_start" };
    
    let currentMessages = messages;
    let currentTurnInfo = turnInfo;
    
    // Phase 1: Message Compaction
    const compactionResult = await processMessageCompaction(currentMessages);
    currentMessages = compactionResult.messages;
    
    if (compactionResult.compactionInfo?.systemMessage) {
        yield compactionResult.compactionInfo.systemMessage;
    }
    
    // Phase 2: Auto-compaction (92% threshold)
    const { messages: processedMessages, wasCompacted } = await autoCompactMessages(
        currentMessages, 
        executionContext
    );
    
    if (wasCompacted) {
        trackEvent("tengu_auto_compact_succeeded", {
            originalMessageCount: messages.length,
            compactedMessageCount: processedMessages.length
        });
        
        // Update turn info for compacted conversations
        if (!currentTurnInfo?.compacted) {
            currentTurnInfo = {
                compacted: true,
                turnId: generateTurnId(),
                turnCounter: 0
            };
        }
        
        currentMessages = processedMessages;
    }
    
    // Phase 3: AI Response Generation with Model Fallback
    const assistantMessages = [];
    let currentModel = executionContext.options.mainLoopModel;
    let shouldContinue = true;
    
    try {
        while (shouldContinue) {
            shouldContinue = false;
            
            try {
                let streamingFallbackTriggered = false;
                
                // Generate AI response with streaming
                for await (const response of processAIResponse(
                    buildSystemPrompt(currentMessages, systemPrompts),
                    buildToolPrompts(additionalContext, contextAnalysis),
                    executionContext.options.maxThinkingTokens,
                    executionContext.options.tools,
                    executionContext.abortController.signal,
                    {
                        getToolPermissionContext: executionContext.getToolPermissionContext,
                        model: currentModel,
                        prependCLISysprompt: true,
                        toolChoice: undefined,
                        isNonInteractiveSession: executionContext.options.isNonInteractiveSession,
                        fallbackModel,
                        onStreamingFallback: () => {
                            streamingFallbackTriggered = true;
                        }
                    }
                )) {
                    // Handle streaming fallback
                    if (streamingFallbackTriggered) {
                        yield* handleStreamingFallback(assistantMessages, "Streaming fallback triggered");
                        assistantMessages.length = 0;
                    }
                    
                    yield response;
                    
                    if (response.type === "assistant") {
                        assistantMessages.push(response);
                    }
                }
                
            } catch (error) {
                // Handle model fallback
                if (error instanceof ModelFallbackError && fallbackModel) {
                    currentModel = fallbackModel;
                    shouldContinue = true;
                    
                    yield* handleStreamingFallback(assistantMessages, "Model fallback triggered");
                    assistantMessages.length = 0;
                    
                    executionContext.options.mainLoopModel = fallbackModel;
                    
                    trackEvent("tengu_model_fallback_triggered", {
                        original_model: error.originalModel,
                        fallback_model: fallbackModel,
                        entrypoint: "cli"
                    });
                    
                    yield createWarningMessage(
                        `Model fallback triggered: switching from ${error.originalModel} to ${error.fallbackModel}`
                    );
                    continue;
                }
                
                throw error;
            }
        }
        
    } catch (error) {
        console.error('Main agent loop error:', error);
        
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        trackEvent("tengu_query_error", {
            assistantMessages: assistantMessages.length,
            toolUses: assistantMessages.flatMap(msg => 
                msg.message.content.filter(content => content.type === "tool_use")
            ).length
        });
        
        yield* handleStreamingFallback(assistantMessages, errorMessage);
        yield createInterruptMessage(false, executionContext);
        return;
    }
    
    // Check for abort
    if (executionContext.abortController.signal.aborted) {
        yield* handleStreamingFallback(assistantMessages, "Interrupted by user");
        yield createInterruptMessage(false, executionContext);
        return;
    }
    
    // Phase 4: Handle No Assistant Messages
    if (!assistantMessages.length) {
        yield* processStopHooks(
            currentMessages,
            assistantMessages,
            systemPrompts,
            additionalContext,
            contextAnalysis,
            toolContext,
            executionContext,
            currentTurnInfo,
            fallbackModel,
            isStopHookActive
        );
        return;
    }
    
    // Phase 5: Tool Execution
    const toolUses = assistantMessages.flatMap(msg => 
        msg.message.content.filter(content => content.type === "tool_use")
    );
    
    // Handle case with no tool uses
    if (!toolUses.length) {
        yield* processStopHooks(
            currentMessages,
            assistantMessages,
            systemPrompts,
            additionalContext,
            contextAnalysis,
            toolContext,
            executionContext,
            currentTurnInfo,
            fallbackModel,
            isStopHookActive
        );
        return;
    }
    
    // Execute tools with concurrency control
    const toolResults = [];
    let preventContinuation = false;
    
    for await (const toolResult of executeTools(
        toolUses, 
        assistantMessages, 
        toolContext, 
        executionContext
    )) {
        yield toolResult;
        
        if (toolResult && toolResult.type === "system" && toolResult.preventContinuation) {
            preventContinuation = true;
        }
        
        toolResults.push(...filterUserMessages([toolResult]));
    }
    
    // Check for abort after tool execution
    if (executionContext.abortController.signal.aborted) {
        yield createInterruptMessage(true, executionContext);
        return;
    }
    
    // Check if continuation is prevented
    if (preventContinuation) {
        return;
    }
    
    // Phase 6: Turn Counter Management
    if (currentTurnInfo?.compacted) {
        currentTurnInfo.turnCounter++;
        trackEvent("tengu_post_autocompact_turn", {
            turnId: currentTurnInfo.turnId,
            turnCounter: currentTurnInfo.turnCounter
        });
    }
    
    // Phase 7: Process Queued Commands
    const queuedCommands = [...executionContext.getQueuedCommands()];
    for await (const commandResult of processQueuedCommands(
        null, 
        executionContext, 
        null, 
        queuedCommands, 
        undefined, 
        messages
    )) {
        yield commandResult;
        toolResults.push(commandResult);
    }
    executionContext.removeQueuedCommands(queuedCommands);
    
    // Phase 8: Rate Limit Fallback
    const finalExecutionContext = shouldUseRateLimitFallback() ? {
        ...executionContext,
        options: {
            ...executionContext.options,
            mainLoopModel: getRateLimitFallbackModel()
        }
    } : executionContext;
    
    if (shouldUseRateLimitFallback() && 
        getRateLimitFallbackModel() !== executionContext.options.mainLoopModel) {
        
        trackEvent("tengu_fallback_system_msg", {
            mainLoopModel: executionContext.options.mainLoopModel,
            fallbackModel: getRateLimitFallbackModel()
        });
        
        yield createWarningMessage(
            `Claude Opus 4 limit reached, now using ${formatModelName(getRateLimitFallbackModel())}`
        );
    }
    
    // Phase 9: Recursive Continuation
    yield* mainAgentLoop(
        [...currentMessages, ...assistantMessages, ...toolResults],
        systemPrompts,
        additionalContext,
        contextAnalysis,
        toolContext,
        finalExecutionContext,
        currentTurnInfo,
        fallbackModel,
        isStopHookActive
    );
}

// Utility functions (TODO: Implement these)
async function* handleStreamingFallback(messages, reason) {
    // Handle streaming fallback when streaming fails
    // Original implementation based on onStreamingFallback at line 344145

    const { createSystemMessage } = require('../core/message-processor');
    const { trackEvent } = require('../analytics/analytics-system');

    // Track the fallback event
    trackEvent("streaming_fallback", {
        reason: reason,
        messageCount: messages.length,
        timestamp: Date.now()
    });

    // Create a system message to inform about the fallback
    yield createSystemMessage({
        content: `Streaming fallback triggered: ${reason}. Switching to non-streaming mode.`,
        isMeta: true,
        timestamp: new Date().toISOString()
    });

    // Log the fallback for debugging
    console.warn(`Streaming fallback: ${reason}`);
}

function filterUserMessages(messages) {
    return messages.filter(msg => msg.type === "user");
}

function shouldUseRateLimitFallback() {
    // Check if rate limit fallback should be used
    // Based on maxRateLimitFallbackActive at line 276118

    try {
        const { getConfigValue } = require('../config/config-manager');
        const { getCurrentRateLimitStatus } = require('../api/rate-limit-manager');

        // Check if fallback is enabled in config
        const fallbackEnabled = getConfigValue('maxRateLimitFallbackActive', false);
        if (!fallbackEnabled) {
            return false;
        }

        // Check current rate limit status
        const rateLimitStatus = getCurrentRateLimitStatus();
        return rateLimitStatus.status === 'limited' && rateLimitStatus.unifiedRateLimitFallbackAvailable;

    } catch (error) {
        console.error('Error checking rate limit fallback:', error);
        return false;
    }
}

function getRateLimitFallbackModel() {
    // Get the fallback model for rate limiting
    // Based on gX() function usage at line 327591

    try {
        const { getConfigValue } = require('../config/config-manager');

        // Try to get fallback model from config
        const fallbackModel = getConfigValue('fallbackModel', null);
        if (fallbackModel) {
            return fallbackModel;
        }

        // Default fallback models in order of preference
        const defaultFallbacks = [
            "claude-3-sonnet-20240229",
            "claude-3-haiku-20240307",
            "claude-3-sonnet"
        ];

        return defaultFallbacks[0];

    } catch (error) {
        console.error('Error getting fallback model:', error);
        return "claude-3-sonnet";
    }
}

function formatModelName(modelName) {
    // Format model name for display
    // Based on ry() function usage at line 351200

    if (!modelName) {
        return "Unknown Model";
    }

    // Model name mappings for display
    const modelDisplayNames = {
        "claude-3-opus-20240229": "Claude 3 Opus",
        "claude-3-sonnet-20240229": "Claude 3 Sonnet",
        "claude-3-haiku-20240307": "Claude 3 Haiku",
        "claude-3-5-sonnet-20241022": "Claude 3.5 Sonnet",
        "claude-3-5-haiku-20241022": "Claude 3.5 Haiku"
    };

    // Return display name if available, otherwise return original
    return modelDisplayNames[modelName] || modelName;
}

class ModelFallbackError extends Error {
    constructor(originalModel, fallbackModel) {
        super(`Model fallback from ${originalModel} to ${fallbackModel}`);
        this.originalModel = originalModel;
        this.fallbackModel = fallbackModel;
    }
}

module.exports = {
    mainAgentLoop,
    ModelFallbackError
};
