/**
 * Task Agent Executor (q5A)
 * 
 * Refactored version of the task agent execution system from the original code.
 * This is a specialized agent that handles individual task execution with isolation,
 * progress tracking, and result synthesis capabilities.
 * 
 * Original function: q5A(A, B, Q, I, D, G={}) at line 338316
 */

const { mainAgentLoop } = require('./main-agent-loop');
const { createUserMessage } = require('../core/message-factory');
const { generateAgentId, calculateMaxThinkingTokens } = require('../utils/agent-utils');
const { getSystemPrompt, getDefaultModel } = require('../core/system-config');
const { trackEvent } = require('../utils/analytics');
const { normalizeMessages, getLastMessage, isInterrupted } = require('../core/message-utils');
const { saveConversationHistory } = require('../core/conversation-storage');

/**
 * Task Agent Executor - Specialized agent for task processing
 * 
 * This agent provides:
 * 1. Isolated execution environment for individual tasks
 * 2. Progress tracking and real-time updates
 * 3. Tool use counting and token usage tracking
 * 4. Exit plan mode detection and handling
 * 5. Result synthesis capabilities
 * 
 * @param {string} prompt - Task prompt to execute
 * @param {number} agentIndex - Agent index for identification
 * @param {Object} toolContext - Tool execution context
 * @param {Object} sessionContext - Session context data
 * @param {Object} agentContext - Agent-specific context
 * @param {Object} options - Execution options
 * @param {boolean} options.isSynthesis - Whether this is a synthesis agent
 * @param {string} options.systemPrompt - Custom system prompt
 * @param {string} options.model - Model to use for this agent
 * @yields {Object} Agent execution progress and results
 */
async function* executeTaskAgent(
    prompt,
    agentIndex,
    toolContext,
    sessionContext,
    agentContext,
    options = {}
) {
    const {
        abortController,
        options: {
            debug,
            verbose,
            isNonInteractiveSession
        },
        getToolPermissionContext,
        readFileState,
        setInProgressToolUseIDs,
        getQueuedCommands,
        removeQueuedCommands,
        tools
    } = toolContext;
    
    const {
        isSynthesis = false,
        systemPrompt,
        model
    } = options;
    
    // Generate unique agent ID
    const agentId = generateAgentId();
    
    // Create initial user message
    const initialMessages = [createUserMessage({ content: prompt })];
    
    // Get system configuration
    const [
        additionalContext,
        contextAnalysis,
        defaultModel
    ] = await Promise.all([
        getAdditionalContext(), // TODO: Implement AY()
        getContextAnalysis(), // TODO: Implement iE()
        model ?? getDefaultModel() // TODO: Implement M7()
    ]);
    
    // Build system prompt
    const systemPromptText = systemPrompt ?? await getSystemPrompt(
        defaultModel,
        Array.from(toolContext.getToolPermissionContext().additionalWorkingDirectories)
    ); // TODO: Implement AL1()
    
    // Initialize tracking variables
    const allMessages = [];
    let toolUseCount = 0;
    let exitPlanModeInput = undefined;
    
    // Create agent execution context
    const agentExecutionContext = {
        abortController,
        options: {
            isNonInteractiveSession: isNonInteractiveSession ?? false,
            tools,
            commands: [], // Task agents don't have access to commands
            debug,
            verbose,
            mainLoopModel: defaultModel,
            maxThinkingTokens: calculateMaxThinkingTokens(initialMessages),
            mcpClients: [],
            mcpResources: {}
        },
        getToolPermissionContext,
        readFileState,
        getQueuedCommands,
        removeQueuedCommands,
        setInProgressToolUseIDs,
        agentId
    };
    
    // Execute main agent loop
    for await (const message of mainAgentLoop(
        initialMessages,
        systemPromptText,
        additionalContext,
        contextAnalysis,
        agentContext,
        agentExecutionContext
    )) {
        // Filter relevant message types
        if (message.type !== "assistant" && 
            message.type !== "user" && 
            message.type !== "progress") {
            continue;
        }
        
        allMessages.push(message);
        
        // Skip progress messages for tool use counting
        if (message.type !== "assistant" && message.type !== "user") {
            continue;
        }
        
        // Process message content for tool uses and exit plan detection
        const normalizedCurrentMessages = normalizeMessages(allMessages);
        
        for (const normalizedMessage of normalizeMessages([message])) {
            for (const contentItem of normalizedMessage.message.content) {
                if (contentItem.type !== "tool_use" && contentItem.type !== "tool_result") {
                    continue;
                }
                
                if (contentItem.type === "tool_use") {
                    toolUseCount++;
                    
                    // Check for exit plan mode
                    if (contentItem.name === "exit_plan_mode" && contentItem.input) {
                        const exitPlanValidation = validateExitPlanInput(contentItem.input); // TODO: Implement
                        if (exitPlanValidation.success) {
                            exitPlanModeInput = {
                                plan: exitPlanValidation.data.plan
                            };
                        }
                    }
                }
                
                // Yield progress update
                yield {
                    type: "progress",
                    toolUseID: isSynthesis 
                        ? `synthesis_${sessionContext.message.id}` 
                        : `agent_${agentIndex}_${sessionContext.message.id}`,
                    data: {
                        message: normalizedMessage,
                        normalizedMessages: normalizedCurrentMessages,
                        type: "agent_progress"
                    }
                };
            }
        }
    }
    
    // Process final results
    const relevantMessages = allMessages.filter(msg => 
        msg.type !== "system" && msg.type !== "progress"
    );
    
    const lastMessage = getLastMessage(relevantMessages);
    
    // Check for interruption
    if (lastMessage && isInterrupted(lastMessage)) {
        if (exitPlanModeInput) {
            throw new TaskInterruptedError(`${getInterruptPrefix()}${exitPlanModeInput.plan}`); // TODO: Implement i3, Rf
        } else {
            throw new TaskInterruptedError(); // TODO: Implement i3
        }
    }
    
    // Validate final message
    if (lastMessage?.type !== "assistant") {
        const errorMessage = isSynthesis 
            ? "Synthesis: Last message was not an assistant message"
            : `Agent ${agentIndex + 1}: Last message was not an assistant message`;
        throw new Error(errorMessage);
    }
    
    // Calculate token usage
    const usage = lastMessage.message.usage;
    const totalTokens = (usage.cache_creation_input_tokens ?? 0) + 
                       (usage.cache_read_input_tokens ?? 0) + 
                       usage.input_tokens + 
                       usage.output_tokens;
    
    // Extract text content
    const textContent = lastMessage.message.content.filter(item => item.type === "text");
    
    // Save conversation history
    await saveConversationHistory([...initialMessages, ...allMessages]); // TODO: Implement NX1()
    
    // Track agent completion
    trackEvent("tengu_task_agent_completed", {
        agentIndex,
        isSynthesis,
        toolUseCount,
        totalTokens,
        hasExitPlan: !!exitPlanModeInput
    });
    
    // Yield final result
    yield {
        type: "result",
        data: {
            agentIndex,
            content: textContent,
            toolUseCount,
            tokens: totalTokens,
            usage: lastMessage.message.usage,
            exitPlanModeInput
        }
    };
}

/**
 * Creates a synthesis agent for combining multiple task results
 * @param {string} synthesisPrompt - Synthesis prompt
 * @param {Object} toolContext - Tool context
 * @param {Object} sessionContext - Session context
 * @param {Object} agentContext - Agent context
 * @returns {AsyncGenerator} Synthesis agent generator
 */
async function* createSynthesisAgent(synthesisPrompt, toolContext, sessionContext, agentContext) {
    return yield* executeTaskAgent(
        synthesisPrompt,
        0, // Synthesis agent always has index 0
        toolContext,
        sessionContext,
        agentContext,
        { isSynthesis: true }
    );
}

/**
 * Creates multiple parallel task agents
 * @param {string} basePrompt - Base prompt for all agents
 * @param {number} agentCount - Number of agents to create
 * @param {Object} toolContext - Tool context
 * @param {Object} sessionContext - Session context
 * @param {Object} agentContext - Agent context
 * @returns {Array} Array of agent generators
 */
function createParallelTaskAgents(basePrompt, agentCount, toolContext, sessionContext, agentContext) {
    const agents = [];
    
    for (let i = 0; i < agentCount; i++) {
        const enhancedPrompt = `${basePrompt}\n\nProvide a thorough and complete analysis.`;
        
        agents.push(executeTaskAgent(
            enhancedPrompt,
            i,
            toolContext,
            sessionContext,
            agentContext
        ));
    }
    
    return agents;
}

// Utility functions (TODO: Implement these)
async function getAdditionalContext() {
    // TODO: Implement AY() function
    return [];
}

async function getContextAnalysis() {
    // TODO: Implement iE() function
    return {};
}

function validateExitPlanInput(input) {
    // TODO: Implement exit plan input validation
    return { success: false };
}

function getInterruptPrefix() {
    // TODO: Implement Rf constant
    return "Task interrupted: ";
}

class TaskInterruptedError extends Error {
    constructor(message = "Task was interrupted") {
        super(message);
        this.name = "TaskInterruptedError";
    }
}

module.exports = {
    executeTaskAgent,
    createSynthesisAgent,
    createParallelTaskAgents,
    TaskInterruptedError
};
