/**
 * Message Processor
 * 
 * Handles message formatting, validation, and processing for the conversation system.
 * Manages different message types and their transformations.
 */

// Message types
const MESSAGE_TYPES = {
    USER: "user",
    ASSISTANT: "assistant", 
    SYSTEM: "system",
    TOOL_USE: "tool_use",
    TOOL_RESULT: "tool_result",
    PROGRESS: "progress",
    INFO: "info",
    WARNING: "warning",
    ERROR: "error"
};

// Content types
const CONTENT_TYPES = {
    TEXT: "text",
    IMAGE: "image",
    TOOL_USE: "tool_use",
    TOOL_RESULT: "tool_result"
};

/**
 * Creates a user message
 * @param {Object} options - Message options
 * @param {string|Array} options.content - Message content
 * @param {boolean} options.isVisibleInTranscriptOnly - Whether message is transcript-only
 * @returns {Object} User message object
 */
function createUserMessage({ content, isVisibleInTranscriptOnly = false }) {
    return {
        type: MESSAGE_TYPES.USER,
        message: {
            content: typeof content === 'string' ? content : content,
            isVisibleInTranscriptOnly
        },
        timestamp: new Date().toISOString(),
        uuid: generateMessageId()
    };
}

/**
 * Creates a system message
 * @param {string} content - Message content
 * @param {Object} options - Additional options
 * @returns {Object} System message object
 */
function createSystemMessage(content, options = {}) {
    return {
        type: MESSAGE_TYPES.SYSTEM,
        message: {
            content,
            ...options
        },
        timestamp: new Date().toISOString(),
        uuid: generateMessageId()
    };
}

/**
 * Creates an assistant message
 * @param {Object} options - Message options
 * @param {Array} options.content - Message content array
 * @param {Object} options.usage - Token usage information
 * @returns {Object} Assistant message object
 */
function createAssistantMessage({ content, usage }) {
    return {
        type: MESSAGE_TYPES.ASSISTANT,
        message: {
            content: Array.isArray(content) ? content : [{ type: CONTENT_TYPES.TEXT, text: content }],
            usage
        },
        timestamp: new Date().toISOString(),
        uuid: generateMessageId()
    };
}

/**
 * Creates a tool use message
 * @param {Object} options - Tool use options
 * @param {string} options.toolName - Name of the tool
 * @param {string} options.toolUseId - Tool use ID
 * @param {Object} options.input - Tool input
 * @returns {Object} Tool use content object
 */
function createToolUse({ toolName, toolUseId, input }) {
    return {
        type: CONTENT_TYPES.TOOL_USE,
        id: toolUseId,
        name: toolName,
        input
    };
}

/**
 * Creates a tool result message
 * @param {Object} options - Tool result options
 * @param {string} options.toolUseId - Tool use ID
 * @param {string|Array} options.content - Result content
 * @param {boolean} options.isError - Whether result is an error
 * @returns {Object} Tool result content object
 */
function createToolResult({ toolUseId, content, isError = false }) {
    return {
        type: CONTENT_TYPES.TOOL_RESULT,
        tool_use_id: toolUseId,
        content: Array.isArray(content) ? content : [{ type: CONTENT_TYPES.TEXT, text: content }],
        is_error: isError
    };
}

/**
 * Creates a progress message
 * @param {Object} options - Progress options
 * @param {string} options.type - Progress type
 * @param {Object} options.data - Progress data
 * @param {string} options.toolUseId - Associated tool use ID
 * @returns {Object} Progress message object
 */
function createProgressMessage({ type, data, toolUseId }) {
    return {
        type: MESSAGE_TYPES.PROGRESS,
        data: {
            type,
            ...data
        },
        toolUseID: toolUseId,
        timestamp: new Date().toISOString(),
        uuid: generateMessageId()
    };
}

/**
 * Creates an image content object
 * @param {Object} options - Image options
 * @param {string} options.data - Base64 image data
 * @param {string} options.mediaType - Image media type
 * @returns {Object} Image content object
 */
function createImageContent({ data, mediaType = "image/png" }) {
    return {
        type: CONTENT_TYPES.IMAGE,
        source: {
            type: "base64",
            media_type: mediaType,
            data
        }
    };
}

/**
 * Validates a message object
 * @param {Object} message - Message to validate
 * @returns {Object} Validation result
 */
function validateMessage(message) {
    const errors = [];
    
    if (!message) {
        errors.push("Message is required");
        return { isValid: false, errors };
    }
    
    if (!message.type || !Object.values(MESSAGE_TYPES).includes(message.type)) {
        errors.push(`Invalid message type: ${message.type}`);
    }
    
    if (!message.message && message.type !== MESSAGE_TYPES.PROGRESS) {
        errors.push("Message content is required");
    }
    
    if (message.type === MESSAGE_TYPES.USER || message.type === MESSAGE_TYPES.ASSISTANT) {
        if (!message.message.content) {
            errors.push("Message content is required for user/assistant messages");
        }
    }
    
    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Normalizes message content to a consistent format
 * @param {string|Array|Object} content - Content to normalize
 * @returns {Array} Normalized content array
 */
function normalizeMessageContent(content) {
    if (typeof content === 'string') {
        return [{ type: CONTENT_TYPES.TEXT, text: content }];
    }
    
    if (Array.isArray(content)) {
        return content.map(item => {
            if (typeof item === 'string') {
                return { type: CONTENT_TYPES.TEXT, text: item };
            }
            return item;
        });
    }
    
    if (content && typeof content === 'object') {
        if (content.type) {
            return [content];
        }
        return [{ type: CONTENT_TYPES.TEXT, text: JSON.stringify(content) }];
    }
    
    return [{ type: CONTENT_TYPES.TEXT, text: String(content) }];
}

/**
 * Extracts tool uses from assistant messages
 * @param {Array} messages - Array of messages
 * @returns {Array} Array of tool use objects
 */
function extractToolUses(messages) {
    return messages
        .filter(msg => msg.type === MESSAGE_TYPES.ASSISTANT)
        .flatMap(msg => {
            const content = Array.isArray(msg.message.content) 
                ? msg.message.content 
                : [msg.message.content];
            
            return content.filter(item => item.type === CONTENT_TYPES.TOOL_USE);
        });
}

/**
 * Extracts text content from messages
 * @param {Array} messages - Array of messages
 * @returns {string} Concatenated text content
 */
function extractTextContent(messages) {
    return messages
        .map(msg => {
            if (!msg.message || !msg.message.content) return '';
            
            const content = Array.isArray(msg.message.content) 
                ? msg.message.content 
                : [msg.message.content];
            
            return content
                .filter(item => item.type === CONTENT_TYPES.TEXT)
                .map(item => item.text || item.content || '')
                .join(' ');
        })
        .filter(text => text.length > 0)
        .join('\n');
}

/**
 * Filters messages by type
 * @param {Array} messages - Array of messages
 * @param {string|Array} types - Message type(s) to filter
 * @returns {Array} Filtered messages
 */
function filterMessagesByType(messages, types) {
    const typeArray = Array.isArray(types) ? types : [types];
    return messages.filter(msg => typeArray.includes(msg.type));
}

/**
 * Counts tokens in message content (approximate)
 * @param {Object} message - Message to count tokens for
 * @returns {number} Approximate token count
 */
function estimateTokenCount(message) {
    if (!message || !message.message) return 0;
    
    const textContent = extractTextContent([message]);
    // Rough approximation: 1 token ≈ 4 characters
    return Math.ceil(textContent.length / 4);
}

/**
 * Truncates message content to fit within token limits
 * @param {Object} message - Message to truncate
 * @param {number} maxTokens - Maximum token count
 * @returns {Object} Truncated message
 */
function truncateMessage(message, maxTokens) {
    const maxChars = maxTokens * 4; // Rough approximation
    const textContent = extractTextContent([message]);
    
    if (textContent.length <= maxChars) {
        return message;
    }
    
    const truncatedText = textContent.substring(0, maxChars - 3) + '...';
    
    return {
        ...message,
        message: {
            ...message.message,
            content: [{ type: CONTENT_TYPES.TEXT, text: truncatedText }]
        }
    };
}

/**
 * Merges consecutive messages of the same type
 * @param {Array} messages - Messages to merge
 * @returns {Array} Merged messages
 */
function mergeConsecutiveMessages(messages) {
    if (messages.length <= 1) return messages;
    
    const merged = [messages[0]];
    
    for (let i = 1; i < messages.length; i++) {
        const current = messages[i];
        const previous = merged[merged.length - 1];
        
        if (current.type === previous.type && 
            current.type === MESSAGE_TYPES.USER &&
            !current.message.isVisibleInTranscriptOnly &&
            !previous.message.isVisibleInTranscriptOnly) {
            
            // Merge user messages
            const previousContent = extractTextContent([previous]);
            const currentContent = extractTextContent([current]);
            
            merged[merged.length - 1] = createUserMessage({
                content: [previousContent, currentContent].join('\n\n')
            });
        } else {
            merged.push(current);
        }
    }
    
    return merged;
}

/**
 * Generates a unique message ID
 * @returns {string} Unique message ID
 */
function generateMessageId() {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

module.exports = {
    MESSAGE_TYPES,
    CONTENT_TYPES,
    createUserMessage,
    createSystemMessage,
    createAssistantMessage,
    createToolUse,
    createToolResult,
    createProgressMessage,
    createImageContent,
    validateMessage,
    normalizeMessageContent,
    extractToolUses,
    extractTextContent,
    filterMessagesByType,
    estimateTokenCount,
    truncateMessage,
    mergeConsecutiveMessages,
    generateMessageId
};
