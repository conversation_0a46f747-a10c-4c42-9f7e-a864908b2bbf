/**
 * Concurrency Controller (Dp)
 * 
 * Refactored version of the intelligent concurrency control system from the original code.
 * This controller manages concurrent execution of async generators with configurable
 * limits, load balancing, and proper resource management.
 * 
 * Original function: Dp(A, B=1/0) at line 320788
 */

/**
 * Execute With Concurrency Limit - Intelligent load balancing for async generators
 * 
 * This function provides:
 * 1. Configurable concurrency limits (default: 10 for tools)
 * 2. Intelligent load balancing across generators
 * 3. Proper resource cleanup and error handling
 * 4. Backpressure control and flow management
 * 5. Real-time progress tracking
 * 
 * The algorithm maintains a pool of active generators and uses Promise.race
 * to handle completion events, automatically starting new generators as
 * slots become available.
 * 
 * @param {Array} generators - Array of async generator functions
 * @param {number} limit - Maximum concurrent executions (default: Infinity)
 * @yields {*} Results from the generators as they complete
 */
async function* executeWithConcurrencyLimit(generators, limit = Infinity) {
    // Create a promise wrapper for each generator that tracks its state
    const createGeneratorPromise = (generator) => {
        const promise = generator.next().then(({ done, value }) => ({
            done,
            value,
            generator,
            promise
        }));
        return promise;
    };
    
    // Initialize the generator queue and active set
    const generatorQueue = [...generators];
    const activePromises = new Set();
    
    // Start initial batch of generators (up to the limit)
    while (activePromises.size < limit && generatorQueue.length > 0) {
        const generator = generatorQueue.shift();
        activePromises.add(createGeneratorPromise(generator));
    }
    
    // Main execution loop
    while (activePromises.size > 0) {
        // Wait for the next generator to produce a value or complete
        const { done, value, generator, promise } = await Promise.race(activePromises);
        
        // Remove the completed promise from the active set
        activePromises.delete(promise);
        
        if (!done) {
            // Generator produced a value - continue it and yield the value
            activePromises.add(createGeneratorPromise(generator));
            
            if (value !== undefined) {
                yield value;
            }
        } else {
            // Generator completed - start a new one if available
            if (generatorQueue.length > 0) {
                const nextGenerator = generatorQueue.shift();
                activePromises.add(createGeneratorPromise(nextGenerator));
            }
        }
    }
}

/**
 * Execute All Generators - Collect all results from async generators
 * 
 * This is a utility function that executes all generators and collects
 * their results into an array. Useful for cases where you need all
 * results before proceeding.
 * 
 * @param {Array} generators - Array of async generator functions
 * @param {number} limit - Maximum concurrent executions
 * @returns {Promise<Array>} Array of all results
 */
async function executeAllGenerators(generators, limit = Infinity) {
    const results = [];
    
    for await (const result of executeWithConcurrencyLimit(generators, limit)) {
        results.push(result);
    }
    
    return results;
}

/**
 * Execute Generators in Batches - Process generators in fixed-size batches
 * 
 * This function processes generators in batches, waiting for each batch
 * to complete before starting the next one. Useful for memory management
 * or when you need to process results in groups.
 * 
 * @param {Array} generators - Array of async generator functions
 * @param {number} batchSize - Size of each batch
 * @yields {Array} Results from each batch
 */
async function* executeInBatches(generators, batchSize) {
    for (let i = 0; i < generators.length; i += batchSize) {
        const batch = generators.slice(i, i + batchSize);
        const batchResults = await executeAllGenerators(batch, batchSize);
        yield batchResults;
    }
}

/**
 * Execute With Timeout - Add timeout support to generator execution
 * 
 * This function wraps generators with timeout functionality, automatically
 * cancelling generators that take too long to complete.
 * 
 * @param {Array} generators - Array of async generator functions
 * @param {number} timeoutMs - Timeout in milliseconds
 * @param {number} limit - Maximum concurrent executions
 * @yields {*} Results from generators or timeout errors
 */
async function* executeWithTimeout(generators, timeoutMs, limit = Infinity) {
    const timeoutGenerators = generators.map(generator => 
        wrapGeneratorWithTimeout(generator, timeoutMs)
    );
    
    yield* executeWithConcurrencyLimit(timeoutGenerators, limit);
}

/**
 * Wraps a generator with timeout functionality
 * 
 * @param {AsyncGenerator} generator - Generator to wrap
 * @param {number} timeoutMs - Timeout in milliseconds
 * @returns {AsyncGenerator} Wrapped generator with timeout
 */
async function* wrapGeneratorWithTimeout(generator, timeoutMs) {
    const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new TimeoutError(`Generator timed out after ${timeoutMs}ms`)), timeoutMs);
    });
    
    try {
        let done = false;
        while (!done) {
            const result = await Promise.race([
                generator.next(),
                timeoutPromise
            ]);
            
            done = result.done;
            if (!done) {
                yield result.value;
            }
        }
    } catch (error) {
        if (error instanceof TimeoutError) {
            yield {
                type: "error",
                error: "timeout",
                message: error.message
            };
        } else {
            throw error;
        }
    }
}

/**
 * Execute With Progress Tracking - Track progress across multiple generators
 * 
 * This function provides progress tracking for long-running generator operations,
 * yielding progress updates alongside the actual results.
 * 
 * @param {Array} generators - Array of async generator functions
 * @param {number} limit - Maximum concurrent executions
 * @param {Function} progressCallback - Progress callback function
 * @yields {*} Results and progress updates
 */
async function* executeWithProgress(generators, limit = Infinity, progressCallback) {
    let completed = 0;
    const total = generators.length;
    
    // Wrap generators to track completion
    const wrappedGenerators = generators.map(generator => 
        wrapGeneratorWithProgress(generator, () => {
            completed++;
            if (progressCallback) {
                progressCallback(completed, total);
            }
        })
    );
    
    for await (const result of executeWithConcurrencyLimit(wrappedGenerators, limit)) {
        // Yield progress updates
        if (result && result.type === "progress") {
            yield {
                type: "progress",
                completed,
                total,
                percentage: Math.round((completed / total) * 100),
                data: result.data
            };
        } else {
            yield result;
        }
    }
}

/**
 * Wraps a generator with progress tracking
 * 
 * @param {AsyncGenerator} generator - Generator to wrap
 * @param {Function} onComplete - Completion callback
 * @returns {AsyncGenerator} Wrapped generator with progress tracking
 */
async function* wrapGeneratorWithProgress(generator, onComplete) {
    try {
        let done = false;
        while (!done) {
            const result = await generator.next();
            done = result.done;
            
            if (!done) {
                yield result.value;
            }
        }
    } finally {
        onComplete();
    }
}

/**
 * Execute With Error Isolation - Isolate errors to prevent cascade failures
 * 
 * This function ensures that errors in one generator don't affect others,
 * providing proper error isolation and recovery.
 * 
 * @param {Array} generators - Array of async generator functions
 * @param {number} limit - Maximum concurrent executions
 * @param {Function} errorHandler - Error handler function
 * @yields {*} Results or error objects
 */
async function* executeWithErrorIsolation(generators, limit = Infinity, errorHandler) {
    const isolatedGenerators = generators.map((generator, index) => 
        wrapGeneratorWithErrorIsolation(generator, index, errorHandler)
    );
    
    yield* executeWithConcurrencyLimit(isolatedGenerators, limit);
}

/**
 * Wraps a generator with error isolation
 * 
 * @param {AsyncGenerator} generator - Generator to wrap
 * @param {number} index - Generator index
 * @param {Function} errorHandler - Error handler function
 * @returns {AsyncGenerator} Wrapped generator with error isolation
 */
async function* wrapGeneratorWithErrorIsolation(generator, index, errorHandler) {
    try {
        let done = false;
        while (!done) {
            const result = await generator.next();
            done = result.done;
            
            if (!done) {
                yield result.value;
            }
        }
    } catch (error) {
        const errorResult = {
            type: "error",
            generatorIndex: index,
            error: error.message,
            stack: error.stack
        };
        
        if (errorHandler) {
            errorHandler(error, index);
        }
        
        yield errorResult;
    }
}

/**
 * Create Generator Pool - Manage a pool of reusable generators
 * 
 * This class provides a pool of generators that can be reused across
 * multiple execution cycles, reducing overhead and improving performance.
 */
class GeneratorPool {
    constructor(generatorFactory, poolSize = 10) {
        this.generatorFactory = generatorFactory;
        this.poolSize = poolSize;
        this.availableGenerators = [];
        this.activeGenerators = new Set();
    }
    
    /**
     * Get a generator from the pool
     * @returns {AsyncGenerator} Generator from pool or newly created
     */
    getGenerator() {
        if (this.availableGenerators.length > 0) {
            const generator = this.availableGenerators.pop();
            this.activeGenerators.add(generator);
            return generator;
        }
        
        const generator = this.generatorFactory();
        this.activeGenerators.add(generator);
        return generator;
    }
    
    /**
     * Return a generator to the pool
     * @param {AsyncGenerator} generator - Generator to return
     */
    returnGenerator(generator) {
        this.activeGenerators.delete(generator);
        
        if (this.availableGenerators.length < this.poolSize) {
            this.availableGenerators.push(generator);
        }
    }
    
    /**
     * Clear the pool and close all generators
     */
    async clear() {
        for (const generator of this.availableGenerators) {
            if (generator.return) {
                await generator.return();
            }
        }
        
        for (const generator of this.activeGenerators) {
            if (generator.return) {
                await generator.return();
            }
        }
        
        this.availableGenerators.length = 0;
        this.activeGenerators.clear();
    }
}

/**
 * Custom error class for timeout operations
 */
class TimeoutError extends Error {
    constructor(message) {
        super(message);
        this.name = "TimeoutError";
    }
}

module.exports = {
    executeWithConcurrencyLimit,
    executeAllGenerators,
    executeInBatches,
    executeWithTimeout,
    executeWithProgress,
    executeWithErrorIsolation,
    GeneratorPool,
    TimeoutError
};
