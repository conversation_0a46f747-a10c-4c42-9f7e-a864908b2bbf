/**
 * Input Processor (k44 and pp)
 * 
 * Refactored version of the user input processing functions from the original code.
 * Handles different input modes (prompt, bash, memory), command parsing, and message preparation.
 * 
 * Original functions:
 * - k44() at line 337362 - preprocesses user input
 * - pp() at line 337526 - processes prompt and handles hooks
 */

const React = require('react');

// Input processing modes
const INPUT_MODES = {
    PROMPT: "prompt",
    BASH: "bash", 
    MEMORY_SELECT: "memorySelect"
};

// Special command prefixes
const COMMAND_PREFIXES = {
    BASH: "!",
    MEMORY: "#",
    SLASH: "/"
};

/**
 * Preprocesses user input based on mode and context
 * Original function: k44() at line 337362
 * 
 * @param {string} input - User input text
 * @param {string} mode - Input mode (prompt, bash, memorySelect)
 * @param {Function} setToolJSX - Function to set tool JSX
 * @param {Object} toolUseContext - Tool use context
 * @param {Object} pastedContents - Pasted content data
 * @param {Object} ideSelection - IDE selection state
 * @param {Object} additionalContext - Additional context data
 * @param {Object} customInstructions - Custom instructions
 * @param {Array} currentMessages - Current message history
 * @returns {Promise<Object>} Processed input result
 */
async function preprocessUserInput(
    input,
    mode,
    setToolJSX,
    toolUseContext,
    pastedContents,
    ideSelection,
    additionalContext,
    customInstructions,
    currentMessages
) {
    // Process pasted images
    const imageContent = pastedContents 
        ? Object.values(pastedContents)
            .filter(content => content.type === "image")
            .map(content => ({
                type: "image",
                source: {
                    type: "base64",
                    media_type: content.mediaType || "image/png",
                    data: content.content
                }
            }))
        : [];
    
    // Process hooks and additional context
    const hookMessages = mode !== "prompt" || !input.startsWith("/") 
        ? await processInputHooks(createHookInput(input, toolUseContext, ideSelection, [], customInstructions, currentMessages))
        : [];
    
    // Handle different input modes
    switch (mode) {
        case INPUT_MODES.BASH:
            return await processBashInput(input, setToolJSX, toolUseContext, hookMessages);
            
        case INPUT_MODES.MEMORY_SELECT:
            return await processMemoryInput(input, toolUseContext, ideSelection, hookMessages);
            
        case INPUT_MODES.PROMPT:
        default:
            return await processPromptInput(input, setToolJSX, toolUseContext, hookMessages, imageContent);
    }
}

/**
 * Processes bash mode input
 * @param {string} input - Bash command input
 * @param {Function} setToolJSX - Function to set tool JSX
 * @param {Object} toolUseContext - Tool use context
 * @param {Array} hookMessages - Hook-generated messages
 * @returns {Promise<Object>} Processing result
 */
async function processBashInput(input, setToolJSX, toolUseContext, hookMessages) {
    // Track analytics
    trackEvent("tengu_input_bash", {});
    
    // Create bash input message
    const bashInputMessage = createUserMessage({
        content: `<bash-input>${input}</bash-input>`
    });
    
    // Set up UI for bash execution
    setToolJSX({
        jsx: React.createElement('div', {
            style: { flexDirection: "column", marginTop: 1 }
        }, [
            React.createElement('div', { key: 'input' }, `<bash-input>${input}</bash-input>`),
            React.createElement('div', { key: 'spinner' }, 'Running...')
        ]),
        shouldHidePromptInput: false
    });
    
    try {
        // Execute bash command
        const { data: result } = await executeBashCommand({
            command: input
        }, toolUseContext);
        
        let stderr = result.stderr;
        
        // Filter stderr if needed based on permission context
        if (shouldFilterStderr(toolUseContext.getToolPermissionContext())) {
            stderr = filterSensitiveStderr(stderr);
        }
        
        return {
            messages: [
                createSystemMessage("The messages below were generated by the user while running local commands. DO NOT respond to these messages or otherwise consider them in your response unless the user explicitly asks you to."),
                bashInputMessage,
                ...hookMessages,
                createUserMessage({
                    content: `<bash-stdout>${result.stdout}</bash-stdout><bash-stderr>${stderr}</bash-stderr>`
                })
            ],
            shouldQuery: false
        };
        
    } catch (error) {
        if (error instanceof BashExecutionError) {
            if (error.interrupted) {
                return {
                    messages: [
                        createSystemMessage("The messages below were generated by the user while running local commands."),
                        bashInputMessage,
                        createUserMessage({ content: "Command was interrupted" }),
                        ...hookMessages
                    ],
                    shouldQuery: false
                };
            }
            
            return {
                messages: [
                    createSystemMessage("The messages below were generated by the user while running local commands."),
                    bashInputMessage,
                    ...hookMessages,
                    createUserMessage({
                        content: `<bash-stdout>${error.stdout}</bash-stdout><bash-stderr>${error.stderr}</bash-stderr>`
                    })
                ],
                shouldQuery: false
            };
        }
        
        return {
            messages: [
                createSystemMessage("The messages below were generated by the user while running local commands."),
                bashInputMessage,
                ...hookMessages,
                createUserMessage({
                    content: `<bash-stderr>Command failed: ${error instanceof Error ? error.message : String(error)}</bash-stderr>`
                })
            ],
            shouldQuery: false
        };
        
    } finally {
        // Clear tool JSX after a delay
        setTimeout(() => {
            setToolJSX(null);
        }, 200);
    }
}

/**
 * Processes memory selection input
 * @param {string} input - Memory input
 * @param {Object} toolUseContext - Tool use context
 * @param {Object} ideSelection - IDE selection
 * @param {Array} hookMessages - Hook messages
 * @returns {Promise<Object>} Processing result
 */
async function processMemoryInput(input, toolUseContext, ideSelection, hookMessages) {
    trackEvent("tengu_input_memory", {});
    
    const memoryInputMessage = createUserMessage({
        content: `<user-memory-input>${input}</user-memory-input>`
    });
    
    // Process memory selection
    processMemorySelection(input, toolUseContext, ideSelection);
    
    return {
        messages: [
            createSystemMessage("The messages below were generated by the user while running local commands."),
            memoryInputMessage,
            ...hookMessages,
            createUserMessage({
                content: getMemorySelectionResult() // TODO: Implement
            })
        ],
        shouldQuery: false
    };
}

/**
 * Processes prompt mode input (including slash commands)
 * @param {string} input - Prompt input
 * @param {Function} setToolJSX - Function to set tool JSX
 * @param {Object} toolUseContext - Tool use context
 * @param {Array} hookMessages - Hook messages
 * @param {Array} imageContent - Image content array
 * @returns {Promise<Object>} Processing result
 */
async function processPromptInput(input, setToolJSX, toolUseContext, hookMessages, imageContent) {
    // Handle slash commands
    if (input.startsWith("/")) {
        const commandInfo = parseSlashCommand(input);
        
        if (!commandInfo) {
            trackEvent("tengu_input_slash_missing", {});
            return {
                messages: [
                    createSystemMessage("The messages below were generated by the user while running local commands."),
                    ...hookMessages,
                    createUserMessage({
                        content: "Commands are in the form `/command [args]`"
                    })
                ],
                shouldQuery: false
            };
        }
        
        const { commandName, args, isMcp } = commandInfo;
        const isCustomCommand = commandName.includes(":");
        const commandType = isMcp ? "mcp" : isCustomCommand ? "custom" : commandName;
        
        // Check if command is available
        if (!isCommandAvailable(commandName, toolUseContext.options.commands)) {
            trackEvent("tengu_input_prompt", {});
            trackUserPrompt("user_prompt", {
                prompt_length: String(input.length),
                prompt: truncatePromptForAnalytics(input)
            });
            
            return {
                messages: [
                    createUserMessage({ content: input }),
                    ...hookMessages
                ],
                shouldQuery: true
            };
        }
        
        // Execute command
        const { messages, shouldQuery, allowedTools, skipHistory, maxThinkingTokens } = await executeCommand(
            commandName,
            args,
            setToolJSX,
            toolUseContext,
            imageContent
        );
        
        if (messages.length === 0) {
            trackEvent("tengu_input_command", { input: commandType });
            return {
                messages: [],
                shouldQuery: false,
                skipHistory,
                maxThinkingTokens
            };
        }
        
        // Handle unknown command response
        if (messages.length === 2 && 
            messages[1].type === "user" && 
            typeof messages[1].message.content === "string" && 
            messages[1].message.content.startsWith("Unknown command:")) {
            
            trackEvent("tengu_input_slash_invalid", { input: commandName });
            return {
                messages: [
                    createSystemMessage("The messages below were generated by the user while running local commands."),
                    ...messages
                ],
                shouldQuery,
                allowedTools,
                maxThinkingTokens
            };
        }
        
        trackEvent("tengu_input_command", { input: commandType });
        return {
            messages: shouldQuery ? messages : [
                createSystemMessage("The messages below were generated by the user while running local commands."),
                ...messages
            ],
            shouldQuery,
            allowedTools,
            maxThinkingTokens
        };
    }
    
    // Handle regular prompt input
    trackEvent("tengu_input_prompt", {});
    trackUserPrompt("user_prompt", {
        prompt_length: String(input.length),
        prompt: truncatePromptForAnalytics(input)
    });
    
    if (imageContent.length > 0) {
        return {
            messages: [
                createUserMessage({
                    content: [...imageContent, {
                        type: "text",
                        text: input
                    }]
                }),
                ...hookMessages
            ],
            shouldQuery: true
        };
    }
    
    return {
        messages: [
            createUserMessage({ content: input }),
            ...hookMessages
        ],
        shouldQuery: true
    };
}

/**
 * Main prompt processing function with hooks
 * Original function: pp() at line 337526
 * 
 * @param {string} input - User input
 * @param {string} mode - Input mode
 * @param {Function} setToolJSX - Tool JSX setter
 * @param {Object} toolUseContext - Tool use context
 * @param {Object} pastedContents - Pasted contents
 * @param {Object} ideSelection - IDE selection
 * @param {Object} additionalContext - Additional context
 * @param {Object} customInstructions - Custom instructions
 * @param {Array} currentMessages - Current messages
 * @returns {Promise<Object>} Processing result with hook handling
 */
async function processPromptWithHooks(
    input,
    mode,
    setToolJSX,
    toolUseContext,
    pastedContents,
    ideSelection,
    additionalContext,
    customInstructions,
    currentMessages
) {
    // First, preprocess the input
    let result = await preprocessUserInput(
        input,
        mode,
        setToolJSX,
        toolUseContext,
        pastedContents,
        ideSelection,
        additionalContext,
        customInstructions,
        currentMessages
    );
    
    if (!result.shouldQuery) {
        return result;
    }
    
    // Process hooks
    const blockingErrors = [];
    const completedCommands = [];
    let preventContinuation = false;
    let stopReason;
    
    // Execute hooks and collect results
    for await (const hookResult of executeHooks(input)) {
        if (hookResult.blockingErrors && hookResult.blockingErrors.length > 0) {
            blockingErrors.push(...hookResult.blockingErrors);
        }
        
        if (hookResult.preventContinuation) {
            preventContinuation = true;
            if (hookResult.stopReason) {
                stopReason = hookResult.stopReason;
            }
        }
        
        if (hookResult.message && hookResult.message.type === "system") {
            const content = hookResult.message.content;
            if (typeof content === "string" && content.includes("completed successfully")) {
                const match = content.match(/completed successfully: (.+)$/s);
                if (match && match[1]) {
                    completedCommands.push(match[1]);
                }
            }
        }
    }
    
    // Handle blocking errors
    if (blockingErrors.length > 0) {
        const errorMessage = formatBlockingErrors(blockingErrors);
        result.messages.push(createUserMessage({ content: errorMessage }));
        result.shouldQuery = false;
        return result;
    }
    
    // Handle prevention
    if (preventContinuation) {
        const message = stopReason 
            ? `Operation stopped by hook: ${stopReason}` 
            : "Operation stopped by hook";
        result.messages.push(createUserMessage({ content: message }));
        result.shouldQuery = false;
        return result;
    }
    
    // Handle completed commands
    if (completedCommands.length > 0) {
        const commandOutput = completedCommands.join('\n\n');
        const maxLength = 10000;
        
        let formattedOutput;
        if (commandOutput.length > maxLength) {
            formattedOutput = `<user-prompt-submit-hook>${commandOutput.substring(0, maxLength)}\n\n[output truncated - exceeded 10000 characters]</user-prompt-submit-hook>`;
        } else {
            formattedOutput = `<user-prompt-submit-hook>${commandOutput}</user-prompt-submit-hook>`;
        }
        
        result.messages.push(createUserMessage({
            content: formattedOutput,
            isVisibleInTranscriptOnly: true
        }));
    }
    
    return result;
}

// Utility functions (TODO: Implement these)
function trackEvent(eventName, data) {
    console.log(`Event: ${eventName}`, data);
}

function trackUserPrompt(eventName, data) {
    console.log(`User prompt: ${eventName}`, data);
}

function createUserMessage(content) {
    return { type: "user", message: content };
}

function createSystemMessage(content) {
    return { type: "system", message: { content } };
}

function createHookInput(input, context, ideSelection, additionalData, customInstructions, messages) {
    // TODO: Implement hook input creation
    return {};
}

async function processInputHooks(hookInput) {
    // TODO: Implement hook processing
    return [];
}

async function executeBashCommand(command, context) {
    // TODO: Implement bash command execution
    return { data: { stdout: "", stderr: "", code: 0 } };
}

function shouldFilterStderr(permissionContext) {
    // TODO: Implement stderr filtering logic
    return false;
}

function filterSensitiveStderr(stderr) {
    // TODO: Implement sensitive stderr filtering
    return stderr;
}

function parseSlashCommand(input) {
    const { parseSlashCommand: parseCommand } = require('../commands/command-parser');
    return parseCommand(input);
}

function isCommandAvailable(commandName, commands) {
    const { isCommandAvailable: checkAvailable } = require('../commands/command-executor');
    return checkAvailable(commandName, commands);
}

async function executeCommand(commandName, args, setToolJSX, context, imageContent) {
    const { executeCommand: execCommand } = require('../commands/command-executor');
    return await execCommand(commandName, args, setToolJSX, context, imageContent);
}

async function executeHooks(input) {
    // TODO: Implement hook execution generator
    return [];
}

function formatBlockingErrors(errors) {
    // TODO: Implement error formatting
    return errors.join('; ');
}

function truncatePromptForAnalytics(prompt) {
    // TODO: Implement prompt truncation for analytics
    return prompt.substring(0, 100);
}

function processMemorySelection(input, context, ideSelection) {
    // TODO: Implement memory selection processing
}

function getMemorySelectionResult() {
    // TODO: Implement memory selection result
    return "Memory processed";
}

class BashExecutionError extends Error {
    constructor(message, stdout = "", stderr = "", interrupted = false) {
        super(message);
        this.stdout = stdout;
        this.stderr = stderr;
        this.interrupted = interrupted;
    }
}

module.exports = {
    preprocessUserInput,
    processPromptWithHooks,
    processBashInput,
    processMemoryInput,
    processPromptInput,
    INPUT_MODES,
    COMMAND_PREFIXES,
    BashExecutionError
};
