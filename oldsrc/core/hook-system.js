/**
 * Hook System (xq1, zn2, Zn2)
 * 
 * Refactored version of the hook execution system from the original code.
 * Handles hook discovery, execution, and result processing for various events.
 * 
 * Original functions:
 * - xq1() at line 322899 - executes hooks for events
 * - zn2() at line 323177 - processes user prompt submit hooks
 * - Zn2() at line 322833 - discovers hooks for events
 */

const chalk = require('chalk');
const { execSync } = require('child_process');

// Hook event types
const HOOK_EVENTS = {
    USER_PROMPT_SUBMIT: "UserPromptSubmit",
    PRE_TOOL_USE: "PreToolUse",
    POST_TOOL_USE: "PostToolUse",
    PRE_COMPACT: "PreCompact",
    STOP: "Stop",
    SUBAGENT_STOP: "SubagentStop"
};

// Hook outcome types
const HOOK_OUTCOMES = {
    SUCCESS: "success",
    BLOCKING: "blocking",
    NON_BLOCKING_ERROR: "non_blocking_error",
    CANCELLED: "cancelled"
};

// Default hook timeout (30 seconds)
const DEFAULT_HOOK_TIMEOUT = 30000;

/**
 * Discovers hooks for a specific event
 * Original function: Zn2() at line 322833
 * 
 * @param {string} eventName - Hook event name
 * @param {Object} eventData - Event data for context
 * @returns {Array} Array of hook configurations
 */
function discoverHooks(eventName, eventData) {
    try {
        // Get hooks configuration
        const hooksConfig = getHooksConfiguration();
        const hooks = hooksConfig?.[eventName] ?? [];
        
        let contextData = undefined;
        
        // Process different hook events
        switch (eventData.hook_event_name) {
            case HOOK_EVENTS.PRE_TOOL_USE:
                if (eventData.tool_name && eventData.tool_input) {
                    contextData = {
                        tool_name: eventData.tool_name,
                        tool_input: eventData.tool_input
                    };
                }
                break;
                
            case HOOK_EVENTS.POST_TOOL_USE:
                if (eventData.tool_name && eventData.tool_result) {
                    contextData = {
                        tool_name: eventData.tool_name,
                        tool_result: eventData.tool_result
                    };
                }
                break;
                
            case HOOK_EVENTS.USER_PROMPT_SUBMIT:
                if (eventData.prompt) {
                    contextData = {
                        prompt: eventData.prompt
                    };
                }
                break;
        }
        
        // Filter hooks based on matchers
        return hooks.filter(hook => {
            if (!hook.matcher) return true;
            
            try {
                return matchesHookPattern(hook.matcher, contextData);
            } catch (error) {
                console.error(`Invalid regex pattern in hook matcher: ${hook.matcher}`, error);
                return false;
            }
        });
        
    } catch (error) {
        console.error('Error discovering hooks:', error);
        return [];
    }
}

/**
 * Executes hooks for a specific event
 * Original function: xq1() at line 322899
 * 
 * @param {Object} eventData - Event data
 * @param {string} parentToolUseID - Parent tool use ID
 * @param {string} trigger - Hook trigger context
 * @param {AbortSignal} abortSignal - Abort signal
 * @param {number} timeout - Hook timeout in milliseconds
 * @yields {Object} Hook execution results
 */
async function* executeHooks(eventData, parentToolUseID, trigger, abortSignal, timeout = DEFAULT_HOOK_TIMEOUT) {
    const eventName = eventData.hook_event_name;
    const hookContext = trigger ? `${eventName}:${trigger}` : eventName;
    
    console.log(`Executing hooks for ${hookContext}`);
    
    // Discover command hooks
    const commandHooks = discoverHooks(eventName, eventData).filter(hook => hook.type === "command");
    
    console.log(`Found ${commandHooks.length} hook commands to execute`);
    
    if (commandHooks.length === 0) {
        return;
    }
    
    if (abortSignal?.aborted) {
        return;
    }
    
    // Serialize event data for hooks
    let serializedEventData;
    try {
        serializedEventData = JSON.stringify(eventData);
    } catch (error) {
        console.error(`Failed to stringify hook ${hookContext} input:`, error);
        yield {
            message: createHookMessage(
                `Failed to prepare hook input: ${error instanceof Error ? error.message : String(error)}`,
                "warning",
                parentToolUseID
            )
        };
        return;
    }
    
    // Track hook execution
    trackEvent("tengu_run_hook", {
        hookName: hookContext,
        numCommands: commandHooks.length
    });
    
    // Yield progress messages for each hook
    for (const hook of commandHooks) {
        yield {
            message: {
                type: "progress",
                data: {
                    type: "running_hook",
                    hookName: hookContext,
                    command: hook.command
                },
                parentToolUseID,
                toolUseID: `hook-${generateUUID()}`,
                timestamp: new Date().toISOString(),
                uuid: generateUUID()
            }
        };
    }
    
    // Execute hooks in parallel
    const hookPromises = commandHooks.map(async (hook) => {
        const hookTimeout = hook.timeout ? hook.timeout * 1000 : timeout;
        let signal, cleanup;
        
        if (abortSignal) {
            const combined = combineAbortSignals(abortSignal, AbortSignal.timeout(hookTimeout));
            signal = combined.signal;
            cleanup = combined.cleanup;
        } else {
            signal = AbortSignal.timeout(hookTimeout);
        }
        
        try {
            console.log(`Executing hook command: ${hook.command} with timeout ${hookTimeout}ms`);
            
            const result = await executeHookCommand(hook.command, serializedEventData, signal);
            
            cleanup?.();
            
            console.log(`Hook command completed with status ${result.status}: ${hook.command}`);
            
            if (result.stdout) {
                console.log(`Hook stdout: ${result.stdout.substring(0, 200)}...`);
            }
            if (result.stderr) {
                console.log(`Hook stderr: ${result.stderr}`);
            }
            
            if (result.aborted) {
                return {
                    message: createHookMessage(
                        `${chalk.bold(hookContext)} [${hook.command}] ${chalk.yellow("cancelled")}`,
                        "info",
                        parentToolUseID
                    ),
                    outcome: HOOK_OUTCOMES.CANCELLED
                };
            }
            
            // Parse hook output
            const { json, plainText, validationError } = parseHookOutput(result.stdout);
            
            if (validationError) {
                return {
                    message: createHookMessage(
                        `${chalk.bold(hookContext)} [${hook.command}] ${chalk.yellow("JSON validation failed")}:\n${validationError}`,
                        "warning",
                        parentToolUseID
                    ),
                    outcome: HOOK_OUTCOMES.NON_BLOCKING_ERROR
                };
            }
            
            if (json) {
                console.log(`Parsed JSON output from hook: ${JSON.stringify(json)}`);
                
                const processedResult = processHookResult(json, hook.command);
                console.log(`Processed hook result: ${JSON.stringify(processedResult)}`);
                
                if (!json.suppressOutput && plainText && result.status === 0) {
                    const successMessage = `${chalk.bold(hookContext)} [${hook.command}] completed successfully`;
                    return {
                        ...processedResult,
                        message: processedResult.message || createHookMessage(successMessage, "info", parentToolUseID),
                        outcome: HOOK_OUTCOMES.SUCCESS
                    };
                }
                
                return {
                    ...processedResult,
                    outcome: HOOK_OUTCOMES.SUCCESS
                };
            }
            
            // Handle plain text output
            if (result.status === 0) {
                let successMessage = `${chalk.bold(hookContext)} [${hook.command}] completed successfully`;
                if (result.stdout.trim()) {
                    successMessage += `: ${result.stdout.trim()}`;
                }
                return {
                    message: createHookMessage(successMessage, "info", parentToolUseID),
                    outcome: HOOK_OUTCOMES.SUCCESS
                };
            }
            
            // Handle blocking errors (status code 2)
            if (result.status === 2) {
                return {
                    blockingError: {
                        blockingError: `[${hook.command}]: ${result.stderr || "No stderr output"}`,
                        command: hook.command
                    },
                    outcome: HOOK_OUTCOMES.BLOCKING
                };
            }
            
            // Handle non-blocking errors
            return {
                message: createHookMessage(
                    `${chalk.bold(hookContext)} [${hook.command}] failed with non-blocking status code ${result.status}: ${result.stderr || "No stderr output"}`,
                    "warning",
                    parentToolUseID
                ),
                outcome: HOOK_OUTCOMES.NON_BLOCKING_ERROR
            };
            
        } catch (error) {
            cleanup?.();
            const errorMessage = error instanceof Error ? error.message : String(error);
            return {
                message: createHookMessage(
                    `${chalk.bold(hookContext)} [${hook.command}] failed to run: ${errorMessage}`,
                    "warning",
                    parentToolUseID
                ),
                outcome: HOOK_OUTCOMES.NON_BLOCKING_ERROR
            };
        }
    });
    
    // Wait for all hooks to complete
    const results = await Promise.all(hookPromises);
    
    // Process results
    const outcomeStats = {
        success: 0,
        blocking: 0,
        non_blocking_error: 0,
        cancelled: 0
    };
    
    let preventContinuation = false;
    let stopReason;
    let hookApproved = false;
    let hookApprovalReason;
    const blockingErrors = [];
    
    for (const result of results) {
        if (!result) continue;
        
        if (result.outcome) {
            outcomeStats[result.outcome]++;
        }
        
        if ("preventContinuation" in result && result.preventContinuation) {
            preventContinuation = true;
            if ("stopReason" in result && result.stopReason) {
                stopReason = result.stopReason;
            }
        }
        
        if ("blockingError" in result && result.blockingError) {
            blockingErrors.push(result.blockingError);
        } else if ("blockingErrors" in result && result.blockingErrors) {
            blockingErrors.push(...result.blockingErrors);
        }
        
        if ("message" in result && result.message) {
            yield { message: result.message };
        }
        
        if ("hookApproved" in result && result.hookApproved) {
            hookApproved = true;
            if ("hookApprovalReason" in result && result.hookApprovalReason) {
                hookApprovalReason = result.hookApprovalReason;
            }
        }
    }
    
    // Track completion
    trackEvent("tengu_repl_hook_finished", {
        hookName: hookContext,
        numCommands: commandHooks.length,
        numSuccess: outcomeStats.success,
        numBlocking: outcomeStats.blocking,
        numNonBlockingError: outcomeStats.non_blocking_error,
        numCancelled: outcomeStats.cancelled
    });
    
    // Yield final results
    yield { blockingErrors };
    
    if (hookApproved) {
        yield {
            hookApproved,
            hookApprovalReason
        };
    }
    
    if (preventContinuation) {
        yield {
            preventContinuation: true,
            stopReason
        };
    }
}

/**
 * Processes user prompt submit hooks
 * Original function: zn2() at line 323177
 * 
 * @param {string} prompt - User prompt
 * @param {AbortSignal} abortSignal - Abort signal
 * @param {number} timeout - Hook timeout
 * @yields {Object} Hook execution results
 */
async function* processUserPromptSubmitHooks(prompt, abortSignal, timeout = DEFAULT_HOOK_TIMEOUT) {
    const eventData = {
        ...getSessionFingerprint(), // TODO: Implement
        hook_event_name: HOOK_EVENTS.USER_PROMPT_SUBMIT,
        prompt
    };
    
    yield* executeHooks(eventData, generateToolUseID(), undefined, abortSignal, timeout);
}

// Utility functions (TODO: Implement these)
function getHooksConfiguration(source = 'project') {
    try {
        const { getConfigValue } = require('../config/config-manager');
        const settings = getConfigValue('settings', source === 'global');
        return settings?.hooks || {};
    } catch (error) {
        console.error('Failed to get hooks configuration:', error);
        return {};
    }
}

function matchesHookPattern(pattern, data) {
    // Original function: L24() at line 322818
    if (!pattern || pattern === "*") {
        return true;
    }

    try {
        // Support regex patterns
        if (pattern.startsWith('/') && pattern.endsWith('/')) {
            const regex = new RegExp(pattern.slice(1, -1));
            return regex.test(data);
        }

        // Support glob-like patterns
        const regexPattern = pattern
            .replace(/\*/g, '.*')
            .replace(/\?/g, '.');
        const regex = new RegExp(`^${regexPattern}$`);
        return regex.test(data);
    } catch (error) {
        console.error(`Invalid regex pattern in hook matcher: ${pattern}`, error);
        return false;
    }
}

function trackEvent(eventName, data) {
    console.log(`Event: ${eventName}`, data);
}

function createHookMessage(content, type, parentToolUseID) {
    // TODO: Implement hook message creation
    return {
        type: "system",
        content,
        messageType: type,
        parentToolUseID
    };
}

function generateUUID() {
    // Generate a simple UUID-like string
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
}

function generateToolUseID() {
    return `tool-${generateUUID()}`;
}

function combineAbortSignals(signal1, signal2) {
    // Original function: Cn2() at line 322881
    const controller = new AbortController();
    const cleanup = () => {
        controller.abort();
    };

    signal1.addEventListener("abort", cleanup);
    signal2.addEventListener("abort", cleanup);

    const cleanupListeners = () => {
        signal1.removeEventListener("abort", cleanup);
        signal2.removeEventListener("abort", cleanup);
    };

    return {
        signal: controller.signal,
        cleanup: cleanupListeners
    };
}

async function executeHookCommand(command, input, abortSignal) {
    // Original function: Gn2() at line 322744
    if (abortSignal?.aborted) {
        return {
            stdout: "",
            stderr: "Operation cancelled",
            status: 1,
            aborted: true
        };
    }

    const { spawn } = require('child_process');
    const { getCurrentWorkingDirectory } = require('../utils/file-utils');

    try {
        const childProcess = spawn(command, [], {
            env: process.env,
            cwd: getCurrentWorkingDirectory(),
            shell: true,
            signal: abortSignal
        });

        let stdout = "";
        let stderr = "";

        childProcess.stdout.on("data", (data) => {
            stdout += data.toString();
        });

        childProcess.stderr.on("data", (data) => {
            stderr += data.toString();
        });

        const stdinPromise = new Promise((resolve, reject) => {
            childProcess.stdin.on("error", reject);
            childProcess.stdin.write(input);
            childProcess.stdin.end();
            resolve();
        });

        const errorPromise = new Promise((resolve, reject) => {
            childProcess.on("error", reject);
        });

        const exitPromise = new Promise((resolve) => {
            childProcess.on("close", (code) => {
                resolve({
                    stdout,
                    stderr,
                    status: code ?? 1,
                    aborted: abortSignal?.aborted || false
                });
            });
        });

        try {
            await Promise.race([stdinPromise, errorPromise]);
            return await Promise.race([exitPromise, errorPromise]);
        } catch (error) {
            if (error.code === "EPIPE") {
                console.error("EPIPE error while writing to hook stdin (hook command likely closed early)");
                return {
                    stdout: "",
                    stderr: "Hook command closed stdin before hook input was fully written (EPIPE)",
                    status: 1
                };
            } else if (error.code === "ABORT_ERR") {
                return {
                    stdout: "",
                    stderr: "Hook cancelled",
                    status: 1,
                    aborted: true
                };
            } else {
                return {
                    stdout: "",
                    stderr: `Error occurred while executing hook command: ${error instanceof Error ? error.message : String(error)}`,
                    status: 1
                };
            }
        }
    } catch (error) {
        return {
            stdout: "",
            stderr: `Failed to spawn hook command: ${error instanceof Error ? error.message : String(error)}`,
            status: 1
        };
    }
}

function parseHookOutput(output) {
    // TODO: Implement hook output parsing
    try {
        const json = JSON.parse(output);
        return { json, plainText: output, validationError: null };
    } catch {
        return { json: null, plainText: output, validationError: null };
    }
}

function processHookResult(json, command) {
    // Process hook result and extract relevant information
    try {
        if (typeof json === 'string') {
            json = JSON.parse(json);
        }

        // Validate hook result structure
        if (json && typeof json === 'object') {
            return {
                outcome: json.outcome || HOOK_OUTCOMES.CONTINUE,
                blockingError: json.blockingError || null,
                message: json.message || null,
                data: json.data || null,
                command: command
            };
        }

        return {
            outcome: HOOK_OUTCOMES.CONTINUE,
            blockingError: null,
            message: null,
            data: json,
            command: command
        };
    } catch (error) {
        console.error(`Failed to process hook result for command "${command}":`, error);
        return {
            outcome: HOOK_OUTCOMES.CONTINUE,
            blockingError: `Hook result processing failed: ${error.message}`,
            message: null,
            data: null,
            command: command
        };
    }
}

function getSessionFingerprint() {
    // TODO: Implement session fingerprint generation
    return {};
}

module.exports = {
    discoverHooks,
    executeHooks,
    processUserPromptSubmitHooks,
    HOOK_EVENTS,
    HOOK_OUTCOMES,
    DEFAULT_HOOK_TIMEOUT
};
