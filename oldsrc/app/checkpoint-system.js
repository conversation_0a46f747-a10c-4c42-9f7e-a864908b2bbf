/**
 * Checkpoint System
 * 
 * Provides git-based checkpointing functionality for tracking and restoring
 * file system states during AI coding sessions.
 */

const crypto = require('crypto');
const path = require('path');
const fs = require('fs');
const { execSync } = require('child_process');

// Constants
const MAX_BUFFER_SIZE = 3000000;

/**
 * <PERSON><PERSON><PERSON> ignore patterns for checkpointing
 */
const DEFAULT_IGNORE_PATTERNS = [
    ".git/", ".parcel-cache/", ".pytest_cache/", ".nuxt/", ".sass-cache/", 
    ".claude/", "__pycache__/", "node_modules/", "pycache/",
    // Media files
    "*.3gp", "*.avif", "*.gif", "*.png", "*.psd", "*.aac", "*.aiff", "*.asf", 
    "*.avi", "*.bmp", "*.divx", "*.flac", "*.heic", "*.ico", "*.jpg", "*.jpeg", 
    "*.m4a", "*.m4v", "*.mkv", "*.mov", "*.mp3", "*.mp4", "*.mpeg", "*.mpg", 
    "*.ogg", "*.opus", "*.raw", "*.rm", "*.rmvb", "*.tiff", "*.tif", "*.vob", 
    "*.wav", "*.webm", "*.webp", "*.wma", "*.wmv",
    // System files
    "*.DS_Store", "*.cache", "*.crdownload", "*.dmp", "*.dump", "*.eslintcache", 
    "*.pyc", "*.pyo", "*.swo", "*.swp", "*.Thumbs.db",
    // Archives
    "*.zip", "*.tar", "*.gz", "*.rar", "*.7z", "*.iso", "*.bin", "*.exe", 
    "*.dll", "*.so", "*.dylib", "*.dat", "*.dmg", "*.msi",
    // Database files
    "*.arrow", "*.accdb", "*.aof", "*.avro", "*.bson", "*.db", "*.dbf", 
    "*.dmp", "*.frm", "*.ibd", "*.mdb", "*.myd", "*.myi", "*.orc", 
    "*.parquet", "*.pdb", "*.rdb", "*.sqlite",
    // GIS files
    "*.shp", "*.shx", "*.sbn", "*.sbx", "*.gdb", "*.gpkg", "*.kmz", 
    "*.dem", "*.img", "*.ecw", "*.las", "*.laz", "*.mxd", "*.qgs", 
    "*.grd", "*.dwg", "*.dxf"
];

/**
 * Checkpoint System Manager
 * Singleton class for managing git-based checkpoints
 */
class CheckpointSystem {
    static instance = null;
    
    constructor() {
        this.status = "uninitialized";
        this.checkpoints = [];
        this.shadowRepoPath = undefined;
    }
    
    /**
     * Gets the singleton instance
     * @returns {CheckpointSystem} The singleton instance
     */
    static getInstance() {
        if (!CheckpointSystem.instance) {
            CheckpointSystem.instance = new CheckpointSystem();
        }
        return CheckpointSystem.instance;
    }
    
    /**
     * Initializes the checkpoint system
     * @returns {Promise<void>}
     */
    async initialize() {
        if (this.status !== "uninitialized") {
            return;
        }
        
        this.status = "initializing";
        
        try {
            const workingDir = process.cwd();
            const dirHash = crypto.createHash("sha256").update(workingDir).digest("hex");
            const checkpointDir = path.join(workingDir, ".claude", "checkpoints", dirHash);
            
            if (!fs.existsSync(checkpointDir)) {
                fs.mkdirSync(checkpointDir, { recursive: true });
            }
            
            this.shadowRepoPath = checkpointDir;
            const gitDir = path.join(checkpointDir, ".git");
            
            if (!fs.existsSync(gitDir)) {
                // Initialize new git repository
                const initResult = await this.executeGitCommand(["init"], { cwd: checkpointDir });
                if (initResult.code !== 0) {
                    throw new Error("Failed to initialize checkpointing (init)");
                }
                
                await this.setupGitIgnore(gitDir, workingDir);
                
                const configResult = await this.executeGitCommand(
                    ["config", "--local", "core.worktree", workingDir], 
                    { cwd: checkpointDir }
                );
                if (configResult.code !== 0) {
                    throw new Error("Failed to initialize checkpointing (config)");
                }
                
                await this.executeGitCommand(["add", "--all", "--ignore-errors"], { cwd: checkpointDir });
                
                const commitResult = await this.executeGitCommand(
                    ["commit", "-m", "Initial checkpoint", "--allow-empty"], 
                    { cwd: checkpointDir, maxBuffer: MAX_BUFFER_SIZE }
                );
                if (commitResult.code !== 0) {
                    throw new Error("Failed to initialize checkpointing (commit)");
                }
            } else {
                // Verify existing repository
                await this.setupGitIgnore(gitDir, workingDir);
                await this.executeGitCommand(["add", "--all", "--ignore-errors"], { cwd: checkpointDir });
                
                const verifyResult = await this.executeGitCommand(
                    ["commit", "-m", "Initialization check", "--allow-empty"], 
                    { cwd: checkpointDir, maxBuffer: MAX_BUFFER_SIZE }
                );
                if (verifyResult.code !== 0) {
                    throw new Error("Failed to initialize checkpointing (verify)");
                }
            }
            
            this.status = "initialized";
        } catch (error) {
            this.status = "error";
            console.error('Checkpoint initialization error:', error);
            throw error;
        }
    }
    
    /**
     * Saves a new checkpoint
     * @param {string} label - Label for the checkpoint
     * @returns {Promise<Object>} The created checkpoint object
     */
    async saveCheckpoint(label = "Checkpoint") {
        if (this.status !== "initialized" || !this.shadowRepoPath) {
            throw new Error("Checkpointing not initialized");
        }
        
        try {
            await this.executeGitCommand(["add", "--all", "--ignore-errors"], { cwd: this.shadowRepoPath });
            
            const commitResult = await this.executeGitCommand(
                ["commit", "-m", label, "--allow-empty"], 
                { cwd: this.shadowRepoPath }
            );
            if (commitResult.code !== 0) {
                throw new Error("Failed to create backup checkpoint commit");
            }
            
            const hashResult = await this.executeGitCommand(["rev-parse", "HEAD"], { cwd: this.shadowRepoPath });
            if (hashResult.code !== 0) {
                throw new Error("Failed to create restore checkpoint commit");
            }
            
            const checkpoint = {
                commit: hashResult.stdout.trim(),
                timestamp: new Date(),
                label: label
            };
            
            this.checkpoints.push(checkpoint);
            await this.logCheckpoint(checkpoint);
            
            return checkpoint;
        } catch (error) {
            console.error('Checkpoint save error:', error);
            throw error;
        }
    }
    
    /**
     * Restores to a specific checkpoint
     * @param {string} commitHash - The commit hash to restore to
     * @returns {Promise<Object>} The backup checkpoint created before restore
     */
    async restoreCheckpoint(commitHash) {
        if (this.status !== "initialized" || !this.shadowRepoPath) {
            throw new Error("Checkpointing not initialized");
        }
        
        try {
            const backupCheckpoint = await this.saveCheckpoint(
                `Backup checkpoint (before restoring to ${commitHash.substring(0, 9)})`
            );
            
            const currentHashResult = await this.executeGitCommand(["rev-parse", "HEAD"], { cwd: this.shadowRepoPath });
            if (currentHashResult.code !== 0) {
                throw new Error("Failed to create backup checkpoint before restoring");
            }
            
            const currentHash = currentHashResult.stdout.trim();
            
            await this.executeGitCommand(["revert", "--no-commit", `${commitHash}..${currentHash}`], { cwd: this.shadowRepoPath });
            
            await this.executeGitCommand(
                ["commit", "-m", `Restore to checkpoint ${commitHash}`, "--allow-empty"], 
                { cwd: this.shadowRepoPath, maxBuffer: MAX_BUFFER_SIZE }
            );
            
            return backupCheckpoint;
        } catch (error) {
            console.error('Checkpoint restore error:', error);
            throw error;
        }
    }
    
    /**
     * Gets the current status of the checkpoint system
     * @returns {string} The current status
     */
    getStatus() {
        return this.status;
    }
    
    /**
     * Gets all checkpoints in reverse chronological order
     * @returns {Array} Array of checkpoint objects
     */
    getCheckpoints() {
        return this.checkpoints.toReversed();
    }
    
    /**
     * Loads checkpoints from a log object
     * @param {Object} log - Log object containing checkpoints
     * @returns {Promise<void>}
     */
    async loadCheckpointsFromLog(log) {
        const checkpoints = log.checkpoints;
        if (!checkpoints) return;
        
        this.checkpoints = checkpoints.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
    }
    
    /**
     * Saves all checkpoints to log
     * @returns {Promise<void>}
     */
    async saveCheckpointsToLog() {
        for (const checkpoint of this.checkpoints) {
            await this.logCheckpoint(checkpoint);
        }
    }
    
    /**
     * Resets the checkpoint system
     */
    reset() {
        this.status = "uninitialized";
        this.checkpoints = [];
        this.shadowRepoPath = undefined;
    }
    
    /**
     * Executes a git command
     * @param {Array} args - Git command arguments
     * @param {Object} options - Execution options
     * @returns {Promise<Object>} Command result
     */
    async executeGitCommand(args, options = {}) {
        return new Promise((resolve) => {
            try {
                const result = execSync(`git ${args.join(' ')}`, {
                    cwd: options.cwd || process.cwd(),
                    encoding: 'utf8',
                    maxBuffer: options.maxBuffer || 1024 * 1024,
                    stdio: 'pipe'
                });
                resolve({ code: 0, stdout: result, stderr: '' });
            } catch (error) {
                resolve({ 
                    code: error.status || 1, 
                    stdout: error.stdout || '', 
                    stderr: error.stderr || error.message 
                });
            }
        });
    }
    
    /**
     * Sets up git ignore file
     * @param {string} gitDir - Git directory path
     * @param {string} workingDir - Working directory path
     * @returns {Promise<void>}
     */
    async setupGitIgnore(gitDir, workingDir) {
        const lfsPatterns = await this.getLfsPatterns(workingDir);
        const allPatterns = DEFAULT_IGNORE_PATTERNS.concat(lfsPatterns);
        
        const infoDir = path.join(gitDir, "info");
        const excludeFile = path.join(gitDir, "info", "exclude");
        
        if (!fs.existsSync(infoDir)) {
            fs.mkdirSync(infoDir, { recursive: true });
        }
        
        fs.writeFileSync(excludeFile, allPatterns.join('\n'), {
            encoding: "utf8",
            flag: 'w'
        });
    }
    
    /**
     * Gets LFS patterns from .gitattributes
     * @param {string} workingDir - Working directory path
     * @returns {Promise<Array>} Array of LFS patterns
     */
    async getLfsPatterns(workingDir) {
        try {
            const gitAttributesPath = path.join(workingDir, ".gitattributes");
            if (fs.existsSync(gitAttributesPath)) {
                const content = fs.readFileSync(gitAttributesPath, { encoding: "utf8" });
                return content
                    .split('\n')
                    .filter(line => line.includes("filter=lfs"))
                    .map(line => line.split(" ")[0]?.trim() || "")
                    .filter(pattern => pattern.length > 0);
            }
        } catch (error) {
            console.error('Error reading .gitattributes:', error);
        }
        return [];
    }
    
    /**
     * Logs a checkpoint (placeholder for actual logging implementation)
     * @param {Object} checkpoint - Checkpoint object to log
     * @returns {Promise<void>}
     */
    async logCheckpoint(checkpoint) {
        // TODO: Implement actual checkpoint logging
        console.log('Checkpoint logged:', checkpoint.label, checkpoint.commit);
    }
}

// Export singleton instance
const checkpointSystem = CheckpointSystem.getInstance();

module.exports = {
    CheckpointSystem,
    checkpointSystem,
    DEFAULT_IGNORE_PATTERNS,
    MAX_BUFFER_SIZE
};
