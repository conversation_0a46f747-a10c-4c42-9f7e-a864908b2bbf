#!/usr/bin/env node

/**
 * Claude Code CLI - Main Entry Point (rF4)
 *
 * Refactored version of the original CLI entry point function rF4.
 * This file serves as the main entry point and orchestrates all the modules.
 *
 * Original function: rF4() at line 354501
 */

const React = require('react');
const { render } = require('ink');
const path = require('path');
const fs = require('fs');

// Import CLI system
const { main: cliMain } = require('../cli/cli-entry');

// Import core modules
const { MainApplicationComponent } = require('./main-app-component');
const { processConversation } = require('../core/conversation-engine');
const { preprocessUserInput, processPromptWithHooks } = require('../core/input-processor');
const { registerTools } = require('../tools/tool-registry');
const { registerBuiltInCommand, getAllCommands } = require('../commands/command-registry');

// Import tool implementations
const { BashTool } = require('../tools/bash-tool');
const { TaskTool } = require('../tools/task-tool');

/**
 * Application configuration
 */
const APP_CONFIG = {
    name: "Claude Code",
    version: "1.0.53",
    description: "AI-powered coding assistant",
    author: "Anthropic",
    homepage: "https://github.com/anthropics/claude-code"
};

/**
 * Default commands available in the application
 */
const DEFAULT_COMMANDS = [
    prCommentsCommand,
    releaseNotesCommand,
    resumeConversationCommand
];

/**
 * Default tools available in the application
 */
const DEFAULT_TOOLS = [
    shellTool
];

/**
 * Application state management
 */
class ApplicationState {
    constructor() {
        this.isLoading = false;
        this.messages = [];
        this.input = "";
        this.mode = "prompt";
        this.verbose = false;
        this.debug = false;
        this.submitCount = 0;
        this.queuedCommands = [];
        this.pastedContents = {};
        this.vimMode = false;
        this.abortController = null;
        this.toolPermissionContext = {};
        this.mcpClients = [];
        this.notification = null;
        this.ideSelection = null;
        this.ideInstallationStatus = "unknown";
        this.apiKeyStatus = "unknown";
        this.autoUpdaterResult = null;
    }
    
    /**
     * Updates application state
     * @param {Object} updates - State updates to apply
     */
    updateState(updates) {
        Object.assign(this, updates);
    }
    
    /**
     * Resets application state to defaults
     */
    reset() {
        this.isLoading = false;
        this.messages = [];
        this.input = "";
        this.mode = "prompt";
        this.submitCount = 0;
        this.queuedCommands = [];
        this.pastedContents = {};
        this.abortController = null;
    }
}

/**
 * Main application class
 */
class ClaudeCodeApp {
    constructor() {
        this.state = new ApplicationState();
        this.commands = [...DEFAULT_COMMANDS];
        this.tools = [...DEFAULT_TOOLS];
        this.initialized = false;
    }
    
    /**
     * Initializes the application
     */
    async initialize() {
        if (this.initialized) return;
        
        try {
            console.log(`Initializing ${APP_CONFIG.name} v${APP_CONFIG.version}...`);
            
            // Initialize checkpoint system
            await checkpointSystem.initialize();
            console.log('Checkpoint system initialized');
            
            // Initialize background task manager
            console.log('Background task manager ready');
            
            // Load configuration
            await this.loadConfiguration();
            
            // Initialize MCP clients if configured
            await this.initializeMCPClients();
            
            this.initialized = true;
            console.log('Application initialized successfully');
        } catch (error) {
            console.error('Failed to initialize application:', error);
            throw error;
        }
    }
    
    /**
     * Loads application configuration
     */
    async loadConfiguration() {
        try {
            const configPath = path.join(process.cwd(), '.claude', 'config.json');
            if (fs.existsSync(configPath)) {
                const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
                console.log('Configuration loaded from', configPath);
                return config;
            }
        } catch (error) {
            console.warn('Could not load configuration:', error.message);
        }
        return {};
    }
    
    /**
     * Initializes MCP (Model Context Protocol) clients
     */
    async initializeMCPClients() {
        // TODO: Implement MCP client initialization
        console.log('MCP clients initialization skipped (not implemented)');
    }
    
    /**
     * Handles user queries
     */
    async handleQuery(messages, abortController, shouldQuery, allowedTools, maxThinkingTokens) {
        try {
            this.state.updateState({ 
                isLoading: true, 
                abortController,
                messages: [...this.state.messages, ...messages]
            });
            
            // TODO: Implement actual query processing with AI model
            console.log('Processing query with', messages.length, 'messages');
            
            // Simulate processing delay
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            if (abortController.signal.aborted) {
                throw new Error('Query was aborted');
            }
            
            // TODO: Process with actual AI model and return response
            const response = {
                type: "assistant",
                content: "This is a placeholder response. The actual AI integration needs to be implemented."
            };
            
            this.state.updateState({
                messages: [...this.state.messages, response],
                isLoading: false,
                abortController: null
            });
            
        } catch (error) {
            console.error('Query processing error:', error);
            this.state.updateState({
                isLoading: false,
                abortController: null
            });
        }
    }
    
    /**
     * Handles application exit
     */
    handleExit() {
        console.log('Exiting Claude Code...');
        
        // Clean up background tasks
        const activeTasks = backgroundTaskManager.getActiveShells();
        if (activeTasks.length > 0) {
            console.log(`Cleaning up ${activeTasks.length} background tasks...`);
            activeTasks.forEach(task => {
                backgroundTaskManager.killShell(task.id);
            });
        }
        
        // Save any pending checkpoints
        if (checkpointSystem.getStatus() === "initialized") {
            checkpointSystem.saveCheckpointsToLog().catch(error => {
                console.warn('Failed to save checkpoints:', error);
            });
        }
        
        process.exit(0);
    }
    
    /**
     * Gets tool use context
     */
    getToolUseContext(messages, tools, abortController, allowedTools) {
        return {
            messages,
            tools,
            abortController,
            allowedTools,
            getToolPermissionContext: () => this.state.toolPermissionContext
        };
    }
    
    /**
     * Renders the application UI
     */
    render() {
        const appProps = {
            debug: this.state.debug,
            ideSelection: this.state.ideSelection,
            toolPermissionContext: this.state.toolPermissionContext,
            setToolPermissionContext: (context) => this.state.updateState({ toolPermissionContext: context }),
            apiKeyStatus: this.state.apiKeyStatus,
            commands: this.commands,
            isLoading: this.state.isLoading,
            onQuery: this.handleQuery.bind(this),
            verbose: this.state.verbose,
            messages: this.state.messages,
            setToolJSX: (jsx) => console.log('Tool JSX:', jsx),
            onAutoUpdaterResult: (result) => this.state.updateState({ autoUpdaterResult: result }),
            autoUpdaterResult: this.state.autoUpdaterResult,
            input: this.state.input,
            onInputChange: (input) => this.state.updateState({ input }),
            mode: this.state.mode,
            onModeChange: (mode) => this.state.updateState({ mode }),
            queuedCommands: this.state.queuedCommands,
            setQueuedCommands: (commands) => this.state.updateState({ queuedCommands: commands }),
            submitCount: this.state.submitCount,
            onSubmitCountChange: (count) => this.state.updateState({ submitCount: count }),
            setIsLoading: (loading) => this.state.updateState({ isLoading: loading }),
            setAbortController: (controller) => this.state.updateState({ abortController: controller }),
            onShowMessageSelector: () => console.log('Show message selector'),
            notification: this.state.notification,
            addNotification: (notification) => this.state.updateState({ notification }),
            mcpClients: this.state.mcpClients,
            pastedContents: this.state.pastedContents,
            setPastedContents: (contents) => this.state.updateState({ pastedContents: contents }),
            vimMode: this.state.vimMode,
            setVimMode: (mode) => this.state.updateState({ vimMode: mode }),
            ideInstallationStatus: this.state.ideInstallationStatus,
            onExit: this.handleExit.bind(this),
            getToolUseContext: this.getToolUseContext.bind(this)
        };
        
        return React.createElement(MainApplication, appProps);
    }
    
    /**
     * Starts the application
     */
    async start() {
        try {
            await this.initialize();
            
            // Handle process signals
            process.on('SIGINT', () => this.handleExit());
            process.on('SIGTERM', () => this.handleExit());
            
            // Render the application
            const { unmount } = render(this.render());
            
            // Handle unmount
            process.on('exit', () => {
                unmount();
            });
            
        } catch (error) {
            console.error('Failed to start application:', error);
            process.exit(1);
        }
    }
}

/**
 * Main entry point
 */
async function main() {
    const app = new ClaudeCodeApp();
    await app.start();
}

// Start the application if this file is run directly
if (require.main === module) {
    main().catch(error => {
        console.error('Application startup failed:', error);
        process.exit(1);
    });
}

module.exports = {
    ClaudeCodeApp,
    ApplicationState,
    APP_CONFIG,
    DEFAULT_COMMANDS,
    DEFAULT_TOOLS
};
