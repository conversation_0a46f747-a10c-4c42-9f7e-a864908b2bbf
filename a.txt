以下是我借助ai来逆向分析重构一个编译后40万行js代码的prompt，但由于代码量太大，仅借助ai来阅读重构的话效果很差。我想借助AST + ai的方案，我的思路如下：

用纯AST的方式先识别去除掉源码里被打包进去的第三方npm包的源码（帮我思考分析一下能否做到），此时在整个依赖树中只需要在每个最早的引入npm包的节点处标记一下，这样是否可以大幅减少ast的体积m，同时生成一份去除掉npm包的新的源码，只在对应的地方留下一个占位标识，这样减少AST的体积。最后我生成一个代码依赖地图，这个依赖地图从根开始，对于函数或者类之类的，不需要关心它里面具体的代码实现，只需要关心这个函数里面的依赖，这样也能减少AST的体积。后续我就顺着着依赖地图和精简的源码来分析
比如：原先第1000 - 10000行实际上是某个库的源码，我只需要标识一下，同时留下它导出的变量名即可
经过剪枝的ast树（结构不一定一样，只是举个例子）：
// 剪枝前
{
   program: {
      a: {
        // npm依赖
        d: {
          // 里面是依赖的依赖
         } 
         e: {}
     }
      b:
      c:
  }
}
// 剪枝后
{
   program: {
      a: {
        // npm依赖，只需要到这一层留下记录信息，更深层的依赖不需要了
        d: {
         
         } 
         e: {}
     }
      b:
      c:
  }
}

### 源码处理
// 原始代码
// 1000-10000行是某个被打包进来的npm库的源码，比如：
// z()是实际的模块加载器
var xxx = z( (pP8, EP0) => {
    EP0.exports = function A(B) {
        // 很多行代码 ...
        // 并且中间可能依赖了其他模块函数（实际上也是这个库的源码,这整个链路的代码都要剔除），
        return x;
    }
}
);
// 去除掉里面实际的源码后，只需要保留占位
var xxx = {}; // 解释是某个库的源码，以及导出的原始变量名，便于被其他引用的地方能查找到
// 并且在使用xxx的地方，应该直接替换成require('原始的npm包名')，比如require('lodash')

###截取出来的需要还原的源码的一部分
import {createRequire as whB} from "node:module";
var EhB = Object.create;
var {getPrototypeOf: HhB, defineProperty: xh1, getOwnPropertyNames: zhB} = Object;
var UhB = Object.prototype.hasOwnProperty;
var G1 = (A, B, Q) => {
    Q = A != null ? EhB(HhB(A)) : {};
    let D = B || !A || !A.__esModule ? xh1(Q, "default", {
        value: A,
        enumerable: !0
    }) : Q;
    for (let Z of zhB(A))
        if (!UhB.call(D, Z))
            xh1(D, Z, {
                get: () => A[Z],
                enumerable: !0
            });
    return D
}
;
var z = (A, B) => () => (B || A((B = {
    exports: {}
}).exports, B),
B.exports);
var Xb = (A, B) => {
    for (var Q in B)
        xh1(A, Q, {
            get: B[Q],
            enumerable: !0,
            configurable: !0,
            set: (D) => B[Q] = () => D
        })
}
;
var UI1 = (A, B) => () => (A && (B = A(A = 0)),
B);
var Y1 = whB(import.meta.url);
var HP0 = z( (pP8, EP0) => {
    EP0.exports = function A(B) {
        return B.map(function(Q) {
            if (Q === "")
                return "''";
            if (Q && typeof Q === "object")
                return Q.op.replace(/(.)/g, "\\$1");
            if (/["\s\\]/.test(Q) && !/'/.test(Q))
                return "'" + Q.replace(/(['])/g, "\\$1") + "'";
            if (/["'\s]/.test(Q))
                return '"' + Q.replace(/(["\\$`!])/g, "\\$1") + '"';
            return String(Q).replace(/([A-Za-z]:)?([#!"$&'()*,:;<=>?@[\\\]^`{|}])/g, "$1\\$2")
        }).join(" ")
    }
}
);
var LP0 = z( (iP8, NP0) => {
    var qP0 = "(?:" + ["\\|\\|", "\\&\\&", ";;", "\\|\\&", "\\<\\(", "\\<\\<\\<", ">>", ">\\&", "<\\&", "[&;()|<>]"].join("|") + ")"
      , zP0 = new RegExp("^" + qP0 + "$")
      , UP0 = "|&;()<> \\t"
      , IiB = '"((\\\\"|[^"])*?)"'
      , YiB = "'((\\\\'|[^'])*?)'"
      , WiB = /^#$/
      , wP0 = "'"
      , $P0 = '"'
      , Qg1 = "$"
      , Ub = ""
      , JiB = 4294967296;
    for (HY1 = 0; HY1 < 4; HY1++)
        Ub += (JiB * Math.random()).toString(16);
    var HY1, XiB = new RegExp("^" + Ub);
    function ViB(A, B) {
        var Q = B.lastIndex, D = [], Z;
        while (Z = B.exec(A))
            if (D.push(Z),
            B.lastIndex === Z.index)
                B.lastIndex += 1;
        return B.lastIndex = Q,
        D
    }
    function CiB(A, B, Q) {
        var D = typeof A === "function" ? A(Q) : A[Q];
        if (typeof D === "undefined" && Q != "")
            D = "";
        else if (typeof D === "undefined")
            D = "$";
        if (typeof D === "object")
            return B + Ub + JSON.stringify(D) + Ub;
        return B + D
    }
    function KiB(A, B, Q) {
        if (!Q)
            Q = {};
        var D = Q.escape || "\\"
          , Z = "(\\" + D + `['"` + UP0 + `]|[^\\s'"` + UP0 + "])+"
          , G = new RegExp(["(" + qP0 + ")", "(" + Z + "|" + IiB + "|" + YiB + ")+"].join("|"),"g")
          , F = ViB(A, G);
        if (F.length === 0)
            return [];
        if (!B)
            B = {};
        var I = !1;
        return F.map(function(Y) {
            var W = Y[0];
            if (!W || I)
                return;
            if (zP0.test(W))
                return {
                    op: W
                };
            var J = !1, X = !1, V = "", C = !1, K;
            function E() {
                K += 1;
                var N, L, T = W.charAt(K);
                if (T === "{") {
                    if (K += 1,
                    W.charAt(K) === "}")
                        throw new Error("Bad substitution: " + W.slice(K - 2, K + 1));
                    if (N = W.indexOf("}", K),
                    N < 0)
                        throw new Error("Bad substitution: " + W.slice(K));
                    L = W.slice(K, N),
                    K = N
                } else if (/[*@#?$!_-]/.test(T))
                    L = T,
                    K += 1;
                else {
                    var R = W.slice(K);
                    if (N = R.match(/[^\w\d_]/),
                    !N)
                        L = R,
                        K = W.length;
                    else
                        L = R.slice(0, N.index),
                        K += N.index - 1
                }
                return CiB(B, "", L)
            }
            for (K = 0; K < W.length; K++) {
                var H = W.charAt(K);
                if (C = C || !J && (H === "*" || H === "?"),
                X)
                    V += H,
                    X = !1;
                else if (J)
                    if (H === J)
                        J = !1;
                    else if (J == wP0)
                        V += H;
                    else if (H === D)
                        if (K += 1,
                        H = W.charAt(K),
                        H === $P0 || H === D || H === Qg1)
                            V += H;
                        else
                            V += D + H;
                    else if (H === Qg1)
                        V += E();
                    else
                        V += H;
                else if (H === $P0 || H === wP0)
                    J = H;
                else if (zP0.test(H))
                    return {
                        op: W
                    };
                else if (WiB.test(H)) {
                    I = !0;
                    var $ = {
                        comment: A.slice(Y.index + K + 1)
                    };
                    if (V.length)
                        return [V, $];
                    return [$]
                } else if (H === D)
                    X = !0;
                else if (H === Qg1)
                    V += E();
                else
                    V += H
            }
            if (C)
                return {
                    op: "glob",
                    pattern: V
                };
            return V
        }).reduce(function(Y, W) {
            return typeof W === "undefined" ? Y : Y.concat(W)
        }, [])
    }
    NP0.exports = function A(B, Q, D) {
        var Z = KiB(B, Q, D);
        if (typeof Q !== "function")
            return Z;
        return Z.reduce(function(G, F) {
            if (typeof F === "object")
                return G.concat(F);
            var I = F.split(RegExp("(" + Ub + ".*?" + Ub + ")", "g"));
            if (I.length === 1)
                return G.concat(I[0]);
            return G.concat(I.filter(Boolean).map(function(Y) {
                if (XiB.test(Y))
                    return JSON.parse(Y.split(Ub)[1]);
                return Y
            }))
        }, [])
    }
}
);
var Qj = z( (EiB) => {
    EiB.quote = HP0();
    EiB.parse = LP0()
}
);
var KE = z( (TP0) => {
    Object.defineProperty(TP0, "__esModule", {
        value: !0
    });
    var MP0 = Object.prototype.toString;
    function UiB(A) {
        switch (MP0.call(A)) {
        case "[object Error]":
        case "[object Exception]":
        case "[object DOMException]":
            return !0;
        default:
            return zY1(A, Error)
        }
    }
    function Ec(A, B) {
        return MP0.call(A) === `[object ${B}]`
    }
    function wiB(A) {
        return Ec(A, "ErrorEvent")
    }
    function $iB(A) {
        return Ec(A, "DOMError")
    }
    function qiB(A) {
        return Ec(A, "DOMException")
    }
    function NiB(A) {
        return Ec(A, "String")
    }
    function RP0(A) {
        return typeof A === "object" && A !== null && "__sentry_template_string__"in A && "__sentry_template_values__"in A
    }
    function LiB(A) {
        return A === null || RP0(A) || typeof A !== "object" && typeof A !== "function"
    }
    function OP0(A) {
        return Ec(A, "Object")
    }
    function MiB(A) {
        return typeof Event !== "undefined" && zY1(A, Event)
    }
    function RiB(A) {
        return typeof Element !== "undefined" && zY1(A, Element)
    }
    function OiB(A) {
        return Ec(A, "RegExp")
    }
    function TiB(A) {
        return Boolean(A && A.then && typeof A.then === "function")
    }
    function PiB(A) {
        return OP0(A) && "nativeEvent"in A && "preventDefault"in A && "stopPropagation"in A
    }
    function SiB(A) {
        return typeof A === "number" && A !== A
    }
    function zY1(A, B) {
        try {
            return A instanceof B
        } catch (Q) {
            return !1
        }
    }
    function jiB(A) {
        return !!(typeof A === "object" && A !== null && (A.__isVue || A._isVue))
    }
    TP0.isDOMError = $iB;
    TP0.isDOMException = qiB;
    TP0.isElement = RiB;
    TP0.isError = UiB;
    TP0.isErrorEvent = wiB;
    TP0.isEvent = MiB;
    TP0.isInstanceOf = zY1;
    TP0.isNaN = SiB;
    TP0.isParameterizedString = RP0;
    TP0.isPlainObject = OP0;
    TP0.isPrimitive = LiB;
    TP0.isRegExp = OiB;
    TP0.isString = NiB;
    TP0.isSyntheticEvent = PiB;
    TP0.isThenable = TiB;
    TP0.isVueViewModel = jiB
}
);
var UA1 = z( (SP0) => {
    Object.defineProperty(SP0, "__esModule", {
        value: !0
    });
    var UY1 = KE();
    function niB(A, B=0) {
        if (typeof A !== "string" || B === 0)
            return A;
        return A.length <= B ? A : `${A.slice(0, B)}...`
    }
    function aiB(A, B) {
        let Q = A
          , D = Q.length;
        if (D <= 150)
            return Q;
        if (B > D)
            B = D;
        let Z = Math.max(B - 60, 0);
        if (Z < 5)
            Z = 0;
        let G = Math.min(Z + 140, D);
        if (G > D - 5)
            G = D;
        if (G === D)
            Z = Math.max(G - 140, 0);
        if (Q = Q.slice(Z, G),
        Z > 0)
            Q = `'{snip} ${Q}`;
        if (G < D)
            Q += " {snip}";
        return Q
    }
    function siB(A, B) {
        if (!Array.isArray(A))
            return "";
        let Q = [];
        for (let D = 0; D < A.length; D++) {
            let Z = A[D];
            try {
                if (UY1.isVueViewModel(Z))
                    Q.push("[VueViewModel]");
                else
                    Q.push(String(Z))
            } catch (G) {
                Q.push("[value cannot be serialized]")
            }
        }
        return Q.join(B)
    }
    function PP0(A, B, Q=!1) {
        if (!UY1.isString(A))
            return !1;
        if (UY1.isRegExp(B))
            return B.test(A);
        if (UY1.isString(B))
            return Q ? A === B : A.includes(B);
        return !1
    }
    function riB(A, B=[], Q=!1) {
        return B.some( (D) => PP0(A, D, Q))
    }
    SP0.isMatchingPattern = PP0;
    SP0.safeJoin = siB;
    SP0.snipLine = aiB;
    SP0.stringMatchesSomePattern = riB;
    SP0.truncate = niB
}
);
var _P0 = z( (kP0) => {
    Object.defineProperty(kP0, "__esModule", {
        value: !0
    });
    var Dg1 = KE()
      , QnB = UA1();
    function DnB(A, B, Q=250, D, Z, G, F) {
        if (!G.exception || !G.exception.values || !F || !Dg1.isInstanceOf(F.originalException, Error))
            return;
        let I = G.exception.values.length > 0 ? G.exception.values[G.exception.values.length - 1] : void 0;
        if (I)
            G.exception.values = ZnB(Zg1(A, B, Z, F.originalException, D, G.exception.values, I, 0), Q)
    }
    function Zg1(A, B, Q, D, Z, G, F, I) {
        if (G.length >= Q + 1)
            return G;
        let Y = [...G];
        if (Dg1.isInstanceOf(D[Z], Error)) {
            jP0(F, I);
            let W = A(B, D[Z])
              , J = Y.length;
            yP0(W, Z, J, I),
            Y = Zg1(A, B, Q, D[Z], Z, [W, ...Y], W, J)
        }
        if (Array.isArray(D.errors))
            D.errors.forEach( (W, J) => {
                if (Dg1.isInstanceOf(W, Error)) {
                    jP0(F, I);
                    let X = A(B, W)
                      , V = Y.length;
                    yP0(X, `errors[${J}]`, V, I),
                    Y = Zg1(A, B, Q, W, Z, [X, ...Y], X, V)
                }
            }
            );
        return Y
    }
    function jP0(A, B) {
        A.mechanism = A.mechanism || {
            type: "generic",
            handled: !0
        },
        A.mechanism = {
            ...A.mechanism,
            ...A.type === "AggregateError" && {
                is_exception_group: !0
            },
            exception_id: B
        }
    }
    function yP0(A, B, Q, D) {
        A.mechanism = A.mechanism || {
            type: "generic",
            handled: !0
        },
        A.mechanism = {
            ...A.mechanism,
            type: "chained",
            source: B,
            exception_id: Q,
            parent_id: D
        }
    }
    function ZnB(A, B) {
        return A.map( (Q) => {
            if (Q.value)
                Q.value = QnB.truncate(Q.value, B);
            return Q
        }
        )
    }
    kP0.applyAggregateErrorsToEvent = DnB
}
);
var KW = z( (xP0) => {
    Object.defineProperty(xP0, "__esModule", {
        value: !0
    });
    function wY1(A) {
        return A && A.Math == Math ? A : void 0
    }
    var Gg1 = typeof globalThis == "object" && wY1(globalThis) || typeof window == "object" && wY1(window) || typeof self == "object" && wY1(self) || typeof global == "object" && wY1(global) || function() {
        return this
    }() || {};
    function FnB() {
        return Gg1
    }
    function InB(A, B, Q) {
        let D = Q || Gg1
          , Z = D.__SENTRY__ = D.__SENTRY__ || {};
        return Z[A] || (Z[A] = B())
    }
    xP0.GLOBAL_OBJ = Gg1;
    xP0.getGlobalObject = FnB;
    xP0.getGlobalSingleton = InB
}
);
var Fg1 = z( (vP0) => {
    Object.defineProperty(vP0, "__esModule", {
        value: !0
    });
    var XnB = KE()
      , VnB = KW()
      , Hc = VnB.getGlobalObject()
      , CnB = 80;
    function KnB(A, B={}) {
        if (!A)
            return "<unknown>";
        try {
            let Q = A, D = 5, Z = [], G = 0, F = 0, I = " > ", Y = I.length, W, J = Array.isArray(B) ? B : B.keyAttrs, X = !Array.isArray(B) && B.maxStringLength || CnB;
            while (Q && G++ < D) {
                if (W = EnB(Q, J),
                W === "html" || G > 1 && F + Z.length * Y + W.length >= X)
                    break;
                Z.push(W),
                F += W.length,
                Q = Q.parentNode
            }
            return Z.reverse().join(I)
        } catch (Q) {
            return "<unknown>"
        }
    }
    function EnB(A, B) {
        let Q = A, D = [], Z, G, F, I, Y;
        if (!Q || !Q.tagName)
            return "";
        if (Hc.HTMLElement) {
            if (Q instanceof HTMLElement && Q.dataset && Q.dataset.sentryComponent)
                return Q.dataset.sentryComponent
        }
        D.push(Q.tagName.toLowerCase());
        let W = B && B.length ? B.filter( (X) => Q.getAttribute(X)).map( (X) => [X, Q.getAttribute(X)]) : null;
        if (W && W.length)
            W.forEach( (X) => {
                D.push(`[${X[0]}="${X[1]}"]`)
            }
            );
        else {
            if (Q.id)
                D.push(`#${Q.id}`);
            if (Z = Q.className,
            Z && XnB.isString(Z)) {
                G = Z.split(/\s+/);
                for (Y = 0; Y < G.length; Y++)
                    D.push(`.${G[Y]}`)
            }
        }
        let J = ["aria-label", "type", "name", "title", "alt"];
        for (Y = 0; Y < J.length; Y++)
            if (F = J[Y],
            I = Q.getAttribute(F),
            I)
                D.push(`[${F}="${I}"]`);
        return D.join("")
    }
    function HnB() {
        try {
            return Hc.document.location.href
        } catch (A) {
            return ""
        }
    }
    function znB(A) {
        if (Hc.document && Hc.document.querySelector)
            return Hc.document.querySelector(A);
        return null
    }
    function UnB(A) {
        if (!Hc.HTMLElement)
            return null;
        let B = A
          , Q = 5;
        for (let D = 0; D < Q; D++) {
            if (!B)
                return null;
            if (B instanceof HTMLElement && B.dataset.sentryComponent)
                return B.dataset.sentryComponent;
            B = B.parentNode
        }
        return null
    }
    vP0.getComponentName = UnB;
    vP0.getDomElement = znB;
    vP0.getLocationHref = HnB;
    vP0.htmlTreeAsString = KnB
}
);
var zq = z( (bP0) => {
    Object.defineProperty(bP0, "__esModule", {
        value: !0
    });
    var LnB = typeof __SENTRY_DEBUG__ === "undefined" || __SENTRY_DEBUG__;
    bP0.DEBUG_BUILD = LnB
}
);
var qU = z( (hP0) => {
    Object.defineProperty(hP0, "__esModule", {
        value: !0
    });
    var RnB = zq()
      , Ig1 = KW()
      , OnB = "Sentry Logger "
      , Yg1 = ["debug", "info", "warn", "error", "log", "assert", "trace"]
      , Wg1 = {};
    function fP0(A) {
        if (!("console"in Ig1.GLOBAL_OBJ))
            return A();
        let B = Ig1.GLOBAL_OBJ.console
          , Q = {}
          , D = Object.keys(Wg1);
        D.forEach( (Z) => {
            let G = Wg1[Z];
            Q[Z] = B[Z],
            B[Z] = G
        }
        );
        try {
            return A()
        } finally {
            D.forEach( (Z) => {
                B[Z] = Q[Z]
            }
            )
        }
    }
    function TnB() {
        let A = !1
          , B = {
            enable: () => {
                A = !0
            }
            ,
            disable: () => {
                A = !1
            }
            ,
            isEnabled: () => A
        };
        if (RnB.DEBUG_BUILD)
            Yg1.forEach( (Q) => {
                B[Q] = (...D) => {
                    if (A)
                        fP0( () => {
                            Ig1.GLOBAL_OBJ.console[Q](`${OnB}[${Q}]:`, ...D)
                        }
                        )
                }
            }
            );
        else
            Yg1.forEach( (Q) => {
                B[Q] = () => {
                    return
                }
            }
            );
        return B
    }
    var PnB = TnB();
    hP0.CONSOLE_LEVELS = Yg1;
    hP0.consoleSandbox = fP0;
    hP0.logger = PnB;
    hP0.originalConsoleMethods = Wg1
}
);
var Jg1 = z( (mP0) => {
    Object.defineProperty(mP0, "__esModule", {
        value: !0
    });
    var _nB = zq()
      , wA1 = qU()
      , xnB = /^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;
    function vnB(A) {
        return A === "http" || A === "https"
    }
    function bnB(A, B=!1) {
        let {host: Q, path: D, pass: Z, port: G, projectId: F, protocol: I, publicKey: Y} = A;
        return `${I}://${Y}${B && Z ? `:${Z}` : ""}@${Q}${G ? `:${G}` : ""}/${D ? `${D}/` : D}${F}`
    }
    function gP0(A) {
        let B = xnB.exec(A);
        if (!B) {
            wA1.consoleSandbox( () => {
                console.error(`Invalid Sentry Dsn: ${A}`)
            }
            );
            return
        }
        let[Q,D,Z="",G,F="",I] = B.slice(1)
          , Y = ""
          , W = I
          , J = W.split("/");
        if (J.length > 1)
            Y = J.slice(0, -1).join("/"),
            W = J.pop();
        if (W) {
            let X = W.match(/^\d+/);
            if (X)
                W = X[0]
        }
        return uP0({
            host: G,
            pass: Z,
            path: Y,
            projectId: W,
            port: F,
            protocol: Q,
            publicKey: D
        })
    }
    function uP0(A) {
        return {
            protocol: A.protocol,
            publicKey: A.publicKey || "",
            pass: A.pass || "",
            host: A.host,
            port: A.port || "",
            path: A.path || "",
            projectId: A.projectId
        }
    }
    function fnB(A) {
        if (!_nB.DEBUG_BUILD)
            return !0;
        let {port: B, projectId: Q, protocol: D} = A;
        if (["protocol", "publicKey", "host", "projectId"].find( (F) => {
            if (!A[F])
                return wA1.logger.error(`Invalid Sentry Dsn: ${F} missing`),
                !0;
            return !1
        }
        ))
            return !1;
        if (!Q.match(/^\d+$/))
            return wA1.logger.error(`Invalid Sentry Dsn: Invalid projectId ${Q}`),
            !1;
        if (!vnB(D))
            return wA1.logger.error(`Invalid Sentry Dsn: Invalid protocol ${D}`),
            !1;
        if (B && isNaN(parseInt(B, 10)))
            return wA1.logger.error(`Invalid Sentry Dsn: Invalid port ${B}`),
            !1;
        return !0
    }
    function hnB(A) {
        let B = typeof A === "string" ? gP0(A) : uP0(A);
        if (!B || !fnB(B))
            return;
        return B
    }
    mP0.dsnFromString = gP0;
    mP0.dsnToString = bnB;
    mP0.makeDsn = hnB
}
);
var Xg1 = z( (cP0) => {
    Object.defineProperty(cP0, "__esModule", {
        value: !0
    });
    class dP0 extends Error {
        constructor(A, B="warn") {
            super(A);
            this.message = A,
            this.name = new.target.prototype.constructor.name,
            Object.setPrototypeOf(this, new.target.prototype),
            this.logLevel = B
        }
    }
    cP0.SentryError = dP0
}
);
var EE = z( (rP0) => {
    Object.defineProperty(rP0, "__esModule", {
        value: !0
    });
    var cnB = Fg1()
      , lnB = zq()
      , zc = KE()
      , pnB = qU()
      , lP0 = UA1();
    function inB(A, B, Q) {
        if (!(B in A))
            return;
        let D = A[B]
          , Z = Q(D);
        if (typeof Z === "function")
            aP0(Z, D);
        A[B] = Z
    }
    function nP0(A, B, Q) {
        try {
            Object.defineProperty(A, B, {
                value: Q,
                writable: !0,
                configurable: !0
            })
        } catch (D) {
            lnB.DEBUG_BUILD && pnB.logger.log(`Failed to add non-enumerable property "${B}" to object`, A)
        }
    }
    function aP0(A, B) {
        try {
            let Q = B.prototype || {};
            A.prototype = B.prototype = Q,
            nP0(A, "__sentry_original__", B)
        } catch (Q) {}
    }
    function nnB(A) {
        return A.__sentry_original__
    }
    function anB(A) {
        return Object.keys(A).map( (B) => `${encodeURIComponent(B)}=${encodeURIComponent(A[B])}`).join("&")
    }
    function sP0(A) {
        if (zc.isError(A))
            return {
                message: A.message,
                name: A.name,
                stack: A.stack,
                ...iP0(A)
            };
        else if (zc.isEvent(A)) {
            let B = {
                type: A.type,
                target: pP0(A.target),
                currentTarget: pP0(A.currentTarget),
                ...iP0(A)
            };
            if (typeof CustomEvent !== "undefined" && zc.isInstanceOf(A, CustomEvent))
                B.detail = A.detail;
            return B
        } else
            return A
    }
    function pP0(A) {
        try {
            return zc.isElement(A) ? cnB.htmlTreeAsString(A) : Object.prototype.toString.call(A)
        } catch (B) {
            return "<unknown>"
        }
    }
    function iP0(A) {
        if (typeof A === "object" && A !== null) {
            let B = {};
            for (let Q in A)
                if (Object.prototype.hasOwnProperty.call(A, Q))
                    B[Q] = A[Q];
            return B
        } else
            return {}
    }
    function snB(A, B=40) {
        let Q = Object.keys(sP0(A));
        if (Q.sort(),
        !Q.length)
            return "[object has no keys]";
        if (Q[0].length >= B)
            return lP0.truncate(Q[0], B);
        for (let D = Q.length; D > 0; D--) {
            let Z = Q.slice(0, D).join(", ");
            if (Z.length > B)
                continue;
            if (D === Q.length)
                return Z;
            return lP0.truncate(Z, B)
        }
        return ""
    }
    function rnB(A) {
        return Vg1(A, new Map)
    }
    function Vg1(A, B) {
        if (onB(A)) {
            let Q = B.get(A);
            if (Q !== void 0)
                return Q;
            let D = {};
            B.set(A, D);
            for (let Z of Object.keys(A))
                if (typeof A[Z] !== "undefined")
                    D[Z] = Vg1(A[Z], B);
            return D
        }
        if (Array.isArray(A)) {
            let Q = B.get(A);
            if (Q !== void 0)
                return Q;
            let D = [];
            return B.set(A, D),
            A.forEach( (Z) => {
                D.push(Vg1(Z, B))
            }
            ),
            D
        }
        return A
    }
    function onB(A) {
        if (!zc.isPlainObject(A))
            return !1;
        try {
            let B = Object.getPrototypeOf(A).constructor.name;
            return !B || B === "Object"
        } catch (B) {
            return !0
        }
    }
    function tnB(A) {
        let B;
        switch (!0) {
        case (A === void 0 || A === null):
            B = new String(A);
            break;
        case (typeof A === "symbol" || typeof A === "bigint"):
            B = Object(A);
            break;
        case zc.isPrimitive(A):
            B = new A.constructor(A);
            break;
        default:
            B = A;
            break
        }
        return B
    }
    rP0.addNonEnumerableProperty = nP0;
    rP0.convertToPlainObject = sP0;
    rP0.dropUndefinedKeys = rnB;
    rP0.extractExceptionKeysForMessage = snB;
    rP0.fill = inB;
    rP0.getOriginalFunction = nnB;
    rP0.markFunctionWrapped = aP0;
    rP0.objectify = tnB;
    rP0.urlEncode = anB
}
);
var $Y1 = z( (tP0) => {
    Object.defineProperty(tP0, "__esModule", {
        value: !0
    });
    function oP0(A, B=!1) {
        return !(B || A && !A.startsWith("/") && !A.match(/^[A-Z]:/) && !A.startsWith(".") && !A.match(/^[a-zA-Z]([a-zA-Z0-9.\-+])*:\/\//)) && A !== void 0 && !A.includes("node_modules/")
    }
    function YaB(A) {
        let B = /^\s*[-]{4,}$/
          , Q = /at (?:async )?(?:(.+?)\s+\()?(?:(.+):(\d+):(\d+)?|([^)]+))\)?/;
        return (D) => {
            let Z = D.match(Q);
            if (Z) {
                let G, F, I, Y, W;
                if (Z[1]) {
                    I = Z[1];
                    let V = I.lastIndexOf(".");
                    if (I[V - 1] === ".")
                        V--;
                    if (V > 0) {
                        G = I.slice(0, V),
                        F = I.slice(V + 1);
                        let C = G.indexOf(".Module");
                        if (C > 0)
                            I = I.slice(C + 1),
                            G = G.slice(0, C)
                    }
                    Y = void 0
                }
                if (F)
                    Y = G,
                    W = F;
                if (F === "<anonymous>")
                    W = void 0,
                    I = void 0;
                if (I === void 0)
                    W = W || "<anonymous>",
                    I = Y ? `${Y}.${W}` : W;
                let J = Z[2] && Z[2].startsWith("file://") ? Z[2].slice(7) : Z[2]
                  , X = Z[5] === "native";
                if (J && J.match(/\/[A-Z]:/))
                    J = J.slice(1);
                if (!J && Z[5] && !X)
                    J = Z[5];
                return {
                    filename: J,
                    module: A ? A(J) : void 0,
                    function: I,
                    lineno: parseInt(Z[3], 10) || void 0,
                    colno: parseInt(Z[4], 10) || void 0,
                    in_app: oP0(J, X)
                }
            }
            if (D.match(B))
                return {
                    filename: D
                };
            return
        }
    }
    tP0.filenameIsInApp = oP0;
    tP0.node = YaB
}
);
var qY1 = z( (GS0) => {
    Object.defineProperty(GS0, "__esModule", {
        value: !0
    });
    var BS0 = $Y1()
      , QS0 = 50
      , eP0 = /\(error: (.*)\)/
      , AS0 = /captureMessage|captureException/;
    function DS0(...A) {
        let B = A.sort( (Q, D) => Q[0] - D[0]).map( (Q) => Q[1]);
        return (Q, D=0) => {
            let Z = []
              , G = Q.split(`
`);
            for (let F = D; F < G.length; F++) {
                let I = G[F];
                if (I.length > 1024)
                    continue;
                let Y = eP0.test(I) ? I.replace(eP0, "$1") : I;
                if (Y.match(/\S*Error: /))
                    continue;
                for (let W of B) {
                    let J = W(Y);
                    if (J) {
                        Z.push(J);
                        break
                    }
                }
                if (Z.length >= QS0)
                    break
            }
            return ZS0(Z)
        }
    }
    function XaB(A) {
        if (Array.isArray(A))
            return DS0(...A);
        return A
    }
    function ZS0(A) {
        if (!A.length)
            return [];
        let B = Array.from(A);
        if (/sentryWrapped/.test(B[B.length - 1].function || ""))
            B.pop();
        if (B.reverse(),
        AS0.test(B[B.length - 1].function || "")) {
            if (B.pop(),
            AS0.test(B[B.length - 1].function || ""))
                B.pop()
        }
        return B.slice(0, QS0).map( (Q) => ({
            ...Q,
            filename: Q.filename || B[B.length - 1].filename,
            function: Q.function || "?"
        }))
    }
    var Cg1 = "<anonymous>";
    function VaB(A) {
        try {
            if (!A || typeof A !== "function")
                return Cg1;
            return A.name || Cg1
        } catch (B) {
            return Cg1
        }
    }
    function CaB(A) {
        return [90, BS0.node(A)]
    }
    GS0.filenameIsInApp = BS0.filenameIsInApp;
    GS0.createStackParser = DS0;
    GS0.getFunctionName = VaB;
    GS0.nodeStackLineParser = CaB;
    GS0.stackParserFromStackParserOptions = XaB;
    GS0.stripSentryFramesAndReverse = ZS0
}
);
var MR = z( (IS0) => {
    Object.defineProperty(IS0, "__esModule", {
        value: !0
    });
    var $aB = zq()
      , qaB = qU()
      , NaB = qY1()
      , Uc = {}
      , FS0 = {};
    function LaB(A, B) {
        Uc[A] = Uc[A] || [],
        Uc[A].push(B)
    }
    function MaB() {
        Object.keys(Uc).forEach( (A) => {
            Uc[A] = void 0
        }
        )
    }
    function RaB(A, B) {
        if (!FS0[A])
            B(),
            FS0[A] = !0
    }
    function OaB(A, B) {
        let Q = A && Uc[A];
        if (!Q)
            return;
        for (let D of Q)
            try {
                D(B)
            } catch (Z) {
                $aB.DEBUG_BUILD && qaB.logger.error(`Error while triggering instrumentation handler.
Type: ${A}
Name: ${NaB.getFunctionName(D)}
Error:`, Z)
            }
    }
    IS0.addHandler = LaB;
    IS0.maybeInstrument = RaB;
    IS0.resetInstrumentationHandlers = MaB;
    IS0.triggerHandlers = OaB
}
);
var Hg1 = z( (YS0) => {
    Object.defineProperty(YS0, "__esModule", {
        value: !0
    });
    var Kg1 = qU()
      , yaB = EE()
      , NY1 = KW()
      , Eg1 = MR();
    function kaB(A) {
        Eg1.addHandler("console", A),
        Eg1.maybeInstrument("console", _aB)
    }
    function _aB() {
        if (!("console"in NY1.GLOBAL_OBJ))
            return;
        Kg1.CONSOLE_LEVELS.forEach(function(A) {
            if (!(A in NY1.GLOBAL_OBJ.console))
                return;
            yaB.fill(NY1.GLOBAL_OBJ.console, A, function(B) {
                return Kg1.originalConsoleMethods[A] = B,
                function(...Q) {
                    let D = {
                        args: Q,
                        level: A
                    };
                    Eg1.triggerHandlers("console", D);
                    let Z = Kg1.originalConsoleMethods[A];
                    Z && Z.apply(NY1.GLOBAL_OBJ.console, Q)
                }
            })
        })
    }
    YS0.addConsoleInstrumentationHandler = kaB
}
);
var $A1 = z( (JS0) => {
    Object.defineProperty(JS0, "__esModule", {
        value: !0
    });
    var vaB = EE()
      , zg1 = UA1()
      , baB = KW();
    function faB() {
        let A = baB.GLOBAL_OBJ
          , B = A.crypto || A.msCrypto
          , Q = () => Math.random() * 16;
        try {
            if (B && B.randomUUID)
                return B.randomUUID().replace(/-/g, "");
            if (B && B.getRandomValues)
                Q = () => {
                    let D = new Uint8Array(1);
                    return B.getRandomValues(D),
                    D[0]
                }
        } catch (D) {}
        return ([1e7] + 1000 + 4000 + 8000 + 100000000000).replace(/[018]/g, (D) => (D ^ (Q() & 15) >> D / 4).toString(16))
    }
    function WS0(A) {
        return A.exception && A.exception.values ? A.exception.values[0] : void 0
    }
    function haB(A) {
        let {message: B, event_id: Q} = A;
        if (B)
            return B;
        let D = WS0(A);
        if (D) {
            if (D.type && D.value)
                return `${D.type}: ${D.value}`;
            return D.type || D.value || Q || "<unknown>"
        }
        return Q || "<unknown>"
    }
    function gaB(A, B, Q) {
        let D = A.exception = A.exception || {}
          , Z = D.values = D.values || []
          , G = Z[0] = Z[0] || {};
        if (!G.value)
            G.value = B || "";
        if (!G.type)
            G.type = Q || "Error"
    }
    function uaB(A, B) {
        let Q = WS0(A);
        if (!Q)
            return;
        let D = {
            type: "generic",
            handled: !0
        }
          , Z = Q.mechanism;
        if (Q.mechanism = {
            ...D,
            ...Z,
            ...B
        },
        B && "data"in B) {
            let G = {
                ...Z && Z.data,
                ...B.data
            };
            Q.mechanism.data = G
        }
    }
    var maB = /^(0|[1-9]\d*)\.(0|[1-9]\d*)\.(0|[1-9]\d*)(?:-((?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\.(?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?$/;
    function daB(A) {
        let B = A.match(maB) || []
          , Q = parseInt(B[1], 10)
          , D = parseInt(B[2], 10)
          , Z = parseInt(B[3], 10);
        return {
            buildmetadata: B[5],
            major: isNaN(Q) ? void 0 : Q,
            minor: isNaN(D) ? void 0 : D,
            patch: isNaN(Z) ? void 0 : Z,
            prerelease: B[4]
        }
    }
    function caB(A, B, Q=5) {
        if (B.lineno === void 0)
            return;
        let D = A.length
          , Z = Math.max(Math.min(D - 1, B.lineno - 1), 0);
        B.pre_context = A.slice(Math.max(0, Z - Q), Z).map( (G) => zg1.snipLine(G, 0)),
        B.context_line = zg1.snipLine(A[Math.min(D - 1, Z)], B.colno || 0),
        B.post_context = A.slice(Math.min(Z + 1, D), Z + 1 + Q).map( (G) => zg1.snipLine(G, 0))
    }
    function laB(A) {
        if (A && A.__sentry_captured__)
            return !0;
        try {
            vaB.addNonEnumerableProperty(A, "__sentry_captured__", !0)
        } catch (B) {}
        return !1
    }
    function paB(A) {
        return Array.isArray(A) ? A : [A]
    }
    JS0.addContextToFrame = caB;
    JS0.addExceptionMechanism = uaB;
    JS0.addExceptionTypeValue = gaB;
    JS0.arrayify = paB;
    JS0.checkOrSetAlreadyCaught = laB;
    JS0.getEventDescription = haB;
    JS0.parseSemver = daB;
    JS0.uuid4 = faB
}
);
var qg1 = z( (KS0) => {
    Object.defineProperty(KS0, "__esModule", {
        value: !0
    });
    var AsB = $A1(), LY1 = EE(), BsB = KW(), Ug1 = MR(), wc = BsB.GLOBAL_OBJ, QsB = 1000, XS0, wg1, $g1;
    function DsB(A) {
        Ug1.addHandler("dom", A),
        Ug1.maybeInstrument("dom", CS0)
    }
    function CS0() {
        if (!wc.document)
            return;
        let A = Ug1.triggerHandlers.bind(null, "dom")
          , B = VS0(A, !0);
        wc.document.addEventListener("click", B, !1),
        wc.document.addEventListener("keypress", B, !1),
        ["EventTarget", "Node"].forEach( (Q) => {
            let D = wc[Q] && wc[Q].prototype;
            if (!D || !D.hasOwnProperty || !D.hasOwnProperty("addEventListener"))
                return;
            LY1.fill(D, "addEventListener", function(Z) {
                return function(G, F, I) {
                    if (G === "click" || G == "keypress")
                        try {
                            let Y = this
                              , W = Y.__sentry_instrumentation_handlers__ = Y.__sentry_instrumentation_handlers__ || {}
                              , J = W[G] = W[G] || {
                                refCount: 0
                            };
                            if (!J.handler) {
                                let X = VS0(A);
                                J.handler = X,
                                Z.call(this, G, X, I)
                            }
                            J.refCount++
                        } catch (Y) {}
                    return Z.call(this, G, F, I)
                }
            }),
            LY1.fill(D, "removeEventListener", function(Z) {
                return function(G, F, I) {
                    if (G === "click" || G == "keypress")
                        try {
                            let Y = this
                              , W = Y.__sentry_instrumentation_handlers__ || {}
                              , J = W[G];
                            if (J) {
                                if (J.refCount--,
                                J.refCount <= 0)
                                    Z.call(this, G, J.handler, I),
                                    J.handler = void 0,
                                    delete W[G];
                                if (Object.keys(W).length === 0)
                                    delete Y.__sentry_instrumentation_handlers__
                            }
                        } catch (Y) {}
                    return Z.call(this, G, F, I)
                }
            })
        }
        )
    }
    function ZsB(A) {
        if (A.type !== wg1)
            return !1;
        try {
            if (!A.target || A.target._sentryId !== $g1)
                return !1
        } catch (B) {}
        return !0
    }
    function GsB(A, B) {
        if (A !== "keypress")
            return !1;
        if (!B || !B.tagName)
            return !0;
        if (B.tagName === "INPUT" || B.tagName === "TEXTAREA" || B.isContentEditable)
            return !1;
        return !0
    }
    function VS0(A, B=!1) {
        return (Q) => {
            if (!Q || Q._sentryCaptured)
                return;
            let D = FsB(Q);
            if (GsB(Q.type, D))
                return;
            if (LY1.addNonEnumerableProperty(Q, "_sentryCaptured", !0),
            D && !D._sentryId)
                LY1.addNonEnumerableProperty(D, "_sentryId", AsB.uuid4());
            let Z = Q.type === "keypress" ? "input" : Q.type;
            if (!ZsB(Q))
                A({
                    event: Q,
                    name: Z,
                    global: B
                }),
                wg1 = Q.type,
                $g1 = D ? D._sentryId : void 0;
            clearTimeout(XS0),
            XS0 = wc.setTimeout( () => {
                $g1 = void 0,
                wg1 = void 0
            }
            , QsB)
        }
    }
    function FsB(A) {
        try {
            return A.target
        } catch (B) {
            return null
        }
    }
    KS0.addClickKeypressInstrumentationHandler = DsB;
    KS0.instrumentDOM = CS0
}
);
var Mg1 = z( (ES0) => {
    Object.defineProperty(ES0, "__esModule", {
        value: !0
    });
    var WsB = zq()
      , JsB = qU()
      , XsB = KW()
      , MY1 = XsB.getGlobalObject();
    function VsB() {
        try {
            return new ErrorEvent(""),
            !0
        } catch (A) {
            return !1
        }
    }
    function CsB() {
        try {
            return new DOMError(""),
            !0
        } catch (A) {
            return !1
        }
    }
    function KsB() {
        try {
            return new DOMException(""),
            !0
        } catch (A) {
            return !1
        }
    }
    function Lg1() {
        if (!("fetch"in MY1))
            return !1;
        try {
            return new Request("http://www.example.com"),
            !0
        } catch (A) {
            return !1
        }
    }
    function Ng1(A) {
        return A && /^function fetch\(\)\s+\{\s+\[native code\]\s+\}$/.test(A.toString())
    }
    function EsB() {
        if (typeof EdgeRuntime === "string")
            return !0;
        if (!Lg1())
            return !1;
        if (Ng1(MY1.fetch))
            return !0;
        let A = !1
          , B = MY1.document;
        if (B && typeof B.createElement === "function")
            try {
                let Q = B.createElement("iframe");
                if (Q.hidden = !0,
                B.head.appendChild(Q),
                Q.contentWindow && Q.contentWindow.fetch)
                    A = Ng1(Q.contentWindow.fetch);
                B.head.removeChild(Q)
            } catch (Q) {
                WsB.DEBUG_BUILD && JsB.logger.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ", Q)
            }
        return A
    }
    function HsB() {
        return "ReportingObserver"in MY1
    }
    function zsB() {
        if (!Lg1())
            return !1;
        try {
            return new Request("_",{
                referrerPolicy: "origin"
            }),
            !0
        } catch (A) {
            return !1
        }
    }
    ES0.isNativeFetch = Ng1;
    ES0.supportsDOMError = CsB;
    ES0.supportsDOMException = KsB;
    ES0.supportsErrorEvent = VsB;
    ES0.supportsFetch = Lg1;
    ES0.supportsNativeFetch = EsB;
    ES0.supportsReferrerPolicy = zsB;
    ES0.supportsReportingObserver = HsB
}
);
var Og1 = z( (wS0) => {
    Object.defineProperty(wS0, "__esModule", {
        value: !0
    });
    var OsB = EE()
      , TsB = Mg1()
      , HS0 = KW()
      , qA1 = MR();
    function PsB(A) {
        qA1.addHandler("fetch", A),
        qA1.maybeInstrument("fetch", SsB)
    }
    function SsB() {
        if (!TsB.supportsNativeFetch())
            return;
        OsB.fill(HS0.GLOBAL_OBJ, "fetch", function(A) {
            return function(...B) {
                let {method: Q, url: D} = US0(B)
                  , Z = {
                    args: B,
                    fetchData: {
                        method: Q,
                        url: D
                    },
                    startTimestamp: Date.now()
                };
                return qA1.triggerHandlers("fetch", {
                    ...Z
                }),
                A.apply(HS0.GLOBAL_OBJ, B).then( (G) => {
                    let F = {
                        ...Z,
                        endTimestamp: Date.now(),
                        response: G
                    };
                    return qA1.triggerHandlers("fetch", F),
                    G
                }
                , (G) => {
                    let F = {
                        ...Z,
                        endTimestamp: Date.now(),
                        error: G
                    };
                    throw qA1.triggerHandlers("fetch", F),
                    G
                }
                )
            }
        })
    }
    function Rg1(A, B) {
        return !!A && typeof A === "object" && !!A[B]
    }
    function zS0(A) {
        if (typeof A === "string")
            return A;
        if (!A)
            return "";
        if (Rg1(A, "url"))
            return A.url;
        if (A.toString)
            return A.toString();
        return ""
    }
    function US0(A) {
        if (A.length === 0)
            return {
                method: "GET",
                url: ""
            };
        if (A.length === 2) {
            let[Q,D] = A;
            return {
                url: zS0(Q),
                method: Rg1(D, "method") ? String(D.method).toUpperCase() : "GET"
            }
        }
        let B = A[0];
        return {
            url: zS0(B),
            method: Rg1(B, "method") ? String(B.method).toUpperCase() : "GET"
        }
    }
    wS0.addFetchInstrumentationHandler = PsB;
    wS0.parseFetchArgs = US0
}
);
var Sg1 = z( ($S0) => {
    Object.defineProperty($S0, "__esModule", {
        value: !0
    });
    var Tg1 = KW()
      , Pg1 = MR()
      , RY1 = null;
    function ksB(A) {
        Pg1.addHandler("error", A),
        Pg1.maybeInstrument("error", _sB)
    }
    function _sB() {
        RY1 = Tg1.GLOBAL_OBJ.onerror,
        Tg1.GLOBAL_OBJ.onerror = function(A, B, Q, D, Z) {
            let G = {
                column: D,
                error: Z,
                line: Q,
                msg: A,
                url: B
            };
            if (Pg1.triggerHandlers("error", G),
            RY1 && !RY1.__SENTRY_LOADER__)
                return RY1.apply(this, arguments);
            return !1
        }
        ,
        Tg1.GLOBAL_OBJ.onerror.__SENTRY_INSTRUMENTED__ = !0
    }
    $S0.addGlobalErrorInstrumentationHandler = ksB
}
);
var kg1 = z( (qS0) => {
    Object.defineProperty(qS0, "__esModule", {
        value: !0
    });
    var jg1 = KW()
      , yg1 = MR()
      , OY1 = null;
    function vsB(A) {
        yg1.addHandler("unhandledrejection", A),
        yg1.maybeInstrument("unhandledrejection", bsB)
    }
    function bsB() {
        OY1 = jg1.GLOBAL_OBJ.onunhandledrejection,
        jg1.GLOBAL_OBJ.onunhandledrejection = function(A) {
            let B = A;
            if (yg1.triggerHandlers("unhandledrejection", B),
            OY1 && !OY1.__SENTRY_LOADER__)
                return OY1.apply(this, arguments);
            return !0
        }
        ,
        jg1.GLOBAL_OBJ.onunhandledrejection.__SENTRY_INSTRUMENTED__ = !0
    }
    qS0.addGlobalUnhandledRejectionInstrumentationHandler = vsB
}
);
var _g1 = z( (NS0) => {
    Object.defineProperty(NS0, "__esModule", {
        value: !0
    });
    var hsB = KW()
      , TY1 = hsB.getGlobalObject();
    function gsB() {
        let A = TY1.chrome
          , B = A && A.app && A.app.runtime
          , Q = "history"in TY1 && !!TY1.history.pushState && !!TY1.history.replaceState;
        return !B && Q
    }
    NS0.supportsHistory = gsB
}
);
var xg1 = z( (MS0) => {
    Object.defineProperty(MS0, "__esModule", {
        value: !0
    });
    var LS0 = EE();
    zq();
    qU();
    var msB = KW(), dsB = _g1(), SY1 = MR(), NA1 = msB.GLOBAL_OBJ, PY1;
    function csB(A) {
        SY1.addHandler("history", A),
        SY1.maybeInstrument("history", lsB)
    }
    function lsB() {
        if (!dsB.supportsHistory())
            return;
        let A = NA1.onpopstate;
        NA1.onpopstate = function(...Q) {
            let D = NA1.location.href
              , Z = PY1;
            PY1 = D;
            let G = {
                from: Z,
                to: D
            };
            if (SY1.triggerHandlers("history", G),
            A)
                try {
                    return A.apply(this, Q)
                } catch (F) {}
        }
        ;
        function B(Q) {
            return function(...D) {
                let Z = D.length > 2 ? D[2] : void 0;
                if (Z) {
                    let G = PY1
                      , F = String(Z);
                    PY1 = F;
                    let I = {
                        from: G,
                        to: F
                    };
                    SY1.triggerHandlers("history", I)
                }
                return Q.apply(this, D)
            }
        }
        LS0.fill(NA1.history, "pushState", B),
        LS0.fill(NA1.history, "replaceState", B)
    }
    MS0.addHistoryInstrumentationHandler = csB
}
);
var vg1 = z( (OS0) => {
    Object.defineProperty(OS0, "__esModule", {
        value: !0
    });
    var yY1 = KE()
      , jY1 = EE()
      , isB = KW()
      , kY1 = MR()
      , nsB = isB.GLOBAL_OBJ
      , LA1 = "__sentry_xhr_v3__";
    function asB(A) {
        kY1.addHandler("xhr", A),
        kY1.maybeInstrument("xhr", RS0)
    }
    function RS0() {
        if (!nsB.XMLHttpRequest)
            return;
        let A = XMLHttpRequest.prototype;
        jY1.fill(A, "open", function(B) {
            return function(...Q) {
                let D = Date.now()
                  , Z = yY1.isString(Q[0]) ? Q[0].toUpperCase() : void 0
                  , G = ssB(Q[1]);
                if (!Z || !G)
                    return B.apply(this, Q);
                if (this[LA1] = {
                    method: Z,
                    url: G,
                    request_headers: {}
                },
                Z === "POST" && G.match(/sentry_key/))
                    this.__sentry_own_request__ = !0;
                let F = () => {
                    let I = this[LA1];
                    if (!I)
                        return;
                    if (this.readyState === 4) {
                        try {
                            I.status_code = this.status
                        } catch (W) {}
                        let Y = {
                            args: [Z, G],
                            endTimestamp: Date.now(),
                            startTimestamp: D,
                            xhr: this
                        };
                        kY1.triggerHandlers("xhr", Y)
                    }
                }
                ;
                if ("onreadystatechange"in this && typeof this.onreadystatechange === "function")
                    jY1.fill(this, "onreadystatechange", function(I) {
                        return function(...Y) {
                            return F(),
                            I.apply(this, Y)
                        }
                    });
                else
                    this.addEventListener("readystatechange", F);
                return jY1.fill(this, "setRequestHeader", function(I) {
                    return function(...Y) {
                        let[W,J] = Y
                          , X = this[LA1];
                        if (X && yY1.isString(W) && yY1.isString(J))
                            X.request_headers[W.toLowerCase()] = J;
                        return I.apply(this, Y)
                    }
                }),
                B.apply(this, Q)
            }
        }),
        jY1.fill(A, "send", function(B) {
            return function(...Q) {
                let D = this[LA1];
                if (!D)
                    return B.apply(this, Q);
                if (Q[0] !== void 0)
                    D.body = Q[0];
                let Z = {
                    args: [D.method, D.url],
                    startTimestamp: Date.now(),
                    xhr: this
                };
                return kY1.triggerHandlers("xhr", Z),
                B.apply(this, Q)
            }
        })
    }
    function ssB(A) {
        if (yY1.isString(A))
            return A;
        try {
            return A.toString()
        } catch (B) {}
        return
    }
    OS0.SENTRY_XHR_DATA_KEY = LA1;
    OS0.addXhrInstrumentationHandler = asB;
    OS0.instrumentXHR = RS0
}
);
var xS0 = z( (_S0) => {
    Object.defineProperty(_S0, "__esModule", {
        value: !0
    });
    var esB = zq()
      , ArB = qU()
      , TS0 = Hg1()
      , PS0 = qg1()
      , SS0 = Og1()
      , jS0 = Sg1()
      , yS0 = kg1()
      , kS0 = xg1()
      , bg1 = vg1();
    function BrB(A, B) {
        switch (A) {
        case "console":
            return TS0.addConsoleInstrumentationHandler(B);
        case "dom":
            return PS0.addClickKeypressInstrumentationHandler(B);
        case "xhr":
            return bg1.addXhrInstrumentationHandler(B);
        case "fetch":
            return SS0.addFetchInstrumentationHandler(B);
        case "history":
            return kS0.addHistoryInstrumentationHandler(B);
        case "error":
            return jS0.addGlobalErrorInstrumentationHandler(B);
        case "unhandledrejection":
            return yS0.addGlobalUnhandledRejectionInstrumentationHandler(B);
        default:
            esB.DEBUG_BUILD && ArB.logger.warn("unknown instrumentation type:", A)
        }
    }
    _S0.addConsoleInstrumentationHandler = TS0.addConsoleInstrumentationHandler;
    _S0.addClickKeypressInstrumentationHandler = PS0.addClickKeypressInstrumentationHandler;
    _S0.addFetchInstrumentationHandler = SS0.addFetchInstrumentationHandler;
    _S0.addGlobalErrorInstrumentationHandler = jS0.addGlobalErrorInstrumentationHandler;
    _S0.addGlobalUnhandledRejectionInstrumentationHandler = yS0.addGlobalUnhandledRejectionInstrumentationHandler;
    _S0.addHistoryInstrumentationHandler = kS0.addHistoryInstrumentationHandler;
    _S0.SENTRY_XHR_DATA_KEY = bg1.SENTRY_XHR_DATA_KEY;
    _S0.addXhrInstrumentationHandler = bg1.addXhrInstrumentationHandler;
    _S0.addInstrumentationHandler = BrB
}
);
var fg1 = z( (vS0) => {
    Object.defineProperty(vS0, "__esModule", {
        value: !0
    });
    function XrB() {
        return typeof __SENTRY_BROWSER_BUNDLE__ !== "undefined" && !!__SENTRY_BROWSER_BUNDLE__
    }
    function VrB() {
        return "npm"
    }
    vS0.getSDKSource = VrB;
    vS0.isBrowserBundle = XrB
}
);
var hg1 = z( (bS0, xY1) => {
    Object.defineProperty(bS0, "__esModule", {
        value: !0
    });
    var ErB = fg1();
    function HrB() {
        return !ErB.isBrowserBundle() && Object.prototype.toString.call(typeof process !== "undefined" ? process : 0) === "[object process]"
    }
    function _Y1(A, B) {
        return A.require(B)
    }
    function zrB(A) {
        let B;
        try {
            B = _Y1(xY1, A)
        } catch (Q) {}
        try {
            let {cwd: Q} = _Y1(xY1, "process");
            B = _Y1(xY1, `${Q()}/node_modules/${A}`)
        } catch (Q) {}
        return B
    }
    bS0.dynamicRequire = _Y1;
    bS0.isNodeEnv = HrB;
    bS0.loadModule = zrB
}
);
var gS0 = z( (hS0) => {
    Object.defineProperty(hS0, "__esModule", {
        value: !0
    });
    var qrB = hg1()
      , fS0 = KW();
    function NrB() {
        return typeof window !== "undefined" && (!qrB.isNodeEnv() || LrB())
    }
    function LrB() {
        return fS0.GLOBAL_OBJ.process !== void 0 && fS0.GLOBAL_OBJ.process.type === "renderer"
    }
    hS0.isBrowser = NrB
}
);
var gg1 = z( (uS0) => {
    Object.defineProperty(uS0, "__esModule", {
        value: !0
    });
    function RrB() {
        let A = typeof WeakSet === "function"
          , B = A ? new WeakSet : [];
        function Q(Z) {
            if (A) {
                if (B.has(Z))
                    return !0;
                return B.add(Z),
                !1
            }
            for (let G = 0; G < B.length; G++)
                if (B[G] === Z)
                    return !0;
            return B.push(Z),
            !1
        }
        function D(Z) {
            if (A)
                B.delete(Z);
            else
                for (let G = 0; G < B.length; G++)
                    if (B[G] === Z) {
                        B.splice(G, 1);
                        break
                    }
        }
        return [Q, D]
    }
    uS0.memoBuilder = RrB
}
);
var MA1 = z( (cS0) => {
    Object.defineProperty(cS0, "__esModule", {
        value: !0
    });
    var ug1 = KE()
      , TrB = gg1()
      , PrB = EE()
      , SrB = qY1();
    function mS0(A, B=100, Q=1 / 0) {
        try {
            return vY1("", A, B, Q)
        } catch (D) {
            return {
                ERROR: `**non-serializable** (${D})`
            }
        }
    }
    function dS0(A, B=3, Q=102400) {
        let D = mS0(A, B);
        if (_rB(D) > Q)
            return dS0(A, B - 1, Q);
        return D
    }
    function vY1(A, B, Q=1 / 0, D=1 / 0, Z=TrB.memoBuilder()) {
        let[G,F] = Z;
        if (B == null || ["number", "boolean", "string"].includes(typeof B) && !ug1.isNaN(B))
            return B;
        let I = jrB(A, B);
        if (!I.startsWith("[object "))
            return I;
        if (B.__sentry_skip_normalization__)
            return B;
        let Y = typeof B.__sentry_override_normalization_depth__ === "number" ? B.__sentry_override_normalization_depth__ : Q;
        if (Y === 0)
            return I.replace("object ", "");
        if (G(B))
            return "[Circular ~]";
        let W = B;
        if (W && typeof W.toJSON === "function")
            try {
                let C = W.toJSON();
                return vY1("", C, Y - 1, D, Z)
            } catch (C) {}
        let J = Array.isArray(B) ? [] : {}
          , X = 0
          , V = PrB.convertToPlainObject(B);
        for (let C in V) {
            if (!Object.prototype.hasOwnProperty.call(V, C))
                continue;
            if (X >= D) {
                J[C] = "[MaxProperties ~]";
                break
            }
            let K = V[C];
            J[C] = vY1(C, K, Y - 1, D, Z),
            X++
        }
        return F(B),
        J
    }
    function jrB(A, B) {
        try {
            if (A === "domain" && B && typeof B === "object" && B._events)
                return "[Domain]";
            if (A === "domainEmitter")
                return "[DomainEmitter]";
            if (typeof global !== "undefined" && B === global)
                return "[Global]";
            if (typeof window !== "undefined" && B === window)
                return "[Window]";
            if (typeof document !== "undefined" && B === document)
                return "[Document]";
            if (ug1.isVueViewModel(B))
                return "[VueViewModel]";
            if (ug1.isSyntheticEvent(B))
                return "[SyntheticEvent]";
            if (typeof B === "number" && B !== B)
                return "[NaN]";
            if (typeof B === "function")
                return `[Function: ${SrB.getFunctionName(B)}]`;
            if (typeof B === "symbol")
                return `[${String(B)}]`;
            if (typeof B === "bigint")
                return `[BigInt: ${String(B)}]`;
            let Q = yrB(B);
            if (/^HTML(\w*)Element$/.test(Q))
                return `[HTMLElement: ${Q}]`;
            return `[object ${Q}]`
        } catch (Q) {
            return `**non-serializable** (${Q})`
        }
    }
    function yrB(A) {
        let B = Object.getPrototypeOf(A);
        return B ? B.constructor.name : "null prototype"
    }
    function krB(A) {
        return ~-encodeURI(A).split(/%..|./).length
    }
    function _rB(A) {
        return krB(JSON.stringify(A))
    }
    function xrB(A, B) {
        let Q = B.replace(/\\/g, "/").replace(/[|\\{}()[\]^$+*?.]/g, "\\$&")
          , D = A;
        try {
            D = decodeURI(A)
        } catch (Z) {}
        return D.replace(/\\/g, "/").replace(/webpack:\/?/g, "").replace(new RegExp(`(file://)?/*${Q}/*`,"ig"), "app:///")
    }
    cS0.normalize = mS0;
    cS0.normalizeToSize = dS0;
    cS0.normalizeUrlToBase = xrB;
    cS0.walk = vY1
}
);
var rS0 = z( (sS0) => {
    Object.defineProperty(sS0, "__esModule", {
        value: !0
    });
    function pS0(A, B) {
        let Q = 0;
        for (let D = A.length - 1; D >= 0; D--) {
            let Z = A[D];
            if (Z === ".")
                A.splice(D, 1);
            else if (Z === "..")
                A.splice(D, 1),
                Q++;
            else if (Q)
                A.splice(D, 1),
                Q--
        }
        if (B)
            for (; Q--; Q)
                A.unshift("..");
        return A
    }
    var grB = /^(\S+:\\|\/?)([\s\S]*?)((?:\.{1,2}|[^/\\]+?|)(\.[^./\\]*|))(?:[/\\]*)$/;
    function iS0(A) {
        let B = A.length > 1024 ? `<truncated>${A.slice(-1024)}` : A
          , Q = grB.exec(B);
        return Q ? Q.slice(1) : []
    }
    function mg1(...A) {
        let B = ""
          , Q = !1;
        for (let D = A.length - 1; D >= -1 && !Q; D--) {
            let Z = D >= 0 ? A[D] : "/";
            if (!Z)
                continue;
            B = `${Z}/${B}`,
            Q = Z.charAt(0) === "/"
        }
        return B = pS0(B.split("/").filter( (D) => !!D), !Q).join("/"),
        (Q ? "/" : "") + B || "."
    }
    function lS0(A) {
        let B = 0;
        for (; B < A.length; B++)
            if (A[B] !== "")
                break;
        let Q = A.length - 1;
        for (; Q >= 0; Q--)
            if (A[Q] !== "")
                break;
        if (B > Q)
            return [];
        return A.slice(B, Q - B + 1)
    }
    function urB(A, B) {
        A = mg1(A).slice(1),
        B = mg1(B).slice(1);
        let Q = lS0(A.split("/"))
          , D = lS0(B.split("/"))
          , Z = Math.min(Q.length, D.length)
          , G = Z;
        for (let I = 0; I < Z; I++)
            if (Q[I] !== D[I]) {
                G = I;
                break
            }
        let F = [];
        for (let I = G; I < Q.length; I++)
            F.push("..");
        return F = F.concat(D.slice(G)),
        F.join("/")
    }
    function nS0(A) {
        let B = aS0(A)
          , Q = A.slice(-1) === "/"
          , D = pS0(A.split("/").filter( (Z) => !!Z), !B).join("/");
        if (!D && !B)
            D = ".";
        if (D && Q)
            D += "/";
        return (B ? "/" : "") + D
    }
    function aS0(A) {
        return A.charAt(0) === "/"
    }
    function mrB(...A) {
        return nS0(A.join("/"))
    }
    function drB(A) {
        let B = iS0(A)
          , Q = B[0]
          , D = B[1];
        if (!Q && !D)
            return ".";
        if (D)
            D = D.slice(0, D.length - 1);
        return Q + D
    }
    function crB(A, B) {
        let Q = iS0(A)[2];
        if (B && Q.slice(B.length * -1) === B)
            Q = Q.slice(0, Q.length - B.length);
        return Q
    }
    sS0.basename = crB;
    sS0.dirname = drB;
    sS0.isAbsolute = aS0;
    sS0.join = mrB;
    sS0.normalizePath = nS0;
    sS0.relative = urB;
    sS0.resolve = mg1
}
);
var dg1 = z( (oS0) => {
    Object.defineProperty(oS0, "__esModule", {
        value: !0
    });
    var orB = KE(), RR;
    (function(A) {
        A[A.PENDING = 0] = "PENDING";
        let Q = 1;
        A[A.RESOLVED = Q] = "RESOLVED";
        let D = 2;
        A[A.REJECTED = D] = "REJECTED"
    }
    )(RR || (RR = {}));
    function trB(A) {
        return new Uq( (B) => {
            B(A)
        }
        )
    }
    function erB(A) {
        return new Uq( (B, Q) => {
            Q(A)
        }
        )
    }
    class Uq {
        constructor(A) {
            Uq.prototype.__init.call(this),
            Uq.prototype.__init2.call(this),
            Uq.prototype.__init3.call(this),
            Uq.prototype.__init4.call(this),
            this._state = RR.PENDING,
            this._handlers = [];
            try {
                A(this._resolve, this._reject)
            } catch (B) {
                this._reject(B)
            }
        }
        then(A, B) {
            return new Uq( (Q, D) => {
                this._handlers.push([!1, (Z) => {
                    if (!A)
                        Q(Z);
                    else
                        try {
                            Q(A(Z))
                        } catch (G) {
                            D(G)
                        }
                }
                , (Z) => {
                    if (!B)
                        D(Z);
                    else
                        try {
                            Q(B(Z))
                        } catch (G) {
                            D(G)
                        }
                }
                ]),
                this._executeHandlers()
            }
            )
        }
        catch(A) {
            return this.then( (B) => B, A)
        }
        finally(A) {
            return new Uq( (B, Q) => {
                let D, Z;
                return this.then( (G) => {
                    if (Z = !1,
                    D = G,
                    A)
                        A()
                }
                , (G) => {
                    if (Z = !0,
                    D = G,
                    A)
                        A()
                }
                ).then( () => {
                    if (Z) {
                        Q(D);
                        return
                    }
                    B(D)
                }
                )
            }
            )
        }
        __init() {
            this._resolve = (A) => {
                this._setResult(RR.RESOLVED, A)
            }
        }
        __init2() {
            this._reject = (A) => {
                this._setResult(RR.REJECTED, A)
            }
        }
        __init3() {
            this._setResult = (A, B) => {
                if (this._state !== RR.PENDING)
                    return;
                if (orB.isThenable(B)) {
                    B.then(this._resolve, this._reject);
                    return
                }
                this._state = A,
                this._value = B,
                this._executeHandlers()
            }
        }
        __init4() {
            this._executeHandlers = () => {
                if (this._state === RR.PENDING)
                    return;
                let A = this._handlers.slice();
                this._handlers = [],
                A.forEach( (B) => {
                    if (B[0])
                        return;
                    if (this._state === RR.RESOLVED)
                        B[1](this._value);
                    if (this._state === RR.REJECTED)
                        B[2](this._value);
                    B[0] = !0
                }
                )
            }
        }
    }
    oS0.SyncPromise = Uq;
    oS0.rejectedSyncPromise = erB;
    oS0.resolvedSyncPromise = trB
}
);
var eS0 = z( (tS0) => {
    Object.defineProperty(tS0, "__esModule", {
        value: !0
    });
    var DoB = Xg1()
      , cg1 = dg1();
    function ZoB(A) {
        let B = [];
        function Q() {
            return A === void 0 || B.length < A
        }
        function D(F) {
            return B.splice(B.indexOf(F), 1)[0]
        }
        function Z(F) {
            if (!Q())
                return cg1.rejectedSyncPromise(new DoB.SentryError("Not adding Promise because buffer limit was reached."));
            let I = F();
            if (B.indexOf(I) === -1)
                B.push(I);
            return I.then( () => D(I)).then(null, () => D(I).then(null, () => {}
            )),
            I
        }
        function G(F) {
            return new cg1.SyncPromise( (I, Y) => {
                let W = B.length;
                if (!W)
                    return I(!0);
                let J = setTimeout( () => {
                    if (F && F > 0)
                        I(!1)
                }
                , F);
                B.forEach( (X) => {
                    cg1.resolvedSyncPromise(X).then( () => {
                        if (!--W)
                            clearTimeout(J),
                            I(!0)
                    }
                    , Y)
                }
                )
            }
            )
        }
        return {
            $: B,
            add: Z,
            drain: G
        }
    }
    tS0.makePromiseBuffer = ZoB
}
);
var Bj0 = z( (Aj0) => {
    Object.defineProperty(Aj0, "__esModule", {
        value: !0
    });
    function FoB(A) {
        let B = {}
          , Q = 0;
        while (Q < A.length) {
            let D = A.indexOf("=", Q);
            if (D === -1)
                break;
            let Z = A.indexOf(";", Q);
            if (Z === -1)
                Z = A.length;
            else if (Z < D) {
                Q = A.lastIndexOf(";", D - 1) + 1;
                continue
            }
            let G = A.slice(Q, D).trim();
            if (B[G] === void 0) {
                let F = A.slice(D + 1, Z).trim();
                if (F.charCodeAt(0) === 34)
                    F = F.slice(1, -1);
                try {
                    B[G] = F.indexOf("%") !== -1 ? decodeURIComponent(F) : F
                } catch (I) {
                    B[G] = F
                }
            }
            Q = Z + 1
        }
        return B
    }
    Aj0.parseCookie = FoB
}
);
var lg1 = z( (Qj0) => {
    Object.defineProperty(Qj0, "__esModule", {
        value: !0
    });
    function YoB(A) {
        if (!A)
            return {};
        let B = A.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);
        if (!B)
            return {};
        let Q = B[6] || ""
          , D = B[8] || "";
        return {
            host: B[4],
            path: B[5],
            protocol: B[2],
            search: Q,
            hash: D,
            relative: B[5] + Q + D
        }
    }
    function WoB(A) {
        return A.split(/[\?#]/, 1)[0]
    }
    function JoB(A) {
        return A.split(/\\?\//).filter( (B) => B.length > 0 && B !== ",").length
    }
    function XoB(A) {
        let {protocol: B, host: Q, path: D} = A
          , Z = Q && Q.replace(/^.*@/, "[filtered]:[filtered]@").replace(/(:80)$/, "").replace(/(:443)$/, "") || "";
        return `${B ? `${B}://` : ""}${Z}${D}`
    }
    Qj0.getNumberOfUrlSegments = JoB;
    Qj0.getSanitizedUrlString = XoB;
    Qj0.parseUrl = YoB;
    Qj0.stripUrlQueryAndFragment = WoB
}
);
var Yj0 = z( (Ij0) => {
    Object.defineProperty(Ij0, "__esModule", {
        value: !0
    });
    var HoB = Bj0()
      , zoB = zq()
      , Dj0 = KE()
      , UoB = qU()
      , woB = MA1()
      , $oB = lg1()
      , qoB = {
        ip: !1,
        request: !0,
        transaction: !0,
        user: !0
    }
      , NoB = ["cookies", "data", "headers", "method", "query_string", "url"]
      , Zj0 = ["id", "username", "email"];
    function LoB(A, B, Q) {
        if (!A)
            return;
        if (!A.metadata.source || A.metadata.source === "url") {
            let[D,Z] = bY1(B, {
                path: !0,
                method: !0
            });
            A.updateName(D),
            A.setMetadata({
                source: Z
            })
        }
        if (A.setAttribute("url", B.originalUrl || B.url),
        B.baseUrl)
            A.setAttribute("baseUrl", B.baseUrl);
        A.setData("query", Gj0(B, Q))
    }
    function bY1(A, B={}) {
        let Q = A.method && A.method.toUpperCase()
          , D = ""
          , Z = "url";
        if (B.customRoute || A.route)
            D = B.customRoute || `${A.baseUrl || ""}${A.route && A.route.path}`,
            Z = "route";
        else if (A.originalUrl || A.url)
            D = $oB.stripUrlQueryAndFragment(A.originalUrl || A.url || "");
        let G = "";
        if (B.method && Q)
            G += Q;
        if (B.method && B.path)
            G += " ";
        if (B.path && D)
            G += D;
        return [G, Z]
    }
    function MoB(A, B) {
        switch (B) {
        case "path":
            return bY1(A, {
                path: !0
            })[0];
        case "handler":
            return A.route && A.route.stack && A.route.stack[0] && A.route.stack[0].name || "<anonymous>";
        case "methodPath":
        default:
            {
                let Q = A._reconstructedRoute ? A._reconstructedRoute : void 0;
                return bY1(A, {
                    path: !0,
                    method: !0,
                    customRoute: Q
                })[0]
            }
        }
    }
    function RoB(A, B) {
        let Q = {};
        return (Array.isArray(B) ? B : Zj0).forEach( (Z) => {
            if (A && Z in A)
                Q[Z] = A[Z]
        }
        ),
        Q
    }
    function pg1(A, B) {
        let {include: Q=NoB, deps: D} = B || {}
          , Z = {}
          , G = A.headers || {}
          , F = A.method
          , I = G.host || A.hostname || A.host || "<no host>"
          , Y = A.protocol === "https" || A.socket && A.socket.encrypted ? "https" : "http"
          , W = A.originalUrl || A.url || ""
          , J = W.startsWith(Y) ? W : `${Y}://${I}${W}`;
        return Q.forEach( (X) => {
            switch (X) {
            case "headers":
                {
                    if (Z.headers = G,
                    !Q.includes("cookies"))
                        delete Z.headers.cookie;
                    break
                }
            case "method":
                {
                    Z.method = F;
                    break
                }
            case "url":
                {
                    Z.url = J;
                    break
                }
            case "cookies":
                {
                    Z.cookies = A.cookies || G.cookie && HoB.parseCookie(G.cookie) || {};
                    break
                }
            case "query_string":
                {
                    Z.query_string = Gj0(A, D);
                    break
                }
            case "data":
                {
                    if (F === "GET" || F === "HEAD")
                        break;
                    if (A.body !== void 0)
                        Z.data = Dj0.isString(A.body) ? A.body : JSON.stringify(woB.normalize(A.body));
                    break
                }
            default:
                if ({}.hasOwnProperty.call(A, X))
                    Z[X] = A[X]
            }
        }
        ),
        Z
    }
    function OoB(A, B, Q) {
        let D = {
            ...qoB,
            ...Q && Q.include
        };
        if (D.request) {
            let Z = Array.isArray(D.request) ? pg1(B, {
                include: D.request,
                deps: Q && Q.deps
            }) : pg1(B, {
                deps: Q && Q.deps
            });
            A.request = {
                ...A.request,
                ...Z
            }
        }
        if (D.user) {
            let Z = B.user && Dj0.isPlainObject(B.user) ? RoB(B.user, D.user) : {};
            if (Object.keys(Z).length)
                A.user = {
                    ...A.user,
                    ...Z
                }
        }
        if (D.ip) {
            let Z = B.ip || B.socket && B.socket.remoteAddress;
            if (Z)
                A.user = {
                    ...A.user,
                    ip_address: Z
                }
        }
        if (D.transaction && !A.transaction)
            A.transaction = MoB(B, D.transaction);
        return A
    }
    function Gj0(A, B) {
        let Q = A.originalUrl || A.url || "";
        if (!Q)
            return;
        if (Q.startsWith("/"))
            Q = `http://dogs.are.great${Q}`;
        try {
            return A.query || typeof URL !== "undefined" && new URL(Q).search.slice(1) || B && B.url && B.url.parse(Q).query || void 0
        } catch (D) {
            return
        }
    }
    function Fj0(A) {
        let B = {};
        try {
            A.forEach( (Q, D) => {
                if (typeof Q === "string")
                    B[D] = Q
            }
            )
        } catch (Q) {
            zoB.DEBUG_BUILD && UoB.logger.warn("Sentry failed extracting headers from a request object. If you see this, please file an issue.")
        }
        return B
    }
    function ToB(A) {
        let B = Fj0(A.headers);
        return {
            method: A.method,
            url: A.url,
            headers: B
        }
    }
    Ij0.DEFAULT_USER_INCLUDES = Zj0;
    Ij0.addRequestDataToEvent = OoB;
    Ij0.addRequestDataToTransaction = LoB;
    Ij0.extractPathForTransaction = bY1;
    Ij0.extractRequestData = pg1;
    Ij0.winterCGHeadersToDict = Fj0;
    Ij0.winterCGRequestToRequestData = ToB
}
);
var Vj0 = z( (Xj0) => {
    Object.defineProperty(Xj0, "__esModule", {
        value: !0
    });
    var Wj0 = ["fatal", "error", "warning", "log", "info", "debug"];
    function voB(A) {
        return Jj0(A)
    }
    function Jj0(A) {
        return A === "warn" ? "warning" : Wj0.includes(A) ? A : "log"
    }
    Xj0.severityFromString = voB;
    Xj0.severityLevelFromString = Jj0;
    Xj0.validSeverityLevels = Wj0
}
);
var ig1 = z( (zj0) => {
    Object.defineProperty(zj0, "__esModule", {
        value: !0
    });
    var Cj0 = KW()
      , Kj0 = 1000;
    function Ej0() {
        return Date.now() / Kj0
    }
    function goB() {
        let {performance: A} = Cj0.GLOBAL_OBJ;
        if (!A || !A.now)
            return Ej0;
        let B = Date.now() - A.now()
          , Q = A.timeOrigin == null ? B : A.timeOrigin;
        return () => {
            return (Q + A.now()) / Kj0
        }
    }
    var Hj0 = goB()
      , uoB = Hj0;
    zj0._browserPerformanceTimeOriginMode = void 0;
    var moB = ( () => {
        let {performance: A} = Cj0.GLOBAL_OBJ;
        if (!A || !A.now) {
            zj0._browserPerformanceTimeOriginMode = "none";
            return
        }
        let B = 3600000
          , Q = A.now()
          , D = Date.now()
          , Z = A.timeOrigin ? Math.abs(A.timeOrigin + Q - D) : B
          , G = Z < B
          , F = A.timing && A.timing.navigationStart
          , Y = typeof F === "number" ? Math.abs(F + Q - D) : B
          , W = Y < B;
        if (G || W)
            if (Z <= Y)
                return zj0._browserPerformanceTimeOriginMode = "timeOrigin",
                A.timeOrigin;
            else
                return zj0._browserPerformanceTimeOriginMode = "navigationStart",
                F;
        return zj0._browserPerformanceTimeOriginMode = "dateNow",
        D
    }
    )();
    zj0.browserPerformanceTimeOrigin = moB;
    zj0.dateTimestampInSeconds = Ej0;
    zj0.timestampInSeconds = Hj0;
    zj0.timestampWithMs = uoB
}
);
var ag1 = z( (qj0) => {
    Object.defineProperty(qj0, "__esModule", {
        value: !0
    });
    var ioB = zq()
      , noB = KE()
      , aoB = qU()
      , soB = "baggage"
      , ng1 = "sentry-"
      , wj0 = /^sentry-/
      , $j0 = 8192;
    function roB(A) {
        if (!noB.isString(A) && !Array.isArray(A))
            return;
        let B = {};
        if (Array.isArray(A))
            B = A.reduce( (D, Z) => {
                let G = Uj0(Z);
                for (let F of Object.keys(G))
                    D[F] = G[F];
                return D
            }
            , {});
        else {
            if (!A)
                return;
            B = Uj0(A)
        }
        let Q = Object.entries(B).reduce( (D, [Z,G]) => {
            if (Z.match(wj0)) {
                let F = Z.slice(ng1.length);
                D[F] = G
            }
            return D
        }
        , {});
        if (Object.keys(Q).length > 0)
            return Q;
        else
            return
    }
    function ooB(A) {
        if (!A)
            return;
        let B = Object.entries(A).reduce( (Q, [D,Z]) => {
            if (Z)
                Q[`${ng1}${D}`] = Z;
            return Q
        }
        , {});
        return toB(B)
    }
    function Uj0(A) {
        return A.split(",").map( (B) => B.split("=").map( (Q) => decodeURIComponent(Q.trim()))).reduce( (B, [Q,D]) => {
            return B[Q] = D,
            B
        }
        , {})
    }
    function toB(A) {
        if (Object.keys(A).length === 0)
            return;
        return Object.entries(A).reduce( (B, [Q,D], Z) => {
            let G = `${encodeURIComponent(Q)}=${encodeURIComponent(D)}`
              , F = Z === 0 ? G : `${B},${G}`;
            if (F.length > $j0)
                return ioB.DEBUG_BUILD && aoB.logger.warn(`Not adding key: ${Q} with val: ${D} to baggage header due to exceeding baggage size limits.`),
                B;
            else
                return F
        }
        , "")
    }
    qj0.BAGGAGE_HEADER_NAME = soB;
    qj0.MAX_BAGGAGE_STRING_LENGTH = $j0;
    qj0.SENTRY_BAGGAGE_KEY_PREFIX = ng1;
    qj0.SENTRY_BAGGAGE_KEY_PREFIX_REGEX = wj0;
    qj0.baggageHeaderToDynamicSamplingContext = roB;
    qj0.dynamicSamplingContextToSentryBaggageHeader = ooB
}
);
var Rj0 = z( (Mj0) => {
    Object.defineProperty(Mj0, "__esModule", {
        value: !0
    });
    var Nj0 = ag1()
      , HE = $A1()
      , Lj0 = new RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");
    function sg1(A) {
        if (!A)
            return;
        let B = A.match(Lj0);
        if (!B)
            return;
        let Q;
        if (B[3] === "1")
            Q = !0;
        else if (B[3] === "0")
            Q = !1;
        return {
            traceId: B[1],
            parentSampled: Q,
            parentSpanId: B[2]
        }
    }
    function GtB(A, B) {
        let Q = sg1(A)
          , D = Nj0.baggageHeaderToDynamicSamplingContext(B)
          , {traceId: Z, parentSpanId: G, parentSampled: F} = Q || {};
        if (!Q)
            return {
                traceparentData: Q,
                dynamicSamplingContext: void 0,
                propagationContext: {
                    traceId: Z || HE.uuid4(),
                    spanId: HE.uuid4().substring(16)
                }
            };
        else
            return {
                traceparentData: Q,
                dynamicSamplingContext: D || {},
                propagationContext: {
                    traceId: Z || HE.uuid4(),
                    parentSpanId: G || HE.uuid4().substring(16),
                    spanId: HE.uuid4().substring(16),
                    sampled: F,
                    dsc: D || {}
                }
            }
    }
    function FtB(A, B) {
        let Q = sg1(A)
          , D = Nj0.baggageHeaderToDynamicSamplingContext(B)
          , {traceId: Z, parentSpanId: G, parentSampled: F} = Q || {};
        if (!Q)
            return {
                traceId: Z || HE.uuid4(),
                spanId: HE.uuid4().substring(16)
            };
        else
            return {
                traceId: Z || HE.uuid4(),
                parentSpanId: G || HE.uuid4().substring(16),
                spanId: HE.uuid4().substring(16),
                sampled: F,
                dsc: D || {}
            }
    }
    function ItB(A=HE.uuid4(), B=HE.uuid4().substring(16), Q) {
        let D = "";
        if (Q !== void 0)
            D = Q ? "-1" : "-0";
        return `${A}-${B}${D}`
    }
    Mj0.TRACEPARENT_REGEXP = Lj0;
    Mj0.extractTraceparentData = sg1;
    Mj0.generateSentryTraceHeader = ItB;
    Mj0.propagationContextFromHeaders = FtB;
    Mj0.tracingContextFromHeaders = GtB
}
);
var og1 = z( (Pj0) => {
    Object.defineProperty(Pj0, "__esModule", {
        value: !0
    });
    var CtB = Jg1()
      , KtB = MA1()
      , Oj0 = EE();
    function EtB(A, B=[]) {
        return [A, B]
    }
    function HtB(A, B) {
        let[Q,D] = A;
        return [Q, [...D, B]]
    }
    function Tj0(A, B) {
        let Q = A[1];
        for (let D of Q) {
            let Z = D[0].type;
            if (B(D, Z))
                return !0
        }
        return !1
    }
    function ztB(A, B) {
        return Tj0(A, (Q, D) => B.includes(D))
    }
    function rg1(A, B) {
        return (B || new TextEncoder).encode(A)
    }
    function UtB(A, B) {
        let[Q,D] = A
          , Z = JSON.stringify(Q);
        function G(F) {
            if (typeof Z === "string")
                Z = typeof F === "string" ? Z + F : [rg1(Z, B), F];
            else
                Z.push(typeof F === "string" ? rg1(F, B) : F)
        }
        for (let F of D) {
            let[I,Y] = F;
            if (G(`
${JSON.stringify(I)}
`),
            typeof Y === "string" || Y instanceof Uint8Array)
                G(Y);
            else {
                let W;
                try {
                    W = JSON.stringify(Y)
                } catch (J) {
                    W = JSON.stringify(KtB.normalize(Y))
                }
                G(W)
            }
        }
        return typeof Z === "string" ? Z : wtB(Z)
    }
    function wtB(A) {
        let B = A.reduce( (Z, G) => Z + G.length, 0)
          , Q = new Uint8Array(B)
          , D = 0;
        for (let Z of A)
            Q.set(Z, D),
            D += Z.length;
        return Q
    }
    function $tB(A, B, Q) {
        let D = typeof A === "string" ? B.encode(A) : A;
        function Z(Y) {
            let W = D.subarray(0, Y);
            return D = D.subarray(Y + 1),
            W
        }
        function G() {
            let Y = D.indexOf(10);
            if (Y < 0)
                Y = D.length;
            return JSON.parse(Q.decode(Z(Y)))
        }
        let F = G()
          , I = [];
        while (D.length) {
            let Y = G()
              , W = typeof Y.length === "number" ? Y.length : void 0;
            I.push([Y, W ? Z(W) : G()])
        }
        return [F, I]
    }
    function qtB(A, B) {
        let Q = typeof A.data === "string" ? rg1(A.data, B) : A.data;
        return [Oj0.dropUndefinedKeys({
            type: "attachment",
            length: Q.length,
            filename: A.filename,
            content_type: A.contentType,
            attachment_type: A.attachmentType
        }), Q]
    }
    var NtB = {
        session: "session",
        sessions: "session",
        attachment: "attachment",
        transaction: "transaction",
        event: "error",
        client_report: "internal",
        user_report: "default",
        profile: "profile",
        replay_event: "replay",
        replay_recording: "replay",
        check_in: "monitor",
        feedback: "feedback",
        span: "span",
        statsd: "metric_bucket"
    };
    function LtB(A) {
        return NtB[A]
    }
    function MtB(A) {
        if (!A || !A.sdk)
            return;
        let {name: B, version: Q} = A.sdk;
        return {
            name: B,
            version: Q
        }
    }
    function RtB(A, B, Q, D) {
        let Z = A.sdkProcessingMetadata && A.sdkProcessingMetadata.dynamicSamplingContext;
        return {
            event_id: A.event_id,
            sent_at: new Date().toISOString(),
            ...B && {
                sdk: B
            },
            ...!!Q && D && {
                dsn: CtB.dsnToString(D)
            },
            ...Z && {
                trace: Oj0.dropUndefinedKeys({
                    ...Z
                })
            }
        }
    }
    Pj0.addItemToEnvelope = HtB;
    Pj0.createAttachmentEnvelopeItem = qtB;
    Pj0.createEnvelope = EtB;
    Pj0.createEventEnvelopeHeaders = RtB;
    Pj0.envelopeContainsItemType = ztB;
    Pj0.envelopeItemTypeToDataCategory = LtB;
    Pj0.forEachEnvelopeItem = Tj0;
    Pj0.getSdkMetadataForEnvelopeHeader = MtB;
    Pj0.parseEnvelope = $tB;
    Pj0.serializeEnvelope = UtB
}
);
// ....中间省略
function jH8(A) {
    let B = {
        exitOnCtrlC: A,
        onFlicker() {
            K1("tengu_flicker", {})
        }
    };
    if (!process.stdin.isTTY && !dQ(!1) && !process.argv.includes("mcp")) {
        if (process.platform !== "win32")
            try {
                let Q = NH8("/dev/tty", "r");
                B = {
                    ...B,
                    stdin: new qH8(Q)
                }
            } catch (Q) {
                b1(Q)
            }
    }
    return B
}
async function yH8(A, B) {
    if (!process.stdin.isTTY && !process.argv.includes("mcp")) {
        if (B === "stream-json")
            return process.stdin;
        process.stdin.setEncoding("utf8");
        let Q = "";
        return process.stdin.on("data", (D) => {
            Q += D
        }
        ),
        await new Promise( (D) => {
            process.stdin.on("end", D)
        }
        ),
        [A, Q].filter(Boolean).join(`
`)
    }
    return A
}
async function kH8() {
    PH8();
    let A = new kfB;
    A.name("claude").description(`${E2} - starts an interactive session by default, use -p/--print for non-interactive output`).argument("[prompt]", "Your prompt", String).helpOption("-h, --help", "Display help for command").option("-d, --debug", "Enable debug mode", () => !0).option("--verbose", "Override verbose mode setting from config", () => !0).option("-p, --print", "Print response and exit (useful for pipes)", () => !0).addOption(new BS("--output-format <format>",'Output format (only works with --print): "text" (default), "json" (single result), or "stream-json" (realtime streaming)').choices(["text", "json", "stream-json"])).addOption(new BS("--input-format <format>",'Input format (only works with --print): "text" (default), or "stream-json" (realtime streaming input)').choices(["text", "stream-json"])).option("--mcp-debug", "[DEPRECATED. Use --debug instead] Enable MCP debug mode (shows MCP server errors)", () => !0).option("--dangerously-skip-permissions", "Bypass all permission checks. Recommended only for sandboxes with no internet access.", () => !0).addOption(new BS("--max-turns <turns>","Maximum number of agentic turns in non-interactive mode. This will early exit the conversation after the specified number of turns. (only works with --print)").argParser(Number).hideHelp()).option("--allowedTools <tools...>", 'Comma or space-separated list of tool names to allow (e.g. "Bash(git:*) Edit")').option("--disallowedTools <tools...>", 'Comma or space-separated list of tool names to deny (e.g. "Bash(git:*) Edit")').option("--mcp-config <file or string>", "Load MCP servers from a JSON file or string").addOption(new BS("--permission-prompt-tool <tool>","MCP tool to use for permission prompts (only works with --print)").argParser(String).hideHelp()).addOption(new BS("--system-prompt <prompt>","System prompt to use for the session  (only works with --print)").argParser(String).hideHelp()).addOption(new BS("--system-prompt-file <file>","Read system prompt from a file (only works with --print)").argParser(String).hideHelp()).addOption(new BS("--append-system-prompt <prompt>","Append a system prompt to the default system prompt").argParser(String)).addOption(new BS("--permission-mode <mode>","Permission mode to use for the session").argParser(String).choices(dK1)).option("-c, --continue", "Continue the most recent conversation", () => !0).option("-r, --resume [sessionId]", "Resume a conversation - provide a session ID or interactively select a conversation to resume", (D) => D || !0).option("--model <model>", "Model for the current session. Provide an alias for the latest model (e.g. 'sonnet' or 'opus') or a model's full name (e.g. 'claude-sonnet-4-20250514').").option("--fallback-model <model>", "Enable automatic fallback to specified model when default model is overloaded (only works with --print)").option("--settings <file>", "Path to a settings JSON file to load additional settings from").option("--add-dir <directories...>", "Additional directories to allow tool access to").option("--ide", "Automatically connect to IDE on startup if exactly one valid IDE is available", () => !0).option("--strict-mcp-config", "Only use MCP servers from --mcp-config, ignoring all other MCP configurations", () => !0).option("--session-id <uuid>", "Use a specific session ID for the conversation (must be a valid UUID)").action(async (D, Z) => {
        let {debug: G=!1, verbose: F=!1, print: I, dangerouslySkipPermissions: Y, allowedTools: W=[], disallowedTools: J=[], mcpConfig: X, outputFormat: V, inputFormat: C, permissionMode: K, addDir: E=[], fallbackModel: H, ide: $=!1, sessionId: N, settings: L} = Z
          , T = Z.strictMcpConfig || !1
          , R = !1
          , O = null
          , j = null;
        if (N) {
            if (Z.continue || Z.resume)
                process.stderr.write(F0.red(`Error: --session-id cannot be used with --continue or --resume.
`)),
                process.exit(1);
            let C1 = XK(N);
            if (!C1)
                process.stderr.write(F0.red(`Error: Invalid session ID. Must be a valid UUID.
`)),
                process.exit(1);
            if ($w2(C1))
                process.stderr.write(F0.red(`Error: Session ID ${C1} is already in use.
`)),
                process.exit(1)
        }
        let b = Kc();
        if (H && Z.model && H === Z.model)
            process.stderr.write(F0.red(`Error: Fallback model cannot be the same as the main model. Please specify a different model for --fallback-model.
`)),
            process.exit(1);
        let y = Z.systemPrompt;
        if (Z.systemPromptFile) {
            if (Z.systemPrompt)
                process.stderr.write(F0.red(`Error: Cannot use both --system-prompt and --system-prompt-file. Please use only one.
`)),
                process.exit(1);
            try {
                let C1 = ChB(Z.systemPromptFile);
                if (!yM0(C1))
                    process.stderr.write(F0.red(`Error: System prompt file not found: ${C1}
`)),
                    process.exit(1);
                y = LH8(C1, "utf8")
            } catch (C1) {
                process.stderr.write(F0.red(`Error reading system prompt file: ${C1instanceof Error ? C1.message : String(C1)}
`)),
                process.exit(1)
            }
        }
        let c = v_B({
            permissionModeCli: K,
            dangerouslySkipPermissions: Y
        })
          , u = void 0;
        if (X) {
            let C1 = null
              , n1 = []
              , Y0 = d3(X);
            if (Y0) {
                let V0 = T91({
                    configObject: Y0,
                    filePath: "command line",
                    expandVars: !0,
                    scope: "dynamic"
                });
                if (V0.config)
                    C1 = V0.config.mcpServers;
                else
                    n1 = V0.errors
            } else {
                let V0 = ChB(X)
                  , A1 = Ms1({
                    filePath: V0,
                    expandVars: !0,
                    scope: "dynamic"
                });
                if (A1.config)
                    C1 = A1.config.mcpServers;
                else
                    n1 = A1.errors
            }
            if (n1.length > 0) {
                let V0 = n1.map( (A1) => `${A1.path ? A1.path + ": " : ""}${A1.message}`).join(`
`);
                throw new Error(`Invalid MCP configuration:
${V0}`)
            }
            if (C1)
                u = Aj(C1, (V0) => ({
                    ...V0,
                    scope: "dynamic"
                }))
        }
        if (!b) {
            let C1 = await RH8(c);
            if (C1 && D?.trim().toLowerCase() === "/login")
                D = "";
            if (!C1)
                jv1()
        }
        let {toolPermissionContext: r, warnings: s} = b_B({
            allowedToolsCli: W,
            disallowedToolsCli: J,
            permissionMode: c,
            addDirs: E
        });
        if (s.forEach( (C1) => {
            console.error(C1)
        }
        ),
        dqB(),
        yz0(u, T),
        C && C !== "text" && C !== "stream-json")
            console.error(`Error: Invalid input format "${C}".`),
            process.exit(1);
        if (C === "stream-json" && V !== "stream-json")
            console.error("Error: --input-format=stream-json requires output-format=stream-json."),
            process.exit(1);
        let x = await yH8(D || "", C ?? "text")
          , t = $$(r, E0().todoFeatureEnabled);
        if (await Uv(zv(), c, I ?? !1, !1, N ? XK(N) : void 0),
        L)
            try {
                let {resolvedPath: C1} = bq(x1(), L);
                if (!yM0(C1))
                    process.stderr.write(F0.red(`Error: Settings file not found: ${C1}
`)),
                    process.exit(1);
                KP0(C1),
                bs1()
            } catch (C1) {
                process.stderr.write(F0.red(`Error reading settings file: ${C1instanceof Error ? C1.message : String(C1)}
`)),
                process.exit(1)
            }
        let[W1,{clients: z1=[], tools: N1=[], commands: D0=[]}] = await Promise.all([tb1(), x || b ? await yz0(u, T) : {
            clients: [],
            tools: [],
            commands: []
        }]);
        if (K1("tengu_init", {
            entrypoint: "claude",
            hasInitialPrompt: Boolean(D),
            hasStdin: Boolean(x),
            verbose: F,
            debug: G,
            print: I,
            outputFormat: V,
            numAllowedTools: W.length,
            numDisallowedTools: J.length,
            mcpClientCount: Object.keys(iU()).length,
            worktree: !1
        }),
        R$B(),
        l_1(null, "initialization"),
        b) {
            if (V === "stream-json" || V === "json")
                Ko0(!0);
            IhB(x, r, z1, W1, D0, t, N1, {
                continue: Z.continue,
                resume: Z.resume,
                verbose: Z.verbose,
                outputFormat: Z.outputFormat,
                permissionPromptToolName: Z.permissionPromptTool,
                allowedTools: W,
                maxTurns: Z.maxTurns,
                systemPrompt: y,
                appendSystemPrompt: Z.appendSystemPrompt,
                userSpecifiedModel: Z.model,
                fallbackModel: H,
                teleport: null
            });
            return
        }
        let[p1,c1] = await Promise.all([jH8(!1), BNB(Ux1)]);
        K1("tengu_startup_manual_model_config", {
            cli_flag: Z.model,
            env_var: process.env.ANTHROPIC_MODEL,
            settings_file: (G4() || {}).model
        });
        let T1 = Z.model || process.env.ANTHROPIC_MODEL || (G4() || {}).model;
        if (Y9() && !jW() && T1 !== void 0 && T1.includes("opus"))
            console.error(F0.yellow("Claude Pro users are not currently able to use Opus 4 in Claude Code. The current model is now Sonnet 4."));
        let Z1 = Z.model;
        HA1(Z1),
        oT0(jr() || null);
        let _1 = {
            verbose: F ?? !1,
            mainLoopModel: KY1(),
            todoFeatureEnabled: E0().todoFeatureEnabled,
            toolPermissionContext: r,
            maxRateLimitFallbackActive: !1,
            mcp: {
                clients: [],
                tools: [],
                commands: [],
                resources: {}
            }
        };
        if ($f1(r),
        OH8(),
        dQ(!1),
        Z.continue)
            try {
                K1("tengu_continue", {});
                let C1 = await Hv(void 0, N1);
                if (!C1)
                    console.error("No conversation found to continue"),
                    process.exit(1);
                let n1 = Az(cB());
                A5(M7.default.createElement(kD, {
                    initialState: _1,
                    onChangeAppState: em
                }, M7.default.createElement(O01, {
                    debug: G,
                    initialPrompt: x,
                    shouldShowPromptInput: !0,
                    commands: [...W1, ...D0],
                    initialTools: N1,
                    initialMessages: C1.messages,
                    initialTodos: n1,
                    mcpClients: z1,
                    dynamicMcpConfig: u,
                    autoConnectIdeFlag: $,
                    strictMcpConfig: T,
                    appendSystemPrompt: Z.appendSystemPrompt
                })), p1)
            } catch (C1) {
                b1(C1 instanceof Error ? C1 : new Error(String(C1))),
                process.exit(1)
            }
        else if (Z.resume) {
            let C1 = null
              , n1 = XK(Z.resume);
            if (n1) {
                let Y0 = n1;
                try {
                    let V0 = await Hv(Y0, N1);
                    if (!V0)
                        console.error(`No conversation found with session ID: ${Y0}`),
                        process.exit(1);
                    C1 = V0.messages
                } catch (V0) {
                    b1(V0 instanceof Error ? V0 : new Error(String(V0))),
                    console.error(`Failed to resume session ${Y0}`),
                    process.exit(1)
                }
            }
            if (Array.isArray(C1))
                A5(M7.default.createElement(kD, {
                    initialState: _1,
                    onChangeAppState: em
                }, M7.default.createElement(O01, {
                    debug: G,
                    initialPrompt: x,
                    shouldShowPromptInput: !0,
                    commands: [...W1, ...D0],
                    initialTools: N1,
                    initialMessages: C1,
                    mcpClients: z1,
                    dynamicMcpConfig: u,
                    autoConnectIdeFlag: $,
                    strictMcpConfig: T,
                    appendSystemPrompt: Z.appendSystemPrompt
                })), p1);
            else {
                let Y0 = {}
                  , V0 = await xr();
                if (!V0.length)
                    console.error("No conversations found to resume"),
                    process.exit(1);
                let {unmount: A1} = A5(M7.default.createElement(hfB, {
                    commands: [...W1, ...D0],
                    context: Y0,
                    debug: G,
                    logs: V0,
                    initialTools: N1,
                    mcpClients: z1,
                    dynamicMcpConfig: u,
                    appState: _1,
                    onChangeAppState: em,
                    strictMcpConfig: T,
                    appendSystemPrompt: Z.appendSystemPrompt
                }), p1);
                Y0.unmount = A1
            }
        } else {
            let C1 = Az(cB());
            A5(M7.default.createElement(kD, {
                initialState: _1,
                onChangeAppState: em
            }, M7.default.createElement(O01, {
                debug: G,
                commands: [...W1, ...D0],
                initialPrompt: x,
                shouldShowPromptInput: !0,
                initialTools: N1,
                initialTodos: C1,
                tipOfTheDay: c1,
                mcpClients: z1,
                dynamicMcpConfig: u,
                autoConnectIdeFlag: $,
                strictMcpConfig: T,
                appendSystemPrompt: Z.appendSystemPrompt
            })), p1)
        }
    }
    ).version(`${{
        ISSUES_EXPLAINER: "report the issue at https://github.com/anthropics/claude-code/issues",
        PACKAGE_URL: "@anthropic-ai/claude-code",
        README_URL: "https://docs.anthropic.com/s/claude-code",
        VERSION: "1.0.61"
    }.VERSION} (${E2})`, "-v, --version", "Output the version number");
    let B = A.command("config").description("Manage configuration (eg. claude config set -g theme dark)").helpOption("-h, --help", "Display help for command");
    B.command("get <key>").description("Get a config value").option("-g, --global", "Use global config").helpOption("-h, --help", "Display help for command").action(async (D, {global: Z}) => {
        await Uv(zv(), "default", !1, !1, void 0),
        console.log(M$2(D, Z ?? !1)),
        process.exit(0)
    }
    ),
    B.command("set <key> <value>").description("Set a config value").option("-g, --global", "Use global config").helpOption("-h, --help", "Display help for command").action(async (D, Z, {global: G}) => {
        await Uv(zv(), "default", !1, !1, void 0),
        R$2(D, Z, G ?? !1),
        console.log(`Set ${D} to ${Z}`),
        process.exit(0)
    }
    ),
    B.command("remove <key> [values...]").alias("rm").description("Remove a config value or items from a config array").option("-g, --global", "Use global config").helpOption("-h, --help", "Display help for command").action(async (D, Z, {global: G}) => {
        if (await Uv(zv(), "default", !1, !1, void 0),
        mr(D, G ?? !1) && Z && Z.length > 0) {
            let F = Z.flatMap( (I) => I.includes(",") ? I.split(",") : I).map( (I) => I.trim()).filter( (I) => I.length > 0);
            if (F.length === 0)
                console.error("Error: No valid values provided"),
                process.exit(1);
            U$2(D, F, G ?? !1, !1),
            console.log(`Removed from ${D} in ${G ? "global" : "project"} config: ${F.join(", ")}`)
        } else
            O$2(D, G ?? !1),
            console.log(`Removed ${D}`);
        process.exit(0)
    }
    ),
    B.command("list").alias("ls").description("List all config values").option("-g, --global", "Use global config", !1).helpOption("-h, --help", "Display help for command").action(async ({global: D}) => {
        await Uv(zv(), "default", !1, !1, void 0),
        console.log(JSON.stringify(T$2(D ?? !1), null, 2)),
        process.exit(0)
    }
    ),
    B.command("add <key> <values...>").description("Add items to a config array (space or comma separated)").option("-g, --global", "Use global config").helpOption("-h, --help", "Display help for command").action(async (D, Z, {global: G}) => {
        await Uv(zv(), "default", !1, !1, void 0);
        let F = Z.flatMap( (I) => I.includes(",") ? I.split(",") : I).map( (I) => I.trim()).filter( (I) => I.length > 0);
        if (F.length === 0)
            console.error("Error: No valid values provided"),
            process.exit(1);
        QR1(D, F, G ?? !1, !1),
        console.log(`Added to ${D} in ${G ? "global" : "project"} config: ${F.join(", ")}`),
        process.exit(0)
    }
    );
    let Q = A.command("mcp").description("Configure and manage MCP servers").helpOption("-h, --help", "Display help for command");
    return Q.command("serve").description(`Start the ${E2} MCP server`).helpOption("-h, --help", "Display help for command").option("-d, --debug", "Enable debug mode", () => !0).option("--verbose", "Override verbose mode setting from config", () => !0).action(async ({debug: D, verbose: Z}) => {
        let G = zv();
        if (K1("tengu_mcp_start", {}),
        !yM0(G))
            console.error(`Error: Directory ${G} does not exist`),
            process.exit(1);
        try {
            await Uv(G, "default", !1, !1, void 0),
            await dfB(G, D ?? !1, Z ?? !1)
        } catch (F) {
            console.error("Error: Failed to start MCP server:", F),
            process.exit(1)
        }
    }
    ),
    Q.command("add <name> <commandOrUrl> [args...]").description("Add a server").option("-s, --scope <scope>", "Configuration scope (local, user, or project)", "local").option("-t, --transport <transport>", "Transport type (stdio, sse, http)", "stdio").option("-e, --env <env...>", "Set environment variables (e.g. -e KEY=value)").option("-H, --header <header...>", 'Set HTTP headers for SSE and HTTP transports (e.g. -H "X-Api-Key: abc123" -H "X-Custom: value")').helpOption("-h, --help", "Display help for command").action(async (D, Z, G, F) => {
        if (!D)
            console.error("Error: Server name is required."),
            console.error("Usage: claude mcp add <name> <command> [args...]"),
            process.exit(1);
        else if (!Z)
            console.error("Error: Command is required when server name is provided."),
            console.error("Usage: claude mcp add <name> <command> [args...]"),
            process.exit(1);
        try {
            let I = U91(F.scope)
              , Y = iFA(F.transport);
            if (K1("tengu_mcp_add", {
                type: Y,
                scope: I,
                source: "command",
                transport: Y
            }),
            Y === "sse") {
                if (!Z)
                    console.error("Error: URL is required for SSE transport."),
                    process.exit(1);
                let W = F.header ? Zs1(F.header) : void 0;
                if (cf(D, {
                    type: "sse",
                    url: Z,
                    headers: W
                }, I),
                process.stdout.write(`Added SSE MCP server ${D} with URL: ${Z} to ${I} config
`),
                W)
                    process.stdout.write(`Headers: ${JSON.stringify(W, null, 2)}
`)
            } else if (Y === "http") {
                if (!Z)
                    console.error("Error: URL is required for HTTP transport."),
                    process.exit(1);
                let W = F.header ? Zs1(F.header) : void 0;
                if (cf(D, {
                    type: "http",
                    url: Z,
                    headers: W
                }, I),
                process.stdout.write(`Added HTTP MCP server ${D} with URL: ${Z} to ${I} config
`),
                W)
                    process.stdout.write(`Headers: ${JSON.stringify(W, null, 2)}
`)
            } else {
                let W = V$2(F.env);
                cf(D, {
                    type: "stdio",
                    command: Z,
                    args: G || [],
                    env: W
                }, I),
                process.stdout.write(`Added stdio MCP server ${D} with command: ${Z} ${(G || []).join(" ")} to ${I} config
`)
            }
            process.stdout.write(`File modified: ${kC(I)}
`),
            process.exit(0)
        } catch (I) {
            console.error(I.message),
            process.exit(1)
        }
    }
    ),
    Q.command("remove <name>").description("Remove an MCP server").option("-s, --scope <scope>", "Configuration scope (local, user, or project) - if not specified, removes from whichever scope it exists in").helpOption("-h, --help", "Display help for command").action(async (D, Z) => {
        try {
            if (Z.scope) {
                let J = U91(Z.scope);
                K1("tengu_mcp_delete", {
                    name: D,
                    scope: J
                }),
                Ls1(D, J),
                process.stdout.write(`Removed MCP server ${D} from ${J} config
`),
                process.stdout.write(`File modified: ${kC(J)}
`),
                process.exit(0)
            }
            let G = u9()
              , F = E0()
              , {servers: I} = OZ("project")
              , Y = !!I[D]
              , W = [];
            if (G.mcpServers?.[D])
                W.push("local");
            if (Y)
                W.push("project");
            if (F.mcpServers?.[D])
                W.push("user");
            if (W.length === 0)
                process.stderr.write(`No MCP server found with name: "${D}"
`),
                process.exit(1);
            else if (W.length === 1) {
                let J = W[0];
                K1("tengu_mcp_delete", {
                    name: D,
                    scope: J
                }),
                Ls1(D, J),
                process.stdout.write(`Removed MCP server "${D}" from ${J} config
`),
                process.stdout.write(`File modified: ${kC(J)}
`),
                process.exit(0)
            } else
                process.stderr.write(`MCP server "${D}" exists in multiple scopes:
`),
                W.forEach( (J) => {
                    process.stderr.write(`  - ${hf(J)} (${kC(J)})
`)
                }
                ),
                process.stderr.write(`
To remove from a specific scope, use:
`),
                W.forEach( (J) => {
                    process.stderr.write(`  claude mcp remove "${D}" -s ${J}
`)
                }
                ),
                process.exit(1)
        } catch (G) {
            process.stderr.write(`${G.message}
`),
            process.exit(1)
        }
    }
    ),
    Q.command("list").description("List configured MCP servers").helpOption("-h, --help", "Display help for command").action(async () => {
        K1("tengu_mcp_list", {});
        let D = iU();
        if (Object.keys(D).length === 0)
            console.log("No MCP servers configured. Use `claude mcp add` to add a server.");
        else {
            console.log(`Checking MCP server health...
`);
            for (let[Z,G] of Object.entries(D)) {
                let F = await KhB(Z, G);
                if (G.type === "sse")
                    console.log(`${Z}: ${G.url} (SSE) - ${F}`);
                else if (G.type === "http")
                    console.log(`${Z}: ${G.url} (HTTP) - ${F}`);
                else if (!G.type || G.type === "stdio") {
                    let I = Array.isArray(G.args) ? G.args : [];
                    console.log(`${Z}: ${G.command} ${I.join(" ")} - ${F}`)
                }
            }
        }
        process.exit(0)
    }
    ),
    Q.command("get <name>").description("Get details about an MCP server").helpOption("-h, --help", "Display help for command").action(async (D) => {
        K1("tengu_mcp_get", {
            name: D
        });
        let Z = wi(D);
        if (!Z)
            console.error(`No MCP server found with name: ${D}`),
            process.exit(1);
        console.log(`${D}:`),
        console.log(`  Scope: ${hf(Z.scope)}`);
        let G = await KhB(D, Z);
        if (console.log(`  Status: ${G}`),
        Z.type === "sse") {
            if (console.log("  Type: sse"),
            console.log(`  URL: ${Z.url}`),
            Z.headers) {
                console.log("  Headers:");
                for (let[F,I] of Object.entries(Z.headers))
                    console.log(`    ${F}: ${I}`)
            }
        } else if (Z.type === "http") {
            if (console.log("  Type: http"),
            console.log(`  URL: ${Z.url}`),
            Z.headers) {
                console.log("  Headers:");
                for (let[F,I] of Object.entries(Z.headers))
                    console.log(`    ${F}: ${I}`)
            }
        } else if (Z.type === "stdio") {
            console.log("  Type: stdio"),
            console.log(`  Command: ${Z.command}`);
            let F = Array.isArray(Z.args) ? Z.args : [];
            if (console.log(`  Args: ${F.join(" ")}`),
            Z.env) {
                console.log("  Environment:");
                for (let[I,Y] of Object.entries(Z.env))
                    console.log(`    ${I}=${Y}`)
            }
        }
        console.log(`
To remove this server, run: claude mcp remove "${D}" -s ${Z.scope}`),
        process.exit(0)
    }
    ),
    Q.command("add-json <name> <json>").description("Add an MCP server (stdio or SSE) with a JSON string").option("-s, --scope <scope>", "Configuration scope (local, user, or project)", "local").helpOption("-h, --help", "Display help for command").action(async (D, Z, G) => {
        try {
            let F = U91(G.scope)
              , I = d3(Z);
            cf(D, I, F);
            let Y = I && typeof I === "object" && "type"in I ? String(I.type || "stdio") : "stdio";
            K1("tengu_mcp_add", {
                scope: F,
                source: "json",
                type: Y
            }),
            console.log(`Added ${Y} MCP server ${D} to ${F} config`),
            process.exit(0)
        } catch (F) {
            console.error(F.message),
            process.exit(1)
        }
    }
    ),
    Q.command("add-from-claude-desktop").description("Import MCP servers from Claude Desktop (Mac and WSL only)").option("-s, --scope <scope>", "Configuration scope (local, user, or project)", "local").helpOption("-h, --help", "Display help for command").action(async (D) => {
        try {
            let Z = U91(D.scope)
              , G = B9();
            K1("tengu_mcp_add", {
                scope: Z,
                platform: G,
                source: "desktop"
            });
            let F = vfB();
            if (Object.keys(F).length === 0)
                console.log("No MCP servers found in Claude Desktop configuration or configuration file does not exist."),
                process.exit(0);
            let {unmount: I} = A5(M7.default.createElement(kD, null, M7.default.createElement(_fB, {
                servers: F,
                scope: Z,
                onDone: () => {
                    I()
                }
            })), {
                exitOnCtrlC: !0
            })
        } catch (Z) {
            console.error(Z.message),
            process.exit(1)
        }
    }
    ),
    Q.command("reset-project-choices").description("Reset all approved and rejected project-scoped (.mcp.json) servers within this project").helpOption("-h, --help", "Display help for command").action(async () => {
        K1("tengu_mcp_reset_mcpjson_choices", {});
        let D = u9();
        d6({
            ...D,
            enabledMcpjsonServers: [],
            disabledMcpjsonServers: [],
            enableAllProjectMcpServers: !1
        }),
        console.log("All project-scoped (.mcp.json) server approvals and rejections have been reset."),
        console.log("You will be prompted for approval next time you start Claude Code."),
        process.exit(0)
    }
    ),
    A.command("migrate-installer").description("Migrate from global npm installation to local installation").helpOption("-h, --help", "Display help for command").action(async () => {
        if (fx())
            console.log("Already running from local installation. No migration needed."),
            process.exit(0);
        K1("tengu_migrate_installer_command", {}),
        await new Promise( (D) => {
            let {waitUntilExit: Z} = A5(M7.default.createElement(kD, null, M7.default.createElement(j11, null)));
            Z().then( () => {
                D()
            }
            )
        }
        ),
        process.exit(0)
    }
    ),
    A.command("setup-token").description("Set up a long-lived authentication token (requires Claude subscription)").helpOption("-h, --help", "Display help for command").action(async () => {
        if (K1("tengu_setup_token_command", {}),
        await e3(),
        !XH())
            process.stderr.write(F0.yellow(`Warning: You already have authentication configured via environment variable or API key helper.
`)),
            process.stderr.write(F0.yellow(`The setup-token command will create a new OAuth token which you can use instead.
`));
        await new Promise( (D) => {
            let {unmount: Z} = A5(M7.default.createElement(Mm, {
                onDone: () => {
                    Z(),
                    D()
                }
                ,
                mode: "setup-token",
                startingMessage: "This will guide you through long-lived (1-year) auth token setup for your Claude account. Claude subscription required."
            }))
        }
        ),
        process.exit(0)
    }
    ),
    A.command("doctor").description("Check the health of your Claude Code auto-updater").helpOption("-h, --help", "Display help for command").action(async () => {
        K1("tengu_doctor_command", {}),
        await new Promise( (D) => {
            let {unmount: Z} = A5(M7.default.createElement(kD, null, M7.default.createElement(Ex1, {
                onDone: () => {
                    Z(),
                    D()
                }
            })), {
                exitOnCtrlC: !1
            })
        }
        ),
        process.exit(0)
    }
    ),
    A.command("update").description("Check for updates and install if available").helpOption("-h, --help", "Display help for command").action(YhB),
    A.command("install [target]").description("Install Claude Code native build. Use [target] to specify version (stable, latest, or specific version)").option("--force", "Force installation even if already installed").helpOption("-h, --help", "Display help for command").action(async (D, Z) => {
        await Uv(zv(), "default", !1, !1, void 0),
        await new Promise( (G) => {
            let F = [];
            if (D)
                F.push(D);
            if (Z.force)
                F.push("--force");
            VhB.call( () => {
                G(),
                process.exit(0)
            }
            , {}, F)
        }
        )
    }
    ),
    await A.parseAsync(process.argv),
    A
}
function _H8() {
    (process.stderr.isTTY ? process.stderr : process.stdout.isTTY ? process.stdout : void 0)?.write(`\x1B[?25h${Ql1}`)
}
SH8();
export {RH8 as showSetupScreens, Uv as setup, MH8 as completeOnboarding};

###已知npm指纹库
const NPM_FINGERPRINTS = {
    'shell-quote': {
        score: 0,
        strings: [
            "'",
            '"',
            'op', 'glob', 'pattern', // 来自 parse 函数
            'Bad substitution: ', // 来自 parse 函数
            /([A-Za-z]:)?([#!"$&'()*,:;<=>?@[\\\]^`{|}])/g, // 来自 quote 函数
        ],
        // 模块暴露的函数或变量名
        exports: ['quote', 'parse'],
    },
    // ...
]
 

###我之前的prompt

**Role**

你是一位顶级的软件工程师，是 JavaScript 逆向工程与大型代码库重构领域的专家。你的方法论兼具外科手术般的精准和架构师的宏观视角。你善于通过系统性的侦察、追踪和文档化，将一团乱麻的编译产物还原为清晰、优雅的源码。

**Goal**

将一个名为 `cli-format-all-61.js`（约40万行）的、经过编译和打包的单体 JavaScript 文件，系统性地重构为一个功能完全相同、但代码清晰、模块化、易于阅读和维护的 `src` 目录源码树。

**最高原则 (Prime Directive)**

**100% 忠于原始逻辑**：在任何情况下，重构后的代码在功能和行为上必须与原始代码完全等价。这是不可违背的最高约束。

### **核心策略与执行阶段 (Core Strategy & Execution Phases)**

我们将摒弃简单的线性分析，采用一种更智能的、分阶段的“由主干到枝叶”的分析策略。

*   **Phase 1: 侦察与架构扫描 (Reconnaissance & Architecture Scan)**
    1.  **识别打包器模式**: 快速扫描文件，识别是 Webpack、Rollup 还是其他工具的引导代码（例如 `(function(modules) { ... })` 结构中的 `__webpack_require__` 或类似的模块加载器）。理解其模块定义和加载机制。
    2.  **定位核心入口点**: 找到整个 CLI 的启动点。这通常是：
        *   文件末尾的顶层调用。
        *   与 `process.argv` 交互的代码。
        *   被 `#!/usr/bin/env node` shebang 间接引用的启动函数。
    3.  **识别大型依赖**: 快速定位并标记出明显是第三方库的大块代码区域（例如，通过库的注释、特定的字符串或代码模式），暂时将其视为“黑盒”。

*   **Phase 2: 主干逻辑追踪 (Main Logic Tracing)**
    1.  **追踪调用图**: 从入口点开始，沿着核心执行路径（Call Graph）进行追踪。重点关注**命令解析、参数处理、主功能分发**等高层逻辑。
    2.  **构建骨架**: 根据主干逻辑，创建出项目的顶层目录结构和文件，例如 `src/index.js` (或 `src/cli.js`), `src/core/`, `src/commands/`。此时，许多函数实现可以暂时保留原始形式或标记为待实现。
    3.  **优先重命名**: 对主干路径上的关键函数和变量进行有意义的重命名，这将为后续的分析提供宝贵的上下文。

*   **Phase 3: 深度挖掘与模块化 (Deep Dive & Modularization)**
    1.  **逐个击破**: 以主干逻辑中引用的“待解析依赖”为目标，逐个深入分析这些功能模块。
    2.  **定义模块边界**: 为这些功能单元（例如，一个负责文件操作的工具集、一个负责网络请求的模块）定义清晰的边界，并将其拆分到合适的文件中（如 `src/utils/fs-helpers.js`）。
    3.  **递归分析**: 在分析一个模块时，如果它又依赖了其他未解析的代码，则重复此过程，直到一个功能分支上的所有“叶子节点”（最底层的工具函数）都被分析完毕。

*   **Phase 4: 依赖剥离与最终清理 (Dependency Externalization & Final Cleanup)**
    1.  **替换三方库**: 将在 Phase 1 中识别的第三方库代码块，用标准的 `require('library-name')` 替换，并在 `package.json` 中记录依赖。
    2.  **代码风格统一**: 对所有重构后的代码进行格式化和风格统一。
    3.  **审查与验证**: 回顾 `REFACTORING_LOG.md` 中的所有“问题与备注”，确保所有问题都已解决。

---

### **迭代工作流程与状态管理 (Iterative Workflow & State Management)**

每一轮交互都代表着在上述某个阶段中的一次具体行动。

1.  **状态输入 (State Input)**: 在每一轮开始时，你需要接收并消化：
    *   **当前分析目标 (Current Analysis Target)**: 我会明确指出本次的目标，例如：“目标：分析入口函数 `_aW`”、“目标：重构 `_bC` 及其所有直接依赖项”、“目标：将行号 50000-65000 的 `Sentry` 库代码剥离”。
    *   **最新的日志文件**: `REFACTORING_LOG.md` 的内容。
    *   **最新的依赖映射文件**: `REFECTING_MAPPING.json` 的内容。

2.  **分析与交叉引用 (Analysis & Cross-Referencing)**:
    *   **聚焦目标**: 集中分析当前目标代码块。
    *   **查询映射**: 当遇到一个标识符（如函数调用 `_aB(c)`），你必须首先查阅 `REFECTING_MAPPING.json`。
        *   **如果已存在**: 表明该依赖已被重构。你必须使用其新名称和模块路径（`const { newName } = require('./utils/someUtil.js')`），并更新调用关系。
        *   **如果不存在**: 表明该依赖尚未被重构。在当前代码中暂时保留其原始调用，并在日志中明确标注这是一个“**待解析依赖 (Unresolved Dependency)**”，并更新 `REFECTING_MAPPING.json` 为其创建一个占位条目。
    *   **更新调用图**: 在你重构并命名了当前块中的某个函数后（例如将 `_aC` 重构为 `parseConfig`），你需要检查 `REFECTING_MAPPING.json`，找到所有依赖 `_aC` 的函数（`dependents` 字段），并在日志中记录：这些上游函数现在可以更新其对 `parseConfig` 的调用了。

3.  **执行重构 (Execute Refactoring)**: 基于上述分析，对当前代码块执行相应的重构操作。

4.  **生成输出 (Generate Output)**: 以**固定格式**向我报告，内容包括：

    *   `### 1. 本轮目标与摘要 (Target & Summary)`
        *   简述本轮的分析目标和关键发现。

    *   `### 2. 重构后的代码 (Refactored Code)`
        *   提供一个或多个代码块，包含重构后的代码。
        *   每个代码块都应有一个确切的文件名，例如 `// File: src/utils/string-helpers.js`。

    *   `### 3. 日志更新 (Log Updates)`
        *   提供需要**追加**到 `REFACTORING_LOG.md` 的新条目。

    *   `### 4. 映射更新 (Mapping Updates)`
        *   提供需要**更新或追加**到 `REFECTING_MAPPING.json` 的新条目或变更。

    *   `### 5. 下一步建议 (Next Step Recommendation)`
        *   基于本次分析，明确建议下一轮的分析目标。例如：“建议：下一步应深入分析 `parseConfig` 调用的 `_tZ` 函数，它似乎负责读取配置文件。”

---

### **日志与映射文件规范 (Logging & Mapping Specification)**

*   `REFACTORING_LOG.md` (人类友好的决策日志)
    *   **目的**: 记录每一步的分析思路、决策过程、遇到的问题和宏观理解。
    *   **格式**: Markdown, 按轮次/目标记录。

*   `REFECTING_MAPPING.json` (机器友好的知识库)
    *   **目的**: 维护原始世界和重构后世界之间的精确映射，是整个工程的单一事实来源 (Single Source of Truth)。
    *   **格式**: JSON
    *   **结构示例**:

    ```json
    {
      "__METADATA__": {
        "lastUpdated": "2023-10-27T10:00:00Z",
        "currentPhase": "Phase 2: Main Logic Tracing"
      },
      "identifiers": {
        "_aW": {
          "type": "Function",
          "status": "Refactored",
          "newName": "parseCliOptions",
          "filePath": "src/core/options-parser.js",
          "originalLines": "1234-2345",
          "dependencies": ["_aX", "_tZ"],
          "dependents": ["_mainEntry"],
          "notes": "核心的命令行参数解析函数，使用了shell-quote库。"
        },
        "_aX": {
          "type": "Variable",
          "status": "Identified",
          "newName": "shellQuote",
          "isExternal": true,
          "notes": "Identified as the shell-quote library."
        },
        "_tZ": {
            "type": "Function",
            "status": "Identified",
            "newName": null,
            "filePath": null,
            "originalLines": "8900-9100",
            "dependencies": [],
            "dependents": ["_aW"],
            "notes": "Unresolved Dependency. Seems to be related to config file reading."
        }
      },
      "modules": {
         "45000-68000": {
            "type": "Library",
            "name": "sentry/node",
            "version": "7.55.0",
            "status": "Identified",
            "notes": "Located via copyright headers and API strings."
         }
      }
    }
    ```

    --------我之前实现的ast分析脚本（效果不行，运行后没剔除掉npm包，生成出来的代码还是40万行）
    #!/usr/bin/env node

// Filename: scripts/enhanced-ast-pruner.js
const fs = require('fs');
const path = require('path');
const parser = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const generate = require('@babel/generator').default;
const t = require('@babel/types');

// 加载外部指纹库
function loadFingerprintDatabase() {
    try {
        const fingerprintPath = path.resolve(__dirname, '../npm-finger2.cjs');
        if (!fs.existsSync(fingerprintPath)) {
            console.error(`错误: 指纹库文件未找到: ${fingerprintPath}`);
            process.exit(1);
        }
        
        delete require.cache[require.resolve(fingerprintPath)];
        const fingerprints = require(fingerprintPath);
        
        console.log(`✅ 成功加载指纹库，包含 ${Object.keys(fingerprints).length} 个NPM包指纹`);
        return fingerprints;
    } catch (error) {
        console.error(`❌ 加载指纹库失败: ${error.message}`);
        process.exit(1);
    }
}

class EnhancedNpmPackageStripper {
    constructor(sourceCode, fingerprintDatabase) {
        this.sourceCode = sourceCode;
        this.NPM_FINGERPRINTS = fingerprintDatabase;
        this.ast = null;

        // 核心数据结构
        this.moduleRegistry = new Map();        // 所有模块的元信息
        this.dependencyGraph = new Map();       // 模块间依赖关系图
        this.libraryComponents = new Map();     // 识别的库组件集合
        this.removalQueue = new Set();          // 待移除的模块集合
        this.preservedModules = new Set();      // 保留的业务模块

        // 统计信息
        this.stats = {
            totalModules: 0,
            identifiedLibraries: 0,
            removedModules: 0,
            preservedModules: 0
        };
    }

    // 主执行流程 (带内存监控)
    async run() {
        console.log('🚀 启动增强版AST剪枝器...\n');
        
        // 内存监控
        this.logMemoryUsage('启动时');
        
        // 阶段1: 解析并建立模块注册表
        console.log('阶段 1: 构建完整模块注册表...');
        this.parseAstIfNeeded();
        this.buildModuleRegistry();
        this.logMemoryUsage('模块注册表构建后');
        
        // 阶段2: 构建模块依赖关系图
        console.log('\n阶段 2: 分析模块依赖关系...');
        this.buildDependencyGraph();
        this.logMemoryUsage('依赖关系图构建后');
        
        // 阶段3: 智能npm包识别
        console.log('\n阶段 3: 增强npm包识别...');
        await this.enhancedLibraryDetection();
        this.logMemoryUsage('库识别后');
        
        // 阶段4: 递归依赖链追踪
        console.log('\n阶段 4: 递归追踪库依赖链...');
        this.recursiveLibraryDependencyTracking();
        this.logMemoryUsage('依赖追踪后');
        
        // 内存清理
        this.performMemoryCleanup();
        
        // 阶段5: 智能保留决策
        console.log('\n阶段 5: 智能保留决策...');
        this.intelligentPreservationDecision();
        this.logMemoryUsage('保留决策后');
        
        // 阶段6: 执行剪枝
        console.log('\n阶段 6: 执行完整剪枝...');
        this.executeCompletePruning();
        this.logMemoryUsage('剪枝后');
        
        // 阶段7: 生成净化代码
        console.log('\n阶段 7: 生成净化代码...');
        const cleanedCode = this.generateCleanedCode();
        this.logMemoryUsage('代码生成后');
        
        // 生成报告
        const report = this.generateDetailedReport();
        
        return { cleanedCode, report };
    }

    // 内存使用监控
    logMemoryUsage(stage) {
        const usage = process.memoryUsage();
        const heapUsed = (usage.heapUsed / 1024 / 1024).toFixed(2);
        const heapTotal = (usage.heapTotal / 1024 / 1024).toFixed(2);
        const external = (usage.external / 1024 / 1024).toFixed(2);
        
        console.log(`  💾 [${stage}] 内存使用: ${heapUsed}MB / ${heapTotal}MB (外部: ${external}MB)`);
        
        // 内存警告
        if (usage.heapUsed > 2 * 1024 * 1024 * 1024) { // > 2GB
            console.warn(`  ⚠️  内存使用过高，建议增加 --max-old-space-size 参数`);
        }
    }

    // 执行内存清理
    performMemoryCleanup() {
        console.log('  🧹 执行内存清理...');
        
        // 清理不必要的引用
        for (const [name, moduleInfo] of this.moduleRegistry.entries()) {
            if (moduleInfo.shouldRemove) {
                // 清理AST引用以释放内存
                if (moduleInfo.astPath) {
                    moduleInfo.astPath = null;
                }
                if (moduleInfo.astNode) {
                    moduleInfo.astNode = null;
                }
                if (moduleInfo.moduleFunction) {
                    moduleInfo.moduleFunction = null;
                }
                // 保留必要信息
                moduleInfo.sourceCode = null; // 清理源码缓存
            }
        }
        
        // 强制垃圾回收
        if (global.gc) {
            const beforeGC = process.memoryUsage().heapUsed;
            global.gc();
            const afterGC = process.memoryUsage().heapUsed;
            const freed = (beforeGC - afterGC) / 1024 / 1024;
            console.log(`  🗑️  垃圾回收释放: ${freed.toFixed(2)}MB`);
        }
    }

    // AST解析
    parseAstIfNeeded() {
        if (this.ast) return;
        
        console.log('正在解析AST...');
        const parserOptions = {
            sourceType: 'module',
            plugins: ['importMeta'],
            ranges: true,       // 需要ranges来获取精确位置
            tokens: false,
            attachComments: false
        };

        try {
            this.ast = parser.parse(this.sourceCode, parserOptions);
            console.log('✅ AST解析完成');
        } catch (e) {
            console.error("❌ AST解析失败", e);
            throw e;
        }
    }

    // 阶段1: 构建完整模块注册表
    buildModuleRegistry() {
        let moduleCount = 0;
        const modulePattern = /^[a-zA-Z_$][a-zA-Z0-9_$]*$/; // 有效的变量名模式

        traverse(this.ast, {
            VariableDeclarator: (path) => {
                const { node } = path;
                
                // 识别模块定义模式: var xxx = z((param1, param2) => { ... })
                if (t.isIdentifier(node.id) &&
                    node.init &&
                    t.isCallExpression(node.init) &&
                    t.isIdentifier(node.init.callee) &&
                    ['z', '__webpack_require__', 'require'].includes(node.init.callee.name)) {
                    
                    const moduleName = node.id.name;
                    const moduleFunction = node.init.arguments[0];
                    
                    if (modulePattern.test(moduleName) && moduleFunction) {
                        const moduleInfo = {
                            name: moduleName,
                            astPath: path,
                            astNode: node,
                            moduleFunction: moduleFunction,
                            sourceRange: this.getSourceRange(node),
                            sourceCode: this.getSourceSlice(node),
                            size: this.calculateModuleSize(node),
                            dependencies: new Set(),
                            isDependency: false,
                            isLibrary: false,
                            libraryInfo: null,
                            shouldRemove: false
                        };
                        
                        this.moduleRegistry.set(moduleName, moduleInfo);
                        moduleCount++;
                    }
                }
            }
        });

        this.stats.totalModules = moduleCount;
        console.log(`  → 发现 ${moduleCount} 个模块定义`);
    }

    // 阶段2: 构建模块依赖关系图
    buildDependencyGraph() {
        console.log('  分析模块间调用关系...');
        
        for (const [moduleName, moduleInfo] of this.moduleRegistry.entries()) {
            const dependencies = this.extractModuleDependencies(moduleInfo);
            moduleInfo.dependencies = dependencies;
            this.dependencyGraph.set(moduleName, dependencies);
        }

        // 统计依赖信息
        let totalDependencies = 0;
        for (const deps of this.dependencyGraph.values()) {
            totalDependencies += deps.size;
        }
        console.log(`  → 分析了 ${this.dependencyGraph.size} 个模块，发现 ${totalDependencies} 个依赖关系`);
    }

    // 提取单个模块的依赖关系
    extractModuleDependencies(moduleInfo) {
        const dependencies = new Set();
        const allModuleNames = new Set(this.moduleRegistry.keys());
        
        if (!moduleInfo.moduleFunction) return dependencies;

        try {
            // 🔧 修复: 使用astPath来获取正确的scope和parentPath
            const modulePath = moduleInfo.astPath;
            const functionExpression = modulePath.get('init.arguments.0');
            
            if (functionExpression && functionExpression.isFunction()) {
                // 在模块函数内查找对其他模块的引用
                functionExpression.traverse({
                    // 直接调用: moduleX()
                    CallExpression(path) {
                        if (t.isIdentifier(path.node.callee)) {
                            const calledName = path.node.callee.name;
                            if (allModuleNames.has(calledName)) {
                                dependencies.add(calledName);
                            }
                        }
                    },
                    
                    // 成员访问: moduleX.method()
                    MemberExpression(path) {
                        let object = path.node.object;
                        while (t.isMemberExpression(object)) {
                            object = object.object;
                        }
                        if (t.isIdentifier(object) && allModuleNames.has(object.name)) {
                            dependencies.add(object.name);
                        }
                    },
                    
                    // 变量引用: var x = moduleY
                    Identifier(path) {
                        if (path.isReferencedIdentifier()) {
                            const refName = path.node.name;
                            if (allModuleNames.has(refName)) {
                                dependencies.add(refName);
                            }
                        }
                    }
                });
            } else {
                // 🔧 备用方案: 如果无法获取path，使用字符串匹配
                console.warn(`  ⚠️  无法遍历模块 ${moduleInfo.name}，使用备用检测方法`);
                dependencies = this.extractDependenciesViaStringAnalysis(moduleInfo, allModuleNames);
            }
        } catch (error) {
            console.warn(`  ⚠️  分析模块 ${moduleInfo.name} 依赖时出错: ${error.message}`);
            // 🔧 备用方案: 使用字符串分析
            return this.extractDependenciesViaStringAnalysis(moduleInfo, allModuleNames);
        }

        return dependencies;
    }

    // 备用依赖提取方法 - 基于字符串分析
    extractDependenciesViaStringAnalysis(moduleInfo, allModuleNames) {
        const dependencies = new Set();
        const sourceCode = moduleInfo.sourceCode;
        
        // 使用正则表达式匹配模块引用
        for (const moduleName of allModuleNames) {
            if (moduleName === moduleInfo.name) continue; // 跳过自己
            
            // 匹配各种调用模式
            const patterns = [
                new RegExp(`\\b${moduleName}\\s*\\(`, 'g'),        // 函数调用
                new RegExp(`\\b${moduleName}\\.\\w+`, 'g'),        // 成员访问
                new RegExp(`\\b${moduleName}\\b(?!\\s*[=:])`, 'g'), // 变量引用
            ];
            
            for (const pattern of patterns) {
                if (pattern.test(sourceCode)) {
                    dependencies.add(moduleName);
                    break; // 找到一种模式就足够了
                }
            }
        }
        
        return dependencies;
    }

    // 阶段3: 增强npm包识别
    async enhancedLibraryDetection() {
        console.log('  开始多维度库识别...');
        
        for (const [moduleName, moduleInfo] of this.moduleRegistry.entries()) {
            const detectionResults = {
                fingerprintScore: 0,
                patternScore: 0,
                structureScore: 0,
                totalScore: 0,
                identifiedAs: null
            };

            // 1. 指纹匹配 (继承原有逻辑)
            detectionResults.fingerprintScore = this.fingerprintMatching(moduleInfo);
            
            // 2. 代码模式识别
            detectionResults.patternScore = this.detectLibraryPatterns(moduleInfo);
            
            // 3. 结构特征分析
            detectionResults.structureScore = this.analyzeStructureFeatures(moduleInfo);
            
            // 计算总分
            detectionResults.totalScore = 
                detectionResults.fingerprintScore * 0.5 +   // 指纹权重50%
                detectionResults.patternScore * 0.3 +       // 模式权重30%
                detectionResults.structureScore * 0.2;      // 结构权重20%

            // 判断是否为库
            if (detectionResults.totalScore > 25) {  // 降低阈值，提高检出率
                moduleInfo.isLibrary = true;
                moduleInfo.libraryInfo = detectionResults;
                this.libraryComponents.set(moduleName, detectionResults);
                
                console.log(`  📚 识别库模块: ${moduleName} → ${detectionResults.identifiedAs || 'Unknown'} (分数: ${detectionResults.totalScore.toFixed(1)})`);
                this.stats.identifiedLibraries++;
            }
        }
    }

    // 指纹匹配 (改进版)
    fingerprintMatching(moduleInfo) {
        let bestMatch = { name: null, score: 0 };
        const sourceSlice = moduleInfo.sourceCode;
        
        for (const [npmName, fingerprint] of Object.entries(this.NPM_FINGERPRINTS)) {
            let currentScore = 0;
            
            // 字符串匹配
            for (const fpString of fingerprint.strings || []) {
                if (fpString instanceof RegExp) {
                    if (sourceSlice.match(fpString)) currentScore += 15;
                } else if (sourceSlice.includes(fpString)) {
                    currentScore += 10;
                }
            }
            
            // 导出匹配
            for (const fpExport of fingerprint.exports || []) {
                if (sourceSlice.includes(fpExport)) currentScore += 5;
            }
            
            if (currentScore > bestMatch.score) {
                bestMatch = { name: npmName, score: currentScore };
            }
        }
        
        if (bestMatch.score > 0) {
            moduleInfo.libraryInfo = { identifiedAs: bestMatch.name };
        }
        
        return bestMatch.score;
    }

    // 代码模式识别
    detectLibraryPatterns(moduleInfo) {
        const sourceCode = moduleInfo.sourceCode;
        let score = 0;

        // 常见库模式
        const libraryPatterns = [
            /exports\.default\s*=/, // ES6 default export
            /module\.exports\s*=/, // CommonJS export
            /Object\.defineProperty\(exports/, // Property definition
            /\/\*[\s\S]*?@license[\s\S]*?\*\//, // License comments
            /\/\*[\s\S]*?Copyright[\s\S]*?\*\//, // Copyright
            /function\s+[A-Z][a-zA-Z]*\s*\(/, // Constructor functions
            /\.prototype\.[a-zA-Z]+\s*=/, // Prototype assignments
            /typeof\s+exports/, // Export type checks
            /typeof\s+module/, // Module type checks
            /function\s*\([^)]*exports[^)]*\)/, // Export parameters
        ];

        for (const pattern of libraryPatterns) {
            if (pattern.test(sourceCode)) {
                score += 5;
            }
        }

        // 检查代码复杂度
        const complexity = this.calculateComplexity(moduleInfo.moduleFunction);
        if (complexity > 100) score += 10; // 高复杂度倾向于库代码

        return score;
    }

    // 结构特征分析
    analyzeStructureFeatures(moduleInfo) {
        let score = 0;
        
        // 分析模块大小
        const size = moduleInfo.size;
        if (size > 1000) score += 15;  // 大型模块倾向于库
        else if (size > 500) score += 10;
        else if (size > 100) score += 5;
        
        // 分析依赖数量
        const depCount = moduleInfo.dependencies.size;
        if (depCount === 0) score += 10; // 无依赖倾向于库
        else if (depCount < 3) score += 5;
        
        return score;
    }

    // 阶段4: 递归依赖链追踪 (带循环检测)
    recursiveLibraryDependencyTracking() {
        console.log('  追踪库模块的完整依赖链...');
        
        const globalVisited = new Set();
        const processing = new Set(); // 正在处理的模块，用于检测循环
        const libraryModules = new Set();
        const maxDepth = 50; // 最大递归深度限制
        
        // 收集所有已识别的库模块
        for (const [moduleName, moduleInfo] of this.moduleRegistry.entries()) {
            if (moduleInfo.isLibrary) {
                libraryModules.add(moduleName);
            }
        }
        
        console.log(`  → 发现 ${libraryModules.size} 个库模块作为起点`);
        
        // 递归追踪每个库的依赖
        let processedCount = 0;
        for (const libraryModule of libraryModules) {
            if (!globalVisited.has(libraryModule)) {
                console.log(`  → [${++processedCount}/${libraryModules.size}] 追踪库: ${libraryModule}`);
                const result = this.markLibraryDependenciesRecursive(
                    libraryModule, 
                    globalVisited, 
                    processing, 
                    new Set(),
                    0, 
                    maxDepth
                );
                
                if (result.hasCircular) {
                    console.log(`  ⚠️  检测到循环依赖链涉及: ${Array.from(result.circularChain).join(' → ')}`);
                }
            }
        }
        
        console.log(`  → 标记了 ${this.removalQueue.size} 个模块用于移除`);
        console.log(`  → 检测到 ${globalVisited.size} 个相关模块`);
    }

    // 递归标记库的依赖 (增强版循环检测)
    markLibraryDependenciesRecursive(moduleName, globalVisited, processing, currentPath, depth, maxDepth) {
        const result = { hasCircular: false, circularChain: new Set() };
        
        // 🔧 深度限制检查
        if (depth > maxDepth) {
            console.warn(`  ⚠️  达到最大递归深度 ${maxDepth}，停止分析: ${moduleName}`);
            return result;
        }
        
        // 🔧 循环依赖检测
        if (processing.has(moduleName)) {
            console.log(`  🔄 检测到循环依赖: ${moduleName} (当前路径: ${Array.from(currentPath).join(' → ')} → ${moduleName})`);
            result.hasCircular = true;
            result.circularChain = new Set([...currentPath, moduleName]);
            return result;
        }
        
        // 🔧 已处理检查
        if (globalVisited.has(moduleName)) {
            return result;
        }
        
        const moduleInfo = this.moduleRegistry.get(moduleName);
        if (!moduleInfo) {
            console.warn(`  ⚠️  模块不存在: ${moduleName}`);
            return result;
        }
        
        // 标记为正在处理
        processing.add(moduleName);
        currentPath.add(moduleName);
        globalVisited.add(moduleName);
        
        // 标记为待移除
        moduleInfo.shouldRemove = true;
        this.removalQueue.add(moduleName);
        
        // 🔧 依赖数量限制检查
        const dependencies = Array.from(moduleInfo.dependencies);
        if (dependencies.length > 100) {
            console.warn(`  ⚠️  模块 ${moduleName} 依赖过多 (${dependencies.length})，仅处理前100个`);
            dependencies.splice(100);
        }
        
        // 递归处理依赖
        for (const depName of dependencies) {
            const depInfo = this.moduleRegistry.get(depName);
            if (depInfo && !this.shouldPreserveModule(depInfo)) {
                const depResult = this.markLibraryDependenciesRecursive(
                    depName, 
                    globalVisited, 
                    processing, 
                    new Set(currentPath),  // 传递路径副本
                    depth + 1, 
                    maxDepth
                );
                
                if (depResult.hasCircular) {
                    result.hasCircular = true;
                    depResult.circularChain.forEach(node => result.circularChain.add(node));
                }
            }
        }
        
        // 清理状态
        processing.delete(moduleName);
        currentPath.delete(moduleName);
        
        return result;
    }

    // 阶段5: 智能保留决策
    intelligentPreservationDecision() {
        console.log('  分析业务逻辑模块...');
        
        for (const [moduleName, moduleInfo] of this.moduleRegistry.entries()) {
            if (!moduleInfo.shouldRemove && !moduleInfo.isLibrary) {
                if (this.isBusinessLogicModule(moduleInfo)) {
                    this.preservedModules.add(moduleName);
                    moduleInfo.shouldRemove = false;
                    
                    // 保护其关键依赖
                    this.protectCriticalDependencies(moduleName, new Set());
                }
            }
        }
        
        this.stats.preservedModules = this.preservedModules.size;
        this.stats.removedModules = this.removalQueue.size;
        
        console.log(`  → 保留 ${this.stats.preservedModules} 个业务模块`);
        console.log(`  → 移除 ${this.stats.removedModules} 个库/依赖模块`);
    }

    // 判断是否为业务逻辑模块
    isBusinessLogicModule(moduleInfo) {
        const sourceCode = moduleInfo.sourceCode;
        
        // 业务逻辑指标
        const businessIndicators = [
            /process\.argv/, // 命令行处理
            /console\.log/, // 日志输出
            /fs\.readFile/, // 文件操作
            /require\s*\(\s*['"]\./, // 相对路径引用
            /module\.exports.*function/, // 自定义导出函数
        ];
        
        let businessScore = 0;
        for (const indicator of businessIndicators) {
            if (indicator.test(sourceCode)) {
                businessScore += 10;
            }
        }
        
        // 非库特征
        const nonLibraryFeatures = [
            moduleInfo.size < 500,  // 相对较小
            moduleInfo.dependencies.size > 2,  // 有多个依赖
            !sourceCode.includes('Copyright'),  // 无版权信息
            !sourceCode.includes('license'),   // 无许可证
        ];
        
        const nonLibScore = nonLibraryFeatures.filter(Boolean).length * 5;
        
        return (businessScore + nonLibScore) > 15;
    }

    // 保护关键依赖 (带循环检测)
    protectCriticalDependencies(moduleName, visited, depth = 0) {
        const maxDepth = 20; // 限制递归深度
        
        // 🔧 循环依赖检测
        if (visited.has(moduleName)) {
            console.log(`  🔄 保护依赖时检测到循环: ${moduleName}`);
            return;
        }
        
        // 🔧 深度限制
        if (depth > maxDepth) {
            console.warn(`  ⚠️  保护依赖达到最大深度 ${maxDepth}，停止: ${moduleName}`);
            return;
        }
        
        visited.add(moduleName);
        
        const moduleInfo = this.moduleRegistry.get(moduleName);
        if (!moduleInfo) {
            visited.delete(moduleName);
            return;
        }
        
        // 限制依赖数量防止爆炸
        const dependencies = Array.from(moduleInfo.dependencies).slice(0, 50);
        
        for (const depName of dependencies) {
            const depInfo = this.moduleRegistry.get(depName);
            if (depInfo && depInfo.shouldRemove && !depInfo.isLibrary) {
                // 这是一个被错误标记的依赖，保护它
                console.log(`  🛡️  保护依赖: ${depName} (被 ${moduleName} 需要)`);
                depInfo.shouldRemove = false;
                this.removalQueue.delete(depName);
                this.preservedModules.add(depName);
                
                // 递归保护（传递visited副本）
                this.protectCriticalDependencies(depName, new Set(visited), depth + 1);
            }
        }
        
        visited.delete(moduleName);
    }

    // 阶段6: 执行完整剪枝
    executeCompletePruning() {
        console.log('  执行AST剪枝...');
        
        const self = this;
        let removedCount = 0;
        
        // 过滤AST body
        if (this.ast.body && Array.isArray(this.ast.body)) {
            this.ast.body = this.ast.body.filter(node => {
                if (t.isVariableDeclaration(node)) {
                    // 过滤变量声明中的模块
                    node.declarations = node.declarations.filter(decl => {
                        if (t.isIdentifier(decl.id)) {
                            const moduleName = decl.id.name;
                            const moduleInfo = self.moduleRegistry.get(moduleName);
                            
                            if (moduleInfo && moduleInfo.shouldRemove) {
                                console.log(`  🗑️  移除: ${moduleName} ${moduleInfo.isLibrary ? '(库)' : '(依赖)'}`);
                                removedCount++;
                                return false;
                            }
                        }
                        return true;
                    });
                    
                    // 如果所有声明都被移除，移除整个变量声明
                    return node.declarations.length > 0;
                }
                return true;
            });
        }
        
        console.log(`  ✅ 成功移除 ${removedCount} 个模块`);
    }

    // 生成净化代码
    generateCleanedCode() {
        const { code } = generate(this.ast, {
            comments: true,
            retainLines: false,
            concise: true
        });
        return code;
    }

    // 辅助方法
    shouldPreserveModule(moduleInfo) {
        return this.preservedModules.has(moduleInfo.name) || 
               this.isBusinessLogicModule(moduleInfo);
    }

    getSourceRange(node) {
        if (node.start !== undefined && node.end !== undefined) {
            return { start: node.start, end: node.end };
        }
        return null;
    }

    getSourceSlice(node) {
        const range = this.getSourceRange(node);
        if (range) {
            return this.sourceCode.substring(range.start, range.end);
        }
        return '';
    }

    calculateModuleSize(node) {
        const sourceSlice = this.getSourceSlice(node);
        return sourceSlice.split('\n').length;
    }

    calculateComplexity(functionNode) {
        let complexity = 0;
        if (!functionNode) return 0;
        
        try {
            // 🔧 修复: 使用简单的节点计数而不是traverse
            const countComplexNodes = (node) => {
                if (!node || typeof node !== 'object') return;
                
                // 增加复杂度的节点类型
                if (node.type === 'IfStatement' || 
                    node.type === 'WhileStatement' || 
                    node.type === 'ForStatement' || 
                    node.type === 'SwitchCase' || 
                    node.type === 'CatchClause' || 
                    node.type === 'FunctionExpression' || 
                    node.type === 'ArrowFunctionExpression') {
                    complexity++;
                }
                
                // 递归处理子节点
                for (const key in node) {
                    if (key === 'parent' || key === 'loc' || key === 'range') continue;
                    const child = node[key];
                    if (Array.isArray(child)) {
                        child.forEach(countComplexNodes);
                    } else if (child && typeof child === 'object' && child.type) {
                        countComplexNodes(child);
                    }
                }
            };
            
            countComplexNodes(functionNode);
        } catch (error) {
            console.warn(`计算复杂度时出错: ${error.message}`);
            // 备用方案：基于源码行数估算
            const sourceCode = generate(functionNode).code;
            complexity = Math.floor(sourceCode.split('\n').length / 10);
        }
        
        return complexity;
    }

    // 生成详细报告
    generateDetailedReport() {
        const libraryDetails = {};
        for (const [moduleName, detectionResult] of this.libraryComponents.entries()) {
            const moduleInfo = this.moduleRegistry.get(moduleName);
            libraryDetails[moduleName] = {
                identifiedAs: detectionResult.identifiedAs,
                confidence: detectionResult.totalScore,
                size: moduleInfo.size,
                dependencies: Array.from(moduleInfo.dependencies)
            };
        }

        return {
            summary: {
                ...this.stats,
                compressionRatio: ((this.stats.removedModules / this.stats.totalModules) * 100).toFixed(1) + '%'
            },
            detailedAnalysis: {
                libraryModules: libraryDetails,
                preservedModules: Array.from(this.preservedModules),
                removalQueue: Array.from(this.removalQueue)
            },
            dependencyGraph: Object.fromEntries(
                Array.from(this.dependencyGraph.entries()).map(([name, deps]) => [
                    name, Array.from(deps)
                ])
            )
        };
    }
}

// 主执行逻辑 (增强版)
function main() {
    console.log("🚀 增强版AST剪枝器启动...\n");

    const args = process.argv.slice(2);
    if (args.length !== 1) {
        console.error("用法: node enhanced-ast-pruner.js <源文件路径.js>");
        console.error("提示: 使用 --max-old-space-size=8192 --expose-gc 增加内存和启用垃圾回收");
        process.exit(1);
    }

    const sourceFilePath = path.resolve(args[0]);
    if (!fs.existsSync(sourceFilePath)) {
        console.error(`错误: 文件未找到于 ${sourceFilePath}`);
        process.exit(1);
    }

    // 确保输出目录存在
    const outputDir = path.resolve(__dirname, '../output');
    if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
        console.log(`✅ 创建输出目录: ${outputDir}`);
    }

    console.log(`正在读取源文件: ${sourceFilePath}`);
    
    try {
        const stats = fs.statSync(sourceFilePath);
        const fileSizeMB = stats.size / (1024 * 1024);
        console.log(`文件大小: ${fileSizeMB.toFixed(2)} MB`);

        // 内存建议
        if (fileSizeMB > 10) {
            console.warn(`⚠️  大文件检测 (${fileSizeMB.toFixed(2)} MB)，建议使用：`);
            console.warn(`   node --max-old-space-size=8192 --expose-gc scripts/enhanced-ast-pruner.js ${args[0]}`);
        }

        const sourceCode = fs.readFileSync(sourceFilePath, 'utf8');
        const fingerprintDatabase = loadFingerprintDatabase();
        const stripper = new EnhancedNpmPackageStripper(sourceCode, fingerprintDatabase);
        
        // 设置进程异常处理
        process.on('uncaughtException', (error) => {
            console.error('\n❌ 发生未捕获异常:', error.message);
            if (error.message.includes('heap out of memory')) {
                console.error('💡 内存不足，请使用: --max-old-space-size=8192');
            }
            process.exit(1);
        });
        
        stripper.run().then(result => {
            if (result) {
                const { cleanedCode, report } = result;
                const baseName = path.basename(sourceFilePath, '.js');

                const cleanedFilePath = path.join(outputDir, `${baseName}.enhanced.pruned.js`);
                const reportFilePath = path.join(outputDir, `${baseName}.enhanced.report.json`);

                fs.writeFileSync(cleanedFilePath, cleanedCode, 'utf8');
                fs.writeFileSync(reportFilePath, JSON.stringify(report, null, 2), 'utf8');

                console.log('\n✅ 处理完成!');
                console.log('='.repeat(60));
                console.log(`📄 净化后代码: ${cleanedFilePath}`);
                console.log(`📊 详细报告: ${reportFilePath}`);
                console.log(`🎯 压缩率: ${report.summary.compressionRatio}`);
                console.log(`📚 移除库数: ${report.summary.identifiedLibraries}`);
                console.log(`🔧 保留模块: ${report.summary.preservedModules}`);
                
                // 循环依赖统计
                if (report.circularDependencies && report.circularDependencies.length > 0) {
                    console.log(`🔄 检测到 ${report.circularDependencies.length} 个循环依赖`);
                }
            }
        }).catch(error => {
            console.error("\n❌ 处理过程中发生错误:", error.stack);
            
            if (error.message.includes('heap out of memory')) {
                console.error("\n🔧 内存不足解决方案:");
                console.error("1. 使用 --max-old-space-size=8192 增加内存限制");
                console.error("2. 使用 --expose-gc 启用手动垃圾回收");
                console.error("3. 考虑将大文件拆分为更小的模块");
            }
            
            process.exit(1);
        });
    } catch(error) {
        console.error("\n❌ 文件读取错误:", error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = EnhancedNpmPackageStripper;

    --------
    你可以先根据我截取出来的部分代码先大致了解一下源码中的一些模式，结合我的思路和其他信息，做个详细的分析，看方案的可行性，如果可行，给出一个详细的方案