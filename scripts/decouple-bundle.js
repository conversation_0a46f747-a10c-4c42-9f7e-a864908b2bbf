const fs = require('fs');
const path = require('path');
const parser = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const generator = require('@babel/generator').default;
const t = require('@babel/types'); // Babel's AST node builder

const INPUT_FILE = '../cli.js';
const OUTPUT_DIR = 'output_final';
const MODULE_FACTORY_NAME = 'E';

/**
 * 删除目录及其所有内容
 * @param {string} dir - 要删除的目录路径
 */
function removeDirectory(dir) {
    if (fs.existsSync(dir)) {
        const files = fs.readdirSync(dir);
        files.forEach(file => {
            const filePath = path.join(dir, file);
            if (fs.lstatSync(filePath).isDirectory()) {
                removeDirectory(filePath);
            } else {
                fs.unlinkSync(filePath);
            }
        });
        fs.rmdirSync(dir);
        console.log(`🗑️ Removed existing directory: ${dir}`);
    }
}

/**
 * 生成唯一的文件名，避免大小写不敏感的文件系统冲突
 * @param {string} outputDir - 输出目录
 * @param {string} fileName - 原始文件名
 * @returns {string} 唯一的文件名
 */
function getUniqueFileName(outputDir, fileName) {
    const fullPath = path.join(outputDir, fileName);

    // 检查文件是否已存在（大小写不敏感）
    if (!fs.existsSync(fullPath)) {
        return fileName;
    }

    // 如果存在，则在文件名后添加 _d
    const ext = path.extname(fileName);
    const baseName = path.basename(fileName, ext);
    const newFileName = `${baseName}_d${ext}`;

    // 递归检查，以防 _d 版本也存在
    return getUniqueFileName(outputDir, newFileName);
}

/**
 * @typedef {object} ModuleInfo
 * @property {string} name
 * @property {Set<string>} dependencies
 * @property {Set<string>} dependents
 * @property {[number, number]} range
 * @property {object} path - The Babel path object for the module definition's declarator.
 * @property {object} statementPath - The Babel path object for the variable declaration statement.
 */

try {
    // --- 0. 清理输出目录 ---
    removeDirectory(OUTPUT_DIR);
    
    // --- 1. 读取和解析源代码 ---
    if (!fs.existsSync(INPUT_FILE)) {
        throw new Error(`Input file not found: ${INPUT_FILE}`);
    }
    const sourceCode = fs.readFileSync(INPUT_FILE, 'utf-8');
    const ast = parser.parse(sourceCode, { sourceType: 'script', errorRecovery: true });

    /** @type {Map<string, ModuleInfo>} */
    const moduleGraph = new Map();

    // --- 2. Pass 1: 识别所有模块定义 ---
    traverse(ast, {
        VariableDeclarator(path) {
            const { node } = path;
            if (
                node.init &&
                node.init.type === 'CallExpression' &&
                node.init.callee.type === 'Identifier' &&
                node.init.callee.name === MODULE_FACTORY_NAME &&
                node.id.type === 'Identifier'
            ) {
                const moduleName = node.id.name;
                const statementPath = path.findParent((p) => p.isVariableDeclaration());
                if (statementPath && !moduleGraph.has(moduleName)) {
                    // 获取代码范围
                    let range = null;
                    if (statementPath.node.start !== undefined && statementPath.node.end !== undefined) {
                        range = [statementPath.node.start, statementPath.node.end];
                    } else if (statementPath.node.range) {
                        range = statementPath.node.range;
                    }
                    
                    console.log(`🔍 Found module ${moduleName}, range: ${range ? `[${range[0]}, ${range[1]}]` : 'undefined'}`);
                    
                    moduleGraph.set(moduleName, {
                        name: moduleName,
                        dependencies: new Set(),
                        dependents: new Set(),
                        path: path,
                        range: range,
                        statementPath: statementPath, // 保存语句路径以便后续使用
                    });
                }
            }
        },
    });

    console.log(`🔍 Found ${moduleGraph.size} modules`);

    // --- 3. Pass 2: 构建依赖图 ---
    if (!moduleGraph || moduleGraph.size === 0) {
        console.warn('⚠️ No modules found in moduleGraph');
    } else {
        for (const [moduleName, moduleInfo] of moduleGraph.entries()) {
            if (!moduleInfo) {
                console.warn(`⚠️ moduleInfo is undefined for ${moduleName}`);
                continue;
            }
            
            if (!moduleInfo.path) {
                console.warn(`⚠️ moduleInfo.path is undefined for ${moduleName}`);
                continue;
            }

            const moduleBodyPath = moduleInfo.path.get('init.arguments.0');
            if (!moduleBodyPath) {
                console.warn(`⚠️ moduleBodyPath is undefined for ${moduleName}`);
                continue;
            }

            try {
                moduleBodyPath.traverse({
                    CallExpression(path) {
                        if (path.node.callee.type === 'Identifier' && moduleGraph.has(path.node.callee.name)) {
                            const dependencyName = path.node.callee.name;
                            if (moduleInfo.dependencies) {
                                moduleInfo.dependencies.add(dependencyName);
                            }
                            const dependencyInfo = moduleGraph.get(dependencyName);
                            if (dependencyInfo && dependencyInfo.dependents) {
                                dependencyInfo.dependents.add(moduleName);
                            }
                        }
                    },
                });
            } catch (error) {
                console.error(`❌ Error traversing module ${moduleName}:`, error.message);
            }
        }
    }

    // --- 4. 识别包，提取代码，并添加所有变量的导出 ---
    const allModules = Array.from(moduleGraph.values()).filter(info => info && info.dependents);
    const entryPoints = allModules
        .filter(info => info.dependents.size === 0)
        .map(info => info.name);
        
    console.log(`📦 Found ${entryPoints.length} entry points:`, entryPoints);

    const visited = new Set();
    const modulesToReplace = new Map(); // Map<entryPoint, allMembers>

    if (!fs.existsSync(OUTPUT_DIR)) {
        fs.mkdirSync(OUTPUT_DIR, { recursive: true });
    }

    if (entryPoints && entryPoints.length > 0) {
        entryPoints.forEach(entryName => {
            if (!entryName) {
                console.warn('⚠️ Entry name is undefined, skipping');
                return;
            }

            const packageMembers = new Set();
            (function findPackageMembers(name) {
                if (visited.has(name) || !moduleGraph.has(name)) return;
                visited.add(name);
                packageMembers.add(name);
                const moduleInfo = moduleGraph.get(name);
                if (moduleInfo && moduleInfo.dependencies) {
                    try {
                        moduleInfo.dependencies.forEach(dep => findPackageMembers(dep));
                    } catch (error) {
                        console.error(`❌ Error processing dependencies for ${name}:`, error.message);
                    }
                }
            })(entryName);

            if (packageMembers.size > 0) {
                modulesToReplace.set(entryName, packageMembers);
                const sortedMembers = Array.from(packageMembers).sort();
                
                console.log(`📋 Package '${entryName}' contains ${sortedMembers.length} modules:`, sortedMembers);
                
                let packageCode = `// Package extracted with entry point: ${entryName}\n`;
                packageCode += `// Contains ${sortedMembers.length} variables: ${sortedMembers.length <= 10 ? sortedMembers.join(', ') : sortedMembers.slice(0, 10).join(', ') + '... (and ' + (sortedMembers.length - 10) + ' more)'}\n\n`;
                
                if (sortedMembers && sortedMembers.length > 0) {
                    sortedMembers.forEach(memberName => {
                        if (!memberName) {
                            console.warn('⚠️ Member name is undefined, skipping');
                            return;
                        }
                        const moduleInfo = moduleGraph.get(memberName);
                        if (moduleInfo) {
                            let codeToAdd = '';
                            
                            // 尝试多种方式获取代码
                            if (moduleInfo.range && Array.isArray(moduleInfo.range) && moduleInfo.range.length >= 2) {
                                const [start, end] = moduleInfo.range;
                                if (typeof start === 'number' && typeof end === 'number') {
                                    codeToAdd = sourceCode.substring(start, end);
                                    console.log(`📄 Extracted code for ${memberName} using range [${start}, ${end}]`);
                                }
                            }
                            
                            // 如果range方式失败，尝试从AST生成代码
                            if (!codeToAdd && moduleInfo.statementPath) {
                                try {
                                    const { code } = generator(moduleInfo.statementPath.node, {
                                        retainLines: false,
                                        compact: false,
                                    });
                                    codeToAdd = code;
                                    console.log(`📄 Generated code for ${memberName} from AST`);
                                } catch (error) {
                                    console.error(`❌ Error generating code for ${memberName}:`, error.message);
                                }
                            }
                            
                            if (codeToAdd) {
                                packageCode += codeToAdd;
                                if (!codeToAdd.endsWith(';') && !codeToAdd.endsWith('\n')) {
                                    packageCode += ';\n';
                                } else if (!codeToAdd.endsWith('\n')) {
                                    packageCode += '\n';
                                }
                            } else {
                                console.warn(`⚠️ Could not extract code for module ${memberName}`);
                            }
                        } else {
                            console.warn(`⚠️ ModuleInfo not found for ${memberName}`);
                        }
                    });
                }

                // ⭐ 关键步骤：导出所有变量
                packageCode += '\n// Export all variables\n';
                packageCode += 'module.exports = {\n';
                sortedMembers.forEach((memberName, index) => {
                    const comma = index < sortedMembers.length - 1 ? ',' : '';
                    packageCode += `  ${memberName}${comma}\n`;
                });
                packageCode += '};\n';

                // 生成文件名 - 优化长文件名问题
                let packageFileName;
                if (sortedMembers.length <= 5) {
                    // 5个或更少变量时，使用变量名连接
                    packageFileName = `${sortedMembers.join('_')}.js`;
                } else {
                    // 超过5个变量时，使用前5个变量名+more
                    const firstFive = sortedMembers.slice(0, 5);
                    packageFileName = `${firstFive.join('_')}_more.js`;
                }
                const finalPackageFileName = getUniqueFileName(OUTPUT_DIR, packageFileName);
                fs.writeFileSync(path.join(OUTPUT_DIR, finalPackageFileName), packageCode, 'utf-8');
                console.log(`✅ Extracted package '${entryName}' to '${path.join(OUTPUT_DIR, finalPackageFileName)}'`);
                console.log(`   📝 File contains ${sortedMembers.length} variables${sortedMembers.length > 10 ? ' (showing first 10)' : ''}: ${sortedMembers.length <= 10 ? sortedMembers.join(', ') : sortedMembers.slice(0, 10).join(', ') + '...'}`);;
                
                // 更新modulesToReplace以包含文件名信息
                modulesToReplace.set(entryName, {
                    members: packageMembers,
                    fileName: finalPackageFileName,
                    sortedMembers: sortedMembers
                });
            }
        });
    } else {
        console.warn('⚠️ No entry points found');
    }

    // --- 5. 修改主 AST：用解构赋值的 `require()` 替换整个包的根入口定义 ---
    // 首先收集所有需要删除的模块路径
    const pathsToRemove = new Set();
    
    traverse(ast, {
        VariableDeclarator(path) {
            const { node } = path;
            if (
                node.init &&
                node.init.type === 'CallExpression' &&
                node.init.callee.type === 'Identifier' &&
                node.init.callee.name === MODULE_FACTORY_NAME &&
                node.id.type === 'Identifier'
            ) {
                const moduleName = node.id.name;
                
                // 检查这个模块是不是我们确定的一个包的入口点
                if (modulesToReplace && modulesToReplace.has(moduleName)) {
                    const packageInfo = modulesToReplace.get(moduleName);
                    const packageFileName = `./${packageInfo.fileName}`;
                    const sortedMembers = packageInfo.sortedMembers;
                    
                    // ⭐ 关键步骤：构建解构赋值的 require 语句
                    // 例如: const { A, B, C } = require('./A_B_C.js');
                    
                    // 创建解构赋值的属性数组
                    const properties = sortedMembers.map(memberName => 
                        t.objectProperty(t.identifier(memberName), t.identifier(memberName))
                    );
                    properties.forEach(prop => {
                        prop.shorthand = true; // 使用简写形式 {A, B, C}
                    });
                    
                    const objectPattern = t.objectPattern(properties);
                    const requireCall = t.callExpression(t.identifier('require'), [t.stringLiteral(packageFileName)]);
                    
                    // 获取父级变量声明语句
                    const statementPath = path.findParent((p) => p.isVariableDeclaration());
                    if (statementPath) {
                        // 创建新的变量声明语句
                        const newVariableDeclaration = t.variableDeclaration('const', [
                            t.variableDeclarator(objectPattern, requireCall)
                        ]);
                        
                        // 替换整个变量声明语句
                        statementPath.replaceWith(newVariableDeclaration);
                        console.log(`🔄 Replaced ${moduleName} package with destructured require('${packageFileName}')`);
                        console.log(`   Variables: ${sortedMembers.join(', ')}`);
                    }

                    // 标记这个包里的其他成员需要删除
                    const membersToRemove = packageInfo.members;
                    if (membersToRemove && typeof membersToRemove.forEach === 'function') {
                        try {
                            membersToRemove.forEach(memberName => {
                                if (memberName && memberName !== moduleName) { // 不要删除入口自身，因为它已经被替换
                                    const memberInfo = moduleGraph.get(memberName);
                                    if (memberInfo && memberInfo.path) {
                                        const statementPath = memberInfo.path.findParent((p) => p.isVariableDeclaration());
                                        if (statementPath) {
                                            pathsToRemove.add(statementPath);
                                        }
                                    }
                                }
                            });
                        } catch (error) {
                            console.error(`❌ Error processing members to remove for ${moduleName}:`, error.message);
                        }
                    }
                } else if (modulesToReplace) {
                    // 检查这个模块是否属于某个包的成员（非入口点）
                    try {
                        for (const [entryName, packageInfo] of modulesToReplace.entries()) {
                            const members = packageInfo.members;
                            if (members && typeof members.has === 'function' && members.has(moduleName) && moduleName !== entryName) {
                                const statementPath = path.findParent((p) => p.isVariableDeclaration());
                                if (statementPath) {
                                    pathsToRemove.add(statementPath);
                                }
                                break;
                            }
                        }
                    } catch (error) {
                        console.error(`❌ Error checking module membership for ${moduleName}:`, error.message);
                    }
                }
            }
        },
    });

    // 删除标记的路径
    if (pathsToRemove && typeof pathsToRemove.forEach === 'function') {
        try {
            pathsToRemove.forEach(pathToRemove => {
                if (pathToRemove && !pathToRemove.removed) {
                    pathToRemove.remove();
                }
            });
        } catch (error) {
            console.error('❌ Error removing paths:', error.message);
        }
    }

    // --- 6. 生成新的主文件 ---
    const { code: newMainCode } = generator(ast, {
        retainLines: false, // 让代码更紧凑
        compact: false,
    });

    const originalMainFileName = 'main.js';
    const mainFileName = getUniqueFileName(OUTPUT_DIR, originalMainFileName);
    fs.writeFileSync(path.join(OUTPUT_DIR, mainFileName), newMainCode, 'utf-8');
    console.log(`✅ Created new decoupled main file at '${path.join(OUTPUT_DIR, mainFileName)}'`);

    // --- 生成依赖图 JSON 文件 ---
    const dependencyGraph = {
        metadata: {
            totalModules: moduleGraph ? moduleGraph.size : 0,
            entryPoints: entryPoints ? entryPoints.length : 0,
            packagesCreated: modulesToReplace ? modulesToReplace.size : 0,
            generatedAt: new Date().toISOString(),
        },
        modules: {},
        packages: {},
        entryPoints: entryPoints || [],
    };

    // 添加所有模块信息
    if (moduleGraph) {
        for (const [moduleName, moduleInfo] of moduleGraph.entries()) {
            dependencyGraph.modules[moduleName] = {
                name: moduleName,
                dependencies: Array.from(moduleInfo.dependencies || []),
                dependents: Array.from(moduleInfo.dependents || []),
                isEntryPoint: entryPoints ? entryPoints.includes(moduleName) : false,
                packageName: null, // 稍后填充
            };
        }
    }

    // 添加包信息
    if (modulesToReplace) {
        for (const [entryName, packageInfo] of modulesToReplace.entries()) {
            const membersList = Array.from(packageInfo.members || []);
            dependencyGraph.packages[entryName] = {
                entryPoint: entryName,
                members: membersList,
                memberCount: membersList.length,
                fileName: packageInfo.fileName,
                exportedVariables: packageInfo.sortedMembers,
            };

            // 更新模块信息中的包名
            membersList.forEach(memberName => {
                if (dependencyGraph.modules[memberName]) {
                    dependencyGraph.modules[memberName].packageName = entryName;
                }
            });
        }
    }

    // 计算一些统计信息
    const stats = {
        totalDependencyCount: 0,
        averageDependenciesPerModule: 0,
        maxDependencies: 0,
        moduleWithMostDependencies: null,
    };

    let totalDeps = 0;
    let maxDeps = 0;
    let maxDepsModule = null;

    Object.values(dependencyGraph.modules).forEach(module => {
        const depCount = module.dependencies.length;
        totalDeps += depCount;
        if (depCount > maxDeps) {
            maxDeps = depCount;
            maxDepsModule = module.name;
        }
    });

    stats.totalDependencyCount = totalDeps;
    stats.averageDependenciesPerModule = Object.keys(dependencyGraph.modules).length > 0 
        ? (totalDeps / Object.keys(dependencyGraph.modules).length).toFixed(2) 
        : 0;
    stats.maxDependencies = maxDeps;
    stats.moduleWithMostDependencies = maxDepsModule;

    dependencyGraph.statistics = stats;

    const originalDependencyGraphFileName = 'dependency-graph.json';
    const dependencyGraphFileName = getUniqueFileName(OUTPUT_DIR, originalDependencyGraphFileName);
    fs.writeFileSync(
        path.join(OUTPUT_DIR, dependencyGraphFileName),
        JSON.stringify(dependencyGraph, null, 2),
        'utf-8'
    );
    console.log(`✅ Generated dependency graph at '${path.join(OUTPUT_DIR, dependencyGraphFileName)}'`);

    // --- 7. 输出统计信息 ---
    console.log('\n📊 Summary:');
    console.log(`- Total modules found: ${moduleGraph ? moduleGraph.size : 0}`);
    console.log(`- Entry points: ${entryPoints ? entryPoints.length : 0}`);
    console.log(`- Packages created: ${modulesToReplace ? modulesToReplace.size : 0}`);
    let totalExtracted = 0;
    if (modulesToReplace && typeof modulesToReplace.forEach === 'function') {
        try {
            modulesToReplace.forEach((packageInfo) => {
                if (packageInfo && packageInfo.members && typeof packageInfo.members.size === 'number') {
                    totalExtracted += packageInfo.members.size;
                }
            });
        } catch (error) {
            console.error('❌ Error calculating total extracted:', error.message);
        }
    }
    console.log(`- Total modules extracted: ${totalExtracted}`);
    const remainingModules = (moduleGraph ? moduleGraph.size : 0) - totalExtracted + (entryPoints ? entryPoints.length : 0);
    console.log(`- Remaining modules in main: ${remainingModules}`);

    // 输出每个包的详细信息
    console.log('\n📦 Package Details:');
    if (modulesToReplace) {
        for (const [entryName, packageInfo] of modulesToReplace.entries()) {
            console.log(`  📄 ${packageInfo.fileName}`);
            console.log(`     Entry: ${entryName}`);
            console.log(`     Exports: ${packageInfo.sortedMembers.join(', ')}`);
            console.log(`     Members: ${packageInfo.members.size} modules`);
        }
    }

} catch (e) {
    console.error('❌ An error occurred during the decoupling process:', e);
    console.error('Stack trace:', e.stack);
}