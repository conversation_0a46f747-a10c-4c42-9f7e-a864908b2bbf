const fs = require('fs');
const path = require('path');
const parser = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const generator = require('@babel/generator').default;
const t = require('@babel/types'); // Babel's AST node builder

const INPUT_FILE = '../cli.js';
const OUTPUT_DIR = 'output_final';
const MODULE_FACTORY_NAME = 'E';

/**
 * 生成唯一的文件名，避免大小写不敏感的文件系统冲突
 * @param {string} outputDir - 输出目录
 * @param {string} fileName - 原始文件名
 * @returns {string} 唯一的文件名
 */
function getUniqueFileName(outputDir, fileName) {
    const fullPath = path.join(outputDir, fileName);

    // 检查文件是否已存在（大小写不敏感）
    if (!fs.existsSync(fullPath)) {
        return fileName;
    }

    // 如果存在，则在文件名后添加 _d
    const ext = path.extname(fileName);
    const baseName = path.basename(fileName, ext);
    const newFileName = `${baseName}_d${ext}`;

    // 递归检查，以防 _d 版本也存在
    return getUniqueFileName(outputDir, newFileName);
}

/**
 * @typedef {object} ModuleInfo
 * @property {string} name
 * @property {Set<string>} dependencies
 * @property {Set<string>} dependents
 * @property {[number, number]} range
 * @property {object} path - The Babel path object for the module definition's declarator.
 * @property {object} statementPath - The Babel path object for the variable declaration statement.
 */

try {
    // --- 1. 读取和解析源代码 ---
    if (!fs.existsSync(INPUT_FILE)) {
        throw new Error(`Input file not found: ${INPUT_FILE}`);
    }
    const sourceCode = fs.readFileSync(INPUT_FILE, 'utf-8');
    const ast = parser.parse(sourceCode, { sourceType: 'script', errorRecovery: true });

    /** @type {Map<string, ModuleInfo>} */
    const moduleGraph = new Map();

    // --- 2. Pass 1: 识别所有模块定义 ---
    traverse(ast, {
        VariableDeclarator(path) {
            const { node } = path;
            if (
                node.init &&
                node.init.type === 'CallExpression' &&
                node.init.callee.type === 'Identifier' &&
                node.init.callee.name === MODULE_FACTORY_NAME &&
                node.id.type === 'Identifier'
            ) {
                const moduleName = node.id.name;
                const statementPath = path.findParent((p) => p.isVariableDeclaration());
                if (statementPath && !moduleGraph.has(moduleName)) {
                    // 获取代码范围
                    let range = null;
                    if (statementPath.node.start !== undefined && statementPath.node.end !== undefined) {
                        range = [statementPath.node.start, statementPath.node.end];
                    } else if (statementPath.node.range) {
                        range = statementPath.node.range;
                    }
                    
                    console.log(`🔍 Found module ${moduleName}, range: ${range ? `[${range[0]}, ${range[1]}]` : 'undefined'}`);
                    
                    moduleGraph.set(moduleName, {
                        name: moduleName,
                        dependencies: new Set(),
                        dependents: new Set(),
                        path: path,
                        range: range,
                        statementPath: statementPath, // 保存语句路径以便后续使用
                    });
                }
            }
        },
    });

    console.log(`🔍 Found ${moduleGraph.size} modules`);

    // --- 3. Pass 2: 构建依赖图 ---
    if (!moduleGraph || moduleGraph.size === 0) {
        console.warn('⚠️ No modules found in moduleGraph');
    } else {
        for (const [moduleName, moduleInfo] of moduleGraph.entries()) {
            if (!moduleInfo) {
                console.warn(`⚠️ moduleInfo is undefined for ${moduleName}`);
                continue;
            }
            
            if (!moduleInfo.path) {
                console.warn(`⚠️ moduleInfo.path is undefined for ${moduleName}`);
                continue;
            }

            const moduleBodyPath = moduleInfo.path.get('init.arguments.0');
            if (!moduleBodyPath) {
                console.warn(`⚠️ moduleBodyPath is undefined for ${moduleName}`);
                continue;
            }

            try {
                moduleBodyPath.traverse({
                    CallExpression(path) {
                        if (path.node.callee.type === 'Identifier' && moduleGraph.has(path.node.callee.name)) {
                            const dependencyName = path.node.callee.name;
                            if (moduleInfo.dependencies) {
                                moduleInfo.dependencies.add(dependencyName);
                            }
                            const dependencyInfo = moduleGraph.get(dependencyName);
                            if (dependencyInfo && dependencyInfo.dependents) {
                                dependencyInfo.dependents.add(moduleName);
                            }
                        }
                    },
                });
            } catch (error) {
                console.error(`❌ Error traversing module ${moduleName}:`, error.message);
            }
        }
    }

    // --- 4. 识别包，提取代码，并添加 `module.exports` ---
    const allModules = Array.from(moduleGraph.values()).filter(info => info && info.dependents);
    const entryPoints = allModules
        .filter(info => info.dependents.size === 0)
        .map(info => info.name);
        
    console.log(`📦 Found ${entryPoints.length} entry points:`, entryPoints);

    const visited = new Set();
    const modulesToReplace = new Map(); // Map<entryPoint, allMembers>

    if (!fs.existsSync(OUTPUT_DIR)) {
        fs.mkdirSync(OUTPUT_DIR, { recursive: true });
    }

    if (entryPoints && entryPoints.length > 0) {
        entryPoints.forEach(entryName => {
            if (!entryName) {
                console.warn('⚠️ Entry name is undefined, skipping');
                return;
            }

            const packageMembers = new Set();
            (function findPackageMembers(name) {
                if (visited.has(name) || !moduleGraph.has(name)) return;
                visited.add(name);
                packageMembers.add(name);
                const moduleInfo = moduleGraph.get(name);
                if (moduleInfo && moduleInfo.dependencies) {
                    try {
                        moduleInfo.dependencies.forEach(dep => findPackageMembers(dep));
                    } catch (error) {
                        console.error(`❌ Error processing dependencies for ${name}:`, error.message);
                    }
                }
            })(entryName);

            if (packageMembers.size > 0) {
                modulesToReplace.set(entryName, packageMembers);
                const sortedMembers = Array.from(packageMembers).sort();
                
                console.log(`📋 Package '${entryName}' contains ${sortedMembers.length} modules:`, sortedMembers);
                
                let packageCode = `// Package extracted with entry point: ${entryName}\n\n`;
                
                if (sortedMembers && sortedMembers.length > 0) {
                    sortedMembers.forEach(memberName => {
                        if (!memberName) {
                            console.warn('⚠️ Member name is undefined, skipping');
                            return;
                        }
                        const moduleInfo = moduleGraph.get(memberName);
                        if (moduleInfo) {
                            let codeToAdd = '';
                            
                            // 尝试多种方式获取代码
                            if (moduleInfo.range && Array.isArray(moduleInfo.range) && moduleInfo.range.length >= 2) {
                                const [start, end] = moduleInfo.range;
                                if (typeof start === 'number' && typeof end === 'number') {
                                    codeToAdd = sourceCode.substring(start, end);
                                    console.log(`📄 Extracted code for ${memberName} using range [${start}, ${end}]`);
                                }
                            }
                            
                            // 如果range方式失败，尝试从AST生成代码
                            if (!codeToAdd && moduleInfo.statementPath) {
                                try {
                                    const { code } = generator(moduleInfo.statementPath.node, {
                                        retainLines: false,
                                        compact: false,
                                    });
                                    codeToAdd = code;
                                    console.log(`📄 Generated code for ${memberName} from AST`);
                                } catch (error) {
                                    console.error(`❌ Error generating code for ${memberName}:`, error.message);
                                }
                            }
                            
                            if (codeToAdd) {
                                packageCode += codeToAdd;
                                if (!codeToAdd.endsWith(';') && !codeToAdd.endsWith('\n')) {
                                    packageCode += ';\n';
                                } else if (!codeToAdd.endsWith('\n')) {
                                    packageCode += '\n';
                                }
                            } else {
                                console.warn(`⚠️ Could not extract code for module ${memberName}`);
                            }
                        } else {
                            console.warn(`⚠️ ModuleInfo not found for ${memberName}`);
                        }
                    });
                }

                // ⭐ 关键步骤：在包的末尾添加导出语句
                packageCode += `\nmodule.exports = ${entryName};\n`;

                const originalPackageFileName = `package_${entryName}.js`;
                const packageFileName = getUniqueFileName(OUTPUT_DIR, originalPackageFileName);
                fs.writeFileSync(path.join(OUTPUT_DIR, packageFileName), packageCode, 'utf-8');
                console.log(`✅ Extracted package '${entryName}' to '${path.join(OUTPUT_DIR, packageFileName)}'`);
            }
        });
    } else {
        console.warn('⚠️ No entry points found');
    }

    // --- 5. 修改主 AST：用 `require()` 替换整个包的根入口定义 ---
    // 首先收集所有需要删除的模块路径
    const pathsToRemove = new Set();
    
    traverse(ast, {
        VariableDeclarator(path) {
            const { node } = path;
            if (
                node.init &&
                node.init.type === 'CallExpression' &&
                node.init.callee.type === 'Identifier' &&
                node.init.callee.name === MODULE_FACTORY_NAME &&
                node.id.type === 'Identifier'
            ) {
                const moduleName = node.id.name;
                
                // 检查这个模块是不是我们确定的一个包的入口点
                if (modulesToReplace && modulesToReplace.has(moduleName)) {
                    const packageFileName = `./package_${moduleName}.js`;
                    
                    // ⭐ 关键步骤：构建 `require('./package_xxx.js')` AST 节点
                    const requireCall = t.callExpression(t.identifier('require'), [t.stringLiteral(packageFileName)]);
                    
                    // 用 `require` 调用替换原来的 `E(...)` 调用
                    const initPath = path.get('init');
                    if (initPath) {
                        initPath.replaceWith(requireCall);
                        console.log(`🔄 Replaced ${moduleName} with require('${packageFileName}')`);
                    }

                    // 标记这个包里的其他成员需要删除
                    const membersToRemove = modulesToReplace.get(moduleName);
                    if (membersToRemove && typeof membersToRemove.forEach === 'function') {
                        try {
                            membersToRemove.forEach(memberName => {
                                if (memberName && memberName !== moduleName) { // 不要删除入口自身，因为它要被替换
                                    const memberInfo = moduleGraph.get(memberName);
                                    if (memberInfo && memberInfo.path) {
                                        const statementPath = memberInfo.path.findParent((p) => p.isVariableDeclaration());
                                        if (statementPath) {
                                            pathsToRemove.add(statementPath);
                                        }
                                    }
                                }
                            });
                        } catch (error) {
                            console.error(`❌ Error processing members to remove for ${moduleName}:`, error.message);
                        }
                    }
                } else if (modulesToReplace) {
                    // 检查这个模块是否属于某个包的成员（非入口点）
                    try {
                        for (const [entryName, members] of modulesToReplace.entries()) {
                            if (members && typeof members.has === 'function' && members.has(moduleName) && moduleName !== entryName) {
                                const statementPath = path.findParent((p) => p.isVariableDeclaration());
                                if (statementPath) {
                                    pathsToRemove.add(statementPath);
                                }
                                break;
                            }
                        }
                    } catch (error) {
                        console.error(`❌ Error checking module membership for ${moduleName}:`, error.message);
                    }
                }
            }
        },
    });

    // 删除标记的路径
    if (pathsToRemove && typeof pathsToRemove.forEach === 'function') {
        try {
            pathsToRemove.forEach(pathToRemove => {
                if (pathToRemove && !pathToRemove.removed) {
                    pathToRemove.remove();
                }
            });
        } catch (error) {
            console.error('❌ Error removing paths:', error.message);
        }
    }

    // --- 6. 生成新的主文件 ---
    const { code: newMainCode } = generator(ast, {
        retainLines: false, // 让代码更紧凑
        compact: false,
    });

    const originalMainFileName = 'main.js';
    const mainFileName = getUniqueFileName(OUTPUT_DIR, originalMainFileName);
    fs.writeFileSync(path.join(OUTPUT_DIR, mainFileName), newMainCode, 'utf-8');
    console.log(`✅ Created new decoupled main file at '${path.join(OUTPUT_DIR, mainFileName)}'`);

    // --- 生成依赖图 JSON 文件 ---
    const dependencyGraph = {
        metadata: {
            totalModules: moduleGraph ? moduleGraph.size : 0,
            entryPoints: entryPoints ? entryPoints.length : 0,
            packagesCreated: modulesToReplace ? modulesToReplace.size : 0,
            generatedAt: new Date().toISOString(),
        },
        modules: {},
        packages: {},
        entryPoints: entryPoints || [],
    };

    // 添加所有模块信息
    if (moduleGraph) {
        for (const [moduleName, moduleInfo] of moduleGraph.entries()) {
            dependencyGraph.modules[moduleName] = {
                name: moduleName,
                dependencies: Array.from(moduleInfo.dependencies || []),
                dependents: Array.from(moduleInfo.dependents || []),
                isEntryPoint: entryPoints ? entryPoints.includes(moduleName) : false,
                packageName: null, // 稍后填充
            };
        }
    }

    // 添加包信息
    if (modulesToReplace) {
        for (const [entryName, members] of modulesToReplace.entries()) {
            const membersList = Array.from(members || []);
            dependencyGraph.packages[entryName] = {
                entryPoint: entryName,
                members: membersList,
                memberCount: membersList.length,
                fileName: `package_${entryName}.js`,
            };

            // 更新模块信息中的包名
            membersList.forEach(memberName => {
                if (dependencyGraph.modules[memberName]) {
                    dependencyGraph.modules[memberName].packageName = entryName;
                }
            });
        }
    }

    // 计算一些统计信息
    const stats = {
        totalDependencyCount: 0,
        averageDependenciesPerModule: 0,
        maxDependencies: 0,
        moduleWithMostDependencies: null,
    };

    let totalDeps = 0;
    let maxDeps = 0;
    let maxDepsModule = null;

    Object.values(dependencyGraph.modules).forEach(module => {
        const depCount = module.dependencies.length;
        totalDeps += depCount;
        if (depCount > maxDeps) {
            maxDeps = depCount;
            maxDepsModule = module.name;
        }
    });

    stats.totalDependencyCount = totalDeps;
    stats.averageDependenciesPerModule = Object.keys(dependencyGraph.modules).length > 0 
        ? (totalDeps / Object.keys(dependencyGraph.modules).length).toFixed(2) 
        : 0;
    stats.maxDependencies = maxDeps;
    stats.moduleWithMostDependencies = maxDepsModule;

    dependencyGraph.statistics = stats;

    const originalDependencyGraphFileName = 'dependency-graph.json';
    const dependencyGraphFileName = getUniqueFileName(OUTPUT_DIR, originalDependencyGraphFileName);
    fs.writeFileSync(
        path.join(OUTPUT_DIR, dependencyGraphFileName),
        JSON.stringify(dependencyGraph, null, 2),
        'utf-8'
    );
    console.log(`✅ Generated dependency graph at '${path.join(OUTPUT_DIR, dependencyGraphFileName)}'`);

    // --- 7. 输出统计信息 ---
    console.log('\n📊 Summary:');
    console.log(`- Total modules found: ${moduleGraph ? moduleGraph.size : 0}`);
    console.log(`- Entry points: ${entryPoints ? entryPoints.length : 0}`);
    console.log(`- Packages created: ${modulesToReplace ? modulesToReplace.size : 0}`);
    let totalExtracted = 0;
    if (modulesToReplace && typeof modulesToReplace.forEach === 'function') {
        try {
            modulesToReplace.forEach((members) => {
                if (members && typeof members.size === 'number') {
                    totalExtracted += members.size;
                }
            });
        } catch (error) {
            console.error('❌ Error calculating total extracted:', error.message);
        }
    }
    console.log(`- Total modules extracted: ${totalExtracted}`);
    const remainingModules = (moduleGraph ? moduleGraph.size : 0) - totalExtracted + (entryPoints ? entryPoints.length : 0);
    console.log(`- Remaining modules in main: ${remainingModules}`);

} catch (e) {
    console.error('❌ An error occurred during the decoupling process:', e);
    console.error('Stack trace:', e.stack);
}