// Package extracted with entry point: fdB
// Contains 8 variables: CI1, TdB, Vg1, fdB, iO0, nO0, vdB, ydB

var CI1=E((SL8)=>{class pO0 extends Error{constructor(A,B,Q){super(Q);Error.captureStackTrace(this,this.constructor),this.name=this.constructor.name,this.code=B,this.exitCode=A,this.nestedError=void 0}}class NdB extends pO0{constructor(A){super(1,"commander.invalidArgument",A);Error.captureStackTrace(this,this.constructor),this.name=this.constructor.name}}SL8.CommanderError=pO0;SL8.InvalidArgumentError=NdB});
var TdB=E((aL8)=>{function iL8(A,B){if(Math.abs(A.length-B.length)>3)return Math.max(A.length,B.length);let Q=[];for(let D=0;D<=A.length;D++)Q[D]=[D];for(let D=0;D<=B.length;D++)Q[0][D]=D;for(let D=1;D<=B.length;D++)for(let Z=1;Z<=A.length;Z++){let G=1;if(A[Z-1]===B[D-1])G=0;else G=1;if(Q[Z][D]=Math.min(Q[Z-1][D]+1,Q[Z][D-1]+1,Q[Z-1][D-1]+G),Z>1&&D>1&&A[Z-1]===B[D-2]&&A[Z-2]===B[D-1])Q[Z][D]=Math.min(Q[Z][D],Q[Z-2][D-2]+1)}return Q[A.length][B.length]}function nL8(A,B){if(!B||B.length===0)return"";B=Array.from(new Set(B));let Q=A.startsWith("--");if(Q)A=A.slice(2),B=B.map((F)=>F.slice(2));let D=[],Z=3,G=0.4;if(B.forEach((F)=>{if(F.length<=1)return;let I=iL8(A,F),Y=Math.max(A.length,F.length);if((Y-I)/Y>G){if(I<Z)Z=I,D=[F];else if(I===Z)D.push(F)}}),D.sort((F,I)=>F.localeCompare(I)),Q)D=D.map((F)=>`--${F}`);if(D.length>1)return`
(Did you mean one of ${D.join(", ")}?)`;if(D.length===1)return`
(Did you mean ${D[0]}?)`;return""}aL8.suggestSimilar=nL8});
var Vg1=E((xL8)=>{var{InvalidArgumentError:kL8}=CI1();class LdB{constructor(A,B){switch(this.description=B||"",this.variadic=!1,this.parseArg=void 0,this.defaultValue=void 0,this.defaultValueDescription=void 0,this.argChoices=void 0,A[0]){case"<":this.required=!0,this._name=A.slice(1,-1);break;case"[":this.required=!1,this._name=A.slice(1,-1);break;default:this.required=!0,this._name=A;break}if(this._name.length>3&&this._name.slice(-3)==="...")this.variadic=!0,this._name=this._name.slice(0,-3)}name(){return this._name}_concatValue(A,B){if(B===this.defaultValue||!Array.isArray(B))return[A];return B.concat(A)}default(A,B){return this.defaultValue=A,this.defaultValueDescription=B,this}argParser(A){return this.parseArg=A,this}choices(A){return this.argChoices=A.slice(),this.parseArg=(B,Q)=>{if(!this.argChoices.includes(B))throw new kL8(`Allowed choices are ${this.argChoices.join(", ")}.`);if(this.variadic)return this._concatValue(B,Q);return B},this}argRequired(){return this.required=!0,this}argOptional(){return this.required=!1,this}}function _L8(A){let B=A.name()+(A.variadic===!0?"...":"");return A.required?"<"+B+">":"["+B+"]"}xL8.Argument=LdB;xL8.humanReadableArgName=_L8});
var fdB=E((IH,bdB)=>{var Gq=vdB();IH=bdB.exports={};IH.program=new Gq.Command;IH.Argument=Gq.Argument;IH.Command=Gq.Command;IH.CommanderError=Gq.CommanderError;IH.Help=Gq.Help;IH.InvalidArgumentError=Gq.InvalidArgumentError;IH.InvalidOptionArgumentError=Gq.InvalidArgumentError;IH.Option=Gq.Option;IH.createCommand=(A)=>new Gq.Command(A);IH.createOption=(A,B)=>new Gq.Option(A,B);IH.createArgument=(A,B)=>new Gq.Argument(A,B)});
var iO0=E((hL8)=>{var{humanReadableArgName:fL8}=Vg1();class MdB{constructor(){this.helpWidth=void 0,this.sortSubcommands=!1,this.sortOptions=!1,this.showGlobalOptions=!1}visibleCommands(A){let B=A.commands.filter((D)=>!D._hidden),Q=A._getHelpCommand();if(Q&&!Q._hidden)B.push(Q);if(this.sortSubcommands)B.sort((D,Z)=>{return D.name().localeCompare(Z.name())});return B}compareOptions(A,B){let Q=(D)=>{return D.short?D.short.replace(/^-/,""):D.long.replace(/^--/,"")};return Q(A).localeCompare(Q(B))}visibleOptions(A){let B=A.options.filter((D)=>!D.hidden),Q=A._getHelpOption();if(Q&&!Q.hidden){let D=Q.short&&A._findOption(Q.short),Z=Q.long&&A._findOption(Q.long);if(!D&&!Z)B.push(Q);else if(Q.long&&!Z)B.push(A.createOption(Q.long,Q.description));else if(Q.short&&!D)B.push(A.createOption(Q.short,Q.description))}if(this.sortOptions)B.sort(this.compareOptions);return B}visibleGlobalOptions(A){if(!this.showGlobalOptions)return[];let B=[];for(let Q=A.parent;Q;Q=Q.parent){let D=Q.options.filter((Z)=>!Z.hidden);B.push(...D)}if(this.sortOptions)B.sort(this.compareOptions);return B}visibleArguments(A){if(A._argsDescription)A.registeredArguments.forEach((B)=>{B.description=B.description||A._argsDescription[B.name()]||""});if(A.registeredArguments.find((B)=>B.description))return A.registeredArguments;return[]}subcommandTerm(A){let B=A.registeredArguments.map((Q)=>fL8(Q)).join(" ");return A._name+(A._aliases[0]?"|"+A._aliases[0]:"")+(A.options.length?" [options]":"")+(B?" "+B:"")}optionTerm(A){return A.flags}argumentTerm(A){return A.name()}longestSubcommandTermLength(A,B){return B.visibleCommands(A).reduce((Q,D)=>{return Math.max(Q,B.subcommandTerm(D).length)},0)}longestOptionTermLength(A,B){return B.visibleOptions(A).reduce((Q,D)=>{return Math.max(Q,B.optionTerm(D).length)},0)}longestGlobalOptionTermLength(A,B){return B.visibleGlobalOptions(A).reduce((Q,D)=>{return Math.max(Q,B.optionTerm(D).length)},0)}longestArgumentTermLength(A,B){return B.visibleArguments(A).reduce((Q,D)=>{return Math.max(Q,B.argumentTerm(D).length)},0)}commandUsage(A){let B=A._name;if(A._aliases[0])B=B+"|"+A._aliases[0];let Q="";for(let D=A.parent;D;D=D.parent)Q=D.name()+" "+Q;return Q+B+" "+A.usage()}commandDescription(A){return A.description()}subcommandDescription(A){return A.summary()||A.description()}optionDescription(A){let B=[];if(A.argChoices)B.push(`choices: ${A.argChoices.map((Q)=>JSON.stringify(Q)).join(", ")}`);if(A.defaultValue!==void 0){if(A.required||A.optional||A.isBoolean()&&typeof A.defaultValue==="boolean")B.push(`default: ${A.defaultValueDescription||JSON.stringify(A.defaultValue)}`)}if(A.presetArg!==void 0&&A.optional)B.push(`preset: ${JSON.stringify(A.presetArg)}`);if(A.envVar!==void 0)B.push(`env: ${A.envVar}`);if(B.length>0)return`${A.description} (${B.join(", ")})`;return A.description}argumentDescription(A){let B=[];if(A.argChoices)B.push(`choices: ${A.argChoices.map((Q)=>JSON.stringify(Q)).join(", ")}`);if(A.defaultValue!==void 0)B.push(`default: ${A.defaultValueDescription||JSON.stringify(A.defaultValue)}`);if(B.length>0){let Q=`(${B.join(", ")})`;if(A.description)return`${A.description} ${Q}`;return Q}return A.description}formatHelp(A,B){let Q=B.padWidth(A,B),D=B.helpWidth||80,Z=2,G=2;function F(C,K){if(K){let H=`${C.padEnd(Q+2)}${K}`;return B.wrap(H,D-2,Q+2)}return C}function I(C){return C.join(`
`).replace(/^/gm," ".repeat(2))}let Y=[`Usage: ${B.commandUsage(A)}`,""],W=B.commandDescription(A);if(W.length>0)Y=Y.concat([B.wrap(W,D,0),""]);let J=B.visibleArguments(A).map((C)=>{return F(B.argumentTerm(C),B.argumentDescription(C))});if(J.length>0)Y=Y.concat(["Arguments:",I(J),""]);let X=B.visibleOptions(A).map((C)=>{return F(B.optionTerm(C),B.optionDescription(C))});if(X.length>0)Y=Y.concat(["Options:",I(X),""]);if(this.showGlobalOptions){let C=B.visibleGlobalOptions(A).map((K)=>{return F(B.optionTerm(K),B.optionDescription(K))});if(C.length>0)Y=Y.concat(["Global Options:",I(C),""])}let V=B.visibleCommands(A).map((C)=>{return F(B.subcommandTerm(C),B.subcommandDescription(C))});if(V.length>0)Y=Y.concat(["Commands:",I(V),""]);return Y.join(`
`)}padWidth(A,B){return Math.max(B.longestOptionTermLength(A,B),B.longestGlobalOptionTermLength(A,B),B.longestSubcommandTermLength(A,B),B.longestArgumentTermLength(A,B))}wrap(A,B,Q,D=40){let G=new RegExp(`[\\n][${" \\f\\t\\v   -   　\uFEFF"}]+`);if(A.match(G))return A;let F=B-Q;if(F<D)return A;let I=A.slice(0,Q),Y=A.slice(Q).replace(`\r
`,`
`),W=" ".repeat(Q),X=`\\s${"​"}`,V=new RegExp(`
|.{1,${F-1}}([${X}]|$)|[^${X}]+?([${X}]|$)`,"g"),C=Y.match(V)||[];return I+C.map((K,H)=>{if(K===`
`)return"";return(H>0?W:"")+K.trimEnd()}).join(`
`)}}hL8.Help=MdB});
var nO0=E((cL8)=>{var{InvalidArgumentError:uL8}=CI1();class RdB{constructor(A,B){this.flags=A,this.description=B||"",this.required=A.includes("<"),this.optional=A.includes("["),this.variadic=/\w\.\.\.[>\]]$/.test(A),this.mandatory=!1;let Q=dL8(A);if(this.short=Q.shortFlag,this.long=Q.longFlag,this.negate=!1,this.long)this.negate=this.long.startsWith("--no-");this.defaultValue=void 0,this.defaultValueDescription=void 0,this.presetArg=void 0,this.envVar=void 0,this.parseArg=void 0,this.hidden=!1,this.argChoices=void 0,this.conflictsWith=[],this.implied=void 0}default(A,B){return this.defaultValue=A,this.defaultValueDescription=B,this}preset(A){return this.presetArg=A,this}conflicts(A){return this.conflictsWith=this.conflictsWith.concat(A),this}implies(A){let B=A;if(typeof A==="string")B={[A]:!0};return this.implied=Object.assign(this.implied||{},B),this}env(A){return this.envVar=A,this}argParser(A){return this.parseArg=A,this}makeOptionMandatory(A=!0){return this.mandatory=!!A,this}hideHelp(A=!0){return this.hidden=!!A,this}_concatValue(A,B){if(B===this.defaultValue||!Array.isArray(B))return[A];return B.concat(A)}choices(A){return this.argChoices=A.slice(),this.parseArg=(B,Q)=>{if(!this.argChoices.includes(B))throw new uL8(`Allowed choices are ${this.argChoices.join(", ")}.`);if(this.variadic)return this._concatValue(B,Q);return B},this}name(){if(this.long)return this.long.replace(/^--/,"");return this.short.replace(/^-/,"")}attributeName(){return mL8(this.name().replace(/^no-/,""))}is(A){return this.short===A||this.long===A}isBoolean(){return!this.required&&!this.optional&&!this.negate}}class OdB{constructor(A){this.positiveOptions=new Map,this.negativeOptions=new Map,this.dualOptions=new Set,A.forEach((B)=>{if(B.negate)this.negativeOptions.set(B.attributeName(),B);else this.positiveOptions.set(B.attributeName(),B)}),this.negativeOptions.forEach((B,Q)=>{if(this.positiveOptions.has(Q))this.dualOptions.add(Q)})}valueFromOption(A,B){let Q=B.attributeName();if(!this.dualOptions.has(Q))return!0;let D=this.negativeOptions.get(Q).presetArg,Z=D!==void 0?D:!1;return B.negate===(Z===A)}}function mL8(A){return A.split("-").reduce((B,Q)=>{return B+Q[0].toUpperCase()+Q.slice(1)})}function dL8(A){let B,Q,D=A.split(/[ |,]+/);if(D.length>1&&!/^[[<]/.test(D[1]))B=D.shift();if(Q=D.shift(),!B&&/^-[^-]$/.test(Q))B=Q,Q=void 0;return{shortFlag:B,longFlag:Q}}cL8.Option=RdB;cL8.DualOptions=OdB});
var vdB=E((GM8)=>{var{Argument:kdB}=Vg1(),{Command:tO0}=ydB(),{CommanderError:DM8,InvalidArgumentError:_dB}=CI1(),{Help:ZM8}=iO0(),{Option:xdB}=nO0();GM8.program=new tO0;GM8.createCommand=(A)=>new tO0(A);GM8.createOption=(A,B)=>new xdB(A,B);GM8.createArgument=(A,B)=>new kdB(A,B);GM8.Command=tO0;GM8.Option=xdB;GM8.Argument=kdB;GM8.Help=ZM8;GM8.CommanderError=DM8;GM8.InvalidArgumentError=_dB;GM8.InvalidOptionArgumentError=_dB});
var ydB=E((BM8)=>{var rL8=J1("node:events").EventEmitter,aO0=J1("node:child_process"),bS=J1("node:path"),sO0=J1("node:fs"),PZ=J1("node:process"),{Argument:oL8,humanReadableArgName:tL8}=Vg1(),{CommanderError:rO0}=CI1(),{Help:eL8}=iO0(),{Option:PdB,DualOptions:AM8}=nO0(),{suggestSimilar:SdB}=TdB();class oO0 extends rL8{constructor(A){super();this.commands=[],this.options=[],this.parent=null,this._allowUnknownOption=!1,this._allowExcessArguments=!0,this.registeredArguments=[],this._args=this.registeredArguments,this.args=[],this.rawArgs=[],this.processedArgs=[],this._scriptPath=null,this._name=A||"",this._optionValues={},this._optionValueSources={},this._storeOptionsAsProperties=!1,this._actionHandler=null,this._executableHandler=!1,this._executableFile=null,this._executableDir=null,this._defaultCommandName=null,this._exitCallback=null,this._aliases=[],this._combineFlagAndOptionalValue=!0,this._description="",this._summary="",this._argsDescription=void 0,this._enablePositionalOptions=!1,this._passThroughOptions=!1,this._lifeCycleHooks={},this._showHelpAfterError=!1,this._showSuggestionAfterError=!0,this._outputConfiguration={writeOut:(B)=>PZ.stdout.write(B),writeErr:(B)=>PZ.stderr.write(B),getOutHelpWidth:()=>PZ.stdout.isTTY?PZ.stdout.columns:void 0,getErrHelpWidth:()=>PZ.stderr.isTTY?PZ.stderr.columns:void 0,outputError:(B,Q)=>Q(B)},this._hidden=!1,this._helpOption=void 0,this._addImplicitHelpCommand=void 0,this._helpCommand=void 0,this._helpConfiguration={}}copyInheritedSettings(A){return this._outputConfiguration=A._outputConfiguration,this._helpOption=A._helpOption,this._helpCommand=A._helpCommand,this._helpConfiguration=A._helpConfiguration,this._exitCallback=A._exitCallback,this._storeOptionsAsProperties=A._storeOptionsAsProperties,this._combineFlagAndOptionalValue=A._combineFlagAndOptionalValue,this._allowExcessArguments=A._allowExcessArguments,this._enablePositionalOptions=A._enablePositionalOptions,this._showHelpAfterError=A._showHelpAfterError,this._showSuggestionAfterError=A._showSuggestionAfterError,this}_getCommandAndAncestors(){let A=[];for(let B=this;B;B=B.parent)A.push(B);return A}command(A,B,Q){let D=B,Z=Q;if(typeof D==="object"&&D!==null)Z=D,D=null;Z=Z||{};let[,G,F]=A.match(/([^ ]+) *(.*)/),I=this.createCommand(G);if(D)I.description(D),I._executableHandler=!0;if(Z.isDefault)this._defaultCommandName=I._name;if(I._hidden=!!(Z.noHelp||Z.hidden),I._executableFile=Z.executableFile||null,F)I.arguments(F);if(this._registerCommand(I),I.parent=this,I.copyInheritedSettings(this),D)return this;return I}createCommand(A){return new oO0(A)}createHelp(){return Object.assign(new eL8,this.configureHelp())}configureHelp(A){if(A===void 0)return this._helpConfiguration;return this._helpConfiguration=A,this}configureOutput(A){if(A===void 0)return this._outputConfiguration;return Object.assign(this._outputConfiguration,A),this}showHelpAfterError(A=!0){if(typeof A!=="string")A=!!A;return this._showHelpAfterError=A,this}showSuggestionAfterError(A=!0){return this._showSuggestionAfterError=!!A,this}addCommand(A,B){if(!A._name)throw new Error(`Command passed to .addCommand() must have a name
- specify the name in Command constructor or using .name()`);if(B=B||{},B.isDefault)this._defaultCommandName=A._name;if(B.noHelp||B.hidden)A._hidden=!0;return this._registerCommand(A),A.parent=this,A._checkForBrokenPassThrough(),this}createArgument(A,B){return new oL8(A,B)}argument(A,B,Q,D){let Z=this.createArgument(A,B);if(typeof Q==="function")Z.default(D).argParser(Q);else Z.default(Q);return this.addArgument(Z),this}arguments(A){return A.trim().split(/ +/).forEach((B)=>{this.argument(B)}),this}addArgument(A){let B=this.registeredArguments.slice(-1)[0];if(B&&B.variadic)throw new Error(`only the last argument can be variadic '${B.name()}'`);if(A.required&&A.defaultValue!==void 0&&A.parseArg===void 0)throw new Error(`a default value for a required argument is never used: '${A.name()}'`);return this.registeredArguments.push(A),this}helpCommand(A,B){if(typeof A==="boolean")return this._addImplicitHelpCommand=A,this;A=A??"help [command]";let[,Q,D]=A.match(/([^ ]+) *(.*)/),Z=B??"display help for command",G=this.createCommand(Q);if(G.helpOption(!1),D)G.arguments(D);if(Z)G.description(Z);return this._addImplicitHelpCommand=!0,this._helpCommand=G,this}addHelpCommand(A,B){if(typeof A!=="object")return this.helpCommand(A,B),this;return this._addImplicitHelpCommand=!0,this._helpCommand=A,this}_getHelpCommand(){if(this._addImplicitHelpCommand??(this.commands.length&&!this._actionHandler&&!this._findCommand("help"))){if(this._helpCommand===void 0)this.helpCommand(void 0,void 0);return this._helpCommand}return null}hook(A,B){let Q=["preSubcommand","preAction","postAction"];if(!Q.includes(A))throw new Error(`Unexpected value for event passed to hook : '${A}'.
Expecting one of '${Q.join("', '")}'`);if(this._lifeCycleHooks[A])this._lifeCycleHooks[A].push(B);else this._lifeCycleHooks[A]=[B];return this}exitOverride(A){if(A)this._exitCallback=A;else this._exitCallback=(B)=>{if(B.code!=="commander.executeSubCommandAsync")throw B};return this}_exit(A,B,Q){if(this._exitCallback)this._exitCallback(new rO0(A,B,Q));PZ.exit(A)}action(A){let B=(Q)=>{let D=this.registeredArguments.length,Z=Q.slice(0,D);if(this._storeOptionsAsProperties)Z[D]=this;else Z[D]=this.opts();return Z.push(this),A.apply(this,Z)};return this._actionHandler=B,this}createOption(A,B){return new PdB(A,B)}_callParseArg(A,B,Q,D){try{return A.parseArg(B,Q)}catch(Z){if(Z.code==="commander.invalidArgument"){let G=`${D} ${Z.message}`;this.error(G,{exitCode:Z.exitCode,code:Z.code})}throw Z}}_registerOption(A){let B=A.short&&this._findOption(A.short)||A.long&&this._findOption(A.long);if(B){let Q=A.long&&this._findOption(A.long)?A.long:A.short;throw new Error(`Cannot add option '${A.flags}'${this._name&&` to command '${this._name}'`} due to conflicting flag '${Q}'
-  already used by option '${B.flags}'`)}this.options.push(A)}_registerCommand(A){let B=(D)=>{return[D.name()].concat(D.aliases())},Q=B(A).find((D)=>this._findCommand(D));if(Q){let D=B(this._findCommand(Q)).join("|"),Z=B(A).join("|");throw new Error(`cannot add command '${Z}' as already have command '${D}'`)}this.commands.push(A)}addOption(A){this._registerOption(A);let B=A.name(),Q=A.attributeName();if(A.negate){let Z=A.long.replace(/^--no-/,"--");if(!this._findOption(Z))this.setOptionValueWithSource(Q,A.defaultValue===void 0?!0:A.defaultValue,"default")}else if(A.defaultValue!==void 0)this.setOptionValueWithSource(Q,A.defaultValue,"default");let D=(Z,G,F)=>{if(Z==null&&A.presetArg!==void 0)Z=A.presetArg;let I=this.getOptionValue(Q);if(Z!==null&&A.parseArg)Z=this._callParseArg(A,Z,I,G);else if(Z!==null&&A.variadic)Z=A._concatValue(Z,I);if(Z==null)if(A.negate)Z=!1;else if(A.isBoolean()||A.optional)Z=!0;else Z="";this.setOptionValueWithSource(Q,Z,F)};if(this.on("option:"+B,(Z)=>{let G=`error: option '${A.flags}' argument '${Z}' is invalid.`;D(Z,G,"cli")}),A.envVar)this.on("optionEnv:"+B,(Z)=>{let G=`error: option '${A.flags}' value '${Z}' from env '${A.envVar}' is invalid.`;D(Z,G,"env")});return this}_optionEx(A,B,Q,D,Z){if(typeof B==="object"&&B instanceof PdB)throw new Error("To add an Option object use addOption() instead of option() or requiredOption()");let G=this.createOption(B,Q);if(G.makeOptionMandatory(!!A.mandatory),typeof D==="function")G.default(Z).argParser(D);else if(D instanceof RegExp){let F=D;D=(I,Y)=>{let W=F.exec(I);return W?W[0]:Y},G.default(Z).argParser(D)}else G.default(D);return this.addOption(G)}option(A,B,Q,D){return this._optionEx({},A,B,Q,D)}requiredOption(A,B,Q,D){return this._optionEx({mandatory:!0},A,B,Q,D)}combineFlagAndOptionalValue(A=!0){return this._combineFlagAndOptionalValue=!!A,this}allowUnknownOption(A=!0){return this._allowUnknownOption=!!A,this}allowExcessArguments(A=!0){return this._allowExcessArguments=!!A,this}enablePositionalOptions(A=!0){return this._enablePositionalOptions=!!A,this}passThroughOptions(A=!0){return this._passThroughOptions=!!A,this._checkForBrokenPassThrough(),this}_checkForBrokenPassThrough(){if(this.parent&&this._passThroughOptions&&!this.parent._enablePositionalOptions)throw new Error(`passThroughOptions cannot be used for '${this._name}' without turning on enablePositionalOptions for parent command(s)`)}storeOptionsAsProperties(A=!0){if(this.options.length)throw new Error("call .storeOptionsAsProperties() before adding options");if(Object.keys(this._optionValues).length)throw new Error("call .storeOptionsAsProperties() before setting option values");return this._storeOptionsAsProperties=!!A,this}getOptionValue(A){if(this._storeOptionsAsProperties)return this[A];return this._optionValues[A]}setOptionValue(A,B){return this.setOptionValueWithSource(A,B,void 0)}setOptionValueWithSource(A,B,Q){if(this._storeOptionsAsProperties)this[A]=B;else this._optionValues[A]=B;return this._optionValueSources[A]=Q,this}getOptionValueSource(A){return this._optionValueSources[A]}getOptionValueSourceWithGlobals(A){let B;return this._getCommandAndAncestors().forEach((Q)=>{if(Q.getOptionValueSource(A)!==void 0)B=Q.getOptionValueSource(A)}),B}_prepareUserArgs(A,B){if(A!==void 0&&!Array.isArray(A))throw new Error("first parameter to parse must be array or undefined");if(B=B||{},A===void 0&&B.from===void 0){if(PZ.versions?.electron)B.from="electron";let D=PZ.execArgv??[];if(D.includes("-e")||D.includes("--eval")||D.includes("-p")||D.includes("--print"))B.from="eval"}if(A===void 0)A=PZ.argv;this.rawArgs=A.slice();let Q;switch(B.from){case void 0:case"node":this._scriptPath=A[1],Q=A.slice(2);break;case"electron":if(PZ.defaultApp)this._scriptPath=A[1],Q=A.slice(2);else Q=A.slice(1);break;case"user":Q=A.slice(0);break;case"eval":Q=A.slice(1);break;default:throw new Error(`unexpected parse option { from: '${B.from}' }`)}if(!this._name&&this._scriptPath)this.nameFromFilename(this._scriptPath);return this._name=this._name||"program",Q}parse(A,B){let Q=this._prepareUserArgs(A,B);return this._parseCommand([],Q),this}async parseAsync(A,B){let Q=this._prepareUserArgs(A,B);return await this._parseCommand([],Q),this}_executeSubCommand(A,B){B=B.slice();let Q=!1,D=[".js",".ts",".tsx",".mjs",".cjs"];function Z(W,J){let X=bS.resolve(W,J);if(sO0.existsSync(X))return X;if(D.includes(bS.extname(J)))return;let V=D.find((C)=>sO0.existsSync(`${X}${C}`));if(V)return`${X}${V}`;return}this._checkForMissingMandatoryOptions(),this._checkForConflictingOptions();let G=A._executableFile||`${this._name}-${A._name}`,F=this._executableDir||"";if(this._scriptPath){let W;try{W=sO0.realpathSync(this._scriptPath)}catch(J){W=this._scriptPath}F=bS.resolve(bS.dirname(W),F)}if(F){let W=Z(F,G);if(!W&&!A._executableFile&&this._scriptPath){let J=bS.basename(this._scriptPath,bS.extname(this._scriptPath));if(J!==this._name)W=Z(F,`${J}-${A._name}`)}G=W||G}Q=D.includes(bS.extname(G));let I;if(PZ.platform!=="win32")if(Q)B.unshift(G),B=jdB(PZ.execArgv).concat(B),I=aO0.spawn(PZ.argv[0],B,{stdio:"inherit"});else I=aO0.spawn(G,B,{stdio:"inherit"});else B.unshift(G),B=jdB(PZ.execArgv).concat(B),I=aO0.spawn(PZ.execPath,B,{stdio:"inherit"});if(!I.killed)["SIGUSR1","SIGUSR2","SIGTERM","SIGINT","SIGHUP"].forEach((J)=>{PZ.on(J,()=>{if(I.killed===!1&&I.exitCode===null)I.kill(J)})});let Y=this._exitCallback;I.on("close",(W)=>{if(W=W??1,!Y)PZ.exit(W);else Y(new rO0(W,"commander.executeSubCommandAsync","(close)"))}),I.on("error",(W)=>{if(W.code==="ENOENT"){let J=F?`searched for local subcommand relative to directory '${F}'`:"no directory for search for local subcommand, use .executableDir() to supply a custom directory",X=`'${G}' does not exist
 - if '${A._name}' is not meant to be an executable command, remove description parameter from '.command()' and use '.description()' instead
 - if the default executable name is not suitable, use the executableFile option to supply a custom name or path
 - ${J}`;throw new Error(X)}else if(W.code==="EACCES")throw new Error(`'${G}' not executable`);if(!Y)PZ.exit(1);else{let J=new rO0(1,"commander.executeSubCommandAsync","(error)");J.nestedError=W,Y(J)}}),this.runningCommand=I}_dispatchSubcommand(A,B,Q){let D=this._findCommand(A);if(!D)this.help({error:!0});let Z;return Z=this._chainOrCallSubCommandHook(Z,D,"preSubcommand"),Z=this._chainOrCall(Z,()=>{if(D._executableHandler)this._executeSubCommand(D,B.concat(Q));else return D._parseCommand(B,Q)}),Z}_dispatchHelpCommand(A){if(!A)this.help();let B=this._findCommand(A);if(B&&!B._executableHandler)B.help();return this._dispatchSubcommand(A,[],[this._getHelpOption()?.long??this._getHelpOption()?.short??"--help"])}_checkNumberOfArguments(){if(this.registeredArguments.forEach((A,B)=>{if(A.required&&this.args[B]==null)this.missingArgument(A.name())}),this.registeredArguments.length>0&&this.registeredArguments[this.registeredArguments.length-1].variadic)return;if(this.args.length>this.registeredArguments.length)this._excessArguments(this.args)}_processArguments(){let A=(Q,D,Z)=>{let G=D;if(D!==null&&Q.parseArg){let F=`error: command-argument value '${D}' is invalid for argument '${Q.name()}'.`;G=this._callParseArg(Q,D,Z,F)}return G};this._checkNumberOfArguments();let B=[];this.registeredArguments.forEach((Q,D)=>{let Z=Q.defaultValue;if(Q.variadic){if(D<this.args.length){if(Z=this.args.slice(D),Q.parseArg)Z=Z.reduce((G,F)=>{return A(Q,F,G)},Q.defaultValue)}else if(Z===void 0)Z=[]}else if(D<this.args.length){if(Z=this.args[D],Q.parseArg)Z=A(Q,Z,Q.defaultValue)}B[D]=Z}),this.processedArgs=B}_chainOrCall(A,B){if(A&&A.then&&typeof A.then==="function")return A.then(()=>B());return B()}_chainOrCallHooks(A,B){let Q=A,D=[];if(this._getCommandAndAncestors().reverse().filter((Z)=>Z._lifeCycleHooks[B]!==void 0).forEach((Z)=>{Z._lifeCycleHooks[B].forEach((G)=>{D.push({hookedCommand:Z,callback:G})})}),B==="postAction")D.reverse();return D.forEach((Z)=>{Q=this._chainOrCall(Q,()=>{return Z.callback(Z.hookedCommand,this)})}),Q}_chainOrCallSubCommandHook(A,B,Q){let D=A;if(this._lifeCycleHooks[Q]!==void 0)this._lifeCycleHooks[Q].forEach((Z)=>{D=this._chainOrCall(D,()=>{return Z(this,B)})});return D}_parseCommand(A,B){let Q=this.parseOptions(B);if(this._parseOptionsEnv(),this._parseOptionsImplied(),A=A.concat(Q.operands),B=Q.unknown,this.args=A.concat(B),A&&this._findCommand(A[0]))return this._dispatchSubcommand(A[0],A.slice(1),B);if(this._getHelpCommand()&&A[0]===this._getHelpCommand().name())return this._dispatchHelpCommand(A[1]);if(this._defaultCommandName)return this._outputHelpIfRequested(B),this._dispatchSubcommand(this._defaultCommandName,A,B);if(this.commands.length&&this.args.length===0&&!this._actionHandler&&!this._defaultCommandName)this.help({error:!0});this._outputHelpIfRequested(Q.unknown),this._checkForMissingMandatoryOptions(),this._checkForConflictingOptions();let D=()=>{if(Q.unknown.length>0)this.unknownOption(Q.unknown[0])},Z=`command:${this.name()}`;if(this._actionHandler){D(),this._processArguments();let G;if(G=this._chainOrCallHooks(G,"preAction"),G=this._chainOrCall(G,()=>this._actionHandler(this.processedArgs)),this.parent)G=this._chainOrCall(G,()=>{this.parent.emit(Z,A,B)});return G=this._chainOrCallHooks(G,"postAction"),G}if(this.parent&&this.parent.listenerCount(Z))D(),this._processArguments(),this.parent.emit(Z,A,B);else if(A.length){if(this._findCommand("*"))return this._dispatchSubcommand("*",A,B);if(this.listenerCount("command:*"))this.emit("command:*",A,B);else if(this.commands.length)this.unknownCommand();else D(),this._processArguments()}else if(this.commands.length)D(),this.help({error:!0});else D(),this._processArguments()}_findCommand(A){if(!A)return;return this.commands.find((B)=>B._name===A||B._aliases.includes(A))}_findOption(A){return this.options.find((B)=>B.is(A))}_checkForMissingMandatoryOptions(){this._getCommandAndAncestors().forEach((A)=>{A.options.forEach((B)=>{if(B.mandatory&&A.getOptionValue(B.attributeName())===void 0)A.missingMandatoryOptionValue(B)})})}_checkForConflictingLocalOptions(){let A=this.options.filter((Q)=>{let D=Q.attributeName();if(this.getOptionValue(D)===void 0)return!1;return this.getOptionValueSource(D)!=="default"});A.filter((Q)=>Q.conflictsWith.length>0).forEach((Q)=>{let D=A.find((Z)=>Q.conflictsWith.includes(Z.attributeName()));if(D)this._conflictingOption(Q,D)})}_checkForConflictingOptions(){this._getCommandAndAncestors().forEach((A)=>{A._checkForConflictingLocalOptions()})}parseOptions(A){let B=[],Q=[],D=B,Z=A.slice();function G(I){return I.length>1&&I[0]==="-"}let F=null;while(Z.length){let I=Z.shift();if(I==="--"){if(D===Q)D.push(I);D.push(...Z);break}if(F&&!G(I)){this.emit(`option:${F.name()}`,I);continue}if(F=null,G(I)){let Y=this._findOption(I);if(Y){if(Y.required){let W=Z.shift();if(W===void 0)this.optionMissingArgument(Y);this.emit(`option:${Y.name()}`,W)}else if(Y.optional){let W=null;if(Z.length>0&&!G(Z[0]))W=Z.shift();this.emit(`option:${Y.name()}`,W)}else this.emit(`option:${Y.name()}`);F=Y.variadic?Y:null;continue}}if(I.length>2&&I[0]==="-"&&I[1]!=="-"){let Y=this._findOption(`-${I[1]}`);if(Y){if(Y.required||Y.optional&&this._combineFlagAndOptionalValue)this.emit(`option:${Y.name()}`,I.slice(2));else this.emit(`option:${Y.name()}`),Z.unshift(`-${I.slice(2)}`);continue}}if(/^--[^=]+=/.test(I)){let Y=I.indexOf("="),W=this._findOption(I.slice(0,Y));if(W&&(W.required||W.optional)){this.emit(`option:${W.name()}`,I.slice(Y+1));continue}}if(G(I))D=Q;if((this._enablePositionalOptions||this._passThroughOptions)&&B.length===0&&Q.length===0){if(this._findCommand(I)){if(B.push(I),Z.length>0)Q.push(...Z);break}else if(this._getHelpCommand()&&I===this._getHelpCommand().name()){if(B.push(I),Z.length>0)B.push(...Z);break}else if(this._defaultCommandName){if(Q.push(I),Z.length>0)Q.push(...Z);break}}if(this._passThroughOptions){if(D.push(I),Z.length>0)D.push(...Z);break}D.push(I)}return{operands:B,unknown:Q}}opts(){if(this._storeOptionsAsProperties){let A={},B=this.options.length;for(let Q=0;Q<B;Q++){let D=this.options[Q].attributeName();A[D]=D===this._versionOptionName?this._version:this[D]}return A}return this._optionValues}optsWithGlobals(){return this._getCommandAndAncestors().reduce((A,B)=>Object.assign(A,B.opts()),{})}error(A,B){if(this._outputConfiguration.outputError(`${A}
`,this._outputConfiguration.writeErr),typeof this._showHelpAfterError==="string")this._outputConfiguration.writeErr(`${this._showHelpAfterError}
`);else if(this._showHelpAfterError)this._outputConfiguration.writeErr(`
`),this.outputHelp({error:!0});let Q=B||{},D=Q.exitCode||1,Z=Q.code||"commander.error";this._exit(D,Z,A)}_parseOptionsEnv(){this.options.forEach((A)=>{if(A.envVar&&A.envVar in PZ.env){let B=A.attributeName();if(this.getOptionValue(B)===void 0||["default","config","env"].includes(this.getOptionValueSource(B)))if(A.required||A.optional)this.emit(`optionEnv:${A.name()}`,PZ.env[A.envVar]);else this.emit(`optionEnv:${A.name()}`)}})}_parseOptionsImplied(){let A=new AM8(this.options),B=(Q)=>{return this.getOptionValue(Q)!==void 0&&!["default","implied"].includes(this.getOptionValueSource(Q))};this.options.filter((Q)=>Q.implied!==void 0&&B(Q.attributeName())&&A.valueFromOption(this.getOptionValue(Q.attributeName()),Q)).forEach((Q)=>{Object.keys(Q.implied).filter((D)=>!B(D)).forEach((D)=>{this.setOptionValueWithSource(D,Q.implied[D],"implied")})})}missingArgument(A){let B=`error: missing required argument '${A}'`;this.error(B,{code:"commander.missingArgument"})}optionMissingArgument(A){let B=`error: option '${A.flags}' argument missing`;this.error(B,{code:"commander.optionMissingArgument"})}missingMandatoryOptionValue(A){let B=`error: required option '${A.flags}' not specified`;this.error(B,{code:"commander.missingMandatoryOptionValue"})}_conflictingOption(A,B){let Q=(G)=>{let F=G.attributeName(),I=this.getOptionValue(F),Y=this.options.find((J)=>J.negate&&F===J.attributeName()),W=this.options.find((J)=>!J.negate&&F===J.attributeName());if(Y&&(Y.presetArg===void 0&&I===!1||Y.presetArg!==void 0&&I===Y.presetArg))return Y;return W||G},D=(G)=>{let F=Q(G),I=F.attributeName();if(this.getOptionValueSource(I)==="env")return`environment variable '${F.envVar}'`;return`option '${F.flags}'`},Z=`error: ${D(A)} cannot be used with ${D(B)}`;this.error(Z,{code:"commander.conflictingOption"})}unknownOption(A){if(this._allowUnknownOption)return;let B="";if(A.startsWith("--")&&this._showSuggestionAfterError){let D=[],Z=this;do{let G=Z.createHelp().visibleOptions(Z).filter((F)=>F.long).map((F)=>F.long);D=D.concat(G),Z=Z.parent}while(Z&&!Z._enablePositionalOptions);B=SdB(A,D)}let Q=`error: unknown option '${A}'${B}`;this.error(Q,{code:"commander.unknownOption"})}_excessArguments(A){if(this._allowExcessArguments)return;let B=this.registeredArguments.length,Q=B===1?"":"s",Z=`error: too many arguments${this.parent?` for '${this.name()}'`:""}. Expected ${B} argument${Q} but got ${A.length}.`;this.error(Z,{code:"commander.excessArguments"})}unknownCommand(){let A=this.args[0],B="";if(this._showSuggestionAfterError){let D=[];this.createHelp().visibleCommands(this).forEach((Z)=>{if(D.push(Z.name()),Z.alias())D.push(Z.alias())}),B=SdB(A,D)}let Q=`error: unknown command '${A}'${B}`;this.error(Q,{code:"commander.unknownCommand"})}version(A,B,Q){if(A===void 0)return this._version;this._version=A,B=B||"-V, --version",Q=Q||"output the version number";let D=this.createOption(B,Q);return this._versionOptionName=D.attributeName(),this._registerOption(D),this.on("option:"+D.name(),()=>{this._outputConfiguration.writeOut(`${A}
`),this._exit(0,"commander.version",A)}),this}description(A,B){if(A===void 0&&B===void 0)return this._description;if(this._description=A,B)this._argsDescription=B;return this}summary(A){if(A===void 0)return this._summary;return this._summary=A,this}alias(A){if(A===void 0)return this._aliases[0];let B=this;if(this.commands.length!==0&&this.commands[this.commands.length-1]._executableHandler)B=this.commands[this.commands.length-1];if(A===B._name)throw new Error("Command alias can't be the same as its name");let Q=this.parent?._findCommand(A);if(Q){let D=[Q.name()].concat(Q.aliases()).join("|");throw new Error(`cannot add alias '${A}' to command '${this.name()}' as already have command '${D}'`)}return B._aliases.push(A),this}aliases(A){if(A===void 0)return this._aliases;return A.forEach((B)=>this.alias(B)),this}usage(A){if(A===void 0){if(this._usage)return this._usage;let B=this.registeredArguments.map((Q)=>{return tL8(Q)});return[].concat(this.options.length||this._helpOption!==null?"[options]":[],this.commands.length?"[command]":[],this.registeredArguments.length?B:[]).join(" ")}return this._usage=A,this}name(A){if(A===void 0)return this._name;return this._name=A,this}nameFromFilename(A){return this._name=bS.basename(A,bS.extname(A)),this}executableDir(A){if(A===void 0)return this._executableDir;return this._executableDir=A,this}helpInformation(A){let B=this.createHelp();if(B.helpWidth===void 0)B.helpWidth=A&&A.error?this._outputConfiguration.getErrHelpWidth():this._outputConfiguration.getOutHelpWidth();return B.formatHelp(this,B)}_getHelpContext(A){A=A||{};let B={error:!!A.error},Q;if(B.error)Q=(D)=>this._outputConfiguration.writeErr(D);else Q=(D)=>this._outputConfiguration.writeOut(D);return B.write=A.write||Q,B.command=this,B}outputHelp(A){let B;if(typeof A==="function")B=A,A=void 0;let Q=this._getHelpContext(A);this._getCommandAndAncestors().reverse().forEach((Z)=>Z.emit("beforeAllHelp",Q)),this.emit("beforeHelp",Q);let D=this.helpInformation(Q);if(B){if(D=B(D),typeof D!=="string"&&!Buffer.isBuffer(D))throw new Error("outputHelp callback must return a string or a Buffer")}if(Q.write(D),this._getHelpOption()?.long)this.emit(this._getHelpOption().long);this.emit("afterHelp",Q),this._getCommandAndAncestors().forEach((Z)=>Z.emit("afterAllHelp",Q))}helpOption(A,B){if(typeof A==="boolean"){if(A)this._helpOption=this._helpOption??void 0;else this._helpOption=null;return this}return A=A??"-h, --help",B=B??"display help for command",this._helpOption=this.createOption(A,B),this}_getHelpOption(){if(this._helpOption===void 0)this.helpOption(void 0,void 0);return this._helpOption}addHelpOption(A){return this._helpOption=A,this}help(A){this.outputHelp(A);let B=PZ.exitCode||0;if(B===0&&A&&typeof A!=="function"&&A.error)B=1;this._exit(B,"commander.help","(outputHelp)")}addHelpText(A,B){let Q=["beforeAll","before","after","afterAll"];if(!Q.includes(A))throw new Error(`Unexpected value for position to addHelpText.
Expecting one of '${Q.join("', '")}'`);let D=`${A}Help`;return this.on(D,(Z)=>{let G;if(typeof B==="function")G=B({error:Z.error,command:Z.command});else G=B;if(G)Z.write(`${G}
`)}),this}_outputHelpIfRequested(A){let B=this._getHelpOption();if(B&&A.find((D)=>B.is(D)))this.outputHelp(),this._exit(0,"commander.helpDisplayed","(outputHelp)")}}function jdB(A){return A.map((B)=>{if(!B.startsWith("--inspect"))return B;let Q,D="127.0.0.1",Z="9229",G;if((G=B.match(/^(--inspect(-brk)?)$/))!==null)Q=G[1];else if((G=B.match(/^(--inspect(-brk|-port)?)=([^:]+)$/))!==null)if(Q=G[1],/^\d+$/.test(G[3]))Z=G[3];else D=G[3];else if((G=B.match(/^(--inspect(-brk|-port)?)=([^:]+):(\d+)$/))!==null)Q=G[1],D=G[3],Z=G[4];if(Q&&Z!=="0")return`${Q}=${D}:${parseInt(Z)+1}`;return B})}BM8.Command=oO0});

// Export all variables
module.exports = {
  CI1,
  TdB,
  Vg1,
  fdB,
  iO0,
  nO0,
  vdB,
  ydB
};
