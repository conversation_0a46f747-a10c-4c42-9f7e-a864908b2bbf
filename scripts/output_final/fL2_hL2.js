// Package extracted with entry point: hL2
// Contains 2 variables: fL2, hL2

var fL2=E((cR5,IO1)=>{IO1.exports=["SIGABRT","SIGALRM","SIGHUP","SIGINT","SIGTERM"];if(process.platform!=="win32")IO1.exports.push("SIGV<PERSON>LRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT");if(process.platform==="linux")IO1.exports.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT","SIGUNUSED")});
var hL2=E((lR5,zo)=>{var RD=global.process,Yu=function(A){return A&&typeof A==="object"&&typeof A.removeListener==="function"&&typeof A.emit==="function"&&typeof A.reallyExit==="function"&&typeof A.listeners==="function"&&typeof A.kill==="function"&&typeof A.pid==="number"&&typeof A.on==="function"};if(!Yu(RD))zo.exports=function(){return function(){}};else{if(BF0=J1("assert"),Wu=fL2(),QF0=/^win/i.test(RD.platform),Ho=J1("events"),typeof Ho!=="function")Ho=Ho.EventEmitter;if(RD.__signal_exit_emitter__)jF=RD.__signal_exit_emitter__;else jF=RD.__signal_exit_emitter__=new Ho,jF.count=0,jF.emitted={};if(!jF.infinite)jF.setMaxListeners(1/0),jF.infinite=!0;zo.exports=function(A,B){if(!Yu(global.process))return function(){};if(BF0.equal(typeof A,"function","a callback must be provided for exit handler"),Ju===!1)YO1();var Q="exit";if(B&&B.alwaysLast)Q="afterexit";var D=function(){if(jF.removeListener(Q,A),jF.listeners("exit").length===0&&jF.listeners("afterexit").length===0)k51()};return jF.on(Q,A),D},k51=function A(){if(!Ju||!Yu(global.process))return;Ju=!1,Wu.forEach(function(B){try{RD.removeListener(B,_51[B])}catch(Q){}}),RD.emit=x51,RD.reallyExit=WO1,jF.count-=1},zo.exports.unload=k51,j_=function A(B,Q,D){if(jF.emitted[B])return;jF.emitted[B]=!0,jF.emit(B,Q,D)},_51={},Wu.forEach(function(A){_51[A]=function B(){if(!Yu(global.process))return;var Q=RD.listeners(A);if(Q.length===jF.count){if(k51(),j_("exit",null,A),j_("afterexit",null,A),QF0&&A==="SIGHUP")A="SIGINT";RD.kill(RD.pid,A)}}}),zo.exports.signals=function(){return Wu},Ju=!1,YO1=function A(){if(Ju||!Yu(global.process))return;Ju=!0,jF.count+=1,Wu=Wu.filter(function(B){try{return RD.on(B,_51[B]),!0}catch(Q){return!1}}),RD.emit=ZF0,RD.reallyExit=DF0},zo.exports.load=YO1,WO1=RD.reallyExit,DF0=function A(B){if(!Yu(global.process))return;RD.exitCode=B||0,j_("exit",RD.exitCode,null),j_("afterexit",RD.exitCode,null),WO1.call(RD,RD.exitCode)},x51=RD.emit,ZF0=function A(B,Q){if(B==="exit"&&Yu(global.process)){if(Q!==void 0)RD.exitCode=Q;var D=x51.apply(this,arguments);return j_("exit",RD.exitCode,null),j_("afterexit",RD.exitCode,null),D}else return x51.apply(this,arguments)}}var BF0,Wu,QF0,Ho,jF,k51,j_,_51,Ju,YO1,WO1,DF0,x51,ZF0});

// Export all variables
module.exports = {
  fL2,
  hL2
};
