// Package extracted with entry point: jSA
// Contains 179 variables: $EA, $e1, $wA, ANA, AQ1, At1, BCA, BVA, Bn, BqA... (and 169 more)

var $EA=E((UZ5,wEA)=>{class UEA{constructor(A){this.tagname=A,this.child=[],this[":@"]={}}add(A,B){if(A==="__proto__")A="#__proto__";this.child.push({[A]:B})}addChild(A){if(A.tagname==="__proto__")A.tagname="#__proto__";if(A[":@"]&&Object.keys(A[":@"]).length>0)this.child.push({[A.tagname]:A.child,[":@"]:A[":@"]});else this.child.push({[A.tagname]:A.child})}}wEA.exports=UEA});
var $e1=E((vG5,XqA)=>{var{defineProperty:BE1,getOwnPropertyDescriptor:EqQ,getOwnPropertyNames:UqQ}=Object,wqQ=Object.prototype.hasOwnProperty,kh=(A,B)=>BE1(A,"name",{value:B,configurable:!0}),$qQ=(A,B)=>{for(var Q in B)BE1(A,Q,{get:B[Q],enumerable:!0})},qqQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of UqQ(B))if(!wqQ.call(A,Z)&&Z!==Q)BE1(A,Z,{get:()=>B[Z],enumerable:!(D=EqQ(B,Z))||D.enumerable})}return A},NqQ=(A)=>qqQ(BE1({},"__esModule",{value:!0}),A),YqA={};$qQ(YqA,{isBrowserNetworkError:()=>JqA,isClockSkewCorrectedError:()=>WqA,isClockSkewError:()=>jqQ,isRetryableByTrait:()=>SqQ,isServerError:()=>kqQ,isThrottlingError:()=>yqQ,isTransientError:()=>we1});XqA.exports=NqQ(YqA);var LqQ=["AuthFailure","InvalidSignatureException","RequestExpired","RequestInTheFuture","RequestTimeTooSkewed","SignatureDoesNotMatch"],MqQ=["BandwidthLimitExceeded","EC2ThrottledException","LimitExceededException","PriorRequestNotComplete","ProvisionedThroughputExceededException","RequestLimitExceeded","RequestThrottled","RequestThrottledException","SlowDown","ThrottledException","Throttling","ThrottlingException","TooManyRequestsException","TransactionInProgressException"],RqQ=["TimeoutError","RequestTimeout","RequestTimeoutException"],OqQ=[500,502,503,504],TqQ=["ECONNRESET","ECONNREFUSED","EPIPE","ETIMEDOUT"],PqQ=["EHOSTUNREACH","ENETUNREACH","ENOTFOUND"],SqQ=kh((A)=>A.$retryable!==void 0,"isRetryableByTrait"),jqQ=kh((A)=>LqQ.includes(A.name),"isClockSkewError"),WqA=kh((A)=>A.$metadata?.clockSkewCorrected,"isClockSkewCorrectedError"),JqA=kh((A)=>{let B=new Set(["Failed to fetch","NetworkError when attempting to fetch resource","The Internet connection appears to be offline","Load failed","Network request failed"]);if(!(A&&A instanceof TypeError))return!1;return B.has(A.message)},"isBrowserNetworkError"),yqQ=kh((A)=>A.$metadata?.httpStatusCode===429||MqQ.includes(A.name)||A.$retryable?.throttling==!0,"isThrottlingError"),we1=kh((A,B=0)=>WqA(A)||RqQ.includes(A.name)||TqQ.includes(A?.code||"")||PqQ.includes(A?.code||"")||OqQ.includes(A.$metadata?.httpStatusCode||0)||JqA(A)||A.cause!==void 0&&B<=10&&we1(A.cause,B+1),"isTransientError"),kqQ=kh((A)=>{if(A.$metadata?.httpStatusCode!==void 0){let B=A.$metadata.httpStatusCode;if(500<=B&&B<=599&&!we1(A))return!0;return!1}return!1},"isServerError")});
var $wA=E((GG5,wwA)=>{var{defineProperty:uz1,getOwnPropertyDescriptor:ZUQ,getOwnPropertyNames:GUQ}=Object,FUQ=Object.prototype.hasOwnProperty,mz1=(A,B)=>uz1(A,"name",{value:B,configurable:!0}),IUQ=(A,B)=>{for(var Q in B)uz1(A,Q,{get:B[Q],enumerable:!0})},YUQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of GUQ(B))if(!FUQ.call(A,Z)&&Z!==Q)uz1(A,Z,{get:()=>B[Z],enumerable:!(D=ZUQ(B,Z))||D.enumerable})}return A},WUQ=(A)=>YUQ(uz1({},"__esModule",{value:!0}),A),XwA={};IUQ(XwA,{AlgorithmId:()=>HwA,EndpointURLScheme:()=>KwA,FieldPosition:()=>zwA,HttpApiKeyAuthLocation:()=>CwA,HttpAuthLocation:()=>VwA,IniSectionType:()=>EwA,RequestHandlerProtocol:()=>UwA,SMITHY_CONTEXT_KEY:()=>KUQ,getDefaultClientConfiguration:()=>VUQ,resolveDefaultRuntimeConfig:()=>CUQ});wwA.exports=WUQ(XwA);var VwA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(VwA||{}),CwA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(CwA||{}),KwA=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(KwA||{}),HwA=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(HwA||{}),JUQ=mz1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),XUQ=mz1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),VUQ=mz1((A)=>{return JUQ(A)},"getDefaultClientConfiguration"),CUQ=mz1((A)=>{return XUQ(A)},"resolveDefaultRuntimeConfig"),zwA=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(zwA||{}),KUQ="__smithy_context",EwA=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(EwA||{}),UwA=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(UwA||{})});
var ANA=E((tG5,BLQ)=>{BLQ.exports={name:"@aws-sdk/client-sts",description:"AWS SDK for JavaScript Sts Client for Node.js, Browser and React Native",version:"3.840.0",scripts:{build:"concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'","build:cjs":"node ../../scripts/compilation/inline client-sts","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"rimraf ./dist-types tsconfig.types.tsbuildinfo && tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4",clean:"rimraf ./dist-* && rimraf *.tsbuildinfo","extract:docs":"api-extractor run --local","generate:client":"node ../../scripts/generate-clients/single-service --solo sts",test:"yarn g:vitest run","test:watch":"yarn g:vitest watch"},main:"./dist-cjs/index.js",types:"./dist-types/index.d.ts",module:"./dist-es/index.js",sideEffects:!1,dependencies:{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.840.0","@aws-sdk/credential-provider-node":"3.840.0","@aws-sdk/middleware-host-header":"3.840.0","@aws-sdk/middleware-logger":"3.840.0","@aws-sdk/middleware-recursion-detection":"3.840.0","@aws-sdk/middleware-user-agent":"3.840.0","@aws-sdk/region-config-resolver":"3.840.0","@aws-sdk/types":"3.840.0","@aws-sdk/util-endpoints":"3.840.0","@aws-sdk/util-user-agent-browser":"3.840.0","@aws-sdk/util-user-agent-node":"3.840.0","@smithy/config-resolver":"^4.1.4","@smithy/core":"^3.6.0","@smithy/fetch-http-handler":"^5.0.4","@smithy/hash-node":"^4.0.4","@smithy/invalid-dependency":"^4.0.4","@smithy/middleware-content-length":"^4.0.4","@smithy/middleware-endpoint":"^4.1.13","@smithy/middleware-retry":"^4.1.14","@smithy/middleware-serde":"^4.0.8","@smithy/middleware-stack":"^4.0.4","@smithy/node-config-provider":"^4.1.3","@smithy/node-http-handler":"^4.0.6","@smithy/protocol-http":"^5.1.2","@smithy/smithy-client":"^4.4.5","@smithy/types":"^4.3.1","@smithy/url-parser":"^4.0.4","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.21","@smithy/util-defaults-mode-node":"^4.0.21","@smithy/util-endpoints":"^3.0.6","@smithy/util-middleware":"^4.0.4","@smithy/util-retry":"^4.0.6","@smithy/util-utf8":"^4.0.0",tslib:"^2.6.2"},devDependencies:{"@tsconfig/node18":"18.2.4","@types/node":"^18.19.69",concurrently:"7.0.0","downlevel-dts":"0.10.1",rimraf:"3.0.2",typescript:"~5.8.3"},engines:{node:">=18.0.0"},typesVersions:{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},files:["dist-*/**"],author:{name:"AWS SDK for JavaScript Team",url:"https://aws.amazon.com/javascript/"},license:"Apache-2.0",browser:{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.browser"},"react-native":{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.native"},homepage:"https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-sts",repository:{type:"git",url:"https://github.com/aws/aws-sdk-js-v3.git",directory:"clients/client-sts"}}});
var AQ1=E((V75,GJA)=>{var{defineProperty:KH1,getOwnPropertyDescriptor:kDQ,getOwnPropertyNames:_DQ}=Object,xDQ=Object.prototype.hasOwnProperty,CH1=(A,B)=>KH1(A,"name",{value:B,configurable:!0}),vDQ=(A,B)=>{for(var Q in B)KH1(A,Q,{get:B[Q],enumerable:!0})},bDQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of _DQ(B))if(!xDQ.call(A,Z)&&Z!==Q)KH1(A,Z,{get:()=>B[Z],enumerable:!(D=kDQ(B,Z))||D.enumerable})}return A},fDQ=(A)=>bDQ(KH1({},"__esModule",{value:!0}),A),QJA={};vDQ(QJA,{addRecursionDetectionMiddlewareOptions:()=>ZJA,getRecursionDetectionPlugin:()=>mDQ,recursionDetectionMiddleware:()=>DJA});GJA.exports=fDQ(QJA);var hDQ=sJ(),zo1="X-Amzn-Trace-Id",gDQ="AWS_LAMBDA_FUNCTION_NAME",uDQ="_X_AMZN_TRACE_ID",DJA=CH1((A)=>(B)=>async(Q)=>{let{request:D}=Q;if(!hDQ.HttpRequest.isInstance(D)||A.runtime!=="node")return B(Q);let Z=Object.keys(D.headers??{}).find((Y)=>Y.toLowerCase()===zo1.toLowerCase())??zo1;if(D.headers.hasOwnProperty(Z))return B(Q);let G=process.env[gDQ],F=process.env[uDQ],I=CH1((Y)=>typeof Y==="string"&&Y.length>0,"nonEmptyString");if(I(G)&&I(F))D.headers[zo1]=F;return B({...Q,request:D})},"recursionDetectionMiddleware"),ZJA={step:"build",tags:["RECURSION_DETECTION"],name:"recursionDetectionMiddleware",override:!0,priority:"low"},mDQ=CH1((A)=>({applyToStack:CH1((B)=>{B.add(DJA(A),ZJA)},"applyToStack")}),"getRecursionDetectionPlugin")});
var At1=E((OD5,aH1)=>{var{defineProperty:JKA,getOwnPropertyDescriptor:FJQ,getOwnPropertyNames:IJQ}=Object,YJQ=Object.prototype.hasOwnProperty,to1=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of IJQ(B))if(!YJQ.call(A,Z)&&Z!==Q)JKA(A,Z,{get:()=>B[Z],enumerable:!(D=FJQ(B,Z))||D.enumerable})}return A},XKA=(A,B,Q)=>(to1(A,B,"default"),Q&&to1(Q,B,"default")),WJQ=(A)=>to1(JKA({},"__esModule",{value:!0}),A),eo1={};aH1.exports=WJQ(eo1);XKA(eo1,FKA(),aH1.exports);XKA(eo1,WKA(),aH1.exports)});
var BCA=E((BD5,ACA)=>{var{defineProperty:hH1,getOwnPropertyDescriptor:bIQ,getOwnPropertyNames:fIQ}=Object,hIQ=Object.prototype.hasOwnProperty,ny=(A,B)=>hH1(A,"name",{value:B,configurable:!0}),gIQ=(A,B)=>{for(var Q in B)hH1(A,Q,{get:B[Q],enumerable:!0})},uIQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of fIQ(B))if(!hIQ.call(A,Z)&&Z!==Q)hH1(A,Z,{get:()=>B[Z],enumerable:!(D=bIQ(B,Z))||D.enumerable})}return A},mIQ=(A)=>uIQ(hH1({},"__esModule",{value:!0}),A),rVA={};gIQ(rVA,{Field:()=>lIQ,Fields:()=>pIQ,HttpRequest:()=>iIQ,HttpResponse:()=>nIQ,IHttpRequest:()=>oVA.HttpRequest,getHttpHandlerExtensionConfiguration:()=>dIQ,isValidHostname:()=>eVA,resolveHttpHandlerRuntimeConfig:()=>cIQ});ACA.exports=mIQ(rVA);var dIQ=ny((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),cIQ=ny((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),oVA=sVA(),lIQ=class{static{ny(this,"Field")}constructor({name:A,kind:B=oVA.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},pIQ=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{ny(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},iIQ=class A{static{ny(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=tVA(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function tVA(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}ny(tVA,"cloneQuery");var nIQ=class{static{ny(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function eVA(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}ny(eVA,"isValidHostname")});
var BVA=E((eXA)=>{Object.defineProperty(eXA,"__esModule",{value:!0});eXA.headStream=void 0;var zFQ=J1("stream"),EFQ=oXA(),UFQ=py(),wFQ=(A,B)=>{if(UFQ.isReadableStream(A))return EFQ.headStream(A,B);return new Promise((Q,D)=>{let Z=new tXA;Z.limit=B,A.pipe(Z),A.on("error",(G)=>{Z.end(),D(G)}),Z.on("error",D),Z.on("finish",function(){let G=new Uint8Array(Buffer.concat(this.buffers));Q(G)})})};eXA.headStream=wFQ;class tXA extends zFQ.Writable{constructor(){super(...arguments);this.buffers=[],this.limit=1/0,this.bytesBuffered=0}_write(A,B,Q){var D;if(this.buffers.push(A),this.bytesBuffered+=(D=A.byteLength)!==null&&D!==void 0?D:0,this.bytesBuffered>=this.limit){let Z=this.bytesBuffered-this.limit,G=this.buffers[this.buffers.length-1];this.buffers[this.buffers.length-1]=G.subarray(0,G.byteLength-Z),this.emit("finish")}Q()}}});
var Bn=E((pD5,XHA)=>{var{defineProperty:Zz1,getOwnPropertyDescriptor:vXQ,getOwnPropertyNames:bXQ}=Object,fXQ=Object.prototype.hasOwnProperty,An=(A,B)=>Zz1(A,"name",{value:B,configurable:!0}),hXQ=(A,B)=>{for(var Q in B)Zz1(A,Q,{get:B[Q],enumerable:!0})},gXQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of bXQ(B))if(!fXQ.call(A,Z)&&Z!==Q)Zz1(A,Z,{get:()=>B[Z],enumerable:!(D=vXQ(B,Z))||D.enumerable})}return A},uXQ=(A)=>gXQ(Zz1({},"__esModule",{value:!0}),A),DHA={};hXQ(DHA,{ConditionObject:()=>e3.ConditionObject,DeprecatedObject:()=>e3.DeprecatedObject,EndpointError:()=>e3.EndpointError,EndpointObject:()=>e3.EndpointObject,EndpointObjectHeaders:()=>e3.EndpointObjectHeaders,EndpointObjectProperties:()=>e3.EndpointObjectProperties,EndpointParams:()=>e3.EndpointParams,EndpointResolverOptions:()=>e3.EndpointResolverOptions,EndpointRuleObject:()=>e3.EndpointRuleObject,ErrorRuleObject:()=>e3.ErrorRuleObject,EvaluateOptions:()=>e3.EvaluateOptions,Expression:()=>e3.Expression,FunctionArgv:()=>e3.FunctionArgv,FunctionObject:()=>e3.FunctionObject,FunctionReturn:()=>e3.FunctionReturn,ParameterObject:()=>e3.ParameterObject,ReferenceObject:()=>e3.ReferenceObject,ReferenceRecord:()=>e3.ReferenceRecord,RuleSetObject:()=>e3.RuleSetObject,RuleSetRules:()=>e3.RuleSetRules,TreeRuleObject:()=>e3.TreeRuleObject,awsEndpointFunctions:()=>JHA,getUserAgentPrefix:()=>lXQ,isIpAddress:()=>e3.isIpAddress,partition:()=>YHA,resolveEndpoint:()=>e3.resolveEndpoint,setPartitionInfo:()=>WHA,useDefaultPartitionInfo:()=>cXQ});XHA.exports=uXQ(DHA);var e3=S7(),ZHA=An((A,B=!1)=>{if(B){for(let Q of A.split("."))if(!ZHA(Q))return!1;return!0}if(!e3.isValidHostLabel(A))return!1;if(A.length<3||A.length>63)return!1;if(A!==A.toLowerCase())return!1;if(e3.isIpAddress(A))return!1;return!0},"isVirtualHostableS3Bucket"),QHA=":",mXQ="/",dXQ=An((A)=>{let B=A.split(QHA);if(B.length<6)return null;let[Q,D,Z,G,F,...I]=B;if(Q!=="arn"||D===""||Z===""||I.join(QHA)==="")return null;let Y=I.map((W)=>W.split(mXQ)).flat();return{partition:D,service:Z,region:G,accountId:F,resourceId:Y}},"parseArn"),GHA={partitions:[{id:"aws",outputs:{dnsSuffix:"amazonaws.com",dualStackDnsSuffix:"api.aws",implicitGlobalRegion:"us-east-1",name:"aws",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^(us|eu|ap|sa|ca|me|af|il|mx)\\-\\w+\\-\\d+$",regions:{"af-south-1":{description:"Africa (Cape Town)"},"ap-east-1":{description:"Asia Pacific (Hong Kong)"},"ap-east-2":{description:"Asia Pacific (Taipei)"},"ap-northeast-1":{description:"Asia Pacific (Tokyo)"},"ap-northeast-2":{description:"Asia Pacific (Seoul)"},"ap-northeast-3":{description:"Asia Pacific (Osaka)"},"ap-south-1":{description:"Asia Pacific (Mumbai)"},"ap-south-2":{description:"Asia Pacific (Hyderabad)"},"ap-southeast-1":{description:"Asia Pacific (Singapore)"},"ap-southeast-2":{description:"Asia Pacific (Sydney)"},"ap-southeast-3":{description:"Asia Pacific (Jakarta)"},"ap-southeast-4":{description:"Asia Pacific (Melbourne)"},"ap-southeast-5":{description:"Asia Pacific (Malaysia)"},"ap-southeast-7":{description:"Asia Pacific (Thailand)"},"aws-global":{description:"AWS Standard global region"},"ca-central-1":{description:"Canada (Central)"},"ca-west-1":{description:"Canada West (Calgary)"},"eu-central-1":{description:"Europe (Frankfurt)"},"eu-central-2":{description:"Europe (Zurich)"},"eu-north-1":{description:"Europe (Stockholm)"},"eu-south-1":{description:"Europe (Milan)"},"eu-south-2":{description:"Europe (Spain)"},"eu-west-1":{description:"Europe (Ireland)"},"eu-west-2":{description:"Europe (London)"},"eu-west-3":{description:"Europe (Paris)"},"il-central-1":{description:"Israel (Tel Aviv)"},"me-central-1":{description:"Middle East (UAE)"},"me-south-1":{description:"Middle East (Bahrain)"},"mx-central-1":{description:"Mexico (Central)"},"sa-east-1":{description:"South America (Sao Paulo)"},"us-east-1":{description:"US East (N. Virginia)"},"us-east-2":{description:"US East (Ohio)"},"us-west-1":{description:"US West (N. California)"},"us-west-2":{description:"US West (Oregon)"}}},{id:"aws-cn",outputs:{dnsSuffix:"amazonaws.com.cn",dualStackDnsSuffix:"api.amazonwebservices.com.cn",implicitGlobalRegion:"cn-northwest-1",name:"aws-cn",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^cn\\-\\w+\\-\\d+$",regions:{"aws-cn-global":{description:"AWS China global region"},"cn-north-1":{description:"China (Beijing)"},"cn-northwest-1":{description:"China (Ningxia)"}}},{id:"aws-us-gov",outputs:{dnsSuffix:"amazonaws.com",dualStackDnsSuffix:"api.aws",implicitGlobalRegion:"us-gov-west-1",name:"aws-us-gov",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^us\\-gov\\-\\w+\\-\\d+$",regions:{"aws-us-gov-global":{description:"AWS GovCloud (US) global region"},"us-gov-east-1":{description:"AWS GovCloud (US-East)"},"us-gov-west-1":{description:"AWS GovCloud (US-West)"}}},{id:"aws-iso",outputs:{dnsSuffix:"c2s.ic.gov",dualStackDnsSuffix:"c2s.ic.gov",implicitGlobalRegion:"us-iso-east-1",name:"aws-iso",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-iso\\-\\w+\\-\\d+$",regions:{"aws-iso-global":{description:"AWS ISO (US) global region"},"us-iso-east-1":{description:"US ISO East"},"us-iso-west-1":{description:"US ISO WEST"}}},{id:"aws-iso-b",outputs:{dnsSuffix:"sc2s.sgov.gov",dualStackDnsSuffix:"sc2s.sgov.gov",implicitGlobalRegion:"us-isob-east-1",name:"aws-iso-b",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-isob\\-\\w+\\-\\d+$",regions:{"aws-iso-b-global":{description:"AWS ISOB (US) global region"},"us-isob-east-1":{description:"US ISOB East (Ohio)"}}},{id:"aws-iso-e",outputs:{dnsSuffix:"cloud.adc-e.uk",dualStackDnsSuffix:"cloud.adc-e.uk",implicitGlobalRegion:"eu-isoe-west-1",name:"aws-iso-e",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^eu\\-isoe\\-\\w+\\-\\d+$",regions:{"aws-iso-e-global":{description:"AWS ISOE (Europe) global region"},"eu-isoe-west-1":{description:"EU ISOE West"}}},{id:"aws-iso-f",outputs:{dnsSuffix:"csp.hci.ic.gov",dualStackDnsSuffix:"csp.hci.ic.gov",implicitGlobalRegion:"us-isof-south-1",name:"aws-iso-f",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-isof\\-\\w+\\-\\d+$",regions:{"aws-iso-f-global":{description:"AWS ISOF global region"},"us-isof-east-1":{description:"US ISOF EAST"},"us-isof-south-1":{description:"US ISOF SOUTH"}}},{id:"aws-eusc",outputs:{dnsSuffix:"amazonaws.eu",dualStackDnsSuffix:"amazonaws.eu",implicitGlobalRegion:"eusc-de-east-1",name:"aws-eusc",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^eusc\\-(de)\\-\\w+\\-\\d+$",regions:{"eusc-de-east-1":{description:"EU (Germany)"}}}],version:"1.1"},FHA=GHA,IHA="",YHA=An((A)=>{let{partitions:B}=FHA;for(let D of B){let{regions:Z,outputs:G}=D;for(let[F,I]of Object.entries(Z))if(F===A)return{...G,...I}}for(let D of B){let{regionRegex:Z,outputs:G}=D;if(new RegExp(Z).test(A))return{...G}}let Q=B.find((D)=>D.id==="aws");if(!Q)throw new Error("Provided region was not found in the partition array or regex, and default partition with id 'aws' doesn't exist.");return{...Q.outputs}},"partition"),WHA=An((A,B="")=>{FHA=A,IHA=B},"setPartitionInfo"),cXQ=An(()=>{WHA(GHA,"")},"useDefaultPartitionInfo"),lXQ=An(()=>IHA,"getUserAgentPrefix"),JHA={isVirtualHostableS3Bucket:ZHA,parseArn:dXQ,partition:YHA};e3.customEndpointFunctions.aws=JHA});
var BqA=E((e$A)=>{Object.defineProperty(e$A,"__esModule",{value:!0});e$A.default=void 0;var e$Q=t$A(Ue1()),AqQ=t$A(o$A());function t$A(A){return A&&A.__esModule?A:{default:A}}var BqQ=e$Q.default("v5",80,AqQ.default),QqQ=BqQ;e$A.default=QqQ});
var C10=E((bF5,gRA)=>{var{defineProperty:OE1,getOwnPropertyDescriptor:CPQ,getOwnPropertyNames:_RA}=Object,KPQ=Object.prototype.hasOwnProperty,TE1=(A,B)=>OE1(A,"name",{value:B,configurable:!0}),HPQ=(A,B)=>function Q(){return A&&(B=A[_RA(A)[0]](A=0)),B},xRA=(A,B)=>{for(var Q in B)OE1(A,Q,{get:B[Q],enumerable:!0})},zPQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of _RA(B))if(!KPQ.call(A,Z)&&Z!==Q)OE1(A,Z,{get:()=>B[Z],enumerable:!(D=CPQ(B,Z))||D.enumerable})}return A},EPQ=(A)=>zPQ(OE1({},"__esModule",{value:!0}),A),vRA={};xRA(vRA,{GetRoleCredentialsCommand:()=>V10.GetRoleCredentialsCommand,SSOClient:()=>V10.SSOClient});var V10,UPQ=HPQ({"src/loadSso.ts"(){V10=HMA()}}),bRA={};xRA(bRA,{fromSSO:()=>$PQ,isSsoProfile:()=>fRA,validateSsoProfile:()=>hRA});gRA.exports=EPQ(bRA);var fRA=TE1((A)=>A&&(typeof A.sso_start_url==="string"||typeof A.sso_account_id==="string"||typeof A.sso_session==="string"||typeof A.sso_region==="string"||typeof A.sso_role_name==="string"),"isSsoProfile"),yRA=Zz(),wPQ=jRA(),Pw=Q9(),RE1=D3(),fQ1=!1,kRA=TE1(async({ssoStartUrl:A,ssoSession:B,ssoAccountId:Q,ssoRegion:D,ssoRoleName:Z,ssoClient:G,clientConfig:F,parentClientConfig:I,profile:Y,logger:W})=>{let J,X="To refresh this SSO session run aws sso login with the corresponding profile.";if(B)try{let f=await wPQ.fromSso({profile:Y})();J={accessToken:f.token,expiresAt:new Date(f.expiration).toISOString()}}catch(f){throw new Pw.CredentialsProviderError(f.message,{tryNextLink:fQ1,logger:W})}else try{J=await RE1.getSSOTokenFromFile(A)}catch(f){throw new Pw.CredentialsProviderError("The SSO session associated with this profile is invalid. To refresh this SSO session run aws sso login with the corresponding profile.",{tryNextLink:fQ1,logger:W})}if(new Date(J.expiresAt).getTime()-Date.now()<=0)throw new Pw.CredentialsProviderError("The SSO session associated with this profile has expired. To refresh this SSO session run aws sso login with the corresponding profile.",{tryNextLink:fQ1,logger:W});let{accessToken:V}=J,{SSOClient:C,GetRoleCredentialsCommand:K}=await Promise.resolve().then(()=>(UPQ(),vRA)),H=G||new C(Object.assign({},F??{},{logger:F?.logger??I?.logger,region:F?.region??D})),z;try{z=await H.send(new K({accountId:Q,roleName:Z,accessToken:V}))}catch(f){throw new Pw.CredentialsProviderError(f,{tryNextLink:fQ1,logger:W})}let{roleCredentials:{accessKeyId:$,secretAccessKey:L,sessionToken:N,expiration:O,credentialScope:R,accountId:T}={}}=z;if(!$||!L||!N||!O)throw new Pw.CredentialsProviderError("SSO returns an invalid temporary credential.",{tryNextLink:fQ1,logger:W});let j={accessKeyId:$,secretAccessKey:L,sessionToken:N,expiration:new Date(O),...R&&{credentialScope:R},...T&&{accountId:T}};if(B)yRA.setCredentialFeature(j,"CREDENTIALS_SSO","s");else yRA.setCredentialFeature(j,"CREDENTIALS_SSO_LEGACY","u");return j},"resolveSSOCredentials"),hRA=TE1((A,B)=>{let{sso_start_url:Q,sso_account_id:D,sso_region:Z,sso_role_name:G}=A;if(!Q||!D||!Z||!G)throw new Pw.CredentialsProviderError(`Profile is configured with invalid SSO credentials. Required parameters "sso_account_id", "sso_region", "sso_role_name", "sso_start_url". Got ${Object.keys(A).join(", ")}
Reference: https://docs.aws.amazon.com/cli/latest/userguide/cli-configure-sso.html`,{tryNextLink:!1,logger:B});return A},"validateSsoProfile"),$PQ=TE1((A={})=>async({callerClientConfig:B}={})=>{A.logger?.debug("@aws-sdk/credential-provider-sso - fromSSO");let{ssoStartUrl:Q,ssoAccountId:D,ssoRegion:Z,ssoRoleName:G,ssoSession:F}=A,{ssoClient:I}=A,Y=RE1.getProfileName({profile:A.profile??B?.profile});if(!Q&&!D&&!Z&&!G&&!F){let J=(await RE1.parseKnownFiles(A))[Y];if(!J)throw new Pw.CredentialsProviderError(`Profile ${Y} was not found.`,{logger:A.logger});if(!fRA(J))throw new Pw.CredentialsProviderError(`Profile ${Y} is not configured with SSO credentials.`,{logger:A.logger});if(J?.sso_session){let $=(await RE1.loadSsoSessionData(A))[J.sso_session],L=` configurations in profile ${Y} and sso-session ${J.sso_session}`;if(Z&&Z!==$.sso_region)throw new Pw.CredentialsProviderError("Conflicting SSO region"+L,{tryNextLink:!1,logger:A.logger});if(Q&&Q!==$.sso_start_url)throw new Pw.CredentialsProviderError("Conflicting SSO start_url"+L,{tryNextLink:!1,logger:A.logger});J.sso_region=$.sso_region,J.sso_start_url=$.sso_start_url}let{sso_start_url:X,sso_account_id:V,sso_region:C,sso_role_name:K,sso_session:H}=hRA(J,A.logger);return kRA({ssoStartUrl:X,ssoSession:H,ssoAccountId:V,ssoRegion:C,ssoRoleName:K,ssoClient:I,clientConfig:A.clientConfig,parentClientConfig:A.parentClientConfig,profile:Y})}else if(!Q||!D||!Z||!G)throw new Pw.CredentialsProviderError('Incomplete configuration. The fromSSO() argument hash must include "ssoStartUrl", "ssoAccountId", "ssoRegion", "ssoRoleName"',{tryNextLink:!1,logger:A.logger});else return kRA({ssoStartUrl:Q,ssoSession:F,ssoAccountId:D,ssoRegion:Z,ssoRoleName:G,ssoClient:I,clientConfig:A.clientConfig,parentClientConfig:A.parentClientConfig,profile:Y})},"fromSSO")});
var CB=E((bD5,bKA)=>{var{defineProperty:eH1,getOwnPropertyDescriptor:PJQ,getOwnPropertyNames:SJQ}=Object,jJQ=Object.prototype.hasOwnProperty,WD=(A,B)=>eH1(A,"name",{value:B,configurable:!0}),yJQ=(A,B)=>{for(var Q in B)eH1(A,Q,{get:B[Q],enumerable:!0})},kJQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of SJQ(B))if(!jJQ.call(A,Z)&&Z!==Q)eH1(A,Z,{get:()=>B[Z],enumerable:!(D=PJQ(B,Z))||D.enumerable})}return A},_JQ=(A)=>kJQ(eH1({},"__esModule",{value:!0}),A),MKA={};yJQ(MKA,{DefaultIdentityProviderConfig:()=>iJQ,EXPIRATION_MS:()=>xKA,HttpApiKeyAuthSigner:()=>nJQ,HttpBearerAuthSigner:()=>aJQ,NoAuthSigner:()=>sJQ,createIsIdentityExpiredFunction:()=>_KA,createPaginator:()=>yKA,doesIdentityRequireRefresh:()=>vKA,getHttpAuthSchemeEndpointRuleSetPlugin:()=>bJQ,getHttpAuthSchemePlugin:()=>hJQ,getHttpSigningPlugin:()=>mJQ,getSmithyContext:()=>xJQ,httpAuthSchemeEndpointRuleSetMiddlewareOptions:()=>TKA,httpAuthSchemeMiddleware:()=>Ft1,httpAuthSchemeMiddlewareOptions:()=>PKA,httpSigningMiddleware:()=>SKA,httpSigningMiddlewareOptions:()=>jKA,isIdentityExpired:()=>rJQ,memoizeIdentityProvider:()=>oJQ,normalizeProvider:()=>dJQ,requestBuilder:()=>pJQ.requestBuilder,setFeature:()=>kKA});bKA.exports=_JQ(MKA);var tH1=Eo1(),xJQ=WD((A)=>A[tH1.SMITHY_CONTEXT_KEY]||(A[tH1.SMITHY_CONTEXT_KEY]={}),"getSmithyContext"),RKA=J5(),vJQ=WD((A,B)=>{if(!B||B.length===0)return A;let Q=[];for(let D of B)for(let Z of A)if(Z.schemeId.split("#")[1]===D)Q.push(Z);for(let D of A)if(!Q.find(({schemeId:Z})=>Z===D.schemeId))Q.push(D);return Q},"resolveAuthOptions");function OKA(A){let B=new Map;for(let Q of A)B.set(Q.schemeId,Q);return B}WD(OKA,"convertHttpAuthSchemesToMap");var Ft1=WD((A,B)=>(Q,D)=>async(Z)=>{let G=A.httpAuthSchemeProvider(await B.httpAuthSchemeParametersProvider(A,D,Z.input)),F=A.authSchemePreference?await A.authSchemePreference():[],I=vJQ(G,F),Y=OKA(A.httpAuthSchemes),W=RKA.getSmithyContext(D),J=[];for(let X of I){let V=Y.get(X.schemeId);if(!V){J.push(`HttpAuthScheme \`${X.schemeId}\` was not enabled for this service.`);continue}let C=V.identityProvider(await B.identityProviderConfigProvider(A));if(!C){J.push(`HttpAuthScheme \`${X.schemeId}\` did not have an IdentityProvider configured.`);continue}let{identityProperties:K={},signingProperties:H={}}=X.propertiesExtractor?.(A,D)||{};X.identityProperties=Object.assign(X.identityProperties||{},K),X.signingProperties=Object.assign(X.signingProperties||{},H),W.selectedHttpAuthScheme={httpAuthOption:X,identity:await C(X.identityProperties),signer:V.signer};break}if(!W.selectedHttpAuthScheme)throw new Error(J.join(`
`));return Q(Z)},"httpAuthSchemeMiddleware"),TKA={step:"serialize",tags:["HTTP_AUTH_SCHEME"],name:"httpAuthSchemeMiddleware",override:!0,relation:"before",toMiddleware:"endpointV2Middleware"},bJQ=WD((A,{httpAuthSchemeParametersProvider:B,identityProviderConfigProvider:Q})=>({applyToStack:(D)=>{D.addRelativeTo(Ft1(A,{httpAuthSchemeParametersProvider:B,identityProviderConfigProvider:Q}),TKA)}}),"getHttpAuthSchemeEndpointRuleSetPlugin"),fJQ=j3(),PKA={step:"serialize",tags:["HTTP_AUTH_SCHEME"],name:"httpAuthSchemeMiddleware",override:!0,relation:"before",toMiddleware:fJQ.serializerMiddlewareOption.name},hJQ=WD((A,{httpAuthSchemeParametersProvider:B,identityProviderConfigProvider:Q})=>({applyToStack:(D)=>{D.addRelativeTo(Ft1(A,{httpAuthSchemeParametersProvider:B,identityProviderConfigProvider:Q}),PKA)}}),"getHttpAuthSchemePlugin"),It1=qh(),gJQ=WD((A)=>(B)=>{throw B},"defaultErrorHandler"),uJQ=WD((A,B)=>{},"defaultSuccessHandler"),SKA=WD((A)=>(B,Q)=>async(D)=>{if(!It1.HttpRequest.isInstance(D.request))return B(D);let G=RKA.getSmithyContext(Q).selectedHttpAuthScheme;if(!G)throw new Error("No HttpAuthScheme was selected: unable to sign request");let{httpAuthOption:{signingProperties:F={}},identity:I,signer:Y}=G,W=await B({...D,request:await Y.sign(D.request,I,F)}).catch((Y.errorHandler||gJQ)(F));return(Y.successHandler||uJQ)(W.response,F),W},"httpSigningMiddleware"),jKA={step:"finalizeRequest",tags:["HTTP_SIGNING"],name:"httpSigningMiddleware",aliases:["apiKeyMiddleware","tokenMiddleware","awsAuthMiddleware"],override:!0,relation:"after",toMiddleware:"retryMiddleware"},mJQ=WD((A)=>({applyToStack:(B)=>{B.addRelativeTo(SKA(A),jKA)}}),"getHttpSigningPlugin"),dJQ=WD((A)=>{if(typeof A==="function")return A;let B=Promise.resolve(A);return()=>B},"normalizeProvider"),cJQ=WD(async(A,B,Q,D=(G)=>G,...Z)=>{let G=new A(Q);return G=D(G)??G,await B.send(G,...Z)},"makePagedClientRequest");function yKA(A,B,Q,D,Z){return WD(async function*G(F,I,...Y){let W=I,J=F.startingToken??W[Q],X=!0,V;while(X){if(W[Q]=J,Z)W[Z]=W[Z]??F.pageSize;if(F.client instanceof A)V=await cJQ(B,F.client,I,F.withCommand,...Y);else throw new Error(`Invalid client, expected instance of ${A.name}`);yield V;let C=J;J=lJQ(V,D),X=!!(J&&(!F.stopOnSameToken||J!==C))}return},"paginateOperation")}WD(yKA,"createPaginator");var lJQ=WD((A,B)=>{let Q=A,D=B.split(".");for(let Z of D){if(!Q||typeof Q!=="object")return;Q=Q[Z]}return Q},"get"),pJQ=M6();function kKA(A,B,Q){if(!A.__smithy_context)A.__smithy_context={features:{}};else if(!A.__smithy_context.features)A.__smithy_context.features={};A.__smithy_context.features[B]=Q}WD(kKA,"setFeature");var iJQ=class{constructor(A){this.authSchemes=new Map;for(let[B,Q]of Object.entries(A))if(Q!==void 0)this.authSchemes.set(B,Q)}static{WD(this,"DefaultIdentityProviderConfig")}getIdentityProvider(A){return this.authSchemes.get(A)}},nJQ=class{static{WD(this,"HttpApiKeyAuthSigner")}async sign(A,B,Q){if(!Q)throw new Error("request could not be signed with `apiKey` since the `name` and `in` signer properties are missing");if(!Q.name)throw new Error("request could not be signed with `apiKey` since the `name` signer property is missing");if(!Q.in)throw new Error("request could not be signed with `apiKey` since the `in` signer property is missing");if(!B.apiKey)throw new Error("request could not be signed with `apiKey` since the `apiKey` is not defined");let D=It1.HttpRequest.clone(A);if(Q.in===tH1.HttpApiKeyAuthLocation.QUERY)D.query[Q.name]=B.apiKey;else if(Q.in===tH1.HttpApiKeyAuthLocation.HEADER)D.headers[Q.name]=Q.scheme?`${Q.scheme} ${B.apiKey}`:B.apiKey;else throw new Error("request can only be signed with `apiKey` locations `query` or `header`, but found: `"+Q.in+"`");return D}},aJQ=class{static{WD(this,"HttpBearerAuthSigner")}async sign(A,B,Q){let D=It1.HttpRequest.clone(A);if(!B.token)throw new Error("request could not be signed with `token` since the `token` is not defined");return D.headers.Authorization=`Bearer ${B.token}`,D}},sJQ=class{static{WD(this,"NoAuthSigner")}async sign(A,B,Q){return A}},_KA=WD((A)=>(B)=>vKA(B)&&B.expiration.getTime()-Date.now()<A,"createIsIdentityExpiredFunction"),xKA=300000,rJQ=_KA(xKA),vKA=WD((A)=>A.expiration!==void 0,"doesIdentityRequireRefresh"),oJQ=WD((A,B,Q)=>{if(A===void 0)return;let D=typeof A!=="function"?async()=>Promise.resolve(A):A,Z,G,F,I=!1,Y=WD(async(W)=>{if(!G)G=D(W);try{Z=await G,F=!0,I=!1}finally{G=void 0}return Z},"coalesceProvider");if(B===void 0)return async(W)=>{if(!F||W?.forceRefresh)Z=await Y(W);return Z};return async(W)=>{if(!F||W?.forceRefresh)Z=await Y(W);if(I)return Z;if(!Q(Z))return I=!0,Z;if(B(Z))return await Y(W),Z;return Z}},"memoizeIdentityProvider")});
var Ce1=E((H$A)=>{Object.defineProperty(H$A,"__esModule",{value:!0});H$A.default=F$Q;var Z$Q=G$Q(J1("crypto"));function G$Q(A){return A&&A.__esModule?A:{default:A}}var AE1=new Uint8Array(256),ez1=AE1.length;function F$Q(){if(ez1>AE1.length-16)Z$Q.default.randomFillSync(AE1),ez1=0;return AE1.slice(ez1,ez1+=16)}});
var D00=E((iN)=>{var TjQ=iN&&iN.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),PjQ=iN&&iN.__setModuleDefault||(Object.create?function(A,B){Object.defineProperty(A,"default",{enumerable:!0,value:B})}:function(A,B){A.default=B}),SjQ=iN&&iN.__importStar||function(){var A=function(B){return A=Object.getOwnPropertyNames||function(Q){var D=[];for(var Z in Q)if(Object.prototype.hasOwnProperty.call(Q,Z))D[D.length]=Z;return D},A(B)};return function(B){if(B&&B.__esModule)return B;var Q={};if(B!=null){for(var D=A(B),Z=0;Z<D.length;Z++)if(D[Z]!=="default")TjQ(Q,B,D[Z])}return PjQ(Q,B),Q}}();Object.defineProperty(iN,"__esModule",{value:!0});iN.fromWebToken=void 0;var jjQ=(A)=>async(B)=>{A.logger?.debug("@aws-sdk/credential-provider-web-identity - fromWebToken");let{roleArn:Q,roleSessionName:D,webIdentityToken:Z,providerId:G,policyArns:F,policy:I,durationSeconds:Y}=A,{roleAssumerWithWebIdentity:W}=A;if(!W){let{getDefaultRoleAssumerWithWebIdentity:J}=await Promise.resolve().then(()=>SjQ(e10()));W=J({...A.clientConfig,credentialProviderLogger:A.logger,parentClientConfig:{...B?.callerClientConfig,...A.parentClientConfig}},A.clientPlugins)}return W({RoleArn:Q,RoleSessionName:D??`aws-sdk-js-session-${Date.now()}`,WebIdentityToken:Z,ProviderId:G,PolicyArns:F,Policy:I,DurationSeconds:Y})};iN.fromWebToken=jjQ});
var D10=E((zMA)=>{Object.defineProperty(zMA,"__esModule",{value:!0});zMA.resolveHttpAuthSchemeConfig=zMA.defaultSSOOIDCHttpAuthSchemeProvider=zMA.defaultSSOOIDCHttpAuthSchemeParametersProvider=void 0;var xOQ=WI(),Q10=J5(),vOQ=async(A,B,Q)=>{return{operation:Q10.getSmithyContext(B).operation,region:await Q10.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};zMA.defaultSSOOIDCHttpAuthSchemeParametersProvider=vOQ;function bOQ(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"sso-oauth",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function fOQ(A){return{schemeId:"smithy.api#noAuth"}}var hOQ=(A)=>{let B=[];switch(A.operation){case"CreateToken":{B.push(fOQ(A));break}default:B.push(bOQ(A))}return B};zMA.defaultSSOOIDCHttpAuthSchemeProvider=hOQ;var gOQ=(A)=>{let B=xOQ.resolveAwsSdkSigV4Config(A);return Object.assign(B,{authSchemePreference:Q10.normalizeProvider(A.authSchemePreference??[])})};zMA.resolveHttpAuthSchemeConfig=gOQ});
var D3=E((IG5,qQ1)=>{var{defineProperty:lz1,getOwnPropertyDescriptor:UUQ,getOwnPropertyNames:wUQ}=Object,$UQ=Object.prototype.hasOwnProperty,Fz=(A,B)=>lz1(A,"name",{value:B,configurable:!0}),qUQ=(A,B)=>{for(var Q in B)lz1(A,Q,{get:B[Q],enumerable:!0})},Fe1=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of wUQ(B))if(!$UQ.call(A,Z)&&Z!==Q)lz1(A,Z,{get:()=>B[Z],enumerable:!(D=UUQ(B,Z))||D.enumerable})}return A},Ye1=(A,B,Q)=>(Fe1(A,B,"default"),Q&&Fe1(Q,B,"default")),NUQ=(A)=>Fe1(lz1({},"__esModule",{value:!0}),A),$Q1={};qUQ($Q1,{CONFIG_PREFIX_SEPARATOR:()=>yh,DEFAULT_PROFILE:()=>OwA,ENV_PROFILE:()=>RwA,getProfileName:()=>LUQ,loadSharedConfigFiles:()=>PwA,loadSsoSessionData:()=>bUQ,parseKnownFiles:()=>hUQ});qQ1.exports=NUQ($Q1);Ye1($Q1,Yn(),qQ1.exports);var RwA="AWS_PROFILE",OwA="default",LUQ=Fz((A)=>A.profile||process.env[RwA]||OwA,"getProfileName");Ye1($Q1,De1(),qQ1.exports);Ye1($Q1,JwA(),qQ1.exports);var dz1=$wA(),MUQ=Fz((A)=>Object.entries(A).filter(([B])=>{let Q=B.indexOf(yh);if(Q===-1)return!1;return Object.values(dz1.IniSectionType).includes(B.substring(0,Q))}).reduce((B,[Q,D])=>{let Z=Q.indexOf(yh),G=Q.substring(0,Z)===dz1.IniSectionType.PROFILE?Q.substring(Z+1):Q;return B[G]=D,B},{...A.default&&{default:A.default}}),"getConfigData"),cz1=J1("path"),RUQ=Yn(),OUQ="AWS_CONFIG_FILE",TwA=Fz(()=>process.env[OUQ]||cz1.join(RUQ.getHomeDir(),".aws","config"),"getConfigFilepath"),TUQ=Yn(),PUQ="AWS_SHARED_CREDENTIALS_FILE",SUQ=Fz(()=>process.env[PUQ]||cz1.join(TUQ.getHomeDir(),".aws","credentials"),"getCredentialsFilepath"),jUQ=Yn(),yUQ=/^([\w-]+)\s(["'])?([\w-@\+\.%:/]+)\2$/,kUQ=["__proto__","profile __proto__"],Ie1=Fz((A)=>{let B={},Q,D;for(let Z of A.split(/\r?\n/)){let G=Z.split(/(^|\s)[;#]/)[0].trim();if(G[0]==="["&&G[G.length-1]==="]"){Q=void 0,D=void 0;let I=G.substring(1,G.length-1),Y=yUQ.exec(I);if(Y){let[,W,,J]=Y;if(Object.values(dz1.IniSectionType).includes(W))Q=[W,J].join(yh)}else Q=I;if(kUQ.includes(I))throw new Error(`Found invalid profile name "${I}"`)}else if(Q){let I=G.indexOf("=");if(![0,-1].includes(I)){let[Y,W]=[G.substring(0,I).trim(),G.substring(I+1).trim()];if(W==="")D=Y;else{if(D&&Z.trimStart()===Z)D=void 0;B[Q]=B[Q]||{};let J=D?[D,Y].join(yh):Y;B[Q][J]=W}}}}return B},"parseIni"),LwA=Ge1(),MwA=Fz(()=>({}),"swallowError"),yh=".",PwA=Fz(async(A={})=>{let{filepath:B=SUQ(),configFilepath:Q=TwA()}=A,D=jUQ.getHomeDir(),Z="~/",G=B;if(B.startsWith("~/"))G=cz1.join(D,B.slice(2));let F=Q;if(Q.startsWith("~/"))F=cz1.join(D,Q.slice(2));let I=await Promise.all([LwA.slurpFile(F,{ignoreCache:A.ignoreCache}).then(Ie1).then(MUQ).catch(MwA),LwA.slurpFile(G,{ignoreCache:A.ignoreCache}).then(Ie1).catch(MwA)]);return{configFile:I[0],credentialsFile:I[1]}},"loadSharedConfigFiles"),_UQ=Fz((A)=>Object.entries(A).filter(([B])=>B.startsWith(dz1.IniSectionType.SSO_SESSION+yh)).reduce((B,[Q,D])=>({...B,[Q.substring(Q.indexOf(yh)+1)]:D}),{}),"getSsoSessionData"),xUQ=Ge1(),vUQ=Fz(()=>({}),"swallowError"),bUQ=Fz(async(A={})=>xUQ.slurpFile(A.configFilepath??TwA()).then(Ie1).then(_UQ).catch(vUQ),"loadSsoSessionData"),fUQ=Fz((...A)=>{let B={};for(let Q of A)for(let[D,Z]of Object.entries(Q))if(B[D]!==void 0)Object.assign(B[D],Z);else B[D]=Z;return B},"mergeConfigFiles"),hUQ=Fz(async(A)=>{let B=await PwA(A);return fUQ(B.configFile,B.credentialsFile)},"parseKnownFiles")});
var De1=E((FwA)=>{Object.defineProperty(FwA,"__esModule",{value:!0});FwA.getSSOTokenFilepath=void 0;var rEQ=J1("crypto"),oEQ=J1("path"),tEQ=Yn(),eEQ=(A)=>{let Q=rEQ.createHash("sha1").update(A).digest("hex");return oEQ.join(tEQ.getHomeDir(),".aws","sso","cache",`${Q}.json`)};FwA.getSSOTokenFilepath=eEQ});
var ECA=E((ID5,zCA)=>{var{defineProperty:dH1,getOwnPropertyDescriptor:WYQ,getOwnPropertyNames:JYQ}=Object,XYQ=Object.prototype.hasOwnProperty,bN=(A,B)=>dH1(A,"name",{value:B,configurable:!0}),VYQ=(A,B)=>{for(var Q in B)dH1(A,Q,{get:B[Q],enumerable:!0})},CYQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of JYQ(B))if(!XYQ.call(A,Z)&&Z!==Q)dH1(A,Z,{get:()=>B[Z],enumerable:!(D=WYQ(B,Z))||D.enumerable})}return A},KYQ=(A)=>CYQ(dH1({},"__esModule",{value:!0}),A),XCA={};VYQ(XCA,{FetchHttpHandler:()=>zYQ,keepAliveSupport:()=>mH1,streamCollector:()=>UYQ});zCA.exports=KYQ(XCA);var JCA=BCA(),HYQ=WCA();function ko1(A,B){return new Request(A,B)}bN(ko1,"createRequest");function VCA(A=0){return new Promise((B,Q)=>{if(A)setTimeout(()=>{let D=new Error(`Request did not complete within ${A} ms`);D.name="TimeoutError",Q(D)},A)})}bN(VCA,"requestTimeout");var mH1={supported:void 0},zYQ=class A{static{bN(this,"FetchHttpHandler")}static create(B){if(typeof B?.handle==="function")return B;return new A(B)}constructor(B){if(typeof B==="function")this.configProvider=B().then((Q)=>Q||{});else this.config=B??{},this.configProvider=Promise.resolve(this.config);if(mH1.supported===void 0)mH1.supported=Boolean(typeof Request!=="undefined"&&"keepalive"in ko1("https://[::1]"))}destroy(){}async handle(B,{abortSignal:Q}={}){if(!this.config)this.config=await this.configProvider;let D=this.config.requestTimeout,Z=this.config.keepAlive===!0,G=this.config.credentials;if(Q?.aborted){let $=new Error("Request aborted");return $.name="AbortError",Promise.reject($)}let F=B.path,I=HYQ.buildQueryString(B.query||{});if(I)F+=`?${I}`;if(B.fragment)F+=`#${B.fragment}`;let Y="";if(B.username!=null||B.password!=null){let $=B.username??"",L=B.password??"";Y=`${$}:${L}@`}let{port:W,method:J}=B,X=`${B.protocol}//${Y}${B.hostname}${W?`:${W}`:""}${F}`,V=J==="GET"||J==="HEAD"?void 0:B.body,C={body:V,headers:new Headers(B.headers),method:J,credentials:G};if(this.config?.cache)C.cache=this.config.cache;if(V)C.duplex="half";if(typeof AbortController!=="undefined")C.signal=Q;if(mH1.supported)C.keepalive=Z;if(typeof this.config.requestInit==="function")Object.assign(C,this.config.requestInit(B));let K=bN(()=>{},"removeSignalEventListener"),H=ko1(X,C),z=[fetch(H).then(($)=>{let L=$.headers,N={};for(let R of L.entries())N[R[0]]=R[1];if($.body==null)return $.blob().then((R)=>({response:new JCA.HttpResponse({headers:N,reason:$.statusText,statusCode:$.status,body:R})}));return{response:new JCA.HttpResponse({headers:N,reason:$.statusText,statusCode:$.status,body:$.body})}}),VCA(D)];if(Q)z.push(new Promise(($,L)=>{let N=bN(()=>{let O=new Error("Request aborted");O.name="AbortError",L(O)},"onAbort");if(typeof Q.addEventListener==="function"){let O=Q;O.addEventListener("abort",N,{once:!0}),K=bN(()=>O.removeEventListener("abort",N),"removeSignalEventListener")}else Q.onabort=N}));return Promise.race(z).finally(K)}updateHttpClientConfig(B,Q){this.config=void 0,this.configProvider=this.configProvider.then((D)=>{return D[B]=Q,D})}httpHandlerConfigs(){return this.config??{}}},EYQ=ii(),UYQ=bN(async(A)=>{if(typeof Blob==="function"&&A instanceof Blob||A.constructor?.name==="Blob"){if(Blob.prototype.arrayBuffer!==void 0)return new Uint8Array(await A.arrayBuffer());return CCA(A)}return KCA(A)},"streamCollector");async function CCA(A){let B=await HCA(A),Q=EYQ.fromBase64(B);return new Uint8Array(Q)}bN(CCA,"collectBlob");async function KCA(A){let B=[],Q=A.getReader(),D=!1,Z=0;while(!D){let{done:I,value:Y}=await Q.read();if(Y)B.push(Y),Z+=Y.length;D=I}let G=new Uint8Array(Z),F=0;for(let I of B)G.set(I,F),F+=I.length;return G}bN(KCA,"collectStream");function HCA(A){return new Promise((B,Q)=>{let D=new FileReader;D.onloadend=()=>{if(D.readyState!==2)return Q(new Error("Reader aborted too early"));let Z=D.result??"",G=Z.indexOf(","),F=G>-1?G+1:Z.length;B(Z.substring(F))},D.onabort=()=>Q(new Error("Read aborted")),D.onerror=()=>Q(D.error),D.readAsDataURL(A)})}bN(HCA,"readToBase64")});
var EEA=E((vKQ)=>{var zEA={preserveOrder:!1,attributeNamePrefix:"@_",attributesGroupName:!1,textNodeName:"#text",ignoreAttributes:!0,removeNSPrefix:!1,allowBooleanAttributes:!1,parseTagValue:!0,parseAttributeValue:!1,trimValues:!0,cdataPropName:!1,numberParseOptions:{hex:!0,leadingZeros:!0,eNotation:!0},tagValueProcessor:function(A,B){return B},attributeValueProcessor:function(A,B){return B},stopNodes:[],alwaysCreateTextNode:!1,isArray:()=>!1,commentPropName:!1,unpairedTags:[],processEntities:!0,htmlEntities:!1,ignoreDeclaration:!1,ignorePiTags:!1,transformTagName:!1,transformAttributeName:!1,updateTag:function(A,B,Q){return A}},xKQ=function(A){return Object.assign({},zEA,A)};vKQ.buildOptions=xKQ;vKQ.defaultOptions=zEA});
var ELA=E((HLA)=>{Object.defineProperty(HLA,"__esModule",{value:!0});HLA.defaultEndpointResolver=void 0;var DRQ=Bn(),te1=S7(),ZRQ=KLA(),GRQ=new te1.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),FRQ=(A,B={})=>{return GRQ.get(A,()=>te1.resolveEndpoint(ZRQ.ruleSet,{endpointParams:A,logger:B.logger}))};HLA.defaultEndpointResolver=FRQ;te1.customEndpointFunctions.aws=DRQ.awsEndpointFunctions});
var EOA=E((HOA)=>{Object.defineProperty(HOA,"__esModule",{value:!0});HOA.defaultEndpointResolver=void 0;var xPQ=Bn(),w10=S7(),vPQ=KOA(),bPQ=new w10.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS","UseGlobalEndpoint"]}),fPQ=(A,B={})=>{return bPQ.get(A,()=>w10.resolveEndpoint(vPQ.ruleSet,{endpointParams:A,logger:B.logger}))};HOA.defaultEndpointResolver=fPQ;w10.customEndpointFunctions.aws=xPQ.awsEndpointFunctions});
var EVA=E((l75,zVA)=>{var{defineProperty:_H1,getOwnPropertyDescriptor:yFQ,getOwnPropertyNames:kFQ}=Object,_FQ=Object.prototype.hasOwnProperty,iy=(A,B)=>_H1(A,"name",{value:B,configurable:!0}),xFQ=(A,B)=>{for(var Q in B)_H1(A,Q,{get:B[Q],enumerable:!0})},vFQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of kFQ(B))if(!_FQ.call(A,Z)&&Z!==Q)_H1(A,Z,{get:()=>B[Z],enumerable:!(D=yFQ(B,Z))||D.enumerable})}return A},bFQ=(A)=>vFQ(_H1({},"__esModule",{value:!0}),A),VVA={};xFQ(VVA,{Field:()=>gFQ,Fields:()=>uFQ,HttpRequest:()=>mFQ,HttpResponse:()=>dFQ,IHttpRequest:()=>CVA.HttpRequest,getHttpHandlerExtensionConfiguration:()=>fFQ,isValidHostname:()=>HVA,resolveHttpHandlerRuntimeConfig:()=>hFQ});zVA.exports=bFQ(VVA);var fFQ=iy((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),hFQ=iy((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),CVA=XVA(),gFQ=class{static{iy(this,"Field")}constructor({name:A,kind:B=CVA.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},uFQ=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{iy(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},mFQ=class A{static{iy(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=KVA(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function KVA(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}iy(KVA,"cloneQuery");var dFQ=class{static{iy(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function HVA(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}iy(HVA,"isValidHostname")});
var Ee1=E((P$A)=>{Object.defineProperty(P$A,"__esModule",{value:!0});P$A.default=void 0;var L$Q=M$Q(LQ1());function M$Q(A){return A&&A.__esModule?A:{default:A}}function R$Q(A){if(!L$Q.default(A))throw TypeError("Invalid UUID");let B,Q=new Uint8Array(16);return Q[0]=(B=parseInt(A.slice(0,8),16))>>>24,Q[1]=B>>>16&255,Q[2]=B>>>8&255,Q[3]=B&255,Q[4]=(B=parseInt(A.slice(9,13),16))>>>8,Q[5]=B&255,Q[6]=(B=parseInt(A.slice(14,18),16))>>>8,Q[7]=B&255,Q[8]=(B=parseInt(A.slice(19,23),16))>>>8,Q[9]=B&255,Q[10]=(B=parseInt(A.slice(24,36),16))/1099511627776&255,Q[11]=B/4294967296&255,Q[12]=B>>>24&255,Q[13]=B>>>16&255,Q[14]=B>>>8&255,Q[15]=B&255,Q}var O$Q=R$Q;P$A.default=O$Q});
var Eo1=E((C75,KJA)=>{var{defineProperty:HH1,getOwnPropertyDescriptor:dDQ,getOwnPropertyNames:cDQ}=Object,lDQ=Object.prototype.hasOwnProperty,zH1=(A,B)=>HH1(A,"name",{value:B,configurable:!0}),pDQ=(A,B)=>{for(var Q in B)HH1(A,Q,{get:B[Q],enumerable:!0})},iDQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of cDQ(B))if(!lDQ.call(A,Z)&&Z!==Q)HH1(A,Z,{get:()=>B[Z],enumerable:!(D=dDQ(B,Z))||D.enumerable})}return A},nDQ=(A)=>iDQ(HH1({},"__esModule",{value:!0}),A),FJA={};pDQ(FJA,{AlgorithmId:()=>JJA,EndpointURLScheme:()=>WJA,FieldPosition:()=>XJA,HttpApiKeyAuthLocation:()=>YJA,HttpAuthLocation:()=>IJA,IniSectionType:()=>VJA,RequestHandlerProtocol:()=>CJA,SMITHY_CONTEXT_KEY:()=>tDQ,getDefaultClientConfiguration:()=>rDQ,resolveDefaultRuntimeConfig:()=>oDQ});KJA.exports=nDQ(FJA);var IJA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(IJA||{}),YJA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(YJA||{}),WJA=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(WJA||{}),JJA=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(JJA||{}),aDQ=zH1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),sDQ=zH1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),rDQ=zH1((A)=>{return aDQ(A)},"getDefaultClientConfiguration"),oDQ=zH1((A)=>{return sDQ(A)},"resolveDefaultRuntimeConfig"),XJA=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(XJA||{}),tDQ="__smithy_context",VJA=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(VJA||{}),CJA=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(CJA||{})});
var F00=E((AI5,yE1)=>{var{defineProperty:STA,getOwnPropertyDescriptor:hjQ,getOwnPropertyNames:gjQ}=Object,ujQ=Object.prototype.hasOwnProperty,Z00=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of gjQ(B))if(!ujQ.call(A,Z)&&Z!==Q)STA(A,Z,{get:()=>B[Z],enumerable:!(D=hjQ(B,Z))||D.enumerable})}return A},jTA=(A,B,Q)=>(Z00(A,B,"default"),Q&&Z00(Q,B,"default")),mjQ=(A)=>Z00(STA({},"__esModule",{value:!0}),A),G00={};yE1.exports=mjQ(G00);jTA(G00,PTA(),yE1.exports);jTA(G00,D00(),yE1.exports)});
var FKA=E((ZKA)=>{Object.defineProperty(ZKA,"__esModule",{value:!0});ZKA.fromBase64=void 0;var AJQ=YD(),BJQ=/^[A-Za-z0-9+/]*={0,2}$/,QJQ=(A)=>{if(A.length*3%4!==0)throw new TypeError("Incorrect padding on base64 string.");if(!BJQ.exec(A))throw new TypeError("Invalid base64 string.");let B=AJQ.fromString(A,"base64");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength)};ZKA.fromBase64=QJQ});
var GCA=E((GD5,ZCA)=>{var{defineProperty:gH1,getOwnPropertyDescriptor:aIQ,getOwnPropertyNames:sIQ}=Object,rIQ=Object.prototype.hasOwnProperty,jo1=(A,B)=>gH1(A,"name",{value:B,configurable:!0}),oIQ=(A,B)=>{for(var Q in B)gH1(A,Q,{get:B[Q],enumerable:!0})},tIQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of sIQ(B))if(!rIQ.call(A,Z)&&Z!==Q)gH1(A,Z,{get:()=>B[Z],enumerable:!(D=aIQ(B,Z))||D.enumerable})}return A},eIQ=(A)=>tIQ(gH1({},"__esModule",{value:!0}),A),QCA={};oIQ(QCA,{escapeUri:()=>DCA,escapeUriPath:()=>BYQ});ZCA.exports=eIQ(QCA);var DCA=jo1((A)=>encodeURIComponent(A).replace(/[!'()*]/g,AYQ),"escapeUri"),AYQ=jo1((A)=>`%${A.charCodeAt(0).toString(16).toUpperCase()}`,"hexEncode"),BYQ=jo1((A)=>A.split("/").map(DCA).join("/"),"escapeUriPath")});
var GXA=E((O75,ZXA)=>{var{defineProperty:OH1,getOwnPropertyDescriptor:FGQ,getOwnPropertyNames:IGQ}=Object,YGQ=Object.prototype.hasOwnProperty,WGQ=(A,B)=>OH1(A,"name",{value:B,configurable:!0}),JGQ=(A,B)=>{for(var Q in B)OH1(A,Q,{get:B[Q],enumerable:!0})},XGQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of IGQ(B))if(!YGQ.call(A,Z)&&Z!==Q)OH1(A,Z,{get:()=>B[Z],enumerable:!(D=FGQ(B,Z))||D.enumerable})}return A},VGQ=(A)=>XGQ(OH1({},"__esModule",{value:!0}),A),DXA={};JGQ(DXA,{isArrayBuffer:()=>CGQ});ZXA.exports=VGQ(DXA);var CGQ=WGQ((A)=>typeof ArrayBuffer==="function"&&A instanceof ArrayBuffer||Object.prototype.toString.call(A)==="[object ArrayBuffer]","isArrayBuffer")});
var Ge1=E((qwA)=>{Object.defineProperty(qwA,"__esModule",{value:!0});qwA.slurpFile=void 0;var HUQ=J1("fs"),{readFile:zUQ}=HUQ.promises,Ze1={},EUQ=(A,B)=>{if(!Ze1[A]||(B===null||B===void 0?void 0:B.ignoreCache))Ze1[A]=zUQ(A,"utf8");return Ze1[A]};qwA.slurpFile=EUQ});
var H10=E((uRA)=>{Object.defineProperty(uRA,"__esModule",{value:!0});uRA.resolveHttpAuthSchemeConfig=uRA.resolveStsAuthConfig=uRA.defaultSTSHttpAuthSchemeProvider=uRA.defaultSTSHttpAuthSchemeParametersProvider=void 0;var qPQ=WI(),K10=J5(),NPQ=hQ1(),LPQ=async(A,B,Q)=>{return{operation:K10.getSmithyContext(B).operation,region:await K10.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};uRA.defaultSTSHttpAuthSchemeParametersProvider=LPQ;function MPQ(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"sts",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function RPQ(A){return{schemeId:"smithy.api#noAuth"}}var OPQ=(A)=>{let B=[];switch(A.operation){case"AssumeRoleWithWebIdentity":{B.push(RPQ(A));break}default:B.push(MPQ(A))}return B};uRA.defaultSTSHttpAuthSchemeProvider=OPQ;var TPQ=(A)=>Object.assign(A,{stsClientCtor:NPQ.STSClient});uRA.resolveStsAuthConfig=TPQ;var PPQ=(A)=>{let B=uRA.resolveStsAuthConfig(A),Q=qPQ.resolveAwsSdkSigV4Config(B);return Object.assign(Q,{authSchemePreference:K10.normalizeProvider(A.authSchemePreference??[])})};uRA.resolveHttpAuthSchemeConfig=PPQ});
var HMA=E((UF5,KMA)=>{var{defineProperty:zE1,getOwnPropertyDescriptor:BOQ,getOwnPropertyNames:QOQ}=Object,DOQ=Object.prototype.hasOwnProperty,O6=(A,B)=>zE1(A,"name",{value:B,configurable:!0}),ZOQ=(A,B)=>{for(var Q in B)zE1(A,Q,{get:B[Q],enumerable:!0})},GOQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of QOQ(B))if(!DOQ.call(A,Z)&&Z!==Q)zE1(A,Z,{get:()=>B[Z],enumerable:!(D=BOQ(B,Z))||D.enumerable})}return A},FOQ=(A)=>GOQ(zE1({},"__esModule",{value:!0}),A),aLA={};ZOQ(aLA,{GetRoleCredentialsCommand:()=>XMA,GetRoleCredentialsRequestFilterSensitiveLog:()=>eLA,GetRoleCredentialsResponseFilterSensitiveLog:()=>BMA,InvalidRequestException:()=>sLA,ListAccountRolesCommand:()=>A10,ListAccountRolesRequestFilterSensitiveLog:()=>QMA,ListAccountsCommand:()=>B10,ListAccountsRequestFilterSensitiveLog:()=>DMA,LogoutCommand:()=>VMA,LogoutRequestFilterSensitiveLog:()=>ZMA,ResourceNotFoundException:()=>rLA,RoleCredentialsFilterSensitiveLog:()=>AMA,SSO:()=>CMA,SSOClient:()=>UE1,SSOServiceException:()=>Hn,TooManyRequestsException:()=>oLA,UnauthorizedException:()=>tLA,__Client:()=>MB.Client,paginateListAccountRoles:()=>kOQ,paginateListAccounts:()=>_OQ});KMA.exports=FOQ(aLA);var dLA=t91(),IOQ=e91(),YOQ=AQ1(),cLA=In(),WOQ=K4(),lO=CB(),JOQ=bG(),_Q1=R6(),lLA=u4(),pLA=ie1(),XOQ=O6((A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,defaultSigningName:"awsssoportal"})},"resolveClientEndpointParameters"),EE1={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}},VOQ=vLA(),iLA=yQ1(),nLA=sJ(),MB=g4(),COQ=O6((A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}},"getHttpAuthExtensionConfiguration"),KOQ=O6((A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}},"resolveHttpAuthRuntimeConfig"),HOQ=O6((A,B)=>{let Q=Object.assign(iLA.getAwsRegionExtensionConfiguration(A),MB.getDefaultExtensionConfiguration(A),nLA.getHttpHandlerExtensionConfiguration(A),COQ(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,iLA.resolveAwsRegionExtensionConfiguration(Q),MB.resolveDefaultRuntimeConfig(Q),nLA.resolveHttpHandlerRuntimeConfig(Q),KOQ(Q))},"resolveRuntimeExtensions"),UE1=class extends MB.Client{static{O6(this,"SSOClient")}config;constructor(...[A]){let B=VOQ.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=XOQ(B),D=cLA.resolveUserAgentConfig(Q),Z=lLA.resolveRetryConfig(D),G=WOQ.resolveRegionConfig(Z),F=dLA.resolveHostHeaderConfig(G),I=_Q1.resolveEndpointConfig(F),Y=pLA.resolveHttpAuthSchemeConfig(I),W=HOQ(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(cLA.getUserAgentPlugin(this.config)),this.middlewareStack.use(lLA.getRetryPlugin(this.config)),this.middlewareStack.use(JOQ.getContentLengthPlugin(this.config)),this.middlewareStack.use(dLA.getHostHeaderPlugin(this.config)),this.middlewareStack.use(IOQ.getLoggerPlugin(this.config)),this.middlewareStack.use(YOQ.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(lO.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:pLA.defaultSSOHttpAuthSchemeParametersProvider,identityProviderConfigProvider:O6(async(J)=>new lO.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials}),"identityProviderConfigProvider")})),this.middlewareStack.use(lO.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}},wE1=j3(),Hn=class A extends MB.ServiceException{static{O6(this,"SSOServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},sLA=class A extends Hn{static{O6(this,"InvalidRequestException")}name="InvalidRequestException";$fault="client";constructor(B){super({name:"InvalidRequestException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},rLA=class A extends Hn{static{O6(this,"ResourceNotFoundException")}name="ResourceNotFoundException";$fault="client";constructor(B){super({name:"ResourceNotFoundException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},oLA=class A extends Hn{static{O6(this,"TooManyRequestsException")}name="TooManyRequestsException";$fault="client";constructor(B){super({name:"TooManyRequestsException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},tLA=class A extends Hn{static{O6(this,"UnauthorizedException")}name="UnauthorizedException";$fault="client";constructor(B){super({name:"UnauthorizedException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},eLA=O6((A)=>({...A,...A.accessToken&&{accessToken:MB.SENSITIVE_STRING}}),"GetRoleCredentialsRequestFilterSensitiveLog"),AMA=O6((A)=>({...A,...A.secretAccessKey&&{secretAccessKey:MB.SENSITIVE_STRING},...A.sessionToken&&{sessionToken:MB.SENSITIVE_STRING}}),"RoleCredentialsFilterSensitiveLog"),BMA=O6((A)=>({...A,...A.roleCredentials&&{roleCredentials:AMA(A.roleCredentials)}}),"GetRoleCredentialsResponseFilterSensitiveLog"),QMA=O6((A)=>({...A,...A.accessToken&&{accessToken:MB.SENSITIVE_STRING}}),"ListAccountRolesRequestFilterSensitiveLog"),DMA=O6((A)=>({...A,...A.accessToken&&{accessToken:MB.SENSITIVE_STRING}}),"ListAccountsRequestFilterSensitiveLog"),ZMA=O6((A)=>({...A,...A.accessToken&&{accessToken:MB.SENSITIVE_STRING}}),"LogoutRequestFilterSensitiveLog"),kQ1=WI(),zOQ=O6(async(A,B)=>{let Q=lO.requestBuilder(A,B),D=MB.map({},MB.isSerializableHeaderValue,{[NE1]:A[qE1]});Q.bp("/federation/credentials");let Z=MB.map({[jOQ]:[,MB.expectNonNull(A[SOQ],"roleName")],[FMA]:[,MB.expectNonNull(A[GMA],"accountId")]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_GetRoleCredentialsCommand"),EOQ=O6(async(A,B)=>{let Q=lO.requestBuilder(A,B),D=MB.map({},MB.isSerializableHeaderValue,{[NE1]:A[qE1]});Q.bp("/assignment/roles");let Z=MB.map({[JMA]:[,A[WMA]],[YMA]:[()=>A.maxResults!==void 0,()=>A[IMA].toString()],[FMA]:[,MB.expectNonNull(A[GMA],"accountId")]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListAccountRolesCommand"),UOQ=O6(async(A,B)=>{let Q=lO.requestBuilder(A,B),D=MB.map({},MB.isSerializableHeaderValue,{[NE1]:A[qE1]});Q.bp("/assignment/accounts");let Z=MB.map({[JMA]:[,A[WMA]],[YMA]:[()=>A.maxResults!==void 0,()=>A[IMA].toString()]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListAccountsCommand"),wOQ=O6(async(A,B)=>{let Q=lO.requestBuilder(A,B),D=MB.map({},MB.isSerializableHeaderValue,{[NE1]:A[qE1]});Q.bp("/logout");let Z;return Q.m("POST").h(D).b(Z),Q.build()},"se_LogoutCommand"),$OQ=O6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return $E1(A,B);let Q=MB.map({$metadata:Fk(A)}),D=MB.expectNonNull(MB.expectObject(await kQ1.parseJsonBody(A.body,B)),"body"),Z=MB.take(D,{roleCredentials:MB._json});return Object.assign(Q,Z),Q},"de_GetRoleCredentialsCommand"),qOQ=O6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return $E1(A,B);let Q=MB.map({$metadata:Fk(A)}),D=MB.expectNonNull(MB.expectObject(await kQ1.parseJsonBody(A.body,B)),"body"),Z=MB.take(D,{nextToken:MB.expectString,roleList:MB._json});return Object.assign(Q,Z),Q},"de_ListAccountRolesCommand"),NOQ=O6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return $E1(A,B);let Q=MB.map({$metadata:Fk(A)}),D=MB.expectNonNull(MB.expectObject(await kQ1.parseJsonBody(A.body,B)),"body"),Z=MB.take(D,{accountList:MB._json,nextToken:MB.expectString});return Object.assign(Q,Z),Q},"de_ListAccountsCommand"),LOQ=O6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return $E1(A,B);let Q=MB.map({$metadata:Fk(A)});return await MB.collectBody(A.body,B),Q},"de_LogoutCommand"),$E1=O6(async(A,B)=>{let Q={...A,body:await kQ1.parseJsonErrorBody(A.body,B)},D=kQ1.loadRestJsonErrorCode(A,Q.body);switch(D){case"InvalidRequestException":case"com.amazonaws.sso#InvalidRequestException":throw await ROQ(Q,B);case"ResourceNotFoundException":case"com.amazonaws.sso#ResourceNotFoundException":throw await OOQ(Q,B);case"TooManyRequestsException":case"com.amazonaws.sso#TooManyRequestsException":throw await TOQ(Q,B);case"UnauthorizedException":case"com.amazonaws.sso#UnauthorizedException":throw await POQ(Q,B);default:let Z=Q.body;return MOQ({output:A,parsedBody:Z,errorCode:D})}},"de_CommandError"),MOQ=MB.withBaseException(Hn),ROQ=O6(async(A,B)=>{let Q=MB.map({}),D=A.body,Z=MB.take(D,{message:MB.expectString});Object.assign(Q,Z);let G=new sLA({$metadata:Fk(A),...Q});return MB.decorateServiceException(G,A.body)},"de_InvalidRequestExceptionRes"),OOQ=O6(async(A,B)=>{let Q=MB.map({}),D=A.body,Z=MB.take(D,{message:MB.expectString});Object.assign(Q,Z);let G=new rLA({$metadata:Fk(A),...Q});return MB.decorateServiceException(G,A.body)},"de_ResourceNotFoundExceptionRes"),TOQ=O6(async(A,B)=>{let Q=MB.map({}),D=A.body,Z=MB.take(D,{message:MB.expectString});Object.assign(Q,Z);let G=new oLA({$metadata:Fk(A),...Q});return MB.decorateServiceException(G,A.body)},"de_TooManyRequestsExceptionRes"),POQ=O6(async(A,B)=>{let Q=MB.map({}),D=A.body,Z=MB.take(D,{message:MB.expectString});Object.assign(Q,Z);let G=new tLA({$metadata:Fk(A),...Q});return MB.decorateServiceException(G,A.body)},"de_UnauthorizedExceptionRes"),Fk=O6((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),GMA="accountId",qE1="accessToken",FMA="account_id",IMA="maxResults",YMA="max_result",WMA="nextToken",JMA="next_token",SOQ="roleName",jOQ="role_name",NE1="x-amz-sso_bearer_token",XMA=class extends MB.Command.classBuilder().ep(EE1).m(function(A,B,Q,D){return[wE1.getSerdePlugin(Q,this.serialize,this.deserialize),_Q1.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","GetRoleCredentials",{}).n("SSOClient","GetRoleCredentialsCommand").f(eLA,BMA).ser(zOQ).de($OQ).build(){static{O6(this,"GetRoleCredentialsCommand")}},A10=class extends MB.Command.classBuilder().ep(EE1).m(function(A,B,Q,D){return[wE1.getSerdePlugin(Q,this.serialize,this.deserialize),_Q1.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","ListAccountRoles",{}).n("SSOClient","ListAccountRolesCommand").f(QMA,void 0).ser(EOQ).de(qOQ).build(){static{O6(this,"ListAccountRolesCommand")}},B10=class extends MB.Command.classBuilder().ep(EE1).m(function(A,B,Q,D){return[wE1.getSerdePlugin(Q,this.serialize,this.deserialize),_Q1.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","ListAccounts",{}).n("SSOClient","ListAccountsCommand").f(DMA,void 0).ser(UOQ).de(NOQ).build(){static{O6(this,"ListAccountsCommand")}},VMA=class extends MB.Command.classBuilder().ep(EE1).m(function(A,B,Q,D){return[wE1.getSerdePlugin(Q,this.serialize,this.deserialize),_Q1.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","Logout",{}).n("SSOClient","LogoutCommand").f(ZMA,void 0).ser(wOQ).de(LOQ).build(){static{O6(this,"LogoutCommand")}},yOQ={GetRoleCredentialsCommand:XMA,ListAccountRolesCommand:A10,ListAccountsCommand:B10,LogoutCommand:VMA},CMA=class extends UE1{static{O6(this,"SSO")}};MB.createAggregatedClient(yOQ,CMA);var kOQ=lO.createPaginator(UE1,A10,"nextToken","nextToken","maxResults"),_OQ=lO.createPaginator(UE1,B10,"nextToken","nextToken","maxResults")});
var HQ1=E((TZ5,iEA)=>{var{defineProperty:Tz1,getOwnPropertyDescriptor:_HQ,getOwnPropertyNames:xHQ}=Object,vHQ=Object.prototype.hasOwnProperty,Pz1=(A,B)=>Tz1(A,"name",{value:B,configurable:!0}),bHQ=(A,B)=>{for(var Q in B)Tz1(A,Q,{get:B[Q],enumerable:!0})},fHQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of xHQ(B))if(!vHQ.call(A,Z)&&Z!==Q)Tz1(A,Z,{get:()=>B[Z],enumerable:!(D=_HQ(B,Z))||D.enumerable})}return A},hHQ=(A)=>fHQ(Tz1({},"__esModule",{value:!0}),A),dEA={};bHQ(dEA,{XmlNode:()=>gHQ,XmlText:()=>pEA});iEA.exports=hHQ(dEA);function cEA(A){return A.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;")}Pz1(cEA,"escapeAttribute");function lEA(A){return A.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\r/g,"&#x0D;").replace(/\n/g,"&#x0A;").replace(/\u0085/g,"&#x85;").replace(/\u2028/,"&#x2028;")}Pz1(lEA,"escapeElement");var pEA=class{constructor(A){this.value=A}static{Pz1(this,"XmlText")}toString(){return lEA(""+this.value)}},gHQ=class A{constructor(B,Q=[]){this.name=B,this.children=Q}static{Pz1(this,"XmlNode")}attributes={};static of(B,Q,D){let Z=new A(B);if(Q!==void 0)Z.addChildNode(new pEA(Q));if(D!==void 0)Z.withName(D);return Z}withName(B){return this.name=B,this}addAttribute(B,Q){return this.attributes[B]=Q,this}addChildNode(B){return this.children.push(B),this}removeAttribute(B){return delete this.attributes[B],this}n(B){return this.name=B,this}c(B){return this.children.push(B),this}a(B,Q){if(Q!=null)this.attributes[B]=Q;return this}cc(B,Q,D=Q){if(B[Q]!=null){let Z=A.of(Q,B[Q]).withName(D);this.c(Z)}}l(B,Q,D,Z){if(B[Q]!=null)Z().map((F)=>{F.withName(D),this.c(F)})}lc(B,Q,D,Z){if(B[Q]!=null){let G=Z(),F=new A(D);G.map((I)=>{F.c(I)}),this.c(F)}}toString(){let B=Boolean(this.children.length),Q=`<${this.name}`,D=this.attributes;for(let Z of Object.keys(D)){let G=D[Z];if(G!=null)Q+=` ${Z}="${cEA(""+G)}"`}return Q+=!B?"/>":`>${this.children.map((Z)=>Z.toString()).join("")}</${this.name}>`}}});
var In=E((nZ5,$UA)=>{var{defineProperty:kz1,getOwnPropertyDescriptor:TzQ,getOwnPropertyNames:PzQ}=Object,SzQ=Object.prototype.hasOwnProperty,mO=(A,B)=>kz1(A,"name",{value:B,configurable:!0}),jzQ=(A,B)=>{for(var Q in B)kz1(A,Q,{get:B[Q],enumerable:!0})},yzQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of PzQ(B))if(!SzQ.call(A,Z)&&Z!==Q)kz1(A,Z,{get:()=>B[Z],enumerable:!(D=TzQ(B,Z))||D.enumerable})}return A},kzQ=(A)=>yzQ(kz1({},"__esModule",{value:!0}),A),VUA={};jzQ(VUA,{DEFAULT_UA_APP_ID:()=>CUA,getUserAgentMiddlewareOptions:()=>wUA,getUserAgentPlugin:()=>uzQ,resolveUserAgentConfig:()=>HUA,userAgentMiddleware:()=>UUA});$UA.exports=kzQ(VUA);var _zQ=CB(),CUA=void 0;function KUA(A){if(A===void 0)return!0;return typeof A==="string"&&A.length<=50}mO(KUA,"isValidUserAgentAppId");function HUA(A){let B=_zQ.normalizeProvider(A.userAgentAppId??CUA),{customUserAgent:Q}=A;return Object.assign(A,{customUserAgent:typeof Q==="string"?[[Q]]:Q,userAgentAppId:mO(async()=>{let D=await B();if(!KUA(D)){let Z=A.logger?.constructor?.name==="NoOpLogger"||!A.logger?console:A.logger;if(typeof D!=="string")Z?.warn("userAgentAppId must be a string or undefined.");else if(D.length>50)Z?.warn("The provided userAgentAppId exceeds the maximum length of 50 characters.")}return D},"userAgentAppId")})}mO(HUA,"resolveUserAgentConfig");var xzQ=Bn(),vzQ=sJ(),dN=WI(),bzQ=/\d{12}\.ddb/;async function zUA(A,B,Q){if(Q.request?.headers?.["smithy-protocol"]==="rpc-v2-cbor")dN.setFeature(A,"PROTOCOL_RPC_V2_CBOR","M");if(typeof B.retryStrategy==="function"){let G=await B.retryStrategy();if(typeof G.acquireInitialRetryToken==="function")if(G.constructor?.name?.includes("Adaptive"))dN.setFeature(A,"RETRY_MODE_ADAPTIVE","F");else dN.setFeature(A,"RETRY_MODE_STANDARD","E");else dN.setFeature(A,"RETRY_MODE_LEGACY","D")}if(typeof B.accountIdEndpointMode==="function"){let G=A.endpointV2;if(String(G?.url?.hostname).match(bzQ))dN.setFeature(A,"ACCOUNT_ID_ENDPOINT","O");switch(await B.accountIdEndpointMode?.()){case"disabled":dN.setFeature(A,"ACCOUNT_ID_MODE_DISABLED","Q");break;case"preferred":dN.setFeature(A,"ACCOUNT_ID_MODE_PREFERRED","P");break;case"required":dN.setFeature(A,"ACCOUNT_ID_MODE_REQUIRED","R");break}}let Z=A.__smithy_context?.selectedHttpAuthScheme?.identity;if(Z?.$source){let G=Z;if(G.accountId)dN.setFeature(A,"RESOLVED_ACCOUNT_ID","T");for(let[F,I]of Object.entries(G.$source??{}))dN.setFeature(A,F,I)}}mO(zUA,"checkFeatures");var WUA="user-agent",et1="x-amz-user-agent",JUA=" ",Ae1="/",fzQ=/[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w]/g,hzQ=/[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w\#]/g,XUA="-",gzQ=1024;function EUA(A){let B="";for(let Q in A){let D=A[Q];if(B.length+D.length+1<=gzQ){if(B.length)B+=","+D;else B+=D;continue}break}return B}mO(EUA,"encodeFeatures");var UUA=mO((A)=>(B,Q)=>async(D)=>{let{request:Z}=D;if(!vzQ.HttpRequest.isInstance(Z))return B(D);let{headers:G}=Z,F=Q?.userAgent?.map(yz1)||[],I=(await A.defaultUserAgentProvider()).map(yz1);await zUA(Q,A,D);let Y=Q;I.push(`m/${EUA(Object.assign({},Q.__smithy_context?.features,Y.__aws_sdk_context?.features))}`);let W=A?.customUserAgent?.map(yz1)||[],J=await A.userAgentAppId();if(J)I.push(yz1([`app/${J}`]));let X=xzQ.getUserAgentPrefix(),V=(X?[X]:[]).concat([...I,...F,...W]).join(JUA),C=[...I.filter((K)=>K.startsWith("aws-sdk-")),...W].join(JUA);if(A.runtime!=="browser"){if(C)G[et1]=G[et1]?`${G[WUA]} ${C}`:C;G[WUA]=V}else G[et1]=V;return B({...D,request:Z})},"userAgentMiddleware"),yz1=mO((A)=>{let B=A[0].split(Ae1).map((F)=>F.replace(fzQ,XUA)).join(Ae1),Q=A[1]?.replace(hzQ,XUA),D=B.indexOf(Ae1),Z=B.substring(0,D),G=B.substring(D+1);if(Z==="api")G=G.toLowerCase();return[Z,G,Q].filter((F)=>F&&F.length>0).reduce((F,I,Y)=>{switch(Y){case 0:return I;case 1:return`${F}/${I}`;default:return`${F}#${I}`}},"")},"escapeUserAgent"),wUA={name:"getUserAgentMiddleware",step:"build",priority:"low",tags:["SET_USER_AGENT","USER_AGENT"],override:!0},uzQ=mO((A)=>({applyToStack:mO((B)=>{B.add(UUA(A),wUA)},"applyToStack")}),"getUserAgentPlugin")});
var IqA=E((GqA)=>{Object.defineProperty(GqA,"__esModule",{value:!0});GqA.default=void 0;var ZqQ=GqQ(LQ1());function GqQ(A){return A&&A.__esModule?A:{default:A}}function FqQ(A){if(!ZqQ.default(A))throw TypeError("Invalid UUID");return parseInt(A.slice(14,15),16)}var IqQ=FqQ;GqA.default=IqQ});
var J10=E((yF5,NRA)=>{var{defineProperty:ME1,getOwnPropertyDescriptor:XTQ,getOwnPropertyNames:VTQ}=Object,CTQ=Object.prototype.hasOwnProperty,s4=(A,B)=>ME1(A,"name",{value:B,configurable:!0}),KTQ=(A,B)=>{for(var Q in B)ME1(A,Q,{get:B[Q],enumerable:!0})},HTQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of VTQ(B))if(!CTQ.call(A,Z)&&Z!==Q)ME1(A,Z,{get:()=>B[Z],enumerable:!(D=XTQ(B,Z))||D.enumerable})}return A},zTQ=(A)=>HTQ(ME1({},"__esModule",{value:!0}),A),DRA={};KTQ(DRA,{$Command:()=>FRA.Command,AccessDeniedException:()=>IRA,AuthorizationPendingException:()=>YRA,CreateTokenCommand:()=>$RA,CreateTokenRequestFilterSensitiveLog:()=>WRA,CreateTokenResponseFilterSensitiveLog:()=>JRA,ExpiredTokenException:()=>XRA,InternalServerException:()=>VRA,InvalidClientException:()=>CRA,InvalidGrantException:()=>KRA,InvalidRequestException:()=>HRA,InvalidScopeException:()=>zRA,SSOOIDC:()=>qRA,SSOOIDCClient:()=>GRA,SSOOIDCServiceException:()=>eC,SlowDownException:()=>ERA,UnauthorizedClientException:()=>URA,UnsupportedGrantTypeException:()=>wRA,__Client:()=>ZRA.Client});NRA.exports=zTQ(DRA);var rMA=t91(),ETQ=e91(),UTQ=AQ1(),oMA=In(),wTQ=K4(),Y10=CB(),$TQ=bG(),qTQ=R6(),tMA=u4(),ZRA=g4(),eMA=D10(),NTQ=s4((A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,defaultSigningName:"sso-oauth"})},"resolveClientEndpointParameters"),LTQ={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}},MTQ=sMA(),ARA=yQ1(),BRA=sJ(),QRA=g4(),RTQ=s4((A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}},"getHttpAuthExtensionConfiguration"),OTQ=s4((A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}},"resolveHttpAuthRuntimeConfig"),TTQ=s4((A,B)=>{let Q=Object.assign(ARA.getAwsRegionExtensionConfiguration(A),QRA.getDefaultExtensionConfiguration(A),BRA.getHttpHandlerExtensionConfiguration(A),RTQ(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,ARA.resolveAwsRegionExtensionConfiguration(Q),QRA.resolveDefaultRuntimeConfig(Q),BRA.resolveHttpHandlerRuntimeConfig(Q),OTQ(Q))},"resolveRuntimeExtensions"),GRA=class extends ZRA.Client{static{s4(this,"SSOOIDCClient")}config;constructor(...[A]){let B=MTQ.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=NTQ(B),D=oMA.resolveUserAgentConfig(Q),Z=tMA.resolveRetryConfig(D),G=wTQ.resolveRegionConfig(Z),F=rMA.resolveHostHeaderConfig(G),I=qTQ.resolveEndpointConfig(F),Y=eMA.resolveHttpAuthSchemeConfig(I),W=TTQ(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(oMA.getUserAgentPlugin(this.config)),this.middlewareStack.use(tMA.getRetryPlugin(this.config)),this.middlewareStack.use($TQ.getContentLengthPlugin(this.config)),this.middlewareStack.use(rMA.getHostHeaderPlugin(this.config)),this.middlewareStack.use(ETQ.getLoggerPlugin(this.config)),this.middlewareStack.use(UTQ.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(Y10.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:eMA.defaultSSOOIDCHttpAuthSchemeParametersProvider,identityProviderConfigProvider:s4(async(J)=>new Y10.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials}),"identityProviderConfigProvider")})),this.middlewareStack.use(Y10.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}},PTQ=g4(),STQ=R6(),jTQ=j3(),FRA=g4(),wn=g4(),yTQ=g4(),eC=class A extends yTQ.ServiceException{static{s4(this,"SSOOIDCServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},IRA=class A extends eC{static{s4(this,"AccessDeniedException")}name="AccessDeniedException";$fault="client";error;error_description;constructor(B){super({name:"AccessDeniedException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},YRA=class A extends eC{static{s4(this,"AuthorizationPendingException")}name="AuthorizationPendingException";$fault="client";error;error_description;constructor(B){super({name:"AuthorizationPendingException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},WRA=s4((A)=>({...A,...A.clientSecret&&{clientSecret:wn.SENSITIVE_STRING},...A.refreshToken&&{refreshToken:wn.SENSITIVE_STRING},...A.codeVerifier&&{codeVerifier:wn.SENSITIVE_STRING}}),"CreateTokenRequestFilterSensitiveLog"),JRA=s4((A)=>({...A,...A.accessToken&&{accessToken:wn.SENSITIVE_STRING},...A.refreshToken&&{refreshToken:wn.SENSITIVE_STRING},...A.idToken&&{idToken:wn.SENSITIVE_STRING}}),"CreateTokenResponseFilterSensitiveLog"),XRA=class A extends eC{static{s4(this,"ExpiredTokenException")}name="ExpiredTokenException";$fault="client";error;error_description;constructor(B){super({name:"ExpiredTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},VRA=class A extends eC{static{s4(this,"InternalServerException")}name="InternalServerException";$fault="server";error;error_description;constructor(B){super({name:"InternalServerException",$fault:"server",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},CRA=class A extends eC{static{s4(this,"InvalidClientException")}name="InvalidClientException";$fault="client";error;error_description;constructor(B){super({name:"InvalidClientException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},KRA=class A extends eC{static{s4(this,"InvalidGrantException")}name="InvalidGrantException";$fault="client";error;error_description;constructor(B){super({name:"InvalidGrantException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},HRA=class A extends eC{static{s4(this,"InvalidRequestException")}name="InvalidRequestException";$fault="client";error;error_description;constructor(B){super({name:"InvalidRequestException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},zRA=class A extends eC{static{s4(this,"InvalidScopeException")}name="InvalidScopeException";$fault="client";error;error_description;constructor(B){super({name:"InvalidScopeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},ERA=class A extends eC{static{s4(this,"SlowDownException")}name="SlowDownException";$fault="client";error;error_description;constructor(B){super({name:"SlowDownException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},URA=class A extends eC{static{s4(this,"UnauthorizedClientException")}name="UnauthorizedClientException";$fault="client";error;error_description;constructor(B){super({name:"UnauthorizedClientException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},wRA=class A extends eC{static{s4(this,"UnsupportedGrantTypeException")}name="UnsupportedGrantTypeException";$fault="client";error;error_description;constructor(B){super({name:"UnsupportedGrantTypeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},W10=WI(),kTQ=CB(),EB=g4(),_TQ=s4(async(A,B)=>{let Q=kTQ.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/token");let Z;return Z=JSON.stringify(EB.take(A,{clientId:[],clientSecret:[],code:[],codeVerifier:[],deviceCode:[],grantType:[],redirectUri:[],refreshToken:[],scope:s4((G)=>EB._json(G),"scope")})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateTokenCommand"),xTQ=s4(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return vTQ(A,B);let Q=EB.map({$metadata:Hz(A)}),D=EB.expectNonNull(EB.expectObject(await W10.parseJsonBody(A.body,B)),"body"),Z=EB.take(D,{accessToken:EB.expectString,expiresIn:EB.expectInt32,idToken:EB.expectString,refreshToken:EB.expectString,tokenType:EB.expectString});return Object.assign(Q,Z),Q},"de_CreateTokenCommand"),vTQ=s4(async(A,B)=>{let Q={...A,body:await W10.parseJsonErrorBody(A.body,B)},D=W10.loadRestJsonErrorCode(A,Q.body);switch(D){case"AccessDeniedException":case"com.amazonaws.ssooidc#AccessDeniedException":throw await fTQ(Q,B);case"AuthorizationPendingException":case"com.amazonaws.ssooidc#AuthorizationPendingException":throw await hTQ(Q,B);case"ExpiredTokenException":case"com.amazonaws.ssooidc#ExpiredTokenException":throw await gTQ(Q,B);case"InternalServerException":case"com.amazonaws.ssooidc#InternalServerException":throw await uTQ(Q,B);case"InvalidClientException":case"com.amazonaws.ssooidc#InvalidClientException":throw await mTQ(Q,B);case"InvalidGrantException":case"com.amazonaws.ssooidc#InvalidGrantException":throw await dTQ(Q,B);case"InvalidRequestException":case"com.amazonaws.ssooidc#InvalidRequestException":throw await cTQ(Q,B);case"InvalidScopeException":case"com.amazonaws.ssooidc#InvalidScopeException":throw await lTQ(Q,B);case"SlowDownException":case"com.amazonaws.ssooidc#SlowDownException":throw await pTQ(Q,B);case"UnauthorizedClientException":case"com.amazonaws.ssooidc#UnauthorizedClientException":throw await iTQ(Q,B);case"UnsupportedGrantTypeException":case"com.amazonaws.ssooidc#UnsupportedGrantTypeException":throw await nTQ(Q,B);default:let Z=Q.body;return bTQ({output:A,parsedBody:Z,errorCode:D})}},"de_CommandError"),bTQ=EB.withBaseException(eC),fTQ=s4(async(A,B)=>{let Q=EB.map({}),D=A.body,Z=EB.take(D,{error:EB.expectString,error_description:EB.expectString});Object.assign(Q,Z);let G=new IRA({$metadata:Hz(A),...Q});return EB.decorateServiceException(G,A.body)},"de_AccessDeniedExceptionRes"),hTQ=s4(async(A,B)=>{let Q=EB.map({}),D=A.body,Z=EB.take(D,{error:EB.expectString,error_description:EB.expectString});Object.assign(Q,Z);let G=new YRA({$metadata:Hz(A),...Q});return EB.decorateServiceException(G,A.body)},"de_AuthorizationPendingExceptionRes"),gTQ=s4(async(A,B)=>{let Q=EB.map({}),D=A.body,Z=EB.take(D,{error:EB.expectString,error_description:EB.expectString});Object.assign(Q,Z);let G=new XRA({$metadata:Hz(A),...Q});return EB.decorateServiceException(G,A.body)},"de_ExpiredTokenExceptionRes"),uTQ=s4(async(A,B)=>{let Q=EB.map({}),D=A.body,Z=EB.take(D,{error:EB.expectString,error_description:EB.expectString});Object.assign(Q,Z);let G=new VRA({$metadata:Hz(A),...Q});return EB.decorateServiceException(G,A.body)},"de_InternalServerExceptionRes"),mTQ=s4(async(A,B)=>{let Q=EB.map({}),D=A.body,Z=EB.take(D,{error:EB.expectString,error_description:EB.expectString});Object.assign(Q,Z);let G=new CRA({$metadata:Hz(A),...Q});return EB.decorateServiceException(G,A.body)},"de_InvalidClientExceptionRes"),dTQ=s4(async(A,B)=>{let Q=EB.map({}),D=A.body,Z=EB.take(D,{error:EB.expectString,error_description:EB.expectString});Object.assign(Q,Z);let G=new KRA({$metadata:Hz(A),...Q});return EB.decorateServiceException(G,A.body)},"de_InvalidGrantExceptionRes"),cTQ=s4(async(A,B)=>{let Q=EB.map({}),D=A.body,Z=EB.take(D,{error:EB.expectString,error_description:EB.expectString});Object.assign(Q,Z);let G=new HRA({$metadata:Hz(A),...Q});return EB.decorateServiceException(G,A.body)},"de_InvalidRequestExceptionRes"),lTQ=s4(async(A,B)=>{let Q=EB.map({}),D=A.body,Z=EB.take(D,{error:EB.expectString,error_description:EB.expectString});Object.assign(Q,Z);let G=new zRA({$metadata:Hz(A),...Q});return EB.decorateServiceException(G,A.body)},"de_InvalidScopeExceptionRes"),pTQ=s4(async(A,B)=>{let Q=EB.map({}),D=A.body,Z=EB.take(D,{error:EB.expectString,error_description:EB.expectString});Object.assign(Q,Z);let G=new ERA({$metadata:Hz(A),...Q});return EB.decorateServiceException(G,A.body)},"de_SlowDownExceptionRes"),iTQ=s4(async(A,B)=>{let Q=EB.map({}),D=A.body,Z=EB.take(D,{error:EB.expectString,error_description:EB.expectString});Object.assign(Q,Z);let G=new URA({$metadata:Hz(A),...Q});return EB.decorateServiceException(G,A.body)},"de_UnauthorizedClientExceptionRes"),nTQ=s4(async(A,B)=>{let Q=EB.map({}),D=A.body,Z=EB.take(D,{error:EB.expectString,error_description:EB.expectString});Object.assign(Q,Z);let G=new wRA({$metadata:Hz(A),...Q});return EB.decorateServiceException(G,A.body)},"de_UnsupportedGrantTypeExceptionRes"),Hz=s4((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),$RA=class extends FRA.Command.classBuilder().ep(LTQ).m(function(A,B,Q,D){return[jTQ.getSerdePlugin(Q,this.serialize,this.deserialize),STQ.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSSOOIDCService","CreateToken",{}).n("SSOOIDCClient","CreateTokenCommand").f(WRA,JRA).ser(_TQ).de(xTQ).build(){static{s4(this,"CreateTokenCommand")}},aTQ={CreateTokenCommand:$RA},qRA=class extends GRA{static{s4(this,"SSOOIDC")}};PTQ.createAggregatedClient(aTQ,qRA)});
var J5=E((H75,PJA)=>{var{defineProperty:wH1,getOwnPropertyDescriptor:JZQ,getOwnPropertyNames:XZQ}=Object,VZQ=Object.prototype.hasOwnProperty,OJA=(A,B)=>wH1(A,"name",{value:B,configurable:!0}),CZQ=(A,B)=>{for(var Q in B)wH1(A,Q,{get:B[Q],enumerable:!0})},KZQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of XZQ(B))if(!VZQ.call(A,Z)&&Z!==Q)wH1(A,Z,{get:()=>B[Z],enumerable:!(D=JZQ(B,Z))||D.enumerable})}return A},HZQ=(A)=>KZQ(wH1({},"__esModule",{value:!0}),A),TJA={};CZQ(TJA,{getSmithyContext:()=>zZQ,normalizeProvider:()=>EZQ});PJA.exports=HZQ(TJA);var RJA=MJA(),zZQ=OJA((A)=>A[RJA.SMITHY_CONTEXT_KEY]||(A[RJA.SMITHY_CONTEXT_KEY]={}),"getSmithyContext"),EZQ=OJA((A)=>{if(typeof A==="function")return A;let B=Promise.resolve(A);return()=>B},"normalizeProvider")});
var JD=E((YG5,ywA)=>{var{defineProperty:pz1,getOwnPropertyDescriptor:gUQ,getOwnPropertyNames:uUQ}=Object,mUQ=Object.prototype.hasOwnProperty,Wn=(A,B)=>pz1(A,"name",{value:B,configurable:!0}),dUQ=(A,B)=>{for(var Q in B)pz1(A,Q,{get:B[Q],enumerable:!0})},cUQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of uUQ(B))if(!mUQ.call(A,Z)&&Z!==Q)pz1(A,Z,{get:()=>B[Z],enumerable:!(D=gUQ(B,Z))||D.enumerable})}return A},lUQ=(A)=>cUQ(pz1({},"__esModule",{value:!0}),A),jwA={};dUQ(jwA,{loadConfig:()=>sUQ});ywA.exports=lUQ(jwA);var NQ1=Q9();function We1(A){try{let B=new Set(Array.from(A.match(/([A-Z_]){3,}/g)??[]));return B.delete("CONFIG"),B.delete("CONFIG_PREFIX_SEPARATOR"),B.delete("ENV"),[...B].join(", ")}catch(B){return A}}Wn(We1,"getSelectorName");var pUQ=Wn((A,B)=>async()=>{try{let Q=A(process.env,B);if(Q===void 0)throw new Error;return Q}catch(Q){throw new NQ1.CredentialsProviderError(Q.message||`Not found in ENV: ${We1(A.toString())}`,{logger:B?.logger})}},"fromEnv"),SwA=D3(),iUQ=Wn((A,{preferredFile:B="config",...Q}={})=>async()=>{let D=SwA.getProfileName(Q),{configFile:Z,credentialsFile:G}=await SwA.loadSharedConfigFiles(Q),F=G[D]||{},I=Z[D]||{},Y=B==="config"?{...F,...I}:{...I,...F};try{let J=A(Y,B==="config"?Z:G);if(J===void 0)throw new Error;return J}catch(W){throw new NQ1.CredentialsProviderError(W.message||`Not found in config files w/ profile [${D}]: ${We1(A.toString())}`,{logger:Q.logger})}},"fromSharedConfigFiles"),nUQ=Wn((A)=>typeof A==="function","isFunction"),aUQ=Wn((A)=>nUQ(A)?async()=>await A():NQ1.fromStatic(A),"fromStatic"),sUQ=Wn(({environmentVariableSelector:A,configFileSelector:B,default:Q},D={})=>{let{signingName:Z,logger:G}=D,F={signingName:Z,logger:G};return NQ1.memoize(NQ1.chain(pUQ(A,F),iUQ(B,D),aUQ(Q)))},"loadConfig")});
var JZ=E((VG5,iwA)=>{var{defineProperty:nz1,getOwnPropertyDescriptor:IwQ,getOwnPropertyNames:YwQ}=Object,WwQ=Object.prototype.hasOwnProperty,JwQ=(A,B)=>nz1(A,"name",{value:B,configurable:!0}),XwQ=(A,B)=>{for(var Q in B)nz1(A,Q,{get:B[Q],enumerable:!0})},VwQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of YwQ(B))if(!WwQ.call(A,Z)&&Z!==Q)nz1(A,Z,{get:()=>B[Z],enumerable:!(D=IwQ(B,Z))||D.enumerable})}return A},CwQ=(A)=>VwQ(nz1({},"__esModule",{value:!0}),A),lwA={};XwQ(lwA,{parseUrl:()=>pwA});iwA.exports=CwQ(lwA);var KwQ=cwA(),pwA=JwQ((A)=>{if(typeof A==="string")return pwA(new URL(A));let{hostname:B,pathname:Q,port:D,protocol:Z,search:G}=A,F;if(G)F=KwQ.parseQueryString(G);return{hostname:B,port:D?parseInt(D):void 0,protocol:Z,path:Q,query:F}},"parseUrl")});
var Je1=E((hwA)=>{Object.defineProperty(hwA,"__esModule",{value:!0});hwA.getEndpointFromConfig=void 0;var oUQ=JD(),tUQ=fwA(),eUQ=async(A)=>oUQ.loadConfig(tUQ.getEndpointUrlConfig(A!==null&&A!==void 0?A:""))();hwA.getEndpointFromConfig=eUQ});
var JwA=E((YwA)=>{Object.defineProperty(YwA,"__esModule",{value:!0});YwA.getSSOTokenFromFile=void 0;var AUQ=J1("fs"),BUQ=De1(),{readFile:QUQ}=AUQ.promises,DUQ=async(A)=>{let B=BUQ.getSSOTokenFilepath(A),Q=await QUQ(B,"utf8");return JSON.parse(Q)};YwA.getSSOTokenFromFile=DUQ});
var K$A=E((HG5,C$A)=>{var{defineProperty:tz1,getOwnPropertyDescriptor:iwQ,getOwnPropertyNames:nwQ}=Object,awQ=Object.prototype.hasOwnProperty,Zk=(A,B)=>tz1(A,"name",{value:B,configurable:!0}),swQ=(A,B)=>{for(var Q in B)tz1(A,Q,{get:B[Q],enumerable:!0})},rwQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of nwQ(B))if(!awQ.call(A,Z)&&Z!==Q)tz1(A,Z,{get:()=>B[Z],enumerable:!(D=iwQ(B,Z))||D.enumerable})}return A},owQ=(A)=>rwQ(tz1({},"__esModule",{value:!0}),A),W$A={};swQ(W$A,{Field:()=>A$Q,Fields:()=>B$Q,HttpRequest:()=>Q$Q,HttpResponse:()=>D$Q,IHttpRequest:()=>J$A.HttpRequest,getHttpHandlerExtensionConfiguration:()=>twQ,isValidHostname:()=>V$A,resolveHttpHandlerRuntimeConfig:()=>ewQ});C$A.exports=owQ(W$A);var twQ=Zk((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),ewQ=Zk((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),J$A=Ve1(),A$Q=class{static{Zk(this,"Field")}constructor({name:A,kind:B=J$A.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},B$Q=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{Zk(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},Q$Q=class A{static{Zk(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=X$A(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function X$A(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}Zk(X$A,"cloneQuery");var D$Q=class{static{Zk(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function V$A(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}Zk(V$A,"isValidHostname")});
var K4=E((sZ5,bUA)=>{var{defineProperty:vz1,getOwnPropertyDescriptor:szQ,getOwnPropertyNames:rzQ}=Object,ozQ=Object.prototype.hasOwnProperty,Rw=(A,B)=>vz1(A,"name",{value:B,configurable:!0}),tzQ=(A,B)=>{for(var Q in B)vz1(A,Q,{get:B[Q],enumerable:!0})},ezQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of rzQ(B))if(!ozQ.call(A,Z)&&Z!==Q)vz1(A,Z,{get:()=>B[Z],enumerable:!(D=szQ(B,Z))||D.enumerable})}return A},AEQ=(A)=>ezQ(vz1({},"__esModule",{value:!0}),A),PUA={};tzQ(PUA,{CONFIG_USE_DUALSTACK_ENDPOINT:()=>jUA,CONFIG_USE_FIPS_ENDPOINT:()=>kUA,DEFAULT_USE_DUALSTACK_ENDPOINT:()=>BEQ,DEFAULT_USE_FIPS_ENDPOINT:()=>DEQ,ENV_USE_DUALSTACK_ENDPOINT:()=>SUA,ENV_USE_FIPS_ENDPOINT:()=>yUA,NODE_REGION_CONFIG_FILE_OPTIONS:()=>WEQ,NODE_REGION_CONFIG_OPTIONS:()=>YEQ,NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS:()=>QEQ,NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS:()=>ZEQ,REGION_ENV_NAME:()=>_UA,REGION_INI_NAME:()=>xUA,getRegionInfo:()=>KEQ,resolveCustomEndpointsConfig:()=>GEQ,resolveEndpointsConfig:()=>IEQ,resolveRegionConfig:()=>JEQ});bUA.exports=AEQ(PUA);var Qk=RUA(),SUA="AWS_USE_DUALSTACK_ENDPOINT",jUA="use_dualstack_endpoint",BEQ=!1,QEQ={environmentVariableSelector:(A)=>Qk.booleanSelector(A,SUA,Qk.SelectorType.ENV),configFileSelector:(A)=>Qk.booleanSelector(A,jUA,Qk.SelectorType.CONFIG),default:!1},yUA="AWS_USE_FIPS_ENDPOINT",kUA="use_fips_endpoint",DEQ=!1,ZEQ={environmentVariableSelector:(A)=>Qk.booleanSelector(A,yUA,Qk.SelectorType.ENV),configFileSelector:(A)=>Qk.booleanSelector(A,kUA,Qk.SelectorType.CONFIG),default:!1},xz1=J5(),GEQ=Rw((A)=>{let{tls:B,endpoint:Q,urlParser:D,useDualstackEndpoint:Z}=A;return Object.assign(A,{tls:B??!0,endpoint:xz1.normalizeProvider(typeof Q==="string"?D(Q):Q),isCustomEndpoint:!0,useDualstackEndpoint:xz1.normalizeProvider(Z??!1)})},"resolveCustomEndpointsConfig"),FEQ=Rw(async(A)=>{let{tls:B=!0}=A,Q=await A.region();if(!new RegExp(/^([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])$/).test(Q))throw new Error("Invalid region in client config");let Z=await A.useDualstackEndpoint(),G=await A.useFipsEndpoint(),{hostname:F}=await A.regionInfoProvider(Q,{useDualstackEndpoint:Z,useFipsEndpoint:G})??{};if(!F)throw new Error("Cannot resolve hostname from client config");return A.urlParser(`${B?"https:":"http:"}//${F}`)},"getEndpointFromRegion"),IEQ=Rw((A)=>{let B=xz1.normalizeProvider(A.useDualstackEndpoint??!1),{endpoint:Q,useFipsEndpoint:D,urlParser:Z,tls:G}=A;return Object.assign(A,{tls:G??!0,endpoint:Q?xz1.normalizeProvider(typeof Q==="string"?Z(Q):Q):()=>FEQ({...A,useDualstackEndpoint:B,useFipsEndpoint:D}),isCustomEndpoint:!!Q,useDualstackEndpoint:B})},"resolveEndpointsConfig"),_UA="AWS_REGION",xUA="region",YEQ={environmentVariableSelector:(A)=>A[_UA],configFileSelector:(A)=>A[xUA],default:()=>{throw new Error("Region is missing")}},WEQ={preferredFile:"credentials"},vUA=Rw((A)=>typeof A==="string"&&(A.startsWith("fips-")||A.endsWith("-fips")),"isFipsRegion"),OUA=Rw((A)=>vUA(A)?["fips-aws-global","aws-fips"].includes(A)?"us-east-1":A.replace(/fips-(dkr-|prod-)?|-fips/,""):A,"getRealRegion"),JEQ=Rw((A)=>{let{region:B,useFipsEndpoint:Q}=A;if(!B)throw new Error("Region is missing");return Object.assign(A,{region:async()=>{if(typeof B==="string")return OUA(B);let D=await B();return OUA(D)},useFipsEndpoint:async()=>{let D=typeof B==="string"?B:await B();if(vUA(D))return!0;return typeof Q!=="function"?Promise.resolve(!!Q):Q()}})},"resolveRegionConfig"),TUA=Rw((A=[],{useFipsEndpoint:B,useDualstackEndpoint:Q})=>A.find(({tags:D})=>B===D.includes("fips")&&Q===D.includes("dualstack"))?.hostname,"getHostnameFromVariants"),XEQ=Rw((A,{regionHostname:B,partitionHostname:Q})=>B?B:Q?Q.replace("{region}",A):void 0,"getResolvedHostname"),VEQ=Rw((A,{partitionHash:B})=>Object.keys(B||{}).find((Q)=>B[Q].regions.includes(A))??"aws","getResolvedPartition"),CEQ=Rw((A,{signingRegion:B,regionRegex:Q,useFipsEndpoint:D})=>{if(B)return B;else if(D){let Z=Q.replace("\\\\","\\").replace(/^\^/g,"\\.").replace(/\$$/g,"\\."),G=A.match(Z);if(G)return G[0].slice(1,-1)}},"getResolvedSigningRegion"),KEQ=Rw((A,{useFipsEndpoint:B=!1,useDualstackEndpoint:Q=!1,signingService:D,regionHash:Z,partitionHash:G})=>{let F=VEQ(A,{partitionHash:G}),I=A in Z?A:G[F]?.endpoint??A,Y={useFipsEndpoint:B,useDualstackEndpoint:Q},W=TUA(Z[I]?.variants,Y),J=TUA(G[F]?.variants,Y),X=XEQ(I,{regionHostname:W,partitionHostname:J});if(X===void 0)throw new Error(`Endpoint resolution failed for: ${{resolvedRegion:I,useFipsEndpoint:B,useDualstackEndpoint:Q}}`);let V=CEQ(X,{signingRegion:Z[I]?.signingRegion,regionRegex:G[F].regionRegex,useFipsEndpoint:B});return{partition:F,signingService:D,hostname:X,...V&&{signingRegion:V},...Z[I]?.signingService&&{signingService:Z[I].signingService}}},"getRegionInfo")});
var KLA=E((VLA)=>{Object.defineProperty(VLA,"__esModule",{value:!0});VLA.ruleSet=void 0;var YLA="required",Jz="fn",Xz="argv",Kn="ref",tNA=!0,eNA="isSet",SQ1="booleanEquals",Vn="error",Cn="endpoint",cO="tree",re1="PartitionResult",oe1="getAttr",ALA={[YLA]:!1,type:"String"},BLA={[YLA]:!0,default:!1,type:"Boolean"},QLA={[Kn]:"Endpoint"},WLA={[Jz]:SQ1,[Xz]:[{[Kn]:"UseFIPS"},!0]},JLA={[Jz]:SQ1,[Xz]:[{[Kn]:"UseDualStack"},!0]},Wz={},DLA={[Jz]:oe1,[Xz]:[{[Kn]:re1},"supportsFIPS"]},XLA={[Kn]:re1},ZLA={[Jz]:SQ1,[Xz]:[!0,{[Jz]:oe1,[Xz]:[XLA,"supportsDualStack"]}]},GLA=[WLA],FLA=[JLA],ILA=[{[Kn]:"Region"}],QRQ={version:"1.0",parameters:{Region:ALA,UseDualStack:BLA,UseFIPS:BLA,Endpoint:ALA},rules:[{conditions:[{[Jz]:eNA,[Xz]:[QLA]}],rules:[{conditions:GLA,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:Vn},{conditions:FLA,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:Vn},{endpoint:{url:QLA,properties:Wz,headers:Wz},type:Cn}],type:cO},{conditions:[{[Jz]:eNA,[Xz]:ILA}],rules:[{conditions:[{[Jz]:"aws.partition",[Xz]:ILA,assign:re1}],rules:[{conditions:[WLA,JLA],rules:[{conditions:[{[Jz]:SQ1,[Xz]:[tNA,DLA]},ZLA],rules:[{endpoint:{url:"https://portal.sso-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:Wz,headers:Wz},type:Cn}],type:cO},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:Vn}],type:cO},{conditions:GLA,rules:[{conditions:[{[Jz]:SQ1,[Xz]:[DLA,tNA]}],rules:[{conditions:[{[Jz]:"stringEquals",[Xz]:[{[Jz]:oe1,[Xz]:[XLA,"name"]},"aws-us-gov"]}],endpoint:{url:"https://portal.sso.{Region}.amazonaws.com",properties:Wz,headers:Wz},type:Cn},{endpoint:{url:"https://portal.sso-fips.{Region}.{PartitionResult#dnsSuffix}",properties:Wz,headers:Wz},type:Cn}],type:cO},{error:"FIPS is enabled but this partition does not support FIPS",type:Vn}],type:cO},{conditions:FLA,rules:[{conditions:[ZLA],rules:[{endpoint:{url:"https://portal.sso.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:Wz,headers:Wz},type:Cn}],type:cO},{error:"DualStack is enabled but this partition does not support DualStack",type:Vn}],type:cO},{endpoint:{url:"https://portal.sso.{Region}.{PartitionResult#dnsSuffix}",properties:Wz,headers:Wz},type:Cn}],type:cO}],type:cO},{error:"Invalid Configuration: Missing Region",type:Vn}]};VLA.ruleSet=QRQ});
var KOA=E((VOA)=>{Object.defineProperty(VOA,"__esModule",{value:!0});VOA.ruleSet=void 0;var QOA="required",H4="type",h8="fn",g8="argv",Yk="ref",pRA=!1,z10=!0,Ik="booleanEquals",jY="stringEquals",DOA="sigv4",ZOA="sts",GOA="us-east-1",XD="endpoint",iRA="https://sts.{Region}.{PartitionResult#dnsSuffix}",pN="tree",$n="error",U10="getAttr",nRA={[QOA]:!1,[H4]:"String"},E10={[QOA]:!0,default:!1,[H4]:"Boolean"},FOA={[Yk]:"Endpoint"},aRA={[h8]:"isSet",[g8]:[{[Yk]:"Region"}]},yY={[Yk]:"Region"},sRA={[h8]:"aws.partition",[g8]:[yY],assign:"PartitionResult"},IOA={[Yk]:"UseFIPS"},YOA={[Yk]:"UseDualStack"},mW={url:"https://sts.amazonaws.com",properties:{authSchemes:[{name:DOA,signingName:ZOA,signingRegion:GOA}]},headers:{}},BK={},rRA={conditions:[{[h8]:jY,[g8]:[yY,"aws-global"]}],[XD]:mW,[H4]:XD},WOA={[h8]:Ik,[g8]:[IOA,!0]},JOA={[h8]:Ik,[g8]:[YOA,!0]},oRA={[h8]:U10,[g8]:[{[Yk]:"PartitionResult"},"supportsFIPS"]},XOA={[Yk]:"PartitionResult"},tRA={[h8]:Ik,[g8]:[!0,{[h8]:U10,[g8]:[XOA,"supportsDualStack"]}]},eRA=[{[h8]:"isSet",[g8]:[FOA]}],AOA=[WOA],BOA=[JOA],_PQ={version:"1.0",parameters:{Region:nRA,UseDualStack:E10,UseFIPS:E10,Endpoint:nRA,UseGlobalEndpoint:E10},rules:[{conditions:[{[h8]:Ik,[g8]:[{[Yk]:"UseGlobalEndpoint"},z10]},{[h8]:"not",[g8]:eRA},aRA,sRA,{[h8]:Ik,[g8]:[IOA,pRA]},{[h8]:Ik,[g8]:[YOA,pRA]}],rules:[{conditions:[{[h8]:jY,[g8]:[yY,"ap-northeast-1"]}],endpoint:mW,[H4]:XD},{conditions:[{[h8]:jY,[g8]:[yY,"ap-south-1"]}],endpoint:mW,[H4]:XD},{conditions:[{[h8]:jY,[g8]:[yY,"ap-southeast-1"]}],endpoint:mW,[H4]:XD},{conditions:[{[h8]:jY,[g8]:[yY,"ap-southeast-2"]}],endpoint:mW,[H4]:XD},rRA,{conditions:[{[h8]:jY,[g8]:[yY,"ca-central-1"]}],endpoint:mW,[H4]:XD},{conditions:[{[h8]:jY,[g8]:[yY,"eu-central-1"]}],endpoint:mW,[H4]:XD},{conditions:[{[h8]:jY,[g8]:[yY,"eu-north-1"]}],endpoint:mW,[H4]:XD},{conditions:[{[h8]:jY,[g8]:[yY,"eu-west-1"]}],endpoint:mW,[H4]:XD},{conditions:[{[h8]:jY,[g8]:[yY,"eu-west-2"]}],endpoint:mW,[H4]:XD},{conditions:[{[h8]:jY,[g8]:[yY,"eu-west-3"]}],endpoint:mW,[H4]:XD},{conditions:[{[h8]:jY,[g8]:[yY,"sa-east-1"]}],endpoint:mW,[H4]:XD},{conditions:[{[h8]:jY,[g8]:[yY,GOA]}],endpoint:mW,[H4]:XD},{conditions:[{[h8]:jY,[g8]:[yY,"us-east-2"]}],endpoint:mW,[H4]:XD},{conditions:[{[h8]:jY,[g8]:[yY,"us-west-1"]}],endpoint:mW,[H4]:XD},{conditions:[{[h8]:jY,[g8]:[yY,"us-west-2"]}],endpoint:mW,[H4]:XD},{endpoint:{url:iRA,properties:{authSchemes:[{name:DOA,signingName:ZOA,signingRegion:"{Region}"}]},headers:BK},[H4]:XD}],[H4]:pN},{conditions:eRA,rules:[{conditions:AOA,error:"Invalid Configuration: FIPS and custom endpoint are not supported",[H4]:$n},{conditions:BOA,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",[H4]:$n},{endpoint:{url:FOA,properties:BK,headers:BK},[H4]:XD}],[H4]:pN},{conditions:[aRA],rules:[{conditions:[sRA],rules:[{conditions:[WOA,JOA],rules:[{conditions:[{[h8]:Ik,[g8]:[z10,oRA]},tRA],rules:[{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:BK,headers:BK},[H4]:XD}],[H4]:pN},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",[H4]:$n}],[H4]:pN},{conditions:AOA,rules:[{conditions:[{[h8]:Ik,[g8]:[oRA,z10]}],rules:[{conditions:[{[h8]:jY,[g8]:[{[h8]:U10,[g8]:[XOA,"name"]},"aws-us-gov"]}],endpoint:{url:"https://sts.{Region}.amazonaws.com",properties:BK,headers:BK},[H4]:XD},{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dnsSuffix}",properties:BK,headers:BK},[H4]:XD}],[H4]:pN},{error:"FIPS is enabled but this partition does not support FIPS",[H4]:$n}],[H4]:pN},{conditions:BOA,rules:[{conditions:[tRA],rules:[{endpoint:{url:"https://sts.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:BK,headers:BK},[H4]:XD}],[H4]:pN},{error:"DualStack is enabled but this partition does not support DualStack",[H4]:$n}],[H4]:pN},rRA,{endpoint:{url:iRA,properties:BK,headers:BK},[H4]:XD}],[H4]:pN}],[H4]:pN},{error:"Invalid Configuration: Missing Region",[H4]:$n}]};VOA.ruleSet=_PQ});
var Ko1=E((G75,mWA)=>{var{defineProperty:IH1,getOwnPropertyDescriptor:a7Q,getOwnPropertyNames:s7Q}=Object,r7Q=Object.prototype.hasOwnProperty,YH1=(A,B)=>IH1(A,"name",{value:B,configurable:!0}),o7Q=(A,B)=>{for(var Q in B)IH1(A,Q,{get:B[Q],enumerable:!0})},t7Q=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of s7Q(B))if(!r7Q.call(A,Z)&&Z!==Q)IH1(A,Z,{get:()=>B[Z],enumerable:!(D=a7Q(B,Z))||D.enumerable})}return A},e7Q=(A)=>t7Q(IH1({},"__esModule",{value:!0}),A),_WA={};o7Q(_WA,{AlgorithmId:()=>fWA,EndpointURLScheme:()=>bWA,FieldPosition:()=>hWA,HttpApiKeyAuthLocation:()=>vWA,HttpAuthLocation:()=>xWA,IniSectionType:()=>gWA,RequestHandlerProtocol:()=>uWA,SMITHY_CONTEXT_KEY:()=>ZDQ,getDefaultClientConfiguration:()=>QDQ,resolveDefaultRuntimeConfig:()=>DDQ});mWA.exports=e7Q(_WA);var xWA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(xWA||{}),vWA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(vWA||{}),bWA=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(bWA||{}),fWA=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(fWA||{}),ADQ=YH1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),BDQ=YH1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),QDQ=YH1((A)=>{return ADQ(A)},"getDefaultClientConfiguration"),DDQ=YH1((A)=>{return BDQ(A)},"resolveDefaultRuntimeConfig"),hWA=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(hWA||{}),ZDQ="__smithy_context",gWA=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(gWA||{}),uWA=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(uWA||{})});
var LQ1=E((w$A)=>{Object.defineProperty(w$A,"__esModule",{value:!0});w$A.default=void 0;var W$Q=J$Q(U$A());function J$Q(A){return A&&A.__esModule?A:{default:A}}function X$Q(A){return typeof A==="string"&&W$Q.default.test(A)}var V$Q=X$Q;w$A.default=V$Q});
var Lo1=E((NXA)=>{Object.defineProperty(NXA,"__esModule",{value:!0});NXA.ChecksumStream=void 0;var mGQ=ii(),dGQ=J1("stream");class qXA extends dGQ.Duplex{constructor({expectedChecksum:A,checksum:B,source:Q,checksumSourceLocation:D,base64Encoder:Z}){var G,F;super();if(typeof Q.pipe==="function")this.source=Q;else throw new Error(`@smithy/util-stream: unsupported source type ${(F=(G=Q===null||Q===void 0?void 0:Q.constructor)===null||G===void 0?void 0:G.name)!==null&&F!==void 0?F:Q} in ChecksumStream.`);this.base64Encoder=Z!==null&&Z!==void 0?Z:mGQ.toBase64,this.expectedChecksum=A,this.checksum=B,this.checksumSourceLocation=D,this.source.pipe(this)}_read(A){}_write(A,B,Q){try{this.checksum.update(A),this.push(A)}catch(D){return Q(D)}return Q()}async _final(A){try{let B=await this.checksum.digest(),Q=this.base64Encoder(B);if(this.expectedChecksum!==Q)return A(new Error(`Checksum mismatch: expected "${this.expectedChecksum}" but received "${Q}" in response header "${this.checksumSourceLocation}".`))}catch(B){return A(B)}return this.push(null),A()}}NXA.ChecksumStream=qXA});
var M6=E((TD5,LKA)=>{var{defineProperty:Zt1,getOwnPropertyDescriptor:JJQ,getOwnPropertyNames:XJQ}=Object,VJQ=Object.prototype.hasOwnProperty,CJQ=(A,B)=>{for(var Q in B)Zt1(A,Q,{get:B[Q],enumerable:!0})},KJQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of XJQ(B))if(!VJQ.call(A,Z)&&Z!==Q)Zt1(A,Z,{get:()=>B[Z],enumerable:!(D=JJQ(B,Z))||D.enumerable})}return A},HJQ=(A)=>KJQ(Zt1({},"__esModule",{value:!0}),A),EKA={};CJQ(EKA,{FromStringShapeDeserializer:()=>qKA,HttpBindingProtocol:()=>UJQ,HttpInterceptingShapeDeserializer:()=>RJQ,HttpInterceptingShapeSerializer:()=>TJQ,RequestBuilder:()=>$KA,RpcProtocol:()=>$JQ,ToStringShapeSerializer:()=>NKA,collectBody:()=>ei,determineTimestampFormat:()=>Gt1,extendedEncodeURIComponent:()=>JQ1,requestBuilder:()=>NJQ,resolvedPath:()=>wKA});LKA.exports=HJQ(EKA);var Bt1=ry(),ei=async(A=new Uint8Array,B)=>{if(A instanceof Uint8Array)return Bt1.Uint8ArrayBlobAdapter.mutate(A);if(!A)return Bt1.Uint8ArrayBlobAdapter.mutate(new Uint8Array);let Q=B.streamCollector(A);return Bt1.Uint8ArrayBlobAdapter.mutate(await Q)};function JQ1(A){return encodeURIComponent(A).replace(/[!'()*]/g,function(B){return"%"+B.charCodeAt(0).toString(16).toUpperCase()})}var YQ1=_Q(),zJQ=qh(),sH1=_Q(),VKA=X6(),CKA=qh(),EJQ=ry(),UKA=class{constructor(A){this.options=A}getRequestType(){return CKA.HttpRequest}getResponseType(){return CKA.HttpResponse}setSerdeContext(A){if(this.serdeContext=A,this.serializer.setSerdeContext(A),this.deserializer.setSerdeContext(A),this.getPayloadCodec())this.getPayloadCodec().setSerdeContext(A)}updateServiceEndpoint(A,B){if("url"in B){A.protocol=B.url.protocol,A.hostname=B.url.hostname,A.port=B.url.port?Number(B.url.port):void 0,A.path=B.url.pathname,A.fragment=B.url.hash||void 0,A.username=B.url.username||void 0,A.password=B.url.password||void 0;for(let[Q,D]of B.url.searchParams.entries()){if(!A.query)A.query={};A.query[Q]=D}return A}else return A.protocol=B.protocol,A.hostname=B.hostname,A.port=B.port?Number(B.port):void 0,A.path=B.path,A.query={...B.query},A}setHostPrefix(A,B,Q){let D=sH1.NormalizedSchema.of(B),Z=sH1.NormalizedSchema.of(B.input);if(D.getMergedTraits().endpoint){let G=D.getMergedTraits().endpoint?.[0];if(typeof G==="string"){let F=[...Z.structIterator()].filter(([,I])=>I.getMergedTraits().hostLabel);for(let[I]of F){let Y=Q[I];if(typeof Y!=="string")throw new Error(`@smithy/core/schema - ${I} in input must be a string as hostLabel.`);G=G.replace(`{${I}}`,Y)}A.hostname=G+A.hostname}}}deserializeMetadata(A){return{httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}}async deserializeHttpMessage(A,B,Q,D,Z){let G;if(D instanceof Set)G=Z;else G=D;let F=this.deserializer,I=sH1.NormalizedSchema.of(A),Y=[];for(let[W,J]of I.structIterator()){let X=J.getMemberTraits();if(X.httpPayload){if(J.isStreaming())if(J.isStructSchema()){let K=this.serdeContext;if(!K.eventStreamMarshaller)throw new Error("@smithy/core - HttpProtocol: eventStreamMarshaller missing in serdeContext.");let H=J.getMemberSchemas();G[W]=K.eventStreamMarshaller.deserialize(Q.body,async(z)=>{let $=Object.keys(z).find((L)=>{return L!=="__type"})??"";if($ in H){let L=H[$];return{[$]:await F.read(L,z[$].body)}}else return{$unknown:z}})}else G[W]=EJQ.sdkStreamMixin(Q.body);else if(Q.body){let C=await ei(Q.body,B);if(C.byteLength>0)G[W]=await F.read(J,C)}}else if(X.httpHeader){let V=String(X.httpHeader).toLowerCase(),C=Q.headers[V];if(C!=null)if(J.isListSchema()){let K=J.getValueSchema(),H;if(K.isTimestampSchema()&&K.getSchema()===sH1.SCHEMA.TIMESTAMP_DEFAULT)H=VKA.splitEvery(C,",",2);else H=VKA.splitHeader(C);let z=[];for(let $ of H)z.push(await F.read([K,{httpHeader:V}],$.trim()));G[W]=z}else G[W]=await F.read(J,C)}else if(X.httpPrefixHeaders!==void 0){G[W]={};for(let[V,C]of Object.entries(Q.headers))if(V.startsWith(X.httpPrefixHeaders))G[W][V.slice(X.httpPrefixHeaders.length)]=await F.read([J.getValueSchema(),{httpHeader:V}],C)}else if(X.httpResponseCode)G[W]=Q.statusCode;else Y.push(W)}return Y}},UJQ=class extends UKA{async serializeRequest(A,B,Q){let D=this.serializer,Z={},G={},F=await Q.endpoint(),I=YQ1.NormalizedSchema.of(A?.input),Y=I.getSchema(),W=!1,J,X=new zJQ.HttpRequest({protocol:"",hostname:"",port:void 0,path:"",fragment:void 0,query:Z,headers:G,body:void 0});if(F){this.updateServiceEndpoint(X,F),this.setHostPrefix(X,A,B);let C=YQ1.NormalizedSchema.translateTraits(A.traits);if(C.http){X.method=C.http[0];let[K,H]=C.http[1].split("?");if(X.path=="/")X.path=K;else X.path+=K;let z=new URLSearchParams(H??"");Object.assign(Z,Object.fromEntries(z))}}let V={...B};for(let C of Object.keys(V)){let K=I.getMemberSchema(C);if(K===void 0)continue;let H=K.getMergedTraits(),z=V[C];if(H.httpPayload)if(K.isStreaming())if(K.isStructSchema())throw new Error("serialization of event streams is not yet implemented");else J=z;else D.write(K,z),J=D.flush();else if(H.httpLabel){D.write(K,z);let $=D.flush();if(X.path.includes(`{${C}+}`))X.path=X.path.replace(`{${C}+}`,$.split("/").map(JQ1).join("/"));else if(X.path.includes(`{${C}}`))X.path=X.path.replace(`{${C}}`,JQ1($));delete V[C]}else if(H.httpHeader)D.write(K,z),G[H.httpHeader.toLowerCase()]=String(D.flush()),delete V[C];else if(typeof H.httpPrefixHeaders==="string"){for(let[$,L]of Object.entries(z)){let N=H.httpPrefixHeaders+$;D.write([K.getValueSchema(),{httpHeader:N}],L),G[N.toLowerCase()]=D.flush()}delete V[C]}else if(H.httpQuery||H.httpQueryParams)this.serializeQuery(K,z,Z),delete V[C];else W=!0}if(W&&B)D.write(Y,V),J=D.flush();return X.headers=G,X.query=Z,X.body=J,X}serializeQuery(A,B,Q){let D=this.serializer,Z=A.getMergedTraits();if(Z.httpQueryParams){for(let[G,F]of Object.entries(B))if(!(G in Q))this.serializeQuery(YQ1.NormalizedSchema.of([A.getValueSchema(),{...Z,httpQuery:G,httpQueryParams:void 0}]),F,Q);return}if(A.isListSchema()){let G=!!A.getMergedTraits().sparse,F=[];for(let I of B){D.write([A.getValueSchema(),Z],I);let Y=D.flush();if(G||Y!==void 0)F.push(Y)}Q[Z.httpQuery]=F}else D.write([A,Z],B),Q[Z.httpQuery]=D.flush()}async deserializeResponse(A,B,Q){let D=this.deserializer,Z=YQ1.NormalizedSchema.of(A.output),G={};if(Q.statusCode>=300){let Y=await ei(Q.body,B);if(Y.byteLength>0)Object.assign(G,await D.read(YQ1.SCHEMA.DOCUMENT,Y));throw await this.handleError(A,B,Q,G,this.deserializeMetadata(Q)),new Error("@smithy/core/protocols - HTTP Protocol error handler failed to throw.")}for(let Y in Q.headers){let W=Q.headers[Y];delete Q.headers[Y],Q.headers[Y.toLowerCase()]=W}let F=await this.deserializeHttpMessage(Z,B,Q,G);if(F.length){let Y=await ei(Q.body,B);if(Y.byteLength>0){let W=await D.read(Z,Y);for(let J of F)G[J]=W[J]}}return{$metadata:this.deserializeMetadata(Q),...G}}},Qt1=_Q(),wJQ=qh(),$JQ=class extends UKA{async serializeRequest(A,B,Q){let D=this.serializer,Z={},G={},F=await Q.endpoint(),Y=Qt1.NormalizedSchema.of(A?.input).getSchema(),W,J=new wJQ.HttpRequest({protocol:"",hostname:"",port:void 0,path:"/",fragment:void 0,query:Z,headers:G,body:void 0});if(F)this.updateServiceEndpoint(J,F),this.setHostPrefix(J,A,B);let X={...B};if(B)D.write(Y,X),W=D.flush();return J.headers=G,J.query=Z,J.body=W,J.method="POST",J}async deserializeResponse(A,B,Q){let D=this.deserializer,Z=Qt1.NormalizedSchema.of(A.output),G={};if(Q.statusCode>=300){let Y=await ei(Q.body,B);if(Y.byteLength>0)Object.assign(G,await D.read(Qt1.SCHEMA.DOCUMENT,Y));throw await this.handleError(A,B,Q,G,this.deserializeMetadata(Q)),new Error("@smithy/core/protocols - RPC Protocol error handler failed to throw.")}for(let Y in Q.headers){let W=Q.headers[Y];delete Q.headers[Y],Q.headers[Y.toLowerCase()]=W}let F=await ei(Q.body,B);if(F.byteLength>0)Object.assign(G,await D.read(Z,F));return{$metadata:this.deserializeMetadata(Q),...G}}},qJQ=qh(),wKA=(A,B,Q,D,Z,G)=>{if(B!=null&&B[Q]!==void 0){let F=D();if(F.length<=0)throw new Error("Empty value provided for input HTTP label: "+Q+".");A=A.replace(Z,G?F.split("/").map((I)=>JQ1(I)).join("/"):JQ1(F))}else throw new Error("No value provided for input HTTP label: "+Q+".");return A};function NJQ(A,B){return new $KA(A,B)}var $KA=class{constructor(A,B){this.input=A,this.context=B,this.query={},this.method="",this.headers={},this.path="",this.body=null,this.hostname="",this.resolvePathStack=[]}async build(){let{hostname:A,protocol:B="https",port:Q,path:D}=await this.context.endpoint();this.path=D;for(let Z of this.resolvePathStack)Z(this.path);return new qJQ.HttpRequest({protocol:B,hostname:this.hostname||A,port:Q,method:this.method,path:this.path,query:this.query,body:this.body,headers:this.headers})}hn(A){return this.hostname=A,this}bp(A){return this.resolvePathStack.push((B)=>{this.path=`${B?.endsWith("/")?B.slice(0,-1):B||""}`+A}),this}p(A,B,Q,D){return this.resolvePathStack.push((Z)=>{this.path=wKA(Z,this.input,A,B,Q,D)}),this}h(A){return this.headers=A,this}q(A){return this.query=A,this}b(A){return this.body=A,this}m(A){return this.method=A,this}},rH1=_Q(),ti=X6(),KKA=At1(),LJQ=cB(),WQ1=_Q();function Gt1(A,B){if(B.timestampFormat.useTrait){if(A.isTimestampSchema()&&(A.getSchema()===WQ1.SCHEMA.TIMESTAMP_DATE_TIME||A.getSchema()===WQ1.SCHEMA.TIMESTAMP_HTTP_DATE||A.getSchema()===WQ1.SCHEMA.TIMESTAMP_EPOCH_SECONDS))return A.getSchema()}let{httpLabel:Q,httpPrefixHeaders:D,httpHeader:Z,httpQuery:G}=A.getMergedTraits();return(B.httpBindings?typeof D==="string"||Boolean(Z)?WQ1.SCHEMA.TIMESTAMP_HTTP_DATE:Boolean(G)||Boolean(Q)?WQ1.SCHEMA.TIMESTAMP_DATE_TIME:void 0:void 0)??B.timestampFormat.default}var qKA=class{constructor(A){this.settings=A}setSerdeContext(A){this.serdeContext=A}read(A,B){let Q=rH1.NormalizedSchema.of(A);if(Q.isListSchema())return ti.splitHeader(B).map((D)=>this.read(Q.getValueSchema(),D));if(Q.isBlobSchema())return(this.serdeContext?.base64Decoder??KKA.fromBase64)(B);if(Q.isTimestampSchema())switch(Gt1(Q,this.settings)){case rH1.SCHEMA.TIMESTAMP_DATE_TIME:return ti.parseRfc3339DateTimeWithOffset(B);case rH1.SCHEMA.TIMESTAMP_HTTP_DATE:return ti.parseRfc7231DateTime(B);case rH1.SCHEMA.TIMESTAMP_EPOCH_SECONDS:return ti.parseEpochTimestamp(B);default:return console.warn("Missing timestamp format, parsing value with Date constructor:",B),new Date(B)}if(Q.isStringSchema()){let D=Q.getMergedTraits().mediaType,Z=B;if(D){if(Q.getMergedTraits().httpHeader)Z=this.base64ToUtf8(Z);if(D==="application/json"||D.endsWith("+json"))Z=ti.LazyJsonString.from(Z);return Z}}switch(!0){case Q.isNumericSchema():return Number(B);case Q.isBigIntegerSchema():return BigInt(B);case Q.isBigDecimalSchema():return new ti.NumericValue(B,"bigDecimal");case Q.isBooleanSchema():return String(B).toLowerCase()==="true"}return B}base64ToUtf8(A){return(this.serdeContext?.utf8Encoder??LJQ.toUtf8)((this.serdeContext?.base64Decoder??KKA.fromBase64)(A))}},MJQ=_Q(),HKA=cB(),RJQ=class{constructor(A,B){this.codecDeserializer=A,this.stringDeserializer=new qKA(B)}setSerdeContext(A){this.stringDeserializer.setSerdeContext(A),this.codecDeserializer.setSerdeContext(A),this.serdeContext=A}read(A,B){let Q=MJQ.NormalizedSchema.of(A),D=Q.getMergedTraits(),Z=this.serdeContext?.utf8Encoder??HKA.toUtf8;if(D.httpHeader||D.httpResponseCode)return this.stringDeserializer.read(Q,Z(B));if(D.httpPayload){if(Q.isBlobSchema()){let G=this.serdeContext?.utf8Decoder??HKA.fromUtf8;if(typeof B==="string")return G(B);return B}else if(Q.isStringSchema()){if("byteLength"in B)return Z(B);return B}}return this.codecDeserializer.read(Q,B)}},OJQ=_Q(),oH1=_Q(),Dt1=X6(),zKA=At1(),NKA=class{constructor(A){this.settings=A,this.stringBuffer="",this.serdeContext=void 0}setSerdeContext(A){this.serdeContext=A}write(A,B){let Q=oH1.NormalizedSchema.of(A);switch(typeof B){case"object":if(B===null){this.stringBuffer="null";return}if(Q.isTimestampSchema()){if(!(B instanceof Date))throw new Error(`@smithy/core/protocols - received non-Date value ${B} when schema expected Date in ${Q.getName(!0)}`);switch(Gt1(Q,this.settings)){case oH1.SCHEMA.TIMESTAMP_DATE_TIME:this.stringBuffer=B.toISOString().replace(".000Z","Z");break;case oH1.SCHEMA.TIMESTAMP_HTTP_DATE:this.stringBuffer=Dt1.dateToUtcString(B);break;case oH1.SCHEMA.TIMESTAMP_EPOCH_SECONDS:this.stringBuffer=String(B.getTime()/1000);break;default:console.warn("Missing timestamp format, using epoch seconds",B),this.stringBuffer=String(B.getTime()/1000)}return}if(Q.isBlobSchema()&&"byteLength"in B){this.stringBuffer=(this.serdeContext?.base64Encoder??zKA.toBase64)(B);return}if(Q.isListSchema()&&Array.isArray(B)){let G="";for(let F of B){this.write([Q.getValueSchema(),Q.getMergedTraits()],F);let I=this.flush(),Y=Q.getValueSchema().isTimestampSchema()?I:Dt1.quoteHeader(I);if(G!=="")G+=", ";G+=Y}this.stringBuffer=G;return}this.stringBuffer=JSON.stringify(B,null,2);break;case"string":let D=Q.getMergedTraits().mediaType,Z=B;if(D){if(D==="application/json"||D.endsWith("+json"))Z=Dt1.LazyJsonString.from(Z);if(Q.getMergedTraits().httpHeader){this.stringBuffer=(this.serdeContext?.base64Encoder??zKA.toBase64)(Z.toString());return}}this.stringBuffer=B;break;default:this.stringBuffer=String(B)}}flush(){let A=this.stringBuffer;return this.stringBuffer="",A}},TJQ=class{constructor(A,B,Q=new NKA(B)){this.codecSerializer=A,this.stringSerializer=Q}setSerdeContext(A){this.codecSerializer.setSerdeContext(A),this.stringSerializer.setSerdeContext(A)}write(A,B){let Q=OJQ.NormalizedSchema.of(A),D=Q.getMergedTraits();if(D.httpHeader||D.httpLabel||D.httpQuery){this.stringSerializer.write(Q,B),this.buffer=this.stringSerializer.flush();return}return this.codecSerializer.write(Q,B)}flush(){if(this.buffer!==void 0){let A=this.buffer;return this.buffer=void 0,A}return this.codecSerializer.flush()}}});
var MEA=E(($Z5,LEA)=>{var nKQ=/^[-+]?0x[a-fA-F0-9]+$/,aKQ=/^([\-\+])?(0*)(\.[0-9]+([eE]\-?[0-9]+)?|[0-9]+(\.[0-9]+([eE]\-?[0-9]+)?)?)$/;if(!Number.parseInt&&window.parseInt)Number.parseInt=window.parseInt;if(!Number.parseFloat&&window.parseFloat)Number.parseFloat=window.parseFloat;var sKQ={hex:!0,leadingZeros:!0,decimalPoint:".",eNotation:!0};function rKQ(A,B={}){if(B=Object.assign({},sKQ,B),!A||typeof A!=="string")return A;let Q=A.trim();if(B.skipLike!==void 0&&B.skipLike.test(Q))return A;else if(B.hex&&nKQ.test(Q))return Number.parseInt(Q,16);else{let D=aKQ.exec(Q);if(D){let Z=D[1],G=D[2],F=oKQ(D[3]),I=D[4]||D[6];if(!B.leadingZeros&&G.length>0&&Z&&Q[2]!==".")return A;else if(!B.leadingZeros&&G.length>0&&!Z&&Q[1]!==".")return A;else{let Y=Number(Q),W=""+Y;if(W.search(/[eE]/)!==-1)if(B.eNotation)return Y;else return A;else if(I)if(B.eNotation)return Y;else return A;else if(Q.indexOf(".")!==-1)if(W==="0"&&F==="")return Y;else if(W===F)return Y;else if(Z&&W==="-"+F)return Y;else return A;if(G)if(F===W)return Y;else if(Z+F===W)return Y;else return A;if(Q===W)return Y;else if(Q===Z+W)return Y;return A}}else return A}}function oKQ(A){if(A&&A.indexOf(".")!==-1){if(A=A.replace(/0+$/,""),A===".")A="0";else if(A[0]===".")A="0"+A;else if(A[A.length-1]===".")A=A.substr(0,A.length-1);return A}return A}LEA.exports=rKQ});
var MJA=E((K75,LJA)=>{var{defineProperty:EH1,getOwnPropertyDescriptor:eDQ,getOwnPropertyNames:AZQ}=Object,BZQ=Object.prototype.hasOwnProperty,UH1=(A,B)=>EH1(A,"name",{value:B,configurable:!0}),QZQ=(A,B)=>{for(var Q in B)EH1(A,Q,{get:B[Q],enumerable:!0})},DZQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of AZQ(B))if(!BZQ.call(A,Z)&&Z!==Q)EH1(A,Z,{get:()=>B[Z],enumerable:!(D=eDQ(B,Z))||D.enumerable})}return A},ZZQ=(A)=>DZQ(EH1({},"__esModule",{value:!0}),A),HJA={};QZQ(HJA,{AlgorithmId:()=>wJA,EndpointURLScheme:()=>UJA,FieldPosition:()=>$JA,HttpApiKeyAuthLocation:()=>EJA,HttpAuthLocation:()=>zJA,IniSectionType:()=>qJA,RequestHandlerProtocol:()=>NJA,SMITHY_CONTEXT_KEY:()=>WZQ,getDefaultClientConfiguration:()=>IZQ,resolveDefaultRuntimeConfig:()=>YZQ});LJA.exports=ZZQ(HJA);var zJA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(zJA||{}),EJA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(EJA||{}),UJA=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(UJA||{}),wJA=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(wJA||{}),GZQ=UH1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),FZQ=UH1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),IZQ=UH1((A)=>{return GZQ(A)},"getDefaultClientConfiguration"),YZQ=UH1((A)=>{return FZQ(A)},"resolveDefaultRuntimeConfig"),$JA=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})($JA||{}),WZQ="__smithy_context",qJA=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(qJA||{}),NJA=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(NJA||{})});
var MNA=E((NNA)=>{Object.defineProperty(NNA,"__esModule",{value:!0});NNA.checkUrl=void 0;var nLQ=Q9(),aLQ="*************",sLQ="*************3",rLQ="[fd00:ec2::23]",oLQ=(A,B)=>{if(A.protocol==="https:")return;if(A.hostname===aLQ||A.hostname===sLQ||A.hostname===rLQ)return;if(A.hostname.includes("[")){if(A.hostname==="[::1]"||A.hostname==="[0000:0000:0000:0000:0000:0000:0000:0001]")return}else{if(A.hostname==="localhost")return;let Q=A.hostname.split("."),D=(Z)=>{let G=parseInt(Z,10);return 0<=G&&G<=255};if(Q[0]==="127"&&D(Q[1])&&D(Q[2])&&D(Q[3])&&Q.length===4)return}throw new nLQ.CredentialsProviderError(`URL not accepted. It must either be HTTPS or match one of the following:
  - loopback CIDR *********/8 or [::1/128]
  - ECS container host *************
  - EKS container host *************3 or [fd00:ec2::23]`,{logger:B})};NNA.checkUrl=oLQ});
var MPA=E((NPA)=>{Object.defineProperty(NPA,"__esModule",{value:!0});NPA.getRuntimeConfig=void 0;var NyQ=WI(),LyQ=CB(),MyQ=g4(),RyQ=JZ(),$PA=ty(),qPA=cB(),OyQ=ve1(),TyQ=wPA(),PyQ=(A)=>{return{apiVersion:"2011-06-15",base64Decoder:A?.base64Decoder??$PA.fromBase64,base64Encoder:A?.base64Encoder??$PA.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??TyQ.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??OyQ.defaultSTSHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new NyQ.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new LyQ.NoAuthSigner}],logger:A?.logger??new MyQ.NoOpLogger,serviceId:A?.serviceId??"STS",urlParser:A?.urlParser??RyQ.parseUrl,utf8Decoder:A?.utf8Decoder??qPA.fromUtf8,utf8Encoder:A?.utf8Encoder??qPA.toUtf8}};NPA.getRuntimeConfig=PyQ});
var MQ1=E((N$A)=>{Object.defineProperty(N$A,"__esModule",{value:!0});N$A.default=void 0;N$A.unsafeStringify=q$A;var C$Q=K$Q(LQ1());function K$Q(A){return A&&A.__esModule?A:{default:A}}var SY=[];for(let A=0;A<256;++A)SY.push((A+256).toString(16).slice(1));function q$A(A,B=0){return SY[A[B+0]]+SY[A[B+1]]+SY[A[B+2]]+SY[A[B+3]]+"-"+SY[A[B+4]]+SY[A[B+5]]+"-"+SY[A[B+6]]+SY[A[B+7]]+"-"+SY[A[B+8]]+SY[A[B+9]]+"-"+SY[A[B+10]]+SY[A[B+11]]+SY[A[B+12]]+SY[A[B+13]]+SY[A[B+14]]+SY[A[B+15]]}function H$Q(A,B=0){let Q=q$A(A,B);if(!C$Q.default(Q))throw TypeError("Stringified UUID is invalid");return Q}var z$Q=H$Q;N$A.default=z$Q});
var Mh=E((iD5,Iz1)=>{var VHA,CHA,KHA,HHA,zHA,EHA,UHA,wHA,$HA,qHA,NHA,LHA,MHA,Gz1,Ct1,RHA,OHA,THA,Qn,PHA,SHA,jHA,yHA,kHA,_HA,xHA,vHA,bHA,Fz1,fHA,hHA,gHA;(function(A){var B=typeof global==="object"?global:typeof self==="object"?self:typeof this==="object"?this:{};if(typeof define==="function"&&define.amd)define("tslib",["exports"],function(D){A(Q(B,Q(D)))});else if(typeof Iz1==="object"&&typeof iD5==="object")A(Q(B,Q(iD5)));else A(Q(B));function Q(D,Z){if(D!==B)if(typeof Object.create==="function")Object.defineProperty(D,"__esModule",{value:!0});else D.__esModule=!0;return function(G,F){return D[G]=Z?Z(G,F):F}}})(function(A){var B=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(G,F){G.__proto__=F}||function(G,F){for(var I in F)if(Object.prototype.hasOwnProperty.call(F,I))G[I]=F[I]};VHA=function(G,F){if(typeof F!=="function"&&F!==null)throw new TypeError("Class extends value "+String(F)+" is not a constructor or null");B(G,F);function I(){this.constructor=G}G.prototype=F===null?Object.create(F):(I.prototype=F.prototype,new I)},CHA=Object.assign||function(G){for(var F,I=1,Y=arguments.length;I<Y;I++){F=arguments[I];for(var W in F)if(Object.prototype.hasOwnProperty.call(F,W))G[W]=F[W]}return G},KHA=function(G,F){var I={};for(var Y in G)if(Object.prototype.hasOwnProperty.call(G,Y)&&F.indexOf(Y)<0)I[Y]=G[Y];if(G!=null&&typeof Object.getOwnPropertySymbols==="function"){for(var W=0,Y=Object.getOwnPropertySymbols(G);W<Y.length;W++)if(F.indexOf(Y[W])<0&&Object.prototype.propertyIsEnumerable.call(G,Y[W]))I[Y[W]]=G[Y[W]]}return I},HHA=function(G,F,I,Y){var W=arguments.length,J=W<3?F:Y===null?Y=Object.getOwnPropertyDescriptor(F,I):Y,X;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")J=Reflect.decorate(G,F,I,Y);else for(var V=G.length-1;V>=0;V--)if(X=G[V])J=(W<3?X(J):W>3?X(F,I,J):X(F,I))||J;return W>3&&J&&Object.defineProperty(F,I,J),J},zHA=function(G,F){return function(I,Y){F(I,Y,G)}},EHA=function(G,F,I,Y,W,J){function X(T){if(T!==void 0&&typeof T!=="function")throw new TypeError("Function expected");return T}var V=Y.kind,C=V==="getter"?"get":V==="setter"?"set":"value",K=!F&&G?Y.static?G:G.prototype:null,H=F||(K?Object.getOwnPropertyDescriptor(K,Y.name):{}),z,$=!1;for(var L=I.length-1;L>=0;L--){var N={};for(var O in Y)N[O]=O==="access"?{}:Y[O];for(var O in Y.access)N.access[O]=Y.access[O];N.addInitializer=function(T){if($)throw new TypeError("Cannot add initializers after decoration has completed");J.push(X(T||null))};var R=I[L](V==="accessor"?{get:H.get,set:H.set}:H[C],N);if(V==="accessor"){if(R===void 0)continue;if(R===null||typeof R!=="object")throw new TypeError("Object expected");if(z=X(R.get))H.get=z;if(z=X(R.set))H.set=z;if(z=X(R.init))W.unshift(z)}else if(z=X(R))if(V==="field")W.unshift(z);else H[C]=z}if(K)Object.defineProperty(K,Y.name,H);$=!0},UHA=function(G,F,I){var Y=arguments.length>2;for(var W=0;W<F.length;W++)I=Y?F[W].call(G,I):F[W].call(G);return Y?I:void 0},wHA=function(G){return typeof G==="symbol"?G:"".concat(G)},$HA=function(G,F,I){if(typeof F==="symbol")F=F.description?"[".concat(F.description,"]"):"";return Object.defineProperty(G,"name",{configurable:!0,value:I?"".concat(I," ",F):F})},qHA=function(G,F){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(G,F)},NHA=function(G,F,I,Y){function W(J){return J instanceof I?J:new I(function(X){X(J)})}return new(I||(I=Promise))(function(J,X){function V(H){try{K(Y.next(H))}catch(z){X(z)}}function C(H){try{K(Y.throw(H))}catch(z){X(z)}}function K(H){H.done?J(H.value):W(H.value).then(V,C)}K((Y=Y.apply(G,F||[])).next())})},LHA=function(G,F){var I={label:0,sent:function(){if(J[0]&1)throw J[1];return J[1]},trys:[],ops:[]},Y,W,J,X=Object.create((typeof Iterator==="function"?Iterator:Object).prototype);return X.next=V(0),X.throw=V(1),X.return=V(2),typeof Symbol==="function"&&(X[Symbol.iterator]=function(){return this}),X;function V(K){return function(H){return C([K,H])}}function C(K){if(Y)throw new TypeError("Generator is already executing.");while(X&&(X=0,K[0]&&(I=0)),I)try{if(Y=1,W&&(J=K[0]&2?W.return:K[0]?W.throw||((J=W.return)&&J.call(W),0):W.next)&&!(J=J.call(W,K[1])).done)return J;if(W=0,J)K=[K[0]&2,J.value];switch(K[0]){case 0:case 1:J=K;break;case 4:return I.label++,{value:K[1],done:!1};case 5:I.label++,W=K[1],K=[0];continue;case 7:K=I.ops.pop(),I.trys.pop();continue;default:if((J=I.trys,!(J=J.length>0&&J[J.length-1]))&&(K[0]===6||K[0]===2)){I=0;continue}if(K[0]===3&&(!J||K[1]>J[0]&&K[1]<J[3])){I.label=K[1];break}if(K[0]===6&&I.label<J[1]){I.label=J[1],J=K;break}if(J&&I.label<J[2]){I.label=J[2],I.ops.push(K);break}if(J[2])I.ops.pop();I.trys.pop();continue}K=F.call(G,I)}catch(H){K=[6,H],W=0}finally{Y=J=0}if(K[0]&5)throw K[1];return{value:K[0]?K[1]:void 0,done:!0}}},MHA=function(G,F){for(var I in G)if(I!=="default"&&!Object.prototype.hasOwnProperty.call(F,I))Fz1(F,G,I)},Fz1=Object.create?function(G,F,I,Y){if(Y===void 0)Y=I;var W=Object.getOwnPropertyDescriptor(F,I);if(!W||("get"in W?!F.__esModule:W.writable||W.configurable))W={enumerable:!0,get:function(){return F[I]}};Object.defineProperty(G,Y,W)}:function(G,F,I,Y){if(Y===void 0)Y=I;G[Y]=F[I]},Gz1=function(G){var F=typeof Symbol==="function"&&Symbol.iterator,I=F&&G[F],Y=0;if(I)return I.call(G);if(G&&typeof G.length==="number")return{next:function(){if(G&&Y>=G.length)G=void 0;return{value:G&&G[Y++],done:!G}}};throw new TypeError(F?"Object is not iterable.":"Symbol.iterator is not defined.")},Ct1=function(G,F){var I=typeof Symbol==="function"&&G[Symbol.iterator];if(!I)return G;var Y=I.call(G),W,J=[],X;try{while((F===void 0||F-- >0)&&!(W=Y.next()).done)J.push(W.value)}catch(V){X={error:V}}finally{try{if(W&&!W.done&&(I=Y.return))I.call(Y)}finally{if(X)throw X.error}}return J},RHA=function(){for(var G=[],F=0;F<arguments.length;F++)G=G.concat(Ct1(arguments[F]));return G},OHA=function(){for(var G=0,F=0,I=arguments.length;F<I;F++)G+=arguments[F].length;for(var Y=Array(G),W=0,F=0;F<I;F++)for(var J=arguments[F],X=0,V=J.length;X<V;X++,W++)Y[W]=J[X];return Y},THA=function(G,F,I){if(I||arguments.length===2){for(var Y=0,W=F.length,J;Y<W;Y++)if(J||!(Y in F)){if(!J)J=Array.prototype.slice.call(F,0,Y);J[Y]=F[Y]}}return G.concat(J||Array.prototype.slice.call(F))},Qn=function(G){return this instanceof Qn?(this.v=G,this):new Qn(G)},PHA=function(G,F,I){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var Y=I.apply(G,F||[]),W,J=[];return W=Object.create((typeof AsyncIterator==="function"?AsyncIterator:Object).prototype),V("next"),V("throw"),V("return",X),W[Symbol.asyncIterator]=function(){return this},W;function X(L){return function(N){return Promise.resolve(N).then(L,z)}}function V(L,N){if(Y[L]){if(W[L]=function(O){return new Promise(function(R,T){J.push([L,O,R,T])>1||C(L,O)})},N)W[L]=N(W[L])}}function C(L,N){try{K(Y[L](N))}catch(O){$(J[0][3],O)}}function K(L){L.value instanceof Qn?Promise.resolve(L.value.v).then(H,z):$(J[0][2],L)}function H(L){C("next",L)}function z(L){C("throw",L)}function $(L,N){if(L(N),J.shift(),J.length)C(J[0][0],J[0][1])}},SHA=function(G){var F,I;return F={},Y("next"),Y("throw",function(W){throw W}),Y("return"),F[Symbol.iterator]=function(){return this},F;function Y(W,J){F[W]=G[W]?function(X){return(I=!I)?{value:Qn(G[W](X)),done:!1}:J?J(X):X}:J}},jHA=function(G){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var F=G[Symbol.asyncIterator],I;return F?F.call(G):(G=typeof Gz1==="function"?Gz1(G):G[Symbol.iterator](),I={},Y("next"),Y("throw"),Y("return"),I[Symbol.asyncIterator]=function(){return this},I);function Y(J){I[J]=G[J]&&function(X){return new Promise(function(V,C){X=G[J](X),W(V,C,X.done,X.value)})}}function W(J,X,V,C){Promise.resolve(C).then(function(K){J({value:K,done:V})},X)}},yHA=function(G,F){if(Object.defineProperty)Object.defineProperty(G,"raw",{value:F});else G.raw=F;return G};var Q=Object.create?function(G,F){Object.defineProperty(G,"default",{enumerable:!0,value:F})}:function(G,F){G.default=F},D=function(G){return D=Object.getOwnPropertyNames||function(F){var I=[];for(var Y in F)if(Object.prototype.hasOwnProperty.call(F,Y))I[I.length]=Y;return I},D(G)};kHA=function(G){if(G&&G.__esModule)return G;var F={};if(G!=null){for(var I=D(G),Y=0;Y<I.length;Y++)if(I[Y]!=="default")Fz1(F,G,I[Y])}return Q(F,G),F},_HA=function(G){return G&&G.__esModule?G:{default:G}},xHA=function(G,F,I,Y){if(I==="a"&&!Y)throw new TypeError("Private accessor was defined without a getter");if(typeof F==="function"?G!==F||!Y:!F.has(G))throw new TypeError("Cannot read private member from an object whose class did not declare it");return I==="m"?Y:I==="a"?Y.call(G):Y?Y.value:F.get(G)},vHA=function(G,F,I,Y,W){if(Y==="m")throw new TypeError("Private method is not writable");if(Y==="a"&&!W)throw new TypeError("Private accessor was defined without a setter");if(typeof F==="function"?G!==F||!W:!F.has(G))throw new TypeError("Cannot write private member to an object whose class did not declare it");return Y==="a"?W.call(G,I):W?W.value=I:F.set(G,I),I},bHA=function(G,F){if(F===null||typeof F!=="object"&&typeof F!=="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof G==="function"?F===G:G.has(F)},fHA=function(G,F,I){if(F!==null&&F!==void 0){if(typeof F!=="object"&&typeof F!=="function")throw new TypeError("Object expected.");var Y,W;if(I){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");Y=F[Symbol.asyncDispose]}if(Y===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");if(Y=F[Symbol.dispose],I)W=Y}if(typeof Y!=="function")throw new TypeError("Object not disposable.");if(W)Y=function(){try{W.call(this)}catch(J){return Promise.reject(J)}};G.stack.push({value:F,dispose:Y,async:I})}else if(I)G.stack.push({async:!0});return F};var Z=typeof SuppressedError==="function"?SuppressedError:function(G,F,I){var Y=new Error(I);return Y.name="SuppressedError",Y.error=G,Y.suppressed=F,Y};hHA=function(G){function F(J){G.error=G.hasError?new Z(J,G.error,"An error was suppressed during disposal."):J,G.hasError=!0}var I,Y=0;function W(){while(I=G.stack.pop())try{if(!I.async&&Y===1)return Y=0,G.stack.push(I),Promise.resolve().then(W);if(I.dispose){var J=I.dispose.call(I.value);if(I.async)return Y|=2,Promise.resolve(J).then(W,function(X){return F(X),W()})}else Y|=1}catch(X){F(X)}if(Y===1)return G.hasError?Promise.reject(G.error):Promise.resolve();if(G.hasError)throw G.error}return W()},gHA=function(G,F){if(typeof G==="string"&&/^\.\.?\//.test(G))return G.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,function(I,Y,W,J,X){return Y?F?".jsx":".js":W&&(!J||!X)?I:W+J+"."+X.toLowerCase()+"js"});return G},A("__extends",VHA),A("__assign",CHA),A("__rest",KHA),A("__decorate",HHA),A("__param",zHA),A("__esDecorate",EHA),A("__runInitializers",UHA),A("__propKey",wHA),A("__setFunctionName",$HA),A("__metadata",qHA),A("__awaiter",NHA),A("__generator",LHA),A("__exportStar",MHA),A("__createBinding",Fz1),A("__values",Gz1),A("__read",Ct1),A("__spread",RHA),A("__spreadArrays",OHA),A("__spreadArray",THA),A("__await",Qn),A("__asyncGenerator",PHA),A("__asyncDelegator",SHA),A("__asyncValues",jHA),A("__makeTemplateObject",yHA),A("__importStar",kHA),A("__importDefault",_HA),A("__classPrivateFieldGet",xHA),A("__classPrivateFieldSet",vHA),A("__classPrivateFieldIn",bHA),A("__addDisposableResource",fHA),A("__disposeResources",hHA),A("__rewriteRelativeImportExtension",gHA)})});
var Mo1=E((bXA)=>{Object.defineProperty(bXA,"__esModule",{value:!0});bXA.ByteArrayCollector=void 0;class vXA{constructor(A){this.allocByteArray=A,this.byteLength=0,this.byteArrays=[]}push(A){this.byteArrays.push(A),this.byteLength+=A.byteLength}flush(){if(this.byteArrays.length===1){let Q=this.byteArrays[0];return this.reset(),Q}let A=this.allocByteArray(this.byteLength),B=0;for(let Q=0;Q<this.byteArrays.length;++Q){let D=this.byteArrays[Q];A.set(D,B),B+=D.byteLength}return this.reset(),A}reset(){this.byteArrays=[],this.byteLength=0}}bXA.ByteArrayCollector=vXA});
var Mw=E((YZ5,BEA)=>{var{defineProperty:Mz1,getOwnPropertyDescriptor:SCQ,getOwnPropertyNames:jCQ}=Object,yCQ=Object.prototype.hasOwnProperty,Gz=(A,B)=>Mz1(A,"name",{value:B,configurable:!0}),kCQ=(A,B)=>{for(var Q in B)Mz1(A,Q,{get:B[Q],enumerable:!0})},_CQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of jCQ(B))if(!yCQ.call(A,Z)&&Z!==Q)Mz1(A,Z,{get:()=>B[Z],enumerable:!(D=SCQ(B,Z))||D.enumerable})}return A},xCQ=(A)=>_CQ(Mz1({},"__esModule",{value:!0}),A),AEA={};kCQ(AEA,{constructStack:()=>yt1});BEA.exports=xCQ(AEA);var Th=Gz((A,B)=>{let Q=[];if(A)Q.push(A);if(B)for(let D of B)Q.push(D);return Q},"getAllAliases"),ey=Gz((A,B)=>{return`${A||"anonymous"}${B&&B.length>0?` (a.k.a. ${B.join(",")})`:""}`},"getMiddlewareNameWithAliases"),yt1=Gz(()=>{let A=[],B=[],Q=!1,D=new Set,Z=Gz((X)=>X.sort((V,C)=>tzA[C.step]-tzA[V.step]||ezA[C.priority||"normal"]-ezA[V.priority||"normal"]),"sort"),G=Gz((X)=>{let V=!1,C=Gz((K)=>{let H=Th(K.name,K.aliases);if(H.includes(X)){V=!0;for(let z of H)D.delete(z);return!1}return!0},"filterCb");return A=A.filter(C),B=B.filter(C),V},"removeByName"),F=Gz((X)=>{let V=!1,C=Gz((K)=>{if(K.middleware===X){V=!0;for(let H of Th(K.name,K.aliases))D.delete(H);return!1}return!0},"filterCb");return A=A.filter(C),B=B.filter(C),V},"removeByReference"),I=Gz((X)=>{return A.forEach((V)=>{X.add(V.middleware,{...V})}),B.forEach((V)=>{X.addRelativeTo(V.middleware,{...V})}),X.identifyOnResolve?.(J.identifyOnResolve()),X},"cloneTo"),Y=Gz((X)=>{let V=[];return X.before.forEach((C)=>{if(C.before.length===0&&C.after.length===0)V.push(C);else V.push(...Y(C))}),V.push(X),X.after.reverse().forEach((C)=>{if(C.before.length===0&&C.after.length===0)V.push(C);else V.push(...Y(C))}),V},"expandRelativeMiddlewareList"),W=Gz((X=!1)=>{let V=[],C=[],K={};return A.forEach((z)=>{let $={...z,before:[],after:[]};for(let L of Th($.name,$.aliases))K[L]=$;V.push($)}),B.forEach((z)=>{let $={...z,before:[],after:[]};for(let L of Th($.name,$.aliases))K[L]=$;C.push($)}),C.forEach((z)=>{if(z.toMiddleware){let $=K[z.toMiddleware];if($===void 0){if(X)return;throw new Error(`${z.toMiddleware} is not found when adding ${ey(z.name,z.aliases)} middleware ${z.relation} ${z.toMiddleware}`)}if(z.relation==="after")$.after.push(z);if(z.relation==="before")$.before.push(z)}}),Z(V).map(Y).reduce((z,$)=>{return z.push(...$),z},[])},"getMiddlewareList"),J={add:(X,V={})=>{let{name:C,override:K,aliases:H}=V,z={step:"initialize",priority:"normal",middleware:X,...V},$=Th(C,H);if($.length>0){if($.some((L)=>D.has(L))){if(!K)throw new Error(`Duplicate middleware name '${ey(C,H)}'`);for(let L of $){let N=A.findIndex((R)=>R.name===L||R.aliases?.some((T)=>T===L));if(N===-1)continue;let O=A[N];if(O.step!==z.step||z.priority!==O.priority)throw new Error(`"${ey(O.name,O.aliases)}" middleware with ${O.priority} priority in ${O.step} step cannot be overridden by "${ey(C,H)}" middleware with ${z.priority} priority in ${z.step} step.`);A.splice(N,1)}}for(let L of $)D.add(L)}A.push(z)},addRelativeTo:(X,V)=>{let{name:C,override:K,aliases:H}=V,z={middleware:X,...V},$=Th(C,H);if($.length>0){if($.some((L)=>D.has(L))){if(!K)throw new Error(`Duplicate middleware name '${ey(C,H)}'`);for(let L of $){let N=B.findIndex((R)=>R.name===L||R.aliases?.some((T)=>T===L));if(N===-1)continue;let O=B[N];if(O.toMiddleware!==z.toMiddleware||O.relation!==z.relation)throw new Error(`"${ey(O.name,O.aliases)}" middleware ${O.relation} "${O.toMiddleware}" middleware cannot be overridden by "${ey(C,H)}" middleware ${z.relation} "${z.toMiddleware}" middleware.`);B.splice(N,1)}}for(let L of $)D.add(L)}B.push(z)},clone:()=>I(yt1()),use:(X)=>{X.applyToStack(J)},remove:(X)=>{if(typeof X==="string")return G(X);else return F(X)},removeByTag:(X)=>{let V=!1,C=Gz((K)=>{let{tags:H,name:z,aliases:$}=K;if(H&&H.includes(X)){let L=Th(z,$);for(let N of L)D.delete(N);return V=!0,!1}return!0},"filterCb");return A=A.filter(C),B=B.filter(C),V},concat:(X)=>{let V=I(yt1());return V.use(X),V.identifyOnResolve(Q||V.identifyOnResolve()||(X.identifyOnResolve?.()??!1)),V},applyToStack:I,identify:()=>{return W(!0).map((X)=>{let V=X.step??X.relation+" "+X.toMiddleware;return ey(X.name,X.aliases)+" - "+V})},identifyOnResolve(X){if(typeof X==="boolean")Q=X;return Q},resolve:(X,V)=>{for(let C of W().map((K)=>K.middleware).reverse())X=C(X,V);if(Q)console.log(J.identify());return X}};return J},"constructStack"),tzA={initialize:5,serialize:4,build:3,finalizeRequest:2,deserialize:1},ezA={high:3,normal:2,low:1}});
var NEA=E((wZ5,qEA)=>{var hKQ=Oz1();function gKQ(A,B){let Q={};if(A[B+3]==="O"&&A[B+4]==="C"&&A[B+5]==="T"&&A[B+6]==="Y"&&A[B+7]==="P"&&A[B+8]==="E"){B=B+9;let D=1,Z=!1,G=!1,F="";for(;B<A.length;B++)if(A[B]==="<"&&!G){if(Z&&dKQ(A,B)){if(B+=7,[entityName,val,B]=uKQ(A,B+1),val.indexOf("&")===-1)Q[iKQ(entityName)]={regx:RegExp(`&${entityName};`,"g"),val}}else if(Z&&cKQ(A,B))B+=8;else if(Z&&lKQ(A,B))B+=8;else if(Z&&pKQ(A,B))B+=9;else if(mKQ)G=!0;else throw new Error("Invalid DOCTYPE");D++,F=""}else if(A[B]===">"){if(G){if(A[B-1]==="-"&&A[B-2]==="-")G=!1,D--}else D--;if(D===0)break}else if(A[B]==="[")Z=!0;else F+=A[B];if(D!==0)throw new Error("Unclosed DOCTYPE")}else throw new Error("Invalid Tag instead of DOCTYPE");return{entities:Q,i:B}}function uKQ(A,B){let Q="";for(;B<A.length&&(A[B]!=="'"&&A[B]!=='"');B++)Q+=A[B];if(Q=Q.trim(),Q.indexOf(" ")!==-1)throw new Error("External entites are not supported");let D=A[B++],Z="";for(;B<A.length&&A[B]!==D;B++)Z+=A[B];return[Q,Z,B]}function mKQ(A,B){if(A[B+1]==="!"&&A[B+2]==="-"&&A[B+3]==="-")return!0;return!1}function dKQ(A,B){if(A[B+1]==="!"&&A[B+2]==="E"&&A[B+3]==="N"&&A[B+4]==="T"&&A[B+5]==="I"&&A[B+6]==="T"&&A[B+7]==="Y")return!0;return!1}function cKQ(A,B){if(A[B+1]==="!"&&A[B+2]==="E"&&A[B+3]==="L"&&A[B+4]==="E"&&A[B+5]==="M"&&A[B+6]==="E"&&A[B+7]==="N"&&A[B+8]==="T")return!0;return!1}function lKQ(A,B){if(A[B+1]==="!"&&A[B+2]==="A"&&A[B+3]==="T"&&A[B+4]==="T"&&A[B+5]==="L"&&A[B+6]==="I"&&A[B+7]==="S"&&A[B+8]==="T")return!0;return!1}function pKQ(A,B){if(A[B+1]==="!"&&A[B+2]==="N"&&A[B+3]==="O"&&A[B+4]==="T"&&A[B+5]==="A"&&A[B+6]==="T"&&A[B+7]==="I"&&A[B+8]==="O"&&A[B+9]==="N")return!0;return!1}function iKQ(A){if(hKQ.isName(A))return A;else throw new Error(`Invalid entity name ${A}`)}qEA.exports=gKQ});
var NLA=E(($LA)=>{Object.defineProperty($LA,"__esModule",{value:!0});$LA.getRuntimeConfig=void 0;var IRQ=WI(),YRQ=CB(),WRQ=g4(),JRQ=JZ(),ULA=ty(),wLA=cB(),XRQ=ie1(),VRQ=ELA(),CRQ=(A)=>{return{apiVersion:"2019-06-10",base64Decoder:A?.base64Decoder??ULA.fromBase64,base64Encoder:A?.base64Encoder??ULA.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??VRQ.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??XRQ.defaultSSOHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new IRQ.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new YRQ.NoAuthSigner}],logger:A?.logger??new WRQ.NoOpLogger,serviceId:A?.serviceId??"SSO",urlParser:A?.urlParser??JRQ.parseUrl,utf8Decoder:A?.utf8Decoder??wLA.fromUtf8,utf8Encoder:A?.utf8Encoder??wLA.toUtf8}};$LA.getRuntimeConfig=CRQ});
var NOA=E(($OA)=>{Object.defineProperty($OA,"__esModule",{value:!0});$OA.getRuntimeConfig=void 0;var hPQ=WI(),gPQ=CB(),uPQ=g4(),mPQ=JZ(),UOA=ty(),wOA=cB(),dPQ=H10(),cPQ=EOA(),lPQ=(A)=>{return{apiVersion:"2011-06-15",base64Decoder:A?.base64Decoder??UOA.fromBase64,base64Encoder:A?.base64Encoder??UOA.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??cPQ.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??dPQ.defaultSTSHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new hPQ.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new gPQ.NoAuthSigner}],logger:A?.logger??new uPQ.NoOpLogger,serviceId:A?.serviceId??"STS",urlParser:A?.urlParser??mPQ.parseUrl,utf8Decoder:A?.utf8Decoder??wOA.fromUtf8,utf8Encoder:A?.utf8Encoder??wOA.toUtf8}};$OA.getRuntimeConfig=lPQ});
var ONA=E((RNA)=>{Object.defineProperty(RNA,"__esModule",{value:!0});RNA.createGetRequest=BMQ;RNA.getCredentials=QMQ;var de1=Q9(),tLQ=sJ(),eLQ=g4(),AMQ=ry();function BMQ(A){return new tLQ.HttpRequest({protocol:A.protocol,hostname:A.hostname,port:Number(A.port),path:A.pathname,query:Array.from(A.searchParams.entries()).reduce((B,[Q,D])=>{return B[Q]=D,B},{}),fragment:A.hash})}async function QMQ(A,B){let D=await AMQ.sdkStreamMixin(A.body).transformToString();if(A.statusCode===200){let Z=JSON.parse(D);if(typeof Z.AccessKeyId!=="string"||typeof Z.SecretAccessKey!=="string"||typeof Z.Token!=="string"||typeof Z.Expiration!=="string")throw new de1.CredentialsProviderError("HTTP credential provider response not of the required format, an object matching: { AccessKeyId: string, SecretAccessKey: string, Token: string, Expiration: string(rfc3339) }",{logger:B});return{accessKeyId:Z.AccessKeyId,secretAccessKey:Z.SecretAccessKey,sessionToken:Z.Token,expiration:eLQ.parseRfc3339DateTime(Z.Expiration)}}if(A.statusCode>=400&&A.statusCode<500){let Z={};try{Z=JSON.parse(D)}catch(G){}throw Object.assign(new de1.CredentialsProviderError(`Server responded with status: ${A.statusCode}`,{logger:B}),{Code:Z.Code,Message:Z.Message})}throw new de1.CredentialsProviderError(`Server responded with status: ${A.statusCode}`,{logger:B})}});
var Oz1=E((zKQ)=>{var VKQ=":A-Za-z_\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.\\d\\u00B7\\u0300-\\u036F\\u203F-\\u2040",JEA="[:A-Za-z_\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]["+VKQ+"]*",CKQ=new RegExp("^"+JEA+"$"),KKQ=function(A,B){let Q=[],D=B.exec(A);while(D){let Z=[];Z.startIndex=B.lastIndex-D[0].length;let G=D.length;for(let F=0;F<G;F++)Z.push(D[F]);Q.push(Z),D=B.exec(A)}return Q},HKQ=function(A){let B=CKQ.exec(A);return!(B===null||typeof B==="undefined")};zKQ.isExist=function(A){return typeof A!=="undefined"};zKQ.isEmptyObject=function(A){return Object.keys(A).length===0};zKQ.merge=function(A,B,Q){if(B){let D=Object.keys(B),Z=D.length;for(let G=0;G<Z;G++)if(Q==="strict")A[D[G]]=[B[D[G]]];else A[D[G]]=B[D[G]]}};zKQ.getValue=function(A){if(zKQ.isExist(A))return A;else return""};zKQ.isName=HKQ;zKQ.getAllMatches=KKQ;zKQ.nameRegexp=JEA});
var PEA=E((qZ5,TEA)=>{var REA=Oz1(),KQ1=$EA(),tKQ=NEA(),eKQ=MEA();class OEA{constructor(A){this.options=A,this.currentNode=null,this.tagsNodeStack=[],this.docTypeEntities={},this.lastEntities={apos:{regex:/&(apos|#39|#x27);/g,val:"'"},gt:{regex:/&(gt|#62|#x3E);/g,val:">"},lt:{regex:/&(lt|#60|#x3C);/g,val:"<"},quot:{regex:/&(quot|#34|#x22);/g,val:'"'}},this.ampEntity={regex:/&(amp|#38|#x26);/g,val:"&"},this.htmlEntities={space:{regex:/&(nbsp|#160);/g,val:" "},cent:{regex:/&(cent|#162);/g,val:"¢"},pound:{regex:/&(pound|#163);/g,val:"£"},yen:{regex:/&(yen|#165);/g,val:"¥"},euro:{regex:/&(euro|#8364);/g,val:"€"},copyright:{regex:/&(copy|#169);/g,val:"©"},reg:{regex:/&(reg|#174);/g,val:"®"},inr:{regex:/&(inr|#8377);/g,val:"₹"},num_dec:{regex:/&#([0-9]{1,7});/g,val:(B,Q)=>String.fromCharCode(Number.parseInt(Q,10))},num_hex:{regex:/&#x([0-9a-fA-F]{1,6});/g,val:(B,Q)=>String.fromCharCode(Number.parseInt(Q,16))}},this.addExternalEntities=AHQ,this.parseXml=GHQ,this.parseTextData=BHQ,this.resolveNameSpace=QHQ,this.buildAttributesMap=ZHQ,this.isItStopNode=WHQ,this.replaceEntitiesValue=IHQ,this.readStopNodeData=XHQ,this.saveTextToParentTag=YHQ,this.addChild=FHQ}}function AHQ(A){let B=Object.keys(A);for(let Q=0;Q<B.length;Q++){let D=B[Q];this.lastEntities[D]={regex:new RegExp("&"+D+";","g"),val:A[D]}}}function BHQ(A,B,Q,D,Z,G,F){if(A!==void 0){if(this.options.trimValues&&!D)A=A.trim();if(A.length>0){if(!F)A=this.replaceEntitiesValue(A);let I=this.options.tagValueProcessor(B,A,Q,Z,G);if(I===null||I===void 0)return A;else if(typeof I!==typeof A||I!==A)return I;else if(this.options.trimValues)return dt1(A,this.options.parseTagValue,this.options.numberParseOptions);else if(A.trim()===A)return dt1(A,this.options.parseTagValue,this.options.numberParseOptions);else return A}}}function QHQ(A){if(this.options.removeNSPrefix){let B=A.split(":"),Q=A.charAt(0)==="/"?"/":"";if(B[0]==="xmlns")return"";if(B.length===2)A=Q+B[1]}return A}var DHQ=new RegExp(`([^\\s=]+)\\s*(=\\s*(['"])([\\s\\S]*?)\\3)?`,"gm");function ZHQ(A,B,Q){if(!this.options.ignoreAttributes&&typeof A==="string"){let D=REA.getAllMatches(A,DHQ),Z=D.length,G={};for(let F=0;F<Z;F++){let I=this.resolveNameSpace(D[F][1]),Y=D[F][4],W=this.options.attributeNamePrefix+I;if(I.length){if(this.options.transformAttributeName)W=this.options.transformAttributeName(W);if(W==="__proto__")W="#__proto__";if(Y!==void 0){if(this.options.trimValues)Y=Y.trim();Y=this.replaceEntitiesValue(Y);let J=this.options.attributeValueProcessor(I,Y,B);if(J===null||J===void 0)G[W]=Y;else if(typeof J!==typeof Y||J!==Y)G[W]=J;else G[W]=dt1(Y,this.options.parseAttributeValue,this.options.numberParseOptions)}else if(this.options.allowBooleanAttributes)G[W]=!0}}if(!Object.keys(G).length)return;if(this.options.attributesGroupName){let F={};return F[this.options.attributesGroupName]=G,F}return G}}var GHQ=function(A){A=A.replace(/\r\n?/g,`
`);let B=new KQ1("!xml"),Q=B,D="",Z="";for(let G=0;G<A.length;G++)if(A[G]==="<")if(A[G+1]==="/"){let I=Ph(A,">",G,"Closing Tag is not closed."),Y=A.substring(G+2,I).trim();if(this.options.removeNSPrefix){let X=Y.indexOf(":");if(X!==-1)Y=Y.substr(X+1)}if(this.options.transformTagName)Y=this.options.transformTagName(Y);if(Q)D=this.saveTextToParentTag(D,Q,Z);let W=Z.substring(Z.lastIndexOf(".")+1);if(Y&&this.options.unpairedTags.indexOf(Y)!==-1)throw new Error(`Unpaired tag can not be used as closing tag: </${Y}>`);let J=0;if(W&&this.options.unpairedTags.indexOf(W)!==-1)J=Z.lastIndexOf(".",Z.lastIndexOf(".")-1),this.tagsNodeStack.pop();else J=Z.lastIndexOf(".");Z=Z.substring(0,J),Q=this.tagsNodeStack.pop(),D="",G=I}else if(A[G+1]==="?"){let I=mt1(A,G,!1,"?>");if(!I)throw new Error("Pi Tag is not closed.");if(D=this.saveTextToParentTag(D,Q,Z),this.options.ignoreDeclaration&&I.tagName==="?xml"||this.options.ignorePiTags);else{let Y=new KQ1(I.tagName);if(Y.add(this.options.textNodeName,""),I.tagName!==I.tagExp&&I.attrExpPresent)Y[":@"]=this.buildAttributesMap(I.tagExp,Z,I.tagName);this.addChild(Q,Y,Z)}G=I.closeIndex+1}else if(A.substr(G+1,3)==="!--"){let I=Ph(A,"-->",G+4,"Comment is not closed.");if(this.options.commentPropName){let Y=A.substring(G+4,I-2);D=this.saveTextToParentTag(D,Q,Z),Q.add(this.options.commentPropName,[{[this.options.textNodeName]:Y}])}G=I}else if(A.substr(G+1,2)==="!D"){let I=tKQ(A,G);this.docTypeEntities=I.entities,G=I.i}else if(A.substr(G+1,2)==="!["){let I=Ph(A,"]]>",G,"CDATA is not closed.")-2,Y=A.substring(G+9,I);D=this.saveTextToParentTag(D,Q,Z);let W=this.parseTextData(Y,Q.tagname,Z,!0,!1,!0,!0);if(W==null)W="";if(this.options.cdataPropName)Q.add(this.options.cdataPropName,[{[this.options.textNodeName]:Y}]);else Q.add(this.options.textNodeName,W);G=I+2}else{let I=mt1(A,G,this.options.removeNSPrefix),Y=I.tagName,W=I.rawTagName,J=I.tagExp,X=I.attrExpPresent,V=I.closeIndex;if(this.options.transformTagName)Y=this.options.transformTagName(Y);if(Q&&D){if(Q.tagname!=="!xml")D=this.saveTextToParentTag(D,Q,Z,!1)}let C=Q;if(C&&this.options.unpairedTags.indexOf(C.tagname)!==-1)Q=this.tagsNodeStack.pop(),Z=Z.substring(0,Z.lastIndexOf("."));if(Y!==B.tagname)Z+=Z?"."+Y:Y;if(this.isItStopNode(this.options.stopNodes,Z,Y)){let K="";if(J.length>0&&J.lastIndexOf("/")===J.length-1){if(Y[Y.length-1]==="/")Y=Y.substr(0,Y.length-1),Z=Z.substr(0,Z.length-1),J=Y;else J=J.substr(0,J.length-1);G=I.closeIndex}else if(this.options.unpairedTags.indexOf(Y)!==-1)G=I.closeIndex;else{let z=this.readStopNodeData(A,W,V+1);if(!z)throw new Error(`Unexpected end of ${W}`);G=z.i,K=z.tagContent}let H=new KQ1(Y);if(Y!==J&&X)H[":@"]=this.buildAttributesMap(J,Z,Y);if(K)K=this.parseTextData(K,Y,Z,!0,X,!0,!0);Z=Z.substr(0,Z.lastIndexOf(".")),H.add(this.options.textNodeName,K),this.addChild(Q,H,Z)}else{if(J.length>0&&J.lastIndexOf("/")===J.length-1){if(Y[Y.length-1]==="/")Y=Y.substr(0,Y.length-1),Z=Z.substr(0,Z.length-1),J=Y;else J=J.substr(0,J.length-1);if(this.options.transformTagName)Y=this.options.transformTagName(Y);let K=new KQ1(Y);if(Y!==J&&X)K[":@"]=this.buildAttributesMap(J,Z,Y);this.addChild(Q,K,Z),Z=Z.substr(0,Z.lastIndexOf("."))}else{let K=new KQ1(Y);if(this.tagsNodeStack.push(Q),Y!==J&&X)K[":@"]=this.buildAttributesMap(J,Z,Y);this.addChild(Q,K,Z),Q=K}D="",G=V}}else D+=A[G];return B.child};function FHQ(A,B,Q){let D=this.options.updateTag(B.tagname,Q,B[":@"]);if(D===!1);else if(typeof D==="string")B.tagname=D,A.addChild(B);else A.addChild(B)}var IHQ=function(A){if(this.options.processEntities){for(let B in this.docTypeEntities){let Q=this.docTypeEntities[B];A=A.replace(Q.regx,Q.val)}for(let B in this.lastEntities){let Q=this.lastEntities[B];A=A.replace(Q.regex,Q.val)}if(this.options.htmlEntities)for(let B in this.htmlEntities){let Q=this.htmlEntities[B];A=A.replace(Q.regex,Q.val)}A=A.replace(this.ampEntity.regex,this.ampEntity.val)}return A};function YHQ(A,B,Q,D){if(A){if(D===void 0)D=Object.keys(B.child).length===0;if(A=this.parseTextData(A,B.tagname,Q,!1,B[":@"]?Object.keys(B[":@"]).length!==0:!1,D),A!==void 0&&A!=="")B.add(this.options.textNodeName,A);A=""}return A}function WHQ(A,B,Q){let D="*."+Q;for(let Z in A){let G=A[Z];if(D===G||B===G)return!0}return!1}function JHQ(A,B,Q=">"){let D,Z="";for(let G=B;G<A.length;G++){let F=A[G];if(D){if(F===D)D=""}else if(F==='"'||F==="'")D=F;else if(F===Q[0])if(Q[1]){if(A[G+1]===Q[1])return{data:Z,index:G}}else return{data:Z,index:G};else if(F==="\t")F=" ";Z+=F}}function Ph(A,B,Q,D){let Z=A.indexOf(B,Q);if(Z===-1)throw new Error(D);else return Z+B.length-1}function mt1(A,B,Q,D=">"){let Z=JHQ(A,B+1,D);if(!Z)return;let{data:G,index:F}=Z,I=G.search(/\s/),Y=G,W=!0;if(I!==-1)Y=G.substring(0,I),G=G.substring(I+1).trimStart();let J=Y;if(Q){let X=Y.indexOf(":");if(X!==-1)Y=Y.substr(X+1),W=Y!==Z.data.substr(X+1)}return{tagName:Y,tagExp:G,closeIndex:F,attrExpPresent:W,rawTagName:J}}function XHQ(A,B,Q){let D=Q,Z=1;for(;Q<A.length;Q++)if(A[Q]==="<")if(A[Q+1]==="/"){let G=Ph(A,">",Q,`${B} is not closed`);if(A.substring(Q+2,G).trim()===B){if(Z--,Z===0)return{tagContent:A.substring(D,Q),i:G}}Q=G}else if(A[Q+1]==="?")Q=Ph(A,"?>",Q+1,"StopNode is not closed.");else if(A.substr(Q+1,3)==="!--")Q=Ph(A,"-->",Q+3,"StopNode is not closed.");else if(A.substr(Q+1,2)==="![")Q=Ph(A,"]]>",Q,"StopNode is not closed.")-2;else{let G=mt1(A,Q,">");if(G){if((G&&G.tagName)===B&&G.tagExp[G.tagExp.length-1]!=="/")Z++;Q=G.closeIndex}}}function dt1(A,B,Q){if(B&&typeof A==="string"){let D=A.trim();if(D==="true")return!0;else if(D==="false")return!1;else return eKQ(A,Q)}else if(REA.isExist(A))return A;else return""}TEA.exports=OEA});
var POA=E((OOA)=>{Object.defineProperty(OOA,"__esModule",{value:!0});OOA.getRuntimeConfig=void 0;var pPQ=Mh(),iPQ=pPQ.__importDefault(Z10()),$10=WI(),LOA=PQ1(),PE1=K4(),nPQ=CB(),aPQ=gG(),MOA=u4(),bh=JD(),ROA=k3(),sPQ=uG(),rPQ=sZ(),oPQ=NOA(),tPQ=g4(),ePQ=mG(),ASQ=g4(),BSQ=(A)=>{ASQ.emitWarningIfUnsupportedVersion(process.version);let B=ePQ.resolveDefaultsModeConfig(A),Q=()=>B().then(tPQ.loadConfigsForDefaultMode),D=oPQ.getRuntimeConfig(A);$10.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile,logger:D.logger};return{...D,...A,runtime:"node",defaultsMode:B,authSchemePreference:A?.authSchemePreference??bh.loadConfig($10.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS,Z),bodyLengthChecker:A?.bodyLengthChecker??sPQ.calculateBodyLength,defaultUserAgentProvider:A?.defaultUserAgentProvider??LOA.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:iPQ.default.version}),httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(G)=>G.getIdentityProvider("aws.auth#sigv4")||(async(F)=>await A.credentialDefaultProvider(F?.__config||{})()),signer:new $10.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(G)=>G.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new nPQ.NoAuthSigner}],maxAttempts:A?.maxAttempts??bh.loadConfig(MOA.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??bh.loadConfig(PE1.NODE_REGION_CONFIG_OPTIONS,{...PE1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:ROA.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??bh.loadConfig({...MOA.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||rPQ.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??aPQ.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??ROA.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??bh.loadConfig(PE1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??bh.loadConfig(PE1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??bh.loadConfig(LOA.NODE_APP_ID_CONFIG_OPTIONS,Z)}};OOA.getRuntimeConfig=BSQ});
var PQ1=E((YF5,lNA)=>{var{defineProperty:XE1,getOwnPropertyDescriptor:PMQ,getOwnPropertyNames:SMQ}=Object,jMQ=Object.prototype.hasOwnProperty,JE1=(A,B)=>XE1(A,"name",{value:B,configurable:!0}),yMQ=(A,B)=>{for(var Q in B)XE1(A,Q,{get:B[Q],enumerable:!0})},kMQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of SMQ(B))if(!jMQ.call(A,Z)&&Z!==Q)XE1(A,Z,{get:()=>B[Z],enumerable:!(D=PMQ(B,Z))||D.enumerable})}return A},_MQ=(A)=>kMQ(XE1({},"__esModule",{value:!0}),A),gNA={};yMQ(gNA,{NODE_APP_ID_CONFIG_OPTIONS:()=>hMQ,UA_APP_ID_ENV_NAME:()=>dNA,UA_APP_ID_INI_NAME:()=>cNA,createDefaultUserAgentProvider:()=>mNA,crtAvailability:()=>uNA,defaultUserAgent:()=>vMQ});lNA.exports=_MQ(gNA);var hNA=J1("os"),ne1=J1("process"),uNA={isCrtAvailable:!1},xMQ=JE1(()=>{if(uNA.isCrtAvailable)return["md/crt-avail"];return null},"isCrtAvailable"),mNA=JE1(({serviceId:A,clientVersion:B})=>{return async(Q)=>{let D=[["aws-sdk-js",B],["ua","2.1"],[`os/${hNA.platform()}`,hNA.release()],["lang/js"],["md/nodejs",`${ne1.versions.node}`]],Z=xMQ();if(Z)D.push(Z);if(A)D.push([`api/${A}`,B]);if(ne1.env.AWS_EXECUTION_ENV)D.push([`exec-env/${ne1.env.AWS_EXECUTION_ENV}`]);let G=await Q?.userAgentAppId?.();return G?[...D,[`app/${G}`]]:[...D]}},"createDefaultUserAgentProvider"),vMQ=mNA,bMQ=In(),dNA="AWS_SDK_UA_APP_ID",cNA="sdk_ua_app_id",fMQ="sdk-ua-app-id",hMQ={environmentVariableSelector:JE1((A)=>A[dNA],"environmentVariableSelector"),configFileSelector:JE1((A)=>A[cNA]??A[fMQ],"configFileSelector"),default:bMQ.DEFAULT_UA_APP_ID}});
var PTA=E((OTA)=>{Object.defineProperty(OTA,"__esModule",{value:!0});OTA.fromTokenFile=void 0;var yjQ=Zz(),kjQ=Q9(),_jQ=J1("fs"),xjQ=D00(),RTA="AWS_WEB_IDENTITY_TOKEN_FILE",vjQ="AWS_ROLE_ARN",bjQ="AWS_ROLE_SESSION_NAME",fjQ=(A={})=>async()=>{A.logger?.debug("@aws-sdk/credential-provider-web-identity - fromTokenFile");let B=A?.webIdentityTokenFile??process.env[RTA],Q=A?.roleArn??process.env[vjQ],D=A?.roleSessionName??process.env[bjQ];if(!B||!Q)throw new kjQ.CredentialsProviderError("Web identity configuration not specified",{logger:A.logger});let Z=await xjQ.fromWebToken({...A,webIdentityToken:_jQ.readFileSync(B,{encoding:"ascii"}),roleArn:Q,roleSessionName:D})();if(B===process.env[RTA])yjQ.setCredentialFeature(Z,"CREDENTIALS_ENV_VARS_STS_WEB_ID_TOKEN","h");return Z};OTA.fromTokenFile=fjQ});
var PY=E((ZZ5,czA)=>{var{defineProperty:Nz1,getOwnPropertyDescriptor:XCQ,getOwnPropertyNames:VCQ}=Object,CCQ=Object.prototype.hasOwnProperty,KCQ=(A,B)=>Nz1(A,"name",{value:B,configurable:!0}),HCQ=(A,B)=>{for(var Q in B)Nz1(A,Q,{get:B[Q],enumerable:!0})},zCQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of VCQ(B))if(!CCQ.call(A,Z)&&Z!==Q)Nz1(A,Z,{get:()=>B[Z],enumerable:!(D=XCQ(B,Z))||D.enumerable})}return A},ECQ=(A)=>zCQ(Nz1({},"__esModule",{value:!0}),A),dzA={};HCQ(dzA,{calculateBodyLength:()=>UCQ});czA.exports=ECQ(dzA);var mzA=typeof TextEncoder=="function"?new TextEncoder:null,UCQ=KCQ((A)=>{if(typeof A==="string"){if(mzA)return mzA.encode(A).byteLength;let B=A.length;for(let Q=B-1;Q>=0;Q--){let D=A.charCodeAt(Q);if(D>127&&D<=2047)B++;else if(D>2047&&D<=65535)B+=2;if(D>=56320&&D<=57343)Q--}return B}else if(typeof A.byteLength==="number")return A.byteLength;else if(typeof A.size==="number")return A.size;throw new Error(`Body Length computation failed for ${A}`)},"calculateBodyLength")});
var Pt1=E((BZ5,uzA)=>{var{defineProperty:qz1,getOwnPropertyDescriptor:nVQ,getOwnPropertyNames:aVQ}=Object,sVQ=Object.prototype.hasOwnProperty,nZ=(A,B)=>qz1(A,"name",{value:B,configurable:!0}),rVQ=(A,B)=>{for(var Q in B)qz1(A,Q,{get:B[Q],enumerable:!0})},oVQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of aVQ(B))if(!sVQ.call(A,Z)&&Z!==Q)qz1(A,Z,{get:()=>B[Z],enumerable:!(D=nVQ(B,Z))||D.enumerable})}return A},tVQ=(A)=>oVQ(qz1({},"__esModule",{value:!0}),A),vzA={};rVQ(vzA,{AWSSDKSigV4Signer:()=>QCQ,AwsSdkSigV4ASigner:()=>ZCQ,AwsSdkSigV4Signer:()=>Tt1,NODE_AUTH_SCHEME_PREFERENCE_OPTIONS:()=>GCQ,NODE_SIGV4A_CONFIG_OPTIONS:()=>YCQ,getBearerTokenEnvKey:()=>bzA,resolveAWSSDKSigV4Config:()=>JCQ,resolveAwsSdkSigV4AConfig:()=>ICQ,resolveAwsSdkSigV4Config:()=>fzA,validateSigningProperties:()=>Ot1});uzA.exports=tVQ(vzA);var eVQ=sJ(),ACQ=sJ(),PzA=nZ((A)=>ACQ.HttpResponse.isInstance(A)?A.headers?.date??A.headers?.Date:void 0,"getDateHeader"),Rt1=nZ((A)=>new Date(Date.now()+A),"getSkewCorrectedDate"),BCQ=nZ((A,B)=>Math.abs(Rt1(B).getTime()-A)>=300000,"isClockSkewed"),SzA=nZ((A,B)=>{let Q=Date.parse(A);if(BCQ(Q,B))return Q-Date.now();return B},"getUpdatedSystemClockOffset"),VQ1=nZ((A,B)=>{if(!B)throw new Error(`Property \`${A}\` is not resolved for AWS SDK SigV4Auth`);return B},"throwSigningPropertyError"),Ot1=nZ(async(A)=>{let B=VQ1("context",A.context),Q=VQ1("config",A.config),D=B.endpointV2?.properties?.authSchemes?.[0],G=await VQ1("signer",Q.signer)(D),F=A?.signingRegion,I=A?.signingRegionSet,Y=A?.signingName;return{config:Q,signer:G,signingRegion:F,signingRegionSet:I,signingName:Y}},"validateSigningProperties"),Tt1=class{static{nZ(this,"AwsSdkSigV4Signer")}async sign(A,B,Q){if(!eVQ.HttpRequest.isInstance(A))throw new Error("The request is not an instance of `HttpRequest` and cannot be signed");let D=await Ot1(Q),{config:Z,signer:G}=D,{signingRegion:F,signingName:I}=D,Y=Q.context;if(Y?.authSchemes?.length??0>1){let[J,X]=Y.authSchemes;if(J?.name==="sigv4a"&&X?.name==="sigv4")F=X?.signingRegion??F,I=X?.signingName??I}return await G.sign(A,{signingDate:Rt1(Z.systemClockOffset),signingRegion:F,signingService:I})}errorHandler(A){return(B)=>{let Q=B.ServerTime??PzA(B.$response);if(Q){let D=VQ1("config",A.config),Z=D.systemClockOffset;if(D.systemClockOffset=SzA(Q,D.systemClockOffset),D.systemClockOffset!==Z&&B.$metadata)B.$metadata.clockSkewCorrected=!0}throw B}}successHandler(A,B){let Q=PzA(A);if(Q){let D=VQ1("config",B.config);D.systemClockOffset=SzA(Q,D.systemClockOffset)}}},QCQ=Tt1,DCQ=sJ(),ZCQ=class extends Tt1{static{nZ(this,"AwsSdkSigV4ASigner")}async sign(A,B,Q){if(!DCQ.HttpRequest.isInstance(A))throw new Error("The request is not an instance of `HttpRequest` and cannot be signed");let{config:D,signer:Z,signingRegion:G,signingRegionSet:F,signingName:I}=await Ot1(Q),W=(await D.sigv4aSigningRegionSet?.()??F??[G]).join(",");return await Z.sign(A,{signingDate:Rt1(D.systemClockOffset),signingRegion:W,signingService:I})}},jzA=nZ((A)=>typeof A==="string"&&A.length>0?A.split(",").map((B)=>B.trim()):[],"getArrayForCommaSeparatedString"),bzA=nZ((A)=>`AWS_BEARER_TOKEN_${A.replace(/[\s-]/g,"_").toUpperCase()}`,"getBearerTokenEnvKey"),yzA="AWS_AUTH_SCHEME_PREFERENCE",kzA="auth_scheme_preference",GCQ={environmentVariableSelector:nZ((A,B)=>{if(B?.signingName){if(bzA(B.signingName)in A)return["httpBearerAuth"]}if(!(yzA in A))return;return jzA(A[yzA])},"environmentVariableSelector"),configFileSelector:nZ((A)=>{if(!(kzA in A))return;return jzA(A[kzA])},"configFileSelector"),default:[]},FCQ=CB(),_zA=Q9(),ICQ=nZ((A)=>{return A.sigv4aSigningRegionSet=FCQ.normalizeProvider(A.sigv4aSigningRegionSet),A},"resolveAwsSdkSigV4AConfig"),YCQ={environmentVariableSelector(A){if(A.AWS_SIGV4A_SIGNING_REGION_SET)return A.AWS_SIGV4A_SIGNING_REGION_SET.split(",").map((B)=>B.trim());throw new _zA.ProviderError("AWS_SIGV4A_SIGNING_REGION_SET not set in env.",{tryNextLink:!0})},configFileSelector(A){if(A.sigv4a_signing_region_set)return(A.sigv4a_signing_region_set??"").split(",").map((B)=>B.trim());throw new _zA.ProviderError("sigv4a_signing_region_set not set in profile.",{tryNextLink:!0})},default:void 0},WCQ=Zz(),Oh=CB(),xzA=TzA(),fzA=nZ((A)=>{let B=A.credentials,Q=!!A.credentials,D=void 0;Object.defineProperty(A,"credentials",{set(W){if(W&&W!==B&&W!==D)Q=!0;B=W;let J=hzA(A,{credentials:B,credentialDefaultProvider:A.credentialDefaultProvider}),X=gzA(A,J);if(Q&&!X.attributed)D=nZ(async(V)=>X(V).then((C)=>WCQ.setCredentialFeature(C,"CREDENTIALS_CODE","e")),"resolvedCredentials"),D.memoized=X.memoized,D.configBound=X.configBound,D.attributed=!0;else D=X},get(){return D},enumerable:!0,configurable:!0}),A.credentials=B;let{signingEscapePath:Z=!0,systemClockOffset:G=A.systemClockOffset||0,sha256:F}=A,I;if(A.signer)I=Oh.normalizeProvider(A.signer);else if(A.regionInfoProvider)I=nZ(()=>Oh.normalizeProvider(A.region)().then(async(W)=>[await A.regionInfoProvider(W,{useFipsEndpoint:await A.useFipsEndpoint(),useDualstackEndpoint:await A.useDualstackEndpoint()})||{},W]).then(([W,J])=>{let{signingRegion:X,signingService:V}=W;A.signingRegion=A.signingRegion||X||J,A.signingName=A.signingName||V||A.serviceId;let C={...A,credentials:A.credentials,region:A.signingRegion,service:A.signingName,sha256:F,uriEscapePath:Z};return new(A.signerConstructor||xzA.SignatureV4)(C)}),"signer");else I=nZ(async(W)=>{W=Object.assign({},{name:"sigv4",signingName:A.signingName||A.defaultSigningName,signingRegion:await Oh.normalizeProvider(A.region)(),properties:{}},W);let{signingRegion:J,signingName:X}=W;A.signingRegion=A.signingRegion||J,A.signingName=A.signingName||X||A.serviceId;let V={...A,credentials:A.credentials,region:A.signingRegion,service:A.signingName,sha256:F,uriEscapePath:Z};return new(A.signerConstructor||xzA.SignatureV4)(V)},"signer");return Object.assign(A,{systemClockOffset:G,signingEscapePath:Z,signer:I})},"resolveAwsSdkSigV4Config"),JCQ=fzA;function hzA(A,{credentials:B,credentialDefaultProvider:Q}){let D;if(B)if(!B?.memoized)D=Oh.memoizeIdentityProvider(B,Oh.isIdentityExpired,Oh.doesIdentityRequireRefresh);else D=B;else if(Q)D=Oh.normalizeProvider(Q(Object.assign({},A,{parentClientConfig:A})));else D=nZ(async()=>{throw new Error("@aws-sdk/core::resolveAwsSdkSigV4Config - `credentials` not provided and no credentialDefaultProvider was configured.")},"credentialsProvider");return D.memoized=!0,D}nZ(hzA,"normalizeCredentialProvider");function gzA(A,B){if(B.configBound)return B;let Q=nZ(async(D)=>B({...D,callerClientConfig:A}),"fn");return Q.memoized=B.memoized,Q.configBound=!0,Q}nZ(gzA,"bindCallerConfig")});
var Q00=E((oF5,MTA)=>{var{defineProperty:jE1,getOwnPropertyDescriptor:HjQ,getOwnPropertyNames:zjQ}=Object,EjQ=Object.prototype.hasOwnProperty,B00=(A,B)=>jE1(A,"name",{value:B,configurable:!0}),UjQ=(A,B)=>{for(var Q in B)jE1(A,Q,{get:B[Q],enumerable:!0})},wjQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of zjQ(B))if(!EjQ.call(A,Z)&&Z!==Q)jE1(A,Z,{get:()=>B[Z],enumerable:!(D=HjQ(B,Z))||D.enumerable})}return A},$jQ=(A)=>wjQ(jE1({},"__esModule",{value:!0}),A),LTA={};UjQ(LTA,{fromProcess:()=>OjQ});MTA.exports=$jQ(LTA);var NTA=D3(),A00=Q9(),qjQ=J1("child_process"),NjQ=J1("util"),LjQ=Zz(),MjQ=B00((A,B,Q)=>{if(B.Version!==1)throw Error(`Profile ${A} credential_process did not return Version 1.`);if(B.AccessKeyId===void 0||B.SecretAccessKey===void 0)throw Error(`Profile ${A} credential_process returned invalid credentials.`);if(B.Expiration){let G=new Date;if(new Date(B.Expiration)<G)throw Error(`Profile ${A} credential_process returned expired credentials.`)}let D=B.AccountId;if(!D&&Q?.[A]?.aws_account_id)D=Q[A].aws_account_id;let Z={accessKeyId:B.AccessKeyId,secretAccessKey:B.SecretAccessKey,...B.SessionToken&&{sessionToken:B.SessionToken},...B.Expiration&&{expiration:new Date(B.Expiration)},...B.CredentialScope&&{credentialScope:B.CredentialScope},...D&&{accountId:D}};return LjQ.setCredentialFeature(Z,"CREDENTIALS_PROCESS","w"),Z},"getValidatedProcessCredentials"),RjQ=B00(async(A,B,Q)=>{let D=B[A];if(B[A]){let Z=D.credential_process;if(Z!==void 0){let G=NjQ.promisify(qjQ.exec);try{let{stdout:F}=await G(Z),I;try{I=JSON.parse(F.trim())}catch{throw Error(`Profile ${A} credential_process returned invalid JSON.`)}return MjQ(A,I,B)}catch(F){throw new A00.CredentialsProviderError(F.message,{logger:Q})}}else throw new A00.CredentialsProviderError(`Profile ${A} did not contain credential_process.`,{logger:Q})}else throw new A00.CredentialsProviderError(`Profile ${A} could not be found in shared credentials file.`,{logger:Q})},"resolveProcessCredentials"),OjQ=B00((A={})=>async({callerClientConfig:B}={})=>{A.logger?.debug("@aws-sdk/credential-provider-process - fromProcess");let Q=await NTA.parseKnownFiles(A);return RjQ(NTA.getProfileName({profile:A.profile??B?.profile}),Q,A.logger)},"fromProcess")});
var Q9=E((aD5,iHA)=>{var{defineProperty:Jz1,getOwnPropertyDescriptor:tXQ,getOwnPropertyNames:eXQ}=Object,AVQ=Object.prototype.hasOwnProperty,Rh=(A,B)=>Jz1(A,"name",{value:B,configurable:!0}),BVQ=(A,B)=>{for(var Q in B)Jz1(A,Q,{get:B[Q],enumerable:!0})},QVQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of eXQ(B))if(!AVQ.call(A,Z)&&Z!==Q)Jz1(A,Z,{get:()=>B[Z],enumerable:!(D=tXQ(B,Z))||D.enumerable})}return A},DVQ=(A)=>QVQ(Jz1({},"__esModule",{value:!0}),A),pHA={};BVQ(pHA,{CredentialsProviderError:()=>ZVQ,ProviderError:()=>Xz1,TokenProviderError:()=>GVQ,chain:()=>FVQ,fromStatic:()=>IVQ,memoize:()=>YVQ});iHA.exports=DVQ(pHA);var Xz1=class A extends Error{constructor(B,Q=!0){let D,Z=!0;if(typeof Q==="boolean")D=void 0,Z=Q;else if(Q!=null&&typeof Q==="object")D=Q.logger,Z=Q.tryNextLink??!0;super(B);this.name="ProviderError",this.tryNextLink=Z,Object.setPrototypeOf(this,A.prototype),D?.debug?.(`@smithy/property-provider ${Z?"->":"(!)"} ${B}`)}static{Rh(this,"ProviderError")}static from(B,Q=!0){return Object.assign(new this(B.message,Q),B)}},ZVQ=class A extends Xz1{constructor(B,Q=!0){super(B,Q);this.name="CredentialsProviderError",Object.setPrototypeOf(this,A.prototype)}static{Rh(this,"CredentialsProviderError")}},GVQ=class A extends Xz1{constructor(B,Q=!0){super(B,Q);this.name="TokenProviderError",Object.setPrototypeOf(this,A.prototype)}static{Rh(this,"TokenProviderError")}},FVQ=Rh((...A)=>async()=>{if(A.length===0)throw new Xz1("No providers in chain");let B;for(let Q of A)try{return await Q()}catch(D){if(B=D,D?.tryNextLink)continue;throw D}throw B},"chain"),IVQ=Rh((A)=>()=>Promise.resolve(A),"fromStatic"),YVQ=Rh((A,B,Q)=>{let D,Z,G,F=!1,I=Rh(async()=>{if(!Z)Z=A();try{D=await Z,G=!0,F=!1}finally{Z=void 0}return D},"coalesceProvider");if(B===void 0)return async(Y)=>{if(!G||Y?.forceRefresh)D=await I();return D};return async(Y)=>{if(!G||Y?.forceRefresh)D=await I();if(F)return D;if(Q&&!Q(D))return F=!0,D;if(B(D))return await I(),D;return D}},"memoize")});
var R6=E((CG5,ewA)=>{var{defineProperty:sz1,getOwnPropertyDescriptor:HwQ,getOwnPropertyNames:zwQ}=Object,EwQ=Object.prototype.hasOwnProperty,Iz=(A,B)=>sz1(A,"name",{value:B,configurable:!0}),UwQ=(A,B)=>{for(var Q in B)sz1(A,Q,{get:B[Q],enumerable:!0})},wwQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of zwQ(B))if(!EwQ.call(A,Z)&&Z!==Q)sz1(A,Z,{get:()=>B[Z],enumerable:!(D=HwQ(B,Z))||D.enumerable})}return A},$wQ=(A)=>wwQ(sz1({},"__esModule",{value:!0}),A),awA={};UwQ(awA,{endpointMiddleware:()=>owA,endpointMiddlewareOptions:()=>twA,getEndpointFromInstructions:()=>swA,getEndpointPlugin:()=>ywQ,resolveEndpointConfig:()=>_wQ,resolveEndpointRequiredConfig:()=>xwQ,resolveParams:()=>rwA,toEndpointV1:()=>Xe1});ewA.exports=$wQ(awA);var qwQ=Iz(async(A)=>{let B=A?.Bucket||"";if(typeof A.Bucket==="string")A.Bucket=B.replace(/#/g,encodeURIComponent("#")).replace(/\?/g,encodeURIComponent("?"));if(OwQ(B)){if(A.ForcePathStyle===!0)throw new Error("Path-style addressing cannot be used with ARN buckets")}else if(!RwQ(B)||B.indexOf(".")!==-1&&!String(A.Endpoint).startsWith("http:")||B.toLowerCase()!==B||B.length<3)A.ForcePathStyle=!0;if(A.DisableMultiRegionAccessPoints)A.disableMultiRegionAccessPoints=!0,A.DisableMRAP=!0;return A},"resolveParamsForS3"),NwQ=/^[a-z0-9][a-z0-9\.\-]{1,61}[a-z0-9]$/,LwQ=/(\d+\.){3}\d+/,MwQ=/\.\./,RwQ=Iz((A)=>NwQ.test(A)&&!LwQ.test(A)&&!MwQ.test(A),"isDnsCompatibleBucketName"),OwQ=Iz((A)=>{let[B,Q,D,,,Z]=A.split(":"),G=B==="arn"&&A.split(":").length>=6,F=Boolean(G&&Q&&D&&Z);if(G&&!F)throw new Error(`Invalid ARN: ${A} was an invalid ARN.`);return F},"isArnBucketName"),TwQ=Iz((A,B,Q)=>{let D=Iz(async()=>{let Z=Q[A]??Q[B];if(typeof Z==="function")return Z();return Z},"configProvider");if(A==="credentialScope"||B==="CredentialScope")return async()=>{let Z=typeof Q.credentials==="function"?await Q.credentials():Q.credentials;return Z?.credentialScope??Z?.CredentialScope};if(A==="accountId"||B==="AccountId")return async()=>{let Z=typeof Q.credentials==="function"?await Q.credentials():Q.credentials;return Z?.accountId??Z?.AccountId};if(A==="endpoint"||B==="endpoint")return async()=>{let Z=await D();if(Z&&typeof Z==="object"){if("url"in Z)return Z.url.href;if("hostname"in Z){let{protocol:G,hostname:F,port:I,path:Y}=Z;return`${G}//${F}${I?":"+I:""}${Y}`}}return Z};return D},"createConfigValueProvider"),PwQ=Je1(),nwA=JZ(),Xe1=Iz((A)=>{if(typeof A==="object"){if("url"in A)return nwA.parseUrl(A.url);return A}return nwA.parseUrl(A)},"toEndpointV1"),swA=Iz(async(A,B,Q,D)=>{if(!Q.endpoint){let F;if(Q.serviceConfiguredEndpoint)F=await Q.serviceConfiguredEndpoint();else F=await PwQ.getEndpointFromConfig(Q.serviceId);if(F)Q.endpoint=()=>Promise.resolve(Xe1(F))}let Z=await rwA(A,B,Q);if(typeof Q.endpointProvider!=="function")throw new Error("config.endpointProvider is not set.");return Q.endpointProvider(Z,D)},"getEndpointFromInstructions"),rwA=Iz(async(A,B,Q)=>{let D={},Z=B?.getEndpointParameterInstructions?.()||{};for(let[G,F]of Object.entries(Z))switch(F.type){case"staticContextParams":D[G]=F.value;break;case"contextParams":D[G]=A[F.name];break;case"clientContextParams":case"builtInParams":D[G]=await TwQ(F.name,G,Q)();break;case"operationContextParams":D[G]=F.get(A);break;default:throw new Error("Unrecognized endpoint parameter instruction: "+JSON.stringify(F))}if(Object.keys(Z).length===0)Object.assign(D,Q);if(String(Q.serviceId).toLowerCase()==="s3")await qwQ(D);return D},"resolveParams"),SwQ=CB(),az1=J5(),owA=Iz(({config:A,instructions:B})=>{return(Q,D)=>async(Z)=>{if(A.endpoint)SwQ.setFeature(D,"ENDPOINT_OVERRIDE","N");let G=await swA(Z.input,{getEndpointParameterInstructions(){return B}},{...A},D);D.endpointV2=G,D.authSchemes=G.properties?.authSchemes;let F=D.authSchemes?.[0];if(F){D.signing_region=F.signingRegion,D.signing_service=F.signingName;let Y=az1.getSmithyContext(D)?.selectedHttpAuthScheme?.httpAuthOption;if(Y)Y.signingProperties=Object.assign(Y.signingProperties||{},{signing_region:F.signingRegion,signingRegion:F.signingRegion,signing_service:F.signingName,signingName:F.signingName,signingRegionSet:F.signingRegionSet},F.properties)}return Q({...Z})}},"endpointMiddleware"),jwQ=j3(),twA={step:"serialize",tags:["ENDPOINT_PARAMETERS","ENDPOINT_V2","ENDPOINT"],name:"endpointV2Middleware",override:!0,relation:"before",toMiddleware:jwQ.serializerMiddlewareOption.name},ywQ=Iz((A,B)=>({applyToStack:(Q)=>{Q.addRelativeTo(owA({config:A,instructions:B}),twA)}}),"getEndpointPlugin"),kwQ=Je1(),_wQ=Iz((A)=>{let B=A.tls??!0,{endpoint:Q,useDualstackEndpoint:D,useFipsEndpoint:Z}=A,G=Q!=null?async()=>Xe1(await az1.normalizeProvider(Q)()):void 0,I=Object.assign(A,{endpoint:G,tls:B,isCustomEndpoint:!!Q,useDualstackEndpoint:az1.normalizeProvider(D??!1),useFipsEndpoint:az1.normalizeProvider(Z??!1)}),Y=void 0;return I.serviceConfiguredEndpoint=async()=>{if(A.serviceId&&!Y)Y=kwQ.getEndpointFromConfig(A.serviceId);return Y},I},"resolveEndpointConfig"),xwQ=Iz((A)=>{let{endpoint:B}=A;if(B===void 0)A.endpoint=async()=>{throw new Error("@smithy/middleware-endpoint: (default endpointRuleSet) endpoint is not set - you must configure an endpoint.")};return A},"resolveEndpointRequiredConfig")});
var RQ1=E((Ow)=>{Object.defineProperty(Ow,"__esModule",{value:!0});Object.defineProperty(Ow,"NIL",{enumerable:!0,get:function(){return VqQ.default}});Object.defineProperty(Ow,"parse",{enumerable:!0,get:function(){return zqQ.default}});Object.defineProperty(Ow,"stringify",{enumerable:!0,get:function(){return HqQ.default}});Object.defineProperty(Ow,"v1",{enumerable:!0,get:function(){return YqQ.default}});Object.defineProperty(Ow,"v3",{enumerable:!0,get:function(){return WqQ.default}});Object.defineProperty(Ow,"v4",{enumerable:!0,get:function(){return JqQ.default}});Object.defineProperty(Ow,"v5",{enumerable:!0,get:function(){return XqQ.default}});Object.defineProperty(Ow,"validate",{enumerable:!0,get:function(){return KqQ.default}});Object.defineProperty(Ow,"version",{enumerable:!0,get:function(){return CqQ.default}});var YqQ=dO(T$A()),WqQ=dO(u$A()),JqQ=dO(a$A()),XqQ=dO(BqA()),VqQ=dO(ZqA()),CqQ=dO(IqA()),KqQ=dO(LQ1()),HqQ=dO(MQ1()),zqQ=dO(Ee1());function dO(A){return A&&A.__esModule?A:{default:A}}});
var RUA=E((aZ5,MUA)=>{var{defineProperty:_z1,getOwnPropertyDescriptor:mzQ,getOwnPropertyNames:dzQ}=Object,czQ=Object.prototype.hasOwnProperty,qUA=(A,B)=>_z1(A,"name",{value:B,configurable:!0}),lzQ=(A,B)=>{for(var Q in B)_z1(A,Q,{get:B[Q],enumerable:!0})},pzQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of dzQ(B))if(!czQ.call(A,Z)&&Z!==Q)_z1(A,Z,{get:()=>B[Z],enumerable:!(D=mzQ(B,Z))||D.enumerable})}return A},izQ=(A)=>pzQ(_z1({},"__esModule",{value:!0}),A),NUA={};lzQ(NUA,{SelectorType:()=>LUA,booleanSelector:()=>nzQ,numberSelector:()=>azQ});MUA.exports=izQ(NUA);var nzQ=qUA((A,B,Q)=>{if(!(B in A))return;if(A[B]==="true")return!0;if(A[B]==="false")return!1;throw new Error(`Cannot load ${Q} "${B}". Expected "true" or "false", got ${A[B]}.`)},"booleanSelector"),azQ=qUA((A,B,Q)=>{if(!(B in A))return;let D=parseInt(A[B],10);if(Number.isNaN(D))throw new TypeError(`Cannot load ${Q} '${B}'. Expected number, got '${A[B]}'.`);return D},"numberSelector"),LUA=((A)=>{return A.ENV="env",A.CONFIG="shared config entry",A})(LUA||{})});
var RVA=E((s75,MVA)=>{var{defineProperty:vH1,getOwnPropertyDescriptor:oFQ,getOwnPropertyNames:tFQ}=Object,eFQ=Object.prototype.hasOwnProperty,AIQ=(A,B)=>vH1(A,"name",{value:B,configurable:!0}),BIQ=(A,B)=>{for(var Q in B)vH1(A,Q,{get:B[Q],enumerable:!0})},QIQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of tFQ(B))if(!eFQ.call(A,Z)&&Z!==Q)vH1(A,Z,{get:()=>B[Z],enumerable:!(D=oFQ(B,Z))||D.enumerable})}return A},DIQ=(A)=>QIQ(vH1({},"__esModule",{value:!0}),A),NVA={};BIQ(NVA,{buildQueryString:()=>LVA});MVA.exports=DIQ(NVA);var Oo1=qVA();function LVA(A){let B=[];for(let Q of Object.keys(A).sort()){let D=A[Q];if(Q=Oo1.escapeUri(Q),Array.isArray(D))for(let Z=0,G=D.length;Z<G;Z++)B.push(`${Q}=${Oo1.escapeUri(D[Z])}`);else{let Z=Q;if(D||typeof D==="string")Z+=`=${Oo1.escapeUri(D)}`;B.push(Z)}}return B.join("&")}AIQ(LVA,"buildQueryString")});
var S7=E((dD5,BHA)=>{var{defineProperty:Qz1,getOwnPropertyDescriptor:WXQ,getOwnPropertyNames:JXQ}=Object,XXQ=Object.prototype.hasOwnProperty,c5=(A,B)=>Qz1(A,"name",{value:B,configurable:!0}),VXQ=(A,B)=>{for(var Q in B)Qz1(A,Q,{get:B[Q],enumerable:!0})},CXQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of JXQ(B))if(!XXQ.call(A,Z)&&Z!==Q)Qz1(A,Z,{get:()=>B[Z],enumerable:!(D=WXQ(B,Z))||D.enumerable})}return A},KXQ=(A)=>CXQ(Qz1({},"__esModule",{value:!0}),A),nKA={};VXQ(nKA,{EndpointCache:()=>HXQ,EndpointError:()=>zV,customEndpointFunctions:()=>Wt1,isIpAddress:()=>aKA,isValidHostLabel:()=>Xt1,resolveEndpoint:()=>xXQ});BHA.exports=KXQ(nKA);var HXQ=class{constructor({size:A,params:B}){if(this.data=new Map,this.parameters=[],this.capacity=A??50,B)this.parameters=B}static{c5(this,"EndpointCache")}get(A,B){let Q=this.hash(A);if(Q===!1)return B();if(!this.data.has(Q)){if(this.data.size>this.capacity+10){let D=this.data.keys(),Z=0;while(!0){let{value:G,done:F}=D.next();if(this.data.delete(G),F||++Z>10)break}}this.data.set(Q,B())}return this.data.get(Q)}size(){return this.data.size}hash(A){let B="",{parameters:Q}=this;if(Q.length===0)return!1;for(let D of Q){let Z=String(A[D]??"");if(Z.includes("|;"))return!1;B+=Z+"|;"}return B}},zXQ=new RegExp("^(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}$"),aKA=c5((A)=>zXQ.test(A)||A.startsWith("[")&&A.endsWith("]"),"isIpAddress"),EXQ=new RegExp("^(?!.*-$)(?!-)[a-zA-Z0-9-]{1,63}$"),Xt1=c5((A,B=!1)=>{if(!B)return EXQ.test(A);let Q=A.split(".");for(let D of Q)if(!Xt1(D))return!1;return!0},"isValidHostLabel"),Wt1={},XQ1="endpoints";function uO(A){if(typeof A!=="object"||A==null)return A;if("ref"in A)return`$${uO(A.ref)}`;if("fn"in A)return`${A.fn}(${(A.argv||[]).map(uO).join(", ")})`;return JSON.stringify(A,null,2)}c5(uO,"toDebugString");var zV=class extends Error{static{c5(this,"EndpointError")}constructor(A){super(A);this.name="EndpointError"}},UXQ=c5((A,B)=>A===B,"booleanEquals"),wXQ=c5((A)=>{let B=A.split("."),Q=[];for(let D of B){let Z=D.indexOf("[");if(Z!==-1){if(D.indexOf("]")!==D.length-1)throw new zV(`Path: '${A}' does not end with ']'`);let G=D.slice(Z+1,-1);if(Number.isNaN(parseInt(G)))throw new zV(`Invalid array index: '${G}' in path: '${A}'`);if(Z!==0)Q.push(D.slice(0,Z));Q.push(G)}else Q.push(D)}return Q},"getAttrPathList"),sKA=c5((A,B)=>wXQ(B).reduce((Q,D)=>{if(typeof Q!=="object")throw new zV(`Index '${D}' in '${B}' not found in '${JSON.stringify(A)}'`);else if(Array.isArray(Q))return Q[parseInt(D)];return Q[D]},A),"getAttr"),$XQ=c5((A)=>A!=null,"isSet"),qXQ=c5((A)=>!A,"not"),Jt1=iKA(),Yt1={[Jt1.EndpointURLScheme.HTTP]:80,[Jt1.EndpointURLScheme.HTTPS]:443},NXQ=c5((A)=>{let B=(()=>{try{if(A instanceof URL)return A;if(typeof A==="object"&&"hostname"in A){let{hostname:V,port:C,protocol:K="",path:H="",query:z={}}=A,$=new URL(`${K}//${V}${C?`:${C}`:""}${H}`);return $.search=Object.entries(z).map(([L,N])=>`${L}=${N}`).join("&"),$}return new URL(A)}catch(V){return null}})();if(!B)return console.error(`Unable to parse ${JSON.stringify(A)} as a whatwg URL.`),null;let Q=B.href,{host:D,hostname:Z,pathname:G,protocol:F,search:I}=B;if(I)return null;let Y=F.slice(0,-1);if(!Object.values(Jt1.EndpointURLScheme).includes(Y))return null;let W=aKA(Z),J=Q.includes(`${D}:${Yt1[Y]}`)||typeof A==="string"&&A.includes(`${D}:${Yt1[Y]}`),X=`${D}${J?`:${Yt1[Y]}`:""}`;return{scheme:Y,authority:X,path:G,normalizedPath:G.endsWith("/")?G:`${G}/`,isIp:W}},"parseURL"),LXQ=c5((A,B)=>A===B,"stringEquals"),MXQ=c5((A,B,Q,D)=>{if(B>=Q||A.length<Q)return null;if(!D)return A.substring(B,Q);return A.substring(A.length-Q,A.length-B)},"substring"),RXQ=c5((A)=>encodeURIComponent(A).replace(/[!*'()]/g,(B)=>`%${B.charCodeAt(0).toString(16).toUpperCase()}`),"uriEncode"),OXQ={booleanEquals:UXQ,getAttr:sKA,isSet:$XQ,isValidHostLabel:Xt1,not:qXQ,parseURL:NXQ,stringEquals:LXQ,substring:MXQ,uriEncode:RXQ},rKA=c5((A,B)=>{let Q=[],D={...B.endpointParams,...B.referenceRecord},Z=0;while(Z<A.length){let G=A.indexOf("{",Z);if(G===-1){Q.push(A.slice(Z));break}Q.push(A.slice(Z,G));let F=A.indexOf("}",G);if(F===-1){Q.push(A.slice(G));break}if(A[G+1]==="{"&&A[F+1]==="}")Q.push(A.slice(G+1,F)),Z=F+2;let I=A.substring(G+1,F);if(I.includes("#")){let[Y,W]=I.split("#");Q.push(sKA(D[Y],W))}else Q.push(D[I]);Z=F+1}return Q.join("")},"evaluateTemplate"),TXQ=c5(({ref:A},B)=>{return{...B.endpointParams,...B.referenceRecord}[A]},"getReferenceValue"),Dz1=c5((A,B,Q)=>{if(typeof A==="string")return rKA(A,Q);else if(A.fn)return oKA(A,Q);else if(A.ref)return TXQ(A,Q);throw new zV(`'${B}': ${String(A)} is not a string, function or reference.`)},"evaluateExpression"),oKA=c5(({fn:A,argv:B},Q)=>{let D=B.map((G)=>["boolean","number"].includes(typeof G)?G:Dz1(G,"arg",Q)),Z=A.split(".");if(Z[0]in Wt1&&Z[1]!=null)return Wt1[Z[0]][Z[1]](...D);return OXQ[A](...D)},"callFunction"),PXQ=c5(({assign:A,...B},Q)=>{if(A&&A in Q.referenceRecord)throw new zV(`'${A}' is already defined in Reference Record.`);let D=oKA(B,Q);return Q.logger?.debug?.(`${XQ1} evaluateCondition: ${uO(B)} = ${uO(D)}`),{result:D===""?!0:!!D,...A!=null&&{toAssign:{name:A,value:D}}}},"evaluateCondition"),Vt1=c5((A=[],B)=>{let Q={};for(let D of A){let{result:Z,toAssign:G}=PXQ(D,{...B,referenceRecord:{...B.referenceRecord,...Q}});if(!Z)return{result:Z};if(G)Q[G.name]=G.value,B.logger?.debug?.(`${XQ1} assign: ${G.name} := ${uO(G.value)}`)}return{result:!0,referenceRecord:Q}},"evaluateConditions"),SXQ=c5((A,B)=>Object.entries(A).reduce((Q,[D,Z])=>({...Q,[D]:Z.map((G)=>{let F=Dz1(G,"Header value entry",B);if(typeof F!=="string")throw new zV(`Header '${D}' value '${F}' is not a string`);return F})}),{}),"getEndpointHeaders"),tKA=c5((A,B)=>{if(Array.isArray(A))return A.map((Q)=>tKA(Q,B));switch(typeof A){case"string":return rKA(A,B);case"object":if(A===null)throw new zV(`Unexpected endpoint property: ${A}`);return eKA(A,B);case"boolean":return A;default:throw new zV(`Unexpected endpoint property type: ${typeof A}`)}},"getEndpointProperty"),eKA=c5((A,B)=>Object.entries(A).reduce((Q,[D,Z])=>({...Q,[D]:tKA(Z,B)}),{}),"getEndpointProperties"),jXQ=c5((A,B)=>{let Q=Dz1(A,"Endpoint URL",B);if(typeof Q==="string")try{return new URL(Q)}catch(D){throw console.error(`Failed to construct URL with ${Q}`,D),D}throw new zV(`Endpoint URL must be a string, got ${typeof Q}`)},"getEndpointUrl"),yXQ=c5((A,B)=>{let{conditions:Q,endpoint:D}=A,{result:Z,referenceRecord:G}=Vt1(Q,B);if(!Z)return;let F={...B,referenceRecord:{...B.referenceRecord,...G}},{url:I,properties:Y,headers:W}=D;return B.logger?.debug?.(`${XQ1} Resolving endpoint from template: ${uO(D)}`),{...W!=null&&{headers:SXQ(W,F)},...Y!=null&&{properties:eKA(Y,F)},url:jXQ(I,F)}},"evaluateEndpointRule"),kXQ=c5((A,B)=>{let{conditions:Q,error:D}=A,{result:Z,referenceRecord:G}=Vt1(Q,B);if(!Z)return;throw new zV(Dz1(D,"Error",{...B,referenceRecord:{...B.referenceRecord,...G}}))},"evaluateErrorRule"),_XQ=c5((A,B)=>{let{conditions:Q,rules:D}=A,{result:Z,referenceRecord:G}=Vt1(Q,B);if(!Z)return;return AHA(D,{...B,referenceRecord:{...B.referenceRecord,...G}})},"evaluateTreeRule"),AHA=c5((A,B)=>{for(let Q of A)if(Q.type==="endpoint"){let D=yXQ(Q,B);if(D)return D}else if(Q.type==="error")kXQ(Q,B);else if(Q.type==="tree"){let D=_XQ(Q,B);if(D)return D}else throw new zV(`Unknown endpoint rule: ${Q}`);throw new zV("Rules evaluation failed")},"evaluateRules"),xXQ=c5((A,B)=>{let{endpointParams:Q,logger:D}=B,{parameters:Z,rules:G}=A;B.logger?.debug?.(`${XQ1} Initial EndpointParams: ${uO(Q)}`);let F=Object.entries(Z).filter(([,W])=>W.default!=null).map(([W,J])=>[W,J.default]);if(F.length>0)for(let[W,J]of F)Q[W]=Q[W]??J;let I=Object.entries(Z).filter(([,W])=>W.required).map(([W])=>W);for(let W of I)if(Q[W]==null)throw new zV(`Missing required parameter: '${W}'`);let Y=AHA(G,{endpointParams:Q,logger:D,referenceRecord:{}});return B.logger?.debug?.(`${XQ1} Resolved endpoint: ${uO(Y)}`),Y},"resolveEndpoint")});
var SCA=E((TCA)=>{Object.defineProperty(TCA,"__esModule",{value:!0});TCA.sdkStreamMixin=void 0;var RYQ=ECA(),OYQ=ii(),TYQ=ay(),PYQ=cB(),MCA=py(),RCA="The stream has already been transformed.",SYQ=(A)=>{var B,Q;if(!OCA(A)&&!MCA.isReadableStream(A)){let F=((Q=(B=A===null||A===void 0?void 0:A.__proto__)===null||B===void 0?void 0:B.constructor)===null||Q===void 0?void 0:Q.name)||A;throw new Error(`Unexpected stream implementation, expect Blob or ReadableStream, got ${F}`)}let D=!1,Z=async()=>{if(D)throw new Error(RCA);return D=!0,await RYQ.streamCollector(A)},G=(F)=>{if(typeof F.stream!=="function")throw new Error(`Cannot transform payload Blob to web stream. Please make sure the Blob.stream() is polyfilled.
If you are using React Native, this API is not yet supported, see: https://react-native.canny.io/feature-requests/p/fetch-streaming-body`);return F.stream()};return Object.assign(A,{transformToByteArray:Z,transformToString:async(F)=>{let I=await Z();if(F==="base64")return OYQ.toBase64(I);else if(F==="hex")return TYQ.toHex(I);else if(F===void 0||F==="utf8"||F==="utf-8")return PYQ.toUtf8(I);else if(typeof TextDecoder==="function")return new TextDecoder(F).decode(I);else throw new Error("TextDecoder is not available, please make sure polyfill is provided.")},transformToWebStream:()=>{if(D)throw new Error(RCA);if(D=!0,OCA(A))return G(A);else if(MCA.isReadableStream(A))return A;else throw new Error(`Cannot transform payload to web stream, got ${A}`)}})};TCA.sdkStreamMixin=SYQ;var OCA=(A)=>typeof Blob==="function"&&A instanceof Blob});
var SNA=E((TNA)=>{Object.defineProperty(TNA,"__esModule",{value:!0});TNA.retryWrapper=void 0;var GMQ=(A,B,Q)=>{return async()=>{for(let D=0;D<B;++D)try{return await A()}catch(Z){await new Promise((G)=>setTimeout(G,Q))}return await A()}};TNA.retryWrapper=GMQ});
var SXA=E((TXA)=>{Object.defineProperty(TXA,"__esModule",{value:!0});TXA.ChecksumStream=void 0;var iGQ=typeof ReadableStream==="function"?ReadableStream:function(){};class OXA extends iGQ{}TXA.ChecksumStream=OXA});
var SqA=E((uG5,ye1)=>{var{defineProperty:DE1,getOwnPropertyDescriptor:iqQ,getOwnPropertyNames:nqQ}=Object,aqQ=Object.prototype.hasOwnProperty,z8=(A,B)=>DE1(A,"name",{value:B,configurable:!0}),sqQ=(A,B)=>{for(var Q in B)DE1(A,Q,{get:B[Q],enumerable:!0})},Oe1=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of nqQ(B))if(!aqQ.call(A,Z)&&Z!==Q)DE1(A,Z,{get:()=>B[Z],enumerable:!(D=iqQ(B,Z))||D.enumerable})}return A},rqQ=(A,B,Q)=>(Oe1(A,B,"default"),Q&&Oe1(Q,B,"default")),oqQ=(A)=>Oe1(DE1({},"__esModule",{value:!0}),A),Se1={};sqQ(Se1,{Client:()=>tqQ,Command:()=>LqA,NoOpLogger:()=>HNQ,SENSITIVE_STRING:()=>ANQ,ServiceException:()=>QNQ,_json:()=>Pe1,collectBody:()=>Re1.collectBody,convertMap:()=>zNQ,createAggregatedClient:()=>BNQ,decorateServiceException:()=>MqA,emitWarningIfUnsupportedVersion:()=>FNQ,extendedEncodeURIComponent:()=>Re1.extendedEncodeURIComponent,getArrayIfSingleItem:()=>CNQ,getDefaultClientConfiguration:()=>XNQ,getDefaultExtensionConfiguration:()=>OqA,getValueFromTextNode:()=>TqA,isSerializableHeaderValue:()=>KNQ,loadConfigsForDefaultMode:()=>GNQ,map:()=>je1,resolveDefaultRuntimeConfig:()=>VNQ,resolvedPath:()=>Re1.resolvedPath,serializeDateTime:()=>NNQ,serializeFloat:()=>qNQ,take:()=>ENQ,throwDefaultError:()=>RqA,withBaseException:()=>DNQ});ye1.exports=oqQ(Se1);var NqA=Mw(),tqQ=class{constructor(A){this.config=A,this.middlewareStack=NqA.constructStack()}static{z8(this,"Client")}send(A,B,Q){let D=typeof B!=="function"?B:void 0,Z=typeof B==="function"?B:Q,G=D===void 0&&this.config.cacheMiddleware===!0,F;if(G){if(!this.handlers)this.handlers=new WeakMap;let I=this.handlers;if(I.has(A.constructor))F=I.get(A.constructor);else F=A.resolveMiddleware(this.middlewareStack,this.config,D),I.set(A.constructor,F)}else delete this.handlers,F=A.resolveMiddleware(this.middlewareStack,this.config,D);if(Z)F(A).then((I)=>Z(null,I.output),(I)=>Z(I)).catch(()=>{});else return F(A).then((I)=>I.output)}destroy(){this.config?.requestHandler?.destroy?.(),delete this.handlers}},Re1=M6(),Te1=Ve1(),LqA=class{constructor(){this.middlewareStack=NqA.constructStack()}static{z8(this,"Command")}static classBuilder(){return new eqQ}resolveMiddlewareWithContext(A,B,Q,{middlewareFn:D,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,smithyContext:Y,additionalContext:W,CommandCtor:J}){for(let H of D.bind(this)(J,A,B,Q))this.middlewareStack.use(H);let X=A.concat(this.middlewareStack),{logger:V}=B,C={logger:V,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,[Te1.SMITHY_CONTEXT_KEY]:{commandInstance:this,...Y},...W},{requestHandler:K}=B;return X.resolve((H)=>K.handle(H.request,Q||{}),C)}},eqQ=class{constructor(){this._init=()=>{},this._ep={},this._middlewareFn=()=>[],this._commandName="",this._clientName="",this._additionalContext={},this._smithyContext={},this._inputFilterSensitiveLog=(A)=>A,this._outputFilterSensitiveLog=(A)=>A,this._serializer=null,this._deserializer=null}static{z8(this,"ClassBuilder")}init(A){this._init=A}ep(A){return this._ep=A,this}m(A){return this._middlewareFn=A,this}s(A,B,Q={}){return this._smithyContext={service:A,operation:B,...Q},this}c(A={}){return this._additionalContext=A,this}n(A,B){return this._clientName=A,this._commandName=B,this}f(A=(Q)=>Q,B=(Q)=>Q){return this._inputFilterSensitiveLog=A,this._outputFilterSensitiveLog=B,this}ser(A){return this._serializer=A,this}de(A){return this._deserializer=A,this}sc(A){return this._operationSchema=A,this._smithyContext.operationSchema=A,this}build(){let A=this,B;return B=class extends LqA{constructor(...[Q]){super();this.serialize=A._serializer,this.deserialize=A._deserializer,this.input=Q??{},A._init(this),this.schema=A._operationSchema}static{z8(this,"CommandRef")}static getEndpointParameterInstructions(){return A._ep}resolveMiddleware(Q,D,Z){return this.resolveMiddlewareWithContext(Q,D,Z,{CommandCtor:B,middlewareFn:A._middlewareFn,clientName:A._clientName,commandName:A._commandName,inputFilterSensitiveLog:A._inputFilterSensitiveLog,outputFilterSensitiveLog:A._outputFilterSensitiveLog,smithyContext:A._smithyContext,additionalContext:A._additionalContext})}}}},ANQ="***SensitiveInformation***",BNQ=z8((A,B)=>{for(let Q of Object.keys(A)){let D=A[Q],Z=z8(async function(F,I,Y){let W=new D(F);if(typeof I==="function")this.send(W,I);else if(typeof Y==="function"){if(typeof I!=="object")throw new Error(`Expected http options but got ${typeof I}`);this.send(W,I||{},Y)}else return this.send(W,I)},"methodImpl"),G=(Q[0].toLowerCase()+Q.slice(1)).replace(/Command$/,"");B.prototype[G]=Z}},"createAggregatedClient"),QNQ=class A extends Error{static{z8(this,"ServiceException")}constructor(B){super(B.message);Object.setPrototypeOf(this,Object.getPrototypeOf(this).constructor.prototype),this.name=B.name,this.$fault=B.$fault,this.$metadata=B.$metadata}static isInstance(B){if(!B)return!1;let Q=B;return A.prototype.isPrototypeOf(Q)||Boolean(Q.$fault)&&Boolean(Q.$metadata)&&(Q.$fault==="client"||Q.$fault==="server")}static[Symbol.hasInstance](B){if(!B)return!1;let Q=B;if(this===A)return A.isInstance(B);if(A.isInstance(B)){if(Q.name&&this.name)return this.prototype.isPrototypeOf(B)||Q.name===this.name;return this.prototype.isPrototypeOf(B)}return!1}},MqA=z8((A,B={})=>{Object.entries(B).filter(([,D])=>D!==void 0).forEach(([D,Z])=>{if(A[D]==null||A[D]==="")A[D]=Z});let Q=A.message||A.Message||"UnknownError";return A.message=Q,delete A.Message,A},"decorateServiceException"),RqA=z8(({output:A,parsedBody:B,exceptionCtor:Q,errorCode:D})=>{let Z=ZNQ(A),G=Z.httpStatusCode?Z.httpStatusCode+"":void 0,F=new Q({name:B?.code||B?.Code||D||G||"UnknownError",$fault:"client",$metadata:Z});throw MqA(F,B)},"throwDefaultError"),DNQ=z8((A)=>{return({output:B,parsedBody:Q,errorCode:D})=>{RqA({output:B,parsedBody:Q,exceptionCtor:A,errorCode:D})}},"withBaseException"),ZNQ=z8((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),GNQ=z8((A)=>{switch(A){case"standard":return{retryMode:"standard",connectionTimeout:3100};case"in-region":return{retryMode:"standard",connectionTimeout:1100};case"cross-region":return{retryMode:"standard",connectionTimeout:3100};case"mobile":return{retryMode:"standard",connectionTimeout:30000};default:return{}}},"loadConfigsForDefaultMode"),qqA=!1,FNQ=z8((A)=>{if(A&&!qqA&&parseInt(A.substring(1,A.indexOf(".")))<16)qqA=!0},"emitWarningIfUnsupportedVersion"),INQ=z8((A)=>{let B=[];for(let Q in Te1.AlgorithmId){let D=Te1.AlgorithmId[Q];if(A[D]===void 0)continue;B.push({algorithmId:()=>D,checksumConstructor:()=>A[D]})}return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),YNQ=z8((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),WNQ=z8((A)=>{return{setRetryStrategy(B){A.retryStrategy=B},retryStrategy(){return A.retryStrategy}}},"getRetryConfiguration"),JNQ=z8((A)=>{let B={};return B.retryStrategy=A.retryStrategy(),B},"resolveRetryRuntimeConfig"),OqA=z8((A)=>{return Object.assign(INQ(A),WNQ(A))},"getDefaultExtensionConfiguration"),XNQ=OqA,VNQ=z8((A)=>{return Object.assign(YNQ(A),JNQ(A))},"resolveDefaultRuntimeConfig"),CNQ=z8((A)=>Array.isArray(A)?A:[A],"getArrayIfSingleItem"),TqA=z8((A)=>{for(let Q in A)if(A.hasOwnProperty(Q)&&A[Q]["#text"]!==void 0)A[Q]=A[Q]["#text"];else if(typeof A[Q]==="object"&&A[Q]!==null)A[Q]=TqA(A[Q]);return A},"getValueFromTextNode"),KNQ=z8((A)=>{return A!=null},"isSerializableHeaderValue"),HNQ=class{static{z8(this,"NoOpLogger")}trace(){}debug(){}info(){}warn(){}error(){}};function je1(A,B,Q){let D,Z,G;if(typeof B==="undefined"&&typeof Q==="undefined")D={},G=A;else if(D=A,typeof B==="function")return Z=B,G=Q,UNQ(D,Z,G);else G=B;for(let F of Object.keys(G)){if(!Array.isArray(G[F])){D[F]=G[F];continue}PqA(D,null,G,F)}return D}z8(je1,"map");var zNQ=z8((A)=>{let B={};for(let[Q,D]of Object.entries(A||{}))B[Q]=[,D];return B},"convertMap"),ENQ=z8((A,B)=>{let Q={};for(let D in B)PqA(Q,A,B,D);return Q},"take"),UNQ=z8((A,B,Q)=>{return je1(A,Object.entries(Q).reduce((D,[Z,G])=>{if(Array.isArray(G))D[Z]=G;else if(typeof G==="function")D[Z]=[B,G()];else D[Z]=[B,G];return D},{}))},"mapWithFilter"),PqA=z8((A,B,Q,D)=>{if(B!==null){let F=Q[D];if(typeof F==="function")F=[,F];let[I=wNQ,Y=$NQ,W=D]=F;if(typeof I==="function"&&I(B[W])||typeof I!=="function"&&!!I)A[D]=Y(B[W]);return}let[Z,G]=Q[D];if(typeof G==="function"){let F,I=Z===void 0&&(F=G())!=null,Y=typeof Z==="function"&&!!Z(void 0)||typeof Z!=="function"&&!!Z;if(I)A[D]=F;else if(Y)A[D]=G()}else{let F=Z===void 0&&G!=null,I=typeof Z==="function"&&!!Z(G)||typeof Z!=="function"&&!!Z;if(F||I)A[D]=G}},"applyInstruction"),wNQ=z8((A)=>A!=null,"nonNullish"),$NQ=z8((A)=>A,"pass"),qNQ=z8((A)=>{if(A!==A)return"NaN";switch(A){case 1/0:return"Infinity";case-1/0:return"-Infinity";default:return A}},"serializeFloat"),NNQ=z8((A)=>A.toISOString().replace(".000Z","Z"),"serializeDateTime"),Pe1=z8((A)=>{if(A==null)return{};if(Array.isArray(A))return A.filter((B)=>B!=null).map(Pe1);if(typeof A==="object"){let B={};for(let Q of Object.keys(A)){if(A[Q]==null)continue;B[Q]=Pe1(A[Q])}return B}return A},"_json");rqQ(Se1,X6(),ye1.exports)});
var T$A=E((R$A)=>{Object.defineProperty(R$A,"__esModule",{value:!0});R$A.default=void 0;var U$Q=$$Q(Ce1()),w$Q=MQ1();function $$Q(A){return A&&A.__esModule?A:{default:A}}var M$A,Ke1,He1=0,ze1=0;function q$Q(A,B,Q){let D=B&&Q||0,Z=B||new Array(16);A=A||{};let G=A.node||M$A,F=A.clockseq!==void 0?A.clockseq:Ke1;if(G==null||F==null){let V=A.random||(A.rng||U$Q.default)();if(G==null)G=M$A=[V[0]|1,V[1],V[2],V[3],V[4],V[5]];if(F==null)F=Ke1=(V[6]<<8|V[7])&16383}let I=A.msecs!==void 0?A.msecs:Date.now(),Y=A.nsecs!==void 0?A.nsecs:ze1+1,W=I-He1+(Y-ze1)/1e4;if(W<0&&A.clockseq===void 0)F=F+1&16383;if((W<0||I>He1)&&A.nsecs===void 0)Y=0;if(Y>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");He1=I,ze1=Y,Ke1=F,I+=12219292800000;let J=((I&268435455)*1e4+Y)%4294967296;Z[D++]=J>>>24&255,Z[D++]=J>>>16&255,Z[D++]=J>>>8&255,Z[D++]=J&255;let X=I/4294967296*1e4&268435455;Z[D++]=X>>>8&255,Z[D++]=X&255,Z[D++]=X>>>24&15|16,Z[D++]=X>>>16&255,Z[D++]=F>>>8|128,Z[D++]=F&255;for(let V=0;V<6;++V)Z[D+V]=G[V];return B||w$Q.unsafeStringify(Z)}var N$Q=q$Q;R$A.default=N$Q});
var TF=E((AF5,qNA)=>{var{defineProperty:YE1,getOwnPropertyDescriptor:VLQ,getOwnPropertyNames:CLQ}=Object,KLQ=Object.prototype.hasOwnProperty,hG=(A,B)=>YE1(A,"name",{value:B,configurable:!0}),HLQ=(A,B)=>{for(var Q in B)YE1(A,Q,{get:B[Q],enumerable:!0})},zLQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of CLQ(B))if(!KLQ.call(A,Z)&&Z!==Q)YE1(A,Z,{get:()=>B[Z],enumerable:!(D=VLQ(B,Z))||D.enumerable})}return A},ELQ=(A)=>zLQ(YE1({},"__esModule",{value:!0}),A),VNA={};HLQ(VNA,{DEFAULT_MAX_RETRIES:()=>zNA,DEFAULT_TIMEOUT:()=>HNA,ENV_CMDS_AUTH_TOKEN:()=>ge1,ENV_CMDS_FULL_URI:()=>FE1,ENV_CMDS_RELATIVE_URI:()=>IE1,Endpoint:()=>ENA,fromContainerMetadata:()=>qLQ,fromInstanceMetadata:()=>dLQ,getInstanceMetadataEndpoint:()=>wNA,httpRequest:()=>Xn,providerConfigFromInit:()=>ue1});qNA.exports=ELQ(VNA);var ULQ=J1("url"),cN=Q9(),wLQ=J1("buffer"),$LQ=J1("http");function Xn(A){return new Promise((B,Q)=>{let D=$LQ.request({method:"GET",...A,hostname:A.hostname?.replace(/^\[(.+)\]$/,"$1")});D.on("error",(Z)=>{Q(Object.assign(new cN.ProviderError("Unable to connect to instance metadata service"),Z)),D.destroy()}),D.on("timeout",()=>{Q(new cN.ProviderError("TimeoutError from instance metadata service")),D.destroy()}),D.on("response",(Z)=>{let{statusCode:G=400}=Z;if(G<200||300<=G)Q(Object.assign(new cN.ProviderError("Error response received from instance metadata service"),{statusCode:G})),D.destroy();let F=[];Z.on("data",(I)=>{F.push(I)}),Z.on("end",()=>{B(wLQ.Buffer.concat(F)),D.destroy()})}),D.end()})}hG(Xn,"httpRequest");var CNA=hG((A)=>Boolean(A)&&typeof A==="object"&&typeof A.AccessKeyId==="string"&&typeof A.SecretAccessKey==="string"&&typeof A.Token==="string"&&typeof A.Expiration==="string","isImdsCredentials"),KNA=hG((A)=>({accessKeyId:A.AccessKeyId,secretAccessKey:A.SecretAccessKey,sessionToken:A.Token,expiration:new Date(A.Expiration),...A.AccountId&&{accountId:A.AccountId}}),"fromImdsCredentials"),HNA=1000,zNA=0,ue1=hG(({maxRetries:A=zNA,timeout:B=HNA})=>({maxRetries:A,timeout:B}),"providerConfigFromInit"),he1=hG((A,B)=>{let Q=A();for(let D=0;D<B;D++)Q=Q.catch(A);return Q},"retry"),FE1="AWS_CONTAINER_CREDENTIALS_FULL_URI",IE1="AWS_CONTAINER_CREDENTIALS_RELATIVE_URI",ge1="AWS_CONTAINER_AUTHORIZATION_TOKEN",qLQ=hG((A={})=>{let{timeout:B,maxRetries:Q}=ue1(A);return()=>he1(async()=>{let D=await OLQ({logger:A.logger}),Z=JSON.parse(await NLQ(B,D));if(!CNA(Z))throw new cN.CredentialsProviderError("Invalid response received from instance metadata service.",{logger:A.logger});return KNA(Z)},Q)},"fromContainerMetadata"),NLQ=hG(async(A,B)=>{if(process.env[ge1])B.headers={...B.headers,Authorization:process.env[ge1]};return(await Xn({...B,timeout:A})).toString()},"requestFromEcsImds"),LLQ="*************",MLQ={localhost:!0,"127.0.0.1":!0},RLQ={"http:":!0,"https:":!0},OLQ=hG(async({logger:A})=>{if(process.env[IE1])return{hostname:LLQ,path:process.env[IE1]};if(process.env[FE1]){let B=ULQ.parse(process.env[FE1]);if(!B.hostname||!(B.hostname in MLQ))throw new cN.CredentialsProviderError(`${B.hostname} is not a valid container metadata service hostname`,{tryNextLink:!1,logger:A});if(!B.protocol||!(B.protocol in RLQ))throw new cN.CredentialsProviderError(`${B.protocol} is not a valid container metadata service protocol`,{tryNextLink:!1,logger:A});return{...B,port:B.port?parseInt(B.port,10):void 0}}throw new cN.CredentialsProviderError(`The container metadata credential provider cannot be used unless the ${IE1} or ${FE1} environment variable is set`,{tryNextLink:!1,logger:A})},"getCmdsUri"),TLQ=class A extends cN.CredentialsProviderError{constructor(B,Q=!0){super(B,Q);this.tryNextLink=Q,this.name="InstanceMetadataV1FallbackError",Object.setPrototypeOf(this,A.prototype)}static{hG(this,"InstanceMetadataV1FallbackError")}},me1=JD(),PLQ=JZ(),ENA=((A)=>{return A.IPv4="http://***************",A.IPv6="http://[fd00:ec2::254]",A})(ENA||{}),SLQ="AWS_EC2_METADATA_SERVICE_ENDPOINT",jLQ="ec2_metadata_service_endpoint",yLQ={environmentVariableSelector:(A)=>A[SLQ],configFileSelector:(A)=>A[jLQ],default:void 0},UNA=((A)=>{return A.IPv4="IPv4",A.IPv6="IPv6",A})(UNA||{}),kLQ="AWS_EC2_METADATA_SERVICE_ENDPOINT_MODE",_LQ="ec2_metadata_service_endpoint_mode",xLQ={environmentVariableSelector:(A)=>A[kLQ],configFileSelector:(A)=>A[_LQ],default:"IPv4"},wNA=hG(async()=>PLQ.parseUrl(await vLQ()||await bLQ()),"getInstanceMetadataEndpoint"),vLQ=hG(async()=>me1.loadConfig(yLQ)(),"getFromEndpointConfig"),bLQ=hG(async()=>{let A=await me1.loadConfig(xLQ)();switch(A){case"IPv4":return"http://***************";case"IPv6":return"http://[fd00:ec2::254]";default:throw new Error(`Unsupported endpoint mode: ${A}. Select from ${Object.values(UNA)}`)}},"getFromEndpointModeConfig"),fLQ=300,hLQ=300,gLQ="https://docs.aws.amazon.com/sdkref/latest/guide/feature-static-credentials.html",WNA=hG((A,B)=>{let Q=fLQ+Math.floor(Math.random()*hLQ),D=new Date(Date.now()+Q*1000);B.warn(`Attempting credential expiration extension due to a credential service availability issue. A refresh of these credentials will be attempted after ${new Date(D)}.
For more information, please visit: `+gLQ);let Z=A.originalExpiration??A.expiration;return{...A,...Z?{originalExpiration:Z}:{},expiration:D}},"getExtendedInstanceMetadataCredentials"),uLQ=hG((A,B={})=>{let Q=B?.logger||console,D;return async()=>{let Z;try{if(Z=await A(),Z.expiration&&Z.expiration.getTime()<Date.now())Z=WNA(Z,Q)}catch(G){if(D)Q.warn("Credential renew failed: ",G),Z=WNA(D,Q);else throw G}return D=Z,Z}},"staticStabilityProvider"),$NA="/latest/meta-data/iam/security-credentials/",mLQ="/latest/api/token",fe1="AWS_EC2_METADATA_V1_DISABLED",JNA="ec2_metadata_v1_disabled",XNA="x-aws-ec2-metadata-token",dLQ=hG((A={})=>uLQ(cLQ(A),{logger:A.logger}),"fromInstanceMetadata"),cLQ=hG((A={})=>{let B=!1,{logger:Q,profile:D}=A,{timeout:Z,maxRetries:G}=ue1(A),F=hG(async(I,Y)=>{if(B||Y.headers?.[XNA]==null){let X=!1,V=!1,C=await me1.loadConfig({environmentVariableSelector:(K)=>{let H=K[fe1];if(V=!!H&&H!=="false",H===void 0)throw new cN.CredentialsProviderError(`${fe1} not set in env, checking config file next.`,{logger:A.logger});return V},configFileSelector:(K)=>{let H=K[JNA];return X=!!H&&H!=="false",X},default:!1},{profile:D})();if(A.ec2MetadataV1Disabled||C){let K=[];if(A.ec2MetadataV1Disabled)K.push("credential provider initialization (runtime option ec2MetadataV1Disabled)");if(X)K.push(`config file profile (${JNA})`);if(V)K.push(`process environment variable (${fe1})`);throw new TLQ(`AWS EC2 Metadata v1 fallback has been blocked by AWS SDK configuration in the following: [${K.join(", ")}].`)}}let J=(await he1(async()=>{let X;try{X=await pLQ(Y)}catch(V){if(V.statusCode===401)B=!1;throw V}return X},I)).trim();return he1(async()=>{let X;try{X=await iLQ(J,Y,A)}catch(V){if(V.statusCode===401)B=!1;throw V}return X},I)},"getCredentials");return async()=>{let I=await wNA();if(B)return Q?.debug("AWS SDK Instance Metadata","using v1 fallback (no token fetch)"),F(G,{...I,timeout:Z});else{let Y;try{Y=(await lLQ({...I,timeout:Z})).toString()}catch(W){if(W?.statusCode===400)throw Object.assign(W,{message:"EC2 Metadata token request returned error"});else if(W.message==="TimeoutError"||[403,404,405].includes(W.statusCode))B=!0;return Q?.debug("AWS SDK Instance Metadata","using v1 fallback (initial)"),F(G,{...I,timeout:Z})}return F(G,{...I,headers:{[XNA]:Y},timeout:Z})}}},"getInstanceMetadataProvider"),lLQ=hG(async(A)=>Xn({...A,path:mLQ,method:"PUT",headers:{"x-aws-ec2-metadata-token-ttl-seconds":"21600"}}),"getMetadataToken"),pLQ=hG(async(A)=>(await Xn({...A,path:$NA})).toString(),"getProfile"),iLQ=hG(async(A,B,Q)=>{let D=JSON.parse((await Xn({...B,path:$NA+A})).toString());if(!CNA(D))throw new cN.CredentialsProviderError("Invalid response received from instance metadata service.",{logger:Q.logger});return KNA(D)},"getCredentialsFromProfile")});
var TQ1=E((z00)=>{Object.defineProperty(z00,"__esModule",{value:!0});z00.STSClient=z00.__Client=void 0;var dPA=t91(),lyQ=e91(),pyQ=AQ1(),cPA=In(),iyQ=K4(),H00=CB(),nyQ=bG(),ayQ=R6(),lPA=u4(),iPA=g4();Object.defineProperty(z00,"__Client",{enumerable:!0,get:function(){return iPA.Client}});var pPA=ve1(),syQ=Yz(),ryQ=yPA(),oyQ=mPA();class nPA extends iPA.Client{config;constructor(...[A]){let B=ryQ.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=syQ.resolveClientEndpointParameters(B),D=cPA.resolveUserAgentConfig(Q),Z=lPA.resolveRetryConfig(D),G=iyQ.resolveRegionConfig(Z),F=dPA.resolveHostHeaderConfig(G),I=ayQ.resolveEndpointConfig(F),Y=pPA.resolveHttpAuthSchemeConfig(I),W=oyQ.resolveRuntimeExtensions(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(cPA.getUserAgentPlugin(this.config)),this.middlewareStack.use(lPA.getRetryPlugin(this.config)),this.middlewareStack.use(nyQ.getContentLengthPlugin(this.config)),this.middlewareStack.use(dPA.getHostHeaderPlugin(this.config)),this.middlewareStack.use(lyQ.getLoggerPlugin(this.config)),this.middlewareStack.use(pyQ.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(H00.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:pPA.defaultSTSHttpAuthSchemeParametersProvider,identityProviderConfigProvider:async(J)=>new H00.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials})})),this.middlewareStack.use(H00.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}}z00.STSClient=nPA});
var TzA=E((oD5,OzA)=>{var{defineProperty:$z1,getOwnPropertyDescriptor:RVQ,getOwnPropertyNames:OVQ}=Object,TVQ=Object.prototype.hasOwnProperty,TY=(A,B)=>$z1(A,"name",{value:B,configurable:!0}),PVQ=(A,B)=>{for(var Q in B)$z1(A,Q,{get:B[Q],enumerable:!0})},SVQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of OVQ(B))if(!TVQ.call(A,Z)&&Z!==Q)$z1(A,Z,{get:()=>B[Z],enumerable:!(D=RVQ(B,Z))||D.enumerable})}return A},jVQ=(A)=>SVQ($z1({},"__esModule",{value:!0}),A),ZzA={};PVQ(ZzA,{ALGORITHM_IDENTIFIER:()=>Kz1,ALGORITHM_IDENTIFIER_V4A:()=>xVQ,ALGORITHM_QUERY_PARAM:()=>GzA,ALWAYS_UNSIGNABLE_HEADERS:()=>CzA,AMZ_DATE_HEADER:()=>Lt1,AMZ_DATE_QUERY_PARAM:()=>wt1,AUTH_HEADER:()=>Nt1,CREDENTIAL_QUERY_PARAM:()=>FzA,DATE_HEADER:()=>WzA,EVENT_ALGORITHM_IDENTIFIER:()=>zzA,EXPIRES_QUERY_PARAM:()=>YzA,GENERATED_HEADERS:()=>JzA,HOST_HEADER:()=>kVQ,KEY_TYPE_IDENTIFIER:()=>Mt1,MAX_CACHE_SIZE:()=>UzA,MAX_PRESIGNED_TTL:()=>wzA,PROXY_HEADER_PATTERN:()=>KzA,REGION_SET_PARAM:()=>yVQ,SEC_HEADER_PATTERN:()=>HzA,SHA256_HEADER:()=>wz1,SIGNATURE_HEADER:()=>XzA,SIGNATURE_QUERY_PARAM:()=>$t1,SIGNED_HEADERS_QUERY_PARAM:()=>IzA,SignatureV4:()=>pVQ,SignatureV4Base:()=>RzA,TOKEN_HEADER:()=>VzA,TOKEN_QUERY_PARAM:()=>qt1,UNSIGNABLE_PATTERNS:()=>_VQ,UNSIGNED_PAYLOAD:()=>EzA,clearCredentialCache:()=>bVQ,createScope:()=>zz1,getCanonicalHeaders:()=>zt1,getCanonicalQuery:()=>MzA,getPayloadHash:()=>Ez1,getSigningKey:()=>$zA,hasHeader:()=>qzA,moveHeadersToQuery:()=>LzA,prepareRequest:()=>Ut1,signatureV4aContainer:()=>iVQ});OzA.exports=jVQ(ZzA);var AzA=cB(),GzA="X-Amz-Algorithm",FzA="X-Amz-Credential",wt1="X-Amz-Date",IzA="X-Amz-SignedHeaders",YzA="X-Amz-Expires",$t1="X-Amz-Signature",qt1="X-Amz-Security-Token",yVQ="X-Amz-Region-Set",Nt1="authorization",Lt1=wt1.toLowerCase(),WzA="date",JzA=[Nt1,Lt1,WzA],XzA=$t1.toLowerCase(),wz1="x-amz-content-sha256",VzA=qt1.toLowerCase(),kVQ="host",CzA={authorization:!0,"cache-control":!0,connection:!0,expect:!0,from:!0,"keep-alive":!0,"max-forwards":!0,pragma:!0,referer:!0,te:!0,trailer:!0,"transfer-encoding":!0,upgrade:!0,"user-agent":!0,"x-amzn-trace-id":!0},KzA=/^proxy-/,HzA=/^sec-/,_VQ=[/^proxy-/i,/^sec-/i],Kz1="AWS4-HMAC-SHA256",xVQ="AWS4-ECDSA-P256-SHA256",zzA="AWS4-HMAC-SHA256-PAYLOAD",EzA="UNSIGNED-PAYLOAD",UzA=50,Mt1="aws4_request",wzA=604800,oy=ay(),vVQ=cB(),Dn={},Hz1=[],zz1=TY((A,B,Q)=>`${A}/${B}/${Q}/${Mt1}`,"createScope"),$zA=TY(async(A,B,Q,D,Z)=>{let G=await BzA(A,B.secretAccessKey,B.accessKeyId),F=`${Q}:${D}:${Z}:${oy.toHex(G)}:${B.sessionToken}`;if(F in Dn)return Dn[F];Hz1.push(F);while(Hz1.length>UzA)delete Dn[Hz1.shift()];let I=`AWS4${B.secretAccessKey}`;for(let Y of[Q,D,Z,Mt1])I=await BzA(A,I,Y);return Dn[F]=I},"getSigningKey"),bVQ=TY(()=>{Hz1.length=0,Object.keys(Dn).forEach((A)=>{delete Dn[A]})},"clearCredentialCache"),BzA=TY((A,B,Q)=>{let D=new A(B);return D.update(vVQ.toUint8Array(Q)),D.digest()},"hmac"),zt1=TY(({headers:A},B,Q)=>{let D={};for(let Z of Object.keys(A).sort()){if(A[Z]==null)continue;let G=Z.toLowerCase();if(G in CzA||B?.has(G)||KzA.test(G)||HzA.test(G)){if(!Q||Q&&!Q.has(G))continue}D[G]=A[Z].trim().replace(/\s+/g," ")}return D},"getCanonicalHeaders"),fVQ=sHA(),hVQ=cB(),Ez1=TY(async({headers:A,body:B},Q)=>{for(let D of Object.keys(A))if(D.toLowerCase()===wz1)return A[D];if(B==null)return"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855";else if(typeof B==="string"||ArrayBuffer.isView(B)||fVQ.isArrayBuffer(B)){let D=new Q;return D.update(hVQ.toUint8Array(B)),oy.toHex(await D.digest())}return EzA},"getPayloadHash"),QzA=cB(),gVQ=class{static{TY(this,"HeaderFormatter")}format(A){let B=[];for(let Z of Object.keys(A)){let G=QzA.fromUtf8(Z);B.push(Uint8Array.from([G.byteLength]),G,this.formatHeaderValue(A[Z]))}let Q=new Uint8Array(B.reduce((Z,G)=>Z+G.byteLength,0)),D=0;for(let Z of B)Q.set(Z,D),D+=Z.byteLength;return Q}formatHeaderValue(A){switch(A.type){case"boolean":return Uint8Array.from([A.value?0:1]);case"byte":return Uint8Array.from([2,A.value]);case"short":let B=new DataView(new ArrayBuffer(3));return B.setUint8(0,3),B.setInt16(1,A.value,!1),new Uint8Array(B.buffer);case"integer":let Q=new DataView(new ArrayBuffer(5));return Q.setUint8(0,4),Q.setInt32(1,A.value,!1),new Uint8Array(Q.buffer);case"long":let D=new Uint8Array(9);return D[0]=5,D.set(A.value.bytes,1),D;case"binary":let Z=new DataView(new ArrayBuffer(3+A.value.byteLength));Z.setUint8(0,6),Z.setUint16(1,A.value.byteLength,!1);let G=new Uint8Array(Z.buffer);return G.set(A.value,3),G;case"string":let F=QzA.fromUtf8(A.value),I=new DataView(new ArrayBuffer(3+F.byteLength));I.setUint8(0,7),I.setUint16(1,F.byteLength,!1);let Y=new Uint8Array(I.buffer);return Y.set(F,3),Y;case"timestamp":let W=new Uint8Array(9);return W[0]=8,W.set(mVQ.fromNumber(A.value.valueOf()).bytes,1),W;case"uuid":if(!uVQ.test(A.value))throw new Error(`Invalid UUID received: ${A.value}`);let J=new Uint8Array(17);return J[0]=9,J.set(oy.fromHex(A.value.replace(/\-/g,"")),1),J}}},uVQ=/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/,mVQ=class A{constructor(B){if(this.bytes=B,B.byteLength!==8)throw new Error("Int64 buffers must be exactly 8 bytes")}static{TY(this,"Int64")}static fromNumber(B){if(B>9223372036854776000||B<-9223372036854776000)throw new Error(`${B} is too large (or, if negative, too small) to represent as an Int64`);let Q=new Uint8Array(8);for(let D=7,Z=Math.abs(Math.round(B));D>-1&&Z>0;D--,Z/=256)Q[D]=Z;if(B<0)Et1(Q);return new A(Q)}valueOf(){let B=this.bytes.slice(0),Q=B[0]&128;if(Q)Et1(B);return parseInt(oy.toHex(B),16)*(Q?-1:1)}toString(){return String(this.valueOf())}};function Et1(A){for(let B=0;B<8;B++)A[B]^=255;for(let B=7;B>-1;B--)if(A[B]++,A[B]!==0)break}TY(Et1,"negate");var qzA=TY((A,B)=>{A=A.toLowerCase();for(let Q of Object.keys(B))if(A===Q.toLowerCase())return!0;return!1},"hasHeader"),NzA=sJ(),LzA=TY((A,B={})=>{let{headers:Q,query:D={}}=NzA.HttpRequest.clone(A);for(let Z of Object.keys(Q)){let G=Z.toLowerCase();if(G.slice(0,6)==="x-amz-"&&!B.unhoistableHeaders?.has(G)||B.hoistableHeaders?.has(G))D[Z]=Q[Z],delete Q[Z]}return{...A,headers:Q,query:D}},"moveHeadersToQuery"),Ut1=TY((A)=>{A=NzA.HttpRequest.clone(A);for(let B of Object.keys(A.headers))if(JzA.indexOf(B.toLowerCase())>-1)delete A.headers[B];return A},"prepareRequest"),DzA=J5(),dVQ=cB(),Uz1=eHA(),MzA=TY(({query:A={}})=>{let B=[],Q={};for(let D of Object.keys(A)){if(D.toLowerCase()===XzA)continue;let Z=Uz1.escapeUri(D);B.push(Z);let G=A[D];if(typeof G==="string")Q[Z]=`${Z}=${Uz1.escapeUri(G)}`;else if(Array.isArray(G))Q[Z]=G.slice(0).reduce((F,I)=>F.concat([`${Z}=${Uz1.escapeUri(I)}`]),[]).sort().join("&")}return B.sort().map((D)=>Q[D]).filter((D)=>D).join("&")},"getCanonicalQuery"),cVQ=TY((A)=>lVQ(A).toISOString().replace(/\.\d{3}Z$/,"Z"),"iso8601"),lVQ=TY((A)=>{if(typeof A==="number")return new Date(A*1000);if(typeof A==="string"){if(Number(A))return new Date(Number(A)*1000);return new Date(A)}return A},"toDate"),RzA=class{static{TY(this,"SignatureV4Base")}constructor({applyChecksum:A,credentials:B,region:Q,service:D,sha256:Z,uriEscapePath:G=!0}){this.service=D,this.sha256=Z,this.uriEscapePath=G,this.applyChecksum=typeof A==="boolean"?A:!0,this.regionProvider=DzA.normalizeProvider(Q),this.credentialProvider=DzA.normalizeProvider(B)}createCanonicalRequest(A,B,Q){let D=Object.keys(B).sort();return`${A.method}
${this.getCanonicalPath(A)}
${MzA(A)}
${D.map((Z)=>`${Z}:${B[Z]}`).join(`
`)}

${D.join(";")}
${Q}`}async createStringToSign(A,B,Q,D){let Z=new this.sha256;Z.update(dVQ.toUint8Array(Q));let G=await Z.digest();return`${D}
${A}
${B}
${oy.toHex(G)}`}getCanonicalPath({path:A}){if(this.uriEscapePath){let B=[];for(let Z of A.split("/")){if(Z?.length===0)continue;if(Z===".")continue;if(Z==="..")B.pop();else B.push(Z)}let Q=`${A?.startsWith("/")?"/":""}${B.join("/")}${B.length>0&&A?.endsWith("/")?"/":""}`;return Uz1.escapeUri(Q).replace(/%2F/g,"/")}return A}validateResolvedCredentials(A){if(typeof A!=="object"||typeof A.accessKeyId!=="string"||typeof A.secretAccessKey!=="string")throw new Error("Resolved credential object is not valid")}formatDate(A){let B=cVQ(A).replace(/[\-:]/g,"");return{longDate:B,shortDate:B.slice(0,8)}}getCanonicalHeaderList(A){return Object.keys(A).sort().join(";")}},pVQ=class extends RzA{constructor({applyChecksum:A,credentials:B,region:Q,service:D,sha256:Z,uriEscapePath:G=!0}){super({applyChecksum:A,credentials:B,region:Q,service:D,sha256:Z,uriEscapePath:G});this.headerFormatter=new gVQ}static{TY(this,"SignatureV4")}async presign(A,B={}){let{signingDate:Q=new Date,expiresIn:D=3600,unsignableHeaders:Z,unhoistableHeaders:G,signableHeaders:F,hoistableHeaders:I,signingRegion:Y,signingService:W}=B,J=await this.credentialProvider();this.validateResolvedCredentials(J);let X=Y??await this.regionProvider(),{longDate:V,shortDate:C}=this.formatDate(Q);if(D>wzA)return Promise.reject("Signature version 4 presigned URLs must have an expiration date less than one week in the future");let K=zz1(C,X,W??this.service),H=LzA(Ut1(A),{unhoistableHeaders:G,hoistableHeaders:I});if(J.sessionToken)H.query[qt1]=J.sessionToken;H.query[GzA]=Kz1,H.query[FzA]=`${J.accessKeyId}/${K}`,H.query[wt1]=V,H.query[YzA]=D.toString(10);let z=zt1(H,Z,F);return H.query[IzA]=this.getCanonicalHeaderList(z),H.query[$t1]=await this.getSignature(V,K,this.getSigningKey(J,X,C,W),this.createCanonicalRequest(H,z,await Ez1(A,this.sha256))),H}async sign(A,B){if(typeof A==="string")return this.signString(A,B);else if(A.headers&&A.payload)return this.signEvent(A,B);else if(A.message)return this.signMessage(A,B);else return this.signRequest(A,B)}async signEvent({headers:A,payload:B},{signingDate:Q=new Date,priorSignature:D,signingRegion:Z,signingService:G}){let F=Z??await this.regionProvider(),{shortDate:I,longDate:Y}=this.formatDate(Q),W=zz1(I,F,G??this.service),J=await Ez1({headers:{},body:B},this.sha256),X=new this.sha256;X.update(A);let V=oy.toHex(await X.digest()),C=[zzA,Y,W,D,V,J].join(`
`);return this.signString(C,{signingDate:Q,signingRegion:F,signingService:G})}async signMessage(A,{signingDate:B=new Date,signingRegion:Q,signingService:D}){return this.signEvent({headers:this.headerFormatter.format(A.message.headers),payload:A.message.body},{signingDate:B,signingRegion:Q,signingService:D,priorSignature:A.priorSignature}).then((G)=>{return{message:A.message,signature:G}})}async signString(A,{signingDate:B=new Date,signingRegion:Q,signingService:D}={}){let Z=await this.credentialProvider();this.validateResolvedCredentials(Z);let G=Q??await this.regionProvider(),{shortDate:F}=this.formatDate(B),I=new this.sha256(await this.getSigningKey(Z,G,F,D));return I.update(AzA.toUint8Array(A)),oy.toHex(await I.digest())}async signRequest(A,{signingDate:B=new Date,signableHeaders:Q,unsignableHeaders:D,signingRegion:Z,signingService:G}={}){let F=await this.credentialProvider();this.validateResolvedCredentials(F);let I=Z??await this.regionProvider(),Y=Ut1(A),{longDate:W,shortDate:J}=this.formatDate(B),X=zz1(J,I,G??this.service);if(Y.headers[Lt1]=W,F.sessionToken)Y.headers[VzA]=F.sessionToken;let V=await Ez1(Y,this.sha256);if(!qzA(wz1,Y.headers)&&this.applyChecksum)Y.headers[wz1]=V;let C=zt1(Y,D,Q),K=await this.getSignature(W,X,this.getSigningKey(F,I,J,G),this.createCanonicalRequest(Y,C,V));return Y.headers[Nt1]=`${Kz1} Credential=${F.accessKeyId}/${X}, SignedHeaders=${this.getCanonicalHeaderList(C)}, Signature=${K}`,Y}async getSignature(A,B,Q,D){let Z=await this.createStringToSign(A,B,D,Kz1),G=new this.sha256(await Q);return G.update(AzA.toUint8Array(Z)),oy.toHex(await G.digest())}getSigningKey(A,B,Q,D){return $zA(this.sha256,A,Q,B,D||this.service)}},iVQ={SignatureV4a:null}});
var U$A=E((z$A)=>{Object.defineProperty(z$A,"__esModule",{value:!0});z$A.default=void 0;var Y$Q=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;z$A.default=Y$Q});
var UXA=E((zXA)=>{Object.defineProperty(zXA,"__esModule",{value:!0});zXA.toBase64=void 0;var xGQ=YD(),vGQ=cB(),bGQ=(A)=>{let B;if(typeof A==="string")B=vGQ.fromUtf8(A);else B=A;if(typeof B!=="object"||typeof B.byteOffset!=="number"||typeof B.byteLength!=="number")throw new Error("@smithy/util-base64: toBase64 encoder function only accepts string | Uint8Array.");return xGQ.fromArrayBuffer(B.buffer,B.byteOffset,B.byteLength).toString("base64")};zXA.toBase64=bGQ});
var Ue1=E((k$A)=>{Object.defineProperty(k$A,"__esModule",{value:!0});k$A.URL=k$A.DNS=void 0;k$A.default=y$Q;var T$Q=MQ1(),P$Q=S$Q(Ee1());function S$Q(A){return A&&A.__esModule?A:{default:A}}function j$Q(A){A=unescape(encodeURIComponent(A));let B=[];for(let Q=0;Q<A.length;++Q)B.push(A.charCodeAt(Q));return B}var j$A="6ba7b810-9dad-11d1-80b4-00c04fd430c8";k$A.DNS=j$A;var y$A="6ba7b811-9dad-11d1-80b4-00c04fd430c8";k$A.URL=y$A;function y$Q(A,B,Q){function D(Z,G,F,I){var Y;if(typeof Z==="string")Z=j$Q(Z);if(typeof G==="string")G=P$Q.default(G);if(((Y=G)===null||Y===void 0?void 0:Y.length)!==16)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let W=new Uint8Array(16+Z.length);if(W.set(G),W.set(Z,G.length),W=Q(W),W[6]=W[6]&15|B,W[8]=W[8]&63|128,F){I=I||0;for(let J=0;J<16;++J)F[I+J]=W[J];return F}return T$Q.unsafeStringify(W)}try{D.name=A}catch(Z){}return D.DNS=j$A,D.URL=y$A,D}});
var Ve1=E((KG5,Y$A)=>{var{defineProperty:rz1,getOwnPropertyDescriptor:vwQ,getOwnPropertyNames:bwQ}=Object,fwQ=Object.prototype.hasOwnProperty,oz1=(A,B)=>rz1(A,"name",{value:B,configurable:!0}),hwQ=(A,B)=>{for(var Q in B)rz1(A,Q,{get:B[Q],enumerable:!0})},gwQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of bwQ(B))if(!fwQ.call(A,Z)&&Z!==Q)rz1(A,Z,{get:()=>B[Z],enumerable:!(D=vwQ(B,Z))||D.enumerable})}return A},uwQ=(A)=>gwQ(rz1({},"__esModule",{value:!0}),A),A$A={};hwQ(A$A,{AlgorithmId:()=>Z$A,EndpointURLScheme:()=>D$A,FieldPosition:()=>G$A,HttpApiKeyAuthLocation:()=>Q$A,HttpAuthLocation:()=>B$A,IniSectionType:()=>F$A,RequestHandlerProtocol:()=>I$A,SMITHY_CONTEXT_KEY:()=>pwQ,getDefaultClientConfiguration:()=>cwQ,resolveDefaultRuntimeConfig:()=>lwQ});Y$A.exports=uwQ(A$A);var B$A=((A)=>{return A.HEADER="header",A.QUERY="query",A})(B$A||{}),Q$A=((A)=>{return A.HEADER="header",A.QUERY="query",A})(Q$A||{}),D$A=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(D$A||{}),Z$A=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(Z$A||{}),mwQ=oz1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),dwQ=oz1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),cwQ=oz1((A)=>{return mwQ(A)},"getDefaultClientConfiguration"),lwQ=oz1((A)=>{return dwQ(A)},"resolveDefaultRuntimeConfig"),G$A=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(G$A||{}),pwQ="__smithy_context",F$A=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(F$A||{}),I$A=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(I$A||{})});
var WCA=E((FD5,YCA)=>{var{defineProperty:uH1,getOwnPropertyDescriptor:QYQ,getOwnPropertyNames:DYQ}=Object,ZYQ=Object.prototype.hasOwnProperty,GYQ=(A,B)=>uH1(A,"name",{value:B,configurable:!0}),FYQ=(A,B)=>{for(var Q in B)uH1(A,Q,{get:B[Q],enumerable:!0})},IYQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of DYQ(B))if(!ZYQ.call(A,Z)&&Z!==Q)uH1(A,Z,{get:()=>B[Z],enumerable:!(D=QYQ(B,Z))||D.enumerable})}return A},YYQ=(A)=>IYQ(uH1({},"__esModule",{value:!0}),A),FCA={};FYQ(FCA,{buildQueryString:()=>ICA});YCA.exports=YYQ(FCA);var yo1=GCA();function ICA(A){let B=[];for(let Q of Object.keys(A).sort()){let D=A[Q];if(Q=yo1.escapeUri(Q),Array.isArray(D))for(let Z=0,G=D.length;Z<G;Z++)B.push(`${Q}=${yo1.escapeUri(D[Z])}`);else{let Z=Q;if(D||typeof D==="string")Z+=`=${yo1.escapeUri(D)}`;B.push(Z)}}return B.join("&")}GYQ(ICA,"buildQueryString")});
var WI=E((wQ1)=>{Object.defineProperty(wQ1,"__esModule",{value:!0});var tt1=Mh();tt1.__exportStar(Zz(),wQ1);tt1.__exportStar(Pt1(),wQ1);tt1.__exportStar(YUA(),wQ1)});
var WKA=E((IKA)=>{Object.defineProperty(IKA,"__esModule",{value:!0});IKA.toBase64=void 0;var DJQ=YD(),ZJQ=cB(),GJQ=(A)=>{let B;if(typeof A==="string")B=ZJQ.fromUtf8(A);else B=A;if(typeof B!=="object"||typeof B.byteOffset!=="number"||typeof B.byteLength!=="number")throw new Error("@smithy/util-base64: toBase64 encoder function only accepts string | Uint8Array.");return DJQ.fromArrayBuffer(B.buffer,B.byteOffset,B.byteLength).toString("base64")};IKA.toBase64=GJQ});
var X6=E((ND5,DKA)=>{var{defineProperty:no1,getOwnPropertyDescriptor:WWQ,getOwnPropertyNames:JWQ}=Object,XWQ=Object.prototype.hasOwnProperty,VWQ=(A,B)=>{for(var Q in B)no1(A,Q,{get:B[Q],enumerable:!0})},CWQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of JWQ(B))if(!XWQ.call(A,Z)&&Z!==Q)no1(A,Z,{get:()=>B[Z],enumerable:!(D=WWQ(B,Z))||D.enumerable})}return A},KWQ=(A)=>CWQ(no1({},"__esModule",{value:!0}),A),rCA={};VWQ(rCA,{LazyJsonString:()=>Lh,NumericValue:()=>QKA,copyDocumentWithTransform:()=>DQ1,dateToUtcString:()=>kWQ,expectBoolean:()=>EWQ,expectByte:()=>io1,expectFloat32:()=>iH1,expectInt:()=>wWQ,expectInt32:()=>lo1,expectLong:()=>FQ1,expectNonNull:()=>qWQ,expectNumber:()=>GQ1,expectObject:()=>oCA,expectShort:()=>po1,expectString:()=>NWQ,expectUnion:()=>LWQ,handleFloat:()=>OWQ,limitedParseDouble:()=>ro1,limitedParseFloat:()=>TWQ,limitedParseFloat32:()=>PWQ,logger:()=>IQ1,nv:()=>eWQ,parseBoolean:()=>zWQ,parseEpochTimestamp:()=>mWQ,parseRfc3339DateTime:()=>xWQ,parseRfc3339DateTimeWithOffset:()=>bWQ,parseRfc7231DateTime:()=>uWQ,quoteHeader:()=>rWQ,splitEvery:()=>oWQ,splitHeader:()=>tWQ,strictParseByte:()=>BKA,strictParseDouble:()=>so1,strictParseFloat:()=>MWQ,strictParseFloat32:()=>tCA,strictParseInt:()=>SWQ,strictParseInt32:()=>jWQ,strictParseLong:()=>AKA,strictParseShort:()=>si});DKA.exports=KWQ(rCA);var HWQ=_Q(),DQ1=(A,B,Q=(D)=>D)=>{let D=HWQ.NormalizedSchema.of(B);switch(typeof A){case"undefined":case"boolean":case"number":case"string":case"bigint":case"symbol":return Q(A,D);case"function":case"object":if(A===null)return Q(null,D);if(Array.isArray(A)){let G=new Array(A.length),F=0;for(let I of A)G[F++]=DQ1(I,D.getValueSchema(),Q);return Q(G,D)}if("byteLength"in A){let G=new Uint8Array(A.byteLength);return G.set(A,0),Q(G,D)}if(A instanceof Date)return Q(A,D);let Z={};if(D.isMapSchema())for(let G of Object.keys(A))Z[G]=DQ1(A[G],D.getValueSchema(),Q);else if(D.isStructSchema())for(let[G,F]of D.structIterator())Z[G]=DQ1(A[G],F,Q);else if(D.isDocumentSchema())for(let G of Object.keys(A))Z[G]=DQ1(A[G],D.getValueSchema(),Q);return Q(Z,D);default:return Q(A,D)}},zWQ=(A)=>{switch(A){case"true":return!0;case"false":return!1;default:throw new Error(`Unable to parse boolean value "${A}"`)}},EWQ=(A)=>{if(A===null||A===void 0)return;if(typeof A==="number"){if(A===0||A===1)IQ1.warn(nH1(`Expected boolean, got ${typeof A}: ${A}`));if(A===0)return!1;if(A===1)return!0}if(typeof A==="string"){let B=A.toLowerCase();if(B==="false"||B==="true")IQ1.warn(nH1(`Expected boolean, got ${typeof A}: ${A}`));if(B==="false")return!1;if(B==="true")return!0}if(typeof A==="boolean")return A;throw new TypeError(`Expected boolean, got ${typeof A}: ${A}`)},GQ1=(A)=>{if(A===null||A===void 0)return;if(typeof A==="string"){let B=parseFloat(A);if(!Number.isNaN(B)){if(String(B)!==String(A))IQ1.warn(nH1(`Expected number but observed string: ${A}`));return B}}if(typeof A==="number")return A;throw new TypeError(`Expected number, got ${typeof A}: ${A}`)},UWQ=Math.ceil(340282346638528860000000000000000000000),iH1=(A)=>{let B=GQ1(A);if(B!==void 0&&!Number.isNaN(B)&&B!==1/0&&B!==-1/0){if(Math.abs(B)>UWQ)throw new TypeError(`Expected 32-bit float, got ${A}`)}return B},FQ1=(A)=>{if(A===null||A===void 0)return;if(Number.isInteger(A)&&!Number.isNaN(A))return A;throw new TypeError(`Expected integer, got ${typeof A}: ${A}`)},wWQ=FQ1,lo1=(A)=>ao1(A,32),po1=(A)=>ao1(A,16),io1=(A)=>ao1(A,8),ao1=(A,B)=>{let Q=FQ1(A);if(Q!==void 0&&$WQ(Q,B)!==Q)throw new TypeError(`Expected ${B}-bit integer, got ${A}`);return Q},$WQ=(A,B)=>{switch(B){case 32:return Int32Array.of(A)[0];case 16:return Int16Array.of(A)[0];case 8:return Int8Array.of(A)[0]}},qWQ=(A,B)=>{if(A===null||A===void 0){if(B)throw new TypeError(`Expected a non-null value for ${B}`);throw new TypeError("Expected a non-null value")}return A},oCA=(A)=>{if(A===null||A===void 0)return;if(typeof A==="object"&&!Array.isArray(A))return A;let B=Array.isArray(A)?"array":typeof A;throw new TypeError(`Expected object, got ${B}: ${A}`)},NWQ=(A)=>{if(A===null||A===void 0)return;if(typeof A==="string")return A;if(["boolean","number","bigint"].includes(typeof A))return IQ1.warn(nH1(`Expected string, got ${typeof A}: ${A}`)),String(A);throw new TypeError(`Expected string, got ${typeof A}: ${A}`)},LWQ=(A)=>{if(A===null||A===void 0)return;let B=oCA(A),Q=Object.entries(B).filter(([,D])=>D!=null).map(([D])=>D);if(Q.length===0)throw new TypeError("Unions must have exactly one non-null member. None were found.");if(Q.length>1)throw new TypeError(`Unions must have exactly one non-null member. Keys ${Q} were not null.`);return B},so1=(A)=>{if(typeof A=="string")return GQ1(oi(A));return GQ1(A)},MWQ=so1,tCA=(A)=>{if(typeof A=="string")return iH1(oi(A));return iH1(A)},RWQ=/(-?(?:0|[1-9]\d*)(?:\.\d+)?(?:[eE][+-]?\d+)?)|(-?Infinity)|(NaN)/g,oi=(A)=>{let B=A.match(RWQ);if(B===null||B[0].length!==A.length)throw new TypeError("Expected real number, got implicit NaN");return parseFloat(A)},ro1=(A)=>{if(typeof A=="string")return eCA(A);return GQ1(A)},OWQ=ro1,TWQ=ro1,PWQ=(A)=>{if(typeof A=="string")return eCA(A);return iH1(A)},eCA=(A)=>{switch(A){case"NaN":return NaN;case"Infinity":return 1/0;case"-Infinity":return-1/0;default:throw new Error(`Unable to parse float value: ${A}`)}},AKA=(A)=>{if(typeof A==="string")return FQ1(oi(A));return FQ1(A)},SWQ=AKA,jWQ=(A)=>{if(typeof A==="string")return lo1(oi(A));return lo1(A)},si=(A)=>{if(typeof A==="string")return po1(oi(A));return po1(A)},BKA=(A)=>{if(typeof A==="string")return io1(oi(A));return io1(A)},nH1=(A)=>{return String(new TypeError(A).stack||A).split(`
`).slice(0,5).filter((B)=>!B.includes("stackTraceWarning")).join(`
`)},IQ1={warn:console.warn},yWQ=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],oo1=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function kWQ(A){let B=A.getUTCFullYear(),Q=A.getUTCMonth(),D=A.getUTCDay(),Z=A.getUTCDate(),G=A.getUTCHours(),F=A.getUTCMinutes(),I=A.getUTCSeconds(),Y=Z<10?`0${Z}`:`${Z}`,W=G<10?`0${G}`:`${G}`,J=F<10?`0${F}`:`${F}`,X=I<10?`0${I}`:`${I}`;return`${yWQ[D]}, ${Y} ${oo1[Q]} ${B} ${W}:${J}:${X} GMT`}var _WQ=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?[zZ]$/),xWQ=(A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-3339 date-times must be expressed as strings");let B=_WQ.exec(A);if(!B)throw new TypeError("Invalid RFC-3339 date-time value");let[Q,D,Z,G,F,I,Y,W]=B,J=si(ri(D)),X=gN(Z,"month",1,12),V=gN(G,"day",1,31);return ZQ1(J,X,V,{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W})},vWQ=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?(([-+]\d{2}\:\d{2})|[zZ])$/),bWQ=(A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-3339 date-times must be expressed as strings");let B=vWQ.exec(A);if(!B)throw new TypeError("Invalid RFC-3339 date-time value");let[Q,D,Z,G,F,I,Y,W,J]=B,X=si(ri(D)),V=gN(Z,"month",1,12),C=gN(G,"day",1,31),K=ZQ1(X,V,C,{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W});if(J.toUpperCase()!="Z")K.setTime(K.getTime()-sWQ(J));return K},fWQ=new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d{2}) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/),hWQ=new RegExp(/^(?:Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d{2})-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/),gWQ=new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( [1-9]|\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? (\d{4})$/),uWQ=(A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-7231 date-times must be expressed as strings");let B=fWQ.exec(A);if(B){let[Q,D,Z,G,F,I,Y,W]=B;return ZQ1(si(ri(G)),co1(Z),gN(D,"day",1,31),{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W})}if(B=hWQ.exec(A),B){let[Q,D,Z,G,F,I,Y,W]=B;return lWQ(ZQ1(dWQ(G),co1(Z),gN(D,"day",1,31),{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W}))}if(B=gWQ.exec(A),B){let[Q,D,Z,G,F,I,Y,W]=B;return ZQ1(si(ri(W)),co1(D),gN(Z.trimLeft(),"day",1,31),{hours:G,minutes:F,seconds:I,fractionalMilliseconds:Y})}throw new TypeError("Invalid RFC-7231 date-time value")},mWQ=(A)=>{if(A===null||A===void 0)return;let B;if(typeof A==="number")B=A;else if(typeof A==="string")B=so1(A);else if(typeof A==="object"&&A.tag===1)B=A.value;else throw new TypeError("Epoch timestamps must be expressed as floating point numbers or their string representation");if(Number.isNaN(B)||B===1/0||B===-1/0)throw new TypeError("Epoch timestamps must be valid, non-Infinite, non-NaN numerics");return new Date(Math.round(B*1000))},ZQ1=(A,B,Q,D)=>{let Z=B-1;return iWQ(A,Z,Q),new Date(Date.UTC(A,Z,Q,gN(D.hours,"hour",0,23),gN(D.minutes,"minute",0,59),gN(D.seconds,"seconds",0,60),aWQ(D.fractionalMilliseconds)))},dWQ=(A)=>{let B=new Date().getUTCFullYear(),Q=Math.floor(B/100)*100+si(ri(A));if(Q<B)return Q+100;return Q},cWQ=1576800000000,lWQ=(A)=>{if(A.getTime()-new Date().getTime()>cWQ)return new Date(Date.UTC(A.getUTCFullYear()-100,A.getUTCMonth(),A.getUTCDate(),A.getUTCHours(),A.getUTCMinutes(),A.getUTCSeconds(),A.getUTCMilliseconds()));return A},co1=(A)=>{let B=oo1.indexOf(A);if(B<0)throw new TypeError(`Invalid month: ${A}`);return B+1},pWQ=[31,28,31,30,31,30,31,31,30,31,30,31],iWQ=(A,B,Q)=>{let D=pWQ[B];if(B===1&&nWQ(A))D=29;if(Q>D)throw new TypeError(`Invalid day for ${oo1[B]} in ${A}: ${Q}`)},nWQ=(A)=>{return A%4===0&&(A%100!==0||A%400===0)},gN=(A,B,Q,D)=>{let Z=BKA(ri(A));if(Z<Q||Z>D)throw new TypeError(`${B} must be between ${Q} and ${D}, inclusive`);return Z},aWQ=(A)=>{if(A===null||A===void 0)return 0;return tCA("0."+A)*1000},sWQ=(A)=>{let B=A[0],Q=1;if(B=="+")Q=1;else if(B=="-")Q=-1;else throw new TypeError(`Offset direction, ${B}, must be "+" or "-"`);let D=Number(A.substring(1,3)),Z=Number(A.substring(4,6));return Q*(D*60+Z)*60*1000},ri=(A)=>{let B=0;while(B<A.length-1&&A.charAt(B)==="0")B++;if(B===0)return A;return A.slice(B)},Lh=function A(B){return Object.assign(new String(B),{deserializeJSON(){return JSON.parse(String(B))},toString(){return String(B)},toJSON(){return String(B)}})};Lh.from=(A)=>{if(A&&typeof A==="object"&&(A instanceof Lh||("deserializeJSON"in A)))return A;else if(typeof A==="string"||Object.getPrototypeOf(A)===String.prototype)return Lh(String(A));return Lh(JSON.stringify(A))};Lh.fromObject=Lh.from;function rWQ(A){if(A.includes(",")||A.includes('"'))A=`"${A.replace(/"/g,"\\\"")}"`;return A}function oWQ(A,B,Q){if(Q<=0||!Number.isInteger(Q))throw new Error("Invalid number of delimiters ("+Q+") for splitEvery.");let D=A.split(B);if(Q===1)return D;let Z=[],G="";for(let F=0;F<D.length;F++){if(G==="")G=D[F];else G+=B+D[F];if((F+1)%Q===0)Z.push(G),G=""}if(G!=="")Z.push(G);return Z}var tWQ=(A)=>{let B=A.length,Q=[],D=!1,Z=void 0,G=0;for(let F=0;F<B;++F){let I=A[F];switch(I){case'"':if(Z!=="\\")D=!D;break;case",":if(!D)Q.push(A.slice(G,F)),G=F+1;break;default:}Z=I}return Q.push(A.slice(G)),Q.map((F)=>{F=F.trim();let I=F.length;if(I<2)return F;if(F[0]==='"'&&F[I-1]==='"')F=F.slice(1,I-1);return F.replace(/\\"/g,'"')})},QKA=class{constructor(A,B){this.string=A,this.type=B;let Q=0;for(let D=0;D<A.length;++D){let Z=A.charCodeAt(D);if(D===0&&Z===45)continue;if(Z===46){if(Q)throw new Error("@smithy/core/serde - NumericValue must contain at most one decimal point.");Q=1;continue}if(Z<48||Z>57)throw new Error('@smithy/core/serde - NumericValue must only contain [0-9], at most one decimal point ".", and an optional negation prefix "-".')}}toString(){return this.string}[Symbol.hasInstance](A){if(!A||typeof A!=="object")return!1;let B=A;if(typeof B.string==="string"&&typeof B.type==="string"&&B.constructor?.name==="NumericValue")return!0;return!1}};function eWQ(A){return new QKA(String(A),"bigDecimal")}});
var XVA=E((c75,JVA)=>{var{defineProperty:yH1,getOwnPropertyDescriptor:$FQ,getOwnPropertyNames:qFQ}=Object,NFQ=Object.prototype.hasOwnProperty,kH1=(A,B)=>yH1(A,"name",{value:B,configurable:!0}),LFQ=(A,B)=>{for(var Q in B)yH1(A,Q,{get:B[Q],enumerable:!0})},MFQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of qFQ(B))if(!NFQ.call(A,Z)&&Z!==Q)yH1(A,Z,{get:()=>B[Z],enumerable:!(D=$FQ(B,Z))||D.enumerable})}return A},RFQ=(A)=>MFQ(yH1({},"__esModule",{value:!0}),A),QVA={};LFQ(QVA,{AlgorithmId:()=>FVA,EndpointURLScheme:()=>GVA,FieldPosition:()=>IVA,HttpApiKeyAuthLocation:()=>ZVA,HttpAuthLocation:()=>DVA,IniSectionType:()=>YVA,RequestHandlerProtocol:()=>WVA,SMITHY_CONTEXT_KEY:()=>jFQ,getDefaultClientConfiguration:()=>PFQ,resolveDefaultRuntimeConfig:()=>SFQ});JVA.exports=RFQ(QVA);var DVA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(DVA||{}),ZVA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(ZVA||{}),GVA=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(GVA||{}),FVA=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(FVA||{}),OFQ=kH1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),TFQ=kH1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),PFQ=kH1((A)=>{return OFQ(A)},"getDefaultClientConfiguration"),SFQ=kH1((A)=>{return TFQ(A)},"resolveDefaultRuntimeConfig"),IVA=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(IVA||{}),jFQ="__smithy_context",YVA=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(YVA||{}),WVA=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(WVA||{})});
var XXA=E((WXA)=>{Object.defineProperty(WXA,"__esModule",{value:!0});WXA.fromBase64=void 0;var LGQ=YD(),MGQ=/^[A-Za-z0-9+/]*={0,2}$/,RGQ=(A)=>{if(A.length*3%4!==0)throw new TypeError("Incorrect padding on base64 string.");if(!MGQ.exec(A))throw new TypeError("Invalid base64 string.");let B=LGQ.fromString(A,"base64");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength)};WXA.fromBase64=RGQ});
var YD=E((T75,YXA)=>{var{defineProperty:TH1,getOwnPropertyDescriptor:KGQ,getOwnPropertyNames:HGQ}=Object,zGQ=Object.prototype.hasOwnProperty,FXA=(A,B)=>TH1(A,"name",{value:B,configurable:!0}),EGQ=(A,B)=>{for(var Q in B)TH1(A,Q,{get:B[Q],enumerable:!0})},UGQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of HGQ(B))if(!zGQ.call(A,Z)&&Z!==Q)TH1(A,Z,{get:()=>B[Z],enumerable:!(D=KGQ(B,Z))||D.enumerable})}return A},wGQ=(A)=>UGQ(TH1({},"__esModule",{value:!0}),A),IXA={};EGQ(IXA,{fromArrayBuffer:()=>qGQ,fromString:()=>NGQ});YXA.exports=wGQ(IXA);var $GQ=GXA(),wo1=J1("buffer"),qGQ=FXA((A,B=0,Q=A.byteLength-B)=>{if(!$GQ.isArrayBuffer(A))throw new TypeError(`The "input" argument must be ArrayBuffer. Received type ${typeof A} (${A})`);return wo1.Buffer.from(A,B,Q)},"fromArrayBuffer"),NGQ=FXA((A,B)=>{if(typeof A!=="string")throw new TypeError(`The "input" argument must be of type string. Received type ${typeof A} (${A})`);return B?wo1.Buffer.from(A,B):wo1.Buffer.from(A)},"fromString")});
var YUA=E((SZ5,IUA)=>{var{defineProperty:jz1,getOwnPropertyDescriptor:uHQ,getOwnPropertyNames:mHQ}=Object,dHQ=Object.prototype.hasOwnProperty,A8=(A,B)=>jz1(A,"name",{value:B,configurable:!0}),cHQ=(A,B)=>{for(var Q in B)jz1(A,Q,{get:B[Q],enumerable:!0})},lHQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of mHQ(B))if(!dHQ.call(A,Z)&&Z!==Q)jz1(A,Z,{get:()=>B[Z],enumerable:!(D=uHQ(B,Z))||D.enumerable})}return A},pHQ=(A)=>lHQ(jz1({},"__esModule",{value:!0}),A),oEA={};cHQ(oEA,{AwsEc2QueryProtocol:()=>$zQ,AwsJson1_0Protocol:()=>GzQ,AwsJson1_1Protocol:()=>FzQ,AwsJsonRpcProtocol:()=>rt1,AwsQueryProtocol:()=>QUA,AwsRestJsonProtocol:()=>YzQ,AwsRestXmlProtocol:()=>OzQ,JsonCodec:()=>st1,JsonShapeDeserializer:()=>AUA,JsonShapeSerializer:()=>BUA,XmlCodec:()=>FUA,XmlShapeDeserializer:()=>ot1,XmlShapeSerializer:()=>GUA,_toBool:()=>nHQ,_toNum:()=>aHQ,_toStr:()=>iHQ,awsExpectUnion:()=>JzQ,loadRestJsonErrorCode:()=>at1,loadRestXmlErrorCode:()=>ZUA,parseJsonBody:()=>nt1,parseJsonErrorBody:()=>AzQ,parseXmlBody:()=>DUA,parseXmlErrorBody:()=>MzQ});IUA.exports=pHQ(oEA);var iHQ=A8((A)=>{if(A==null)return A;if(typeof A==="number"||typeof A==="bigint"){let B=new Error(`Received number ${A} where a string was expected.`);return B.name="Warning",console.warn(B),String(A)}if(typeof A==="boolean"){let B=new Error(`Received boolean ${A} where a string was expected.`);return B.name="Warning",console.warn(B),String(A)}return A},"_toStr"),nHQ=A8((A)=>{if(A==null)return A;if(typeof A==="string"){let B=A.toLowerCase();if(A!==""&&B!=="false"&&B!=="true"){let Q=new Error(`Received string "${A}" where a boolean was expected.`);Q.name="Warning",console.warn(Q)}return A!==""&&B!=="false"}return A},"_toBool"),aHQ=A8((A)=>{if(A==null)return A;if(typeof A==="string"){let B=Number(A);if(B.toString()!==A){let Q=new Error(`Received string "${A}" where a number was expected.`);return Q.name="Warning",console.warn(Q),A}return B}return A},"_toNum"),sHQ=M6(),Zn=_Q(),rHQ=PY(),jh=class{static{A8(this,"SerdeContextConfig")}serdeContext;setSerdeContext(A){this.serdeContext=A}},zQ1=_Q(),Gn=X6(),oHQ=ty(),tHQ=X6();function tEA(A,B,Q){if(Q?.source){let D=Q.source;if(typeof B==="number"){if(B>Number.MAX_SAFE_INTEGER||B<Number.MIN_SAFE_INTEGER||D!==String(B))if(D.includes("."))return new tHQ.NumericValue(D,"bigDecimal");else return BigInt(D)}}return B}A8(tEA,"jsonReviver");var eHQ=g4(),eEA=A8((A,B)=>eHQ.collectBody(A,B).then((Q)=>B.utf8Encoder(Q)),"collectBodyString"),nt1=A8((A,B)=>eEA(A,B).then((Q)=>{if(Q.length)try{return JSON.parse(Q)}catch(D){if(D?.name==="SyntaxError")Object.defineProperty(D,"$responseBodyText",{value:Q});throw D}return{}}),"parseJsonBody"),AzQ=A8(async(A,B)=>{let Q=await nt1(A,B);return Q.message=Q.message??Q.Message,Q},"parseJsonErrorBody"),at1=A8((A,B)=>{let Q=A8((G,F)=>Object.keys(G).find((I)=>I.toLowerCase()===F.toLowerCase()),"findKey"),D=A8((G)=>{let F=G;if(typeof F==="number")F=F.toString();if(F.indexOf(",")>=0)F=F.split(",")[0];if(F.indexOf(":")>=0)F=F.split(":")[0];if(F.indexOf("#")>=0)F=F.split("#")[1];return F},"sanitizeErrorCode"),Z=Q(A.headers,"x-amzn-errortype");if(Z!==void 0)return D(A.headers[Z]);if(B&&typeof B==="object"){let G=Q(B,"code");if(G&&B[G]!==void 0)return D(B[G]);if(B.__type!==void 0)return D(B.__type)}},"loadRestJsonErrorCode"),AUA=class extends jh{constructor(A){super();this.settings=A}static{A8(this,"JsonShapeDeserializer")}async read(A,B){return this._read(A,typeof B==="string"?JSON.parse(B,tEA):await nt1(B,this.serdeContext))}readObject(A,B){return this._read(A,B)}_read(A,B){let Q=B!==null&&typeof B==="object",D=zQ1.NormalizedSchema.of(A);if(D.isListSchema()&&Array.isArray(B)){let G=D.getValueSchema(),F=[],I=!!D.getMergedTraits().sparse;for(let Y of B)if(I||Y!=null)F.push(this._read(G,Y));return F}else if(D.isMapSchema()&&Q){let G=D.getValueSchema(),F={},I=!!D.getMergedTraits().sparse;for(let[Y,W]of Object.entries(B))if(I||W!=null)F[Y]=this._read(G,W);return F}else if(D.isStructSchema()&&Q){let G={};for(let[F,I]of D.structIterator()){let Y=this.settings.jsonName?I.getMergedTraits().jsonName??F:F,W=this._read(I,B[Y]);if(W!=null)G[F]=W}return G}if(D.isBlobSchema()&&typeof B==="string")return oHQ.fromBase64(B);let Z=D.getMergedTraits().mediaType;if(D.isStringSchema()&&typeof B==="string"&&Z){if(Z==="application/json"||Z.endsWith("+json"))return Gn.LazyJsonString.from(B)}if(D.isTimestampSchema()){let G=this.settings.timestampFormat;switch(G.useTrait?D.getSchema()===zQ1.SCHEMA.TIMESTAMP_DEFAULT?G.default:D.getSchema()??G.default:G.default){case zQ1.SCHEMA.TIMESTAMP_DATE_TIME:return Gn.parseRfc3339DateTimeWithOffset(B);case zQ1.SCHEMA.TIMESTAMP_HTTP_DATE:return Gn.parseRfc7231DateTime(B);case zQ1.SCHEMA.TIMESTAMP_EPOCH_SECONDS:return Gn.parseEpochTimestamp(B);default:return console.warn("Missing timestamp format, parsing value with Date constructor:",B),new Date(B)}}if(D.isBigIntegerSchema()&&(typeof B==="number"||typeof B==="string"))return BigInt(B);if(D.isBigDecimalSchema()&&B!=null){if(B instanceof Gn.NumericValue)return B;return new Gn.NumericValue(String(B),"bigDecimal")}if(D.isNumericSchema()&&typeof B==="string")switch(B){case"Infinity":return 1/0;case"-Infinity":return-1/0;case"NaN":return NaN}return B}},Fn=_Q(),BzQ=X6(),QzQ=X6(),DzQ=X6(),nEA=String.fromCharCode(925),ZzQ=class{static{A8(this,"JsonReplacer")}values=new Map;counter=0;stage=0;createReplacer(){if(this.stage===1)throw new Error("@aws-sdk/core/protocols - JsonReplacer already created.");if(this.stage===2)throw new Error("@aws-sdk/core/protocols - JsonReplacer exhausted.");return this.stage=1,(A,B)=>{if(B instanceof DzQ.NumericValue){let Q=`${nEA+NaN+this.counter++}_`+B.string;return this.values.set(`"${Q}"`,B.string),Q}if(typeof B==="bigint"){let Q=B.toString(),D=`${nEA+"b"+this.counter++}_`+Q;return this.values.set(`"${D}"`,Q),D}return B}}replaceInJson(A){if(this.stage===0)throw new Error("@aws-sdk/core/protocols - JsonReplacer not created yet.");if(this.stage===2)throw new Error("@aws-sdk/core/protocols - JsonReplacer exhausted.");if(this.stage=2,this.counter===0)return A;for(let[B,Q]of this.values)A=A.replace(B,Q);return A}},BUA=class extends jh{constructor(A){super();this.settings=A}static{A8(this,"JsonShapeSerializer")}buffer;rootSchema;write(A,B){this.rootSchema=Fn.NormalizedSchema.of(A),this.buffer=this._write(this.rootSchema,B)}flush(){if(this.rootSchema?.isStructSchema()||this.rootSchema?.isDocumentSchema()){let A=new ZzQ;return A.replaceInJson(JSON.stringify(this.buffer,A.createReplacer(),0))}return this.buffer}_write(A,B,Q){let D=B!==null&&typeof B==="object",Z=Fn.NormalizedSchema.of(A);if(Z.isListSchema()&&Array.isArray(B)){let F=Z.getValueSchema(),I=[],Y=!!Z.getMergedTraits().sparse;for(let W of B)if(Y||W!=null)I.push(this._write(F,W));return I}else if(Z.isMapSchema()&&D){let F=Z.getValueSchema(),I={},Y=!!Z.getMergedTraits().sparse;for(let[W,J]of Object.entries(B))if(Y||J!=null)I[W]=this._write(F,J);return I}else if(Z.isStructSchema()&&D){let F={};for(let[I,Y]of Z.structIterator()){let W=this.settings.jsonName?Y.getMergedTraits().jsonName??I:I,J=this._write(Y,B[I],Z);if(J!==void 0)F[W]=J}return F}if(B===null&&Q?.isStructSchema())return;if(Z.isBlobSchema()&&(B instanceof Uint8Array||typeof B==="string")){if(Z===this.rootSchema)return B;if(!this.serdeContext?.base64Encoder)throw new Error("Missing base64Encoder in serdeContext");return this.serdeContext?.base64Encoder(B)}if(Z.isTimestampSchema()&&B instanceof Date){let F=this.settings.timestampFormat;switch(F.useTrait?Z.getSchema()===Fn.SCHEMA.TIMESTAMP_DEFAULT?F.default:Z.getSchema()??F.default:F.default){case Fn.SCHEMA.TIMESTAMP_DATE_TIME:return B.toISOString().replace(".000Z","Z");case Fn.SCHEMA.TIMESTAMP_HTTP_DATE:return BzQ.dateToUtcString(B);case Fn.SCHEMA.TIMESTAMP_EPOCH_SECONDS:return B.getTime()/1000;default:return console.warn("Missing timestamp format, using epoch seconds",B),B.getTime()/1000}}if(Z.isNumericSchema()&&typeof B==="number"){if(Math.abs(B)===1/0||isNaN(B))return String(B)}let G=Z.getMergedTraits().mediaType;if(Z.isStringSchema()&&typeof B==="string"&&G){if(G==="application/json"||G.endsWith("+json"))return QzQ.LazyJsonString.from(B)}return B}},st1=class extends jh{constructor(A){super();this.settings=A}static{A8(this,"JsonCodec")}createSerializer(){let A=new BUA(this.settings);return A.setSerdeContext(this.serdeContext),A}createDeserializer(){let A=new AUA(this.settings);return A.setSerdeContext(this.serdeContext),A}},rt1=class extends sHQ.RpcProtocol{static{A8(this,"AwsJsonRpcProtocol")}serializer;deserializer;codec;constructor({defaultNamespace:A}){super({defaultNamespace:A});this.codec=new st1({timestampFormat:{useTrait:!0,default:Zn.SCHEMA.TIMESTAMP_EPOCH_SECONDS},jsonName:!1}),this.serializer=this.codec.createSerializer(),this.deserializer=this.codec.createDeserializer()}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q);if(!D.path.endsWith("/"))D.path+="/";if(Object.assign(D.headers,{"content-type":`application/x-amz-json-${this.getJsonRpcVersion()}`,"x-amz-target":(this.getJsonRpcVersion()==="1.0"?"JsonRpc10.":"JsonProtocol.")+Zn.NormalizedSchema.of(A).getName()}),Zn.deref(A.input)==="unit"||!D.body)D.body="{}";try{D.headers["content-length"]=String(rHQ.calculateBodyLength(D.body))}catch(Z){}return D}getPayloadCodec(){return this.codec}async handleError(A,B,Q,D,Z){let G=at1(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=Zn.TypeRegistry.for(F),W;try{W=Y.getSchema(G)}catch(K){let H=Zn.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(H){let z=H.ctor;throw Object.assign(new z(I),D)}throw new Error(I)}let J=Zn.NormalizedSchema.of(W),X=D.message??D.Message??"Unknown",V=new W.ctor(X);await this.deserializeHttpMessage(W,B,Q,D);let C={};for(let[K,H]of J.structIterator()){let z=H.getMergedTraits().jsonName??K;C[K]=this.codec.createDeserializer().readObject(H,D[z])}throw Object.assign(V,{$metadata:Z,$response:Q,$fault:J.getMergedTraits().error,message:X,...C}),V}},GzQ=class extends rt1{static{A8(this,"AwsJson1_0Protocol")}constructor({defaultNamespace:A}){super({defaultNamespace:A})}getShapeId(){return"aws.protocols#awsJson1_0"}getJsonRpcVersion(){return"1.0"}},FzQ=class extends rt1{static{A8(this,"AwsJson1_1Protocol")}constructor({defaultNamespace:A}){super({defaultNamespace:A})}getShapeId(){return"aws.protocols#awsJson1_1"}getJsonRpcVersion(){return"1.1"}},ct1=M6(),EQ1=_Q(),IzQ=PY(),YzQ=class extends ct1.HttpBindingProtocol{static{A8(this,"AwsRestJsonProtocol")}serializer;deserializer;codec;constructor({defaultNamespace:A}){super({defaultNamespace:A});let B={timestampFormat:{useTrait:!0,default:EQ1.SCHEMA.TIMESTAMP_EPOCH_SECONDS},httpBindings:!0,jsonName:!0};this.codec=new st1(B),this.serializer=new ct1.HttpInterceptingShapeSerializer(this.codec.createSerializer(),B),this.deserializer=new ct1.HttpInterceptingShapeDeserializer(this.codec.createDeserializer(),B)}getShapeId(){return"aws.protocols#restJson1"}getPayloadCodec(){return this.codec}setSerdeContext(A){this.codec.setSerdeContext(A),super.setSerdeContext(A)}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q),Z=EQ1.NormalizedSchema.of(A.input),G=Z.getMemberSchemas();if(!D.headers["content-type"]){let F=Object.values(G).find((I)=>{return!!I.getMergedTraits().httpPayload});if(F){let I=F.getMergedTraits().mediaType;if(I)D.headers["content-type"]=I;else if(F.isStringSchema())D.headers["content-type"]="text/plain";else if(F.isBlobSchema())D.headers["content-type"]="application/octet-stream";else D.headers["content-type"]="application/json"}else if(!Z.isUnitSchema()){if(Object.values(G).find((Y)=>{let{httpQuery:W,httpQueryParams:J,httpHeader:X,httpLabel:V,httpPrefixHeaders:C}=Y.getMergedTraits();return!W&&!J&&!X&&!V&&C===void 0}))D.headers["content-type"]="application/json"}}if(D.headers["content-type"]&&!D.body)D.body="{}";if(D.body)try{D.headers["content-length"]=String(IzQ.calculateBodyLength(D.body))}catch(F){}return D}async handleError(A,B,Q,D,Z){let G=at1(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=EQ1.TypeRegistry.for(F),W;try{W=Y.getSchema(G)}catch(K){let H=EQ1.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(H){let z=H.ctor;throw Object.assign(new z(I),D)}throw new Error(I)}let J=EQ1.NormalizedSchema.of(W),X=D.message??D.Message??"Unknown",V=new W.ctor(X);await this.deserializeHttpMessage(W,B,Q,D);let C={};for(let[K,H]of J.structIterator()){let z=H.getMergedTraits().jsonName??K;C[K]=this.codec.createDeserializer().readObject(H,D[z])}throw Object.assign(V,{$metadata:Z,$response:Q,$fault:J.getMergedTraits().error,message:X,...C}),V}},WzQ=g4(),JzQ=A8((A)=>{if(A==null)return;if(typeof A==="object"&&"__type"in A)delete A.__type;return WzQ.expectUnion(A)},"awsExpectUnion"),lt1=M6(),Bk=_Q(),XzQ=PY(),VzQ=M6(),aEA=_Q(),CzQ=g4(),KzQ=cB(),HzQ=uN(),ot1=class extends jh{constructor(A){super();this.settings=A,this.stringDeserializer=new VzQ.FromStringShapeDeserializer(A)}static{A8(this,"XmlShapeDeserializer")}stringDeserializer;setSerdeContext(A){this.serdeContext=A,this.stringDeserializer.setSerdeContext(A)}read(A,B,Q){let D=aEA.NormalizedSchema.of(A),Z=D.getMemberSchemas();if(D.isStructSchema()&&D.isMemberSchema()&&!!Object.values(Z).find((Y)=>{return!!Y.getMemberTraits().eventPayload})){let Y={},W=Object.keys(Z)[0];if(Z[W].isBlobSchema())Y[W]=B;else Y[W]=this.read(Z[W],B);return Y}let F=(this.serdeContext?.utf8Encoder??KzQ.toUtf8)(B),I=this.parseXml(F);return this.readSchema(A,Q?I[Q]:I)}readSchema(A,B){let Q=aEA.NormalizedSchema.of(A),D=Q.getMergedTraits(),Z=Q.getSchema();if(Q.isListSchema()&&!Array.isArray(B))return this.readSchema(Z,[B]);if(B==null)return B;if(typeof B==="object"){let G=!!D.sparse,F=!!D.xmlFlattened;if(Q.isListSchema()){let Y=Q.getValueSchema(),W=[],J=Y.getMergedTraits().xmlName??"member",X=F?B:(B[0]??B)[J],V=Array.isArray(X)?X:[X];for(let C of V)if(C!=null||G)W.push(this.readSchema(Y,C));return W}let I={};if(Q.isMapSchema()){let Y=Q.getKeySchema(),W=Q.getValueSchema(),J;if(F)J=Array.isArray(B)?B:[B];else J=Array.isArray(B.entry)?B.entry:[B.entry];let X=Y.getMergedTraits().xmlName??"key",V=W.getMergedTraits().xmlName??"value";for(let C of J){let K=C[X],H=C[V];if(H!=null||G)I[K]=this.readSchema(W,H)}return I}if(Q.isStructSchema()){for(let[Y,W]of Q.structIterator()){let J=W.getMergedTraits(),X=!J.httpPayload?W.getMemberTraits().xmlName??Y:J.xmlName??W.getName();if(B[X]!=null)I[Y]=this.readSchema(W,B[X])}return I}if(Q.isDocumentSchema())return B;throw new Error(`@aws-sdk/core/protocols - xml deserializer unhandled schema type for ${Q.getName(!0)}`)}else{if(Q.isListSchema())return[];else if(Q.isMapSchema()||Q.isStructSchema())return{};return this.stringDeserializer.read(Q,B)}}parseXml(A){if(A.length){let B=new HzQ.XMLParser({attributeNamePrefix:"",htmlEntities:!0,ignoreAttributes:!1,ignoreDeclaration:!0,parseTagValue:!1,trimValues:!1,tagValueProcessor:A8((F,I)=>I.trim()===""&&I.includes(`
`)?"":void 0,"tagValueProcessor")});B.addEntity("#xD","\r"),B.addEntity("#10",`
`);let Q;try{Q=B.parse(A,!0)}catch(F){if(F&&typeof F==="object")Object.defineProperty(F,"$responseBodyText",{value:A});throw F}let D="#text",Z=Object.keys(Q)[0],G=Q[Z];if(G[D])G[Z]=G[D],delete G[D];return CzQ.getValueFromTextNode(G)}return{}}},pt1=M6(),Sz1=_Q(),zzQ=X6(),EzQ=g4(),UzQ=ty(),wzQ=class extends jh{constructor(A){super();this.settings=A}static{A8(this,"QueryShapeSerializer")}buffer;write(A,B,Q=""){if(this.buffer===void 0)this.buffer="";let D=Sz1.NormalizedSchema.of(A);if(Q&&!Q.endsWith("."))Q+=".";if(D.isBlobSchema()){if(typeof B==="string"||B instanceof Uint8Array)this.writeKey(Q),this.writeValue((this.serdeContext?.base64Encoder??UzQ.toBase64)(B))}else if(D.isBooleanSchema()||D.isNumericSchema()||D.isStringSchema()){if(B!=null)this.writeKey(Q),this.writeValue(String(B))}else if(D.isBigIntegerSchema()){if(B!=null)this.writeKey(Q),this.writeValue(String(B))}else if(D.isBigDecimalSchema()){if(B!=null)this.writeKey(Q),this.writeValue(B instanceof zzQ.NumericValue?B.string:String(B))}else if(D.isTimestampSchema()){if(B instanceof Date)switch(this.writeKey(Q),pt1.determineTimestampFormat(D,this.settings)){case Sz1.SCHEMA.TIMESTAMP_DATE_TIME:this.writeValue(B.toISOString().replace(".000Z","Z"));break;case Sz1.SCHEMA.TIMESTAMP_HTTP_DATE:this.writeValue(EzQ.dateToUtcString(B));break;case Sz1.SCHEMA.TIMESTAMP_EPOCH_SECONDS:this.writeValue(String(B.getTime()/1000));break}}else if(D.isDocumentSchema())throw new Error(`@aws-sdk/core/protocols - QuerySerializer unsupported document type ${D.getName(!0)}`);else if(D.isListSchema()){if(Array.isArray(B))if(B.length===0){if(this.settings.serializeEmptyLists)this.writeKey(Q),this.writeValue("")}else{let Z=D.getValueSchema(),G=this.settings.flattenLists||D.getMergedTraits().xmlFlattened,F=1;for(let I of B){if(I==null)continue;let Y=this.getKey("member",Z.getMergedTraits().xmlName),W=G?`${Q}${F}`:`${Q}${Y}.${F}`;this.write(Z,I,W),++F}}}else if(D.isMapSchema()){if(B&&typeof B==="object"){let Z=D.getKeySchema(),G=D.getValueSchema(),F=D.getMergedTraits().xmlFlattened,I=1;for(let[Y,W]of Object.entries(B)){if(W==null)continue;let J=this.getKey("key",Z.getMergedTraits().xmlName),X=F?`${Q}${I}.${J}`:`${Q}entry.${I}.${J}`,V=this.getKey("value",G.getMergedTraits().xmlName),C=F?`${Q}${I}.${V}`:`${Q}entry.${I}.${V}`;this.write(Z,Y,X),this.write(G,W,C),++I}}}else if(D.isStructSchema()){if(B&&typeof B==="object")for(let[Z,G]of D.structIterator()){if(B[Z]==null)continue;let F=this.getKey(Z,G.getMergedTraits().xmlName),I=`${Q}${F}`;this.write(G,B[Z],I)}}else if(D.isUnitSchema());else throw new Error(`@aws-sdk/core/protocols - QuerySerializer unrecognized schema type ${D.getName(!0)}`)}flush(){if(this.buffer===void 0)throw new Error("@aws-sdk/core/protocols - QuerySerializer cannot flush with nothing written to buffer.");let A=this.buffer;return delete this.buffer,A}getKey(A,B){let Q=B??A;if(this.settings.capitalizeKeys)return Q[0].toUpperCase()+Q.slice(1);return Q}writeKey(A){if(A.endsWith("."))A=A.slice(0,A.length-1);this.buffer+=`&${pt1.extendedEncodeURIComponent(A)}=`}writeValue(A){this.buffer+=pt1.extendedEncodeURIComponent(A)}},QUA=class extends lt1.RpcProtocol{constructor(A){super({defaultNamespace:A.defaultNamespace});this.options=A;let B={timestampFormat:{useTrait:!0,default:Bk.SCHEMA.TIMESTAMP_DATE_TIME},httpBindings:!1,xmlNamespace:A.xmlNamespace,serviceNamespace:A.defaultNamespace,serializeEmptyLists:!0};this.serializer=new wzQ(B),this.deserializer=new ot1(B)}static{A8(this,"AwsQueryProtocol")}serializer;deserializer;getShapeId(){return"aws.protocols#awsQuery"}setSerdeContext(A){this.serializer.setSerdeContext(A),this.deserializer.setSerdeContext(A)}getPayloadCodec(){throw new Error("AWSQuery protocol has no payload codec.")}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q);if(!D.path.endsWith("/"))D.path+="/";if(Object.assign(D.headers,{"content-type":"application/x-www-form-urlencoded"}),Bk.deref(A.input)==="unit"||!D.body)D.body="";if(D.body=`Action=${A.name.split("#")[1]}&Version=${this.options.version}`+D.body,D.body.endsWith("&"))D.body=D.body.slice(-1);try{D.headers["content-length"]=String(XzQ.calculateBodyLength(D.body))}catch(Z){}return D}async deserializeResponse(A,B,Q){let D=this.deserializer,Z=Bk.NormalizedSchema.of(A.output),G={};if(Q.statusCode>=300){let W=await lt1.collectBody(Q.body,B);if(W.byteLength>0)Object.assign(G,await D.read(Bk.SCHEMA.DOCUMENT,W));await this.handleError(A,B,Q,G,this.deserializeMetadata(Q))}for(let W in Q.headers){let J=Q.headers[W];delete Q.headers[W],Q.headers[W.toLowerCase()]=J}let F=Z.isStructSchema()&&this.useNestedResult()?A.name.split("#")[1]+"Result":void 0,I=await lt1.collectBody(Q.body,B);if(I.byteLength>0)Object.assign(G,await D.read(Z,I,F));return{$metadata:this.deserializeMetadata(Q),...G}}useNestedResult(){return!0}async handleError(A,B,Q,D,Z){let G=this.loadQueryErrorCode(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=this.loadQueryError(D),W=Bk.TypeRegistry.for(F),J;try{if(J=W.find((H)=>Bk.NormalizedSchema.of(H).getMergedTraits().awsQueryError?.[0]===I),!J)J=W.getSchema(G)}catch(H){let z=Bk.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(z){let $=z.ctor;throw Object.assign(new $(I),Y)}throw new Error(I)}let X=Bk.NormalizedSchema.of(J),V=this.loadQueryErrorMessage(D),C=new J.ctor(V),K={};for(let[H,z]of X.structIterator()){let $=z.getMergedTraits().xmlName??H,L=Y[$]??D[$];K[H]=this.deserializer.readSchema(z,L)}throw Object.assign(C,{$metadata:Z,$response:Q,$fault:X.getMergedTraits().error,message:V,...K}),C}loadQueryErrorCode(A,B){let Q=(B.Errors?.[0]?.Error??B.Errors?.Error??B.Error)?.Code;if(Q!==void 0)return Q;if(A.statusCode==404)return"NotFound"}loadQueryError(A){return A.Errors?.[0]?.Error??A.Errors?.Error??A.Error}loadQueryErrorMessage(A){let B=this.loadQueryError(A);return B?.message??B?.Message??A.message??A.Message??"Unknown"}},$zQ=class extends QUA{constructor(A){super(A);this.options=A;let B={capitalizeKeys:!0,flattenLists:!0,serializeEmptyLists:!1};Object.assign(this.serializer.settings,B)}static{A8(this,"AwsEc2QueryProtocol")}useNestedResult(){return!1}},it1=M6(),UQ1=_Q(),qzQ=PY(),NzQ=g4(),LzQ=uN(),DUA=A8((A,B)=>eEA(A,B).then((Q)=>{if(Q.length){let D=new LzQ.XMLParser({attributeNamePrefix:"",htmlEntities:!0,ignoreAttributes:!1,ignoreDeclaration:!0,parseTagValue:!1,trimValues:!1,tagValueProcessor:A8((Y,W)=>W.trim()===""&&W.includes(`
`)?"":void 0,"tagValueProcessor")});D.addEntity("#xD","\r"),D.addEntity("#10",`
`);let Z;try{Z=D.parse(Q,!0)}catch(Y){if(Y&&typeof Y==="object")Object.defineProperty(Y,"$responseBodyText",{value:Q});throw Y}let G="#text",F=Object.keys(Z)[0],I=Z[F];if(I[G])I[F]=I[G],delete I[G];return NzQ.getValueFromTextNode(I)}return{}}),"parseXmlBody"),MzQ=A8(async(A,B)=>{let Q=await DUA(A,B);if(Q.Error)Q.Error.message=Q.Error.message??Q.Error.Message;return Q},"parseXmlErrorBody"),ZUA=A8((A,B)=>{if(B?.Error?.Code!==void 0)return B.Error.Code;if(B?.Code!==void 0)return B.Code;if(A.statusCode==404)return"NotFound"},"loadRestXmlErrorCode"),mN=HQ1(),Sh=_Q(),RzQ=X6(),sEA=g4(),rEA=ty(),GUA=class extends jh{constructor(A){super();this.settings=A}static{A8(this,"XmlShapeSerializer")}stringBuffer;byteBuffer;buffer;write(A,B){let Q=Sh.NormalizedSchema.of(A);if(Q.isStringSchema()&&typeof B==="string")this.stringBuffer=B;else if(Q.isBlobSchema())this.byteBuffer="byteLength"in B?B:(this.serdeContext?.base64Decoder??rEA.fromBase64)(B);else{this.buffer=this.writeStruct(Q,B,void 0);let D=Q.getMergedTraits();if(D.httpPayload&&!D.xmlName)this.buffer.withName(Q.getName())}}flush(){if(this.byteBuffer!==void 0){let B=this.byteBuffer;return delete this.byteBuffer,B}if(this.stringBuffer!==void 0){let B=this.stringBuffer;return delete this.stringBuffer,B}let A=this.buffer;if(this.settings.xmlNamespace){if(!A?.attributes?.xmlns)A.addAttribute("xmlns",this.settings.xmlNamespace)}return delete this.buffer,A.toString()}writeStruct(A,B,Q){let D=A.getMergedTraits(),Z=A.isMemberSchema()&&!D.httpPayload?A.getMemberTraits().xmlName??A.getMemberName():D.xmlName??A.getName();if(!Z||!A.isStructSchema())throw new Error(`@aws-sdk/core/protocols - xml serializer, cannot write struct with empty name or non-struct, schema=${A.getName(!0)}.`);let G=mN.XmlNode.of(Z),[F,I]=this.getXmlnsAttribute(A,Q);if(I)G.addAttribute(F,I);for(let[Y,W]of A.structIterator()){let J=B[Y];if(J!=null){if(W.getMergedTraits().xmlAttribute){G.addAttribute(W.getMergedTraits().xmlName??Y,this.writeSimple(W,J));continue}if(W.isListSchema())this.writeList(W,J,G,I);else if(W.isMapSchema())this.writeMap(W,J,G,I);else if(W.isStructSchema())G.addChildNode(this.writeStruct(W,J,I));else{let X=mN.XmlNode.of(W.getMergedTraits().xmlName??W.getMemberName());this.writeSimpleInto(W,J,X,I),G.addChildNode(X)}}}return G}writeList(A,B,Q,D){if(!A.isMemberSchema())throw new Error(`@aws-sdk/core/protocols - xml serializer, cannot write non-member list: ${A.getName(!0)}`);let Z=A.getMergedTraits(),G=A.getValueSchema(),F=G.getMergedTraits(),I=!!F.sparse,Y=!!Z.xmlFlattened,[W,J]=this.getXmlnsAttribute(A,D),X=A8((V,C)=>{if(G.isListSchema())this.writeList(G,Array.isArray(C)?C:[C],V,J);else if(G.isMapSchema())this.writeMap(G,C,V,J);else if(G.isStructSchema()){let K=this.writeStruct(G,C,J);V.addChildNode(K.withName(Y?Z.xmlName??A.getMemberName():F.xmlName??"member"))}else{let K=mN.XmlNode.of(Y?Z.xmlName??A.getMemberName():F.xmlName??"member");this.writeSimpleInto(G,C,K,J),V.addChildNode(K)}},"writeItem");if(Y){for(let V of B)if(I||V!=null)X(Q,V)}else{let V=mN.XmlNode.of(Z.xmlName??A.getMemberName());if(J)V.addAttribute(W,J);for(let C of B)if(I||C!=null)X(V,C);Q.addChildNode(V)}}writeMap(A,B,Q,D,Z=!1){if(!A.isMemberSchema())throw new Error(`@aws-sdk/core/protocols - xml serializer, cannot write non-member map: ${A.getName(!0)}`);let G=A.getMergedTraits(),F=A.getKeySchema(),Y=F.getMergedTraits().xmlName??"key",W=A.getValueSchema(),J=W.getMergedTraits(),X=J.xmlName??"value",V=!!J.sparse,C=!!G.xmlFlattened,[K,H]=this.getXmlnsAttribute(A,D),z=A8(($,L,N)=>{let O=mN.XmlNode.of(Y,L),[R,T]=this.getXmlnsAttribute(F,H);if(T)O.addAttribute(R,T);$.addChildNode(O);let j=mN.XmlNode.of(X);if(W.isListSchema())this.writeList(W,N,j,H);else if(W.isMapSchema())this.writeMap(W,N,j,H,!0);else if(W.isStructSchema())j=this.writeStruct(W,N,H);else this.writeSimpleInto(W,N,j,H);$.addChildNode(j)},"addKeyValue");if(C){for(let[$,L]of Object.entries(B))if(V||L!=null){let N=mN.XmlNode.of(G.xmlName??A.getMemberName());z(N,$,L),Q.addChildNode(N)}}else{let $;if(!Z){if($=mN.XmlNode.of(G.xmlName??A.getMemberName()),H)$.addAttribute(K,H);Q.addChildNode($)}for(let[L,N]of Object.entries(B))if(V||N!=null){let O=mN.XmlNode.of("entry");z(O,L,N),(Z?Q:$).addChildNode(O)}}}writeSimple(A,B){if(B===null)throw new Error("@aws-sdk/core/protocols - (XML serializer) cannot write null value.");let Q=Sh.NormalizedSchema.of(A),D=null;if(B&&typeof B==="object")if(Q.isBlobSchema())D=(this.serdeContext?.base64Encoder??rEA.toBase64)(B);else if(Q.isTimestampSchema()&&B instanceof Date){let Z=this.settings.timestampFormat;switch(Z.useTrait?Q.getSchema()===Sh.SCHEMA.TIMESTAMP_DEFAULT?Z.default:Q.getSchema()??Z.default:Z.default){case Sh.SCHEMA.TIMESTAMP_DATE_TIME:D=B.toISOString().replace(".000Z","Z");break;case Sh.SCHEMA.TIMESTAMP_HTTP_DATE:D=sEA.dateToUtcString(B);break;case Sh.SCHEMA.TIMESTAMP_EPOCH_SECONDS:D=String(B.getTime()/1000);break;default:console.warn("Missing timestamp format, using http date",B),D=sEA.dateToUtcString(B);break}}else if(Q.isBigDecimalSchema()&&B){if(B instanceof RzQ.NumericValue)return B.string;return String(B)}else if(Q.isMapSchema()||Q.isListSchema())throw new Error("@aws-sdk/core/protocols - xml serializer, cannot call _write() on List/Map schema, call writeList or writeMap() instead.");else throw new Error(`@aws-sdk/core/protocols - xml serializer, unhandled schema type for object value and schema: ${Q.getName(!0)}`);if(Q.isStringSchema()||Q.isBooleanSchema()||Q.isNumericSchema()||Q.isBigIntegerSchema()||Q.isBigDecimalSchema())D=String(B);if(D===null)throw new Error(`Unhandled schema-value pair ${Q.getName(!0)}=${B}`);return D}writeSimpleInto(A,B,Q,D){let Z=this.writeSimple(A,B),G=Sh.NormalizedSchema.of(A),F=new mN.XmlText(Z),[I,Y]=this.getXmlnsAttribute(G,D);if(Y)Q.addAttribute(I,Y);Q.addChildNode(F)}getXmlnsAttribute(A,B){let Q=A.getMergedTraits(),[D,Z]=Q.xmlNamespace??[];if(Z&&Z!==B)return[D?`xmlns:${D}`:"xmlns",Z];return[void 0,void 0]}},FUA=class extends jh{constructor(A){super();this.settings=A}static{A8(this,"XmlCodec")}createSerializer(){let A=new GUA(this.settings);return A.setSerdeContext(this.serdeContext),A}createDeserializer(){let A=new ot1(this.settings);return A.setSerdeContext(this.serdeContext),A}},OzQ=class extends it1.HttpBindingProtocol{static{A8(this,"AwsRestXmlProtocol")}codec;serializer;deserializer;constructor(A){super(A);let B={timestampFormat:{useTrait:!0,default:UQ1.SCHEMA.TIMESTAMP_DATE_TIME},httpBindings:!0,xmlNamespace:A.xmlNamespace,serviceNamespace:A.defaultNamespace};this.codec=new FUA(B),this.serializer=new it1.HttpInterceptingShapeSerializer(this.codec.createSerializer(),B),this.deserializer=new it1.HttpInterceptingShapeDeserializer(this.codec.createDeserializer(),B)}getPayloadCodec(){return this.codec}getShapeId(){return"aws.protocols#restXml"}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q),Z=UQ1.NormalizedSchema.of(A.input),G=Z.getMemberSchemas();if(D.path=String(D.path).split("/").filter((F)=>{return F!=="{Bucket}"}).join("/")||"/",!D.headers["content-type"]){let F=Object.values(G).find((I)=>{return!!I.getMergedTraits().httpPayload});if(F){let I=F.getMergedTraits().mediaType;if(I)D.headers["content-type"]=I;else if(F.isStringSchema())D.headers["content-type"]="text/plain";else if(F.isBlobSchema())D.headers["content-type"]="application/octet-stream";else D.headers["content-type"]="application/xml"}else if(!Z.isUnitSchema()){if(Object.values(G).find((Y)=>{let{httpQuery:W,httpQueryParams:J,httpHeader:X,httpLabel:V,httpPrefixHeaders:C}=Y.getMergedTraits();return!W&&!J&&!X&&!V&&C===void 0}))D.headers["content-type"]="application/xml"}}if(D.headers["content-type"]==="application/xml"){if(typeof D.body==="string")D.body='<?xml version="1.0" encoding="UTF-8"?>'+D.body}if(D.body)try{D.headers["content-length"]=String(qzQ.calculateBodyLength(D.body))}catch(F){}return D}async deserializeResponse(A,B,Q){return super.deserializeResponse(A,B,Q)}async handleError(A,B,Q,D,Z){let G=ZUA(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=UQ1.TypeRegistry.for(F),W;try{W=Y.getSchema(G)}catch(K){let H=UQ1.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(H){let z=H.ctor;throw Object.assign(new z(I),D)}throw new Error(I)}let J=UQ1.NormalizedSchema.of(W),X=D.Error?.message??D.Error?.Message??D.message??D.Message??"Unknown",V=new W.ctor(X);await this.deserializeHttpMessage(W,B,Q,D);let C={};for(let[K,H]of J.structIterator()){let z=H.getMergedTraits().xmlName??K,$=D.Error?.[z]??D[z];C[K]=this.codec.createDeserializer().readSchema(H,$)}throw Object.assign(V,{$metadata:Z,$response:Q,$fault:J.getMergedTraits().error,message:X,...C}),V}}});
var Yn=E((ZwA)=>{Object.defineProperty(ZwA,"__esModule",{value:!0});ZwA.getHomeDir=void 0;var iEQ=J1("os"),nEQ=J1("path"),Qe1={},aEQ=()=>{if(process&&process.geteuid)return`${process.geteuid()}`;return"DEFAULT"},sEQ=()=>{let{HOME:A,USERPROFILE:B,HOMEPATH:Q,HOMEDRIVE:D=`C:${nEQ.sep}`}=process.env;if(A)return A;if(B)return B;if(Q)return`${D}${Q}`;let Z=aEQ();if(!Qe1[Z])Qe1[Z]=iEQ.homedir();return Qe1[Z]};ZwA.getHomeDir=sEQ});
var Yz=E((tqA)=>{Object.defineProperty(tqA,"__esModule",{value:!0});tqA.commonParams=tqA.resolveClientEndpointParameters=void 0;var eNQ=(A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,useGlobalEndpoint:A.useGlobalEndpoint??!1,defaultSigningName:"sts"})};tqA.resolveClientEndpointParameters=eNQ;tqA.commonParams={UseGlobalEndpoint:{type:"builtInParams",name:"useGlobalEndpoint"},UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}}});
var Z10=E((OF5,dOQ)=>{dOQ.exports={name:"@aws-sdk/nested-clients",version:"3.840.0",description:"Nested clients for AWS SDK packages.",main:"./dist-cjs/index.js",module:"./dist-es/index.js",types:"./dist-types/index.d.ts",scripts:{build:"yarn lint && concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'","build:cjs":"node ../../scripts/compilation/inline nested-clients","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4",clean:"rimraf ./dist-* && rimraf *.tsbuildinfo",lint:"node ../../scripts/validation/submodules-linter.js --pkg nested-clients",test:"yarn g:vitest run","test:watch":"yarn g:vitest watch"},engines:{node:">=18.0.0"},author:{name:"AWS SDK for JavaScript Team",url:"https://aws.amazon.com/javascript/"},license:"Apache-2.0",dependencies:{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.840.0","@aws-sdk/middleware-host-header":"3.840.0","@aws-sdk/middleware-logger":"3.840.0","@aws-sdk/middleware-recursion-detection":"3.840.0","@aws-sdk/middleware-user-agent":"3.840.0","@aws-sdk/region-config-resolver":"3.840.0","@aws-sdk/types":"3.840.0","@aws-sdk/util-endpoints":"3.840.0","@aws-sdk/util-user-agent-browser":"3.840.0","@aws-sdk/util-user-agent-node":"3.840.0","@smithy/config-resolver":"^4.1.4","@smithy/core":"^3.6.0","@smithy/fetch-http-handler":"^5.0.4","@smithy/hash-node":"^4.0.4","@smithy/invalid-dependency":"^4.0.4","@smithy/middleware-content-length":"^4.0.4","@smithy/middleware-endpoint":"^4.1.13","@smithy/middleware-retry":"^4.1.14","@smithy/middleware-serde":"^4.0.8","@smithy/middleware-stack":"^4.0.4","@smithy/node-config-provider":"^4.1.3","@smithy/node-http-handler":"^4.0.6","@smithy/protocol-http":"^5.1.2","@smithy/smithy-client":"^4.4.5","@smithy/types":"^4.3.1","@smithy/url-parser":"^4.0.4","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.21","@smithy/util-defaults-mode-node":"^4.0.21","@smithy/util-endpoints":"^3.0.6","@smithy/util-middleware":"^4.0.4","@smithy/util-retry":"^4.0.6","@smithy/util-utf8":"^4.0.0",tslib:"^2.6.2"},devDependencies:{concurrently:"7.0.0","downlevel-dts":"0.10.1",rimraf:"3.0.2",typescript:"~5.8.3"},typesVersions:{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},files:["./sso-oidc.d.ts","./sso-oidc.js","./sts.d.ts","./sts.js","dist-*/**"],browser:{"./dist-es/submodules/sso-oidc/runtimeConfig":"./dist-es/submodules/sso-oidc/runtimeConfig.browser","./dist-es/submodules/sts/runtimeConfig":"./dist-es/submodules/sts/runtimeConfig.browser"},"react-native":{},homepage:"https://github.com/aws/aws-sdk-js-v3/tree/main/packages/nested-clients",repository:{type:"git",url:"https://github.com/aws/aws-sdk-js-v3.git",directory:"packages/nested-clients"},exports:{"./sso-oidc":{types:"./dist-types/submodules/sso-oidc/index.d.ts",module:"./dist-es/submodules/sso-oidc/index.js",node:"./dist-cjs/submodules/sso-oidc/index.js",import:"./dist-es/submodules/sso-oidc/index.js",require:"./dist-cjs/submodules/sso-oidc/index.js"},"./sts":{types:"./dist-types/submodules/sts/index.d.ts",module:"./dist-es/submodules/sts/index.js",node:"./dist-cjs/submodules/sts/index.js",import:"./dist-es/submodules/sts/index.js",require:"./dist-cjs/submodules/sts/index.js"}}}});
var ZqA=E((QqA)=>{Object.defineProperty(QqA,"__esModule",{value:!0});QqA.default=void 0;var DqQ="00000000-0000-0000-0000-000000000000";QqA.default=DqQ});
var Zz=E((nD5,lHA)=>{var{defineProperty:Yz1,getOwnPropertyDescriptor:pXQ,getOwnPropertyNames:iXQ}=Object,nXQ=Object.prototype.hasOwnProperty,Wz1=(A,B)=>Yz1(A,"name",{value:B,configurable:!0}),aXQ=(A,B)=>{for(var Q in B)Yz1(A,Q,{get:B[Q],enumerable:!0})},sXQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of iXQ(B))if(!nXQ.call(A,Z)&&Z!==Q)Yz1(A,Z,{get:()=>B[Z],enumerable:!(D=pXQ(B,Z))||D.enumerable})}return A},rXQ=(A)=>sXQ(Yz1({},"__esModule",{value:!0}),A),uHA={};aXQ(uHA,{emitWarningIfUnsupportedVersion:()=>oXQ,setCredentialFeature:()=>mHA,setFeature:()=>dHA,setTokenFeature:()=>cHA,state:()=>Kt1});lHA.exports=rXQ(uHA);var Kt1={warningEmitted:!1},oXQ=Wz1((A)=>{if(A&&!Kt1.warningEmitted&&parseInt(A.substring(1,A.indexOf(".")))<18)Kt1.warningEmitted=!0,process.emitWarning(`NodeDeprecationWarning: The AWS SDK for JavaScript (v3) will
no longer support Node.js 16.x on January 6, 2025.

To continue receiving updates to AWS services, bug fixes, and security
updates please upgrade to a supported Node.js LTS version.

More information can be found at: https://a.co/74kJMmI`)},"emitWarningIfUnsupportedVersion");function mHA(A,B,Q){if(!A.$source)A.$source={};return A.$source[B]=Q,A}Wz1(mHA,"setCredentialFeature");function dHA(A,B,Q){if(!A.__aws_sdk_context)A.__aws_sdk_context={features:{}};else if(!A.__aws_sdk_context.features)A.__aws_sdk_context.features={};A.__aws_sdk_context.features[B]=Q}Wz1(dHA,"setFeature");function cHA(A,B,Q){if(!A.$source)A.$source={};return A.$source[B]=Q,A}Wz1(cHA,"setTokenFeature")});
var _CA=E((yCA)=>{Object.defineProperty(yCA,"__esModule",{value:!0});yCA.sdkStreamMixin=void 0;var jYQ=k3(),yYQ=YD(),xo1=J1("stream"),kYQ=SCA(),jCA="The stream has already been transformed.",_YQ=(A)=>{var B,Q;if(!(A instanceof xo1.Readable))try{return kYQ.sdkStreamMixin(A)}catch(G){let F=((Q=(B=A===null||A===void 0?void 0:A.__proto__)===null||B===void 0?void 0:B.constructor)===null||Q===void 0?void 0:Q.name)||A;throw new Error(`Unexpected stream implementation, expect Stream.Readable instance, got ${F}`)}let D=!1,Z=async()=>{if(D)throw new Error(jCA);return D=!0,await jYQ.streamCollector(A)};return Object.assign(A,{transformToByteArray:Z,transformToString:async(G)=>{let F=await Z();if(G===void 0||Buffer.isEncoding(G))return yYQ.fromArrayBuffer(F.buffer,F.byteOffset,F.byteLength).toString(G);else return new TextDecoder(G).decode(F)},transformToWebStream:()=>{if(D)throw new Error(jCA);if(A.readableFlowing!==null)throw new Error("The stream has been consumed by other callbacks.");if(typeof xo1.Readable.toWeb!=="function")throw new Error("Readable.toWeb() is not supported. Please ensure a polyfill is available.");return D=!0,xo1.Readable.toWeb(A)}})};yCA.sdkStreamMixin=_YQ});
var _EA=E((LZ5,kEA)=>{var{buildOptions:UHQ}=EEA(),wHQ=PEA(),{prettify:$HQ}=jEA(),qHQ=ut1();class yEA{constructor(A){this.externalEntities={},this.options=UHQ(A)}parse(A,B){if(typeof A==="string");else if(A.toString)A=A.toString();else throw new Error("XML data is accepted in String or Bytes[] form.");if(B){if(B===!0)B={};let Z=qHQ.validate(A,B);if(Z!==!0)throw Error(`${Z.err.msg}:${Z.err.line}:${Z.err.col}`)}let Q=new wHQ(this.options);Q.addExternalEntities(this.externalEntities);let D=Q.parseXml(A);if(this.options.preserveOrder||D===void 0)return D;else return $HQ(D,this.options)}addEntity(A,B){if(B.indexOf("&")!==-1)throw new Error("Entity value can't have '&'");else if(A.indexOf("&")!==-1||A.indexOf(";")!==-1)throw new Error("An entity must be set without '&' and ';'. Eg. use '#xD' for '&#xD;'");else if(B==="&")throw new Error("An entity with value '&' is not permitted");else this.externalEntities[A]=B}}kEA.exports=yEA});
var _Q=E((KD5,sCA)=>{var{defineProperty:go1,getOwnPropertyDescriptor:lYQ,getOwnPropertyNames:pYQ}=Object,iYQ=Object.prototype.hasOwnProperty,nYQ=(A,B)=>{for(var Q in B)go1(A,Q,{get:B[Q],enumerable:!0})},aYQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of pYQ(B))if(!iYQ.call(A,Z)&&Z!==Q)go1(A,Z,{get:()=>B[Z],enumerable:!(D=lYQ(B,Z))||D.enumerable})}return A},sYQ=(A)=>aYQ(go1({},"__esModule",{value:!0}),A),lCA={};nYQ(lCA,{ErrorSchema:()=>aCA,ListSchema:()=>uo1,MapSchema:()=>mo1,NormalizedSchema:()=>YWQ,OperationSchema:()=>nCA,SCHEMA:()=>iZ,Schema:()=>ai,SimpleSchema:()=>do1,StructureSchema:()=>pH1,TypeRegistry:()=>Nh,deref:()=>QQ1,deserializerMiddlewareOption:()=>pCA,error:()=>FWQ,getSchemaSerdePlugin:()=>BWQ,list:()=>QWQ,map:()=>DWQ,op:()=>ZWQ,serializerMiddlewareOption:()=>iCA,sim:()=>IWQ,struct:()=>GWQ});sCA.exports=sYQ(lCA);var QQ1=(A)=>{if(typeof A==="function")return A();return A},rYQ=qh(),oYQ=J5(),tYQ=(A)=>(B,Q)=>async(D)=>{let{response:Z}=await B(D),{operationSchema:G}=oYQ.getSmithyContext(Q);try{let F=await A.protocol.deserializeResponse(G,{...A,...Q},Z);return{response:Z,output:F}}catch(F){if(Object.defineProperty(F,"$response",{value:Z}),!("$metadata"in F)){try{F.message+=`
  Deserialization error: to see the raw response, inspect the hidden field {error}.$response on this object.`}catch(Y){if(!Q.logger||Q.logger?.constructor?.name==="NoOpLogger")console.warn("Deserialization error: to see the raw response, inspect the hidden field {error}.$response on this object.");else Q.logger?.warn?.("Deserialization error: to see the raw response, inspect the hidden field {error}.$response on this object.")}if(typeof F.$responseBodyText!=="undefined"){if(F.$response)F.$response.body=F.$responseBodyText}try{if(rYQ.HttpResponse.isInstance(Z)){let{headers:Y={}}=Z,W=Object.entries(Y);F.$metadata={httpStatusCode:Z.statusCode,requestId:ho1(/^x-[\w-]+-request-?id$/,W),extendedRequestId:ho1(/^x-[\w-]+-id-2$/,W),cfId:ho1(/^x-[\w-]+-cf-id$/,W)}}}catch(Y){}}throw F}},ho1=(A,B)=>{return(B.find(([Q])=>{return Q.match(A)})||[void 0,void 0])[1]},eYQ=J5(),AWQ=(A)=>(B,Q)=>async(D)=>{let{operationSchema:Z}=eYQ.getSmithyContext(Q),G=Q.endpointV2?.url&&A.urlParser?async()=>A.urlParser(Q.endpointV2.url):A.endpoint,F=await A.protocol.serializeRequest(Z,D.input,{...A,...Q,endpoint:G});return B({...D,request:F})},pCA={name:"deserializerMiddleware",step:"deserialize",tags:["DESERIALIZER"],override:!0},iCA={name:"serializerMiddleware",step:"serialize",tags:["SERIALIZER"],override:!0};function BWQ(A){return{applyToStack:(B)=>{B.add(AWQ(A),iCA),B.add(tYQ(A),pCA),A.protocol.setSerdeContext(A)}}}var Nh=class A{constructor(B,Q=new Map){this.namespace=B,this.schemas=Q}static{this.registries=new Map}static for(B){if(!A.registries.has(B))A.registries.set(B,new A(B));return A.registries.get(B)}register(B,Q){let D=this.normalizeShapeId(B);A.for(this.getNamespace(B)).schemas.set(D,Q)}getSchema(B){let Q=this.normalizeShapeId(B);if(!this.schemas.has(Q))throw new Error(`@smithy/core/schema - schema not found for ${Q}`);return this.schemas.get(Q)}getBaseException(){for(let[B,Q]of this.schemas.entries())if(B.startsWith("smithy.ts.sdk.synthetic.")&&B.endsWith("ServiceException"))return Q;return}find(B){return[...this.schemas.values()].find(B)}destroy(){A.registries.delete(this.namespace),this.schemas.clear()}normalizeShapeId(B){if(B.includes("#"))return B;return this.namespace+"#"+B}getNamespace(B){return this.normalizeShapeId(B).split("#")[0]}},ai=class{constructor(A,B){this.name=A,this.traits=B}},uo1=class extends ai{constructor(A,B,Q){super(A,B);this.name=A,this.traits=B,this.valueSchema=Q}};function QWQ(A,B,Q={},D){let Z=new uo1(A+"#"+B,Q,typeof D==="function"?D():D);return Nh.for(A).register(B,Z),Z}var mo1=class extends ai{constructor(A,B,Q,D){super(A,B);this.name=A,this.traits=B,this.keySchema=Q,this.valueSchema=D}};function DWQ(A,B,Q={},D,Z){let G=new mo1(A+"#"+B,Q,D,typeof Z==="function"?Z():Z);return Nh.for(A).register(B,G),G}var nCA=class extends ai{constructor(A,B,Q,D){super(A,B);this.name=A,this.traits=B,this.input=Q,this.output=D}};function ZWQ(A,B,Q={},D,Z){let G=new nCA(A+"#"+B,Q,D,Z);return Nh.for(A).register(B,G),G}var pH1=class extends ai{constructor(A,B,Q,D){super(A,B);this.name=A,this.traits=B,this.memberNames=Q,this.memberList=D,this.members={};for(let Z=0;Z<Q.length;++Z)this.members[Q[Z]]=Array.isArray(D[Z])?D[Z]:[D[Z],0]}};function GWQ(A,B,Q,D,Z){let G=new pH1(A+"#"+B,Q,D,Z);return Nh.for(A).register(B,G),G}var aCA=class extends pH1{constructor(A,B,Q,D,Z){super(A,B,Q,D);this.name=A,this.traits=B,this.memberNames=Q,this.memberList=D,this.ctor=Z}};function FWQ(A,B,Q={},D,Z,G){let F=new aCA(A+"#"+B,Q,D,Z,G);return Nh.for(A).register(B,F),F}var iZ={BLOB:21,STREAMING_BLOB:42,BOOLEAN:2,STRING:0,NUMERIC:1,BIG_INTEGER:17,BIG_DECIMAL:19,DOCUMENT:15,TIMESTAMP_DEFAULT:4,TIMESTAMP_DATE_TIME:5,TIMESTAMP_HTTP_DATE:6,TIMESTAMP_EPOCH_SECONDS:7,LIST_MODIFIER:64,MAP_MODIFIER:128},do1=class extends ai{constructor(A,B,Q){super(A,Q);this.name=A,this.schemaRef=B,this.traits=Q}};function IWQ(A,B,Q,D){let Z=new do1(A+"#"+B,Q,D);return Nh.for(A).register(B,Z),Z}var YWQ=class A{constructor(B,Q){this.ref=B,this.memberName=Q;let D=[],Z=B,G=B;this._isMemberSchema=!1;while(Array.isArray(Z))D.push(Z[1]),Z=Z[0],G=QQ1(Z),this._isMemberSchema=!0;if(D.length>0){this.memberTraits={};for(let F=D.length-1;F>=0;--F){let I=D[F];Object.assign(this.memberTraits,A.translateTraits(I))}}else this.memberTraits=0;if(G instanceof A){this.name=G.name,this.traits=G.traits,this._isMemberSchema=G._isMemberSchema,this.schema=G.schema,this.memberTraits=Object.assign({},G.getMemberTraits(),this.getMemberTraits()),this.normalizedTraits=void 0,this.ref=G.ref,this.memberName=Q??G.memberName;return}if(this.schema=QQ1(G),this.schema&&typeof this.schema==="object")this.traits=this.schema?.traits??{};else this.traits=0;if(this.name=(typeof this.schema==="object"?this.schema?.name:void 0)??this.memberName??this.getSchemaName(),this._isMemberSchema&&!Q)throw new Error(`@smithy/core/schema - NormalizedSchema member schema ${this.getName(!0)} must initialize with memberName argument.`)}static of(B,Q){if(B instanceof A)return B;return new A(B,Q)}static translateTraits(B){if(typeof B==="object")return B;B=B|0;let Q={};if((B&1)===1)Q.httpLabel=1;if((B>>1&1)===1)Q.idempotent=1;if((B>>2&1)===1)Q.idempotencyToken=1;if((B>>3&1)===1)Q.sensitive=1;if((B>>4&1)===1)Q.httpPayload=1;if((B>>5&1)===1)Q.httpResponseCode=1;if((B>>6&1)===1)Q.httpQueryParams=1;return Q}static memberFrom(B,Q){if(B instanceof A)return B.memberName=Q,B._isMemberSchema=!0,B;return new A(B,Q)}getSchema(){if(this.schema instanceof A)return this.schema=this.schema.getSchema();if(this.schema instanceof do1)return QQ1(this.schema.schemaRef);return QQ1(this.schema)}getName(B=!1){if(!B){if(this.name&&this.name.includes("#"))return this.name.split("#")[1]}return this.name||void 0}getMemberName(){if(!this.isMemberSchema())throw new Error(`@smithy/core/schema - cannot get member name on non-member schema: ${this.getName(!0)}`);return this.memberName}isMemberSchema(){return this._isMemberSchema}isUnitSchema(){return this.getSchema()==="unit"}isListSchema(){let B=this.getSchema();if(typeof B==="number")return B>=iZ.LIST_MODIFIER&&B<iZ.MAP_MODIFIER;return B instanceof uo1}isMapSchema(){let B=this.getSchema();if(typeof B==="number")return B>=iZ.MAP_MODIFIER&&B<=255;return B instanceof mo1}isDocumentSchema(){return this.getSchema()===iZ.DOCUMENT}isStructSchema(){let B=this.getSchema();return B!==null&&typeof B==="object"&&"members"in B||B instanceof pH1}isBlobSchema(){return this.getSchema()===iZ.BLOB||this.getSchema()===iZ.STREAMING_BLOB}isTimestampSchema(){let B=this.getSchema();return typeof B==="number"&&B>=iZ.TIMESTAMP_DEFAULT&&B<=iZ.TIMESTAMP_EPOCH_SECONDS}isStringSchema(){return this.getSchema()===iZ.STRING}isBooleanSchema(){return this.getSchema()===iZ.BOOLEAN}isNumericSchema(){return this.getSchema()===iZ.NUMERIC}isBigIntegerSchema(){return this.getSchema()===iZ.BIG_INTEGER}isBigDecimalSchema(){return this.getSchema()===iZ.BIG_DECIMAL}isStreaming(){if(!!this.getMergedTraits().streaming)return!0;return this.getSchema()===iZ.STREAMING_BLOB}getMergedTraits(){if(this.normalizedTraits)return this.normalizedTraits;return this.normalizedTraits={...this.getOwnTraits(),...this.getMemberTraits()},this.normalizedTraits}getMemberTraits(){return A.translateTraits(this.memberTraits)}getOwnTraits(){return A.translateTraits(this.traits)}getKeySchema(){if(this.isDocumentSchema())return A.memberFrom([iZ.DOCUMENT,0],"key");if(!this.isMapSchema())throw new Error(`@smithy/core/schema - cannot get key schema for non-map schema: ${this.getName(!0)}`);let B=this.getSchema();if(typeof B==="number")return A.memberFrom([63&B,0],"key");return A.memberFrom([B.keySchema,0],"key")}getValueSchema(){let B=this.getSchema();if(typeof B==="number"){if(this.isMapSchema())return A.memberFrom([63&B,0],"value");else if(this.isListSchema())return A.memberFrom([63&B,0],"member")}if(B&&typeof B==="object"){if(this.isStructSchema())throw new Error(`cannot call getValueSchema() with StructureSchema ${this.getName(!0)}`);let Q=B;if("valueSchema"in Q){if(this.isMapSchema())return A.memberFrom([Q.valueSchema,0],"value");else if(this.isListSchema())return A.memberFrom([Q.valueSchema,0],"member")}}if(this.isDocumentSchema())return A.memberFrom([iZ.DOCUMENT,0],"value");throw new Error(`@smithy/core/schema - the schema ${this.getName(!0)} does not have a value member.`)}getMemberSchema(B){if(this.isStructSchema()){let Q=this.getSchema();if(!(B in Q.members))throw new Error(`@smithy/core/schema - the schema ${this.getName(!0)} does not have a member with name=${B}.`);return A.memberFrom(Q.members[B],B)}if(this.isDocumentSchema())return A.memberFrom([iZ.DOCUMENT,0],B);throw new Error(`@smithy/core/schema - the schema ${this.getName(!0)} does not have members.`)}getMemberSchemas(){let{schema:B}=this,Q=B;if(!Q||typeof Q!=="object")return{};if("members"in Q){let D={};for(let Z of Q.memberNames)D[Z]=this.getMemberSchema(Z);return D}return{}}*structIterator(){if(this.isUnitSchema())return;if(!this.isStructSchema())throw new Error("@smithy/core/schema - cannot acquire structIterator on non-struct schema.");let B=this.getSchema();for(let Q=0;Q<B.memberNames.length;++Q)yield[B.memberNames[Q],A.memberFrom([B.memberList[Q],0],B.memberNames[Q])]}getSchemaName(){let B=this.getSchema();if(typeof B==="number"){let Q=63&B,D=192&B,Z=Object.entries(iZ).find(([,G])=>{return G===Q})?.[0]??"Unknown";switch(D){case iZ.MAP_MODIFIER:return`${Z}Map`;case iZ.LIST_MODIFIER:return`${Z}List`;case 0:return Z}}return"Unknown"}}});
var a$A=E((i$A)=>{Object.defineProperty(i$A,"__esModule",{value:!0});i$A.default=void 0;var l$A=p$A(c$A()),p$Q=p$A(Ce1()),i$Q=MQ1();function p$A(A){return A&&A.__esModule?A:{default:A}}function n$Q(A,B,Q){if(l$A.default.randomUUID&&!B&&!A)return l$A.default.randomUUID();A=A||{};let D=A.random||(A.rng||p$Q.default)();if(D[6]=D[6]&15|64,D[8]=D[8]&63|128,B){Q=Q||0;for(let Z=0;Z<16;++Z)B[Q+Z]=D[Z];return B}return i$Q.unsafeStringify(D)}var a$Q=n$Q;i$A.default=a$Q});
var ay=E((YD5,LCA)=>{var{defineProperty:cH1,getOwnPropertyDescriptor:wYQ,getOwnPropertyNames:$YQ}=Object,qYQ=Object.prototype.hasOwnProperty,UCA=(A,B)=>cH1(A,"name",{value:B,configurable:!0}),NYQ=(A,B)=>{for(var Q in B)cH1(A,Q,{get:B[Q],enumerable:!0})},LYQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of $YQ(B))if(!qYQ.call(A,Z)&&Z!==Q)cH1(A,Z,{get:()=>B[Z],enumerable:!(D=wYQ(B,Z))||D.enumerable})}return A},MYQ=(A)=>LYQ(cH1({},"__esModule",{value:!0}),A),wCA={};NYQ(wCA,{fromHex:()=>qCA,toHex:()=>NCA});LCA.exports=MYQ(wCA);var $CA={},_o1={};for(let A=0;A<256;A++){let B=A.toString(16).toLowerCase();if(B.length===1)B=`0${B}`;$CA[A]=B,_o1[B]=A}function qCA(A){if(A.length%2!==0)throw new Error("Hex encoded strings must have an even number length");let B=new Uint8Array(A.length/2);for(let Q=0;Q<A.length;Q+=2){let D=A.slice(Q,Q+2).toLowerCase();if(D in _o1)B[Q/2]=_o1[D];else throw new Error(`Cannot decode unrecognized sequence ${D} as hexadecimal`)}return B}UCA(qCA,"fromHex");function NCA(A){let B="";for(let Q=0;Q<A.byteLength;Q++)B+=$CA[A[Q]];return B}UCA(NCA,"toHex")});
var b$A=E((x$A)=>{Object.defineProperty(x$A,"__esModule",{value:!0});x$A.default=void 0;var x$Q=v$Q(J1("crypto"));function v$Q(A){return A&&A.__esModule?A:{default:A}}function b$Q(A){if(Array.isArray(A))A=Buffer.from(A);else if(typeof A==="string")A=Buffer.from(A,"utf8");return x$Q.default.createHash("md5").update(A).digest()}var f$Q=b$Q;x$A.default=f$Q});
var bG=E((BG5,DwA)=>{var{defineProperty:gz1,getOwnPropertyDescriptor:hEQ,getOwnPropertyNames:gEQ}=Object,uEQ=Object.prototype.hasOwnProperty,AwA=(A,B)=>gz1(A,"name",{value:B,configurable:!0}),mEQ=(A,B)=>{for(var Q in B)gz1(A,Q,{get:B[Q],enumerable:!0})},dEQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of gEQ(B))if(!uEQ.call(A,Z)&&Z!==Q)gz1(A,Z,{get:()=>B[Z],enumerable:!(D=hEQ(B,Z))||D.enumerable})}return A},cEQ=(A)=>dEQ(gz1({},"__esModule",{value:!0}),A),BwA={};mEQ(BwA,{contentLengthMiddleware:()=>Be1,contentLengthMiddlewareOptions:()=>QwA,getContentLengthPlugin:()=>pEQ});DwA.exports=cEQ(BwA);var lEQ=tUA(),eUA="content-length";function Be1(A){return(B)=>async(Q)=>{let D=Q.request;if(lEQ.HttpRequest.isInstance(D)){let{body:Z,headers:G}=D;if(Z&&Object.keys(G).map((F)=>F.toLowerCase()).indexOf(eUA)===-1)try{let F=A(Z);D.headers={...D.headers,[eUA]:String(F)}}catch(F){}}return B({...Q,request:D})}}AwA(Be1,"contentLengthMiddleware");var QwA={step:"build",tags:["SET_CONTENT_LENGTH","CONTENT_LENGTH"],name:"contentLengthMiddleware",override:!0},pEQ=AwA((A)=>({applyToStack:(B)=>{B.add(Be1(A.bodyLengthChecker),QwA)}}),"getContentLengthPlugin")});
var be1=E((eG5,YNA)=>{var{defineProperty:GE1,getOwnPropertyDescriptor:QLQ,getOwnPropertyNames:DLQ}=Object,ZLQ=Object.prototype.hasOwnProperty,GLQ=(A,B)=>GE1(A,"name",{value:B,configurable:!0}),FLQ=(A,B)=>{for(var Q in B)GE1(A,Q,{get:B[Q],enumerable:!0})},ILQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of DLQ(B))if(!ZLQ.call(A,Z)&&Z!==Q)GE1(A,Z,{get:()=>B[Z],enumerable:!(D=QLQ(B,Z))||D.enumerable})}return A},YLQ=(A)=>ILQ(GE1({},"__esModule",{value:!0}),A),BNA={};FLQ(BNA,{ENV_ACCOUNT_ID:()=>INA,ENV_CREDENTIAL_SCOPE:()=>FNA,ENV_EXPIRATION:()=>GNA,ENV_KEY:()=>QNA,ENV_SECRET:()=>DNA,ENV_SESSION:()=>ZNA,fromEnv:()=>XLQ});YNA.exports=YLQ(BNA);var WLQ=Zz(),JLQ=Q9(),QNA="AWS_ACCESS_KEY_ID",DNA="AWS_SECRET_ACCESS_KEY",ZNA="AWS_SESSION_TOKEN",GNA="AWS_CREDENTIAL_EXPIRATION",FNA="AWS_CREDENTIAL_SCOPE",INA="AWS_ACCOUNT_ID",XLQ=GLQ((A)=>async()=>{A?.logger?.debug("@aws-sdk/credential-provider-env - fromEnv");let B=process.env[QNA],Q=process.env[DNA],D=process.env[ZNA],Z=process.env[GNA],G=process.env[FNA],F=process.env[INA];if(B&&Q){let I={accessKeyId:B,secretAccessKey:Q,...D&&{sessionToken:D},...Z&&{expiration:new Date(Z)},...G&&{credentialScope:G},...F&&{accountId:F}};return WLQ.setCredentialFeature(I,"CREDENTIALS_ENV_VARS","g"),I}throw new JLQ.CredentialsProviderError("Unable to find environment variable credentials.",{logger:A?.logger})},"fromEnv")});
var c$A=E((m$A)=>{Object.defineProperty(m$A,"__esModule",{value:!0});m$A.default=void 0;var d$Q=c$Q(J1("crypto"));function c$Q(A){return A&&A.__esModule?A:{default:A}}var l$Q={randomUUID:d$Q.default.randomUUID};m$A.default=l$Q});
var cB=E((S75,HXA)=>{var{defineProperty:PH1,getOwnPropertyDescriptor:OGQ,getOwnPropertyNames:TGQ}=Object,PGQ=Object.prototype.hasOwnProperty,$o1=(A,B)=>PH1(A,"name",{value:B,configurable:!0}),SGQ=(A,B)=>{for(var Q in B)PH1(A,Q,{get:B[Q],enumerable:!0})},jGQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of TGQ(B))if(!PGQ.call(A,Z)&&Z!==Q)PH1(A,Z,{get:()=>B[Z],enumerable:!(D=OGQ(B,Z))||D.enumerable})}return A},yGQ=(A)=>jGQ(PH1({},"__esModule",{value:!0}),A),VXA={};SGQ(VXA,{fromUtf8:()=>KXA,toUint8Array:()=>kGQ,toUtf8:()=>_GQ});HXA.exports=yGQ(VXA);var CXA=YD(),KXA=$o1((A)=>{let B=CXA.fromString(A,"utf8");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength/Uint8Array.BYTES_PER_ELEMENT)},"fromUtf8"),kGQ=$o1((A)=>{if(typeof A==="string")return KXA(A);if(ArrayBuffer.isView(A))return new Uint8Array(A.buffer,A.byteOffset,A.byteLength/Uint8Array.BYTES_PER_ELEMENT);return new Uint8Array(A)},"toUint8Array"),_GQ=$o1((A)=>{if(typeof A==="string")return A;if(typeof A!=="object"||typeof A.byteOffset!=="number"||typeof A.byteLength!=="number")throw new Error("@smithy/util-utf8: toUtf8 encoder function only accepts string | Uint8Array.");return CXA.fromArrayBuffer(A.buffer,A.byteOffset,A.byteLength).toString("utf8")},"toUtf8")});
var cXA=E((mXA)=>{Object.defineProperty(mXA,"__esModule",{value:!0});mXA.createBufferedReadable=void 0;mXA.createBufferedReadableStream=hXA;mXA.merge=gXA;mXA.flush=jH1;mXA.sizeOf=ni;mXA.modeOf=uXA;var QFQ=Mo1();function hXA(A,B,Q){let D=A.getReader(),Z=!1,G=0,F=["",new QFQ.ByteArrayCollector((W)=>new Uint8Array(W))],I=-1,Y=async(W)=>{let{value:J,done:X}=await D.read(),V=J;if(X){if(I!==-1){let C=jH1(F,I);if(ni(C)>0)W.enqueue(C)}W.close()}else{let C=uXA(V,!1);if(I!==C){if(I>=0)W.enqueue(jH1(F,I));I=C}if(I===-1){W.enqueue(V);return}let K=ni(V);G+=K;let H=ni(F[I]);if(K>=B&&H===0)W.enqueue(V);else{let z=gXA(F,I,V);if(!Z&&G>B*2)Z=!0,Q===null||Q===void 0||Q.warn(`@smithy/util-stream - stream chunk size ${K} is below threshold of ${B}, automatically buffering.`);if(z>=B)W.enqueue(jH1(F,I));else await Y(W)}}};return new ReadableStream({pull:Y})}mXA.createBufferedReadable=hXA;function gXA(A,B,Q){switch(B){case 0:return A[0]+=Q,ni(A[0]);case 1:case 2:return A[B].push(Q),ni(A[B])}}function jH1(A,B){switch(B){case 0:let Q=A[0];return A[0]="",Q;case 1:case 2:return A[B].flush()}throw new Error(`@smithy/util-stream - invalid index ${B} given to flush()`)}function ni(A){var B,Q;return(Q=(B=A===null||A===void 0?void 0:A.byteLength)!==null&&B!==void 0?B:A===null||A===void 0?void 0:A.length)!==null&&Q!==void 0?Q:0}function uXA(A,B=!0){if(B&&typeof Buffer!=="undefined"&&A instanceof Buffer)return 2;if(A instanceof Uint8Array)return 1;if(typeof A==="string")return 0;return-1}});
var cwA=E((XG5,dwA)=>{var{defineProperty:iz1,getOwnPropertyDescriptor:AwQ,getOwnPropertyNames:BwQ}=Object,QwQ=Object.prototype.hasOwnProperty,DwQ=(A,B)=>iz1(A,"name",{value:B,configurable:!0}),ZwQ=(A,B)=>{for(var Q in B)iz1(A,Q,{get:B[Q],enumerable:!0})},GwQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of BwQ(B))if(!QwQ.call(A,Z)&&Z!==Q)iz1(A,Z,{get:()=>B[Z],enumerable:!(D=AwQ(B,Z))||D.enumerable})}return A},FwQ=(A)=>GwQ(iz1({},"__esModule",{value:!0}),A),uwA={};ZwQ(uwA,{parseQueryString:()=>mwA});dwA.exports=FwQ(uwA);function mwA(A){let B={};if(A=A.replace(/^\?/,""),A)for(let Q of A.split("&")){let[D,Z=null]=Q.split("=");if(D=decodeURIComponent(D),Z)Z=decodeURIComponent(Z);if(!(D in B))B[D]=Z;else if(Array.isArray(B[D]))B[D].push(Z);else B[D]=[B[D],Z]}return B}DwQ(mwA,"parseQueryString")});
var dMA=E((uMA)=>{Object.defineProperty(uMA,"__esModule",{value:!0});uMA.getRuntimeConfig=void 0;var aOQ=WI(),sOQ=CB(),rOQ=g4(),oOQ=JZ(),hMA=ty(),gMA=cB(),tOQ=D10(),eOQ=fMA(),ATQ=(A)=>{return{apiVersion:"2019-06-10",base64Decoder:A?.base64Decoder??hMA.fromBase64,base64Encoder:A?.base64Encoder??hMA.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??eOQ.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??tOQ.defaultSSOOIDCHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new aOQ.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new sOQ.NoAuthSigner}],logger:A?.logger??new rOQ.NoOpLogger,serviceId:A?.serviceId??"SSO OIDC",urlParser:A?.urlParser??oOQ.parseUrl,utf8Decoder:A?.utf8Decoder??gMA.fromUtf8,utf8Encoder:A?.utf8Encoder??gMA.toUtf8}};uMA.getRuntimeConfig=ATQ});
var e10=E((nF5,t10)=>{var{defineProperty:SE1,getOwnPropertyDescriptor:KSQ,getOwnPropertyNames:HSQ}=Object,zSQ=Object.prototype.hasOwnProperty,D9=(A,B)=>SE1(A,"name",{value:B,configurable:!0}),ESQ=(A,B)=>{for(var Q in B)SE1(A,Q,{get:B[Q],enumerable:!0})},p10=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of HSQ(B))if(!zSQ.call(A,Z)&&Z!==Q)SE1(A,Z,{get:()=>B[Z],enumerable:!(D=KSQ(B,Z))||D.enumerable})}return A},USQ=(A,B,Q)=>(p10(A,B,"default"),Q&&p10(Q,B,"default")),wSQ=(A)=>p10(SE1({},"__esModule",{value:!0}),A),n10={};ESQ(n10,{AssumeRoleCommand:()=>r10,AssumeRoleResponseFilterSensitiveLog:()=>aOA,AssumeRoleWithWebIdentityCommand:()=>o10,AssumeRoleWithWebIdentityRequestFilterSensitiveLog:()=>BTA,AssumeRoleWithWebIdentityResponseFilterSensitiveLog:()=>QTA,ClientInputEndpointParameters:()=>XjQ.ClientInputEndpointParameters,CredentialsFilterSensitiveLog:()=>a10,ExpiredTokenException:()=>sOA,IDPCommunicationErrorException:()=>DTA,IDPRejectedClaimException:()=>eOA,InvalidIdentityTokenException:()=>ATA,MalformedPolicyDocumentException:()=>rOA,PackedPolicyTooLargeException:()=>oOA,RegionDisabledException:()=>tOA,STS:()=>KTA,STSServiceException:()=>aO,decorateDefaultCredentialProvider:()=>KjQ,getDefaultRoleAssumer:()=>$TA,getDefaultRoleAssumerWithWebIdentity:()=>qTA});t10.exports=wSQ(n10);USQ(n10,hQ1(),t10.exports);var $SQ=g4(),qSQ=R6(),NSQ=j3(),LSQ=g4(),MSQ=gQ1(),nOA=g4(),RSQ=g4(),aO=class A extends RSQ.ServiceException{static{D9(this,"STSServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},a10=D9((A)=>({...A,...A.SecretAccessKey&&{SecretAccessKey:nOA.SENSITIVE_STRING}}),"CredentialsFilterSensitiveLog"),aOA=D9((A)=>({...A,...A.Credentials&&{Credentials:a10(A.Credentials)}}),"AssumeRoleResponseFilterSensitiveLog"),sOA=class A extends aO{static{D9(this,"ExpiredTokenException")}name="ExpiredTokenException";$fault="client";constructor(B){super({name:"ExpiredTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},rOA=class A extends aO{static{D9(this,"MalformedPolicyDocumentException")}name="MalformedPolicyDocumentException";$fault="client";constructor(B){super({name:"MalformedPolicyDocumentException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},oOA=class A extends aO{static{D9(this,"PackedPolicyTooLargeException")}name="PackedPolicyTooLargeException";$fault="client";constructor(B){super({name:"PackedPolicyTooLargeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},tOA=class A extends aO{static{D9(this,"RegionDisabledException")}name="RegionDisabledException";$fault="client";constructor(B){super({name:"RegionDisabledException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},eOA=class A extends aO{static{D9(this,"IDPRejectedClaimException")}name="IDPRejectedClaimException";$fault="client";constructor(B){super({name:"IDPRejectedClaimException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},ATA=class A extends aO{static{D9(this,"InvalidIdentityTokenException")}name="InvalidIdentityTokenException";$fault="client";constructor(B){super({name:"InvalidIdentityTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},BTA=D9((A)=>({...A,...A.WebIdentityToken&&{WebIdentityToken:nOA.SENSITIVE_STRING}}),"AssumeRoleWithWebIdentityRequestFilterSensitiveLog"),QTA=D9((A)=>({...A,...A.Credentials&&{Credentials:a10(A.Credentials)}}),"AssumeRoleWithWebIdentityResponseFilterSensitiveLog"),DTA=class A extends aO{static{D9(this,"IDPCommunicationErrorException")}name="IDPCommunicationErrorException";$fault="client";constructor(B){super({name:"IDPCommunicationErrorException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},s10=WI(),OSQ=sJ(),X5=g4(),TSQ=D9(async(A,B)=>{let Q=WTA,D;return D=CTA({...hSQ(A,B),[XTA]:QjQ,[VTA]:JTA}),YTA(B,Q,"/",void 0,D)},"se_AssumeRoleCommand"),PSQ=D9(async(A,B)=>{let Q=WTA,D;return D=CTA({...gSQ(A,B),[XTA]:DjQ,[VTA]:JTA}),YTA(B,Q,"/",void 0,D)},"se_AssumeRoleWithWebIdentityCommand"),SSQ=D9(async(A,B)=>{if(A.statusCode>=300)return ZTA(A,B);let Q=await s10.parseXmlBody(A.body,B),D={};return D=iSQ(Q.AssumeRoleResult,B),{$metadata:sO(A),...D}},"de_AssumeRoleCommand"),jSQ=D9(async(A,B)=>{if(A.statusCode>=300)return ZTA(A,B);let Q=await s10.parseXmlBody(A.body,B),D={};return D=nSQ(Q.AssumeRoleWithWebIdentityResult,B),{$metadata:sO(A),...D}},"de_AssumeRoleWithWebIdentityCommand"),ZTA=D9(async(A,B)=>{let Q={...A,body:await s10.parseXmlErrorBody(A.body,B)},D=ZjQ(A,Q.body);switch(D){case"ExpiredTokenException":case"com.amazonaws.sts#ExpiredTokenException":throw await ySQ(Q,B);case"MalformedPolicyDocument":case"com.amazonaws.sts#MalformedPolicyDocumentException":throw await vSQ(Q,B);case"PackedPolicyTooLarge":case"com.amazonaws.sts#PackedPolicyTooLargeException":throw await bSQ(Q,B);case"RegionDisabledException":case"com.amazonaws.sts#RegionDisabledException":throw await fSQ(Q,B);case"IDPCommunicationError":case"com.amazonaws.sts#IDPCommunicationErrorException":throw await kSQ(Q,B);case"IDPRejectedClaim":case"com.amazonaws.sts#IDPRejectedClaimException":throw await _SQ(Q,B);case"InvalidIdentityToken":case"com.amazonaws.sts#InvalidIdentityTokenException":throw await xSQ(Q,B);default:let Z=Q.body;return BjQ({output:A,parsedBody:Z.Error,errorCode:D})}},"de_CommandError"),ySQ=D9(async(A,B)=>{let Q=A.body,D=aSQ(Q.Error,B),Z=new sOA({$metadata:sO(A),...D});return X5.decorateServiceException(Z,Q)},"de_ExpiredTokenExceptionRes"),kSQ=D9(async(A,B)=>{let Q=A.body,D=sSQ(Q.Error,B),Z=new DTA({$metadata:sO(A),...D});return X5.decorateServiceException(Z,Q)},"de_IDPCommunicationErrorExceptionRes"),_SQ=D9(async(A,B)=>{let Q=A.body,D=rSQ(Q.Error,B),Z=new eOA({$metadata:sO(A),...D});return X5.decorateServiceException(Z,Q)},"de_IDPRejectedClaimExceptionRes"),xSQ=D9(async(A,B)=>{let Q=A.body,D=oSQ(Q.Error,B),Z=new ATA({$metadata:sO(A),...D});return X5.decorateServiceException(Z,Q)},"de_InvalidIdentityTokenExceptionRes"),vSQ=D9(async(A,B)=>{let Q=A.body,D=tSQ(Q.Error,B),Z=new rOA({$metadata:sO(A),...D});return X5.decorateServiceException(Z,Q)},"de_MalformedPolicyDocumentExceptionRes"),bSQ=D9(async(A,B)=>{let Q=A.body,D=eSQ(Q.Error,B),Z=new oOA({$metadata:sO(A),...D});return X5.decorateServiceException(Z,Q)},"de_PackedPolicyTooLargeExceptionRes"),fSQ=D9(async(A,B)=>{let Q=A.body,D=AjQ(Q.Error,B),Z=new tOA({$metadata:sO(A),...D});return X5.decorateServiceException(Z,Q)},"de_RegionDisabledExceptionRes"),hSQ=D9((A,B)=>{let Q={};if(A[Tn]!=null)Q[Tn]=A[Tn];if(A[Pn]!=null)Q[Pn]=A[Pn];if(A[Rn]!=null){let D=GTA(A[Rn],B);if(A[Rn]?.length===0)Q.PolicyArns=[];Object.entries(D).forEach(([Z,G])=>{let F=`PolicyArns.${Z}`;Q[F]=G})}if(A[Mn]!=null)Q[Mn]=A[Mn];if(A[Ln]!=null)Q[Ln]=A[Ln];if(A[g10]!=null){let D=pSQ(A[g10],B);if(A[g10]?.length===0)Q.Tags=[];Object.entries(D).forEach(([Z,G])=>{let F=`Tags.${Z}`;Q[F]=G})}if(A[m10]!=null){let D=lSQ(A[m10],B);if(A[m10]?.length===0)Q.TransitiveTagKeys=[];Object.entries(D).forEach(([Z,G])=>{let F=`TransitiveTagKeys.${Z}`;Q[F]=G})}if(A[S10]!=null)Q[S10]=A[S10];if(A[f10]!=null)Q[f10]=A[f10];if(A[u10]!=null)Q[u10]=A[u10];if(A[nO]!=null)Q[nO]=A[nO];if(A[k10]!=null){let D=dSQ(A[k10],B);if(A[k10]?.length===0)Q.ProvidedContexts=[];Object.entries(D).forEach(([Z,G])=>{let F=`ProvidedContexts.${Z}`;Q[F]=G})}return Q},"se_AssumeRoleRequest"),gSQ=D9((A,B)=>{let Q={};if(A[Tn]!=null)Q[Tn]=A[Tn];if(A[Pn]!=null)Q[Pn]=A[Pn];if(A[c10]!=null)Q[c10]=A[c10];if(A[_10]!=null)Q[_10]=A[_10];if(A[Rn]!=null){let D=GTA(A[Rn],B);if(A[Rn]?.length===0)Q.PolicyArns=[];Object.entries(D).forEach(([Z,G])=>{let F=`PolicyArns.${Z}`;Q[F]=G})}if(A[Mn]!=null)Q[Mn]=A[Mn];if(A[Ln]!=null)Q[Ln]=A[Ln];return Q},"se_AssumeRoleWithWebIdentityRequest"),GTA=D9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=uSQ(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_policyDescriptorListType"),uSQ=D9((A,B)=>{let Q={};if(A[l10]!=null)Q[l10]=A[l10];return Q},"se_PolicyDescriptorType"),mSQ=D9((A,B)=>{let Q={};if(A[y10]!=null)Q[y10]=A[y10];if(A[T10]!=null)Q[T10]=A[T10];return Q},"se_ProvidedContext"),dSQ=D9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=mSQ(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_ProvidedContextsListType"),cSQ=D9((A,B)=>{let Q={};if(A[j10]!=null)Q[j10]=A[j10];if(A[d10]!=null)Q[d10]=A[d10];return Q},"se_Tag"),lSQ=D9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;Q[`member.${D}`]=Z,D++}return Q},"se_tagKeyListType"),pSQ=D9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=cSQ(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_tagListType"),FTA=D9((A,B)=>{let Q={};if(A[M10]!=null)Q[M10]=X5.expectString(A[M10]);if(A[R10]!=null)Q[R10]=X5.expectString(A[R10]);return Q},"de_AssumedRoleUser"),iSQ=D9((A,B)=>{let Q={};if(A[Nn]!=null)Q[Nn]=ITA(A[Nn],B);if(A[qn]!=null)Q[qn]=FTA(A[qn],B);if(A[On]!=null)Q[On]=X5.strictParseInt32(A[On]);if(A[nO]!=null)Q[nO]=X5.expectString(A[nO]);return Q},"de_AssumeRoleResponse"),nSQ=D9((A,B)=>{let Q={};if(A[Nn]!=null)Q[Nn]=ITA(A[Nn],B);if(A[b10]!=null)Q[b10]=X5.expectString(A[b10]);if(A[qn]!=null)Q[qn]=FTA(A[qn],B);if(A[On]!=null)Q[On]=X5.strictParseInt32(A[On]);if(A[x10]!=null)Q[x10]=X5.expectString(A[x10]);if(A[O10]!=null)Q[O10]=X5.expectString(A[O10]);if(A[nO]!=null)Q[nO]=X5.expectString(A[nO]);return Q},"de_AssumeRoleWithWebIdentityResponse"),ITA=D9((A,B)=>{let Q={};if(A[L10]!=null)Q[L10]=X5.expectString(A[L10]);if(A[v10]!=null)Q[v10]=X5.expectString(A[v10]);if(A[h10]!=null)Q[h10]=X5.expectString(A[h10]);if(A[P10]!=null)Q[P10]=X5.expectNonNull(X5.parseRfc3339DateTimeWithOffset(A[P10]));return Q},"de_Credentials"),aSQ=D9((A,B)=>{let Q={};if(A[rZ]!=null)Q[rZ]=X5.expectString(A[rZ]);return Q},"de_ExpiredTokenException"),sSQ=D9((A,B)=>{let Q={};if(A[rZ]!=null)Q[rZ]=X5.expectString(A[rZ]);return Q},"de_IDPCommunicationErrorException"),rSQ=D9((A,B)=>{let Q={};if(A[rZ]!=null)Q[rZ]=X5.expectString(A[rZ]);return Q},"de_IDPRejectedClaimException"),oSQ=D9((A,B)=>{let Q={};if(A[rZ]!=null)Q[rZ]=X5.expectString(A[rZ]);return Q},"de_InvalidIdentityTokenException"),tSQ=D9((A,B)=>{let Q={};if(A[rZ]!=null)Q[rZ]=X5.expectString(A[rZ]);return Q},"de_MalformedPolicyDocumentException"),eSQ=D9((A,B)=>{let Q={};if(A[rZ]!=null)Q[rZ]=X5.expectString(A[rZ]);return Q},"de_PackedPolicyTooLargeException"),AjQ=D9((A,B)=>{let Q={};if(A[rZ]!=null)Q[rZ]=X5.expectString(A[rZ]);return Q},"de_RegionDisabledException"),sO=D9((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),BjQ=X5.withBaseException(aO),YTA=D9(async(A,B,Q,D,Z)=>{let{hostname:G,protocol:F="https",port:I,path:Y}=await A.endpoint(),W={protocol:F,hostname:G,port:I,method:"POST",path:Y.endsWith("/")?Y.slice(0,-1)+Q:Y+Q,headers:B};if(D!==void 0)W.hostname=D;if(Z!==void 0)W.body=Z;return new OSQ.HttpRequest(W)},"buildHttpRpcRequest"),WTA={"content-type":"application/x-www-form-urlencoded"},JTA="2011-06-15",XTA="Action",L10="AccessKeyId",QjQ="AssumeRole",M10="AssumedRoleId",qn="AssumedRoleUser",DjQ="AssumeRoleWithWebIdentity",R10="Arn",O10="Audience",Nn="Credentials",T10="ContextAssertion",Ln="DurationSeconds",P10="Expiration",S10="ExternalId",j10="Key",Mn="Policy",Rn="PolicyArns",y10="ProviderArn",k10="ProvidedContexts",_10="ProviderId",On="PackedPolicySize",x10="Provider",Tn="RoleArn",Pn="RoleSessionName",v10="SecretAccessKey",b10="SubjectFromWebIdentityToken",nO="SourceIdentity",f10="SerialNumber",h10="SessionToken",g10="Tags",u10="TokenCode",m10="TransitiveTagKeys",VTA="Version",d10="Value",c10="WebIdentityToken",l10="arn",rZ="message",CTA=D9((A)=>Object.entries(A).map(([B,Q])=>X5.extendedEncodeURIComponent(B)+"="+X5.extendedEncodeURIComponent(Q)).join("&"),"buildFormUrlencodedString"),ZjQ=D9((A,B)=>{if(B.Error?.Code!==void 0)return B.Error.Code;if(A.statusCode==404)return"NotFound"},"loadQueryErrorCode"),r10=class extends LSQ.Command.classBuilder().ep(MSQ.commonParams).m(function(A,B,Q,D){return[NSQ.getSerdePlugin(Q,this.serialize,this.deserialize),qSQ.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRole",{}).n("STSClient","AssumeRoleCommand").f(void 0,aOA).ser(TSQ).de(SSQ).build(){static{D9(this,"AssumeRoleCommand")}},GjQ=R6(),FjQ=j3(),IjQ=g4(),YjQ=gQ1(),o10=class extends IjQ.Command.classBuilder().ep(YjQ.commonParams).m(function(A,B,Q,D){return[FjQ.getSerdePlugin(Q,this.serialize,this.deserialize),GjQ.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRoleWithWebIdentity",{}).n("STSClient","AssumeRoleWithWebIdentityCommand").f(BTA,QTA).ser(PSQ).de(jSQ).build(){static{D9(this,"AssumeRoleWithWebIdentityCommand")}},WjQ=hQ1(),JjQ={AssumeRoleCommand:r10,AssumeRoleWithWebIdentityCommand:o10},KTA=class extends WjQ.STSClient{static{D9(this,"STS")}};$SQ.createAggregatedClient(JjQ,KTA);var XjQ=gQ1(),i10=Zz(),iOA="us-east-1",HTA=D9((A)=>{if(typeof A?.Arn==="string"){let B=A.Arn.split(":");if(B.length>4&&B[4]!=="")return B[4]}return},"getAccountIdFromAssumedRoleUser"),zTA=D9(async(A,B,Q)=>{let D=typeof A==="function"?await A():A,Z=typeof B==="function"?await B():B;return Q?.debug?.("@aws-sdk/client-sts::resolveRegion","accepting first of:",`${D} (provider)`,`${Z} (parent client)`,`${iOA} (STS default)`),D??Z??iOA},"resolveRegion"),VjQ=D9((A,B)=>{let Q,D;return async(Z,G)=>{if(D=Z,!Q){let{logger:J=A?.parentClientConfig?.logger,region:X,requestHandler:V=A?.parentClientConfig?.requestHandler,credentialProviderLogger:C}=A,K=await zTA(X,A?.parentClientConfig?.region,C),H=!ETA(V);Q=new B({profile:A?.parentClientConfig?.profile,credentialDefaultProvider:D9(()=>async()=>D,"credentialDefaultProvider"),region:K,requestHandler:H?V:void 0,logger:J})}let{Credentials:F,AssumedRoleUser:I}=await Q.send(new r10(G));if(!F||!F.AccessKeyId||!F.SecretAccessKey)throw new Error(`Invalid response from STS.assumeRole call with role ${G.RoleArn}`);let Y=HTA(I),W={accessKeyId:F.AccessKeyId,secretAccessKey:F.SecretAccessKey,sessionToken:F.SessionToken,expiration:F.Expiration,...F.CredentialScope&&{credentialScope:F.CredentialScope},...Y&&{accountId:Y}};return i10.setCredentialFeature(W,"CREDENTIALS_STS_ASSUME_ROLE","i"),W}},"getDefaultRoleAssumer"),CjQ=D9((A,B)=>{let Q;return async(D)=>{if(!Q){let{logger:Y=A?.parentClientConfig?.logger,region:W,requestHandler:J=A?.parentClientConfig?.requestHandler,credentialProviderLogger:X}=A,V=await zTA(W,A?.parentClientConfig?.region,X),C=!ETA(J);Q=new B({profile:A?.parentClientConfig?.profile,region:V,requestHandler:C?J:void 0,logger:Y})}let{Credentials:Z,AssumedRoleUser:G}=await Q.send(new o10(D));if(!Z||!Z.AccessKeyId||!Z.SecretAccessKey)throw new Error(`Invalid response from STS.assumeRoleWithWebIdentity call with role ${D.RoleArn}`);let F=HTA(G),I={accessKeyId:Z.AccessKeyId,secretAccessKey:Z.SecretAccessKey,sessionToken:Z.SessionToken,expiration:Z.Expiration,...Z.CredentialScope&&{credentialScope:Z.CredentialScope},...F&&{accountId:F}};if(F)i10.setCredentialFeature(I,"RESOLVED_ACCOUNT_ID","T");return i10.setCredentialFeature(I,"CREDENTIALS_STS_ASSUME_ROLE_WEB_ID","k"),I}},"getDefaultRoleAssumerWithWebIdentity"),ETA=D9((A)=>{return A?.metadata?.handlerProtocol==="h2"},"isH2"),UTA=hQ1(),wTA=D9((A,B)=>{if(!B)return A;else return class Q extends A{static{D9(this,"CustomizableSTSClient")}constructor(D){super(D);for(let Z of B)this.middlewareStack.use(Z)}}},"getCustomizableStsClientCtor"),$TA=D9((A={},B)=>VjQ(A,wTA(UTA.STSClient,B)),"getDefaultRoleAssumer"),qTA=D9((A={},B)=>CjQ(A,wTA(UTA.STSClient,B)),"getDefaultRoleAssumerWithWebIdentity"),KjQ=D9((A)=>(B)=>A({roleAssumer:$TA(B),roleAssumerWithWebIdentity:qTA(B),...B}),"decorateDefaultCredentialProvider")});
var e91=E((X75,BJA)=>{var{defineProperty:VH1,getOwnPropertyDescriptor:RDQ,getOwnPropertyNames:ODQ}=Object,TDQ=Object.prototype.hasOwnProperty,Ho1=(A,B)=>VH1(A,"name",{value:B,configurable:!0}),PDQ=(A,B)=>{for(var Q in B)VH1(A,Q,{get:B[Q],enumerable:!0})},SDQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of ODQ(B))if(!TDQ.call(A,Z)&&Z!==Q)VH1(A,Z,{get:()=>B[Z],enumerable:!(D=RDQ(B,Z))||D.enumerable})}return A},jDQ=(A)=>SDQ(VH1({},"__esModule",{value:!0}),A),tWA={};PDQ(tWA,{getLoggerPlugin:()=>yDQ,loggerMiddleware:()=>eWA,loggerMiddlewareOptions:()=>AJA});BJA.exports=jDQ(tWA);var eWA=Ho1(()=>(A,B)=>async(Q)=>{try{let D=await A(Q),{clientName:Z,commandName:G,logger:F,dynamoDbDocumentClientOptions:I={}}=B,{overrideInputFilterSensitiveLog:Y,overrideOutputFilterSensitiveLog:W}=I,J=Y??B.inputFilterSensitiveLog,X=W??B.outputFilterSensitiveLog,{$metadata:V,...C}=D.output;return F?.info?.({clientName:Z,commandName:G,input:J(Q.input),output:X(C),metadata:V}),D}catch(D){let{clientName:Z,commandName:G,logger:F,dynamoDbDocumentClientOptions:I={}}=B,{overrideInputFilterSensitiveLog:Y}=I,W=Y??B.inputFilterSensitiveLog;throw F?.error?.({clientName:Z,commandName:G,input:W(Q.input),error:D,metadata:D.$metadata}),D}},"loggerMiddleware"),AJA={name:"loggerMiddleware",tags:["LOGGER"],step:"initialize",override:!0},yDQ=Ho1((A)=>({applyToStack:Ho1((B)=>{B.add(eWA(),AJA)},"applyToStack")}),"getLoggerPlugin")});
var eHA=E((rD5,tHA)=>{var{defineProperty:Cz1,getOwnPropertyDescriptor:EVQ,getOwnPropertyNames:UVQ}=Object,wVQ=Object.prototype.hasOwnProperty,Ht1=(A,B)=>Cz1(A,"name",{value:B,configurable:!0}),$VQ=(A,B)=>{for(var Q in B)Cz1(A,Q,{get:B[Q],enumerable:!0})},qVQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of UVQ(B))if(!wVQ.call(A,Z)&&Z!==Q)Cz1(A,Z,{get:()=>B[Z],enumerable:!(D=EVQ(B,Z))||D.enumerable})}return A},NVQ=(A)=>qVQ(Cz1({},"__esModule",{value:!0}),A),rHA={};$VQ(rHA,{escapeUri:()=>oHA,escapeUriPath:()=>MVQ});tHA.exports=NVQ(rHA);var oHA=Ht1((A)=>encodeURIComponent(A).replace(/[!'()*]/g,LVQ),"escapeUri"),LVQ=Ht1((A)=>`%${A.charCodeAt(0).toString(16).toUpperCase()}`,"hexEncode"),MVQ=Ht1((A)=>A.split("/").map(oHA).join("/"),"escapeUriPath")});
var fMA=E((vMA)=>{Object.defineProperty(vMA,"__esModule",{value:!0});vMA.defaultEndpointResolver=void 0;var lOQ=Bn(),I10=S7(),pOQ=xMA(),iOQ=new I10.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),nOQ=(A,B={})=>{return iOQ.get(A,()=>I10.resolveEndpoint(pOQ.ruleSet,{endpointParams:A,logger:B.logger}))};vMA.defaultEndpointResolver=nOQ;I10.customEndpointFunctions.aws=lOQ.awsEndpointFunctions});
var fNA=E((IF5,TMQ)=>{TMQ.exports={name:"@aws-sdk/client-sso",description:"AWS SDK for JavaScript Sso Client for Node.js, Browser and React Native",version:"3.840.0",scripts:{build:"concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'","build:cjs":"node ../../scripts/compilation/inline client-sso","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4",clean:"rimraf ./dist-* && rimraf *.tsbuildinfo","extract:docs":"api-extractor run --local","generate:client":"node ../../scripts/generate-clients/single-service --solo sso"},main:"./dist-cjs/index.js",types:"./dist-types/index.d.ts",module:"./dist-es/index.js",sideEffects:!1,dependencies:{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.840.0","@aws-sdk/middleware-host-header":"3.840.0","@aws-sdk/middleware-logger":"3.840.0","@aws-sdk/middleware-recursion-detection":"3.840.0","@aws-sdk/middleware-user-agent":"3.840.0","@aws-sdk/region-config-resolver":"3.840.0","@aws-sdk/types":"3.840.0","@aws-sdk/util-endpoints":"3.840.0","@aws-sdk/util-user-agent-browser":"3.840.0","@aws-sdk/util-user-agent-node":"3.840.0","@smithy/config-resolver":"^4.1.4","@smithy/core":"^3.6.0","@smithy/fetch-http-handler":"^5.0.4","@smithy/hash-node":"^4.0.4","@smithy/invalid-dependency":"^4.0.4","@smithy/middleware-content-length":"^4.0.4","@smithy/middleware-endpoint":"^4.1.13","@smithy/middleware-retry":"^4.1.14","@smithy/middleware-serde":"^4.0.8","@smithy/middleware-stack":"^4.0.4","@smithy/node-config-provider":"^4.1.3","@smithy/node-http-handler":"^4.0.6","@smithy/protocol-http":"^5.1.2","@smithy/smithy-client":"^4.4.5","@smithy/types":"^4.3.1","@smithy/url-parser":"^4.0.4","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.21","@smithy/util-defaults-mode-node":"^4.0.21","@smithy/util-endpoints":"^3.0.6","@smithy/util-middleware":"^4.0.4","@smithy/util-retry":"^4.0.6","@smithy/util-utf8":"^4.0.0",tslib:"^2.6.2"},devDependencies:{"@tsconfig/node18":"18.2.4","@types/node":"^18.19.69",concurrently:"7.0.0","downlevel-dts":"0.10.1",rimraf:"3.0.2",typescript:"~5.8.3"},engines:{node:">=18.0.0"},typesVersions:{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},files:["dist-*/**"],author:{name:"AWS SDK for JavaScript Team",url:"https://aws.amazon.com/javascript/"},license:"Apache-2.0",browser:{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.browser"},"react-native":{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.native"},homepage:"https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-sso",repository:{type:"git",url:"https://github.com/aws/aws-sdk-js-v3.git",directory:"clients/client-sso"}}});
var fwA=E((vwA)=>{Object.defineProperty(vwA,"__esModule",{value:!0});vwA.getEndpointUrlConfig=void 0;var kwA=D3(),_wA="AWS_ENDPOINT_URL",xwA="endpoint_url",rUQ=(A)=>({environmentVariableSelector:(B)=>{let Q=A.split(" ").map((G)=>G.toUpperCase()),D=B[[_wA,...Q].join("_")];if(D)return D;let Z=B[_wA];if(Z)return Z;return},configFileSelector:(B,Q)=>{if(Q&&B.services){let Z=Q[["services",B.services].join(kwA.CONFIG_PREFIX_SEPARATOR)];if(Z){let G=A.split(" ").map((I)=>I.toLowerCase()),F=Z[[G.join("_"),xwA].join(kwA.CONFIG_PREFIX_SEPARATOR)];if(F)return F}}let D=B[xwA];if(D)return D;return},default:void 0});vwA.getEndpointUrlConfig=rUQ});
var g4=E((WZ5,ht1)=>{var{defineProperty:Rz1,getOwnPropertyDescriptor:vCQ,getOwnPropertyNames:bCQ}=Object,fCQ=Object.prototype.hasOwnProperty,H8=(A,B)=>Rz1(A,"name",{value:B,configurable:!0}),hCQ=(A,B)=>{for(var Q in B)Rz1(A,Q,{get:B[Q],enumerable:!0})},_t1=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of bCQ(B))if(!fCQ.call(A,Z)&&Z!==Q)Rz1(A,Z,{get:()=>B[Z],enumerable:!(D=vCQ(B,Z))||D.enumerable})}return A},gCQ=(A,B,Q)=>(_t1(A,B,"default"),Q&&_t1(Q,B,"default")),uCQ=(A)=>_t1(Rz1({},"__esModule",{value:!0}),A),bt1={};hCQ(bt1,{Client:()=>mCQ,Command:()=>ZEA,NoOpLogger:()=>ZKQ,SENSITIVE_STRING:()=>cCQ,ServiceException:()=>pCQ,_json:()=>vt1,collectBody:()=>kt1.collectBody,convertMap:()=>GKQ,createAggregatedClient:()=>lCQ,decorateServiceException:()=>GEA,emitWarningIfUnsupportedVersion:()=>sCQ,extendedEncodeURIComponent:()=>kt1.extendedEncodeURIComponent,getArrayIfSingleItem:()=>QKQ,getDefaultClientConfiguration:()=>AKQ,getDefaultExtensionConfiguration:()=>IEA,getValueFromTextNode:()=>YEA,isSerializableHeaderValue:()=>DKQ,loadConfigsForDefaultMode:()=>aCQ,map:()=>ft1,resolveDefaultRuntimeConfig:()=>BKQ,resolvedPath:()=>kt1.resolvedPath,serializeDateTime:()=>XKQ,serializeFloat:()=>JKQ,take:()=>FKQ,throwDefaultError:()=>FEA,withBaseException:()=>iCQ});ht1.exports=uCQ(bt1);var DEA=Mw(),mCQ=class{constructor(A){this.config=A,this.middlewareStack=DEA.constructStack()}static{H8(this,"Client")}send(A,B,Q){let D=typeof B!=="function"?B:void 0,Z=typeof B==="function"?B:Q,G=D===void 0&&this.config.cacheMiddleware===!0,F;if(G){if(!this.handlers)this.handlers=new WeakMap;let I=this.handlers;if(I.has(A.constructor))F=I.get(A.constructor);else F=A.resolveMiddleware(this.middlewareStack,this.config,D),I.set(A.constructor,F)}else delete this.handlers,F=A.resolveMiddleware(this.middlewareStack,this.config,D);if(Z)F(A).then((I)=>Z(null,I.output),(I)=>Z(I)).catch(()=>{});else return F(A).then((I)=>I.output)}destroy(){this.config?.requestHandler?.destroy?.(),delete this.handlers}},kt1=M6(),xt1=Ko1(),ZEA=class{constructor(){this.middlewareStack=DEA.constructStack()}static{H8(this,"Command")}static classBuilder(){return new dCQ}resolveMiddlewareWithContext(A,B,Q,{middlewareFn:D,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,smithyContext:Y,additionalContext:W,CommandCtor:J}){for(let H of D.bind(this)(J,A,B,Q))this.middlewareStack.use(H);let X=A.concat(this.middlewareStack),{logger:V}=B,C={logger:V,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,[xt1.SMITHY_CONTEXT_KEY]:{commandInstance:this,...Y},...W},{requestHandler:K}=B;return X.resolve((H)=>K.handle(H.request,Q||{}),C)}},dCQ=class{constructor(){this._init=()=>{},this._ep={},this._middlewareFn=()=>[],this._commandName="",this._clientName="",this._additionalContext={},this._smithyContext={},this._inputFilterSensitiveLog=(A)=>A,this._outputFilterSensitiveLog=(A)=>A,this._serializer=null,this._deserializer=null}static{H8(this,"ClassBuilder")}init(A){this._init=A}ep(A){return this._ep=A,this}m(A){return this._middlewareFn=A,this}s(A,B,Q={}){return this._smithyContext={service:A,operation:B,...Q},this}c(A={}){return this._additionalContext=A,this}n(A,B){return this._clientName=A,this._commandName=B,this}f(A=(Q)=>Q,B=(Q)=>Q){return this._inputFilterSensitiveLog=A,this._outputFilterSensitiveLog=B,this}ser(A){return this._serializer=A,this}de(A){return this._deserializer=A,this}sc(A){return this._operationSchema=A,this._smithyContext.operationSchema=A,this}build(){let A=this,B;return B=class extends ZEA{constructor(...[Q]){super();this.serialize=A._serializer,this.deserialize=A._deserializer,this.input=Q??{},A._init(this),this.schema=A._operationSchema}static{H8(this,"CommandRef")}static getEndpointParameterInstructions(){return A._ep}resolveMiddleware(Q,D,Z){return this.resolveMiddlewareWithContext(Q,D,Z,{CommandCtor:B,middlewareFn:A._middlewareFn,clientName:A._clientName,commandName:A._commandName,inputFilterSensitiveLog:A._inputFilterSensitiveLog,outputFilterSensitiveLog:A._outputFilterSensitiveLog,smithyContext:A._smithyContext,additionalContext:A._additionalContext})}}}},cCQ="***SensitiveInformation***",lCQ=H8((A,B)=>{for(let Q of Object.keys(A)){let D=A[Q],Z=H8(async function(F,I,Y){let W=new D(F);if(typeof I==="function")this.send(W,I);else if(typeof Y==="function"){if(typeof I!=="object")throw new Error(`Expected http options but got ${typeof I}`);this.send(W,I||{},Y)}else return this.send(W,I)},"methodImpl"),G=(Q[0].toLowerCase()+Q.slice(1)).replace(/Command$/,"");B.prototype[G]=Z}},"createAggregatedClient"),pCQ=class A extends Error{static{H8(this,"ServiceException")}constructor(B){super(B.message);Object.setPrototypeOf(this,Object.getPrototypeOf(this).constructor.prototype),this.name=B.name,this.$fault=B.$fault,this.$metadata=B.$metadata}static isInstance(B){if(!B)return!1;let Q=B;return A.prototype.isPrototypeOf(Q)||Boolean(Q.$fault)&&Boolean(Q.$metadata)&&(Q.$fault==="client"||Q.$fault==="server")}static[Symbol.hasInstance](B){if(!B)return!1;let Q=B;if(this===A)return A.isInstance(B);if(A.isInstance(B)){if(Q.name&&this.name)return this.prototype.isPrototypeOf(B)||Q.name===this.name;return this.prototype.isPrototypeOf(B)}return!1}},GEA=H8((A,B={})=>{Object.entries(B).filter(([,D])=>D!==void 0).forEach(([D,Z])=>{if(A[D]==null||A[D]==="")A[D]=Z});let Q=A.message||A.Message||"UnknownError";return A.message=Q,delete A.Message,A},"decorateServiceException"),FEA=H8(({output:A,parsedBody:B,exceptionCtor:Q,errorCode:D})=>{let Z=nCQ(A),G=Z.httpStatusCode?Z.httpStatusCode+"":void 0,F=new Q({name:B?.code||B?.Code||D||G||"UnknownError",$fault:"client",$metadata:Z});throw GEA(F,B)},"throwDefaultError"),iCQ=H8((A)=>{return({output:B,parsedBody:Q,errorCode:D})=>{FEA({output:B,parsedBody:Q,exceptionCtor:A,errorCode:D})}},"withBaseException"),nCQ=H8((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),aCQ=H8((A)=>{switch(A){case"standard":return{retryMode:"standard",connectionTimeout:3100};case"in-region":return{retryMode:"standard",connectionTimeout:1100};case"cross-region":return{retryMode:"standard",connectionTimeout:3100};case"mobile":return{retryMode:"standard",connectionTimeout:30000};default:return{}}},"loadConfigsForDefaultMode"),QEA=!1,sCQ=H8((A)=>{if(A&&!QEA&&parseInt(A.substring(1,A.indexOf(".")))<16)QEA=!0},"emitWarningIfUnsupportedVersion"),rCQ=H8((A)=>{let B=[];for(let Q in xt1.AlgorithmId){let D=xt1.AlgorithmId[Q];if(A[D]===void 0)continue;B.push({algorithmId:()=>D,checksumConstructor:()=>A[D]})}return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),oCQ=H8((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),tCQ=H8((A)=>{return{setRetryStrategy(B){A.retryStrategy=B},retryStrategy(){return A.retryStrategy}}},"getRetryConfiguration"),eCQ=H8((A)=>{let B={};return B.retryStrategy=A.retryStrategy(),B},"resolveRetryRuntimeConfig"),IEA=H8((A)=>{return Object.assign(rCQ(A),tCQ(A))},"getDefaultExtensionConfiguration"),AKQ=IEA,BKQ=H8((A)=>{return Object.assign(oCQ(A),eCQ(A))},"resolveDefaultRuntimeConfig"),QKQ=H8((A)=>Array.isArray(A)?A:[A],"getArrayIfSingleItem"),YEA=H8((A)=>{for(let Q in A)if(A.hasOwnProperty(Q)&&A[Q]["#text"]!==void 0)A[Q]=A[Q]["#text"];else if(typeof A[Q]==="object"&&A[Q]!==null)A[Q]=YEA(A[Q]);return A},"getValueFromTextNode"),DKQ=H8((A)=>{return A!=null},"isSerializableHeaderValue"),ZKQ=class{static{H8(this,"NoOpLogger")}trace(){}debug(){}info(){}warn(){}error(){}};function ft1(A,B,Q){let D,Z,G;if(typeof B==="undefined"&&typeof Q==="undefined")D={},G=A;else if(D=A,typeof B==="function")return Z=B,G=Q,IKQ(D,Z,G);else G=B;for(let F of Object.keys(G)){if(!Array.isArray(G[F])){D[F]=G[F];continue}WEA(D,null,G,F)}return D}H8(ft1,"map");var GKQ=H8((A)=>{let B={};for(let[Q,D]of Object.entries(A||{}))B[Q]=[,D];return B},"convertMap"),FKQ=H8((A,B)=>{let Q={};for(let D in B)WEA(Q,A,B,D);return Q},"take"),IKQ=H8((A,B,Q)=>{return ft1(A,Object.entries(Q).reduce((D,[Z,G])=>{if(Array.isArray(G))D[Z]=G;else if(typeof G==="function")D[Z]=[B,G()];else D[Z]=[B,G];return D},{}))},"mapWithFilter"),WEA=H8((A,B,Q,D)=>{if(B!==null){let F=Q[D];if(typeof F==="function")F=[,F];let[I=YKQ,Y=WKQ,W=D]=F;if(typeof I==="function"&&I(B[W])||typeof I!=="function"&&!!I)A[D]=Y(B[W]);return}let[Z,G]=Q[D];if(typeof G==="function"){let F,I=Z===void 0&&(F=G())!=null,Y=typeof Z==="function"&&!!Z(void 0)||typeof Z!=="function"&&!!Z;if(I)A[D]=F;else if(Y)A[D]=G()}else{let F=Z===void 0&&G!=null,I=typeof Z==="function"&&!!Z(G)||typeof Z!=="function"&&!!Z;if(F||I)A[D]=G}},"applyInstruction"),YKQ=H8((A)=>A!=null,"nonNullish"),WKQ=H8((A)=>A,"pass"),JKQ=H8((A)=>{if(A!==A)return"NaN";switch(A){case 1/0:return"Infinity";case-1/0:return"-Infinity";default:return A}},"serializeFloat"),XKQ=H8((A)=>A.toISOString().replace(".000Z","Z"),"serializeDateTime"),vt1=H8((A)=>{if(A==null)return{};if(Array.isArray(A))return A.filter((B)=>B!=null).map(vt1);if(typeof A==="object"){let B={};for(let Q of Object.keys(A)){if(A[Q]==null)continue;B[Q]=vt1(A[Q])}return B}return A},"_json");gCQ(bt1,X6(),ht1.exports)});
var gCA=E((hCA)=>{Object.defineProperty(hCA,"__esModule",{value:!0});hCA.splitStream=fYQ;var bCA=J1("stream"),bYQ=vCA(),fCA=py();async function fYQ(A){if(fCA.isReadableStream(A)||fCA.isBlob(A))return bYQ.splitStream(A);let B=new bCA.PassThrough,Q=new bCA.PassThrough;return A.pipe(B),A.pipe(Q),[B,Q]}});
var gG=E((WF5,aNA)=>{var{defineProperty:VE1,getOwnPropertyDescriptor:gMQ,getOwnPropertyNames:uMQ}=Object,mMQ=Object.prototype.hasOwnProperty,iNA=(A,B)=>VE1(A,"name",{value:B,configurable:!0}),dMQ=(A,B)=>{for(var Q in B)VE1(A,Q,{get:B[Q],enumerable:!0})},cMQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of uMQ(B))if(!mMQ.call(A,Z)&&Z!==Q)VE1(A,Z,{get:()=>B[Z],enumerable:!(D=gMQ(B,Z))||D.enumerable})}return A},lMQ=(A)=>cMQ(VE1({},"__esModule",{value:!0}),A),nNA={};dMQ(nNA,{Hash:()=>nMQ});aNA.exports=lMQ(nNA);var ae1=YD(),pMQ=cB(),iMQ=J1("buffer"),pNA=J1("crypto"),nMQ=class{static{iNA(this,"Hash")}constructor(A,B){this.algorithmIdentifier=A,this.secret=B,this.reset()}update(A,B){this.hash.update(pMQ.toUint8Array(se1(A,B)))}digest(){return Promise.resolve(this.hash.digest())}reset(){this.hash=this.secret?pNA.createHmac(this.algorithmIdentifier,se1(this.secret)):pNA.createHash(this.algorithmIdentifier)}};function se1(A,B){if(iMQ.Buffer.isBuffer(A))return A;if(typeof A==="string")return ae1.fromString(A,B);if(ArrayBuffer.isView(A))return ae1.fromArrayBuffer(A.buffer,A.byteOffset,A.byteLength);return ae1.fromArrayBuffer(A)}iNA(se1,"castSourceData")});
var gQ1=E((cRA)=>{Object.defineProperty(cRA,"__esModule",{value:!0});cRA.commonParams=cRA.resolveClientEndpointParameters=void 0;var yPQ=(A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,useGlobalEndpoint:A.useGlobalEndpoint??!1,defaultSigningName:"sts"})};cRA.resolveClientEndpointParameters=yPQ;cRA.commonParams={UseGlobalEndpoint:{type:"builtInParams",name:"useGlobalEndpoint"},UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}}});
var hEA=E((MZ5,fEA)=>{function NHQ(A,B){let Q="";if(B.format&&B.indentBy.length>0)Q=`
`;return vEA(A,B,"",Q)}function vEA(A,B,Q,D){let Z="",G=!1;for(let F=0;F<A.length;F++){let I=A[F],Y=LHQ(I);if(Y===void 0)continue;let W="";if(Q.length===0)W=Y;else W=`${Q}.${Y}`;if(Y===B.textNodeName){let K=I[Y];if(!MHQ(W,B))K=B.tagValueProcessor(Y,K),K=bEA(K,B);if(G)Z+=D;Z+=K,G=!1;continue}else if(Y===B.cdataPropName){if(G)Z+=D;Z+=`<![CDATA[${I[Y][0][B.textNodeName]}]]>`,G=!1;continue}else if(Y===B.commentPropName){Z+=D+`<!--${I[Y][0][B.textNodeName]}-->`,G=!0;continue}else if(Y[0]==="?"){let K=xEA(I[":@"],B),H=Y==="?xml"?"":D,z=I[Y][0][B.textNodeName];z=z.length!==0?" "+z:"",Z+=H+`<${Y}${z}${K}?>`,G=!0;continue}let J=D;if(J!=="")J+=B.indentBy;let X=xEA(I[":@"],B),V=D+`<${Y}${X}`,C=vEA(I[Y],B,W,J);if(B.unpairedTags.indexOf(Y)!==-1)if(B.suppressUnpairedNode)Z+=V+">";else Z+=V+"/>";else if((!C||C.length===0)&&B.suppressEmptyNode)Z+=V+"/>";else if(C&&C.endsWith(">"))Z+=V+`>${C}${D}</${Y}>`;else{if(Z+=V+">",C&&D!==""&&(C.includes("/>")||C.includes("</")))Z+=D+B.indentBy+C+D;else Z+=C;Z+=`</${Y}>`}G=!0}return Z}function LHQ(A){let B=Object.keys(A);for(let Q=0;Q<B.length;Q++){let D=B[Q];if(!A.hasOwnProperty(D))continue;if(D!==":@")return D}}function xEA(A,B){let Q="";if(A&&!B.ignoreAttributes)for(let D in A){if(!A.hasOwnProperty(D))continue;let Z=B.attributeValueProcessor(D,A[D]);if(Z=bEA(Z,B),Z===!0&&B.suppressBooleanAttributes)Q+=` ${D.substr(B.attributeNamePrefix.length)}`;else Q+=` ${D.substr(B.attributeNamePrefix.length)}="${Z}"`}return Q}function MHQ(A,B){A=A.substr(0,A.length-B.textNodeName.length-1);let Q=A.substr(A.lastIndexOf(".")+1);for(let D in B.stopNodes)if(B.stopNodes[D]===A||B.stopNodes[D]==="*."+Q)return!0;return!1}function bEA(A,B){if(A&&A.length>0&&B.processEntities)for(let Q=0;Q<B.entities.length;Q++){let D=B.entities[Q];A=A.replace(D.regex,D.val)}return A}fEA.exports=NHQ});
var hJA=E((z75,fJA)=>{var{defineProperty:$H1,getOwnPropertyDescriptor:UZQ,getOwnPropertyNames:wZQ}=Object,$ZQ=Object.prototype.hasOwnProperty,qH1=(A,B)=>$H1(A,"name",{value:B,configurable:!0}),qZQ=(A,B)=>{for(var Q in B)$H1(A,Q,{get:B[Q],enumerable:!0})},NZQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of wZQ(B))if(!$ZQ.call(A,Z)&&Z!==Q)$H1(A,Z,{get:()=>B[Z],enumerable:!(D=UZQ(B,Z))||D.enumerable})}return A},LZQ=(A)=>NZQ($H1({},"__esModule",{value:!0}),A),SJA={};qZQ(SJA,{AlgorithmId:()=>_JA,EndpointURLScheme:()=>kJA,FieldPosition:()=>xJA,HttpApiKeyAuthLocation:()=>yJA,HttpAuthLocation:()=>jJA,IniSectionType:()=>vJA,RequestHandlerProtocol:()=>bJA,SMITHY_CONTEXT_KEY:()=>PZQ,getDefaultClientConfiguration:()=>OZQ,resolveDefaultRuntimeConfig:()=>TZQ});fJA.exports=LZQ(SJA);var jJA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(jJA||{}),yJA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(yJA||{}),kJA=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(kJA||{}),_JA=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(_JA||{}),MZQ=qH1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),RZQ=qH1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),OZQ=qH1((A)=>{return MZQ(A)},"getDefaultClientConfiguration"),TZQ=qH1((A)=>{return RZQ(A)},"resolveDefaultRuntimeConfig"),xJA=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(xJA||{}),PZQ="__smithy_context",vJA=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(vJA||{}),bJA=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(bJA||{})});
var hOA=E((bOA)=>{Object.defineProperty(bOA,"__esModule",{value:!0});bOA.resolveRuntimeExtensions=void 0;var kOA=yQ1(),_OA=sJ(),xOA=g4(),vOA=yOA(),GSQ=(A,B)=>{let Q=Object.assign(kOA.getAwsRegionExtensionConfiguration(A),xOA.getDefaultExtensionConfiguration(A),_OA.getHttpHandlerExtensionConfiguration(A),vOA.getHttpAuthExtensionConfiguration(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,kOA.resolveAwsRegionExtensionConfiguration(Q),xOA.resolveDefaultRuntimeConfig(Q),_OA.resolveHttpHandlerRuntimeConfig(Q),vOA.resolveHttpAuthRuntimeConfig(Q))};bOA.resolveRuntimeExtensions=GSQ});
var hQ1=E((N10)=>{Object.defineProperty(N10,"__esModule",{value:!0});N10.STSClient=N10.__Client=void 0;var gOA=t91(),FSQ=e91(),ISQ=AQ1(),uOA=In(),YSQ=K4(),q10=CB(),WSQ=bG(),JSQ=R6(),mOA=u4(),cOA=g4();Object.defineProperty(N10,"__Client",{enumerable:!0,get:function(){return cOA.Client}});var dOA=H10(),XSQ=gQ1(),VSQ=POA(),CSQ=hOA();class lOA extends cOA.Client{config;constructor(...[A]){let B=VSQ.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=XSQ.resolveClientEndpointParameters(B),D=uOA.resolveUserAgentConfig(Q),Z=mOA.resolveRetryConfig(D),G=YSQ.resolveRegionConfig(Z),F=gOA.resolveHostHeaderConfig(G),I=JSQ.resolveEndpointConfig(F),Y=dOA.resolveHttpAuthSchemeConfig(I),W=CSQ.resolveRuntimeExtensions(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(uOA.getUserAgentPlugin(this.config)),this.middlewareStack.use(mOA.getRetryPlugin(this.config)),this.middlewareStack.use(WSQ.getContentLengthPlugin(this.config)),this.middlewareStack.use(gOA.getHostHeaderPlugin(this.config)),this.middlewareStack.use(FSQ.getLoggerPlugin(this.config)),this.middlewareStack.use(ISQ.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(q10.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:dOA.defaultSTSHttpAuthSchemeParametersProvider,identityProviderConfigProvider:async(J)=>new q10.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials})})),this.middlewareStack.use(q10.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}}N10.STSClient=lOA});
var hTA=E((BI5,fTA)=>{var{create:djQ,defineProperty:mQ1,getOwnPropertyDescriptor:cjQ,getOwnPropertyNames:ljQ,getPrototypeOf:pjQ}=Object,ijQ=Object.prototype.hasOwnProperty,dG=(A,B)=>mQ1(A,"name",{value:B,configurable:!0}),njQ=(A,B)=>{for(var Q in B)mQ1(A,Q,{get:B[Q],enumerable:!0})},xTA=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of ljQ(B))if(!ijQ.call(A,Z)&&Z!==Q)mQ1(A,Z,{get:()=>B[Z],enumerable:!(D=cjQ(B,Z))||D.enumerable})}return A},Wk=(A,B,Q)=>(Q=A!=null?djQ(pjQ(A)):{},xTA(B||!A||!A.__esModule?mQ1(Q,"default",{value:A,enumerable:!0}):Q,A)),ajQ=(A)=>xTA(mQ1({},"__esModule",{value:!0}),A),vTA={};njQ(vTA,{fromIni:()=>FyQ});fTA.exports=ajQ(vTA);var Y00=D3(),Jk=Zz(),uQ1=Q9(),sjQ=dG((A,B,Q)=>{let D={EcsContainer:dG(async(Z)=>{let{fromHttp:G}=await Promise.resolve().then(()=>Wk(le1())),{fromContainerMetadata:F}=await Promise.resolve().then(()=>Wk(TF()));return Q?.debug("@aws-sdk/credential-provider-ini - credential_source is EcsContainer"),async()=>uQ1.chain(G(Z??{}),F(Z))().then(I00)},"EcsContainer"),Ec2InstanceMetadata:dG(async(Z)=>{Q?.debug("@aws-sdk/credential-provider-ini - credential_source is Ec2InstanceMetadata");let{fromInstanceMetadata:G}=await Promise.resolve().then(()=>Wk(TF()));return async()=>G(Z)().then(I00)},"Ec2InstanceMetadata"),Environment:dG(async(Z)=>{Q?.debug("@aws-sdk/credential-provider-ini - credential_source is Environment");let{fromEnv:G}=await Promise.resolve().then(()=>Wk(be1()));return async()=>G(Z)().then(I00)},"Environment")};if(A in D)return D[A];else throw new uQ1.CredentialsProviderError(`Unsupported credential source in profile ${B}. Got ${A}, expected EcsContainer or Ec2InstanceMetadata or Environment.`,{logger:Q})},"resolveCredentialSource"),I00=dG((A)=>Jk.setCredentialFeature(A,"CREDENTIALS_PROFILE_NAMED_PROVIDER","p"),"setNamedProvider"),rjQ=dG((A,{profile:B="default",logger:Q}={})=>{return Boolean(A)&&typeof A==="object"&&typeof A.role_arn==="string"&&["undefined","string"].indexOf(typeof A.role_session_name)>-1&&["undefined","string"].indexOf(typeof A.external_id)>-1&&["undefined","string"].indexOf(typeof A.mfa_serial)>-1&&(ojQ(A,{profile:B,logger:Q})||tjQ(A,{profile:B,logger:Q}))},"isAssumeRoleProfile"),ojQ=dG((A,{profile:B,logger:Q})=>{let D=typeof A.source_profile==="string"&&typeof A.credential_source==="undefined";if(D)Q?.debug?.(`    ${B} isAssumeRoleWithSourceProfile source_profile=${A.source_profile}`);return D},"isAssumeRoleWithSourceProfile"),tjQ=dG((A,{profile:B,logger:Q})=>{let D=typeof A.credential_source==="string"&&typeof A.source_profile==="undefined";if(D)Q?.debug?.(`    ${B} isCredentialSourceProfile credential_source=${A.credential_source}`);return D},"isCredentialSourceProfile"),ejQ=dG(async(A,B,Q,D={})=>{Q.logger?.debug("@aws-sdk/credential-provider-ini - resolveAssumeRoleCredentials (STS)");let Z=B[A],{source_profile:G,region:F}=Z;if(!Q.roleAssumer){let{getDefaultRoleAssumer:Y}=await Promise.resolve().then(()=>Wk(e10()));Q.roleAssumer=Y({...Q.clientConfig,credentialProviderLogger:Q.logger,parentClientConfig:{...Q?.parentClientConfig,region:F??Q?.parentClientConfig?.region}},Q.clientPlugins)}if(G&&G in D)throw new uQ1.CredentialsProviderError(`Detected a cycle attempting to resolve credentials for profile ${Y00.getProfileName(Q)}. Profiles visited: `+Object.keys(D).join(", "),{logger:Q.logger});Q.logger?.debug(`@aws-sdk/credential-provider-ini - finding credential resolver using ${G?`source_profile=[${G}]`:`profile=[${A}]`}`);let I=G?bTA(G,B,Q,{...D,[G]:!0},yTA(B[G]??{})):(await sjQ(Z.credential_source,A,Q.logger)(Q))();if(yTA(Z))return I.then((Y)=>Jk.setCredentialFeature(Y,"CREDENTIALS_PROFILE_SOURCE_PROFILE","o"));else{let Y={RoleArn:Z.role_arn,RoleSessionName:Z.role_session_name||`aws-sdk-js-${Date.now()}`,ExternalId:Z.external_id,DurationSeconds:parseInt(Z.duration_seconds||"3600",10)},{mfa_serial:W}=Z;if(W){if(!Q.mfaCodeProvider)throw new uQ1.CredentialsProviderError(`Profile ${A} requires multi-factor authentication, but no MFA code callback was provided.`,{logger:Q.logger,tryNextLink:!1});Y.SerialNumber=W,Y.TokenCode=await Q.mfaCodeProvider(W)}let J=await I;return Q.roleAssumer(J,Y).then((X)=>Jk.setCredentialFeature(X,"CREDENTIALS_PROFILE_SOURCE_PROFILE","o"))}},"resolveAssumeRoleCredentials"),yTA=dG((A)=>{return!A.role_arn&&!!A.credential_source},"isCredentialSourceWithoutRoleArn"),AyQ=dG((A)=>Boolean(A)&&typeof A==="object"&&typeof A.credential_process==="string","isProcessProfile"),ByQ=dG(async(A,B)=>Promise.resolve().then(()=>Wk(Q00())).then(({fromProcess:Q})=>Q({...A,profile:B})().then((D)=>Jk.setCredentialFeature(D,"CREDENTIALS_PROFILE_PROCESS","v"))),"resolveProcessCredentials"),QyQ=dG(async(A,B,Q={})=>{let{fromSSO:D}=await Promise.resolve().then(()=>Wk(C10()));return D({profile:A,logger:Q.logger,parentClientConfig:Q.parentClientConfig,clientConfig:Q.clientConfig})().then((Z)=>{if(B.sso_session)return Jk.setCredentialFeature(Z,"CREDENTIALS_PROFILE_SSO","r");else return Jk.setCredentialFeature(Z,"CREDENTIALS_PROFILE_SSO_LEGACY","t")})},"resolveSsoCredentials"),DyQ=dG((A)=>A&&(typeof A.sso_start_url==="string"||typeof A.sso_account_id==="string"||typeof A.sso_session==="string"||typeof A.sso_region==="string"||typeof A.sso_role_name==="string"),"isSsoProfile"),kTA=dG((A)=>Boolean(A)&&typeof A==="object"&&typeof A.aws_access_key_id==="string"&&typeof A.aws_secret_access_key==="string"&&["undefined","string"].indexOf(typeof A.aws_session_token)>-1&&["undefined","string"].indexOf(typeof A.aws_account_id)>-1,"isStaticCredsProfile"),_TA=dG(async(A,B)=>{B?.logger?.debug("@aws-sdk/credential-provider-ini - resolveStaticCredentials");let Q={accessKeyId:A.aws_access_key_id,secretAccessKey:A.aws_secret_access_key,sessionToken:A.aws_session_token,...A.aws_credential_scope&&{credentialScope:A.aws_credential_scope},...A.aws_account_id&&{accountId:A.aws_account_id}};return Jk.setCredentialFeature(Q,"CREDENTIALS_PROFILE","n")},"resolveStaticCredentials"),ZyQ=dG((A)=>Boolean(A)&&typeof A==="object"&&typeof A.web_identity_token_file==="string"&&typeof A.role_arn==="string"&&["undefined","string"].indexOf(typeof A.role_session_name)>-1,"isWebIdentityProfile"),GyQ=dG(async(A,B)=>Promise.resolve().then(()=>Wk(F00())).then(({fromTokenFile:Q})=>Q({webIdentityTokenFile:A.web_identity_token_file,roleArn:A.role_arn,roleSessionName:A.role_session_name,roleAssumerWithWebIdentity:B.roleAssumerWithWebIdentity,logger:B.logger,parentClientConfig:B.parentClientConfig})().then((D)=>Jk.setCredentialFeature(D,"CREDENTIALS_PROFILE_STS_WEB_ID_TOKEN","q"))),"resolveWebIdentityCredentials"),bTA=dG(async(A,B,Q,D={},Z=!1)=>{let G=B[A];if(Object.keys(D).length>0&&kTA(G))return _TA(G,Q);if(Z||rjQ(G,{profile:A,logger:Q.logger}))return ejQ(A,B,Q,D);if(kTA(G))return _TA(G,Q);if(ZyQ(G))return GyQ(G,Q);if(AyQ(G))return ByQ(Q,A);if(DyQ(G))return await QyQ(A,G,Q);throw new uQ1.CredentialsProviderError(`Could not resolve credentials using profile: [${A}] in configuration/credentials file(s).`,{logger:Q.logger})},"resolveProfileData"),FyQ=dG((A={})=>async({callerClientConfig:B}={})=>{let Q={...A,parentClientConfig:{...B,...A.parentClientConfig}};Q.logger?.debug("@aws-sdk/credential-provider-ini - fromIni");let D=await Y00.parseKnownFiles(Q);return bTA(Y00.getProfileName({profile:A.profile??B?.profile}),D,Q)},"fromIni")});
var iKA=E((mD5,pKA)=>{var{defineProperty:Az1,getOwnPropertyDescriptor:tJQ,getOwnPropertyNames:eJQ}=Object,AXQ=Object.prototype.hasOwnProperty,Bz1=(A,B)=>Az1(A,"name",{value:B,configurable:!0}),BXQ=(A,B)=>{for(var Q in B)Az1(A,Q,{get:B[Q],enumerable:!0})},QXQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of eJQ(B))if(!AXQ.call(A,Z)&&Z!==Q)Az1(A,Z,{get:()=>B[Z],enumerable:!(D=tJQ(B,Z))||D.enumerable})}return A},DXQ=(A)=>QXQ(Az1({},"__esModule",{value:!0}),A),fKA={};BXQ(fKA,{AlgorithmId:()=>mKA,EndpointURLScheme:()=>uKA,FieldPosition:()=>dKA,HttpApiKeyAuthLocation:()=>gKA,HttpAuthLocation:()=>hKA,IniSectionType:()=>cKA,RequestHandlerProtocol:()=>lKA,SMITHY_CONTEXT_KEY:()=>YXQ,getDefaultClientConfiguration:()=>FXQ,resolveDefaultRuntimeConfig:()=>IXQ});pKA.exports=DXQ(fKA);var hKA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(hKA||{}),gKA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(gKA||{}),uKA=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(uKA||{}),mKA=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(mKA||{}),ZXQ=Bz1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),GXQ=Bz1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),FXQ=Bz1((A)=>{return ZXQ(A)},"getDefaultClientConfiguration"),IXQ=Bz1((A)=>{return GXQ(A)},"resolveDefaultRuntimeConfig"),dKA=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(dKA||{}),YXQ="__smithy_context",cKA=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(cKA||{}),lKA=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(lKA||{})});
var iTA=E((QI5,pTA)=>{var{create:IyQ,defineProperty:dQ1,getOwnPropertyDescriptor:YyQ,getOwnPropertyNames:WyQ,getPrototypeOf:JyQ}=Object,XyQ=Object.prototype.hasOwnProperty,kE1=(A,B)=>dQ1(A,"name",{value:B,configurable:!0}),VyQ=(A,B)=>{for(var Q in B)dQ1(A,Q,{get:B[Q],enumerable:!0})},mTA=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of WyQ(B))if(!XyQ.call(A,Z)&&Z!==Q)dQ1(A,Z,{get:()=>B[Z],enumerable:!(D=YyQ(B,Z))||D.enumerable})}return A},Sn=(A,B,Q)=>(Q=A!=null?IyQ(JyQ(A)):{},mTA(B||!A||!A.__esModule?dQ1(Q,"default",{value:A,enumerable:!0}):Q,A)),CyQ=(A)=>mTA(dQ1({},"__esModule",{value:!0}),A),dTA={};VyQ(dTA,{credentialsTreatedAsExpired:()=>lTA,credentialsWillNeedRefresh:()=>cTA,defaultProvider:()=>zyQ});pTA.exports=CyQ(dTA);var W00=be1(),KyQ=D3(),fh=Q9(),gTA="AWS_EC2_METADATA_DISABLED",HyQ=kE1(async(A)=>{let{ENV_CMDS_FULL_URI:B,ENV_CMDS_RELATIVE_URI:Q,fromContainerMetadata:D,fromInstanceMetadata:Z}=await Promise.resolve().then(()=>Sn(TF()));if(process.env[Q]||process.env[B]){A.logger?.debug("@aws-sdk/credential-provider-node - remoteProvider::fromHttp/fromContainerMetadata");let{fromHttp:G}=await Promise.resolve().then(()=>Sn(le1()));return fh.chain(G(A),D(A))}if(process.env[gTA]&&process.env[gTA]!=="false")return async()=>{throw new fh.CredentialsProviderError("EC2 Instance Metadata Service access disabled",{logger:A.logger})};return A.logger?.debug("@aws-sdk/credential-provider-node - remoteProvider::fromInstanceMetadata"),Z(A)},"remoteProvider"),uTA=!1,zyQ=kE1((A={})=>fh.memoize(fh.chain(async()=>{if(A.profile??process.env[KyQ.ENV_PROFILE]){if(process.env[W00.ENV_KEY]&&process.env[W00.ENV_SECRET]){if(!uTA)(A.logger?.warn&&A.logger?.constructor?.name!=="NoOpLogger"?A.logger.warn:console.warn)(`@aws-sdk/credential-provider-node - defaultProvider::fromEnv WARNING:
    Multiple credential sources detected: 
    Both AWS_PROFILE and the pair AWS_ACCESS_KEY_ID/AWS_SECRET_ACCESS_KEY static credentials are set.
    This SDK will proceed with the AWS_PROFILE value.
    
    However, a future version may change this behavior to prefer the ENV static credentials.
    Please ensure that your environment only sets either the AWS_PROFILE or the
    AWS_ACCESS_KEY_ID/AWS_SECRET_ACCESS_KEY pair.
`),uTA=!0}throw new fh.CredentialsProviderError("AWS_PROFILE is set, skipping fromEnv provider.",{logger:A.logger,tryNextLink:!0})}return A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromEnv"),W00.fromEnv(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromSSO");let{ssoStartUrl:B,ssoAccountId:Q,ssoRegion:D,ssoRoleName:Z,ssoSession:G}=A;if(!B&&!Q&&!D&&!Z&&!G)throw new fh.CredentialsProviderError("Skipping SSO provider in default chain (inputs do not include SSO fields).",{logger:A.logger});let{fromSSO:F}=await Promise.resolve().then(()=>Sn(C10()));return F(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromIni");let{fromIni:B}=await Promise.resolve().then(()=>Sn(hTA()));return B(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromProcess");let{fromProcess:B}=await Promise.resolve().then(()=>Sn(Q00()));return B(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromTokenFile");let{fromTokenFile:B}=await Promise.resolve().then(()=>Sn(F00()));return B(A)()},async()=>{return A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::remoteProvider"),(await HyQ(A))()},async()=>{throw new fh.CredentialsProviderError("Could not load credentials from any providers",{tryNextLink:!1,logger:A.logger})}),lTA,cTA),"defaultProvider"),cTA=kE1((A)=>A?.expiration!==void 0,"credentialsWillNeedRefresh"),lTA=kE1((A)=>A?.expiration!==void 0&&A.expiration.getTime()-Date.now()<300000,"credentialsTreatedAsExpired")});
var iUA=E((rZ5,pUA)=>{var{defineProperty:bz1,getOwnPropertyDescriptor:HEQ,getOwnPropertyNames:zEQ}=Object,EEQ=Object.prototype.hasOwnProperty,fz1=(A,B)=>bz1(A,"name",{value:B,configurable:!0}),UEQ=(A,B)=>{for(var Q in B)bz1(A,Q,{get:B[Q],enumerable:!0})},wEQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of zEQ(B))if(!EEQ.call(A,Z)&&Z!==Q)bz1(A,Z,{get:()=>B[Z],enumerable:!(D=HEQ(B,Z))||D.enumerable})}return A},$EQ=(A)=>wEQ(bz1({},"__esModule",{value:!0}),A),fUA={};UEQ(fUA,{AlgorithmId:()=>mUA,EndpointURLScheme:()=>uUA,FieldPosition:()=>dUA,HttpApiKeyAuthLocation:()=>gUA,HttpAuthLocation:()=>hUA,IniSectionType:()=>cUA,RequestHandlerProtocol:()=>lUA,SMITHY_CONTEXT_KEY:()=>REQ,getDefaultClientConfiguration:()=>LEQ,resolveDefaultRuntimeConfig:()=>MEQ});pUA.exports=$EQ(fUA);var hUA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(hUA||{}),gUA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(gUA||{}),uUA=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(uUA||{}),mUA=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(mUA||{}),qEQ=fz1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),NEQ=fz1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),LEQ=fz1((A)=>{return qEQ(A)},"getDefaultClientConfiguration"),MEQ=fz1((A)=>{return NEQ(A)},"resolveDefaultRuntimeConfig"),dUA=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(dUA||{}),REQ="__smithy_context",cUA=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(cUA||{}),lUA=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(lUA||{})});
var iXA=E((pXA)=>{Object.defineProperty(pXA,"__esModule",{value:!0});pXA.createBufferedReadable=JFQ;var YFQ=J1("node:stream"),lXA=Mo1(),gO=cXA(),WFQ=py();function JFQ(A,B,Q){if(WFQ.isReadableStream(A))return gO.createBufferedReadableStream(A,B,Q);let D=new YFQ.Readable({read(){}}),Z=!1,G=0,F=["",new lXA.ByteArrayCollector((Y)=>new Uint8Array(Y)),new lXA.ByteArrayCollector((Y)=>Buffer.from(new Uint8Array(Y)))],I=-1;return A.on("data",(Y)=>{let W=gO.modeOf(Y,!0);if(I!==W){if(I>=0)D.push(gO.flush(F,I));I=W}if(I===-1){D.push(Y);return}let J=gO.sizeOf(Y);G+=J;let X=gO.sizeOf(F[I]);if(J>=B&&X===0)D.push(Y);else{let V=gO.merge(F,I,Y);if(!Z&&G>B*2)Z=!0,Q===null||Q===void 0||Q.warn(`@smithy/util-stream - stream chunk size ${J} is below threshold of ${B}, automatically buffering.`);if(V>=B)D.push(gO.flush(F,I))}}),A.on("end",()=>{if(I!==-1){let Y=gO.flush(F,I);if(gO.sizeOf(Y)>0)D.push(Y)}D.push(null)}),D}});
var ie1=E((vNA)=>{Object.defineProperty(vNA,"__esModule",{value:!0});vNA.resolveHttpAuthSchemeConfig=vNA.defaultSSOHttpAuthSchemeProvider=vNA.defaultSSOHttpAuthSchemeParametersProvider=void 0;var $MQ=WI(),pe1=J5(),qMQ=async(A,B,Q)=>{return{operation:pe1.getSmithyContext(B).operation,region:await pe1.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};vNA.defaultSSOHttpAuthSchemeParametersProvider=qMQ;function NMQ(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"awsssoportal",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function WE1(A){return{schemeId:"smithy.api#noAuth"}}var LMQ=(A)=>{let B=[];switch(A.operation){case"GetRoleCredentials":{B.push(WE1(A));break}case"ListAccountRoles":{B.push(WE1(A));break}case"ListAccounts":{B.push(WE1(A));break}case"Logout":{B.push(WE1(A));break}default:B.push(NMQ(A))}return B};vNA.defaultSSOHttpAuthSchemeProvider=LMQ;var MMQ=(A)=>{let B=$MQ.resolveAwsSdkSigV4Config(A);return Object.assign(B,{authSchemePreference:pe1.normalizeProvider(A.authSchemePreference??[])})};vNA.resolveHttpAuthSchemeConfig=MMQ});
var ii=E((y75,SH1)=>{var{defineProperty:wXA,getOwnPropertyDescriptor:fGQ,getOwnPropertyNames:hGQ}=Object,gGQ=Object.prototype.hasOwnProperty,qo1=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of hGQ(B))if(!gGQ.call(A,Z)&&Z!==Q)wXA(A,Z,{get:()=>B[Z],enumerable:!(D=fGQ(B,Z))||D.enumerable})}return A},$XA=(A,B,Q)=>(qo1(A,B,"default"),Q&&qo1(Q,B,"default")),uGQ=(A)=>qo1(wXA({},"__esModule",{value:!0}),A),No1={};SH1.exports=uGQ(No1);$XA(No1,XXA(),SH1.exports);$XA(No1,UXA(),SH1.exports)});
var izA=E((lzA)=>{Object.defineProperty(lzA,"__esModule",{value:!0});lzA.fromBase64=void 0;var wCQ=YD(),$CQ=/^[A-Za-z0-9+/]*={0,2}$/,qCQ=(A)=>{if(A.length*3%4!==0)throw new TypeError("Incorrect padding on base64 string.");if(!$CQ.exec(A))throw new TypeError("Invalid base64 string.");let B=wCQ.fromString(A,"base64");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength)};lzA.fromBase64=qCQ});
var j3=E((q75,oJA)=>{var{defineProperty:LH1,getOwnPropertyDescriptor:mZQ,getOwnPropertyNames:dZQ}=Object,cZQ=Object.prototype.hasOwnProperty,MH1=(A,B)=>LH1(A,"name",{value:B,configurable:!0}),lZQ=(A,B)=>{for(var Q in B)LH1(A,Q,{get:B[Q],enumerable:!0})},pZQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of dZQ(B))if(!cZQ.call(A,Z)&&Z!==Q)LH1(A,Z,{get:()=>B[Z],enumerable:!(D=mZQ(B,Z))||D.enumerable})}return A},iZQ=(A)=>pZQ(LH1({},"__esModule",{value:!0}),A),pJA={};lZQ(pJA,{deserializerMiddleware:()=>iJA,deserializerMiddlewareOption:()=>aJA,getSerdePlugin:()=>rJA,serializerMiddleware:()=>nJA,serializerMiddlewareOption:()=>sJA});oJA.exports=iZQ(pJA);var nZQ=lJA(),iJA=MH1((A,B)=>(Q,D)=>async(Z)=>{let{response:G}=await Q(Z);try{let F=await B(G,A);return{response:G,output:F}}catch(F){if(Object.defineProperty(F,"$response",{value:G}),!("$metadata"in F)){try{F.message+=`
  Deserialization error: to see the raw response, inspect the hidden field {error}.$response on this object.`}catch(Y){if(!D.logger||D.logger?.constructor?.name==="NoOpLogger")console.warn("Deserialization error: to see the raw response, inspect the hidden field {error}.$response on this object.");else D.logger?.warn?.("Deserialization error: to see the raw response, inspect the hidden field {error}.$response on this object.")}if(typeof F.$responseBodyText!=="undefined"){if(F.$response)F.$response.body=F.$responseBodyText}try{if(nZQ.HttpResponse.isInstance(G)){let{headers:Y={}}=G,W=Object.entries(Y);F.$metadata={httpStatusCode:G.statusCode,requestId:Uo1(/^x-[\w-]+-request-?id$/,W),extendedRequestId:Uo1(/^x-[\w-]+-id-2$/,W),cfId:Uo1(/^x-[\w-]+-cf-id$/,W)}}}catch(Y){}}throw F}},"deserializerMiddleware"),Uo1=MH1((A,B)=>{return(B.find(([Q])=>{return Q.match(A)})||[void 0,void 0])[1]},"findHeader"),nJA=MH1((A,B)=>(Q,D)=>async(Z)=>{let G=A,F=D.endpointV2?.url&&G.urlParser?async()=>G.urlParser(D.endpointV2.url):G.endpoint;if(!F)throw new Error("No valid endpoint provider available.");let I=await B(Z.input,{...A,endpoint:F});return Q({...Z,request:I})},"serializerMiddleware"),aJA={name:"deserializerMiddleware",step:"deserialize",tags:["DESERIALIZER"],override:!0},sJA={name:"serializerMiddleware",step:"serialize",tags:["SERIALIZER"],override:!0};function rJA(A,B,Q){return{applyToStack:(D)=>{D.add(iJA(A,Q),aJA),D.add(nJA(A,B),sJA)}}}MH1(rJA,"getSerdePlugin")});
var jEA=E((zHQ)=>{function VHQ(A,B){return SEA(A,B)}function SEA(A,B,Q){let D,Z={};for(let G=0;G<A.length;G++){let F=A[G],I=CHQ(F),Y="";if(Q===void 0)Y=I;else Y=Q+"."+I;if(I===B.textNodeName)if(D===void 0)D=F[I];else D+=""+F[I];else if(I===void 0)continue;else if(F[I]){let W=SEA(F[I],B,Y),J=HHQ(W,B);if(F[":@"])KHQ(W,F[":@"],Y,B);else if(Object.keys(W).length===1&&W[B.textNodeName]!==void 0&&!B.alwaysCreateTextNode)W=W[B.textNodeName];else if(Object.keys(W).length===0)if(B.alwaysCreateTextNode)W[B.textNodeName]="";else W="";if(Z[I]!==void 0&&Z.hasOwnProperty(I)){if(!Array.isArray(Z[I]))Z[I]=[Z[I]];Z[I].push(W)}else if(B.isArray(I,Y,J))Z[I]=[W];else Z[I]=W}}if(typeof D==="string"){if(D.length>0)Z[B.textNodeName]=D}else if(D!==void 0)Z[B.textNodeName]=D;return Z}function CHQ(A){let B=Object.keys(A);for(let Q=0;Q<B.length;Q++){let D=B[Q];if(D!==":@")return D}}function KHQ(A,B,Q,D){if(B){let Z=Object.keys(B),G=Z.length;for(let F=0;F<G;F++){let I=Z[F];if(D.isArray(I,Q+"."+I,!0,!0))A[I]=[B[I]];else A[I]=B[I]}}}function HHQ(A,B){let{textNodeName:Q}=B,D=Object.keys(A).length;if(D===0)return!0;if(D===1&&(A[Q]||typeof A[Q]==="boolean"||A[Q]===0))return!0;return!1}zHQ.prettify=VHQ});
var jRA=E((vF5,SRA)=>{var{create:sTQ,defineProperty:bQ1,getOwnPropertyDescriptor:rTQ,getOwnPropertyNames:oTQ,getPrototypeOf:tTQ}=Object,eTQ=Object.prototype.hasOwnProperty,iO=(A,B)=>bQ1(A,"name",{value:B,configurable:!0}),APQ=(A,B)=>{for(var Q in B)bQ1(A,Q,{get:B[Q],enumerable:!0})},RRA=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of oTQ(B))if(!eTQ.call(A,Z)&&Z!==Q)bQ1(A,Z,{get:()=>B[Z],enumerable:!(D=rTQ(B,Z))||D.enumerable})}return A},ORA=(A,B,Q)=>(Q=A!=null?sTQ(tTQ(A)):{},RRA(B||!A||!A.__esModule?bQ1(Q,"default",{value:A,enumerable:!0}):Q,A)),BPQ=(A)=>RRA(bQ1({},"__esModule",{value:!0}),A),TRA={};APQ(TRA,{fromEnvSigningName:()=>ZPQ,fromSso:()=>PRA,fromStatic:()=>XPQ,nodeProvider:()=>VPQ});SRA.exports=BPQ(TRA);var QPQ=Zz(),DPQ=Pt1(),AK=Q9(),ZPQ=iO(({logger:A,signingName:B}={})=>async()=>{if(A?.debug?.("@aws-sdk/token-providers - fromEnvSigningName"),!B)throw new AK.TokenProviderError("Please pass 'signingName' to compute environment variable key",{logger:A});let Q=DPQ.getBearerTokenEnvKey(B);if(!(Q in process.env))throw new AK.TokenProviderError(`Token not present in '${Q}' environment variable`,{logger:A});let D={token:process.env[Q]};return QPQ.setTokenFeature(D,"BEARER_SERVICE_ENV_VARS","3"),D},"fromEnvSigningName"),GPQ=300000,X10="To refresh this SSO session run 'aws sso login' with the corresponding profile.",FPQ=iO(async(A,B={})=>{let{SSOOIDCClient:Q}=await Promise.resolve().then(()=>ORA(J10()));return new Q(Object.assign({},B.clientConfig??{},{region:A??B.clientConfig?.region,logger:B.clientConfig?.logger??B.parentClientConfig?.logger}))},"getSsoOidcClient"),IPQ=iO(async(A,B,Q={})=>{let{CreateTokenCommand:D}=await Promise.resolve().then(()=>ORA(J10()));return(await FPQ(B,Q)).send(new D({clientId:A.clientId,clientSecret:A.clientSecret,refreshToken:A.refreshToken,grantType:"refresh_token"}))},"getNewSsoOidcToken"),LRA=iO((A)=>{if(A.expiration&&A.expiration.getTime()<Date.now())throw new AK.TokenProviderError(`Token is expired. ${X10}`,!1)},"validateTokenExpiry"),vh=iO((A,B,Q=!1)=>{if(typeof B==="undefined")throw new AK.TokenProviderError(`Value not present for '${A}' in SSO Token${Q?". Cannot refresh":""}. ${X10}`,!1)},"validateTokenKey"),vQ1=D3(),YPQ=J1("fs"),{writeFile:WPQ}=YPQ.promises,JPQ=iO((A,B)=>{let Q=vQ1.getSSOTokenFilepath(A),D=JSON.stringify(B,null,2);return WPQ(Q,D)},"writeSSOTokenToFile"),MRA=new Date(0),PRA=iO((A={})=>async({callerClientConfig:B}={})=>{let Q={...A,parentClientConfig:{...B,...A.parentClientConfig}};Q.logger?.debug("@aws-sdk/token-providers - fromSso");let D=await vQ1.parseKnownFiles(Q),Z=vQ1.getProfileName({profile:Q.profile??B?.profile}),G=D[Z];if(!G)throw new AK.TokenProviderError(`Profile '${Z}' could not be found in shared credentials file.`,!1);else if(!G.sso_session)throw new AK.TokenProviderError(`Profile '${Z}' is missing required property 'sso_session'.`);let F=G.sso_session,Y=(await vQ1.loadSsoSessionData(Q))[F];if(!Y)throw new AK.TokenProviderError(`Sso session '${F}' could not be found in shared credentials file.`,!1);for(let H of["sso_start_url","sso_region"])if(!Y[H])throw new AK.TokenProviderError(`Sso session '${F}' is missing required property '${H}'.`,!1);let{sso_start_url:W,sso_region:J}=Y,X;try{X=await vQ1.getSSOTokenFromFile(F)}catch(H){throw new AK.TokenProviderError(`The SSO session token associated with profile=${Z} was not found or is invalid. ${X10}`,!1)}vh("accessToken",X.accessToken),vh("expiresAt",X.expiresAt);let{accessToken:V,expiresAt:C}=X,K={token:V,expiration:new Date(C)};if(K.expiration.getTime()-Date.now()>GPQ)return K;if(Date.now()-MRA.getTime()<30000)return LRA(K),K;vh("clientId",X.clientId,!0),vh("clientSecret",X.clientSecret,!0),vh("refreshToken",X.refreshToken,!0);try{MRA.setTime(Date.now());let H=await IPQ(X,J,Q);vh("accessToken",H.accessToken),vh("expiresIn",H.expiresIn);let z=new Date(Date.now()+H.expiresIn*1000);try{await JPQ(F,{...X,accessToken:H.accessToken,expiresAt:z.toISOString(),refreshToken:H.refreshToken})}catch($){}return{token:H.accessToken,expiration:z}}catch(H){return LRA(K),K}},"fromSso"),XPQ=iO(({token:A,logger:B})=>async()=>{if(B?.debug("@aws-sdk/token-providers - fromStatic"),!A||!A.token)throw new AK.TokenProviderError("Please pass a valid token to fromStatic",!1);return A},"fromStatic"),VPQ=iO((A={})=>AK.memoize(AK.chain(PRA(A),async()=>{throw new AK.TokenProviderError("Could not load token from any providers",!1)}),(B)=>B.expiration!==void 0&&B.expiration.getTime()-Date.now()<300000,(B)=>B.expiration!==void 0),"nodeProvider")});
var jSA=E((XI5,t00)=>{var{defineProperty:xE1,getOwnPropertyDescriptor:tyQ,getOwnPropertyNames:eyQ}=Object,AkQ=Object.prototype.hasOwnProperty,_A=(A,B)=>xE1(A,"name",{value:B,configurable:!0}),BkQ=(A,B)=>{for(var Q in B)xE1(A,Q,{get:B[Q],enumerable:!0})},i00=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of eyQ(B))if(!AkQ.call(A,Z)&&Z!==Q)xE1(A,Z,{get:()=>B[Z],enumerable:!(D=tyQ(B,Z))||D.enumerable})}return A},QkQ=(A,B,Q)=>(i00(A,B,"default"),Q&&i00(Q,B,"default")),DkQ=(A)=>i00(xE1({},"__esModule",{value:!0}),A),a00={};BkQ(a00,{AssumeRoleCommand:()=>r00,AssumeRoleResponseFilterSensitiveLog:()=>GSA,AssumeRoleWithSAMLCommand:()=>HSA,AssumeRoleWithSAMLRequestFilterSensitiveLog:()=>FSA,AssumeRoleWithSAMLResponseFilterSensitiveLog:()=>ISA,AssumeRoleWithWebIdentityCommand:()=>o00,AssumeRoleWithWebIdentityRequestFilterSensitiveLog:()=>YSA,AssumeRoleWithWebIdentityResponseFilterSensitiveLog:()=>WSA,AssumeRootCommand:()=>zSA,AssumeRootResponseFilterSensitiveLog:()=>JSA,ClientInputEndpointParameters:()=>j_Q.ClientInputEndpointParameters,CredentialsFilterSensitiveLog:()=>gh,DecodeAuthorizationMessageCommand:()=>ESA,ExpiredTokenException:()=>oPA,GetAccessKeyInfoCommand:()=>USA,GetCallerIdentityCommand:()=>wSA,GetFederationTokenCommand:()=>$SA,GetFederationTokenResponseFilterSensitiveLog:()=>XSA,GetSessionTokenCommand:()=>qSA,GetSessionTokenResponseFilterSensitiveLog:()=>VSA,IDPCommunicationErrorException:()=>DSA,IDPRejectedClaimException:()=>BSA,InvalidAuthorizationMessageException:()=>ZSA,InvalidIdentityTokenException:()=>QSA,MalformedPolicyDocumentException:()=>tPA,PackedPolicyTooLargeException:()=>ePA,RegionDisabledException:()=>ASA,STS:()=>NSA,STSServiceException:()=>aN,decorateDefaultCredentialProvider:()=>__Q,getDefaultRoleAssumer:()=>PSA,getDefaultRoleAssumerWithWebIdentity:()=>SSA});t00.exports=DkQ(a00);QkQ(a00,TQ1(),t00.exports);var eO=R6(),AT=j3(),ZkQ=Yz(),aB=g4(),aN=class A extends aB.ServiceException{static{_A(this,"STSServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},oPA=class A extends aN{static{_A(this,"ExpiredTokenException")}name="ExpiredTokenException";$fault="client";constructor(B){super({name:"ExpiredTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},tPA=class A extends aN{static{_A(this,"MalformedPolicyDocumentException")}name="MalformedPolicyDocumentException";$fault="client";constructor(B){super({name:"MalformedPolicyDocumentException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},ePA=class A extends aN{static{_A(this,"PackedPolicyTooLargeException")}name="PackedPolicyTooLargeException";$fault="client";constructor(B){super({name:"PackedPolicyTooLargeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},ASA=class A extends aN{static{_A(this,"RegionDisabledException")}name="RegionDisabledException";$fault="client";constructor(B){super({name:"RegionDisabledException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},BSA=class A extends aN{static{_A(this,"IDPRejectedClaimException")}name="IDPRejectedClaimException";$fault="client";constructor(B){super({name:"IDPRejectedClaimException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},QSA=class A extends aN{static{_A(this,"InvalidIdentityTokenException")}name="InvalidIdentityTokenException";$fault="client";constructor(B){super({name:"InvalidIdentityTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},DSA=class A extends aN{static{_A(this,"IDPCommunicationErrorException")}name="IDPCommunicationErrorException";$fault="client";constructor(B){super({name:"IDPCommunicationErrorException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},ZSA=class A extends aN{static{_A(this,"InvalidAuthorizationMessageException")}name="InvalidAuthorizationMessageException";$fault="client";constructor(B){super({name:"InvalidAuthorizationMessageException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},gh=_A((A)=>({...A,...A.SecretAccessKey&&{SecretAccessKey:aB.SENSITIVE_STRING}}),"CredentialsFilterSensitiveLog"),GSA=_A((A)=>({...A,...A.Credentials&&{Credentials:gh(A.Credentials)}}),"AssumeRoleResponseFilterSensitiveLog"),FSA=_A((A)=>({...A,...A.SAMLAssertion&&{SAMLAssertion:aB.SENSITIVE_STRING}}),"AssumeRoleWithSAMLRequestFilterSensitiveLog"),ISA=_A((A)=>({...A,...A.Credentials&&{Credentials:gh(A.Credentials)}}),"AssumeRoleWithSAMLResponseFilterSensitiveLog"),YSA=_A((A)=>({...A,...A.WebIdentityToken&&{WebIdentityToken:aB.SENSITIVE_STRING}}),"AssumeRoleWithWebIdentityRequestFilterSensitiveLog"),WSA=_A((A)=>({...A,...A.Credentials&&{Credentials:gh(A.Credentials)}}),"AssumeRoleWithWebIdentityResponseFilterSensitiveLog"),JSA=_A((A)=>({...A,...A.Credentials&&{Credentials:gh(A.Credentials)}}),"AssumeRootResponseFilterSensitiveLog"),XSA=_A((A)=>({...A,...A.Credentials&&{Credentials:gh(A.Credentials)}}),"GetFederationTokenResponseFilterSensitiveLog"),VSA=_A((A)=>({...A,...A.Credentials&&{Credentials:gh(A.Credentials)}}),"GetSessionTokenResponseFilterSensitiveLog"),sN=WI(),GkQ=sJ(),FkQ=_A(async(A,B)=>{let Q=DT,D;return D=IT({...kkQ(A,B),[GT]:J_Q,[FT]:ZT}),QT(B,Q,"/",void 0,D)},"se_AssumeRoleCommand"),IkQ=_A(async(A,B)=>{let Q=DT,D;return D=IT({..._kQ(A,B),[GT]:X_Q,[FT]:ZT}),QT(B,Q,"/",void 0,D)},"se_AssumeRoleWithSAMLCommand"),YkQ=_A(async(A,B)=>{let Q=DT,D;return D=IT({...xkQ(A,B),[GT]:V_Q,[FT]:ZT}),QT(B,Q,"/",void 0,D)},"se_AssumeRoleWithWebIdentityCommand"),WkQ=_A(async(A,B)=>{let Q=DT,D;return D=IT({...vkQ(A,B),[GT]:C_Q,[FT]:ZT}),QT(B,Q,"/",void 0,D)},"se_AssumeRootCommand"),JkQ=_A(async(A,B)=>{let Q=DT,D;return D=IT({...bkQ(A,B),[GT]:K_Q,[FT]:ZT}),QT(B,Q,"/",void 0,D)},"se_DecodeAuthorizationMessageCommand"),XkQ=_A(async(A,B)=>{let Q=DT,D;return D=IT({...fkQ(A,B),[GT]:H_Q,[FT]:ZT}),QT(B,Q,"/",void 0,D)},"se_GetAccessKeyInfoCommand"),VkQ=_A(async(A,B)=>{let Q=DT,D;return D=IT({...hkQ(A,B),[GT]:z_Q,[FT]:ZT}),QT(B,Q,"/",void 0,D)},"se_GetCallerIdentityCommand"),CkQ=_A(async(A,B)=>{let Q=DT,D;return D=IT({...gkQ(A,B),[GT]:E_Q,[FT]:ZT}),QT(B,Q,"/",void 0,D)},"se_GetFederationTokenCommand"),KkQ=_A(async(A,B)=>{let Q=DT,D;return D=IT({...ukQ(A,B),[GT]:U_Q,[FT]:ZT}),QT(B,Q,"/",void 0,D)},"se_GetSessionTokenCommand"),HkQ=_A(async(A,B)=>{if(A.statusCode>=300)return BT(A,B);let Q=await sN.parseXmlBody(A.body,B),D={};return D=pkQ(Q.AssumeRoleResult,B),{$metadata:xY(A),...D}},"de_AssumeRoleCommand"),zkQ=_A(async(A,B)=>{if(A.statusCode>=300)return BT(A,B);let Q=await sN.parseXmlBody(A.body,B),D={};return D=ikQ(Q.AssumeRoleWithSAMLResult,B),{$metadata:xY(A),...D}},"de_AssumeRoleWithSAMLCommand"),EkQ=_A(async(A,B)=>{if(A.statusCode>=300)return BT(A,B);let Q=await sN.parseXmlBody(A.body,B),D={};return D=nkQ(Q.AssumeRoleWithWebIdentityResult,B),{$metadata:xY(A),...D}},"de_AssumeRoleWithWebIdentityCommand"),UkQ=_A(async(A,B)=>{if(A.statusCode>=300)return BT(A,B);let Q=await sN.parseXmlBody(A.body,B),D={};return D=akQ(Q.AssumeRootResult,B),{$metadata:xY(A),...D}},"de_AssumeRootCommand"),wkQ=_A(async(A,B)=>{if(A.statusCode>=300)return BT(A,B);let Q=await sN.parseXmlBody(A.body,B),D={};return D=skQ(Q.DecodeAuthorizationMessageResult,B),{$metadata:xY(A),...D}},"de_DecodeAuthorizationMessageCommand"),$kQ=_A(async(A,B)=>{if(A.statusCode>=300)return BT(A,B);let Q=await sN.parseXmlBody(A.body,B),D={};return D=tkQ(Q.GetAccessKeyInfoResult,B),{$metadata:xY(A),...D}},"de_GetAccessKeyInfoCommand"),qkQ=_A(async(A,B)=>{if(A.statusCode>=300)return BT(A,B);let Q=await sN.parseXmlBody(A.body,B),D={};return D=ekQ(Q.GetCallerIdentityResult,B),{$metadata:xY(A),...D}},"de_GetCallerIdentityCommand"),NkQ=_A(async(A,B)=>{if(A.statusCode>=300)return BT(A,B);let Q=await sN.parseXmlBody(A.body,B),D={};return D=A_Q(Q.GetFederationTokenResult,B),{$metadata:xY(A),...D}},"de_GetFederationTokenCommand"),LkQ=_A(async(A,B)=>{if(A.statusCode>=300)return BT(A,B);let Q=await sN.parseXmlBody(A.body,B),D={};return D=B_Q(Q.GetSessionTokenResult,B),{$metadata:xY(A),...D}},"de_GetSessionTokenCommand"),BT=_A(async(A,B)=>{let Q={...A,body:await sN.parseXmlErrorBody(A.body,B)},D=w_Q(A,Q.body);switch(D){case"ExpiredTokenException":case"com.amazonaws.sts#ExpiredTokenException":throw await MkQ(Q,B);case"MalformedPolicyDocument":case"com.amazonaws.sts#MalformedPolicyDocumentException":throw await SkQ(Q,B);case"PackedPolicyTooLarge":case"com.amazonaws.sts#PackedPolicyTooLargeException":throw await jkQ(Q,B);case"RegionDisabledException":case"com.amazonaws.sts#RegionDisabledException":throw await ykQ(Q,B);case"IDPRejectedClaim":case"com.amazonaws.sts#IDPRejectedClaimException":throw await OkQ(Q,B);case"InvalidIdentityToken":case"com.amazonaws.sts#InvalidIdentityTokenException":throw await PkQ(Q,B);case"IDPCommunicationError":case"com.amazonaws.sts#IDPCommunicationErrorException":throw await RkQ(Q,B);case"InvalidAuthorizationMessageException":case"com.amazonaws.sts#InvalidAuthorizationMessageException":throw await TkQ(Q,B);default:let Z=Q.body;return W_Q({output:A,parsedBody:Z.Error,errorCode:D})}},"de_CommandError"),MkQ=_A(async(A,B)=>{let Q=A.body,D=rkQ(Q.Error,B),Z=new oPA({$metadata:xY(A),...D});return aB.decorateServiceException(Z,Q)},"de_ExpiredTokenExceptionRes"),RkQ=_A(async(A,B)=>{let Q=A.body,D=Q_Q(Q.Error,B),Z=new DSA({$metadata:xY(A),...D});return aB.decorateServiceException(Z,Q)},"de_IDPCommunicationErrorExceptionRes"),OkQ=_A(async(A,B)=>{let Q=A.body,D=D_Q(Q.Error,B),Z=new BSA({$metadata:xY(A),...D});return aB.decorateServiceException(Z,Q)},"de_IDPRejectedClaimExceptionRes"),TkQ=_A(async(A,B)=>{let Q=A.body,D=Z_Q(Q.Error,B),Z=new ZSA({$metadata:xY(A),...D});return aB.decorateServiceException(Z,Q)},"de_InvalidAuthorizationMessageExceptionRes"),PkQ=_A(async(A,B)=>{let Q=A.body,D=G_Q(Q.Error,B),Z=new QSA({$metadata:xY(A),...D});return aB.decorateServiceException(Z,Q)},"de_InvalidIdentityTokenExceptionRes"),SkQ=_A(async(A,B)=>{let Q=A.body,D=F_Q(Q.Error,B),Z=new tPA({$metadata:xY(A),...D});return aB.decorateServiceException(Z,Q)},"de_MalformedPolicyDocumentExceptionRes"),jkQ=_A(async(A,B)=>{let Q=A.body,D=I_Q(Q.Error,B),Z=new ePA({$metadata:xY(A),...D});return aB.decorateServiceException(Z,Q)},"de_PackedPolicyTooLargeExceptionRes"),ykQ=_A(async(A,B)=>{let Q=A.body,D=Y_Q(Q.Error,B),Z=new ASA({$metadata:xY(A),...D});return aB.decorateServiceException(Z,Q)},"de_RegionDisabledExceptionRes"),kkQ=_A((A,B)=>{let Q={};if(A[tO]!=null)Q[tO]=A[tO];if(A[xn]!=null)Q[xn]=A[xn];if(A[Ez]!=null){let D=vE1(A[Ez],B);if(A[Ez]?.length===0)Q.PolicyArns=[];Object.entries(D).forEach(([Z,G])=>{let F=`PolicyArns.${Z}`;Q[F]=G})}if(A[zz]!=null)Q[zz]=A[zz];if(A[XI]!=null)Q[XI]=A[XI];if(A[bn]!=null){let D=KSA(A[bn],B);if(A[bn]?.length===0)Q.Tags=[];Object.entries(D).forEach(([Z,G])=>{let F=`Tags.${Z}`;Q[F]=G})}if(A[m00]!=null){let D=lkQ(A[m00],B);if(A[m00]?.length===0)Q.TransitiveTagKeys=[];Object.entries(D).forEach(([Z,G])=>{let F=`TransitiveTagKeys.${Z}`;Q[F]=G})}if(A[q00]!=null)Q[q00]=A[q00];if(A[vn]!=null)Q[vn]=A[vn];if(A[fn]!=null)Q[fn]=A[fn];if(A[oJ]!=null)Q[oJ]=A[oJ];if(A[y00]!=null){let D=dkQ(A[y00],B);if(A[y00]?.length===0)Q.ProvidedContexts=[];Object.entries(D).forEach(([Z,G])=>{let F=`ProvidedContexts.${Z}`;Q[F]=G})}return Q},"se_AssumeRoleRequest"),_kQ=_A((A,B)=>{let Q={};if(A[tO]!=null)Q[tO]=A[tO];if(A[S00]!=null)Q[S00]=A[S00];if(A[b00]!=null)Q[b00]=A[b00];if(A[Ez]!=null){let D=vE1(A[Ez],B);if(A[Ez]?.length===0)Q.PolicyArns=[];Object.entries(D).forEach(([Z,G])=>{let F=`PolicyArns.${Z}`;Q[F]=G})}if(A[zz]!=null)Q[zz]=A[zz];if(A[XI]!=null)Q[XI]=A[XI];return Q},"se_AssumeRoleWithSAMLRequest"),xkQ=_A((A,B)=>{let Q={};if(A[tO]!=null)Q[tO]=A[tO];if(A[xn]!=null)Q[xn]=A[xn];if(A[l00]!=null)Q[l00]=A[l00];if(A[k00]!=null)Q[k00]=A[k00];if(A[Ez]!=null){let D=vE1(A[Ez],B);if(A[Ez]?.length===0)Q.PolicyArns=[];Object.entries(D).forEach(([Z,G])=>{let F=`PolicyArns.${Z}`;Q[F]=G})}if(A[zz]!=null)Q[zz]=A[zz];if(A[XI]!=null)Q[XI]=A[XI];return Q},"se_AssumeRoleWithWebIdentityRequest"),vkQ=_A((A,B)=>{let Q={};if(A[u00]!=null)Q[u00]=A[u00];if(A[sPA]!=null){let D=CSA(A[sPA],B);Object.entries(D).forEach(([Z,G])=>{let F=`TaskPolicyArn.${Z}`;Q[F]=G})}if(A[XI]!=null)Q[XI]=A[XI];return Q},"se_AssumeRootRequest"),bkQ=_A((A,B)=>{let Q={};if(A[N00]!=null)Q[N00]=A[N00];return Q},"se_DecodeAuthorizationMessageRequest"),fkQ=_A((A,B)=>{let Q={};if(A[yn]!=null)Q[yn]=A[yn];return Q},"se_GetAccessKeyInfoRequest"),hkQ=_A((A,B)=>{return{}},"se_GetCallerIdentityRequest"),gkQ=_A((A,B)=>{let Q={};if(A[T00]!=null)Q[T00]=A[T00];if(A[zz]!=null)Q[zz]=A[zz];if(A[Ez]!=null){let D=vE1(A[Ez],B);if(A[Ez]?.length===0)Q.PolicyArns=[];Object.entries(D).forEach(([Z,G])=>{let F=`PolicyArns.${Z}`;Q[F]=G})}if(A[XI]!=null)Q[XI]=A[XI];if(A[bn]!=null){let D=KSA(A[bn],B);if(A[bn]?.length===0)Q.Tags=[];Object.entries(D).forEach(([Z,G])=>{let F=`Tags.${Z}`;Q[F]=G})}return Q},"se_GetFederationTokenRequest"),ukQ=_A((A,B)=>{let Q={};if(A[XI]!=null)Q[XI]=A[XI];if(A[vn]!=null)Q[vn]=A[vn];if(A[fn]!=null)Q[fn]=A[fn];return Q},"se_GetSessionTokenRequest"),vE1=_A((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=CSA(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_policyDescriptorListType"),CSA=_A((A,B)=>{let Q={};if(A[p00]!=null)Q[p00]=A[p00];return Q},"se_PolicyDescriptorType"),mkQ=_A((A,B)=>{let Q={};if(A[j00]!=null)Q[j00]=A[j00];if(A[U00]!=null)Q[U00]=A[U00];return Q},"se_ProvidedContext"),dkQ=_A((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=mkQ(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_ProvidedContextsListType"),ckQ=_A((A,B)=>{let Q={};if(A[O00]!=null)Q[O00]=A[O00];if(A[c00]!=null)Q[c00]=A[c00];return Q},"se_Tag"),lkQ=_A((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;Q[`member.${D}`]=Z,D++}return Q},"se_tagKeyListType"),KSA=_A((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=ckQ(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_tagListType"),s00=_A((A,B)=>{let Q={};if(A[E00]!=null)Q[E00]=aB.expectString(A[E00]);if(A[oO]!=null)Q[oO]=aB.expectString(A[oO]);return Q},"de_AssumedRoleUser"),pkQ=_A((A,B)=>{let Q={};if(A[JI]!=null)Q[JI]=hn(A[JI],B);if(A[rO]!=null)Q[rO]=s00(A[rO],B);if(A[Uz]!=null)Q[Uz]=aB.strictParseInt32(A[Uz]);if(A[oJ]!=null)Q[oJ]=aB.expectString(A[oJ]);return Q},"de_AssumeRoleResponse"),ikQ=_A((A,B)=>{let Q={};if(A[JI]!=null)Q[JI]=hn(A[JI],B);if(A[rO]!=null)Q[rO]=s00(A[rO],B);if(A[Uz]!=null)Q[Uz]=aB.strictParseInt32(A[Uz]);if(A[x00]!=null)Q[x00]=aB.expectString(A[x00]);if(A[h00]!=null)Q[h00]=aB.expectString(A[h00]);if(A[R00]!=null)Q[R00]=aB.expectString(A[R00]);if(A[_n]!=null)Q[_n]=aB.expectString(A[_n]);if(A[P00]!=null)Q[P00]=aB.expectString(A[P00]);if(A[oJ]!=null)Q[oJ]=aB.expectString(A[oJ]);return Q},"de_AssumeRoleWithSAMLResponse"),nkQ=_A((A,B)=>{let Q={};if(A[JI]!=null)Q[JI]=hn(A[JI],B);if(A[f00]!=null)Q[f00]=aB.expectString(A[f00]);if(A[rO]!=null)Q[rO]=s00(A[rO],B);if(A[Uz]!=null)Q[Uz]=aB.strictParseInt32(A[Uz]);if(A[_00]!=null)Q[_00]=aB.expectString(A[_00]);if(A[_n]!=null)Q[_n]=aB.expectString(A[_n]);if(A[oJ]!=null)Q[oJ]=aB.expectString(A[oJ]);return Q},"de_AssumeRoleWithWebIdentityResponse"),akQ=_A((A,B)=>{let Q={};if(A[JI]!=null)Q[JI]=hn(A[JI],B);if(A[oJ]!=null)Q[oJ]=aB.expectString(A[oJ]);return Q},"de_AssumeRootResponse"),hn=_A((A,B)=>{let Q={};if(A[yn]!=null)Q[yn]=aB.expectString(A[yn]);if(A[v00]!=null)Q[v00]=aB.expectString(A[v00]);if(A[g00]!=null)Q[g00]=aB.expectString(A[g00]);if(A[$00]!=null)Q[$00]=aB.expectNonNull(aB.parseRfc3339DateTimeWithOffset(A[$00]));return Q},"de_Credentials"),skQ=_A((A,B)=>{let Q={};if(A[w00]!=null)Q[w00]=aB.expectString(A[w00]);return Q},"de_DecodeAuthorizationMessageResponse"),rkQ=_A((A,B)=>{let Q={};if(A[CD]!=null)Q[CD]=aB.expectString(A[CD]);return Q},"de_ExpiredTokenException"),okQ=_A((A,B)=>{let Q={};if(A[M00]!=null)Q[M00]=aB.expectString(A[M00]);if(A[oO]!=null)Q[oO]=aB.expectString(A[oO]);return Q},"de_FederatedUser"),tkQ=_A((A,B)=>{let Q={};if(A[kn]!=null)Q[kn]=aB.expectString(A[kn]);return Q},"de_GetAccessKeyInfoResponse"),ekQ=_A((A,B)=>{let Q={};if(A[d00]!=null)Q[d00]=aB.expectString(A[d00]);if(A[kn]!=null)Q[kn]=aB.expectString(A[kn]);if(A[oO]!=null)Q[oO]=aB.expectString(A[oO]);return Q},"de_GetCallerIdentityResponse"),A_Q=_A((A,B)=>{let Q={};if(A[JI]!=null)Q[JI]=hn(A[JI],B);if(A[L00]!=null)Q[L00]=okQ(A[L00],B);if(A[Uz]!=null)Q[Uz]=aB.strictParseInt32(A[Uz]);return Q},"de_GetFederationTokenResponse"),B_Q=_A((A,B)=>{let Q={};if(A[JI]!=null)Q[JI]=hn(A[JI],B);return Q},"de_GetSessionTokenResponse"),Q_Q=_A((A,B)=>{let Q={};if(A[CD]!=null)Q[CD]=aB.expectString(A[CD]);return Q},"de_IDPCommunicationErrorException"),D_Q=_A((A,B)=>{let Q={};if(A[CD]!=null)Q[CD]=aB.expectString(A[CD]);return Q},"de_IDPRejectedClaimException"),Z_Q=_A((A,B)=>{let Q={};if(A[CD]!=null)Q[CD]=aB.expectString(A[CD]);return Q},"de_InvalidAuthorizationMessageException"),G_Q=_A((A,B)=>{let Q={};if(A[CD]!=null)Q[CD]=aB.expectString(A[CD]);return Q},"de_InvalidIdentityTokenException"),F_Q=_A((A,B)=>{let Q={};if(A[CD]!=null)Q[CD]=aB.expectString(A[CD]);return Q},"de_MalformedPolicyDocumentException"),I_Q=_A((A,B)=>{let Q={};if(A[CD]!=null)Q[CD]=aB.expectString(A[CD]);return Q},"de_PackedPolicyTooLargeException"),Y_Q=_A((A,B)=>{let Q={};if(A[CD]!=null)Q[CD]=aB.expectString(A[CD]);return Q},"de_RegionDisabledException"),xY=_A((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),W_Q=aB.withBaseException(aN),QT=_A(async(A,B,Q,D,Z)=>{let{hostname:G,protocol:F="https",port:I,path:Y}=await A.endpoint(),W={protocol:F,hostname:G,port:I,method:"POST",path:Y.endsWith("/")?Y.slice(0,-1)+Q:Y+Q,headers:B};if(D!==void 0)W.hostname=D;if(Z!==void 0)W.body=Z;return new GkQ.HttpRequest(W)},"buildHttpRpcRequest"),DT={"content-type":"application/x-www-form-urlencoded"},ZT="2011-06-15",GT="Action",yn="AccessKeyId",J_Q="AssumeRole",E00="AssumedRoleId",rO="AssumedRoleUser",X_Q="AssumeRoleWithSAML",V_Q="AssumeRoleWithWebIdentity",C_Q="AssumeRoot",kn="Account",oO="Arn",_n="Audience",JI="Credentials",U00="ContextAssertion",K_Q="DecodeAuthorizationMessage",w00="DecodedMessage",XI="DurationSeconds",$00="Expiration",q00="ExternalId",N00="EncodedMessage",L00="FederatedUser",M00="FederatedUserId",H_Q="GetAccessKeyInfo",z_Q="GetCallerIdentity",E_Q="GetFederationToken",U_Q="GetSessionToken",R00="Issuer",O00="Key",T00="Name",P00="NameQualifier",zz="Policy",Ez="PolicyArns",S00="PrincipalArn",j00="ProviderArn",y00="ProvidedContexts",k00="ProviderId",Uz="PackedPolicySize",_00="Provider",tO="RoleArn",xn="RoleSessionName",x00="Subject",v00="SecretAccessKey",b00="SAMLAssertion",f00="SubjectFromWebIdentityToken",oJ="SourceIdentity",vn="SerialNumber",h00="SubjectType",g00="SessionToken",bn="Tags",fn="TokenCode",u00="TargetPrincipal",sPA="TaskPolicyArn",m00="TransitiveTagKeys",d00="UserId",FT="Version",c00="Value",l00="WebIdentityToken",p00="arn",CD="message",IT=_A((A)=>Object.entries(A).map(([B,Q])=>aB.extendedEncodeURIComponent(B)+"="+aB.extendedEncodeURIComponent(Q)).join("&"),"buildFormUrlencodedString"),w_Q=_A((A,B)=>{if(B.Error?.Code!==void 0)return B.Error.Code;if(A.statusCode==404)return"NotFound"},"loadQueryErrorCode"),r00=class extends aB.Command.classBuilder().ep(ZkQ.commonParams).m(function(A,B,Q,D){return[AT.getSerdePlugin(Q,this.serialize,this.deserialize),eO.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRole",{}).n("STSClient","AssumeRoleCommand").f(void 0,GSA).ser(FkQ).de(HkQ).build(){static{_A(this,"AssumeRoleCommand")}},$_Q=Yz(),HSA=class extends aB.Command.classBuilder().ep($_Q.commonParams).m(function(A,B,Q,D){return[AT.getSerdePlugin(Q,this.serialize,this.deserialize),eO.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRoleWithSAML",{}).n("STSClient","AssumeRoleWithSAMLCommand").f(FSA,ISA).ser(IkQ).de(zkQ).build(){static{_A(this,"AssumeRoleWithSAMLCommand")}},q_Q=Yz(),o00=class extends aB.Command.classBuilder().ep(q_Q.commonParams).m(function(A,B,Q,D){return[AT.getSerdePlugin(Q,this.serialize,this.deserialize),eO.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRoleWithWebIdentity",{}).n("STSClient","AssumeRoleWithWebIdentityCommand").f(YSA,WSA).ser(YkQ).de(EkQ).build(){static{_A(this,"AssumeRoleWithWebIdentityCommand")}},N_Q=Yz(),zSA=class extends aB.Command.classBuilder().ep(N_Q.commonParams).m(function(A,B,Q,D){return[AT.getSerdePlugin(Q,this.serialize,this.deserialize),eO.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRoot",{}).n("STSClient","AssumeRootCommand").f(void 0,JSA).ser(WkQ).de(UkQ).build(){static{_A(this,"AssumeRootCommand")}},L_Q=Yz(),ESA=class extends aB.Command.classBuilder().ep(L_Q.commonParams).m(function(A,B,Q,D){return[AT.getSerdePlugin(Q,this.serialize,this.deserialize),eO.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","DecodeAuthorizationMessage",{}).n("STSClient","DecodeAuthorizationMessageCommand").f(void 0,void 0).ser(JkQ).de(wkQ).build(){static{_A(this,"DecodeAuthorizationMessageCommand")}},M_Q=Yz(),USA=class extends aB.Command.classBuilder().ep(M_Q.commonParams).m(function(A,B,Q,D){return[AT.getSerdePlugin(Q,this.serialize,this.deserialize),eO.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","GetAccessKeyInfo",{}).n("STSClient","GetAccessKeyInfoCommand").f(void 0,void 0).ser(XkQ).de($kQ).build(){static{_A(this,"GetAccessKeyInfoCommand")}},R_Q=Yz(),wSA=class extends aB.Command.classBuilder().ep(R_Q.commonParams).m(function(A,B,Q,D){return[AT.getSerdePlugin(Q,this.serialize,this.deserialize),eO.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","GetCallerIdentity",{}).n("STSClient","GetCallerIdentityCommand").f(void 0,void 0).ser(VkQ).de(qkQ).build(){static{_A(this,"GetCallerIdentityCommand")}},O_Q=Yz(),$SA=class extends aB.Command.classBuilder().ep(O_Q.commonParams).m(function(A,B,Q,D){return[AT.getSerdePlugin(Q,this.serialize,this.deserialize),eO.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","GetFederationToken",{}).n("STSClient","GetFederationTokenCommand").f(void 0,XSA).ser(CkQ).de(NkQ).build(){static{_A(this,"GetFederationTokenCommand")}},T_Q=Yz(),qSA=class extends aB.Command.classBuilder().ep(T_Q.commonParams).m(function(A,B,Q,D){return[AT.getSerdePlugin(Q,this.serialize,this.deserialize),eO.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","GetSessionToken",{}).n("STSClient","GetSessionTokenCommand").f(void 0,VSA).ser(KkQ).de(LkQ).build(){static{_A(this,"GetSessionTokenCommand")}},P_Q=TQ1(),S_Q={AssumeRoleCommand:r00,AssumeRoleWithSAMLCommand:HSA,AssumeRoleWithWebIdentityCommand:o00,AssumeRootCommand:zSA,DecodeAuthorizationMessageCommand:ESA,GetAccessKeyInfoCommand:USA,GetCallerIdentityCommand:wSA,GetFederationTokenCommand:$SA,GetSessionTokenCommand:qSA},NSA=class extends P_Q.STSClient{static{_A(this,"STS")}};aB.createAggregatedClient(S_Q,NSA);var j_Q=Yz(),n00=Zz(),rPA="us-east-1",LSA=_A((A)=>{if(typeof A?.Arn==="string"){let B=A.Arn.split(":");if(B.length>4&&B[4]!=="")return B[4]}return},"getAccountIdFromAssumedRoleUser"),MSA=_A(async(A,B,Q)=>{let D=typeof A==="function"?await A():A,Z=typeof B==="function"?await B():B;return Q?.debug?.("@aws-sdk/client-sts::resolveRegion","accepting first of:",`${D} (provider)`,`${Z} (parent client)`,`${rPA} (STS default)`),D??Z??rPA},"resolveRegion"),y_Q=_A((A,B)=>{let Q,D;return async(Z,G)=>{if(D=Z,!Q){let{logger:J=A?.parentClientConfig?.logger,region:X,requestHandler:V=A?.parentClientConfig?.requestHandler,credentialProviderLogger:C}=A,K=await MSA(X,A?.parentClientConfig?.region,C),H=!RSA(V);Q=new B({profile:A?.parentClientConfig?.profile,credentialDefaultProvider:_A(()=>async()=>D,"credentialDefaultProvider"),region:K,requestHandler:H?V:void 0,logger:J})}let{Credentials:F,AssumedRoleUser:I}=await Q.send(new r00(G));if(!F||!F.AccessKeyId||!F.SecretAccessKey)throw new Error(`Invalid response from STS.assumeRole call with role ${G.RoleArn}`);let Y=LSA(I),W={accessKeyId:F.AccessKeyId,secretAccessKey:F.SecretAccessKey,sessionToken:F.SessionToken,expiration:F.Expiration,...F.CredentialScope&&{credentialScope:F.CredentialScope},...Y&&{accountId:Y}};return n00.setCredentialFeature(W,"CREDENTIALS_STS_ASSUME_ROLE","i"),W}},"getDefaultRoleAssumer"),k_Q=_A((A,B)=>{let Q;return async(D)=>{if(!Q){let{logger:Y=A?.parentClientConfig?.logger,region:W,requestHandler:J=A?.parentClientConfig?.requestHandler,credentialProviderLogger:X}=A,V=await MSA(W,A?.parentClientConfig?.region,X),C=!RSA(J);Q=new B({profile:A?.parentClientConfig?.profile,region:V,requestHandler:C?J:void 0,logger:Y})}let{Credentials:Z,AssumedRoleUser:G}=await Q.send(new o00(D));if(!Z||!Z.AccessKeyId||!Z.SecretAccessKey)throw new Error(`Invalid response from STS.assumeRoleWithWebIdentity call with role ${D.RoleArn}`);let F=LSA(G),I={accessKeyId:Z.AccessKeyId,secretAccessKey:Z.SecretAccessKey,sessionToken:Z.SessionToken,expiration:Z.Expiration,...Z.CredentialScope&&{credentialScope:Z.CredentialScope},...F&&{accountId:F}};if(F)n00.setCredentialFeature(I,"RESOLVED_ACCOUNT_ID","T");return n00.setCredentialFeature(I,"CREDENTIALS_STS_ASSUME_ROLE_WEB_ID","k"),I}},"getDefaultRoleAssumerWithWebIdentity"),RSA=_A((A)=>{return A?.metadata?.handlerProtocol==="h2"},"isH2"),OSA=TQ1(),TSA=_A((A,B)=>{if(!B)return A;else return class Q extends A{static{_A(this,"CustomizableSTSClient")}constructor(D){super(D);for(let Z of B)this.middlewareStack.use(Z)}}},"getCustomizableStsClientCtor"),PSA=_A((A={},B)=>y_Q(A,TSA(OSA.STSClient,B)),"getDefaultRoleAssumer"),SSA=_A((A={},B)=>k_Q(A,TSA(OSA.STSClient,B)),"getDefaultRoleAssumerWithWebIdentity"),__Q=_A((A)=>(B)=>A({roleAssumer:PSA(B),roleAssumerWithWebIdentity:SSA(B),...B}),"decorateDefaultCredentialProvider")});
var k3=E((r75,gVA)=>{var{create:ZIQ,defineProperty:BQ1,getOwnPropertyDescriptor:GIQ,getOwnPropertyNames:FIQ,getPrototypeOf:IIQ}=Object,YIQ=Object.prototype.hasOwnProperty,y3=(A,B)=>BQ1(A,"name",{value:B,configurable:!0}),WIQ=(A,B)=>{for(var Q in B)BQ1(A,Q,{get:B[Q],enumerable:!0})},jVA=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of FIQ(B))if(!YIQ.call(A,Z)&&Z!==Q)BQ1(A,Z,{get:()=>B[Z],enumerable:!(D=GIQ(B,Z))||D.enumerable})}return A},JIQ=(A,B,Q)=>(Q=A!=null?ZIQ(IIQ(A)):{},jVA(B||!A||!A.__esModule?BQ1(Q,"default",{value:A,enumerable:!0}):Q,A)),XIQ=(A)=>jVA(BQ1({},"__esModule",{value:!0}),A),yVA={};WIQ(yVA,{DEFAULT_REQUEST_TIMEOUT:()=>fVA,NodeHttp2Handler:()=>qIQ,NodeHttpHandler:()=>EIQ,streamCollector:()=>LIQ});gVA.exports=XIQ(yVA);var kVA=EVA(),_VA=RVA(),To1=J1("http"),Po1=J1("https"),VIQ=["ECONNRESET","EPIPE","ETIMEDOUT"],xVA=y3((A)=>{let B={};for(let Q of Object.keys(A)){let D=A[Q];B[Q]=Array.isArray(D)?D.join(","):D}return B},"getTransformedHeaders"),HV={setTimeout:(A,B)=>setTimeout(A,B),clearTimeout:(A)=>clearTimeout(A)},OVA=1000,CIQ=y3((A,B,Q=0)=>{if(!Q)return-1;let D=y3((Z)=>{let G=HV.setTimeout(()=>{A.destroy(),B(Object.assign(new Error(`Socket timed out without establishing a connection within ${Q} ms`),{name:"TimeoutError"}))},Q-Z),F=y3((I)=>{if(I?.connecting)I.on("connect",()=>{HV.clearTimeout(G)});else HV.clearTimeout(G)},"doWithSocket");if(A.socket)F(A.socket);else A.on("socket",F)},"registerTimeout");if(Q<2000)return D(0),0;return HV.setTimeout(D.bind(null,OVA),OVA)},"setConnectionTimeout"),KIQ=3000,HIQ=y3((A,{keepAlive:B,keepAliveMsecs:Q},D=KIQ)=>{if(B!==!0)return-1;let Z=y3(()=>{if(A.socket)A.socket.setKeepAlive(B,Q||0);else A.on("socket",(G)=>{G.setKeepAlive(B,Q||0)})},"registerListener");if(D===0)return Z(),0;return HV.setTimeout(Z,D)},"setSocketKeepAlive"),TVA=3000,zIQ=y3((A,B,Q=fVA)=>{let D=y3((Z)=>{let G=Q-Z,F=y3(()=>{A.destroy(),B(Object.assign(new Error(`Connection timed out after ${Q} ms`),{name:"TimeoutError"}))},"onTimeout");if(A.socket)A.socket.setTimeout(G,F),A.on("close",()=>A.socket?.removeListener("timeout",F));else A.setTimeout(G,F)},"registerTimeout");if(0<Q&&Q<6000)return D(0),0;return HV.setTimeout(D.bind(null,Q===0?0:TVA),TVA)},"setSocketTimeout"),vVA=J1("stream"),PVA=6000;async function So1(A,B,Q=PVA){let D=B.headers??{},Z=D.Expect||D.expect,G=-1,F=!0;if(Z==="100-continue")F=await Promise.race([new Promise((I)=>{G=Number(HV.setTimeout(()=>I(!0),Math.max(PVA,Q)))}),new Promise((I)=>{A.on("continue",()=>{HV.clearTimeout(G),I(!0)}),A.on("response",()=>{HV.clearTimeout(G),I(!1)}),A.on("error",()=>{HV.clearTimeout(G),I(!1)})})]);if(F)bVA(A,B.body)}y3(So1,"writeRequestBody");function bVA(A,B){if(B instanceof vVA.Readable){B.pipe(A);return}if(B){if(Buffer.isBuffer(B)||typeof B==="string"){A.end(B);return}let Q=B;if(typeof Q==="object"&&Q.buffer&&typeof Q.byteOffset==="number"&&typeof Q.byteLength==="number"){A.end(Buffer.from(Q.buffer,Q.byteOffset,Q.byteLength));return}A.end(Buffer.from(B));return}A.end()}y3(bVA,"writeBody");var fVA=0,EIQ=class A{constructor(B){this.socketWarningTimestamp=0,this.metadata={handlerProtocol:"http/1.1"},this.configProvider=new Promise((Q,D)=>{if(typeof B==="function")B().then((Z)=>{Q(this.resolveDefaultConfig(Z))}).catch(D);else Q(this.resolveDefaultConfig(B))})}static{y3(this,"NodeHttpHandler")}static create(B){if(typeof B?.handle==="function")return B;return new A(B)}static checkSocketUsage(B,Q,D=console){let{sockets:Z,requests:G,maxSockets:F}=B;if(typeof F!=="number"||F===1/0)return Q;let I=15000;if(Date.now()-I<Q)return Q;if(Z&&G)for(let Y in Z){let W=Z[Y]?.length??0,J=G[Y]?.length??0;if(W>=F&&J>=2*F)return D?.warn?.(`@smithy/node-http-handler:WARN - socket usage at capacity=${W} and ${J} additional requests are enqueued.
See https://docs.aws.amazon.com/sdk-for-javascript/v3/developer-guide/node-configuring-maxsockets.html
or increase socketAcquisitionWarningTimeout=(millis) in the NodeHttpHandler config.`),Date.now()}return Q}resolveDefaultConfig(B){let{requestTimeout:Q,connectionTimeout:D,socketTimeout:Z,socketAcquisitionWarningTimeout:G,httpAgent:F,httpsAgent:I}=B||{},Y=!0,W=50;return{connectionTimeout:D,requestTimeout:Q??Z,socketAcquisitionWarningTimeout:G,httpAgent:(()=>{if(F instanceof To1.Agent||typeof F?.destroy==="function")return F;return new To1.Agent({keepAlive:!0,maxSockets:50,...F})})(),httpsAgent:(()=>{if(I instanceof Po1.Agent||typeof I?.destroy==="function")return I;return new Po1.Agent({keepAlive:!0,maxSockets:50,...I})})(),logger:console}}destroy(){this.config?.httpAgent?.destroy(),this.config?.httpsAgent?.destroy()}async handle(B,{abortSignal:Q}={}){if(!this.config)this.config=await this.configProvider;return new Promise((D,Z)=>{let G=void 0,F=[],I=y3(async(N)=>{await G,F.forEach(HV.clearTimeout),D(N)},"resolve"),Y=y3(async(N)=>{await G,F.forEach(HV.clearTimeout),Z(N)},"reject");if(!this.config)throw new Error("Node HTTP request handler config is not resolved");if(Q?.aborted){let N=new Error("Request aborted");N.name="AbortError",Y(N);return}let W=B.protocol==="https:",J=W?this.config.httpsAgent:this.config.httpAgent;F.push(HV.setTimeout(()=>{this.socketWarningTimestamp=A.checkSocketUsage(J,this.socketWarningTimestamp,this.config.logger)},this.config.socketAcquisitionWarningTimeout??(this.config.requestTimeout??2000)+(this.config.connectionTimeout??1000)));let X=_VA.buildQueryString(B.query||{}),V=void 0;if(B.username!=null||B.password!=null){let N=B.username??"",O=B.password??"";V=`${N}:${O}`}let C=B.path;if(X)C+=`?${X}`;if(B.fragment)C+=`#${B.fragment}`;let K=B.hostname??"";if(K[0]==="["&&K.endsWith("]"))K=B.hostname.slice(1,-1);else K=B.hostname;let H={headers:B.headers,host:K,method:B.method,path:C,port:B.port,agent:J,auth:V},$=(W?Po1.request:To1.request)(H,(N)=>{let O=new kVA.HttpResponse({statusCode:N.statusCode||-1,reason:N.statusMessage,headers:xVA(N.headers),body:N});I({response:O})});if($.on("error",(N)=>{if(VIQ.includes(N.code))Y(Object.assign(N,{name:"TimeoutError"}));else Y(N)}),Q){let N=y3(()=>{$.destroy();let O=new Error("Request aborted");O.name="AbortError",Y(O)},"onAbort");if(typeof Q.addEventListener==="function"){let O=Q;O.addEventListener("abort",N,{once:!0}),$.once("close",()=>O.removeEventListener("abort",N))}else Q.onabort=N}F.push(CIQ($,Y,this.config.connectionTimeout)),F.push(zIQ($,Y,this.config.requestTimeout));let L=H.agent;if(typeof L==="object"&&"keepAlive"in L)F.push(HIQ($,{keepAlive:L.keepAlive,keepAliveMsecs:L.keepAliveMsecs}));G=So1($,B,this.config.requestTimeout).catch((N)=>{return F.forEach(HV.clearTimeout),Z(N)})})}updateHttpClientConfig(B,Q){this.config=void 0,this.configProvider=this.configProvider.then((D)=>{return{...D,[B]:Q}})}httpHandlerConfigs(){return this.config??{}}},SVA=J1("http2"),UIQ=JIQ(J1("http2")),wIQ=class{constructor(A){this.sessions=[],this.sessions=A??[]}static{y3(this,"NodeHttp2ConnectionPool")}poll(){if(this.sessions.length>0)return this.sessions.shift()}offerLast(A){this.sessions.push(A)}contains(A){return this.sessions.includes(A)}remove(A){this.sessions=this.sessions.filter((B)=>B!==A)}[Symbol.iterator](){return this.sessions[Symbol.iterator]()}destroy(A){for(let B of this.sessions)if(B===A){if(!B.destroyed)B.destroy()}}},$IQ=class{constructor(A){if(this.sessionCache=new Map,this.config=A,this.config.maxConcurrency&&this.config.maxConcurrency<=0)throw new RangeError("maxConcurrency must be greater than zero.")}static{y3(this,"NodeHttp2ConnectionManager")}lease(A,B){let Q=this.getUrlString(A),D=this.sessionCache.get(Q);if(D){let I=D.poll();if(I&&!this.config.disableConcurrency)return I}let Z=UIQ.default.connect(Q);if(this.config.maxConcurrency)Z.settings({maxConcurrentStreams:this.config.maxConcurrency},(I)=>{if(I)throw new Error("Fail to set maxConcurrentStreams to "+this.config.maxConcurrency+"when creating new session for "+A.destination.toString())});Z.unref();let G=y3(()=>{Z.destroy(),this.deleteSession(Q,Z)},"destroySessionCb");if(Z.on("goaway",G),Z.on("error",G),Z.on("frameError",G),Z.on("close",()=>this.deleteSession(Q,Z)),B.requestTimeout)Z.setTimeout(B.requestTimeout,G);let F=this.sessionCache.get(Q)||new wIQ;return F.offerLast(Z),this.sessionCache.set(Q,F),Z}deleteSession(A,B){let Q=this.sessionCache.get(A);if(!Q)return;if(!Q.contains(B))return;Q.remove(B),this.sessionCache.set(A,Q)}release(A,B){let Q=this.getUrlString(A);this.sessionCache.get(Q)?.offerLast(B)}destroy(){for(let[A,B]of this.sessionCache){for(let Q of B){if(!Q.destroyed)Q.destroy();B.remove(Q)}this.sessionCache.delete(A)}}setMaxConcurrentStreams(A){if(A&&A<=0)throw new RangeError("maxConcurrentStreams must be greater than zero.");this.config.maxConcurrency=A}setDisableConcurrentStreams(A){this.config.disableConcurrency=A}getUrlString(A){return A.destination.toString()}},qIQ=class A{constructor(B){this.metadata={handlerProtocol:"h2"},this.connectionManager=new $IQ({}),this.configProvider=new Promise((Q,D)=>{if(typeof B==="function")B().then((Z)=>{Q(Z||{})}).catch(D);else Q(B||{})})}static{y3(this,"NodeHttp2Handler")}static create(B){if(typeof B?.handle==="function")return B;return new A(B)}destroy(){this.connectionManager.destroy()}async handle(B,{abortSignal:Q}={}){if(!this.config){if(this.config=await this.configProvider,this.connectionManager.setDisableConcurrentStreams(this.config.disableConcurrentStreams||!1),this.config.maxConcurrentStreams)this.connectionManager.setMaxConcurrentStreams(this.config.maxConcurrentStreams)}let{requestTimeout:D,disableConcurrentStreams:Z}=this.config;return new Promise((G,F)=>{let I=!1,Y=void 0,W=y3(async(f)=>{await Y,G(f)},"resolve"),J=y3(async(f)=>{await Y,F(f)},"reject");if(Q?.aborted){I=!0;let f=new Error("Request aborted");f.name="AbortError",J(f);return}let{hostname:X,method:V,port:C,protocol:K,query:H}=B,z="";if(B.username!=null||B.password!=null){let f=B.username??"",y=B.password??"";z=`${f}:${y}@`}let $=`${K}//${z}${X}${C?`:${C}`:""}`,L={destination:new URL($)},N=this.connectionManager.lease(L,{requestTimeout:this.config?.sessionTimeout,disableConcurrentStreams:Z||!1}),O=y3((f)=>{if(Z)this.destroySession(N);I=!0,J(f)},"rejectWithDestroy"),R=_VA.buildQueryString(H||{}),T=B.path;if(R)T+=`?${R}`;if(B.fragment)T+=`#${B.fragment}`;let j=N.request({...B.headers,[SVA.constants.HTTP2_HEADER_PATH]:T,[SVA.constants.HTTP2_HEADER_METHOD]:V});if(N.ref(),j.on("response",(f)=>{let y=new kVA.HttpResponse({statusCode:f[":status"]||-1,headers:xVA(f),body:j});if(I=!0,W({response:y}),Z)N.close(),this.connectionManager.deleteSession($,N)}),D)j.setTimeout(D,()=>{j.close();let f=new Error(`Stream timed out because of no activity for ${D} ms`);f.name="TimeoutError",O(f)});if(Q){let f=y3(()=>{j.close();let y=new Error("Request aborted");y.name="AbortError",O(y)},"onAbort");if(typeof Q.addEventListener==="function"){let y=Q;y.addEventListener("abort",f,{once:!0}),j.once("close",()=>y.removeEventListener("abort",f))}else Q.onabort=f}j.on("frameError",(f,y,c)=>{O(new Error(`Frame type id ${f} in stream id ${c} has failed with code ${y}.`))}),j.on("error",O),j.on("aborted",()=>{O(new Error(`HTTP/2 stream is abnormally aborted in mid-communication with result code ${j.rstCode}.`))}),j.on("close",()=>{if(N.unref(),Z)N.destroy();if(!I)O(new Error("Unexpected error: http2 request did not get a response"))}),Y=So1(j,B,D)})}updateHttpClientConfig(B,Q){this.config=void 0,this.configProvider=this.configProvider.then((D)=>{return{...D,[B]:Q}})}httpHandlerConfigs(){return this.config??{}}destroySession(B){if(!B.destroyed)B.destroy()}},NIQ=class extends vVA.Writable{constructor(){super(...arguments);this.bufferedBytes=[]}static{y3(this,"Collector")}_write(A,B,Q){this.bufferedBytes.push(A),Q()}},LIQ=y3((A)=>{if(MIQ(A))return hVA(A);return new Promise((B,Q)=>{let D=new NIQ;A.pipe(D),A.on("error",(Z)=>{D.end(),Q(Z)}),D.on("error",Q),D.on("finish",function(){let Z=new Uint8Array(Buffer.concat(this.bufferedBytes));B(Z)})})},"streamCollector"),MIQ=y3((A)=>typeof ReadableStream==="function"&&A instanceof ReadableStream,"isReadableStreamInstance");async function hVA(A){let B=[],Q=A.getReader(),D=!1,Z=0;while(!D){let{done:I,value:Y}=await Q.read();if(Y)B.push(Y),Z+=Y.length;D=I}let G=new Uint8Array(Z),F=0;for(let I of B)G.set(I,F),F+=I.length;return G}y3(hVA,"collectReadableStream")});
var kXA=E((jXA)=>{Object.defineProperty(jXA,"__esModule",{value:!0});jXA.createChecksumStream=void 0;var nGQ=ii(),aGQ=py(),sGQ=SXA(),rGQ=({expectedChecksum:A,checksum:B,source:Q,checksumSourceLocation:D,base64Encoder:Z})=>{var G,F;if(!aGQ.isReadableStream(Q))throw new Error(`@smithy/util-stream: unsupported source type ${(F=(G=Q===null||Q===void 0?void 0:Q.constructor)===null||G===void 0?void 0:G.name)!==null&&F!==void 0?F:Q} in ChecksumStream.`);let I=Z!==null&&Z!==void 0?Z:nGQ.toBase64;if(typeof TransformStream!=="function")throw new Error("@smithy/util-stream: unable to instantiate ChecksumStream because API unavailable: ReadableStream/TransformStream.");let Y=new TransformStream({start(){},async transform(J,X){B.update(J),X.enqueue(J)},async flush(J){let X=await B.digest(),V=I(X);if(A!==V){let C=new Error(`Checksum mismatch: expected "${A}" but received "${V}" in response header "${D}".`);J.error(C)}else J.terminate()}});Q.pipeThrough(Y);let W=Y.readable;return Object.setPrototypeOf(W,sGQ.ChecksumStream.prototype),W};jXA.createChecksumStream=rGQ});
var kqA=E((jqA)=>{Object.defineProperty(jqA,"__esModule",{value:!0});jqA.isStreamingPayload=void 0;var LNQ=J1("stream"),MNQ=(A)=>(A===null||A===void 0?void 0:A.body)instanceof LNQ.Readable||typeof ReadableStream!=="undefined"&&(A===null||A===void 0?void 0:A.body)instanceof ReadableStream;jqA.isStreamingPayload=MNQ});
var lJA=E((E75,cJA)=>{var{defineProperty:NH1,getOwnPropertyDescriptor:SZQ,getOwnPropertyNames:jZQ}=Object,yZQ=Object.prototype.hasOwnProperty,cy=(A,B)=>NH1(A,"name",{value:B,configurable:!0}),kZQ=(A,B)=>{for(var Q in B)NH1(A,Q,{get:B[Q],enumerable:!0})},_ZQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of jZQ(B))if(!yZQ.call(A,Z)&&Z!==Q)NH1(A,Z,{get:()=>B[Z],enumerable:!(D=SZQ(B,Z))||D.enumerable})}return A},xZQ=(A)=>_ZQ(NH1({},"__esModule",{value:!0}),A),gJA={};kZQ(gJA,{Field:()=>fZQ,Fields:()=>hZQ,HttpRequest:()=>gZQ,HttpResponse:()=>uZQ,IHttpRequest:()=>uJA.HttpRequest,getHttpHandlerExtensionConfiguration:()=>vZQ,isValidHostname:()=>dJA,resolveHttpHandlerRuntimeConfig:()=>bZQ});cJA.exports=xZQ(gJA);var vZQ=cy((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),bZQ=cy((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),uJA=hJA(),fZQ=class{static{cy(this,"Field")}constructor({name:A,kind:B=uJA.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},hZQ=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{cy(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},gZQ=class A{static{cy(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=mJA(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function mJA(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}cy(mJA,"cloneQuery");var uZQ=class{static{cy(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function dJA(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}cy(dJA,"isValidHostname")});
var le1=E((ce1)=>{Object.defineProperty(ce1,"__esModule",{value:!0});ce1.fromHttp=void 0;var UMQ=xNA();Object.defineProperty(ce1,"fromHttp",{enumerable:!0,get:function(){return UMQ.fromHttp}})});
var mG=E((HF5,PLA)=>{var{create:KRQ,defineProperty:jQ1,getOwnPropertyDescriptor:HRQ,getOwnPropertyNames:zRQ,getPrototypeOf:ERQ}=Object,URQ=Object.prototype.hasOwnProperty,ee1=(A,B)=>jQ1(A,"name",{value:B,configurable:!0}),wRQ=(A,B)=>{for(var Q in B)jQ1(A,Q,{get:B[Q],enumerable:!0})},OLA=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of zRQ(B))if(!URQ.call(A,Z)&&Z!==Q)jQ1(A,Z,{get:()=>B[Z],enumerable:!(D=HRQ(B,Z))||D.enumerable})}return A},$RQ=(A,B,Q)=>(Q=A!=null?KRQ(ERQ(A)):{},OLA(B||!A||!A.__esModule?jQ1(Q,"default",{value:A,enumerable:!0}):Q,A)),qRQ=(A)=>OLA(jQ1({},"__esModule",{value:!0}),A),TLA={};wRQ(TLA,{resolveDefaultsModeConfig:()=>yRQ});PLA.exports=qRQ(TLA);var NRQ=K4(),LLA=JD(),LRQ=Q9(),MRQ="AWS_EXECUTION_ENV",MLA="AWS_REGION",RLA="AWS_DEFAULT_REGION",RRQ="AWS_EC2_METADATA_DISABLED",ORQ=["in-region","cross-region","mobile","standard","legacy"],TRQ="/latest/meta-data/placement/region",PRQ="AWS_DEFAULTS_MODE",SRQ="defaults_mode",jRQ={environmentVariableSelector:(A)=>{return A[PRQ]},configFileSelector:(A)=>{return A[SRQ]},default:"legacy"},yRQ=ee1(({region:A=LLA.loadConfig(NRQ.NODE_REGION_CONFIG_OPTIONS),defaultsMode:B=LLA.loadConfig(jRQ)}={})=>LRQ.memoize(async()=>{let Q=typeof B==="function"?await B():B;switch(Q?.toLowerCase()){case"auto":return kRQ(A);case"in-region":case"cross-region":case"mobile":case"standard":case"legacy":return Promise.resolve(Q?.toLocaleLowerCase());case void 0:return Promise.resolve("legacy");default:throw new Error(`Invalid parameter for "defaultsMode", expect ${ORQ.join(", ")}, got ${Q}`)}}),"resolveDefaultsModeConfig"),kRQ=ee1(async(A)=>{if(A){let B=typeof A==="function"?await A():A,Q=await _RQ();if(!Q)return"standard";if(B===Q)return"in-region";else return"cross-region"}return"standard"},"resolveNodeDefaultsModeAuto"),_RQ=ee1(async()=>{if(process.env[MRQ]&&(process.env[MLA]||process.env[RLA]))return process.env[MLA]??process.env[RLA];if(!process.env[RRQ])try{let{getInstanceMetadataEndpoint:A,httpRequest:B}=await Promise.resolve().then(()=>$RQ(TF())),Q=await A();return(await B({...Q,path:TRQ})).toString()}catch(A){}},"inferPhysicalRegion")});
var mPA=E((gPA)=>{Object.defineProperty(gPA,"__esModule",{value:!0});gPA.resolveRuntimeExtensions=void 0;var vPA=yQ1(),bPA=sJ(),fPA=g4(),hPA=xPA(),cyQ=(A,B)=>{let Q=Object.assign(vPA.getAwsRegionExtensionConfiguration(A),fPA.getDefaultExtensionConfiguration(A),bPA.getHttpHandlerExtensionConfiguration(A),hPA.getHttpAuthExtensionConfiguration(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,vPA.resolveAwsRegionExtensionConfiguration(Q),fPA.resolveDefaultRuntimeConfig(Q),bPA.resolveHttpHandlerRuntimeConfig(Q),hPA.resolveHttpAuthRuntimeConfig(Q))};gPA.resolveRuntimeExtensions=cyQ});
var o$A=E((s$A)=>{Object.defineProperty(s$A,"__esModule",{value:!0});s$A.default=void 0;var s$Q=r$Q(J1("crypto"));function r$Q(A){return A&&A.__esModule?A:{default:A}}function o$Q(A){if(Array.isArray(A))A=Buffer.from(A);else if(typeof A==="string")A=Buffer.from(A,"utf8");return s$Q.default.createHash("sha1").update(A).digest()}var t$Q=o$Q;s$A.default=t$Q});
var oXA=E((rXA)=>{Object.defineProperty(rXA,"__esModule",{value:!0});rXA.headStream=KFQ;async function KFQ(A,B){var Q;let D=0,Z=[],G=A.getReader(),F=!1;while(!F){let{done:W,value:J}=await G.read();if(J)Z.push(J),D+=(Q=J===null||J===void 0?void 0:J.byteLength)!==null&&Q!==void 0?Q:0;if(D>=B)break;F=W}G.releaseLock();let I=new Uint8Array(Math.min(B,D)),Y=0;for(let W of Z){if(W.byteLength>I.byteLength-Y){I.set(W.subarray(0,I.byteLength-Y),Y);break}else I.set(W,Y);Y+=W.length}return I}});
var py=E((MXA)=>{Object.defineProperty(MXA,"__esModule",{value:!0});MXA.isBlob=MXA.isReadableStream=void 0;var cGQ=(A)=>{var B;return typeof ReadableStream==="function"&&(((B=A===null||A===void 0?void 0:A.constructor)===null||B===void 0?void 0:B.name)===ReadableStream.name||A instanceof ReadableStream)};MXA.isReadableStream=cGQ;var lGQ=(A)=>{var B;return typeof Blob==="function"&&(((B=A===null||A===void 0?void 0:A.constructor)===null||B===void 0?void 0:B.name)===Blob.name||A instanceof Blob)};MXA.isBlob=lGQ});
var qVA=E((a75,$VA)=>{var{defineProperty:xH1,getOwnPropertyDescriptor:cFQ,getOwnPropertyNames:lFQ}=Object,pFQ=Object.prototype.hasOwnProperty,Ro1=(A,B)=>xH1(A,"name",{value:B,configurable:!0}),iFQ=(A,B)=>{for(var Q in B)xH1(A,Q,{get:B[Q],enumerable:!0})},nFQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of lFQ(B))if(!pFQ.call(A,Z)&&Z!==Q)xH1(A,Z,{get:()=>B[Z],enumerable:!(D=cFQ(B,Z))||D.enumerable})}return A},aFQ=(A)=>nFQ(xH1({},"__esModule",{value:!0}),A),UVA={};iFQ(UVA,{escapeUri:()=>wVA,escapeUriPath:()=>rFQ});$VA.exports=aFQ(UVA);var wVA=Ro1((A)=>encodeURIComponent(A).replace(/[!'()*]/g,sFQ),"escapeUri"),sFQ=Ro1((A)=>`%${A.charCodeAt(0).toString(16).toUpperCase()}`,"hexEncode"),rFQ=Ro1((A)=>A.split("/").map(wVA).join("/"),"escapeUriPath")});
var qh=E((N75,QXA)=>{var{defineProperty:RH1,getOwnPropertyDescriptor:aZQ,getOwnPropertyNames:sZQ}=Object,rZQ=Object.prototype.hasOwnProperty,ly=(A,B)=>RH1(A,"name",{value:B,configurable:!0}),oZQ=(A,B)=>{for(var Q in B)RH1(A,Q,{get:B[Q],enumerable:!0})},tZQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of sZQ(B))if(!rZQ.call(A,Z)&&Z!==Q)RH1(A,Z,{get:()=>B[Z],enumerable:!(D=aZQ(B,Z))||D.enumerable})}return A},eZQ=(A)=>tZQ(RH1({},"__esModule",{value:!0}),A),tJA={};oZQ(tJA,{Field:()=>QGQ,Fields:()=>DGQ,HttpRequest:()=>ZGQ,HttpResponse:()=>GGQ,IHttpRequest:()=>eJA.HttpRequest,getHttpHandlerExtensionConfiguration:()=>AGQ,isValidHostname:()=>BXA,resolveHttpHandlerRuntimeConfig:()=>BGQ});QXA.exports=eZQ(tJA);var AGQ=ly((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),BGQ=ly((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),eJA=Eo1(),QGQ=class{static{ly(this,"Field")}constructor({name:A,kind:B=eJA.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},DGQ=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{ly(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},ZGQ=class A{static{ly(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=AXA(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function AXA(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}ly(AXA,"cloneQuery");var GGQ=class{static{ly(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function BXA(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}ly(BXA,"isValidHostname")});
var ry=E((CD5,hN)=>{var{defineProperty:lH1,getOwnPropertyDescriptor:gYQ,getOwnPropertyNames:uYQ}=Object,mYQ=Object.prototype.hasOwnProperty,fo1=(A,B)=>lH1(A,"name",{value:B,configurable:!0}),dYQ=(A,B)=>{for(var Q in B)lH1(A,Q,{get:B[Q],enumerable:!0})},vo1=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of uYQ(B))if(!mYQ.call(A,Z)&&Z!==Q)lH1(A,Z,{get:()=>B[Z],enumerable:!(D=gYQ(B,Z))||D.enumerable})}return A},sy=(A,B,Q)=>(vo1(A,B,"default"),Q&&vo1(Q,B,"default")),cYQ=(A)=>vo1(lH1({},"__esModule",{value:!0}),A),fN={};dYQ(fN,{Uint8ArrayBlobAdapter:()=>bo1});hN.exports=cYQ(fN);var uCA=ii(),mCA=cB();function dCA(A,B="utf-8"){if(B==="base64")return uCA.toBase64(A);return mCA.toUtf8(A)}fo1(dCA,"transformToString");function cCA(A,B){if(B==="base64")return bo1.mutate(uCA.fromBase64(A));return bo1.mutate(mCA.fromUtf8(A))}fo1(cCA,"transformFromString");var bo1=class A extends Uint8Array{static{fo1(this,"Uint8ArrayBlobAdapter")}static fromString(B,Q="utf-8"){switch(typeof B){case"string":return cCA(B,Q);default:throw new Error(`Unsupported conversion from ${typeof B} to Uint8ArrayBlobAdapter.`)}}static mutate(B){return Object.setPrototypeOf(B,A.prototype),B}transformToString(B="utf-8"){return dCA(this,B)}};sy(fN,Lo1(),hN.exports);sy(fN,xXA(),hN.exports);sy(fN,iXA(),hN.exports);sy(fN,sXA(),hN.exports);sy(fN,BVA(),hN.exports);sy(fN,_CA(),hN.exports);sy(fN,gCA(),hN.exports);sy(fN,py(),hN.exports)});
var sHA=E((sD5,aHA)=>{var{defineProperty:Vz1,getOwnPropertyDescriptor:WVQ,getOwnPropertyNames:JVQ}=Object,XVQ=Object.prototype.hasOwnProperty,VVQ=(A,B)=>Vz1(A,"name",{value:B,configurable:!0}),CVQ=(A,B)=>{for(var Q in B)Vz1(A,Q,{get:B[Q],enumerable:!0})},KVQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of JVQ(B))if(!XVQ.call(A,Z)&&Z!==Q)Vz1(A,Z,{get:()=>B[Z],enumerable:!(D=WVQ(B,Z))||D.enumerable})}return A},HVQ=(A)=>KVQ(Vz1({},"__esModule",{value:!0}),A),nHA={};CVQ(nHA,{isArrayBuffer:()=>zVQ});aHA.exports=HVQ(nHA);var zVQ=VVQ((A)=>typeof ArrayBuffer==="function"&&A instanceof ArrayBuffer||Object.prototype.toString.call(A)==="[object ArrayBuffer]","isArrayBuffer")});
var sJ=E((F75,iWA)=>{var{defineProperty:WH1,getOwnPropertyDescriptor:GDQ,getOwnPropertyNames:FDQ}=Object,IDQ=Object.prototype.hasOwnProperty,dy=(A,B)=>WH1(A,"name",{value:B,configurable:!0}),YDQ=(A,B)=>{for(var Q in B)WH1(A,Q,{get:B[Q],enumerable:!0})},WDQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of FDQ(B))if(!IDQ.call(A,Z)&&Z!==Q)WH1(A,Z,{get:()=>B[Z],enumerable:!(D=GDQ(B,Z))||D.enumerable})}return A},JDQ=(A)=>WDQ(WH1({},"__esModule",{value:!0}),A),dWA={};YDQ(dWA,{Field:()=>CDQ,Fields:()=>KDQ,HttpRequest:()=>HDQ,HttpResponse:()=>zDQ,IHttpRequest:()=>cWA.HttpRequest,getHttpHandlerExtensionConfiguration:()=>XDQ,isValidHostname:()=>pWA,resolveHttpHandlerRuntimeConfig:()=>VDQ});iWA.exports=JDQ(dWA);var XDQ=dy((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),VDQ=dy((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),cWA=Ko1(),CDQ=class{static{dy(this,"Field")}constructor({name:A,kind:B=cWA.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},KDQ=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{dy(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},HDQ=class A{static{dy(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=lWA(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function lWA(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}dy(lWA,"cloneQuery");var zDQ=class{static{dy(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function pWA(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}dy(pWA,"isValidHostname")});
var sMA=E((nMA)=>{Object.defineProperty(nMA,"__esModule",{value:!0});nMA.getRuntimeConfig=void 0;var BTQ=Mh(),QTQ=BTQ.__importDefault(Z10()),cMA=WI(),lMA=PQ1(),LE1=K4(),DTQ=gG(),pMA=u4(),xh=JD(),iMA=k3(),ZTQ=uG(),GTQ=sZ(),FTQ=dMA(),ITQ=g4(),YTQ=mG(),WTQ=g4(),JTQ=(A)=>{WTQ.emitWarningIfUnsupportedVersion(process.version);let B=YTQ.resolveDefaultsModeConfig(A),Q=()=>B().then(ITQ.loadConfigsForDefaultMode),D=FTQ.getRuntimeConfig(A);cMA.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile,logger:D.logger};return{...D,...A,runtime:"node",defaultsMode:B,authSchemePreference:A?.authSchemePreference??xh.loadConfig(cMA.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS,Z),bodyLengthChecker:A?.bodyLengthChecker??ZTQ.calculateBodyLength,defaultUserAgentProvider:A?.defaultUserAgentProvider??lMA.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:QTQ.default.version}),maxAttempts:A?.maxAttempts??xh.loadConfig(pMA.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??xh.loadConfig(LE1.NODE_REGION_CONFIG_OPTIONS,{...LE1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:iMA.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??xh.loadConfig({...pMA.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||GTQ.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??DTQ.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??iMA.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??xh.loadConfig(LE1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??xh.loadConfig(LE1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??xh.loadConfig(lMA.NODE_APP_ID_CONFIG_OPTIONS,Z)}};nMA.getRuntimeConfig=JTQ});
var sVA=E((AD5,aVA)=>{var{defineProperty:bH1,getOwnPropertyDescriptor:RIQ,getOwnPropertyNames:OIQ}=Object,TIQ=Object.prototype.hasOwnProperty,fH1=(A,B)=>bH1(A,"name",{value:B,configurable:!0}),PIQ=(A,B)=>{for(var Q in B)bH1(A,Q,{get:B[Q],enumerable:!0})},SIQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of OIQ(B))if(!TIQ.call(A,Z)&&Z!==Q)bH1(A,Z,{get:()=>B[Z],enumerable:!(D=RIQ(B,Z))||D.enumerable})}return A},jIQ=(A)=>SIQ(bH1({},"__esModule",{value:!0}),A),uVA={};PIQ(uVA,{AlgorithmId:()=>lVA,EndpointURLScheme:()=>cVA,FieldPosition:()=>pVA,HttpApiKeyAuthLocation:()=>dVA,HttpAuthLocation:()=>mVA,IniSectionType:()=>iVA,RequestHandlerProtocol:()=>nVA,SMITHY_CONTEXT_KEY:()=>vIQ,getDefaultClientConfiguration:()=>_IQ,resolveDefaultRuntimeConfig:()=>xIQ});aVA.exports=jIQ(uVA);var mVA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(mVA||{}),dVA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(dVA||{}),cVA=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(cVA||{}),lVA=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(lVA||{}),yIQ=fH1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),kIQ=fH1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),_IQ=fH1((A)=>{return yIQ(A)},"getDefaultClientConfiguration"),xIQ=fH1((A)=>{return kIQ(A)},"resolveDefaultRuntimeConfig"),pVA=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(pVA||{}),vIQ="__smithy_context",iVA=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(iVA||{}),nVA=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(nVA||{})});
var sXA=E((nXA)=>{Object.defineProperty(nXA,"__esModule",{value:!0});nXA.getAwsChunkedEncodingStream=void 0;var VFQ=J1("stream"),CFQ=(A,B)=>{let{base64Encoder:Q,bodyLengthChecker:D,checksumAlgorithmFn:Z,checksumLocationName:G,streamHasher:F}=B,I=Q!==void 0&&Z!==void 0&&G!==void 0&&F!==void 0,Y=I?F(Z,A):void 0,W=new VFQ.Readable({read:()=>{}});return A.on("data",(J)=>{let X=D(J)||0;W.push(`${X.toString(16)}\r
`),W.push(J),W.push(`\r
`)}),A.on("end",async()=>{if(W.push(`0\r
`),I){let J=Q(await Y);W.push(`${G}:${J}\r
`),W.push(`\r
`)}W.push(null)}),W};nXA.getAwsChunkedEncodingStream=CFQ});
var sZ=E((bG5,$qA)=>{var{defineProperty:QE1,getOwnPropertyDescriptor:_qQ,getOwnPropertyNames:xqQ}=Object,vqQ=Object.prototype.hasOwnProperty,Tw=(A,B)=>QE1(A,"name",{value:B,configurable:!0}),bqQ=(A,B)=>{for(var Q in B)QE1(A,Q,{get:B[Q],enumerable:!0})},fqQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of xqQ(B))if(!vqQ.call(A,Z)&&Z!==Q)QE1(A,Z,{get:()=>B[Z],enumerable:!(D=_qQ(B,Z))||D.enumerable})}return A},hqQ=(A)=>fqQ(QE1({},"__esModule",{value:!0}),A),CqA={};bqQ(CqA,{AdaptiveRetryStrategy:()=>lqQ,ConfiguredRetryStrategy:()=>pqQ,DEFAULT_MAX_ATTEMPTS:()=>qe1,DEFAULT_RETRY_DELAY_BASE:()=>OQ1,DEFAULT_RETRY_MODE:()=>gqQ,DefaultRateLimiter:()=>HqA,INITIAL_RETRY_TOKENS:()=>Ne1,INVOCATION_ID_HEADER:()=>mqQ,MAXIMUM_RETRY_DELAY:()=>Le1,NO_RETRY_INCREMENT:()=>wqA,REQUEST_HEADER:()=>dqQ,RETRY_COST:()=>EqA,RETRY_MODES:()=>KqA,StandardRetryStrategy:()=>Me1,THROTTLING_RETRY_DELAY_BASE:()=>zqA,TIMEOUT_RETRY_COST:()=>UqA});$qA.exports=hqQ(CqA);var KqA=((A)=>{return A.STANDARD="standard",A.ADAPTIVE="adaptive",A})(KqA||{}),qe1=3,gqQ="standard",uqQ=$e1(),HqA=class A{constructor(B){this.currentCapacity=0,this.enabled=!1,this.lastMaxRate=0,this.measuredTxRate=0,this.requestCount=0,this.lastTimestamp=0,this.timeWindow=0,this.beta=B?.beta??0.7,this.minCapacity=B?.minCapacity??1,this.minFillRate=B?.minFillRate??0.5,this.scaleConstant=B?.scaleConstant??0.4,this.smooth=B?.smooth??0.8;let Q=this.getCurrentTimeInSeconds();this.lastThrottleTime=Q,this.lastTxRateBucket=Math.floor(this.getCurrentTimeInSeconds()),this.fillRate=this.minFillRate,this.maxCapacity=this.minCapacity}static{Tw(this,"DefaultRateLimiter")}static{this.setTimeoutFn=setTimeout}getCurrentTimeInSeconds(){return Date.now()/1000}async getSendToken(){return this.acquireTokenBucket(1)}async acquireTokenBucket(B){if(!this.enabled)return;if(this.refillTokenBucket(),B>this.currentCapacity){let Q=(B-this.currentCapacity)/this.fillRate*1000;await new Promise((D)=>A.setTimeoutFn(D,Q))}this.currentCapacity=this.currentCapacity-B}refillTokenBucket(){let B=this.getCurrentTimeInSeconds();if(!this.lastTimestamp){this.lastTimestamp=B;return}let Q=(B-this.lastTimestamp)*this.fillRate;this.currentCapacity=Math.min(this.maxCapacity,this.currentCapacity+Q),this.lastTimestamp=B}updateClientSendingRate(B){let Q;if(this.updateMeasuredRate(),uqQ.isThrottlingError(B)){let Z=!this.enabled?this.measuredTxRate:Math.min(this.measuredTxRate,this.fillRate);this.lastMaxRate=Z,this.calculateTimeWindow(),this.lastThrottleTime=this.getCurrentTimeInSeconds(),Q=this.cubicThrottle(Z),this.enableTokenBucket()}else this.calculateTimeWindow(),Q=this.cubicSuccess(this.getCurrentTimeInSeconds());let D=Math.min(Q,2*this.measuredTxRate);this.updateTokenBucketRate(D)}calculateTimeWindow(){this.timeWindow=this.getPrecise(Math.pow(this.lastMaxRate*(1-this.beta)/this.scaleConstant,0.3333333333333333))}cubicThrottle(B){return this.getPrecise(B*this.beta)}cubicSuccess(B){return this.getPrecise(this.scaleConstant*Math.pow(B-this.lastThrottleTime-this.timeWindow,3)+this.lastMaxRate)}enableTokenBucket(){this.enabled=!0}updateTokenBucketRate(B){this.refillTokenBucket(),this.fillRate=Math.max(B,this.minFillRate),this.maxCapacity=Math.max(B,this.minCapacity),this.currentCapacity=Math.min(this.currentCapacity,this.maxCapacity)}updateMeasuredRate(){let B=this.getCurrentTimeInSeconds(),Q=Math.floor(B*2)/2;if(this.requestCount++,Q>this.lastTxRateBucket){let D=this.requestCount/(Q-this.lastTxRateBucket);this.measuredTxRate=this.getPrecise(D*this.smooth+this.measuredTxRate*(1-this.smooth)),this.requestCount=0,this.lastTxRateBucket=Q}}getPrecise(B){return parseFloat(B.toFixed(8))}},OQ1=100,Le1=20000,zqA=500,Ne1=500,EqA=5,UqA=10,wqA=1,mqQ="amz-sdk-invocation-id",dqQ="amz-sdk-request",cqQ=Tw(()=>{let A=OQ1;return{computeNextBackoffDelay:Tw((D)=>{return Math.floor(Math.min(Le1,Math.random()*2**D*A))},"computeNextBackoffDelay"),setDelayBase:Tw((D)=>{A=D},"setDelayBase")}},"getDefaultRetryBackoffStrategy"),VqA=Tw(({retryDelay:A,retryCount:B,retryCost:Q})=>{return{getRetryCount:Tw(()=>B,"getRetryCount"),getRetryDelay:Tw(()=>Math.min(Le1,A),"getRetryDelay"),getRetryCost:Tw(()=>Q,"getRetryCost")}},"createDefaultRetryToken"),Me1=class{constructor(A){this.maxAttempts=A,this.mode="standard",this.capacity=Ne1,this.retryBackoffStrategy=cqQ(),this.maxAttemptsProvider=typeof A==="function"?A:async()=>A}static{Tw(this,"StandardRetryStrategy")}async acquireInitialRetryToken(A){return VqA({retryDelay:OQ1,retryCount:0})}async refreshRetryTokenForRetry(A,B){let Q=await this.getMaxAttempts();if(this.shouldRetry(A,B,Q)){let D=B.errorType;this.retryBackoffStrategy.setDelayBase(D==="THROTTLING"?zqA:OQ1);let Z=this.retryBackoffStrategy.computeNextBackoffDelay(A.getRetryCount()),G=B.retryAfterHint?Math.max(B.retryAfterHint.getTime()-Date.now()||0,Z):Z,F=this.getCapacityCost(D);return this.capacity-=F,VqA({retryDelay:G,retryCount:A.getRetryCount()+1,retryCost:F})}throw new Error("No retry token available")}recordSuccess(A){this.capacity=Math.max(Ne1,this.capacity+(A.getRetryCost()??wqA))}getCapacity(){return this.capacity}async getMaxAttempts(){try{return await this.maxAttemptsProvider()}catch(A){return console.warn(`Max attempts provider could not resolve. Using default of ${qe1}`),qe1}}shouldRetry(A,B,Q){return A.getRetryCount()+1<Q&&this.capacity>=this.getCapacityCost(B.errorType)&&this.isRetryableError(B.errorType)}getCapacityCost(A){return A==="TRANSIENT"?UqA:EqA}isRetryableError(A){return A==="THROTTLING"||A==="TRANSIENT"}},lqQ=class{constructor(A,B){this.maxAttemptsProvider=A,this.mode="adaptive";let{rateLimiter:Q}=B??{};this.rateLimiter=Q??new HqA,this.standardRetryStrategy=new Me1(A)}static{Tw(this,"AdaptiveRetryStrategy")}async acquireInitialRetryToken(A){return await this.rateLimiter.getSendToken(),this.standardRetryStrategy.acquireInitialRetryToken(A)}async refreshRetryTokenForRetry(A,B){return this.rateLimiter.updateClientSendingRate(B),this.standardRetryStrategy.refreshRetryTokenForRetry(A,B)}recordSuccess(A){this.rateLimiter.updateClientSendingRate({}),this.standardRetryStrategy.recordSuccess(A)}},pqQ=class extends Me1{static{Tw(this,"ConfiguredRetryStrategy")}constructor(A,B=OQ1){super(typeof A==="function"?A:async()=>A);if(typeof B==="number")this.computeNextBackoffDelay=()=>B;else this.computeNextBackoffDelay=B}async refreshRetryTokenForRetry(A,B){let Q=await super.refreshRetryTokenForRetry(A,B);return Q.getRetryDelay=()=>this.computeNextBackoffDelay(Q.getRetryCount()),Q}}});
var szA=E((nzA)=>{Object.defineProperty(nzA,"__esModule",{value:!0});nzA.toBase64=void 0;var NCQ=YD(),LCQ=cB(),MCQ=(A)=>{let B;if(typeof A==="string")B=LCQ.fromUtf8(A);else B=A;if(typeof B!=="object"||typeof B.byteOffset!=="number"||typeof B.byteLength!=="number")throw new Error("@smithy/util-base64: toBase64 encoder function only accepts string | Uint8Array.");return NCQ.fromArrayBuffer(B.buffer,B.byteOffset,B.byteLength).toString("base64")};nzA.toBase64=MCQ});
var t91=E((J75,oWA)=>{var{defineProperty:XH1,getOwnPropertyDescriptor:EDQ,getOwnPropertyNames:UDQ}=Object,wDQ=Object.prototype.hasOwnProperty,JH1=(A,B)=>XH1(A,"name",{value:B,configurable:!0}),$DQ=(A,B)=>{for(var Q in B)XH1(A,Q,{get:B[Q],enumerable:!0})},qDQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of UDQ(B))if(!wDQ.call(A,Z)&&Z!==Q)XH1(A,Z,{get:()=>B[Z],enumerable:!(D=EDQ(B,Z))||D.enumerable})}return A},NDQ=(A)=>qDQ(XH1({},"__esModule",{value:!0}),A),nWA={};$DQ(nWA,{getHostHeaderPlugin:()=>MDQ,hostHeaderMiddleware:()=>sWA,hostHeaderMiddlewareOptions:()=>rWA,resolveHostHeaderConfig:()=>aWA});oWA.exports=NDQ(nWA);var LDQ=sJ();function aWA(A){return A}JH1(aWA,"resolveHostHeaderConfig");var sWA=JH1((A)=>(B)=>async(Q)=>{if(!LDQ.HttpRequest.isInstance(Q.request))return B(Q);let{request:D}=Q,{handlerProtocol:Z=""}=A.requestHandler.metadata||{};if(Z.indexOf("h2")>=0&&!D.headers[":authority"])delete D.headers.host,D.headers[":authority"]=D.hostname+(D.port?":"+D.port:"");else if(!D.headers.host){let G=D.hostname;if(D.port!=null)G+=`:${D.port}`;D.headers.host=G}return B(Q)},"hostHeaderMiddleware"),rWA={name:"hostHeaderMiddleware",step:"build",priority:"low",tags:["HOST"],override:!0},MDQ=JH1((A)=>({applyToStack:JH1((B)=>{B.add(sWA(A),rWA)},"applyToStack")}),"getHostHeaderPlugin")});
var tUA=E((oZ5,oUA)=>{var{defineProperty:hz1,getOwnPropertyDescriptor:OEQ,getOwnPropertyNames:TEQ}=Object,PEQ=Object.prototype.hasOwnProperty,Dk=(A,B)=>hz1(A,"name",{value:B,configurable:!0}),SEQ=(A,B)=>{for(var Q in B)hz1(A,Q,{get:B[Q],enumerable:!0})},jEQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of TEQ(B))if(!PEQ.call(A,Z)&&Z!==Q)hz1(A,Z,{get:()=>B[Z],enumerable:!(D=OEQ(B,Z))||D.enumerable})}return A},yEQ=(A)=>jEQ(hz1({},"__esModule",{value:!0}),A),nUA={};SEQ(nUA,{Field:()=>xEQ,Fields:()=>vEQ,HttpRequest:()=>bEQ,HttpResponse:()=>fEQ,IHttpRequest:()=>aUA.HttpRequest,getHttpHandlerExtensionConfiguration:()=>kEQ,isValidHostname:()=>rUA,resolveHttpHandlerRuntimeConfig:()=>_EQ});oUA.exports=yEQ(nUA);var kEQ=Dk((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),_EQ=Dk((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),aUA=iUA(),xEQ=class{static{Dk(this,"Field")}constructor({name:A,kind:B=aUA.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},vEQ=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{Dk(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},bEQ=class A{static{Dk(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=sUA(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function sUA(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}Dk(sUA,"cloneQuery");var fEQ=class{static{Dk(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function rUA(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}Dk(rUA,"isValidHostname")});
var ty=E((IZ5,Lz1)=>{var{defineProperty:rzA,getOwnPropertyDescriptor:RCQ,getOwnPropertyNames:OCQ}=Object,TCQ=Object.prototype.hasOwnProperty,St1=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of OCQ(B))if(!TCQ.call(A,Z)&&Z!==Q)rzA(A,Z,{get:()=>B[Z],enumerable:!(D=RCQ(B,Z))||D.enumerable})}return A},ozA=(A,B,Q)=>(St1(A,B,"default"),Q&&St1(Q,B,"default")),PCQ=(A)=>St1(rzA({},"__esModule",{value:!0}),A),jt1={};Lz1.exports=PCQ(jt1);ozA(jt1,izA(),Lz1.exports);ozA(jt1,szA(),Lz1.exports)});
var u$A=E((h$A)=>{Object.defineProperty(h$A,"__esModule",{value:!0});h$A.default=void 0;var h$Q=f$A(Ue1()),g$Q=f$A(b$A());function f$A(A){return A&&A.__esModule?A:{default:A}}var u$Q=h$Q.default("v3",48,g$Q.default),m$Q=u$Q;h$A.default=m$Q});
var u4=E((nG5,nqA)=>{var{defineProperty:ZE1,getOwnPropertyDescriptor:RNQ,getOwnPropertyNames:ONQ}=Object,TNQ=Object.prototype.hasOwnProperty,fG=(A,B)=>ZE1(A,"name",{value:B,configurable:!0}),PNQ=(A,B)=>{for(var Q in B)ZE1(A,Q,{get:B[Q],enumerable:!0})},SNQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of ONQ(B))if(!TNQ.call(A,Z)&&Z!==Q)ZE1(A,Z,{get:()=>B[Z],enumerable:!(D=RNQ(B,Z))||D.enumerable})}return A},jNQ=(A)=>SNQ(ZE1({},"__esModule",{value:!0}),A),xqA={};PNQ(xqA,{AdaptiveRetryStrategy:()=>_NQ,CONFIG_MAX_ATTEMPTS:()=>_e1,CONFIG_RETRY_MODE:()=>mqA,ENV_MAX_ATTEMPTS:()=>ke1,ENV_RETRY_MODE:()=>uqA,NODE_MAX_ATTEMPT_CONFIG_OPTIONS:()=>xNQ,NODE_RETRY_MODE_CONFIG_OPTIONS:()=>bNQ,StandardRetryStrategy:()=>gqA,defaultDelayDecider:()=>bqA,defaultRetryDecider:()=>fqA,getOmitRetryHeadersPlugin:()=>fNQ,getRetryAfterHint:()=>iqA,getRetryPlugin:()=>cNQ,omitRetryHeadersMiddleware:()=>dqA,omitRetryHeadersMiddlewareOptions:()=>cqA,resolveRetryConfig:()=>vNQ,retryMiddleware:()=>lqA,retryMiddlewareOptions:()=>pqA});nqA.exports=jNQ(xqA);var Jn=K$A(),vqA=RQ1(),cD=sZ(),yNQ=fG((A,B)=>{let Q=A,D=B?.noRetryIncrement??cD.NO_RETRY_INCREMENT,Z=B?.retryCost??cD.RETRY_COST,G=B?.timeoutRetryCost??cD.TIMEOUT_RETRY_COST,F=A,I=fG((X)=>X.name==="TimeoutError"?G:Z,"getCapacityAmount"),Y=fG((X)=>I(X)<=F,"hasRetryTokens");return Object.freeze({hasRetryTokens:Y,retrieveRetryTokens:fG((X)=>{if(!Y(X))throw new Error("No retry token available");let V=I(X);return F-=V,V},"retrieveRetryTokens"),releaseRetryTokens:fG((X)=>{F+=X??D,F=Math.min(F,Q)},"releaseRetryTokens")})},"getDefaultRetryQuota"),bqA=fG((A,B)=>Math.floor(Math.min(cD.MAXIMUM_RETRY_DELAY,Math.random()*2**B*A)),"defaultDelayDecider"),Gk=$e1(),fqA=fG((A)=>{if(!A)return!1;return Gk.isRetryableByTrait(A)||Gk.isClockSkewError(A)||Gk.isThrottlingError(A)||Gk.isTransientError(A)},"defaultRetryDecider"),hqA=fG((A)=>{if(A instanceof Error)return A;if(A instanceof Object)return Object.assign(new Error,A);if(typeof A==="string")return new Error(A);return new Error(`AWS SDK error wrapper for ${A}`)},"asSdkError"),gqA=class{constructor(A,B){this.maxAttemptsProvider=A,this.mode=cD.RETRY_MODES.STANDARD,this.retryDecider=B?.retryDecider??fqA,this.delayDecider=B?.delayDecider??bqA,this.retryQuota=B?.retryQuota??yNQ(cD.INITIAL_RETRY_TOKENS)}static{fG(this,"StandardRetryStrategy")}shouldRetry(A,B,Q){return B<Q&&this.retryDecider(A)&&this.retryQuota.hasRetryTokens(A)}async getMaxAttempts(){let A;try{A=await this.maxAttemptsProvider()}catch(B){A=cD.DEFAULT_MAX_ATTEMPTS}return A}async retry(A,B,Q){let D,Z=0,G=0,F=await this.getMaxAttempts(),{request:I}=B;if(Jn.HttpRequest.isInstance(I))I.headers[cD.INVOCATION_ID_HEADER]=vqA.v4();while(!0)try{if(Jn.HttpRequest.isInstance(I))I.headers[cD.REQUEST_HEADER]=`attempt=${Z+1}; max=${F}`;if(Q?.beforeRequest)await Q.beforeRequest();let{response:Y,output:W}=await A(B);if(Q?.afterRequest)Q.afterRequest(Y);return this.retryQuota.releaseRetryTokens(D),W.$metadata.attempts=Z+1,W.$metadata.totalRetryDelay=G,{response:Y,output:W}}catch(Y){let W=hqA(Y);if(Z++,this.shouldRetry(W,Z,F)){D=this.retryQuota.retrieveRetryTokens(W);let J=this.delayDecider(Gk.isThrottlingError(W)?cD.THROTTLING_RETRY_DELAY_BASE:cD.DEFAULT_RETRY_DELAY_BASE,Z),X=kNQ(W.$response),V=Math.max(X||0,J);G+=V,await new Promise((C)=>setTimeout(C,V));continue}if(!W.$metadata)W.$metadata={};throw W.$metadata.attempts=Z,W.$metadata.totalRetryDelay=G,W}}},kNQ=fG((A)=>{if(!Jn.HttpResponse.isInstance(A))return;let B=Object.keys(A.headers).find((G)=>G.toLowerCase()==="retry-after");if(!B)return;let Q=A.headers[B],D=Number(Q);if(!Number.isNaN(D))return D*1000;return new Date(Q).getTime()-Date.now()},"getDelayFromRetryAfterHeader"),_NQ=class extends gqA{static{fG(this,"AdaptiveRetryStrategy")}constructor(A,B){let{rateLimiter:Q,...D}=B??{};super(A,D);this.rateLimiter=Q??new cD.DefaultRateLimiter,this.mode=cD.RETRY_MODES.ADAPTIVE}async retry(A,B){return super.retry(A,B,{beforeRequest:async()=>{return this.rateLimiter.getSendToken()},afterRequest:(Q)=>{this.rateLimiter.updateClientSendingRate(Q)}})}},_qA=J5(),ke1="AWS_MAX_ATTEMPTS",_e1="max_attempts",xNQ={environmentVariableSelector:(A)=>{let B=A[ke1];if(!B)return;let Q=parseInt(B);if(Number.isNaN(Q))throw new Error(`Environment variable ${ke1} mast be a number, got "${B}"`);return Q},configFileSelector:(A)=>{let B=A[_e1];if(!B)return;let Q=parseInt(B);if(Number.isNaN(Q))throw new Error(`Shared config file entry ${_e1} mast be a number, got "${B}"`);return Q},default:cD.DEFAULT_MAX_ATTEMPTS},vNQ=fG((A)=>{let{retryStrategy:B,retryMode:Q,maxAttempts:D}=A,Z=_qA.normalizeProvider(D??cD.DEFAULT_MAX_ATTEMPTS);return Object.assign(A,{maxAttempts:Z,retryStrategy:async()=>{if(B)return B;if(await _qA.normalizeProvider(Q)()===cD.RETRY_MODES.ADAPTIVE)return new cD.AdaptiveRetryStrategy(Z);return new cD.StandardRetryStrategy(Z)}})},"resolveRetryConfig"),uqA="AWS_RETRY_MODE",mqA="retry_mode",bNQ={environmentVariableSelector:(A)=>A[uqA],configFileSelector:(A)=>A[mqA],default:cD.DEFAULT_RETRY_MODE},dqA=fG(()=>(A)=>async(B)=>{let{request:Q}=B;if(Jn.HttpRequest.isInstance(Q))delete Q.headers[cD.INVOCATION_ID_HEADER],delete Q.headers[cD.REQUEST_HEADER];return A(B)},"omitRetryHeadersMiddleware"),cqA={name:"omitRetryHeadersMiddleware",tags:["RETRY","HEADERS","OMIT_RETRY_HEADERS"],relation:"before",toMiddleware:"awsAuthMiddleware",override:!0},fNQ=fG((A)=>({applyToStack:(B)=>{B.addRelativeTo(dqA(),cqA)}}),"getOmitRetryHeadersPlugin"),hNQ=SqA(),gNQ=kqA(),lqA=fG((A)=>(B,Q)=>async(D)=>{let Z=await A.retryStrategy(),G=await A.maxAttempts();if(uNQ(Z)){Z=Z;let F=await Z.acquireInitialRetryToken(Q.partition_id),I=new Error,Y=0,W=0,{request:J}=D,X=Jn.HttpRequest.isInstance(J);if(X)J.headers[cD.INVOCATION_ID_HEADER]=vqA.v4();while(!0)try{if(X)J.headers[cD.REQUEST_HEADER]=`attempt=${Y+1}; max=${G}`;let{response:V,output:C}=await B(D);return Z.recordSuccess(F),C.$metadata.attempts=Y+1,C.$metadata.totalRetryDelay=W,{response:V,output:C}}catch(V){let C=mNQ(V);if(I=hqA(V),X&&gNQ.isStreamingPayload(J))throw(Q.logger instanceof hNQ.NoOpLogger?console:Q.logger)?.warn("An error was encountered in a non-retryable streaming request."),I;try{F=await Z.refreshRetryTokenForRetry(F,C)}catch(H){if(!I.$metadata)I.$metadata={};throw I.$metadata.attempts=Y+1,I.$metadata.totalRetryDelay=W,I}Y=F.getRetryCount();let K=F.getRetryDelay();W+=K,await new Promise((H)=>setTimeout(H,K))}}else{if(Z=Z,Z?.mode)Q.userAgent=[...Q.userAgent||[],["cfg/retry-mode",Z.mode]];return Z.retry(B,D)}},"retryMiddleware"),uNQ=fG((A)=>typeof A.acquireInitialRetryToken!=="undefined"&&typeof A.refreshRetryTokenForRetry!=="undefined"&&typeof A.recordSuccess!=="undefined","isRetryStrategyV2"),mNQ=fG((A)=>{let B={error:A,errorType:dNQ(A)},Q=iqA(A.$response);if(Q)B.retryAfterHint=Q;return B},"getRetryErrorInfo"),dNQ=fG((A)=>{if(Gk.isThrottlingError(A))return"THROTTLING";if(Gk.isTransientError(A))return"TRANSIENT";if(Gk.isServerError(A))return"SERVER_ERROR";return"CLIENT_ERROR"},"getRetryErrorType"),pqA={name:"retryMiddleware",tags:["RETRY"],step:"finalizeRequest",priority:"high",override:!0},cNQ=fG((A)=>({applyToStack:(B)=>{B.add(lqA(A),pqA)}}),"getRetryPlugin"),iqA=fG((A)=>{if(!Jn.HttpResponse.isInstance(A))return;let B=Object.keys(A.headers).find((G)=>G.toLowerCase()==="retry-after");if(!B)return;let Q=A.headers[B],D=Number(Q);if(!Number.isNaN(D))return new Date(D*1000);return new Date(Q)},"getRetryAfterHint")});
var uEA=E((RZ5,gEA)=>{var RHQ=hEA(),OHQ={attributeNamePrefix:"@_",attributesGroupName:!1,textNodeName:"#text",ignoreAttributes:!0,cdataPropName:!1,format:!1,indentBy:"  ",suppressEmptyNode:!1,suppressUnpairedNode:!0,suppressBooleanAttributes:!0,tagValueProcessor:function(A,B){return B},attributeValueProcessor:function(A,B){return B},preserveOrder:!1,commentPropName:!1,unpairedTags:[],entities:[{regex:new RegExp("&","g"),val:"&amp;"},{regex:new RegExp(">","g"),val:"&gt;"},{regex:new RegExp("<","g"),val:"&lt;"},{regex:new RegExp("'","g"),val:"&apos;"},{regex:new RegExp('"',"g"),val:"&quot;"}],processEntities:!0,stopNodes:[],oneListGroup:!1};function Ak(A){if(this.options=Object.assign({},OHQ,A),this.options.ignoreAttributes||this.options.attributesGroupName)this.isAttribute=function(){return!1};else this.attrPrefixLen=this.options.attributeNamePrefix.length,this.isAttribute=SHQ;if(this.processTextOrObjNode=THQ,this.options.format)this.indentate=PHQ,this.tagEndChar=`>
`,this.newLine=`
`;else this.indentate=function(){return""},this.tagEndChar=">",this.newLine=""}Ak.prototype.build=function(A){if(this.options.preserveOrder)return RHQ(A,this.options);else{if(Array.isArray(A)&&this.options.arrayNodeName&&this.options.arrayNodeName.length>1)A={[this.options.arrayNodeName]:A};return this.j2x(A,0).val}};Ak.prototype.j2x=function(A,B){let Q="",D="";for(let Z in A){if(!Object.prototype.hasOwnProperty.call(A,Z))continue;if(typeof A[Z]==="undefined"){if(this.isAttribute(Z))D+=""}else if(A[Z]===null)if(this.isAttribute(Z))D+="";else if(Z[0]==="?")D+=this.indentate(B)+"<"+Z+"?"+this.tagEndChar;else D+=this.indentate(B)+"<"+Z+"/"+this.tagEndChar;else if(A[Z]instanceof Date)D+=this.buildTextValNode(A[Z],Z,"",B);else if(typeof A[Z]!=="object"){let G=this.isAttribute(Z);if(G)Q+=this.buildAttrPairStr(G,""+A[Z]);else if(Z===this.options.textNodeName){let F=this.options.tagValueProcessor(Z,""+A[Z]);D+=this.replaceEntitiesValue(F)}else D+=this.buildTextValNode(A[Z],Z,"",B)}else if(Array.isArray(A[Z])){let G=A[Z].length,F="",I="";for(let Y=0;Y<G;Y++){let W=A[Z][Y];if(typeof W==="undefined");else if(W===null)if(Z[0]==="?")D+=this.indentate(B)+"<"+Z+"?"+this.tagEndChar;else D+=this.indentate(B)+"<"+Z+"/"+this.tagEndChar;else if(typeof W==="object")if(this.options.oneListGroup){let J=this.j2x(W,B+1);if(F+=J.val,this.options.attributesGroupName&&W.hasOwnProperty(this.options.attributesGroupName))I+=J.attrStr}else F+=this.processTextOrObjNode(W,Z,B);else if(this.options.oneListGroup){let J=this.options.tagValueProcessor(Z,W);J=this.replaceEntitiesValue(J),F+=J}else F+=this.buildTextValNode(W,Z,"",B)}if(this.options.oneListGroup)F=this.buildObjectNode(F,Z,I,B);D+=F}else if(this.options.attributesGroupName&&Z===this.options.attributesGroupName){let G=Object.keys(A[Z]),F=G.length;for(let I=0;I<F;I++)Q+=this.buildAttrPairStr(G[I],""+A[Z][G[I]])}else D+=this.processTextOrObjNode(A[Z],Z,B)}return{attrStr:Q,val:D}};Ak.prototype.buildAttrPairStr=function(A,B){if(B=this.options.attributeValueProcessor(A,""+B),B=this.replaceEntitiesValue(B),this.options.suppressBooleanAttributes&&B==="true")return" "+A;else return" "+A+'="'+B+'"'};function THQ(A,B,Q){let D=this.j2x(A,Q+1);if(A[this.options.textNodeName]!==void 0&&Object.keys(A).length===1)return this.buildTextValNode(A[this.options.textNodeName],B,D.attrStr,Q);else return this.buildObjectNode(D.val,B,D.attrStr,Q)}Ak.prototype.buildObjectNode=function(A,B,Q,D){if(A==="")if(B[0]==="?")return this.indentate(D)+"<"+B+Q+"?"+this.tagEndChar;else return this.indentate(D)+"<"+B+Q+this.closeTag(B)+this.tagEndChar;else{let Z="</"+B+this.tagEndChar,G="";if(B[0]==="?")G="?",Z="";if((Q||Q==="")&&A.indexOf("<")===-1)return this.indentate(D)+"<"+B+Q+G+">"+A+Z;else if(this.options.commentPropName!==!1&&B===this.options.commentPropName&&G.length===0)return this.indentate(D)+`<!--${A}-->`+this.newLine;else return this.indentate(D)+"<"+B+Q+G+this.tagEndChar+A+this.indentate(D)+Z}};Ak.prototype.closeTag=function(A){let B="";if(this.options.unpairedTags.indexOf(A)!==-1){if(!this.options.suppressUnpairedNode)B="/"}else if(this.options.suppressEmptyNode)B="/";else B=`></${A}`;return B};Ak.prototype.buildTextValNode=function(A,B,Q,D){if(this.options.cdataPropName!==!1&&B===this.options.cdataPropName)return this.indentate(D)+`<![CDATA[${A}]]>`+this.newLine;else if(this.options.commentPropName!==!1&&B===this.options.commentPropName)return this.indentate(D)+`<!--${A}-->`+this.newLine;else if(B[0]==="?")return this.indentate(D)+"<"+B+Q+"?"+this.tagEndChar;else{let Z=this.options.tagValueProcessor(B,A);if(Z=this.replaceEntitiesValue(Z),Z==="")return this.indentate(D)+"<"+B+Q+this.closeTag(B)+this.tagEndChar;else return this.indentate(D)+"<"+B+Q+">"+Z+"</"+B+this.tagEndChar}};Ak.prototype.replaceEntitiesValue=function(A){if(A&&A.length>0&&this.options.processEntities)for(let B=0;B<this.options.entities.length;B++){let Q=this.options.entities[B];A=A.replace(Q.regex,Q.val)}return A};function PHQ(A){return this.options.indentBy.repeat(A)}function SHQ(A){if(A.startsWith(this.options.attributeNamePrefix)&&A!==this.options.textNodeName)return A.substr(this.attrPrefixLen);else return!1}gEA.exports=Ak});
var uG=E((XF5,oNA)=>{var{defineProperty:CE1,getOwnPropertyDescriptor:aMQ,getOwnPropertyNames:sMQ}=Object,rMQ=Object.prototype.hasOwnProperty,oMQ=(A,B)=>CE1(A,"name",{value:B,configurable:!0}),tMQ=(A,B)=>{for(var Q in B)CE1(A,Q,{get:B[Q],enumerable:!0})},eMQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of sMQ(B))if(!rMQ.call(A,Z)&&Z!==Q)CE1(A,Z,{get:()=>B[Z],enumerable:!(D=aMQ(B,Z))||D.enumerable})}return A},ARQ=(A)=>eMQ(CE1({},"__esModule",{value:!0}),A),rNA={};tMQ(rNA,{calculateBodyLength:()=>BRQ});oNA.exports=ARQ(rNA);var sNA=J1("fs"),BRQ=oMQ((A)=>{if(!A)return 0;if(typeof A==="string")return Buffer.byteLength(A);else if(typeof A.byteLength==="number")return A.byteLength;else if(typeof A.size==="number")return A.size;else if(typeof A.start==="number"&&typeof A.end==="number")return A.end+1-A.start;else if(typeof A.path==="string"||Buffer.isBuffer(A.path))return sNA.lstatSync(A.path).size;else if(typeof A.fd==="number")return sNA.fstatSync(A.fd).size;throw new Error(`Body Length computation failed for ${A}`)},"calculateBodyLength")});
var uN=E((OZ5,mEA)=>{var jHQ=ut1(),yHQ=_EA(),kHQ=uEA();mEA.exports={XMLParser:yHQ,XMLValidator:jHQ,XMLBuilder:kHQ}});
var ut1=E((kKQ)=>{var gt1=Oz1(),LKQ={allowBooleanAttributes:!1,unpairedTags:[]};kKQ.validate=function(A,B){B=Object.assign({},LKQ,B);let Q=[],D=!1,Z=!1;if(A[0]==="\uFEFF")A=A.substr(1);for(let G=0;G<A.length;G++)if(A[G]==="<"&&A[G+1]==="?"){if(G+=2,G=CEA(A,G),G.err)return G}else if(A[G]==="<"){let F=G;if(G++,A[G]==="!"){G=KEA(A,G);continue}else{let I=!1;if(A[G]==="/")I=!0,G++;let Y="";for(;G<A.length&&A[G]!==">"&&A[G]!==" "&&A[G]!=="\t"&&A[G]!==`
`&&A[G]!=="\r";G++)Y+=A[G];if(Y=Y.trim(),Y[Y.length-1]==="/")Y=Y.substring(0,Y.length-1),G--;if(!yKQ(Y)){let X;if(Y.trim().length===0)X="Invalid space after '<'.";else X="Tag '"+Y+"' is an invalid name.";return aZ("InvalidTag",X,rJ(A,G))}let W=OKQ(A,G);if(W===!1)return aZ("InvalidAttr","Attributes for '"+Y+"' have open quote.",rJ(A,G));let J=W.value;if(G=W.index,J[J.length-1]==="/"){let X=G-J.length;J=J.substring(0,J.length-1);let V=HEA(J,B);if(V===!0)D=!0;else return aZ(V.err.code,V.err.msg,rJ(A,X+V.err.line))}else if(I)if(!W.tagClosed)return aZ("InvalidTag","Closing tag '"+Y+"' doesn't have proper closing.",rJ(A,G));else if(J.trim().length>0)return aZ("InvalidTag","Closing tag '"+Y+"' can't have attributes or invalid starting.",rJ(A,F));else if(Q.length===0)return aZ("InvalidTag","Closing tag '"+Y+"' has not been opened.",rJ(A,F));else{let X=Q.pop();if(Y!==X.tagName){let V=rJ(A,X.tagStartPos);return aZ("InvalidTag","Expected closing tag '"+X.tagName+"' (opened in line "+V.line+", col "+V.col+") instead of closing tag '"+Y+"'.",rJ(A,F))}if(Q.length==0)Z=!0}else{let X=HEA(J,B);if(X!==!0)return aZ(X.err.code,X.err.msg,rJ(A,G-J.length+X.err.line));if(Z===!0)return aZ("InvalidXml","Multiple possible root nodes found.",rJ(A,G));else if(B.unpairedTags.indexOf(Y)!==-1);else Q.push({tagName:Y,tagStartPos:F});D=!0}for(G++;G<A.length;G++)if(A[G]==="<")if(A[G+1]==="!"){G++,G=KEA(A,G);continue}else if(A[G+1]==="?"){if(G=CEA(A,++G),G.err)return G}else break;else if(A[G]==="&"){let X=SKQ(A,G);if(X==-1)return aZ("InvalidChar","char '&' is not expected.",rJ(A,G));G=X}else if(Z===!0&&!VEA(A[G]))return aZ("InvalidXml","Extra text at the end",rJ(A,G));if(A[G]==="<")G--}}else{if(VEA(A[G]))continue;return aZ("InvalidChar","char '"+A[G]+"' is not expected.",rJ(A,G))}if(!D)return aZ("InvalidXml","Start tag expected.",1);else if(Q.length==1)return aZ("InvalidTag","Unclosed tag '"+Q[0].tagName+"'.",rJ(A,Q[0].tagStartPos));else if(Q.length>0)return aZ("InvalidXml","Invalid '"+JSON.stringify(Q.map((G)=>G.tagName),null,4).replace(/\r?\n/g,"")+"' found.",{line:1,col:1});return!0};function VEA(A){return A===" "||A==="\t"||A===`
`||A==="\r"}function CEA(A,B){let Q=B;for(;B<A.length;B++)if(A[B]=="?"||A[B]==" "){let D=A.substr(Q,B-Q);if(B>5&&D==="xml")return aZ("InvalidXml","XML declaration allowed only at the start of the document.",rJ(A,B));else if(A[B]=="?"&&A[B+1]==">"){B++;break}else continue}return B}function KEA(A,B){if(A.length>B+5&&A[B+1]==="-"&&A[B+2]==="-"){for(B+=3;B<A.length;B++)if(A[B]==="-"&&A[B+1]==="-"&&A[B+2]===">"){B+=2;break}}else if(A.length>B+8&&A[B+1]==="D"&&A[B+2]==="O"&&A[B+3]==="C"&&A[B+4]==="T"&&A[B+5]==="Y"&&A[B+6]==="P"&&A[B+7]==="E"){let Q=1;for(B+=8;B<A.length;B++)if(A[B]==="<")Q++;else if(A[B]===">"){if(Q--,Q===0)break}}else if(A.length>B+9&&A[B+1]==="["&&A[B+2]==="C"&&A[B+3]==="D"&&A[B+4]==="A"&&A[B+5]==="T"&&A[B+6]==="A"&&A[B+7]==="["){for(B+=8;B<A.length;B++)if(A[B]==="]"&&A[B+1]==="]"&&A[B+2]===">"){B+=2;break}}return B}var MKQ='"',RKQ="'";function OKQ(A,B){let Q="",D="",Z=!1;for(;B<A.length;B++){if(A[B]===MKQ||A[B]===RKQ)if(D==="")D=A[B];else if(D!==A[B]);else D="";else if(A[B]===">"){if(D===""){Z=!0;break}}Q+=A[B]}if(D!=="")return!1;return{value:Q,index:B,tagClosed:Z}}var TKQ=new RegExp(`(\\s*)([^\\s=]+)(\\s*=)?(\\s*(['"])(([\\s\\S])*?)\\5)?`,"g");function HEA(A,B){let Q=gt1.getAllMatches(A,TKQ),D={};for(let Z=0;Z<Q.length;Z++){if(Q[Z][1].length===0)return aZ("InvalidAttr","Attribute '"+Q[Z][2]+"' has no space in starting.",CQ1(Q[Z]));else if(Q[Z][3]!==void 0&&Q[Z][4]===void 0)return aZ("InvalidAttr","Attribute '"+Q[Z][2]+"' is without value.",CQ1(Q[Z]));else if(Q[Z][3]===void 0&&!B.allowBooleanAttributes)return aZ("InvalidAttr","boolean attribute '"+Q[Z][2]+"' is not allowed.",CQ1(Q[Z]));let G=Q[Z][2];if(!jKQ(G))return aZ("InvalidAttr","Attribute '"+G+"' is an invalid name.",CQ1(Q[Z]));if(!D.hasOwnProperty(G))D[G]=1;else return aZ("InvalidAttr","Attribute '"+G+"' is repeated.",CQ1(Q[Z]))}return!0}function PKQ(A,B){let Q=/\d/;if(A[B]==="x")B++,Q=/[\da-fA-F]/;for(;B<A.length;B++){if(A[B]===";")return B;if(!A[B].match(Q))break}return-1}function SKQ(A,B){if(B++,A[B]===";")return-1;if(A[B]==="#")return B++,PKQ(A,B);let Q=0;for(;B<A.length;B++,Q++){if(A[B].match(/\w/)&&Q<20)continue;if(A[B]===";")break;return-1}return B}function aZ(A,B,Q){return{err:{code:A,msg:B,line:Q.line||Q,col:Q.col}}}function jKQ(A){return gt1.isName(A)}function yKQ(A){return gt1.isName(A)}function rJ(A,B){let Q=A.substring(0,B).split(/\r?\n/);return{line:Q.length,col:Q[Q.length-1].length+1}}function CQ1(A){return A.startIndex+A[1].length}});
var vCA=E((xCA)=>{Object.defineProperty(xCA,"__esModule",{value:!0});xCA.splitStream=xYQ;async function xYQ(A){if(typeof A.stream==="function")A=A.stream();return A.tee()}});
var vLA=E((_LA)=>{Object.defineProperty(_LA,"__esModule",{value:!0});_LA.getRuntimeConfig=void 0;var xRQ=Mh(),vRQ=xRQ.__importDefault(fNA()),SLA=WI(),jLA=PQ1(),KE1=K4(),bRQ=gG(),yLA=u4(),_h=JD(),kLA=k3(),fRQ=uG(),hRQ=sZ(),gRQ=NLA(),uRQ=g4(),mRQ=mG(),dRQ=g4(),cRQ=(A)=>{dRQ.emitWarningIfUnsupportedVersion(process.version);let B=mRQ.resolveDefaultsModeConfig(A),Q=()=>B().then(uRQ.loadConfigsForDefaultMode),D=gRQ.getRuntimeConfig(A);SLA.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile,logger:D.logger};return{...D,...A,runtime:"node",defaultsMode:B,authSchemePreference:A?.authSchemePreference??_h.loadConfig(SLA.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS,Z),bodyLengthChecker:A?.bodyLengthChecker??fRQ.calculateBodyLength,defaultUserAgentProvider:A?.defaultUserAgentProvider??jLA.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:vRQ.default.version}),maxAttempts:A?.maxAttempts??_h.loadConfig(yLA.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??_h.loadConfig(KE1.NODE_REGION_CONFIG_OPTIONS,{...KE1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:kLA.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??_h.loadConfig({...yLA.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||hRQ.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??bRQ.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??kLA.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??_h.loadConfig(KE1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??_h.loadConfig(KE1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??_h.loadConfig(jLA.NODE_APP_ID_CONFIG_OPTIONS,Z)}};_LA.getRuntimeConfig=cRQ});
var ve1=E((sqA)=>{Object.defineProperty(sqA,"__esModule",{value:!0});sqA.resolveHttpAuthSchemeConfig=sqA.resolveStsAuthConfig=sqA.defaultSTSHttpAuthSchemeProvider=sqA.defaultSTSHttpAuthSchemeParametersProvider=void 0;var lNQ=WI(),xe1=J5(),pNQ=TQ1(),iNQ=async(A,B,Q)=>{return{operation:xe1.getSmithyContext(B).operation,region:await xe1.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};sqA.defaultSTSHttpAuthSchemeParametersProvider=iNQ;function nNQ(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"sts",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function aqA(A){return{schemeId:"smithy.api#noAuth"}}var aNQ=(A)=>{let B=[];switch(A.operation){case"AssumeRoleWithSAML":{B.push(aqA(A));break}case"AssumeRoleWithWebIdentity":{B.push(aqA(A));break}default:B.push(nNQ(A))}return B};sqA.defaultSTSHttpAuthSchemeProvider=aNQ;var sNQ=(A)=>Object.assign(A,{stsClientCtor:pNQ.STSClient});sqA.resolveStsAuthConfig=sNQ;var rNQ=(A)=>{let B=sqA.resolveStsAuthConfig(A),Q=lNQ.resolveAwsSdkSigV4Config(B);return Object.assign(Q,{authSchemePreference:xe1.normalizeProvider(A.authSchemePreference??[])})};sqA.resolveHttpAuthSchemeConfig=rNQ});
var wPA=E((EPA)=>{Object.defineProperty(EPA,"__esModule",{value:!0});EPA.defaultEndpointResolver=void 0;var UyQ=Bn(),C00=S7(),wyQ=zPA(),$yQ=new C00.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS","UseGlobalEndpoint"]}),qyQ=(A,B={})=>{return $yQ.get(A,()=>C00.resolveEndpoint(wyQ.ruleSet,{endpointParams:A,logger:B.logger}))};EPA.defaultEndpointResolver=qyQ;C00.customEndpointFunctions.aws=UyQ.awsEndpointFunctions});
var xMA=E((kMA)=>{Object.defineProperty(kMA,"__esModule",{value:!0});kMA.ruleSet=void 0;var PMA="required",Cz="fn",Kz="argv",Un="ref",UMA=!0,wMA="isSet",xQ1="booleanEquals",zn="error",En="endpoint",pO="tree",G10="PartitionResult",F10="getAttr",$MA={[PMA]:!1,type:"String"},qMA={[PMA]:!0,default:!1,type:"Boolean"},NMA={[Un]:"Endpoint"},SMA={[Cz]:xQ1,[Kz]:[{[Un]:"UseFIPS"},!0]},jMA={[Cz]:xQ1,[Kz]:[{[Un]:"UseDualStack"},!0]},Vz={},LMA={[Cz]:F10,[Kz]:[{[Un]:G10},"supportsFIPS"]},yMA={[Un]:G10},MMA={[Cz]:xQ1,[Kz]:[!0,{[Cz]:F10,[Kz]:[yMA,"supportsDualStack"]}]},RMA=[SMA],OMA=[jMA],TMA=[{[Un]:"Region"}],cOQ={version:"1.0",parameters:{Region:$MA,UseDualStack:qMA,UseFIPS:qMA,Endpoint:$MA},rules:[{conditions:[{[Cz]:wMA,[Kz]:[NMA]}],rules:[{conditions:RMA,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:zn},{conditions:OMA,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:zn},{endpoint:{url:NMA,properties:Vz,headers:Vz},type:En}],type:pO},{conditions:[{[Cz]:wMA,[Kz]:TMA}],rules:[{conditions:[{[Cz]:"aws.partition",[Kz]:TMA,assign:G10}],rules:[{conditions:[SMA,jMA],rules:[{conditions:[{[Cz]:xQ1,[Kz]:[UMA,LMA]},MMA],rules:[{endpoint:{url:"https://oidc-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:Vz,headers:Vz},type:En}],type:pO},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:zn}],type:pO},{conditions:RMA,rules:[{conditions:[{[Cz]:xQ1,[Kz]:[LMA,UMA]}],rules:[{conditions:[{[Cz]:"stringEquals",[Kz]:[{[Cz]:F10,[Kz]:[yMA,"name"]},"aws-us-gov"]}],endpoint:{url:"https://oidc.{Region}.amazonaws.com",properties:Vz,headers:Vz},type:En},{endpoint:{url:"https://oidc-fips.{Region}.{PartitionResult#dnsSuffix}",properties:Vz,headers:Vz},type:En}],type:pO},{error:"FIPS is enabled but this partition does not support FIPS",type:zn}],type:pO},{conditions:OMA,rules:[{conditions:[MMA],rules:[{endpoint:{url:"https://oidc.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:Vz,headers:Vz},type:En}],type:pO},{error:"DualStack is enabled but this partition does not support DualStack",type:zn}],type:pO},{endpoint:{url:"https://oidc.{Region}.{PartitionResult#dnsSuffix}",properties:Vz,headers:Vz},type:En}],type:pO}],type:pO},{error:"Invalid Configuration: Missing Region",type:zn}]};kMA.ruleSet=cOQ});
var xNA=E((kNA)=>{Object.defineProperty(kNA,"__esModule",{value:!0});kNA.fromHttp=void 0;var FMQ=Mh(),IMQ=Zz(),YMQ=k3(),jNA=Q9(),WMQ=FMQ.__importDefault(J1("fs/promises")),JMQ=MNA(),yNA=ONA(),XMQ=SNA(),VMQ="AWS_CONTAINER_CREDENTIALS_RELATIVE_URI",CMQ="http://*************",KMQ="AWS_CONTAINER_CREDENTIALS_FULL_URI",HMQ="AWS_CONTAINER_AUTHORIZATION_TOKEN_FILE",zMQ="AWS_CONTAINER_AUTHORIZATION_TOKEN",EMQ=(A={})=>{A.logger?.debug("@aws-sdk/credential-provider-http - fromHttp");let B,Q=A.awsContainerCredentialsRelativeUri??process.env[VMQ],D=A.awsContainerCredentialsFullUri??process.env[KMQ],Z=A.awsContainerAuthorizationToken??process.env[zMQ],G=A.awsContainerAuthorizationTokenFile??process.env[HMQ],F=A.logger?.constructor?.name==="NoOpLogger"||!A.logger?console.warn:A.logger.warn;if(Q&&D)F("@aws-sdk/credential-provider-http: you have set both awsContainerCredentialsRelativeUri and awsContainerCredentialsFullUri."),F("awsContainerCredentialsFullUri will take precedence.");if(Z&&G)F("@aws-sdk/credential-provider-http: you have set both awsContainerAuthorizationToken and awsContainerAuthorizationTokenFile."),F("awsContainerAuthorizationToken will take precedence.");if(D)B=D;else if(Q)B=`${CMQ}${Q}`;else throw new jNA.CredentialsProviderError(`No HTTP credential provider host provided.
Set AWS_CONTAINER_CREDENTIALS_FULL_URI or AWS_CONTAINER_CREDENTIALS_RELATIVE_URI.`,{logger:A.logger});let I=new URL(B);JMQ.checkUrl(I,A.logger);let Y=new YMQ.NodeHttpHandler({requestTimeout:A.timeout??1000,connectionTimeout:A.timeout??1000});return XMQ.retryWrapper(async()=>{let W=yNA.createGetRequest(I);if(Z)W.headers.Authorization=Z;else if(G)W.headers.Authorization=(await WMQ.default.readFile(G)).toString();try{let J=await Y.handle(W);return yNA.getCredentials(J.response).then((X)=>IMQ.setCredentialFeature(X,"CREDENTIALS_HTTP","z"))}catch(J){throw new jNA.CredentialsProviderError(String(J),{logger:A.logger})}},A.maxRetries??3,A.timeout??1000)};kNA.fromHttp=EMQ});
var xPA=E((kPA)=>{Object.defineProperty(kPA,"__esModule",{value:!0});kPA.resolveHttpAuthRuntimeConfig=kPA.getHttpAuthExtensionConfiguration=void 0;var uyQ=(A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}};kPA.getHttpAuthExtensionConfiguration=uyQ;var myQ=(A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}};kPA.resolveHttpAuthRuntimeConfig=myQ});
var xXA=E((_XA)=>{Object.defineProperty(_XA,"__esModule",{value:!0});_XA.createChecksumStream=AFQ;var oGQ=py(),tGQ=Lo1(),eGQ=kXA();function AFQ(A){if(typeof ReadableStream==="function"&&oGQ.isReadableStream(A.source))return eGQ.createChecksumStream(A);return new tGQ.ChecksumStream(A)}});
var yOA=E((SOA)=>{Object.defineProperty(SOA,"__esModule",{value:!0});SOA.resolveHttpAuthRuntimeConfig=SOA.getHttpAuthExtensionConfiguration=void 0;var QSQ=(A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}};SOA.getHttpAuthExtensionConfiguration=QSQ;var DSQ=(A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}};SOA.resolveHttpAuthRuntimeConfig=DSQ});
var yPA=E((SPA)=>{Object.defineProperty(SPA,"__esModule",{value:!0});SPA.getRuntimeConfig=void 0;var SyQ=Mh(),jyQ=SyQ.__importDefault(ANA()),K00=WI(),RPA=iTA(),OPA=PQ1(),_E1=K4(),yyQ=CB(),kyQ=gG(),TPA=u4(),hh=JD(),PPA=k3(),_yQ=uG(),xyQ=sZ(),vyQ=MPA(),byQ=g4(),fyQ=mG(),hyQ=g4(),gyQ=(A)=>{hyQ.emitWarningIfUnsupportedVersion(process.version);let B=fyQ.resolveDefaultsModeConfig(A),Q=()=>B().then(byQ.loadConfigsForDefaultMode),D=vyQ.getRuntimeConfig(A);K00.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile,logger:D.logger};return{...D,...A,runtime:"node",defaultsMode:B,authSchemePreference:A?.authSchemePreference??hh.loadConfig(K00.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS,Z),bodyLengthChecker:A?.bodyLengthChecker??_yQ.calculateBodyLength,credentialDefaultProvider:A?.credentialDefaultProvider??RPA.defaultProvider,defaultUserAgentProvider:A?.defaultUserAgentProvider??OPA.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:jyQ.default.version}),httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(G)=>G.getIdentityProvider("aws.auth#sigv4")||(async(F)=>await RPA.defaultProvider(F?.__config||{})()),signer:new K00.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(G)=>G.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new yyQ.NoAuthSigner}],maxAttempts:A?.maxAttempts??hh.loadConfig(TPA.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??hh.loadConfig(_E1.NODE_REGION_CONFIG_OPTIONS,{..._E1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:PPA.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??hh.loadConfig({...TPA.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||xyQ.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??kyQ.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??PPA.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??hh.loadConfig(_E1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??hh.loadConfig(_E1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??hh.loadConfig(OPA.NODE_APP_ID_CONFIG_OPTIONS,Z)}};SPA.getRuntimeConfig=gyQ});
var yQ1=E((EF5,mLA)=>{var{defineProperty:HE1,getOwnPropertyDescriptor:lRQ,getOwnPropertyNames:pRQ}=Object,iRQ=Object.prototype.hasOwnProperty,lN=(A,B)=>HE1(A,"name",{value:B,configurable:!0}),nRQ=(A,B)=>{for(var Q in B)HE1(A,Q,{get:B[Q],enumerable:!0})},aRQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of pRQ(B))if(!iRQ.call(A,Z)&&Z!==Q)HE1(A,Z,{get:()=>B[Z],enumerable:!(D=lRQ(B,Z))||D.enumerable})}return A},sRQ=(A)=>aRQ(HE1({},"__esModule",{value:!0}),A),fLA={};nRQ(fLA,{NODE_REGION_CONFIG_FILE_OPTIONS:()=>eRQ,NODE_REGION_CONFIG_OPTIONS:()=>tRQ,REGION_ENV_NAME:()=>hLA,REGION_INI_NAME:()=>gLA,getAwsRegionExtensionConfiguration:()=>rRQ,resolveAwsRegionExtensionConfiguration:()=>oRQ,resolveRegionConfig:()=>AOQ});mLA.exports=sRQ(fLA);var rRQ=lN((A)=>{return{setRegion(B){A.region=B},region(){return A.region}}},"getAwsRegionExtensionConfiguration"),oRQ=lN((A)=>{return{region:A.region()}},"resolveAwsRegionExtensionConfiguration"),hLA="AWS_REGION",gLA="region",tRQ={environmentVariableSelector:lN((A)=>A[hLA],"environmentVariableSelector"),configFileSelector:lN((A)=>A[gLA],"configFileSelector"),default:lN(()=>{throw new Error("Region is missing")},"default")},eRQ={preferredFile:"credentials"},uLA=lN((A)=>typeof A==="string"&&(A.startsWith("fips-")||A.endsWith("-fips")),"isFipsRegion"),bLA=lN((A)=>uLA(A)?["fips-aws-global","aws-fips"].includes(A)?"us-east-1":A.replace(/fips-(dkr-|prod-)?|-fips/,""):A,"getRealRegion"),AOQ=lN((A)=>{let{region:B,useFipsEndpoint:Q}=A;if(!B)throw new Error("Region is missing");return Object.assign(A,{region:lN(async()=>{if(typeof B==="string")return bLA(B);let D=await B();return bLA(D)},"region"),useFipsEndpoint:lN(async()=>{let D=typeof B==="string"?B:await B();if(uLA(D))return!0;return typeof Q!=="function"?Promise.resolve(!!Q):Q()},"useFipsEndpoint")})},"resolveRegionConfig")});
var zPA=E((KPA)=>{Object.defineProperty(KPA,"__esModule",{value:!0});KPA.ruleSet=void 0;var ZPA="required",z4="type",u8="fn",m8="argv",Vk="ref",nTA=!1,J00=!0,Xk="booleanEquals",kY="stringEquals",GPA="sigv4",FPA="sts",IPA="us-east-1",VD="endpoint",aTA="https://sts.{Region}.{PartitionResult#dnsSuffix}",nN="tree",jn="error",V00="getAttr",sTA={[ZPA]:!1,[z4]:"String"},X00={[ZPA]:!0,default:!1,[z4]:"Boolean"},YPA={[Vk]:"Endpoint"},rTA={[u8]:"isSet",[m8]:[{[Vk]:"Region"}]},_Y={[Vk]:"Region"},oTA={[u8]:"aws.partition",[m8]:[_Y],assign:"PartitionResult"},WPA={[Vk]:"UseFIPS"},JPA={[Vk]:"UseDualStack"},dW={url:"https://sts.amazonaws.com",properties:{authSchemes:[{name:GPA,signingName:FPA,signingRegion:IPA}]},headers:{}},QK={},tTA={conditions:[{[u8]:kY,[m8]:[_Y,"aws-global"]}],[VD]:dW,[z4]:VD},XPA={[u8]:Xk,[m8]:[WPA,!0]},VPA={[u8]:Xk,[m8]:[JPA,!0]},eTA={[u8]:V00,[m8]:[{[Vk]:"PartitionResult"},"supportsFIPS"]},CPA={[Vk]:"PartitionResult"},APA={[u8]:Xk,[m8]:[!0,{[u8]:V00,[m8]:[CPA,"supportsDualStack"]}]},BPA=[{[u8]:"isSet",[m8]:[YPA]}],QPA=[XPA],DPA=[VPA],EyQ={version:"1.0",parameters:{Region:sTA,UseDualStack:X00,UseFIPS:X00,Endpoint:sTA,UseGlobalEndpoint:X00},rules:[{conditions:[{[u8]:Xk,[m8]:[{[Vk]:"UseGlobalEndpoint"},J00]},{[u8]:"not",[m8]:BPA},rTA,oTA,{[u8]:Xk,[m8]:[WPA,nTA]},{[u8]:Xk,[m8]:[JPA,nTA]}],rules:[{conditions:[{[u8]:kY,[m8]:[_Y,"ap-northeast-1"]}],endpoint:dW,[z4]:VD},{conditions:[{[u8]:kY,[m8]:[_Y,"ap-south-1"]}],endpoint:dW,[z4]:VD},{conditions:[{[u8]:kY,[m8]:[_Y,"ap-southeast-1"]}],endpoint:dW,[z4]:VD},{conditions:[{[u8]:kY,[m8]:[_Y,"ap-southeast-2"]}],endpoint:dW,[z4]:VD},tTA,{conditions:[{[u8]:kY,[m8]:[_Y,"ca-central-1"]}],endpoint:dW,[z4]:VD},{conditions:[{[u8]:kY,[m8]:[_Y,"eu-central-1"]}],endpoint:dW,[z4]:VD},{conditions:[{[u8]:kY,[m8]:[_Y,"eu-north-1"]}],endpoint:dW,[z4]:VD},{conditions:[{[u8]:kY,[m8]:[_Y,"eu-west-1"]}],endpoint:dW,[z4]:VD},{conditions:[{[u8]:kY,[m8]:[_Y,"eu-west-2"]}],endpoint:dW,[z4]:VD},{conditions:[{[u8]:kY,[m8]:[_Y,"eu-west-3"]}],endpoint:dW,[z4]:VD},{conditions:[{[u8]:kY,[m8]:[_Y,"sa-east-1"]}],endpoint:dW,[z4]:VD},{conditions:[{[u8]:kY,[m8]:[_Y,IPA]}],endpoint:dW,[z4]:VD},{conditions:[{[u8]:kY,[m8]:[_Y,"us-east-2"]}],endpoint:dW,[z4]:VD},{conditions:[{[u8]:kY,[m8]:[_Y,"us-west-1"]}],endpoint:dW,[z4]:VD},{conditions:[{[u8]:kY,[m8]:[_Y,"us-west-2"]}],endpoint:dW,[z4]:VD},{endpoint:{url:aTA,properties:{authSchemes:[{name:GPA,signingName:FPA,signingRegion:"{Region}"}]},headers:QK},[z4]:VD}],[z4]:nN},{conditions:BPA,rules:[{conditions:QPA,error:"Invalid Configuration: FIPS and custom endpoint are not supported",[z4]:jn},{conditions:DPA,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",[z4]:jn},{endpoint:{url:YPA,properties:QK,headers:QK},[z4]:VD}],[z4]:nN},{conditions:[rTA],rules:[{conditions:[oTA],rules:[{conditions:[XPA,VPA],rules:[{conditions:[{[u8]:Xk,[m8]:[J00,eTA]},APA],rules:[{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:QK,headers:QK},[z4]:VD}],[z4]:nN},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",[z4]:jn}],[z4]:nN},{conditions:QPA,rules:[{conditions:[{[u8]:Xk,[m8]:[eTA,J00]}],rules:[{conditions:[{[u8]:kY,[m8]:[{[u8]:V00,[m8]:[CPA,"name"]},"aws-us-gov"]}],endpoint:{url:"https://sts.{Region}.amazonaws.com",properties:QK,headers:QK},[z4]:VD},{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dnsSuffix}",properties:QK,headers:QK},[z4]:VD}],[z4]:nN},{error:"FIPS is enabled but this partition does not support FIPS",[z4]:jn}],[z4]:nN},{conditions:DPA,rules:[{conditions:[APA],rules:[{endpoint:{url:"https://sts.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:QK,headers:QK},[z4]:VD}],[z4]:nN},{error:"DualStack is enabled but this partition does not support DualStack",[z4]:jn}],[z4]:nN},tTA,{endpoint:{url:aTA,properties:QK,headers:QK},[z4]:VD}],[z4]:nN}],[z4]:nN},{error:"Invalid Configuration: Missing Region",[z4]:jn}]};KPA.ruleSet=EyQ});

// Export all variables
module.exports = {
  $EA,
  $e1,
  $wA,
  ANA,
  AQ1,
  At1,
  BCA,
  BVA,
  Bn,
  BqA,
  C10,
  CB,
  Ce1,
  D00,
  D10,
  D3,
  De1,
  ECA,
  EEA,
  ELA,
  EOA,
  EVA,
  Ee1,
  Eo1,
  F00,
  FKA,
  GCA,
  GXA,
  Ge1,
  H10,
  HMA,
  HQ1,
  In,
  IqA,
  J10,
  J5,
  JD,
  JZ,
  Je1,
  JwA,
  K$A,
  K4,
  KLA,
  KOA,
  Ko1,
  LQ1,
  Lo1,
  M6,
  MEA,
  MJA,
  MNA,
  MPA,
  MQ1,
  Mh,
  Mo1,
  Mw,
  NEA,
  NLA,
  NOA,
  ONA,
  Oz1,
  PEA,
  POA,
  PQ1,
  PTA,
  PY,
  Pt1,
  Q00,
  Q9,
  R6,
  RQ1,
  RUA,
  RVA,
  S7,
  SCA,
  SNA,
  SXA,
  SqA,
  T$A,
  TF,
  TQ1,
  TzA,
  U$A,
  UXA,
  Ue1,
  Ve1,
  WCA,
  WI,
  WKA,
  X6,
  XVA,
  XXA,
  YD,
  YUA,
  Yn,
  Yz,
  Z10,
  ZqA,
  Zz,
  _CA,
  _EA,
  _Q,
  a$A,
  ay,
  b$A,
  bG,
  be1,
  c$A,
  cB,
  cXA,
  cwA,
  dMA,
  e10,
  e91,
  eHA,
  fMA,
  fNA,
  fwA,
  g4,
  gCA,
  gG,
  gQ1,
  hEA,
  hJA,
  hOA,
  hQ1,
  hTA,
  iKA,
  iTA,
  iUA,
  iXA,
  ie1,
  ii,
  izA,
  j3,
  jEA,
  jRA,
  jSA,
  k3,
  kXA,
  kqA,
  lJA,
  le1,
  mG,
  mPA,
  o$A,
  oXA,
  py,
  qVA,
  qh,
  ry,
  sHA,
  sJ,
  sMA,
  sVA,
  sXA,
  sZ,
  szA,
  t91,
  tUA,
  ty,
  u$A,
  u4,
  uEA,
  uG,
  uN,
  ut1,
  vCA,
  vLA,
  ve1,
  wPA,
  xMA,
  xNA,
  xPA,
  xXA,
  yOA,
  yPA,
  yQ1,
  zPA
};
