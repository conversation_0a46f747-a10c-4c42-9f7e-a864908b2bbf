// Package extracted with entry point: KAB
// Contains 5 variables: CAB, FAB, KAB, VAB, XAB

var CAB=E((GV0)=>{Object.defineProperty(GV0,"__esModule",{value:!0});GV0.OTLPLogExporter=void 0;var QN6=VAB();Object.defineProperty(GV0,"OTLPLogExporter",{enumerable:!0,get:function(){return QN6.OTLPLogExporter}})});
var FAB=E((ZAB)=>{Object.defineProperty(ZAB,"__esModule",{value:!0});ZAB.VERSION=void 0;ZAB.VERSION="0.200.0"});
var KAB=E((FV0)=>{Object.defineProperty(FV0,"__esModule",{value:!0});FV0.OTLPLogExporter=void 0;var ZN6=CAB();Object.defineProperty(FV0,"OTLPLogExporter",{enumerable:!0,get:function(){return ZN6.OTLPLogExporter}})});
var VAB=E((ZV0)=>{Object.defineProperty(ZV0,"__esModule",{value:!0});ZV0.OTLPLogExporter=void 0;var AN6=XAB();Object.defineProperty(ZV0,"OTLPLogExporter",{enumerable:!0,get:function(){return AN6.OTLPLogExporter}})});
var XAB=E((WAB)=>{Object.defineProperty(WAB,"__esModule",{value:!0});WAB.OTLPLogExporter=void 0;var oq6=xu(),tq6=fu(),IAB=co(),eq6=FAB();class YAB extends oq6.OTLPExporterBase{constructor(A={}){super(IAB.createOtlpHttpExportDelegate(IAB.convertLegacyHttpOptions(A,"LOGS","v1/logs",{"User-Agent":`OTel-OTLP-Exporter-JavaScript/${eq6.VERSION}`,"Content-Type":"application/x-protobuf"}),tq6.ProtobufLogsSerializer))}}WAB.OTLPLogExporter=YAB});

// Export all variables
module.exports = {
  CAB,
  FAB,
  KAB,
  VAB,
  XAB
};
