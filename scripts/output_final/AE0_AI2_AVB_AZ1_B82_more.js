// Package extracted with entry point: xHB
// Contains 113 variables: AE0, AI2, AVB, AZ1, B82, BJ2, BW2, BZ2, C61, C70... (and 103 more)

var AE0=E((kA3,aVB)=>{var{defineProperty:I_1,getOwnPropertyDescriptor:gp6,getOwnPropertyNames:up6}=Object,mp6=Object.prototype.hasOwnProperty,Y_1=(A,B)=>I_1(A,"name",{value:B,configurable:!0}),dp6=(A,B)=>{for(var Q in B)I_1(A,Q,{get:B[Q],enumerable:!0})},cp6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of up6(B))if(!mp6.call(A,Z)&&Z!==Q)I_1(A,Z,{get:()=>B[Z],enumerable:!(D=gp6(B,Z))||D.enumerable})}return A},lp6=(A)=>cp6(I_1({},"__esModule",{value:!0}),A),uVB={};dp6(uVB,{AlgorithmId:()=>lVB,EndpointURLScheme:()=>cVB,FieldPosition:()=>pVB,HttpApiKeyAuthLocation:()=>dVB,HttpAuthLocation:()=>mVB,IniSectionType:()=>iVB,RequestHandlerProtocol:()=>nVB,SMITHY_CONTEXT_KEY:()=>sp6,getDefaultClientConfiguration:()=>np6,resolveDefaultRuntimeConfig:()=>ap6});aVB.exports=lp6(uVB);var mVB=((A)=>{return A.HEADER="header",A.QUERY="query",A})(mVB||{}),dVB=((A)=>{return A.HEADER="header",A.QUERY="query",A})(dVB||{}),cVB=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(cVB||{}),lVB=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(lVB||{}),pp6=Y_1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),ip6=Y_1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),np6=Y_1((A)=>{return pp6(A)},"getDefaultClientConfiguration"),ap6=Y_1((A)=>{return ip6(A)},"resolveDefaultRuntimeConfig"),pVB=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(pVB||{}),sp6="__smithy_context",iVB=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(iVB||{}),nVB=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(nVB||{})});
var AI2=E((tF2)=>{Object.defineProperty(tF2,"__esModule",{value:!0});tF2.getRuntimeConfig=void 0;var JR4=KI(),XR4=CB(),VR4=XZ(),CR4=JZ(),rF2=Y30(),oF2=cB(),KR4=Q30(),HR4=sF2(),zR4=(A)=>{return{apiVersion:"2019-06-10",base64Decoder:A?.base64Decoder??rF2.fromBase64,base64Encoder:A?.base64Encoder??rF2.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??HR4.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??KR4.defaultSSOOIDCHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new JR4.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new XR4.NoAuthSigner}],logger:A?.logger??new VR4.NoOpLogger,serviceId:A?.serviceId??"SSO OIDC",urlParser:A?.urlParser??CR4.parseUrl,utf8Decoder:A?.utf8Decoder??oF2.fromUtf8,utf8Encoder:A?.utf8Encoder??oF2.toUtf8}};tF2.getRuntimeConfig=zR4});
var AVB=E((IA3,eXB)=>{var{defineProperty:tk1,getOwnPropertyDescriptor:Hl6,getOwnPropertyNames:zl6}=Object,El6=Object.prototype.hasOwnProperty,nz0=(A,B)=>tk1(A,"name",{value:B,configurable:!0}),Ul6=(A,B)=>{for(var Q in B)tk1(A,Q,{get:B[Q],enumerable:!0})},wl6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of zl6(B))if(!El6.call(A,Z)&&Z!==Q)tk1(A,Z,{get:()=>B[Z],enumerable:!(D=Hl6(B,Z))||D.enumerable})}return A},$l6=(A)=>wl6(tk1({},"__esModule",{value:!0}),A),rXB={};Ul6(rXB,{fromUtf8:()=>tXB,toUint8Array:()=>ql6,toUtf8:()=>Nl6});eXB.exports=$l6(rXB);var oXB=sXB(),tXB=nz0((A)=>{let B=oXB.fromString(A,"utf8");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength/Uint8Array.BYTES_PER_ELEMENT)},"fromUtf8"),ql6=nz0((A)=>{if(typeof A==="string")return tXB(A);if(ArrayBuffer.isView(A))return new Uint8Array(A.buffer,A.byteOffset,A.byteLength/Uint8Array.BYTES_PER_ELEMENT);return new Uint8Array(A)},"toUint8Array"),Nl6=nz0((A)=>{if(typeof A==="string")return A;if(typeof A!=="object"||typeof A.byteOffset!=="number"||typeof A.byteLength!=="number")throw new Error("@smithy/util-utf8: toUtf8 encoder function only accepts string | Uint8Array.");return oXB.fromArrayBuffer(A.buffer,A.byteOffset,A.byteLength).toString("utf8")},"toUtf8")});
var AZ1=E((_A3,VCB)=>{var{defineProperty:X_1,getOwnPropertyDescriptor:rp6,getOwnPropertyNames:op6}=Object,tp6=Object.prototype.hasOwnProperty,x2=(A,B)=>X_1(A,"name",{value:B,configurable:!0}),ep6=(A,B)=>{for(var Q in B)X_1(A,Q,{get:B[Q],enumerable:!0})},Ai6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of op6(B))if(!tp6.call(A,Z)&&Z!==Q)X_1(A,Z,{get:()=>B[Z],enumerable:!(D=rp6(B,Z))||D.enumerable})}return A},Bi6=(A)=>Ai6(X_1({},"__esModule",{value:!0}),A),rVB={};ep6(rVB,{Client:()=>Qi6,Command:()=>tVB,LazyJsonString:()=>wm,NoOpLogger:()=>ti6,SENSITIVE_STRING:()=>Zi6,ServiceException:()=>gi6,_json:()=>IE0,collectBody:()=>BE0.collectBody,convertMap:()=>ei6,createAggregatedClient:()=>Gi6,dateToUtcString:()=>ZCB,decorateServiceException:()=>GCB,emitWarningIfUnsupportedVersion:()=>ci6,expectBoolean:()=>Ii6,expectByte:()=>FE0,expectFloat32:()=>W_1,expectInt:()=>Wi6,expectInt32:()=>ZE0,expectLong:()=>tD1,expectNonNull:()=>Xi6,expectNumber:()=>oD1,expectObject:()=>eVB,expectShort:()=>GE0,expectString:()=>Vi6,expectUnion:()=>Ci6,extendedEncodeURIComponent:()=>BE0.extendedEncodeURIComponent,getArrayIfSingleItem:()=>ri6,getDefaultClientConfiguration:()=>ai6,getDefaultExtensionConfiguration:()=>ICB,getValueFromTextNode:()=>YCB,handleFloat:()=>zi6,isSerializableHeaderValue:()=>oi6,limitedParseDouble:()=>JE0,limitedParseFloat:()=>Ei6,limitedParseFloat32:()=>Ui6,loadConfigsForDefaultMode:()=>di6,logger:()=>eD1,map:()=>VE0,parseBoolean:()=>Fi6,parseEpochTimestamp:()=>ji6,parseRfc3339DateTime:()=>Li6,parseRfc3339DateTimeWithOffset:()=>Ri6,parseRfc7231DateTime:()=>Si6,quoteHeader:()=>JCB,resolveDefaultRuntimeConfig:()=>si6,resolvedPath:()=>BE0.resolvedPath,serializeDateTime:()=>Gn6,serializeFloat:()=>Zn6,splitEvery:()=>XCB,splitHeader:()=>Fn6,strictParseByte:()=>DCB,strictParseDouble:()=>WE0,strictParseFloat:()=>Ki6,strictParseFloat32:()=>ACB,strictParseInt:()=>wi6,strictParseInt32:()=>$i6,strictParseLong:()=>QCB,strictParseShort:()=>Se,take:()=>An6,throwDefaultError:()=>FCB,withBaseException:()=>ui6});VCB.exports=Bi6(rVB);var oVB=Mw(),Qi6=class{constructor(A){this.config=A,this.middlewareStack=oVB.constructStack()}static{x2(this,"Client")}send(A,B,Q){let D=typeof B!=="function"?B:void 0,Z=typeof B==="function"?B:Q,G=D===void 0&&this.config.cacheMiddleware===!0,F;if(G){if(!this.handlers)this.handlers=new WeakMap;let I=this.handlers;if(I.has(A.constructor))F=I.get(A.constructor);else F=A.resolveMiddleware(this.middlewareStack,this.config,D),I.set(A.constructor,F)}else delete this.handlers,F=A.resolveMiddleware(this.middlewareStack,this.config,D);if(Z)F(A).then((I)=>Z(null,I.output),(I)=>Z(I)).catch(()=>{});else return F(A).then((I)=>I.output)}destroy(){this.config?.requestHandler?.destroy?.(),delete this.handlers}},BE0=M6(),DE0=AE0(),tVB=class{constructor(){this.middlewareStack=oVB.constructStack()}static{x2(this,"Command")}static classBuilder(){return new Di6}resolveMiddlewareWithContext(A,B,Q,{middlewareFn:D,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,smithyContext:Y,additionalContext:W,CommandCtor:J}){for(let H of D.bind(this)(J,A,B,Q))this.middlewareStack.use(H);let X=A.concat(this.middlewareStack),{logger:V}=B,C={logger:V,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,[DE0.SMITHY_CONTEXT_KEY]:{commandInstance:this,...Y},...W},{requestHandler:K}=B;return X.resolve((H)=>K.handle(H.request,Q||{}),C)}},Di6=class{constructor(){this._init=()=>{},this._ep={},this._middlewareFn=()=>[],this._commandName="",this._clientName="",this._additionalContext={},this._smithyContext={},this._inputFilterSensitiveLog=(A)=>A,this._outputFilterSensitiveLog=(A)=>A,this._serializer=null,this._deserializer=null}static{x2(this,"ClassBuilder")}init(A){this._init=A}ep(A){return this._ep=A,this}m(A){return this._middlewareFn=A,this}s(A,B,Q={}){return this._smithyContext={service:A,operation:B,...Q},this}c(A={}){return this._additionalContext=A,this}n(A,B){return this._clientName=A,this._commandName=B,this}f(A=(Q)=>Q,B=(Q)=>Q){return this._inputFilterSensitiveLog=A,this._outputFilterSensitiveLog=B,this}ser(A){return this._serializer=A,this}de(A){return this._deserializer=A,this}build(){let A=this,B;return B=class extends tVB{constructor(...[Q]){super();this.serialize=A._serializer,this.deserialize=A._deserializer,this.input=Q??{},A._init(this)}static{x2(this,"CommandRef")}static getEndpointParameterInstructions(){return A._ep}resolveMiddleware(Q,D,Z){return this.resolveMiddlewareWithContext(Q,D,Z,{CommandCtor:B,middlewareFn:A._middlewareFn,clientName:A._clientName,commandName:A._commandName,inputFilterSensitiveLog:A._inputFilterSensitiveLog,outputFilterSensitiveLog:A._outputFilterSensitiveLog,smithyContext:A._smithyContext,additionalContext:A._additionalContext})}}}},Zi6="***SensitiveInformation***",Gi6=x2((A,B)=>{for(let Q of Object.keys(A)){let D=A[Q],Z=x2(async function(F,I,Y){let W=new D(F);if(typeof I==="function")this.send(W,I);else if(typeof Y==="function"){if(typeof I!=="object")throw new Error(`Expected http options but got ${typeof I}`);this.send(W,I||{},Y)}else return this.send(W,I)},"methodImpl"),G=(Q[0].toLowerCase()+Q.slice(1)).replace(/Command$/,"");B.prototype[G]=Z}},"createAggregatedClient"),Fi6=x2((A)=>{switch(A){case"true":return!0;case"false":return!1;default:throw new Error(`Unable to parse boolean value "${A}"`)}},"parseBoolean"),Ii6=x2((A)=>{if(A===null||A===void 0)return;if(typeof A==="number"){if(A===0||A===1)eD1.warn(J_1(`Expected boolean, got ${typeof A}: ${A}`));if(A===0)return!1;if(A===1)return!0}if(typeof A==="string"){let B=A.toLowerCase();if(B==="false"||B==="true")eD1.warn(J_1(`Expected boolean, got ${typeof A}: ${A}`));if(B==="false")return!1;if(B==="true")return!0}if(typeof A==="boolean")return A;throw new TypeError(`Expected boolean, got ${typeof A}: ${A}`)},"expectBoolean"),oD1=x2((A)=>{if(A===null||A===void 0)return;if(typeof A==="string"){let B=parseFloat(A);if(!Number.isNaN(B)){if(String(B)!==String(A))eD1.warn(J_1(`Expected number but observed string: ${A}`));return B}}if(typeof A==="number")return A;throw new TypeError(`Expected number, got ${typeof A}: ${A}`)},"expectNumber"),Yi6=Math.ceil(340282346638528860000000000000000000000),W_1=x2((A)=>{let B=oD1(A);if(B!==void 0&&!Number.isNaN(B)&&B!==1/0&&B!==-1/0){if(Math.abs(B)>Yi6)throw new TypeError(`Expected 32-bit float, got ${A}`)}return B},"expectFloat32"),tD1=x2((A)=>{if(A===null||A===void 0)return;if(Number.isInteger(A)&&!Number.isNaN(A))return A;throw new TypeError(`Expected integer, got ${typeof A}: ${A}`)},"expectLong"),Wi6=tD1,ZE0=x2((A)=>YE0(A,32),"expectInt32"),GE0=x2((A)=>YE0(A,16),"expectShort"),FE0=x2((A)=>YE0(A,8),"expectByte"),YE0=x2((A,B)=>{let Q=tD1(A);if(Q!==void 0&&Ji6(Q,B)!==Q)throw new TypeError(`Expected ${B}-bit integer, got ${A}`);return Q},"expectSizedInt"),Ji6=x2((A,B)=>{switch(B){case 32:return Int32Array.of(A)[0];case 16:return Int16Array.of(A)[0];case 8:return Int8Array.of(A)[0]}},"castInt"),Xi6=x2((A,B)=>{if(A===null||A===void 0){if(B)throw new TypeError(`Expected a non-null value for ${B}`);throw new TypeError("Expected a non-null value")}return A},"expectNonNull"),eVB=x2((A)=>{if(A===null||A===void 0)return;if(typeof A==="object"&&!Array.isArray(A))return A;let B=Array.isArray(A)?"array":typeof A;throw new TypeError(`Expected object, got ${B}: ${A}`)},"expectObject"),Vi6=x2((A)=>{if(A===null||A===void 0)return;if(typeof A==="string")return A;if(["boolean","number","bigint"].includes(typeof A))return eD1.warn(J_1(`Expected string, got ${typeof A}: ${A}`)),String(A);throw new TypeError(`Expected string, got ${typeof A}: ${A}`)},"expectString"),Ci6=x2((A)=>{if(A===null||A===void 0)return;let B=eVB(A),Q=Object.entries(B).filter(([,D])=>D!=null).map(([D])=>D);if(Q.length===0)throw new TypeError("Unions must have exactly one non-null member. None were found.");if(Q.length>1)throw new TypeError(`Unions must have exactly one non-null member. Keys ${Q} were not null.`);return B},"expectUnion"),WE0=x2((A)=>{if(typeof A=="string")return oD1(ye(A));return oD1(A)},"strictParseDouble"),Ki6=WE0,ACB=x2((A)=>{if(typeof A=="string")return W_1(ye(A));return W_1(A)},"strictParseFloat32"),Hi6=/(-?(?:0|[1-9]\d*)(?:\.\d+)?(?:[eE][+-]?\d+)?)|(-?Infinity)|(NaN)/g,ye=x2((A)=>{let B=A.match(Hi6);if(B===null||B[0].length!==A.length)throw new TypeError("Expected real number, got implicit NaN");return parseFloat(A)},"parseNumber"),JE0=x2((A)=>{if(typeof A=="string")return BCB(A);return oD1(A)},"limitedParseDouble"),zi6=JE0,Ei6=JE0,Ui6=x2((A)=>{if(typeof A=="string")return BCB(A);return W_1(A)},"limitedParseFloat32"),BCB=x2((A)=>{switch(A){case"NaN":return NaN;case"Infinity":return 1/0;case"-Infinity":return-1/0;default:throw new Error(`Unable to parse float value: ${A}`)}},"parseFloatString"),QCB=x2((A)=>{if(typeof A==="string")return tD1(ye(A));return tD1(A)},"strictParseLong"),wi6=QCB,$i6=x2((A)=>{if(typeof A==="string")return ZE0(ye(A));return ZE0(A)},"strictParseInt32"),Se=x2((A)=>{if(typeof A==="string")return GE0(ye(A));return GE0(A)},"strictParseShort"),DCB=x2((A)=>{if(typeof A==="string")return FE0(ye(A));return FE0(A)},"strictParseByte"),J_1=x2((A)=>{return String(new TypeError(A).stack||A).split(`
`).slice(0,5).filter((B)=>!B.includes("stackTraceWarning")).join(`
`)},"stackTraceWarning"),eD1={warn:console.warn},qi6=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],XE0=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function ZCB(A){let B=A.getUTCFullYear(),Q=A.getUTCMonth(),D=A.getUTCDay(),Z=A.getUTCDate(),G=A.getUTCHours(),F=A.getUTCMinutes(),I=A.getUTCSeconds(),Y=Z<10?`0${Z}`:`${Z}`,W=G<10?`0${G}`:`${G}`,J=F<10?`0${F}`:`${F}`,X=I<10?`0${I}`:`${I}`;return`${qi6[D]}, ${Y} ${XE0[Q]} ${B} ${W}:${J}:${X} GMT`}x2(ZCB,"dateToUtcString");var Ni6=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?[zZ]$/),Li6=x2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-3339 date-times must be expressed as strings");let B=Ni6.exec(A);if(!B)throw new TypeError("Invalid RFC-3339 date-time value");let[Q,D,Z,G,F,I,Y,W]=B,J=Se(je(D)),X=SM(Z,"month",1,12),V=SM(G,"day",1,31);return rD1(J,X,V,{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W})},"parseRfc3339DateTime"),Mi6=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?(([-+]\d{2}\:\d{2})|[zZ])$/),Ri6=x2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-3339 date-times must be expressed as strings");let B=Mi6.exec(A);if(!B)throw new TypeError("Invalid RFC-3339 date-time value");let[Q,D,Z,G,F,I,Y,W,J]=B,X=Se(je(D)),V=SM(Z,"month",1,12),C=SM(G,"day",1,31),K=rD1(X,V,C,{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W});if(J.toUpperCase()!="Z")K.setTime(K.getTime()-hi6(J));return K},"parseRfc3339DateTimeWithOffset"),Oi6=new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d{2}) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/),Ti6=new RegExp(/^(?:Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d{2})-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/),Pi6=new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( [1-9]|\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? (\d{4})$/),Si6=x2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-7231 date-times must be expressed as strings");let B=Oi6.exec(A);if(B){let[Q,D,Z,G,F,I,Y,W]=B;return rD1(Se(je(G)),QE0(Z),SM(D,"day",1,31),{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W})}if(B=Ti6.exec(A),B){let[Q,D,Z,G,F,I,Y,W]=B;return _i6(rD1(yi6(G),QE0(Z),SM(D,"day",1,31),{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W}))}if(B=Pi6.exec(A),B){let[Q,D,Z,G,F,I,Y,W]=B;return rD1(Se(je(W)),QE0(D),SM(Z.trimLeft(),"day",1,31),{hours:G,minutes:F,seconds:I,fractionalMilliseconds:Y})}throw new TypeError("Invalid RFC-7231 date-time value")},"parseRfc7231DateTime"),ji6=x2((A)=>{if(A===null||A===void 0)return;let B;if(typeof A==="number")B=A;else if(typeof A==="string")B=WE0(A);else if(typeof A==="object"&&A.tag===1)B=A.value;else throw new TypeError("Epoch timestamps must be expressed as floating point numbers or their string representation");if(Number.isNaN(B)||B===1/0||B===-1/0)throw new TypeError("Epoch timestamps must be valid, non-Infinite, non-NaN numerics");return new Date(Math.round(B*1000))},"parseEpochTimestamp"),rD1=x2((A,B,Q,D)=>{let Z=B-1;return vi6(A,Z,Q),new Date(Date.UTC(A,Z,Q,SM(D.hours,"hour",0,23),SM(D.minutes,"minute",0,59),SM(D.seconds,"seconds",0,60),fi6(D.fractionalMilliseconds)))},"buildDate"),yi6=x2((A)=>{let B=new Date().getUTCFullYear(),Q=Math.floor(B/100)*100+Se(je(A));if(Q<B)return Q+100;return Q},"parseTwoDigitYear"),ki6=1576800000000,_i6=x2((A)=>{if(A.getTime()-new Date().getTime()>ki6)return new Date(Date.UTC(A.getUTCFullYear()-100,A.getUTCMonth(),A.getUTCDate(),A.getUTCHours(),A.getUTCMinutes(),A.getUTCSeconds(),A.getUTCMilliseconds()));return A},"adjustRfc850Year"),QE0=x2((A)=>{let B=XE0.indexOf(A);if(B<0)throw new TypeError(`Invalid month: ${A}`);return B+1},"parseMonthByShortName"),xi6=[31,28,31,30,31,30,31,31,30,31,30,31],vi6=x2((A,B,Q)=>{let D=xi6[B];if(B===1&&bi6(A))D=29;if(Q>D)throw new TypeError(`Invalid day for ${XE0[B]} in ${A}: ${Q}`)},"validateDayOfMonth"),bi6=x2((A)=>{return A%4===0&&(A%100!==0||A%400===0)},"isLeapYear"),SM=x2((A,B,Q,D)=>{let Z=DCB(je(A));if(Z<Q||Z>D)throw new TypeError(`${B} must be between ${Q} and ${D}, inclusive`);return Z},"parseDateValue"),fi6=x2((A)=>{if(A===null||A===void 0)return 0;return ACB("0."+A)*1000},"parseMilliseconds"),hi6=x2((A)=>{let B=A[0],Q=1;if(B=="+")Q=1;else if(B=="-")Q=-1;else throw new TypeError(`Offset direction, ${B}, must be "+" or "-"`);let D=Number(A.substring(1,3)),Z=Number(A.substring(4,6));return Q*(D*60+Z)*60*1000},"parseOffsetToMilliseconds"),je=x2((A)=>{let B=0;while(B<A.length-1&&A.charAt(B)==="0")B++;if(B===0)return A;return A.slice(B)},"stripLeadingZeroes"),gi6=class A extends Error{static{x2(this,"ServiceException")}constructor(B){super(B.message);Object.setPrototypeOf(this,Object.getPrototypeOf(this).constructor.prototype),this.name=B.name,this.$fault=B.$fault,this.$metadata=B.$metadata}static isInstance(B){if(!B)return!1;let Q=B;return A.prototype.isPrototypeOf(Q)||Boolean(Q.$fault)&&Boolean(Q.$metadata)&&(Q.$fault==="client"||Q.$fault==="server")}static[Symbol.hasInstance](B){if(!B)return!1;let Q=B;if(this===A)return A.isInstance(B);if(A.isInstance(B)){if(Q.name&&this.name)return this.prototype.isPrototypeOf(B)||Q.name===this.name;return this.prototype.isPrototypeOf(B)}return!1}},GCB=x2((A,B={})=>{Object.entries(B).filter(([,D])=>D!==void 0).forEach(([D,Z])=>{if(A[D]==null||A[D]==="")A[D]=Z});let Q=A.message||A.Message||"UnknownError";return A.message=Q,delete A.Message,A},"decorateServiceException"),FCB=x2(({output:A,parsedBody:B,exceptionCtor:Q,errorCode:D})=>{let Z=mi6(A),G=Z.httpStatusCode?Z.httpStatusCode+"":void 0,F=new Q({name:B?.code||B?.Code||D||G||"UnknownError",$fault:"client",$metadata:Z});throw GCB(F,B)},"throwDefaultError"),ui6=x2((A)=>{return({output:B,parsedBody:Q,errorCode:D})=>{FCB({output:B,parsedBody:Q,exceptionCtor:A,errorCode:D})}},"withBaseException"),mi6=x2((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),di6=x2((A)=>{switch(A){case"standard":return{retryMode:"standard",connectionTimeout:3100};case"in-region":return{retryMode:"standard",connectionTimeout:1100};case"cross-region":return{retryMode:"standard",connectionTimeout:3100};case"mobile":return{retryMode:"standard",connectionTimeout:30000};default:return{}}},"loadConfigsForDefaultMode"),sVB=!1,ci6=x2((A)=>{if(A&&!sVB&&parseInt(A.substring(1,A.indexOf(".")))<16)sVB=!0},"emitWarningIfUnsupportedVersion"),li6=x2((A)=>{let B=[];for(let Q in DE0.AlgorithmId){let D=DE0.AlgorithmId[Q];if(A[D]===void 0)continue;B.push({algorithmId:()=>D,checksumConstructor:()=>A[D]})}return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),pi6=x2((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),ii6=x2((A)=>{return{setRetryStrategy(B){A.retryStrategy=B},retryStrategy(){return A.retryStrategy}}},"getRetryConfiguration"),ni6=x2((A)=>{let B={};return B.retryStrategy=A.retryStrategy(),B},"resolveRetryRuntimeConfig"),ICB=x2((A)=>{return Object.assign(li6(A),ii6(A))},"getDefaultExtensionConfiguration"),ai6=ICB,si6=x2((A)=>{return Object.assign(pi6(A),ni6(A))},"resolveDefaultRuntimeConfig"),ri6=x2((A)=>Array.isArray(A)?A:[A],"getArrayIfSingleItem"),YCB=x2((A)=>{for(let Q in A)if(A.hasOwnProperty(Q)&&A[Q]["#text"]!==void 0)A[Q]=A[Q]["#text"];else if(typeof A[Q]==="object"&&A[Q]!==null)A[Q]=YCB(A[Q]);return A},"getValueFromTextNode"),oi6=x2((A)=>{return A!=null},"isSerializableHeaderValue"),wm=x2(function A(B){return Object.assign(new String(B),{deserializeJSON(){return JSON.parse(String(B))},toString(){return String(B)},toJSON(){return String(B)}})},"LazyJsonString");wm.from=(A)=>{if(A&&typeof A==="object"&&(A instanceof wm||("deserializeJSON"in A)))return A;else if(typeof A==="string"||Object.getPrototypeOf(A)===String.prototype)return wm(String(A));return wm(JSON.stringify(A))};wm.fromObject=wm.from;var ti6=class{static{x2(this,"NoOpLogger")}trace(){}debug(){}info(){}warn(){}error(){}};function VE0(A,B,Q){let D,Z,G;if(typeof B==="undefined"&&typeof Q==="undefined")D={},G=A;else if(D=A,typeof B==="function")return Z=B,G=Q,Bn6(D,Z,G);else G=B;for(let F of Object.keys(G)){if(!Array.isArray(G[F])){D[F]=G[F];continue}WCB(D,null,G,F)}return D}x2(VE0,"map");var ei6=x2((A)=>{let B={};for(let[Q,D]of Object.entries(A||{}))B[Q]=[,D];return B},"convertMap"),An6=x2((A,B)=>{let Q={};for(let D in B)WCB(Q,A,B,D);return Q},"take"),Bn6=x2((A,B,Q)=>{return VE0(A,Object.entries(Q).reduce((D,[Z,G])=>{if(Array.isArray(G))D[Z]=G;else if(typeof G==="function")D[Z]=[B,G()];else D[Z]=[B,G];return D},{}))},"mapWithFilter"),WCB=x2((A,B,Q,D)=>{if(B!==null){let F=Q[D];if(typeof F==="function")F=[,F];let[I=Qn6,Y=Dn6,W=D]=F;if(typeof I==="function"&&I(B[W])||typeof I!=="function"&&!!I)A[D]=Y(B[W]);return}let[Z,G]=Q[D];if(typeof G==="function"){let F,I=Z===void 0&&(F=G())!=null,Y=typeof Z==="function"&&!!Z(void 0)||typeof Z!=="function"&&!!Z;if(I)A[D]=F;else if(Y)A[D]=G()}else{let F=Z===void 0&&G!=null,I=typeof Z==="function"&&!!Z(G)||typeof Z!=="function"&&!!Z;if(F||I)A[D]=G}},"applyInstruction"),Qn6=x2((A)=>A!=null,"nonNullish"),Dn6=x2((A)=>A,"pass");function JCB(A){if(A.includes(",")||A.includes('"'))A=`"${A.replace(/"/g,"\\\"")}"`;return A}x2(JCB,"quoteHeader");var Zn6=x2((A)=>{if(A!==A)return"NaN";switch(A){case 1/0:return"Infinity";case-1/0:return"-Infinity";default:return A}},"serializeFloat"),Gn6=x2((A)=>A.toISOString().replace(".000Z","Z"),"serializeDateTime"),IE0=x2((A)=>{if(A==null)return{};if(Array.isArray(A))return A.filter((B)=>B!=null).map(IE0);if(typeof A==="object"){let B={};for(let Q of Object.keys(A)){if(A[Q]==null)continue;B[Q]=IE0(A[Q])}return B}return A},"_json");function XCB(A,B,Q){if(Q<=0||!Number.isInteger(Q))throw new Error("Invalid number of delimiters ("+Q+") for splitEvery.");let D=A.split(B);if(Q===1)return D;let Z=[],G="";for(let F=0;F<D.length;F++){if(G==="")G=D[F];else G+=B+D[F];if((F+1)%Q===0)Z.push(G),G=""}if(G!=="")Z.push(G);return Z}x2(XCB,"splitEvery");var Fn6=x2((A)=>{let B=A.length,Q=[],D=!1,Z=void 0,G=0;for(let F=0;F<B;++F){let I=A[F];switch(I){case'"':if(Z!=="\\")D=!D;break;case",":if(!D)Q.push(A.slice(G,F)),G=F+1;break;default:}Z=I}return Q.push(A.slice(G)),Q.map((F)=>{F=F.trim();let I=F.length;if(I<2)return F;if(F[0]==='"'&&F[I-1]==='"')F=F.slice(1,I-1);return F.replace(/\\"/g,'"')})},"splitHeader")});
var B82=E((e62)=>{Object.defineProperty(e62,"__esModule",{value:!0});e62.fromHttp=void 0;var TK4=I62(),PK4=zL(),SK4=k3(),o62=Q9(),jK4=TK4.__importDefault(J1("fs/promises")),yK4=J62(),t62=n62(),kK4=r62(),_K4="AWS_CONTAINER_CREDENTIALS_RELATIVE_URI",xK4="http://*************",vK4="AWS_CONTAINER_CREDENTIALS_FULL_URI",bK4="AWS_CONTAINER_AUTHORIZATION_TOKEN_FILE",fK4="AWS_CONTAINER_AUTHORIZATION_TOKEN",hK4=(A={})=>{A.logger?.debug("@aws-sdk/credential-provider-http - fromHttp");let B,Q=A.awsContainerCredentialsRelativeUri??process.env[_K4],D=A.awsContainerCredentialsFullUri??process.env[vK4],Z=A.awsContainerAuthorizationToken??process.env[fK4],G=A.awsContainerAuthorizationTokenFile??process.env[bK4],F=A.logger?.constructor?.name==="NoOpLogger"||!A.logger?console.warn:A.logger.warn;if(Q&&D)F("@aws-sdk/credential-provider-http: you have set both awsContainerCredentialsRelativeUri and awsContainerCredentialsFullUri."),F("awsContainerCredentialsFullUri will take precedence.");if(Z&&G)F("@aws-sdk/credential-provider-http: you have set both awsContainerAuthorizationToken and awsContainerAuthorizationTokenFile."),F("awsContainerAuthorizationToken will take precedence.");if(D)B=D;else if(Q)B=`${xK4}${Q}`;else throw new o62.CredentialsProviderError(`No HTTP credential provider host provided.
Set AWS_CONTAINER_CREDENTIALS_FULL_URI or AWS_CONTAINER_CREDENTIALS_RELATIVE_URI.`,{logger:A.logger});let I=new URL(B);yK4.checkUrl(I,A.logger);let Y=new SK4.NodeHttpHandler({requestTimeout:A.timeout??1000,connectionTimeout:A.timeout??1000});return kK4.retryWrapper(async()=>{let W=t62.createGetRequest(I);if(Z)W.headers.Authorization=Z;else if(G)W.headers.Authorization=(await jK4.default.readFile(G)).toString();try{let J=await Y.handle(W);return t62.getCredentials(J.response).then((X)=>PK4.setCredentialFeature(X,"CREDENTIALS_HTTP","z"))}catch(J){throw new o62.CredentialsProviderError(String(J),{logger:A.logger})}},A.maxRetries??3,A.timeout??1000)};e62.fromHttp=hK4});
var BJ2=E((RU5,AJ2)=>{var{create:KS4,defineProperty:c61,getOwnPropertyDescriptor:HS4,getOwnPropertyNames:zS4,getPrototypeOf:ES4}=Object,US4=Object.prototype.hasOwnProperty,nG=(A,B)=>c61(A,"name",{value:B,configurable:!0}),wS4=(A,B)=>{for(var Q in B)c61(A,Q,{get:B[Q],enumerable:!0})},oW2=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of zS4(B))if(!US4.call(A,Z)&&Z!==Q)c61(A,Z,{get:()=>B[Z],enumerable:!(D=HS4(B,Z))||D.enumerable})}return A},ok=(A,B,Q)=>(Q=A!=null?KS4(ES4(A)):{},oW2(B||!A||!A.__esModule?c61(Q,"default",{value:A,enumerable:!0}):Q,A)),$S4=(A)=>oW2(c61({},"__esModule",{value:!0}),A),tW2={};wS4(tW2,{fromIni:()=>kS4});AJ2.exports=$S4(tW2);var X70=D3(),tk=zL(),d61=Q9(),qS4=nG((A,B,Q)=>{let D={EcsContainer:nG(async(Z)=>{let{fromHttp:G}=await Promise.resolve().then(()=>ok(g80())),{fromContainerMetadata:F}=await Promise.resolve().then(()=>ok(TF()));return Q?.debug("@aws-sdk/credential-provider-ini - credential_source is EcsContainer"),async()=>d61.chain(G(Z??{}),F(Z))().then(J70)},"EcsContainer"),Ec2InstanceMetadata:nG(async(Z)=>{Q?.debug("@aws-sdk/credential-provider-ini - credential_source is Ec2InstanceMetadata");let{fromInstanceMetadata:G}=await Promise.resolve().then(()=>ok(TF()));return async()=>G(Z)().then(J70)},"Ec2InstanceMetadata"),Environment:nG(async(Z)=>{Q?.debug("@aws-sdk/credential-provider-ini - credential_source is Environment");let{fromEnv:G}=await Promise.resolve().then(()=>ok(N80()));return async()=>G(Z)().then(J70)},"Environment")};if(A in D)return D[A];else throw new d61.CredentialsProviderError(`Unsupported credential source in profile ${B}. Got ${A}, expected EcsContainer or Ec2InstanceMetadata or Environment.`,{logger:Q})},"resolveCredentialSource"),J70=nG((A)=>tk.setCredentialFeature(A,"CREDENTIALS_PROFILE_NAMED_PROVIDER","p"),"setNamedProvider"),NS4=nG((A,{profile:B="default",logger:Q}={})=>{return Boolean(A)&&typeof A==="object"&&typeof A.role_arn==="string"&&["undefined","string"].indexOf(typeof A.role_session_name)>-1&&["undefined","string"].indexOf(typeof A.external_id)>-1&&["undefined","string"].indexOf(typeof A.mfa_serial)>-1&&(LS4(A,{profile:B,logger:Q})||MS4(A,{profile:B,logger:Q}))},"isAssumeRoleProfile"),LS4=nG((A,{profile:B,logger:Q})=>{let D=typeof A.source_profile==="string"&&typeof A.credential_source==="undefined";if(D)Q?.debug?.(`    ${B} isAssumeRoleWithSourceProfile source_profile=${A.source_profile}`);return D},"isAssumeRoleWithSourceProfile"),MS4=nG((A,{profile:B,logger:Q})=>{let D=typeof A.credential_source==="string"&&typeof A.source_profile==="undefined";if(D)Q?.debug?.(`    ${B} isCredentialSourceProfile credential_source=${A.credential_source}`);return D},"isCredentialSourceProfile"),RS4=nG(async(A,B,Q,D={})=>{Q.logger?.debug("@aws-sdk/credential-provider-ini - resolveAssumeRoleCredentials (STS)");let Z=B[A],{source_profile:G,region:F}=Z;if(!Q.roleAssumer){let{getDefaultRoleAssumer:Y}=await Promise.resolve().then(()=>ok(Q70()));Q.roleAssumer=Y({...Q.clientConfig,credentialProviderLogger:Q.logger,parentClientConfig:{...Q?.parentClientConfig,region:F??Q?.parentClientConfig?.region}},Q.clientPlugins)}if(G&&G in D)throw new d61.CredentialsProviderError(`Detected a cycle attempting to resolve credentials for profile ${X70.getProfileName(Q)}. Profiles visited: `+Object.keys(D).join(", "),{logger:Q.logger});Q.logger?.debug(`@aws-sdk/credential-provider-ini - finding credential resolver using ${G?`source_profile=[${G}]`:`profile=[${A}]`}`);let I=G?eW2(G,B,Q,{...D,[G]:!0},aW2(B[G]??{})):(await qS4(Z.credential_source,A,Q.logger)(Q))();if(aW2(Z))return I.then((Y)=>tk.setCredentialFeature(Y,"CREDENTIALS_PROFILE_SOURCE_PROFILE","o"));else{let Y={RoleArn:Z.role_arn,RoleSessionName:Z.role_session_name||`aws-sdk-js-${Date.now()}`,ExternalId:Z.external_id,DurationSeconds:parseInt(Z.duration_seconds||"3600",10)},{mfa_serial:W}=Z;if(W){if(!Q.mfaCodeProvider)throw new d61.CredentialsProviderError(`Profile ${A} requires multi-factor authentication, but no MFA code callback was provided.`,{logger:Q.logger,tryNextLink:!1});Y.SerialNumber=W,Y.TokenCode=await Q.mfaCodeProvider(W)}let J=await I;return Q.roleAssumer(J,Y).then((X)=>tk.setCredentialFeature(X,"CREDENTIALS_PROFILE_SOURCE_PROFILE","o"))}},"resolveAssumeRoleCredentials"),aW2=nG((A)=>{return!A.role_arn&&!!A.credential_source},"isCredentialSourceWithoutRoleArn"),OS4=nG((A)=>Boolean(A)&&typeof A==="object"&&typeof A.credential_process==="string","isProcessProfile"),TS4=nG(async(A,B)=>Promise.resolve().then(()=>ok(G70())).then(({fromProcess:Q})=>Q({...A,profile:B})().then((D)=>tk.setCredentialFeature(D,"CREDENTIALS_PROFILE_PROCESS","v"))),"resolveProcessCredentials"),PS4=nG(async(A,B,Q={})=>{let{fromSSO:D}=await Promise.resolve().then(()=>ok(E30()));return D({profile:A,logger:Q.logger,parentClientConfig:Q.parentClientConfig,clientConfig:Q.clientConfig})().then((Z)=>{if(B.sso_session)return tk.setCredentialFeature(Z,"CREDENTIALS_PROFILE_SSO","r");else return tk.setCredentialFeature(Z,"CREDENTIALS_PROFILE_SSO_LEGACY","t")})},"resolveSsoCredentials"),SS4=nG((A)=>A&&(typeof A.sso_start_url==="string"||typeof A.sso_account_id==="string"||typeof A.sso_session==="string"||typeof A.sso_region==="string"||typeof A.sso_role_name==="string"),"isSsoProfile"),sW2=nG((A)=>Boolean(A)&&typeof A==="object"&&typeof A.aws_access_key_id==="string"&&typeof A.aws_secret_access_key==="string"&&["undefined","string"].indexOf(typeof A.aws_session_token)>-1&&["undefined","string"].indexOf(typeof A.aws_account_id)>-1,"isStaticCredsProfile"),rW2=nG(async(A,B)=>{B?.logger?.debug("@aws-sdk/credential-provider-ini - resolveStaticCredentials");let Q={accessKeyId:A.aws_access_key_id,secretAccessKey:A.aws_secret_access_key,sessionToken:A.aws_session_token,...A.aws_credential_scope&&{credentialScope:A.aws_credential_scope},...A.aws_account_id&&{accountId:A.aws_account_id}};return tk.setCredentialFeature(Q,"CREDENTIALS_PROFILE","n")},"resolveStaticCredentials"),jS4=nG((A)=>Boolean(A)&&typeof A==="object"&&typeof A.web_identity_token_file==="string"&&typeof A.role_arn==="string"&&["undefined","string"].indexOf(typeof A.role_session_name)>-1,"isWebIdentityProfile"),yS4=nG(async(A,B)=>Promise.resolve().then(()=>ok(W70())).then(({fromTokenFile:Q})=>Q({webIdentityTokenFile:A.web_identity_token_file,roleArn:A.role_arn,roleSessionName:A.role_session_name,roleAssumerWithWebIdentity:B.roleAssumerWithWebIdentity,logger:B.logger,parentClientConfig:B.parentClientConfig})().then((D)=>tk.setCredentialFeature(D,"CREDENTIALS_PROFILE_STS_WEB_ID_TOKEN","q"))),"resolveWebIdentityCredentials"),eW2=nG(async(A,B,Q,D={},Z=!1)=>{let G=B[A];if(Object.keys(D).length>0&&sW2(G))return rW2(G,Q);if(Z||NS4(G,{profile:A,logger:Q.logger}))return RS4(A,B,Q,D);if(sW2(G))return rW2(G,Q);if(jS4(G))return yS4(G,Q);if(OS4(G))return TS4(Q,A);if(SS4(G))return await PS4(A,G,Q);throw new d61.CredentialsProviderError(`Could not resolve credentials using profile: [${A}] in configuration/credentials file(s).`,{logger:Q.logger})},"resolveProfileData"),kS4=nG((A={})=>async({callerClientConfig:B}={})=>{let Q={...A,parentClientConfig:{...B,...A.parentClientConfig}};Q.logger?.debug("@aws-sdk/credential-provider-ini - fromIni");let D=await X70.parseKnownFiles(Q);return eW2(X70.getProfileName({profile:A.profile??B?.profile}),D,Q)},"fromIni")});
var BW2=E((eY2)=>{Object.defineProperty(eY2,"__esModule",{value:!0});eY2.resolveRuntimeExtensions=void 0;var sY2=S61(),rY2=uN1(),oY2=XZ(),tY2=aY2(),yT4=(A,B)=>{let Q=Object.assign(sY2.getAwsRegionExtensionConfiguration(A),oY2.getDefaultExtensionConfiguration(A),rY2.getHttpHandlerExtensionConfiguration(A),tY2.getHttpAuthExtensionConfiguration(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,sY2.resolveAwsRegionExtensionConfiguration(Q),oY2.resolveDefaultRuntimeConfig(Q),rY2.resolveHttpHandlerRuntimeConfig(Q),tY2.resolveHttpAuthRuntimeConfig(Q))};eY2.resolveRuntimeExtensions=yT4});
var BZ2=E((EE5,UN1)=>{var{defineProperty:eD2,getOwnPropertyDescriptor:cq4,getOwnPropertyNames:lq4}=Object,pq4=Object.prototype.hasOwnProperty,v50=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of lq4(B))if(!pq4.call(A,Z)&&Z!==Q)eD2(A,Z,{get:()=>B[Z],enumerable:!(D=cq4(B,Z))||D.enumerable})}return A},AZ2=(A,B,Q)=>(v50(A,B,"default"),Q&&v50(Q,B,"default")),iq4=(A)=>v50(eD2({},"__esModule",{value:!0}),A),b50={};UN1.exports=iq4(b50);AZ2(b50,sD2(),UN1.exports);AZ2(b50,tD2(),UN1.exports)});
var C61=E((qz5,O82)=>{var{defineProperty:Pq1,getOwnPropertyDescriptor:UH4,getOwnPropertyNames:wH4}=Object,$H4=Object.prototype.hasOwnProperty,u80=(A,B)=>Pq1(A,"name",{value:B,configurable:!0}),qH4=(A,B)=>{for(var Q in B)Pq1(A,Q,{get:B[Q],enumerable:!0})},NH4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of wH4(B))if(!$H4.call(A,Z)&&Z!==Q)Pq1(A,Z,{get:()=>B[Z],enumerable:!(D=UH4(B,Z))||D.enumerable})}return A},LH4=(A)=>NH4(Pq1({},"__esModule",{value:!0}),A),L82={};qH4(L82,{getLoggerPlugin:()=>MH4,loggerMiddleware:()=>M82,loggerMiddlewareOptions:()=>R82});O82.exports=LH4(L82);var M82=u80(()=>(A,B)=>async(Q)=>{try{let D=await A(Q),{clientName:Z,commandName:G,logger:F,dynamoDbDocumentClientOptions:I={}}=B,{overrideInputFilterSensitiveLog:Y,overrideOutputFilterSensitiveLog:W}=I,J=Y??B.inputFilterSensitiveLog,X=W??B.outputFilterSensitiveLog,{$metadata:V,...C}=D.output;return F?.info?.({clientName:Z,commandName:G,input:J(Q.input),output:X(C),metadata:V}),D}catch(D){let{clientName:Z,commandName:G,logger:F,dynamoDbDocumentClientOptions:I={}}=B,{overrideInputFilterSensitiveLog:Y}=I,W=Y??B.inputFilterSensitiveLog;throw F?.error?.({clientName:Z,commandName:G,input:W(Q.input),error:D,metadata:D.$metadata}),D}},"loggerMiddleware"),R82={name:"loggerMiddleware",tags:["LOGGER"],step:"initialize",override:!0},MH4=u80((A)=>({applyToStack:u80((B)=>{B.add(M82(),R82)},"applyToStack")}),"getLoggerPlugin")});
var C70=E((OU5,YJ2)=>{var{create:_S4,defineProperty:l61,getOwnPropertyDescriptor:xS4,getOwnPropertyNames:vS4,getPrototypeOf:bS4}=Object,fS4=Object.prototype.hasOwnProperty,sN1=(A,B)=>l61(A,"name",{value:B,configurable:!0}),hS4=(A,B)=>{for(var Q in B)l61(A,Q,{get:B[Q],enumerable:!0})},ZJ2=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of vS4(B))if(!fS4.call(A,Z)&&Z!==Q)l61(A,Z,{get:()=>B[Z],enumerable:!(D=xS4(B,Z))||D.enumerable})}return A},Br=(A,B,Q)=>(Q=A!=null?_S4(bS4(A)):{},ZJ2(B||!A||!A.__esModule?l61(Q,"default",{value:A,enumerable:!0}):Q,A)),gS4=(A)=>ZJ2(l61({},"__esModule",{value:!0}),A),GJ2={};hS4(GJ2,{credentialsTreatedAsExpired:()=>IJ2,credentialsWillNeedRefresh:()=>FJ2,defaultProvider:()=>dS4});YJ2.exports=gS4(GJ2);var V70=N80(),uS4=D3(),Og=Q9(),QJ2="AWS_EC2_METADATA_DISABLED",mS4=sN1(async(A)=>{let{ENV_CMDS_FULL_URI:B,ENV_CMDS_RELATIVE_URI:Q,fromContainerMetadata:D,fromInstanceMetadata:Z}=await Promise.resolve().then(()=>Br(TF()));if(process.env[Q]||process.env[B]){A.logger?.debug("@aws-sdk/credential-provider-node - remoteProvider::fromHttp/fromContainerMetadata");let{fromHttp:G}=await Promise.resolve().then(()=>Br(g80()));return Og.chain(G(A),D(A))}if(process.env[QJ2]&&process.env[QJ2]!=="false")return async()=>{throw new Og.CredentialsProviderError("EC2 Instance Metadata Service access disabled",{logger:A.logger})};return A.logger?.debug("@aws-sdk/credential-provider-node - remoteProvider::fromInstanceMetadata"),Z(A)},"remoteProvider"),DJ2=!1,dS4=sN1((A={})=>Og.memoize(Og.chain(async()=>{if(A.profile??process.env[uS4.ENV_PROFILE]){if(process.env[V70.ENV_KEY]&&process.env[V70.ENV_SECRET]){if(!DJ2)(A.logger?.warn&&A.logger?.constructor?.name!=="NoOpLogger"?A.logger.warn:console.warn)(`@aws-sdk/credential-provider-node - defaultProvider::fromEnv WARNING:
    Multiple credential sources detected: 
    Both AWS_PROFILE and the pair AWS_ACCESS_KEY_ID/AWS_SECRET_ACCESS_KEY static credentials are set.
    This SDK will proceed with the AWS_PROFILE value.
    
    However, a future version may change this behavior to prefer the ENV static credentials.
    Please ensure that your environment only sets either the AWS_PROFILE or the
    AWS_ACCESS_KEY_ID/AWS_SECRET_ACCESS_KEY pair.
`),DJ2=!0}throw new Og.CredentialsProviderError("AWS_PROFILE is set, skipping fromEnv provider.",{logger:A.logger,tryNextLink:!0})}return A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromEnv"),V70.fromEnv(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromSSO");let{ssoStartUrl:B,ssoAccountId:Q,ssoRegion:D,ssoRoleName:Z,ssoSession:G}=A;if(!B&&!Q&&!D&&!Z&&!G)throw new Og.CredentialsProviderError("Skipping SSO provider in default chain (inputs do not include SSO fields).",{logger:A.logger});let{fromSSO:F}=await Promise.resolve().then(()=>Br(E30()));return F(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromIni");let{fromIni:B}=await Promise.resolve().then(()=>Br(BJ2()));return B(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromProcess");let{fromProcess:B}=await Promise.resolve().then(()=>Br(G70()));return B(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromTokenFile");let{fromTokenFile:B}=await Promise.resolve().then(()=>Br(W70()));return B(A)()},async()=>{return A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::remoteProvider"),(await mS4(A))()},async()=>{throw new Og.CredentialsProviderError("Could not load credentials from any providers",{tryNextLink:!1,logger:A.logger})}),IJ2,FJ2),"defaultProvider"),FJ2=sN1((A)=>A?.expiration!==void 0,"credentialsWillNeedRefresh"),IJ2=sN1((A)=>A?.expiration!==void 0&&A.expiration.getTime()-Date.now()<300000,"credentialsTreatedAsExpired")});
var CXB=E((QA3,ik1)=>{var gJB,uJB,mJB,dJB,cJB,lJB,pJB,iJB,nJB,aJB,sJB,rJB,oJB,lk1,cz0,tJB,eJB,AXB,Re,BXB,QXB,DXB,ZXB,GXB,FXB,IXB,YXB,WXB,pk1,JXB,XXB,VXB;(function(A){var B=typeof global==="object"?global:typeof self==="object"?self:typeof this==="object"?this:{};if(typeof define==="function"&&define.amd)define("tslib",["exports"],function(D){A(Q(B,Q(D)))});else if(typeof ik1==="object"&&typeof QA3==="object")A(Q(B,Q(QA3)));else A(Q(B));function Q(D,Z){if(D!==B)if(typeof Object.create==="function")Object.defineProperty(D,"__esModule",{value:!0});else D.__esModule=!0;return function(G,F){return D[G]=Z?Z(G,F):F}}})(function(A){var B=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(G,F){G.__proto__=F}||function(G,F){for(var I in F)if(Object.prototype.hasOwnProperty.call(F,I))G[I]=F[I]};gJB=function(G,F){if(typeof F!=="function"&&F!==null)throw new TypeError("Class extends value "+String(F)+" is not a constructor or null");B(G,F);function I(){this.constructor=G}G.prototype=F===null?Object.create(F):(I.prototype=F.prototype,new I)},uJB=Object.assign||function(G){for(var F,I=1,Y=arguments.length;I<Y;I++){F=arguments[I];for(var W in F)if(Object.prototype.hasOwnProperty.call(F,W))G[W]=F[W]}return G},mJB=function(G,F){var I={};for(var Y in G)if(Object.prototype.hasOwnProperty.call(G,Y)&&F.indexOf(Y)<0)I[Y]=G[Y];if(G!=null&&typeof Object.getOwnPropertySymbols==="function"){for(var W=0,Y=Object.getOwnPropertySymbols(G);W<Y.length;W++)if(F.indexOf(Y[W])<0&&Object.prototype.propertyIsEnumerable.call(G,Y[W]))I[Y[W]]=G[Y[W]]}return I},dJB=function(G,F,I,Y){var W=arguments.length,J=W<3?F:Y===null?Y=Object.getOwnPropertyDescriptor(F,I):Y,X;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")J=Reflect.decorate(G,F,I,Y);else for(var V=G.length-1;V>=0;V--)if(X=G[V])J=(W<3?X(J):W>3?X(F,I,J):X(F,I))||J;return W>3&&J&&Object.defineProperty(F,I,J),J},cJB=function(G,F){return function(I,Y){F(I,Y,G)}},lJB=function(G,F,I,Y,W,J){function X(T){if(T!==void 0&&typeof T!=="function")throw new TypeError("Function expected");return T}var V=Y.kind,C=V==="getter"?"get":V==="setter"?"set":"value",K=!F&&G?Y.static?G:G.prototype:null,H=F||(K?Object.getOwnPropertyDescriptor(K,Y.name):{}),z,$=!1;for(var L=I.length-1;L>=0;L--){var N={};for(var O in Y)N[O]=O==="access"?{}:Y[O];for(var O in Y.access)N.access[O]=Y.access[O];N.addInitializer=function(T){if($)throw new TypeError("Cannot add initializers after decoration has completed");J.push(X(T||null))};var R=I[L](V==="accessor"?{get:H.get,set:H.set}:H[C],N);if(V==="accessor"){if(R===void 0)continue;if(R===null||typeof R!=="object")throw new TypeError("Object expected");if(z=X(R.get))H.get=z;if(z=X(R.set))H.set=z;if(z=X(R.init))W.unshift(z)}else if(z=X(R))if(V==="field")W.unshift(z);else H[C]=z}if(K)Object.defineProperty(K,Y.name,H);$=!0},pJB=function(G,F,I){var Y=arguments.length>2;for(var W=0;W<F.length;W++)I=Y?F[W].call(G,I):F[W].call(G);return Y?I:void 0},iJB=function(G){return typeof G==="symbol"?G:"".concat(G)},nJB=function(G,F,I){if(typeof F==="symbol")F=F.description?"[".concat(F.description,"]"):"";return Object.defineProperty(G,"name",{configurable:!0,value:I?"".concat(I," ",F):F})},aJB=function(G,F){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(G,F)},sJB=function(G,F,I,Y){function W(J){return J instanceof I?J:new I(function(X){X(J)})}return new(I||(I=Promise))(function(J,X){function V(H){try{K(Y.next(H))}catch(z){X(z)}}function C(H){try{K(Y.throw(H))}catch(z){X(z)}}function K(H){H.done?J(H.value):W(H.value).then(V,C)}K((Y=Y.apply(G,F||[])).next())})},rJB=function(G,F){var I={label:0,sent:function(){if(J[0]&1)throw J[1];return J[1]},trys:[],ops:[]},Y,W,J,X=Object.create((typeof Iterator==="function"?Iterator:Object).prototype);return X.next=V(0),X.throw=V(1),X.return=V(2),typeof Symbol==="function"&&(X[Symbol.iterator]=function(){return this}),X;function V(K){return function(H){return C([K,H])}}function C(K){if(Y)throw new TypeError("Generator is already executing.");while(X&&(X=0,K[0]&&(I=0)),I)try{if(Y=1,W&&(J=K[0]&2?W.return:K[0]?W.throw||((J=W.return)&&J.call(W),0):W.next)&&!(J=J.call(W,K[1])).done)return J;if(W=0,J)K=[K[0]&2,J.value];switch(K[0]){case 0:case 1:J=K;break;case 4:return I.label++,{value:K[1],done:!1};case 5:I.label++,W=K[1],K=[0];continue;case 7:K=I.ops.pop(),I.trys.pop();continue;default:if((J=I.trys,!(J=J.length>0&&J[J.length-1]))&&(K[0]===6||K[0]===2)){I=0;continue}if(K[0]===3&&(!J||K[1]>J[0]&&K[1]<J[3])){I.label=K[1];break}if(K[0]===6&&I.label<J[1]){I.label=J[1],J=K;break}if(J&&I.label<J[2]){I.label=J[2],I.ops.push(K);break}if(J[2])I.ops.pop();I.trys.pop();continue}K=F.call(G,I)}catch(H){K=[6,H],W=0}finally{Y=J=0}if(K[0]&5)throw K[1];return{value:K[0]?K[1]:void 0,done:!0}}},oJB=function(G,F){for(var I in G)if(I!=="default"&&!Object.prototype.hasOwnProperty.call(F,I))pk1(F,G,I)},pk1=Object.create?function(G,F,I,Y){if(Y===void 0)Y=I;var W=Object.getOwnPropertyDescriptor(F,I);if(!W||("get"in W?!F.__esModule:W.writable||W.configurable))W={enumerable:!0,get:function(){return F[I]}};Object.defineProperty(G,Y,W)}:function(G,F,I,Y){if(Y===void 0)Y=I;G[Y]=F[I]},lk1=function(G){var F=typeof Symbol==="function"&&Symbol.iterator,I=F&&G[F],Y=0;if(I)return I.call(G);if(G&&typeof G.length==="number")return{next:function(){if(G&&Y>=G.length)G=void 0;return{value:G&&G[Y++],done:!G}}};throw new TypeError(F?"Object is not iterable.":"Symbol.iterator is not defined.")},cz0=function(G,F){var I=typeof Symbol==="function"&&G[Symbol.iterator];if(!I)return G;var Y=I.call(G),W,J=[],X;try{while((F===void 0||F-- >0)&&!(W=Y.next()).done)J.push(W.value)}catch(V){X={error:V}}finally{try{if(W&&!W.done&&(I=Y.return))I.call(Y)}finally{if(X)throw X.error}}return J},tJB=function(){for(var G=[],F=0;F<arguments.length;F++)G=G.concat(cz0(arguments[F]));return G},eJB=function(){for(var G=0,F=0,I=arguments.length;F<I;F++)G+=arguments[F].length;for(var Y=Array(G),W=0,F=0;F<I;F++)for(var J=arguments[F],X=0,V=J.length;X<V;X++,W++)Y[W]=J[X];return Y},AXB=function(G,F,I){if(I||arguments.length===2){for(var Y=0,W=F.length,J;Y<W;Y++)if(J||!(Y in F)){if(!J)J=Array.prototype.slice.call(F,0,Y);J[Y]=F[Y]}}return G.concat(J||Array.prototype.slice.call(F))},Re=function(G){return this instanceof Re?(this.v=G,this):new Re(G)},BXB=function(G,F,I){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var Y=I.apply(G,F||[]),W,J=[];return W=Object.create((typeof AsyncIterator==="function"?AsyncIterator:Object).prototype),V("next"),V("throw"),V("return",X),W[Symbol.asyncIterator]=function(){return this},W;function X(L){return function(N){return Promise.resolve(N).then(L,z)}}function V(L,N){if(Y[L]){if(W[L]=function(O){return new Promise(function(R,T){J.push([L,O,R,T])>1||C(L,O)})},N)W[L]=N(W[L])}}function C(L,N){try{K(Y[L](N))}catch(O){$(J[0][3],O)}}function K(L){L.value instanceof Re?Promise.resolve(L.value.v).then(H,z):$(J[0][2],L)}function H(L){C("next",L)}function z(L){C("throw",L)}function $(L,N){if(L(N),J.shift(),J.length)C(J[0][0],J[0][1])}},QXB=function(G){var F,I;return F={},Y("next"),Y("throw",function(W){throw W}),Y("return"),F[Symbol.iterator]=function(){return this},F;function Y(W,J){F[W]=G[W]?function(X){return(I=!I)?{value:Re(G[W](X)),done:!1}:J?J(X):X}:J}},DXB=function(G){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var F=G[Symbol.asyncIterator],I;return F?F.call(G):(G=typeof lk1==="function"?lk1(G):G[Symbol.iterator](),I={},Y("next"),Y("throw"),Y("return"),I[Symbol.asyncIterator]=function(){return this},I);function Y(J){I[J]=G[J]&&function(X){return new Promise(function(V,C){X=G[J](X),W(V,C,X.done,X.value)})}}function W(J,X,V,C){Promise.resolve(C).then(function(K){J({value:K,done:V})},X)}},ZXB=function(G,F){if(Object.defineProperty)Object.defineProperty(G,"raw",{value:F});else G.raw=F;return G};var Q=Object.create?function(G,F){Object.defineProperty(G,"default",{enumerable:!0,value:F})}:function(G,F){G.default=F},D=function(G){return D=Object.getOwnPropertyNames||function(F){var I=[];for(var Y in F)if(Object.prototype.hasOwnProperty.call(F,Y))I[I.length]=Y;return I},D(G)};GXB=function(G){if(G&&G.__esModule)return G;var F={};if(G!=null){for(var I=D(G),Y=0;Y<I.length;Y++)if(I[Y]!=="default")pk1(F,G,I[Y])}return Q(F,G),F},FXB=function(G){return G&&G.__esModule?G:{default:G}},IXB=function(G,F,I,Y){if(I==="a"&&!Y)throw new TypeError("Private accessor was defined without a getter");if(typeof F==="function"?G!==F||!Y:!F.has(G))throw new TypeError("Cannot read private member from an object whose class did not declare it");return I==="m"?Y:I==="a"?Y.call(G):Y?Y.value:F.get(G)},YXB=function(G,F,I,Y,W){if(Y==="m")throw new TypeError("Private method is not writable");if(Y==="a"&&!W)throw new TypeError("Private accessor was defined without a setter");if(typeof F==="function"?G!==F||!W:!F.has(G))throw new TypeError("Cannot write private member to an object whose class did not declare it");return Y==="a"?W.call(G,I):W?W.value=I:F.set(G,I),I},WXB=function(G,F){if(F===null||typeof F!=="object"&&typeof F!=="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof G==="function"?F===G:G.has(F)},JXB=function(G,F,I){if(F!==null&&F!==void 0){if(typeof F!=="object"&&typeof F!=="function")throw new TypeError("Object expected.");var Y,W;if(I){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");Y=F[Symbol.asyncDispose]}if(Y===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");if(Y=F[Symbol.dispose],I)W=Y}if(typeof Y!=="function")throw new TypeError("Object not disposable.");if(W)Y=function(){try{W.call(this)}catch(J){return Promise.reject(J)}};G.stack.push({value:F,dispose:Y,async:I})}else if(I)G.stack.push({async:!0});return F};var Z=typeof SuppressedError==="function"?SuppressedError:function(G,F,I){var Y=new Error(I);return Y.name="SuppressedError",Y.error=G,Y.suppressed=F,Y};XXB=function(G){function F(J){G.error=G.hasError?new Z(J,G.error,"An error was suppressed during disposal."):J,G.hasError=!0}var I,Y=0;function W(){while(I=G.stack.pop())try{if(!I.async&&Y===1)return Y=0,G.stack.push(I),Promise.resolve().then(W);if(I.dispose){var J=I.dispose.call(I.value);if(I.async)return Y|=2,Promise.resolve(J).then(W,function(X){return F(X),W()})}else Y|=1}catch(X){F(X)}if(Y===1)return G.hasError?Promise.reject(G.error):Promise.resolve();if(G.hasError)throw G.error}return W()},VXB=function(G,F){if(typeof G==="string"&&/^\.\.?\//.test(G))return G.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,function(I,Y,W,J,X){return Y?F?".jsx":".js":W&&(!J||!X)?I:W+J+"."+X.toLowerCase()+"js"});return G},A("__extends",gJB),A("__assign",uJB),A("__rest",mJB),A("__decorate",dJB),A("__param",cJB),A("__esDecorate",lJB),A("__runInitializers",pJB),A("__propKey",iJB),A("__setFunctionName",nJB),A("__metadata",aJB),A("__awaiter",sJB),A("__generator",rJB),A("__exportStar",oJB),A("__createBinding",pk1),A("__values",lk1),A("__read",cz0),A("__spread",tJB),A("__spreadArrays",eJB),A("__spreadArray",AXB),A("__await",Re),A("__asyncGenerator",BXB),A("__asyncDelegator",QXB),A("__asyncValues",DXB),A("__makeTemplateObject",ZXB),A("__importStar",GXB),A("__importDefault",FXB),A("__classPrivateFieldGet",IXB),A("__classPrivateFieldSet",YXB),A("__classPrivateFieldIn",WXB),A("__addDisposableResource",JXB),A("__disposeResources",XXB),A("__rewriteRelativeImportExtension",VXB)})});
var DKB=E((iA3,QKB)=>{var{defineProperty:K_1,getOwnPropertyDescriptor:dn6,getOwnPropertyNames:cn6}=Object,ln6=Object.prototype.hasOwnProperty,mx=(A,B)=>K_1(A,"name",{value:B,configurable:!0}),pn6=(A,B)=>{for(var Q in B)K_1(A,Q,{get:B[Q],enumerable:!0})},in6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of cn6(B))if(!ln6.call(A,Z)&&Z!==Q)K_1(A,Z,{get:()=>B[Z],enumerable:!(D=dn6(B,Z))||D.enumerable})}return A},nn6=(A)=>in6(K_1({},"__esModule",{value:!0}),A),tCB={};pn6(tCB,{Field:()=>rn6,Fields:()=>on6,HttpRequest:()=>tn6,HttpResponse:()=>en6,IHttpRequest:()=>eCB.HttpRequest,getHttpHandlerExtensionConfiguration:()=>an6,isValidHostname:()=>BKB,resolveHttpHandlerRuntimeConfig:()=>sn6});QKB.exports=nn6(tCB);var an6=mx((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),sn6=mx((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),eCB=AE0(),rn6=class{static{mx(this,"Field")}constructor({name:A,kind:B=eCB.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},on6=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{mx(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},tn6=class A{static{mx(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=AKB(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function AKB(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}mx(AKB,"cloneQuery");var en6=class{static{mx(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function BKB(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}mx(BKB,"isValidHostname")});
var DN1=E((sz5,z72)=>{var{defineProperty:QN1,getOwnPropertyDescriptor:IU4,getOwnPropertyNames:YU4}=Object,WU4=Object.prototype.hasOwnProperty,j2=(A,B)=>QN1(A,"name",{value:B,configurable:!0}),JU4=(A,B)=>{for(var Q in B)QN1(A,Q,{get:B[Q],enumerable:!0})},XU4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of YU4(B))if(!WU4.call(A,Z)&&Z!==Q)QN1(A,Z,{get:()=>B[Z],enumerable:!(D=IU4(B,Z))||D.enumerable})}return A},VU4=(A)=>XU4(QN1({},"__esModule",{value:!0}),A),A72={};JU4(A72,{Client:()=>CU4,Command:()=>Q72,LazyJsonString:()=>Ng,NoOpLogger:()=>Ww4,SENSITIVE_STRING:()=>HU4,ServiceException:()=>rU4,_json:()=>W50,collectBody:()=>D50.collectBody,convertMap:()=>Jw4,createAggregatedClient:()=>zU4,dateToUtcString:()=>Y72,decorateServiceException:()=>W72,emitWarningIfUnsupportedVersion:()=>Aw4,expectBoolean:()=>UU4,expectByte:()=>Y50,expectFloat32:()=>AN1,expectInt:()=>$U4,expectInt32:()=>F50,expectLong:()=>w61,expectNonNull:()=>NU4,expectNumber:()=>U61,expectObject:()=>D72,expectShort:()=>I50,expectString:()=>LU4,expectUnion:()=>MU4,extendedEncodeURIComponent:()=>D50.extendedEncodeURIComponent,getArrayIfSingleItem:()=>Iw4,getDefaultClientConfiguration:()=>Gw4,getDefaultExtensionConfiguration:()=>X72,getValueFromTextNode:()=>V72,handleFloat:()=>TU4,isSerializableHeaderValue:()=>Yw4,limitedParseDouble:()=>V50,limitedParseFloat:()=>PU4,limitedParseFloat32:()=>SU4,loadConfigsForDefaultMode:()=>eU4,logger:()=>$61,map:()=>K50,parseBoolean:()=>EU4,parseEpochTimestamp:()=>mU4,parseRfc3339DateTime:()=>xU4,parseRfc3339DateTimeWithOffset:()=>bU4,parseRfc7231DateTime:()=>uU4,quoteHeader:()=>K72,resolveDefaultRuntimeConfig:()=>Fw4,resolvedPath:()=>D50.resolvedPath,serializeDateTime:()=>zw4,serializeFloat:()=>Hw4,splitEvery:()=>H72,splitHeader:()=>Ew4,strictParseByte:()=>I72,strictParseDouble:()=>X50,strictParseFloat:()=>RU4,strictParseFloat32:()=>Z72,strictParseInt:()=>jU4,strictParseInt32:()=>yU4,strictParseLong:()=>F72,strictParseShort:()=>Ns,take:()=>Xw4,throwDefaultError:()=>J72,withBaseException:()=>oU4});z72.exports=VU4(A72);var B72=Mw(),CU4=class{constructor(A){this.config=A,this.middlewareStack=B72.constructStack()}static{j2(this,"Client")}send(A,B,Q){let D=typeof B!=="function"?B:void 0,Z=typeof B==="function"?B:Q,G=D===void 0&&this.config.cacheMiddleware===!0,F;if(G){if(!this.handlers)this.handlers=new WeakMap;let I=this.handlers;if(I.has(A.constructor))F=I.get(A.constructor);else F=A.resolveMiddleware(this.middlewareStack,this.config,D),I.set(A.constructor,F)}else delete this.handlers,F=A.resolveMiddleware(this.middlewareStack,this.config,D);if(Z)F(A).then((I)=>Z(null,I.output),(I)=>Z(I)).catch(()=>{});else return F(A).then((I)=>I.output)}destroy(){this.config?.requestHandler?.destroy?.(),delete this.handlers}},D50=M6(),G50=c80(),Q72=class{constructor(){this.middlewareStack=B72.constructStack()}static{j2(this,"Command")}static classBuilder(){return new KU4}resolveMiddlewareWithContext(A,B,Q,{middlewareFn:D,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,smithyContext:Y,additionalContext:W,CommandCtor:J}){for(let H of D.bind(this)(J,A,B,Q))this.middlewareStack.use(H);let X=A.concat(this.middlewareStack),{logger:V}=B,C={logger:V,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,[G50.SMITHY_CONTEXT_KEY]:{commandInstance:this,...Y},...W},{requestHandler:K}=B;return X.resolve((H)=>K.handle(H.request,Q||{}),C)}},KU4=class{constructor(){this._init=()=>{},this._ep={},this._middlewareFn=()=>[],this._commandName="",this._clientName="",this._additionalContext={},this._smithyContext={},this._inputFilterSensitiveLog=(A)=>A,this._outputFilterSensitiveLog=(A)=>A,this._serializer=null,this._deserializer=null}static{j2(this,"ClassBuilder")}init(A){this._init=A}ep(A){return this._ep=A,this}m(A){return this._middlewareFn=A,this}s(A,B,Q={}){return this._smithyContext={service:A,operation:B,...Q},this}c(A={}){return this._additionalContext=A,this}n(A,B){return this._clientName=A,this._commandName=B,this}f(A=(Q)=>Q,B=(Q)=>Q){return this._inputFilterSensitiveLog=A,this._outputFilterSensitiveLog=B,this}ser(A){return this._serializer=A,this}de(A){return this._deserializer=A,this}build(){let A=this,B;return B=class extends Q72{constructor(...[Q]){super();this.serialize=A._serializer,this.deserialize=A._deserializer,this.input=Q??{},A._init(this)}static{j2(this,"CommandRef")}static getEndpointParameterInstructions(){return A._ep}resolveMiddleware(Q,D,Z){return this.resolveMiddlewareWithContext(Q,D,Z,{CommandCtor:B,middlewareFn:A._middlewareFn,clientName:A._clientName,commandName:A._commandName,inputFilterSensitiveLog:A._inputFilterSensitiveLog,outputFilterSensitiveLog:A._outputFilterSensitiveLog,smithyContext:A._smithyContext,additionalContext:A._additionalContext})}}}},HU4="***SensitiveInformation***",zU4=j2((A,B)=>{for(let Q of Object.keys(A)){let D=A[Q],Z=j2(async function(F,I,Y){let W=new D(F);if(typeof I==="function")this.send(W,I);else if(typeof Y==="function"){if(typeof I!=="object")throw new Error(`Expected http options but got ${typeof I}`);this.send(W,I||{},Y)}else return this.send(W,I)},"methodImpl"),G=(Q[0].toLowerCase()+Q.slice(1)).replace(/Command$/,"");B.prototype[G]=Z}},"createAggregatedClient"),EU4=j2((A)=>{switch(A){case"true":return!0;case"false":return!1;default:throw new Error(`Unable to parse boolean value "${A}"`)}},"parseBoolean"),UU4=j2((A)=>{if(A===null||A===void 0)return;if(typeof A==="number"){if(A===0||A===1)$61.warn(BN1(`Expected boolean, got ${typeof A}: ${A}`));if(A===0)return!1;if(A===1)return!0}if(typeof A==="string"){let B=A.toLowerCase();if(B==="false"||B==="true")$61.warn(BN1(`Expected boolean, got ${typeof A}: ${A}`));if(B==="false")return!1;if(B==="true")return!0}if(typeof A==="boolean")return A;throw new TypeError(`Expected boolean, got ${typeof A}: ${A}`)},"expectBoolean"),U61=j2((A)=>{if(A===null||A===void 0)return;if(typeof A==="string"){let B=parseFloat(A);if(!Number.isNaN(B)){if(String(B)!==String(A))$61.warn(BN1(`Expected number but observed string: ${A}`));return B}}if(typeof A==="number")return A;throw new TypeError(`Expected number, got ${typeof A}: ${A}`)},"expectNumber"),wU4=Math.ceil(340282346638528860000000000000000000000),AN1=j2((A)=>{let B=U61(A);if(B!==void 0&&!Number.isNaN(B)&&B!==1/0&&B!==-1/0){if(Math.abs(B)>wU4)throw new TypeError(`Expected 32-bit float, got ${A}`)}return B},"expectFloat32"),w61=j2((A)=>{if(A===null||A===void 0)return;if(Number.isInteger(A)&&!Number.isNaN(A))return A;throw new TypeError(`Expected integer, got ${typeof A}: ${A}`)},"expectLong"),$U4=w61,F50=j2((A)=>J50(A,32),"expectInt32"),I50=j2((A)=>J50(A,16),"expectShort"),Y50=j2((A)=>J50(A,8),"expectByte"),J50=j2((A,B)=>{let Q=w61(A);if(Q!==void 0&&qU4(Q,B)!==Q)throw new TypeError(`Expected ${B}-bit integer, got ${A}`);return Q},"expectSizedInt"),qU4=j2((A,B)=>{switch(B){case 32:return Int32Array.of(A)[0];case 16:return Int16Array.of(A)[0];case 8:return Int8Array.of(A)[0]}},"castInt"),NU4=j2((A,B)=>{if(A===null||A===void 0){if(B)throw new TypeError(`Expected a non-null value for ${B}`);throw new TypeError("Expected a non-null value")}return A},"expectNonNull"),D72=j2((A)=>{if(A===null||A===void 0)return;if(typeof A==="object"&&!Array.isArray(A))return A;let B=Array.isArray(A)?"array":typeof A;throw new TypeError(`Expected object, got ${B}: ${A}`)},"expectObject"),LU4=j2((A)=>{if(A===null||A===void 0)return;if(typeof A==="string")return A;if(["boolean","number","bigint"].includes(typeof A))return $61.warn(BN1(`Expected string, got ${typeof A}: ${A}`)),String(A);throw new TypeError(`Expected string, got ${typeof A}: ${A}`)},"expectString"),MU4=j2((A)=>{if(A===null||A===void 0)return;let B=D72(A),Q=Object.entries(B).filter(([,D])=>D!=null).map(([D])=>D);if(Q.length===0)throw new TypeError("Unions must have exactly one non-null member. None were found.");if(Q.length>1)throw new TypeError(`Unions must have exactly one non-null member. Keys ${Q} were not null.`);return B},"expectUnion"),X50=j2((A)=>{if(typeof A=="string")return U61(Ms(A));return U61(A)},"strictParseDouble"),RU4=X50,Z72=j2((A)=>{if(typeof A=="string")return AN1(Ms(A));return AN1(A)},"strictParseFloat32"),OU4=/(-?(?:0|[1-9]\d*)(?:\.\d+)?(?:[eE][+-]?\d+)?)|(-?Infinity)|(NaN)/g,Ms=j2((A)=>{let B=A.match(OU4);if(B===null||B[0].length!==A.length)throw new TypeError("Expected real number, got implicit NaN");return parseFloat(A)},"parseNumber"),V50=j2((A)=>{if(typeof A=="string")return G72(A);return U61(A)},"limitedParseDouble"),TU4=V50,PU4=V50,SU4=j2((A)=>{if(typeof A=="string")return G72(A);return AN1(A)},"limitedParseFloat32"),G72=j2((A)=>{switch(A){case"NaN":return NaN;case"Infinity":return 1/0;case"-Infinity":return-1/0;default:throw new Error(`Unable to parse float value: ${A}`)}},"parseFloatString"),F72=j2((A)=>{if(typeof A==="string")return w61(Ms(A));return w61(A)},"strictParseLong"),jU4=F72,yU4=j2((A)=>{if(typeof A==="string")return F50(Ms(A));return F50(A)},"strictParseInt32"),Ns=j2((A)=>{if(typeof A==="string")return I50(Ms(A));return I50(A)},"strictParseShort"),I72=j2((A)=>{if(typeof A==="string")return Y50(Ms(A));return Y50(A)},"strictParseByte"),BN1=j2((A)=>{return String(new TypeError(A).stack||A).split(`
`).slice(0,5).filter((B)=>!B.includes("stackTraceWarning")).join(`
`)},"stackTraceWarning"),$61={warn:console.warn},kU4=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],C50=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function Y72(A){let B=A.getUTCFullYear(),Q=A.getUTCMonth(),D=A.getUTCDay(),Z=A.getUTCDate(),G=A.getUTCHours(),F=A.getUTCMinutes(),I=A.getUTCSeconds(),Y=Z<10?`0${Z}`:`${Z}`,W=G<10?`0${G}`:`${G}`,J=F<10?`0${F}`:`${F}`,X=I<10?`0${I}`:`${I}`;return`${kU4[D]}, ${Y} ${C50[Q]} ${B} ${W}:${J}:${X} GMT`}j2(Y72,"dateToUtcString");var _U4=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?[zZ]$/),xU4=j2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-3339 date-times must be expressed as strings");let B=_U4.exec(A);if(!B)throw new TypeError("Invalid RFC-3339 date-time value");let[Q,D,Z,G,F,I,Y,W]=B,J=Ns(Ls(D)),X=UL(Z,"month",1,12),V=UL(G,"day",1,31);return E61(J,X,V,{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W})},"parseRfc3339DateTime"),vU4=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?(([-+]\d{2}\:\d{2})|[zZ])$/),bU4=j2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-3339 date-times must be expressed as strings");let B=vU4.exec(A);if(!B)throw new TypeError("Invalid RFC-3339 date-time value");let[Q,D,Z,G,F,I,Y,W,J]=B,X=Ns(Ls(D)),V=UL(Z,"month",1,12),C=UL(G,"day",1,31),K=E61(X,V,C,{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W});if(J.toUpperCase()!="Z")K.setTime(K.getTime()-sU4(J));return K},"parseRfc3339DateTimeWithOffset"),fU4=new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d{2}) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/),hU4=new RegExp(/^(?:Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d{2})-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/),gU4=new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( [1-9]|\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? (\d{4})$/),uU4=j2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-7231 date-times must be expressed as strings");let B=fU4.exec(A);if(B){let[Q,D,Z,G,F,I,Y,W]=B;return E61(Ns(Ls(G)),Z50(Z),UL(D,"day",1,31),{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W})}if(B=hU4.exec(A),B){let[Q,D,Z,G,F,I,Y,W]=B;return lU4(E61(dU4(G),Z50(Z),UL(D,"day",1,31),{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W}))}if(B=gU4.exec(A),B){let[Q,D,Z,G,F,I,Y,W]=B;return E61(Ns(Ls(W)),Z50(D),UL(Z.trimLeft(),"day",1,31),{hours:G,minutes:F,seconds:I,fractionalMilliseconds:Y})}throw new TypeError("Invalid RFC-7231 date-time value")},"parseRfc7231DateTime"),mU4=j2((A)=>{if(A===null||A===void 0)return;let B;if(typeof A==="number")B=A;else if(typeof A==="string")B=X50(A);else if(typeof A==="object"&&A.tag===1)B=A.value;else throw new TypeError("Epoch timestamps must be expressed as floating point numbers or their string representation");if(Number.isNaN(B)||B===1/0||B===-1/0)throw new TypeError("Epoch timestamps must be valid, non-Infinite, non-NaN numerics");return new Date(Math.round(B*1000))},"parseEpochTimestamp"),E61=j2((A,B,Q,D)=>{let Z=B-1;return iU4(A,Z,Q),new Date(Date.UTC(A,Z,Q,UL(D.hours,"hour",0,23),UL(D.minutes,"minute",0,59),UL(D.seconds,"seconds",0,60),aU4(D.fractionalMilliseconds)))},"buildDate"),dU4=j2((A)=>{let B=new Date().getUTCFullYear(),Q=Math.floor(B/100)*100+Ns(Ls(A));if(Q<B)return Q+100;return Q},"parseTwoDigitYear"),cU4=1576800000000,lU4=j2((A)=>{if(A.getTime()-new Date().getTime()>cU4)return new Date(Date.UTC(A.getUTCFullYear()-100,A.getUTCMonth(),A.getUTCDate(),A.getUTCHours(),A.getUTCMinutes(),A.getUTCSeconds(),A.getUTCMilliseconds()));return A},"adjustRfc850Year"),Z50=j2((A)=>{let B=C50.indexOf(A);if(B<0)throw new TypeError(`Invalid month: ${A}`);return B+1},"parseMonthByShortName"),pU4=[31,28,31,30,31,30,31,31,30,31,30,31],iU4=j2((A,B,Q)=>{let D=pU4[B];if(B===1&&nU4(A))D=29;if(Q>D)throw new TypeError(`Invalid day for ${C50[B]} in ${A}: ${Q}`)},"validateDayOfMonth"),nU4=j2((A)=>{return A%4===0&&(A%100!==0||A%400===0)},"isLeapYear"),UL=j2((A,B,Q,D)=>{let Z=I72(Ls(A));if(Z<Q||Z>D)throw new TypeError(`${B} must be between ${Q} and ${D}, inclusive`);return Z},"parseDateValue"),aU4=j2((A)=>{if(A===null||A===void 0)return 0;return Z72("0."+A)*1000},"parseMilliseconds"),sU4=j2((A)=>{let B=A[0],Q=1;if(B=="+")Q=1;else if(B=="-")Q=-1;else throw new TypeError(`Offset direction, ${B}, must be "+" or "-"`);let D=Number(A.substring(1,3)),Z=Number(A.substring(4,6));return Q*(D*60+Z)*60*1000},"parseOffsetToMilliseconds"),Ls=j2((A)=>{let B=0;while(B<A.length-1&&A.charAt(B)==="0")B++;if(B===0)return A;return A.slice(B)},"stripLeadingZeroes"),rU4=class A extends Error{static{j2(this,"ServiceException")}constructor(B){super(B.message);Object.setPrototypeOf(this,Object.getPrototypeOf(this).constructor.prototype),this.name=B.name,this.$fault=B.$fault,this.$metadata=B.$metadata}static isInstance(B){if(!B)return!1;let Q=B;return A.prototype.isPrototypeOf(Q)||Boolean(Q.$fault)&&Boolean(Q.$metadata)&&(Q.$fault==="client"||Q.$fault==="server")}static[Symbol.hasInstance](B){if(!B)return!1;let Q=B;if(this===A)return A.isInstance(B);if(A.isInstance(B)){if(Q.name&&this.name)return this.prototype.isPrototypeOf(B)||Q.name===this.name;return this.prototype.isPrototypeOf(B)}return!1}},W72=j2((A,B={})=>{Object.entries(B).filter(([,D])=>D!==void 0).forEach(([D,Z])=>{if(A[D]==null||A[D]==="")A[D]=Z});let Q=A.message||A.Message||"UnknownError";return A.message=Q,delete A.Message,A},"decorateServiceException"),J72=j2(({output:A,parsedBody:B,exceptionCtor:Q,errorCode:D})=>{let Z=tU4(A),G=Z.httpStatusCode?Z.httpStatusCode+"":void 0,F=new Q({name:B?.code||B?.Code||D||G||"UnknownError",$fault:"client",$metadata:Z});throw W72(F,B)},"throwDefaultError"),oU4=j2((A)=>{return({output:B,parsedBody:Q,errorCode:D})=>{J72({output:B,parsedBody:Q,exceptionCtor:A,errorCode:D})}},"withBaseException"),tU4=j2((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),eU4=j2((A)=>{switch(A){case"standard":return{retryMode:"standard",connectionTimeout:3100};case"in-region":return{retryMode:"standard",connectionTimeout:1100};case"cross-region":return{retryMode:"standard",connectionTimeout:3100};case"mobile":return{retryMode:"standard",connectionTimeout:30000};default:return{}}},"loadConfigsForDefaultMode"),e32=!1,Aw4=j2((A)=>{if(A&&!e32&&parseInt(A.substring(1,A.indexOf(".")))<16)e32=!0},"emitWarningIfUnsupportedVersion"),Bw4=j2((A)=>{let B=[];for(let Q in G50.AlgorithmId){let D=G50.AlgorithmId[Q];if(A[D]===void 0)continue;B.push({algorithmId:()=>D,checksumConstructor:()=>A[D]})}return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),Qw4=j2((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),Dw4=j2((A)=>{return{setRetryStrategy(B){A.retryStrategy=B},retryStrategy(){return A.retryStrategy}}},"getRetryConfiguration"),Zw4=j2((A)=>{let B={};return B.retryStrategy=A.retryStrategy(),B},"resolveRetryRuntimeConfig"),X72=j2((A)=>{return Object.assign(Bw4(A),Dw4(A))},"getDefaultExtensionConfiguration"),Gw4=X72,Fw4=j2((A)=>{return Object.assign(Qw4(A),Zw4(A))},"resolveDefaultRuntimeConfig"),Iw4=j2((A)=>Array.isArray(A)?A:[A],"getArrayIfSingleItem"),V72=j2((A)=>{for(let Q in A)if(A.hasOwnProperty(Q)&&A[Q]["#text"]!==void 0)A[Q]=A[Q]["#text"];else if(typeof A[Q]==="object"&&A[Q]!==null)A[Q]=V72(A[Q]);return A},"getValueFromTextNode"),Yw4=j2((A)=>{return A!=null},"isSerializableHeaderValue"),Ng=j2(function A(B){return Object.assign(new String(B),{deserializeJSON(){return JSON.parse(String(B))},toString(){return String(B)},toJSON(){return String(B)}})},"LazyJsonString");Ng.from=(A)=>{if(A&&typeof A==="object"&&(A instanceof Ng||("deserializeJSON"in A)))return A;else if(typeof A==="string"||Object.getPrototypeOf(A)===String.prototype)return Ng(String(A));return Ng(JSON.stringify(A))};Ng.fromObject=Ng.from;var Ww4=class{static{j2(this,"NoOpLogger")}trace(){}debug(){}info(){}warn(){}error(){}};function K50(A,B,Q){let D,Z,G;if(typeof B==="undefined"&&typeof Q==="undefined")D={},G=A;else if(D=A,typeof B==="function")return Z=B,G=Q,Vw4(D,Z,G);else G=B;for(let F of Object.keys(G)){if(!Array.isArray(G[F])){D[F]=G[F];continue}C72(D,null,G,F)}return D}j2(K50,"map");var Jw4=j2((A)=>{let B={};for(let[Q,D]of Object.entries(A||{}))B[Q]=[,D];return B},"convertMap"),Xw4=j2((A,B)=>{let Q={};for(let D in B)C72(Q,A,B,D);return Q},"take"),Vw4=j2((A,B,Q)=>{return K50(A,Object.entries(Q).reduce((D,[Z,G])=>{if(Array.isArray(G))D[Z]=G;else if(typeof G==="function")D[Z]=[B,G()];else D[Z]=[B,G];return D},{}))},"mapWithFilter"),C72=j2((A,B,Q,D)=>{if(B!==null){let F=Q[D];if(typeof F==="function")F=[,F];let[I=Cw4,Y=Kw4,W=D]=F;if(typeof I==="function"&&I(B[W])||typeof I!=="function"&&!!I)A[D]=Y(B[W]);return}let[Z,G]=Q[D];if(typeof G==="function"){let F,I=Z===void 0&&(F=G())!=null,Y=typeof Z==="function"&&!!Z(void 0)||typeof Z!=="function"&&!!Z;if(I)A[D]=F;else if(Y)A[D]=G()}else{let F=Z===void 0&&G!=null,I=typeof Z==="function"&&!!Z(G)||typeof Z!=="function"&&!!Z;if(F||I)A[D]=G}},"applyInstruction"),Cw4=j2((A)=>A!=null,"nonNullish"),Kw4=j2((A)=>A,"pass");function K72(A){if(A.includes(",")||A.includes('"'))A=`"${A.replace(/"/g,"\\\"")}"`;return A}j2(K72,"quoteHeader");var Hw4=j2((A)=>{if(A!==A)return"NaN";switch(A){case 1/0:return"Infinity";case-1/0:return"-Infinity";default:return A}},"serializeFloat"),zw4=j2((A)=>A.toISOString().replace(".000Z","Z"),"serializeDateTime"),W50=j2((A)=>{if(A==null)return{};if(Array.isArray(A))return A.filter((B)=>B!=null).map(W50);if(typeof A==="object"){let B={};for(let Q of Object.keys(A)){if(A[Q]==null)continue;B[Q]=W50(A[Q])}return B}return A},"_json");function H72(A,B,Q){if(Q<=0||!Number.isInteger(Q))throw new Error("Invalid number of delimiters ("+Q+") for splitEvery.");let D=A.split(B);if(Q===1)return D;let Z=[],G="";for(let F=0;F<D.length;F++){if(G==="")G=D[F];else G+=B+D[F];if((F+1)%Q===0)Z.push(G),G=""}if(G!=="")Z.push(G);return Z}j2(H72,"splitEvery");var Ew4=j2((A)=>{let B=A.length,Q=[],D=!1,Z=void 0,G=0;for(let F=0;F<B;++F){let I=A[F];switch(I){case'"':if(Z!=="\\")D=!D;break;case",":if(!D)Q.push(A.slice(G,F)),G=F+1;break;default:}Z=I}return Q.push(A.slice(G)),Q.map((F)=>{F=F.trim();let I=F.length;if(I<2)return F;if(F[0]==='"'&&F[I-1]==='"')F=F.slice(1,I-1);return F.replace(/\\"/g,'"')})},"splitHeader")});
var DVB=E((BVB)=>{Object.defineProperty(BVB,"__esModule",{value:!0});BVB.convertToBuffer=void 0;var Ll6=AVB(),Ml6=typeof Buffer!=="undefined"&&Buffer.from?function(A){return Buffer.from(A,"utf8")}:Ll6.fromUtf8;function Rl6(A){if(A instanceof Uint8Array)return A;if(typeof A==="string")return Ml6(A);if(ArrayBuffer.isView(A))return new Uint8Array(A.buffer,A.byteOffset,A.byteLength/Uint8Array.BYTES_PER_ELEMENT);return new Uint8Array(A)}BVB.convertToBuffer=Rl6});
var E30=E((FU5,BY2)=>{var{defineProperty:cN1,getOwnPropertyDescriptor:gO4,getOwnPropertyNames:sI2}=Object,uO4=Object.prototype.hasOwnProperty,lN1=(A,B)=>cN1(A,"name",{value:B,configurable:!0}),mO4=(A,B)=>function Q(){return A&&(B=A[sI2(A)[0]](A=0)),B},rI2=(A,B)=>{for(var Q in B)cN1(A,Q,{get:B[Q],enumerable:!0})},dO4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of sI2(B))if(!uO4.call(A,Z)&&Z!==Q)cN1(A,Z,{get:()=>B[Z],enumerable:!(D=gO4(B,Z))||D.enumerable})}return A},cO4=(A)=>dO4(cN1({},"__esModule",{value:!0}),A),oI2={};rI2(oI2,{GetRoleCredentialsCommand:()=>z30.GetRoleCredentialsCommand,SSOClient:()=>z30.SSOClient});var z30,lO4=mO4({"src/loadSso.ts"(){z30=UG2()}}),tI2={};rI2(tI2,{fromSSO:()=>iO4,isSsoProfile:()=>eI2,validateSsoProfile:()=>AY2});BY2.exports=cO4(tI2);var eI2=lN1((A)=>A&&(typeof A.sso_start_url==="string"||typeof A.sso_account_id==="string"||typeof A.sso_session==="string"||typeof A.sso_region==="string"||typeof A.sso_role_name==="string"),"isSsoProfile"),nI2=zL(),pO4=iI2(),gw=Q9(),dN1=D3(),g61=!1,aI2=lN1(async({ssoStartUrl:A,ssoSession:B,ssoAccountId:Q,ssoRegion:D,ssoRoleName:Z,ssoClient:G,clientConfig:F,parentClientConfig:I,profile:Y,logger:W})=>{let J,X="To refresh this SSO session run aws sso login with the corresponding profile.";if(B)try{let f=await pO4.fromSso({profile:Y})();J={accessToken:f.token,expiresAt:new Date(f.expiration).toISOString()}}catch(f){throw new gw.CredentialsProviderError(f.message,{tryNextLink:g61,logger:W})}else try{J=await dN1.getSSOTokenFromFile(A)}catch(f){throw new gw.CredentialsProviderError("The SSO session associated with this profile is invalid. To refresh this SSO session run aws sso login with the corresponding profile.",{tryNextLink:g61,logger:W})}if(new Date(J.expiresAt).getTime()-Date.now()<=0)throw new gw.CredentialsProviderError("The SSO session associated with this profile has expired. To refresh this SSO session run aws sso login with the corresponding profile.",{tryNextLink:g61,logger:W});let{accessToken:V}=J,{SSOClient:C,GetRoleCredentialsCommand:K}=await Promise.resolve().then(()=>(lO4(),oI2)),H=G||new C(Object.assign({},F??{},{logger:F?.logger??I?.logger,region:F?.region??D})),z;try{z=await H.send(new K({accountId:Q,roleName:Z,accessToken:V}))}catch(f){throw new gw.CredentialsProviderError(f,{tryNextLink:g61,logger:W})}let{roleCredentials:{accessKeyId:$,secretAccessKey:L,sessionToken:N,expiration:O,credentialScope:R,accountId:T}={}}=z;if(!$||!L||!N||!O)throw new gw.CredentialsProviderError("SSO returns an invalid temporary credential.",{tryNextLink:g61,logger:W});let j={accessKeyId:$,secretAccessKey:L,sessionToken:N,expiration:new Date(O),...R&&{credentialScope:R},...T&&{accountId:T}};if(B)nI2.setCredentialFeature(j,"CREDENTIALS_SSO","s");else nI2.setCredentialFeature(j,"CREDENTIALS_SSO_LEGACY","u");return j},"resolveSSOCredentials"),AY2=lN1((A,B)=>{let{sso_start_url:Q,sso_account_id:D,sso_region:Z,sso_role_name:G}=A;if(!Q||!D||!Z||!G)throw new gw.CredentialsProviderError(`Profile is configured with invalid SSO credentials. Required parameters "sso_account_id", "sso_region", "sso_role_name", "sso_start_url". Got ${Object.keys(A).join(", ")}
Reference: https://docs.aws.amazon.com/cli/latest/userguide/cli-configure-sso.html`,{tryNextLink:!1,logger:B});return A},"validateSsoProfile"),iO4=lN1((A={})=>async({callerClientConfig:B}={})=>{A.logger?.debug("@aws-sdk/credential-provider-sso - fromSSO");let{ssoStartUrl:Q,ssoAccountId:D,ssoRegion:Z,ssoRoleName:G,ssoSession:F}=A,{ssoClient:I}=A,Y=dN1.getProfileName({profile:A.profile??B?.profile});if(!Q&&!D&&!Z&&!G&&!F){let J=(await dN1.parseKnownFiles(A))[Y];if(!J)throw new gw.CredentialsProviderError(`Profile ${Y} was not found.`,{logger:A.logger});if(!eI2(J))throw new gw.CredentialsProviderError(`Profile ${Y} is not configured with SSO credentials.`,{logger:A.logger});if(J?.sso_session){let $=(await dN1.loadSsoSessionData(A))[J.sso_session],L=` configurations in profile ${Y} and sso-session ${J.sso_session}`;if(Z&&Z!==$.sso_region)throw new gw.CredentialsProviderError("Conflicting SSO region"+L,{tryNextLink:!1,logger:A.logger});if(Q&&Q!==$.sso_start_url)throw new gw.CredentialsProviderError("Conflicting SSO start_url"+L,{tryNextLink:!1,logger:A.logger});J.sso_region=$.sso_region,J.sso_start_url=$.sso_start_url}let{sso_start_url:X,sso_account_id:V,sso_region:C,sso_role_name:K,sso_session:H}=AY2(J,A.logger);return aI2({ssoStartUrl:X,ssoSession:H,ssoAccountId:V,ssoRegion:C,ssoRoleName:K,ssoClient:I,clientConfig:A.clientConfig,parentClientConfig:A.parentClientConfig,profile:Y})}else if(!Q||!D||!Z||!G)throw new gw.CredentialsProviderError('Incomplete configuration. The fromSSO() argument hash must include "ssoStartUrl", "ssoAccountId", "ssoRegion", "ssoRoleName"',{tryNextLink:!1,logger:A.logger});else return aI2({ssoStartUrl:Q,ssoSession:F,ssoAccountId:D,ssoRegion:Z,ssoRoleName:G,ssoClient:I,clientConfig:A.clientConfig,parentClientConfig:A.parentClientConfig,profile:Y})},"fromSSO")});
var E82=E((zz5,z82)=>{var{defineProperty:Rq1,getOwnPropertyDescriptor:tK4,getOwnPropertyNames:eK4}=Object,AH4=Object.prototype.hasOwnProperty,uk=(A,B)=>Rq1(A,"name",{value:B,configurable:!0}),BH4=(A,B)=>{for(var Q in B)Rq1(A,Q,{get:B[Q],enumerable:!0})},QH4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of eK4(B))if(!AH4.call(A,Z)&&Z!==Q)Rq1(A,Z,{get:()=>B[Z],enumerable:!(D=tK4(B,Z))||D.enumerable})}return A},DH4=(A)=>QH4(Rq1({},"__esModule",{value:!0}),A),V82={};BH4(V82,{Field:()=>FH4,Fields:()=>IH4,HttpRequest:()=>YH4,HttpResponse:()=>WH4,IHttpRequest:()=>C82.HttpRequest,getHttpHandlerExtensionConfiguration:()=>ZH4,isValidHostname:()=>H82,resolveHttpHandlerRuntimeConfig:()=>GH4});z82.exports=DH4(V82);var ZH4=uk((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),GH4=uk((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),C82=X82(),FH4=class{static{uk(this,"Field")}constructor({name:A,kind:B=C82.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},IH4=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{uk(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},YH4=class A{static{uk(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=K82(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function K82(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}uk(K82,"cloneQuery");var WH4=class{static{uk(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function H82(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}uk(H82,"isValidHostname")});
var EVB=E((HVB)=>{Object.defineProperty(HVB,"__esModule",{value:!0});HVB.AwsCrc32=void 0;var CVB=pz0(),sz0=az0(),KVB=ek1(),xl6=function(){function A(){this.crc32=new KVB.Crc32}return A.prototype.update=function(B){if(sz0.isEmptyData(B))return;this.crc32.update(sz0.convertToBuffer(B))},A.prototype.digest=function(){return CVB.__awaiter(this,void 0,void 0,function(){return CVB.__generator(this,function(B){return[2,sz0.numToUint8(this.crc32.digest())]})})},A.prototype.reset=function(){this.crc32=new KVB.Crc32},A}();HVB.AwsCrc32=xl6});
var F70=E((ML)=>{var eP4=ML&&ML.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),AS4=ML&&ML.__setModuleDefault||(Object.create?function(A,B){Object.defineProperty(A,"default",{enumerable:!0,value:B})}:function(A,B){A.default=B}),BS4=ML&&ML.__importStar||function(A){if(A&&A.__esModule)return A;var B={};if(A!=null){for(var Q in A)if(Q!=="default"&&Object.prototype.hasOwnProperty.call(A,Q))eP4(B,A,Q)}return AS4(B,A),B};Object.defineProperty(ML,"__esModule",{value:!0});ML.fromWebToken=void 0;var QS4=(A)=>async(B)=>{A.logger?.debug("@aws-sdk/credential-provider-web-identity - fromWebToken");let{roleArn:Q,roleSessionName:D,webIdentityToken:Z,providerId:G,policyArns:F,policy:I,durationSeconds:Y}=A,{roleAssumerWithWebIdentity:W}=A;if(!W){let{getDefaultRoleAssumerWithWebIdentity:J}=await Promise.resolve().then(()=>BS4(Q70()));W=J({...A.clientConfig,credentialProviderLogger:A.logger,parentClientConfig:{...B?.callerClientConfig,...A.parentClientConfig}},A.clientPlugins)}return W({RoleArn:Q,RoleSessionName:D??`aws-sdk-js-session-${Date.now()}`,WebIdentityToken:Z,ProviderId:G,PolicyArns:F,Policy:I,DurationSeconds:Y})};ML.fromWebToken=QS4});
var FI2=E((ZI2)=>{Object.defineProperty(ZI2,"__esModule",{value:!0});ZI2.getRuntimeConfig=void 0;var ER4=Z30(),UR4=ER4.__importDefault(G30()),wR4=KI(),BI2=N61(),hN1=K4(),$R4=gG(),QI2=u4(),cs=JD(),DI2=k3(),qR4=uG(),NR4=sZ(),LR4=AI2(),MR4=XZ(),RR4=mG(),OR4=XZ(),TR4=(A)=>{OR4.emitWarningIfUnsupportedVersion(process.version);let B=RR4.resolveDefaultsModeConfig(A),Q=()=>B().then(MR4.loadConfigsForDefaultMode),D=LR4.getRuntimeConfig(A);wR4.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile};return{...D,...A,runtime:"node",defaultsMode:B,bodyLengthChecker:A?.bodyLengthChecker??qR4.calculateBodyLength,defaultUserAgentProvider:A?.defaultUserAgentProvider??BI2.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:UR4.default.version}),maxAttempts:A?.maxAttempts??cs.loadConfig(QI2.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??cs.loadConfig(hN1.NODE_REGION_CONFIG_OPTIONS,{...hN1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:DI2.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??cs.loadConfig({...QI2.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||NR4.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??$R4.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??DI2.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??cs.loadConfig(hN1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??cs.loadConfig(hN1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??cs.loadConfig(BI2.NODE_APP_ID_CONFIG_OPTIONS,Z)}};ZI2.getRuntimeConfig=TR4});
var FVB=E((ZVB)=>{Object.defineProperty(ZVB,"__esModule",{value:!0});ZVB.isEmptyData=void 0;function Ol6(A){if(typeof A==="string")return A.length===0;return A.byteLength===0}ZVB.isEmptyData=Ol6});
var G30=E((cE5,aM4)=>{aM4.exports={name:"@aws-sdk/nested-clients",version:"3.797.0",description:"Nested clients for AWS SDK packages.",main:"./dist-cjs/index.js",module:"./dist-es/index.js",types:"./dist-types/index.d.ts",scripts:{build:"yarn lint && concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'","build:cjs":"node ../../scripts/compilation/inline nested-clients","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4",clean:"rimraf ./dist-* && rimraf *.tsbuildinfo",lint:"node ../../scripts/validation/submodules-linter.js --pkg nested-clients",test:"yarn g:vitest run","test:watch":"yarn g:vitest watch"},engines:{node:">=18.0.0"},author:{name:"AWS SDK for JavaScript Team",url:"https://aws.amazon.com/javascript/"},license:"Apache-2.0",dependencies:{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.796.0","@aws-sdk/middleware-host-header":"3.775.0","@aws-sdk/middleware-logger":"3.775.0","@aws-sdk/middleware-recursion-detection":"3.775.0","@aws-sdk/middleware-user-agent":"3.796.0","@aws-sdk/region-config-resolver":"3.775.0","@aws-sdk/types":"3.775.0","@aws-sdk/util-endpoints":"3.787.0","@aws-sdk/util-user-agent-browser":"3.775.0","@aws-sdk/util-user-agent-node":"3.796.0","@smithy/config-resolver":"^4.1.0","@smithy/core":"^3.2.0","@smithy/fetch-http-handler":"^5.0.2","@smithy/hash-node":"^4.0.2","@smithy/invalid-dependency":"^4.0.2","@smithy/middleware-content-length":"^4.0.2","@smithy/middleware-endpoint":"^4.1.0","@smithy/middleware-retry":"^4.1.0","@smithy/middleware-serde":"^4.0.3","@smithy/middleware-stack":"^4.0.2","@smithy/node-config-provider":"^4.0.2","@smithy/node-http-handler":"^4.0.4","@smithy/protocol-http":"^5.1.0","@smithy/smithy-client":"^4.2.0","@smithy/types":"^4.2.0","@smithy/url-parser":"^4.0.2","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.8","@smithy/util-defaults-mode-node":"^4.0.8","@smithy/util-endpoints":"^3.0.2","@smithy/util-middleware":"^4.0.2","@smithy/util-retry":"^4.0.2","@smithy/util-utf8":"^4.0.0",tslib:"^2.6.2"},devDependencies:{concurrently:"7.0.0","downlevel-dts":"0.10.1",rimraf:"3.0.2",typescript:"~5.2.2"},typesVersions:{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},files:["./sso-oidc.d.ts","./sso-oidc.js","./sts.d.ts","./sts.js","dist-*/**"],browser:{"./dist-es/submodules/sso-oidc/runtimeConfig":"./dist-es/submodules/sso-oidc/runtimeConfig.browser","./dist-es/submodules/sts/runtimeConfig":"./dist-es/submodules/sts/runtimeConfig.browser"},"react-native":{},homepage:"https://github.com/aws/aws-sdk-js-v3/tree/main/packages/nested-clients",repository:{type:"git",url:"https://github.com/aws/aws-sdk-js-v3.git",directory:"packages/nested-clients"},exports:{"./sso-oidc":{types:"./dist-types/submodules/sso-oidc/index.d.ts",module:"./dist-es/submodules/sso-oidc/index.js",node:"./dist-cjs/submodules/sso-oidc/index.js",import:"./dist-es/submodules/sso-oidc/index.js",require:"./dist-cjs/submodules/sso-oidc/index.js"},"./sts":{types:"./dist-types/submodules/sts/index.d.ts",module:"./dist-es/submodules/sts/index.js",node:"./dist-cjs/submodules/sts/index.js",import:"./dist-es/submodules/sts/index.js",require:"./dist-cjs/submodules/sts/index.js"}}}});
var G70=E((qU5,mW2)=>{var{defineProperty:nN1,getOwnPropertyDescriptor:mP4,getOwnPropertyNames:dP4}=Object,cP4=Object.prototype.hasOwnProperty,Z70=(A,B)=>nN1(A,"name",{value:B,configurable:!0}),lP4=(A,B)=>{for(var Q in B)nN1(A,Q,{get:B[Q],enumerable:!0})},pP4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of dP4(B))if(!cP4.call(A,Z)&&Z!==Q)nN1(A,Z,{get:()=>B[Z],enumerable:!(D=mP4(B,Z))||D.enumerable})}return A},iP4=(A)=>pP4(nN1({},"__esModule",{value:!0}),A),uW2={};lP4(uW2,{fromProcess:()=>tP4});mW2.exports=iP4(uW2);var gW2=D3(),D70=Q9(),nP4=J1("child_process"),aP4=J1("util"),sP4=zL(),rP4=Z70((A,B,Q)=>{if(B.Version!==1)throw Error(`Profile ${A} credential_process did not return Version 1.`);if(B.AccessKeyId===void 0||B.SecretAccessKey===void 0)throw Error(`Profile ${A} credential_process returned invalid credentials.`);if(B.Expiration){let G=new Date;if(new Date(B.Expiration)<G)throw Error(`Profile ${A} credential_process returned expired credentials.`)}let D=B.AccountId;if(!D&&Q?.[A]?.aws_account_id)D=Q[A].aws_account_id;let Z={accessKeyId:B.AccessKeyId,secretAccessKey:B.SecretAccessKey,...B.SessionToken&&{sessionToken:B.SessionToken},...B.Expiration&&{expiration:new Date(B.Expiration)},...B.CredentialScope&&{credentialScope:B.CredentialScope},...D&&{accountId:D}};return sP4.setCredentialFeature(Z,"CREDENTIALS_PROCESS","w"),Z},"getValidatedProcessCredentials"),oP4=Z70(async(A,B,Q)=>{let D=B[A];if(B[A]){let Z=D.credential_process;if(Z!==void 0){let G=aP4.promisify(nP4.exec);try{let{stdout:F}=await G(Z),I;try{I=JSON.parse(F.trim())}catch{throw Error(`Profile ${A} credential_process returned invalid JSON.`)}return rP4(A,I,B)}catch(F){throw new D70.CredentialsProviderError(F.message,{logger:Q})}}else throw new D70.CredentialsProviderError(`Profile ${A} did not contain credential_process.`,{logger:Q})}else throw new D70.CredentialsProviderError(`Profile ${A} could not be found in shared credentials file.`,{logger:Q})},"resolveProcessCredentials"),tP4=Z70((A={})=>async({callerClientConfig:B}={})=>{A.logger?.debug("@aws-sdk/credential-provider-process - fromProcess");let Q=await gW2.parseKnownFiles(A);return oP4(gW2.getProfileName({profile:A.profile??B?.profile}),Q,A.logger)},"fromProcess")});
var H61=E((bz5,I32)=>{var{defineProperty:cq1,getOwnPropertyDescriptor:az4,getOwnPropertyNames:sz4}=Object,rz4=Object.prototype.hasOwnProperty,ck=(A,B)=>cq1(A,"name",{value:B,configurable:!0}),oz4=(A,B)=>{for(var Q in B)cq1(A,Q,{get:B[Q],enumerable:!0})},tz4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of sz4(B))if(!rz4.call(A,Z)&&Z!==Q)cq1(A,Z,{get:()=>B[Z],enumerable:!(D=az4(B,Z))||D.enumerable})}return A},ez4=(A)=>tz4(cq1({},"__esModule",{value:!0}),A),D32={};oz4(D32,{Field:()=>QE4,Fields:()=>DE4,HttpRequest:()=>ZE4,HttpResponse:()=>GE4,IHttpRequest:()=>Z32.HttpRequest,getHttpHandlerExtensionConfiguration:()=>AE4,isValidHostname:()=>F32,resolveHttpHandlerRuntimeConfig:()=>BE4});I32.exports=ez4(D32);var AE4=ck((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),BE4=ck((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),Z32=c80(),QE4=class{static{ck(this,"Field")}constructor({name:A,kind:B=Z32.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},DE4=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{ck(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},ZE4=class A{static{ck(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=G32(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function G32(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}ck(G32,"cloneQuery");var GE4=class{static{ck(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function F32(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}ck(F32,"isValidHostname")});
var HCB=E((CCB)=>{Object.defineProperty(CCB,"__esModule",{value:!0});CCB.fromBase64=void 0;var In6=YD(),Yn6=/^[A-Za-z0-9+/]*={0,2}$/,Wn6=(A)=>{if(A.length*3%4!==0)throw new TypeError("Incorrect padding on base64 string.");if(!Yn6.exec(A))throw new TypeError("Invalid base64 string.");let B=In6.fromString(A,"base64");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength)};CCB.fromBase64=Wn6});
var I62=E((tH5,zq1)=>{var _42,x42,v42,b42,f42,h42,g42,u42,m42,d42,c42,l42,p42,Kq1,L80,i42,n42,a42,Ks,s42,r42,o42,t42,e42,A62,B62,Q62,D62,Hq1,Z62,G62,F62;(function(A){var B=typeof global==="object"?global:typeof self==="object"?self:typeof this==="object"?this:{};if(typeof define==="function"&&define.amd)define("tslib",["exports"],function(D){A(Q(B,Q(D)))});else if(typeof zq1==="object"&&typeof tH5==="object")A(Q(B,Q(tH5)));else A(Q(B));function Q(D,Z){if(D!==B)if(typeof Object.create==="function")Object.defineProperty(D,"__esModule",{value:!0});else D.__esModule=!0;return function(G,F){return D[G]=Z?Z(G,F):F}}})(function(A){var B=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(G,F){G.__proto__=F}||function(G,F){for(var I in F)if(Object.prototype.hasOwnProperty.call(F,I))G[I]=F[I]};_42=function(G,F){if(typeof F!=="function"&&F!==null)throw new TypeError("Class extends value "+String(F)+" is not a constructor or null");B(G,F);function I(){this.constructor=G}G.prototype=F===null?Object.create(F):(I.prototype=F.prototype,new I)},x42=Object.assign||function(G){for(var F,I=1,Y=arguments.length;I<Y;I++){F=arguments[I];for(var W in F)if(Object.prototype.hasOwnProperty.call(F,W))G[W]=F[W]}return G},v42=function(G,F){var I={};for(var Y in G)if(Object.prototype.hasOwnProperty.call(G,Y)&&F.indexOf(Y)<0)I[Y]=G[Y];if(G!=null&&typeof Object.getOwnPropertySymbols==="function"){for(var W=0,Y=Object.getOwnPropertySymbols(G);W<Y.length;W++)if(F.indexOf(Y[W])<0&&Object.prototype.propertyIsEnumerable.call(G,Y[W]))I[Y[W]]=G[Y[W]]}return I},b42=function(G,F,I,Y){var W=arguments.length,J=W<3?F:Y===null?Y=Object.getOwnPropertyDescriptor(F,I):Y,X;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")J=Reflect.decorate(G,F,I,Y);else for(var V=G.length-1;V>=0;V--)if(X=G[V])J=(W<3?X(J):W>3?X(F,I,J):X(F,I))||J;return W>3&&J&&Object.defineProperty(F,I,J),J},f42=function(G,F){return function(I,Y){F(I,Y,G)}},h42=function(G,F,I,Y,W,J){function X(T){if(T!==void 0&&typeof T!=="function")throw new TypeError("Function expected");return T}var V=Y.kind,C=V==="getter"?"get":V==="setter"?"set":"value",K=!F&&G?Y.static?G:G.prototype:null,H=F||(K?Object.getOwnPropertyDescriptor(K,Y.name):{}),z,$=!1;for(var L=I.length-1;L>=0;L--){var N={};for(var O in Y)N[O]=O==="access"?{}:Y[O];for(var O in Y.access)N.access[O]=Y.access[O];N.addInitializer=function(T){if($)throw new TypeError("Cannot add initializers after decoration has completed");J.push(X(T||null))};var R=I[L](V==="accessor"?{get:H.get,set:H.set}:H[C],N);if(V==="accessor"){if(R===void 0)continue;if(R===null||typeof R!=="object")throw new TypeError("Object expected");if(z=X(R.get))H.get=z;if(z=X(R.set))H.set=z;if(z=X(R.init))W.unshift(z)}else if(z=X(R))if(V==="field")W.unshift(z);else H[C]=z}if(K)Object.defineProperty(K,Y.name,H);$=!0},g42=function(G,F,I){var Y=arguments.length>2;for(var W=0;W<F.length;W++)I=Y?F[W].call(G,I):F[W].call(G);return Y?I:void 0},u42=function(G){return typeof G==="symbol"?G:"".concat(G)},m42=function(G,F,I){if(typeof F==="symbol")F=F.description?"[".concat(F.description,"]"):"";return Object.defineProperty(G,"name",{configurable:!0,value:I?"".concat(I," ",F):F})},d42=function(G,F){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(G,F)},c42=function(G,F,I,Y){function W(J){return J instanceof I?J:new I(function(X){X(J)})}return new(I||(I=Promise))(function(J,X){function V(H){try{K(Y.next(H))}catch(z){X(z)}}function C(H){try{K(Y.throw(H))}catch(z){X(z)}}function K(H){H.done?J(H.value):W(H.value).then(V,C)}K((Y=Y.apply(G,F||[])).next())})},l42=function(G,F){var I={label:0,sent:function(){if(J[0]&1)throw J[1];return J[1]},trys:[],ops:[]},Y,W,J,X=Object.create((typeof Iterator==="function"?Iterator:Object).prototype);return X.next=V(0),X.throw=V(1),X.return=V(2),typeof Symbol==="function"&&(X[Symbol.iterator]=function(){return this}),X;function V(K){return function(H){return C([K,H])}}function C(K){if(Y)throw new TypeError("Generator is already executing.");while(X&&(X=0,K[0]&&(I=0)),I)try{if(Y=1,W&&(J=K[0]&2?W.return:K[0]?W.throw||((J=W.return)&&J.call(W),0):W.next)&&!(J=J.call(W,K[1])).done)return J;if(W=0,J)K=[K[0]&2,J.value];switch(K[0]){case 0:case 1:J=K;break;case 4:return I.label++,{value:K[1],done:!1};case 5:I.label++,W=K[1],K=[0];continue;case 7:K=I.ops.pop(),I.trys.pop();continue;default:if((J=I.trys,!(J=J.length>0&&J[J.length-1]))&&(K[0]===6||K[0]===2)){I=0;continue}if(K[0]===3&&(!J||K[1]>J[0]&&K[1]<J[3])){I.label=K[1];break}if(K[0]===6&&I.label<J[1]){I.label=J[1],J=K;break}if(J&&I.label<J[2]){I.label=J[2],I.ops.push(K);break}if(J[2])I.ops.pop();I.trys.pop();continue}K=F.call(G,I)}catch(H){K=[6,H],W=0}finally{Y=J=0}if(K[0]&5)throw K[1];return{value:K[0]?K[1]:void 0,done:!0}}},p42=function(G,F){for(var I in G)if(I!=="default"&&!Object.prototype.hasOwnProperty.call(F,I))Hq1(F,G,I)},Hq1=Object.create?function(G,F,I,Y){if(Y===void 0)Y=I;var W=Object.getOwnPropertyDescriptor(F,I);if(!W||("get"in W?!F.__esModule:W.writable||W.configurable))W={enumerable:!0,get:function(){return F[I]}};Object.defineProperty(G,Y,W)}:function(G,F,I,Y){if(Y===void 0)Y=I;G[Y]=F[I]},Kq1=function(G){var F=typeof Symbol==="function"&&Symbol.iterator,I=F&&G[F],Y=0;if(I)return I.call(G);if(G&&typeof G.length==="number")return{next:function(){if(G&&Y>=G.length)G=void 0;return{value:G&&G[Y++],done:!G}}};throw new TypeError(F?"Object is not iterable.":"Symbol.iterator is not defined.")},L80=function(G,F){var I=typeof Symbol==="function"&&G[Symbol.iterator];if(!I)return G;var Y=I.call(G),W,J=[],X;try{while((F===void 0||F-- >0)&&!(W=Y.next()).done)J.push(W.value)}catch(V){X={error:V}}finally{try{if(W&&!W.done&&(I=Y.return))I.call(Y)}finally{if(X)throw X.error}}return J},i42=function(){for(var G=[],F=0;F<arguments.length;F++)G=G.concat(L80(arguments[F]));return G},n42=function(){for(var G=0,F=0,I=arguments.length;F<I;F++)G+=arguments[F].length;for(var Y=Array(G),W=0,F=0;F<I;F++)for(var J=arguments[F],X=0,V=J.length;X<V;X++,W++)Y[W]=J[X];return Y},a42=function(G,F,I){if(I||arguments.length===2){for(var Y=0,W=F.length,J;Y<W;Y++)if(J||!(Y in F)){if(!J)J=Array.prototype.slice.call(F,0,Y);J[Y]=F[Y]}}return G.concat(J||Array.prototype.slice.call(F))},Ks=function(G){return this instanceof Ks?(this.v=G,this):new Ks(G)},s42=function(G,F,I){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var Y=I.apply(G,F||[]),W,J=[];return W=Object.create((typeof AsyncIterator==="function"?AsyncIterator:Object).prototype),V("next"),V("throw"),V("return",X),W[Symbol.asyncIterator]=function(){return this},W;function X(L){return function(N){return Promise.resolve(N).then(L,z)}}function V(L,N){if(Y[L]){if(W[L]=function(O){return new Promise(function(R,T){J.push([L,O,R,T])>1||C(L,O)})},N)W[L]=N(W[L])}}function C(L,N){try{K(Y[L](N))}catch(O){$(J[0][3],O)}}function K(L){L.value instanceof Ks?Promise.resolve(L.value.v).then(H,z):$(J[0][2],L)}function H(L){C("next",L)}function z(L){C("throw",L)}function $(L,N){if(L(N),J.shift(),J.length)C(J[0][0],J[0][1])}},r42=function(G){var F,I;return F={},Y("next"),Y("throw",function(W){throw W}),Y("return"),F[Symbol.iterator]=function(){return this},F;function Y(W,J){F[W]=G[W]?function(X){return(I=!I)?{value:Ks(G[W](X)),done:!1}:J?J(X):X}:J}},o42=function(G){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var F=G[Symbol.asyncIterator],I;return F?F.call(G):(G=typeof Kq1==="function"?Kq1(G):G[Symbol.iterator](),I={},Y("next"),Y("throw"),Y("return"),I[Symbol.asyncIterator]=function(){return this},I);function Y(J){I[J]=G[J]&&function(X){return new Promise(function(V,C){X=G[J](X),W(V,C,X.done,X.value)})}}function W(J,X,V,C){Promise.resolve(C).then(function(K){J({value:K,done:V})},X)}},t42=function(G,F){if(Object.defineProperty)Object.defineProperty(G,"raw",{value:F});else G.raw=F;return G};var Q=Object.create?function(G,F){Object.defineProperty(G,"default",{enumerable:!0,value:F})}:function(G,F){G.default=F},D=function(G){return D=Object.getOwnPropertyNames||function(F){var I=[];for(var Y in F)if(Object.prototype.hasOwnProperty.call(F,Y))I[I.length]=Y;return I},D(G)};e42=function(G){if(G&&G.__esModule)return G;var F={};if(G!=null){for(var I=D(G),Y=0;Y<I.length;Y++)if(I[Y]!=="default")Hq1(F,G,I[Y])}return Q(F,G),F},A62=function(G){return G&&G.__esModule?G:{default:G}},B62=function(G,F,I,Y){if(I==="a"&&!Y)throw new TypeError("Private accessor was defined without a getter");if(typeof F==="function"?G!==F||!Y:!F.has(G))throw new TypeError("Cannot read private member from an object whose class did not declare it");return I==="m"?Y:I==="a"?Y.call(G):Y?Y.value:F.get(G)},Q62=function(G,F,I,Y,W){if(Y==="m")throw new TypeError("Private method is not writable");if(Y==="a"&&!W)throw new TypeError("Private accessor was defined without a setter");if(typeof F==="function"?G!==F||!W:!F.has(G))throw new TypeError("Cannot write private member to an object whose class did not declare it");return Y==="a"?W.call(G,I):W?W.value=I:F.set(G,I),I},D62=function(G,F){if(F===null||typeof F!=="object"&&typeof F!=="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof G==="function"?F===G:G.has(F)},Z62=function(G,F,I){if(F!==null&&F!==void 0){if(typeof F!=="object"&&typeof F!=="function")throw new TypeError("Object expected.");var Y,W;if(I){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");Y=F[Symbol.asyncDispose]}if(Y===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");if(Y=F[Symbol.dispose],I)W=Y}if(typeof Y!=="function")throw new TypeError("Object not disposable.");if(W)Y=function(){try{W.call(this)}catch(J){return Promise.reject(J)}};G.stack.push({value:F,dispose:Y,async:I})}else if(I)G.stack.push({async:!0});return F};var Z=typeof SuppressedError==="function"?SuppressedError:function(G,F,I){var Y=new Error(I);return Y.name="SuppressedError",Y.error=G,Y.suppressed=F,Y};G62=function(G){function F(J){G.error=G.hasError?new Z(J,G.error,"An error was suppressed during disposal."):J,G.hasError=!0}var I,Y=0;function W(){while(I=G.stack.pop())try{if(!I.async&&Y===1)return Y=0,G.stack.push(I),Promise.resolve().then(W);if(I.dispose){var J=I.dispose.call(I.value);if(I.async)return Y|=2,Promise.resolve(J).then(W,function(X){return F(X),W()})}else Y|=1}catch(X){F(X)}if(Y===1)return G.hasError?Promise.reject(G.error):Promise.resolve();if(G.hasError)throw G.error}return W()},F62=function(G,F){if(typeof G==="string"&&/^\.\.?\//.test(G))return G.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,function(I,Y,W,J,X){return Y?F?".jsx":".js":W&&(!J||!X)?I:W+J+"."+X.toLowerCase()+"js"});return G},A("__extends",_42),A("__assign",x42),A("__rest",v42),A("__decorate",b42),A("__param",f42),A("__esDecorate",h42),A("__runInitializers",g42),A("__propKey",u42),A("__setFunctionName",m42),A("__metadata",d42),A("__awaiter",c42),A("__generator",l42),A("__exportStar",p42),A("__createBinding",Hq1),A("__values",Kq1),A("__read",L80),A("__spread",i42),A("__spreadArrays",n42),A("__spreadArray",a42),A("__await",Ks),A("__asyncGenerator",s42),A("__asyncDelegator",r42),A("__asyncValues",o42),A("__makeTemplateObject",t42),A("__importStar",e42),A("__importDefault",A62),A("__classPrivateFieldGet",B62),A("__classPrivateFieldSet",Q62),A("__classPrivateFieldIn",D62),A("__addDisposableResource",Z62),A("__disposeResources",G62),A("__rewriteRelativeImportExtension",F62)})});
var J32=E((uz5,W32)=>{var{defineProperty:lq1,getOwnPropertyDescriptor:FE4,getOwnPropertyNames:IE4}=Object,YE4=Object.prototype.hasOwnProperty,WE4=(A,B)=>lq1(A,"name",{value:B,configurable:!0}),JE4=(A,B)=>{for(var Q in B)lq1(A,Q,{get:B[Q],enumerable:!0})},XE4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of IE4(B))if(!YE4.call(A,Z)&&Z!==Q)lq1(A,Z,{get:()=>B[Z],enumerable:!(D=FE4(B,Z))||D.enumerable})}return A},VE4=(A)=>XE4(lq1({},"__esModule",{value:!0}),A),Y32={};JE4(Y32,{isArrayBuffer:()=>CE4});W32.exports=VE4(Y32);var CE4=WE4((A)=>typeof ArrayBuffer==="function"&&A instanceof ArrayBuffer||Object.prototype.toString.call(A)==="[object ArrayBuffer]","isArrayBuffer")});
var J62=E((Y62)=>{Object.defineProperty(Y62,"__esModule",{value:!0});Y62.checkUrl=void 0;var vV4=Q9(),bV4="*************",fV4="*************3",hV4="[fd00:ec2::23]",gV4=(A,B)=>{if(A.protocol==="https:")return;if(A.hostname===bV4||A.hostname===fV4||A.hostname===hV4)return;if(A.hostname.includes("[")){if(A.hostname==="[::1]"||A.hostname==="[0000:0000:0000:0000:0000:0000:0000:0001]")return}else{if(A.hostname==="localhost")return;let Q=A.hostname.split("."),D=(Z)=>{let G=parseInt(Z,10);return 0<=G&&G<=255};if(Q[0]==="127"&&D(Q[1])&&D(Q[2])&&D(Q[3])&&Q.length===4)return}throw new vV4.CredentialsProviderError(`URL not accepted. It must either be HTTPS or match one of the following:
  - loopback CIDR *********/8 or [::1/128]
  - ECS container host *************
  - EKS container host *************3 or [fd00:ec2::23]`,{logger:B})};Y62.checkUrl=gV4});
var K30=E((BU5,hI2)=>{var{defineProperty:mN1,getOwnPropertyDescriptor:uR4,getOwnPropertyNames:mR4}=Object,dR4=Object.prototype.hasOwnProperty,e4=(A,B)=>mN1(A,"name",{value:B,configurable:!0}),cR4=(A,B)=>{for(var Q in B)mN1(A,Q,{get:B[Q],enumerable:!0})},lR4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of mR4(B))if(!dR4.call(A,Z)&&Z!==Q)mN1(A,Z,{get:()=>B[Z],enumerable:!(D=uR4(B,Z))||D.enumerable})}return A},pR4=(A)=>lR4(mN1({},"__esModule",{value:!0}),A),wI2={};cR4(wI2,{$Command:()=>NI2.Command,AccessDeniedException:()=>LI2,AuthorizationPendingException:()=>MI2,CreateTokenCommand:()=>bI2,CreateTokenRequestFilterSensitiveLog:()=>RI2,CreateTokenResponseFilterSensitiveLog:()=>OI2,ExpiredTokenException:()=>TI2,InternalServerException:()=>PI2,InvalidClientException:()=>SI2,InvalidGrantException:()=>jI2,InvalidRequestException:()=>yI2,InvalidScopeException:()=>kI2,SSOOIDC:()=>fI2,SSOOIDCClient:()=>qI2,SSOOIDCServiceException:()=>EK,SlowDownException:()=>_I2,UnauthorizedClientException:()=>xI2,UnsupportedGrantTypeException:()=>vI2,__Client:()=>$I2.Client});hI2.exports=pR4(wI2);var VI2=V61(),iR4=C61(),nR4=K61(),CI2=Rs(),aR4=K4(),V30=CB(),sR4=bG(),rR4=R6(),KI2=u4(),$I2=XZ(),HI2=Q30(),oR4=e4((A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,defaultSigningName:"sso-oauth"})},"resolveClientEndpointParameters"),tR4={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}},eR4=FI2(),zI2=S61(),EI2=uN1(),UI2=XZ(),AO4=e4((A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}},"getHttpAuthExtensionConfiguration"),BO4=e4((A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}},"resolveHttpAuthRuntimeConfig"),QO4=e4((A,B)=>{let Q=Object.assign(zI2.getAwsRegionExtensionConfiguration(A),UI2.getDefaultExtensionConfiguration(A),EI2.getHttpHandlerExtensionConfiguration(A),AO4(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,zI2.resolveAwsRegionExtensionConfiguration(Q),UI2.resolveDefaultRuntimeConfig(Q),EI2.resolveHttpHandlerRuntimeConfig(Q),BO4(Q))},"resolveRuntimeExtensions"),qI2=class extends $I2.Client{static{e4(this,"SSOOIDCClient")}config;constructor(...[A]){let B=eR4.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=oR4(B),D=CI2.resolveUserAgentConfig(Q),Z=KI2.resolveRetryConfig(D),G=aR4.resolveRegionConfig(Z),F=VI2.resolveHostHeaderConfig(G),I=rR4.resolveEndpointConfig(F),Y=HI2.resolveHttpAuthSchemeConfig(I),W=QO4(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(CI2.getUserAgentPlugin(this.config)),this.middlewareStack.use(KI2.getRetryPlugin(this.config)),this.middlewareStack.use(sR4.getContentLengthPlugin(this.config)),this.middlewareStack.use(VI2.getHostHeaderPlugin(this.config)),this.middlewareStack.use(iR4.getLoggerPlugin(this.config)),this.middlewareStack.use(nR4.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(V30.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:HI2.defaultSSOOIDCHttpAuthSchemeParametersProvider,identityProviderConfigProvider:e4(async(J)=>new V30.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials}),"identityProviderConfigProvider")})),this.middlewareStack.use(V30.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}},DO4=XZ(),ZO4=R6(),GO4=j3(),NI2=XZ(),ls=XZ(),FO4=XZ(),EK=class A extends FO4.ServiceException{static{e4(this,"SSOOIDCServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},LI2=class A extends EK{static{e4(this,"AccessDeniedException")}name="AccessDeniedException";$fault="client";error;error_description;constructor(B){super({name:"AccessDeniedException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},MI2=class A extends EK{static{e4(this,"AuthorizationPendingException")}name="AuthorizationPendingException";$fault="client";error;error_description;constructor(B){super({name:"AuthorizationPendingException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},RI2=e4((A)=>({...A,...A.clientSecret&&{clientSecret:ls.SENSITIVE_STRING},...A.refreshToken&&{refreshToken:ls.SENSITIVE_STRING},...A.codeVerifier&&{codeVerifier:ls.SENSITIVE_STRING}}),"CreateTokenRequestFilterSensitiveLog"),OI2=e4((A)=>({...A,...A.accessToken&&{accessToken:ls.SENSITIVE_STRING},...A.refreshToken&&{refreshToken:ls.SENSITIVE_STRING},...A.idToken&&{idToken:ls.SENSITIVE_STRING}}),"CreateTokenResponseFilterSensitiveLog"),TI2=class A extends EK{static{e4(this,"ExpiredTokenException")}name="ExpiredTokenException";$fault="client";error;error_description;constructor(B){super({name:"ExpiredTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},PI2=class A extends EK{static{e4(this,"InternalServerException")}name="InternalServerException";$fault="server";error;error_description;constructor(B){super({name:"InternalServerException",$fault:"server",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},SI2=class A extends EK{static{e4(this,"InvalidClientException")}name="InvalidClientException";$fault="client";error;error_description;constructor(B){super({name:"InvalidClientException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},jI2=class A extends EK{static{e4(this,"InvalidGrantException")}name="InvalidGrantException";$fault="client";error;error_description;constructor(B){super({name:"InvalidGrantException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},yI2=class A extends EK{static{e4(this,"InvalidRequestException")}name="InvalidRequestException";$fault="client";error;error_description;constructor(B){super({name:"InvalidRequestException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},kI2=class A extends EK{static{e4(this,"InvalidScopeException")}name="InvalidScopeException";$fault="client";error;error_description;constructor(B){super({name:"InvalidScopeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},_I2=class A extends EK{static{e4(this,"SlowDownException")}name="SlowDownException";$fault="client";error;error_description;constructor(B){super({name:"SlowDownException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},xI2=class A extends EK{static{e4(this,"UnauthorizedClientException")}name="UnauthorizedClientException";$fault="client";error;error_description;constructor(B){super({name:"UnauthorizedClientException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},vI2=class A extends EK{static{e4(this,"UnsupportedGrantTypeException")}name="UnsupportedGrantTypeException";$fault="client";error;error_description;constructor(B){super({name:"UnsupportedGrantTypeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},C30=KI(),IO4=CB(),NB=XZ(),YO4=e4(async(A,B)=>{let Q=IO4.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/token");let Z;return Z=JSON.stringify(NB.take(A,{clientId:[],clientSecret:[],code:[],codeVerifier:[],deviceCode:[],grantType:[],redirectUri:[],refreshToken:[],scope:e4((G)=>NB._json(G),"scope")})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateTokenCommand"),WO4=e4(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return JO4(A,B);let Q=NB.map({$metadata:rz(A)}),D=NB.expectNonNull(NB.expectObject(await C30.parseJsonBody(A.body,B)),"body"),Z=NB.take(D,{accessToken:NB.expectString,expiresIn:NB.expectInt32,idToken:NB.expectString,refreshToken:NB.expectString,tokenType:NB.expectString});return Object.assign(Q,Z),Q},"de_CreateTokenCommand"),JO4=e4(async(A,B)=>{let Q={...A,body:await C30.parseJsonErrorBody(A.body,B)},D=C30.loadRestJsonErrorCode(A,Q.body);switch(D){case"AccessDeniedException":case"com.amazonaws.ssooidc#AccessDeniedException":throw await VO4(Q,B);case"AuthorizationPendingException":case"com.amazonaws.ssooidc#AuthorizationPendingException":throw await CO4(Q,B);case"ExpiredTokenException":case"com.amazonaws.ssooidc#ExpiredTokenException":throw await KO4(Q,B);case"InternalServerException":case"com.amazonaws.ssooidc#InternalServerException":throw await HO4(Q,B);case"InvalidClientException":case"com.amazonaws.ssooidc#InvalidClientException":throw await zO4(Q,B);case"InvalidGrantException":case"com.amazonaws.ssooidc#InvalidGrantException":throw await EO4(Q,B);case"InvalidRequestException":case"com.amazonaws.ssooidc#InvalidRequestException":throw await UO4(Q,B);case"InvalidScopeException":case"com.amazonaws.ssooidc#InvalidScopeException":throw await wO4(Q,B);case"SlowDownException":case"com.amazonaws.ssooidc#SlowDownException":throw await $O4(Q,B);case"UnauthorizedClientException":case"com.amazonaws.ssooidc#UnauthorizedClientException":throw await qO4(Q,B);case"UnsupportedGrantTypeException":case"com.amazonaws.ssooidc#UnsupportedGrantTypeException":throw await NO4(Q,B);default:let Z=Q.body;return XO4({output:A,parsedBody:Z,errorCode:D})}},"de_CommandError"),XO4=NB.withBaseException(EK),VO4=e4(async(A,B)=>{let Q=NB.map({}),D=A.body,Z=NB.take(D,{error:NB.expectString,error_description:NB.expectString});Object.assign(Q,Z);let G=new LI2({$metadata:rz(A),...Q});return NB.decorateServiceException(G,A.body)},"de_AccessDeniedExceptionRes"),CO4=e4(async(A,B)=>{let Q=NB.map({}),D=A.body,Z=NB.take(D,{error:NB.expectString,error_description:NB.expectString});Object.assign(Q,Z);let G=new MI2({$metadata:rz(A),...Q});return NB.decorateServiceException(G,A.body)},"de_AuthorizationPendingExceptionRes"),KO4=e4(async(A,B)=>{let Q=NB.map({}),D=A.body,Z=NB.take(D,{error:NB.expectString,error_description:NB.expectString});Object.assign(Q,Z);let G=new TI2({$metadata:rz(A),...Q});return NB.decorateServiceException(G,A.body)},"de_ExpiredTokenExceptionRes"),HO4=e4(async(A,B)=>{let Q=NB.map({}),D=A.body,Z=NB.take(D,{error:NB.expectString,error_description:NB.expectString});Object.assign(Q,Z);let G=new PI2({$metadata:rz(A),...Q});return NB.decorateServiceException(G,A.body)},"de_InternalServerExceptionRes"),zO4=e4(async(A,B)=>{let Q=NB.map({}),D=A.body,Z=NB.take(D,{error:NB.expectString,error_description:NB.expectString});Object.assign(Q,Z);let G=new SI2({$metadata:rz(A),...Q});return NB.decorateServiceException(G,A.body)},"de_InvalidClientExceptionRes"),EO4=e4(async(A,B)=>{let Q=NB.map({}),D=A.body,Z=NB.take(D,{error:NB.expectString,error_description:NB.expectString});Object.assign(Q,Z);let G=new jI2({$metadata:rz(A),...Q});return NB.decorateServiceException(G,A.body)},"de_InvalidGrantExceptionRes"),UO4=e4(async(A,B)=>{let Q=NB.map({}),D=A.body,Z=NB.take(D,{error:NB.expectString,error_description:NB.expectString});Object.assign(Q,Z);let G=new yI2({$metadata:rz(A),...Q});return NB.decorateServiceException(G,A.body)},"de_InvalidRequestExceptionRes"),wO4=e4(async(A,B)=>{let Q=NB.map({}),D=A.body,Z=NB.take(D,{error:NB.expectString,error_description:NB.expectString});Object.assign(Q,Z);let G=new kI2({$metadata:rz(A),...Q});return NB.decorateServiceException(G,A.body)},"de_InvalidScopeExceptionRes"),$O4=e4(async(A,B)=>{let Q=NB.map({}),D=A.body,Z=NB.take(D,{error:NB.expectString,error_description:NB.expectString});Object.assign(Q,Z);let G=new _I2({$metadata:rz(A),...Q});return NB.decorateServiceException(G,A.body)},"de_SlowDownExceptionRes"),qO4=e4(async(A,B)=>{let Q=NB.map({}),D=A.body,Z=NB.take(D,{error:NB.expectString,error_description:NB.expectString});Object.assign(Q,Z);let G=new xI2({$metadata:rz(A),...Q});return NB.decorateServiceException(G,A.body)},"de_UnauthorizedClientExceptionRes"),NO4=e4(async(A,B)=>{let Q=NB.map({}),D=A.body,Z=NB.take(D,{error:NB.expectString,error_description:NB.expectString});Object.assign(Q,Z);let G=new vI2({$metadata:rz(A),...Q});return NB.decorateServiceException(G,A.body)},"de_UnsupportedGrantTypeExceptionRes"),rz=e4((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),bI2=class extends NI2.Command.classBuilder().ep(tR4).m(function(A,B,Q,D){return[GO4.getSerdePlugin(Q,this.serialize,this.deserialize),ZO4.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSSOOIDCService","CreateToken",{}).n("SSOOIDCClient","CreateTokenCommand").f(RI2,OI2).ser(YO4).de(WO4).build(){static{e4(this,"CreateTokenCommand")}},LO4={CreateTokenCommand:bI2},fI2=class extends qI2{static{e4(this,"SSOOIDC")}};DO4.createAggregatedClient(LO4,fI2)});
var K32=E((mz5,C32)=>{var{defineProperty:pq1,getOwnPropertyDescriptor:KE4,getOwnPropertyNames:HE4}=Object,zE4=Object.prototype.hasOwnProperty,l80=(A,B)=>pq1(A,"name",{value:B,configurable:!0}),EE4=(A,B)=>{for(var Q in B)pq1(A,Q,{get:B[Q],enumerable:!0})},UE4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of HE4(B))if(!zE4.call(A,Z)&&Z!==Q)pq1(A,Z,{get:()=>B[Z],enumerable:!(D=KE4(B,Z))||D.enumerable})}return A},wE4=(A)=>UE4(pq1({},"__esModule",{value:!0}),A),X32={};EE4(X32,{escapeUri:()=>V32,escapeUriPath:()=>qE4});C32.exports=wE4(X32);var V32=l80((A)=>encodeURIComponent(A).replace(/[!'()*]/g,$E4),"escapeUri"),$E4=l80((A)=>`%${A.charCodeAt(0).toString(16).toUpperCase()}`,"hexEncode"),qE4=l80((A)=>A.split("/").map(V32).join("/"),"escapeUriPath")});
var K61=E((Tz5,i82)=>{var{defineProperty:_q1,getOwnPropertyDescriptor:aH4,getOwnPropertyNames:sH4}=Object,rH4=Object.prototype.hasOwnProperty,kq1=(A,B)=>_q1(A,"name",{value:B,configurable:!0}),oH4=(A,B)=>{for(var Q in B)_q1(A,Q,{get:B[Q],enumerable:!0})},tH4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of sH4(B))if(!rH4.call(A,Z)&&Z!==Q)_q1(A,Z,{get:()=>B[Z],enumerable:!(D=aH4(B,Z))||D.enumerable})}return A},eH4=(A)=>tH4(_q1({},"__esModule",{value:!0}),A),c82={};oH4(c82,{addRecursionDetectionMiddlewareOptions:()=>p82,getRecursionDetectionPlugin:()=>Dz4,recursionDetectionMiddleware:()=>l82});i82.exports=eH4(c82);var Az4=d82(),m80="X-Amzn-Trace-Id",Bz4="AWS_LAMBDA_FUNCTION_NAME",Qz4="_X_AMZN_TRACE_ID",l82=kq1((A)=>(B)=>async(Q)=>{let{request:D}=Q;if(!Az4.HttpRequest.isInstance(D)||A.runtime!=="node")return B(Q);let Z=Object.keys(D.headers??{}).find((Y)=>Y.toLowerCase()===m80.toLowerCase())??m80;if(D.headers.hasOwnProperty(Z))return B(Q);let G=process.env[Bz4],F=process.env[Qz4],I=kq1((Y)=>typeof Y==="string"&&Y.length>0,"nonEmptyString");if(I(G)&&I(F))D.headers[m80]=F;return B({...Q,request:D})},"recursionDetectionMiddleware"),p82={step:"build",tags:["RECURSION_DETECTION"],name:"recursionDetectionMiddleware",override:!0,priority:"low"},Dz4=kq1((A)=>({applyToStack:kq1((B)=>{B.add(l82(A),p82)},"applyToStack")}),"getRecursionDetectionPlugin")});
var KI=E((q61)=>{Object.defineProperty(q61,"__esModule",{value:!0});var H50=n52();H50.__exportStar(zL(),q61);H50.__exportStar(t32(),q61);H50.__exportStar(N72(),q61)});
var KXB=E((DA3,oc6)=>{oc6.exports={name:"@aws-sdk/client-bedrock-runtime",description:"AWS SDK for JavaScript Bedrock Runtime Client for Node.js, Browser and React Native",version:"3.797.0",scripts:{build:"concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'","build:cjs":"node ../../scripts/compilation/inline client-bedrock-runtime","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4",clean:"rimraf ./dist-* && rimraf *.tsbuildinfo","extract:docs":"api-extractor run --local","generate:client":"node ../../scripts/generate-clients/single-service --solo bedrock-runtime"},main:"./dist-cjs/index.js",types:"./dist-types/index.d.ts",module:"./dist-es/index.js",sideEffects:!1,dependencies:{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.796.0","@aws-sdk/credential-provider-node":"3.797.0","@aws-sdk/eventstream-handler-node":"3.775.0","@aws-sdk/middleware-eventstream":"3.775.0","@aws-sdk/middleware-host-header":"3.775.0","@aws-sdk/middleware-logger":"3.775.0","@aws-sdk/middleware-recursion-detection":"3.775.0","@aws-sdk/middleware-user-agent":"3.796.0","@aws-sdk/region-config-resolver":"3.775.0","@aws-sdk/types":"3.775.0","@aws-sdk/util-endpoints":"3.787.0","@aws-sdk/util-user-agent-browser":"3.775.0","@aws-sdk/util-user-agent-node":"3.796.0","@smithy/config-resolver":"^4.1.0","@smithy/core":"^3.2.0","@smithy/eventstream-serde-browser":"^4.0.2","@smithy/eventstream-serde-config-resolver":"^4.1.0","@smithy/eventstream-serde-node":"^4.0.2","@smithy/fetch-http-handler":"^5.0.2","@smithy/hash-node":"^4.0.2","@smithy/invalid-dependency":"^4.0.2","@smithy/middleware-content-length":"^4.0.2","@smithy/middleware-endpoint":"^4.1.0","@smithy/middleware-retry":"^4.1.0","@smithy/middleware-serde":"^4.0.3","@smithy/middleware-stack":"^4.0.2","@smithy/node-config-provider":"^4.0.2","@smithy/node-http-handler":"^4.0.4","@smithy/protocol-http":"^5.1.0","@smithy/smithy-client":"^4.2.0","@smithy/types":"^4.2.0","@smithy/url-parser":"^4.0.2","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.8","@smithy/util-defaults-mode-node":"^4.0.8","@smithy/util-endpoints":"^3.0.2","@smithy/util-middleware":"^4.0.2","@smithy/util-retry":"^4.0.2","@smithy/util-stream":"^4.2.0","@smithy/util-utf8":"^4.0.0","@types/uuid":"^9.0.1",tslib:"^2.6.2",uuid:"^9.0.1"},devDependencies:{"@tsconfig/node18":"18.2.4","@types/node":"^18.19.69",concurrently:"7.0.0","downlevel-dts":"0.10.1",rimraf:"3.0.2",typescript:"~5.2.2"},engines:{node:">=18.0.0"},typesVersions:{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},files:["dist-*/**"],author:{name:"AWS SDK for JavaScript Team",url:"https://aws.amazon.com/javascript/"},license:"Apache-2.0",browser:{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.browser"},"react-native":{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.native"},homepage:"https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-bedrock-runtime",repository:{type:"git",url:"https://github.com/aws/aws-sdk-js-v3.git",directory:"clients/client-bedrock-runtime"}}});
var M80=E((Az5,w62)=>{var{defineProperty:Eq1,getOwnPropertyDescriptor:uV4,getOwnPropertyNames:mV4}=Object,dV4=Object.prototype.hasOwnProperty,Uq1=(A,B)=>Eq1(A,"name",{value:B,configurable:!0}),cV4=(A,B)=>{for(var Q in B)Eq1(A,Q,{get:B[Q],enumerable:!0})},lV4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of mV4(B))if(!dV4.call(A,Z)&&Z!==Q)Eq1(A,Z,{get:()=>B[Z],enumerable:!(D=uV4(B,Z))||D.enumerable})}return A},pV4=(A)=>lV4(Eq1({},"__esModule",{value:!0}),A),X62={};cV4(X62,{AlgorithmId:()=>H62,EndpointURLScheme:()=>K62,FieldPosition:()=>z62,HttpApiKeyAuthLocation:()=>C62,HttpAuthLocation:()=>V62,IniSectionType:()=>E62,RequestHandlerProtocol:()=>U62,SMITHY_CONTEXT_KEY:()=>rV4,getDefaultClientConfiguration:()=>aV4,resolveDefaultRuntimeConfig:()=>sV4});w62.exports=pV4(X62);var V62=((A)=>{return A.HEADER="header",A.QUERY="query",A})(V62||{}),C62=((A)=>{return A.HEADER="header",A.QUERY="query",A})(C62||{}),K62=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(K62||{}),H62=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(H62||{}),iV4=Uq1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),nV4=Uq1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),aV4=Uq1((A)=>{return iV4(A)},"getDefaultClientConfiguration"),sV4=Uq1((A)=>{return nV4(A)},"resolveDefaultRuntimeConfig"),z62=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(z62||{}),rV4="__smithy_context",E62=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(E62||{}),U62=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(U62||{})});
var MF2=E((NF2)=>{Object.defineProperty(NF2,"__esModule",{value:!0});NF2.fromBase64=void 0;var sM4=YD(),rM4=/^[A-Za-z0-9+/]*={0,2}$/,oM4=(A)=>{if(A.length*3%4!==0)throw new TypeError("Incorrect padding on base64 string.");if(!rM4.exec(A))throw new TypeError("Invalid base64 string.");let B=sM4.fromString(A,"base64");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength)};NF2.fromBase64=oM4});
var MJB=E((s03,LJB)=>{var{defineProperty:mk1,getOwnPropertyDescriptor:Ec6,getOwnPropertyNames:Uc6}=Object,wc6=Object.prototype.hasOwnProperty,gx=(A,B)=>mk1(A,"name",{value:B,configurable:!0}),$c6=(A,B)=>{for(var Q in B)mk1(A,Q,{get:B[Q],enumerable:!0})},qc6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Uc6(B))if(!wc6.call(A,Z)&&Z!==Q)mk1(A,Z,{get:()=>B[Z],enumerable:!(D=Ec6(B,Z))||D.enumerable})}return A},Nc6=(A)=>qc6(mk1({},"__esModule",{value:!0}),A),wJB={};$c6(wJB,{Field:()=>Rc6,Fields:()=>Oc6,HttpRequest:()=>Tc6,HttpResponse:()=>Pc6,IHttpRequest:()=>$JB.HttpRequest,getHttpHandlerExtensionConfiguration:()=>Lc6,isValidHostname:()=>NJB,resolveHttpHandlerRuntimeConfig:()=>Mc6});LJB.exports=Nc6(wJB);var Lc6=gx((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),Mc6=gx((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),$JB=UJB(),Rc6=class{static{gx(this,"Field")}constructor({name:A,kind:B=$JB.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},Oc6=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{gx(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},Tc6=class A{static{gx(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=qJB(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function qJB(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}gx(qJB,"cloneQuery");var Pc6=class{static{gx(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function NJB(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}gx(NJB,"isValidHostname")});
var N50=E((YE5,PD2)=>{var{defineProperty:CN1,getOwnPropertyDescriptor:H$4,getOwnPropertyNames:z$4}=Object,E$4=Object.prototype.hasOwnProperty,KN1=(A,B)=>CN1(A,"name",{value:B,configurable:!0}),U$4=(A,B)=>{for(var Q in B)CN1(A,Q,{get:B[Q],enumerable:!0})},w$4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of z$4(B))if(!E$4.call(A,Z)&&Z!==Q)CN1(A,Z,{get:()=>B[Z],enumerable:!(D=H$4(B,Z))||D.enumerable})}return A},$$4=(A)=>w$4(CN1({},"__esModule",{value:!0}),A),$D2={};U$4($D2,{AlgorithmId:()=>MD2,EndpointURLScheme:()=>LD2,FieldPosition:()=>RD2,HttpApiKeyAuthLocation:()=>ND2,HttpAuthLocation:()=>qD2,IniSectionType:()=>OD2,RequestHandlerProtocol:()=>TD2,SMITHY_CONTEXT_KEY:()=>R$4,getDefaultClientConfiguration:()=>L$4,resolveDefaultRuntimeConfig:()=>M$4});PD2.exports=$$4($D2);var qD2=((A)=>{return A.HEADER="header",A.QUERY="query",A})(qD2||{}),ND2=((A)=>{return A.HEADER="header",A.QUERY="query",A})(ND2||{}),LD2=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(LD2||{}),MD2=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(MD2||{}),q$4=KN1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),N$4=KN1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),L$4=KN1((A)=>{return q$4(A)},"getDefaultClientConfiguration"),M$4=KN1((A)=>{return N$4(A)},"resolveDefaultRuntimeConfig"),RD2=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(RD2||{}),R$4="__smithy_context",OD2=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(OD2||{}),TD2=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(TD2||{})});
var N61=E((IE5,wD2)=>{var{defineProperty:VN1,getOwnPropertyDescriptor:Z$4,getOwnPropertyNames:G$4}=Object,F$4=Object.prototype.hasOwnProperty,XN1=(A,B)=>VN1(A,"name",{value:B,configurable:!0}),I$4=(A,B)=>{for(var Q in B)VN1(A,Q,{get:B[Q],enumerable:!0})},Y$4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of G$4(B))if(!F$4.call(A,Z)&&Z!==Q)VN1(A,Z,{get:()=>B[Z],enumerable:!(D=Z$4(B,Z))||D.enumerable})}return A},W$4=(A)=>Y$4(VN1({},"__esModule",{value:!0}),A),KD2={};I$4(KD2,{NODE_APP_ID_CONFIG_OPTIONS:()=>K$4,UA_APP_ID_ENV_NAME:()=>ED2,UA_APP_ID_INI_NAME:()=>UD2,createDefaultUserAgentProvider:()=>zD2,crtAvailability:()=>HD2,defaultUserAgent:()=>X$4});wD2.exports=W$4(KD2);var CD2=J1("os"),q50=J1("process"),HD2={isCrtAvailable:!1},J$4=XN1(()=>{if(HD2.isCrtAvailable)return["md/crt-avail"];return null},"isCrtAvailable"),zD2=XN1(({serviceId:A,clientVersion:B})=>{return async(Q)=>{let D=[["aws-sdk-js",B],["ua","2.1"],[`os/${CD2.platform()}`,CD2.release()],["lang/js"],["md/nodejs",`${q50.versions.node}`]],Z=J$4();if(Z)D.push(Z);if(A)D.push([`api/${A}`,B]);if(q50.env.AWS_EXECUTION_ENV)D.push([`exec-env/${q50.env.AWS_EXECUTION_ENV}`]);let G=await Q?.userAgentAppId?.();return G?[...D,[`app/${G}`]]:[...D]}},"createDefaultUserAgentProvider"),X$4=zD2,V$4=Rs(),ED2="AWS_SDK_UA_APP_ID",UD2="sdk_ua_app_id",C$4="sdk-ua-app-id",K$4={environmentVariableSelector:XN1((A)=>A[ED2],"environmentVariableSelector"),configFileSelector:XN1((A)=>A[UD2]??A[C$4],"configFileSelector"),default:V$4.DEFAULT_UA_APP_ID}});
var N72=E((BE5,q72)=>{var{defineProperty:ZN1,getOwnPropertyDescriptor:Uw4,getOwnPropertyNames:ww4}=Object,$w4=Object.prototype.hasOwnProperty,RV=(A,B)=>ZN1(A,"name",{value:B,configurable:!0}),qw4=(A,B)=>{for(var Q in B)ZN1(A,Q,{get:B[Q],enumerable:!0})},Nw4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of ww4(B))if(!$w4.call(A,Z)&&Z!==Q)ZN1(A,Z,{get:()=>B[Z],enumerable:!(D=Uw4(B,Z))||D.enumerable})}return A},Lw4=(A)=>Nw4(ZN1({},"__esModule",{value:!0}),A),E72={};qw4(E72,{_toBool:()=>Rw4,_toNum:()=>Ow4,_toStr:()=>Mw4,awsExpectUnion:()=>Pw4,loadRestJsonErrorCode:()=>yw4,loadRestXmlErrorCode:()=>vw4,parseJsonBody:()=>w72,parseJsonErrorBody:()=>jw4,parseXmlBody:()=>$72,parseXmlErrorBody:()=>xw4});q72.exports=Lw4(E72);var Mw4=RV((A)=>{if(A==null)return A;if(typeof A==="number"||typeof A==="bigint"){let B=new Error(`Received number ${A} where a string was expected.`);return B.name="Warning",console.warn(B),String(A)}if(typeof A==="boolean"){let B=new Error(`Received boolean ${A} where a string was expected.`);return B.name="Warning",console.warn(B),String(A)}return A},"_toStr"),Rw4=RV((A)=>{if(A==null)return A;if(typeof A==="string"){let B=A.toLowerCase();if(A!==""&&B!=="false"&&B!=="true"){let Q=new Error(`Received string "${A}" where a boolean was expected.`);Q.name="Warning",console.warn(Q)}return A!==""&&B!=="false"}return A},"_toBool"),Ow4=RV((A)=>{if(A==null)return A;if(typeof A==="string"){let B=Number(A);if(B.toString()!==A){let Q=new Error(`Received string "${A}" where a number was expected.`);return Q.name="Warning",console.warn(Q),A}return B}return A},"_toNum"),Tw4=DN1(),Pw4=RV((A)=>{if(A==null)return;if(typeof A==="object"&&"__type"in A)delete A.__type;return Tw4.expectUnion(A)},"awsExpectUnion"),Sw4=DN1(),U72=RV((A,B)=>Sw4.collectBody(A,B).then((Q)=>B.utf8Encoder(Q)),"collectBodyString"),w72=RV((A,B)=>U72(A,B).then((Q)=>{if(Q.length)try{return JSON.parse(Q)}catch(D){if(D?.name==="SyntaxError")Object.defineProperty(D,"$responseBodyText",{value:Q});throw D}return{}}),"parseJsonBody"),jw4=RV(async(A,B)=>{let Q=await w72(A,B);return Q.message=Q.message??Q.Message,Q},"parseJsonErrorBody"),yw4=RV((A,B)=>{let Q=RV((G,F)=>Object.keys(G).find((I)=>I.toLowerCase()===F.toLowerCase()),"findKey"),D=RV((G)=>{let F=G;if(typeof F==="number")F=F.toString();if(F.indexOf(",")>=0)F=F.split(",")[0];if(F.indexOf(":")>=0)F=F.split(":")[0];if(F.indexOf("#")>=0)F=F.split("#")[1];return F},"sanitizeErrorCode"),Z=Q(A.headers,"x-amzn-errortype");if(Z!==void 0)return D(A.headers[Z]);if(B.code!==void 0)return D(B.code);if(B.__type!==void 0)return D(B.__type)},"loadRestJsonErrorCode"),kw4=DN1(),_w4=uN(),$72=RV((A,B)=>U72(A,B).then((Q)=>{if(Q.length){let D=new _w4.XMLParser({attributeNamePrefix:"",htmlEntities:!0,ignoreAttributes:!1,ignoreDeclaration:!0,parseTagValue:!1,trimValues:!1,tagValueProcessor:RV((Y,W)=>W.trim()===""&&W.includes(`
`)?"":void 0,"tagValueProcessor")});D.addEntity("#xD","\r"),D.addEntity("#10",`
`);let Z;try{Z=D.parse(Q,!0)}catch(Y){if(Y&&typeof Y==="object")Object.defineProperty(Y,"$responseBodyText",{value:Q});throw Y}let G="#text",F=Object.keys(Z)[0],I=Z[F];if(I[G])I[F]=I[G],delete I[G];return kw4.getValueFromTextNode(I)}return{}}),"parseXmlBody"),xw4=RV(async(A,B)=>{let Q=await $72(A,B);if(Q.Error)Q.Error.message=Q.Error.message??Q.Error.Message;return Q},"parseXmlErrorBody"),vw4=RV((A,B)=>{if(B?.Error?.Code!==void 0)return B.Error.Code;if(B?.Code!==void 0)return B.Code;if(A.statusCode==404)return"NotFound"},"loadRestXmlErrorCode")});
var N80=E((oH5,k42)=>{var{defineProperty:Cq1,getOwnPropertyDescriptor:RV4,getOwnPropertyNames:OV4}=Object,TV4=Object.prototype.hasOwnProperty,PV4=(A,B)=>Cq1(A,"name",{value:B,configurable:!0}),SV4=(A,B)=>{for(var Q in B)Cq1(A,Q,{get:B[Q],enumerable:!0})},jV4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of OV4(B))if(!TV4.call(A,Z)&&Z!==Q)Cq1(A,Z,{get:()=>B[Z],enumerable:!(D=RV4(B,Z))||D.enumerable})}return A},yV4=(A)=>jV4(Cq1({},"__esModule",{value:!0}),A),R42={};SV4(R42,{ENV_ACCOUNT_ID:()=>y42,ENV_CREDENTIAL_SCOPE:()=>j42,ENV_EXPIRATION:()=>S42,ENV_KEY:()=>O42,ENV_SECRET:()=>T42,ENV_SESSION:()=>P42,fromEnv:()=>xV4});k42.exports=yV4(R42);var kV4=zL(),_V4=Q9(),O42="AWS_ACCESS_KEY_ID",T42="AWS_SECRET_ACCESS_KEY",P42="AWS_SESSION_TOKEN",S42="AWS_CREDENTIAL_EXPIRATION",j42="AWS_CREDENTIAL_SCOPE",y42="AWS_ACCOUNT_ID",xV4=PV4((A)=>async()=>{A?.logger?.debug("@aws-sdk/credential-provider-env - fromEnv");let B=process.env[O42],Q=process.env[T42],D=process.env[P42],Z=process.env[S42],G=process.env[j42],F=process.env[y42];if(B&&Q){let I={accessKeyId:B,secretAccessKey:Q,...D&&{sessionToken:D},...Z&&{expiration:new Date(Z)},...G&&{credentialScope:G},...F&&{accountId:F}};return kV4.setCredentialFeature(I,"CREDENTIALS_ENV_VARS","g"),I}throw new _V4.CredentialsProviderError("Unable to find environment variable credentials.",{logger:A?.logger})},"fromEnv")});
var OZ2=E((MZ2)=>{Object.defineProperty(MZ2,"__esModule",{value:!0});MZ2.getRuntimeConfig=void 0;var tq4=KI(),eq4=CB(),AN4=T61(),BN4=JZ(),NZ2=BZ2(),LZ2=cB(),QN4=w50(),DN4=qZ2(),ZN4=(A)=>{return{apiVersion:"2019-06-10",base64Decoder:A?.base64Decoder??NZ2.fromBase64,base64Encoder:A?.base64Encoder??NZ2.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??DN4.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??QN4.defaultSSOHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new tq4.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new eq4.NoAuthSigner}],logger:A?.logger??new AN4.NoOpLogger,serviceId:A?.serviceId??"SSO",urlParser:A?.urlParser??BN4.parseUrl,utf8Decoder:A?.utf8Decoder??LZ2.fromUtf8,utf8Encoder:A?.utf8Encoder??LZ2.toUtf8}};MZ2.getRuntimeConfig=ZN4});
var PVB=E((RA3,TVB)=>{var{defineProperty:D_1,getOwnPropertyDescriptor:Cp6,getOwnPropertyNames:Kp6}=Object,Hp6=Object.prototype.hasOwnProperty,Z_1=(A,B)=>D_1(A,"name",{value:B,configurable:!0}),zp6=(A,B)=>{for(var Q in B)D_1(A,Q,{get:B[Q],enumerable:!0})},Ep6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Kp6(B))if(!Hp6.call(A,Z)&&Z!==Q)D_1(A,Z,{get:()=>B[Z],enumerable:!(D=Cp6(B,Z))||D.enumerable})}return A},Up6=(A)=>Ep6(D_1({},"__esModule",{value:!0}),A),RVB={};zp6(RVB,{eventStreamPayloadHandlerProvider:()=>Np6});TVB.exports=Up6(RVB);var wp6=tz0(),Q_1=J1("stream"),$p6=class extends Q_1.Transform{static{Z_1(this,"EventSigningStream")}priorSignature;messageSigner;eventStreamCodec;systemClockOffsetProvider;constructor(A){super({autoDestroy:!0,readableObjectMode:!0,writableObjectMode:!0,...A});this.priorSignature=A.priorSignature,this.eventStreamCodec=A.eventStreamCodec,this.messageSigner=A.messageSigner,this.systemClockOffsetProvider=A.systemClockOffsetProvider}async _transform(A,B,Q){try{let D=new Date(Date.now()+await this.systemClockOffsetProvider()),Z={":date":{type:"timestamp",value:D}},G=await this.messageSigner.sign({message:{body:A,headers:Z},priorSignature:this.priorSignature},{signingDate:D});this.priorSignature=G.signature;let F=this.eventStreamCodec.encode({headers:{...Z,":chunk-signature":{type:"binary",value:OVB(G.signature)}},body:A});return this.push(F),Q()}catch(D){Q(D)}}};function OVB(A){let B=Buffer.from(A,"hex");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength/Uint8Array.BYTES_PER_ELEMENT)}Z_1(OVB,"getSignatureBinary");var qp6=class{static{Z_1(this,"EventStreamPayloadHandler")}messageSigner;eventStreamCodec;systemClockOffsetProvider;constructor(A){this.messageSigner=A.messageSigner,this.eventStreamCodec=new wp6.EventStreamCodec(A.utf8Encoder,A.utf8Decoder),this.systemClockOffsetProvider=async()=>A.systemClockOffset??0}async handle(A,B,Q={}){let D=B.request,{body:Z,query:G}=D;if(!(Z instanceof Q_1.Readable))throw new Error("Eventstream payload must be a Readable stream.");let F=Z;D.body=new Q_1.PassThrough({objectMode:!0});let Y=D.headers?.authorization?.match(/Signature=([\w]+)$/)?.[1]??G?.["X-Amz-Signature"]??"",W=new $p6({priorSignature:Y,eventStreamCodec:this.eventStreamCodec,messageSigner:await this.messageSigner(),systemClockOffsetProvider:this.systemClockOffsetProvider});Q_1.pipeline(F,W,D.body,(X)=>{if(X)throw X});let J;try{J=await A(B)}catch(X){throw D.body.end(),X}return J}},Np6=Z_1((A)=>new qp6(A),"eventStreamPayloadHandlerProvider")});
var Q30=E((iG2)=>{Object.defineProperty(iG2,"__esModule",{value:!0});iG2.resolveHttpAuthSchemeConfig=iG2.defaultSSOOIDCHttpAuthSchemeProvider=iG2.defaultSSOOIDCHttpAuthSchemeParametersProvider=void 0;var uM4=KI(),B30=J5(),mM4=async(A,B,Q)=>{return{operation:B30.getSmithyContext(B).operation,region:await B30.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};iG2.defaultSSOOIDCHttpAuthSchemeParametersProvider=mM4;function dM4(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"sso-oauth",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function cM4(A){return{schemeId:"smithy.api#noAuth"}}var lM4=(A)=>{let B=[];switch(A.operation){case"CreateToken":{B.push(cM4(A));break}default:B.push(dM4(A))}return B};iG2.defaultSSOOIDCHttpAuthSchemeProvider=lM4;var pM4=(A)=>{let B=uM4.resolveAwsSdkSigV4Config(A);return Object.assign(B,{authSchemePreference:B30.normalizeProvider(A.authSchemePreference??[])})};iG2.resolveHttpAuthSchemeConfig=pM4});
var Q70=E((EU5,B70)=>{var{defineProperty:iN1,getOwnPropertyDescriptor:uT4,getOwnPropertyNames:mT4}=Object,dT4=Object.prototype.hasOwnProperty,Y9=(A,B)=>iN1(A,"name",{value:B,configurable:!0}),cT4=(A,B)=>{for(var Q in B)iN1(A,Q,{get:B[Q],enumerable:!0})},a30=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of mT4(B))if(!dT4.call(A,Z)&&Z!==Q)iN1(A,Z,{get:()=>B[Z],enumerable:!(D=uT4(B,Z))||D.enumerable})}return A},lT4=(A,B,Q)=>(a30(A,B,"default"),Q&&a30(Q,B,"default")),pT4=(A)=>a30(iN1({},"__esModule",{value:!0}),A),r30={};cT4(r30,{AssumeRoleCommand:()=>e30,AssumeRoleResponseFilterSensitiveLog:()=>XW2,AssumeRoleWithWebIdentityCommand:()=>A70,AssumeRoleWithWebIdentityRequestFilterSensitiveLog:()=>UW2,AssumeRoleWithWebIdentityResponseFilterSensitiveLog:()=>wW2,ClientInputEndpointParameters:()=>fP4.ClientInputEndpointParameters,CredentialsFilterSensitiveLog:()=>o30,ExpiredTokenException:()=>VW2,IDPCommunicationErrorException:()=>$W2,IDPRejectedClaimException:()=>zW2,InvalidIdentityTokenException:()=>EW2,MalformedPolicyDocumentException:()=>CW2,PackedPolicyTooLargeException:()=>KW2,RegionDisabledException:()=>HW2,STS:()=>yW2,STSServiceException:()=>uT,decorateDefaultCredentialProvider:()=>uP4,getDefaultRoleAssumer:()=>fW2,getDefaultRoleAssumerWithWebIdentity:()=>hW2});B70.exports=pT4(r30);lT4(r30,u61(),B70.exports);var iT4=XZ(),nT4=R6(),aT4=j3(),sT4=XZ(),rT4=m61(),JW2=XZ(),oT4=XZ(),uT=class A extends oT4.ServiceException{static{Y9(this,"STSServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},o30=Y9((A)=>({...A,...A.SecretAccessKey&&{SecretAccessKey:JW2.SENSITIVE_STRING}}),"CredentialsFilterSensitiveLog"),XW2=Y9((A)=>({...A,...A.Credentials&&{Credentials:o30(A.Credentials)}}),"AssumeRoleResponseFilterSensitiveLog"),VW2=class A extends uT{static{Y9(this,"ExpiredTokenException")}name="ExpiredTokenException";$fault="client";constructor(B){super({name:"ExpiredTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},CW2=class A extends uT{static{Y9(this,"MalformedPolicyDocumentException")}name="MalformedPolicyDocumentException";$fault="client";constructor(B){super({name:"MalformedPolicyDocumentException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},KW2=class A extends uT{static{Y9(this,"PackedPolicyTooLargeException")}name="PackedPolicyTooLargeException";$fault="client";constructor(B){super({name:"PackedPolicyTooLargeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},HW2=class A extends uT{static{Y9(this,"RegionDisabledException")}name="RegionDisabledException";$fault="client";constructor(B){super({name:"RegionDisabledException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},zW2=class A extends uT{static{Y9(this,"IDPRejectedClaimException")}name="IDPRejectedClaimException";$fault="client";constructor(B){super({name:"IDPRejectedClaimException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},EW2=class A extends uT{static{Y9(this,"InvalidIdentityTokenException")}name="InvalidIdentityTokenException";$fault="client";constructor(B){super({name:"InvalidIdentityTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},UW2=Y9((A)=>({...A,...A.WebIdentityToken&&{WebIdentityToken:JW2.SENSITIVE_STRING}}),"AssumeRoleWithWebIdentityRequestFilterSensitiveLog"),wW2=Y9((A)=>({...A,...A.Credentials&&{Credentials:o30(A.Credentials)}}),"AssumeRoleWithWebIdentityResponseFilterSensitiveLog"),$W2=class A extends uT{static{Y9(this,"IDPCommunicationErrorException")}name="IDPCommunicationErrorException";$fault="client";constructor(B){super({name:"IDPCommunicationErrorException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},t30=KI(),tT4=uN1(),H5=XZ(),eT4=Y9(async(A,B)=>{let Q=OW2,D;return D=jW2({...JP4(A,B),[PW2]:PP4,[SW2]:TW2}),RW2(B,Q,"/",void 0,D)},"se_AssumeRoleCommand"),AP4=Y9(async(A,B)=>{let Q=OW2,D;return D=jW2({...XP4(A,B),[PW2]:SP4,[SW2]:TW2}),RW2(B,Q,"/",void 0,D)},"se_AssumeRoleWithWebIdentityCommand"),BP4=Y9(async(A,B)=>{if(A.statusCode>=300)return qW2(A,B);let Q=await t30.parseXmlBody(A.body,B),D={};return D=UP4(Q.AssumeRoleResult,B),{$metadata:mT(A),...D}},"de_AssumeRoleCommand"),QP4=Y9(async(A,B)=>{if(A.statusCode>=300)return qW2(A,B);let Q=await t30.parseXmlBody(A.body,B),D={};return D=wP4(Q.AssumeRoleWithWebIdentityResult,B),{$metadata:mT(A),...D}},"de_AssumeRoleWithWebIdentityCommand"),qW2=Y9(async(A,B)=>{let Q={...A,body:await t30.parseXmlErrorBody(A.body,B)},D=jP4(A,Q.body);switch(D){case"ExpiredTokenException":case"com.amazonaws.sts#ExpiredTokenException":throw await DP4(Q,B);case"MalformedPolicyDocument":case"com.amazonaws.sts#MalformedPolicyDocumentException":throw await IP4(Q,B);case"PackedPolicyTooLarge":case"com.amazonaws.sts#PackedPolicyTooLargeException":throw await YP4(Q,B);case"RegionDisabledException":case"com.amazonaws.sts#RegionDisabledException":throw await WP4(Q,B);case"IDPCommunicationError":case"com.amazonaws.sts#IDPCommunicationErrorException":throw await ZP4(Q,B);case"IDPRejectedClaim":case"com.amazonaws.sts#IDPRejectedClaimException":throw await GP4(Q,B);case"InvalidIdentityToken":case"com.amazonaws.sts#InvalidIdentityTokenException":throw await FP4(Q,B);default:let Z=Q.body;return TP4({output:A,parsedBody:Z.Error,errorCode:D})}},"de_CommandError"),DP4=Y9(async(A,B)=>{let Q=A.body,D=$P4(Q.Error,B),Z=new VW2({$metadata:mT(A),...D});return H5.decorateServiceException(Z,Q)},"de_ExpiredTokenExceptionRes"),ZP4=Y9(async(A,B)=>{let Q=A.body,D=qP4(Q.Error,B),Z=new $W2({$metadata:mT(A),...D});return H5.decorateServiceException(Z,Q)},"de_IDPCommunicationErrorExceptionRes"),GP4=Y9(async(A,B)=>{let Q=A.body,D=NP4(Q.Error,B),Z=new zW2({$metadata:mT(A),...D});return H5.decorateServiceException(Z,Q)},"de_IDPRejectedClaimExceptionRes"),FP4=Y9(async(A,B)=>{let Q=A.body,D=LP4(Q.Error,B),Z=new EW2({$metadata:mT(A),...D});return H5.decorateServiceException(Z,Q)},"de_InvalidIdentityTokenExceptionRes"),IP4=Y9(async(A,B)=>{let Q=A.body,D=MP4(Q.Error,B),Z=new CW2({$metadata:mT(A),...D});return H5.decorateServiceException(Z,Q)},"de_MalformedPolicyDocumentExceptionRes"),YP4=Y9(async(A,B)=>{let Q=A.body,D=RP4(Q.Error,B),Z=new KW2({$metadata:mT(A),...D});return H5.decorateServiceException(Z,Q)},"de_PackedPolicyTooLargeExceptionRes"),WP4=Y9(async(A,B)=>{let Q=A.body,D=OP4(Q.Error,B),Z=new HW2({$metadata:mT(A),...D});return H5.decorateServiceException(Z,Q)},"de_RegionDisabledExceptionRes"),JP4=Y9((A,B)=>{let Q={};if(A[es]!=null)Q[es]=A[es];if(A[Ar]!=null)Q[Ar]=A[Ar];if(A[os]!=null){let D=NW2(A[os],B);if(A[os]?.length===0)Q.PolicyArns=[];Object.entries(D).forEach(([Z,G])=>{let F=`PolicyArns.${Z}`;Q[F]=G})}if(A[rs]!=null)Q[rs]=A[rs];if(A[ss]!=null)Q[ss]=A[ss];if(A[d30]!=null){let D=EP4(A[d30],B);if(A[d30]?.length===0)Q.Tags=[];Object.entries(D).forEach(([Z,G])=>{let F=`Tags.${Z}`;Q[F]=G})}if(A[l30]!=null){let D=zP4(A[l30],B);if(A[l30]?.length===0)Q.TransitiveTagKeys=[];Object.entries(D).forEach(([Z,G])=>{let F=`TransitiveTagKeys.${Z}`;Q[F]=G})}if(A[k30]!=null)Q[k30]=A[k30];if(A[u30]!=null)Q[u30]=A[u30];if(A[c30]!=null)Q[c30]=A[c30];if(A[gT]!=null)Q[gT]=A[gT];if(A[v30]!=null){let D=KP4(A[v30],B);if(A[v30]?.length===0)Q.ProvidedContexts=[];Object.entries(D).forEach(([Z,G])=>{let F=`ProvidedContexts.${Z}`;Q[F]=G})}return Q},"se_AssumeRoleRequest"),XP4=Y9((A,B)=>{let Q={};if(A[es]!=null)Q[es]=A[es];if(A[Ar]!=null)Q[Ar]=A[Ar];if(A[i30]!=null)Q[i30]=A[i30];if(A[b30]!=null)Q[b30]=A[b30];if(A[os]!=null){let D=NW2(A[os],B);if(A[os]?.length===0)Q.PolicyArns=[];Object.entries(D).forEach(([Z,G])=>{let F=`PolicyArns.${Z}`;Q[F]=G})}if(A[rs]!=null)Q[rs]=A[rs];if(A[ss]!=null)Q[ss]=A[ss];return Q},"se_AssumeRoleWithWebIdentityRequest"),NW2=Y9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=VP4(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_policyDescriptorListType"),VP4=Y9((A,B)=>{let Q={};if(A[n30]!=null)Q[n30]=A[n30];return Q},"se_PolicyDescriptorType"),CP4=Y9((A,B)=>{let Q={};if(A[x30]!=null)Q[x30]=A[x30];if(A[j30]!=null)Q[j30]=A[j30];return Q},"se_ProvidedContext"),KP4=Y9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=CP4(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_ProvidedContextsListType"),HP4=Y9((A,B)=>{let Q={};if(A[_30]!=null)Q[_30]=A[_30];if(A[p30]!=null)Q[p30]=A[p30];return Q},"se_Tag"),zP4=Y9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;Q[`member.${D}`]=Z,D++}return Q},"se_tagKeyListType"),EP4=Y9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=HP4(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_tagListType"),LW2=Y9((A,B)=>{let Q={};if(A[T30]!=null)Q[T30]=H5.expectString(A[T30]);if(A[P30]!=null)Q[P30]=H5.expectString(A[P30]);return Q},"de_AssumedRoleUser"),UP4=Y9((A,B)=>{let Q={};if(A[as]!=null)Q[as]=MW2(A[as],B);if(A[ns]!=null)Q[ns]=LW2(A[ns],B);if(A[ts]!=null)Q[ts]=H5.strictParseInt32(A[ts]);if(A[gT]!=null)Q[gT]=H5.expectString(A[gT]);return Q},"de_AssumeRoleResponse"),wP4=Y9((A,B)=>{let Q={};if(A[as]!=null)Q[as]=MW2(A[as],B);if(A[g30]!=null)Q[g30]=H5.expectString(A[g30]);if(A[ns]!=null)Q[ns]=LW2(A[ns],B);if(A[ts]!=null)Q[ts]=H5.strictParseInt32(A[ts]);if(A[f30]!=null)Q[f30]=H5.expectString(A[f30]);if(A[S30]!=null)Q[S30]=H5.expectString(A[S30]);if(A[gT]!=null)Q[gT]=H5.expectString(A[gT]);return Q},"de_AssumeRoleWithWebIdentityResponse"),MW2=Y9((A,B)=>{let Q={};if(A[O30]!=null)Q[O30]=H5.expectString(A[O30]);if(A[h30]!=null)Q[h30]=H5.expectString(A[h30]);if(A[m30]!=null)Q[m30]=H5.expectString(A[m30]);if(A[y30]!=null)Q[y30]=H5.expectNonNull(H5.parseRfc3339DateTimeWithOffset(A[y30]));return Q},"de_Credentials"),$P4=Y9((A,B)=>{let Q={};if(A[ZG]!=null)Q[ZG]=H5.expectString(A[ZG]);return Q},"de_ExpiredTokenException"),qP4=Y9((A,B)=>{let Q={};if(A[ZG]!=null)Q[ZG]=H5.expectString(A[ZG]);return Q},"de_IDPCommunicationErrorException"),NP4=Y9((A,B)=>{let Q={};if(A[ZG]!=null)Q[ZG]=H5.expectString(A[ZG]);return Q},"de_IDPRejectedClaimException"),LP4=Y9((A,B)=>{let Q={};if(A[ZG]!=null)Q[ZG]=H5.expectString(A[ZG]);return Q},"de_InvalidIdentityTokenException"),MP4=Y9((A,B)=>{let Q={};if(A[ZG]!=null)Q[ZG]=H5.expectString(A[ZG]);return Q},"de_MalformedPolicyDocumentException"),RP4=Y9((A,B)=>{let Q={};if(A[ZG]!=null)Q[ZG]=H5.expectString(A[ZG]);return Q},"de_PackedPolicyTooLargeException"),OP4=Y9((A,B)=>{let Q={};if(A[ZG]!=null)Q[ZG]=H5.expectString(A[ZG]);return Q},"de_RegionDisabledException"),mT=Y9((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),TP4=H5.withBaseException(uT),RW2=Y9(async(A,B,Q,D,Z)=>{let{hostname:G,protocol:F="https",port:I,path:Y}=await A.endpoint(),W={protocol:F,hostname:G,port:I,method:"POST",path:Y.endsWith("/")?Y.slice(0,-1)+Q:Y+Q,headers:B};if(D!==void 0)W.hostname=D;if(Z!==void 0)W.body=Z;return new tT4.HttpRequest(W)},"buildHttpRpcRequest"),OW2={"content-type":"application/x-www-form-urlencoded"},TW2="2011-06-15",PW2="Action",O30="AccessKeyId",PP4="AssumeRole",T30="AssumedRoleId",ns="AssumedRoleUser",SP4="AssumeRoleWithWebIdentity",P30="Arn",S30="Audience",as="Credentials",j30="ContextAssertion",ss="DurationSeconds",y30="Expiration",k30="ExternalId",_30="Key",rs="Policy",os="PolicyArns",x30="ProviderArn",v30="ProvidedContexts",b30="ProviderId",ts="PackedPolicySize",f30="Provider",es="RoleArn",Ar="RoleSessionName",h30="SecretAccessKey",g30="SubjectFromWebIdentityToken",gT="SourceIdentity",u30="SerialNumber",m30="SessionToken",d30="Tags",c30="TokenCode",l30="TransitiveTagKeys",SW2="Version",p30="Value",i30="WebIdentityToken",n30="arn",ZG="message",jW2=Y9((A)=>Object.entries(A).map(([B,Q])=>H5.extendedEncodeURIComponent(B)+"="+H5.extendedEncodeURIComponent(Q)).join("&"),"buildFormUrlencodedString"),jP4=Y9((A,B)=>{if(B.Error?.Code!==void 0)return B.Error.Code;if(A.statusCode==404)return"NotFound"},"loadQueryErrorCode"),e30=class extends sT4.Command.classBuilder().ep(rT4.commonParams).m(function(A,B,Q,D){return[aT4.getSerdePlugin(Q,this.serialize,this.deserialize),nT4.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRole",{}).n("STSClient","AssumeRoleCommand").f(void 0,XW2).ser(eT4).de(BP4).build(){static{Y9(this,"AssumeRoleCommand")}},yP4=R6(),kP4=j3(),_P4=XZ(),xP4=m61(),A70=class extends _P4.Command.classBuilder().ep(xP4.commonParams).m(function(A,B,Q,D){return[kP4.getSerdePlugin(Q,this.serialize,this.deserialize),yP4.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRoleWithWebIdentity",{}).n("STSClient","AssumeRoleWithWebIdentityCommand").f(UW2,wW2).ser(AP4).de(QP4).build(){static{Y9(this,"AssumeRoleWithWebIdentityCommand")}},vP4=u61(),bP4={AssumeRoleCommand:e30,AssumeRoleWithWebIdentityCommand:A70},yW2=class extends vP4.STSClient{static{Y9(this,"STS")}};iT4.createAggregatedClient(bP4,yW2);var fP4=m61(),s30=zL(),WW2="us-east-1",kW2=Y9((A)=>{if(typeof A?.Arn==="string"){let B=A.Arn.split(":");if(B.length>4&&B[4]!=="")return B[4]}return},"getAccountIdFromAssumedRoleUser"),_W2=Y9(async(A,B,Q)=>{let D=typeof A==="function"?await A():A,Z=typeof B==="function"?await B():B;return Q?.debug?.("@aws-sdk/client-sts::resolveRegion","accepting first of:",`${D} (provider)`,`${Z} (parent client)`,`${WW2} (STS default)`),D??Z??WW2},"resolveRegion"),hP4=Y9((A,B)=>{let Q,D;return async(Z,G)=>{if(D=Z,!Q){let{logger:J=A?.parentClientConfig?.logger,region:X,requestHandler:V=A?.parentClientConfig?.requestHandler,credentialProviderLogger:C}=A,K=await _W2(X,A?.parentClientConfig?.region,C),H=!xW2(V);Q=new B({profile:A?.parentClientConfig?.profile,credentialDefaultProvider:Y9(()=>async()=>D,"credentialDefaultProvider"),region:K,requestHandler:H?V:void 0,logger:J})}let{Credentials:F,AssumedRoleUser:I}=await Q.send(new e30(G));if(!F||!F.AccessKeyId||!F.SecretAccessKey)throw new Error(`Invalid response from STS.assumeRole call with role ${G.RoleArn}`);let Y=kW2(I),W={accessKeyId:F.AccessKeyId,secretAccessKey:F.SecretAccessKey,sessionToken:F.SessionToken,expiration:F.Expiration,...F.CredentialScope&&{credentialScope:F.CredentialScope},...Y&&{accountId:Y}};return s30.setCredentialFeature(W,"CREDENTIALS_STS_ASSUME_ROLE","i"),W}},"getDefaultRoleAssumer"),gP4=Y9((A,B)=>{let Q;return async(D)=>{if(!Q){let{logger:Y=A?.parentClientConfig?.logger,region:W,requestHandler:J=A?.parentClientConfig?.requestHandler,credentialProviderLogger:X}=A,V=await _W2(W,A?.parentClientConfig?.region,X),C=!xW2(J);Q=new B({profile:A?.parentClientConfig?.profile,region:V,requestHandler:C?J:void 0,logger:Y})}let{Credentials:Z,AssumedRoleUser:G}=await Q.send(new A70(D));if(!Z||!Z.AccessKeyId||!Z.SecretAccessKey)throw new Error(`Invalid response from STS.assumeRoleWithWebIdentity call with role ${D.RoleArn}`);let F=kW2(G),I={accessKeyId:Z.AccessKeyId,secretAccessKey:Z.SecretAccessKey,sessionToken:Z.SessionToken,expiration:Z.Expiration,...Z.CredentialScope&&{credentialScope:Z.CredentialScope},...F&&{accountId:F}};if(F)s30.setCredentialFeature(I,"RESOLVED_ACCOUNT_ID","T");return s30.setCredentialFeature(I,"CREDENTIALS_STS_ASSUME_ROLE_WEB_ID","k"),I}},"getDefaultRoleAssumerWithWebIdentity"),xW2=Y9((A)=>{return A?.metadata?.handlerProtocol==="h2"},"isH2"),vW2=u61(),bW2=Y9((A,B)=>{if(!B)return A;else return class Q extends A{static{Y9(this,"CustomizableSTSClient")}constructor(D){super(D);for(let Z of B)this.middlewareStack.use(Z)}}},"getCustomizableStsClientCtor"),fW2=Y9((A={},B)=>hP4(A,bW2(vW2.STSClient,B)),"getDefaultRoleAssumer"),hW2=Y9((A={},B)=>gP4(A,bW2(vW2.STSClient,B)),"getDefaultRoleAssumerWithWebIdentity"),uP4=Y9((A)=>(B)=>A({roleAssumer:fW2(B),roleAssumerWithWebIdentity:hW2(B),...B}),"decorateDefaultCredentialProvider")});
var R62=E((Bz5,M62)=>{var{defineProperty:wq1,getOwnPropertyDescriptor:oV4,getOwnPropertyNames:tV4}=Object,eV4=Object.prototype.hasOwnProperty,gk=(A,B)=>wq1(A,"name",{value:B,configurable:!0}),AC4=(A,B)=>{for(var Q in B)wq1(A,Q,{get:B[Q],enumerable:!0})},BC4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of tV4(B))if(!eV4.call(A,Z)&&Z!==Q)wq1(A,Z,{get:()=>B[Z],enumerable:!(D=oV4(B,Z))||D.enumerable})}return A},QC4=(A)=>BC4(wq1({},"__esModule",{value:!0}),A),$62={};AC4($62,{Field:()=>GC4,Fields:()=>FC4,HttpRequest:()=>IC4,HttpResponse:()=>YC4,IHttpRequest:()=>q62.HttpRequest,getHttpHandlerExtensionConfiguration:()=>DC4,isValidHostname:()=>L62,resolveHttpHandlerRuntimeConfig:()=>ZC4});M62.exports=QC4($62);var DC4=gk((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),ZC4=gk((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),q62=M80(),GC4=class{static{gk(this,"Field")}constructor({name:A,kind:B=q62.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},FC4=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{gk(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},IC4=class A{static{gk(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=N62(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function N62(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}gk(N62,"cloneQuery");var YC4=class{static{gk(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function L62(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}gk(L62,"isValidHostname")});
var Rs=E((DE5,x72)=>{var{defineProperty:FN1,getOwnPropertyDescriptor:bw4,getOwnPropertyNames:fw4}=Object,hw4=Object.prototype.hasOwnProperty,vT=(A,B)=>FN1(A,"name",{value:B,configurable:!0}),gw4=(A,B)=>{for(var Q in B)FN1(A,Q,{get:B[Q],enumerable:!0})},uw4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of fw4(B))if(!hw4.call(A,Z)&&Z!==Q)FN1(A,Z,{get:()=>B[Z],enumerable:!(D=bw4(B,Z))||D.enumerable})}return A},mw4=(A)=>uw4(FN1({},"__esModule",{value:!0}),A),O72={};gw4(O72,{DEFAULT_UA_APP_ID:()=>T72,getUserAgentMiddlewareOptions:()=>_72,getUserAgentPlugin:()=>sw4,resolveUserAgentConfig:()=>S72,userAgentMiddleware:()=>k72});x72.exports=mw4(O72);var dw4=CB(),T72=void 0;function P72(A){if(A===void 0)return!0;return typeof A==="string"&&A.length<=50}vT(P72,"isValidUserAgentAppId");function S72(A){let B=dw4.normalizeProvider(A.userAgentAppId??T72),{customUserAgent:Q}=A;return Object.assign(A,{customUserAgent:typeof Q==="string"?[[Q]]:Q,userAgentAppId:vT(async()=>{let D=await B();if(!P72(D)){let Z=A.logger?.constructor?.name==="NoOpLogger"||!A.logger?console:A.logger;if(typeof D!=="string")Z?.warn("userAgentAppId must be a string or undefined.");else if(D.length>50)Z?.warn("The provided userAgentAppId exceeds the maximum length of 50 characters.")}return D},"userAgentAppId")})}vT(S72,"resolveUserAgentConfig");var cw4=ws(),lw4=U52(),wL=KI(),pw4=/\d{12}\.ddb/;async function j72(A,B,Q){if(Q.request?.headers?.["smithy-protocol"]==="rpc-v2-cbor")wL.setFeature(A,"PROTOCOL_RPC_V2_CBOR","M");if(typeof B.retryStrategy==="function"){let G=await B.retryStrategy();if(typeof G.acquireInitialRetryToken==="function")if(G.constructor?.name?.includes("Adaptive"))wL.setFeature(A,"RETRY_MODE_ADAPTIVE","F");else wL.setFeature(A,"RETRY_MODE_STANDARD","E");else wL.setFeature(A,"RETRY_MODE_LEGACY","D")}if(typeof B.accountIdEndpointMode==="function"){let G=A.endpointV2;if(String(G?.url?.hostname).match(pw4))wL.setFeature(A,"ACCOUNT_ID_ENDPOINT","O");switch(await B.accountIdEndpointMode?.()){case"disabled":wL.setFeature(A,"ACCOUNT_ID_MODE_DISABLED","Q");break;case"preferred":wL.setFeature(A,"ACCOUNT_ID_MODE_PREFERRED","P");break;case"required":wL.setFeature(A,"ACCOUNT_ID_MODE_REQUIRED","R");break}}let Z=A.__smithy_context?.selectedHttpAuthScheme?.identity;if(Z?.$source){let G=Z;if(G.accountId)wL.setFeature(A,"RESOLVED_ACCOUNT_ID","T");for(let[F,I]of Object.entries(G.$source??{}))wL.setFeature(A,F,I)}}vT(j72,"checkFeatures");var L72="user-agent",z50="x-amz-user-agent",M72=" ",E50="/",iw4=/[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w]/g,nw4=/[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w\#]/g,R72="-",aw4=1024;function y72(A){let B="";for(let Q in A){let D=A[Q];if(B.length+D.length+1<=aw4){if(B.length)B+=","+D;else B+=D;continue}break}return B}vT(y72,"encodeFeatures");var k72=vT((A)=>(B,Q)=>async(D)=>{let{request:Z}=D;if(!lw4.HttpRequest.isInstance(Z))return B(D);let{headers:G}=Z,F=Q?.userAgent?.map(GN1)||[],I=(await A.defaultUserAgentProvider()).map(GN1);await j72(Q,A,D);let Y=Q;I.push(`m/${y72(Object.assign({},Q.__smithy_context?.features,Y.__aws_sdk_context?.features))}`);let W=A?.customUserAgent?.map(GN1)||[],J=await A.userAgentAppId();if(J)I.push(GN1([`app/${J}`]));let X=cw4.getUserAgentPrefix(),V=(X?[X]:[]).concat([...I,...F,...W]).join(M72),C=[...I.filter((K)=>K.startsWith("aws-sdk-")),...W].join(M72);if(A.runtime!=="browser"){if(C)G[z50]=G[z50]?`${G[L72]} ${C}`:C;G[L72]=V}else G[z50]=V;return B({...D,request:Z})},"userAgentMiddleware"),GN1=vT((A)=>{let B=A[0].split(E50).map((F)=>F.replace(iw4,R72)).join(E50),Q=A[1]?.replace(nw4,R72),D=B.indexOf(E50),Z=B.substring(0,D),G=B.substring(D+1);if(Z==="api")G=G.toLowerCase();return[Z,G,Q].filter((F)=>F&&F.length>0).reduce((F,I,Y)=>{switch(Y){case 0:return I;case 1:return`${F}/${I}`;default:return`${F}#${I}`}},"")},"escapeUserAgent"),_72={name:"getUserAgentMiddleware",step:"build",priority:"low",tags:["SET_USER_AGENT","USER_AGENT"],override:!0},sw4=vT((A)=>({applyToStack:vT((B)=>{B.add(k72(A),_72)},"applyToStack")}),"getUserAgentPlugin")});
var S61=E((NE5,hZ2)=>{var{defineProperty:$N1,getOwnPropertyDescriptor:zN4,getOwnPropertyNames:EN4}=Object,UN4=Object.prototype.hasOwnProperty,qL=(A,B)=>$N1(A,"name",{value:B,configurable:!0}),wN4=(A,B)=>{for(var Q in B)$N1(A,Q,{get:B[Q],enumerable:!0})},$N4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of EN4(B))if(!UN4.call(A,Z)&&Z!==Q)$N1(A,Z,{get:()=>B[Z],enumerable:!(D=zN4(B,Z))||D.enumerable})}return A},qN4=(A)=>$N4($N1({},"__esModule",{value:!0}),A),xZ2={};wN4(xZ2,{NODE_REGION_CONFIG_FILE_OPTIONS:()=>RN4,NODE_REGION_CONFIG_OPTIONS:()=>MN4,REGION_ENV_NAME:()=>vZ2,REGION_INI_NAME:()=>bZ2,getAwsRegionExtensionConfiguration:()=>NN4,resolveAwsRegionExtensionConfiguration:()=>LN4,resolveRegionConfig:()=>ON4});hZ2.exports=qN4(xZ2);var NN4=qL((A)=>{return{setRegion(B){A.region=B},region(){return A.region}}},"getAwsRegionExtensionConfiguration"),LN4=qL((A)=>{return{region:A.region()}},"resolveAwsRegionExtensionConfiguration"),vZ2="AWS_REGION",bZ2="region",MN4={environmentVariableSelector:qL((A)=>A[vZ2],"environmentVariableSelector"),configFileSelector:qL((A)=>A[bZ2],"configFileSelector"),default:qL(()=>{throw new Error("Region is missing")},"default")},RN4={preferredFile:"credentials"},fZ2=qL((A)=>typeof A==="string"&&(A.startsWith("fips-")||A.endsWith("-fips")),"isFipsRegion"),_Z2=qL((A)=>fZ2(A)?["fips-aws-global","aws-fips"].includes(A)?"us-east-1":A.replace(/fips-(dkr-|prod-)?|-fips/,""):A,"getRealRegion"),ON4=qL((A)=>{let{region:B,useFipsEndpoint:Q}=A;if(!B)throw new Error("Region is missing");return Object.assign(A,{region:qL(async()=>{if(typeof B==="string")return _Z2(B);let D=await B();return _Z2(D)},"region"),useFipsEndpoint:qL(async()=>{let D=typeof B==="string"?B:await B();if(fZ2(D))return!0;return typeof Q!=="function"?Promise.resolve(!!Q):Q()},"useFipsEndpoint")})},"resolveRegionConfig")});
var T61=E((WE5,iD2)=>{var{defineProperty:EN1,getOwnPropertyDescriptor:O$4,getOwnPropertyNames:T$4}=Object,P$4=Object.prototype.hasOwnProperty,y2=(A,B)=>EN1(A,"name",{value:B,configurable:!0}),S$4=(A,B)=>{for(var Q in B)EN1(A,Q,{get:B[Q],enumerable:!0})},j$4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of T$4(B))if(!P$4.call(A,Z)&&Z!==Q)EN1(A,Z,{get:()=>B[Z],enumerable:!(D=O$4(B,Z))||D.enumerable})}return A},y$4=(A)=>j$4(EN1({},"__esModule",{value:!0}),A),jD2={};S$4(jD2,{Client:()=>k$4,Command:()=>kD2,LazyJsonString:()=>Lg,NoOpLogger:()=>Pq4,SENSITIVE_STRING:()=>x$4,ServiceException:()=>Hq4,_json:()=>S50,collectBody:()=>L50.collectBody,convertMap:()=>Sq4,createAggregatedClient:()=>v$4,dateToUtcString:()=>hD2,decorateServiceException:()=>gD2,emitWarningIfUnsupportedVersion:()=>wq4,expectBoolean:()=>f$4,expectByte:()=>P50,expectFloat32:()=>HN1,expectInt:()=>g$4,expectInt32:()=>O50,expectLong:()=>R61,expectNonNull:()=>m$4,expectNumber:()=>M61,expectObject:()=>_D2,expectShort:()=>T50,expectString:()=>d$4,expectUnion:()=>c$4,extendedEncodeURIComponent:()=>L50.extendedEncodeURIComponent,getArrayIfSingleItem:()=>Oq4,getDefaultClientConfiguration:()=>Mq4,getDefaultExtensionConfiguration:()=>mD2,getValueFromTextNode:()=>dD2,handleFloat:()=>i$4,isSerializableHeaderValue:()=>Tq4,limitedParseDouble:()=>k50,limitedParseFloat:()=>n$4,limitedParseFloat32:()=>a$4,loadConfigsForDefaultMode:()=>Uq4,logger:()=>O61,map:()=>x50,parseBoolean:()=>b$4,parseEpochTimestamp:()=>Fq4,parseRfc3339DateTime:()=>e$4,parseRfc3339DateTimeWithOffset:()=>Bq4,parseRfc7231DateTime:()=>Gq4,quoteHeader:()=>lD2,resolveDefaultRuntimeConfig:()=>Rq4,resolvedPath:()=>L50.resolvedPath,serializeDateTime:()=>vq4,serializeFloat:()=>xq4,splitEvery:()=>pD2,splitHeader:()=>bq4,strictParseByte:()=>fD2,strictParseDouble:()=>y50,strictParseFloat:()=>l$4,strictParseFloat32:()=>xD2,strictParseInt:()=>s$4,strictParseInt32:()=>r$4,strictParseLong:()=>bD2,strictParseShort:()=>Ts,take:()=>jq4,throwDefaultError:()=>uD2,withBaseException:()=>zq4});iD2.exports=y$4(jD2);var yD2=Mw(),k$4=class{constructor(A){this.config=A,this.middlewareStack=yD2.constructStack()}static{y2(this,"Client")}send(A,B,Q){let D=typeof B!=="function"?B:void 0,Z=typeof B==="function"?B:Q,G=D===void 0&&this.config.cacheMiddleware===!0,F;if(G){if(!this.handlers)this.handlers=new WeakMap;let I=this.handlers;if(I.has(A.constructor))F=I.get(A.constructor);else F=A.resolveMiddleware(this.middlewareStack,this.config,D),I.set(A.constructor,F)}else delete this.handlers,F=A.resolveMiddleware(this.middlewareStack,this.config,D);if(Z)F(A).then((I)=>Z(null,I.output),(I)=>Z(I)).catch(()=>{});else return F(A).then((I)=>I.output)}destroy(){this.config?.requestHandler?.destroy?.(),delete this.handlers}},L50=M6(),R50=N50(),kD2=class{constructor(){this.middlewareStack=yD2.constructStack()}static{y2(this,"Command")}static classBuilder(){return new _$4}resolveMiddlewareWithContext(A,B,Q,{middlewareFn:D,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,smithyContext:Y,additionalContext:W,CommandCtor:J}){for(let H of D.bind(this)(J,A,B,Q))this.middlewareStack.use(H);let X=A.concat(this.middlewareStack),{logger:V}=B,C={logger:V,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,[R50.SMITHY_CONTEXT_KEY]:{commandInstance:this,...Y},...W},{requestHandler:K}=B;return X.resolve((H)=>K.handle(H.request,Q||{}),C)}},_$4=class{constructor(){this._init=()=>{},this._ep={},this._middlewareFn=()=>[],this._commandName="",this._clientName="",this._additionalContext={},this._smithyContext={},this._inputFilterSensitiveLog=(A)=>A,this._outputFilterSensitiveLog=(A)=>A,this._serializer=null,this._deserializer=null}static{y2(this,"ClassBuilder")}init(A){this._init=A}ep(A){return this._ep=A,this}m(A){return this._middlewareFn=A,this}s(A,B,Q={}){return this._smithyContext={service:A,operation:B,...Q},this}c(A={}){return this._additionalContext=A,this}n(A,B){return this._clientName=A,this._commandName=B,this}f(A=(Q)=>Q,B=(Q)=>Q){return this._inputFilterSensitiveLog=A,this._outputFilterSensitiveLog=B,this}ser(A){return this._serializer=A,this}de(A){return this._deserializer=A,this}build(){let A=this,B;return B=class extends kD2{constructor(...[Q]){super();this.serialize=A._serializer,this.deserialize=A._deserializer,this.input=Q??{},A._init(this)}static{y2(this,"CommandRef")}static getEndpointParameterInstructions(){return A._ep}resolveMiddleware(Q,D,Z){return this.resolveMiddlewareWithContext(Q,D,Z,{CommandCtor:B,middlewareFn:A._middlewareFn,clientName:A._clientName,commandName:A._commandName,inputFilterSensitiveLog:A._inputFilterSensitiveLog,outputFilterSensitiveLog:A._outputFilterSensitiveLog,smithyContext:A._smithyContext,additionalContext:A._additionalContext})}}}},x$4="***SensitiveInformation***",v$4=y2((A,B)=>{for(let Q of Object.keys(A)){let D=A[Q],Z=y2(async function(F,I,Y){let W=new D(F);if(typeof I==="function")this.send(W,I);else if(typeof Y==="function"){if(typeof I!=="object")throw new Error(`Expected http options but got ${typeof I}`);this.send(W,I||{},Y)}else return this.send(W,I)},"methodImpl"),G=(Q[0].toLowerCase()+Q.slice(1)).replace(/Command$/,"");B.prototype[G]=Z}},"createAggregatedClient"),b$4=y2((A)=>{switch(A){case"true":return!0;case"false":return!1;default:throw new Error(`Unable to parse boolean value "${A}"`)}},"parseBoolean"),f$4=y2((A)=>{if(A===null||A===void 0)return;if(typeof A==="number"){if(A===0||A===1)O61.warn(zN1(`Expected boolean, got ${typeof A}: ${A}`));if(A===0)return!1;if(A===1)return!0}if(typeof A==="string"){let B=A.toLowerCase();if(B==="false"||B==="true")O61.warn(zN1(`Expected boolean, got ${typeof A}: ${A}`));if(B==="false")return!1;if(B==="true")return!0}if(typeof A==="boolean")return A;throw new TypeError(`Expected boolean, got ${typeof A}: ${A}`)},"expectBoolean"),M61=y2((A)=>{if(A===null||A===void 0)return;if(typeof A==="string"){let B=parseFloat(A);if(!Number.isNaN(B)){if(String(B)!==String(A))O61.warn(zN1(`Expected number but observed string: ${A}`));return B}}if(typeof A==="number")return A;throw new TypeError(`Expected number, got ${typeof A}: ${A}`)},"expectNumber"),h$4=Math.ceil(340282346638528860000000000000000000000),HN1=y2((A)=>{let B=M61(A);if(B!==void 0&&!Number.isNaN(B)&&B!==1/0&&B!==-1/0){if(Math.abs(B)>h$4)throw new TypeError(`Expected 32-bit float, got ${A}`)}return B},"expectFloat32"),R61=y2((A)=>{if(A===null||A===void 0)return;if(Number.isInteger(A)&&!Number.isNaN(A))return A;throw new TypeError(`Expected integer, got ${typeof A}: ${A}`)},"expectLong"),g$4=R61,O50=y2((A)=>j50(A,32),"expectInt32"),T50=y2((A)=>j50(A,16),"expectShort"),P50=y2((A)=>j50(A,8),"expectByte"),j50=y2((A,B)=>{let Q=R61(A);if(Q!==void 0&&u$4(Q,B)!==Q)throw new TypeError(`Expected ${B}-bit integer, got ${A}`);return Q},"expectSizedInt"),u$4=y2((A,B)=>{switch(B){case 32:return Int32Array.of(A)[0];case 16:return Int16Array.of(A)[0];case 8:return Int8Array.of(A)[0]}},"castInt"),m$4=y2((A,B)=>{if(A===null||A===void 0){if(B)throw new TypeError(`Expected a non-null value for ${B}`);throw new TypeError("Expected a non-null value")}return A},"expectNonNull"),_D2=y2((A)=>{if(A===null||A===void 0)return;if(typeof A==="object"&&!Array.isArray(A))return A;let B=Array.isArray(A)?"array":typeof A;throw new TypeError(`Expected object, got ${B}: ${A}`)},"expectObject"),d$4=y2((A)=>{if(A===null||A===void 0)return;if(typeof A==="string")return A;if(["boolean","number","bigint"].includes(typeof A))return O61.warn(zN1(`Expected string, got ${typeof A}: ${A}`)),String(A);throw new TypeError(`Expected string, got ${typeof A}: ${A}`)},"expectString"),c$4=y2((A)=>{if(A===null||A===void 0)return;let B=_D2(A),Q=Object.entries(B).filter(([,D])=>D!=null).map(([D])=>D);if(Q.length===0)throw new TypeError("Unions must have exactly one non-null member. None were found.");if(Q.length>1)throw new TypeError(`Unions must have exactly one non-null member. Keys ${Q} were not null.`);return B},"expectUnion"),y50=y2((A)=>{if(typeof A=="string")return M61(Ss(A));return M61(A)},"strictParseDouble"),l$4=y50,xD2=y2((A)=>{if(typeof A=="string")return HN1(Ss(A));return HN1(A)},"strictParseFloat32"),p$4=/(-?(?:0|[1-9]\d*)(?:\.\d+)?(?:[eE][+-]?\d+)?)|(-?Infinity)|(NaN)/g,Ss=y2((A)=>{let B=A.match(p$4);if(B===null||B[0].length!==A.length)throw new TypeError("Expected real number, got implicit NaN");return parseFloat(A)},"parseNumber"),k50=y2((A)=>{if(typeof A=="string")return vD2(A);return M61(A)},"limitedParseDouble"),i$4=k50,n$4=k50,a$4=y2((A)=>{if(typeof A=="string")return vD2(A);return HN1(A)},"limitedParseFloat32"),vD2=y2((A)=>{switch(A){case"NaN":return NaN;case"Infinity":return 1/0;case"-Infinity":return-1/0;default:throw new Error(`Unable to parse float value: ${A}`)}},"parseFloatString"),bD2=y2((A)=>{if(typeof A==="string")return R61(Ss(A));return R61(A)},"strictParseLong"),s$4=bD2,r$4=y2((A)=>{if(typeof A==="string")return O50(Ss(A));return O50(A)},"strictParseInt32"),Ts=y2((A)=>{if(typeof A==="string")return T50(Ss(A));return T50(A)},"strictParseShort"),fD2=y2((A)=>{if(typeof A==="string")return P50(Ss(A));return P50(A)},"strictParseByte"),zN1=y2((A)=>{return String(new TypeError(A).stack||A).split(`
`).slice(0,5).filter((B)=>!B.includes("stackTraceWarning")).join(`
`)},"stackTraceWarning"),O61={warn:console.warn},o$4=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],_50=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function hD2(A){let B=A.getUTCFullYear(),Q=A.getUTCMonth(),D=A.getUTCDay(),Z=A.getUTCDate(),G=A.getUTCHours(),F=A.getUTCMinutes(),I=A.getUTCSeconds(),Y=Z<10?`0${Z}`:`${Z}`,W=G<10?`0${G}`:`${G}`,J=F<10?`0${F}`:`${F}`,X=I<10?`0${I}`:`${I}`;return`${o$4[D]}, ${Y} ${_50[Q]} ${B} ${W}:${J}:${X} GMT`}y2(hD2,"dateToUtcString");var t$4=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?[zZ]$/),e$4=y2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-3339 date-times must be expressed as strings");let B=t$4.exec(A);if(!B)throw new TypeError("Invalid RFC-3339 date-time value");let[Q,D,Z,G,F,I,Y,W]=B,J=Ts(Ps(D)),X=$L(Z,"month",1,12),V=$L(G,"day",1,31);return L61(J,X,V,{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W})},"parseRfc3339DateTime"),Aq4=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?(([-+]\d{2}\:\d{2})|[zZ])$/),Bq4=y2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-3339 date-times must be expressed as strings");let B=Aq4.exec(A);if(!B)throw new TypeError("Invalid RFC-3339 date-time value");let[Q,D,Z,G,F,I,Y,W,J]=B,X=Ts(Ps(D)),V=$L(Z,"month",1,12),C=$L(G,"day",1,31),K=L61(X,V,C,{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W});if(J.toUpperCase()!="Z")K.setTime(K.getTime()-Kq4(J));return K},"parseRfc3339DateTimeWithOffset"),Qq4=new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d{2}) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/),Dq4=new RegExp(/^(?:Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d{2})-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/),Zq4=new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( [1-9]|\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? (\d{4})$/),Gq4=y2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-7231 date-times must be expressed as strings");let B=Qq4.exec(A);if(B){let[Q,D,Z,G,F,I,Y,W]=B;return L61(Ts(Ps(G)),M50(Z),$L(D,"day",1,31),{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W})}if(B=Dq4.exec(A),B){let[Q,D,Z,G,F,I,Y,W]=B;return Wq4(L61(Iq4(G),M50(Z),$L(D,"day",1,31),{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W}))}if(B=Zq4.exec(A),B){let[Q,D,Z,G,F,I,Y,W]=B;return L61(Ts(Ps(W)),M50(D),$L(Z.trimLeft(),"day",1,31),{hours:G,minutes:F,seconds:I,fractionalMilliseconds:Y})}throw new TypeError("Invalid RFC-7231 date-time value")},"parseRfc7231DateTime"),Fq4=y2((A)=>{if(A===null||A===void 0)return;let B;if(typeof A==="number")B=A;else if(typeof A==="string")B=y50(A);else if(typeof A==="object"&&A.tag===1)B=A.value;else throw new TypeError("Epoch timestamps must be expressed as floating point numbers or their string representation");if(Number.isNaN(B)||B===1/0||B===-1/0)throw new TypeError("Epoch timestamps must be valid, non-Infinite, non-NaN numerics");return new Date(Math.round(B*1000))},"parseEpochTimestamp"),L61=y2((A,B,Q,D)=>{let Z=B-1;return Xq4(A,Z,Q),new Date(Date.UTC(A,Z,Q,$L(D.hours,"hour",0,23),$L(D.minutes,"minute",0,59),$L(D.seconds,"seconds",0,60),Cq4(D.fractionalMilliseconds)))},"buildDate"),Iq4=y2((A)=>{let B=new Date().getUTCFullYear(),Q=Math.floor(B/100)*100+Ts(Ps(A));if(Q<B)return Q+100;return Q},"parseTwoDigitYear"),Yq4=1576800000000,Wq4=y2((A)=>{if(A.getTime()-new Date().getTime()>Yq4)return new Date(Date.UTC(A.getUTCFullYear()-100,A.getUTCMonth(),A.getUTCDate(),A.getUTCHours(),A.getUTCMinutes(),A.getUTCSeconds(),A.getUTCMilliseconds()));return A},"adjustRfc850Year"),M50=y2((A)=>{let B=_50.indexOf(A);if(B<0)throw new TypeError(`Invalid month: ${A}`);return B+1},"parseMonthByShortName"),Jq4=[31,28,31,30,31,30,31,31,30,31,30,31],Xq4=y2((A,B,Q)=>{let D=Jq4[B];if(B===1&&Vq4(A))D=29;if(Q>D)throw new TypeError(`Invalid day for ${_50[B]} in ${A}: ${Q}`)},"validateDayOfMonth"),Vq4=y2((A)=>{return A%4===0&&(A%100!==0||A%400===0)},"isLeapYear"),$L=y2((A,B,Q,D)=>{let Z=fD2(Ps(A));if(Z<Q||Z>D)throw new TypeError(`${B} must be between ${Q} and ${D}, inclusive`);return Z},"parseDateValue"),Cq4=y2((A)=>{if(A===null||A===void 0)return 0;return xD2("0."+A)*1000},"parseMilliseconds"),Kq4=y2((A)=>{let B=A[0],Q=1;if(B=="+")Q=1;else if(B=="-")Q=-1;else throw new TypeError(`Offset direction, ${B}, must be "+" or "-"`);let D=Number(A.substring(1,3)),Z=Number(A.substring(4,6));return Q*(D*60+Z)*60*1000},"parseOffsetToMilliseconds"),Ps=y2((A)=>{let B=0;while(B<A.length-1&&A.charAt(B)==="0")B++;if(B===0)return A;return A.slice(B)},"stripLeadingZeroes"),Hq4=class A extends Error{static{y2(this,"ServiceException")}constructor(B){super(B.message);Object.setPrototypeOf(this,Object.getPrototypeOf(this).constructor.prototype),this.name=B.name,this.$fault=B.$fault,this.$metadata=B.$metadata}static isInstance(B){if(!B)return!1;let Q=B;return A.prototype.isPrototypeOf(Q)||Boolean(Q.$fault)&&Boolean(Q.$metadata)&&(Q.$fault==="client"||Q.$fault==="server")}static[Symbol.hasInstance](B){if(!B)return!1;let Q=B;if(this===A)return A.isInstance(B);if(A.isInstance(B)){if(Q.name&&this.name)return this.prototype.isPrototypeOf(B)||Q.name===this.name;return this.prototype.isPrototypeOf(B)}return!1}},gD2=y2((A,B={})=>{Object.entries(B).filter(([,D])=>D!==void 0).forEach(([D,Z])=>{if(A[D]==null||A[D]==="")A[D]=Z});let Q=A.message||A.Message||"UnknownError";return A.message=Q,delete A.Message,A},"decorateServiceException"),uD2=y2(({output:A,parsedBody:B,exceptionCtor:Q,errorCode:D})=>{let Z=Eq4(A),G=Z.httpStatusCode?Z.httpStatusCode+"":void 0,F=new Q({name:B?.code||B?.Code||D||G||"UnknownError",$fault:"client",$metadata:Z});throw gD2(F,B)},"throwDefaultError"),zq4=y2((A)=>{return({output:B,parsedBody:Q,errorCode:D})=>{uD2({output:B,parsedBody:Q,exceptionCtor:A,errorCode:D})}},"withBaseException"),Eq4=y2((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),Uq4=y2((A)=>{switch(A){case"standard":return{retryMode:"standard",connectionTimeout:3100};case"in-region":return{retryMode:"standard",connectionTimeout:1100};case"cross-region":return{retryMode:"standard",connectionTimeout:3100};case"mobile":return{retryMode:"standard",connectionTimeout:30000};default:return{}}},"loadConfigsForDefaultMode"),SD2=!1,wq4=y2((A)=>{if(A&&!SD2&&parseInt(A.substring(1,A.indexOf(".")))<16)SD2=!0},"emitWarningIfUnsupportedVersion"),$q4=y2((A)=>{let B=[];for(let Q in R50.AlgorithmId){let D=R50.AlgorithmId[Q];if(A[D]===void 0)continue;B.push({algorithmId:()=>D,checksumConstructor:()=>A[D]})}return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),qq4=y2((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),Nq4=y2((A)=>{return{setRetryStrategy(B){A.retryStrategy=B},retryStrategy(){return A.retryStrategy}}},"getRetryConfiguration"),Lq4=y2((A)=>{let B={};return B.retryStrategy=A.retryStrategy(),B},"resolveRetryRuntimeConfig"),mD2=y2((A)=>{return Object.assign($q4(A),Nq4(A))},"getDefaultExtensionConfiguration"),Mq4=mD2,Rq4=y2((A)=>{return Object.assign(qq4(A),Lq4(A))},"resolveDefaultRuntimeConfig"),Oq4=y2((A)=>Array.isArray(A)?A:[A],"getArrayIfSingleItem"),dD2=y2((A)=>{for(let Q in A)if(A.hasOwnProperty(Q)&&A[Q]["#text"]!==void 0)A[Q]=A[Q]["#text"];else if(typeof A[Q]==="object"&&A[Q]!==null)A[Q]=dD2(A[Q]);return A},"getValueFromTextNode"),Tq4=y2((A)=>{return A!=null},"isSerializableHeaderValue"),Lg=y2(function A(B){return Object.assign(new String(B),{deserializeJSON(){return JSON.parse(String(B))},toString(){return String(B)},toJSON(){return String(B)}})},"LazyJsonString");Lg.from=(A)=>{if(A&&typeof A==="object"&&(A instanceof Lg||("deserializeJSON"in A)))return A;else if(typeof A==="string"||Object.getPrototypeOf(A)===String.prototype)return Lg(String(A));return Lg(JSON.stringify(A))};Lg.fromObject=Lg.from;var Pq4=class{static{y2(this,"NoOpLogger")}trace(){}debug(){}info(){}warn(){}error(){}};function x50(A,B,Q){let D,Z,G;if(typeof B==="undefined"&&typeof Q==="undefined")D={},G=A;else if(D=A,typeof B==="function")return Z=B,G=Q,yq4(D,Z,G);else G=B;for(let F of Object.keys(G)){if(!Array.isArray(G[F])){D[F]=G[F];continue}cD2(D,null,G,F)}return D}y2(x50,"map");var Sq4=y2((A)=>{let B={};for(let[Q,D]of Object.entries(A||{}))B[Q]=[,D];return B},"convertMap"),jq4=y2((A,B)=>{let Q={};for(let D in B)cD2(Q,A,B,D);return Q},"take"),yq4=y2((A,B,Q)=>{return x50(A,Object.entries(Q).reduce((D,[Z,G])=>{if(Array.isArray(G))D[Z]=G;else if(typeof G==="function")D[Z]=[B,G()];else D[Z]=[B,G];return D},{}))},"mapWithFilter"),cD2=y2((A,B,Q,D)=>{if(B!==null){let F=Q[D];if(typeof F==="function")F=[,F];let[I=kq4,Y=_q4,W=D]=F;if(typeof I==="function"&&I(B[W])||typeof I!=="function"&&!!I)A[D]=Y(B[W]);return}let[Z,G]=Q[D];if(typeof G==="function"){let F,I=Z===void 0&&(F=G())!=null,Y=typeof Z==="function"&&!!Z(void 0)||typeof Z!=="function"&&!!Z;if(I)A[D]=F;else if(Y)A[D]=G()}else{let F=Z===void 0&&G!=null,I=typeof Z==="function"&&!!Z(G)||typeof Z!=="function"&&!!Z;if(F||I)A[D]=G}},"applyInstruction"),kq4=y2((A)=>A!=null,"nonNullish"),_q4=y2((A)=>A,"pass");function lD2(A){if(A.includes(",")||A.includes('"'))A=`"${A.replace(/"/g,"\\\"")}"`;return A}y2(lD2,"quoteHeader");var xq4=y2((A)=>{if(A!==A)return"NaN";switch(A){case 1/0:return"Infinity";case-1/0:return"-Infinity";default:return A}},"serializeFloat"),vq4=y2((A)=>A.toISOString().replace(".000Z","Z"),"serializeDateTime"),S50=y2((A)=>{if(A==null)return{};if(Array.isArray(A))return A.filter((B)=>B!=null).map(S50);if(typeof A==="object"){let B={};for(let Q of Object.keys(A)){if(A[Q]==null)continue;B[Q]=S50(A[Q])}return B}return A},"_json");function pD2(A,B,Q){if(Q<=0||!Number.isInteger(Q))throw new Error("Invalid number of delimiters ("+Q+") for splitEvery.");let D=A.split(B);if(Q===1)return D;let Z=[],G="";for(let F=0;F<D.length;F++){if(G==="")G=D[F];else G+=B+D[F];if((F+1)%Q===0)Z.push(G),G=""}if(G!=="")Z.push(G);return Z}y2(pD2,"splitEvery");var bq4=y2((A)=>{let B=A.length,Q=[],D=!1,Z=void 0,G=0;for(let F=0;F<B;++F){let I=A[F];switch(I){case'"':if(Z!=="\\")D=!D;break;case",":if(!D)Q.push(A.slice(G,F)),G=F+1;break;default:}Z=I}return Q.push(A.slice(G)),Q.map((F)=>{F=F.trim();let I=F.length;if(I<2)return F;if(F[0]==='"'&&F[I-1]==='"')F=F.slice(1,I-1);return F.replace(/\\"/g,'"')})},"splitHeader")});
var TF2=E((RF2)=>{Object.defineProperty(RF2,"__esModule",{value:!0});RF2.toBase64=void 0;var tM4=YD(),eM4=cB(),AR4=(A)=>{let B;if(typeof A==="string")B=eM4.fromUtf8(A);else B=A;if(typeof B!=="object"||typeof B.byteOffset!=="number"||typeof B.byteLength!=="number")throw new Error("@smithy/util-base64: toBase64 encoder function only accepts string | Uint8Array.");return tM4.fromArrayBuffer(B.buffer,B.byteOffset,B.byteLength).toString("base64")};RF2.toBase64=AR4});
var U52=E((jz5,E52)=>{var{defineProperty:fq1,getOwnPropertyDescriptor:Rz4,getOwnPropertyNames:Oz4}=Object,Tz4=Object.prototype.hasOwnProperty,dk=(A,B)=>fq1(A,"name",{value:B,configurable:!0}),Pz4=(A,B)=>{for(var Q in B)fq1(A,Q,{get:B[Q],enumerable:!0})},Sz4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Oz4(B))if(!Tz4.call(A,Z)&&Z!==Q)fq1(A,Z,{get:()=>B[Z],enumerable:!(D=Rz4(B,Z))||D.enumerable})}return A},jz4=(A)=>Sz4(fq1({},"__esModule",{value:!0}),A),C52={};Pz4(C52,{Field:()=>_z4,Fields:()=>xz4,HttpRequest:()=>vz4,HttpResponse:()=>bz4,IHttpRequest:()=>K52.HttpRequest,getHttpHandlerExtensionConfiguration:()=>yz4,isValidHostname:()=>z52,resolveHttpHandlerRuntimeConfig:()=>kz4});E52.exports=jz4(C52);var yz4=dk((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),kz4=dk((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),K52=V52(),_z4=class{static{dk(this,"Field")}constructor({name:A,kind:B=K52.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},xz4=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{dk(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},vz4=class A{static{dk(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=H52(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function H52(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}dk(H52,"cloneQuery");var bz4=class{static{dk(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function z52(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}dk(z52,"isValidHostname")});
var UCB=E((zCB)=>{Object.defineProperty(zCB,"__esModule",{value:!0});zCB.toBase64=void 0;var Jn6=YD(),Xn6=cB(),Vn6=(A)=>{let B;if(typeof A==="string")B=Xn6.fromUtf8(A);else B=A;if(typeof B!=="object"||typeof B.byteOffset!=="number"||typeof B.byteLength!=="number")throw new Error("@smithy/util-base64: toBase64 encoder function only accepts string | Uint8Array.");return Jn6.fromArrayBuffer(B.buffer,B.byteOffset,B.byteLength).toString("base64")};zCB.toBase64=Vn6});
var UG2=E((TE5,EG2)=>{var{defineProperty:NN1,getOwnPropertyDescriptor:gN4,getOwnPropertyNames:uN4}=Object,mN4=Object.prototype.hasOwnProperty,j6=(A,B)=>NN1(A,"name",{value:B,configurable:!0}),dN4=(A,B)=>{for(var Q in B)NN1(A,Q,{get:B[Q],enumerable:!0})},cN4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of uN4(B))if(!mN4.call(A,Z)&&Z!==Q)NN1(A,Z,{get:()=>B[Z],enumerable:!(D=gN4(B,Z))||D.enumerable})}return A},lN4=(A)=>cN4(NN1({},"__esModule",{value:!0}),A),oZ2={};dN4(oZ2,{GetRoleCredentialsCommand:()=>KG2,GetRoleCredentialsRequestFilterSensitiveLog:()=>QG2,GetRoleCredentialsResponseFilterSensitiveLog:()=>ZG2,InvalidRequestException:()=>tZ2,ListAccountRolesCommand:()=>u50,ListAccountRolesRequestFilterSensitiveLog:()=>GG2,ListAccountsCommand:()=>m50,ListAccountsRequestFilterSensitiveLog:()=>FG2,LogoutCommand:()=>HG2,LogoutRequestFilterSensitiveLog:()=>IG2,ResourceNotFoundException:()=>eZ2,RoleCredentialsFilterSensitiveLog:()=>DG2,SSO:()=>zG2,SSOClient:()=>MN1,SSOServiceException:()=>xs,TooManyRequestsException:()=>AG2,UnauthorizedException:()=>BG2,__Client:()=>yB.Client,paginateListAccountRoles:()=>zL4,paginateListAccounts:()=>EL4});EG2.exports=lN4(oZ2);var pZ2=V61(),pN4=C61(),iN4=K61(),iZ2=Rs(),nN4=K4(),fT=CB(),aN4=bG(),y61=R6(),nZ2=u4(),aZ2=w50(),sN4=j6((A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,defaultSigningName:"awsssoportal"})},"resolveClientEndpointParameters"),LN1={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}},rN4=kZ2(),sZ2=S61(),rZ2=lZ2(),yB=T61(),oN4=j6((A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}},"getHttpAuthExtensionConfiguration"),tN4=j6((A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}},"resolveHttpAuthRuntimeConfig"),eN4=j6((A,B)=>{let Q=Object.assign(sZ2.getAwsRegionExtensionConfiguration(A),yB.getDefaultExtensionConfiguration(A),rZ2.getHttpHandlerExtensionConfiguration(A),oN4(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,sZ2.resolveAwsRegionExtensionConfiguration(Q),yB.resolveDefaultRuntimeConfig(Q),rZ2.resolveHttpHandlerRuntimeConfig(Q),tN4(Q))},"resolveRuntimeExtensions"),MN1=class extends yB.Client{static{j6(this,"SSOClient")}config;constructor(...[A]){let B=rN4.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=sN4(B),D=iZ2.resolveUserAgentConfig(Q),Z=nZ2.resolveRetryConfig(D),G=nN4.resolveRegionConfig(Z),F=pZ2.resolveHostHeaderConfig(G),I=y61.resolveEndpointConfig(F),Y=aZ2.resolveHttpAuthSchemeConfig(I),W=eN4(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(iZ2.getUserAgentPlugin(this.config)),this.middlewareStack.use(nZ2.getRetryPlugin(this.config)),this.middlewareStack.use(aN4.getContentLengthPlugin(this.config)),this.middlewareStack.use(pZ2.getHostHeaderPlugin(this.config)),this.middlewareStack.use(pN4.getLoggerPlugin(this.config)),this.middlewareStack.use(iN4.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(fT.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:aZ2.defaultSSOHttpAuthSchemeParametersProvider,identityProviderConfigProvider:j6(async(J)=>new fT.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials}),"identityProviderConfigProvider")})),this.middlewareStack.use(fT.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}},RN1=j3(),xs=class A extends yB.ServiceException{static{j6(this,"SSOServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},tZ2=class A extends xs{static{j6(this,"InvalidRequestException")}name="InvalidRequestException";$fault="client";constructor(B){super({name:"InvalidRequestException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},eZ2=class A extends xs{static{j6(this,"ResourceNotFoundException")}name="ResourceNotFoundException";$fault="client";constructor(B){super({name:"ResourceNotFoundException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},AG2=class A extends xs{static{j6(this,"TooManyRequestsException")}name="TooManyRequestsException";$fault="client";constructor(B){super({name:"TooManyRequestsException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},BG2=class A extends xs{static{j6(this,"UnauthorizedException")}name="UnauthorizedException";$fault="client";constructor(B){super({name:"UnauthorizedException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},QG2=j6((A)=>({...A,...A.accessToken&&{accessToken:yB.SENSITIVE_STRING}}),"GetRoleCredentialsRequestFilterSensitiveLog"),DG2=j6((A)=>({...A,...A.secretAccessKey&&{secretAccessKey:yB.SENSITIVE_STRING},...A.sessionToken&&{sessionToken:yB.SENSITIVE_STRING}}),"RoleCredentialsFilterSensitiveLog"),ZG2=j6((A)=>({...A,...A.roleCredentials&&{roleCredentials:DG2(A.roleCredentials)}}),"GetRoleCredentialsResponseFilterSensitiveLog"),GG2=j6((A)=>({...A,...A.accessToken&&{accessToken:yB.SENSITIVE_STRING}}),"ListAccountRolesRequestFilterSensitiveLog"),FG2=j6((A)=>({...A,...A.accessToken&&{accessToken:yB.SENSITIVE_STRING}}),"ListAccountsRequestFilterSensitiveLog"),IG2=j6((A)=>({...A,...A.accessToken&&{accessToken:yB.SENSITIVE_STRING}}),"LogoutRequestFilterSensitiveLog"),j61=KI(),AL4=j6(async(A,B)=>{let Q=fT.requestBuilder(A,B),D=yB.map({},yB.isSerializableHeaderValue,{[PN1]:A[TN1]});Q.bp("/federation/credentials");let Z=yB.map({[KL4]:[,yB.expectNonNull(A[CL4],"roleName")],[WG2]:[,yB.expectNonNull(A[YG2],"accountId")]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_GetRoleCredentialsCommand"),BL4=j6(async(A,B)=>{let Q=fT.requestBuilder(A,B),D=yB.map({},yB.isSerializableHeaderValue,{[PN1]:A[TN1]});Q.bp("/assignment/roles");let Z=yB.map({[CG2]:[,A[VG2]],[XG2]:[()=>A.maxResults!==void 0,()=>A[JG2].toString()],[WG2]:[,yB.expectNonNull(A[YG2],"accountId")]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListAccountRolesCommand"),QL4=j6(async(A,B)=>{let Q=fT.requestBuilder(A,B),D=yB.map({},yB.isSerializableHeaderValue,{[PN1]:A[TN1]});Q.bp("/assignment/accounts");let Z=yB.map({[CG2]:[,A[VG2]],[XG2]:[()=>A.maxResults!==void 0,()=>A[JG2].toString()]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListAccountsCommand"),DL4=j6(async(A,B)=>{let Q=fT.requestBuilder(A,B),D=yB.map({},yB.isSerializableHeaderValue,{[PN1]:A[TN1]});Q.bp("/logout");let Z;return Q.m("POST").h(D).b(Z),Q.build()},"se_LogoutCommand"),ZL4=j6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return ON1(A,B);let Q=yB.map({$metadata:ik(A)}),D=yB.expectNonNull(yB.expectObject(await j61.parseJsonBody(A.body,B)),"body"),Z=yB.take(D,{roleCredentials:yB._json});return Object.assign(Q,Z),Q},"de_GetRoleCredentialsCommand"),GL4=j6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return ON1(A,B);let Q=yB.map({$metadata:ik(A)}),D=yB.expectNonNull(yB.expectObject(await j61.parseJsonBody(A.body,B)),"body"),Z=yB.take(D,{nextToken:yB.expectString,roleList:yB._json});return Object.assign(Q,Z),Q},"de_ListAccountRolesCommand"),FL4=j6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return ON1(A,B);let Q=yB.map({$metadata:ik(A)}),D=yB.expectNonNull(yB.expectObject(await j61.parseJsonBody(A.body,B)),"body"),Z=yB.take(D,{accountList:yB._json,nextToken:yB.expectString});return Object.assign(Q,Z),Q},"de_ListAccountsCommand"),IL4=j6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return ON1(A,B);let Q=yB.map({$metadata:ik(A)});return await yB.collectBody(A.body,B),Q},"de_LogoutCommand"),ON1=j6(async(A,B)=>{let Q={...A,body:await j61.parseJsonErrorBody(A.body,B)},D=j61.loadRestJsonErrorCode(A,Q.body);switch(D){case"InvalidRequestException":case"com.amazonaws.sso#InvalidRequestException":throw await WL4(Q,B);case"ResourceNotFoundException":case"com.amazonaws.sso#ResourceNotFoundException":throw await JL4(Q,B);case"TooManyRequestsException":case"com.amazonaws.sso#TooManyRequestsException":throw await XL4(Q,B);case"UnauthorizedException":case"com.amazonaws.sso#UnauthorizedException":throw await VL4(Q,B);default:let Z=Q.body;return YL4({output:A,parsedBody:Z,errorCode:D})}},"de_CommandError"),YL4=yB.withBaseException(xs),WL4=j6(async(A,B)=>{let Q=yB.map({}),D=A.body,Z=yB.take(D,{message:yB.expectString});Object.assign(Q,Z);let G=new tZ2({$metadata:ik(A),...Q});return yB.decorateServiceException(G,A.body)},"de_InvalidRequestExceptionRes"),JL4=j6(async(A,B)=>{let Q=yB.map({}),D=A.body,Z=yB.take(D,{message:yB.expectString});Object.assign(Q,Z);let G=new eZ2({$metadata:ik(A),...Q});return yB.decorateServiceException(G,A.body)},"de_ResourceNotFoundExceptionRes"),XL4=j6(async(A,B)=>{let Q=yB.map({}),D=A.body,Z=yB.take(D,{message:yB.expectString});Object.assign(Q,Z);let G=new AG2({$metadata:ik(A),...Q});return yB.decorateServiceException(G,A.body)},"de_TooManyRequestsExceptionRes"),VL4=j6(async(A,B)=>{let Q=yB.map({}),D=A.body,Z=yB.take(D,{message:yB.expectString});Object.assign(Q,Z);let G=new BG2({$metadata:ik(A),...Q});return yB.decorateServiceException(G,A.body)},"de_UnauthorizedExceptionRes"),ik=j6((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),YG2="accountId",TN1="accessToken",WG2="account_id",JG2="maxResults",XG2="max_result",VG2="nextToken",CG2="next_token",CL4="roleName",KL4="role_name",PN1="x-amz-sso_bearer_token",KG2=class extends yB.Command.classBuilder().ep(LN1).m(function(A,B,Q,D){return[RN1.getSerdePlugin(Q,this.serialize,this.deserialize),y61.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","GetRoleCredentials",{}).n("SSOClient","GetRoleCredentialsCommand").f(QG2,ZG2).ser(AL4).de(ZL4).build(){static{j6(this,"GetRoleCredentialsCommand")}},u50=class extends yB.Command.classBuilder().ep(LN1).m(function(A,B,Q,D){return[RN1.getSerdePlugin(Q,this.serialize,this.deserialize),y61.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","ListAccountRoles",{}).n("SSOClient","ListAccountRolesCommand").f(GG2,void 0).ser(BL4).de(GL4).build(){static{j6(this,"ListAccountRolesCommand")}},m50=class extends yB.Command.classBuilder().ep(LN1).m(function(A,B,Q,D){return[RN1.getSerdePlugin(Q,this.serialize,this.deserialize),y61.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","ListAccounts",{}).n("SSOClient","ListAccountsCommand").f(FG2,void 0).ser(QL4).de(FL4).build(){static{j6(this,"ListAccountsCommand")}},HG2=class extends yB.Command.classBuilder().ep(LN1).m(function(A,B,Q,D){return[RN1.getSerdePlugin(Q,this.serialize,this.deserialize),y61.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","Logout",{}).n("SSOClient","LogoutCommand").f(IG2,void 0).ser(DL4).de(IL4).build(){static{j6(this,"LogoutCommand")}},HL4={GetRoleCredentialsCommand:KG2,ListAccountRolesCommand:u50,ListAccountsCommand:m50,LogoutCommand:HG2},zG2=class extends MN1{static{j6(this,"SSO")}};yB.createAggregatedClient(HL4,zG2);var zL4=fT.createPaginator(MN1,u50,"nextToken","nextToken","maxResults"),EL4=fT.createPaginator(MN1,m50,"nextToken","nextToken","maxResults")});
var UJB=E((a03,EJB)=>{var{defineProperty:gk1,getOwnPropertyDescriptor:Fc6,getOwnPropertyNames:Ic6}=Object,Yc6=Object.prototype.hasOwnProperty,uk1=(A,B)=>gk1(A,"name",{value:B,configurable:!0}),Wc6=(A,B)=>{for(var Q in B)gk1(A,Q,{get:B[Q],enumerable:!0})},Jc6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Ic6(B))if(!Yc6.call(A,Z)&&Z!==Q)gk1(A,Z,{get:()=>B[Z],enumerable:!(D=Fc6(B,Z))||D.enumerable})}return A},Xc6=(A)=>Jc6(gk1({},"__esModule",{value:!0}),A),WJB={};Wc6(WJB,{AlgorithmId:()=>CJB,EndpointURLScheme:()=>VJB,FieldPosition:()=>KJB,HttpApiKeyAuthLocation:()=>XJB,HttpAuthLocation:()=>JJB,IniSectionType:()=>HJB,RequestHandlerProtocol:()=>zJB,SMITHY_CONTEXT_KEY:()=>zc6,getDefaultClientConfiguration:()=>Kc6,resolveDefaultRuntimeConfig:()=>Hc6});EJB.exports=Xc6(WJB);var JJB=((A)=>{return A.HEADER="header",A.QUERY="query",A})(JJB||{}),XJB=((A)=>{return A.HEADER="header",A.QUERY="query",A})(XJB||{}),VJB=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(VJB||{}),CJB=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(CJB||{}),Vc6=uk1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),Cc6=uk1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),Kc6=uk1((A)=>{return Vc6(A)},"getDefaultClientConfiguration"),Hc6=uk1((A)=>{return Cc6(A)},"resolveDefaultRuntimeConfig"),KJB=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(KJB||{}),zc6="__smithy_context",HJB=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(HJB||{}),zJB=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(zJB||{})});
var UZ2=E((zZ2)=>{Object.defineProperty(zZ2,"__esModule",{value:!0});zZ2.ruleSet=void 0;var VZ2="required",pz="fn",iz="argv",ks="ref",QZ2=!0,DZ2="isSet",P61="booleanEquals",js="error",ys="endpoint",bT="tree",f50="PartitionResult",h50="getAttr",ZZ2={[VZ2]:!1,type:"String"},GZ2={[VZ2]:!0,default:!1,type:"Boolean"},FZ2={[ks]:"Endpoint"},CZ2={[pz]:P61,[iz]:[{[ks]:"UseFIPS"},!0]},KZ2={[pz]:P61,[iz]:[{[ks]:"UseDualStack"},!0]},lz={},IZ2={[pz]:h50,[iz]:[{[ks]:f50},"supportsFIPS"]},HZ2={[ks]:f50},YZ2={[pz]:P61,[iz]:[!0,{[pz]:h50,[iz]:[HZ2,"supportsDualStack"]}]},WZ2=[CZ2],JZ2=[KZ2],XZ2=[{[ks]:"Region"}],nq4={version:"1.0",parameters:{Region:ZZ2,UseDualStack:GZ2,UseFIPS:GZ2,Endpoint:ZZ2},rules:[{conditions:[{[pz]:DZ2,[iz]:[FZ2]}],rules:[{conditions:WZ2,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:js},{conditions:JZ2,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:js},{endpoint:{url:FZ2,properties:lz,headers:lz},type:ys}],type:bT},{conditions:[{[pz]:DZ2,[iz]:XZ2}],rules:[{conditions:[{[pz]:"aws.partition",[iz]:XZ2,assign:f50}],rules:[{conditions:[CZ2,KZ2],rules:[{conditions:[{[pz]:P61,[iz]:[QZ2,IZ2]},YZ2],rules:[{endpoint:{url:"https://portal.sso-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:lz,headers:lz},type:ys}],type:bT},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:js}],type:bT},{conditions:WZ2,rules:[{conditions:[{[pz]:P61,[iz]:[IZ2,QZ2]}],rules:[{conditions:[{[pz]:"stringEquals",[iz]:[{[pz]:h50,[iz]:[HZ2,"name"]},"aws-us-gov"]}],endpoint:{url:"https://portal.sso.{Region}.amazonaws.com",properties:lz,headers:lz},type:ys},{endpoint:{url:"https://portal.sso-fips.{Region}.{PartitionResult#dnsSuffix}",properties:lz,headers:lz},type:ys}],type:bT},{error:"FIPS is enabled but this partition does not support FIPS",type:js}],type:bT},{conditions:JZ2,rules:[{conditions:[YZ2],rules:[{endpoint:{url:"https://portal.sso.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:lz,headers:lz},type:ys}],type:bT},{error:"DualStack is enabled but this partition does not support DualStack",type:js}],type:bT},{endpoint:{url:"https://portal.sso.{Region}.{PartitionResult#dnsSuffix}",properties:lz,headers:lz},type:ys}],type:bT}],type:bT},{error:"Invalid Configuration: Missing Region",type:js}]};zZ2.ruleSet=nq4});
var V52=E((Sz5,X52)=>{var{defineProperty:vq1,getOwnPropertyDescriptor:Kz4,getOwnPropertyNames:Hz4}=Object,zz4=Object.prototype.hasOwnProperty,bq1=(A,B)=>vq1(A,"name",{value:B,configurable:!0}),Ez4=(A,B)=>{for(var Q in B)vq1(A,Q,{get:B[Q],enumerable:!0})},Uz4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Hz4(B))if(!zz4.call(A,Z)&&Z!==Q)vq1(A,Z,{get:()=>B[Z],enumerable:!(D=Kz4(B,Z))||D.enumerable})}return A},wz4=(A)=>Uz4(vq1({},"__esModule",{value:!0}),A),D52={};Ez4(D52,{AlgorithmId:()=>I52,EndpointURLScheme:()=>F52,FieldPosition:()=>Y52,HttpApiKeyAuthLocation:()=>G52,HttpAuthLocation:()=>Z52,IniSectionType:()=>W52,RequestHandlerProtocol:()=>J52,SMITHY_CONTEXT_KEY:()=>Mz4,getDefaultClientConfiguration:()=>Nz4,resolveDefaultRuntimeConfig:()=>Lz4});X52.exports=wz4(D52);var Z52=((A)=>{return A.HEADER="header",A.QUERY="query",A})(Z52||{}),G52=((A)=>{return A.HEADER="header",A.QUERY="query",A})(G52||{}),F52=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(F52||{}),I52=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(I52||{}),$z4=bq1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),qz4=bq1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),Nz4=bq1((A)=>{return $z4(A)},"getDefaultClientConfiguration"),Lz4=bq1((A)=>{return qz4(A)},"resolveDefaultRuntimeConfig"),Y52=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(Y52||{}),Mz4="__smithy_context",W52=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(W52||{}),J52=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(J52||{})});
var V61=E(($z5,N82)=>{var{defineProperty:Tq1,getOwnPropertyDescriptor:JH4,getOwnPropertyNames:XH4}=Object,VH4=Object.prototype.hasOwnProperty,Oq1=(A,B)=>Tq1(A,"name",{value:B,configurable:!0}),CH4=(A,B)=>{for(var Q in B)Tq1(A,Q,{get:B[Q],enumerable:!0})},KH4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of XH4(B))if(!VH4.call(A,Z)&&Z!==Q)Tq1(A,Z,{get:()=>B[Z],enumerable:!(D=JH4(B,Z))||D.enumerable})}return A},HH4=(A)=>KH4(Tq1({},"__esModule",{value:!0}),A),U82={};CH4(U82,{getHostHeaderPlugin:()=>EH4,hostHeaderMiddleware:()=>$82,hostHeaderMiddlewareOptions:()=>q82,resolveHostHeaderConfig:()=>w82});N82.exports=HH4(U82);var zH4=E82();function w82(A){return A}Oq1(w82,"resolveHostHeaderConfig");var $82=Oq1((A)=>(B)=>async(Q)=>{if(!zH4.HttpRequest.isInstance(Q.request))return B(Q);let{request:D}=Q,{handlerProtocol:Z=""}=A.requestHandler.metadata||{};if(Z.indexOf("h2")>=0&&!D.headers[":authority"])delete D.headers.host,D.headers[":authority"]=D.hostname+(D.port?":"+D.port:"");else if(!D.headers.host){let G=D.hostname;if(D.port!=null)G+=`:${D.port}`;D.headers.host=G}return B(Q)},"hostHeaderMiddleware"),q82={name:"hostHeaderMiddleware",step:"build",priority:"low",tags:["HOST"],override:!0},EH4=Oq1((A)=>({applyToStack:Oq1((B)=>{B.add($82(A),q82)},"applyToStack")}),"getHostHeaderPlugin")});
var VD2=E((FE5,D$4)=>{D$4.exports={name:"@aws-sdk/client-sso",description:"AWS SDK for JavaScript Sso Client for Node.js, Browser and React Native",version:"3.797.0",scripts:{build:"concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'","build:cjs":"node ../../scripts/compilation/inline client-sso","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4",clean:"rimraf ./dist-* && rimraf *.tsbuildinfo","extract:docs":"api-extractor run --local","generate:client":"node ../../scripts/generate-clients/single-service --solo sso"},main:"./dist-cjs/index.js",types:"./dist-types/index.d.ts",module:"./dist-es/index.js",sideEffects:!1,dependencies:{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.796.0","@aws-sdk/middleware-host-header":"3.775.0","@aws-sdk/middleware-logger":"3.775.0","@aws-sdk/middleware-recursion-detection":"3.775.0","@aws-sdk/middleware-user-agent":"3.796.0","@aws-sdk/region-config-resolver":"3.775.0","@aws-sdk/types":"3.775.0","@aws-sdk/util-endpoints":"3.787.0","@aws-sdk/util-user-agent-browser":"3.775.0","@aws-sdk/util-user-agent-node":"3.796.0","@smithy/config-resolver":"^4.1.0","@smithy/core":"^3.2.0","@smithy/fetch-http-handler":"^5.0.2","@smithy/hash-node":"^4.0.2","@smithy/invalid-dependency":"^4.0.2","@smithy/middleware-content-length":"^4.0.2","@smithy/middleware-endpoint":"^4.1.0","@smithy/middleware-retry":"^4.1.0","@smithy/middleware-serde":"^4.0.3","@smithy/middleware-stack":"^4.0.2","@smithy/node-config-provider":"^4.0.2","@smithy/node-http-handler":"^4.0.4","@smithy/protocol-http":"^5.1.0","@smithy/smithy-client":"^4.2.0","@smithy/types":"^4.2.0","@smithy/url-parser":"^4.0.2","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.8","@smithy/util-defaults-mode-node":"^4.0.8","@smithy/util-endpoints":"^3.0.2","@smithy/util-middleware":"^4.0.2","@smithy/util-retry":"^4.0.2","@smithy/util-utf8":"^4.0.0",tslib:"^2.6.2"},devDependencies:{"@tsconfig/node18":"18.2.4","@types/node":"^18.19.69",concurrently:"7.0.0","downlevel-dts":"0.10.1",rimraf:"3.0.2",typescript:"~5.2.2"},engines:{node:">=18.0.0"},typesVersions:{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},files:["dist-*/**"],author:{name:"AWS SDK for JavaScript Team",url:"https://aws.amazon.com/javascript/"},license:"Apache-2.0",browser:{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.browser"},"react-native":{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.native"},homepage:"https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-sso",repository:{type:"git",url:"https://github.com/aws/aws-sdk-js-v3.git",directory:"clients/client-sso"}}});
var VVB=E((JVB)=>{Object.defineProperty(JVB,"__esModule",{value:!0});JVB.uint32ArrayFrom=void 0;function Pl6(A){if(!Uint32Array.from){var B=new Uint32Array(A.length),Q=0;while(Q<A.length)B[Q]=A[Q],Q+=1;return B}return Uint32Array.from(A)}JVB.uint32ArrayFrom=Pl6});
var W70=E((MU5,aN1)=>{var{defineProperty:iW2,getOwnPropertyDescriptor:JS4,getOwnPropertyNames:XS4}=Object,VS4=Object.prototype.hasOwnProperty,I70=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of XS4(B))if(!VS4.call(A,Z)&&Z!==Q)iW2(A,Z,{get:()=>B[Z],enumerable:!(D=JS4(B,Z))||D.enumerable})}return A},nW2=(A,B,Q)=>(I70(A,B,"default"),Q&&I70(Q,B,"default")),CS4=(A)=>I70(iW2({},"__esModule",{value:!0}),A),Y70={};aN1.exports=CS4(Y70);nW2(Y70,pW2(),aN1.exports);nW2(Y70,F70(),aN1.exports)});
var WVB=E((IVB)=>{Object.defineProperty(IVB,"__esModule",{value:!0});IVB.numToUint8=void 0;function Tl6(A){return new Uint8Array([(A&4278190080)>>24,(A&16711680)>>16,(A&65280)>>8,A&255])}IVB.numToUint8=Tl6});
var X82=E((Hz5,J82)=>{var{defineProperty:Lq1,getOwnPropertyDescriptor:mK4,getOwnPropertyNames:dK4}=Object,cK4=Object.prototype.hasOwnProperty,Mq1=(A,B)=>Lq1(A,"name",{value:B,configurable:!0}),lK4=(A,B)=>{for(var Q in B)Lq1(A,Q,{get:B[Q],enumerable:!0})},pK4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of dK4(B))if(!cK4.call(A,Z)&&Z!==Q)Lq1(A,Z,{get:()=>B[Z],enumerable:!(D=mK4(B,Z))||D.enumerable})}return A},iK4=(A)=>pK4(Lq1({},"__esModule",{value:!0}),A),Q82={};lK4(Q82,{AlgorithmId:()=>F82,EndpointURLScheme:()=>G82,FieldPosition:()=>I82,HttpApiKeyAuthLocation:()=>Z82,HttpAuthLocation:()=>D82,IniSectionType:()=>Y82,RequestHandlerProtocol:()=>W82,SMITHY_CONTEXT_KEY:()=>oK4,getDefaultClientConfiguration:()=>sK4,resolveDefaultRuntimeConfig:()=>rK4});J82.exports=iK4(Q82);var D82=((A)=>{return A.HEADER="header",A.QUERY="query",A})(D82||{}),Z82=((A)=>{return A.HEADER="header",A.QUERY="query",A})(Z82||{}),G82=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(G82||{}),F82=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(F82||{}),nK4=Mq1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),aK4=Mq1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),sK4=Mq1((A)=>{return nK4(A)},"getDefaultClientConfiguration"),rK4=Mq1((A)=>{return aK4(A)},"resolveDefaultRuntimeConfig"),I82=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(I82||{}),oK4="__smithy_context",Y82=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(Y82||{}),W82=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(W82||{})});
var XD2=E((GE5,JN1)=>{var f72,h72,g72,u72,m72,d72,c72,l72,p72,i72,n72,a72,s72,YN1,$50,r72,o72,t72,Os,e72,AD2,BD2,QD2,DD2,ZD2,GD2,FD2,ID2,WN1,YD2,WD2,JD2;(function(A){var B=typeof global==="object"?global:typeof self==="object"?self:typeof this==="object"?this:{};if(typeof define==="function"&&define.amd)define("tslib",["exports"],function(D){A(Q(B,Q(D)))});else if(typeof JN1==="object"&&typeof GE5==="object")A(Q(B,Q(GE5)));else A(Q(B));function Q(D,Z){if(D!==B)if(typeof Object.create==="function")Object.defineProperty(D,"__esModule",{value:!0});else D.__esModule=!0;return function(G,F){return D[G]=Z?Z(G,F):F}}})(function(A){var B=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(G,F){G.__proto__=F}||function(G,F){for(var I in F)if(Object.prototype.hasOwnProperty.call(F,I))G[I]=F[I]};f72=function(G,F){if(typeof F!=="function"&&F!==null)throw new TypeError("Class extends value "+String(F)+" is not a constructor or null");B(G,F);function I(){this.constructor=G}G.prototype=F===null?Object.create(F):(I.prototype=F.prototype,new I)},h72=Object.assign||function(G){for(var F,I=1,Y=arguments.length;I<Y;I++){F=arguments[I];for(var W in F)if(Object.prototype.hasOwnProperty.call(F,W))G[W]=F[W]}return G},g72=function(G,F){var I={};for(var Y in G)if(Object.prototype.hasOwnProperty.call(G,Y)&&F.indexOf(Y)<0)I[Y]=G[Y];if(G!=null&&typeof Object.getOwnPropertySymbols==="function"){for(var W=0,Y=Object.getOwnPropertySymbols(G);W<Y.length;W++)if(F.indexOf(Y[W])<0&&Object.prototype.propertyIsEnumerable.call(G,Y[W]))I[Y[W]]=G[Y[W]]}return I},u72=function(G,F,I,Y){var W=arguments.length,J=W<3?F:Y===null?Y=Object.getOwnPropertyDescriptor(F,I):Y,X;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")J=Reflect.decorate(G,F,I,Y);else for(var V=G.length-1;V>=0;V--)if(X=G[V])J=(W<3?X(J):W>3?X(F,I,J):X(F,I))||J;return W>3&&J&&Object.defineProperty(F,I,J),J},m72=function(G,F){return function(I,Y){F(I,Y,G)}},d72=function(G,F,I,Y,W,J){function X(T){if(T!==void 0&&typeof T!=="function")throw new TypeError("Function expected");return T}var V=Y.kind,C=V==="getter"?"get":V==="setter"?"set":"value",K=!F&&G?Y.static?G:G.prototype:null,H=F||(K?Object.getOwnPropertyDescriptor(K,Y.name):{}),z,$=!1;for(var L=I.length-1;L>=0;L--){var N={};for(var O in Y)N[O]=O==="access"?{}:Y[O];for(var O in Y.access)N.access[O]=Y.access[O];N.addInitializer=function(T){if($)throw new TypeError("Cannot add initializers after decoration has completed");J.push(X(T||null))};var R=I[L](V==="accessor"?{get:H.get,set:H.set}:H[C],N);if(V==="accessor"){if(R===void 0)continue;if(R===null||typeof R!=="object")throw new TypeError("Object expected");if(z=X(R.get))H.get=z;if(z=X(R.set))H.set=z;if(z=X(R.init))W.unshift(z)}else if(z=X(R))if(V==="field")W.unshift(z);else H[C]=z}if(K)Object.defineProperty(K,Y.name,H);$=!0},c72=function(G,F,I){var Y=arguments.length>2;for(var W=0;W<F.length;W++)I=Y?F[W].call(G,I):F[W].call(G);return Y?I:void 0},l72=function(G){return typeof G==="symbol"?G:"".concat(G)},p72=function(G,F,I){if(typeof F==="symbol")F=F.description?"[".concat(F.description,"]"):"";return Object.defineProperty(G,"name",{configurable:!0,value:I?"".concat(I," ",F):F})},i72=function(G,F){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(G,F)},n72=function(G,F,I,Y){function W(J){return J instanceof I?J:new I(function(X){X(J)})}return new(I||(I=Promise))(function(J,X){function V(H){try{K(Y.next(H))}catch(z){X(z)}}function C(H){try{K(Y.throw(H))}catch(z){X(z)}}function K(H){H.done?J(H.value):W(H.value).then(V,C)}K((Y=Y.apply(G,F||[])).next())})},a72=function(G,F){var I={label:0,sent:function(){if(J[0]&1)throw J[1];return J[1]},trys:[],ops:[]},Y,W,J,X=Object.create((typeof Iterator==="function"?Iterator:Object).prototype);return X.next=V(0),X.throw=V(1),X.return=V(2),typeof Symbol==="function"&&(X[Symbol.iterator]=function(){return this}),X;function V(K){return function(H){return C([K,H])}}function C(K){if(Y)throw new TypeError("Generator is already executing.");while(X&&(X=0,K[0]&&(I=0)),I)try{if(Y=1,W&&(J=K[0]&2?W.return:K[0]?W.throw||((J=W.return)&&J.call(W),0):W.next)&&!(J=J.call(W,K[1])).done)return J;if(W=0,J)K=[K[0]&2,J.value];switch(K[0]){case 0:case 1:J=K;break;case 4:return I.label++,{value:K[1],done:!1};case 5:I.label++,W=K[1],K=[0];continue;case 7:K=I.ops.pop(),I.trys.pop();continue;default:if((J=I.trys,!(J=J.length>0&&J[J.length-1]))&&(K[0]===6||K[0]===2)){I=0;continue}if(K[0]===3&&(!J||K[1]>J[0]&&K[1]<J[3])){I.label=K[1];break}if(K[0]===6&&I.label<J[1]){I.label=J[1],J=K;break}if(J&&I.label<J[2]){I.label=J[2],I.ops.push(K);break}if(J[2])I.ops.pop();I.trys.pop();continue}K=F.call(G,I)}catch(H){K=[6,H],W=0}finally{Y=J=0}if(K[0]&5)throw K[1];return{value:K[0]?K[1]:void 0,done:!0}}},s72=function(G,F){for(var I in G)if(I!=="default"&&!Object.prototype.hasOwnProperty.call(F,I))WN1(F,G,I)},WN1=Object.create?function(G,F,I,Y){if(Y===void 0)Y=I;var W=Object.getOwnPropertyDescriptor(F,I);if(!W||("get"in W?!F.__esModule:W.writable||W.configurable))W={enumerable:!0,get:function(){return F[I]}};Object.defineProperty(G,Y,W)}:function(G,F,I,Y){if(Y===void 0)Y=I;G[Y]=F[I]},YN1=function(G){var F=typeof Symbol==="function"&&Symbol.iterator,I=F&&G[F],Y=0;if(I)return I.call(G);if(G&&typeof G.length==="number")return{next:function(){if(G&&Y>=G.length)G=void 0;return{value:G&&G[Y++],done:!G}}};throw new TypeError(F?"Object is not iterable.":"Symbol.iterator is not defined.")},$50=function(G,F){var I=typeof Symbol==="function"&&G[Symbol.iterator];if(!I)return G;var Y=I.call(G),W,J=[],X;try{while((F===void 0||F-- >0)&&!(W=Y.next()).done)J.push(W.value)}catch(V){X={error:V}}finally{try{if(W&&!W.done&&(I=Y.return))I.call(Y)}finally{if(X)throw X.error}}return J},r72=function(){for(var G=[],F=0;F<arguments.length;F++)G=G.concat($50(arguments[F]));return G},o72=function(){for(var G=0,F=0,I=arguments.length;F<I;F++)G+=arguments[F].length;for(var Y=Array(G),W=0,F=0;F<I;F++)for(var J=arguments[F],X=0,V=J.length;X<V;X++,W++)Y[W]=J[X];return Y},t72=function(G,F,I){if(I||arguments.length===2){for(var Y=0,W=F.length,J;Y<W;Y++)if(J||!(Y in F)){if(!J)J=Array.prototype.slice.call(F,0,Y);J[Y]=F[Y]}}return G.concat(J||Array.prototype.slice.call(F))},Os=function(G){return this instanceof Os?(this.v=G,this):new Os(G)},e72=function(G,F,I){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var Y=I.apply(G,F||[]),W,J=[];return W=Object.create((typeof AsyncIterator==="function"?AsyncIterator:Object).prototype),V("next"),V("throw"),V("return",X),W[Symbol.asyncIterator]=function(){return this},W;function X(L){return function(N){return Promise.resolve(N).then(L,z)}}function V(L,N){if(Y[L]){if(W[L]=function(O){return new Promise(function(R,T){J.push([L,O,R,T])>1||C(L,O)})},N)W[L]=N(W[L])}}function C(L,N){try{K(Y[L](N))}catch(O){$(J[0][3],O)}}function K(L){L.value instanceof Os?Promise.resolve(L.value.v).then(H,z):$(J[0][2],L)}function H(L){C("next",L)}function z(L){C("throw",L)}function $(L,N){if(L(N),J.shift(),J.length)C(J[0][0],J[0][1])}},AD2=function(G){var F,I;return F={},Y("next"),Y("throw",function(W){throw W}),Y("return"),F[Symbol.iterator]=function(){return this},F;function Y(W,J){F[W]=G[W]?function(X){return(I=!I)?{value:Os(G[W](X)),done:!1}:J?J(X):X}:J}},BD2=function(G){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var F=G[Symbol.asyncIterator],I;return F?F.call(G):(G=typeof YN1==="function"?YN1(G):G[Symbol.iterator](),I={},Y("next"),Y("throw"),Y("return"),I[Symbol.asyncIterator]=function(){return this},I);function Y(J){I[J]=G[J]&&function(X){return new Promise(function(V,C){X=G[J](X),W(V,C,X.done,X.value)})}}function W(J,X,V,C){Promise.resolve(C).then(function(K){J({value:K,done:V})},X)}},QD2=function(G,F){if(Object.defineProperty)Object.defineProperty(G,"raw",{value:F});else G.raw=F;return G};var Q=Object.create?function(G,F){Object.defineProperty(G,"default",{enumerable:!0,value:F})}:function(G,F){G.default=F},D=function(G){return D=Object.getOwnPropertyNames||function(F){var I=[];for(var Y in F)if(Object.prototype.hasOwnProperty.call(F,Y))I[I.length]=Y;return I},D(G)};DD2=function(G){if(G&&G.__esModule)return G;var F={};if(G!=null){for(var I=D(G),Y=0;Y<I.length;Y++)if(I[Y]!=="default")WN1(F,G,I[Y])}return Q(F,G),F},ZD2=function(G){return G&&G.__esModule?G:{default:G}},GD2=function(G,F,I,Y){if(I==="a"&&!Y)throw new TypeError("Private accessor was defined without a getter");if(typeof F==="function"?G!==F||!Y:!F.has(G))throw new TypeError("Cannot read private member from an object whose class did not declare it");return I==="m"?Y:I==="a"?Y.call(G):Y?Y.value:F.get(G)},FD2=function(G,F,I,Y,W){if(Y==="m")throw new TypeError("Private method is not writable");if(Y==="a"&&!W)throw new TypeError("Private accessor was defined without a setter");if(typeof F==="function"?G!==F||!W:!F.has(G))throw new TypeError("Cannot write private member to an object whose class did not declare it");return Y==="a"?W.call(G,I):W?W.value=I:F.set(G,I),I},ID2=function(G,F){if(F===null||typeof F!=="object"&&typeof F!=="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof G==="function"?F===G:G.has(F)},YD2=function(G,F,I){if(F!==null&&F!==void 0){if(typeof F!=="object"&&typeof F!=="function")throw new TypeError("Object expected.");var Y,W;if(I){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");Y=F[Symbol.asyncDispose]}if(Y===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");if(Y=F[Symbol.dispose],I)W=Y}if(typeof Y!=="function")throw new TypeError("Object not disposable.");if(W)Y=function(){try{W.call(this)}catch(J){return Promise.reject(J)}};G.stack.push({value:F,dispose:Y,async:I})}else if(I)G.stack.push({async:!0});return F};var Z=typeof SuppressedError==="function"?SuppressedError:function(G,F,I){var Y=new Error(I);return Y.name="SuppressedError",Y.error=G,Y.suppressed=F,Y};WD2=function(G){function F(J){G.error=G.hasError?new Z(J,G.error,"An error was suppressed during disposal."):J,G.hasError=!0}var I,Y=0;function W(){while(I=G.stack.pop())try{if(!I.async&&Y===1)return Y=0,G.stack.push(I),Promise.resolve().then(W);if(I.dispose){var J=I.dispose.call(I.value);if(I.async)return Y|=2,Promise.resolve(J).then(W,function(X){return F(X),W()})}else Y|=1}catch(X){F(X)}if(Y===1)return G.hasError?Promise.reject(G.error):Promise.resolve();if(G.hasError)throw G.error}return W()},JD2=function(G,F){if(typeof G==="string"&&/^\.\.?\//.test(G))return G.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,function(I,Y,W,J,X){return Y?F?".jsx":".js":W&&(!J||!X)?I:W+J+"."+X.toLowerCase()+"js"});return G},A("__extends",f72),A("__assign",h72),A("__rest",g72),A("__decorate",u72),A("__param",m72),A("__esDecorate",d72),A("__runInitializers",c72),A("__propKey",l72),A("__setFunctionName",p72),A("__metadata",i72),A("__awaiter",n72),A("__generator",a72),A("__exportStar",s72),A("__createBinding",WN1),A("__values",YN1),A("__read",$50),A("__spread",r72),A("__spreadArrays",o72),A("__spreadArray",t72),A("__await",Os),A("__asyncGenerator",e72),A("__asyncDelegator",AD2),A("__asyncValues",BD2),A("__makeTemplateObject",QD2),A("__importStar",DD2),A("__importDefault",ZD2),A("__classPrivateFieldGet",GD2),A("__classPrivateFieldSet",FD2),A("__classPrivateFieldIn",ID2),A("__addDisposableResource",YD2),A("__disposeResources",WD2),A("__rewriteRelativeImportExtension",JD2)})});
var XZ=E((vE5,pG2)=>{var{defineProperty:_N1,getOwnPropertyDescriptor:SL4,getOwnPropertyNames:jL4}=Object,yL4=Object.prototype.hasOwnProperty,k2=(A,B)=>_N1(A,"name",{value:B,configurable:!0}),kL4=(A,B)=>{for(var Q in B)_N1(A,Q,{get:B[Q],enumerable:!0})},_L4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of jL4(B))if(!yL4.call(A,Z)&&Z!==Q)_N1(A,Z,{get:()=>B[Z],enumerable:!(D=SL4(B,Z))||D.enumerable})}return A},xL4=(A)=>_L4(_N1({},"__esModule",{value:!0}),A),SG2={};kL4(SG2,{Client:()=>vL4,Command:()=>yG2,LazyJsonString:()=>Mg,NoOpLogger:()=>yM4,SENSITIVE_STRING:()=>fL4,ServiceException:()=>UM4,_json:()=>s50,collectBody:()=>c50.collectBody,convertMap:()=>kM4,createAggregatedClient:()=>hL4,dateToUtcString:()=>fG2,decorateServiceException:()=>hG2,emitWarningIfUnsupportedVersion:()=>NM4,expectBoolean:()=>uL4,expectByte:()=>a50,expectFloat32:()=>yN1,expectInt:()=>dL4,expectInt32:()=>i50,expectLong:()=>x61,expectNonNull:()=>lL4,expectNumber:()=>_61,expectObject:()=>kG2,expectShort:()=>n50,expectString:()=>pL4,expectUnion:()=>iL4,extendedEncodeURIComponent:()=>c50.extendedEncodeURIComponent,getArrayIfSingleItem:()=>SM4,getDefaultClientConfiguration:()=>TM4,getDefaultExtensionConfiguration:()=>uG2,getValueFromTextNode:()=>mG2,handleFloat:()=>sL4,isSerializableHeaderValue:()=>jM4,limitedParseDouble:()=>t50,limitedParseFloat:()=>rL4,limitedParseFloat32:()=>oL4,loadConfigsForDefaultMode:()=>qM4,logger:()=>v61,map:()=>A30,parseBoolean:()=>gL4,parseEpochTimestamp:()=>WM4,parseRfc3339DateTime:()=>QM4,parseRfc3339DateTimeWithOffset:()=>ZM4,parseRfc7231DateTime:()=>YM4,quoteHeader:()=>cG2,resolveDefaultRuntimeConfig:()=>PM4,resolvedPath:()=>c50.resolvedPath,serializeDateTime:()=>hM4,serializeFloat:()=>fM4,splitEvery:()=>lG2,splitHeader:()=>gM4,strictParseByte:()=>bG2,strictParseDouble:()=>o50,strictParseFloat:()=>nL4,strictParseFloat32:()=>_G2,strictParseInt:()=>tL4,strictParseInt32:()=>eL4,strictParseLong:()=>vG2,strictParseShort:()=>vs,take:()=>_M4,throwDefaultError:()=>gG2,withBaseException:()=>wM4});pG2.exports=xL4(SG2);var jG2=Mw(),vL4=class{constructor(A){this.config=A,this.middlewareStack=jG2.constructStack()}static{k2(this,"Client")}send(A,B,Q){let D=typeof B!=="function"?B:void 0,Z=typeof B==="function"?B:Q,G=D===void 0&&this.config.cacheMiddleware===!0,F;if(G){if(!this.handlers)this.handlers=new WeakMap;let I=this.handlers;if(I.has(A.constructor))F=I.get(A.constructor);else F=A.resolveMiddleware(this.middlewareStack,this.config,D),I.set(A.constructor,F)}else delete this.handlers,F=A.resolveMiddleware(this.middlewareStack,this.config,D);if(Z)F(A).then((I)=>Z(null,I.output),(I)=>Z(I)).catch(()=>{});else return F(A).then((I)=>I.output)}destroy(){this.config?.requestHandler?.destroy?.(),delete this.handlers}},c50=M6(),p50=d50(),yG2=class{constructor(){this.middlewareStack=jG2.constructStack()}static{k2(this,"Command")}static classBuilder(){return new bL4}resolveMiddlewareWithContext(A,B,Q,{middlewareFn:D,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,smithyContext:Y,additionalContext:W,CommandCtor:J}){for(let H of D.bind(this)(J,A,B,Q))this.middlewareStack.use(H);let X=A.concat(this.middlewareStack),{logger:V}=B,C={logger:V,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,[p50.SMITHY_CONTEXT_KEY]:{commandInstance:this,...Y},...W},{requestHandler:K}=B;return X.resolve((H)=>K.handle(H.request,Q||{}),C)}},bL4=class{constructor(){this._init=()=>{},this._ep={},this._middlewareFn=()=>[],this._commandName="",this._clientName="",this._additionalContext={},this._smithyContext={},this._inputFilterSensitiveLog=(A)=>A,this._outputFilterSensitiveLog=(A)=>A,this._serializer=null,this._deserializer=null}static{k2(this,"ClassBuilder")}init(A){this._init=A}ep(A){return this._ep=A,this}m(A){return this._middlewareFn=A,this}s(A,B,Q={}){return this._smithyContext={service:A,operation:B,...Q},this}c(A={}){return this._additionalContext=A,this}n(A,B){return this._clientName=A,this._commandName=B,this}f(A=(Q)=>Q,B=(Q)=>Q){return this._inputFilterSensitiveLog=A,this._outputFilterSensitiveLog=B,this}ser(A){return this._serializer=A,this}de(A){return this._deserializer=A,this}build(){let A=this,B;return B=class extends yG2{constructor(...[Q]){super();this.serialize=A._serializer,this.deserialize=A._deserializer,this.input=Q??{},A._init(this)}static{k2(this,"CommandRef")}static getEndpointParameterInstructions(){return A._ep}resolveMiddleware(Q,D,Z){return this.resolveMiddlewareWithContext(Q,D,Z,{CommandCtor:B,middlewareFn:A._middlewareFn,clientName:A._clientName,commandName:A._commandName,inputFilterSensitiveLog:A._inputFilterSensitiveLog,outputFilterSensitiveLog:A._outputFilterSensitiveLog,smithyContext:A._smithyContext,additionalContext:A._additionalContext})}}}},fL4="***SensitiveInformation***",hL4=k2((A,B)=>{for(let Q of Object.keys(A)){let D=A[Q],Z=k2(async function(F,I,Y){let W=new D(F);if(typeof I==="function")this.send(W,I);else if(typeof Y==="function"){if(typeof I!=="object")throw new Error(`Expected http options but got ${typeof I}`);this.send(W,I||{},Y)}else return this.send(W,I)},"methodImpl"),G=(Q[0].toLowerCase()+Q.slice(1)).replace(/Command$/,"");B.prototype[G]=Z}},"createAggregatedClient"),gL4=k2((A)=>{switch(A){case"true":return!0;case"false":return!1;default:throw new Error(`Unable to parse boolean value "${A}"`)}},"parseBoolean"),uL4=k2((A)=>{if(A===null||A===void 0)return;if(typeof A==="number"){if(A===0||A===1)v61.warn(kN1(`Expected boolean, got ${typeof A}: ${A}`));if(A===0)return!1;if(A===1)return!0}if(typeof A==="string"){let B=A.toLowerCase();if(B==="false"||B==="true")v61.warn(kN1(`Expected boolean, got ${typeof A}: ${A}`));if(B==="false")return!1;if(B==="true")return!0}if(typeof A==="boolean")return A;throw new TypeError(`Expected boolean, got ${typeof A}: ${A}`)},"expectBoolean"),_61=k2((A)=>{if(A===null||A===void 0)return;if(typeof A==="string"){let B=parseFloat(A);if(!Number.isNaN(B)){if(String(B)!==String(A))v61.warn(kN1(`Expected number but observed string: ${A}`));return B}}if(typeof A==="number")return A;throw new TypeError(`Expected number, got ${typeof A}: ${A}`)},"expectNumber"),mL4=Math.ceil(340282346638528860000000000000000000000),yN1=k2((A)=>{let B=_61(A);if(B!==void 0&&!Number.isNaN(B)&&B!==1/0&&B!==-1/0){if(Math.abs(B)>mL4)throw new TypeError(`Expected 32-bit float, got ${A}`)}return B},"expectFloat32"),x61=k2((A)=>{if(A===null||A===void 0)return;if(Number.isInteger(A)&&!Number.isNaN(A))return A;throw new TypeError(`Expected integer, got ${typeof A}: ${A}`)},"expectLong"),dL4=x61,i50=k2((A)=>r50(A,32),"expectInt32"),n50=k2((A)=>r50(A,16),"expectShort"),a50=k2((A)=>r50(A,8),"expectByte"),r50=k2((A,B)=>{let Q=x61(A);if(Q!==void 0&&cL4(Q,B)!==Q)throw new TypeError(`Expected ${B}-bit integer, got ${A}`);return Q},"expectSizedInt"),cL4=k2((A,B)=>{switch(B){case 32:return Int32Array.of(A)[0];case 16:return Int16Array.of(A)[0];case 8:return Int8Array.of(A)[0]}},"castInt"),lL4=k2((A,B)=>{if(A===null||A===void 0){if(B)throw new TypeError(`Expected a non-null value for ${B}`);throw new TypeError("Expected a non-null value")}return A},"expectNonNull"),kG2=k2((A)=>{if(A===null||A===void 0)return;if(typeof A==="object"&&!Array.isArray(A))return A;let B=Array.isArray(A)?"array":typeof A;throw new TypeError(`Expected object, got ${B}: ${A}`)},"expectObject"),pL4=k2((A)=>{if(A===null||A===void 0)return;if(typeof A==="string")return A;if(["boolean","number","bigint"].includes(typeof A))return v61.warn(kN1(`Expected string, got ${typeof A}: ${A}`)),String(A);throw new TypeError(`Expected string, got ${typeof A}: ${A}`)},"expectString"),iL4=k2((A)=>{if(A===null||A===void 0)return;let B=kG2(A),Q=Object.entries(B).filter(([,D])=>D!=null).map(([D])=>D);if(Q.length===0)throw new TypeError("Unions must have exactly one non-null member. None were found.");if(Q.length>1)throw new TypeError(`Unions must have exactly one non-null member. Keys ${Q} were not null.`);return B},"expectUnion"),o50=k2((A)=>{if(typeof A=="string")return _61(hs(A));return _61(A)},"strictParseDouble"),nL4=o50,_G2=k2((A)=>{if(typeof A=="string")return yN1(hs(A));return yN1(A)},"strictParseFloat32"),aL4=/(-?(?:0|[1-9]\d*)(?:\.\d+)?(?:[eE][+-]?\d+)?)|(-?Infinity)|(NaN)/g,hs=k2((A)=>{let B=A.match(aL4);if(B===null||B[0].length!==A.length)throw new TypeError("Expected real number, got implicit NaN");return parseFloat(A)},"parseNumber"),t50=k2((A)=>{if(typeof A=="string")return xG2(A);return _61(A)},"limitedParseDouble"),sL4=t50,rL4=t50,oL4=k2((A)=>{if(typeof A=="string")return xG2(A);return yN1(A)},"limitedParseFloat32"),xG2=k2((A)=>{switch(A){case"NaN":return NaN;case"Infinity":return 1/0;case"-Infinity":return-1/0;default:throw new Error(`Unable to parse float value: ${A}`)}},"parseFloatString"),vG2=k2((A)=>{if(typeof A==="string")return x61(hs(A));return x61(A)},"strictParseLong"),tL4=vG2,eL4=k2((A)=>{if(typeof A==="string")return i50(hs(A));return i50(A)},"strictParseInt32"),vs=k2((A)=>{if(typeof A==="string")return n50(hs(A));return n50(A)},"strictParseShort"),bG2=k2((A)=>{if(typeof A==="string")return a50(hs(A));return a50(A)},"strictParseByte"),kN1=k2((A)=>{return String(new TypeError(A).stack||A).split(`
`).slice(0,5).filter((B)=>!B.includes("stackTraceWarning")).join(`
`)},"stackTraceWarning"),v61={warn:console.warn},AM4=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],e50=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function fG2(A){let B=A.getUTCFullYear(),Q=A.getUTCMonth(),D=A.getUTCDay(),Z=A.getUTCDate(),G=A.getUTCHours(),F=A.getUTCMinutes(),I=A.getUTCSeconds(),Y=Z<10?`0${Z}`:`${Z}`,W=G<10?`0${G}`:`${G}`,J=F<10?`0${F}`:`${F}`,X=I<10?`0${I}`:`${I}`;return`${AM4[D]}, ${Y} ${e50[Q]} ${B} ${W}:${J}:${X} GMT`}k2(fG2,"dateToUtcString");var BM4=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?[zZ]$/),QM4=k2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-3339 date-times must be expressed as strings");let B=BM4.exec(A);if(!B)throw new TypeError("Invalid RFC-3339 date-time value");let[Q,D,Z,G,F,I,Y,W]=B,J=vs(bs(D)),X=NL(Z,"month",1,12),V=NL(G,"day",1,31);return k61(J,X,V,{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W})},"parseRfc3339DateTime"),DM4=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?(([-+]\d{2}\:\d{2})|[zZ])$/),ZM4=k2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-3339 date-times must be expressed as strings");let B=DM4.exec(A);if(!B)throw new TypeError("Invalid RFC-3339 date-time value");let[Q,D,Z,G,F,I,Y,W,J]=B,X=vs(bs(D)),V=NL(Z,"month",1,12),C=NL(G,"day",1,31),K=k61(X,V,C,{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W});if(J.toUpperCase()!="Z")K.setTime(K.getTime()-EM4(J));return K},"parseRfc3339DateTimeWithOffset"),GM4=new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d{2}) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/),FM4=new RegExp(/^(?:Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d{2})-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/),IM4=new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( [1-9]|\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? (\d{4})$/),YM4=k2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-7231 date-times must be expressed as strings");let B=GM4.exec(A);if(B){let[Q,D,Z,G,F,I,Y,W]=B;return k61(vs(bs(G)),l50(Z),NL(D,"day",1,31),{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W})}if(B=FM4.exec(A),B){let[Q,D,Z,G,F,I,Y,W]=B;return VM4(k61(JM4(G),l50(Z),NL(D,"day",1,31),{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W}))}if(B=IM4.exec(A),B){let[Q,D,Z,G,F,I,Y,W]=B;return k61(vs(bs(W)),l50(D),NL(Z.trimLeft(),"day",1,31),{hours:G,minutes:F,seconds:I,fractionalMilliseconds:Y})}throw new TypeError("Invalid RFC-7231 date-time value")},"parseRfc7231DateTime"),WM4=k2((A)=>{if(A===null||A===void 0)return;let B;if(typeof A==="number")B=A;else if(typeof A==="string")B=o50(A);else if(typeof A==="object"&&A.tag===1)B=A.value;else throw new TypeError("Epoch timestamps must be expressed as floating point numbers or their string representation");if(Number.isNaN(B)||B===1/0||B===-1/0)throw new TypeError("Epoch timestamps must be valid, non-Infinite, non-NaN numerics");return new Date(Math.round(B*1000))},"parseEpochTimestamp"),k61=k2((A,B,Q,D)=>{let Z=B-1;return KM4(A,Z,Q),new Date(Date.UTC(A,Z,Q,NL(D.hours,"hour",0,23),NL(D.minutes,"minute",0,59),NL(D.seconds,"seconds",0,60),zM4(D.fractionalMilliseconds)))},"buildDate"),JM4=k2((A)=>{let B=new Date().getUTCFullYear(),Q=Math.floor(B/100)*100+vs(bs(A));if(Q<B)return Q+100;return Q},"parseTwoDigitYear"),XM4=1576800000000,VM4=k2((A)=>{if(A.getTime()-new Date().getTime()>XM4)return new Date(Date.UTC(A.getUTCFullYear()-100,A.getUTCMonth(),A.getUTCDate(),A.getUTCHours(),A.getUTCMinutes(),A.getUTCSeconds(),A.getUTCMilliseconds()));return A},"adjustRfc850Year"),l50=k2((A)=>{let B=e50.indexOf(A);if(B<0)throw new TypeError(`Invalid month: ${A}`);return B+1},"parseMonthByShortName"),CM4=[31,28,31,30,31,30,31,31,30,31,30,31],KM4=k2((A,B,Q)=>{let D=CM4[B];if(B===1&&HM4(A))D=29;if(Q>D)throw new TypeError(`Invalid day for ${e50[B]} in ${A}: ${Q}`)},"validateDayOfMonth"),HM4=k2((A)=>{return A%4===0&&(A%100!==0||A%400===0)},"isLeapYear"),NL=k2((A,B,Q,D)=>{let Z=bG2(bs(A));if(Z<Q||Z>D)throw new TypeError(`${B} must be between ${Q} and ${D}, inclusive`);return Z},"parseDateValue"),zM4=k2((A)=>{if(A===null||A===void 0)return 0;return _G2("0."+A)*1000},"parseMilliseconds"),EM4=k2((A)=>{let B=A[0],Q=1;if(B=="+")Q=1;else if(B=="-")Q=-1;else throw new TypeError(`Offset direction, ${B}, must be "+" or "-"`);let D=Number(A.substring(1,3)),Z=Number(A.substring(4,6));return Q*(D*60+Z)*60*1000},"parseOffsetToMilliseconds"),bs=k2((A)=>{let B=0;while(B<A.length-1&&A.charAt(B)==="0")B++;if(B===0)return A;return A.slice(B)},"stripLeadingZeroes"),UM4=class A extends Error{static{k2(this,"ServiceException")}constructor(B){super(B.message);Object.setPrototypeOf(this,Object.getPrototypeOf(this).constructor.prototype),this.name=B.name,this.$fault=B.$fault,this.$metadata=B.$metadata}static isInstance(B){if(!B)return!1;let Q=B;return A.prototype.isPrototypeOf(Q)||Boolean(Q.$fault)&&Boolean(Q.$metadata)&&(Q.$fault==="client"||Q.$fault==="server")}static[Symbol.hasInstance](B){if(!B)return!1;let Q=B;if(this===A)return A.isInstance(B);if(A.isInstance(B)){if(Q.name&&this.name)return this.prototype.isPrototypeOf(B)||Q.name===this.name;return this.prototype.isPrototypeOf(B)}return!1}},hG2=k2((A,B={})=>{Object.entries(B).filter(([,D])=>D!==void 0).forEach(([D,Z])=>{if(A[D]==null||A[D]==="")A[D]=Z});let Q=A.message||A.Message||"UnknownError";return A.message=Q,delete A.Message,A},"decorateServiceException"),gG2=k2(({output:A,parsedBody:B,exceptionCtor:Q,errorCode:D})=>{let Z=$M4(A),G=Z.httpStatusCode?Z.httpStatusCode+"":void 0,F=new Q({name:B?.code||B?.Code||D||G||"UnknownError",$fault:"client",$metadata:Z});throw hG2(F,B)},"throwDefaultError"),wM4=k2((A)=>{return({output:B,parsedBody:Q,errorCode:D})=>{gG2({output:B,parsedBody:Q,exceptionCtor:A,errorCode:D})}},"withBaseException"),$M4=k2((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),qM4=k2((A)=>{switch(A){case"standard":return{retryMode:"standard",connectionTimeout:3100};case"in-region":return{retryMode:"standard",connectionTimeout:1100};case"cross-region":return{retryMode:"standard",connectionTimeout:3100};case"mobile":return{retryMode:"standard",connectionTimeout:30000};default:return{}}},"loadConfigsForDefaultMode"),PG2=!1,NM4=k2((A)=>{if(A&&!PG2&&parseInt(A.substring(1,A.indexOf(".")))<16)PG2=!0},"emitWarningIfUnsupportedVersion"),LM4=k2((A)=>{let B=[];for(let Q in p50.AlgorithmId){let D=p50.AlgorithmId[Q];if(A[D]===void 0)continue;B.push({algorithmId:()=>D,checksumConstructor:()=>A[D]})}return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),MM4=k2((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),RM4=k2((A)=>{return{setRetryStrategy(B){A.retryStrategy=B},retryStrategy(){return A.retryStrategy}}},"getRetryConfiguration"),OM4=k2((A)=>{let B={};return B.retryStrategy=A.retryStrategy(),B},"resolveRetryRuntimeConfig"),uG2=k2((A)=>{return Object.assign(LM4(A),RM4(A))},"getDefaultExtensionConfiguration"),TM4=uG2,PM4=k2((A)=>{return Object.assign(MM4(A),OM4(A))},"resolveDefaultRuntimeConfig"),SM4=k2((A)=>Array.isArray(A)?A:[A],"getArrayIfSingleItem"),mG2=k2((A)=>{for(let Q in A)if(A.hasOwnProperty(Q)&&A[Q]["#text"]!==void 0)A[Q]=A[Q]["#text"];else if(typeof A[Q]==="object"&&A[Q]!==null)A[Q]=mG2(A[Q]);return A},"getValueFromTextNode"),jM4=k2((A)=>{return A!=null},"isSerializableHeaderValue"),Mg=k2(function A(B){return Object.assign(new String(B),{deserializeJSON(){return JSON.parse(String(B))},toString(){return String(B)},toJSON(){return String(B)}})},"LazyJsonString");Mg.from=(A)=>{if(A&&typeof A==="object"&&(A instanceof Mg||("deserializeJSON"in A)))return A;else if(typeof A==="string"||Object.getPrototypeOf(A)===String.prototype)return Mg(String(A));return Mg(JSON.stringify(A))};Mg.fromObject=Mg.from;var yM4=class{static{k2(this,"NoOpLogger")}trace(){}debug(){}info(){}warn(){}error(){}};function A30(A,B,Q){let D,Z,G;if(typeof B==="undefined"&&typeof Q==="undefined")D={},G=A;else if(D=A,typeof B==="function")return Z=B,G=Q,xM4(D,Z,G);else G=B;for(let F of Object.keys(G)){if(!Array.isArray(G[F])){D[F]=G[F];continue}dG2(D,null,G,F)}return D}k2(A30,"map");var kM4=k2((A)=>{let B={};for(let[Q,D]of Object.entries(A||{}))B[Q]=[,D];return B},"convertMap"),_M4=k2((A,B)=>{let Q={};for(let D in B)dG2(Q,A,B,D);return Q},"take"),xM4=k2((A,B,Q)=>{return A30(A,Object.entries(Q).reduce((D,[Z,G])=>{if(Array.isArray(G))D[Z]=G;else if(typeof G==="function")D[Z]=[B,G()];else D[Z]=[B,G];return D},{}))},"mapWithFilter"),dG2=k2((A,B,Q,D)=>{if(B!==null){let F=Q[D];if(typeof F==="function")F=[,F];let[I=vM4,Y=bM4,W=D]=F;if(typeof I==="function"&&I(B[W])||typeof I!=="function"&&!!I)A[D]=Y(B[W]);return}let[Z,G]=Q[D];if(typeof G==="function"){let F,I=Z===void 0&&(F=G())!=null,Y=typeof Z==="function"&&!!Z(void 0)||typeof Z!=="function"&&!!Z;if(I)A[D]=F;else if(Y)A[D]=G()}else{let F=Z===void 0&&G!=null,I=typeof Z==="function"&&!!Z(G)||typeof Z!=="function"&&!!Z;if(F||I)A[D]=G}},"applyInstruction"),vM4=k2((A)=>A!=null,"nonNullish"),bM4=k2((A)=>A,"pass");function cG2(A){if(A.includes(",")||A.includes('"'))A=`"${A.replace(/"/g,"\\\"")}"`;return A}k2(cG2,"quoteHeader");var fM4=k2((A)=>{if(A!==A)return"NaN";switch(A){case 1/0:return"Infinity";case-1/0:return"-Infinity";default:return A}},"serializeFloat"),hM4=k2((A)=>A.toISOString().replace(".000Z","Z"),"serializeDateTime"),s50=k2((A)=>{if(A==null)return{};if(Array.isArray(A))return A.filter((B)=>B!=null).map(s50);if(typeof A==="object"){let B={};for(let Q of Object.keys(A)){if(A[Q]==null)continue;B[Q]=s50(A[Q])}return B}return A},"_json");function lG2(A,B,Q){if(Q<=0||!Number.isInteger(Q))throw new Error("Invalid number of delimiters ("+Q+") for splitEvery.");let D=A.split(B);if(Q===1)return D;let Z=[],G="";for(let F=0;F<D.length;F++){if(G==="")G=D[F];else G+=B+D[F];if((F+1)%Q===0)Z.push(G),G=""}if(G!=="")Z.push(G);return Z}k2(lG2,"splitEvery");var gM4=k2((A)=>{let B=A.length,Q=[],D=!1,Z=void 0,G=0;for(let F=0;F<B;++F){let I=A[F];switch(I){case'"':if(Z!=="\\")D=!D;break;case",":if(!D)Q.push(A.slice(G,F)),G=F+1;break;default:}Z=I}return Q.push(A.slice(G)),Q.map((F)=>{F=F.trim();let I=F.length;if(I<2)return F;if(F[0]==='"'&&F[I-1]==='"')F=F.slice(1,I-1);return F.replace(/\\"/g,'"')})},"splitHeader")});
var Y30=E((iE5,fN1)=>{var{defineProperty:PF2,getOwnPropertyDescriptor:BR4,getOwnPropertyNames:QR4}=Object,DR4=Object.prototype.hasOwnProperty,F30=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of QR4(B))if(!DR4.call(A,Z)&&Z!==Q)PF2(A,Z,{get:()=>B[Z],enumerable:!(D=BR4(B,Z))||D.enumerable})}return A},SF2=(A,B,Q)=>(F30(A,B,"default"),Q&&F30(Q,B,"default")),ZR4=(A)=>F30(PF2({},"__esModule",{value:!0}),A),I30={};fN1.exports=ZR4(I30);SF2(I30,MF2(),fN1.exports);SF2(I30,TF2(),fN1.exports)});
var Z30=E((dE5,bN1)=>{var aG2,sG2,rG2,oG2,tG2,eG2,AF2,BF2,QF2,DF2,ZF2,GF2,FF2,xN1,D30,IF2,YF2,WF2,gs,JF2,XF2,VF2,CF2,KF2,HF2,zF2,EF2,UF2,vN1,wF2,$F2,qF2;(function(A){var B=typeof global==="object"?global:typeof self==="object"?self:typeof this==="object"?this:{};if(typeof define==="function"&&define.amd)define("tslib",["exports"],function(D){A(Q(B,Q(D)))});else if(typeof bN1==="object"&&typeof dE5==="object")A(Q(B,Q(dE5)));else A(Q(B));function Q(D,Z){if(D!==B)if(typeof Object.create==="function")Object.defineProperty(D,"__esModule",{value:!0});else D.__esModule=!0;return function(G,F){return D[G]=Z?Z(G,F):F}}})(function(A){var B=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(G,F){G.__proto__=F}||function(G,F){for(var I in F)if(Object.prototype.hasOwnProperty.call(F,I))G[I]=F[I]};aG2=function(G,F){if(typeof F!=="function"&&F!==null)throw new TypeError("Class extends value "+String(F)+" is not a constructor or null");B(G,F);function I(){this.constructor=G}G.prototype=F===null?Object.create(F):(I.prototype=F.prototype,new I)},sG2=Object.assign||function(G){for(var F,I=1,Y=arguments.length;I<Y;I++){F=arguments[I];for(var W in F)if(Object.prototype.hasOwnProperty.call(F,W))G[W]=F[W]}return G},rG2=function(G,F){var I={};for(var Y in G)if(Object.prototype.hasOwnProperty.call(G,Y)&&F.indexOf(Y)<0)I[Y]=G[Y];if(G!=null&&typeof Object.getOwnPropertySymbols==="function"){for(var W=0,Y=Object.getOwnPropertySymbols(G);W<Y.length;W++)if(F.indexOf(Y[W])<0&&Object.prototype.propertyIsEnumerable.call(G,Y[W]))I[Y[W]]=G[Y[W]]}return I},oG2=function(G,F,I,Y){var W=arguments.length,J=W<3?F:Y===null?Y=Object.getOwnPropertyDescriptor(F,I):Y,X;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")J=Reflect.decorate(G,F,I,Y);else for(var V=G.length-1;V>=0;V--)if(X=G[V])J=(W<3?X(J):W>3?X(F,I,J):X(F,I))||J;return W>3&&J&&Object.defineProperty(F,I,J),J},tG2=function(G,F){return function(I,Y){F(I,Y,G)}},eG2=function(G,F,I,Y,W,J){function X(T){if(T!==void 0&&typeof T!=="function")throw new TypeError("Function expected");return T}var V=Y.kind,C=V==="getter"?"get":V==="setter"?"set":"value",K=!F&&G?Y.static?G:G.prototype:null,H=F||(K?Object.getOwnPropertyDescriptor(K,Y.name):{}),z,$=!1;for(var L=I.length-1;L>=0;L--){var N={};for(var O in Y)N[O]=O==="access"?{}:Y[O];for(var O in Y.access)N.access[O]=Y.access[O];N.addInitializer=function(T){if($)throw new TypeError("Cannot add initializers after decoration has completed");J.push(X(T||null))};var R=I[L](V==="accessor"?{get:H.get,set:H.set}:H[C],N);if(V==="accessor"){if(R===void 0)continue;if(R===null||typeof R!=="object")throw new TypeError("Object expected");if(z=X(R.get))H.get=z;if(z=X(R.set))H.set=z;if(z=X(R.init))W.unshift(z)}else if(z=X(R))if(V==="field")W.unshift(z);else H[C]=z}if(K)Object.defineProperty(K,Y.name,H);$=!0},AF2=function(G,F,I){var Y=arguments.length>2;for(var W=0;W<F.length;W++)I=Y?F[W].call(G,I):F[W].call(G);return Y?I:void 0},BF2=function(G){return typeof G==="symbol"?G:"".concat(G)},QF2=function(G,F,I){if(typeof F==="symbol")F=F.description?"[".concat(F.description,"]"):"";return Object.defineProperty(G,"name",{configurable:!0,value:I?"".concat(I," ",F):F})},DF2=function(G,F){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(G,F)},ZF2=function(G,F,I,Y){function W(J){return J instanceof I?J:new I(function(X){X(J)})}return new(I||(I=Promise))(function(J,X){function V(H){try{K(Y.next(H))}catch(z){X(z)}}function C(H){try{K(Y.throw(H))}catch(z){X(z)}}function K(H){H.done?J(H.value):W(H.value).then(V,C)}K((Y=Y.apply(G,F||[])).next())})},GF2=function(G,F){var I={label:0,sent:function(){if(J[0]&1)throw J[1];return J[1]},trys:[],ops:[]},Y,W,J,X=Object.create((typeof Iterator==="function"?Iterator:Object).prototype);return X.next=V(0),X.throw=V(1),X.return=V(2),typeof Symbol==="function"&&(X[Symbol.iterator]=function(){return this}),X;function V(K){return function(H){return C([K,H])}}function C(K){if(Y)throw new TypeError("Generator is already executing.");while(X&&(X=0,K[0]&&(I=0)),I)try{if(Y=1,W&&(J=K[0]&2?W.return:K[0]?W.throw||((J=W.return)&&J.call(W),0):W.next)&&!(J=J.call(W,K[1])).done)return J;if(W=0,J)K=[K[0]&2,J.value];switch(K[0]){case 0:case 1:J=K;break;case 4:return I.label++,{value:K[1],done:!1};case 5:I.label++,W=K[1],K=[0];continue;case 7:K=I.ops.pop(),I.trys.pop();continue;default:if((J=I.trys,!(J=J.length>0&&J[J.length-1]))&&(K[0]===6||K[0]===2)){I=0;continue}if(K[0]===3&&(!J||K[1]>J[0]&&K[1]<J[3])){I.label=K[1];break}if(K[0]===6&&I.label<J[1]){I.label=J[1],J=K;break}if(J&&I.label<J[2]){I.label=J[2],I.ops.push(K);break}if(J[2])I.ops.pop();I.trys.pop();continue}K=F.call(G,I)}catch(H){K=[6,H],W=0}finally{Y=J=0}if(K[0]&5)throw K[1];return{value:K[0]?K[1]:void 0,done:!0}}},FF2=function(G,F){for(var I in G)if(I!=="default"&&!Object.prototype.hasOwnProperty.call(F,I))vN1(F,G,I)},vN1=Object.create?function(G,F,I,Y){if(Y===void 0)Y=I;var W=Object.getOwnPropertyDescriptor(F,I);if(!W||("get"in W?!F.__esModule:W.writable||W.configurable))W={enumerable:!0,get:function(){return F[I]}};Object.defineProperty(G,Y,W)}:function(G,F,I,Y){if(Y===void 0)Y=I;G[Y]=F[I]},xN1=function(G){var F=typeof Symbol==="function"&&Symbol.iterator,I=F&&G[F],Y=0;if(I)return I.call(G);if(G&&typeof G.length==="number")return{next:function(){if(G&&Y>=G.length)G=void 0;return{value:G&&G[Y++],done:!G}}};throw new TypeError(F?"Object is not iterable.":"Symbol.iterator is not defined.")},D30=function(G,F){var I=typeof Symbol==="function"&&G[Symbol.iterator];if(!I)return G;var Y=I.call(G),W,J=[],X;try{while((F===void 0||F-- >0)&&!(W=Y.next()).done)J.push(W.value)}catch(V){X={error:V}}finally{try{if(W&&!W.done&&(I=Y.return))I.call(Y)}finally{if(X)throw X.error}}return J},IF2=function(){for(var G=[],F=0;F<arguments.length;F++)G=G.concat(D30(arguments[F]));return G},YF2=function(){for(var G=0,F=0,I=arguments.length;F<I;F++)G+=arguments[F].length;for(var Y=Array(G),W=0,F=0;F<I;F++)for(var J=arguments[F],X=0,V=J.length;X<V;X++,W++)Y[W]=J[X];return Y},WF2=function(G,F,I){if(I||arguments.length===2){for(var Y=0,W=F.length,J;Y<W;Y++)if(J||!(Y in F)){if(!J)J=Array.prototype.slice.call(F,0,Y);J[Y]=F[Y]}}return G.concat(J||Array.prototype.slice.call(F))},gs=function(G){return this instanceof gs?(this.v=G,this):new gs(G)},JF2=function(G,F,I){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var Y=I.apply(G,F||[]),W,J=[];return W=Object.create((typeof AsyncIterator==="function"?AsyncIterator:Object).prototype),V("next"),V("throw"),V("return",X),W[Symbol.asyncIterator]=function(){return this},W;function X(L){return function(N){return Promise.resolve(N).then(L,z)}}function V(L,N){if(Y[L]){if(W[L]=function(O){return new Promise(function(R,T){J.push([L,O,R,T])>1||C(L,O)})},N)W[L]=N(W[L])}}function C(L,N){try{K(Y[L](N))}catch(O){$(J[0][3],O)}}function K(L){L.value instanceof gs?Promise.resolve(L.value.v).then(H,z):$(J[0][2],L)}function H(L){C("next",L)}function z(L){C("throw",L)}function $(L,N){if(L(N),J.shift(),J.length)C(J[0][0],J[0][1])}},XF2=function(G){var F,I;return F={},Y("next"),Y("throw",function(W){throw W}),Y("return"),F[Symbol.iterator]=function(){return this},F;function Y(W,J){F[W]=G[W]?function(X){return(I=!I)?{value:gs(G[W](X)),done:!1}:J?J(X):X}:J}},VF2=function(G){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var F=G[Symbol.asyncIterator],I;return F?F.call(G):(G=typeof xN1==="function"?xN1(G):G[Symbol.iterator](),I={},Y("next"),Y("throw"),Y("return"),I[Symbol.asyncIterator]=function(){return this},I);function Y(J){I[J]=G[J]&&function(X){return new Promise(function(V,C){X=G[J](X),W(V,C,X.done,X.value)})}}function W(J,X,V,C){Promise.resolve(C).then(function(K){J({value:K,done:V})},X)}},CF2=function(G,F){if(Object.defineProperty)Object.defineProperty(G,"raw",{value:F});else G.raw=F;return G};var Q=Object.create?function(G,F){Object.defineProperty(G,"default",{enumerable:!0,value:F})}:function(G,F){G.default=F},D=function(G){return D=Object.getOwnPropertyNames||function(F){var I=[];for(var Y in F)if(Object.prototype.hasOwnProperty.call(F,Y))I[I.length]=Y;return I},D(G)};KF2=function(G){if(G&&G.__esModule)return G;var F={};if(G!=null){for(var I=D(G),Y=0;Y<I.length;Y++)if(I[Y]!=="default")vN1(F,G,I[Y])}return Q(F,G),F},HF2=function(G){return G&&G.__esModule?G:{default:G}},zF2=function(G,F,I,Y){if(I==="a"&&!Y)throw new TypeError("Private accessor was defined without a getter");if(typeof F==="function"?G!==F||!Y:!F.has(G))throw new TypeError("Cannot read private member from an object whose class did not declare it");return I==="m"?Y:I==="a"?Y.call(G):Y?Y.value:F.get(G)},EF2=function(G,F,I,Y,W){if(Y==="m")throw new TypeError("Private method is not writable");if(Y==="a"&&!W)throw new TypeError("Private accessor was defined without a setter");if(typeof F==="function"?G!==F||!W:!F.has(G))throw new TypeError("Cannot write private member to an object whose class did not declare it");return Y==="a"?W.call(G,I):W?W.value=I:F.set(G,I),I},UF2=function(G,F){if(F===null||typeof F!=="object"&&typeof F!=="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof G==="function"?F===G:G.has(F)},wF2=function(G,F,I){if(F!==null&&F!==void 0){if(typeof F!=="object"&&typeof F!=="function")throw new TypeError("Object expected.");var Y,W;if(I){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");Y=F[Symbol.asyncDispose]}if(Y===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");if(Y=F[Symbol.dispose],I)W=Y}if(typeof Y!=="function")throw new TypeError("Object not disposable.");if(W)Y=function(){try{W.call(this)}catch(J){return Promise.reject(J)}};G.stack.push({value:F,dispose:Y,async:I})}else if(I)G.stack.push({async:!0});return F};var Z=typeof SuppressedError==="function"?SuppressedError:function(G,F,I){var Y=new Error(I);return Y.name="SuppressedError",Y.error=G,Y.suppressed=F,Y};$F2=function(G){function F(J){G.error=G.hasError?new Z(J,G.error,"An error was suppressed during disposal."):J,G.hasError=!0}var I,Y=0;function W(){while(I=G.stack.pop())try{if(!I.async&&Y===1)return Y=0,G.stack.push(I),Promise.resolve().then(W);if(I.dispose){var J=I.dispose.call(I.value);if(I.async)return Y|=2,Promise.resolve(J).then(W,function(X){return F(X),W()})}else Y|=1}catch(X){F(X)}if(Y===1)return G.hasError?Promise.reject(G.error):Promise.resolve();if(G.hasError)throw G.error}return W()},qF2=function(G,F){if(typeof G==="string"&&/^\.\.?\//.test(G))return G.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,function(I,Y,W,J,X){return Y?F?".jsx":".js":W&&(!J||!X)?I:W+J+"."+X.toLowerCase()+"js"});return G},A("__extends",aG2),A("__assign",sG2),A("__rest",rG2),A("__decorate",oG2),A("__param",tG2),A("__esDecorate",eG2),A("__runInitializers",AF2),A("__propKey",BF2),A("__setFunctionName",QF2),A("__metadata",DF2),A("__awaiter",ZF2),A("__generator",GF2),A("__exportStar",FF2),A("__createBinding",vN1),A("__values",xN1),A("__read",D30),A("__spread",IF2),A("__spreadArrays",YF2),A("__spreadArray",WF2),A("__await",gs),A("__asyncGenerator",JF2),A("__asyncDelegator",XF2),A("__asyncValues",VF2),A("__makeTemplateObject",CF2),A("__importStar",KF2),A("__importDefault",HF2),A("__classPrivateFieldGet",zF2),A("__classPrivateFieldSet",EF2),A("__classPrivateFieldIn",UF2),A("__addDisposableResource",wF2),A("__disposeResources",$F2),A("__rewriteRelativeImportExtension",qF2)})});
var _JB=E((e03,kJB)=>{var{defineProperty:dk1,getOwnPropertyDescriptor:Sc6,getOwnPropertyNames:jc6}=Object,yc6=Object.prototype.hasOwnProperty,aD1=(A,B)=>dk1(A,"name",{value:B,configurable:!0}),kc6=(A,B)=>{for(var Q in B)dk1(A,Q,{get:B[Q],enumerable:!0})},_c6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of jc6(B))if(!yc6.call(A,Z)&&Z!==Q)dk1(A,Z,{get:()=>B[Z],enumerable:!(D=Sc6(B,Z))||D.enumerable})}return A},xc6=(A)=>_c6(dk1({},"__esModule",{value:!0}),A),RJB={};kc6(RJB,{eventStreamHandlingMiddleware:()=>PJB,eventStreamHandlingMiddlewareOptions:()=>SJB,eventStreamHeaderMiddleware:()=>jJB,eventStreamHeaderMiddlewareOptions:()=>yJB,getEventStreamPlugin:()=>vc6,resolveEventStreamConfig:()=>OJB});kJB.exports=xc6(RJB);function OJB(A){let{signer:B,signer:Q}=A,D=Object.assign(A,{eventSigner:B,messageSigner:Q}),Z=D.eventStreamPayloadHandlerProvider(D);return Object.assign(D,{eventStreamPayloadHandler:Z})}aD1(OJB,"resolveEventStreamConfig");var TJB=MJB(),PJB=aD1((A)=>(B,Q)=>async(D)=>{let{request:Z}=D;if(!TJB.HttpRequest.isInstance(Z))return B(D);return A.eventStreamPayloadHandler.handle(B,D,Q)},"eventStreamHandlingMiddleware"),SJB={tags:["EVENT_STREAM","SIGNATURE","HANDLE"],name:"eventStreamHandlingMiddleware",relation:"after",toMiddleware:"awsAuthMiddleware",override:!0},jJB=aD1((A)=>async(B)=>{let{request:Q}=B;if(!TJB.HttpRequest.isInstance(Q))return A(B);return Q.headers={...Q.headers,"content-type":"application/vnd.amazon.eventstream","x-amz-content-sha256":"STREAMING-AWS4-HMAC-SHA256-EVENTS"},A({...B,request:Q})},"eventStreamHeaderMiddleware"),yJB={step:"build",tags:["EVENT_STREAM","HEADER","CONTENT_TYPE","CONTENT_SHA256"],name:"eventStreamHeaderMiddleware",override:!0},vc6=aD1((A)=>({applyToStack:aD1((B)=>{B.addRelativeTo(PJB(A),SJB),B.add(jJB,yJB)},"applyToStack")}),"getEventStreamPlugin")});
var _Y2=E((yY2)=>{Object.defineProperty(yY2,"__esModule",{value:!0});yY2.defaultEndpointResolver=void 0;var FT4=ws(),L30=S7(),IT4=jY2(),YT4=new L30.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS","UseGlobalEndpoint"]}),WT4=(A,B={})=>{return YT4.get(A,()=>L30.resolveEndpoint(IT4.ruleSet,{endpointParams:A,logger:B.logger}))};yY2.defaultEndpointResolver=WT4;L30.customEndpointFunctions.aws=FT4.awsEndpointFunctions});
var aY2=E((iY2)=>{Object.defineProperty(iY2,"__esModule",{value:!0});iY2.resolveHttpAuthRuntimeConfig=iY2.getHttpAuthExtensionConfiguration=void 0;var PT4=(A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}};iY2.getHttpAuthExtensionConfiguration=PT4;var ST4=(A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}};iY2.resolveHttpAuthRuntimeConfig=ST4});
var az0=E((Te)=>{Object.defineProperty(Te,"__esModule",{value:!0});Te.uint32ArrayFrom=Te.numToUint8=Te.isEmptyData=Te.convertToBuffer=void 0;var Sl6=DVB();Object.defineProperty(Te,"convertToBuffer",{enumerable:!0,get:function(){return Sl6.convertToBuffer}});var jl6=FVB();Object.defineProperty(Te,"isEmptyData",{enumerable:!0,get:function(){return jl6.isEmptyData}});var yl6=WVB();Object.defineProperty(Te,"numToUint8",{enumerable:!0,get:function(){return yl6.numToUint8}});var kl6=VVB();Object.defineProperty(Te,"uint32ArrayFrom",{enumerable:!0,get:function(){return kl6.uint32ArrayFrom}})});
var b82=E((Nz5,v82)=>{var{defineProperty:Sq1,getOwnPropertyDescriptor:RH4,getOwnPropertyNames:OH4}=Object,TH4=Object.prototype.hasOwnProperty,jq1=(A,B)=>Sq1(A,"name",{value:B,configurable:!0}),PH4=(A,B)=>{for(var Q in B)Sq1(A,Q,{get:B[Q],enumerable:!0})},SH4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of OH4(B))if(!TH4.call(A,Z)&&Z!==Q)Sq1(A,Z,{get:()=>B[Z],enumerable:!(D=RH4(B,Z))||D.enumerable})}return A},jH4=(A)=>SH4(Sq1({},"__esModule",{value:!0}),A),T82={};PH4(T82,{AlgorithmId:()=>y82,EndpointURLScheme:()=>j82,FieldPosition:()=>k82,HttpApiKeyAuthLocation:()=>S82,HttpAuthLocation:()=>P82,IniSectionType:()=>_82,RequestHandlerProtocol:()=>x82,SMITHY_CONTEXT_KEY:()=>vH4,getDefaultClientConfiguration:()=>_H4,resolveDefaultRuntimeConfig:()=>xH4});v82.exports=jH4(T82);var P82=((A)=>{return A.HEADER="header",A.QUERY="query",A})(P82||{}),S82=((A)=>{return A.HEADER="header",A.QUERY="query",A})(S82||{}),j82=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(j82||{}),y82=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(y82||{}),yH4=jq1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),kH4=jq1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),_H4=jq1((A)=>{return yH4(A)},"getDefaultClientConfiguration"),xH4=jq1((A)=>{return kH4(A)},"resolveDefaultRuntimeConfig"),k82=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(k82||{}),vH4="__smithy_context",_82=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(_82||{}),x82=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(x82||{})});
var bJB=E((AA3,vJB)=>{var{defineProperty:ck1,getOwnPropertyDescriptor:bc6,getOwnPropertyNames:fc6}=Object,hc6=Object.prototype.hasOwnProperty,gc6=(A,B)=>ck1(A,"name",{value:B,configurable:!0}),uc6=(A,B)=>{for(var Q in B)ck1(A,Q,{get:B[Q],enumerable:!0})},mc6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of fc6(B))if(!hc6.call(A,Z)&&Z!==Q)ck1(A,Z,{get:()=>B[Z],enumerable:!(D=bc6(B,Z))||D.enumerable})}return A},dc6=(A)=>mc6(ck1({},"__esModule",{value:!0}),A),xJB={};uc6(xJB,{resolveEventStreamSerdeConfig:()=>cc6});vJB.exports=dc6(xJB);var cc6=gc6((A)=>Object.assign(A,{eventStreamMarshaller:A.eventStreamSerdeProvider(A)}),"resolveEventStreamSerdeConfig")});
var c80=E((vz5,Q32)=>{var{defineProperty:mq1,getOwnPropertyDescriptor:fz4,getOwnPropertyNames:hz4}=Object,gz4=Object.prototype.hasOwnProperty,dq1=(A,B)=>mq1(A,"name",{value:B,configurable:!0}),uz4=(A,B)=>{for(var Q in B)mq1(A,Q,{get:B[Q],enumerable:!0})},mz4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of hz4(B))if(!gz4.call(A,Z)&&Z!==Q)mq1(A,Z,{get:()=>B[Z],enumerable:!(D=fz4(B,Z))||D.enumerable})}return A},dz4=(A)=>mz4(mq1({},"__esModule",{value:!0}),A),a52={};uz4(a52,{AlgorithmId:()=>t52,EndpointURLScheme:()=>o52,FieldPosition:()=>e52,HttpApiKeyAuthLocation:()=>r52,HttpAuthLocation:()=>s52,IniSectionType:()=>A32,RequestHandlerProtocol:()=>B32,SMITHY_CONTEXT_KEY:()=>nz4,getDefaultClientConfiguration:()=>pz4,resolveDefaultRuntimeConfig:()=>iz4});Q32.exports=dz4(a52);var s52=((A)=>{return A.HEADER="header",A.QUERY="query",A})(s52||{}),r52=((A)=>{return A.HEADER="header",A.QUERY="query",A})(r52||{}),o52=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(o52||{}),t52=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(t52||{}),cz4=dq1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),lz4=dq1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),pz4=dq1((A)=>{return cz4(A)},"getDefaultClientConfiguration"),iz4=dq1((A)=>{return lz4(A)},"resolveDefaultRuntimeConfig"),e52=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(e52||{}),nz4="__smithy_context",A32=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(A32||{}),B32=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(B32||{})});
var d32=E((dz5,m32)=>{var{defineProperty:tq1,getOwnPropertyDescriptor:NE4,getOwnPropertyNames:LE4}=Object,ME4=Object.prototype.hasOwnProperty,pY=(A,B)=>tq1(A,"name",{value:B,configurable:!0}),RE4=(A,B)=>{for(var Q in B)tq1(A,Q,{get:B[Q],enumerable:!0})},OE4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of LE4(B))if(!ME4.call(A,Z)&&Z!==Q)tq1(A,Z,{get:()=>B[Z],enumerable:!(D=NE4(B,Z))||D.enumerable})}return A},TE4=(A)=>OE4(tq1({},"__esModule",{value:!0}),A),w32={};RE4(w32,{ALGORITHM_IDENTIFIER:()=>iq1,ALGORITHM_IDENTIFIER_V4A:()=>yE4,ALGORITHM_QUERY_PARAM:()=>$32,ALWAYS_UNSIGNABLE_HEADERS:()=>P32,AMZ_DATE_HEADER:()=>t80,AMZ_DATE_QUERY_PARAM:()=>a80,AUTH_HEADER:()=>o80,CREDENTIAL_QUERY_PARAM:()=>q32,DATE_HEADER:()=>M32,EVENT_ALGORITHM_IDENTIFIER:()=>y32,EXPIRES_QUERY_PARAM:()=>L32,GENERATED_HEADERS:()=>R32,HOST_HEADER:()=>SE4,KEY_TYPE_IDENTIFIER:()=>e80,MAX_CACHE_SIZE:()=>_32,MAX_PRESIGNED_TTL:()=>x32,PROXY_HEADER_PATTERN:()=>S32,REGION_SET_PARAM:()=>PE4,SEC_HEADER_PATTERN:()=>j32,SHA256_HEADER:()=>oq1,SIGNATURE_HEADER:()=>O32,SIGNATURE_QUERY_PARAM:()=>s80,SIGNED_HEADERS_QUERY_PARAM:()=>N32,SignatureV4:()=>dE4,SignatureV4Base:()=>u32,TOKEN_HEADER:()=>T32,TOKEN_QUERY_PARAM:()=>r80,UNSIGNABLE_PATTERNS:()=>jE4,UNSIGNED_PAYLOAD:()=>k32,clearCredentialCache:()=>_E4,createScope:()=>aq1,getCanonicalHeaders:()=>p80,getCanonicalQuery:()=>g32,getPayloadHash:()=>sq1,getSigningKey:()=>v32,hasHeader:()=>b32,moveHeadersToQuery:()=>h32,prepareRequest:()=>n80,signatureV4aContainer:()=>cE4});m32.exports=TE4(w32);var H32=cB(),$32="X-Amz-Algorithm",q32="X-Amz-Credential",a80="X-Amz-Date",N32="X-Amz-SignedHeaders",L32="X-Amz-Expires",s80="X-Amz-Signature",r80="X-Amz-Security-Token",PE4="X-Amz-Region-Set",o80="authorization",t80=a80.toLowerCase(),M32="date",R32=[o80,t80,M32],O32=s80.toLowerCase(),oq1="x-amz-content-sha256",T32=r80.toLowerCase(),SE4="host",P32={authorization:!0,"cache-control":!0,connection:!0,expect:!0,from:!0,"keep-alive":!0,"max-forwards":!0,pragma:!0,referer:!0,te:!0,trailer:!0,"transfer-encoding":!0,upgrade:!0,"user-agent":!0,"x-amzn-trace-id":!0},S32=/^proxy-/,j32=/^sec-/,jE4=[/^proxy-/i,/^sec-/i],iq1="AWS4-HMAC-SHA256",yE4="AWS4-ECDSA-P256-SHA256",y32="AWS4-HMAC-SHA256-PAYLOAD",k32="UNSIGNED-PAYLOAD",_32=50,e80="aws4_request",x32=604800,lk=ay(),kE4=cB(),qs={},nq1=[],aq1=pY((A,B,Q)=>`${A}/${B}/${Q}/${e80}`,"createScope"),v32=pY(async(A,B,Q,D,Z)=>{let G=await z32(A,B.secretAccessKey,B.accessKeyId),F=`${Q}:${D}:${Z}:${lk.toHex(G)}:${B.sessionToken}`;if(F in qs)return qs[F];nq1.push(F);while(nq1.length>_32)delete qs[nq1.shift()];let I=`AWS4${B.secretAccessKey}`;for(let Y of[Q,D,Z,e80])I=await z32(A,I,Y);return qs[F]=I},"getSigningKey"),_E4=pY(()=>{nq1.length=0,Object.keys(qs).forEach((A)=>{delete qs[A]})},"clearCredentialCache"),z32=pY((A,B,Q)=>{let D=new A(B);return D.update(kE4.toUint8Array(Q)),D.digest()},"hmac"),p80=pY(({headers:A},B,Q)=>{let D={};for(let Z of Object.keys(A).sort()){if(A[Z]==null)continue;let G=Z.toLowerCase();if(G in P32||B?.has(G)||S32.test(G)||j32.test(G)){if(!Q||Q&&!Q.has(G))continue}D[G]=A[Z].trim().replace(/\s+/g," ")}return D},"getCanonicalHeaders"),xE4=J32(),vE4=cB(),sq1=pY(async({headers:A,body:B},Q)=>{for(let D of Object.keys(A))if(D.toLowerCase()===oq1)return A[D];if(B==null)return"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855";else if(typeof B==="string"||ArrayBuffer.isView(B)||xE4.isArrayBuffer(B)){let D=new Q;return D.update(vE4.toUint8Array(B)),lk.toHex(await D.digest())}return k32},"getPayloadHash"),E32=cB(),bE4=class{static{pY(this,"HeaderFormatter")}format(A){let B=[];for(let Z of Object.keys(A)){let G=E32.fromUtf8(Z);B.push(Uint8Array.from([G.byteLength]),G,this.formatHeaderValue(A[Z]))}let Q=new Uint8Array(B.reduce((Z,G)=>Z+G.byteLength,0)),D=0;for(let Z of B)Q.set(Z,D),D+=Z.byteLength;return Q}formatHeaderValue(A){switch(A.type){case"boolean":return Uint8Array.from([A.value?0:1]);case"byte":return Uint8Array.from([2,A.value]);case"short":let B=new DataView(new ArrayBuffer(3));return B.setUint8(0,3),B.setInt16(1,A.value,!1),new Uint8Array(B.buffer);case"integer":let Q=new DataView(new ArrayBuffer(5));return Q.setUint8(0,4),Q.setInt32(1,A.value,!1),new Uint8Array(Q.buffer);case"long":let D=new Uint8Array(9);return D[0]=5,D.set(A.value.bytes,1),D;case"binary":let Z=new DataView(new ArrayBuffer(3+A.value.byteLength));Z.setUint8(0,6),Z.setUint16(1,A.value.byteLength,!1);let G=new Uint8Array(Z.buffer);return G.set(A.value,3),G;case"string":let F=E32.fromUtf8(A.value),I=new DataView(new ArrayBuffer(3+F.byteLength));I.setUint8(0,7),I.setUint16(1,F.byteLength,!1);let Y=new Uint8Array(I.buffer);return Y.set(F,3),Y;case"timestamp":let W=new Uint8Array(9);return W[0]=8,W.set(hE4.fromNumber(A.value.valueOf()).bytes,1),W;case"uuid":if(!fE4.test(A.value))throw new Error(`Invalid UUID received: ${A.value}`);let J=new Uint8Array(17);return J[0]=9,J.set(lk.fromHex(A.value.replace(/\-/g,"")),1),J}}},fE4=/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/,hE4=class A{constructor(B){if(this.bytes=B,B.byteLength!==8)throw new Error("Int64 buffers must be exactly 8 bytes")}static{pY(this,"Int64")}static fromNumber(B){if(B>9223372036854776000||B<-9223372036854776000)throw new Error(`${B} is too large (or, if negative, too small) to represent as an Int64`);let Q=new Uint8Array(8);for(let D=7,Z=Math.abs(Math.round(B));D>-1&&Z>0;D--,Z/=256)Q[D]=Z;if(B<0)i80(Q);return new A(Q)}valueOf(){let B=this.bytes.slice(0),Q=B[0]&128;if(Q)i80(B);return parseInt(lk.toHex(B),16)*(Q?-1:1)}toString(){return String(this.valueOf())}};function i80(A){for(let B=0;B<8;B++)A[B]^=255;for(let B=7;B>-1;B--)if(A[B]++,A[B]!==0)break}pY(i80,"negate");var b32=pY((A,B)=>{A=A.toLowerCase();for(let Q of Object.keys(B))if(A===Q.toLowerCase())return!0;return!1},"hasHeader"),f32=H61(),h32=pY((A,B={})=>{let{headers:Q,query:D={}}=f32.HttpRequest.clone(A);for(let Z of Object.keys(Q)){let G=Z.toLowerCase();if(G.slice(0,6)==="x-amz-"&&!B.unhoistableHeaders?.has(G)||B.hoistableHeaders?.has(G))D[Z]=Q[Z],delete Q[Z]}return{...A,headers:Q,query:D}},"moveHeadersToQuery"),n80=pY((A)=>{A=f32.HttpRequest.clone(A);for(let B of Object.keys(A.headers))if(R32.indexOf(B.toLowerCase())>-1)delete A.headers[B];return A},"prepareRequest"),U32=J5(),gE4=cB(),rq1=K32(),g32=pY(({query:A={}})=>{let B=[],Q={};for(let D of Object.keys(A)){if(D.toLowerCase()===O32)continue;let Z=rq1.escapeUri(D);B.push(Z);let G=A[D];if(typeof G==="string")Q[Z]=`${Z}=${rq1.escapeUri(G)}`;else if(Array.isArray(G))Q[Z]=G.slice(0).reduce((F,I)=>F.concat([`${Z}=${rq1.escapeUri(I)}`]),[]).sort().join("&")}return B.sort().map((D)=>Q[D]).filter((D)=>D).join("&")},"getCanonicalQuery"),uE4=pY((A)=>mE4(A).toISOString().replace(/\.\d{3}Z$/,"Z"),"iso8601"),mE4=pY((A)=>{if(typeof A==="number")return new Date(A*1000);if(typeof A==="string"){if(Number(A))return new Date(Number(A)*1000);return new Date(A)}return A},"toDate"),u32=class{static{pY(this,"SignatureV4Base")}constructor({applyChecksum:A,credentials:B,region:Q,service:D,sha256:Z,uriEscapePath:G=!0}){this.service=D,this.sha256=Z,this.uriEscapePath=G,this.applyChecksum=typeof A==="boolean"?A:!0,this.regionProvider=U32.normalizeProvider(Q),this.credentialProvider=U32.normalizeProvider(B)}createCanonicalRequest(A,B,Q){let D=Object.keys(B).sort();return`${A.method}
${this.getCanonicalPath(A)}
${g32(A)}
${D.map((Z)=>`${Z}:${B[Z]}`).join(`
`)}

${D.join(";")}
${Q}`}async createStringToSign(A,B,Q,D){let Z=new this.sha256;Z.update(gE4.toUint8Array(Q));let G=await Z.digest();return`${D}
${A}
${B}
${lk.toHex(G)}`}getCanonicalPath({path:A}){if(this.uriEscapePath){let B=[];for(let Z of A.split("/")){if(Z?.length===0)continue;if(Z===".")continue;if(Z==="..")B.pop();else B.push(Z)}let Q=`${A?.startsWith("/")?"/":""}${B.join("/")}${B.length>0&&A?.endsWith("/")?"/":""}`;return rq1.escapeUri(Q).replace(/%2F/g,"/")}return A}validateResolvedCredentials(A){if(typeof A!=="object"||typeof A.accessKeyId!=="string"||typeof A.secretAccessKey!=="string")throw new Error("Resolved credential object is not valid")}formatDate(A){let B=uE4(A).replace(/[\-:]/g,"");return{longDate:B,shortDate:B.slice(0,8)}}getCanonicalHeaderList(A){return Object.keys(A).sort().join(";")}},dE4=class extends u32{constructor({applyChecksum:A,credentials:B,region:Q,service:D,sha256:Z,uriEscapePath:G=!0}){super({applyChecksum:A,credentials:B,region:Q,service:D,sha256:Z,uriEscapePath:G});this.headerFormatter=new bE4}static{pY(this,"SignatureV4")}async presign(A,B={}){let{signingDate:Q=new Date,expiresIn:D=3600,unsignableHeaders:Z,unhoistableHeaders:G,signableHeaders:F,hoistableHeaders:I,signingRegion:Y,signingService:W}=B,J=await this.credentialProvider();this.validateResolvedCredentials(J);let X=Y??await this.regionProvider(),{longDate:V,shortDate:C}=this.formatDate(Q);if(D>x32)return Promise.reject("Signature version 4 presigned URLs must have an expiration date less than one week in the future");let K=aq1(C,X,W??this.service),H=h32(n80(A),{unhoistableHeaders:G,hoistableHeaders:I});if(J.sessionToken)H.query[r80]=J.sessionToken;H.query[$32]=iq1,H.query[q32]=`${J.accessKeyId}/${K}`,H.query[a80]=V,H.query[L32]=D.toString(10);let z=p80(H,Z,F);return H.query[N32]=this.getCanonicalHeaderList(z),H.query[s80]=await this.getSignature(V,K,this.getSigningKey(J,X,C,W),this.createCanonicalRequest(H,z,await sq1(A,this.sha256))),H}async sign(A,B){if(typeof A==="string")return this.signString(A,B);else if(A.headers&&A.payload)return this.signEvent(A,B);else if(A.message)return this.signMessage(A,B);else return this.signRequest(A,B)}async signEvent({headers:A,payload:B},{signingDate:Q=new Date,priorSignature:D,signingRegion:Z,signingService:G}){let F=Z??await this.regionProvider(),{shortDate:I,longDate:Y}=this.formatDate(Q),W=aq1(I,F,G??this.service),J=await sq1({headers:{},body:B},this.sha256),X=new this.sha256;X.update(A);let V=lk.toHex(await X.digest()),C=[y32,Y,W,D,V,J].join(`
`);return this.signString(C,{signingDate:Q,signingRegion:F,signingService:G})}async signMessage(A,{signingDate:B=new Date,signingRegion:Q,signingService:D}){return this.signEvent({headers:this.headerFormatter.format(A.message.headers),payload:A.message.body},{signingDate:B,signingRegion:Q,signingService:D,priorSignature:A.priorSignature}).then((G)=>{return{message:A.message,signature:G}})}async signString(A,{signingDate:B=new Date,signingRegion:Q,signingService:D}={}){let Z=await this.credentialProvider();this.validateResolvedCredentials(Z);let G=Q??await this.regionProvider(),{shortDate:F}=this.formatDate(B),I=new this.sha256(await this.getSigningKey(Z,G,F,D));return I.update(H32.toUint8Array(A)),lk.toHex(await I.digest())}async signRequest(A,{signingDate:B=new Date,signableHeaders:Q,unsignableHeaders:D,signingRegion:Z,signingService:G}={}){let F=await this.credentialProvider();this.validateResolvedCredentials(F);let I=Z??await this.regionProvider(),Y=n80(A),{longDate:W,shortDate:J}=this.formatDate(B),X=aq1(J,I,G??this.service);if(Y.headers[t80]=W,F.sessionToken)Y.headers[T32]=F.sessionToken;let V=await sq1(Y,this.sha256);if(!b32(oq1,Y.headers)&&this.applyChecksum)Y.headers[oq1]=V;let C=p80(Y,D,Q),K=await this.getSignature(W,X,this.getSigningKey(F,I,J,G),this.createCanonicalRequest(Y,C,V));return Y.headers[o80]=`${iq1} Credential=${F.accessKeyId}/${X}, SignedHeaders=${this.getCanonicalHeaderList(C)}, Signature=${K}`,Y}async getSignature(A,B,Q,D){let Z=await this.createStringToSign(A,B,D,iq1),G=new this.sha256(await Q);return G.update(H32.toUint8Array(Z)),lk.toHex(await G.digest())}getSigningKey(A,B,Q,D){return v32(this.sha256,A,Q,B,D||this.service)}},cE4={SignatureV4a:null}});
var d50=E((xE5,TG2)=>{var{defineProperty:SN1,getOwnPropertyDescriptor:UL4,getOwnPropertyNames:wL4}=Object,$L4=Object.prototype.hasOwnProperty,jN1=(A,B)=>SN1(A,"name",{value:B,configurable:!0}),qL4=(A,B)=>{for(var Q in B)SN1(A,Q,{get:B[Q],enumerable:!0})},NL4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of wL4(B))if(!$L4.call(A,Z)&&Z!==Q)SN1(A,Z,{get:()=>B[Z],enumerable:!(D=UL4(B,Z))||D.enumerable})}return A},LL4=(A)=>NL4(SN1({},"__esModule",{value:!0}),A),wG2={};qL4(wG2,{AlgorithmId:()=>LG2,EndpointURLScheme:()=>NG2,FieldPosition:()=>MG2,HttpApiKeyAuthLocation:()=>qG2,HttpAuthLocation:()=>$G2,IniSectionType:()=>RG2,RequestHandlerProtocol:()=>OG2,SMITHY_CONTEXT_KEY:()=>PL4,getDefaultClientConfiguration:()=>OL4,resolveDefaultRuntimeConfig:()=>TL4});TG2.exports=LL4(wG2);var $G2=((A)=>{return A.HEADER="header",A.QUERY="query",A})($G2||{}),qG2=((A)=>{return A.HEADER="header",A.QUERY="query",A})(qG2||{}),NG2=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(NG2||{}),LG2=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(LG2||{}),ML4=jN1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),RL4=jN1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),OL4=jN1((A)=>{return ML4(A)},"getDefaultClientConfiguration"),TL4=jN1((A)=>{return RL4(A)},"resolveDefaultRuntimeConfig"),MG2=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(MG2||{}),PL4="__smithy_context",RG2=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(RG2||{}),OG2=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(OG2||{})});
var d82=E((Lz5,m82)=>{var{defineProperty:yq1,getOwnPropertyDescriptor:bH4,getOwnPropertyNames:fH4}=Object,hH4=Object.prototype.hasOwnProperty,mk=(A,B)=>yq1(A,"name",{value:B,configurable:!0}),gH4=(A,B)=>{for(var Q in B)yq1(A,Q,{get:B[Q],enumerable:!0})},uH4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of fH4(B))if(!hH4.call(A,Z)&&Z!==Q)yq1(A,Z,{get:()=>B[Z],enumerable:!(D=bH4(B,Z))||D.enumerable})}return A},mH4=(A)=>uH4(yq1({},"__esModule",{value:!0}),A),f82={};gH4(f82,{Field:()=>lH4,Fields:()=>pH4,HttpRequest:()=>iH4,HttpResponse:()=>nH4,IHttpRequest:()=>h82.HttpRequest,getHttpHandlerExtensionConfiguration:()=>dH4,isValidHostname:()=>u82,resolveHttpHandlerRuntimeConfig:()=>cH4});m82.exports=mH4(f82);var dH4=mk((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),cH4=mk((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),h82=b82(),lH4=class{static{mk(this,"Field")}constructor({name:A,kind:B=h82.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},pH4=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{mk(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},iH4=class A{static{mk(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=g82(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function g82(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}mk(g82,"cloneQuery");var nH4=class{static{mk(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function u82(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}mk(u82,"isValidHostname")});
var dz0=E((fJB)=>{Object.defineProperty(fJB,"__esModule",{value:!0});fJB.resolveHttpAuthSchemeConfig=fJB.defaultBedrockRuntimeHttpAuthSchemeProvider=fJB.defaultBedrockRuntimeHttpAuthSchemeParametersProvider=void 0;var lc6=KI(),mz0=J5(),pc6=async(A,B,Q)=>{return{operation:mz0.getSmithyContext(B).operation,region:await mz0.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};fJB.defaultBedrockRuntimeHttpAuthSchemeParametersProvider=pc6;function ic6(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"bedrock",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}var nc6=(A)=>{let B=[];switch(A.operation){default:B.push(ic6(A))}return B};fJB.defaultBedrockRuntimeHttpAuthSchemeProvider=nc6;var ac6=(A)=>{let B=lc6.resolveAwsSdkSigV4Config(A);return Object.assign(B,{authSchemePreference:mz0.normalizeProvider(A.authSchemePreference??[])})};fJB.resolveHttpAuthSchemeConfig=ac6});
var ek1=E((rz0)=>{Object.defineProperty(rz0,"__esModule",{value:!0});rz0.AwsCrc32=rz0.Crc32=rz0.crc32=void 0;var vl6=pz0(),bl6=az0();function fl6(A){return new UVB().update(A).digest()}rz0.crc32=fl6;var UVB=function(){function A(){this.checksum=**********}return A.prototype.update=function(B){var Q,D;try{for(var Z=vl6.__values(B),G=Z.next();!G.done;G=Z.next()){var F=G.value;this.checksum=this.checksum>>>8^gl6[(this.checksum^F)&255]}}catch(I){Q={error:I}}finally{try{if(G&&!G.done&&(D=Z.return))D.call(Z)}finally{if(Q)throw Q.error}}return this},A.prototype.digest=function(){return(this.checksum^**********)>>>0},A}();rz0.Crc32=UVB;var hl6=[0,**********,**********,**********,124634137,**********,**********,**********,249268274,**********,**********,**********,162941995,**********,**********,**********,498536548,**********,**********,**********,450548861,**********,**********,**********,325883990,**********,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,936918000,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117],gl6=bl6.uint32ArrayFrom(hl6),ul6=EVB();Object.defineProperty(rz0,"AwsCrc32",{enumerable:!0,get:function(){return ul6.AwsCrc32}})});
var fCB=E((vCB)=>{Object.defineProperty(vCB,"__esModule",{value:!0});vCB.ruleSet=void 0;var kCB="required",yM="fn",kM="argv",_e="ref",NCB=!0,LCB="isSet",QZ1="booleanEquals",ke="error",BZ1="endpoint",VX="tree",HE0="PartitionResult",MCB={[kCB]:!1,type:"String"},RCB={[kCB]:!0,default:!1,type:"Boolean"},OCB={[_e]:"Endpoint"},_CB={[yM]:QZ1,[kM]:[{[_e]:"UseFIPS"},!0]},xCB={[yM]:QZ1,[kM]:[{[_e]:"UseDualStack"},!0]},jM={},TCB={[yM]:"getAttr",[kM]:[{[_e]:HE0},"supportsFIPS"]},PCB={[yM]:QZ1,[kM]:[!0,{[yM]:"getAttr",[kM]:[{[_e]:HE0},"supportsDualStack"]}]},SCB=[_CB],jCB=[xCB],yCB=[{[_e]:"Region"}],En6={version:"1.0",parameters:{Region:MCB,UseDualStack:RCB,UseFIPS:RCB,Endpoint:MCB},rules:[{conditions:[{[yM]:LCB,[kM]:[OCB]}],rules:[{conditions:SCB,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:ke},{rules:[{conditions:jCB,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:ke},{endpoint:{url:OCB,properties:jM,headers:jM},type:BZ1}],type:VX}],type:VX},{rules:[{conditions:[{[yM]:LCB,[kM]:yCB}],rules:[{conditions:[{[yM]:"aws.partition",[kM]:yCB,assign:HE0}],rules:[{conditions:[_CB,xCB],rules:[{conditions:[{[yM]:QZ1,[kM]:[NCB,TCB]},PCB],rules:[{rules:[{endpoint:{url:"https://bedrock-runtime-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:jM,headers:jM},type:BZ1}],type:VX}],type:VX},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:ke}],type:VX},{conditions:SCB,rules:[{conditions:[{[yM]:QZ1,[kM]:[TCB,NCB]}],rules:[{rules:[{endpoint:{url:"https://bedrock-runtime-fips.{Region}.{PartitionResult#dnsSuffix}",properties:jM,headers:jM},type:BZ1}],type:VX}],type:VX},{error:"FIPS is enabled but this partition does not support FIPS",type:ke}],type:VX},{conditions:jCB,rules:[{conditions:[PCB],rules:[{rules:[{endpoint:{url:"https://bedrock-runtime.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:jM,headers:jM},type:BZ1}],type:VX}],type:VX},{error:"DualStack is enabled but this partition does not support DualStack",type:ke}],type:VX},{rules:[{endpoint:{url:"https://bedrock-runtime.{Region}.{PartitionResult#dnsSuffix}",properties:jM,headers:jM},type:BZ1}],type:VX}],type:VX}],type:VX},{error:"Invalid Configuration: Missing Region",type:ke}],type:VX}]};vCB.ruleSet=En6});
var g80=E((h80)=>{Object.defineProperty(h80,"__esModule",{value:!0});h80.fromHttp=void 0;var gK4=B82();Object.defineProperty(h80,"fromHttp",{enumerable:!0,get:function(){return gK4.fromHttp}})});
var gVB=E((jA3,hVB)=>{var{defineProperty:F_1,getOwnPropertyDescriptor:jp6,getOwnPropertyNames:yp6}=Object,kp6=Object.prototype.hasOwnProperty,ez0=(A,B)=>F_1(A,"name",{value:B,configurable:!0}),_p6=(A,B)=>{for(var Q in B)F_1(A,Q,{get:B[Q],enumerable:!0})},xp6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of yp6(B))if(!kp6.call(A,Z)&&Z!==Q)F_1(A,Z,{get:()=>B[Z],enumerable:!(D=jp6(B,Z))||D.enumerable})}return A},vp6=(A)=>xp6(F_1({},"__esModule",{value:!0}),A),vVB={};_p6(vVB,{EventStreamMarshaller:()=>fVB,eventStreamSerdeProvider:()=>hp6});hVB.exports=vp6(vVB);var bp6=xVB(),fp6=J1("stream");async function*bVB(A){let B=!1,Q=!1,D=new Array;A.on("error",(Z)=>{if(!B)B=!0;if(Z)throw Z}),A.on("data",(Z)=>{D.push(Z)}),A.on("end",()=>{B=!0});while(!Q){let Z=await new Promise((G)=>setTimeout(()=>G(D.shift()),0));if(Z)yield Z;Q=B&&D.length===0}}ez0(bVB,"readabletoIterable");var fVB=class{static{ez0(this,"EventStreamMarshaller")}constructor({utf8Encoder:A,utf8Decoder:B}){this.universalMarshaller=new bp6.EventStreamMarshaller({utf8Decoder:B,utf8Encoder:A})}deserialize(A,B){let Q=typeof A[Symbol.asyncIterator]==="function"?A:bVB(A);return this.universalMarshaller.deserialize(Q,B)}serialize(A,B){return fp6.Readable.from(this.universalMarshaller.serialize(A,B))}},hp6=ez0((A)=>new fVB(A),"eventStreamSerdeProvider")});
var hY2=E((bY2)=>{Object.defineProperty(bY2,"__esModule",{value:!0});bY2.getRuntimeConfig=void 0;var JT4=KI(),XT4=CB(),VT4=XZ(),CT4=JZ(),xY2=Y30(),vY2=cB(),KT4=w30(),HT4=_Y2(),zT4=(A)=>{return{apiVersion:"2011-06-15",base64Decoder:A?.base64Decoder??xY2.fromBase64,base64Encoder:A?.base64Encoder??xY2.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??HT4.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??KT4.defaultSTSHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new JT4.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new XT4.NoAuthSigner}],logger:A?.logger??new VT4.NoOpLogger,serviceId:A?.serviceId??"STS",urlParser:A?.urlParser??CT4.parseUrl,utf8Decoder:A?.utf8Decoder??vY2.fromUtf8,utf8Encoder:A?.utf8Encoder??vY2.toUtf8}};bY2.getRuntimeConfig=zT4});
var iF2=E((lF2)=>{Object.defineProperty(lF2,"__esModule",{value:!0});lF2.ruleSet=void 0;var uF2="required",az="fn",sz="argv",ds="ref",jF2=!0,yF2="isSet",b61="booleanEquals",us="error",ms="endpoint",hT="tree",W30="PartitionResult",J30="getAttr",kF2={[uF2]:!1,type:"String"},_F2={[uF2]:!0,default:!1,type:"Boolean"},xF2={[ds]:"Endpoint"},mF2={[az]:b61,[sz]:[{[ds]:"UseFIPS"},!0]},dF2={[az]:b61,[sz]:[{[ds]:"UseDualStack"},!0]},nz={},vF2={[az]:J30,[sz]:[{[ds]:W30},"supportsFIPS"]},cF2={[ds]:W30},bF2={[az]:b61,[sz]:[!0,{[az]:J30,[sz]:[cF2,"supportsDualStack"]}]},fF2=[mF2],hF2=[dF2],gF2=[{[ds]:"Region"}],GR4={version:"1.0",parameters:{Region:kF2,UseDualStack:_F2,UseFIPS:_F2,Endpoint:kF2},rules:[{conditions:[{[az]:yF2,[sz]:[xF2]}],rules:[{conditions:fF2,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:us},{conditions:hF2,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:us},{endpoint:{url:xF2,properties:nz,headers:nz},type:ms}],type:hT},{conditions:[{[az]:yF2,[sz]:gF2}],rules:[{conditions:[{[az]:"aws.partition",[sz]:gF2,assign:W30}],rules:[{conditions:[mF2,dF2],rules:[{conditions:[{[az]:b61,[sz]:[jF2,vF2]},bF2],rules:[{endpoint:{url:"https://oidc-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:nz,headers:nz},type:ms}],type:hT},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:us}],type:hT},{conditions:fF2,rules:[{conditions:[{[az]:b61,[sz]:[vF2,jF2]}],rules:[{conditions:[{[az]:"stringEquals",[sz]:[{[az]:J30,[sz]:[cF2,"name"]},"aws-us-gov"]}],endpoint:{url:"https://oidc.{Region}.amazonaws.com",properties:nz,headers:nz},type:ms},{endpoint:{url:"https://oidc-fips.{Region}.{PartitionResult#dnsSuffix}",properties:nz,headers:nz},type:ms}],type:hT},{error:"FIPS is enabled but this partition does not support FIPS",type:us}],type:hT},{conditions:hF2,rules:[{conditions:[bF2],rules:[{endpoint:{url:"https://oidc.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:nz,headers:nz},type:ms}],type:hT},{error:"DualStack is enabled but this partition does not support DualStack",type:us}],type:hT},{endpoint:{url:"https://oidc.{Region}.{PartitionResult#dnsSuffix}",properties:nz,headers:nz},type:ms}],type:hT}],type:hT},{error:"Invalid Configuration: Missing Region",type:us}]};lF2.ruleSet=GR4});
var iI2=E((GU5,pI2)=>{var{create:MO4,defineProperty:h61,getOwnPropertyDescriptor:RO4,getOwnPropertyNames:OO4,getPrototypeOf:TO4}=Object,PO4=Object.prototype.hasOwnProperty,ak=(A,B)=>h61(A,"name",{value:B,configurable:!0}),SO4=(A,B)=>{for(var Q in B)h61(A,Q,{get:B[Q],enumerable:!0})},mI2=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of OO4(B))if(!PO4.call(A,Z)&&Z!==Q)h61(A,Z,{get:()=>B[Z],enumerable:!(D=RO4(B,Z))||D.enumerable})}return A},dI2=(A,B,Q)=>(Q=A!=null?MO4(TO4(A)):{},mI2(B||!A||!A.__esModule?h61(Q,"default",{value:A,enumerable:!0}):Q,A)),jO4=(A)=>mI2(h61({},"__esModule",{value:!0}),A),cI2={};SO4(cI2,{fromSso:()=>lI2,fromStatic:()=>fO4,nodeProvider:()=>hO4});pI2.exports=jO4(cI2);var yO4=300000,H30="To refresh this SSO session run 'aws sso login' with the corresponding profile.",kO4=ak(async(A,B={})=>{let{SSOOIDCClient:Q}=await Promise.resolve().then(()=>dI2(K30()));return new Q(Object.assign({},B.clientConfig??{},{region:A??B.clientConfig?.region,logger:B.clientConfig?.logger??B.parentClientConfig?.logger}))},"getSsoOidcClient"),_O4=ak(async(A,B,Q={})=>{let{CreateTokenCommand:D}=await Promise.resolve().then(()=>dI2(K30()));return(await kO4(B,Q)).send(new D({clientId:A.clientId,clientSecret:A.clientSecret,refreshToken:A.refreshToken,grantType:"refresh_token"}))},"getNewSsoOidcToken"),hw=Q9(),gI2=ak((A)=>{if(A.expiration&&A.expiration.getTime()<Date.now())throw new hw.TokenProviderError(`Token is expired. ${H30}`,!1)},"validateTokenExpiry"),Rg=ak((A,B,Q=!1)=>{if(typeof B==="undefined")throw new hw.TokenProviderError(`Value not present for '${A}' in SSO Token${Q?". Cannot refresh":""}. ${H30}`,!1)},"validateTokenKey"),f61=D3(),xO4=J1("fs"),{writeFile:vO4}=xO4.promises,bO4=ak((A,B)=>{let Q=f61.getSSOTokenFilepath(A),D=JSON.stringify(B,null,2);return vO4(Q,D)},"writeSSOTokenToFile"),uI2=new Date(0),lI2=ak((A={})=>async({callerClientConfig:B}={})=>{let Q={...A,parentClientConfig:{...B,...A.parentClientConfig}};Q.logger?.debug("@aws-sdk/token-providers - fromSso");let D=await f61.parseKnownFiles(Q),Z=f61.getProfileName({profile:Q.profile??B?.profile}),G=D[Z];if(!G)throw new hw.TokenProviderError(`Profile '${Z}' could not be found in shared credentials file.`,!1);else if(!G.sso_session)throw new hw.TokenProviderError(`Profile '${Z}' is missing required property 'sso_session'.`);let F=G.sso_session,Y=(await f61.loadSsoSessionData(Q))[F];if(!Y)throw new hw.TokenProviderError(`Sso session '${F}' could not be found in shared credentials file.`,!1);for(let H of["sso_start_url","sso_region"])if(!Y[H])throw new hw.TokenProviderError(`Sso session '${F}' is missing required property '${H}'.`,!1);let{sso_start_url:W,sso_region:J}=Y,X;try{X=await f61.getSSOTokenFromFile(F)}catch(H){throw new hw.TokenProviderError(`The SSO session token associated with profile=${Z} was not found or is invalid. ${H30}`,!1)}Rg("accessToken",X.accessToken),Rg("expiresAt",X.expiresAt);let{accessToken:V,expiresAt:C}=X,K={token:V,expiration:new Date(C)};if(K.expiration.getTime()-Date.now()>yO4)return K;if(Date.now()-uI2.getTime()<30000)return gI2(K),K;Rg("clientId",X.clientId,!0),Rg("clientSecret",X.clientSecret,!0),Rg("refreshToken",X.refreshToken,!0);try{uI2.setTime(Date.now());let H=await _O4(X,J,Q);Rg("accessToken",H.accessToken),Rg("expiresIn",H.expiresIn);let z=new Date(Date.now()+H.expiresIn*1000);try{await bO4(F,{...X,accessToken:H.accessToken,expiresAt:z.toISOString(),refreshToken:H.refreshToken})}catch($){}return{token:H.accessToken,expiration:z}}catch(H){return gI2(K),K}},"fromSso"),fO4=ak(({token:A,logger:B})=>async()=>{if(B?.debug("@aws-sdk/token-providers - fromStatic"),!A||!A.token)throw new hw.TokenProviderError("Please pass a valid token to fromStatic",!1);return A},"fromStatic"),hO4=ak((A={})=>hw.memoize(hw.chain(lI2(A),async()=>{throw new hw.TokenProviderError("Could not load token from any providers",!1)}),(B)=>B.expiration!==void 0&&B.expiration.getTime()-Date.now()<300000,(B)=>B.expiration!==void 0),"nodeProvider")});
var jY2=E((PY2)=>{Object.defineProperty(PY2,"__esModule",{value:!0});PY2.ruleSet=void 0;var UY2="required",$4="type",a8="fn",s8="argv",rk="ref",IY2=!1,$30=!0,sk="booleanEquals",nY="stringEquals",wY2="sigv4",$Y2="sts",qY2="us-east-1",qD="endpoint",YY2="https://sts.{Region}.{PartitionResult#dnsSuffix}",LL="tree",ps="error",N30="getAttr",WY2={[UY2]:!1,[$4]:"String"},q30={[UY2]:!0,default:!1,[$4]:"Boolean"},NY2={[rk]:"Endpoint"},JY2={[a8]:"isSet",[s8]:[{[rk]:"Region"}]},aY={[rk]:"Region"},XY2={[a8]:"aws.partition",[s8]:[aY],assign:"PartitionResult"},LY2={[rk]:"UseFIPS"},MY2={[rk]:"UseDualStack"},oW={url:"https://sts.amazonaws.com",properties:{authSchemes:[{name:wY2,signingName:$Y2,signingRegion:qY2}]},headers:{}},UK={},VY2={conditions:[{[a8]:nY,[s8]:[aY,"aws-global"]}],[qD]:oW,[$4]:qD},RY2={[a8]:sk,[s8]:[LY2,!0]},OY2={[a8]:sk,[s8]:[MY2,!0]},CY2={[a8]:N30,[s8]:[{[rk]:"PartitionResult"},"supportsFIPS"]},TY2={[rk]:"PartitionResult"},KY2={[a8]:sk,[s8]:[!0,{[a8]:N30,[s8]:[TY2,"supportsDualStack"]}]},HY2=[{[a8]:"isSet",[s8]:[NY2]}],zY2=[RY2],EY2=[OY2],GT4={version:"1.0",parameters:{Region:WY2,UseDualStack:q30,UseFIPS:q30,Endpoint:WY2,UseGlobalEndpoint:q30},rules:[{conditions:[{[a8]:sk,[s8]:[{[rk]:"UseGlobalEndpoint"},$30]},{[a8]:"not",[s8]:HY2},JY2,XY2,{[a8]:sk,[s8]:[LY2,IY2]},{[a8]:sk,[s8]:[MY2,IY2]}],rules:[{conditions:[{[a8]:nY,[s8]:[aY,"ap-northeast-1"]}],endpoint:oW,[$4]:qD},{conditions:[{[a8]:nY,[s8]:[aY,"ap-south-1"]}],endpoint:oW,[$4]:qD},{conditions:[{[a8]:nY,[s8]:[aY,"ap-southeast-1"]}],endpoint:oW,[$4]:qD},{conditions:[{[a8]:nY,[s8]:[aY,"ap-southeast-2"]}],endpoint:oW,[$4]:qD},VY2,{conditions:[{[a8]:nY,[s8]:[aY,"ca-central-1"]}],endpoint:oW,[$4]:qD},{conditions:[{[a8]:nY,[s8]:[aY,"eu-central-1"]}],endpoint:oW,[$4]:qD},{conditions:[{[a8]:nY,[s8]:[aY,"eu-north-1"]}],endpoint:oW,[$4]:qD},{conditions:[{[a8]:nY,[s8]:[aY,"eu-west-1"]}],endpoint:oW,[$4]:qD},{conditions:[{[a8]:nY,[s8]:[aY,"eu-west-2"]}],endpoint:oW,[$4]:qD},{conditions:[{[a8]:nY,[s8]:[aY,"eu-west-3"]}],endpoint:oW,[$4]:qD},{conditions:[{[a8]:nY,[s8]:[aY,"sa-east-1"]}],endpoint:oW,[$4]:qD},{conditions:[{[a8]:nY,[s8]:[aY,qY2]}],endpoint:oW,[$4]:qD},{conditions:[{[a8]:nY,[s8]:[aY,"us-east-2"]}],endpoint:oW,[$4]:qD},{conditions:[{[a8]:nY,[s8]:[aY,"us-west-1"]}],endpoint:oW,[$4]:qD},{conditions:[{[a8]:nY,[s8]:[aY,"us-west-2"]}],endpoint:oW,[$4]:qD},{endpoint:{url:YY2,properties:{authSchemes:[{name:wY2,signingName:$Y2,signingRegion:"{Region}"}]},headers:UK},[$4]:qD}],[$4]:LL},{conditions:HY2,rules:[{conditions:zY2,error:"Invalid Configuration: FIPS and custom endpoint are not supported",[$4]:ps},{conditions:EY2,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",[$4]:ps},{endpoint:{url:NY2,properties:UK,headers:UK},[$4]:qD}],[$4]:LL},{conditions:[JY2],rules:[{conditions:[XY2],rules:[{conditions:[RY2,OY2],rules:[{conditions:[{[a8]:sk,[s8]:[$30,CY2]},KY2],rules:[{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:UK,headers:UK},[$4]:qD}],[$4]:LL},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",[$4]:ps}],[$4]:LL},{conditions:zY2,rules:[{conditions:[{[a8]:sk,[s8]:[CY2,$30]}],rules:[{conditions:[{[a8]:nY,[s8]:[{[a8]:N30,[s8]:[TY2,"name"]},"aws-us-gov"]}],endpoint:{url:"https://sts.{Region}.amazonaws.com",properties:UK,headers:UK},[$4]:qD},{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dnsSuffix}",properties:UK,headers:UK},[$4]:qD}],[$4]:LL},{error:"FIPS is enabled but this partition does not support FIPS",[$4]:ps}],[$4]:LL},{conditions:EY2,rules:[{conditions:[KY2],rules:[{endpoint:{url:"https://sts.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:UK,headers:UK},[$4]:qD}],[$4]:LL},{error:"DualStack is enabled but this partition does not support DualStack",[$4]:ps}],[$4]:LL},VY2,{endpoint:{url:YY2,properties:UK,headers:UK},[$4]:qD}],[$4]:LL}],[$4]:LL},{error:"Invalid Configuration: Missing Region",[$4]:ps}]};PY2.ruleSet=GT4});
var kZ2=E((jZ2)=>{Object.defineProperty(jZ2,"__esModule",{value:!0});jZ2.getRuntimeConfig=void 0;var GN4=XD2(),FN4=GN4.__importDefault(VD2()),IN4=KI(),TZ2=N61(),wN1=K4(),YN4=gG(),PZ2=u4(),_s=JD(),SZ2=k3(),WN4=uG(),JN4=sZ(),XN4=OZ2(),VN4=T61(),CN4=mG(),KN4=T61(),HN4=(A)=>{KN4.emitWarningIfUnsupportedVersion(process.version);let B=CN4.resolveDefaultsModeConfig(A),Q=()=>B().then(VN4.loadConfigsForDefaultMode),D=XN4.getRuntimeConfig(A);IN4.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile};return{...D,...A,runtime:"node",defaultsMode:B,bodyLengthChecker:A?.bodyLengthChecker??WN4.calculateBodyLength,defaultUserAgentProvider:A?.defaultUserAgentProvider??TZ2.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:FN4.default.version}),maxAttempts:A?.maxAttempts??_s.loadConfig(PZ2.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??_s.loadConfig(wN1.NODE_REGION_CONFIG_OPTIONS,{...wN1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:SZ2.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??_s.loadConfig({...PZ2.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||JN4.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??YN4.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??SZ2.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??_s.loadConfig(wN1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??_s.loadConfig(wN1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??_s.loadConfig(TZ2.NODE_APP_ID_CONFIG_OPTIONS,Z)}};jZ2.getRuntimeConfig=HN4});
var l62=E((Gz5,c62)=>{var{defineProperty:Nq1,getOwnPropertyDescriptor:WC4,getOwnPropertyNames:JC4}=Object,XC4=Object.prototype.hasOwnProperty,S2=(A,B)=>Nq1(A,"name",{value:B,configurable:!0}),VC4=(A,B)=>{for(var Q in B)Nq1(A,Q,{get:B[Q],enumerable:!0})},CC4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of JC4(B))if(!XC4.call(A,Z)&&Z!==Q)Nq1(A,Z,{get:()=>B[Z],enumerable:!(D=WC4(B,Z))||D.enumerable})}return A},KC4=(A)=>CC4(Nq1({},"__esModule",{value:!0}),A),T62={};VC4(T62,{Client:()=>HC4,Command:()=>S62,LazyJsonString:()=>$g,NoOpLogger:()=>XK4,SENSITIVE_STRING:()=>EC4,ServiceException:()=>tC4,_json:()=>y80,collectBody:()=>R80.collectBody,convertMap:()=>VK4,createAggregatedClient:()=>UC4,dateToUtcString:()=>v62,decorateServiceException:()=>b62,emitWarningIfUnsupportedVersion:()=>QK4,expectBoolean:()=>$C4,expectByte:()=>j80,expectFloat32:()=>$q1,expectInt:()=>NC4,expectInt32:()=>P80,expectLong:()=>J61,expectNonNull:()=>MC4,expectNumber:()=>W61,expectObject:()=>j62,expectShort:()=>S80,expectString:()=>RC4,expectUnion:()=>OC4,extendedEncodeURIComponent:()=>R80.extendedEncodeURIComponent,getArrayIfSingleItem:()=>WK4,getDefaultClientConfiguration:()=>IK4,getDefaultExtensionConfiguration:()=>h62,getValueFromTextNode:()=>g62,handleFloat:()=>SC4,isSerializableHeaderValue:()=>JK4,limitedParseDouble:()=>x80,limitedParseFloat:()=>jC4,limitedParseFloat32:()=>yC4,loadConfigsForDefaultMode:()=>BK4,logger:()=>X61,map:()=>b80,parseBoolean:()=>wC4,parseEpochTimestamp:()=>cC4,parseRfc3339DateTime:()=>bC4,parseRfc3339DateTimeWithOffset:()=>hC4,parseRfc7231DateTime:()=>dC4,quoteHeader:()=>m62,resolveDefaultRuntimeConfig:()=>YK4,resolvedPath:()=>R80.resolvedPath,serializeDateTime:()=>UK4,serializeFloat:()=>EK4,splitEvery:()=>d62,splitHeader:()=>wK4,strictParseByte:()=>x62,strictParseDouble:()=>_80,strictParseFloat:()=>TC4,strictParseFloat32:()=>y62,strictParseInt:()=>kC4,strictParseInt32:()=>_C4,strictParseLong:()=>_62,strictParseShort:()=>Hs,take:()=>CK4,throwDefaultError:()=>f62,withBaseException:()=>eC4});c62.exports=KC4(T62);var P62=Mw(),HC4=class{constructor(A){this.config=A,this.middlewareStack=P62.constructStack()}static{S2(this,"Client")}send(A,B,Q){let D=typeof B!=="function"?B:void 0,Z=typeof B==="function"?B:Q,G=D===void 0&&this.config.cacheMiddleware===!0,F;if(G){if(!this.handlers)this.handlers=new WeakMap;let I=this.handlers;if(I.has(A.constructor))F=I.get(A.constructor);else F=A.resolveMiddleware(this.middlewareStack,this.config,D),I.set(A.constructor,F)}else delete this.handlers,F=A.resolveMiddleware(this.middlewareStack,this.config,D);if(Z)F(A).then((I)=>Z(null,I.output),(I)=>Z(I)).catch(()=>{});else return F(A).then((I)=>I.output)}destroy(){this.config?.requestHandler?.destroy?.(),delete this.handlers}},R80=M6(),T80=M80(),S62=class{constructor(){this.middlewareStack=P62.constructStack()}static{S2(this,"Command")}static classBuilder(){return new zC4}resolveMiddlewareWithContext(A,B,Q,{middlewareFn:D,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,smithyContext:Y,additionalContext:W,CommandCtor:J}){for(let H of D.bind(this)(J,A,B,Q))this.middlewareStack.use(H);let X=A.concat(this.middlewareStack),{logger:V}=B,C={logger:V,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,[T80.SMITHY_CONTEXT_KEY]:{commandInstance:this,...Y},...W},{requestHandler:K}=B;return X.resolve((H)=>K.handle(H.request,Q||{}),C)}},zC4=class{constructor(){this._init=()=>{},this._ep={},this._middlewareFn=()=>[],this._commandName="",this._clientName="",this._additionalContext={},this._smithyContext={},this._inputFilterSensitiveLog=(A)=>A,this._outputFilterSensitiveLog=(A)=>A,this._serializer=null,this._deserializer=null}static{S2(this,"ClassBuilder")}init(A){this._init=A}ep(A){return this._ep=A,this}m(A){return this._middlewareFn=A,this}s(A,B,Q={}){return this._smithyContext={service:A,operation:B,...Q},this}c(A={}){return this._additionalContext=A,this}n(A,B){return this._clientName=A,this._commandName=B,this}f(A=(Q)=>Q,B=(Q)=>Q){return this._inputFilterSensitiveLog=A,this._outputFilterSensitiveLog=B,this}ser(A){return this._serializer=A,this}de(A){return this._deserializer=A,this}build(){let A=this,B;return B=class extends S62{constructor(...[Q]){super();this.serialize=A._serializer,this.deserialize=A._deserializer,this.input=Q??{},A._init(this)}static{S2(this,"CommandRef")}static getEndpointParameterInstructions(){return A._ep}resolveMiddleware(Q,D,Z){return this.resolveMiddlewareWithContext(Q,D,Z,{CommandCtor:B,middlewareFn:A._middlewareFn,clientName:A._clientName,commandName:A._commandName,inputFilterSensitiveLog:A._inputFilterSensitiveLog,outputFilterSensitiveLog:A._outputFilterSensitiveLog,smithyContext:A._smithyContext,additionalContext:A._additionalContext})}}}},EC4="***SensitiveInformation***",UC4=S2((A,B)=>{for(let Q of Object.keys(A)){let D=A[Q],Z=S2(async function(F,I,Y){let W=new D(F);if(typeof I==="function")this.send(W,I);else if(typeof Y==="function"){if(typeof I!=="object")throw new Error(`Expected http options but got ${typeof I}`);this.send(W,I||{},Y)}else return this.send(W,I)},"methodImpl"),G=(Q[0].toLowerCase()+Q.slice(1)).replace(/Command$/,"");B.prototype[G]=Z}},"createAggregatedClient"),wC4=S2((A)=>{switch(A){case"true":return!0;case"false":return!1;default:throw new Error(`Unable to parse boolean value "${A}"`)}},"parseBoolean"),$C4=S2((A)=>{if(A===null||A===void 0)return;if(typeof A==="number"){if(A===0||A===1)X61.warn(qq1(`Expected boolean, got ${typeof A}: ${A}`));if(A===0)return!1;if(A===1)return!0}if(typeof A==="string"){let B=A.toLowerCase();if(B==="false"||B==="true")X61.warn(qq1(`Expected boolean, got ${typeof A}: ${A}`));if(B==="false")return!1;if(B==="true")return!0}if(typeof A==="boolean")return A;throw new TypeError(`Expected boolean, got ${typeof A}: ${A}`)},"expectBoolean"),W61=S2((A)=>{if(A===null||A===void 0)return;if(typeof A==="string"){let B=parseFloat(A);if(!Number.isNaN(B)){if(String(B)!==String(A))X61.warn(qq1(`Expected number but observed string: ${A}`));return B}}if(typeof A==="number")return A;throw new TypeError(`Expected number, got ${typeof A}: ${A}`)},"expectNumber"),qC4=Math.ceil(340282346638528860000000000000000000000),$q1=S2((A)=>{let B=W61(A);if(B!==void 0&&!Number.isNaN(B)&&B!==1/0&&B!==-1/0){if(Math.abs(B)>qC4)throw new TypeError(`Expected 32-bit float, got ${A}`)}return B},"expectFloat32"),J61=S2((A)=>{if(A===null||A===void 0)return;if(Number.isInteger(A)&&!Number.isNaN(A))return A;throw new TypeError(`Expected integer, got ${typeof A}: ${A}`)},"expectLong"),NC4=J61,P80=S2((A)=>k80(A,32),"expectInt32"),S80=S2((A)=>k80(A,16),"expectShort"),j80=S2((A)=>k80(A,8),"expectByte"),k80=S2((A,B)=>{let Q=J61(A);if(Q!==void 0&&LC4(Q,B)!==Q)throw new TypeError(`Expected ${B}-bit integer, got ${A}`);return Q},"expectSizedInt"),LC4=S2((A,B)=>{switch(B){case 32:return Int32Array.of(A)[0];case 16:return Int16Array.of(A)[0];case 8:return Int8Array.of(A)[0]}},"castInt"),MC4=S2((A,B)=>{if(A===null||A===void 0){if(B)throw new TypeError(`Expected a non-null value for ${B}`);throw new TypeError("Expected a non-null value")}return A},"expectNonNull"),j62=S2((A)=>{if(A===null||A===void 0)return;if(typeof A==="object"&&!Array.isArray(A))return A;let B=Array.isArray(A)?"array":typeof A;throw new TypeError(`Expected object, got ${B}: ${A}`)},"expectObject"),RC4=S2((A)=>{if(A===null||A===void 0)return;if(typeof A==="string")return A;if(["boolean","number","bigint"].includes(typeof A))return X61.warn(qq1(`Expected string, got ${typeof A}: ${A}`)),String(A);throw new TypeError(`Expected string, got ${typeof A}: ${A}`)},"expectString"),OC4=S2((A)=>{if(A===null||A===void 0)return;let B=j62(A),Q=Object.entries(B).filter(([,D])=>D!=null).map(([D])=>D);if(Q.length===0)throw new TypeError("Unions must have exactly one non-null member. None were found.");if(Q.length>1)throw new TypeError(`Unions must have exactly one non-null member. Keys ${Q} were not null.`);return B},"expectUnion"),_80=S2((A)=>{if(typeof A=="string")return W61(Es(A));return W61(A)},"strictParseDouble"),TC4=_80,y62=S2((A)=>{if(typeof A=="string")return $q1(Es(A));return $q1(A)},"strictParseFloat32"),PC4=/(-?(?:0|[1-9]\d*)(?:\.\d+)?(?:[eE][+-]?\d+)?)|(-?Infinity)|(NaN)/g,Es=S2((A)=>{let B=A.match(PC4);if(B===null||B[0].length!==A.length)throw new TypeError("Expected real number, got implicit NaN");return parseFloat(A)},"parseNumber"),x80=S2((A)=>{if(typeof A=="string")return k62(A);return W61(A)},"limitedParseDouble"),SC4=x80,jC4=x80,yC4=S2((A)=>{if(typeof A=="string")return k62(A);return $q1(A)},"limitedParseFloat32"),k62=S2((A)=>{switch(A){case"NaN":return NaN;case"Infinity":return 1/0;case"-Infinity":return-1/0;default:throw new Error(`Unable to parse float value: ${A}`)}},"parseFloatString"),_62=S2((A)=>{if(typeof A==="string")return J61(Es(A));return J61(A)},"strictParseLong"),kC4=_62,_C4=S2((A)=>{if(typeof A==="string")return P80(Es(A));return P80(A)},"strictParseInt32"),Hs=S2((A)=>{if(typeof A==="string")return S80(Es(A));return S80(A)},"strictParseShort"),x62=S2((A)=>{if(typeof A==="string")return j80(Es(A));return j80(A)},"strictParseByte"),qq1=S2((A)=>{return String(new TypeError(A).stack||A).split(`
`).slice(0,5).filter((B)=>!B.includes("stackTraceWarning")).join(`
`)},"stackTraceWarning"),X61={warn:console.warn},xC4=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],v80=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function v62(A){let B=A.getUTCFullYear(),Q=A.getUTCMonth(),D=A.getUTCDay(),Z=A.getUTCDate(),G=A.getUTCHours(),F=A.getUTCMinutes(),I=A.getUTCSeconds(),Y=Z<10?`0${Z}`:`${Z}`,W=G<10?`0${G}`:`${G}`,J=F<10?`0${F}`:`${F}`,X=I<10?`0${I}`:`${I}`;return`${xC4[D]}, ${Y} ${v80[Q]} ${B} ${W}:${J}:${X} GMT`}S2(v62,"dateToUtcString");var vC4=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?[zZ]$/),bC4=S2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-3339 date-times must be expressed as strings");let B=vC4.exec(A);if(!B)throw new TypeError("Invalid RFC-3339 date-time value");let[Q,D,Z,G,F,I,Y,W]=B,J=Hs(zs(D)),X=EL(Z,"month",1,12),V=EL(G,"day",1,31);return Y61(J,X,V,{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W})},"parseRfc3339DateTime"),fC4=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?(([-+]\d{2}\:\d{2})|[zZ])$/),hC4=S2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-3339 date-times must be expressed as strings");let B=fC4.exec(A);if(!B)throw new TypeError("Invalid RFC-3339 date-time value");let[Q,D,Z,G,F,I,Y,W,J]=B,X=Hs(zs(D)),V=EL(Z,"month",1,12),C=EL(G,"day",1,31),K=Y61(X,V,C,{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W});if(J.toUpperCase()!="Z")K.setTime(K.getTime()-oC4(J));return K},"parseRfc3339DateTimeWithOffset"),gC4=new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d{2}) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/),uC4=new RegExp(/^(?:Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d{2})-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/),mC4=new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( [1-9]|\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? (\d{4})$/),dC4=S2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-7231 date-times must be expressed as strings");let B=gC4.exec(A);if(B){let[Q,D,Z,G,F,I,Y,W]=B;return Y61(Hs(zs(G)),O80(Z),EL(D,"day",1,31),{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W})}if(B=uC4.exec(A),B){let[Q,D,Z,G,F,I,Y,W]=B;return iC4(Y61(lC4(G),O80(Z),EL(D,"day",1,31),{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W}))}if(B=mC4.exec(A),B){let[Q,D,Z,G,F,I,Y,W]=B;return Y61(Hs(zs(W)),O80(D),EL(Z.trimLeft(),"day",1,31),{hours:G,minutes:F,seconds:I,fractionalMilliseconds:Y})}throw new TypeError("Invalid RFC-7231 date-time value")},"parseRfc7231DateTime"),cC4=S2((A)=>{if(A===null||A===void 0)return;let B;if(typeof A==="number")B=A;else if(typeof A==="string")B=_80(A);else if(typeof A==="object"&&A.tag===1)B=A.value;else throw new TypeError("Epoch timestamps must be expressed as floating point numbers or their string representation");if(Number.isNaN(B)||B===1/0||B===-1/0)throw new TypeError("Epoch timestamps must be valid, non-Infinite, non-NaN numerics");return new Date(Math.round(B*1000))},"parseEpochTimestamp"),Y61=S2((A,B,Q,D)=>{let Z=B-1;return aC4(A,Z,Q),new Date(Date.UTC(A,Z,Q,EL(D.hours,"hour",0,23),EL(D.minutes,"minute",0,59),EL(D.seconds,"seconds",0,60),rC4(D.fractionalMilliseconds)))},"buildDate"),lC4=S2((A)=>{let B=new Date().getUTCFullYear(),Q=Math.floor(B/100)*100+Hs(zs(A));if(Q<B)return Q+100;return Q},"parseTwoDigitYear"),pC4=1576800000000,iC4=S2((A)=>{if(A.getTime()-new Date().getTime()>pC4)return new Date(Date.UTC(A.getUTCFullYear()-100,A.getUTCMonth(),A.getUTCDate(),A.getUTCHours(),A.getUTCMinutes(),A.getUTCSeconds(),A.getUTCMilliseconds()));return A},"adjustRfc850Year"),O80=S2((A)=>{let B=v80.indexOf(A);if(B<0)throw new TypeError(`Invalid month: ${A}`);return B+1},"parseMonthByShortName"),nC4=[31,28,31,30,31,30,31,31,30,31,30,31],aC4=S2((A,B,Q)=>{let D=nC4[B];if(B===1&&sC4(A))D=29;if(Q>D)throw new TypeError(`Invalid day for ${v80[B]} in ${A}: ${Q}`)},"validateDayOfMonth"),sC4=S2((A)=>{return A%4===0&&(A%100!==0||A%400===0)},"isLeapYear"),EL=S2((A,B,Q,D)=>{let Z=x62(zs(A));if(Z<Q||Z>D)throw new TypeError(`${B} must be between ${Q} and ${D}, inclusive`);return Z},"parseDateValue"),rC4=S2((A)=>{if(A===null||A===void 0)return 0;return y62("0."+A)*1000},"parseMilliseconds"),oC4=S2((A)=>{let B=A[0],Q=1;if(B=="+")Q=1;else if(B=="-")Q=-1;else throw new TypeError(`Offset direction, ${B}, must be "+" or "-"`);let D=Number(A.substring(1,3)),Z=Number(A.substring(4,6));return Q*(D*60+Z)*60*1000},"parseOffsetToMilliseconds"),zs=S2((A)=>{let B=0;while(B<A.length-1&&A.charAt(B)==="0")B++;if(B===0)return A;return A.slice(B)},"stripLeadingZeroes"),tC4=class A extends Error{static{S2(this,"ServiceException")}constructor(B){super(B.message);Object.setPrototypeOf(this,Object.getPrototypeOf(this).constructor.prototype),this.name=B.name,this.$fault=B.$fault,this.$metadata=B.$metadata}static isInstance(B){if(!B)return!1;let Q=B;return A.prototype.isPrototypeOf(Q)||Boolean(Q.$fault)&&Boolean(Q.$metadata)&&(Q.$fault==="client"||Q.$fault==="server")}static[Symbol.hasInstance](B){if(!B)return!1;let Q=B;if(this===A)return A.isInstance(B);if(A.isInstance(B)){if(Q.name&&this.name)return this.prototype.isPrototypeOf(B)||Q.name===this.name;return this.prototype.isPrototypeOf(B)}return!1}},b62=S2((A,B={})=>{Object.entries(B).filter(([,D])=>D!==void 0).forEach(([D,Z])=>{if(A[D]==null||A[D]==="")A[D]=Z});let Q=A.message||A.Message||"UnknownError";return A.message=Q,delete A.Message,A},"decorateServiceException"),f62=S2(({output:A,parsedBody:B,exceptionCtor:Q,errorCode:D})=>{let Z=AK4(A),G=Z.httpStatusCode?Z.httpStatusCode+"":void 0,F=new Q({name:B?.code||B?.Code||D||G||"UnknownError",$fault:"client",$metadata:Z});throw b62(F,B)},"throwDefaultError"),eC4=S2((A)=>{return({output:B,parsedBody:Q,errorCode:D})=>{f62({output:B,parsedBody:Q,exceptionCtor:A,errorCode:D})}},"withBaseException"),AK4=S2((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),BK4=S2((A)=>{switch(A){case"standard":return{retryMode:"standard",connectionTimeout:3100};case"in-region":return{retryMode:"standard",connectionTimeout:1100};case"cross-region":return{retryMode:"standard",connectionTimeout:3100};case"mobile":return{retryMode:"standard",connectionTimeout:30000};default:return{}}},"loadConfigsForDefaultMode"),O62=!1,QK4=S2((A)=>{if(A&&!O62&&parseInt(A.substring(1,A.indexOf(".")))<16)O62=!0},"emitWarningIfUnsupportedVersion"),DK4=S2((A)=>{let B=[];for(let Q in T80.AlgorithmId){let D=T80.AlgorithmId[Q];if(A[D]===void 0)continue;B.push({algorithmId:()=>D,checksumConstructor:()=>A[D]})}return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),ZK4=S2((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),GK4=S2((A)=>{return{setRetryStrategy(B){A.retryStrategy=B},retryStrategy(){return A.retryStrategy}}},"getRetryConfiguration"),FK4=S2((A)=>{let B={};return B.retryStrategy=A.retryStrategy(),B},"resolveRetryRuntimeConfig"),h62=S2((A)=>{return Object.assign(DK4(A),GK4(A))},"getDefaultExtensionConfiguration"),IK4=h62,YK4=S2((A)=>{return Object.assign(ZK4(A),FK4(A))},"resolveDefaultRuntimeConfig"),WK4=S2((A)=>Array.isArray(A)?A:[A],"getArrayIfSingleItem"),g62=S2((A)=>{for(let Q in A)if(A.hasOwnProperty(Q)&&A[Q]["#text"]!==void 0)A[Q]=A[Q]["#text"];else if(typeof A[Q]==="object"&&A[Q]!==null)A[Q]=g62(A[Q]);return A},"getValueFromTextNode"),JK4=S2((A)=>{return A!=null},"isSerializableHeaderValue"),$g=S2(function A(B){return Object.assign(new String(B),{deserializeJSON(){return JSON.parse(String(B))},toString(){return String(B)},toJSON(){return String(B)}})},"LazyJsonString");$g.from=(A)=>{if(A&&typeof A==="object"&&(A instanceof $g||("deserializeJSON"in A)))return A;else if(typeof A==="string"||Object.getPrototypeOf(A)===String.prototype)return $g(String(A));return $g(JSON.stringify(A))};$g.fromObject=$g.from;var XK4=class{static{S2(this,"NoOpLogger")}trace(){}debug(){}info(){}warn(){}error(){}};function b80(A,B,Q){let D,Z,G;if(typeof B==="undefined"&&typeof Q==="undefined")D={},G=A;else if(D=A,typeof B==="function")return Z=B,G=Q,KK4(D,Z,G);else G=B;for(let F of Object.keys(G)){if(!Array.isArray(G[F])){D[F]=G[F];continue}u62(D,null,G,F)}return D}S2(b80,"map");var VK4=S2((A)=>{let B={};for(let[Q,D]of Object.entries(A||{}))B[Q]=[,D];return B},"convertMap"),CK4=S2((A,B)=>{let Q={};for(let D in B)u62(Q,A,B,D);return Q},"take"),KK4=S2((A,B,Q)=>{return b80(A,Object.entries(Q).reduce((D,[Z,G])=>{if(Array.isArray(G))D[Z]=G;else if(typeof G==="function")D[Z]=[B,G()];else D[Z]=[B,G];return D},{}))},"mapWithFilter"),u62=S2((A,B,Q,D)=>{if(B!==null){let F=Q[D];if(typeof F==="function")F=[,F];let[I=HK4,Y=zK4,W=D]=F;if(typeof I==="function"&&I(B[W])||typeof I!=="function"&&!!I)A[D]=Y(B[W]);return}let[Z,G]=Q[D];if(typeof G==="function"){let F,I=Z===void 0&&(F=G())!=null,Y=typeof Z==="function"&&!!Z(void 0)||typeof Z!=="function"&&!!Z;if(I)A[D]=F;else if(Y)A[D]=G()}else{let F=Z===void 0&&G!=null,I=typeof Z==="function"&&!!Z(G)||typeof Z!=="function"&&!!Z;if(F||I)A[D]=G}},"applyInstruction"),HK4=S2((A)=>A!=null,"nonNullish"),zK4=S2((A)=>A,"pass");function m62(A){if(A.includes(",")||A.includes('"'))A=`"${A.replace(/"/g,"\\\"")}"`;return A}S2(m62,"quoteHeader");var EK4=S2((A)=>{if(A!==A)return"NaN";switch(A){case 1/0:return"Infinity";case-1/0:return"-Infinity";default:return A}},"serializeFloat"),UK4=S2((A)=>A.toISOString().replace(".000Z","Z"),"serializeDateTime"),y80=S2((A)=>{if(A==null)return{};if(Array.isArray(A))return A.filter((B)=>B!=null).map(y80);if(typeof A==="object"){let B={};for(let Q of Object.keys(A)){if(A[Q]==null)continue;B[Q]=y80(A[Q])}return B}return A},"_json");function d62(A,B,Q){if(Q<=0||!Number.isInteger(Q))throw new Error("Invalid number of delimiters ("+Q+") for splitEvery.");let D=A.split(B);if(Q===1)return D;let Z=[],G="";for(let F=0;F<D.length;F++){if(G==="")G=D[F];else G+=B+D[F];if((F+1)%Q===0)Z.push(G),G=""}if(G!=="")Z.push(G);return Z}S2(d62,"splitEvery");var wK4=S2((A)=>{let B=A.length,Q=[],D=!1,Z=void 0,G=0;for(let F=0;F<B;++F){let I=A[F];switch(I){case'"':if(Z!=="\\")D=!D;break;case",":if(!D)Q.push(A.slice(G,F)),G=F+1;break;default:}Z=I}return Q.push(A.slice(G)),Q.map((F)=>{F=F.trim();let I=F.length;if(I<2)return F;if(F[0]==='"'&&F[I-1]==='"')F=F.slice(1,I-1);return F.replace(/\\"/g,'"')})},"splitHeader")});
var lZ2=E((LE5,cZ2)=>{var{defineProperty:qN1,getOwnPropertyDescriptor:TN4,getOwnPropertyNames:PN4}=Object,SN4=Object.prototype.hasOwnProperty,pk=(A,B)=>qN1(A,"name",{value:B,configurable:!0}),jN4=(A,B)=>{for(var Q in B)qN1(A,Q,{get:B[Q],enumerable:!0})},yN4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of PN4(B))if(!SN4.call(A,Z)&&Z!==Q)qN1(A,Z,{get:()=>B[Z],enumerable:!(D=TN4(B,Z))||D.enumerable})}return A},kN4=(A)=>yN4(qN1({},"__esModule",{value:!0}),A),gZ2={};jN4(gZ2,{Field:()=>vN4,Fields:()=>bN4,HttpRequest:()=>fN4,HttpResponse:()=>hN4,IHttpRequest:()=>uZ2.HttpRequest,getHttpHandlerExtensionConfiguration:()=>_N4,isValidHostname:()=>dZ2,resolveHttpHandlerRuntimeConfig:()=>xN4});cZ2.exports=kN4(gZ2);var _N4=pk((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),xN4=pk((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),uZ2=N50(),vN4=class{static{pk(this,"Field")}constructor({name:A,kind:B=uZ2.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},bN4=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{pk(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},fN4=class A{static{pk(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=mZ2(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function mZ2(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}pk(mZ2,"cloneQuery");var hN4=class{static{pk(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function dZ2(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}pk(dZ2,"isValidHostname")});
var m61=E((GY2)=>{Object.defineProperty(GY2,"__esModule",{value:!0});GY2.commonParams=GY2.resolveClientEndpointParameters=void 0;var DT4=(A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,useGlobalEndpoint:A.useGlobalEndpoint??!1,defaultSigningName:"sts"})};GY2.resolveClientEndpointParameters=DT4;GY2.commonParams={UseGlobalEndpoint:{type:"builtInParams",name:"useGlobalEndpoint"},UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}}});
var n52=E((xz5,uq1)=>{var w52,$52,q52,N52,L52,M52,R52,O52,T52,P52,S52,j52,y52,hq1,d80,k52,_52,x52,$s,v52,b52,f52,h52,g52,u52,m52,d52,c52,gq1,l52,p52,i52;(function(A){var B=typeof global==="object"?global:typeof self==="object"?self:typeof this==="object"?this:{};if(typeof define==="function"&&define.amd)define("tslib",["exports"],function(D){A(Q(B,Q(D)))});else if(typeof uq1==="object"&&typeof xz5==="object")A(Q(B,Q(xz5)));else A(Q(B));function Q(D,Z){if(D!==B)if(typeof Object.create==="function")Object.defineProperty(D,"__esModule",{value:!0});else D.__esModule=!0;return function(G,F){return D[G]=Z?Z(G,F):F}}})(function(A){var B=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(G,F){G.__proto__=F}||function(G,F){for(var I in F)if(Object.prototype.hasOwnProperty.call(F,I))G[I]=F[I]};w52=function(G,F){if(typeof F!=="function"&&F!==null)throw new TypeError("Class extends value "+String(F)+" is not a constructor or null");B(G,F);function I(){this.constructor=G}G.prototype=F===null?Object.create(F):(I.prototype=F.prototype,new I)},$52=Object.assign||function(G){for(var F,I=1,Y=arguments.length;I<Y;I++){F=arguments[I];for(var W in F)if(Object.prototype.hasOwnProperty.call(F,W))G[W]=F[W]}return G},q52=function(G,F){var I={};for(var Y in G)if(Object.prototype.hasOwnProperty.call(G,Y)&&F.indexOf(Y)<0)I[Y]=G[Y];if(G!=null&&typeof Object.getOwnPropertySymbols==="function"){for(var W=0,Y=Object.getOwnPropertySymbols(G);W<Y.length;W++)if(F.indexOf(Y[W])<0&&Object.prototype.propertyIsEnumerable.call(G,Y[W]))I[Y[W]]=G[Y[W]]}return I},N52=function(G,F,I,Y){var W=arguments.length,J=W<3?F:Y===null?Y=Object.getOwnPropertyDescriptor(F,I):Y,X;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")J=Reflect.decorate(G,F,I,Y);else for(var V=G.length-1;V>=0;V--)if(X=G[V])J=(W<3?X(J):W>3?X(F,I,J):X(F,I))||J;return W>3&&J&&Object.defineProperty(F,I,J),J},L52=function(G,F){return function(I,Y){F(I,Y,G)}},M52=function(G,F,I,Y,W,J){function X(T){if(T!==void 0&&typeof T!=="function")throw new TypeError("Function expected");return T}var V=Y.kind,C=V==="getter"?"get":V==="setter"?"set":"value",K=!F&&G?Y.static?G:G.prototype:null,H=F||(K?Object.getOwnPropertyDescriptor(K,Y.name):{}),z,$=!1;for(var L=I.length-1;L>=0;L--){var N={};for(var O in Y)N[O]=O==="access"?{}:Y[O];for(var O in Y.access)N.access[O]=Y.access[O];N.addInitializer=function(T){if($)throw new TypeError("Cannot add initializers after decoration has completed");J.push(X(T||null))};var R=I[L](V==="accessor"?{get:H.get,set:H.set}:H[C],N);if(V==="accessor"){if(R===void 0)continue;if(R===null||typeof R!=="object")throw new TypeError("Object expected");if(z=X(R.get))H.get=z;if(z=X(R.set))H.set=z;if(z=X(R.init))W.unshift(z)}else if(z=X(R))if(V==="field")W.unshift(z);else H[C]=z}if(K)Object.defineProperty(K,Y.name,H);$=!0},R52=function(G,F,I){var Y=arguments.length>2;for(var W=0;W<F.length;W++)I=Y?F[W].call(G,I):F[W].call(G);return Y?I:void 0},O52=function(G){return typeof G==="symbol"?G:"".concat(G)},T52=function(G,F,I){if(typeof F==="symbol")F=F.description?"[".concat(F.description,"]"):"";return Object.defineProperty(G,"name",{configurable:!0,value:I?"".concat(I," ",F):F})},P52=function(G,F){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(G,F)},S52=function(G,F,I,Y){function W(J){return J instanceof I?J:new I(function(X){X(J)})}return new(I||(I=Promise))(function(J,X){function V(H){try{K(Y.next(H))}catch(z){X(z)}}function C(H){try{K(Y.throw(H))}catch(z){X(z)}}function K(H){H.done?J(H.value):W(H.value).then(V,C)}K((Y=Y.apply(G,F||[])).next())})},j52=function(G,F){var I={label:0,sent:function(){if(J[0]&1)throw J[1];return J[1]},trys:[],ops:[]},Y,W,J,X=Object.create((typeof Iterator==="function"?Iterator:Object).prototype);return X.next=V(0),X.throw=V(1),X.return=V(2),typeof Symbol==="function"&&(X[Symbol.iterator]=function(){return this}),X;function V(K){return function(H){return C([K,H])}}function C(K){if(Y)throw new TypeError("Generator is already executing.");while(X&&(X=0,K[0]&&(I=0)),I)try{if(Y=1,W&&(J=K[0]&2?W.return:K[0]?W.throw||((J=W.return)&&J.call(W),0):W.next)&&!(J=J.call(W,K[1])).done)return J;if(W=0,J)K=[K[0]&2,J.value];switch(K[0]){case 0:case 1:J=K;break;case 4:return I.label++,{value:K[1],done:!1};case 5:I.label++,W=K[1],K=[0];continue;case 7:K=I.ops.pop(),I.trys.pop();continue;default:if((J=I.trys,!(J=J.length>0&&J[J.length-1]))&&(K[0]===6||K[0]===2)){I=0;continue}if(K[0]===3&&(!J||K[1]>J[0]&&K[1]<J[3])){I.label=K[1];break}if(K[0]===6&&I.label<J[1]){I.label=J[1],J=K;break}if(J&&I.label<J[2]){I.label=J[2],I.ops.push(K);break}if(J[2])I.ops.pop();I.trys.pop();continue}K=F.call(G,I)}catch(H){K=[6,H],W=0}finally{Y=J=0}if(K[0]&5)throw K[1];return{value:K[0]?K[1]:void 0,done:!0}}},y52=function(G,F){for(var I in G)if(I!=="default"&&!Object.prototype.hasOwnProperty.call(F,I))gq1(F,G,I)},gq1=Object.create?function(G,F,I,Y){if(Y===void 0)Y=I;var W=Object.getOwnPropertyDescriptor(F,I);if(!W||("get"in W?!F.__esModule:W.writable||W.configurable))W={enumerable:!0,get:function(){return F[I]}};Object.defineProperty(G,Y,W)}:function(G,F,I,Y){if(Y===void 0)Y=I;G[Y]=F[I]},hq1=function(G){var F=typeof Symbol==="function"&&Symbol.iterator,I=F&&G[F],Y=0;if(I)return I.call(G);if(G&&typeof G.length==="number")return{next:function(){if(G&&Y>=G.length)G=void 0;return{value:G&&G[Y++],done:!G}}};throw new TypeError(F?"Object is not iterable.":"Symbol.iterator is not defined.")},d80=function(G,F){var I=typeof Symbol==="function"&&G[Symbol.iterator];if(!I)return G;var Y=I.call(G),W,J=[],X;try{while((F===void 0||F-- >0)&&!(W=Y.next()).done)J.push(W.value)}catch(V){X={error:V}}finally{try{if(W&&!W.done&&(I=Y.return))I.call(Y)}finally{if(X)throw X.error}}return J},k52=function(){for(var G=[],F=0;F<arguments.length;F++)G=G.concat(d80(arguments[F]));return G},_52=function(){for(var G=0,F=0,I=arguments.length;F<I;F++)G+=arguments[F].length;for(var Y=Array(G),W=0,F=0;F<I;F++)for(var J=arguments[F],X=0,V=J.length;X<V;X++,W++)Y[W]=J[X];return Y},x52=function(G,F,I){if(I||arguments.length===2){for(var Y=0,W=F.length,J;Y<W;Y++)if(J||!(Y in F)){if(!J)J=Array.prototype.slice.call(F,0,Y);J[Y]=F[Y]}}return G.concat(J||Array.prototype.slice.call(F))},$s=function(G){return this instanceof $s?(this.v=G,this):new $s(G)},v52=function(G,F,I){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var Y=I.apply(G,F||[]),W,J=[];return W=Object.create((typeof AsyncIterator==="function"?AsyncIterator:Object).prototype),V("next"),V("throw"),V("return",X),W[Symbol.asyncIterator]=function(){return this},W;function X(L){return function(N){return Promise.resolve(N).then(L,z)}}function V(L,N){if(Y[L]){if(W[L]=function(O){return new Promise(function(R,T){J.push([L,O,R,T])>1||C(L,O)})},N)W[L]=N(W[L])}}function C(L,N){try{K(Y[L](N))}catch(O){$(J[0][3],O)}}function K(L){L.value instanceof $s?Promise.resolve(L.value.v).then(H,z):$(J[0][2],L)}function H(L){C("next",L)}function z(L){C("throw",L)}function $(L,N){if(L(N),J.shift(),J.length)C(J[0][0],J[0][1])}},b52=function(G){var F,I;return F={},Y("next"),Y("throw",function(W){throw W}),Y("return"),F[Symbol.iterator]=function(){return this},F;function Y(W,J){F[W]=G[W]?function(X){return(I=!I)?{value:$s(G[W](X)),done:!1}:J?J(X):X}:J}},f52=function(G){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var F=G[Symbol.asyncIterator],I;return F?F.call(G):(G=typeof hq1==="function"?hq1(G):G[Symbol.iterator](),I={},Y("next"),Y("throw"),Y("return"),I[Symbol.asyncIterator]=function(){return this},I);function Y(J){I[J]=G[J]&&function(X){return new Promise(function(V,C){X=G[J](X),W(V,C,X.done,X.value)})}}function W(J,X,V,C){Promise.resolve(C).then(function(K){J({value:K,done:V})},X)}},h52=function(G,F){if(Object.defineProperty)Object.defineProperty(G,"raw",{value:F});else G.raw=F;return G};var Q=Object.create?function(G,F){Object.defineProperty(G,"default",{enumerable:!0,value:F})}:function(G,F){G.default=F},D=function(G){return D=Object.getOwnPropertyNames||function(F){var I=[];for(var Y in F)if(Object.prototype.hasOwnProperty.call(F,Y))I[I.length]=Y;return I},D(G)};g52=function(G){if(G&&G.__esModule)return G;var F={};if(G!=null){for(var I=D(G),Y=0;Y<I.length;Y++)if(I[Y]!=="default")gq1(F,G,I[Y])}return Q(F,G),F},u52=function(G){return G&&G.__esModule?G:{default:G}},m52=function(G,F,I,Y){if(I==="a"&&!Y)throw new TypeError("Private accessor was defined without a getter");if(typeof F==="function"?G!==F||!Y:!F.has(G))throw new TypeError("Cannot read private member from an object whose class did not declare it");return I==="m"?Y:I==="a"?Y.call(G):Y?Y.value:F.get(G)},d52=function(G,F,I,Y,W){if(Y==="m")throw new TypeError("Private method is not writable");if(Y==="a"&&!W)throw new TypeError("Private accessor was defined without a setter");if(typeof F==="function"?G!==F||!W:!F.has(G))throw new TypeError("Cannot write private member to an object whose class did not declare it");return Y==="a"?W.call(G,I):W?W.value=I:F.set(G,I),I},c52=function(G,F){if(F===null||typeof F!=="object"&&typeof F!=="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof G==="function"?F===G:G.has(F)},l52=function(G,F,I){if(F!==null&&F!==void 0){if(typeof F!=="object"&&typeof F!=="function")throw new TypeError("Object expected.");var Y,W;if(I){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");Y=F[Symbol.asyncDispose]}if(Y===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");if(Y=F[Symbol.dispose],I)W=Y}if(typeof Y!=="function")throw new TypeError("Object not disposable.");if(W)Y=function(){try{W.call(this)}catch(J){return Promise.reject(J)}};G.stack.push({value:F,dispose:Y,async:I})}else if(I)G.stack.push({async:!0});return F};var Z=typeof SuppressedError==="function"?SuppressedError:function(G,F,I){var Y=new Error(I);return Y.name="SuppressedError",Y.error=G,Y.suppressed=F,Y};p52=function(G){function F(J){G.error=G.hasError?new Z(J,G.error,"An error was suppressed during disposal."):J,G.hasError=!0}var I,Y=0;function W(){while(I=G.stack.pop())try{if(!I.async&&Y===1)return Y=0,G.stack.push(I),Promise.resolve().then(W);if(I.dispose){var J=I.dispose.call(I.value);if(I.async)return Y|=2,Promise.resolve(J).then(W,function(X){return F(X),W()})}else Y|=1}catch(X){F(X)}if(Y===1)return G.hasError?Promise.reject(G.error):Promise.resolve();if(G.hasError)throw G.error}return W()},i52=function(G,F){if(typeof G==="string"&&/^\.\.?\//.test(G))return G.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,function(I,Y,W,J,X){return Y?F?".jsx":".js":W&&(!J||!X)?I:W+J+"."+X.toLowerCase()+"js"});return G},A("__extends",w52),A("__assign",$52),A("__rest",q52),A("__decorate",N52),A("__param",L52),A("__esDecorate",M52),A("__runInitializers",R52),A("__propKey",O52),A("__setFunctionName",T52),A("__metadata",P52),A("__awaiter",S52),A("__generator",j52),A("__exportStar",y52),A("__createBinding",gq1),A("__values",hq1),A("__read",d80),A("__spread",k52),A("__spreadArrays",_52),A("__spreadArray",x52),A("__await",$s),A("__asyncGenerator",v52),A("__asyncDelegator",b52),A("__asyncValues",f52),A("__makeTemplateObject",h52),A("__importStar",g52),A("__importDefault",u52),A("__classPrivateFieldGet",m52),A("__classPrivateFieldSet",d52),A("__classPrivateFieldIn",c52),A("__addDisposableResource",l52),A("__disposeResources",p52),A("__rewriteRelativeImportExtension",i52)})});
var n62=E((p62)=>{Object.defineProperty(p62,"__esModule",{value:!0});p62.getCredentials=p62.createGetRequest=void 0;var f80=Q9(),$K4=R62(),qK4=l62(),NK4=ry();function LK4(A){return new $K4.HttpRequest({protocol:A.protocol,hostname:A.hostname,port:Number(A.port),path:A.pathname,query:Array.from(A.searchParams.entries()).reduce((B,[Q,D])=>{return B[Q]=D,B},{}),fragment:A.hash})}p62.createGetRequest=LK4;async function MK4(A,B){let D=await NK4.sdkStreamMixin(A.body).transformToString();if(A.statusCode===200){let Z=JSON.parse(D);if(typeof Z.AccessKeyId!=="string"||typeof Z.SecretAccessKey!=="string"||typeof Z.Token!=="string"||typeof Z.Expiration!=="string")throw new f80.CredentialsProviderError("HTTP credential provider response not of the required format, an object matching: { AccessKeyId: string, SecretAccessKey: string, Token: string, Expiration: string(rfc3339) }",{logger:B});return{accessKeyId:Z.AccessKeyId,secretAccessKey:Z.SecretAccessKey,sessionToken:Z.Token,expiration:qK4.parseRfc3339DateTime(Z.Expiration)}}if(A.statusCode>=400&&A.statusCode<500){let Z={};try{Z=JSON.parse(D)}catch(G){}throw Object.assign(new f80.CredentialsProviderError(`Server responded with status: ${A.statusCode}`,{logger:B}),{Code:Z.Code,Message:Z.Message})}throw new f80.CredentialsProviderError(`Server responded with status: ${A.statusCode}`,{logger:B})}p62.getCredentials=MK4});
var oCB=E((sCB)=>{Object.defineProperty(sCB,"__esModule",{value:!0});sCB.getRuntimeConfig=void 0;var Pn6=CXB(),Sn6=Pn6.__importDefault(KXB()),jn6=KI(),yn6=C70(),kn6=PVB(),iCB=N61(),C_1=K4(),_n6=gVB(),xn6=gG(),nCB=u4(),xe=JD(),aCB=k3(),vn6=uG(),bn6=sZ(),fn6=pCB(),hn6=AZ1(),gn6=mG(),un6=AZ1(),mn6=(A)=>{un6.emitWarningIfUnsupportedVersion(process.version);let B=gn6.resolveDefaultsModeConfig(A),Q=()=>B().then(hn6.loadConfigsForDefaultMode),D=fn6.getRuntimeConfig(A);jn6.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile};return{...D,...A,runtime:"node",defaultsMode:B,bodyLengthChecker:A?.bodyLengthChecker??vn6.calculateBodyLength,credentialDefaultProvider:A?.credentialDefaultProvider??yn6.defaultProvider,defaultUserAgentProvider:A?.defaultUserAgentProvider??iCB.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:Sn6.default.version}),eventStreamPayloadHandlerProvider:A?.eventStreamPayloadHandlerProvider??kn6.eventStreamPayloadHandlerProvider,eventStreamSerdeProvider:A?.eventStreamSerdeProvider??_n6.eventStreamSerdeProvider,maxAttempts:A?.maxAttempts??xe.loadConfig(nCB.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??xe.loadConfig(C_1.NODE_REGION_CONFIG_OPTIONS,{...C_1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:aCB.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??xe.loadConfig({...nCB.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||bn6.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??xn6.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??aCB.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??xe.loadConfig(C_1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??xe.loadConfig(C_1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??xe.loadConfig(iCB.NODE_APP_ID_CONFIG_OPTIONS,Z)}};sCB.getRuntimeConfig=mn6});
var pCB=E((cCB)=>{Object.defineProperty(cCB,"__esModule",{value:!0});cCB.getRuntimeConfig=void 0;var Nn6=KI(),Ln6=AZ1(),Mn6=JZ(),mCB=qCB(),dCB=cB(),Rn6=dz0(),On6=uCB(),Tn6=(A)=>{return{apiVersion:"2023-09-30",base64Decoder:A?.base64Decoder??mCB.fromBase64,base64Encoder:A?.base64Encoder??mCB.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??On6.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??Rn6.defaultBedrockRuntimeHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new Nn6.AwsSdkSigV4Signer}],logger:A?.logger??new Ln6.NoOpLogger,serviceId:A?.serviceId??"Bedrock Runtime",urlParser:A?.urlParser??Mn6.parseUrl,utf8Decoder:A?.utf8Decoder??dCB.fromUtf8,utf8Encoder:A?.utf8Encoder??dCB.toUtf8}};cCB.getRuntimeConfig=Tn6});
var pW2=E((cW2)=>{Object.defineProperty(cW2,"__esModule",{value:!0});cW2.fromTokenFile=void 0;var DS4=zL(),ZS4=Q9(),GS4=J1("fs"),FS4=F70(),dW2="AWS_WEB_IDENTITY_TOKEN_FILE",IS4="AWS_ROLE_ARN",YS4="AWS_ROLE_SESSION_NAME",WS4=(A={})=>async()=>{A.logger?.debug("@aws-sdk/credential-provider-web-identity - fromTokenFile");let B=A?.webIdentityTokenFile??process.env[dW2],Q=A?.roleArn??process.env[IS4],D=A?.roleSessionName??process.env[YS4];if(!B||!Q)throw new ZS4.CredentialsProviderError("Web identity configuration not specified",{logger:A.logger});let Z=await FS4.fromWebToken({...A,webIdentityToken:GS4.readFileSync(B,{encoding:"ascii"}),roleArn:Q,roleSessionName:D})();if(B===process.env[dW2])DS4.setCredentialFeature(Z,"CREDENTIALS_ENV_VARS_STS_WEB_ID_TOKEN","h");return Z};cW2.fromTokenFile=WS4});
var pXB=E((GA3,lXB)=>{var{defineProperty:rk1,getOwnPropertyDescriptor:tc6,getOwnPropertyNames:ec6}=Object,Al6=Object.prototype.hasOwnProperty,Bl6=(A,B)=>rk1(A,"name",{value:B,configurable:!0}),Ql6=(A,B)=>{for(var Q in B)rk1(A,Q,{get:B[Q],enumerable:!0})},Dl6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of ec6(B))if(!Al6.call(A,Z)&&Z!==Q)rk1(A,Z,{get:()=>B[Z],enumerable:!(D=tc6(B,Z))||D.enumerable})}return A},Zl6=(A)=>Dl6(rk1({},"__esModule",{value:!0}),A),cXB={};Ql6(cXB,{isArrayBuffer:()=>Gl6});lXB.exports=Zl6(cXB);var Gl6=Bl6((A)=>typeof ArrayBuffer==="function"&&A instanceof ArrayBuffer||Object.prototype.toString.call(A)==="[object ArrayBuffer]","isArrayBuffer")});
var pY2=E((cY2)=>{Object.defineProperty(cY2,"__esModule",{value:!0});cY2.getRuntimeConfig=void 0;var ET4=Z30(),UT4=ET4.__importDefault(G30()),gY2=KI(),uY2=N61(),pN1=K4(),wT4=CB(),$T4=gG(),mY2=u4(),is=JD(),dY2=k3(),qT4=uG(),NT4=sZ(),LT4=hY2(),MT4=XZ(),RT4=mG(),OT4=XZ(),TT4=(A)=>{OT4.emitWarningIfUnsupportedVersion(process.version);let B=RT4.resolveDefaultsModeConfig(A),Q=()=>B().then(MT4.loadConfigsForDefaultMode),D=LT4.getRuntimeConfig(A);gY2.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile};return{...D,...A,runtime:"node",defaultsMode:B,bodyLengthChecker:A?.bodyLengthChecker??qT4.calculateBodyLength,defaultUserAgentProvider:A?.defaultUserAgentProvider??uY2.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:UT4.default.version}),httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(G)=>G.getIdentityProvider("aws.auth#sigv4")||(async(F)=>await A.credentialDefaultProvider(F?.__config||{})()),signer:new gY2.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(G)=>G.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new wT4.NoAuthSigner}],maxAttempts:A?.maxAttempts??is.loadConfig(mY2.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??is.loadConfig(pN1.NODE_REGION_CONFIG_OPTIONS,{...pN1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:dY2.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??is.loadConfig({...mY2.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||NT4.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??$T4.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??dY2.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??is.loadConfig(pN1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??is.loadConfig(pN1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??is.loadConfig(uY2.NODE_APP_ID_CONFIG_OPTIONS,Z)}};cY2.getRuntimeConfig=TT4});
var pz0=E((ZA3,sk1)=>{var HXB,zXB,EXB,UXB,wXB,$XB,qXB,NXB,LXB,MXB,RXB,OXB,TXB,nk1,lz0,PXB,SXB,jXB,Oe,yXB,kXB,_XB,xXB,vXB,bXB,fXB,hXB,gXB,ak1,uXB,mXB,dXB;(function(A){var B=typeof global==="object"?global:typeof self==="object"?self:typeof this==="object"?this:{};if(typeof define==="function"&&define.amd)define("tslib",["exports"],function(D){A(Q(B,Q(D)))});else if(typeof sk1==="object"&&typeof ZA3==="object")A(Q(B,Q(ZA3)));else A(Q(B));function Q(D,Z){if(D!==B)if(typeof Object.create==="function")Object.defineProperty(D,"__esModule",{value:!0});else D.__esModule=!0;return function(G,F){return D[G]=Z?Z(G,F):F}}})(function(A){var B=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(G,F){G.__proto__=F}||function(G,F){for(var I in F)if(Object.prototype.hasOwnProperty.call(F,I))G[I]=F[I]};HXB=function(G,F){if(typeof F!=="function"&&F!==null)throw new TypeError("Class extends value "+String(F)+" is not a constructor or null");B(G,F);function I(){this.constructor=G}G.prototype=F===null?Object.create(F):(I.prototype=F.prototype,new I)},zXB=Object.assign||function(G){for(var F,I=1,Y=arguments.length;I<Y;I++){F=arguments[I];for(var W in F)if(Object.prototype.hasOwnProperty.call(F,W))G[W]=F[W]}return G},EXB=function(G,F){var I={};for(var Y in G)if(Object.prototype.hasOwnProperty.call(G,Y)&&F.indexOf(Y)<0)I[Y]=G[Y];if(G!=null&&typeof Object.getOwnPropertySymbols==="function"){for(var W=0,Y=Object.getOwnPropertySymbols(G);W<Y.length;W++)if(F.indexOf(Y[W])<0&&Object.prototype.propertyIsEnumerable.call(G,Y[W]))I[Y[W]]=G[Y[W]]}return I},UXB=function(G,F,I,Y){var W=arguments.length,J=W<3?F:Y===null?Y=Object.getOwnPropertyDescriptor(F,I):Y,X;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")J=Reflect.decorate(G,F,I,Y);else for(var V=G.length-1;V>=0;V--)if(X=G[V])J=(W<3?X(J):W>3?X(F,I,J):X(F,I))||J;return W>3&&J&&Object.defineProperty(F,I,J),J},wXB=function(G,F){return function(I,Y){F(I,Y,G)}},$XB=function(G,F,I,Y,W,J){function X(T){if(T!==void 0&&typeof T!=="function")throw new TypeError("Function expected");return T}var V=Y.kind,C=V==="getter"?"get":V==="setter"?"set":"value",K=!F&&G?Y.static?G:G.prototype:null,H=F||(K?Object.getOwnPropertyDescriptor(K,Y.name):{}),z,$=!1;for(var L=I.length-1;L>=0;L--){var N={};for(var O in Y)N[O]=O==="access"?{}:Y[O];for(var O in Y.access)N.access[O]=Y.access[O];N.addInitializer=function(T){if($)throw new TypeError("Cannot add initializers after decoration has completed");J.push(X(T||null))};var R=I[L](V==="accessor"?{get:H.get,set:H.set}:H[C],N);if(V==="accessor"){if(R===void 0)continue;if(R===null||typeof R!=="object")throw new TypeError("Object expected");if(z=X(R.get))H.get=z;if(z=X(R.set))H.set=z;if(z=X(R.init))W.unshift(z)}else if(z=X(R))if(V==="field")W.unshift(z);else H[C]=z}if(K)Object.defineProperty(K,Y.name,H);$=!0},qXB=function(G,F,I){var Y=arguments.length>2;for(var W=0;W<F.length;W++)I=Y?F[W].call(G,I):F[W].call(G);return Y?I:void 0},NXB=function(G){return typeof G==="symbol"?G:"".concat(G)},LXB=function(G,F,I){if(typeof F==="symbol")F=F.description?"[".concat(F.description,"]"):"";return Object.defineProperty(G,"name",{configurable:!0,value:I?"".concat(I," ",F):F})},MXB=function(G,F){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(G,F)},RXB=function(G,F,I,Y){function W(J){return J instanceof I?J:new I(function(X){X(J)})}return new(I||(I=Promise))(function(J,X){function V(H){try{K(Y.next(H))}catch(z){X(z)}}function C(H){try{K(Y.throw(H))}catch(z){X(z)}}function K(H){H.done?J(H.value):W(H.value).then(V,C)}K((Y=Y.apply(G,F||[])).next())})},OXB=function(G,F){var I={label:0,sent:function(){if(J[0]&1)throw J[1];return J[1]},trys:[],ops:[]},Y,W,J,X=Object.create((typeof Iterator==="function"?Iterator:Object).prototype);return X.next=V(0),X.throw=V(1),X.return=V(2),typeof Symbol==="function"&&(X[Symbol.iterator]=function(){return this}),X;function V(K){return function(H){return C([K,H])}}function C(K){if(Y)throw new TypeError("Generator is already executing.");while(X&&(X=0,K[0]&&(I=0)),I)try{if(Y=1,W&&(J=K[0]&2?W.return:K[0]?W.throw||((J=W.return)&&J.call(W),0):W.next)&&!(J=J.call(W,K[1])).done)return J;if(W=0,J)K=[K[0]&2,J.value];switch(K[0]){case 0:case 1:J=K;break;case 4:return I.label++,{value:K[1],done:!1};case 5:I.label++,W=K[1],K=[0];continue;case 7:K=I.ops.pop(),I.trys.pop();continue;default:if((J=I.trys,!(J=J.length>0&&J[J.length-1]))&&(K[0]===6||K[0]===2)){I=0;continue}if(K[0]===3&&(!J||K[1]>J[0]&&K[1]<J[3])){I.label=K[1];break}if(K[0]===6&&I.label<J[1]){I.label=J[1],J=K;break}if(J&&I.label<J[2]){I.label=J[2],I.ops.push(K);break}if(J[2])I.ops.pop();I.trys.pop();continue}K=F.call(G,I)}catch(H){K=[6,H],W=0}finally{Y=J=0}if(K[0]&5)throw K[1];return{value:K[0]?K[1]:void 0,done:!0}}},TXB=function(G,F){for(var I in G)if(I!=="default"&&!Object.prototype.hasOwnProperty.call(F,I))ak1(F,G,I)},ak1=Object.create?function(G,F,I,Y){if(Y===void 0)Y=I;var W=Object.getOwnPropertyDescriptor(F,I);if(!W||("get"in W?!F.__esModule:W.writable||W.configurable))W={enumerable:!0,get:function(){return F[I]}};Object.defineProperty(G,Y,W)}:function(G,F,I,Y){if(Y===void 0)Y=I;G[Y]=F[I]},nk1=function(G){var F=typeof Symbol==="function"&&Symbol.iterator,I=F&&G[F],Y=0;if(I)return I.call(G);if(G&&typeof G.length==="number")return{next:function(){if(G&&Y>=G.length)G=void 0;return{value:G&&G[Y++],done:!G}}};throw new TypeError(F?"Object is not iterable.":"Symbol.iterator is not defined.")},lz0=function(G,F){var I=typeof Symbol==="function"&&G[Symbol.iterator];if(!I)return G;var Y=I.call(G),W,J=[],X;try{while((F===void 0||F-- >0)&&!(W=Y.next()).done)J.push(W.value)}catch(V){X={error:V}}finally{try{if(W&&!W.done&&(I=Y.return))I.call(Y)}finally{if(X)throw X.error}}return J},PXB=function(){for(var G=[],F=0;F<arguments.length;F++)G=G.concat(lz0(arguments[F]));return G},SXB=function(){for(var G=0,F=0,I=arguments.length;F<I;F++)G+=arguments[F].length;for(var Y=Array(G),W=0,F=0;F<I;F++)for(var J=arguments[F],X=0,V=J.length;X<V;X++,W++)Y[W]=J[X];return Y},jXB=function(G,F,I){if(I||arguments.length===2){for(var Y=0,W=F.length,J;Y<W;Y++)if(J||!(Y in F)){if(!J)J=Array.prototype.slice.call(F,0,Y);J[Y]=F[Y]}}return G.concat(J||Array.prototype.slice.call(F))},Oe=function(G){return this instanceof Oe?(this.v=G,this):new Oe(G)},yXB=function(G,F,I){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var Y=I.apply(G,F||[]),W,J=[];return W=Object.create((typeof AsyncIterator==="function"?AsyncIterator:Object).prototype),V("next"),V("throw"),V("return",X),W[Symbol.asyncIterator]=function(){return this},W;function X(L){return function(N){return Promise.resolve(N).then(L,z)}}function V(L,N){if(Y[L]){if(W[L]=function(O){return new Promise(function(R,T){J.push([L,O,R,T])>1||C(L,O)})},N)W[L]=N(W[L])}}function C(L,N){try{K(Y[L](N))}catch(O){$(J[0][3],O)}}function K(L){L.value instanceof Oe?Promise.resolve(L.value.v).then(H,z):$(J[0][2],L)}function H(L){C("next",L)}function z(L){C("throw",L)}function $(L,N){if(L(N),J.shift(),J.length)C(J[0][0],J[0][1])}},kXB=function(G){var F,I;return F={},Y("next"),Y("throw",function(W){throw W}),Y("return"),F[Symbol.iterator]=function(){return this},F;function Y(W,J){F[W]=G[W]?function(X){return(I=!I)?{value:Oe(G[W](X)),done:!1}:J?J(X):X}:J}},_XB=function(G){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var F=G[Symbol.asyncIterator],I;return F?F.call(G):(G=typeof nk1==="function"?nk1(G):G[Symbol.iterator](),I={},Y("next"),Y("throw"),Y("return"),I[Symbol.asyncIterator]=function(){return this},I);function Y(J){I[J]=G[J]&&function(X){return new Promise(function(V,C){X=G[J](X),W(V,C,X.done,X.value)})}}function W(J,X,V,C){Promise.resolve(C).then(function(K){J({value:K,done:V})},X)}},xXB=function(G,F){if(Object.defineProperty)Object.defineProperty(G,"raw",{value:F});else G.raw=F;return G};var Q=Object.create?function(G,F){Object.defineProperty(G,"default",{enumerable:!0,value:F})}:function(G,F){G.default=F},D=function(G){return D=Object.getOwnPropertyNames||function(F){var I=[];for(var Y in F)if(Object.prototype.hasOwnProperty.call(F,Y))I[I.length]=Y;return I},D(G)};vXB=function(G){if(G&&G.__esModule)return G;var F={};if(G!=null){for(var I=D(G),Y=0;Y<I.length;Y++)if(I[Y]!=="default")ak1(F,G,I[Y])}return Q(F,G),F},bXB=function(G){return G&&G.__esModule?G:{default:G}},fXB=function(G,F,I,Y){if(I==="a"&&!Y)throw new TypeError("Private accessor was defined without a getter");if(typeof F==="function"?G!==F||!Y:!F.has(G))throw new TypeError("Cannot read private member from an object whose class did not declare it");return I==="m"?Y:I==="a"?Y.call(G):Y?Y.value:F.get(G)},hXB=function(G,F,I,Y,W){if(Y==="m")throw new TypeError("Private method is not writable");if(Y==="a"&&!W)throw new TypeError("Private accessor was defined without a setter");if(typeof F==="function"?G!==F||!W:!F.has(G))throw new TypeError("Cannot write private member to an object whose class did not declare it");return Y==="a"?W.call(G,I):W?W.value=I:F.set(G,I),I},gXB=function(G,F){if(F===null||typeof F!=="object"&&typeof F!=="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof G==="function"?F===G:G.has(F)},uXB=function(G,F,I){if(F!==null&&F!==void 0){if(typeof F!=="object"&&typeof F!=="function")throw new TypeError("Object expected.");var Y,W;if(I){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");Y=F[Symbol.asyncDispose]}if(Y===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");if(Y=F[Symbol.dispose],I)W=Y}if(typeof Y!=="function")throw new TypeError("Object not disposable.");if(W)Y=function(){try{W.call(this)}catch(J){return Promise.reject(J)}};G.stack.push({value:F,dispose:Y,async:I})}else if(I)G.stack.push({async:!0});return F};var Z=typeof SuppressedError==="function"?SuppressedError:function(G,F,I){var Y=new Error(I);return Y.name="SuppressedError",Y.error=G,Y.suppressed=F,Y};mXB=function(G){function F(J){G.error=G.hasError?new Z(J,G.error,"An error was suppressed during disposal."):J,G.hasError=!0}var I,Y=0;function W(){while(I=G.stack.pop())try{if(!I.async&&Y===1)return Y=0,G.stack.push(I),Promise.resolve().then(W);if(I.dispose){var J=I.dispose.call(I.value);if(I.async)return Y|=2,Promise.resolve(J).then(W,function(X){return F(X),W()})}else Y|=1}catch(X){F(X)}if(Y===1)return G.hasError?Promise.reject(G.error):Promise.resolve();if(G.hasError)throw G.error}return W()},dXB=function(G,F){if(typeof G==="string"&&/^\.\.?\//.test(G))return G.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,function(I,Y,W,J,X){return Y?F?".jsx":".js":W&&(!J||!X)?I:W+J+"."+X.toLowerCase()+"js"});return G},A("__extends",HXB),A("__assign",zXB),A("__rest",EXB),A("__decorate",UXB),A("__param",wXB),A("__esDecorate",$XB),A("__runInitializers",qXB),A("__propKey",NXB),A("__setFunctionName",LXB),A("__metadata",MXB),A("__awaiter",RXB),A("__generator",OXB),A("__exportStar",TXB),A("__createBinding",ak1),A("__values",nk1),A("__read",lz0),A("__spread",PXB),A("__spreadArrays",SXB),A("__spreadArray",jXB),A("__await",Oe),A("__asyncGenerator",yXB),A("__asyncDelegator",kXB),A("__asyncValues",_XB),A("__makeTemplateObject",xXB),A("__importStar",vXB),A("__importDefault",bXB),A("__classPrivateFieldGet",fXB),A("__classPrivateFieldSet",hXB),A("__classPrivateFieldIn",gXB),A("__addDisposableResource",uXB),A("__disposeResources",mXB),A("__rewriteRelativeImportExtension",dXB)})});
var qCB=E((mA3,V_1)=>{var{defineProperty:wCB,getOwnPropertyDescriptor:Cn6,getOwnPropertyNames:Kn6}=Object,Hn6=Object.prototype.hasOwnProperty,CE0=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Kn6(B))if(!Hn6.call(A,Z)&&Z!==Q)wCB(A,Z,{get:()=>B[Z],enumerable:!(D=Cn6(B,Z))||D.enumerable})}return A},$CB=(A,B,Q)=>(CE0(A,B,"default"),Q&&CE0(Q,B,"default")),zn6=(A)=>CE0(wCB({},"__esModule",{value:!0}),A),KE0={};V_1.exports=zn6(KE0);$CB(KE0,HCB(),V_1.exports);$CB(KE0,UCB(),V_1.exports)});
var qZ2=E((wZ2)=>{Object.defineProperty(wZ2,"__esModule",{value:!0});wZ2.defaultEndpointResolver=void 0;var aq4=ws(),g50=S7(),sq4=UZ2(),rq4=new g50.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),oq4=(A,B={})=>{return rq4.get(A,()=>g50.resolveEndpoint(sq4.ruleSet,{endpointParams:A,logger:B.logger}))};wZ2.defaultEndpointResolver=oq4;g50.customEndpointFunctions.aws=aq4.awsEndpointFunctions});
var r62=E((a62)=>{Object.defineProperty(a62,"__esModule",{value:!0});a62.retryWrapper=void 0;var OK4=(A,B,Q)=>{return async()=>{for(let D=0;D<B;++D)try{return await A()}catch(Z){await new Promise((G)=>setTimeout(G,Q))}return await A()}};a62.retryWrapper=OK4});
var sD2=E((nD2)=>{Object.defineProperty(nD2,"__esModule",{value:!0});nD2.fromBase64=void 0;var fq4=YD(),hq4=/^[A-Za-z0-9+/]*={0,2}$/,gq4=(A)=>{if(A.length*3%4!==0)throw new TypeError("Incorrect padding on base64 string.");if(!hq4.exec(A))throw new TypeError("Invalid base64 string.");let B=fq4.fromString(A,"base64");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength)};nD2.fromBase64=gq4});
var sF2=E((nF2)=>{Object.defineProperty(nF2,"__esModule",{value:!0});nF2.defaultEndpointResolver=void 0;var FR4=ws(),X30=S7(),IR4=iF2(),YR4=new X30.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),WR4=(A,B={})=>{return YR4.get(A,()=>X30.resolveEndpoint(IR4.ruleSet,{endpointParams:A,logger:B.logger}))};nF2.defaultEndpointResolver=WR4;X30.customEndpointFunctions.aws=FR4.awsEndpointFunctions});
var sXB=E((FA3,aXB)=>{var{defineProperty:ok1,getOwnPropertyDescriptor:Fl6,getOwnPropertyNames:Il6}=Object,Yl6=Object.prototype.hasOwnProperty,iXB=(A,B)=>ok1(A,"name",{value:B,configurable:!0}),Wl6=(A,B)=>{for(var Q in B)ok1(A,Q,{get:B[Q],enumerable:!0})},Jl6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Il6(B))if(!Yl6.call(A,Z)&&Z!==Q)ok1(A,Z,{get:()=>B[Z],enumerable:!(D=Fl6(B,Z))||D.enumerable})}return A},Xl6=(A)=>Jl6(ok1({},"__esModule",{value:!0}),A),nXB={};Wl6(nXB,{fromArrayBuffer:()=>Cl6,fromString:()=>Kl6});aXB.exports=Xl6(nXB);var Vl6=pXB(),iz0=J1("buffer"),Cl6=iXB((A,B=0,Q=A.byteLength-B)=>{if(!Vl6.isArrayBuffer(A))throw new TypeError(`The "input" argument must be ArrayBuffer. Received type ${typeof A} (${A})`);return iz0.Buffer.from(A,B,Q)},"fromArrayBuffer"),Kl6=iXB((A,B)=>{if(typeof A!=="string")throw new TypeError(`The "input" argument must be of type string. Received type ${typeof A} (${A})`);return B?iz0.Buffer.from(A,B):iz0.Buffer.from(A)},"fromString")});
var t32=E((iz5,o32)=>{var{defineProperty:eq1,getOwnPropertyDescriptor:lE4,getOwnPropertyNames:pE4}=Object,iE4=Object.prototype.hasOwnProperty,iY=(A,B)=>eq1(A,"name",{value:B,configurable:!0}),nE4=(A,B)=>{for(var Q in B)eq1(A,Q,{get:B[Q],enumerable:!0})},aE4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of pE4(B))if(!iE4.call(A,Z)&&Z!==Q)eq1(A,Z,{get:()=>B[Z],enumerable:!(D=lE4(B,Z))||D.enumerable})}return A},sE4=(A)=>aE4(eq1({},"__esModule",{value:!0}),A),n32={};nE4(n32,{AWSSDKSigV4Signer:()=>eE4,AwsSdkSigV4ASigner:()=>BU4,AwsSdkSigV4Signer:()=>Q50,NODE_SIGV4A_CONFIG_OPTIONS:()=>ZU4,resolveAWSSDKSigV4Config:()=>FU4,resolveAwsSdkSigV4AConfig:()=>DU4,resolveAwsSdkSigV4Config:()=>a32,validateSigningProperties:()=>B50});o32.exports=sE4(n32);var rE4=H61(),oE4=H61(),c32=iY((A)=>oE4.HttpResponse.isInstance(A)?A.headers?.date??A.headers?.Date:void 0,"getDateHeader"),A50=iY((A)=>new Date(Date.now()+A),"getSkewCorrectedDate"),tE4=iY((A,B)=>Math.abs(A50(B).getTime()-A)>=300000,"isClockSkewed"),l32=iY((A,B)=>{let Q=Date.parse(A);if(tE4(Q,B))return Q-Date.now();return B},"getUpdatedSystemClockOffset"),z61=iY((A,B)=>{if(!B)throw new Error(`Property \`${A}\` is not resolved for AWS SDK SigV4Auth`);return B},"throwSigningPropertyError"),B50=iY(async(A)=>{let B=z61("context",A.context),Q=z61("config",A.config),D=B.endpointV2?.properties?.authSchemes?.[0],G=await z61("signer",Q.signer)(D),F=A?.signingRegion,I=A?.signingRegionSet,Y=A?.signingName;return{config:Q,signer:G,signingRegion:F,signingRegionSet:I,signingName:Y}},"validateSigningProperties"),Q50=class{static{iY(this,"AwsSdkSigV4Signer")}async sign(A,B,Q){if(!rE4.HttpRequest.isInstance(A))throw new Error("The request is not an instance of `HttpRequest` and cannot be signed");let D=await B50(Q),{config:Z,signer:G}=D,{signingRegion:F,signingName:I}=D,Y=Q.context;if(Y?.authSchemes?.length??0>1){let[J,X]=Y.authSchemes;if(J?.name==="sigv4a"&&X?.name==="sigv4")F=X?.signingRegion??F,I=X?.signingName??I}return await G.sign(A,{signingDate:A50(Z.systemClockOffset),signingRegion:F,signingService:I})}errorHandler(A){return(B)=>{let Q=B.ServerTime??c32(B.$response);if(Q){let D=z61("config",A.config),Z=D.systemClockOffset;if(D.systemClockOffset=l32(Q,D.systemClockOffset),D.systemClockOffset!==Z&&B.$metadata)B.$metadata.clockSkewCorrected=!0}throw B}}successHandler(A,B){let Q=c32(A);if(Q){let D=z61("config",B.config);D.systemClockOffset=l32(Q,D.systemClockOffset)}}},eE4=Q50,AU4=H61(),BU4=class extends Q50{static{iY(this,"AwsSdkSigV4ASigner")}async sign(A,B,Q){if(!AU4.HttpRequest.isInstance(A))throw new Error("The request is not an instance of `HttpRequest` and cannot be signed");let{config:D,signer:Z,signingRegion:G,signingRegionSet:F,signingName:I}=await B50(Q),W=(await D.sigv4aSigningRegionSet?.()??F??[G]).join(",");return await Z.sign(A,{signingDate:A50(D.systemClockOffset),signingRegion:W,signingService:I})}},QU4=CB(),p32=Q9(),DU4=iY((A)=>{return A.sigv4aSigningRegionSet=QU4.normalizeProvider(A.sigv4aSigningRegionSet),A},"resolveAwsSdkSigV4AConfig"),ZU4={environmentVariableSelector(A){if(A.AWS_SIGV4A_SIGNING_REGION_SET)return A.AWS_SIGV4A_SIGNING_REGION_SET.split(",").map((B)=>B.trim());throw new p32.ProviderError("AWS_SIGV4A_SIGNING_REGION_SET not set in env.",{tryNextLink:!0})},configFileSelector(A){if(A.sigv4a_signing_region_set)return(A.sigv4a_signing_region_set??"").split(",").map((B)=>B.trim());throw new p32.ProviderError("sigv4a_signing_region_set not set in profile.",{tryNextLink:!0})},default:void 0},GU4=zL(),qg=CB(),i32=d32(),a32=iY((A)=>{let B=A.credentials,Q=!!A.credentials,D=void 0;Object.defineProperty(A,"credentials",{set(W){if(W&&W!==B&&W!==D)Q=!0;B=W;let J=s32(A,{credentials:B,credentialDefaultProvider:A.credentialDefaultProvider}),X=r32(A,J);if(Q&&!X.attributed)D=iY(async(V)=>X(V).then((C)=>GU4.setCredentialFeature(C,"CREDENTIALS_CODE","e")),"resolvedCredentials"),D.memoized=X.memoized,D.configBound=X.configBound,D.attributed=!0;else D=X},get(){return D},enumerable:!0,configurable:!0}),A.credentials=B;let{signingEscapePath:Z=!0,systemClockOffset:G=A.systemClockOffset||0,sha256:F}=A,I;if(A.signer)I=qg.normalizeProvider(A.signer);else if(A.regionInfoProvider)I=iY(()=>qg.normalizeProvider(A.region)().then(async(W)=>[await A.regionInfoProvider(W,{useFipsEndpoint:await A.useFipsEndpoint(),useDualstackEndpoint:await A.useDualstackEndpoint()})||{},W]).then(([W,J])=>{let{signingRegion:X,signingService:V}=W;A.signingRegion=A.signingRegion||X||J,A.signingName=A.signingName||V||A.serviceId;let C={...A,credentials:A.credentials,region:A.signingRegion,service:A.signingName,sha256:F,uriEscapePath:Z};return new(A.signerConstructor||i32.SignatureV4)(C)}),"signer");else I=iY(async(W)=>{W=Object.assign({},{name:"sigv4",signingName:A.signingName||A.defaultSigningName,signingRegion:await qg.normalizeProvider(A.region)(),properties:{}},W);let{signingRegion:J,signingName:X}=W;A.signingRegion=A.signingRegion||J,A.signingName=A.signingName||X||A.serviceId;let V={...A,credentials:A.credentials,region:A.signingRegion,service:A.signingName,sha256:F,uriEscapePath:Z};return new(A.signerConstructor||i32.SignatureV4)(V)},"signer");return Object.assign(A,{systemClockOffset:G,signingEscapePath:Z,signer:I})},"resolveAwsSdkSigV4Config"),FU4=a32;function s32(A,{credentials:B,credentialDefaultProvider:Q}){let D;if(B)if(!B?.memoized)D=qg.memoizeIdentityProvider(B,qg.isIdentityExpired,qg.doesIdentityRequireRefresh);else D=B;else if(Q)D=qg.normalizeProvider(Q(Object.assign({},A,{parentClientConfig:A})));else D=iY(async()=>{throw new Error("@aws-sdk/core::resolveAwsSdkSigV4Config - `credentials` not provided and no credentialDefaultProvider was configured.")},"credentialsProvider");return D.memoized=!0,D}iY(s32,"normalizeCredentialProvider");function r32(A,B){if(B.configBound)return B;let Q=iY(async(D)=>B({...D,callerClientConfig:A}),"fn");return Q.memoized=B.memoized,Q.configBound=!0,Q}iY(r32,"bindCallerConfig")});
var tD2=E((rD2)=>{Object.defineProperty(rD2,"__esModule",{value:!0});rD2.toBase64=void 0;var uq4=YD(),mq4=cB(),dq4=(A)=>{let B;if(typeof A==="string")B=mq4.fromUtf8(A);else B=A;if(typeof B!=="object"||typeof B.byteOffset!=="number"||typeof B.byteLength!=="number")throw new Error("@smithy/util-base64: toBase64 encoder function only accepts string | Uint8Array.");return uq4.fromArrayBuffer(B.buffer,B.byteOffset,B.byteLength).toString("base64")};rD2.toBase64=dq4});
var tz0=E((UA3,MVB)=>{var{defineProperty:B_1,getOwnPropertyDescriptor:ll6,getOwnPropertyNames:pl6}=Object,il6=Object.prototype.hasOwnProperty,pP=(A,B)=>B_1(A,"name",{value:B,configurable:!0}),nl6=(A,B)=>{for(var Q in B)B_1(A,Q,{get:B[Q],enumerable:!0})},al6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of pl6(B))if(!il6.call(A,Z)&&Z!==Q)B_1(A,Z,{get:()=>B[Z],enumerable:!(D=ll6(B,Z))||D.enumerable})}return A},sl6=(A)=>al6(B_1({},"__esModule",{value:!0}),A),$VB={};nl6($VB,{EventStreamCodec:()=>Yp6,HeaderMarshaller:()=>qVB,Int64:()=>A_1,MessageDecoderStream:()=>Wp6,MessageEncoderStream:()=>Jp6,SmithyMessageDecoderStream:()=>Xp6,SmithyMessageEncoderStream:()=>Vp6});MVB.exports=sl6($VB);var rl6=ek1(),Em=ay(),A_1=class A{constructor(B){if(this.bytes=B,B.byteLength!==8)throw new Error("Int64 buffers must be exactly 8 bytes")}static{pP(this,"Int64")}static fromNumber(B){if(B>9223372036854776000||B<-9223372036854776000)throw new Error(`${B} is too large (or, if negative, too small) to represent as an Int64`);let Q=new Uint8Array(8);for(let D=7,Z=Math.abs(Math.round(B));D>-1&&Z>0;D--,Z/=256)Q[D]=Z;if(B<0)oz0(Q);return new A(Q)}valueOf(){let B=this.bytes.slice(0),Q=B[0]&128;if(Q)oz0(B);return parseInt(Em.toHex(B),16)*(Q?-1:1)}toString(){return String(this.valueOf())}};function oz0(A){for(let B=0;B<8;B++)A[B]^=255;for(let B=7;B>-1;B--)if(A[B]++,A[B]!==0)break}pP(oz0,"negate");var qVB=class{constructor(A,B){this.toUtf8=A,this.fromUtf8=B}static{pP(this,"HeaderMarshaller")}format(A){let B=[];for(let Z of Object.keys(A)){let G=this.fromUtf8(Z);B.push(Uint8Array.from([G.byteLength]),G,this.formatHeaderValue(A[Z]))}let Q=new Uint8Array(B.reduce((Z,G)=>Z+G.byteLength,0)),D=0;for(let Z of B)Q.set(Z,D),D+=Z.byteLength;return Q}formatHeaderValue(A){switch(A.type){case"boolean":return Uint8Array.from([A.value?0:1]);case"byte":return Uint8Array.from([2,A.value]);case"short":let B=new DataView(new ArrayBuffer(3));return B.setUint8(0,3),B.setInt16(1,A.value,!1),new Uint8Array(B.buffer);case"integer":let Q=new DataView(new ArrayBuffer(5));return Q.setUint8(0,4),Q.setInt32(1,A.value,!1),new Uint8Array(Q.buffer);case"long":let D=new Uint8Array(9);return D[0]=5,D.set(A.value.bytes,1),D;case"binary":let Z=new DataView(new ArrayBuffer(3+A.value.byteLength));Z.setUint8(0,6),Z.setUint16(1,A.value.byteLength,!1);let G=new Uint8Array(Z.buffer);return G.set(A.value,3),G;case"string":let F=this.fromUtf8(A.value),I=new DataView(new ArrayBuffer(3+F.byteLength));I.setUint8(0,7),I.setUint16(1,F.byteLength,!1);let Y=new Uint8Array(I.buffer);return Y.set(F,3),Y;case"timestamp":let W=new Uint8Array(9);return W[0]=8,W.set(A_1.fromNumber(A.value.valueOf()).bytes,1),W;case"uuid":if(!Gp6.test(A.value))throw new Error(`Invalid UUID received: ${A.value}`);let J=new Uint8Array(17);return J[0]=9,J.set(Em.fromHex(A.value.replace(/\-/g,"")),1),J}}parse(A){let B={},Q=0;while(Q<A.byteLength){let D=A.getUint8(Q++),Z=this.toUtf8(new Uint8Array(A.buffer,A.byteOffset+Q,D));switch(Q+=D,A.getUint8(Q++)){case 0:B[Z]={type:wVB,value:!0};break;case 1:B[Z]={type:wVB,value:!1};break;case 2:B[Z]={type:ol6,value:A.getInt8(Q++)};break;case 3:B[Z]={type:tl6,value:A.getInt16(Q,!1)},Q+=2;break;case 4:B[Z]={type:el6,value:A.getInt32(Q,!1)},Q+=4;break;case 5:B[Z]={type:Ap6,value:new A_1(new Uint8Array(A.buffer,A.byteOffset+Q,8))},Q+=8;break;case 6:let G=A.getUint16(Q,!1);Q+=2,B[Z]={type:Bp6,value:new Uint8Array(A.buffer,A.byteOffset+Q,G)},Q+=G;break;case 7:let F=A.getUint16(Q,!1);Q+=2,B[Z]={type:Qp6,value:this.toUtf8(new Uint8Array(A.buffer,A.byteOffset+Q,F))},Q+=F;break;case 8:B[Z]={type:Dp6,value:new Date(new A_1(new Uint8Array(A.buffer,A.byteOffset+Q,8)).valueOf())},Q+=8;break;case 9:let I=new Uint8Array(A.buffer,A.byteOffset+Q,16);Q+=16,B[Z]={type:Zp6,value:`${Em.toHex(I.subarray(0,4))}-${Em.toHex(I.subarray(4,6))}-${Em.toHex(I.subarray(6,8))}-${Em.toHex(I.subarray(8,10))}-${Em.toHex(I.subarray(10))}`};break;default:throw new Error("Unrecognized header type tag")}}return B}},wVB="boolean",ol6="byte",tl6="short",el6="integer",Ap6="long",Bp6="binary",Qp6="string",Dp6="timestamp",Zp6="uuid",Gp6=/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/,Fp6=ek1(),NVB=4,ux=NVB*2,Um=4,Ip6=ux+Um*2;function LVB({byteLength:A,byteOffset:B,buffer:Q}){if(A<Ip6)throw new Error("Provided message too short to accommodate event stream message overhead");let D=new DataView(Q,B,A),Z=D.getUint32(0,!1);if(A!==Z)throw new Error("Reported message length does not match received message length");let G=D.getUint32(NVB,!1),F=D.getUint32(ux,!1),I=D.getUint32(A-Um,!1),Y=new Fp6.Crc32().update(new Uint8Array(Q,B,ux));if(F!==Y.digest())throw new Error(`The prelude checksum specified in the message (${F}) does not match the calculated CRC32 checksum (${Y.digest()})`);if(Y.update(new Uint8Array(Q,B+ux,A-(ux+Um))),I!==Y.digest())throw new Error(`The message checksum (${Y.digest()}) did not match the expected value of ${I}`);return{headers:new DataView(Q,B+ux+Um,G),body:new Uint8Array(Q,B+ux+Um+G,Z-G-(ux+Um+Um))}}pP(LVB,"splitMessage");var Yp6=class{static{pP(this,"EventStreamCodec")}constructor(A,B){this.headerMarshaller=new qVB(A,B),this.messageBuffer=[],this.isEndOfStream=!1}feed(A){this.messageBuffer.push(this.decode(A))}endOfStream(){this.isEndOfStream=!0}getMessage(){let A=this.messageBuffer.pop(),B=this.isEndOfStream;return{getMessage(){return A},isEndOfStream(){return B}}}getAvailableMessages(){let A=this.messageBuffer;this.messageBuffer=[];let B=this.isEndOfStream;return{getMessages(){return A},isEndOfStream(){return B}}}encode({headers:A,body:B}){let Q=this.headerMarshaller.format(A),D=Q.byteLength+B.byteLength+16,Z=new Uint8Array(D),G=new DataView(Z.buffer,Z.byteOffset,Z.byteLength),F=new rl6.Crc32;return G.setUint32(0,D,!1),G.setUint32(4,Q.byteLength,!1),G.setUint32(8,F.update(Z.subarray(0,8)).digest(),!1),Z.set(Q,12),Z.set(B,Q.byteLength+12),G.setUint32(D-4,F.update(Z.subarray(8,D-4)).digest(),!1),Z}decode(A){let{headers:B,body:Q}=LVB(A);return{headers:this.headerMarshaller.parse(B),body:Q}}formatHeaders(A){return this.headerMarshaller.format(A)}},Wp6=class{constructor(A){this.options=A}static{pP(this,"MessageDecoderStream")}[Symbol.asyncIterator](){return this.asyncIterator()}async*asyncIterator(){for await(let A of this.options.inputStream)yield this.options.decoder.decode(A)}},Jp6=class{constructor(A){this.options=A}static{pP(this,"MessageEncoderStream")}[Symbol.asyncIterator](){return this.asyncIterator()}async*asyncIterator(){for await(let A of this.options.messageStream)yield this.options.encoder.encode(A);if(this.options.includeEndFrame)yield new Uint8Array(0)}},Xp6=class{constructor(A){this.options=A}static{pP(this,"SmithyMessageDecoderStream")}[Symbol.asyncIterator](){return this.asyncIterator()}async*asyncIterator(){for await(let A of this.options.messageStream){let B=await this.options.deserializer(A);if(B===void 0)continue;yield B}}},Vp6=class{constructor(A){this.options=A}static{pP(this,"SmithyMessageEncoderStream")}[Symbol.asyncIterator](){return this.asyncIterator()}async*asyncIterator(){for await(let A of this.options.inputStream)yield this.options.serializer(A)}}});
var u61=E((R30)=>{Object.defineProperty(R30,"__esModule",{value:!0});R30.STSClient=R30.__Client=void 0;var QW2=V61(),kT4=C61(),_T4=K61(),DW2=Rs(),xT4=K4(),M30=CB(),vT4=bG(),bT4=R6(),ZW2=u4(),FW2=XZ();Object.defineProperty(R30,"__Client",{enumerable:!0,get:function(){return FW2.Client}});var GW2=w30(),fT4=m61(),hT4=pY2(),gT4=BW2();class IW2 extends FW2.Client{config;constructor(...[A]){let B=hT4.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=fT4.resolveClientEndpointParameters(B),D=DW2.resolveUserAgentConfig(Q),Z=ZW2.resolveRetryConfig(D),G=xT4.resolveRegionConfig(Z),F=QW2.resolveHostHeaderConfig(G),I=bT4.resolveEndpointConfig(F),Y=GW2.resolveHttpAuthSchemeConfig(I),W=gT4.resolveRuntimeExtensions(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(DW2.getUserAgentPlugin(this.config)),this.middlewareStack.use(ZW2.getRetryPlugin(this.config)),this.middlewareStack.use(vT4.getContentLengthPlugin(this.config)),this.middlewareStack.use(QW2.getHostHeaderPlugin(this.config)),this.middlewareStack.use(kT4.getLoggerPlugin(this.config)),this.middlewareStack.use(_T4.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(M30.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:GW2.defaultSTSHttpAuthSchemeParametersProvider,identityProviderConfigProvider:async(J)=>new M30.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials})})),this.middlewareStack.use(M30.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}}R30.STSClient=IW2});
var uCB=E((hCB)=>{Object.defineProperty(hCB,"__esModule",{value:!0});hCB.defaultEndpointResolver=void 0;var Un6=ws(),zE0=S7(),wn6=fCB(),$n6=new zE0.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),qn6=(A,B={})=>{return $n6.get(A,()=>zE0.resolveEndpoint(wn6.ruleSet,{endpointParams:A,logger:B.logger}))};hCB.defaultEndpointResolver=qn6;zE0.customEndpointFunctions.aws=Un6.awsEndpointFunctions});
var uN1=E((oE5,XI2)=>{var{defineProperty:gN1,getOwnPropertyDescriptor:PR4,getOwnPropertyNames:SR4}=Object,jR4=Object.prototype.hasOwnProperty,nk=(A,B)=>gN1(A,"name",{value:B,configurable:!0}),yR4=(A,B)=>{for(var Q in B)gN1(A,Q,{get:B[Q],enumerable:!0})},kR4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of SR4(B))if(!jR4.call(A,Z)&&Z!==Q)gN1(A,Z,{get:()=>B[Z],enumerable:!(D=PR4(B,Z))||D.enumerable})}return A},_R4=(A)=>kR4(gN1({},"__esModule",{value:!0}),A),II2={};yR4(II2,{Field:()=>bR4,Fields:()=>fR4,HttpRequest:()=>hR4,HttpResponse:()=>gR4,IHttpRequest:()=>YI2.HttpRequest,getHttpHandlerExtensionConfiguration:()=>xR4,isValidHostname:()=>JI2,resolveHttpHandlerRuntimeConfig:()=>vR4});XI2.exports=_R4(II2);var xR4=nk((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),vR4=nk((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),YI2=d50(),bR4=class{static{nk(this,"Field")}constructor({name:A,kind:B=YI2.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},fR4=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{nk(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},hR4=class A{static{nk(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=WI2(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function WI2(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}nk(WI2,"cloneQuery");var gR4=class{static{nk(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function JI2(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}nk(JI2,"isValidHostname")});
var w30=E((QY2)=>{Object.defineProperty(QY2,"__esModule",{value:!0});QY2.resolveHttpAuthSchemeConfig=QY2.resolveStsAuthConfig=QY2.defaultSTSHttpAuthSchemeProvider=QY2.defaultSTSHttpAuthSchemeParametersProvider=void 0;var nO4=KI(),U30=J5(),aO4=u61(),sO4=async(A,B,Q)=>{return{operation:U30.getSmithyContext(B).operation,region:await U30.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};QY2.defaultSTSHttpAuthSchemeParametersProvider=sO4;function rO4(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"sts",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function oO4(A){return{schemeId:"smithy.api#noAuth"}}var tO4=(A)=>{let B=[];switch(A.operation){case"AssumeRoleWithWebIdentity":{B.push(oO4(A));break}default:B.push(rO4(A))}return B};QY2.defaultSTSHttpAuthSchemeProvider=tO4;var eO4=(A)=>Object.assign(A,{stsClientCtor:aO4.STSClient});QY2.resolveStsAuthConfig=eO4;var AT4=(A)=>{let B=QY2.resolveStsAuthConfig(A),Q=nO4.resolveAwsSdkSigV4Config(B);return Object.assign(Q,{authSchemePreference:U30.normalizeProvider(A.authSchemePreference??[])})};QY2.resolveHttpAuthSchemeConfig=AT4});
var w50=E((v72)=>{Object.defineProperty(v72,"__esModule",{value:!0});v72.resolveHttpAuthSchemeConfig=v72.defaultSSOHttpAuthSchemeProvider=v72.defaultSSOHttpAuthSchemeParametersProvider=void 0;var rw4=KI(),U50=J5(),ow4=async(A,B,Q)=>{return{operation:U50.getSmithyContext(B).operation,region:await U50.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};v72.defaultSSOHttpAuthSchemeParametersProvider=ow4;function tw4(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"awsssoportal",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function IN1(A){return{schemeId:"smithy.api#noAuth"}}var ew4=(A)=>{let B=[];switch(A.operation){case"GetRoleCredentials":{B.push(IN1(A));break}case"ListAccountRoles":{B.push(IN1(A));break}case"ListAccounts":{B.push(IN1(A));break}case"Logout":{B.push(IN1(A));break}default:B.push(tw4(A))}return B};v72.defaultSSOHttpAuthSchemeProvider=ew4;var A$4=(A)=>{let B=rw4.resolveAwsSdkSigV4Config(A);return Object.assign(B,{authSchemePreference:U50.normalizeProvider(A.authSchemePreference??[])})};v72.resolveHttpAuthSchemeConfig=A$4});
var ws=E((Pz5,Q52)=>{var{defineProperty:xq1,getOwnPropertyDescriptor:Zz4,getOwnPropertyNames:Gz4}=Object,Fz4=Object.prototype.hasOwnProperty,Us=(A,B)=>xq1(A,"name",{value:B,configurable:!0}),Iz4=(A,B)=>{for(var Q in B)xq1(A,Q,{get:B[Q],enumerable:!0})},Yz4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Gz4(B))if(!Fz4.call(A,Z)&&Z!==Q)xq1(A,Z,{get:()=>B[Z],enumerable:!(D=Zz4(B,Z))||D.enumerable})}return A},Wz4=(A)=>Yz4(xq1({},"__esModule",{value:!0}),A),a82={};Iz4(a82,{ConditionObject:()=>D7.ConditionObject,DeprecatedObject:()=>D7.DeprecatedObject,EndpointError:()=>D7.EndpointError,EndpointObject:()=>D7.EndpointObject,EndpointObjectHeaders:()=>D7.EndpointObjectHeaders,EndpointObjectProperties:()=>D7.EndpointObjectProperties,EndpointParams:()=>D7.EndpointParams,EndpointResolverOptions:()=>D7.EndpointResolverOptions,EndpointRuleObject:()=>D7.EndpointRuleObject,ErrorRuleObject:()=>D7.ErrorRuleObject,EvaluateOptions:()=>D7.EvaluateOptions,Expression:()=>D7.Expression,FunctionArgv:()=>D7.FunctionArgv,FunctionObject:()=>D7.FunctionObject,FunctionReturn:()=>D7.FunctionReturn,ParameterObject:()=>D7.ParameterObject,ReferenceObject:()=>D7.ReferenceObject,ReferenceRecord:()=>D7.ReferenceRecord,RuleSetObject:()=>D7.RuleSetObject,RuleSetRules:()=>D7.RuleSetRules,TreeRuleObject:()=>D7.TreeRuleObject,awsEndpointFunctions:()=>B52,getUserAgentPrefix:()=>Cz4,isIpAddress:()=>D7.isIpAddress,partition:()=>e82,resolveEndpoint:()=>D7.resolveEndpoint,setPartitionInfo:()=>A52,useDefaultPartitionInfo:()=>Vz4});Q52.exports=Wz4(a82);var D7=S7(),s82=Us((A,B=!1)=>{if(B){for(let Q of A.split("."))if(!s82(Q))return!1;return!0}if(!D7.isValidHostLabel(A))return!1;if(A.length<3||A.length>63)return!1;if(A!==A.toLowerCase())return!1;if(D7.isIpAddress(A))return!1;return!0},"isVirtualHostableS3Bucket"),n82=":",Jz4="/",Xz4=Us((A)=>{let B=A.split(n82);if(B.length<6)return null;let[Q,D,Z,G,F,...I]=B;if(Q!=="arn"||D===""||Z===""||I.join(n82)==="")return null;let Y=I.map((W)=>W.split(Jz4)).flat();return{partition:D,service:Z,region:G,accountId:F,resourceId:Y}},"parseArn"),r82={partitions:[{id:"aws",outputs:{dnsSuffix:"amazonaws.com",dualStackDnsSuffix:"api.aws",implicitGlobalRegion:"us-east-1",name:"aws",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^(us|eu|ap|sa|ca|me|af|il|mx)\\-\\w+\\-\\d+$",regions:{"af-south-1":{description:"Africa (Cape Town)"},"ap-east-1":{description:"Asia Pacific (Hong Kong)"},"ap-northeast-1":{description:"Asia Pacific (Tokyo)"},"ap-northeast-2":{description:"Asia Pacific (Seoul)"},"ap-northeast-3":{description:"Asia Pacific (Osaka)"},"ap-south-1":{description:"Asia Pacific (Mumbai)"},"ap-south-2":{description:"Asia Pacific (Hyderabad)"},"ap-southeast-1":{description:"Asia Pacific (Singapore)"},"ap-southeast-2":{description:"Asia Pacific (Sydney)"},"ap-southeast-3":{description:"Asia Pacific (Jakarta)"},"ap-southeast-4":{description:"Asia Pacific (Melbourne)"},"ap-southeast-5":{description:"Asia Pacific (Malaysia)"},"ap-southeast-7":{description:"Asia Pacific (Thailand)"},"aws-global":{description:"AWS Standard global region"},"ca-central-1":{description:"Canada (Central)"},"ca-west-1":{description:"Canada West (Calgary)"},"eu-central-1":{description:"Europe (Frankfurt)"},"eu-central-2":{description:"Europe (Zurich)"},"eu-north-1":{description:"Europe (Stockholm)"},"eu-south-1":{description:"Europe (Milan)"},"eu-south-2":{description:"Europe (Spain)"},"eu-west-1":{description:"Europe (Ireland)"},"eu-west-2":{description:"Europe (London)"},"eu-west-3":{description:"Europe (Paris)"},"il-central-1":{description:"Israel (Tel Aviv)"},"me-central-1":{description:"Middle East (UAE)"},"me-south-1":{description:"Middle East (Bahrain)"},"mx-central-1":{description:"Mexico (Central)"},"sa-east-1":{description:"South America (Sao Paulo)"},"us-east-1":{description:"US East (N. Virginia)"},"us-east-2":{description:"US East (Ohio)"},"us-west-1":{description:"US West (N. California)"},"us-west-2":{description:"US West (Oregon)"}}},{id:"aws-cn",outputs:{dnsSuffix:"amazonaws.com.cn",dualStackDnsSuffix:"api.amazonwebservices.com.cn",implicitGlobalRegion:"cn-northwest-1",name:"aws-cn",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^cn\\-\\w+\\-\\d+$",regions:{"aws-cn-global":{description:"AWS China global region"},"cn-north-1":{description:"China (Beijing)"},"cn-northwest-1":{description:"China (Ningxia)"}}},{id:"aws-us-gov",outputs:{dnsSuffix:"amazonaws.com",dualStackDnsSuffix:"api.aws",implicitGlobalRegion:"us-gov-west-1",name:"aws-us-gov",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^us\\-gov\\-\\w+\\-\\d+$",regions:{"aws-us-gov-global":{description:"AWS GovCloud (US) global region"},"us-gov-east-1":{description:"AWS GovCloud (US-East)"},"us-gov-west-1":{description:"AWS GovCloud (US-West)"}}},{id:"aws-iso",outputs:{dnsSuffix:"c2s.ic.gov",dualStackDnsSuffix:"c2s.ic.gov",implicitGlobalRegion:"us-iso-east-1",name:"aws-iso",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-iso\\-\\w+\\-\\d+$",regions:{"aws-iso-global":{description:"AWS ISO (US) global region"},"us-iso-east-1":{description:"US ISO East"},"us-iso-west-1":{description:"US ISO WEST"}}},{id:"aws-iso-b",outputs:{dnsSuffix:"sc2s.sgov.gov",dualStackDnsSuffix:"sc2s.sgov.gov",implicitGlobalRegion:"us-isob-east-1",name:"aws-iso-b",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-isob\\-\\w+\\-\\d+$",regions:{"aws-iso-b-global":{description:"AWS ISOB (US) global region"},"us-isob-east-1":{description:"US ISOB East (Ohio)"}}},{id:"aws-iso-e",outputs:{dnsSuffix:"cloud.adc-e.uk",dualStackDnsSuffix:"cloud.adc-e.uk",implicitGlobalRegion:"eu-isoe-west-1",name:"aws-iso-e",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^eu\\-isoe\\-\\w+\\-\\d+$",regions:{"aws-iso-e-global":{description:"AWS ISOE (Europe) global region"},"eu-isoe-west-1":{description:"EU ISOE West"}}},{id:"aws-iso-f",outputs:{dnsSuffix:"csp.hci.ic.gov",dualStackDnsSuffix:"csp.hci.ic.gov",implicitGlobalRegion:"us-isof-south-1",name:"aws-iso-f",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-isof\\-\\w+\\-\\d+$",regions:{"aws-iso-f-global":{description:"AWS ISOF global region"},"us-isof-east-1":{description:"US ISOF EAST"},"us-isof-south-1":{description:"US ISOF SOUTH"}}},{id:"aws-eusc",outputs:{dnsSuffix:"amazonaws.eu",dualStackDnsSuffix:"amazonaws.eu",implicitGlobalRegion:"eusc-de-east-1",name:"aws-eusc",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^eusc\\-(de)\\-\\w+\\-\\d+$",regions:{"eusc-de-east-1":{description:"EU (Germany)"}}}],version:"1.1"},o82=r82,t82="",e82=Us((A)=>{let{partitions:B}=o82;for(let D of B){let{regions:Z,outputs:G}=D;for(let[F,I]of Object.entries(Z))if(F===A)return{...G,...I}}for(let D of B){let{regionRegex:Z,outputs:G}=D;if(new RegExp(Z).test(A))return{...G}}let Q=B.find((D)=>D.id==="aws");if(!Q)throw new Error("Provided region was not found in the partition array or regex, and default partition with id 'aws' doesn't exist.");return{...Q.outputs}},"partition"),A52=Us((A,B="")=>{o82=A,t82=B},"setPartitionInfo"),Vz4=Us(()=>{A52(r82,"")},"useDefaultPartitionInfo"),Cz4=Us(()=>t82,"getUserAgentPrefix"),B52={isVirtualHostableS3Bucket:s82,parseArn:Xz4,partition:e82};D7.customEndpointFunctions.aws=B52});
var xHB=E((rA3,_HB)=>{var{defineProperty:S_1,getOwnPropertyDescriptor:Aa6,getOwnPropertyNames:Ba6}=Object,Qa6=Object.prototype.hasOwnProperty,h1=(A,B)=>S_1(A,"name",{value:B,configurable:!0}),Da6=(A,B)=>{for(var Q in B)S_1(A,Q,{get:B[Q],enumerable:!0})},Za6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Ba6(B))if(!Qa6.call(A,Z)&&Z!==Q)S_1(A,Z,{get:()=>B[Z],enumerable:!(D=Aa6(B,Z))||D.enumerable})}return A},Ga6=(A)=>Za6(S_1({},"__esModule",{value:!0}),A),EKB={};Da6(EKB,{AccessDeniedException:()=>wKB,ApplyGuardrailCommand:()=>MHB,ApplyGuardrailRequestFilterSensitiveLog:()=>bKB,AsyncInvokeOutputDataConfig:()=>EE0,AsyncInvokeStatus:()=>za6,AsyncInvokeSummaryFilterSensitiveLog:()=>kKB,BedrockRuntime:()=>kHB,BedrockRuntimeClient:()=>PE0,BedrockRuntimeServiceException:()=>oV,BidirectionalInputPayloadPartFilterSensitiveLog:()=>Ds6,BidirectionalOutputPayloadPartFilterSensitiveLog:()=>Gs6,CachePointType:()=>fa6,ConflictException:()=>LKB,ContentBlock:()=>M_1,ContentBlockDelta:()=>NE0,ContentBlockDeltaEventFilterSensitiveLog:()=>cKB,ContentBlockDeltaFilterSensitiveLog:()=>dKB,ContentBlockFilterSensitiveLog:()=>fKB,ContentBlockStart:()=>LE0,ConversationRole:()=>la6,ConverseCommand:()=>RHB,ConverseOutput:()=>$E0,ConverseOutputFilterSensitiveLog:()=>gKB,ConverseRequestFilterSensitiveLog:()=>hKB,ConverseResponseFilterSensitiveLog:()=>uKB,ConverseStreamCommand:()=>OHB,ConverseStreamOutput:()=>ME0,ConverseStreamOutputFilterSensitiveLog:()=>Qs6,ConverseStreamRequestFilterSensitiveLog:()=>mKB,ConverseStreamResponseFilterSensitiveLog:()=>lKB,DocumentFormat:()=>ha6,DocumentSource:()=>E_1,GetAsyncInvokeCommand:()=>THB,GetAsyncInvokeResponseFilterSensitiveLog:()=>yKB,GuardrailAction:()=>La6,GuardrailContentBlock:()=>z_1,GuardrailContentBlockFilterSensitiveLog:()=>vKB,GuardrailContentFilterConfidence:()=>Ra6,GuardrailContentFilterStrength:()=>Oa6,GuardrailContentFilterType:()=>Ta6,GuardrailContentPolicyAction:()=>Ma6,GuardrailContentQualifier:()=>$a6,GuardrailContentSource:()=>Na6,GuardrailContextualGroundingFilterType:()=>Sa6,GuardrailContextualGroundingPolicyAction:()=>Pa6,GuardrailConverseContentBlock:()=>w_1,GuardrailConverseContentBlockFilterSensitiveLog:()=>SE0,GuardrailConverseContentQualifier:()=>ua6,GuardrailConverseImageBlockFilterSensitiveLog:()=>ta6,GuardrailConverseImageFormat:()=>ga6,GuardrailConverseImageSource:()=>U_1,GuardrailConverseImageSourceFilterSensitiveLog:()=>oa6,GuardrailImageBlockFilterSensitiveLog:()=>ra6,GuardrailImageFormat:()=>wa6,GuardrailImageSource:()=>H_1,GuardrailImageSourceFilterSensitiveLog:()=>sa6,GuardrailManagedWordType:()=>va6,GuardrailOutputScope:()=>qa6,GuardrailPiiEntityType:()=>ya6,GuardrailSensitiveInformationPolicyAction:()=>ja6,GuardrailStreamProcessingMode:()=>na6,GuardrailTopicPolicyAction:()=>ka6,GuardrailTopicType:()=>_a6,GuardrailTrace:()=>ba6,GuardrailWordPolicyAction:()=>xa6,ImageFormat:()=>ma6,ImageSource:()=>$_1,InternalServerException:()=>$KB,InvokeModelCommand:()=>PHB,InvokeModelRequestFilterSensitiveLog:()=>pKB,InvokeModelResponseFilterSensitiveLog:()=>iKB,InvokeModelWithBidirectionalStreamCommand:()=>SHB,InvokeModelWithBidirectionalStreamInput:()=>P_1,InvokeModelWithBidirectionalStreamInputFilterSensitiveLog:()=>Zs6,InvokeModelWithBidirectionalStreamOutput:()=>RE0,InvokeModelWithBidirectionalStreamOutputFilterSensitiveLog:()=>Fs6,InvokeModelWithBidirectionalStreamRequestFilterSensitiveLog:()=>nKB,InvokeModelWithBidirectionalStreamResponseFilterSensitiveLog:()=>aKB,InvokeModelWithResponseStreamCommand:()=>jHB,InvokeModelWithResponseStreamRequestFilterSensitiveLog:()=>sKB,InvokeModelWithResponseStreamResponseFilterSensitiveLog:()=>rKB,ListAsyncInvokesCommand:()=>fE0,ListAsyncInvokesResponseFilterSensitiveLog:()=>_KB,MessageFilterSensitiveLog:()=>j_1,ModelErrorException:()=>TKB,ModelNotReadyException:()=>PKB,ModelStreamErrorException:()=>jKB,ModelTimeoutException:()=>SKB,PayloadPartFilterSensitiveLog:()=>Is6,PerformanceConfigLatency:()=>pa6,PromptVariableValues:()=>UE0,ReasoningContentBlock:()=>q_1,ReasoningContentBlockDelta:()=>qE0,ReasoningContentBlockDeltaFilterSensitiveLog:()=>Bs6,ReasoningContentBlockFilterSensitiveLog:()=>As6,ReasoningTextBlockFilterSensitiveLog:()=>ea6,ResourceNotFoundException:()=>MKB,ResponseStream:()=>OE0,ResponseStreamFilterSensitiveLog:()=>Ys6,ServiceQuotaExceededException:()=>RKB,ServiceUnavailableException:()=>OKB,SortAsyncInvocationBy:()=>Ea6,SortOrder:()=>Ua6,StartAsyncInvokeCommand:()=>yHB,StartAsyncInvokeRequestFilterSensitiveLog:()=>xKB,StopReason:()=>ia6,SystemContentBlock:()=>R_1,SystemContentBlockFilterSensitiveLog:()=>jE0,ThrottlingException:()=>qKB,Tool:()=>T_1,ToolChoice:()=>wE0,ToolInputSchema:()=>O_1,ToolResultContentBlock:()=>L_1,ToolResultStatus:()=>ca6,Trace:()=>aa6,ValidationException:()=>NKB,VideoFormat:()=>da6,VideoSource:()=>N_1,__Client:()=>r1.Client,paginateListAsyncInvokes:()=>tr6});_HB.exports=Ga6(EKB);var UKB=_JB(),ZKB=V61(),Fa6=C61(),Ia6=K61(),GKB=Rs(),Ya6=K4(),mK=CB(),Wa6=bJB(),Ja6=bG(),_M=R6(),FKB=u4(),IKB=dz0(),Xa6=h1((A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,defaultSigningName:"bedrock"})},"resolveClientEndpointParameters"),iP={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}},Va6=oCB(),YKB=S61(),WKB=DKB(),r1=AZ1(),Ca6=h1((A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}},"getHttpAuthExtensionConfiguration"),Ka6=h1((A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}},"resolveHttpAuthRuntimeConfig"),Ha6=h1((A,B)=>{let Q=Object.assign(YKB.getAwsRegionExtensionConfiguration(A),r1.getDefaultExtensionConfiguration(A),WKB.getHttpHandlerExtensionConfiguration(A),Ca6(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,YKB.resolveAwsRegionExtensionConfiguration(Q),r1.resolveDefaultRuntimeConfig(Q),WKB.resolveHttpHandlerRuntimeConfig(Q),Ka6(Q))},"resolveRuntimeExtensions"),PE0=class extends r1.Client{static{h1(this,"BedrockRuntimeClient")}config;constructor(...[A]){let B=Va6.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=Xa6(B),D=GKB.resolveUserAgentConfig(Q),Z=FKB.resolveRetryConfig(D),G=Ya6.resolveRegionConfig(Z),F=ZKB.resolveHostHeaderConfig(G),I=_M.resolveEndpointConfig(F),Y=Wa6.resolveEventStreamSerdeConfig(I),W=IKB.resolveHttpAuthSchemeConfig(Y),J=UKB.resolveEventStreamConfig(W),X=Ha6(J,A?.extensions||[]);this.config=X,this.middlewareStack.use(GKB.getUserAgentPlugin(this.config)),this.middlewareStack.use(FKB.getRetryPlugin(this.config)),this.middlewareStack.use(Ja6.getContentLengthPlugin(this.config)),this.middlewareStack.use(ZKB.getHostHeaderPlugin(this.config)),this.middlewareStack.use(Fa6.getLoggerPlugin(this.config)),this.middlewareStack.use(Ia6.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(mK.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:IKB.defaultBedrockRuntimeHttpAuthSchemeParametersProvider,identityProviderConfigProvider:h1(async(V)=>new mK.DefaultIdentityProviderConfig({"aws.auth#sigv4":V.credentials}),"identityProviderConfigProvider")})),this.middlewareStack.use(mK.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}},nP=j3(),oV=class A extends r1.ServiceException{static{h1(this,"BedrockRuntimeServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},wKB=class A extends oV{static{h1(this,"AccessDeniedException")}name="AccessDeniedException";$fault="client";constructor(B){super({name:"AccessDeniedException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},EE0;((A)=>{A.visit=h1((B,Q)=>{if(B.s3OutputDataConfig!==void 0)return Q.s3OutputDataConfig(B.s3OutputDataConfig);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(EE0||(EE0={}));var za6={COMPLETED:"Completed",FAILED:"Failed",IN_PROGRESS:"InProgress"},$KB=class A extends oV{static{h1(this,"InternalServerException")}name="InternalServerException";$fault="server";constructor(B){super({name:"InternalServerException",$fault:"server",...B});Object.setPrototypeOf(this,A.prototype)}},qKB=class A extends oV{static{h1(this,"ThrottlingException")}name="ThrottlingException";$fault="client";constructor(B){super({name:"ThrottlingException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},NKB=class A extends oV{static{h1(this,"ValidationException")}name="ValidationException";$fault="client";constructor(B){super({name:"ValidationException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},Ea6={SUBMISSION_TIME:"SubmissionTime"},Ua6={ASCENDING:"Ascending",DESCENDING:"Descending"},LKB=class A extends oV{static{h1(this,"ConflictException")}name="ConflictException";$fault="client";constructor(B){super({name:"ConflictException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},MKB=class A extends oV{static{h1(this,"ResourceNotFoundException")}name="ResourceNotFoundException";$fault="client";constructor(B){super({name:"ResourceNotFoundException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},RKB=class A extends oV{static{h1(this,"ServiceQuotaExceededException")}name="ServiceQuotaExceededException";$fault="client";constructor(B){super({name:"ServiceQuotaExceededException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},OKB=class A extends oV{static{h1(this,"ServiceUnavailableException")}name="ServiceUnavailableException";$fault="server";constructor(B){super({name:"ServiceUnavailableException",$fault:"server",...B});Object.setPrototypeOf(this,A.prototype)}},wa6={JPEG:"jpeg",PNG:"png"},H_1;((A)=>{A.visit=h1((B,Q)=>{if(B.bytes!==void 0)return Q.bytes(B.bytes);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(H_1||(H_1={}));var $a6={GROUNDING_SOURCE:"grounding_source",GUARD_CONTENT:"guard_content",QUERY:"query"},z_1;((A)=>{A.visit=h1((B,Q)=>{if(B.text!==void 0)return Q.text(B.text);if(B.image!==void 0)return Q.image(B.image);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(z_1||(z_1={}));var qa6={FULL:"FULL",INTERVENTIONS:"INTERVENTIONS"},Na6={INPUT:"INPUT",OUTPUT:"OUTPUT"},La6={GUARDRAIL_INTERVENED:"GUARDRAIL_INTERVENED",NONE:"NONE"},Ma6={BLOCKED:"BLOCKED",NONE:"NONE"},Ra6={HIGH:"HIGH",LOW:"LOW",MEDIUM:"MEDIUM",NONE:"NONE"},Oa6={HIGH:"HIGH",LOW:"LOW",MEDIUM:"MEDIUM",NONE:"NONE"},Ta6={HATE:"HATE",INSULTS:"INSULTS",MISCONDUCT:"MISCONDUCT",PROMPT_ATTACK:"PROMPT_ATTACK",SEXUAL:"SEXUAL",VIOLENCE:"VIOLENCE"},Pa6={BLOCKED:"BLOCKED",NONE:"NONE"},Sa6={GROUNDING:"GROUNDING",RELEVANCE:"RELEVANCE"},ja6={ANONYMIZED:"ANONYMIZED",BLOCKED:"BLOCKED",NONE:"NONE"},ya6={ADDRESS:"ADDRESS",AGE:"AGE",AWS_ACCESS_KEY:"AWS_ACCESS_KEY",AWS_SECRET_KEY:"AWS_SECRET_KEY",CA_HEALTH_NUMBER:"CA_HEALTH_NUMBER",CA_SOCIAL_INSURANCE_NUMBER:"CA_SOCIAL_INSURANCE_NUMBER",CREDIT_DEBIT_CARD_CVV:"CREDIT_DEBIT_CARD_CVV",CREDIT_DEBIT_CARD_EXPIRY:"CREDIT_DEBIT_CARD_EXPIRY",CREDIT_DEBIT_CARD_NUMBER:"CREDIT_DEBIT_CARD_NUMBER",DRIVER_ID:"DRIVER_ID",EMAIL:"EMAIL",INTERNATIONAL_BANK_ACCOUNT_NUMBER:"INTERNATIONAL_BANK_ACCOUNT_NUMBER",IP_ADDRESS:"IP_ADDRESS",LICENSE_PLATE:"LICENSE_PLATE",MAC_ADDRESS:"MAC_ADDRESS",NAME:"NAME",PASSWORD:"PASSWORD",PHONE:"PHONE",PIN:"PIN",SWIFT_CODE:"SWIFT_CODE",UK_NATIONAL_HEALTH_SERVICE_NUMBER:"UK_NATIONAL_HEALTH_SERVICE_NUMBER",UK_NATIONAL_INSURANCE_NUMBER:"UK_NATIONAL_INSURANCE_NUMBER",UK_UNIQUE_TAXPAYER_REFERENCE_NUMBER:"UK_UNIQUE_TAXPAYER_REFERENCE_NUMBER",URL:"URL",USERNAME:"USERNAME",US_BANK_ACCOUNT_NUMBER:"US_BANK_ACCOUNT_NUMBER",US_BANK_ROUTING_NUMBER:"US_BANK_ROUTING_NUMBER",US_INDIVIDUAL_TAX_IDENTIFICATION_NUMBER:"US_INDIVIDUAL_TAX_IDENTIFICATION_NUMBER",US_PASSPORT_NUMBER:"US_PASSPORT_NUMBER",US_SOCIAL_SECURITY_NUMBER:"US_SOCIAL_SECURITY_NUMBER",VEHICLE_IDENTIFICATION_NUMBER:"VEHICLE_IDENTIFICATION_NUMBER"},ka6={BLOCKED:"BLOCKED",NONE:"NONE"},_a6={DENY:"DENY"},xa6={BLOCKED:"BLOCKED",NONE:"NONE"},va6={PROFANITY:"PROFANITY"},ba6={DISABLED:"disabled",ENABLED:"enabled",ENABLED_FULL:"enabled_full"},fa6={DEFAULT:"default"},ha6={CSV:"csv",DOC:"doc",DOCX:"docx",HTML:"html",MD:"md",PDF:"pdf",TXT:"txt",XLS:"xls",XLSX:"xlsx"},E_1;((A)=>{A.visit=h1((B,Q)=>{if(B.bytes!==void 0)return Q.bytes(B.bytes);if(B.s3Location!==void 0)return Q.s3Location(B.s3Location);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(E_1||(E_1={}));var ga6={JPEG:"jpeg",PNG:"png"},U_1;((A)=>{A.visit=h1((B,Q)=>{if(B.bytes!==void 0)return Q.bytes(B.bytes);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(U_1||(U_1={}));var ua6={GROUNDING_SOURCE:"grounding_source",GUARD_CONTENT:"guard_content",QUERY:"query"},w_1;((A)=>{A.visit=h1((B,Q)=>{if(B.text!==void 0)return Q.text(B.text);if(B.image!==void 0)return Q.image(B.image);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(w_1||(w_1={}));var ma6={GIF:"gif",JPEG:"jpeg",PNG:"png",WEBP:"webp"},$_1;((A)=>{A.visit=h1((B,Q)=>{if(B.bytes!==void 0)return Q.bytes(B.bytes);if(B.s3Location!==void 0)return Q.s3Location(B.s3Location);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})($_1||($_1={}));var q_1;((A)=>{A.visit=h1((B,Q)=>{if(B.reasoningText!==void 0)return Q.reasoningText(B.reasoningText);if(B.redactedContent!==void 0)return Q.redactedContent(B.redactedContent);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(q_1||(q_1={}));var da6={FLV:"flv",MKV:"mkv",MOV:"mov",MP4:"mp4",MPEG:"mpeg",MPG:"mpg",THREE_GP:"three_gp",WEBM:"webm",WMV:"wmv"},N_1;((A)=>{A.visit=h1((B,Q)=>{if(B.bytes!==void 0)return Q.bytes(B.bytes);if(B.s3Location!==void 0)return Q.s3Location(B.s3Location);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(N_1||(N_1={}));var L_1;((A)=>{A.visit=h1((B,Q)=>{if(B.json!==void 0)return Q.json(B.json);if(B.text!==void 0)return Q.text(B.text);if(B.image!==void 0)return Q.image(B.image);if(B.document!==void 0)return Q.document(B.document);if(B.video!==void 0)return Q.video(B.video);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(L_1||(L_1={}));var ca6={ERROR:"error",SUCCESS:"success"},M_1;((A)=>{A.visit=h1((B,Q)=>{if(B.text!==void 0)return Q.text(B.text);if(B.image!==void 0)return Q.image(B.image);if(B.document!==void 0)return Q.document(B.document);if(B.video!==void 0)return Q.video(B.video);if(B.toolUse!==void 0)return Q.toolUse(B.toolUse);if(B.toolResult!==void 0)return Q.toolResult(B.toolResult);if(B.guardContent!==void 0)return Q.guardContent(B.guardContent);if(B.cachePoint!==void 0)return Q.cachePoint(B.cachePoint);if(B.reasoningContent!==void 0)return Q.reasoningContent(B.reasoningContent);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(M_1||(M_1={}));var la6={ASSISTANT:"assistant",USER:"user"},pa6={OPTIMIZED:"optimized",STANDARD:"standard"},UE0;((A)=>{A.visit=h1((B,Q)=>{if(B.text!==void 0)return Q.text(B.text);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(UE0||(UE0={}));var R_1;((A)=>{A.visit=h1((B,Q)=>{if(B.text!==void 0)return Q.text(B.text);if(B.guardContent!==void 0)return Q.guardContent(B.guardContent);if(B.cachePoint!==void 0)return Q.cachePoint(B.cachePoint);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(R_1||(R_1={}));var wE0;((A)=>{A.visit=h1((B,Q)=>{if(B.auto!==void 0)return Q.auto(B.auto);if(B.any!==void 0)return Q.any(B.any);if(B.tool!==void 0)return Q.tool(B.tool);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(wE0||(wE0={}));var O_1;((A)=>{A.visit=h1((B,Q)=>{if(B.json!==void 0)return Q.json(B.json);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(O_1||(O_1={}));var T_1;((A)=>{A.visit=h1((B,Q)=>{if(B.toolSpec!==void 0)return Q.toolSpec(B.toolSpec);if(B.cachePoint!==void 0)return Q.cachePoint(B.cachePoint);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(T_1||(T_1={}));var $E0;((A)=>{A.visit=h1((B,Q)=>{if(B.message!==void 0)return Q.message(B.message);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})($E0||($E0={}));var ia6={CONTENT_FILTERED:"content_filtered",END_TURN:"end_turn",GUARDRAIL_INTERVENED:"guardrail_intervened",MAX_TOKENS:"max_tokens",STOP_SEQUENCE:"stop_sequence",TOOL_USE:"tool_use"},TKB=class A extends oV{static{h1(this,"ModelErrorException")}name="ModelErrorException";$fault="client";originalStatusCode;resourceName;constructor(B){super({name:"ModelErrorException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.originalStatusCode=B.originalStatusCode,this.resourceName=B.resourceName}},PKB=class A extends oV{static{h1(this,"ModelNotReadyException")}name="ModelNotReadyException";$fault="client";$retryable={};constructor(B){super({name:"ModelNotReadyException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},SKB=class A extends oV{static{h1(this,"ModelTimeoutException")}name="ModelTimeoutException";$fault="client";constructor(B){super({name:"ModelTimeoutException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},na6={ASYNC:"async",SYNC:"sync"},qE0;((A)=>{A.visit=h1((B,Q)=>{if(B.text!==void 0)return Q.text(B.text);if(B.redactedContent!==void 0)return Q.redactedContent(B.redactedContent);if(B.signature!==void 0)return Q.signature(B.signature);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(qE0||(qE0={}));var NE0;((A)=>{A.visit=h1((B,Q)=>{if(B.text!==void 0)return Q.text(B.text);if(B.toolUse!==void 0)return Q.toolUse(B.toolUse);if(B.reasoningContent!==void 0)return Q.reasoningContent(B.reasoningContent);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(NE0||(NE0={}));var LE0;((A)=>{A.visit=h1((B,Q)=>{if(B.toolUse!==void 0)return Q.toolUse(B.toolUse);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(LE0||(LE0={}));var jKB=class A extends oV{static{h1(this,"ModelStreamErrorException")}name="ModelStreamErrorException";$fault="client";originalStatusCode;originalMessage;constructor(B){super({name:"ModelStreamErrorException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.originalStatusCode=B.originalStatusCode,this.originalMessage=B.originalMessage}},ME0;((A)=>{A.visit=h1((B,Q)=>{if(B.messageStart!==void 0)return Q.messageStart(B.messageStart);if(B.contentBlockStart!==void 0)return Q.contentBlockStart(B.contentBlockStart);if(B.contentBlockDelta!==void 0)return Q.contentBlockDelta(B.contentBlockDelta);if(B.contentBlockStop!==void 0)return Q.contentBlockStop(B.contentBlockStop);if(B.messageStop!==void 0)return Q.messageStop(B.messageStop);if(B.metadata!==void 0)return Q.metadata(B.metadata);if(B.internalServerException!==void 0)return Q.internalServerException(B.internalServerException);if(B.modelStreamErrorException!==void 0)return Q.modelStreamErrorException(B.modelStreamErrorException);if(B.validationException!==void 0)return Q.validationException(B.validationException);if(B.throttlingException!==void 0)return Q.throttlingException(B.throttlingException);if(B.serviceUnavailableException!==void 0)return Q.serviceUnavailableException(B.serviceUnavailableException);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(ME0||(ME0={}));var aa6={DISABLED:"DISABLED",ENABLED:"ENABLED",ENABLED_FULL:"ENABLED_FULL"},P_1;((A)=>{A.visit=h1((B,Q)=>{if(B.chunk!==void 0)return Q.chunk(B.chunk);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(P_1||(P_1={}));var RE0;((A)=>{A.visit=h1((B,Q)=>{if(B.chunk!==void 0)return Q.chunk(B.chunk);if(B.internalServerException!==void 0)return Q.internalServerException(B.internalServerException);if(B.modelStreamErrorException!==void 0)return Q.modelStreamErrorException(B.modelStreamErrorException);if(B.validationException!==void 0)return Q.validationException(B.validationException);if(B.throttlingException!==void 0)return Q.throttlingException(B.throttlingException);if(B.modelTimeoutException!==void 0)return Q.modelTimeoutException(B.modelTimeoutException);if(B.serviceUnavailableException!==void 0)return Q.serviceUnavailableException(B.serviceUnavailableException);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(RE0||(RE0={}));var OE0;((A)=>{A.visit=h1((B,Q)=>{if(B.chunk!==void 0)return Q.chunk(B.chunk);if(B.internalServerException!==void 0)return Q.internalServerException(B.internalServerException);if(B.modelStreamErrorException!==void 0)return Q.modelStreamErrorException(B.modelStreamErrorException);if(B.validationException!==void 0)return Q.validationException(B.validationException);if(B.throttlingException!==void 0)return Q.throttlingException(B.throttlingException);if(B.modelTimeoutException!==void 0)return Q.modelTimeoutException(B.modelTimeoutException);if(B.serviceUnavailableException!==void 0)return Q.serviceUnavailableException(B.serviceUnavailableException);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(OE0||(OE0={}));var yKB=h1((A)=>({...A,...A.failureMessage&&{failureMessage:r1.SENSITIVE_STRING},...A.outputDataConfig&&{outputDataConfig:A.outputDataConfig}}),"GetAsyncInvokeResponseFilterSensitiveLog"),kKB=h1((A)=>({...A,...A.failureMessage&&{failureMessage:r1.SENSITIVE_STRING},...A.outputDataConfig&&{outputDataConfig:A.outputDataConfig}}),"AsyncInvokeSummaryFilterSensitiveLog"),_KB=h1((A)=>({...A,...A.asyncInvokeSummaries&&{asyncInvokeSummaries:A.asyncInvokeSummaries.map((B)=>kKB(B))}}),"ListAsyncInvokesResponseFilterSensitiveLog"),xKB=h1((A)=>({...A,...A.modelInput&&{modelInput:r1.SENSITIVE_STRING},...A.outputDataConfig&&{outputDataConfig:A.outputDataConfig}}),"StartAsyncInvokeRequestFilterSensitiveLog"),sa6=h1((A)=>{if(A.bytes!==void 0)return{bytes:A.bytes};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"GuardrailImageSourceFilterSensitiveLog"),ra6=h1((A)=>({...A,...A.source&&{source:r1.SENSITIVE_STRING}}),"GuardrailImageBlockFilterSensitiveLog"),vKB=h1((A)=>{if(A.text!==void 0)return{text:A.text};if(A.image!==void 0)return{image:r1.SENSITIVE_STRING};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"GuardrailContentBlockFilterSensitiveLog"),bKB=h1((A)=>({...A,...A.content&&{content:A.content.map((B)=>vKB(B))}}),"ApplyGuardrailRequestFilterSensitiveLog"),oa6=h1((A)=>{if(A.bytes!==void 0)return{bytes:A.bytes};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"GuardrailConverseImageSourceFilterSensitiveLog"),ta6=h1((A)=>({...A,...A.source&&{source:r1.SENSITIVE_STRING}}),"GuardrailConverseImageBlockFilterSensitiveLog"),SE0=h1((A)=>{if(A.text!==void 0)return{text:A.text};if(A.image!==void 0)return{image:r1.SENSITIVE_STRING};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"GuardrailConverseContentBlockFilterSensitiveLog"),ea6=h1((A)=>({...A}),"ReasoningTextBlockFilterSensitiveLog"),As6=h1((A)=>{if(A.reasoningText!==void 0)return{reasoningText:r1.SENSITIVE_STRING};if(A.redactedContent!==void 0)return{redactedContent:A.redactedContent};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"ReasoningContentBlockFilterSensitiveLog"),fKB=h1((A)=>{if(A.text!==void 0)return{text:A.text};if(A.image!==void 0)return{image:A.image};if(A.document!==void 0)return{document:A.document};if(A.video!==void 0)return{video:A.video};if(A.toolUse!==void 0)return{toolUse:A.toolUse};if(A.toolResult!==void 0)return{toolResult:A.toolResult};if(A.guardContent!==void 0)return{guardContent:SE0(A.guardContent)};if(A.cachePoint!==void 0)return{cachePoint:A.cachePoint};if(A.reasoningContent!==void 0)return{reasoningContent:r1.SENSITIVE_STRING};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"ContentBlockFilterSensitiveLog"),j_1=h1((A)=>({...A,...A.content&&{content:A.content.map((B)=>fKB(B))}}),"MessageFilterSensitiveLog"),jE0=h1((A)=>{if(A.text!==void 0)return{text:A.text};if(A.guardContent!==void 0)return{guardContent:SE0(A.guardContent)};if(A.cachePoint!==void 0)return{cachePoint:A.cachePoint};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"SystemContentBlockFilterSensitiveLog"),hKB=h1((A)=>({...A,...A.messages&&{messages:A.messages.map((B)=>j_1(B))},...A.system&&{system:A.system.map((B)=>jE0(B))},...A.toolConfig&&{toolConfig:A.toolConfig},...A.promptVariables&&{promptVariables:r1.SENSITIVE_STRING},...A.requestMetadata&&{requestMetadata:r1.SENSITIVE_STRING}}),"ConverseRequestFilterSensitiveLog"),gKB=h1((A)=>{if(A.message!==void 0)return{message:j_1(A.message)};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"ConverseOutputFilterSensitiveLog"),uKB=h1((A)=>({...A,...A.output&&{output:gKB(A.output)}}),"ConverseResponseFilterSensitiveLog"),mKB=h1((A)=>({...A,...A.messages&&{messages:A.messages.map((B)=>j_1(B))},...A.system&&{system:A.system.map((B)=>jE0(B))},...A.toolConfig&&{toolConfig:A.toolConfig},...A.promptVariables&&{promptVariables:r1.SENSITIVE_STRING},...A.requestMetadata&&{requestMetadata:r1.SENSITIVE_STRING}}),"ConverseStreamRequestFilterSensitiveLog"),Bs6=h1((A)=>{if(A.text!==void 0)return{text:A.text};if(A.redactedContent!==void 0)return{redactedContent:A.redactedContent};if(A.signature!==void 0)return{signature:A.signature};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"ReasoningContentBlockDeltaFilterSensitiveLog"),dKB=h1((A)=>{if(A.text!==void 0)return{text:A.text};if(A.toolUse!==void 0)return{toolUse:A.toolUse};if(A.reasoningContent!==void 0)return{reasoningContent:r1.SENSITIVE_STRING};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"ContentBlockDeltaFilterSensitiveLog"),cKB=h1((A)=>({...A,...A.delta&&{delta:dKB(A.delta)}}),"ContentBlockDeltaEventFilterSensitiveLog"),Qs6=h1((A)=>{if(A.messageStart!==void 0)return{messageStart:A.messageStart};if(A.contentBlockStart!==void 0)return{contentBlockStart:A.contentBlockStart};if(A.contentBlockDelta!==void 0)return{contentBlockDelta:cKB(A.contentBlockDelta)};if(A.contentBlockStop!==void 0)return{contentBlockStop:A.contentBlockStop};if(A.messageStop!==void 0)return{messageStop:A.messageStop};if(A.metadata!==void 0)return{metadata:A.metadata};if(A.internalServerException!==void 0)return{internalServerException:A.internalServerException};if(A.modelStreamErrorException!==void 0)return{modelStreamErrorException:A.modelStreamErrorException};if(A.validationException!==void 0)return{validationException:A.validationException};if(A.throttlingException!==void 0)return{throttlingException:A.throttlingException};if(A.serviceUnavailableException!==void 0)return{serviceUnavailableException:A.serviceUnavailableException};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"ConverseStreamOutputFilterSensitiveLog"),lKB=h1((A)=>({...A,...A.stream&&{stream:"STREAMING_CONTENT"}}),"ConverseStreamResponseFilterSensitiveLog"),pKB=h1((A)=>({...A,...A.body&&{body:r1.SENSITIVE_STRING}}),"InvokeModelRequestFilterSensitiveLog"),iKB=h1((A)=>({...A,...A.body&&{body:r1.SENSITIVE_STRING}}),"InvokeModelResponseFilterSensitiveLog"),Ds6=h1((A)=>({...A,...A.bytes&&{bytes:r1.SENSITIVE_STRING}}),"BidirectionalInputPayloadPartFilterSensitiveLog"),Zs6=h1((A)=>{if(A.chunk!==void 0)return{chunk:r1.SENSITIVE_STRING};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"InvokeModelWithBidirectionalStreamInputFilterSensitiveLog"),nKB=h1((A)=>({...A,...A.body&&{body:"STREAMING_CONTENT"}}),"InvokeModelWithBidirectionalStreamRequestFilterSensitiveLog"),Gs6=h1((A)=>({...A,...A.bytes&&{bytes:r1.SENSITIVE_STRING}}),"BidirectionalOutputPayloadPartFilterSensitiveLog"),Fs6=h1((A)=>{if(A.chunk!==void 0)return{chunk:r1.SENSITIVE_STRING};if(A.internalServerException!==void 0)return{internalServerException:A.internalServerException};if(A.modelStreamErrorException!==void 0)return{modelStreamErrorException:A.modelStreamErrorException};if(A.validationException!==void 0)return{validationException:A.validationException};if(A.throttlingException!==void 0)return{throttlingException:A.throttlingException};if(A.modelTimeoutException!==void 0)return{modelTimeoutException:A.modelTimeoutException};if(A.serviceUnavailableException!==void 0)return{serviceUnavailableException:A.serviceUnavailableException};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"InvokeModelWithBidirectionalStreamOutputFilterSensitiveLog"),aKB=h1((A)=>({...A,...A.body&&{body:"STREAMING_CONTENT"}}),"InvokeModelWithBidirectionalStreamResponseFilterSensitiveLog"),sKB=h1((A)=>({...A,...A.body&&{body:r1.SENSITIVE_STRING}}),"InvokeModelWithResponseStreamRequestFilterSensitiveLog"),Is6=h1((A)=>({...A,...A.bytes&&{bytes:r1.SENSITIVE_STRING}}),"PayloadPartFilterSensitiveLog"),Ys6=h1((A)=>{if(A.chunk!==void 0)return{chunk:r1.SENSITIVE_STRING};if(A.internalServerException!==void 0)return{internalServerException:A.internalServerException};if(A.modelStreamErrorException!==void 0)return{modelStreamErrorException:A.modelStreamErrorException};if(A.validationException!==void 0)return{validationException:A.validationException};if(A.throttlingException!==void 0)return{throttlingException:A.throttlingException};if(A.modelTimeoutException!==void 0)return{modelTimeoutException:A.modelTimeoutException};if(A.serviceUnavailableException!==void 0)return{serviceUnavailableException:A.serviceUnavailableException};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"ResponseStreamFilterSensitiveLog"),rKB=h1((A)=>({...A,...A.body&&{body:"STREAMING_CONTENT"}}),"InvokeModelWithResponseStreamResponseFilterSensitiveLog"),Y8=KI(),Ws6=RQ1(),Js6=h1(async(A,B)=>{let Q=mK.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/guardrail/{guardrailIdentifier}/version/{guardrailVersion}/apply"),Q.p("guardrailIdentifier",()=>A.guardrailIdentifier,"{guardrailIdentifier}",!1),Q.p("guardrailVersion",()=>A.guardrailVersion,"{guardrailVersion}",!1);let Z;return Z=JSON.stringify(r1.take(A,{content:h1((G)=>es6(G,B),"content"),outputScope:[],source:[]})),Q.m("POST").h(D).b(Z),Q.build()},"se_ApplyGuardrailCommand"),Xs6=h1(async(A,B)=>{let Q=mK.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/model/{modelId}/converse"),Q.p("modelId",()=>A.modelId,"{modelId}",!1);let Z;return Z=JSON.stringify(r1.take(A,{additionalModelRequestFields:h1((G)=>DZ1(G,B),"additionalModelRequestFields"),additionalModelResponseFieldPaths:h1((G)=>r1._json(G),"additionalModelResponseFieldPaths"),guardrailConfig:h1((G)=>r1._json(G),"guardrailConfig"),inferenceConfig:h1((G)=>IHB(G,B),"inferenceConfig"),messages:h1((G)=>YHB(G,B),"messages"),performanceConfig:h1((G)=>r1._json(G),"performanceConfig"),promptVariables:h1((G)=>r1._json(G),"promptVariables"),requestMetadata:h1((G)=>r1._json(G),"requestMetadata"),system:h1((G)=>WHB(G,B),"system"),toolConfig:h1((G)=>JHB(G,B),"toolConfig")})),Q.m("POST").h(D).b(Z),Q.build()},"se_ConverseCommand"),Vs6=h1(async(A,B)=>{let Q=mK.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/model/{modelId}/converse-stream"),Q.p("modelId",()=>A.modelId,"{modelId}",!1);let Z;return Z=JSON.stringify(r1.take(A,{additionalModelRequestFields:h1((G)=>DZ1(G,B),"additionalModelRequestFields"),additionalModelResponseFieldPaths:h1((G)=>r1._json(G),"additionalModelResponseFieldPaths"),guardrailConfig:h1((G)=>r1._json(G),"guardrailConfig"),inferenceConfig:h1((G)=>IHB(G,B),"inferenceConfig"),messages:h1((G)=>YHB(G,B),"messages"),performanceConfig:h1((G)=>r1._json(G),"performanceConfig"),promptVariables:h1((G)=>r1._json(G),"promptVariables"),requestMetadata:h1((G)=>r1._json(G),"requestMetadata"),system:h1((G)=>WHB(G,B),"system"),toolConfig:h1((G)=>JHB(G,B),"toolConfig")})),Q.m("POST").h(D).b(Z),Q.build()},"se_ConverseStreamCommand"),Cs6=h1(async(A,B)=>{let Q=mK.requestBuilder(A,B),D={};Q.bp("/async-invoke/{invocationArn}"),Q.p("invocationArn",()=>A.invocationArn,"{invocationArn}",!1);let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetAsyncInvokeCommand"),Ks6=h1(async(A,B)=>{let Q=mK.requestBuilder(A,B),D=r1.map({},r1.isSerializableHeaderValue,{[bE0]:A[k_1]||"application/octet-stream",[TE0]:A[TE0],[LHB]:A[$HB],[qHB]:A[UHB],[NHB]:A[wHB],[x_1]:A[__1]});Q.bp("/model/{modelId}/invoke"),Q.p("modelId",()=>A.modelId,"{modelId}",!1);let Z;if(A.body!==void 0)Z=A.body;return Q.m("POST").h(D).b(Z),Q.build()},"se_InvokeModelCommand"),Hs6=h1(async(A,B)=>{let Q=mK.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/model/{modelId}/invoke-with-bidirectional-stream"),Q.p("modelId",()=>A.modelId,"{modelId}",!1);let Z;if(A.body!==void 0)Z=vs6(A.body,B);return Q.m("POST").h(D).b(Z),Q.build()},"se_InvokeModelWithBidirectionalStreamCommand"),zs6=h1(async(A,B)=>{let Q=mK.requestBuilder(A,B),D=r1.map({},r1.isSerializableHeaderValue,{[bE0]:A[k_1]||"application/octet-stream",[sr6]:A[TE0],[LHB]:A[$HB],[qHB]:A[UHB],[NHB]:A[wHB],[x_1]:A[__1]});Q.bp("/model/{modelId}/invoke-with-response-stream"),Q.p("modelId",()=>A.modelId,"{modelId}",!1);let Z;if(A.body!==void 0)Z=A.body;return Q.m("POST").h(D).b(Z),Q.build()},"se_InvokeModelWithResponseStreamCommand"),Es6=h1(async(A,B)=>{let Q=mK.requestBuilder(A,B),D={};Q.bp("/async-invoke");let Z=r1.map({[HKB]:[()=>A.submitTimeAfter!==void 0,()=>r1.serializeDateTime(A[HKB]).toString()],[zKB]:[()=>A.submitTimeBefore!==void 0,()=>r1.serializeDateTime(A[zKB]).toString()],[CKB]:[,A[CKB]],[JKB]:[()=>A.maxResults!==void 0,()=>A[JKB].toString()],[XKB]:[,A[XKB]],[VKB]:[,A[VKB]],[KKB]:[,A[KKB]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListAsyncInvokesCommand"),Us6=h1(async(A,B)=>{let Q=mK.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/async-invoke");let Z;return Z=JSON.stringify(r1.take(A,{clientRequestToken:[!0,(G)=>G??Ws6.v4()],modelId:[],modelInput:h1((G)=>Fr6(G,B),"modelInput"),outputDataConfig:h1((G)=>r1._json(G),"outputDataConfig"),tags:h1((G)=>r1._json(G),"tags")})),Q.m("POST").h(D).b(Z),Q.build()},"se_StartAsyncInvokeCommand"),ws6=h1(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return aP(A,B);let Q=r1.map({$metadata:KG(A)}),D=r1.expectNonNull(r1.expectObject(await Y8.parseJsonBody(A.body,B)),"body"),Z=r1.take(D,{action:r1.expectString,actionReason:r1.expectString,assessments:h1((G)=>KHB(G,B),"assessments"),guardrailCoverage:r1._json,outputs:r1._json,usage:r1._json});return Object.assign(Q,Z),Q},"de_ApplyGuardrailCommand"),$s6=h1(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return aP(A,B);let Q=r1.map({$metadata:KG(A)}),D=r1.expectNonNull(r1.expectObject(await Y8.parseJsonBody(A.body,B)),"body"),Z=r1.take(D,{additionalModelResponseFields:h1((G)=>y_1(G,B),"additionalModelResponseFields"),metrics:r1._json,output:h1((G)=>Rr6(Y8.awsExpectUnion(G),B),"output"),performanceConfig:r1._json,stopReason:r1.expectString,trace:h1((G)=>Pr6(G,B),"trace"),usage:r1._json});return Object.assign(Q,Z),Q},"de_ConverseCommand"),qs6=h1(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return aP(A,B);let Q=r1.map({$metadata:KG(A)}),D=A.body;return Q.stream=fs6(D,B),Q},"de_ConverseStreamCommand"),Ns6=h1(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return aP(A,B);let Q=r1.map({$metadata:KG(A)}),D=r1.expectNonNull(r1.expectObject(await Y8.parseJsonBody(A.body,B)),"body"),Z=r1.take(D,{clientRequestToken:r1.expectString,endTime:h1((G)=>r1.expectNonNull(r1.parseRfc3339DateTimeWithOffset(G)),"endTime"),failureMessage:r1.expectString,invocationArn:r1.expectString,lastModifiedTime:h1((G)=>r1.expectNonNull(r1.parseRfc3339DateTimeWithOffset(G)),"lastModifiedTime"),modelArn:r1.expectString,outputDataConfig:h1((G)=>r1._json(Y8.awsExpectUnion(G)),"outputDataConfig"),status:r1.expectString,submitTime:h1((G)=>r1.expectNonNull(r1.parseRfc3339DateTimeWithOffset(G)),"submitTime")});return Object.assign(Q,Z),Q},"de_GetAsyncInvokeCommand"),Ls6=h1(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return aP(A,B);let Q=r1.map({$metadata:KG(A),[k_1]:[,A.headers[bE0]],[__1]:[,A.headers[x_1]]}),D=await r1.collectBody(A.body,B);return Q.body=D,Q},"de_InvokeModelCommand"),Ms6=h1(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return aP(A,B);let Q=r1.map({$metadata:KG(A)}),D=A.body;return Q.body=hs6(D,B),Q},"de_InvokeModelWithBidirectionalStreamCommand"),Rs6=h1(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return aP(A,B);let Q=r1.map({$metadata:KG(A),[k_1]:[,A.headers[rr6]],[__1]:[,A.headers[x_1]]}),D=A.body;return Q.body=gs6(D,B),Q},"de_InvokeModelWithResponseStreamCommand"),Os6=h1(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return aP(A,B);let Q=r1.map({$metadata:KG(A)}),D=r1.expectNonNull(r1.expectObject(await Y8.parseJsonBody(A.body,B)),"body"),Z=r1.take(D,{asyncInvokeSummaries:h1((G)=>Ur6(G,B),"asyncInvokeSummaries"),nextToken:r1.expectString});return Object.assign(Q,Z),Q},"de_ListAsyncInvokesCommand"),Ts6=h1(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return aP(A,B);let Q=r1.map({$metadata:KG(A)}),D=r1.expectNonNull(r1.expectObject(await Y8.parseJsonBody(A.body,B)),"body"),Z=r1.take(D,{invocationArn:r1.expectString});return Object.assign(Q,Z),Q},"de_StartAsyncInvokeCommand"),aP=h1(async(A,B)=>{let Q={...A,body:await Y8.parseJsonErrorBody(A.body,B)},D=Y8.loadRestJsonErrorCode(A,Q.body);switch(D){case"AccessDeniedException":case"com.amazonaws.bedrockruntime#AccessDeniedException":throw await Ss6(Q,B);case"InternalServerException":case"com.amazonaws.bedrockruntime#InternalServerException":throw await oKB(Q,B);case"ResourceNotFoundException":case"com.amazonaws.bedrockruntime#ResourceNotFoundException":throw await _s6(Q,B);case"ServiceQuotaExceededException":case"com.amazonaws.bedrockruntime#ServiceQuotaExceededException":throw await xs6(Q,B);case"ThrottlingException":case"com.amazonaws.bedrockruntime#ThrottlingException":throw await BHB(Q,B);case"ValidationException":case"com.amazonaws.bedrockruntime#ValidationException":throw await QHB(Q,B);case"ModelErrorException":case"com.amazonaws.bedrockruntime#ModelErrorException":throw await ys6(Q,B);case"ModelNotReadyException":case"com.amazonaws.bedrockruntime#ModelNotReadyException":throw await ks6(Q,B);case"ModelTimeoutException":case"com.amazonaws.bedrockruntime#ModelTimeoutException":throw await eKB(Q,B);case"ServiceUnavailableException":case"com.amazonaws.bedrockruntime#ServiceUnavailableException":throw await AHB(Q,B);case"ModelStreamErrorException":case"com.amazonaws.bedrockruntime#ModelStreamErrorException":throw await tKB(Q,B);case"ConflictException":case"com.amazonaws.bedrockruntime#ConflictException":throw await js6(Q,B);default:let Z=Q.body;return Ps6({output:A,parsedBody:Z,errorCode:D})}},"de_CommandError"),Ps6=r1.withBaseException(oV),Ss6=h1(async(A,B)=>{let Q=r1.map({}),D=A.body,Z=r1.take(D,{message:r1.expectString});Object.assign(Q,Z);let G=new wKB({$metadata:KG(A),...Q});return r1.decorateServiceException(G,A.body)},"de_AccessDeniedExceptionRes"),js6=h1(async(A,B)=>{let Q=r1.map({}),D=A.body,Z=r1.take(D,{message:r1.expectString});Object.assign(Q,Z);let G=new LKB({$metadata:KG(A),...Q});return r1.decorateServiceException(G,A.body)},"de_ConflictExceptionRes"),oKB=h1(async(A,B)=>{let Q=r1.map({}),D=A.body,Z=r1.take(D,{message:r1.expectString});Object.assign(Q,Z);let G=new $KB({$metadata:KG(A),...Q});return r1.decorateServiceException(G,A.body)},"de_InternalServerExceptionRes"),ys6=h1(async(A,B)=>{let Q=r1.map({}),D=A.body,Z=r1.take(D,{message:r1.expectString,originalStatusCode:r1.expectInt32,resourceName:r1.expectString});Object.assign(Q,Z);let G=new TKB({$metadata:KG(A),...Q});return r1.decorateServiceException(G,A.body)},"de_ModelErrorExceptionRes"),ks6=h1(async(A,B)=>{let Q=r1.map({}),D=A.body,Z=r1.take(D,{message:r1.expectString});Object.assign(Q,Z);let G=new PKB({$metadata:KG(A),...Q});return r1.decorateServiceException(G,A.body)},"de_ModelNotReadyExceptionRes"),tKB=h1(async(A,B)=>{let Q=r1.map({}),D=A.body,Z=r1.take(D,{message:r1.expectString,originalMessage:r1.expectString,originalStatusCode:r1.expectInt32});Object.assign(Q,Z);let G=new jKB({$metadata:KG(A),...Q});return r1.decorateServiceException(G,A.body)},"de_ModelStreamErrorExceptionRes"),eKB=h1(async(A,B)=>{let Q=r1.map({}),D=A.body,Z=r1.take(D,{message:r1.expectString});Object.assign(Q,Z);let G=new SKB({$metadata:KG(A),...Q});return r1.decorateServiceException(G,A.body)},"de_ModelTimeoutExceptionRes"),_s6=h1(async(A,B)=>{let Q=r1.map({}),D=A.body,Z=r1.take(D,{message:r1.expectString});Object.assign(Q,Z);let G=new MKB({$metadata:KG(A),...Q});return r1.decorateServiceException(G,A.body)},"de_ResourceNotFoundExceptionRes"),xs6=h1(async(A,B)=>{let Q=r1.map({}),D=A.body,Z=r1.take(D,{message:r1.expectString});Object.assign(Q,Z);let G=new RKB({$metadata:KG(A),...Q});return r1.decorateServiceException(G,A.body)},"de_ServiceQuotaExceededExceptionRes"),AHB=h1(async(A,B)=>{let Q=r1.map({}),D=A.body,Z=r1.take(D,{message:r1.expectString});Object.assign(Q,Z);let G=new OKB({$metadata:KG(A),...Q});return r1.decorateServiceException(G,A.body)},"de_ServiceUnavailableExceptionRes"),BHB=h1(async(A,B)=>{let Q=r1.map({}),D=A.body,Z=r1.take(D,{message:r1.expectString});Object.assign(Q,Z);let G=new qKB({$metadata:KG(A),...Q});return r1.decorateServiceException(G,A.body)},"de_ThrottlingExceptionRes"),QHB=h1(async(A,B)=>{let Q=r1.map({}),D=A.body,Z=r1.take(D,{message:r1.expectString});Object.assign(Q,Z);let G=new NKB({$metadata:KG(A),...Q});return r1.decorateServiceException(G,A.body)},"de_ValidationExceptionRes"),vs6=h1((A,B)=>{let Q=h1((D)=>P_1.visit(D,{chunk:h1((Z)=>bs6(Z,B),"chunk"),_:h1((Z)=>Z,"_")}),"eventMarshallingVisitor");return B.eventStreamMarshaller.serialize(A,Q)},"se_InvokeModelWithBidirectionalStreamInput"),bs6=h1((A,B)=>{let Q={":event-type":{type:"string",value:"chunk"},":message-type":{type:"string",value:"event"},":content-type":{type:"string",value:"application/json"}},D=new Uint8Array;return D=as6(A,B),D=B.utf8Decoder(JSON.stringify(D)),{headers:Q,body:D}},"se_BidirectionalInputPayloadPart_event"),fs6=h1((A,B)=>{return B.eventStreamMarshaller.deserialize(A,async(Q)=>{if(Q.messageStart!=null)return{messageStart:await ps6(Q.messageStart,B)};if(Q.contentBlockStart!=null)return{contentBlockStart:await ds6(Q.contentBlockStart,B)};if(Q.contentBlockDelta!=null)return{contentBlockDelta:await ms6(Q.contentBlockDelta,B)};if(Q.contentBlockStop!=null)return{contentBlockStop:await cs6(Q.contentBlockStop,B)};if(Q.messageStop!=null)return{messageStop:await is6(Q.messageStop,B)};if(Q.metadata!=null)return{metadata:await ls6(Q.metadata,B)};if(Q.internalServerException!=null)return{internalServerException:await yE0(Q.internalServerException,B)};if(Q.modelStreamErrorException!=null)return{modelStreamErrorException:await kE0(Q.modelStreamErrorException,B)};if(Q.validationException!=null)return{validationException:await vE0(Q.validationException,B)};if(Q.throttlingException!=null)return{throttlingException:await xE0(Q.throttlingException,B)};if(Q.serviceUnavailableException!=null)return{serviceUnavailableException:await _E0(Q.serviceUnavailableException,B)};return{$unknown:A}})},"de_ConverseStreamOutput"),hs6=h1((A,B)=>{return B.eventStreamMarshaller.deserialize(A,async(Q)=>{if(Q.chunk!=null)return{chunk:await us6(Q.chunk,B)};if(Q.internalServerException!=null)return{internalServerException:await yE0(Q.internalServerException,B)};if(Q.modelStreamErrorException!=null)return{modelStreamErrorException:await kE0(Q.modelStreamErrorException,B)};if(Q.validationException!=null)return{validationException:await vE0(Q.validationException,B)};if(Q.throttlingException!=null)return{throttlingException:await xE0(Q.throttlingException,B)};if(Q.modelTimeoutException!=null)return{modelTimeoutException:await DHB(Q.modelTimeoutException,B)};if(Q.serviceUnavailableException!=null)return{serviceUnavailableException:await _E0(Q.serviceUnavailableException,B)};return{$unknown:A}})},"de_InvokeModelWithBidirectionalStreamOutput"),gs6=h1((A,B)=>{return B.eventStreamMarshaller.deserialize(A,async(Q)=>{if(Q.chunk!=null)return{chunk:await ns6(Q.chunk,B)};if(Q.internalServerException!=null)return{internalServerException:await yE0(Q.internalServerException,B)};if(Q.modelStreamErrorException!=null)return{modelStreamErrorException:await kE0(Q.modelStreamErrorException,B)};if(Q.validationException!=null)return{validationException:await vE0(Q.validationException,B)};if(Q.throttlingException!=null)return{throttlingException:await xE0(Q.throttlingException,B)};if(Q.modelTimeoutException!=null)return{modelTimeoutException:await DHB(Q.modelTimeoutException,B)};if(Q.serviceUnavailableException!=null)return{serviceUnavailableException:await _E0(Q.serviceUnavailableException,B)};return{$unknown:A}})},"de_ResponseStream"),us6=h1(async(A,B)=>{let Q={},D=await Y8.parseJsonBody(A.body,B);return Object.assign(Q,$r6(D,B)),Q},"de_BidirectionalOutputPayloadPart_event"),ms6=h1(async(A,B)=>{let Q={},D=await Y8.parseJsonBody(A.body,B);return Object.assign(Q,Lr6(D,B)),Q},"de_ContentBlockDeltaEvent_event"),ds6=h1(async(A,B)=>{let Q={},D=await Y8.parseJsonBody(A.body,B);return Object.assign(Q,r1._json(D)),Q},"de_ContentBlockStartEvent_event"),cs6=h1(async(A,B)=>{let Q={},D=await Y8.parseJsonBody(A.body,B);return Object.assign(Q,r1._json(D)),Q},"de_ContentBlockStopEvent_event"),ls6=h1(async(A,B)=>{let Q={},D=await Y8.parseJsonBody(A.body,B);return Object.assign(Q,Or6(D,B)),Q},"de_ConverseStreamMetadataEvent_event"),yE0=h1(async(A,B)=>{let Q={...A,body:await Y8.parseJsonBody(A.body,B)};return oKB(Q,B)},"de_InternalServerException_event"),ps6=h1(async(A,B)=>{let Q={},D=await Y8.parseJsonBody(A.body,B);return Object.assign(Q,r1._json(D)),Q},"de_MessageStartEvent_event"),is6=h1(async(A,B)=>{let Q={},D=await Y8.parseJsonBody(A.body,B);return Object.assign(Q,ur6(D,B)),Q},"de_MessageStopEvent_event"),kE0=h1(async(A,B)=>{let Q={...A,body:await Y8.parseJsonBody(A.body,B)};return tKB(Q,B)},"de_ModelStreamErrorException_event"),DHB=h1(async(A,B)=>{let Q={...A,body:await Y8.parseJsonBody(A.body,B)};return eKB(Q,B)},"de_ModelTimeoutException_event"),ns6=h1(async(A,B)=>{let Q={},D=await Y8.parseJsonBody(A.body,B);return Object.assign(Q,mr6(D,B)),Q},"de_PayloadPart_event"),_E0=h1(async(A,B)=>{let Q={...A,body:await Y8.parseJsonBody(A.body,B)};return AHB(Q,B)},"de_ServiceUnavailableException_event"),xE0=h1(async(A,B)=>{let Q={...A,body:await Y8.parseJsonBody(A.body,B)};return BHB(Q,B)},"de_ThrottlingException_event"),vE0=h1(async(A,B)=>{let Q={...A,body:await Y8.parseJsonBody(A.body,B)};return QHB(Q,B)},"de_ValidationException_event"),as6=h1((A,B)=>{return r1.take(A,{bytes:B.base64Encoder})},"se_BidirectionalInputPayloadPart"),ss6=h1((A,B)=>{return M_1.visit(A,{cachePoint:h1((Q)=>({cachePoint:r1._json(Q)}),"cachePoint"),document:h1((Q)=>({document:ZHB(Q,B)}),"document"),guardContent:h1((Q)=>({guardContent:GHB(Q,B)}),"guardContent"),image:h1((Q)=>({image:FHB(Q,B)}),"image"),reasoningContent:h1((Q)=>({reasoningContent:Ir6(Q,B)}),"reasoningContent"),text:h1((Q)=>({text:Q}),"text"),toolResult:h1((Q)=>({toolResult:Xr6(Q,B)}),"toolResult"),toolUse:h1((Q)=>({toolUse:zr6(Q,B)}),"toolUse"),video:h1((Q)=>({video:XHB(Q,B)}),"video"),_:h1((Q,D)=>({[Q]:D}),"_")})},"se_ContentBlock"),rs6=h1((A,B)=>{return A.filter((Q)=>Q!=null).map((Q)=>{return ss6(Q,B)})},"se_ContentBlocks"),ZHB=h1((A,B)=>{return r1.take(A,{format:[],name:[],source:h1((Q)=>os6(Q,B),"source")})},"se_DocumentBlock"),os6=h1((A,B)=>{return E_1.visit(A,{bytes:h1((Q)=>({bytes:B.base64Encoder(Q)}),"bytes"),s3Location:h1((Q)=>({s3Location:r1._json(Q)}),"s3Location"),_:h1((Q,D)=>({[Q]:D}),"_")})},"se_DocumentSource"),ts6=h1((A,B)=>{return z_1.visit(A,{image:h1((Q)=>({image:Qr6(Q,B)}),"image"),text:h1((Q)=>({text:r1._json(Q)}),"text"),_:h1((Q,D)=>({[Q]:D}),"_")})},"se_GuardrailContentBlock"),es6=h1((A,B)=>{return A.filter((Q)=>Q!=null).map((Q)=>{return ts6(Q,B)})},"se_GuardrailContentBlockList"),GHB=h1((A,B)=>{return w_1.visit(A,{image:h1((Q)=>({image:Ar6(Q,B)}),"image"),text:h1((Q)=>({text:r1._json(Q)}),"text"),_:h1((Q,D)=>({[Q]:D}),"_")})},"se_GuardrailConverseContentBlock"),Ar6=h1((A,B)=>{return r1.take(A,{format:[],source:h1((Q)=>Br6(Q,B),"source")})},"se_GuardrailConverseImageBlock"),Br6=h1((A,B)=>{return U_1.visit(A,{bytes:h1((Q)=>({bytes:B.base64Encoder(Q)}),"bytes"),_:h1((Q,D)=>({[Q]:D}),"_")})},"se_GuardrailConverseImageSource"),Qr6=h1((A,B)=>{return r1.take(A,{format:[],source:h1((Q)=>Dr6(Q,B),"source")})},"se_GuardrailImageBlock"),Dr6=h1((A,B)=>{return H_1.visit(A,{bytes:h1((Q)=>({bytes:B.base64Encoder(Q)}),"bytes"),_:h1((Q,D)=>({[Q]:D}),"_")})},"se_GuardrailImageSource"),FHB=h1((A,B)=>{return r1.take(A,{format:[],source:h1((Q)=>Zr6(Q,B),"source")})},"se_ImageBlock"),Zr6=h1((A,B)=>{return $_1.visit(A,{bytes:h1((Q)=>({bytes:B.base64Encoder(Q)}),"bytes"),s3Location:h1((Q)=>({s3Location:r1._json(Q)}),"s3Location"),_:h1((Q,D)=>({[Q]:D}),"_")})},"se_ImageSource"),IHB=h1((A,B)=>{return r1.take(A,{maxTokens:[],stopSequences:r1._json,temperature:r1.serializeFloat,topP:r1.serializeFloat})},"se_InferenceConfiguration"),Gr6=h1((A,B)=>{return r1.take(A,{content:h1((Q)=>rs6(Q,B),"content"),role:[]})},"se_Message"),YHB=h1((A,B)=>{return A.filter((Q)=>Q!=null).map((Q)=>{return Gr6(Q,B)})},"se_Messages"),Fr6=h1((A,B)=>{return A},"se_ModelInputPayload"),Ir6=h1((A,B)=>{return q_1.visit(A,{reasoningText:h1((Q)=>({reasoningText:r1._json(Q)}),"reasoningText"),redactedContent:h1((Q)=>({redactedContent:B.base64Encoder(Q)}),"redactedContent"),_:h1((Q,D)=>({[Q]:D}),"_")})},"se_ReasoningContentBlock"),Yr6=h1((A,B)=>{return R_1.visit(A,{cachePoint:h1((Q)=>({cachePoint:r1._json(Q)}),"cachePoint"),guardContent:h1((Q)=>({guardContent:GHB(Q,B)}),"guardContent"),text:h1((Q)=>({text:Q}),"text"),_:h1((Q,D)=>({[Q]:D}),"_")})},"se_SystemContentBlock"),WHB=h1((A,B)=>{return A.filter((Q)=>Q!=null).map((Q)=>{return Yr6(Q,B)})},"se_SystemContentBlocks"),Wr6=h1((A,B)=>{return T_1.visit(A,{cachePoint:h1((Q)=>({cachePoint:r1._json(Q)}),"cachePoint"),toolSpec:h1((Q)=>({toolSpec:Hr6(Q,B)}),"toolSpec"),_:h1((Q,D)=>({[Q]:D}),"_")})},"se_Tool"),JHB=h1((A,B)=>{return r1.take(A,{toolChoice:r1._json,tools:h1((Q)=>Kr6(Q,B),"tools")})},"se_ToolConfiguration"),Jr6=h1((A,B)=>{return O_1.visit(A,{json:h1((Q)=>({json:DZ1(Q,B)}),"json"),_:h1((Q,D)=>({[Q]:D}),"_")})},"se_ToolInputSchema"),Xr6=h1((A,B)=>{return r1.take(A,{content:h1((Q)=>Cr6(Q,B),"content"),status:[],toolUseId:[]})},"se_ToolResultBlock"),Vr6=h1((A,B)=>{return L_1.visit(A,{document:h1((Q)=>({document:ZHB(Q,B)}),"document"),image:h1((Q)=>({image:FHB(Q,B)}),"image"),json:h1((Q)=>({json:DZ1(Q,B)}),"json"),text:h1((Q)=>({text:Q}),"text"),video:h1((Q)=>({video:XHB(Q,B)}),"video"),_:h1((Q,D)=>({[Q]:D}),"_")})},"se_ToolResultContentBlock"),Cr6=h1((A,B)=>{return A.filter((Q)=>Q!=null).map((Q)=>{return Vr6(Q,B)})},"se_ToolResultContentBlocks"),Kr6=h1((A,B)=>{return A.filter((Q)=>Q!=null).map((Q)=>{return Wr6(Q,B)})},"se_Tools"),Hr6=h1((A,B)=>{return r1.take(A,{description:[],inputSchema:h1((Q)=>Jr6(Q,B),"inputSchema"),name:[]})},"se_ToolSpecification"),zr6=h1((A,B)=>{return r1.take(A,{input:h1((Q)=>DZ1(Q,B),"input"),name:[],toolUseId:[]})},"se_ToolUseBlock"),XHB=h1((A,B)=>{return r1.take(A,{format:[],source:h1((Q)=>Er6(Q,B),"source")})},"se_VideoBlock"),Er6=h1((A,B)=>{return N_1.visit(A,{bytes:h1((Q)=>({bytes:B.base64Encoder(Q)}),"bytes"),s3Location:h1((Q)=>({s3Location:r1._json(Q)}),"s3Location"),_:h1((Q,D)=>({[Q]:D}),"_")})},"se_VideoSource"),DZ1=h1((A,B)=>{return A},"se_Document"),Ur6=h1((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return wr6(D,B)})},"de_AsyncInvokeSummaries"),wr6=h1((A,B)=>{return r1.take(A,{clientRequestToken:r1.expectString,endTime:h1((Q)=>r1.expectNonNull(r1.parseRfc3339DateTimeWithOffset(Q)),"endTime"),failureMessage:r1.expectString,invocationArn:r1.expectString,lastModifiedTime:h1((Q)=>r1.expectNonNull(r1.parseRfc3339DateTimeWithOffset(Q)),"lastModifiedTime"),modelArn:r1.expectString,outputDataConfig:h1((Q)=>r1._json(Y8.awsExpectUnion(Q)),"outputDataConfig"),status:r1.expectString,submitTime:h1((Q)=>r1.expectNonNull(r1.parseRfc3339DateTimeWithOffset(Q)),"submitTime")})},"de_AsyncInvokeSummary"),$r6=h1((A,B)=>{return r1.take(A,{bytes:B.base64Decoder})},"de_BidirectionalOutputPayloadPart"),qr6=h1((A,B)=>{if(A.cachePoint!=null)return{cachePoint:r1._json(A.cachePoint)};if(A.document!=null)return{document:VHB(A.document,B)};if(A.guardContent!=null)return{guardContent:vr6(Y8.awsExpectUnion(A.guardContent),B)};if(A.image!=null)return{image:zHB(A.image,B)};if(A.reasoningContent!=null)return{reasoningContent:dr6(Y8.awsExpectUnion(A.reasoningContent),B)};if(r1.expectString(A.text)!==void 0)return{text:r1.expectString(A.text)};if(A.toolResult!=null)return{toolResult:lr6(A.toolResult,B)};if(A.toolUse!=null)return{toolUse:nr6(A.toolUse,B)};if(A.video!=null)return{video:EHB(A.video,B)};return{$unknown:Object.entries(A)[0]}},"de_ContentBlock"),Nr6=h1((A,B)=>{if(A.reasoningContent!=null)return{reasoningContent:cr6(Y8.awsExpectUnion(A.reasoningContent),B)};if(r1.expectString(A.text)!==void 0)return{text:r1.expectString(A.text)};if(A.toolUse!=null)return{toolUse:r1._json(A.toolUse)};return{$unknown:Object.entries(A)[0]}},"de_ContentBlockDelta"),Lr6=h1((A,B)=>{return r1.take(A,{contentBlockIndex:r1.expectInt32,delta:h1((Q)=>Nr6(Y8.awsExpectUnion(Q),B),"delta")})},"de_ContentBlockDeltaEvent"),Mr6=h1((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return qr6(Y8.awsExpectUnion(D),B)})},"de_ContentBlocks"),Rr6=h1((A,B)=>{if(A.message!=null)return{message:gr6(A.message,B)};return{$unknown:Object.entries(A)[0]}},"de_ConverseOutput"),Or6=h1((A,B)=>{return r1.take(A,{metrics:r1._json,performanceConfig:r1._json,trace:h1((Q)=>Tr6(Q,B),"trace"),usage:r1._json})},"de_ConverseStreamMetadataEvent"),Tr6=h1((A,B)=>{return r1.take(A,{guardrail:h1((Q)=>HHB(Q,B),"guardrail"),promptRouter:r1._json})},"de_ConverseStreamTrace"),Pr6=h1((A,B)=>{return r1.take(A,{guardrail:h1((Q)=>HHB(Q,B),"guardrail"),promptRouter:r1._json})},"de_ConverseTrace"),VHB=h1((A,B)=>{return r1.take(A,{format:r1.expectString,name:r1.expectString,source:h1((Q)=>Sr6(Y8.awsExpectUnion(Q),B),"source")})},"de_DocumentBlock"),Sr6=h1((A,B)=>{if(A.bytes!=null)return{bytes:B.base64Decoder(A.bytes)};if(A.s3Location!=null)return{s3Location:r1._json(A.s3Location)};return{$unknown:Object.entries(A)[0]}},"de_DocumentSource"),CHB=h1((A,B)=>{return r1.take(A,{contentPolicy:r1._json,contextualGroundingPolicy:h1((Q)=>xr6(Q,B),"contextualGroundingPolicy"),invocationMetrics:r1._json,sensitiveInformationPolicy:r1._json,topicPolicy:r1._json,wordPolicy:r1._json})},"de_GuardrailAssessment"),KHB=h1((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return CHB(D,B)})},"de_GuardrailAssessmentList"),jr6=h1((A,B)=>{return Object.entries(A).reduce((Q,[D,Z])=>{if(Z===null)return Q;return Q[D]=KHB(Z,B),Q},{})},"de_GuardrailAssessmentListMap"),yr6=h1((A,B)=>{return Object.entries(A).reduce((Q,[D,Z])=>{if(Z===null)return Q;return Q[D]=CHB(Z,B),Q},{})},"de_GuardrailAssessmentMap"),kr6=h1((A,B)=>{return r1.take(A,{action:r1.expectString,detected:r1.expectBoolean,score:r1.limitedParseDouble,threshold:r1.limitedParseDouble,type:r1.expectString})},"de_GuardrailContextualGroundingFilter"),_r6=h1((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return kr6(D,B)})},"de_GuardrailContextualGroundingFilters"),xr6=h1((A,B)=>{return r1.take(A,{filters:h1((Q)=>_r6(Q,B),"filters")})},"de_GuardrailContextualGroundingPolicyAssessment"),vr6=h1((A,B)=>{if(A.image!=null)return{image:br6(A.image,B)};if(A.text!=null)return{text:r1._json(A.text)};return{$unknown:Object.entries(A)[0]}},"de_GuardrailConverseContentBlock"),br6=h1((A,B)=>{return r1.take(A,{format:r1.expectString,source:h1((Q)=>fr6(Y8.awsExpectUnion(Q),B),"source")})},"de_GuardrailConverseImageBlock"),fr6=h1((A,B)=>{if(A.bytes!=null)return{bytes:B.base64Decoder(A.bytes)};return{$unknown:Object.entries(A)[0]}},"de_GuardrailConverseImageSource"),HHB=h1((A,B)=>{return r1.take(A,{actionReason:r1.expectString,inputAssessment:h1((Q)=>yr6(Q,B),"inputAssessment"),modelOutput:r1._json,outputAssessments:h1((Q)=>jr6(Q,B),"outputAssessments")})},"de_GuardrailTraceAssessment"),zHB=h1((A,B)=>{return r1.take(A,{format:r1.expectString,source:h1((Q)=>hr6(Y8.awsExpectUnion(Q),B),"source")})},"de_ImageBlock"),hr6=h1((A,B)=>{if(A.bytes!=null)return{bytes:B.base64Decoder(A.bytes)};if(A.s3Location!=null)return{s3Location:r1._json(A.s3Location)};return{$unknown:Object.entries(A)[0]}},"de_ImageSource"),gr6=h1((A,B)=>{return r1.take(A,{content:h1((Q)=>Mr6(Q,B),"content"),role:r1.expectString})},"de_Message"),ur6=h1((A,B)=>{return r1.take(A,{additionalModelResponseFields:h1((Q)=>y_1(Q,B),"additionalModelResponseFields"),stopReason:r1.expectString})},"de_MessageStopEvent"),mr6=h1((A,B)=>{return r1.take(A,{bytes:B.base64Decoder})},"de_PayloadPart"),dr6=h1((A,B)=>{if(A.reasoningText!=null)return{reasoningText:r1._json(A.reasoningText)};if(A.redactedContent!=null)return{redactedContent:B.base64Decoder(A.redactedContent)};return{$unknown:Object.entries(A)[0]}},"de_ReasoningContentBlock"),cr6=h1((A,B)=>{if(A.redactedContent!=null)return{redactedContent:B.base64Decoder(A.redactedContent)};if(r1.expectString(A.signature)!==void 0)return{signature:r1.expectString(A.signature)};if(r1.expectString(A.text)!==void 0)return{text:r1.expectString(A.text)};return{$unknown:Object.entries(A)[0]}},"de_ReasoningContentBlockDelta"),lr6=h1((A,B)=>{return r1.take(A,{content:h1((Q)=>ir6(Q,B),"content"),status:r1.expectString,toolUseId:r1.expectString})},"de_ToolResultBlock"),pr6=h1((A,B)=>{if(A.document!=null)return{document:VHB(A.document,B)};if(A.image!=null)return{image:zHB(A.image,B)};if(A.json!=null)return{json:y_1(A.json,B)};if(r1.expectString(A.text)!==void 0)return{text:r1.expectString(A.text)};if(A.video!=null)return{video:EHB(A.video,B)};return{$unknown:Object.entries(A)[0]}},"de_ToolResultContentBlock"),ir6=h1((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return pr6(Y8.awsExpectUnion(D),B)})},"de_ToolResultContentBlocks"),nr6=h1((A,B)=>{return r1.take(A,{input:h1((Q)=>y_1(Q,B),"input"),name:r1.expectString,toolUseId:r1.expectString})},"de_ToolUseBlock"),EHB=h1((A,B)=>{return r1.take(A,{format:r1.expectString,source:h1((Q)=>ar6(Y8.awsExpectUnion(Q),B),"source")})},"de_VideoBlock"),ar6=h1((A,B)=>{if(A.bytes!=null)return{bytes:B.base64Decoder(A.bytes)};if(A.s3Location!=null)return{s3Location:r1._json(A.s3Location)};return{$unknown:Object.entries(A)[0]}},"de_VideoSource"),y_1=h1((A,B)=>{return A},"de_Document"),KG=h1((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),TE0="accept",k_1="contentType",bE0="content-type",UHB="guardrailIdentifier",wHB="guardrailVersion",JKB="maxResults",XKB="nextToken",__1="performanceConfigLatency",VKB="sortBy",CKB="statusEquals",KKB="sortOrder",HKB="submitTimeAfter",zKB="submitTimeBefore",$HB="trace",sr6="x-amzn-bedrock-accept",rr6="x-amzn-bedrock-content-type",qHB="x-amzn-bedrock-guardrailidentifier",NHB="x-amzn-bedrock-guardrailversion",x_1="x-amzn-bedrock-performanceconfig-latency",LHB="x-amzn-bedrock-trace",MHB=class extends r1.Command.classBuilder().ep(iP).m(function(A,B,Q,D){return[nP.getSerdePlugin(Q,this.serialize,this.deserialize),_M.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockFrontendService","ApplyGuardrail",{}).n("BedrockRuntimeClient","ApplyGuardrailCommand").f(bKB,void 0).ser(Js6).de(ws6).build(){static{h1(this,"ApplyGuardrailCommand")}},RHB=class extends r1.Command.classBuilder().ep(iP).m(function(A,B,Q,D){return[nP.getSerdePlugin(Q,this.serialize,this.deserialize),_M.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockFrontendService","Converse",{}).n("BedrockRuntimeClient","ConverseCommand").f(hKB,uKB).ser(Xs6).de($s6).build(){static{h1(this,"ConverseCommand")}},OHB=class extends r1.Command.classBuilder().ep(iP).m(function(A,B,Q,D){return[nP.getSerdePlugin(Q,this.serialize,this.deserialize),_M.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockFrontendService","ConverseStream",{eventStream:{output:!0}}).n("BedrockRuntimeClient","ConverseStreamCommand").f(mKB,lKB).ser(Vs6).de(qs6).build(){static{h1(this,"ConverseStreamCommand")}},THB=class extends r1.Command.classBuilder().ep(iP).m(function(A,B,Q,D){return[nP.getSerdePlugin(Q,this.serialize,this.deserialize),_M.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockFrontendService","GetAsyncInvoke",{}).n("BedrockRuntimeClient","GetAsyncInvokeCommand").f(void 0,yKB).ser(Cs6).de(Ns6).build(){static{h1(this,"GetAsyncInvokeCommand")}},PHB=class extends r1.Command.classBuilder().ep(iP).m(function(A,B,Q,D){return[nP.getSerdePlugin(Q,this.serialize,this.deserialize),_M.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockFrontendService","InvokeModel",{}).n("BedrockRuntimeClient","InvokeModelCommand").f(pKB,iKB).ser(Ks6).de(Ls6).build(){static{h1(this,"InvokeModelCommand")}},SHB=class extends r1.Command.classBuilder().ep(iP).m(function(A,B,Q,D){return[nP.getSerdePlugin(Q,this.serialize,this.deserialize),_M.getEndpointPlugin(Q,A.getEndpointParameterInstructions()),UKB.getEventStreamPlugin(Q)]}).s("AmazonBedrockFrontendService","InvokeModelWithBidirectionalStream",{eventStream:{input:!0,output:!0}}).n("BedrockRuntimeClient","InvokeModelWithBidirectionalStreamCommand").f(nKB,aKB).ser(Hs6).de(Ms6).build(){static{h1(this,"InvokeModelWithBidirectionalStreamCommand")}},jHB=class extends r1.Command.classBuilder().ep(iP).m(function(A,B,Q,D){return[nP.getSerdePlugin(Q,this.serialize,this.deserialize),_M.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockFrontendService","InvokeModelWithResponseStream",{eventStream:{output:!0}}).n("BedrockRuntimeClient","InvokeModelWithResponseStreamCommand").f(sKB,rKB).ser(zs6).de(Rs6).build(){static{h1(this,"InvokeModelWithResponseStreamCommand")}},fE0=class extends r1.Command.classBuilder().ep(iP).m(function(A,B,Q,D){return[nP.getSerdePlugin(Q,this.serialize,this.deserialize),_M.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockFrontendService","ListAsyncInvokes",{}).n("BedrockRuntimeClient","ListAsyncInvokesCommand").f(void 0,_KB).ser(Es6).de(Os6).build(){static{h1(this,"ListAsyncInvokesCommand")}},yHB=class extends r1.Command.classBuilder().ep(iP).m(function(A,B,Q,D){return[nP.getSerdePlugin(Q,this.serialize,this.deserialize),_M.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockFrontendService","StartAsyncInvoke",{}).n("BedrockRuntimeClient","StartAsyncInvokeCommand").f(xKB,void 0).ser(Us6).de(Ts6).build(){static{h1(this,"StartAsyncInvokeCommand")}},or6={ApplyGuardrailCommand:MHB,ConverseCommand:RHB,ConverseStreamCommand:OHB,GetAsyncInvokeCommand:THB,InvokeModelCommand:PHB,InvokeModelWithBidirectionalStreamCommand:SHB,InvokeModelWithResponseStreamCommand:jHB,ListAsyncInvokesCommand:fE0,StartAsyncInvokeCommand:yHB},kHB=class extends PE0{static{h1(this,"BedrockRuntime")}};r1.createAggregatedClient(or6,kHB);var tr6=mK.createPaginator(PE0,fE0,"nextToken","nextToken","maxResults")});
var xVB=E((PA3,_VB)=>{var{defineProperty:G_1,getOwnPropertyDescriptor:Lp6,getOwnPropertyNames:Mp6}=Object,Rp6=Object.prototype.hasOwnProperty,Pe=(A,B)=>G_1(A,"name",{value:B,configurable:!0}),Op6=(A,B)=>{for(var Q in B)G_1(A,Q,{get:B[Q],enumerable:!0})},Tp6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Mp6(B))if(!Rp6.call(A,Z)&&Z!==Q)G_1(A,Z,{get:()=>B[Z],enumerable:!(D=Lp6(B,Z))||D.enumerable})}return A},Pp6=(A)=>Tp6(G_1({},"__esModule",{value:!0}),A),SVB={};Op6(SVB,{EventStreamMarshaller:()=>kVB,eventStreamSerdeProvider:()=>Sp6});_VB.exports=Pp6(SVB);var sD1=tz0();function jVB(A){let B=0,Q=0,D=null,Z=null,G=Pe((I)=>{if(typeof I!=="number")throw new Error("Attempted to allocate an event message where size was not a number: "+I);B=I,Q=4,D=new Uint8Array(I),new DataView(D.buffer).setUint32(0,I,!1)},"allocateMessage"),F=Pe(async function*(){let I=A[Symbol.asyncIterator]();while(!0){let{value:Y,done:W}=await I.next();if(W){if(!B)return;else if(B===Q)yield D;else throw new Error("Truncated event message received.");return}let J=Y.length,X=0;while(X<J){if(!D){let C=J-X;if(!Z)Z=new Uint8Array(4);let K=Math.min(4-Q,C);if(Z.set(Y.slice(X,X+K),Q),Q+=K,X+=K,Q<4)break;G(new DataView(Z.buffer).getUint32(0,!1)),Z=null}let V=Math.min(B-Q,J-X);if(D.set(Y.slice(X,X+V),Q),Q+=V,X+=V,B&&B===Q)yield D,D=null,B=0,Q=0}}},"iterator");return{[Symbol.asyncIterator]:F}}Pe(jVB,"getChunkedStream");function yVB(A,B){return async function(Q){let{value:D}=Q.headers[":message-type"];if(D==="error"){let Z=new Error(Q.headers[":error-message"].value||"UnknownError");throw Z.name=Q.headers[":error-code"].value,Z}else if(D==="exception"){let Z=Q.headers[":exception-type"].value,G={[Z]:Q},F=await A(G);if(F.$unknown){let I=new Error(B(Q.body));throw I.name=Z,I}throw F[Z]}else if(D==="event"){let Z={[Q.headers[":event-type"].value]:Q},G=await A(Z);if(G.$unknown)return;return G}else throw Error(`Unrecognizable event type: ${Q.headers[":event-type"].value}`)}}Pe(yVB,"getMessageUnmarshaller");var kVB=class{static{Pe(this,"EventStreamMarshaller")}constructor({utf8Encoder:A,utf8Decoder:B}){this.eventStreamCodec=new sD1.EventStreamCodec(A,B),this.utfEncoder=A}deserialize(A,B){let Q=jVB(A);return new sD1.SmithyMessageDecoderStream({messageStream:new sD1.MessageDecoderStream({inputStream:Q,decoder:this.eventStreamCodec}),deserializer:yVB(B,this.utfEncoder)})}serialize(A,B){return new sD1.MessageEncoderStream({messageStream:new sD1.SmithyMessageEncoderStream({inputStream:A,serializer:B}),encoder:this.eventStreamCodec,includeEndFrame:!0})}},Sp6=Pe((A)=>new kVB(A),"eventStreamSerdeProvider")});
var zL=E((rH5,M42)=>{var{defineProperty:Vq1,getOwnPropertyDescriptor:UV4,getOwnPropertyNames:wV4}=Object,$V4=Object.prototype.hasOwnProperty,q80=(A,B)=>Vq1(A,"name",{value:B,configurable:!0}),qV4=(A,B)=>{for(var Q in B)Vq1(A,Q,{get:B[Q],enumerable:!0})},NV4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of wV4(B))if(!$V4.call(A,Z)&&Z!==Q)Vq1(A,Z,{get:()=>B[Z],enumerable:!(D=UV4(B,Z))||D.enumerable})}return A},LV4=(A)=>NV4(Vq1({},"__esModule",{value:!0}),A),q42={};qV4(q42,{emitWarningIfUnsupportedVersion:()=>MV4,setCredentialFeature:()=>N42,setFeature:()=>L42,state:()=>$80});M42.exports=LV4(q42);var $80={warningEmitted:!1},MV4=q80((A)=>{if(A&&!$80.warningEmitted&&parseInt(A.substring(1,A.indexOf(".")))<18)$80.warningEmitted=!0,process.emitWarning(`NodeDeprecationWarning: The AWS SDK for JavaScript (v3) will
no longer support Node.js 16.x on January 6, 2025.

To continue receiving updates to AWS services, bug fixes, and security
updates please upgrade to a supported Node.js LTS version.

More information can be found at: https://a.co/74kJMmI`)},"emitWarningIfUnsupportedVersion");function N42(A,B,Q){if(!A.$source)A.$source={};return A.$source[B]=Q,A}q80(N42,"setCredentialFeature");function L42(A,B,Q){if(!A.__aws_sdk_context)A.__aws_sdk_context={features:{}};else if(!A.__aws_sdk_context.features)A.__aws_sdk_context.features={};A.__aws_sdk_context.features[B]=Q}q80(L42,"setFeature")});

// Export all variables
module.exports = {
  AE0,
  AI2,
  AVB,
  AZ1,
  B82,
  BJ2,
  BW2,
  BZ2,
  C61,
  C70,
  CXB,
  DKB,
  DN1,
  DVB,
  E30,
  E82,
  EVB,
  F70,
  FI2,
  FVB,
  G30,
  G70,
  H61,
  HCB,
  I62,
  J32,
  J62,
  K30,
  K32,
  K61,
  KI,
  KXB,
  M80,
  MF2,
  MJB,
  N50,
  N61,
  N72,
  N80,
  OZ2,
  PVB,
  Q30,
  Q70,
  R62,
  Rs,
  S61,
  T61,
  TF2,
  U52,
  UCB,
  UG2,
  UJB,
  UZ2,
  V52,
  V61,
  VD2,
  VVB,
  W70,
  WVB,
  X82,
  XD2,
  XZ,
  Y30,
  Z30,
  _JB,
  _Y2,
  aY2,
  az0,
  b82,
  bJB,
  c80,
  d32,
  d50,
  d82,
  dz0,
  ek1,
  fCB,
  g80,
  gVB,
  hY2,
  iF2,
  iI2,
  jY2,
  kZ2,
  l62,
  lZ2,
  m61,
  n52,
  n62,
  oCB,
  pCB,
  pW2,
  pXB,
  pY2,
  pz0,
  qCB,
  qZ2,
  r62,
  sD2,
  sF2,
  sXB,
  t32,
  tD2,
  tz0,
  u61,
  uCB,
  uN1,
  w30,
  w50,
  ws,
  xHB,
  xVB,
  zL
};
