// Package extracted with entry point: Eo0
// Contains 50 variables: $B1, $V1, Bo0, Br0, By, Co0, Di1, Eo0, Fi1, Gi1... (and 40 more)

var $B1=E((ra0)=>{Object.defineProperty(ra0,"__esModule",{value:!0});ra0._getStorageKey=ra0._getUserStorageKey=void 0;var aa0=el();function sa0(A,B,Q){var D;if(Q)return Q(A,B);let Z=B&&B.customIDs?B.customIDs:{},G=[`uid:${(D=B===null||B===void 0?void 0:B.userID)!==null&&D!==void 0?D:""}`,`cids:${Object.keys(Z).sort((F,I)=>F.localeCompare(I)).map((F)=>`${F}-${Z[F]}`).join(",")}`,`k:${A}`];return aa0._DJB2(G.join("|"))}ra0._getUserStorageKey=sa0;function gj9(A,B,Q){if(B)return sa0(A,B,Q);return aa0._DJB2(`k:${A}`)}ra0._getStorageKey=gj9});
var $V1=E((Cs0)=>{Object.defineProperty(Cs0,"__esModule",{value:!0});Cs0._notifyVisibilityChanged=Cs0._subscribeToVisiblityChanged=Cs0._isUnloading=Cs0._isCurrentlyVisible=void 0;var UV1=uf(),wV1="foreground",Ai1="background",Vs0=[],ep1=wV1,Bi1=!1,Cy9=()=>{return ep1===wV1};Cs0._isCurrentlyVisible=Cy9;var Ky9=()=>Bi1;Cs0._isUnloading=Ky9;var Hy9=(A)=>{Vs0.unshift(A)};Cs0._subscribeToVisiblityChanged=Hy9;var zy9=(A)=>{if(A===ep1)return;ep1=A,Vs0.forEach((B)=>B(A))};Cs0._notifyVisibilityChanged=zy9;UV1._addWindowEventListenerSafe("focus",()=>{Bi1=!1,Cs0._notifyVisibilityChanged(wV1)});UV1._addWindowEventListenerSafe("blur",()=>Cs0._notifyVisibilityChanged(Ai1));UV1._addWindowEventListenerSafe("beforeunload",()=>{Bi1=!0,Cs0._notifyVisibilityChanged(Ai1)});UV1._addDocumentEventListenerSafe("visibilitychange",()=>{Cs0._notifyVisibilityChanged(document.visibilityState==="visible"?wV1:Ai1)})});
var Bo0=E((Ao0)=>{Object.defineProperty(Ao0,"__esModule",{value:!0});var cf=By();class er0{constructor(A){this._sdkKey=A,this._rawValues=null,this._values=null,this._source="Uninitialized",this._lcut=0,this._receivedAt=0,this._bootstrapMetadata=null,this._warnings=new Set}reset(){this._values=null,this._rawValues=null,this._source="Loading",this._lcut=0,this._receivedAt=0,this._bootstrapMetadata=null}finalize(){if(this._values)return;this._source="NoValues"}getValues(){return this._rawValues?cf._typedJsonParse(this._rawValues,"has_updates","EvaluationStoreValues"):null}setValues(A,B){var Q;if(!A)return!1;let D=cf._typedJsonParse(A.data,"has_updates","EvaluationResponse");if(D==null)return!1;if(this._source=A.source,(D===null||D===void 0?void 0:D.has_updates)!==!0)return!0;if(this._rawValues=A.data,this._lcut=D.time,this._receivedAt=A.receivedAt,this._values=D,this._bootstrapMetadata=this._extractBootstrapMetadata(A.source,D),A.source&&D.user)this._setWarningState(B,D);return cf.SDKFlags.setFlags(this._sdkKey,(Q=D.sdk_flags)!==null&&Q!==void 0?Q:{}),!0}getWarnings(){if(this._warnings.size===0)return;return Array.from(this._warnings)}getGate(A){var B;return this._getDetailedStoreResult((B=this._values)===null||B===void 0?void 0:B.feature_gates,A)}getConfig(A){var B;return this._getDetailedStoreResult((B=this._values)===null||B===void 0?void 0:B.dynamic_configs,A)}getLayer(A){var B;return this._getDetailedStoreResult((B=this._values)===null||B===void 0?void 0:B.layer_configs,A)}getParamStore(A){var B;return this._getDetailedStoreResult((B=this._values)===null||B===void 0?void 0:B.param_stores,A)}getSource(){return this._source}getExposureMapping(){var A;return(A=this._values)===null||A===void 0?void 0:A.exposures}_extractBootstrapMetadata(A,B){if(A!=="Bootstrap")return null;let Q={};if(B.user)Q.user=B.user;if(B.sdkInfo)Q.generatorSDKInfo=B.sdkInfo;return Q.lcut=B.time,Q}_getDetailedStoreResult(A,B){let Q=null;if(A)Q=A[B]?A[B]:A[cf._DJB2(B)];return{result:Q,details:this._getDetails(Q==null)}}_setWarningState(A,B){var Q;let D=cf.StableID.get(this._sdkKey);if(((Q=A.customIDs)===null||Q===void 0?void 0:Q.stableID)!==D){this._warnings.add("StableIDMismatch");return}if("user"in B){let Z=B.user;if(cf._getFullUserHash(A)!==cf._getFullUserHash(Z))this._warnings.add("PartialUserMatch")}}getCurrentSourceDetails(){if(this._source==="Uninitialized"||this._source==="NoValues")return{reason:this._source};let A={reason:this._source,lcut:this._lcut,receivedAt:this._receivedAt};if(this._warnings.size>0)A.warnings=Array.from(this._warnings);return A}_getDetails(A){var B,Q;let D=this.getCurrentSourceDetails(),Z=D.reason,G=(B=D.warnings)!==null&&B!==void 0?B:[];if(this._source==="Bootstrap"&&G.length>0)Z=Z+G[0];if(Z!=="Uninitialized"&&Z!=="NoValues")Z=`${Z}:${A?"Unrecognized":"Recognized"}`;let F=this._source==="Bootstrap"?(Q=this._bootstrapMetadata)!==null&&Q!==void 0?Q:void 0:void 0;if(F)D.bootstrapMetadata=F;return Object.assign(Object.assign({},D),{reason:Z})}}Ao0.default=er0});
var Br0=E((Gp)=>{var ty9=Gp&&Gp.__awaiter||function(A,B,Q,D){function Z(G){return G instanceof Q?G:new Q(function(F){F(G)})}return new(Q||(Q=Promise))(function(G,F){function I(J){try{W(D.next(J))}catch(X){F(X)}}function Y(J){try{W(D.throw(J))}catch(X){F(X)}}function W(J){J.done?G(J.value):Z(J.value).then(I,Y)}W((D=D.apply(A,B||[])).next())})};Object.defineProperty(Gp,"__esModule",{value:!0});Gp._fetchTxtRecords=void 0;var ey9=new Uint8Array([0,0,1,0,0,1,0,0,0,0,0,0,13,102,101,97,116,117,114,101,97,115,115,101,116,115,3,111,114,103,0,0,16,0,1]),Ak9="https://cloudflare-dns.com/dns-query",Bk9=["i","e","d"],Qk9=200;function Dk9(A){return ty9(this,void 0,void 0,function*(){let B=yield A(Ak9,{method:"POST",headers:{"Content-Type":"application/dns-message",Accept:"application/dns-message"},body:ey9});if(!B.ok){let Z=new Error("Failed to fetch TXT records from DNS");throw Z.name="DnsTxtFetchError",Z}let Q=yield B.arrayBuffer(),D=new Uint8Array(Q);return Zk9(D)})}Gp._fetchTxtRecords=Dk9;function Zk9(A){let B=A.findIndex((D,Z)=>Z<Qk9&&String.fromCharCode(D)==="="&&Bk9.includes(String.fromCharCode(A[Z-1])));if(B===-1){let D=new Error("Failed to parse TXT records from DNS");throw D.name="DnsTxtParseError",D}let Q="";for(let D=B-1;D<A.length;D++)Q+=String.fromCharCode(A[D]);return Q.split(",")}});
var By=E((i9)=>{var F_9=i9&&i9.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),b4=i9&&i9.__exportStar||function(A,B){for(var Q in A)if(Q!=="default"&&!Object.prototype.hasOwnProperty.call(B,Q))F_9(B,A,Q)};Object.defineProperty(i9,"__esModule",{value:!0});i9.Storage=i9.Log=i9.EventLogger=i9.Diagnostics=void 0;gf();var I_9=WV1();Object.defineProperty(i9,"Diagnostics",{enumerable:!0,get:function(){return I_9.Diagnostics}});var Y_9=Di1();Object.defineProperty(i9,"EventLogger",{enumerable:!0,get:function(){return Y_9.EventLogger}});var tr0=fW();Object.defineProperty(i9,"Log",{enumerable:!0,get:function(){return tr0.Log}});var W_9=RB1(),J_9=UO();Object.defineProperty(i9,"Storage",{enumerable:!0,get:function(){return J_9.Storage}});b4(gf(),i9);b4($B1(),i9);b4(qs0(),i9);b4(hs0(),i9);b4(WV1(),i9);b4(us0(),i9);b4(Yi1(),i9);b4(as0(),i9);b4(rs0(),i9);b4(el(),i9);b4(ts0(),i9);b4(fW(),i9);b4(Wi1(),i9);b4(qB1(),i9);b4(Sr0(),i9);b4(yr0(),i9);b4(_r0(),i9);b4(uf(),i9);b4(PV1(),i9);b4(jV1(),i9);b4(MV1(),i9);b4(vr0(),i9);b4(Ki1(),i9);b4(hr0(),i9);b4(sp1(),i9);b4(RB1(),i9);b4(ur0(),i9);b4(dr0(),i9);b4(ir0(),i9);b4(ar0(),i9);b4(Gi1(),i9);b4(UO(),i9);b4(Fi1(),i9);b4(JV1(),i9);b4(tp1(),i9);b4(NV1(),i9);b4($V1(),i9);b4(or0(),i9);b4(Vi1(),i9);__STATSIG__=Object.assign(Object.assign({},__STATSIG__!==null&&__STATSIG__!==void 0?__STATSIG__:{}),{Log:tr0.Log,SDK_VERSION:W_9.SDK_VERSION})});
var Co0=E((Wp)=>{var L_9=Wp&&Wp.__awaiter||function(A,B,Q,D){function Z(G){return G instanceof Q?G:new Q(function(F){F(G)})}return new(Q||(Q=Promise))(function(G,F){function I(J){try{W(D.next(J))}catch(X){F(X)}}function Y(J){try{W(D.throw(J))}catch(X){F(X)}}function W(J){J.done?G(J.value):Z(J.value).then(I,Y)}W((D=D.apply(A,B||[])).next())})};Object.defineProperty(Wp,"__esModule",{value:!0});Wp.StatsigEvaluationsDataAdapter=void 0;var lf=By(),M_9=wi1();class Vo0 extends lf.DataAdapterCore{constructor(){super("EvaluationsDataAdapter","evaluations");this._network=null,this._options=null}attach(A,B){super.attach(A,B),this._network=new M_9.default(B!==null&&B!==void 0?B:{})}getDataAsync(A,B,Q){return this._getDataAsyncImpl(A,lf._normalizeUser(B,this._options),Q)}prefetchData(A,B){return this._prefetchDataImpl(A,B)}setData(A){let B=lf._typedJsonParse(A,"has_updates","data");if(B&&"user"in B)super.setData(A,B.user);else lf.Log.error("StatsigUser not found. You may be using an older server SDK version. Please upgrade your SDK or use setDataLegacy.")}setDataLegacy(A,B){super.setData(A,B)}_fetchFromNetwork(A,B,Q,D){var Z;return L_9(this,void 0,void 0,function*(){let G=yield(Z=this._network)===null||Z===void 0?void 0:Z.fetchEvaluations(this._getSdkKey(),A,Q===null||Q===void 0?void 0:Q.priority,B,D);return G!==null&&G!==void 0?G:null})}_getCacheKey(A){var B;let Q=lf._getStorageKey(this._getSdkKey(),A,(B=this._options)===null||B===void 0?void 0:B.customUserCacheKeyFunc);return`${lf.DataAdapterCachePrefix}.${this._cacheSuffix}.${Q}`}_isCachedResultValidFor204(A,B){return A.fullUserHash!=null&&A.fullUserHash===lf._getFullUserHash(B)}}Wp.StatsigEvaluationsDataAdapter=Vo0});
var Di1=E((Dp)=>{var Bp=Dp&&Dp.__awaiter||function(A,B,Q,D){function Z(G){return G instanceof Q?G:new Q(function(F){F(G)})}return new(Q||(Q=Promise))(function(G,F){function I(J){try{W(D.next(J))}catch(X){F(X)}}function Y(J){try{W(D.throw(J))}catch(X){F(X)}}function W(J){J.done?G(J.value):Z(J.value).then(I,Y)}W((D=D.apply(A,B||[])).next())})};Object.defineProperty(Dp,"__esModule",{value:!0});Dp.EventLogger=void 0;var $y9=$B1(),qy9=el(),LB1=fW(),Ks0=qB1(),Qi1=uf(),Ny9=sp1(),Qp=UO(),Ly9=tp1(),Hs0=$V1(),My9=100,Ry9=1e4,Oy9=1000,Ty9=600000,Py9=500,zs0=200,MB1={},qV1={Startup:"startup",GainedFocus:"gained_focus"};class mf{static _safeFlushAndForget(A){var B;(B=MB1[A])===null||B===void 0||B.flush().catch(()=>{})}static _safeRetryFailedLogs(A){var B;(B=MB1[A])===null||B===void 0||B._retryFailedLogs(qV1.GainedFocus)}constructor(A,B,Q,D){var Z;this._sdkKey=A,this._emitter=B,this._network=Q,this._options=D,this._queue=[],this._lastExposureTimeMap={},this._nonExposedChecks={},this._hasRunQuickFlush=!1,this._creationTime=Date.now(),this._isLoggingDisabled=(D===null||D===void 0?void 0:D.disableLogging)===!0,this._maxQueueSize=(Z=D===null||D===void 0?void 0:D.loggingBufferMaxSize)!==null&&Z!==void 0?Z:My9;let G=D===null||D===void 0?void 0:D.networkConfig;this._logEventUrlConfig=new Ly9.UrlConfiguration(Ks0.Endpoint._rgstr,G===null||G===void 0?void 0:G.logEventUrl,G===null||G===void 0?void 0:G.api,G===null||G===void 0?void 0:G.logEventFallbackUrls)}setLoggingDisabled(A){this._isLoggingDisabled=A}enqueue(A){if(!this._shouldLogEvent(A))return;if(this._normalizeAndAppendEvent(A),this._quickFlushIfNeeded(),this._queue.length>this._maxQueueSize)mf._safeFlushAndForget(this._sdkKey)}incrementNonExposureCount(A){var B;let Q=(B=this._nonExposedChecks[A])!==null&&B!==void 0?B:0;this._nonExposedChecks[A]=Q+1}reset(){this._lastExposureTimeMap={}}start(){if(Qi1._isServerEnv())return;MB1[this._sdkKey]=this,Hs0._subscribeToVisiblityChanged((A)=>{if(A==="background")mf._safeFlushAndForget(this._sdkKey);else if(A==="foreground")mf._safeRetryFailedLogs(this._sdkKey)}),this._retryFailedLogs(qV1.Startup),this._startBackgroundFlushInterval()}stop(){return Bp(this,void 0,void 0,function*(){if(this._flushIntervalId)clearInterval(this._flushIntervalId),this._flushIntervalId=null;delete MB1[this._sdkKey],yield this.flush()})}flush(){return Bp(this,void 0,void 0,function*(){if(this._appendAndResetNonExposedChecks(),this._queue.length===0)return;let A=this._queue;this._queue=[],yield this._sendEvents(A)})}_quickFlushIfNeeded(){if(this._hasRunQuickFlush)return;if(this._hasRunQuickFlush=!0,Date.now()-this._creationTime>zs0)return;setTimeout(()=>mf._safeFlushAndForget(this._sdkKey),zs0)}_shouldLogEvent(A){if(Qi1._isServerEnv())return!1;if(!Ny9._isExposureEvent(A))return!0;let B=A.user?A.user:{statsigEnvironment:void 0},Q=$y9._getUserStorageKey(this._sdkKey,B),D=A.metadata?A.metadata:{},Z=[A.eventName,Q,D.gate,D.config,D.ruleID,D.allocatedExperiment,D.parameterName,String(D.isExplicitParameter),D.reason].join("|"),G=this._lastExposureTimeMap[Z],F=Date.now();if(G&&F-G<Ty9)return!1;if(Object.keys(this._lastExposureTimeMap).length>Oy9)this._lastExposureTimeMap={};return this._lastExposureTimeMap[Z]=F,!0}_sendEvents(A){var B,Q;return Bp(this,void 0,void 0,function*(){if(this._isLoggingDisabled)return this._saveFailedLogsToStorage(A),!1;try{let Z=Hs0._isUnloading()&&this._network.isBeaconSupported()&&((Q=(B=this._options)===null||B===void 0?void 0:B.networkConfig)===null||Q===void 0?void 0:Q.networkOverrideFunc)==null;if(this._emitter({name:"pre_logs_flushed",events:A}),(Z?yield this._sendEventsViaBeacon(A):yield this._sendEventsViaPost(A)).success)return this._emitter({name:"logs_flushed",events:A}),!0;else return LB1.Log.warn("Failed to flush events."),this._saveFailedLogsToStorage(A),!1}catch(D){return LB1.Log.warn("Failed to flush events."),!1}})}_sendEventsViaPost(A){var B;return Bp(this,void 0,void 0,function*(){let Q=yield this._network.post(this._getRequestData(A)),D=(B=Q===null||Q===void 0?void 0:Q.code)!==null&&B!==void 0?B:-1;return{success:D>=200&&D<300}})}_sendEventsViaBeacon(A){return Bp(this,void 0,void 0,function*(){return{success:yield this._network.beacon(this._getRequestData(A))}})}_getRequestData(A){return{sdkKey:this._sdkKey,data:{events:A},urlConfig:this._logEventUrlConfig,retries:3,isCompressable:!0,params:{[Ks0.NetworkParam.EventCount]:String(A.length)}}}_saveFailedLogsToStorage(A){while(A.length>Py9)A.shift();let B=this._getStorageKey();try{Qp._setObjectInStorage(B,A)}catch(Q){LB1.Log.warn("Unable to save failed logs to storage")}}_retryFailedLogs(A){let B=this._getStorageKey();(()=>Bp(this,void 0,void 0,function*(){if(!Qp.Storage.isReady())yield Qp.Storage.isReadyResolver();let Q=Qp._getObjectFromStorage(B);if(!Q)return;if(A===qV1.Startup)Qp.Storage.removeItem(B);if((yield this._sendEvents(Q))&&A===qV1.GainedFocus)Qp.Storage.removeItem(B)}))().catch(()=>{LB1.Log.warn("Failed to flush stored logs")})}_getStorageKey(){return`statsig.failed_logs.${qy9._DJB2(this._sdkKey)}`}_normalizeAndAppendEvent(A){if(A.user)A.user=Object.assign({},A.user),delete A.user.privateAttributes;let B={},Q=this._getCurrentPageUrl();if(Q)B.statsigMetadata={currentPage:Q};let D=Object.assign(Object.assign({},A),B);LB1.Log.debug("Enqueued Event:",D),this._queue.push(D)}_appendAndResetNonExposedChecks(){if(Object.keys(this._nonExposedChecks).length===0)return;this._normalizeAndAppendEvent({eventName:"statsig::non_exposed_checks",user:null,time:Date.now(),metadata:{checks:Object.assign({},this._nonExposedChecks)}}),this._nonExposedChecks={}}_getCurrentPageUrl(){var A;if(((A=this._options)===null||A===void 0?void 0:A.includeCurrentPageUrlWithEvents)===!1)return;return Qi1._getCurrentPageUrlSafe()}_startBackgroundFlushInterval(){var A,B;let Q=(B=(A=this._options)===null||A===void 0?void 0:A.loggingIntervalMs)!==null&&B!==void 0?B:Ry9,D=setInterval(()=>{let Z=MB1[this._sdkKey];if(!Z||Z._flushIntervalId!==D)clearInterval(D);else mf._safeFlushAndForget(this._sdkKey)},Q);this._flushIntervalId=D}}Dp.EventLogger=mf});
var Eo0=E((KN)=>{var P_9=KN&&KN.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),S_9=KN&&KN.__exportStar||function(A,B){for(var Q in A)if(Q!=="default"&&!Object.prototype.hasOwnProperty.call(B,Q))P_9(B,A,Q)};Object.defineProperty(KN,"__esModule",{value:!0});KN.StatsigClient=void 0;var zo0=Ho0();KN.StatsigClient=zo0.default;S_9(By(),KN);__STATSIG__=Object.assign(Object.assign({},__STATSIG__!==null&&__STATSIG__!==void 0?__STATSIG__:{}),{StatsigClient:zo0.default});KN.default=__STATSIG__});
var Fi1=E((ys0)=>{Object.defineProperty(ys0,"__esModule",{value:!0});ys0._typedJsonParse=void 0;var gy9=fW();function uy9(A,B,Q){try{let D=JSON.parse(A);if(D&&typeof D==="object"&&B in D)return D}catch(D){}return gy9.Log.error(`Failed to parse ${Q}`),null}ys0._typedJsonParse=uy9});
var Gi1=E((Ss0)=>{Object.defineProperty(Ss0,"__esModule",{value:!0});Ss0._getFullUserHash=Ss0._normalizeUser=void 0;var xy9=el(),vy9=fW();function by9(A,B,Q){try{let D=JSON.parse(JSON.stringify(A));if(B!=null&&B.environment!=null)D.statsigEnvironment=B.environment;else if(Q!=null)D.statsigEnvironment={tier:Q};return D}catch(D){return vy9.Log.error("Failed to JSON.stringify user"),{statsigEnvironment:void 0}}}Ss0._normalizeUser=by9;function fy9(A){return A?xy9._DJB2Object(A):null}Ss0._getFullUserHash=fy9});
var Go0=E((Do0)=>{Object.defineProperty(Do0,"__esModule",{value:!0});Do0._resolveDeltasResponse=void 0;var Qo0=By(),V_9=2;function C_9(A,B){let Q=Qo0._typedJsonParse(B,"checksum","DeltasEvaluationResponse");if(!Q)return{hadBadDeltaChecksum:!0};let D=K_9(A,Q),Z=H_9(D),G=Qo0._DJB2Object({feature_gates:Z.feature_gates,dynamic_configs:Z.dynamic_configs,layer_configs:Z.layer_configs},V_9);if(G!==Q.checksumV2)return{hadBadDeltaChecksum:!0,badChecksum:G,badMergedConfigs:Z,badFullResponse:Q.deltas_full_response};return JSON.stringify(Z)}Do0._resolveDeltasResponse=C_9;function K_9(A,B){return Object.assign(Object.assign(Object.assign({},A),B),{feature_gates:Object.assign(Object.assign({},A.feature_gates),B.feature_gates),layer_configs:Object.assign(Object.assign({},A.layer_configs),B.layer_configs),dynamic_configs:Object.assign(Object.assign({},A.dynamic_configs),B.dynamic_configs)})}function H_9(A){let B=A;return Ui1(A.deleted_gates,B.feature_gates),delete B.deleted_gates,Ui1(A.deleted_configs,B.dynamic_configs),delete B.deleted_configs,Ui1(A.deleted_layers,B.layer_configs),delete B.deleted_layers,B}function Ui1(A,B){A===null||A===void 0||A.forEach((Q)=>{delete B[Q]})}});
var Ho0=E((TB1)=>{var qi1=TB1&&TB1.__awaiter||function(A,B,Q,D){function Z(G){return G instanceof Q?G:new Q(function(F){F(G)})}return new(Q||(Q=Promise))(function(G,F){function I(J){try{W(D.next(J))}catch(X){F(X)}}function Y(J){try{W(D.throw(J))}catch(X){F(X)}}function W(J){J.done?G(J.value):Z(J.value).then(I,Y)}W((D=D.apply(A,B||[])).next())})};Object.defineProperty(TB1,"__esModule",{value:!0});var a4=By(),R_9=Bo0(),O_9=wi1(),Ko0=Xo0(),T_9=Co0();class bV1 extends a4.StatsigClientBase{static instance(A){let B=a4._getStatsigGlobal().instance(A);if(B instanceof bV1)return B;return a4.Log.warn(a4._isServerEnv()?"StatsigClient.instance is not supported in server environments":"Unable to find StatsigClient instance"),new bV1(A!==null&&A!==void 0?A:"",{})}constructor(A,B,Q=null){var D,Z;a4.SDKType._setClientType(A,"javascript-client");let G=new O_9.default(Q,(I)=>{this.$emt(I)});super(A,(D=Q===null||Q===void 0?void 0:Q.dataAdapter)!==null&&D!==void 0?D:new T_9.StatsigEvaluationsDataAdapter,G,Q);this.getFeatureGate=this._memoize(a4.MemoPrefix._gate,this._getFeatureGateImpl.bind(this)),this.getDynamicConfig=this._memoize(a4.MemoPrefix._dynamicConfig,this._getDynamicConfigImpl.bind(this)),this.getExperiment=this._memoize(a4.MemoPrefix._experiment,this._getExperimentImpl.bind(this)),this.getLayer=this._memoize(a4.MemoPrefix._layer,this._getLayerImpl.bind(this)),this.getParameterStore=this._memoize(a4.MemoPrefix._paramStore,this._getParameterStoreImpl.bind(this)),this._store=new R_9.default(A),this._network=G,this._user=this._configureUser(B,Q);let F=(Z=Q===null||Q===void 0?void 0:Q.plugins)!==null&&Z!==void 0?Z:[];for(let I of F)I.bind(this)}initializeSync(A){var B;if(this.loadingStatus!=="Uninitialized")return a4.createUpdateDetails(!0,this._store.getSource(),-1,null,null,["MultipleInitializations",...(B=this._store.getWarnings())!==null&&B!==void 0?B:[]]);return this._logger.start(),this.updateUserSync(this._user,A)}initializeAsync(A){return qi1(this,void 0,void 0,function*(){if(this._initializePromise)return this._initializePromise;return this._initializePromise=this._initializeAsyncImpl(A),this._initializePromise})}updateUserSync(A,B){var Q;let D=performance.now(),Z=[...(Q=this._store.getWarnings())!==null&&Q!==void 0?Q:[]];this._resetForUser(A);let G=this.dataAdapter.getDataSync(this._user);if(G==null)Z.push("NoCachedValues");this._store.setValues(G,this._user),this._finalizeUpdate(G);let F=B===null||B===void 0?void 0:B.disableBackgroundCacheRefresh;if(F===!0||F==null&&(G===null||G===void 0?void 0:G.source)==="Bootstrap")return a4.createUpdateDetails(!0,this._store.getSource(),performance.now()-D,this._errorBoundary.getLastSeenErrorAndReset(),this._network.getLastUsedInitUrlAndReset(),Z);return this._runPostUpdate(G!==null&&G!==void 0?G:null,this._user),a4.createUpdateDetails(!0,this._store.getSource(),performance.now()-D,this._errorBoundary.getLastSeenErrorAndReset(),this._network.getLastUsedInitUrlAndReset(),Z)}updateUserAsync(A,B){return qi1(this,void 0,void 0,function*(){this._resetForUser(A);let Q=this._user;a4.Diagnostics._markInitOverallStart(this._sdkKey);let D=this.dataAdapter.getDataSync(Q);if(this._store.setValues(D,this._user),this._setStatus("Loading",D),D=yield this.dataAdapter.getDataAsync(D,Q,B),Q!==this._user)return a4.createUpdateDetails(!1,this._store.getSource(),-1,new Error("User changed during update"),this._network.getLastUsedInitUrlAndReset());let Z=!1;if(D!=null)a4.Diagnostics._markInitProcessStart(this._sdkKey),Z=this._store.setValues(D,this._user),a4.Diagnostics._markInitProcessEnd(this._sdkKey,{success:Z});if(this._finalizeUpdate(D),!Z)this._errorBoundary.attachErrorIfNoneExists(a4.UPDATE_DETAIL_ERROR_MESSAGES.NO_NETWORK_DATA),this.$emt({name:"initialization_failure"});a4.Diagnostics._markInitOverallEnd(this._sdkKey,Z,this._store.getCurrentSourceDetails());let G=a4.Diagnostics._enqueueDiagnosticsEvent(this._user,this._logger,this._sdkKey,this._options);return a4.createUpdateDetails(Z,this._store.getSource(),G,this._errorBoundary.getLastSeenErrorAndReset(),this._network.getLastUsedInitUrlAndReset(),this._store.getWarnings())})}getContext(){return{sdkKey:this._sdkKey,options:this._options,values:this._store.getValues(),user:JSON.parse(JSON.stringify(this._user)),errorBoundary:this._errorBoundary,session:a4.StatsigSession.get(this._sdkKey),stableID:a4.StableID.get(this._sdkKey)}}checkGate(A,B){return this.getFeatureGate(A,B).value}logEvent(A,B,Q){let D=typeof A==="string"?{eventName:A,value:B,metadata:Q}:A;this._logger.enqueue(Object.assign(Object.assign({},D),{user:this._user,time:Date.now()}))}_primeReadyRipcord(){this.$on("error",()=>{this.loadingStatus==="Loading"&&this._finalizeUpdate(null)})}_initializeAsyncImpl(A){return qi1(this,void 0,void 0,function*(){if(!a4.Storage.isReady())yield a4.Storage.isReadyResolver();return this._logger.start(),this.updateUserAsync(this._user,A)})}_finalizeUpdate(A){this._store.finalize(),this._setStatus("Ready",A)}_runPostUpdate(A,B){this.dataAdapter.getDataAsync(A,B,{priority:"low"}).catch((Q)=>{a4.Log.error("An error occurred after update.",Q)})}_resetForUser(A){this._logger.reset(),this._store.reset(),this._user=this._configureUser(A,this._options)}_configureUser(A,B){var Q;let D=a4._normalizeUser(A,B),Z=(Q=D.customIDs)===null||Q===void 0?void 0:Q.stableID;if(Z)a4.StableID.setOverride(Z,this._sdkKey);return D}_getFeatureGateImpl(A,B){var Q,D;let{result:Z,details:G}=this._store.getGate(A),F=a4._makeFeatureGate(A,G,Z),I=(D=(Q=this.overrideAdapter)===null||Q===void 0?void 0:Q.getGateOverride)===null||D===void 0?void 0:D.call(Q,F,this._user,B),Y=I!==null&&I!==void 0?I:F;return this._enqueueExposure(A,a4._createGateExposure(this._user,Y,this._store.getExposureMapping()),B),this.$emt({name:"gate_evaluation",gate:Y}),Y}_getDynamicConfigImpl(A,B){var Q,D;let{result:Z,details:G}=this._store.getConfig(A),F=a4._makeDynamicConfig(A,G,Z),I=(D=(Q=this.overrideAdapter)===null||Q===void 0?void 0:Q.getDynamicConfigOverride)===null||D===void 0?void 0:D.call(Q,F,this._user,B),Y=I!==null&&I!==void 0?I:F;return this._enqueueExposure(A,a4._createConfigExposure(this._user,Y,this._store.getExposureMapping()),B),this.$emt({name:"dynamic_config_evaluation",dynamicConfig:Y}),Y}_getExperimentImpl(A,B){var Q,D,Z,G;let{result:F,details:I}=this._store.getConfig(A),Y=a4._makeExperiment(A,I,F);if(Y.__evaluation!=null)Y.__evaluation.secondary_exposures=a4._mapExposures((D=(Q=Y.__evaluation)===null||Q===void 0?void 0:Q.secondary_exposures)!==null&&D!==void 0?D:[],this._store.getExposureMapping());let W=(G=(Z=this.overrideAdapter)===null||Z===void 0?void 0:Z.getExperimentOverride)===null||G===void 0?void 0:G.call(Z,Y,this._user,B),J=W!==null&&W!==void 0?W:Y;return this._enqueueExposure(A,a4._createConfigExposure(this._user,J,this._store.getExposureMapping()),B),this.$emt({name:"experiment_evaluation",experiment:J}),J}_getLayerImpl(A,B){var Q,D,Z;let{result:G,details:F}=this._store.getLayer(A),I=a4._makeLayer(A,F,G),Y=(D=(Q=this.overrideAdapter)===null||Q===void 0?void 0:Q.getLayerOverride)===null||D===void 0?void 0:D.call(Q,I,this._user,B);if(B===null||B===void 0?void 0:B.disableExposureLog)this._logger.incrementNonExposureCount(A);let W=a4._mergeOverride(I,Y,(Z=Y===null||Y===void 0?void 0:Y.__value)!==null&&Z!==void 0?Z:I.__value,(J)=>{if(B===null||B===void 0?void 0:B.disableExposureLog)return;this._enqueueExposure(A,a4._createLayerParameterExposure(this._user,W,J,this._store.getExposureMapping()),B)});return this.$emt({name:"layer_evaluation",layer:W}),W}_getParameterStoreImpl(A,B){var Q,D;let{result:Z,details:G}=this._store.getParamStore(A);this._logger.incrementNonExposureCount(A);let F={name:A,details:G,__configuration:Z,get:Ko0._makeParamStoreGetter(this,Z,B)},I=(D=(Q=this.overrideAdapter)===null||Q===void 0?void 0:Q.getParamStoreOverride)===null||D===void 0?void 0:D.call(Q,F,B);if(I!=null)F.__configuration=I.config,F.details=I.details,F.get=Ko0._makeParamStoreGetter(this,I.config,B);return F}}TB1.default=bV1});
var JV1=E((la0)=>{Object.defineProperty(la0,"__esModule",{value:!0});la0._isTypeMatch=la0._typeOf=void 0;function yj9(A){return Array.isArray(A)?"array":typeof A}la0._typeOf=yj9;function kj9(A,B){let Q=(D)=>Array.isArray(D)?"array":typeof D;return Q(A)===Q(B)}la0._isTypeMatch=kj9});
var Ki1=E((wr0)=>{Object.defineProperty(wr0,"__esModule",{value:!0});wr0.ErrorTag=void 0;wr0.ErrorTag={NetworkError:"NetworkError"}});
var MV1=E((Ts0)=>{Object.defineProperty(Ts0,"__esModule",{value:!0});Ts0.StableID=void 0;var jy9=$B1(),yy9=fW(),Rs0=UO(),ky9=NV1(),LV1={};Ts0.StableID={get:(A)=>{if(LV1[A]==null){let B=_y9(A);if(B==null)B=ky9.getUUID(),Ms0(B,A);LV1[A]=B}return LV1[A]},setOverride:(A,B)=>{LV1[B]=A,Ms0(A,B)}};function Os0(A){return`statsig.stable_id.${jy9._getStorageKey(A)}`}function Ms0(A,B){let Q=Os0(B);try{Rs0._setObjectInStorage(Q,A)}catch(D){yy9.Log.warn("Failed to save StableID")}}function _y9(A){let B=Os0(A);return Rs0._getObjectFromStorage(B)}});
var NV1=E((Ns0)=>{Object.defineProperty(Ns0,"__esModule",{value:!0});Ns0.getUUID=void 0;function Sy9(){if(typeof crypto!=="undefined"&&typeof crypto.randomUUID==="function")return crypto.randomUUID();let A=new Date().getTime(),B=typeof performance!=="undefined"&&performance.now&&performance.now()*1000||0;return`xxxxxxxx-xxxx-4xxx-${"89ab"[Math.floor(Math.random()*4)]}xxx-xxxxxxxxxxxx`.replace(/[xy]/g,(D)=>{let Z=Math.random()*16;if(A>0)Z=(A+Z)%16|0,A=Math.floor(A/16);else Z=(B+Z)%16|0,B=Math.floor(B/16);return(D==="x"?Z:Z&7|8).toString(16)})}Ns0.getUUID=Sy9});
var PV1=E((ds0)=>{Object.defineProperty(ds0,"__esModule",{value:!0});ds0.SDKType=void 0;var ms0={},Zp;ds0.SDKType={_get:(A)=>{var B;return((B=ms0[A])!==null&&B!==void 0?B:"js-mono")+(Zp!==null&&Zp!==void 0?Zp:"")},_setClientType(A,B){ms0[A]=B},_setBindingType(A){if(!Zp||Zp==="-react")Zp="-"+A}}});
var RB1=E((Es0)=>{Object.defineProperty(Es0,"__esModule",{value:!0});Es0.StatsigMetadataProvider=Es0.SDK_VERSION=void 0;Es0.SDK_VERSION="3.12.1";var Zi1={sdkVersion:Es0.SDK_VERSION,sdkType:"js-mono"};Es0.StatsigMetadataProvider={get:()=>Zi1,add:(A)=>{Zi1=Object.assign(Object.assign({},Zi1),A)}}});
var Sr0=E((Ip)=>{var Fp=Ip&&Ip.__awaiter||function(A,B,Q,D){function Z(G){return G instanceof Q?G:new Q(function(F){F(G)})}return new(Q||(Q=Promise))(function(G,F){function I(J){try{W(D.next(J))}catch(X){F(X)}}function Y(J){try{W(D.throw(J))}catch(X){F(X)}}function W(J){J.done?G(J.value):Z(J.value).then(I,Y)}W((D=D.apply(A,B||[])).next())})};Object.defineProperty(Ip,"__esModule",{value:!0});Ip.NetworkCore=void 0;gf();var qr0=gf(),Hi1=WV1(),df=fW(),Gw=qB1(),$k9=Yr0(),qk9=Vi1(),Mr0=PV1(),Nk9=uf(),Rr0=jV1(),Lk9=MV1(),Mk9=Ki1(),Or0=RB1(),Rk9=$V1(),Ok9=1e4,Tk9=500,Pk9=30000,Sk9=1000,Tr0=50,jk9=Tr0/Sk9,yk9=new Set([408,500,502,503,504,522,524,599]);class Pr0{constructor(A,B){if(this._emitter=B,this._errorBoundary=null,this._timeout=Ok9,this._netConfig={},this._options={},this._leakyBucket={},this._lastUsedInitUrl=null,A)this._options=A;if(this._options.networkConfig)this._netConfig=this._options.networkConfig;if(this._netConfig.networkTimeoutMs)this._timeout=this._netConfig.networkTimeoutMs;this._fallbackResolver=new $k9.NetworkFallbackResolver(this._options)}setErrorBoundary(A){this._errorBoundary=A,this._errorBoundary.wrap(this),this._errorBoundary.wrap(this._fallbackResolver),this._fallbackResolver.setErrorBoundary(A)}isBeaconSupported(){return typeof navigator!=="undefined"&&typeof navigator.sendBeacon==="function"}getLastUsedInitUrlAndReset(){let A=this._lastUsedInitUrl;return this._lastUsedInitUrl=null,A}beacon(A){return Fp(this,void 0,void 0,function*(){if(!Nr0(A))return!1;let B=this._getInternalRequestArgs("POST",A);yield this._tryToCompressBody(B);let Q=yield this._getPopulatedURL(B),D=navigator;return D.sendBeacon.bind(D)(Q,B.body)})}post(A){return Fp(this,void 0,void 0,function*(){let B=this._getInternalRequestArgs("POST",A);return this._tryEncodeBody(B),yield this._tryToCompressBody(B),this._sendRequest(B)})}get(A){let B=this._getInternalRequestArgs("GET",A);return this._sendRequest(B)}_sendRequest(A){var B,Q,D,Z;return Fp(this,void 0,void 0,function*(){if(!Nr0(A))return null;if(this._netConfig.preventAllNetworkTraffic)return null;let{method:G,body:F,retries:I,attempt:Y}=A,W=A.urlConfig.endpoint;if(this._isRateLimited(W))return df.Log.warn(`Request to ${W} was blocked because you are making requests too frequently.`),null;let J=Y!==null&&Y!==void 0?Y:1,X=typeof AbortController!=="undefined"?new AbortController:null,V=setTimeout(()=>{X===null||X===void 0||X.abort(`Timeout of ${this._timeout}ms expired.`)},this._timeout),C=yield this._getPopulatedURL(A),K=null,H=Rk9._isUnloading();try{let z={method:G,body:F,headers:Object.assign({},A.headers),signal:X===null||X===void 0?void 0:X.signal,priority:A.priority,keepalive:H};vk9(A,J);let $=this._leakyBucket[W];if($)$.lastRequestTime=Date.now(),this._leakyBucket[W]=$;if(K=yield((B=this._netConfig.networkOverrideFunc)!==null&&B!==void 0?B:fetch)(C,z),clearTimeout(V),!K.ok){let O=yield K.text().catch(()=>"No Text"),R=new Error(`NetworkError: ${C} ${O}`);throw R.name="NetworkError",R}let N=yield K.text();return Lr0(A,K,J,N),this._fallbackResolver.tryBumpExpiryTime(A.sdkKey,A.urlConfig),{body:N,code:K.status}}catch(z){let $=_k9(X,z),L=xk9(X);if(Lr0(A,K,J,"",z),yield this._fallbackResolver.tryFetchUpdatedFallbackInfo(A.sdkKey,A.urlConfig,$,L))A.fallbackUrl=this._fallbackResolver.getActiveFallbackUrl(A.sdkKey,A.urlConfig);if(!I||J>I||!yk9.has((Q=K===null||K===void 0?void 0:K.status)!==null&&Q!==void 0?Q:500)){(D=this._emitter)===null||D===void 0||D.call(this,{name:"error",error:z,tag:Mk9.ErrorTag.NetworkError,requestArgs:A});let O=`A networking error occurred during ${G} request to ${C}.`;return df.Log.error(O,$,z),(Z=this._errorBoundary)===null||Z===void 0||Z.attachErrorIfNoneExists(O),null}return yield bk9(J),this._sendRequest(Object.assign(Object.assign({},A),{retries:I,attempt:J+1}))}})}_isRateLimited(A){var B;let Q=Date.now(),D=(B=this._leakyBucket[A])!==null&&B!==void 0?B:{count:0,lastRequestTime:Q},Z=Q-D.lastRequestTime,G=Math.floor(Z*jk9);if(D.count=Math.max(0,D.count-G),D.count>=Tr0)return!0;return D.count+=1,D.lastRequestTime=Q,this._leakyBucket[A]=D,!1}_getPopulatedURL(A){var B;return Fp(this,void 0,void 0,function*(){let Q=(B=A.fallbackUrl)!==null&&B!==void 0?B:A.urlConfig.getUrl();if(A.urlConfig.endpoint===Gw.Endpoint._initialize||A.urlConfig.endpoint===Gw.Endpoint._download_config_specs)this._lastUsedInitUrl=Q;let D=Object.assign({[Gw.NetworkParam.SdkKey]:A.sdkKey,[Gw.NetworkParam.SdkType]:Mr0.SDKType._get(A.sdkKey),[Gw.NetworkParam.SdkVersion]:Or0.SDK_VERSION,[Gw.NetworkParam.Time]:String(Date.now()),[Gw.NetworkParam.SessionID]:Rr0.SessionID.get(A.sdkKey)},A.params),Z=Object.keys(D).map((G)=>{return`${encodeURIComponent(G)}=${encodeURIComponent(D[G])}`}).join("&");return`${Q}${Z?`?${Z}`:""}`})}_tryEncodeBody(A){var B;let Q=Nk9._getWindowSafe(),D=A.body;if(!A.isStatsigEncodable||this._options.disableStatsigEncoding||typeof D!=="string"||qr0._getStatsigGlobalFlag("no-encode")!=null||!(Q===null||Q===void 0?void 0:Q.btoa))return;try{A.body=Q.btoa(D).split("").reverse().join(""),A.params=Object.assign(Object.assign({},(B=A.params)!==null&&B!==void 0?B:{}),{[Gw.NetworkParam.StatsigEncoded]:"1"})}catch(Z){df.Log.warn(`Request encoding failed for ${A.urlConfig.getUrl()}`,Z)}}_tryToCompressBody(A){var B;return Fp(this,void 0,void 0,function*(){let Q=A.body;if(!A.isCompressable||this._options.disableCompression||typeof Q!=="string"||qk9.SDKFlags.get(A.sdkKey,"enable_log_event_compression")!==!0||qr0._getStatsigGlobalFlag("no-compress")!=null||typeof CompressionStream==="undefined"||typeof TextEncoder==="undefined")return;try{let D=new TextEncoder().encode(Q),Z=new CompressionStream("gzip"),G=Z.writable.getWriter();G.write(D).catch(df.Log.error),G.close().catch(df.Log.error);let F=Z.readable.getReader(),I=[],Y;while(!(Y=yield F.read()).done)I.push(Y.value);let W=I.reduce((V,C)=>V+C.length,0),J=new Uint8Array(W),X=0;for(let V of I)J.set(V,X),X+=V.length;A.body=J,A.params=Object.assign(Object.assign({},(B=A.params)!==null&&B!==void 0?B:{}),{[Gw.NetworkParam.IsGzipped]:"1"})}catch(D){df.Log.warn(`Request compression failed for ${A.urlConfig.getUrl()}`,D)}})}_getInternalRequestArgs(A,B){let Q=this._fallbackResolver.getActiveFallbackUrl(B.sdkKey,B.urlConfig),D=Object.assign(Object.assign({},B),{method:A,fallbackUrl:Q});if("data"in B)kk9(D,B.data);return D}}Ip.NetworkCore=Pr0;var Nr0=(A)=>{if(!A.sdkKey)return df.Log.warn("Unable to make request without an SDK key"),!1;return!0},kk9=(A,B)=>{let{sdkKey:Q,fallbackUrl:D}=A,Z=Lk9.StableID.get(Q),G=Rr0.SessionID.get(Q),F=Mr0.SDKType._get(Q);A.body=JSON.stringify(Object.assign(Object.assign({},B),{statsigMetadata:Object.assign(Object.assign({},Or0.StatsigMetadataProvider.get()),{stableID:Z,sessionID:G,sdkType:F,fallbackUrl:D})}))};function _k9(A,B){if((A===null||A===void 0?void 0:A.signal.aborted)&&typeof A.signal.reason==="string")return A.signal.reason;if(typeof B==="string")return B;if(B instanceof Error)return`${B.name}: ${B.message}`;return"Unknown Error"}function xk9(A){return(A===null||A===void 0?void 0:A.signal.aborted)&&typeof A.signal.reason==="string"&&A.signal.reason.includes("Timeout")||!1}function vk9(A,B){if(A.urlConfig.endpoint!==Gw.Endpoint._initialize)return;Hi1.Diagnostics._markInitNetworkReqStart(A.sdkKey,{attempt:B})}function Lr0(A,B,Q,D,Z){if(A.urlConfig.endpoint!==Gw.Endpoint._initialize)return;Hi1.Diagnostics._markInitNetworkReqEnd(A.sdkKey,Hi1.Diagnostics._getDiagnosticsData(B,Q,D,Z))}function bk9(A){return Fp(this,void 0,void 0,function*(){yield new Promise((B)=>setTimeout(B,Math.min(Tk9*(A*A),Pk9)))})}});
var UO=E((Is0)=>{Object.defineProperty(Is0,"__esModule",{value:!0});Is0._setObjectInStorage=Is0._getObjectFromStorage=Is0.Storage=void 0;var Fy9=fW(),Iy9=uf(),NB1={},op1={isReady:()=>!0,isReadyResolver:()=>null,getProviderName:()=>"InMemory",getItem:(A)=>NB1[A]?NB1[A]:null,setItem:(A,B)=>{NB1[A]=B},removeItem:(A)=>{delete NB1[A]},getAllKeys:()=>Object.keys(NB1)},HV1=null;try{let A=Iy9._getWindowSafe();if(A&&A.localStorage&&typeof A.localStorage.getItem==="function")HV1={isReady:()=>!0,isReadyResolver:()=>null,getProviderName:()=>"LocalStorage",getItem:(B)=>A.localStorage.getItem(B),setItem:(B,Q)=>A.localStorage.setItem(B,Q),removeItem:(B)=>A.localStorage.removeItem(B),getAllKeys:()=>Object.keys(A.localStorage)}}catch(A){Fy9.Log.warn("Failed to setup localStorageProvider.")}var rp1=HV1!==null&&HV1!==void 0?HV1:op1,CN=rp1;function Yy9(A){try{return A()}catch(B){if(B instanceof Error&&B.name==="SecurityError")return Is0.Storage._setProvider(op1),null;throw B}}Is0.Storage={isReady:()=>CN.isReady(),isReadyResolver:()=>CN.isReadyResolver(),getProviderName:()=>CN.getProviderName(),getItem:(A)=>Yy9(()=>CN.getItem(A)),setItem:(A,B)=>CN.setItem(A,B),removeItem:(A)=>CN.removeItem(A),getAllKeys:()=>CN.getAllKeys(),_setProvider:(A)=>{rp1=A,CN=A},_setDisabled:(A)=>{if(A)CN=op1;else CN=rp1}};function Wy9(A){let B=Is0.Storage.getItem(A);return JSON.parse(B!==null&&B!==void 0?B:"null")}Is0._getObjectFromStorage=Wy9;function Jy9(A,B){Is0.Storage.setItem(A,JSON.stringify(B))}Is0._setObjectInStorage=Jy9});
var Vi1=E((Jr0)=>{Object.defineProperty(Jr0,"__esModule",{value:!0});Jr0.SDKFlags=void 0;var Wr0={};Jr0.SDKFlags={setFlags:(A,B)=>{Wr0[A]=B},get:(A,B)=>{var Q,D;return(D=(Q=Wr0[A])===null||Q===void 0?void 0:Q[B])!==null&&D!==void 0?D:!1}}});
var WV1=E((ca0)=>{Object.defineProperty(ca0,"__esModule",{value:!0});ca0.Diagnostics=void 0;var YV1=new Map,lp1="start",pp1="end",Sj9="statsig::diagnostics";ca0.Diagnostics={_getMarkers:(A)=>{return YV1.get(A)},_markInitOverallStart:(A)=>{tl(A,ol({},lp1,"overall"))},_markInitOverallEnd:(A,B,Q)=>{tl(A,ol({success:B,error:B?void 0:{name:"InitializeError",message:"Failed to initialize"},evaluationDetails:Q},pp1,"overall"))},_markInitNetworkReqStart:(A,B)=>{tl(A,ol(B,lp1,"initialize","network_request"))},_markInitNetworkReqEnd:(A,B)=>{tl(A,ol(B,pp1,"initialize","network_request"))},_markInitProcessStart:(A)=>{tl(A,ol({},lp1,"initialize","process"))},_markInitProcessEnd:(A,B)=>{tl(A,ol(B,pp1,"initialize","process"))},_clearMarkers:(A)=>{YV1.delete(A)},_formatError(A){if(!(A&&typeof A==="object"))return;return{code:ip1(A,"code"),name:ip1(A,"name"),message:ip1(A,"message")}},_getDiagnosticsData(A,B,Q,D){var Z;return{success:(A===null||A===void 0?void 0:A.ok)===!0,statusCode:A===null||A===void 0?void 0:A.status,sdkRegion:(Z=A===null||A===void 0?void 0:A.headers)===null||Z===void 0?void 0:Z.get("x-statsig-region"),isDelta:Q.includes('"is_delta":true')===!0?!0:void 0,attempt:B,error:ca0.Diagnostics._formatError(D)}},_enqueueDiagnosticsEvent(A,B,Q,D){let Z=ca0.Diagnostics._getMarkers(Q);if(Z==null||Z.length<=0)return-1;let G=Z[Z.length-1].timestamp-Z[0].timestamp;ca0.Diagnostics._clearMarkers(Q);let F=jj9(A,{context:"initialize",markers:Z.slice(),statsigOptions:D});return B.enqueue(F),G}};function ol(A,B,Q,D){return Object.assign({key:Q,action:B,step:D,timestamp:Date.now()},A)}function jj9(A,B){return{eventName:Sj9,user:A,value:null,metadata:B,time:Date.now()}}function tl(A,B){var Q;let D=(Q=YV1.get(A))!==null&&Q!==void 0?Q:[];D.push(B),YV1.set(A,D)}function ip1(A,B){if(B in A)return A[B];return}});
var Wi1=E((es0)=>{Object.defineProperty(es0,"__esModule",{value:!0});es0.createMemoKey=es0.MemoPrefix=void 0;es0.MemoPrefix={_gate:"g",_dynamicConfig:"c",_experiment:"e",_layer:"l",_paramStore:"p"};var ay9=new Set([]),sy9=new Set(["userPersistedValues"]);function ry9(A,B,Q){let D=`${A}|${B}`;if(!Q)return D;for(let Z of Object.keys(Q)){if(sy9.has(Z))return;if(ay9.has(Z))D+=`|${Z}=true`;else D+=`|${Z}=${Q[Z]}`}return D}es0.createMemoKey=ry9});
var Xo0=E((Wo0)=>{Object.defineProperty(Wo0,"__esModule",{value:!0});Wo0._makeParamStoreGetter=void 0;var Yo0=By(),xV1={disableExposureLog:!0};function vV1(A){return A==null||A.disableExposureLog===!1}function $i1(A,B){return B!=null&&!Yo0._isTypeMatch(A,B)}function E_9(A,B){return A.value}function U_9(A,B,Q){if(A.getFeatureGate(B.gate_name,vV1(Q)?void 0:xV1).value)return B.pass_value;return B.fail_value}function w_9(A,B,Q,D){let G=A.getDynamicConfig(B.config_name,xV1).get(B.param_name);if($i1(G,Q))return Q;if(vV1(D))A.getDynamicConfig(B.config_name);return G}function $_9(A,B,Q,D){let G=A.getExperiment(B.experiment_name,xV1).get(B.param_name);if($i1(G,Q))return Q;if(vV1(D))A.getExperiment(B.experiment_name);return G}function q_9(A,B,Q,D){let G=A.getLayer(B.layer_name,xV1).get(B.param_name);if($i1(G,Q))return Q;if(vV1(D))A.getLayer(B.layer_name).get(B.param_name);return G}function N_9(A,B,Q){return(D,Z)=>{if(B==null)return Z;let G=B[D];if(G==null||Z!=null&&Yo0._typeOf(Z)!==G.param_type)return Z;switch(G.ref_type){case"static":return E_9(G,Q);case"gate":return U_9(A,G,Q);case"dynamic_config":return w_9(A,G,Z,Q);case"experiment":return $_9(A,G,Z,Q);case"layer":return q_9(A,G,Z,Q);default:return Z}}}Wo0._makeParamStoreGetter=N_9});
var Yi1=E((wO)=>{var dy9=wO&&wO.__awaiter||function(A,B,Q,D){function Z(G){return G instanceof Q?G:new Q(function(F){F(G)})}return new(Q||(Q=Promise))(function(G,F){function I(J){try{W(D.next(J))}catch(X){F(X)}}function Y(J){try{W(D.throw(J))}catch(X){F(X)}}function W(J){J.done?G(J.value):Z(J.value).then(I,Y)}W((D=D.apply(A,B||[])).next())})};Object.defineProperty(wO,"__esModule",{value:!0});wO.ErrorBoundary=wO.EXCEPTION_ENDPOINT=void 0;var cy9=fW(),ly9=PV1(),py9=RB1();wO.EXCEPTION_ENDPOINT="https://statsigapi.net/v1/sdk_exception";var ps0="[Statsig] UnknownError";class is0{constructor(A,B,Q,D){this._sdkKey=A,this._options=B,this._emitter=Q,this._lastSeenError=D,this._seen=new Set}wrap(A){try{let B=A;ny9(B).forEach((Q)=>{let D=B[Q];if("$EB"in D)return;B[Q]=(...Z)=>{return this._capture(Q,()=>D.apply(A,Z))},B[Q].$EB=!0})}catch(B){this._onError("eb:wrap",B)}}logError(A,B){this._onError(A,B)}getLastSeenErrorAndReset(){let A=this._lastSeenError;return this._lastSeenError=void 0,A!==null&&A!==void 0?A:null}attachErrorIfNoneExists(A){if(this._lastSeenError)return;this._lastSeenError=ls0(A)}_capture(A,B){try{let Q=B();if(Q&&Q instanceof Promise)return Q.catch((D)=>this._onError(A,D));return Q}catch(Q){return this._onError(A,Q),null}}_onError(A,B){try{cy9.Log.warn(`Caught error in ${A}`,{error:B}),(()=>dy9(this,void 0,void 0,function*(){var D,Z,G,F,I,Y,W;let J=B?B:Error(ps0),X=J instanceof Error,V=X?J.name:"No Name",C=ls0(J);if(this._lastSeenError=C,this._seen.has(V))return;if(this._seen.add(V),(Z=(D=this._options)===null||D===void 0?void 0:D.networkConfig)===null||Z===void 0?void 0:Z.preventAllNetworkTraffic){(G=this._emitter)===null||G===void 0||G.call(this,{name:"error",error:B,tag:A});return}let K=ly9.SDKType._get(this._sdkKey),H=py9.StatsigMetadataProvider.get(),z=X?J.stack:iy9(J),$=JSON.stringify(Object.assign({tag:A,exception:V,info:z},Object.assign(Object.assign({},H),{sdkType:K})));yield((Y=(I=(F=this._options)===null||F===void 0?void 0:F.networkConfig)===null||I===void 0?void 0:I.networkOverrideFunc)!==null&&Y!==void 0?Y:fetch)(wO.EXCEPTION_ENDPOINT,{method:"POST",headers:{"STATSIG-API-KEY":this._sdkKey,"STATSIG-SDK-TYPE":String(K),"STATSIG-SDK-VERSION":String(H.sdkVersion),"Content-Type":"application/json"},body:$}),(W=this._emitter)===null||W===void 0||W.call(this,{name:"error",error:B,tag:A})}))().then(()=>{}).catch(()=>{})}catch(Q){}}}wO.ErrorBoundary=is0;function ls0(A){if(A instanceof Error)return A;else if(typeof A==="string")return new Error(A);else return new Error("An unknown error occurred.")}function iy9(A){try{return JSON.stringify(A)}catch(B){return ps0}}function ny9(A){let B=new Set,Q=Object.getPrototypeOf(A);while(Q&&Q!==Object.prototype)Object.getOwnPropertyNames(Q).filter((D)=>typeof(Q===null||Q===void 0?void 0:Q[D])==="function").forEach((D)=>B.add(D)),Q=Object.getPrototypeOf(Q);return Array.from(B)}});
var Yr0=E((Ay)=>{var Qr0=Ay&&Ay.__awaiter||function(A,B,Q,D){function Z(G){return G instanceof Q?G:new Q(function(F){F(G)})}return new(Q||(Q=Promise))(function(G,F){function I(J){try{W(D.next(J))}catch(X){F(X)}}function Y(J){try{W(D.throw(J))}catch(X){F(X)}}function W(J){J.done?G(J.value):Z(J.value).then(I,Y)}W((D=D.apply(A,B||[])).next())})};Object.defineProperty(Ay,"__esModule",{value:!0});Ay._isDomainFailure=Ay.NetworkFallbackResolver=void 0;var Gk9=Br0(),Fk9=el(),Ik9=fW(),Xi1=UO(),Dr0=604800000,Yk9=14400000;class Gr0{constructor(A){var B;this._fallbackInfo=null,this._errorBoundary=null,this._dnsQueryCooldowns={},this._networkOverrideFunc=(B=A.networkConfig)===null||B===void 0?void 0:B.networkOverrideFunc}setErrorBoundary(A){this._errorBoundary=A}tryBumpExpiryTime(A,B){var Q;let D=(Q=this._fallbackInfo)===null||Q===void 0?void 0:Q[B.endpoint];if(!D)return;D.expiryTime=Date.now()+Dr0,Ji1(A,Object.assign(Object.assign({},this._fallbackInfo),{[B.endpoint]:D}))}getActiveFallbackUrl(A,B){var Q,D;let Z=this._fallbackInfo;if(Z==null)Z=(Q=Wk9(A))!==null&&Q!==void 0?Q:{},this._fallbackInfo=Z;let G=Z[B.endpoint];if(!G||Date.now()>((D=G.expiryTime)!==null&&D!==void 0?D:0))return delete Z[B.endpoint],this._fallbackInfo=Z,Ji1(A,this._fallbackInfo),null;if(G.url)return G.url;return null}getFallbackFromProvided(A){let B=Zr0(A);if(B)return A.replace(B,"");return null}tryFetchUpdatedFallbackInfo(A,B,Q,D){var Z,G;return Qr0(this,void 0,void 0,function*(){try{if(!Fr0(Q,D))return!1;let I=B.customUrl==null&&B.fallbackUrls==null?yield this._tryFetchFallbackUrlsFromNetwork(B):B.fallbackUrls,Y=this._pickNewFallbackUrl((Z=this._fallbackInfo)===null||Z===void 0?void 0:Z[B.endpoint],I);if(!Y)return!1;return this._updateFallbackInfoWithNewUrl(A,B.endpoint,Y),!0}catch(F){return(G=this._errorBoundary)===null||G===void 0||G.logError("tryFetchUpdatedFallbackInfo",F),!1}})}_updateFallbackInfoWithNewUrl(A,B,Q){var D,Z,G;let F={url:Q,expiryTime:Date.now()+Dr0,previous:[]},I=(D=this._fallbackInfo)===null||D===void 0?void 0:D[B];if(I)F.previous.push(...I.previous);if(F.previous.length>10)F.previous=[];let Y=(G=(Z=this._fallbackInfo)===null||Z===void 0?void 0:Z[B])===null||G===void 0?void 0:G.url;if(Y!=null)F.previous.push(Y);this._fallbackInfo=Object.assign(Object.assign({},this._fallbackInfo),{[B]:F}),Ji1(A,this._fallbackInfo)}_tryFetchFallbackUrlsFromNetwork(A){var B;return Qr0(this,void 0,void 0,function*(){let Q=this._dnsQueryCooldowns[A.endpoint];if(Q&&Date.now()<Q)return null;this._dnsQueryCooldowns[A.endpoint]=Date.now()+Yk9;let D=[],Z=yield Gk9._fetchTxtRecords((B=this._networkOverrideFunc)!==null&&B!==void 0?B:fetch),G=Zr0(A.defaultUrl);for(let F of Z){if(!F.startsWith(A.endpointDnsKey+"="))continue;let I=F.split("=");if(I.length>1){let Y=I[1];if(Y.endsWith("/"))Y=Y.slice(0,-1);D.push(`https://${Y}${G}`)}}return D})}_pickNewFallbackUrl(A,B){var Q;if(B==null)return null;let D=new Set((Q=A===null||A===void 0?void 0:A.previous)!==null&&Q!==void 0?Q:[]),Z=A===null||A===void 0?void 0:A.url,G=null;for(let F of B){let I=F.endsWith("/")?F.slice(0,-1):F;if(!D.has(F)&&I!==Z){G=I;break}}return G}}Ay.NetworkFallbackResolver=Gr0;function Fr0(A,B){var Q;let D=(Q=A===null||A===void 0?void 0:A.toLowerCase())!==null&&Q!==void 0?Q:"";return B||D.includes("uncaught exception")||D.includes("failed to fetch")||D.includes("networkerror when attempting to fetch resource")}Ay._isDomainFailure=Fr0;function Ir0(A){return`statsig.network_fallback.${Fk9._DJB2(A)}`}function Ji1(A,B){let Q=Ir0(A);if(!B||Object.keys(B).length===0){Xi1.Storage.removeItem(Q);return}Xi1.Storage.setItem(Q,JSON.stringify(B))}function Wk9(A){let B=Ir0(A),Q=Xi1.Storage.getItem(B);if(!Q)return null;try{return JSON.parse(Q)}catch(D){return Ik9.Log.error("Failed to parse FallbackInfo"),null}}function Zr0(A){try{return new URL(A).pathname}catch(B){return null}}});
var _r0=E((kr0)=>{Object.defineProperty(kr0,"__esModule",{value:!0})});
var ar0=E((nr0)=>{Object.defineProperty(nr0,"__esModule",{value:!0})});
var as0=E((ns0)=>{Object.defineProperty(ns0,"__esModule",{value:!0})});
var dr0=E((mr0)=>{Object.defineProperty(mr0,"__esModule",{value:!0})});
var el=E((ia0)=>{Object.defineProperty(ia0,"__esModule",{value:!0});ia0._getSortedObject=ia0._DJB2Object=ia0._DJB2=void 0;var xj9=JV1(),vj9=(A)=>{let B=0;for(let Q=0;Q<A.length;Q++){let D=A.charCodeAt(Q);B=(B<<5)-B+D,B=B&B}return String(B>>>0)};ia0._DJB2=vj9;var bj9=(A,B)=>{return ia0._DJB2(JSON.stringify(ia0._getSortedObject(A,B)))};ia0._DJB2Object=bj9;var fj9=(A,B)=>{if(A==null)return null;let Q=Object.keys(A).sort(),D={};return Q.forEach((Z)=>{let G=A[Z];if(B===0||xj9._typeOf(G)!=="object"){D[Z]=G;return}D[Z]=ia0._getSortedObject(G,B!=null?B-1:B)}),D};ia0._getSortedObject=fj9});
var fW=E((fa0)=>{Object.defineProperty(fa0,"__esModule",{value:!0});fa0.Log=fa0.LogLevel=void 0;var $j9=" DEBUG ",qj9="  INFO ",Nj9="  WARN ",Lj9=" ERROR ";function FV1(A){return A.unshift("[Statsig]"),A}fa0.LogLevel={None:0,Error:1,Warn:2,Info:3,Debug:4};class hf{static info(...A){if(hf.level>=fa0.LogLevel.Info)console.info(qj9,...FV1(A))}static debug(...A){if(hf.level>=fa0.LogLevel.Debug)console.debug($j9,...FV1(A))}static warn(...A){if(hf.level>=fa0.LogLevel.Warn)console.warn(Nj9,...FV1(A))}static error(...A){if(hf.level>=fa0.LogLevel.Error)console.error(Lj9,...FV1(A))}}fa0.Log=hf;hf.level=fa0.LogLevel.Warn});
var gf=E((da0)=>{var gp1,up1,mp1;Object.defineProperty(da0,"__esModule",{value:!0});da0._getInstance=da0._getStatsigGlobalFlag=da0._getStatsigGlobal=void 0;var Mj9=fW(),Rj9=()=>{return __STATSIG__?__STATSIG__:IV1};da0._getStatsigGlobal=Rj9;var Oj9=(A)=>{return da0._getStatsigGlobal()[A]};da0._getStatsigGlobalFlag=Oj9;var Tj9=(A)=>{let B=da0._getStatsigGlobal();if(!A){if(B.instances&&Object.keys(B.instances).length>1)Mj9.Log.warn("Call made to Statsig global instance without an SDK key but there is more than one client instance. If you are using mulitple clients, please specify the SDK key.");return B.firstInstance}return B.instances&&B.instances[A]};da0._getInstance=Tj9;var rl="__STATSIG__",ga0=typeof window!=="undefined"?window:{},ua0=typeof global!=="undefined"?global:{},ma0=typeof globalThis!=="undefined"?globalThis:{},IV1=(mp1=(up1=(gp1=ga0[rl])!==null&&gp1!==void 0?gp1:ua0[rl])!==null&&up1!==void 0?up1:ma0[rl])!==null&&mp1!==void 0?mp1:{instance:da0._getInstance};ga0[rl]=IV1;ua0[rl]=IV1;ma0[rl]=IV1});
var hr0=E((br0)=>{Object.defineProperty(br0,"__esModule",{value:!0});br0.DataAdapterCachePrefix=void 0;br0.DataAdapterCachePrefix="statsig.cached"});
var hs0=E((ej)=>{var Ii1=ej&&ej.__awaiter||function(A,B,Q,D){function Z(G){return G instanceof Q?G:new Q(function(F){F(G)})}return new(Q||(Q=Promise))(function(G,F){function I(J){try{W(D.next(J))}catch(X){F(X)}}function Y(J){try{W(D.throw(J))}catch(X){F(X)}}function W(J){J.done?G(J.value):Z(J.value).then(I,Y)}W((D=D.apply(A,B||[])).next())})};Object.defineProperty(ej,"__esModule",{value:!0});ej._makeDataAdapterResult=ej.DataAdapterCore=void 0;var RV1=fW(),my9=MV1(),OV1=Gi1(),tj=UO(),_s0=Fi1(),xs0=10;class vs0{constructor(A,B){this._adapterName=A,this._cacheSuffix=B,this._options=null,this._sdkKey=null,this._lastModifiedStoreKey=`statsig.last_modified_time.${B}`,this._inMemoryCache=new bs0}attach(A,B){this._sdkKey=A,this._options=B}getDataSync(A){let B=A&&OV1._normalizeUser(A,this._options),Q=this._getCacheKey(B),D=this._inMemoryCache.get(Q,B);if(D)return D;let Z=this._loadFromCache(Q);if(Z)return this._inMemoryCache.add(Q,Z),this._inMemoryCache.get(Q,B);return null}setData(A,B){let Q=B&&OV1._normalizeUser(B,this._options),D=this._getCacheKey(Q);this._inMemoryCache.add(D,TV1("Bootstrap",A,null,Q))}_getDataAsyncImpl(A,B,Q){return Ii1(this,void 0,void 0,function*(){if(!tj.Storage.isReady())yield tj.Storage.isReadyResolver();let D=A!==null&&A!==void 0?A:this.getDataSync(B),Z=[this._fetchAndPrepFromNetwork(D,B,Q)];if(Q===null||Q===void 0?void 0:Q.timeoutMs)Z.push(new Promise((G)=>setTimeout(G,Q.timeoutMs)).then(()=>{return RV1.Log.debug("Fetching latest value timed out"),null}));return yield Promise.race(Z)})}_prefetchDataImpl(A,B){return Ii1(this,void 0,void 0,function*(){let Q=A&&OV1._normalizeUser(A,this._options),D=this._getCacheKey(Q),Z=yield this._getDataAsyncImpl(null,Q,B);if(Z)this._inMemoryCache.add(D,Object.assign(Object.assign({},Z),{source:"Prefetch"}))})}_fetchAndPrepFromNetwork(A,B,Q){var D;return Ii1(this,void 0,void 0,function*(){let Z=(D=A===null||A===void 0?void 0:A.data)!==null&&D!==void 0?D:null,G=A!=null&&this._isCachedResultValidFor204(A,B),F=yield this._fetchFromNetwork(Z,B,Q,G);if(!F)return RV1.Log.debug("No response returned for latest value"),null;let I=_s0._typedJsonParse(F,"has_updates","Response"),Y=this._getSdkKey(),W=my9.StableID.get(Y),J=null;if((I===null||I===void 0?void 0:I.has_updates)===!0)J=TV1("Network",F,W,B);else if(Z&&(I===null||I===void 0?void 0:I.has_updates)===!1)J=TV1("NetworkNotModified",Z,W,B);else return null;let X=this._getCacheKey(B);return this._inMemoryCache.add(X,J),this._writeToCache(X,J),J})}_getSdkKey(){if(this._sdkKey!=null)return this._sdkKey;return RV1.Log.error(`${this._adapterName} is not attached to a Client`),""}_loadFromCache(A){var B;let Q=(B=tj.Storage.getItem)===null||B===void 0?void 0:B.call(tj.Storage,A);if(Q==null)return null;let D=_s0._typedJsonParse(Q,"source","Cached Result");return D?Object.assign(Object.assign({},D),{source:"Cache"}):null}_writeToCache(A,B){tj.Storage.setItem(A,JSON.stringify(B)),this._runLocalStorageCacheEviction(A)}_runLocalStorageCacheEviction(A){var B;let Q=(B=tj._getObjectFromStorage(this._lastModifiedStoreKey))!==null&&B!==void 0?B:{};Q[A]=Date.now();let D=fs0(Q,xs0);if(D)delete Q[D],tj.Storage.removeItem(D);tj._setObjectInStorage(this._lastModifiedStoreKey,Q)}}ej.DataAdapterCore=vs0;function TV1(A,B,Q,D){return{source:A,data:B,receivedAt:Date.now(),stableID:Q,fullUserHash:OV1._getFullUserHash(D)}}ej._makeDataAdapterResult=TV1;class bs0{constructor(){this._data={}}get(A,B){var Q;let D=this._data[A],Z=D===null||D===void 0?void 0:D.stableID,G=(Q=B===null||B===void 0?void 0:B.customIDs)===null||Q===void 0?void 0:Q.stableID;if(G&&Z&&G!==Z)return RV1.Log.warn("'StatsigUser.customIDs.stableID' mismatch"),null;return D}add(A,B){let Q=fs0(this._data,xs0-1);if(Q)delete this._data[Q];this._data[A]=B}merge(A){this._data=Object.assign(Object.assign({},this._data),A)}}function fs0(A,B){let Q=Object.keys(A);if(Q.length<=B)return null;return Q.reduce((D,Z)=>{let G=A[D],F=A[Z];if(typeof G==="object"&&typeof F==="object")return F.receivedAt<G.receivedAt?Z:D;return F<G?Z:D})}});
var ir0=E((lr0)=>{Object.defineProperty(lr0,"__esModule",{value:!0});lr0._makeTypedGet=lr0._mergeOverride=lr0._makeLayer=lr0._makeExperiment=lr0._makeDynamicConfig=lr0._makeFeatureGate=void 0;var ik9=fW(),nk9=JV1(),ak9="default";function Ei1(A,B,Q,D){var Z;return{name:A,details:B,ruleID:(Z=Q===null||Q===void 0?void 0:Q.rule_id)!==null&&Z!==void 0?Z:ak9,__evaluation:Q,value:D}}function sk9(A,B,Q){return Ei1(A,B,Q,(Q===null||Q===void 0?void 0:Q.value)===!0)}lr0._makeFeatureGate=sk9;function cr0(A,B,Q){var D;let Z=(D=Q===null||Q===void 0?void 0:Q.value)!==null&&D!==void 0?D:{};return Object.assign(Object.assign({},Ei1(A,B,Q,Z)),{get:kV1(A,Q===null||Q===void 0?void 0:Q.value)})}lr0._makeDynamicConfig=cr0;function rk9(A,B,Q){var D;let Z=cr0(A,B,Q);return Object.assign(Object.assign({},Z),{groupName:(D=Q===null||Q===void 0?void 0:Q.group_name)!==null&&D!==void 0?D:null})}lr0._makeExperiment=rk9;function ok9(A,B,Q,D){var Z,G;return Object.assign(Object.assign({},Ei1(A,B,Q,void 0)),{get:kV1(A,Q===null||Q===void 0?void 0:Q.value,D),groupName:(Z=Q===null||Q===void 0?void 0:Q.group_name)!==null&&Z!==void 0?Z:null,__value:(G=Q===null||Q===void 0?void 0:Q.value)!==null&&G!==void 0?G:{}})}lr0._makeLayer=ok9;function tk9(A,B,Q,D){return Object.assign(Object.assign(Object.assign({},A),B),{get:kV1(A.name,Q,D)})}lr0._mergeOverride=tk9;function kV1(A,B,Q){return(D,Z)=>{var G;let F=(G=B===null||B===void 0?void 0:B[D])!==null&&G!==void 0?G:null;if(F==null)return Z!==null&&Z!==void 0?Z:null;if(Z!=null&&!nk9._isTypeMatch(F,Z))return ik9.Log.warn(`Parameter type mismatch. '${A}.${D}' was found to be type '${typeof F}' but fallback/return type is '${typeof Z}'. See https://docs.statsig.com/client/javascript-sdk/#typed-getters`),Z!==null&&Z!==void 0?Z:null;return Q===null||Q===void 0||Q(D),F}}lr0._makeTypedGet=kV1});
var jV1=E((Ur0)=>{Object.defineProperty(Ur0,"__esModule",{value:!0});Ur0.StatsigSession=Ur0.SessionID=void 0;var Jk9=$B1(),Xk9=fW(),Cr0=UO(),Kr0=NV1(),Hr0=1800000,zr0=14400000,SV1={};Ur0.SessionID={get:(A)=>{return Ur0.StatsigSession.get(A).data.sessionID}};Ur0.StatsigSession={get:(A)=>{if(SV1[A]==null)SV1[A]=Vk9(A);let B=SV1[A];return Kk9(B)},overrideInitialSessionID:(A,B)=>{SV1[B]=Ck9(A,B)}};function Vk9(A){let B=Uk9(A),Q=Date.now();if(!B)B={sessionID:Kr0.getUUID(),startTime:Q,lastUpdate:Q};return{data:B,sdkKey:A}}function Ck9(A,B){let Q=Date.now();return{data:{sessionID:A,startTime:Q,lastUpdate:Q},sdkKey:B}}function Kk9(A){let B=Date.now(),Q=A.data;if(Hk9(Q)||zk9(Q))Q.sessionID=Kr0.getUUID(),Q.startTime=B;Q.lastUpdate=B,Ek9(Q,A.sdkKey),clearTimeout(A.idleTimeoutID),clearTimeout(A.ageTimeoutID);let D=B-Q.startTime,Z=A.sdkKey;return A.idleTimeoutID=Vr0(Z,Hr0),A.ageTimeoutID=Vr0(Z,zr0-D),A}function Vr0(A,B){return setTimeout(()=>{let Q=__STATSIG__===null||__STATSIG__===void 0?void 0:__STATSIG__.instance(A);if(Q)Q.$emt({name:"session_expired"})},B)}function Hk9({lastUpdate:A}){return Date.now()-A>Hr0}function zk9({startTime:A}){return Date.now()-A>zr0}function Er0(A){return`statsig.session_id.${Jk9._getStorageKey(A)}`}function Ek9(A,B){let Q=Er0(B);try{Cr0._setObjectInStorage(Q,A)}catch(D){Xk9.Log.warn("Failed to save SessionID")}}function Uk9(A){let B=Er0(A);return Cr0._getObjectFromStorage(B)}});
var or0=E((sr0)=>{Object.defineProperty(sr0,"__esModule",{value:!0});sr0.UPDATE_DETAIL_ERROR_MESSAGES=sr0.createUpdateDetails=void 0;var Z_9=(A,B,Q,D,Z,G)=>{return{duration:Q,source:B,success:A,error:D,sourceUrl:Z,warnings:G}};sr0.createUpdateDetails=Z_9;sr0.UPDATE_DETAIL_ERROR_MESSAGES={NO_NETWORK_DATA:"No data was returned from the network. This may be due to a network timeout if a timeout value was specified in the options or ad blocker error."}});
var qB1=E((ta0)=>{Object.defineProperty(ta0,"__esModule",{value:!0});ta0.NetworkParam=ta0.NetworkDefault=ta0.Endpoint=void 0;ta0.Endpoint={_initialize:"initialize",_rgstr:"rgstr",_download_config_specs:"download_config_specs"};ta0.NetworkDefault={[ta0.Endpoint._rgstr]:"https://prodregistryv2.org/v1",[ta0.Endpoint._initialize]:"https://featureassets.org/v1",[ta0.Endpoint._download_config_specs]:"https://api.statsigcdn.com/v1"};ta0.NetworkParam={EventCount:"ec",SdkKey:"k",SdkType:"st",SdkVersion:"sv",Time:"t",SessionID:"sid",StatsigEncoded:"se",IsGzipped:"gz"}});
var qs0=E(($s0)=>{Object.defineProperty($s0,"__esModule",{value:!0})});
var rs0=E((ss0)=>{Object.defineProperty(ss0,"__esModule",{value:!0})});
var sp1=E((Gs0)=>{Object.defineProperty(Gs0,"__esModule",{value:!0});Gs0._createLayerParameterExposure=Gs0._createConfigExposure=Gs0._mapExposures=Gs0._createGateExposure=Gs0._isExposureEvent=void 0;var Qs0="statsig::config_exposure",Ds0="statsig::gate_exposure",Zs0="statsig::layer_exposure",ap1=(A,B,Q,D,Z)=>{if(Q.bootstrapMetadata)D.bootstrapMetadata=Q.bootstrapMetadata;return{eventName:A,user:B,value:null,metadata:By9(Q,D),secondaryExposures:Z,time:Date.now()}},oj9=({eventName:A})=>{return A===Ds0||A===Qs0||A===Zs0};Gs0._isExposureEvent=oj9;var tj9=(A,B,Q)=>{var D,Z,G;let F={gate:B.name,gateValue:String(B.value),ruleID:B.ruleID};if(((D=B.__evaluation)===null||D===void 0?void 0:D.version)!=null)F.configVersion=B.__evaluation.version;return ap1(Ds0,A,B.details,F,KV1((G=(Z=B.__evaluation)===null||Z===void 0?void 0:Z.secondary_exposures)!==null&&G!==void 0?G:[],Q))};Gs0._createGateExposure=tj9;function KV1(A,B){return A.map((Q)=>{if(typeof Q==="string")return(B!==null&&B!==void 0?B:{})[Q];return Q}).filter((Q)=>Q!=null)}Gs0._mapExposures=KV1;var ej9=(A,B,Q)=>{var D,Z,G,F;let I={config:B.name,ruleID:B.ruleID};if(((D=B.__evaluation)===null||D===void 0?void 0:D.version)!=null)I.configVersion=B.__evaluation.version;if(((Z=B.__evaluation)===null||Z===void 0?void 0:Z.passed)!=null)I.rulePassed=String(B.__evaluation.passed);return ap1(Qs0,A,B.details,I,KV1((F=(G=B.__evaluation)===null||G===void 0?void 0:G.secondary_exposures)!==null&&F!==void 0?F:[],Q))};Gs0._createConfigExposure=ej9;var Ay9=(A,B,Q,D)=>{var Z,G,F,I;let Y=B.__evaluation,W=((Z=Y===null||Y===void 0?void 0:Y.explicit_parameters)===null||Z===void 0?void 0:Z.includes(Q))===!0,J="",X=(G=Y===null||Y===void 0?void 0:Y.undelegated_secondary_exposures)!==null&&G!==void 0?G:[];if(W)J=(F=Y.allocated_experiment_name)!==null&&F!==void 0?F:"",X=Y.secondary_exposures;let V={config:B.name,parameterName:Q,ruleID:B.ruleID,allocatedExperiment:J,isExplicitParameter:String(W)};if(((I=B.__evaluation)===null||I===void 0?void 0:I.version)!=null)V.configVersion=B.__evaluation.version;return ap1(Zs0,A,B.details,V,KV1(X,D))};Gs0._createLayerParameterExposure=Ay9;var By9=(A,B)=>{if(B.reason=A.reason,A.lcut)B.lcut=String(A.lcut);if(A.receivedAt)B.receivedAt=String(A.receivedAt);return B}});
var tp1=E((Js0)=>{Object.defineProperty(Js0,"__esModule",{value:!0});Js0.UrlConfiguration=void 0;var EV1=qB1(),Vy9={[EV1.Endpoint._initialize]:"i",[EV1.Endpoint._rgstr]:"e",[EV1.Endpoint._download_config_specs]:"d"};class Ws0{constructor(A,B,Q,D){if(this.customUrl=null,this.fallbackUrls=null,this.endpoint=A,this.endpointDnsKey=Vy9[A],B)this.customUrl=B;if(!B&&Q)this.customUrl=Q.endsWith("/")?`${Q}${A}`:`${Q}/${A}`;if(D)this.fallbackUrls=D;let Z=EV1.NetworkDefault[A];this.defaultUrl=`${Z}/${A}`}getUrl(){var A;return(A=this.customUrl)!==null&&A!==void 0?A:this.defaultUrl}}Js0.UrlConfiguration=Ws0});
var ts0=E((os0)=>{Object.defineProperty(os0,"__esModule",{value:!0})});
var uf=E((As0)=>{Object.defineProperty(As0,"__esModule",{value:!0});As0._getCurrentPageUrlSafe=As0._addDocumentEventListenerSafe=As0._addWindowEventListenerSafe=As0._isServerEnv=As0._getDocumentSafe=As0._getWindowSafe=void 0;var dj9=()=>{return typeof window!=="undefined"?window:null};As0._getWindowSafe=dj9;var cj9=()=>{var A;let B=As0._getWindowSafe();return(A=B===null||B===void 0?void 0:B.document)!==null&&A!==void 0?A:null};As0._getDocumentSafe=cj9;var lj9=()=>{if(As0._getDocumentSafe()!==null)return!1;let A=typeof process!=="undefined"&&process.versions!=null&&process.versions.node!=null;return typeof EdgeRuntime==="string"||A};As0._isServerEnv=lj9;var pj9=(A,B)=>{let Q=As0._getWindowSafe();if(typeof(Q===null||Q===void 0?void 0:Q.addEventListener)==="function")Q.addEventListener(A,B)};As0._addWindowEventListenerSafe=pj9;var ij9=(A,B)=>{let Q=As0._getDocumentSafe();if(typeof(Q===null||Q===void 0?void 0:Q.addEventListener)==="function")Q.addEventListener(A,B)};As0._addDocumentEventListenerSafe=ij9;var nj9=()=>{var A;try{return(A=As0._getWindowSafe())===null||A===void 0?void 0:A.location.href.split(/[?#]/)[0]}catch(B){return}};As0._getCurrentPageUrlSafe=nj9});
var ur0=E((gr0)=>{Object.defineProperty(gr0,"__esModule",{value:!0})});
var us0=E((gs0)=>{Object.defineProperty(gs0,"__esModule",{value:!0})});
var vr0=E((Yp)=>{var fk9=Yp&&Yp.__awaiter||function(A,B,Q,D){function Z(G){return G instanceof Q?G:new Q(function(F){F(G)})}return new(Q||(Q=Promise))(function(G,F){function I(J){try{W(D.next(J))}catch(X){F(X)}}function Y(J){try{W(D.throw(J))}catch(X){F(X)}}function W(J){J.done?G(J.value):Z(J.value).then(I,Y)}W((D=D.apply(A,B||[])).next())})};Object.defineProperty(Yp,"__esModule",{value:!0});Yp.StatsigClientBase=void 0;gf();var hk9=gf(),gk9=Yi1(),uk9=Di1(),zi1=fW(),mk9=Wi1(),dk9=uf(),ck9=jV1(),yV1=UO(),lk9=3000;class xr0{constructor(A,B,Q,D){var Z;this.loadingStatus="Uninitialized",this._initializePromise=null,this._listeners={};let G=this.$emt.bind(this);(D===null||D===void 0?void 0:D.logLevel)!=null&&(zi1.Log.level=D.logLevel),(D===null||D===void 0?void 0:D.disableStorage)&&yV1.Storage._setDisabled(!0),(D===null||D===void 0?void 0:D.initialSessionID)&&ck9.StatsigSession.overrideInitialSessionID(D.initialSessionID,A),(D===null||D===void 0?void 0:D.storageProvider)&&yV1.Storage._setProvider(D.storageProvider),this._sdkKey=A,this._options=D!==null&&D!==void 0?D:{},this._memoCache={},this.overrideAdapter=(Z=D===null||D===void 0?void 0:D.overrideAdapter)!==null&&Z!==void 0?Z:null,this._logger=new uk9.EventLogger(A,G,Q,D),this._errorBoundary=new gk9.ErrorBoundary(A,D,G),this._errorBoundary.wrap(this),this._errorBoundary.wrap(B),this._errorBoundary.wrap(this._logger),Q.setErrorBoundary(this._errorBoundary),this.dataAdapter=B,this.dataAdapter.attach(A,D),this.storageProvider=yV1.Storage,this._primeReadyRipcord(),pk9(A,this)}updateRuntimeOptions(A){if(A.disableLogging!=null)this._options.disableLogging=A.disableLogging,this._logger.setLoggingDisabled(A.disableLogging);if(A.disableStorage!=null)this._options.disableStorage=A.disableStorage,yV1.Storage._setDisabled(A.disableStorage)}flush(){return this._logger.flush()}shutdown(){return fk9(this,void 0,void 0,function*(){this.$emt({name:"pre_shutdown"}),this._setStatus("Uninitialized",null),this._initializePromise=null,yield this._logger.stop()})}on(A,B){if(!this._listeners[A])this._listeners[A]=[];this._listeners[A].push(B)}off(A,B){if(this._listeners[A]){let Q=this._listeners[A].indexOf(B);if(Q!==-1)this._listeners[A].splice(Q,1)}}$on(A,B){B.__isInternal=!0,this.on(A,B)}$emt(A){var B;let Q=(D)=>{try{D(A)}catch(Z){if(D.__isInternal===!0){this._errorBoundary.logError(`__emit:${A.name}`,Z);return}zi1.Log.error("An error occurred in a StatsigClientEvent listener. This is not an issue with Statsig.",A)}};if(this._listeners[A.name])this._listeners[A.name].forEach((D)=>Q(D));(B=this._listeners["*"])===null||B===void 0||B.forEach(Q)}_setStatus(A,B){this.loadingStatus=A,this._memoCache={},this.$emt({name:"values_updated",status:A,values:B})}_enqueueExposure(A,B,Q){if((Q===null||Q===void 0?void 0:Q.disableExposureLog)===!0){this._logger.incrementNonExposureCount(A);return}this._logger.enqueue(B)}_memoize(A,B){return(Q,D)=>{if(this._options.disableEvaluationMemoization)return B(Q,D);let Z=mk9.createMemoKey(A,Q,D);if(!Z)return B(Q,D);if(!(Z in this._memoCache)){if(Object.keys(this._memoCache).length>=lk9)this._memoCache={};this._memoCache[Z]=B(Q,D)}return this._memoCache[Z]}}}Yp.StatsigClientBase=xr0;function pk9(A,B){var Q;if(dk9._isServerEnv())return;let D=hk9._getStatsigGlobal(),Z=(Q=D.instances)!==null&&Q!==void 0?Q:{},G=B;if(Z[A]!=null)zi1.Log.warn("Creating multiple Statsig clients with the same SDK key can lead to unexpected behavior. Multi-instance support requires different SDK keys.");if(Z[A]=G,!D.firstInstance)D.firstInstance=G;D.instances=Z,__STATSIG__=D}});
var wi1=E((OB1)=>{var Fo0=OB1&&OB1.__awaiter||function(A,B,Q,D){function Z(G){return G instanceof Q?G:new Q(function(F){F(G)})}return new(Q||(Q=Promise))(function(G,F){function I(J){try{W(D.next(J))}catch(X){F(X)}}function Y(J){try{W(D.throw(J))}catch(X){F(X)}}function W(J){J.done?G(J.value):Z(J.value).then(I,Y)}W((D=D.apply(A,B||[])).next())})};Object.defineProperty(OB1,"__esModule",{value:!0});var _V1=By(),z_9=Go0();class Io0 extends _V1.NetworkCore{constructor(A,B){super(A,B);let Q=A===null||A===void 0?void 0:A.networkConfig;this._initializeUrlConfig=new _V1.UrlConfiguration(_V1.Endpoint._initialize,Q===null||Q===void 0?void 0:Q.initializeUrl,Q===null||Q===void 0?void 0:Q.api,Q===null||Q===void 0?void 0:Q.initializeFallbackUrls)}fetchEvaluations(A,B,Q,D,Z){return Fo0(this,void 0,void 0,function*(){let G=B?_V1._typedJsonParse(B,"has_updates","InitializeResponse"):null,F={user:D,hash:"djb2",deltasResponseRequested:!1,full_checksum:null};if(G===null||G===void 0?void 0:G.has_updates)F=Object.assign(Object.assign({},F),{sinceTime:Z?G.time:0,previousDerivedFields:"derived_fields"in G&&Z?G.derived_fields:{},deltasResponseRequested:!0,full_checksum:G.full_checksum});return this._fetchEvaluations(A,G,F,Q)})}_fetchEvaluations(A,B,Q,D){var Z,G;return Fo0(this,void 0,void 0,function*(){let F=yield this.post({sdkKey:A,urlConfig:this._initializeUrlConfig,data:Q,retries:2,isStatsigEncodable:!0,priority:D});if((F===null||F===void 0?void 0:F.code)===204)return'{"has_updates": false}';if((F===null||F===void 0?void 0:F.code)!==200)return(Z=F===null||F===void 0?void 0:F.body)!==null&&Z!==void 0?Z:null;if((B===null||B===void 0?void 0:B.has_updates)!==!0||((G=F.body)===null||G===void 0?void 0:G.includes('"is_delta":true'))!==!0||Q.deltasResponseRequested!==!0)return F.body;let I=z_9._resolveDeltasResponse(B,F.body);if(typeof I==="string")return I;return this._fetchEvaluations(A,B,Object.assign(Object.assign(Object.assign({},Q),I),{deltasResponseRequested:!1}),D)})}}OB1.default=Io0});
var yr0=E((jr0)=>{Object.defineProperty(jr0,"__esModule",{value:!0})});

// Export all variables
module.exports = {
  $B1,
  $V1,
  Bo0,
  Br0,
  By,
  Co0,
  Di1,
  Eo0,
  Fi1,
  Gi1,
  Go0,
  Ho0,
  JV1,
  Ki1,
  MV1,
  NV1,
  PV1,
  RB1,
  Sr0,
  UO,
  Vi1,
  WV1,
  Wi1,
  Xo0,
  Yi1,
  Yr0,
  _r0,
  ar0,
  as0,
  dr0,
  el,
  fW,
  gf,
  hr0,
  hs0,
  ir0,
  jV1,
  or0,
  qB1,
  qs0,
  rs0,
  sp1,
  tp1,
  ts0,
  uf,
  ur0,
  us0,
  vr0,
  wi1,
  yr0
};
