// Package extracted with entry point: Jn2
// Contains 227 variables: $h2, $v2, AS2, Ai2, Aj2, Am2, An2, Bc2, Bn2, CP... (and 217 more)

var $h2=E((wh2)=>{Object.defineProperty(wh2,"__esModule",{value:!0});wh2.ExportResultCode=void 0;var G76;(function(A){A[A.SUCCESS=0]="SUCCESS",A[A.FAILED=1]="FAILED"})(G76=wh2.ExportResultCode||(wh2.ExportResultCode={}))});
var $v2=E((Cv2)=>{Object.defineProperty(Cv2,"__esModule",{value:!0});Cv2.SEMATTRS_NET_HOST_CARRIER_ICC=Cv2.SEMATTRS_NET_HOST_CARRIER_MNC=Cv2.SEMATTRS_NET_HOST_CARRIER_MCC=Cv2.SEMATTRS_NET_HOST_CARRIER_NAME=Cv2.SEMATTRS_NET_HOST_CONNECTION_SUBTYPE=Cv2.SEMATTRS_NET_HOST_CONNECTION_TYPE=Cv2.SEMATTRS_NET_HOST_NAME=Cv2.SEMATTRS_NET_HOST_PORT=Cv2.SEMATTRS_NET_HOST_IP=Cv2.SEMATTRS_NET_PEER_NAME=Cv2.SEMATTRS_NET_PEER_PORT=Cv2.SEMATTRS_NET_PEER_IP=Cv2.SEMATTRS_NET_TRANSPORT=Cv2.SEMATTRS_FAAS_INVOKED_REGION=Cv2.SEMATTRS_FAAS_INVOKED_PROVIDER=Cv2.SEMATTRS_FAAS_INVOKED_NAME=Cv2.SEMATTRS_FAAS_COLDSTART=Cv2.SEMATTRS_FAAS_CRON=Cv2.SEMATTRS_FAAS_TIME=Cv2.SEMATTRS_FAAS_DOCUMENT_NAME=Cv2.SEMATTRS_FAAS_DOCUMENT_TIME=Cv2.SEMATTRS_FAAS_DOCUMENT_OPERATION=Cv2.SEMATTRS_FAAS_DOCUMENT_COLLECTION=Cv2.SEMATTRS_FAAS_EXECUTION=Cv2.SEMATTRS_FAAS_TRIGGER=Cv2.SEMATTRS_EXCEPTION_ESCAPED=Cv2.SEMATTRS_EXCEPTION_STACKTRACE=Cv2.SEMATTRS_EXCEPTION_MESSAGE=Cv2.SEMATTRS_EXCEPTION_TYPE=Cv2.SEMATTRS_DB_SQL_TABLE=Cv2.SEMATTRS_DB_MONGODB_COLLECTION=Cv2.SEMATTRS_DB_REDIS_DATABASE_INDEX=Cv2.SEMATTRS_DB_HBASE_NAMESPACE=Cv2.SEMATTRS_DB_CASSANDRA_COORDINATOR_DC=Cv2.SEMATTRS_DB_CASSANDRA_COORDINATOR_ID=Cv2.SEMATTRS_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT=Cv2.SEMATTRS_DB_CASSANDRA_IDEMPOTENCE=Cv2.SEMATTRS_DB_CASSANDRA_TABLE=Cv2.SEMATTRS_DB_CASSANDRA_CONSISTENCY_LEVEL=Cv2.SEMATTRS_DB_CASSANDRA_PAGE_SIZE=Cv2.SEMATTRS_DB_CASSANDRA_KEYSPACE=Cv2.SEMATTRS_DB_MSSQL_INSTANCE_NAME=Cv2.SEMATTRS_DB_OPERATION=Cv2.SEMATTRS_DB_STATEMENT=Cv2.SEMATTRS_DB_NAME=Cv2.SEMATTRS_DB_JDBC_DRIVER_CLASSNAME=Cv2.SEMATTRS_DB_USER=Cv2.SEMATTRS_DB_CONNECTION_STRING=Cv2.SEMATTRS_DB_SYSTEM=Cv2.SEMATTRS_AWS_LAMBDA_INVOKED_ARN=void 0;Cv2.SEMATTRS_MESSAGING_DESTINATION_KIND=Cv2.SEMATTRS_MESSAGING_DESTINATION=Cv2.SEMATTRS_MESSAGING_SYSTEM=Cv2.SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES=Cv2.SEMATTRS_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS=Cv2.SEMATTRS_AWS_DYNAMODB_SCANNED_COUNT=Cv2.SEMATTRS_AWS_DYNAMODB_COUNT=Cv2.SEMATTRS_AWS_DYNAMODB_TOTAL_SEGMENTS=Cv2.SEMATTRS_AWS_DYNAMODB_SEGMENT=Cv2.SEMATTRS_AWS_DYNAMODB_SCAN_FORWARD=Cv2.SEMATTRS_AWS_DYNAMODB_TABLE_COUNT=Cv2.SEMATTRS_AWS_DYNAMODB_EXCLUSIVE_START_TABLE=Cv2.SEMATTRS_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES=Cv2.SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES=Cv2.SEMATTRS_AWS_DYNAMODB_SELECT=Cv2.SEMATTRS_AWS_DYNAMODB_INDEX_NAME=Cv2.SEMATTRS_AWS_DYNAMODB_ATTRIBUTES_TO_GET=Cv2.SEMATTRS_AWS_DYNAMODB_LIMIT=Cv2.SEMATTRS_AWS_DYNAMODB_PROJECTION=Cv2.SEMATTRS_AWS_DYNAMODB_CONSISTENT_READ=Cv2.SEMATTRS_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY=Cv2.SEMATTRS_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY=Cv2.SEMATTRS_AWS_DYNAMODB_ITEM_COLLECTION_METRICS=Cv2.SEMATTRS_AWS_DYNAMODB_CONSUMED_CAPACITY=Cv2.SEMATTRS_AWS_DYNAMODB_TABLE_NAMES=Cv2.SEMATTRS_HTTP_CLIENT_IP=Cv2.SEMATTRS_HTTP_ROUTE=Cv2.SEMATTRS_HTTP_SERVER_NAME=Cv2.SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED=Cv2.SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH=Cv2.SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED=Cv2.SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH=Cv2.SEMATTRS_HTTP_USER_AGENT=Cv2.SEMATTRS_HTTP_FLAVOR=Cv2.SEMATTRS_HTTP_STATUS_CODE=Cv2.SEMATTRS_HTTP_SCHEME=Cv2.SEMATTRS_HTTP_HOST=Cv2.SEMATTRS_HTTP_TARGET=Cv2.SEMATTRS_HTTP_URL=Cv2.SEMATTRS_HTTP_METHOD=Cv2.SEMATTRS_CODE_LINENO=Cv2.SEMATTRS_CODE_FILEPATH=Cv2.SEMATTRS_CODE_NAMESPACE=Cv2.SEMATTRS_CODE_FUNCTION=Cv2.SEMATTRS_THREAD_NAME=Cv2.SEMATTRS_THREAD_ID=Cv2.SEMATTRS_ENDUSER_SCOPE=Cv2.SEMATTRS_ENDUSER_ROLE=Cv2.SEMATTRS_ENDUSER_ID=Cv2.SEMATTRS_PEER_SERVICE=void 0;Cv2.DBSYSTEMVALUES_FILEMAKER=Cv2.DBSYSTEMVALUES_DERBY=Cv2.DBSYSTEMVALUES_FIREBIRD=Cv2.DBSYSTEMVALUES_ADABAS=Cv2.DBSYSTEMVALUES_CACHE=Cv2.DBSYSTEMVALUES_EDB=Cv2.DBSYSTEMVALUES_FIRSTSQL=Cv2.DBSYSTEMVALUES_INGRES=Cv2.DBSYSTEMVALUES_HANADB=Cv2.DBSYSTEMVALUES_MAXDB=Cv2.DBSYSTEMVALUES_PROGRESS=Cv2.DBSYSTEMVALUES_HSQLDB=Cv2.DBSYSTEMVALUES_CLOUDSCAPE=Cv2.DBSYSTEMVALUES_HIVE=Cv2.DBSYSTEMVALUES_REDSHIFT=Cv2.DBSYSTEMVALUES_POSTGRESQL=Cv2.DBSYSTEMVALUES_DB2=Cv2.DBSYSTEMVALUES_ORACLE=Cv2.DBSYSTEMVALUES_MYSQL=Cv2.DBSYSTEMVALUES_MSSQL=Cv2.DBSYSTEMVALUES_OTHER_SQL=Cv2.SemanticAttributes=Cv2.SEMATTRS_MESSAGE_UNCOMPRESSED_SIZE=Cv2.SEMATTRS_MESSAGE_COMPRESSED_SIZE=Cv2.SEMATTRS_MESSAGE_ID=Cv2.SEMATTRS_MESSAGE_TYPE=Cv2.SEMATTRS_RPC_JSONRPC_ERROR_MESSAGE=Cv2.SEMATTRS_RPC_JSONRPC_ERROR_CODE=Cv2.SEMATTRS_RPC_JSONRPC_REQUEST_ID=Cv2.SEMATTRS_RPC_JSONRPC_VERSION=Cv2.SEMATTRS_RPC_GRPC_STATUS_CODE=Cv2.SEMATTRS_RPC_METHOD=Cv2.SEMATTRS_RPC_SERVICE=Cv2.SEMATTRS_RPC_SYSTEM=Cv2.SEMATTRS_MESSAGING_KAFKA_TOMBSTONE=Cv2.SEMATTRS_MESSAGING_KAFKA_PARTITION=Cv2.SEMATTRS_MESSAGING_KAFKA_CLIENT_ID=Cv2.SEMATTRS_MESSAGING_KAFKA_CONSUMER_GROUP=Cv2.SEMATTRS_MESSAGING_KAFKA_MESSAGE_KEY=Cv2.SEMATTRS_MESSAGING_RABBITMQ_ROUTING_KEY=Cv2.SEMATTRS_MESSAGING_CONSUMER_ID=Cv2.SEMATTRS_MESSAGING_OPERATION=Cv2.SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES=Cv2.SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES=Cv2.SEMATTRS_MESSAGING_CONVERSATION_ID=Cv2.SEMATTRS_MESSAGING_MESSAGE_ID=Cv2.SEMATTRS_MESSAGING_URL=Cv2.SEMATTRS_MESSAGING_PROTOCOL_VERSION=Cv2.SEMATTRS_MESSAGING_PROTOCOL=Cv2.SEMATTRS_MESSAGING_TEMP_DESTINATION=void 0;Cv2.FAASINVOKEDPROVIDERVALUES_ALIBABA_CLOUD=Cv2.FaasDocumentOperationValues=Cv2.FAASDOCUMENTOPERATIONVALUES_DELETE=Cv2.FAASDOCUMENTOPERATIONVALUES_EDIT=Cv2.FAASDOCUMENTOPERATIONVALUES_INSERT=Cv2.FaasTriggerValues=Cv2.FAASTRIGGERVALUES_OTHER=Cv2.FAASTRIGGERVALUES_TIMER=Cv2.FAASTRIGGERVALUES_PUBSUB=Cv2.FAASTRIGGERVALUES_HTTP=Cv2.FAASTRIGGERVALUES_DATASOURCE=Cv2.DbCassandraConsistencyLevelValues=Cv2.DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_SERIAL=Cv2.DBCASSANDRACONSISTENCYLEVELVALUES_SERIAL=Cv2.DBCASSANDRACONSISTENCYLEVELVALUES_ANY=Cv2.DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_ONE=Cv2.DBCASSANDRACONSISTENCYLEVELVALUES_THREE=Cv2.DBCASSANDRACONSISTENCYLEVELVALUES_TWO=Cv2.DBCASSANDRACONSISTENCYLEVELVALUES_ONE=Cv2.DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_QUORUM=Cv2.DBCASSANDRACONSISTENCYLEVELVALUES_QUORUM=Cv2.DBCASSANDRACONSISTENCYLEVELVALUES_EACH_QUORUM=Cv2.DBCASSANDRACONSISTENCYLEVELVALUES_ALL=Cv2.DbSystemValues=Cv2.DBSYSTEMVALUES_COCKROACHDB=Cv2.DBSYSTEMVALUES_MEMCACHED=Cv2.DBSYSTEMVALUES_ELASTICSEARCH=Cv2.DBSYSTEMVALUES_GEODE=Cv2.DBSYSTEMVALUES_NEO4J=Cv2.DBSYSTEMVALUES_DYNAMODB=Cv2.DBSYSTEMVALUES_COSMOSDB=Cv2.DBSYSTEMVALUES_COUCHDB=Cv2.DBSYSTEMVALUES_COUCHBASE=Cv2.DBSYSTEMVALUES_REDIS=Cv2.DBSYSTEMVALUES_MONGODB=Cv2.DBSYSTEMVALUES_HBASE=Cv2.DBSYSTEMVALUES_CASSANDRA=Cv2.DBSYSTEMVALUES_COLDFUSION=Cv2.DBSYSTEMVALUES_H2=Cv2.DBSYSTEMVALUES_VERTICA=Cv2.DBSYSTEMVALUES_TERADATA=Cv2.DBSYSTEMVALUES_SYBASE=Cv2.DBSYSTEMVALUES_SQLITE=Cv2.DBSYSTEMVALUES_POINTBASE=Cv2.DBSYSTEMVALUES_PERVASIVE=Cv2.DBSYSTEMVALUES_NETEZZA=Cv2.DBSYSTEMVALUES_MARIADB=Cv2.DBSYSTEMVALUES_INTERBASE=Cv2.DBSYSTEMVALUES_INSTANTDB=Cv2.DBSYSTEMVALUES_INFORMIX=void 0;Cv2.MESSAGINGOPERATIONVALUES_RECEIVE=Cv2.MessagingDestinationKindValues=Cv2.MESSAGINGDESTINATIONKINDVALUES_TOPIC=Cv2.MESSAGINGDESTINATIONKINDVALUES_QUEUE=Cv2.HttpFlavorValues=Cv2.HTTPFLAVORVALUES_QUIC=Cv2.HTTPFLAVORVALUES_SPDY=Cv2.HTTPFLAVORVALUES_HTTP_2_0=Cv2.HTTPFLAVORVALUES_HTTP_1_1=Cv2.HTTPFLAVORVALUES_HTTP_1_0=Cv2.NetHostConnectionSubtypeValues=Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_LTE_CA=Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_NRNSA=Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_NR=Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_IWLAN=Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_TD_SCDMA=Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_GSM=Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_HSPAP=Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_EHRPD=Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_LTE=Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_B=Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_IDEN=Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_HSPA=Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_HSUPA=Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_HSDPA=Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_CDMA2000_1XRTT=Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_A=Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_0=Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_CDMA=Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_UMTS=Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_EDGE=Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_GPRS=Cv2.NetHostConnectionTypeValues=Cv2.NETHOSTCONNECTIONTYPEVALUES_UNKNOWN=Cv2.NETHOSTCONNECTIONTYPEVALUES_UNAVAILABLE=Cv2.NETHOSTCONNECTIONTYPEVALUES_CELL=Cv2.NETHOSTCONNECTIONTYPEVALUES_WIRED=Cv2.NETHOSTCONNECTIONTYPEVALUES_WIFI=Cv2.NetTransportValues=Cv2.NETTRANSPORTVALUES_OTHER=Cv2.NETTRANSPORTVALUES_INPROC=Cv2.NETTRANSPORTVALUES_PIPE=Cv2.NETTRANSPORTVALUES_UNIX=Cv2.NETTRANSPORTVALUES_IP=Cv2.NETTRANSPORTVALUES_IP_UDP=Cv2.NETTRANSPORTVALUES_IP_TCP=Cv2.FaasInvokedProviderValues=Cv2.FAASINVOKEDPROVIDERVALUES_GCP=Cv2.FAASINVOKEDPROVIDERVALUES_AZURE=Cv2.FAASINVOKEDPROVIDERVALUES_AWS=void 0;Cv2.MessageTypeValues=Cv2.MESSAGETYPEVALUES_RECEIVED=Cv2.MESSAGETYPEVALUES_SENT=Cv2.RpcGrpcStatusCodeValues=Cv2.RPCGRPCSTATUSCODEVALUES_UNAUTHENTICATED=Cv2.RPCGRPCSTATUSCODEVALUES_DATA_LOSS=Cv2.RPCGRPCSTATUSCODEVALUES_UNAVAILABLE=Cv2.RPCGRPCSTATUSCODEVALUES_INTERNAL=Cv2.RPCGRPCSTATUSCODEVALUES_UNIMPLEMENTED=Cv2.RPCGRPCSTATUSCODEVALUES_OUT_OF_RANGE=Cv2.RPCGRPCSTATUSCODEVALUES_ABORTED=Cv2.RPCGRPCSTATUSCODEVALUES_FAILED_PRECONDITION=Cv2.RPCGRPCSTATUSCODEVALUES_RESOURCE_EXHAUSTED=Cv2.RPCGRPCSTATUSCODEVALUES_PERMISSION_DENIED=Cv2.RPCGRPCSTATUSCODEVALUES_ALREADY_EXISTS=Cv2.RPCGRPCSTATUSCODEVALUES_NOT_FOUND=Cv2.RPCGRPCSTATUSCODEVALUES_DEADLINE_EXCEEDED=Cv2.RPCGRPCSTATUSCODEVALUES_INVALID_ARGUMENT=Cv2.RPCGRPCSTATUSCODEVALUES_UNKNOWN=Cv2.RPCGRPCSTATUSCODEVALUES_CANCELLED=Cv2.RPCGRPCSTATUSCODEVALUES_OK=Cv2.MessagingOperationValues=Cv2.MESSAGINGOPERATIONVALUES_PROCESS=void 0;var VE=mI0(),Mj2="aws.lambda.invoked_arn",Rj2="db.system",Oj2="db.connection_string",Tj2="db.user",Pj2="db.jdbc.driver_classname",Sj2="db.name",jj2="db.statement",yj2="db.operation",kj2="db.mssql.instance_name",_j2="db.cassandra.keyspace",xj2="db.cassandra.page_size",vj2="db.cassandra.consistency_level",bj2="db.cassandra.table",fj2="db.cassandra.idempotence",hj2="db.cassandra.speculative_execution_count",gj2="db.cassandra.coordinator.id",uj2="db.cassandra.coordinator.dc",mj2="db.hbase.namespace",dj2="db.redis.database_index",cj2="db.mongodb.collection",lj2="db.sql.table",pj2="exception.type",ij2="exception.message",nj2="exception.stacktrace",aj2="exception.escaped",sj2="faas.trigger",rj2="faas.execution",oj2="faas.document.collection",tj2="faas.document.operation",ej2="faas.document.time",Ay2="faas.document.name",By2="faas.time",Qy2="faas.cron",Dy2="faas.coldstart",Zy2="faas.invoked_name",Gy2="faas.invoked_provider",Fy2="faas.invoked_region",Iy2="net.transport",Yy2="net.peer.ip",Wy2="net.peer.port",Jy2="net.peer.name",Xy2="net.host.ip",Vy2="net.host.port",Cy2="net.host.name",Ky2="net.host.connection.type",Hy2="net.host.connection.subtype",zy2="net.host.carrier.name",Ey2="net.host.carrier.mcc",Uy2="net.host.carrier.mnc",wy2="net.host.carrier.icc",$y2="peer.service",qy2="enduser.id",Ny2="enduser.role",Ly2="enduser.scope",My2="thread.id",Ry2="thread.name",Oy2="code.function",Ty2="code.namespace",Py2="code.filepath",Sy2="code.lineno",jy2="http.method",yy2="http.url",ky2="http.target",_y2="http.host",xy2="http.scheme",vy2="http.status_code",by2="http.flavor",fy2="http.user_agent",hy2="http.request_content_length",gy2="http.request_content_length_uncompressed",uy2="http.response_content_length",my2="http.response_content_length_uncompressed",dy2="http.server_name",cy2="http.route",ly2="http.client_ip",py2="aws.dynamodb.table_names",iy2="aws.dynamodb.consumed_capacity",ny2="aws.dynamodb.item_collection_metrics",ay2="aws.dynamodb.provisioned_read_capacity",sy2="aws.dynamodb.provisioned_write_capacity",ry2="aws.dynamodb.consistent_read",oy2="aws.dynamodb.projection",ty2="aws.dynamodb.limit",ey2="aws.dynamodb.attributes_to_get",Ak2="aws.dynamodb.index_name",Bk2="aws.dynamodb.select",Qk2="aws.dynamodb.global_secondary_indexes",Dk2="aws.dynamodb.local_secondary_indexes",Zk2="aws.dynamodb.exclusive_start_table",Gk2="aws.dynamodb.table_count",Fk2="aws.dynamodb.scan_forward",Ik2="aws.dynamodb.segment",Yk2="aws.dynamodb.total_segments",Wk2="aws.dynamodb.count",Jk2="aws.dynamodb.scanned_count",Xk2="aws.dynamodb.attribute_definitions",Vk2="aws.dynamodb.global_secondary_index_updates",Ck2="messaging.system",Kk2="messaging.destination",Hk2="messaging.destination_kind",zk2="messaging.temp_destination",Ek2="messaging.protocol",Uk2="messaging.protocol_version",wk2="messaging.url",$k2="messaging.message_id",qk2="messaging.conversation_id",Nk2="messaging.message_payload_size_bytes",Lk2="messaging.message_payload_compressed_size_bytes",Mk2="messaging.operation",Rk2="messaging.consumer_id",Ok2="messaging.rabbitmq.routing_key",Tk2="messaging.kafka.message_key",Pk2="messaging.kafka.consumer_group",Sk2="messaging.kafka.client_id",jk2="messaging.kafka.partition",yk2="messaging.kafka.tombstone",kk2="rpc.system",_k2="rpc.service",xk2="rpc.method",vk2="rpc.grpc.status_code",bk2="rpc.jsonrpc.version",fk2="rpc.jsonrpc.request_id",hk2="rpc.jsonrpc.error_code",gk2="rpc.jsonrpc.error_message",uk2="message.type",mk2="message.id",dk2="message.compressed_size",ck2="message.uncompressed_size";Cv2.SEMATTRS_AWS_LAMBDA_INVOKED_ARN=Mj2;Cv2.SEMATTRS_DB_SYSTEM=Rj2;Cv2.SEMATTRS_DB_CONNECTION_STRING=Oj2;Cv2.SEMATTRS_DB_USER=Tj2;Cv2.SEMATTRS_DB_JDBC_DRIVER_CLASSNAME=Pj2;Cv2.SEMATTRS_DB_NAME=Sj2;Cv2.SEMATTRS_DB_STATEMENT=jj2;Cv2.SEMATTRS_DB_OPERATION=yj2;Cv2.SEMATTRS_DB_MSSQL_INSTANCE_NAME=kj2;Cv2.SEMATTRS_DB_CASSANDRA_KEYSPACE=_j2;Cv2.SEMATTRS_DB_CASSANDRA_PAGE_SIZE=xj2;Cv2.SEMATTRS_DB_CASSANDRA_CONSISTENCY_LEVEL=vj2;Cv2.SEMATTRS_DB_CASSANDRA_TABLE=bj2;Cv2.SEMATTRS_DB_CASSANDRA_IDEMPOTENCE=fj2;Cv2.SEMATTRS_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT=hj2;Cv2.SEMATTRS_DB_CASSANDRA_COORDINATOR_ID=gj2;Cv2.SEMATTRS_DB_CASSANDRA_COORDINATOR_DC=uj2;Cv2.SEMATTRS_DB_HBASE_NAMESPACE=mj2;Cv2.SEMATTRS_DB_REDIS_DATABASE_INDEX=dj2;Cv2.SEMATTRS_DB_MONGODB_COLLECTION=cj2;Cv2.SEMATTRS_DB_SQL_TABLE=lj2;Cv2.SEMATTRS_EXCEPTION_TYPE=pj2;Cv2.SEMATTRS_EXCEPTION_MESSAGE=ij2;Cv2.SEMATTRS_EXCEPTION_STACKTRACE=nj2;Cv2.SEMATTRS_EXCEPTION_ESCAPED=aj2;Cv2.SEMATTRS_FAAS_TRIGGER=sj2;Cv2.SEMATTRS_FAAS_EXECUTION=rj2;Cv2.SEMATTRS_FAAS_DOCUMENT_COLLECTION=oj2;Cv2.SEMATTRS_FAAS_DOCUMENT_OPERATION=tj2;Cv2.SEMATTRS_FAAS_DOCUMENT_TIME=ej2;Cv2.SEMATTRS_FAAS_DOCUMENT_NAME=Ay2;Cv2.SEMATTRS_FAAS_TIME=By2;Cv2.SEMATTRS_FAAS_CRON=Qy2;Cv2.SEMATTRS_FAAS_COLDSTART=Dy2;Cv2.SEMATTRS_FAAS_INVOKED_NAME=Zy2;Cv2.SEMATTRS_FAAS_INVOKED_PROVIDER=Gy2;Cv2.SEMATTRS_FAAS_INVOKED_REGION=Fy2;Cv2.SEMATTRS_NET_TRANSPORT=Iy2;Cv2.SEMATTRS_NET_PEER_IP=Yy2;Cv2.SEMATTRS_NET_PEER_PORT=Wy2;Cv2.SEMATTRS_NET_PEER_NAME=Jy2;Cv2.SEMATTRS_NET_HOST_IP=Xy2;Cv2.SEMATTRS_NET_HOST_PORT=Vy2;Cv2.SEMATTRS_NET_HOST_NAME=Cy2;Cv2.SEMATTRS_NET_HOST_CONNECTION_TYPE=Ky2;Cv2.SEMATTRS_NET_HOST_CONNECTION_SUBTYPE=Hy2;Cv2.SEMATTRS_NET_HOST_CARRIER_NAME=zy2;Cv2.SEMATTRS_NET_HOST_CARRIER_MCC=Ey2;Cv2.SEMATTRS_NET_HOST_CARRIER_MNC=Uy2;Cv2.SEMATTRS_NET_HOST_CARRIER_ICC=wy2;Cv2.SEMATTRS_PEER_SERVICE=$y2;Cv2.SEMATTRS_ENDUSER_ID=qy2;Cv2.SEMATTRS_ENDUSER_ROLE=Ny2;Cv2.SEMATTRS_ENDUSER_SCOPE=Ly2;Cv2.SEMATTRS_THREAD_ID=My2;Cv2.SEMATTRS_THREAD_NAME=Ry2;Cv2.SEMATTRS_CODE_FUNCTION=Oy2;Cv2.SEMATTRS_CODE_NAMESPACE=Ty2;Cv2.SEMATTRS_CODE_FILEPATH=Py2;Cv2.SEMATTRS_CODE_LINENO=Sy2;Cv2.SEMATTRS_HTTP_METHOD=jy2;Cv2.SEMATTRS_HTTP_URL=yy2;Cv2.SEMATTRS_HTTP_TARGET=ky2;Cv2.SEMATTRS_HTTP_HOST=_y2;Cv2.SEMATTRS_HTTP_SCHEME=xy2;Cv2.SEMATTRS_HTTP_STATUS_CODE=vy2;Cv2.SEMATTRS_HTTP_FLAVOR=by2;Cv2.SEMATTRS_HTTP_USER_AGENT=fy2;Cv2.SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH=hy2;Cv2.SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED=gy2;Cv2.SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH=uy2;Cv2.SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED=my2;Cv2.SEMATTRS_HTTP_SERVER_NAME=dy2;Cv2.SEMATTRS_HTTP_ROUTE=cy2;Cv2.SEMATTRS_HTTP_CLIENT_IP=ly2;Cv2.SEMATTRS_AWS_DYNAMODB_TABLE_NAMES=py2;Cv2.SEMATTRS_AWS_DYNAMODB_CONSUMED_CAPACITY=iy2;Cv2.SEMATTRS_AWS_DYNAMODB_ITEM_COLLECTION_METRICS=ny2;Cv2.SEMATTRS_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY=ay2;Cv2.SEMATTRS_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY=sy2;Cv2.SEMATTRS_AWS_DYNAMODB_CONSISTENT_READ=ry2;Cv2.SEMATTRS_AWS_DYNAMODB_PROJECTION=oy2;Cv2.SEMATTRS_AWS_DYNAMODB_LIMIT=ty2;Cv2.SEMATTRS_AWS_DYNAMODB_ATTRIBUTES_TO_GET=ey2;Cv2.SEMATTRS_AWS_DYNAMODB_INDEX_NAME=Ak2;Cv2.SEMATTRS_AWS_DYNAMODB_SELECT=Bk2;Cv2.SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES=Qk2;Cv2.SEMATTRS_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES=Dk2;Cv2.SEMATTRS_AWS_DYNAMODB_EXCLUSIVE_START_TABLE=Zk2;Cv2.SEMATTRS_AWS_DYNAMODB_TABLE_COUNT=Gk2;Cv2.SEMATTRS_AWS_DYNAMODB_SCAN_FORWARD=Fk2;Cv2.SEMATTRS_AWS_DYNAMODB_SEGMENT=Ik2;Cv2.SEMATTRS_AWS_DYNAMODB_TOTAL_SEGMENTS=Yk2;Cv2.SEMATTRS_AWS_DYNAMODB_COUNT=Wk2;Cv2.SEMATTRS_AWS_DYNAMODB_SCANNED_COUNT=Jk2;Cv2.SEMATTRS_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS=Xk2;Cv2.SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES=Vk2;Cv2.SEMATTRS_MESSAGING_SYSTEM=Ck2;Cv2.SEMATTRS_MESSAGING_DESTINATION=Kk2;Cv2.SEMATTRS_MESSAGING_DESTINATION_KIND=Hk2;Cv2.SEMATTRS_MESSAGING_TEMP_DESTINATION=zk2;Cv2.SEMATTRS_MESSAGING_PROTOCOL=Ek2;Cv2.SEMATTRS_MESSAGING_PROTOCOL_VERSION=Uk2;Cv2.SEMATTRS_MESSAGING_URL=wk2;Cv2.SEMATTRS_MESSAGING_MESSAGE_ID=$k2;Cv2.SEMATTRS_MESSAGING_CONVERSATION_ID=qk2;Cv2.SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES=Nk2;Cv2.SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES=Lk2;Cv2.SEMATTRS_MESSAGING_OPERATION=Mk2;Cv2.SEMATTRS_MESSAGING_CONSUMER_ID=Rk2;Cv2.SEMATTRS_MESSAGING_RABBITMQ_ROUTING_KEY=Ok2;Cv2.SEMATTRS_MESSAGING_KAFKA_MESSAGE_KEY=Tk2;Cv2.SEMATTRS_MESSAGING_KAFKA_CONSUMER_GROUP=Pk2;Cv2.SEMATTRS_MESSAGING_KAFKA_CLIENT_ID=Sk2;Cv2.SEMATTRS_MESSAGING_KAFKA_PARTITION=jk2;Cv2.SEMATTRS_MESSAGING_KAFKA_TOMBSTONE=yk2;Cv2.SEMATTRS_RPC_SYSTEM=kk2;Cv2.SEMATTRS_RPC_SERVICE=_k2;Cv2.SEMATTRS_RPC_METHOD=xk2;Cv2.SEMATTRS_RPC_GRPC_STATUS_CODE=vk2;Cv2.SEMATTRS_RPC_JSONRPC_VERSION=bk2;Cv2.SEMATTRS_RPC_JSONRPC_REQUEST_ID=fk2;Cv2.SEMATTRS_RPC_JSONRPC_ERROR_CODE=hk2;Cv2.SEMATTRS_RPC_JSONRPC_ERROR_MESSAGE=gk2;Cv2.SEMATTRS_MESSAGE_TYPE=uk2;Cv2.SEMATTRS_MESSAGE_ID=mk2;Cv2.SEMATTRS_MESSAGE_COMPRESSED_SIZE=dk2;Cv2.SEMATTRS_MESSAGE_UNCOMPRESSED_SIZE=ck2;Cv2.SemanticAttributes=VE.createConstMap([Mj2,Rj2,Oj2,Tj2,Pj2,Sj2,jj2,yj2,kj2,_j2,xj2,vj2,bj2,fj2,hj2,gj2,uj2,mj2,dj2,cj2,lj2,pj2,ij2,nj2,aj2,sj2,rj2,oj2,tj2,ej2,Ay2,By2,Qy2,Dy2,Zy2,Gy2,Fy2,Iy2,Yy2,Wy2,Jy2,Xy2,Vy2,Cy2,Ky2,Hy2,zy2,Ey2,Uy2,wy2,$y2,qy2,Ny2,Ly2,My2,Ry2,Oy2,Ty2,Py2,Sy2,jy2,yy2,ky2,_y2,xy2,vy2,by2,fy2,hy2,gy2,uy2,my2,dy2,cy2,ly2,py2,iy2,ny2,ay2,sy2,ry2,oy2,ty2,ey2,Ak2,Bk2,Qk2,Dk2,Zk2,Gk2,Fk2,Ik2,Yk2,Wk2,Jk2,Xk2,Vk2,Ck2,Kk2,Hk2,zk2,Ek2,Uk2,wk2,$k2,qk2,Nk2,Lk2,Mk2,Rk2,Ok2,Tk2,Pk2,Sk2,jk2,yk2,kk2,_k2,xk2,vk2,bk2,fk2,hk2,gk2,uk2,mk2,dk2,ck2]);var lk2="other_sql",pk2="mssql",ik2="mysql",nk2="oracle",ak2="db2",sk2="postgresql",rk2="redshift",ok2="hive",tk2="cloudscape",ek2="hsqldb",A_2="progress",B_2="maxdb",Q_2="hanadb",D_2="ingres",Z_2="firstsql",G_2="edb",F_2="cache",I_2="adabas",Y_2="firebird",W_2="derby",J_2="filemaker",X_2="informix",V_2="instantdb",C_2="interbase",K_2="mariadb",H_2="netezza",z_2="pervasive",E_2="pointbase",U_2="sqlite",w_2="sybase",$_2="teradata",q_2="vertica",N_2="h2",L_2="coldfusion",M_2="cassandra",R_2="hbase",O_2="mongodb",T_2="redis",P_2="couchbase",S_2="couchdb",j_2="cosmosdb",y_2="dynamodb",k_2="neo4j",__2="geode",x_2="elasticsearch",v_2="memcached",b_2="cockroachdb";Cv2.DBSYSTEMVALUES_OTHER_SQL=lk2;Cv2.DBSYSTEMVALUES_MSSQL=pk2;Cv2.DBSYSTEMVALUES_MYSQL=ik2;Cv2.DBSYSTEMVALUES_ORACLE=nk2;Cv2.DBSYSTEMVALUES_DB2=ak2;Cv2.DBSYSTEMVALUES_POSTGRESQL=sk2;Cv2.DBSYSTEMVALUES_REDSHIFT=rk2;Cv2.DBSYSTEMVALUES_HIVE=ok2;Cv2.DBSYSTEMVALUES_CLOUDSCAPE=tk2;Cv2.DBSYSTEMVALUES_HSQLDB=ek2;Cv2.DBSYSTEMVALUES_PROGRESS=A_2;Cv2.DBSYSTEMVALUES_MAXDB=B_2;Cv2.DBSYSTEMVALUES_HANADB=Q_2;Cv2.DBSYSTEMVALUES_INGRES=D_2;Cv2.DBSYSTEMVALUES_FIRSTSQL=Z_2;Cv2.DBSYSTEMVALUES_EDB=G_2;Cv2.DBSYSTEMVALUES_CACHE=F_2;Cv2.DBSYSTEMVALUES_ADABAS=I_2;Cv2.DBSYSTEMVALUES_FIREBIRD=Y_2;Cv2.DBSYSTEMVALUES_DERBY=W_2;Cv2.DBSYSTEMVALUES_FILEMAKER=J_2;Cv2.DBSYSTEMVALUES_INFORMIX=X_2;Cv2.DBSYSTEMVALUES_INSTANTDB=V_2;Cv2.DBSYSTEMVALUES_INTERBASE=C_2;Cv2.DBSYSTEMVALUES_MARIADB=K_2;Cv2.DBSYSTEMVALUES_NETEZZA=H_2;Cv2.DBSYSTEMVALUES_PERVASIVE=z_2;Cv2.DBSYSTEMVALUES_POINTBASE=E_2;Cv2.DBSYSTEMVALUES_SQLITE=U_2;Cv2.DBSYSTEMVALUES_SYBASE=w_2;Cv2.DBSYSTEMVALUES_TERADATA=$_2;Cv2.DBSYSTEMVALUES_VERTICA=q_2;Cv2.DBSYSTEMVALUES_H2=N_2;Cv2.DBSYSTEMVALUES_COLDFUSION=L_2;Cv2.DBSYSTEMVALUES_CASSANDRA=M_2;Cv2.DBSYSTEMVALUES_HBASE=R_2;Cv2.DBSYSTEMVALUES_MONGODB=O_2;Cv2.DBSYSTEMVALUES_REDIS=T_2;Cv2.DBSYSTEMVALUES_COUCHBASE=P_2;Cv2.DBSYSTEMVALUES_COUCHDB=S_2;Cv2.DBSYSTEMVALUES_COSMOSDB=j_2;Cv2.DBSYSTEMVALUES_DYNAMODB=y_2;Cv2.DBSYSTEMVALUES_NEO4J=k_2;Cv2.DBSYSTEMVALUES_GEODE=__2;Cv2.DBSYSTEMVALUES_ELASTICSEARCH=x_2;Cv2.DBSYSTEMVALUES_MEMCACHED=v_2;Cv2.DBSYSTEMVALUES_COCKROACHDB=b_2;Cv2.DbSystemValues=VE.createConstMap([lk2,pk2,ik2,nk2,ak2,sk2,rk2,ok2,tk2,ek2,A_2,B_2,Q_2,D_2,Z_2,G_2,F_2,I_2,Y_2,W_2,J_2,X_2,V_2,C_2,K_2,H_2,z_2,E_2,U_2,w_2,$_2,q_2,N_2,L_2,M_2,R_2,O_2,T_2,P_2,S_2,j_2,y_2,k_2,__2,x_2,v_2,b_2]);var f_2="all",h_2="each_quorum",g_2="quorum",u_2="local_quorum",m_2="one",d_2="two",c_2="three",l_2="local_one",p_2="any",i_2="serial",n_2="local_serial";Cv2.DBCASSANDRACONSISTENCYLEVELVALUES_ALL=f_2;Cv2.DBCASSANDRACONSISTENCYLEVELVALUES_EACH_QUORUM=h_2;Cv2.DBCASSANDRACONSISTENCYLEVELVALUES_QUORUM=g_2;Cv2.DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_QUORUM=u_2;Cv2.DBCASSANDRACONSISTENCYLEVELVALUES_ONE=m_2;Cv2.DBCASSANDRACONSISTENCYLEVELVALUES_TWO=d_2;Cv2.DBCASSANDRACONSISTENCYLEVELVALUES_THREE=c_2;Cv2.DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_ONE=l_2;Cv2.DBCASSANDRACONSISTENCYLEVELVALUES_ANY=p_2;Cv2.DBCASSANDRACONSISTENCYLEVELVALUES_SERIAL=i_2;Cv2.DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_SERIAL=n_2;Cv2.DbCassandraConsistencyLevelValues=VE.createConstMap([f_2,h_2,g_2,u_2,m_2,d_2,c_2,l_2,p_2,i_2,n_2]);var a_2="datasource",s_2="http",r_2="pubsub",o_2="timer",t_2="other";Cv2.FAASTRIGGERVALUES_DATASOURCE=a_2;Cv2.FAASTRIGGERVALUES_HTTP=s_2;Cv2.FAASTRIGGERVALUES_PUBSUB=r_2;Cv2.FAASTRIGGERVALUES_TIMER=o_2;Cv2.FAASTRIGGERVALUES_OTHER=t_2;Cv2.FaasTriggerValues=VE.createConstMap([a_2,s_2,r_2,o_2,t_2]);var e_2="insert",Ax2="edit",Bx2="delete";Cv2.FAASDOCUMENTOPERATIONVALUES_INSERT=e_2;Cv2.FAASDOCUMENTOPERATIONVALUES_EDIT=Ax2;Cv2.FAASDOCUMENTOPERATIONVALUES_DELETE=Bx2;Cv2.FaasDocumentOperationValues=VE.createConstMap([e_2,Ax2,Bx2]);var Qx2="alibaba_cloud",Dx2="aws",Zx2="azure",Gx2="gcp";Cv2.FAASINVOKEDPROVIDERVALUES_ALIBABA_CLOUD=Qx2;Cv2.FAASINVOKEDPROVIDERVALUES_AWS=Dx2;Cv2.FAASINVOKEDPROVIDERVALUES_AZURE=Zx2;Cv2.FAASINVOKEDPROVIDERVALUES_GCP=Gx2;Cv2.FaasInvokedProviderValues=VE.createConstMap([Qx2,Dx2,Zx2,Gx2]);var Fx2="ip_tcp",Ix2="ip_udp",Yx2="ip",Wx2="unix",Jx2="pipe",Xx2="inproc",Vx2="other";Cv2.NETTRANSPORTVALUES_IP_TCP=Fx2;Cv2.NETTRANSPORTVALUES_IP_UDP=Ix2;Cv2.NETTRANSPORTVALUES_IP=Yx2;Cv2.NETTRANSPORTVALUES_UNIX=Wx2;Cv2.NETTRANSPORTVALUES_PIPE=Jx2;Cv2.NETTRANSPORTVALUES_INPROC=Xx2;Cv2.NETTRANSPORTVALUES_OTHER=Vx2;Cv2.NetTransportValues=VE.createConstMap([Fx2,Ix2,Yx2,Wx2,Jx2,Xx2,Vx2]);var Cx2="wifi",Kx2="wired",Hx2="cell",zx2="unavailable",Ex2="unknown";Cv2.NETHOSTCONNECTIONTYPEVALUES_WIFI=Cx2;Cv2.NETHOSTCONNECTIONTYPEVALUES_WIRED=Kx2;Cv2.NETHOSTCONNECTIONTYPEVALUES_CELL=Hx2;Cv2.NETHOSTCONNECTIONTYPEVALUES_UNAVAILABLE=zx2;Cv2.NETHOSTCONNECTIONTYPEVALUES_UNKNOWN=Ex2;Cv2.NetHostConnectionTypeValues=VE.createConstMap([Cx2,Kx2,Hx2,zx2,Ex2]);var Ux2="gprs",wx2="edge",$x2="umts",qx2="cdma",Nx2="evdo_0",Lx2="evdo_a",Mx2="cdma2000_1xrtt",Rx2="hsdpa",Ox2="hsupa",Tx2="hspa",Px2="iden",Sx2="evdo_b",jx2="lte",yx2="ehrpd",kx2="hspap",_x2="gsm",xx2="td_scdma",vx2="iwlan",bx2="nr",fx2="nrnsa",hx2="lte_ca";Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_GPRS=Ux2;Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_EDGE=wx2;Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_UMTS=$x2;Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_CDMA=qx2;Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_0=Nx2;Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_A=Lx2;Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_CDMA2000_1XRTT=Mx2;Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_HSDPA=Rx2;Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_HSUPA=Ox2;Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_HSPA=Tx2;Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_IDEN=Px2;Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_B=Sx2;Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_LTE=jx2;Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_EHRPD=yx2;Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_HSPAP=kx2;Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_GSM=_x2;Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_TD_SCDMA=xx2;Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_IWLAN=vx2;Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_NR=bx2;Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_NRNSA=fx2;Cv2.NETHOSTCONNECTIONSUBTYPEVALUES_LTE_CA=hx2;Cv2.NetHostConnectionSubtypeValues=VE.createConstMap([Ux2,wx2,$x2,qx2,Nx2,Lx2,Mx2,Rx2,Ox2,Tx2,Px2,Sx2,jx2,yx2,kx2,_x2,xx2,vx2,bx2,fx2,hx2]);var gx2="1.0",ux2="1.1",mx2="2.0",dx2="SPDY",cx2="QUIC";Cv2.HTTPFLAVORVALUES_HTTP_1_0=gx2;Cv2.HTTPFLAVORVALUES_HTTP_1_1=ux2;Cv2.HTTPFLAVORVALUES_HTTP_2_0=mx2;Cv2.HTTPFLAVORVALUES_SPDY=dx2;Cv2.HTTPFLAVORVALUES_QUIC=cx2;Cv2.HttpFlavorValues={HTTP_1_0:gx2,HTTP_1_1:ux2,HTTP_2_0:mx2,SPDY:dx2,QUIC:cx2};var lx2="queue",px2="topic";Cv2.MESSAGINGDESTINATIONKINDVALUES_QUEUE=lx2;Cv2.MESSAGINGDESTINATIONKINDVALUES_TOPIC=px2;Cv2.MessagingDestinationKindValues=VE.createConstMap([lx2,px2]);var ix2="receive",nx2="process";Cv2.MESSAGINGOPERATIONVALUES_RECEIVE=ix2;Cv2.MESSAGINGOPERATIONVALUES_PROCESS=nx2;Cv2.MessagingOperationValues=VE.createConstMap([ix2,nx2]);var ax2=0,sx2=1,rx2=2,ox2=3,tx2=4,ex2=5,Av2=6,Bv2=7,Qv2=8,Dv2=9,Zv2=10,Gv2=11,Fv2=12,Iv2=13,Yv2=14,Wv2=15,Jv2=16;Cv2.RPCGRPCSTATUSCODEVALUES_OK=ax2;Cv2.RPCGRPCSTATUSCODEVALUES_CANCELLED=sx2;Cv2.RPCGRPCSTATUSCODEVALUES_UNKNOWN=rx2;Cv2.RPCGRPCSTATUSCODEVALUES_INVALID_ARGUMENT=ox2;Cv2.RPCGRPCSTATUSCODEVALUES_DEADLINE_EXCEEDED=tx2;Cv2.RPCGRPCSTATUSCODEVALUES_NOT_FOUND=ex2;Cv2.RPCGRPCSTATUSCODEVALUES_ALREADY_EXISTS=Av2;Cv2.RPCGRPCSTATUSCODEVALUES_PERMISSION_DENIED=Bv2;Cv2.RPCGRPCSTATUSCODEVALUES_RESOURCE_EXHAUSTED=Qv2;Cv2.RPCGRPCSTATUSCODEVALUES_FAILED_PRECONDITION=Dv2;Cv2.RPCGRPCSTATUSCODEVALUES_ABORTED=Zv2;Cv2.RPCGRPCSTATUSCODEVALUES_OUT_OF_RANGE=Gv2;Cv2.RPCGRPCSTATUSCODEVALUES_UNIMPLEMENTED=Fv2;Cv2.RPCGRPCSTATUSCODEVALUES_INTERNAL=Iv2;Cv2.RPCGRPCSTATUSCODEVALUES_UNAVAILABLE=Yv2;Cv2.RPCGRPCSTATUSCODEVALUES_DATA_LOSS=Wv2;Cv2.RPCGRPCSTATUSCODEVALUES_UNAUTHENTICATED=Jv2;Cv2.RpcGrpcStatusCodeValues={OK:ax2,CANCELLED:sx2,UNKNOWN:rx2,INVALID_ARGUMENT:ox2,DEADLINE_EXCEEDED:tx2,NOT_FOUND:ex2,ALREADY_EXISTS:Av2,PERMISSION_DENIED:Bv2,RESOURCE_EXHAUSTED:Qv2,FAILED_PRECONDITION:Dv2,ABORTED:Zv2,OUT_OF_RANGE:Gv2,UNIMPLEMENTED:Fv2,INTERNAL:Iv2,UNAVAILABLE:Yv2,DATA_LOSS:Wv2,UNAUTHENTICATED:Jv2};var Xv2="SENT",Vv2="RECEIVED";Cv2.MESSAGETYPEVALUES_SENT=Xv2;Cv2.MESSAGETYPEVALUES_RECEIVED=Vv2;Cv2.MessageTypeValues=VE.createConstMap([Xv2,Vv2])});
var AS2=E((tP2)=>{Object.defineProperty(tP2,"__esModule",{value:!0});tP2.HistogramAggregator=tP2.HistogramAccumulation=void 0;var ge4=So(),i51=f_(),ue4=tw();function me4(A){let B=A.map(()=>0);return B.push(0),{buckets:{boundaries:A,counts:B},sum:0,count:0,hasMinMax:!1,min:1/0,max:-1/0}}class n51{startTime;_boundaries;_recordMinMax;_current;constructor(A,B,Q=!0,D=me4(B)){this.startTime=A,this._boundaries=B,this._recordMinMax=Q,this._current=D}record(A){if(Number.isNaN(A))return;if(this._current.count+=1,this._current.sum+=A,this._recordMinMax)this._current.min=Math.min(A,this._current.min),this._current.max=Math.max(A,this._current.max),this._current.hasMinMax=!0;let B=ue4.binarySearchUB(this._boundaries,A);this._current.buckets.counts[B]+=1}setStartTime(A){this.startTime=A}toPointValue(){return this._current}}tP2.HistogramAccumulation=n51;class oP2{_boundaries;_recordMinMax;kind=ge4.AggregatorKind.HISTOGRAM;constructor(A,B){this._boundaries=A,this._recordMinMax=B}createAccumulation(A){return new n51(A,this._boundaries,this._recordMinMax)}merge(A,B){let Q=A.toPointValue(),D=B.toPointValue(),Z=Q.buckets.counts,G=D.buckets.counts,F=new Array(Z.length);for(let W=0;W<Z.length;W++)F[W]=Z[W]+G[W];let I=1/0,Y=-1/0;if(this._recordMinMax){if(Q.hasMinMax&&D.hasMinMax)I=Math.min(Q.min,D.min),Y=Math.max(Q.max,D.max);else if(Q.hasMinMax)I=Q.min,Y=Q.max;else if(D.hasMinMax)I=D.min,Y=D.max}return new n51(A.startTime,Q.buckets.boundaries,this._recordMinMax,{buckets:{boundaries:Q.buckets.boundaries,counts:F},count:Q.count+D.count,sum:Q.sum+D.sum,hasMinMax:this._recordMinMax&&(Q.hasMinMax||D.hasMinMax),min:I,max:Y})}diff(A,B){let Q=A.toPointValue(),D=B.toPointValue(),Z=Q.buckets.counts,G=D.buckets.counts,F=new Array(Z.length);for(let I=0;I<Z.length;I++)F[I]=G[I]-Z[I];return new n51(B.startTime,Q.buckets.boundaries,this._recordMinMax,{buckets:{boundaries:Q.buckets.boundaries,counts:F},count:D.count-Q.count,sum:D.sum-Q.sum,hasMinMax:!1,min:1/0,max:-1/0})}toMetricData(A,B,Q,D){return{descriptor:A,aggregationTemporality:B,dataPointType:i51.DataPointType.HISTOGRAM,dataPoints:Q.map(([Z,G])=>{let F=G.toPointValue(),I=A.type===i51.InstrumentType.GAUGE||A.type===i51.InstrumentType.UP_DOWN_COUNTER||A.type===i51.InstrumentType.OBSERVABLE_GAUGE||A.type===i51.InstrumentType.OBSERVABLE_UP_DOWN_COUNTER;return{attributes:Z,startTime:G.startTime,endTime:D,value:{min:F.hasMinMax?F.min:void 0,max:F.hasMinMax?F.max:void 0,sum:!I?F.sum:void 0,buckets:F.buckets,count:F.count}}})}}}tP2.HistogramAggregator=oP2});
var Ai2=E((JW0)=>{Object.defineProperty(JW0,"__esModule",{value:!0});JW0.JsonMetricsSerializer=void 0;var JY6=ep2();Object.defineProperty(JW0,"JsonMetricsSerializer",{enumerable:!0,get:function(){return JY6.JsonMetricsSerializer}})});
var Aj2=E((tS2)=>{Object.defineProperty(tS2,"__esModule",{value:!0});tS2.isAttributeValue=tS2.isAttributeKey=tS2.sanitizeAttributes=void 0;var aS2=VQ();function _16(A){let B={};if(typeof A!=="object"||A==null)return B;for(let[Q,D]of Object.entries(A)){if(!sS2(Q)){aS2.diag.warn(`Invalid attribute key: ${Q}`);continue}if(!rS2(D)){aS2.diag.warn(`Invalid attribute value set for key: ${Q}`);continue}if(Array.isArray(D))B[Q]=D.slice();else B[Q]=D}return B}tS2.sanitizeAttributes=_16;function sS2(A){return typeof A==="string"&&A.length>0}tS2.isAttributeKey=sS2;function rS2(A){if(A==null)return!0;if(Array.isArray(A))return x16(A);return oS2(A)}tS2.isAttributeValue=rS2;function x16(A){let B;for(let Q of A){if(Q==null)continue;if(!B){if(oS2(Q)){B=typeof Q;continue}return!1}if(typeof Q===B)continue;return!1}return!0}function oS2(A){switch(typeof A){case"number":case"boolean":case"string":return!0}return!1}});
var Am2=E((tu2)=>{Object.defineProperty(tu2,"__esModule",{value:!0});tu2.getMachineId=void 0;var ou2=J1("process"),XZ6=YT1(),VZ6=VQ();async function CZ6(){let B="%windir%\\System32\\REG.exe";if(ou2.arch==="ia32"&&"PROCESSOR_ARCHITEW6432"in ou2.env)B="%windir%\\sysnative\\cmd.exe /c "+B;try{let D=(await XZ6.execAsync(`${B} QUERY HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Cryptography /v MachineGuid`)).stdout.split("REG_SZ");if(D.length===2)return D[1].trim()}catch(Q){VZ6.diag.debug(`error reading machine id: ${Q}`)}return}tu2.getMachineId=CZ6});
var An2=E((KW0)=>{Object.defineProperty(KW0,"__esModule",{value:!0});KW0.OTLPMetricExporter=void 0;var LW6=ei2();Object.defineProperty(KW0,"OTLPMetricExporter",{enumerable:!0,get:function(){return LW6.OTLPMetricExporter}})});
var Bc2=E((ed2)=>{Object.defineProperty(ed2,"__esModule",{value:!0});ed2.MeterSharedState=void 0;var hG6=F31(),gG6=em2(),uG6=tw(),mG6=Kd2(),dG6=Rd2(),cG6=Sd2(),lG6=md2(),pG6=pd2(),iG6=KT1();class td2{_meterProviderSharedState;_instrumentationScope;metricStorageRegistry=new dG6.MetricStorageRegistry;observableRegistry=new lG6.ObservableRegistry;meter;constructor(A,B){this._meterProviderSharedState=A,this._instrumentationScope=B,this.meter=new gG6.Meter(this)}registerMetricStorage(A){let B=this._registerMetricStorage(A,pG6.SyncMetricStorage);if(B.length===1)return B[0];return new cG6.MultiMetricStorage(B)}registerAsyncMetricStorage(A){return this._registerMetricStorage(A,mG6.AsyncMetricStorage)}async collect(A,B,Q){let D=await this.observableRegistry.observe(B,Q?.timeoutMillis),Z=this.metricStorageRegistry.getStorages(A);if(Z.length===0)return null;let G=Z.map((F)=>{return F.collect(A,B)}).filter(uG6.isNotNullish);if(G.length===0)return{errors:D};return{scopeMetrics:{scope:this._instrumentationScope,metrics:G},errors:D}}_registerMetricStorage(A,B){let D=this._meterProviderSharedState.viewRegistry.findViews(A,this._instrumentationScope).map((Z)=>{let G=hG6.createInstrumentDescriptorWithView(Z,A),F=this.metricStorageRegistry.findOrUpdateCompatibleStorage(G);if(F!=null)return F;let I=Z.aggregation.createAggregator(G),Y=new B(G,I,Z.attributesProcessor,this._meterProviderSharedState.metricCollectors,Z.aggregationCardinalityLimit);return this.metricStorageRegistry.register(Y),Y});if(D.length===0){let G=this._meterProviderSharedState.selectAggregations(A.type).map(([F,I])=>{let Y=this.metricStorageRegistry.findOrUpdateCompatibleCollectorStorage(F,A);if(Y!=null)return Y;let W=I.createAggregator(A),J=F.selectCardinalityLimit(A.type),X=new B(A,W,iG6.createNoopAttributesProcessor(),[F],J);return this.metricStorageRegistry.registerForCollector(F,X),X});D=D.concat(G)}return D}}ed2.MeterSharedState=td2});
var Bn2=E((HW0)=>{Object.defineProperty(HW0,"__esModule",{value:!0});HW0.OTLPMetricExporter=void 0;var RW6=An2();Object.defineProperty(HW0,"OTLPMetricExporter",{enumerable:!0,get:function(){return RW6.OTLPMetricExporter}})});
var CP=E((sL)=>{var j36=sL&&sL.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),iO1=sL&&sL.__exportStar||function(A,B){for(var Q in A)if(Q!=="default"&&!Object.prototype.hasOwnProperty.call(B,Q))j36(B,A,Q)};Object.defineProperty(sL,"__esModule",{value:!0});iO1(qv2(),sL);iO1(of2(),sL);iO1(Qh2(),sL);iO1(Gh2(),sL)});
var DO2=E((BO2)=>{Object.defineProperty(BO2,"__esModule",{value:!0});BO2.DiagConsoleLogger=void 0;var SF0=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class AO2{constructor(){function A(B){return function(...Q){if(console){let D=console[B];if(typeof D!=="function")D=console.log;if(typeof D==="function")return D.apply(console,Q)}}}for(let B=0;B<SF0.length;B++)this[SF0[B].n]=A(SF0[B].c)}}BO2.DiagConsoleLogger=AO2});
var DP2=E((BP2)=>{Object.defineProperty(BP2,"__esModule",{value:!0});BP2.TraceAPI=void 0;var CI0=zu(),tT2=eF0(),eT2=fO1(),Po=aF0(),AP2=Eu(),KI0="trace";class HI0{constructor(){this._proxyTracerProvider=new tT2.ProxyTracerProvider,this.wrapSpanContext=eT2.wrapSpanContext,this.isSpanContextValid=eT2.isSpanContextValid,this.deleteSpan=Po.deleteSpan,this.getSpan=Po.getSpan,this.getActiveSpan=Po.getActiveSpan,this.getSpanContext=Po.getSpanContext,this.setSpan=Po.setSpan,this.setSpanContext=Po.setSpanContext}static getInstance(){if(!this._instance)this._instance=new HI0;return this._instance}setGlobalTracerProvider(A){let B=CI0.registerGlobal(KI0,this._proxyTracerProvider,AP2.DiagAPI.instance());if(B)this._proxyTracerProvider.setDelegate(A);return B}getTracerProvider(){return CI0.getGlobal(KI0)||this._proxyTracerProvider}getTracer(A,B){return this.getTracerProvider().getTracer(A,B)}disable(){CI0.unregisterGlobal(KI0,AP2.DiagAPI.instance()),this._proxyTracerProvider=new tT2.ProxyTracerProvider}}BP2.TraceAPI=HI0});
var DS2=E((BS2)=>{Object.defineProperty(BS2,"__esModule",{value:!0});BS2.Buckets=void 0;class jI0{backing;indexBase;indexStart;indexEnd;constructor(A=new yI0,B=0,Q=0,D=0){this.backing=A,this.indexBase=B,this.indexStart=Q,this.indexEnd=D}get offset(){return this.indexStart}get length(){if(this.backing.length===0)return 0;if(this.indexEnd===this.indexStart&&this.at(0)===0)return 0;return this.indexEnd-this.indexStart+1}counts(){return Array.from({length:this.length},(A,B)=>this.at(B))}at(A){let B=this.indexBase-this.indexStart;if(A<B)A+=this.backing.length;return A-=B,this.backing.countAt(A)}incrementBucket(A,B){this.backing.increment(A,B)}decrementBucket(A,B){this.backing.decrement(A,B)}trim(){for(let A=0;A<this.length;A++)if(this.at(A)!==0){this.indexStart+=A;break}else if(A===this.length-1){this.indexStart=this.indexEnd=this.indexBase=0;return}for(let A=this.length-1;A>=0;A--)if(this.at(A)!==0){this.indexEnd-=this.length-A-1;break}this._rotate()}downscale(A){this._rotate();let B=1+this.indexEnd-this.indexStart,Q=1<<A,D=0,Z=0;for(let G=this.indexStart;G<=this.indexEnd;){let F=G%Q;if(F<0)F+=Q;for(let I=F;I<Q&&D<B;I++)this._relocateBucket(Z,D),D++,G++;Z++}this.indexStart>>=A,this.indexEnd>>=A,this.indexBase=this.indexStart}clone(){return new jI0(this.backing.clone(),this.indexBase,this.indexStart,this.indexEnd)}_rotate(){let A=this.indexBase-this.indexStart;if(A===0)return;else if(A>0)this.backing.reverse(0,this.backing.length),this.backing.reverse(0,A),this.backing.reverse(A,this.backing.length);else this.backing.reverse(0,this.backing.length),this.backing.reverse(0,this.backing.length+A);this.indexBase=this.indexStart}_relocateBucket(A,B){if(A===B)return;this.incrementBucket(A,this.backing.emptyBucket(B))}}BS2.Buckets=jI0;class yI0{_counts;constructor(A=[0]){this._counts=A}get length(){return this._counts.length}countAt(A){return this._counts[A]}growTo(A,B,Q){let D=new Array(A).fill(0);D.splice(Q,this._counts.length-B,...this._counts.slice(B)),D.splice(0,B,...this._counts.slice(0,B)),this._counts=D}reverse(A,B){let Q=Math.floor((A+B)/2)-A;for(let D=0;D<Q;D++){let Z=this._counts[A+D];this._counts[A+D]=this._counts[B-D-1],this._counts[B-D-1]=Z}}emptyBucket(A){let B=this._counts[A];return this._counts[A]=0,B}increment(A,B){this._counts[A]+=B}decrement(A,B){if(this._counts[A]>=B)this._counts[A]-=B;else this._counts[A]=0}clone(){return new yI0([...this._counts])}}});
var DW0=E((Up2)=>{Object.defineProperty(Up2,"__esModule",{value:!0});Up2.toLogAttributes=Up2.createExportLogsServiceRequest=void 0;var NI6=OT1(),PT1=TT1();function LI6(A,B){let Q=NI6.getOtlpEncoder(B);return{resourceLogs:RI6(A,Q)}}Up2.createExportLogsServiceRequest=LI6;function MI6(A){let B=new Map;for(let Q of A){let{resource:D,instrumentationScope:{name:Z,version:G="",schemaUrl:F=""}}=Q,I=B.get(D);if(!I)I=new Map,B.set(D,I);let Y=`${Z}@${G}:${F}`,W=I.get(Y);if(!W)W=[],I.set(Y,W);W.push(Q)}return B}function RI6(A,B){let Q=MI6(A);return Array.from(Q,([D,Z])=>({resource:PT1.createResource(D),scopeLogs:Array.from(Z,([,G])=>{return{scope:PT1.createInstrumentationScope(G[0].instrumentationScope),logRecords:G.map((F)=>OI6(F,B)),schemaUrl:G[0].instrumentationScope.schemaUrl}}),schemaUrl:void 0}))}function OI6(A,B){return{timeUnixNano:B.encodeHrTime(A.hrTime),observedTimeUnixNano:B.encodeHrTime(A.hrTimeObserved),severityNumber:TI6(A.severityNumber),severityText:A.severityText,body:PT1.toAnyValue(A.body),attributes:Ep2(A.attributes),droppedAttributesCount:A.droppedAttributesCount,flags:A.spanContext?.traceFlags,traceId:B.encodeOptionalSpanContext(A.spanContext?.traceId),spanId:B.encodeOptionalSpanContext(A.spanContext?.spanId)}}function TI6(A){return A}function Ep2(A){return Object.keys(A).map((B)=>PT1.toKeyValue(B,A[B]))}Up2.toLogAttributes=Ep2});
var DY0=E((Fu2)=>{Object.defineProperty(Fu2,"__esModule",{value:!0});Fu2.MetricReader=void 0;var Du2=VQ(),FT1=tw(),Zu2=QY0();class Gu2{_shutdown=!1;_metricProducers;_sdkMetricProducer;_aggregationTemporalitySelector;_aggregationSelector;_cardinalitySelector;constructor(A){this._aggregationSelector=A?.aggregationSelector??Zu2.DEFAULT_AGGREGATION_SELECTOR,this._aggregationTemporalitySelector=A?.aggregationTemporalitySelector??Zu2.DEFAULT_AGGREGATION_TEMPORALITY_SELECTOR,this._metricProducers=A?.metricProducers??[],this._cardinalitySelector=A?.cardinalitySelector}setMetricProducer(A){if(this._sdkMetricProducer)throw new Error("MetricReader can not be bound to a MeterProvider again.");this._sdkMetricProducer=A,this.onInitialized()}selectAggregation(A){return this._aggregationSelector(A)}selectAggregationTemporality(A){return this._aggregationTemporalitySelector(A)}selectCardinalityLimit(A){return this._cardinalitySelector?this._cardinalitySelector(A):2000}onInitialized(){}async collect(A){if(this._sdkMetricProducer===void 0)throw new Error("MetricReader is not bound to a MetricProducer");if(this._shutdown)throw new Error("MetricReader is shutdown");let[B,...Q]=await Promise.all([this._sdkMetricProducer.collect({timeoutMillis:A?.timeoutMillis}),...this._metricProducers.map((F)=>F.collect({timeoutMillis:A?.timeoutMillis}))]),D=B.errors.concat(FT1.FlatMap(Q,(F)=>F.errors)),Z=B.resourceMetrics.resource,G=B.resourceMetrics.scopeMetrics.concat(FT1.FlatMap(Q,(F)=>F.resourceMetrics.scopeMetrics));return{resourceMetrics:{resource:Z,scopeMetrics:G},errors:D}}async shutdown(A){if(this._shutdown){Du2.diag.error("Cannot call shutdown twice.");return}if(A?.timeoutMillis==null)await this.onShutdown();else await FT1.callWithTimeout(this.onShutdown(),A.timeoutMillis);this._shutdown=!0}async forceFlush(A){if(this._shutdown){Du2.diag.warn("Cannot forceFlush on already shutdown MetricReader.");return}if(A?.timeoutMillis==null){await this.onForceFlush();return}await FT1.callWithTimeout(this.onForceFlush(),A.timeoutMillis)}}Fu2.MetricReader=Gu2});
var Dg2=E((Bg2)=>{Object.defineProperty(Bg2,"__esModule",{value:!0});Bg2.merge=void 0;var oh2=rh2(),d76=20;function c76(...A){let B=A.shift(),Q=new WeakMap;while(A.length>0)B=eh2(B,A.shift(),0,Q);return B}Bg2.merge=c76;function oI0(A){if(eO1(A))return A.slice();return A}function eh2(A,B,Q=0,D){let Z;if(Q>d76)return;if(Q++,tO1(A)||tO1(B)||Ag2(B))Z=oI0(B);else if(eO1(A)){if(Z=A.slice(),eO1(B))for(let G=0,F=B.length;G<F;G++)Z.push(oI0(B[G]));else if(t51(B)){let G=Object.keys(B);for(let F=0,I=G.length;F<I;F++){let Y=G[F];Z[Y]=oI0(B[Y])}}}else if(t51(A))if(t51(B)){if(!l76(A,B))return B;Z=Object.assign({},A);let G=Object.keys(B);for(let F=0,I=G.length;F<I;F++){let Y=G[F],W=B[Y];if(tO1(W))if(typeof W==="undefined")delete Z[Y];else Z[Y]=W;else{let J=Z[Y],X=W;if(th2(A,Y,D)||th2(B,Y,D))delete Z[Y];else{if(t51(J)&&t51(X)){let V=D.get(J)||[],C=D.get(X)||[];V.push({obj:A,key:Y}),C.push({obj:B,key:Y}),D.set(J,V),D.set(X,C)}Z[Y]=eh2(Z[Y],W,Q,D)}}}}else Z=B;return Z}function th2(A,B,Q){let D=Q.get(A[B])||[];for(let Z=0,G=D.length;Z<G;Z++){let F=D[Z];if(F.key===B&&F.obj===A)return!0}return!1}function eO1(A){return Array.isArray(A)}function Ag2(A){return typeof A==="function"}function t51(A){return!tO1(A)&&!eO1(A)&&!Ag2(A)&&typeof A==="object"}function tO1(A){return typeof A==="string"||typeof A==="number"||typeof A==="boolean"||typeof A==="undefined"||A instanceof Date||A instanceof RegExp||A===null}function l76(A,B){if(!oh2.isPlainObject(A)||!oh2.isPlainObject(B))return!1;return!0}});
var Di2=E((Bi2)=>{Object.defineProperty(Bi2,"__esModule",{value:!0});Bi2.JsonTraceSerializer=void 0;var VY6=IW0();Bi2.JsonTraceSerializer={serializeRequest:(A)=>{let B=VY6.createExportTraceServiceRequest(A,{useHex:!0,useLongBits:!1});return new TextEncoder().encode(JSON.stringify(B))},deserializeResponse:(A)=>{return JSON.parse(new TextDecoder().decode(A))}}});
var Dm2=E((Bm2)=>{Object.defineProperty(Bm2,"__esModule",{value:!0});Bm2.getMachineId=void 0;var KZ6=VQ();async function HZ6(){KZ6.diag.debug("could not read machine-id: unsupported platform");return}Bm2.getMachineId=HZ6});
var ET1=E((uc2)=>{Object.defineProperty(uc2,"__esModule",{value:!0});uc2.OTLPExporterError=void 0;class gc2 extends Error{code;name="OTLPExporterError";data;constructor(A,B,Q){super(A);this.data=Q,this.code=B}}uc2.OTLPExporterError=gc2});
var Ec2=E((Hc2)=>{Object.defineProperty(Hc2,"__esModule",{value:!0});Hc2.InstrumentSelector=void 0;var Cc2=HT1();class Kc2{_nameFilter;_type;_unitFilter;constructor(A){this._nameFilter=new Cc2.PatternPredicate(A?.name??"*"),this._type=A?.type,this._unitFilter=new Cc2.ExactPredicate(A?.unit)}getType(){return this._type}getNameFilter(){return this._nameFilter}getUnitFilter(){return this._unitFilter}}Hc2.InstrumentSelector=Kc2});
var Ei2=E((Hi2)=>{Object.defineProperty(Hi2,"__esModule",{value:!0});Hi2.createHttpAgent=Hi2.compressAndSend=Hi2.sendWithHttp=void 0;var Vi2=J1("http"),Ci2=J1("https"),RY6=J1("zlib"),OY6=J1("stream"),Xi2=Ji2(),TY6=ET1();function PY6(A,B,Q,D,Z){let G=new URL(A.url),F=Number(process.versions.node.split(".")[0]),I={hostname:G.hostname,port:G.port,path:G.pathname,method:"POST",headers:{...A.headers()},agent:B},W=(G.protocol==="http:"?Vi2.request:Ci2.request)(I,(X)=>{let V=[];X.on("data",(C)=>V.push(C)),X.on("end",()=>{if(X.statusCode&&X.statusCode<299)D({status:"success",data:Buffer.concat(V)});else if(X.statusCode&&Xi2.isExportRetryable(X.statusCode))D({status:"retryable",retryInMillis:Xi2.parseRetryAfterToMills(X.headers["retry-after"])});else{let C=new TY6.OTLPExporterError(X.statusMessage,X.statusCode,Buffer.concat(V).toString());D({status:"failure",error:C})}})});W.setTimeout(Z,()=>{W.destroy(),D({status:"failure",error:new Error("Request Timeout")})}),W.on("error",(X)=>{D({status:"failure",error:X})});let J=F>=14?"close":"abort";W.on(J,()=>{D({status:"failure",error:new Error("Request timed out")})}),Ki2(W,A.compression,Q,(X)=>{D({status:"failure",error:X})})}Hi2.sendWithHttp=PY6;function Ki2(A,B,Q,D){let Z=SY6(Q);if(B==="gzip")A.setHeader("Content-Encoding","gzip"),Z=Z.on("error",D).pipe(RY6.createGzip()).on("error",D);Z.pipe(A).on("error",D)}Hi2.compressAndSend=Ki2;function SY6(A){let B=new OY6.Readable;return B.push(A),B.push(null),B}function jY6(A,B){return new(new URL(A).protocol==="http:"?Vi2.Agent:Ci2.Agent)(B)}Hi2.createHttpAgent=jY6});
var El2=E((wh5,zl2)=>{zl2.exports=$T1;function $T1(){this._listeners={}}$T1.prototype.on=function A(B,Q,D){return(this._listeners[B]||(this._listeners[B]=[])).push({fn:Q,ctx:D||this}),this};$T1.prototype.off=function A(B,Q){if(B===void 0)this._listeners={};else if(Q===void 0)this._listeners[B]=[];else{var D=this._listeners[B];for(var Z=0;Z<D.length;)if(D[Z].fn===Q)D.splice(Z,1);else++Z}return this};$T1.prototype.emit=function A(B){var Q=this._listeners[B];if(Q){var D=[],Z=1;for(;Z<arguments.length;)D.push(arguments[Z++]);for(Z=0;Z<Q.length;)Q[Z].fn.apply(Q[Z++].ctx,D)}return this}});
var Eu=E((dR2)=>{Object.defineProperty(dR2,"__esModule",{value:!0});dR2.DiagAPI=void 0;var ar4=bR2(),sr4=uR2(),mR2=kO1(),_O1=zu(),rr4="diag";class TF0{constructor(){function A(D){return function(...Z){let G=_O1.getGlobal("diag");if(!G)return;return G[D](...Z)}}let B=this,Q=(D,Z={logLevel:mR2.DiagLogLevel.INFO})=>{var G,F,I;if(D===B){let J=new Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return B.error((G=J.stack)!==null&&G!==void 0?G:J.message),!1}if(typeof Z==="number")Z={logLevel:Z};let Y=_O1.getGlobal("diag"),W=sr4.createLogLevelDiagLogger((F=Z.logLevel)!==null&&F!==void 0?F:mR2.DiagLogLevel.INFO,D);if(Y&&!Z.suppressOverrideMessage){let J=(I=new Error().stack)!==null&&I!==void 0?I:"<failed to generate stacktrace>";Y.warn(`Current logger will be overwritten from ${J}`),W.warn(`Current logger will overwrite one already registered from ${J}`)}return _O1.registerGlobal("diag",W,B,!0)};B.setLogger=Q,B.disable=()=>{_O1.unregisterGlobal(rr4,B)},B.createComponentLogger=(D)=>{return new ar4.DiagComponentLogger(D)},B.verbose=A("verbose"),B.debug=A("debug"),B.info=A("info"),B.warn=A("warn"),B.error=A("error")}static instance(){if(!this._instance)this._instance=new TF0;return this._instance}}dR2.DiagAPI=TF0});
var Eu2=E((Hu2)=>{Object.defineProperty(Hu2,"__esModule",{value:!0});Hu2.InMemoryMetricExporter=void 0;var Cu2=x3();class Ku2{_shutdown=!1;_aggregationTemporality;_metrics=[];constructor(A){this._aggregationTemporality=A}export(A,B){if(this._shutdown){setTimeout(()=>B({code:Cu2.ExportResultCode.FAILED}),0);return}this._metrics.push(A),setTimeout(()=>B({code:Cu2.ExportResultCode.SUCCESS}),0)}getMetrics(){return this._metrics}forceFlush(){return Promise.resolve()}reset(){this._metrics=[]}selectAggregationTemporality(A){return this._aggregationTemporality}shutdown(){return this._shutdown=!0,Promise.resolve()}}Hu2.InMemoryMetricExporter=Ku2});
var F31=E((gm2)=>{Object.defineProperty(gm2,"__esModule",{value:!0});gm2.isValidName=gm2.isDescriptorCompatibleWith=gm2.createInstrumentDescriptorWithView=gm2.createInstrumentDescriptor=void 0;var fm2=VQ(),uZ6=tw();function mZ6(A,B,Q){if(!hm2(A))fm2.diag.warn(`Invalid metric name: "${A}". The metric name should be a ASCII string with a length no greater than 255 characters.`);return{name:A,type:B,description:Q?.description??"",unit:Q?.unit??"",valueType:Q?.valueType??fm2.ValueType.DOUBLE,advice:Q?.advice??{}}}gm2.createInstrumentDescriptor=mZ6;function dZ6(A,B){return{name:A.name??B.name,description:A.description??B.description,type:B.type,unit:B.unit,valueType:B.valueType,advice:B.advice}}gm2.createInstrumentDescriptorWithView=dZ6;function cZ6(A,B){return uZ6.equalsCaseInsensitive(A.name,B.name)&&A.unit===B.unit&&A.type===B.type&&A.valueType===B.valueType}gm2.isDescriptorCompatibleWith=cZ6;var lZ6=/^[a-z][a-z0-9_.\-/]{0,254}$/i;function hm2(A){return A.match(lZ6)!=null}gm2.isValidName=hm2});
var FP2=E((ZP2)=>{Object.defineProperty(ZP2,"__esModule",{value:!0});ZP2.trace=void 0;var kt4=DP2();ZP2.trace=kt4.TraceAPI.getInstance()});
var FT2=E((GT2)=>{Object.defineProperty(GT2,"__esModule",{value:!0});GT2.SpanStatusCode=void 0;var Qt4;(function(A){A[A.UNSET=0]="UNSET",A[A.OK=1]="OK",A[A.ERROR=2]="ERROR"})(Qt4=GT2.SpanStatusCode||(GT2.SpanStatusCode={}))});
var Fg2=E((Zg2)=>{Object.defineProperty(Zg2,"__esModule",{value:!0});Zg2.callWithTimeout=Zg2.TimeoutError=void 0;class AT1 extends Error{constructor(A){super(A);Object.setPrototypeOf(this,AT1.prototype)}}Zg2.TimeoutError=AT1;function p76(A,B){let Q,D=new Promise(function Z(G,F){Q=setTimeout(function I(){F(new AT1("Operation timed out."))},B)});return Promise.race([A,D]).then((Z)=>{return clearTimeout(Q),Z},(Z)=>{throw clearTimeout(Q),Z})}Zg2.callWithTimeout=p76});
var Fj2=E((Zj2)=>{Object.defineProperty(Zj2,"__esModule",{value:!0});Zj2.globalErrorHandler=Zj2.setGlobalErrorHandler=void 0;var m16=uI0(),Dj2=m16.loggingErrorHandler();function d16(A){Dj2=A}Zj2.setGlobalErrorHandler=d16;function c16(A){try{Dj2(A)}catch{}}Zj2.globalErrorHandler=c16});
var GW0=E((jp2)=>{Object.defineProperty(jp2,"__esModule",{value:!0});jp2.createExportMetricsServiceRequest=jp2.toMetric=jp2.toScopeMetrics=jp2.toResourceMetrics=void 0;var Rp2=VQ(),mo=m_(),xI6=OT1(),K31=TT1();function Tp2(A,B){let Q=xI6.getOtlpEncoder(B);return{resource:K31.createResource(A.resource),schemaUrl:void 0,scopeMetrics:Pp2(A.scopeMetrics,Q)}}jp2.toResourceMetrics=Tp2;function Pp2(A,B){return Array.from(A.map((Q)=>({scope:K31.createInstrumentationScope(Q.scope),metrics:Q.metrics.map((D)=>Sp2(D,B)),schemaUrl:Q.scope.schemaUrl})))}jp2.toScopeMetrics=Pp2;function Sp2(A,B){let Q={name:A.descriptor.name,description:A.descriptor.description,unit:A.descriptor.unit},D=hI6(A.aggregationTemporality);switch(A.dataPointType){case mo.DataPointType.SUM:Q.sum={aggregationTemporality:D,isMonotonic:A.isMonotonic,dataPoints:Op2(A,B)};break;case mo.DataPointType.GAUGE:Q.gauge={dataPoints:Op2(A,B)};break;case mo.DataPointType.HISTOGRAM:Q.histogram={aggregationTemporality:D,dataPoints:bI6(A,B)};break;case mo.DataPointType.EXPONENTIAL_HISTOGRAM:Q.exponentialHistogram={aggregationTemporality:D,dataPoints:fI6(A,B)};break}return Q}jp2.toMetric=Sp2;function vI6(A,B,Q){let D={attributes:K31.toAttributes(A.attributes),startTimeUnixNano:Q.encodeHrTime(A.startTime),timeUnixNano:Q.encodeHrTime(A.endTime)};switch(B){case Rp2.ValueType.INT:D.asInt=A.value;break;case Rp2.ValueType.DOUBLE:D.asDouble=A.value;break}return D}function Op2(A,B){return A.dataPoints.map((Q)=>{return vI6(Q,A.descriptor.valueType,B)})}function bI6(A,B){return A.dataPoints.map((Q)=>{let D=Q.value;return{attributes:K31.toAttributes(Q.attributes),bucketCounts:D.buckets.counts,explicitBounds:D.buckets.boundaries,count:D.count,sum:D.sum,min:D.min,max:D.max,startTimeUnixNano:B.encodeHrTime(Q.startTime),timeUnixNano:B.encodeHrTime(Q.endTime)}})}function fI6(A,B){return A.dataPoints.map((Q)=>{let D=Q.value;return{attributes:K31.toAttributes(Q.attributes),count:D.count,min:D.min,max:D.max,sum:D.sum,positive:{offset:D.positive.offset,bucketCounts:D.positive.bucketCounts},negative:{offset:D.negative.offset,bucketCounts:D.negative.bucketCounts},scale:D.scale,zeroCount:D.zeroCount,startTimeUnixNano:B.encodeHrTime(Q.startTime),timeUnixNano:B.encodeHrTime(Q.endTime)}})}function hI6(A){switch(A){case mo.AggregationTemporality.DELTA:return 1;case mo.AggregationTemporality.CUMULATIVE:return 2}}function gI6(A,B){return{resourceMetrics:A.map((Q)=>Tp2(Q,B))}}jp2.createExportMetricsServiceRequest=gI6});
var Gc2=E((Dc2)=>{Object.defineProperty(Dc2,"__esModule",{value:!0});Dc2.MeterProviderSharedState=void 0;var nG6=tw(),aG6=bm2(),sG6=Bc2(),rG6=Q31();class Qc2{resource;viewRegistry=new aG6.ViewRegistry;metricCollectors=[];meterSharedStates=new Map;constructor(A){this.resource=A}getMeterSharedState(A){let B=nG6.instrumentationScopeId(A),Q=this.meterSharedStates.get(B);if(Q==null)Q=new sG6.MeterSharedState(this,A),this.meterSharedStates.set(B,Q);return Q}selectAggregations(A){let B=[];for(let Q of this.metricCollectors)B.push([Q,rG6.toAggregation(Q.selectAggregation(A))]);return B}}Dc2.MeterProviderSharedState=Qc2});
var Gh2=E((Dh2)=>{Object.defineProperty(Dh2,"__esModule",{value:!0});Dh2.METRIC_SIGNALR_SERVER_CONNECTION_DURATION=Dh2.METRIC_SIGNALR_SERVER_ACTIVE_CONNECTIONS=Dh2.METRIC_KESTREL_UPGRADED_CONNECTIONS=Dh2.METRIC_KESTREL_TLS_HANDSHAKE_DURATION=Dh2.METRIC_KESTREL_REJECTED_CONNECTIONS=Dh2.METRIC_KESTREL_QUEUED_REQUESTS=Dh2.METRIC_KESTREL_QUEUED_CONNECTIONS=Dh2.METRIC_KESTREL_CONNECTION_DURATION=Dh2.METRIC_KESTREL_ACTIVE_TLS_HANDSHAKES=Dh2.METRIC_KESTREL_ACTIVE_CONNECTIONS=Dh2.METRIC_JVM_THREAD_COUNT=Dh2.METRIC_JVM_MEMORY_USED_AFTER_LAST_GC=Dh2.METRIC_JVM_MEMORY_USED=Dh2.METRIC_JVM_MEMORY_LIMIT=Dh2.METRIC_JVM_MEMORY_COMMITTED=Dh2.METRIC_JVM_GC_DURATION=Dh2.METRIC_JVM_CPU_TIME=Dh2.METRIC_JVM_CPU_RECENT_UTILIZATION=Dh2.METRIC_JVM_CPU_COUNT=Dh2.METRIC_JVM_CLASS_UNLOADED=Dh2.METRIC_JVM_CLASS_LOADED=Dh2.METRIC_JVM_CLASS_COUNT=Dh2.METRIC_HTTP_SERVER_REQUEST_DURATION=Dh2.METRIC_HTTP_CLIENT_REQUEST_DURATION=Dh2.METRIC_DOTNET_TIMER_COUNT=Dh2.METRIC_DOTNET_THREAD_POOL_WORK_ITEM_COUNT=Dh2.METRIC_DOTNET_THREAD_POOL_THREAD_COUNT=Dh2.METRIC_DOTNET_THREAD_POOL_QUEUE_LENGTH=Dh2.METRIC_DOTNET_PROCESS_MEMORY_WORKING_SET=Dh2.METRIC_DOTNET_PROCESS_CPU_TIME=Dh2.METRIC_DOTNET_PROCESS_CPU_COUNT=Dh2.METRIC_DOTNET_MONITOR_LOCK_CONTENTIONS=Dh2.METRIC_DOTNET_JIT_COMPILED_METHODS=Dh2.METRIC_DOTNET_JIT_COMPILED_IL_SIZE=Dh2.METRIC_DOTNET_JIT_COMPILATION_TIME=Dh2.METRIC_DOTNET_GC_PAUSE_TIME=Dh2.METRIC_DOTNET_GC_LAST_COLLECTION_MEMORY_COMMITTED_SIZE=Dh2.METRIC_DOTNET_GC_LAST_COLLECTION_HEAP_SIZE=Dh2.METRIC_DOTNET_GC_LAST_COLLECTION_HEAP_FRAGMENTATION_SIZE=Dh2.METRIC_DOTNET_GC_HEAP_TOTAL_ALLOCATED=Dh2.METRIC_DOTNET_GC_COLLECTIONS=Dh2.METRIC_DOTNET_EXCEPTIONS=Dh2.METRIC_DOTNET_ASSEMBLY_COUNT=Dh2.METRIC_ASPNETCORE_ROUTING_MATCH_ATTEMPTS=Dh2.METRIC_ASPNETCORE_RATE_LIMITING_REQUESTS=Dh2.METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_LEASE_DURATION=Dh2.METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_TIME_IN_QUEUE=Dh2.METRIC_ASPNETCORE_RATE_LIMITING_QUEUED_REQUESTS=Dh2.METRIC_ASPNETCORE_RATE_LIMITING_ACTIVE_REQUEST_LEASES=Dh2.METRIC_ASPNETCORE_DIAGNOSTICS_EXCEPTIONS=void 0;Dh2.METRIC_ASPNETCORE_DIAGNOSTICS_EXCEPTIONS="aspnetcore.diagnostics.exceptions";Dh2.METRIC_ASPNETCORE_RATE_LIMITING_ACTIVE_REQUEST_LEASES="aspnetcore.rate_limiting.active_request_leases";Dh2.METRIC_ASPNETCORE_RATE_LIMITING_QUEUED_REQUESTS="aspnetcore.rate_limiting.queued_requests";Dh2.METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_TIME_IN_QUEUE="aspnetcore.rate_limiting.request.time_in_queue";Dh2.METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_LEASE_DURATION="aspnetcore.rate_limiting.request_lease.duration";Dh2.METRIC_ASPNETCORE_RATE_LIMITING_REQUESTS="aspnetcore.rate_limiting.requests";Dh2.METRIC_ASPNETCORE_ROUTING_MATCH_ATTEMPTS="aspnetcore.routing.match_attempts";Dh2.METRIC_DOTNET_ASSEMBLY_COUNT="dotnet.assembly.count";Dh2.METRIC_DOTNET_EXCEPTIONS="dotnet.exceptions";Dh2.METRIC_DOTNET_GC_COLLECTIONS="dotnet.gc.collections";Dh2.METRIC_DOTNET_GC_HEAP_TOTAL_ALLOCATED="dotnet.gc.heap.total_allocated";Dh2.METRIC_DOTNET_GC_LAST_COLLECTION_HEAP_FRAGMENTATION_SIZE="dotnet.gc.last_collection.heap.fragmentation.size";Dh2.METRIC_DOTNET_GC_LAST_COLLECTION_HEAP_SIZE="dotnet.gc.last_collection.heap.size";Dh2.METRIC_DOTNET_GC_LAST_COLLECTION_MEMORY_COMMITTED_SIZE="dotnet.gc.last_collection.memory.committed_size";Dh2.METRIC_DOTNET_GC_PAUSE_TIME="dotnet.gc.pause.time";Dh2.METRIC_DOTNET_JIT_COMPILATION_TIME="dotnet.jit.compilation.time";Dh2.METRIC_DOTNET_JIT_COMPILED_IL_SIZE="dotnet.jit.compiled_il.size";Dh2.METRIC_DOTNET_JIT_COMPILED_METHODS="dotnet.jit.compiled_methods";Dh2.METRIC_DOTNET_MONITOR_LOCK_CONTENTIONS="dotnet.monitor.lock_contentions";Dh2.METRIC_DOTNET_PROCESS_CPU_COUNT="dotnet.process.cpu.count";Dh2.METRIC_DOTNET_PROCESS_CPU_TIME="dotnet.process.cpu.time";Dh2.METRIC_DOTNET_PROCESS_MEMORY_WORKING_SET="dotnet.process.memory.working_set";Dh2.METRIC_DOTNET_THREAD_POOL_QUEUE_LENGTH="dotnet.thread_pool.queue.length";Dh2.METRIC_DOTNET_THREAD_POOL_THREAD_COUNT="dotnet.thread_pool.thread.count";Dh2.METRIC_DOTNET_THREAD_POOL_WORK_ITEM_COUNT="dotnet.thread_pool.work_item.count";Dh2.METRIC_DOTNET_TIMER_COUNT="dotnet.timer.count";Dh2.METRIC_HTTP_CLIENT_REQUEST_DURATION="http.client.request.duration";Dh2.METRIC_HTTP_SERVER_REQUEST_DURATION="http.server.request.duration";Dh2.METRIC_JVM_CLASS_COUNT="jvm.class.count";Dh2.METRIC_JVM_CLASS_LOADED="jvm.class.loaded";Dh2.METRIC_JVM_CLASS_UNLOADED="jvm.class.unloaded";Dh2.METRIC_JVM_CPU_COUNT="jvm.cpu.count";Dh2.METRIC_JVM_CPU_RECENT_UTILIZATION="jvm.cpu.recent_utilization";Dh2.METRIC_JVM_CPU_TIME="jvm.cpu.time";Dh2.METRIC_JVM_GC_DURATION="jvm.gc.duration";Dh2.METRIC_JVM_MEMORY_COMMITTED="jvm.memory.committed";Dh2.METRIC_JVM_MEMORY_LIMIT="jvm.memory.limit";Dh2.METRIC_JVM_MEMORY_USED="jvm.memory.used";Dh2.METRIC_JVM_MEMORY_USED_AFTER_LAST_GC="jvm.memory.used_after_last_gc";Dh2.METRIC_JVM_THREAD_COUNT="jvm.thread.count";Dh2.METRIC_KESTREL_ACTIVE_CONNECTIONS="kestrel.active_connections";Dh2.METRIC_KESTREL_ACTIVE_TLS_HANDSHAKES="kestrel.active_tls_handshakes";Dh2.METRIC_KESTREL_CONNECTION_DURATION="kestrel.connection.duration";Dh2.METRIC_KESTREL_QUEUED_CONNECTIONS="kestrel.queued_connections";Dh2.METRIC_KESTREL_QUEUED_REQUESTS="kestrel.queued_requests";Dh2.METRIC_KESTREL_REJECTED_CONNECTIONS="kestrel.rejected_connections";Dh2.METRIC_KESTREL_TLS_HANDSHAKE_DURATION="kestrel.tls_handshake.duration";Dh2.METRIC_KESTREL_UPGRADED_CONNECTIONS="kestrel.upgraded_connections";Dh2.METRIC_SIGNALR_SERVER_ACTIVE_CONNECTIONS="signalr.server.active_connections";Dh2.METRIC_SIGNALR_SERVER_CONNECTION_DURATION="signalr.server.connection.duration"});
var Gl2=E((Dl2)=>{Object.defineProperty(Dl2,"__esModule",{value:!0});Dl2.createOtlpNetworkExportDelegate=void 0;var _F6=jY0(),xF6=yY0();function vF6(A,B,Q){return xF6.createOtlpExportDelegate({transport:Q,serializer:B,promiseHandler:_F6.createBoundedQueueExportPromiseHandler(A)},{timeout:A.timeoutMillis})}Dl2.createOtlpNetworkExportDelegate=vF6});
var Gm2=E((Zm2)=>{Object.defineProperty(Zm2,"__esModule",{value:!0});Zm2.getMachineId=void 0;var zZ6=J1("process"),_o;Zm2.getMachineId=_o;switch(zZ6.platform){case"darwin":Zm2.getMachineId=_o=cu2().getMachineId;break;case"linux":Zm2.getMachineId=_o=iu2().getMachineId;break;case"freebsd":Zm2.getMachineId=_o=ru2().getMachineId;break;case"win32":Zm2.getMachineId=_o=Am2().getMachineId;break;default:Zm2.getMachineId=_o=Dm2().getMachineId}});
var HO2=E((KO2)=>{Object.defineProperty(KO2,"__esModule",{value:!0});KO2.ValueType=void 0;var Eo4;(function(A){A[A.INT=0]="INT",A[A.DOUBLE=1]="DOUBLE"})(Eo4=KO2.ValueType||(KO2.ValueType={}))});
var HS2=E((CS2)=>{Object.defineProperty(CS2,"__esModule",{value:!0});CS2.ExponentMapping=void 0;var jo=_I0(),B16=cO1(),XS2=lO1();class VS2{_shift;constructor(A){this._shift=-A}mapToIndex(A){if(A<jo.MIN_VALUE)return this._minNormalLowerBoundaryIndex();let B=jo.getNormalBase2(A),Q=this._rightShift(jo.getSignificand(A)-1,jo.SIGNIFICAND_WIDTH);return B+Q>>this._shift}lowerBoundary(A){let B=this._minNormalLowerBoundaryIndex();if(A<B)throw new XS2.MappingError(`underflow: ${A} is < minimum lower boundary: ${B}`);let Q=this._maxNormalLowerBoundaryIndex();if(A>Q)throw new XS2.MappingError(`overflow: ${A} is > maximum lower boundary: ${Q}`);return B16.ldexp(1,A<<this._shift)}get scale(){if(this._shift===0)return 0;return-this._shift}_minNormalLowerBoundaryIndex(){let A=jo.MIN_NORMAL_EXPONENT>>this._shift;if(this._shift<2)A--;return A}_maxNormalLowerBoundaryIndex(){return jo.MAX_NORMAL_EXPONENT>>this._shift}_rightShift(A,B){return Math.floor(A*Math.pow(2,-B))}}CS2.ExponentMapping=VS2});
var HT1=E((Xc2)=>{Object.defineProperty(Xc2,"__esModule",{value:!0});Xc2.ExactPredicate=Xc2.PatternPredicate=void 0;var tG6=/[\^$\\.+?()[\]{}|]/g;class RY0{_matchAll;_regexp;constructor(A){if(A==="*")this._matchAll=!0,this._regexp=/.*/;else this._matchAll=!1,this._regexp=new RegExp(RY0.escapePattern(A))}match(A){if(this._matchAll)return!0;return this._regexp.test(A)}static escapePattern(A){return`^${A.replace(tG6,"\\$&").replace("*",".*")}$`}static hasWildcard(A){return A.includes("*")}}Xc2.PatternPredicate=RY0;class Jc2{_matchAll;_pattern;constructor(A){this._matchAll=A===void 0,this._pattern=A}match(A){if(this._matchAll)return!0;if(A===this._pattern)return!0;return!1}}Xc2.ExactPredicate=Jc2});
var HY0=E((Fm2)=>{Object.defineProperty(Fm2,"__esModule",{value:!0});Fm2.normalizeType=Fm2.normalizeArch=void 0;var EZ6=(A)=>{switch(A){case"arm":return"arm32";case"ppc":return"ppc32";case"x64":return"amd64";default:return A}};Fm2.normalizeArch=EZ6;var UZ6=(A)=>{switch(A){case"sunos":return"solaris";case"win32":return"windows";default:return A}};Fm2.normalizeType=UZ6});
var Hj2=E((Cj2)=>{Object.defineProperty(Cj2,"__esModule",{value:!0});Cj2._globalThis=void 0;Cj2._globalThis=typeof globalThis==="object"?globalThis:global});
var Hl2=E((Kl2)=>{var wT1=Kl2;wT1.length=function A(B){var Q=B.length;if(!Q)return 0;var D=0;while(--Q%4>1&&B.charAt(Q)==="=")++D;return Math.ceil(B.length*3)/4-D};var uo=new Array(64),Cl2=new Array(123);for(HE=0;HE<64;)Cl2[uo[HE]=HE<26?HE+65:HE<52?HE+71:HE<62?HE-4:HE-59|43]=HE++;var HE;wT1.encode=function A(B,Q,D){var Z=null,G=[],F=0,I=0,Y;while(Q<D){var W=B[Q++];switch(I){case 0:G[F++]=uo[W>>2],Y=(W&3)<<4,I=1;break;case 1:G[F++]=uo[Y|W>>4],Y=(W&15)<<2,I=2;break;case 2:G[F++]=uo[Y|W>>6],G[F++]=uo[W&63],I=0;break}if(F>8191)(Z||(Z=[])).push(String.fromCharCode.apply(String,G)),F=0}if(I){if(G[F++]=uo[Y],G[F++]=61,I===1)G[F++]=61}if(Z){if(F)Z.push(String.fromCharCode.apply(String,G.slice(0,F)));return Z.join("")}return String.fromCharCode.apply(String,G.slice(0,F))};var Vl2="invalid encoding";wT1.decode=function A(B,Q,D){var Z=D,G=0,F;for(var I=0;I<B.length;){var Y=B.charCodeAt(I++);if(Y===61&&G>1)break;if((Y=Cl2[Y])===void 0)throw Error(Vl2);switch(G){case 0:F=Y,G=1;break;case 1:Q[D++]=F<<2|(Y&48)>>4,F=Y,G=2;break;case 2:Q[D++]=(F&15)<<4|(Y&60)>>2,F=Y,G=3;break;case 3:Q[D++]=(F&3)<<6|Y,G=0;break}}if(G===1)throw Error(Vl2);return D-Z};wT1.test=function A(B){return/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(B)}});
var I31=E((Zd2)=>{Object.defineProperty(Zd2,"__esModule",{value:!0});Zd2.AttributeHashMap=Zd2.HashMap=void 0;var FG6=tw();class $Y0{_hash;_valueMap=new Map;_keyMap=new Map;constructor(A){this._hash=A}get(A,B){return B??=this._hash(A),this._valueMap.get(B)}getOrDefault(A,B){let Q=this._hash(A);if(this._valueMap.has(Q))return this._valueMap.get(Q);let D=B();if(!this._keyMap.has(Q))this._keyMap.set(Q,A);return this._valueMap.set(Q,D),D}set(A,B,Q){if(Q??=this._hash(A),!this._keyMap.has(Q))this._keyMap.set(Q,A);this._valueMap.set(Q,B)}has(A,B){return B??=this._hash(A),this._valueMap.has(B)}*keys(){let A=this._keyMap.entries(),B=A.next();while(B.done!==!0)yield[B.value[1],B.value[0]],B=A.next()}*entries(){let A=this._valueMap.entries(),B=A.next();while(B.done!==!0)yield[this._keyMap.get(B.value[0]),B.value[1],B.value[0]],B=A.next()}get size(){return this._valueMap.size}}Zd2.HashMap=$Y0;class Dd2 extends $Y0{constructor(){super(FG6.hashAttributes)}}Zd2.AttributeHashMap=Dd2});
var IW0=E((up2)=>{Object.defineProperty(up2,"__esModule",{value:!0});up2.createExportTraceServiceRequest=up2.toOtlpSpanEvent=up2.toOtlpLink=up2.sdkSpanToOtlpSpan=void 0;var H31=TT1(),aI6=OT1();function fp2(A,B){let Q=A.spanContext(),D=A.status,Z=A.parentSpanContext?.spanId?B.encodeSpanContext(A.parentSpanContext?.spanId):void 0;return{traceId:B.encodeSpanContext(Q.traceId),spanId:B.encodeSpanContext(Q.spanId),parentSpanId:Z,traceState:Q.traceState?.serialize(),name:A.name,kind:A.kind==null?0:A.kind+1,startTimeUnixNano:B.encodeHrTime(A.startTime),endTimeUnixNano:B.encodeHrTime(A.endTime),attributes:H31.toAttributes(A.attributes),droppedAttributesCount:A.droppedAttributesCount,events:A.events.map((G)=>gp2(G,B)),droppedEventsCount:A.droppedEventsCount,status:{code:D.code,message:D.message},links:A.links.map((G)=>hp2(G,B)),droppedLinksCount:A.droppedLinksCount}}up2.sdkSpanToOtlpSpan=fp2;function hp2(A,B){return{attributes:A.attributes?H31.toAttributes(A.attributes):[],spanId:B.encodeSpanContext(A.context.spanId),traceId:B.encodeSpanContext(A.context.traceId),traceState:A.context.traceState?.serialize(),droppedAttributesCount:A.droppedAttributesCount||0}}up2.toOtlpLink=hp2;function gp2(A,B){return{attributes:A.attributes?H31.toAttributes(A.attributes):[],name:A.name,timeUnixNano:B.encodeHrTime(A.time),droppedAttributesCount:A.droppedAttributesCount||0}}up2.toOtlpSpanEvent=gp2;function sI6(A,B){let Q=aI6.getOtlpEncoder(B);return{resourceSpans:oI6(A,Q)}}up2.createExportTraceServiceRequest=sI6;function rI6(A){let B=new Map;for(let Q of A){let D=B.get(Q.resource);if(!D)D=new Map,B.set(Q.resource,D);let Z=`${Q.instrumentationScope.name}@${Q.instrumentationScope.version||""}:${Q.instrumentationScope.schemaUrl||""}`,G=D.get(Z);if(!G)G=[],D.set(Z,G);G.push(Q)}return B}function oI6(A,B){let Q=rI6(A),D=[],Z=Q.entries(),G=Z.next();while(!G.done){let[F,I]=G.value,Y=[],W=I.values(),J=W.next();while(!J.done){let V=J.value;if(V.length>0){let C=V.map((K)=>fp2(K,B));Y.push({scope:H31.createInstrumentationScope(V[0].instrumentationScope),spans:C,schemaUrl:V[0].instrumentationScope.schemaUrl})}J=W.next()}let X={resource:H31.createResource(F),scopeSpans:Y,schemaUrl:void 0};D.push(X),G=Z.next()}return D}});
var Ii2=E((Gi2)=>{Object.defineProperty(Gi2,"__esModule",{value:!0});Gi2.VERSION=void 0;Gi2.VERSION="0.200.0"});
var Ip2=E((Gp2)=>{Object.defineProperty(Gp2,"__esModule",{value:!0});Gp2.hexToBinary=void 0;function Zp2(A){if(A>=48&&A<=57)return A-48;if(A>=97&&A<=102)return A-87;return A-55}function II6(A){let B=new Uint8Array(A.length/2),Q=0;for(let D=0;D<A.length;D+=2){let Z=Zp2(A.charCodeAt(D)),G=Zp2(A.charCodeAt(D+1));B[Q++]=Z<<4|G}return B}Gp2.hexToBinary=II6});
var J31=E((cc2)=>{Object.defineProperty(cc2,"__esModule",{value:!0});cc2.getSharedConfigurationDefaults=cc2.mergeOtlpSharedConfigurationWithDefaults=cc2.wrapStaticHeadersInFunction=cc2.validateTimeoutMillis=void 0;function dc2(A){if(Number.isFinite(A)&&A>0)return A;throw new Error(`Configuration: timeoutMillis is invalid, expected number greater than 0 (actual: '${A}')`)}cc2.validateTimeoutMillis=dc2;function wF6(A){if(A==null)return;return()=>A}cc2.wrapStaticHeadersInFunction=wF6;function $F6(A,B,Q){return{timeoutMillis:dc2(A.timeoutMillis??B.timeoutMillis??Q.timeoutMillis),concurrencyLimit:A.concurrencyLimit??B.concurrencyLimit??Q.concurrencyLimit,compression:A.compression??B.compression??Q.compression}}cc2.mergeOtlpSharedConfigurationWithDefaults=$F6;function qF6(){return{timeoutMillis:1e4,concurrencyLimit:30,compression:"none"}}cc2.getSharedConfigurationDefaults=qF6});
var Jg2=E((Yg2)=>{Object.defineProperty(Yg2,"__esModule",{value:!0});Yg2.isUrlIgnored=Yg2.urlMatches=void 0;function Ig2(A,B){if(typeof B==="string")return A===B;else return!!A.match(B)}Yg2.urlMatches=Ig2;function n76(A,B){if(!B)return!1;for(let Q of B)if(Ig2(A,Q))return!0;return!1}Yg2.isUrlIgnored=n76});
var Ji2=E((Yi2)=>{Object.defineProperty(Yi2,"__esModule",{value:!0});Yi2.parseRetryAfterToMills=Yi2.isExportRetryable=void 0;function NY6(A){return[429,502,503,504].includes(A)}Yi2.isExportRetryable=NY6;function LY6(A){if(A==null)return;let B=Number.parseInt(A,10);if(Number.isInteger(B))return B>0?B*1000:-1;let Q=new Date(A).getTime()-Date.now();if(Q>=0)return Q;return 0}Yi2.parseRetryAfterToMills=LY6});
var Jn2=E((zW0)=>{Object.defineProperty(zW0,"__esModule",{value:!0});zW0.OTLPMetricExporter=void 0;var _W6=Wn2();Object.defineProperty(zW0,"OTLPMetricExporter",{enumerable:!0,get:function(){return _W6.OTLPMetricExporter}})});
var KT1=E((rd2)=>{Object.defineProperty(rd2,"__esModule",{value:!0});rd2.createDenyListAttributesProcessor=rd2.createAllowListAttributesProcessor=rd2.createMultiAttributesProcessor=rd2.createNoopAttributesProcessor=void 0;class id2{process(A,B){return A}}class nd2{_processors;constructor(A){this._processors=A}process(A,B){let Q=A;for(let D of this._processors)Q=D.process(Q,B);return Q}}class ad2{_allowedAttributeNames;constructor(A){this._allowedAttributeNames=A}process(A,B){let Q={};return Object.keys(A).filter((D)=>this._allowedAttributeNames.includes(D)).forEach((D)=>Q[D]=A[D]),Q}}class sd2{_deniedAttributeNames;constructor(A){this._deniedAttributeNames=A}process(A,B){let Q={};return Object.keys(A).filter((D)=>!this._deniedAttributeNames.includes(D)).forEach((D)=>Q[D]=A[D]),Q}}function jG6(){return xG6}rd2.createNoopAttributesProcessor=jG6;function yG6(A){return new nd2(A)}rd2.createMultiAttributesProcessor=yG6;function kG6(A){return new ad2(A)}rd2.createAllowListAttributesProcessor=kG6;function _G6(A){return new sd2(A)}rd2.createDenyListAttributesProcessor=_G6;var xG6=new id2});
var Kd2=E((Vd2)=>{Object.defineProperty(Vd2,"__esModule",{value:!0});Vd2.AsyncMetricStorage=void 0;var VG6=wY0(),CG6=NY0(),KG6=LY0(),HG6=I31();class Xd2 extends VG6.MetricStorage{_attributesProcessor;_aggregationCardinalityLimit;_deltaMetricStorage;_temporalMetricStorage;constructor(A,B,Q,D,Z){super(A);this._attributesProcessor=Q,this._aggregationCardinalityLimit=Z,this._deltaMetricStorage=new CG6.DeltaMetricProcessor(B,this._aggregationCardinalityLimit),this._temporalMetricStorage=new KG6.TemporalMetricProcessor(B,D)}record(A,B){let Q=new HG6.AttributeHashMap;Array.from(A.entries()).forEach(([D,Z])=>{Q.set(this._attributesProcessor.process(D),Z)}),this._deltaMetricStorage.batchCumulate(Q,B)}collect(A,B){let Q=this._deltaMetricStorage.collect();return this._temporalMetricStorage.buildMetrics(A,this._instrumentDescriptor,Q,B)}}Vd2.AsyncMetricStorage=Xd2});
var Kg2=E((Vg2)=>{Object.defineProperty(Vg2,"__esModule",{value:!0});Vg2.Deferred=void 0;class Xg2{_promise;_resolve;_reject;constructor(){this._promise=new Promise((A,B)=>{this._resolve=A,this._reject=B})}get promise(){return this._promise}resolve(A){this._resolve(A)}reject(A){this._reject(A)}}Vg2.Deferred=Xg2});
var LR2=E((Hu)=>{var _r4=Hu&&Hu.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;Object.defineProperty(A,D,{enumerable:!0,get:function(){return B[Q]}})}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),xr4=Hu&&Hu.__exportStar||function(A,B){for(var Q in A)if(Q!=="default"&&!Object.prototype.hasOwnProperty.call(B,Q))_r4(B,A,Q)};Object.defineProperty(Hu,"__esModule",{value:!0});xr4(NR2(),Hu)});
var LY0=E((Wd2)=>{Object.defineProperty(Wd2,"__esModule",{value:!0});Wd2.TemporalMetricProcessor=void 0;var WG6=mO1(),JG6=I31();class Y31{_aggregator;_unreportedAccumulations=new Map;_reportHistory=new Map;constructor(A,B){this._aggregator=A,B.forEach((Q)=>{this._unreportedAccumulations.set(Q,[])})}buildMetrics(A,B,Q,D){this._stashAccumulations(Q);let Z=this._getMergedUnreportedAccumulations(A),G=Z,F;if(this._reportHistory.has(A)){let Y=this._reportHistory.get(A),W=Y.collectionTime;if(F=Y.aggregationTemporality,F===WG6.AggregationTemporality.CUMULATIVE)G=Y31.merge(Y.accumulations,Z,this._aggregator);else G=Y31.calibrateStartTime(Y.accumulations,Z,W)}else F=A.selectAggregationTemporality(B.type);this._reportHistory.set(A,{accumulations:G,collectionTime:D,aggregationTemporality:F});let I=XG6(G);if(I.length===0)return;return this._aggregator.toMetricData(B,F,I,D)}_stashAccumulations(A){let B=this._unreportedAccumulations.keys();for(let Q of B){let D=this._unreportedAccumulations.get(Q);if(D===void 0)D=[],this._unreportedAccumulations.set(Q,D);D.push(A)}}_getMergedUnreportedAccumulations(A){let B=new JG6.AttributeHashMap,Q=this._unreportedAccumulations.get(A);if(this._unreportedAccumulations.set(A,[]),Q===void 0)return B;for(let D of Q)B=Y31.merge(B,D,this._aggregator);return B}static merge(A,B,Q){let D=A,Z=B.entries(),G=Z.next();while(G.done!==!0){let[F,I,Y]=G.value;if(A.has(F,Y)){let W=A.get(F,Y),J=Q.merge(W,I);D.set(F,J,Y)}else D.set(F,I,Y);G=Z.next()}return D}static calibrateStartTime(A,B,Q){for(let[D,Z]of A.keys())B.get(D,Z)?.setStartTime(Q);return B}}Wd2.TemporalMetricProcessor=Y31;function XG6(A){return Array.from(A.entries())}});
var Lp2=E((qp2)=>{Object.defineProperty(qp2,"__esModule",{value:!0});qp2.ProtobufLogsSerializer=void 0;var $p2=RT1(),SI6=DW0(),jI6=$p2.opentelemetry.proto.collector.logs.v1.ExportLogsServiceResponse,yI6=$p2.opentelemetry.proto.collector.logs.v1.ExportLogsServiceRequest;qp2.ProtobufLogsSerializer={serializeRequest:(A)=>{let B=SI6.createExportLogsServiceRequest(A);return yI6.encode(B).finish()},deserializeResponse:(A)=>{return jI6.decode(A)}}});
var MT1=E((Th5,pl2)=>{pl2.exports=WG;var A$=eL(),nY0,cl2=A$.LongBits,GI6=A$.utf8;function B$(A,B){return RangeError("index out of range: "+A.pos+" + "+(B||1)+" > "+A.len)}function WG(A){this.buf=A,this.pos=0,this.len=A.length}var ml2=typeof Uint8Array!=="undefined"?function A(B){if(B instanceof Uint8Array||Array.isArray(B))return new WG(B);throw Error("illegal buffer")}:function A(B){if(Array.isArray(B))return new WG(B);throw Error("illegal buffer")},ll2=function A(){return A$.Buffer?function B(Q){return(WG.create=function D(Z){return A$.Buffer.isBuffer(Z)?new nY0(Z):ml2(Z)})(Q)}:ml2};WG.create=ll2();WG.prototype._slice=A$.Array.prototype.subarray||A$.Array.prototype.slice;WG.prototype.uint32=function A(){var B=**********;return function Q(){if(B=(this.buf[this.pos]&127)>>>0,this.buf[this.pos++]<128)return B;if(B=(B|(this.buf[this.pos]&127)<<7)>>>0,this.buf[this.pos++]<128)return B;if(B=(B|(this.buf[this.pos]&127)<<14)>>>0,this.buf[this.pos++]<128)return B;if(B=(B|(this.buf[this.pos]&127)<<21)>>>0,this.buf[this.pos++]<128)return B;if(B=(B|(this.buf[this.pos]&15)<<28)>>>0,this.buf[this.pos++]<128)return B;if((this.pos+=5)>this.len)throw this.pos=this.len,B$(this,10);return B}}();WG.prototype.int32=function A(){return this.uint32()|0};WG.prototype.sint32=function A(){var B=this.uint32();return B>>>1^-(B&1)|0};function iY0(){var A=new cl2(0,0),B=0;if(this.len-this.pos>4){for(;B<4;++B)if(A.lo=(A.lo|(this.buf[this.pos]&127)<<B*7)>>>0,this.buf[this.pos++]<128)return A;if(A.lo=(A.lo|(this.buf[this.pos]&127)<<28)>>>0,A.hi=(A.hi|(this.buf[this.pos]&127)>>4)>>>0,this.buf[this.pos++]<128)return A;B=0}else{for(;B<3;++B){if(this.pos>=this.len)throw B$(this);if(A.lo=(A.lo|(this.buf[this.pos]&127)<<B*7)>>>0,this.buf[this.pos++]<128)return A}return A.lo=(A.lo|(this.buf[this.pos++]&127)<<B*7)>>>0,A}if(this.len-this.pos>4){for(;B<5;++B)if(A.hi=(A.hi|(this.buf[this.pos]&127)<<B*7+3)>>>0,this.buf[this.pos++]<128)return A}else for(;B<5;++B){if(this.pos>=this.len)throw B$(this);if(A.hi=(A.hi|(this.buf[this.pos]&127)<<B*7+3)>>>0,this.buf[this.pos++]<128)return A}throw Error("invalid varint encoding")}WG.prototype.bool=function A(){return this.uint32()!==0};function LT1(A,B){return(A[B-4]|A[B-3]<<8|A[B-2]<<16|A[B-1]<<24)>>>0}WG.prototype.fixed32=function A(){if(this.pos+4>this.len)throw B$(this,4);return LT1(this.buf,this.pos+=4)};WG.prototype.sfixed32=function A(){if(this.pos+4>this.len)throw B$(this,4);return LT1(this.buf,this.pos+=4)|0};function dl2(){if(this.pos+8>this.len)throw B$(this,8);return new cl2(LT1(this.buf,this.pos+=4),LT1(this.buf,this.pos+=4))}WG.prototype.float=function A(){if(this.pos+4>this.len)throw B$(this,4);var B=A$.float.readFloatLE(this.buf,this.pos);return this.pos+=4,B};WG.prototype.double=function A(){if(this.pos+8>this.len)throw B$(this,4);var B=A$.float.readDoubleLE(this.buf,this.pos);return this.pos+=8,B};WG.prototype.bytes=function A(){var B=this.uint32(),Q=this.pos,D=this.pos+B;if(D>this.len)throw B$(this,B);if(this.pos+=B,Array.isArray(this.buf))return this.buf.slice(Q,D);if(Q===D){var Z=A$.Buffer;return Z?Z.alloc(0):new this.buf.constructor(0)}return this._slice.call(this.buf,Q,D)};WG.prototype.string=function A(){var B=this.bytes();return GI6.read(B,0,B.length)};WG.prototype.skip=function A(B){if(typeof B==="number"){if(this.pos+B>this.len)throw B$(this,B);this.pos+=B}else do if(this.pos>=this.len)throw B$(this);while(this.buf[this.pos++]&128);return this};WG.prototype.skipType=function(A){switch(A){case 0:this.skip();break;case 1:this.skip(8);break;case 2:this.skip(this.uint32());break;case 3:while((A=this.uint32()&7)!==4)this.skipType(A);break;case 5:this.skip(4);break;default:throw Error("invalid wire type "+A+" at offset "+this.pos)}return this};WG._configure=function(A){nY0=A,WG.create=ll2(),nY0._configure();var B=A$.Long?"toLong":"toNumber";A$.merge(WG.prototype,{int64:function Q(){return iY0.call(this)[B](!1)},uint64:function Q(){return iY0.call(this)[B](!0)},sint64:function Q(){return iY0.call(this).zzDecode()[B](!1)},fixed64:function Q(){return dl2.call(this)[B](!0)},sfixed64:function Q(){return dl2.call(this)[B](!1)}})}});
var Ml2=E(($h5,Ll2)=>{Ll2.exports=Ul2(Ul2);function Ul2(A){if(typeof Float32Array!=="undefined")(function(){var B=new Float32Array([-0]),Q=new Uint8Array(B.buffer),D=Q[3]===128;function Z(Y,W,J){B[0]=Y,W[J]=Q[0],W[J+1]=Q[1],W[J+2]=Q[2],W[J+3]=Q[3]}function G(Y,W,J){B[0]=Y,W[J]=Q[3],W[J+1]=Q[2],W[J+2]=Q[1],W[J+3]=Q[0]}A.writeFloatLE=D?Z:G,A.writeFloatBE=D?G:Z;function F(Y,W){return Q[0]=Y[W],Q[1]=Y[W+1],Q[2]=Y[W+2],Q[3]=Y[W+3],B[0]}function I(Y,W){return Q[3]=Y[W],Q[2]=Y[W+1],Q[1]=Y[W+2],Q[0]=Y[W+3],B[0]}A.readFloatLE=D?F:I,A.readFloatBE=D?I:F})();else(function(){function B(D,Z,G,F){var I=Z<0?1:0;if(I)Z=-Z;if(Z===0)D(1/Z>0?0:2147483648,G,F);else if(isNaN(Z))D(2143289344,G,F);else if(Z>340282346638528860000000000000000000000)D((I<<31|2139095040)>>>0,G,F);else if(Z<0.000000000000000000000000000000000000011754943508222875)D((I<<31|Math.round(Z/0.000000000000000000000000000000000000000000001401298464324817))>>>0,G,F);else{var Y=Math.floor(Math.log(Z)/Math.LN2),W=Math.round(Z*Math.pow(2,-Y)*8388608)&8388607;D((I<<31|Y+127<<23|W)>>>0,G,F)}}A.writeFloatLE=B.bind(null,wl2),A.writeFloatBE=B.bind(null,$l2);function Q(D,Z,G){var F=D(Z,G),I=(F>>31)*2+1,Y=F>>>23&255,W=F&8388607;return Y===255?W?NaN:I*(1/0):Y===0?I*0.000000000000000000000000000000000000000000001401298464324817*W:I*Math.pow(2,Y-150)*(W+8388608)}A.readFloatLE=Q.bind(null,ql2),A.readFloatBE=Q.bind(null,Nl2)})();if(typeof Float64Array!=="undefined")(function(){var B=new Float64Array([-0]),Q=new Uint8Array(B.buffer),D=Q[7]===128;function Z(Y,W,J){B[0]=Y,W[J]=Q[0],W[J+1]=Q[1],W[J+2]=Q[2],W[J+3]=Q[3],W[J+4]=Q[4],W[J+5]=Q[5],W[J+6]=Q[6],W[J+7]=Q[7]}function G(Y,W,J){B[0]=Y,W[J]=Q[7],W[J+1]=Q[6],W[J+2]=Q[5],W[J+3]=Q[4],W[J+4]=Q[3],W[J+5]=Q[2],W[J+6]=Q[1],W[J+7]=Q[0]}A.writeDoubleLE=D?Z:G,A.writeDoubleBE=D?G:Z;function F(Y,W){return Q[0]=Y[W],Q[1]=Y[W+1],Q[2]=Y[W+2],Q[3]=Y[W+3],Q[4]=Y[W+4],Q[5]=Y[W+5],Q[6]=Y[W+6],Q[7]=Y[W+7],B[0]}function I(Y,W){return Q[7]=Y[W],Q[6]=Y[W+1],Q[5]=Y[W+2],Q[4]=Y[W+3],Q[3]=Y[W+4],Q[2]=Y[W+5],Q[1]=Y[W+6],Q[0]=Y[W+7],B[0]}A.readDoubleLE=D?F:I,A.readDoubleBE=D?I:F})();else(function(){function B(D,Z,G,F,I,Y){var W=F<0?1:0;if(W)F=-F;if(F===0)D(0,I,Y+Z),D(1/F>0?0:2147483648,I,Y+G);else if(isNaN(F))D(0,I,Y+Z),D(2146959360,I,Y+G);else if(F>179769313486231570000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000)D(0,I,Y+Z),D((W<<31|2146435072)>>>0,I,Y+G);else{var J;if(F<0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000022250738585072014)J=F/0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000005,D(J>>>0,I,Y+Z),D((W<<31|J/**********)>>>0,I,Y+G);else{var X=Math.floor(Math.log(F)/Math.LN2);if(X===1024)X=1023;J=F*Math.pow(2,-X),D(J*4503599627370496>>>0,I,Y+Z),D((W<<31|X+1023<<20|J*1048576&1048575)>>>0,I,Y+G)}}}A.writeDoubleLE=B.bind(null,wl2,0,4),A.writeDoubleBE=B.bind(null,$l2,4,0);function Q(D,Z,G,F,I){var Y=D(F,I+Z),W=D(F,I+G),J=(W>>31)*2+1,X=W>>>20&2047,V=***********(W&1048575)+Y;return X===2047?V?NaN:J*(1/0):X===0?J*0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000005*V:J*Math.pow(2,X-1075)*(V+4503599627370496)}A.readDoubleLE=Q.bind(null,ql2,0,4),A.readDoubleBE=Q.bind(null,Nl2,4,0)})();return A}function wl2(A,B,Q){B[Q]=A&255,B[Q+1]=A>>>8&255,B[Q+2]=A>>>16&255,B[Q+3]=A>>>24}function $l2(A,B,Q){B[Q]=A>>>24,B[Q+1]=A>>>16&255,B[Q+2]=A>>>8&255,B[Q+3]=A&255}function ql2(A,B){return(A[B]|A[B+1]<<8|A[B+2]<<16|A[B+3]<<24)>>>0}function Nl2(A,B){return(A[B]<<24|A[B+1]<<16|A[B+2]<<8|A[B+3])>>>0}});
var Mp2=E((ZW0)=>{Object.defineProperty(ZW0,"__esModule",{value:!0});ZW0.ProtobufLogsSerializer=void 0;var kI6=Lp2();Object.defineProperty(ZW0,"ProtobufLogsSerializer",{enumerable:!0,get:function(){return kI6.ProtobufLogsSerializer}})});
var Mu2=E((Nu2)=>{Object.defineProperty(Nu2,"__esModule",{value:!0});Nu2.defaultServiceName=void 0;function vD6(){return`unknown_service:${process.argv0}`}Nu2.defaultServiceName=vD6});
var NR2=E((Ku)=>{var yr4=Ku&&Ku.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;Object.defineProperty(A,D,{enumerable:!0,get:function(){return B[Q]}})}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),kr4=Ku&&Ku.__exportStar||function(A,B){for(var Q in A)if(Q!=="default"&&!Object.prototype.hasOwnProperty.call(B,Q))yr4(B,A,Q)};Object.defineProperty(Ku,"__esModule",{value:!0});kr4(qR2(),Ku)});
var NT1=E((Rh5,fl2)=>{fl2.exports=N8;var zE=eL(),uY0,qT1=zE.LongBits,xl2=zE.base64,vl2=zE.utf8;function V31(A,B,Q){this.fn=A,this.len=B,this.next=void 0,this.val=Q}function dY0(){}function BI6(A){this.head=A.head,this.tail=A.tail,this.len=A.len,this.next=A.states}function N8(){this.len=0,this.head=new V31(dY0,0,0),this.tail=this.head,this.states=null}var bl2=function A(){return zE.Buffer?function B(){return(N8.create=function Q(){return new uY0})()}:function B(){return new N8}};N8.create=bl2();N8.alloc=function A(B){return new zE.Array(B)};if(zE.Array!==Array)N8.alloc=zE.pool(N8.alloc,zE.Array.prototype.subarray);N8.prototype._push=function A(B,Q,D){return this.tail=this.tail.next=new V31(B,Q,D),this.len+=Q,this};function cY0(A,B,Q){B[Q]=A&255}function QI6(A,B,Q){while(A>127)B[Q++]=A&127|128,A>>>=7;B[Q]=A}function lY0(A,B){this.len=A,this.next=void 0,this.val=B}lY0.prototype=Object.create(V31.prototype);lY0.prototype.fn=QI6;N8.prototype.uint32=function A(B){return this.len+=(this.tail=this.tail.next=new lY0((B=B>>>0)<128?1:B<16384?2:B<2097152?3:B<268435456?4:5,B)).len,this};N8.prototype.int32=function A(B){return B<0?this._push(pY0,10,qT1.fromNumber(B)):this.uint32(B)};N8.prototype.sint32=function A(B){return this.uint32((B<<1^B>>31)>>>0)};function pY0(A,B,Q){while(A.hi)B[Q++]=A.lo&127|128,A.lo=(A.lo>>>7|A.hi<<25)>>>0,A.hi>>>=7;while(A.lo>127)B[Q++]=A.lo&127|128,A.lo=A.lo>>>7;B[Q++]=A.lo}N8.prototype.uint64=function A(B){var Q=qT1.from(B);return this._push(pY0,Q.length(),Q)};N8.prototype.int64=N8.prototype.uint64;N8.prototype.sint64=function A(B){var Q=qT1.from(B).zzEncode();return this._push(pY0,Q.length(),Q)};N8.prototype.bool=function A(B){return this._push(cY0,1,B?1:0)};function mY0(A,B,Q){B[Q]=A&255,B[Q+1]=A>>>8&255,B[Q+2]=A>>>16&255,B[Q+3]=A>>>24}N8.prototype.fixed32=function A(B){return this._push(mY0,4,B>>>0)};N8.prototype.sfixed32=N8.prototype.fixed32;N8.prototype.fixed64=function A(B){var Q=qT1.from(B);return this._push(mY0,4,Q.lo)._push(mY0,4,Q.hi)};N8.prototype.sfixed64=N8.prototype.fixed64;N8.prototype.float=function A(B){return this._push(zE.float.writeFloatLE,4,B)};N8.prototype.double=function A(B){return this._push(zE.float.writeDoubleLE,8,B)};var DI6=zE.Array.prototype.set?function A(B,Q,D){Q.set(B,D)}:function A(B,Q,D){for(var Z=0;Z<B.length;++Z)Q[D+Z]=B[Z]};N8.prototype.bytes=function A(B){var Q=B.length>>>0;if(!Q)return this._push(cY0,1,0);if(zE.isString(B)){var D=N8.alloc(Q=xl2.length(B));xl2.decode(B,D,0),B=D}return this.uint32(Q)._push(DI6,Q,B)};N8.prototype.string=function A(B){var Q=vl2.length(B);return Q?this.uint32(Q)._push(vl2.write,Q,B):this._push(cY0,1,0)};N8.prototype.fork=function A(){return this.states=new BI6(this),this.head=this.tail=new V31(dY0,0,0),this.len=0,this};N8.prototype.reset=function A(){if(this.states)this.head=this.states.head,this.tail=this.states.tail,this.len=this.states.len,this.states=this.states.next;else this.head=this.tail=new V31(dY0,0,0),this.len=0;return this};N8.prototype.ldelim=function A(){var B=this.head,Q=this.tail,D=this.len;if(this.reset().uint32(D),D)this.tail.next=B.next,this.tail=Q,this.len+=D;return this};N8.prototype.finish=function A(){var B=this.head.next,Q=this.constructor.alloc(this.len),D=0;while(B)B.fn(B.val,Q,D),D+=B.len,B=B.next;return Q};N8._configure=function(A){uY0=A,N8.create=bl2(),uY0._configure()}});
var NT2=E(($T2)=>{Object.defineProperty($T2,"__esModule",{value:!0});$T2.context=void 0;var Kt4=p51();$T2.context=Kt4.ContextAPI.getInstance()});
var NY0=E((Id2)=>{Object.defineProperty(Id2,"__esModule",{value:!0});Id2.DeltaMetricProcessor=void 0;var YG6=tw(),qY0=I31();class Fd2{_aggregator;_activeCollectionStorage=new qY0.AttributeHashMap;_cumulativeMemoStorage=new qY0.AttributeHashMap;_cardinalityLimit;_overflowAttributes={"otel.metric.overflow":!0};_overflowHashCode;constructor(A,B){this._aggregator=A,this._cardinalityLimit=(B??2000)-1,this._overflowHashCode=YG6.hashAttributes(this._overflowAttributes)}record(A,B,Q,D){let Z=this._activeCollectionStorage.get(B);if(!Z){if(this._activeCollectionStorage.size>=this._cardinalityLimit){this._activeCollectionStorage.getOrDefault(this._overflowAttributes,()=>this._aggregator.createAccumulation(D))?.record(A);return}Z=this._aggregator.createAccumulation(D),this._activeCollectionStorage.set(B,Z)}Z?.record(A)}batchCumulate(A,B){Array.from(A.entries()).forEach(([Q,D,Z])=>{let G=this._aggregator.createAccumulation(B);G?.record(D);let F=G;if(this._cumulativeMemoStorage.has(Q,Z)){let I=this._cumulativeMemoStorage.get(Q,Z);F=this._aggregator.diff(I,G)}else if(this._cumulativeMemoStorage.size>=this._cardinalityLimit){if(Q=this._overflowAttributes,Z=this._overflowHashCode,this._cumulativeMemoStorage.has(Q,Z)){let I=this._cumulativeMemoStorage.get(Q,Z);F=this._aggregator.diff(I,G)}}if(this._activeCollectionStorage.has(Q,Z)){let I=this._activeCollectionStorage.get(Q,Z);F=this._aggregator.merge(I,F)}this._cumulativeMemoStorage.set(Q,G,Z),this._activeCollectionStorage.set(Q,F,Z)})}collect(){let A=this._activeCollectionStorage;return this._activeCollectionStorage=new qY0.AttributeHashMap,A}}Id2.DeltaMetricProcessor=Fd2});
var Ng2=E(($g2)=>{Object.defineProperty($g2,"__esModule",{value:!0});$g2.diagLogLevelFromString=void 0;var KP=VQ(),wg2={ALL:KP.DiagLogLevel.ALL,VERBOSE:KP.DiagLogLevel.VERBOSE,DEBUG:KP.DiagLogLevel.DEBUG,INFO:KP.DiagLogLevel.INFO,WARN:KP.DiagLogLevel.WARN,ERROR:KP.DiagLogLevel.ERROR,NONE:KP.DiagLogLevel.NONE};function r76(A){if(A==null)return;let B=wg2[A.toUpperCase()];if(B==null)return KP.diag.warn(`Unknown log level "${A}", expected one of ${Object.keys(wg2)}, using default`),KP.DiagLogLevel.INFO;return B}$g2.diagLogLevelFromString=r76});
var Nm2=E(($m2)=>{Object.defineProperty($m2,"__esModule",{value:!0});$m2.processDetector=void 0;var LZ6=VQ(),HP=CP(),MZ6=J1("os");class wm2{detect(A){let B={[HP.SEMRESATTRS_PROCESS_PID]:process.pid,[HP.SEMRESATTRS_PROCESS_EXECUTABLE_NAME]:process.title,[HP.SEMRESATTRS_PROCESS_EXECUTABLE_PATH]:process.execPath,[HP.SEMRESATTRS_PROCESS_COMMAND_ARGS]:[process.argv[0],...process.execArgv,...process.argv.slice(1)],[HP.SEMRESATTRS_PROCESS_RUNTIME_VERSION]:process.versions.node,[HP.SEMRESATTRS_PROCESS_RUNTIME_NAME]:"nodejs",[HP.SEMRESATTRS_PROCESS_RUNTIME_DESCRIPTION]:"Node.js"};if(process.argv.length>1)B[HP.SEMRESATTRS_PROCESS_COMMAND]=process.argv[1];try{let Q=MZ6.userInfo();B[HP.SEMRESATTRS_PROCESS_OWNER]=Q.username}catch(Q){LZ6.diag.debug(`error obtaining process owner: ${Q}`)}return{attributes:B}}}$m2.processDetector=new wm2});
var OS2=E((MS2)=>{Object.defineProperty(MS2,"__esModule",{value:!0});MS2.getMapping=void 0;var Q16=HS2(),D16=qS2(),Z16=lO1(),NS2=-10,LS2=20,G16=Array.from({length:31},(A,B)=>{if(B>10)return new D16.LogarithmMapping(B-10);return new Q16.ExponentMapping(B-10)});function F16(A){if(A>LS2||A<NS2)throw new Z16.MappingError(`expected scale >= ${NS2} && <= ${LS2}, got: ${A}`);return G16[A+10]}MS2.getMapping=F16});
var OT1=E((Vp2)=>{Object.defineProperty(Vp2,"__esModule",{value:!0});Vp2.getOtlpEncoder=Vp2.encodeAsString=Vp2.encodeAsLongBits=Vp2.toLongBits=Vp2.hrTimeToNanos=void 0;var YI6=x3(),tY0=Ip2();function eY0(A){let B=BigInt(1e9);return BigInt(A[0])*B+BigInt(A[1])}Vp2.hrTimeToNanos=eY0;function Wp2(A){let B=Number(BigInt.asUintN(32,A)),Q=Number(BigInt.asUintN(32,A>>BigInt(32)));return{low:B,high:Q}}Vp2.toLongBits=Wp2;function AW0(A){let B=eY0(A);return Wp2(B)}Vp2.encodeAsLongBits=AW0;function Jp2(A){return eY0(A).toString()}Vp2.encodeAsString=Jp2;var WI6=typeof BigInt!=="undefined"?Jp2:YI6.hrTimeToNanoseconds;function Yp2(A){return A}function Xp2(A){if(A===void 0)return;return tY0.hexToBinary(A)}var JI6={encodeHrTime:AW0,encodeSpanContext:tY0.hexToBinary,encodeOptionalSpanContext:Xp2};function XI6(A){if(A===void 0)return JI6;let B=A.useLongBits??!0,Q=A.useHex??!1;return{encodeHrTime:B?AW0:WI6,encodeSpanContext:Q?Yp2:tY0.hexToBinary,encodeOptionalSpanContext:Q?Yp2:Xp2}}Vp2.getOtlpEncoder=XI6});
var Og2=E((Mg2)=>{Object.defineProperty(Mg2,"__esModule",{value:!0});Mg2._export=void 0;var Lg2=VQ(),o76=s51();function t76(A,B){return new Promise((Q)=>{Lg2.context.with(o76.suppressTracing(Lg2.context.active()),()=>{A.export(B,(D)=>{Q(D)})})})}Mg2._export=t76});
var Oi2=E((Mi2)=>{Object.defineProperty(Mi2,"__esModule",{value:!0});Mi2.createRetryingTransport=void 0;var xY6=5,vY6=1000,bY6=5000,fY6=1.5,Ni2=0.2;function hY6(){return Math.random()*(2*Ni2)-Ni2}class Li2{_transport;constructor(A){this._transport=A}retry(A,B,Q){return new Promise((D,Z)=>{setTimeout(()=>{this._transport.send(A,B).then(D,Z)},Q)})}async send(A,B){let Q=Date.now()+B,D=await this._transport.send(A,B),Z=xY6,G=vY6;while(D.status==="retryable"&&Z>0){Z--;let F=Math.max(Math.min(G,bY6)+hY6(),0);G=G*fY6;let I=D.retryInMillis??F,Y=Q-Date.now();if(I>Y)return D;D=await this.retry(A,Y,I)}return D}shutdown(){return this._transport.shutdown()}}function gY6(A){return new Li2(A.transport)}Mi2.createRetryingTransport=gY6});
var Om2=E((Mm2)=>{Object.defineProperty(Mm2,"__esModule",{value:!0});Mm2.serviceInstanceIdDetector=void 0;var RZ6=CP(),OZ6=J1("crypto");class Lm2{detect(A){return{attributes:{[RZ6.SEMRESATTRS_SERVICE_INSTANCE_ID]:OZ6.randomUUID()}}}}Mm2.serviceInstanceIdDetector=new Lm2});
var PF0=E((rR2)=>{Object.defineProperty(rR2,"__esModule",{value:!0});rR2.baggageEntryMetadataFromString=rR2.createBaggage=void 0;var or4=Eu(),tr4=iR2(),er4=sR2(),Ao4=or4.DiagAPI.instance();function Bo4(A={}){return new tr4.BaggageImpl(new Map(Object.entries(A)))}rR2.createBaggage=Bo4;function Qo4(A){if(typeof A!=="string")Ao4.error(`Cannot create baggage metadata from unknown type: ${typeof A}`),A="";return{__TYPE__:er4.baggageEntryMetadataSymbol,toString(){return A}}}rR2.baggageEntryMetadataFromString=Qo4});
var PT2=E((OT2)=>{Object.defineProperty(OT2,"__esModule",{value:!0});OT2.NOOP_METER_PROVIDER=OT2.NoopMeterProvider=void 0;var zt4=hF0();class GI0{getMeter(A,B,Q){return zt4.NOOP_METER}}OT2.NoopMeterProvider=GI0;OT2.NOOP_METER_PROVIDER=new GI0});
var PY0=E((xc2)=>{Object.defineProperty(xc2,"__esModule",{value:!0});xc2.AggregationTemporalityPreference=void 0;var UF6;(function(A){A[A.DELTA=0]="DELTA",A[A.CUMULATIVE=1]="CUMULATIVE",A[A.LOWMEMORY=2]="LOWMEMORY"})(UF6=xc2.AggregationTemporalityPreference||(xc2.AggregationTemporalityPreference={}))});
var Ph2=E((Oh2)=>{Object.defineProperty(Oh2,"__esModule",{value:!0});Oh2.validateValue=Oh2.validateKey=void 0;var nI0="[_0-9a-z-*/]",F76=`[a-z]${nI0}{0,255}`,I76=`[a-z0-9]${nI0}{0,240}@[a-z]${nI0}{0,13}`,Y76=new RegExp(`^(?:${F76}|${I76})$`),W76=/^[ -~]{0,255}[!-~]$/,J76=/,|=/;function X76(A){return Y76.test(A)}Oh2.validateKey=X76;function V76(A){return W76.test(A)&&!J76.test(A)}Oh2.validateValue=V76});
var Pm2=E((vo)=>{Object.defineProperty(vo,"__esModule",{value:!0});vo.serviceInstanceIdDetector=vo.processDetector=vo.osDetector=vo.hostDetector=void 0;var WT1=Tm2();Object.defineProperty(vo,"hostDetector",{enumerable:!0,get:function(){return WT1.hostDetector}});Object.defineProperty(vo,"osDetector",{enumerable:!0,get:function(){return WT1.osDetector}});Object.defineProperty(vo,"processDetector",{enumerable:!0,get:function(){return WT1.processDetector}});Object.defineProperty(vo,"serviceInstanceIdDetector",{enumerable:!0,get:function(){return WT1.serviceInstanceIdDetector}})});
var Pu2=E((Ou2)=>{Object.defineProperty(Ou2,"__esModule",{value:!0});Ou2.identity=Ou2.isPromiseLike=void 0;var uD6=(A)=>{return A!==null&&typeof A==="object"&&typeof A.then==="function"};Ou2.isPromiseLike=uD6;function mD6(A){return A}Ou2.identity=mD6});
var Q31=E((eg2)=>{Object.defineProperty(eg2,"__esModule",{value:!0});eg2.toAggregation=eg2.AggregationType=void 0;var Ou=og2(),Tu;(function(A){A[A.DEFAULT=0]="DEFAULT",A[A.DROP=1]="DROP",A[A.SUM=2]="SUM",A[A.LAST_VALUE=3]="LAST_VALUE",A[A.EXPLICIT_BUCKET_HISTOGRAM=4]="EXPLICIT_BUCKET_HISTOGRAM",A[A.EXPONENTIAL_HISTOGRAM=5]="EXPONENTIAL_HISTOGRAM"})(Tu=eg2.AggregationType||(eg2.AggregationType={}));function TD6(A){switch(A.type){case Tu.DEFAULT:return Ou.DEFAULT_AGGREGATION;case Tu.DROP:return Ou.DROP_AGGREGATION;case Tu.SUM:return Ou.SUM_AGGREGATION;case Tu.LAST_VALUE:return Ou.LAST_VALUE_AGGREGATION;case Tu.EXPONENTIAL_HISTOGRAM:{let B=A;return new Ou.ExponentialHistogramAggregation(B.options?.maxSize,B.options?.recordMinMax)}case Tu.EXPLICIT_BUCKET_HISTOGRAM:{let B=A;if(B.options==null)return Ou.HISTOGRAM_AGGREGATION;else return new Ou.ExplicitBucketHistogramAggregation(B.options?.boundaries,B.options?.recordMinMax)}default:throw new Error("Unsupported Aggregation")}}eg2.toAggregation=TD6});
var QT2=E((BT2)=>{Object.defineProperty(BT2,"__esModule",{value:!0});BT2.SamplingDecision=void 0;var At4;(function(A){A[A.NOT_RECORD=0]="NOT_RECORD",A[A.RECORD=1]="RECORD",A[A.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"})(At4=BT2.SamplingDecision||(BT2.SamplingDecision={}))});
var QY0=E((Bu2)=>{Object.defineProperty(Bu2,"__esModule",{value:!0});Bu2.DEFAULT_AGGREGATION_TEMPORALITY_SELECTOR=Bu2.DEFAULT_AGGREGATION_SELECTOR=void 0;var PD6=mO1(),SD6=Q31(),jD6=(A)=>{return{type:SD6.AggregationType.DEFAULT}};Bu2.DEFAULT_AGGREGATION_SELECTOR=jD6;var yD6=(A)=>PD6.AggregationTemporality.CUMULATIVE;Bu2.DEFAULT_AGGREGATION_TEMPORALITY_SELECTOR=yD6});
var Qh2=E((tf2)=>{Object.defineProperty(tf2,"__esModule",{value:!0});tf2.ATTR_JVM_GC_NAME=tf2.ATTR_JVM_GC_ACTION=tf2.ATTR_HTTP_ROUTE=tf2.ATTR_HTTP_RESPONSE_STATUS_CODE=tf2.ATTR_HTTP_RESPONSE_HEADER=tf2.ATTR_HTTP_REQUEST_RESEND_COUNT=tf2.ATTR_HTTP_REQUEST_METHOD_ORIGINAL=tf2.HTTP_REQUEST_METHOD_VALUE_TRACE=tf2.HTTP_REQUEST_METHOD_VALUE_PUT=tf2.HTTP_REQUEST_METHOD_VALUE_POST=tf2.HTTP_REQUEST_METHOD_VALUE_PATCH=tf2.HTTP_REQUEST_METHOD_VALUE_OPTIONS=tf2.HTTP_REQUEST_METHOD_VALUE_HEAD=tf2.HTTP_REQUEST_METHOD_VALUE_GET=tf2.HTTP_REQUEST_METHOD_VALUE_DELETE=tf2.HTTP_REQUEST_METHOD_VALUE_CONNECT=tf2.HTTP_REQUEST_METHOD_VALUE_OTHER=tf2.ATTR_HTTP_REQUEST_METHOD=tf2.ATTR_HTTP_REQUEST_HEADER=tf2.ATTR_EXCEPTION_TYPE=tf2.ATTR_EXCEPTION_STACKTRACE=tf2.ATTR_EXCEPTION_MESSAGE=tf2.ATTR_EXCEPTION_ESCAPED=tf2.ERROR_TYPE_VALUE_OTHER=tf2.ATTR_ERROR_TYPE=tf2.DOTNET_GC_HEAP_GENERATION_VALUE_POH=tf2.DOTNET_GC_HEAP_GENERATION_VALUE_LOH=tf2.DOTNET_GC_HEAP_GENERATION_VALUE_GEN2=tf2.DOTNET_GC_HEAP_GENERATION_VALUE_GEN1=tf2.DOTNET_GC_HEAP_GENERATION_VALUE_GEN0=tf2.ATTR_DOTNET_GC_HEAP_GENERATION=tf2.ATTR_CLIENT_PORT=tf2.ATTR_CLIENT_ADDRESS=tf2.ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_SUCCESS=tf2.ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_FAILURE=tf2.ATTR_ASPNETCORE_ROUTING_MATCH_STATUS=tf2.ATTR_ASPNETCORE_ROUTING_IS_FALLBACK=tf2.ATTR_ASPNETCORE_REQUEST_IS_UNHANDLED=tf2.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_REQUEST_CANCELED=tf2.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_GLOBAL_LIMITER=tf2.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ENDPOINT_LIMITER=tf2.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ACQUIRED=tf2.ATTR_ASPNETCORE_RATE_LIMITING_RESULT=tf2.ATTR_ASPNETCORE_RATE_LIMITING_POLICY=tf2.ATTR_ASPNETCORE_DIAGNOSTICS_HANDLER_TYPE=tf2.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_UNHANDLED=tf2.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_SKIPPED=tf2.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_HANDLED=tf2.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_ABORTED=tf2.ATTR_ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT=void 0;tf2.TELEMETRY_SDK_LANGUAGE_VALUE_GO=tf2.TELEMETRY_SDK_LANGUAGE_VALUE_ERLANG=tf2.TELEMETRY_SDK_LANGUAGE_VALUE_DOTNET=tf2.TELEMETRY_SDK_LANGUAGE_VALUE_CPP=tf2.ATTR_TELEMETRY_SDK_LANGUAGE=tf2.SIGNALR_TRANSPORT_VALUE_WEB_SOCKETS=tf2.SIGNALR_TRANSPORT_VALUE_SERVER_SENT_EVENTS=tf2.SIGNALR_TRANSPORT_VALUE_LONG_POLLING=tf2.ATTR_SIGNALR_TRANSPORT=tf2.SIGNALR_CONNECTION_STATUS_VALUE_TIMEOUT=tf2.SIGNALR_CONNECTION_STATUS_VALUE_NORMAL_CLOSURE=tf2.SIGNALR_CONNECTION_STATUS_VALUE_APP_SHUTDOWN=tf2.ATTR_SIGNALR_CONNECTION_STATUS=tf2.ATTR_SERVICE_VERSION=tf2.ATTR_SERVICE_NAME=tf2.ATTR_SERVER_PORT=tf2.ATTR_SERVER_ADDRESS=tf2.ATTR_OTEL_STATUS_DESCRIPTION=tf2.OTEL_STATUS_CODE_VALUE_OK=tf2.OTEL_STATUS_CODE_VALUE_ERROR=tf2.ATTR_OTEL_STATUS_CODE=tf2.ATTR_OTEL_SCOPE_VERSION=tf2.ATTR_OTEL_SCOPE_NAME=tf2.NETWORK_TYPE_VALUE_IPV6=tf2.NETWORK_TYPE_VALUE_IPV4=tf2.ATTR_NETWORK_TYPE=tf2.NETWORK_TRANSPORT_VALUE_UNIX=tf2.NETWORK_TRANSPORT_VALUE_UDP=tf2.NETWORK_TRANSPORT_VALUE_TCP=tf2.NETWORK_TRANSPORT_VALUE_QUIC=tf2.NETWORK_TRANSPORT_VALUE_PIPE=tf2.ATTR_NETWORK_TRANSPORT=tf2.ATTR_NETWORK_PROTOCOL_VERSION=tf2.ATTR_NETWORK_PROTOCOL_NAME=tf2.ATTR_NETWORK_PEER_PORT=tf2.ATTR_NETWORK_PEER_ADDRESS=tf2.ATTR_NETWORK_LOCAL_PORT=tf2.ATTR_NETWORK_LOCAL_ADDRESS=tf2.JVM_THREAD_STATE_VALUE_WAITING=tf2.JVM_THREAD_STATE_VALUE_TIMED_WAITING=tf2.JVM_THREAD_STATE_VALUE_TERMINATED=tf2.JVM_THREAD_STATE_VALUE_RUNNABLE=tf2.JVM_THREAD_STATE_VALUE_NEW=tf2.JVM_THREAD_STATE_VALUE_BLOCKED=tf2.ATTR_JVM_THREAD_STATE=tf2.ATTR_JVM_THREAD_DAEMON=tf2.JVM_MEMORY_TYPE_VALUE_NON_HEAP=tf2.JVM_MEMORY_TYPE_VALUE_HEAP=tf2.ATTR_JVM_MEMORY_TYPE=tf2.ATTR_JVM_MEMORY_POOL_NAME=void 0;tf2.ATTR_USER_AGENT_ORIGINAL=tf2.ATTR_URL_SCHEME=tf2.ATTR_URL_QUERY=tf2.ATTR_URL_PATH=tf2.ATTR_URL_FULL=tf2.ATTR_URL_FRAGMENT=tf2.ATTR_TELEMETRY_SDK_VERSION=tf2.ATTR_TELEMETRY_SDK_NAME=tf2.TELEMETRY_SDK_LANGUAGE_VALUE_WEBJS=tf2.TELEMETRY_SDK_LANGUAGE_VALUE_SWIFT=tf2.TELEMETRY_SDK_LANGUAGE_VALUE_RUST=tf2.TELEMETRY_SDK_LANGUAGE_VALUE_RUBY=tf2.TELEMETRY_SDK_LANGUAGE_VALUE_PYTHON=tf2.TELEMETRY_SDK_LANGUAGE_VALUE_PHP=tf2.TELEMETRY_SDK_LANGUAGE_VALUE_NODEJS=tf2.TELEMETRY_SDK_LANGUAGE_VALUE_JAVA=void 0;tf2.ATTR_ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT="aspnetcore.diagnostics.exception.result";tf2.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_ABORTED="aborted";tf2.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_HANDLED="handled";tf2.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_SKIPPED="skipped";tf2.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_UNHANDLED="unhandled";tf2.ATTR_ASPNETCORE_DIAGNOSTICS_HANDLER_TYPE="aspnetcore.diagnostics.handler.type";tf2.ATTR_ASPNETCORE_RATE_LIMITING_POLICY="aspnetcore.rate_limiting.policy";tf2.ATTR_ASPNETCORE_RATE_LIMITING_RESULT="aspnetcore.rate_limiting.result";tf2.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ACQUIRED="acquired";tf2.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ENDPOINT_LIMITER="endpoint_limiter";tf2.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_GLOBAL_LIMITER="global_limiter";tf2.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_REQUEST_CANCELED="request_canceled";tf2.ATTR_ASPNETCORE_REQUEST_IS_UNHANDLED="aspnetcore.request.is_unhandled";tf2.ATTR_ASPNETCORE_ROUTING_IS_FALLBACK="aspnetcore.routing.is_fallback";tf2.ATTR_ASPNETCORE_ROUTING_MATCH_STATUS="aspnetcore.routing.match_status";tf2.ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_FAILURE="failure";tf2.ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_SUCCESS="success";tf2.ATTR_CLIENT_ADDRESS="client.address";tf2.ATTR_CLIENT_PORT="client.port";tf2.ATTR_DOTNET_GC_HEAP_GENERATION="dotnet.gc.heap.generation";tf2.DOTNET_GC_HEAP_GENERATION_VALUE_GEN0="gen0";tf2.DOTNET_GC_HEAP_GENERATION_VALUE_GEN1="gen1";tf2.DOTNET_GC_HEAP_GENERATION_VALUE_GEN2="gen2";tf2.DOTNET_GC_HEAP_GENERATION_VALUE_LOH="loh";tf2.DOTNET_GC_HEAP_GENERATION_VALUE_POH="poh";tf2.ATTR_ERROR_TYPE="error.type";tf2.ERROR_TYPE_VALUE_OTHER="_OTHER";tf2.ATTR_EXCEPTION_ESCAPED="exception.escaped";tf2.ATTR_EXCEPTION_MESSAGE="exception.message";tf2.ATTR_EXCEPTION_STACKTRACE="exception.stacktrace";tf2.ATTR_EXCEPTION_TYPE="exception.type";var P66=(A)=>`http.request.header.${A}`;tf2.ATTR_HTTP_REQUEST_HEADER=P66;tf2.ATTR_HTTP_REQUEST_METHOD="http.request.method";tf2.HTTP_REQUEST_METHOD_VALUE_OTHER="_OTHER";tf2.HTTP_REQUEST_METHOD_VALUE_CONNECT="CONNECT";tf2.HTTP_REQUEST_METHOD_VALUE_DELETE="DELETE";tf2.HTTP_REQUEST_METHOD_VALUE_GET="GET";tf2.HTTP_REQUEST_METHOD_VALUE_HEAD="HEAD";tf2.HTTP_REQUEST_METHOD_VALUE_OPTIONS="OPTIONS";tf2.HTTP_REQUEST_METHOD_VALUE_PATCH="PATCH";tf2.HTTP_REQUEST_METHOD_VALUE_POST="POST";tf2.HTTP_REQUEST_METHOD_VALUE_PUT="PUT";tf2.HTTP_REQUEST_METHOD_VALUE_TRACE="TRACE";tf2.ATTR_HTTP_REQUEST_METHOD_ORIGINAL="http.request.method_original";tf2.ATTR_HTTP_REQUEST_RESEND_COUNT="http.request.resend_count";var S66=(A)=>`http.response.header.${A}`;tf2.ATTR_HTTP_RESPONSE_HEADER=S66;tf2.ATTR_HTTP_RESPONSE_STATUS_CODE="http.response.status_code";tf2.ATTR_HTTP_ROUTE="http.route";tf2.ATTR_JVM_GC_ACTION="jvm.gc.action";tf2.ATTR_JVM_GC_NAME="jvm.gc.name";tf2.ATTR_JVM_MEMORY_POOL_NAME="jvm.memory.pool.name";tf2.ATTR_JVM_MEMORY_TYPE="jvm.memory.type";tf2.JVM_MEMORY_TYPE_VALUE_HEAP="heap";tf2.JVM_MEMORY_TYPE_VALUE_NON_HEAP="non_heap";tf2.ATTR_JVM_THREAD_DAEMON="jvm.thread.daemon";tf2.ATTR_JVM_THREAD_STATE="jvm.thread.state";tf2.JVM_THREAD_STATE_VALUE_BLOCKED="blocked";tf2.JVM_THREAD_STATE_VALUE_NEW="new";tf2.JVM_THREAD_STATE_VALUE_RUNNABLE="runnable";tf2.JVM_THREAD_STATE_VALUE_TERMINATED="terminated";tf2.JVM_THREAD_STATE_VALUE_TIMED_WAITING="timed_waiting";tf2.JVM_THREAD_STATE_VALUE_WAITING="waiting";tf2.ATTR_NETWORK_LOCAL_ADDRESS="network.local.address";tf2.ATTR_NETWORK_LOCAL_PORT="network.local.port";tf2.ATTR_NETWORK_PEER_ADDRESS="network.peer.address";tf2.ATTR_NETWORK_PEER_PORT="network.peer.port";tf2.ATTR_NETWORK_PROTOCOL_NAME="network.protocol.name";tf2.ATTR_NETWORK_PROTOCOL_VERSION="network.protocol.version";tf2.ATTR_NETWORK_TRANSPORT="network.transport";tf2.NETWORK_TRANSPORT_VALUE_PIPE="pipe";tf2.NETWORK_TRANSPORT_VALUE_QUIC="quic";tf2.NETWORK_TRANSPORT_VALUE_TCP="tcp";tf2.NETWORK_TRANSPORT_VALUE_UDP="udp";tf2.NETWORK_TRANSPORT_VALUE_UNIX="unix";tf2.ATTR_NETWORK_TYPE="network.type";tf2.NETWORK_TYPE_VALUE_IPV4="ipv4";tf2.NETWORK_TYPE_VALUE_IPV6="ipv6";tf2.ATTR_OTEL_SCOPE_NAME="otel.scope.name";tf2.ATTR_OTEL_SCOPE_VERSION="otel.scope.version";tf2.ATTR_OTEL_STATUS_CODE="otel.status_code";tf2.OTEL_STATUS_CODE_VALUE_ERROR="ERROR";tf2.OTEL_STATUS_CODE_VALUE_OK="OK";tf2.ATTR_OTEL_STATUS_DESCRIPTION="otel.status_description";tf2.ATTR_SERVER_ADDRESS="server.address";tf2.ATTR_SERVER_PORT="server.port";tf2.ATTR_SERVICE_NAME="service.name";tf2.ATTR_SERVICE_VERSION="service.version";tf2.ATTR_SIGNALR_CONNECTION_STATUS="signalr.connection.status";tf2.SIGNALR_CONNECTION_STATUS_VALUE_APP_SHUTDOWN="app_shutdown";tf2.SIGNALR_CONNECTION_STATUS_VALUE_NORMAL_CLOSURE="normal_closure";tf2.SIGNALR_CONNECTION_STATUS_VALUE_TIMEOUT="timeout";tf2.ATTR_SIGNALR_TRANSPORT="signalr.transport";tf2.SIGNALR_TRANSPORT_VALUE_LONG_POLLING="long_polling";tf2.SIGNALR_TRANSPORT_VALUE_SERVER_SENT_EVENTS="server_sent_events";tf2.SIGNALR_TRANSPORT_VALUE_WEB_SOCKETS="web_sockets";tf2.ATTR_TELEMETRY_SDK_LANGUAGE="telemetry.sdk.language";tf2.TELEMETRY_SDK_LANGUAGE_VALUE_CPP="cpp";tf2.TELEMETRY_SDK_LANGUAGE_VALUE_DOTNET="dotnet";tf2.TELEMETRY_SDK_LANGUAGE_VALUE_ERLANG="erlang";tf2.TELEMETRY_SDK_LANGUAGE_VALUE_GO="go";tf2.TELEMETRY_SDK_LANGUAGE_VALUE_JAVA="java";tf2.TELEMETRY_SDK_LANGUAGE_VALUE_NODEJS="nodejs";tf2.TELEMETRY_SDK_LANGUAGE_VALUE_PHP="php";tf2.TELEMETRY_SDK_LANGUAGE_VALUE_PYTHON="python";tf2.TELEMETRY_SDK_LANGUAGE_VALUE_RUBY="ruby";tf2.TELEMETRY_SDK_LANGUAGE_VALUE_RUST="rust";tf2.TELEMETRY_SDK_LANGUAGE_VALUE_SWIFT="swift";tf2.TELEMETRY_SDK_LANGUAGE_VALUE_WEBJS="webjs";tf2.ATTR_TELEMETRY_SDK_NAME="telemetry.sdk.name";tf2.ATTR_TELEMETRY_SDK_VERSION="telemetry.sdk.version";tf2.ATTR_URL_FRAGMENT="url.fragment";tf2.ATTR_URL_FULL="url.full";tf2.ATTR_URL_PATH="url.path";tf2.ATTR_URL_QUERY="url.query";tf2.ATTR_URL_SCHEME="url.scheme";tf2.ATTR_USER_AGENT_ORIGINAL="user_agent.original"});
var RF0=E((MR2)=>{Object.defineProperty(MR2,"__esModule",{value:!0});MR2.VERSION=void 0;MR2.VERSION="1.9.0"});
var RT1=E((Qp2,Dp2)=>{Object.defineProperty(Qp2,"__esModule",{value:!0});var n9=oY0(),u0=n9.Reader,W4=n9.Writer,H1=n9.util,X1=n9.roots.default||(n9.roots.default={});X1.opentelemetry=function(){var A={};return A.proto=function(){var B={};return B.common=function(){var Q={};return Q.v1=function(){var D={};return D.AnyValue=function(){function Z(F){if(F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}Z.prototype.stringValue=null,Z.prototype.boolValue=null,Z.prototype.intValue=null,Z.prototype.doubleValue=null,Z.prototype.arrayValue=null,Z.prototype.kvlistValue=null,Z.prototype.bytesValue=null;var G;return Object.defineProperty(Z.prototype,"value",{get:H1.oneOfGetter(G=["stringValue","boolValue","intValue","doubleValue","arrayValue","kvlistValue","bytesValue"]),set:H1.oneOfSetter(G)}),Z.create=function F(I){return new Z(I)},Z.encode=function F(I,Y){if(!Y)Y=W4.create();if(I.stringValue!=null&&Object.hasOwnProperty.call(I,"stringValue"))Y.uint32(10).string(I.stringValue);if(I.boolValue!=null&&Object.hasOwnProperty.call(I,"boolValue"))Y.uint32(16).bool(I.boolValue);if(I.intValue!=null&&Object.hasOwnProperty.call(I,"intValue"))Y.uint32(24).int64(I.intValue);if(I.doubleValue!=null&&Object.hasOwnProperty.call(I,"doubleValue"))Y.uint32(33).double(I.doubleValue);if(I.arrayValue!=null&&Object.hasOwnProperty.call(I,"arrayValue"))X1.opentelemetry.proto.common.v1.ArrayValue.encode(I.arrayValue,Y.uint32(42).fork()).ldelim();if(I.kvlistValue!=null&&Object.hasOwnProperty.call(I,"kvlistValue"))X1.opentelemetry.proto.common.v1.KeyValueList.encode(I.kvlistValue,Y.uint32(50).fork()).ldelim();if(I.bytesValue!=null&&Object.hasOwnProperty.call(I,"bytesValue"))Y.uint32(58).bytes(I.bytesValue);return Y},Z.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},Z.decode=function F(I,Y){if(!(I instanceof u0))I=u0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.common.v1.AnyValue;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 1:{J.stringValue=I.string();break}case 2:{J.boolValue=I.bool();break}case 3:{J.intValue=I.int64();break}case 4:{J.doubleValue=I.double();break}case 5:{J.arrayValue=X1.opentelemetry.proto.common.v1.ArrayValue.decode(I,I.uint32());break}case 6:{J.kvlistValue=X1.opentelemetry.proto.common.v1.KeyValueList.decode(I,I.uint32());break}case 7:{J.bytesValue=I.bytes();break}default:I.skipType(X&7);break}}return J},Z.decodeDelimited=function F(I){if(!(I instanceof u0))I=new u0(I);return this.decode(I,I.uint32())},Z.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";var Y={};if(I.stringValue!=null&&I.hasOwnProperty("stringValue")){if(Y.value=1,!H1.isString(I.stringValue))return"stringValue: string expected"}if(I.boolValue!=null&&I.hasOwnProperty("boolValue")){if(Y.value===1)return"value: multiple values";if(Y.value=1,typeof I.boolValue!=="boolean")return"boolValue: boolean expected"}if(I.intValue!=null&&I.hasOwnProperty("intValue")){if(Y.value===1)return"value: multiple values";if(Y.value=1,!H1.isInteger(I.intValue)&&!(I.intValue&&H1.isInteger(I.intValue.low)&&H1.isInteger(I.intValue.high)))return"intValue: integer|Long expected"}if(I.doubleValue!=null&&I.hasOwnProperty("doubleValue")){if(Y.value===1)return"value: multiple values";if(Y.value=1,typeof I.doubleValue!=="number")return"doubleValue: number expected"}if(I.arrayValue!=null&&I.hasOwnProperty("arrayValue")){if(Y.value===1)return"value: multiple values";Y.value=1;{var W=X1.opentelemetry.proto.common.v1.ArrayValue.verify(I.arrayValue);if(W)return"arrayValue."+W}}if(I.kvlistValue!=null&&I.hasOwnProperty("kvlistValue")){if(Y.value===1)return"value: multiple values";Y.value=1;{var W=X1.opentelemetry.proto.common.v1.KeyValueList.verify(I.kvlistValue);if(W)return"kvlistValue."+W}}if(I.bytesValue!=null&&I.hasOwnProperty("bytesValue")){if(Y.value===1)return"value: multiple values";if(Y.value=1,!(I.bytesValue&&typeof I.bytesValue.length==="number"||H1.isString(I.bytesValue)))return"bytesValue: buffer expected"}return null},Z.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.common.v1.AnyValue)return I;var Y=new X1.opentelemetry.proto.common.v1.AnyValue;if(I.stringValue!=null)Y.stringValue=String(I.stringValue);if(I.boolValue!=null)Y.boolValue=Boolean(I.boolValue);if(I.intValue!=null){if(H1.Long)(Y.intValue=H1.Long.fromValue(I.intValue)).unsigned=!1;else if(typeof I.intValue==="string")Y.intValue=parseInt(I.intValue,10);else if(typeof I.intValue==="number")Y.intValue=I.intValue;else if(typeof I.intValue==="object")Y.intValue=new H1.LongBits(I.intValue.low>>>0,I.intValue.high>>>0).toNumber()}if(I.doubleValue!=null)Y.doubleValue=Number(I.doubleValue);if(I.arrayValue!=null){if(typeof I.arrayValue!=="object")throw TypeError(".opentelemetry.proto.common.v1.AnyValue.arrayValue: object expected");Y.arrayValue=X1.opentelemetry.proto.common.v1.ArrayValue.fromObject(I.arrayValue)}if(I.kvlistValue!=null){if(typeof I.kvlistValue!=="object")throw TypeError(".opentelemetry.proto.common.v1.AnyValue.kvlistValue: object expected");Y.kvlistValue=X1.opentelemetry.proto.common.v1.KeyValueList.fromObject(I.kvlistValue)}if(I.bytesValue!=null){if(typeof I.bytesValue==="string")H1.base64.decode(I.bytesValue,Y.bytesValue=H1.newBuffer(H1.base64.length(I.bytesValue)),0);else if(I.bytesValue.length>=0)Y.bytesValue=I.bytesValue}return Y},Z.toObject=function F(I,Y){if(!Y)Y={};var W={};if(I.stringValue!=null&&I.hasOwnProperty("stringValue")){if(W.stringValue=I.stringValue,Y.oneofs)W.value="stringValue"}if(I.boolValue!=null&&I.hasOwnProperty("boolValue")){if(W.boolValue=I.boolValue,Y.oneofs)W.value="boolValue"}if(I.intValue!=null&&I.hasOwnProperty("intValue")){if(typeof I.intValue==="number")W.intValue=Y.longs===String?String(I.intValue):I.intValue;else W.intValue=Y.longs===String?H1.Long.prototype.toString.call(I.intValue):Y.longs===Number?new H1.LongBits(I.intValue.low>>>0,I.intValue.high>>>0).toNumber():I.intValue;if(Y.oneofs)W.value="intValue"}if(I.doubleValue!=null&&I.hasOwnProperty("doubleValue")){if(W.doubleValue=Y.json&&!isFinite(I.doubleValue)?String(I.doubleValue):I.doubleValue,Y.oneofs)W.value="doubleValue"}if(I.arrayValue!=null&&I.hasOwnProperty("arrayValue")){if(W.arrayValue=X1.opentelemetry.proto.common.v1.ArrayValue.toObject(I.arrayValue,Y),Y.oneofs)W.value="arrayValue"}if(I.kvlistValue!=null&&I.hasOwnProperty("kvlistValue")){if(W.kvlistValue=X1.opentelemetry.proto.common.v1.KeyValueList.toObject(I.kvlistValue,Y),Y.oneofs)W.value="kvlistValue"}if(I.bytesValue!=null&&I.hasOwnProperty("bytesValue")){if(W.bytesValue=Y.bytes===String?H1.base64.encode(I.bytesValue,0,I.bytesValue.length):Y.bytes===Array?Array.prototype.slice.call(I.bytesValue):I.bytesValue,Y.oneofs)W.value="bytesValue"}return W},Z.prototype.toJSON=function F(){return this.constructor.toObject(this,n9.util.toJSONOptions)},Z.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.common.v1.AnyValue"},Z}(),D.ArrayValue=function(){function Z(G){if(this.values=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.values=H1.emptyArray,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=W4.create();if(F.values!=null&&F.values.length)for(var Y=0;Y<F.values.length;++Y)X1.opentelemetry.proto.common.v1.AnyValue.encode(F.values[Y],I.uint32(10).fork()).ldelim();return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof u0))F=u0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.common.v1.ArrayValue;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{if(!(W.values&&W.values.length))W.values=[];W.values.push(X1.opentelemetry.proto.common.v1.AnyValue.decode(F,F.uint32()));break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof u0))F=new u0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.values!=null&&F.hasOwnProperty("values")){if(!Array.isArray(F.values))return"values: array expected";for(var I=0;I<F.values.length;++I){var Y=X1.opentelemetry.proto.common.v1.AnyValue.verify(F.values[I]);if(Y)return"values."+Y}}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.common.v1.ArrayValue)return F;var I=new X1.opentelemetry.proto.common.v1.ArrayValue;if(F.values){if(!Array.isArray(F.values))throw TypeError(".opentelemetry.proto.common.v1.ArrayValue.values: array expected");I.values=[];for(var Y=0;Y<F.values.length;++Y){if(typeof F.values[Y]!=="object")throw TypeError(".opentelemetry.proto.common.v1.ArrayValue.values: object expected");I.values[Y]=X1.opentelemetry.proto.common.v1.AnyValue.fromObject(F.values[Y])}}return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.values=[];if(F.values&&F.values.length){Y.values=[];for(var W=0;W<F.values.length;++W)Y.values[W]=X1.opentelemetry.proto.common.v1.AnyValue.toObject(F.values[W],I)}return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,n9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.common.v1.ArrayValue"},Z}(),D.KeyValueList=function(){function Z(G){if(this.values=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.values=H1.emptyArray,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=W4.create();if(F.values!=null&&F.values.length)for(var Y=0;Y<F.values.length;++Y)X1.opentelemetry.proto.common.v1.KeyValue.encode(F.values[Y],I.uint32(10).fork()).ldelim();return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof u0))F=u0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.common.v1.KeyValueList;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{if(!(W.values&&W.values.length))W.values=[];W.values.push(X1.opentelemetry.proto.common.v1.KeyValue.decode(F,F.uint32()));break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof u0))F=new u0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.values!=null&&F.hasOwnProperty("values")){if(!Array.isArray(F.values))return"values: array expected";for(var I=0;I<F.values.length;++I){var Y=X1.opentelemetry.proto.common.v1.KeyValue.verify(F.values[I]);if(Y)return"values."+Y}}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.common.v1.KeyValueList)return F;var I=new X1.opentelemetry.proto.common.v1.KeyValueList;if(F.values){if(!Array.isArray(F.values))throw TypeError(".opentelemetry.proto.common.v1.KeyValueList.values: array expected");I.values=[];for(var Y=0;Y<F.values.length;++Y){if(typeof F.values[Y]!=="object")throw TypeError(".opentelemetry.proto.common.v1.KeyValueList.values: object expected");I.values[Y]=X1.opentelemetry.proto.common.v1.KeyValue.fromObject(F.values[Y])}}return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.values=[];if(F.values&&F.values.length){Y.values=[];for(var W=0;W<F.values.length;++W)Y.values[W]=X1.opentelemetry.proto.common.v1.KeyValue.toObject(F.values[W],I)}return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,n9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.common.v1.KeyValueList"},Z}(),D.KeyValue=function(){function Z(G){if(G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.key=null,Z.prototype.value=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=W4.create();if(F.key!=null&&Object.hasOwnProperty.call(F,"key"))I.uint32(10).string(F.key);if(F.value!=null&&Object.hasOwnProperty.call(F,"value"))X1.opentelemetry.proto.common.v1.AnyValue.encode(F.value,I.uint32(18).fork()).ldelim();return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof u0))F=u0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.common.v1.KeyValue;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{W.key=F.string();break}case 2:{W.value=X1.opentelemetry.proto.common.v1.AnyValue.decode(F,F.uint32());break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof u0))F=new u0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.key!=null&&F.hasOwnProperty("key")){if(!H1.isString(F.key))return"key: string expected"}if(F.value!=null&&F.hasOwnProperty("value")){var I=X1.opentelemetry.proto.common.v1.AnyValue.verify(F.value);if(I)return"value."+I}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.common.v1.KeyValue)return F;var I=new X1.opentelemetry.proto.common.v1.KeyValue;if(F.key!=null)I.key=String(F.key);if(F.value!=null){if(typeof F.value!=="object")throw TypeError(".opentelemetry.proto.common.v1.KeyValue.value: object expected");I.value=X1.opentelemetry.proto.common.v1.AnyValue.fromObject(F.value)}return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.defaults)Y.key="",Y.value=null;if(F.key!=null&&F.hasOwnProperty("key"))Y.key=F.key;if(F.value!=null&&F.hasOwnProperty("value"))Y.value=X1.opentelemetry.proto.common.v1.AnyValue.toObject(F.value,I);return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,n9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.common.v1.KeyValue"},Z}(),D.InstrumentationScope=function(){function Z(G){if(this.attributes=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.name=null,Z.prototype.version=null,Z.prototype.attributes=H1.emptyArray,Z.prototype.droppedAttributesCount=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=W4.create();if(F.name!=null&&Object.hasOwnProperty.call(F,"name"))I.uint32(10).string(F.name);if(F.version!=null&&Object.hasOwnProperty.call(F,"version"))I.uint32(18).string(F.version);if(F.attributes!=null&&F.attributes.length)for(var Y=0;Y<F.attributes.length;++Y)X1.opentelemetry.proto.common.v1.KeyValue.encode(F.attributes[Y],I.uint32(26).fork()).ldelim();if(F.droppedAttributesCount!=null&&Object.hasOwnProperty.call(F,"droppedAttributesCount"))I.uint32(32).uint32(F.droppedAttributesCount);return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof u0))F=u0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.common.v1.InstrumentationScope;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{W.name=F.string();break}case 2:{W.version=F.string();break}case 3:{if(!(W.attributes&&W.attributes.length))W.attributes=[];W.attributes.push(X1.opentelemetry.proto.common.v1.KeyValue.decode(F,F.uint32()));break}case 4:{W.droppedAttributesCount=F.uint32();break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof u0))F=new u0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.name!=null&&F.hasOwnProperty("name")){if(!H1.isString(F.name))return"name: string expected"}if(F.version!=null&&F.hasOwnProperty("version")){if(!H1.isString(F.version))return"version: string expected"}if(F.attributes!=null&&F.hasOwnProperty("attributes")){if(!Array.isArray(F.attributes))return"attributes: array expected";for(var I=0;I<F.attributes.length;++I){var Y=X1.opentelemetry.proto.common.v1.KeyValue.verify(F.attributes[I]);if(Y)return"attributes."+Y}}if(F.droppedAttributesCount!=null&&F.hasOwnProperty("droppedAttributesCount")){if(!H1.isInteger(F.droppedAttributesCount))return"droppedAttributesCount: integer expected"}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.common.v1.InstrumentationScope)return F;var I=new X1.opentelemetry.proto.common.v1.InstrumentationScope;if(F.name!=null)I.name=String(F.name);if(F.version!=null)I.version=String(F.version);if(F.attributes){if(!Array.isArray(F.attributes))throw TypeError(".opentelemetry.proto.common.v1.InstrumentationScope.attributes: array expected");I.attributes=[];for(var Y=0;Y<F.attributes.length;++Y){if(typeof F.attributes[Y]!=="object")throw TypeError(".opentelemetry.proto.common.v1.InstrumentationScope.attributes: object expected");I.attributes[Y]=X1.opentelemetry.proto.common.v1.KeyValue.fromObject(F.attributes[Y])}}if(F.droppedAttributesCount!=null)I.droppedAttributesCount=F.droppedAttributesCount>>>0;return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.attributes=[];if(I.defaults)Y.name="",Y.version="",Y.droppedAttributesCount=0;if(F.name!=null&&F.hasOwnProperty("name"))Y.name=F.name;if(F.version!=null&&F.hasOwnProperty("version"))Y.version=F.version;if(F.attributes&&F.attributes.length){Y.attributes=[];for(var W=0;W<F.attributes.length;++W)Y.attributes[W]=X1.opentelemetry.proto.common.v1.KeyValue.toObject(F.attributes[W],I)}if(F.droppedAttributesCount!=null&&F.hasOwnProperty("droppedAttributesCount"))Y.droppedAttributesCount=F.droppedAttributesCount;return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,n9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.common.v1.InstrumentationScope"},Z}(),D}(),Q}(),B.resource=function(){var Q={};return Q.v1=function(){var D={};return D.Resource=function(){function Z(G){if(this.attributes=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.attributes=H1.emptyArray,Z.prototype.droppedAttributesCount=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=W4.create();if(F.attributes!=null&&F.attributes.length)for(var Y=0;Y<F.attributes.length;++Y)X1.opentelemetry.proto.common.v1.KeyValue.encode(F.attributes[Y],I.uint32(10).fork()).ldelim();if(F.droppedAttributesCount!=null&&Object.hasOwnProperty.call(F,"droppedAttributesCount"))I.uint32(16).uint32(F.droppedAttributesCount);return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof u0))F=u0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.resource.v1.Resource;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{if(!(W.attributes&&W.attributes.length))W.attributes=[];W.attributes.push(X1.opentelemetry.proto.common.v1.KeyValue.decode(F,F.uint32()));break}case 2:{W.droppedAttributesCount=F.uint32();break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof u0))F=new u0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.attributes!=null&&F.hasOwnProperty("attributes")){if(!Array.isArray(F.attributes))return"attributes: array expected";for(var I=0;I<F.attributes.length;++I){var Y=X1.opentelemetry.proto.common.v1.KeyValue.verify(F.attributes[I]);if(Y)return"attributes."+Y}}if(F.droppedAttributesCount!=null&&F.hasOwnProperty("droppedAttributesCount")){if(!H1.isInteger(F.droppedAttributesCount))return"droppedAttributesCount: integer expected"}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.resource.v1.Resource)return F;var I=new X1.opentelemetry.proto.resource.v1.Resource;if(F.attributes){if(!Array.isArray(F.attributes))throw TypeError(".opentelemetry.proto.resource.v1.Resource.attributes: array expected");I.attributes=[];for(var Y=0;Y<F.attributes.length;++Y){if(typeof F.attributes[Y]!=="object")throw TypeError(".opentelemetry.proto.resource.v1.Resource.attributes: object expected");I.attributes[Y]=X1.opentelemetry.proto.common.v1.KeyValue.fromObject(F.attributes[Y])}}if(F.droppedAttributesCount!=null)I.droppedAttributesCount=F.droppedAttributesCount>>>0;return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.attributes=[];if(I.defaults)Y.droppedAttributesCount=0;if(F.attributes&&F.attributes.length){Y.attributes=[];for(var W=0;W<F.attributes.length;++W)Y.attributes[W]=X1.opentelemetry.proto.common.v1.KeyValue.toObject(F.attributes[W],I)}if(F.droppedAttributesCount!=null&&F.hasOwnProperty("droppedAttributesCount"))Y.droppedAttributesCount=F.droppedAttributesCount;return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,n9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.resource.v1.Resource"},Z}(),D}(),Q}(),B.trace=function(){var Q={};return Q.v1=function(){var D={};return D.TracesData=function(){function Z(G){if(this.resourceSpans=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.resourceSpans=H1.emptyArray,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=W4.create();if(F.resourceSpans!=null&&F.resourceSpans.length)for(var Y=0;Y<F.resourceSpans.length;++Y)X1.opentelemetry.proto.trace.v1.ResourceSpans.encode(F.resourceSpans[Y],I.uint32(10).fork()).ldelim();return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof u0))F=u0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.trace.v1.TracesData;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{if(!(W.resourceSpans&&W.resourceSpans.length))W.resourceSpans=[];W.resourceSpans.push(X1.opentelemetry.proto.trace.v1.ResourceSpans.decode(F,F.uint32()));break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof u0))F=new u0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.resourceSpans!=null&&F.hasOwnProperty("resourceSpans")){if(!Array.isArray(F.resourceSpans))return"resourceSpans: array expected";for(var I=0;I<F.resourceSpans.length;++I){var Y=X1.opentelemetry.proto.trace.v1.ResourceSpans.verify(F.resourceSpans[I]);if(Y)return"resourceSpans."+Y}}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.trace.v1.TracesData)return F;var I=new X1.opentelemetry.proto.trace.v1.TracesData;if(F.resourceSpans){if(!Array.isArray(F.resourceSpans))throw TypeError(".opentelemetry.proto.trace.v1.TracesData.resourceSpans: array expected");I.resourceSpans=[];for(var Y=0;Y<F.resourceSpans.length;++Y){if(typeof F.resourceSpans[Y]!=="object")throw TypeError(".opentelemetry.proto.trace.v1.TracesData.resourceSpans: object expected");I.resourceSpans[Y]=X1.opentelemetry.proto.trace.v1.ResourceSpans.fromObject(F.resourceSpans[Y])}}return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.resourceSpans=[];if(F.resourceSpans&&F.resourceSpans.length){Y.resourceSpans=[];for(var W=0;W<F.resourceSpans.length;++W)Y.resourceSpans[W]=X1.opentelemetry.proto.trace.v1.ResourceSpans.toObject(F.resourceSpans[W],I)}return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,n9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.trace.v1.TracesData"},Z}(),D.ResourceSpans=function(){function Z(G){if(this.scopeSpans=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.resource=null,Z.prototype.scopeSpans=H1.emptyArray,Z.prototype.schemaUrl=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=W4.create();if(F.resource!=null&&Object.hasOwnProperty.call(F,"resource"))X1.opentelemetry.proto.resource.v1.Resource.encode(F.resource,I.uint32(10).fork()).ldelim();if(F.scopeSpans!=null&&F.scopeSpans.length)for(var Y=0;Y<F.scopeSpans.length;++Y)X1.opentelemetry.proto.trace.v1.ScopeSpans.encode(F.scopeSpans[Y],I.uint32(18).fork()).ldelim();if(F.schemaUrl!=null&&Object.hasOwnProperty.call(F,"schemaUrl"))I.uint32(26).string(F.schemaUrl);return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof u0))F=u0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.trace.v1.ResourceSpans;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{W.resource=X1.opentelemetry.proto.resource.v1.Resource.decode(F,F.uint32());break}case 2:{if(!(W.scopeSpans&&W.scopeSpans.length))W.scopeSpans=[];W.scopeSpans.push(X1.opentelemetry.proto.trace.v1.ScopeSpans.decode(F,F.uint32()));break}case 3:{W.schemaUrl=F.string();break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof u0))F=new u0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.resource!=null&&F.hasOwnProperty("resource")){var I=X1.opentelemetry.proto.resource.v1.Resource.verify(F.resource);if(I)return"resource."+I}if(F.scopeSpans!=null&&F.hasOwnProperty("scopeSpans")){if(!Array.isArray(F.scopeSpans))return"scopeSpans: array expected";for(var Y=0;Y<F.scopeSpans.length;++Y){var I=X1.opentelemetry.proto.trace.v1.ScopeSpans.verify(F.scopeSpans[Y]);if(I)return"scopeSpans."+I}}if(F.schemaUrl!=null&&F.hasOwnProperty("schemaUrl")){if(!H1.isString(F.schemaUrl))return"schemaUrl: string expected"}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.trace.v1.ResourceSpans)return F;var I=new X1.opentelemetry.proto.trace.v1.ResourceSpans;if(F.resource!=null){if(typeof F.resource!=="object")throw TypeError(".opentelemetry.proto.trace.v1.ResourceSpans.resource: object expected");I.resource=X1.opentelemetry.proto.resource.v1.Resource.fromObject(F.resource)}if(F.scopeSpans){if(!Array.isArray(F.scopeSpans))throw TypeError(".opentelemetry.proto.trace.v1.ResourceSpans.scopeSpans: array expected");I.scopeSpans=[];for(var Y=0;Y<F.scopeSpans.length;++Y){if(typeof F.scopeSpans[Y]!=="object")throw TypeError(".opentelemetry.proto.trace.v1.ResourceSpans.scopeSpans: object expected");I.scopeSpans[Y]=X1.opentelemetry.proto.trace.v1.ScopeSpans.fromObject(F.scopeSpans[Y])}}if(F.schemaUrl!=null)I.schemaUrl=String(F.schemaUrl);return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.scopeSpans=[];if(I.defaults)Y.resource=null,Y.schemaUrl="";if(F.resource!=null&&F.hasOwnProperty("resource"))Y.resource=X1.opentelemetry.proto.resource.v1.Resource.toObject(F.resource,I);if(F.scopeSpans&&F.scopeSpans.length){Y.scopeSpans=[];for(var W=0;W<F.scopeSpans.length;++W)Y.scopeSpans[W]=X1.opentelemetry.proto.trace.v1.ScopeSpans.toObject(F.scopeSpans[W],I)}if(F.schemaUrl!=null&&F.hasOwnProperty("schemaUrl"))Y.schemaUrl=F.schemaUrl;return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,n9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.trace.v1.ResourceSpans"},Z}(),D.ScopeSpans=function(){function Z(G){if(this.spans=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.scope=null,Z.prototype.spans=H1.emptyArray,Z.prototype.schemaUrl=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=W4.create();if(F.scope!=null&&Object.hasOwnProperty.call(F,"scope"))X1.opentelemetry.proto.common.v1.InstrumentationScope.encode(F.scope,I.uint32(10).fork()).ldelim();if(F.spans!=null&&F.spans.length)for(var Y=0;Y<F.spans.length;++Y)X1.opentelemetry.proto.trace.v1.Span.encode(F.spans[Y],I.uint32(18).fork()).ldelim();if(F.schemaUrl!=null&&Object.hasOwnProperty.call(F,"schemaUrl"))I.uint32(26).string(F.schemaUrl);return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof u0))F=u0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.trace.v1.ScopeSpans;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{W.scope=X1.opentelemetry.proto.common.v1.InstrumentationScope.decode(F,F.uint32());break}case 2:{if(!(W.spans&&W.spans.length))W.spans=[];W.spans.push(X1.opentelemetry.proto.trace.v1.Span.decode(F,F.uint32()));break}case 3:{W.schemaUrl=F.string();break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof u0))F=new u0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.scope!=null&&F.hasOwnProperty("scope")){var I=X1.opentelemetry.proto.common.v1.InstrumentationScope.verify(F.scope);if(I)return"scope."+I}if(F.spans!=null&&F.hasOwnProperty("spans")){if(!Array.isArray(F.spans))return"spans: array expected";for(var Y=0;Y<F.spans.length;++Y){var I=X1.opentelemetry.proto.trace.v1.Span.verify(F.spans[Y]);if(I)return"spans."+I}}if(F.schemaUrl!=null&&F.hasOwnProperty("schemaUrl")){if(!H1.isString(F.schemaUrl))return"schemaUrl: string expected"}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.trace.v1.ScopeSpans)return F;var I=new X1.opentelemetry.proto.trace.v1.ScopeSpans;if(F.scope!=null){if(typeof F.scope!=="object")throw TypeError(".opentelemetry.proto.trace.v1.ScopeSpans.scope: object expected");I.scope=X1.opentelemetry.proto.common.v1.InstrumentationScope.fromObject(F.scope)}if(F.spans){if(!Array.isArray(F.spans))throw TypeError(".opentelemetry.proto.trace.v1.ScopeSpans.spans: array expected");I.spans=[];for(var Y=0;Y<F.spans.length;++Y){if(typeof F.spans[Y]!=="object")throw TypeError(".opentelemetry.proto.trace.v1.ScopeSpans.spans: object expected");I.spans[Y]=X1.opentelemetry.proto.trace.v1.Span.fromObject(F.spans[Y])}}if(F.schemaUrl!=null)I.schemaUrl=String(F.schemaUrl);return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.spans=[];if(I.defaults)Y.scope=null,Y.schemaUrl="";if(F.scope!=null&&F.hasOwnProperty("scope"))Y.scope=X1.opentelemetry.proto.common.v1.InstrumentationScope.toObject(F.scope,I);if(F.spans&&F.spans.length){Y.spans=[];for(var W=0;W<F.spans.length;++W)Y.spans[W]=X1.opentelemetry.proto.trace.v1.Span.toObject(F.spans[W],I)}if(F.schemaUrl!=null&&F.hasOwnProperty("schemaUrl"))Y.schemaUrl=F.schemaUrl;return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,n9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.trace.v1.ScopeSpans"},Z}(),D.Span=function(){function Z(G){if(this.attributes=[],this.events=[],this.links=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.traceId=null,Z.prototype.spanId=null,Z.prototype.traceState=null,Z.prototype.parentSpanId=null,Z.prototype.name=null,Z.prototype.kind=null,Z.prototype.startTimeUnixNano=null,Z.prototype.endTimeUnixNano=null,Z.prototype.attributes=H1.emptyArray,Z.prototype.droppedAttributesCount=null,Z.prototype.events=H1.emptyArray,Z.prototype.droppedEventsCount=null,Z.prototype.links=H1.emptyArray,Z.prototype.droppedLinksCount=null,Z.prototype.status=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=W4.create();if(F.traceId!=null&&Object.hasOwnProperty.call(F,"traceId"))I.uint32(10).bytes(F.traceId);if(F.spanId!=null&&Object.hasOwnProperty.call(F,"spanId"))I.uint32(18).bytes(F.spanId);if(F.traceState!=null&&Object.hasOwnProperty.call(F,"traceState"))I.uint32(26).string(F.traceState);if(F.parentSpanId!=null&&Object.hasOwnProperty.call(F,"parentSpanId"))I.uint32(34).bytes(F.parentSpanId);if(F.name!=null&&Object.hasOwnProperty.call(F,"name"))I.uint32(42).string(F.name);if(F.kind!=null&&Object.hasOwnProperty.call(F,"kind"))I.uint32(48).int32(F.kind);if(F.startTimeUnixNano!=null&&Object.hasOwnProperty.call(F,"startTimeUnixNano"))I.uint32(57).fixed64(F.startTimeUnixNano);if(F.endTimeUnixNano!=null&&Object.hasOwnProperty.call(F,"endTimeUnixNano"))I.uint32(65).fixed64(F.endTimeUnixNano);if(F.attributes!=null&&F.attributes.length)for(var Y=0;Y<F.attributes.length;++Y)X1.opentelemetry.proto.common.v1.KeyValue.encode(F.attributes[Y],I.uint32(74).fork()).ldelim();if(F.droppedAttributesCount!=null&&Object.hasOwnProperty.call(F,"droppedAttributesCount"))I.uint32(80).uint32(F.droppedAttributesCount);if(F.events!=null&&F.events.length)for(var Y=0;Y<F.events.length;++Y)X1.opentelemetry.proto.trace.v1.Span.Event.encode(F.events[Y],I.uint32(90).fork()).ldelim();if(F.droppedEventsCount!=null&&Object.hasOwnProperty.call(F,"droppedEventsCount"))I.uint32(96).uint32(F.droppedEventsCount);if(F.links!=null&&F.links.length)for(var Y=0;Y<F.links.length;++Y)X1.opentelemetry.proto.trace.v1.Span.Link.encode(F.links[Y],I.uint32(106).fork()).ldelim();if(F.droppedLinksCount!=null&&Object.hasOwnProperty.call(F,"droppedLinksCount"))I.uint32(112).uint32(F.droppedLinksCount);if(F.status!=null&&Object.hasOwnProperty.call(F,"status"))X1.opentelemetry.proto.trace.v1.Status.encode(F.status,I.uint32(122).fork()).ldelim();return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof u0))F=u0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.trace.v1.Span;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{W.traceId=F.bytes();break}case 2:{W.spanId=F.bytes();break}case 3:{W.traceState=F.string();break}case 4:{W.parentSpanId=F.bytes();break}case 5:{W.name=F.string();break}case 6:{W.kind=F.int32();break}case 7:{W.startTimeUnixNano=F.fixed64();break}case 8:{W.endTimeUnixNano=F.fixed64();break}case 9:{if(!(W.attributes&&W.attributes.length))W.attributes=[];W.attributes.push(X1.opentelemetry.proto.common.v1.KeyValue.decode(F,F.uint32()));break}case 10:{W.droppedAttributesCount=F.uint32();break}case 11:{if(!(W.events&&W.events.length))W.events=[];W.events.push(X1.opentelemetry.proto.trace.v1.Span.Event.decode(F,F.uint32()));break}case 12:{W.droppedEventsCount=F.uint32();break}case 13:{if(!(W.links&&W.links.length))W.links=[];W.links.push(X1.opentelemetry.proto.trace.v1.Span.Link.decode(F,F.uint32()));break}case 14:{W.droppedLinksCount=F.uint32();break}case 15:{W.status=X1.opentelemetry.proto.trace.v1.Status.decode(F,F.uint32());break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof u0))F=new u0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.traceId!=null&&F.hasOwnProperty("traceId")){if(!(F.traceId&&typeof F.traceId.length==="number"||H1.isString(F.traceId)))return"traceId: buffer expected"}if(F.spanId!=null&&F.hasOwnProperty("spanId")){if(!(F.spanId&&typeof F.spanId.length==="number"||H1.isString(F.spanId)))return"spanId: buffer expected"}if(F.traceState!=null&&F.hasOwnProperty("traceState")){if(!H1.isString(F.traceState))return"traceState: string expected"}if(F.parentSpanId!=null&&F.hasOwnProperty("parentSpanId")){if(!(F.parentSpanId&&typeof F.parentSpanId.length==="number"||H1.isString(F.parentSpanId)))return"parentSpanId: buffer expected"}if(F.name!=null&&F.hasOwnProperty("name")){if(!H1.isString(F.name))return"name: string expected"}if(F.kind!=null&&F.hasOwnProperty("kind"))switch(F.kind){default:return"kind: enum value expected";case 0:case 1:case 2:case 3:case 4:case 5:break}if(F.startTimeUnixNano!=null&&F.hasOwnProperty("startTimeUnixNano")){if(!H1.isInteger(F.startTimeUnixNano)&&!(F.startTimeUnixNano&&H1.isInteger(F.startTimeUnixNano.low)&&H1.isInteger(F.startTimeUnixNano.high)))return"startTimeUnixNano: integer|Long expected"}if(F.endTimeUnixNano!=null&&F.hasOwnProperty("endTimeUnixNano")){if(!H1.isInteger(F.endTimeUnixNano)&&!(F.endTimeUnixNano&&H1.isInteger(F.endTimeUnixNano.low)&&H1.isInteger(F.endTimeUnixNano.high)))return"endTimeUnixNano: integer|Long expected"}if(F.attributes!=null&&F.hasOwnProperty("attributes")){if(!Array.isArray(F.attributes))return"attributes: array expected";for(var I=0;I<F.attributes.length;++I){var Y=X1.opentelemetry.proto.common.v1.KeyValue.verify(F.attributes[I]);if(Y)return"attributes."+Y}}if(F.droppedAttributesCount!=null&&F.hasOwnProperty("droppedAttributesCount")){if(!H1.isInteger(F.droppedAttributesCount))return"droppedAttributesCount: integer expected"}if(F.events!=null&&F.hasOwnProperty("events")){if(!Array.isArray(F.events))return"events: array expected";for(var I=0;I<F.events.length;++I){var Y=X1.opentelemetry.proto.trace.v1.Span.Event.verify(F.events[I]);if(Y)return"events."+Y}}if(F.droppedEventsCount!=null&&F.hasOwnProperty("droppedEventsCount")){if(!H1.isInteger(F.droppedEventsCount))return"droppedEventsCount: integer expected"}if(F.links!=null&&F.hasOwnProperty("links")){if(!Array.isArray(F.links))return"links: array expected";for(var I=0;I<F.links.length;++I){var Y=X1.opentelemetry.proto.trace.v1.Span.Link.verify(F.links[I]);if(Y)return"links."+Y}}if(F.droppedLinksCount!=null&&F.hasOwnProperty("droppedLinksCount")){if(!H1.isInteger(F.droppedLinksCount))return"droppedLinksCount: integer expected"}if(F.status!=null&&F.hasOwnProperty("status")){var Y=X1.opentelemetry.proto.trace.v1.Status.verify(F.status);if(Y)return"status."+Y}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.trace.v1.Span)return F;var I=new X1.opentelemetry.proto.trace.v1.Span;if(F.traceId!=null){if(typeof F.traceId==="string")H1.base64.decode(F.traceId,I.traceId=H1.newBuffer(H1.base64.length(F.traceId)),0);else if(F.traceId.length>=0)I.traceId=F.traceId}if(F.spanId!=null){if(typeof F.spanId==="string")H1.base64.decode(F.spanId,I.spanId=H1.newBuffer(H1.base64.length(F.spanId)),0);else if(F.spanId.length>=0)I.spanId=F.spanId}if(F.traceState!=null)I.traceState=String(F.traceState);if(F.parentSpanId!=null){if(typeof F.parentSpanId==="string")H1.base64.decode(F.parentSpanId,I.parentSpanId=H1.newBuffer(H1.base64.length(F.parentSpanId)),0);else if(F.parentSpanId.length>=0)I.parentSpanId=F.parentSpanId}if(F.name!=null)I.name=String(F.name);switch(F.kind){default:if(typeof F.kind==="number"){I.kind=F.kind;break}break;case"SPAN_KIND_UNSPECIFIED":case 0:I.kind=0;break;case"SPAN_KIND_INTERNAL":case 1:I.kind=1;break;case"SPAN_KIND_SERVER":case 2:I.kind=2;break;case"SPAN_KIND_CLIENT":case 3:I.kind=3;break;case"SPAN_KIND_PRODUCER":case 4:I.kind=4;break;case"SPAN_KIND_CONSUMER":case 5:I.kind=5;break}if(F.startTimeUnixNano!=null){if(H1.Long)(I.startTimeUnixNano=H1.Long.fromValue(F.startTimeUnixNano)).unsigned=!1;else if(typeof F.startTimeUnixNano==="string")I.startTimeUnixNano=parseInt(F.startTimeUnixNano,10);else if(typeof F.startTimeUnixNano==="number")I.startTimeUnixNano=F.startTimeUnixNano;else if(typeof F.startTimeUnixNano==="object")I.startTimeUnixNano=new H1.LongBits(F.startTimeUnixNano.low>>>0,F.startTimeUnixNano.high>>>0).toNumber()}if(F.endTimeUnixNano!=null){if(H1.Long)(I.endTimeUnixNano=H1.Long.fromValue(F.endTimeUnixNano)).unsigned=!1;else if(typeof F.endTimeUnixNano==="string")I.endTimeUnixNano=parseInt(F.endTimeUnixNano,10);else if(typeof F.endTimeUnixNano==="number")I.endTimeUnixNano=F.endTimeUnixNano;else if(typeof F.endTimeUnixNano==="object")I.endTimeUnixNano=new H1.LongBits(F.endTimeUnixNano.low>>>0,F.endTimeUnixNano.high>>>0).toNumber()}if(F.attributes){if(!Array.isArray(F.attributes))throw TypeError(".opentelemetry.proto.trace.v1.Span.attributes: array expected");I.attributes=[];for(var Y=0;Y<F.attributes.length;++Y){if(typeof F.attributes[Y]!=="object")throw TypeError(".opentelemetry.proto.trace.v1.Span.attributes: object expected");I.attributes[Y]=X1.opentelemetry.proto.common.v1.KeyValue.fromObject(F.attributes[Y])}}if(F.droppedAttributesCount!=null)I.droppedAttributesCount=F.droppedAttributesCount>>>0;if(F.events){if(!Array.isArray(F.events))throw TypeError(".opentelemetry.proto.trace.v1.Span.events: array expected");I.events=[];for(var Y=0;Y<F.events.length;++Y){if(typeof F.events[Y]!=="object")throw TypeError(".opentelemetry.proto.trace.v1.Span.events: object expected");I.events[Y]=X1.opentelemetry.proto.trace.v1.Span.Event.fromObject(F.events[Y])}}if(F.droppedEventsCount!=null)I.droppedEventsCount=F.droppedEventsCount>>>0;if(F.links){if(!Array.isArray(F.links))throw TypeError(".opentelemetry.proto.trace.v1.Span.links: array expected");I.links=[];for(var Y=0;Y<F.links.length;++Y){if(typeof F.links[Y]!=="object")throw TypeError(".opentelemetry.proto.trace.v1.Span.links: object expected");I.links[Y]=X1.opentelemetry.proto.trace.v1.Span.Link.fromObject(F.links[Y])}}if(F.droppedLinksCount!=null)I.droppedLinksCount=F.droppedLinksCount>>>0;if(F.status!=null){if(typeof F.status!=="object")throw TypeError(".opentelemetry.proto.trace.v1.Span.status: object expected");I.status=X1.opentelemetry.proto.trace.v1.Status.fromObject(F.status)}return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.attributes=[],Y.events=[],Y.links=[];if(I.defaults){if(I.bytes===String)Y.traceId="";else if(Y.traceId=[],I.bytes!==Array)Y.traceId=H1.newBuffer(Y.traceId);if(I.bytes===String)Y.spanId="";else if(Y.spanId=[],I.bytes!==Array)Y.spanId=H1.newBuffer(Y.spanId);if(Y.traceState="",I.bytes===String)Y.parentSpanId="";else if(Y.parentSpanId=[],I.bytes!==Array)Y.parentSpanId=H1.newBuffer(Y.parentSpanId);if(Y.name="",Y.kind=I.enums===String?"SPAN_KIND_UNSPECIFIED":0,H1.Long){var W=new H1.Long(0,0,!1);Y.startTimeUnixNano=I.longs===String?W.toString():I.longs===Number?W.toNumber():W}else Y.startTimeUnixNano=I.longs===String?"0":0;if(H1.Long){var W=new H1.Long(0,0,!1);Y.endTimeUnixNano=I.longs===String?W.toString():I.longs===Number?W.toNumber():W}else Y.endTimeUnixNano=I.longs===String?"0":0;Y.droppedAttributesCount=0,Y.droppedEventsCount=0,Y.droppedLinksCount=0,Y.status=null}if(F.traceId!=null&&F.hasOwnProperty("traceId"))Y.traceId=I.bytes===String?H1.base64.encode(F.traceId,0,F.traceId.length):I.bytes===Array?Array.prototype.slice.call(F.traceId):F.traceId;if(F.spanId!=null&&F.hasOwnProperty("spanId"))Y.spanId=I.bytes===String?H1.base64.encode(F.spanId,0,F.spanId.length):I.bytes===Array?Array.prototype.slice.call(F.spanId):F.spanId;if(F.traceState!=null&&F.hasOwnProperty("traceState"))Y.traceState=F.traceState;if(F.parentSpanId!=null&&F.hasOwnProperty("parentSpanId"))Y.parentSpanId=I.bytes===String?H1.base64.encode(F.parentSpanId,0,F.parentSpanId.length):I.bytes===Array?Array.prototype.slice.call(F.parentSpanId):F.parentSpanId;if(F.name!=null&&F.hasOwnProperty("name"))Y.name=F.name;if(F.kind!=null&&F.hasOwnProperty("kind"))Y.kind=I.enums===String?X1.opentelemetry.proto.trace.v1.Span.SpanKind[F.kind]===void 0?F.kind:X1.opentelemetry.proto.trace.v1.Span.SpanKind[F.kind]:F.kind;if(F.startTimeUnixNano!=null&&F.hasOwnProperty("startTimeUnixNano"))if(typeof F.startTimeUnixNano==="number")Y.startTimeUnixNano=I.longs===String?String(F.startTimeUnixNano):F.startTimeUnixNano;else Y.startTimeUnixNano=I.longs===String?H1.Long.prototype.toString.call(F.startTimeUnixNano):I.longs===Number?new H1.LongBits(F.startTimeUnixNano.low>>>0,F.startTimeUnixNano.high>>>0).toNumber():F.startTimeUnixNano;if(F.endTimeUnixNano!=null&&F.hasOwnProperty("endTimeUnixNano"))if(typeof F.endTimeUnixNano==="number")Y.endTimeUnixNano=I.longs===String?String(F.endTimeUnixNano):F.endTimeUnixNano;else Y.endTimeUnixNano=I.longs===String?H1.Long.prototype.toString.call(F.endTimeUnixNano):I.longs===Number?new H1.LongBits(F.endTimeUnixNano.low>>>0,F.endTimeUnixNano.high>>>0).toNumber():F.endTimeUnixNano;if(F.attributes&&F.attributes.length){Y.attributes=[];for(var J=0;J<F.attributes.length;++J)Y.attributes[J]=X1.opentelemetry.proto.common.v1.KeyValue.toObject(F.attributes[J],I)}if(F.droppedAttributesCount!=null&&F.hasOwnProperty("droppedAttributesCount"))Y.droppedAttributesCount=F.droppedAttributesCount;if(F.events&&F.events.length){Y.events=[];for(var J=0;J<F.events.length;++J)Y.events[J]=X1.opentelemetry.proto.trace.v1.Span.Event.toObject(F.events[J],I)}if(F.droppedEventsCount!=null&&F.hasOwnProperty("droppedEventsCount"))Y.droppedEventsCount=F.droppedEventsCount;if(F.links&&F.links.length){Y.links=[];for(var J=0;J<F.links.length;++J)Y.links[J]=X1.opentelemetry.proto.trace.v1.Span.Link.toObject(F.links[J],I)}if(F.droppedLinksCount!=null&&F.hasOwnProperty("droppedLinksCount"))Y.droppedLinksCount=F.droppedLinksCount;if(F.status!=null&&F.hasOwnProperty("status"))Y.status=X1.opentelemetry.proto.trace.v1.Status.toObject(F.status,I);return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,n9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.trace.v1.Span"},Z.SpanKind=function(){var G={},F=Object.create(G);return F[G[0]="SPAN_KIND_UNSPECIFIED"]=0,F[G[1]="SPAN_KIND_INTERNAL"]=1,F[G[2]="SPAN_KIND_SERVER"]=2,F[G[3]="SPAN_KIND_CLIENT"]=3,F[G[4]="SPAN_KIND_PRODUCER"]=4,F[G[5]="SPAN_KIND_CONSUMER"]=5,F}(),Z.Event=function(){function G(F){if(this.attributes=[],F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}return G.prototype.timeUnixNano=null,G.prototype.name=null,G.prototype.attributes=H1.emptyArray,G.prototype.droppedAttributesCount=null,G.create=function F(I){return new G(I)},G.encode=function F(I,Y){if(!Y)Y=W4.create();if(I.timeUnixNano!=null&&Object.hasOwnProperty.call(I,"timeUnixNano"))Y.uint32(9).fixed64(I.timeUnixNano);if(I.name!=null&&Object.hasOwnProperty.call(I,"name"))Y.uint32(18).string(I.name);if(I.attributes!=null&&I.attributes.length)for(var W=0;W<I.attributes.length;++W)X1.opentelemetry.proto.common.v1.KeyValue.encode(I.attributes[W],Y.uint32(26).fork()).ldelim();if(I.droppedAttributesCount!=null&&Object.hasOwnProperty.call(I,"droppedAttributesCount"))Y.uint32(32).uint32(I.droppedAttributesCount);return Y},G.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},G.decode=function F(I,Y){if(!(I instanceof u0))I=u0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.trace.v1.Span.Event;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 1:{J.timeUnixNano=I.fixed64();break}case 2:{J.name=I.string();break}case 3:{if(!(J.attributes&&J.attributes.length))J.attributes=[];J.attributes.push(X1.opentelemetry.proto.common.v1.KeyValue.decode(I,I.uint32()));break}case 4:{J.droppedAttributesCount=I.uint32();break}default:I.skipType(X&7);break}}return J},G.decodeDelimited=function F(I){if(!(I instanceof u0))I=new u0(I);return this.decode(I,I.uint32())},G.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";if(I.timeUnixNano!=null&&I.hasOwnProperty("timeUnixNano")){if(!H1.isInteger(I.timeUnixNano)&&!(I.timeUnixNano&&H1.isInteger(I.timeUnixNano.low)&&H1.isInteger(I.timeUnixNano.high)))return"timeUnixNano: integer|Long expected"}if(I.name!=null&&I.hasOwnProperty("name")){if(!H1.isString(I.name))return"name: string expected"}if(I.attributes!=null&&I.hasOwnProperty("attributes")){if(!Array.isArray(I.attributes))return"attributes: array expected";for(var Y=0;Y<I.attributes.length;++Y){var W=X1.opentelemetry.proto.common.v1.KeyValue.verify(I.attributes[Y]);if(W)return"attributes."+W}}if(I.droppedAttributesCount!=null&&I.hasOwnProperty("droppedAttributesCount")){if(!H1.isInteger(I.droppedAttributesCount))return"droppedAttributesCount: integer expected"}return null},G.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.trace.v1.Span.Event)return I;var Y=new X1.opentelemetry.proto.trace.v1.Span.Event;if(I.timeUnixNano!=null){if(H1.Long)(Y.timeUnixNano=H1.Long.fromValue(I.timeUnixNano)).unsigned=!1;else if(typeof I.timeUnixNano==="string")Y.timeUnixNano=parseInt(I.timeUnixNano,10);else if(typeof I.timeUnixNano==="number")Y.timeUnixNano=I.timeUnixNano;else if(typeof I.timeUnixNano==="object")Y.timeUnixNano=new H1.LongBits(I.timeUnixNano.low>>>0,I.timeUnixNano.high>>>0).toNumber()}if(I.name!=null)Y.name=String(I.name);if(I.attributes){if(!Array.isArray(I.attributes))throw TypeError(".opentelemetry.proto.trace.v1.Span.Event.attributes: array expected");Y.attributes=[];for(var W=0;W<I.attributes.length;++W){if(typeof I.attributes[W]!=="object")throw TypeError(".opentelemetry.proto.trace.v1.Span.Event.attributes: object expected");Y.attributes[W]=X1.opentelemetry.proto.common.v1.KeyValue.fromObject(I.attributes[W])}}if(I.droppedAttributesCount!=null)Y.droppedAttributesCount=I.droppedAttributesCount>>>0;return Y},G.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.arrays||Y.defaults)W.attributes=[];if(Y.defaults){if(H1.Long){var J=new H1.Long(0,0,!1);W.timeUnixNano=Y.longs===String?J.toString():Y.longs===Number?J.toNumber():J}else W.timeUnixNano=Y.longs===String?"0":0;W.name="",W.droppedAttributesCount=0}if(I.timeUnixNano!=null&&I.hasOwnProperty("timeUnixNano"))if(typeof I.timeUnixNano==="number")W.timeUnixNano=Y.longs===String?String(I.timeUnixNano):I.timeUnixNano;else W.timeUnixNano=Y.longs===String?H1.Long.prototype.toString.call(I.timeUnixNano):Y.longs===Number?new H1.LongBits(I.timeUnixNano.low>>>0,I.timeUnixNano.high>>>0).toNumber():I.timeUnixNano;if(I.name!=null&&I.hasOwnProperty("name"))W.name=I.name;if(I.attributes&&I.attributes.length){W.attributes=[];for(var X=0;X<I.attributes.length;++X)W.attributes[X]=X1.opentelemetry.proto.common.v1.KeyValue.toObject(I.attributes[X],Y)}if(I.droppedAttributesCount!=null&&I.hasOwnProperty("droppedAttributesCount"))W.droppedAttributesCount=I.droppedAttributesCount;return W},G.prototype.toJSON=function F(){return this.constructor.toObject(this,n9.util.toJSONOptions)},G.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.trace.v1.Span.Event"},G}(),Z.Link=function(){function G(F){if(this.attributes=[],F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}return G.prototype.traceId=null,G.prototype.spanId=null,G.prototype.traceState=null,G.prototype.attributes=H1.emptyArray,G.prototype.droppedAttributesCount=null,G.create=function F(I){return new G(I)},G.encode=function F(I,Y){if(!Y)Y=W4.create();if(I.traceId!=null&&Object.hasOwnProperty.call(I,"traceId"))Y.uint32(10).bytes(I.traceId);if(I.spanId!=null&&Object.hasOwnProperty.call(I,"spanId"))Y.uint32(18).bytes(I.spanId);if(I.traceState!=null&&Object.hasOwnProperty.call(I,"traceState"))Y.uint32(26).string(I.traceState);if(I.attributes!=null&&I.attributes.length)for(var W=0;W<I.attributes.length;++W)X1.opentelemetry.proto.common.v1.KeyValue.encode(I.attributes[W],Y.uint32(34).fork()).ldelim();if(I.droppedAttributesCount!=null&&Object.hasOwnProperty.call(I,"droppedAttributesCount"))Y.uint32(40).uint32(I.droppedAttributesCount);return Y},G.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},G.decode=function F(I,Y){if(!(I instanceof u0))I=u0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.trace.v1.Span.Link;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 1:{J.traceId=I.bytes();break}case 2:{J.spanId=I.bytes();break}case 3:{J.traceState=I.string();break}case 4:{if(!(J.attributes&&J.attributes.length))J.attributes=[];J.attributes.push(X1.opentelemetry.proto.common.v1.KeyValue.decode(I,I.uint32()));break}case 5:{J.droppedAttributesCount=I.uint32();break}default:I.skipType(X&7);break}}return J},G.decodeDelimited=function F(I){if(!(I instanceof u0))I=new u0(I);return this.decode(I,I.uint32())},G.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";if(I.traceId!=null&&I.hasOwnProperty("traceId")){if(!(I.traceId&&typeof I.traceId.length==="number"||H1.isString(I.traceId)))return"traceId: buffer expected"}if(I.spanId!=null&&I.hasOwnProperty("spanId")){if(!(I.spanId&&typeof I.spanId.length==="number"||H1.isString(I.spanId)))return"spanId: buffer expected"}if(I.traceState!=null&&I.hasOwnProperty("traceState")){if(!H1.isString(I.traceState))return"traceState: string expected"}if(I.attributes!=null&&I.hasOwnProperty("attributes")){if(!Array.isArray(I.attributes))return"attributes: array expected";for(var Y=0;Y<I.attributes.length;++Y){var W=X1.opentelemetry.proto.common.v1.KeyValue.verify(I.attributes[Y]);if(W)return"attributes."+W}}if(I.droppedAttributesCount!=null&&I.hasOwnProperty("droppedAttributesCount")){if(!H1.isInteger(I.droppedAttributesCount))return"droppedAttributesCount: integer expected"}return null},G.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.trace.v1.Span.Link)return I;var Y=new X1.opentelemetry.proto.trace.v1.Span.Link;if(I.traceId!=null){if(typeof I.traceId==="string")H1.base64.decode(I.traceId,Y.traceId=H1.newBuffer(H1.base64.length(I.traceId)),0);else if(I.traceId.length>=0)Y.traceId=I.traceId}if(I.spanId!=null){if(typeof I.spanId==="string")H1.base64.decode(I.spanId,Y.spanId=H1.newBuffer(H1.base64.length(I.spanId)),0);else if(I.spanId.length>=0)Y.spanId=I.spanId}if(I.traceState!=null)Y.traceState=String(I.traceState);if(I.attributes){if(!Array.isArray(I.attributes))throw TypeError(".opentelemetry.proto.trace.v1.Span.Link.attributes: array expected");Y.attributes=[];for(var W=0;W<I.attributes.length;++W){if(typeof I.attributes[W]!=="object")throw TypeError(".opentelemetry.proto.trace.v1.Span.Link.attributes: object expected");Y.attributes[W]=X1.opentelemetry.proto.common.v1.KeyValue.fromObject(I.attributes[W])}}if(I.droppedAttributesCount!=null)Y.droppedAttributesCount=I.droppedAttributesCount>>>0;return Y},G.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.arrays||Y.defaults)W.attributes=[];if(Y.defaults){if(Y.bytes===String)W.traceId="";else if(W.traceId=[],Y.bytes!==Array)W.traceId=H1.newBuffer(W.traceId);if(Y.bytes===String)W.spanId="";else if(W.spanId=[],Y.bytes!==Array)W.spanId=H1.newBuffer(W.spanId);W.traceState="",W.droppedAttributesCount=0}if(I.traceId!=null&&I.hasOwnProperty("traceId"))W.traceId=Y.bytes===String?H1.base64.encode(I.traceId,0,I.traceId.length):Y.bytes===Array?Array.prototype.slice.call(I.traceId):I.traceId;if(I.spanId!=null&&I.hasOwnProperty("spanId"))W.spanId=Y.bytes===String?H1.base64.encode(I.spanId,0,I.spanId.length):Y.bytes===Array?Array.prototype.slice.call(I.spanId):I.spanId;if(I.traceState!=null&&I.hasOwnProperty("traceState"))W.traceState=I.traceState;if(I.attributes&&I.attributes.length){W.attributes=[];for(var J=0;J<I.attributes.length;++J)W.attributes[J]=X1.opentelemetry.proto.common.v1.KeyValue.toObject(I.attributes[J],Y)}if(I.droppedAttributesCount!=null&&I.hasOwnProperty("droppedAttributesCount"))W.droppedAttributesCount=I.droppedAttributesCount;return W},G.prototype.toJSON=function F(){return this.constructor.toObject(this,n9.util.toJSONOptions)},G.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.trace.v1.Span.Link"},G}(),Z}(),D.Status=function(){function Z(G){if(G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.message=null,Z.prototype.code=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=W4.create();if(F.message!=null&&Object.hasOwnProperty.call(F,"message"))I.uint32(18).string(F.message);if(F.code!=null&&Object.hasOwnProperty.call(F,"code"))I.uint32(24).int32(F.code);return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof u0))F=u0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.trace.v1.Status;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 2:{W.message=F.string();break}case 3:{W.code=F.int32();break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof u0))F=new u0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.message!=null&&F.hasOwnProperty("message")){if(!H1.isString(F.message))return"message: string expected"}if(F.code!=null&&F.hasOwnProperty("code"))switch(F.code){default:return"code: enum value expected";case 0:case 1:case 2:break}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.trace.v1.Status)return F;var I=new X1.opentelemetry.proto.trace.v1.Status;if(F.message!=null)I.message=String(F.message);switch(F.code){default:if(typeof F.code==="number"){I.code=F.code;break}break;case"STATUS_CODE_UNSET":case 0:I.code=0;break;case"STATUS_CODE_OK":case 1:I.code=1;break;case"STATUS_CODE_ERROR":case 2:I.code=2;break}return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.defaults)Y.message="",Y.code=I.enums===String?"STATUS_CODE_UNSET":0;if(F.message!=null&&F.hasOwnProperty("message"))Y.message=F.message;if(F.code!=null&&F.hasOwnProperty("code"))Y.code=I.enums===String?X1.opentelemetry.proto.trace.v1.Status.StatusCode[F.code]===void 0?F.code:X1.opentelemetry.proto.trace.v1.Status.StatusCode[F.code]:F.code;return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,n9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.trace.v1.Status"},Z.StatusCode=function(){var G={},F=Object.create(G);return F[G[0]="STATUS_CODE_UNSET"]=0,F[G[1]="STATUS_CODE_OK"]=1,F[G[2]="STATUS_CODE_ERROR"]=2,F}(),Z}(),D}(),Q}(),B.collector=function(){var Q={};return Q.trace=function(){var D={};return D.v1=function(){var Z={};return Z.TraceService=function(){function G(F,I,Y){n9.rpc.Service.call(this,F,I,Y)}return(G.prototype=Object.create(n9.rpc.Service.prototype)).constructor=G,G.create=function F(I,Y,W){return new this(I,Y,W)},Object.defineProperty(G.prototype.export=function F(I,Y){return this.rpcCall(F,X1.opentelemetry.proto.collector.trace.v1.ExportTraceServiceRequest,X1.opentelemetry.proto.collector.trace.v1.ExportTraceServiceResponse,I,Y)},"name",{value:"Export"}),G}(),Z.ExportTraceServiceRequest=function(){function G(F){if(this.resourceSpans=[],F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}return G.prototype.resourceSpans=H1.emptyArray,G.create=function F(I){return new G(I)},G.encode=function F(I,Y){if(!Y)Y=W4.create();if(I.resourceSpans!=null&&I.resourceSpans.length)for(var W=0;W<I.resourceSpans.length;++W)X1.opentelemetry.proto.trace.v1.ResourceSpans.encode(I.resourceSpans[W],Y.uint32(10).fork()).ldelim();return Y},G.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},G.decode=function F(I,Y){if(!(I instanceof u0))I=u0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.collector.trace.v1.ExportTraceServiceRequest;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 1:{if(!(J.resourceSpans&&J.resourceSpans.length))J.resourceSpans=[];J.resourceSpans.push(X1.opentelemetry.proto.trace.v1.ResourceSpans.decode(I,I.uint32()));break}default:I.skipType(X&7);break}}return J},G.decodeDelimited=function F(I){if(!(I instanceof u0))I=new u0(I);return this.decode(I,I.uint32())},G.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";if(I.resourceSpans!=null&&I.hasOwnProperty("resourceSpans")){if(!Array.isArray(I.resourceSpans))return"resourceSpans: array expected";for(var Y=0;Y<I.resourceSpans.length;++Y){var W=X1.opentelemetry.proto.trace.v1.ResourceSpans.verify(I.resourceSpans[Y]);if(W)return"resourceSpans."+W}}return null},G.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.collector.trace.v1.ExportTraceServiceRequest)return I;var Y=new X1.opentelemetry.proto.collector.trace.v1.ExportTraceServiceRequest;if(I.resourceSpans){if(!Array.isArray(I.resourceSpans))throw TypeError(".opentelemetry.proto.collector.trace.v1.ExportTraceServiceRequest.resourceSpans: array expected");Y.resourceSpans=[];for(var W=0;W<I.resourceSpans.length;++W){if(typeof I.resourceSpans[W]!=="object")throw TypeError(".opentelemetry.proto.collector.trace.v1.ExportTraceServiceRequest.resourceSpans: object expected");Y.resourceSpans[W]=X1.opentelemetry.proto.trace.v1.ResourceSpans.fromObject(I.resourceSpans[W])}}return Y},G.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.arrays||Y.defaults)W.resourceSpans=[];if(I.resourceSpans&&I.resourceSpans.length){W.resourceSpans=[];for(var J=0;J<I.resourceSpans.length;++J)W.resourceSpans[J]=X1.opentelemetry.proto.trace.v1.ResourceSpans.toObject(I.resourceSpans[J],Y)}return W},G.prototype.toJSON=function F(){return this.constructor.toObject(this,n9.util.toJSONOptions)},G.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.collector.trace.v1.ExportTraceServiceRequest"},G}(),Z.ExportTraceServiceResponse=function(){function G(F){if(F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}return G.prototype.partialSuccess=null,G.create=function F(I){return new G(I)},G.encode=function F(I,Y){if(!Y)Y=W4.create();if(I.partialSuccess!=null&&Object.hasOwnProperty.call(I,"partialSuccess"))X1.opentelemetry.proto.collector.trace.v1.ExportTracePartialSuccess.encode(I.partialSuccess,Y.uint32(10).fork()).ldelim();return Y},G.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},G.decode=function F(I,Y){if(!(I instanceof u0))I=u0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.collector.trace.v1.ExportTraceServiceResponse;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 1:{J.partialSuccess=X1.opentelemetry.proto.collector.trace.v1.ExportTracePartialSuccess.decode(I,I.uint32());break}default:I.skipType(X&7);break}}return J},G.decodeDelimited=function F(I){if(!(I instanceof u0))I=new u0(I);return this.decode(I,I.uint32())},G.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";if(I.partialSuccess!=null&&I.hasOwnProperty("partialSuccess")){var Y=X1.opentelemetry.proto.collector.trace.v1.ExportTracePartialSuccess.verify(I.partialSuccess);if(Y)return"partialSuccess."+Y}return null},G.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.collector.trace.v1.ExportTraceServiceResponse)return I;var Y=new X1.opentelemetry.proto.collector.trace.v1.ExportTraceServiceResponse;if(I.partialSuccess!=null){if(typeof I.partialSuccess!=="object")throw TypeError(".opentelemetry.proto.collector.trace.v1.ExportTraceServiceResponse.partialSuccess: object expected");Y.partialSuccess=X1.opentelemetry.proto.collector.trace.v1.ExportTracePartialSuccess.fromObject(I.partialSuccess)}return Y},G.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.defaults)W.partialSuccess=null;if(I.partialSuccess!=null&&I.hasOwnProperty("partialSuccess"))W.partialSuccess=X1.opentelemetry.proto.collector.trace.v1.ExportTracePartialSuccess.toObject(I.partialSuccess,Y);return W},G.prototype.toJSON=function F(){return this.constructor.toObject(this,n9.util.toJSONOptions)},G.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.collector.trace.v1.ExportTraceServiceResponse"},G}(),Z.ExportTracePartialSuccess=function(){function G(F){if(F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}return G.prototype.rejectedSpans=null,G.prototype.errorMessage=null,G.create=function F(I){return new G(I)},G.encode=function F(I,Y){if(!Y)Y=W4.create();if(I.rejectedSpans!=null&&Object.hasOwnProperty.call(I,"rejectedSpans"))Y.uint32(8).int64(I.rejectedSpans);if(I.errorMessage!=null&&Object.hasOwnProperty.call(I,"errorMessage"))Y.uint32(18).string(I.errorMessage);return Y},G.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},G.decode=function F(I,Y){if(!(I instanceof u0))I=u0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.collector.trace.v1.ExportTracePartialSuccess;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 1:{J.rejectedSpans=I.int64();break}case 2:{J.errorMessage=I.string();break}default:I.skipType(X&7);break}}return J},G.decodeDelimited=function F(I){if(!(I instanceof u0))I=new u0(I);return this.decode(I,I.uint32())},G.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";if(I.rejectedSpans!=null&&I.hasOwnProperty("rejectedSpans")){if(!H1.isInteger(I.rejectedSpans)&&!(I.rejectedSpans&&H1.isInteger(I.rejectedSpans.low)&&H1.isInteger(I.rejectedSpans.high)))return"rejectedSpans: integer|Long expected"}if(I.errorMessage!=null&&I.hasOwnProperty("errorMessage")){if(!H1.isString(I.errorMessage))return"errorMessage: string expected"}return null},G.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.collector.trace.v1.ExportTracePartialSuccess)return I;var Y=new X1.opentelemetry.proto.collector.trace.v1.ExportTracePartialSuccess;if(I.rejectedSpans!=null){if(H1.Long)(Y.rejectedSpans=H1.Long.fromValue(I.rejectedSpans)).unsigned=!1;else if(typeof I.rejectedSpans==="string")Y.rejectedSpans=parseInt(I.rejectedSpans,10);else if(typeof I.rejectedSpans==="number")Y.rejectedSpans=I.rejectedSpans;else if(typeof I.rejectedSpans==="object")Y.rejectedSpans=new H1.LongBits(I.rejectedSpans.low>>>0,I.rejectedSpans.high>>>0).toNumber()}if(I.errorMessage!=null)Y.errorMessage=String(I.errorMessage);return Y},G.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.defaults){if(H1.Long){var J=new H1.Long(0,0,!1);W.rejectedSpans=Y.longs===String?J.toString():Y.longs===Number?J.toNumber():J}else W.rejectedSpans=Y.longs===String?"0":0;W.errorMessage=""}if(I.rejectedSpans!=null&&I.hasOwnProperty("rejectedSpans"))if(typeof I.rejectedSpans==="number")W.rejectedSpans=Y.longs===String?String(I.rejectedSpans):I.rejectedSpans;else W.rejectedSpans=Y.longs===String?H1.Long.prototype.toString.call(I.rejectedSpans):Y.longs===Number?new H1.LongBits(I.rejectedSpans.low>>>0,I.rejectedSpans.high>>>0).toNumber():I.rejectedSpans;if(I.errorMessage!=null&&I.hasOwnProperty("errorMessage"))W.errorMessage=I.errorMessage;return W},G.prototype.toJSON=function F(){return this.constructor.toObject(this,n9.util.toJSONOptions)},G.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.collector.trace.v1.ExportTracePartialSuccess"},G}(),Z}(),D}(),Q.metrics=function(){var D={};return D.v1=function(){var Z={};return Z.MetricsService=function(){function G(F,I,Y){n9.rpc.Service.call(this,F,I,Y)}return(G.prototype=Object.create(n9.rpc.Service.prototype)).constructor=G,G.create=function F(I,Y,W){return new this(I,Y,W)},Object.defineProperty(G.prototype.export=function F(I,Y){return this.rpcCall(F,X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceRequest,X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceResponse,I,Y)},"name",{value:"Export"}),G}(),Z.ExportMetricsServiceRequest=function(){function G(F){if(this.resourceMetrics=[],F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}return G.prototype.resourceMetrics=H1.emptyArray,G.create=function F(I){return new G(I)},G.encode=function F(I,Y){if(!Y)Y=W4.create();if(I.resourceMetrics!=null&&I.resourceMetrics.length)for(var W=0;W<I.resourceMetrics.length;++W)X1.opentelemetry.proto.metrics.v1.ResourceMetrics.encode(I.resourceMetrics[W],Y.uint32(10).fork()).ldelim();return Y},G.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},G.decode=function F(I,Y){if(!(I instanceof u0))I=u0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceRequest;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 1:{if(!(J.resourceMetrics&&J.resourceMetrics.length))J.resourceMetrics=[];J.resourceMetrics.push(X1.opentelemetry.proto.metrics.v1.ResourceMetrics.decode(I,I.uint32()));break}default:I.skipType(X&7);break}}return J},G.decodeDelimited=function F(I){if(!(I instanceof u0))I=new u0(I);return this.decode(I,I.uint32())},G.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";if(I.resourceMetrics!=null&&I.hasOwnProperty("resourceMetrics")){if(!Array.isArray(I.resourceMetrics))return"resourceMetrics: array expected";for(var Y=0;Y<I.resourceMetrics.length;++Y){var W=X1.opentelemetry.proto.metrics.v1.ResourceMetrics.verify(I.resourceMetrics[Y]);if(W)return"resourceMetrics."+W}}return null},G.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceRequest)return I;var Y=new X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceRequest;if(I.resourceMetrics){if(!Array.isArray(I.resourceMetrics))throw TypeError(".opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceRequest.resourceMetrics: array expected");Y.resourceMetrics=[];for(var W=0;W<I.resourceMetrics.length;++W){if(typeof I.resourceMetrics[W]!=="object")throw TypeError(".opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceRequest.resourceMetrics: object expected");Y.resourceMetrics[W]=X1.opentelemetry.proto.metrics.v1.ResourceMetrics.fromObject(I.resourceMetrics[W])}}return Y},G.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.arrays||Y.defaults)W.resourceMetrics=[];if(I.resourceMetrics&&I.resourceMetrics.length){W.resourceMetrics=[];for(var J=0;J<I.resourceMetrics.length;++J)W.resourceMetrics[J]=X1.opentelemetry.proto.metrics.v1.ResourceMetrics.toObject(I.resourceMetrics[J],Y)}return W},G.prototype.toJSON=function F(){return this.constructor.toObject(this,n9.util.toJSONOptions)},G.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceRequest"},G}(),Z.ExportMetricsServiceResponse=function(){function G(F){if(F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}return G.prototype.partialSuccess=null,G.create=function F(I){return new G(I)},G.encode=function F(I,Y){if(!Y)Y=W4.create();if(I.partialSuccess!=null&&Object.hasOwnProperty.call(I,"partialSuccess"))X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsPartialSuccess.encode(I.partialSuccess,Y.uint32(10).fork()).ldelim();return Y},G.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},G.decode=function F(I,Y){if(!(I instanceof u0))I=u0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceResponse;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 1:{J.partialSuccess=X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsPartialSuccess.decode(I,I.uint32());break}default:I.skipType(X&7);break}}return J},G.decodeDelimited=function F(I){if(!(I instanceof u0))I=new u0(I);return this.decode(I,I.uint32())},G.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";if(I.partialSuccess!=null&&I.hasOwnProperty("partialSuccess")){var Y=X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsPartialSuccess.verify(I.partialSuccess);if(Y)return"partialSuccess."+Y}return null},G.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceResponse)return I;var Y=new X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceResponse;if(I.partialSuccess!=null){if(typeof I.partialSuccess!=="object")throw TypeError(".opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceResponse.partialSuccess: object expected");Y.partialSuccess=X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsPartialSuccess.fromObject(I.partialSuccess)}return Y},G.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.defaults)W.partialSuccess=null;if(I.partialSuccess!=null&&I.hasOwnProperty("partialSuccess"))W.partialSuccess=X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsPartialSuccess.toObject(I.partialSuccess,Y);return W},G.prototype.toJSON=function F(){return this.constructor.toObject(this,n9.util.toJSONOptions)},G.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceResponse"},G}(),Z.ExportMetricsPartialSuccess=function(){function G(F){if(F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}return G.prototype.rejectedDataPoints=null,G.prototype.errorMessage=null,G.create=function F(I){return new G(I)},G.encode=function F(I,Y){if(!Y)Y=W4.create();if(I.rejectedDataPoints!=null&&Object.hasOwnProperty.call(I,"rejectedDataPoints"))Y.uint32(8).int64(I.rejectedDataPoints);if(I.errorMessage!=null&&Object.hasOwnProperty.call(I,"errorMessage"))Y.uint32(18).string(I.errorMessage);return Y},G.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},G.decode=function F(I,Y){if(!(I instanceof u0))I=u0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsPartialSuccess;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 1:{J.rejectedDataPoints=I.int64();break}case 2:{J.errorMessage=I.string();break}default:I.skipType(X&7);break}}return J},G.decodeDelimited=function F(I){if(!(I instanceof u0))I=new u0(I);return this.decode(I,I.uint32())},G.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";if(I.rejectedDataPoints!=null&&I.hasOwnProperty("rejectedDataPoints")){if(!H1.isInteger(I.rejectedDataPoints)&&!(I.rejectedDataPoints&&H1.isInteger(I.rejectedDataPoints.low)&&H1.isInteger(I.rejectedDataPoints.high)))return"rejectedDataPoints: integer|Long expected"}if(I.errorMessage!=null&&I.hasOwnProperty("errorMessage")){if(!H1.isString(I.errorMessage))return"errorMessage: string expected"}return null},G.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsPartialSuccess)return I;var Y=new X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsPartialSuccess;if(I.rejectedDataPoints!=null){if(H1.Long)(Y.rejectedDataPoints=H1.Long.fromValue(I.rejectedDataPoints)).unsigned=!1;else if(typeof I.rejectedDataPoints==="string")Y.rejectedDataPoints=parseInt(I.rejectedDataPoints,10);else if(typeof I.rejectedDataPoints==="number")Y.rejectedDataPoints=I.rejectedDataPoints;else if(typeof I.rejectedDataPoints==="object")Y.rejectedDataPoints=new H1.LongBits(I.rejectedDataPoints.low>>>0,I.rejectedDataPoints.high>>>0).toNumber()}if(I.errorMessage!=null)Y.errorMessage=String(I.errorMessage);return Y},G.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.defaults){if(H1.Long){var J=new H1.Long(0,0,!1);W.rejectedDataPoints=Y.longs===String?J.toString():Y.longs===Number?J.toNumber():J}else W.rejectedDataPoints=Y.longs===String?"0":0;W.errorMessage=""}if(I.rejectedDataPoints!=null&&I.hasOwnProperty("rejectedDataPoints"))if(typeof I.rejectedDataPoints==="number")W.rejectedDataPoints=Y.longs===String?String(I.rejectedDataPoints):I.rejectedDataPoints;else W.rejectedDataPoints=Y.longs===String?H1.Long.prototype.toString.call(I.rejectedDataPoints):Y.longs===Number?new H1.LongBits(I.rejectedDataPoints.low>>>0,I.rejectedDataPoints.high>>>0).toNumber():I.rejectedDataPoints;if(I.errorMessage!=null&&I.hasOwnProperty("errorMessage"))W.errorMessage=I.errorMessage;return W},G.prototype.toJSON=function F(){return this.constructor.toObject(this,n9.util.toJSONOptions)},G.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.collector.metrics.v1.ExportMetricsPartialSuccess"},G}(),Z}(),D}(),Q.logs=function(){var D={};return D.v1=function(){var Z={};return Z.LogsService=function(){function G(F,I,Y){n9.rpc.Service.call(this,F,I,Y)}return(G.prototype=Object.create(n9.rpc.Service.prototype)).constructor=G,G.create=function F(I,Y,W){return new this(I,Y,W)},Object.defineProperty(G.prototype.export=function F(I,Y){return this.rpcCall(F,X1.opentelemetry.proto.collector.logs.v1.ExportLogsServiceRequest,X1.opentelemetry.proto.collector.logs.v1.ExportLogsServiceResponse,I,Y)},"name",{value:"Export"}),G}(),Z.ExportLogsServiceRequest=function(){function G(F){if(this.resourceLogs=[],F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}return G.prototype.resourceLogs=H1.emptyArray,G.create=function F(I){return new G(I)},G.encode=function F(I,Y){if(!Y)Y=W4.create();if(I.resourceLogs!=null&&I.resourceLogs.length)for(var W=0;W<I.resourceLogs.length;++W)X1.opentelemetry.proto.logs.v1.ResourceLogs.encode(I.resourceLogs[W],Y.uint32(10).fork()).ldelim();return Y},G.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},G.decode=function F(I,Y){if(!(I instanceof u0))I=u0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.collector.logs.v1.ExportLogsServiceRequest;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 1:{if(!(J.resourceLogs&&J.resourceLogs.length))J.resourceLogs=[];J.resourceLogs.push(X1.opentelemetry.proto.logs.v1.ResourceLogs.decode(I,I.uint32()));break}default:I.skipType(X&7);break}}return J},G.decodeDelimited=function F(I){if(!(I instanceof u0))I=new u0(I);return this.decode(I,I.uint32())},G.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";if(I.resourceLogs!=null&&I.hasOwnProperty("resourceLogs")){if(!Array.isArray(I.resourceLogs))return"resourceLogs: array expected";for(var Y=0;Y<I.resourceLogs.length;++Y){var W=X1.opentelemetry.proto.logs.v1.ResourceLogs.verify(I.resourceLogs[Y]);if(W)return"resourceLogs."+W}}return null},G.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.collector.logs.v1.ExportLogsServiceRequest)return I;var Y=new X1.opentelemetry.proto.collector.logs.v1.ExportLogsServiceRequest;if(I.resourceLogs){if(!Array.isArray(I.resourceLogs))throw TypeError(".opentelemetry.proto.collector.logs.v1.ExportLogsServiceRequest.resourceLogs: array expected");Y.resourceLogs=[];for(var W=0;W<I.resourceLogs.length;++W){if(typeof I.resourceLogs[W]!=="object")throw TypeError(".opentelemetry.proto.collector.logs.v1.ExportLogsServiceRequest.resourceLogs: object expected");Y.resourceLogs[W]=X1.opentelemetry.proto.logs.v1.ResourceLogs.fromObject(I.resourceLogs[W])}}return Y},G.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.arrays||Y.defaults)W.resourceLogs=[];if(I.resourceLogs&&I.resourceLogs.length){W.resourceLogs=[];for(var J=0;J<I.resourceLogs.length;++J)W.resourceLogs[J]=X1.opentelemetry.proto.logs.v1.ResourceLogs.toObject(I.resourceLogs[J],Y)}return W},G.prototype.toJSON=function F(){return this.constructor.toObject(this,n9.util.toJSONOptions)},G.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.collector.logs.v1.ExportLogsServiceRequest"},G}(),Z.ExportLogsServiceResponse=function(){function G(F){if(F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}return G.prototype.partialSuccess=null,G.create=function F(I){return new G(I)},G.encode=function F(I,Y){if(!Y)Y=W4.create();if(I.partialSuccess!=null&&Object.hasOwnProperty.call(I,"partialSuccess"))X1.opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess.encode(I.partialSuccess,Y.uint32(10).fork()).ldelim();return Y},G.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},G.decode=function F(I,Y){if(!(I instanceof u0))I=u0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.collector.logs.v1.ExportLogsServiceResponse;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 1:{J.partialSuccess=X1.opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess.decode(I,I.uint32());break}default:I.skipType(X&7);break}}return J},G.decodeDelimited=function F(I){if(!(I instanceof u0))I=new u0(I);return this.decode(I,I.uint32())},G.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";if(I.partialSuccess!=null&&I.hasOwnProperty("partialSuccess")){var Y=X1.opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess.verify(I.partialSuccess);if(Y)return"partialSuccess."+Y}return null},G.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.collector.logs.v1.ExportLogsServiceResponse)return I;var Y=new X1.opentelemetry.proto.collector.logs.v1.ExportLogsServiceResponse;if(I.partialSuccess!=null){if(typeof I.partialSuccess!=="object")throw TypeError(".opentelemetry.proto.collector.logs.v1.ExportLogsServiceResponse.partialSuccess: object expected");Y.partialSuccess=X1.opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess.fromObject(I.partialSuccess)}return Y},G.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.defaults)W.partialSuccess=null;if(I.partialSuccess!=null&&I.hasOwnProperty("partialSuccess"))W.partialSuccess=X1.opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess.toObject(I.partialSuccess,Y);return W},G.prototype.toJSON=function F(){return this.constructor.toObject(this,n9.util.toJSONOptions)},G.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.collector.logs.v1.ExportLogsServiceResponse"},G}(),Z.ExportLogsPartialSuccess=function(){function G(F){if(F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}return G.prototype.rejectedLogRecords=null,G.prototype.errorMessage=null,G.create=function F(I){return new G(I)},G.encode=function F(I,Y){if(!Y)Y=W4.create();if(I.rejectedLogRecords!=null&&Object.hasOwnProperty.call(I,"rejectedLogRecords"))Y.uint32(8).int64(I.rejectedLogRecords);if(I.errorMessage!=null&&Object.hasOwnProperty.call(I,"errorMessage"))Y.uint32(18).string(I.errorMessage);return Y},G.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},G.decode=function F(I,Y){if(!(I instanceof u0))I=u0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 1:{J.rejectedLogRecords=I.int64();break}case 2:{J.errorMessage=I.string();break}default:I.skipType(X&7);break}}return J},G.decodeDelimited=function F(I){if(!(I instanceof u0))I=new u0(I);return this.decode(I,I.uint32())},G.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";if(I.rejectedLogRecords!=null&&I.hasOwnProperty("rejectedLogRecords")){if(!H1.isInteger(I.rejectedLogRecords)&&!(I.rejectedLogRecords&&H1.isInteger(I.rejectedLogRecords.low)&&H1.isInteger(I.rejectedLogRecords.high)))return"rejectedLogRecords: integer|Long expected"}if(I.errorMessage!=null&&I.hasOwnProperty("errorMessage")){if(!H1.isString(I.errorMessage))return"errorMessage: string expected"}return null},G.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess)return I;var Y=new X1.opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess;if(I.rejectedLogRecords!=null){if(H1.Long)(Y.rejectedLogRecords=H1.Long.fromValue(I.rejectedLogRecords)).unsigned=!1;else if(typeof I.rejectedLogRecords==="string")Y.rejectedLogRecords=parseInt(I.rejectedLogRecords,10);else if(typeof I.rejectedLogRecords==="number")Y.rejectedLogRecords=I.rejectedLogRecords;else if(typeof I.rejectedLogRecords==="object")Y.rejectedLogRecords=new H1.LongBits(I.rejectedLogRecords.low>>>0,I.rejectedLogRecords.high>>>0).toNumber()}if(I.errorMessage!=null)Y.errorMessage=String(I.errorMessage);return Y},G.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.defaults){if(H1.Long){var J=new H1.Long(0,0,!1);W.rejectedLogRecords=Y.longs===String?J.toString():Y.longs===Number?J.toNumber():J}else W.rejectedLogRecords=Y.longs===String?"0":0;W.errorMessage=""}if(I.rejectedLogRecords!=null&&I.hasOwnProperty("rejectedLogRecords"))if(typeof I.rejectedLogRecords==="number")W.rejectedLogRecords=Y.longs===String?String(I.rejectedLogRecords):I.rejectedLogRecords;else W.rejectedLogRecords=Y.longs===String?H1.Long.prototype.toString.call(I.rejectedLogRecords):Y.longs===Number?new H1.LongBits(I.rejectedLogRecords.low>>>0,I.rejectedLogRecords.high>>>0).toNumber():I.rejectedLogRecords;if(I.errorMessage!=null&&I.hasOwnProperty("errorMessage"))W.errorMessage=I.errorMessage;return W},G.prototype.toJSON=function F(){return this.constructor.toObject(this,n9.util.toJSONOptions)},G.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess"},G}(),Z}(),D}(),Q}(),B.metrics=function(){var Q={};return Q.v1=function(){var D={};return D.MetricsData=function(){function Z(G){if(this.resourceMetrics=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.resourceMetrics=H1.emptyArray,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=W4.create();if(F.resourceMetrics!=null&&F.resourceMetrics.length)for(var Y=0;Y<F.resourceMetrics.length;++Y)X1.opentelemetry.proto.metrics.v1.ResourceMetrics.encode(F.resourceMetrics[Y],I.uint32(10).fork()).ldelim();return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof u0))F=u0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.metrics.v1.MetricsData;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{if(!(W.resourceMetrics&&W.resourceMetrics.length))W.resourceMetrics=[];W.resourceMetrics.push(X1.opentelemetry.proto.metrics.v1.ResourceMetrics.decode(F,F.uint32()));break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof u0))F=new u0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.resourceMetrics!=null&&F.hasOwnProperty("resourceMetrics")){if(!Array.isArray(F.resourceMetrics))return"resourceMetrics: array expected";for(var I=0;I<F.resourceMetrics.length;++I){var Y=X1.opentelemetry.proto.metrics.v1.ResourceMetrics.verify(F.resourceMetrics[I]);if(Y)return"resourceMetrics."+Y}}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.metrics.v1.MetricsData)return F;var I=new X1.opentelemetry.proto.metrics.v1.MetricsData;if(F.resourceMetrics){if(!Array.isArray(F.resourceMetrics))throw TypeError(".opentelemetry.proto.metrics.v1.MetricsData.resourceMetrics: array expected");I.resourceMetrics=[];for(var Y=0;Y<F.resourceMetrics.length;++Y){if(typeof F.resourceMetrics[Y]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.MetricsData.resourceMetrics: object expected");I.resourceMetrics[Y]=X1.opentelemetry.proto.metrics.v1.ResourceMetrics.fromObject(F.resourceMetrics[Y])}}return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.resourceMetrics=[];if(F.resourceMetrics&&F.resourceMetrics.length){Y.resourceMetrics=[];for(var W=0;W<F.resourceMetrics.length;++W)Y.resourceMetrics[W]=X1.opentelemetry.proto.metrics.v1.ResourceMetrics.toObject(F.resourceMetrics[W],I)}return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,n9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.metrics.v1.MetricsData"},Z}(),D.ResourceMetrics=function(){function Z(G){if(this.scopeMetrics=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.resource=null,Z.prototype.scopeMetrics=H1.emptyArray,Z.prototype.schemaUrl=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=W4.create();if(F.resource!=null&&Object.hasOwnProperty.call(F,"resource"))X1.opentelemetry.proto.resource.v1.Resource.encode(F.resource,I.uint32(10).fork()).ldelim();if(F.scopeMetrics!=null&&F.scopeMetrics.length)for(var Y=0;Y<F.scopeMetrics.length;++Y)X1.opentelemetry.proto.metrics.v1.ScopeMetrics.encode(F.scopeMetrics[Y],I.uint32(18).fork()).ldelim();if(F.schemaUrl!=null&&Object.hasOwnProperty.call(F,"schemaUrl"))I.uint32(26).string(F.schemaUrl);return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof u0))F=u0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.metrics.v1.ResourceMetrics;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{W.resource=X1.opentelemetry.proto.resource.v1.Resource.decode(F,F.uint32());break}case 2:{if(!(W.scopeMetrics&&W.scopeMetrics.length))W.scopeMetrics=[];W.scopeMetrics.push(X1.opentelemetry.proto.metrics.v1.ScopeMetrics.decode(F,F.uint32()));break}case 3:{W.schemaUrl=F.string();break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof u0))F=new u0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.resource!=null&&F.hasOwnProperty("resource")){var I=X1.opentelemetry.proto.resource.v1.Resource.verify(F.resource);if(I)return"resource."+I}if(F.scopeMetrics!=null&&F.hasOwnProperty("scopeMetrics")){if(!Array.isArray(F.scopeMetrics))return"scopeMetrics: array expected";for(var Y=0;Y<F.scopeMetrics.length;++Y){var I=X1.opentelemetry.proto.metrics.v1.ScopeMetrics.verify(F.scopeMetrics[Y]);if(I)return"scopeMetrics."+I}}if(F.schemaUrl!=null&&F.hasOwnProperty("schemaUrl")){if(!H1.isString(F.schemaUrl))return"schemaUrl: string expected"}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.metrics.v1.ResourceMetrics)return F;var I=new X1.opentelemetry.proto.metrics.v1.ResourceMetrics;if(F.resource!=null){if(typeof F.resource!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.ResourceMetrics.resource: object expected");I.resource=X1.opentelemetry.proto.resource.v1.Resource.fromObject(F.resource)}if(F.scopeMetrics){if(!Array.isArray(F.scopeMetrics))throw TypeError(".opentelemetry.proto.metrics.v1.ResourceMetrics.scopeMetrics: array expected");I.scopeMetrics=[];for(var Y=0;Y<F.scopeMetrics.length;++Y){if(typeof F.scopeMetrics[Y]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.ResourceMetrics.scopeMetrics: object expected");I.scopeMetrics[Y]=X1.opentelemetry.proto.metrics.v1.ScopeMetrics.fromObject(F.scopeMetrics[Y])}}if(F.schemaUrl!=null)I.schemaUrl=String(F.schemaUrl);return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.scopeMetrics=[];if(I.defaults)Y.resource=null,Y.schemaUrl="";if(F.resource!=null&&F.hasOwnProperty("resource"))Y.resource=X1.opentelemetry.proto.resource.v1.Resource.toObject(F.resource,I);if(F.scopeMetrics&&F.scopeMetrics.length){Y.scopeMetrics=[];for(var W=0;W<F.scopeMetrics.length;++W)Y.scopeMetrics[W]=X1.opentelemetry.proto.metrics.v1.ScopeMetrics.toObject(F.scopeMetrics[W],I)}if(F.schemaUrl!=null&&F.hasOwnProperty("schemaUrl"))Y.schemaUrl=F.schemaUrl;return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,n9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.metrics.v1.ResourceMetrics"},Z}(),D.ScopeMetrics=function(){function Z(G){if(this.metrics=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.scope=null,Z.prototype.metrics=H1.emptyArray,Z.prototype.schemaUrl=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=W4.create();if(F.scope!=null&&Object.hasOwnProperty.call(F,"scope"))X1.opentelemetry.proto.common.v1.InstrumentationScope.encode(F.scope,I.uint32(10).fork()).ldelim();if(F.metrics!=null&&F.metrics.length)for(var Y=0;Y<F.metrics.length;++Y)X1.opentelemetry.proto.metrics.v1.Metric.encode(F.metrics[Y],I.uint32(18).fork()).ldelim();if(F.schemaUrl!=null&&Object.hasOwnProperty.call(F,"schemaUrl"))I.uint32(26).string(F.schemaUrl);return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof u0))F=u0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.metrics.v1.ScopeMetrics;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{W.scope=X1.opentelemetry.proto.common.v1.InstrumentationScope.decode(F,F.uint32());break}case 2:{if(!(W.metrics&&W.metrics.length))W.metrics=[];W.metrics.push(X1.opentelemetry.proto.metrics.v1.Metric.decode(F,F.uint32()));break}case 3:{W.schemaUrl=F.string();break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof u0))F=new u0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.scope!=null&&F.hasOwnProperty("scope")){var I=X1.opentelemetry.proto.common.v1.InstrumentationScope.verify(F.scope);if(I)return"scope."+I}if(F.metrics!=null&&F.hasOwnProperty("metrics")){if(!Array.isArray(F.metrics))return"metrics: array expected";for(var Y=0;Y<F.metrics.length;++Y){var I=X1.opentelemetry.proto.metrics.v1.Metric.verify(F.metrics[Y]);if(I)return"metrics."+I}}if(F.schemaUrl!=null&&F.hasOwnProperty("schemaUrl")){if(!H1.isString(F.schemaUrl))return"schemaUrl: string expected"}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.metrics.v1.ScopeMetrics)return F;var I=new X1.opentelemetry.proto.metrics.v1.ScopeMetrics;if(F.scope!=null){if(typeof F.scope!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.ScopeMetrics.scope: object expected");I.scope=X1.opentelemetry.proto.common.v1.InstrumentationScope.fromObject(F.scope)}if(F.metrics){if(!Array.isArray(F.metrics))throw TypeError(".opentelemetry.proto.metrics.v1.ScopeMetrics.metrics: array expected");I.metrics=[];for(var Y=0;Y<F.metrics.length;++Y){if(typeof F.metrics[Y]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.ScopeMetrics.metrics: object expected");I.metrics[Y]=X1.opentelemetry.proto.metrics.v1.Metric.fromObject(F.metrics[Y])}}if(F.schemaUrl!=null)I.schemaUrl=String(F.schemaUrl);return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.metrics=[];if(I.defaults)Y.scope=null,Y.schemaUrl="";if(F.scope!=null&&F.hasOwnProperty("scope"))Y.scope=X1.opentelemetry.proto.common.v1.InstrumentationScope.toObject(F.scope,I);if(F.metrics&&F.metrics.length){Y.metrics=[];for(var W=0;W<F.metrics.length;++W)Y.metrics[W]=X1.opentelemetry.proto.metrics.v1.Metric.toObject(F.metrics[W],I)}if(F.schemaUrl!=null&&F.hasOwnProperty("schemaUrl"))Y.schemaUrl=F.schemaUrl;return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,n9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.metrics.v1.ScopeMetrics"},Z}(),D.Metric=function(){function Z(F){if(F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}Z.prototype.name=null,Z.prototype.description=null,Z.prototype.unit=null,Z.prototype.gauge=null,Z.prototype.sum=null,Z.prototype.histogram=null,Z.prototype.exponentialHistogram=null,Z.prototype.summary=null;var G;return Object.defineProperty(Z.prototype,"data",{get:H1.oneOfGetter(G=["gauge","sum","histogram","exponentialHistogram","summary"]),set:H1.oneOfSetter(G)}),Z.create=function F(I){return new Z(I)},Z.encode=function F(I,Y){if(!Y)Y=W4.create();if(I.name!=null&&Object.hasOwnProperty.call(I,"name"))Y.uint32(10).string(I.name);if(I.description!=null&&Object.hasOwnProperty.call(I,"description"))Y.uint32(18).string(I.description);if(I.unit!=null&&Object.hasOwnProperty.call(I,"unit"))Y.uint32(26).string(I.unit);if(I.gauge!=null&&Object.hasOwnProperty.call(I,"gauge"))X1.opentelemetry.proto.metrics.v1.Gauge.encode(I.gauge,Y.uint32(42).fork()).ldelim();if(I.sum!=null&&Object.hasOwnProperty.call(I,"sum"))X1.opentelemetry.proto.metrics.v1.Sum.encode(I.sum,Y.uint32(58).fork()).ldelim();if(I.histogram!=null&&Object.hasOwnProperty.call(I,"histogram"))X1.opentelemetry.proto.metrics.v1.Histogram.encode(I.histogram,Y.uint32(74).fork()).ldelim();if(I.exponentialHistogram!=null&&Object.hasOwnProperty.call(I,"exponentialHistogram"))X1.opentelemetry.proto.metrics.v1.ExponentialHistogram.encode(I.exponentialHistogram,Y.uint32(82).fork()).ldelim();if(I.summary!=null&&Object.hasOwnProperty.call(I,"summary"))X1.opentelemetry.proto.metrics.v1.Summary.encode(I.summary,Y.uint32(90).fork()).ldelim();return Y},Z.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},Z.decode=function F(I,Y){if(!(I instanceof u0))I=u0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.metrics.v1.Metric;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 1:{J.name=I.string();break}case 2:{J.description=I.string();break}case 3:{J.unit=I.string();break}case 5:{J.gauge=X1.opentelemetry.proto.metrics.v1.Gauge.decode(I,I.uint32());break}case 7:{J.sum=X1.opentelemetry.proto.metrics.v1.Sum.decode(I,I.uint32());break}case 9:{J.histogram=X1.opentelemetry.proto.metrics.v1.Histogram.decode(I,I.uint32());break}case 10:{J.exponentialHistogram=X1.opentelemetry.proto.metrics.v1.ExponentialHistogram.decode(I,I.uint32());break}case 11:{J.summary=X1.opentelemetry.proto.metrics.v1.Summary.decode(I,I.uint32());break}default:I.skipType(X&7);break}}return J},Z.decodeDelimited=function F(I){if(!(I instanceof u0))I=new u0(I);return this.decode(I,I.uint32())},Z.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";var Y={};if(I.name!=null&&I.hasOwnProperty("name")){if(!H1.isString(I.name))return"name: string expected"}if(I.description!=null&&I.hasOwnProperty("description")){if(!H1.isString(I.description))return"description: string expected"}if(I.unit!=null&&I.hasOwnProperty("unit")){if(!H1.isString(I.unit))return"unit: string expected"}if(I.gauge!=null&&I.hasOwnProperty("gauge")){Y.data=1;{var W=X1.opentelemetry.proto.metrics.v1.Gauge.verify(I.gauge);if(W)return"gauge."+W}}if(I.sum!=null&&I.hasOwnProperty("sum")){if(Y.data===1)return"data: multiple values";Y.data=1;{var W=X1.opentelemetry.proto.metrics.v1.Sum.verify(I.sum);if(W)return"sum."+W}}if(I.histogram!=null&&I.hasOwnProperty("histogram")){if(Y.data===1)return"data: multiple values";Y.data=1;{var W=X1.opentelemetry.proto.metrics.v1.Histogram.verify(I.histogram);if(W)return"histogram."+W}}if(I.exponentialHistogram!=null&&I.hasOwnProperty("exponentialHistogram")){if(Y.data===1)return"data: multiple values";Y.data=1;{var W=X1.opentelemetry.proto.metrics.v1.ExponentialHistogram.verify(I.exponentialHistogram);if(W)return"exponentialHistogram."+W}}if(I.summary!=null&&I.hasOwnProperty("summary")){if(Y.data===1)return"data: multiple values";Y.data=1;{var W=X1.opentelemetry.proto.metrics.v1.Summary.verify(I.summary);if(W)return"summary."+W}}return null},Z.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.metrics.v1.Metric)return I;var Y=new X1.opentelemetry.proto.metrics.v1.Metric;if(I.name!=null)Y.name=String(I.name);if(I.description!=null)Y.description=String(I.description);if(I.unit!=null)Y.unit=String(I.unit);if(I.gauge!=null){if(typeof I.gauge!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.Metric.gauge: object expected");Y.gauge=X1.opentelemetry.proto.metrics.v1.Gauge.fromObject(I.gauge)}if(I.sum!=null){if(typeof I.sum!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.Metric.sum: object expected");Y.sum=X1.opentelemetry.proto.metrics.v1.Sum.fromObject(I.sum)}if(I.histogram!=null){if(typeof I.histogram!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.Metric.histogram: object expected");Y.histogram=X1.opentelemetry.proto.metrics.v1.Histogram.fromObject(I.histogram)}if(I.exponentialHistogram!=null){if(typeof I.exponentialHistogram!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.Metric.exponentialHistogram: object expected");Y.exponentialHistogram=X1.opentelemetry.proto.metrics.v1.ExponentialHistogram.fromObject(I.exponentialHistogram)}if(I.summary!=null){if(typeof I.summary!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.Metric.summary: object expected");Y.summary=X1.opentelemetry.proto.metrics.v1.Summary.fromObject(I.summary)}return Y},Z.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.defaults)W.name="",W.description="",W.unit="";if(I.name!=null&&I.hasOwnProperty("name"))W.name=I.name;if(I.description!=null&&I.hasOwnProperty("description"))W.description=I.description;if(I.unit!=null&&I.hasOwnProperty("unit"))W.unit=I.unit;if(I.gauge!=null&&I.hasOwnProperty("gauge")){if(W.gauge=X1.opentelemetry.proto.metrics.v1.Gauge.toObject(I.gauge,Y),Y.oneofs)W.data="gauge"}if(I.sum!=null&&I.hasOwnProperty("sum")){if(W.sum=X1.opentelemetry.proto.metrics.v1.Sum.toObject(I.sum,Y),Y.oneofs)W.data="sum"}if(I.histogram!=null&&I.hasOwnProperty("histogram")){if(W.histogram=X1.opentelemetry.proto.metrics.v1.Histogram.toObject(I.histogram,Y),Y.oneofs)W.data="histogram"}if(I.exponentialHistogram!=null&&I.hasOwnProperty("exponentialHistogram")){if(W.exponentialHistogram=X1.opentelemetry.proto.metrics.v1.ExponentialHistogram.toObject(I.exponentialHistogram,Y),Y.oneofs)W.data="exponentialHistogram"}if(I.summary!=null&&I.hasOwnProperty("summary")){if(W.summary=X1.opentelemetry.proto.metrics.v1.Summary.toObject(I.summary,Y),Y.oneofs)W.data="summary"}return W},Z.prototype.toJSON=function F(){return this.constructor.toObject(this,n9.util.toJSONOptions)},Z.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.metrics.v1.Metric"},Z}(),D.Gauge=function(){function Z(G){if(this.dataPoints=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.dataPoints=H1.emptyArray,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=W4.create();if(F.dataPoints!=null&&F.dataPoints.length)for(var Y=0;Y<F.dataPoints.length;++Y)X1.opentelemetry.proto.metrics.v1.NumberDataPoint.encode(F.dataPoints[Y],I.uint32(10).fork()).ldelim();return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof u0))F=u0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.metrics.v1.Gauge;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{if(!(W.dataPoints&&W.dataPoints.length))W.dataPoints=[];W.dataPoints.push(X1.opentelemetry.proto.metrics.v1.NumberDataPoint.decode(F,F.uint32()));break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof u0))F=new u0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.dataPoints!=null&&F.hasOwnProperty("dataPoints")){if(!Array.isArray(F.dataPoints))return"dataPoints: array expected";for(var I=0;I<F.dataPoints.length;++I){var Y=X1.opentelemetry.proto.metrics.v1.NumberDataPoint.verify(F.dataPoints[I]);if(Y)return"dataPoints."+Y}}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.metrics.v1.Gauge)return F;var I=new X1.opentelemetry.proto.metrics.v1.Gauge;if(F.dataPoints){if(!Array.isArray(F.dataPoints))throw TypeError(".opentelemetry.proto.metrics.v1.Gauge.dataPoints: array expected");I.dataPoints=[];for(var Y=0;Y<F.dataPoints.length;++Y){if(typeof F.dataPoints[Y]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.Gauge.dataPoints: object expected");I.dataPoints[Y]=X1.opentelemetry.proto.metrics.v1.NumberDataPoint.fromObject(F.dataPoints[Y])}}return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.dataPoints=[];if(F.dataPoints&&F.dataPoints.length){Y.dataPoints=[];for(var W=0;W<F.dataPoints.length;++W)Y.dataPoints[W]=X1.opentelemetry.proto.metrics.v1.NumberDataPoint.toObject(F.dataPoints[W],I)}return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,n9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.metrics.v1.Gauge"},Z}(),D.Sum=function(){function Z(G){if(this.dataPoints=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.dataPoints=H1.emptyArray,Z.prototype.aggregationTemporality=null,Z.prototype.isMonotonic=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=W4.create();if(F.dataPoints!=null&&F.dataPoints.length)for(var Y=0;Y<F.dataPoints.length;++Y)X1.opentelemetry.proto.metrics.v1.NumberDataPoint.encode(F.dataPoints[Y],I.uint32(10).fork()).ldelim();if(F.aggregationTemporality!=null&&Object.hasOwnProperty.call(F,"aggregationTemporality"))I.uint32(16).int32(F.aggregationTemporality);if(F.isMonotonic!=null&&Object.hasOwnProperty.call(F,"isMonotonic"))I.uint32(24).bool(F.isMonotonic);return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof u0))F=u0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.metrics.v1.Sum;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{if(!(W.dataPoints&&W.dataPoints.length))W.dataPoints=[];W.dataPoints.push(X1.opentelemetry.proto.metrics.v1.NumberDataPoint.decode(F,F.uint32()));break}case 2:{W.aggregationTemporality=F.int32();break}case 3:{W.isMonotonic=F.bool();break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof u0))F=new u0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.dataPoints!=null&&F.hasOwnProperty("dataPoints")){if(!Array.isArray(F.dataPoints))return"dataPoints: array expected";for(var I=0;I<F.dataPoints.length;++I){var Y=X1.opentelemetry.proto.metrics.v1.NumberDataPoint.verify(F.dataPoints[I]);if(Y)return"dataPoints."+Y}}if(F.aggregationTemporality!=null&&F.hasOwnProperty("aggregationTemporality"))switch(F.aggregationTemporality){default:return"aggregationTemporality: enum value expected";case 0:case 1:case 2:break}if(F.isMonotonic!=null&&F.hasOwnProperty("isMonotonic")){if(typeof F.isMonotonic!=="boolean")return"isMonotonic: boolean expected"}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.metrics.v1.Sum)return F;var I=new X1.opentelemetry.proto.metrics.v1.Sum;if(F.dataPoints){if(!Array.isArray(F.dataPoints))throw TypeError(".opentelemetry.proto.metrics.v1.Sum.dataPoints: array expected");I.dataPoints=[];for(var Y=0;Y<F.dataPoints.length;++Y){if(typeof F.dataPoints[Y]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.Sum.dataPoints: object expected");I.dataPoints[Y]=X1.opentelemetry.proto.metrics.v1.NumberDataPoint.fromObject(F.dataPoints[Y])}}switch(F.aggregationTemporality){default:if(typeof F.aggregationTemporality==="number"){I.aggregationTemporality=F.aggregationTemporality;break}break;case"AGGREGATION_TEMPORALITY_UNSPECIFIED":case 0:I.aggregationTemporality=0;break;case"AGGREGATION_TEMPORALITY_DELTA":case 1:I.aggregationTemporality=1;break;case"AGGREGATION_TEMPORALITY_CUMULATIVE":case 2:I.aggregationTemporality=2;break}if(F.isMonotonic!=null)I.isMonotonic=Boolean(F.isMonotonic);return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.dataPoints=[];if(I.defaults)Y.aggregationTemporality=I.enums===String?"AGGREGATION_TEMPORALITY_UNSPECIFIED":0,Y.isMonotonic=!1;if(F.dataPoints&&F.dataPoints.length){Y.dataPoints=[];for(var W=0;W<F.dataPoints.length;++W)Y.dataPoints[W]=X1.opentelemetry.proto.metrics.v1.NumberDataPoint.toObject(F.dataPoints[W],I)}if(F.aggregationTemporality!=null&&F.hasOwnProperty("aggregationTemporality"))Y.aggregationTemporality=I.enums===String?X1.opentelemetry.proto.metrics.v1.AggregationTemporality[F.aggregationTemporality]===void 0?F.aggregationTemporality:X1.opentelemetry.proto.metrics.v1.AggregationTemporality[F.aggregationTemporality]:F.aggregationTemporality;if(F.isMonotonic!=null&&F.hasOwnProperty("isMonotonic"))Y.isMonotonic=F.isMonotonic;return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,n9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.metrics.v1.Sum"},Z}(),D.Histogram=function(){function Z(G){if(this.dataPoints=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.dataPoints=H1.emptyArray,Z.prototype.aggregationTemporality=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=W4.create();if(F.dataPoints!=null&&F.dataPoints.length)for(var Y=0;Y<F.dataPoints.length;++Y)X1.opentelemetry.proto.metrics.v1.HistogramDataPoint.encode(F.dataPoints[Y],I.uint32(10).fork()).ldelim();if(F.aggregationTemporality!=null&&Object.hasOwnProperty.call(F,"aggregationTemporality"))I.uint32(16).int32(F.aggregationTemporality);return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof u0))F=u0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.metrics.v1.Histogram;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{if(!(W.dataPoints&&W.dataPoints.length))W.dataPoints=[];W.dataPoints.push(X1.opentelemetry.proto.metrics.v1.HistogramDataPoint.decode(F,F.uint32()));break}case 2:{W.aggregationTemporality=F.int32();break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof u0))F=new u0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.dataPoints!=null&&F.hasOwnProperty("dataPoints")){if(!Array.isArray(F.dataPoints))return"dataPoints: array expected";for(var I=0;I<F.dataPoints.length;++I){var Y=X1.opentelemetry.proto.metrics.v1.HistogramDataPoint.verify(F.dataPoints[I]);if(Y)return"dataPoints."+Y}}if(F.aggregationTemporality!=null&&F.hasOwnProperty("aggregationTemporality"))switch(F.aggregationTemporality){default:return"aggregationTemporality: enum value expected";case 0:case 1:case 2:break}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.metrics.v1.Histogram)return F;var I=new X1.opentelemetry.proto.metrics.v1.Histogram;if(F.dataPoints){if(!Array.isArray(F.dataPoints))throw TypeError(".opentelemetry.proto.metrics.v1.Histogram.dataPoints: array expected");I.dataPoints=[];for(var Y=0;Y<F.dataPoints.length;++Y){if(typeof F.dataPoints[Y]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.Histogram.dataPoints: object expected");I.dataPoints[Y]=X1.opentelemetry.proto.metrics.v1.HistogramDataPoint.fromObject(F.dataPoints[Y])}}switch(F.aggregationTemporality){default:if(typeof F.aggregationTemporality==="number"){I.aggregationTemporality=F.aggregationTemporality;break}break;case"AGGREGATION_TEMPORALITY_UNSPECIFIED":case 0:I.aggregationTemporality=0;break;case"AGGREGATION_TEMPORALITY_DELTA":case 1:I.aggregationTemporality=1;break;case"AGGREGATION_TEMPORALITY_CUMULATIVE":case 2:I.aggregationTemporality=2;break}return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.dataPoints=[];if(I.defaults)Y.aggregationTemporality=I.enums===String?"AGGREGATION_TEMPORALITY_UNSPECIFIED":0;if(F.dataPoints&&F.dataPoints.length){Y.dataPoints=[];for(var W=0;W<F.dataPoints.length;++W)Y.dataPoints[W]=X1.opentelemetry.proto.metrics.v1.HistogramDataPoint.toObject(F.dataPoints[W],I)}if(F.aggregationTemporality!=null&&F.hasOwnProperty("aggregationTemporality"))Y.aggregationTemporality=I.enums===String?X1.opentelemetry.proto.metrics.v1.AggregationTemporality[F.aggregationTemporality]===void 0?F.aggregationTemporality:X1.opentelemetry.proto.metrics.v1.AggregationTemporality[F.aggregationTemporality]:F.aggregationTemporality;return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,n9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.metrics.v1.Histogram"},Z}(),D.ExponentialHistogram=function(){function Z(G){if(this.dataPoints=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.dataPoints=H1.emptyArray,Z.prototype.aggregationTemporality=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=W4.create();if(F.dataPoints!=null&&F.dataPoints.length)for(var Y=0;Y<F.dataPoints.length;++Y)X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.encode(F.dataPoints[Y],I.uint32(10).fork()).ldelim();if(F.aggregationTemporality!=null&&Object.hasOwnProperty.call(F,"aggregationTemporality"))I.uint32(16).int32(F.aggregationTemporality);return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof u0))F=u0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.metrics.v1.ExponentialHistogram;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{if(!(W.dataPoints&&W.dataPoints.length))W.dataPoints=[];W.dataPoints.push(X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.decode(F,F.uint32()));break}case 2:{W.aggregationTemporality=F.int32();break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof u0))F=new u0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.dataPoints!=null&&F.hasOwnProperty("dataPoints")){if(!Array.isArray(F.dataPoints))return"dataPoints: array expected";for(var I=0;I<F.dataPoints.length;++I){var Y=X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.verify(F.dataPoints[I]);if(Y)return"dataPoints."+Y}}if(F.aggregationTemporality!=null&&F.hasOwnProperty("aggregationTemporality"))switch(F.aggregationTemporality){default:return"aggregationTemporality: enum value expected";case 0:case 1:case 2:break}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.metrics.v1.ExponentialHistogram)return F;var I=new X1.opentelemetry.proto.metrics.v1.ExponentialHistogram;if(F.dataPoints){if(!Array.isArray(F.dataPoints))throw TypeError(".opentelemetry.proto.metrics.v1.ExponentialHistogram.dataPoints: array expected");I.dataPoints=[];for(var Y=0;Y<F.dataPoints.length;++Y){if(typeof F.dataPoints[Y]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.ExponentialHistogram.dataPoints: object expected");I.dataPoints[Y]=X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.fromObject(F.dataPoints[Y])}}switch(F.aggregationTemporality){default:if(typeof F.aggregationTemporality==="number"){I.aggregationTemporality=F.aggregationTemporality;break}break;case"AGGREGATION_TEMPORALITY_UNSPECIFIED":case 0:I.aggregationTemporality=0;break;case"AGGREGATION_TEMPORALITY_DELTA":case 1:I.aggregationTemporality=1;break;case"AGGREGATION_TEMPORALITY_CUMULATIVE":case 2:I.aggregationTemporality=2;break}return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.dataPoints=[];if(I.defaults)Y.aggregationTemporality=I.enums===String?"AGGREGATION_TEMPORALITY_UNSPECIFIED":0;if(F.dataPoints&&F.dataPoints.length){Y.dataPoints=[];for(var W=0;W<F.dataPoints.length;++W)Y.dataPoints[W]=X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.toObject(F.dataPoints[W],I)}if(F.aggregationTemporality!=null&&F.hasOwnProperty("aggregationTemporality"))Y.aggregationTemporality=I.enums===String?X1.opentelemetry.proto.metrics.v1.AggregationTemporality[F.aggregationTemporality]===void 0?F.aggregationTemporality:X1.opentelemetry.proto.metrics.v1.AggregationTemporality[F.aggregationTemporality]:F.aggregationTemporality;return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,n9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.metrics.v1.ExponentialHistogram"},Z}(),D.Summary=function(){function Z(G){if(this.dataPoints=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.dataPoints=H1.emptyArray,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=W4.create();if(F.dataPoints!=null&&F.dataPoints.length)for(var Y=0;Y<F.dataPoints.length;++Y)X1.opentelemetry.proto.metrics.v1.SummaryDataPoint.encode(F.dataPoints[Y],I.uint32(10).fork()).ldelim();return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof u0))F=u0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.metrics.v1.Summary;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{if(!(W.dataPoints&&W.dataPoints.length))W.dataPoints=[];W.dataPoints.push(X1.opentelemetry.proto.metrics.v1.SummaryDataPoint.decode(F,F.uint32()));break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof u0))F=new u0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.dataPoints!=null&&F.hasOwnProperty("dataPoints")){if(!Array.isArray(F.dataPoints))return"dataPoints: array expected";for(var I=0;I<F.dataPoints.length;++I){var Y=X1.opentelemetry.proto.metrics.v1.SummaryDataPoint.verify(F.dataPoints[I]);if(Y)return"dataPoints."+Y}}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.metrics.v1.Summary)return F;var I=new X1.opentelemetry.proto.metrics.v1.Summary;if(F.dataPoints){if(!Array.isArray(F.dataPoints))throw TypeError(".opentelemetry.proto.metrics.v1.Summary.dataPoints: array expected");I.dataPoints=[];for(var Y=0;Y<F.dataPoints.length;++Y){if(typeof F.dataPoints[Y]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.Summary.dataPoints: object expected");I.dataPoints[Y]=X1.opentelemetry.proto.metrics.v1.SummaryDataPoint.fromObject(F.dataPoints[Y])}}return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.dataPoints=[];if(F.dataPoints&&F.dataPoints.length){Y.dataPoints=[];for(var W=0;W<F.dataPoints.length;++W)Y.dataPoints[W]=X1.opentelemetry.proto.metrics.v1.SummaryDataPoint.toObject(F.dataPoints[W],I)}return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,n9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.metrics.v1.Summary"},Z}(),D.AggregationTemporality=function(){var Z={},G=Object.create(Z);return G[Z[0]="AGGREGATION_TEMPORALITY_UNSPECIFIED"]=0,G[Z[1]="AGGREGATION_TEMPORALITY_DELTA"]=1,G[Z[2]="AGGREGATION_TEMPORALITY_CUMULATIVE"]=2,G}(),D.DataPointFlags=function(){var Z={},G=Object.create(Z);return G[Z[0]="DATA_POINT_FLAGS_DO_NOT_USE"]=0,G[Z[1]="DATA_POINT_FLAGS_NO_RECORDED_VALUE_MASK"]=1,G}(),D.NumberDataPoint=function(){function Z(F){if(this.attributes=[],this.exemplars=[],F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}Z.prototype.attributes=H1.emptyArray,Z.prototype.startTimeUnixNano=null,Z.prototype.timeUnixNano=null,Z.prototype.asDouble=null,Z.prototype.asInt=null,Z.prototype.exemplars=H1.emptyArray,Z.prototype.flags=null;var G;return Object.defineProperty(Z.prototype,"value",{get:H1.oneOfGetter(G=["asDouble","asInt"]),set:H1.oneOfSetter(G)}),Z.create=function F(I){return new Z(I)},Z.encode=function F(I,Y){if(!Y)Y=W4.create();if(I.startTimeUnixNano!=null&&Object.hasOwnProperty.call(I,"startTimeUnixNano"))Y.uint32(17).fixed64(I.startTimeUnixNano);if(I.timeUnixNano!=null&&Object.hasOwnProperty.call(I,"timeUnixNano"))Y.uint32(25).fixed64(I.timeUnixNano);if(I.asDouble!=null&&Object.hasOwnProperty.call(I,"asDouble"))Y.uint32(33).double(I.asDouble);if(I.exemplars!=null&&I.exemplars.length)for(var W=0;W<I.exemplars.length;++W)X1.opentelemetry.proto.metrics.v1.Exemplar.encode(I.exemplars[W],Y.uint32(42).fork()).ldelim();if(I.asInt!=null&&Object.hasOwnProperty.call(I,"asInt"))Y.uint32(49).sfixed64(I.asInt);if(I.attributes!=null&&I.attributes.length)for(var W=0;W<I.attributes.length;++W)X1.opentelemetry.proto.common.v1.KeyValue.encode(I.attributes[W],Y.uint32(58).fork()).ldelim();if(I.flags!=null&&Object.hasOwnProperty.call(I,"flags"))Y.uint32(64).uint32(I.flags);return Y},Z.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},Z.decode=function F(I,Y){if(!(I instanceof u0))I=u0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.metrics.v1.NumberDataPoint;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 7:{if(!(J.attributes&&J.attributes.length))J.attributes=[];J.attributes.push(X1.opentelemetry.proto.common.v1.KeyValue.decode(I,I.uint32()));break}case 2:{J.startTimeUnixNano=I.fixed64();break}case 3:{J.timeUnixNano=I.fixed64();break}case 4:{J.asDouble=I.double();break}case 6:{J.asInt=I.sfixed64();break}case 5:{if(!(J.exemplars&&J.exemplars.length))J.exemplars=[];J.exemplars.push(X1.opentelemetry.proto.metrics.v1.Exemplar.decode(I,I.uint32()));break}case 8:{J.flags=I.uint32();break}default:I.skipType(X&7);break}}return J},Z.decodeDelimited=function F(I){if(!(I instanceof u0))I=new u0(I);return this.decode(I,I.uint32())},Z.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";var Y={};if(I.attributes!=null&&I.hasOwnProperty("attributes")){if(!Array.isArray(I.attributes))return"attributes: array expected";for(var W=0;W<I.attributes.length;++W){var J=X1.opentelemetry.proto.common.v1.KeyValue.verify(I.attributes[W]);if(J)return"attributes."+J}}if(I.startTimeUnixNano!=null&&I.hasOwnProperty("startTimeUnixNano")){if(!H1.isInteger(I.startTimeUnixNano)&&!(I.startTimeUnixNano&&H1.isInteger(I.startTimeUnixNano.low)&&H1.isInteger(I.startTimeUnixNano.high)))return"startTimeUnixNano: integer|Long expected"}if(I.timeUnixNano!=null&&I.hasOwnProperty("timeUnixNano")){if(!H1.isInteger(I.timeUnixNano)&&!(I.timeUnixNano&&H1.isInteger(I.timeUnixNano.low)&&H1.isInteger(I.timeUnixNano.high)))return"timeUnixNano: integer|Long expected"}if(I.asDouble!=null&&I.hasOwnProperty("asDouble")){if(Y.value=1,typeof I.asDouble!=="number")return"asDouble: number expected"}if(I.asInt!=null&&I.hasOwnProperty("asInt")){if(Y.value===1)return"value: multiple values";if(Y.value=1,!H1.isInteger(I.asInt)&&!(I.asInt&&H1.isInteger(I.asInt.low)&&H1.isInteger(I.asInt.high)))return"asInt: integer|Long expected"}if(I.exemplars!=null&&I.hasOwnProperty("exemplars")){if(!Array.isArray(I.exemplars))return"exemplars: array expected";for(var W=0;W<I.exemplars.length;++W){var J=X1.opentelemetry.proto.metrics.v1.Exemplar.verify(I.exemplars[W]);if(J)return"exemplars."+J}}if(I.flags!=null&&I.hasOwnProperty("flags")){if(!H1.isInteger(I.flags))return"flags: integer expected"}return null},Z.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.metrics.v1.NumberDataPoint)return I;var Y=new X1.opentelemetry.proto.metrics.v1.NumberDataPoint;if(I.attributes){if(!Array.isArray(I.attributes))throw TypeError(".opentelemetry.proto.metrics.v1.NumberDataPoint.attributes: array expected");Y.attributes=[];for(var W=0;W<I.attributes.length;++W){if(typeof I.attributes[W]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.NumberDataPoint.attributes: object expected");Y.attributes[W]=X1.opentelemetry.proto.common.v1.KeyValue.fromObject(I.attributes[W])}}if(I.startTimeUnixNano!=null){if(H1.Long)(Y.startTimeUnixNano=H1.Long.fromValue(I.startTimeUnixNano)).unsigned=!1;else if(typeof I.startTimeUnixNano==="string")Y.startTimeUnixNano=parseInt(I.startTimeUnixNano,10);else if(typeof I.startTimeUnixNano==="number")Y.startTimeUnixNano=I.startTimeUnixNano;else if(typeof I.startTimeUnixNano==="object")Y.startTimeUnixNano=new H1.LongBits(I.startTimeUnixNano.low>>>0,I.startTimeUnixNano.high>>>0).toNumber()}if(I.timeUnixNano!=null){if(H1.Long)(Y.timeUnixNano=H1.Long.fromValue(I.timeUnixNano)).unsigned=!1;else if(typeof I.timeUnixNano==="string")Y.timeUnixNano=parseInt(I.timeUnixNano,10);else if(typeof I.timeUnixNano==="number")Y.timeUnixNano=I.timeUnixNano;else if(typeof I.timeUnixNano==="object")Y.timeUnixNano=new H1.LongBits(I.timeUnixNano.low>>>0,I.timeUnixNano.high>>>0).toNumber()}if(I.asDouble!=null)Y.asDouble=Number(I.asDouble);if(I.asInt!=null){if(H1.Long)(Y.asInt=H1.Long.fromValue(I.asInt)).unsigned=!1;else if(typeof I.asInt==="string")Y.asInt=parseInt(I.asInt,10);else if(typeof I.asInt==="number")Y.asInt=I.asInt;else if(typeof I.asInt==="object")Y.asInt=new H1.LongBits(I.asInt.low>>>0,I.asInt.high>>>0).toNumber()}if(I.exemplars){if(!Array.isArray(I.exemplars))throw TypeError(".opentelemetry.proto.metrics.v1.NumberDataPoint.exemplars: array expected");Y.exemplars=[];for(var W=0;W<I.exemplars.length;++W){if(typeof I.exemplars[W]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.NumberDataPoint.exemplars: object expected");Y.exemplars[W]=X1.opentelemetry.proto.metrics.v1.Exemplar.fromObject(I.exemplars[W])}}if(I.flags!=null)Y.flags=I.flags>>>0;return Y},Z.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.arrays||Y.defaults)W.exemplars=[],W.attributes=[];if(Y.defaults){if(H1.Long){var J=new H1.Long(0,0,!1);W.startTimeUnixNano=Y.longs===String?J.toString():Y.longs===Number?J.toNumber():J}else W.startTimeUnixNano=Y.longs===String?"0":0;if(H1.Long){var J=new H1.Long(0,0,!1);W.timeUnixNano=Y.longs===String?J.toString():Y.longs===Number?J.toNumber():J}else W.timeUnixNano=Y.longs===String?"0":0;W.flags=0}if(I.startTimeUnixNano!=null&&I.hasOwnProperty("startTimeUnixNano"))if(typeof I.startTimeUnixNano==="number")W.startTimeUnixNano=Y.longs===String?String(I.startTimeUnixNano):I.startTimeUnixNano;else W.startTimeUnixNano=Y.longs===String?H1.Long.prototype.toString.call(I.startTimeUnixNano):Y.longs===Number?new H1.LongBits(I.startTimeUnixNano.low>>>0,I.startTimeUnixNano.high>>>0).toNumber():I.startTimeUnixNano;if(I.timeUnixNano!=null&&I.hasOwnProperty("timeUnixNano"))if(typeof I.timeUnixNano==="number")W.timeUnixNano=Y.longs===String?String(I.timeUnixNano):I.timeUnixNano;else W.timeUnixNano=Y.longs===String?H1.Long.prototype.toString.call(I.timeUnixNano):Y.longs===Number?new H1.LongBits(I.timeUnixNano.low>>>0,I.timeUnixNano.high>>>0).toNumber():I.timeUnixNano;if(I.asDouble!=null&&I.hasOwnProperty("asDouble")){if(W.asDouble=Y.json&&!isFinite(I.asDouble)?String(I.asDouble):I.asDouble,Y.oneofs)W.value="asDouble"}if(I.exemplars&&I.exemplars.length){W.exemplars=[];for(var X=0;X<I.exemplars.length;++X)W.exemplars[X]=X1.opentelemetry.proto.metrics.v1.Exemplar.toObject(I.exemplars[X],Y)}if(I.asInt!=null&&I.hasOwnProperty("asInt")){if(typeof I.asInt==="number")W.asInt=Y.longs===String?String(I.asInt):I.asInt;else W.asInt=Y.longs===String?H1.Long.prototype.toString.call(I.asInt):Y.longs===Number?new H1.LongBits(I.asInt.low>>>0,I.asInt.high>>>0).toNumber():I.asInt;if(Y.oneofs)W.value="asInt"}if(I.attributes&&I.attributes.length){W.attributes=[];for(var X=0;X<I.attributes.length;++X)W.attributes[X]=X1.opentelemetry.proto.common.v1.KeyValue.toObject(I.attributes[X],Y)}if(I.flags!=null&&I.hasOwnProperty("flags"))W.flags=I.flags;return W},Z.prototype.toJSON=function F(){return this.constructor.toObject(this,n9.util.toJSONOptions)},Z.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.metrics.v1.NumberDataPoint"},Z}(),D.HistogramDataPoint=function(){function Z(F){if(this.attributes=[],this.bucketCounts=[],this.explicitBounds=[],this.exemplars=[],F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}Z.prototype.attributes=H1.emptyArray,Z.prototype.startTimeUnixNano=null,Z.prototype.timeUnixNano=null,Z.prototype.count=null,Z.prototype.sum=null,Z.prototype.bucketCounts=H1.emptyArray,Z.prototype.explicitBounds=H1.emptyArray,Z.prototype.exemplars=H1.emptyArray,Z.prototype.flags=null,Z.prototype.min=null,Z.prototype.max=null;var G;return Object.defineProperty(Z.prototype,"_sum",{get:H1.oneOfGetter(G=["sum"]),set:H1.oneOfSetter(G)}),Object.defineProperty(Z.prototype,"_min",{get:H1.oneOfGetter(G=["min"]),set:H1.oneOfSetter(G)}),Object.defineProperty(Z.prototype,"_max",{get:H1.oneOfGetter(G=["max"]),set:H1.oneOfSetter(G)}),Z.create=function F(I){return new Z(I)},Z.encode=function F(I,Y){if(!Y)Y=W4.create();if(I.startTimeUnixNano!=null&&Object.hasOwnProperty.call(I,"startTimeUnixNano"))Y.uint32(17).fixed64(I.startTimeUnixNano);if(I.timeUnixNano!=null&&Object.hasOwnProperty.call(I,"timeUnixNano"))Y.uint32(25).fixed64(I.timeUnixNano);if(I.count!=null&&Object.hasOwnProperty.call(I,"count"))Y.uint32(33).fixed64(I.count);if(I.sum!=null&&Object.hasOwnProperty.call(I,"sum"))Y.uint32(41).double(I.sum);if(I.bucketCounts!=null&&I.bucketCounts.length){Y.uint32(50).fork();for(var W=0;W<I.bucketCounts.length;++W)Y.fixed64(I.bucketCounts[W]);Y.ldelim()}if(I.explicitBounds!=null&&I.explicitBounds.length){Y.uint32(58).fork();for(var W=0;W<I.explicitBounds.length;++W)Y.double(I.explicitBounds[W]);Y.ldelim()}if(I.exemplars!=null&&I.exemplars.length)for(var W=0;W<I.exemplars.length;++W)X1.opentelemetry.proto.metrics.v1.Exemplar.encode(I.exemplars[W],Y.uint32(66).fork()).ldelim();if(I.attributes!=null&&I.attributes.length)for(var W=0;W<I.attributes.length;++W)X1.opentelemetry.proto.common.v1.KeyValue.encode(I.attributes[W],Y.uint32(74).fork()).ldelim();if(I.flags!=null&&Object.hasOwnProperty.call(I,"flags"))Y.uint32(80).uint32(I.flags);if(I.min!=null&&Object.hasOwnProperty.call(I,"min"))Y.uint32(89).double(I.min);if(I.max!=null&&Object.hasOwnProperty.call(I,"max"))Y.uint32(97).double(I.max);return Y},Z.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},Z.decode=function F(I,Y){if(!(I instanceof u0))I=u0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.metrics.v1.HistogramDataPoint;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 9:{if(!(J.attributes&&J.attributes.length))J.attributes=[];J.attributes.push(X1.opentelemetry.proto.common.v1.KeyValue.decode(I,I.uint32()));break}case 2:{J.startTimeUnixNano=I.fixed64();break}case 3:{J.timeUnixNano=I.fixed64();break}case 4:{J.count=I.fixed64();break}case 5:{J.sum=I.double();break}case 6:{if(!(J.bucketCounts&&J.bucketCounts.length))J.bucketCounts=[];if((X&7)===2){var V=I.uint32()+I.pos;while(I.pos<V)J.bucketCounts.push(I.fixed64())}else J.bucketCounts.push(I.fixed64());break}case 7:{if(!(J.explicitBounds&&J.explicitBounds.length))J.explicitBounds=[];if((X&7)===2){var V=I.uint32()+I.pos;while(I.pos<V)J.explicitBounds.push(I.double())}else J.explicitBounds.push(I.double());break}case 8:{if(!(J.exemplars&&J.exemplars.length))J.exemplars=[];J.exemplars.push(X1.opentelemetry.proto.metrics.v1.Exemplar.decode(I,I.uint32()));break}case 10:{J.flags=I.uint32();break}case 11:{J.min=I.double();break}case 12:{J.max=I.double();break}default:I.skipType(X&7);break}}return J},Z.decodeDelimited=function F(I){if(!(I instanceof u0))I=new u0(I);return this.decode(I,I.uint32())},Z.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";var Y={};if(I.attributes!=null&&I.hasOwnProperty("attributes")){if(!Array.isArray(I.attributes))return"attributes: array expected";for(var W=0;W<I.attributes.length;++W){var J=X1.opentelemetry.proto.common.v1.KeyValue.verify(I.attributes[W]);if(J)return"attributes."+J}}if(I.startTimeUnixNano!=null&&I.hasOwnProperty("startTimeUnixNano")){if(!H1.isInteger(I.startTimeUnixNano)&&!(I.startTimeUnixNano&&H1.isInteger(I.startTimeUnixNano.low)&&H1.isInteger(I.startTimeUnixNano.high)))return"startTimeUnixNano: integer|Long expected"}if(I.timeUnixNano!=null&&I.hasOwnProperty("timeUnixNano")){if(!H1.isInteger(I.timeUnixNano)&&!(I.timeUnixNano&&H1.isInteger(I.timeUnixNano.low)&&H1.isInteger(I.timeUnixNano.high)))return"timeUnixNano: integer|Long expected"}if(I.count!=null&&I.hasOwnProperty("count")){if(!H1.isInteger(I.count)&&!(I.count&&H1.isInteger(I.count.low)&&H1.isInteger(I.count.high)))return"count: integer|Long expected"}if(I.sum!=null&&I.hasOwnProperty("sum")){if(Y._sum=1,typeof I.sum!=="number")return"sum: number expected"}if(I.bucketCounts!=null&&I.hasOwnProperty("bucketCounts")){if(!Array.isArray(I.bucketCounts))return"bucketCounts: array expected";for(var W=0;W<I.bucketCounts.length;++W)if(!H1.isInteger(I.bucketCounts[W])&&!(I.bucketCounts[W]&&H1.isInteger(I.bucketCounts[W].low)&&H1.isInteger(I.bucketCounts[W].high)))return"bucketCounts: integer|Long[] expected"}if(I.explicitBounds!=null&&I.hasOwnProperty("explicitBounds")){if(!Array.isArray(I.explicitBounds))return"explicitBounds: array expected";for(var W=0;W<I.explicitBounds.length;++W)if(typeof I.explicitBounds[W]!=="number")return"explicitBounds: number[] expected"}if(I.exemplars!=null&&I.hasOwnProperty("exemplars")){if(!Array.isArray(I.exemplars))return"exemplars: array expected";for(var W=0;W<I.exemplars.length;++W){var J=X1.opentelemetry.proto.metrics.v1.Exemplar.verify(I.exemplars[W]);if(J)return"exemplars."+J}}if(I.flags!=null&&I.hasOwnProperty("flags")){if(!H1.isInteger(I.flags))return"flags: integer expected"}if(I.min!=null&&I.hasOwnProperty("min")){if(Y._min=1,typeof I.min!=="number")return"min: number expected"}if(I.max!=null&&I.hasOwnProperty("max")){if(Y._max=1,typeof I.max!=="number")return"max: number expected"}return null},Z.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.metrics.v1.HistogramDataPoint)return I;var Y=new X1.opentelemetry.proto.metrics.v1.HistogramDataPoint;if(I.attributes){if(!Array.isArray(I.attributes))throw TypeError(".opentelemetry.proto.metrics.v1.HistogramDataPoint.attributes: array expected");Y.attributes=[];for(var W=0;W<I.attributes.length;++W){if(typeof I.attributes[W]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.HistogramDataPoint.attributes: object expected");Y.attributes[W]=X1.opentelemetry.proto.common.v1.KeyValue.fromObject(I.attributes[W])}}if(I.startTimeUnixNano!=null){if(H1.Long)(Y.startTimeUnixNano=H1.Long.fromValue(I.startTimeUnixNano)).unsigned=!1;else if(typeof I.startTimeUnixNano==="string")Y.startTimeUnixNano=parseInt(I.startTimeUnixNano,10);else if(typeof I.startTimeUnixNano==="number")Y.startTimeUnixNano=I.startTimeUnixNano;else if(typeof I.startTimeUnixNano==="object")Y.startTimeUnixNano=new H1.LongBits(I.startTimeUnixNano.low>>>0,I.startTimeUnixNano.high>>>0).toNumber()}if(I.timeUnixNano!=null){if(H1.Long)(Y.timeUnixNano=H1.Long.fromValue(I.timeUnixNano)).unsigned=!1;else if(typeof I.timeUnixNano==="string")Y.timeUnixNano=parseInt(I.timeUnixNano,10);else if(typeof I.timeUnixNano==="number")Y.timeUnixNano=I.timeUnixNano;else if(typeof I.timeUnixNano==="object")Y.timeUnixNano=new H1.LongBits(I.timeUnixNano.low>>>0,I.timeUnixNano.high>>>0).toNumber()}if(I.count!=null){if(H1.Long)(Y.count=H1.Long.fromValue(I.count)).unsigned=!1;else if(typeof I.count==="string")Y.count=parseInt(I.count,10);else if(typeof I.count==="number")Y.count=I.count;else if(typeof I.count==="object")Y.count=new H1.LongBits(I.count.low>>>0,I.count.high>>>0).toNumber()}if(I.sum!=null)Y.sum=Number(I.sum);if(I.bucketCounts){if(!Array.isArray(I.bucketCounts))throw TypeError(".opentelemetry.proto.metrics.v1.HistogramDataPoint.bucketCounts: array expected");Y.bucketCounts=[];for(var W=0;W<I.bucketCounts.length;++W)if(H1.Long)(Y.bucketCounts[W]=H1.Long.fromValue(I.bucketCounts[W])).unsigned=!1;else if(typeof I.bucketCounts[W]==="string")Y.bucketCounts[W]=parseInt(I.bucketCounts[W],10);else if(typeof I.bucketCounts[W]==="number")Y.bucketCounts[W]=I.bucketCounts[W];else if(typeof I.bucketCounts[W]==="object")Y.bucketCounts[W]=new H1.LongBits(I.bucketCounts[W].low>>>0,I.bucketCounts[W].high>>>0).toNumber()}if(I.explicitBounds){if(!Array.isArray(I.explicitBounds))throw TypeError(".opentelemetry.proto.metrics.v1.HistogramDataPoint.explicitBounds: array expected");Y.explicitBounds=[];for(var W=0;W<I.explicitBounds.length;++W)Y.explicitBounds[W]=Number(I.explicitBounds[W])}if(I.exemplars){if(!Array.isArray(I.exemplars))throw TypeError(".opentelemetry.proto.metrics.v1.HistogramDataPoint.exemplars: array expected");Y.exemplars=[];for(var W=0;W<I.exemplars.length;++W){if(typeof I.exemplars[W]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.HistogramDataPoint.exemplars: object expected");Y.exemplars[W]=X1.opentelemetry.proto.metrics.v1.Exemplar.fromObject(I.exemplars[W])}}if(I.flags!=null)Y.flags=I.flags>>>0;if(I.min!=null)Y.min=Number(I.min);if(I.max!=null)Y.max=Number(I.max);return Y},Z.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.arrays||Y.defaults)W.bucketCounts=[],W.explicitBounds=[],W.exemplars=[],W.attributes=[];if(Y.defaults){if(H1.Long){var J=new H1.Long(0,0,!1);W.startTimeUnixNano=Y.longs===String?J.toString():Y.longs===Number?J.toNumber():J}else W.startTimeUnixNano=Y.longs===String?"0":0;if(H1.Long){var J=new H1.Long(0,0,!1);W.timeUnixNano=Y.longs===String?J.toString():Y.longs===Number?J.toNumber():J}else W.timeUnixNano=Y.longs===String?"0":0;if(H1.Long){var J=new H1.Long(0,0,!1);W.count=Y.longs===String?J.toString():Y.longs===Number?J.toNumber():J}else W.count=Y.longs===String?"0":0;W.flags=0}if(I.startTimeUnixNano!=null&&I.hasOwnProperty("startTimeUnixNano"))if(typeof I.startTimeUnixNano==="number")W.startTimeUnixNano=Y.longs===String?String(I.startTimeUnixNano):I.startTimeUnixNano;else W.startTimeUnixNano=Y.longs===String?H1.Long.prototype.toString.call(I.startTimeUnixNano):Y.longs===Number?new H1.LongBits(I.startTimeUnixNano.low>>>0,I.startTimeUnixNano.high>>>0).toNumber():I.startTimeUnixNano;if(I.timeUnixNano!=null&&I.hasOwnProperty("timeUnixNano"))if(typeof I.timeUnixNano==="number")W.timeUnixNano=Y.longs===String?String(I.timeUnixNano):I.timeUnixNano;else W.timeUnixNano=Y.longs===String?H1.Long.prototype.toString.call(I.timeUnixNano):Y.longs===Number?new H1.LongBits(I.timeUnixNano.low>>>0,I.timeUnixNano.high>>>0).toNumber():I.timeUnixNano;if(I.count!=null&&I.hasOwnProperty("count"))if(typeof I.count==="number")W.count=Y.longs===String?String(I.count):I.count;else W.count=Y.longs===String?H1.Long.prototype.toString.call(I.count):Y.longs===Number?new H1.LongBits(I.count.low>>>0,I.count.high>>>0).toNumber():I.count;if(I.sum!=null&&I.hasOwnProperty("sum")){if(W.sum=Y.json&&!isFinite(I.sum)?String(I.sum):I.sum,Y.oneofs)W._sum="sum"}if(I.bucketCounts&&I.bucketCounts.length){W.bucketCounts=[];for(var X=0;X<I.bucketCounts.length;++X)if(typeof I.bucketCounts[X]==="number")W.bucketCounts[X]=Y.longs===String?String(I.bucketCounts[X]):I.bucketCounts[X];else W.bucketCounts[X]=Y.longs===String?H1.Long.prototype.toString.call(I.bucketCounts[X]):Y.longs===Number?new H1.LongBits(I.bucketCounts[X].low>>>0,I.bucketCounts[X].high>>>0).toNumber():I.bucketCounts[X]}if(I.explicitBounds&&I.explicitBounds.length){W.explicitBounds=[];for(var X=0;X<I.explicitBounds.length;++X)W.explicitBounds[X]=Y.json&&!isFinite(I.explicitBounds[X])?String(I.explicitBounds[X]):I.explicitBounds[X]}if(I.exemplars&&I.exemplars.length){W.exemplars=[];for(var X=0;X<I.exemplars.length;++X)W.exemplars[X]=X1.opentelemetry.proto.metrics.v1.Exemplar.toObject(I.exemplars[X],Y)}if(I.attributes&&I.attributes.length){W.attributes=[];for(var X=0;X<I.attributes.length;++X)W.attributes[X]=X1.opentelemetry.proto.common.v1.KeyValue.toObject(I.attributes[X],Y)}if(I.flags!=null&&I.hasOwnProperty("flags"))W.flags=I.flags;if(I.min!=null&&I.hasOwnProperty("min")){if(W.min=Y.json&&!isFinite(I.min)?String(I.min):I.min,Y.oneofs)W._min="min"}if(I.max!=null&&I.hasOwnProperty("max")){if(W.max=Y.json&&!isFinite(I.max)?String(I.max):I.max,Y.oneofs)W._max="max"}return W},Z.prototype.toJSON=function F(){return this.constructor.toObject(this,n9.util.toJSONOptions)},Z.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.metrics.v1.HistogramDataPoint"},Z}(),D.ExponentialHistogramDataPoint=function(){function Z(F){if(this.attributes=[],this.exemplars=[],F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}Z.prototype.attributes=H1.emptyArray,Z.prototype.startTimeUnixNano=null,Z.prototype.timeUnixNano=null,Z.prototype.count=null,Z.prototype.sum=null,Z.prototype.scale=null,Z.prototype.zeroCount=null,Z.prototype.positive=null,Z.prototype.negative=null,Z.prototype.flags=null,Z.prototype.exemplars=H1.emptyArray,Z.prototype.min=null,Z.prototype.max=null,Z.prototype.zeroThreshold=null;var G;return Object.defineProperty(Z.prototype,"_sum",{get:H1.oneOfGetter(G=["sum"]),set:H1.oneOfSetter(G)}),Object.defineProperty(Z.prototype,"_min",{get:H1.oneOfGetter(G=["min"]),set:H1.oneOfSetter(G)}),Object.defineProperty(Z.prototype,"_max",{get:H1.oneOfGetter(G=["max"]),set:H1.oneOfSetter(G)}),Z.create=function F(I){return new Z(I)},Z.encode=function F(I,Y){if(!Y)Y=W4.create();if(I.attributes!=null&&I.attributes.length)for(var W=0;W<I.attributes.length;++W)X1.opentelemetry.proto.common.v1.KeyValue.encode(I.attributes[W],Y.uint32(10).fork()).ldelim();if(I.startTimeUnixNano!=null&&Object.hasOwnProperty.call(I,"startTimeUnixNano"))Y.uint32(17).fixed64(I.startTimeUnixNano);if(I.timeUnixNano!=null&&Object.hasOwnProperty.call(I,"timeUnixNano"))Y.uint32(25).fixed64(I.timeUnixNano);if(I.count!=null&&Object.hasOwnProperty.call(I,"count"))Y.uint32(33).fixed64(I.count);if(I.sum!=null&&Object.hasOwnProperty.call(I,"sum"))Y.uint32(41).double(I.sum);if(I.scale!=null&&Object.hasOwnProperty.call(I,"scale"))Y.uint32(48).sint32(I.scale);if(I.zeroCount!=null&&Object.hasOwnProperty.call(I,"zeroCount"))Y.uint32(57).fixed64(I.zeroCount);if(I.positive!=null&&Object.hasOwnProperty.call(I,"positive"))X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.encode(I.positive,Y.uint32(66).fork()).ldelim();if(I.negative!=null&&Object.hasOwnProperty.call(I,"negative"))X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.encode(I.negative,Y.uint32(74).fork()).ldelim();if(I.flags!=null&&Object.hasOwnProperty.call(I,"flags"))Y.uint32(80).uint32(I.flags);if(I.exemplars!=null&&I.exemplars.length)for(var W=0;W<I.exemplars.length;++W)X1.opentelemetry.proto.metrics.v1.Exemplar.encode(I.exemplars[W],Y.uint32(90).fork()).ldelim();if(I.min!=null&&Object.hasOwnProperty.call(I,"min"))Y.uint32(97).double(I.min);if(I.max!=null&&Object.hasOwnProperty.call(I,"max"))Y.uint32(105).double(I.max);if(I.zeroThreshold!=null&&Object.hasOwnProperty.call(I,"zeroThreshold"))Y.uint32(113).double(I.zeroThreshold);return Y},Z.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},Z.decode=function F(I,Y){if(!(I instanceof u0))I=u0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 1:{if(!(J.attributes&&J.attributes.length))J.attributes=[];J.attributes.push(X1.opentelemetry.proto.common.v1.KeyValue.decode(I,I.uint32()));break}case 2:{J.startTimeUnixNano=I.fixed64();break}case 3:{J.timeUnixNano=I.fixed64();break}case 4:{J.count=I.fixed64();break}case 5:{J.sum=I.double();break}case 6:{J.scale=I.sint32();break}case 7:{J.zeroCount=I.fixed64();break}case 8:{J.positive=X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.decode(I,I.uint32());break}case 9:{J.negative=X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.decode(I,I.uint32());break}case 10:{J.flags=I.uint32();break}case 11:{if(!(J.exemplars&&J.exemplars.length))J.exemplars=[];J.exemplars.push(X1.opentelemetry.proto.metrics.v1.Exemplar.decode(I,I.uint32()));break}case 12:{J.min=I.double();break}case 13:{J.max=I.double();break}case 14:{J.zeroThreshold=I.double();break}default:I.skipType(X&7);break}}return J},Z.decodeDelimited=function F(I){if(!(I instanceof u0))I=new u0(I);return this.decode(I,I.uint32())},Z.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";var Y={};if(I.attributes!=null&&I.hasOwnProperty("attributes")){if(!Array.isArray(I.attributes))return"attributes: array expected";for(var W=0;W<I.attributes.length;++W){var J=X1.opentelemetry.proto.common.v1.KeyValue.verify(I.attributes[W]);if(J)return"attributes."+J}}if(I.startTimeUnixNano!=null&&I.hasOwnProperty("startTimeUnixNano")){if(!H1.isInteger(I.startTimeUnixNano)&&!(I.startTimeUnixNano&&H1.isInteger(I.startTimeUnixNano.low)&&H1.isInteger(I.startTimeUnixNano.high)))return"startTimeUnixNano: integer|Long expected"}if(I.timeUnixNano!=null&&I.hasOwnProperty("timeUnixNano")){if(!H1.isInteger(I.timeUnixNano)&&!(I.timeUnixNano&&H1.isInteger(I.timeUnixNano.low)&&H1.isInteger(I.timeUnixNano.high)))return"timeUnixNano: integer|Long expected"}if(I.count!=null&&I.hasOwnProperty("count")){if(!H1.isInteger(I.count)&&!(I.count&&H1.isInteger(I.count.low)&&H1.isInteger(I.count.high)))return"count: integer|Long expected"}if(I.sum!=null&&I.hasOwnProperty("sum")){if(Y._sum=1,typeof I.sum!=="number")return"sum: number expected"}if(I.scale!=null&&I.hasOwnProperty("scale")){if(!H1.isInteger(I.scale))return"scale: integer expected"}if(I.zeroCount!=null&&I.hasOwnProperty("zeroCount")){if(!H1.isInteger(I.zeroCount)&&!(I.zeroCount&&H1.isInteger(I.zeroCount.low)&&H1.isInteger(I.zeroCount.high)))return"zeroCount: integer|Long expected"}if(I.positive!=null&&I.hasOwnProperty("positive")){var J=X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.verify(I.positive);if(J)return"positive."+J}if(I.negative!=null&&I.hasOwnProperty("negative")){var J=X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.verify(I.negative);if(J)return"negative."+J}if(I.flags!=null&&I.hasOwnProperty("flags")){if(!H1.isInteger(I.flags))return"flags: integer expected"}if(I.exemplars!=null&&I.hasOwnProperty("exemplars")){if(!Array.isArray(I.exemplars))return"exemplars: array expected";for(var W=0;W<I.exemplars.length;++W){var J=X1.opentelemetry.proto.metrics.v1.Exemplar.verify(I.exemplars[W]);if(J)return"exemplars."+J}}if(I.min!=null&&I.hasOwnProperty("min")){if(Y._min=1,typeof I.min!=="number")return"min: number expected"}if(I.max!=null&&I.hasOwnProperty("max")){if(Y._max=1,typeof I.max!=="number")return"max: number expected"}if(I.zeroThreshold!=null&&I.hasOwnProperty("zeroThreshold")){if(typeof I.zeroThreshold!=="number")return"zeroThreshold: number expected"}return null},Z.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint)return I;var Y=new X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint;if(I.attributes){if(!Array.isArray(I.attributes))throw TypeError(".opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.attributes: array expected");Y.attributes=[];for(var W=0;W<I.attributes.length;++W){if(typeof I.attributes[W]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.attributes: object expected");Y.attributes[W]=X1.opentelemetry.proto.common.v1.KeyValue.fromObject(I.attributes[W])}}if(I.startTimeUnixNano!=null){if(H1.Long)(Y.startTimeUnixNano=H1.Long.fromValue(I.startTimeUnixNano)).unsigned=!1;else if(typeof I.startTimeUnixNano==="string")Y.startTimeUnixNano=parseInt(I.startTimeUnixNano,10);else if(typeof I.startTimeUnixNano==="number")Y.startTimeUnixNano=I.startTimeUnixNano;else if(typeof I.startTimeUnixNano==="object")Y.startTimeUnixNano=new H1.LongBits(I.startTimeUnixNano.low>>>0,I.startTimeUnixNano.high>>>0).toNumber()}if(I.timeUnixNano!=null){if(H1.Long)(Y.timeUnixNano=H1.Long.fromValue(I.timeUnixNano)).unsigned=!1;else if(typeof I.timeUnixNano==="string")Y.timeUnixNano=parseInt(I.timeUnixNano,10);else if(typeof I.timeUnixNano==="number")Y.timeUnixNano=I.timeUnixNano;else if(typeof I.timeUnixNano==="object")Y.timeUnixNano=new H1.LongBits(I.timeUnixNano.low>>>0,I.timeUnixNano.high>>>0).toNumber()}if(I.count!=null){if(H1.Long)(Y.count=H1.Long.fromValue(I.count)).unsigned=!1;else if(typeof I.count==="string")Y.count=parseInt(I.count,10);else if(typeof I.count==="number")Y.count=I.count;else if(typeof I.count==="object")Y.count=new H1.LongBits(I.count.low>>>0,I.count.high>>>0).toNumber()}if(I.sum!=null)Y.sum=Number(I.sum);if(I.scale!=null)Y.scale=I.scale|0;if(I.zeroCount!=null){if(H1.Long)(Y.zeroCount=H1.Long.fromValue(I.zeroCount)).unsigned=!1;else if(typeof I.zeroCount==="string")Y.zeroCount=parseInt(I.zeroCount,10);else if(typeof I.zeroCount==="number")Y.zeroCount=I.zeroCount;else if(typeof I.zeroCount==="object")Y.zeroCount=new H1.LongBits(I.zeroCount.low>>>0,I.zeroCount.high>>>0).toNumber()}if(I.positive!=null){if(typeof I.positive!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.positive: object expected");Y.positive=X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.fromObject(I.positive)}if(I.negative!=null){if(typeof I.negative!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.negative: object expected");Y.negative=X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.fromObject(I.negative)}if(I.flags!=null)Y.flags=I.flags>>>0;if(I.exemplars){if(!Array.isArray(I.exemplars))throw TypeError(".opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.exemplars: array expected");Y.exemplars=[];for(var W=0;W<I.exemplars.length;++W){if(typeof I.exemplars[W]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.exemplars: object expected");Y.exemplars[W]=X1.opentelemetry.proto.metrics.v1.Exemplar.fromObject(I.exemplars[W])}}if(I.min!=null)Y.min=Number(I.min);if(I.max!=null)Y.max=Number(I.max);if(I.zeroThreshold!=null)Y.zeroThreshold=Number(I.zeroThreshold);return Y},Z.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.arrays||Y.defaults)W.attributes=[],W.exemplars=[];if(Y.defaults){if(H1.Long){var J=new H1.Long(0,0,!1);W.startTimeUnixNano=Y.longs===String?J.toString():Y.longs===Number?J.toNumber():J}else W.startTimeUnixNano=Y.longs===String?"0":0;if(H1.Long){var J=new H1.Long(0,0,!1);W.timeUnixNano=Y.longs===String?J.toString():Y.longs===Number?J.toNumber():J}else W.timeUnixNano=Y.longs===String?"0":0;if(H1.Long){var J=new H1.Long(0,0,!1);W.count=Y.longs===String?J.toString():Y.longs===Number?J.toNumber():J}else W.count=Y.longs===String?"0":0;if(W.scale=0,H1.Long){var J=new H1.Long(0,0,!1);W.zeroCount=Y.longs===String?J.toString():Y.longs===Number?J.toNumber():J}else W.zeroCount=Y.longs===String?"0":0;W.positive=null,W.negative=null,W.flags=0,W.zeroThreshold=0}if(I.attributes&&I.attributes.length){W.attributes=[];for(var X=0;X<I.attributes.length;++X)W.attributes[X]=X1.opentelemetry.proto.common.v1.KeyValue.toObject(I.attributes[X],Y)}if(I.startTimeUnixNano!=null&&I.hasOwnProperty("startTimeUnixNano"))if(typeof I.startTimeUnixNano==="number")W.startTimeUnixNano=Y.longs===String?String(I.startTimeUnixNano):I.startTimeUnixNano;else W.startTimeUnixNano=Y.longs===String?H1.Long.prototype.toString.call(I.startTimeUnixNano):Y.longs===Number?new H1.LongBits(I.startTimeUnixNano.low>>>0,I.startTimeUnixNano.high>>>0).toNumber():I.startTimeUnixNano;if(I.timeUnixNano!=null&&I.hasOwnProperty("timeUnixNano"))if(typeof I.timeUnixNano==="number")W.timeUnixNano=Y.longs===String?String(I.timeUnixNano):I.timeUnixNano;else W.timeUnixNano=Y.longs===String?H1.Long.prototype.toString.call(I.timeUnixNano):Y.longs===Number?new H1.LongBits(I.timeUnixNano.low>>>0,I.timeUnixNano.high>>>0).toNumber():I.timeUnixNano;if(I.count!=null&&I.hasOwnProperty("count"))if(typeof I.count==="number")W.count=Y.longs===String?String(I.count):I.count;else W.count=Y.longs===String?H1.Long.prototype.toString.call(I.count):Y.longs===Number?new H1.LongBits(I.count.low>>>0,I.count.high>>>0).toNumber():I.count;if(I.sum!=null&&I.hasOwnProperty("sum")){if(W.sum=Y.json&&!isFinite(I.sum)?String(I.sum):I.sum,Y.oneofs)W._sum="sum"}if(I.scale!=null&&I.hasOwnProperty("scale"))W.scale=I.scale;if(I.zeroCount!=null&&I.hasOwnProperty("zeroCount"))if(typeof I.zeroCount==="number")W.zeroCount=Y.longs===String?String(I.zeroCount):I.zeroCount;else W.zeroCount=Y.longs===String?H1.Long.prototype.toString.call(I.zeroCount):Y.longs===Number?new H1.LongBits(I.zeroCount.low>>>0,I.zeroCount.high>>>0).toNumber():I.zeroCount;if(I.positive!=null&&I.hasOwnProperty("positive"))W.positive=X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.toObject(I.positive,Y);if(I.negative!=null&&I.hasOwnProperty("negative"))W.negative=X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.toObject(I.negative,Y);if(I.flags!=null&&I.hasOwnProperty("flags"))W.flags=I.flags;if(I.exemplars&&I.exemplars.length){W.exemplars=[];for(var X=0;X<I.exemplars.length;++X)W.exemplars[X]=X1.opentelemetry.proto.metrics.v1.Exemplar.toObject(I.exemplars[X],Y)}if(I.min!=null&&I.hasOwnProperty("min")){if(W.min=Y.json&&!isFinite(I.min)?String(I.min):I.min,Y.oneofs)W._min="min"}if(I.max!=null&&I.hasOwnProperty("max")){if(W.max=Y.json&&!isFinite(I.max)?String(I.max):I.max,Y.oneofs)W._max="max"}if(I.zeroThreshold!=null&&I.hasOwnProperty("zeroThreshold"))W.zeroThreshold=Y.json&&!isFinite(I.zeroThreshold)?String(I.zeroThreshold):I.zeroThreshold;return W},Z.prototype.toJSON=function F(){return this.constructor.toObject(this,n9.util.toJSONOptions)},Z.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint"},Z.Buckets=function(){function F(I){if(this.bucketCounts=[],I){for(var Y=Object.keys(I),W=0;W<Y.length;++W)if(I[Y[W]]!=null)this[Y[W]]=I[Y[W]]}}return F.prototype.offset=null,F.prototype.bucketCounts=H1.emptyArray,F.create=function I(Y){return new F(Y)},F.encode=function I(Y,W){if(!W)W=W4.create();if(Y.offset!=null&&Object.hasOwnProperty.call(Y,"offset"))W.uint32(8).sint32(Y.offset);if(Y.bucketCounts!=null&&Y.bucketCounts.length){W.uint32(18).fork();for(var J=0;J<Y.bucketCounts.length;++J)W.uint64(Y.bucketCounts[J]);W.ldelim()}return W},F.encodeDelimited=function I(Y,W){return this.encode(Y,W).ldelim()},F.decode=function I(Y,W){if(!(Y instanceof u0))Y=u0.create(Y);var J=W===void 0?Y.len:Y.pos+W,X=new X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets;while(Y.pos<J){var V=Y.uint32();switch(V>>>3){case 1:{X.offset=Y.sint32();break}case 2:{if(!(X.bucketCounts&&X.bucketCounts.length))X.bucketCounts=[];if((V&7)===2){var C=Y.uint32()+Y.pos;while(Y.pos<C)X.bucketCounts.push(Y.uint64())}else X.bucketCounts.push(Y.uint64());break}default:Y.skipType(V&7);break}}return X},F.decodeDelimited=function I(Y){if(!(Y instanceof u0))Y=new u0(Y);return this.decode(Y,Y.uint32())},F.verify=function I(Y){if(typeof Y!=="object"||Y===null)return"object expected";if(Y.offset!=null&&Y.hasOwnProperty("offset")){if(!H1.isInteger(Y.offset))return"offset: integer expected"}if(Y.bucketCounts!=null&&Y.hasOwnProperty("bucketCounts")){if(!Array.isArray(Y.bucketCounts))return"bucketCounts: array expected";for(var W=0;W<Y.bucketCounts.length;++W)if(!H1.isInteger(Y.bucketCounts[W])&&!(Y.bucketCounts[W]&&H1.isInteger(Y.bucketCounts[W].low)&&H1.isInteger(Y.bucketCounts[W].high)))return"bucketCounts: integer|Long[] expected"}return null},F.fromObject=function I(Y){if(Y instanceof X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets)return Y;var W=new X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets;if(Y.offset!=null)W.offset=Y.offset|0;if(Y.bucketCounts){if(!Array.isArray(Y.bucketCounts))throw TypeError(".opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.bucketCounts: array expected");W.bucketCounts=[];for(var J=0;J<Y.bucketCounts.length;++J)if(H1.Long)(W.bucketCounts[J]=H1.Long.fromValue(Y.bucketCounts[J])).unsigned=!0;else if(typeof Y.bucketCounts[J]==="string")W.bucketCounts[J]=parseInt(Y.bucketCounts[J],10);else if(typeof Y.bucketCounts[J]==="number")W.bucketCounts[J]=Y.bucketCounts[J];else if(typeof Y.bucketCounts[J]==="object")W.bucketCounts[J]=new H1.LongBits(Y.bucketCounts[J].low>>>0,Y.bucketCounts[J].high>>>0).toNumber(!0)}return W},F.toObject=function I(Y,W){if(!W)W={};var J={};if(W.arrays||W.defaults)J.bucketCounts=[];if(W.defaults)J.offset=0;if(Y.offset!=null&&Y.hasOwnProperty("offset"))J.offset=Y.offset;if(Y.bucketCounts&&Y.bucketCounts.length){J.bucketCounts=[];for(var X=0;X<Y.bucketCounts.length;++X)if(typeof Y.bucketCounts[X]==="number")J.bucketCounts[X]=W.longs===String?String(Y.bucketCounts[X]):Y.bucketCounts[X];else J.bucketCounts[X]=W.longs===String?H1.Long.prototype.toString.call(Y.bucketCounts[X]):W.longs===Number?new H1.LongBits(Y.bucketCounts[X].low>>>0,Y.bucketCounts[X].high>>>0).toNumber(!0):Y.bucketCounts[X]}return J},F.prototype.toJSON=function I(){return this.constructor.toObject(this,n9.util.toJSONOptions)},F.getTypeUrl=function I(Y){if(Y===void 0)Y="type.googleapis.com";return Y+"/opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets"},F}(),Z}(),D.SummaryDataPoint=function(){function Z(G){if(this.attributes=[],this.quantileValues=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.attributes=H1.emptyArray,Z.prototype.startTimeUnixNano=null,Z.prototype.timeUnixNano=null,Z.prototype.count=null,Z.prototype.sum=null,Z.prototype.quantileValues=H1.emptyArray,Z.prototype.flags=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=W4.create();if(F.startTimeUnixNano!=null&&Object.hasOwnProperty.call(F,"startTimeUnixNano"))I.uint32(17).fixed64(F.startTimeUnixNano);if(F.timeUnixNano!=null&&Object.hasOwnProperty.call(F,"timeUnixNano"))I.uint32(25).fixed64(F.timeUnixNano);if(F.count!=null&&Object.hasOwnProperty.call(F,"count"))I.uint32(33).fixed64(F.count);if(F.sum!=null&&Object.hasOwnProperty.call(F,"sum"))I.uint32(41).double(F.sum);if(F.quantileValues!=null&&F.quantileValues.length)for(var Y=0;Y<F.quantileValues.length;++Y)X1.opentelemetry.proto.metrics.v1.SummaryDataPoint.ValueAtQuantile.encode(F.quantileValues[Y],I.uint32(50).fork()).ldelim();if(F.attributes!=null&&F.attributes.length)for(var Y=0;Y<F.attributes.length;++Y)X1.opentelemetry.proto.common.v1.KeyValue.encode(F.attributes[Y],I.uint32(58).fork()).ldelim();if(F.flags!=null&&Object.hasOwnProperty.call(F,"flags"))I.uint32(64).uint32(F.flags);return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof u0))F=u0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.metrics.v1.SummaryDataPoint;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 7:{if(!(W.attributes&&W.attributes.length))W.attributes=[];W.attributes.push(X1.opentelemetry.proto.common.v1.KeyValue.decode(F,F.uint32()));break}case 2:{W.startTimeUnixNano=F.fixed64();break}case 3:{W.timeUnixNano=F.fixed64();break}case 4:{W.count=F.fixed64();break}case 5:{W.sum=F.double();break}case 6:{if(!(W.quantileValues&&W.quantileValues.length))W.quantileValues=[];W.quantileValues.push(X1.opentelemetry.proto.metrics.v1.SummaryDataPoint.ValueAtQuantile.decode(F,F.uint32()));break}case 8:{W.flags=F.uint32();break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof u0))F=new u0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.attributes!=null&&F.hasOwnProperty("attributes")){if(!Array.isArray(F.attributes))return"attributes: array expected";for(var I=0;I<F.attributes.length;++I){var Y=X1.opentelemetry.proto.common.v1.KeyValue.verify(F.attributes[I]);if(Y)return"attributes."+Y}}if(F.startTimeUnixNano!=null&&F.hasOwnProperty("startTimeUnixNano")){if(!H1.isInteger(F.startTimeUnixNano)&&!(F.startTimeUnixNano&&H1.isInteger(F.startTimeUnixNano.low)&&H1.isInteger(F.startTimeUnixNano.high)))return"startTimeUnixNano: integer|Long expected"}if(F.timeUnixNano!=null&&F.hasOwnProperty("timeUnixNano")){if(!H1.isInteger(F.timeUnixNano)&&!(F.timeUnixNano&&H1.isInteger(F.timeUnixNano.low)&&H1.isInteger(F.timeUnixNano.high)))return"timeUnixNano: integer|Long expected"}if(F.count!=null&&F.hasOwnProperty("count")){if(!H1.isInteger(F.count)&&!(F.count&&H1.isInteger(F.count.low)&&H1.isInteger(F.count.high)))return"count: integer|Long expected"}if(F.sum!=null&&F.hasOwnProperty("sum")){if(typeof F.sum!=="number")return"sum: number expected"}if(F.quantileValues!=null&&F.hasOwnProperty("quantileValues")){if(!Array.isArray(F.quantileValues))return"quantileValues: array expected";for(var I=0;I<F.quantileValues.length;++I){var Y=X1.opentelemetry.proto.metrics.v1.SummaryDataPoint.ValueAtQuantile.verify(F.quantileValues[I]);if(Y)return"quantileValues."+Y}}if(F.flags!=null&&F.hasOwnProperty("flags")){if(!H1.isInteger(F.flags))return"flags: integer expected"}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.metrics.v1.SummaryDataPoint)return F;var I=new X1.opentelemetry.proto.metrics.v1.SummaryDataPoint;if(F.attributes){if(!Array.isArray(F.attributes))throw TypeError(".opentelemetry.proto.metrics.v1.SummaryDataPoint.attributes: array expected");I.attributes=[];for(var Y=0;Y<F.attributes.length;++Y){if(typeof F.attributes[Y]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.SummaryDataPoint.attributes: object expected");I.attributes[Y]=X1.opentelemetry.proto.common.v1.KeyValue.fromObject(F.attributes[Y])}}if(F.startTimeUnixNano!=null){if(H1.Long)(I.startTimeUnixNano=H1.Long.fromValue(F.startTimeUnixNano)).unsigned=!1;else if(typeof F.startTimeUnixNano==="string")I.startTimeUnixNano=parseInt(F.startTimeUnixNano,10);else if(typeof F.startTimeUnixNano==="number")I.startTimeUnixNano=F.startTimeUnixNano;else if(typeof F.startTimeUnixNano==="object")I.startTimeUnixNano=new H1.LongBits(F.startTimeUnixNano.low>>>0,F.startTimeUnixNano.high>>>0).toNumber()}if(F.timeUnixNano!=null){if(H1.Long)(I.timeUnixNano=H1.Long.fromValue(F.timeUnixNano)).unsigned=!1;else if(typeof F.timeUnixNano==="string")I.timeUnixNano=parseInt(F.timeUnixNano,10);else if(typeof F.timeUnixNano==="number")I.timeUnixNano=F.timeUnixNano;else if(typeof F.timeUnixNano==="object")I.timeUnixNano=new H1.LongBits(F.timeUnixNano.low>>>0,F.timeUnixNano.high>>>0).toNumber()}if(F.count!=null){if(H1.Long)(I.count=H1.Long.fromValue(F.count)).unsigned=!1;else if(typeof F.count==="string")I.count=parseInt(F.count,10);else if(typeof F.count==="number")I.count=F.count;else if(typeof F.count==="object")I.count=new H1.LongBits(F.count.low>>>0,F.count.high>>>0).toNumber()}if(F.sum!=null)I.sum=Number(F.sum);if(F.quantileValues){if(!Array.isArray(F.quantileValues))throw TypeError(".opentelemetry.proto.metrics.v1.SummaryDataPoint.quantileValues: array expected");I.quantileValues=[];for(var Y=0;Y<F.quantileValues.length;++Y){if(typeof F.quantileValues[Y]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.SummaryDataPoint.quantileValues: object expected");I.quantileValues[Y]=X1.opentelemetry.proto.metrics.v1.SummaryDataPoint.ValueAtQuantile.fromObject(F.quantileValues[Y])}}if(F.flags!=null)I.flags=F.flags>>>0;return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.quantileValues=[],Y.attributes=[];if(I.defaults){if(H1.Long){var W=new H1.Long(0,0,!1);Y.startTimeUnixNano=I.longs===String?W.toString():I.longs===Number?W.toNumber():W}else Y.startTimeUnixNano=I.longs===String?"0":0;if(H1.Long){var W=new H1.Long(0,0,!1);Y.timeUnixNano=I.longs===String?W.toString():I.longs===Number?W.toNumber():W}else Y.timeUnixNano=I.longs===String?"0":0;if(H1.Long){var W=new H1.Long(0,0,!1);Y.count=I.longs===String?W.toString():I.longs===Number?W.toNumber():W}else Y.count=I.longs===String?"0":0;Y.sum=0,Y.flags=0}if(F.startTimeUnixNano!=null&&F.hasOwnProperty("startTimeUnixNano"))if(typeof F.startTimeUnixNano==="number")Y.startTimeUnixNano=I.longs===String?String(F.startTimeUnixNano):F.startTimeUnixNano;else Y.startTimeUnixNano=I.longs===String?H1.Long.prototype.toString.call(F.startTimeUnixNano):I.longs===Number?new H1.LongBits(F.startTimeUnixNano.low>>>0,F.startTimeUnixNano.high>>>0).toNumber():F.startTimeUnixNano;if(F.timeUnixNano!=null&&F.hasOwnProperty("timeUnixNano"))if(typeof F.timeUnixNano==="number")Y.timeUnixNano=I.longs===String?String(F.timeUnixNano):F.timeUnixNano;else Y.timeUnixNano=I.longs===String?H1.Long.prototype.toString.call(F.timeUnixNano):I.longs===Number?new H1.LongBits(F.timeUnixNano.low>>>0,F.timeUnixNano.high>>>0).toNumber():F.timeUnixNano;if(F.count!=null&&F.hasOwnProperty("count"))if(typeof F.count==="number")Y.count=I.longs===String?String(F.count):F.count;else Y.count=I.longs===String?H1.Long.prototype.toString.call(F.count):I.longs===Number?new H1.LongBits(F.count.low>>>0,F.count.high>>>0).toNumber():F.count;if(F.sum!=null&&F.hasOwnProperty("sum"))Y.sum=I.json&&!isFinite(F.sum)?String(F.sum):F.sum;if(F.quantileValues&&F.quantileValues.length){Y.quantileValues=[];for(var J=0;J<F.quantileValues.length;++J)Y.quantileValues[J]=X1.opentelemetry.proto.metrics.v1.SummaryDataPoint.ValueAtQuantile.toObject(F.quantileValues[J],I)}if(F.attributes&&F.attributes.length){Y.attributes=[];for(var J=0;J<F.attributes.length;++J)Y.attributes[J]=X1.opentelemetry.proto.common.v1.KeyValue.toObject(F.attributes[J],I)}if(F.flags!=null&&F.hasOwnProperty("flags"))Y.flags=F.flags;return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,n9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.metrics.v1.SummaryDataPoint"},Z.ValueAtQuantile=function(){function G(F){if(F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}return G.prototype.quantile=null,G.prototype.value=null,G.create=function F(I){return new G(I)},G.encode=function F(I,Y){if(!Y)Y=W4.create();if(I.quantile!=null&&Object.hasOwnProperty.call(I,"quantile"))Y.uint32(9).double(I.quantile);if(I.value!=null&&Object.hasOwnProperty.call(I,"value"))Y.uint32(17).double(I.value);return Y},G.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},G.decode=function F(I,Y){if(!(I instanceof u0))I=u0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.metrics.v1.SummaryDataPoint.ValueAtQuantile;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 1:{J.quantile=I.double();break}case 2:{J.value=I.double();break}default:I.skipType(X&7);break}}return J},G.decodeDelimited=function F(I){if(!(I instanceof u0))I=new u0(I);return this.decode(I,I.uint32())},G.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";if(I.quantile!=null&&I.hasOwnProperty("quantile")){if(typeof I.quantile!=="number")return"quantile: number expected"}if(I.value!=null&&I.hasOwnProperty("value")){if(typeof I.value!=="number")return"value: number expected"}return null},G.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.metrics.v1.SummaryDataPoint.ValueAtQuantile)return I;var Y=new X1.opentelemetry.proto.metrics.v1.SummaryDataPoint.ValueAtQuantile;if(I.quantile!=null)Y.quantile=Number(I.quantile);if(I.value!=null)Y.value=Number(I.value);return Y},G.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.defaults)W.quantile=0,W.value=0;if(I.quantile!=null&&I.hasOwnProperty("quantile"))W.quantile=Y.json&&!isFinite(I.quantile)?String(I.quantile):I.quantile;if(I.value!=null&&I.hasOwnProperty("value"))W.value=Y.json&&!isFinite(I.value)?String(I.value):I.value;return W},G.prototype.toJSON=function F(){return this.constructor.toObject(this,n9.util.toJSONOptions)},G.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.metrics.v1.SummaryDataPoint.ValueAtQuantile"},G}(),Z}(),D.Exemplar=function(){function Z(F){if(this.filteredAttributes=[],F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}Z.prototype.filteredAttributes=H1.emptyArray,Z.prototype.timeUnixNano=null,Z.prototype.asDouble=null,Z.prototype.asInt=null,Z.prototype.spanId=null,Z.prototype.traceId=null;var G;return Object.defineProperty(Z.prototype,"value",{get:H1.oneOfGetter(G=["asDouble","asInt"]),set:H1.oneOfSetter(G)}),Z.create=function F(I){return new Z(I)},Z.encode=function F(I,Y){if(!Y)Y=W4.create();if(I.timeUnixNano!=null&&Object.hasOwnProperty.call(I,"timeUnixNano"))Y.uint32(17).fixed64(I.timeUnixNano);if(I.asDouble!=null&&Object.hasOwnProperty.call(I,"asDouble"))Y.uint32(25).double(I.asDouble);if(I.spanId!=null&&Object.hasOwnProperty.call(I,"spanId"))Y.uint32(34).bytes(I.spanId);if(I.traceId!=null&&Object.hasOwnProperty.call(I,"traceId"))Y.uint32(42).bytes(I.traceId);if(I.asInt!=null&&Object.hasOwnProperty.call(I,"asInt"))Y.uint32(49).sfixed64(I.asInt);if(I.filteredAttributes!=null&&I.filteredAttributes.length)for(var W=0;W<I.filteredAttributes.length;++W)X1.opentelemetry.proto.common.v1.KeyValue.encode(I.filteredAttributes[W],Y.uint32(58).fork()).ldelim();return Y},Z.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},Z.decode=function F(I,Y){if(!(I instanceof u0))I=u0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.metrics.v1.Exemplar;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 7:{if(!(J.filteredAttributes&&J.filteredAttributes.length))J.filteredAttributes=[];J.filteredAttributes.push(X1.opentelemetry.proto.common.v1.KeyValue.decode(I,I.uint32()));break}case 2:{J.timeUnixNano=I.fixed64();break}case 3:{J.asDouble=I.double();break}case 6:{J.asInt=I.sfixed64();break}case 4:{J.spanId=I.bytes();break}case 5:{J.traceId=I.bytes();break}default:I.skipType(X&7);break}}return J},Z.decodeDelimited=function F(I){if(!(I instanceof u0))I=new u0(I);return this.decode(I,I.uint32())},Z.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";var Y={};if(I.filteredAttributes!=null&&I.hasOwnProperty("filteredAttributes")){if(!Array.isArray(I.filteredAttributes))return"filteredAttributes: array expected";for(var W=0;W<I.filteredAttributes.length;++W){var J=X1.opentelemetry.proto.common.v1.KeyValue.verify(I.filteredAttributes[W]);if(J)return"filteredAttributes."+J}}if(I.timeUnixNano!=null&&I.hasOwnProperty("timeUnixNano")){if(!H1.isInteger(I.timeUnixNano)&&!(I.timeUnixNano&&H1.isInteger(I.timeUnixNano.low)&&H1.isInteger(I.timeUnixNano.high)))return"timeUnixNano: integer|Long expected"}if(I.asDouble!=null&&I.hasOwnProperty("asDouble")){if(Y.value=1,typeof I.asDouble!=="number")return"asDouble: number expected"}if(I.asInt!=null&&I.hasOwnProperty("asInt")){if(Y.value===1)return"value: multiple values";if(Y.value=1,!H1.isInteger(I.asInt)&&!(I.asInt&&H1.isInteger(I.asInt.low)&&H1.isInteger(I.asInt.high)))return"asInt: integer|Long expected"}if(I.spanId!=null&&I.hasOwnProperty("spanId")){if(!(I.spanId&&typeof I.spanId.length==="number"||H1.isString(I.spanId)))return"spanId: buffer expected"}if(I.traceId!=null&&I.hasOwnProperty("traceId")){if(!(I.traceId&&typeof I.traceId.length==="number"||H1.isString(I.traceId)))return"traceId: buffer expected"}return null},Z.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.metrics.v1.Exemplar)return I;var Y=new X1.opentelemetry.proto.metrics.v1.Exemplar;if(I.filteredAttributes){if(!Array.isArray(I.filteredAttributes))throw TypeError(".opentelemetry.proto.metrics.v1.Exemplar.filteredAttributes: array expected");Y.filteredAttributes=[];for(var W=0;W<I.filteredAttributes.length;++W){if(typeof I.filteredAttributes[W]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.Exemplar.filteredAttributes: object expected");Y.filteredAttributes[W]=X1.opentelemetry.proto.common.v1.KeyValue.fromObject(I.filteredAttributes[W])}}if(I.timeUnixNano!=null){if(H1.Long)(Y.timeUnixNano=H1.Long.fromValue(I.timeUnixNano)).unsigned=!1;else if(typeof I.timeUnixNano==="string")Y.timeUnixNano=parseInt(I.timeUnixNano,10);else if(typeof I.timeUnixNano==="number")Y.timeUnixNano=I.timeUnixNano;else if(typeof I.timeUnixNano==="object")Y.timeUnixNano=new H1.LongBits(I.timeUnixNano.low>>>0,I.timeUnixNano.high>>>0).toNumber()}if(I.asDouble!=null)Y.asDouble=Number(I.asDouble);if(I.asInt!=null){if(H1.Long)(Y.asInt=H1.Long.fromValue(I.asInt)).unsigned=!1;else if(typeof I.asInt==="string")Y.asInt=parseInt(I.asInt,10);else if(typeof I.asInt==="number")Y.asInt=I.asInt;else if(typeof I.asInt==="object")Y.asInt=new H1.LongBits(I.asInt.low>>>0,I.asInt.high>>>0).toNumber()}if(I.spanId!=null){if(typeof I.spanId==="string")H1.base64.decode(I.spanId,Y.spanId=H1.newBuffer(H1.base64.length(I.spanId)),0);else if(I.spanId.length>=0)Y.spanId=I.spanId}if(I.traceId!=null){if(typeof I.traceId==="string")H1.base64.decode(I.traceId,Y.traceId=H1.newBuffer(H1.base64.length(I.traceId)),0);else if(I.traceId.length>=0)Y.traceId=I.traceId}return Y},Z.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.arrays||Y.defaults)W.filteredAttributes=[];if(Y.defaults){if(H1.Long){var J=new H1.Long(0,0,!1);W.timeUnixNano=Y.longs===String?J.toString():Y.longs===Number?J.toNumber():J}else W.timeUnixNano=Y.longs===String?"0":0;if(Y.bytes===String)W.spanId="";else if(W.spanId=[],Y.bytes!==Array)W.spanId=H1.newBuffer(W.spanId);if(Y.bytes===String)W.traceId="";else if(W.traceId=[],Y.bytes!==Array)W.traceId=H1.newBuffer(W.traceId)}if(I.timeUnixNano!=null&&I.hasOwnProperty("timeUnixNano"))if(typeof I.timeUnixNano==="number")W.timeUnixNano=Y.longs===String?String(I.timeUnixNano):I.timeUnixNano;else W.timeUnixNano=Y.longs===String?H1.Long.prototype.toString.call(I.timeUnixNano):Y.longs===Number?new H1.LongBits(I.timeUnixNano.low>>>0,I.timeUnixNano.high>>>0).toNumber():I.timeUnixNano;if(I.asDouble!=null&&I.hasOwnProperty("asDouble")){if(W.asDouble=Y.json&&!isFinite(I.asDouble)?String(I.asDouble):I.asDouble,Y.oneofs)W.value="asDouble"}if(I.spanId!=null&&I.hasOwnProperty("spanId"))W.spanId=Y.bytes===String?H1.base64.encode(I.spanId,0,I.spanId.length):Y.bytes===Array?Array.prototype.slice.call(I.spanId):I.spanId;if(I.traceId!=null&&I.hasOwnProperty("traceId"))W.traceId=Y.bytes===String?H1.base64.encode(I.traceId,0,I.traceId.length):Y.bytes===Array?Array.prototype.slice.call(I.traceId):I.traceId;if(I.asInt!=null&&I.hasOwnProperty("asInt")){if(typeof I.asInt==="number")W.asInt=Y.longs===String?String(I.asInt):I.asInt;else W.asInt=Y.longs===String?H1.Long.prototype.toString.call(I.asInt):Y.longs===Number?new H1.LongBits(I.asInt.low>>>0,I.asInt.high>>>0).toNumber():I.asInt;if(Y.oneofs)W.value="asInt"}if(I.filteredAttributes&&I.filteredAttributes.length){W.filteredAttributes=[];for(var X=0;X<I.filteredAttributes.length;++X)W.filteredAttributes[X]=X1.opentelemetry.proto.common.v1.KeyValue.toObject(I.filteredAttributes[X],Y)}return W},Z.prototype.toJSON=function F(){return this.constructor.toObject(this,n9.util.toJSONOptions)},Z.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.metrics.v1.Exemplar"},Z}(),D}(),Q}(),B.logs=function(){var Q={};return Q.v1=function(){var D={};return D.LogsData=function(){function Z(G){if(this.resourceLogs=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.resourceLogs=H1.emptyArray,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=W4.create();if(F.resourceLogs!=null&&F.resourceLogs.length)for(var Y=0;Y<F.resourceLogs.length;++Y)X1.opentelemetry.proto.logs.v1.ResourceLogs.encode(F.resourceLogs[Y],I.uint32(10).fork()).ldelim();return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof u0))F=u0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.logs.v1.LogsData;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{if(!(W.resourceLogs&&W.resourceLogs.length))W.resourceLogs=[];W.resourceLogs.push(X1.opentelemetry.proto.logs.v1.ResourceLogs.decode(F,F.uint32()));break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof u0))F=new u0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.resourceLogs!=null&&F.hasOwnProperty("resourceLogs")){if(!Array.isArray(F.resourceLogs))return"resourceLogs: array expected";for(var I=0;I<F.resourceLogs.length;++I){var Y=X1.opentelemetry.proto.logs.v1.ResourceLogs.verify(F.resourceLogs[I]);if(Y)return"resourceLogs."+Y}}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.logs.v1.LogsData)return F;var I=new X1.opentelemetry.proto.logs.v1.LogsData;if(F.resourceLogs){if(!Array.isArray(F.resourceLogs))throw TypeError(".opentelemetry.proto.logs.v1.LogsData.resourceLogs: array expected");I.resourceLogs=[];for(var Y=0;Y<F.resourceLogs.length;++Y){if(typeof F.resourceLogs[Y]!=="object")throw TypeError(".opentelemetry.proto.logs.v1.LogsData.resourceLogs: object expected");I.resourceLogs[Y]=X1.opentelemetry.proto.logs.v1.ResourceLogs.fromObject(F.resourceLogs[Y])}}return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.resourceLogs=[];if(F.resourceLogs&&F.resourceLogs.length){Y.resourceLogs=[];for(var W=0;W<F.resourceLogs.length;++W)Y.resourceLogs[W]=X1.opentelemetry.proto.logs.v1.ResourceLogs.toObject(F.resourceLogs[W],I)}return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,n9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.logs.v1.LogsData"},Z}(),D.ResourceLogs=function(){function Z(G){if(this.scopeLogs=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.resource=null,Z.prototype.scopeLogs=H1.emptyArray,Z.prototype.schemaUrl=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=W4.create();if(F.resource!=null&&Object.hasOwnProperty.call(F,"resource"))X1.opentelemetry.proto.resource.v1.Resource.encode(F.resource,I.uint32(10).fork()).ldelim();if(F.scopeLogs!=null&&F.scopeLogs.length)for(var Y=0;Y<F.scopeLogs.length;++Y)X1.opentelemetry.proto.logs.v1.ScopeLogs.encode(F.scopeLogs[Y],I.uint32(18).fork()).ldelim();if(F.schemaUrl!=null&&Object.hasOwnProperty.call(F,"schemaUrl"))I.uint32(26).string(F.schemaUrl);return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof u0))F=u0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.logs.v1.ResourceLogs;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{W.resource=X1.opentelemetry.proto.resource.v1.Resource.decode(F,F.uint32());break}case 2:{if(!(W.scopeLogs&&W.scopeLogs.length))W.scopeLogs=[];W.scopeLogs.push(X1.opentelemetry.proto.logs.v1.ScopeLogs.decode(F,F.uint32()));break}case 3:{W.schemaUrl=F.string();break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof u0))F=new u0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.resource!=null&&F.hasOwnProperty("resource")){var I=X1.opentelemetry.proto.resource.v1.Resource.verify(F.resource);if(I)return"resource."+I}if(F.scopeLogs!=null&&F.hasOwnProperty("scopeLogs")){if(!Array.isArray(F.scopeLogs))return"scopeLogs: array expected";for(var Y=0;Y<F.scopeLogs.length;++Y){var I=X1.opentelemetry.proto.logs.v1.ScopeLogs.verify(F.scopeLogs[Y]);if(I)return"scopeLogs."+I}}if(F.schemaUrl!=null&&F.hasOwnProperty("schemaUrl")){if(!H1.isString(F.schemaUrl))return"schemaUrl: string expected"}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.logs.v1.ResourceLogs)return F;var I=new X1.opentelemetry.proto.logs.v1.ResourceLogs;if(F.resource!=null){if(typeof F.resource!=="object")throw TypeError(".opentelemetry.proto.logs.v1.ResourceLogs.resource: object expected");I.resource=X1.opentelemetry.proto.resource.v1.Resource.fromObject(F.resource)}if(F.scopeLogs){if(!Array.isArray(F.scopeLogs))throw TypeError(".opentelemetry.proto.logs.v1.ResourceLogs.scopeLogs: array expected");I.scopeLogs=[];for(var Y=0;Y<F.scopeLogs.length;++Y){if(typeof F.scopeLogs[Y]!=="object")throw TypeError(".opentelemetry.proto.logs.v1.ResourceLogs.scopeLogs: object expected");I.scopeLogs[Y]=X1.opentelemetry.proto.logs.v1.ScopeLogs.fromObject(F.scopeLogs[Y])}}if(F.schemaUrl!=null)I.schemaUrl=String(F.schemaUrl);return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.scopeLogs=[];if(I.defaults)Y.resource=null,Y.schemaUrl="";if(F.resource!=null&&F.hasOwnProperty("resource"))Y.resource=X1.opentelemetry.proto.resource.v1.Resource.toObject(F.resource,I);if(F.scopeLogs&&F.scopeLogs.length){Y.scopeLogs=[];for(var W=0;W<F.scopeLogs.length;++W)Y.scopeLogs[W]=X1.opentelemetry.proto.logs.v1.ScopeLogs.toObject(F.scopeLogs[W],I)}if(F.schemaUrl!=null&&F.hasOwnProperty("schemaUrl"))Y.schemaUrl=F.schemaUrl;return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,n9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.logs.v1.ResourceLogs"},Z}(),D.ScopeLogs=function(){function Z(G){if(this.logRecords=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.scope=null,Z.prototype.logRecords=H1.emptyArray,Z.prototype.schemaUrl=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=W4.create();if(F.scope!=null&&Object.hasOwnProperty.call(F,"scope"))X1.opentelemetry.proto.common.v1.InstrumentationScope.encode(F.scope,I.uint32(10).fork()).ldelim();if(F.logRecords!=null&&F.logRecords.length)for(var Y=0;Y<F.logRecords.length;++Y)X1.opentelemetry.proto.logs.v1.LogRecord.encode(F.logRecords[Y],I.uint32(18).fork()).ldelim();if(F.schemaUrl!=null&&Object.hasOwnProperty.call(F,"schemaUrl"))I.uint32(26).string(F.schemaUrl);return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof u0))F=u0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.logs.v1.ScopeLogs;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{W.scope=X1.opentelemetry.proto.common.v1.InstrumentationScope.decode(F,F.uint32());break}case 2:{if(!(W.logRecords&&W.logRecords.length))W.logRecords=[];W.logRecords.push(X1.opentelemetry.proto.logs.v1.LogRecord.decode(F,F.uint32()));break}case 3:{W.schemaUrl=F.string();break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof u0))F=new u0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.scope!=null&&F.hasOwnProperty("scope")){var I=X1.opentelemetry.proto.common.v1.InstrumentationScope.verify(F.scope);if(I)return"scope."+I}if(F.logRecords!=null&&F.hasOwnProperty("logRecords")){if(!Array.isArray(F.logRecords))return"logRecords: array expected";for(var Y=0;Y<F.logRecords.length;++Y){var I=X1.opentelemetry.proto.logs.v1.LogRecord.verify(F.logRecords[Y]);if(I)return"logRecords."+I}}if(F.schemaUrl!=null&&F.hasOwnProperty("schemaUrl")){if(!H1.isString(F.schemaUrl))return"schemaUrl: string expected"}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.logs.v1.ScopeLogs)return F;var I=new X1.opentelemetry.proto.logs.v1.ScopeLogs;if(F.scope!=null){if(typeof F.scope!=="object")throw TypeError(".opentelemetry.proto.logs.v1.ScopeLogs.scope: object expected");I.scope=X1.opentelemetry.proto.common.v1.InstrumentationScope.fromObject(F.scope)}if(F.logRecords){if(!Array.isArray(F.logRecords))throw TypeError(".opentelemetry.proto.logs.v1.ScopeLogs.logRecords: array expected");I.logRecords=[];for(var Y=0;Y<F.logRecords.length;++Y){if(typeof F.logRecords[Y]!=="object")throw TypeError(".opentelemetry.proto.logs.v1.ScopeLogs.logRecords: object expected");I.logRecords[Y]=X1.opentelemetry.proto.logs.v1.LogRecord.fromObject(F.logRecords[Y])}}if(F.schemaUrl!=null)I.schemaUrl=String(F.schemaUrl);return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.logRecords=[];if(I.defaults)Y.scope=null,Y.schemaUrl="";if(F.scope!=null&&F.hasOwnProperty("scope"))Y.scope=X1.opentelemetry.proto.common.v1.InstrumentationScope.toObject(F.scope,I);if(F.logRecords&&F.logRecords.length){Y.logRecords=[];for(var W=0;W<F.logRecords.length;++W)Y.logRecords[W]=X1.opentelemetry.proto.logs.v1.LogRecord.toObject(F.logRecords[W],I)}if(F.schemaUrl!=null&&F.hasOwnProperty("schemaUrl"))Y.schemaUrl=F.schemaUrl;return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,n9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.logs.v1.ScopeLogs"},Z}(),D.SeverityNumber=function(){var Z={},G=Object.create(Z);return G[Z[0]="SEVERITY_NUMBER_UNSPECIFIED"]=0,G[Z[1]="SEVERITY_NUMBER_TRACE"]=1,G[Z[2]="SEVERITY_NUMBER_TRACE2"]=2,G[Z[3]="SEVERITY_NUMBER_TRACE3"]=3,G[Z[4]="SEVERITY_NUMBER_TRACE4"]=4,G[Z[5]="SEVERITY_NUMBER_DEBUG"]=5,G[Z[6]="SEVERITY_NUMBER_DEBUG2"]=6,G[Z[7]="SEVERITY_NUMBER_DEBUG3"]=7,G[Z[8]="SEVERITY_NUMBER_DEBUG4"]=8,G[Z[9]="SEVERITY_NUMBER_INFO"]=9,G[Z[10]="SEVERITY_NUMBER_INFO2"]=10,G[Z[11]="SEVERITY_NUMBER_INFO3"]=11,G[Z[12]="SEVERITY_NUMBER_INFO4"]=12,G[Z[13]="SEVERITY_NUMBER_WARN"]=13,G[Z[14]="SEVERITY_NUMBER_WARN2"]=14,G[Z[15]="SEVERITY_NUMBER_WARN3"]=15,G[Z[16]="SEVERITY_NUMBER_WARN4"]=16,G[Z[17]="SEVERITY_NUMBER_ERROR"]=17,G[Z[18]="SEVERITY_NUMBER_ERROR2"]=18,G[Z[19]="SEVERITY_NUMBER_ERROR3"]=19,G[Z[20]="SEVERITY_NUMBER_ERROR4"]=20,G[Z[21]="SEVERITY_NUMBER_FATAL"]=21,G[Z[22]="SEVERITY_NUMBER_FATAL2"]=22,G[Z[23]="SEVERITY_NUMBER_FATAL3"]=23,G[Z[24]="SEVERITY_NUMBER_FATAL4"]=24,G}(),D.LogRecordFlags=function(){var Z={},G=Object.create(Z);return G[Z[0]="LOG_RECORD_FLAGS_DO_NOT_USE"]=0,G[Z[255]="LOG_RECORD_FLAGS_TRACE_FLAGS_MASK"]=255,G}(),D.LogRecord=function(){function Z(G){if(this.attributes=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.timeUnixNano=null,Z.prototype.observedTimeUnixNano=null,Z.prototype.severityNumber=null,Z.prototype.severityText=null,Z.prototype.body=null,Z.prototype.attributes=H1.emptyArray,Z.prototype.droppedAttributesCount=null,Z.prototype.flags=null,Z.prototype.traceId=null,Z.prototype.spanId=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=W4.create();if(F.timeUnixNano!=null&&Object.hasOwnProperty.call(F,"timeUnixNano"))I.uint32(9).fixed64(F.timeUnixNano);if(F.severityNumber!=null&&Object.hasOwnProperty.call(F,"severityNumber"))I.uint32(16).int32(F.severityNumber);if(F.severityText!=null&&Object.hasOwnProperty.call(F,"severityText"))I.uint32(26).string(F.severityText);if(F.body!=null&&Object.hasOwnProperty.call(F,"body"))X1.opentelemetry.proto.common.v1.AnyValue.encode(F.body,I.uint32(42).fork()).ldelim();if(F.attributes!=null&&F.attributes.length)for(var Y=0;Y<F.attributes.length;++Y)X1.opentelemetry.proto.common.v1.KeyValue.encode(F.attributes[Y],I.uint32(50).fork()).ldelim();if(F.droppedAttributesCount!=null&&Object.hasOwnProperty.call(F,"droppedAttributesCount"))I.uint32(56).uint32(F.droppedAttributesCount);if(F.flags!=null&&Object.hasOwnProperty.call(F,"flags"))I.uint32(69).fixed32(F.flags);if(F.traceId!=null&&Object.hasOwnProperty.call(F,"traceId"))I.uint32(74).bytes(F.traceId);if(F.spanId!=null&&Object.hasOwnProperty.call(F,"spanId"))I.uint32(82).bytes(F.spanId);if(F.observedTimeUnixNano!=null&&Object.hasOwnProperty.call(F,"observedTimeUnixNano"))I.uint32(89).fixed64(F.observedTimeUnixNano);return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof u0))F=u0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.logs.v1.LogRecord;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{W.timeUnixNano=F.fixed64();break}case 11:{W.observedTimeUnixNano=F.fixed64();break}case 2:{W.severityNumber=F.int32();break}case 3:{W.severityText=F.string();break}case 5:{W.body=X1.opentelemetry.proto.common.v1.AnyValue.decode(F,F.uint32());break}case 6:{if(!(W.attributes&&W.attributes.length))W.attributes=[];W.attributes.push(X1.opentelemetry.proto.common.v1.KeyValue.decode(F,F.uint32()));break}case 7:{W.droppedAttributesCount=F.uint32();break}case 8:{W.flags=F.fixed32();break}case 9:{W.traceId=F.bytes();break}case 10:{W.spanId=F.bytes();break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof u0))F=new u0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.timeUnixNano!=null&&F.hasOwnProperty("timeUnixNano")){if(!H1.isInteger(F.timeUnixNano)&&!(F.timeUnixNano&&H1.isInteger(F.timeUnixNano.low)&&H1.isInteger(F.timeUnixNano.high)))return"timeUnixNano: integer|Long expected"}if(F.observedTimeUnixNano!=null&&F.hasOwnProperty("observedTimeUnixNano")){if(!H1.isInteger(F.observedTimeUnixNano)&&!(F.observedTimeUnixNano&&H1.isInteger(F.observedTimeUnixNano.low)&&H1.isInteger(F.observedTimeUnixNano.high)))return"observedTimeUnixNano: integer|Long expected"}if(F.severityNumber!=null&&F.hasOwnProperty("severityNumber"))switch(F.severityNumber){default:return"severityNumber: enum value expected";case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 12:case 13:case 14:case 15:case 16:case 17:case 18:case 19:case 20:case 21:case 22:case 23:case 24:break}if(F.severityText!=null&&F.hasOwnProperty("severityText")){if(!H1.isString(F.severityText))return"severityText: string expected"}if(F.body!=null&&F.hasOwnProperty("body")){var I=X1.opentelemetry.proto.common.v1.AnyValue.verify(F.body);if(I)return"body."+I}if(F.attributes!=null&&F.hasOwnProperty("attributes")){if(!Array.isArray(F.attributes))return"attributes: array expected";for(var Y=0;Y<F.attributes.length;++Y){var I=X1.opentelemetry.proto.common.v1.KeyValue.verify(F.attributes[Y]);if(I)return"attributes."+I}}if(F.droppedAttributesCount!=null&&F.hasOwnProperty("droppedAttributesCount")){if(!H1.isInteger(F.droppedAttributesCount))return"droppedAttributesCount: integer expected"}if(F.flags!=null&&F.hasOwnProperty("flags")){if(!H1.isInteger(F.flags))return"flags: integer expected"}if(F.traceId!=null&&F.hasOwnProperty("traceId")){if(!(F.traceId&&typeof F.traceId.length==="number"||H1.isString(F.traceId)))return"traceId: buffer expected"}if(F.spanId!=null&&F.hasOwnProperty("spanId")){if(!(F.spanId&&typeof F.spanId.length==="number"||H1.isString(F.spanId)))return"spanId: buffer expected"}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.logs.v1.LogRecord)return F;var I=new X1.opentelemetry.proto.logs.v1.LogRecord;if(F.timeUnixNano!=null){if(H1.Long)(I.timeUnixNano=H1.Long.fromValue(F.timeUnixNano)).unsigned=!1;else if(typeof F.timeUnixNano==="string")I.timeUnixNano=parseInt(F.timeUnixNano,10);else if(typeof F.timeUnixNano==="number")I.timeUnixNano=F.timeUnixNano;else if(typeof F.timeUnixNano==="object")I.timeUnixNano=new H1.LongBits(F.timeUnixNano.low>>>0,F.timeUnixNano.high>>>0).toNumber()}if(F.observedTimeUnixNano!=null){if(H1.Long)(I.observedTimeUnixNano=H1.Long.fromValue(F.observedTimeUnixNano)).unsigned=!1;else if(typeof F.observedTimeUnixNano==="string")I.observedTimeUnixNano=parseInt(F.observedTimeUnixNano,10);else if(typeof F.observedTimeUnixNano==="number")I.observedTimeUnixNano=F.observedTimeUnixNano;else if(typeof F.observedTimeUnixNano==="object")I.observedTimeUnixNano=new H1.LongBits(F.observedTimeUnixNano.low>>>0,F.observedTimeUnixNano.high>>>0).toNumber()}switch(F.severityNumber){default:if(typeof F.severityNumber==="number"){I.severityNumber=F.severityNumber;break}break;case"SEVERITY_NUMBER_UNSPECIFIED":case 0:I.severityNumber=0;break;case"SEVERITY_NUMBER_TRACE":case 1:I.severityNumber=1;break;case"SEVERITY_NUMBER_TRACE2":case 2:I.severityNumber=2;break;case"SEVERITY_NUMBER_TRACE3":case 3:I.severityNumber=3;break;case"SEVERITY_NUMBER_TRACE4":case 4:I.severityNumber=4;break;case"SEVERITY_NUMBER_DEBUG":case 5:I.severityNumber=5;break;case"SEVERITY_NUMBER_DEBUG2":case 6:I.severityNumber=6;break;case"SEVERITY_NUMBER_DEBUG3":case 7:I.severityNumber=7;break;case"SEVERITY_NUMBER_DEBUG4":case 8:I.severityNumber=8;break;case"SEVERITY_NUMBER_INFO":case 9:I.severityNumber=9;break;case"SEVERITY_NUMBER_INFO2":case 10:I.severityNumber=10;break;case"SEVERITY_NUMBER_INFO3":case 11:I.severityNumber=11;break;case"SEVERITY_NUMBER_INFO4":case 12:I.severityNumber=12;break;case"SEVERITY_NUMBER_WARN":case 13:I.severityNumber=13;break;case"SEVERITY_NUMBER_WARN2":case 14:I.severityNumber=14;break;case"SEVERITY_NUMBER_WARN3":case 15:I.severityNumber=15;break;case"SEVERITY_NUMBER_WARN4":case 16:I.severityNumber=16;break;case"SEVERITY_NUMBER_ERROR":case 17:I.severityNumber=17;break;case"SEVERITY_NUMBER_ERROR2":case 18:I.severityNumber=18;break;case"SEVERITY_NUMBER_ERROR3":case 19:I.severityNumber=19;break;case"SEVERITY_NUMBER_ERROR4":case 20:I.severityNumber=20;break;case"SEVERITY_NUMBER_FATAL":case 21:I.severityNumber=21;break;case"SEVERITY_NUMBER_FATAL2":case 22:I.severityNumber=22;break;case"SEVERITY_NUMBER_FATAL3":case 23:I.severityNumber=23;break;case"SEVERITY_NUMBER_FATAL4":case 24:I.severityNumber=24;break}if(F.severityText!=null)I.severityText=String(F.severityText);if(F.body!=null){if(typeof F.body!=="object")throw TypeError(".opentelemetry.proto.logs.v1.LogRecord.body: object expected");I.body=X1.opentelemetry.proto.common.v1.AnyValue.fromObject(F.body)}if(F.attributes){if(!Array.isArray(F.attributes))throw TypeError(".opentelemetry.proto.logs.v1.LogRecord.attributes: array expected");I.attributes=[];for(var Y=0;Y<F.attributes.length;++Y){if(typeof F.attributes[Y]!=="object")throw TypeError(".opentelemetry.proto.logs.v1.LogRecord.attributes: object expected");I.attributes[Y]=X1.opentelemetry.proto.common.v1.KeyValue.fromObject(F.attributes[Y])}}if(F.droppedAttributesCount!=null)I.droppedAttributesCount=F.droppedAttributesCount>>>0;if(F.flags!=null)I.flags=F.flags>>>0;if(F.traceId!=null){if(typeof F.traceId==="string")H1.base64.decode(F.traceId,I.traceId=H1.newBuffer(H1.base64.length(F.traceId)),0);else if(F.traceId.length>=0)I.traceId=F.traceId}if(F.spanId!=null){if(typeof F.spanId==="string")H1.base64.decode(F.spanId,I.spanId=H1.newBuffer(H1.base64.length(F.spanId)),0);else if(F.spanId.length>=0)I.spanId=F.spanId}return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.attributes=[];if(I.defaults){if(H1.Long){var W=new H1.Long(0,0,!1);Y.timeUnixNano=I.longs===String?W.toString():I.longs===Number?W.toNumber():W}else Y.timeUnixNano=I.longs===String?"0":0;if(Y.severityNumber=I.enums===String?"SEVERITY_NUMBER_UNSPECIFIED":0,Y.severityText="",Y.body=null,Y.droppedAttributesCount=0,Y.flags=0,I.bytes===String)Y.traceId="";else if(Y.traceId=[],I.bytes!==Array)Y.traceId=H1.newBuffer(Y.traceId);if(I.bytes===String)Y.spanId="";else if(Y.spanId=[],I.bytes!==Array)Y.spanId=H1.newBuffer(Y.spanId);if(H1.Long){var W=new H1.Long(0,0,!1);Y.observedTimeUnixNano=I.longs===String?W.toString():I.longs===Number?W.toNumber():W}else Y.observedTimeUnixNano=I.longs===String?"0":0}if(F.timeUnixNano!=null&&F.hasOwnProperty("timeUnixNano"))if(typeof F.timeUnixNano==="number")Y.timeUnixNano=I.longs===String?String(F.timeUnixNano):F.timeUnixNano;else Y.timeUnixNano=I.longs===String?H1.Long.prototype.toString.call(F.timeUnixNano):I.longs===Number?new H1.LongBits(F.timeUnixNano.low>>>0,F.timeUnixNano.high>>>0).toNumber():F.timeUnixNano;if(F.severityNumber!=null&&F.hasOwnProperty("severityNumber"))Y.severityNumber=I.enums===String?X1.opentelemetry.proto.logs.v1.SeverityNumber[F.severityNumber]===void 0?F.severityNumber:X1.opentelemetry.proto.logs.v1.SeverityNumber[F.severityNumber]:F.severityNumber;if(F.severityText!=null&&F.hasOwnProperty("severityText"))Y.severityText=F.severityText;if(F.body!=null&&F.hasOwnProperty("body"))Y.body=X1.opentelemetry.proto.common.v1.AnyValue.toObject(F.body,I);if(F.attributes&&F.attributes.length){Y.attributes=[];for(var J=0;J<F.attributes.length;++J)Y.attributes[J]=X1.opentelemetry.proto.common.v1.KeyValue.toObject(F.attributes[J],I)}if(F.droppedAttributesCount!=null&&F.hasOwnProperty("droppedAttributesCount"))Y.droppedAttributesCount=F.droppedAttributesCount;if(F.flags!=null&&F.hasOwnProperty("flags"))Y.flags=F.flags;if(F.traceId!=null&&F.hasOwnProperty("traceId"))Y.traceId=I.bytes===String?H1.base64.encode(F.traceId,0,F.traceId.length):I.bytes===Array?Array.prototype.slice.call(F.traceId):F.traceId;if(F.spanId!=null&&F.hasOwnProperty("spanId"))Y.spanId=I.bytes===String?H1.base64.encode(F.spanId,0,F.spanId.length):I.bytes===Array?Array.prototype.slice.call(F.spanId):F.spanId;if(F.observedTimeUnixNano!=null&&F.hasOwnProperty("observedTimeUnixNano"))if(typeof F.observedTimeUnixNano==="number")Y.observedTimeUnixNano=I.longs===String?String(F.observedTimeUnixNano):F.observedTimeUnixNano;else Y.observedTimeUnixNano=I.longs===String?H1.Long.prototype.toString.call(F.observedTimeUnixNano):I.longs===Number?new H1.LongBits(F.observedTimeUnixNano.low>>>0,F.observedTimeUnixNano.high>>>0).toNumber():F.observedTimeUnixNano;return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,n9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.logs.v1.LogRecord"},Z}(),D}(),Q}(),B}(),A}();Dp2.exports=X1});
var RT2=E((LT2)=>{Object.defineProperty(LT2,"__esModule",{value:!0});LT2.diag=void 0;var Ht4=Eu();LT2.diag=Ht4.DiagAPI.instance()});
var Rd2=E((Ld2)=>{Object.defineProperty(Ld2,"__esModule",{value:!0});Ld2.MetricStorageRegistry=void 0;var LG6=F31(),Nd2=VQ(),CT1=qd2();class MY0{_sharedRegistry=new Map;_perCollectorRegistry=new Map;static create(){return new MY0}getStorages(A){let B=[];for(let D of this._sharedRegistry.values())B=B.concat(D);let Q=this._perCollectorRegistry.get(A);if(Q!=null)for(let D of Q.values())B=B.concat(D);return B}register(A){this._registerStorage(A,this._sharedRegistry)}registerForCollector(A,B){let Q=this._perCollectorRegistry.get(A);if(Q==null)Q=new Map,this._perCollectorRegistry.set(A,Q);this._registerStorage(B,Q)}findOrUpdateCompatibleStorage(A){let B=this._sharedRegistry.get(A.name);if(B===void 0)return null;return this._findOrUpdateCompatibleStorage(A,B)}findOrUpdateCompatibleCollectorStorage(A,B){let Q=this._perCollectorRegistry.get(A);if(Q===void 0)return null;let D=Q.get(B.name);if(D===void 0)return null;return this._findOrUpdateCompatibleStorage(B,D)}_registerStorage(A,B){let Q=A.getInstrumentDescriptor(),D=B.get(Q.name);if(D===void 0){B.set(Q.name,[A]);return}D.push(A)}_findOrUpdateCompatibleStorage(A,B){let Q=null;for(let D of B){let Z=D.getInstrumentDescriptor();if(LG6.isDescriptorCompatibleWith(Z,A)){if(Z.description!==A.description){if(A.description.length>Z.description.length)D.updateDescription(A.description);Nd2.diag.warn("A view or instrument with the name ",A.name,` has already been registered, but has a different description and is incompatible with another registered view.
`,`Details:
`,CT1.getIncompatibilityDetails(Z,A),`The longer description will be used.
To resolve the conflict:`,CT1.getConflictResolutionRecipe(Z,A))}Q=D}else Nd2.diag.warn("A view or instrument with the name ",A.name,` has already been registered and is incompatible with another registered view.
`,`Details:
`,CT1.getIncompatibilityDetails(Z,A),`To resolve the conflict:
`,CT1.getConflictResolutionRecipe(Z,A))}return Q}}Ld2.MetricStorageRegistry=MY0});
var Rh2=E((Lh2)=>{Object.defineProperty(Lh2,"__esModule",{value:!0});Lh2.CompositePropagator=void 0;var qh2=VQ();class Nh2{_propagators;_fields;constructor(A={}){this._propagators=A.propagators??[],this._fields=Array.from(new Set(this._propagators.map((B)=>typeof B.fields==="function"?B.fields():[]).reduce((B,Q)=>B.concat(Q),[])))}inject(A,B,Q){for(let D of this._propagators)try{D.inject(A,B,Q)}catch(Z){qh2.diag.warn(`Failed to inject with ${D.constructor.name}. Err: ${Z.message}`)}}extract(A,B,Q){return this._propagators.reduce((D,Z)=>{try{return Z.extract(D,B,Q)}catch(G){qh2.diag.warn(`Failed to extract with ${Z.constructor.name}. Err: ${G.message}`)}return D},A)}fields(){return this._fields.slice()}}Lh2.CompositePropagator=Nh2});
var Ru2=E((FY0)=>{Object.defineProperty(FY0,"__esModule",{value:!0});FY0.defaultServiceName=void 0;var bD6=Mu2();Object.defineProperty(FY0,"defaultServiceName",{enumerable:!0,get:function(){return bD6.defaultServiceName}})});
var Sd2=E((Td2)=>{Object.defineProperty(Td2,"__esModule",{value:!0});Td2.MultiMetricStorage=void 0;class Od2{_backingStorages;constructor(A){this._backingStorages=A}record(A,B,Q,D){this._backingStorages.forEach((Z)=>{Z.record(A,B,Q,D)})}}Td2.MultiMetricStorage=Od2});
var Si2=E((Ti2)=>{Object.defineProperty(Ti2,"__esModule",{value:!0});Ti2.createOtlpHttpExportDelegate=void 0;var uY6=yY0(),mY6=qi2(),dY6=jY0(),cY6=Oi2();function lY6(A,B){return uY6.createOtlpExportDelegate({transport:cY6.createRetryingTransport({transport:mY6.createHttpExporterTransport(A)}),serializer:B,promiseHandler:dY6.createBoundedQueueExportPromiseHandler(A)},{timeout:A.timeoutMillis})}Ti2.createOtlpHttpExportDelegate=lY6});
var Sl2=E((Nh5,Pl2)=>{Pl2.exports=eF6;function eF6(A,B,Q){var D=Q||8192,Z=D>>>1,G=null,F=D;return function I(Y){if(Y<1||Y>Z)return A(Y);if(F+Y>D)G=A(D),F=0;var W=B.call(G,F,F+=Y);if(F&7)F=(F|7)+1;return W}}});
var So=E((iP2)=>{Object.defineProperty(iP2,"__esModule",{value:!0});iP2.AggregatorKind=void 0;var fe4;(function(A){A[A.DROP=0]="DROP",A[A.SUM=1]="SUM",A[A.LAST_VALUE=2]="LAST_VALUE",A[A.HISTOGRAM=3]="HISTOGRAM",A[A.EXPONENTIAL_HISTOGRAM=4]="EXPONENTIAL_HISTOGRAM"})(fe4=iP2.AggregatorKind||(iP2.AggregatorKind={}))});
var TT1=E((Hp2)=>{Object.defineProperty(Hp2,"__esModule",{value:!0});Hp2.toAnyValue=Hp2.toKeyValue=Hp2.toAttributes=Hp2.createInstrumentationScope=Hp2.createResource=void 0;function zI6(A){return{attributes:Kp2(A.attributes),droppedAttributesCount:0}}Hp2.createResource=zI6;function EI6(A){return{name:A.name,version:A.version}}Hp2.createInstrumentationScope=EI6;function Kp2(A){return Object.keys(A).map((B)=>BW0(B,A[B]))}Hp2.toAttributes=Kp2;function BW0(A,B){return{key:A,value:QW0(B)}}Hp2.toKeyValue=BW0;function QW0(A){let B=typeof A;if(B==="string")return{stringValue:A};if(B==="number"){if(!Number.isInteger(A))return{doubleValue:A};return{intValue:A}}if(B==="boolean")return{boolValue:A};if(A instanceof Uint8Array)return{bytesValue:A};if(Array.isArray(A))return{arrayValue:{values:A.map(QW0)}};if(B==="object"&&A!=null)return{kvlistValue:{values:Object.entries(A).map(([Q,D])=>BW0(Q,D))}};return{}}Hp2.toAnyValue=QW0});
var Tc2=E((Rc2)=>{Object.defineProperty(Rc2,"__esModule",{value:!0});Rc2.View=void 0;var AF6=HT1(),Nc2=KT1(),BF6=Ec2(),QF6=qc2(),Lc2=Q31();function DF6(A){return A.instrumentName==null&&A.instrumentType==null&&A.instrumentUnit==null&&A.meterName==null&&A.meterVersion==null&&A.meterSchemaUrl==null}function ZF6(A){if(DF6(A))throw new Error("Cannot create view with no selector arguments supplied");if(A.name!=null&&(A?.instrumentName==null||AF6.PatternPredicate.hasWildcard(A.instrumentName)))throw new Error("Views with a specified name must be declared with an instrument selector that selects at most one instrument per meter.")}class Mc2{name;description;aggregation;attributesProcessor;instrumentSelector;meterSelector;aggregationCardinalityLimit;constructor(A){if(ZF6(A),A.attributesProcessors!=null)this.attributesProcessor=Nc2.createMultiAttributesProcessor(A.attributesProcessors);else this.attributesProcessor=Nc2.createNoopAttributesProcessor();this.name=A.name,this.description=A.description,this.aggregation=Lc2.toAggregation(A.aggregation??{type:Lc2.AggregationType.DEFAULT}),this.instrumentSelector=new BF6.InstrumentSelector({name:A.instrumentName,type:A.instrumentType,unit:A.instrumentUnit}),this.meterSelector=new QF6.MeterSelector({name:A.meterName,version:A.meterVersion,schemaUrl:A.meterSchemaUrl}),this.aggregationCardinalityLimit=A.aggregationCardinalityLimit}}Rc2.View=Mc2});
var Tl2=E((Ol2)=>{var hY0=Ol2;hY0.length=function A(B){var Q=0,D=0;for(var Z=0;Z<B.length;++Z)if(D=B.charCodeAt(Z),D<128)Q+=1;else if(D<2048)Q+=2;else if((D&64512)===55296&&(B.charCodeAt(Z+1)&64512)===56320)++Z,Q+=4;else Q+=3;return Q};hY0.read=function A(B,Q,D){var Z=D-Q;if(Z<1)return"";var G=null,F=[],I=0,Y;while(Q<D){if(Y=B[Q++],Y<128)F[I++]=Y;else if(Y>191&&Y<224)F[I++]=(Y&31)<<6|B[Q++]&63;else if(Y>239&&Y<365)Y=((Y&7)<<18|(B[Q++]&63)<<12|(B[Q++]&63)<<6|B[Q++]&63)-65536,F[I++]=55296+(Y>>10),F[I++]=56320+(Y&1023);else F[I++]=(Y&15)<<12|(B[Q++]&63)<<6|B[Q++]&63;if(I>8191)(G||(G=[])).push(String.fromCharCode.apply(String,F)),I=0}if(G){if(I)G.push(String.fromCharCode.apply(String,F.slice(0,I)));return G.join("")}return String.fromCharCode.apply(String,F.slice(0,I))};hY0.write=function A(B,Q,D){var Z=D,G,F;for(var I=0;I<B.length;++I)if(G=B.charCodeAt(I),G<128)Q[D++]=G;else if(G<2048)Q[D++]=G>>6|192,Q[D++]=G&63|128;else if((G&64512)===55296&&((F=B.charCodeAt(I+1))&64512)===56320)G=65536+((G&1023)<<10)+(F&1023),++I,Q[D++]=G>>18|240,Q[D++]=G>>12&63|128,Q[D++]=G>>6&63|128,Q[D++]=G&63|128;else Q[D++]=G>>12|224,Q[D++]=G>>6&63|128,Q[D++]=G&63|128;return D-Z}});
var Tm2=E((xo)=>{Object.defineProperty(xo,"__esModule",{value:!0});xo.serviceInstanceIdDetector=xo.processDetector=xo.osDetector=xo.hostDetector=void 0;var TZ6=Vm2();Object.defineProperty(xo,"hostDetector",{enumerable:!0,get:function(){return TZ6.hostDetector}});var PZ6=Um2();Object.defineProperty(xo,"osDetector",{enumerable:!0,get:function(){return PZ6.osDetector}});var SZ6=Nm2();Object.defineProperty(xo,"processDetector",{enumerable:!0,get:function(){return SZ6.processDetector}});var jZ6=Om2();Object.defineProperty(xo,"serviceInstanceIdDetector",{enumerable:!0,get:function(){return jZ6.serviceInstanceIdDetector}})});
var Ug2=E((zg2)=>{Object.defineProperty(zg2,"__esModule",{value:!0});zg2.BindOnceFuture=void 0;var s76=Kg2();class Hg2{_callback;_that;_isCalled=!1;_deferred=new s76.Deferred;constructor(A,B){this._callback=A,this._that=B}get isCalled(){return this._isCalled}get promise(){return this._deferred.promise}call(...A){if(!this._isCalled){this._isCalled=!0;try{Promise.resolve(this._callback.call(this._that,...A)).then((B)=>this._deferred.resolve(B),(B)=>this._deferred.reject(B))}catch(B){this._deferred.reject(B)}}return this._deferred.promise}}zg2.BindOnceFuture=Hg2});
var Uh2=E((zh2)=>{Object.defineProperty(zh2,"__esModule",{value:!0});zh2.addHrTimes=zh2.isTimeInput=zh2.isTimeInputHrTime=zh2.hrTimeToMicroseconds=zh2.hrTimeToMilliseconds=zh2.hrTimeToNanoseconds=zh2.hrTimeToTimeStamp=zh2.hrTimeDuration=zh2.timeInputToHrTime=zh2.hrTime=zh2.getTimeOrigin=zh2.millisToHrTime=void 0;var cI0=dI0(),Ch2=9,g36=6,u36=Math.pow(10,g36),aO1=Math.pow(10,Ch2);function o51(A){let B=A/1000,Q=Math.trunc(B),D=Math.round(A%1000*u36);return[Q,D]}zh2.millisToHrTime=o51;function lI0(){let A=cI0.otperformance.timeOrigin;if(typeof A!=="number"){let B=cI0.otperformance;A=B.timing&&B.timing.fetchStart}return A}zh2.getTimeOrigin=lI0;function Kh2(A){let B=o51(lI0()),Q=o51(typeof A==="number"?A:cI0.otperformance.now());return Hh2(B,Q)}zh2.hrTime=Kh2;function m36(A){if(pI0(A))return A;else if(typeof A==="number")if(A<lI0())return Kh2(A);else return o51(A);else if(A instanceof Date)return o51(A.getTime());else throw TypeError("Invalid input type")}zh2.timeInputToHrTime=m36;function d36(A,B){let Q=B[0]-A[0],D=B[1]-A[1];if(D<0)Q-=1,D+=aO1;return[Q,D]}zh2.hrTimeDuration=d36;function c36(A){let B=Ch2,Q=`${"0".repeat(B)}${A[1]}Z`,D=Q.substring(Q.length-B-1);return new Date(A[0]*1000).toISOString().replace("000Z",D)}zh2.hrTimeToTimeStamp=c36;function l36(A){return A[0]*aO1+A[1]}zh2.hrTimeToNanoseconds=l36;function p36(A){return A[0]*1000+A[1]/1e6}zh2.hrTimeToMilliseconds=p36;function i36(A){return A[0]*1e6+A[1]/1000}zh2.hrTimeToMicroseconds=i36;function pI0(A){return Array.isArray(A)&&A.length===2&&typeof A[0]==="number"&&typeof A[1]==="number"}zh2.isTimeInputHrTime=pI0;function n36(A){return pI0(A)||typeof A==="number"||A instanceof Date}zh2.isTimeInput=n36;function Hh2(A,B){let Q=[A[0]+B[0],A[1]+B[1]];if(Q[1]>=aO1)Q[1]-=aO1,Q[0]+=1;return Q}zh2.addHrTimes=Hh2});
var Uj2=E((zj2)=>{Object.defineProperty(zj2,"__esModule",{value:!0});zj2.otperformance=void 0;var o16=J1("perf_hooks");zj2.otperformance=o16.performance});
var Um2=E((zm2)=>{Object.defineProperty(zm2,"__esModule",{value:!0});zm2.osDetector=void 0;var Cm2=CP(),Km2=J1("os"),NZ6=HY0();class Hm2{detect(A){return{attributes:{[Cm2.SEMRESATTRS_OS_TYPE]:NZ6.normalizeType(Km2.platform()),[Cm2.SEMRESATTRS_OS_VERSION]:Km2.release()}}}}zm2.osDetector=new Hm2});
var VQ=E((n5)=>{Object.defineProperty(n5,"__esModule",{value:!0});n5.trace=n5.propagation=n5.metrics=n5.diag=n5.context=n5.INVALID_SPAN_CONTEXT=n5.INVALID_TRACEID=n5.INVALID_SPANID=n5.isValidSpanId=n5.isValidTraceId=n5.isSpanContextValid=n5.createTraceState=n5.TraceFlags=n5.SpanStatusCode=n5.SpanKind=n5.SamplingDecision=n5.ProxyTracerProvider=n5.ProxyTracer=n5.defaultTextMapSetter=n5.defaultTextMapGetter=n5.ValueType=n5.createNoopMeter=n5.DiagLogLevel=n5.DiagConsoleLogger=n5.ROOT_CONTEXT=n5.createContextKey=n5.baggageEntryMetadataFromString=void 0;var _t4=PF0();Object.defineProperty(n5,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return _t4.baggageEntryMetadataFromString}});var IP2=c51();Object.defineProperty(n5,"createContextKey",{enumerable:!0,get:function(){return IP2.createContextKey}});Object.defineProperty(n5,"ROOT_CONTEXT",{enumerable:!0,get:function(){return IP2.ROOT_CONTEXT}});var xt4=DO2();Object.defineProperty(n5,"DiagConsoleLogger",{enumerable:!0,get:function(){return xt4.DiagConsoleLogger}});var vt4=kO1();Object.defineProperty(n5,"DiagLogLevel",{enumerable:!0,get:function(){return vt4.DiagLogLevel}});var bt4=hF0();Object.defineProperty(n5,"createNoopMeter",{enumerable:!0,get:function(){return bt4.createNoopMeter}});var ft4=HO2();Object.defineProperty(n5,"ValueType",{enumerable:!0,get:function(){return ft4.ValueType}});var YP2=uF0();Object.defineProperty(n5,"defaultTextMapGetter",{enumerable:!0,get:function(){return YP2.defaultTextMapGetter}});Object.defineProperty(n5,"defaultTextMapSetter",{enumerable:!0,get:function(){return YP2.defaultTextMapSetter}});var ht4=tF0();Object.defineProperty(n5,"ProxyTracer",{enumerable:!0,get:function(){return ht4.ProxyTracer}});var gt4=eF0();Object.defineProperty(n5,"ProxyTracerProvider",{enumerable:!0,get:function(){return gt4.ProxyTracerProvider}});var ut4=QT2();Object.defineProperty(n5,"SamplingDecision",{enumerable:!0,get:function(){return ut4.SamplingDecision}});var mt4=ZT2();Object.defineProperty(n5,"SpanKind",{enumerable:!0,get:function(){return mt4.SpanKind}});var dt4=FT2();Object.defineProperty(n5,"SpanStatusCode",{enumerable:!0,get:function(){return dt4.SpanStatusCode}});var ct4=pF0();Object.defineProperty(n5,"TraceFlags",{enumerable:!0,get:function(){return ct4.TraceFlags}});var lt4=wT2();Object.defineProperty(n5,"createTraceState",{enumerable:!0,get:function(){return lt4.createTraceState}});var zI0=fO1();Object.defineProperty(n5,"isSpanContextValid",{enumerable:!0,get:function(){return zI0.isSpanContextValid}});Object.defineProperty(n5,"isValidTraceId",{enumerable:!0,get:function(){return zI0.isValidTraceId}});Object.defineProperty(n5,"isValidSpanId",{enumerable:!0,get:function(){return zI0.isValidSpanId}});var EI0=vO1();Object.defineProperty(n5,"INVALID_SPANID",{enumerable:!0,get:function(){return EI0.INVALID_SPANID}});Object.defineProperty(n5,"INVALID_TRACEID",{enumerable:!0,get:function(){return EI0.INVALID_TRACEID}});Object.defineProperty(n5,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return EI0.INVALID_SPAN_CONTEXT}});var WP2=NT2();Object.defineProperty(n5,"context",{enumerable:!0,get:function(){return WP2.context}});var JP2=RT2();Object.defineProperty(n5,"diag",{enumerable:!0,get:function(){return JP2.diag}});var XP2=vT2();Object.defineProperty(n5,"metrics",{enumerable:!0,get:function(){return XP2.metrics}});var VP2=oT2();Object.defineProperty(n5,"propagation",{enumerable:!0,get:function(){return VP2.propagation}});var CP2=FP2();Object.defineProperty(n5,"trace",{enumerable:!0,get:function(){return CP2.trace}});n5.default={context:WP2.context,diag:JP2.diag,metrics:XP2.metrics,propagation:VP2.propagation,trace:CP2.trace}});
var VT1=E((am2)=>{Object.defineProperty(am2,"__esModule",{value:!0});am2.isObservableInstrument=am2.ObservableUpDownCounterInstrument=am2.ObservableGaugeInstrument=am2.ObservableCounterInstrument=am2.ObservableInstrument=am2.HistogramInstrument=am2.GaugeInstrument=am2.CounterInstrument=am2.UpDownCounterInstrument=am2.SyncInstrument=void 0;var bo=VQ(),aZ6=x3();class fo{_writableMetricStorage;_descriptor;constructor(A,B){this._writableMetricStorage=A,this._descriptor=B}_record(A,B={},Q=bo.context.active()){if(typeof A!=="number"){bo.diag.warn(`non-number value provided to metric ${this._descriptor.name}: ${A}`);return}if(this._descriptor.valueType===bo.ValueType.INT&&!Number.isInteger(A)){if(bo.diag.warn(`INT value type cannot accept a floating-point value for ${this._descriptor.name}, ignoring the fractional digits.`),A=Math.trunc(A),!Number.isInteger(A))return}this._writableMetricStorage.record(A,B,Q,aZ6.millisToHrTime(Date.now()))}}am2.SyncInstrument=fo;class mm2 extends fo{add(A,B,Q){this._record(A,B,Q)}}am2.UpDownCounterInstrument=mm2;class dm2 extends fo{add(A,B,Q){if(A<0){bo.diag.warn(`negative value provided to counter ${this._descriptor.name}: ${A}`);return}this._record(A,B,Q)}}am2.CounterInstrument=dm2;class cm2 extends fo{record(A,B,Q){this._record(A,B,Q)}}am2.GaugeInstrument=cm2;class lm2 extends fo{record(A,B,Q){if(A<0){bo.diag.warn(`negative value provided to histogram ${this._descriptor.name}: ${A}`);return}this._record(A,B,Q)}}am2.HistogramInstrument=lm2;class ho{_observableRegistry;_metricStorages;_descriptor;constructor(A,B,Q){this._observableRegistry=Q,this._descriptor=A,this._metricStorages=B}addCallback(A){this._observableRegistry.addCallback(A,this)}removeCallback(A){this._observableRegistry.removeCallback(A,this)}}am2.ObservableInstrument=ho;class pm2 extends ho{}am2.ObservableCounterInstrument=pm2;class im2 extends ho{}am2.ObservableGaugeInstrument=im2;class nm2 extends ho{}am2.ObservableUpDownCounterInstrument=nm2;function sZ6(A){return A instanceof ho}am2.isObservableInstrument=sZ6});
var VW0=E((_i2)=>{Object.defineProperty(_i2,"__esModule",{value:!0});_i2.getSharedConfigurationFromEnvironment=void 0;var ki2=VQ();function ji2(A){let B=process.env[A]?.trim();if(B!=null&&B!==""){let Q=Number(B);if(Number.isFinite(Q)&&Q>0)return Q;ki2.diag.warn(`Configuration: ${A} is invalid, expected number greater than 0 (actual: ${B})`)}return}function pY6(A){let B=ji2(`OTEL_EXPORTER_OTLP_${A}_TIMEOUT`),Q=ji2("OTEL_EXPORTER_OTLP_TIMEOUT");return B??Q}function yi2(A){let B=process.env[A]?.trim();if(B==="")return;if(B==null||B==="none"||B==="gzip")return B;ki2.diag.warn(`Configuration: ${A} is invalid, expected 'none' or 'gzip' (actual: '${B}')`);return}function iY6(A){let B=yi2(`OTEL_EXPORTER_OTLP_${A}_COMPRESSION`),Q=yi2("OTEL_EXPORTER_OTLP_COMPRESSION");return B??Q}function nY6(A){return{timeoutMillis:pY6(A),compression:iY6(A)}}_i2.getSharedConfigurationFromEnvironment=nY6});
var VY0=E((Su2)=>{Object.defineProperty(Su2,"__esModule",{value:!0});Su2.defaultResource=Su2.emptyResource=Su2.resourceFromDetectedResource=Su2.resourceFromAttributes=void 0;var WY0=VQ(),JY0=x3(),Pu=CP(),cD6=YY0(),IT1=Pu2();class Z31{_rawAttributes;_asyncAttributesPending=!1;_memoizedAttributes;static FromAttributeList(A){let B=new Z31({});return B._rawAttributes=A,B._asyncAttributesPending=A.filter(([Q,D])=>IT1.isPromiseLike(D)).length>0,B}constructor(A){let B=A.attributes??{};this._rawAttributes=Object.entries(B).map(([Q,D])=>{if(IT1.isPromiseLike(D))this._asyncAttributesPending=!0;return[Q,D]})}get asyncAttributesPending(){return this._asyncAttributesPending}async waitForAsyncAttributes(){if(!this.asyncAttributesPending)return;for(let A=0;A<this._rawAttributes.length;A++){let[B,Q]=this._rawAttributes[A];try{this._rawAttributes[A]=[B,IT1.isPromiseLike(Q)?await Q:Q]}catch(D){WY0.diag.debug("a resource's async attributes promise rejected: %s",D),this._rawAttributes[A]=[B,void 0]}}this._asyncAttributesPending=!1}get attributes(){if(this.asyncAttributesPending)WY0.diag.error("Accessing resource attributes before async attributes settled");if(this._memoizedAttributes)return this._memoizedAttributes;let A={};for(let[B,Q]of this._rawAttributes){if(IT1.isPromiseLike(Q)){WY0.diag.debug(`Unsettled resource attribute ${B} skipped`);continue}if(Q!=null)A[B]??=Q}if(!this._asyncAttributesPending)this._memoizedAttributes=A;return A}getRawAttributes(){return this._rawAttributes}merge(A){if(A==null)return this;return Z31.FromAttributeList([...A.getRawAttributes(),...this.getRawAttributes()])}}function XY0(A){return Z31.FromAttributeList(Object.entries(A))}Su2.resourceFromAttributes=XY0;function lD6(A){return new Z31(A)}Su2.resourceFromDetectedResource=lD6;function pD6(){return XY0({})}Su2.emptyResource=pD6;function iD6(){return XY0({[Pu.ATTR_SERVICE_NAME]:cD6.defaultServiceName(),[Pu.ATTR_TELEMETRY_SDK_LANGUAGE]:JY0.SDK_INFO[Pu.ATTR_TELEMETRY_SDK_LANGUAGE],[Pu.ATTR_TELEMETRY_SDK_NAME]:JY0.SDK_INFO[Pu.ATTR_TELEMETRY_SDK_NAME],[Pu.ATTR_TELEMETRY_SDK_VERSION]:JY0.SDK_INFO[Pu.ATTR_TELEMETRY_SDK_VERSION]})}Su2.defaultResource=iD6});
var Vh2=E((rL)=>{Object.defineProperty(rL,"__esModule",{value:!0});rL.unrefTimer=rL.SDK_INFO=rL.otperformance=rL._globalThis=rL.getStringListFromEnv=rL.getNumberFromEnv=rL.getBooleanFromEnv=rL.getStringFromEnv=void 0;var nO1=Vj2();Object.defineProperty(rL,"getStringFromEnv",{enumerable:!0,get:function(){return nO1.getStringFromEnv}});Object.defineProperty(rL,"getBooleanFromEnv",{enumerable:!0,get:function(){return nO1.getBooleanFromEnv}});Object.defineProperty(rL,"getNumberFromEnv",{enumerable:!0,get:function(){return nO1.getNumberFromEnv}});Object.defineProperty(rL,"getStringListFromEnv",{enumerable:!0,get:function(){return nO1.getStringListFromEnv}});var _36=Hj2();Object.defineProperty(rL,"_globalThis",{enumerable:!0,get:function(){return _36._globalThis}});var x36=Uj2();Object.defineProperty(rL,"otperformance",{enumerable:!0,get:function(){return x36.otperformance}});var v36=Yh2();Object.defineProperty(rL,"SDK_INFO",{enumerable:!0,get:function(){return v36.SDK_INFO}});var b36=Xh2();Object.defineProperty(rL,"unrefTimer",{enumerable:!0,get:function(){return b36.unrefTimer}})});
var Vj2=E((Jj2)=>{Object.defineProperty(Jj2,"__esModule",{value:!0});Jj2.getStringListFromEnv=Jj2.getBooleanFromEnv=Jj2.getStringFromEnv=Jj2.getNumberFromEnv=void 0;var Ij2=VQ(),Yj2=J1("util");function p16(A){let B=process.env[A];if(B==null||B.trim()==="")return;let Q=Number(B);if(isNaN(Q)){Ij2.diag.warn(`Unknown value ${Yj2.inspect(B)} for ${A}, expected a number, using defaults`);return}return Q}Jj2.getNumberFromEnv=p16;function Wj2(A){let B=process.env[A];if(B==null||B.trim()==="")return;return B}Jj2.getStringFromEnv=Wj2;function i16(A){let B=process.env[A]?.trim().toLowerCase();if(B==null||B==="")return!1;if(B==="true")return!0;else if(B==="false")return!1;else return Ij2.diag.warn(`Unknown value ${Yj2.inspect(B)} for ${A}, expected 'true' or 'false', falling back to 'false' (default)`),!1}Jj2.getBooleanFromEnv=i16;function n16(A){return Wj2(A)?.split(",").map((B)=>B.trim()).filter((B)=>B!=="")}Jj2.getStringListFromEnv=n16});
var Vm2=E((Jm2)=>{Object.defineProperty(Jm2,"__esModule",{value:!0});Jm2.hostDetector=void 0;var zY0=CP(),Ym2=J1("os"),$Z6=Gm2(),qZ6=HY0();class Wm2{detect(A){return{attributes:{[zY0.SEMRESATTRS_HOST_NAME]:Ym2.hostname(),[zY0.SEMRESATTRS_HOST_ARCH]:qZ6.normalizeArch(Ym2.arch()),[zY0.SEMRESATTRS_HOST_ID]:$Z6.getMachineId()}}}}Jm2.hostDetector=new Wm2});
var Vu2=E((Ju2)=>{Object.defineProperty(Ju2,"__esModule",{value:!0});Ju2.PeriodicExportingMetricReader=void 0;var ZY0=VQ(),D31=x3(),_D6=DY0(),Yu2=tw();class Wu2 extends _D6.MetricReader{_interval;_exporter;_exportInterval;_exportTimeout;constructor(A){super({aggregationSelector:A.exporter.selectAggregation?.bind(A.exporter),aggregationTemporalitySelector:A.exporter.selectAggregationTemporality?.bind(A.exporter),metricProducers:A.metricProducers});if(A.exportIntervalMillis!==void 0&&A.exportIntervalMillis<=0)throw Error("exportIntervalMillis must be greater than 0");if(A.exportTimeoutMillis!==void 0&&A.exportTimeoutMillis<=0)throw Error("exportTimeoutMillis must be greater than 0");if(A.exportTimeoutMillis!==void 0&&A.exportIntervalMillis!==void 0&&A.exportIntervalMillis<A.exportTimeoutMillis)throw Error("exportIntervalMillis must be greater than or equal to exportTimeoutMillis");this._exportInterval=A.exportIntervalMillis??60000,this._exportTimeout=A.exportTimeoutMillis??30000,this._exporter=A.exporter}async _runOnce(){try{await Yu2.callWithTimeout(this._doRun(),this._exportTimeout)}catch(A){if(A instanceof Yu2.TimeoutError){ZY0.diag.error("Export took longer than %s milliseconds and timed out.",this._exportTimeout);return}D31.globalErrorHandler(A)}}async _doRun(){let{resourceMetrics:A,errors:B}=await this.collect({timeoutMillis:this._exportTimeout});if(B.length>0)ZY0.diag.error("PeriodicExportingMetricReader: metrics collection errors",...B);if(A.resource.asyncAttributesPending)try{await A.resource.waitForAsyncAttributes?.()}catch(D){ZY0.diag.debug("Error while resolving async portion of resource: ",D),D31.globalErrorHandler(D)}if(A.scopeMetrics.length===0)return;let Q=await D31.internal._export(this._exporter,A);if(Q.code!==D31.ExportResultCode.SUCCESS)throw new Error(`PeriodicExportingMetricReader: metrics export failed (error ${Q.error})`)}onInitialized(){this._interval=setInterval(()=>{this._runOnce()},this._exportInterval),D31.unrefTimer(this._interval)}async onForceFlush(){await this._runOnce(),await this._exporter.forceFlush()}async onShutdown(){if(this._interval)clearInterval(this._interval);await this.onForceFlush(),await this._exporter.shutdown()}}Ju2.PeriodicExportingMetricReader=Wu2});
var WT2=E((IT2)=>{Object.defineProperty(IT2,"__esModule",{value:!0});IT2.validateValue=IT2.validateKey=void 0;var DI0="[_0-9a-z-*/]",Dt4=`[a-z]${DI0}{0,255}`,Zt4=`[a-z0-9]${DI0}{0,240}@[a-z]${DI0}{0,13}`,Gt4=new RegExp(`^(?:${Dt4}|${Zt4})$`),Ft4=/^[ -~]{0,255}[!-~]$/,It4=/,|=/;function Yt4(A){return Gt4.test(A)}IT2.validateKey=Yt4;function Wt4(A){return Ft4.test(A)&&!It4.test(A)}IT2.validateValue=Wt4});
var Wc2=E((Ic2)=>{Object.defineProperty(Ic2,"__esModule",{value:!0});Ic2.MetricCollector=void 0;var oG6=x3();class Fc2{_sharedState;_metricReader;constructor(A,B){this._sharedState=A,this._metricReader=B}async collect(A){let B=oG6.millisToHrTime(Date.now()),Q=[],D=[],Z=Array.from(this._sharedState.meterSharedStates.values()).map(async(G)=>{let F=await G.collect(this,B,A);if(F?.scopeMetrics!=null)Q.push(F.scopeMetrics);if(F?.errors!=null)D.push(...F.errors)});return await Promise.all(Z),{resourceMetrics:{resource:this._sharedState.resource,scopeMetrics:Q},errors:D}}async forceFlush(A){await this._metricReader.forceFlush(A)}async shutdown(A){await this._metricReader.shutdown(A)}selectAggregationTemporality(A){return this._metricReader.selectAggregationTemporality(A)}selectAggregation(A){return this._metricReader.selectAggregation(A)}selectCardinalityLimit(A){return this._metricReader.selectCardinalityLimit?.(A)??2000}}Ic2.MetricCollector=Fc2});
var Wn2=E((In2)=>{Object.defineProperty(In2,"__esModule",{value:!0});In2.OTLPMetricExporter=void 0;var jW6=yT1(),yW6=fu(),kW6=Zn2(),Gn2=co();class Fn2 extends jW6.OTLPMetricExporterBase{constructor(A){super(Gn2.createOtlpHttpExportDelegate(Gn2.convertLegacyHttpOptions(A??{},"METRICS","v1/metrics",{"User-Agent":`OTel-OTLP-Exporter-JavaScript/${kW6.VERSION}`,"Content-Type":"application/x-protobuf"}),yW6.ProtobufMetricsSerializer),A)}}In2.OTLPMetricExporter=Fn2});
var XT1=E((KE)=>{Object.defineProperty(KE,"__esModule",{value:!0});KE.defaultServiceName=KE.emptyResource=KE.defaultResource=KE.resourceFromAttributes=KE.serviceInstanceIdDetector=KE.processDetector=KE.osDetector=KE.hostDetector=KE.envDetector=KE.detectResources=void 0;var fZ6=_u2();Object.defineProperty(KE,"detectResources",{enumerable:!0,get:function(){return fZ6.detectResources}});var G31=km2();Object.defineProperty(KE,"envDetector",{enumerable:!0,get:function(){return G31.envDetector}});Object.defineProperty(KE,"hostDetector",{enumerable:!0,get:function(){return G31.hostDetector}});Object.defineProperty(KE,"osDetector",{enumerable:!0,get:function(){return G31.osDetector}});Object.defineProperty(KE,"processDetector",{enumerable:!0,get:function(){return G31.processDetector}});Object.defineProperty(KE,"serviceInstanceIdDetector",{enumerable:!0,get:function(){return G31.serviceInstanceIdDetector}});var UY0=VY0();Object.defineProperty(KE,"resourceFromAttributes",{enumerable:!0,get:function(){return UY0.resourceFromAttributes}});Object.defineProperty(KE,"defaultResource",{enumerable:!0,get:function(){return UY0.defaultResource}});Object.defineProperty(KE,"emptyResource",{enumerable:!0,get:function(){return UY0.emptyResource}});var hZ6=YY0();Object.defineProperty(KE,"defaultServiceName",{enumerable:!0,get:function(){return hZ6.defaultServiceName}})});
var Xh2=E((Wh2)=>{Object.defineProperty(Wh2,"__esModule",{value:!0});Wh2.unrefTimer=void 0;function k36(A){A.unref()}Wh2.unrefTimer=k36});
var YT1=E((gu2)=>{Object.defineProperty(gu2,"__esModule",{value:!0});gu2.execAsync=void 0;var AZ6=J1("child_process"),BZ6=J1("util");gu2.execAsync=BZ6.promisify(AZ6.exec)});
var YY0=E((IY0)=>{Object.defineProperty(IY0,"__esModule",{value:!0});IY0.defaultServiceName=void 0;var hD6=Ru2();Object.defineProperty(IY0,"defaultServiceName",{enumerable:!0,get:function(){return hD6.defaultServiceName}})});
var Yh2=E((Fh2)=>{Object.defineProperty(Fh2,"__esModule",{value:!0});Fh2.SDK_INFO=void 0;var y36=qj2(),r51=CP();Fh2.SDK_INFO={[r51.SEMRESATTRS_TELEMETRY_SDK_NAME]:"opentelemetry",[r51.SEMRESATTRS_PROCESS_RUNTIME_NAME]:"node",[r51.SEMRESATTRS_TELEMETRY_SDK_LANGUAGE]:r51.TELEMETRYSDKLANGUAGEVALUES_NODEJS,[r51.SEMRESATTRS_TELEMETRY_SDK_VERSION]:y36.VERSION}});
var ZT2=E((DT2)=>{Object.defineProperty(DT2,"__esModule",{value:!0});DT2.SpanKind=void 0;var Bt4;(function(A){A[A.INTERNAL=0]="INTERNAL",A[A.SERVER=1]="SERVER",A[A.CLIENT=2]="CLIENT",A[A.PRODUCER=3]="PRODUCER",A[A.CONSUMER=4]="CONSUMER"})(Bt4=DT2.SpanKind||(DT2.SpanKind={}))});
var Zi2=E((XW0)=>{Object.defineProperty(XW0,"__esModule",{value:!0});XW0.JsonTraceSerializer=void 0;var CY6=Di2();Object.defineProperty(XW0,"JsonTraceSerializer",{enumerable:!0,get:function(){return CY6.JsonTraceSerializer}})});
var Zn2=E((Qn2)=>{Object.defineProperty(Qn2,"__esModule",{value:!0});Qn2.VERSION=void 0;Qn2.VERSION="0.200.0"});
var _I0=E((ZS2)=>{Object.defineProperty(ZS2,"__esModule",{value:!0});ZS2.getSignificand=ZS2.getNormalBase2=ZS2.MIN_VALUE=ZS2.MAX_NORMAL_EXPONENT=ZS2.MIN_NORMAL_EXPONENT=ZS2.SIGNIFICAND_WIDTH=void 0;ZS2.SIGNIFICAND_WIDTH=52;var ce4=2146435072,le4=1048575,kI0=1023;ZS2.MIN_NORMAL_EXPONENT=-kI0+1;ZS2.MAX_NORMAL_EXPONENT=kI0;ZS2.MIN_VALUE=Math.pow(2,-1022);function pe4(A){let B=new DataView(new ArrayBuffer(8));return B.setFloat64(0,A),((B.getUint32(0)&ce4)>>20)-kI0}ZS2.getNormalBase2=pe4;function ie4(A){let B=new DataView(new ArrayBuffer(8));B.setFloat64(0,A);let Q=B.getUint32(0),D=B.getUint32(4);return(Q&le4)*Math.pow(2,32)+D}ZS2.getSignificand=ie4});
var _u2=E((yu2)=>{Object.defineProperty(yu2,"__esModule",{value:!0});yu2.detectResources=void 0;var KY0=VQ(),CY0=VY0(),rD6=(A={})=>{let B=(A.detectors||[]).map((Q)=>{try{let D=CY0.resourceFromDetectedResource(Q.detect(A));return KY0.diag.debug(`${Q.constructor.name} found resource.`,D),D}catch(D){return KY0.diag.debug(`${Q.constructor.name} failed: ${D.message}`),CY0.emptyResource()}});return oD6(B),B.reduce((Q,D)=>Q.merge(D),CY0.emptyResource())};yu2.detectResources=rD6;var oD6=(A)=>{A.forEach((B)=>{if(Object.keys(B.attributes).length>0){let Q=JSON.stringify(B.attributes,null,4);KY0.diag.verbose(Q)}})}});
var aF0=E((xO2)=>{Object.defineProperty(xO2,"__esModule",{value:!0});xO2.getSpanContext=xO2.setSpanContext=xO2.deleteSpan=xO2.setSpan=xO2.getActiveSpan=xO2.getSpan=void 0;var Ro4=c51(),Oo4=bO1(),To4=p51(),iF0=Ro4.createContextKey("OpenTelemetry Context Key SPAN");function nF0(A){return A.getValue(iF0)||void 0}xO2.getSpan=nF0;function Po4(){return nF0(To4.ContextAPI.getInstance().active())}xO2.getActiveSpan=Po4;function _O2(A,B){return A.setValue(iF0,B)}xO2.setSpan=_O2;function So4(A){return A.deleteValue(iF0)}xO2.deleteSpan=So4;function jo4(A,B){return _O2(A,new Oo4.NonRecordingSpan(B))}xO2.setSpanContext=jo4;function yo4(A){var B;return(B=nF0(A))===null||B===void 0?void 0:B.spanContext()}xO2.getSpanContext=yo4});
var aT2=E((iT2)=>{Object.defineProperty(iT2,"__esModule",{value:!0});iT2.PropagationAPI=void 0;var JI0=zu(),Pt4=gT2(),lT2=uF0(),hO1=cT2(),St4=PF0(),pT2=Eu(),XI0="propagation",jt4=new Pt4.NoopTextMapPropagator;class VI0{constructor(){this.createBaggage=St4.createBaggage,this.getBaggage=hO1.getBaggage,this.getActiveBaggage=hO1.getActiveBaggage,this.setBaggage=hO1.setBaggage,this.deleteBaggage=hO1.deleteBaggage}static getInstance(){if(!this._instance)this._instance=new VI0;return this._instance}setGlobalPropagator(A){return JI0.registerGlobal(XI0,A,pT2.DiagAPI.instance())}inject(A,B,Q=lT2.defaultTextMapSetter){return this._getGlobalPropagator().inject(A,B,Q)}extract(A,B,Q=lT2.defaultTextMapGetter){return this._getGlobalPropagator().extract(A,B,Q)}fields(){return this._getGlobalPropagator().fields()}disable(){JI0.unregisterGlobal(XI0,pT2.DiagAPI.instance())}_getGlobalPropagator(){return JI0.getGlobal(XI0)||jt4}}iT2.PropagationAPI=VI0});
var ai2=E((ii2)=>{Object.defineProperty(ii2,"__esModule",{value:!0});ii2.convertLegacyHttpOptions=void 0;var pi2=mi2(),JW6=li2(),XW6=VQ(),VW6=J31();function CW6(A){if(A?.keepAlive!=null)if(A.httpAgentOptions!=null){if(A.httpAgentOptions.keepAlive==null)A.httpAgentOptions.keepAlive=A.keepAlive}else A.httpAgentOptions={keepAlive:A.keepAlive};return A.httpAgentOptions}function KW6(A,B,Q,D){if(A.metadata)XW6.diag.warn("Metadata cannot be set when using http");return pi2.mergeOtlpHttpConfigurationWithDefaults({url:A.url,headers:VW6.wrapStaticHeadersInFunction(A.headers),concurrencyLimit:A.concurrencyLimit,timeoutMillis:A.timeoutMillis,compression:A.compression,agentOptions:CW6(A)},JW6.getHttpConfigurationFromEnvironment(B,Q),pi2.getHttpConfigurationDefaults(D,Q))}ii2.convertLegacyHttpOptions=KW6});
var bI0=E((vS2)=>{Object.defineProperty(vS2,"__esModule",{value:!0});vS2.BAGGAGE_MAX_TOTAL_LENGTH=vS2.BAGGAGE_MAX_PER_NAME_VALUE_PAIRS=vS2.BAGGAGE_MAX_NAME_VALUE_PAIRS=vS2.BAGGAGE_HEADER=vS2.BAGGAGE_ITEMS_SEPARATOR=vS2.BAGGAGE_PROPERTIES_SEPARATOR=vS2.BAGGAGE_KEY_PAIR_SEPARATOR=void 0;vS2.BAGGAGE_KEY_PAIR_SEPARATOR="=";vS2.BAGGAGE_PROPERTIES_SEPARATOR=";";vS2.BAGGAGE_ITEMS_SEPARATOR=",";vS2.BAGGAGE_HEADER="baggage";vS2.BAGGAGE_MAX_NAME_VALUE_PAIRS=180;vS2.BAGGAGE_MAX_PER_NAME_VALUE_PAIRS=4096;vS2.BAGGAGE_MAX_TOTAL_LENGTH=8192});
var bO1=E((yO2)=>{Object.defineProperty(yO2,"__esModule",{value:!0});yO2.NonRecordingSpan=void 0;var Mo4=vO1();class jO2{constructor(A=Mo4.INVALID_SPAN_CONTEXT){this._spanContext=A}spanContext(){return this._spanContext}setAttribute(A,B){return this}setAttributes(A){return this}addEvent(A,B){return this}addLink(A){return this}addLinks(A){return this}setStatus(A){return this}updateName(A){return this}end(A){}isRecording(){return!1}recordException(A,B){}}yO2.NonRecordingSpan=jO2});
var bR2=E((xR2)=>{Object.defineProperty(xR2,"__esModule",{value:!0});xR2.DiagComponentLogger=void 0;var pr4=zu();class _R2{constructor(A){this._namespace=A.namespace||"DiagComponentLogger"}debug(...A){return d51("debug",this._namespace,A)}error(...A){return d51("error",this._namespace,A)}info(...A){return d51("info",this._namespace,A)}warn(...A){return d51("warn",this._namespace,A)}verbose(...A){return d51("verbose",this._namespace,A)}}xR2.DiagComponentLogger=_R2;function d51(A,B,Q){let D=pr4.getGlobal("diag");if(!D)return;return Q.unshift(B),D[A](...Q)}});
var bm2=E((xm2)=>{Object.defineProperty(xm2,"__esModule",{value:!0});xm2.ViewRegistry=void 0;class _m2{_registeredViews=[];addView(A){this._registeredViews.push(A)}findViews(A,B){return this._registeredViews.filter((D)=>{return this._matchInstrument(D.instrumentSelector,A)&&this._matchMeter(D.meterSelector,B)})}_matchInstrument(A,B){return(A.getType()===void 0||B.type===A.getType())&&A.getNameFilter().match(B.name)&&A.getUnitFilter().match(B.unit)}_matchMeter(A,B){return A.getNameFilter().match(B.name)&&(B.version===void 0||A.getVersionFilter().match(B.version))&&(B.schemaUrl===void 0||A.getSchemaUrlFilter().match(B.schemaUrl))}}xm2.ViewRegistry=_m2});
var bp2=E((FW0)=>{Object.defineProperty(FW0,"__esModule",{value:!0});FW0.ProtobufMetricsSerializer=void 0;var iI6=vp2();Object.defineProperty(FW0,"ProtobufMetricsSerializer",{enumerable:!0,get:function(){return iI6.ProtobufMetricsSerializer}})});
var c51=E((tR2)=>{Object.defineProperty(tR2,"__esModule",{value:!0});tR2.ROOT_CONTEXT=tR2.createContextKey=void 0;function Zo4(A){return Symbol.for(A)}tR2.createContextKey=Zo4;class xO1{constructor(A){let B=this;B._currentContext=A?new Map(A):new Map,B.getValue=(Q)=>B._currentContext.get(Q),B.setValue=(Q,D)=>{let Z=new xO1(B._currentContext);return Z._currentContext.set(Q,D),Z},B.deleteValue=(Q)=>{let D=new xO1(B._currentContext);return D._currentContext.delete(Q),D}}}tR2.ROOT_CONTEXT=new xO1});
var cO1=E((FS2)=>{Object.defineProperty(FS2,"__esModule",{value:!0});FS2.nextGreaterSquare=FS2.ldexp=void 0;function te4(A,B){if(A===0||A===Number.POSITIVE_INFINITY||A===Number.NEGATIVE_INFINITY||Number.isNaN(A))return A;return A*Math.pow(2,B)}FS2.ldexp=te4;function ee4(A){return A--,A|=A>>1,A|=A>>2,A|=A>>4,A|=A>>8,A|=A>>16,A++,A}FS2.nextGreaterSquare=ee4});
var cS2=E((mS2)=>{Object.defineProperty(mS2,"__esModule",{value:!0});mS2.W3CBaggagePropagator=void 0;var hI0=VQ(),k16=s51(),wu=bI0(),gI0=fI0();class uS2{inject(A,B,Q){let D=hI0.propagation.getBaggage(A);if(!D||k16.isTracingSuppressed(A))return;let Z=gI0.getKeyPairs(D).filter((F)=>{return F.length<=wu.BAGGAGE_MAX_PER_NAME_VALUE_PAIRS}).slice(0,wu.BAGGAGE_MAX_NAME_VALUE_PAIRS),G=gI0.serializeKeyPairs(Z);if(G.length>0)Q.set(B,wu.BAGGAGE_HEADER,G)}extract(A,B,Q){let D=Q.get(B,wu.BAGGAGE_HEADER),Z=Array.isArray(D)?D.join(wu.BAGGAGE_ITEMS_SEPARATOR):D;if(!Z)return A;let G={};if(Z.length===0)return A;if(Z.split(wu.BAGGAGE_ITEMS_SEPARATOR).forEach((I)=>{let Y=gI0.parsePairKeyValue(I);if(Y){let W={value:Y.value};if(Y.metadata)W.metadata=Y.metadata;G[Y.key]=W}}),Object.entries(G).length===0)return A;return hI0.propagation.setBaggage(A,hI0.propagation.createBaggage(G))}fields(){return[wu.BAGGAGE_HEADER]}}mS2.W3CBaggagePropagator=uS2});
var cT2=E((mT2)=>{Object.defineProperty(mT2,"__esModule",{value:!0});mT2.deleteBaggage=mT2.setBaggage=mT2.getActiveBaggage=mT2.getBaggage=void 0;var $t4=p51(),qt4=c51(),WI0=qt4.createContextKey("OpenTelemetry Baggage Key");function uT2(A){return A.getValue(WI0)||void 0}mT2.getBaggage=uT2;function Nt4(){return uT2($t4.ContextAPI.getInstance().active())}mT2.getActiveBaggage=Nt4;function Lt4(A,B){return A.setValue(WI0,B)}mT2.setBaggage=Lt4;function Mt4(A){return A.deleteValue(WI0)}mT2.deleteBaggage=Mt4});
var ch2=E((mh2)=>{Object.defineProperty(mh2,"__esModule",{value:!0});mh2.getRPCMetadata=mh2.deleteRPCMetadata=mh2.setRPCMetadata=mh2.RPCType=void 0;var M76=VQ(),rI0=M76.createContextKey("OpenTelemetry SDK Context Key RPC_METADATA"),R76;(function(A){A.HTTP="http"})(R76=mh2.RPCType||(mh2.RPCType={}));function O76(A,B){return A.setValue(rI0,B)}mh2.setRPCMetadata=O76;function T76(A){return A.deleteValue(rI0)}mh2.deleteRPCMetadata=T76;function P76(A){return A.getValue(rI0)}mh2.getRPCMetadata=P76});
var co=E((z31)=>{Object.defineProperty(z31,"__esModule",{value:!0});z31.convertLegacyHttpOptions=z31.getSharedConfigurationFromEnvironment=z31.createOtlpHttpExportDelegate=void 0;var HW6=Si2();Object.defineProperty(z31,"createOtlpHttpExportDelegate",{enumerable:!0,get:function(){return HW6.createOtlpHttpExportDelegate}});var zW6=VW0();Object.defineProperty(z31,"getSharedConfigurationFromEnvironment",{enumerable:!0,get:function(){return zW6.getSharedConfigurationFromEnvironment}});var EW6=ai2();Object.defineProperty(z31,"convertLegacyHttpOptions",{enumerable:!0,get:function(){return EW6.convertLegacyHttpOptions}})});
var cu2=E((mu2)=>{Object.defineProperty(mu2,"__esModule",{value:!0});mu2.getMachineId=void 0;var QZ6=YT1(),DZ6=VQ();async function ZZ6(){try{let B=(await QZ6.execAsync('ioreg -rd1 -c "IOPlatformExpertDevice"')).stdout.split(`
`).find((D)=>D.includes("IOPlatformUUID"));if(!B)return;let Q=B.split('" = "');if(Q.length===2)return Q[1].slice(0,-1)}catch(A){DZ6.diag.debug(`error reading machine id: ${A}`)}return}mu2.getMachineId=ZZ6});
var dI0=E((oL)=>{Object.defineProperty(oL,"__esModule",{value:!0});oL.getStringListFromEnv=oL.getNumberFromEnv=oL.getStringFromEnv=oL.getBooleanFromEnv=oL.unrefTimer=oL.otperformance=oL._globalThis=oL.SDK_INFO=void 0;var h_=Vh2();Object.defineProperty(oL,"SDK_INFO",{enumerable:!0,get:function(){return h_.SDK_INFO}});Object.defineProperty(oL,"_globalThis",{enumerable:!0,get:function(){return h_._globalThis}});Object.defineProperty(oL,"otperformance",{enumerable:!0,get:function(){return h_.otperformance}});Object.defineProperty(oL,"unrefTimer",{enumerable:!0,get:function(){return h_.unrefTimer}});Object.defineProperty(oL,"getBooleanFromEnv",{enumerable:!0,get:function(){return h_.getBooleanFromEnv}});Object.defineProperty(oL,"getStringFromEnv",{enumerable:!0,get:function(){return h_.getStringFromEnv}});Object.defineProperty(oL,"getNumberFromEnv",{enumerable:!0,get:function(){return h_.getNumberFromEnv}});Object.defineProperty(oL,"getStringListFromEnv",{enumerable:!0,get:function(){return h_.getStringListFromEnv}})});
var eF0=E((eO2)=>{Object.defineProperty(eO2,"__esModule",{value:!0});eO2.ProxyTracerProvider=void 0;var oo4=tF0(),to4=oO2(),eo4=new to4.NoopTracerProvider;class tO2{getTracer(A,B,Q){var D;return(D=this.getDelegateTracer(A,B,Q))!==null&&D!==void 0?D:new oo4.ProxyTracer(this,A,B,Q)}getDelegate(){var A;return(A=this._delegate)!==null&&A!==void 0?A:eo4}setDelegate(A){this._delegate=A}getDelegateTracer(A,B,Q){var D;return(D=this._delegate)===null||D===void 0?void 0:D.getTracer(A,B,Q)}}eO2.ProxyTracerProvider=tO2});
var eL=E((gY0)=>{var S9=gY0;S9.asPromise=vY0();S9.base64=Hl2();S9.EventEmitter=El2();S9.float=Ml2();S9.inquire=fY0();S9.utf8=Tl2();S9.pool=Sl2();S9.LongBits=yl2();S9.isNode=Boolean(typeof global!=="undefined"&&global&&global.process&&global.process.versions&&global.process.versions.node);S9.global=S9.isNode&&global||typeof window!=="undefined"&&window||typeof self!=="undefined"&&self||gY0;S9.emptyArray=Object.freeze?Object.freeze([]):[];S9.emptyObject=Object.freeze?Object.freeze({}):{};S9.isInteger=Number.isInteger||function A(B){return typeof B==="number"&&isFinite(B)&&Math.floor(B)===B};S9.isString=function A(B){return typeof B==="string"||B instanceof String};S9.isObject=function A(B){return B&&typeof B==="object"};S9.isset=S9.isSet=function A(B,Q){var D=B[Q];if(D!=null&&B.hasOwnProperty(Q))return typeof D!=="object"||(Array.isArray(D)?D.length:Object.keys(D).length)>0;return!1};S9.Buffer=function(){try{var A=S9.inquire("buffer").Buffer;return A.prototype.utf8Write?A:null}catch(B){return null}}();S9._Buffer_from=null;S9._Buffer_allocUnsafe=null;S9.newBuffer=function A(B){return typeof B==="number"?S9.Buffer?S9._Buffer_allocUnsafe(B):new S9.Array(B):S9.Buffer?S9._Buffer_from(B):typeof Uint8Array==="undefined"?B:new Uint8Array(B)};S9.Array=typeof Uint8Array!=="undefined"?Uint8Array:Array;S9.Long=S9.global.dcodeIO&&S9.global.dcodeIO.Long||S9.global.Long||S9.inquire("long");S9.key2Re=/^true|false|0|1$/;S9.key32Re=/^-?(?:0|[1-9][0-9]*)$/;S9.key64Re=/^(?:[\\x00-\\xff]{8}|-?(?:0|[1-9][0-9]*))$/;S9.longToHash=function A(B){return B?S9.LongBits.from(B).toHash():S9.LongBits.zeroHash};S9.longFromHash=function A(B,Q){var D=S9.LongBits.fromHash(B);if(S9.Long)return S9.Long.fromBits(D.lo,D.hi,Q);return D.toNumber(Boolean(Q))};function kl2(A,B,Q){for(var D=Object.keys(B),Z=0;Z<D.length;++Z)if(A[D[Z]]===void 0||!Q)A[D[Z]]=B[D[Z]];return A}S9.merge=kl2;S9.lcFirst=function A(B){return B.charAt(0).toLowerCase()+B.substring(1)};function _l2(A){function B(Q,D){if(!(this instanceof B))return new B(Q,D);if(Object.defineProperty(this,"message",{get:function(){return Q}}),Error.captureStackTrace)Error.captureStackTrace(this,B);else Object.defineProperty(this,"stack",{value:new Error().stack||""});if(D)kl2(this,D)}return B.prototype=Object.create(Error.prototype,{constructor:{value:B,writable:!0,enumerable:!1,configurable:!0},name:{get:function Q(){return A},set:void 0,enumerable:!1,configurable:!0},toString:{value:function Q(){return this.name+": "+this.message},writable:!0,enumerable:!1,configurable:!0}}),B}S9.newError=_l2;S9.ProtocolError=_l2("ProtocolError");S9.oneOfGetter=function A(B){var Q={};for(var D=0;D<B.length;++D)Q[B[D]]=1;return function(){for(var Z=Object.keys(this),G=Z.length-1;G>-1;--G)if(Q[Z[G]]===1&&this[Z[G]]!==void 0&&this[Z[G]]!==null)return Z[G]}};S9.oneOfSetter=function A(B){return function(Q){for(var D=0;D<B.length;++D)if(B[D]!==Q)delete this[B[D]]}};S9.toJSONOptions={longs:String,enums:String,bytes:String,json:!0};S9._configure=function(){var A=S9.Buffer;if(!A){S9._Buffer_from=S9._Buffer_allocUnsafe=null;return}S9._Buffer_from=A.from!==Uint8Array.from&&A.from||function B(Q,D){return new A(Q,D)},S9._Buffer_allocUnsafe=A.allocUnsafe||function B(Q){return new A(Q)}}});
var ei2=E((oi2)=>{Object.defineProperty(oi2,"__esModule",{value:!0});oi2.OTLPMetricExporter=void 0;var wW6=xY0(),$W6=fu(),qW6=Ii2(),si2=co(),NW6={"User-Agent":`OTel-OTLP-Exporter-JavaScript/${qW6.VERSION}`};class ri2 extends wW6.OTLPMetricExporterBase{constructor(A){super(si2.createOtlpHttpExportDelegate(si2.convertLegacyHttpOptions(A??{},"METRICS","v1/metrics",{...NW6,"Content-Type":"application/json"}),$W6.JsonMetricsSerializer),A)}}oi2.OTLPMetricExporter=ri2});
var em2=E((om2)=>{Object.defineProperty(om2,"__esModule",{value:!0});om2.Meter=void 0;var ju=F31(),yu=VT1(),ku=f_();class rm2{_meterSharedState;constructor(A){this._meterSharedState=A}createGauge(A,B){let Q=ju.createInstrumentDescriptor(A,ku.InstrumentType.GAUGE,B),D=this._meterSharedState.registerMetricStorage(Q);return new yu.GaugeInstrument(D,Q)}createHistogram(A,B){let Q=ju.createInstrumentDescriptor(A,ku.InstrumentType.HISTOGRAM,B),D=this._meterSharedState.registerMetricStorage(Q);return new yu.HistogramInstrument(D,Q)}createCounter(A,B){let Q=ju.createInstrumentDescriptor(A,ku.InstrumentType.COUNTER,B),D=this._meterSharedState.registerMetricStorage(Q);return new yu.CounterInstrument(D,Q)}createUpDownCounter(A,B){let Q=ju.createInstrumentDescriptor(A,ku.InstrumentType.UP_DOWN_COUNTER,B),D=this._meterSharedState.registerMetricStorage(Q);return new yu.UpDownCounterInstrument(D,Q)}createObservableGauge(A,B){let Q=ju.createInstrumentDescriptor(A,ku.InstrumentType.OBSERVABLE_GAUGE,B),D=this._meterSharedState.registerAsyncMetricStorage(Q);return new yu.ObservableGaugeInstrument(Q,D,this._meterSharedState.observableRegistry)}createObservableCounter(A,B){let Q=ju.createInstrumentDescriptor(A,ku.InstrumentType.OBSERVABLE_COUNTER,B),D=this._meterSharedState.registerAsyncMetricStorage(Q);return new yu.ObservableCounterInstrument(Q,D,this._meterSharedState.observableRegistry)}createObservableUpDownCounter(A,B){let Q=ju.createInstrumentDescriptor(A,ku.InstrumentType.OBSERVABLE_UP_DOWN_COUNTER,B),D=this._meterSharedState.registerAsyncMetricStorage(Q);return new yu.ObservableUpDownCounterInstrument(Q,D,this._meterSharedState.observableRegistry)}addBatchObservableCallback(A,B){this._meterSharedState.observableRegistry.addBatchCallback(A,B)}removeBatchObservableCallback(A,B){this._meterSharedState.observableRegistry.removeBatchCallback(A,B)}}om2.Meter=rm2});
var ep2=E((op2)=>{Object.defineProperty(op2,"__esModule",{value:!0});op2.JsonMetricsSerializer=void 0;var WY6=GW0();op2.JsonMetricsSerializer={serializeRequest:(A)=>{let B=WY6.createExportMetricsServiceRequest([A],{useLongBits:!1});return new TextEncoder().encode(JSON.stringify(B))},deserializeResponse:(A)=>{return JSON.parse(new TextDecoder().decode(A))}}});
var fI0=E((hS2)=>{Object.defineProperty(hS2,"__esModule",{value:!0});hS2.parseKeyPairsIntoRecord=hS2.parsePairKeyValue=hS2.getKeyPairs=hS2.serializeKeyPairs=void 0;var R16=VQ(),Uu=bI0();function O16(A){return A.reduce((B,Q)=>{let D=`${B}${B!==""?Uu.BAGGAGE_ITEMS_SEPARATOR:""}${Q}`;return D.length>Uu.BAGGAGE_MAX_TOTAL_LENGTH?B:D},"")}hS2.serializeKeyPairs=O16;function T16(A){return A.getAllEntries().map(([B,Q])=>{let D=`${encodeURIComponent(B)}=${encodeURIComponent(Q.value)}`;if(Q.metadata!==void 0)D+=Uu.BAGGAGE_PROPERTIES_SEPARATOR+Q.metadata.toString();return D})}hS2.getKeyPairs=T16;function fS2(A){let B=A.split(Uu.BAGGAGE_PROPERTIES_SEPARATOR);if(B.length<=0)return;let Q=B.shift();if(!Q)return;let D=Q.indexOf(Uu.BAGGAGE_KEY_PAIR_SEPARATOR);if(D<=0)return;let Z=decodeURIComponent(Q.substring(0,D).trim()),G=decodeURIComponent(Q.substring(D+1).trim()),F;if(B.length>0)F=R16.baggageEntryMetadataFromString(B.join(Uu.BAGGAGE_PROPERTIES_SEPARATOR));return{key:Z,value:G,metadata:F}}hS2.parsePairKeyValue=fS2;function P16(A){if(typeof A!=="string"||A.length===0)return{};return A.split(Uu.BAGGAGE_ITEMS_SEPARATOR).map((B)=>{return fS2(B)}).filter((B)=>B!==void 0&&B.value.length>0).reduce((B,Q)=>{return B[Q.key]=Q.value,B},{})}hS2.parseKeyPairsIntoRecord=P16});
var fO1=E((gO2)=>{Object.defineProperty(gO2,"__esModule",{value:!0});gO2.wrapSpanContext=gO2.isSpanContextValid=gO2.isValidSpanId=gO2.isValidTraceId=void 0;var bO2=vO1(),fo4=bO1(),ho4=/^([0-9a-f]{32})$/i,go4=/^[0-9a-f]{16}$/i;function fO2(A){return ho4.test(A)&&A!==bO2.INVALID_TRACEID}gO2.isValidTraceId=fO2;function hO2(A){return go4.test(A)&&A!==bO2.INVALID_SPANID}gO2.isValidSpanId=hO2;function uo4(A){return fO2(A.traceId)&&hO2(A.spanId)}gO2.isSpanContextValid=uo4;function mo4(A){return new fo4.NonRecordingSpan(A)}gO2.wrapSpanContext=mo4});
var fY0=E((Rl2,bY0)=>{bY0.exports=tF6;function tF6(moduleName){try{var mod=eval("quire".replace(/^/,"re"))(moduleName);if(mod&&(mod.length||Object.keys(mod).length))return mod}catch(A){}return null}});
var f_=E((cP2)=>{Object.defineProperty(cP2,"__esModule",{value:!0});cP2.DataPointType=cP2.InstrumentType=void 0;var He4;(function(A){A.COUNTER="COUNTER",A.GAUGE="GAUGE",A.HISTOGRAM="HISTOGRAM",A.UP_DOWN_COUNTER="UP_DOWN_COUNTER",A.OBSERVABLE_COUNTER="OBSERVABLE_COUNTER",A.OBSERVABLE_GAUGE="OBSERVABLE_GAUGE",A.OBSERVABLE_UP_DOWN_COUNTER="OBSERVABLE_UP_DOWN_COUNTER"})(He4=cP2.InstrumentType||(cP2.InstrumentType={}));var ze4;(function(A){A[A.HISTOGRAM=0]="HISTOGRAM",A[A.EXPONENTIAL_HISTOGRAM=1]="EXPONENTIAL_HISTOGRAM",A[A.GAUGE=2]="GAUGE",A[A.SUM=3]="SUM"})(ze4=cP2.DataPointType||(cP2.DataPointType={}))});
var fi2=E((vi2)=>{Object.defineProperty(vi2,"__esModule",{value:!0});vi2.validateAndNormalizeHeaders=void 0;var aY6=VQ();function sY6(A){return()=>{let B={};return Object.entries(A?.()??{}).forEach(([Q,D])=>{if(typeof D!=="undefined")B[Q]=String(D);else aY6.diag.warn(`Header "${Q}" has invalid value (${D}) and will be ignored`)}),B}}vi2.validateAndNormalizeHeaders=sY6});
var fu=E((p_)=>{Object.defineProperty(p_,"__esModule",{value:!0});p_.JsonTraceSerializer=p_.JsonMetricsSerializer=p_.JsonLogsSerializer=p_.ProtobufTraceSerializer=p_.ProtobufMetricsSerializer=p_.ProtobufLogsSerializer=void 0;var HY6=Mp2();Object.defineProperty(p_,"ProtobufLogsSerializer",{enumerable:!0,get:function(){return HY6.ProtobufLogsSerializer}});var zY6=bp2();Object.defineProperty(p_,"ProtobufMetricsSerializer",{enumerable:!0,get:function(){return zY6.ProtobufMetricsSerializer}});var EY6=ip2();Object.defineProperty(p_,"ProtobufTraceSerializer",{enumerable:!0,get:function(){return EY6.ProtobufTraceSerializer}});var UY6=rp2();Object.defineProperty(p_,"JsonLogsSerializer",{enumerable:!0,get:function(){return UY6.JsonLogsSerializer}});var wY6=Ai2();Object.defineProperty(p_,"JsonMetricsSerializer",{enumerable:!0,get:function(){return wY6.JsonMetricsSerializer}});var $Y6=Zi2();Object.defineProperty(p_,"JsonTraceSerializer",{enumerable:!0,get:function(){return $Y6.JsonTraceSerializer}})});
var gT2=E((fT2)=>{Object.defineProperty(fT2,"__esModule",{value:!0});fT2.NoopTextMapPropagator=void 0;class bT2{inject(A,B){}extract(A,B){return A}fields(){return[]}}fT2.NoopTextMapPropagator=bT2});
var gg2=E((fg2)=>{Object.defineProperty(fg2,"__esModule",{value:!0});fg2.SumAggregator=fg2.SumAccumulation=void 0;var CD6=So(),KD6=f_();class Mu{startTime;monotonic;_current;reset;constructor(A,B,Q=0,D=!1){this.startTime=A,this.monotonic=B,this._current=Q,this.reset=D}record(A){if(this.monotonic&&A<0)return;this._current+=A}setStartTime(A){this.startTime=A}toPointValue(){return this._current}}fg2.SumAccumulation=Mu;class bg2{monotonic;kind=CD6.AggregatorKind.SUM;constructor(A){this.monotonic=A}createAccumulation(A){return new Mu(A,this.monotonic)}merge(A,B){let Q=A.toPointValue(),D=B.toPointValue();if(B.reset)return new Mu(B.startTime,this.monotonic,D,B.reset);return new Mu(A.startTime,this.monotonic,Q+D)}diff(A,B){let Q=A.toPointValue(),D=B.toPointValue();if(this.monotonic&&Q>D)return new Mu(B.startTime,this.monotonic,D,!0);return new Mu(B.startTime,this.monotonic,D-Q)}toMetricData(A,B,Q,D){return{descriptor:A,aggregationTemporality:B,dataPointType:KD6.DataPointType.SUM,dataPoints:Q.map(([Z,G])=>{return{attributes:Z,startTime:G.startTime,endTime:D,value:G.toPointValue()}}),isMonotonic:this.monotonic}}}fg2.SumAggregator=bg2});
var gh2=E((fh2)=>{Object.defineProperty(fh2,"__esModule",{value:!0});fh2.W3CTraceContextPropagator=fh2.parseTraceParent=fh2.TRACE_STATE_HEADER=fh2.TRACE_PARENT_HEADER=void 0;var sO1=VQ(),H76=s51(),z76=sI0();fh2.TRACE_PARENT_HEADER="traceparent";fh2.TRACE_STATE_HEADER="tracestate";var E76="00",U76="(?!ff)[\\da-f]{2}",w76="(?![0]{32})[\\da-f]{32}",$76="(?![0]{16})[\\da-f]{16}",q76="[\\da-f]{2}",N76=new RegExp(`^\\s?(${U76})-(${w76})-(${$76})-(${q76})(-.*)?\\s?$`);function vh2(A){let B=N76.exec(A);if(!B)return null;if(B[1]==="00"&&B[5])return null;return{traceId:B[2],spanId:B[3],traceFlags:parseInt(B[4],16)}}fh2.parseTraceParent=vh2;class bh2{inject(A,B,Q){let D=sO1.trace.getSpanContext(A);if(!D||H76.isTracingSuppressed(A)||!sO1.isSpanContextValid(D))return;let Z=`${E76}-${D.traceId}-${D.spanId}-0${Number(D.traceFlags||sO1.TraceFlags.NONE).toString(16)}`;if(Q.set(B,fh2.TRACE_PARENT_HEADER,Z),D.traceState)Q.set(B,fh2.TRACE_STATE_HEADER,D.traceState.serialize())}extract(A,B,Q){let D=Q.get(B,fh2.TRACE_PARENT_HEADER);if(!D)return A;let Z=Array.isArray(D)?D[0]:D;if(typeof Z!=="string")return A;let G=vh2(Z);if(!G)return A;G.isRemote=!0;let F=Q.get(B,fh2.TRACE_STATE_HEADER);if(F){let I=Array.isArray(F)?F.join(","):F;G.traceState=new z76.TraceState(typeof I==="string"?I:void 0)}return sO1.trace.setSpanContext(A,G)}fields(){return[fh2.TRACE_PARENT_HEADER,fh2.TRACE_STATE_HEADER]}}fh2.W3CTraceContextPropagator=bh2});
var hF0=E((ZO2)=>{Object.defineProperty(ZO2,"__esModule",{value:!0});ZO2.createNoopMeter=ZO2.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=ZO2.NOOP_OBSERVABLE_GAUGE_METRIC=ZO2.NOOP_OBSERVABLE_COUNTER_METRIC=ZO2.NOOP_UP_DOWN_COUNTER_METRIC=ZO2.NOOP_HISTOGRAM_METRIC=ZO2.NOOP_GAUGE_METRIC=ZO2.NOOP_COUNTER_METRIC=ZO2.NOOP_METER=ZO2.NoopObservableUpDownCounterMetric=ZO2.NoopObservableGaugeMetric=ZO2.NoopObservableCounterMetric=ZO2.NoopObservableMetric=ZO2.NoopHistogramMetric=ZO2.NoopGaugeMetric=ZO2.NoopUpDownCounterMetric=ZO2.NoopCounterMetric=ZO2.NoopMetric=ZO2.NoopMeter=void 0;class jF0{constructor(){}createGauge(A,B){return ZO2.NOOP_GAUGE_METRIC}createHistogram(A,B){return ZO2.NOOP_HISTOGRAM_METRIC}createCounter(A,B){return ZO2.NOOP_COUNTER_METRIC}createUpDownCounter(A,B){return ZO2.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(A,B){return ZO2.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(A,B){return ZO2.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(A,B){return ZO2.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(A,B){}removeBatchObservableCallback(A){}}ZO2.NoopMeter=jF0;class To{}ZO2.NoopMetric=To;class yF0 extends To{add(A,B){}}ZO2.NoopCounterMetric=yF0;class kF0 extends To{add(A,B){}}ZO2.NoopUpDownCounterMetric=kF0;class _F0 extends To{record(A,B){}}ZO2.NoopGaugeMetric=_F0;class xF0 extends To{record(A,B){}}ZO2.NoopHistogramMetric=xF0;class l51{addCallback(A){}removeCallback(A){}}ZO2.NoopObservableMetric=l51;class vF0 extends l51{}ZO2.NoopObservableCounterMetric=vF0;class bF0 extends l51{}ZO2.NoopObservableGaugeMetric=bF0;class fF0 extends l51{}ZO2.NoopObservableUpDownCounterMetric=fF0;ZO2.NOOP_METER=new jF0;ZO2.NOOP_COUNTER_METRIC=new yF0;ZO2.NOOP_GAUGE_METRIC=new _F0;ZO2.NOOP_HISTOGRAM_METRIC=new xF0;ZO2.NOOP_UP_DOWN_COUNTER_METRIC=new kF0;ZO2.NOOP_OBSERVABLE_COUNTER_METRIC=new vF0;ZO2.NOOP_OBSERVABLE_GAUGE_METRIC=new bF0;ZO2.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new fF0;function Fo4(){return ZO2.NOOP_METER}ZO2.createNoopMeter=Fo4});
var hc2=E((bc2)=>{Object.defineProperty(bc2,"__esModule",{value:!0});bc2.OTLPExporterBase=void 0;class vc2{_delegate;constructor(A){this._delegate=A}export(A,B){this._delegate.export(A,B)}forceFlush(){return this._delegate.forceFlush()}shutdown(){return this._delegate.shutdown()}}bc2.OTLPExporterBase=vc2});
var hu2=E((bu2)=>{Object.defineProperty(bu2,"__esModule",{value:!0});bu2.envDetector=void 0;var tD6=VQ(),eD6=CP(),xu2=x3();class vu2{_MAX_LENGTH=255;_COMMA_SEPARATOR=",";_LABEL_KEY_VALUE_SPLITTER="=";_ERROR_MESSAGE_INVALID_CHARS="should be a ASCII string with a length greater than 0 and not exceed "+this._MAX_LENGTH+" characters.";_ERROR_MESSAGE_INVALID_VALUE="should be a ASCII string with a length not exceed "+this._MAX_LENGTH+" characters.";detect(A){let B={},Q=xu2.getStringFromEnv("OTEL_RESOURCE_ATTRIBUTES"),D=xu2.getStringFromEnv("OTEL_SERVICE_NAME");if(Q)try{let Z=this._parseResourceAttributes(Q);Object.assign(B,Z)}catch(Z){tD6.diag.debug(`EnvDetector failed: ${Z.message}`)}if(D)B[eD6.SEMRESATTRS_SERVICE_NAME]=D;return{attributes:B}}_parseResourceAttributes(A){if(!A)return{};let B={},Q=A.split(this._COMMA_SEPARATOR,-1);for(let D of Q){let Z=D.split(this._LABEL_KEY_VALUE_SPLITTER,-1);if(Z.length!==2)continue;let[G,F]=Z;if(G=G.trim(),F=F.trim().split(/^"|"$/).join(""),!this._isValidAndNotEmpty(G))throw new Error(`Attribute key ${this._ERROR_MESSAGE_INVALID_CHARS}`);if(!this._isValid(F))throw new Error(`Attribute value ${this._ERROR_MESSAGE_INVALID_VALUE}`);B[G]=decodeURIComponent(F)}return B}_isValid(A){return A.length<=this._MAX_LENGTH&&this._isBaggageOctetString(A)}_isBaggageOctetString(A){for(let B=0;B<A.length;B++){let Q=A.charCodeAt(B);if(Q<33||Q===44||Q===59||Q===92||Q>126)return!1}return!0}_isValidAndNotEmpty(A){return A.length>0&&this._isValid(A)}}bu2.envDetector=new vu2});
var iR2=E((lR2)=>{Object.defineProperty(lR2,"__esModule",{value:!0});lR2.BaggageImpl=void 0;class Oo{constructor(A){this._entries=A?new Map(A):new Map}getEntry(A){let B=this._entries.get(A);if(!B)return;return Object.assign({},B)}getAllEntries(){return Array.from(this._entries.entries()).map(([A,B])=>[A,B])}setEntry(A,B){let Q=new Oo(this._entries);return Q._entries.set(A,B),Q}removeEntry(A){let B=new Oo(this._entries);return B._entries.delete(A),B}removeEntries(...A){let B=new Oo(this._entries);for(let Q of A)B._entries.delete(Q);return B}clear(){return new Oo}}lR2.BaggageImpl=Oo});
var ic2=E((pc2)=>{Object.defineProperty(pc2,"__esModule",{value:!0});pc2.CompressionAlgorithm=void 0;var RF6;(function(A){A.NONE="none",A.GZIP="gzip"})(RF6=pc2.CompressionAlgorithm||(pc2.CompressionAlgorithm={}))});
var ip2=E((YW0)=>{Object.defineProperty(YW0,"__esModule",{value:!0});YW0.ProtobufTraceSerializer=void 0;var ZY6=pp2();Object.defineProperty(YW0,"ProtobufTraceSerializer",{enumerable:!0,get:function(){return ZY6.ProtobufTraceSerializer}})});
var iu2=E((lu2)=>{Object.defineProperty(lu2,"__esModule",{value:!0});lu2.getMachineId=void 0;var GZ6=J1("fs"),FZ6=VQ();async function IZ6(){let A=["/etc/machine-id","/var/lib/dbus/machine-id"];for(let B of A)try{return(await GZ6.promises.readFile(B,{encoding:"utf8"})).trim()}catch(Q){FZ6.diag.debug(`error reading machine id: ${Q}`)}return}lu2.getMachineId=IZ6});
var jR2=E((PR2)=>{Object.defineProperty(PR2,"__esModule",{value:!0});PR2.isCompatible=PR2._makeCompatibilityCheck=void 0;var vr4=RF0(),OR2=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function TR2(A){let B=new Set([A]),Q=new Set,D=A.match(OR2);if(!D)return()=>!1;let Z={major:+D[1],minor:+D[2],patch:+D[3],prerelease:D[4]};if(Z.prerelease!=null)return function I(Y){return Y===A};function G(I){return Q.add(I),!1}function F(I){return B.add(I),!0}return function I(Y){if(B.has(Y))return!0;if(Q.has(Y))return!1;let W=Y.match(OR2);if(!W)return G(Y);let J={major:+W[1],minor:+W[2],patch:+W[3],prerelease:W[4]};if(J.prerelease!=null)return G(Y);if(Z.major!==J.major)return G(Y);if(Z.major===0){if(Z.minor===J.minor&&Z.patch<=J.patch)return F(Y);return G(Y)}if(Z.minor<=J.minor)return F(Y);return G(Y)}}PR2._makeCompatibilityCheck=TR2;PR2.isCompatible=TR2(vr4.VERSION)});
var jY0=E((ac2)=>{Object.defineProperty(ac2,"__esModule",{value:!0});ac2.createBoundedQueueExportPromiseHandler=void 0;class nc2{_concurrencyLimit;_sendingPromises=[];constructor(A){this._concurrencyLimit=A}pushPromise(A){if(this.hasReachedLimit())throw new Error("Concurrency Limit reached");this._sendingPromises.push(A);let B=()=>{let Q=this._sendingPromises.indexOf(A);this._sendingPromises.splice(Q,1)};A.then(B,B)}hasReachedLimit(){return this._sendingPromises.length>=this._concurrencyLimit}async awaitAll(){await Promise.all(this._sendingPromises)}}function OF6(A){return new nc2(A.concurrencyLimit)}ac2.createBoundedQueueExportPromiseHandler=OF6});
var kO1=E((fR2)=>{Object.defineProperty(fR2,"__esModule",{value:!0});fR2.DiagLogLevel=void 0;var ir4;(function(A){A[A.NONE=0]="NONE",A[A.ERROR=30]="ERROR",A[A.WARN=50]="WARN",A[A.INFO=60]="INFO",A[A.DEBUG=70]="DEBUG",A[A.VERBOSE=80]="VERBOSE",A[A.ALL=9999]="ALL"})(ir4=fR2.DiagLogLevel||(fR2.DiagLogLevel={}))});
var kS2=E((jS2)=>{Object.defineProperty(jS2,"__esModule",{value:!0});jS2.ExponentialHistogramAggregator=jS2.ExponentialHistogramAccumulation=void 0;var I16=So(),a51=f_(),Y16=VQ(),TS2=DS2(),PS2=OS2(),W16=cO1();class ko{low;high;static combine(A,B){return new ko(Math.min(A.low,B.low),Math.max(A.high,B.high))}constructor(A,B){this.low=A,this.high=B}}var J16=20,X16=160,xI0=2;class pO1{startTime;_maxSize;_recordMinMax;_sum;_count;_zeroCount;_min;_max;_positive;_negative;_mapping;constructor(A=A,B=X16,Q=!0,D=0,Z=0,G=0,F=Number.POSITIVE_INFINITY,I=Number.NEGATIVE_INFINITY,Y=new TS2.Buckets,W=new TS2.Buckets,J=PS2.getMapping(J16)){if(this.startTime=A,this._maxSize=B,this._recordMinMax=Q,this._sum=D,this._count=Z,this._zeroCount=G,this._min=F,this._max=I,this._positive=Y,this._negative=W,this._mapping=J,this._maxSize<xI0)Y16.diag.warn(`Exponential Histogram Max Size set to ${this._maxSize},                 changing to the minimum size of: ${xI0}`),this._maxSize=xI0}record(A){this.updateByIncrement(A,1)}setStartTime(A){this.startTime=A}toPointValue(){return{hasMinMax:this._recordMinMax,min:this.min,max:this.max,sum:this.sum,positive:{offset:this.positive.offset,bucketCounts:this.positive.counts()},negative:{offset:this.negative.offset,bucketCounts:this.negative.counts()},count:this.count,scale:this.scale,zeroCount:this.zeroCount}}get sum(){return this._sum}get min(){return this._min}get max(){return this._max}get count(){return this._count}get zeroCount(){return this._zeroCount}get scale(){if(this._count===this._zeroCount)return 0;return this._mapping.scale}get positive(){return this._positive}get negative(){return this._negative}updateByIncrement(A,B){if(Number.isNaN(A))return;if(A>this._max)this._max=A;if(A<this._min)this._min=A;if(this._count+=B,A===0){this._zeroCount+=B;return}if(this._sum+=A*B,A>0)this._updateBuckets(this._positive,A,B);else this._updateBuckets(this._negative,-A,B)}merge(A){if(this._count===0)this._min=A.min,this._max=A.max;else if(A.count!==0){if(A.min<this.min)this._min=A.min;if(A.max>this.max)this._max=A.max}this.startTime=A.startTime,this._sum+=A.sum,this._count+=A.count,this._zeroCount+=A.zeroCount;let B=this._minScale(A);this._downscale(this.scale-B),this._mergeBuckets(this.positive,A,A.positive,B),this._mergeBuckets(this.negative,A,A.negative,B)}diff(A){this._min=1/0,this._max=-1/0,this._sum-=A.sum,this._count-=A.count,this._zeroCount-=A.zeroCount;let B=this._minScale(A);this._downscale(this.scale-B),this._diffBuckets(this.positive,A,A.positive,B),this._diffBuckets(this.negative,A,A.negative,B)}clone(){return new pO1(this.startTime,this._maxSize,this._recordMinMax,this._sum,this._count,this._zeroCount,this._min,this._max,this.positive.clone(),this.negative.clone(),this._mapping)}_updateBuckets(A,B,Q){let D=this._mapping.mapToIndex(B),Z=!1,G=0,F=0;if(A.length===0)A.indexStart=D,A.indexEnd=A.indexStart,A.indexBase=A.indexStart;else if(D<A.indexStart&&A.indexEnd-D>=this._maxSize)Z=!0,F=D,G=A.indexEnd;else if(D>A.indexEnd&&D-A.indexStart>=this._maxSize)Z=!0,F=A.indexStart,G=D;if(Z){let I=this._changeScale(G,F);this._downscale(I),D=this._mapping.mapToIndex(B)}this._incrementIndexBy(A,D,Q)}_incrementIndexBy(A,B,Q){if(Q===0)return;if(A.length===0)A.indexStart=A.indexEnd=A.indexBase=B;if(B<A.indexStart){let Z=A.indexEnd-B;if(Z>=A.backing.length)this._grow(A,Z+1);A.indexStart=B}else if(B>A.indexEnd){let Z=B-A.indexStart;if(Z>=A.backing.length)this._grow(A,Z+1);A.indexEnd=B}let D=B-A.indexBase;if(D<0)D+=A.backing.length;A.incrementBucket(D,Q)}_grow(A,B){let Q=A.backing.length,D=A.indexBase-A.indexStart,Z=Q-D,G=W16.nextGreaterSquare(B);if(G>this._maxSize)G=this._maxSize;let F=G-D;A.backing.growTo(G,Z,F)}_changeScale(A,B){let Q=0;while(A-B>=this._maxSize)A>>=1,B>>=1,Q++;return Q}_downscale(A){if(A===0)return;if(A<0)throw new Error(`impossible change of scale: ${this.scale}`);let B=this._mapping.scale-A;this._positive.downscale(A),this._negative.downscale(A),this._mapping=PS2.getMapping(B)}_minScale(A){let B=Math.min(this.scale,A.scale),Q=ko.combine(this._highLowAtScale(this.positive,this.scale,B),this._highLowAtScale(A.positive,A.scale,B)),D=ko.combine(this._highLowAtScale(this.negative,this.scale,B),this._highLowAtScale(A.negative,A.scale,B));return Math.min(B-this._changeScale(Q.high,Q.low),B-this._changeScale(D.high,D.low))}_highLowAtScale(A,B,Q){if(A.length===0)return new ko(0,-1);let D=B-Q;return new ko(A.indexStart>>D,A.indexEnd>>D)}_mergeBuckets(A,B,Q,D){let Z=Q.offset,G=B.scale-D;for(let F=0;F<Q.length;F++)this._incrementIndexBy(A,Z+F>>G,Q.at(F))}_diffBuckets(A,B,Q,D){let Z=Q.offset,G=B.scale-D;for(let F=0;F<Q.length;F++){let Y=(Z+F>>G)-A.indexBase;if(Y<0)Y+=A.backing.length;A.decrementBucket(Y,Q.at(F))}A.trim()}}jS2.ExponentialHistogramAccumulation=pO1;class SS2{_maxSize;_recordMinMax;kind=I16.AggregatorKind.EXPONENTIAL_HISTOGRAM;constructor(A,B){this._maxSize=A,this._recordMinMax=B}createAccumulation(A){return new pO1(A,this._maxSize,this._recordMinMax)}merge(A,B){let Q=B.clone();return Q.merge(A),Q}diff(A,B){let Q=B.clone();return Q.diff(A),Q}toMetricData(A,B,Q,D){return{descriptor:A,aggregationTemporality:B,dataPointType:a51.DataPointType.EXPONENTIAL_HISTOGRAM,dataPoints:Q.map(([Z,G])=>{let F=G.toPointValue(),I=A.type===a51.InstrumentType.GAUGE||A.type===a51.InstrumentType.UP_DOWN_COUNTER||A.type===a51.InstrumentType.OBSERVABLE_GAUGE||A.type===a51.InstrumentType.OBSERVABLE_UP_DOWN_COUNTER;return{attributes:Z,startTime:G.startTime,endTime:D,value:{min:F.hasMinMax?F.min:void 0,max:F.hasMinMax?F.max:void 0,sum:!I?F.sum:void 0,positive:{offset:F.positive.offset,bucketCounts:F.positive.bucketCounts},negative:{offset:F.negative.offset,bucketCounts:F.negative.bucketCounts},count:F.count,scale:F.scale,zeroCount:F.zeroCount}}})}}}jS2.ExponentialHistogramAggregator=SS2});
var kT2=E((jT2)=>{Object.defineProperty(jT2,"__esModule",{value:!0});jT2.MetricsAPI=void 0;var Ut4=PT2(),FI0=zu(),ST2=Eu(),II0="metrics";class YI0{constructor(){}static getInstance(){if(!this._instance)this._instance=new YI0;return this._instance}setGlobalMeterProvider(A){return FI0.registerGlobal(II0,A,ST2.DiagAPI.instance())}getMeterProvider(){return FI0.getGlobal(II0)||Ut4.NOOP_METER_PROVIDER}getMeter(A,B,Q){return this.getMeterProvider().getMeter(A,B,Q)}disable(){FI0.unregisterGlobal(II0,ST2.DiagAPI.instance())}}jT2.MetricsAPI=YI0});
var km2=E((u_)=>{Object.defineProperty(u_,"__esModule",{value:!0});u_.noopDetector=u_.serviceInstanceIdDetector=u_.processDetector=u_.osDetector=u_.hostDetector=u_.envDetector=void 0;var xZ6=hu2();Object.defineProperty(u_,"envDetector",{enumerable:!0,get:function(){return xZ6.envDetector}});var JT1=Pm2();Object.defineProperty(u_,"hostDetector",{enumerable:!0,get:function(){return JT1.hostDetector}});Object.defineProperty(u_,"osDetector",{enumerable:!0,get:function(){return JT1.osDetector}});Object.defineProperty(u_,"processDetector",{enumerable:!0,get:function(){return JT1.processDetector}});Object.defineProperty(u_,"serviceInstanceIdDetector",{enumerable:!0,get:function(){return JT1.serviceInstanceIdDetector}});var vZ6=ym2();Object.defineProperty(u_,"noopDetector",{enumerable:!0,get:function(){return vZ6.noopDetector}})});
var lO1=E((WS2)=>{Object.defineProperty(WS2,"__esModule",{value:!0});WS2.MappingError=void 0;class YS2 extends Error{}WS2.MappingError=YS2});
var lg2=E((ew)=>{Object.defineProperty(ew,"__esModule",{value:!0});ew.SumAggregator=ew.SumAccumulation=ew.LastValueAggregator=ew.LastValueAccumulation=ew.ExponentialHistogramAggregator=ew.ExponentialHistogramAccumulation=ew.HistogramAggregator=ew.HistogramAccumulation=ew.DropAggregator=void 0;var zD6=rP2();Object.defineProperty(ew,"DropAggregator",{enumerable:!0,get:function(){return zD6.DropAggregator}});var ug2=AS2();Object.defineProperty(ew,"HistogramAccumulation",{enumerable:!0,get:function(){return ug2.HistogramAccumulation}});Object.defineProperty(ew,"HistogramAggregator",{enumerable:!0,get:function(){return ug2.HistogramAggregator}});var mg2=kS2();Object.defineProperty(ew,"ExponentialHistogramAccumulation",{enumerable:!0,get:function(){return mg2.ExponentialHistogramAccumulation}});Object.defineProperty(ew,"ExponentialHistogramAggregator",{enumerable:!0,get:function(){return mg2.ExponentialHistogramAggregator}});var dg2=vg2();Object.defineProperty(ew,"LastValueAccumulation",{enumerable:!0,get:function(){return dg2.LastValueAccumulation}});Object.defineProperty(ew,"LastValueAggregator",{enumerable:!0,get:function(){return dg2.LastValueAggregator}});var cg2=gg2();Object.defineProperty(ew,"SumAccumulation",{enumerable:!0,get:function(){return cg2.SumAccumulation}});Object.defineProperty(ew,"SumAggregator",{enumerable:!0,get:function(){return cg2.SumAggregator}})});
var li2=E((di2)=>{Object.defineProperty(di2,"__esModule",{value:!0});di2.getHttpConfigurationFromEnvironment=void 0;var ST1=x3(),CW0=VQ(),QW6=VW0(),DW6=J31();function ZW6(A){let B=process.env[`OTEL_EXPORTER_OTLP_${A}_HEADERS`]?.trim(),Q=process.env.OTEL_EXPORTER_OTLP_HEADERS?.trim(),D=ST1.parseKeyPairsIntoRecord(B),Z=ST1.parseKeyPairsIntoRecord(Q);if(Object.keys(D).length===0&&Object.keys(Z).length===0)return;return Object.assign({},ST1.parseKeyPairsIntoRecord(Q),ST1.parseKeyPairsIntoRecord(B))}function GW6(A){try{return new URL(A).toString()}catch{CW0.diag.warn(`Configuration: Could not parse environment-provided export URL: '${A}', falling back to undefined`);return}}function FW6(A,B){try{new URL(A)}catch{CW0.diag.warn(`Configuration: Could not parse environment-provided export URL: '${A}', falling back to undefined`);return}if(!A.endsWith("/"))A=A+"/";A+=B;try{new URL(A)}catch{CW0.diag.warn(`Configuration: Provided URL appended with '${B}' is not a valid URL, using 'undefined' instead of '${A}'`);return}return A}function IW6(A){let B=process.env.OTEL_EXPORTER_OTLP_ENDPOINT?.trim();if(B==null||B==="")return;return FW6(B,A)}function YW6(A){let B=process.env[`OTEL_EXPORTER_OTLP_${A}_ENDPOINT`]?.trim();if(B==null||B==="")return;return GW6(B)}function WW6(A,B){return{...QW6.getSharedConfigurationFromEnvironment(A),url:YW6(A)??IW6(B),headers:DW6.wrapStaticHeadersInFunction(ZW6(A))}}di2.getHttpConfigurationFromEnvironment=WW6});
var mI0=E((Nj2)=>{Object.defineProperty(Nj2,"__esModule",{value:!0});Nj2.createConstMap=void 0;function t16(A){let B={},Q=A.length;for(let D=0;D<Q;D++){let Z=A[D];if(Z)B[String(Z).toUpperCase().replace(/[-.]/g,"_")]=Z}return B}Nj2.createConstMap=t16});
var mO1=E((mP2)=>{Object.defineProperty(mP2,"__esModule",{value:!0});mP2.AggregationTemporality=void 0;var Ke4;(function(A){A[A.DELTA=0]="DELTA",A[A.CUMULATIVE=1]="CUMULATIVE"})(Ke4=mP2.AggregationTemporality||(mP2.AggregationTemporality={}))});
var m_=E((bV)=>{Object.defineProperty(bV,"__esModule",{value:!0});bV.TimeoutError=bV.createDenyListAttributesProcessor=bV.createAllowListAttributesProcessor=bV.AggregationType=bV.MeterProvider=bV.ConsoleMetricExporter=bV.InMemoryMetricExporter=bV.PeriodicExportingMetricReader=bV.MetricReader=bV.InstrumentType=bV.DataPointType=bV.AggregationTemporality=void 0;var WF6=mO1();Object.defineProperty(bV,"AggregationTemporality",{enumerable:!0,get:function(){return WF6.AggregationTemporality}});var kc2=f_();Object.defineProperty(bV,"DataPointType",{enumerable:!0,get:function(){return kc2.DataPointType}});Object.defineProperty(bV,"InstrumentType",{enumerable:!0,get:function(){return kc2.InstrumentType}});var JF6=DY0();Object.defineProperty(bV,"MetricReader",{enumerable:!0,get:function(){return JF6.MetricReader}});var XF6=Vu2();Object.defineProperty(bV,"PeriodicExportingMetricReader",{enumerable:!0,get:function(){return XF6.PeriodicExportingMetricReader}});var VF6=Eu2();Object.defineProperty(bV,"InMemoryMetricExporter",{enumerable:!0,get:function(){return VF6.InMemoryMetricExporter}});var CF6=qu2();Object.defineProperty(bV,"ConsoleMetricExporter",{enumerable:!0,get:function(){return CF6.ConsoleMetricExporter}});var KF6=yc2();Object.defineProperty(bV,"MeterProvider",{enumerable:!0,get:function(){return KF6.MeterProvider}});var HF6=Q31();Object.defineProperty(bV,"AggregationType",{enumerable:!0,get:function(){return HF6.AggregationType}});var _c2=KT1();Object.defineProperty(bV,"createAllowListAttributesProcessor",{enumerable:!0,get:function(){return _c2.createAllowListAttributesProcessor}});Object.defineProperty(bV,"createDenyListAttributesProcessor",{enumerable:!0,get:function(){return _c2.createDenyListAttributesProcessor}});var zF6=tw();Object.defineProperty(bV,"TimeoutError",{enumerable:!0,get:function(){return zF6.TimeoutError}})});
var md2=E((gd2)=>{Object.defineProperty(gd2,"__esModule",{value:!0});gd2.ObservableRegistry=void 0;var OG6=VQ(),bd2=VT1(),fd2=vd2(),W31=tw();class hd2{_callbacks=[];_batchCallbacks=[];addCallback(A,B){if(this._findCallback(A,B)>=0)return;this._callbacks.push({callback:A,instrument:B})}removeCallback(A,B){let Q=this._findCallback(A,B);if(Q<0)return;this._callbacks.splice(Q,1)}addBatchCallback(A,B){let Q=new Set(B.filter(bd2.isObservableInstrument));if(Q.size===0){OG6.diag.error("BatchObservableCallback is not associated with valid instruments",B);return}if(this._findBatchCallback(A,Q)>=0)return;this._batchCallbacks.push({callback:A,instruments:Q})}removeBatchCallback(A,B){let Q=new Set(B.filter(bd2.isObservableInstrument)),D=this._findBatchCallback(A,Q);if(D<0)return;this._batchCallbacks.splice(D,1)}async observe(A,B){let Q=this._observeCallbacks(A,B),D=this._observeBatchCallbacks(A,B);return(await W31.PromiseAllSettled([...Q,...D])).filter(W31.isPromiseAllSettledRejectionResult).map((F)=>F.reason)}_observeCallbacks(A,B){return this._callbacks.map(async({callback:Q,instrument:D})=>{let Z=new fd2.ObservableResultImpl(D._descriptor.name,D._descriptor.valueType),G=Promise.resolve(Q(Z));if(B!=null)G=W31.callWithTimeout(G,B);await G,D._metricStorages.forEach((F)=>{F.record(Z._buffer,A)})})}_observeBatchCallbacks(A,B){return this._batchCallbacks.map(async({callback:Q,instruments:D})=>{let Z=new fd2.BatchObservableResultImpl,G=Promise.resolve(Q(Z));if(B!=null)G=W31.callWithTimeout(G,B);await G,D.forEach((F)=>{let I=Z._buffer.get(F);if(I==null)return;F._metricStorages.forEach((Y)=>{Y.record(I,A)})})})}_findCallback(A,B){return this._callbacks.findIndex((Q)=>{return Q.callback===A&&Q.instrument===B})}_findBatchCallback(A,B){return this._batchCallbacks.findIndex((Q)=>{return Q.callback===A&&W31.setEquals(Q.instruments,B)})}}gd2.ObservableRegistry=hd2});
var mi2=E((gi2)=>{Object.defineProperty(gi2,"__esModule",{value:!0});gi2.getHttpConfigurationDefaults=gi2.mergeOtlpHttpConfigurationWithDefaults=void 0;var hi2=J31(),rY6=fi2();function oY6(A,B,Q){let D={...Q()},Z={};return()=>{if(B!=null)Object.assign(Z,B());if(A!=null)Object.assign(Z,A());return Object.assign(Z,D)}}function tY6(A){if(A==null)return;try{return new URL(A),A}catch(B){throw new Error(`Configuration: Could not parse user-provided export URL: '${A}'`)}}function eY6(A,B,Q){return{...hi2.mergeOtlpSharedConfigurationWithDefaults(A,B,Q),headers:oY6(rY6.validateAndNormalizeHeaders(A.headers),B.headers,Q.headers),url:tY6(A.url)??B.url??Q.url,agentOptions:A.agentOptions??B.agentOptions??Q.agentOptions}}gi2.mergeOtlpHttpConfigurationWithDefaults=eY6;function AW6(A,B){return{...hi2.getSharedConfigurationDefaults(),headers:()=>A,url:"http://localhost:4318/"+B,agentOptions:{keepAlive:!0}}}gi2.getHttpConfigurationDefaults=AW6});
var nS2=E((pS2)=>{Object.defineProperty(pS2,"__esModule",{value:!0});pS2.AnchoredClock=void 0;class lS2{_monotonicClock;_epochMillis;_performanceMillis;constructor(A,B){this._monotonicClock=B,this._epochMillis=A.now(),this._performanceMillis=B.now()}now(){let A=this._monotonicClock.now()-this._performanceMillis;return this._epochMillis+A}}pS2.AnchoredClock=lS2});
var oF0=E((cO2)=>{Object.defineProperty(cO2,"__esModule",{value:!0});cO2.NoopTracer=void 0;var po4=p51(),mO2=aF0(),sF0=bO1(),io4=fO1(),rF0=po4.ContextAPI.getInstance();class dO2{startSpan(A,B,Q=rF0.active()){if(Boolean(B===null||B===void 0?void 0:B.root))return new sF0.NonRecordingSpan;let Z=Q&&mO2.getSpanContext(Q);if(no4(Z)&&io4.isSpanContextValid(Z))return new sF0.NonRecordingSpan(Z);else return new sF0.NonRecordingSpan}startActiveSpan(A,B,Q,D){let Z,G,F;if(arguments.length<2)return;else if(arguments.length===2)F=B;else if(arguments.length===3)Z=B,F=Q;else Z=B,G=Q,F=D;let I=G!==null&&G!==void 0?G:rF0.active(),Y=this.startSpan(A,Z,I),W=mO2.setSpan(I,Y);return rF0.with(W,F,void 0,Y)}}cO2.NoopTracer=dO2;function no4(A){return typeof A==="object"&&typeof A.spanId==="string"&&typeof A.traceId==="string"&&typeof A.traceFlags==="number"}});
var oO2=E((sO2)=>{Object.defineProperty(sO2,"__esModule",{value:!0});sO2.NoopTracerProvider=void 0;var ro4=oF0();class aO2{getTracer(A,B,Q){return new ro4.NoopTracer}}sO2.NoopTracerProvider=aO2});
var oT2=E((sT2)=>{Object.defineProperty(sT2,"__esModule",{value:!0});sT2.propagation=void 0;var yt4=aT2();sT2.propagation=yt4.PropagationAPI.getInstance()});
var oY0=E((Bp2)=>{var fV=Bp2;fV.build="minimal";fV.Writer=NT1();fV.BufferWriter=ul2();fV.Reader=MT1();fV.BufferReader=sl2();fV.util=eL();fV.rpc=sY0();fV.roots=rY0();fV.configure=Ap2;function Ap2(){fV.util._configure(),fV.Writer._configure(fV.BufferWriter),fV.Reader._configure(fV.BufferReader)}Ap2()});
var of2=E((Nu)=>{var O66=Nu&&Nu.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),T66=Nu&&Nu.__exportStar||function(A,B){for(var Q in A)if(Q!=="default"&&!Object.prototype.hasOwnProperty.call(B,Q))O66(B,A,Q)};Object.defineProperty(Nu,"__esModule",{value:!0});T66(rf2(),Nu)});
var og2=E((pg2)=>{Object.defineProperty(pg2,"__esModule",{value:!0});pg2.DEFAULT_AGGREGATION=pg2.EXPONENTIAL_HISTOGRAM_AGGREGATION=pg2.HISTOGRAM_AGGREGATION=pg2.LAST_VALUE_AGGREGATION=pg2.SUM_AGGREGATION=pg2.DROP_AGGREGATION=pg2.DefaultAggregation=pg2.ExponentialHistogramAggregation=pg2.ExplicitBucketHistogramAggregation=pg2.HistogramAggregation=pg2.LastValueAggregation=pg2.SumAggregation=pg2.DropAggregation=void 0;var UD6=VQ(),Ru=lg2(),tL=f_();class DT1{static DEFAULT_INSTANCE=new Ru.DropAggregator;createAggregator(A){return DT1.DEFAULT_INSTANCE}}pg2.DropAggregation=DT1;class B31{static MONOTONIC_INSTANCE=new Ru.SumAggregator(!0);static NON_MONOTONIC_INSTANCE=new Ru.SumAggregator(!1);createAggregator(A){switch(A.type){case tL.InstrumentType.COUNTER:case tL.InstrumentType.OBSERVABLE_COUNTER:case tL.InstrumentType.HISTOGRAM:return B31.MONOTONIC_INSTANCE;default:return B31.NON_MONOTONIC_INSTANCE}}}pg2.SumAggregation=B31;class ZT1{static DEFAULT_INSTANCE=new Ru.LastValueAggregator;createAggregator(A){return ZT1.DEFAULT_INSTANCE}}pg2.LastValueAggregation=ZT1;class GT1{static DEFAULT_INSTANCE=new Ru.HistogramAggregator([0,5,10,25,50,75,100,250,500,750,1000,2500,5000,7500,1e4],!0);createAggregator(A){return GT1.DEFAULT_INSTANCE}}pg2.HistogramAggregation=GT1;class eI0{_recordMinMax;_boundaries;constructor(A,B=!0){if(this._recordMinMax=B,A==null)throw new Error("ExplicitBucketHistogramAggregation should be created with explicit boundaries, if a single bucket histogram is required, please pass an empty array");A=A.concat(),A=A.sort((Z,G)=>Z-G);let Q=A.lastIndexOf(-1/0),D=A.indexOf(1/0);if(D===-1)D=void 0;this._boundaries=A.slice(Q+1,D)}createAggregator(A){return new Ru.HistogramAggregator(this._boundaries,this._recordMinMax)}}pg2.ExplicitBucketHistogramAggregation=eI0;class AY0{_maxSize;_recordMinMax;constructor(A=160,B=!0){this._maxSize=A,this._recordMinMax=B}createAggregator(A){return new Ru.ExponentialHistogramAggregator(this._maxSize,this._recordMinMax)}}pg2.ExponentialHistogramAggregation=AY0;class BY0{_resolve(A){switch(A.type){case tL.InstrumentType.COUNTER:case tL.InstrumentType.UP_DOWN_COUNTER:case tL.InstrumentType.OBSERVABLE_COUNTER:case tL.InstrumentType.OBSERVABLE_UP_DOWN_COUNTER:return pg2.SUM_AGGREGATION;case tL.InstrumentType.GAUGE:case tL.InstrumentType.OBSERVABLE_GAUGE:return pg2.LAST_VALUE_AGGREGATION;case tL.InstrumentType.HISTOGRAM:{if(A.advice.explicitBucketBoundaries)return new eI0(A.advice.explicitBucketBoundaries);return pg2.HISTOGRAM_AGGREGATION}}return UD6.diag.warn(`Unable to recognize instrument type: ${A.type}`),pg2.DROP_AGGREGATION}createAggregator(A){return this._resolve(A).createAggregator(A)}}pg2.DefaultAggregation=BY0;pg2.DROP_AGGREGATION=new DT1;pg2.SUM_AGGREGATION=new B31;pg2.LAST_VALUE_AGGREGATION=new ZT1;pg2.HISTOGRAM_AGGREGATION=new GT1;pg2.EXPONENTIAL_HISTOGRAM_AGGREGATION=new AY0;pg2.DEFAULT_AGGREGATION=new BY0});
var ol2=E((Sh5,rl2)=>{rl2.exports=C31;var aY0=eL();(C31.prototype=Object.create(aY0.EventEmitter.prototype)).constructor=C31;function C31(A,B,Q){if(typeof A!=="function")throw TypeError("rpcImpl must be a function");aY0.EventEmitter.call(this),this.rpcImpl=A,this.requestDelimited=Boolean(B),this.responseDelimited=Boolean(Q)}C31.prototype.rpcCall=function A(B,Q,D,Z,G){if(!Z)throw TypeError("request must be specified");var F=this;if(!G)return aY0.asPromise(A,F,B,Q,D,Z);if(!F.rpcImpl){setTimeout(function(){G(Error("already ended"))},0);return}try{return F.rpcImpl(B,Q[F.requestDelimited?"encodeDelimited":"encode"](Z).finish(),function I(Y,W){if(Y)return F.emit("error",Y,B),G(Y);if(W===null){F.end(!0);return}if(!(W instanceof D))try{W=D[F.responseDelimited?"decodeDelimited":"decode"](W)}catch(J){return F.emit("error",J,B),G(J)}return F.emit("data",W,B),G(null,W)})}catch(I){F.emit("error",I,B),setTimeout(function(){G(I)},0);return}};C31.prototype.end=function A(B){if(this.rpcImpl){if(!B)this.rpcImpl(null,null,null);this.rpcImpl=null,this.emit("end").off()}return this}});
var p51=E((LO2)=>{Object.defineProperty(LO2,"__esModule",{value:!0});LO2.ContextAPI=void 0;var $o4=qO2(),mF0=zu(),NO2=Eu(),dF0="context",qo4=new $o4.NoopContextManager;class cF0{constructor(){}static getInstance(){if(!this._instance)this._instance=new cF0;return this._instance}setGlobalContextManager(A){return mF0.registerGlobal(dF0,A,NO2.DiagAPI.instance())}active(){return this._getContextManager().active()}with(A,B,Q,...D){return this._getContextManager().with(A,B,Q,...D)}bind(A,B){return this._getContextManager().bind(A,B)}_getContextManager(){return mF0.getGlobal(dF0)||qo4}disable(){this._getContextManager().disable(),mF0.unregisterGlobal(dF0,NO2.DiagAPI.instance())}}LO2.ContextAPI=cF0});
var pF0=E((RO2)=>{Object.defineProperty(RO2,"__esModule",{value:!0});RO2.TraceFlags=void 0;var No4;(function(A){A[A.NONE=0]="NONE",A[A.SAMPLED=1]="SAMPLED"})(No4=RO2.TraceFlags||(RO2.TraceFlags={}))});
var pd2=E((cd2)=>{Object.defineProperty(cd2,"__esModule",{value:!0});cd2.SyncMetricStorage=void 0;var TG6=wY0(),PG6=NY0(),SG6=LY0();class dd2 extends TG6.MetricStorage{_attributesProcessor;_aggregationCardinalityLimit;_deltaMetricStorage;_temporalMetricStorage;constructor(A,B,Q,D,Z){super(A);this._attributesProcessor=Q,this._aggregationCardinalityLimit=Z,this._deltaMetricStorage=new PG6.DeltaMetricProcessor(B,this._aggregationCardinalityLimit),this._temporalMetricStorage=new SG6.TemporalMetricProcessor(B,D)}record(A,B,Q,D){B=this._attributesProcessor.process(B,Q),this._deltaMetricStorage.record(A,B,Q,D)}collect(A,B){let Q=this._deltaMetricStorage.collect();return this._temporalMetricStorage.buildMetrics(A,this._instrumentDescriptor,Q,B)}}cd2.SyncMetricStorage=dd2});
var pp2=E((cp2)=>{Object.defineProperty(cp2,"__esModule",{value:!0});cp2.ProtobufTraceSerializer=void 0;var dp2=RT1(),BY6=IW0(),QY6=dp2.opentelemetry.proto.collector.trace.v1.ExportTraceServiceResponse,DY6=dp2.opentelemetry.proto.collector.trace.v1.ExportTraceServiceRequest;cp2.ProtobufTraceSerializer={serializeRequest:(A)=>{let B=BY6.createExportTraceServiceRequest(A);return DY6.encode(B).finish()},deserializeResponse:(A)=>{return QY6.decode(A)}}});
var qO2=E((wO2)=>{Object.defineProperty(wO2,"__esModule",{value:!0});wO2.NoopContextManager=void 0;var wo4=c51();class UO2{active(){return wo4.ROOT_CONTEXT}with(A,B,Q,...D){return B.call(Q,...D)}bind(A,B){return B}enable(){return this}disable(){return this}}wO2.NoopContextManager=UO2});
var qR2=E((wR2)=>{Object.defineProperty(wR2,"__esModule",{value:!0});wR2._globalThis=void 0;wR2._globalThis=typeof globalThis==="object"?globalThis:global});
var qS2=E((wS2)=>{Object.defineProperty(wS2,"__esModule",{value:!0});wS2.LogarithmMapping=void 0;var yo=_I0(),zS2=cO1(),ES2=lO1();class US2{_scale;_scaleFactor;_inverseFactor;constructor(A){this._scale=A,this._scaleFactor=zS2.ldexp(Math.LOG2E,A),this._inverseFactor=zS2.ldexp(Math.LN2,-A)}mapToIndex(A){if(A<=yo.MIN_VALUE)return this._minNormalLowerBoundaryIndex()-1;if(yo.getSignificand(A)===0)return(yo.getNormalBase2(A)<<this._scale)-1;let B=Math.floor(Math.log(A)*this._scaleFactor),Q=this._maxNormalLowerBoundaryIndex();if(B>=Q)return Q;return B}lowerBoundary(A){let B=this._maxNormalLowerBoundaryIndex();if(A>=B){if(A===B)return 2*Math.exp((A-(1<<this._scale))/this._scaleFactor);throw new ES2.MappingError(`overflow: ${A} is > maximum lower boundary: ${B}`)}let Q=this._minNormalLowerBoundaryIndex();if(A<=Q){if(A===Q)return yo.MIN_VALUE;else if(A===Q-1)return Math.exp((A+(1<<this._scale))/this._scaleFactor)/2;throw new ES2.MappingError(`overflow: ${A} is < minimum lower boundary: ${Q}`)}return Math.exp(A*this._inverseFactor)}get scale(){return this._scale}_minNormalLowerBoundaryIndex(){return yo.MIN_NORMAL_EXPONENT<<this._scale}_maxNormalLowerBoundaryIndex(){return(yo.MAX_NORMAL_EXPONENT+1<<this._scale)-1}}wS2.LogarithmMapping=US2});
var qc2=E((wc2)=>{Object.defineProperty(wc2,"__esModule",{value:!0});wc2.MeterSelector=void 0;var OY0=HT1();class Uc2{_nameFilter;_versionFilter;_schemaUrlFilter;constructor(A){this._nameFilter=new OY0.ExactPredicate(A?.name),this._versionFilter=new OY0.ExactPredicate(A?.version),this._schemaUrlFilter=new OY0.ExactPredicate(A?.schemaUrl)}getNameFilter(){return this._nameFilter}getVersionFilter(){return this._versionFilter}getSchemaUrlFilter(){return this._schemaUrlFilter}}wc2.MeterSelector=Uc2});
var qd2=E((wd2)=>{Object.defineProperty(wd2,"__esModule",{value:!0});wd2.getConflictResolutionRecipe=wd2.getDescriptionResolutionRecipe=wd2.getTypeConflictResolutionRecipe=wd2.getUnitConflictResolutionRecipe=wd2.getValueTypeConflictResolutionRecipe=wd2.getIncompatibilityDetails=void 0;function zG6(A,B){let Q="";if(A.unit!==B.unit)Q+=`	- Unit '${A.unit}' does not match '${B.unit}'
`;if(A.type!==B.type)Q+=`	- Type '${A.type}' does not match '${B.type}'
`;if(A.valueType!==B.valueType)Q+=`	- Value Type '${A.valueType}' does not match '${B.valueType}'
`;if(A.description!==B.description)Q+=`	- Description '${A.description}' does not match '${B.description}'
`;return Q}wd2.getIncompatibilityDetails=zG6;function Hd2(A,B){return`	- use valueType '${A.valueType}' on instrument creation or use an instrument name other than '${B.name}'`}wd2.getValueTypeConflictResolutionRecipe=Hd2;function zd2(A,B){return`	- use unit '${A.unit}' on instrument creation or use an instrument name other than '${B.name}'`}wd2.getUnitConflictResolutionRecipe=zd2;function Ed2(A,B){let Q={name:B.name,type:B.type,unit:B.unit},D=JSON.stringify(Q);return`	- create a new view with a name other than '${A.name}' and InstrumentSelector '${D}'`}wd2.getTypeConflictResolutionRecipe=Ed2;function Ud2(A,B){let Q={name:B.name,type:B.type,unit:B.unit},D=JSON.stringify(Q);return`	- create a new view with a name other than '${A.name}' and InstrumentSelector '${D}'
    	- OR - create a new view with the name ${A.name} and description '${A.description}' and InstrumentSelector ${D}
    	- OR - create a new view with the name ${B.name} and description '${A.description}' and InstrumentSelector ${D}`}wd2.getDescriptionResolutionRecipe=Ud2;function EG6(A,B){if(A.valueType!==B.valueType)return Hd2(A,B);if(A.unit!==B.unit)return zd2(A,B);if(A.type!==B.type)return Ed2(A,B);if(A.description!==B.description)return Ud2(A,B);return""}wd2.getConflictResolutionRecipe=EG6});
var qi2=E((wi2)=>{Object.defineProperty(wi2,"__esModule",{value:!0});wi2.createHttpExporterTransport=void 0;class Ui2{_parameters;_utils=null;constructor(A){this._parameters=A}async send(A,B){let{agent:Q,send:D}=this._loadUtils();return new Promise((Z)=>{D(this._parameters,Q,A,(G)=>{Z(G)},B)})}shutdown(){}_loadUtils(){let A=this._utils;if(A===null){let{sendWithHttp:B,createHttpAgent:Q}=Ei2();A=this._utils={agent:Q(this._parameters.url,this._parameters.agentOptions),send:B}}return A}}function _Y6(A){return new Ui2(A)}wi2.createHttpExporterTransport=_Y6});
var qj2=E((wj2)=>{Object.defineProperty(wj2,"__esModule",{value:!0});wj2.VERSION=void 0;wj2.VERSION="2.0.0"});
var qu2=E((wu2)=>{Object.defineProperty(wu2,"__esModule",{value:!0});wu2.ConsoleMetricExporter=void 0;var Uu2=x3(),xD6=QY0();class GY0{_shutdown=!1;_temporalitySelector;constructor(A){this._temporalitySelector=A?.temporalitySelector??xD6.DEFAULT_AGGREGATION_TEMPORALITY_SELECTOR}export(A,B){if(this._shutdown){setImmediate(B,{code:Uu2.ExportResultCode.FAILED});return}return GY0._sendMetrics(A,B)}forceFlush(){return Promise.resolve()}selectAggregationTemporality(A){return this._temporalitySelector(A)}shutdown(){return this._shutdown=!0,Promise.resolve()}static _sendMetrics(A,B){for(let Q of A.scopeMetrics)for(let D of Q.metrics)console.dir({descriptor:D.descriptor,dataPointType:D.dataPointType,dataPoints:D.dataPoints},{depth:null});B({code:Uu2.ExportResultCode.SUCCESS})}}wu2.ConsoleMetricExporter=GY0});
var qv2=E(($u)=>{var r96=$u&&$u.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),o96=$u&&$u.__exportStar||function(A,B){for(var Q in A)if(Q!=="default"&&!Object.prototype.hasOwnProperty.call(B,Q))r96(B,A,Q)};Object.defineProperty($u,"__esModule",{value:!0});o96($v2(),$u)});
var rP2=E((aP2)=>{Object.defineProperty(aP2,"__esModule",{value:!0});aP2.DropAggregator=void 0;var he4=So();class nP2{kind=he4.AggregatorKind.DROP;createAccumulation(){return}merge(A,B){return}diff(A,B){return}toMetricData(A,B,Q,D){return}}aP2.DropAggregator=nP2});
var rY0=E((yh5,el2)=>{el2.exports={}});
var rf2=E((if2)=>{Object.defineProperty(if2,"__esModule",{value:!0});if2.SEMRESATTRS_K8S_STATEFULSET_NAME=if2.SEMRESATTRS_K8S_STATEFULSET_UID=if2.SEMRESATTRS_K8S_DEPLOYMENT_NAME=if2.SEMRESATTRS_K8S_DEPLOYMENT_UID=if2.SEMRESATTRS_K8S_REPLICASET_NAME=if2.SEMRESATTRS_K8S_REPLICASET_UID=if2.SEMRESATTRS_K8S_CONTAINER_NAME=if2.SEMRESATTRS_K8S_POD_NAME=if2.SEMRESATTRS_K8S_POD_UID=if2.SEMRESATTRS_K8S_NAMESPACE_NAME=if2.SEMRESATTRS_K8S_NODE_UID=if2.SEMRESATTRS_K8S_NODE_NAME=if2.SEMRESATTRS_K8S_CLUSTER_NAME=if2.SEMRESATTRS_HOST_IMAGE_VERSION=if2.SEMRESATTRS_HOST_IMAGE_ID=if2.SEMRESATTRS_HOST_IMAGE_NAME=if2.SEMRESATTRS_HOST_ARCH=if2.SEMRESATTRS_HOST_TYPE=if2.SEMRESATTRS_HOST_NAME=if2.SEMRESATTRS_HOST_ID=if2.SEMRESATTRS_FAAS_MAX_MEMORY=if2.SEMRESATTRS_FAAS_INSTANCE=if2.SEMRESATTRS_FAAS_VERSION=if2.SEMRESATTRS_FAAS_ID=if2.SEMRESATTRS_FAAS_NAME=if2.SEMRESATTRS_DEVICE_MODEL_NAME=if2.SEMRESATTRS_DEVICE_MODEL_IDENTIFIER=if2.SEMRESATTRS_DEVICE_ID=if2.SEMRESATTRS_DEPLOYMENT_ENVIRONMENT=if2.SEMRESATTRS_CONTAINER_IMAGE_TAG=if2.SEMRESATTRS_CONTAINER_IMAGE_NAME=if2.SEMRESATTRS_CONTAINER_RUNTIME=if2.SEMRESATTRS_CONTAINER_ID=if2.SEMRESATTRS_CONTAINER_NAME=if2.SEMRESATTRS_AWS_LOG_STREAM_ARNS=if2.SEMRESATTRS_AWS_LOG_STREAM_NAMES=if2.SEMRESATTRS_AWS_LOG_GROUP_ARNS=if2.SEMRESATTRS_AWS_LOG_GROUP_NAMES=if2.SEMRESATTRS_AWS_EKS_CLUSTER_ARN=if2.SEMRESATTRS_AWS_ECS_TASK_REVISION=if2.SEMRESATTRS_AWS_ECS_TASK_FAMILY=if2.SEMRESATTRS_AWS_ECS_TASK_ARN=if2.SEMRESATTRS_AWS_ECS_LAUNCHTYPE=if2.SEMRESATTRS_AWS_ECS_CLUSTER_ARN=if2.SEMRESATTRS_AWS_ECS_CONTAINER_ARN=if2.SEMRESATTRS_CLOUD_PLATFORM=if2.SEMRESATTRS_CLOUD_AVAILABILITY_ZONE=if2.SEMRESATTRS_CLOUD_REGION=if2.SEMRESATTRS_CLOUD_ACCOUNT_ID=if2.SEMRESATTRS_CLOUD_PROVIDER=void 0;if2.CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE=if2.CLOUDPLATFORMVALUES_AZURE_APP_SERVICE=if2.CLOUDPLATFORMVALUES_AZURE_FUNCTIONS=if2.CLOUDPLATFORMVALUES_AZURE_AKS=if2.CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES=if2.CLOUDPLATFORMVALUES_AZURE_VM=if2.CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK=if2.CLOUDPLATFORMVALUES_AWS_LAMBDA=if2.CLOUDPLATFORMVALUES_AWS_EKS=if2.CLOUDPLATFORMVALUES_AWS_ECS=if2.CLOUDPLATFORMVALUES_AWS_EC2=if2.CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC=if2.CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS=if2.CloudProviderValues=if2.CLOUDPROVIDERVALUES_GCP=if2.CLOUDPROVIDERVALUES_AZURE=if2.CLOUDPROVIDERVALUES_AWS=if2.CLOUDPROVIDERVALUES_ALIBABA_CLOUD=if2.SemanticResourceAttributes=if2.SEMRESATTRS_WEBENGINE_DESCRIPTION=if2.SEMRESATTRS_WEBENGINE_VERSION=if2.SEMRESATTRS_WEBENGINE_NAME=if2.SEMRESATTRS_TELEMETRY_AUTO_VERSION=if2.SEMRESATTRS_TELEMETRY_SDK_VERSION=if2.SEMRESATTRS_TELEMETRY_SDK_LANGUAGE=if2.SEMRESATTRS_TELEMETRY_SDK_NAME=if2.SEMRESATTRS_SERVICE_VERSION=if2.SEMRESATTRS_SERVICE_INSTANCE_ID=if2.SEMRESATTRS_SERVICE_NAMESPACE=if2.SEMRESATTRS_SERVICE_NAME=if2.SEMRESATTRS_PROCESS_RUNTIME_DESCRIPTION=if2.SEMRESATTRS_PROCESS_RUNTIME_VERSION=if2.SEMRESATTRS_PROCESS_RUNTIME_NAME=if2.SEMRESATTRS_PROCESS_OWNER=if2.SEMRESATTRS_PROCESS_COMMAND_ARGS=if2.SEMRESATTRS_PROCESS_COMMAND_LINE=if2.SEMRESATTRS_PROCESS_COMMAND=if2.SEMRESATTRS_PROCESS_EXECUTABLE_PATH=if2.SEMRESATTRS_PROCESS_EXECUTABLE_NAME=if2.SEMRESATTRS_PROCESS_PID=if2.SEMRESATTRS_OS_VERSION=if2.SEMRESATTRS_OS_NAME=if2.SEMRESATTRS_OS_DESCRIPTION=if2.SEMRESATTRS_OS_TYPE=if2.SEMRESATTRS_K8S_CRONJOB_NAME=if2.SEMRESATTRS_K8S_CRONJOB_UID=if2.SEMRESATTRS_K8S_JOB_NAME=if2.SEMRESATTRS_K8S_JOB_UID=if2.SEMRESATTRS_K8S_DAEMONSET_NAME=if2.SEMRESATTRS_K8S_DAEMONSET_UID=void 0;if2.TelemetrySdkLanguageValues=if2.TELEMETRYSDKLANGUAGEVALUES_WEBJS=if2.TELEMETRYSDKLANGUAGEVALUES_RUBY=if2.TELEMETRYSDKLANGUAGEVALUES_PYTHON=if2.TELEMETRYSDKLANGUAGEVALUES_PHP=if2.TELEMETRYSDKLANGUAGEVALUES_NODEJS=if2.TELEMETRYSDKLANGUAGEVALUES_JAVA=if2.TELEMETRYSDKLANGUAGEVALUES_GO=if2.TELEMETRYSDKLANGUAGEVALUES_ERLANG=if2.TELEMETRYSDKLANGUAGEVALUES_DOTNET=if2.TELEMETRYSDKLANGUAGEVALUES_CPP=if2.OsTypeValues=if2.OSTYPEVALUES_Z_OS=if2.OSTYPEVALUES_SOLARIS=if2.OSTYPEVALUES_AIX=if2.OSTYPEVALUES_HPUX=if2.OSTYPEVALUES_DRAGONFLYBSD=if2.OSTYPEVALUES_OPENBSD=if2.OSTYPEVALUES_NETBSD=if2.OSTYPEVALUES_FREEBSD=if2.OSTYPEVALUES_DARWIN=if2.OSTYPEVALUES_LINUX=if2.OSTYPEVALUES_WINDOWS=if2.HostArchValues=if2.HOSTARCHVALUES_X86=if2.HOSTARCHVALUES_PPC64=if2.HOSTARCHVALUES_PPC32=if2.HOSTARCHVALUES_IA64=if2.HOSTARCHVALUES_ARM64=if2.HOSTARCHVALUES_ARM32=if2.HOSTARCHVALUES_AMD64=if2.AwsEcsLaunchtypeValues=if2.AWSECSLAUNCHTYPEVALUES_FARGATE=if2.AWSECSLAUNCHTYPEVALUES_EC2=if2.CloudPlatformValues=if2.CLOUDPLATFORMVALUES_GCP_APP_ENGINE=if2.CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS=if2.CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE=if2.CLOUDPLATFORMVALUES_GCP_CLOUD_RUN=void 0;var qu=mI0(),Nv2="cloud.provider",Lv2="cloud.account.id",Mv2="cloud.region",Rv2="cloud.availability_zone",Ov2="cloud.platform",Tv2="aws.ecs.container.arn",Pv2="aws.ecs.cluster.arn",Sv2="aws.ecs.launchtype",jv2="aws.ecs.task.arn",yv2="aws.ecs.task.family",kv2="aws.ecs.task.revision",_v2="aws.eks.cluster.arn",xv2="aws.log.group.names",vv2="aws.log.group.arns",bv2="aws.log.stream.names",fv2="aws.log.stream.arns",hv2="container.name",gv2="container.id",uv2="container.runtime",mv2="container.image.name",dv2="container.image.tag",cv2="deployment.environment",lv2="device.id",pv2="device.model.identifier",iv2="device.model.name",nv2="faas.name",av2="faas.id",sv2="faas.version",rv2="faas.instance",ov2="faas.max_memory",tv2="host.id",ev2="host.name",Ab2="host.type",Bb2="host.arch",Qb2="host.image.name",Db2="host.image.id",Zb2="host.image.version",Gb2="k8s.cluster.name",Fb2="k8s.node.name",Ib2="k8s.node.uid",Yb2="k8s.namespace.name",Wb2="k8s.pod.uid",Jb2="k8s.pod.name",Xb2="k8s.container.name",Vb2="k8s.replicaset.uid",Cb2="k8s.replicaset.name",Kb2="k8s.deployment.uid",Hb2="k8s.deployment.name",zb2="k8s.statefulset.uid",Eb2="k8s.statefulset.name",Ub2="k8s.daemonset.uid",wb2="k8s.daemonset.name",$b2="k8s.job.uid",qb2="k8s.job.name",Nb2="k8s.cronjob.uid",Lb2="k8s.cronjob.name",Mb2="os.type",Rb2="os.description",Ob2="os.name",Tb2="os.version",Pb2="process.pid",Sb2="process.executable.name",jb2="process.executable.path",yb2="process.command",kb2="process.command_line",_b2="process.command_args",xb2="process.owner",vb2="process.runtime.name",bb2="process.runtime.version",fb2="process.runtime.description",hb2="service.name",gb2="service.namespace",ub2="service.instance.id",mb2="service.version",db2="telemetry.sdk.name",cb2="telemetry.sdk.language",lb2="telemetry.sdk.version",pb2="telemetry.auto.version",ib2="webengine.name",nb2="webengine.version",ab2="webengine.description";if2.SEMRESATTRS_CLOUD_PROVIDER=Nv2;if2.SEMRESATTRS_CLOUD_ACCOUNT_ID=Lv2;if2.SEMRESATTRS_CLOUD_REGION=Mv2;if2.SEMRESATTRS_CLOUD_AVAILABILITY_ZONE=Rv2;if2.SEMRESATTRS_CLOUD_PLATFORM=Ov2;if2.SEMRESATTRS_AWS_ECS_CONTAINER_ARN=Tv2;if2.SEMRESATTRS_AWS_ECS_CLUSTER_ARN=Pv2;if2.SEMRESATTRS_AWS_ECS_LAUNCHTYPE=Sv2;if2.SEMRESATTRS_AWS_ECS_TASK_ARN=jv2;if2.SEMRESATTRS_AWS_ECS_TASK_FAMILY=yv2;if2.SEMRESATTRS_AWS_ECS_TASK_REVISION=kv2;if2.SEMRESATTRS_AWS_EKS_CLUSTER_ARN=_v2;if2.SEMRESATTRS_AWS_LOG_GROUP_NAMES=xv2;if2.SEMRESATTRS_AWS_LOG_GROUP_ARNS=vv2;if2.SEMRESATTRS_AWS_LOG_STREAM_NAMES=bv2;if2.SEMRESATTRS_AWS_LOG_STREAM_ARNS=fv2;if2.SEMRESATTRS_CONTAINER_NAME=hv2;if2.SEMRESATTRS_CONTAINER_ID=gv2;if2.SEMRESATTRS_CONTAINER_RUNTIME=uv2;if2.SEMRESATTRS_CONTAINER_IMAGE_NAME=mv2;if2.SEMRESATTRS_CONTAINER_IMAGE_TAG=dv2;if2.SEMRESATTRS_DEPLOYMENT_ENVIRONMENT=cv2;if2.SEMRESATTRS_DEVICE_ID=lv2;if2.SEMRESATTRS_DEVICE_MODEL_IDENTIFIER=pv2;if2.SEMRESATTRS_DEVICE_MODEL_NAME=iv2;if2.SEMRESATTRS_FAAS_NAME=nv2;if2.SEMRESATTRS_FAAS_ID=av2;if2.SEMRESATTRS_FAAS_VERSION=sv2;if2.SEMRESATTRS_FAAS_INSTANCE=rv2;if2.SEMRESATTRS_FAAS_MAX_MEMORY=ov2;if2.SEMRESATTRS_HOST_ID=tv2;if2.SEMRESATTRS_HOST_NAME=ev2;if2.SEMRESATTRS_HOST_TYPE=Ab2;if2.SEMRESATTRS_HOST_ARCH=Bb2;if2.SEMRESATTRS_HOST_IMAGE_NAME=Qb2;if2.SEMRESATTRS_HOST_IMAGE_ID=Db2;if2.SEMRESATTRS_HOST_IMAGE_VERSION=Zb2;if2.SEMRESATTRS_K8S_CLUSTER_NAME=Gb2;if2.SEMRESATTRS_K8S_NODE_NAME=Fb2;if2.SEMRESATTRS_K8S_NODE_UID=Ib2;if2.SEMRESATTRS_K8S_NAMESPACE_NAME=Yb2;if2.SEMRESATTRS_K8S_POD_UID=Wb2;if2.SEMRESATTRS_K8S_POD_NAME=Jb2;if2.SEMRESATTRS_K8S_CONTAINER_NAME=Xb2;if2.SEMRESATTRS_K8S_REPLICASET_UID=Vb2;if2.SEMRESATTRS_K8S_REPLICASET_NAME=Cb2;if2.SEMRESATTRS_K8S_DEPLOYMENT_UID=Kb2;if2.SEMRESATTRS_K8S_DEPLOYMENT_NAME=Hb2;if2.SEMRESATTRS_K8S_STATEFULSET_UID=zb2;if2.SEMRESATTRS_K8S_STATEFULSET_NAME=Eb2;if2.SEMRESATTRS_K8S_DAEMONSET_UID=Ub2;if2.SEMRESATTRS_K8S_DAEMONSET_NAME=wb2;if2.SEMRESATTRS_K8S_JOB_UID=$b2;if2.SEMRESATTRS_K8S_JOB_NAME=qb2;if2.SEMRESATTRS_K8S_CRONJOB_UID=Nb2;if2.SEMRESATTRS_K8S_CRONJOB_NAME=Lb2;if2.SEMRESATTRS_OS_TYPE=Mb2;if2.SEMRESATTRS_OS_DESCRIPTION=Rb2;if2.SEMRESATTRS_OS_NAME=Ob2;if2.SEMRESATTRS_OS_VERSION=Tb2;if2.SEMRESATTRS_PROCESS_PID=Pb2;if2.SEMRESATTRS_PROCESS_EXECUTABLE_NAME=Sb2;if2.SEMRESATTRS_PROCESS_EXECUTABLE_PATH=jb2;if2.SEMRESATTRS_PROCESS_COMMAND=yb2;if2.SEMRESATTRS_PROCESS_COMMAND_LINE=kb2;if2.SEMRESATTRS_PROCESS_COMMAND_ARGS=_b2;if2.SEMRESATTRS_PROCESS_OWNER=xb2;if2.SEMRESATTRS_PROCESS_RUNTIME_NAME=vb2;if2.SEMRESATTRS_PROCESS_RUNTIME_VERSION=bb2;if2.SEMRESATTRS_PROCESS_RUNTIME_DESCRIPTION=fb2;if2.SEMRESATTRS_SERVICE_NAME=hb2;if2.SEMRESATTRS_SERVICE_NAMESPACE=gb2;if2.SEMRESATTRS_SERVICE_INSTANCE_ID=ub2;if2.SEMRESATTRS_SERVICE_VERSION=mb2;if2.SEMRESATTRS_TELEMETRY_SDK_NAME=db2;if2.SEMRESATTRS_TELEMETRY_SDK_LANGUAGE=cb2;if2.SEMRESATTRS_TELEMETRY_SDK_VERSION=lb2;if2.SEMRESATTRS_TELEMETRY_AUTO_VERSION=pb2;if2.SEMRESATTRS_WEBENGINE_NAME=ib2;if2.SEMRESATTRS_WEBENGINE_VERSION=nb2;if2.SEMRESATTRS_WEBENGINE_DESCRIPTION=ab2;if2.SemanticResourceAttributes=qu.createConstMap([Nv2,Lv2,Mv2,Rv2,Ov2,Tv2,Pv2,Sv2,jv2,yv2,kv2,_v2,xv2,vv2,bv2,fv2,hv2,gv2,uv2,mv2,dv2,cv2,lv2,pv2,iv2,nv2,av2,sv2,rv2,ov2,tv2,ev2,Ab2,Bb2,Qb2,Db2,Zb2,Gb2,Fb2,Ib2,Yb2,Wb2,Jb2,Xb2,Vb2,Cb2,Kb2,Hb2,zb2,Eb2,Ub2,wb2,$b2,qb2,Nb2,Lb2,Mb2,Rb2,Ob2,Tb2,Pb2,Sb2,jb2,yb2,kb2,_b2,xb2,vb2,bb2,fb2,hb2,gb2,ub2,mb2,db2,cb2,lb2,pb2,ib2,nb2,ab2]);var sb2="alibaba_cloud",rb2="aws",ob2="azure",tb2="gcp";if2.CLOUDPROVIDERVALUES_ALIBABA_CLOUD=sb2;if2.CLOUDPROVIDERVALUES_AWS=rb2;if2.CLOUDPROVIDERVALUES_AZURE=ob2;if2.CLOUDPROVIDERVALUES_GCP=tb2;if2.CloudProviderValues=qu.createConstMap([sb2,rb2,ob2,tb2]);var eb2="alibaba_cloud_ecs",Af2="alibaba_cloud_fc",Bf2="aws_ec2",Qf2="aws_ecs",Df2="aws_eks",Zf2="aws_lambda",Gf2="aws_elastic_beanstalk",Ff2="azure_vm",If2="azure_container_instances",Yf2="azure_aks",Wf2="azure_functions",Jf2="azure_app_service",Xf2="gcp_compute_engine",Vf2="gcp_cloud_run",Cf2="gcp_kubernetes_engine",Kf2="gcp_cloud_functions",Hf2="gcp_app_engine";if2.CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS=eb2;if2.CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC=Af2;if2.CLOUDPLATFORMVALUES_AWS_EC2=Bf2;if2.CLOUDPLATFORMVALUES_AWS_ECS=Qf2;if2.CLOUDPLATFORMVALUES_AWS_EKS=Df2;if2.CLOUDPLATFORMVALUES_AWS_LAMBDA=Zf2;if2.CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK=Gf2;if2.CLOUDPLATFORMVALUES_AZURE_VM=Ff2;if2.CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES=If2;if2.CLOUDPLATFORMVALUES_AZURE_AKS=Yf2;if2.CLOUDPLATFORMVALUES_AZURE_FUNCTIONS=Wf2;if2.CLOUDPLATFORMVALUES_AZURE_APP_SERVICE=Jf2;if2.CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE=Xf2;if2.CLOUDPLATFORMVALUES_GCP_CLOUD_RUN=Vf2;if2.CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE=Cf2;if2.CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS=Kf2;if2.CLOUDPLATFORMVALUES_GCP_APP_ENGINE=Hf2;if2.CloudPlatformValues=qu.createConstMap([eb2,Af2,Bf2,Qf2,Df2,Zf2,Gf2,Ff2,If2,Yf2,Wf2,Jf2,Xf2,Vf2,Cf2,Kf2,Hf2]);var zf2="ec2",Ef2="fargate";if2.AWSECSLAUNCHTYPEVALUES_EC2=zf2;if2.AWSECSLAUNCHTYPEVALUES_FARGATE=Ef2;if2.AwsEcsLaunchtypeValues=qu.createConstMap([zf2,Ef2]);var Uf2="amd64",wf2="arm32",$f2="arm64",qf2="ia64",Nf2="ppc32",Lf2="ppc64",Mf2="x86";if2.HOSTARCHVALUES_AMD64=Uf2;if2.HOSTARCHVALUES_ARM32=wf2;if2.HOSTARCHVALUES_ARM64=$f2;if2.HOSTARCHVALUES_IA64=qf2;if2.HOSTARCHVALUES_PPC32=Nf2;if2.HOSTARCHVALUES_PPC64=Lf2;if2.HOSTARCHVALUES_X86=Mf2;if2.HostArchValues=qu.createConstMap([Uf2,wf2,$f2,qf2,Nf2,Lf2,Mf2]);var Rf2="windows",Of2="linux",Tf2="darwin",Pf2="freebsd",Sf2="netbsd",jf2="openbsd",yf2="dragonflybsd",kf2="hpux",_f2="aix",xf2="solaris",vf2="z_os";if2.OSTYPEVALUES_WINDOWS=Rf2;if2.OSTYPEVALUES_LINUX=Of2;if2.OSTYPEVALUES_DARWIN=Tf2;if2.OSTYPEVALUES_FREEBSD=Pf2;if2.OSTYPEVALUES_NETBSD=Sf2;if2.OSTYPEVALUES_OPENBSD=jf2;if2.OSTYPEVALUES_DRAGONFLYBSD=yf2;if2.OSTYPEVALUES_HPUX=kf2;if2.OSTYPEVALUES_AIX=_f2;if2.OSTYPEVALUES_SOLARIS=xf2;if2.OSTYPEVALUES_Z_OS=vf2;if2.OsTypeValues=qu.createConstMap([Rf2,Of2,Tf2,Pf2,Sf2,jf2,yf2,kf2,_f2,xf2,vf2]);var bf2="cpp",ff2="dotnet",hf2="erlang",gf2="go",uf2="java",mf2="nodejs",df2="php",cf2="python",lf2="ruby",pf2="webjs";if2.TELEMETRYSDKLANGUAGEVALUES_CPP=bf2;if2.TELEMETRYSDKLANGUAGEVALUES_DOTNET=ff2;if2.TELEMETRYSDKLANGUAGEVALUES_ERLANG=hf2;if2.TELEMETRYSDKLANGUAGEVALUES_GO=gf2;if2.TELEMETRYSDKLANGUAGEVALUES_JAVA=uf2;if2.TELEMETRYSDKLANGUAGEVALUES_NODEJS=mf2;if2.TELEMETRYSDKLANGUAGEVALUES_PHP=df2;if2.TELEMETRYSDKLANGUAGEVALUES_PYTHON=cf2;if2.TELEMETRYSDKLANGUAGEVALUES_RUBY=lf2;if2.TELEMETRYSDKLANGUAGEVALUES_WEBJS=pf2;if2.TelemetrySdkLanguageValues=qu.createConstMap([bf2,ff2,hf2,gf2,uf2,mf2,df2,cf2,lf2,pf2])});
var rh2=E((ah2)=>{Object.defineProperty(ah2,"__esModule",{value:!0});ah2.isPlainObject=void 0;var y76="[object Object]",k76="[object Null]",_76="[object Undefined]",x76=Function.prototype,lh2=x76.toString,v76=lh2.call(Object),b76=Object.getPrototypeOf,ph2=Object.prototype,ih2=ph2.hasOwnProperty,Lu=Symbol?Symbol.toStringTag:void 0,nh2=ph2.toString;function f76(A){if(!h76(A)||g76(A)!==y76)return!1;let B=b76(A);if(B===null)return!0;let Q=ih2.call(B,"constructor")&&B.constructor;return typeof Q=="function"&&Q instanceof Q&&lh2.call(Q)===v76}ah2.isPlainObject=f76;function h76(A){return A!=null&&typeof A=="object"}function g76(A){if(A==null)return A===void 0?_76:k76;return Lu&&Lu in Object(A)?u76(A):m76(A)}function u76(A){let B=ih2.call(A,Lu),Q=A[Lu],D=!1;try{A[Lu]=void 0,D=!0}catch(G){}let Z=nh2.call(A);if(D)if(B)A[Lu]=Q;else delete A[Lu];return Z}function m76(A){return nh2.call(A)}});
var rp2=E((WW0)=>{Object.defineProperty(WW0,"__esModule",{value:!0});WW0.JsonLogsSerializer=void 0;var IY6=sp2();Object.defineProperty(WW0,"JsonLogsSerializer",{enumerable:!0,get:function(){return IY6.JsonLogsSerializer}})});
var ru2=E((au2)=>{Object.defineProperty(au2,"__esModule",{value:!0});au2.getMachineId=void 0;var YZ6=J1("fs"),WZ6=YT1(),nu2=VQ();async function JZ6(){try{return(await YZ6.promises.readFile("/etc/hostid",{encoding:"utf8"})).trim()}catch(A){nu2.diag.debug(`error reading machine id: ${A}`)}try{return(await WZ6.execAsync("kenv -q smbios.system.uuid")).stdout.trim()}catch(A){nu2.diag.debug(`error reading machine id: ${A}`)}return}au2.getMachineId=JZ6});
var s51=E((_S2)=>{Object.defineProperty(_S2,"__esModule",{value:!0});_S2.isTracingSuppressed=_S2.unsuppressTracing=_S2.suppressTracing=void 0;var C16=VQ(),vI0=C16.createContextKey("OpenTelemetry SDK Context Key SUPPRESS_TRACING");function K16(A){return A.setValue(vI0,!0)}_S2.suppressTracing=K16;function H16(A){return A.deleteValue(vI0)}_S2.unsuppressTracing=H16;function z16(A){return A.getValue(vI0)===!0}_S2.isTracingSuppressed=z16});
var sI0=E((_h2)=>{Object.defineProperty(_h2,"__esModule",{value:!0});_h2.TraceState=void 0;var Sh2=Ph2(),jh2=32,K76=512,yh2=",",kh2="=";class aI0{_internalState=new Map;constructor(A){if(A)this._parse(A)}set(A,B){let Q=this._clone();if(Q._internalState.has(A))Q._internalState.delete(A);return Q._internalState.set(A,B),Q}unset(A){let B=this._clone();return B._internalState.delete(A),B}get(A){return this._internalState.get(A)}serialize(){return this._keys().reduce((A,B)=>{return A.push(B+kh2+this.get(B)),A},[]).join(yh2)}_parse(A){if(A.length>K76)return;if(this._internalState=A.split(yh2).reverse().reduce((B,Q)=>{let D=Q.trim(),Z=D.indexOf(kh2);if(Z!==-1){let G=D.slice(0,Z),F=D.slice(Z+1,Q.length);if(Sh2.validateKey(G)&&Sh2.validateValue(F))B.set(G,F)}return B},new Map),this._internalState.size>jh2)this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,jh2))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let A=new aI0;return A._internalState=new Map(this._internalState),A}}_h2.TraceState=aI0});
var sR2=E((nR2)=>{Object.defineProperty(nR2,"__esModule",{value:!0});nR2.baggageEntryMetadataSymbol=void 0;nR2.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")});
var sY0=E((tl2)=>{var FI6=tl2;FI6.Service=ol2()});
var sl2=E((Ph5,al2)=>{al2.exports=bu;var nl2=MT1();(bu.prototype=Object.create(nl2.prototype)).constructor=bu;var il2=eL();function bu(A){nl2.call(this,A)}bu._configure=function(){if(il2.Buffer)bu.prototype._slice=il2.Buffer.prototype.slice};bu.prototype.string=function A(){var B=this.uint32();return this.buf.utf8Slice?this.buf.utf8Slice(this.pos,this.pos=Math.min(this.pos+B,this.len)):this.buf.toString("utf-8",this.pos,this.pos=Math.min(this.pos+B,this.len))};bu._configure()});
var sp2=E((np2)=>{Object.defineProperty(np2,"__esModule",{value:!0});np2.JsonLogsSerializer=void 0;var FY6=DW0();np2.JsonLogsSerializer={serializeRequest:(A)=>{let B=FY6.createExportLogsServiceRequest(A,{useHex:!0,useLongBits:!1});return new TextEncoder().encode(JSON.stringify(B))},deserializeResponse:(A)=>{return JSON.parse(new TextDecoder().decode(A))}}});
var tF0=E((iO2)=>{Object.defineProperty(iO2,"__esModule",{value:!0});iO2.ProxyTracer=void 0;var ao4=oF0(),so4=new ao4.NoopTracer;class pO2{constructor(A,B,Q,D){this._provider=A,this.name=B,this.version=Q,this.options=D}startSpan(A,B,Q){return this._getTracer().startSpan(A,B,Q)}startActiveSpan(A,B,Q,D){let Z=this._getTracer();return Reflect.apply(Z.startActiveSpan,Z,arguments)}_getTracer(){if(this._delegate)return this._delegate;let A=this._provider.getDelegateTracer(this.name,this.version,this.options);if(!A)return so4;return this._delegate=A,this._delegate}}iO2.ProxyTracer=pO2});
var tc2=E((rc2)=>{Object.defineProperty(rc2,"__esModule",{value:!0});rc2.createLoggingPartialSuccessResponseHandler=void 0;var TF6=VQ();function PF6(A){return Object.prototype.hasOwnProperty.call(A,"partialSuccess")}function SF6(){return{handleResponse(A){if(A==null||!PF6(A)||A.partialSuccess==null||Object.keys(A.partialSuccess).length===0)return;TF6.diag.warn("Received Partial Success response:",JSON.stringify(A.partialSuccess))}}}rc2.createLoggingPartialSuccessResponseHandler=SF6});
var tw=E((lP2)=>{Object.defineProperty(lP2,"__esModule",{value:!0});lP2.equalsCaseInsensitive=lP2.binarySearchUB=lP2.setEquals=lP2.FlatMap=lP2.isPromiseAllSettledRejectionResult=lP2.PromiseAllSettled=lP2.callWithTimeout=lP2.TimeoutError=lP2.instrumentationScopeId=lP2.hashAttributes=lP2.isNotNullish=void 0;function Ee4(A){return A!==void 0&&A!==null}lP2.isNotNullish=Ee4;function Ue4(A){let B=Object.keys(A);if(B.length===0)return"";return B=B.sort(),JSON.stringify(B.map((Q)=>[Q,A[Q]]))}lP2.hashAttributes=Ue4;function we4(A){return`${A.name}:${A.version??""}:${A.schemaUrl??""}`}lP2.instrumentationScopeId=we4;class dO1 extends Error{constructor(A){super(A);Object.setPrototypeOf(this,dO1.prototype)}}lP2.TimeoutError=dO1;function $e4(A,B){let Q,D=new Promise(function Z(G,F){Q=setTimeout(function I(){F(new dO1("Operation timed out."))},B)});return Promise.race([A,D]).then((Z)=>{return clearTimeout(Q),Z},(Z)=>{throw clearTimeout(Q),Z})}lP2.callWithTimeout=$e4;async function qe4(A){return Promise.all(A.map(async(B)=>{try{return{status:"fulfilled",value:await B}}catch(Q){return{status:"rejected",reason:Q}}}))}lP2.PromiseAllSettled=qe4;function Ne4(A){return A.status==="rejected"}lP2.isPromiseAllSettledRejectionResult=Ne4;function Le4(A,B){let Q=[];return A.forEach((D)=>{Q.push(...B(D))}),Q}lP2.FlatMap=Le4;function Me4(A,B){if(A.size!==B.size)return!1;for(let Q of A)if(!B.has(Q))return!1;return!0}lP2.setEquals=Me4;function Re4(A,B){let Q=0,D=A.length-1,Z=A.length;while(D>=Q){let G=Q+Math.trunc((D-Q)/2);if(A[G]<B)Q=G+1;else Z=G,D=G-1}return Z}lP2.binarySearchUB=Re4;function Oe4(A,B){return A.toLowerCase()===B.toLowerCase()}lP2.equalsCaseInsensitive=Oe4});
var uF0=E((zO2)=>{Object.defineProperty(zO2,"__esModule",{value:!0});zO2.defaultTextMapSetter=zO2.defaultTextMapGetter=void 0;zO2.defaultTextMapGetter={get(A,B){if(A==null)return;return A[B]},keys(A){if(A==null)return[];return Object.keys(A)}};zO2.defaultTextMapSetter={set(A,B,Q){if(A==null)return;A[B]=Q}}});
var uI0=E((Bj2)=>{Object.defineProperty(Bj2,"__esModule",{value:!0});Bj2.loggingErrorHandler=void 0;var f16=VQ();function h16(){return(A)=>{f16.diag.error(g16(A))}}Bj2.loggingErrorHandler=h16;function g16(A){if(typeof A==="string")return A;else return JSON.stringify(u16(A))}function u16(A){let B={},Q=A;while(Q!==null)Object.getOwnPropertyNames(Q).forEach((D)=>{if(B[D])return;let Z=Q[D];if(Z)B[D]=String(Z)}),Q=Object.getPrototypeOf(Q);return B}});
var uR2=E((hR2)=>{Object.defineProperty(hR2,"__esModule",{value:!0});hR2.createLogLevelDiagLogger=void 0;var XP=kO1();function nr4(A,B){if(A<XP.DiagLogLevel.NONE)A=XP.DiagLogLevel.NONE;else if(A>XP.DiagLogLevel.ALL)A=XP.DiagLogLevel.ALL;B=B||{};function Q(D,Z){let G=B[D];if(typeof G==="function"&&A>=Z)return G.bind(B);return function(){}}return{error:Q("error",XP.DiagLogLevel.ERROR),warn:Q("warn",XP.DiagLogLevel.WARN),info:Q("info",XP.DiagLogLevel.INFO),debug:Q("debug",XP.DiagLogLevel.DEBUG),verbose:Q("verbose",XP.DiagLogLevel.VERBOSE)}}hR2.createLogLevelDiagLogger=nr4});
var ul2=E((Oh5,gl2)=>{gl2.exports=AM;var hl2=NT1();(AM.prototype=Object.create(hl2.prototype)).constructor=AM;var l_=eL();function AM(){hl2.call(this)}AM._configure=function(){AM.alloc=l_._Buffer_allocUnsafe,AM.writeBytesBuffer=l_.Buffer&&l_.Buffer.prototype instanceof Uint8Array&&l_.Buffer.prototype.set.name==="set"?function A(B,Q,D){Q.set(B,D)}:function A(B,Q,D){if(B.copy)B.copy(Q,D,0,B.length);else for(var Z=0;Z<B.length;)Q[D++]=B[Z++]}};AM.prototype.bytes=function A(B){if(l_.isString(B))B=l_._Buffer_from(B,"base64");var Q=B.length>>>0;if(this.uint32(Q),Q)this._push(AM.writeBytesBuffer,Q,B);return this};function ZI6(A,B,Q){if(A.length<40)l_.utf8.write(A,B,Q);else if(B.utf8Write)B.utf8Write(A,Q);else B.write(A,Q)}AM.prototype.string=function A(B){var Q=l_.Buffer.byteLength(B);if(this.uint32(Q),Q)this._push(ZI6,Q,B);return this};AM._configure()});
var vO1=E((OO2)=>{Object.defineProperty(OO2,"__esModule",{value:!0});OO2.INVALID_SPAN_CONTEXT=OO2.INVALID_TRACEID=OO2.INVALID_SPANID=void 0;var Lo4=pF0();OO2.INVALID_SPANID="0000000000000000";OO2.INVALID_TRACEID="00000000000000000000000000000000";OO2.INVALID_SPAN_CONTEXT={traceId:OO2.INVALID_TRACEID,spanId:OO2.INVALID_SPANID,traceFlags:Lo4.TraceFlags.NONE}});
var vT2=E((_T2)=>{Object.defineProperty(_T2,"__esModule",{value:!0});_T2.metrics=void 0;var wt4=kT2();_T2.metrics=wt4.MetricsAPI.getInstance()});
var vY0=E((Eh5,Xl2)=>{Xl2.exports=oF6;function oF6(A,B){var Q=new Array(arguments.length-1),D=0,Z=2,G=!0;while(Z<arguments.length)Q[D++]=arguments[Z++];return new Promise(function F(I,Y){Q[D]=function W(J){if(G)if(G=!1,J)Y(J);else{var X=new Array(arguments.length-1),V=0;while(V<X.length)X[V++]=arguments[V];I.apply(null,X)}};try{A.apply(B||null,Q)}catch(W){if(G)G=!1,Y(W)}})}});
var vd2=E((_d2)=>{Object.defineProperty(_d2,"__esModule",{value:!0});_d2.BatchObservableResultImpl=_d2.ObservableResultImpl=void 0;var go=VQ(),jd2=I31(),MG6=VT1();class yd2{_instrumentName;_valueType;_buffer=new jd2.AttributeHashMap;constructor(A,B){this._instrumentName=A,this._valueType=B}observe(A,B={}){if(typeof A!=="number"){go.diag.warn(`non-number value provided to metric ${this._instrumentName}: ${A}`);return}if(this._valueType===go.ValueType.INT&&!Number.isInteger(A)){if(go.diag.warn(`INT value type cannot accept a floating-point value for ${this._instrumentName}, ignoring the fractional digits.`),A=Math.trunc(A),!Number.isInteger(A))return}this._buffer.set(B,A)}}_d2.ObservableResultImpl=yd2;class kd2{_buffer=new Map;observe(A,B,Q={}){if(!MG6.isObservableInstrument(A))return;let D=this._buffer.get(A);if(D==null)D=new jd2.AttributeHashMap,this._buffer.set(A,D);if(typeof B!=="number"){go.diag.warn(`non-number value provided to metric ${A._descriptor.name}: ${B}`);return}if(A._descriptor.valueType===go.ValueType.INT&&!Number.isInteger(B)){if(go.diag.warn(`INT value type cannot accept a floating-point value for ${A._descriptor.name}, ignoring the fractional digits.`),B=Math.trunc(B),!Number.isInteger(B))return}D.set(Q,B)}}_d2.BatchObservableResultImpl=kd2});
var vg2=E((_g2)=>{Object.defineProperty(_g2,"__esModule",{value:!0});_g2.LastValueAggregator=_g2.LastValueAccumulation=void 0;var JD6=So(),e51=x3(),XD6=f_();class A31{startTime;_current;sampleTime;constructor(A,B=0,Q=[0,0]){this.startTime=A,this._current=B,this.sampleTime=Q}record(A){this._current=A,this.sampleTime=e51.millisToHrTime(Date.now())}setStartTime(A){this.startTime=A}toPointValue(){return this._current}}_g2.LastValueAccumulation=A31;class kg2{kind=JD6.AggregatorKind.LAST_VALUE;createAccumulation(A){return new A31(A)}merge(A,B){let Q=e51.hrTimeToMicroseconds(B.sampleTime)>=e51.hrTimeToMicroseconds(A.sampleTime)?B:A;return new A31(A.startTime,Q.toPointValue(),Q.sampleTime)}diff(A,B){let Q=e51.hrTimeToMicroseconds(B.sampleTime)>=e51.hrTimeToMicroseconds(A.sampleTime)?B:A;return new A31(B.startTime,Q.toPointValue(),Q.sampleTime)}toMetricData(A,B,Q,D){return{descriptor:A,aggregationTemporality:B,dataPointType:XD6.DataPointType.GAUGE,dataPoints:Q.map(([Z,G])=>{return{attributes:Z,startTime:G.startTime,endTime:D,value:G.toPointValue()}})}}}_g2.LastValueAggregator=kg2});
var vp2=E((_p2)=>{Object.defineProperty(_p2,"__esModule",{value:!0});_p2.ProtobufMetricsSerializer=void 0;var kp2=RT1(),cI6=GW0(),lI6=kp2.opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceResponse,pI6=kp2.opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceRequest;_p2.ProtobufMetricsSerializer={serializeRequest:(A)=>{let B=cI6.createExportMetricsServiceRequest([A]);return pI6.encode(B).finish()},deserializeResponse:(A)=>{return lI6.decode(A)}}});
var wT2=E((ET2)=>{Object.defineProperty(ET2,"__esModule",{value:!0});ET2.createTraceState=void 0;var Vt4=zT2();function Ct4(A){return new Vt4.TraceStateImpl(A)}ET2.createTraceState=Ct4});
var wY0=E((Bd2)=>{Object.defineProperty(Bd2,"__esModule",{value:!0});Bd2.MetricStorage=void 0;var GG6=F31();class Ad2{_instrumentDescriptor;constructor(A){this._instrumentDescriptor=A}getInstrumentDescriptor(){return this._instrumentDescriptor}updateDescription(A){this._instrumentDescriptor=GG6.createInstrumentDescriptor(this._instrumentDescriptor.name,this._instrumentDescriptor.type,{description:A,valueType:this._instrumentDescriptor.valueType,unit:this._instrumentDescriptor.unit,advice:this._instrumentDescriptor.advice})}}Bd2.MetricStorage=Ad2});
var x3=E((AQ)=>{Object.defineProperty(AQ,"__esModule",{value:!0});AQ.internal=AQ.diagLogLevelFromString=AQ.BindOnceFuture=AQ.urlMatches=AQ.isUrlIgnored=AQ.callWithTimeout=AQ.TimeoutError=AQ.merge=AQ.TraceState=AQ.unsuppressTracing=AQ.suppressTracing=AQ.isTracingSuppressed=AQ.setRPCMetadata=AQ.getRPCMetadata=AQ.deleteRPCMetadata=AQ.RPCType=AQ.parseTraceParent=AQ.W3CTraceContextPropagator=AQ.TRACE_STATE_HEADER=AQ.TRACE_PARENT_HEADER=AQ.CompositePropagator=AQ.unrefTimer=AQ.otperformance=AQ.getStringListFromEnv=AQ.getNumberFromEnv=AQ.getBooleanFromEnv=AQ.getStringFromEnv=AQ._globalThis=AQ.SDK_INFO=AQ.parseKeyPairsIntoRecord=AQ.ExportResultCode=AQ.timeInputToHrTime=AQ.millisToHrTime=AQ.isTimeInputHrTime=AQ.isTimeInput=AQ.hrTimeToTimeStamp=AQ.hrTimeToNanoseconds=AQ.hrTimeToMilliseconds=AQ.hrTimeToMicroseconds=AQ.hrTimeDuration=AQ.hrTime=AQ.getTimeOrigin=AQ.addHrTimes=AQ.loggingErrorHandler=AQ.setGlobalErrorHandler=AQ.globalErrorHandler=AQ.sanitizeAttributes=AQ.isAttributeValue=AQ.AnchoredClock=AQ.W3CBaggagePropagator=void 0;var e76=cS2();Object.defineProperty(AQ,"W3CBaggagePropagator",{enumerable:!0,get:function(){return e76.W3CBaggagePropagator}});var AD6=nS2();Object.defineProperty(AQ,"AnchoredClock",{enumerable:!0,get:function(){return AD6.AnchoredClock}});var Tg2=Aj2();Object.defineProperty(AQ,"isAttributeValue",{enumerable:!0,get:function(){return Tg2.isAttributeValue}});Object.defineProperty(AQ,"sanitizeAttributes",{enumerable:!0,get:function(){return Tg2.sanitizeAttributes}});var Pg2=Fj2();Object.defineProperty(AQ,"globalErrorHandler",{enumerable:!0,get:function(){return Pg2.globalErrorHandler}});Object.defineProperty(AQ,"setGlobalErrorHandler",{enumerable:!0,get:function(){return Pg2.setGlobalErrorHandler}});var BD6=uI0();Object.defineProperty(AQ,"loggingErrorHandler",{enumerable:!0,get:function(){return BD6.loggingErrorHandler}});var CE=Uh2();Object.defineProperty(AQ,"addHrTimes",{enumerable:!0,get:function(){return CE.addHrTimes}});Object.defineProperty(AQ,"getTimeOrigin",{enumerable:!0,get:function(){return CE.getTimeOrigin}});Object.defineProperty(AQ,"hrTime",{enumerable:!0,get:function(){return CE.hrTime}});Object.defineProperty(AQ,"hrTimeDuration",{enumerable:!0,get:function(){return CE.hrTimeDuration}});Object.defineProperty(AQ,"hrTimeToMicroseconds",{enumerable:!0,get:function(){return CE.hrTimeToMicroseconds}});Object.defineProperty(AQ,"hrTimeToMilliseconds",{enumerable:!0,get:function(){return CE.hrTimeToMilliseconds}});Object.defineProperty(AQ,"hrTimeToNanoseconds",{enumerable:!0,get:function(){return CE.hrTimeToNanoseconds}});Object.defineProperty(AQ,"hrTimeToTimeStamp",{enumerable:!0,get:function(){return CE.hrTimeToTimeStamp}});Object.defineProperty(AQ,"isTimeInput",{enumerable:!0,get:function(){return CE.isTimeInput}});Object.defineProperty(AQ,"isTimeInputHrTime",{enumerable:!0,get:function(){return CE.isTimeInputHrTime}});Object.defineProperty(AQ,"millisToHrTime",{enumerable:!0,get:function(){return CE.millisToHrTime}});Object.defineProperty(AQ,"timeInputToHrTime",{enumerable:!0,get:function(){return CE.timeInputToHrTime}});var QD6=$h2();Object.defineProperty(AQ,"ExportResultCode",{enumerable:!0,get:function(){return QD6.ExportResultCode}});var DD6=fI0();Object.defineProperty(AQ,"parseKeyPairsIntoRecord",{enumerable:!0,get:function(){return DD6.parseKeyPairsIntoRecord}});var g_=dI0();Object.defineProperty(AQ,"SDK_INFO",{enumerable:!0,get:function(){return g_.SDK_INFO}});Object.defineProperty(AQ,"_globalThis",{enumerable:!0,get:function(){return g_._globalThis}});Object.defineProperty(AQ,"getStringFromEnv",{enumerable:!0,get:function(){return g_.getStringFromEnv}});Object.defineProperty(AQ,"getBooleanFromEnv",{enumerable:!0,get:function(){return g_.getBooleanFromEnv}});Object.defineProperty(AQ,"getNumberFromEnv",{enumerable:!0,get:function(){return g_.getNumberFromEnv}});Object.defineProperty(AQ,"getStringListFromEnv",{enumerable:!0,get:function(){return g_.getStringListFromEnv}});Object.defineProperty(AQ,"otperformance",{enumerable:!0,get:function(){return g_.otperformance}});Object.defineProperty(AQ,"unrefTimer",{enumerable:!0,get:function(){return g_.unrefTimer}});var ZD6=Rh2();Object.defineProperty(AQ,"CompositePropagator",{enumerable:!0,get:function(){return ZD6.CompositePropagator}});var BT1=gh2();Object.defineProperty(AQ,"TRACE_PARENT_HEADER",{enumerable:!0,get:function(){return BT1.TRACE_PARENT_HEADER}});Object.defineProperty(AQ,"TRACE_STATE_HEADER",{enumerable:!0,get:function(){return BT1.TRACE_STATE_HEADER}});Object.defineProperty(AQ,"W3CTraceContextPropagator",{enumerable:!0,get:function(){return BT1.W3CTraceContextPropagator}});Object.defineProperty(AQ,"parseTraceParent",{enumerable:!0,get:function(){return BT1.parseTraceParent}});var QT1=ch2();Object.defineProperty(AQ,"RPCType",{enumerable:!0,get:function(){return QT1.RPCType}});Object.defineProperty(AQ,"deleteRPCMetadata",{enumerable:!0,get:function(){return QT1.deleteRPCMetadata}});Object.defineProperty(AQ,"getRPCMetadata",{enumerable:!0,get:function(){return QT1.getRPCMetadata}});Object.defineProperty(AQ,"setRPCMetadata",{enumerable:!0,get:function(){return QT1.setRPCMetadata}});var tI0=s51();Object.defineProperty(AQ,"isTracingSuppressed",{enumerable:!0,get:function(){return tI0.isTracingSuppressed}});Object.defineProperty(AQ,"suppressTracing",{enumerable:!0,get:function(){return tI0.suppressTracing}});Object.defineProperty(AQ,"unsuppressTracing",{enumerable:!0,get:function(){return tI0.unsuppressTracing}});var GD6=sI0();Object.defineProperty(AQ,"TraceState",{enumerable:!0,get:function(){return GD6.TraceState}});var FD6=Dg2();Object.defineProperty(AQ,"merge",{enumerable:!0,get:function(){return FD6.merge}});var Sg2=Fg2();Object.defineProperty(AQ,"TimeoutError",{enumerable:!0,get:function(){return Sg2.TimeoutError}});Object.defineProperty(AQ,"callWithTimeout",{enumerable:!0,get:function(){return Sg2.callWithTimeout}});var jg2=Jg2();Object.defineProperty(AQ,"isUrlIgnored",{enumerable:!0,get:function(){return jg2.isUrlIgnored}});Object.defineProperty(AQ,"urlMatches",{enumerable:!0,get:function(){return jg2.urlMatches}});var ID6=Ug2();Object.defineProperty(AQ,"BindOnceFuture",{enumerable:!0,get:function(){return ID6.BindOnceFuture}});var YD6=Ng2();Object.defineProperty(AQ,"diagLogLevelFromString",{enumerable:!0,get:function(){return YD6.diagLogLevelFromString}});var WD6=Og2();AQ.internal={_export:WD6._export}});
var xY0=E((Wl2)=>{Object.defineProperty(Wl2,"__esModule",{value:!0});Wl2.OTLPMetricExporterBase=Wl2.LowMemoryTemporalitySelector=Wl2.DeltaTemporalitySelector=Wl2.CumulativeTemporalitySelector=void 0;var mF6=x3(),tG=m_(),Il2=PY0(),dF6=xu(),cF6=VQ(),lF6=()=>tG.AggregationTemporality.CUMULATIVE;Wl2.CumulativeTemporalitySelector=lF6;var pF6=(A)=>{switch(A){case tG.InstrumentType.COUNTER:case tG.InstrumentType.OBSERVABLE_COUNTER:case tG.InstrumentType.GAUGE:case tG.InstrumentType.HISTOGRAM:case tG.InstrumentType.OBSERVABLE_GAUGE:return tG.AggregationTemporality.DELTA;case tG.InstrumentType.UP_DOWN_COUNTER:case tG.InstrumentType.OBSERVABLE_UP_DOWN_COUNTER:return tG.AggregationTemporality.CUMULATIVE}};Wl2.DeltaTemporalitySelector=pF6;var iF6=(A)=>{switch(A){case tG.InstrumentType.COUNTER:case tG.InstrumentType.HISTOGRAM:return tG.AggregationTemporality.DELTA;case tG.InstrumentType.GAUGE:case tG.InstrumentType.UP_DOWN_COUNTER:case tG.InstrumentType.OBSERVABLE_UP_DOWN_COUNTER:case tG.InstrumentType.OBSERVABLE_COUNTER:case tG.InstrumentType.OBSERVABLE_GAUGE:return tG.AggregationTemporality.CUMULATIVE}};Wl2.LowMemoryTemporalitySelector=iF6;function nF6(){let A=(mF6.getStringFromEnv("OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE")??"cumulative").toLowerCase();if(A==="cumulative")return Wl2.CumulativeTemporalitySelector;if(A==="delta")return Wl2.DeltaTemporalitySelector;if(A==="lowmemory")return Wl2.LowMemoryTemporalitySelector;return cF6.diag.warn(`OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE is set to '${A}', but only 'cumulative' and 'delta' are allowed. Using default ('cumulative') instead.`),Wl2.CumulativeTemporalitySelector}function aF6(A){if(A!=null){if(A===Il2.AggregationTemporalityPreference.DELTA)return Wl2.DeltaTemporalitySelector;else if(A===Il2.AggregationTemporalityPreference.LOWMEMORY)return Wl2.LowMemoryTemporalitySelector;return Wl2.CumulativeTemporalitySelector}return nF6()}var sF6=Object.freeze({type:tG.AggregationType.DEFAULT});function rF6(A){return A?.aggregationPreference??(()=>sF6)}class Yl2 extends dF6.OTLPExporterBase{_aggregationTemporalitySelector;_aggregationSelector;constructor(A,B){super(A);this._aggregationSelector=rF6(B),this._aggregationTemporalitySelector=aF6(B?.temporalityPreference)}selectAggregation(A){return this._aggregationSelector(A)}selectAggregationTemporality(A){return this._aggregationTemporalitySelector(A)}}Wl2.OTLPMetricExporterBase=Yl2});
var xu=E((d_)=>{Object.defineProperty(d_,"__esModule",{value:!0});d_.createOtlpNetworkExportDelegate=d_.CompressionAlgorithm=d_.getSharedConfigurationDefaults=d_.mergeOtlpSharedConfigurationWithDefaults=d_.OTLPExporterError=d_.OTLPExporterBase=void 0;var bF6=hc2();Object.defineProperty(d_,"OTLPExporterBase",{enumerable:!0,get:function(){return bF6.OTLPExporterBase}});var fF6=ET1();Object.defineProperty(d_,"OTLPExporterError",{enumerable:!0,get:function(){return fF6.OTLPExporterError}});var Fl2=J31();Object.defineProperty(d_,"mergeOtlpSharedConfigurationWithDefaults",{enumerable:!0,get:function(){return Fl2.mergeOtlpSharedConfigurationWithDefaults}});Object.defineProperty(d_,"getSharedConfigurationDefaults",{enumerable:!0,get:function(){return Fl2.getSharedConfigurationDefaults}});var hF6=ic2();Object.defineProperty(d_,"CompressionAlgorithm",{enumerable:!0,get:function(){return hF6.CompressionAlgorithm}});var gF6=Gl2();Object.defineProperty(d_,"createOtlpNetworkExportDelegate",{enumerable:!0,get:function(){return gF6.createOtlpNetworkExportDelegate}})});
var yT1=E((i_)=>{Object.defineProperty(i_,"__esModule",{value:!0});i_.OTLPMetricExporterBase=i_.LowMemoryTemporalitySelector=i_.DeltaTemporalitySelector=i_.CumulativeTemporalitySelector=i_.AggregationTemporalityPreference=i_.OTLPMetricExporter=void 0;var TW6=Bn2();Object.defineProperty(i_,"OTLPMetricExporter",{enumerable:!0,get:function(){return TW6.OTLPMetricExporter}});var PW6=PY0();Object.defineProperty(i_,"AggregationTemporalityPreference",{enumerable:!0,get:function(){return PW6.AggregationTemporalityPreference}});var jT1=xY0();Object.defineProperty(i_,"CumulativeTemporalitySelector",{enumerable:!0,get:function(){return jT1.CumulativeTemporalitySelector}});Object.defineProperty(i_,"DeltaTemporalitySelector",{enumerable:!0,get:function(){return jT1.DeltaTemporalitySelector}});Object.defineProperty(i_,"LowMemoryTemporalitySelector",{enumerable:!0,get:function(){return jT1.LowMemoryTemporalitySelector}});Object.defineProperty(i_,"OTLPMetricExporterBase",{enumerable:!0,get:function(){return jT1.OTLPMetricExporterBase}})});
var yY0=E((Bl2)=>{Object.defineProperty(Bl2,"__esModule",{value:!0});Bl2.createOtlpExportDelegate=void 0;var _u=x3(),ec2=ET1(),jF6=tc2(),yF6=VQ();class Al2{_transport;_serializer;_responseHandler;_promiseQueue;_timeout;_diagLogger;constructor(A,B,Q,D,Z){this._transport=A,this._serializer=B,this._responseHandler=Q,this._promiseQueue=D,this._timeout=Z,this._diagLogger=yF6.diag.createComponentLogger({namespace:"OTLPExportDelegate"})}export(A,B){if(this._diagLogger.debug("items to be sent",A),this._promiseQueue.hasReachedLimit()){B({code:_u.ExportResultCode.FAILED,error:new Error("Concurrent export limit reached")});return}let Q=this._serializer.serializeRequest(A);if(Q==null){B({code:_u.ExportResultCode.FAILED,error:new Error("Nothing to send")});return}this._promiseQueue.pushPromise(this._transport.send(Q,this._timeout).then((D)=>{if(D.status==="success"){if(D.data!=null)try{this._responseHandler.handleResponse(this._serializer.deserializeResponse(D.data))}catch(Z){this._diagLogger.warn("Export succeeded but could not deserialize response - is the response specification compliant?",Z,D.data)}B({code:_u.ExportResultCode.SUCCESS});return}else if(D.status==="failure"&&D.error){B({code:_u.ExportResultCode.FAILED,error:D.error});return}else if(D.status==="retryable")B({code:_u.ExportResultCode.FAILED,error:new ec2.OTLPExporterError("Export failed with retryable status")});else B({code:_u.ExportResultCode.FAILED,error:new ec2.OTLPExporterError("Export failed with unknown error")})},(D)=>B({code:_u.ExportResultCode.FAILED,error:D})))}forceFlush(){return this._promiseQueue.awaitAll()}async shutdown(){this._diagLogger.debug("shutdown started"),await this.forceFlush(),this._transport.shutdown()}}function kF6(A,B){return new Al2(A.transport,A.serializer,jF6.createLoggingPartialSuccessResponseHandler(),A.promiseHandler,B.timeout)}Bl2.createOtlpExportDelegate=kF6});
var yc2=E((Sc2)=>{Object.defineProperty(Sc2,"__esModule",{value:!0});Sc2.MeterProvider=void 0;var zT1=VQ(),GF6=XT1(),FF6=Gc2(),IF6=Wc2(),YF6=Tc2();class Pc2{_sharedState;_shutdown=!1;constructor(A){if(this._sharedState=new FF6.MeterProviderSharedState(A?.resource??GF6.defaultResource()),A?.views!=null&&A.views.length>0)for(let B of A.views)this._sharedState.viewRegistry.addView(new YF6.View(B));if(A?.readers!=null&&A.readers.length>0)for(let B of A.readers){let Q=new IF6.MetricCollector(this._sharedState,B);B.setMetricProducer(Q),this._sharedState.metricCollectors.push(Q)}}getMeter(A,B="",Q={}){if(this._shutdown)return zT1.diag.warn("A shutdown MeterProvider cannot provide a Meter"),zT1.createNoopMeter();return this._sharedState.getMeterSharedState({name:A,version:B,schemaUrl:Q.schemaUrl}).meter}async shutdown(A){if(this._shutdown){zT1.diag.warn("shutdown may only be called once per MeterProvider");return}this._shutdown=!0,await Promise.all(this._sharedState.metricCollectors.map((B)=>{return B.shutdown(A)}))}async forceFlush(A){if(this._shutdown){zT1.diag.warn("invalid attempt to force flush after MeterProvider shutdown");return}await Promise.all(this._sharedState.metricCollectors.map((B)=>{return B.forceFlush(A)}))}}Sc2.MeterProvider=Pc2});
var yl2=E((Lh5,jl2)=>{jl2.exports=wI;var X31=eL();function wI(A,B){this.lo=A>>>0,this.hi=B>>>0}var vu=wI.zero=new wI(0,0);vu.toNumber=function(){return 0};vu.zzEncode=vu.zzDecode=function(){return this};vu.length=function(){return 1};var AI6=wI.zeroHash="\x00\x00\x00\x00\x00\x00\x00\x00";wI.fromNumber=function A(B){if(B===0)return vu;var Q=B<0;if(Q)B=-B;var D=B>>>0,Z=(B-D)/**********>>>0;if(Q){if(Z=~Z>>>0,D=~D>>>0,++D>**********){if(D=0,++Z>**********)Z=0}}return new wI(D,Z)};wI.from=function A(B){if(typeof B==="number")return wI.fromNumber(B);if(X31.isString(B))if(X31.Long)B=X31.Long.fromString(B);else return wI.fromNumber(parseInt(B,10));return B.low||B.high?new wI(B.low>>>0,B.high>>>0):vu};wI.prototype.toNumber=function A(B){if(!B&&this.hi>>>31){var Q=~this.lo+1>>>0,D=~this.hi>>>0;if(!Q)D=D+1>>>0;return-(Q+D***********)}return this.lo+this.hi***********};wI.prototype.toLong=function A(B){return X31.Long?new X31.Long(this.lo|0,this.hi|0,Boolean(B)):{low:this.lo|0,high:this.hi|0,unsigned:Boolean(B)}};var c_=String.prototype.charCodeAt;wI.fromHash=function A(B){if(B===AI6)return vu;return new wI((c_.call(B,0)|c_.call(B,1)<<8|c_.call(B,2)<<16|c_.call(B,3)<<24)>>>0,(c_.call(B,4)|c_.call(B,5)<<8|c_.call(B,6)<<16|c_.call(B,7)<<24)>>>0)};wI.prototype.toHash=function A(){return String.fromCharCode(this.lo&255,this.lo>>>8&255,this.lo>>>16&255,this.lo>>>24,this.hi&255,this.hi>>>8&255,this.hi>>>16&255,this.hi>>>24)};wI.prototype.zzEncode=function A(){var B=this.hi>>31;return this.hi=((this.hi<<1|this.lo>>>31)^B)>>>0,this.lo=(this.lo<<1^B)>>>0,this};wI.prototype.zzDecode=function A(){var B=-(this.lo&1);return this.lo=((this.lo>>>1|this.hi<<31)^B)>>>0,this.hi=(this.hi>>>1^B)>>>0,this};wI.prototype.length=function A(){var B=this.lo,Q=(this.lo>>>28|this.hi<<4)>>>0,D=this.hi>>>24;return D===0?Q===0?B<16384?B<128?1:2:B<2097152?3:4:Q<16384?Q<128?5:6:Q<2097152?7:8:D<128?9:10}});
var ym2=E((Sm2)=>{Object.defineProperty(Sm2,"__esModule",{value:!0});Sm2.noopDetector=Sm2.NoopDetector=void 0;class EY0{detect(){return{attributes:{}}}}Sm2.NoopDetector=EY0;Sm2.noopDetector=new EY0});
var zT2=E((KT2)=>{Object.defineProperty(KT2,"__esModule",{value:!0});KT2.TraceStateImpl=void 0;var JT2=WT2(),XT2=32,Xt4=512,VT2=",",CT2="=";class ZI0{constructor(A){if(this._internalState=new Map,A)this._parse(A)}set(A,B){let Q=this._clone();if(Q._internalState.has(A))Q._internalState.delete(A);return Q._internalState.set(A,B),Q}unset(A){let B=this._clone();return B._internalState.delete(A),B}get(A){return this._internalState.get(A)}serialize(){return this._keys().reduce((A,B)=>{return A.push(B+CT2+this.get(B)),A},[]).join(VT2)}_parse(A){if(A.length>Xt4)return;if(this._internalState=A.split(VT2).reverse().reduce((B,Q)=>{let D=Q.trim(),Z=D.indexOf(CT2);if(Z!==-1){let G=D.slice(0,Z),F=D.slice(Z+1,Q.length);if(JT2.validateKey(G)&&JT2.validateValue(F))B.set(G,F)}return B},new Map),this._internalState.size>XT2)this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,XT2))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let A=new ZI0;return A._internalState=new Map(this._internalState),A}}KT2.TraceStateImpl=ZI0});
var zu=E((yR2)=>{Object.defineProperty(yR2,"__esModule",{value:!0});yR2.unregisterGlobal=yR2.getGlobal=yR2.registerGlobal=void 0;var fr4=LR2(),Ro=RF0(),hr4=jR2(),gr4=Ro.VERSION.split(".")[0],u51=Symbol.for(`opentelemetry.js.api.${gr4}`),m51=fr4._globalThis;function ur4(A,B,Q,D=!1){var Z;let G=m51[u51]=(Z=m51[u51])!==null&&Z!==void 0?Z:{version:Ro.VERSION};if(!D&&G[A]){let F=new Error(`@opentelemetry/api: Attempted duplicate registration of API: ${A}`);return Q.error(F.stack||F.message),!1}if(G.version!==Ro.VERSION){let F=new Error(`@opentelemetry/api: Registration of version v${G.version} for ${A} does not match previously registered API v${Ro.VERSION}`);return Q.error(F.stack||F.message),!1}return G[A]=B,Q.debug(`@opentelemetry/api: Registered a global for ${A} v${Ro.VERSION}.`),!0}yR2.registerGlobal=ur4;function mr4(A){var B,Q;let D=(B=m51[u51])===null||B===void 0?void 0:B.version;if(!D||!hr4.isCompatible(D))return;return(Q=m51[u51])===null||Q===void 0?void 0:Q[A]}yR2.getGlobal=mr4;function dr4(A,B){B.debug(`@opentelemetry/api: Unregistering a global for ${A} v${Ro.VERSION}.`);let Q=m51[u51];if(Q)delete Q[A]}yR2.unregisterGlobal=dr4});

// Export all variables
module.exports = {
  $h2,
  $v2,
  AS2,
  Ai2,
  Aj2,
  Am2,
  An2,
  Bc2,
  Bn2,
  CP,
  DO2,
  DP2,
  DS2,
  DW0,
  DY0,
  Dg2,
  Di2,
  Dm2,
  ET1,
  Ec2,
  Ei2,
  El2,
  Eu,
  Eu2,
  F31,
  FP2,
  FT2,
  Fg2,
  Fj2,
  GW0,
  Gc2,
  Gh2,
  Gl2,
  Gm2,
  HO2,
  HS2,
  HT1,
  HY0,
  Hj2,
  Hl2,
  I31,
  IW0,
  Ii2,
  Ip2,
  J31,
  Jg2,
  Ji2,
  Jn2,
  KT1,
  Kd2,
  Kg2,
  LR2,
  LY0,
  Lp2,
  MT1,
  Ml2,
  Mp2,
  Mu2,
  NR2,
  NT1,
  NT2,
  NY0,
  Ng2,
  Nm2,
  OS2,
  OT1,
  Og2,
  Oi2,
  Om2,
  PF0,
  PT2,
  PY0,
  Ph2,
  Pm2,
  Pu2,
  Q31,
  QT2,
  QY0,
  Qh2,
  RF0,
  RT1,
  RT2,
  Rd2,
  Rh2,
  Ru2,
  Sd2,
  Si2,
  Sl2,
  So,
  TT1,
  Tc2,
  Tl2,
  Tm2,
  Ug2,
  Uh2,
  Uj2,
  Um2,
  VQ,
  VT1,
  VW0,
  VY0,
  Vh2,
  Vj2,
  Vm2,
  Vu2,
  WT2,
  Wc2,
  Wn2,
  XT1,
  Xh2,
  YT1,
  YY0,
  Yh2,
  ZT2,
  Zi2,
  Zn2,
  _I0,
  _u2,
  aF0,
  aT2,
  ai2,
  bI0,
  bO1,
  bR2,
  bm2,
  bp2,
  c51,
  cO1,
  cS2,
  cT2,
  ch2,
  co,
  cu2,
  dI0,
  eF0,
  eL,
  ei2,
  em2,
  ep2,
  fI0,
  fO1,
  fY0,
  f_,
  fi2,
  fu,
  gT2,
  gg2,
  gh2,
  hF0,
  hc2,
  hu2,
  iR2,
  ic2,
  ip2,
  iu2,
  jR2,
  jY0,
  kO1,
  kS2,
  kT2,
  km2,
  lO1,
  lg2,
  li2,
  mI0,
  mO1,
  m_,
  md2,
  mi2,
  nS2,
  oF0,
  oO2,
  oT2,
  oY0,
  of2,
  og2,
  ol2,
  p51,
  pF0,
  pd2,
  pp2,
  qO2,
  qR2,
  qS2,
  qc2,
  qd2,
  qi2,
  qj2,
  qu2,
  qv2,
  rP2,
  rY0,
  rf2,
  rh2,
  rp2,
  ru2,
  s51,
  sI0,
  sR2,
  sY0,
  sl2,
  sp2,
  tF0,
  tc2,
  tw,
  uF0,
  uI0,
  uR2,
  ul2,
  vO1,
  vT2,
  vY0,
  vd2,
  vg2,
  vp2,
  wT2,
  wY0,
  x3,
  xY0,
  xu,
  yT1,
  yY0,
  yc2,
  yl2,
  ym2,
  zT2,
  zu
};
