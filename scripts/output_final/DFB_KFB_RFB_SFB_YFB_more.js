// Package extracted with entry point: sFB
// Contains 8 variables: DFB, KFB, RFB, SFB, YFB, gD1, iH0, sFB

var DFB=E((G03,QFB)=>{var{defineProperty:Ak1,getOwnPropertyDescriptor:ab6,getOwnPropertyNames:sb6}=Object,rb6=Object.prototype.hasOwnProperty,Bk1=(A,B)=>Ak1(A,"name",{value:B,configurable:!0}),ob6=(A,B)=>{for(var Q in B)Ak1(A,Q,{get:B[Q],enumerable:!0})},tb6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of sb6(B))if(!rb6.call(A,Z)&&Z!==Q)Ak1(A,Z,{get:()=>B[Z],enumerable:!(D=ab6(B,Z))||D.enumerable})}return A},eb6=(A)=>tb6(Ak1({},"__esModule",{value:!0}),A),aGB={};ob6(aGB,{AlgorithmId:()=>tGB,EndpointURLScheme:()=>oGB,FieldPosition:()=>eGB,HttpApiKeyAuthLocation:()=>rGB,HttpAuthLocation:()=>sGB,IniSectionType:()=>AFB,RequestHandlerProtocol:()=>BFB,SMITHY_CONTEXT_KEY:()=>Zf6,getDefaultClientConfiguration:()=>Qf6,resolveDefaultRuntimeConfig:()=>Df6});QFB.exports=eb6(aGB);var sGB=((A)=>{return A.HEADER="header",A.QUERY="query",A})(sGB||{}),rGB=((A)=>{return A.HEADER="header",A.QUERY="query",A})(rGB||{}),oGB=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(oGB||{}),tGB=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(tGB||{}),Af6=Bk1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{_checksumAlgorithms:B,addChecksumAlgorithm(Q){this._checksumAlgorithms.push(Q)},checksumAlgorithms(){return this._checksumAlgorithms}}},"getChecksumConfiguration"),Bf6=Bk1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),Qf6=Bk1((A)=>{return{...Af6(A)}},"getDefaultClientConfiguration"),Df6=Bk1((A)=>{return{...Bf6(A)}},"resolveDefaultRuntimeConfig"),eGB=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(eGB||{}),Zf6="__smithy_context",AFB=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(AFB||{}),BFB=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(BFB||{})});
var KFB=E((Y03,CFB)=>{var{defineProperty:Zk1,getOwnPropertyDescriptor:qf6,getOwnPropertyNames:Nf6}=Object,Lf6=Object.prototype.hasOwnProperty,XFB=(A,B)=>Zk1(A,"name",{value:B,configurable:!0}),Mf6=(A,B)=>{for(var Q in B)Zk1(A,Q,{get:B[Q],enumerable:!0})},Rf6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Nf6(B))if(!Lf6.call(A,Z)&&Z!==Q)Zk1(A,Z,{get:()=>B[Z],enumerable:!(D=qf6(B,Z))||D.enumerable})}return A},Of6=(A)=>Rf6(Zk1({},"__esModule",{value:!0}),A),VFB={};Mf6(VFB,{fromArrayBuffer:()=>Pf6,fromString:()=>Sf6});CFB.exports=Of6(VFB);var Tf6=iH0(),nH0=J1("buffer"),Pf6=XFB((A,B=0,Q=A.byteLength-B)=>{if(!Tf6.isArrayBuffer(A))throw new TypeError(`The "input" argument must be ArrayBuffer. Received type ${typeof A} (${A})`);return nH0.Buffer.from(A,B,Q)},"fromArrayBuffer"),Sf6=XFB((A,B)=>{if(typeof A!=="string")throw new TypeError(`The "input" argument must be of type string. Received type ${typeof A} (${A})`);return B?nH0.Buffer.from(A,B):nH0.Buffer.from(A)},"fromString")});
var RFB=E((J03,MFB)=>{var{defineProperty:Fk1,getOwnPropertyDescriptor:hf6,getOwnPropertyNames:gf6}=Object,uf6=Object.prototype.hasOwnProperty,wFB=(A,B)=>Fk1(A,"name",{value:B,configurable:!0}),mf6=(A,B)=>{for(var Q in B)Fk1(A,Q,{get:B[Q],enumerable:!0})},df6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of gf6(B))if(!uf6.call(A,Z)&&Z!==Q)Fk1(A,Z,{get:()=>B[Z],enumerable:!(D=hf6(B,Z))||D.enumerable})}return A},cf6=(A)=>df6(Fk1({},"__esModule",{value:!0}),A),$FB={};mf6($FB,{fromHex:()=>NFB,toHex:()=>LFB});MFB.exports=cf6($FB);var qFB={},sH0={};for(let A=0;A<256;A++){let B=A.toString(16).toLowerCase();if(B.length===1)B=`0${B}`;qFB[A]=B,sH0[B]=A}function NFB(A){if(A.length%2!==0)throw new Error("Hex encoded strings must have an even number length");let B=new Uint8Array(A.length/2);for(let Q=0;Q<A.length;Q+=2){let D=A.slice(Q,Q+2).toLowerCase();if(D in sH0)B[Q/2]=sH0[D];else throw new Error(`Cannot decode unrecognized sequence ${D} as hexadecimal`)}return B}wFB(NFB,"fromHex");function LFB(A){let B="";for(let Q=0;Q<A.byteLength;Q++)B+=qFB[A[Q]];return B}wFB(LFB,"toHex")});
var SFB=E((X03,PFB)=>{var{defineProperty:Ik1,getOwnPropertyDescriptor:lf6,getOwnPropertyNames:pf6}=Object,if6=Object.prototype.hasOwnProperty,rH0=(A,B)=>Ik1(A,"name",{value:B,configurable:!0}),nf6=(A,B)=>{for(var Q in B)Ik1(A,Q,{get:B[Q],enumerable:!0})},af6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of pf6(B))if(!if6.call(A,Z)&&Z!==Q)Ik1(A,Z,{get:()=>B[Z],enumerable:!(D=lf6(B,Z))||D.enumerable})}return A},sf6=(A)=>af6(Ik1({},"__esModule",{value:!0}),A),OFB={};nf6(OFB,{escapeUri:()=>TFB,escapeUriPath:()=>of6});PFB.exports=sf6(OFB);var TFB=rH0((A)=>encodeURIComponent(A).replace(/[!'()*]/g,rf6),"escapeUri"),rf6=rH0((A)=>`%${A.charCodeAt(0).toString(16).toUpperCase()}`,"hexEncode"),of6=rH0((A)=>A.split("/").map(TFB).join("/"),"escapeUriPath")});
var YFB=E((F03,IFB)=>{var{defineProperty:Qk1,getOwnPropertyDescriptor:Gf6,getOwnPropertyNames:Ff6}=Object,If6=Object.prototype.hasOwnProperty,GFB=(A,B)=>Qk1(A,"name",{value:B,configurable:!0}),Yf6=(A,B)=>{for(var Q in B)Qk1(A,Q,{get:B[Q],enumerable:!0})},Wf6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Ff6(B))if(!If6.call(A,Z)&&Z!==Q)Qk1(A,Z,{get:()=>B[Z],enumerable:!(D=Gf6(B,Z))||D.enumerable})}return A},Jf6=(A)=>Wf6(Qk1({},"__esModule",{value:!0}),A),FFB={};Yf6(FFB,{getSmithyContext:()=>Xf6,normalizeProvider:()=>Vf6});IFB.exports=Jf6(FFB);var ZFB=DFB(),Xf6=GFB((A)=>A[ZFB.SMITHY_CONTEXT_KEY]||(A[ZFB.SMITHY_CONTEXT_KEY]={}),"getSmithyContext"),Vf6=GFB((A)=>{if(typeof A==="function")return A;let B=Promise.resolve(A);return()=>B},"normalizeProvider")});
var gD1=E((W03,UFB)=>{var{defineProperty:Gk1,getOwnPropertyDescriptor:jf6,getOwnPropertyNames:yf6}=Object,kf6=Object.prototype.hasOwnProperty,aH0=(A,B)=>Gk1(A,"name",{value:B,configurable:!0}),_f6=(A,B)=>{for(var Q in B)Gk1(A,Q,{get:B[Q],enumerable:!0})},xf6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of yf6(B))if(!kf6.call(A,Z)&&Z!==Q)Gk1(A,Z,{get:()=>B[Z],enumerable:!(D=jf6(B,Z))||D.enumerable})}return A},vf6=(A)=>xf6(Gk1({},"__esModule",{value:!0}),A),HFB={};_f6(HFB,{fromUtf8:()=>EFB,toUint8Array:()=>bf6,toUtf8:()=>ff6});UFB.exports=vf6(HFB);var zFB=KFB(),EFB=aH0((A)=>{let B=zFB.fromString(A,"utf8");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength/Uint8Array.BYTES_PER_ELEMENT)},"fromUtf8"),bf6=aH0((A)=>{if(typeof A==="string")return EFB(A);if(ArrayBuffer.isView(A))return new Uint8Array(A.buffer,A.byteOffset,A.byteLength/Uint8Array.BYTES_PER_ELEMENT);return new Uint8Array(A)},"toUint8Array"),ff6=aH0((A)=>{if(typeof A==="string")return A;if(typeof A!=="object"||typeof A.byteOffset!=="number"||typeof A.byteLength!=="number")throw new Error("@smithy/util-utf8: toUtf8 encoder function only accepts string | Uint8Array.");return zFB.fromArrayBuffer(A.buffer,A.byteOffset,A.byteLength).toString("utf8")},"toUtf8")});
var iH0=E((I03,JFB)=>{var{defineProperty:Dk1,getOwnPropertyDescriptor:Cf6,getOwnPropertyNames:Kf6}=Object,Hf6=Object.prototype.hasOwnProperty,zf6=(A,B)=>Dk1(A,"name",{value:B,configurable:!0}),Ef6=(A,B)=>{for(var Q in B)Dk1(A,Q,{get:B[Q],enumerable:!0})},Uf6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Kf6(B))if(!Hf6.call(A,Z)&&Z!==Q)Dk1(A,Z,{get:()=>B[Z],enumerable:!(D=Cf6(B,Z))||D.enumerable})}return A},wf6=(A)=>Uf6(Dk1({},"__esModule",{value:!0}),A),WFB={};Ef6(WFB,{isArrayBuffer:()=>$f6});JFB.exports=wf6(WFB);var $f6=zf6((A)=>typeof ArrayBuffer==="function"&&A instanceof ArrayBuffer||Object.prototype.toString.call(A)==="[object ArrayBuffer]","isArrayBuffer")});
var sFB=E((V03,aFB)=>{var{defineProperty:Vk1,getOwnPropertyDescriptor:tf6,getOwnPropertyNames:ef6}=Object,Ah6=Object.prototype.hasOwnProperty,BF=(A,B)=>Vk1(A,"name",{value:B,configurable:!0}),Bh6=(A,B)=>{for(var Q in B)Vk1(A,Q,{get:B[Q],enumerable:!0})},Qh6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of ef6(B))if(!Ah6.call(A,Z)&&Z!==Q)Vk1(A,Z,{get:()=>B[Z],enumerable:!(D=tf6(B,Z))||D.enumerable})}return A},Dh6=(A)=>Qh6(Vk1({},"__esModule",{value:!0}),A),xFB={};Bh6(xFB,{SignatureV4:()=>jh6,clearCredentialCache:()=>$h6,createScope:()=>Jk1,getCanonicalHeaders:()=>Az0,getCanonicalQuery:()=>dFB,getPayloadHash:()=>Xk1,getSigningKey:()=>mFB,moveHeadersToQuery:()=>iFB,prepareRequest:()=>Qz0});aFB.exports=Dh6(xFB);var jFB=YFB(),oH0=gD1(),Zh6="X-Amz-Algorithm",Gh6="X-Amz-Credential",vFB="X-Amz-Date",Fh6="X-Amz-SignedHeaders",Ih6="X-Amz-Expires",bFB="X-Amz-Signature",fFB="X-Amz-Security-Token",hFB="authorization",gFB=vFB.toLowerCase(),Yh6="date",Wh6=[hFB,gFB,Yh6],Jh6=bFB.toLowerCase(),eH0="x-amz-content-sha256",Xh6=fFB.toLowerCase(),Vh6={authorization:!0,"cache-control":!0,connection:!0,expect:!0,from:!0,"keep-alive":!0,"max-forwards":!0,pragma:!0,referer:!0,te:!0,trailer:!0,"transfer-encoding":!0,upgrade:!0,"user-agent":!0,"x-amzn-trace-id":!0},Ch6=/^proxy-/,Kh6=/^sec-/,tH0="AWS4-HMAC-SHA256",Hh6="AWS4-HMAC-SHA256-PAYLOAD",zh6="UNSIGNED-PAYLOAD",Eh6=50,uFB="aws4_request",Uh6=604800,bx=RFB(),wh6=gD1(),Ue={},Wk1=[],Jk1=BF((A,B,Q)=>`${A}/${B}/${Q}/${uFB}`,"createScope"),mFB=BF(async(A,B,Q,D,Z)=>{let G=await yFB(A,B.secretAccessKey,B.accessKeyId),F=`${Q}:${D}:${Z}:${bx.toHex(G)}:${B.sessionToken}`;if(F in Ue)return Ue[F];Wk1.push(F);while(Wk1.length>Eh6)delete Ue[Wk1.shift()];let I=`AWS4${B.secretAccessKey}`;for(let Y of[Q,D,Z,uFB])I=await yFB(A,I,Y);return Ue[F]=I},"getSigningKey"),$h6=BF(()=>{Wk1.length=0,Object.keys(Ue).forEach((A)=>{delete Ue[A]})},"clearCredentialCache"),yFB=BF((A,B,Q)=>{let D=new A(B);return D.update(wh6.toUint8Array(Q)),D.digest()},"hmac"),Az0=BF(({headers:A},B,Q)=>{let D={};for(let Z of Object.keys(A).sort()){if(A[Z]==null)continue;let G=Z.toLowerCase();if(G in Vh6||(B==null?void 0:B.has(G))||Ch6.test(G)||Kh6.test(G)){if(!Q||Q&&!Q.has(G))continue}D[G]=A[Z].trim().replace(/\s+/g," ")}return D},"getCanonicalHeaders"),uD1=SFB(),dFB=BF(({query:A={}})=>{let B=[],Q={};for(let D of Object.keys(A).sort()){if(D.toLowerCase()===Jh6)continue;B.push(D);let Z=A[D];if(typeof Z==="string")Q[D]=`${uD1.escapeUri(D)}=${uD1.escapeUri(Z)}`;else if(Array.isArray(Z))Q[D]=Z.slice(0).reduce((G,F)=>G.concat([`${uD1.escapeUri(D)}=${uD1.escapeUri(F)}`]),[]).sort().join("&")}return B.map((D)=>Q[D]).filter((D)=>D).join("&")},"getCanonicalQuery"),qh6=iH0(),Nh6=gD1(),Xk1=BF(async({headers:A,body:B},Q)=>{for(let D of Object.keys(A))if(D.toLowerCase()===eH0)return A[D];if(B==null)return"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855";else if(typeof B==="string"||ArrayBuffer.isView(B)||qh6.isArrayBuffer(B)){let D=new Q;return D.update(Nh6.toUint8Array(B)),bx.toHex(await D.digest())}return zh6},"getPayloadHash"),kFB=gD1(),cFB=class A{format(B){let Q=[];for(let G of Object.keys(B)){let F=kFB.fromUtf8(G);Q.push(Uint8Array.from([F.byteLength]),F,this.formatHeaderValue(B[G]))}let D=new Uint8Array(Q.reduce((G,F)=>G+F.byteLength,0)),Z=0;for(let G of Q)D.set(G,Z),Z+=G.byteLength;return D}formatHeaderValue(B){switch(B.type){case"boolean":return Uint8Array.from([B.value?0:1]);case"byte":return Uint8Array.from([2,B.value]);case"short":let Q=new DataView(new ArrayBuffer(3));return Q.setUint8(0,3),Q.setInt16(1,B.value,!1),new Uint8Array(Q.buffer);case"integer":let D=new DataView(new ArrayBuffer(5));return D.setUint8(0,4),D.setInt32(1,B.value,!1),new Uint8Array(D.buffer);case"long":let Z=new Uint8Array(9);return Z[0]=5,Z.set(B.value.bytes,1),Z;case"binary":let G=new DataView(new ArrayBuffer(3+B.value.byteLength));G.setUint8(0,6),G.setUint16(1,B.value.byteLength,!1);let F=new Uint8Array(G.buffer);return F.set(B.value,3),F;case"string":let I=kFB.fromUtf8(B.value),Y=new DataView(new ArrayBuffer(3+I.byteLength));Y.setUint8(0,7),Y.setUint16(1,I.byteLength,!1);let W=new Uint8Array(Y.buffer);return W.set(I,3),W;case"timestamp":let J=new Uint8Array(9);return J[0]=8,J.set(Rh6.fromNumber(B.value.valueOf()).bytes,1),J;case"uuid":if(!Mh6.test(B.value))throw new Error(`Invalid UUID received: ${B.value}`);let X=new Uint8Array(17);return X[0]=9,X.set(bx.fromHex(B.value.replace(/\-/g,"")),1),X}}};BF(cFB,"HeaderFormatter");var Lh6=cFB,Mh6=/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/,lFB=class A{constructor(B){if(this.bytes=B,B.byteLength!==8)throw new Error("Int64 buffers must be exactly 8 bytes")}static fromNumber(B){if(B>9223372036854776000||B<-9223372036854776000)throw new Error(`${B} is too large (or, if negative, too small) to represent as an Int64`);let Q=new Uint8Array(8);for(let D=7,Z=Math.abs(Math.round(B));D>-1&&Z>0;D--,Z/=256)Q[D]=Z;if(B<0)Bz0(Q);return new A(Q)}valueOf(){let B=this.bytes.slice(0),Q=B[0]&128;if(Q)Bz0(B);return parseInt(bx.toHex(B),16)*(Q?-1:1)}toString(){return String(this.valueOf())}};BF(lFB,"Int64");var Rh6=lFB;function Bz0(A){for(let B=0;B<8;B++)A[B]^=255;for(let B=7;B>-1;B--)if(A[B]++,A[B]!==0)break}BF(Bz0,"negate");var Oh6=BF((A,B)=>{A=A.toLowerCase();for(let Q of Object.keys(B))if(A===Q.toLowerCase())return!0;return!1},"hasHeader"),pFB=BF(({headers:A,query:B,...Q})=>({...Q,headers:{...A},query:B?Th6(B):void 0}),"cloneRequest"),Th6=BF((A)=>Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{}),"cloneQuery"),iFB=BF((A,B={})=>{var Q;let{headers:D,query:Z={}}=typeof A.clone==="function"?A.clone():pFB(A);for(let G of Object.keys(D)){let F=G.toLowerCase();if(F.slice(0,6)==="x-amz-"&&!((Q=B.unhoistableHeaders)==null?void 0:Q.has(F)))Z[G]=D[G],delete D[G]}return{...A,headers:D,query:Z}},"moveHeadersToQuery"),Qz0=BF((A)=>{A=typeof A.clone==="function"?A.clone():pFB(A);for(let B of Object.keys(A.headers))if(Wh6.indexOf(B.toLowerCase())>-1)delete A.headers[B];return A},"prepareRequest"),Ph6=BF((A)=>Sh6(A).toISOString().replace(/\.\d{3}Z$/,"Z"),"iso8601"),Sh6=BF((A)=>{if(typeof A==="number")return new Date(A*1000);if(typeof A==="string"){if(Number(A))return new Date(Number(A)*1000);return new Date(A)}return A},"toDate"),nFB=class A{constructor({applyChecksum:B,credentials:Q,region:D,service:Z,sha256:G,uriEscapePath:F=!0}){this.headerFormatter=new Lh6,this.service=Z,this.sha256=G,this.uriEscapePath=F,this.applyChecksum=typeof B==="boolean"?B:!0,this.regionProvider=jFB.normalizeProvider(D),this.credentialProvider=jFB.normalizeProvider(Q)}async presign(B,Q={}){let{signingDate:D=new Date,expiresIn:Z=3600,unsignableHeaders:G,unhoistableHeaders:F,signableHeaders:I,signingRegion:Y,signingService:W}=Q,J=await this.credentialProvider();this.validateResolvedCredentials(J);let X=Y??await this.regionProvider(),{longDate:V,shortDate:C}=Yk1(D);if(Z>Uh6)return Promise.reject("Signature version 4 presigned URLs must have an expiration date less than one week in the future");let K=Jk1(C,X,W??this.service),H=iFB(Qz0(B),{unhoistableHeaders:F});if(J.sessionToken)H.query[fFB]=J.sessionToken;H.query[Zh6]=tH0,H.query[Gh6]=`${J.accessKeyId}/${K}`,H.query[vFB]=V,H.query[Ih6]=Z.toString(10);let z=Az0(H,G,I);return H.query[Fh6]=_FB(z),H.query[bFB]=await this.getSignature(V,K,this.getSigningKey(J,X,C,W),this.createCanonicalRequest(H,z,await Xk1(B,this.sha256))),H}async sign(B,Q){if(typeof B==="string")return this.signString(B,Q);else if(B.headers&&B.payload)return this.signEvent(B,Q);else if(B.message)return this.signMessage(B,Q);else return this.signRequest(B,Q)}async signEvent({headers:B,payload:Q},{signingDate:D=new Date,priorSignature:Z,signingRegion:G,signingService:F}){let I=G??await this.regionProvider(),{shortDate:Y,longDate:W}=Yk1(D),J=Jk1(Y,I,F??this.service),X=await Xk1({headers:{},body:Q},this.sha256),V=new this.sha256;V.update(B);let C=bx.toHex(await V.digest()),K=[Hh6,W,J,Z,C,X].join(`
`);return this.signString(K,{signingDate:D,signingRegion:I,signingService:F})}async signMessage(B,{signingDate:Q=new Date,signingRegion:D,signingService:Z}){return this.signEvent({headers:this.headerFormatter.format(B.message.headers),payload:B.message.body},{signingDate:Q,signingRegion:D,signingService:Z,priorSignature:B.priorSignature}).then((F)=>{return{message:B.message,signature:F}})}async signString(B,{signingDate:Q=new Date,signingRegion:D,signingService:Z}={}){let G=await this.credentialProvider();this.validateResolvedCredentials(G);let F=D??await this.regionProvider(),{shortDate:I}=Yk1(Q),Y=new this.sha256(await this.getSigningKey(G,F,I,Z));return Y.update(oH0.toUint8Array(B)),bx.toHex(await Y.digest())}async signRequest(B,{signingDate:Q=new Date,signableHeaders:D,unsignableHeaders:Z,signingRegion:G,signingService:F}={}){let I=await this.credentialProvider();this.validateResolvedCredentials(I);let Y=G??await this.regionProvider(),W=Qz0(B),{longDate:J,shortDate:X}=Yk1(Q),V=Jk1(X,Y,F??this.service);if(W.headers[gFB]=J,I.sessionToken)W.headers[Xh6]=I.sessionToken;let C=await Xk1(W,this.sha256);if(!Oh6(eH0,W.headers)&&this.applyChecksum)W.headers[eH0]=C;let K=Az0(W,Z,D),H=await this.getSignature(J,V,this.getSigningKey(I,Y,X,F),this.createCanonicalRequest(W,K,C));return W.headers[hFB]=`${tH0} Credential=${I.accessKeyId}/${V}, SignedHeaders=${_FB(K)}, Signature=${H}`,W}createCanonicalRequest(B,Q,D){let Z=Object.keys(Q).sort();return`${B.method}
${this.getCanonicalPath(B)}
${dFB(B)}
${Z.map((G)=>`${G}:${Q[G]}`).join(`
`)}

${Z.join(";")}
${D}`}async createStringToSign(B,Q,D){let Z=new this.sha256;Z.update(oH0.toUint8Array(D));let G=await Z.digest();return`${tH0}
${B}
${Q}
${bx.toHex(G)}`}getCanonicalPath({path:B}){if(this.uriEscapePath){let Q=[];for(let G of B.split("/")){if((G==null?void 0:G.length)===0)continue;if(G===".")continue;if(G==="..")Q.pop();else Q.push(G)}let D=`${(B==null?void 0:B.startsWith("/"))?"/":""}${Q.join("/")}${Q.length>0&&(B==null?void 0:B.endsWith("/"))?"/":""}`;return uD1.escapeUri(D).replace(/%2F/g,"/")}return B}async getSignature(B,Q,D,Z){let G=await this.createStringToSign(B,Q,Z),F=new this.sha256(await D);return F.update(oH0.toUint8Array(G)),bx.toHex(await F.digest())}getSigningKey(B,Q,D,Z){return mFB(this.sha256,B,D,Q,Z||this.service)}validateResolvedCredentials(B){if(typeof B!=="object"||typeof B.accessKeyId!=="string"||typeof B.secretAccessKey!=="string")throw new Error("Resolved credential object is not valid")}};BF(nFB,"SignatureV4");var jh6=nFB,Yk1=BF((A)=>{let B=Ph6(A).replace(/[\-:]/g,"");return{longDate:B,shortDate:B.slice(0,8)}},"formatDate"),_FB=BF((A)=>Object.keys(A).sort().join(";"),"getCanonicalHeaderList")});

// Export all variables
module.exports = {
  DFB,
  KFB,
  RFB,
  SFB,
  YFB,
  gD1,
  iH0,
  sFB
};
