// Package extracted with entry point: QL2
// Contains 11 variables: <PERSON><PERSON>, <PERSON>, <PERSON>51, QL2, _G0, fG0, gN2, q51, sR1, vG0... (and 1 more)

var FP=E((HM5,JN2)=>{var YN2=["nodebuffer","arraybuffer","fragments"],WN2=typeof Blob!=="undefined";if(WN2)YN2.push("blob");JN2.exports={BINARY_TYPES:YN2,EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",hasBlob:WN2,kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}});
var Jo=E((wM5,dR1)=>{var{isUtf8:NN2}=J1("buffer"),{hasBlob:An4}=FP(),Bn4=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0];function Qn4(A){return A>=1000&&A<=1014&&A!==1004&&A!==1005&&A!==1006||A>=3000&&A<=4999}function yG0(A){let B=A.length,Q=0;while(Q<B)if((A[Q]&128)===0)Q++;else if((A[Q]&224)===192){if(Q+1===B||(A[Q+1]&192)!==128||(A[Q]&254)===192)return!1;Q+=2}else if((A[Q]&240)===224){if(Q+2>=B||(A[Q+1]&192)!==128||(A[Q+2]&192)!==128||A[Q]===224&&(A[Q+1]&224)===128||A[Q]===237&&(A[Q+1]&224)===160)return!1;Q+=3}else if((A[Q]&248)===240){if(Q+3>=B||(A[Q+1]&192)!==128||(A[Q+2]&192)!==128||(A[Q+3]&192)!==128||A[Q]===240&&(A[Q+1]&240)===128||A[Q]===244&&A[Q+1]>143||A[Q]>244)return!1;Q+=4}else return!1;return!0}function Dn4(A){return An4&&typeof A==="object"&&typeof A.arrayBuffer==="function"&&typeof A.type==="string"&&typeof A.stream==="function"&&(A[Symbol.toStringTag]==="Blob"||A[Symbol.toStringTag]==="File")}dR1.exports={isBlob:Dn4,isValidStatusCode:Qn4,isValidUTF8:yG0,tokenChars:Bn4};if(NN2)dR1.exports.isValidUTF8=function(A){return A.length<24?yG0(A):NN2(A)};else if(!process.env.WS_NO_UTF_8_VALIDATE)try{let A=(()=>{throw new Error("Cannot require module "+"utf-8-validate");})();dR1.exports.isValidUTF8=function(B){return B.length<32?yG0(B):A(B)}}catch(A){}});
var M51=E((UM5,qN2)=>{var N51=J1("zlib"),EN2=q51(),si4=zN2(),{kStatusCode:UN2}=FP(),ri4=Buffer[Symbol.species],oi4=Buffer.from([0,0,255,255]),mR1=Symbol("permessage-deflate"),IP=Symbol("total-length"),L51=Symbol("callback"),R_=Symbol("buffers"),uR1=Symbol("error"),gR1;class wN2{constructor(A,B,Q){if(this._maxPayload=Q|0,this._options=A||{},this._threshold=this._options.threshold!==void 0?this._options.threshold:1024,this._isServer=!!B,this._deflate=null,this._inflate=null,this.params=null,!gR1){let D=this._options.concurrencyLimit!==void 0?this._options.concurrencyLimit:10;gR1=new si4(D)}}static get extensionName(){return"permessage-deflate"}offer(){let A={};if(this._options.serverNoContextTakeover)A.server_no_context_takeover=!0;if(this._options.clientNoContextTakeover)A.client_no_context_takeover=!0;if(this._options.serverMaxWindowBits)A.server_max_window_bits=this._options.serverMaxWindowBits;if(this._options.clientMaxWindowBits)A.client_max_window_bits=this._options.clientMaxWindowBits;else if(this._options.clientMaxWindowBits==null)A.client_max_window_bits=!0;return A}accept(A){return A=this.normalizeParams(A),this.params=this._isServer?this.acceptAsServer(A):this.acceptAsClient(A),this.params}cleanup(){if(this._inflate)this._inflate.close(),this._inflate=null;if(this._deflate){let A=this._deflate[L51];if(this._deflate.close(),this._deflate=null,A)A(new Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(A){let B=this._options,Q=A.find((D)=>{if(B.serverNoContextTakeover===!1&&D.server_no_context_takeover||D.server_max_window_bits&&(B.serverMaxWindowBits===!1||typeof B.serverMaxWindowBits==="number"&&B.serverMaxWindowBits>D.server_max_window_bits)||typeof B.clientMaxWindowBits==="number"&&!D.client_max_window_bits)return!1;return!0});if(!Q)throw new Error("None of the extension offers can be accepted");if(B.serverNoContextTakeover)Q.server_no_context_takeover=!0;if(B.clientNoContextTakeover)Q.client_no_context_takeover=!0;if(typeof B.serverMaxWindowBits==="number")Q.server_max_window_bits=B.serverMaxWindowBits;if(typeof B.clientMaxWindowBits==="number")Q.client_max_window_bits=B.clientMaxWindowBits;else if(Q.client_max_window_bits===!0||B.clientMaxWindowBits===!1)delete Q.client_max_window_bits;return Q}acceptAsClient(A){let B=A[0];if(this._options.clientNoContextTakeover===!1&&B.client_no_context_takeover)throw new Error('Unexpected parameter "client_no_context_takeover"');if(!B.client_max_window_bits){if(typeof this._options.clientMaxWindowBits==="number")B.client_max_window_bits=this._options.clientMaxWindowBits}else if(this._options.clientMaxWindowBits===!1||typeof this._options.clientMaxWindowBits==="number"&&B.client_max_window_bits>this._options.clientMaxWindowBits)throw new Error('Unexpected or invalid parameter "client_max_window_bits"');return B}normalizeParams(A){return A.forEach((B)=>{Object.keys(B).forEach((Q)=>{let D=B[Q];if(D.length>1)throw new Error(`Parameter "${Q}" must have only a single value`);if(D=D[0],Q==="client_max_window_bits"){if(D!==!0){let Z=+D;if(!Number.isInteger(Z)||Z<8||Z>15)throw new TypeError(`Invalid value for parameter "${Q}": ${D}`);D=Z}else if(!this._isServer)throw new TypeError(`Invalid value for parameter "${Q}": ${D}`)}else if(Q==="server_max_window_bits"){let Z=+D;if(!Number.isInteger(Z)||Z<8||Z>15)throw new TypeError(`Invalid value for parameter "${Q}": ${D}`);D=Z}else if(Q==="client_no_context_takeover"||Q==="server_no_context_takeover"){if(D!==!0)throw new TypeError(`Invalid value for parameter "${Q}": ${D}`)}else throw new Error(`Unknown parameter "${Q}"`);B[Q]=D})}),A}decompress(A,B,Q){gR1.add((D)=>{this._decompress(A,B,(Z,G)=>{D(),Q(Z,G)})})}compress(A,B,Q){gR1.add((D)=>{this._compress(A,B,(Z,G)=>{D(),Q(Z,G)})})}_decompress(A,B,Q){let D=this._isServer?"client":"server";if(!this._inflate){let Z=`${D}_max_window_bits`,G=typeof this.params[Z]!=="number"?N51.Z_DEFAULT_WINDOWBITS:this.params[Z];this._inflate=N51.createInflateRaw({...this._options.zlibInflateOptions,windowBits:G}),this._inflate[mR1]=this,this._inflate[IP]=0,this._inflate[R_]=[],this._inflate.on("error",ei4),this._inflate.on("data",$N2)}if(this._inflate[L51]=Q,this._inflate.write(A),B)this._inflate.write(oi4);this._inflate.flush(()=>{let Z=this._inflate[uR1];if(Z){this._inflate.close(),this._inflate=null,Q(Z);return}let G=EN2.concat(this._inflate[R_],this._inflate[IP]);if(this._inflate._readableState.endEmitted)this._inflate.close(),this._inflate=null;else if(this._inflate[IP]=0,this._inflate[R_]=[],B&&this.params[`${D}_no_context_takeover`])this._inflate.reset();Q(null,G)})}_compress(A,B,Q){let D=this._isServer?"server":"client";if(!this._deflate){let Z=`${D}_max_window_bits`,G=typeof this.params[Z]!=="number"?N51.Z_DEFAULT_WINDOWBITS:this.params[Z];this._deflate=N51.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:G}),this._deflate[IP]=0,this._deflate[R_]=[],this._deflate.on("data",ti4)}this._deflate[L51]=Q,this._deflate.write(A),this._deflate.flush(N51.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let Z=EN2.concat(this._deflate[R_],this._deflate[IP]);if(B)Z=new ri4(Z.buffer,Z.byteOffset,Z.length-4);if(this._deflate[L51]=null,this._deflate[IP]=0,this._deflate[R_]=[],B&&this.params[`${D}_no_context_takeover`])this._deflate.reset();Q(null,Z)})}}qN2.exports=wN2;function ti4(A){this[R_].push(A),this[IP]+=A.length}function $N2(A){if(this[IP]+=A.length,this[mR1]._maxPayload<1||this[IP]<=this[mR1]._maxPayload){this[R_].push(A);return}this[uR1]=new RangeError("Max payload size exceeded"),this[uR1].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[uR1][UN2]=1009,this.removeListener("data",$N2),this.reset()}function ei4(A){this[mR1]._inflate=null,A[UN2]=1007,this[L51](A)}});
var QL2=E((SM5,BL2)=>{var PM5=sR1(),{Duplex:rn4}=J1("stream");function eN2(A){A.emit("close")}function on4(){if(!this.destroyed&&this._writableState.finished)this.destroy()}function AL2(A){if(this.removeListener("error",AL2),this.destroy(),this.listenerCount("error")===0)this.emit("error",A)}function tn4(A,B){let Q=!0,D=new rn4({...B,autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1});return A.on("message",function Z(G,F){let I=!F&&D._readableState.objectMode?G.toString():G;if(!D.push(I))A.pause()}),A.once("error",function Z(G){if(D.destroyed)return;Q=!1,D.destroy(G)}),A.once("close",function Z(){if(D.destroyed)return;D.push(null)}),D._destroy=function(Z,G){if(A.readyState===A.CLOSED){G(Z),process.nextTick(eN2,D);return}let F=!1;if(A.once("error",function I(Y){F=!0,G(Y)}),A.once("close",function I(){if(!F)G(Z);process.nextTick(eN2,D)}),Q)A.terminate()},D._final=function(Z){if(A.readyState===A.CONNECTING){A.once("open",function G(){D._final(Z)});return}if(A._socket===null)return;if(A._socket._writableState.finished){if(Z(),D._readableState.endEmitted)D.destroy()}else A._socket.once("finish",function G(){Z()}),A.close()},D._read=function(){if(A.isPaused)A.resume()},D._write=function(Z,G,F){if(A.readyState===A.CONNECTING){A.once("open",function I(){D._write(Z,G,F)});return}A.send(Z,F)},D.on("end",on4),D.on("error",AL2),D}BL2.exports=tn4});
var _G0=E(($M5,TN2)=>{var{Writable:Zn4}=J1("stream"),LN2=M51(),{BINARY_TYPES:Gn4,EMPTY_BUFFER:MN2,kStatusCode:Fn4,kWebSocket:In4}=FP(),{concat:kG0,toArrayBuffer:Yn4,unmask:Wn4}=q51(),{isValidStatusCode:Jn4,isValidUTF8:RN2}=Jo(),cR1=Buffer[Symbol.species];class ON2 extends Zn4{constructor(A={}){super();this._allowSynchronousEvents=A.allowSynchronousEvents!==void 0?A.allowSynchronousEvents:!0,this._binaryType=A.binaryType||Gn4[0],this._extensions=A.extensions||{},this._isServer=!!A.isServer,this._maxPayload=A.maxPayload|0,this._skipUTF8Validation=!!A.skipUTF8Validation,this[In4]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._errored=!1,this._loop=!1,this._state=0}_write(A,B,Q){if(this._opcode===8&&this._state==0)return Q();this._bufferedBytes+=A.length,this._buffers.push(A),this.startLoop(Q)}consume(A){if(this._bufferedBytes-=A,A===this._buffers[0].length)return this._buffers.shift();if(A<this._buffers[0].length){let Q=this._buffers[0];return this._buffers[0]=new cR1(Q.buffer,Q.byteOffset+A,Q.length-A),new cR1(Q.buffer,Q.byteOffset,A)}let B=Buffer.allocUnsafe(A);do{let Q=this._buffers[0],D=B.length-A;if(A>=Q.length)B.set(this._buffers.shift(),D);else B.set(new Uint8Array(Q.buffer,Q.byteOffset,A),D),this._buffers[0]=new cR1(Q.buffer,Q.byteOffset+A,Q.length-A);A-=Q.length}while(A>0);return B}startLoop(A){this._loop=!0;do switch(this._state){case 0:this.getInfo(A);break;case 1:this.getPayloadLength16(A);break;case 2:this.getPayloadLength64(A);break;case 3:this.getMask();break;case 4:this.getData(A);break;case 5:case 6:this._loop=!1;return}while(this._loop);if(!this._errored)A()}getInfo(A){if(this._bufferedBytes<2){this._loop=!1;return}let B=this.consume(2);if((B[0]&48)!==0){let D=this.createError(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3");A(D);return}let Q=(B[0]&64)===64;if(Q&&!this._extensions[LN2.extensionName]){let D=this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");A(D);return}if(this._fin=(B[0]&128)===128,this._opcode=B[0]&15,this._payloadLength=B[1]&127,this._opcode===0){if(Q){let D=this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");A(D);return}if(!this._fragmented){let D=this.createError(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE");A(D);return}this._opcode=this._fragmented}else if(this._opcode===1||this._opcode===2){if(this._fragmented){let D=this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE");A(D);return}this._compressed=Q}else if(this._opcode>7&&this._opcode<11){if(!this._fin){let D=this.createError(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN");A(D);return}if(Q){let D=this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");A(D);return}if(this._payloadLength>125||this._opcode===8&&this._payloadLength===1){let D=this.createError(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH");A(D);return}}else{let D=this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE");A(D);return}if(!this._fin&&!this._fragmented)this._fragmented=this._opcode;if(this._masked=(B[1]&128)===128,this._isServer){if(!this._masked){let D=this.createError(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK");A(D);return}}else if(this._masked){let D=this.createError(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK");A(D);return}if(this._payloadLength===126)this._state=1;else if(this._payloadLength===127)this._state=2;else this.haveLength(A)}getPayloadLength16(A){if(this._bufferedBytes<2){this._loop=!1;return}this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength(A)}getPayloadLength64(A){if(this._bufferedBytes<8){this._loop=!1;return}let B=this.consume(8),Q=B.readUInt32BE(0);if(Q>Math.pow(2,21)-1){let D=this.createError(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH");A(D);return}this._payloadLength=Q*Math.pow(2,32)+B.readUInt32BE(4),this.haveLength(A)}haveLength(A){if(this._payloadLength&&this._opcode<8){if(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0){let B=this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH");A(B);return}}if(this._masked)this._state=3;else this._state=4}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=4}getData(A){let B=MN2;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}if(B=this.consume(this._payloadLength),this._masked&&(this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3])!==0)Wn4(B,this._mask)}if(this._opcode>7){this.controlMessage(B,A);return}if(this._compressed){this._state=5,this.decompress(B,A);return}if(B.length)this._messageLength=this._totalPayloadLength,this._fragments.push(B);this.dataMessage(A)}decompress(A,B){this._extensions[LN2.extensionName].decompress(A,this._fin,(D,Z)=>{if(D)return B(D);if(Z.length){if(this._messageLength+=Z.length,this._messageLength>this._maxPayload&&this._maxPayload>0){let G=this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH");B(G);return}this._fragments.push(Z)}if(this.dataMessage(B),this._state===0)this.startLoop(B)})}dataMessage(A){if(!this._fin){this._state=0;return}let B=this._messageLength,Q=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],this._opcode===2){let D;if(this._binaryType==="nodebuffer")D=kG0(Q,B);else if(this._binaryType==="arraybuffer")D=Yn4(kG0(Q,B));else if(this._binaryType==="blob")D=new Blob(Q);else D=Q;if(this._allowSynchronousEvents)this.emit("message",D,!0),this._state=0;else this._state=6,setImmediate(()=>{this.emit("message",D,!0),this._state=0,this.startLoop(A)})}else{let D=kG0(Q,B);if(!this._skipUTF8Validation&&!RN2(D)){let Z=this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8");A(Z);return}if(this._state===5||this._allowSynchronousEvents)this.emit("message",D,!1),this._state=0;else this._state=6,setImmediate(()=>{this.emit("message",D,!1),this._state=0,this.startLoop(A)})}}controlMessage(A,B){if(this._opcode===8){if(A.length===0)this._loop=!1,this.emit("conclude",1005,MN2),this.end();else{let Q=A.readUInt16BE(0);if(!Jn4(Q)){let Z=this.createError(RangeError,`invalid status code ${Q}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE");B(Z);return}let D=new cR1(A.buffer,A.byteOffset+2,A.length-2);if(!this._skipUTF8Validation&&!RN2(D)){let Z=this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8");B(Z);return}this._loop=!1,this.emit("conclude",Q,D),this.end()}this._state=0;return}if(this._allowSynchronousEvents)this.emit(this._opcode===9?"ping":"pong",A),this._state=0;else this._state=6,setImmediate(()=>{this.emit(this._opcode===9?"ping":"pong",A),this._state=0,this.startLoop(B)})}createError(A,B,Q,D,Z){this._loop=!1,this._errored=!0;let G=new A(Q?`Invalid WebSocket frame: ${B}`:B);return Error.captureStackTrace(G,this.createError),G.code=Z,G[Fn4]=D,G}}TN2.exports=ON2});
var fG0=E((MM5,uN2)=>{var{tokenChars:T51}=Jo();function iL(A,B,Q){if(A[B]===void 0)A[B]=[Q];else A[B].push(Q)}function qn4(A){let B=Object.create(null),Q=Object.create(null),D=!1,Z=!1,G=!1,F,I,Y=-1,W=-1,J=-1,X=0;for(;X<A.length;X++)if(W=A.charCodeAt(X),F===void 0)if(J===-1&&T51[W]===1){if(Y===-1)Y=X}else if(X!==0&&(W===32||W===9)){if(J===-1&&Y!==-1)J=X}else if(W===59||W===44){if(Y===-1)throw new SyntaxError(`Unexpected character at index ${X}`);if(J===-1)J=X;let C=A.slice(Y,J);if(W===44)iL(B,C,Q),Q=Object.create(null);else F=C;Y=J=-1}else throw new SyntaxError(`Unexpected character at index ${X}`);else if(I===void 0)if(J===-1&&T51[W]===1){if(Y===-1)Y=X}else if(W===32||W===9){if(J===-1&&Y!==-1)J=X}else if(W===59||W===44){if(Y===-1)throw new SyntaxError(`Unexpected character at index ${X}`);if(J===-1)J=X;if(iL(Q,A.slice(Y,J),!0),W===44)iL(B,F,Q),Q=Object.create(null),F=void 0;Y=J=-1}else if(W===61&&Y!==-1&&J===-1)I=A.slice(Y,X),Y=J=-1;else throw new SyntaxError(`Unexpected character at index ${X}`);else if(Z){if(T51[W]!==1)throw new SyntaxError(`Unexpected character at index ${X}`);if(Y===-1)Y=X;else if(!D)D=!0;Z=!1}else if(G)if(T51[W]===1){if(Y===-1)Y=X}else if(W===34&&Y!==-1)G=!1,J=X;else if(W===92)Z=!0;else throw new SyntaxError(`Unexpected character at index ${X}`);else if(W===34&&A.charCodeAt(X-1)===61)G=!0;else if(J===-1&&T51[W]===1){if(Y===-1)Y=X}else if(Y!==-1&&(W===32||W===9)){if(J===-1)J=X}else if(W===59||W===44){if(Y===-1)throw new SyntaxError(`Unexpected character at index ${X}`);if(J===-1)J=X;let C=A.slice(Y,J);if(D)C=C.replace(/\\/g,""),D=!1;if(iL(Q,I,C),W===44)iL(B,F,Q),Q=Object.create(null),F=void 0;I=void 0,Y=J=-1}else throw new SyntaxError(`Unexpected character at index ${X}`);if(Y===-1||G||W===32||W===9)throw new SyntaxError("Unexpected end of input");if(J===-1)J=X;let V=A.slice(Y,J);if(F===void 0)iL(B,V,Q);else{if(I===void 0)iL(Q,V,!0);else if(D)iL(Q,I,V.replace(/\\/g,""));else iL(Q,I,V);iL(B,F,Q)}return B}function Nn4(A){return Object.keys(A).map((B)=>{let Q=A[B];if(!Array.isArray(Q))Q=[Q];return Q.map((D)=>{return[B].concat(Object.keys(D).map((Z)=>{let G=D[Z];if(!Array.isArray(G))G=[G];return G.map((F)=>F===!0?Z:`${Z}=${F}`).join("; ")})).join("; ")}).join(", ")}).join(", ")}uN2.exports={format:Nn4,parse:qn4}});
var gN2=E((LM5,hN2)=>{var{kForOnEventAttribute:R51,kListener:bG0}=FP(),yN2=Symbol("kCode"),kN2=Symbol("kData"),_N2=Symbol("kError"),xN2=Symbol("kMessage"),vN2=Symbol("kReason"),Co=Symbol("kTarget"),bN2=Symbol("kType"),fN2=Symbol("kWasClean");class T_{constructor(A){this[Co]=null,this[bN2]=A}get target(){return this[Co]}get type(){return this[bN2]}}Object.defineProperty(T_.prototype,"target",{enumerable:!0});Object.defineProperty(T_.prototype,"type",{enumerable:!0});class Ko extends T_{constructor(A,B={}){super(A);this[yN2]=B.code===void 0?0:B.code,this[vN2]=B.reason===void 0?"":B.reason,this[fN2]=B.wasClean===void 0?!1:B.wasClean}get code(){return this[yN2]}get reason(){return this[vN2]}get wasClean(){return this[fN2]}}Object.defineProperty(Ko.prototype,"code",{enumerable:!0});Object.defineProperty(Ko.prototype,"reason",{enumerable:!0});Object.defineProperty(Ko.prototype,"wasClean",{enumerable:!0});class O51 extends T_{constructor(A,B={}){super(A);this[_N2]=B.error===void 0?null:B.error,this[xN2]=B.message===void 0?"":B.message}get error(){return this[_N2]}get message(){return this[xN2]}}Object.defineProperty(O51.prototype,"error",{enumerable:!0});Object.defineProperty(O51.prototype,"message",{enumerable:!0});class pR1 extends T_{constructor(A,B={}){super(A);this[kN2]=B.data===void 0?null:B.data}get data(){return this[kN2]}}Object.defineProperty(pR1.prototype,"data",{enumerable:!0});var $n4={addEventListener(A,B,Q={}){for(let Z of this.listeners(A))if(!Q[R51]&&Z[bG0]===B&&!Z[R51])return;let D;if(A==="message")D=function Z(G,F){let I=new pR1("message",{data:F?G:G.toString()});I[Co]=this,lR1(B,this,I)};else if(A==="close")D=function Z(G,F){let I=new Ko("close",{code:G,reason:F.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});I[Co]=this,lR1(B,this,I)};else if(A==="error")D=function Z(G){let F=new O51("error",{error:G,message:G.message});F[Co]=this,lR1(B,this,F)};else if(A==="open")D=function Z(){let G=new T_("open");G[Co]=this,lR1(B,this,G)};else return;if(D[R51]=!!Q[R51],D[bG0]=B,Q.once)this.once(A,D);else this.on(A,D)},removeEventListener(A,B){for(let Q of this.listeners(A))if(Q[bG0]===B&&!Q[R51]){this.removeListener(A,Q);break}}};hN2.exports={CloseEvent:Ko,ErrorEvent:O51,Event:T_,EventTarget:$n4,MessageEvent:pR1};function lR1(A,B,Q){if(typeof A==="object"&&A.handleEvent)A.handleEvent.call(A,Q);else A.call(B,Q)}});
var q51=E((zM5,hR1)=>{var{EMPTY_BUFFER:ii4}=FP(),PG0=Buffer[Symbol.species];function ni4(A,B){if(A.length===0)return ii4;if(A.length===1)return A[0];let Q=Buffer.allocUnsafe(B),D=0;for(let Z=0;Z<A.length;Z++){let G=A[Z];Q.set(G,D),D+=G.length}if(D<B)return new PG0(Q.buffer,Q.byteOffset,D);return Q}function XN2(A,B,Q,D,Z){for(let G=0;G<Z;G++)Q[D+G]=A[G]^B[G&3]}function VN2(A,B){for(let Q=0;Q<A.length;Q++)A[Q]^=B[Q&3]}function ai4(A){if(A.length===A.buffer.byteLength)return A.buffer;return A.buffer.slice(A.byteOffset,A.byteOffset+A.length)}function SG0(A){if(SG0.readOnly=!0,Buffer.isBuffer(A))return A;let B;if(A instanceof ArrayBuffer)B=new PG0(A);else if(ArrayBuffer.isView(A))B=new PG0(A.buffer,A.byteOffset,A.byteLength);else B=Buffer.from(A),SG0.readOnly=!1;return B}hR1.exports={concat:ni4,mask:XN2,toArrayBuffer:ai4,toBuffer:SG0,unmask:VN2};if(!process.env.WS_NO_BUFFER_UTIL)try{let A=(()=>{throw new Error("Cannot require module "+"bufferutil");})();hR1.exports.mask=function(B,Q,D,Z,G){if(G<48)XN2(B,Q,D,Z,G);else A.mask(B,Q,D,Z,G)},hR1.exports.unmask=function(B,Q){if(B.length<32)VN2(B,Q);else A.unmask(B,Q)}}catch(A){}});
var sR1=E((TM5,tN2)=>{var Ln4=J1("events"),Mn4=J1("https"),Rn4=J1("http"),cN2=J1("net"),On4=J1("tls"),{randomBytes:Tn4,createHash:Pn4}=J1("crypto"),{Duplex:RM5,Readable:OM5}=J1("stream"),{URL:hG0}=J1("url"),P_=M51(),Sn4=_G0(),jn4=vG0(),{isBlob:yn4}=Jo(),{BINARY_TYPES:mN2,EMPTY_BUFFER:iR1,GUID:kn4,kForOnEventAttribute:gG0,kListener:_n4,kStatusCode:xn4,kWebSocket:UI,NOOP:lN2}=FP(),{EventTarget:{addEventListener:vn4,removeEventListener:bn4}}=gN2(),{format:fn4,parse:hn4}=fG0(),{toBuffer:gn4}=q51(),pN2=Symbol("kAborted"),uG0=[8,13],YP=["CONNECTING","OPEN","CLOSING","CLOSED"],un4=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/;class aQ extends Ln4{constructor(A,B,Q){super();if(this._binaryType=mN2[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=iR1,this._closeTimer=null,this._errorEmitted=!1,this._extensions={},this._paused=!1,this._protocol="",this._readyState=aQ.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,A!==null){if(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,B===void 0)B=[];else if(!Array.isArray(B))if(typeof B==="object"&&B!==null)Q=B,B=[];else B=[B];iN2(this,A,B,Q)}else this._autoPong=Q.autoPong,this._isServer=!0}get binaryType(){return this._binaryType}set binaryType(A){if(!mN2.includes(A))return;if(this._binaryType=A,this._receiver)this._receiver._binaryType=A}get bufferedAmount(){if(!this._socket)return this._bufferedAmount;return this._socket._writableState.length+this._sender._bufferedBytes}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(A,B,Q){let D=new Sn4({allowSynchronousEvents:Q.allowSynchronousEvents,binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:Q.maxPayload,skipUTF8Validation:Q.skipUTF8Validation}),Z=new jn4(A,this._extensions,Q.generateMask);if(this._receiver=D,this._sender=Z,this._socket=A,D[UI]=this,Z[UI]=this,A[UI]=this,D.on("conclude",cn4),D.on("drain",ln4),D.on("error",pn4),D.on("message",in4),D.on("ping",nn4),D.on("pong",an4),Z.onerror=sn4,A.setTimeout)A.setTimeout(0);if(A.setNoDelay)A.setNoDelay();if(B.length>0)A.unshift(B);A.on("close",sN2),A.on("data",aR1),A.on("end",rN2),A.on("error",oN2),this._readyState=aQ.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=aQ.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}if(this._extensions[P_.extensionName])this._extensions[P_.extensionName].cleanup();this._receiver.removeAllListeners(),this._readyState=aQ.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(A,B){if(this.readyState===aQ.CLOSED)return;if(this.readyState===aQ.CONNECTING){SK(this,this._req,"WebSocket was closed before the connection was established");return}if(this.readyState===aQ.CLOSING){if(this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted))this._socket.end();return}this._readyState=aQ.CLOSING,this._sender.close(A,B,!this._isServer,(Q)=>{if(Q)return;if(this._closeFrameSent=!0,this._closeFrameReceived||this._receiver._writableState.errorEmitted)this._socket.end()}),aN2(this)}pause(){if(this.readyState===aQ.CONNECTING||this.readyState===aQ.CLOSED)return;this._paused=!0,this._socket.pause()}ping(A,B,Q){if(this.readyState===aQ.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof A==="function")Q=A,A=B=void 0;else if(typeof B==="function")Q=B,B=void 0;if(typeof A==="number")A=A.toString();if(this.readyState!==aQ.OPEN){mG0(this,A,Q);return}if(B===void 0)B=!this._isServer;this._sender.ping(A||iR1,B,Q)}pong(A,B,Q){if(this.readyState===aQ.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof A==="function")Q=A,A=B=void 0;else if(typeof B==="function")Q=B,B=void 0;if(typeof A==="number")A=A.toString();if(this.readyState!==aQ.OPEN){mG0(this,A,Q);return}if(B===void 0)B=!this._isServer;this._sender.pong(A||iR1,B,Q)}resume(){if(this.readyState===aQ.CONNECTING||this.readyState===aQ.CLOSED)return;if(this._paused=!1,!this._receiver._writableState.needDrain)this._socket.resume()}send(A,B,Q){if(this.readyState===aQ.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof B==="function")Q=B,B={};if(typeof A==="number")A=A.toString();if(this.readyState!==aQ.OPEN){mG0(this,A,Q);return}let D={binary:typeof A!=="string",mask:!this._isServer,compress:!0,fin:!0,...B};if(!this._extensions[P_.extensionName])D.compress=!1;this._sender.send(A||iR1,D,Q)}terminate(){if(this.readyState===aQ.CLOSED)return;if(this.readyState===aQ.CONNECTING){SK(this,this._req,"WebSocket was closed before the connection was established");return}if(this._socket)this._readyState=aQ.CLOSING,this._socket.destroy()}}Object.defineProperty(aQ,"CONNECTING",{enumerable:!0,value:YP.indexOf("CONNECTING")});Object.defineProperty(aQ.prototype,"CONNECTING",{enumerable:!0,value:YP.indexOf("CONNECTING")});Object.defineProperty(aQ,"OPEN",{enumerable:!0,value:YP.indexOf("OPEN")});Object.defineProperty(aQ.prototype,"OPEN",{enumerable:!0,value:YP.indexOf("OPEN")});Object.defineProperty(aQ,"CLOSING",{enumerable:!0,value:YP.indexOf("CLOSING")});Object.defineProperty(aQ.prototype,"CLOSING",{enumerable:!0,value:YP.indexOf("CLOSING")});Object.defineProperty(aQ,"CLOSED",{enumerable:!0,value:YP.indexOf("CLOSED")});Object.defineProperty(aQ.prototype,"CLOSED",{enumerable:!0,value:YP.indexOf("CLOSED")});["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach((A)=>{Object.defineProperty(aQ.prototype,A,{enumerable:!0})});["open","error","close","message"].forEach((A)=>{Object.defineProperty(aQ.prototype,`on${A}`,{enumerable:!0,get(){for(let B of this.listeners(A))if(B[gG0])return B[_n4];return null},set(B){for(let Q of this.listeners(A))if(Q[gG0]){this.removeListener(A,Q);break}if(typeof B!=="function")return;this.addEventListener(A,B,{[gG0]:!0})}})});aQ.prototype.addEventListener=vn4;aQ.prototype.removeEventListener=bn4;tN2.exports=aQ;function iN2(A,B,Q,D){let Z={allowSynchronousEvents:!0,autoPong:!0,protocolVersion:uG0[1],maxPayload:*********,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10,...D,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:"GET",host:void 0,path:void 0,port:void 0};if(A._autoPong=Z.autoPong,!uG0.includes(Z.protocolVersion))throw new RangeError(`Unsupported protocol version: ${Z.protocolVersion} (supported versions: ${uG0.join(", ")})`);let G;if(B instanceof hG0)G=B;else try{G=new hG0(B)}catch(H){throw new SyntaxError(`Invalid URL: ${B}`)}if(G.protocol==="http:")G.protocol="ws:";else if(G.protocol==="https:")G.protocol="wss:";A._url=G.href;let F=G.protocol==="wss:",I=G.protocol==="ws+unix:",Y;if(G.protocol!=="ws:"&&!F&&!I)Y=`The URL's protocol must be one of "ws:", "wss:", "http:", "https", or "ws+unix:"`;else if(I&&!G.pathname)Y="The URL's pathname is empty";else if(G.hash)Y="The URL contains a fragment identifier";if(Y){let H=new SyntaxError(Y);if(A._redirects===0)throw H;else{nR1(A,H);return}}let W=F?443:80,J=Tn4(16).toString("base64"),X=F?Mn4.request:Rn4.request,V=new Set,C;if(Z.createConnection=Z.createConnection||(F?dn4:mn4),Z.defaultPort=Z.defaultPort||W,Z.port=G.port||W,Z.host=G.hostname.startsWith("[")?G.hostname.slice(1,-1):G.hostname,Z.headers={...Z.headers,"Sec-WebSocket-Version":Z.protocolVersion,"Sec-WebSocket-Key":J,Connection:"Upgrade",Upgrade:"websocket"},Z.path=G.pathname+G.search,Z.timeout=Z.handshakeTimeout,Z.perMessageDeflate)C=new P_(Z.perMessageDeflate!==!0?Z.perMessageDeflate:{},!1,Z.maxPayload),Z.headers["Sec-WebSocket-Extensions"]=fn4({[P_.extensionName]:C.offer()});if(Q.length){for(let H of Q){if(typeof H!=="string"||!un4.test(H)||V.has(H))throw new SyntaxError("An invalid or duplicated subprotocol was specified");V.add(H)}Z.headers["Sec-WebSocket-Protocol"]=Q.join(",")}if(Z.origin)if(Z.protocolVersion<13)Z.headers["Sec-WebSocket-Origin"]=Z.origin;else Z.headers.Origin=Z.origin;if(G.username||G.password)Z.auth=`${G.username}:${G.password}`;if(I){let H=Z.path.split(":");Z.socketPath=H[0],Z.path=H[1]}let K;if(Z.followRedirects){if(A._redirects===0){A._originalIpc=I,A._originalSecure=F,A._originalHostOrSocketPath=I?Z.socketPath:G.host;let H=D&&D.headers;if(D={...D,headers:{}},H)for(let[z,$]of Object.entries(H))D.headers[z.toLowerCase()]=$}else if(A.listenerCount("redirect")===0){let H=I?A._originalIpc?Z.socketPath===A._originalHostOrSocketPath:!1:A._originalIpc?!1:G.host===A._originalHostOrSocketPath;if(!H||A._originalSecure&&!F){if(delete Z.headers.authorization,delete Z.headers.cookie,!H)delete Z.headers.host;Z.auth=void 0}}if(Z.auth&&!D.headers.authorization)D.headers.authorization="Basic "+Buffer.from(Z.auth).toString("base64");if(K=A._req=X(Z),A._redirects)A.emit("redirect",A.url,K)}else K=A._req=X(Z);if(Z.timeout)K.on("timeout",()=>{SK(A,K,"Opening handshake has timed out")});if(K.on("error",(H)=>{if(K===null||K[pN2])return;K=A._req=null,nR1(A,H)}),K.on("response",(H)=>{let z=H.headers.location,$=H.statusCode;if(z&&Z.followRedirects&&$>=300&&$<400){if(++A._redirects>Z.maxRedirects){SK(A,K,"Maximum redirects exceeded");return}K.abort();let L;try{L=new hG0(z,B)}catch(N){let O=new SyntaxError(`Invalid URL: ${z}`);nR1(A,O);return}iN2(A,L,Q,D)}else if(!A.emit("unexpected-response",K,H))SK(A,K,`Unexpected server response: ${H.statusCode}`)}),K.on("upgrade",(H,z,$)=>{if(A.emit("upgrade",H),A.readyState!==aQ.CONNECTING)return;K=A._req=null;let L=H.headers.upgrade;if(L===void 0||L.toLowerCase()!=="websocket"){SK(A,z,"Invalid Upgrade header");return}let N=Pn4("sha1").update(J+kn4).digest("base64");if(H.headers["sec-websocket-accept"]!==N){SK(A,z,"Invalid Sec-WebSocket-Accept header");return}let O=H.headers["sec-websocket-protocol"],R;if(O!==void 0){if(!V.size)R="Server sent a subprotocol but none was requested";else if(!V.has(O))R="Server sent an invalid subprotocol"}else if(V.size)R="Server sent no subprotocol";if(R){SK(A,z,R);return}if(O)A._protocol=O;let T=H.headers["sec-websocket-extensions"];if(T!==void 0){if(!C){SK(A,z,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");return}let j;try{j=hn4(T)}catch(y){SK(A,z,"Invalid Sec-WebSocket-Extensions header");return}let f=Object.keys(j);if(f.length!==1||f[0]!==P_.extensionName){SK(A,z,"Server indicated an extension that was not requested");return}try{C.accept(j[P_.extensionName])}catch(y){SK(A,z,"Invalid Sec-WebSocket-Extensions header");return}A._extensions[P_.extensionName]=C}A.setSocket(z,$,{allowSynchronousEvents:Z.allowSynchronousEvents,generateMask:Z.generateMask,maxPayload:Z.maxPayload,skipUTF8Validation:Z.skipUTF8Validation})}),Z.finishRequest)Z.finishRequest(K,A);else K.end()}function nR1(A,B){A._readyState=aQ.CLOSING,A._errorEmitted=!0,A.emit("error",B),A.emitClose()}function mn4(A){return A.path=A.socketPath,cN2.connect(A)}function dn4(A){if(A.path=void 0,!A.servername&&A.servername!=="")A.servername=cN2.isIP(A.host)?"":A.host;return On4.connect(A)}function SK(A,B,Q){A._readyState=aQ.CLOSING;let D=new Error(Q);if(Error.captureStackTrace(D,SK),B.setHeader){if(B[pN2]=!0,B.abort(),B.socket&&!B.socket.destroyed)B.socket.destroy();process.nextTick(nR1,A,D)}else B.destroy(D),B.once("error",A.emit.bind(A,"error")),B.once("close",A.emitClose.bind(A))}function mG0(A,B,Q){if(B){let D=yn4(B)?B.size:gn4(B).length;if(A._socket)A._sender._bufferedBytes+=D;else A._bufferedAmount+=D}if(Q){let D=new Error(`WebSocket is not open: readyState ${A.readyState} (${YP[A.readyState]})`);process.nextTick(Q,D)}}function cn4(A,B){let Q=this[UI];if(Q._closeFrameReceived=!0,Q._closeMessage=B,Q._closeCode=A,Q._socket[UI]===void 0)return;if(Q._socket.removeListener("data",aR1),process.nextTick(nN2,Q._socket),A===1005)Q.close();else Q.close(A,B)}function ln4(){let A=this[UI];if(!A.isPaused)A._socket.resume()}function pn4(A){let B=this[UI];if(B._socket[UI]!==void 0)B._socket.removeListener("data",aR1),process.nextTick(nN2,B._socket),B.close(A[xn4]);if(!B._errorEmitted)B._errorEmitted=!0,B.emit("error",A)}function dN2(){this[UI].emitClose()}function in4(A,B){this[UI].emit("message",A,B)}function nn4(A){let B=this[UI];if(B._autoPong)B.pong(A,!this._isServer,lN2);B.emit("ping",A)}function an4(A){this[UI].emit("pong",A)}function nN2(A){A.resume()}function sn4(A){let B=this[UI];if(B.readyState===aQ.CLOSED)return;if(B.readyState===aQ.OPEN)B._readyState=aQ.CLOSING,aN2(B);if(this._socket.end(),!B._errorEmitted)B._errorEmitted=!0,B.emit("error",A)}function aN2(A){A._closeTimer=setTimeout(A._socket.destroy.bind(A._socket),30000)}function sN2(){let A=this[UI];this.removeListener("close",sN2),this.removeListener("data",aR1),this.removeListener("end",rN2),A._readyState=aQ.CLOSING;let B;if(!this._readableState.endEmitted&&!A._closeFrameReceived&&!A._receiver._writableState.errorEmitted&&(B=A._socket.read())!==null)A._receiver.write(B);if(A._receiver.end(),this[UI]=void 0,clearTimeout(A._closeTimer),A._receiver._writableState.finished||A._receiver._writableState.errorEmitted)A.emitClose();else A._receiver.on("error",dN2),A._receiver.on("finish",dN2)}function aR1(A){if(!this[UI]._receiver.write(A))this.pause()}function rN2(){let A=this[UI];A._readyState=aQ.CLOSING,A._receiver.end(),this.end()}function oN2(){let A=this[UI];if(this.removeListener("error",oN2),this.on("error",lN2),A)A._readyState=aQ.CLOSING,this.destroy()}});
var vG0=E((NM5,jN2)=>{var{Duplex:qM5}=J1("stream"),{randomFillSync:Xn4}=J1("crypto"),PN2=M51(),{EMPTY_BUFFER:Vn4,kWebSocket:Cn4,NOOP:Kn4}=FP(),{isBlob:Xo,isValidStatusCode:Hn4}=Jo(),{mask:SN2,toBuffer:Du}=q51(),WE=Symbol("kByteLength"),zn4=Buffer.alloc(4),Zu,Vo=8192,rw=0,En4=1,Un4=2;class O_{constructor(A,B,Q){if(this._extensions=B||{},Q)this._generateMask=Q,this._maskBuffer=Buffer.alloc(4);this._socket=A,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._queue=[],this._state=rw,this.onerror=Kn4,this[Cn4]=void 0}static frame(A,B){let Q,D=!1,Z=2,G=!1;if(B.mask){if(Q=B.maskBuffer||zn4,B.generateMask)B.generateMask(Q);else{if(Vo===8192){if(Zu===void 0)Zu=Buffer.alloc(8192);Xn4(Zu,0,8192),Vo=0}Q[0]=Zu[Vo++],Q[1]=Zu[Vo++],Q[2]=Zu[Vo++],Q[3]=Zu[Vo++]}G=(Q[0]|Q[1]|Q[2]|Q[3])===0,Z=6}let F;if(typeof A==="string")if((!B.mask||G)&&B[WE]!==void 0)F=B[WE];else A=Buffer.from(A),F=A.length;else F=A.length,D=B.mask&&B.readOnly&&!G;let I=F;if(F>=65536)Z+=8,I=127;else if(F>125)Z+=2,I=126;let Y=Buffer.allocUnsafe(D?F+Z:Z);if(Y[0]=B.fin?B.opcode|128:B.opcode,B.rsv1)Y[0]|=64;if(Y[1]=I,I===126)Y.writeUInt16BE(F,2);else if(I===127)Y[2]=Y[3]=0,Y.writeUIntBE(F,4,6);if(!B.mask)return[Y,A];if(Y[1]|=128,Y[Z-4]=Q[0],Y[Z-3]=Q[1],Y[Z-2]=Q[2],Y[Z-1]=Q[3],G)return[Y,A];if(D)return SN2(A,Q,Y,Z,F),[Y];return SN2(A,Q,A,0,F),[Y,A]}close(A,B,Q,D){let Z;if(A===void 0)Z=Vn4;else if(typeof A!=="number"||!Hn4(A))throw new TypeError("First argument must be a valid error code number");else if(B===void 0||!B.length)Z=Buffer.allocUnsafe(2),Z.writeUInt16BE(A,0);else{let F=Buffer.byteLength(B);if(F>123)throw new RangeError("The message must not be greater than 123 bytes");if(Z=Buffer.allocUnsafe(2+F),Z.writeUInt16BE(A,0),typeof B==="string")Z.write(B,2);else Z.set(B,2)}let G={[WE]:Z.length,fin:!0,generateMask:this._generateMask,mask:Q,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};if(this._state!==rw)this.enqueue([this.dispatch,Z,!1,G,D]);else this.sendFrame(O_.frame(Z,G),D)}ping(A,B,Q){let D,Z;if(typeof A==="string")D=Buffer.byteLength(A),Z=!1;else if(Xo(A))D=A.size,Z=!1;else A=Du(A),D=A.length,Z=Du.readOnly;if(D>125)throw new RangeError("The data size must not be greater than 125 bytes");let G={[WE]:D,fin:!0,generateMask:this._generateMask,mask:B,maskBuffer:this._maskBuffer,opcode:9,readOnly:Z,rsv1:!1};if(Xo(A))if(this._state!==rw)this.enqueue([this.getBlobData,A,!1,G,Q]);else this.getBlobData(A,!1,G,Q);else if(this._state!==rw)this.enqueue([this.dispatch,A,!1,G,Q]);else this.sendFrame(O_.frame(A,G),Q)}pong(A,B,Q){let D,Z;if(typeof A==="string")D=Buffer.byteLength(A),Z=!1;else if(Xo(A))D=A.size,Z=!1;else A=Du(A),D=A.length,Z=Du.readOnly;if(D>125)throw new RangeError("The data size must not be greater than 125 bytes");let G={[WE]:D,fin:!0,generateMask:this._generateMask,mask:B,maskBuffer:this._maskBuffer,opcode:10,readOnly:Z,rsv1:!1};if(Xo(A))if(this._state!==rw)this.enqueue([this.getBlobData,A,!1,G,Q]);else this.getBlobData(A,!1,G,Q);else if(this._state!==rw)this.enqueue([this.dispatch,A,!1,G,Q]);else this.sendFrame(O_.frame(A,G),Q)}send(A,B,Q){let D=this._extensions[PN2.extensionName],Z=B.binary?2:1,G=B.compress,F,I;if(typeof A==="string")F=Buffer.byteLength(A),I=!1;else if(Xo(A))F=A.size,I=!1;else A=Du(A),F=A.length,I=Du.readOnly;if(this._firstFragment){if(this._firstFragment=!1,G&&D&&D.params[D._isServer?"server_no_context_takeover":"client_no_context_takeover"])G=F>=D._threshold;this._compress=G}else G=!1,Z=0;if(B.fin)this._firstFragment=!0;let Y={[WE]:F,fin:B.fin,generateMask:this._generateMask,mask:B.mask,maskBuffer:this._maskBuffer,opcode:Z,readOnly:I,rsv1:G};if(Xo(A))if(this._state!==rw)this.enqueue([this.getBlobData,A,this._compress,Y,Q]);else this.getBlobData(A,this._compress,Y,Q);else if(this._state!==rw)this.enqueue([this.dispatch,A,this._compress,Y,Q]);else this.dispatch(A,this._compress,Y,Q)}getBlobData(A,B,Q,D){this._bufferedBytes+=Q[WE],this._state=Un4,A.arrayBuffer().then((Z)=>{if(this._socket.destroyed){let F=new Error("The socket was closed while the blob was being read");process.nextTick(xG0,this,F,D);return}this._bufferedBytes-=Q[WE];let G=Du(Z);if(!B)this._state=rw,this.sendFrame(O_.frame(G,Q),D),this.dequeue();else this.dispatch(G,B,Q,D)}).catch((Z)=>{process.nextTick(wn4,this,Z,D)})}dispatch(A,B,Q,D){if(!B){this.sendFrame(O_.frame(A,Q),D);return}let Z=this._extensions[PN2.extensionName];this._bufferedBytes+=Q[WE],this._state=En4,Z.compress(A,Q.fin,(G,F)=>{if(this._socket.destroyed){let I=new Error("The socket was closed while data was being compressed");xG0(this,I,D);return}this._bufferedBytes-=Q[WE],this._state=rw,Q.readOnly=!1,this.sendFrame(O_.frame(F,Q),D),this.dequeue()})}dequeue(){while(this._state===rw&&this._queue.length){let A=this._queue.shift();this._bufferedBytes-=A[3][WE],Reflect.apply(A[0],this,A.slice(1))}}enqueue(A){this._bufferedBytes+=A[3][WE],this._queue.push(A)}sendFrame(A,B){if(A.length===2)this._socket.cork(),this._socket.write(A[0]),this._socket.write(A[1],B),this._socket.uncork();else this._socket.write(A[0],B)}}jN2.exports=O_;function xG0(A,B,Q){if(typeof Q==="function")Q(B);for(let D=0;D<A._queue.length;D++){let Z=A._queue[D],G=Z[Z.length-1];if(typeof G==="function")G(B)}}function wn4(A,B,Q){xG0(A,B,Q),A.onerror(B)}});
var zN2=E((EM5,HN2)=>{var CN2=Symbol("kDone"),jG0=Symbol("kRun");class KN2{constructor(A){this[CN2]=()=>{this.pending--,this[jG0]()},this.concurrency=A||1/0,this.jobs=[],this.pending=0}add(A){this.jobs.push(A),this[jG0]()}[jG0](){if(this.pending===this.concurrency)return;if(this.jobs.length){let A=this.jobs.shift();this.pending++,A(this[CN2])}}}HN2.exports=KN2});

// Export all variables
module.exports = {
  FP,
  Jo,
  M51,
  QL2,
  _G0,
  fG0,
  gN2,
  q51,
  sR1,
  vG0,
  zN2
};
