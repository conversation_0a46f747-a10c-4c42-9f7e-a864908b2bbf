// Package extracted with entry point: wOB
// Contains 10 variables: $q0, KOB, Lq0, Nq0, Pv1, Tv1, aRB, lRB, wOB, wq0

var $q0=E((nY3,dRB)=>{dRB.exports={indexOf:function(A,B){var Q,D;if(Array.prototype.indexOf)return A.indexOf(B);for(Q=0,D=A.length;Q<D;Q++)if(A[Q]===B)return Q;return-1},forEach:function(A,B,Q){var D,Z;if(Array.prototype.forEach)return A.forEach(B,Q);for(D=0,Z=A.length;D<Z;D++)B.call(Q,A[D],D,A)},trim:function(A){if(String.prototype.trim)return A.trim();return A.replace(/(^\s*)|(\s*$)/g,"")},trimRight:function(A){if(String.prototype.trimRight)return A.trimRight();return A.replace(/(\s*$)/g,"")}}});
var KOB=E((AW3,COB)=>{var V38=Tv1().FilterCSS,aE=Nq0(),XOB=Lq0(),C38=XOB.parseTag,K38=XOB.parseAttr,kv1=Pv1();function yv1(A){return A===void 0||A===null}function H38(A){var B=kv1.spaceIndex(A);if(B===-1)return{html:"",closing:A[A.length-2]==="/"};A=kv1.trim(A.slice(B+1,-1));var Q=A[A.length-1]==="/";if(Q)A=kv1.trim(A.slice(0,-1));return{html:A,closing:Q}}function z38(A){var B={};for(var Q in A)B[Q]=A[Q];return B}function E38(A){var B={};for(var Q in A)if(Array.isArray(A[Q]))B[Q.toLowerCase()]=A[Q].map(function(D){return D.toLowerCase()});else B[Q.toLowerCase()]=A[Q];return B}function VOB(A){if(A=z38(A||{}),A.stripIgnoreTag){if(A.onIgnoreTag)console.error('Notes: cannot use these two options "stripIgnoreTag" and "onIgnoreTag" at the same time');A.onIgnoreTag=aE.onIgnoreTagStripAll}if(A.whiteList||A.allowList)A.whiteList=E38(A.whiteList||A.allowList);else A.whiteList=aE.whiteList;if(this.attributeWrapSign=A.singleQuotedAttributeValue===!0?"'":aE.attributeWrapSign,A.onTag=A.onTag||aE.onTag,A.onTagAttr=A.onTagAttr||aE.onTagAttr,A.onIgnoreTag=A.onIgnoreTag||aE.onIgnoreTag,A.onIgnoreTagAttr=A.onIgnoreTagAttr||aE.onIgnoreTagAttr,A.safeAttrValue=A.safeAttrValue||aE.safeAttrValue,A.escapeHtml=A.escapeHtml||aE.escapeHtml,this.options=A,A.css===!1)this.cssFilter=!1;else A.css=A.css||{},this.cssFilter=new V38(A.css)}VOB.prototype.process=function(A){if(A=A||"",A=A.toString(),!A)return"";var B=this,Q=B.options,D=Q.whiteList,Z=Q.onTag,G=Q.onIgnoreTag,F=Q.onTagAttr,I=Q.onIgnoreTagAttr,Y=Q.safeAttrValue,W=Q.escapeHtml,J=B.attributeWrapSign,X=B.cssFilter;if(Q.stripBlankChar)A=aE.stripBlankChar(A);if(!Q.allowCommentTag)A=aE.stripCommentTag(A);var V=!1;if(Q.stripIgnoreTagBody)V=aE.StripTagBody(Q.stripIgnoreTagBody,G),G=V.onIgnoreTag;var C=C38(A,function(K,H,z,$,L){var N={sourcePosition:K,position:H,isClosing:L,isWhite:Object.prototype.hasOwnProperty.call(D,z)},O=Z(z,$,N);if(!yv1(O))return O;if(N.isWhite){if(N.isClosing)return"</"+z+">";var R=H38($),T=D[z],j=K38(R.html,function(f,y){var c=kv1.indexOf(T,f)!==-1,h=F(z,f,y,c);if(!yv1(h))return h;if(c)if(y=Y(z,f,y,X),y)return f+"="+J+y+J;else return f;else{if(h=I(z,f,y,c),!yv1(h))return h;return}});if($="<"+z,j)$+=" "+j;if(R.closing)$+=" /";return $+=">",$}else{if(O=G(z,$,N),!yv1(O))return O;return W($)}},W);if(V)C=V.remove(C);return C};COB.exports=VOB});
var Lq0=E((W38)=>{var Cv=Pv1();function A38(A){var B=Cv.spaceIndex(A),Q;if(B===-1)Q=A.slice(1,-1);else Q=A.slice(1,B+1);if(Q=Cv.trim(Q).toLowerCase(),Q.slice(0,1)==="/")Q=Q.slice(1);if(Q.slice(-1)==="/")Q=Q.slice(0,-1);return Q}function B38(A){return A.slice(0,2)==="</"}function Q38(A,B,Q){var D="",Z=0,G=!1,F=!1,I=0,Y=A.length,W="",J="";A:for(I=0;I<Y;I++){var X=A.charAt(I);if(G===!1){if(X==="<"){G=I;continue}}else if(F===!1){if(X==="<"){D+=Q(A.slice(Z,I)),G=I,Z=I;continue}if(X===">"||I===Y-1){D+=Q(A.slice(Z,G)),J=A.slice(G,I+1),W=A38(J),D+=B(G,D.length,W,J,B38(J)),Z=I+1,G=!1;continue}if(X==='"'||X==="'"){var V=1,C=A.charAt(I-V);while(C.trim()===""||C==="="){if(C==="="){F=X;continue A}C=A.charAt(I-++V)}}}else if(X===F){F=!1;continue}}if(Z<Y)D+=Q(A.substr(Z));return D}var D38=/[^a-zA-Z0-9\\_:.-]/gim;function Z38(A,B){var Q=0,D=0,Z=[],G=!1,F=A.length;function I(V,C){if(V=Cv.trim(V),V=V.replace(D38,"").toLowerCase(),V.length<1)return;var K=B(V,C||"");if(K)Z.push(K)}for(var Y=0;Y<F;Y++){var W=A.charAt(Y),J,X;if(G===!1&&W==="="){G=A.slice(Q,Y),Q=Y+1,D=A.charAt(Q)==='"'||A.charAt(Q)==="'"?Q:F38(A,Y+1);continue}if(G!==!1){if(Y===D)if(X=A.indexOf(W,Y+1),X===-1)break;else{J=Cv.trim(A.slice(D+1,X)),I(G,J),G=!1,Y=X,Q=Y+1;continue}}if(/\s|\n|\t/.test(W))if(A=A.replace(/\s|\n|\t/g," "),G===!1)if(X=G38(A,Y),X===-1){J=Cv.trim(A.slice(Q,Y)),I(J),G=!1,Q=Y+1;continue}else{Y=X-1;continue}else if(X=I38(A,Y-1),X===-1){J=Cv.trim(A.slice(Q,Y)),J=JOB(J),I(G,J),G=!1,Q=Y+1;continue}else continue}if(Q<A.length)if(G===!1)I(A.slice(Q));else I(G,JOB(Cv.trim(A.slice(Q))));return Cv.trim(Z.join(" "))}function G38(A,B){for(;B<A.length;B++){var Q=A[B];if(Q===" ")continue;if(Q==="=")return B;return-1}}function F38(A,B){for(;B<A.length;B++){var Q=A[B];if(Q===" ")continue;if(Q==="'"||Q==='"')return B;return-1}}function I38(A,B){for(;B>0;B--){var Q=A[B];if(Q===" ")continue;if(Q==="=")return B;return-1}}function Y38(A){if(A[0]==='"'&&A[A.length-1]==='"'||A[0]==="'"&&A[A.length-1]==="'")return!0;else return!1}function JOB(A){if(Y38(A))return A.substr(1,A.length-2);else return A}W38.parseTag=Q38;W38.parseAttr=Z38});
var Nq0=E((y58)=>{var C58=Tv1().FilterCSS,K58=Tv1().getDefaultWhiteList,jv1=Pv1();function AOB(){return{a:["target","href","title"],abbr:["title"],address:[],area:["shape","coords","href","alt"],article:[],aside:[],audio:["autoplay","controls","crossorigin","loop","muted","preload","src"],b:[],bdi:["dir"],bdo:["dir"],big:[],blockquote:["cite"],br:[],caption:[],center:[],cite:[],code:[],col:["align","valign","span","width"],colgroup:["align","valign","span","width"],dd:[],del:["datetime"],details:["open"],div:[],dl:[],dt:[],em:[],figcaption:[],figure:[],font:["color","size","face"],footer:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],header:[],hr:[],i:[],img:["src","alt","title","width","height","loading"],ins:["datetime"],kbd:[],li:[],mark:[],nav:[],ol:[],p:[],pre:[],s:[],section:[],small:[],span:[],sub:[],summary:[],sup:[],strong:[],strike:[],table:["width","border","align","valign"],tbody:["align","valign"],td:["width","rowspan","colspan","align","valign"],tfoot:["align","valign"],th:["width","rowspan","colspan","align","valign"],thead:["align","valign"],tr:["rowspan","align","valign"],tt:[],u:[],ul:[],video:["autoplay","controls","crossorigin","loop","muted","playsinline","poster","preload","src","height","width"]}}var BOB=new C58;function H58(A,B,Q){}function z58(A,B,Q){}function E58(A,B,Q){}function U58(A,B,Q){}function QOB(A){return A.replace($58,"&lt;").replace(q58,"&gt;")}function w58(A,B,Q,D){if(Q=YOB(Q),B==="href"||B==="src"){if(Q=jv1.trim(Q),Q==="#")return"#";if(!(Q.substr(0,7)==="http://"||Q.substr(0,8)==="https://"||Q.substr(0,7)==="mailto:"||Q.substr(0,4)==="tel:"||Q.substr(0,11)==="data:image/"||Q.substr(0,6)==="ftp://"||Q.substr(0,2)==="./"||Q.substr(0,3)==="../"||Q[0]==="#"||Q[0]==="/"))return""}else if(B==="background"){if(Sv1.lastIndex=0,Sv1.test(Q))return""}else if(B==="style"){if(tRB.lastIndex=0,tRB.test(Q))return"";if(eRB.lastIndex=0,eRB.test(Q)){if(Sv1.lastIndex=0,Sv1.test(Q))return""}if(D!==!1)D=D||BOB,Q=D.process(Q)}return Q=WOB(Q),Q}var $58=/</g,q58=/>/g,N58=/"/g,L58=/&quot;/g,M58=/&#([a-zA-Z0-9]*);?/gim,R58=/&colon;?/gim,O58=/&newline;?/gim,Sv1=/((j\s*a\s*v\s*a|v\s*b|l\s*i\s*v\s*e)\s*s\s*c\s*r\s*i\s*p\s*t\s*|m\s*o\s*c\s*h\s*a):/gi,tRB=/e\s*x\s*p\s*r\s*e\s*s\s*s\s*i\s*o\s*n\s*\(.*/gi,eRB=/u\s*r\s*l\s*\(.*/gi;function DOB(A){return A.replace(N58,"&quot;")}function ZOB(A){return A.replace(L58,'"')}function GOB(A){return A.replace(M58,function B(Q,D){return D[0]==="x"||D[0]==="X"?String.fromCharCode(parseInt(D.substr(1),16)):String.fromCharCode(parseInt(D,10))})}function FOB(A){return A.replace(R58,":").replace(O58," ")}function IOB(A){var B="";for(var Q=0,D=A.length;Q<D;Q++)B+=A.charCodeAt(Q)<32?" ":A.charAt(Q);return jv1.trim(B)}function YOB(A){return A=ZOB(A),A=GOB(A),A=FOB(A),A=IOB(A),A}function WOB(A){return A=DOB(A),A=QOB(A),A}function T58(){return""}function P58(A,B){if(typeof B!=="function")B=function(){};var Q=!Array.isArray(A);function D(F){if(Q)return!0;return jv1.indexOf(A,F)!==-1}var Z=[],G=!1;return{onIgnoreTag:function(F,I,Y){if(D(F))if(Y.isClosing){var W="[/removed]",J=Y.position+W.length;return Z.push([G!==!1?G:Y.position,J]),G=!1,W}else{if(!G)G=Y.position;return"[removed]"}else return B(F,I,Y)},remove:function(F){var I="",Y=0;return jv1.forEach(Z,function(W){I+=F.slice(Y,W[0]),Y=W[1]}),I+=F.slice(Y),I}}}function S58(A){var B="",Q=0;while(Q<A.length){var D=A.indexOf("<!--",Q);if(D===-1){B+=A.slice(Q);break}B+=A.slice(Q,D);var Z=A.indexOf("-->",D);if(Z===-1)break;Q=Z+3}return B}function j58(A){var B=A.split("");return B=B.filter(function(Q){var D=Q.charCodeAt(0);if(D===127)return!1;if(D<=31){if(D===10||D===13)return!0;return!1}return!0}),B.join("")}y58.whiteList=AOB();y58.getDefaultWhiteList=AOB;y58.onTag=H58;y58.onIgnoreTag=z58;y58.onTagAttr=E58;y58.onIgnoreTagAttr=U58;y58.safeAttrValue=w58;y58.escapeHtml=QOB;y58.escapeQuote=DOB;y58.unescapeQuote=ZOB;y58.escapeHtmlEntities=GOB;y58.escapeDangerHtml5Entities=FOB;y58.clearNonPrintableCharacter=IOB;y58.friendlyAttrValue=YOB;y58.escapeAttrValue=WOB;y58.onIgnoreTagStripAll=T58;y58.StripTagBody=P58;y58.stripCommentTag=S58;y58.stripBlankChar=j58;y58.attributeWrapSign='"';y58.cssFilter=BOB;y58.getDefaultCSSWhiteList=K58});
var Pv1=E((oY3,oRB)=>{oRB.exports={indexOf:function(A,B){var Q,D;if(Array.prototype.indexOf)return A.indexOf(B);for(Q=0,D=A.length;Q<D;Q++)if(A[Q]===B)return Q;return-1},forEach:function(A,B,Q){var D,Z;if(Array.prototype.forEach)return A.forEach(B,Q);for(D=0,Z=A.length;D<Z;D++)B.call(Q,A[D],D,A)},trim:function(A){if(String.prototype.trim)return A.trim();return A.replace(/(^\s*)|(\s*$)/g,"")},spaceIndex:function(A){var B=/\s|\n|\t/,Q=B.exec(A);return Q?Q.index:-1}}});
var Tv1=E((Ov1,qq0)=>{var sRB=wq0(),rRB=aRB();function V58(A,B){var Q=new rRB(B);return Q.process(A)}Ov1=qq0.exports=V58;Ov1.FilterCSS=rRB;for(Rv1 in sRB)Ov1[Rv1]=sRB[Rv1];var Rv1;if(typeof window!=="undefined")window.filterCSS=qq0.exports});
var aRB=E((rY3,nRB)=>{var Mv1=wq0(),J58=lRB(),sY3=$q0();function pRB(A){return A===void 0||A===null}function X58(A){var B={};for(var Q in A)B[Q]=A[Q];return B}function iRB(A){A=X58(A||{}),A.whiteList=A.whiteList||Mv1.whiteList,A.onAttr=A.onAttr||Mv1.onAttr,A.onIgnoreAttr=A.onIgnoreAttr||Mv1.onIgnoreAttr,A.safeAttrValue=A.safeAttrValue||Mv1.safeAttrValue,this.options=A}iRB.prototype.process=function(A){if(A=A||"",A=A.toString(),!A)return"";var B=this,Q=B.options,D=Q.whiteList,Z=Q.onAttr,G=Q.onIgnoreAttr,F=Q.safeAttrValue,I=J58(A,function(Y,W,J,X,V){var C=D[J],K=!1;if(C===!0)K=C;else if(typeof C==="function")K=C(X);else if(C instanceof RegExp)K=C.test(X);if(K!==!0)K=!1;if(X=F(J,X),!X)return;var H={position:W,sourcePosition:Y,source:V,isWhite:K};if(K){var z=Z(J,X,H);if(pRB(z))return J+":"+X;else return z}else{var z=G(J,X,H);if(!pRB(z))return z}});return I};nRB.exports=iRB});
var lRB=E((aY3,cRB)=>{var eZ1=$q0();function W58(A,B){if(A=eZ1.trimRight(A),A[A.length-1]!==";")A+=";";var Q=A.length,D=!1,Z=0,G=0,F="";function I(){if(!D){var J=eZ1.trim(A.slice(Z,G)),X=J.indexOf(":");if(X!==-1){var V=eZ1.trim(J.slice(0,X)),C=eZ1.trim(J.slice(X+1));if(V){var K=B(Z,F.length,V,C,J);if(K)F+=K+"; "}}}Z=G+1}for(;G<Q;G++){var Y=A[G];if(Y==="/"&&A[G+1]==="*"){var W=A.indexOf("*/",G+2);if(W===-1)break;G=W+1,Z=G+1,D=!1}else if(Y==="(")D=!0;else if(Y===")")D=!1;else if(Y===";")if(D);else I();else if(Y===`
`)I()}return eZ1.trim(F)}cRB.exports=W58});
var wOB=E((y11,_v1)=>{var HOB=Nq0(),zOB=Lq0(),EOB=KOB();function UOB(A,B){var Q=new EOB(B);return Q.process(A)}y11=_v1.exports=UOB;y11.filterXSS=UOB;y11.FilterXSS=EOB;(function(){for(var A in HOB)y11[A]=HOB[A];for(var B in zOB)y11[B]=zOB[B]})();if(typeof window!=="undefined")window.filterXSS=_v1.exports;function U38(){return typeof self!=="undefined"&&typeof DedicatedWorkerGlobalScope!=="undefined"&&self instanceof DedicatedWorkerGlobalScope}if(U38())self.filterXSS=_v1.exports});
var wq0=E((D58)=>{function mRB(){var A={};return A["align-content"]=!1,A["align-items"]=!1,A["align-self"]=!1,A["alignment-adjust"]=!1,A["alignment-baseline"]=!1,A.all=!1,A["anchor-point"]=!1,A.animation=!1,A["animation-delay"]=!1,A["animation-direction"]=!1,A["animation-duration"]=!1,A["animation-fill-mode"]=!1,A["animation-iteration-count"]=!1,A["animation-name"]=!1,A["animation-play-state"]=!1,A["animation-timing-function"]=!1,A.azimuth=!1,A["backface-visibility"]=!1,A.background=!0,A["background-attachment"]=!0,A["background-clip"]=!0,A["background-color"]=!0,A["background-image"]=!0,A["background-origin"]=!0,A["background-position"]=!0,A["background-repeat"]=!0,A["background-size"]=!0,A["baseline-shift"]=!1,A.binding=!1,A.bleed=!1,A["bookmark-label"]=!1,A["bookmark-level"]=!1,A["bookmark-state"]=!1,A.border=!0,A["border-bottom"]=!0,A["border-bottom-color"]=!0,A["border-bottom-left-radius"]=!0,A["border-bottom-right-radius"]=!0,A["border-bottom-style"]=!0,A["border-bottom-width"]=!0,A["border-collapse"]=!0,A["border-color"]=!0,A["border-image"]=!0,A["border-image-outset"]=!0,A["border-image-repeat"]=!0,A["border-image-slice"]=!0,A["border-image-source"]=!0,A["border-image-width"]=!0,A["border-left"]=!0,A["border-left-color"]=!0,A["border-left-style"]=!0,A["border-left-width"]=!0,A["border-radius"]=!0,A["border-right"]=!0,A["border-right-color"]=!0,A["border-right-style"]=!0,A["border-right-width"]=!0,A["border-spacing"]=!0,A["border-style"]=!0,A["border-top"]=!0,A["border-top-color"]=!0,A["border-top-left-radius"]=!0,A["border-top-right-radius"]=!0,A["border-top-style"]=!0,A["border-top-width"]=!0,A["border-width"]=!0,A.bottom=!1,A["box-decoration-break"]=!0,A["box-shadow"]=!0,A["box-sizing"]=!0,A["box-snap"]=!0,A["box-suppress"]=!0,A["break-after"]=!0,A["break-before"]=!0,A["break-inside"]=!0,A["caption-side"]=!1,A.chains=!1,A.clear=!0,A.clip=!1,A["clip-path"]=!1,A["clip-rule"]=!1,A.color=!0,A["color-interpolation-filters"]=!0,A["column-count"]=!1,A["column-fill"]=!1,A["column-gap"]=!1,A["column-rule"]=!1,A["column-rule-color"]=!1,A["column-rule-style"]=!1,A["column-rule-width"]=!1,A["column-span"]=!1,A["column-width"]=!1,A.columns=!1,A.contain=!1,A.content=!1,A["counter-increment"]=!1,A["counter-reset"]=!1,A["counter-set"]=!1,A.crop=!1,A.cue=!1,A["cue-after"]=!1,A["cue-before"]=!1,A.cursor=!1,A.direction=!1,A.display=!0,A["display-inside"]=!0,A["display-list"]=!0,A["display-outside"]=!0,A["dominant-baseline"]=!1,A.elevation=!1,A["empty-cells"]=!1,A.filter=!1,A.flex=!1,A["flex-basis"]=!1,A["flex-direction"]=!1,A["flex-flow"]=!1,A["flex-grow"]=!1,A["flex-shrink"]=!1,A["flex-wrap"]=!1,A.float=!1,A["float-offset"]=!1,A["flood-color"]=!1,A["flood-opacity"]=!1,A["flow-from"]=!1,A["flow-into"]=!1,A.font=!0,A["font-family"]=!0,A["font-feature-settings"]=!0,A["font-kerning"]=!0,A["font-language-override"]=!0,A["font-size"]=!0,A["font-size-adjust"]=!0,A["font-stretch"]=!0,A["font-style"]=!0,A["font-synthesis"]=!0,A["font-variant"]=!0,A["font-variant-alternates"]=!0,A["font-variant-caps"]=!0,A["font-variant-east-asian"]=!0,A["font-variant-ligatures"]=!0,A["font-variant-numeric"]=!0,A["font-variant-position"]=!0,A["font-weight"]=!0,A.grid=!1,A["grid-area"]=!1,A["grid-auto-columns"]=!1,A["grid-auto-flow"]=!1,A["grid-auto-rows"]=!1,A["grid-column"]=!1,A["grid-column-end"]=!1,A["grid-column-start"]=!1,A["grid-row"]=!1,A["grid-row-end"]=!1,A["grid-row-start"]=!1,A["grid-template"]=!1,A["grid-template-areas"]=!1,A["grid-template-columns"]=!1,A["grid-template-rows"]=!1,A["hanging-punctuation"]=!1,A.height=!0,A.hyphens=!1,A.icon=!1,A["image-orientation"]=!1,A["image-resolution"]=!1,A["ime-mode"]=!1,A["initial-letters"]=!1,A["inline-box-align"]=!1,A["justify-content"]=!1,A["justify-items"]=!1,A["justify-self"]=!1,A.left=!1,A["letter-spacing"]=!0,A["lighting-color"]=!0,A["line-box-contain"]=!1,A["line-break"]=!1,A["line-grid"]=!1,A["line-height"]=!1,A["line-snap"]=!1,A["line-stacking"]=!1,A["line-stacking-ruby"]=!1,A["line-stacking-shift"]=!1,A["line-stacking-strategy"]=!1,A["list-style"]=!0,A["list-style-image"]=!0,A["list-style-position"]=!0,A["list-style-type"]=!0,A.margin=!0,A["margin-bottom"]=!0,A["margin-left"]=!0,A["margin-right"]=!0,A["margin-top"]=!0,A["marker-offset"]=!1,A["marker-side"]=!1,A.marks=!1,A.mask=!1,A["mask-box"]=!1,A["mask-box-outset"]=!1,A["mask-box-repeat"]=!1,A["mask-box-slice"]=!1,A["mask-box-source"]=!1,A["mask-box-width"]=!1,A["mask-clip"]=!1,A["mask-image"]=!1,A["mask-origin"]=!1,A["mask-position"]=!1,A["mask-repeat"]=!1,A["mask-size"]=!1,A["mask-source-type"]=!1,A["mask-type"]=!1,A["max-height"]=!0,A["max-lines"]=!1,A["max-width"]=!0,A["min-height"]=!0,A["min-width"]=!0,A["move-to"]=!1,A["nav-down"]=!1,A["nav-index"]=!1,A["nav-left"]=!1,A["nav-right"]=!1,A["nav-up"]=!1,A["object-fit"]=!1,A["object-position"]=!1,A.opacity=!1,A.order=!1,A.orphans=!1,A.outline=!1,A["outline-color"]=!1,A["outline-offset"]=!1,A["outline-style"]=!1,A["outline-width"]=!1,A.overflow=!1,A["overflow-wrap"]=!1,A["overflow-x"]=!1,A["overflow-y"]=!1,A.padding=!0,A["padding-bottom"]=!0,A["padding-left"]=!0,A["padding-right"]=!0,A["padding-top"]=!0,A.page=!1,A["page-break-after"]=!1,A["page-break-before"]=!1,A["page-break-inside"]=!1,A["page-policy"]=!1,A.pause=!1,A["pause-after"]=!1,A["pause-before"]=!1,A.perspective=!1,A["perspective-origin"]=!1,A.pitch=!1,A["pitch-range"]=!1,A["play-during"]=!1,A.position=!1,A["presentation-level"]=!1,A.quotes=!1,A["region-fragment"]=!1,A.resize=!1,A.rest=!1,A["rest-after"]=!1,A["rest-before"]=!1,A.richness=!1,A.right=!1,A.rotation=!1,A["rotation-point"]=!1,A["ruby-align"]=!1,A["ruby-merge"]=!1,A["ruby-position"]=!1,A["shape-image-threshold"]=!1,A["shape-outside"]=!1,A["shape-margin"]=!1,A.size=!1,A.speak=!1,A["speak-as"]=!1,A["speak-header"]=!1,A["speak-numeral"]=!1,A["speak-punctuation"]=!1,A["speech-rate"]=!1,A.stress=!1,A["string-set"]=!1,A["tab-size"]=!1,A["table-layout"]=!1,A["text-align"]=!0,A["text-align-last"]=!0,A["text-combine-upright"]=!0,A["text-decoration"]=!0,A["text-decoration-color"]=!0,A["text-decoration-line"]=!0,A["text-decoration-skip"]=!0,A["text-decoration-style"]=!0,A["text-emphasis"]=!0,A["text-emphasis-color"]=!0,A["text-emphasis-position"]=!0,A["text-emphasis-style"]=!0,A["text-height"]=!0,A["text-indent"]=!0,A["text-justify"]=!0,A["text-orientation"]=!0,A["text-overflow"]=!0,A["text-shadow"]=!0,A["text-space-collapse"]=!0,A["text-transform"]=!0,A["text-underline-position"]=!0,A["text-wrap"]=!0,A.top=!1,A.transform=!1,A["transform-origin"]=!1,A["transform-style"]=!1,A.transition=!1,A["transition-delay"]=!1,A["transition-duration"]=!1,A["transition-property"]=!1,A["transition-timing-function"]=!1,A["unicode-bidi"]=!1,A["vertical-align"]=!1,A.visibility=!1,A["voice-balance"]=!1,A["voice-duration"]=!1,A["voice-family"]=!1,A["voice-pitch"]=!1,A["voice-range"]=!1,A["voice-rate"]=!1,A["voice-stress"]=!1,A["voice-volume"]=!1,A.volume=!1,A["white-space"]=!1,A.widows=!1,A.width=!0,A["will-change"]=!1,A["word-break"]=!0,A["word-spacing"]=!0,A["word-wrap"]=!0,A["wrap-flow"]=!1,A["wrap-through"]=!1,A["writing-mode"]=!1,A["z-index"]=!1,A}function e88(A,B,Q){}function A58(A,B,Q){}var B58=/javascript\s*\:/img;function Q58(A,B){if(B58.test(B))return"";return B}D58.whiteList=mRB();D58.getDefaultWhiteList=mRB;D58.onAttr=e88;D58.onIgnoreAttr=A58;D58.safeAttrValue=Q58});

// Export all variables
module.exports = {
  $q0,
  KOB,
  Lq0,
  Nq0,
  Pv1,
  Tv1,
  aRB,
  lRB,
  wOB,
  wq0
};
