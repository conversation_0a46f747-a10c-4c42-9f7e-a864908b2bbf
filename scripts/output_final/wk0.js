// Package extracted with entry point: wk0
// Contains 1 variables: wk0

var wk0=E((zg8,Uk0)=>{var{PassThrough:ZeB}=J1("stream");Uk0.exports=function(){var A=[],B=new ZeB({objectMode:!0});return B.setMaxListeners(0),B.add=Q,B.isEmpty=D,B.on("unpipe",Z),Array.prototype.slice.call(arguments).forEach(Q),B;function Q(G){if(Array.isArray(G))return G.forEach(Q),this;return A.push(G),G.once("end",Z.bind(null,G)),G.once("error",B.emit.bind(B,"error")),G.pipe(B,{end:!1}),this}function D(){return A.length==0}function Z(G){if(A=A.filter(function(F){return F!==G}),!A.length&&B.readable)B.end()}}});

// Export all variables
module.exports = {
  wk0
};
