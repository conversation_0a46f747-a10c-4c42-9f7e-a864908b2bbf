// Package extracted with entry point: Dm1
// Contains 13 variables: Cy0, Dm1, Hy0, My0, Ny0, Py0, Sy0, Yy0, _y0, dy0... (and 3 more)

var Cy0=E((Ah8,Vy0)=>{Vy0.exports=Jy0;Jy0.sync=xoB;var Wy0=J1("fs");function Jy0(A,B,Q){Wy0.stat(A,function(D,Z){Q(D,D?!1:Xy0(Z,B))})}function xoB(A,B){return Xy0(Wy0.statSync(A),B)}function Xy0(A,B){return A.isFile()&&voB(A,B)}function voB(A,B){var{mode:Q,uid:D,gid:Z}=A,G=B.uid!==void 0?B.uid:process.getuid&&process.getuid(),F=B.gid!==void 0?B.gid:process.getgid&&process.getgid(),I=parseInt("100",8),Y=parseInt("010",8),W=parseInt("001",8),J=I|Y,X=Q&W||Q&Y&&Z===F||Q&I&&D===G||Q&J&&G===0;return X}});
var Dm1=E((Xh8,tc)=>{var cy0=J1("child_process"),Bm1=gy0(),Qm1=dy0();function ly0(A,B,Q){let D=Bm1(A,B,Q),Z=cy0.spawn(D.command,D.args,D.options);return Qm1.hookChildProcess(Z,D),Z}function FtB(A,B,Q){let D=Bm1(A,B,Q),Z=cy0.spawnSync(D.command,D.args,D.options);return Z.error=Z.error||Qm1.verifyENOENTSync(Z.status,D),Z}tc.exports=ly0;tc.exports.spawn=ly0;tc.exports.sync=FtB;tc.exports._parse=Bm1;tc.exports._enoent=Qm1});
var Hy0=E((Qh8,Ky0)=>{var Bh8=J1("fs"),HW1;if(process.platform==="win32"||global.TESTING_WINDOWS)HW1=Yy0();else HW1=Cy0();Ky0.exports=au1;au1.sync=boB;function au1(A,B,Q){if(typeof B==="function")Q=B,B={};if(!Q){if(typeof Promise!=="function")throw new TypeError("callback not provided");return new Promise(function(D,Z){au1(A,B||{},function(G,F){if(G)Z(G);else D(F)})})}HW1(A,B||{},function(D,Z){if(D){if(D.code==="EACCES"||B&&B.ignoreErrors)D=null,Z=!1}Q(D,Z)})}function boB(A,B){try{return HW1.sync(A,B||{})}catch(Q){if(B&&B.ignoreErrors||Q.code==="EACCES")return!1;else throw Q}}});
var My0=E((Zh8,su1)=>{var Ly0=(A={})=>{let B=A.env||process.env;if((A.platform||process.platform)!=="win32")return"PATH";return Object.keys(B).reverse().find((D)=>D.toUpperCase()==="PATH")||"Path"};su1.exports=Ly0;su1.exports.default=Ly0});
var Ny0=E((Dh8,qy0)=>{var oc=process.platform==="win32"||process.env.OSTYPE==="cygwin"||process.env.OSTYPE==="msys",zy0=J1("path"),foB=oc?";":":",Ey0=Hy0(),Uy0=(A)=>Object.assign(new Error(`not found: ${A}`),{code:"ENOENT"}),wy0=(A,B)=>{let Q=B.colon||foB,D=A.match(/\//)||oc&&A.match(/\\/)?[""]:[...oc?[process.cwd()]:[],...(B.path||process.env.PATH||"").split(Q)],Z=oc?B.pathExt||process.env.PATHEXT||".EXE;.CMD;.BAT;.COM":"",G=oc?Z.split(Q):[""];if(oc){if(A.indexOf(".")!==-1&&G[0]!=="")G.unshift("")}return{pathEnv:D,pathExt:G,pathExtExe:Z}},$y0=(A,B,Q)=>{if(typeof B==="function")Q=B,B={};if(!B)B={};let{pathEnv:D,pathExt:Z,pathExtExe:G}=wy0(A,B),F=[],I=(W)=>new Promise((J,X)=>{if(W===D.length)return B.all&&F.length?J(F):X(Uy0(A));let V=D[W],C=/^".*"$/.test(V)?V.slice(1,-1):V,K=zy0.join(C,A),H=!C&&/^\.[\\\/]/.test(A)?A.slice(0,2)+K:K;J(Y(H,W,0))}),Y=(W,J,X)=>new Promise((V,C)=>{if(X===Z.length)return V(I(J+1));let K=Z[X];Ey0(W+K,{pathExt:G},(H,z)=>{if(!H&&z)if(B.all)F.push(W+K);else return V(W+K);return V(Y(W,J,X+1))})});return Q?I(0).then((W)=>Q(null,W),Q):I(0)},hoB=(A,B)=>{B=B||{};let{pathEnv:Q,pathExt:D,pathExtExe:Z}=wy0(A,B),G=[];for(let F=0;F<Q.length;F++){let I=Q[F],Y=/^".*"$/.test(I)?I.slice(1,-1):I,W=zy0.join(Y,A),J=!Y&&/^\.[\\\/]/.test(A)?A.slice(0,2)+W:W;for(let X=0;X<D.length;X++){let V=J+D[X];try{if(Ey0.sync(V,{pathExt:Z}))if(B.all)G.push(V);else return V}catch(C){}}}if(B.all&&G.length)return G;if(B.nothrow)return null;throw Uy0(A)};qy0.exports=$y0;$y0.sync=hoB});
var Py0=E((Gh8,Ty0)=>{var Ry0=J1("path"),goB=Ny0(),uoB=My0();function Oy0(A,B){let Q=A.options.env||process.env,D=process.cwd(),Z=A.options.cwd!=null,G=Z&&process.chdir!==void 0&&!process.chdir.disabled;if(G)try{process.chdir(A.options.cwd)}catch(I){}let F;try{F=goB.sync(A.command,{path:Q[uoB({env:Q})],pathExt:B?Ry0.delimiter:void 0})}catch(I){}finally{if(G)process.chdir(D)}if(F)F=Ry0.resolve(Z?A.options.cwd:"",F);return F}function moB(A){return Oy0(A)||Oy0(A,!0)}Ty0.exports=moB});
var Sy0=E((loB,ou1)=>{var ru1=/([()\][%!^"`<>&|;, *?])/g;function doB(A){return A=A.replace(ru1,"^$1"),A}function coB(A,B){if(A=`${A}`,A=A.replace(/(?=(\\+?)?)\1"/g,"$1$1\\\""),A=A.replace(/(?=(\\+?)?)\1$/,"$1$1"),A=`"${A}"`,A=A.replace(ru1,"^$1"),B)A=A.replace(ru1,"^$1");return A}loB.command=doB;loB.argument=coB});
var Yy0=E((ef8,Iy0)=>{Iy0.exports=Fy0;Fy0.sync=_oB;var Zy0=J1("fs");function koB(A,B){var Q=B.pathExt!==void 0?B.pathExt:process.env.PATHEXT;if(!Q)return!0;if(Q=Q.split(";"),Q.indexOf("")!==-1)return!0;for(var D=0;D<Q.length;D++){var Z=Q[D].toLowerCase();if(Z&&A.substr(-Z.length).toLowerCase()===Z)return!0}return!1}function Gy0(A,B,Q){if(!A.isSymbolicLink()&&!A.isFile())return!1;return koB(B,Q)}function Fy0(A,B,Q){Zy0.stat(A,function(D,Z){Q(D,D?!1:Gy0(Z,A,B))})}function _oB(A,B){return Gy0(Zy0.statSync(A),A,B)}});
var _y0=E((Ih8,ky0)=>{var noB=yy0();ky0.exports=(A="")=>{let B=A.match(noB);if(!B)return null;let[Q,D]=B[0].replace(/#! ?/,"").split(" "),Z=Q.split("/").pop();if(Z==="env")return D;return D?`${Z} ${D}`:Z}});
var dy0=E((Jh8,my0)=>{var eu1=process.platform==="win32";function Am1(A,B){return Object.assign(new Error(`${B} ${A.command} ENOENT`),{code:"ENOENT",errno:"ENOENT",syscall:`${B} ${A.command}`,path:A.command,spawnargs:A.args})}function ZtB(A,B){if(!eu1)return;let Q=A.emit;A.emit=function(D,Z){if(D==="exit"){let G=uy0(Z,B);if(G)return Q.call(A,"error",G)}return Q.apply(A,arguments)}}function uy0(A,B){if(eu1&&A===1&&!B.file)return Am1(B.original,"spawn");return null}function GtB(A,B){if(eu1&&A===1&&!B.file)return Am1(B.original,"spawnSync");return null}my0.exports={hookChildProcess:ZtB,verifyENOENT:uy0,verifyENOENTSync:GtB,notFoundError:Am1}});
var gy0=E((Wh8,hy0)=>{var roB=J1("path"),by0=Py0(),fy0=Sy0(),ooB=vy0(),toB=process.platform==="win32",eoB=/\.(?:com|exe)$/i,AtB=/node_modules[\\/].bin[\\/][^\\/]+\.cmd$/i;function BtB(A){A.file=by0(A);let B=A.file&&ooB(A.file);if(B)return A.args.unshift(A.file),A.command=B,by0(A);return A.file}function QtB(A){if(!toB)return A;let B=BtB(A),Q=!eoB.test(B);if(A.options.forceShell||Q){let D=AtB.test(B);A.command=roB.normalize(A.command),A.command=fy0.command(A.command),A.args=A.args.map((G)=>fy0.argument(G,D));let Z=[A.command].concat(A.args).join(" ");A.args=["/d","/s","/c",`"${Z}"`],A.command=process.env.comspec||"cmd.exe",A.options.windowsVerbatimArguments=!0}return A}function DtB(A,B,Q){if(B&&!Array.isArray(B))Q=B,B=null;B=B?B.slice(0):[],Q=Object.assign({},Q);let D={command:A,args:B,options:Q,file:void 0,original:{command:A,args:B}};return Q.shell?D:QtB(D)}hy0.exports=DtB});
var vy0=E((Yh8,xy0)=>{var tu1=J1("fs"),aoB=_y0();function soB(A){let Q=Buffer.alloc(150),D;try{D=tu1.openSync(A,"r"),tu1.readSync(D,Q,0,150,0),tu1.closeSync(D)}catch(Z){}return aoB(Q.toString())}xy0.exports=soB});
var yy0=E((Fh8,jy0)=>{jy0.exports=/^#!(.*)/});

// Export all variables
module.exports = {
  Cy0,
  Dm1,
  Hy0,
  My0,
  Ny0,
  Py0,
  Sy0,
  Yy0,
  _y0,
  dy0,
  gy0,
  vy0,
  yy0
};
