// Package extracted with entry point: MN
// Contains 3 variables: DIA, MN, rFA

var DIA=E((i45,QIA)=>{var BIA="(?:"+["\\|\\|","\\&\\&",";;","\\|\\&","\\<\\(","\\<\\<\\<",">>",">\\&","<\\&","[&;()|<>]"].join("|")+")",oFA=new RegExp("^"+BIA+"$"),tFA="|&;()<> \\t",g6Q='"((\\\\"|[^"])*?)"',u6Q="'((\\\\'|[^'])*?)'",m6Q=/^#$/,eFA="'",AIA='"',Zr1="$",Ah="",d6Q=4294967296;for(ZK1=0;ZK1<4;ZK1++)Ah+=(d6Q*Math.random()).toString(16);var ZK1,c6Q=new RegExp("^"+Ah);function l6Q(A,B){var Q=B.lastIndex,D=[],Z;while(Z=B.exec(A))if(D.push(Z),B.lastIndex===Z.index)B.lastIndex+=1;return B.lastIndex=Q,D}function p6Q(A,B,Q){var D=typeof A==="function"?A(Q):A[Q];if(typeof D==="undefined"&&Q!="")D="";else if(typeof D==="undefined")D="$";if(typeof D==="object")return B+Ah+JSON.stringify(D)+Ah;return B+D}function i6Q(A,B,Q){if(!Q)Q={};var D=Q.escape||"\\",Z="(\\"+D+`['"`+tFA+`]|[^\\s'"`+tFA+"])+",G=new RegExp(["("+BIA+")","("+Z+"|"+g6Q+"|"+u6Q+")+"].join("|"),"g"),F=l6Q(A,G);if(F.length===0)return[];if(!B)B={};var I=!1;return F.map(function(Y){var W=Y[0];if(!W||I)return;if(oFA.test(W))return{op:W};var J=!1,X=!1,V="",C=!1,K;function H(){K+=1;var L,N,O=W.charAt(K);if(O==="{"){if(K+=1,W.charAt(K)==="}")throw new Error("Bad substitution: "+W.slice(K-2,K+1));if(L=W.indexOf("}",K),L<0)throw new Error("Bad substitution: "+W.slice(K));N=W.slice(K,L),K=L}else if(/[*@#?$!_-]/.test(O))N=O,K+=1;else{var R=W.slice(K);if(L=R.match(/[^\w\d_]/),!L)N=R,K=W.length;else N=R.slice(0,L.index),K+=L.index-1}return p6Q(B,"",N)}for(K=0;K<W.length;K++){var z=W.charAt(K);if(C=C||!J&&(z==="*"||z==="?"),X)V+=z,X=!1;else if(J)if(z===J)J=!1;else if(J==eFA)V+=z;else if(z===D)if(K+=1,z=W.charAt(K),z===AIA||z===D||z===Zr1)V+=z;else V+=D+z;else if(z===Zr1)V+=H();else V+=z;else if(z===AIA||z===eFA)J=z;else if(oFA.test(z))return{op:W};else if(m6Q.test(z)){I=!0;var $={comment:A.slice(Y.index+K+1)};if(V.length)return[V,$];return[$]}else if(z===D)X=!0;else if(z===Zr1)V+=H();else V+=z}if(C)return{op:"glob",pattern:V};return V}).reduce(function(Y,W){return typeof W==="undefined"?Y:Y.concat(W)},[])}QIA.exports=function A(B,Q,D){var Z=i6Q(B,Q,D);if(typeof Q!=="function")return Z;return Z.reduce(function(G,F){if(typeof F==="object")return G.concat(F);var I=F.split(RegExp("("+Ah+".*?"+Ah+")","g"));if(I.length===1)return G.concat(I[0]);return G.concat(I.filter(Boolean).map(function(Y){if(c6Q.test(Y))return JSON.parse(Y.split(Ah)[1]);return Y}))},[])}});
var MN=E((n6Q)=>{n6Q.quote=rFA();n6Q.parse=DIA()});
var rFA=E((p45,sFA)=>{sFA.exports=function A(B){return B.map(function(Q){if(Q==="")return"''";if(Q&&typeof Q==="object")return Q.op.replace(/(.)/g,"\\$1");if(/["\s\\]/.test(Q)&&!/'/.test(Q))return"'"+Q.replace(/(['])/g,"\\$1")+"'";if(/["'\s]/.test(Q))return'"'+Q.replace(/(["\\$`!])/g,"\\$1")+'"';return String(Q).replace(/([A-Za-z]:)?([#!"$&'()*,:;<=>?@[\\\]^`{|}])/g,"$1\\$2")}).join(" ")}});

// Export all variables
module.exports = {
  DIA,
  MN,
  rFA
};
