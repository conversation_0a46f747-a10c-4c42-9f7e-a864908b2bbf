// Package extracted with entry point: bL2
// Contains 2 variables: bL2, xL2

var bL2=E((dR5,FO1)=>{var ua4=xL2(),GO1=new WeakMap,vL2=(A,B={})=>{if(typeof A!=="function")throw new TypeError("Expected a function");let Q,D=0,Z=A.displayName||A.name||"<anonymous>",G=function(...F){if(GO1.set(G,++D),D===1)Q=A.apply(this,F),A=null;else if(B.throw===!0)throw new Error(`Function \`${Z}\` can only be called once`);return Q};return ua4(G,A),GO1.set(G,D),G};FO1.exports=vL2;FO1.exports.default=vL2;FO1.exports.callCount=(A)=>{if(!GO1.has(A))throw new Error(`The given function \`${A.name}\` is not wrapped by the \`onetime\` package`);return GO1.get(A)}});
var xL2=E((mR5,AF0)=>{var _L2=(A,B)=>{for(let Q of Reflect.ownKeys(B))Object.defineProperty(A,Q,Object.getOwnPropertyDescriptor(B,Q));return A};AF0.exports=_L2;AF0.exports.default=_L2});

// Export all variables
module.exports = {
  bL2,
  xL2
};
