// Package extracted with entry point: W91
// Contains 1 variables: W91

var W91=E((JB5,cC1)=>{function v6A(A){return Array.isArray(A)?A:[A]}var g0Q=void 0,Ss1="",_6A=" ",Ps1="\\",u0Q=/^\s+$/,m0Q=/(?:[^\\]|^)\\$/,d0Q=/^\\!/,c0Q=/^\\#/,l0Q=/\r?\n/g,p0Q=/^\.{0,2}\/|^\.{1,2}$/,i0Q=/\/$/,Zi="/",b6A="node-ignore";if(typeof Symbol!=="undefined")b6A=Symbol.for("node-ignore");var f6A=b6A,Gi=(A,B,Q)=>{return Object.defineProperty(A,B,{value:Q}),Q},n0Q=/([0-z])-([0-z])/g,h6A=()=>!1,a0Q=(A)=>A.replace(n0Q,(B,Q,D)=>Q.charCodeAt(0)<=D.charCodeAt(0)?B:Ss1),s0Q=(A)=>{let{length:B}=A;return A.slice(0,B-B%2)},r0Q=[[/^\uFEFF/,()=>Ss1],[/((?:\\\\)*?)(\\?\s+)$/,(A,B,Q)=>B+(Q.indexOf("\\")===0?_6A:Ss1)],[/(\\+?)\s/g,(A,B)=>{let{length:Q}=B;return B.slice(0,Q-Q%2)+_6A}],[/[\\$.|*+(){^]/g,(A)=>`\\${A}`],[/(?!\\)\?/g,()=>"[^/]"],[/^\//,()=>"^"],[/\//g,()=>"\\/"],[/^\^*\\\*\\\*\\\//,()=>"^(?:.*\\/)?"],[/^(?=[^^])/,function A(){return!/\/(?!$)/.test(this)?"(?:^|\\/)":"^"}],[/\\\/\\\*\\\*(?=\\\/|$)/g,(A,B,Q)=>B+6<Q.length?"(?:\\/[^\\/]+)*":"\\/.+"],[/(^|[^\\]+)(\\\*)+(?=.+)/g,(A,B,Q)=>{let D=Q.replace(/\\\*/g,"[^\\/]*");return B+D}],[/\\\\\\(?=[$.|*+(){^])/g,()=>Ps1],[/\\\\/g,()=>Ps1],[/(\\)?\[([^\]/]*?)(\\*)($|\])/g,(A,B,Q,D,Z)=>B===Ps1?`\\[${Q}${s0Q(D)}${Z}`:Z==="]"?D.length%2===0?`[${a0Q(Q)}${D}]`:"[]":"[]"],[/(?:[^*])$/,(A)=>/\/$/.test(A)?`${A}$`:`${A}(?=$|\\/$)`]],o0Q=/(^|\\\/)?\\\*$/,Y91="regex",mC1="checkRegex",x6A="_",t0Q={[Y91](A,B){return`${B?`${B}[^/]+`:"[^/]*"}(?=$|\\/$)`},[mC1](A,B){return`${B?`${B}[^/]*`:"[^/]*"}(?=$|\\/$)`}},e0Q=(A)=>r0Q.reduce((B,[Q,D])=>B.replace(Q,D.bind(A)),A),dC1=(A)=>typeof A==="string",AAQ=(A)=>A&&dC1(A)&&!u0Q.test(A)&&!m0Q.test(A)&&A.indexOf("#")!==0,BAQ=(A)=>A.split(l0Q).filter(Boolean);class g6A{constructor(A,B,Q,D,Z,G){this.pattern=A,this.mark=B,this.negative=Z,Gi(this,"body",Q),Gi(this,"ignoreCase",D),Gi(this,"regexPrefix",G)}get regex(){let A=x6A+Y91;if(this[A])return this[A];return this._make(Y91,A)}get checkRegex(){let A=x6A+mC1;if(this[A])return this[A];return this._make(mC1,A)}_make(A,B){let Q=this.regexPrefix.replace(o0Q,t0Q[A]),D=this.ignoreCase?new RegExp(Q,"i"):new RegExp(Q);return Gi(this,B,D)}}var QAQ=({pattern:A,mark:B},Q)=>{let D=!1,Z=A;if(Z.indexOf("!")===0)D=!0,Z=Z.substr(1);Z=Z.replace(d0Q,"!").replace(c0Q,"#");let G=e0Q(Z);return new g6A(A,B,Z,Q,D,G)};class u6A{constructor(A){this._ignoreCase=A,this._rules=[]}_add(A){if(A&&A[f6A]){this._rules=this._rules.concat(A._rules._rules),this._added=!0;return}if(dC1(A))A={pattern:A};if(AAQ(A.pattern)){let B=QAQ(A,this._ignoreCase);this._added=!0,this._rules.push(B)}}add(A){return this._added=!1,v6A(dC1(A)?BAQ(A):A).forEach(this._add,this),this._added}test(A,B,Q){let D=!1,Z=!1,G;this._rules.forEach((I)=>{let{negative:Y}=I;if(Z===Y&&D!==Z||Y&&!D&&!Z&&!B)return;if(!I[Q].test(A))return;D=!Y,Z=Y,G=Y?g0Q:I});let F={ignored:D,unignored:Z};if(G)F.rule=G;return F}}var DAQ=(A,B)=>{throw new B(A)},SO=(A,B,Q)=>{if(!dC1(A))return Q(`path must be a string, but got \`${B}\``,TypeError);if(!A)return Q("path must not be empty",TypeError);if(SO.isNotRelative(A))return Q(`path should be a \`path.relative()\`d string, but got "${B}"`,RangeError);return!0},m6A=(A)=>p0Q.test(A);SO.isNotRelative=m6A;SO.convert=(A)=>A;class d6A{constructor({ignorecase:A=!0,ignoreCase:B=A,allowRelativePaths:Q=!1}={}){Gi(this,f6A,!0),this._rules=new u6A(B),this._strictPathCheck=!Q,this._initCache()}_initCache(){this._ignoreCache=Object.create(null),this._testCache=Object.create(null)}add(A){if(this._rules.add(A))this._initCache();return this}addPattern(A){return this.add(A)}_test(A,B,Q,D){let Z=A&&SO.convert(A);return SO(Z,A,this._strictPathCheck?DAQ:h6A),this._t(Z,B,Q,D)}checkIgnore(A){if(!i0Q.test(A))return this.test(A);let B=A.split(Zi).filter(Boolean);if(B.pop(),B.length){let Q=this._t(B.join(Zi)+Zi,this._testCache,!0,B);if(Q.ignored)return Q}return this._rules.test(A,!1,mC1)}_t(A,B,Q,D){if(A in B)return B[A];if(!D)D=A.split(Zi).filter(Boolean);if(D.pop(),!D.length)return B[A]=this._rules.test(A,Q,Y91);let Z=this._t(D.join(Zi)+Zi,B,Q,D);return B[A]=Z.ignored?Z:this._rules.test(A,Q,Y91)}ignores(A){return this._test(A,this._ignoreCache,!1).ignored}createFilter(){return(A)=>!this.ignores(A)}filter(A){return v6A(A).filter(this.createFilter())}test(A){return this._test(A,this._testCache,!0)}}var js1=(A)=>new d6A(A),ZAQ=(A)=>SO(A&&SO.convert(A),A,h6A),c6A=()=>{let A=(Q)=>/^\\\\\?\\/.test(Q)||/["<>|\u0000-\u001F]+/u.test(Q)?Q:Q.replace(/\\/g,"/");SO.convert=A;let B=/^[a-z]:\//i;SO.isNotRelative=(Q)=>B.test(Q)||m6A(Q)};if(typeof process!=="undefined"&&process.platform==="win32")c6A();cC1.exports=js1;js1.default=js1;cC1.exports.isPathValid=ZAQ;Gi(cC1.exports,Symbol.for("setupWindows"),c6A)});

// Export all variables
module.exports = {
  W91
};
