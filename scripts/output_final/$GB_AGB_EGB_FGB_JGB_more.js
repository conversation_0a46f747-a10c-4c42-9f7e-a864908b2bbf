// Package extracted with entry point: cH0
// Contains 8 variables: $GB, AGB, EGB, FGB, JGB, KGB, LGB, cH0

var $GB=E((UGB)=>{Object.defineProperty(UGB,"__esModule",{value:!0});UGB.toBase64=void 0;var Ib6=YD(),Yb6=cB(),Wb6=(A)=>{let B;if(typeof A==="string")B=Yb6.fromUtf8(A);else B=A;if(typeof B!=="object"||typeof B.byteOffset!=="number"||typeof B.byteLength!=="number")throw new Error("@smithy/util-base64: toBase64 encoder function only accepts string | Uint8Array.");return Ib6.fromArrayBuffer(B.buffer,B.byteOffset,B.byteLength).toString("base64")};UGB.toBase64=Wb6});
var AGB=E((i13,eZB)=>{var{defineProperty:cy1,getOwnPropertyDescriptor:$v6,getOwnPropertyNames:qv6}=Object,Nv6=Object.prototype.hasOwnProperty,ly1=(A,B)=>cy1(A,"name",{value:B,configurable:!0}),Lv6=(A,B)=>{for(var Q in B)cy1(A,Q,{get:B[Q],enumerable:!0})},Mv6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of qv6(B))if(!Nv6.call(A,Z)&&Z!==Q)cy1(A,Z,{get:()=>B[Z],enumerable:!(D=$v6(B,Z))||D.enumerable})}return A},Rv6=(A)=>Mv6(cy1({},"__esModule",{value:!0}),A),pZB={};Lv6(pZB,{AlgorithmId:()=>sZB,EndpointURLScheme:()=>aZB,FieldPosition:()=>rZB,HttpApiKeyAuthLocation:()=>nZB,HttpAuthLocation:()=>iZB,IniSectionType:()=>oZB,RequestHandlerProtocol:()=>tZB,SMITHY_CONTEXT_KEY:()=>jv6,getDefaultClientConfiguration:()=>Pv6,resolveDefaultRuntimeConfig:()=>Sv6});eZB.exports=Rv6(pZB);var iZB=((A)=>{return A.HEADER="header",A.QUERY="query",A})(iZB||{}),nZB=((A)=>{return A.HEADER="header",A.QUERY="query",A})(nZB||{}),aZB=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(aZB||{}),sZB=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(sZB||{}),Ov6=ly1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),Tv6=ly1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),Pv6=ly1((A)=>{return Ov6(A)},"getDefaultClientConfiguration"),Sv6=ly1((A)=>{return Tv6(A)},"resolveDefaultRuntimeConfig"),rZB=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(rZB||{}),jv6="__smithy_context",oZB=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(oZB||{}),tZB=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(tZB||{})});
var EGB=E((HGB)=>{Object.defineProperty(HGB,"__esModule",{value:!0});HGB.fromBase64=void 0;var Zb6=YD(),Gb6=/^[A-Za-z0-9+/]*={0,2}$/,Fb6=(A)=>{if(A.length*3%4!==0)throw new TypeError("Incorrect padding on base64 string.");if(!Gb6.exec(A))throw new TypeError("Invalid base64 string.");let B=Zb6.fromString(A,"base64");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength)};HGB.fromBase64=Fb6});
var FGB=E((n13,GGB)=>{var{defineProperty:py1,getOwnPropertyDescriptor:yv6,getOwnPropertyNames:kv6}=Object,_v6=Object.prototype.hasOwnProperty,xx=(A,B)=>py1(A,"name",{value:B,configurable:!0}),xv6=(A,B)=>{for(var Q in B)py1(A,Q,{get:B[Q],enumerable:!0})},vv6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of kv6(B))if(!_v6.call(A,Z)&&Z!==Q)py1(A,Z,{get:()=>B[Z],enumerable:!(D=yv6(B,Z))||D.enumerable})}return A},bv6=(A)=>vv6(py1({},"__esModule",{value:!0}),A),BGB={};xv6(BGB,{Field:()=>gv6,Fields:()=>uv6,HttpRequest:()=>mv6,HttpResponse:()=>dv6,IHttpRequest:()=>QGB.HttpRequest,getHttpHandlerExtensionConfiguration:()=>fv6,isValidHostname:()=>ZGB,resolveHttpHandlerRuntimeConfig:()=>hv6});GGB.exports=bv6(BGB);var fv6=xx((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),hv6=xx((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),QGB=AGB(),gv6=class{static{xx(this,"Field")}constructor({name:A,kind:B=QGB.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},uv6=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{xx(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},mv6=class A{static{xx(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=DGB(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function DGB(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}xx(DGB,"cloneQuery");var dv6=class{static{xx(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function ZGB(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}xx(ZGB,"isValidHostname")});
var JGB=E((o13,WGB)=>{var{defineProperty:iy1,getOwnPropertyDescriptor:cv6,getOwnPropertyNames:lv6}=Object,pv6=Object.prototype.hasOwnProperty,hH0=(A,B)=>iy1(A,"name",{value:B,configurable:!0}),iv6=(A,B)=>{for(var Q in B)iy1(A,Q,{get:B[Q],enumerable:!0})},nv6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of lv6(B))if(!pv6.call(A,Z)&&Z!==Q)iy1(A,Z,{get:()=>B[Z],enumerable:!(D=cv6(B,Z))||D.enumerable})}return A},av6=(A)=>nv6(iy1({},"__esModule",{value:!0}),A),IGB={};iv6(IGB,{escapeUri:()=>YGB,escapeUriPath:()=>rv6});WGB.exports=av6(IGB);var YGB=hH0((A)=>encodeURIComponent(A).replace(/[!'()*]/g,sv6),"escapeUri"),sv6=hH0((A)=>`%${A.charCodeAt(0).toString(16).toUpperCase()}`,"hexEncode"),rv6=hH0((A)=>A.split("/").map(YGB).join("/"),"escapeUriPath")});
var KGB=E((t13,CGB)=>{var{defineProperty:ny1,getOwnPropertyDescriptor:ov6,getOwnPropertyNames:tv6}=Object,ev6=Object.prototype.hasOwnProperty,Ab6=(A,B)=>ny1(A,"name",{value:B,configurable:!0}),Bb6=(A,B)=>{for(var Q in B)ny1(A,Q,{get:B[Q],enumerable:!0})},Qb6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of tv6(B))if(!ev6.call(A,Z)&&Z!==Q)ny1(A,Z,{get:()=>B[Z],enumerable:!(D=ov6(B,Z))||D.enumerable})}return A},Db6=(A)=>Qb6(ny1({},"__esModule",{value:!0}),A),XGB={};Bb6(XGB,{buildQueryString:()=>VGB});CGB.exports=Db6(XGB);var gH0=JGB();function VGB(A){let B=[];for(let Q of Object.keys(A).sort()){let D=A[Q];if(Q=gH0.escapeUri(Q),Array.isArray(D))for(let Z=0,G=D.length;Z<G;Z++)B.push(`${Q}=${gH0.escapeUri(D[Z])}`);else{let Z=Q;if(D||typeof D==="string")Z+=`=${gH0.escapeUri(D)}`;B.push(Z)}}return B.join("&")}Ab6(VGB,"buildQueryString")});
var LGB=E((B03,ay1)=>{var{defineProperty:qGB,getOwnPropertyDescriptor:Jb6,getOwnPropertyNames:Xb6}=Object,Vb6=Object.prototype.hasOwnProperty,uH0=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Xb6(B))if(!Vb6.call(A,Z)&&Z!==Q)qGB(A,Z,{get:()=>B[Z],enumerable:!(D=Jb6(B,Z))||D.enumerable})}return A},NGB=(A,B,Q)=>(uH0(A,B,"default"),Q&&uH0(Q,B,"default")),Cb6=(A)=>uH0(qGB({},"__esModule",{value:!0}),A),mH0={};ay1.exports=Cb6(mH0);NGB(mH0,EGB(),ay1.exports);NGB(mH0,$GB(),ay1.exports)});
var cH0=E((Q03,jGB)=>{var{defineProperty:ry1,getOwnPropertyDescriptor:Kb6,getOwnPropertyNames:Hb6}=Object,zb6=Object.prototype.hasOwnProperty,TM=(A,B)=>ry1(A,"name",{value:B,configurable:!0}),Eb6=(A,B)=>{for(var Q in B)ry1(A,Q,{get:B[Q],enumerable:!0})},Ub6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Hb6(B))if(!zb6.call(A,Z)&&Z!==Q)ry1(A,Z,{get:()=>B[Z],enumerable:!(D=Kb6(B,Z))||D.enumerable})}return A},wb6=(A)=>Ub6(ry1({},"__esModule",{value:!0}),A),RGB={};Eb6(RGB,{FetchHttpHandler:()=>qb6,keepAliveSupport:()=>sy1,streamCollector:()=>Lb6});jGB.exports=wb6(RGB);var MGB=FGB(),$b6=KGB();function dH0(A,B){return new Request(A,B)}TM(dH0,"createRequest");function OGB(A=0){return new Promise((B,Q)=>{if(A)setTimeout(()=>{let D=new Error(`Request did not complete within ${A} ms`);D.name="TimeoutError",Q(D)},A)})}TM(OGB,"requestTimeout");var sy1={supported:void 0},qb6=class A{static{TM(this,"FetchHttpHandler")}static create(B){if(typeof B?.handle==="function")return B;return new A(B)}constructor(B){if(typeof B==="function")this.configProvider=B().then((Q)=>Q||{});else this.config=B??{},this.configProvider=Promise.resolve(this.config);if(sy1.supported===void 0)sy1.supported=Boolean(typeof Request!=="undefined"&&"keepalive"in dH0("https://[::1]"))}destroy(){}async handle(B,{abortSignal:Q}={}){if(!this.config)this.config=await this.configProvider;let D=this.config.requestTimeout,Z=this.config.keepAlive===!0,G=this.config.credentials;if(Q?.aborted){let $=new Error("Request aborted");return $.name="AbortError",Promise.reject($)}let F=B.path,I=$b6.buildQueryString(B.query||{});if(I)F+=`?${I}`;if(B.fragment)F+=`#${B.fragment}`;let Y="";if(B.username!=null||B.password!=null){let $=B.username??"",L=B.password??"";Y=`${$}:${L}@`}let{port:W,method:J}=B,X=`${B.protocol}//${Y}${B.hostname}${W?`:${W}`:""}${F}`,V=J==="GET"||J==="HEAD"?void 0:B.body,C={body:V,headers:new Headers(B.headers),method:J,credentials:G};if(this.config?.cache)C.cache=this.config.cache;if(V)C.duplex="half";if(typeof AbortController!=="undefined")C.signal=Q;if(sy1.supported)C.keepalive=Z;if(typeof this.config.requestInit==="function")Object.assign(C,this.config.requestInit(B));let K=TM(()=>{},"removeSignalEventListener"),H=dH0(X,C),z=[fetch(H).then(($)=>{let L=$.headers,N={};for(let R of L.entries())N[R[0]]=R[1];if($.body==null)return $.blob().then((R)=>({response:new MGB.HttpResponse({headers:N,reason:$.statusText,statusCode:$.status,body:R})}));return{response:new MGB.HttpResponse({headers:N,reason:$.statusText,statusCode:$.status,body:$.body})}}),OGB(D)];if(Q)z.push(new Promise(($,L)=>{let N=TM(()=>{let O=new Error("Request aborted");O.name="AbortError",L(O)},"onAbort");if(typeof Q.addEventListener==="function"){let O=Q;O.addEventListener("abort",N,{once:!0}),K=TM(()=>O.removeEventListener("abort",N),"removeSignalEventListener")}else Q.onabort=N}));return Promise.race(z).finally(K)}updateHttpClientConfig(B,Q){this.config=void 0,this.configProvider=this.configProvider.then((D)=>{return D[B]=Q,D})}httpHandlerConfigs(){return this.config??{}}},Nb6=LGB(),Lb6=TM(async(A)=>{if(typeof Blob==="function"&&A instanceof Blob||A.constructor?.name==="Blob"){if(Blob.prototype.arrayBuffer!==void 0)return new Uint8Array(await A.arrayBuffer());return TGB(A)}return PGB(A)},"streamCollector");async function TGB(A){let B=await SGB(A),Q=Nb6.fromBase64(B);return new Uint8Array(Q)}TM(TGB,"collectBlob");async function PGB(A){let B=[],Q=A.getReader(),D=!1,Z=0;while(!D){let{done:I,value:Y}=await Q.read();if(Y)B.push(Y),Z+=Y.length;D=I}let G=new Uint8Array(Z),F=0;for(let I of B)G.set(I,F),F+=I.length;return G}TM(PGB,"collectStream");function SGB(A){return new Promise((B,Q)=>{let D=new FileReader;D.onloadend=()=>{if(D.readyState!==2)return Q(new Error("Reader aborted too early"));let Z=D.result??"",G=Z.indexOf(","),F=G>-1?G+1:Z.length;B(Z.substring(F))},D.onabort=()=>Q(new Error("Read aborted")),D.onerror=()=>Q(D.error),D.readAsDataURL(A)})}TM(SGB,"readToBase64")});

// Export all variables
module.exports = {
  $GB,
  AGB,
  EGB,
  FGB,
  JGB,
  KGB,
  LGB,
  cH0
};
