// Package extracted with entry point: $L2
// Contains 2 variables: $L2, UL2

var $L2=E((cM5,lG0)=>{var wL2=UL2();lG0.exports=wL2;lG0.exports.default=wL2});
var UL2=E((dM5,Ha4)=>{Ha4.exports={single:{topLeft:"┌",top:"─",topRight:"┐",right:"│",bottomRight:"┘",bottom:"─",bottomLeft:"└",left:"│"},double:{topLeft:"╔",top:"═",topRight:"╗",right:"║",bottomRight:"╝",bottom:"═",bottomLeft:"╚",left:"║"},round:{topLeft:"╭",top:"─",topRight:"╮",right:"│",bottomRight:"╯",bottom:"─",bottomLeft:"╰",left:"│"},bold:{topLeft:"┏",top:"━",topRight:"┓",right:"┃",bottomRight:"┛",bottom:"━",bottomLeft:"┗",left:"┃"},singleDouble:{topLeft:"╓",top:"─",topRight:"╖",right:"║",bottomRight:"╜",bottom:"─",bottomLeft:"╙",left:"║"},doubleSingle:{topLeft:"╒",top:"═",topRight:"╕",right:"│",bottomRight:"╛",bottom:"═",bottomLeft:"╘",left:"│"},classic:{topLeft:"+",top:"-",topRight:"+",right:"|",bottomRight:"+",bottom:"-",bottomLeft:"+",left:"|"},arrow:{topLeft:"↘",top:"↓",topRight:"↙",right:"←",bottomRight:"↖",bottom:"↑",bottomLeft:"↗",left:"→"}}});

// Export all variables
module.exports = {
  $L2,
  UL2
};
