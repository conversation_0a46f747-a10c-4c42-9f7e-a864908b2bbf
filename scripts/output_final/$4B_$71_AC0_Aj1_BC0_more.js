// Package extracted with entry point: Dj1
// Contains 45 variables: $4B, $71, AC0, Aj1, BC0, C4B, CC0, DC0, Dj1, Dm... (and 35 more)

var $4B=E((Ji5,w4B)=>{var S71=VC0(),j71=z4B(),E4B=Object.hasOwnProperty,U4B=Object.create(null);for(P71 in S71)if(E4B.call(S71,P71))U4B[S71[P71]]=P71;var P71,vK=w4B.exports={to:{},get:{}};vK.get=function(A){var B=A.substring(0,3).toLowerCase(),Q,D;switch(B){case"hsl":Q=vK.get.hsl(A),D="hsl";break;case"hwb":Q=vK.get.hwb(A),D="hwb";break;default:Q=vK.get.rgb(A),D="rgb";break}if(!Q)return null;return{model:D,value:Q}};vK.get.rgb=function(A){if(!A)return null;var B=/^#([a-f0-9]{3,4})$/i,Q=/^#([a-f0-9]{6})([a-f0-9]{2})?$/i,D=/^rgba?\(\s*([+-]?\d+)(?=[\s,])\s*(?:,\s*)?([+-]?\d+)(?=[\s,])\s*(?:,\s*)?([+-]?\d+)\s*(?:[,|\/]\s*([+-]?[\d\.]+)(%?)\s*)?\)$/,Z=/^rgba?\(\s*([+-]?[\d\.]+)\%\s*,?\s*([+-]?[\d\.]+)\%\s*,?\s*([+-]?[\d\.]+)\%\s*(?:[,|\/]\s*([+-]?[\d\.]+)(%?)\s*)?\)$/,G=/^(\w+)$/,F=[0,0,0,1],I,Y,W;if(I=A.match(Q)){W=I[2],I=I[1];for(Y=0;Y<3;Y++){var J=Y*2;F[Y]=parseInt(I.slice(J,J+2),16)}if(W)F[3]=parseInt(W,16)/255}else if(I=A.match(B)){I=I[1],W=I[3];for(Y=0;Y<3;Y++)F[Y]=parseInt(I[Y]+I[Y],16);if(W)F[3]=parseInt(W+W,16)/255}else if(I=A.match(D)){for(Y=0;Y<3;Y++)F[Y]=parseInt(I[Y+1],0);if(I[4])if(I[5])F[3]=parseFloat(I[4])*0.01;else F[3]=parseFloat(I[4])}else if(I=A.match(Z)){for(Y=0;Y<3;Y++)F[Y]=Math.round(parseFloat(I[Y+1])*2.55);if(I[4])if(I[5])F[3]=parseFloat(I[4])*0.01;else F[3]=parseFloat(I[4])}else if(I=A.match(G)){if(I[1]==="transparent")return[0,0,0,0];if(!E4B.call(S71,I[1]))return null;return F=S71[I[1]],F[3]=1,F}else return null;for(Y=0;Y<3;Y++)F[Y]=Ux(F[Y],0,255);return F[3]=Ux(F[3],0,1),F};vK.get.hsl=function(A){if(!A)return null;var B=/^hsla?\(\s*([+-]?(?:\d{0,3}\.)?\d+)(?:deg)?\s*,?\s*([+-]?[\d\.]+)%\s*,?\s*([+-]?[\d\.]+)%\s*(?:[,|\/]\s*([+-]?(?=\.\d|\d)(?:0|[1-9]\d*)?(?:\.\d*)?(?:[eE][+-]?\d+)?)\s*)?\)$/,Q=A.match(B);if(Q){var D=parseFloat(Q[4]),Z=(parseFloat(Q[1])%360+360)%360,G=Ux(parseFloat(Q[2]),0,100),F=Ux(parseFloat(Q[3]),0,100),I=Ux(isNaN(D)?1:D,0,1);return[Z,G,F,I]}return null};vK.get.hwb=function(A){if(!A)return null;var B=/^hwb\(\s*([+-]?\d{0,3}(?:\.\d+)?)(?:deg)?\s*,\s*([+-]?[\d\.]+)%\s*,\s*([+-]?[\d\.]+)%\s*(?:,\s*([+-]?(?=\.\d|\d)(?:0|[1-9]\d*)?(?:\.\d*)?(?:[eE][+-]?\d+)?)\s*)?\)$/,Q=A.match(B);if(Q){var D=parseFloat(Q[4]),Z=(parseFloat(Q[1])%360+360)%360,G=Ux(parseFloat(Q[2]),0,100),F=Ux(parseFloat(Q[3]),0,100),I=Ux(isNaN(D)?1:D,0,1);return[Z,G,F,I]}return null};vK.to.hex=function(){var A=j71(arguments);return"#"+oS1(A[0])+oS1(A[1])+oS1(A[2])+(A[3]<1?oS1(Math.round(A[3]*255)):"")};vK.to.rgb=function(){var A=j71(arguments);return A.length<4||A[3]===1?"rgb("+Math.round(A[0])+", "+Math.round(A[1])+", "+Math.round(A[2])+")":"rgba("+Math.round(A[0])+", "+Math.round(A[1])+", "+Math.round(A[2])+", "+A[3]+")"};vK.to.rgb.percent=function(){var A=j71(arguments),B=Math.round(A[0]/255*100),Q=Math.round(A[1]/255*100),D=Math.round(A[2]/255*100);return A.length<4||A[3]===1?"rgb("+B+"%, "+Q+"%, "+D+"%)":"rgba("+B+"%, "+Q+"%, "+D+"%, "+A[3]+")"};vK.to.hsl=function(){var A=j71(arguments);return A.length<4||A[3]===1?"hsl("+A[0]+", "+A[1]+"%, "+A[2]+"%)":"hsla("+A[0]+", "+A[1]+"%, "+A[2]+"%, "+A[3]+")"};vK.to.hwb=function(){var A=j71(arguments),B="";if(A.length>=4&&A[3]!==1)B=", "+A[3];return"hwb("+A[0]+", "+A[1]+"%, "+A[2]+"%"+B+")"};vK.to.keyword=function(A){return U4B[A.slice(0,3)]};function Ux(A,B,Q){return Math.min(Math.max(B,A),Q)}function oS1(A){var B=Math.round(A).toString(16).toUpperCase();return B.length<2?"0"+B:B}});
var $71=E((bp5,KQB)=>{var nM6=typeof process==="object"&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...A)=>console.error("SEMVER",...A):()=>{};KQB.exports=nM6});
var AC0=E((dp5,TQB)=>{var XR6=YJ(),VR6=Dm(),{safeRe:cS1,t:lS1}=ft(),CR6=(A,B)=>{if(A instanceof XR6)return A;if(typeof A==="number")A=String(A);if(typeof A!=="string")return null;B=B||{};let Q=null;if(!B.rtl)Q=A.match(B.includePrerelease?cS1[lS1.COERCEFULL]:cS1[lS1.COERCE]);else{let Y=B.includePrerelease?cS1[lS1.COERCERTLFULL]:cS1[lS1.COERCERTL],W;while((W=Y.exec(A))&&(!Q||Q.index+Q[0].length!==A.length)){if(!Q||W.index+W[0].length!==Q.index+Q[0].length)Q=W;Y.lastIndex=W.index+W[1].length+W[2].length}Y.lastIndex=-1}if(Q===null)return null;let D=Q[2],Z=Q[3]||"0",G=Q[4]||"0",F=B.includePrerelease&&Q[5]?`-${Q[5]}`:"",I=B.includePrerelease&&Q[6]?`+${Q[6]}`:"";return VR6(`${D}.${Z}.${G}${F}${I}`,B)};TQB.exports=CR6});
var Aj1=E((Ki5,T4B)=>{var dt=$4B(),bK=HC0(),O4B=["keyword","gray","hex"],zC0={};for(let A of Object.keys(bK))zC0[[...bK[A].labels].sort().join("")]=A;var eS1={};function MI(A,B){if(!(this instanceof MI))return new MI(A,B);if(B&&B in O4B)B=null;if(B&&!(B in bK))throw new Error("Unknown model: "+B);let Q,D;if(A==null)this.model="rgb",this.color=[0,0,0],this.valpha=1;else if(A instanceof MI)this.model=A.model,this.color=[...A.color],this.valpha=A.valpha;else if(typeof A==="string"){let Z=dt.get(A);if(Z===null)throw new Error("Unable to parse color from string: "+A);this.model=Z.model,D=bK[this.model].channels,this.color=Z.value.slice(0,D),this.valpha=typeof Z.value[D]==="number"?Z.value[D]:1}else if(A.length>0){this.model=B||"rgb",D=bK[this.model].channels;let Z=Array.prototype.slice.call(A,0,D);this.color=EC0(Z,D),this.valpha=typeof A[D]==="number"?A[D]:1}else if(typeof A==="number")this.model="rgb",this.color=[A>>16&255,A>>8&255,A&255],this.valpha=1;else{this.valpha=1;let Z=Object.keys(A);if("alpha"in A)Z.splice(Z.indexOf("alpha"),1),this.valpha=typeof A.alpha==="number"?A.alpha:0;let G=Z.sort().join("");if(!(G in zC0))throw new Error("Unable to parse color from object: "+JSON.stringify(A));this.model=zC0[G];let{labels:F}=bK[this.model],I=[];for(Q=0;Q<F.length;Q++)I.push(A[F[Q]]);this.color=EC0(I)}if(eS1[this.model]){D=bK[this.model].channels;for(Q=0;Q<D;Q++){let Z=eS1[this.model][Q];if(Z)this.color[Q]=Z(this.color[Q])}}if(this.valpha=Math.max(0,Math.min(1,this.valpha)),Object.freeze)Object.freeze(this)}MI.prototype={toString(){return this.string()},toJSON(){return this[this.model]()},string(A){let B=this.model in dt.to?this:this.rgb();B=B.round(typeof A==="number"?A:1);let Q=B.valpha===1?B.color:[...B.color,this.valpha];return dt.to[B.model](Q)},percentString(A){let B=this.rgb().round(typeof A==="number"?A:1),Q=B.valpha===1?B.color:[...B.color,this.valpha];return dt.to.rgb.percent(Q)},array(){return this.valpha===1?[...this.color]:[...this.color,this.valpha]},object(){let A={},{channels:B}=bK[this.model],{labels:Q}=bK[this.model];for(let D=0;D<B;D++)A[Q[D]]=this.color[D];if(this.valpha!==1)A.alpha=this.valpha;return A},unitArray(){let A=this.rgb().color;if(A[0]/=255,A[1]/=255,A[2]/=255,this.valpha!==1)A.push(this.valpha);return A},unitObject(){let A=this.rgb().object();if(A.r/=255,A.g/=255,A.b/=255,this.valpha!==1)A.alpha=this.valpha;return A},round(A){return A=Math.max(A||0,0),new MI([...this.color.map(lO6(A)),this.valpha],this.model)},alpha(A){if(A!==void 0)return new MI([...this.color,Math.max(0,Math.min(1,A))],this.model);return this.valpha},red:HZ("rgb",0,kF(255)),green:HZ("rgb",1,kF(255)),blue:HZ("rgb",2,kF(255)),hue:HZ(["hsl","hsv","hsl","hwb","hcg"],0,(A)=>(A%360+360)%360),saturationl:HZ("hsl",1,kF(100)),lightness:HZ("hsl",2,kF(100)),saturationv:HZ("hsv",1,kF(100)),value:HZ("hsv",2,kF(100)),chroma:HZ("hcg",1,kF(100)),gray:HZ("hcg",2,kF(100)),white:HZ("hwb",1,kF(100)),wblack:HZ("hwb",2,kF(100)),cyan:HZ("cmyk",0,kF(100)),magenta:HZ("cmyk",1,kF(100)),yellow:HZ("cmyk",2,kF(100)),black:HZ("cmyk",3,kF(100)),x:HZ("xyz",0,kF(95.047)),y:HZ("xyz",1,kF(100)),z:HZ("xyz",2,kF(108.833)),l:HZ("lab",0,kF(100)),a:HZ("lab",1),b:HZ("lab",2),keyword(A){if(A!==void 0)return new MI(A);return bK[this.model].keyword(this.color)},hex(A){if(A!==void 0)return new MI(A);return dt.to.hex(this.rgb().round().color)},hexa(A){if(A!==void 0)return new MI(A);let B=this.rgb().round().color,Q=Math.round(this.valpha*255).toString(16).toUpperCase();if(Q.length===1)Q="0"+Q;return dt.to.hex(B)+Q},rgbNumber(){let A=this.rgb().color;return(A[0]&255)<<16|(A[1]&255)<<8|A[2]&255},luminosity(){let A=this.rgb().color,B=[];for(let[Q,D]of A.entries()){let Z=D/255;B[Q]=Z<=0.04045?Z/12.92:((Z+0.055)/1.055)**2.4}return 0.2126*B[0]+0.7152*B[1]+0.0722*B[2]},contrast(A){let B=this.luminosity(),Q=A.luminosity();if(B>Q)return(B+0.05)/(Q+0.05);return(Q+0.05)/(B+0.05)},level(A){let B=this.contrast(A);if(B>=7)return"AAA";return B>=4.5?"AA":""},isDark(){let A=this.rgb().color;return(A[0]*2126+A[1]*7152+A[2]*722)/1e4<128},isLight(){return!this.isDark()},negate(){let A=this.rgb();for(let B=0;B<3;B++)A.color[B]=255-A.color[B];return A},lighten(A){let B=this.hsl();return B.color[2]+=B.color[2]*A,B},darken(A){let B=this.hsl();return B.color[2]-=B.color[2]*A,B},saturate(A){let B=this.hsl();return B.color[1]+=B.color[1]*A,B},desaturate(A){let B=this.hsl();return B.color[1]-=B.color[1]*A,B},whiten(A){let B=this.hwb();return B.color[1]+=B.color[1]*A,B},blacken(A){let B=this.hwb();return B.color[2]+=B.color[2]*A,B},grayscale(){let A=this.rgb().color,B=A[0]*0.3+A[1]*0.59+A[2]*0.11;return MI.rgb(B,B,B)},fade(A){return this.alpha(this.valpha-this.valpha*A)},opaquer(A){return this.alpha(this.valpha+this.valpha*A)},rotate(A){let B=this.hsl(),Q=B.color[0];return Q=(Q+A)%360,Q=Q<0?360+Q:Q,B.color[0]=Q,B},mix(A,B){if(!A||!A.rgb)throw new Error('Argument to "mix" was not a Color instance, but rather an instance of '+typeof A);let Q=A.rgb(),D=this.rgb(),Z=B===void 0?0.5:B,G=2*Z-1,F=Q.alpha()-D.alpha(),I=((G*F===-1?G:(G+F)/(1+G*F))+1)/2,Y=1-I;return MI.rgb(I*Q.red()+Y*D.red(),I*Q.green()+Y*D.green(),I*Q.blue()+Y*D.blue(),Q.alpha()*Z+D.alpha()*(1-Z))}};for(let A of Object.keys(bK)){if(O4B.includes(A))continue;let{channels:B}=bK[A];MI.prototype[A]=function(...Q){if(this.model===A)return new MI(this);if(Q.length>0)return new MI(Q,A);return new MI([...pO6(bK[this.model][A].raw(this.color)),this.valpha],A)},MI[A]=function(...Q){let D=Q[0];if(typeof D==="number")D=EC0(Q,B);return new MI(D,A)}}function cO6(A,B){return Number(A.toFixed(B))}function lO6(A){return function(B){return cO6(B,A)}}function HZ(A,B,Q){A=Array.isArray(A)?A:[A];for(let D of A)(eS1[D]||(eS1[D]=[]))[B]=Q;return A=A[0],function(D){let Z;if(D!==void 0){if(Q)D=Q(D);return Z=this[A](),Z.color[B]=D,Z}if(Z=this[A]().color[B],Q)Z=Q(Z);return Z}}function kF(A){return function(B){return Math.max(0,Math.min(A,B))}}function pO6(A){return Array.isArray(A)?A:[A]}function EC0(A,B){for(let Q=0;Q<B;Q++)if(typeof A[Q]!=="number")A[Q]=0;return A}T4B.exports=MI});
var BC0=E((ip5,xQB)=>{var ER6=OE(),UR6=(A,B,Q)=>ER6(A,B,Q)===0;xQB.exports=UR6});
var C4B=E((Yi5,V4B)=>{V4B.exports=function A(B){if(!B||typeof B==="string")return!1;return B instanceof Array||Array.isArray(B)||B.length>=0&&(B.splice instanceof Function||Object.getOwnPropertyDescriptor(B,B.length-1)&&B.constructor.name!=="String")}});
var CC0=E((Xi5,N4B)=>{var y71=VC0(),q4B={};for(let A of Object.keys(y71))q4B[y71[A]]=A;var K9={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};N4B.exports=K9;for(let A of Object.keys(K9)){if(!("channels"in K9[A]))throw new Error("missing channels property: "+A);if(!("labels"in K9[A]))throw new Error("missing channel labels property: "+A);if(K9[A].labels.length!==K9[A].channels)throw new Error("channel and label counts mismatch: "+A);let{channels:B,labels:Q}=K9[A];delete K9[A].channels,delete K9[A].labels,Object.defineProperty(K9[A],"channels",{value:B}),Object.defineProperty(K9[A],"labels",{value:Q})}K9.rgb.hsl=function(A){let B=A[0]/255,Q=A[1]/255,D=A[2]/255,Z=Math.min(B,Q,D),G=Math.max(B,Q,D),F=G-Z,I,Y;if(G===Z)I=0;else if(B===G)I=(Q-D)/F;else if(Q===G)I=2+(D-B)/F;else if(D===G)I=4+(B-Q)/F;if(I=Math.min(I*60,360),I<0)I+=360;let W=(Z+G)/2;if(G===Z)Y=0;else if(W<=0.5)Y=F/(G+Z);else Y=F/(2-G-Z);return[I,Y*100,W*100]};K9.rgb.hsv=function(A){let B,Q,D,Z,G,F=A[0]/255,I=A[1]/255,Y=A[2]/255,W=Math.max(F,I,Y),J=W-Math.min(F,I,Y),X=function(V){return(W-V)/6/J+0.5};if(J===0)Z=0,G=0;else{if(G=J/W,B=X(F),Q=X(I),D=X(Y),F===W)Z=D-Q;else if(I===W)Z=0.3333333333333333+B-D;else if(Y===W)Z=0.6666666666666666+Q-B;if(Z<0)Z+=1;else if(Z>1)Z-=1}return[Z*360,G*100,W*100]};K9.rgb.hwb=function(A){let B=A[0],Q=A[1],D=A[2],Z=K9.rgb.hsl(A)[0],G=0.00392156862745098*Math.min(B,Math.min(Q,D));return D=1-0.00392156862745098*Math.max(B,Math.max(Q,D)),[Z,G*100,D*100]};K9.rgb.cmyk=function(A){let B=A[0]/255,Q=A[1]/255,D=A[2]/255,Z=Math.min(1-B,1-Q,1-D),G=(1-B-Z)/(1-Z)||0,F=(1-Q-Z)/(1-Z)||0,I=(1-D-Z)/(1-Z)||0;return[G*100,F*100,I*100,Z*100]};function xO6(A,B){return(A[0]-B[0])**2+(A[1]-B[1])**2+(A[2]-B[2])**2}K9.rgb.keyword=function(A){let B=q4B[A];if(B)return B;let Q=1/0,D;for(let Z of Object.keys(y71)){let G=y71[Z],F=xO6(A,G);if(F<Q)Q=F,D=Z}return D};K9.keyword.rgb=function(A){return y71[A]};K9.rgb.xyz=function(A){let B=A[0]/255,Q=A[1]/255,D=A[2]/255;B=B>0.04045?((B+0.055)/1.055)**2.4:B/12.92,Q=Q>0.04045?((Q+0.055)/1.055)**2.4:Q/12.92,D=D>0.04045?((D+0.055)/1.055)**2.4:D/12.92;let Z=B*0.4124+Q*0.3576+D*0.1805,G=B*0.2126+Q*0.7152+D*0.0722,F=B*0.0193+Q*0.1192+D*0.9505;return[Z*100,G*100,F*100]};K9.rgb.lab=function(A){let B=K9.rgb.xyz(A),Q=B[0],D=B[1],Z=B[2];Q/=95.047,D/=100,Z/=108.883,Q=Q>0.008856?Q**0.3333333333333333:7.787*Q+0.13793103448275862,D=D>0.008856?D**0.3333333333333333:7.787*D+0.13793103448275862,Z=Z>0.008856?Z**0.3333333333333333:7.787*Z+0.13793103448275862;let G=116*D-16,F=500*(Q-D),I=200*(D-Z);return[G,F,I]};K9.hsl.rgb=function(A){let B=A[0]/360,Q=A[1]/100,D=A[2]/100,Z,G,F;if(Q===0)return F=D*255,[F,F,F];if(D<0.5)Z=D*(1+Q);else Z=D+Q-D*Q;let I=2*D-Z,Y=[0,0,0];for(let W=0;W<3;W++){if(G=B+0.3333333333333333*-(W-1),G<0)G++;if(G>1)G--;if(6*G<1)F=I+(Z-I)*6*G;else if(2*G<1)F=Z;else if(3*G<2)F=I+(Z-I)*(0.6666666666666666-G)*6;else F=I;Y[W]=F*255}return Y};K9.hsl.hsv=function(A){let B=A[0],Q=A[1]/100,D=A[2]/100,Z=Q,G=Math.max(D,0.01);D*=2,Q*=D<=1?D:2-D,Z*=G<=1?G:2-G;let F=(D+Q)/2,I=D===0?2*Z/(G+Z):2*Q/(D+Q);return[B,I*100,F*100]};K9.hsv.rgb=function(A){let B=A[0]/60,Q=A[1]/100,D=A[2]/100,Z=Math.floor(B)%6,G=B-Math.floor(B),F=255*D*(1-Q),I=255*D*(1-Q*G),Y=255*D*(1-Q*(1-G));switch(D*=255,Z){case 0:return[D,Y,F];case 1:return[I,D,F];case 2:return[F,D,Y];case 3:return[F,I,D];case 4:return[Y,F,D];case 5:return[D,F,I]}};K9.hsv.hsl=function(A){let B=A[0],Q=A[1]/100,D=A[2]/100,Z=Math.max(D,0.01),G,F;F=(2-Q)*D;let I=(2-Q)*Z;return G=Q*Z,G/=I<=1?I:2-I,G=G||0,F/=2,[B,G*100,F*100]};K9.hwb.rgb=function(A){let B=A[0]/360,Q=A[1]/100,D=A[2]/100,Z=Q+D,G;if(Z>1)Q/=Z,D/=Z;let F=Math.floor(6*B),I=1-D;if(G=6*B-F,(F&1)!==0)G=1-G;let Y=Q+G*(I-Q),W,J,X;switch(F){default:case 6:case 0:W=I,J=Y,X=Q;break;case 1:W=Y,J=I,X=Q;break;case 2:W=Q,J=I,X=Y;break;case 3:W=Q,J=Y,X=I;break;case 4:W=Y,J=Q,X=I;break;case 5:W=I,J=Q,X=Y;break}return[W*255,J*255,X*255]};K9.cmyk.rgb=function(A){let B=A[0]/100,Q=A[1]/100,D=A[2]/100,Z=A[3]/100,G=1-Math.min(1,B*(1-Z)+Z),F=1-Math.min(1,Q*(1-Z)+Z),I=1-Math.min(1,D*(1-Z)+Z);return[G*255,F*255,I*255]};K9.xyz.rgb=function(A){let B=A[0]/100,Q=A[1]/100,D=A[2]/100,Z,G,F;return Z=B*3.2406+Q*-1.5372+D*-0.4986,G=B*-0.9689+Q*1.8758+D*0.0415,F=B*0.0557+Q*-0.204+D*1.057,Z=Z>0.0031308?1.055*Z**0.4166666666666667-0.055:Z*12.92,G=G>0.0031308?1.055*G**0.4166666666666667-0.055:G*12.92,F=F>0.0031308?1.055*F**0.4166666666666667-0.055:F*12.92,Z=Math.min(Math.max(0,Z),1),G=Math.min(Math.max(0,G),1),F=Math.min(Math.max(0,F),1),[Z*255,G*255,F*255]};K9.xyz.lab=function(A){let B=A[0],Q=A[1],D=A[2];B/=95.047,Q/=100,D/=108.883,B=B>0.008856?B**0.3333333333333333:7.787*B+0.13793103448275862,Q=Q>0.008856?Q**0.3333333333333333:7.787*Q+0.13793103448275862,D=D>0.008856?D**0.3333333333333333:7.787*D+0.13793103448275862;let Z=116*Q-16,G=500*(B-Q),F=200*(Q-D);return[Z,G,F]};K9.lab.xyz=function(A){let B=A[0],Q=A[1],D=A[2],Z,G,F;G=(B+16)/116,Z=Q/500+G,F=G-D/200;let I=G**3,Y=Z**3,W=F**3;return G=I>0.008856?I:(G-0.13793103448275862)/7.787,Z=Y>0.008856?Y:(Z-0.13793103448275862)/7.787,F=W>0.008856?W:(F-0.13793103448275862)/7.787,Z*=95.047,G*=100,F*=108.883,[Z,G,F]};K9.lab.lch=function(A){let B=A[0],Q=A[1],D=A[2],Z;if(Z=Math.atan2(D,Q)*360/2/Math.PI,Z<0)Z+=360;let F=Math.sqrt(Q*Q+D*D);return[B,F,Z]};K9.lch.lab=function(A){let B=A[0],Q=A[1],Z=A[2]/360*2*Math.PI,G=Q*Math.cos(Z),F=Q*Math.sin(Z);return[B,G,F]};K9.rgb.ansi16=function(A,B=null){let[Q,D,Z]=A,G=B===null?K9.rgb.hsv(A)[2]:B;if(G=Math.round(G/50),G===0)return 30;let F=30+(Math.round(Z/255)<<2|Math.round(D/255)<<1|Math.round(Q/255));if(G===2)F+=60;return F};K9.hsv.ansi16=function(A){return K9.rgb.ansi16(K9.hsv.rgb(A),A[2])};K9.rgb.ansi256=function(A){let B=A[0],Q=A[1],D=A[2];if(B===Q&&Q===D){if(B<8)return 16;if(B>248)return 231;return Math.round((B-8)/247*24)+232}return 16+36*Math.round(B/255*5)+6*Math.round(Q/255*5)+Math.round(D/255*5)};K9.ansi16.rgb=function(A){let B=A%10;if(B===0||B===7){if(A>50)B+=3.5;return B=B/10.5*255,[B,B,B]}let Q=(~~(A>50)+1)*0.5,D=(B&1)*Q*255,Z=(B>>1&1)*Q*255,G=(B>>2&1)*Q*255;return[D,Z,G]};K9.ansi256.rgb=function(A){if(A>=232){let G=(A-232)*10+8;return[G,G,G]}A-=16;let B,Q=Math.floor(A/36)/5*255,D=Math.floor((B=A%36)/6)/5*255,Z=B%6/5*255;return[Q,D,Z]};K9.rgb.hex=function(A){let Q=(((Math.round(A[0])&255)<<16)+((Math.round(A[1])&255)<<8)+(Math.round(A[2])&255)).toString(16).toUpperCase();return"000000".substring(Q.length)+Q};K9.hex.rgb=function(A){let B=A.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!B)return[0,0,0];let Q=B[0];if(B[0].length===3)Q=Q.split("").map((I)=>{return I+I}).join("");let D=parseInt(Q,16),Z=D>>16&255,G=D>>8&255,F=D&255;return[Z,G,F]};K9.rgb.hcg=function(A){let B=A[0]/255,Q=A[1]/255,D=A[2]/255,Z=Math.max(Math.max(B,Q),D),G=Math.min(Math.min(B,Q),D),F=Z-G,I,Y;if(F<1)I=G/(1-F);else I=0;if(F<=0)Y=0;else if(Z===B)Y=(Q-D)/F%6;else if(Z===Q)Y=2+(D-B)/F;else Y=4+(B-Q)/F;return Y/=6,Y%=1,[Y*360,F*100,I*100]};K9.hsl.hcg=function(A){let B=A[1]/100,Q=A[2]/100,D=Q<0.5?2*B*Q:2*B*(1-Q),Z=0;if(D<1)Z=(Q-0.5*D)/(1-D);return[A[0],D*100,Z*100]};K9.hsv.hcg=function(A){let B=A[1]/100,Q=A[2]/100,D=B*Q,Z=0;if(D<1)Z=(Q-D)/(1-D);return[A[0],D*100,Z*100]};K9.hcg.rgb=function(A){let B=A[0]/360,Q=A[1]/100,D=A[2]/100;if(Q===0)return[D*255,D*255,D*255];let Z=[0,0,0],G=B%1*6,F=G%1,I=1-F,Y=0;switch(Math.floor(G)){case 0:Z[0]=1,Z[1]=F,Z[2]=0;break;case 1:Z[0]=I,Z[1]=1,Z[2]=0;break;case 2:Z[0]=0,Z[1]=1,Z[2]=F;break;case 3:Z[0]=0,Z[1]=I,Z[2]=1;break;case 4:Z[0]=F,Z[1]=0,Z[2]=1;break;default:Z[0]=1,Z[1]=0,Z[2]=I}return Y=(1-Q)*D,[(Q*Z[0]+Y)*255,(Q*Z[1]+Y)*255,(Q*Z[2]+Y)*255]};K9.hcg.hsv=function(A){let B=A[1]/100,Q=A[2]/100,D=B+Q*(1-B),Z=0;if(D>0)Z=B/D;return[A[0],Z*100,D*100]};K9.hcg.hsl=function(A){let B=A[1]/100,D=A[2]/100*(1-B)+0.5*B,Z=0;if(D>0&&D<0.5)Z=B/(2*D);else if(D>=0.5&&D<1)Z=B/(2*(1-D));return[A[0],Z*100,D*100]};K9.hcg.hwb=function(A){let B=A[1]/100,Q=A[2]/100,D=B+Q*(1-B);return[A[0],(D-B)*100,(1-D)*100]};K9.hwb.hcg=function(A){let B=A[1]/100,D=1-A[2]/100,Z=D-B,G=0;if(Z<1)G=(D-Z)/(1-Z);return[A[0],Z*100,G*100]};K9.apple.rgb=function(A){return[A[0]/65535*255,A[1]/65535*255,A[2]/65535*255]};K9.rgb.apple=function(A){return[A[0]/255*65535,A[1]/255*65535,A[2]/255*65535]};K9.gray.rgb=function(A){return[A[0]/100*255,A[0]/100*255,A[0]/100*255]};K9.gray.hsl=function(A){return[0,0,A[0]]};K9.gray.hsv=K9.gray.hsl;K9.gray.hwb=function(A){return[0,100,A[0]]};K9.gray.cmyk=function(A){return[0,0,0,A[0]]};K9.gray.lab=function(A){return[A[0],0,0]};K9.gray.hex=function(A){let B=Math.round(A[0]/100*255)&255,D=((B<<16)+(B<<8)+B).toString(16).toUpperCase();return"000000".substring(D.length)+D};K9.rgb.gray=function(A){return[(A[0]+A[1]+A[2])/3/255*100]}});
var DC0=E((op5,gQB)=>{var TR6=BC0(),PR6=QC0(),SR6=L71(),jR6=N71(),yR6=pS1(),kR6=iS1(),_R6=(A,B,Q,D)=>{switch(B){case"===":if(typeof A==="object")A=A.version;if(typeof Q==="object")Q=Q.version;return A===Q;case"!==":if(typeof A==="object")A=A.version;if(typeof Q==="object")Q=Q.version;return A!==Q;case"":case"=":case"==":return TR6(A,Q,D);case"!=":return PR6(A,Q,D);case">":return SR6(A,Q,D);case">=":return jR6(A,Q,D);case"<":return yR6(A,Q,D);case"<=":return kR6(A,Q,D);default:throw new TypeError(`Invalid operator: ${B}`)}};gQB.exports=_R6});
var Dj1=E((Mi5,D6B)=>{var yP=J4B();j4B()(yP);b4B()(yP);h4B()(yP);m4B()(yP);l4B()(yP);i4B()(yP);t4B()(yP);Q6B()(yP);D6B.exports=yP});
var Dm=E((mp5,OQB)=>{var RQB=YJ(),JR6=(A,B,Q=!1)=>{if(A instanceof RQB)return A;try{return new RQB(A,B)}catch(D){if(!Q)return null;throw D}};OQB.exports=JR6});
var HC0=E((Ci5,R4B)=>{var KC0=CC0(),gO6=M4B(),mt={},uO6=Object.keys(KC0);function mO6(A){let B=function(...Q){let D=Q[0];if(D===void 0||D===null)return D;if(D.length>1)Q=D;return A(Q)};if("conversion"in A)B.conversion=A.conversion;return B}function dO6(A){let B=function(...Q){let D=Q[0];if(D===void 0||D===null)return D;if(D.length>1)Q=D;let Z=A(Q);if(typeof Z==="object")for(let G=Z.length,F=0;F<G;F++)Z[F]=Math.round(Z[F]);return Z};if("conversion"in A)B.conversion=A.conversion;return B}uO6.forEach((A)=>{mt[A]={},Object.defineProperty(mt[A],"channels",{value:KC0[A].channels}),Object.defineProperty(mt[A],"labels",{value:KC0[A].labels});let B=gO6(A);Object.keys(B).forEach((D)=>{let Z=B[D];mt[A][D]=dO6(Z),mt[A][D].raw=mO6(Z)})});R4B.exports=mt});
var IC0=E((Bi5,DO6)=>{DO6.exports={name:"sharp",description:"High performance Node.js image processing, the fastest module to resize JPEG, PNG, WebP, GIF, AVIF and TIFF images",version:"0.33.5",author:"Lovell Fuller <<EMAIL>>",homepage:"https://sharp.pixelplumbing.com",contributors:["Pierre Inglebert <<EMAIL>>","Jonathan Ong <<EMAIL>>","Chanon Sajjamanochai <<EMAIL>>","Juliano Julio <<EMAIL>>","Daniel Gasienica <<EMAIL>>","Julian Walker <<EMAIL>>","Amit Pitaru <<EMAIL>>","Brandon Aaron <<EMAIL>>","Andreas Lind <<EMAIL>>","Maurus Cuelenaere <<EMAIL>>","Linus Unnebäck <<EMAIL>>","Victor Mateevitsi <<EMAIL>>","Alaric Holloway <<EMAIL>>","Bernhard K. Weisshuhn <<EMAIL>>","Chris Riley <<EMAIL>>","David Carley <<EMAIL>>","John Tobin <<EMAIL>>","Kenton Gray <<EMAIL>>","Felix Bünemann <<EMAIL>>","Samy Al Zahrani <<EMAIL>>","Chintan Thakkar <<EMAIL>>","F. Orlando Galashan <<EMAIL>>","Kleis Auke Wolthuizen <<EMAIL>>","Matt Hirsch <<EMAIL>>","Matthias Thoemmes <<EMAIL>>","Patrick Paskaris <<EMAIL>>","Jérémy Lal <<EMAIL>>","Rahul Nanwani <<EMAIL>>","Alice Monday <<EMAIL>>","Kristo Jorgenson <<EMAIL>>","YvesBos <<EMAIL>>","Guy Maliar <<EMAIL>>","Nicolas Coden <<EMAIL>>","Matt Parrish <<EMAIL>>","Marcel Bretschneider <<EMAIL>>","Matthew McEachen <<EMAIL>>","Jarda Kotěšovec <<EMAIL>>","Kenric D'Souza <<EMAIL>>","Oleh Aleinyk <<EMAIL>>","Marcel Bretschneider <<EMAIL>>","Andrea Bianco <<EMAIL>>","Rik Heywood <<EMAIL>>","Thomas Parisot <<EMAIL>>","Nathan Graves <<EMAIL>>","Tom Lokhorst <<EMAIL>>","Espen Hovlandsdal <<EMAIL>>","Sylvain Dumont <<EMAIL>>","Alun Davies <<EMAIL>>","Aidan Hoolachan <<EMAIL>>","Axel Eirola <<EMAIL>>","Freezy <<EMAIL>>","Daiz <<EMAIL>>","Julian Aubourg <<EMAIL>>","Keith Belovay <<EMAIL>>","Michael B. Klein <<EMAIL>>","Jordan Prudhomme <<EMAIL>>","Ilya Ovdin <<EMAIL>>","Andargor <<EMAIL>>","Paul Neave <<EMAIL>>","Brendan Kennedy <<EMAIL>>","Brychan Bennett-Odlum <**************>","Edward Silverton <<EMAIL>>","Roman Malieiev <<EMAIL>>","Tomas Szabo <<EMAIL>>","Robert O'Rourke <<EMAIL>>","Guillermo Alfonso Varela Chouciño <<EMAIL>>","Christian Flintrup <<EMAIL>>","Manan Jadhav <<EMAIL>>","Leon Radley <<EMAIL>>","alza54 <<EMAIL>>","Jacob Smith <<EMAIL>>","Michael Nutt <<EMAIL>>","Brad Parham <<EMAIL>>","Taneli Vatanen <<EMAIL>>","Joris Dugué <<EMAIL>>","Chris Banks <<EMAIL>>","Ompal Singh <<EMAIL>>","Brodan <<EMAIL>>","Ankur Parihar <<EMAIL>>","Brahim Ait elhaj <<EMAIL>>","Mart Jansink <<EMAIL>>","Lachlan Newman <<EMAIL>>","Dennis Beatty <<EMAIL>>","Ingvar Stepanyan <<EMAIL>>","Don Denton <<EMAIL>>"],scripts:{install:"node install/check",clean:"rm -rf src/build/ .nyc_output/ coverage/ test/fixtures/output.*",test:"npm run test-lint && npm run test-unit && npm run test-licensing && npm run test-types","test-lint":"semistandard && cpplint","test-unit":"nyc --reporter=lcov --reporter=text --check-coverage --branches=100 mocha","test-licensing":'license-checker --production --summary --onlyAllow="Apache-2.0;BSD;ISC;LGPL-3.0-or-later;MIT"',"test-leak":"./test/leak/leak.sh","test-types":"tsd","package-from-local-build":"node npm/from-local-build","package-from-github-release":"node npm/from-github-release","docs-build":"node docs/build && node docs/search-index/build","docs-serve":"cd docs && npx serve","docs-publish":"cd docs && npx firebase-tools deploy --project pixelplumbing --only hosting:pixelplumbing-sharp"},type:"commonjs",main:"lib/index.js",types:"lib/index.d.ts",files:["install","lib","src/*.{cc,h,gyp}"],repository:{type:"git",url:"git://github.com/lovell/sharp.git"},keywords:["jpeg","png","webp","avif","tiff","gif","svg","jp2","dzi","image","resize","thumbnail","crop","embed","libvips","vips"],dependencies:{color:"^4.2.3","detect-libc":"^2.0.3",semver:"^7.6.3"},optionalDependencies:{"@img/sharp-darwin-arm64":"0.33.5","@img/sharp-darwin-x64":"0.33.5","@img/sharp-libvips-darwin-arm64":"1.0.4","@img/sharp-libvips-darwin-x64":"1.0.4","@img/sharp-libvips-linux-arm":"1.0.5","@img/sharp-libvips-linux-arm64":"1.0.4","@img/sharp-libvips-linux-s390x":"1.0.4","@img/sharp-libvips-linux-x64":"1.0.4","@img/sharp-libvips-linuxmusl-arm64":"1.0.4","@img/sharp-libvips-linuxmusl-x64":"1.0.4","@img/sharp-linux-arm":"0.33.5","@img/sharp-linux-arm64":"0.33.5","@img/sharp-linux-s390x":"0.33.5","@img/sharp-linux-x64":"0.33.5","@img/sharp-linuxmusl-arm64":"0.33.5","@img/sharp-linuxmusl-x64":"0.33.5","@img/sharp-wasm32":"0.33.5","@img/sharp-win32-ia32":"0.33.5","@img/sharp-win32-x64":"0.33.5"},devDependencies:{"@emnapi/runtime":"^1.2.0","@img/sharp-libvips-dev":"1.0.4","@img/sharp-libvips-dev-wasm32":"1.0.5","@img/sharp-libvips-win32-ia32":"1.0.4","@img/sharp-libvips-win32-x64":"1.0.4","@types/node":"*",async:"^3.2.5",cc:"^3.0.1",emnapi:"^1.2.0","exif-reader":"^2.0.1","extract-zip":"^2.0.1",icc:"^3.0.0","jsdoc-to-markdown":"^8.0.3","license-checker":"^25.0.1",mocha:"^10.7.3","node-addon-api":"^8.1.0",nyc:"^17.0.0",prebuild:"^13.0.1",semistandard:"^17.0.0","tar-fs":"^3.0.6",tsd:"^0.31.1"},license:"Apache-2.0",engines:{node:"^18.17.0 || ^20.3.0 || >=21.0.0"},config:{libvips:">=8.15.3"},funding:{url:"https://opencollective.com/libvips"},binary:{napi_versions:[9]},semistandard:{env:["mocha"]},cc:{linelength:"120",filter:["build/include"]},nyc:{include:["lib"]},tsd:{directory:"test/types/"}}});
var J4B=E((Fi5,W4B)=>{var TO6=J1("node:util"),XC0=J1("node:stream"),PO6=wM();T71();var SO6=TO6.debuglog("sharp"),Gm=function(A,B){if(arguments.length===1&&!PO6.defined(A))throw new Error("Invalid input");if(!(this instanceof Gm))return new Gm(A,B);return XC0.Duplex.call(this),this.options={topOffsetPre:-1,leftOffsetPre:-1,widthPre:-1,heightPre:-1,topOffsetPost:-1,leftOffsetPost:-1,widthPost:-1,heightPost:-1,width:-1,height:-1,canvas:"crop",position:0,resizeBackground:[0,0,0,255],useExifOrientation:!1,angle:0,rotationAngle:0,rotationBackground:[0,0,0,255],rotateBeforePreExtract:!1,flip:!1,flop:!1,extendTop:0,extendBottom:0,extendLeft:0,extendRight:0,extendBackground:[0,0,0,255],extendWith:"background",withoutEnlargement:!1,withoutReduction:!1,affineMatrix:[],affineBackground:[0,0,0,255],affineIdx:0,affineIdy:0,affineOdx:0,affineOdy:0,affineInterpolator:this.constructor.interpolators.bilinear,kernel:"lanczos3",fastShrinkOnLoad:!0,tint:[-1,0,0,0],flatten:!1,flattenBackground:[0,0,0],unflatten:!1,negate:!1,negateAlpha:!0,medianSize:0,blurSigma:0,precision:"integer",minAmpl:0.2,sharpenSigma:0,sharpenM1:1,sharpenM2:2,sharpenX1:2,sharpenY2:10,sharpenY3:20,threshold:0,thresholdGrayscale:!0,trimBackground:[],trimThreshold:-1,trimLineArt:!1,gamma:0,gammaOut:0,greyscale:!1,normalise:!1,normaliseLower:1,normaliseUpper:99,claheWidth:0,claheHeight:0,claheMaxSlope:3,brightness:1,saturation:1,hue:0,lightness:0,booleanBufferIn:null,booleanFileIn:"",joinChannelIn:[],extractChannel:-1,removeAlpha:!1,ensureAlpha:-1,colourspace:"srgb",colourspacePipeline:"last",composite:[],fileOut:"",formatOut:"input",streamOut:!1,keepMetadata:0,withMetadataOrientation:-1,withMetadataDensity:0,withIccProfile:"",withExif:{},withExifMerge:!0,resolveWithObject:!1,jpegQuality:80,jpegProgressive:!1,jpegChromaSubsampling:"4:2:0",jpegTrellisQuantisation:!1,jpegOvershootDeringing:!1,jpegOptimiseScans:!1,jpegOptimiseCoding:!0,jpegQuantisationTable:0,pngProgressive:!1,pngCompressionLevel:6,pngAdaptiveFiltering:!1,pngPalette:!1,pngQuality:100,pngEffort:7,pngBitdepth:8,pngDither:1,jp2Quality:80,jp2TileHeight:512,jp2TileWidth:512,jp2Lossless:!1,jp2ChromaSubsampling:"4:4:4",webpQuality:80,webpAlphaQuality:100,webpLossless:!1,webpNearLossless:!1,webpSmartSubsample:!1,webpPreset:"default",webpEffort:4,webpMinSize:!1,webpMixed:!1,gifBitdepth:8,gifEffort:7,gifDither:1,gifInterFrameMaxError:0,gifInterPaletteMaxError:3,gifReuse:!0,gifProgressive:!1,tiffQuality:80,tiffCompression:"jpeg",tiffPredictor:"horizontal",tiffPyramid:!1,tiffMiniswhite:!1,tiffBitdepth:8,tiffTile:!1,tiffTileHeight:256,tiffTileWidth:256,tiffXres:1,tiffYres:1,tiffResolutionUnit:"inch",heifQuality:50,heifLossless:!1,heifCompression:"av1",heifEffort:4,heifChromaSubsampling:"4:4:4",heifBitdepth:8,jxlDistance:1,jxlDecodingTier:0,jxlEffort:7,jxlLossless:!1,rawDepth:"uchar",tileSize:256,tileOverlap:0,tileContainer:"fs",tileLayout:"dz",tileFormat:"last",tileDepth:"last",tileAngle:0,tileSkipBlanks:-1,tileBackground:[255,255,255,255],tileCentre:!1,tileId:"https://example.com/iiif",tileBasename:"",timeoutSeconds:0,linearA:[],linearB:[],debuglog:(Q)=>{this.emit("warning",Q),SO6(Q)},queueListener:function(Q){Gm.queue.emit("change",Q)}},this.options.input=this._createInputDescriptor(A,B,{allowStream:!0}),this};Object.setPrototypeOf(Gm.prototype,XC0.Duplex.prototype);Object.setPrototypeOf(Gm,XC0.Duplex);function jO6(){let A=this.constructor.call(),{debuglog:B,queueListener:Q,...D}=this.options;if(A.options=structuredClone(D),A.options.debuglog=B,A.options.queueListener=Q,this._isStreamInput())this.on("finish",()=>{this._flattenBufferIn(),A.options.input.buffer=this.options.input.buffer,A.emit("finish")});return A}Object.assign(Gm.prototype,{clone:jO6});W4B.exports=Gm});
var L71=E((ap5,bQB)=>{var qR6=OE(),NR6=(A,B,Q)=>qR6(A,B,Q)>0;bQB.exports=NR6});
var M4B=E((Vi5,L4B)=>{var tS1=CC0();function vO6(){let A={},B=Object.keys(tS1);for(let Q=B.length,D=0;D<Q;D++)A[B[D]]={distance:-1,parent:null};return A}function bO6(A){let B=vO6(),Q=[A];B[A].distance=0;while(Q.length){let D=Q.pop(),Z=Object.keys(tS1[D]);for(let G=Z.length,F=0;F<G;F++){let I=Z[F],Y=B[I];if(Y.distance===-1)Y.distance=B[D].distance+1,Y.parent=D,Q.unshift(I)}}return B}function fO6(A,B){return function(Q){return B(A(Q))}}function hO6(A,B){let Q=[B[A].parent,A],D=tS1[B[A].parent][A],Z=B[A].parent;while(B[Z].parent)Q.unshift(B[Z].parent),D=fO6(tS1[B[Z].parent][Z],D),Z=B[Z].parent;return D.conversion=Q,D}L4B.exports=function(A){let B=bO6(A),Q={},D=Object.keys(B);for(let Z=D.length,G=0;G<Z;G++){let F=D[G];if(B[F].parent===null)continue;Q[F]=hO6(F,B)}return Q}});
var N71=E((lp5,jQB)=>{var HR6=OE(),zR6=(A,B,Q)=>HR6(A,B,Q)>=0;jQB.exports=zR6});
var OE=E((cp5,SQB)=>{var PQB=YJ(),KR6=(A,B,Q)=>new PQB(A,Q).compare(new PQB(B,Q));SQB.exports=KR6});
var Q6B=E((Ni5,B6B)=>{var zP6=J1("node:events"),Qj1=hS1(),PE=wM(),{runtimePlatformArch:EP6}=WC0(),FX=T71(),e4B=EP6(),NC0=FX.libvipsVersion(),$x=FX.format();$x.heif.output.alias=["avif","heic"];$x.jpeg.output.alias=["jpe","jpg"];$x.tiff.output.alias=["tif"];$x.jp2k.output.alias=["j2c","j2k","jp2","jpx"];var UP6={nearest:"nearest",bilinear:"bilinear",bicubic:"bicubic",locallyBoundedBicubic:"lbb",nohalo:"nohalo",vertexSplitQuadraticBasisSpline:"vsqbs"},lt={vips:NC0.semver};if(!NC0.isGlobal)if(!NC0.isWasm)try{lt=J1(`@img/sharp-${e4B}/versions`)}catch(A){try{lt=J1(`@img/sharp-libvips-${e4B}/versions`)}catch(B){}}else try{lt=(()=>{throw new Error("Cannot require module "+"@img/sharp-wasm32/versions");})()}catch(A){}lt.sharp=IC0().version;if(lt.heif&&$x.heif)$x.heif.input.fileSuffix=[".avif"],$x.heif.output.alias=["avif"];function A6B(A){if(PE.bool(A))if(A)return FX.cache(50,20,100);else return FX.cache(0,0,0);else if(PE.object(A))return FX.cache(A.memory,A.files,A.items);else return FX.cache()}A6B(!0);function wP6(A){return FX.concurrency(PE.integer(A)?A:null)}if(Qj1.familySync()===Qj1.GLIBC&&!FX._isUsingJemalloc())FX.concurrency(1);else if(Qj1.familySync()===Qj1.MUSL&&FX.concurrency()===1024)FX.concurrency(J1("node:os").availableParallelism());var $P6=new zP6.EventEmitter;function qP6(){return FX.counters()}function NP6(A){return FX.simd(PE.bool(A)?A:null)}function LP6(A){if(PE.object(A))if(Array.isArray(A.operation)&&A.operation.every(PE.string))FX.block(A.operation,!0);else throw PE.invalidParameterError("operation","Array<string>",A.operation);else throw PE.invalidParameterError("options","object",A)}function MP6(A){if(PE.object(A))if(Array.isArray(A.operation)&&A.operation.every(PE.string))FX.block(A.operation,!1);else throw PE.invalidParameterError("operation","Array<string>",A.operation);else throw PE.invalidParameterError("options","object",A)}B6B.exports=function(A){A.cache=A6B,A.concurrency=wP6,A.counters=qP6,A.simd=NP6,A.format=$x,A.interpolators=UP6,A.versions=lt,A.queue=$P6,A.block=LP6,A.unblock=MP6}});
var QC0=E((np5,vQB)=>{var wR6=OE(),$R6=(A,B,Q)=>wR6(A,B,Q)!==0;vQB.exports=$R6});
var R71=E((tp5,pQB)=>{var M71=Symbol("SemVer ANY");class nS1{static get ANY(){return M71}constructor(A,B){if(B=uQB(B),A instanceof nS1)if(A.loose===!!B.loose)return A;else A=A.value;if(A=A.trim().split(/\s+/).join(" "),GC0("comparator",A,B),this.options=B,this.loose=!!B.loose,this.parse(A),this.semver===M71)this.value="";else this.value=this.operator+this.semver.version;GC0("comp",this)}parse(A){let B=this.options.loose?mQB[dQB.COMPARATORLOOSE]:mQB[dQB.COMPARATOR],Q=A.match(B);if(!Q)throw new TypeError(`Invalid comparator: ${A}`);if(this.operator=Q[1]!==void 0?Q[1]:"",this.operator==="=")this.operator="";if(!Q[2])this.semver=M71;else this.semver=new cQB(Q[2],this.options.loose)}toString(){return this.value}test(A){if(GC0("Comparator.test",A,this.options.loose),this.semver===M71||A===M71)return!0;if(typeof A==="string")try{A=new cQB(A,this.options)}catch(B){return!1}return ZC0(A,this.operator,this.semver,this.options)}intersects(A,B){if(!(A instanceof nS1))throw new TypeError("a Comparator is required");if(this.operator===""){if(this.value==="")return!0;return new lQB(A.value,B).test(this.value)}else if(A.operator===""){if(A.value==="")return!0;return new lQB(this.value,B).test(A.semver)}if(B=uQB(B),B.includePrerelease&&(this.value==="<0.0.0-0"||A.value==="<0.0.0-0"))return!1;if(!B.includePrerelease&&(this.value.startsWith("<0.0.0")||A.value.startsWith("<0.0.0")))return!1;if(this.operator.startsWith(">")&&A.operator.startsWith(">"))return!0;if(this.operator.startsWith("<")&&A.operator.startsWith("<"))return!0;if(this.semver.version===A.semver.version&&this.operator.includes("=")&&A.operator.includes("="))return!0;if(ZC0(this.semver,"<",A.semver,B)&&this.operator.startsWith(">")&&A.operator.startsWith("<"))return!0;if(ZC0(this.semver,">",A.semver,B)&&this.operator.startsWith("<")&&A.operator.startsWith(">"))return!0;return!1}}pQB.exports=nS1;var uQB=gS1(),{safeRe:mQB,t:dQB}=ft(),ZC0=DC0(),GC0=$71(),cQB=YJ(),lQB=TE()});
var T71=E((Zi5,Y4B)=>{var{familySync:qO6,versionSync:NO6}=hS1(),{runtimePlatformArch:LO6,isUnsupportedNodeRuntime:I4B,prebuiltPlatforms:MO6,minimumLibvipsVersion:RO6}=WC0(),Zm=LO6(),OO6=[`../src/build/Release/sharp-${Zm}.node`,"../src/build/Release/sharp-wasm32.node",`@img/sharp-${Zm}/sharp.node`,"@img/sharp-wasm32/sharp.node"],JC0,rS1=[];for(let A of OO6)try{JC0=J1(A);break}catch(B){rS1.push(B)}if(JC0)Y4B.exports=JC0;else{let[A,B,Q]=["linux","darwin","win32"].map((G)=>Zm.startsWith(G)),D=[`Could not load the "sharp" module using the ${Zm} runtime`];rS1.forEach((G)=>{if(G.code!=="MODULE_NOT_FOUND")D.push(`${G.code}: ${G.message}`)});let Z=rS1.map((G)=>G.message).join(" ");if(D.push("Possible solutions:"),I4B()){let{found:G,expected:F}=I4B();D.push("- Please upgrade Node.js:",`    Found ${G}`,`    Requires ${F}`)}else if(MO6.includes(Zm)){let[G,F]=Zm.split("-"),I=G.endsWith("musl")?" --libc=musl":"";D.push("- Ensure optional dependencies can be installed:","    npm install --include=optional sharp","- Ensure your package manager supports multi-platform installation:","    See https://sharp.pixelplumbing.com/install#cross-platform","- Add platform-specific dependencies:",`    npm install --os=${G.replace("musl","")}${I} --cpu=${F} sharp`)}else D.push(`- Manually install libvips >= ${RO6}`,"- Add experimental WebAssembly-based dependencies:","    npm install --cpu=wasm32 sharp","    npm install @img/sharp-wasm32");if(A&&/(symbol not found|CXXABI_)/i.test(Z))try{let{config:G}=J1(`@img/sharp-libvips-${Zm}/package`),F=`${qO6()} ${NO6()}`,I=`${G.musl?"musl":"glibc"} ${G.musl||G.glibc}`;D.push("- Update your OS:",`    Found ${F}`,`    Requires ${I}`)}catch(G){}if(A&&/\/snap\/core[0-9]{2}/.test(Z))D.push("- Remove the Node.js Snap, which does not support native modules","    snap remove node");if(B&&/Incompatible library version/.test(Z))D.push("- Update Homebrew:","    brew update && brew upgrade vips");if(rS1.some((G)=>G.code==="ERR_DLOPEN_DISABLED"))D.push("- Run Node.js without using the --no-addons flag");if(Q&&/The specified procedure could not be found/.test(Z))D.push("- Using the canvas package on Windows?","    See https://sharp.pixelplumbing.com/install#canvas-and-windows","- Check for outdated versions of sharp in the dependency tree:","    npm ls sharp");throw D.push("- Consult the installation documentation:","    See https://sharp.pixelplumbing.com/install"),new Error(D.join(`
`))}});
var TE=E((ep5,sQB)=>{var xR6=/\s+/g;class O71{constructor(A,B){if(B=bR6(B),A instanceof O71)if(A.loose===!!B.loose&&A.includePrerelease===!!B.includePrerelease)return A;else return new O71(A.raw,B);if(A instanceof FC0)return this.raw=A.value,this.set=[[A]],this.formatted=void 0,this;if(this.options=B,this.loose=!!B.loose,this.includePrerelease=!!B.includePrerelease,this.raw=A.trim().replace(xR6," "),this.set=this.raw.split("||").map((Q)=>this.parseRange(Q.trim())).filter((Q)=>Q.length),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){let Q=this.set[0];if(this.set=this.set.filter((D)=>!nQB(D[0])),this.set.length===0)this.set=[Q];else if(this.set.length>1){for(let D of this.set)if(D.length===1&&cR6(D[0])){this.set=[D];break}}}this.formatted=void 0}get range(){if(this.formatted===void 0){this.formatted="";for(let A=0;A<this.set.length;A++){if(A>0)this.formatted+="||";let B=this.set[A];for(let Q=0;Q<B.length;Q++){if(Q>0)this.formatted+=" ";this.formatted+=B[Q].toString().trim()}}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(A){let Q=((this.options.includePrerelease&&mR6)|(this.options.loose&&dR6))+":"+A,D=iQB.get(Q);if(D)return D;let Z=this.options.loose,G=Z?pV[ZX.HYPHENRANGELOOSE]:pV[ZX.HYPHENRANGE];A=A.replace(G,eR6(this.options.includePrerelease)),k7("hyphen replace",A),A=A.replace(pV[ZX.COMPARATORTRIM],hR6),k7("comparator trim",A),A=A.replace(pV[ZX.TILDETRIM],gR6),k7("tilde trim",A),A=A.replace(pV[ZX.CARETTRIM],uR6),k7("caret trim",A);let F=A.split(" ").map((J)=>lR6(J,this.options)).join(" ").split(/\s+/).map((J)=>tR6(J,this.options));if(Z)F=F.filter((J)=>{return k7("loose invalid filter",J,this.options),!!J.match(pV[ZX.COMPARATORLOOSE])});k7("range list",F);let I=new Map,Y=F.map((J)=>new FC0(J,this.options));for(let J of Y){if(nQB(J))return[J];I.set(J.value,J)}if(I.size>1&&I.has(""))I.delete("");let W=[...I.values()];return iQB.set(Q,W),W}intersects(A,B){if(!(A instanceof O71))throw new TypeError("a Range is required");return this.set.some((Q)=>{return aQB(Q,B)&&A.set.some((D)=>{return aQB(D,B)&&Q.every((Z)=>{return D.every((G)=>{return Z.intersects(G,B)})})})})}test(A){if(!A)return!1;if(typeof A==="string")try{A=new fR6(A,this.options)}catch(B){return!1}for(let B=0;B<this.set.length;B++)if(AO6(this.set[B],A,this.options))return!0;return!1}}sQB.exports=O71;var vR6=_QB(),iQB=new vR6,bR6=gS1(),FC0=R71(),k7=$71(),fR6=YJ(),{safeRe:pV,t:ZX,comparatorTrimReplace:hR6,tildeTrimReplace:gR6,caretTrimReplace:uR6}=ft(),{FLAG_INCLUDE_PRERELEASE:mR6,FLAG_LOOSE:dR6}=q71(),nQB=(A)=>A.value==="<0.0.0-0",cR6=(A)=>A.value==="",aQB=(A,B)=>{let Q=!0,D=A.slice(),Z=D.pop();while(Q&&D.length)Q=D.every((G)=>{return Z.intersects(G,B)}),Z=D.pop();return Q},lR6=(A,B)=>{return k7("comp",A,B),A=nR6(A,B),k7("caret",A),A=pR6(A,B),k7("tildes",A),A=sR6(A,B),k7("xrange",A),A=oR6(A,B),k7("stars",A),A},GX=(A)=>!A||A.toLowerCase()==="x"||A==="*",pR6=(A,B)=>{return A.trim().split(/\s+/).map((Q)=>iR6(Q,B)).join(" ")},iR6=(A,B)=>{let Q=B.loose?pV[ZX.TILDELOOSE]:pV[ZX.TILDE];return A.replace(Q,(D,Z,G,F,I)=>{k7("tilde",A,D,Z,G,F,I);let Y;if(GX(Z))Y="";else if(GX(G))Y=`>=${Z}.0.0 <${+Z+1}.0.0-0`;else if(GX(F))Y=`>=${Z}.${G}.0 <${Z}.${+G+1}.0-0`;else if(I)k7("replaceTilde pr",I),Y=`>=${Z}.${G}.${F}-${I} <${Z}.${+G+1}.0-0`;else Y=`>=${Z}.${G}.${F} <${Z}.${+G+1}.0-0`;return k7("tilde return",Y),Y})},nR6=(A,B)=>{return A.trim().split(/\s+/).map((Q)=>aR6(Q,B)).join(" ")},aR6=(A,B)=>{k7("caret",A,B);let Q=B.loose?pV[ZX.CARETLOOSE]:pV[ZX.CARET],D=B.includePrerelease?"-0":"";return A.replace(Q,(Z,G,F,I,Y)=>{k7("caret",A,Z,G,F,I,Y);let W;if(GX(G))W="";else if(GX(F))W=`>=${G}.0.0${D} <${+G+1}.0.0-0`;else if(GX(I))if(G==="0")W=`>=${G}.${F}.0${D} <${G}.${+F+1}.0-0`;else W=`>=${G}.${F}.0${D} <${+G+1}.0.0-0`;else if(Y)if(k7("replaceCaret pr",Y),G==="0")if(F==="0")W=`>=${G}.${F}.${I}-${Y} <${G}.${F}.${+I+1}-0`;else W=`>=${G}.${F}.${I}-${Y} <${G}.${+F+1}.0-0`;else W=`>=${G}.${F}.${I}-${Y} <${+G+1}.0.0-0`;else if(k7("no pr"),G==="0")if(F==="0")W=`>=${G}.${F}.${I}${D} <${G}.${F}.${+I+1}-0`;else W=`>=${G}.${F}.${I}${D} <${G}.${+F+1}.0-0`;else W=`>=${G}.${F}.${I} <${+G+1}.0.0-0`;return k7("caret return",W),W})},sR6=(A,B)=>{return k7("replaceXRanges",A,B),A.split(/\s+/).map((Q)=>rR6(Q,B)).join(" ")},rR6=(A,B)=>{A=A.trim();let Q=B.loose?pV[ZX.XRANGELOOSE]:pV[ZX.XRANGE];return A.replace(Q,(D,Z,G,F,I,Y)=>{k7("xRange",A,D,Z,G,F,I,Y);let W=GX(G),J=W||GX(F),X=J||GX(I),V=X;if(Z==="="&&V)Z="";if(Y=B.includePrerelease?"-0":"",W)if(Z===">"||Z==="<")D="<0.0.0-0";else D="*";else if(Z&&V){if(J)F=0;if(I=0,Z===">")if(Z=">=",J)G=+G+1,F=0,I=0;else F=+F+1,I=0;else if(Z==="<=")if(Z="<",J)G=+G+1;else F=+F+1;if(Z==="<")Y="-0";D=`${Z+G}.${F}.${I}${Y}`}else if(J)D=`>=${G}.0.0${Y} <${+G+1}.0.0-0`;else if(X)D=`>=${G}.${F}.0${Y} <${G}.${+F+1}.0-0`;return k7("xRange return",D),D})},oR6=(A,B)=>{return k7("replaceStars",A,B),A.trim().replace(pV[ZX.STAR],"")},tR6=(A,B)=>{return k7("replaceGTE0",A,B),A.trim().replace(pV[B.includePrerelease?ZX.GTE0PRE:ZX.GTE0],"")},eR6=(A)=>(B,Q,D,Z,G,F,I,Y,W,J,X,V)=>{if(GX(D))Q="";else if(GX(Z))Q=`>=${D}.0.0${A?"-0":""}`;else if(GX(G))Q=`>=${D}.${Z}.0${A?"-0":""}`;else if(F)Q=`>=${Q}`;else Q=`>=${Q}${A?"-0":""}`;if(GX(W))Y="";else if(GX(J))Y=`<${+W+1}.0.0-0`;else if(GX(X))Y=`<${W}.${+J+1}.0-0`;else if(V)Y=`<=${W}.${J}.${X}-${V}`;else if(A)Y=`<${W}.${J}.${+X+1}-0`;else Y=`<=${Y}`;return`${Q} ${Y}`.trim()},AO6=(A,B,Q)=>{for(let D=0;D<A.length;D++)if(!A[D].test(B))return!1;if(B.prerelease.length&&!Q.includePrerelease){for(let D=0;D<A.length;D++){if(k7(A[D].semver),A[D].semver===FC0.ANY)continue;if(A[D].semver.prerelease.length>0){let Z=A[D].semver;if(Z.major===B.major&&Z.minor===B.minor&&Z.patch===B.patch)return!0}}return!1}return!0}});
var VC0=E((Ii5,X4B)=>{X4B.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}});
var WC0=E((Qi5,F4B)=>{var{spawnSync:aS1}=J1("node:child_process"),{createHash:ZO6}=J1("node:crypto"),A4B=AC0(),GO6=N71(),FO6=gt(),oQB=hS1(),{config:IO6,engines:tQB,optionalDependencies:YO6}=IC0(),WO6=process.env.npm_package_config_libvips||IO6.libvips,B4B=A4B(WO6).version,JO6=["darwin-arm64","darwin-x64","linux-arm","linux-arm64","linux-s390x","linux-x64","linuxmusl-arm64","linuxmusl-x64","win32-ia32","win32-x64"],sS1={encoding:"utf8",shell:!0},XO6=(A)=>{if(A instanceof Error)console.error(`sharp: Installation error: ${A.message}`);else console.log(`sharp: ${A}`)},Q4B=()=>oQB.isNonGlibcLinuxSync()?oQB.familySync():"",VO6=()=>`${process.platform}${Q4B()}-${process.arch}`,ut=()=>{if(D4B())return"wasm32";let{npm_config_arch:A,npm_config_platform:B,npm_config_libc:Q}=process.env,D=typeof Q==="string"?Q:Q4B();return`${B||process.platform}${D}-${A||process.arch}`},CO6=()=>{try{return J1(`@img/sharp-libvips-dev-${ut()}/include`)}catch{try{return (()=>{throw new Error("Cannot require module "+"@img/sharp-libvips-dev/include");})()}catch{}}return""},KO6=()=>{try{return (()=>{throw new Error("Cannot require module "+"@img/sharp-libvips-dev/cplusplus");})()}catch{}return""},HO6=()=>{try{return J1(`@img/sharp-libvips-dev-${ut()}/lib`)}catch{try{return J1(`@img/sharp-libvips-${ut()}/lib`)}catch{}}return""},zO6=()=>{if(process.release?.name==="node"&&process.versions){if(!FO6(process.versions.node,tQB.node))return{found:process.versions.node,expected:tQB.node}}},D4B=()=>{let{CC:A}=process.env;return Boolean(A&&A.endsWith("/emcc"))},EO6=()=>{if(process.platform==="darwin"&&process.arch==="x64")return(aS1("sysctl sysctl.proc_translated",sS1).stdout||"").trim()==="sysctl.proc_translated: 1";return!1},eQB=(A)=>ZO6("sha512").update(A).digest("hex"),UO6=()=>{try{let A=eQB(`imgsharp-libvips-${ut()}`),B=A4B(YO6[`@img/sharp-libvips-${ut()}`]).version;return eQB(`${A}npm:${B}`).slice(0,10)}catch{}return""},wO6=()=>aS1(`node-gyp rebuild --directory=src ${D4B()?"--nodedir=emscripten":""}`,{...sS1,stdio:"inherit"}).status,Z4B=()=>{if(process.platform!=="win32")return(aS1("pkg-config --modversion vips-cpp",{...sS1,env:{...process.env,PKG_CONFIG_PATH:G4B()}}).stdout||"").trim();else return""},G4B=()=>{if(process.platform!=="win32")return[(aS1('which brew >/dev/null 2>&1 && brew environment --plain | grep PKG_CONFIG_LIBDIR | cut -d" " -f2',sS1).stdout||"").trim(),process.env.PKG_CONFIG_PATH,"/usr/local/lib/pkgconfig","/usr/lib/pkgconfig","/usr/local/libdata/pkgconfig","/usr/libdata/pkgconfig"].filter(Boolean).join(":");else return""},YC0=(A,B,Q)=>{if(Q)Q(`Detected ${B}, skipping search for globally-installed libvips`);return A},$O6=(A)=>{if(Boolean(process.env.SHARP_IGNORE_GLOBAL_LIBVIPS)===!0)return YC0(!1,"SHARP_IGNORE_GLOBAL_LIBVIPS",A);if(Boolean(process.env.SHARP_FORCE_GLOBAL_LIBVIPS)===!0)return YC0(!0,"SHARP_FORCE_GLOBAL_LIBVIPS",A);if(EO6())return YC0(!1,"Rosetta",A);let B=Z4B();return!!B&&GO6(B,B4B)};F4B.exports={minimumLibvipsVersion:B4B,prebuiltPlatforms:JO6,buildPlatformArch:ut,buildSharpLibvipsIncludeDir:CO6,buildSharpLibvipsCPlusPlusDir:KO6,buildSharpLibvipsLibDir:HO6,isUnsupportedNodeRuntime:zO6,runtimePlatformArch:VO6,log:XO6,yarnLocator:UO6,spawnRebuild:wO6,globalLibvipsVersion:Z4B,pkgConfigPath:G4B,useGlobalLibvips:$O6}});
var YJ=E((up5,MQB)=>{var uS1=$71(),{MAX_LENGTH:qQB,MAX_SAFE_INTEGER:mS1}=q71(),{safeRe:NQB,safeSrc:LQB,t:dS1}=ft(),WR6=gS1(),{compareIdentifiers:ht}=eV0();class J${constructor(A,B){if(B=WR6(B),A instanceof J$)if(A.loose===!!B.loose&&A.includePrerelease===!!B.includePrerelease)return A;else A=A.version;else if(typeof A!=="string")throw new TypeError(`Invalid version. Must be a string. Got type "${typeof A}".`);if(A.length>qQB)throw new TypeError(`version is longer than ${qQB} characters`);uS1("SemVer",A,B),this.options=B,this.loose=!!B.loose,this.includePrerelease=!!B.includePrerelease;let Q=A.trim().match(B.loose?NQB[dS1.LOOSE]:NQB[dS1.FULL]);if(!Q)throw new TypeError(`Invalid Version: ${A}`);if(this.raw=A,this.major=+Q[1],this.minor=+Q[2],this.patch=+Q[3],this.major>mS1||this.major<0)throw new TypeError("Invalid major version");if(this.minor>mS1||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>mS1||this.patch<0)throw new TypeError("Invalid patch version");if(!Q[4])this.prerelease=[];else this.prerelease=Q[4].split(".").map((D)=>{if(/^[0-9]+$/.test(D)){let Z=+D;if(Z>=0&&Z<mS1)return Z}return D});this.build=Q[5]?Q[5].split("."):[],this.format()}format(){if(this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length)this.version+=`-${this.prerelease.join(".")}`;return this.version}toString(){return this.version}compare(A){if(uS1("SemVer.compare",this.version,this.options,A),!(A instanceof J$)){if(typeof A==="string"&&A===this.version)return 0;A=new J$(A,this.options)}if(A.version===this.version)return 0;return this.compareMain(A)||this.comparePre(A)}compareMain(A){if(!(A instanceof J$))A=new J$(A,this.options);return ht(this.major,A.major)||ht(this.minor,A.minor)||ht(this.patch,A.patch)}comparePre(A){if(!(A instanceof J$))A=new J$(A,this.options);if(this.prerelease.length&&!A.prerelease.length)return-1;else if(!this.prerelease.length&&A.prerelease.length)return 1;else if(!this.prerelease.length&&!A.prerelease.length)return 0;let B=0;do{let Q=this.prerelease[B],D=A.prerelease[B];if(uS1("prerelease compare",B,Q,D),Q===void 0&&D===void 0)return 0;else if(D===void 0)return 1;else if(Q===void 0)return-1;else if(Q===D)continue;else return ht(Q,D)}while(++B)}compareBuild(A){if(!(A instanceof J$))A=new J$(A,this.options);let B=0;do{let Q=this.build[B],D=A.build[B];if(uS1("build compare",B,Q,D),Q===void 0&&D===void 0)return 0;else if(D===void 0)return 1;else if(Q===void 0)return-1;else if(Q===D)continue;else return ht(Q,D)}while(++B)}inc(A,B,Q){if(A.startsWith("pre")){if(!B&&Q===!1)throw new Error("invalid increment argument: identifier is empty");if(B){let D=new RegExp(`^${this.options.loose?LQB[dS1.PRERELEASELOOSE]:LQB[dS1.PRERELEASE]}$`),Z=`-${B}`.match(D);if(!Z||Z[1]!==B)throw new Error(`invalid identifier: ${B}`)}}switch(A){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",B,Q);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",B,Q);break;case"prepatch":this.prerelease.length=0,this.inc("patch",B,Q),this.inc("pre",B,Q);break;case"prerelease":if(this.prerelease.length===0)this.inc("patch",B,Q);this.inc("pre",B,Q);break;case"release":if(this.prerelease.length===0)throw new Error(`version ${this.raw} is not a prerelease`);this.prerelease.length=0;break;case"major":if(this.minor!==0||this.patch!==0||this.prerelease.length===0)this.major++;this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":if(this.patch!==0||this.prerelease.length===0)this.minor++;this.patch=0,this.prerelease=[];break;case"patch":if(this.prerelease.length===0)this.patch++;this.prerelease=[];break;case"pre":{let D=Number(Q)?1:0;if(this.prerelease.length===0)this.prerelease=[D];else{let Z=this.prerelease.length;while(--Z>=0)if(typeof this.prerelease[Z]==="number")this.prerelease[Z]++,Z=-2;if(Z===-1){if(B===this.prerelease.join(".")&&Q===!1)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(D)}}if(B){let Z=[B,D];if(Q===!1)Z=[B];if(ht(this.prerelease[0],B)===0){if(isNaN(this.prerelease[1]))this.prerelease=Z}else this.prerelease=Z}break}default:throw new Error(`invalid increment argument: ${A}`)}if(this.raw=this.format(),this.build.length)this.raw+=`+${this.build.join(".")}`;return this}}MQB.exports=J$});
var _QB=E((pp5,kQB)=>{class yQB{constructor(){this.max=1000,this.map=new Map}get(A){let B=this.map.get(A);if(B===void 0)return;else return this.map.delete(A),this.map.set(A,B),B}delete(A){return this.map.delete(A)}set(A,B){if(!this.delete(A)&&B!==void 0){if(this.map.size>=this.max){let D=this.map.keys().next().value;this.delete(D)}this.map.set(A,B)}return this}}kQB.exports=yQB});
var a9B=E((_p5,n9B)=>{var i9B=()=>process.platform==="linux",bS1=null,vM6=()=>{if(!bS1)if(i9B()&&process.report){let A=process.report.excludeNetwork;process.report.excludeNetwork=!0,bS1=process.report.getReport(),process.report.excludeNetwork=A}else bS1={};return bS1};n9B.exports={isLinux:i9B,getReport:vM6}});
var b4B=E((zi5,v4B)=>{var L9=wM(),k4B={center:0,centre:0,north:1,east:2,south:3,west:4,northeast:5,southeast:6,southwest:7,northwest:8},_4B={top:1,right:2,bottom:3,left:4,"right top":5,"right bottom":6,"left bottom":7,"left top":8},y4B={background:"background",copy:"copy",repeat:"repeat",mirror:"mirror"},x4B={entropy:16,attention:17},UC0={nearest:"nearest",linear:"linear",cubic:"cubic",mitchell:"mitchell",lanczos2:"lanczos2",lanczos3:"lanczos3"},AT6={contain:"contain",cover:"cover",fill:"fill",inside:"inside",outside:"outside"},BT6={contain:"embed",cover:"crop",fill:"ignore_aspect",inside:"max",outside:"min"};function wC0(A){return A.angle%360!==0||A.useExifOrientation===!0||A.rotationAngle!==0}function Bj1(A){return A.width!==-1||A.height!==-1}function QT6(A,B,Q){if(Bj1(this.options))this.options.debuglog("ignoring previous resize options");if(this.options.widthPost!==-1)this.options.debuglog("operation order will be: extract, resize, extract");if(L9.defined(A))if(L9.object(A)&&!L9.defined(Q))Q=A;else if(L9.integer(A)&&A>0)this.options.width=A;else throw L9.invalidParameterError("width","positive integer",A);else this.options.width=-1;if(L9.defined(B))if(L9.integer(B)&&B>0)this.options.height=B;else throw L9.invalidParameterError("height","positive integer",B);else this.options.height=-1;if(L9.object(Q)){if(L9.defined(Q.width))if(L9.integer(Q.width)&&Q.width>0)this.options.width=Q.width;else throw L9.invalidParameterError("width","positive integer",Q.width);if(L9.defined(Q.height))if(L9.integer(Q.height)&&Q.height>0)this.options.height=Q.height;else throw L9.invalidParameterError("height","positive integer",Q.height);if(L9.defined(Q.fit)){let D=BT6[Q.fit];if(L9.string(D))this.options.canvas=D;else throw L9.invalidParameterError("fit","valid fit",Q.fit)}if(L9.defined(Q.position)){let D=L9.integer(Q.position)?Q.position:x4B[Q.position]||_4B[Q.position]||k4B[Q.position];if(L9.integer(D)&&(L9.inRange(D,0,8)||L9.inRange(D,16,17)))this.options.position=D;else throw L9.invalidParameterError("position","valid position/gravity/strategy",Q.position)}if(this._setBackgroundColourOption("resizeBackground",Q.background),L9.defined(Q.kernel))if(L9.string(UC0[Q.kernel]))this.options.kernel=UC0[Q.kernel];else throw L9.invalidParameterError("kernel","valid kernel name",Q.kernel);if(L9.defined(Q.withoutEnlargement))this._setBooleanOption("withoutEnlargement",Q.withoutEnlargement);if(L9.defined(Q.withoutReduction))this._setBooleanOption("withoutReduction",Q.withoutReduction);if(L9.defined(Q.fastShrinkOnLoad))this._setBooleanOption("fastShrinkOnLoad",Q.fastShrinkOnLoad)}if(wC0(this.options)&&Bj1(this.options))this.options.rotateBeforePreExtract=!0;return this}function DT6(A){if(L9.integer(A)&&A>0)this.options.extendTop=A,this.options.extendBottom=A,this.options.extendLeft=A,this.options.extendRight=A;else if(L9.object(A)){if(L9.defined(A.top))if(L9.integer(A.top)&&A.top>=0)this.options.extendTop=A.top;else throw L9.invalidParameterError("top","positive integer",A.top);if(L9.defined(A.bottom))if(L9.integer(A.bottom)&&A.bottom>=0)this.options.extendBottom=A.bottom;else throw L9.invalidParameterError("bottom","positive integer",A.bottom);if(L9.defined(A.left))if(L9.integer(A.left)&&A.left>=0)this.options.extendLeft=A.left;else throw L9.invalidParameterError("left","positive integer",A.left);if(L9.defined(A.right))if(L9.integer(A.right)&&A.right>=0)this.options.extendRight=A.right;else throw L9.invalidParameterError("right","positive integer",A.right);if(this._setBackgroundColourOption("extendBackground",A.background),L9.defined(A.extendWith))if(L9.string(y4B[A.extendWith]))this.options.extendWith=y4B[A.extendWith];else throw L9.invalidParameterError("extendWith","one of: background, copy, repeat, mirror",A.extendWith)}else throw L9.invalidParameterError("extend","integer or object",A);return this}function ZT6(A){let B=Bj1(this.options)||this.options.widthPre!==-1?"Post":"Pre";if(this.options[`width${B}`]!==-1)this.options.debuglog("ignoring previous extract options");if(["left","top","width","height"].forEach(function(Q){let D=A[Q];if(L9.integer(D)&&D>=0)this.options[Q+(Q==="left"||Q==="top"?"Offset":"")+B]=D;else throw L9.invalidParameterError(Q,"integer",D)},this),wC0(this.options)&&!Bj1(this.options)){if(this.options.widthPre===-1||this.options.widthPost===-1)this.options.rotateBeforePreExtract=!0}return this}function GT6(A){if(this.options.trimThreshold=10,L9.defined(A))if(L9.object(A)){if(L9.defined(A.background))this._setBackgroundColourOption("trimBackground",A.background);if(L9.defined(A.threshold))if(L9.number(A.threshold)&&A.threshold>=0)this.options.trimThreshold=A.threshold;else throw L9.invalidParameterError("threshold","positive number",A.threshold);if(L9.defined(A.lineArt))this._setBooleanOption("trimLineArt",A.lineArt)}else throw L9.invalidParameterError("trim","object",A);if(wC0(this.options))this.options.rotateBeforePreExtract=!0;return this}v4B.exports=function(A){Object.assign(A.prototype,{resize:QT6,extend:DT6,extract:ZT6,trim:GT6}),A.gravity=k4B,A.strategy=x4B,A.kernel=UC0,A.fit=AT6,A.position=_4B}});
var eV0=E((gp5,$QB)=>{var UQB=/^[0-9]+$/,wQB=(A,B)=>{let Q=UQB.test(A),D=UQB.test(B);if(Q&&D)A=+A,B=+B;return A===B?0:Q&&!D?-1:D&&!Q?1:A<B?-1:1},YR6=(A,B)=>wQB(B,A);$QB.exports={compareIdentifiers:wQB,rcompareIdentifiers:YR6}});
var ft=E((NM,zQB)=>{var{MAX_SAFE_COMPONENT_LENGTH:oV0,MAX_SAFE_BUILD_LENGTH:rM6,MAX_LENGTH:oM6}=q71(),tM6=$71();NM=zQB.exports={};var eM6=NM.re=[],AR6=NM.safeRe=[],KB=NM.src=[],BR6=NM.safeSrc=[],HB=NM.t={},QR6=0,tV0="[a-zA-Z0-9-]",DR6=[["\\s",1],["\\d",oM6],[tV0,rM6]],ZR6=(A)=>{for(let[B,Q]of DR6)A=A.split(`${B}*`).join(`${B}{0,${Q}}`).split(`${B}+`).join(`${B}{1,${Q}}`);return A},tQ=(A,B,Q)=>{let D=ZR6(B),Z=QR6++;tM6(A,Z,B),HB[A]=Z,KB[Z]=B,BR6[Z]=D,eM6[Z]=new RegExp(B,Q?"g":void 0),AR6[Z]=new RegExp(D,Q?"g":void 0)};tQ("NUMERICIDENTIFIER","0|[1-9]\\d*");tQ("NUMERICIDENTIFIERLOOSE","\\d+");tQ("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${tV0}*`);tQ("MAINVERSION",`(${KB[HB.NUMERICIDENTIFIER]})\\.(${KB[HB.NUMERICIDENTIFIER]})\\.(${KB[HB.NUMERICIDENTIFIER]})`);tQ("MAINVERSIONLOOSE",`(${KB[HB.NUMERICIDENTIFIERLOOSE]})\\.(${KB[HB.NUMERICIDENTIFIERLOOSE]})\\.(${KB[HB.NUMERICIDENTIFIERLOOSE]})`);tQ("PRERELEASEIDENTIFIER",`(?:${KB[HB.NUMERICIDENTIFIER]}|${KB[HB.NONNUMERICIDENTIFIER]})`);tQ("PRERELEASEIDENTIFIERLOOSE",`(?:${KB[HB.NUMERICIDENTIFIERLOOSE]}|${KB[HB.NONNUMERICIDENTIFIER]})`);tQ("PRERELEASE",`(?:-(${KB[HB.PRERELEASEIDENTIFIER]}(?:\\.${KB[HB.PRERELEASEIDENTIFIER]})*))`);tQ("PRERELEASELOOSE",`(?:-?(${KB[HB.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${KB[HB.PRERELEASEIDENTIFIERLOOSE]})*))`);tQ("BUILDIDENTIFIER",`${tV0}+`);tQ("BUILD",`(?:\\+(${KB[HB.BUILDIDENTIFIER]}(?:\\.${KB[HB.BUILDIDENTIFIER]})*))`);tQ("FULLPLAIN",`v?${KB[HB.MAINVERSION]}${KB[HB.PRERELEASE]}?${KB[HB.BUILD]}?`);tQ("FULL",`^${KB[HB.FULLPLAIN]}$`);tQ("LOOSEPLAIN",`[v=\\s]*${KB[HB.MAINVERSIONLOOSE]}${KB[HB.PRERELEASELOOSE]}?${KB[HB.BUILD]}?`);tQ("LOOSE",`^${KB[HB.LOOSEPLAIN]}$`);tQ("GTLT","((?:<|>)?=?)");tQ("XRANGEIDENTIFIERLOOSE",`${KB[HB.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`);tQ("XRANGEIDENTIFIER",`${KB[HB.NUMERICIDENTIFIER]}|x|X|\\*`);tQ("XRANGEPLAIN",`[v=\\s]*(${KB[HB.XRANGEIDENTIFIER]})(?:\\.(${KB[HB.XRANGEIDENTIFIER]})(?:\\.(${KB[HB.XRANGEIDENTIFIER]})(?:${KB[HB.PRERELEASE]})?${KB[HB.BUILD]}?)?)?`);tQ("XRANGEPLAINLOOSE",`[v=\\s]*(${KB[HB.XRANGEIDENTIFIERLOOSE]})(?:\\.(${KB[HB.XRANGEIDENTIFIERLOOSE]})(?:\\.(${KB[HB.XRANGEIDENTIFIERLOOSE]})(?:${KB[HB.PRERELEASELOOSE]})?${KB[HB.BUILD]}?)?)?`);tQ("XRANGE",`^${KB[HB.GTLT]}\\s*${KB[HB.XRANGEPLAIN]}$`);tQ("XRANGELOOSE",`^${KB[HB.GTLT]}\\s*${KB[HB.XRANGEPLAINLOOSE]}$`);tQ("COERCEPLAIN",`(^|[^\\d])(\\d{1,${oV0}})(?:\\.(\\d{1,${oV0}}))?(?:\\.(\\d{1,${oV0}}))?`);tQ("COERCE",`${KB[HB.COERCEPLAIN]}(?:$|[^\\d])`);tQ("COERCEFULL",KB[HB.COERCEPLAIN]+`(?:${KB[HB.PRERELEASE]})?(?:${KB[HB.BUILD]})?(?:$|[^\\d])`);tQ("COERCERTL",KB[HB.COERCE],!0);tQ("COERCERTLFULL",KB[HB.COERCEFULL],!0);tQ("LONETILDE","(?:~>?)");tQ("TILDETRIM",`(\\s*)${KB[HB.LONETILDE]}\\s+`,!0);NM.tildeTrimReplace="$1~";tQ("TILDE",`^${KB[HB.LONETILDE]}${KB[HB.XRANGEPLAIN]}$`);tQ("TILDELOOSE",`^${KB[HB.LONETILDE]}${KB[HB.XRANGEPLAINLOOSE]}$`);tQ("LONECARET","(?:\\^)");tQ("CARETTRIM",`(\\s*)${KB[HB.LONECARET]}\\s+`,!0);NM.caretTrimReplace="$1^";tQ("CARET",`^${KB[HB.LONECARET]}${KB[HB.XRANGEPLAIN]}$`);tQ("CARETLOOSE",`^${KB[HB.LONECARET]}${KB[HB.XRANGEPLAINLOOSE]}$`);tQ("COMPARATORLOOSE",`^${KB[HB.GTLT]}\\s*(${KB[HB.LOOSEPLAIN]})$|^$`);tQ("COMPARATOR",`^${KB[HB.GTLT]}\\s*(${KB[HB.FULLPLAIN]})$|^$`);tQ("COMPARATORTRIM",`(\\s*)${KB[HB.GTLT]}\\s*(${KB[HB.LOOSEPLAIN]}|${KB[HB.XRANGEPLAIN]})`,!0);NM.comparatorTrimReplace="$1$2$3";tQ("HYPHENRANGE",`^\\s*(${KB[HB.XRANGEPLAIN]})\\s+-\\s+(${KB[HB.XRANGEPLAIN]})\\s*$`);tQ("HYPHENRANGELOOSE",`^\\s*(${KB[HB.XRANGEPLAINLOOSE]})\\s+-\\s+(${KB[HB.XRANGEPLAINLOOSE]})\\s*$`);tQ("STAR","(<|>)?=?\\s*\\*");tQ("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$");tQ("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")});
var gS1=E((hp5,EQB)=>{var GR6=Object.freeze({loose:!0}),FR6=Object.freeze({}),IR6=(A)=>{if(!A)return FR6;if(typeof A!=="object")return GR6;return A};EQB.exports=IR6});
var gt=E((Ai5,rQB)=>{var BO6=TE(),QO6=(A,B,Q)=>{try{B=new BO6(B,Q)}catch(D){return!1}return B.test(A)};rQB.exports=QO6});
var h4B=E((Ei5,f4B)=>{var Y3=wM(),$C0={clear:"clear",source:"source",over:"over",in:"in",out:"out",atop:"atop",dest:"dest","dest-over":"dest-over","dest-in":"dest-in","dest-out":"dest-out","dest-atop":"dest-atop",xor:"xor",add:"add",saturate:"saturate",multiply:"multiply",screen:"screen",overlay:"overlay",darken:"darken",lighten:"lighten","colour-dodge":"colour-dodge","color-dodge":"colour-dodge","colour-burn":"colour-burn","color-burn":"colour-burn","hard-light":"hard-light","soft-light":"soft-light",difference:"difference",exclusion:"exclusion"};function FT6(A){if(!Array.isArray(A))throw Y3.invalidParameterError("images to composite","array",A);return this.options.composite=A.map((B)=>{if(!Y3.object(B))throw Y3.invalidParameterError("image to composite","object",B);let Q=this._inputOptionsFromObject(B),D={input:this._createInputDescriptor(B.input,Q,{allowStream:!1}),blend:"over",tile:!1,left:0,top:0,hasOffset:!1,gravity:0,premultiplied:!1};if(Y3.defined(B.blend))if(Y3.string($C0[B.blend]))D.blend=$C0[B.blend];else throw Y3.invalidParameterError("blend","valid blend name",B.blend);if(Y3.defined(B.tile))if(Y3.bool(B.tile))D.tile=B.tile;else throw Y3.invalidParameterError("tile","boolean",B.tile);if(Y3.defined(B.left))if(Y3.integer(B.left))D.left=B.left;else throw Y3.invalidParameterError("left","integer",B.left);if(Y3.defined(B.top))if(Y3.integer(B.top))D.top=B.top;else throw Y3.invalidParameterError("top","integer",B.top);if(Y3.defined(B.top)!==Y3.defined(B.left))throw new Error("Expected both left and top to be set");else D.hasOffset=Y3.integer(B.top)&&Y3.integer(B.left);if(Y3.defined(B.gravity))if(Y3.integer(B.gravity)&&Y3.inRange(B.gravity,0,8))D.gravity=B.gravity;else if(Y3.string(B.gravity)&&Y3.integer(this.constructor.gravity[B.gravity]))D.gravity=this.constructor.gravity[B.gravity];else throw Y3.invalidParameterError("gravity","valid gravity",B.gravity);if(Y3.defined(B.premultiplied))if(Y3.bool(B.premultiplied))D.premultiplied=B.premultiplied;else throw Y3.invalidParameterError("premultiplied","boolean",B.premultiplied);return D}),this}f4B.exports=function(A){A.prototype.composite=FT6,A.blend=$C0}});
var hS1=E((vp5,CQB)=>{var e9B=J1("child_process"),{isLinux:bt,getReport:AQB}=a9B(),{LDD_PATH:fS1,readFile:BQB,readFileSync:QQB}=o9B(),$M,qM,zx="",DQB=()=>{if(!zx)return new Promise((A)=>{e9B.exec("getconf GNU_LIBC_VERSION 2>&1 || true; ldd --version 2>&1 || true",(B,Q)=>{zx=B?" ":Q,A(zx)})});return zx},ZQB=()=>{if(!zx)try{zx=e9B.execSync("getconf GNU_LIBC_VERSION 2>&1 || true; ldd --version 2>&1 || true",{encoding:"utf8"})}catch(A){zx=" "}return zx},Ex="glibc",GQB=/LIBC[a-z0-9 \-).]*?(\d+\.\d+)/i,vt="musl",hM6=(A)=>A.includes("libc.musl-")||A.includes("ld-musl-"),FQB=()=>{let A=AQB();if(A.header&&A.header.glibcVersionRuntime)return Ex;if(Array.isArray(A.sharedObjects)){if(A.sharedObjects.some(hM6))return vt}return null},IQB=(A)=>{let[B,Q]=A.split(/[\r\n]+/);if(B&&B.includes(Ex))return Ex;if(Q&&Q.includes(vt))return vt;return null},YQB=(A)=>{if(A.includes("musl"))return vt;if(A.includes("GNU C Library"))return Ex;return null},gM6=async()=>{if($M!==void 0)return $M;$M=null;try{let A=await BQB(fS1);$M=YQB(A)}catch(A){}return $M},uM6=()=>{if($M!==void 0)return $M;$M=null;try{let A=QQB(fS1);$M=YQB(A)}catch(A){}return $M},WQB=async()=>{let A=null;if(bt()){if(A=await gM6(),!A)A=FQB();if(!A){let B=await DQB();A=IQB(B)}}return A},JQB=()=>{let A=null;if(bt()){if(A=uM6(),!A)A=FQB();if(!A){let B=ZQB();A=IQB(B)}}return A},mM6=async()=>bt()&&await WQB()!==Ex,dM6=()=>bt()&&JQB()!==Ex,cM6=async()=>{if(qM!==void 0)return qM;qM=null;try{let B=(await BQB(fS1)).match(GQB);if(B)qM=B[1]}catch(A){}return qM},lM6=()=>{if(qM!==void 0)return qM;qM=null;try{let B=QQB(fS1).match(GQB);if(B)qM=B[1]}catch(A){}return qM},XQB=()=>{let A=AQB();if(A.header&&A.header.glibcVersionRuntime)return A.header.glibcVersionRuntime;return null},t9B=(A)=>A.trim().split(/\s+/)[1],VQB=(A)=>{let[B,Q,D]=A.split(/[\r\n]+/);if(B&&B.includes(Ex))return t9B(B);if(Q&&D&&Q.includes(vt))return t9B(D);return null},pM6=async()=>{let A=null;if(bt()){if(A=await cM6(),!A)A=XQB();if(!A){let B=await DQB();A=VQB(B)}}return A},iM6=()=>{let A=null;if(bt()){if(A=lM6(),!A)A=XQB();if(!A){let B=ZQB();A=VQB(B)}}return A};CQB.exports={GLIBC:Ex,MUSL:vt,family:WQB,familySync:JQB,isNonGlibcLinux:mM6,isNonGlibcLinuxSync:dM6,version:pM6,versionSync:iM6}});
var i4B=E(($i5,p4B)=>{var LM=wM(),fT6={and:"and",or:"or",eor:"eor"};function hT6(){return this.options.removeAlpha=!0,this}function gT6(A){if(LM.defined(A))if(LM.number(A)&&LM.inRange(A,0,1))this.options.ensureAlpha=A;else throw LM.invalidParameterError("alpha","number between 0 and 1",A);else this.options.ensureAlpha=1;return this}function uT6(A){let B={red:0,green:1,blue:2,alpha:3};if(Object.keys(B).includes(A))A=B[A];if(LM.integer(A)&&LM.inRange(A,0,4))this.options.extractChannel=A;else throw LM.invalidParameterError("channel","integer or one of: red, green, blue, alpha",A);return this}function mT6(A,B){if(Array.isArray(A))A.forEach(function(Q){this.options.joinChannelIn.push(this._createInputDescriptor(Q,B))},this);else this.options.joinChannelIn.push(this._createInputDescriptor(A,B));return this}function dT6(A){if(LM.string(A)&&LM.inArray(A,["and","or","eor"]))this.options.bandBoolOp=A;else throw LM.invalidParameterError("boolOp","one of: and, or, eor",A);return this}p4B.exports=function(A){Object.assign(A.prototype,{removeAlpha:hT6,ensureAlpha:gT6,extractChannel:uT6,joinChannel:mT6,bandbool:dT6}),A.bool=fT6}});
var iS1=E((rp5,hQB)=>{var RR6=OE(),OR6=(A,B,Q)=>RR6(A,B,Q)<=0;hQB.exports=OR6});
var j4B=E((Hi5,S4B)=>{var iO6=Aj1(),IA=wM(),wx=T71(),nO6={left:"low",center:"centre",centre:"centre",right:"high"};function P4B(A){let{raw:B,density:Q,limitInputPixels:D,ignoreIcc:Z,unlimited:G,sequentialRead:F,failOn:I,failOnError:Y,animated:W,page:J,pages:X,subifd:V}=A;return[B,Q,D,Z,G,F,I,Y,W,J,X,V].some(IA.defined)?{raw:B,density:Q,limitInputPixels:D,ignoreIcc:Z,unlimited:G,sequentialRead:F,failOn:I,failOnError:Y,animated:W,page:J,pages:X,subifd:V}:void 0}function aO6(A,B,Q){let D={failOn:"warning",limitInputPixels:Math.pow(16383,2),ignoreIcc:!1,unlimited:!1,sequentialRead:!0};if(IA.string(A))D.file=A;else if(IA.buffer(A)){if(A.length===0)throw Error("Input Buffer is empty");D.buffer=A}else if(IA.arrayBuffer(A)){if(A.byteLength===0)throw Error("Input bit Array is empty");D.buffer=Buffer.from(A,0,A.byteLength)}else if(IA.typedArray(A)){if(A.length===0)throw Error("Input Bit Array is empty");D.buffer=Buffer.from(A.buffer,A.byteOffset,A.byteLength)}else if(IA.plainObject(A)&&!IA.defined(B)){if(B=A,P4B(B))D.buffer=[]}else if(!IA.defined(A)&&!IA.defined(B)&&IA.object(Q)&&Q.allowStream)D.buffer=[];else throw new Error(`Unsupported input '${A}' of type ${typeof A}${IA.defined(B)?` when also providing options of type ${typeof B}`:""}`);if(IA.object(B)){if(IA.defined(B.failOnError))if(IA.bool(B.failOnError))D.failOn=B.failOnError?"warning":"none";else throw IA.invalidParameterError("failOnError","boolean",B.failOnError);if(IA.defined(B.failOn))if(IA.string(B.failOn)&&IA.inArray(B.failOn,["none","truncated","error","warning"]))D.failOn=B.failOn;else throw IA.invalidParameterError("failOn","one of: none, truncated, error, warning",B.failOn);if(IA.defined(B.density))if(IA.inRange(B.density,1,1e5))D.density=B.density;else throw IA.invalidParameterError("density","number between 1 and 100000",B.density);if(IA.defined(B.ignoreIcc))if(IA.bool(B.ignoreIcc))D.ignoreIcc=B.ignoreIcc;else throw IA.invalidParameterError("ignoreIcc","boolean",B.ignoreIcc);if(IA.defined(B.limitInputPixels))if(IA.bool(B.limitInputPixels))D.limitInputPixels=B.limitInputPixels?Math.pow(16383,2):0;else if(IA.integer(B.limitInputPixels)&&IA.inRange(B.limitInputPixels,0,Number.MAX_SAFE_INTEGER))D.limitInputPixels=B.limitInputPixels;else throw IA.invalidParameterError("limitInputPixels","positive integer",B.limitInputPixels);if(IA.defined(B.unlimited))if(IA.bool(B.unlimited))D.unlimited=B.unlimited;else throw IA.invalidParameterError("unlimited","boolean",B.unlimited);if(IA.defined(B.sequentialRead))if(IA.bool(B.sequentialRead))D.sequentialRead=B.sequentialRead;else throw IA.invalidParameterError("sequentialRead","boolean",B.sequentialRead);if(IA.defined(B.raw))if(IA.object(B.raw)&&IA.integer(B.raw.width)&&B.raw.width>0&&IA.integer(B.raw.height)&&B.raw.height>0&&IA.integer(B.raw.channels)&&IA.inRange(B.raw.channels,1,4))switch(D.rawWidth=B.raw.width,D.rawHeight=B.raw.height,D.rawChannels=B.raw.channels,D.rawPremultiplied=!!B.raw.premultiplied,A.constructor){case Uint8Array:case Uint8ClampedArray:D.rawDepth="uchar";break;case Int8Array:D.rawDepth="char";break;case Uint16Array:D.rawDepth="ushort";break;case Int16Array:D.rawDepth="short";break;case Uint32Array:D.rawDepth="uint";break;case Int32Array:D.rawDepth="int";break;case Float32Array:D.rawDepth="float";break;case Float64Array:D.rawDepth="double";break;default:D.rawDepth="uchar";break}else throw new Error("Expected width, height and channels for raw pixel input");if(IA.defined(B.animated))if(IA.bool(B.animated))D.pages=B.animated?-1:1;else throw IA.invalidParameterError("animated","boolean",B.animated);if(IA.defined(B.pages))if(IA.integer(B.pages)&&IA.inRange(B.pages,-1,1e5))D.pages=B.pages;else throw IA.invalidParameterError("pages","integer between -1 and 100000",B.pages);if(IA.defined(B.page))if(IA.integer(B.page)&&IA.inRange(B.page,0,1e5))D.page=B.page;else throw IA.invalidParameterError("page","integer between 0 and 100000",B.page);if(IA.defined(B.level))if(IA.integer(B.level)&&IA.inRange(B.level,0,256))D.level=B.level;else throw IA.invalidParameterError("level","integer between 0 and 256",B.level);if(IA.defined(B.subifd))if(IA.integer(B.subifd)&&IA.inRange(B.subifd,-1,1e5))D.subifd=B.subifd;else throw IA.invalidParameterError("subifd","integer between -1 and 100000",B.subifd);if(IA.defined(B.create))if(IA.object(B.create)&&IA.integer(B.create.width)&&B.create.width>0&&IA.integer(B.create.height)&&B.create.height>0&&IA.integer(B.create.channels)){if(D.createWidth=B.create.width,D.createHeight=B.create.height,D.createChannels=B.create.channels,IA.defined(B.create.noise)){if(!IA.object(B.create.noise))throw new Error("Expected noise to be an object");if(!IA.inArray(B.create.noise.type,["gaussian"]))throw new Error("Only gaussian noise is supported at the moment");if(!IA.inRange(B.create.channels,1,4))throw IA.invalidParameterError("create.channels","number between 1 and 4",B.create.channels);if(D.createNoiseType=B.create.noise.type,IA.number(B.create.noise.mean)&&IA.inRange(B.create.noise.mean,0,1e4))D.createNoiseMean=B.create.noise.mean;else throw IA.invalidParameterError("create.noise.mean","number between 0 and 10000",B.create.noise.mean);if(IA.number(B.create.noise.sigma)&&IA.inRange(B.create.noise.sigma,0,1e4))D.createNoiseSigma=B.create.noise.sigma;else throw IA.invalidParameterError("create.noise.sigma","number between 0 and 10000",B.create.noise.sigma)}else if(IA.defined(B.create.background)){if(!IA.inRange(B.create.channels,3,4))throw IA.invalidParameterError("create.channels","number between 3 and 4",B.create.channels);let Z=iO6(B.create.background);D.createBackground=[Z.red(),Z.green(),Z.blue(),Math.round(Z.alpha()*255)]}else throw new Error("Expected valid noise or background to create a new input image");delete D.buffer}else throw new Error("Expected valid width, height and channels to create a new input image");if(IA.defined(B.text))if(IA.object(B.text)&&IA.string(B.text.text)){if(D.textValue=B.text.text,IA.defined(B.text.height)&&IA.defined(B.text.dpi))throw new Error("Expected only one of dpi or height");if(IA.defined(B.text.font))if(IA.string(B.text.font))D.textFont=B.text.font;else throw IA.invalidParameterError("text.font","string",B.text.font);if(IA.defined(B.text.fontfile))if(IA.string(B.text.fontfile))D.textFontfile=B.text.fontfile;else throw IA.invalidParameterError("text.fontfile","string",B.text.fontfile);if(IA.defined(B.text.width))if(IA.integer(B.text.width)&&B.text.width>0)D.textWidth=B.text.width;else throw IA.invalidParameterError("text.width","positive integer",B.text.width);if(IA.defined(B.text.height))if(IA.integer(B.text.height)&&B.text.height>0)D.textHeight=B.text.height;else throw IA.invalidParameterError("text.height","positive integer",B.text.height);if(IA.defined(B.text.align))if(IA.string(B.text.align)&&IA.string(this.constructor.align[B.text.align]))D.textAlign=this.constructor.align[B.text.align];else throw IA.invalidParameterError("text.align","valid alignment",B.text.align);if(IA.defined(B.text.justify))if(IA.bool(B.text.justify))D.textJustify=B.text.justify;else throw IA.invalidParameterError("text.justify","boolean",B.text.justify);if(IA.defined(B.text.dpi))if(IA.integer(B.text.dpi)&&IA.inRange(B.text.dpi,1,1e6))D.textDpi=B.text.dpi;else throw IA.invalidParameterError("text.dpi","integer between 1 and 1000000",B.text.dpi);if(IA.defined(B.text.rgba))if(IA.bool(B.text.rgba))D.textRgba=B.text.rgba;else throw IA.invalidParameterError("text.rgba","bool",B.text.rgba);if(IA.defined(B.text.spacing))if(IA.integer(B.text.spacing)&&IA.inRange(B.text.spacing,-1e6,1e6))D.textSpacing=B.text.spacing;else throw IA.invalidParameterError("text.spacing","integer between -1000000 and 1000000",B.text.spacing);if(IA.defined(B.text.wrap))if(IA.string(B.text.wrap)&&IA.inArray(B.text.wrap,["word","char","word-char","none"]))D.textWrap=B.text.wrap;else throw IA.invalidParameterError("text.wrap","one of: word, char, word-char, none",B.text.wrap);delete D.buffer}else throw new Error("Expected a valid string to create an image with text.")}else if(IA.defined(B))throw new Error("Invalid input options "+B);return D}function sO6(A,B,Q){if(Array.isArray(this.options.input.buffer))if(IA.buffer(A)){if(this.options.input.buffer.length===0)this.on("finish",()=>{this.streamInFinished=!0});this.options.input.buffer.push(A),Q()}else Q(new Error("Non-Buffer data on Writable Stream"));else Q(new Error("Unexpected data on Writable Stream"))}function rO6(){if(this._isStreamInput())this.options.input.buffer=Buffer.concat(this.options.input.buffer)}function oO6(){return Array.isArray(this.options.input.buffer)}function tO6(A){let B=Error();if(IA.fn(A)){if(this._isStreamInput())this.on("finish",()=>{this._flattenBufferIn(),wx.metadata(this.options,(Q,D)=>{if(Q)A(IA.nativeError(Q,B));else A(null,D)})});else wx.metadata(this.options,(Q,D)=>{if(Q)A(IA.nativeError(Q,B));else A(null,D)});return this}else if(this._isStreamInput())return new Promise((Q,D)=>{let Z=()=>{this._flattenBufferIn(),wx.metadata(this.options,(G,F)=>{if(G)D(IA.nativeError(G,B));else Q(F)})};if(this.writableFinished)Z();else this.once("finish",Z)});else return new Promise((Q,D)=>{wx.metadata(this.options,(Z,G)=>{if(Z)D(IA.nativeError(Z,B));else Q(G)})})}function eO6(A){let B=Error();if(IA.fn(A)){if(this._isStreamInput())this.on("finish",()=>{this._flattenBufferIn(),wx.stats(this.options,(Q,D)=>{if(Q)A(IA.nativeError(Q,B));else A(null,D)})});else wx.stats(this.options,(Q,D)=>{if(Q)A(IA.nativeError(Q,B));else A(null,D)});return this}else if(this._isStreamInput())return new Promise((Q,D)=>{this.on("finish",function(){this._flattenBufferIn(),wx.stats(this.options,(Z,G)=>{if(Z)D(IA.nativeError(Z,B));else Q(G)})})});else return new Promise((Q,D)=>{wx.stats(this.options,(Z,G)=>{if(Z)D(IA.nativeError(Z,B));else Q(G)})})}S4B.exports=function(A){Object.assign(A.prototype,{_inputOptionsFromObject:P4B,_createInputDescriptor:aO6,_write:sO6,_flattenBufferIn:rO6,_isStreamInput:oO6,metadata:tO6,stats:eO6}),A.align=nO6}});
var l4B=E((wi5,c4B)=>{var PT6=Aj1(),jP=wM(),d4B={multiband:"multiband","b-w":"b-w",bw:"b-w",cmyk:"cmyk",srgb:"srgb"};function ST6(A){return this._setBackgroundColourOption("tint",A),this}function jT6(A){return this.options.greyscale=jP.bool(A)?A:!0,this}function yT6(A){return this.greyscale(A)}function kT6(A){if(!jP.string(A))throw jP.invalidParameterError("colourspace","string",A);return this.options.colourspacePipeline=A,this}function _T6(A){return this.pipelineColourspace(A)}function xT6(A){if(!jP.string(A))throw jP.invalidParameterError("colourspace","string",A);return this.options.colourspace=A,this}function vT6(A){return this.toColourspace(A)}function bT6(A,B){if(jP.defined(B))if(jP.object(B)||jP.string(B)){let Q=PT6(B);this.options[A]=[Q.red(),Q.green(),Q.blue(),Math.round(Q.alpha()*255)]}else throw jP.invalidParameterError("background","object or string",B)}c4B.exports=function(A){Object.assign(A.prototype,{tint:ST6,greyscale:jT6,grayscale:yT6,pipelineColourspace:kT6,pipelineColorspace:_T6,toColourspace:xT6,toColorspace:vT6,_setBackgroundColourOption:bT6}),A.colourspace=d4B,A.colorspace=d4B}});
var m4B=E((Ui5,u4B)=>{var IT6=Aj1(),n0=wM(),g4B={integer:"integer",float:"float",approximate:"approximate"};function YT6(A,B){if(this.options.useExifOrientation||this.options.angle||this.options.rotationAngle)this.options.debuglog("ignoring previous rotate options");if(!n0.defined(A))this.options.useExifOrientation=!0;else if(n0.integer(A)&&!(A%90))this.options.angle=A;else if(n0.number(A)){if(this.options.rotationAngle=A,n0.object(B)&&B.background){let Q=IT6(B.background);this.options.rotationBackground=[Q.red(),Q.green(),Q.blue(),Math.round(Q.alpha()*255)]}}else throw n0.invalidParameterError("angle","numeric",A);return this}function WT6(A){return this.options.flip=n0.bool(A)?A:!0,this}function JT6(A){return this.options.flop=n0.bool(A)?A:!0,this}function XT6(A,B){let Q=[].concat(...A);if(Q.length===4&&Q.every(n0.number))this.options.affineMatrix=Q;else throw n0.invalidParameterError("matrix","1x4 or 2x2 array",A);if(n0.defined(B))if(n0.object(B)){if(this._setBackgroundColourOption("affineBackground",B.background),n0.defined(B.idx))if(n0.number(B.idx))this.options.affineIdx=B.idx;else throw n0.invalidParameterError("options.idx","number",B.idx);if(n0.defined(B.idy))if(n0.number(B.idy))this.options.affineIdy=B.idy;else throw n0.invalidParameterError("options.idy","number",B.idy);if(n0.defined(B.odx))if(n0.number(B.odx))this.options.affineOdx=B.odx;else throw n0.invalidParameterError("options.odx","number",B.odx);if(n0.defined(B.ody))if(n0.number(B.ody))this.options.affineOdy=B.ody;else throw n0.invalidParameterError("options.ody","number",B.ody);if(n0.defined(B.interpolator))if(n0.inArray(B.interpolator,Object.values(this.constructor.interpolators)))this.options.affineInterpolator=B.interpolator;else throw n0.invalidParameterError("options.interpolator","valid interpolator name",B.interpolator)}else throw n0.invalidParameterError("options","object",B);return this}function VT6(A,B,Q){if(!n0.defined(A))this.options.sharpenSigma=-1;else if(n0.bool(A))this.options.sharpenSigma=A?-1:0;else if(n0.number(A)&&n0.inRange(A,0.01,1e4)){if(this.options.sharpenSigma=A,n0.defined(B))if(n0.number(B)&&n0.inRange(B,0,1e4))this.options.sharpenM1=B;else throw n0.invalidParameterError("flat","number between 0 and 10000",B);if(n0.defined(Q))if(n0.number(Q)&&n0.inRange(Q,0,1e4))this.options.sharpenM2=Q;else throw n0.invalidParameterError("jagged","number between 0 and 10000",Q)}else if(n0.plainObject(A)){if(n0.number(A.sigma)&&n0.inRange(A.sigma,0.000001,10))this.options.sharpenSigma=A.sigma;else throw n0.invalidParameterError("options.sigma","number between 0.000001 and 10",A.sigma);if(n0.defined(A.m1))if(n0.number(A.m1)&&n0.inRange(A.m1,0,1e6))this.options.sharpenM1=A.m1;else throw n0.invalidParameterError("options.m1","number between 0 and 1000000",A.m1);if(n0.defined(A.m2))if(n0.number(A.m2)&&n0.inRange(A.m2,0,1e6))this.options.sharpenM2=A.m2;else throw n0.invalidParameterError("options.m2","number between 0 and 1000000",A.m2);if(n0.defined(A.x1))if(n0.number(A.x1)&&n0.inRange(A.x1,0,1e6))this.options.sharpenX1=A.x1;else throw n0.invalidParameterError("options.x1","number between 0 and 1000000",A.x1);if(n0.defined(A.y2))if(n0.number(A.y2)&&n0.inRange(A.y2,0,1e6))this.options.sharpenY2=A.y2;else throw n0.invalidParameterError("options.y2","number between 0 and 1000000",A.y2);if(n0.defined(A.y3))if(n0.number(A.y3)&&n0.inRange(A.y3,0,1e6))this.options.sharpenY3=A.y3;else throw n0.invalidParameterError("options.y3","number between 0 and 1000000",A.y3)}else throw n0.invalidParameterError("sigma","number between 0.01 and 10000",A);return this}function CT6(A){if(!n0.defined(A))this.options.medianSize=3;else if(n0.integer(A)&&n0.inRange(A,1,1000))this.options.medianSize=A;else throw n0.invalidParameterError("size","integer between 1 and 1000",A);return this}function KT6(A){let B;if(n0.number(A))B=A;else if(n0.plainObject(A)){if(!n0.number(A.sigma))throw n0.invalidParameterError("options.sigma","number between 0.3 and 1000",B);if(B=A.sigma,"precision"in A)if(n0.string(g4B[A.precision]))this.options.precision=g4B[A.precision];else throw n0.invalidParameterError("precision","one of: integer, float, approximate",A.precision);if("minAmplitude"in A)if(n0.number(A.minAmplitude)&&n0.inRange(A.minAmplitude,0.001,1))this.options.minAmpl=A.minAmplitude;else throw n0.invalidParameterError("minAmplitude","number between 0.001 and 1",A.minAmplitude)}if(!n0.defined(A))this.options.blurSigma=-1;else if(n0.bool(A))this.options.blurSigma=A?-1:0;else if(n0.number(B)&&n0.inRange(B,0.3,1000))this.options.blurSigma=B;else throw n0.invalidParameterError("sigma","number between 0.3 and 1000",B);return this}function HT6(A){if(this.options.flatten=n0.bool(A)?A:!0,n0.object(A))this._setBackgroundColourOption("flattenBackground",A.background);return this}function zT6(){return this.options.unflatten=!0,this}function ET6(A,B){if(!n0.defined(A))this.options.gamma=2.2;else if(n0.number(A)&&n0.inRange(A,1,3))this.options.gamma=A;else throw n0.invalidParameterError("gamma","number between 1.0 and 3.0",A);if(!n0.defined(B))this.options.gammaOut=this.options.gamma;else if(n0.number(B)&&n0.inRange(B,1,3))this.options.gammaOut=B;else throw n0.invalidParameterError("gammaOut","number between 1.0 and 3.0",B);return this}function UT6(A){if(this.options.negate=n0.bool(A)?A:!0,n0.plainObject(A)&&"alpha"in A)if(!n0.bool(A.alpha))throw n0.invalidParameterError("alpha","should be boolean value",A.alpha);else this.options.negateAlpha=A.alpha;return this}function wT6(A){if(n0.plainObject(A)){if(n0.defined(A.lower))if(n0.number(A.lower)&&n0.inRange(A.lower,0,99))this.options.normaliseLower=A.lower;else throw n0.invalidParameterError("lower","number between 0 and 99",A.lower);if(n0.defined(A.upper))if(n0.number(A.upper)&&n0.inRange(A.upper,1,100))this.options.normaliseUpper=A.upper;else throw n0.invalidParameterError("upper","number between 1 and 100",A.upper)}if(this.options.normaliseLower>=this.options.normaliseUpper)throw n0.invalidParameterError("range","lower to be less than upper",`${this.options.normaliseLower} >= ${this.options.normaliseUpper}`);return this.options.normalise=!0,this}function $T6(A){return this.normalise(A)}function qT6(A){if(n0.plainObject(A)){if(n0.integer(A.width)&&A.width>0)this.options.claheWidth=A.width;else throw n0.invalidParameterError("width","integer greater than zero",A.width);if(n0.integer(A.height)&&A.height>0)this.options.claheHeight=A.height;else throw n0.invalidParameterError("height","integer greater than zero",A.height);if(n0.defined(A.maxSlope))if(n0.integer(A.maxSlope)&&n0.inRange(A.maxSlope,0,100))this.options.claheMaxSlope=A.maxSlope;else throw n0.invalidParameterError("maxSlope","integer between 0 and 100",A.maxSlope)}else throw n0.invalidParameterError("options","plain object",A);return this}function NT6(A){if(!n0.object(A)||!Array.isArray(A.kernel)||!n0.integer(A.width)||!n0.integer(A.height)||!n0.inRange(A.width,3,1001)||!n0.inRange(A.height,3,1001)||A.height*A.width!==A.kernel.length)throw new Error("Invalid convolution kernel");if(!n0.integer(A.scale))A.scale=A.kernel.reduce(function(B,Q){return B+Q},0);if(A.scale<1)A.scale=1;if(!n0.integer(A.offset))A.offset=0;return this.options.convKernel=A,this}function LT6(A,B){if(!n0.defined(A))this.options.threshold=128;else if(n0.bool(A))this.options.threshold=A?128:0;else if(n0.integer(A)&&n0.inRange(A,0,255))this.options.threshold=A;else throw n0.invalidParameterError("threshold","integer between 0 and 255",A);if(!n0.object(B)||B.greyscale===!0||B.grayscale===!0)this.options.thresholdGrayscale=!0;else this.options.thresholdGrayscale=!1;return this}function MT6(A,B,Q){if(this.options.boolean=this._createInputDescriptor(A,Q),n0.string(B)&&n0.inArray(B,["and","or","eor"]))this.options.booleanOp=B;else throw n0.invalidParameterError("operator","one of: and, or, eor",B);return this}function RT6(A,B){if(!n0.defined(A)&&n0.number(B))A=1;else if(n0.number(A)&&!n0.defined(B))B=0;if(!n0.defined(A))this.options.linearA=[];else if(n0.number(A))this.options.linearA=[A];else if(Array.isArray(A)&&A.length&&A.every(n0.number))this.options.linearA=A;else throw n0.invalidParameterError("a","number or array of numbers",A);if(!n0.defined(B))this.options.linearB=[];else if(n0.number(B))this.options.linearB=[B];else if(Array.isArray(B)&&B.length&&B.every(n0.number))this.options.linearB=B;else throw n0.invalidParameterError("b","number or array of numbers",B);if(this.options.linearA.length!==this.options.linearB.length)throw new Error("Expected a and b to be arrays of the same length");return this}function OT6(A){if(!Array.isArray(A))throw n0.invalidParameterError("inputMatrix","array",A);if(A.length!==3&&A.length!==4)throw n0.invalidParameterError("inputMatrix","3x3 or 4x4 array",A.length);let B=A.flat().map(Number);if(B.length!==9&&B.length!==16)throw n0.invalidParameterError("inputMatrix","cardinality of 9 or 16",B.length);return this.options.recombMatrix=B,this}function TT6(A){if(!n0.plainObject(A))throw n0.invalidParameterError("options","plain object",A);if("brightness"in A)if(n0.number(A.brightness)&&A.brightness>=0)this.options.brightness=A.brightness;else throw n0.invalidParameterError("brightness","number above zero",A.brightness);if("saturation"in A)if(n0.number(A.saturation)&&A.saturation>=0)this.options.saturation=A.saturation;else throw n0.invalidParameterError("saturation","number above zero",A.saturation);if("hue"in A)if(n0.integer(A.hue))this.options.hue=A.hue%360;else throw n0.invalidParameterError("hue","number",A.hue);if("lightness"in A)if(n0.number(A.lightness))this.options.lightness=A.lightness;else throw n0.invalidParameterError("lightness","number",A.lightness);return this}u4B.exports=function(A){Object.assign(A.prototype,{rotate:YT6,flip:WT6,flop:JT6,affine:XT6,sharpen:VT6,median:CT6,blur:KT6,flatten:HT6,unflatten:zT6,gamma:ET6,negate:UT6,normalise:wT6,normalize:$T6,clahe:qT6,convolve:NT6,threshold:LT6,boolean:MT6,linear:RT6,recomb:OT6,modulate:TT6})}});
var o9B=E((xp5,r9B)=>{var s9B=J1("fs"),bM6=(A)=>s9B.readFileSync(A,"utf-8"),fM6=(A)=>new Promise((B,Q)=>{s9B.readFile(A,"utf-8",(D,Z)=>{if(D)Q(D);else B(Z)})});r9B.exports={LDD_PATH:"/usr/bin/ldd",readFileSync:bM6,readFile:fM6}});
var pS1=E((sp5,fQB)=>{var LR6=OE(),MR6=(A,B,Q)=>LR6(A,B,Q)<0;fQB.exports=MR6});
var q71=E((fp5,HQB)=>{var aM6=Number.MAX_SAFE_INTEGER||9007199254740991,sM6=["major","premajor","minor","preminor","patch","prepatch","prerelease"];HQB.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:16,MAX_SAFE_BUILD_LENGTH:250,MAX_SAFE_INTEGER:aM6,RELEASE_TYPES:sM6,SEMVER_SPEC_VERSION:"2.0.0",FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}});
var t4B=E((qi5,o4B)=>{var qC0=J1("node:path"),p1=wM(),ct=T71(),n4B=new Map([["heic","heif"],["heif","heif"],["avif","avif"],["jpeg","jpeg"],["jpg","jpeg"],["jpe","jpeg"],["tile","tile"],["dz","tile"],["png","png"],["raw","raw"],["tiff","tiff"],["tif","tiff"],["webp","webp"],["gif","gif"],["jp2","jp2"],["jpx","jp2"],["j2k","jp2"],["j2c","jp2"],["jxl","jxl"]]),cT6=/\.(jp[2x]|j2[kc])$/i,a4B=()=>new Error("JP2 output requires libvips with support for OpenJPEG"),s4B=(A)=>1<<31-Math.clz32(Math.ceil(Math.log2(A)));function lT6(A,B){let Q;if(!p1.string(A))Q=new Error("Missing output file path");else if(p1.string(this.options.input.file)&&qC0.resolve(this.options.input.file)===qC0.resolve(A))Q=new Error("Cannot use same file for input and output");else if(cT6.test(qC0.extname(A))&&!this.constructor.format.jp2k.output.file)Q=a4B();if(Q)if(p1.fn(B))B(Q);else return Promise.reject(Q);else{this.options.fileOut=A;let D=Error();return this._pipeline(B,D)}return this}function pT6(A,B){if(p1.object(A))this._setBooleanOption("resolveWithObject",A.resolveWithObject);else if(this.options.resolveWithObject)this.options.resolveWithObject=!1;this.options.fileOut="";let Q=Error();return this._pipeline(p1.fn(A)?A:B,Q)}function iT6(){return this.options.keepMetadata|=1,this}function nT6(A){if(p1.object(A))for(let[B,Q]of Object.entries(A))if(p1.object(Q))for(let[D,Z]of Object.entries(Q))if(p1.string(Z))this.options.withExif[`exif-${B.toLowerCase()}-${D}`]=Z;else throw p1.invalidParameterError(`${B}.${D}`,"string",Z);else throw p1.invalidParameterError(B,"object",Q);else throw p1.invalidParameterError("exif","object",A);return this.options.withExifMerge=!1,this.keepExif()}function aT6(A){return this.withExif(A),this.options.withExifMerge=!0,this}function sT6(){return this.options.keepMetadata|=8,this}function rT6(A,B){if(p1.string(A))this.options.withIccProfile=A;else throw p1.invalidParameterError("icc","string",A);if(this.keepIccProfile(),p1.object(B)){if(p1.defined(B.attach))if(p1.bool(B.attach)){if(!B.attach)this.options.keepMetadata&=-9}else throw p1.invalidParameterError("attach","boolean",B.attach)}return this}function oT6(){return this.options.keepMetadata=31,this}function tT6(A){if(this.keepMetadata(),this.withIccProfile("srgb"),p1.object(A)){if(p1.defined(A.orientation))if(p1.integer(A.orientation)&&p1.inRange(A.orientation,1,8))this.options.withMetadataOrientation=A.orientation;else throw p1.invalidParameterError("orientation","integer between 1 and 8",A.orientation);if(p1.defined(A.density))if(p1.number(A.density)&&A.density>0)this.options.withMetadataDensity=A.density;else throw p1.invalidParameterError("density","positive number",A.density);if(p1.defined(A.icc))this.withIccProfile(A.icc);if(p1.defined(A.exif))this.withExifMerge(A.exif)}return this}function eT6(A,B){let Q=n4B.get((p1.object(A)&&p1.string(A.id)?A.id:A).toLowerCase());if(!Q)throw p1.invalidParameterError("format",`one of: ${[...n4B.keys()].join(", ")}`,A);return this[Q](B)}function AP6(A){if(p1.object(A)){if(p1.defined(A.quality))if(p1.integer(A.quality)&&p1.inRange(A.quality,1,100))this.options.jpegQuality=A.quality;else throw p1.invalidParameterError("quality","integer between 1 and 100",A.quality);if(p1.defined(A.progressive))this._setBooleanOption("jpegProgressive",A.progressive);if(p1.defined(A.chromaSubsampling))if(p1.string(A.chromaSubsampling)&&p1.inArray(A.chromaSubsampling,["4:2:0","4:4:4"]))this.options.jpegChromaSubsampling=A.chromaSubsampling;else throw p1.invalidParameterError("chromaSubsampling","one of: 4:2:0, 4:4:4",A.chromaSubsampling);let B=p1.bool(A.optimizeCoding)?A.optimizeCoding:A.optimiseCoding;if(p1.defined(B))this._setBooleanOption("jpegOptimiseCoding",B);if(p1.defined(A.mozjpeg))if(p1.bool(A.mozjpeg)){if(A.mozjpeg)this.options.jpegTrellisQuantisation=!0,this.options.jpegOvershootDeringing=!0,this.options.jpegOptimiseScans=!0,this.options.jpegProgressive=!0,this.options.jpegQuantisationTable=3}else throw p1.invalidParameterError("mozjpeg","boolean",A.mozjpeg);let Q=p1.bool(A.trellisQuantization)?A.trellisQuantization:A.trellisQuantisation;if(p1.defined(Q))this._setBooleanOption("jpegTrellisQuantisation",Q);if(p1.defined(A.overshootDeringing))this._setBooleanOption("jpegOvershootDeringing",A.overshootDeringing);let D=p1.bool(A.optimizeScans)?A.optimizeScans:A.optimiseScans;if(p1.defined(D)){if(this._setBooleanOption("jpegOptimiseScans",D),D)this.options.jpegProgressive=!0}let Z=p1.number(A.quantizationTable)?A.quantizationTable:A.quantisationTable;if(p1.defined(Z))if(p1.integer(Z)&&p1.inRange(Z,0,8))this.options.jpegQuantisationTable=Z;else throw p1.invalidParameterError("quantisationTable","integer between 0 and 8",Z)}return this._updateFormatOut("jpeg",A)}function BP6(A){if(p1.object(A)){if(p1.defined(A.progressive))this._setBooleanOption("pngProgressive",A.progressive);if(p1.defined(A.compressionLevel))if(p1.integer(A.compressionLevel)&&p1.inRange(A.compressionLevel,0,9))this.options.pngCompressionLevel=A.compressionLevel;else throw p1.invalidParameterError("compressionLevel","integer between 0 and 9",A.compressionLevel);if(p1.defined(A.adaptiveFiltering))this._setBooleanOption("pngAdaptiveFiltering",A.adaptiveFiltering);let B=A.colours||A.colors;if(p1.defined(B))if(p1.integer(B)&&p1.inRange(B,2,256))this.options.pngBitdepth=s4B(B);else throw p1.invalidParameterError("colours","integer between 2 and 256",B);if(p1.defined(A.palette))this._setBooleanOption("pngPalette",A.palette);else if([A.quality,A.effort,A.colours,A.colors,A.dither].some(p1.defined))this._setBooleanOption("pngPalette",!0);if(this.options.pngPalette){if(p1.defined(A.quality))if(p1.integer(A.quality)&&p1.inRange(A.quality,0,100))this.options.pngQuality=A.quality;else throw p1.invalidParameterError("quality","integer between 0 and 100",A.quality);if(p1.defined(A.effort))if(p1.integer(A.effort)&&p1.inRange(A.effort,1,10))this.options.pngEffort=A.effort;else throw p1.invalidParameterError("effort","integer between 1 and 10",A.effort);if(p1.defined(A.dither))if(p1.number(A.dither)&&p1.inRange(A.dither,0,1))this.options.pngDither=A.dither;else throw p1.invalidParameterError("dither","number between 0.0 and 1.0",A.dither)}}return this._updateFormatOut("png",A)}function QP6(A){if(p1.object(A)){if(p1.defined(A.quality))if(p1.integer(A.quality)&&p1.inRange(A.quality,1,100))this.options.webpQuality=A.quality;else throw p1.invalidParameterError("quality","integer between 1 and 100",A.quality);if(p1.defined(A.alphaQuality))if(p1.integer(A.alphaQuality)&&p1.inRange(A.alphaQuality,0,100))this.options.webpAlphaQuality=A.alphaQuality;else throw p1.invalidParameterError("alphaQuality","integer between 0 and 100",A.alphaQuality);if(p1.defined(A.lossless))this._setBooleanOption("webpLossless",A.lossless);if(p1.defined(A.nearLossless))this._setBooleanOption("webpNearLossless",A.nearLossless);if(p1.defined(A.smartSubsample))this._setBooleanOption("webpSmartSubsample",A.smartSubsample);if(p1.defined(A.preset))if(p1.string(A.preset)&&p1.inArray(A.preset,["default","photo","picture","drawing","icon","text"]))this.options.webpPreset=A.preset;else throw p1.invalidParameterError("preset","one of: default, photo, picture, drawing, icon, text",A.preset);if(p1.defined(A.effort))if(p1.integer(A.effort)&&p1.inRange(A.effort,0,6))this.options.webpEffort=A.effort;else throw p1.invalidParameterError("effort","integer between 0 and 6",A.effort);if(p1.defined(A.minSize))this._setBooleanOption("webpMinSize",A.minSize);if(p1.defined(A.mixed))this._setBooleanOption("webpMixed",A.mixed)}return r4B(A,this.options),this._updateFormatOut("webp",A)}function DP6(A){if(p1.object(A)){if(p1.defined(A.reuse))this._setBooleanOption("gifReuse",A.reuse);if(p1.defined(A.progressive))this._setBooleanOption("gifProgressive",A.progressive);let B=A.colours||A.colors;if(p1.defined(B))if(p1.integer(B)&&p1.inRange(B,2,256))this.options.gifBitdepth=s4B(B);else throw p1.invalidParameterError("colours","integer between 2 and 256",B);if(p1.defined(A.effort))if(p1.number(A.effort)&&p1.inRange(A.effort,1,10))this.options.gifEffort=A.effort;else throw p1.invalidParameterError("effort","integer between 1 and 10",A.effort);if(p1.defined(A.dither))if(p1.number(A.dither)&&p1.inRange(A.dither,0,1))this.options.gifDither=A.dither;else throw p1.invalidParameterError("dither","number between 0.0 and 1.0",A.dither);if(p1.defined(A.interFrameMaxError))if(p1.number(A.interFrameMaxError)&&p1.inRange(A.interFrameMaxError,0,32))this.options.gifInterFrameMaxError=A.interFrameMaxError;else throw p1.invalidParameterError("interFrameMaxError","number between 0.0 and 32.0",A.interFrameMaxError);if(p1.defined(A.interPaletteMaxError))if(p1.number(A.interPaletteMaxError)&&p1.inRange(A.interPaletteMaxError,0,256))this.options.gifInterPaletteMaxError=A.interPaletteMaxError;else throw p1.invalidParameterError("interPaletteMaxError","number between 0.0 and 256.0",A.interPaletteMaxError)}return r4B(A,this.options),this._updateFormatOut("gif",A)}function ZP6(A){if(!this.constructor.format.jp2k.output.buffer)throw a4B();if(p1.object(A)){if(p1.defined(A.quality))if(p1.integer(A.quality)&&p1.inRange(A.quality,1,100))this.options.jp2Quality=A.quality;else throw p1.invalidParameterError("quality","integer between 1 and 100",A.quality);if(p1.defined(A.lossless))if(p1.bool(A.lossless))this.options.jp2Lossless=A.lossless;else throw p1.invalidParameterError("lossless","boolean",A.lossless);if(p1.defined(A.tileWidth))if(p1.integer(A.tileWidth)&&p1.inRange(A.tileWidth,1,32768))this.options.jp2TileWidth=A.tileWidth;else throw p1.invalidParameterError("tileWidth","integer between 1 and 32768",A.tileWidth);if(p1.defined(A.tileHeight))if(p1.integer(A.tileHeight)&&p1.inRange(A.tileHeight,1,32768))this.options.jp2TileHeight=A.tileHeight;else throw p1.invalidParameterError("tileHeight","integer between 1 and 32768",A.tileHeight);if(p1.defined(A.chromaSubsampling))if(p1.string(A.chromaSubsampling)&&p1.inArray(A.chromaSubsampling,["4:2:0","4:4:4"]))this.options.jp2ChromaSubsampling=A.chromaSubsampling;else throw p1.invalidParameterError("chromaSubsampling","one of: 4:2:0, 4:4:4",A.chromaSubsampling)}return this._updateFormatOut("jp2",A)}function r4B(A,B){if(p1.object(A)&&p1.defined(A.loop))if(p1.integer(A.loop)&&p1.inRange(A.loop,0,65535))B.loop=A.loop;else throw p1.invalidParameterError("loop","integer between 0 and 65535",A.loop);if(p1.object(A)&&p1.defined(A.delay))if(p1.integer(A.delay)&&p1.inRange(A.delay,0,65535))B.delay=[A.delay];else if(Array.isArray(A.delay)&&A.delay.every(p1.integer)&&A.delay.every((Q)=>p1.inRange(Q,0,65535)))B.delay=A.delay;else throw p1.invalidParameterError("delay","integer or an array of integers between 0 and 65535",A.delay)}function GP6(A){if(p1.object(A)){if(p1.defined(A.quality))if(p1.integer(A.quality)&&p1.inRange(A.quality,1,100))this.options.tiffQuality=A.quality;else throw p1.invalidParameterError("quality","integer between 1 and 100",A.quality);if(p1.defined(A.bitdepth))if(p1.integer(A.bitdepth)&&p1.inArray(A.bitdepth,[1,2,4,8]))this.options.tiffBitdepth=A.bitdepth;else throw p1.invalidParameterError("bitdepth","1, 2, 4 or 8",A.bitdepth);if(p1.defined(A.tile))this._setBooleanOption("tiffTile",A.tile);if(p1.defined(A.tileWidth))if(p1.integer(A.tileWidth)&&A.tileWidth>0)this.options.tiffTileWidth=A.tileWidth;else throw p1.invalidParameterError("tileWidth","integer greater than zero",A.tileWidth);if(p1.defined(A.tileHeight))if(p1.integer(A.tileHeight)&&A.tileHeight>0)this.options.tiffTileHeight=A.tileHeight;else throw p1.invalidParameterError("tileHeight","integer greater than zero",A.tileHeight);if(p1.defined(A.miniswhite))this._setBooleanOption("tiffMiniswhite",A.miniswhite);if(p1.defined(A.pyramid))this._setBooleanOption("tiffPyramid",A.pyramid);if(p1.defined(A.xres))if(p1.number(A.xres)&&A.xres>0)this.options.tiffXres=A.xres;else throw p1.invalidParameterError("xres","number greater than zero",A.xres);if(p1.defined(A.yres))if(p1.number(A.yres)&&A.yres>0)this.options.tiffYres=A.yres;else throw p1.invalidParameterError("yres","number greater than zero",A.yres);if(p1.defined(A.compression))if(p1.string(A.compression)&&p1.inArray(A.compression,["none","jpeg","deflate","packbits","ccittfax4","lzw","webp","zstd","jp2k"]))this.options.tiffCompression=A.compression;else throw p1.invalidParameterError("compression","one of: none, jpeg, deflate, packbits, ccittfax4, lzw, webp, zstd, jp2k",A.compression);if(p1.defined(A.predictor))if(p1.string(A.predictor)&&p1.inArray(A.predictor,["none","horizontal","float"]))this.options.tiffPredictor=A.predictor;else throw p1.invalidParameterError("predictor","one of: none, horizontal, float",A.predictor);if(p1.defined(A.resolutionUnit))if(p1.string(A.resolutionUnit)&&p1.inArray(A.resolutionUnit,["inch","cm"]))this.options.tiffResolutionUnit=A.resolutionUnit;else throw p1.invalidParameterError("resolutionUnit","one of: inch, cm",A.resolutionUnit)}return this._updateFormatOut("tiff",A)}function FP6(A){return this.heif({...A,compression:"av1"})}function IP6(A){if(p1.object(A)){if(p1.string(A.compression)&&p1.inArray(A.compression,["av1","hevc"]))this.options.heifCompression=A.compression;else throw p1.invalidParameterError("compression","one of: av1, hevc",A.compression);if(p1.defined(A.quality))if(p1.integer(A.quality)&&p1.inRange(A.quality,1,100))this.options.heifQuality=A.quality;else throw p1.invalidParameterError("quality","integer between 1 and 100",A.quality);if(p1.defined(A.lossless))if(p1.bool(A.lossless))this.options.heifLossless=A.lossless;else throw p1.invalidParameterError("lossless","boolean",A.lossless);if(p1.defined(A.effort))if(p1.integer(A.effort)&&p1.inRange(A.effort,0,9))this.options.heifEffort=A.effort;else throw p1.invalidParameterError("effort","integer between 0 and 9",A.effort);if(p1.defined(A.chromaSubsampling))if(p1.string(A.chromaSubsampling)&&p1.inArray(A.chromaSubsampling,["4:2:0","4:4:4"]))this.options.heifChromaSubsampling=A.chromaSubsampling;else throw p1.invalidParameterError("chromaSubsampling","one of: 4:2:0, 4:4:4",A.chromaSubsampling);if(p1.defined(A.bitdepth))if(p1.integer(A.bitdepth)&&p1.inArray(A.bitdepth,[8,10,12])){if(A.bitdepth!==8&&this.constructor.versions.heif)throw p1.invalidParameterError("bitdepth when using prebuilt binaries",8,A.bitdepth);this.options.heifBitdepth=A.bitdepth}else throw p1.invalidParameterError("bitdepth","8, 10 or 12",A.bitdepth)}else throw p1.invalidParameterError("options","Object",A);return this._updateFormatOut("heif",A)}function YP6(A){if(p1.object(A)){if(p1.defined(A.quality))if(p1.integer(A.quality)&&p1.inRange(A.quality,1,100))this.options.jxlDistance=A.quality>=30?0.1+(100-A.quality)*0.09:0.017666666666666667*A.quality*A.quality-1.15*A.quality+25;else throw p1.invalidParameterError("quality","integer between 1 and 100",A.quality);else if(p1.defined(A.distance))if(p1.number(A.distance)&&p1.inRange(A.distance,0,15))this.options.jxlDistance=A.distance;else throw p1.invalidParameterError("distance","number between 0.0 and 15.0",A.distance);if(p1.defined(A.decodingTier))if(p1.integer(A.decodingTier)&&p1.inRange(A.decodingTier,0,4))this.options.jxlDecodingTier=A.decodingTier;else throw p1.invalidParameterError("decodingTier","integer between 0 and 4",A.decodingTier);if(p1.defined(A.lossless))if(p1.bool(A.lossless))this.options.jxlLossless=A.lossless;else throw p1.invalidParameterError("lossless","boolean",A.lossless);if(p1.defined(A.effort))if(p1.integer(A.effort)&&p1.inRange(A.effort,3,9))this.options.jxlEffort=A.effort;else throw p1.invalidParameterError("effort","integer between 3 and 9",A.effort)}return this._updateFormatOut("jxl",A)}function WP6(A){if(p1.object(A)){if(p1.defined(A.depth))if(p1.string(A.depth)&&p1.inArray(A.depth,["char","uchar","short","ushort","int","uint","float","complex","double","dpcomplex"]))this.options.rawDepth=A.depth;else throw p1.invalidParameterError("depth","one of: char, uchar, short, ushort, int, uint, float, complex, double, dpcomplex",A.depth)}return this._updateFormatOut("raw")}function JP6(A){if(p1.object(A)){if(p1.defined(A.size))if(p1.integer(A.size)&&p1.inRange(A.size,1,8192))this.options.tileSize=A.size;else throw p1.invalidParameterError("size","integer between 1 and 8192",A.size);if(p1.defined(A.overlap))if(p1.integer(A.overlap)&&p1.inRange(A.overlap,0,8192)){if(A.overlap>this.options.tileSize)throw p1.invalidParameterError("overlap",`<= size (${this.options.tileSize})`,A.overlap);this.options.tileOverlap=A.overlap}else throw p1.invalidParameterError("overlap","integer between 0 and 8192",A.overlap);if(p1.defined(A.container))if(p1.string(A.container)&&p1.inArray(A.container,["fs","zip"]))this.options.tileContainer=A.container;else throw p1.invalidParameterError("container","one of: fs, zip",A.container);if(p1.defined(A.layout))if(p1.string(A.layout)&&p1.inArray(A.layout,["dz","google","iiif","iiif3","zoomify"]))this.options.tileLayout=A.layout;else throw p1.invalidParameterError("layout","one of: dz, google, iiif, iiif3, zoomify",A.layout);if(p1.defined(A.angle))if(p1.integer(A.angle)&&!(A.angle%90))this.options.tileAngle=A.angle;else throw p1.invalidParameterError("angle","positive/negative multiple of 90",A.angle);if(this._setBackgroundColourOption("tileBackground",A.background),p1.defined(A.depth))if(p1.string(A.depth)&&p1.inArray(A.depth,["onepixel","onetile","one"]))this.options.tileDepth=A.depth;else throw p1.invalidParameterError("depth","one of: onepixel, onetile, one",A.depth);if(p1.defined(A.skipBlanks))if(p1.integer(A.skipBlanks)&&p1.inRange(A.skipBlanks,-1,65535))this.options.tileSkipBlanks=A.skipBlanks;else throw p1.invalidParameterError("skipBlanks","integer between -1 and 255/65535",A.skipBlanks);else if(p1.defined(A.layout)&&A.layout==="google")this.options.tileSkipBlanks=5;let B=p1.bool(A.center)?A.center:A.centre;if(p1.defined(B))this._setBooleanOption("tileCentre",B);if(p1.defined(A.id))if(p1.string(A.id))this.options.tileId=A.id;else throw p1.invalidParameterError("id","string",A.id);if(p1.defined(A.basename))if(p1.string(A.basename))this.options.tileBasename=A.basename;else throw p1.invalidParameterError("basename","string",A.basename)}if(p1.inArray(this.options.formatOut,["jpeg","png","webp"]))this.options.tileFormat=this.options.formatOut;else if(this.options.formatOut!=="input")throw p1.invalidParameterError("format","one of: jpeg, png, webp",this.options.formatOut);return this._updateFormatOut("dz")}function XP6(A){if(!p1.plainObject(A))throw p1.invalidParameterError("options","object",A);if(p1.integer(A.seconds)&&p1.inRange(A.seconds,0,3600))this.options.timeoutSeconds=A.seconds;else throw p1.invalidParameterError("seconds","integer between 0 and 3600",A.seconds);return this}function VP6(A,B){if(!(p1.object(B)&&B.force===!1))this.options.formatOut=A;return this}function CP6(A,B){if(p1.bool(B))this.options[A]=B;else throw p1.invalidParameterError(A,"boolean",B)}function KP6(){if(!this.options.streamOut){this.options.streamOut=!0;let A=Error();this._pipeline(void 0,A)}}function HP6(A,B){if(typeof A==="function"){if(this._isStreamInput())this.on("finish",()=>{this._flattenBufferIn(),ct.pipeline(this.options,(Q,D,Z)=>{if(Q)A(p1.nativeError(Q,B));else A(null,D,Z)})});else ct.pipeline(this.options,(Q,D,Z)=>{if(Q)A(p1.nativeError(Q,B));else A(null,D,Z)});return this}else if(this.options.streamOut){if(this._isStreamInput()){if(this.once("finish",()=>{this._flattenBufferIn(),ct.pipeline(this.options,(Q,D,Z)=>{if(Q)this.emit("error",p1.nativeError(Q,B));else this.emit("info",Z),this.push(D);this.push(null),this.on("end",()=>this.emit("close"))})}),this.streamInFinished)this.emit("finish")}else ct.pipeline(this.options,(Q,D,Z)=>{if(Q)this.emit("error",p1.nativeError(Q,B));else this.emit("info",Z),this.push(D);this.push(null),this.on("end",()=>this.emit("close"))});return this}else if(this._isStreamInput())return new Promise((Q,D)=>{this.once("finish",()=>{this._flattenBufferIn(),ct.pipeline(this.options,(Z,G,F)=>{if(Z)D(p1.nativeError(Z,B));else if(this.options.resolveWithObject)Q({data:G,info:F});else Q(G)})})});else return new Promise((Q,D)=>{ct.pipeline(this.options,(Z,G,F)=>{if(Z)D(p1.nativeError(Z,B));else if(this.options.resolveWithObject)Q({data:G,info:F});else Q(G)})})}o4B.exports=function(A){Object.assign(A.prototype,{toFile:lT6,toBuffer:pT6,keepExif:iT6,withExif:nT6,withExifMerge:aT6,keepIccProfile:sT6,withIccProfile:rT6,keepMetadata:oT6,withMetadata:tT6,toFormat:eT6,jpeg:AP6,jp2:ZP6,png:BP6,webp:QP6,tiff:GP6,avif:FP6,heif:IP6,jxl:YP6,gif:DP6,raw:WP6,tile:JP6,timeout:XP6,_updateFormatOut:VP6,_setBooleanOption:CP6,_read:KP6,_pipeline:HP6})}});
var wM=E((kp5,p9B)=>{var l9B=function(A){return typeof A!=="undefined"&&A!==null},qM6=function(A){return typeof A==="object"},NM6=function(A){return Object.prototype.toString.call(A)==="[object Object]"},LM6=function(A){return typeof A==="function"},MM6=function(A){return typeof A==="boolean"},RM6=function(A){return A instanceof Buffer},OM6=function(A){if(l9B(A))switch(A.constructor){case Uint8Array:case Uint8ClampedArray:case Int8Array:case Uint16Array:case Int16Array:case Uint32Array:case Int32Array:case Float32Array:case Float64Array:return!0}return!1},TM6=function(A){return A instanceof ArrayBuffer},PM6=function(A){return typeof A==="string"&&A.length>0},SM6=function(A){return typeof A==="number"&&!Number.isNaN(A)},jM6=function(A){return Number.isInteger(A)},yM6=function(A,B,Q){return A>=B&&A<=Q},kM6=function(A,B){return B.includes(A)},_M6=function(A,B,Q){return new Error(`Expected ${B} for ${A} but received ${Q} of type ${typeof Q}`)},xM6=function(A,B){return B.message=A.message,B};p9B.exports={defined:l9B,object:qM6,plainObject:NM6,fn:LM6,bool:MM6,buffer:RM6,typedArray:OM6,arrayBuffer:TM6,string:PM6,number:SM6,integer:jM6,inRange:yM6,inArray:kM6,invalidParameterError:_M6,nativeError:xM6}});
var z4B=E((Wi5,H4B)=>{var yO6=C4B(),kO6=Array.prototype.concat,_O6=Array.prototype.slice,K4B=H4B.exports=function A(B){var Q=[];for(var D=0,Z=B.length;D<Z;D++){var G=B[D];if(yO6(G))Q=kO6.call(Q,_O6.call(G));else Q.push(G)}return Q};K4B.wrap=function(A){return function(){return A(K4B(arguments))}}});

// Export all variables
module.exports = {
  $4B,
  $71,
  AC0,
  Aj1,
  BC0,
  C4B,
  CC0,
  DC0,
  Dj1,
  Dm,
  HC0,
  IC0,
  J4B,
  L71,
  M4B,
  N71,
  OE,
  Q6B,
  QC0,
  R71,
  T71,
  TE,
  VC0,
  WC0,
  YJ,
  _QB,
  a9B,
  b4B,
  eV0,
  ft,
  gS1,
  gt,
  h4B,
  hS1,
  i4B,
  iS1,
  j4B,
  l4B,
  m4B,
  o9B,
  pS1,
  q71,
  t4B,
  wM,
  z4B
};
