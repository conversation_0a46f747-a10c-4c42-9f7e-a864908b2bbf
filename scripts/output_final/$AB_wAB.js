// Package extracted with entry point: $AB
// Contains 2 variables: $AB, wAB

var $AB=E((IV0)=>{Object.defineProperty(IV0,"__esModule",{value:!0});IV0.OTLPLogExporter=void 0;var YN6=wAB();Object.defineProperty(IV0,"OTLPLogExporter",{enumerable:!0,get:function(){return YN6.OTLPLogExporter}})});
var wAB=E((EAB)=>{Object.defineProperty(EAB,"__esModule",{value:!0});EAB.OTLPLogExporter=void 0;var HAB=aX0(),FN6=fu(),IN6=xu();class zAB extends IN6.OTLPExporterBase{constructor(A={}){super(HAB.createOtlpGrpcExportDelegate(HAB.convertLegacyOtlpGrpcOptions(A,"LOGS"),FN6.ProtobufLogsSerializer,"LogsExportService","/opentelemetry.proto.collector.logs.v1.LogsService/Export"))}}EAB.OTLPLogExporter=zAB});

// Export all variables
module.exports = {
  $AB,
  wAB
};
