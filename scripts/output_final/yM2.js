// Package extracted with entry point: yM2
// Contains 1 variables: yM2

var yM2=E((QP5,jM2)=>{var xs4=function A(B){return vs4(B)&&!bs4(B)};function vs4(A){return!!A&&typeof A==="object"}function bs4(A){var B=Object.prototype.toString.call(A);return B==="[object RegExp]"||B==="[object Date]"||gs4(A)}var fs4=typeof Symbol==="function"&&Symbol.for,hs4=fs4?Symbol.for("react.element"):60103;function gs4(A){return A.$$typeof===hs4}function us4(A){return Array.isArray(A)?[]:{}}function v51(A,B){return B.clone!==!1&&B.isMergeableObject(A)?Uo(us4(A),A,B):A}function ms4(A,B,Q){return A.concat(B).map(function(D){return v51(D,Q)})}function ds4(A,B){if(!B.customMerge)return Uo;var Q=B.customMerge(A);return typeof Q==="function"?Q:Uo}function cs4(A){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(A).filter(function(B){return Object.propertyIsEnumerable.call(A,B)}):[]}function PM2(A){return Object.keys(A).concat(cs4(A))}function SM2(A,B){try{return B in A}catch(Q){return!1}}function ls4(A,B){return SM2(A,B)&&!(Object.hasOwnProperty.call(A,B)&&Object.propertyIsEnumerable.call(A,B))}function ps4(A,B,Q){var D={};if(Q.isMergeableObject(A))PM2(A).forEach(function(Z){D[Z]=v51(A[Z],Q)});return PM2(B).forEach(function(Z){if(ls4(A,Z))return;if(SM2(A,Z)&&Q.isMergeableObject(B[Z]))D[Z]=ds4(Z,Q)(A[Z],B[Z],Q);else D[Z]=v51(B[Z],Q)}),D}function Uo(A,B,Q){Q=Q||{},Q.arrayMerge=Q.arrayMerge||ms4,Q.isMergeableObject=Q.isMergeableObject||xs4,Q.cloneUnlessOtherwiseSpecified=v51;var D=Array.isArray(B),Z=Array.isArray(A),G=D===Z;if(!G)return v51(B,Q);else if(D)return Q.arrayMerge(A,B,Q);else return ps4(A,B,Q)}Uo.all=function A(B,Q){if(!Array.isArray(B))throw new Error("first argument should be an array");return B.reduce(function(D,Z){return Uo(D,Z,Q)},{})};var is4=Uo;jM2.exports=is4});

// Export all variables
module.exports = {
  yM2
};
