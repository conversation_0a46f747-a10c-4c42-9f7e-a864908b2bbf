// Package extracted with entry point: r1B
// Contains 107 variables: $I, $X0, A71, AX0, BM, Cr2, D1B, DP1, EE, EW0... (and 97 more)

var $I=E((qu5,Gr2)=>{var JG=Gr2.exports=eL(),Zr2=rY0(),wJ0,$J0;JG.codegen=Ls2();JG.fetch=Rs2();JG.path=Ps2();JG.fs=JG.inquire("fs");JG.toArray=function A(B){if(B){var Q=Object.keys(B),D=new Array(Q.length),Z=0;while(Z<Q.length)D[Z]=B[Q[Z++]];return D}return[]};JG.toObject=function A(B){var Q={},D=0;while(D<B.length){var Z=B[D++],G=B[D++];if(G!==void 0)Q[Z]=G}return Q};var BK6=/\\/g,QK6=/"/g;JG.isReserved=function A(B){return/^(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$/.test(B)};JG.safeProp=function A(B){if(!/^[$\w_]+$/.test(B)||JG.isReserved(B))return'["'+B.replace(BK6,"\\\\").replace(QK6,"\\\"")+'"]';return"."+B};JG.ucFirst=function A(B){return B.charAt(0).toUpperCase()+B.substring(1)};var DK6=/_([a-z])/g;JG.camelCase=function A(B){return B.substring(0,1)+B.substring(1).replace(DK6,function(Q,D){return D.toUpperCase()})};JG.compareFieldsById=function A(B,Q){return B.id-Q.id};JG.decorateType=function A(B,Q){if(B.$type){if(Q&&B.$type.name!==Q)JG.decorateRoot.remove(B.$type),B.$type.name=Q,JG.decorateRoot.add(B.$type);return B.$type}if(!wJ0)wJ0=YP1();var D=new wJ0(Q||B.name);return JG.decorateRoot.add(D),D.ctor=B,Object.defineProperty(B,"$type",{value:D,enumerable:!1}),Object.defineProperty(B.prototype,"$type",{value:D,enumerable:!1}),D};var ZK6=0;JG.decorateEnum=function A(B){if(B.$type)return B.$type;if(!$J0)$J0=Z$();var Q=new $J0("Enum"+ZK6++,B);return JG.decorateRoot.add(Q),Object.defineProperty(B,"$type",{value:Q,enumerable:!1}),Q};JG.setProperty=function A(B,Q,D){function Z(G,F,I){var Y=F.shift();if(Y==="__proto__"||Y==="prototype")return G;if(F.length>0)G[Y]=Z(G[Y]||{},F,I);else{var W=G[Y];if(W)I=[].concat(W).concat(I);G[Y]=I}return G}if(typeof B!=="object")throw TypeError("dst must be an object");if(!Q)throw TypeError("path must be specified");return Q=Q.split("."),Z(B,Q,D)};Object.defineProperty(JG,"decorateRoot",{get:function(){return Zr2.decorated||(Zr2.decorated=new(XP1()))}})});
var $X0=E((Je2)=>{Object.defineProperty(Je2,"__esModule",{value:!0});Je2.BaseServerInterceptingCall=Je2.ServerInterceptingCall=Je2.ResponderBuilder=Je2.ServerListenerBuilder=void 0;Je2.isInterceptingServerListener=WU6;Je2.getServerInterceptingCall=KU6;var EX0=GJ(),uV=_6(),Ct=J1("http2"),tt2=kT1(),et2=J1("zlib"),YU6=pJ0(),De2=F7(),Ze2="server_call";function au(A){De2.trace(uV.LogVerbosity.DEBUG,Ze2,A)}class Ge2{constructor(){this.metadata=void 0,this.message=void 0,this.halfClose=void 0,this.cancel=void 0}withOnReceiveMetadata(A){return this.metadata=A,this}withOnReceiveMessage(A){return this.message=A,this}withOnReceiveHalfClose(A){return this.halfClose=A,this}withOnCancel(A){return this.cancel=A,this}build(){return{onReceiveMetadata:this.metadata,onReceiveMessage:this.message,onReceiveHalfClose:this.halfClose,onCancel:this.cancel}}}Je2.ServerListenerBuilder=Ge2;function WU6(A){return A.onReceiveMetadata!==void 0&&A.onReceiveMetadata.length===1}class Fe2{constructor(A,B){this.listener=A,this.nextListener=B,this.cancelled=!1,this.processingMetadata=!1,this.hasPendingMessage=!1,this.pendingMessage=null,this.processingMessage=!1,this.hasPendingHalfClose=!1}processPendingMessage(){if(this.hasPendingMessage)this.nextListener.onReceiveMessage(this.pendingMessage),this.pendingMessage=null,this.hasPendingMessage=!1}processPendingHalfClose(){if(this.hasPendingHalfClose)this.nextListener.onReceiveHalfClose(),this.hasPendingHalfClose=!1}onReceiveMetadata(A){if(this.cancelled)return;this.processingMetadata=!0,this.listener.onReceiveMetadata(A,(B)=>{if(this.processingMetadata=!1,this.cancelled)return;this.nextListener.onReceiveMetadata(B),this.processPendingMessage(),this.processPendingHalfClose()})}onReceiveMessage(A){if(this.cancelled)return;this.processingMessage=!0,this.listener.onReceiveMessage(A,(B)=>{if(this.processingMessage=!1,this.cancelled)return;if(this.processingMetadata)this.pendingMessage=B,this.hasPendingMessage=!0;else this.nextListener.onReceiveMessage(B),this.processPendingHalfClose()})}onReceiveHalfClose(){if(this.cancelled)return;this.listener.onReceiveHalfClose(()=>{if(this.cancelled)return;if(this.processingMetadata||this.processingMessage)this.hasPendingHalfClose=!0;else this.nextListener.onReceiveHalfClose()})}onCancel(){this.cancelled=!0,this.listener.onCancel(),this.nextListener.onCancel()}}class Ie2{constructor(){this.start=void 0,this.metadata=void 0,this.message=void 0,this.status=void 0}withStart(A){return this.start=A,this}withSendMetadata(A){return this.metadata=A,this}withSendMessage(A){return this.message=A,this}withSendStatus(A){return this.status=A,this}build(){return{start:this.start,sendMetadata:this.metadata,sendMessage:this.message,sendStatus:this.status}}}Je2.ResponderBuilder=Ie2;var gP1={onReceiveMetadata:(A,B)=>{B(A)},onReceiveMessage:(A,B)=>{B(A)},onReceiveHalfClose:(A)=>{A()},onCancel:()=>{}},uP1={start:(A)=>{A()},sendMetadata:(A,B)=>{B(A)},sendMessage:(A,B)=>{B(A)},sendStatus:(A,B)=>{B(A)}};class Ye2{constructor(A,B){var Q,D,Z,G;this.nextCall=A,this.processingMetadata=!1,this.sentMetadata=!1,this.processingMessage=!1,this.pendingMessage=null,this.pendingMessageCallback=null,this.pendingStatus=null,this.responder={start:(Q=B===null||B===void 0?void 0:B.start)!==null&&Q!==void 0?Q:uP1.start,sendMetadata:(D=B===null||B===void 0?void 0:B.sendMetadata)!==null&&D!==void 0?D:uP1.sendMetadata,sendMessage:(Z=B===null||B===void 0?void 0:B.sendMessage)!==null&&Z!==void 0?Z:uP1.sendMessage,sendStatus:(G=B===null||B===void 0?void 0:B.sendStatus)!==null&&G!==void 0?G:uP1.sendStatus}}processPendingMessage(){if(this.pendingMessageCallback)this.nextCall.sendMessage(this.pendingMessage,this.pendingMessageCallback),this.pendingMessage=null,this.pendingMessageCallback=null}processPendingStatus(){if(this.pendingStatus)this.nextCall.sendStatus(this.pendingStatus),this.pendingStatus=null}start(A){this.responder.start((B)=>{var Q,D,Z,G;let F={onReceiveMetadata:(Q=B===null||B===void 0?void 0:B.onReceiveMetadata)!==null&&Q!==void 0?Q:gP1.onReceiveMetadata,onReceiveMessage:(D=B===null||B===void 0?void 0:B.onReceiveMessage)!==null&&D!==void 0?D:gP1.onReceiveMessage,onReceiveHalfClose:(Z=B===null||B===void 0?void 0:B.onReceiveHalfClose)!==null&&Z!==void 0?Z:gP1.onReceiveHalfClose,onCancel:(G=B===null||B===void 0?void 0:B.onCancel)!==null&&G!==void 0?G:gP1.onCancel},I=new Fe2(F,A);this.nextCall.start(I)})}sendMetadata(A){this.processingMetadata=!0,this.sentMetadata=!0,this.responder.sendMetadata(A,(B)=>{this.processingMetadata=!1,this.nextCall.sendMetadata(B),this.processPendingMessage(),this.processPendingStatus()})}sendMessage(A,B){if(this.processingMessage=!0,!this.sentMetadata)this.sendMetadata(new EX0.Metadata);this.responder.sendMessage(A,(Q)=>{if(this.processingMessage=!1,this.processingMetadata)this.pendingMessage=Q,this.pendingMessageCallback=B;else this.nextCall.sendMessage(Q,B)})}sendStatus(A){this.responder.sendStatus(A,(B)=>{if(this.processingMetadata||this.processingMessage)this.pendingStatus=B;else this.nextCall.sendStatus(B)})}startRead(){this.nextCall.startRead()}getPeer(){return this.nextCall.getPeer()}getDeadline(){return this.nextCall.getDeadline()}getHost(){return this.nextCall.getHost()}}Je2.ServerInterceptingCall=Ye2;var We2="grpc-accept-encoding",UX0="grpc-encoding",Ae2="grpc-message",Be2="grpc-status",zX0="grpc-timeout",JU6=/(\d{1,8})\s*([HMSmun])/,XU6={H:3600000,M:60000,S:1000,m:1,u:0.001,n:0.000001},VU6={[We2]:"identity,deflate,gzip",[UX0]:"identity"},Qe2={[Ct.constants.HTTP2_HEADER_STATUS]:Ct.constants.HTTP_STATUS_OK,[Ct.constants.HTTP2_HEADER_CONTENT_TYPE]:"application/grpc+proto"},CU6={waitForTrailers:!0};class wX0{constructor(A,B,Q,D,Z){var G;if(this.stream=A,this.callEventTracker=Q,this.handler=D,this.listener=null,this.deadlineTimer=null,this.deadline=1/0,this.maxSendMessageSize=uV.DEFAULT_MAX_SEND_MESSAGE_LENGTH,this.maxReceiveMessageSize=uV.DEFAULT_MAX_RECEIVE_MESSAGE_LENGTH,this.cancelled=!1,this.metadataSent=!1,this.wantTrailers=!1,this.cancelNotified=!1,this.incomingEncoding="identity",this.readQueue=[],this.isReadPending=!1,this.receivedHalfClose=!1,this.streamEnded=!1,this.stream.once("error",(W)=>{}),this.stream.once("close",()=>{var W;if(au("Request to method "+((W=this.handler)===null||W===void 0?void 0:W.path)+" stream closed with rstCode "+this.stream.rstCode),this.callEventTracker&&!this.streamEnded)this.streamEnded=!0,this.callEventTracker.onStreamEnd(!1),this.callEventTracker.onCallEnd({code:uV.Status.CANCELLED,details:"Stream closed before sending status",metadata:null});this.notifyOnCancel()}),this.stream.on("data",(W)=>{this.handleDataFrame(W)}),this.stream.pause(),this.stream.on("end",()=>{this.handleEndEvent()}),"grpc.max_send_message_length"in Z)this.maxSendMessageSize=Z["grpc.max_send_message_length"];if("grpc.max_receive_message_length"in Z)this.maxReceiveMessageSize=Z["grpc.max_receive_message_length"];this.host=(G=B[":authority"])!==null&&G!==void 0?G:B.host,this.decoder=new YU6.StreamDecoder(this.maxReceiveMessageSize);let F=EX0.Metadata.fromHttp2Headers(B);if(De2.isTracerEnabled(Ze2))au("Request to "+this.handler.path+" received headers "+JSON.stringify(F.toJSON()));let I=F.get(zX0);if(I.length>0)this.handleTimeoutHeader(I[0]);let Y=F.get(UX0);if(Y.length>0)this.incomingEncoding=Y[0];F.remove(zX0),F.remove(UX0),F.remove(We2),F.remove(Ct.constants.HTTP2_HEADER_ACCEPT_ENCODING),F.remove(Ct.constants.HTTP2_HEADER_TE),F.remove(Ct.constants.HTTP2_HEADER_CONTENT_TYPE),this.metadata=F}handleTimeoutHeader(A){let B=A.toString().match(JU6);if(B===null){let Z={code:uV.Status.INTERNAL,details:`Invalid ${zX0} value "${A}"`,metadata:null};process.nextTick(()=>{this.sendStatus(Z)});return}let Q=+B[1]*XU6[B[2]]|0,D=new Date;this.deadline=D.setMilliseconds(D.getMilliseconds()+Q),this.deadlineTimer=setTimeout(()=>{let Z={code:uV.Status.DEADLINE_EXCEEDED,details:"Deadline exceeded",metadata:null};this.sendStatus(Z)},Q)}checkCancelled(){if(!this.cancelled&&(this.stream.destroyed||this.stream.closed))this.notifyOnCancel(),this.cancelled=!0;return this.cancelled}notifyOnCancel(){if(this.cancelNotified)return;if(this.cancelNotified=!0,this.cancelled=!0,process.nextTick(()=>{var A;(A=this.listener)===null||A===void 0||A.onCancel()}),this.deadlineTimer)clearTimeout(this.deadlineTimer);this.stream.resume()}maybeSendMetadata(){if(!this.metadataSent)this.sendMetadata(new EX0.Metadata)}serializeMessage(A){let B=this.handler.serialize(A),Q=B.byteLength,D=Buffer.allocUnsafe(Q+5);return D.writeUInt8(0,0),D.writeUInt32BE(Q,1),B.copy(D,5),D}decompressMessage(A,B){let Q=A.subarray(5);if(B==="identity")return Q;else if(B==="deflate"||B==="gzip"){let D;if(B==="deflate")D=et2.createInflate();else D=et2.createGunzip();return new Promise((Z,G)=>{let F=0,I=[];D.on("data",(Y)=>{if(I.push(Y),F+=Y.byteLength,this.maxReceiveMessageSize!==-1&&F>this.maxReceiveMessageSize)D.destroy(),G({code:uV.Status.RESOURCE_EXHAUSTED,details:`Received message that decompresses to a size larger than ${this.maxReceiveMessageSize}`})}),D.on("end",()=>{Z(Buffer.concat(I))}),D.write(Q),D.end()})}else return Promise.reject({code:uV.Status.UNIMPLEMENTED,details:`Received message compressed with unsupported encoding "${B}"`})}async decompressAndMaybePush(A){if(A.type!=="COMPRESSED")throw new Error(`Invalid queue entry type: ${A.type}`);let Q=A.compressedMessage.readUInt8(0)===1?this.incomingEncoding:"identity",D;try{D=await this.decompressMessage(A.compressedMessage,Q)}catch(Z){this.sendStatus(Z);return}try{A.parsedMessage=this.handler.deserialize(D)}catch(Z){this.sendStatus({code:uV.Status.INTERNAL,details:`Error deserializing request: ${Z.message}`});return}A.type="READABLE",this.maybePushNextMessage()}maybePushNextMessage(){if(this.listener&&this.isReadPending&&this.readQueue.length>0&&this.readQueue[0].type!=="COMPRESSED"){this.isReadPending=!1;let A=this.readQueue.shift();if(A.type==="READABLE")this.listener.onReceiveMessage(A.parsedMessage);else this.listener.onReceiveHalfClose()}}handleDataFrame(A){var B;if(this.checkCancelled())return;au("Request to "+this.handler.path+" received data frame of size "+A.length);let Q;try{Q=this.decoder.write(A)}catch(D){this.sendStatus({code:uV.Status.RESOURCE_EXHAUSTED,details:D.message});return}for(let D of Q){this.stream.pause();let Z={type:"COMPRESSED",compressedMessage:D,parsedMessage:null};this.readQueue.push(Z),this.decompressAndMaybePush(Z),(B=this.callEventTracker)===null||B===void 0||B.addMessageReceived()}}handleEndEvent(){this.readQueue.push({type:"HALF_CLOSE",compressedMessage:null,parsedMessage:null}),this.receivedHalfClose=!0,this.maybePushNextMessage()}start(A){if(au("Request to "+this.handler.path+" start called"),this.checkCancelled())return;this.listener=A,A.onReceiveMetadata(this.metadata)}sendMetadata(A){if(this.checkCancelled())return;if(this.metadataSent)return;this.metadataSent=!0;let B=A?A.toHttp2Headers():null,Q=Object.assign(Object.assign(Object.assign({},Qe2),VU6),B);this.stream.respond(Q,CU6)}sendMessage(A,B){if(this.checkCancelled())return;let Q;try{Q=this.serializeMessage(A)}catch(D){this.sendStatus({code:uV.Status.INTERNAL,details:`Error serializing response: ${tt2.getErrorMessage(D)}`,metadata:null});return}if(this.maxSendMessageSize!==-1&&Q.length-5>this.maxSendMessageSize){this.sendStatus({code:uV.Status.RESOURCE_EXHAUSTED,details:`Sent message larger than max (${Q.length} vs. ${this.maxSendMessageSize})`,metadata:null});return}this.maybeSendMetadata(),au("Request to "+this.handler.path+" sent data frame of size "+Q.length),this.stream.write(Q,(D)=>{var Z;if(D){this.sendStatus({code:uV.Status.INTERNAL,details:`Error writing message: ${tt2.getErrorMessage(D)}`,metadata:null});return}(Z=this.callEventTracker)===null||Z===void 0||Z.addMessageSent(),B()})}sendStatus(A){var B,Q;if(this.checkCancelled())return;if(au("Request to method "+((B=this.handler)===null||B===void 0?void 0:B.path)+" ended with status code: "+uV.Status[A.code]+" details: "+A.details),this.metadataSent)if(!this.wantTrailers)this.wantTrailers=!0,this.stream.once("wantTrailers",()=>{var D;if(this.callEventTracker&&!this.streamEnded)this.streamEnded=!0,this.callEventTracker.onStreamEnd(!0),this.callEventTracker.onCallEnd(A);let Z=Object.assign({[Be2]:A.code,[Ae2]:encodeURI(A.details)},(D=A.metadata)===null||D===void 0?void 0:D.toHttp2Headers());this.stream.sendTrailers(Z),this.notifyOnCancel()}),this.stream.end();else this.notifyOnCancel();else{if(this.callEventTracker&&!this.streamEnded)this.streamEnded=!0,this.callEventTracker.onStreamEnd(!0),this.callEventTracker.onCallEnd(A);let D=Object.assign(Object.assign({[Be2]:A.code,[Ae2]:encodeURI(A.details)},Qe2),(Q=A.metadata)===null||Q===void 0?void 0:Q.toHttp2Headers());this.stream.respond(D,{endStream:!0}),this.notifyOnCancel()}}startRead(){if(au("Request to "+this.handler.path+" startRead called"),this.checkCancelled())return;if(this.isReadPending=!0,this.readQueue.length===0){if(!this.receivedHalfClose)this.stream.resume()}else this.maybePushNextMessage()}getPeer(){var A;let B=(A=this.stream.session)===null||A===void 0?void 0:A.socket;if(B===null||B===void 0?void 0:B.remoteAddress)if(B.remotePort)return`${B.remoteAddress}:${B.remotePort}`;else return B.remoteAddress;else return"unknown"}getDeadline(){return this.deadline}getHost(){return this.host}}Je2.BaseServerInterceptingCall=wX0;function KU6(A,B,Q,D,Z,G){let F={path:Z.path,requestStream:Z.type==="clientStream"||Z.type==="bidi",responseStream:Z.type==="serverStream"||Z.type==="bidi",requestDeserialize:Z.deserialize,responseSerialize:Z.serialize},I=new wX0(B,Q,D,Z,G);return A.reduce((Y,W)=>{return W(F,Y)},I)}});
var A71=E((w1B)=>{Object.defineProperty(w1B,"__esModule",{value:!0});w1B.createOtlpGrpcExporterTransport=w1B.GrpcExporterTransport=w1B.createEmptyMetadata=w1B.createSslCredentials=w1B.createInsecureCredentials=void 0;var q$6=0,N$6=2;function L$6(A){return A==="gzip"?N$6:q$6}function M$6(){let{credentials:A}=e31();return A.createInsecure()}w1B.createInsecureCredentials=M$6;function R$6(A,B,Q){let{credentials:D}=e31();return D.createSsl(A,B,Q)}w1B.createSslCredentials=R$6;function O$6(){let{Metadata:A}=e31();return new A}w1B.createEmptyMetadata=O$6;class pX0{_parameters;_client;_metadata;constructor(A){this._parameters=A}shutdown(){this._client?.close()}send(A,B){let Q=Buffer.from(A);if(this._client==null){let{createServiceClientConstructor:D}=U1B();try{this._metadata=this._parameters.metadata()}catch(G){return Promise.resolve({status:"failure",error:G})}let Z=D(this._parameters.grpcPath,this._parameters.grpcName);try{this._client=new Z(this._parameters.address,this._parameters.credentials(),{"grpc.default_compression_algorithm":L$6(this._parameters.compression)})}catch(G){return Promise.resolve({status:"failure",error:G})}}return new Promise((D)=>{let Z=Date.now()+B;if(this._metadata==null)return D({error:new Error("metadata was null"),status:"failure"});this._client.export(Q,this._metadata,{deadline:Z},(G,F)=>{if(G)D({status:"failure",error:G});else D({data:F,status:"success"})})})}}w1B.GrpcExporterTransport=pX0;function T$6(A){return new pX0(A)}w1B.createOtlpGrpcExporterTransport=T$6});
var AX0=E((to2)=>{Object.defineProperty(to2,"__esModule",{value:!0});to2.BaseFilter=void 0;class oo2{async sendMetadata(A){return A}receiveMetadata(A){return A}async sendMessage(A){return A}async receiveMessage(A){return A}receiveTrailers(A){return A}}to2.BaseFilter=oo2});
var BM=E((yn2)=>{Object.defineProperty(yn2,"__esModule",{value:!0});yn2.registerResolver=PJ6;yn2.registerDefaultScheme=SJ6;yn2.createResolver=jJ6;yn2.getDefaultAuthority=yJ6;yn2.mapUriDefaultScheme=kJ6;var jW0=hV(),io={},SW0=null;function PJ6(A,B){io[A]=B}function SJ6(A){SW0=A}function jJ6(A,B,Q){if(A.scheme!==void 0&&A.scheme in io)return new io[A.scheme](A,B,Q);else throw new Error(`No resolver could be created for target ${jW0.uriToString(A)}`)}function yJ6(A){if(A.scheme!==void 0&&A.scheme in io)return io[A.scheme].getDefaultAuthority(A);else throw new Error(`Invalid target ${jW0.uriToString(A)}`)}function kJ6(A){if(A.scheme===void 0||!(A.scheme in io))if(SW0!==null)return{scheme:SW0,authority:void 0,path:jW0.uriToString(A)};else return null;return A}});
var Cr2=E((Ru5,Vr2)=>{var F8=Vr2.exports=oY0();F8.build="light";function IK6(A,B,Q){if(typeof B==="function")Q=B,B=new F8.Root;else if(!B)B=new F8.Root;return B.load(A,Q)}F8.load=IK6;function YK6(A,B){if(!B)B=new F8.Root;return B.loadSync(A)}F8.loadSync=YK6;F8.encoder=zJ0();F8.decoder=GJ0();F8.verifier=YJ0();F8.converter=XJ0();F8.ReflectionObject=du();F8.Namespace=Qt();F8.Root=XP1();F8.Enum=Z$();F8.Type=YP1();F8.Field=o_();F8.OneOf=eo();F8.MapField=QP1();F8.Service=ZP1();F8.Method=DP1();F8.Message=GP1();F8.wrappers=VJ0();F8.types=mu();F8.util=$I();F8.ReflectionObject._configure(F8.Root);F8.Namespace._configure(F8.Type,F8.Service,F8.Enum);F8.Root._configure(F8.Type);F8.Field._configure(F8.Type)});
var D1B=E((B1B)=>{Object.defineProperty(B1B,"__esModule",{value:!0});B1B.RoundRobinLoadBalancer=void 0;B1B.setup=Tw6;var ee2=gu(),DX=EE(),_X0=s_(),Lw6=F7(),Mw6=_6(),oe2=UE(),Rw6=dP1(),Ow6="round_robin";function te2(A){Lw6.trace(Mw6.LogVerbosity.DEBUG,Ow6,A)}var iP1="round_robin";class xX0{getLoadBalancerName(){return iP1}constructor(){}toJsonObject(){return{[iP1]:{}}}static createFromJson(A){return new xX0}}class A1B{constructor(A,B=0){this.children=A,this.nextIndex=B}pick(A){let B=this.children[this.nextIndex].picker;return this.nextIndex=(this.nextIndex+1)%this.children.length,B.pick(A)}peekNextEndpoint(){return this.children[this.nextIndex].endpoint}}class vX0{constructor(A){this.channelControlHelper=A,this.children=[],this.currentState=DX.ConnectivityState.IDLE,this.currentReadyPicker=null,this.updatesPaused=!1,this.lastError=null,this.childChannelControlHelper=ee2.createChildChannelControlHelper(A,{updateState:(B,Q,D)=>{if(this.currentState===DX.ConnectivityState.READY&&B!==DX.ConnectivityState.READY)this.channelControlHelper.requestReresolution();if(D)this.lastError=D;this.calculateAndUpdateState()}})}countChildrenWithState(A){return this.children.filter((B)=>B.getConnectivityState()===A).length}calculateAndUpdateState(){if(this.updatesPaused)return;if(this.countChildrenWithState(DX.ConnectivityState.READY)>0){let A=this.children.filter((Q)=>Q.getConnectivityState()===DX.ConnectivityState.READY),B=0;if(this.currentReadyPicker!==null){let Q=this.currentReadyPicker.peekNextEndpoint();if(B=A.findIndex((D)=>oe2.endpointEqual(D.getEndpoint(),Q)),B<0)B=0}this.updateState(DX.ConnectivityState.READY,new A1B(A.map((Q)=>({endpoint:Q.getEndpoint(),picker:Q.getPicker()})),B),null)}else if(this.countChildrenWithState(DX.ConnectivityState.CONNECTING)>0)this.updateState(DX.ConnectivityState.CONNECTING,new _X0.QueuePicker(this),null);else if(this.countChildrenWithState(DX.ConnectivityState.TRANSIENT_FAILURE)>0){let A=`round_robin: No connection established. Last error: ${this.lastError}`;this.updateState(DX.ConnectivityState.TRANSIENT_FAILURE,new _X0.UnavailablePicker({details:A}),A)}else this.updateState(DX.ConnectivityState.IDLE,new _X0.QueuePicker(this),null);for(let A of this.children)if(A.getConnectivityState()===DX.ConnectivityState.IDLE)A.exitIdle()}updateState(A,B,Q){if(te2(DX.ConnectivityState[this.currentState]+" -> "+DX.ConnectivityState[A]),A===DX.ConnectivityState.READY)this.currentReadyPicker=B;else this.currentReadyPicker=null;this.currentState=A,this.channelControlHelper.updateState(A,B,Q)}resetSubchannelList(){for(let A of this.children)A.destroy()}updateAddressList(A,B,Q){this.resetSubchannelList(),te2("Connect to endpoint list "+A.map(oe2.endpointToString)),this.updatesPaused=!0,this.children=A.map((D)=>new Rw6.LeafLoadBalancer(D,this.childChannelControlHelper,Q));for(let D of this.children)D.startConnecting();this.updatesPaused=!1,this.calculateAndUpdateState()}exitIdle(){}resetBackoff(){}destroy(){this.resetSubchannelList()}getTypeName(){return iP1}}B1B.RoundRobinLoadBalancer=vX0;function Tw6(){ee2.registerLoadBalancerType(iP1,vX0,xX0)}});
var DP1=E((Vu5,ms2)=>{ms2.exports=cu;var DJ0=du();((cu.prototype=Object.create(DJ0.prototype)).constructor=cu).className="Method";var Dt=$I();function cu(A,B,Q,D,Z,G,F,I,Y){if(Dt.isObject(Z))F=Z,Z=G=void 0;else if(Dt.isObject(G))F=G,G=void 0;if(!(B===void 0||Dt.isString(B)))throw TypeError("type must be a string");if(!Dt.isString(Q))throw TypeError("requestType must be a string");if(!Dt.isString(D))throw TypeError("responseType must be a string");DJ0.call(this,A,F),this.type=B||"rpc",this.requestType=Q,this.requestStream=Z?!0:void 0,this.responseType=D,this.responseStream=G?!0:void 0,this.resolvedRequestType=null,this.resolvedResponseType=null,this.comment=I,this.parsedOptions=Y}cu.fromJSON=function A(B,Q){return new cu(B,Q.type,Q.requestType,Q.responseType,Q.requestStream,Q.responseStream,Q.options,Q.comment,Q.parsedOptions)};cu.prototype.toJSON=function A(B){var Q=B?Boolean(B.keepComments):!1;return Dt.toObject(["type",this.type!=="rpc"&&this.type||void 0,"requestType",this.requestType,"requestStream",this.requestStream,"responseType",this.responseType,"responseStream",this.responseStream,"options",this.options,"comment",Q?this.comment:void 0,"parsedOptions",this.parsedOptions])};cu.prototype.resolve=function A(){if(this.resolved)return this;return this.resolvedRequestType=this.parent.lookupType(this.requestType),this.resolvedResponseType=this.parent.lookupType(this.responseType),DJ0.prototype.resolve.call(this)}});
var EE=E((cn2)=>{Object.defineProperty(cn2,"__esModule",{value:!0});cn2.ConnectivityState=void 0;var dn2;(function(A){A[A.IDLE=0]="IDLE",A[A.CONNECTING=1]="CONNECTING",A[A.READY=2]="READY",A[A.TRANSIENT_FAILURE=3]="TRANSIENT_FAILURE",A[A.SHUTDOWN=4]="SHUTDOWN"})(dn2||(cn2.ConnectivityState=dn2={}))});
var EW0=E((jg5,gW6)=>{gW6.exports={name:"@grpc/grpc-js",version:"1.13.1",description:"gRPC Library for Node - pure JS implementation",homepage:"https://grpc.io/",repository:"https://github.com/grpc/grpc-node/tree/master/packages/grpc-js",main:"build/src/index.js",engines:{node:">=12.10.0"},keywords:[],author:{name:"Google Inc."},types:"build/src/index.d.ts",license:"Apache-2.0",devDependencies:{"@grpc/proto-loader":"file:../proto-loader","@types/gulp":"^4.0.17","@types/gulp-mocha":"0.0.37","@types/lodash":"^4.14.202","@types/mocha":"^10.0.6","@types/ncp":"^2.0.8","@types/node":">=20.11.20","@types/pify":"^5.0.4","@types/semver":"^7.5.8","@typescript-eslint/eslint-plugin":"^7.1.0","@typescript-eslint/parser":"^7.1.0","@typescript-eslint/typescript-estree":"^7.1.0","clang-format":"^1.8.0",eslint:"^8.42.0","eslint-config-prettier":"^8.8.0","eslint-plugin-node":"^11.1.0","eslint-plugin-prettier":"^4.2.1",execa:"^2.0.3",gulp:"^4.0.2","gulp-mocha":"^6.0.0",lodash:"^4.17.21",madge:"^5.0.1","mocha-jenkins-reporter":"^0.4.1",ncp:"^2.0.0",pify:"^4.0.1",prettier:"^2.8.8",rimraf:"^3.0.2",semver:"^7.6.0","ts-node":"^10.9.2",typescript:"^5.3.3"},contributors:[{name:"Google Inc."}],scripts:{build:"npm run compile",clean:"rimraf ./build",compile:"tsc -p .",format:'clang-format -i -style="{Language: JavaScript, BasedOnStyle: Google, ColumnLimit: 80}" src/*.ts test/*.ts',lint:"eslint src/*.ts test/*.ts",prepare:"npm run generate-types && npm run compile",test:"gulp test",check:"npm run lint",fix:"eslint --fix src/*.ts test/*.ts",pretest:"npm run generate-types && npm run generate-test-types && npm run compile",posttest:"npm run check && madge -c ./build/src","generate-types":"proto-loader-gen-types --keepCase --longs String --enums String --defaults --oneofs --includeComments --includeDirs proto/ --include-dirs test/fixtures/ -O src/generated/ --grpcLib ../index channelz.proto","generate-test-types":"proto-loader-gen-types --keepCase --longs String --enums String --defaults --oneofs --includeComments --include-dirs test/fixtures/ -O test/generated/ --grpcLib ../../src/index test_service.proto"},dependencies:{"@grpc/proto-loader":"^0.7.13","@js-sdsl/ordered-map":"^4.4.2"},files:["src/**/*.ts","build/src/**/*.{js,d.ts,js.map}","proto/*.proto","LICENSE","deps/envoy-api/envoy/api/v2/**/*.proto","deps/envoy-api/envoy/config/**/*.proto","deps/envoy-api/envoy/service/**/*.proto","deps/envoy-api/envoy/type/**/*.proto","deps/udpa/udpa/**/*.proto","deps/googleapis/google/api/*.proto","deps/googleapis/google/rpc/*.proto","deps/protoc-gen-validate/validate/**/*.proto"]}});
var F7=E((Un2)=>{var UW0,wW0,$W0,qW0;Object.defineProperty(Un2,"__esModule",{value:!0});Un2.log=Un2.setLoggerVerbosity=Un2.setLogger=Un2.getLogger=void 0;Un2.trace=rW6;Un2.isTracerEnabled=En2;var n_=_6(),uW6=J1("process"),mW6=EW0().version,dW6={error:(A,...B)=>{console.error("E "+A,...B)},info:(A,...B)=>{console.error("I "+A,...B)},debug:(A,...B)=>{console.error("D "+A,...B)}},hu=dW6,lo=n_.LogVerbosity.ERROR,cW6=(wW0=(UW0=process.env.GRPC_NODE_VERBOSITY)!==null&&UW0!==void 0?UW0:process.env.GRPC_VERBOSITY)!==null&&wW0!==void 0?wW0:"";switch(cW6.toUpperCase()){case"DEBUG":lo=n_.LogVerbosity.DEBUG;break;case"INFO":lo=n_.LogVerbosity.INFO;break;case"ERROR":lo=n_.LogVerbosity.ERROR;break;case"NONE":lo=n_.LogVerbosity.NONE;break;default:}var lW6=()=>{return hu};Un2.getLogger=lW6;var pW6=(A)=>{hu=A};Un2.setLogger=pW6;var iW6=(A)=>{lo=A};Un2.setLoggerVerbosity=iW6;var nW6=(A,...B)=>{let Q;if(A>=lo){switch(A){case n_.LogVerbosity.DEBUG:Q=hu.debug;break;case n_.LogVerbosity.INFO:Q=hu.info;break;case n_.LogVerbosity.ERROR:Q=hu.error;break}if(!Q)Q=hu.error;if(Q)Q.bind(hu)(...B)}};Un2.log=nW6;var aW6=(qW0=($W0=process.env.GRPC_NODE_TRACE)!==null&&$W0!==void 0?$W0:process.env.GRPC_TRACE)!==null&&qW0!==void 0?qW0:"",NW0=new Set,zn2=new Set;for(let A of aW6.split(","))if(A.startsWith("-"))zn2.add(A.substring(1));else NW0.add(A);var sW6=NW0.has("all");function rW6(A,B,Q){if(En2(B))Un2.log(A,new Date().toISOString()+" | v"+mW6+" "+uW6.pid+" | "+B+" | "+Q)}function En2(A){return!zn2.has(A)&&(sW6||NW0.has(A))}});
var GJ=E((Nn2)=>{Object.defineProperty(Nn2,"__esModule",{value:!0});Nn2.Metadata=void 0;var FJ6=F7(),IJ6=_6(),YJ6=kT1(),WJ6=/^[0-9a-z_.-]+$/,JJ6=/^[ -~]*$/;function XJ6(A){return WJ6.test(A)}function VJ6(A){return JJ6.test(A)}function qn2(A){return A.endsWith("-bin")}function CJ6(A){return!A.startsWith("grpc-")}function _T1(A){return A.toLowerCase()}function $n2(A,B){if(!XJ6(A))throw new Error('Metadata key "'+A+'" contains illegal characters');if(B!==null&&B!==void 0)if(qn2(A)){if(!Buffer.isBuffer(B))throw new Error("keys that end with '-bin' must have Buffer values")}else{if(Buffer.isBuffer(B))throw new Error("keys that don't end with '-bin' must have String values");if(!VJ6(B))throw new Error('Metadata string value "'+B+'" contains illegal characters')}}class xT1{constructor(A={}){this.internalRepr=new Map,this.options=A}set(A,B){A=_T1(A),$n2(A,B),this.internalRepr.set(A,[B])}add(A,B){A=_T1(A),$n2(A,B);let Q=this.internalRepr.get(A);if(Q===void 0)this.internalRepr.set(A,[B]);else Q.push(B)}remove(A){A=_T1(A),this.internalRepr.delete(A)}get(A){return A=_T1(A),this.internalRepr.get(A)||[]}getMap(){let A={};for(let[B,Q]of this.internalRepr)if(Q.length>0){let D=Q[0];A[B]=Buffer.isBuffer(D)?Buffer.from(D):D}return A}clone(){let A=new xT1(this.options),B=A.internalRepr;for(let[Q,D]of this.internalRepr){let Z=D.map((G)=>{if(Buffer.isBuffer(G))return Buffer.from(G);else return G});B.set(Q,Z)}return A}merge(A){for(let[B,Q]of A.internalRepr){let D=(this.internalRepr.get(B)||[]).concat(Q);this.internalRepr.set(B,D)}}setOptions(A){this.options=A}getOptions(){return this.options}toHttp2Headers(){let A={};for(let[B,Q]of this.internalRepr)A[B]=Q.map(KJ6);return A}toJSON(){let A={};for(let[B,Q]of this.internalRepr)A[B]=Q;return A}static fromHttp2Headers(A){let B=new xT1;for(let Q of Object.keys(A)){if(Q.charAt(0)===":")continue;let D=A[Q];try{if(qn2(Q)){if(Array.isArray(D))D.forEach((Z)=>{B.add(Q,Buffer.from(Z,"base64"))});else if(D!==void 0)if(CJ6(Q))D.split(",").forEach((Z)=>{B.add(Q,Buffer.from(Z.trim(),"base64"))});else B.add(Q,Buffer.from(D,"base64"))}else if(Array.isArray(D))D.forEach((Z)=>{B.add(Q,Z)});else if(D!==void 0)B.add(Q,D)}catch(Z){let G=`Failed to add metadata entry ${Q}: ${D}. ${YJ6.getErrorMessage(Z)}. For more information see https://github.com/grpc/grpc-node/issues/1173`;FJ6.log(IJ6.LogVerbosity.ERROR,G)}}return B}}Nn2.Metadata=xT1;var KJ6=(A)=>{return Buffer.isBuffer(A)?A.toString("base64"):A}});
var GJ0=E((Hu5,is2)=>{is2.exports=uC6;var hC6=Z$(),UP=mu(),ps2=$I();function gC6(A){return"missing required '"+A.name+"'"}function uC6(A){var B=ps2.codegen(["r","l"],A.name+"$decode")("if(!(r instanceof Reader))")("r=Reader.create(r)")("var c=l===undefined?r.len:r.pos+l,m=new this.ctor"+(A.fieldsArray.filter(function(I){return I.map}).length?",k,value":""))("while(r.pos<c){")("var t=r.uint32()");if(A.group)B("if((t&7)===4)")("break");B("switch(t>>>3){");var Q=0;for(;Q<A.fieldsArray.length;++Q){var D=A._fieldsArray[Q].resolve(),Z=D.resolvedType instanceof hC6?"int32":D.type,G="m"+ps2.safeProp(D.name);if(B("case %i: {",D.id),D.map){if(B("if(%s===util.emptyObject)",G)("%s={}",G)("var c2 = r.uint32()+r.pos"),UP.defaults[D.keyType]!==void 0)B("k=%j",UP.defaults[D.keyType]);else B("k=null");if(UP.defaults[Z]!==void 0)B("value=%j",UP.defaults[Z]);else B("value=null");if(B("while(r.pos<c2){")("var tag2=r.uint32()")("switch(tag2>>>3){")("case 1: k=r.%s(); break",D.keyType)("case 2:"),UP.basic[Z]===void 0)B("value=types[%i].decode(r,r.uint32())",Q);else B("value=r.%s()",Z);if(B("break")("default:")("r.skipType(tag2&7)")("break")("}")("}"),UP.long[D.keyType]!==void 0)B('%s[typeof k==="object"?util.longToHash(k):k]=value',G);else B("%s[k]=value",G)}else if(D.repeated){if(B("if(!(%s&&%s.length))",G,G)("%s=[]",G),UP.packed[Z]!==void 0)B("if((t&7)===2){")("var c2=r.uint32()+r.pos")("while(r.pos<c2)")("%s.push(r.%s())",G,Z)("}else");if(UP.basic[Z]===void 0)B(D.resolvedType.group?"%s.push(types[%i].decode(r))":"%s.push(types[%i].decode(r,r.uint32()))",G,Q);else B("%s.push(r.%s())",G,Z)}else if(UP.basic[Z]===void 0)B(D.resolvedType.group?"%s=types[%i].decode(r)":"%s=types[%i].decode(r,r.uint32())",G,Q);else B("%s=r.%s()",G,Z);B("break")("}")}B("default:")("r.skipType(t&7)")("break")("}")("}");for(Q=0;Q<A._fieldsArray.length;++Q){var F=A._fieldsArray[Q];if(F.required)B("if(!m.hasOwnProperty(%j))",F.name)("throw util.ProtocolError(%j,{instance:m})",gC6(F))}return B("return m")}});
var GP1=E((Ku5,ls2)=>{ls2.exports=DM;var fC6=eL();function DM(A){if(A)for(var B=Object.keys(A),Q=0;Q<B.length;++Q)this[B[Q]]=A[B[Q]]}DM.create=function A(B){return this.$type.create(B)};DM.encode=function A(B,Q){return this.$type.encode(B,Q)};DM.encodeDelimited=function A(B,Q){return this.$type.encodeDelimited(B,Q)};DM.decode=function A(B){return this.$type.decode(B)};DM.decodeDelimited=function A(B){return this.$type.decodeDelimited(B)};DM.verify=function A(B){return this.$type.verify(B)};DM.fromObject=function A(B){return this.$type.fromObject(B)};DM.toObject=function A(B,Q){return this.$type.toObject(B,Q)};DM.prototype.toJSON=function A(){return this.$type.toObject(this,fC6.toJSONOptions)}});
var HP1=E((Su5,Or2)=>{var Ax=Or2.exports=Cr2();Ax.build="full";Ax.tokenize=MJ0();Ax.parse=Nr2();Ax.common=Rr2();Ax.Root._configure(Ax.Type,Ax.parse,Ax.common)});
var IX0=E((bt2)=>{Object.defineProperty(bt2,"__esModule",{value:!0});bt2.InternalChannel=bt2.SUBCHANNEL_ARGS_EXCLUDE_KEY_PREFIX=void 0;var xE6=w31(),vE6=Za2(),bE6=po2(),FX0=s_(),fE6=GJ(),Zx=_6(),hE6=tJ0(),gE6=Yt2(),yt2=BM(),vP1=F7(),uE6=lJ0(),bP1=hV(),LE=EE(),a31=pu(),mE6=zt2(),dE6=i31(),cE6=qt2(),ZX0=nJ0(),lE6=jP1(),GX0=Tt2(),pE6=xP1(),iE6=2147483647,nE6=1000,aE6=1800000,fP1=new Map,sE6=16777216,rE6=1048576;class kt2 extends pE6.BaseSubchannelWrapper{constructor(A,B){super(A);this.channel=B,this.refCount=0,this.subchannelStateListener=(Q,D,Z,G)=>{B.throttleKeepalive(G)}}ref(){if(this.refCount===0)this.child.addConnectivityStateListener(this.subchannelStateListener),this.channel.addWrappedSubchannel(this);this.child.ref(),this.refCount+=1}unref(){if(this.child.unref(),this.refCount-=1,this.refCount<=0)this.child.removeConnectivityStateListener(this.subchannelStateListener),this.channel.removeWrappedSubchannel(this)}}class _t2{pick(A){return{pickResultType:FX0.PickResultType.DROP,status:{code:Zx.Status.UNAVAILABLE,details:"Channel closed before call started",metadata:new fE6.Metadata},subchannel:null,onCallStarted:null,onCallEnded:null}}}bt2.SUBCHANNEL_ARGS_EXCLUDE_KEY_PREFIX="grpc.internal.no_subchannel";class xt2{constructor(A){this.target=A,this.trace=new a31.ChannelzTrace,this.callTracker=new a31.ChannelzCallTracker,this.childrenTracker=new a31.ChannelzChildrenTracker,this.state=LE.ConnectivityState.IDLE}getChannelzInfoCallback(){return()=>{return{target:this.target,state:this.state,trace:this.trace,callTracker:this.callTracker,children:this.childrenTracker.getChildLists()}}}}class vt2{constructor(A,B,Q){var D,Z,G,F,I,Y;if(this.credentials=B,this.options=Q,this.connectivityState=LE.ConnectivityState.IDLE,this.currentPicker=new FX0.UnavailablePicker,this.configSelectionQueue=[],this.pickQueue=[],this.connectivityStateWatchers=[],this.callRefTimer=null,this.configSelector=null,this.currentResolutionError=null,this.wrappedSubchannels=new Set,this.callCount=0,this.idleTimer=null,this.channelzEnabled=!0,this.randomChannelId=Math.floor(Math.random()*Number.MAX_SAFE_INTEGER),typeof A!=="string")throw new TypeError("Channel target must be a string");if(!(B instanceof xE6.ChannelCredentials))throw new TypeError("Channel credentials must be a ChannelCredentials object");if(Q){if(typeof Q!=="object")throw new TypeError("Channel options must be an object")}this.channelzInfoTracker=new xt2(A);let W=bP1.parseUri(A);if(W===null)throw new Error(`Could not parse target name "${A}"`);let J=yt2.mapUriDefaultScheme(W);if(J===null)throw new Error(`Could not find a default scheme for target name "${A}"`);if(this.options["grpc.enable_channelz"]===0)this.channelzEnabled=!1;if(this.channelzRef=a31.registerChannelzChannel(A,this.channelzInfoTracker.getChannelzInfoCallback(),this.channelzEnabled),this.channelzEnabled)this.channelzInfoTracker.trace.addTrace("CT_INFO","Channel created");if(this.options["grpc.default_authority"])this.defaultAuthority=this.options["grpc.default_authority"];else this.defaultAuthority=yt2.getDefaultAuthority(J);let X=uE6.mapProxyName(J,Q);this.target=X.target,this.options=Object.assign({},this.options,X.extraOptions),this.subchannelPool=bE6.getSubchannelPool(((D=Q["grpc.use_local_subchannel_pool"])!==null&&D!==void 0?D:0)===0),this.retryBufferTracker=new GX0.MessageBufferTracker((Z=Q["grpc.retry_buffer_size"])!==null&&Z!==void 0?Z:sE6,(G=Q["grpc.per_rpc_retry_buffer_size"])!==null&&G!==void 0?G:rE6),this.keepaliveTime=(F=Q["grpc.keepalive_time_ms"])!==null&&F!==void 0?F:-1,this.idleTimeoutMs=Math.max((I=Q["grpc.client_idle_timeout_ms"])!==null&&I!==void 0?I:aE6,nE6);let V={createSubchannel:(K,H)=>{let z={};for(let[N,O]of Object.entries(H))if(!N.startsWith(bt2.SUBCHANNEL_ARGS_EXCLUDE_KEY_PREFIX))z[N]=O;let $=this.subchannelPool.getOrCreateSubchannel(this.target,K,z,this.credentials);if($.throttleKeepalive(this.keepaliveTime),this.channelzEnabled)this.channelzInfoTracker.trace.addTrace("CT_INFO","Created subchannel or used existing subchannel",$.getChannelzRef());return new kt2($,this)},updateState:(K,H)=>{this.currentPicker=H;let z=this.pickQueue.slice();if(this.pickQueue=[],z.length>0)this.callRefTimerUnref();for(let $ of z)$.doPick();this.updateState(K)},requestReresolution:()=>{throw new Error("Resolving load balancer should never call requestReresolution")},addChannelzChild:(K)=>{if(this.channelzEnabled)this.channelzInfoTracker.childrenTracker.refChild(K)},removeChannelzChild:(K)=>{if(this.channelzEnabled)this.channelzInfoTracker.childrenTracker.unrefChild(K)}};this.resolvingLoadBalancer=new vE6.ResolvingLoadBalancer(this.target,V,Q,(K,H)=>{var z;if(K.retryThrottling)fP1.set(this.getTarget(),new GX0.RetryThrottler(K.retryThrottling.maxTokens,K.retryThrottling.tokenRatio,fP1.get(this.getTarget())));else fP1.delete(this.getTarget());if(this.channelzEnabled)this.channelzInfoTracker.trace.addTrace("CT_INFO","Address resolution succeeded");(z=this.configSelector)===null||z===void 0||z.unref(),this.configSelector=H,this.currentResolutionError=null,process.nextTick(()=>{let $=this.configSelectionQueue;if(this.configSelectionQueue=[],$.length>0)this.callRefTimerUnref();for(let L of $)L.getConfig()})},(K)=>{if(this.channelzEnabled)this.channelzInfoTracker.trace.addTrace("CT_WARNING","Address resolution failed with code "+K.code+' and details "'+K.details+'"');if(this.configSelectionQueue.length>0)this.trace("Name resolution failed with calls queued for config selection");if(this.configSelector===null)this.currentResolutionError=Object.assign(Object.assign({},lE6.restrictControlPlaneStatusCode(K.code,K.details)),{metadata:K.metadata});let H=this.configSelectionQueue;if(this.configSelectionQueue=[],H.length>0)this.callRefTimerUnref();for(let z of H)z.reportResolverError(K)}),this.filterStackFactory=new hE6.FilterStackFactory([new gE6.CompressionFilterFactory(this,this.options)]),this.trace("Channel constructed with options "+JSON.stringify(Q,void 0,2));let C=new Error;if(vP1.isTracerEnabled("channel_stacktrace"))vP1.trace(Zx.LogVerbosity.DEBUG,"channel_stacktrace","("+this.channelzRef.id+`) Channel constructed 
`+((Y=C.stack)===null||Y===void 0?void 0:Y.substring(C.stack.indexOf(`
`)+1)));this.lastActivityTimestamp=new Date}trace(A,B){vP1.trace(B!==null&&B!==void 0?B:Zx.LogVerbosity.DEBUG,"channel","("+this.channelzRef.id+") "+bP1.uriToString(this.target)+" "+A)}callRefTimerRef(){var A,B,Q,D;if(!this.callRefTimer)this.callRefTimer=setInterval(()=>{},iE6);if(!((B=(A=this.callRefTimer).hasRef)===null||B===void 0?void 0:B.call(A)))this.trace("callRefTimer.ref | configSelectionQueue.length="+this.configSelectionQueue.length+" pickQueue.length="+this.pickQueue.length),(D=(Q=this.callRefTimer).ref)===null||D===void 0||D.call(Q)}callRefTimerUnref(){var A,B,Q;if(!((A=this.callRefTimer)===null||A===void 0?void 0:A.hasRef)||this.callRefTimer.hasRef())this.trace("callRefTimer.unref | configSelectionQueue.length="+this.configSelectionQueue.length+" pickQueue.length="+this.pickQueue.length),(Q=(B=this.callRefTimer)===null||B===void 0?void 0:B.unref)===null||Q===void 0||Q.call(B)}removeConnectivityStateWatcher(A){let B=this.connectivityStateWatchers.findIndex((Q)=>Q===A);if(B>=0)this.connectivityStateWatchers.splice(B,1)}updateState(A){if(vP1.trace(Zx.LogVerbosity.DEBUG,"connectivity_state","("+this.channelzRef.id+") "+bP1.uriToString(this.target)+" "+LE.ConnectivityState[this.connectivityState]+" -> "+LE.ConnectivityState[A]),this.channelzEnabled)this.channelzInfoTracker.trace.addTrace("CT_INFO","Connectivity state change to "+LE.ConnectivityState[A]);this.connectivityState=A,this.channelzInfoTracker.state=A;let B=this.connectivityStateWatchers.slice();for(let Q of B)if(A!==Q.currentState){if(Q.timer)clearTimeout(Q.timer);this.removeConnectivityStateWatcher(Q),Q.callback()}if(A!==LE.ConnectivityState.TRANSIENT_FAILURE)this.currentResolutionError=null}throttleKeepalive(A){if(A>this.keepaliveTime){this.keepaliveTime=A;for(let B of this.wrappedSubchannels)B.throttleKeepalive(A)}}addWrappedSubchannel(A){this.wrappedSubchannels.add(A)}removeWrappedSubchannel(A){this.wrappedSubchannels.delete(A)}doPick(A,B){return this.currentPicker.pick({metadata:A,extraPickInfo:B})}queueCallForPick(A){this.pickQueue.push(A),this.callRefTimerRef()}getConfig(A,B){if(this.connectivityState!==LE.ConnectivityState.SHUTDOWN)this.resolvingLoadBalancer.exitIdle();if(this.configSelector)return{type:"SUCCESS",config:this.configSelector.invoke(A,B,this.randomChannelId)};else if(this.currentResolutionError)return{type:"ERROR",error:this.currentResolutionError};else return{type:"NONE"}}queueCallForConfig(A){this.configSelectionQueue.push(A),this.callRefTimerRef()}enterIdle(){if(this.resolvingLoadBalancer.destroy(),this.updateState(LE.ConnectivityState.IDLE),this.currentPicker=new FX0.QueuePicker(this.resolvingLoadBalancer),this.idleTimer)clearTimeout(this.idleTimer),this.idleTimer=null;if(this.callRefTimer)clearInterval(this.callRefTimer),this.callRefTimer=null}startIdleTimeout(A){var B,Q;this.idleTimer=setTimeout(()=>{if(this.callCount>0){this.startIdleTimeout(this.idleTimeoutMs);return}let Z=new Date().valueOf()-this.lastActivityTimestamp.valueOf();if(Z>=this.idleTimeoutMs)this.trace("Idle timer triggered after "+this.idleTimeoutMs+"ms of inactivity"),this.enterIdle();else this.startIdleTimeout(this.idleTimeoutMs-Z)},A),(Q=(B=this.idleTimer).unref)===null||Q===void 0||Q.call(B)}maybeStartIdleTimer(){if(this.connectivityState!==LE.ConnectivityState.SHUTDOWN&&!this.idleTimer)this.startIdleTimeout(this.idleTimeoutMs)}onCallStart(){if(this.channelzEnabled)this.channelzInfoTracker.callTracker.addCallStarted();this.callCount+=1}onCallEnd(A){if(this.channelzEnabled)if(A.code===Zx.Status.OK)this.channelzInfoTracker.callTracker.addCallSucceeded();else this.channelzInfoTracker.callTracker.addCallFailed();this.callCount-=1,this.lastActivityTimestamp=new Date,this.maybeStartIdleTimer()}createLoadBalancingCall(A,B,Q,D,Z){let G=ZX0.getNextCallNumber();return this.trace("createLoadBalancingCall ["+G+'] method="'+B+'"'),new mE6.LoadBalancingCall(this,A,B,Q,D,Z,G)}createRetryingCall(A,B,Q,D,Z){let G=ZX0.getNextCallNumber();return this.trace("createRetryingCall ["+G+'] method="'+B+'"'),new GX0.RetryingCall(this,A,B,Q,D,Z,G,this.retryBufferTracker,fP1.get(this.getTarget()))}createResolvingCall(A,B,Q,D,Z){let G=ZX0.getNextCallNumber();this.trace("createResolvingCall ["+G+'] method="'+A+'", deadline='+dE6.deadlineToString(B));let F={deadline:B,flags:Z!==null&&Z!==void 0?Z:Zx.Propagate.DEFAULTS,host:Q!==null&&Q!==void 0?Q:this.defaultAuthority,parentCall:D},I=new cE6.ResolvingCall(this,A,F,this.filterStackFactory.clone(),G);return this.onCallStart(),I.addStatusWatcher((Y)=>{this.onCallEnd(Y)}),I}close(){var A;this.resolvingLoadBalancer.destroy(),this.updateState(LE.ConnectivityState.SHUTDOWN),this.currentPicker=new _t2;for(let B of this.configSelectionQueue)B.cancelWithStatus(Zx.Status.UNAVAILABLE,"Channel closed before call started");this.configSelectionQueue=[];for(let B of this.pickQueue)B.cancelWithStatus(Zx.Status.UNAVAILABLE,"Channel closed before call started");if(this.pickQueue=[],this.callRefTimer)clearInterval(this.callRefTimer);if(this.idleTimer)clearTimeout(this.idleTimer);if(this.channelzEnabled)a31.unregisterChannelzRef(this.channelzRef);this.subchannelPool.unrefUnusedSubchannels(),(A=this.configSelector)===null||A===void 0||A.unref(),this.configSelector=null}getTarget(){return bP1.uriToString(this.target)}getConnectivityState(A){let B=this.connectivityState;if(A)this.resolvingLoadBalancer.exitIdle(),this.lastActivityTimestamp=new Date,this.maybeStartIdleTimer();return B}watchConnectivityState(A,B,Q){if(this.connectivityState===LE.ConnectivityState.SHUTDOWN)throw new Error("Channel has been shut down");let D=null;if(B!==1/0){let G=B instanceof Date?B:new Date(B),F=new Date;if(B===-1/0||G<=F){process.nextTick(Q,new Error("Deadline passed without connectivity state change"));return}D=setTimeout(()=>{this.removeConnectivityStateWatcher(Z),Q(new Error("Deadline passed without connectivity state change"))},G.getTime()-F.getTime())}let Z={currentState:A,callback:Q,timer:D};this.connectivityStateWatchers.push(Z)}getChannelzRef(){return this.channelzRef}createCall(A,B,Q,D,Z){if(typeof A!=="string")throw new TypeError("Channel#createCall: method must be a string");if(!(typeof B==="number"||B instanceof Date))throw new TypeError("Channel#createCall: deadline must be a number or Date");if(this.connectivityState===LE.ConnectivityState.SHUTDOWN)throw new Error("Channel has been shut down");return this.createResolvingCall(A,B,Q,D,Z)}getOptions(){return this.options}}bt2.InternalChannel=vt2});
var Ia2=E((Ga2)=>{Object.defineProperty(Ga2,"__esModule",{value:!0});Ga2.recognizedOptions=void 0;Ga2.channelOptionsEqual=nX6;Ga2.recognizedOptions={"grpc.ssl_target_name_override":!0,"grpc.primary_user_agent":!0,"grpc.secondary_user_agent":!0,"grpc.default_authority":!0,"grpc.keepalive_time_ms":!0,"grpc.keepalive_timeout_ms":!0,"grpc.keepalive_permit_without_calls":!0,"grpc.service_config":!0,"grpc.max_concurrent_streams":!0,"grpc.initial_reconnect_backoff_ms":!0,"grpc.max_reconnect_backoff_ms":!0,"grpc.use_local_subchannel_pool":!0,"grpc.max_send_message_length":!0,"grpc.max_receive_message_length":!0,"grpc.enable_http_proxy":!0,"grpc.enable_channelz":!0,"grpc.dns_min_time_between_resolutions_ms":!0,"grpc.enable_retries":!0,"grpc.per_rpc_retry_buffer_size":!0,"grpc.retry_buffer_size":!0,"grpc.max_connection_age_ms":!0,"grpc.max_connection_age_grace_ms":!0,"grpc-node.max_session_memory":!0,"grpc.service_config_disable_resolution":!0,"grpc.client_idle_timeout_ms":!0,"grpc-node.tls_enable_trace":!0,"grpc.lb.ring_hash.ring_size_cap":!0,"grpc-node.retry_max_attempts_limit":!0,"grpc-node.flow_control_window":!0};function nX6(A,B){let Q=Object.keys(A).sort(),D=Object.keys(B).sort();if(Q.length!==D.length)return!1;for(let Z=0;Z<Q.length;Z+=1){if(Q[Z]!==D[Z])return!1;if(A[Q[Z]]!==B[D[Z]])return!1}return!0}});
var L1B=E((q1B)=>{Object.defineProperty(q1B,"__esModule",{value:!0});q1B.VERSION=void 0;q1B.VERSION="0.200.0"});
var Ls2=E((Zu5,Ns2)=>{Ns2.exports=rW0;function rW0(A,B){if(typeof A==="string")B=A,A=void 0;var Q=[];function D(G){if(typeof G!=="string"){var F=Z();if(rW0.verbose)console.log("codegen: "+F);if(F="return "+F,G){var I=Object.keys(G),Y=new Array(I.length+1),W=new Array(I.length),J=0;while(J<I.length)Y[J]=I[J],W[J]=G[I[J++]];return Y[J]=F,Function.apply(null,Y).apply(null,W)}return Function(F)()}var X=new Array(arguments.length-1),V=0;while(V<X.length)X[V]=arguments[++V];if(V=0,G=G.replace(/%([%dfijs])/g,function C(K,H){var z=X[V++];switch(H){case"d":case"f":return String(Number(z));case"i":return String(Math.floor(z));case"j":return JSON.stringify(z);case"s":return String(z)}return"%"}),V!==X.length)throw Error("parameter count mismatch");return Q.push(G),D}function Z(G){return"function "+(G||B||"")+"("+(A&&A.join(",")||"")+`){
  `+Q.join(`
  `)+`
}`}return D.toString=Z,D}rW0.verbose=!1});
var MJ0=E((Ou5,zr2)=>{zr2.exports=Hr2;var LJ0=/[\s{}=;:[\],'"()<>]/g,WK6=/(?:"([^"\\]*(?:\\.[^"\\]*)*)")/g,JK6=/(?:'([^'\\]*(?:\\.[^'\\]*)*)')/g,XK6=/^ *[*/]+ */,VK6=/^\s*\*?\/*/,CK6=/\n/g,KK6=/\s/,HK6=/\\(.?)/g,zK6={"0":"\x00",r:"\r",n:`
`,t:"\t"};function Kr2(A){return A.replace(HK6,function(B,Q){switch(Q){case"\\":case"":return Q;default:return zK6[Q]||""}})}Hr2.unescape=Kr2;function Hr2(A,B){A=A.toString();var Q=0,D=A.length,Z=1,G=0,F={},I=[],Y=null;function W(O){return Error("illegal "+O+" (line "+Z+")")}function J(){var O=Y==="'"?JK6:WK6;O.lastIndex=Q-1;var R=O.exec(A);if(!R)throw W("string");return Q=O.lastIndex,z(Y),Y=null,Kr2(R[1])}function X(O){return A.charAt(O)}function V(O,R,T){var j={type:A.charAt(O++),lineEmpty:!1,leading:T},f;if(B)f=2;else f=3;var y=O-f,c;do if(--y<0||(c=A.charAt(y))===`
`){j.lineEmpty=!0;break}while(c===" "||c==="\t");var h=A.substring(O,R).split(CK6);for(var a=0;a<h.length;++a)h[a]=h[a].replace(B?VK6:XK6,"").trim();j.text=h.join(`
`).trim(),F[Z]=j,G=Z}function C(O){var R=K(O),T=A.substring(O,R),j=/^\s*\/\//.test(T);return j}function K(O){var R=O;while(R<D&&X(R)!==`
`)R++;return R}function H(){if(I.length>0)return I.shift();if(Y)return J();var O,R,T,j,f,y=Q===0;do{if(Q===D)return null;O=!1;while(KK6.test(T=X(Q))){if(T===`
`)y=!0,++Z;if(++Q===D)return null}if(X(Q)==="/"){if(++Q===D)throw W("comment");if(X(Q)==="/")if(!B){f=X(j=Q+1)==="/";while(X(++Q)!==`
`)if(Q===D)return null;if(++Q,f)V(j,Q-1,y),y=!0;++Z,O=!0}else{if(j=Q,f=!1,C(Q-1)){f=!0;do{if(Q=K(Q),Q===D)break;if(Q++,!y)break}while(C(Q))}else Q=Math.min(D,K(Q)+1);if(f)V(j,Q,y),y=!0;Z++,O=!0}else if((T=X(Q))==="*"){j=Q+1,f=B||X(j)==="*";do{if(T===`
`)++Z;if(++Q===D)throw W("comment");R=T,T=X(Q)}while(R!=="*"||T!=="/");if(++Q,f)V(j,Q-2,y),y=!0;O=!0}else return"/"}}while(O);var c=Q;LJ0.lastIndex=0;var h=LJ0.test(X(c++));if(!h)while(c<D&&!LJ0.test(X(c)))++c;var a=A.substring(Q,Q=c);if(a==='"'||a==="'")Y=a;return a}function z(O){I.push(O)}function $(){if(!I.length){var O=H();if(O===null)return null;z(O)}return I[0]}function L(O,R){var T=$(),j=T===O;if(j)return H(),!0;if(!R)throw W("token '"+T+"', '"+O+"' expected");return!1}function N(O){var R=null,T;if(O===void 0){if(T=F[Z-1],delete F[Z-1],T&&(B||T.type==="*"||T.lineEmpty))R=T.leading?T.text:null}else{if(G<O)$();if(T=F[O],delete F[O],T&&!T.lineEmpty&&(B||T.type==="/"))R=T.leading?null:T.text}return R}return Object.defineProperty({next:H,peek:$,push:z,skip:L,cmnt:N},"line",{get:function(){return Z}})}});
var MX0=E((Le2)=>{Object.defineProperty(Le2,"__esModule",{value:!0});Le2.msToDuration=_U6;Le2.durationToMs=xU6;Le2.isDuration=vU6;Le2.parseDuration=fU6;function _U6(A){return{seconds:A/1000|0,nanos:A%1000*1e6|0}}function xU6(A){return A.seconds*1000+A.nanos/1e6|0}function vU6(A){return typeof A.seconds==="number"&&typeof A.nanos==="number"}var bU6=/^(\d+)(?:\.(\d+))?s$/;function fU6(A){let B=A.match(bU6);if(!B)return null;return{seconds:Number.parseInt(B[1],10),nanos:B[2]?Number.parseInt(B[2].padEnd(9,"0"),10):0}}});
var Na2=E((qa2)=>{Object.defineProperty(qa2,"t",{value:!0});class bW0{constructor(A,B,Q=1){this.i=void 0,this.h=void 0,this.o=void 0,this.u=A,this.l=B,this.p=Q}I(){let A=this,B=A.o.o===A;if(B&&A.p===1)A=A.h;else if(A.i){A=A.i;while(A.h)A=A.h}else{if(B)return A.o;let Q=A.o;while(Q.i===A)A=Q,Q=A.o;A=Q}return A}B(){let A=this;if(A.h){A=A.h;while(A.i)A=A.i;return A}else{let B=A.o;while(B.h===A)A=B,B=A.o;if(A.h!==B)return B;else return A}}_(){let A=this.o,B=this.h,Q=B.i;if(A.o===this)A.o=B;else if(A.i===this)A.i=B;else A.h=B;if(B.o=A,B.i=this,this.o=B,this.h=Q,Q)Q.o=this;return B}g(){let A=this.o,B=this.i,Q=B.h;if(A.o===this)A.o=B;else if(A.i===this)A.i=B;else A.h=B;if(B.o=A,B.h=this,this.o=B,this.i=Q,Q)Q.o=this;return B}}class Ka2 extends bW0{constructor(){super(...arguments);this.M=1}_(){let A=super._();return this.O(),A.O(),A}g(){let A=super.g();return this.O(),A.O(),A}O(){if(this.M=1,this.i)this.M+=this.i.M;if(this.h)this.M+=this.h.M}}class Ha2{constructor(A=0){this.iteratorType=A}equals(A){return this.T===A.T}}class za2{constructor(){this.m=0}get length(){return this.m}size(){return this.m}empty(){return this.m===0}}class Ea2 extends za2{}function uu(){throw new RangeError("Iterator access denied!")}class Ua2 extends Ea2{constructor(A=function(Q,D){if(Q<D)return-1;if(Q>D)return 1;return 0},B=!1){super();this.v=void 0,this.A=A,this.enableIndex=B,this.N=B?Ka2:bW0,this.C=new this.N}R(A,B){let Q=this.C;while(A){let D=this.A(A.u,B);if(D<0)A=A.h;else if(D>0)Q=A,A=A.i;else return A}return Q}K(A,B){let Q=this.C;while(A)if(this.A(A.u,B)<=0)A=A.h;else Q=A,A=A.i;return Q}L(A,B){let Q=this.C;while(A){let D=this.A(A.u,B);if(D<0)Q=A,A=A.h;else if(D>0)A=A.i;else return A}return Q}k(A,B){let Q=this.C;while(A)if(this.A(A.u,B)<0)Q=A,A=A.h;else A=A.i;return Q}P(A){while(!0){let B=A.o;if(B===this.C)return;if(A.p===1){A.p=0;return}if(A===B.i){let Q=B.h;if(Q.p===1)if(Q.p=0,B.p=1,B===this.v)this.v=B._();else B._();else if(Q.h&&Q.h.p===1){if(Q.p=B.p,B.p=0,Q.h.p=0,B===this.v)this.v=B._();else B._();return}else if(Q.i&&Q.i.p===1)Q.p=1,Q.i.p=0,Q.g();else Q.p=1,A=B}else{let Q=B.i;if(Q.p===1)if(Q.p=0,B.p=1,B===this.v)this.v=B.g();else B.g();else if(Q.i&&Q.i.p===1){if(Q.p=B.p,B.p=0,Q.i.p=0,B===this.v)this.v=B.g();else B.g();return}else if(Q.h&&Q.h.p===1)Q.p=1,Q.h.p=0,Q._();else Q.p=1,A=B}}}S(A){if(this.m===1){this.clear();return}let B=A;while(B.i||B.h){if(B.h){B=B.h;while(B.i)B=B.i}else B=B.i;let D=A.u;A.u=B.u,B.u=D;let Z=A.l;A.l=B.l,B.l=Z,A=B}if(this.C.i===B)this.C.i=B.o;else if(this.C.h===B)this.C.h=B.o;this.P(B);let Q=B.o;if(B===Q.i)Q.i=void 0;else Q.h=void 0;if(this.m-=1,this.v.p=0,this.enableIndex)while(Q!==this.C)Q.M-=1,Q=Q.o}U(A){let B=typeof A==="number"?A:void 0,Q=typeof A==="function"?A:void 0,D=typeof A==="undefined"?[]:void 0,Z=0,G=this.v,F=[];while(F.length||G)if(G)F.push(G),G=G.i;else{if(G=F.pop(),Z===B)return G;D&&D.push(G),Q&&Q(G,Z,this),Z+=1,G=G.h}return D}j(A){while(!0){let B=A.o;if(B.p===0)return;let Q=B.o;if(B===Q.i){let D=Q.h;if(D&&D.p===1){if(D.p=B.p=0,Q===this.v)return;Q.p=1,A=Q;continue}else if(A===B.h){if(A.p=0,A.i)A.i.o=B;if(A.h)A.h.o=Q;if(B.h=A.i,Q.i=A.h,A.i=B,A.h=Q,Q===this.v)this.v=A,this.C.o=A;else{let Z=Q.o;if(Z.i===Q)Z.i=A;else Z.h=A}A.o=Q.o,B.o=A,Q.o=A,Q.p=1}else{if(B.p=0,Q===this.v)this.v=Q.g();else Q.g();Q.p=1;return}}else{let D=Q.i;if(D&&D.p===1){if(D.p=B.p=0,Q===this.v)return;Q.p=1,A=Q;continue}else if(A===B.i){if(A.p=0,A.i)A.i.o=Q;if(A.h)A.h.o=B;if(Q.h=A.i,B.i=A.h,A.i=Q,A.h=B,Q===this.v)this.v=A,this.C.o=A;else{let Z=Q.o;if(Z.i===Q)Z.i=A;else Z.h=A}A.o=Q.o,B.o=A,Q.o=A,Q.p=1}else{if(B.p=0,Q===this.v)this.v=Q._();else Q._();Q.p=1;return}}if(this.enableIndex)B.O(),Q.O(),A.O();return}}q(A,B,Q){if(this.v===void 0)return this.m+=1,this.v=new this.N(A,B,0),this.v.o=this.C,this.C.o=this.C.i=this.C.h=this.v,this.m;let D,Z=this.C.i,G=this.A(Z.u,A);if(G===0)return Z.l=B,this.m;else if(G>0)Z.i=new this.N(A,B),Z.i.o=Z,D=Z.i,this.C.i=D;else{let F=this.C.h,I=this.A(F.u,A);if(I===0)return F.l=B,this.m;else if(I<0)F.h=new this.N(A,B),F.h.o=F,D=F.h,this.C.h=D;else{if(Q!==void 0){let Y=Q.T;if(Y!==this.C){let W=this.A(Y.u,A);if(W===0)return Y.l=B,this.m;else if(W>0){let J=Y.I(),X=this.A(J.u,A);if(X===0)return J.l=B,this.m;else if(X<0)if(D=new this.N(A,B),J.h===void 0)J.h=D,D.o=J;else Y.i=D,D.o=Y}}}if(D===void 0){D=this.v;while(!0){let Y=this.A(D.u,A);if(Y>0){if(D.i===void 0){D.i=new this.N(A,B),D.i.o=D,D=D.i;break}D=D.i}else if(Y<0){if(D.h===void 0){D.h=new this.N(A,B),D.h.o=D,D=D.h;break}D=D.h}else return D.l=B,this.m}}}}if(this.enableIndex){let F=D.o;while(F!==this.C)F.M+=1,F=F.o}return this.j(D),this.m+=1,this.m}H(A,B){while(A){let Q=this.A(A.u,B);if(Q<0)A=A.h;else if(Q>0)A=A.i;else return A}return A||this.C}clear(){this.m=0,this.v=void 0,this.C.o=void 0,this.C.i=this.C.h=void 0}updateKeyByIterator(A,B){let Q=A.T;if(Q===this.C)uu();if(this.m===1)return Q.u=B,!0;let D=Q.B().u;if(Q===this.C.i){if(this.A(D,B)>0)return Q.u=B,!0;return!1}let Z=Q.I().u;if(Q===this.C.h){if(this.A(Z,B)<0)return Q.u=B,!0;return!1}if(this.A(Z,B)>=0||this.A(D,B)<=0)return!1;return Q.u=B,!0}eraseElementByPos(A){if(A<0||A>this.m-1)throw new RangeError;let B=this.U(A);return this.S(B),this.m}eraseElementByKey(A){if(this.m===0)return!1;let B=this.H(this.v,A);if(B===this.C)return!1;return this.S(B),!0}eraseElementByIterator(A){let B=A.T;if(B===this.C)uu();let Q=B.h===void 0;if(A.iteratorType===0){if(Q)A.next()}else if(!Q||B.i===void 0)A.next();return this.S(B),A}getHeight(){if(this.m===0)return 0;function A(B){if(!B)return 0;return Math.max(A(B.i),A(B.h))+1}return A(this.v)}}class wa2 extends Ha2{constructor(A,B,Q){super(Q);if(this.T=A,this.C=B,this.iteratorType===0)this.pre=function(){if(this.T===this.C.i)uu();return this.T=this.T.I(),this},this.next=function(){if(this.T===this.C)uu();return this.T=this.T.B(),this};else this.pre=function(){if(this.T===this.C.h)uu();return this.T=this.T.B(),this},this.next=function(){if(this.T===this.C)uu();return this.T=this.T.I(),this}}get index(){let A=this.T,B=this.C.o;if(A===this.C){if(B)return B.M-1;return 0}let Q=0;if(A.i)Q+=A.i.M;while(A!==B){let D=A.o;if(A===D.h){if(Q+=1,D.i)Q+=D.i.M}A=D}return Q}isAccessible(){return this.T!==this.C}}class Q$ extends wa2{constructor(A,B,Q,D){super(A,B,D);this.container=Q}get pointer(){if(this.T===this.C)uu();let A=this;return new Proxy([],{get(B,Q){if(Q==="0")return A.T.u;else if(Q==="1")return A.T.l;return B[0]=A.T.u,B[1]=A.T.l,B[Q]},set(B,Q,D){if(Q!=="1")throw new TypeError("prop must be 1");return A.T.l=D,!0}})}copy(){return new Q$(this.T,this.C,this.container,this.iteratorType)}}class $a2 extends Ua2{constructor(A=[],B,Q){super(B,Q);let D=this;A.forEach(function(Z){D.setElement(Z[0],Z[1])})}begin(){return new Q$(this.C.i||this.C,this.C,this)}end(){return new Q$(this.C,this.C,this)}rBegin(){return new Q$(this.C.h||this.C,this.C,this,1)}rEnd(){return new Q$(this.C,this.C,this,1)}front(){if(this.m===0)return;let A=this.C.i;return[A.u,A.l]}back(){if(this.m===0)return;let A=this.C.h;return[A.u,A.l]}lowerBound(A){let B=this.R(this.v,A);return new Q$(B,this.C,this)}upperBound(A){let B=this.K(this.v,A);return new Q$(B,this.C,this)}reverseLowerBound(A){let B=this.L(this.v,A);return new Q$(B,this.C,this)}reverseUpperBound(A){let B=this.k(this.v,A);return new Q$(B,this.C,this)}forEach(A){this.U(function(B,Q,D){A([B.u,B.l],Q,D)})}setElement(A,B,Q){return this.q(A,B,Q)}getElementByPos(A){if(A<0||A>this.m-1)throw new RangeError;let B=this.U(A);return[B.u,B.l]}find(A){let B=this.H(this.v,A);return new Q$(B,this.C,this)}getElementByKey(A){return this.H(this.v,A).l}union(A){let B=this;return A.forEach(function(Q){B.setElement(Q[0],Q[1])}),this.m}*[Symbol.iterator](){let A=this.m,B=this.U();for(let Q=0;Q<A;++Q){let D=B[Q];yield[D.u,D.l]}}}qa2.OrderedMap=$a2});
var Ne2=E(($e2)=>{Object.defineProperty($e2,"__esModule",{value:!0});$e2.StatusBuilder=void 0;class we2{constructor(){this.code=null,this.details=null,this.metadata=null}withCode(A){return this.code=A,this}withDetails(A){return this.details=A,this}withMetadata(A){return this.metadata=A,this}build(){let A={};if(this.code!==null)A.code=this.code;if(this.details!==null)A.details=this.details;if(this.metadata!==null)A.metadata=this.metadata;return A}}$e2.StatusBuilder=we2});
var Nr2=E((Tu5,qr2)=>{qr2.exports=wP;wP.filename=null;wP.defaults={keepCase:!1};var EK6=MJ0(),Er2=XP1(),Ur2=YP1(),wr2=o_(),UK6=QP1(),$r2=eo(),wK6=Z$(),$K6=ZP1(),qK6=DP1(),RJ0=mu(),OJ0=$I(),NK6=/^[1-9][0-9]*$/,LK6=/^-?[1-9][0-9]*$/,MK6=/^0[x][0-9a-fA-F]+$/,RK6=/^-?0[x][0-9a-fA-F]+$/,OK6=/^0[0-7]+$/,TK6=/^-?0[0-7]+$/,PK6=/^(?![eE])[0-9]*(?:\.[0-9]*)?(?:[eE][+-]?[0-9]+)?$/,FM=/^[a-zA-Z_][a-zA-Z_0-9]*$/,IM=/^(?:\.?[a-zA-Z_][a-zA-Z_0-9]*)(?:\.[a-zA-Z_][a-zA-Z_0-9]*)*$/,SK6=/^(?:\.[a-zA-Z_][a-zA-Z_0-9]*)+$/;function wP(A,B,Q){if(!(B instanceof Er2))Q=B,B=new Er2;if(!Q)Q=wP.defaults;var D=Q.preferTrailingComment||!1,Z=EK6(A,Q.alternateCommentMode||!1),G=Z.next,F=Z.push,I=Z.peek,Y=Z.skip,W=Z.cmnt,J=!0,X,V,C,K,H=!1,z=B,$=Q.keepCase?function(A1){return A1}:OJ0.camelCase;function L(A1,D1,I1){var E1=wP.filename;if(!I1)wP.filename=null;return Error("illegal "+(D1||"token")+" '"+A1+"' ("+(E1?E1+", ":"")+"line "+Z.line+")")}function N(){var A1=[],D1;do{if((D1=G())!=='"'&&D1!=="'")throw L(D1);A1.push(G()),Y(D1),D1=I()}while(D1==='"'||D1==="'");return A1.join("")}function O(A1){var D1=G();switch(D1){case"'":case'"':return F(D1),N();case"true":case"TRUE":return!0;case"false":case"FALSE":return!1}try{return T(D1,!0)}catch(I1){if(A1&&IM.test(D1))return D1;throw L(D1,"value")}}function R(A1,D1){var I1,E1;do if(D1&&((I1=I())==='"'||I1==="'"))A1.push(N());else A1.push([E1=j(G()),Y("to",!0)?j(G()):E1]);while(Y(",",!0));var M1={options:void 0};M1.setOption=function(B1,b1){if(this.options===void 0)this.options={};this.options[B1]=b1},a(M1,function B1(b1){if(b1==="option")X0(M1,b1),Y(";");else throw L(b1)},function B1(){_1(M1)})}function T(A1,D1){var I1=1;if(A1.charAt(0)==="-")I1=-1,A1=A1.substring(1);switch(A1){case"inf":case"INF":case"Inf":return I1*(1/0);case"nan":case"NAN":case"Nan":case"NaN":return NaN;case"0":return 0}if(NK6.test(A1))return I1*parseInt(A1,10);if(MK6.test(A1))return I1*parseInt(A1,16);if(OK6.test(A1))return I1*parseInt(A1,8);if(PK6.test(A1))return I1*parseFloat(A1);throw L(A1,"number",D1)}function j(A1,D1){switch(A1){case"max":case"MAX":case"Max":return 536870911;case"0":return 0}if(!D1&&A1.charAt(0)==="-")throw L(A1,"id");if(LK6.test(A1))return parseInt(A1,10);if(RK6.test(A1))return parseInt(A1,16);if(TK6.test(A1))return parseInt(A1,8);throw L(A1,"id")}function f(){if(X!==void 0)throw L("package");if(X=G(),!IM.test(X))throw L(X,"name");z=z.define(X),Y(";")}function y(){var A1=I(),D1;switch(A1){case"weak":D1=C||(C=[]),G();break;case"public":G();default:D1=V||(V=[]);break}A1=N(),Y(";"),D1.push(A1)}function c(){if(Y("="),K=N(),H=K==="proto3",!H&&K!=="proto2")throw L(K,"syntax");B.setOption("syntax",K),Y(";")}function h(A1,D1){switch(D1){case"option":return X0(A1,D1),Y(";"),!0;case"message":return n(A1,D1),!0;case"enum":return f1(A1,D1),!0;case"service":return q1(A1,D1),!0;case"extend":return K0(A1,D1),!0}return!1}function a(A1,D1,I1){var E1=Z.line;if(A1){if(typeof A1.comment!=="string")A1.comment=W();A1.filename=wP.filename}if(Y("{",!0)){var M1;while((M1=G())!=="}")D1(M1);Y(";",!0)}else{if(I1)I1();if(Y(";"),A1&&(typeof A1.comment!=="string"||D))A1.comment=W(E1)||A1.comment}}function n(A1,D1){if(!FM.test(D1=G()))throw L(D1,"type name");var I1=new Ur2(D1);a(I1,function E1(M1){if(h(I1,M1))return;switch(M1){case"map":W1(I1,M1);break;case"required":case"repeated":v(I1,M1);break;case"optional":if(H)v(I1,"proto3_optional");else v(I1,"optional");break;case"oneof":z1(I1,M1);break;case"extensions":R(I1.extensions||(I1.extensions=[]));break;case"reserved":R(I1.reserved||(I1.reserved=[]),!0);break;default:if(!H||!IM.test(M1))throw L(M1);F(M1),v(I1,"optional");break}}),A1.add(I1)}function v(A1,D1,I1){var E1=G();if(E1==="group"){t(A1,D1);return}while(E1.endsWith(".")||I().startsWith("."))E1+=G();if(!IM.test(E1))throw L(E1,"type");var M1=G();if(!FM.test(M1))throw L(M1,"name");M1=$(M1),Y("=");var B1=new wr2(M1,j(G()),E1,D1,I1);if(a(B1,function c1(n1){if(n1==="option")X0(B1,n1),Y(";");else throw L(n1)},function c1(){_1(B1)}),D1==="proto3_optional"){var b1=new $r2("_"+M1);B1.setOption("proto3_optional",!0),b1.add(B1),A1.add(b1)}else A1.add(B1);if(!H&&B1.repeated&&(RJ0.packed[E1]!==void 0||RJ0.basic[E1]===void 0))B1.setOption("packed",!1,!0)}function t(A1,D1){var I1=G();if(!FM.test(I1))throw L(I1,"name");var E1=OJ0.lcFirst(I1);if(I1===E1)I1=OJ0.ucFirst(I1);Y("=");var M1=j(G()),B1=new Ur2(I1);B1.group=!0;var b1=new wr2(E1,M1,I1,D1);b1.filename=wP.filename,a(B1,function c1(n1){switch(n1){case"option":X0(B1,n1),Y(";");break;case"required":case"repeated":v(B1,n1);break;case"optional":if(H)v(B1,"proto3_optional");else v(B1,"optional");break;case"message":n(B1,n1);break;case"enum":f1(B1,n1);break;default:throw L(n1)}}),A1.add(B1).add(b1)}function W1(A1){Y("<");var D1=G();if(RJ0.mapKey[D1]===void 0)throw L(D1,"type");Y(",");var I1=G();if(!IM.test(I1))throw L(I1,"type");Y(">");var E1=G();if(!FM.test(E1))throw L(E1,"name");Y("=");var M1=new UK6($(E1),j(G()),D1,I1);a(M1,function B1(b1){if(b1==="option")X0(M1,b1),Y(";");else throw L(b1)},function B1(){_1(M1)}),A1.add(M1)}function z1(A1,D1){if(!FM.test(D1=G()))throw L(D1,"name");var I1=new $r2($(D1));a(I1,function E1(M1){if(M1==="option")X0(I1,M1),Y(";");else F(M1),v(I1,"optional")}),A1.add(I1)}function f1(A1,D1){if(!FM.test(D1=G()))throw L(D1,"name");var I1=new wK6(D1);a(I1,function E1(M1){switch(M1){case"option":X0(I1,M1),Y(";");break;case"reserved":R(I1.reserved||(I1.reserved=[]),!0);break;default:G0(I1,M1)}}),A1.add(I1)}function G0(A1,D1){if(!FM.test(D1))throw L(D1,"name");Y("=");var I1=j(G(),!0),E1={options:void 0};E1.setOption=function(M1,B1){if(this.options===void 0)this.options={};this.options[M1]=B1},a(E1,function M1(B1){if(B1==="option")X0(E1,B1),Y(";");else throw L(B1)},function M1(){_1(E1)}),A1.add(D1,I1,E1.comment,E1.options)}function X0(A1,D1){var I1=Y("(",!0);if(!IM.test(D1=G()))throw L(D1,"name");var E1=D1,M1=E1,B1;if(I1){if(Y(")"),E1="("+E1+")",M1=E1,D1=I(),SK6.test(D1))B1=D1.slice(1),E1+=D1,G()}Y("=");var b1=g1(A1,E1);Q1(A1,M1,b1,B1)}function g1(A1,D1){if(Y("{",!0)){var I1={};while(!Y("}",!0)){if(!FM.test(s1=G()))throw L(s1,"name");if(s1===null)throw L(s1,"end of input");var E1,M1=s1;if(Y(":",!0),I()==="{")E1=g1(A1,D1+"."+s1);else if(I()==="["){E1=[];var B1;if(Y("[",!0)){do B1=O(!0),E1.push(B1);while(Y(",",!0));if(Y("]"),typeof B1!=="undefined")K1(A1,D1+"."+s1,B1)}}else E1=O(!0),K1(A1,D1+"."+s1,E1);var b1=I1[M1];if(b1)E1=[].concat(b1).concat(E1);I1[M1]=E1,Y(",",!0),Y(";",!0)}return I1}var c1=O(!0);return K1(A1,D1,c1),c1}function K1(A1,D1,I1){if(A1.setOption)A1.setOption(D1,I1)}function Q1(A1,D1,I1,E1){if(A1.setParsedOption)A1.setParsedOption(D1,I1,E1)}function _1(A1){if(Y("[",!0)){do X0(A1,"option");while(Y(",",!0));Y("]")}return A1}function q1(A1,D1){if(!FM.test(D1=G()))throw L(D1,"service name");var I1=new $K6(D1);a(I1,function E1(M1){if(h(I1,M1))return;if(M1==="rpc")B0(I1,M1);else throw L(M1)}),A1.add(I1)}function B0(A1,D1){var I1=W(),E1=D1;if(!FM.test(D1=G()))throw L(D1,"name");var M1=D1,B1,b1,c1,n1;if(Y("("),Y("stream",!0))b1=!0;if(!IM.test(D1=G()))throw L(D1);if(B1=D1,Y(")"),Y("returns"),Y("("),Y("stream",!0))n1=!0;if(!IM.test(D1=G()))throw L(D1);c1=D1,Y(")");var C0=new qK6(M1,E1,B1,c1,b1,n1);C0.comment=I1,a(C0,function W0(O0){if(O0==="option")X0(C0,O0),Y(";");else throw L(O0)}),A1.add(C0)}function K0(A1,D1){if(!IM.test(D1=G()))throw L(D1,"reference");var I1=D1;a(null,function E1(M1){switch(M1){case"required":case"repeated":v(A1,M1,I1);break;case"optional":if(H)v(A1,"proto3_optional",I1);else v(A1,"optional",I1);break;default:if(!H||!IM.test(M1))throw L(M1);F(M1),v(A1,"optional",I1);break}})}var s1;while((s1=G())!==null)switch(s1){case"package":if(!J)throw L(s1);f();break;case"import":if(!J)throw L(s1);y();break;case"syntax":if(!J)throw L(s1);c();break;case"option":X0(z,s1),Y(";");break;default:if(h(z,s1)){J=!1;continue}throw L(s1)}return wP.filename=null,{package:X,imports:V,weakImports:C,syntax:K,root:B}}});
var PW0=E((Tn2)=>{Object.defineProperty(Tn2,"__esModule",{value:!0});Tn2.CIPHER_SUITES=void 0;Tn2.getDefaultRootsData=EJ6;var zJ6=J1("fs");Tn2.CIPHER_SUITES=process.env.GRPC_SSL_CIPHER_SUITES;var On2=process.env.GRPC_DEFAULT_SSL_ROOTS_FILE_PATH,TW0=null;function EJ6(){if(On2){if(TW0===null)TW0=zJ6.readFileSync(On2);return TW0}return null}});
var PX0=E((I8)=>{Object.defineProperty(I8,"__esModule",{value:!0});I8.SUBCHANNEL_ARGS_EXCLUDE_KEY_PREFIX=I8.createCertificateProviderChannelCredentials=I8.FileWatcherCertificateProvider=I8.createCertificateProviderServerCredentials=I8.createServerCredentialsWithInterceptors=I8.BaseSubchannelWrapper=I8.registerAdminService=I8.FilterStackFactory=I8.BaseFilter=I8.PickResultType=I8.QueuePicker=I8.UnavailablePicker=I8.ChildLoadBalancerHandler=I8.EndpointMap=I8.endpointHasAddress=I8.endpointToString=I8.subchannelAddressToString=I8.LeafLoadBalancer=I8.isLoadBalancerNameRegistered=I8.parseLoadBalancingConfig=I8.selectLbConfigFromList=I8.registerLoadBalancerType=I8.createChildChannelControlHelper=I8.BackoffTimeout=I8.parseDuration=I8.durationToMs=I8.splitHostPort=I8.uriToString=I8.createResolver=I8.registerResolver=I8.log=I8.trace=void 0;var fe2=F7();Object.defineProperty(I8,"trace",{enumerable:!0,get:function(){return fe2.trace}});Object.defineProperty(I8,"log",{enumerable:!0,get:function(){return fe2.log}});var he2=BM();Object.defineProperty(I8,"registerResolver",{enumerable:!0,get:function(){return he2.registerResolver}});Object.defineProperty(I8,"createResolver",{enumerable:!0,get:function(){return he2.createResolver}});var ge2=hV();Object.defineProperty(I8,"uriToString",{enumerable:!0,get:function(){return ge2.uriToString}});Object.defineProperty(I8,"splitHostPort",{enumerable:!0,get:function(){return ge2.splitHostPort}});var ue2=MX0();Object.defineProperty(I8,"durationToMs",{enumerable:!0,get:function(){return ue2.durationToMs}});Object.defineProperty(I8,"parseDuration",{enumerable:!0,get:function(){return ue2.parseDuration}});var Zw6=q31();Object.defineProperty(I8,"BackoffTimeout",{enumerable:!0,get:function(){return Zw6.BackoffTimeout}});var o31=gu();Object.defineProperty(I8,"createChildChannelControlHelper",{enumerable:!0,get:function(){return o31.createChildChannelControlHelper}});Object.defineProperty(I8,"registerLoadBalancerType",{enumerable:!0,get:function(){return o31.registerLoadBalancerType}});Object.defineProperty(I8,"selectLbConfigFromList",{enumerable:!0,get:function(){return o31.selectLbConfigFromList}});Object.defineProperty(I8,"parseLoadBalancingConfig",{enumerable:!0,get:function(){return o31.parseLoadBalancingConfig}});Object.defineProperty(I8,"isLoadBalancerNameRegistered",{enumerable:!0,get:function(){return o31.isLoadBalancerNameRegistered}});var Gw6=dP1();Object.defineProperty(I8,"LeafLoadBalancer",{enumerable:!0,get:function(){return Gw6.LeafLoadBalancer}});var lP1=UE();Object.defineProperty(I8,"subchannelAddressToString",{enumerable:!0,get:function(){return lP1.subchannelAddressToString}});Object.defineProperty(I8,"endpointToString",{enumerable:!0,get:function(){return lP1.endpointToString}});Object.defineProperty(I8,"endpointHasAddress",{enumerable:!0,get:function(){return lP1.endpointHasAddress}});Object.defineProperty(I8,"EndpointMap",{enumerable:!0,get:function(){return lP1.EndpointMap}});var Fw6=pT1();Object.defineProperty(I8,"ChildLoadBalancerHandler",{enumerable:!0,get:function(){return Fw6.ChildLoadBalancerHandler}});var TX0=s_();Object.defineProperty(I8,"UnavailablePicker",{enumerable:!0,get:function(){return TX0.UnavailablePicker}});Object.defineProperty(I8,"QueuePicker",{enumerable:!0,get:function(){return TX0.QueuePicker}});Object.defineProperty(I8,"PickResultType",{enumerable:!0,get:function(){return TX0.PickResultType}});var Iw6=AX0();Object.defineProperty(I8,"BaseFilter",{enumerable:!0,get:function(){return Iw6.BaseFilter}});var Yw6=tJ0();Object.defineProperty(I8,"FilterStackFactory",{enumerable:!0,get:function(){return Yw6.FilterStackFactory}});var Ww6=nT1();Object.defineProperty(I8,"registerAdminService",{enumerable:!0,get:function(){return Ww6.registerAdminService}});var Jw6=xP1();Object.defineProperty(I8,"BaseSubchannelWrapper",{enumerable:!0,get:function(){return Jw6.BaseSubchannelWrapper}});var me2=hP1();Object.defineProperty(I8,"createServerCredentialsWithInterceptors",{enumerable:!0,get:function(){return me2.createServerCredentialsWithInterceptors}});Object.defineProperty(I8,"createCertificateProviderServerCredentials",{enumerable:!0,get:function(){return me2.createCertificateProviderServerCredentials}});var Xw6=be2();Object.defineProperty(I8,"FileWatcherCertificateProvider",{enumerable:!0,get:function(){return Xw6.FileWatcherCertificateProvider}});var Vw6=w31();Object.defineProperty(I8,"createCertificateProviderChannelCredentials",{enumerable:!0,get:function(){return Vw6.createCertificateProviderChannelCredentials}});var Cw6=IX0();Object.defineProperty(I8,"SUBCHANNEL_ARGS_EXCLUDE_KEY_PREFIX",{enumerable:!0,get:function(){return Cw6.SUBCHANNEL_ARGS_EXCLUDE_KEY_PREFIX}})});
var Ps2=E((Ts2)=>{var eW0=Ts2,Os2=eW0.isAbsolute=function A(B){return/^(?:\/|\w+:)/.test(B)},tW0=eW0.normalize=function A(B){B=B.replace(/\\/g,"/").replace(/\/{2,}/g,"/");var Q=B.split("/"),D=Os2(B),Z="";if(D)Z=Q.shift()+"/";for(var G=0;G<Q.length;)if(Q[G]==="..")if(G>0&&Q[G-1]!=="..")Q.splice(--G,2);else if(D)Q.splice(G,1);else++G;else if(Q[G]===".")Q.splice(G,1);else++G;return Z+Q.join("/")};eW0.resolve=function A(B,Q,D){if(!D)Q=tW0(Q);if(Os2(Q))return Q;if(!D)B=tW0(B);return(B=B.replace(/(?:\/|^)[^/]+$/,"")).length?tW0(B+"/"+Q):Q}});
var QP1=E((Xu5,us2)=>{us2.exports=EP;var QJ0=o_();((EP.prototype=Object.create(QJ0.prototype)).constructor=EP).className="MapField";var vC6=mu(),_31=$I();function EP(A,B,Q,D,Z,G){if(QJ0.call(this,A,B,D,void 0,void 0,Z,G),!_31.isString(Q))throw TypeError("keyType must be a string");this.keyType=Q,this.resolvedKeyType=null,this.map=!0}EP.fromJSON=function A(B,Q){return new EP(B,Q.id,Q.keyType,Q.type,Q.options,Q.comment)};EP.prototype.toJSON=function A(B){var Q=B?Boolean(B.keepComments):!1;return _31.toObject(["keyType",this.keyType,"type",this.type,"id",this.id,"extend",this.extend,"options",this.options,"comment",Q?this.comment:void 0])};EP.prototype.resolve=function A(){if(this.resolved)return this;if(vC6.mapKey[this.keyType]===void 0)throw Error("invalid key type: "+this.keyType);return QJ0.prototype.resolve.call(this)};EP.d=function A(B,Q,D){if(typeof D==="function")D=_31.decorateType(D).name;else if(D&&typeof D==="object")D=_31.decorateEnum(D).name;return function Z(G,F){_31.decorateType(G.constructor).add(new EP(F,B,Q,D))}}});
var Qt=E((Ju5,gs2)=>{gs2.exports=r8;var BJ0=du();((r8.prototype=Object.create(BJ0.prototype)).constructor=r8).className="Namespace";var bs2=o_(),BP1=$I(),xC6=eo(),At,k31,Bt;r8.fromJSON=function A(B,Q){return new r8(B,Q.options).addJSON(Q.nested)};function fs2(A,B){if(!(A&&A.length))return;var Q={};for(var D=0;D<A.length;++D)Q[A[D].name]=A[D].toJSON(B);return Q}r8.arrayToJSON=fs2;r8.isReservedId=function A(B,Q){if(B){for(var D=0;D<B.length;++D)if(typeof B[D]!=="string"&&B[D][0]<=Q&&B[D][1]>Q)return!0}return!1};r8.isReservedName=function A(B,Q){if(B){for(var D=0;D<B.length;++D)if(B[D]===Q)return!0}return!1};function r8(A,B){BJ0.call(this,A,B),this.nested=void 0,this._nestedArray=null}function hs2(A){return A._nestedArray=null,A}Object.defineProperty(r8.prototype,"nestedArray",{get:function(){return this._nestedArray||(this._nestedArray=BP1.toArray(this.nested))}});r8.prototype.toJSON=function A(B){return BP1.toObject(["options",this.options,"nested",fs2(this.nestedArray,B)])};r8.prototype.addJSON=function A(B){var Q=this;if(B)for(var D=Object.keys(B),Z=0,G;Z<D.length;++Z)G=B[D[Z]],Q.add((G.fields!==void 0?At.fromJSON:G.values!==void 0?Bt.fromJSON:G.methods!==void 0?k31.fromJSON:G.id!==void 0?bs2.fromJSON:r8.fromJSON)(D[Z],G));return this};r8.prototype.get=function A(B){return this.nested&&this.nested[B]||null};r8.prototype.getEnum=function A(B){if(this.nested&&this.nested[B]instanceof Bt)return this.nested[B].values;throw Error("no such enum: "+B)};r8.prototype.add=function A(B){if(!(B instanceof bs2&&B.extend!==void 0||B instanceof At||B instanceof xC6||B instanceof Bt||B instanceof k31||B instanceof r8))throw TypeError("object must be a valid nested object");if(!this.nested)this.nested={};else{var Q=this.get(B.name);if(Q)if(Q instanceof r8&&B instanceof r8&&!(Q instanceof At||Q instanceof k31)){var D=Q.nestedArray;for(var Z=0;Z<D.length;++Z)B.add(D[Z]);if(this.remove(Q),!this.nested)this.nested={};B.setOptions(Q.options,!0)}else throw Error("duplicate name '"+B.name+"' in "+this)}return this.nested[B.name]=B,B.onAdd(this),hs2(this)};r8.prototype.remove=function A(B){if(!(B instanceof BJ0))throw TypeError("object must be a ReflectionObject");if(B.parent!==this)throw Error(B+" is not a member of "+this);if(delete this.nested[B.name],!Object.keys(this.nested).length)this.nested=void 0;return B.onRemove(this),hs2(this)};r8.prototype.define=function A(B,Q){if(BP1.isString(B))B=B.split(".");else if(!Array.isArray(B))throw TypeError("illegal path");if(B&&B.length&&B[0]==="")throw Error("path must be relative");var D=this;while(B.length>0){var Z=B.shift();if(D.nested&&D.nested[Z]){if(D=D.nested[Z],!(D instanceof r8))throw Error("path conflicts with non-namespace objects")}else D.add(D=new r8(Z))}if(Q)D.addJSON(Q);return D};r8.prototype.resolveAll=function A(){var B=this.nestedArray,Q=0;while(Q<B.length)if(B[Q]instanceof r8)B[Q++].resolveAll();else B[Q++].resolve();return this.resolve()};r8.prototype.lookup=function A(B,Q,D){if(typeof Q==="boolean")D=Q,Q=void 0;else if(Q&&!Array.isArray(Q))Q=[Q];if(BP1.isString(B)&&B.length){if(B===".")return this.root;B=B.split(".")}else if(!B.length)return this;if(B[0]==="")return this.root.lookup(B.slice(1),Q);var Z=this.get(B[0]);if(Z){if(B.length===1){if(!Q||Q.indexOf(Z.constructor)>-1)return Z}else if(Z instanceof r8&&(Z=Z.lookup(B.slice(1),Q,!0)))return Z}else for(var G=0;G<this.nestedArray.length;++G)if(this._nestedArray[G]instanceof r8&&(Z=this._nestedArray[G].lookup(B,Q,!0)))return Z;if(this.parent===null||D)return null;return this.parent.lookup(B,Q)};r8.prototype.lookupType=function A(B){var Q=this.lookup(B,[At]);if(!Q)throw Error("no such type: "+B);return Q};r8.prototype.lookupEnum=function A(B){var Q=this.lookup(B,[Bt]);if(!Q)throw Error("no such Enum '"+B+"' in "+this);return Q};r8.prototype.lookupTypeOrEnum=function A(B){var Q=this.lookup(B,[At,Bt]);if(!Q)throw Error("no such Type or Enum '"+B+"' in "+this);return Q};r8.prototype.lookupService=function A(B){var Q=this.lookup(B,[k31]);if(!Q)throw Error("no such Service '"+B+"' in "+this);return Q};r8._configure=function(A,B,Q){At=A,k31=B,Bt=Q}});
var Rr2=E((Pu5,Mr2)=>{Mr2.exports=F$;var jK6=/\/|\./;function F$(A,B){if(!jK6.test(A))A="google/protobuf/"+A+".proto",B={nested:{google:{nested:{protobuf:{nested:B}}}}};F$[A]=B}F$("any",{Any:{fields:{type_url:{type:"string",id:1},value:{type:"bytes",id:2}}}});var Lr2;F$("duration",{Duration:Lr2={fields:{seconds:{type:"int64",id:1},nanos:{type:"int32",id:2}}}});F$("timestamp",{Timestamp:Lr2});F$("empty",{Empty:{fields:{}}});F$("struct",{Struct:{fields:{fields:{keyType:"string",type:"Value",id:1}}},Value:{oneofs:{kind:{oneof:["nullValue","numberValue","stringValue","boolValue","structValue","listValue"]}},fields:{nullValue:{type:"NullValue",id:1},numberValue:{type:"double",id:2},stringValue:{type:"string",id:3},boolValue:{type:"bool",id:4},structValue:{type:"Struct",id:5},listValue:{type:"ListValue",id:6}}},NullValue:{values:{NULL_VALUE:0}},ListValue:{fields:{values:{rule:"repeated",type:"Value",id:1}}}});F$("wrappers",{DoubleValue:{fields:{value:{type:"double",id:1}}},FloatValue:{fields:{value:{type:"float",id:1}}},Int64Value:{fields:{value:{type:"int64",id:1}}},UInt64Value:{fields:{value:{type:"uint64",id:1}}},Int32Value:{fields:{value:{type:"int32",id:1}}},UInt32Value:{fields:{value:{type:"uint32",id:1}}},BoolValue:{fields:{value:{type:"bool",id:1}}},StringValue:{fields:{value:{type:"string",id:1}}},BytesValue:{fields:{value:{type:"bytes",id:1}}}});F$("field_mask",{FieldMask:{fields:{paths:{rule:"repeated",type:"string",id:1}}}});F$.get=function A(B){return F$[B]||null}});
var Rs2=E((Gu5,Ms2)=>{Ms2.exports=S31;var SC6=vY0(),jC6=fY0(),oW0=jC6("fs");function S31(A,B,Q){if(typeof B==="function")Q=B,B={};else if(!B)B={};if(!Q)return SC6(S31,this,A,B);if(!B.xhr&&oW0&&oW0.readFile)return oW0.readFile(A,function D(Z,G){return Z&&typeof XMLHttpRequest!=="undefined"?S31.xhr(A,B,Q):Z?Q(Z):Q(null,B.binary?G:G.toString("utf8"))});return S31.xhr(A,B,Q)}S31.xhr=function A(B,Q,D){var Z=new XMLHttpRequest;if(Z.onreadystatechange=function G(){if(Z.readyState!==4)return;if(Z.status!==0&&Z.status!==200)return D(Error("status "+Z.status));if(Q.binary){var F=Z.response;if(!F){F=[];for(var I=0;I<Z.responseText.length;++I)F.push(Z.responseText.charCodeAt(I)&255)}return D(null,typeof Uint8Array!=="undefined"?new Uint8Array(F):F)}return D(null,Z.responseText)},Q.binary){if("overrideMimeType"in Z)Z.overrideMimeType("text/plain; charset=x-user-defined");Z.responseType="arraybuffer"}Z.open("GET",B),Z.send()}});
var TJ0=E((ju5,yK6)=>{yK6.exports={nested:{google:{nested:{protobuf:{nested:{FileDescriptorSet:{fields:{file:{rule:"repeated",type:"FileDescriptorProto",id:1}}},FileDescriptorProto:{fields:{name:{type:"string",id:1},package:{type:"string",id:2},dependency:{rule:"repeated",type:"string",id:3},publicDependency:{rule:"repeated",type:"int32",id:10,options:{packed:!1}},weakDependency:{rule:"repeated",type:"int32",id:11,options:{packed:!1}},messageType:{rule:"repeated",type:"DescriptorProto",id:4},enumType:{rule:"repeated",type:"EnumDescriptorProto",id:5},service:{rule:"repeated",type:"ServiceDescriptorProto",id:6},extension:{rule:"repeated",type:"FieldDescriptorProto",id:7},options:{type:"FileOptions",id:8},sourceCodeInfo:{type:"SourceCodeInfo",id:9},syntax:{type:"string",id:12}}},DescriptorProto:{fields:{name:{type:"string",id:1},field:{rule:"repeated",type:"FieldDescriptorProto",id:2},extension:{rule:"repeated",type:"FieldDescriptorProto",id:6},nestedType:{rule:"repeated",type:"DescriptorProto",id:3},enumType:{rule:"repeated",type:"EnumDescriptorProto",id:4},extensionRange:{rule:"repeated",type:"ExtensionRange",id:5},oneofDecl:{rule:"repeated",type:"OneofDescriptorProto",id:8},options:{type:"MessageOptions",id:7},reservedRange:{rule:"repeated",type:"ReservedRange",id:9},reservedName:{rule:"repeated",type:"string",id:10}},nested:{ExtensionRange:{fields:{start:{type:"int32",id:1},end:{type:"int32",id:2}}},ReservedRange:{fields:{start:{type:"int32",id:1},end:{type:"int32",id:2}}}}},FieldDescriptorProto:{fields:{name:{type:"string",id:1},number:{type:"int32",id:3},label:{type:"Label",id:4},type:{type:"Type",id:5},typeName:{type:"string",id:6},extendee:{type:"string",id:2},defaultValue:{type:"string",id:7},oneofIndex:{type:"int32",id:9},jsonName:{type:"string",id:10},options:{type:"FieldOptions",id:8}},nested:{Type:{values:{TYPE_DOUBLE:1,TYPE_FLOAT:2,TYPE_INT64:3,TYPE_UINT64:4,TYPE_INT32:5,TYPE_FIXED64:6,TYPE_FIXED32:7,TYPE_BOOL:8,TYPE_STRING:9,TYPE_GROUP:10,TYPE_MESSAGE:11,TYPE_BYTES:12,TYPE_UINT32:13,TYPE_ENUM:14,TYPE_SFIXED32:15,TYPE_SFIXED64:16,TYPE_SINT32:17,TYPE_SINT64:18}},Label:{values:{LABEL_OPTIONAL:1,LABEL_REQUIRED:2,LABEL_REPEATED:3}}}},OneofDescriptorProto:{fields:{name:{type:"string",id:1},options:{type:"OneofOptions",id:2}}},EnumDescriptorProto:{fields:{name:{type:"string",id:1},value:{rule:"repeated",type:"EnumValueDescriptorProto",id:2},options:{type:"EnumOptions",id:3}}},EnumValueDescriptorProto:{fields:{name:{type:"string",id:1},number:{type:"int32",id:2},options:{type:"EnumValueOptions",id:3}}},ServiceDescriptorProto:{fields:{name:{type:"string",id:1},method:{rule:"repeated",type:"MethodDescriptorProto",id:2},options:{type:"ServiceOptions",id:3}}},MethodDescriptorProto:{fields:{name:{type:"string",id:1},inputType:{type:"string",id:2},outputType:{type:"string",id:3},options:{type:"MethodOptions",id:4},clientStreaming:{type:"bool",id:5},serverStreaming:{type:"bool",id:6}}},FileOptions:{fields:{javaPackage:{type:"string",id:1},javaOuterClassname:{type:"string",id:8},javaMultipleFiles:{type:"bool",id:10},javaGenerateEqualsAndHash:{type:"bool",id:20,options:{deprecated:!0}},javaStringCheckUtf8:{type:"bool",id:27},optimizeFor:{type:"OptimizeMode",id:9,options:{default:"SPEED"}},goPackage:{type:"string",id:11},ccGenericServices:{type:"bool",id:16},javaGenericServices:{type:"bool",id:17},pyGenericServices:{type:"bool",id:18},deprecated:{type:"bool",id:23},ccEnableArenas:{type:"bool",id:31},objcClassPrefix:{type:"string",id:36},csharpNamespace:{type:"string",id:37},uninterpretedOption:{rule:"repeated",type:"UninterpretedOption",id:999}},extensions:[[1000,536870911]],reserved:[[38,38]],nested:{OptimizeMode:{values:{SPEED:1,CODE_SIZE:2,LITE_RUNTIME:3}}}},MessageOptions:{fields:{messageSetWireFormat:{type:"bool",id:1},noStandardDescriptorAccessor:{type:"bool",id:2},deprecated:{type:"bool",id:3},mapEntry:{type:"bool",id:7},uninterpretedOption:{rule:"repeated",type:"UninterpretedOption",id:999}},extensions:[[1000,536870911]],reserved:[[8,8]]},FieldOptions:{fields:{ctype:{type:"CType",id:1,options:{default:"STRING"}},packed:{type:"bool",id:2},jstype:{type:"JSType",id:6,options:{default:"JS_NORMAL"}},lazy:{type:"bool",id:5},deprecated:{type:"bool",id:3},weak:{type:"bool",id:10},uninterpretedOption:{rule:"repeated",type:"UninterpretedOption",id:999}},extensions:[[1000,536870911]],reserved:[[4,4]],nested:{CType:{values:{STRING:0,CORD:1,STRING_PIECE:2}},JSType:{values:{JS_NORMAL:0,JS_STRING:1,JS_NUMBER:2}}}},OneofOptions:{fields:{uninterpretedOption:{rule:"repeated",type:"UninterpretedOption",id:999}},extensions:[[1000,536870911]]},EnumOptions:{fields:{allowAlias:{type:"bool",id:2},deprecated:{type:"bool",id:3},uninterpretedOption:{rule:"repeated",type:"UninterpretedOption",id:999}},extensions:[[1000,536870911]]},EnumValueOptions:{fields:{deprecated:{type:"bool",id:1},uninterpretedOption:{rule:"repeated",type:"UninterpretedOption",id:999}},extensions:[[1000,536870911]]},ServiceOptions:{fields:{deprecated:{type:"bool",id:33},uninterpretedOption:{rule:"repeated",type:"UninterpretedOption",id:999}},extensions:[[1000,536870911]]},MethodOptions:{fields:{deprecated:{type:"bool",id:33},uninterpretedOption:{rule:"repeated",type:"UninterpretedOption",id:999}},extensions:[[1000,536870911]]},UninterpretedOption:{fields:{name:{rule:"repeated",type:"NamePart",id:2},identifierValue:{type:"string",id:3},positiveIntValue:{type:"uint64",id:4},negativeIntValue:{type:"int64",id:5},doubleValue:{type:"double",id:6},stringValue:{type:"bytes",id:7},aggregateValue:{type:"string",id:8}},nested:{NamePart:{fields:{namePart:{rule:"required",type:"string",id:1},isExtension:{rule:"required",type:"bool",id:2}}}}},SourceCodeInfo:{fields:{location:{rule:"repeated",type:"Location",id:1}},nested:{Location:{fields:{path:{rule:"repeated",type:"int32",id:1},span:{rule:"repeated",type:"int32",id:2},leadingComments:{type:"string",id:3},trailingComments:{type:"string",id:4},leadingDetachedComments:{rule:"repeated",type:"string",id:6}}}}},GeneratedCodeInfo:{fields:{annotation:{rule:"repeated",type:"Annotation",id:1}},nested:{Annotation:{fields:{path:{rule:"repeated",type:"int32",id:1},sourceFile:{type:"string",id:2},begin:{type:"int32",id:3},end:{type:"int32",id:4}}}}}}}}}}}});
var Tt2=E((Rt2)=>{Object.defineProperty(Rt2,"__esModule",{value:!0});Rt2.RetryingCall=Rt2.MessageBufferTracker=Rt2.RetryThrottler=void 0;var _P1=_6(),TE6=i31(),PE6=GJ(),SE6=F7(),jE6="retrying_call";class Nt2{constructor(A,B,Q){if(this.maxTokens=A,this.tokenRatio=B,Q)this.tokens=Q.tokens*(A/Q.maxTokens);else this.tokens=A}addCallSucceeded(){this.tokens=Math.min(this.tokens+this.tokenRatio,this.maxTokens)}addCallFailed(){this.tokens=Math.max(this.tokens-1,0)}canRetryCall(){return this.tokens>this.maxTokens/2}}Rt2.RetryThrottler=Nt2;class Lt2{constructor(A,B){this.totalLimit=A,this.limitPerCall=B,this.totalAllocated=0,this.allocatedPerCall=new Map}allocate(A,B){var Q;let D=(Q=this.allocatedPerCall.get(B))!==null&&Q!==void 0?Q:0;if(this.limitPerCall-D<A||this.totalLimit-this.totalAllocated<A)return!1;return this.allocatedPerCall.set(B,D+A),this.totalAllocated+=A,!0}free(A,B){var Q;if(this.totalAllocated<A)throw new Error(`Invalid buffer allocation state: call ${B} freed ${A} > total allocated ${this.totalAllocated}`);this.totalAllocated-=A;let D=(Q=this.allocatedPerCall.get(B))!==null&&Q!==void 0?Q:0;if(D<A)throw new Error(`Invalid buffer allocation state: call ${B} freed ${A} > allocated for call ${D}`);this.allocatedPerCall.set(B,D-A)}freeAll(A){var B;let Q=(B=this.allocatedPerCall.get(A))!==null&&B!==void 0?B:0;if(this.totalAllocated<Q)throw new Error(`Invalid buffer allocation state: call ${A} allocated ${Q} > total allocated ${this.totalAllocated}`);this.totalAllocated-=Q,this.allocatedPerCall.delete(A)}}Rt2.MessageBufferTracker=Lt2;var DX0="grpc-previous-rpc-attempts",yE6=5;class Mt2{constructor(A,B,Q,D,Z,G,F,I,Y){var W;this.channel=A,this.callConfig=B,this.methodName=Q,this.host=D,this.credentials=Z,this.deadline=G,this.callNumber=F,this.bufferTracker=I,this.retryThrottler=Y,this.listener=null,this.initialMetadata=null,this.underlyingCalls=[],this.writeBuffer=[],this.writeBufferOffset=0,this.readStarted=!1,this.transparentRetryUsed=!1,this.attempts=0,this.hedgingTimer=null,this.committedCallIndex=null,this.initialRetryBackoffSec=0,this.nextRetryBackoffSec=0;let J=(W=A.getOptions()["grpc-node.retry_max_attempts_limit"])!==null&&W!==void 0?W:yE6;if(A.getOptions()["grpc.enable_retries"]===0)this.state="NO_RETRY",this.maxAttempts=1;else if(B.methodConfig.retryPolicy){this.state="RETRY";let X=B.methodConfig.retryPolicy;this.nextRetryBackoffSec=this.initialRetryBackoffSec=Number(X.initialBackoff.substring(0,X.initialBackoff.length-1)),this.maxAttempts=Math.min(X.maxAttempts,J)}else if(B.methodConfig.hedgingPolicy)this.state="HEDGING",this.maxAttempts=Math.min(B.methodConfig.hedgingPolicy.maxAttempts,J);else this.state="TRANSPARENT_ONLY",this.maxAttempts=1;this.startTime=new Date}getDeadlineInfo(){if(this.underlyingCalls.length===0)return[];let A=[],B=this.underlyingCalls[this.underlyingCalls.length-1];if(this.underlyingCalls.length>1)A.push(`previous attempts: ${this.underlyingCalls.length-1}`);if(B.startTime>this.startTime)A.push(`time to current attempt start: ${TE6.formatDateDifference(this.startTime,B.startTime)}`);return A.push(...B.call.getDeadlineInfo()),A}getCallNumber(){return this.callNumber}trace(A){SE6.trace(_P1.LogVerbosity.DEBUG,jE6,"["+this.callNumber+"] "+A)}reportStatus(A){this.trace("ended with status: code="+A.code+' details="'+A.details+'" start time='+this.startTime.toISOString()),this.bufferTracker.freeAll(this.callNumber),this.writeBufferOffset=this.writeBufferOffset+this.writeBuffer.length,this.writeBuffer=[],process.nextTick(()=>{var B;(B=this.listener)===null||B===void 0||B.onReceiveStatus({code:A.code,details:A.details,metadata:A.metadata})})}cancelWithStatus(A,B){this.trace("cancelWithStatus code: "+A+' details: "'+B+'"'),this.reportStatus({code:A,details:B,metadata:new PE6.Metadata});for(let{call:Q}of this.underlyingCalls)Q.cancelWithStatus(A,B)}getPeer(){if(this.committedCallIndex!==null)return this.underlyingCalls[this.committedCallIndex].call.getPeer();else return"unknown"}getBufferEntry(A){var B;return(B=this.writeBuffer[A-this.writeBufferOffset])!==null&&B!==void 0?B:{entryType:"FREED",allocated:!1}}getNextBufferIndex(){return this.writeBufferOffset+this.writeBuffer.length}clearSentMessages(){if(this.state!=="COMMITTED")return;let A;if(this.underlyingCalls[this.committedCallIndex].state==="COMPLETED")A=this.getNextBufferIndex();else A=this.underlyingCalls[this.committedCallIndex].nextMessageToSend;for(let B=this.writeBufferOffset;B<A;B++){let Q=this.getBufferEntry(B);if(Q.allocated)this.bufferTracker.free(Q.message.message.length,this.callNumber)}this.writeBuffer=this.writeBuffer.slice(A-this.writeBufferOffset),this.writeBufferOffset=A}commitCall(A){var B,Q;if(this.state==="COMMITTED")return;this.trace("Committing call ["+this.underlyingCalls[A].call.getCallNumber()+"] at index "+A),this.state="COMMITTED",(Q=(B=this.callConfig).onCommitted)===null||Q===void 0||Q.call(B),this.committedCallIndex=A;for(let D=0;D<this.underlyingCalls.length;D++){if(D===A)continue;if(this.underlyingCalls[D].state==="COMPLETED")continue;this.underlyingCalls[D].state="COMPLETED",this.underlyingCalls[D].call.cancelWithStatus(_P1.Status.CANCELLED,"Discarded in favor of other hedged attempt")}this.clearSentMessages()}commitCallWithMostMessages(){if(this.state==="COMMITTED")return;let A=-1,B=-1;for(let[Q,D]of this.underlyingCalls.entries())if(D.state==="ACTIVE"&&D.nextMessageToSend>A)A=D.nextMessageToSend,B=Q;if(B===-1)this.state="TRANSPARENT_ONLY";else this.commitCall(B)}isStatusCodeInList(A,B){return A.some((Q)=>{var D;return Q===B||Q.toString().toLowerCase()===((D=_P1.Status[B])===null||D===void 0?void 0:D.toLowerCase())})}getNextRetryBackoffMs(){var A;let B=(A=this.callConfig)===null||A===void 0?void 0:A.methodConfig.retryPolicy;if(!B)return 0;let Q=Math.random()*this.nextRetryBackoffSec*1000,D=Number(B.maxBackoff.substring(0,B.maxBackoff.length-1));return this.nextRetryBackoffSec=Math.min(this.nextRetryBackoffSec*B.backoffMultiplier,D),Q}maybeRetryCall(A,B){if(this.state!=="RETRY"){B(!1);return}if(this.attempts>=this.maxAttempts){B(!1);return}let Q;if(A===null)Q=this.getNextRetryBackoffMs();else if(A<0){this.state="TRANSPARENT_ONLY",B(!1);return}else Q=A,this.nextRetryBackoffSec=this.initialRetryBackoffSec;setTimeout(()=>{var D,Z;if(this.state!=="RETRY"){B(!1);return}if((Z=(D=this.retryThrottler)===null||D===void 0?void 0:D.canRetryCall())!==null&&Z!==void 0?Z:!0)B(!0),this.attempts+=1,this.startNewAttempt();else this.trace("Retry attempt denied by throttling policy"),B(!1)},Q)}countActiveCalls(){let A=0;for(let B of this.underlyingCalls)if((B===null||B===void 0?void 0:B.state)==="ACTIVE")A+=1;return A}handleProcessedStatus(A,B,Q){var D,Z,G;switch(this.state){case"COMMITTED":case"NO_RETRY":case"TRANSPARENT_ONLY":this.commitCall(B),this.reportStatus(A);break;case"HEDGING":if(this.isStatusCodeInList((D=this.callConfig.methodConfig.hedgingPolicy.nonFatalStatusCodes)!==null&&D!==void 0?D:[],A.code)){(Z=this.retryThrottler)===null||Z===void 0||Z.addCallFailed();let F;if(Q===null)F=0;else if(Q<0){this.state="TRANSPARENT_ONLY",this.commitCall(B),this.reportStatus(A);return}else F=Q;setTimeout(()=>{if(this.maybeStartHedgingAttempt(),this.countActiveCalls()===0)this.commitCall(B),this.reportStatus(A)},F)}else this.commitCall(B),this.reportStatus(A);break;case"RETRY":if(this.isStatusCodeInList(this.callConfig.methodConfig.retryPolicy.retryableStatusCodes,A.code))(G=this.retryThrottler)===null||G===void 0||G.addCallFailed(),this.maybeRetryCall(Q,(F)=>{if(!F)this.commitCall(B),this.reportStatus(A)});else this.commitCall(B),this.reportStatus(A);break}}getPushback(A){let B=A.get("grpc-retry-pushback-ms");if(B.length===0)return null;try{return parseInt(B[0])}catch(Q){return-1}}handleChildStatus(A,B){var Q;if(this.underlyingCalls[B].state==="COMPLETED")return;if(this.trace("state="+this.state+" handling status with progress "+A.progress+" from child ["+this.underlyingCalls[B].call.getCallNumber()+"] in state "+this.underlyingCalls[B].state),this.underlyingCalls[B].state="COMPLETED",A.code===_P1.Status.OK){(Q=this.retryThrottler)===null||Q===void 0||Q.addCallSucceeded(),this.commitCall(B),this.reportStatus(A);return}if(this.state==="NO_RETRY"){this.commitCall(B),this.reportStatus(A);return}if(this.state==="COMMITTED"){this.reportStatus(A);return}let D=this.getPushback(A.metadata);switch(A.progress){case"NOT_STARTED":this.startNewAttempt();break;case"REFUSED":if(this.transparentRetryUsed)this.handleProcessedStatus(A,B,D);else this.transparentRetryUsed=!0,this.startNewAttempt();break;case"DROP":this.commitCall(B),this.reportStatus(A);break;case"PROCESSED":this.handleProcessedStatus(A,B,D);break}}maybeStartHedgingAttempt(){if(this.state!=="HEDGING")return;if(!this.callConfig.methodConfig.hedgingPolicy)return;if(this.attempts>=this.maxAttempts)return;this.attempts+=1,this.startNewAttempt(),this.maybeStartHedgingTimer()}maybeStartHedgingTimer(){var A,B,Q;if(this.hedgingTimer)clearTimeout(this.hedgingTimer);if(this.state!=="HEDGING")return;if(!this.callConfig.methodConfig.hedgingPolicy)return;let D=this.callConfig.methodConfig.hedgingPolicy;if(this.attempts>=this.maxAttempts)return;let Z=(A=D.hedgingDelay)!==null&&A!==void 0?A:"0s",G=Number(Z.substring(0,Z.length-1));this.hedgingTimer=setTimeout(()=>{this.maybeStartHedgingAttempt()},G*1000),(Q=(B=this.hedgingTimer).unref)===null||Q===void 0||Q.call(B)}startNewAttempt(){let A=this.channel.createLoadBalancingCall(this.callConfig,this.methodName,this.host,this.credentials,this.deadline);this.trace("Created child call ["+A.getCallNumber()+"] for attempt "+this.attempts);let B=this.underlyingCalls.length;this.underlyingCalls.push({state:"ACTIVE",call:A,nextMessageToSend:0,startTime:new Date});let Q=this.attempts-1,D=this.initialMetadata.clone();if(Q>0)D.set(DX0,`${Q}`);let Z=!1;if(A.start(D,{onReceiveMetadata:(G)=>{if(this.trace("Received metadata from child ["+A.getCallNumber()+"]"),this.commitCall(B),Z=!0,Q>0)G.set(DX0,`${Q}`);if(this.underlyingCalls[B].state==="ACTIVE")this.listener.onReceiveMetadata(G)},onReceiveMessage:(G)=>{if(this.trace("Received message from child ["+A.getCallNumber()+"]"),this.commitCall(B),this.underlyingCalls[B].state==="ACTIVE")this.listener.onReceiveMessage(G)},onReceiveStatus:(G)=>{if(this.trace("Received status from child ["+A.getCallNumber()+"]"),!Z&&Q>0)G.metadata.set(DX0,`${Q}`);this.handleChildStatus(G,B)}}),this.sendNextChildMessage(B),this.readStarted)A.startRead()}start(A,B){this.trace("start called"),this.listener=B,this.initialMetadata=A,this.attempts+=1,this.startNewAttempt(),this.maybeStartHedgingTimer()}handleChildWriteCompleted(A){var B,Q;let D=this.underlyingCalls[A],Z=D.nextMessageToSend;(Q=(B=this.getBufferEntry(Z)).callback)===null||Q===void 0||Q.call(B),this.clearSentMessages(),D.nextMessageToSend+=1,this.sendNextChildMessage(A)}sendNextChildMessage(A){let B=this.underlyingCalls[A];if(B.state==="COMPLETED")return;if(this.getBufferEntry(B.nextMessageToSend)){let Q=this.getBufferEntry(B.nextMessageToSend);switch(Q.entryType){case"MESSAGE":B.call.sendMessageWithContext({callback:(D)=>{this.handleChildWriteCompleted(A)}},Q.message.message);break;case"HALF_CLOSE":B.nextMessageToSend+=1,B.call.halfClose();break;case"FREED":break}}}sendMessageWithContext(A,B){var Q;this.trace("write() called with message of length "+B.length);let D={message:B,flags:A.flags},Z=this.getNextBufferIndex(),G={entryType:"MESSAGE",message:D,allocated:this.bufferTracker.allocate(B.length,this.callNumber)};if(this.writeBuffer.push(G),G.allocated){(Q=A.callback)===null||Q===void 0||Q.call(A);for(let[F,I]of this.underlyingCalls.entries())if(I.state==="ACTIVE"&&I.nextMessageToSend===Z)I.call.sendMessageWithContext({callback:(Y)=>{this.handleChildWriteCompleted(F)}},B)}else{if(this.commitCallWithMostMessages(),this.committedCallIndex===null)return;let F=this.underlyingCalls[this.committedCallIndex];if(G.callback=A.callback,F.state==="ACTIVE"&&F.nextMessageToSend===Z)F.call.sendMessageWithContext({callback:(I)=>{this.handleChildWriteCompleted(this.committedCallIndex)}},B)}}startRead(){this.trace("startRead called"),this.readStarted=!0;for(let A of this.underlyingCalls)if((A===null||A===void 0?void 0:A.state)==="ACTIVE")A.call.startRead()}halfClose(){this.trace("halfClose called");let A=this.getNextBufferIndex();this.writeBuffer.push({entryType:"HALF_CLOSE",allocated:!1});for(let B of this.underlyingCalls)if((B===null||B===void 0?void 0:B.state)==="ACTIVE"&&B.nextMessageToSend===A)B.nextMessageToSend+=1,B.call.halfClose()}setCredentials(A){throw new Error("Method not implemented.")}getMethod(){return this.methodName}getHost(){return this.host}}Rt2.RetryingCall=Mt2});
var U1B=E((z1B)=>{Object.defineProperty(z1B,"__esModule",{value:!0});z1B.createServiceClientConstructor=void 0;var w$6=e31();function $$6(A,B){let Q={export:{path:A,requestStream:!1,responseStream:!1,requestSerialize:(D)=>{return D},requestDeserialize:(D)=>{return D},responseSerialize:(D)=>{return D},responseDeserialize:(D)=>{return D}}};return w$6.makeGenericClientConstructor(Q,B)}z1B.createServiceClientConstructor=$$6});
var UE=E((Va2)=>{Object.defineProperty(Va2,"__esModule",{value:!0});Va2.EndpointMap=void 0;Va2.isTcpSubchannelAddress=M31;Va2.subchannelAddressEqual=iT1;Va2.subchannelAddressToString=Wa2;Va2.stringToSubchannelAddress=rX6;Va2.endpointEqual=oX6;Va2.endpointToString=tX6;Va2.endpointHasAddress=Ja2;var Ya2=J1("net");function M31(A){return"port"in A}function iT1(A,B){if(!A&&!B)return!0;if(!A||!B)return!1;if(M31(A))return M31(B)&&A.host===B.host&&A.port===B.port;else return!M31(B)&&A.path===B.path}function Wa2(A){if(M31(A))if(Ya2.isIPv6(A.host))return"["+A.host+"]:"+A.port;else return A.host+":"+A.port;else return A.path}var sX6=443;function rX6(A,B){if(Ya2.isIP(A))return{host:A,port:B!==null&&B!==void 0?B:sX6};else return{path:A}}function oX6(A,B){if(A.addresses.length!==B.addresses.length)return!1;for(let Q=0;Q<A.addresses.length;Q++)if(!iT1(A.addresses[Q],B.addresses[Q]))return!1;return!0}function tX6(A){return"["+A.addresses.map(Wa2).join(", ")+"]"}function Ja2(A,B){for(let Q of A.addresses)if(iT1(Q,B))return!0;return!1}function L31(A,B){if(A.addresses.length!==B.addresses.length)return!1;for(let Q of A.addresses){let D=!1;for(let Z of B.addresses)if(iT1(Q,Z)){D=!0;break}if(!D)return!1}return!0}class Xa2{constructor(){this.map=new Set}get size(){return this.map.size}getForSubchannelAddress(A){for(let B of this.map)if(Ja2(B.key,A))return B.value;return}deleteMissing(A){let B=[];for(let Q of this.map){let D=!1;for(let Z of A)if(L31(Z,Q.key))D=!0;if(!D)B.push(Q.value),this.map.delete(Q)}return B}get(A){for(let B of this.map)if(L31(A,B.key))return B.value;return}set(A,B){for(let Q of this.map)if(L31(A,Q.key)){Q.value=B;return}this.map.add({key:A,value:B})}delete(A){for(let B of this.map)if(L31(A,B.key)){this.map.delete(B);return}}has(A){for(let B of this.map)if(L31(A,B.key))return!0;return!1}clear(){this.map.clear()}*keys(){for(let A of this.map)yield A.key}*values(){for(let A of this.map)yield A.value}*entries(){for(let A of this.map)yield[A.key,A.value]}}Va2.EndpointMap=Xa2});
var Ue2=E((Fx)=>{var $U6=Fx&&Fx.__runInitializers||function(A,B,Q){var D=arguments.length>2;for(var Z=0;Z<B.length;Z++)Q=D?B[Z].call(A,Q):B[Z].call(A);return D?Q:void 0},qU6=Fx&&Fx.__esDecorate||function(A,B,Q,D,Z,G){function F($){if($!==void 0&&typeof $!=="function")throw new TypeError("Function expected");return $}var I=D.kind,Y=I==="getter"?"get":I==="setter"?"set":"value",W=!B&&A?D.static?A:A.prototype:null,J=B||(W?Object.getOwnPropertyDescriptor(W,D.name):{}),X,V=!1;for(var C=Q.length-1;C>=0;C--){var K={};for(var H in D)K[H]=H==="access"?{}:D[H];for(var H in D.access)K.access[H]=D.access[H];K.addInitializer=function($){if(V)throw new TypeError("Cannot add initializers after decoration has completed");G.push(F($||null))};var z=Q[C](I==="accessor"?{get:J.get,set:J.set}:J[Y],K);if(I==="accessor"){if(z===void 0)continue;if(z===null||typeof z!=="object")throw new TypeError("Object expected");if(X=F(z.get))J.get=X;if(X=F(z.set))J.set=X;if(X=F(z.init))Z.unshift(X)}else if(X=F(z))if(I==="field")Z.unshift(X);else J[Y]=X}if(W)Object.defineProperty(W,D.name,J);V=!0};Object.defineProperty(Fx,"__esModule",{value:!0});Fx.Server=void 0;var mV=J1("http2"),NU6=J1("util"),XG=_6(),zt=st2(),qX0=hP1(),Ve2=BM(),Ht=F7(),Gx=UE(),I$=hV(),yF=pu(),Ce2=$X0(),Kt=2147483647,NX0=2147483647,LU6=20000,Ke2=2147483647,{HTTP2_HEADER_PATH:He2}=mV.constants,MU6="server",ze2=Buffer.from("max_age");function Ee2(A){Ht.trace(XG.LogVerbosity.DEBUG,"server_call",A)}function RU6(){}function OU6(A){return function(B,Q){return NU6.deprecate(B,A)}}function LX0(A){return{code:XG.Status.UNIMPLEMENTED,details:`The server does not implement the method ${A}`}}function TU6(A,B){let Q=LX0(B);switch(A){case"unary":return(D,Z)=>{Z(Q,null)};case"clientStream":return(D,Z)=>{Z(Q,null)};case"serverStream":return(D)=>{D.emit("error",Q)};case"bidi":return(D)=>{D.emit("error",Q)};default:throw new Error(`Invalid handlerType ${A}`)}}var PU6=(()=>{var A;let B=[],Q;return A=class D{constructor(Z){var G,F,I,Y,W,J;if(this.boundPorts=($U6(this,B),new Map),this.http2Servers=new Map,this.sessionIdleTimeouts=new Map,this.handlers=new Map,this.sessions=new Map,this.started=!1,this.shutdown=!1,this.serverAddressString="null",this.channelzEnabled=!0,this.options=Z!==null&&Z!==void 0?Z:{},this.options["grpc.enable_channelz"]===0)this.channelzEnabled=!1,this.channelzTrace=new yF.ChannelzTraceStub,this.callTracker=new yF.ChannelzCallTrackerStub,this.listenerChildrenTracker=new yF.ChannelzChildrenTrackerStub,this.sessionChildrenTracker=new yF.ChannelzChildrenTrackerStub;else this.channelzTrace=new yF.ChannelzTrace,this.callTracker=new yF.ChannelzCallTracker,this.listenerChildrenTracker=new yF.ChannelzChildrenTracker,this.sessionChildrenTracker=new yF.ChannelzChildrenTracker;if(this.channelzRef=yF.registerChannelzServer("server",()=>this.getChannelzInfo(),this.channelzEnabled),this.channelzTrace.addTrace("CT_INFO","Server created"),this.maxConnectionAgeMs=(G=this.options["grpc.max_connection_age_ms"])!==null&&G!==void 0?G:Kt,this.maxConnectionAgeGraceMs=(F=this.options["grpc.max_connection_age_grace_ms"])!==null&&F!==void 0?F:Kt,this.keepaliveTimeMs=(I=this.options["grpc.keepalive_time_ms"])!==null&&I!==void 0?I:NX0,this.keepaliveTimeoutMs=(Y=this.options["grpc.keepalive_timeout_ms"])!==null&&Y!==void 0?Y:LU6,this.sessionIdleTimeout=(W=this.options["grpc.max_connection_idle_ms"])!==null&&W!==void 0?W:Ke2,this.commonServerOptions={maxSendHeaderBlockLength:Number.MAX_SAFE_INTEGER},"grpc-node.max_session_memory"in this.options)this.commonServerOptions.maxSessionMemory=this.options["grpc-node.max_session_memory"];else this.commonServerOptions.maxSessionMemory=Number.MAX_SAFE_INTEGER;if("grpc.max_concurrent_streams"in this.options)this.commonServerOptions.settings={maxConcurrentStreams:this.options["grpc.max_concurrent_streams"]};this.interceptors=(J=this.options.interceptors)!==null&&J!==void 0?J:[],this.trace("Server constructed")}getChannelzInfo(){return{trace:this.channelzTrace,callTracker:this.callTracker,listenerChildren:this.listenerChildrenTracker.getChildLists(),sessionChildren:this.sessionChildrenTracker.getChildLists()}}getChannelzSessionInfo(Z){var G,F,I;let Y=this.sessions.get(Z),W=Z.socket,J=W.remoteAddress?Gx.stringToSubchannelAddress(W.remoteAddress,W.remotePort):null,X=W.localAddress?Gx.stringToSubchannelAddress(W.localAddress,W.localPort):null,V;if(Z.encrypted){let K=W,H=K.getCipher(),z=K.getCertificate(),$=K.getPeerCertificate();V={cipherSuiteStandardName:(G=H.standardName)!==null&&G!==void 0?G:null,cipherSuiteOtherName:H.standardName?null:H.name,localCertificate:z&&"raw"in z?z.raw:null,remoteCertificate:$&&"raw"in $?$.raw:null}}else V=null;return{remoteAddress:J,localAddress:X,security:V,remoteName:null,streamsStarted:Y.streamTracker.callsStarted,streamsSucceeded:Y.streamTracker.callsSucceeded,streamsFailed:Y.streamTracker.callsFailed,messagesSent:Y.messagesSent,messagesReceived:Y.messagesReceived,keepAlivesSent:Y.keepAlivesSent,lastLocalStreamCreatedTimestamp:null,lastRemoteStreamCreatedTimestamp:Y.streamTracker.lastCallStartedTimestamp,lastMessageSentTimestamp:Y.lastMessageSentTimestamp,lastMessageReceivedTimestamp:Y.lastMessageReceivedTimestamp,localFlowControlWindow:(F=Z.state.localWindowSize)!==null&&F!==void 0?F:null,remoteFlowControlWindow:(I=Z.state.remoteWindowSize)!==null&&I!==void 0?I:null}}trace(Z){Ht.trace(XG.LogVerbosity.DEBUG,MU6,"("+this.channelzRef.id+") "+Z)}keepaliveTrace(Z){Ht.trace(XG.LogVerbosity.DEBUG,"keepalive","("+this.channelzRef.id+") "+Z)}addProtoService(){throw new Error("Not implemented. Use addService() instead")}addService(Z,G){if(Z===null||typeof Z!=="object"||G===null||typeof G!=="object")throw new Error("addService() requires two objects as arguments");let F=Object.keys(Z);if(F.length===0)throw new Error("Cannot add an empty service to a server");F.forEach((I)=>{let Y=Z[I],W;if(Y.requestStream)if(Y.responseStream)W="bidi";else W="clientStream";else if(Y.responseStream)W="serverStream";else W="unary";let J=G[I],X;if(J===void 0&&typeof Y.originalName==="string")J=G[Y.originalName];if(J!==void 0)X=J.bind(G);else X=TU6(W,I);if(this.register(Y.path,X,Y.responseSerialize,Y.requestDeserialize,W)===!1)throw new Error(`Method handler for ${Y.path} already provided.`)})}removeService(Z){if(Z===null||typeof Z!=="object")throw new Error("removeService() requires object as argument");Object.keys(Z).forEach((F)=>{let I=Z[F];this.unregister(I.path)})}bind(Z,G){throw new Error("Not implemented. Use bindAsync() instead")}experimentalRegisterListenerToChannelz(Z){return yF.registerChannelzSocket(Gx.subchannelAddressToString(Z),()=>{return{localAddress:Z,remoteAddress:null,security:null,remoteName:null,streamsStarted:0,streamsSucceeded:0,streamsFailed:0,messagesSent:0,messagesReceived:0,keepAlivesSent:0,lastLocalStreamCreatedTimestamp:null,lastRemoteStreamCreatedTimestamp:null,lastMessageSentTimestamp:null,lastMessageReceivedTimestamp:null,localFlowControlWindow:null,remoteFlowControlWindow:null}},this.channelzEnabled)}experimentalUnregisterListenerFromChannelz(Z){yF.unregisterChannelzRef(Z)}createHttp2Server(Z){let G;if(Z._isSecure()){let F=Z._getConstructorOptions(),I=Z._getSecureContextOptions(),Y=Object.assign(Object.assign(Object.assign(Object.assign({},this.commonServerOptions),F),I),{enableTrace:this.options["grpc-node.tls_enable_trace"]===1}),W=I!==null;this.trace("Initial credentials valid: "+W),G=mV.createSecureServer(Y),G.prependListener("connection",(X)=>{if(!W)this.trace("Dropped connection from "+JSON.stringify(X.address())+" due to unloaded credentials"),X.destroy()}),G.on("secureConnection",(X)=>{X.on("error",(V)=>{this.trace("An incoming TLS connection closed with error: "+V.message)})});let J=(X)=>{if(X){let V=G;try{V.setSecureContext(X)}catch(C){Ht.log(XG.LogVerbosity.ERROR,"Failed to set secure context with error "+C.message),X=null}}W=X!==null,this.trace("Post-update credentials valid: "+W)};Z._addWatcher(J),G.on("close",()=>{Z._removeWatcher(J)})}else G=mV.createServer(this.commonServerOptions);return G.setTimeout(0,RU6),this._setupHandlers(G,Z._getInterceptors()),G}bindOneAddress(Z,G){this.trace("Attempting to bind "+Gx.subchannelAddressToString(Z));let F=this.createHttp2Server(G.credentials);return new Promise((I,Y)=>{let W=(J)=>{this.trace("Failed to bind "+Gx.subchannelAddressToString(Z)+" with error "+J.message),I({port:"port"in Z?Z.port:1,error:J.message})};F.once("error",W),F.listen(Z,()=>{let J=F.address(),X;if(typeof J==="string")X={path:J};else X={host:J.address,port:J.port};let V=this.experimentalRegisterListenerToChannelz(X);this.listenerChildrenTracker.refChild(V),this.http2Servers.set(F,{channelzRef:V,sessions:new Set,ownsChannelzRef:!0}),G.listeningServers.add(F),this.trace("Successfully bound "+Gx.subchannelAddressToString(X)),I({port:"port"in X?X.port:1}),F.removeListener("error",W)})})}async bindManyPorts(Z,G){if(Z.length===0)return{count:0,port:0,errors:[]};if(Gx.isTcpSubchannelAddress(Z[0])&&Z[0].port===0){let F=await this.bindOneAddress(Z[0],G);if(F.error){let I=await this.bindManyPorts(Z.slice(1),G);return Object.assign(Object.assign({},I),{errors:[F.error,...I.errors]})}else{let I=Z.slice(1).map((J)=>Gx.isTcpSubchannelAddress(J)?{host:J.host,port:F.port}:J),Y=await Promise.all(I.map((J)=>this.bindOneAddress(J,G))),W=[F,...Y];return{count:W.filter((J)=>J.error===void 0).length,port:F.port,errors:W.filter((J)=>J.error).map((J)=>J.error)}}}else{let F=await Promise.all(Z.map((I)=>this.bindOneAddress(I,G)));return{count:F.filter((I)=>I.error===void 0).length,port:F[0].port,errors:F.filter((I)=>I.error).map((I)=>I.error)}}}async bindAddressList(Z,G){let F=await this.bindManyPorts(Z,G);if(F.count>0){if(F.count<Z.length)Ht.log(XG.LogVerbosity.INFO,`WARNING Only ${F.count} addresses added out of total ${Z.length} resolved`);return F.port}else{let I=`No address added out of total ${Z.length} resolved`;throw Ht.log(XG.LogVerbosity.ERROR,I),new Error(`${I} errors: [${F.errors.join(",")}]`)}}resolvePort(Z){return new Promise((G,F)=>{let I={onSuccessfulResolution:(W,J,X)=>{I.onSuccessfulResolution=()=>{};let V=[].concat(...W.map((C)=>C.addresses));if(V.length===0){F(new Error(`No addresses resolved for port ${Z}`));return}G(V)},onError:(W)=>{F(new Error(W.details))}};Ve2.createResolver(Z,I,this.options).updateResolution()})}async bindPort(Z,G){let F=await this.resolvePort(Z);if(G.cancelled)throw this.completeUnbind(G),new Error("bindAsync operation cancelled by unbind call");let I=await this.bindAddressList(F,G);if(G.cancelled)throw this.completeUnbind(G),new Error("bindAsync operation cancelled by unbind call");return I}normalizePort(Z){let G=I$.parseUri(Z);if(G===null)throw new Error(`Could not parse port "${Z}"`);let F=Ve2.mapUriDefaultScheme(G);if(F===null)throw new Error(`Could not get a default scheme for port "${Z}"`);return F}bindAsync(Z,G,F){if(this.shutdown)throw new Error("bindAsync called after shutdown");if(typeof Z!=="string")throw new TypeError("port must be a string");if(G===null||!(G instanceof qX0.ServerCredentials))throw new TypeError("creds must be a ServerCredentials object");if(typeof F!=="function")throw new TypeError("callback must be a function");this.trace("bindAsync port="+Z);let I=this.normalizePort(Z),Y=(V,C)=>{process.nextTick(()=>F(V,C))},W=this.boundPorts.get(I$.uriToString(I));if(W){if(!G._equals(W.credentials)){Y(new Error(`${Z} already bound with incompatible credentials`),0);return}if(W.cancelled=!1,W.completionPromise)W.completionPromise.then((V)=>F(null,V),(V)=>F(V,0));else Y(null,W.portNumber);return}W={mapKey:I$.uriToString(I),originalUri:I,completionPromise:null,cancelled:!1,portNumber:0,credentials:G,listeningServers:new Set};let J=I$.splitHostPort(I.path),X=this.bindPort(I,W);if(W.completionPromise=X,(J===null||J===void 0?void 0:J.port)===0)X.then((V)=>{let C={scheme:I.scheme,authority:I.authority,path:I$.combineHostPort({host:J.host,port:V})};W.mapKey=I$.uriToString(C),W.completionPromise=null,W.portNumber=V,this.boundPorts.set(W.mapKey,W),F(null,V)},(V)=>{F(V,0)});else this.boundPorts.set(W.mapKey,W),X.then((V)=>{W.completionPromise=null,W.portNumber=V,F(null,V)},(V)=>{F(V,0)})}registerInjectorToChannelz(){return yF.registerChannelzSocket("injector",()=>{return{localAddress:null,remoteAddress:null,security:null,remoteName:null,streamsStarted:0,streamsSucceeded:0,streamsFailed:0,messagesSent:0,messagesReceived:0,keepAlivesSent:0,lastLocalStreamCreatedTimestamp:null,lastRemoteStreamCreatedTimestamp:null,lastMessageSentTimestamp:null,lastMessageReceivedTimestamp:null,localFlowControlWindow:null,remoteFlowControlWindow:null}},this.channelzEnabled)}experimentalCreateConnectionInjectorWithChannelzRef(Z,G,F=!1){if(Z===null||!(Z instanceof qX0.ServerCredentials))throw new TypeError("creds must be a ServerCredentials object");if(this.channelzEnabled)this.listenerChildrenTracker.refChild(G);let I=this.createHttp2Server(Z),Y=new Set;return this.http2Servers.set(I,{channelzRef:G,sessions:Y,ownsChannelzRef:F}),{injectConnection:(W)=>{I.emit("connection",W)},drain:(W)=>{var J,X;for(let V of Y)this.closeSession(V);(X=(J=setTimeout(()=>{for(let V of Y)V.destroy(mV.constants.NGHTTP2_CANCEL)},W)).unref)===null||X===void 0||X.call(J)},destroy:()=>{this.closeServer(I);for(let W of Y)this.closeSession(W)}}}createConnectionInjector(Z){if(Z===null||!(Z instanceof qX0.ServerCredentials))throw new TypeError("creds must be a ServerCredentials object");let G=this.registerInjectorToChannelz();return this.experimentalCreateConnectionInjectorWithChannelzRef(Z,G,!0)}closeServer(Z,G){this.trace("Closing server with address "+JSON.stringify(Z.address()));let F=this.http2Servers.get(Z);Z.close(()=>{if(F&&F.ownsChannelzRef)this.listenerChildrenTracker.unrefChild(F.channelzRef),yF.unregisterChannelzRef(F.channelzRef);this.http2Servers.delete(Z),G===null||G===void 0||G()})}closeSession(Z,G){var F;this.trace("Closing session initiated by "+((F=Z.socket)===null||F===void 0?void 0:F.remoteAddress));let I=this.sessions.get(Z),Y=()=>{if(I)this.sessionChildrenTracker.unrefChild(I.ref),yF.unregisterChannelzRef(I.ref);G===null||G===void 0||G()};if(Z.closed)queueMicrotask(Y);else Z.close(Y)}completeUnbind(Z){for(let G of Z.listeningServers){let F=this.http2Servers.get(G);if(this.closeServer(G,()=>{Z.listeningServers.delete(G)}),F)for(let I of F.sessions)this.closeSession(I)}this.boundPorts.delete(Z.mapKey)}unbind(Z){this.trace("unbind port="+Z);let G=this.normalizePort(Z),F=I$.splitHostPort(G.path);if((F===null||F===void 0?void 0:F.port)===0)throw new Error("Cannot unbind port 0");let I=this.boundPorts.get(I$.uriToString(G));if(I)if(this.trace("unbinding "+I.mapKey+" originally bound as "+I$.uriToString(I.originalUri)),I.completionPromise)I.cancelled=!0;else this.completeUnbind(I)}drain(Z,G){var F,I;this.trace("drain port="+Z+" graceTimeMs="+G);let Y=this.normalizePort(Z),W=I$.splitHostPort(Y.path);if((W===null||W===void 0?void 0:W.port)===0)throw new Error("Cannot drain port 0");let J=this.boundPorts.get(I$.uriToString(Y));if(!J)return;let X=new Set;for(let V of J.listeningServers){let C=this.http2Servers.get(V);if(C)for(let K of C.sessions)X.add(K),this.closeSession(K,()=>{X.delete(K)})}(I=(F=setTimeout(()=>{for(let V of X)V.destroy(mV.constants.NGHTTP2_CANCEL)},G)).unref)===null||I===void 0||I.call(F)}forceShutdown(){for(let Z of this.boundPorts.values())Z.cancelled=!0;this.boundPorts.clear();for(let Z of this.http2Servers.keys())this.closeServer(Z);this.sessions.forEach((Z,G)=>{this.closeSession(G),G.destroy(mV.constants.NGHTTP2_CANCEL)}),this.sessions.clear(),yF.unregisterChannelzRef(this.channelzRef),this.shutdown=!0}register(Z,G,F,I,Y){if(this.handlers.has(Z))return!1;return this.handlers.set(Z,{func:G,serialize:F,deserialize:I,type:Y,path:Z}),!0}unregister(Z){return this.handlers.delete(Z)}start(){if(this.http2Servers.size===0||[...this.http2Servers.keys()].every((Z)=>!Z.listening))throw new Error("server must be bound in order to start");if(this.started===!0)throw new Error("server is already started");this.started=!0}tryShutdown(Z){var G;let F=(W)=>{yF.unregisterChannelzRef(this.channelzRef),Z(W)},I=0;function Y(){if(I--,I===0)F()}this.shutdown=!0;for(let[W,J]of this.http2Servers.entries()){I++;let X=J.channelzRef.name;this.trace("Waiting for server "+X+" to close"),this.closeServer(W,()=>{this.trace("Server "+X+" finished closing"),Y()});for(let V of J.sessions.keys()){I++;let C=(G=V.socket)===null||G===void 0?void 0:G.remoteAddress;this.trace("Waiting for session "+C+" to close"),this.closeSession(V,()=>{this.trace("Session "+C+" finished closing"),Y()})}}if(I===0)F()}addHttp2Port(){throw new Error("Not yet implemented")}getChannelzRef(){return this.channelzRef}_verifyContentType(Z,G){let F=G[mV.constants.HTTP2_HEADER_CONTENT_TYPE];if(typeof F!=="string"||!F.startsWith("application/grpc"))return Z.respond({[mV.constants.HTTP2_HEADER_STATUS]:mV.constants.HTTP_STATUS_UNSUPPORTED_MEDIA_TYPE},{endStream:!0}),!1;return!0}_retrieveHandler(Z){Ee2("Received call to method "+Z+" at address "+this.serverAddressString);let G=this.handlers.get(Z);if(G===void 0)return Ee2("No handler registered for method "+Z+". Sending UNIMPLEMENTED status."),null;return G}_respondWithError(Z,G,F=null){var I,Y;let W=Object.assign({"grpc-status":(I=Z.code)!==null&&I!==void 0?I:XG.Status.INTERNAL,"grpc-message":Z.details,[mV.constants.HTTP2_HEADER_STATUS]:mV.constants.HTTP_STATUS_OK,[mV.constants.HTTP2_HEADER_CONTENT_TYPE]:"application/grpc+proto"},(Y=Z.metadata)===null||Y===void 0?void 0:Y.toHttp2Headers());G.respond(W,{endStream:!0}),this.callTracker.addCallFailed(),F===null||F===void 0||F.streamTracker.addCallFailed()}_channelzHandler(Z,G,F){this.onStreamOpened(G);let I=this.sessions.get(G.session);if(this.callTracker.addCallStarted(),I===null||I===void 0||I.streamTracker.addCallStarted(),!this._verifyContentType(G,F)){this.callTracker.addCallFailed(),I===null||I===void 0||I.streamTracker.addCallFailed();return}let Y=F[He2],W=this._retrieveHandler(Y);if(!W){this._respondWithError(LX0(Y),G,I);return}let J={addMessageSent:()=>{if(I)I.messagesSent+=1,I.lastMessageSentTimestamp=new Date},addMessageReceived:()=>{if(I)I.messagesReceived+=1,I.lastMessageReceivedTimestamp=new Date},onCallEnd:(V)=>{if(V.code===XG.Status.OK)this.callTracker.addCallSucceeded();else this.callTracker.addCallFailed()},onStreamEnd:(V)=>{if(I)if(V)I.streamTracker.addCallSucceeded();else I.streamTracker.addCallFailed()}},X=Ce2.getServerInterceptingCall([...Z,...this.interceptors],G,F,J,W,this.options);if(!this._runHandlerForCall(X,W))this.callTracker.addCallFailed(),I===null||I===void 0||I.streamTracker.addCallFailed(),X.sendStatus({code:XG.Status.INTERNAL,details:`Unknown handler type: ${W.type}`})}_streamHandler(Z,G,F){if(this.onStreamOpened(G),this._verifyContentType(G,F)!==!0)return;let I=F[He2],Y=this._retrieveHandler(I);if(!Y){this._respondWithError(LX0(I),G,null);return}let W=Ce2.getServerInterceptingCall([...Z,...this.interceptors],G,F,null,Y,this.options);if(!this._runHandlerForCall(W,Y))W.sendStatus({code:XG.Status.INTERNAL,details:`Unknown handler type: ${Y.type}`})}_runHandlerForCall(Z,G){let{type:F}=G;if(F==="unary")SU6(Z,G);else if(F==="clientStream")jU6(Z,G);else if(F==="serverStream")yU6(Z,G);else if(F==="bidi")kU6(Z,G);else return!1;return!0}_setupHandlers(Z,G){if(Z===null)return;let F=Z.address(),I="null";if(F)if(typeof F==="string")I=F;else I=F.address+":"+F.port;this.serverAddressString=I;let Y=this.channelzEnabled?this._channelzHandler:this._streamHandler,W=this.channelzEnabled?this._channelzSessionHandler(Z):this._sessionHandler(Z);Z.on("stream",Y.bind(this,G)),Z.on("session",W)}_sessionHandler(Z){return(G)=>{var F,I;(F=this.http2Servers.get(Z))===null||F===void 0||F.sessions.add(G);let Y=null,W=null,J=null,X=!1,V=this.enableIdleTimeout(G);if(this.maxConnectionAgeMs!==Kt){let $=this.maxConnectionAgeMs/10,L=Math.random()*$*2-$;Y=setTimeout(()=>{var N,O;X=!0,this.trace("Connection dropped by max connection age: "+((N=G.socket)===null||N===void 0?void 0:N.remoteAddress));try{G.goaway(mV.constants.NGHTTP2_NO_ERROR,2147483647,ze2)}catch(R){G.destroy();return}if(G.close(),this.maxConnectionAgeGraceMs!==Kt)W=setTimeout(()=>{G.destroy()},this.maxConnectionAgeGraceMs),(O=W.unref)===null||O===void 0||O.call(W)},this.maxConnectionAgeMs+L),(I=Y.unref)===null||I===void 0||I.call(Y)}let C=()=>{if(J)clearTimeout(J),J=null},K=()=>{return!G.destroyed&&this.keepaliveTimeMs<NX0&&this.keepaliveTimeMs>0},H,z=()=>{var $;if(!K())return;this.keepaliveTrace("Starting keepalive timer for "+this.keepaliveTimeMs+"ms"),J=setTimeout(()=>{C(),H()},this.keepaliveTimeMs),($=J.unref)===null||$===void 0||$.call(J)};H=()=>{var $;if(!K())return;this.keepaliveTrace("Sending ping with timeout "+this.keepaliveTimeoutMs+"ms");let L="";try{if(!G.ping((O,R,T)=>{if(C(),O)this.keepaliveTrace("Ping failed with error: "+O.message),X=!0,G.close();else this.keepaliveTrace("Received ping response"),z()}))L="Ping returned false"}catch(N){L=(N instanceof Error?N.message:"")||"Unknown error"}if(L){this.keepaliveTrace("Ping send failed: "+L),this.trace("Connection dropped due to ping send error: "+L),X=!0,G.close();return}J=setTimeout(()=>{C(),this.keepaliveTrace("Ping timeout passed without response"),this.trace("Connection dropped by keepalive timeout"),X=!0,G.close()},this.keepaliveTimeoutMs),($=J.unref)===null||$===void 0||$.call(J)},z(),G.on("close",()=>{var $,L;if(!X)this.trace(`Connection dropped by client ${($=G.socket)===null||$===void 0?void 0:$.remoteAddress}`);if(Y)clearTimeout(Y);if(W)clearTimeout(W);if(C(),V!==null)clearTimeout(V.timeout),this.sessionIdleTimeouts.delete(G);(L=this.http2Servers.get(Z))===null||L===void 0||L.sessions.delete(G)})}}_channelzSessionHandler(Z){return(G)=>{var F,I,Y,W;let J=yF.registerChannelzSocket((I=(F=G.socket)===null||F===void 0?void 0:F.remoteAddress)!==null&&I!==void 0?I:"unknown",this.getChannelzSessionInfo.bind(this,G),this.channelzEnabled),X={ref:J,streamTracker:new yF.ChannelzCallTracker,messagesSent:0,messagesReceived:0,keepAlivesSent:0,lastMessageSentTimestamp:null,lastMessageReceivedTimestamp:null};(Y=this.http2Servers.get(Z))===null||Y===void 0||Y.sessions.add(G),this.sessions.set(G,X);let V=`${G.socket.remoteAddress}:${G.socket.remotePort}`;this.channelzTrace.addTrace("CT_INFO","Connection established by client "+V),this.trace("Connection established by client "+V),this.sessionChildrenTracker.refChild(J);let C=null,K=null,H=null,z=!1,$=this.enableIdleTimeout(G);if(this.maxConnectionAgeMs!==Kt){let T=this.maxConnectionAgeMs/10,j=Math.random()*T*2-T;C=setTimeout(()=>{var f;z=!0,this.channelzTrace.addTrace("CT_INFO","Connection dropped by max connection age from "+V);try{G.goaway(mV.constants.NGHTTP2_NO_ERROR,2147483647,ze2)}catch(y){G.destroy();return}if(G.close(),this.maxConnectionAgeGraceMs!==Kt)K=setTimeout(()=>{G.destroy()},this.maxConnectionAgeGraceMs),(f=K.unref)===null||f===void 0||f.call(K)},this.maxConnectionAgeMs+j),(W=C.unref)===null||W===void 0||W.call(C)}let L=()=>{if(H)clearTimeout(H),H=null},N=()=>{return!G.destroyed&&this.keepaliveTimeMs<NX0&&this.keepaliveTimeMs>0},O,R=()=>{var T;if(!N())return;this.keepaliveTrace("Starting keepalive timer for "+this.keepaliveTimeMs+"ms"),H=setTimeout(()=>{L(),O()},this.keepaliveTimeMs),(T=H.unref)===null||T===void 0||T.call(H)};O=()=>{var T;if(!N())return;this.keepaliveTrace("Sending ping with timeout "+this.keepaliveTimeoutMs+"ms");let j="";try{if(!G.ping((y,c,h)=>{if(L(),y)this.keepaliveTrace("Ping failed with error: "+y.message),this.channelzTrace.addTrace("CT_INFO","Connection dropped due to error of a ping frame "+y.message+" return in "+c),z=!0,G.close();else this.keepaliveTrace("Received ping response"),R()}))j="Ping returned false"}catch(f){j=(f instanceof Error?f.message:"")||"Unknown error"}if(j){this.keepaliveTrace("Ping send failed: "+j),this.channelzTrace.addTrace("CT_INFO","Connection dropped due to ping send error: "+j),z=!0,G.close();return}X.keepAlivesSent+=1,H=setTimeout(()=>{L(),this.keepaliveTrace("Ping timeout passed without response"),this.channelzTrace.addTrace("CT_INFO","Connection dropped by keepalive timeout from "+V),z=!0,G.close()},this.keepaliveTimeoutMs),(T=H.unref)===null||T===void 0||T.call(H)},R(),G.on("close",()=>{var T;if(!z)this.channelzTrace.addTrace("CT_INFO","Connection dropped by client "+V);if(this.sessionChildrenTracker.unrefChild(J),yF.unregisterChannelzRef(J),C)clearTimeout(C);if(K)clearTimeout(K);if(L(),$!==null)clearTimeout($.timeout),this.sessionIdleTimeouts.delete(G);(T=this.http2Servers.get(Z))===null||T===void 0||T.sessions.delete(G),this.sessions.delete(G)})}}enableIdleTimeout(Z){var G,F;if(this.sessionIdleTimeout>=Ke2)return null;let I={activeStreams:0,lastIdle:Date.now(),onClose:this.onStreamClose.bind(this,Z),timeout:setTimeout(this.onIdleTimeout,this.sessionIdleTimeout,this,Z)};(F=(G=I.timeout).unref)===null||F===void 0||F.call(G),this.sessionIdleTimeouts.set(Z,I);let{socket:Y}=Z;return this.trace("Enable idle timeout for "+Y.remoteAddress+":"+Y.remotePort),I}onIdleTimeout(Z,G){let{socket:F}=G,I=Z.sessionIdleTimeouts.get(G);if(I!==void 0&&I.activeStreams===0)if(Date.now()-I.lastIdle>=Z.sessionIdleTimeout)Z.trace("Session idle timeout triggered for "+(F===null||F===void 0?void 0:F.remoteAddress)+":"+(F===null||F===void 0?void 0:F.remotePort)+" last idle at "+I.lastIdle),Z.closeSession(G);else I.timeout.refresh()}onStreamOpened(Z){let G=Z.session,F=this.sessionIdleTimeouts.get(G);if(F)F.activeStreams+=1,Z.once("close",F.onClose)}onStreamClose(Z){var G,F;let I=this.sessionIdleTimeouts.get(Z);if(I){if(I.activeStreams-=1,I.activeStreams===0)I.lastIdle=Date.now(),I.timeout.refresh(),this.trace("Session onStreamClose"+((G=Z.socket)===null||G===void 0?void 0:G.remoteAddress)+":"+((F=Z.socket)===null||F===void 0?void 0:F.remotePort)+" at "+I.lastIdle)}}},(()=>{let D=typeof Symbol==="function"&&Symbol.metadata?Object.create(null):void 0;if(Q=[OU6("Calling start() is no longer necessary. It can be safely omitted.")],qU6(A,null,Q,{kind:"method",name:"start",static:!1,private:!1,access:{has:(Z)=>("start"in Z),get:(Z)=>Z.start},metadata:D},null,B),D)Object.defineProperty(A,Symbol.metadata,{enumerable:!0,configurable:!0,writable:!0,value:D})})(),A})();Fx.Server=PU6;async function SU6(A,B){let Q;function D(F,I,Y,W){if(F){A.sendStatus(zt.serverErrorToStatus(F,Y));return}A.sendMessage(I,()=>{A.sendStatus({code:XG.Status.OK,details:"OK",metadata:Y!==null&&Y!==void 0?Y:null})})}let Z,G=null;A.start({onReceiveMetadata(F){Z=F,A.startRead()},onReceiveMessage(F){if(G){A.sendStatus({code:XG.Status.UNIMPLEMENTED,details:`Received a second request message for server streaming method ${B.path}`,metadata:null});return}G=F,A.startRead()},onReceiveHalfClose(){if(!G){A.sendStatus({code:XG.Status.UNIMPLEMENTED,details:`Received no request message for server streaming method ${B.path}`,metadata:null});return}Q=new zt.ServerWritableStreamImpl(B.path,A,Z,G);try{B.func(Q,D)}catch(F){A.sendStatus({code:XG.Status.UNKNOWN,details:`Server method handler threw error ${F.message}`,metadata:null})}},onCancel(){if(Q)Q.cancelled=!0,Q.emit("cancelled","cancelled")}})}function jU6(A,B){let Q;function D(Z,G,F,I){if(Z){A.sendStatus(zt.serverErrorToStatus(Z,F));return}A.sendMessage(G,()=>{A.sendStatus({code:XG.Status.OK,details:"OK",metadata:F!==null&&F!==void 0?F:null})})}A.start({onReceiveMetadata(Z){Q=new zt.ServerDuplexStreamImpl(B.path,A,Z);try{B.func(Q,D)}catch(G){A.sendStatus({code:XG.Status.UNKNOWN,details:`Server method handler threw error ${G.message}`,metadata:null})}},onReceiveMessage(Z){Q.push(Z)},onReceiveHalfClose(){Q.push(null)},onCancel(){if(Q)Q.cancelled=!0,Q.emit("cancelled","cancelled"),Q.destroy()}})}function yU6(A,B){let Q,D,Z=null;A.start({onReceiveMetadata(G){D=G,A.startRead()},onReceiveMessage(G){if(Z){A.sendStatus({code:XG.Status.UNIMPLEMENTED,details:`Received a second request message for server streaming method ${B.path}`,metadata:null});return}Z=G,A.startRead()},onReceiveHalfClose(){if(!Z){A.sendStatus({code:XG.Status.UNIMPLEMENTED,details:`Received no request message for server streaming method ${B.path}`,metadata:null});return}Q=new zt.ServerWritableStreamImpl(B.path,A,D,Z);try{B.func(Q)}catch(G){A.sendStatus({code:XG.Status.UNKNOWN,details:`Server method handler threw error ${G.message}`,metadata:null})}},onCancel(){if(Q)Q.cancelled=!0,Q.emit("cancelled","cancelled"),Q.destroy()}})}function kU6(A,B){let Q;A.start({onReceiveMetadata(D){Q=new zt.ServerDuplexStreamImpl(B.path,A,D);try{B.func(Q)}catch(Z){A.sendStatus({code:XG.Status.UNKNOWN,details:`Server method handler threw error ${Z.message}`,metadata:null})}},onReceiveMessage(D){Q.push(D)},onReceiveHalfClose(){Q.push(null)},onCancel(){if(Q)Q.cancelled=!0,Q.emit("cancelled","cancelled"),Q.destroy()}})}});
var VJ0=E((rs2)=>{var lC6=rs2,pC6=GP1();lC6[".google.protobuf.Any"]={fromObject:function(A){if(A&&A["@type"]){var B=A["@type"].substring(A["@type"].lastIndexOf("/")+1),Q=this.lookup(B);if(Q){var D=A["@type"].charAt(0)==="."?A["@type"].slice(1):A["@type"];if(D.indexOf("/")===-1)D="/"+D;return this.create({type_url:D,value:Q.encode(Q.fromObject(A)).finish()})}}return this.fromObject(A)},toObject:function(A,B){var Q="type.googleapis.com/",D="",Z="";if(B&&B.json&&A.type_url&&A.value){Z=A.type_url.substring(A.type_url.lastIndexOf("/")+1),D=A.type_url.substring(0,A.type_url.lastIndexOf("/")+1);var G=this.lookup(Z);if(G)A=G.decode(A.value)}if(!(A instanceof this.ctor)&&A instanceof pC6){var F=A.$type.toObject(A,B),I=A.$type.fullName[0]==="."?A.$type.fullName.slice(1):A.$type.fullName;if(D==="")D=Q;return Z=D+I,F["@type"]=Z,F}return this.toObject(A,B)}}});
var X1B=E((W1B)=>{var bX0;Object.defineProperty(W1B,"__esModule",{value:!0});W1B.OutlierDetectionLoadBalancer=W1B.OutlierDetectionLoadBalancingConfig=void 0;W1B.setup=gw6;var Sw6=EE(),Z1B=_6(),ru=MX0(),G1B=PX0(),jw6=gu(),yw6=pT1(),kw6=s_(),fX0=UE(),_w6=xP1(),xw6=F7(),vw6="outlier_detection";function qI(A){xw6.trace(Z1B.LogVerbosity.DEBUG,vw6,A)}var uX0="outlier_detection",bw6=((bX0=process.env.GRPC_EXPERIMENTAL_ENABLE_OUTLIER_DETECTION)!==null&&bX0!==void 0?bX0:"true")==="true",fw6={stdev_factor:1900,enforcement_percentage:100,minimum_hosts:5,request_volume:100},hw6={threshold:85,enforcement_percentage:100,minimum_hosts:5,request_volume:50};function Ut(A,B,Q,D){if(B in A&&A[B]!==void 0&&typeof A[B]!==Q){let Z=D?`${D}.${B}`:B;throw new Error(`outlier detection config ${Z} parse error: expected ${Q}, got ${typeof A[B]}`)}}function hX0(A,B,Q){let D=Q?`${Q}.${B}`:B;if(B in A&&A[B]!==void 0){if(!ru.isDuration(A[B]))throw new Error(`outlier detection config ${D} parse error: expected Duration, got ${typeof A[B]}`);if(!(A[B].seconds>=0&&A[B].seconds<=315576000000&&A[B].nanos>=0&&A[B].nanos<=999999999))throw new Error(`outlier detection config ${D} parse error: values out of range for non-negative Duaration`)}}function nP1(A,B,Q){let D=Q?`${Q}.${B}`:B;if(Ut(A,B,"number",Q),B in A&&A[B]!==void 0&&!(A[B]>=0&&A[B]<=100))throw new Error(`outlier detection config ${D} parse error: value out of range for percentage (0-100)`)}class t31{constructor(A,B,Q,D,Z,G,F){if(this.childPolicy=F,F.getLoadBalancerName()==="pick_first")throw new Error("outlier_detection LB policy cannot have a pick_first child policy");this.intervalMs=A!==null&&A!==void 0?A:1e4,this.baseEjectionTimeMs=B!==null&&B!==void 0?B:30000,this.maxEjectionTimeMs=Q!==null&&Q!==void 0?Q:300000,this.maxEjectionPercent=D!==null&&D!==void 0?D:10,this.successRateEjection=Z?Object.assign(Object.assign({},fw6),Z):null,this.failurePercentageEjection=G?Object.assign(Object.assign({},hw6),G):null}getLoadBalancerName(){return uX0}toJsonObject(){var A,B;return{outlier_detection:{interval:ru.msToDuration(this.intervalMs),base_ejection_time:ru.msToDuration(this.baseEjectionTimeMs),max_ejection_time:ru.msToDuration(this.maxEjectionTimeMs),max_ejection_percent:this.maxEjectionPercent,success_rate_ejection:(A=this.successRateEjection)!==null&&A!==void 0?A:void 0,failure_percentage_ejection:(B=this.failurePercentageEjection)!==null&&B!==void 0?B:void 0,child_policy:[this.childPolicy.toJsonObject()]}}}getIntervalMs(){return this.intervalMs}getBaseEjectionTimeMs(){return this.baseEjectionTimeMs}getMaxEjectionTimeMs(){return this.maxEjectionTimeMs}getMaxEjectionPercent(){return this.maxEjectionPercent}getSuccessRateEjectionConfig(){return this.successRateEjection}getFailurePercentageEjectionConfig(){return this.failurePercentageEjection}getChildPolicy(){return this.childPolicy}static createFromJson(A){var B;if(hX0(A,"interval"),hX0(A,"base_ejection_time"),hX0(A,"max_ejection_time"),nP1(A,"max_ejection_percent"),"success_rate_ejection"in A&&A.success_rate_ejection!==void 0){if(typeof A.success_rate_ejection!=="object")throw new Error("outlier detection config success_rate_ejection must be an object");Ut(A.success_rate_ejection,"stdev_factor","number","success_rate_ejection"),nP1(A.success_rate_ejection,"enforcement_percentage","success_rate_ejection"),Ut(A.success_rate_ejection,"minimum_hosts","number","success_rate_ejection"),Ut(A.success_rate_ejection,"request_volume","number","success_rate_ejection")}if("failure_percentage_ejection"in A&&A.failure_percentage_ejection!==void 0){if(typeof A.failure_percentage_ejection!=="object")throw new Error("outlier detection config failure_percentage_ejection must be an object");nP1(A.failure_percentage_ejection,"threshold","failure_percentage_ejection"),nP1(A.failure_percentage_ejection,"enforcement_percentage","failure_percentage_ejection"),Ut(A.failure_percentage_ejection,"minimum_hosts","number","failure_percentage_ejection"),Ut(A.failure_percentage_ejection,"request_volume","number","failure_percentage_ejection")}if(!("child_policy"in A)||!Array.isArray(A.child_policy))throw new Error("outlier detection config child_policy must be an array");let Q=jw6.selectLbConfigFromList(A.child_policy);if(!Q)throw new Error("outlier detection config child_policy: no valid recognized policy found");return new t31(A.interval?ru.durationToMs(A.interval):null,A.base_ejection_time?ru.durationToMs(A.base_ejection_time):null,A.max_ejection_time?ru.durationToMs(A.max_ejection_time):null,(B=A.max_ejection_percent)!==null&&B!==void 0?B:null,A.success_rate_ejection,A.failure_percentage_ejection,Q)}}W1B.OutlierDetectionLoadBalancingConfig=t31;class F1B extends _w6.BaseSubchannelWrapper{constructor(A,B){super(A);this.mapEntry=B,this.refCount=0}ref(){this.child.ref(),this.refCount+=1}unref(){if(this.child.unref(),this.refCount-=1,this.refCount<=0){if(this.mapEntry){let A=this.mapEntry.subchannelWrappers.indexOf(this);if(A>=0)this.mapEntry.subchannelWrappers.splice(A,1)}}}eject(){this.setHealthy(!1)}uneject(){this.setHealthy(!0)}getMapEntry(){return this.mapEntry}getWrappedSubchannel(){return this.child}}function gX0(){return{success:0,failure:0}}class I1B{constructor(){this.activeBucket=gX0(),this.inactiveBucket=gX0()}addSuccess(){this.activeBucket.success+=1}addFailure(){this.activeBucket.failure+=1}switchBuckets(){this.inactiveBucket=this.activeBucket,this.activeBucket=gX0()}getLastSuccesses(){return this.inactiveBucket.success}getLastFailures(){return this.inactiveBucket.failure}}class Y1B{constructor(A,B){this.wrappedPicker=A,this.countCalls=B}pick(A){let B=this.wrappedPicker.pick(A);if(B.pickResultType===kw6.PickResultType.COMPLETE){let Q=B.subchannel,D=Q.getMapEntry();if(D){let Z=B.onCallEnded;if(this.countCalls)Z=(G)=>{var F;if(G===Z1B.Status.OK)D.counter.addSuccess();else D.counter.addFailure();(F=B.onCallEnded)===null||F===void 0||F.call(B,G)};return Object.assign(Object.assign({},B),{subchannel:Q.getWrappedSubchannel(),onCallEnded:Z})}else return Object.assign(Object.assign({},B),{subchannel:Q.getWrappedSubchannel()})}else return B}}class mX0{constructor(A){this.entryMap=new fX0.EndpointMap,this.latestConfig=null,this.timerStartTime=null,this.childBalancer=new yw6.ChildLoadBalancerHandler(G1B.createChildChannelControlHelper(A,{createSubchannel:(B,Q)=>{let D=A.createSubchannel(B,Q),Z=this.entryMap.getForSubchannelAddress(B),G=new F1B(D,Z);if((Z===null||Z===void 0?void 0:Z.currentEjectionTimestamp)!==null)G.eject();return Z===null||Z===void 0||Z.subchannelWrappers.push(G),G},updateState:(B,Q,D)=>{if(B===Sw6.ConnectivityState.READY)A.updateState(B,new Y1B(Q,this.isCountingEnabled()),D);else A.updateState(B,Q,D)}})),this.ejectionTimer=setInterval(()=>{},0),clearInterval(this.ejectionTimer)}isCountingEnabled(){return this.latestConfig!==null&&(this.latestConfig.getSuccessRateEjectionConfig()!==null||this.latestConfig.getFailurePercentageEjectionConfig()!==null)}getCurrentEjectionPercent(){let A=0;for(let B of this.entryMap.values())if(B.currentEjectionTimestamp!==null)A+=1;return A*100/this.entryMap.size}runSuccessRateCheck(A){if(!this.latestConfig)return;let B=this.latestConfig.getSuccessRateEjectionConfig();if(!B)return;qI("Running success rate check");let Q=B.request_volume,D=0,Z=[];for(let[J,X]of this.entryMap.entries()){let V=X.counter.getLastSuccesses(),C=X.counter.getLastFailures();if(qI("Stats for "+fX0.endpointToString(J)+": successes="+V+" failures="+C+" targetRequestVolume="+Q),V+C>=Q)D+=1,Z.push(V/(V+C))}if(qI("Found "+D+" success rate candidates; currentEjectionPercent="+this.getCurrentEjectionPercent()+" successRates=["+Z+"]"),D<B.minimum_hosts)return;let G=Z.reduce((J,X)=>J+X)/Z.length,F=0;for(let J of Z){let X=J-G;F+=X*X}let I=F/Z.length,Y=Math.sqrt(I),W=G-Y*(B.stdev_factor/1000);qI("stdev="+Y+" ejectionThreshold="+W);for(let[J,X]of this.entryMap.entries()){if(this.getCurrentEjectionPercent()>=this.latestConfig.getMaxEjectionPercent())break;let V=X.counter.getLastSuccesses(),C=X.counter.getLastFailures();if(V+C<Q)continue;let K=V/(V+C);if(qI("Checking candidate "+J+" successRate="+K),K<W){let H=Math.random()*100;if(qI("Candidate "+J+" randomNumber="+H+" enforcement_percentage="+B.enforcement_percentage),H<B.enforcement_percentage)qI("Ejecting candidate "+J),this.eject(X,A)}}}runFailurePercentageCheck(A){if(!this.latestConfig)return;let B=this.latestConfig.getFailurePercentageEjectionConfig();if(!B)return;qI("Running failure percentage check. threshold="+B.threshold+" request volume threshold="+B.request_volume);let Q=0;for(let D of this.entryMap.values()){let Z=D.counter.getLastSuccesses(),G=D.counter.getLastFailures();if(Z+G>=B.request_volume)Q+=1}if(Q<B.minimum_hosts)return;for(let[D,Z]of this.entryMap.entries()){if(this.getCurrentEjectionPercent()>=this.latestConfig.getMaxEjectionPercent())break;let G=Z.counter.getLastSuccesses(),F=Z.counter.getLastFailures();if(qI("Candidate successes="+G+" failures="+F),G+F<B.request_volume)continue;if(F*100/(F+G)>B.threshold){let Y=Math.random()*100;if(qI("Candidate "+D+" randomNumber="+Y+" enforcement_percentage="+B.enforcement_percentage),Y<B.enforcement_percentage)qI("Ejecting candidate "+D),this.eject(Z,A)}}}eject(A,B){A.currentEjectionTimestamp=new Date,A.ejectionTimeMultiplier+=1;for(let Q of A.subchannelWrappers)Q.eject()}uneject(A){A.currentEjectionTimestamp=null;for(let B of A.subchannelWrappers)B.uneject()}switchAllBuckets(){for(let A of this.entryMap.values())A.counter.switchBuckets()}startTimer(A){var B,Q;this.ejectionTimer=setTimeout(()=>this.runChecks(),A),(Q=(B=this.ejectionTimer).unref)===null||Q===void 0||Q.call(B)}runChecks(){let A=new Date;if(qI("Ejection timer running"),this.switchAllBuckets(),!this.latestConfig)return;this.timerStartTime=A,this.startTimer(this.latestConfig.getIntervalMs()),this.runSuccessRateCheck(A),this.runFailurePercentageCheck(A);for(let[B,Q]of this.entryMap.entries())if(Q.currentEjectionTimestamp===null){if(Q.ejectionTimeMultiplier>0)Q.ejectionTimeMultiplier-=1}else{let D=this.latestConfig.getBaseEjectionTimeMs(),Z=this.latestConfig.getMaxEjectionTimeMs(),G=new Date(Q.currentEjectionTimestamp.getTime());if(G.setMilliseconds(G.getMilliseconds()+Math.min(D*Q.ejectionTimeMultiplier,Math.max(D,Z))),G<new Date)qI("Unejecting "+B),this.uneject(Q)}}updateAddressList(A,B,Q){if(!(B instanceof t31))return;qI("Received update with config: "+JSON.stringify(B.toJsonObject(),void 0,2));for(let Z of A)if(!this.entryMap.has(Z))qI("Adding map entry for "+fX0.endpointToString(Z)),this.entryMap.set(Z,{counter:new I1B,currentEjectionTimestamp:null,ejectionTimeMultiplier:0,subchannelWrappers:[]});this.entryMap.deleteMissing(A);let D=B.getChildPolicy();if(this.childBalancer.updateAddressList(A,D,Q),B.getSuccessRateEjectionConfig()||B.getFailurePercentageEjectionConfig())if(this.timerStartTime){qI("Previous timer existed. Replacing timer"),clearTimeout(this.ejectionTimer);let Z=B.getIntervalMs()-(new Date().getTime()-this.timerStartTime.getTime());this.startTimer(Z)}else qI("Starting new timer"),this.timerStartTime=new Date,this.startTimer(B.getIntervalMs()),this.switchAllBuckets();else{qI("Counting disabled. Cancelling timer."),this.timerStartTime=null,clearTimeout(this.ejectionTimer);for(let Z of this.entryMap.values())this.uneject(Z),Z.ejectionTimeMultiplier=0}this.latestConfig=B}exitIdle(){this.childBalancer.exitIdle()}resetBackoff(){this.childBalancer.resetBackoff()}destroy(){clearTimeout(this.ejectionTimer),this.childBalancer.destroy()}getTypeName(){return uX0}}W1B.OutlierDetectionLoadBalancer=mX0;function gw6(){if(bw6)G1B.registerLoadBalancerType(uX0,mX0,t31)}});
var XJ0=E((ss2)=>{var as2=ss2,v31=Z$(),ZM=$I();function WJ0(A,B,Q,D){var Z=!1;if(B.resolvedType)if(B.resolvedType instanceof v31){A("switch(d%s){",D);for(var G=B.resolvedType.values,F=Object.keys(G),I=0;I<F.length;++I){if(G[F[I]]===B.typeDefault&&!Z){if(A("default:")('if(typeof(d%s)==="number"){m%s=d%s;break}',D,D,D),!B.repeated)A("break");Z=!0}A("case%j:",F[I])("case %i:",G[F[I]])("m%s=%j",D,G[F[I]])("break")}A("}")}else A('if(typeof d%s!=="object")',D)("throw TypeError(%j)",B.fullName+": object expected")("m%s=types[%i].fromObject(d%s)",D,Q,D);else{var Y=!1;switch(B.type){case"double":case"float":A("m%s=Number(d%s)",D,D);break;case"uint32":case"fixed32":A("m%s=d%s>>>0",D,D);break;case"int32":case"sint32":case"sfixed32":A("m%s=d%s|0",D,D);break;case"uint64":Y=!0;case"int64":case"sint64":case"fixed64":case"sfixed64":A("if(util.Long)")("(m%s=util.Long.fromValue(d%s)).unsigned=%j",D,D,Y)('else if(typeof d%s==="string")',D)("m%s=parseInt(d%s,10)",D,D)('else if(typeof d%s==="number")',D)("m%s=d%s",D,D)('else if(typeof d%s==="object")',D)("m%s=new util.LongBits(d%s.low>>>0,d%s.high>>>0).toNumber(%s)",D,D,D,Y?"true":"");break;case"bytes":A('if(typeof d%s==="string")',D)("util.base64.decode(d%s,m%s=util.newBuffer(util.base64.length(d%s)),0)",D,D,D)("else if(d%s.length >= 0)",D)("m%s=d%s",D,D);break;case"string":A("m%s=String(d%s)",D,D);break;case"bool":A("m%s=Boolean(d%s)",D,D);break}}return A}as2.fromObject=function A(B){var Q=B.fieldsArray,D=ZM.codegen(["d"],B.name+"$fromObject")("if(d instanceof this.ctor)")("return d");if(!Q.length)return D("return new this.ctor");D("var m=new this.ctor");for(var Z=0;Z<Q.length;++Z){var G=Q[Z].resolve(),F=ZM.safeProp(G.name);if(G.map)D("if(d%s){",F)('if(typeof d%s!=="object")',F)("throw TypeError(%j)",G.fullName+": object expected")("m%s={}",F)("for(var ks=Object.keys(d%s),i=0;i<ks.length;++i){",F),WJ0(D,G,Z,F+"[ks[i]]")("}")("}");else if(G.repeated)D("if(d%s){",F)("if(!Array.isArray(d%s))",F)("throw TypeError(%j)",G.fullName+": array expected")("m%s=[]",F)("for(var i=0;i<d%s.length;++i){",F),WJ0(D,G,Z,F+"[i]")("}")("}");else{if(!(G.resolvedType instanceof v31))D("if(d%s!=null){",F);if(WJ0(D,G,Z,F),!(G.resolvedType instanceof v31))D("}")}}return D("return m")};function JJ0(A,B,Q,D){if(B.resolvedType)if(B.resolvedType instanceof v31)A("d%s=o.enums===String?(types[%i].values[m%s]===undefined?m%s:types[%i].values[m%s]):m%s",D,Q,D,D,Q,D,D);else A("d%s=types[%i].toObject(m%s,o)",D,Q,D);else{var Z=!1;switch(B.type){case"double":case"float":A("d%s=o.json&&!isFinite(m%s)?String(m%s):m%s",D,D,D,D);break;case"uint64":Z=!0;case"int64":case"sint64":case"fixed64":case"sfixed64":A('if(typeof m%s==="number")',D)("d%s=o.longs===String?String(m%s):m%s",D,D,D)("else")("d%s=o.longs===String?util.Long.prototype.toString.call(m%s):o.longs===Number?new util.LongBits(m%s.low>>>0,m%s.high>>>0).toNumber(%s):m%s",D,D,D,D,Z?"true":"",D);break;case"bytes":A("d%s=o.bytes===String?util.base64.encode(m%s,0,m%s.length):o.bytes===Array?Array.prototype.slice.call(m%s):m%s",D,D,D,D,D);break;default:A("d%s=m%s",D,D);break}}return A}as2.toObject=function A(B){var Q=B.fieldsArray.slice().sort(ZM.compareFieldsById);if(!Q.length)return ZM.codegen()("return {}");var D=ZM.codegen(["m","o"],B.name+"$toObject")("if(!o)")("o={}")("var d={}"),Z=[],G=[],F=[],I=0;for(;I<Q.length;++I)if(!Q[I].partOf)(Q[I].resolve().repeated?Z:Q[I].map?G:F).push(Q[I]);if(Z.length){D("if(o.arrays||o.defaults){");for(I=0;I<Z.length;++I)D("d%s=[]",ZM.safeProp(Z[I].name));D("}")}if(G.length){D("if(o.objects||o.defaults){");for(I=0;I<G.length;++I)D("d%s={}",ZM.safeProp(G[I].name));D("}")}if(F.length){D("if(o.defaults){");for(I=0;I<F.length;++I){var Y=F[I],W=ZM.safeProp(Y.name);if(Y.resolvedType instanceof v31)D("d%s=o.enums===String?%j:%j",W,Y.resolvedType.valuesById[Y.typeDefault],Y.typeDefault);else if(Y.long)D("if(util.Long){")("var n=new util.Long(%i,%i,%j)",Y.typeDefault.low,Y.typeDefault.high,Y.typeDefault.unsigned)("d%s=o.longs===String?n.toString():o.longs===Number?n.toNumber():n",W)("}else")("d%s=o.longs===String?%j:%i",W,Y.typeDefault.toString(),Y.typeDefault.toNumber());else if(Y.bytes){var J="["+Array.prototype.slice.call(Y.typeDefault).join(",")+"]";D("if(o.bytes===String)d%s=%j",W,String.fromCharCode.apply(String,Y.typeDefault))("else{")("d%s=%s",W,J)("if(o.bytes!==Array)d%s=util.newBuffer(d%s)",W,W)("}")}else D("d%s=%j",W,Y.typeDefault)}D("}")}var X=!1;for(I=0;I<Q.length;++I){var Y=Q[I],V=B._fieldsArray.indexOf(Y),W=ZM.safeProp(Y.name);if(Y.map){if(!X)X=!0,D("var ks2");D("if(m%s&&(ks2=Object.keys(m%s)).length){",W,W)("d%s={}",W)("for(var j=0;j<ks2.length;++j){"),JJ0(D,Y,V,W+"[ks2[j]]")("}")}else if(Y.repeated)D("if(m%s&&m%s.length){",W,W)("d%s=[]",W)("for(var j=0;j<m%s.length;++j){",W),JJ0(D,Y,V,W+"[j]")("}");else if(D("if(m%s!=null&&m.hasOwnProperty(%j)){",W,Y.name),JJ0(D,Y,V,W),Y.partOf)D("if(o.oneofs)")("d%s=%j",ZM.safeProp(Y.partOf.name),Y.name);D("}")}return D("return d")}});
var XP1=E(($u5,Dr2)=>{Dr2.exports=kK;var JP1=Qt();((kK.prototype=Object.create(JP1.prototype)).constructor=kK).className="Root";var UJ0=o_(),Ar2=Z$(),AK6=eo(),e_=$I(),Br2,EJ0,b31;function kK(A){JP1.call(this,"",A),this.deferred=[],this.files=[]}kK.fromJSON=function A(B,Q){if(!Q)Q=new kK;if(B.options)Q.setOptions(B.options);return Q.addJSON(B.nested)};kK.prototype.resolvePath=e_.path.resolve;kK.prototype.fetch=e_.fetch;function Qr2(){}kK.prototype.load=function A(B,Q,D){if(typeof Q==="function")D=Q,Q=void 0;var Z=this;if(!D)return e_.asPromise(A,Z,B,Q);var G=D===Qr2;function F(C,K){if(!D)return;if(G)throw C;var H=D;D=null,H(C,K)}function I(C){var K=C.lastIndexOf("google/protobuf/");if(K>-1){var H=C.substring(K);if(H in b31)return H}return null}function Y(C,K){try{if(e_.isString(K)&&K.charAt(0)==="{")K=JSON.parse(K);if(!e_.isString(K))Z.setOptions(K.options).addJSON(K.nested);else{EJ0.filename=C;var H=EJ0(K,Z,Q),z,$=0;if(H.imports){for(;$<H.imports.length;++$)if(z=I(H.imports[$])||Z.resolvePath(C,H.imports[$]))W(z)}if(H.weakImports){for($=0;$<H.weakImports.length;++$)if(z=I(H.weakImports[$])||Z.resolvePath(C,H.weakImports[$]))W(z,!0)}}}catch(L){F(L)}if(!G&&!J)F(null,Z)}function W(C,K){if(C=I(C)||C,Z.files.indexOf(C)>-1)return;if(Z.files.push(C),C in b31){if(G)Y(C,b31[C]);else++J,setTimeout(function(){--J,Y(C,b31[C])});return}if(G){var H;try{H=e_.fs.readFileSync(C).toString("utf8")}catch(z){if(!K)F(z);return}Y(C,H)}else++J,Z.fetch(C,function(z,$){if(--J,!D)return;if(z){if(!K)F(z);else if(!J)F(null,Z);return}Y(C,$)})}var J=0;if(e_.isString(B))B=[B];for(var X=0,V;X<B.length;++X)if(V=Z.resolvePath("",B[X]))W(V);if(G)return Z;if(!J)F(null,Z);return};kK.prototype.loadSync=function A(B,Q){if(!e_.isNode)throw Error("not supported");return this.load(B,Q,Qr2)};kK.prototype.resolveAll=function A(){if(this.deferred.length)throw Error("unresolvable extensions: "+this.deferred.map(function(B){return"'extend "+B.extend+"' in "+B.parent.fullName}).join(", "));return JP1.prototype.resolveAll.call(this)};var WP1=/^[A-Z]/;function es2(A,B){var Q=B.parent.lookup(B.extend);if(Q){var D=new UJ0(B.fullName,B.id,B.type,B.rule,void 0,B.options);if(Q.get(D.name))return!0;return D.declaringField=B,B.extensionField=D,Q.add(D),!0}return!1}kK.prototype._handleAdd=function A(B){if(B instanceof UJ0){if(B.extend!==void 0&&!B.extensionField){if(!es2(this,B))this.deferred.push(B)}}else if(B instanceof Ar2){if(WP1.test(B.name))B.parent[B.name]=B.values}else if(!(B instanceof AK6)){if(B instanceof Br2)for(var Q=0;Q<this.deferred.length;)if(es2(this,this.deferred[Q]))this.deferred.splice(Q,1);else++Q;for(var D=0;D<B.nestedArray.length;++D)this._handleAdd(B._nestedArray[D]);if(WP1.test(B.name))B.parent[B.name]=B}};kK.prototype._handleRemove=function A(B){if(B instanceof UJ0){if(B.extend!==void 0)if(B.extensionField)B.extensionField.parent.remove(B.extensionField),B.extensionField=null;else{var Q=this.deferred.indexOf(B);if(Q>-1)this.deferred.splice(Q,1)}}else if(B instanceof Ar2){if(WP1.test(B.name))delete B.parent[B.name]}else if(B instanceof JP1){for(var D=0;D<B.nestedArray.length;++D)this._handleRemove(B._nestedArray[D]);if(WP1.test(B.name))delete B.parent[B.name]}};kK._configure=function(A,B,Q){Br2=A,EJ0=B,b31=Q}});
var YJ0=E((zu5,ns2)=>{ns2.exports=cC6;var mC6=Z$(),FJ0=$I();function NE(A,B){return A.name+": "+B+(A.repeated&&B!=="array"?"[]":A.map&&B!=="object"?"{k:"+A.keyType+"}":"")+" expected"}function IJ0(A,B,Q,D){if(B.resolvedType)if(B.resolvedType instanceof mC6){A("switch(%s){",D)("default:")("return%j",NE(B,"enum value"));for(var Z=Object.keys(B.resolvedType.values),G=0;G<Z.length;++G)A("case %i:",B.resolvedType.values[Z[G]]);A("break")("}")}else A("{")("var e=types[%i].verify(%s);",Q,D)("if(e)")("return%j+e",B.name+".")("}");else switch(B.type){case"int32":case"uint32":case"sint32":case"fixed32":case"sfixed32":A("if(!util.isInteger(%s))",D)("return%j",NE(B,"integer"));break;case"int64":case"uint64":case"sint64":case"fixed64":case"sfixed64":A("if(!util.isInteger(%s)&&!(%s&&util.isInteger(%s.low)&&util.isInteger(%s.high)))",D,D,D,D)("return%j",NE(B,"integer|Long"));break;case"float":case"double":A('if(typeof %s!=="number")',D)("return%j",NE(B,"number"));break;case"bool":A('if(typeof %s!=="boolean")',D)("return%j",NE(B,"boolean"));break;case"string":A("if(!util.isString(%s))",D)("return%j",NE(B,"string"));break;case"bytes":A('if(!(%s&&typeof %s.length==="number"||util.isString(%s)))',D,D,D)("return%j",NE(B,"buffer"));break}return A}function dC6(A,B,Q){switch(B.keyType){case"int32":case"uint32":case"sint32":case"fixed32":case"sfixed32":A("if(!util.key32Re.test(%s))",Q)("return%j",NE(B,"integer key"));break;case"int64":case"uint64":case"sint64":case"fixed64":case"sfixed64":A("if(!util.key64Re.test(%s))",Q)("return%j",NE(B,"integer|Long key"));break;case"bool":A("if(!util.key2Re.test(%s))",Q)("return%j",NE(B,"boolean key"));break}return A}function cC6(A){var B=FJ0.codegen(["m"],A.name+"$verify")('if(typeof m!=="object"||m===null)')("return%j","object expected"),Q=A.oneofsArray,D={};if(Q.length)B("var p={}");for(var Z=0;Z<A.fieldsArray.length;++Z){var G=A._fieldsArray[Z].resolve(),F="m"+FJ0.safeProp(G.name);if(G.optional)B("if(%s!=null&&m.hasOwnProperty(%j)){",F,G.name);if(G.map)B("if(!util.isObject(%s))",F)("return%j",NE(G,"object"))("var k=Object.keys(%s)",F)("for(var i=0;i<k.length;++i){"),dC6(B,G,"k[i]"),IJ0(B,G,Z,F+"[k[i]]")("}");else if(G.repeated)B("if(!Array.isArray(%s))",F)("return%j",NE(G,"array"))("for(var i=0;i<%s.length;++i){",F),IJ0(B,G,Z,F+"[i]")("}");else{if(G.partOf){var I=FJ0.safeProp(G.partOf.name);if(D[G.partOf.name]===1)B("if(p%s===1)",I)("return%j",G.partOf.name+": multiple values");D[G.partOf.name]=1,B("p%s=1",I)}IJ0(B,G,Z,F)}if(G.optional)B("}")}return B("return null")}});
var YP1=E((wu5,ts2)=>{ts2.exports=I7;var G$=Qt();((I7.prototype=Object.create(G$.prototype)).constructor=I7).className="Type";var iC6=Z$(),HJ0=eo(),FP1=o_(),nC6=QP1(),aC6=ZP1(),CJ0=GP1(),KJ0=MT1(),sC6=NT1(),FJ=$I(),rC6=zJ0(),oC6=GJ0(),tC6=YJ0(),os2=XJ0(),eC6=VJ0();function I7(A,B){G$.call(this,A,B),this.fields={},this.oneofs=void 0,this.extensions=void 0,this.reserved=void 0,this.group=void 0,this._fieldsById=null,this._fieldsArray=null,this._oneofsArray=null,this._ctor=null}Object.defineProperties(I7.prototype,{fieldsById:{get:function(){if(this._fieldsById)return this._fieldsById;this._fieldsById={};for(var A=Object.keys(this.fields),B=0;B<A.length;++B){var Q=this.fields[A[B]],D=Q.id;if(this._fieldsById[D])throw Error("duplicate id "+D+" in "+this);this._fieldsById[D]=Q}return this._fieldsById}},fieldsArray:{get:function(){return this._fieldsArray||(this._fieldsArray=FJ.toArray(this.fields))}},oneofsArray:{get:function(){return this._oneofsArray||(this._oneofsArray=FJ.toArray(this.oneofs))}},ctor:{get:function(){return this._ctor||(this.ctor=I7.generateConstructor(this)())},set:function(A){var B=A.prototype;if(!(B instanceof CJ0))(A.prototype=new CJ0).constructor=A,FJ.merge(A.prototype,B);A.$type=A.prototype.$type=this,FJ.merge(A,CJ0,!0),this._ctor=A;var Q=0;for(;Q<this.fieldsArray.length;++Q)this._fieldsArray[Q].resolve();var D={};for(Q=0;Q<this.oneofsArray.length;++Q)D[this._oneofsArray[Q].resolve().name]={get:FJ.oneOfGetter(this._oneofsArray[Q].oneof),set:FJ.oneOfSetter(this._oneofsArray[Q].oneof)};if(Q)Object.defineProperties(A.prototype,D)}}});I7.generateConstructor=function A(B){var Q=FJ.codegen(["p"],B.name);for(var D=0,Z;D<B.fieldsArray.length;++D)if((Z=B._fieldsArray[D]).map)Q("this%s={}",FJ.safeProp(Z.name));else if(Z.repeated)Q("this%s=[]",FJ.safeProp(Z.name));return Q("if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)")("this[ks[i]]=p[ks[i]]")};function IP1(A){return A._fieldsById=A._fieldsArray=A._oneofsArray=null,delete A.encode,delete A.decode,delete A.verify,A}I7.fromJSON=function A(B,Q){var D=new I7(B,Q.options);D.extensions=Q.extensions,D.reserved=Q.reserved;var Z=Object.keys(Q.fields),G=0;for(;G<Z.length;++G)D.add((typeof Q.fields[Z[G]].keyType!=="undefined"?nC6.fromJSON:FP1.fromJSON)(Z[G],Q.fields[Z[G]]));if(Q.oneofs)for(Z=Object.keys(Q.oneofs),G=0;G<Z.length;++G)D.add(HJ0.fromJSON(Z[G],Q.oneofs[Z[G]]));if(Q.nested)for(Z=Object.keys(Q.nested),G=0;G<Z.length;++G){var F=Q.nested[Z[G]];D.add((F.id!==void 0?FP1.fromJSON:F.fields!==void 0?I7.fromJSON:F.values!==void 0?iC6.fromJSON:F.methods!==void 0?aC6.fromJSON:G$.fromJSON)(Z[G],F))}if(Q.extensions&&Q.extensions.length)D.extensions=Q.extensions;if(Q.reserved&&Q.reserved.length)D.reserved=Q.reserved;if(Q.group)D.group=!0;if(Q.comment)D.comment=Q.comment;return D};I7.prototype.toJSON=function A(B){var Q=G$.prototype.toJSON.call(this,B),D=B?Boolean(B.keepComments):!1;return FJ.toObject(["options",Q&&Q.options||void 0,"oneofs",G$.arrayToJSON(this.oneofsArray,B),"fields",G$.arrayToJSON(this.fieldsArray.filter(function(Z){return!Z.declaringField}),B)||{},"extensions",this.extensions&&this.extensions.length?this.extensions:void 0,"reserved",this.reserved&&this.reserved.length?this.reserved:void 0,"group",this.group||void 0,"nested",Q&&Q.nested||void 0,"comment",D?this.comment:void 0])};I7.prototype.resolveAll=function A(){var B=this.fieldsArray,Q=0;while(Q<B.length)B[Q++].resolve();var D=this.oneofsArray;Q=0;while(Q<D.length)D[Q++].resolve();return G$.prototype.resolveAll.call(this)};I7.prototype.get=function A(B){return this.fields[B]||this.oneofs&&this.oneofs[B]||this.nested&&this.nested[B]||null};I7.prototype.add=function A(B){if(this.get(B.name))throw Error("duplicate name '"+B.name+"' in "+this);if(B instanceof FP1&&B.extend===void 0){if(this._fieldsById?this._fieldsById[B.id]:this.fieldsById[B.id])throw Error("duplicate id "+B.id+" in "+this);if(this.isReservedId(B.id))throw Error("id "+B.id+" is reserved in "+this);if(this.isReservedName(B.name))throw Error("name '"+B.name+"' is reserved in "+this);if(B.parent)B.parent.remove(B);return this.fields[B.name]=B,B.message=this,B.onAdd(this),IP1(this)}if(B instanceof HJ0){if(!this.oneofs)this.oneofs={};return this.oneofs[B.name]=B,B.onAdd(this),IP1(this)}return G$.prototype.add.call(this,B)};I7.prototype.remove=function A(B){if(B instanceof FP1&&B.extend===void 0){if(!this.fields||this.fields[B.name]!==B)throw Error(B+" is not a member of "+this);return delete this.fields[B.name],B.parent=null,B.onRemove(this),IP1(this)}if(B instanceof HJ0){if(!this.oneofs||this.oneofs[B.name]!==B)throw Error(B+" is not a member of "+this);return delete this.oneofs[B.name],B.parent=null,B.onRemove(this),IP1(this)}return G$.prototype.remove.call(this,B)};I7.prototype.isReservedId=function A(B){return G$.isReservedId(this.reserved,B)};I7.prototype.isReservedName=function A(B){return G$.isReservedName(this.reserved,B)};I7.prototype.create=function A(B){return new this.ctor(B)};I7.prototype.setup=function A(){var B=this.fullName,Q=[];for(var D=0;D<this.fieldsArray.length;++D)Q.push(this._fieldsArray[D].resolve().resolvedType);this.encode=rC6(this)({Writer:sC6,types:Q,util:FJ}),this.decode=oC6(this)({Reader:KJ0,types:Q,util:FJ}),this.verify=tC6(this)({types:Q,util:FJ}),this.fromObject=os2.fromObject(this)({types:Q,util:FJ}),this.toObject=os2.toObject(this)({types:Q,util:FJ});var Z=eC6[B];if(Z){var G=Object.create(this);G.fromObject=this.fromObject,this.fromObject=Z.fromObject.bind(G),G.toObject=this.toObject,this.toObject=Z.toObject.bind(G)}return this};I7.prototype.encode=function A(B,Q){return this.setup().encode(B,Q)};I7.prototype.encodeDelimited=function A(B,Q){return this.encode(B,Q&&Q.len?Q.fork():Q).ldelim()};I7.prototype.decode=function A(B,Q){return this.setup().decode(B,Q)};I7.prototype.decodeDelimited=function A(B){if(!(B instanceof KJ0))B=KJ0.create(B);return this.decode(B,B.uint32())};I7.prototype.verify=function A(B){return this.setup().verify(B)};I7.prototype.fromObject=function A(B){return this.setup().fromObject(B)};I7.prototype.toObject=function A(B,Q){return this.setup().toObject(B,Q)};I7.d=function A(B){return function Q(D){FJ.decorateType(D,B)}}});
var Yt2=E((Ft2)=>{Object.defineProperty(Ft2,"__esModule",{value:!0});Ft2.CompressionFilterFactory=Ft2.CompressionFilter=void 0;var SP1=J1("zlib"),Bt2=eJ0(),Jt=_6(),AE6=AX0(),BE6=F7(),QE6=(A)=>{return typeof A==="number"&&typeof Bt2.CompressionAlgorithms[A]==="string"};class p31{async writeMessage(A,B){let Q=A;if(B)Q=await this.compressMessage(Q);let D=Buffer.allocUnsafe(Q.length+5);return D.writeUInt8(B?1:0,0),D.writeUInt32BE(Q.length,1),Q.copy(D,5),D}async readMessage(A){let B=A.readUInt8(0)===1,Q=A.slice(5);if(B)Q=await this.decompressMessage(Q);return Q}}class Xt extends p31{async compressMessage(A){return A}async writeMessage(A,B){let Q=Buffer.allocUnsafe(A.length+5);return Q.writeUInt8(0,0),Q.writeUInt32BE(A.length,1),A.copy(Q,5),Q}decompressMessage(A){return Promise.reject(new Error('Received compressed message but "grpc-encoding" header was identity'))}}class Qt2 extends p31{constructor(A){super();this.maxRecvMessageLength=A}compressMessage(A){return new Promise((B,Q)=>{SP1.deflate(A,(D,Z)=>{if(D)Q(D);else B(Z)})})}decompressMessage(A){return new Promise((B,Q)=>{let D=0,Z=[],G=SP1.createInflate();G.on("data",(F)=>{if(Z.push(F),D+=F.byteLength,this.maxRecvMessageLength!==-1&&D>this.maxRecvMessageLength)G.destroy(),Q({code:Jt.Status.RESOURCE_EXHAUSTED,details:`Received message that decompresses to a size larger than ${this.maxRecvMessageLength}`})}),G.on("end",()=>{B(Buffer.concat(Z))}),G.write(A),G.end()})}}class Dt2 extends p31{constructor(A){super();this.maxRecvMessageLength=A}compressMessage(A){return new Promise((B,Q)=>{SP1.gzip(A,(D,Z)=>{if(D)Q(D);else B(Z)})})}decompressMessage(A){return new Promise((B,Q)=>{let D=0,Z=[],G=SP1.createGunzip();G.on("data",(F)=>{if(Z.push(F),D+=F.byteLength,this.maxRecvMessageLength!==-1&&D>this.maxRecvMessageLength)G.destroy(),Q({code:Jt.Status.RESOURCE_EXHAUSTED,details:`Received message that decompresses to a size larger than ${this.maxRecvMessageLength}`})}),G.on("end",()=>{B(Buffer.concat(Z))}),G.write(A),G.end()})}}class Zt2 extends p31{constructor(A){super();this.compressionName=A}compressMessage(A){return Promise.reject(new Error(`Received message compressed with unsupported compression method ${this.compressionName}`))}decompressMessage(A){return Promise.reject(new Error(`Compression method not supported: ${this.compressionName}`))}}function At2(A,B){switch(A){case"identity":return new Xt;case"deflate":return new Qt2(B);case"gzip":return new Dt2(B);default:return new Zt2(A)}}class BX0 extends AE6.BaseFilter{constructor(A,B){var Q,D,Z;super();this.sharedFilterConfig=B,this.sendCompression=new Xt,this.receiveCompression=new Xt,this.currentCompressionAlgorithm="identity";let G=A["grpc.default_compression_algorithm"];if(this.maxReceiveMessageLength=(Q=A["grpc.max_receive_message_length"])!==null&&Q!==void 0?Q:Jt.DEFAULT_MAX_RECEIVE_MESSAGE_LENGTH,this.maxSendMessageLength=(D=A["grpc.max_send_message_length"])!==null&&D!==void 0?D:Jt.DEFAULT_MAX_SEND_MESSAGE_LENGTH,G!==void 0)if(QE6(G)){let F=Bt2.CompressionAlgorithms[G],I=(Z=B.serverSupportedEncodingHeader)===null||Z===void 0?void 0:Z.split(",");if(!I||I.includes(F))this.currentCompressionAlgorithm=F,this.sendCompression=At2(this.currentCompressionAlgorithm,-1)}else BE6.log(Jt.LogVerbosity.ERROR,`Invalid value provided for grpc.default_compression_algorithm option: ${G}`)}async sendMetadata(A){let B=await A;if(B.set("grpc-accept-encoding","identity,deflate,gzip"),B.set("accept-encoding","identity"),this.currentCompressionAlgorithm==="identity")B.remove("grpc-encoding");else B.set("grpc-encoding",this.currentCompressionAlgorithm);return B}receiveMetadata(A){let B=A.get("grpc-encoding");if(B.length>0){let D=B[0];if(typeof D==="string")this.receiveCompression=At2(D,this.maxReceiveMessageLength)}A.remove("grpc-encoding");let Q=A.get("grpc-accept-encoding")[0];if(Q){if(this.sharedFilterConfig.serverSupportedEncodingHeader=Q,!Q.split(",").includes(this.currentCompressionAlgorithm))this.sendCompression=new Xt,this.currentCompressionAlgorithm="identity"}return A.remove("grpc-accept-encoding"),A}async sendMessage(A){var B;let Q=await A;if(this.maxSendMessageLength!==-1&&Q.message.length>this.maxSendMessageLength)throw{code:Jt.Status.RESOURCE_EXHAUSTED,details:`Attempted to send message with a size larger than ${this.maxSendMessageLength}`};let D;if(this.sendCompression instanceof Xt)D=!1;else D=(((B=Q.flags)!==null&&B!==void 0?B:0)&2)===0;return{message:await this.sendCompression.writeMessage(Q.message,D),flags:Q.flags}}async receiveMessage(A){return this.receiveCompression.readMessage(await A)}}Ft2.CompressionFilter=BX0;class Gt2{constructor(A,B){this.options=B,this.sharedFilterConfig={}}createFilter(){return new BX0(this.options,this.sharedFilterConfig)}}Ft2.CompressionFilterFactory=Gt2});
var Z$=E((Lu5,Wr2)=>{Wr2.exports=GM;var Ir2=du();((GM.prototype=Object.create(Ir2.prototype)).constructor=GM).className="Enum";var Yr2=Qt(),KP1=$I();function GM(A,B,Q,D,Z,G){if(Ir2.call(this,A,Q),B&&typeof B!=="object")throw TypeError("values must be an object");if(this.valuesById={},this.values=Object.create(this.valuesById),this.comment=D,this.comments=Z||{},this.valuesOptions=G,this.reserved=void 0,B){for(var F=Object.keys(B),I=0;I<F.length;++I)if(typeof B[F[I]]==="number")this.valuesById[this.values[F[I]]=B[F[I]]]=F[I]}}GM.fromJSON=function A(B,Q){var D=new GM(B,Q.values,Q.options,Q.comment,Q.comments);return D.reserved=Q.reserved,D};GM.prototype.toJSON=function A(B){var Q=B?Boolean(B.keepComments):!1;return KP1.toObject(["options",this.options,"valuesOptions",this.valuesOptions,"values",this.values,"reserved",this.reserved&&this.reserved.length?this.reserved:void 0,"comment",Q?this.comment:void 0,"comments",Q?this.comments:void 0])};GM.prototype.add=function A(B,Q,D,Z){if(!KP1.isString(B))throw TypeError("name must be a string");if(!KP1.isInteger(Q))throw TypeError("id must be an integer");if(this.values[B]!==void 0)throw Error("duplicate name '"+B+"' in "+this);if(this.isReservedId(Q))throw Error("id "+Q+" is reserved in "+this);if(this.isReservedName(B))throw Error("name '"+B+"' is reserved in "+this);if(this.valuesById[Q]!==void 0){if(!(this.options&&this.options.allow_alias))throw Error("duplicate id "+Q+" in "+this);this.values[B]=Q}else this.valuesById[this.values[B]=Q]=B;if(Z){if(this.valuesOptions===void 0)this.valuesOptions={};this.valuesOptions[B]=Z||null}return this.comments[B]=D||null,this};GM.prototype.remove=function A(B){if(!KP1.isString(B))throw TypeError("name must be a string");var Q=this.values[B];if(Q==null)throw Error("name '"+B+"' does not exist in "+this);if(delete this.valuesById[Q],delete this.values[B],delete this.comments[B],this.valuesOptions)delete this.valuesOptions[B];return this};GM.prototype.isReservedId=function A(B){return Yr2.isReservedId(this.reserved,B)};GM.prototype.isReservedName=function A(B){return Yr2.isReservedName(this.reserved,B)}});
var ZP1=E((Cu5,cs2)=>{cs2.exports=qE;var t_=Qt();((qE.prototype=Object.create(t_.prototype)).constructor=qE).className="Service";var ZJ0=DP1(),x31=$I(),bC6=sY0();function qE(A,B){t_.call(this,A,B),this.methods={},this._methodsArray=null}qE.fromJSON=function A(B,Q){var D=new qE(B,Q.options);if(Q.methods)for(var Z=Object.keys(Q.methods),G=0;G<Z.length;++G)D.add(ZJ0.fromJSON(Z[G],Q.methods[Z[G]]));if(Q.nested)D.addJSON(Q.nested);return D.comment=Q.comment,D};qE.prototype.toJSON=function A(B){var Q=t_.prototype.toJSON.call(this,B),D=B?Boolean(B.keepComments):!1;return x31.toObject(["options",Q&&Q.options||void 0,"methods",t_.arrayToJSON(this.methodsArray,B)||{},"nested",Q&&Q.nested||void 0,"comment",D?this.comment:void 0])};Object.defineProperty(qE.prototype,"methodsArray",{get:function(){return this._methodsArray||(this._methodsArray=x31.toArray(this.methods))}});function ds2(A){return A._methodsArray=null,A}qE.prototype.get=function A(B){return this.methods[B]||t_.prototype.get.call(this,B)};qE.prototype.resolveAll=function A(){var B=this.methodsArray;for(var Q=0;Q<B.length;++Q)B[Q].resolve();return t_.prototype.resolve.call(this)};qE.prototype.add=function A(B){if(this.get(B.name))throw Error("duplicate name '"+B.name+"' in "+this);if(B instanceof ZJ0)return this.methods[B.name]=B,B.parent=this,ds2(this);return t_.prototype.add.call(this,B)};qE.prototype.remove=function A(B){if(B instanceof ZJ0){if(this.methods[B.name]!==B)throw Error(B+" is not a member of "+this);return delete this.methods[B.name],B.parent=null,ds2(this)}return t_.prototype.remove.call(this,B)};qE.prototype.create=function A(B,Q,D){var Z=new bC6.Service(B,Q,D);for(var G=0,F;G<this.methodsArray.length;++G){var I=x31.lcFirst((F=this._methodsArray[G]).resolve().name).replace(/[^$\w_]/g,"");Z[I]=x31.codegen(["r","c"],x31.isReserved(I)?I+"_":I)("return this.rpcCall(m,q,s,r,c)")({m:F,q:F.resolvedRequestType.ctor,s:F.resolvedResponseType.ctor})}return Z}});
var Za2=E((Qa2)=>{Object.defineProperty(Qa2,"__esModule",{value:!0});Qa2.ResolvingLoadBalancer=void 0;var _X6=gu(),xX6=xW0(),gV=EE(),vX6=BM(),N31=s_(),bX6=q31(),vW0=_6(),fX6=GJ(),hX6=F7(),gX6=_6(),uX6=hV(),mX6=pT1(),dX6="resolving_load_balancer";function Aa2(A){hX6.trace(gX6.LogVerbosity.DEBUG,dX6,A)}var cX6=["SERVICE_AND_METHOD","SERVICE","EMPTY"];function lX6(A,B,Q,D){for(let Z of Q.name)switch(D){case"EMPTY":if(!Z.service&&!Z.method)return!0;break;case"SERVICE":if(Z.service===A&&!Z.method)return!0;break;case"SERVICE_AND_METHOD":if(Z.service===A&&Z.method===B)return!0}return!1}function pX6(A,B,Q,D){for(let Z of Q)if(lX6(A,B,Z,D))return Z;return null}function iX6(A){return{invoke(B,Q){var D,Z;let G=B.split("/").filter((Y)=>Y.length>0),F=(D=G[0])!==null&&D!==void 0?D:"",I=(Z=G[1])!==null&&Z!==void 0?Z:"";if(A&&A.methodConfig)for(let Y of cX6){let W=pX6(F,I,A.methodConfig,Y);if(W)return{methodConfig:W,pickInformation:{},status:vW0.Status.OK,dynamicFilterFactories:[]}}return{methodConfig:{name:[]},pickInformation:{},status:vW0.Status.OK,dynamicFilterFactories:[]}},unref(){}}}class Ba2{constructor(A,B,Q,D,Z){if(this.target=A,this.channelControlHelper=B,this.channelOptions=Q,this.onSuccessfulResolution=D,this.onFailedResolution=Z,this.latestChildState=gV.ConnectivityState.IDLE,this.latestChildPicker=new N31.QueuePicker(this),this.latestChildErrorMessage=null,this.currentState=gV.ConnectivityState.IDLE,this.previousServiceConfig=null,this.continueResolving=!1,Q["grpc.service_config"])this.defaultServiceConfig=xX6.validateServiceConfig(JSON.parse(Q["grpc.service_config"]));else this.defaultServiceConfig={loadBalancingConfig:[],methodConfig:[]};this.updateState(gV.ConnectivityState.IDLE,new N31.QueuePicker(this),null),this.childLoadBalancer=new mX6.ChildLoadBalancerHandler({createSubchannel:B.createSubchannel.bind(B),requestReresolution:()=>{if(this.backoffTimeout.isRunning())Aa2("requestReresolution delayed by backoff timer until "+this.backoffTimeout.getEndTime().toISOString()),this.continueResolving=!0;else this.updateResolution()},updateState:(F,I,Y)=>{this.latestChildState=F,this.latestChildPicker=I,this.latestChildErrorMessage=Y,this.updateState(F,I,Y)},addChannelzChild:B.addChannelzChild.bind(B),removeChannelzChild:B.removeChannelzChild.bind(B)}),this.innerResolver=vX6.createResolver(A,{onSuccessfulResolution:(F,I,Y,W,J)=>{var X;this.backoffTimeout.stop(),this.backoffTimeout.reset();let V=null;if(I===null)if(Y===null)this.previousServiceConfig=null,V=this.defaultServiceConfig;else if(this.previousServiceConfig===null)this.handleResolutionFailure(Y);else V=this.previousServiceConfig;else V=I,this.previousServiceConfig=I;let C=(X=V===null||V===void 0?void 0:V.loadBalancingConfig)!==null&&X!==void 0?X:[],K=_X6.selectLbConfigFromList(C,!0);if(K===null){this.handleResolutionFailure({code:vW0.Status.UNAVAILABLE,details:"All load balancer options in service config are not compatible",metadata:new fX6.Metadata}),W===null||W===void 0||W.unref();return}this.childLoadBalancer.updateAddressList(F,K,Object.assign(Object.assign({},this.channelOptions),J));let H=V!==null&&V!==void 0?V:this.defaultServiceConfig;this.onSuccessfulResolution(H,W!==null&&W!==void 0?W:iX6(H))},onError:(F)=>{this.handleResolutionFailure(F)}},Q);let G={initialDelay:Q["grpc.initial_reconnect_backoff_ms"],maxDelay:Q["grpc.max_reconnect_backoff_ms"]};this.backoffTimeout=new bX6.BackoffTimeout(()=>{if(this.continueResolving)this.updateResolution(),this.continueResolving=!1;else this.updateState(this.latestChildState,this.latestChildPicker,this.latestChildErrorMessage)},G),this.backoffTimeout.unref()}updateResolution(){if(this.innerResolver.updateResolution(),this.currentState===gV.ConnectivityState.IDLE)this.updateState(gV.ConnectivityState.CONNECTING,this.latestChildPicker,this.latestChildErrorMessage);this.backoffTimeout.runOnce()}updateState(A,B,Q){if(Aa2(uX6.uriToString(this.target)+" "+gV.ConnectivityState[this.currentState]+" -> "+gV.ConnectivityState[A]),A===gV.ConnectivityState.IDLE)B=new N31.QueuePicker(this,B);this.currentState=A,this.channelControlHelper.updateState(A,B,Q)}handleResolutionFailure(A){if(this.latestChildState===gV.ConnectivityState.IDLE)this.updateState(gV.ConnectivityState.TRANSIENT_FAILURE,new N31.UnavailablePicker(A),A.details),this.onFailedResolution(A)}exitIdle(){if(this.currentState===gV.ConnectivityState.IDLE||this.currentState===gV.ConnectivityState.TRANSIENT_FAILURE)if(this.backoffTimeout.isRunning())this.continueResolving=!0;else this.updateResolution();this.childLoadBalancer.exitIdle()}updateAddressList(A,B){throw new Error("updateAddressList not supported on ResolvingLoadBalancer")}resetBackoff(){this.backoffTimeout.reset(),this.childLoadBalancer.resetBackoff()}destroy(){this.childLoadBalancer.destroy(),this.innerResolver.destroy(),this.backoffTimeout.reset(),this.backoffTimeout.stop(),this.latestChildState=gV.ConnectivityState.IDLE,this.latestChildPicker=new N31.QueuePicker(this),this.currentState=gV.ConnectivityState.IDLE,this.previousServiceConfig=null,this.continueResolving=!1}getTypeName(){return"resolving_load_balancer"}}Qa2.ResolvingLoadBalancer=Ba2});
var _6=E((Kn2)=>{Object.defineProperty(Kn2,"__esModule",{value:!0});Kn2.DEFAULT_MAX_RECEIVE_MESSAGE_LENGTH=Kn2.DEFAULT_MAX_SEND_MESSAGE_LENGTH=Kn2.Propagate=Kn2.LogVerbosity=Kn2.Status=void 0;var Xn2;(function(A){A[A.OK=0]="OK",A[A.CANCELLED=1]="CANCELLED",A[A.UNKNOWN=2]="UNKNOWN",A[A.INVALID_ARGUMENT=3]="INVALID_ARGUMENT",A[A.DEADLINE_EXCEEDED=4]="DEADLINE_EXCEEDED",A[A.NOT_FOUND=5]="NOT_FOUND",A[A.ALREADY_EXISTS=6]="ALREADY_EXISTS",A[A.PERMISSION_DENIED=7]="PERMISSION_DENIED",A[A.RESOURCE_EXHAUSTED=8]="RESOURCE_EXHAUSTED",A[A.FAILED_PRECONDITION=9]="FAILED_PRECONDITION",A[A.ABORTED=10]="ABORTED",A[A.OUT_OF_RANGE=11]="OUT_OF_RANGE",A[A.UNIMPLEMENTED=12]="UNIMPLEMENTED",A[A.INTERNAL=13]="INTERNAL",A[A.UNAVAILABLE=14]="UNAVAILABLE",A[A.DATA_LOSS=15]="DATA_LOSS",A[A.UNAUTHENTICATED=16]="UNAUTHENTICATED"})(Xn2||(Kn2.Status=Xn2={}));var Vn2;(function(A){A[A.DEBUG=0]="DEBUG",A[A.INFO=1]="INFO",A[A.ERROR=2]="ERROR",A[A.NONE=3]="NONE"})(Vn2||(Kn2.LogVerbosity=Vn2={}));var Cn2;(function(A){A[A.DEADLINE=1]="DEADLINE",A[A.CENSUS_STATS_CONTEXT=2]="CENSUS_STATS_CONTEXT",A[A.CENSUS_TRACING_CONTEXT=4]="CENSUS_TRACING_CONTEXT",A[A.CANCELLATION=8]="CANCELLATION",A[A.DEFAULTS=65535]="DEFAULTS"})(Cn2||(Kn2.Propagate=Cn2={}));Kn2.DEFAULT_MAX_SEND_MESSAGE_LENGTH=-1;Kn2.DEFAULT_MAX_RECEIVE_MESSAGE_LENGTH=4194304});
var _r2=E((ku5,cK6)=>{cK6.exports={nested:{google:{nested:{protobuf:{nested:{SourceContext:{fields:{fileName:{type:"string",id:1}}}}}}}}}});
var aX0=E((oP1)=>{Object.defineProperty(oP1,"__esModule",{value:!0});oP1.createOtlpGrpcExportDelegate=oP1.convertLegacyOtlpGrpcOptions=void 0;var Qq6=m1B();Object.defineProperty(oP1,"convertLegacyOtlpGrpcOptions",{enumerable:!0,get:function(){return Qq6.convertLegacyOtlpGrpcOptions}});var Dq6=l1B();Object.defineProperty(oP1,"createOtlpGrpcExportDelegate",{enumerable:!0,get:function(){return Dq6.createOtlpGrpcExportDelegate}})});
var bT1=E((Mn2)=>{Object.defineProperty(Mn2,"__esModule",{value:!0});Mn2.CallCredentials=void 0;var MW0=GJ();function HJ6(A){return"getRequestHeaders"in A&&typeof A.getRequestHeaders==="function"}class po{static createFromMetadataGenerator(A){return new RW0(A)}static createFromGoogleCredential(A){return po.createFromMetadataGenerator((B,Q)=>{let D;if(HJ6(A))D=A.getRequestHeaders(B.service_url);else D=new Promise((Z,G)=>{A.getRequestMetadata(B.service_url,(F,I)=>{if(F){G(F);return}if(!I){G(new Error("Headers not set by metadata plugin"));return}Z(I)})});D.then((Z)=>{let G=new MW0.Metadata;for(let F of Object.keys(Z))G.add(F,Z[F]);Q(null,G)},(Z)=>{Q(Z)})})}static createEmpty(){return new OW0}}Mn2.CallCredentials=po;class vT1 extends po{constructor(A){super();this.creds=A}async generateMetadata(A){let B=new MW0.Metadata,Q=await Promise.all(this.creds.map((D)=>D.generateMetadata(A)));for(let D of Q)B.merge(D);return B}compose(A){return new vT1(this.creds.concat([A]))}_equals(A){if(this===A)return!0;if(A instanceof vT1)return this.creds.every((B,Q)=>B._equals(A.creds[Q]));else return!1}}class RW0 extends po{constructor(A){super();this.metadataGenerator=A}generateMetadata(A){return new Promise((B,Q)=>{this.metadataGenerator(A,(D,Z)=>{if(Z!==void 0)B(Z);else Q(D)})})}compose(A){return new vT1([this,A])}_equals(A){if(this===A)return!0;if(A instanceof RW0)return this.metadataGenerator===A.metadataGenerator;else return!1}}class OW0 extends po{generateMetadata(A){return Promise.resolve(new MW0.Metadata)}compose(A){return A}_equals(A){return A instanceof OW0}}});
var be2=E((xe2)=>{Object.defineProperty(xe2,"__esModule",{value:!0});xe2.FileWatcherCertificateProvider=void 0;var eU6=J1("fs"),Aw6=F7(),Bw6=_6(),Qw6=J1("util"),Dw6="certificate_provider";function cP1(A){Aw6.trace(Bw6.LogVerbosity.DEBUG,Dw6,A)}var OX0=Qw6.promisify(eU6.readFile);class _e2{constructor(A){if(this.config=A,this.refreshTimer=null,this.fileResultPromise=null,this.latestCaUpdate=void 0,this.caListeners=new Set,this.latestIdentityUpdate=void 0,this.identityListeners=new Set,this.lastUpdateTime=null,A.certificateFile===void 0!==(A.privateKeyFile===void 0))throw new Error("certificateFile and privateKeyFile must be set or unset together");if(A.certificateFile===void 0&&A.caCertificateFile===void 0)throw new Error("At least one of certificateFile and caCertificateFile must be set");cP1("File watcher constructed with config "+JSON.stringify(A))}updateCertificates(){if(this.fileResultPromise)return;this.fileResultPromise=Promise.allSettled([this.config.certificateFile?OX0(this.config.certificateFile):Promise.reject(),this.config.privateKeyFile?OX0(this.config.privateKeyFile):Promise.reject(),this.config.caCertificateFile?OX0(this.config.caCertificateFile):Promise.reject()]),this.fileResultPromise.then(([A,B,Q])=>{if(!this.refreshTimer)return;if(cP1("File watcher read certificates certificate "+A.status+", privateKey "+B.status+", CA certificate "+Q.status),this.lastUpdateTime=new Date,this.fileResultPromise=null,A.status==="fulfilled"&&B.status==="fulfilled")this.latestIdentityUpdate={certificate:A.value,privateKey:B.value};else this.latestIdentityUpdate=null;if(Q.status==="fulfilled")this.latestCaUpdate={caCertificate:Q.value};else this.latestCaUpdate=null;for(let D of this.identityListeners)D(this.latestIdentityUpdate);for(let D of this.caListeners)D(this.latestCaUpdate)}),cP1("File watcher initiated certificate update")}maybeStartWatchingFiles(){if(!this.refreshTimer){let A=this.lastUpdateTime?new Date().getTime()-this.lastUpdateTime.getTime():1/0;if(A>this.config.refreshIntervalMs)this.updateCertificates();if(A>this.config.refreshIntervalMs*2)this.latestCaUpdate=void 0,this.latestIdentityUpdate=void 0;this.refreshTimer=setInterval(()=>this.updateCertificates(),this.config.refreshIntervalMs),cP1("File watcher started watching")}}maybeStopWatchingFiles(){if(this.caListeners.size===0&&this.identityListeners.size===0){if(this.fileResultPromise=null,this.refreshTimer)clearInterval(this.refreshTimer),this.refreshTimer=null}}addCaCertificateListener(A){if(this.caListeners.add(A),this.maybeStartWatchingFiles(),this.latestCaUpdate!==void 0)process.nextTick(A,this.latestCaUpdate)}removeCaCertificateListener(A){this.caListeners.delete(A),this.maybeStopWatchingFiles()}addIdentityCertificateListener(A){if(this.identityListeners.add(A),this.maybeStartWatchingFiles(),this.latestIdentityUpdate!==void 0)process.nextTick(A,this.latestIdentityUpdate)}removeIdentityCertificateListener(A){this.identityListeners.delete(A),this.maybeStopWatchingFiles()}}xe2.FileWatcherCertificateProvider=_e2});
var bo2=E((xo2)=>{Object.defineProperty(xo2,"__esModule",{value:!0});xo2.Http2SubchannelCall=void 0;var NP=J1("http2"),$z6=J1("os"),F3=_6(),LP=GJ(),qz6=pJ0(),Nz6=F7(),Lz6=_6(),Mz6="subchannel_call";function Rz6(A){for(let[B,Q]of Object.entries($z6.constants.errno))if(Q===A)return B;return"Unknown system error "+A}function iJ0(A){let B=`Received HTTP status code ${A}`,Q;switch(A){case 400:Q=F3.Status.INTERNAL;break;case 401:Q=F3.Status.UNAUTHENTICATED;break;case 403:Q=F3.Status.PERMISSION_DENIED;break;case 404:Q=F3.Status.UNIMPLEMENTED;break;case 429:case 502:case 503:case 504:Q=F3.Status.UNAVAILABLE;break;default:Q=F3.Status.UNKNOWN}return{code:Q,details:B,metadata:new LP.Metadata}}class _o2{constructor(A,B,Q,D,Z){var G;this.http2Stream=A,this.callEventTracker=B,this.listener=Q,this.transport=D,this.callId=Z,this.isReadFilterPending=!1,this.isPushPending=!1,this.canPush=!1,this.readsClosed=!1,this.statusOutput=!1,this.unpushedReadMessages=[],this.finalStatus=null,this.internalError=null,this.serverEndedCall=!1,this.connectionDropped=!1;let F=(G=D.getOptions()["grpc.max_receive_message_length"])!==null&&G!==void 0?G:F3.DEFAULT_MAX_RECEIVE_MESSAGE_LENGTH;this.decoder=new qz6.StreamDecoder(F),A.on("response",(I,Y)=>{let W="";for(let J of Object.keys(I))W+="\t\t"+J+": "+I[J]+`
`;if(this.trace(`Received server headers:
`+W),this.httpStatusCode=I[":status"],Y&NP.constants.NGHTTP2_FLAG_END_STREAM)this.handleTrailers(I);else{let J;try{J=LP.Metadata.fromHttp2Headers(I)}catch(X){this.endCall({code:F3.Status.UNKNOWN,details:X.message,metadata:new LP.Metadata});return}this.listener.onReceiveMetadata(J)}}),A.on("trailers",(I)=>{this.handleTrailers(I)}),A.on("data",(I)=>{if(this.statusOutput)return;this.trace("receive HTTP/2 data frame of length "+I.length);let Y;try{Y=this.decoder.write(I)}catch(W){if(this.httpStatusCode!==void 0&&this.httpStatusCode!==200){let J=iJ0(this.httpStatusCode);this.cancelWithStatus(J.code,J.details)}else this.cancelWithStatus(F3.Status.RESOURCE_EXHAUSTED,W.message);return}for(let W of Y)this.trace("parsed message of length "+W.length),this.callEventTracker.addMessageReceived(),this.tryPush(W)}),A.on("end",()=>{this.readsClosed=!0,this.maybeOutputStatus()}),A.on("close",()=>{this.serverEndedCall=!0,process.nextTick(()=>{var I;if(this.trace("HTTP/2 stream closed with code "+A.rstCode),((I=this.finalStatus)===null||I===void 0?void 0:I.code)===F3.Status.OK)return;let Y,W="";switch(A.rstCode){case NP.constants.NGHTTP2_NO_ERROR:if(this.finalStatus!==null)return;if(this.httpStatusCode&&this.httpStatusCode!==200){let J=iJ0(this.httpStatusCode);Y=J.code,W=J.details}else Y=F3.Status.INTERNAL,W=`Received RST_STREAM with code ${A.rstCode} (Call ended without gRPC status)`;break;case NP.constants.NGHTTP2_REFUSED_STREAM:Y=F3.Status.UNAVAILABLE,W="Stream refused by server";break;case NP.constants.NGHTTP2_CANCEL:if(this.connectionDropped)Y=F3.Status.UNAVAILABLE,W="Connection dropped";else Y=F3.Status.CANCELLED,W="Call cancelled";break;case NP.constants.NGHTTP2_ENHANCE_YOUR_CALM:Y=F3.Status.RESOURCE_EXHAUSTED,W="Bandwidth exhausted or memory limit exceeded";break;case NP.constants.NGHTTP2_INADEQUATE_SECURITY:Y=F3.Status.PERMISSION_DENIED,W="Protocol not secure enough";break;case NP.constants.NGHTTP2_INTERNAL_ERROR:if(Y=F3.Status.INTERNAL,this.internalError===null)W=`Received RST_STREAM with code ${A.rstCode} (Internal server error)`;else if(this.internalError.code==="ECONNRESET"||this.internalError.code==="ETIMEDOUT")Y=F3.Status.UNAVAILABLE,W=this.internalError.message;else W=`Received RST_STREAM with code ${A.rstCode} triggered by internal client error: ${this.internalError.message}`;break;default:Y=F3.Status.INTERNAL,W=`Received RST_STREAM with code ${A.rstCode}`}this.endCall({code:Y,details:W,metadata:new LP.Metadata,rstCode:A.rstCode})})}),A.on("error",(I)=>{if(I.code!=="ERR_HTTP2_STREAM_ERROR")this.trace("Node error event: message="+I.message+" code="+I.code+" errno="+Rz6(I.errno)+" syscall="+I.syscall),this.internalError=I;this.callEventTracker.onStreamEnd(!1)})}getDeadlineInfo(){return[`remote_addr=${this.getPeer()}`]}onDisconnect(){this.connectionDropped=!0,setImmediate(()=>{this.endCall({code:F3.Status.UNAVAILABLE,details:"Connection dropped",metadata:new LP.Metadata})})}outputStatus(){if(!this.statusOutput)this.statusOutput=!0,this.trace("ended with status: code="+this.finalStatus.code+' details="'+this.finalStatus.details+'"'),this.callEventTracker.onCallEnd(this.finalStatus),process.nextTick(()=>{this.listener.onReceiveStatus(this.finalStatus)}),this.http2Stream.resume()}trace(A){Nz6.trace(Lz6.LogVerbosity.DEBUG,Mz6,"["+this.callId+"] "+A)}endCall(A){if(this.finalStatus===null||this.finalStatus.code===F3.Status.OK)this.finalStatus=A,this.maybeOutputStatus();this.destroyHttp2Stream()}maybeOutputStatus(){if(this.finalStatus!==null){if(this.finalStatus.code!==F3.Status.OK||this.readsClosed&&this.unpushedReadMessages.length===0&&!this.isReadFilterPending&&!this.isPushPending)this.outputStatus()}}push(A){this.trace("pushing to reader message of length "+(A instanceof Buffer?A.length:null)),this.canPush=!1,this.isPushPending=!0,process.nextTick(()=>{if(this.isPushPending=!1,this.statusOutput)return;this.listener.onReceiveMessage(A),this.maybeOutputStatus()})}tryPush(A){if(this.canPush)this.http2Stream.pause(),this.push(A);else this.trace("unpushedReadMessages.push message of length "+A.length),this.unpushedReadMessages.push(A)}handleTrailers(A){this.serverEndedCall=!0,this.callEventTracker.onStreamEnd(!0);let B="";for(let G of Object.keys(A))B+="\t\t"+G+": "+A[G]+`
`;this.trace(`Received server trailers:
`+B);let Q;try{Q=LP.Metadata.fromHttp2Headers(A)}catch(G){Q=new LP.Metadata}let D=Q.getMap(),Z;if(typeof D["grpc-status"]==="string"){let G=Number(D["grpc-status"]);this.trace("received status code "+G+" from server"),Q.remove("grpc-status");let F="";if(typeof D["grpc-message"]==="string"){try{F=decodeURI(D["grpc-message"])}catch(I){F=D["grpc-message"]}Q.remove("grpc-message"),this.trace('received status details string "'+F+'" from server')}Z={code:G,details:F,metadata:Q}}else if(this.httpStatusCode)Z=iJ0(this.httpStatusCode),Z.metadata=Q;else Z={code:F3.Status.UNKNOWN,details:"No status information received",metadata:Q};this.endCall(Z)}destroyHttp2Stream(){var A;if(this.http2Stream.destroyed)return;if(this.serverEndedCall)this.http2Stream.end();else{let B;if(((A=this.finalStatus)===null||A===void 0?void 0:A.code)===F3.Status.OK)B=NP.constants.NGHTTP2_NO_ERROR;else B=NP.constants.NGHTTP2_CANCEL;this.trace("close http2 stream with code "+B),this.http2Stream.close(B)}}cancelWithStatus(A,B){this.trace("cancelWithStatus code: "+A+' details: "'+B+'"'),this.endCall({code:A,details:B,metadata:new LP.Metadata})}getStatus(){return this.finalStatus}getPeer(){return this.transport.getPeerName()}getCallNumber(){return this.callId}startRead(){if(this.finalStatus!==null&&this.finalStatus.code!==F3.Status.OK){this.readsClosed=!0,this.maybeOutputStatus();return}if(this.canPush=!0,this.unpushedReadMessages.length>0){let A=this.unpushedReadMessages.shift();this.push(A);return}this.http2Stream.resume()}sendMessageWithContext(A,B){this.trace("write() called with message of length "+B.length);let Q=(D)=>{process.nextTick(()=>{var Z;let G=F3.Status.UNAVAILABLE;if((D===null||D===void 0?void 0:D.code)==="ERR_STREAM_WRITE_AFTER_END")G=F3.Status.INTERNAL;if(D)this.cancelWithStatus(G,`Write error: ${D.message}`);(Z=A.callback)===null||Z===void 0||Z.call(A)})};this.trace("sending data chunk of length "+B.length),this.callEventTracker.addMessageSent();try{this.http2Stream.write(B,Q)}catch(D){this.endCall({code:F3.Status.UNAVAILABLE,details:`Write failed with error ${D.message}`,metadata:new LP.Metadata})}}halfClose(){this.trace("end() called"),this.trace("calling end() on HTTP/2 stream"),this.http2Stream.end()}}xo2.Http2SubchannelCall=_o2});
var cJ0=E((Mo2)=>{Object.defineProperty(Mo2,"__esModule",{value:!0});Mo2.DEFAULT_PORT=void 0;Mo2.setup=Zz6;var $o2=BM(),uJ0=J1("dns"),tH6=xW0(),mJ0=_6(),dJ0=GJ(),eH6=F7(),Az6=_6(),Dx=hV(),qo2=J1("net"),Bz6=q31(),No2=wo2(),Qz6="dns_resolver";function XM(A){eH6.trace(Az6.LogVerbosity.DEBUG,Qz6,A)}Mo2.DEFAULT_PORT=443;var Dz6=30000;class Lo2{constructor(A,B,Q){var D,Z,G;if(this.target=A,this.listener=B,this.pendingLookupPromise=null,this.pendingTxtPromise=null,this.latestLookupResult=null,this.latestServiceConfig=null,this.latestServiceConfigError=null,this.continueResolving=!1,this.isNextResolutionTimerRunning=!1,this.isServiceConfigEnabled=!0,this.returnedIpResult=!1,this.alternativeResolver=new uJ0.promises.Resolver,XM("Resolver constructed for target "+Dx.uriToString(A)),A.authority)this.alternativeResolver.setServers([A.authority]);let F=Dx.splitHostPort(A.path);if(F===null)this.ipResult=null,this.dnsHostname=null,this.port=null;else if(qo2.isIPv4(F.host)||qo2.isIPv6(F.host))this.ipResult=[{addresses:[{host:F.host,port:(D=F.port)!==null&&D!==void 0?D:Mo2.DEFAULT_PORT}]}],this.dnsHostname=null,this.port=null;else this.ipResult=null,this.dnsHostname=F.host,this.port=(Z=F.port)!==null&&Z!==void 0?Z:Mo2.DEFAULT_PORT;if(this.percentage=Math.random()*100,Q["grpc.service_config_disable_resolution"]===1)this.isServiceConfigEnabled=!1;this.defaultResolutionError={code:mJ0.Status.UNAVAILABLE,details:`Name resolution failed for target ${Dx.uriToString(this.target)}`,metadata:new dJ0.Metadata};let I={initialDelay:Q["grpc.initial_reconnect_backoff_ms"],maxDelay:Q["grpc.max_reconnect_backoff_ms"]};this.backoff=new Bz6.BackoffTimeout(()=>{if(this.continueResolving)this.startResolutionWithBackoff()},I),this.backoff.unref(),this.minTimeBetweenResolutionsMs=(G=Q["grpc.dns_min_time_between_resolutions_ms"])!==null&&G!==void 0?G:Dz6,this.nextResolutionTimer=setTimeout(()=>{},0),clearTimeout(this.nextResolutionTimer)}startResolution(){if(this.ipResult!==null){if(!this.returnedIpResult)XM("Returning IP address for target "+Dx.uriToString(this.target)),setImmediate(()=>{this.listener.onSuccessfulResolution(this.ipResult,null,null,null,{})}),this.returnedIpResult=!0;this.backoff.stop(),this.backoff.reset(),this.stopNextResolutionTimer();return}if(this.dnsHostname===null)XM("Failed to parse DNS address "+Dx.uriToString(this.target)),setImmediate(()=>{this.listener.onError({code:mJ0.Status.UNAVAILABLE,details:`Failed to parse DNS address ${Dx.uriToString(this.target)}`,metadata:new dJ0.Metadata})}),this.stopNextResolutionTimer();else{if(this.pendingLookupPromise!==null)return;XM("Looking up DNS hostname "+this.dnsHostname),this.latestLookupResult=null;let A=this.dnsHostname;if(this.pendingLookupPromise=this.lookup(A),this.pendingLookupPromise.then((B)=>{if(this.pendingLookupPromise===null)return;this.pendingLookupPromise=null,this.backoff.reset(),this.backoff.stop(),this.latestLookupResult=B.map((D)=>({addresses:[D]}));let Q="["+B.map((D)=>D.host+":"+D.port).join(",")+"]";if(XM("Resolved addresses for target "+Dx.uriToString(this.target)+": "+Q),this.latestLookupResult.length===0){this.listener.onError(this.defaultResolutionError);return}this.listener.onSuccessfulResolution(this.latestLookupResult,this.latestServiceConfig,this.latestServiceConfigError,null,{})},(B)=>{if(this.pendingLookupPromise===null)return;XM("Resolution error for target "+Dx.uriToString(this.target)+": "+B.message),this.pendingLookupPromise=null,this.stopNextResolutionTimer(),this.listener.onError(this.defaultResolutionError)}),this.isServiceConfigEnabled&&this.pendingTxtPromise===null)this.pendingTxtPromise=this.resolveTxt(A),this.pendingTxtPromise.then((B)=>{if(this.pendingTxtPromise===null)return;this.pendingTxtPromise=null;try{this.latestServiceConfig=tH6.extractAndSelectServiceConfig(B,this.percentage)}catch(Q){this.latestServiceConfigError={code:mJ0.Status.UNAVAILABLE,details:`Parsing service config failed with error ${Q.message}`,metadata:new dJ0.Metadata}}if(this.latestLookupResult!==null)this.listener.onSuccessfulResolution(this.latestLookupResult,this.latestServiceConfig,this.latestServiceConfigError,null,{})},(B)=>{})}}async lookup(A){if(No2.GRPC_NODE_USE_ALTERNATIVE_RESOLVER){XM("Using alternative DNS resolver.");let Q=await Promise.allSettled([this.alternativeResolver.resolve4(A),this.alternativeResolver.resolve6(A)]);if(Q.every((D)=>D.status==="rejected"))throw new Error(Q[0].reason);return Q.reduce((D,Z)=>{return Z.status==="fulfilled"?[...D,...Z.value]:D},[]).map((D)=>({host:D,port:+this.port}))}return(await uJ0.promises.lookup(A,{all:!0})).map((Q)=>({host:Q.address,port:+this.port}))}async resolveTxt(A){if(No2.GRPC_NODE_USE_ALTERNATIVE_RESOLVER)return XM("Using alternative DNS resolver."),this.alternativeResolver.resolveTxt(A);return uJ0.promises.resolveTxt(A)}startNextResolutionTimer(){var A,B;clearTimeout(this.nextResolutionTimer),this.nextResolutionTimer=setTimeout(()=>{if(this.stopNextResolutionTimer(),this.continueResolving)this.startResolutionWithBackoff()},this.minTimeBetweenResolutionsMs),(B=(A=this.nextResolutionTimer).unref)===null||B===void 0||B.call(A),this.isNextResolutionTimerRunning=!0}stopNextResolutionTimer(){clearTimeout(this.nextResolutionTimer),this.isNextResolutionTimerRunning=!1}startResolutionWithBackoff(){if(this.pendingLookupPromise===null)this.continueResolving=!1,this.backoff.runOnce(),this.startNextResolutionTimer(),this.startResolution()}updateResolution(){if(this.pendingLookupPromise===null)if(this.isNextResolutionTimerRunning||this.backoff.isRunning()){if(this.isNextResolutionTimerRunning)XM('resolution update delayed by "min time between resolutions" rate limit');else XM("resolution update delayed by backoff timer until "+this.backoff.getEndTime().toISOString());this.continueResolving=!0}else this.startResolutionWithBackoff()}destroy(){this.continueResolving=!1,this.backoff.reset(),this.backoff.stop(),this.stopNextResolutionTimer(),this.pendingLookupPromise=null,this.pendingTxtPromise=null,this.latestLookupResult=null,this.latestServiceConfig=null,this.latestServiceConfigError=null,this.returnedIpResult=!1}static getDefaultAuthority(A){return A.path}}function Zz6(){$o2.registerResolver("dns",Lo2),$o2.registerDefaultScheme("dns")}});
var cW0=E((ut2)=>{Object.defineProperty(ut2,"__esModule",{value:!0});ut2.ChannelImplementation=void 0;var oE6=w31(),tE6=IX0();class gt2{constructor(A,B,Q){if(typeof A!=="string")throw new TypeError("Channel target must be a string");if(!(B instanceof oE6.ChannelCredentials))throw new TypeError("Channel credentials must be a ChannelCredentials object");if(Q){if(typeof Q!=="object")throw new TypeError("Channel options must be an object")}this.internalChannel=new tE6.InternalChannel(A,B,Q)}close(){this.internalChannel.close()}getTarget(){return this.internalChannel.getTarget()}getConnectivityState(A){return this.internalChannel.getConnectivityState(A)}watchConnectivityState(A,B,Q){this.internalChannel.watchConnectivityState(A,B,Q)}getChannelzRef(){return this.internalChannel.getChannelzRef()}createCall(A,B,Q,D,Z){if(typeof A!=="string")throw new TypeError("Channel#createCall: method must be a string");if(!(typeof B==="number"||B instanceof Date))throw new TypeError("Channel#createCall: deadline must be a number or Date");return this.internalChannel.createCall(A,B,Q,D,Z)}}ut2.ChannelImplementation=gt2});
var dP1=E((ye2)=>{Object.defineProperty(ye2,"__esModule",{value:!0});ye2.LeafLoadBalancer=ye2.PickFirstLoadBalancer=ye2.PickFirstLoadBalancingConfig=void 0;ye2.shuffled=Pe2;ye2.setup=aU6;var RX0=gu(),AF=EE(),su=s_(),Me2=UE(),dU6=F7(),cU6=_6(),Re2=UE(),Oe2=J1("net"),lU6="pick_first";function s31(A){dU6.trace(cU6.LogVerbosity.DEBUG,lU6,A)}var r31="pick_first",pU6=250;class Et{constructor(A){this.shuffleAddressList=A}getLoadBalancerName(){return r31}toJsonObject(){return{[r31]:{shuffleAddressList:this.shuffleAddressList}}}getShuffleAddressList(){return this.shuffleAddressList}static createFromJson(A){if("shuffleAddressList"in A&&typeof A.shuffleAddressList!=="boolean")throw new Error("pick_first config field shuffleAddressList must be a boolean if provided");return new Et(A.shuffleAddressList===!0)}}ye2.PickFirstLoadBalancingConfig=Et;class Te2{constructor(A){this.subchannel=A}pick(A){return{pickResultType:su.PickResultType.COMPLETE,subchannel:this.subchannel,status:null,onCallStarted:null,onCallEnded:null}}}function Pe2(A){let B=A.slice();for(let Q=B.length-1;Q>1;Q--){let D=Math.floor(Math.random()*(Q+1)),Z=B[Q];B[Q]=B[D],B[D]=Z}return B}function iU6(A){if(A.length===0)return[];let B=[],Q=[],D=[],Z=Re2.isTcpSubchannelAddress(A[0])&&Oe2.isIPv6(A[0].host);for(let I of A)if(Re2.isTcpSubchannelAddress(I)&&Oe2.isIPv6(I.host))Q.push(I);else D.push(I);let G=Z?Q:D,F=Z?D:Q;for(let I=0;I<Math.max(G.length,F.length);I++){if(I<G.length)B.push(G[I]);if(I<F.length)B.push(F[I])}return B}var Se2="grpc-node.internal.pick-first.report_health_status";class mP1{constructor(A){this.channelControlHelper=A,this.children=[],this.currentState=AF.ConnectivityState.IDLE,this.currentSubchannelIndex=0,this.currentPick=null,this.subchannelStateListener=(B,Q,D,Z,G)=>{this.onSubchannelStateUpdate(B,Q,D,G)},this.pickedSubchannelHealthListener=()=>this.calculateAndReportNewState(),this.stickyTransientFailureMode=!1,this.reportHealthStatus=!1,this.lastError=null,this.latestAddressList=null,this.latestOptions={},this.connectionDelayTimeout=setTimeout(()=>{},0),clearTimeout(this.connectionDelayTimeout)}allChildrenHaveReportedTF(){return this.children.every((A)=>A.hasReportedTransientFailure)}resetChildrenReportedTF(){this.children.every((A)=>A.hasReportedTransientFailure=!1)}calculateAndReportNewState(){var A;if(this.currentPick)if(this.reportHealthStatus&&!this.currentPick.isHealthy()){let B=`Picked subchannel ${this.currentPick.getAddress()} is unhealthy`;this.updateState(AF.ConnectivityState.TRANSIENT_FAILURE,new su.UnavailablePicker({details:B}),B)}else this.updateState(AF.ConnectivityState.READY,new Te2(this.currentPick),null);else if(((A=this.latestAddressList)===null||A===void 0?void 0:A.length)===0){let B=`No connection established. Last error: ${this.lastError}`;this.updateState(AF.ConnectivityState.TRANSIENT_FAILURE,new su.UnavailablePicker({details:B}),B)}else if(this.children.length===0)this.updateState(AF.ConnectivityState.IDLE,new su.QueuePicker(this),null);else if(this.stickyTransientFailureMode){let B=`No connection established. Last error: ${this.lastError}`;this.updateState(AF.ConnectivityState.TRANSIENT_FAILURE,new su.UnavailablePicker({details:B}),B)}else this.updateState(AF.ConnectivityState.CONNECTING,new su.QueuePicker(this),null)}requestReresolution(){this.channelControlHelper.requestReresolution()}maybeEnterStickyTransientFailureMode(){if(!this.allChildrenHaveReportedTF())return;if(this.requestReresolution(),this.resetChildrenReportedTF(),this.stickyTransientFailureMode){this.calculateAndReportNewState();return}this.stickyTransientFailureMode=!0;for(let{subchannel:A}of this.children)A.startConnecting();this.calculateAndReportNewState()}removeCurrentPick(){if(this.currentPick!==null)this.currentPick.removeConnectivityStateListener(this.subchannelStateListener),this.channelControlHelper.removeChannelzChild(this.currentPick.getChannelzRef()),this.currentPick.removeHealthStateWatcher(this.pickedSubchannelHealthListener),this.currentPick.unref(),this.currentPick=null}onSubchannelStateUpdate(A,B,Q,D){var Z;if((Z=this.currentPick)===null||Z===void 0?void 0:Z.realSubchannelEquals(A)){if(Q!==AF.ConnectivityState.READY)this.removeCurrentPick(),this.calculateAndReportNewState();return}for(let[G,F]of this.children.entries())if(A.realSubchannelEquals(F.subchannel)){if(Q===AF.ConnectivityState.READY)this.pickSubchannel(F.subchannel);if(Q===AF.ConnectivityState.TRANSIENT_FAILURE){if(F.hasReportedTransientFailure=!0,D)this.lastError=D;if(this.maybeEnterStickyTransientFailureMode(),G===this.currentSubchannelIndex)this.startNextSubchannelConnecting(G+1)}F.subchannel.startConnecting();return}}startNextSubchannelConnecting(A){clearTimeout(this.connectionDelayTimeout);for(let[B,Q]of this.children.entries())if(B>=A){let D=Q.subchannel.getConnectivityState();if(D===AF.ConnectivityState.IDLE||D===AF.ConnectivityState.CONNECTING){this.startConnecting(B);return}}this.maybeEnterStickyTransientFailureMode()}startConnecting(A){var B,Q;if(clearTimeout(this.connectionDelayTimeout),this.currentSubchannelIndex=A,this.children[A].subchannel.getConnectivityState()===AF.ConnectivityState.IDLE)s31("Start connecting to subchannel with address "+this.children[A].subchannel.getAddress()),process.nextTick(()=>{var D;(D=this.children[A])===null||D===void 0||D.subchannel.startConnecting()});this.connectionDelayTimeout=setTimeout(()=>{this.startNextSubchannelConnecting(A+1)},pU6),(Q=(B=this.connectionDelayTimeout).unref)===null||Q===void 0||Q.call(B)}pickSubchannel(A){s31("Pick subchannel with address "+A.getAddress()),this.stickyTransientFailureMode=!1,A.ref(),this.channelControlHelper.addChannelzChild(A.getChannelzRef()),this.removeCurrentPick(),this.resetSubchannelList(),A.addConnectivityStateListener(this.subchannelStateListener),A.addHealthStateWatcher(this.pickedSubchannelHealthListener),this.currentPick=A,clearTimeout(this.connectionDelayTimeout),this.calculateAndReportNewState()}updateState(A,B,Q){s31(AF.ConnectivityState[this.currentState]+" -> "+AF.ConnectivityState[A]),this.currentState=A,this.channelControlHelper.updateState(A,B,Q)}resetSubchannelList(){for(let A of this.children)A.subchannel.removeConnectivityStateListener(this.subchannelStateListener),A.subchannel.unref(),this.channelControlHelper.removeChannelzChild(A.subchannel.getChannelzRef());this.currentSubchannelIndex=0,this.children=[]}connectToAddressList(A,B){s31("connectToAddressList(["+A.map((D)=>Me2.subchannelAddressToString(D))+"])");let Q=A.map((D)=>({subchannel:this.channelControlHelper.createSubchannel(D,B),hasReportedTransientFailure:!1}));for(let{subchannel:D}of Q)if(D.getConnectivityState()===AF.ConnectivityState.READY){this.pickSubchannel(D);return}for(let{subchannel:D}of Q)D.ref(),this.channelControlHelper.addChannelzChild(D.getChannelzRef());this.resetSubchannelList(),this.children=Q;for(let{subchannel:D}of this.children)D.addConnectivityStateListener(this.subchannelStateListener);for(let D of this.children)if(D.subchannel.getConnectivityState()===AF.ConnectivityState.TRANSIENT_FAILURE)D.hasReportedTransientFailure=!0;this.startNextSubchannelConnecting(0),this.calculateAndReportNewState()}updateAddressList(A,B,Q){if(!(B instanceof Et))return;if(this.reportHealthStatus=Q[Se2],B.getShuffleAddressList())A=Pe2(A);let D=[].concat(...A.map((G)=>G.addresses));if(s31("updateAddressList(["+D.map((G)=>Me2.subchannelAddressToString(G))+"])"),D.length===0)this.lastError="No addresses resolved";let Z=iU6(D);this.latestAddressList=Z,this.latestOptions=Q,this.connectToAddressList(Z,Q)}exitIdle(){if(this.currentState===AF.ConnectivityState.IDLE&&this.latestAddressList)this.connectToAddressList(this.latestAddressList,this.latestOptions)}resetBackoff(){}destroy(){this.resetSubchannelList(),this.removeCurrentPick()}getTypeName(){return r31}}ye2.PickFirstLoadBalancer=mP1;var nU6=new Et(!1);class je2{constructor(A,B,Q){this.endpoint=A,this.options=Q,this.latestState=AF.ConnectivityState.IDLE;let D=RX0.createChildChannelControlHelper(B,{updateState:(Z,G,F)=>{this.latestState=Z,this.latestPicker=G,B.updateState(Z,G,F)}});this.pickFirstBalancer=new mP1(D),this.latestPicker=new su.QueuePicker(this.pickFirstBalancer)}startConnecting(){this.pickFirstBalancer.updateAddressList([this.endpoint],nU6,Object.assign(Object.assign({},this.options),{[Se2]:!0}))}updateEndpoint(A,B){if(this.options=B,this.endpoint=A,this.latestState!==AF.ConnectivityState.IDLE)this.startConnecting()}getConnectivityState(){return this.latestState}getPicker(){return this.latestPicker}getEndpoint(){return this.endpoint}exitIdle(){this.pickFirstBalancer.exitIdle()}destroy(){this.pickFirstBalancer.destroy()}}ye2.LeafLoadBalancer=je2;function aU6(){RX0.registerLoadBalancerType(r31,mP1,Et),RX0.registerDefaultLoadBalancerType(r31)}});
var dW0=E((na2)=>{Object.defineProperty(na2,"__esModule",{value:!0});na2.Client=void 0;var QM=ya2(),TV6=cW0(),PV6=EE(),r_=_6(),ao=GJ(),aT1=uW0(),D$=Symbol(),so=Symbol(),ro=Symbol(),zP=Symbol();function mW0(A){return typeof A==="function"}function oo(A){var B;return((B=A.stack)===null||B===void 0?void 0:B.split(`
`).slice(1).join(`
`))||"no stack trace available"}class ia2{constructor(A,B,Q={}){var D,Z;if(Q=Object.assign({},Q),this[so]=(D=Q.interceptors)!==null&&D!==void 0?D:[],delete Q.interceptors,this[ro]=(Z=Q.interceptor_providers)!==null&&Z!==void 0?Z:[],delete Q.interceptor_providers,this[so].length>0&&this[ro].length>0)throw new Error("Both interceptors and interceptor_providers were passed as options to the client constructor. Only one of these is allowed.");if(this[zP]=Q.callInvocationTransformer,delete Q.callInvocationTransformer,Q.channelOverride)this[D$]=Q.channelOverride;else if(Q.channelFactoryOverride){let G=Q.channelFactoryOverride;delete Q.channelFactoryOverride,this[D$]=G(A,B,Q)}else this[D$]=new TV6.ChannelImplementation(A,B,Q)}close(){this[D$].close()}getChannel(){return this[D$]}waitForReady(A,B){let Q=(D)=>{if(D){B(new Error("Failed to connect before the deadline"));return}let Z;try{Z=this[D$].getConnectivityState(!0)}catch(G){B(new Error("The channel has been closed"));return}if(Z===PV6.ConnectivityState.READY)B();else try{this[D$].watchConnectivityState(Z,A,Q)}catch(G){B(new Error("The channel has been closed"))}};setImmediate(Q)}checkOptionalUnaryResponseArguments(A,B,Q){if(mW0(A))return{metadata:new ao.Metadata,options:{},callback:A};else if(mW0(B))if(A instanceof ao.Metadata)return{metadata:A,options:{},callback:B};else return{metadata:new ao.Metadata,options:A,callback:B};else{if(!(A instanceof ao.Metadata&&B instanceof Object&&mW0(Q)))throw new Error("Incorrect arguments passed");return{metadata:A,options:B,callback:Q}}}makeUnaryRequest(A,B,Q,D,Z,G,F){var I,Y;let W=this.checkOptionalUnaryResponseArguments(Z,G,F),J={path:A,requestStream:!1,responseStream:!1,requestSerialize:B,responseDeserialize:Q},X={argument:D,metadata:W.metadata,call:new QM.ClientUnaryCallImpl,channel:this[D$],methodDefinition:J,callOptions:W.options,callback:W.callback};if(this[zP])X=this[zP](X);let V=X.call,C={clientInterceptors:this[so],clientInterceptorProviders:this[ro],callInterceptors:(I=X.callOptions.interceptors)!==null&&I!==void 0?I:[],callInterceptorProviders:(Y=X.callOptions.interceptor_providers)!==null&&Y!==void 0?Y:[]},K=aT1.getInterceptingCall(C,X.methodDefinition,X.callOptions,X.channel);V.call=K;let H=null,z=!1,$=new Error;return K.start(X.metadata,{onReceiveMetadata:(L)=>{V.emit("metadata",L)},onReceiveMessage(L){if(H!==null)K.cancelWithStatus(r_.Status.UNIMPLEMENTED,"Too many responses received");H=L},onReceiveStatus(L){if(z)return;if(z=!0,L.code===r_.Status.OK)if(H===null){let N=oo($);X.callback(QM.callErrorFromStatus({code:r_.Status.UNIMPLEMENTED,details:"No message received",metadata:L.metadata},N))}else X.callback(null,H);else{let N=oo($);X.callback(QM.callErrorFromStatus(L,N))}$=null,V.emit("status",L)}}),K.sendMessage(D),K.halfClose(),V}makeClientStreamRequest(A,B,Q,D,Z,G){var F,I;let Y=this.checkOptionalUnaryResponseArguments(D,Z,G),W={path:A,requestStream:!0,responseStream:!1,requestSerialize:B,responseDeserialize:Q},J={metadata:Y.metadata,call:new QM.ClientWritableStreamImpl(B),channel:this[D$],methodDefinition:W,callOptions:Y.options,callback:Y.callback};if(this[zP])J=this[zP](J);let X=J.call,V={clientInterceptors:this[so],clientInterceptorProviders:this[ro],callInterceptors:(F=J.callOptions.interceptors)!==null&&F!==void 0?F:[],callInterceptorProviders:(I=J.callOptions.interceptor_providers)!==null&&I!==void 0?I:[]},C=aT1.getInterceptingCall(V,J.methodDefinition,J.callOptions,J.channel);X.call=C;let K=null,H=!1,z=new Error;return C.start(J.metadata,{onReceiveMetadata:($)=>{X.emit("metadata",$)},onReceiveMessage($){if(K!==null)C.cancelWithStatus(r_.Status.UNIMPLEMENTED,"Too many responses received");K=$,C.startRead()},onReceiveStatus($){if(H)return;if(H=!0,$.code===r_.Status.OK)if(K===null){let L=oo(z);J.callback(QM.callErrorFromStatus({code:r_.Status.UNIMPLEMENTED,details:"No message received",metadata:$.metadata},L))}else J.callback(null,K);else{let L=oo(z);J.callback(QM.callErrorFromStatus($,L))}z=null,X.emit("status",$)}}),X}checkMetadataAndOptions(A,B){let Q,D;if(A instanceof ao.Metadata)if(Q=A,B)D=B;else D={};else{if(A)D=A;else D={};Q=new ao.Metadata}return{metadata:Q,options:D}}makeServerStreamRequest(A,B,Q,D,Z,G){var F,I;let Y=this.checkMetadataAndOptions(Z,G),W={path:A,requestStream:!1,responseStream:!0,requestSerialize:B,responseDeserialize:Q},J={argument:D,metadata:Y.metadata,call:new QM.ClientReadableStreamImpl(Q),channel:this[D$],methodDefinition:W,callOptions:Y.options};if(this[zP])J=this[zP](J);let X=J.call,V={clientInterceptors:this[so],clientInterceptorProviders:this[ro],callInterceptors:(F=J.callOptions.interceptors)!==null&&F!==void 0?F:[],callInterceptorProviders:(I=J.callOptions.interceptor_providers)!==null&&I!==void 0?I:[]},C=aT1.getInterceptingCall(V,J.methodDefinition,J.callOptions,J.channel);X.call=C;let K=!1,H=new Error;return C.start(J.metadata,{onReceiveMetadata(z){X.emit("metadata",z)},onReceiveMessage(z){X.push(z)},onReceiveStatus(z){if(K)return;if(K=!0,X.push(null),z.code!==r_.Status.OK){let $=oo(H);X.emit("error",QM.callErrorFromStatus(z,$))}H=null,X.emit("status",z)}}),C.sendMessage(D),C.halfClose(),X}makeBidiStreamRequest(A,B,Q,D,Z){var G,F;let I=this.checkMetadataAndOptions(D,Z),Y={path:A,requestStream:!0,responseStream:!0,requestSerialize:B,responseDeserialize:Q},W={metadata:I.metadata,call:new QM.ClientDuplexStreamImpl(B,Q),channel:this[D$],methodDefinition:Y,callOptions:I.options};if(this[zP])W=this[zP](W);let J=W.call,X={clientInterceptors:this[so],clientInterceptorProviders:this[ro],callInterceptors:(G=W.callOptions.interceptors)!==null&&G!==void 0?G:[],callInterceptorProviders:(F=W.callOptions.interceptor_providers)!==null&&F!==void 0?F:[]},V=aT1.getInterceptingCall(X,W.methodDefinition,W.callOptions,W.channel);J.call=V;let C=!1,K=new Error;return V.start(W.metadata,{onReceiveMetadata(H){J.emit("metadata",H)},onReceiveMessage(H){J.push(H)},onReceiveStatus(H){if(C)return;if(C=!0,J.push(null),H.code!==r_.Status.OK){let z=oo(K);J.emit("error",QM.callErrorFromStatus(H,z))}K=null,J.emit("status",H)}}),J}}na2.Client=ia2});
var do2=E((uo2)=>{Object.defineProperty(uo2,"__esModule",{value:!0});uo2.Http2SubchannelConnector=void 0;var TP1=J1("http2"),RP1=pu(),l31=_6(),Sz6=lJ0(),Wt=F7(),jz6=BM(),OP1=UE(),aJ0=hV(),yz6=J1("net"),kz6=bo2(),_z6=nJ0(),sJ0="transport",xz6="transport_flowctrl",vz6=EW0().version,{HTTP2_HEADER_AUTHORITY:bz6,HTTP2_HEADER_CONTENT_TYPE:fz6,HTTP2_HEADER_METHOD:hz6,HTTP2_HEADER_PATH:gz6,HTTP2_HEADER_TE:uz6,HTTP2_HEADER_USER_AGENT:mz6}=TP1.constants,dz6=20000,cz6=Buffer.from("too_many_pings","ascii");class ho2{constructor(A,B,Q,D){if(this.session=A,this.options=Q,this.remoteName=D,this.keepaliveTimer=null,this.pendingSendKeepalivePing=!1,this.activeCalls=new Set,this.disconnectListeners=[],this.disconnectHandled=!1,this.channelzEnabled=!0,this.keepalivesSent=0,this.messagesSent=0,this.messagesReceived=0,this.lastMessageSentTimestamp=null,this.lastMessageReceivedTimestamp=null,this.subchannelAddressString=OP1.subchannelAddressToString(B),Q["grpc.enable_channelz"]===0)this.channelzEnabled=!1,this.streamTracker=new RP1.ChannelzCallTrackerStub;else this.streamTracker=new RP1.ChannelzCallTracker;if(this.channelzRef=RP1.registerChannelzSocket(this.subchannelAddressString,()=>this.getChannelzInfo(),this.channelzEnabled),this.userAgent=[Q["grpc.primary_user_agent"],`grpc-node-js/${vz6}`,Q["grpc.secondary_user_agent"]].filter((Z)=>Z).join(" "),"grpc.keepalive_time_ms"in Q)this.keepaliveTimeMs=Q["grpc.keepalive_time_ms"];else this.keepaliveTimeMs=-1;if("grpc.keepalive_timeout_ms"in Q)this.keepaliveTimeoutMs=Q["grpc.keepalive_timeout_ms"];else this.keepaliveTimeoutMs=dz6;if("grpc.keepalive_permit_without_calls"in Q)this.keepaliveWithoutCalls=Q["grpc.keepalive_permit_without_calls"]===1;else this.keepaliveWithoutCalls=!1;if(A.once("close",()=>{this.trace("session closed"),this.handleDisconnect()}),A.once("goaway",(Z,G,F)=>{let I=!1;if(Z===TP1.constants.NGHTTP2_ENHANCE_YOUR_CALM&&F&&F.equals(cz6))I=!0;this.trace("connection closed by GOAWAY with code "+Z+" and data "+(F===null||F===void 0?void 0:F.toString())),this.reportDisconnectToOwner(I)}),A.once("error",(Z)=>{this.trace("connection closed with error "+Z.message),this.handleDisconnect()}),A.socket.once("close",(Z)=>{this.trace("connection closed. hadError="+Z),this.handleDisconnect()}),Wt.isTracerEnabled(sJ0))A.on("remoteSettings",(Z)=>{this.trace("new settings received"+(this.session!==A?" on the old connection":"")+": "+JSON.stringify(Z))}),A.on("localSettings",(Z)=>{this.trace("local settings acknowledged by remote"+(this.session!==A?" on the old connection":"")+": "+JSON.stringify(Z))});if(this.keepaliveWithoutCalls)this.maybeStartKeepalivePingTimer()}getChannelzInfo(){var A,B,Q;let D=this.session.socket,Z=D.remoteAddress?OP1.stringToSubchannelAddress(D.remoteAddress,D.remotePort):null,G=D.localAddress?OP1.stringToSubchannelAddress(D.localAddress,D.localPort):null,F;if(this.session.encrypted){let Y=D,W=Y.getCipher(),J=Y.getCertificate(),X=Y.getPeerCertificate();F={cipherSuiteStandardName:(A=W.standardName)!==null&&A!==void 0?A:null,cipherSuiteOtherName:W.standardName?null:W.name,localCertificate:J&&"raw"in J?J.raw:null,remoteCertificate:X&&"raw"in X?X.raw:null}}else F=null;return{remoteAddress:Z,localAddress:G,security:F,remoteName:this.remoteName,streamsStarted:this.streamTracker.callsStarted,streamsSucceeded:this.streamTracker.callsSucceeded,streamsFailed:this.streamTracker.callsFailed,messagesSent:this.messagesSent,messagesReceived:this.messagesReceived,keepAlivesSent:this.keepalivesSent,lastLocalStreamCreatedTimestamp:this.streamTracker.lastCallStartedTimestamp,lastRemoteStreamCreatedTimestamp:null,lastMessageSentTimestamp:this.lastMessageSentTimestamp,lastMessageReceivedTimestamp:this.lastMessageReceivedTimestamp,localFlowControlWindow:(B=this.session.state.localWindowSize)!==null&&B!==void 0?B:null,remoteFlowControlWindow:(Q=this.session.state.remoteWindowSize)!==null&&Q!==void 0?Q:null}}trace(A){Wt.trace(l31.LogVerbosity.DEBUG,sJ0,"("+this.channelzRef.id+") "+this.subchannelAddressString+" "+A)}keepaliveTrace(A){Wt.trace(l31.LogVerbosity.DEBUG,"keepalive","("+this.channelzRef.id+") "+this.subchannelAddressString+" "+A)}flowControlTrace(A){Wt.trace(l31.LogVerbosity.DEBUG,xz6,"("+this.channelzRef.id+") "+this.subchannelAddressString+" "+A)}internalsTrace(A){Wt.trace(l31.LogVerbosity.DEBUG,"transport_internals","("+this.channelzRef.id+") "+this.subchannelAddressString+" "+A)}reportDisconnectToOwner(A){if(this.disconnectHandled)return;this.disconnectHandled=!0,this.disconnectListeners.forEach((B)=>B(A))}handleDisconnect(){this.clearKeepaliveTimeout(),this.reportDisconnectToOwner(!1);for(let A of this.activeCalls)A.onDisconnect();setImmediate(()=>{this.session.destroy()})}addDisconnectListener(A){this.disconnectListeners.push(A)}canSendPing(){return!this.session.destroyed&&this.keepaliveTimeMs>0&&(this.keepaliveWithoutCalls||this.activeCalls.size>0)}maybeSendPing(){var A,B;if(!this.canSendPing()){this.pendingSendKeepalivePing=!0;return}if(this.keepaliveTimer){console.error("keepaliveTimeout is not null");return}if(this.channelzEnabled)this.keepalivesSent+=1;this.keepaliveTrace("Sending ping with timeout "+this.keepaliveTimeoutMs+"ms"),this.keepaliveTimer=setTimeout(()=>{this.keepaliveTimer=null,this.keepaliveTrace("Ping timeout passed without response"),this.handleDisconnect()},this.keepaliveTimeoutMs),(B=(A=this.keepaliveTimer).unref)===null||B===void 0||B.call(A);let Q="";try{if(!this.session.ping((Z,G,F)=>{if(this.clearKeepaliveTimeout(),Z)this.keepaliveTrace("Ping failed with error "+Z.message),this.handleDisconnect();else this.keepaliveTrace("Received ping response"),this.maybeStartKeepalivePingTimer()}))Q="Ping returned false"}catch(D){Q=(D instanceof Error?D.message:"")||"Unknown error"}if(Q)this.keepaliveTrace("Ping send failed: "+Q),this.handleDisconnect()}maybeStartKeepalivePingTimer(){var A,B;if(!this.canSendPing())return;if(this.pendingSendKeepalivePing)this.pendingSendKeepalivePing=!1,this.maybeSendPing();else if(!this.keepaliveTimer)this.keepaliveTrace("Starting keepalive timer for "+this.keepaliveTimeMs+"ms"),this.keepaliveTimer=setTimeout(()=>{this.keepaliveTimer=null,this.maybeSendPing()},this.keepaliveTimeMs),(B=(A=this.keepaliveTimer).unref)===null||B===void 0||B.call(A)}clearKeepaliveTimeout(){if(this.keepaliveTimer)clearTimeout(this.keepaliveTimer),this.keepaliveTimer=null}removeActiveCall(A){if(this.activeCalls.delete(A),this.activeCalls.size===0)this.session.unref()}addActiveCall(A){if(this.activeCalls.add(A),this.activeCalls.size===1){if(this.session.ref(),!this.keepaliveWithoutCalls)this.maybeStartKeepalivePingTimer()}}createCall(A,B,Q,D,Z){let G=A.toHttp2Headers();G[bz6]=B,G[mz6]=this.userAgent,G[fz6]="application/grpc",G[hz6]="POST",G[gz6]=Q,G[uz6]="trailers";let F;try{F=this.session.request(G)}catch(W){throw this.handleDisconnect(),W}this.flowControlTrace("local window size: "+this.session.state.localWindowSize+" remote window size: "+this.session.state.remoteWindowSize),this.internalsTrace("session.closed="+this.session.closed+" session.destroyed="+this.session.destroyed+" session.socket.destroyed="+this.session.socket.destroyed);let I,Y;if(this.channelzEnabled)this.streamTracker.addCallStarted(),I={addMessageSent:()=>{var W;this.messagesSent+=1,this.lastMessageSentTimestamp=new Date,(W=Z.addMessageSent)===null||W===void 0||W.call(Z)},addMessageReceived:()=>{var W;this.messagesReceived+=1,this.lastMessageReceivedTimestamp=new Date,(W=Z.addMessageReceived)===null||W===void 0||W.call(Z)},onCallEnd:(W)=>{var J;(J=Z.onCallEnd)===null||J===void 0||J.call(Z,W),this.removeActiveCall(Y)},onStreamEnd:(W)=>{var J;if(W)this.streamTracker.addCallSucceeded();else this.streamTracker.addCallFailed();(J=Z.onStreamEnd)===null||J===void 0||J.call(Z,W)}};else I={addMessageSent:()=>{var W;(W=Z.addMessageSent)===null||W===void 0||W.call(Z)},addMessageReceived:()=>{var W;(W=Z.addMessageReceived)===null||W===void 0||W.call(Z)},onCallEnd:(W)=>{var J;(J=Z.onCallEnd)===null||J===void 0||J.call(Z,W),this.removeActiveCall(Y)},onStreamEnd:(W)=>{var J;(J=Z.onStreamEnd)===null||J===void 0||J.call(Z,W)}};return Y=new kz6.Http2SubchannelCall(F,I,D,this,_z6.getNextCallNumber()),this.addActiveCall(Y),Y}getChannelzRef(){return this.channelzRef}getPeerName(){return this.subchannelAddressString}getOptions(){return this.options}shutdown(){this.session.close(),RP1.unregisterChannelzRef(this.channelzRef)}}class go2{constructor(A){this.channelTarget=A,this.session=null,this.isShutdown=!1}trace(A){Wt.trace(l31.LogVerbosity.DEBUG,sJ0,aJ0.uriToString(this.channelTarget)+" "+A)}createSession(A,B,Q){if(this.isShutdown)return Promise.reject();if(A.socket.closed)return Promise.reject("Connection closed before starting HTTP/2 handshake");return new Promise((D,Z)=>{var G;let F=null,I=this.channelTarget;if("grpc.http_connect_target"in Q){let H=aJ0.parseUri(Q["grpc.http_connect_target"]);if(H)I=H,F=aJ0.uriToString(H)}let Y=A.secure?"https":"http",W=jz6.getDefaultAuthority(I),J=()=>{var H;(H=this.session)===null||H===void 0||H.destroy(),this.session=null,setImmediate(()=>{if(!K)K=!0,Z(`${C.trim()} (${new Date().toISOString()})`)})},X=(H)=>{var z;if((z=this.session)===null||z===void 0||z.destroy(),C=H.message,this.trace("connection failed with error "+C),!K)K=!0,Z(`${C} (${new Date().toISOString()})`)},V=TP1.connect(`${Y}://${W}`,{createConnection:(H,z)=>{return A.socket},settings:{initialWindowSize:(G=Q["grpc-node.flow_control_window"])!==null&&G!==void 0?G:TP1.getDefaultSettings().initialWindowSize}});this.session=V;let C="Failed to connect",K=!1;V.unref(),V.once("remoteSettings",()=>{V.removeAllListeners(),A.socket.removeListener("close",J),A.socket.removeListener("error",X),D(new ho2(V,B,Q,F)),this.session=null}),V.once("close",J),V.once("error",X),A.socket.once("close",J),A.socket.once("error",X)})}tcpConnect(A,B){return Sz6.getProxiedConnection(A,B).then((Q)=>{if(Q)return Q;else return new Promise((D,Z)=>{let G=()=>{Z(new Error("Socket closed"))},F=(Y)=>{Z(Y)},I=yz6.connect(A,()=>{I.removeListener("close",G),I.removeListener("error",F),D(I)});I.once("close",G),I.once("error",F)})})}async connect(A,B,Q){if(this.isShutdown)return Promise.reject();let D=null,Z=null,G=OP1.subchannelAddressToString(A);try{return this.trace(G+" Waiting for secureConnector to be ready"),await B.waitForReady(),this.trace(G+" secureConnector is ready"),D=await this.tcpConnect(A,Q),this.trace(G+" Established TCP connection"),Z=await B.connect(D),this.trace(G+" Established secure connection"),this.createSession(Z,A,Q)}catch(F){throw D===null||D===void 0||D.destroy(),Z===null||Z===void 0||Z.socket.destroy(),F}}shutdown(){var A;this.isShutdown=!0,(A=this.session)===null||A===void 0||A.close(),this.session=null}}uo2.Http2SubchannelConnector=go2});
var du=E((Nu5,Fr2)=>{Fr2.exports=_K;_K.className="ReflectionObject";var VP1=$I(),CP1;function _K(A,B){if(!VP1.isString(A))throw TypeError("name must be a string");if(B&&!VP1.isObject(B))throw TypeError("options must be an object");this.options=B,this.parsedOptions=null,this.name=A,this.parent=null,this.resolved=!1,this.comment=null,this.filename=null}Object.defineProperties(_K.prototype,{root:{get:function(){var A=this;while(A.parent!==null)A=A.parent;return A}},fullName:{get:function(){var A=[this.name],B=this.parent;while(B)A.unshift(B.name),B=B.parent;return A.join(".")}}});_K.prototype.toJSON=function A(){throw Error()};_K.prototype.onAdd=function A(B){if(this.parent&&this.parent!==B)this.parent.remove(this);this.parent=B,this.resolved=!1;var Q=B.root;if(Q instanceof CP1)Q._handleAdd(this)};_K.prototype.onRemove=function A(B){var Q=B.root;if(Q instanceof CP1)Q._handleRemove(this);this.parent=null,this.resolved=!1};_K.prototype.resolve=function A(){if(this.resolved)return this;if(this.root instanceof CP1)this.resolved=!0;return this};_K.prototype.getOption=function A(B){if(this.options)return this.options[B];return};_K.prototype.setOption=function A(B,Q,D){if(!D||!this.options||this.options[B]===void 0)(this.options||(this.options={}))[B]=Q;return this};_K.prototype.setParsedOption=function A(B,Q,D){if(!this.parsedOptions)this.parsedOptions=[];var Z=this.parsedOptions;if(D){var G=Z.find(function(Y){return Object.prototype.hasOwnProperty.call(Y,B)});if(G){var F=G[B];VP1.setProperty(F,D,Q)}else G={},G[B]=VP1.setProperty({},D,Q),Z.push(G)}else{var I={};I[B]=Q,Z.push(I)}return this};_K.prototype.setOptions=function A(B,Q){if(B)for(var D=Object.keys(B),Z=0;Z<D.length;++Z)this.setOption(D[Z],B[D[Z]],Q);return this};_K.prototype.toString=function A(){var B=this.constructor.className,Q=this.fullName;if(Q.length)return B+" "+Q;return B};_K._configure=function(A){CP1=A}});
var e31=E((I3)=>{Object.defineProperty(I3,"__esModule",{value:!0});I3.experimental=I3.ServerInterceptingCall=I3.ResponderBuilder=I3.ServerListenerBuilder=I3.addAdminServicesToServer=I3.getChannelzHandlers=I3.getChannelzServiceDefinition=I3.InterceptorConfigurationError=I3.InterceptingCall=I3.RequesterBuilder=I3.ListenerBuilder=I3.StatusBuilder=I3.getClientChannel=I3.ServerCredentials=I3.Server=I3.setLogVerbosity=I3.setLogger=I3.load=I3.loadObject=I3.CallCredentials=I3.ChannelCredentials=I3.waitForClientReady=I3.closeClient=I3.Channel=I3.makeGenericClientConstructor=I3.makeClientConstructor=I3.loadPackageDefinition=I3.Client=I3.compressionAlgorithms=I3.propagate=I3.connectivityState=I3.status=I3.logVerbosity=I3.Metadata=I3.credentials=void 0;var aP1=bT1();Object.defineProperty(I3,"CallCredentials",{enumerable:!0,get:function(){return aP1.CallCredentials}});var dw6=cW0();Object.defineProperty(I3,"Channel",{enumerable:!0,get:function(){return dw6.ChannelImplementation}});var cw6=eJ0();Object.defineProperty(I3,"compressionAlgorithms",{enumerable:!0,get:function(){return cw6.CompressionAlgorithms}});var lw6=EE();Object.defineProperty(I3,"connectivityState",{enumerable:!0,get:function(){return lw6.ConnectivityState}});var sP1=w31();Object.defineProperty(I3,"ChannelCredentials",{enumerable:!0,get:function(){return sP1.ChannelCredentials}});var V1B=dW0();Object.defineProperty(I3,"Client",{enumerable:!0,get:function(){return V1B.Client}});var dX0=_6();Object.defineProperty(I3,"logVerbosity",{enumerable:!0,get:function(){return dX0.LogVerbosity}});Object.defineProperty(I3,"status",{enumerable:!0,get:function(){return dX0.Status}});Object.defineProperty(I3,"propagate",{enumerable:!0,get:function(){return dX0.Propagate}});var C1B=F7(),cX0=pW0();Object.defineProperty(I3,"loadPackageDefinition",{enumerable:!0,get:function(){return cX0.loadPackageDefinition}});Object.defineProperty(I3,"makeClientConstructor",{enumerable:!0,get:function(){return cX0.makeClientConstructor}});Object.defineProperty(I3,"makeGenericClientConstructor",{enumerable:!0,get:function(){return cX0.makeClientConstructor}});var pw6=GJ();Object.defineProperty(I3,"Metadata",{enumerable:!0,get:function(){return pw6.Metadata}});var iw6=Ue2();Object.defineProperty(I3,"Server",{enumerable:!0,get:function(){return iw6.Server}});var nw6=hP1();Object.defineProperty(I3,"ServerCredentials",{enumerable:!0,get:function(){return nw6.ServerCredentials}});var aw6=Ne2();Object.defineProperty(I3,"StatusBuilder",{enumerable:!0,get:function(){return aw6.StatusBuilder}});I3.credentials={combineChannelCredentials:(A,...B)=>{return B.reduce((Q,D)=>Q.compose(D),A)},combineCallCredentials:(A,...B)=>{return B.reduce((Q,D)=>Q.compose(D),A)},createInsecure:sP1.ChannelCredentials.createInsecure,createSsl:sP1.ChannelCredentials.createSsl,createFromSecureContext:sP1.ChannelCredentials.createFromSecureContext,createFromMetadataGenerator:aP1.CallCredentials.createFromMetadataGenerator,createFromGoogleCredential:aP1.CallCredentials.createFromGoogleCredential,createEmpty:aP1.CallCredentials.createEmpty};var sw6=(A)=>A.close();I3.closeClient=sw6;var rw6=(A,B,Q)=>A.waitForReady(B,Q);I3.waitForClientReady=rw6;var ow6=(A,B)=>{throw new Error("Not available in this library. Use @grpc/proto-loader and loadPackageDefinition instead")};I3.loadObject=ow6;var tw6=(A,B,Q)=>{throw new Error("Not available in this library. Use @grpc/proto-loader and loadPackageDefinition instead")};I3.load=tw6;var ew6=(A)=>{C1B.setLogger(A)};I3.setLogger=ew6;var A$6=(A)=>{C1B.setLoggerVerbosity(A)};I3.setLogVerbosity=A$6;var B$6=(A)=>{return V1B.Client.prototype.getChannel.call(A)};I3.getClientChannel=B$6;var rP1=uW0();Object.defineProperty(I3,"ListenerBuilder",{enumerable:!0,get:function(){return rP1.ListenerBuilder}});Object.defineProperty(I3,"RequesterBuilder",{enumerable:!0,get:function(){return rP1.RequesterBuilder}});Object.defineProperty(I3,"InterceptingCall",{enumerable:!0,get:function(){return rP1.InterceptingCall}});Object.defineProperty(I3,"InterceptorConfigurationError",{enumerable:!0,get:function(){return rP1.InterceptorConfigurationError}});var K1B=pu();Object.defineProperty(I3,"getChannelzServiceDefinition",{enumerable:!0,get:function(){return K1B.getChannelzServiceDefinition}});Object.defineProperty(I3,"getChannelzHandlers",{enumerable:!0,get:function(){return K1B.getChannelzHandlers}});var Q$6=nT1();Object.defineProperty(I3,"addAdminServicesToServer",{enumerable:!0,get:function(){return Q$6.addAdminServicesToServer}});var lX0=$X0();Object.defineProperty(I3,"ServerListenerBuilder",{enumerable:!0,get:function(){return lX0.ServerListenerBuilder}});Object.defineProperty(I3,"ResponderBuilder",{enumerable:!0,get:function(){return lX0.ResponderBuilder}});Object.defineProperty(I3,"ServerInterceptingCall",{enumerable:!0,get:function(){return lX0.ServerInterceptingCall}});var D$6=PX0();I3.experimental=D$6;var Z$6=cJ0(),G$6=le2(),F$6=re2(),I$6=dP1(),Y$6=D1B(),W$6=X1B(),J$6=pu();(()=>{Z$6.setup(),G$6.setup(),F$6.setup(),I$6.setup(),Y$6.setup(),W$6.setup(),J$6.setup()})()});
var eJ0=E((so2)=>{Object.defineProperty(so2,"__esModule",{value:!0});so2.CompressionAlgorithms=void 0;var ao2;(function(A){A[A.identity=0]="identity",A[A.deflate=1]="deflate",A[A.gzip=2]="gzip"})(ao2||(so2.CompressionAlgorithms=ao2={}))});
var eo=E((Wu5,vs2)=>{vs2.exports=$E;var AP1=du();(($E.prototype=Object.create(AP1.prototype)).constructor=$E).className="OneOf";var _s2=o_(),eT1=$I();function $E(A,B,Q,D){if(!Array.isArray(B))Q=B,B=void 0;if(AP1.call(this,A,Q),!(B===void 0||Array.isArray(B)))throw TypeError("fieldNames must be an Array");this.oneof=B||[],this.fieldsArray=[],this.comment=D}$E.fromJSON=function A(B,Q){return new $E(B,Q.oneof,Q.options,Q.comment)};$E.prototype.toJSON=function A(B){var Q=B?Boolean(B.keepComments):!1;return eT1.toObject(["options",this.options,"oneof",this.oneof,"comment",Q?this.comment:void 0])};function xs2(A){if(A.parent){for(var B=0;B<A.fieldsArray.length;++B)if(!A.fieldsArray[B].parent)A.parent.add(A.fieldsArray[B])}}$E.prototype.add=function A(B){if(!(B instanceof _s2))throw TypeError("field must be a Field");if(B.parent&&B.parent!==this.parent)B.parent.remove(B);return this.oneof.push(B.name),this.fieldsArray.push(B),B.partOf=this,xs2(this),this};$E.prototype.remove=function A(B){if(!(B instanceof _s2))throw TypeError("field must be a Field");var Q=this.fieldsArray.indexOf(B);if(Q<0)throw Error(B+" is not a member of "+this);if(this.fieldsArray.splice(Q,1),Q=this.oneof.indexOf(B.name),Q>-1)this.oneof.splice(Q,1);return B.partOf=null,this};$E.prototype.onAdd=function A(B){AP1.prototype.onAdd.call(this,B);var Q=this;for(var D=0;D<this.oneof.length;++D){var Z=B.get(this.oneof[D]);if(Z&&!Z.partOf)Z.partOf=Q,Q.fieldsArray.push(Z)}xs2(this)};$E.prototype.onRemove=function A(B){for(var Q=0,D;Q<this.fieldsArray.length;++Q)if((D=this.fieldsArray[Q]).parent)D.parent.remove(D);AP1.prototype.onRemove.call(this,B)};$E.d=function A(){var B=new Array(arguments.length),Q=0;while(Q<arguments.length)B[Q]=arguments[Q++];return function D(Z,G){eT1.decorateType(Z.constructor).add(new $E(G,B)),Object.defineProperty(Z,G,{get:eT1.oneOfGetter(B),set:eT1.oneOfSetter(B)})}}});
var f1B=E((v1B)=>{Object.defineProperty(v1B,"__esModule",{value:!0});v1B.getOtlpGrpcConfigurationFromEnv=void 0;var y1B=x3(),Q71=A71(),h$6=co(),g$6=J1("fs"),u$6=J1("path"),_1B=VQ();function iX0(A,B){if(A!=null&&A!=="")return A;if(B!=null&&B!=="")return B;return}function m$6(A){let B=process.env[`OTEL_EXPORTER_OTLP_${A}_HEADERS`]?.trim(),Q=process.env.OTEL_EXPORTER_OTLP_HEADERS?.trim(),D=y1B.parseKeyPairsIntoRecord(B),Z=y1B.parseKeyPairsIntoRecord(Q);if(Object.keys(D).length===0&&Object.keys(Z).length===0)return;let G=Object.assign({},Z,D),F=Q71.createEmptyMetadata();for(let[I,Y]of Object.entries(G))F.set(I,Y);return F}function d$6(A){let B=m$6(A);if(B==null)return;return()=>B}function c$6(A){let B=process.env[`OTEL_EXPORTER_OTLP_${A}_ENDPOINT`]?.trim(),Q=process.env.OTEL_EXPORTER_OTLP_ENDPOINT?.trim();return iX0(B,Q)}function l$6(A){let B=process.env[`OTEL_EXPORTER_OTLP_${A}_INSECURE`]?.toLowerCase().trim(),Q=process.env.OTEL_EXPORTER_OTLP_INSECURE?.toLowerCase().trim();return iX0(B,Q)==="true"}function nX0(A,B,Q){let D=process.env[A]?.trim(),Z=process.env[B]?.trim(),G=iX0(D,Z);if(G!=null)try{return g$6.readFileSync(u$6.resolve(process.cwd(),G))}catch{_1B.diag.warn(Q);return}else return}function p$6(A){return nX0(`OTEL_EXPORTER_OTLP_${A}_CLIENT_CERTIFICATE`,"OTEL_EXPORTER_OTLP_CLIENT_CERTIFICATE","Failed to read client certificate chain file")}function i$6(A){return nX0(`OTEL_EXPORTER_OTLP_${A}_CLIENT_KEY`,"OTEL_EXPORTER_OTLP_CLIENT_KEY","Failed to read client certificate private key file")}function k1B(A){return nX0(`OTEL_EXPORTER_OTLP_${A}_CERTIFICATE`,"OTEL_EXPORTER_OTLP_CERTIFICATE","Failed to read root certificate file")}function x1B(A){let B=i$6(A),Q=p$6(A),D=k1B(A),Z=B!=null&&Q!=null;if(D!=null&&!Z)return _1B.diag.warn("Client key and certificate must both be provided, but one was missing - attempting to create credentials from just the root certificate"),Q71.createSslCredentials(k1B(A));return Q71.createSslCredentials(D,B,Q)}function n$6(A){if(l$6(A))return Q71.createInsecureCredentials();return x1B(A)}function a$6(A){return{...h$6.getSharedConfigurationFromEnvironment(A),metadata:d$6(A),url:c$6(A),credentials:(B)=>{if(B.startsWith("http://"))return()=>{return Q71.createInsecureCredentials()};else if(B.startsWith("https://"))return()=>{return x1B(A)};return()=>{return n$6(A)}}}}v1B.getOtlpGrpcConfigurationFromEnv=a$6});
var gu=E((hn2)=>{Object.defineProperty(hn2,"__esModule",{value:!0});hn2.createChildChannelControlHelper=lJ6;hn2.registerLoadBalancerType=pJ6;hn2.registerDefaultLoadBalancerType=iJ6;hn2.createLoadBalancer=nJ6;hn2.isLoadBalancerNameRegistered=aJ6;hn2.parseLoadBalancingConfig=fn2;hn2.getDefaultConfig=sJ6;hn2.selectLbConfigFromList=rJ6;var dJ6=F7(),cJ6=_6();function lJ6(A,B){var Q,D,Z,G,F,I,Y,W,J,X;return{createSubchannel:(D=(Q=B.createSubchannel)===null||Q===void 0?void 0:Q.bind(B))!==null&&D!==void 0?D:A.createSubchannel.bind(A),updateState:(G=(Z=B.updateState)===null||Z===void 0?void 0:Z.bind(B))!==null&&G!==void 0?G:A.updateState.bind(A),requestReresolution:(I=(F=B.requestReresolution)===null||F===void 0?void 0:F.bind(B))!==null&&I!==void 0?I:A.requestReresolution.bind(A),addChannelzChild:(W=(Y=B.addChannelzChild)===null||Y===void 0?void 0:Y.bind(B))!==null&&W!==void 0?W:A.addChannelzChild.bind(A),removeChannelzChild:(X=(J=B.removeChannelzChild)===null||J===void 0?void 0:J.bind(B))!==null&&X!==void 0?X:A.removeChannelzChild.bind(A)}}var a_={},$31=null;function pJ6(A,B,Q){a_[A]={LoadBalancer:B,LoadBalancingConfig:Q}}function iJ6(A){$31=A}function nJ6(A,B){let Q=A.getLoadBalancerName();if(Q in a_)return new a_[Q].LoadBalancer(B);else return null}function aJ6(A){return A in a_}function fn2(A){let B=Object.keys(A);if(B.length!==1)throw new Error("Provided load balancing config has multiple conflicting entries");let Q=B[0];if(Q in a_)try{return a_[Q].LoadBalancingConfig.createFromJson(A[Q])}catch(D){throw new Error(`${Q}: ${D.message}`)}else throw new Error(`Unrecognized load balancing config name ${Q}`)}function sJ6(){if(!$31)throw new Error("No default load balancer type registered");return new a_[$31].LoadBalancingConfig}function rJ6(A,B=!1){for(let Q of A)try{return fn2(Q)}catch(D){dJ6.log(cJ6.LogVerbosity.DEBUG,"Config parsing failed with error",D.message);continue}if(B)if($31)return new a_[$31].LoadBalancingConfig;else return null;else return null}});
var hP1=E((rt2)=>{Object.defineProperty(rt2,"__esModule",{value:!0});rt2.ServerCredentials=void 0;rt2.createCertificateProviderServerCredentials=ZU6;rt2.createServerCredentialsWithInterceptors=GU6;var XX0=PW0();class Vt{constructor(A,B){this.serverConstructorOptions=A,this.watchers=new Set,this.latestContextOptions=null,this.latestContextOptions=B!==null&&B!==void 0?B:null}_addWatcher(A){this.watchers.add(A)}_removeWatcher(A){this.watchers.delete(A)}getWatcherCount(){return this.watchers.size}updateSecureContextOptions(A){this.latestContextOptions=A;for(let B of this.watchers)B(this.latestContextOptions)}_isSecure(){return this.serverConstructorOptions!==null}_getSecureContextOptions(){return this.latestContextOptions}_getConstructorOptions(){return this.serverConstructorOptions}_getInterceptors(){return[]}static createInsecure(){return new VX0}static createSsl(A,B,Q=!1){var D;if(A!==null&&!Buffer.isBuffer(A))throw new TypeError("rootCerts must be null or a Buffer");if(!Array.isArray(B))throw new TypeError("keyCertPairs must be an array");if(typeof Q!=="boolean")throw new TypeError("checkClientCertificate must be a boolean");let Z=[],G=[];for(let F=0;F<B.length;F++){let I=B[F];if(I===null||typeof I!=="object")throw new TypeError(`keyCertPair[${F}] must be an object`);if(!Buffer.isBuffer(I.private_key))throw new TypeError(`keyCertPair[${F}].private_key must be a Buffer`);if(!Buffer.isBuffer(I.cert_chain))throw new TypeError(`keyCertPair[${F}].cert_chain must be a Buffer`);Z.push(I.cert_chain),G.push(I.private_key)}return new CX0({requestCert:Q,ciphers:XX0.CIPHER_SUITES},{ca:(D=A!==null&&A!==void 0?A:XX0.getDefaultRootsData())!==null&&D!==void 0?D:void 0,cert:Z,key:G})}}rt2.ServerCredentials=Vt;class VX0 extends Vt{constructor(){super(null)}_getSettings(){return null}_equals(A){return A instanceof VX0}}class CX0 extends Vt{constructor(A,B){super(A,B);this.options=Object.assign(Object.assign({},A),B)}_equals(A){if(this===A)return!0;if(!(A instanceof CX0))return!1;if(Buffer.isBuffer(this.options.ca)&&Buffer.isBuffer(A.options.ca)){if(!this.options.ca.equals(A.options.ca))return!1}else if(this.options.ca!==A.options.ca)return!1;if(Array.isArray(this.options.cert)&&Array.isArray(A.options.cert)){if(this.options.cert.length!==A.options.cert.length)return!1;for(let B=0;B<this.options.cert.length;B++){let Q=this.options.cert[B],D=A.options.cert[B];if(Buffer.isBuffer(Q)&&Buffer.isBuffer(D)){if(!Q.equals(D))return!1}else if(Q!==D)return!1}}else if(this.options.cert!==A.options.cert)return!1;if(Array.isArray(this.options.key)&&Array.isArray(A.options.key)){if(this.options.key.length!==A.options.key.length)return!1;for(let B=0;B<this.options.key.length;B++){let Q=this.options.key[B],D=A.options.key[B];if(Buffer.isBuffer(Q)&&Buffer.isBuffer(D)){if(!Q.equals(D))return!1}else if(Q!==D)return!1}}else if(this.options.key!==A.options.key)return!1;if(this.options.requestCert!==A.options.requestCert)return!1;return!0}}class KX0 extends Vt{constructor(A,B,Q){super({requestCert:B!==null,rejectUnauthorized:Q,ciphers:XX0.CIPHER_SUITES});this.identityCertificateProvider=A,this.caCertificateProvider=B,this.requireClientCertificate=Q,this.latestCaUpdate=null,this.latestIdentityUpdate=null,this.caCertificateUpdateListener=this.handleCaCertificateUpdate.bind(this),this.identityCertificateUpdateListener=this.handleIdentityCertitificateUpdate.bind(this)}_addWatcher(A){var B;if(this.getWatcherCount()===0)(B=this.caCertificateProvider)===null||B===void 0||B.addCaCertificateListener(this.caCertificateUpdateListener),this.identityCertificateProvider.addIdentityCertificateListener(this.identityCertificateUpdateListener);super._addWatcher(A)}_removeWatcher(A){var B;if(super._removeWatcher(A),this.getWatcherCount()===0)(B=this.caCertificateProvider)===null||B===void 0||B.removeCaCertificateListener(this.caCertificateUpdateListener),this.identityCertificateProvider.removeIdentityCertificateListener(this.identityCertificateUpdateListener)}_equals(A){if(this===A)return!0;if(!(A instanceof KX0))return!1;return this.caCertificateProvider===A.caCertificateProvider&&this.identityCertificateProvider===A.identityCertificateProvider&&this.requireClientCertificate===A.requireClientCertificate}calculateSecureContextOptions(){var A;if(this.latestIdentityUpdate===null)return null;if(this.caCertificateProvider!==null&&this.latestCaUpdate===null)return null;return{ca:(A=this.latestCaUpdate)===null||A===void 0?void 0:A.caCertificate,cert:[this.latestIdentityUpdate.certificate],key:[this.latestIdentityUpdate.privateKey]}}finalizeUpdate(){let A=this.calculateSecureContextOptions();this.updateSecureContextOptions(A)}handleCaCertificateUpdate(A){this.latestCaUpdate=A,this.finalizeUpdate()}handleIdentityCertitificateUpdate(A){this.latestIdentityUpdate=A,this.finalizeUpdate()}}function ZU6(A,B,Q){return new KX0(A,B,Q)}class HX0 extends Vt{constructor(A,B){super({});this.childCredentials=A,this.interceptors=B}_isSecure(){return this.childCredentials._isSecure()}_equals(A){if(!(A instanceof HX0))return!1;if(!this.childCredentials._equals(A.childCredentials))return!1;if(this.interceptors.length!==A.interceptors.length)return!1;for(let B=0;B<this.interceptors.length;B++)if(this.interceptors[B]!==A.interceptors[B])return!1;return!0}_getInterceptors(){return this.interceptors}_addWatcher(A){this.childCredentials._addWatcher(A)}_removeWatcher(A){this.childCredentials._removeWatcher(A)}_getConstructorOptions(){return this.childCredentials._getConstructorOptions()}_getSecureContextOptions(){return this.childCredentials._getSecureContextOptions()}}function GU6(A,B){return new HX0(A,B)}});
var hV=E((jn2)=>{Object.defineProperty(jn2,"__esModule",{value:!0});jn2.parseUri=$J6;jn2.splitHostPort=qJ6;jn2.combineHostPort=NJ6;jn2.uriToString=LJ6;var wJ6=/^(?:([A-Za-z0-9+.-]+):)?(?:\/\/([^/]*)\/)?(.+)$/;function $J6(A){let B=wJ6.exec(A);if(B===null)return null;return{scheme:B[1],authority:B[2],path:B[3]}}var Sn2=/^\d+$/;function qJ6(A){if(A.startsWith("[")){let B=A.indexOf("]");if(B===-1)return null;let Q=A.substring(1,B);if(Q.indexOf(":")===-1)return null;if(A.length>B+1)if(A[B+1]===":"){let D=A.substring(B+2);if(Sn2.test(D))return{host:Q,port:+D};else return null}else return null;else return{host:Q}}else{let B=A.split(":");if(B.length===2)if(Sn2.test(B[1]))return{host:B[0],port:+B[1]};else return null;else return{host:A}}}function NJ6(A){if(A.port===void 0)return A.host;else if(A.host.includes(":"))return`[${A.host}]:${A.port}`;else return`${A.host}:${A.port}`}function LJ6(A){let B="";if(A.scheme!==void 0)B+=A.scheme+":";if(A.authority!==void 0)B+="//"+A.authority+"/";return B+=A.path,B}});
var i31=E((Wt2)=>{Object.defineProperty(Wt2,"__esModule",{value:!0});Wt2.minDeadline=ZE6;Wt2.getDeadlineTimeoutString=FE6;Wt2.getRelativeTimeout=YE6;Wt2.deadlineToString=WE6;Wt2.formatDateDifference=JE6;function ZE6(...A){let B=1/0;for(let Q of A){let D=Q instanceof Date?Q.getTime():Q;if(D<B)B=D}return B}var GE6=[["m",1],["S",1000],["M",60000],["H",3600000]];function FE6(A){let B=new Date().getTime();if(A instanceof Date)A=A.getTime();let Q=Math.max(A-B,0);for(let[D,Z]of GE6){let G=Q/Z;if(G<1e8)return String(Math.ceil(G))+D}throw new Error("Deadline is too far in the future")}var IE6=2147483647;function YE6(A){let B=A instanceof Date?A.getTime():A,Q=new Date().getTime(),D=B-Q;if(D<0)return 0;else if(D>IE6)return 1/0;else return D}function WE6(A){if(A instanceof Date)return A.toISOString();else{let B=new Date(A);if(Number.isNaN(B.getTime()))return""+A;else return B.toISOString()}}function JE6(A,B){return((B.getTime()-A.getTime())/1000).toFixed(3)+"s"}});
var j1B=E((P1B)=>{Object.defineProperty(P1B,"__esModule",{value:!0});P1B.getOtlpGrpcDefaultConfiguration=P1B.mergeOtlpGrpcConfigurationWithDefaults=P1B.validateAndNormalizeUrl=void 0;var O1B=xu(),B71=A71(),k$6=L1B(),_$6=J1("url"),M1B=VQ();function T1B(A){if(A=A.trim(),!A.match(/^([\w]{1,8}):\/\//))A=`https://${A}`;let Q=new _$6.URL(A);if(Q.protocol==="unix:")return A;if(Q.pathname&&Q.pathname!=="/")M1B.diag.warn("URL path should not be set when using grpc, the path part of the URL will be ignored.");if(Q.protocol!==""&&!Q.protocol?.match(/^(http)s?:$/))M1B.diag.warn("URL protocol should be http(s)://. Using http://.");return Q.host}P1B.validateAndNormalizeUrl=T1B;function R1B(A,B){for(let[Q,D]of Object.entries(B.getMap()))if(A.get(Q).length<1)A.set(Q,D)}function x$6(A,B,Q){let D=A.url??B.url??Q.url;return{...O1B.mergeOtlpSharedConfigurationWithDefaults(A,B,Q),metadata:()=>{let Z=Q.metadata();return R1B(Z,A.metadata?.().clone()??B71.createEmptyMetadata()),R1B(Z,B.metadata?.()??B71.createEmptyMetadata()),Z},url:T1B(D),credentials:A.credentials??B.credentials?.(D)??Q.credentials(D)}}P1B.mergeOtlpGrpcConfigurationWithDefaults=x$6;function v$6(){return{...O1B.getSharedConfigurationDefaults(),metadata:()=>{let A=B71.createEmptyMetadata();return A.set("User-Agent",`OTel-OTLP-Exporter-JavaScript/${k$6.VERSION}`),A},url:"http://localhost:4317",credentials:(A)=>{if(A.startsWith("http://"))return()=>B71.createInsecureCredentials();else return()=>B71.createSslCredentials()}}}P1B.getOtlpGrpcDefaultConfiguration=v$6});
var jP1=E((Jt2)=>{Object.defineProperty(Jt2,"__esModule",{value:!0});Jt2.restrictControlPlaneStatusCode=EE6;var CM=_6(),zE6=[CM.Status.OK,CM.Status.INVALID_ARGUMENT,CM.Status.NOT_FOUND,CM.Status.ALREADY_EXISTS,CM.Status.FAILED_PRECONDITION,CM.Status.ABORTED,CM.Status.OUT_OF_RANGE,CM.Status.DATA_LOSS];function EE6(A,B){if(zE6.includes(A))return{code:CM.Status.INTERNAL,details:`Invalid status from control plane: ${A} ${CM.Status[A]} ${B}`};else return{code:A,details:B}}});
var kT1=E((wn2)=>{Object.defineProperty(wn2,"__esModule",{value:!0});wn2.getErrorMessage=QJ6;wn2.getErrorCode=DJ6;function QJ6(A){if(A instanceof Error)return A.message;else return String(A)}function DJ6(A){if(typeof A==="object"&&A!==null&&"code"in A&&typeof A.code==="number")return A.code;else return null}});
var kr2=E((yu5,dK6)=>{dK6.exports={nested:{google:{nested:{protobuf:{nested:{Api:{fields:{name:{type:"string",id:1},methods:{rule:"repeated",type:"Method",id:2},options:{rule:"repeated",type:"Option",id:3},version:{type:"string",id:4},sourceContext:{type:"SourceContext",id:5},mixins:{rule:"repeated",type:"Mixin",id:6},syntax:{type:"Syntax",id:7}}},Method:{fields:{name:{type:"string",id:1},requestTypeUrl:{type:"string",id:2},requestStreaming:{type:"bool",id:3},responseTypeUrl:{type:"string",id:4},responseStreaming:{type:"bool",id:5},options:{rule:"repeated",type:"Option",id:6},syntax:{type:"Syntax",id:7}}},Mixin:{fields:{name:{type:"string",id:1},root:{type:"string",id:2}}},SourceContext:{fields:{fileName:{type:"string",id:1}}},Option:{fields:{name:{type:"string",id:1},value:{type:"Any",id:2}}},Syntax:{values:{SYNTAX_PROTO2:0,SYNTAX_PROTO3:1}}}}}}}}});
var l1B=E((d1B)=>{Object.defineProperty(d1B,"__esModule",{value:!0});d1B.createOtlpGrpcExportDelegate=void 0;var e$6=xu(),Aq6=A71();function Bq6(A,B,Q,D){return e$6.createOtlpNetworkExportDelegate(A,B,Aq6.createOtlpGrpcExporterTransport({address:A.url,compression:A.compression,credentials:A.credentials,metadata:A.metadata,grpcName:Q,grpcPath:D}))}d1B.createOtlpGrpcExportDelegate=Bq6});
var lJ0=E((So2)=>{Object.defineProperty(So2,"__esModule",{value:!0});So2.parseCIDR=To2;So2.mapProxyName=Hz6;So2.getProxiedConnection=zz6;var d31=F7(),It=_6(),Oo2=J1("net"),Fz6=J1("http"),Iz6=F7(),Ro2=UE(),c31=hV(),Yz6=J1("url"),Wz6=cJ0(),Jz6="proxy";function Yt(A){Iz6.trace(It.LogVerbosity.DEBUG,Jz6,A)}function Xz6(){let A="",B="";if(process.env.grpc_proxy)B="grpc_proxy",A=process.env.grpc_proxy;else if(process.env.https_proxy)B="https_proxy",A=process.env.https_proxy;else if(process.env.http_proxy)B="http_proxy",A=process.env.http_proxy;else return{};let Q;try{Q=new Yz6.URL(A)}catch(I){return d31.log(It.LogVerbosity.ERROR,`cannot parse value of "${B}" env var`),{}}if(Q.protocol!=="http:")return d31.log(It.LogVerbosity.ERROR,`"${Q.protocol}" scheme not supported in proxy URI`),{};let D=null;if(Q.username)if(Q.password)d31.log(It.LogVerbosity.INFO,"userinfo found in proxy URI"),D=decodeURIComponent(`${Q.username}:${Q.password}`);else D=Q.username;let{hostname:Z,port:G}=Q;if(G==="")G="80";let F={address:`${Z}:${G}`};if(D)F.creds=D;return Yt("Proxy server "+F.address+" set by environment variable "+B),F}function Vz6(){let A=process.env.no_grpc_proxy,B="no_grpc_proxy";if(!A)A=process.env.no_proxy,B="no_proxy";if(A)return Yt("No proxy server list set by environment variable "+B),A.split(",");else return[]}function To2(A){let B=A.split("/");if(B.length!==2)return null;let Q=parseInt(B[1],10);if(!Oo2.isIPv4(B[0])||Number.isNaN(Q)||Q<0||Q>32)return null;return{ip:Po2(B[0]),prefixLength:Q}}function Po2(A){return A.split(".").reduce((B,Q)=>(B<<8)+parseInt(Q,10),0)}function Cz6(A,B){let Q=A.ip,D=-1<<32-A.prefixLength;return(Po2(B)&D)===(Q&D)}function Kz6(A){for(let B of Vz6()){let Q=To2(B);if(Oo2.isIPv4(A)&&Q&&Cz6(Q,A))return!0;else if(A.endsWith(B))return!0}return!1}function Hz6(A,B){var Q;let D={target:A,extraOptions:{}};if(((Q=B["grpc.enable_http_proxy"])!==null&&Q!==void 0?Q:1)===0)return D;if(A.scheme==="unix")return D;let Z=Xz6();if(!Z.address)return D;let G=c31.splitHostPort(A.path);if(!G)return D;let F=G.host;if(Kz6(F))return Yt("Not using proxy for target in no_proxy list: "+c31.uriToString(A)),D;let I={"grpc.http_connect_target":c31.uriToString(A)};if(Z.creds)I["grpc.http_connect_creds"]=Z.creds;return{target:{scheme:"dns",path:Z.address},extraOptions:I}}function zz6(A,B){var Q;if(!("grpc.http_connect_target"in B))return Promise.resolve(null);let D=B["grpc.http_connect_target"],Z=c31.parseUri(D);if(Z===null)return Promise.resolve(null);let G=c31.splitHostPort(Z.path);if(G===null)return Promise.resolve(null);let F=`${G.host}:${(Q=G.port)!==null&&Q!==void 0?Q:Wz6.DEFAULT_PORT}`,I={method:"CONNECT",path:F},Y={Host:F};if(Ro2.isTcpSubchannelAddress(A))I.host=A.host,I.port=A.port;else I.socketPath=A.path;if("grpc.http_connect_creds"in B)Y["Proxy-Authorization"]="Basic "+Buffer.from(B["grpc.http_connect_creds"]).toString("base64");I.headers=Y;let W=Ro2.subchannelAddressToString(A);return Yt("Using proxy "+W+" to connect to "+I.path),new Promise((J,X)=>{let V=Fz6.request(I);V.once("connect",(C,K,H)=>{if(V.removeAllListeners(),K.removeAllListeners(),C.statusCode===200){if(Yt("Successfully connected to "+I.path+" through proxy "+W),H.length>0)K.unshift(H);Yt("Successfully established a plaintext connection to "+I.path+" through proxy "+W),J(K)}else d31.log(It.LogVerbosity.ERROR,"Failed to connect to "+I.path+" through proxy "+W+" with status "+C.statusCode),X()}),V.once("error",(C)=>{V.removeAllListeners(),d31.log(It.LogVerbosity.ERROR,"Failed to connect to proxy "+W+" with error "+C.message),X()}),V.end()})}});
var le2=E((ce2)=>{Object.defineProperty(ce2,"__esModule",{value:!0});ce2.setup=zw6;var Hw6=BM();class de2{constructor(A,B,Q){this.listener=B,this.hasReturnedResult=!1,this.endpoints=[];let D;if(A.authority==="")D="/"+A.path;else D=A.path;this.endpoints=[{addresses:[{path:D}]}]}updateResolution(){if(!this.hasReturnedResult)this.hasReturnedResult=!0,process.nextTick(this.listener.onSuccessfulResolution,this.endpoints,null,null,null,{})}destroy(){this.hasReturnedResult=!1}static getDefaultAuthority(A){return"localhost"}}function zw6(){Hw6.registerResolver("unix",de2)}});
var m1B=E((g1B)=>{Object.defineProperty(g1B,"__esModule",{value:!0});g1B.convertLegacyOtlpGrpcOptions=void 0;var s$6=VQ(),h1B=j1B(),r$6=A71(),o$6=f1B();function t$6(A,B){if(A.headers)s$6.diag.warn("Headers cannot be set when using grpc");let Q=A.credentials;return h1B.mergeOtlpGrpcConfigurationWithDefaults({url:A.url,metadata:()=>{return A.metadata??r$6.createEmptyMetadata()},compression:A.compression,timeoutMillis:A.timeoutMillis,concurrencyLimit:A.concurrencyLimit,credentials:Q!=null?()=>Q:void 0},o$6.getOtlpGrpcConfigurationFromEnv(B),h1B.getOtlpGrpcDefaultConfiguration())}g1B.convertLegacyOtlpGrpcOptions=t$6});
var mr2=E((g31,SJ0)=>{(function(A,B){function Q(D){return"default"in D?D.default:D}if(typeof define==="function"&&define.amd)define([],function(){var D={};return B(D),Q(D)});else if(typeof g31==="object"){if(B(g31),typeof SJ0==="object")SJ0.exports=Q(g31)}else(function(){var D={};B(D),A.Long=Q(D)})()})(typeof globalThis!=="undefined"?globalThis:typeof self!=="undefined"?self:g31,function(A){Object.defineProperty(A,"__esModule",{value:!0}),A.default=void 0;var B=null;try{B=new WebAssembly.Instance(new WebAssembly.Module(new Uint8Array([0,97,115,109,1,0,0,0,1,13,2,96,0,1,127,96,4,127,127,127,127,1,127,3,7,6,0,1,1,1,1,1,6,6,1,127,1,65,0,11,7,50,6,3,109,117,108,0,1,5,100,105,118,95,115,0,2,5,100,105,118,95,117,0,3,5,114,101,109,95,115,0,4,5,114,101,109,95,117,0,5,8,103,101,116,95,104,105,103,104,0,0,10,191,1,6,4,0,35,0,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,126,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,127,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,128,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,129,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,130,34,4,66,32,135,167,36,0,32,4,167,11])),{}).exports}catch{}function Q(n,v,t){this.low=n|0,this.high=v|0,this.unsigned=!!t}Q.prototype.__isLong__,Object.defineProperty(Q.prototype,"__isLong__",{value:!0});function D(n){return(n&&n.__isLong__)===!0}function Z(n){var v=Math.clz32(n&-n);return n?31-v:v}Q.isLong=D;var G={},F={};function I(n,v){var t,W1,z1;if(v){if(n>>>=0,z1=0<=n&&n<256){if(W1=F[n],W1)return W1}if(t=W(n,0,!0),z1)F[n]=t;return t}else{if(n|=0,z1=-128<=n&&n<128){if(W1=G[n],W1)return W1}if(t=W(n,n<0?-1:0,!1),z1)G[n]=t;return t}}Q.fromInt=I;function Y(n,v){if(isNaN(n))return v?O:N;if(v){if(n<0)return O;if(n>=z)return y}else{if(n<=-$)return c;if(n+1>=$)return f}if(n<0)return Y(-n,v).neg();return W(n%H|0,n/H|0,v)}Q.fromNumber=Y;function W(n,v,t){return new Q(n,v,t)}Q.fromBits=W;var J=Math.pow;function X(n,v,t){if(n.length===0)throw Error("empty string");if(typeof v==="number")t=v,v=!1;else v=!!v;if(n==="NaN"||n==="Infinity"||n==="+Infinity"||n==="-Infinity")return v?O:N;if(t=t||10,t<2||36<t)throw RangeError("radix");var W1;if((W1=n.indexOf("-"))>0)throw Error("interior hyphen");else if(W1===0)return X(n.substring(1),v,t).neg();var z1=Y(J(t,8)),f1=N;for(var G0=0;G0<n.length;G0+=8){var X0=Math.min(8,n.length-G0),g1=parseInt(n.substring(G0,G0+X0),t);if(X0<8){var K1=Y(J(t,X0));f1=f1.mul(K1).add(Y(g1))}else f1=f1.mul(z1),f1=f1.add(Y(g1))}return f1.unsigned=v,f1}Q.fromString=X;function V(n,v){if(typeof n==="number")return Y(n,v);if(typeof n==="string")return X(n,v);return W(n.low,n.high,typeof v==="boolean"?v:n.unsigned)}Q.fromValue=V;var C=65536,K=16777216,H=C*C,z=H*H,$=z/2,L=I(K),N=I(0);Q.ZERO=N;var O=I(0,!0);Q.UZERO=O;var R=I(1);Q.ONE=R;var T=I(1,!0);Q.UONE=T;var j=I(-1);Q.NEG_ONE=j;var f=W(-1,2147483647,!1);Q.MAX_VALUE=f;var y=W(-1,-1,!0);Q.MAX_UNSIGNED_VALUE=y;var c=W(0,-2147483648,!1);Q.MIN_VALUE=c;var h=Q.prototype;if(h.toInt=function n(){return this.unsigned?this.low>>>0:this.low},h.toNumber=function n(){if(this.unsigned)return(this.high>>>0)*H+(this.low>>>0);return this.high*H+(this.low>>>0)},h.toString=function n(v){if(v=v||10,v<2||36<v)throw RangeError("radix");if(this.isZero())return"0";if(this.isNegative())if(this.eq(c)){var t=Y(v),W1=this.div(t),z1=W1.mul(t).sub(this);return W1.toString(v)+z1.toInt().toString(v)}else return"-"+this.neg().toString(v);var f1=Y(J(v,6),this.unsigned),G0=this,X0="";while(!0){var g1=G0.div(f1),K1=G0.sub(g1.mul(f1)).toInt()>>>0,Q1=K1.toString(v);if(G0=g1,G0.isZero())return Q1+X0;else{while(Q1.length<6)Q1="0"+Q1;X0=""+Q1+X0}}},h.getHighBits=function n(){return this.high},h.getHighBitsUnsigned=function n(){return this.high>>>0},h.getLowBits=function n(){return this.low},h.getLowBitsUnsigned=function n(){return this.low>>>0},h.getNumBitsAbs=function n(){if(this.isNegative())return this.eq(c)?64:this.neg().getNumBitsAbs();var v=this.high!=0?this.high:this.low;for(var t=31;t>0;t--)if((v&1<<t)!=0)break;return this.high!=0?t+33:t+1},h.isSafeInteger=function n(){var v=this.high>>21;if(!v)return!0;if(this.unsigned)return!1;return v===-1&&!(this.low===0&&this.high===-2097152)},h.isZero=function n(){return this.high===0&&this.low===0},h.eqz=h.isZero,h.isNegative=function n(){return!this.unsigned&&this.high<0},h.isPositive=function n(){return this.unsigned||this.high>=0},h.isOdd=function n(){return(this.low&1)===1},h.isEven=function n(){return(this.low&1)===0},h.equals=function n(v){if(!D(v))v=V(v);if(this.unsigned!==v.unsigned&&this.high>>>31===1&&v.high>>>31===1)return!1;return this.high===v.high&&this.low===v.low},h.eq=h.equals,h.notEquals=function n(v){return!this.eq(v)},h.neq=h.notEquals,h.ne=h.notEquals,h.lessThan=function n(v){return this.comp(v)<0},h.lt=h.lessThan,h.lessThanOrEqual=function n(v){return this.comp(v)<=0},h.lte=h.lessThanOrEqual,h.le=h.lessThanOrEqual,h.greaterThan=function n(v){return this.comp(v)>0},h.gt=h.greaterThan,h.greaterThanOrEqual=function n(v){return this.comp(v)>=0},h.gte=h.greaterThanOrEqual,h.ge=h.greaterThanOrEqual,h.compare=function n(v){if(!D(v))v=V(v);if(this.eq(v))return 0;var t=this.isNegative(),W1=v.isNegative();if(t&&!W1)return-1;if(!t&&W1)return 1;if(!this.unsigned)return this.sub(v).isNegative()?-1:1;return v.high>>>0>this.high>>>0||v.high===this.high&&v.low>>>0>this.low>>>0?-1:1},h.comp=h.compare,h.negate=function n(){if(!this.unsigned&&this.eq(c))return c;return this.not().add(R)},h.neg=h.negate,h.add=function n(v){if(!D(v))v=V(v);var t=this.high>>>16,W1=this.high&65535,z1=this.low>>>16,f1=this.low&65535,G0=v.high>>>16,X0=v.high&65535,g1=v.low>>>16,K1=v.low&65535,Q1=0,_1=0,q1=0,B0=0;return B0+=f1+K1,q1+=B0>>>16,B0&=65535,q1+=z1+g1,_1+=q1>>>16,q1&=65535,_1+=W1+X0,Q1+=_1>>>16,_1&=65535,Q1+=t+G0,Q1&=65535,W(q1<<16|B0,Q1<<16|_1,this.unsigned)},h.subtract=function n(v){if(!D(v))v=V(v);return this.add(v.neg())},h.sub=h.subtract,h.multiply=function n(v){if(this.isZero())return this;if(!D(v))v=V(v);if(B){var t=B.mul(this.low,this.high,v.low,v.high);return W(t,B.get_high(),this.unsigned)}if(v.isZero())return this.unsigned?O:N;if(this.eq(c))return v.isOdd()?c:N;if(v.eq(c))return this.isOdd()?c:N;if(this.isNegative())if(v.isNegative())return this.neg().mul(v.neg());else return this.neg().mul(v).neg();else if(v.isNegative())return this.mul(v.neg()).neg();if(this.lt(L)&&v.lt(L))return Y(this.toNumber()*v.toNumber(),this.unsigned);var W1=this.high>>>16,z1=this.high&65535,f1=this.low>>>16,G0=this.low&65535,X0=v.high>>>16,g1=v.high&65535,K1=v.low>>>16,Q1=v.low&65535,_1=0,q1=0,B0=0,K0=0;return K0+=G0*Q1,B0+=K0>>>16,K0&=65535,B0+=f1*Q1,q1+=B0>>>16,B0&=65535,B0+=G0*K1,q1+=B0>>>16,B0&=65535,q1+=z1*Q1,_1+=q1>>>16,q1&=65535,q1+=f1*K1,_1+=q1>>>16,q1&=65535,q1+=G0*g1,_1+=q1>>>16,q1&=65535,_1+=W1*Q1+z1*K1+f1*g1+G0*X0,_1&=65535,W(B0<<16|K0,_1<<16|q1,this.unsigned)},h.mul=h.multiply,h.divide=function n(v){if(!D(v))v=V(v);if(v.isZero())throw Error("division by zero");if(B){if(!this.unsigned&&this.high===-2147483648&&v.low===-1&&v.high===-1)return this;var t=(this.unsigned?B.div_u:B.div_s)(this.low,this.high,v.low,v.high);return W(t,B.get_high(),this.unsigned)}if(this.isZero())return this.unsigned?O:N;var W1,z1,f1;if(!this.unsigned){if(this.eq(c))if(v.eq(R)||v.eq(j))return c;else if(v.eq(c))return R;else{var G0=this.shr(1);if(W1=G0.div(v).shl(1),W1.eq(N))return v.isNegative()?R:j;else return z1=this.sub(v.mul(W1)),f1=W1.add(z1.div(v)),f1}else if(v.eq(c))return this.unsigned?O:N;if(this.isNegative()){if(v.isNegative())return this.neg().div(v.neg());return this.neg().div(v).neg()}else if(v.isNegative())return this.div(v.neg()).neg();f1=N}else{if(!v.unsigned)v=v.toUnsigned();if(v.gt(this))return O;if(v.gt(this.shru(1)))return T;f1=O}z1=this;while(z1.gte(v)){W1=Math.max(1,Math.floor(z1.toNumber()/v.toNumber()));var X0=Math.ceil(Math.log(W1)/Math.LN2),g1=X0<=48?1:J(2,X0-48),K1=Y(W1),Q1=K1.mul(v);while(Q1.isNegative()||Q1.gt(z1))W1-=g1,K1=Y(W1,this.unsigned),Q1=K1.mul(v);if(K1.isZero())K1=R;f1=f1.add(K1),z1=z1.sub(Q1)}return f1},h.div=h.divide,h.modulo=function n(v){if(!D(v))v=V(v);if(B){var t=(this.unsigned?B.rem_u:B.rem_s)(this.low,this.high,v.low,v.high);return W(t,B.get_high(),this.unsigned)}return this.sub(this.div(v).mul(v))},h.mod=h.modulo,h.rem=h.modulo,h.not=function n(){return W(~this.low,~this.high,this.unsigned)},h.countLeadingZeros=function n(){return this.high?Math.clz32(this.high):Math.clz32(this.low)+32},h.clz=h.countLeadingZeros,h.countTrailingZeros=function n(){return this.low?Z(this.low):Z(this.high)+32},h.ctz=h.countTrailingZeros,h.and=function n(v){if(!D(v))v=V(v);return W(this.low&v.low,this.high&v.high,this.unsigned)},h.or=function n(v){if(!D(v))v=V(v);return W(this.low|v.low,this.high|v.high,this.unsigned)},h.xor=function n(v){if(!D(v))v=V(v);return W(this.low^v.low,this.high^v.high,this.unsigned)},h.shiftLeft=function n(v){if(D(v))v=v.toInt();if((v&=63)===0)return this;else if(v<32)return W(this.low<<v,this.high<<v|this.low>>>32-v,this.unsigned);else return W(0,this.low<<v-32,this.unsigned)},h.shl=h.shiftLeft,h.shiftRight=function n(v){if(D(v))v=v.toInt();if((v&=63)===0)return this;else if(v<32)return W(this.low>>>v|this.high<<32-v,this.high>>v,this.unsigned);else return W(this.high>>v-32,this.high>=0?0:-1,this.unsigned)},h.shr=h.shiftRight,h.shiftRightUnsigned=function n(v){if(D(v))v=v.toInt();if((v&=63)===0)return this;if(v<32)return W(this.low>>>v|this.high<<32-v,this.high>>>v,this.unsigned);if(v===32)return W(this.high,0,this.unsigned);return W(this.high>>>v-32,0,this.unsigned)},h.shru=h.shiftRightUnsigned,h.shr_u=h.shiftRightUnsigned,h.rotateLeft=function n(v){var t;if(D(v))v=v.toInt();if((v&=63)===0)return this;if(v===32)return W(this.high,this.low,this.unsigned);if(v<32)return t=32-v,W(this.low<<v|this.high>>>t,this.high<<v|this.low>>>t,this.unsigned);return v-=32,t=32-v,W(this.high<<v|this.low>>>t,this.low<<v|this.high>>>t,this.unsigned)},h.rotl=h.rotateLeft,h.rotateRight=function n(v){var t;if(D(v))v=v.toInt();if((v&=63)===0)return this;if(v===32)return W(this.high,this.low,this.unsigned);if(v<32)return t=32-v,W(this.high<<t|this.low>>>v,this.low<<t|this.high>>>v,this.unsigned);return v-=32,t=32-v,W(this.low<<t|this.high>>>v,this.high<<t|this.low>>>v,this.unsigned)},h.rotr=h.rotateRight,h.toSigned=function n(){if(!this.unsigned)return this;return W(this.low,this.high,!1)},h.toUnsigned=function n(){if(this.unsigned)return this;return W(this.low,this.high,!0)},h.toBytes=function n(v){return v?this.toBytesLE():this.toBytesBE()},h.toBytesLE=function n(){var v=this.high,t=this.low;return[t&255,t>>>8&255,t>>>16&255,t>>>24,v&255,v>>>8&255,v>>>16&255,v>>>24]},h.toBytesBE=function n(){var v=this.high,t=this.low;return[v>>>24,v>>>16&255,v>>>8&255,v&255,t>>>24,t>>>16&255,t>>>8&255,t&255]},Q.fromBytes=function n(v,t,W1){return W1?Q.fromBytesLE(v,t):Q.fromBytesBE(v,t)},Q.fromBytesLE=function n(v,t){return new Q(v[0]|v[1]<<8|v[2]<<16|v[3]<<24,v[4]|v[5]<<8|v[6]<<16|v[7]<<24,t)},Q.fromBytesBE=function n(v,t){return new Q(v[4]<<24|v[5]<<16|v[6]<<8|v[7],v[0]<<24|v[1]<<16|v[2]<<8|v[3],t)},typeof BigInt==="function")Q.fromBigInt=function n(v,t){var W1=Number(BigInt.asIntN(32,v)),z1=Number(BigInt.asIntN(32,v>>BigInt(32)));return W(W1,z1,t)},Q.fromValue=function n(v,t){if(typeof v==="bigint")return fromBigInt(v,t);return V(v,t)},h.toBigInt=function n(){var v=BigInt(this.low>>>0),t=BigInt(this.unsigned?this.high>>>0:this.high);return t<<BigInt(32)|v};var a=A.default=Q})});
var mu=E((Ss2)=>{var j31=Ss2,yC6=$I(),kC6=["double","float","int32","uint32","sint32","fixed32","sfixed32","int64","uint64","sint64","fixed64","sfixed64","bool","string","bytes"];function y31(A,B){var Q=0,D={};B|=0;while(Q<A.length)D[kC6[Q+B]]=A[Q++];return D}j31.basic=y31([1,5,0,0,0,5,5,0,0,0,1,1,0,2,2]);j31.defaults=y31([0,0,0,0,0,0,0,0,0,0,0,0,!1,"",yC6.emptyArray,null]);j31.long=y31([0,0,0,1,1],7);j31.mapKey=y31([0,0,0,5,5,0,0,0,1,1,0,2],2);j31.packed=y31([1,5,0,0,0,5,5,0,0,0,1,1,0])});
var nJ0=E((fo2)=>{Object.defineProperty(fo2,"__esModule",{value:!0});fo2.getNextCallNumber=Tz6;var Oz6=0;function Tz6(){return Oz6++}});
var nT1=E((Ma2)=>{Object.defineProperty(Ma2,"__esModule",{value:!0});Ma2.registerAdminService=IV6;Ma2.addAdminServicesToServer=YV6;var La2=[];function IV6(A,B){La2.push({getServiceDefinition:A,getHandlers:B})}function YV6(A){for(let{getServiceDefinition:B,getHandlers:Q}of La2)A.addService(B(),Q())}});
var o_=E((Yu5,ks2)=>{ks2.exports=wE;var tT1=du();((wE.prototype=Object.create(tT1.prototype)).constructor=wE).className="Field";var js2=Z$(),ys2=mu(),eG=$I(),AJ0,_C6=/^required|optional|repeated$/;wE.fromJSON=function A(B,Q){return new wE(B,Q.id,Q.type,Q.rule,Q.extend,Q.options,Q.comment)};function wE(A,B,Q,D,Z,G,F){if(eG.isObject(D))F=Z,G=D,D=Z=void 0;else if(eG.isObject(Z))F=G,G=Z,Z=void 0;if(tT1.call(this,A,G),!eG.isInteger(B)||B<0)throw TypeError("id must be a non-negative integer");if(!eG.isString(Q))throw TypeError("type must be a string");if(D!==void 0&&!_C6.test(D=D.toString().toLowerCase()))throw TypeError("rule must be a string rule");if(Z!==void 0&&!eG.isString(Z))throw TypeError("extend must be a string");if(D==="proto3_optional")D="optional";this.rule=D&&D!=="optional"?D:void 0,this.type=Q,this.id=B,this.extend=Z||void 0,this.required=D==="required",this.optional=!this.required,this.repeated=D==="repeated",this.map=!1,this.message=null,this.partOf=null,this.typeDefault=null,this.defaultValue=null,this.long=eG.Long?ys2.long[Q]!==void 0:!1,this.bytes=Q==="bytes",this.resolvedType=null,this.extensionField=null,this.declaringField=null,this._packed=null,this.comment=F}Object.defineProperty(wE.prototype,"packed",{get:function(){if(this._packed===null)this._packed=this.getOption("packed")!==!1;return this._packed}});wE.prototype.setOption=function A(B,Q,D){if(B==="packed")this._packed=null;return tT1.prototype.setOption.call(this,B,Q,D)};wE.prototype.toJSON=function A(B){var Q=B?Boolean(B.keepComments):!1;return eG.toObject(["rule",this.rule!=="optional"&&this.rule||void 0,"type",this.type,"id",this.id,"extend",this.extend,"options",this.options,"comment",Q?this.comment:void 0])};wE.prototype.resolve=function A(){if(this.resolved)return this;if((this.typeDefault=ys2.defaults[this.type])===void 0)if(this.resolvedType=(this.declaringField?this.declaringField.parent:this.parent).lookupTypeOrEnum(this.type),this.resolvedType instanceof AJ0)this.typeDefault=null;else this.typeDefault=this.resolvedType.values[Object.keys(this.resolvedType.values)[0]];else if(this.options&&this.options.proto3_optional)this.typeDefault=null;if(this.options&&this.options.default!=null){if(this.typeDefault=this.options.default,this.resolvedType instanceof js2&&typeof this.typeDefault==="string")this.typeDefault=this.resolvedType.values[this.typeDefault]}if(this.options){if(this.options.packed===!0||this.options.packed!==void 0&&this.resolvedType&&!(this.resolvedType instanceof js2))delete this.options.packed;if(!Object.keys(this.options).length)this.options=void 0}if(this.long){if(this.typeDefault=eG.Long.fromNumber(this.typeDefault,this.type.charAt(0)==="u"),Object.freeze)Object.freeze(this.typeDefault)}else if(this.bytes&&typeof this.typeDefault==="string"){var B;if(eG.base64.test(this.typeDefault))eG.base64.decode(this.typeDefault,B=eG.newBuffer(eG.base64.length(this.typeDefault)),0);else eG.utf8.write(this.typeDefault,B=eG.newBuffer(eG.utf8.length(this.typeDefault)),0);this.typeDefault=B}if(this.map)this.defaultValue=eG.emptyObject;else if(this.repeated)this.defaultValue=eG.emptyArray;else this.defaultValue=this.typeDefault;if(this.parent instanceof AJ0)this.parent.ctor.prototype[this.name]=this.defaultValue;return tT1.prototype.resolve.call(this)};wE.d=function A(B,Q,D,Z){if(typeof Q==="function")Q=eG.decorateType(Q).name;else if(Q&&typeof Q==="object")Q=eG.decorateEnum(Q).name;return function G(F,I){eG.decorateType(F.constructor).add(new wE(I,B,Q,D,{default:Z}))}};wE._configure=function A(B){AJ0=B}});
var or2=E((sr2)=>{Object.defineProperty(sr2,"__esModule",{value:!0});sr2.loadFileDescriptorSetFromObject=sr2.loadFileDescriptorSetFromBuffer=sr2.fromJSON=sr2.loadSync=sr2.load=sr2.IdempotencyLevel=sr2.isAnyExtension=sr2.Long=void 0;var rK6=qs2(),YM=HP1(),yJ0=yr2(),kJ0=ur2(),oK6=mr2();sr2.Long=oK6;function tK6(A){return"@type"in A&&typeof A["@type"]==="string"}sr2.isAnyExtension=tK6;var pr2;(function(A){A.IDEMPOTENCY_UNKNOWN="IDEMPOTENCY_UNKNOWN",A.NO_SIDE_EFFECTS="NO_SIDE_EFFECTS",A.IDEMPOTENT="IDEMPOTENT"})(pr2=sr2.IdempotencyLevel||(sr2.IdempotencyLevel={}));var ir2={longs:String,enums:String,bytes:String,defaults:!0,oneofs:!0,json:!0};function eK6(A,B){if(A==="")return B;else return A+"."+B}function AH6(A){return A instanceof YM.Service||A instanceof YM.Type||A instanceof YM.Enum}function BH6(A){return A instanceof YM.Namespace||A instanceof YM.Root}function nr2(A,B){let Q=eK6(B,A.name);if(AH6(A))return[[Q,A]];else if(BH6(A)&&typeof A.nested!=="undefined")return Object.keys(A.nested).map((D)=>{return nr2(A.nested[D],Q)}).reduce((D,Z)=>D.concat(Z),[]);return[]}function dr2(A,B){return function Q(D){return A.toObject(A.decode(D),B)}}function cr2(A){return function B(Q){if(Array.isArray(Q))throw new Error(`Failed to serialize message: expected object with ${A.name} structure, got array instead`);let D=A.fromObject(Q);return A.encode(D).finish()}}function QH6(A){return(A||[]).reduce((B,Q)=>{for(let[D,Z]of Object.entries(Q))switch(D){case"uninterpreted_option":B.uninterpreted_option.push(Q.uninterpreted_option);break;default:B[D]=Z}return B},{deprecated:!1,idempotency_level:pr2.IDEMPOTENCY_UNKNOWN,uninterpreted_option:[]})}function DH6(A,B,Q,D){let{resolvedRequestType:Z,resolvedResponseType:G}=A;return{path:"/"+B+"/"+A.name,requestStream:!!A.requestStream,responseStream:!!A.responseStream,requestSerialize:cr2(Z),requestDeserialize:dr2(Z,Q),responseSerialize:cr2(G),responseDeserialize:dr2(G,Q),originalName:rK6(A.name),requestType:jJ0(Z,D),responseType:jJ0(G,D),options:QH6(A.parsedOptions)}}function ZH6(A,B,Q,D){let Z={};for(let G of A.methodsArray)Z[G.name]=DH6(G,B,Q,D);return Z}function jJ0(A,B){let Q=A.toDescriptor("proto3");return{format:"Protocol Buffer 3 DescriptorProto",type:Q.$type.toObject(Q,ir2),fileDescriptorProtos:B}}function GH6(A,B){let Q=A.toDescriptor("proto3");return{format:"Protocol Buffer 3 EnumDescriptorProto",type:Q.$type.toObject(Q,ir2),fileDescriptorProtos:B}}function FH6(A,B,Q,D){if(A instanceof YM.Service)return ZH6(A,B,Q,D);else if(A instanceof YM.Type)return jJ0(A,D);else if(A instanceof YM.Enum)return GH6(A,D);else throw new Error("Type mismatch in reflection object handling")}function UP1(A,B){let Q={};A.resolveAll();let Z=A.toDescriptor("proto3").file.map((G)=>Buffer.from(yJ0.FileDescriptorProto.encode(G).finish()));for(let[G,F]of nr2(A,""))Q[G]=FH6(F,G,B,Z);return Q}function ar2(A,B){B=B||{};let Q=YM.Root.fromDescriptor(A);return Q.resolveAll(),UP1(Q,B)}function IH6(A,B){return kJ0.loadProtosWithOptions(A,B).then((Q)=>{return UP1(Q,B)})}sr2.load=IH6;function YH6(A,B){let Q=kJ0.loadProtosWithOptionsSync(A,B);return UP1(Q,B)}sr2.loadSync=YH6;function WH6(A,B){B=B||{};let Q=YM.Root.fromJSON(A);return Q.resolveAll(),UP1(Q,B)}sr2.fromJSON=WH6;function JH6(A,B){let Q=yJ0.FileDescriptorSet.decode(A);return ar2(Q,B)}sr2.loadFileDescriptorSetFromBuffer=JH6;function XH6(A,B){let Q=yJ0.FileDescriptorSet.fromObject(A);return ar2(Q,B)}sr2.loadFileDescriptorSetFromObject=XH6;kJ0.addCommonProtos()});
var pJ0=E((yo2)=>{Object.defineProperty(yo2,"__esModule",{value:!0});yo2.StreamDecoder=void 0;var VM;(function(A){A[A.NO_DATA=0]="NO_DATA",A[A.READING_SIZE=1]="READING_SIZE",A[A.READING_MESSAGE=2]="READING_MESSAGE"})(VM||(VM={}));class jo2{constructor(A){this.maxReadMessageLength=A,this.readState=VM.NO_DATA,this.readCompressFlag=Buffer.alloc(1),this.readPartialSize=Buffer.alloc(4),this.readSizeRemaining=4,this.readMessageSize=0,this.readPartialMessage=[],this.readMessageRemaining=0}write(A){let B=0,Q,D=[];while(B<A.length)switch(this.readState){case VM.NO_DATA:this.readCompressFlag=A.slice(B,B+1),B+=1,this.readState=VM.READING_SIZE,this.readPartialSize.fill(0),this.readSizeRemaining=4,this.readMessageSize=0,this.readMessageRemaining=0,this.readPartialMessage=[];break;case VM.READING_SIZE:if(Q=Math.min(A.length-B,this.readSizeRemaining),A.copy(this.readPartialSize,4-this.readSizeRemaining,B,B+Q),this.readSizeRemaining-=Q,B+=Q,this.readSizeRemaining===0){if(this.readMessageSize=this.readPartialSize.readUInt32BE(0),this.maxReadMessageLength!==-1&&this.readMessageSize>this.maxReadMessageLength)throw new Error(`Received message larger than max (${this.readMessageSize} vs ${this.maxReadMessageLength})`);if(this.readMessageRemaining=this.readMessageSize,this.readMessageRemaining>0)this.readState=VM.READING_MESSAGE;else{let Z=Buffer.concat([this.readCompressFlag,this.readPartialSize],5);this.readState=VM.NO_DATA,D.push(Z)}}break;case VM.READING_MESSAGE:if(Q=Math.min(A.length-B,this.readMessageRemaining),this.readPartialMessage.push(A.slice(B,B+Q)),this.readMessageRemaining-=Q,B+=Q,this.readMessageRemaining===0){let Z=[this.readCompressFlag,this.readPartialSize].concat(this.readPartialMessage),G=Buffer.concat(Z,this.readMessageSize+5);this.readState=VM.NO_DATA,D.push(G)}break;default:throw new Error("Unexpected read state")}return D}}yo2.StreamDecoder=jo2});
var pT1=E((tn2)=>{Object.defineProperty(tn2,"__esModule",{value:!0});tn2.ChildLoadBalancerHandler=void 0;var jX6=gu(),yX6=EE(),kX6="child_load_balancer_helper";class on2{constructor(A){this.channelControlHelper=A,this.currentChild=null,this.pendingChild=null,this.latestConfig=null,this.ChildPolicyHelper=class{constructor(B){this.parent=B,this.child=null}createSubchannel(B,Q){return this.parent.channelControlHelper.createSubchannel(B,Q)}updateState(B,Q,D){var Z;if(this.calledByPendingChild()){if(B===yX6.ConnectivityState.CONNECTING)return;(Z=this.parent.currentChild)===null||Z===void 0||Z.destroy(),this.parent.currentChild=this.parent.pendingChild,this.parent.pendingChild=null}else if(!this.calledByCurrentChild())return;this.parent.channelControlHelper.updateState(B,Q,D)}requestReresolution(){var B;let Q=(B=this.parent.pendingChild)!==null&&B!==void 0?B:this.parent.currentChild;if(this.child===Q)this.parent.channelControlHelper.requestReresolution()}setChild(B){this.child=B}addChannelzChild(B){this.parent.channelControlHelper.addChannelzChild(B)}removeChannelzChild(B){this.parent.channelControlHelper.removeChannelzChild(B)}calledByPendingChild(){return this.child===this.parent.pendingChild}calledByCurrentChild(){return this.child===this.parent.currentChild}}}configUpdateRequiresNewPolicyInstance(A,B){return A.getLoadBalancerName()!==B.getLoadBalancerName()}updateAddressList(A,B,Q){let D;if(this.currentChild===null||this.latestConfig===null||this.configUpdateRequiresNewPolicyInstance(this.latestConfig,B)){let Z=new this.ChildPolicyHelper(this),G=jX6.createLoadBalancer(B,Z);if(Z.setChild(G),this.currentChild===null)this.currentChild=G,D=this.currentChild;else{if(this.pendingChild)this.pendingChild.destroy();this.pendingChild=G,D=this.pendingChild}}else if(this.pendingChild===null)D=this.currentChild;else D=this.pendingChild;this.latestConfig=B,D.updateAddressList(A,B,Q)}exitIdle(){if(this.currentChild){if(this.currentChild.exitIdle(),this.pendingChild)this.pendingChild.exitIdle()}}resetBackoff(){if(this.currentChild){if(this.currentChild.resetBackoff(),this.pendingChild)this.pendingChild.resetBackoff()}}destroy(){if(this.currentChild)this.currentChild.destroy(),this.currentChild=null;if(this.pendingChild)this.pendingChild.destroy(),this.pendingChild=null}getTypeName(){return kX6}}tn2.ChildLoadBalancerHandler=on2});
var pW0=E((ra2)=>{Object.defineProperty(ra2,"__esModule",{value:!0});ra2.makeClientConstructor=sa2;ra2.loadPackageDefinition=kV6;var P31=dW0(),SV6={unary:P31.Client.prototype.makeUnaryRequest,server_stream:P31.Client.prototype.makeServerStreamRequest,client_stream:P31.Client.prototype.makeClientStreamRequest,bidi:P31.Client.prototype.makeBidiStreamRequest};function lW0(A){return["__proto__","prototype","constructor"].includes(A)}function sa2(A,B,Q){if(!Q)Q={};class D extends P31.Client{}return Object.keys(A).forEach((Z)=>{if(lW0(Z))return;let G=A[Z],F;if(typeof Z==="string"&&Z.charAt(0)==="$")throw new Error("Method names cannot start with $");if(G.requestStream)if(G.responseStream)F="bidi";else F="client_stream";else if(G.responseStream)F="server_stream";else F="unary";let{requestSerialize:I,responseDeserialize:Y}=G,W=jV6(SV6[F],G.path,I,Y);if(D.prototype[Z]=W,Object.assign(D.prototype[Z],G),G.originalName&&!lW0(G.originalName))D.prototype[G.originalName]=D.prototype[Z]}),D.service=A,D.serviceName=B,D}function jV6(A,B,Q,D){return function(...Z){return A.call(this,B,Q,D,...Z)}}function yV6(A){return"format"in A}function kV6(A){let B={};for(let Q in A)if(Object.prototype.hasOwnProperty.call(A,Q)){let D=A[Q],Z=Q.split(".");if(Z.some((I)=>lW0(I)))continue;let G=Z[Z.length-1],F=B;for(let I of Z.slice(0,-1)){if(!F[I])F[I]={};F=F[I]}if(yV6(D))F[G]=D;else F[G]=sa2(D,G,{})}return B}});
var po2=E((co2)=>{Object.defineProperty(co2,"__esModule",{value:!0});co2.SubchannelPool=void 0;co2.getSubchannelPool=oz6;var lz6=Ia2(),pz6=zo2(),iz6=UE(),nz6=hV(),az6=do2(),sz6=1e4;class PP1{constructor(){this.pool=Object.create(null),this.cleanupTimer=null}unrefUnusedSubchannels(){let A=!0;for(let B in this.pool){let D=this.pool[B].filter((Z)=>!Z.subchannel.unrefIfOneRef());if(D.length>0)A=!1;this.pool[B]=D}if(A&&this.cleanupTimer!==null)clearInterval(this.cleanupTimer),this.cleanupTimer=null}ensureCleanupTask(){var A,B;if(this.cleanupTimer===null)this.cleanupTimer=setInterval(()=>{this.unrefUnusedSubchannels()},sz6),(B=(A=this.cleanupTimer).unref)===null||B===void 0||B.call(A)}getOrCreateSubchannel(A,B,Q,D){this.ensureCleanupTask();let Z=nz6.uriToString(A);if(Z in this.pool){let F=this.pool[Z];for(let I of F)if(iz6.subchannelAddressEqual(B,I.subchannelAddress)&&lz6.channelOptionsEqual(Q,I.channelArguments)&&D._equals(I.channelCredentials))return I.subchannel}let G=new pz6.Subchannel(A,B,Q,D,new az6.Http2SubchannelConnector(A));if(!(Z in this.pool))this.pool[Z]=[];return this.pool[Z].push({subchannelAddress:B,channelArguments:Q,channelCredentials:D,subchannel:G}),G.ref(),G}}co2.SubchannelPool=PP1;var rz6=new PP1;function oz6(A){if(A)return rz6;else return new PP1}});
var pu=E((Xo2)=>{var __dirname="/home/<USER>/code/tmp/claude-cli-external-build-2536/node_modules/@grpc/grpc-js/build/src";Object.defineProperty(Xo2,"__esModule",{value:!0});Xo2.registerChannelzSocket=Xo2.registerChannelzServer=Xo2.registerChannelzSubchannel=Xo2.registerChannelzChannel=Xo2.ChannelzCallTrackerStub=Xo2.ChannelzCallTracker=Xo2.ChannelzChildrenTrackerStub=Xo2.ChannelzChildrenTracker=Xo2.ChannelzTrace=Xo2.ChannelzTraceStub=void 0;Xo2.unregisterChannelzRef=NH6;Xo2.getChannelzHandlers=Wo2;Xo2.getChannelzServiceDefinition=Jo2;Xo2.setup=_H6;var $P1=J1("net"),lu=Na2(),u31=EE(),m31=_6(),UH6=UE(),wH6=nT1(),$H6=pW0();function _J0(A){return{channel_id:A.id,name:A.name}}function xJ0(A){return{subchannel_id:A.id,name:A.name}}function qH6(A){return{server_id:A.id}}function qP1(A){return{socket_id:A.id,name:A.name}}var tr2=32,vJ0=100;class Qo2{constructor(){this.events=[],this.creationTimestamp=new Date,this.eventsLogged=0}addTrace(){}getTraceMessage(){return{creation_timestamp:WM(this.creationTimestamp),num_events_logged:this.eventsLogged,events:[]}}}Xo2.ChannelzTraceStub=Qo2;class Do2{constructor(){this.events=[],this.eventsLogged=0,this.creationTimestamp=new Date}addTrace(A,B,Q){let D=new Date;if(this.events.push({description:B,severity:A,timestamp:D,childChannel:(Q===null||Q===void 0?void 0:Q.kind)==="channel"?Q:void 0,childSubchannel:(Q===null||Q===void 0?void 0:Q.kind)==="subchannel"?Q:void 0}),this.events.length>=tr2*2)this.events=this.events.slice(tr2);this.eventsLogged+=1}getTraceMessage(){return{creation_timestamp:WM(this.creationTimestamp),num_events_logged:this.eventsLogged,events:this.events.map((A)=>{return{description:A.description,severity:A.severity,timestamp:WM(A.timestamp),channel_ref:A.childChannel?_J0(A.childChannel):null,subchannel_ref:A.childSubchannel?xJ0(A.childSubchannel):null}})}}}Xo2.ChannelzTrace=Do2;class bJ0{constructor(){this.channelChildren=new lu.OrderedMap,this.subchannelChildren=new lu.OrderedMap,this.socketChildren=new lu.OrderedMap,this.trackerMap={["channel"]:this.channelChildren,["subchannel"]:this.subchannelChildren,["socket"]:this.socketChildren}}refChild(A){let B=this.trackerMap[A.kind],Q=B.find(A.id);if(Q.equals(B.end()))B.setElement(A.id,{ref:A,count:1},Q);else Q.pointer[1].count+=1}unrefChild(A){let B=this.trackerMap[A.kind],Q=B.getElementByKey(A.id);if(Q!==void 0){if(Q.count-=1,Q.count===0)B.eraseElementByKey(A.id)}}getChildLists(){return{channels:this.channelChildren,subchannels:this.subchannelChildren,sockets:this.socketChildren}}}Xo2.ChannelzChildrenTracker=bJ0;class Zo2 extends bJ0{refChild(){}unrefChild(){}}Xo2.ChannelzChildrenTrackerStub=Zo2;class fJ0{constructor(){this.callsStarted=0,this.callsSucceeded=0,this.callsFailed=0,this.lastCallStartedTimestamp=null}addCallStarted(){this.callsStarted+=1,this.lastCallStartedTimestamp=new Date}addCallSucceeded(){this.callsSucceeded+=1}addCallFailed(){this.callsFailed+=1}}Xo2.ChannelzCallTracker=fJ0;class Go2 extends fJ0{addCallStarted(){}addCallSucceeded(){}addCallFailed(){}}Xo2.ChannelzCallTrackerStub=Go2;var qP={["channel"]:new lu.OrderedMap,["subchannel"]:new lu.OrderedMap,["server"]:new lu.OrderedMap,["socket"]:new lu.OrderedMap},NP1=(A)=>{let B=1;function Q(){return B++}let D=qP[A];return(Z,G,F)=>{let I=Q(),Y={id:I,name:Z,kind:A};if(F)D.setElement(I,{ref:Y,getInfo:G});return Y}};Xo2.registerChannelzChannel=NP1("channel");Xo2.registerChannelzSubchannel=NP1("subchannel");Xo2.registerChannelzServer=NP1("server");Xo2.registerChannelzSocket=NP1("socket");function NH6(A){qP[A.kind].eraseElementByKey(A.id)}function LH6(A){let B=Number.parseInt(A,16);return[B/256|0,B%256]}function er2(A){if(A==="")return[];let B=A.split(":").map((D)=>LH6(D));return[].concat(...B)}function MH6(A){return $P1.isIPv6(A)&&A.toLowerCase().startsWith("::ffff:")&&$P1.isIPv4(A.substring(7))}function Ao2(A){return Buffer.from(Uint8Array.from(A.split(".").map((B)=>Number.parseInt(B))))}function RH6(A){if($P1.isIPv4(A))return Ao2(A);else if(MH6(A))return Ao2(A.substring(7));else if($P1.isIPv6(A)){let B,Q,D=A.indexOf("::");if(D===-1)B=A,Q="";else B=A.substring(0,D),Q=A.substring(D+2);let Z=Buffer.from(er2(B)),G=Buffer.from(er2(Q)),F=Buffer.alloc(16-Z.length-G.length,0);return Buffer.concat([Z,F,G])}else return null}function Fo2(A){switch(A){case u31.ConnectivityState.CONNECTING:return{state:"CONNECTING"};case u31.ConnectivityState.IDLE:return{state:"IDLE"};case u31.ConnectivityState.READY:return{state:"READY"};case u31.ConnectivityState.SHUTDOWN:return{state:"SHUTDOWN"};case u31.ConnectivityState.TRANSIENT_FAILURE:return{state:"TRANSIENT_FAILURE"};default:return{state:"UNKNOWN"}}}function WM(A){if(!A)return null;let B=A.getTime();return{seconds:B/1000|0,nanos:B%1000*1e6}}function Io2(A){let B=A.getInfo(),Q=[],D=[];return B.children.channels.forEach((Z)=>{Q.push(_J0(Z[1].ref))}),B.children.subchannels.forEach((Z)=>{D.push(xJ0(Z[1].ref))}),{ref:_J0(A.ref),data:{target:B.target,state:Fo2(B.state),calls_started:B.callTracker.callsStarted,calls_succeeded:B.callTracker.callsSucceeded,calls_failed:B.callTracker.callsFailed,last_call_started_timestamp:WM(B.callTracker.lastCallStartedTimestamp),trace:B.trace.getTraceMessage()},channel_ref:Q,subchannel_ref:D}}function OH6(A,B){let Q=parseInt(A.request.channel_id,10),D=qP.channel.getElementByKey(Q);if(D===void 0){B({code:m31.Status.NOT_FOUND,details:"No channel data found for id "+Q});return}B(null,{channel:Io2(D)})}function TH6(A,B){let Q=parseInt(A.request.max_results,10)||vJ0,D=[],Z=parseInt(A.request.start_channel_id,10),G=qP.channel,F;for(F=G.lowerBound(Z);!F.equals(G.end())&&D.length<Q;F=F.next())D.push(Io2(F.pointer[1]));B(null,{channel:D,end:F.equals(G.end())})}function Yo2(A){let B=A.getInfo(),Q=[];return B.listenerChildren.sockets.forEach((D)=>{Q.push(qP1(D[1].ref))}),{ref:qH6(A.ref),data:{calls_started:B.callTracker.callsStarted,calls_succeeded:B.callTracker.callsSucceeded,calls_failed:B.callTracker.callsFailed,last_call_started_timestamp:WM(B.callTracker.lastCallStartedTimestamp),trace:B.trace.getTraceMessage()},listen_socket:Q}}function PH6(A,B){let Q=parseInt(A.request.server_id,10),Z=qP.server.getElementByKey(Q);if(Z===void 0){B({code:m31.Status.NOT_FOUND,details:"No server data found for id "+Q});return}B(null,{server:Yo2(Z)})}function SH6(A,B){let Q=parseInt(A.request.max_results,10)||vJ0,D=parseInt(A.request.start_server_id,10),Z=qP.server,G=[],F;for(F=Z.lowerBound(D);!F.equals(Z.end())&&G.length<Q;F=F.next())G.push(Yo2(F.pointer[1]));B(null,{server:G,end:F.equals(Z.end())})}function jH6(A,B){let Q=parseInt(A.request.subchannel_id,10),D=qP.subchannel.getElementByKey(Q);if(D===void 0){B({code:m31.Status.NOT_FOUND,details:"No subchannel data found for id "+Q});return}let Z=D.getInfo(),G=[];Z.children.sockets.forEach((I)=>{G.push(qP1(I[1].ref))});let F={ref:xJ0(D.ref),data:{target:Z.target,state:Fo2(Z.state),calls_started:Z.callTracker.callsStarted,calls_succeeded:Z.callTracker.callsSucceeded,calls_failed:Z.callTracker.callsFailed,last_call_started_timestamp:WM(Z.callTracker.lastCallStartedTimestamp),trace:Z.trace.getTraceMessage()},socket_ref:G};B(null,{subchannel:F})}function Bo2(A){var B;if(UH6.isTcpSubchannelAddress(A))return{address:"tcpip_address",tcpip_address:{ip_address:(B=RH6(A.host))!==null&&B!==void 0?B:void 0,port:A.port}};else return{address:"uds_address",uds_address:{filename:A.path}}}function yH6(A,B){var Q,D,Z,G,F;let I=parseInt(A.request.socket_id,10),Y=qP.socket.getElementByKey(I);if(Y===void 0){B({code:m31.Status.NOT_FOUND,details:"No socket data found for id "+I});return}let W=Y.getInfo(),J=W.security?{model:"tls",tls:{cipher_suite:W.security.cipherSuiteStandardName?"standard_name":"other_name",standard_name:(Q=W.security.cipherSuiteStandardName)!==null&&Q!==void 0?Q:void 0,other_name:(D=W.security.cipherSuiteOtherName)!==null&&D!==void 0?D:void 0,local_certificate:(Z=W.security.localCertificate)!==null&&Z!==void 0?Z:void 0,remote_certificate:(G=W.security.remoteCertificate)!==null&&G!==void 0?G:void 0}}:null,X={ref:qP1(Y.ref),local:W.localAddress?Bo2(W.localAddress):null,remote:W.remoteAddress?Bo2(W.remoteAddress):null,remote_name:(F=W.remoteName)!==null&&F!==void 0?F:void 0,security:J,data:{keep_alives_sent:W.keepAlivesSent,streams_started:W.streamsStarted,streams_succeeded:W.streamsSucceeded,streams_failed:W.streamsFailed,last_local_stream_created_timestamp:WM(W.lastLocalStreamCreatedTimestamp),last_remote_stream_created_timestamp:WM(W.lastRemoteStreamCreatedTimestamp),messages_received:W.messagesReceived,messages_sent:W.messagesSent,last_message_received_timestamp:WM(W.lastMessageReceivedTimestamp),last_message_sent_timestamp:WM(W.lastMessageSentTimestamp),local_flow_control_window:W.localFlowControlWindow?{value:W.localFlowControlWindow}:null,remote_flow_control_window:W.remoteFlowControlWindow?{value:W.remoteFlowControlWindow}:null}};B(null,{socket:X})}function kH6(A,B){let Q=parseInt(A.request.server_id,10),D=qP.server.getElementByKey(Q);if(D===void 0){B({code:m31.Status.NOT_FOUND,details:"No server data found for id "+Q});return}let Z=parseInt(A.request.start_socket_id,10),G=parseInt(A.request.max_results,10)||vJ0,I=D.getInfo().sessionChildren.sockets,Y=[],W;for(W=I.lowerBound(Z);!W.equals(I.end())&&Y.length<G;W=W.next())Y.push(qP1(W.pointer[1].ref));B(null,{socket_ref:Y,end:W.equals(I.end())})}function Wo2(){return{GetChannel:OH6,GetTopChannels:TH6,GetServer:PH6,GetServers:SH6,GetSubchannel:jH6,GetSocket:yH6,GetServerSockets:kH6}}var wP1=null;function Jo2(){if(wP1)return wP1;let A=or2().loadSync,B=A("channelz.proto",{keepCase:!0,longs:String,enums:String,defaults:!0,oneofs:!0,includeDirs:[`${__dirname}/../../proto`]});return wP1=$H6.loadPackageDefinition(B).grpc.channelz.v1.Channelz.service,wP1}function _H6(){wH6.registerAdminService(Jo2,Wo2)}});
var q31=E((sn2)=>{Object.defineProperty(sn2,"__esModule",{value:!0});sn2.BackoffTimeout=void 0;var NX6=_6(),LX6=F7(),MX6="backoff",RX6=1000,OX6=1.6,TX6=120000,PX6=0.2;function SX6(A,B){return Math.random()*(B-A)+A}class lT1{constructor(A,B){if(this.callback=A,this.initialDelay=RX6,this.multiplier=OX6,this.maxDelay=TX6,this.jitter=PX6,this.running=!1,this.hasRef=!0,this.startTime=new Date,this.endTime=new Date,this.id=lT1.getNextId(),B){if(B.initialDelay)this.initialDelay=B.initialDelay;if(B.multiplier)this.multiplier=B.multiplier;if(B.jitter)this.jitter=B.jitter;if(B.maxDelay)this.maxDelay=B.maxDelay}this.trace("constructed initialDelay="+this.initialDelay+" multiplier="+this.multiplier+" jitter="+this.jitter+" maxDelay="+this.maxDelay),this.nextDelay=this.initialDelay,this.timerId=setTimeout(()=>{},0),clearTimeout(this.timerId)}static getNextId(){return this.nextId++}trace(A){LX6.trace(NX6.LogVerbosity.DEBUG,MX6,"{"+this.id+"} "+A)}runTimer(A){var B,Q;if(this.trace("runTimer(delay="+A+")"),this.endTime=this.startTime,this.endTime.setMilliseconds(this.endTime.getMilliseconds()+A),clearTimeout(this.timerId),this.timerId=setTimeout(()=>{this.trace("timer fired"),this.running=!1,this.callback()},A),!this.hasRef)(Q=(B=this.timerId).unref)===null||Q===void 0||Q.call(B)}runOnce(){this.trace("runOnce()"),this.running=!0,this.startTime=new Date,this.runTimer(this.nextDelay);let A=Math.min(this.nextDelay*this.multiplier,this.maxDelay),B=A*this.jitter;this.nextDelay=A+SX6(-B,B)}stop(){this.trace("stop()"),clearTimeout(this.timerId),this.running=!1}reset(){if(this.trace("reset() running="+this.running),this.nextDelay=this.initialDelay,this.running){let A=new Date,B=this.startTime;if(B.setMilliseconds(B.getMilliseconds()+this.nextDelay),clearTimeout(this.timerId),A<B)this.runTimer(B.getTime()-A.getTime());else this.running=!1}}isRunning(){return this.running}ref(){var A,B;this.hasRef=!0,(B=(A=this.timerId).ref)===null||B===void 0||B.call(A)}unref(){var A,B;this.hasRef=!1,(B=(A=this.timerId).unref)===null||B===void 0||B.call(A)}getEndTime(){return this.endTime}}sn2.BackoffTimeout=lT1;lT1.nextId=0});
var qs2=E((Du5,$s2)=>{var vV6=1/0,bV6="[object Symbol]",fV6=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,hV6=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,rT1="\\ud800-\\udfff",Zs2="\\u0300-\\u036f\\ufe20-\\ufe23",Gs2="\\u20d0-\\u20f0",Fs2="\\u2700-\\u27bf",Is2="a-z\\xdf-\\xf6\\xf8-\\xff",gV6="\\xac\\xb1\\xd7\\xf7",uV6="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",mV6="\\u2000-\\u206f",dV6=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",Ys2="A-Z\\xc0-\\xd6\\xd8-\\xde",Ws2="\\ufe0e\\ufe0f",Js2=gV6+uV6+mV6+dV6,nW0="['’]",cV6="["+rT1+"]",oa2="["+Js2+"]",sT1="["+Zs2+Gs2+"]",Xs2="\\d+",lV6="["+Fs2+"]",Vs2="["+Is2+"]",Cs2="[^"+rT1+Js2+Xs2+Fs2+Is2+Ys2+"]",iW0="\\ud83c[\\udffb-\\udfff]",pV6="(?:"+sT1+"|"+iW0+")",Ks2="[^"+rT1+"]",aW0="(?:\\ud83c[\\udde6-\\uddff]){2}",sW0="[\\ud800-\\udbff][\\udc00-\\udfff]",to="["+Ys2+"]",Hs2="\\u200d",ta2="(?:"+Vs2+"|"+Cs2+")",iV6="(?:"+to+"|"+Cs2+")",ea2="(?:"+nW0+"(?:d|ll|m|re|s|t|ve))?",As2="(?:"+nW0+"(?:D|LL|M|RE|S|T|VE))?",zs2=pV6+"?",Es2="["+Ws2+"]?",nV6="(?:"+Hs2+"(?:"+[Ks2,aW0,sW0].join("|")+")"+Es2+zs2+")*",Us2=Es2+zs2+nV6,aV6="(?:"+[lV6,aW0,sW0].join("|")+")"+Us2,sV6="(?:"+[Ks2+sT1+"?",sT1,aW0,sW0,cV6].join("|")+")",rV6=RegExp(nW0,"g"),oV6=RegExp(sT1,"g"),tV6=RegExp(iW0+"(?="+iW0+")|"+sV6+Us2,"g"),eV6=RegExp([to+"?"+Vs2+"+"+ea2+"(?="+[oa2,to,"$"].join("|")+")",iV6+"+"+As2+"(?="+[oa2,to+ta2,"$"].join("|")+")",to+"?"+ta2+"+"+ea2,to+"+"+As2,Xs2,aV6].join("|"),"g"),AC6=RegExp("["+Hs2+rT1+Zs2+Gs2+Ws2+"]"),BC6=/[a-z][A-Z]|[A-Z]{2,}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,QC6={"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"ss"},DC6=typeof global=="object"&&global&&global.Object===Object&&global,ZC6=typeof self=="object"&&self&&self.Object===Object&&self,GC6=DC6||ZC6||Function("return this")();function FC6(A,B,Q,D){var Z=-1,G=A?A.length:0;if(D&&G)Q=A[++Z];while(++Z<G)Q=B(Q,A[Z],Z,A);return Q}function IC6(A){return A.split("")}function YC6(A){return A.match(fV6)||[]}function WC6(A){return function(B){return A==null?void 0:A[B]}}var JC6=WC6(QC6);function ws2(A){return AC6.test(A)}function XC6(A){return BC6.test(A)}function VC6(A){return ws2(A)?CC6(A):IC6(A)}function CC6(A){return A.match(tV6)||[]}function KC6(A){return A.match(eV6)||[]}var HC6=Object.prototype,zC6=HC6.toString,Bs2=GC6.Symbol,Qs2=Bs2?Bs2.prototype:void 0,Ds2=Qs2?Qs2.toString:void 0;function EC6(A,B,Q){var D=-1,Z=A.length;if(B<0)B=-B>Z?0:Z+B;if(Q=Q>Z?Z:Q,Q<0)Q+=Z;Z=B>Q?0:Q-B>>>0,B>>>=0;var G=Array(Z);while(++D<Z)G[D]=A[D+B];return G}function UC6(A){if(typeof A=="string")return A;if(LC6(A))return Ds2?Ds2.call(A):"";var B=A+"";return B=="0"&&1/A==-vV6?"-0":B}function wC6(A,B,Q){var D=A.length;return Q=Q===void 0?D:Q,!B&&Q>=D?A:EC6(A,B,Q)}function $C6(A){return function(B){B=oT1(B);var Q=ws2(B)?VC6(B):void 0,D=Q?Q[0]:B.charAt(0),Z=Q?wC6(Q,1).join(""):B.slice(1);return D[A]()+Z}}function qC6(A){return function(B){return FC6(PC6(OC6(B).replace(rV6,"")),A,"")}}function NC6(A){return!!A&&typeof A=="object"}function LC6(A){return typeof A=="symbol"||NC6(A)&&zC6.call(A)==bV6}function oT1(A){return A==null?"":UC6(A)}var MC6=qC6(function(A,B,Q){return B=B.toLowerCase(),A+(Q?RC6(B):B)});function RC6(A){return TC6(oT1(A).toLowerCase())}function OC6(A){return A=oT1(A),A&&A.replace(hV6,JC6).replace(oV6,"")}var TC6=$C6("toUpperCase");function PC6(A,B,Q){if(A=oT1(A),B=Q?void 0:B,B===void 0)return XC6(A)?KC6(A):YC6(A);return A.match(B)||[]}$s2.exports=MC6});
var qt2=E((wt2)=>{Object.defineProperty(wt2,"__esModule",{value:!0});wt2.ResolvingCall=void 0;var LE6=bT1(),iu=_6(),nu=i31(),Et2=GJ(),ME6=F7(),RE6=jP1(),OE6="resolving_call";class Ut2{constructor(A,B,Q,D,Z){if(this.channel=A,this.method=B,this.filterStackFactory=D,this.callNumber=Z,this.child=null,this.readPending=!1,this.pendingMessage=null,this.pendingHalfClose=!1,this.ended=!1,this.readFilterPending=!1,this.writeFilterPending=!1,this.pendingChildStatus=null,this.metadata=null,this.listener=null,this.statusWatchers=[],this.deadlineTimer=setTimeout(()=>{},0),this.filterStack=null,this.deadlineStartTime=null,this.configReceivedTime=null,this.childStartTime=null,this.credentials=LE6.CallCredentials.createEmpty(),this.deadline=Q.deadline,this.host=Q.host,Q.parentCall){if(Q.flags&iu.Propagate.CANCELLATION)Q.parentCall.on("cancelled",()=>{this.cancelWithStatus(iu.Status.CANCELLED,"Cancelled by parent call")});if(Q.flags&iu.Propagate.DEADLINE)this.trace("Propagating deadline from parent: "+Q.parentCall.getDeadline()),this.deadline=nu.minDeadline(this.deadline,Q.parentCall.getDeadline())}this.trace("Created"),this.runDeadlineTimer()}trace(A){ME6.trace(iu.LogVerbosity.DEBUG,OE6,"["+this.callNumber+"] "+A)}runDeadlineTimer(){clearTimeout(this.deadlineTimer),this.deadlineStartTime=new Date,this.trace("Deadline: "+nu.deadlineToString(this.deadline));let A=nu.getRelativeTimeout(this.deadline);if(A!==1/0){this.trace("Deadline will be reached in "+A+"ms");let B=()=>{if(!this.deadlineStartTime){this.cancelWithStatus(iu.Status.DEADLINE_EXCEEDED,"Deadline exceeded");return}let Q=[],D=new Date;if(Q.push(`Deadline exceeded after ${nu.formatDateDifference(this.deadlineStartTime,D)}`),this.configReceivedTime){if(this.configReceivedTime>this.deadlineStartTime)Q.push(`name resolution: ${nu.formatDateDifference(this.deadlineStartTime,this.configReceivedTime)}`);if(this.childStartTime){if(this.childStartTime>this.configReceivedTime)Q.push(`metadata filters: ${nu.formatDateDifference(this.configReceivedTime,this.childStartTime)}`)}else Q.push("waiting for metadata filters")}else Q.push("waiting for name resolution");if(this.child)Q.push(...this.child.getDeadlineInfo());this.cancelWithStatus(iu.Status.DEADLINE_EXCEEDED,Q.join(","))};if(A<=0)process.nextTick(B);else this.deadlineTimer=setTimeout(B,A)}}outputStatus(A){if(!this.ended){if(this.ended=!0,!this.filterStack)this.filterStack=this.filterStackFactory.createFilter();clearTimeout(this.deadlineTimer);let B=this.filterStack.receiveTrailers(A);this.trace("ended with status: code="+B.code+' details="'+B.details+'"'),this.statusWatchers.forEach((Q)=>Q(B)),process.nextTick(()=>{var Q;(Q=this.listener)===null||Q===void 0||Q.onReceiveStatus(B)})}}sendMessageOnChild(A,B){if(!this.child)throw new Error("sendMessageonChild called with child not populated");let Q=this.child;this.writeFilterPending=!0,this.filterStack.sendMessage(Promise.resolve({message:B,flags:A.flags})).then((D)=>{if(this.writeFilterPending=!1,Q.sendMessageWithContext(A,D.message),this.pendingHalfClose)Q.halfClose()},(D)=>{this.cancelWithStatus(D.code,D.details)})}getConfig(){if(this.ended)return;if(!this.metadata||!this.listener)throw new Error("getConfig called before start");let A=this.channel.getConfig(this.method,this.metadata);if(A.type==="NONE"){this.channel.queueCallForConfig(this);return}else if(A.type==="ERROR"){if(this.metadata.getOptions().waitForReady)this.channel.queueCallForConfig(this);else this.outputStatus(A.error);return}this.configReceivedTime=new Date;let B=A.config;if(B.status!==iu.Status.OK){let{code:Q,details:D}=RE6.restrictControlPlaneStatusCode(B.status,"Failed to route call to method "+this.method);this.outputStatus({code:Q,details:D,metadata:new Et2.Metadata});return}if(B.methodConfig.timeout){let Q=new Date;Q.setSeconds(Q.getSeconds()+B.methodConfig.timeout.seconds),Q.setMilliseconds(Q.getMilliseconds()+B.methodConfig.timeout.nanos/1e6),this.deadline=nu.minDeadline(this.deadline,Q),this.runDeadlineTimer()}this.filterStackFactory.push(B.dynamicFilterFactories),this.filterStack=this.filterStackFactory.createFilter(),this.filterStack.sendMetadata(Promise.resolve(this.metadata)).then((Q)=>{if(this.child=this.channel.createRetryingCall(B,this.method,this.host,this.credentials,this.deadline),this.trace("Created child ["+this.child.getCallNumber()+"]"),this.childStartTime=new Date,this.child.start(Q,{onReceiveMetadata:(D)=>{this.trace("Received metadata"),this.listener.onReceiveMetadata(this.filterStack.receiveMetadata(D))},onReceiveMessage:(D)=>{this.trace("Received message"),this.readFilterPending=!0,this.filterStack.receiveMessage(D).then((Z)=>{if(this.trace("Finished filtering received message"),this.readFilterPending=!1,this.listener.onReceiveMessage(Z),this.pendingChildStatus)this.outputStatus(this.pendingChildStatus)},(Z)=>{this.cancelWithStatus(Z.code,Z.details)})},onReceiveStatus:(D)=>{if(this.trace("Received status"),this.readFilterPending)this.pendingChildStatus=D;else this.outputStatus(D)}}),this.readPending)this.child.startRead();if(this.pendingMessage)this.sendMessageOnChild(this.pendingMessage.context,this.pendingMessage.message);else if(this.pendingHalfClose)this.child.halfClose()},(Q)=>{this.outputStatus(Q)})}reportResolverError(A){var B;if((B=this.metadata)===null||B===void 0?void 0:B.getOptions().waitForReady)this.channel.queueCallForConfig(this);else this.outputStatus(A)}cancelWithStatus(A,B){var Q;this.trace("cancelWithStatus code: "+A+' details: "'+B+'"'),(Q=this.child)===null||Q===void 0||Q.cancelWithStatus(A,B),this.outputStatus({code:A,details:B,metadata:new Et2.Metadata})}getPeer(){var A,B;return(B=(A=this.child)===null||A===void 0?void 0:A.getPeer())!==null&&B!==void 0?B:this.channel.getTarget()}start(A,B){this.trace("start called"),this.metadata=A.clone(),this.listener=B,this.getConfig()}sendMessageWithContext(A,B){if(this.trace("write() called with message of length "+B.length),this.child)this.sendMessageOnChild(A,B);else this.pendingMessage={context:A,message:B}}startRead(){if(this.trace("startRead called"),this.child)this.child.startRead();else this.readPending=!0}halfClose(){if(this.trace("halfClose called"),this.child&&!this.writeFilterPending)this.child.halfClose();else this.pendingHalfClose=!0}setCredentials(A){this.credentials=A}addStatusWatcher(A){this.statusWatchers.push(A)}getCallNumber(){return this.callNumber}}wt2.ResolvingCall=Ut2});
var r1B=E((sX0)=>{Object.defineProperty(sX0,"__esModule",{value:!0});sX0.OTLPMetricExporter=void 0;var Iq6=s1B();Object.defineProperty(sX0,"OTLPMetricExporter",{enumerable:!0,get:function(){return Iq6.OTLPMetricExporter}})});
var re2=E((se2)=>{Object.defineProperty(se2,"__esModule",{value:!0});se2.setup=qw6;var pe2=J1("net"),pP1=_6(),SX0=GJ(),ie2=BM(),ne2=hV(),Uw6=F7(),ww6="ip_resolver";function ae2(A){Uw6.trace(pP1.LogVerbosity.DEBUG,ww6,A)}var jX0="ipv4",yX0="ipv6",$w6=443;class kX0{constructor(A,B,Q){var D;this.listener=B,this.endpoints=[],this.error=null,this.hasReturnedResult=!1,ae2("Resolver constructed for target "+ne2.uriToString(A));let Z=[];if(!(A.scheme===jX0||A.scheme===yX0)){this.error={code:pP1.Status.UNAVAILABLE,details:`Unrecognized scheme ${A.scheme} in IP resolver`,metadata:new SX0.Metadata};return}let G=A.path.split(",");for(let F of G){let I=ne2.splitHostPort(F);if(I===null){this.error={code:pP1.Status.UNAVAILABLE,details:`Failed to parse ${A.scheme} address ${F}`,metadata:new SX0.Metadata};return}if(A.scheme===jX0&&!pe2.isIPv4(I.host)||A.scheme===yX0&&!pe2.isIPv6(I.host)){this.error={code:pP1.Status.UNAVAILABLE,details:`Failed to parse ${A.scheme} address ${F}`,metadata:new SX0.Metadata};return}Z.push({host:I.host,port:(D=I.port)!==null&&D!==void 0?D:$w6})}this.endpoints=Z.map((F)=>({addresses:[F]})),ae2("Parsed "+A.scheme+" address list "+Z)}updateResolution(){if(!this.hasReturnedResult)this.hasReturnedResult=!0,process.nextTick(()=>{if(this.error)this.listener.onError(this.error);else this.listener.onSuccessfulResolution(this.endpoints,null,null,null,{})})}destroy(){this.hasReturnedResult=!1}static getDefaultAuthority(A){return A.path.split(",")[0]}}function qw6(){ie2.registerResolver(jX0,kX0),ie2.registerResolver(yX0,kX0)}});
var s1B=E((n1B)=>{Object.defineProperty(n1B,"__esModule",{value:!0});n1B.OTLPMetricExporter=void 0;var Gq6=yT1(),p1B=aX0(),Fq6=fu();class i1B extends Gq6.OTLPMetricExporterBase{constructor(A){super(p1B.createOtlpGrpcExportDelegate(p1B.convertLegacyOtlpGrpcOptions(A??{},"METRICS"),Fq6.ProtobufMetricsSerializer,"MetricsExportService","/opentelemetry.proto.collector.metrics.v1.MetricsService/Export"),A)}}n1B.OTLPMetricExporter=i1B});
var s_=E((nn2)=>{Object.defineProperty(nn2,"__esModule",{value:!0});nn2.QueuePicker=nn2.UnavailablePicker=nn2.PickResultType=void 0;var UX6=GJ(),wX6=_6(),cT1;(function(A){A[A.COMPLETE=0]="COMPLETE",A[A.QUEUE=1]="QUEUE",A[A.TRANSIENT_FAILURE=2]="TRANSIENT_FAILURE",A[A.DROP=3]="DROP"})(cT1||(nn2.PickResultType=cT1={}));class pn2{constructor(A){this.status=Object.assign({code:wX6.Status.UNAVAILABLE,details:"No connection established",metadata:new UX6.Metadata},A)}pick(A){return{pickResultType:cT1.TRANSIENT_FAILURE,subchannel:null,status:this.status,onCallStarted:null,onCallEnded:null}}}nn2.UnavailablePicker=pn2;class in2{constructor(A,B){this.loadBalancer=A,this.childPicker=B,this.calledExitIdle=!1}pick(A){if(!this.calledExitIdle)process.nextTick(()=>{this.loadBalancer.exitIdle()}),this.calledExitIdle=!0;if(this.childPicker)return this.childPicker.pick(A);else return{pickResultType:cT1.QUEUE,subchannel:null,status:null,onCallStarted:null,onCallEnded:null}}}nn2.QueuePicker=in2});
var st2=E((nt2)=>{Object.defineProperty(nt2,"__esModule",{value:!0});nt2.ServerDuplexStreamImpl=nt2.ServerWritableStreamImpl=nt2.ServerReadableStreamImpl=nt2.ServerUnaryCallImpl=void 0;nt2.serverErrorToStatus=JX0;var eE6=J1("events"),YX0=J1("stream"),WX0=_6(),dt2=GJ();function JX0(A,B){var Q;let D={code:WX0.Status.UNKNOWN,details:"message"in A?A.message:"Unknown Error",metadata:(Q=B!==null&&B!==void 0?B:A.metadata)!==null&&Q!==void 0?Q:null};if("code"in A&&typeof A.code==="number"&&Number.isInteger(A.code)){if(D.code=A.code,"details"in A&&typeof A.details==="string")D.details=A.details}return D}class ct2 extends eE6.EventEmitter{constructor(A,B,Q,D){super();this.path=A,this.call=B,this.metadata=Q,this.request=D,this.cancelled=!1}getPeer(){return this.call.getPeer()}sendMetadata(A){this.call.sendMetadata(A)}getDeadline(){return this.call.getDeadline()}getPath(){return this.path}getHost(){return this.call.getHost()}}nt2.ServerUnaryCallImpl=ct2;class lt2 extends YX0.Readable{constructor(A,B,Q){super({objectMode:!0});this.path=A,this.call=B,this.metadata=Q,this.cancelled=!1}_read(A){this.call.startRead()}getPeer(){return this.call.getPeer()}sendMetadata(A){this.call.sendMetadata(A)}getDeadline(){return this.call.getDeadline()}getPath(){return this.path}getHost(){return this.call.getHost()}}nt2.ServerReadableStreamImpl=lt2;class pt2 extends YX0.Writable{constructor(A,B,Q,D){super({objectMode:!0});this.path=A,this.call=B,this.metadata=Q,this.request=D,this.pendingStatus={code:WX0.Status.OK,details:"OK"},this.cancelled=!1,this.trailingMetadata=new dt2.Metadata,this.on("error",(Z)=>{this.pendingStatus=JX0(Z),this.end()})}getPeer(){return this.call.getPeer()}sendMetadata(A){this.call.sendMetadata(A)}getDeadline(){return this.call.getDeadline()}getPath(){return this.path}getHost(){return this.call.getHost()}_write(A,B,Q){this.call.sendMessage(A,Q)}_final(A){var B;A(null),this.call.sendStatus(Object.assign(Object.assign({},this.pendingStatus),{metadata:(B=this.pendingStatus.metadata)!==null&&B!==void 0?B:this.trailingMetadata}))}end(A){if(A)this.trailingMetadata=A;return super.end()}}nt2.ServerWritableStreamImpl=pt2;class it2 extends YX0.Duplex{constructor(A,B,Q){super({objectMode:!0});this.path=A,this.call=B,this.metadata=Q,this.pendingStatus={code:WX0.Status.OK,details:"OK"},this.cancelled=!1,this.trailingMetadata=new dt2.Metadata,this.on("error",(D)=>{this.pendingStatus=JX0(D),this.end()})}getPeer(){return this.call.getPeer()}sendMetadata(A){this.call.sendMetadata(A)}getDeadline(){return this.call.getDeadline()}getPath(){return this.path}getHost(){return this.call.getHost()}_read(A){this.call.startRead()}_write(A,B,Q){this.call.sendMessage(A,Q)}_final(A){var B;A(null),this.call.sendStatus(Object.assign(Object.assign({},this.pendingStatus),{metadata:(B=this.pendingStatus.metadata)!==null&&B!==void 0?B:this.trailingMetadata}))}end(A){if(A)this.trailingMetadata=A;return super.end()}}nt2.ServerDuplexStreamImpl=it2});
var tJ0=E((io2)=>{Object.defineProperty(io2,"__esModule",{value:!0});io2.FilterStackFactory=io2.FilterStack=void 0;class rJ0{constructor(A){this.filters=A}sendMetadata(A){let B=A;for(let Q=0;Q<this.filters.length;Q++)B=this.filters[Q].sendMetadata(B);return B}receiveMetadata(A){let B=A;for(let Q=this.filters.length-1;Q>=0;Q--)B=this.filters[Q].receiveMetadata(B);return B}sendMessage(A){let B=A;for(let Q=0;Q<this.filters.length;Q++)B=this.filters[Q].sendMessage(B);return B}receiveMessage(A){let B=A;for(let Q=this.filters.length-1;Q>=0;Q--)B=this.filters[Q].receiveMessage(B);return B}receiveTrailers(A){let B=A;for(let Q=this.filters.length-1;Q>=0;Q--)B=this.filters[Q].receiveTrailers(B);return B}push(A){this.filters.unshift(...A)}getFilters(){return this.filters}}io2.FilterStack=rJ0;class oJ0{constructor(A){this.factories=A}push(A){this.factories.unshift(...A)}clone(){return new oJ0([...this.factories])}createFilter(){return new rJ0(this.factories.map((A)=>A.createFilter()))}}io2.FilterStackFactory=oJ0});
var uW0=E((la2)=>{Object.defineProperty(la2,"__esModule",{value:!0});la2.InterceptingCall=la2.RequesterBuilder=la2.ListenerBuilder=la2.InterceptorConfigurationError=void 0;la2.getInterceptingCall=NV6;var wV6=GJ(),ba2=va2(),fa2=_6(),ha2=kT1();class T31 extends Error{constructor(A){super(A);this.name="InterceptorConfigurationError",Error.captureStackTrace(this,T31)}}la2.InterceptorConfigurationError=T31;class ga2{constructor(){this.metadata=void 0,this.message=void 0,this.status=void 0}withOnReceiveMetadata(A){return this.metadata=A,this}withOnReceiveMessage(A){return this.message=A,this}withOnReceiveStatus(A){return this.status=A,this}build(){return{onReceiveMetadata:this.metadata,onReceiveMessage:this.message,onReceiveStatus:this.status}}}la2.ListenerBuilder=ga2;class ua2{constructor(){this.start=void 0,this.message=void 0,this.halfClose=void 0,this.cancel=void 0}withStart(A){return this.start=A,this}withSendMessage(A){return this.message=A,this}withHalfClose(A){return this.halfClose=A,this}withCancel(A){return this.cancel=A,this}build(){return{start:this.start,sendMessage:this.message,halfClose:this.halfClose,cancel:this.cancel}}}la2.RequesterBuilder=ua2;var hW0={onReceiveMetadata:(A,B)=>{B(A)},onReceiveMessage:(A,B)=>{B(A)},onReceiveStatus:(A,B)=>{B(A)}},O31={start:(A,B,Q)=>{Q(A,B)},sendMessage:(A,B)=>{B(A)},halfClose:(A)=>{A()},cancel:(A)=>{A()}};class ma2{constructor(A,B){var Q,D,Z,G;if(this.nextCall=A,this.processingMetadata=!1,this.pendingMessageContext=null,this.processingMessage=!1,this.pendingHalfClose=!1,B)this.requester={start:(Q=B.start)!==null&&Q!==void 0?Q:O31.start,sendMessage:(D=B.sendMessage)!==null&&D!==void 0?D:O31.sendMessage,halfClose:(Z=B.halfClose)!==null&&Z!==void 0?Z:O31.halfClose,cancel:(G=B.cancel)!==null&&G!==void 0?G:O31.cancel};else this.requester=O31}cancelWithStatus(A,B){this.requester.cancel(()=>{this.nextCall.cancelWithStatus(A,B)})}getPeer(){return this.nextCall.getPeer()}processPendingMessage(){if(this.pendingMessageContext)this.nextCall.sendMessageWithContext(this.pendingMessageContext,this.pendingMessage),this.pendingMessageContext=null,this.pendingMessage=null}processPendingHalfClose(){if(this.pendingHalfClose)this.nextCall.halfClose()}start(A,B){var Q,D,Z,G,F,I;let Y={onReceiveMetadata:(D=(Q=B===null||B===void 0?void 0:B.onReceiveMetadata)===null||Q===void 0?void 0:Q.bind(B))!==null&&D!==void 0?D:(W)=>{},onReceiveMessage:(G=(Z=B===null||B===void 0?void 0:B.onReceiveMessage)===null||Z===void 0?void 0:Z.bind(B))!==null&&G!==void 0?G:(W)=>{},onReceiveStatus:(I=(F=B===null||B===void 0?void 0:B.onReceiveStatus)===null||F===void 0?void 0:F.bind(B))!==null&&I!==void 0?I:(W)=>{}};this.processingMetadata=!0,this.requester.start(A,Y,(W,J)=>{var X,V,C;this.processingMetadata=!1;let K;if(ba2.isInterceptingListener(J))K=J;else{let H={onReceiveMetadata:(X=J.onReceiveMetadata)!==null&&X!==void 0?X:hW0.onReceiveMetadata,onReceiveMessage:(V=J.onReceiveMessage)!==null&&V!==void 0?V:hW0.onReceiveMessage,onReceiveStatus:(C=J.onReceiveStatus)!==null&&C!==void 0?C:hW0.onReceiveStatus};K=new ba2.InterceptingListenerImpl(H,Y)}this.nextCall.start(W,K),this.processPendingMessage(),this.processPendingHalfClose()})}sendMessageWithContext(A,B){this.processingMessage=!0,this.requester.sendMessage(B,(Q)=>{if(this.processingMessage=!1,this.processingMetadata)this.pendingMessageContext=A,this.pendingMessage=B;else this.nextCall.sendMessageWithContext(A,Q),this.processPendingHalfClose()})}sendMessage(A){this.sendMessageWithContext({},A)}startRead(){this.nextCall.startRead()}halfClose(){this.requester.halfClose(()=>{if(this.processingMetadata||this.processingMessage)this.pendingHalfClose=!0;else this.nextCall.halfClose()})}}la2.InterceptingCall=ma2;function $V6(A,B,Q){var D,Z;let G=(D=Q.deadline)!==null&&D!==void 0?D:1/0,F=Q.host,I=(Z=Q.parent)!==null&&Z!==void 0?Z:null,Y=Q.propagate_flags,W=Q.credentials,J=A.createCall(B,G,F,I,Y);if(W)J.setCredentials(W);return J}class gW0{constructor(A,B){this.call=A,this.methodDefinition=B}cancelWithStatus(A,B){this.call.cancelWithStatus(A,B)}getPeer(){return this.call.getPeer()}sendMessageWithContext(A,B){let Q;try{Q=this.methodDefinition.requestSerialize(B)}catch(D){this.call.cancelWithStatus(fa2.Status.INTERNAL,`Request message serialization failure: ${ha2.getErrorMessage(D)}`);return}this.call.sendMessageWithContext(A,Q)}sendMessage(A){this.sendMessageWithContext({},A)}start(A,B){let Q=null;this.call.start(A,{onReceiveMetadata:(D)=>{var Z;(Z=B===null||B===void 0?void 0:B.onReceiveMetadata)===null||Z===void 0||Z.call(B,D)},onReceiveMessage:(D)=>{var Z;let G;try{G=this.methodDefinition.responseDeserialize(D)}catch(F){Q={code:fa2.Status.INTERNAL,details:`Response message parsing error: ${ha2.getErrorMessage(F)}`,metadata:new wV6.Metadata},this.call.cancelWithStatus(Q.code,Q.details);return}(Z=B===null||B===void 0?void 0:B.onReceiveMessage)===null||Z===void 0||Z.call(B,G)},onReceiveStatus:(D)=>{var Z,G;if(Q)(Z=B===null||B===void 0?void 0:B.onReceiveStatus)===null||Z===void 0||Z.call(B,Q);else(G=B===null||B===void 0?void 0:B.onReceiveStatus)===null||G===void 0||G.call(B,D)}})}startRead(){this.call.startRead()}halfClose(){this.call.halfClose()}}class da2 extends gW0{constructor(A,B){super(A,B)}start(A,B){var Q,D;let Z=!1,G={onReceiveMetadata:(D=(Q=B===null||B===void 0?void 0:B.onReceiveMetadata)===null||Q===void 0?void 0:Q.bind(B))!==null&&D!==void 0?D:(F)=>{},onReceiveMessage:(F)=>{var I;Z=!0,(I=B===null||B===void 0?void 0:B.onReceiveMessage)===null||I===void 0||I.call(B,F)},onReceiveStatus:(F)=>{var I,Y;if(!Z)(I=B===null||B===void 0?void 0:B.onReceiveMessage)===null||I===void 0||I.call(B,null);(Y=B===null||B===void 0?void 0:B.onReceiveStatus)===null||Y===void 0||Y.call(B,F)}};super.start(A,G),this.call.startRead()}}class ca2 extends gW0{}function qV6(A,B,Q){let D=$V6(A,Q.path,B);if(Q.responseStream)return new ca2(D,Q);else return new da2(D,Q)}function NV6(A,B,Q,D){if(A.clientInterceptors.length>0&&A.clientInterceptorProviders.length>0)throw new T31("Both interceptors and interceptor_providers were passed as options to the client constructor. Only one of these is allowed.");if(A.callInterceptors.length>0&&A.callInterceptorProviders.length>0)throw new T31("Both interceptors and interceptor_providers were passed as call options. Only one of these is allowed.");let Z=[];if(A.callInterceptors.length>0||A.callInterceptorProviders.length>0)Z=[].concat(A.callInterceptors,A.callInterceptorProviders.map((I)=>I(B))).filter((I)=>I);else Z=[].concat(A.clientInterceptors,A.clientInterceptorProviders.map((I)=>I(B))).filter((I)=>I);let G=Object.assign({},Q,{method_definition:B});return Z.reduceRight((I,Y)=>{return(W)=>Y(W,I)},(I)=>qV6(D,I,B))(G)}});
var ur2=E((hr2)=>{Object.defineProperty(hr2,"__esModule",{value:!0});hr2.addCommonProtos=hr2.loadProtosWithOptionsSync=hr2.loadProtosWithOptions=void 0;var vr2=J1("fs"),br2=J1("path"),Ft=HP1();function fr2(A,B){let Q=A.resolvePath;A.resolvePath=(D,Z)=>{if(br2.isAbsolute(Z))return Z;for(let G of B){let F=br2.join(G,Z);try{return vr2.accessSync(F,vr2.constants.R_OK),F}catch(I){continue}}return process.emitWarning(`${Z} not found in any of the include paths ${B}`),Q(D,Z)}}async function pK6(A,B){let Q=new Ft.Root;if(B=B||{},B.includeDirs){if(!Array.isArray(B.includeDirs))return Promise.reject(new Error("The includeDirs option must be an array"));fr2(Q,B.includeDirs)}let D=await Q.load(A,B);return D.resolveAll(),D}hr2.loadProtosWithOptions=pK6;function iK6(A,B){let Q=new Ft.Root;if(B=B||{},B.includeDirs){if(!Array.isArray(B.includeDirs))throw new Error("The includeDirs option must be an array");fr2(Q,B.includeDirs)}let D=Q.loadSync(A,B);return D.resolveAll(),D}hr2.loadProtosWithOptionsSync=iK6;function nK6(){let A=kr2(),B=TJ0(),Q=_r2(),D=xr2();Ft.common("api",A.nested.google.nested.protobuf.nested),Ft.common("descriptor",B.nested.google.nested.protobuf.nested),Ft.common("source_context",Q.nested.google.nested.protobuf.nested),Ft.common("type",D.nested.google.nested.protobuf.nested)}hr2.addCommonProtos=nK6});
var va2=E((_a2)=>{Object.defineProperty(_a2,"__esModule",{value:!0});_a2.InterceptingListenerImpl=void 0;_a2.isInterceptingListener=EV6;function EV6(A){return A.onReceiveMetadata!==void 0&&A.onReceiveMetadata.length===1}class ka2{constructor(A,B){this.listener=A,this.nextListener=B,this.processingMetadata=!1,this.hasPendingMessage=!1,this.processingMessage=!1,this.pendingStatus=null}processPendingMessage(){if(this.hasPendingMessage)this.nextListener.onReceiveMessage(this.pendingMessage),this.pendingMessage=null,this.hasPendingMessage=!1}processPendingStatus(){if(this.pendingStatus)this.nextListener.onReceiveStatus(this.pendingStatus)}onReceiveMetadata(A){this.processingMetadata=!0,this.listener.onReceiveMetadata(A,(B)=>{this.processingMetadata=!1,this.nextListener.onReceiveMetadata(B),this.processPendingMessage(),this.processPendingStatus()})}onReceiveMessage(A){this.processingMessage=!0,this.listener.onReceiveMessage(A,(B)=>{if(this.processingMessage=!1,this.processingMetadata)this.pendingMessage=B,this.hasPendingMessage=!0;else this.nextListener.onReceiveMessage(B),this.processPendingStatus()})}onReceiveStatus(A){this.listener.onReceiveStatus(A,(B)=>{if(this.processingMetadata||this.processingMessage)this.pendingStatus=B;else this.nextListener.onReceiveStatus(B)})}}_a2.InterceptingListenerImpl=ka2});
var w31=E((vn2)=>{Object.defineProperty(vn2,"__esModule",{value:!0});vn2.ChannelCredentials=void 0;vn2.createCertificateProviderChannelCredentials=uJ6;var U31=J1("tls"),uT1=bT1(),kW0=PW0(),fT1=hV(),kn2=BM(),hJ6=F7(),gJ6=_6();function yW0(A,B){if(A&&!(A instanceof Buffer))throw new TypeError(`${B}, if provided, must be a Buffer.`)}class no{compose(A){return new gT1(this,A)}static createSsl(A,B,Q,D){var Z;if(yW0(A,"Root certificate"),yW0(B,"Private key"),yW0(Q,"Certificate chain"),B&&!Q)throw new Error("Private key must be given with accompanying certificate chain");if(!B&&Q)throw new Error("Certificate chain must be given with accompanying private key");let G=U31.createSecureContext({ca:(Z=A!==null&&A!==void 0?A:kW0.getDefaultRootsData())!==null&&Z!==void 0?Z:void 0,key:B!==null&&B!==void 0?B:void 0,cert:Q!==null&&Q!==void 0?Q:void 0,ciphers:kW0.CIPHER_SUITES});return new hT1(G,D!==null&&D!==void 0?D:{})}static createFromSecureContext(A,B){return new hT1(A,B!==null&&B!==void 0?B:{})}static createInsecure(){return new _W0}}vn2.ChannelCredentials=no;class _W0 extends no{constructor(){super()}compose(A){throw new Error("Cannot compose insecure credentials")}_isSecure(){return!1}_equals(A){return A instanceof _W0}_createSecureConnector(A,B,Q){return{connect(D){return Promise.resolve({socket:D,secure:!1})},waitForReady:()=>{return Promise.resolve()},getCallCredentials:()=>{return Q!==null&&Q!==void 0?Q:uT1.CallCredentials.createEmpty()},destroy(){}}}}function _n2(A,B,Q,D){var Z,G,F,I;let Y={secureContext:A};if(B.checkServerIdentity)Y.checkServerIdentity=B.checkServerIdentity;if(B.rejectUnauthorized!==void 0)Y.rejectUnauthorized=B.rejectUnauthorized;if(Y.ALPNProtocols=["h2"],D["grpc.ssl_target_name_override"]){let C=D["grpc.ssl_target_name_override"],K=(Z=Y.checkServerIdentity)!==null&&Z!==void 0?Z:U31.checkServerIdentity;Y.checkServerIdentity=(H,z)=>{return K(C,z)},Y.servername=C}else if("grpc.http_connect_target"in D){let C=kn2.getDefaultAuthority((G=fT1.parseUri(D["grpc.http_connect_target"]))!==null&&G!==void 0?G:{path:"localhost"}),K=fT1.splitHostPort(C);Y.servername=(F=K===null||K===void 0?void 0:K.host)!==null&&F!==void 0?F:C}if(D["grpc-node.tls_enable_trace"])Y.enableTrace=!0;let W=Q;if("grpc.http_connect_target"in D){let C=fT1.parseUri(D["grpc.http_connect_target"]);if(C)W=C}let J=kn2.getDefaultAuthority(W),X=fT1.splitHostPort(J),V=(I=X===null||X===void 0?void 0:X.host)!==null&&I!==void 0?I:J;return Y.host=V,Y.servername=V,Y}class xn2{constructor(A,B){this.connectionOptions=A,this.callCredentials=B}connect(A){let B=Object.assign({socket:A},this.connectionOptions);return new Promise((Q,D)=>{let Z=U31.connect(B,()=>{var G;if(((G=this.connectionOptions.rejectUnauthorized)!==null&&G!==void 0?G:!0)&&!Z.authorized){D(Z.authorizationError);return}Q({socket:Z,secure:!0})});Z.on("error",(G)=>{D(G)})})}waitForReady(){return Promise.resolve()}getCallCredentials(){return this.callCredentials}destroy(){}}class hT1 extends no{constructor(A,B){super();this.secureContext=A,this.verifyOptions=B}_isSecure(){return!0}_equals(A){if(this===A)return!0;if(A instanceof hT1)return this.secureContext===A.secureContext&&this.verifyOptions.checkServerIdentity===A.verifyOptions.checkServerIdentity;else return!1}_createSecureConnector(A,B,Q){let D=_n2(this.secureContext,this.verifyOptions,A,B);return new xn2(D,Q!==null&&Q!==void 0?Q:uT1.CallCredentials.createEmpty())}}class E31 extends no{constructor(A,B,Q){super();this.caCertificateProvider=A,this.identityCertificateProvider=B,this.verifyOptions=Q,this.refcount=0,this.latestCaUpdate=void 0,this.latestIdentityUpdate=void 0,this.caCertificateUpdateListener=this.handleCaCertificateUpdate.bind(this),this.identityCertificateUpdateListener=this.handleIdentityCertitificateUpdate.bind(this),this.secureContextWatchers=[]}_isSecure(){return!0}_equals(A){var B,Q;if(this===A)return!0;if(A instanceof E31)return this.caCertificateProvider===A.caCertificateProvider&&this.identityCertificateProvider===A.identityCertificateProvider&&((B=this.verifyOptions)===null||B===void 0?void 0:B.checkServerIdentity)===((Q=A.verifyOptions)===null||Q===void 0?void 0:Q.checkServerIdentity);else return!1}ref(){var A;if(this.refcount===0)this.caCertificateProvider.addCaCertificateListener(this.caCertificateUpdateListener),(A=this.identityCertificateProvider)===null||A===void 0||A.addIdentityCertificateListener(this.identityCertificateUpdateListener);this.refcount+=1}unref(){var A;if(this.refcount-=1,this.refcount===0)this.caCertificateProvider.removeCaCertificateListener(this.caCertificateUpdateListener),(A=this.identityCertificateProvider)===null||A===void 0||A.removeIdentityCertificateListener(this.identityCertificateUpdateListener)}_createSecureConnector(A,B,Q){return this.ref(),new E31.SecureConnectorImpl(this,A,B,Q!==null&&Q!==void 0?Q:uT1.CallCredentials.createEmpty())}maybeUpdateWatchers(){if(this.hasReceivedUpdates()){for(let A of this.secureContextWatchers)A(this.getLatestSecureContext());this.secureContextWatchers=[]}}handleCaCertificateUpdate(A){this.latestCaUpdate=A,this.maybeUpdateWatchers()}handleIdentityCertitificateUpdate(A){this.latestIdentityUpdate=A,this.maybeUpdateWatchers()}hasReceivedUpdates(){if(this.latestCaUpdate===void 0)return!1;if(this.identityCertificateProvider&&this.latestIdentityUpdate===void 0)return!1;return!0}getSecureContext(){if(this.hasReceivedUpdates())return Promise.resolve(this.getLatestSecureContext());else return new Promise((A)=>{this.secureContextWatchers.push(A)})}getLatestSecureContext(){var A,B;if(!this.latestCaUpdate)return null;if(this.identityCertificateProvider!==null&&!this.latestIdentityUpdate)return null;try{return U31.createSecureContext({ca:this.latestCaUpdate.caCertificate,key:(A=this.latestIdentityUpdate)===null||A===void 0?void 0:A.privateKey,cert:(B=this.latestIdentityUpdate)===null||B===void 0?void 0:B.certificate,ciphers:kW0.CIPHER_SUITES})}catch(Q){return hJ6.log(gJ6.LogVerbosity.ERROR,"Failed to createSecureContext with error "+Q.message),null}}}E31.SecureConnectorImpl=class{constructor(A,B,Q,D){this.parent=A,this.channelTarget=B,this.options=Q,this.callCredentials=D}connect(A){return new Promise((B,Q)=>{let D=this.parent.getLatestSecureContext();if(!D){Q(new Error("Failed to load credentials"));return}if(A.closed)Q(new Error("Socket closed while loading credentials"));let Z=_n2(D,this.parent.verifyOptions,this.channelTarget,this.options),G=Object.assign({socket:A},Z),F=()=>{Q(new Error("Socket closed"))},I=(W)=>{Q(W)},Y=U31.connect(G,()=>{var W;if(Y.removeListener("close",F),Y.removeListener("error",I),((W=this.parent.verifyOptions.rejectUnauthorized)!==null&&W!==void 0?W:!0)&&!Y.authorized){Q(Y.authorizationError);return}B({socket:Y,secure:!0})});Y.once("close",F),Y.once("error",I)})}async waitForReady(){await this.parent.getSecureContext()}getCallCredentials(){return this.callCredentials}destroy(){this.parent.unref()}};function uJ6(A,B,Q){return new E31(A,B,Q!==null&&Q!==void 0?Q:{})}class gT1 extends no{constructor(A,B){super();if(this.channelCredentials=A,this.callCredentials=B,!A._isSecure())throw new Error("Cannot compose insecure credentials")}compose(A){let B=this.callCredentials.compose(A);return new gT1(this.channelCredentials,B)}_isSecure(){return!0}_equals(A){if(this===A)return!0;if(A instanceof gT1)return this.channelCredentials._equals(A.channelCredentials)&&this.callCredentials._equals(A.callCredentials);else return!1}_createSecureConnector(A,B,Q){let D=this.callCredentials.compose(Q!==null&&Q!==void 0?Q:uT1.CallCredentials.createEmpty());return this.channelCredentials._createSecureConnector(A,B,D)}}});
var wo2=E((Eo2)=>{var gJ0;Object.defineProperty(Eo2,"__esModule",{value:!0});Eo2.GRPC_NODE_USE_ALTERNATIVE_RESOLVER=void 0;Eo2.GRPC_NODE_USE_ALTERNATIVE_RESOLVER=((gJ0=process.env.GRPC_NODE_USE_ALTERNATIVE_RESOLVER)!==null&&gJ0!==void 0?gJ0:"false")==="true"});
var xP1=E((St2)=>{Object.defineProperty(St2,"__esModule",{value:!0});St2.BaseSubchannelWrapper=void 0;class Pt2{constructor(A){this.child=A,this.healthy=!0,this.healthListeners=new Set,A.addHealthStateWatcher((B)=>{if(this.healthy)this.updateHealthListeners()})}updateHealthListeners(){for(let A of this.healthListeners)A(this.isHealthy())}getConnectivityState(){return this.child.getConnectivityState()}addConnectivityStateListener(A){this.child.addConnectivityStateListener(A)}removeConnectivityStateListener(A){this.child.removeConnectivityStateListener(A)}startConnecting(){this.child.startConnecting()}getAddress(){return this.child.getAddress()}throttleKeepalive(A){this.child.throttleKeepalive(A)}ref(){this.child.ref()}unref(){this.child.unref()}getChannelzRef(){return this.child.getChannelzRef()}isHealthy(){return this.healthy&&this.child.isHealthy()}addHealthStateWatcher(A){this.healthListeners.add(A)}removeHealthStateWatcher(A){this.healthListeners.delete(A)}setHealthy(A){if(A!==this.healthy){if(this.healthy=A,this.child.isHealthy())this.updateHealthListeners()}}getRealSubchannel(){return this.child.getRealSubchannel()}realSubchannelEquals(A){return this.getRealSubchannel()===A.getRealSubchannel()}getCallCredentials(){return this.child.getCallCredentials()}}St2.BaseSubchannelWrapper=Pt2});
var xW0=E((mn2)=>{Object.defineProperty(mn2,"__esModule",{value:!0});mn2.validateRetryThrottling=gn2;mn2.validateServiceConfig=un2;mn2.extractAndSelectServiceConfig=KX6;var GX6=J1("os"),mT1=_6(),dT1=/^\d+(\.\d{1,9})?s$/,FX6="node";function IX6(A){if("service"in A&&A.service!==""){if(typeof A.service!=="string")throw new Error(`Invalid method config name: invalid service: expected type string, got ${typeof A.service}`);if("method"in A&&A.method!==""){if(typeof A.method!=="string")throw new Error(`Invalid method config name: invalid method: expected type string, got ${typeof A.service}`);return{service:A.service,method:A.method}}else return{service:A.service}}else{if("method"in A&&A.method!==void 0)throw new Error("Invalid method config name: method set with empty or unset service");return{}}}function YX6(A){if(!("maxAttempts"in A)||!Number.isInteger(A.maxAttempts)||A.maxAttempts<2)throw new Error("Invalid method config retry policy: maxAttempts must be an integer at least 2");if(!("initialBackoff"in A)||typeof A.initialBackoff!=="string"||!dT1.test(A.initialBackoff))throw new Error("Invalid method config retry policy: initialBackoff must be a string consisting of a positive integer or decimal followed by s");if(!("maxBackoff"in A)||typeof A.maxBackoff!=="string"||!dT1.test(A.maxBackoff))throw new Error("Invalid method config retry policy: maxBackoff must be a string consisting of a positive integer or decimal followed by s");if(!("backoffMultiplier"in A)||typeof A.backoffMultiplier!=="number"||A.backoffMultiplier<=0)throw new Error("Invalid method config retry policy: backoffMultiplier must be a number greater than 0");if(!(("retryableStatusCodes"in A)&&Array.isArray(A.retryableStatusCodes)))throw new Error("Invalid method config retry policy: retryableStatusCodes is required");if(A.retryableStatusCodes.length===0)throw new Error("Invalid method config retry policy: retryableStatusCodes must be non-empty");for(let B of A.retryableStatusCodes)if(typeof B==="number"){if(!Object.values(mT1.Status).includes(B))throw new Error("Invalid method config retry policy: retryableStatusCodes value not in status code range")}else if(typeof B==="string"){if(!Object.values(mT1.Status).includes(B.toUpperCase()))throw new Error("Invalid method config retry policy: retryableStatusCodes value not a status code name")}else throw new Error("Invalid method config retry policy: retryableStatusCodes value must be a string or number");return{maxAttempts:A.maxAttempts,initialBackoff:A.initialBackoff,maxBackoff:A.maxBackoff,backoffMultiplier:A.backoffMultiplier,retryableStatusCodes:A.retryableStatusCodes}}function WX6(A){if(!("maxAttempts"in A)||!Number.isInteger(A.maxAttempts)||A.maxAttempts<2)throw new Error("Invalid method config hedging policy: maxAttempts must be an integer at least 2");if("hedgingDelay"in A&&(typeof A.hedgingDelay!=="string"||!dT1.test(A.hedgingDelay)))throw new Error("Invalid method config hedging policy: hedgingDelay must be a string consisting of a positive integer followed by s");if("nonFatalStatusCodes"in A&&Array.isArray(A.nonFatalStatusCodes))for(let Q of A.nonFatalStatusCodes)if(typeof Q==="number"){if(!Object.values(mT1.Status).includes(Q))throw new Error("Invalid method config hedging policy: nonFatalStatusCodes value not in status code range")}else if(typeof Q==="string"){if(!Object.values(mT1.Status).includes(Q.toUpperCase()))throw new Error("Invalid method config hedging policy: nonFatalStatusCodes value not a status code name")}else throw new Error("Invalid method config hedging policy: nonFatalStatusCodes value must be a string or number");let B={maxAttempts:A.maxAttempts};if(A.hedgingDelay)B.hedgingDelay=A.hedgingDelay;if(A.nonFatalStatusCodes)B.nonFatalStatusCodes=A.nonFatalStatusCodes;return B}function JX6(A){var B;let Q={name:[]};if(!("name"in A)||!Array.isArray(A.name))throw new Error("Invalid method config: invalid name array");for(let D of A.name)Q.name.push(IX6(D));if("waitForReady"in A){if(typeof A.waitForReady!=="boolean")throw new Error("Invalid method config: invalid waitForReady");Q.waitForReady=A.waitForReady}if("timeout"in A)if(typeof A.timeout==="object"){if(!("seconds"in A.timeout)||typeof A.timeout.seconds!=="number")throw new Error("Invalid method config: invalid timeout.seconds");if(!("nanos"in A.timeout)||typeof A.timeout.nanos!=="number")throw new Error("Invalid method config: invalid timeout.nanos");Q.timeout=A.timeout}else if(typeof A.timeout==="string"&&dT1.test(A.timeout)){let D=A.timeout.substring(0,A.timeout.length-1).split(".");Q.timeout={seconds:D[0]|0,nanos:((B=D[1])!==null&&B!==void 0?B:0)|0}}else throw new Error("Invalid method config: invalid timeout");if("maxRequestBytes"in A){if(typeof A.maxRequestBytes!=="number")throw new Error("Invalid method config: invalid maxRequestBytes");Q.maxRequestBytes=A.maxRequestBytes}if("maxResponseBytes"in A){if(typeof A.maxResponseBytes!=="number")throw new Error("Invalid method config: invalid maxRequestBytes");Q.maxResponseBytes=A.maxResponseBytes}if("retryPolicy"in A)if("hedgingPolicy"in A)throw new Error("Invalid method config: retryPolicy and hedgingPolicy cannot both be specified");else Q.retryPolicy=YX6(A.retryPolicy);else if("hedgingPolicy"in A)Q.hedgingPolicy=WX6(A.hedgingPolicy);return Q}function gn2(A){if(!("maxTokens"in A)||typeof A.maxTokens!=="number"||A.maxTokens<=0||A.maxTokens>1000)throw new Error("Invalid retryThrottling: maxTokens must be a number in (0, 1000]");if(!("tokenRatio"in A)||typeof A.tokenRatio!=="number"||A.tokenRatio<=0)throw new Error("Invalid retryThrottling: tokenRatio must be a number greater than 0");return{maxTokens:+A.maxTokens.toFixed(3),tokenRatio:+A.tokenRatio.toFixed(3)}}function XX6(A){if(!(typeof A==="object"&&A!==null))throw new Error(`Invalid loadBalancingConfig: unexpected type ${typeof A}`);let B=Object.keys(A);if(B.length>1)throw new Error(`Invalid loadBalancingConfig: unexpected multiple keys ${B}`);if(B.length===0)throw new Error("Invalid loadBalancingConfig: load balancing policy name required");return{[B[0]]:A[B[0]]}}function un2(A){let B={loadBalancingConfig:[],methodConfig:[]};if("loadBalancingPolicy"in A)if(typeof A.loadBalancingPolicy==="string")B.loadBalancingPolicy=A.loadBalancingPolicy;else throw new Error("Invalid service config: invalid loadBalancingPolicy");if("loadBalancingConfig"in A)if(Array.isArray(A.loadBalancingConfig))for(let D of A.loadBalancingConfig)B.loadBalancingConfig.push(XX6(D));else throw new Error("Invalid service config: invalid loadBalancingConfig");if("methodConfig"in A){if(Array.isArray(A.methodConfig))for(let D of A.methodConfig)B.methodConfig.push(JX6(D))}if("retryThrottling"in A)B.retryThrottling=gn2(A.retryThrottling);let Q=[];for(let D of B.methodConfig)for(let Z of D.name){for(let G of Q)if(Z.service===G.service&&Z.method===G.method)throw new Error(`Invalid service config: duplicate name ${Z.service}/${Z.method}`);Q.push(Z)}return B}function VX6(A){if(!("serviceConfig"in A))throw new Error("Invalid service config choice: missing service config");let B={serviceConfig:un2(A.serviceConfig)};if("clientLanguage"in A)if(Array.isArray(A.clientLanguage)){B.clientLanguage=[];for(let D of A.clientLanguage)if(typeof D==="string")B.clientLanguage.push(D);else throw new Error("Invalid service config choice: invalid clientLanguage")}else throw new Error("Invalid service config choice: invalid clientLanguage");if("clientHostname"in A)if(Array.isArray(A.clientHostname)){B.clientHostname=[];for(let D of A.clientHostname)if(typeof D==="string")B.clientHostname.push(D);else throw new Error("Invalid service config choice: invalid clientHostname")}else throw new Error("Invalid service config choice: invalid clientHostname");if("percentage"in A)if(typeof A.percentage==="number"&&0<=A.percentage&&A.percentage<=100)B.percentage=A.percentage;else throw new Error("Invalid service config choice: invalid percentage");let Q=["clientLanguage","percentage","clientHostname","serviceConfig"];for(let D in A)if(!Q.includes(D))throw new Error(`Invalid service config choice: unexpected field ${D}`);return B}function CX6(A,B){if(!Array.isArray(A))throw new Error("Invalid service config list");for(let Q of A){let D=VX6(Q);if(typeof D.percentage==="number"&&B>D.percentage)continue;if(Array.isArray(D.clientHostname)){let Z=!1;for(let G of D.clientHostname)if(G===GX6.hostname())Z=!0;if(!Z)continue}if(Array.isArray(D.clientLanguage)){let Z=!1;for(let G of D.clientLanguage)if(G===FX6)Z=!0;if(!Z)continue}return D.serviceConfig}throw new Error("No matching service config found")}function KX6(A,B){for(let Q of A)if(Q.length>0&&Q[0].startsWith("grpc_config=")){let D=Q.join("").substring(12),Z=JSON.parse(D);return CX6(Z,B)}return null}});
var xr2=E((_u5,lK6)=>{lK6.exports={nested:{google:{nested:{protobuf:{nested:{Type:{fields:{name:{type:"string",id:1},fields:{rule:"repeated",type:"Field",id:2},oneofs:{rule:"repeated",type:"string",id:3},options:{rule:"repeated",type:"Option",id:4},sourceContext:{type:"SourceContext",id:5},syntax:{type:"Syntax",id:6}}},Field:{fields:{kind:{type:"Kind",id:1},cardinality:{type:"Cardinality",id:2},number:{type:"int32",id:3},name:{type:"string",id:4},typeUrl:{type:"string",id:6},oneofIndex:{type:"int32",id:7},packed:{type:"bool",id:8},options:{rule:"repeated",type:"Option",id:9},jsonName:{type:"string",id:10},defaultValue:{type:"string",id:11}},nested:{Kind:{values:{TYPE_UNKNOWN:0,TYPE_DOUBLE:1,TYPE_FLOAT:2,TYPE_INT64:3,TYPE_UINT64:4,TYPE_INT32:5,TYPE_FIXED64:6,TYPE_FIXED32:7,TYPE_BOOL:8,TYPE_STRING:9,TYPE_GROUP:10,TYPE_MESSAGE:11,TYPE_BYTES:12,TYPE_UINT32:13,TYPE_ENUM:14,TYPE_SFIXED32:15,TYPE_SFIXED64:16,TYPE_SINT32:17,TYPE_SINT64:18}},Cardinality:{values:{CARDINALITY_UNKNOWN:0,CARDINALITY_OPTIONAL:1,CARDINALITY_REQUIRED:2,CARDINALITY_REPEATED:3}}}},Enum:{fields:{name:{type:"string",id:1},enumvalue:{rule:"repeated",type:"EnumValue",id:2},options:{rule:"repeated",type:"Option",id:3},sourceContext:{type:"SourceContext",id:4},syntax:{type:"Syntax",id:5}}},EnumValue:{fields:{name:{type:"string",id:1},number:{type:"int32",id:2},options:{rule:"repeated",type:"Option",id:3}}},Option:{fields:{name:{type:"string",id:1},value:{type:"Any",id:2}}},Syntax:{values:{SYNTAX_PROTO2:0,SYNTAX_PROTO3:1}},Any:{fields:{type_url:{type:"string",id:1},value:{type:"bytes",id:2}}},SourceContext:{fields:{fileName:{type:"string",id:1}}}}}}}}}});
var ya2=E((Sa2)=>{Object.defineProperty(Sa2,"__esModule",{value:!0});Sa2.ClientDuplexStreamImpl=Sa2.ClientWritableStreamImpl=Sa2.ClientReadableStreamImpl=Sa2.ClientUnaryCallImpl=void 0;Sa2.callErrorFromStatus=VV6;var XV6=J1("events"),fW0=J1("stream"),R31=_6();function VV6(A,B){let Q=`${A.code} ${R31.Status[A.code]}: ${A.details}`,Z=`${new Error(Q).stack}
for call at
${B}`;return Object.assign(new Error(Q),A,{stack:Z})}class Ra2 extends XV6.EventEmitter{constructor(){super()}cancel(){var A;(A=this.call)===null||A===void 0||A.cancelWithStatus(R31.Status.CANCELLED,"Cancelled on client")}getPeer(){var A,B;return(B=(A=this.call)===null||A===void 0?void 0:A.getPeer())!==null&&B!==void 0?B:"unknown"}}Sa2.ClientUnaryCallImpl=Ra2;class Oa2 extends fW0.Readable{constructor(A){super({objectMode:!0});this.deserialize=A}cancel(){var A;(A=this.call)===null||A===void 0||A.cancelWithStatus(R31.Status.CANCELLED,"Cancelled on client")}getPeer(){var A,B;return(B=(A=this.call)===null||A===void 0?void 0:A.getPeer())!==null&&B!==void 0?B:"unknown"}_read(A){var B;(B=this.call)===null||B===void 0||B.startRead()}}Sa2.ClientReadableStreamImpl=Oa2;class Ta2 extends fW0.Writable{constructor(A){super({objectMode:!0});this.serialize=A}cancel(){var A;(A=this.call)===null||A===void 0||A.cancelWithStatus(R31.Status.CANCELLED,"Cancelled on client")}getPeer(){var A,B;return(B=(A=this.call)===null||A===void 0?void 0:A.getPeer())!==null&&B!==void 0?B:"unknown"}_write(A,B,Q){var D;let Z={callback:Q},G=Number(B);if(!Number.isNaN(G))Z.flags=G;(D=this.call)===null||D===void 0||D.sendMessageWithContext(Z,A)}_final(A){var B;(B=this.call)===null||B===void 0||B.halfClose(),A()}}Sa2.ClientWritableStreamImpl=Ta2;class Pa2 extends fW0.Duplex{constructor(A,B){super({objectMode:!0});this.serialize=A,this.deserialize=B}cancel(){var A;(A=this.call)===null||A===void 0||A.cancelWithStatus(R31.Status.CANCELLED,"Cancelled on client")}getPeer(){var A,B;return(B=(A=this.call)===null||A===void 0?void 0:A.getPeer())!==null&&B!==void 0?B:"unknown"}_read(A){var B;(B=this.call)===null||B===void 0||B.startRead()}_write(A,B,Q){var D;let Z={callback:Q},G=Number(B);if(!Number.isNaN(G))Z.flags=G;(D=this.call)===null||D===void 0||D.sendMessageWithContext(Z,A)}_final(A){var B;(B=this.call)===null||B===void 0||B.halfClose(),A()}}Sa2.ClientDuplexStreamImpl=Pa2});
var yr2=E((B6,jr2)=>{var QX=HP1();jr2.exports=B6=QX.descriptor=QX.Root.fromJSON(TJ0()).lookup(".google.protobuf");var{Namespace:Tr2,Root:f31,Enum:$P,Type:Bx,Field:Qx,MapField:kK6,OneOf:zP1,Service:h31,Method:EP1}=QX;f31.fromDescriptor=function A(B){if(typeof B.length==="number")B=B6.FileDescriptorSet.decode(B);var Q=new f31;if(B.file){var D,Z;for(var G=0,F;G<B.file.length;++G){if(Z=Q,(D=B.file[G]).package&&D.package.length)Z=Q.define(D.package);if(D.name&&D.name.length)Q.files.push(Z.filename=D.name);if(D.messageType)for(F=0;F<D.messageType.length;++F)Z.add(Bx.fromDescriptor(D.messageType[F],D.syntax));if(D.enumType)for(F=0;F<D.enumType.length;++F)Z.add($P.fromDescriptor(D.enumType[F]));if(D.extension)for(F=0;F<D.extension.length;++F)Z.add(Qx.fromDescriptor(D.extension[F]));if(D.service)for(F=0;F<D.service.length;++F)Z.add(h31.fromDescriptor(D.service[F]));var I=Zt(D.options,B6.FileOptions);if(I){var Y=Object.keys(I);for(F=0;F<Y.length;++F)Z.setOption(Y[F],I[Y[F]])}}}return Q};f31.prototype.toDescriptor=function A(B){var Q=B6.FileDescriptorSet.create();return Pr2(this,Q.file,B),Q};function Pr2(A,B,Q){var D=B6.FileDescriptorProto.create({name:A.filename||(A.fullName.substring(1).replace(/\./g,"_")||"root")+".proto"});if(Q)D.syntax=Q;if(!(A instanceof f31))D.package=A.fullName.substring(1);for(var Z=0,G;Z<A.nestedArray.length;++Z)if((G=A._nestedArray[Z])instanceof Bx)D.messageType.push(G.toDescriptor(Q));else if(G instanceof $P)D.enumType.push(G.toDescriptor());else if(G instanceof Qx)D.extension.push(G.toDescriptor(Q));else if(G instanceof h31)D.service.push(G.toDescriptor());else if(G instanceof Tr2)Pr2(G,B,Q);if(D.options=Gt(A.options,B6.FileOptions),D.messageType.length+D.enumType.length+D.extension.length+D.service.length)B.push(D)}var _K6=0;Bx.fromDescriptor=function A(B,Q){if(typeof B.length==="number")B=B6.DescriptorProto.decode(B);var D=new Bx(B.name.length?B.name:"Type"+_K6++,Zt(B.options,B6.MessageOptions)),Z;if(B.oneofDecl)for(Z=0;Z<B.oneofDecl.length;++Z)D.add(zP1.fromDescriptor(B.oneofDecl[Z]));if(B.field)for(Z=0;Z<B.field.length;++Z){var G=Qx.fromDescriptor(B.field[Z],Q);if(D.add(G),B.field[Z].hasOwnProperty("oneofIndex"))D.oneofsArray[B.field[Z].oneofIndex].add(G)}if(B.extension)for(Z=0;Z<B.extension.length;++Z)D.add(Qx.fromDescriptor(B.extension[Z],Q));if(B.nestedType){for(Z=0;Z<B.nestedType.length;++Z)if(D.add(Bx.fromDescriptor(B.nestedType[Z],Q)),B.nestedType[Z].options&&B.nestedType[Z].options.mapEntry)D.setOption("map_entry",!0)}if(B.enumType)for(Z=0;Z<B.enumType.length;++Z)D.add($P.fromDescriptor(B.enumType[Z]));if(B.extensionRange&&B.extensionRange.length){D.extensions=[];for(Z=0;Z<B.extensionRange.length;++Z)D.extensions.push([B.extensionRange[Z].start,B.extensionRange[Z].end])}if(B.reservedRange&&B.reservedRange.length||B.reservedName&&B.reservedName.length){if(D.reserved=[],B.reservedRange)for(Z=0;Z<B.reservedRange.length;++Z)D.reserved.push([B.reservedRange[Z].start,B.reservedRange[Z].end]);if(B.reservedName)for(Z=0;Z<B.reservedName.length;++Z)D.reserved.push(B.reservedName[Z])}return D};Bx.prototype.toDescriptor=function A(B){var Q=B6.DescriptorProto.create({name:this.name}),D;for(D=0;D<this.fieldsArray.length;++D){var Z;if(Q.field.push(Z=this._fieldsArray[D].toDescriptor(B)),this._fieldsArray[D]instanceof kK6){var G=PJ0(this._fieldsArray[D].keyType,this._fieldsArray[D].resolvedKeyType),F=PJ0(this._fieldsArray[D].type,this._fieldsArray[D].resolvedType),I=F===11||F===14?this._fieldsArray[D].resolvedType&&Sr2(this.parent,this._fieldsArray[D].resolvedType)||this._fieldsArray[D].type:void 0;Q.nestedType.push(B6.DescriptorProto.create({name:Z.typeName,field:[B6.FieldDescriptorProto.create({name:"key",number:1,label:1,type:G}),B6.FieldDescriptorProto.create({name:"value",number:2,label:1,type:F,typeName:I})],options:B6.MessageOptions.create({mapEntry:!0})}))}}for(D=0;D<this.oneofsArray.length;++D)Q.oneofDecl.push(this._oneofsArray[D].toDescriptor());for(D=0;D<this.nestedArray.length;++D)if(this._nestedArray[D]instanceof Qx)Q.field.push(this._nestedArray[D].toDescriptor(B));else if(this._nestedArray[D]instanceof Bx)Q.nestedType.push(this._nestedArray[D].toDescriptor(B));else if(this._nestedArray[D]instanceof $P)Q.enumType.push(this._nestedArray[D].toDescriptor());if(this.extensions)for(D=0;D<this.extensions.length;++D)Q.extensionRange.push(B6.DescriptorProto.ExtensionRange.create({start:this.extensions[D][0],end:this.extensions[D][1]}));if(this.reserved)for(D=0;D<this.reserved.length;++D)if(typeof this.reserved[D]==="string")Q.reservedName.push(this.reserved[D]);else Q.reservedRange.push(B6.DescriptorProto.ReservedRange.create({start:this.reserved[D][0],end:this.reserved[D][1]}));return Q.options=Gt(this.options,B6.MessageOptions),Q};var xK6=/^(?![eE])[0-9]*(?:\.[0-9]*)?(?:[eE][+-]?[0-9]+)?$/;Qx.fromDescriptor=function A(B,Q){if(typeof B.length==="number")B=B6.DescriptorProto.decode(B);if(typeof B.number!=="number")throw Error("missing field id");var D;if(B.typeName&&B.typeName.length)D=B.typeName;else D=gK6(B.type);var Z;switch(B.label){case 1:Z=void 0;break;case 2:Z="required";break;case 3:Z="repeated";break;default:throw Error("illegal label: "+B.label)}var G=B.extendee;if(B.extendee!==void 0)G=G.length?G:void 0;var F=new Qx(B.name.length?B.name:"field"+B.number,B.number,D,Z,G);if(F.options=Zt(B.options,B6.FieldOptions),B.defaultValue&&B.defaultValue.length){var I=B.defaultValue;switch(I){case"true":case"TRUE":I=!0;break;case"false":case"FALSE":I=!1;break;default:var Y=xK6.exec(I);if(Y)I=parseInt(I);break}F.setOption("default",I)}if(uK6(B.type)){if(Q==="proto3"){if(B.options&&!B.options.packed)F.setOption("packed",!1)}else if(!(B.options&&B.options.packed))F.setOption("packed",!1)}return F};Qx.prototype.toDescriptor=function A(B){var Q=B6.FieldDescriptorProto.create({name:this.name,number:this.id});if(this.map)Q.type=11,Q.typeName=QX.util.ucFirst(this.name),Q.label=3;else{switch(Q.type=PJ0(this.type,this.resolve().resolvedType)){case 10:case 11:case 14:Q.typeName=this.resolvedType?Sr2(this.parent,this.resolvedType):this.type;break}switch(this.rule){case"repeated":Q.label=3;break;case"required":Q.label=2;break;default:Q.label=1;break}}if(Q.extendee=this.extensionField?this.extensionField.parent.fullName:this.extend,this.partOf){if((Q.oneofIndex=this.parent.oneofsArray.indexOf(this.partOf))<0)throw Error("missing oneof")}if(this.options){if(Q.options=Gt(this.options,B6.FieldOptions),this.options.default!=null)Q.defaultValue=String(this.options.default)}if(B==="proto3"){if(!this.packed)(Q.options||(Q.options=B6.FieldOptions.create())).packed=!1}else if(this.packed)(Q.options||(Q.options=B6.FieldOptions.create())).packed=!0;return Q};var vK6=0;$P.fromDescriptor=function A(B){if(typeof B.length==="number")B=B6.EnumDescriptorProto.decode(B);var Q={};if(B.value)for(var D=0;D<B.value.length;++D){var Z=B.value[D].name,G=B.value[D].number||0;Q[Z&&Z.length?Z:"NAME"+G]=G}return new $P(B.name&&B.name.length?B.name:"Enum"+vK6++,Q,Zt(B.options,B6.EnumOptions))};$P.prototype.toDescriptor=function A(){var B=[];for(var Q=0,D=Object.keys(this.values);Q<D.length;++Q)B.push(B6.EnumValueDescriptorProto.create({name:D[Q],number:this.values[D[Q]]}));return B6.EnumDescriptorProto.create({name:this.name,value:B,options:Gt(this.options,B6.EnumOptions)})};var bK6=0;zP1.fromDescriptor=function A(B){if(typeof B.length==="number")B=B6.OneofDescriptorProto.decode(B);return new zP1(B.name&&B.name.length?B.name:"oneof"+bK6++)};zP1.prototype.toDescriptor=function A(){return B6.OneofDescriptorProto.create({name:this.name})};var fK6=0;h31.fromDescriptor=function A(B){if(typeof B.length==="number")B=B6.ServiceDescriptorProto.decode(B);var Q=new h31(B.name&&B.name.length?B.name:"Service"+fK6++,Zt(B.options,B6.ServiceOptions));if(B.method)for(var D=0;D<B.method.length;++D)Q.add(EP1.fromDescriptor(B.method[D]));return Q};h31.prototype.toDescriptor=function A(){var B=[];for(var Q=0;Q<this.methodsArray.length;++Q)B.push(this._methodsArray[Q].toDescriptor());return B6.ServiceDescriptorProto.create({name:this.name,method:B,options:Gt(this.options,B6.ServiceOptions)})};var hK6=0;EP1.fromDescriptor=function A(B){if(typeof B.length==="number")B=B6.MethodDescriptorProto.decode(B);return new EP1(B.name&&B.name.length?B.name:"Method"+hK6++,"rpc",B.inputType,B.outputType,Boolean(B.clientStreaming),Boolean(B.serverStreaming),Zt(B.options,B6.MethodOptions))};EP1.prototype.toDescriptor=function A(){return B6.MethodDescriptorProto.create({name:this.name,inputType:this.resolvedRequestType?this.resolvedRequestType.fullName:this.requestType,outputType:this.resolvedResponseType?this.resolvedResponseType.fullName:this.responseType,clientStreaming:this.requestStream,serverStreaming:this.responseStream,options:Gt(this.options,B6.MethodOptions)})};function gK6(A){switch(A){case 1:return"double";case 2:return"float";case 3:return"int64";case 4:return"uint64";case 5:return"int32";case 6:return"fixed64";case 7:return"fixed32";case 8:return"bool";case 9:return"string";case 12:return"bytes";case 13:return"uint32";case 15:return"sfixed32";case 16:return"sfixed64";case 17:return"sint32";case 18:return"sint64"}throw Error("illegal type: "+A)}function uK6(A){switch(A){case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 13:case 14:case 15:case 16:case 17:case 18:return!0}return!1}function PJ0(A,B){switch(A){case"double":return 1;case"float":return 2;case"int64":return 3;case"uint64":return 4;case"int32":return 5;case"fixed64":return 6;case"fixed32":return 7;case"bool":return 8;case"string":return 9;case"bytes":return 12;case"uint32":return 13;case"sfixed32":return 15;case"sfixed64":return 16;case"sint32":return 17;case"sint64":return 18}if(B instanceof $P)return 14;if(B instanceof Bx)return B.group?10:11;throw Error("illegal type: "+A)}function Zt(A,B){if(!A)return;var Q=[];for(var D=0,Z,G,F;D<B.fieldsArray.length;++D)if((G=(Z=B._fieldsArray[D]).name)!=="uninterpretedOption"){if(A.hasOwnProperty(G)){if(F=A[G],Z.resolvedType instanceof $P&&typeof F==="number"&&Z.resolvedType.valuesById[F]!==void 0)F=Z.resolvedType.valuesById[F];Q.push(mK6(G),F)}}return Q.length?QX.util.toObject(Q):void 0}function Gt(A,B){if(!A)return;var Q=[];for(var D=0,Z=Object.keys(A),G,F;D<Z.length;++D){if(F=A[G=Z[D]],G==="default")continue;var I=B.fields[G];if(!I&&!(I=B.fields[G=QX.util.camelCase(G)]))continue;Q.push(G,F)}return Q.length?B.fromObject(QX.util.toObject(Q)):void 0}function Sr2(A,B){var Q=A.fullName.split("."),D=B.fullName.split("."),Z=0,G=0,F=D.length-1;if(!(A instanceof f31)&&B instanceof Tr2)while(Z<Q.length&&G<F&&Q[Z]===D[G]){var I=B.lookup(Q[Z++],!0);if(I!==null&&I!==B)break;++G}else for(;Z<Q.length&&G<F&&Q[Z]===D[G];++Z,++G);return D.slice(G).join(".")}function mK6(A){return A.substring(0,1)+A.substring(1).replace(/([A-Z])(?=[a-z]|$)/g,function(B,Q){return"_"+Q.toLowerCase()})}});
var zJ0=E((Mu5,Xr2)=>{Xr2.exports=FK6;var GK6=Z$(),qJ0=mu(),NJ0=$I();function Jr2(A,B,Q,D){return B.resolvedType.group?A("types[%i].encode(%s,w.uint32(%i)).uint32(%i)",Q,D,(B.id<<3|3)>>>0,(B.id<<3|4)>>>0):A("types[%i].encode(%s,w.uint32(%i).fork()).ldelim()",Q,D,(B.id<<3|2)>>>0)}function FK6(A){var B=NJ0.codegen(["m","w"],A.name+"$encode")("if(!w)")("w=Writer.create()"),Q,D,Z=A.fieldsArray.slice().sort(NJ0.compareFieldsById);for(var Q=0;Q<Z.length;++Q){var G=Z[Q].resolve(),F=A._fieldsArray.indexOf(G),I=G.resolvedType instanceof GK6?"int32":G.type,Y=qJ0.basic[I];if(D="m"+NJ0.safeProp(G.name),G.map){if(B("if(%s!=null&&Object.hasOwnProperty.call(m,%j)){",D,G.name)("for(var ks=Object.keys(%s),i=0;i<ks.length;++i){",D)("w.uint32(%i).fork().uint32(%i).%s(ks[i])",(G.id<<3|2)>>>0,8|qJ0.mapKey[G.keyType],G.keyType),Y===void 0)B("types[%i].encode(%s[ks[i]],w.uint32(18).fork()).ldelim().ldelim()",F,D);else B(".uint32(%i).%s(%s[ks[i]]).ldelim()",16|Y,I,D);B("}")("}")}else if(G.repeated){if(B("if(%s!=null&&%s.length){",D,D),G.packed&&qJ0.packed[I]!==void 0)B("w.uint32(%i).fork()",(G.id<<3|2)>>>0)("for(var i=0;i<%s.length;++i)",D)("w.%s(%s[i])",I,D)("w.ldelim()");else if(B("for(var i=0;i<%s.length;++i)",D),Y===void 0)Jr2(B,G,F,D+"[i]");else B("w.uint32(%i).%s(%s[i])",(G.id<<3|Y)>>>0,I,D);B("}")}else{if(G.optional)B("if(%s!=null&&Object.hasOwnProperty.call(m,%j))",D,G.name);if(Y===void 0)Jr2(B,G,F,D);else B("w.uint32(%i).%s(%s)",(G.id<<3|Y)>>>0,I,D)}}return B("return w")}});
var zo2=E((Ko2)=>{Object.defineProperty(Ko2,"__esModule",{value:!0});Ko2.Subchannel=void 0;var G3=EE(),nH6=q31(),hJ0=F7(),LP1=_6(),aH6=hV(),sH6=UE(),JM=pu(),rH6="subchannel",oH6=2147483647;class Co2{constructor(A,B,Q,D,Z){var G;this.channelTarget=A,this.subchannelAddress=B,this.options=Q,this.connector=Z,this.connectivityState=G3.ConnectivityState.IDLE,this.transport=null,this.continueConnecting=!1,this.stateListeners=new Set,this.refcount=0,this.channelzEnabled=!0;let F={initialDelay:Q["grpc.initial_reconnect_backoff_ms"],maxDelay:Q["grpc.max_reconnect_backoff_ms"]};if(this.backoffTimeout=new nH6.BackoffTimeout(()=>{this.handleBackoffTimer()},F),this.backoffTimeout.unref(),this.subchannelAddressString=sH6.subchannelAddressToString(B),this.keepaliveTime=(G=Q["grpc.keepalive_time_ms"])!==null&&G!==void 0?G:-1,Q["grpc.enable_channelz"]===0)this.channelzEnabled=!1,this.channelzTrace=new JM.ChannelzTraceStub,this.callTracker=new JM.ChannelzCallTrackerStub,this.childrenTracker=new JM.ChannelzChildrenTrackerStub,this.streamTracker=new JM.ChannelzCallTrackerStub;else this.channelzTrace=new JM.ChannelzTrace,this.callTracker=new JM.ChannelzCallTracker,this.childrenTracker=new JM.ChannelzChildrenTracker,this.streamTracker=new JM.ChannelzCallTracker;this.channelzRef=JM.registerChannelzSubchannel(this.subchannelAddressString,()=>this.getChannelzInfo(),this.channelzEnabled),this.channelzTrace.addTrace("CT_INFO","Subchannel created"),this.trace("Subchannel constructed with options "+JSON.stringify(Q,void 0,2)),this.secureConnector=D._createSecureConnector(A,Q)}getChannelzInfo(){return{state:this.connectivityState,trace:this.channelzTrace,callTracker:this.callTracker,children:this.childrenTracker.getChildLists(),target:this.subchannelAddressString}}trace(A){hJ0.trace(LP1.LogVerbosity.DEBUG,rH6,"("+this.channelzRef.id+") "+this.subchannelAddressString+" "+A)}refTrace(A){hJ0.trace(LP1.LogVerbosity.DEBUG,"subchannel_refcount","("+this.channelzRef.id+") "+this.subchannelAddressString+" "+A)}handleBackoffTimer(){if(this.continueConnecting)this.transitionToState([G3.ConnectivityState.TRANSIENT_FAILURE],G3.ConnectivityState.CONNECTING);else this.transitionToState([G3.ConnectivityState.TRANSIENT_FAILURE],G3.ConnectivityState.IDLE)}startBackoff(){this.backoffTimeout.runOnce()}stopBackoff(){this.backoffTimeout.stop(),this.backoffTimeout.reset()}startConnectingInternal(){let A=this.options;if(A["grpc.keepalive_time_ms"]){let B=Math.min(this.keepaliveTime,oH6);A=Object.assign(Object.assign({},A),{"grpc.keepalive_time_ms":B})}this.connector.connect(this.subchannelAddress,this.secureConnector,A).then((B)=>{if(this.transitionToState([G3.ConnectivityState.CONNECTING],G3.ConnectivityState.READY)){if(this.transport=B,this.channelzEnabled)this.childrenTracker.refChild(B.getChannelzRef());B.addDisconnectListener((Q)=>{if(this.transitionToState([G3.ConnectivityState.READY],G3.ConnectivityState.IDLE),Q&&this.keepaliveTime>0)this.keepaliveTime*=2,hJ0.log(LP1.LogVerbosity.ERROR,`Connection to ${aH6.uriToString(this.channelTarget)} at ${this.subchannelAddressString} rejected by server because of excess pings. Increasing ping interval to ${this.keepaliveTime} ms`)})}else B.shutdown()},(B)=>{this.transitionToState([G3.ConnectivityState.CONNECTING],G3.ConnectivityState.TRANSIENT_FAILURE,`${B}`)})}transitionToState(A,B,Q){var D,Z;if(A.indexOf(this.connectivityState)===-1)return!1;if(Q)this.trace(G3.ConnectivityState[this.connectivityState]+" -> "+G3.ConnectivityState[B]+' with error "'+Q+'"');else this.trace(G3.ConnectivityState[this.connectivityState]+" -> "+G3.ConnectivityState[B]);if(this.channelzEnabled)this.channelzTrace.addTrace("CT_INFO","Connectivity state change to "+G3.ConnectivityState[B]);let G=this.connectivityState;switch(this.connectivityState=B,B){case G3.ConnectivityState.READY:this.stopBackoff();break;case G3.ConnectivityState.CONNECTING:this.startBackoff(),this.startConnectingInternal(),this.continueConnecting=!1;break;case G3.ConnectivityState.TRANSIENT_FAILURE:if(this.channelzEnabled&&this.transport)this.childrenTracker.unrefChild(this.transport.getChannelzRef());if((D=this.transport)===null||D===void 0||D.shutdown(),this.transport=null,!this.backoffTimeout.isRunning())process.nextTick(()=>{this.handleBackoffTimer()});break;case G3.ConnectivityState.IDLE:if(this.channelzEnabled&&this.transport)this.childrenTracker.unrefChild(this.transport.getChannelzRef());(Z=this.transport)===null||Z===void 0||Z.shutdown(),this.transport=null;break;default:throw new Error(`Invalid state: unknown ConnectivityState ${B}`)}for(let F of this.stateListeners)F(this,G,B,this.keepaliveTime,Q);return!0}ref(){this.refTrace("refcount "+this.refcount+" -> "+(this.refcount+1)),this.refcount+=1}unref(){if(this.refTrace("refcount "+this.refcount+" -> "+(this.refcount-1)),this.refcount-=1,this.refcount===0)this.channelzTrace.addTrace("CT_INFO","Shutting down"),JM.unregisterChannelzRef(this.channelzRef),this.secureConnector.destroy(),process.nextTick(()=>{this.transitionToState([G3.ConnectivityState.CONNECTING,G3.ConnectivityState.READY],G3.ConnectivityState.IDLE)})}unrefIfOneRef(){if(this.refcount===1)return this.unref(),!0;return!1}createCall(A,B,Q,D){if(!this.transport)throw new Error("Cannot create call, subchannel not READY");let Z;if(this.channelzEnabled)this.callTracker.addCallStarted(),this.streamTracker.addCallStarted(),Z={onCallEnd:(G)=>{if(G.code===LP1.Status.OK)this.callTracker.addCallSucceeded();else this.callTracker.addCallFailed()}};else Z={};return this.transport.createCall(A,B,Q,D,Z)}startConnecting(){process.nextTick(()=>{if(!this.transitionToState([G3.ConnectivityState.IDLE],G3.ConnectivityState.CONNECTING)){if(this.connectivityState===G3.ConnectivityState.TRANSIENT_FAILURE)this.continueConnecting=!0}})}getConnectivityState(){return this.connectivityState}addConnectivityStateListener(A){this.stateListeners.add(A)}removeConnectivityStateListener(A){this.stateListeners.delete(A)}resetBackoff(){process.nextTick(()=>{this.backoffTimeout.reset(),this.transitionToState([G3.ConnectivityState.TRANSIENT_FAILURE],G3.ConnectivityState.CONNECTING)})}getAddress(){return this.subchannelAddressString}getChannelzRef(){return this.channelzRef}isHealthy(){return!0}addHealthStateWatcher(A){}removeHealthStateWatcher(A){}getRealSubchannel(){return this}realSubchannelEquals(A){return A.getRealSubchannel()===this}throttleKeepalive(A){if(A>this.keepaliveTime)this.keepaliveTime=A}getCallCredentials(){return this.secureConnector.getCallCredentials()}}Ko2.Subchannel=Co2});
var zt2=E((Kt2)=>{Object.defineProperty(Kt2,"__esModule",{value:!0});Kt2.LoadBalancingCall=void 0;var Xt2=EE(),yP1=_6(),Vt2=i31(),kP1=GJ(),n31=s_(),wE6=hV(),$E6=F7(),QX0=jP1(),qE6=J1("http2"),NE6="load_balancing_call";class Ct2{constructor(A,B,Q,D,Z,G,F){var I,Y;this.channel=A,this.callConfig=B,this.methodName=Q,this.host=D,this.credentials=Z,this.deadline=G,this.callNumber=F,this.child=null,this.readPending=!1,this.pendingMessage=null,this.pendingHalfClose=!1,this.ended=!1,this.metadata=null,this.listener=null,this.onCallEnded=null,this.childStartTime=null;let W=this.methodName.split("/"),J="";if(W.length>=2)J=W[1];let X=(Y=(I=wE6.splitHostPort(this.host))===null||I===void 0?void 0:I.host)!==null&&Y!==void 0?Y:"localhost";this.serviceUrl=`https://${X}/${J}`,this.startTime=new Date}getDeadlineInfo(){var A,B;let Q=[];if(this.childStartTime){if(this.childStartTime>this.startTime){if((A=this.metadata)===null||A===void 0?void 0:A.getOptions().waitForReady)Q.push("wait_for_ready");Q.push(`LB pick: ${Vt2.formatDateDifference(this.startTime,this.childStartTime)}`)}return Q.push(...this.child.getDeadlineInfo()),Q}else{if((B=this.metadata)===null||B===void 0?void 0:B.getOptions().waitForReady)Q.push("wait_for_ready");Q.push("Waiting for LB pick")}return Q}trace(A){$E6.trace(yP1.LogVerbosity.DEBUG,NE6,"["+this.callNumber+"] "+A)}outputStatus(A,B){var Q,D;if(!this.ended){this.ended=!0,this.trace("ended with status: code="+A.code+' details="'+A.details+'" start time='+this.startTime.toISOString());let Z=Object.assign(Object.assign({},A),{progress:B});(Q=this.listener)===null||Q===void 0||Q.onReceiveStatus(Z),(D=this.onCallEnded)===null||D===void 0||D.call(this,Z.code)}}doPick(){var A,B;if(this.ended)return;if(!this.metadata)throw new Error("doPick called before start");this.trace("Pick called");let Q=this.metadata.clone(),D=this.channel.doPick(Q,this.callConfig.pickInformation),Z=D.subchannel?"("+D.subchannel.getChannelzRef().id+") "+D.subchannel.getAddress():""+D.subchannel;switch(this.trace("Pick result: "+n31.PickResultType[D.pickResultType]+" subchannel: "+Z+" status: "+((A=D.status)===null||A===void 0?void 0:A.code)+" "+((B=D.status)===null||B===void 0?void 0:B.details)),D.pickResultType){case n31.PickResultType.COMPLETE:this.credentials.compose(D.subchannel.getCallCredentials()).generateMetadata({method_name:this.methodName,service_url:this.serviceUrl}).then((Y)=>{var W;if(this.ended){this.trace("Credentials metadata generation finished after call ended");return}if(Q.merge(Y),Q.get("authorization").length>1)this.outputStatus({code:yP1.Status.INTERNAL,details:'"authorization" metadata cannot have multiple values',metadata:new kP1.Metadata},"PROCESSED");if(D.subchannel.getConnectivityState()!==Xt2.ConnectivityState.READY){this.trace("Picked subchannel "+Z+" has state "+Xt2.ConnectivityState[D.subchannel.getConnectivityState()]+" after getting credentials metadata. Retrying pick"),this.doPick();return}if(this.deadline!==1/0)Q.set("grpc-timeout",Vt2.getDeadlineTimeoutString(this.deadline));try{this.child=D.subchannel.getRealSubchannel().createCall(Q,this.host,this.methodName,{onReceiveMetadata:(J)=>{this.trace("Received metadata"),this.listener.onReceiveMetadata(J)},onReceiveMessage:(J)=>{this.trace("Received message"),this.listener.onReceiveMessage(J)},onReceiveStatus:(J)=>{if(this.trace("Received status"),J.rstCode===qE6.constants.NGHTTP2_REFUSED_STREAM)this.outputStatus(J,"REFUSED");else this.outputStatus(J,"PROCESSED")}}),this.childStartTime=new Date}catch(J){this.trace("Failed to start call on picked subchannel "+Z+" with error "+J.message),this.outputStatus({code:yP1.Status.INTERNAL,details:"Failed to start HTTP/2 stream with error "+J.message,metadata:new kP1.Metadata},"NOT_STARTED");return}if((W=D.onCallStarted)===null||W===void 0||W.call(D),this.onCallEnded=D.onCallEnded,this.trace("Created child call ["+this.child.getCallNumber()+"]"),this.readPending)this.child.startRead();if(this.pendingMessage)this.child.sendMessageWithContext(this.pendingMessage.context,this.pendingMessage.message);if(this.pendingHalfClose)this.child.halfClose()},(Y)=>{let{code:W,details:J}=QX0.restrictControlPlaneStatusCode(typeof Y.code==="number"?Y.code:yP1.Status.UNKNOWN,`Getting metadata from plugin failed with error: ${Y.message}`);this.outputStatus({code:W,details:J,metadata:new kP1.Metadata},"PROCESSED")});break;case n31.PickResultType.DROP:let{code:F,details:I}=QX0.restrictControlPlaneStatusCode(D.status.code,D.status.details);setImmediate(()=>{this.outputStatus({code:F,details:I,metadata:D.status.metadata},"DROP")});break;case n31.PickResultType.TRANSIENT_FAILURE:if(this.metadata.getOptions().waitForReady)this.channel.queueCallForPick(this);else{let{code:Y,details:W}=QX0.restrictControlPlaneStatusCode(D.status.code,D.status.details);setImmediate(()=>{this.outputStatus({code:Y,details:W,metadata:D.status.metadata},"PROCESSED")})}break;case n31.PickResultType.QUEUE:this.channel.queueCallForPick(this)}}cancelWithStatus(A,B){var Q;this.trace("cancelWithStatus code: "+A+' details: "'+B+'"'),(Q=this.child)===null||Q===void 0||Q.cancelWithStatus(A,B),this.outputStatus({code:A,details:B,metadata:new kP1.Metadata},"PROCESSED")}getPeer(){var A,B;return(B=(A=this.child)===null||A===void 0?void 0:A.getPeer())!==null&&B!==void 0?B:this.channel.getTarget()}start(A,B){this.trace("start called"),this.listener=B,this.metadata=A,this.doPick()}sendMessageWithContext(A,B){if(this.trace("write() called with message of length "+B.length),this.child)this.child.sendMessageWithContext(A,B);else this.pendingMessage={context:A,message:B}}startRead(){if(this.trace("startRead called"),this.child)this.child.startRead();else this.readPending=!0}halfClose(){if(this.trace("halfClose called"),this.child)this.child.halfClose();else this.pendingHalfClose=!0}setCredentials(A){throw new Error("Method not implemented.")}getCallNumber(){return this.callNumber}}Kt2.LoadBalancingCall=Ct2});

// Export all variables
module.exports = {
  $I,
  $X0,
  A71,
  AX0,
  BM,
  Cr2,
  D1B,
  DP1,
  EE,
  EW0,
  F7,
  GJ,
  GJ0,
  GP1,
  HP1,
  IX0,
  Ia2,
  L1B,
  Ls2,
  MJ0,
  MX0,
  Na2,
  Ne2,
  Nr2,
  PW0,
  PX0,
  Ps2,
  QP1,
  Qt,
  Rr2,
  Rs2,
  TJ0,
  Tt2,
  U1B,
  UE,
  Ue2,
  VJ0,
  X1B,
  XJ0,
  XP1,
  YJ0,
  YP1,
  Yt2,
  Z$,
  ZP1,
  Za2,
  _6,
  _r2,
  aX0,
  bT1,
  be2,
  bo2,
  cJ0,
  cW0,
  dP1,
  dW0,
  do2,
  du,
  e31,
  eJ0,
  eo,
  f1B,
  gu,
  hP1,
  hV,
  i31,
  j1B,
  jP1,
  kT1,
  kr2,
  l1B,
  lJ0,
  le2,
  m1B,
  mr2,
  mu,
  nJ0,
  nT1,
  o_,
  or2,
  pJ0,
  pT1,
  pW0,
  po2,
  pu,
  q31,
  qs2,
  qt2,
  r1B,
  re2,
  s1B,
  s_,
  st2,
  tJ0,
  uW0,
  ur2,
  va2,
  w31,
  wo2,
  xP1,
  xW0,
  xr2,
  ya2,
  yr2,
  zJ0,
  zo2,
  zt2
};
