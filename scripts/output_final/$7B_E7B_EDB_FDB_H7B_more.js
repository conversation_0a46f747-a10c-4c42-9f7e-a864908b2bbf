// Package extracted with entry point: _x
// Contains 25 variables: $7B, E7B, EDB, FDB, H7B, L7B, R7B, S7B, T7B, WDB... (and 15 more)

var $7B=E((Me5,w7B)=>{var U7B=YJ(),tk6=(A,B,Q,D,Z)=>{if(typeof Q==="string")Z=D,D=Q,Q=void 0;try{return new U7B(A instanceof U7B?A.version:A,Q).inc(B,D,Z).version}catch(G){return null}};w7B.exports=tk6});
var E7B=E((Le5,z7B)=>{var rk6=Dm(),ok6=(A,B)=>{let Q=rk6(A.trim().replace(/^[=v]+/,""),B);return Q?Q.version:null};z7B.exports=ok6});
var EDB=E((pe5,zDB)=>{var VDB=TE(),$H0=R71(),{ANY:wH0}=$H0,xD1=gt(),qH0=OE(),c_6=(A,B,Q={})=>{if(A===B)return!0;A=new VDB(A,Q),B=new VDB(B,Q);let D=!1;A:for(let Z of A.set){for(let G of B.set){let F=p_6(Z,G,Q);if(D=D||F!==null,F)continue A}if(D)return!1}return!0},l_6=[new $H0(">=0.0.0-0")],CDB=[new $H0(">=0.0.0")],p_6=(A,B,Q)=>{if(A===B)return!0;if(A.length===1&&A[0].semver===wH0)if(B.length===1&&B[0].semver===wH0)return!0;else if(Q.includePrerelease)A=l_6;else A=CDB;if(B.length===1&&B[0].semver===wH0)if(Q.includePrerelease)return!0;else B=CDB;let D=new Set,Z,G;for(let C of A)if(C.operator===">"||C.operator===">=")Z=KDB(Z,C,Q);else if(C.operator==="<"||C.operator==="<=")G=HDB(G,C,Q);else D.add(C.semver);if(D.size>1)return null;let F;if(Z&&G){if(F=qH0(Z.semver,G.semver,Q),F>0)return null;else if(F===0&&(Z.operator!==">="||G.operator!=="<="))return null}for(let C of D){if(Z&&!xD1(C,String(Z),Q))return null;if(G&&!xD1(C,String(G),Q))return null;for(let K of B)if(!xD1(C,String(K),Q))return!1;return!0}let I,Y,W,J,X=G&&!Q.includePrerelease&&G.semver.prerelease.length?G.semver:!1,V=Z&&!Q.includePrerelease&&Z.semver.prerelease.length?Z.semver:!1;if(X&&X.prerelease.length===1&&G.operator==="<"&&X.prerelease[0]===0)X=!1;for(let C of B){if(J=J||C.operator===">"||C.operator===">=",W=W||C.operator==="<"||C.operator==="<=",Z){if(V){if(C.semver.prerelease&&C.semver.prerelease.length&&C.semver.major===V.major&&C.semver.minor===V.minor&&C.semver.patch===V.patch)V=!1}if(C.operator===">"||C.operator===">="){if(I=KDB(Z,C,Q),I===C&&I!==Z)return!1}else if(Z.operator===">="&&!xD1(Z.semver,String(C),Q))return!1}if(G){if(X){if(C.semver.prerelease&&C.semver.prerelease.length&&C.semver.major===X.major&&C.semver.minor===X.minor&&C.semver.patch===X.patch)X=!1}if(C.operator==="<"||C.operator==="<="){if(Y=HDB(G,C,Q),Y===C&&Y!==G)return!1}else if(G.operator==="<="&&!xD1(G.semver,String(C),Q))return!1}if(!C.operator&&(G||Z)&&F!==0)return!1}if(Z&&W&&!G&&F!==0)return!1;if(G&&J&&!Z&&F!==0)return!1;if(V||X)return!1;return!0},KDB=(A,B,Q)=>{if(!A)return B;let D=qH0(A.semver,B.semver,Q);return D>0?A:D<0?B:B.operator===">"&&A.operator===">="?B:A},HDB=(A,B,Q)=>{if(!A)return B;let D=qH0(A.semver,B.semver,Q);return D<0?A:D>0?B:B.operator==="<"&&A.operator==="<="?B:A};zDB.exports=c_6});
var FDB=E((de5,GDB)=>{var h_6=ky1(),g_6=(A,B,Q)=>h_6(A,B,"<",Q);GDB.exports=g_6});
var H7B=E((Ne5,K7B)=>{var ak6=Dm(),sk6=(A,B)=>{let Q=ak6(A,B);return Q?Q.version:null};K7B.exports=sk6});
var L7B=E((Re5,N7B)=>{var q7B=Dm(),ek6=(A,B)=>{let Q=q7B(A,null,!0),D=q7B(B,null,!0),Z=Q.compare(D);if(Z===0)return null;let G=Z>0,F=G?Q:D,I=G?D:Q,Y=!!F.prerelease.length;if(!!I.prerelease.length&&!Y){if(!I.patch&&!I.minor)return"major";if(I.compareMain(F)===0){if(I.minor&&!I.patch)return"minor";return"patch"}}let J=Y?"pre":"";if(Q.major!==D.major)return J+"major";if(Q.minor!==D.minor)return J+"minor";if(Q.patch!==D.patch)return J+"patch";return"prerelease"};N7B.exports=ek6});
var R7B=E((Oe5,M7B)=>{var A_6=YJ(),B_6=(A,B)=>new A_6(A,B).major;M7B.exports=B_6});
var S7B=E((Pe5,P7B)=>{var Z_6=YJ(),G_6=(A,B)=>new Z_6(A,B).patch;P7B.exports=G_6});
var T7B=E((Te5,O7B)=>{var Q_6=YJ(),D_6=(A,B)=>new Q_6(A,B).minor;O7B.exports=D_6});
var WDB=E((ce5,YDB)=>{var IDB=TE(),u_6=(A,B,Q)=>{return A=new IDB(A,Q),B=new IDB(B,Q),A.intersects(B,Q)};YDB.exports=u_6});
var XDB=E((le5,JDB)=>{var m_6=gt(),d_6=OE();JDB.exports=(A,B,Q)=>{let D=[],Z=null,G=null,F=A.sort((J,X)=>d_6(J,X,Q));for(let J of F)if(m_6(J,B,Q)){if(G=J,!Z)Z=J}else{if(G)D.push([Z,G]);G=null,Z=null}if(Z)D.push([Z,null]);let I=[];for(let[J,X]of D)if(J===X)I.push(J);else if(!X&&J===F[0])I.push("*");else if(!X)I.push(`>=${J}`);else if(J===F[0])I.push(`<=${X}`);else I.push(`${J} - ${X}`);let Y=I.join(" || "),W=typeof B.raw==="string"?B.raw:String(B);return Y.length<W.length?Y:B}});
var ZDB=E((me5,DDB)=>{var b_6=ky1(),f_6=(A,B,Q)=>b_6(A,B,">",Q);DDB.exports=f_6});
var _7B=E((je5,k7B)=>{var Y_6=OE(),W_6=(A,B,Q)=>Y_6(B,A,Q);k7B.exports=W_6});
var _x=E((ie5,$DB)=>{var NH0=ft(),UDB=q71(),i_6=YJ(),wDB=eV0(),n_6=Dm(),a_6=H7B(),s_6=E7B(),r_6=$7B(),o_6=L7B(),t_6=R7B(),e_6=T7B(),Ax6=S7B(),Bx6=y7B(),Qx6=OE(),Dx6=_7B(),Zx6=v7B(),Gx6=yy1(),Fx6=g7B(),Ix6=m7B(),Yx6=L71(),Wx6=pS1(),Jx6=BC0(),Xx6=QC0(),Vx6=N71(),Cx6=iS1(),Kx6=DC0(),Hx6=AC0(),zx6=R71(),Ex6=TE(),Ux6=gt(),wx6=c7B(),$x6=p7B(),qx6=n7B(),Nx6=r7B(),Lx6=t7B(),Mx6=ky1(),Rx6=ZDB(),Ox6=FDB(),Tx6=WDB(),Px6=XDB(),Sx6=EDB();$DB.exports={parse:n_6,valid:a_6,clean:s_6,inc:r_6,diff:o_6,major:t_6,minor:e_6,patch:Ax6,prerelease:Bx6,compare:Qx6,rcompare:Dx6,compareLoose:Zx6,compareBuild:Gx6,sort:Fx6,rsort:Ix6,gt:Yx6,lt:Wx6,eq:Jx6,neq:Xx6,gte:Vx6,lte:Cx6,cmp:Kx6,coerce:Hx6,Comparator:zx6,Range:Ex6,satisfies:Ux6,toComparators:wx6,maxSatisfying:$x6,minSatisfying:qx6,minVersion:Nx6,validRange:Lx6,outside:Mx6,gtr:Rx6,ltr:Ox6,intersects:Tx6,simplifyRange:Px6,subset:Sx6,SemVer:i_6,re:NH0.re,src:NH0.src,tokens:NH0.t,SEMVER_SPEC_VERSION:UDB.SEMVER_SPEC_VERSION,RELEASE_TYPES:UDB.RELEASE_TYPES,compareIdentifiers:wDB.compareIdentifiers,rcompareIdentifiers:wDB.rcompareIdentifiers}});
var c7B=E((ve5,d7B)=>{var E_6=TE(),U_6=(A,B)=>new E_6(A,B).set.map((Q)=>Q.map((D)=>D.value).join(" ").trim().split(" "));d7B.exports=U_6});
var g7B=E((_e5,h7B)=>{var C_6=yy1(),K_6=(A,B)=>A.sort((Q,D)=>C_6(Q,D,B));h7B.exports=K_6});
var ky1=E((ue5,QDB)=>{var S_6=YJ(),BDB=R71(),{ANY:j_6}=BDB,y_6=TE(),k_6=gt(),e7B=L71(),ADB=pS1(),__6=iS1(),x_6=N71(),v_6=(A,B,Q,D)=>{A=new S_6(A,D),B=new y_6(B,D);let Z,G,F,I,Y;switch(Q){case">":Z=e7B,G=__6,F=ADB,I=">",Y=">=";break;case"<":Z=ADB,G=x_6,F=e7B,I="<",Y="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(k_6(A,B,D))return!1;for(let W=0;W<B.set.length;++W){let J=B.set[W],X=null,V=null;if(J.forEach((C)=>{if(C.semver===j_6)C=new BDB(">=0.0.0");if(X=X||C,V=V||C,Z(C.semver,X.semver,D))X=C;else if(F(C.semver,V.semver,D))V=C}),X.operator===I||X.operator===Y)return!1;if((!V.operator||V.operator===I)&&G(A,V.semver))return!1;else if(V.operator===Y&&F(A,V.semver))return!1}return!0};QDB.exports=v_6});
var m7B=E((xe5,u7B)=>{var H_6=yy1(),z_6=(A,B)=>A.sort((Q,D)=>H_6(D,Q,B));u7B.exports=z_6});
var n7B=E((fe5,i7B)=>{var N_6=YJ(),L_6=TE(),M_6=(A,B,Q)=>{let D=null,Z=null,G=null;try{G=new L_6(B,Q)}catch(F){return null}return A.forEach((F)=>{if(G.test(F)){if(!D||Z.compare(F)===1)D=F,Z=new N_6(D,Q)}}),D};i7B.exports=M_6});
var p7B=E((be5,l7B)=>{var w_6=YJ(),$_6=TE(),q_6=(A,B,Q)=>{let D=null,Z=null,G=null;try{G=new $_6(B,Q)}catch(F){return null}return A.forEach((F)=>{if(G.test(F)){if(!D||Z.compare(F)===-1)D=F,Z=new w_6(D,Q)}}),D};l7B.exports=q_6});
var r7B=E((he5,s7B)=>{var UH0=YJ(),R_6=TE(),a7B=L71(),O_6=(A,B)=>{A=new R_6(A,B);let Q=new UH0("0.0.0");if(A.test(Q))return Q;if(Q=new UH0("0.0.0-0"),A.test(Q))return Q;Q=null;for(let D=0;D<A.set.length;++D){let Z=A.set[D],G=null;if(Z.forEach((F)=>{let I=new UH0(F.semver.version);switch(F.operator){case">":if(I.prerelease.length===0)I.patch++;else I.prerelease.push(0);I.raw=I.format();case"":case">=":if(!G||a7B(I,G))G=I;break;case"<":case"<=":break;default:throw new Error(`Unexpected operation: ${F.operator}`)}}),G&&(!Q||a7B(Q,G)))Q=G}if(Q&&A.test(Q))return Q;return null};s7B.exports=O_6});
var t7B=E((ge5,o7B)=>{var T_6=TE(),P_6=(A,B)=>{try{return new T_6(A,B).range||"*"}catch(Q){return null}};o7B.exports=P_6});
var v7B=E((ye5,x7B)=>{var J_6=OE(),X_6=(A,B)=>J_6(A,B,!0);x7B.exports=X_6});
var y7B=E((Se5,j7B)=>{var F_6=Dm(),I_6=(A,B)=>{let Q=F_6(A,B);return Q&&Q.prerelease.length?Q.prerelease:null};j7B.exports=I_6});
var yy1=E((ke5,f7B)=>{var b7B=YJ(),V_6=(A,B,Q)=>{let D=new b7B(A,Q),Z=new b7B(B,Q);return D.compare(Z)||D.compareBuild(Z)};f7B.exports=V_6});

// Export all variables
module.exports = {
  $7B,
  E7B,
  EDB,
  FDB,
  H7B,
  L7B,
  R7B,
  S7B,
  T7B,
  WDB,
  XDB,
  ZDB,
  _7B,
  _x,
  c7B,
  g7B,
  ky1,
  m7B,
  n7B,
  p7B,
  r7B,
  t7B,
  v7B,
  y7B,
  yy1
};
