// Package extracted with entry point: qQ0
// Contains 142 variables: $41, $Q0, $_A, $vA, AfA, AgA, AiA, BA0, BB0, BnA... (and 132 more)

var $41=E((AX5,qcA)=>{var{defineProperty:Lw1,getOwnPropertyDescriptor:PoQ,getOwnPropertyNames:SoQ}=Object,joQ=Object.prototype.hasOwnProperty,ET=(A,B)=>Lw1(A,"name",{value:B,configurable:!0}),yoQ=(A,B)=>{for(var Q in B)Lw1(A,Q,{get:B[Q],enumerable:!0})},koQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of SoQ(B))if(!joQ.call(A,Z)&&Z!==Q)Lw1(A,Z,{get:()=>B[Z],enumerable:!(D=PoQ(B,Z))||D.enumerable})}return A},_oQ=(A)=>koQ(Lw1({},"__esModule",{value:!0}),A),CcA={};yoQ(CcA,{DEFAULT_UA_APP_ID:()=>KcA,getUserAgentMiddlewareOptions:()=>$cA,getUserAgentPlugin:()=>moQ,resolveUserAgentConfig:()=>zcA,userAgentMiddleware:()=>wcA});qcA.exports=_oQ(CcA);var xoQ=CB(),KcA=void 0;function HcA(A){if(A===void 0)return!0;return typeof A==="string"&&A.length<=50}ET(HcA,"isValidUserAgentAppId");function zcA(A){let B=xoQ.normalizeProvider(A.userAgentAppId??KcA),{customUserAgent:Q}=A;return Object.assign(A,{customUserAgent:typeof Q==="string"?[[Q]]:Q,userAgentAppId:ET(async()=>{let D=await B();if(!HcA(D)){let Z=A.logger?.constructor?.name==="NoOpLogger"||!A.logger?console:A.logger;if(typeof D!=="string")Z?.warn("userAgentAppId must be a string or undefined.");else if(D.length>50)Z?.warn("The provided userAgentAppId exceeds the maximum length of 50 characters.")}return D},"userAgentAppId")})}ET(zcA,"resolveUserAgentConfig");var voQ=K41(),boQ=YK(),DL=UV(),foQ=/\d{12}\.ddb/;async function EcA(A,B,Q){if(Q.request?.headers?.["smithy-protocol"]==="rpc-v2-cbor")DL.setFeature(A,"PROTOCOL_RPC_V2_CBOR","M");if(typeof B.retryStrategy==="function"){let G=await B.retryStrategy();if(typeof G.acquireInitialRetryToken==="function")if(G.constructor?.name?.includes("Adaptive"))DL.setFeature(A,"RETRY_MODE_ADAPTIVE","F");else DL.setFeature(A,"RETRY_MODE_STANDARD","E");else DL.setFeature(A,"RETRY_MODE_LEGACY","D")}if(typeof B.accountIdEndpointMode==="function"){let G=A.endpointV2;if(String(G?.url?.hostname).match(foQ))DL.setFeature(A,"ACCOUNT_ID_ENDPOINT","O");switch(await B.accountIdEndpointMode?.()){case"disabled":DL.setFeature(A,"ACCOUNT_ID_MODE_DISABLED","Q");break;case"preferred":DL.setFeature(A,"ACCOUNT_ID_MODE_PREFERRED","P");break;case"required":DL.setFeature(A,"ACCOUNT_ID_MODE_REQUIRED","R");break}}let Z=A.__smithy_context?.selectedHttpAuthScheme?.identity;if(Z?.$source){let G=Z;if(G.accountId)DL.setFeature(A,"RESOLVED_ACCOUNT_ID","T");for(let[F,I]of Object.entries(G.$source??{}))DL.setFeature(A,F,I)}}ET(EcA,"checkFeatures");var JcA="user-agent",G90="x-amz-user-agent",XcA=" ",F90="/",hoQ=/[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w]/g,goQ=/[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w\#]/g,VcA="-",uoQ=1024;function UcA(A){let B="";for(let Q in A){let D=A[Q];if(B.length+D.length+1<=uoQ){if(B.length)B+=","+D;else B+=D;continue}break}return B}ET(UcA,"encodeFeatures");var wcA=ET((A)=>(B,Q)=>async(D)=>{let{request:Z}=D;if(!boQ.HttpRequest.isInstance(Z))return B(D);let{headers:G}=Z,F=Q?.userAgent?.map(Nw1)||[],I=(await A.defaultUserAgentProvider()).map(Nw1);await EcA(Q,A,D);let Y=Q;I.push(`m/${UcA(Object.assign({},Q.__smithy_context?.features,Y.__aws_sdk_context?.features))}`);let W=A?.customUserAgent?.map(Nw1)||[],J=await A.userAgentAppId();if(J)I.push(Nw1([`app/${J}`]));let X=voQ.getUserAgentPrefix(),V=(X?[X]:[]).concat([...I,...F,...W]).join(XcA),C=[...I.filter((K)=>K.startsWith("aws-sdk-")),...W].join(XcA);if(A.runtime!=="browser"){if(C)G[G90]=G[G90]?`${G[JcA]} ${C}`:C;G[JcA]=V}else G[G90]=V;return B({...D,request:Z})},"userAgentMiddleware"),Nw1=ET((A)=>{let B=A[0].split(F90).map((F)=>F.replace(hoQ,VcA)).join(F90),Q=A[1]?.replace(goQ,VcA),D=B.indexOf(F90),Z=B.substring(0,D),G=B.substring(D+1);if(Z==="api")G=G.toLowerCase();return[Z,G,Q].filter((F)=>F&&F.length>0).reduce((F,I,Y)=>{switch(Y){case 0:return I;case 1:return`${F}/${I}`;default:return`${F}#${I}`}},"")},"escapeUserAgent"),$cA={name:"getUserAgentMiddleware",step:"build",priority:"low",tags:["SET_USER_AGENT","USER_AGENT"],override:!0},moQ=ET((A)=>({applyToStack:ET((B)=>{B.add(wcA(A),$cA)},"applyToStack")}),"getUserAgentPlugin")});
var $Q0=E((OaA)=>{Object.defineProperty(OaA,"__esModule",{value:!0});OaA.fromNodeProviderChain=void 0;var IB4=RaA(),YB4=(A={})=>IB4.defaultProvider({...A});OaA.fromNodeProviderChain=YB4});
var $_A=E((U_A)=>{Object.defineProperty(U_A,"__esModule",{value:!0});U_A.checkUrl=void 0;var DhQ=Q9(),ZhQ="*************",GhQ="*************3",FhQ="[fd00:ec2::23]",IhQ=(A,B)=>{if(A.protocol==="https:")return;if(A.hostname===ZhQ||A.hostname===GhQ||A.hostname===FhQ)return;if(A.hostname.includes("[")){if(A.hostname==="[::1]"||A.hostname==="[0000:0000:0000:0000:0000:0000:0000:0001]")return}else{if(A.hostname==="localhost")return;let Q=A.hostname.split("."),D=(Z)=>{let G=parseInt(Z,10);return 0<=G&&G<=255};if(Q[0]==="127"&&D(Q[1])&&D(Q[2])&&D(Q[3])&&Q.length===4)return}throw new DhQ.CredentialsProviderError(`URL not accepted. It must either be HTTPS or match one of the following:
  - loopback CIDR *********/8 or [::1/128]
  - ECS container host *************
  - EKS container host *************3 or [fd00:ec2::23]`,{logger:B})};U_A.checkUrl=IhQ});
var $vA=E((UvA)=>{Object.defineProperty(UvA,"__esModule",{value:!0});UvA.defaultEndpointResolver=void 0;var EuQ=mn(),F20=S7(),UuQ=EvA(),wuQ=new F20.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),$uQ=(A,B={})=>{return wuQ.get(A,()=>F20.resolveEndpoint(UuQ.ruleSet,{endpointParams:A,logger:B.logger}))};UvA.defaultEndpointResolver=$uQ;F20.customEndpointFunctions.aws=EuQ.awsEndpointFunctions});
var AfA=E((tbA)=>{Object.defineProperty(tbA,"__esModule",{value:!0});tbA.defaultEndpointResolver=void 0;var IdQ=mn(),U20=S7(),YdQ=obA(),WdQ=new U20.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS","UseGlobalEndpoint"]}),JdQ=(A,B={})=>{return WdQ.get(A,()=>U20.resolveEndpoint(YdQ.ruleSet,{endpointParams:A,logger:B.logger}))};tbA.defaultEndpointResolver=JdQ;U20.customEndpointFunctions.aws=IdQ.awsEndpointFunctions});
var AgA=E((thA)=>{Object.defineProperty(thA,"__esModule",{value:!0});thA.getRuntimeConfig=void 0;var slQ=VI(),rlQ=CB(),olQ=V6(),tlQ=JZ(),rhA=Hk(),ohA=cB(),elQ=dA0(),ApQ=shA(),BpQ=(A)=>{return{apiVersion:"2014-06-30",base64Decoder:A?.base64Decoder??rhA.fromBase64,base64Encoder:A?.base64Encoder??rhA.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??ApQ.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??elQ.defaultCognitoIdentityHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new slQ.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new rlQ.NoAuthSigner}],logger:A?.logger??new olQ.NoOpLogger,serviceId:A?.serviceId??"Cognito Identity",urlParser:A?.urlParser??tlQ.parseUrl,utf8Decoder:A?.utf8Decoder??ohA.fromUtf8,utf8Encoder:A?.utf8Encoder??ohA.toUtf8}};thA.getRuntimeConfig=BpQ});
var AiA=E((tpA)=>{Object.defineProperty(tpA,"__esModule",{value:!0});tpA.getRuntimeConfig=void 0;var f14=uh(),h14=f14.__importDefault(NpA()),apA=UV(),spA=Ow1(),vw1=K4(),g14=gG(),rpA=u4(),Qg=JD(),opA=k3(),u14=uG(),m14=sZ(),d14=npA(),c14=w8(),l14=mG(),p14=w8(),i14=(A)=>{p14.emitWarningIfUnsupportedVersion(process.version);let B=l14.resolveDefaultsModeConfig(A),Q=()=>B().then(c14.loadConfigsForDefaultMode),D=d14.getRuntimeConfig(A);apA.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile,logger:D.logger};return{...D,...A,runtime:"node",defaultsMode:B,authSchemePreference:A?.authSchemePreference??Qg.loadConfig(apA.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS,Z),bodyLengthChecker:A?.bodyLengthChecker??u14.calculateBodyLength,defaultUserAgentProvider:A?.defaultUserAgentProvider??spA.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:h14.default.version}),maxAttempts:A?.maxAttempts??Qg.loadConfig(rpA.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??Qg.loadConfig(vw1.NODE_REGION_CONFIG_OPTIONS,{...vw1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:opA.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??Qg.loadConfig({...rpA.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||m14.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??g14.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??opA.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??Qg.loadConfig(vw1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??Qg.loadConfig(vw1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??Qg.loadConfig(spA.NODE_APP_ID_CONFIG_OPTIONS,Z)}};tpA.getRuntimeConfig=i14});
var BA0=E((MI5,HjA)=>{var{defineProperty:gE1,getOwnPropertyDescriptor:h_Q,getOwnPropertyNames:g_Q}=Object,u_Q=Object.prototype.hasOwnProperty,uE1=(A,B)=>gE1(A,"name",{value:B,configurable:!0}),m_Q=(A,B)=>{for(var Q in B)gE1(A,Q,{get:B[Q],enumerable:!0})},d_Q=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of g_Q(B))if(!u_Q.call(A,Z)&&Z!==Q)gE1(A,Z,{get:()=>B[Z],enumerable:!(D=h_Q(B,Z))||D.enumerable})}return A},c_Q=(A)=>d_Q(gE1({},"__esModule",{value:!0}),A),IjA={};m_Q(IjA,{AlgorithmId:()=>XjA,EndpointURLScheme:()=>JjA,FieldPosition:()=>VjA,HttpApiKeyAuthLocation:()=>WjA,HttpAuthLocation:()=>YjA,IniSectionType:()=>CjA,RequestHandlerProtocol:()=>KjA,SMITHY_CONTEXT_KEY:()=>a_Q,getDefaultClientConfiguration:()=>i_Q,resolveDefaultRuntimeConfig:()=>n_Q});HjA.exports=c_Q(IjA);var YjA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(YjA||{}),WjA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(WjA||{}),JjA=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(JjA||{}),XjA=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(XjA||{}),l_Q=uE1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),p_Q=uE1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),i_Q=uE1((A)=>{return l_Q(A)},"getDefaultClientConfiguration"),n_Q=uE1((A)=>{return p_Q(A)},"resolveDefaultRuntimeConfig"),VjA=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(VjA||{}),a_Q="__smithy_context",CjA=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(CjA||{}),KjA=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(KjA||{})});
var BB0=E((CW5,IhA)=>{var{defineProperty:bU1,getOwnPropertyDescriptor:dcQ,getOwnPropertyNames:ccQ}=Object,lcQ=Object.prototype.hasOwnProperty,AB0=(A,B)=>bU1(A,"name",{value:B,configurable:!0}),pcQ=(A,B)=>{for(var Q in B)bU1(A,Q,{get:B[Q],enumerable:!0})},icQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of ccQ(B))if(!lcQ.call(A,Z)&&Z!==Q)bU1(A,Z,{get:()=>B[Z],enumerable:!(D=dcQ(B,Z))||D.enumerable})}return A},ncQ=(A)=>icQ(bU1({},"__esModule",{value:!0}),A),FhA={};pcQ(FhA,{fromProcess:()=>ecQ});IhA.exports=ncQ(FhA);var GhA=D3(),e20=Q9(),acQ=J1("child_process"),scQ=J1("util"),rcQ=Sw(),ocQ=AB0((A,B,Q)=>{if(B.Version!==1)throw Error(`Profile ${A} credential_process did not return Version 1.`);if(B.AccessKeyId===void 0||B.SecretAccessKey===void 0)throw Error(`Profile ${A} credential_process returned invalid credentials.`);if(B.Expiration){let G=new Date;if(new Date(B.Expiration)<G)throw Error(`Profile ${A} credential_process returned expired credentials.`)}let D=B.AccountId;if(!D&&Q?.[A]?.aws_account_id)D=Q[A].aws_account_id;let Z={accessKeyId:B.AccessKeyId,secretAccessKey:B.SecretAccessKey,...B.SessionToken&&{sessionToken:B.SessionToken},...B.Expiration&&{expiration:new Date(B.Expiration)},...B.CredentialScope&&{credentialScope:B.CredentialScope},...D&&{accountId:D}};return rcQ.setCredentialFeature(Z,"CREDENTIALS_PROCESS","w"),Z},"getValidatedProcessCredentials"),tcQ=AB0(async(A,B,Q)=>{let D=B[A];if(B[A]){let Z=D.credential_process;if(Z!==void 0){let G=scQ.promisify(acQ.exec);try{let{stdout:F}=await G(Z),I;try{I=JSON.parse(F.trim())}catch{throw Error(`Profile ${A} credential_process returned invalid JSON.`)}return ocQ(A,I,B)}catch(F){throw new e20.CredentialsProviderError(F.message,{logger:Q})}}else throw new e20.CredentialsProviderError(`Profile ${A} did not contain credential_process.`,{logger:Q})}else throw new e20.CredentialsProviderError(`Profile ${A} could not be found in shared credentials file.`,{logger:Q})},"resolveProcessCredentials"),ecQ=AB0((A={})=>async({callerClientConfig:B}={})=>{A.logger?.debug("@aws-sdk/credential-provider-process - fromProcess");let Q=await GhA.parseKnownFiles(A);return tcQ(GhA.getProfileName({profile:A.profile??B?.profile}),Q,A.logger)},"fromProcess")});
var BnA=E((eiA)=>{Object.defineProperty(eiA,"__esModule",{value:!0});eiA.getRuntimeConfig=void 0;var g04=UV(),u04=CB(),m04=w8(),d04=JZ(),oiA=th(),tiA=cB(),c04=ZQ0(),l04=riA(),p04=(A)=>{return{apiVersion:"2019-06-10",base64Decoder:A?.base64Decoder??oiA.fromBase64,base64Encoder:A?.base64Encoder??oiA.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??l04.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??c04.defaultSSOOIDCHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new g04.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new u04.NoAuthSigner}],logger:A?.logger??new m04.NoOpLogger,serviceId:A?.serviceId??"SSO OIDC",urlParser:A?.urlParser??d04.parseUrl,utf8Decoder:A?.utf8Decoder??tiA.fromUtf8,utf8Encoder:A?.utf8Encoder??tiA.toUtf8}};eiA.getRuntimeConfig=p04});
var CQ0=E((FL)=>{var V24=FL&&FL.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),C24=FL&&FL.__setModuleDefault||(Object.create?function(A,B){Object.defineProperty(A,"default",{enumerable:!0,value:B})}:function(A,B){A.default=B}),K24=FL&&FL.__importStar||function(){var A=function(B){return A=Object.getOwnPropertyNames||function(Q){var D=[];for(var Z in Q)if(Object.prototype.hasOwnProperty.call(Q,Z))D[D.length]=Z;return D},A(B)};return function(B){if(B&&B.__esModule)return B;var Q={};if(B!=null){for(var D=A(B),Z=0;Z<D.length;Z++)if(D[Z]!=="default")V24(Q,B,D[Z])}return C24(Q,B),Q}}();Object.defineProperty(FL,"__esModule",{value:!0});FL.fromWebToken=void 0;var H24=(A)=>async(B)=>{A.logger?.debug("@aws-sdk/credential-provider-web-identity - fromWebToken");let{roleArn:Q,roleSessionName:D,webIdentityToken:Z,providerId:G,policyArns:F,policy:I,durationSeconds:Y}=A,{roleAssumerWithWebIdentity:W}=A;if(!W){let{getDefaultRoleAssumerWithWebIdentity:J}=await Promise.resolve().then(()=>K24(yw1()));W=J({...A.clientConfig,credentialProviderLogger:A.logger,parentClientConfig:{...B?.callerClientConfig,...A.parentClientConfig}},A.clientPlugins)}return W({RoleArn:Q,RoleSessionName:D??`aws-sdk-js-session-${Date.now()}`,WebIdentityToken:Z,ProviderId:G,PolicyArns:F,Policy:I,DurationSeconds:Y})};FL.fromWebToken=H24});
var CbA=E((oY5,VbA)=>{var{create:NmQ,defineProperty:Z41,getOwnPropertyDescriptor:LmQ,getOwnPropertyNames:MmQ,getPrototypeOf:RmQ}=Object,OmQ=Object.prototype.hasOwnProperty,VT=(A,B)=>Z41(A,"name",{value:B,configurable:!0}),TmQ=(A,B)=>{for(var Q in B)Z41(A,Q,{get:B[Q],enumerable:!0})},YbA=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of MmQ(B))if(!OmQ.call(A,Z)&&Z!==Q)Z41(A,Z,{get:()=>B[Z],enumerable:!(D=LmQ(B,Z))||D.enumerable})}return A},WbA=(A,B,Q)=>(Q=A!=null?NmQ(RmQ(A)):{},YbA(B||!A||!A.__esModule?Z41(Q,"default",{value:A,enumerable:!0}):Q,A)),PmQ=(A)=>YbA(Z41({},"__esModule",{value:!0}),A),JbA={};TmQ(JbA,{fromEnvSigningName:()=>ymQ,fromSso:()=>XbA,fromStatic:()=>hmQ,nodeProvider:()=>gmQ});VbA.exports=PmQ(JbA);var SmQ=Sw(),jmQ=wA0(),ZK=Q9(),ymQ=VT(({logger:A,signingName:B}={})=>async()=>{if(A?.debug?.("@aws-sdk/token-providers - fromEnvSigningName"),!B)throw new ZK.TokenProviderError("Please pass 'signingName' to compute environment variable key",{logger:A});let Q=jmQ.getBearerTokenEnvKey(B);if(!(Q in process.env))throw new ZK.TokenProviderError(`Token not present in '${Q}' environment variable`,{logger:A});let D={token:process.env[Q]};return SmQ.setTokenFeature(D,"BEARER_SERVICE_ENV_VARS","3"),D},"fromEnvSigningName"),kmQ=300000,J20="To refresh this SSO session run 'aws sso login' with the corresponding profile.",_mQ=VT(async(A,B={})=>{let{SSOOIDCClient:Q}=await Promise.resolve().then(()=>WbA(W20()));return new Q(Object.assign({},B.clientConfig??{},{region:A??B.clientConfig?.region,logger:B.clientConfig?.logger??B.parentClientConfig?.logger}))},"getSsoOidcClient"),xmQ=VT(async(A,B,Q={})=>{let{CreateTokenCommand:D}=await Promise.resolve().then(()=>WbA(W20()));return(await _mQ(B,Q)).send(new D({clientId:A.clientId,clientSecret:A.clientSecret,refreshToken:A.refreshToken,grantType:"refresh_token"}))},"getNewSsoOidcToken"),FbA=VT((A)=>{if(A.expiration&&A.expiration.getTime()<Date.now())throw new ZK.TokenProviderError(`Token is expired. ${J20}`,!1)},"validateTokenExpiry"),nh=VT((A,B,Q=!1)=>{if(typeof B==="undefined")throw new ZK.TokenProviderError(`Value not present for '${A}' in SSO Token${Q?". Cannot refresh":""}. ${J20}`,!1)},"validateTokenKey"),D41=D3(),vmQ=J1("fs"),{writeFile:bmQ}=vmQ.promises,fmQ=VT((A,B)=>{let Q=D41.getSSOTokenFilepath(A),D=JSON.stringify(B,null,2);return bmQ(Q,D)},"writeSSOTokenToFile"),IbA=new Date(0),XbA=VT((A={})=>async({callerClientConfig:B}={})=>{let Q={...A,parentClientConfig:{...B,...A.parentClientConfig}};Q.logger?.debug("@aws-sdk/token-providers - fromSso");let D=await D41.parseKnownFiles(Q),Z=D41.getProfileName({profile:Q.profile??B?.profile}),G=D[Z];if(!G)throw new ZK.TokenProviderError(`Profile '${Z}' could not be found in shared credentials file.`,!1);else if(!G.sso_session)throw new ZK.TokenProviderError(`Profile '${Z}' is missing required property 'sso_session'.`);let F=G.sso_session,Y=(await D41.loadSsoSessionData(Q))[F];if(!Y)throw new ZK.TokenProviderError(`Sso session '${F}' could not be found in shared credentials file.`,!1);for(let H of["sso_start_url","sso_region"])if(!Y[H])throw new ZK.TokenProviderError(`Sso session '${F}' is missing required property '${H}'.`,!1);let{sso_start_url:W,sso_region:J}=Y,X;try{X=await D41.getSSOTokenFromFile(F)}catch(H){throw new ZK.TokenProviderError(`The SSO session token associated with profile=${Z} was not found or is invalid. ${J20}`,!1)}nh("accessToken",X.accessToken),nh("expiresAt",X.expiresAt);let{accessToken:V,expiresAt:C}=X,K={token:V,expiration:new Date(C)};if(K.expiration.getTime()-Date.now()>kmQ)return K;if(Date.now()-IbA.getTime()<30000)return FbA(K),K;nh("clientId",X.clientId,!0),nh("clientSecret",X.clientSecret,!0),nh("refreshToken",X.refreshToken,!0);try{IbA.setTime(Date.now());let H=await xmQ(X,J,Q);nh("accessToken",H.accessToken),nh("expiresIn",H.expiresIn);let z=new Date(Date.now()+H.expiresIn*1000);try{await fmQ(F,{...X,accessToken:H.accessToken,expiresAt:z.toISOString(),refreshToken:H.refreshToken})}catch($){}return{token:H.accessToken,expiration:z}}catch(H){return FbA(K),K}},"fromSso"),hmQ=VT(({token:A,logger:B})=>async()=>{if(B?.debug("@aws-sdk/token-providers - fromStatic"),!A||!A.token)throw new ZK.TokenProviderError("Please pass a valid token to fromStatic",!1);return A},"fromStatic"),gmQ=VT((A={})=>ZK.memoize(ZK.chain(XbA(A),async()=>{throw new ZK.TokenProviderError("Could not load token from any providers",!1)}),(B)=>B.expiration!==void 0&&B.expiration.getTime()-Date.now()<300000,(B)=>B.expiration!==void 0),"nodeProvider")});
var D20=E((dY5,HuQ)=>{HuQ.exports={name:"@aws-sdk/nested-clients",version:"3.840.0",description:"Nested clients for AWS SDK packages.",main:"./dist-cjs/index.js",module:"./dist-es/index.js",types:"./dist-types/index.d.ts",scripts:{build:"yarn lint && concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'","build:cjs":"node ../../scripts/compilation/inline nested-clients","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4",clean:"rimraf ./dist-* && rimraf *.tsbuildinfo",lint:"node ../../scripts/validation/submodules-linter.js --pkg nested-clients",test:"yarn g:vitest run","test:watch":"yarn g:vitest watch"},engines:{node:">=18.0.0"},author:{name:"AWS SDK for JavaScript Team",url:"https://aws.amazon.com/javascript/"},license:"Apache-2.0",dependencies:{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.840.0","@aws-sdk/middleware-host-header":"3.840.0","@aws-sdk/middleware-logger":"3.840.0","@aws-sdk/middleware-recursion-detection":"3.840.0","@aws-sdk/middleware-user-agent":"3.840.0","@aws-sdk/region-config-resolver":"3.840.0","@aws-sdk/types":"3.840.0","@aws-sdk/util-endpoints":"3.840.0","@aws-sdk/util-user-agent-browser":"3.840.0","@aws-sdk/util-user-agent-node":"3.840.0","@smithy/config-resolver":"^4.1.4","@smithy/core":"^3.6.0","@smithy/fetch-http-handler":"^5.0.4","@smithy/hash-node":"^4.0.4","@smithy/invalid-dependency":"^4.0.4","@smithy/middleware-content-length":"^4.0.4","@smithy/middleware-endpoint":"^4.1.13","@smithy/middleware-retry":"^4.1.14","@smithy/middleware-serde":"^4.0.8","@smithy/middleware-stack":"^4.0.4","@smithy/node-config-provider":"^4.1.3","@smithy/node-http-handler":"^4.0.6","@smithy/protocol-http":"^5.1.2","@smithy/smithy-client":"^4.4.5","@smithy/types":"^4.3.1","@smithy/url-parser":"^4.0.4","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.21","@smithy/util-defaults-mode-node":"^4.0.21","@smithy/util-endpoints":"^3.0.6","@smithy/util-middleware":"^4.0.4","@smithy/util-retry":"^4.0.6","@smithy/util-utf8":"^4.0.0",tslib:"^2.6.2"},devDependencies:{concurrently:"7.0.0","downlevel-dts":"0.10.1",rimraf:"3.0.2",typescript:"~5.8.3"},typesVersions:{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},files:["./sso-oidc.d.ts","./sso-oidc.js","./sts.d.ts","./sts.js","dist-*/**"],browser:{"./dist-es/submodules/sso-oidc/runtimeConfig":"./dist-es/submodules/sso-oidc/runtimeConfig.browser","./dist-es/submodules/sts/runtimeConfig":"./dist-es/submodules/sts/runtimeConfig.browser"},"react-native":{},homepage:"https://github.com/aws/aws-sdk-js-v3/tree/main/packages/nested-clients",repository:{type:"git",url:"https://github.com/aws/aws-sdk-js-v3.git",directory:"packages/nested-clients"},exports:{"./sso-oidc":{types:"./dist-types/submodules/sso-oidc/index.d.ts",module:"./dist-es/submodules/sso-oidc/index.js",node:"./dist-cjs/submodules/sso-oidc/index.js",import:"./dist-es/submodules/sso-oidc/index.js",require:"./dist-cjs/submodules/sso-oidc/index.js"},"./sts":{types:"./dist-types/submodules/sts/index.d.ts",module:"./dist-es/submodules/sts/index.js",node:"./dist-cjs/submodules/sts/index.js",import:"./dist-es/submodules/sts/index.js",require:"./dist-cjs/submodules/sts/index.js"}}}});
var DkA=E((fI5,QkA)=>{var{defineProperty:IU1,getOwnPropertyDescriptor:XvQ,getOwnPropertyNames:VvQ}=Object,CvQ=Object.prototype.hasOwnProperty,vY=(A,B)=>IU1(A,"name",{value:B,configurable:!0}),KvQ=(A,B)=>{for(var Q in B)IU1(A,Q,{get:B[Q],enumerable:!0})},HvQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of VvQ(B))if(!CvQ.call(A,Z)&&Z!==Q)IU1(A,Z,{get:()=>B[Z],enumerable:!(D=XvQ(B,Z))||D.enumerable})}return A},zvQ=(A)=>HvQ(IU1({},"__esModule",{value:!0}),A),xyA={};KvQ(xyA,{ALGORITHM_IDENTIFIER:()=>BU1,ALGORITHM_IDENTIFIER_V4A:()=>$vQ,ALGORITHM_QUERY_PARAM:()=>vyA,ALWAYS_UNSIGNABLE_HEADERS:()=>cyA,AMZ_DATE_HEADER:()=>KA0,AMZ_DATE_QUERY_PARAM:()=>JA0,AUTH_HEADER:()=>CA0,CREDENTIAL_QUERY_PARAM:()=>byA,DATE_HEADER:()=>gyA,EVENT_ALGORITHM_IDENTIFIER:()=>iyA,EXPIRES_QUERY_PARAM:()=>hyA,GENERATED_HEADERS:()=>uyA,HOST_HEADER:()=>UvQ,KEY_TYPE_IDENTIFIER:()=>HA0,MAX_CACHE_SIZE:()=>ayA,MAX_PRESIGNED_TTL:()=>syA,PROXY_HEADER_PATTERN:()=>lyA,REGION_SET_PARAM:()=>EvQ,SEC_HEADER_PATTERN:()=>pyA,SHA256_HEADER:()=>FU1,SIGNATURE_HEADER:()=>myA,SIGNATURE_QUERY_PARAM:()=>XA0,SIGNED_HEADERS_QUERY_PARAM:()=>fyA,SignatureV4:()=>yvQ,SignatureV4Base:()=>BkA,TOKEN_HEADER:()=>dyA,TOKEN_QUERY_PARAM:()=>VA0,UNSIGNABLE_PATTERNS:()=>wvQ,UNSIGNED_PAYLOAD:()=>nyA,clearCredentialCache:()=>NvQ,createScope:()=>DU1,getCanonicalHeaders:()=>IA0,getCanonicalQuery:()=>AkA,getPayloadHash:()=>ZU1,getSigningKey:()=>ryA,hasHeader:()=>oyA,moveHeadersToQuery:()=>eyA,prepareRequest:()=>WA0,signatureV4aContainer:()=>kvQ});QkA.exports=zvQ(xyA);var jyA=cB(),vyA="X-Amz-Algorithm",byA="X-Amz-Credential",JA0="X-Amz-Date",fyA="X-Amz-SignedHeaders",hyA="X-Amz-Expires",XA0="X-Amz-Signature",VA0="X-Amz-Security-Token",EvQ="X-Amz-Region-Set",CA0="authorization",KA0=JA0.toLowerCase(),gyA="date",uyA=[CA0,KA0,gyA],myA=XA0.toLowerCase(),FU1="x-amz-content-sha256",dyA=VA0.toLowerCase(),UvQ="host",cyA={authorization:!0,"cache-control":!0,connection:!0,expect:!0,from:!0,"keep-alive":!0,"max-forwards":!0,pragma:!0,referer:!0,te:!0,trailer:!0,"transfer-encoding":!0,upgrade:!0,"user-agent":!0,"x-amzn-trace-id":!0},lyA=/^proxy-/,pyA=/^sec-/,wvQ=[/^proxy-/i,/^sec-/i],BU1="AWS4-HMAC-SHA256",$vQ="AWS4-ECDSA-P256-SHA256",iyA="AWS4-HMAC-SHA256-PAYLOAD",nyA="UNSIGNED-PAYLOAD",ayA=50,HA0="aws4_request",syA=604800,Kk=ay(),qvQ=cB(),cn={},QU1=[],DU1=vY((A,B,Q)=>`${A}/${B}/${Q}/${HA0}`,"createScope"),ryA=vY(async(A,B,Q,D,Z)=>{let G=await yyA(A,B.secretAccessKey,B.accessKeyId),F=`${Q}:${D}:${Z}:${Kk.toHex(G)}:${B.sessionToken}`;if(F in cn)return cn[F];QU1.push(F);while(QU1.length>ayA)delete cn[QU1.shift()];let I=`AWS4${B.secretAccessKey}`;for(let Y of[Q,D,Z,HA0])I=await yyA(A,I,Y);return cn[F]=I},"getSigningKey"),NvQ=vY(()=>{QU1.length=0,Object.keys(cn).forEach((A)=>{delete cn[A]})},"clearCredentialCache"),yyA=vY((A,B,Q)=>{let D=new A(B);return D.update(qvQ.toUint8Array(Q)),D.digest()},"hmac"),IA0=vY(({headers:A},B,Q)=>{let D={};for(let Z of Object.keys(A).sort()){if(A[Z]==null)continue;let G=Z.toLowerCase();if(G in cyA||B?.has(G)||lyA.test(G)||pyA.test(G)){if(!Q||Q&&!Q.has(G))continue}D[G]=A[Z].trim().replace(/\s+/g," ")}return D},"getCanonicalHeaders"),LvQ=RyA(),MvQ=cB(),ZU1=vY(async({headers:A,body:B},Q)=>{for(let D of Object.keys(A))if(D.toLowerCase()===FU1)return A[D];if(B==null)return"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855";else if(typeof B==="string"||ArrayBuffer.isView(B)||LvQ.isArrayBuffer(B)){let D=new Q;return D.update(MvQ.toUint8Array(B)),Kk.toHex(await D.digest())}return nyA},"getPayloadHash"),kyA=cB(),RvQ=class{static{vY(this,"HeaderFormatter")}format(A){let B=[];for(let Z of Object.keys(A)){let G=kyA.fromUtf8(Z);B.push(Uint8Array.from([G.byteLength]),G,this.formatHeaderValue(A[Z]))}let Q=new Uint8Array(B.reduce((Z,G)=>Z+G.byteLength,0)),D=0;for(let Z of B)Q.set(Z,D),D+=Z.byteLength;return Q}formatHeaderValue(A){switch(A.type){case"boolean":return Uint8Array.from([A.value?0:1]);case"byte":return Uint8Array.from([2,A.value]);case"short":let B=new DataView(new ArrayBuffer(3));return B.setUint8(0,3),B.setInt16(1,A.value,!1),new Uint8Array(B.buffer);case"integer":let Q=new DataView(new ArrayBuffer(5));return Q.setUint8(0,4),Q.setInt32(1,A.value,!1),new Uint8Array(Q.buffer);case"long":let D=new Uint8Array(9);return D[0]=5,D.set(A.value.bytes,1),D;case"binary":let Z=new DataView(new ArrayBuffer(3+A.value.byteLength));Z.setUint8(0,6),Z.setUint16(1,A.value.byteLength,!1);let G=new Uint8Array(Z.buffer);return G.set(A.value,3),G;case"string":let F=kyA.fromUtf8(A.value),I=new DataView(new ArrayBuffer(3+F.byteLength));I.setUint8(0,7),I.setUint16(1,F.byteLength,!1);let Y=new Uint8Array(I.buffer);return Y.set(F,3),Y;case"timestamp":let W=new Uint8Array(9);return W[0]=8,W.set(TvQ.fromNumber(A.value.valueOf()).bytes,1),W;case"uuid":if(!OvQ.test(A.value))throw new Error(`Invalid UUID received: ${A.value}`);let J=new Uint8Array(17);return J[0]=9,J.set(Kk.fromHex(A.value.replace(/\-/g,"")),1),J}}},OvQ=/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/,TvQ=class A{constructor(B){if(this.bytes=B,B.byteLength!==8)throw new Error("Int64 buffers must be exactly 8 bytes")}static{vY(this,"Int64")}static fromNumber(B){if(B>9223372036854776000||B<-9223372036854776000)throw new Error(`${B} is too large (or, if negative, too small) to represent as an Int64`);let Q=new Uint8Array(8);for(let D=7,Z=Math.abs(Math.round(B));D>-1&&Z>0;D--,Z/=256)Q[D]=Z;if(B<0)YA0(Q);return new A(Q)}valueOf(){let B=this.bytes.slice(0),Q=B[0]&128;if(Q)YA0(B);return parseInt(Kk.toHex(B),16)*(Q?-1:1)}toString(){return String(this.valueOf())}};function YA0(A){for(let B=0;B<8;B++)A[B]^=255;for(let B=7;B>-1;B--)if(A[B]++,A[B]!==0)break}vY(YA0,"negate");var oyA=vY((A,B)=>{A=A.toLowerCase();for(let Q of Object.keys(B))if(A===Q.toLowerCase())return!0;return!1},"hasHeader"),tyA=EV(),eyA=vY((A,B={})=>{let{headers:Q,query:D={}}=tyA.HttpRequest.clone(A);for(let Z of Object.keys(Q)){let G=Z.toLowerCase();if(G.slice(0,6)==="x-amz-"&&!B.unhoistableHeaders?.has(G)||B.hoistableHeaders?.has(G))D[Z]=Q[Z],delete Q[Z]}return{...A,headers:Q,query:D}},"moveHeadersToQuery"),WA0=vY((A)=>{A=tyA.HttpRequest.clone(A);for(let B of Object.keys(A.headers))if(uyA.indexOf(B.toLowerCase())>-1)delete A.headers[B];return A},"prepareRequest"),_yA=J5(),PvQ=cB(),GU1=SyA(),AkA=vY(({query:A={}})=>{let B=[],Q={};for(let D of Object.keys(A)){if(D.toLowerCase()===myA)continue;let Z=GU1.escapeUri(D);B.push(Z);let G=A[D];if(typeof G==="string")Q[Z]=`${Z}=${GU1.escapeUri(G)}`;else if(Array.isArray(G))Q[Z]=G.slice(0).reduce((F,I)=>F.concat([`${Z}=${GU1.escapeUri(I)}`]),[]).sort().join("&")}return B.sort().map((D)=>Q[D]).filter((D)=>D).join("&")},"getCanonicalQuery"),SvQ=vY((A)=>jvQ(A).toISOString().replace(/\.\d{3}Z$/,"Z"),"iso8601"),jvQ=vY((A)=>{if(typeof A==="number")return new Date(A*1000);if(typeof A==="string"){if(Number(A))return new Date(Number(A)*1000);return new Date(A)}return A},"toDate"),BkA=class{static{vY(this,"SignatureV4Base")}constructor({applyChecksum:A,credentials:B,region:Q,service:D,sha256:Z,uriEscapePath:G=!0}){this.service=D,this.sha256=Z,this.uriEscapePath=G,this.applyChecksum=typeof A==="boolean"?A:!0,this.regionProvider=_yA.normalizeProvider(Q),this.credentialProvider=_yA.normalizeProvider(B)}createCanonicalRequest(A,B,Q){let D=Object.keys(B).sort();return`${A.method}
${this.getCanonicalPath(A)}
${AkA(A)}
${D.map((Z)=>`${Z}:${B[Z]}`).join(`
`)}

${D.join(";")}
${Q}`}async createStringToSign(A,B,Q,D){let Z=new this.sha256;Z.update(PvQ.toUint8Array(Q));let G=await Z.digest();return`${D}
${A}
${B}
${Kk.toHex(G)}`}getCanonicalPath({path:A}){if(this.uriEscapePath){let B=[];for(let Z of A.split("/")){if(Z?.length===0)continue;if(Z===".")continue;if(Z==="..")B.pop();else B.push(Z)}let Q=`${A?.startsWith("/")?"/":""}${B.join("/")}${B.length>0&&A?.endsWith("/")?"/":""}`;return GU1.escapeUri(Q).replace(/%2F/g,"/")}return A}validateResolvedCredentials(A){if(typeof A!=="object"||typeof A.accessKeyId!=="string"||typeof A.secretAccessKey!=="string")throw new Error("Resolved credential object is not valid")}formatDate(A){let B=SvQ(A).replace(/[\-:]/g,"");return{longDate:B,shortDate:B.slice(0,8)}}getCanonicalHeaderList(A){return Object.keys(A).sort().join(";")}},yvQ=class extends BkA{constructor({applyChecksum:A,credentials:B,region:Q,service:D,sha256:Z,uriEscapePath:G=!0}){super({applyChecksum:A,credentials:B,region:Q,service:D,sha256:Z,uriEscapePath:G});this.headerFormatter=new RvQ}static{vY(this,"SignatureV4")}async presign(A,B={}){let{signingDate:Q=new Date,expiresIn:D=3600,unsignableHeaders:Z,unhoistableHeaders:G,signableHeaders:F,hoistableHeaders:I,signingRegion:Y,signingService:W}=B,J=await this.credentialProvider();this.validateResolvedCredentials(J);let X=Y??await this.regionProvider(),{longDate:V,shortDate:C}=this.formatDate(Q);if(D>syA)return Promise.reject("Signature version 4 presigned URLs must have an expiration date less than one week in the future");let K=DU1(C,X,W??this.service),H=eyA(WA0(A),{unhoistableHeaders:G,hoistableHeaders:I});if(J.sessionToken)H.query[VA0]=J.sessionToken;H.query[vyA]=BU1,H.query[byA]=`${J.accessKeyId}/${K}`,H.query[JA0]=V,H.query[hyA]=D.toString(10);let z=IA0(H,Z,F);return H.query[fyA]=this.getCanonicalHeaderList(z),H.query[XA0]=await this.getSignature(V,K,this.getSigningKey(J,X,C,W),this.createCanonicalRequest(H,z,await ZU1(A,this.sha256))),H}async sign(A,B){if(typeof A==="string")return this.signString(A,B);else if(A.headers&&A.payload)return this.signEvent(A,B);else if(A.message)return this.signMessage(A,B);else return this.signRequest(A,B)}async signEvent({headers:A,payload:B},{signingDate:Q=new Date,priorSignature:D,signingRegion:Z,signingService:G}){let F=Z??await this.regionProvider(),{shortDate:I,longDate:Y}=this.formatDate(Q),W=DU1(I,F,G??this.service),J=await ZU1({headers:{},body:B},this.sha256),X=new this.sha256;X.update(A);let V=Kk.toHex(await X.digest()),C=[iyA,Y,W,D,V,J].join(`
`);return this.signString(C,{signingDate:Q,signingRegion:F,signingService:G})}async signMessage(A,{signingDate:B=new Date,signingRegion:Q,signingService:D}){return this.signEvent({headers:this.headerFormatter.format(A.message.headers),payload:A.message.body},{signingDate:B,signingRegion:Q,signingService:D,priorSignature:A.priorSignature}).then((G)=>{return{message:A.message,signature:G}})}async signString(A,{signingDate:B=new Date,signingRegion:Q,signingService:D}={}){let Z=await this.credentialProvider();this.validateResolvedCredentials(Z);let G=Q??await this.regionProvider(),{shortDate:F}=this.formatDate(B),I=new this.sha256(await this.getSigningKey(Z,G,F,D));return I.update(jyA.toUint8Array(A)),Kk.toHex(await I.digest())}async signRequest(A,{signingDate:B=new Date,signableHeaders:Q,unsignableHeaders:D,signingRegion:Z,signingService:G}={}){let F=await this.credentialProvider();this.validateResolvedCredentials(F);let I=Z??await this.regionProvider(),Y=WA0(A),{longDate:W,shortDate:J}=this.formatDate(B),X=DU1(J,I,G??this.service);if(Y.headers[KA0]=W,F.sessionToken)Y.headers[dyA]=F.sessionToken;let V=await ZU1(Y,this.sha256);if(!oyA(FU1,Y.headers)&&this.applyChecksum)Y.headers[FU1]=V;let C=IA0(Y,D,Q),K=await this.getSignature(W,X,this.getSigningKey(F,I,J,G),this.createCanonicalRequest(Y,C,V));return Y.headers[CA0]=`${BU1} Credential=${F.accessKeyId}/${X}, SignedHeaders=${this.getCanonicalHeaderList(C)}, Signature=${K}`,Y}async getSignature(A,B,Q,D){let Z=await this.createStringToSign(A,B,D,BU1),G=new this.sha256(await Q);return G.update(jyA.toUint8Array(Z)),Kk.toHex(await G.digest())}getSigningKey(A,B,Q,D){return ryA(this.sha256,A,Q,B,D||this.service)}},kvQ={SignatureV4a:null}});
var DlA=E((BlA)=>{Object.defineProperty(BlA,"__esModule",{value:!0});BlA.ruleSet=void 0;var pcA="required",U4="type",l8="fn",p8="argv",Ok="ref",xcA=!1,X90=!0,Rk="booleanEquals",gY="stringEquals",icA="sigv4",ncA="sts",acA="us-east-1",ED="endpoint",vcA="https://sts.{Region}.{PartitionResult#dnsSuffix}",ZL="tree",$a="error",C90="getAttr",bcA={[pcA]:!1,[U4]:"String"},V90={[pcA]:!0,default:!1,[U4]:"Boolean"},scA={[Ok]:"Endpoint"},fcA={[l8]:"isSet",[p8]:[{[Ok]:"Region"}]},uY={[Ok]:"Region"},hcA={[l8]:"aws.partition",[p8]:[uY],assign:"PartitionResult"},rcA={[Ok]:"UseFIPS"},ocA={[Ok]:"UseDualStack"},iW={url:"https://sts.amazonaws.com",properties:{authSchemes:[{name:icA,signingName:ncA,signingRegion:acA}]},headers:{}},WK={},gcA={conditions:[{[l8]:gY,[p8]:[uY,"aws-global"]}],[ED]:iW,[U4]:ED},tcA={[l8]:Rk,[p8]:[rcA,!0]},ecA={[l8]:Rk,[p8]:[ocA,!0]},ucA={[l8]:C90,[p8]:[{[Ok]:"PartitionResult"},"supportsFIPS"]},AlA={[Ok]:"PartitionResult"},mcA={[l8]:Rk,[p8]:[!0,{[l8]:C90,[p8]:[AlA,"supportsDualStack"]}]},dcA=[{[l8]:"isSet",[p8]:[scA]}],ccA=[tcA],lcA=[ecA],VtQ={version:"1.0",parameters:{Region:bcA,UseDualStack:V90,UseFIPS:V90,Endpoint:bcA,UseGlobalEndpoint:V90},rules:[{conditions:[{[l8]:Rk,[p8]:[{[Ok]:"UseGlobalEndpoint"},X90]},{[l8]:"not",[p8]:dcA},fcA,hcA,{[l8]:Rk,[p8]:[rcA,xcA]},{[l8]:Rk,[p8]:[ocA,xcA]}],rules:[{conditions:[{[l8]:gY,[p8]:[uY,"ap-northeast-1"]}],endpoint:iW,[U4]:ED},{conditions:[{[l8]:gY,[p8]:[uY,"ap-south-1"]}],endpoint:iW,[U4]:ED},{conditions:[{[l8]:gY,[p8]:[uY,"ap-southeast-1"]}],endpoint:iW,[U4]:ED},{conditions:[{[l8]:gY,[p8]:[uY,"ap-southeast-2"]}],endpoint:iW,[U4]:ED},gcA,{conditions:[{[l8]:gY,[p8]:[uY,"ca-central-1"]}],endpoint:iW,[U4]:ED},{conditions:[{[l8]:gY,[p8]:[uY,"eu-central-1"]}],endpoint:iW,[U4]:ED},{conditions:[{[l8]:gY,[p8]:[uY,"eu-north-1"]}],endpoint:iW,[U4]:ED},{conditions:[{[l8]:gY,[p8]:[uY,"eu-west-1"]}],endpoint:iW,[U4]:ED},{conditions:[{[l8]:gY,[p8]:[uY,"eu-west-2"]}],endpoint:iW,[U4]:ED},{conditions:[{[l8]:gY,[p8]:[uY,"eu-west-3"]}],endpoint:iW,[U4]:ED},{conditions:[{[l8]:gY,[p8]:[uY,"sa-east-1"]}],endpoint:iW,[U4]:ED},{conditions:[{[l8]:gY,[p8]:[uY,acA]}],endpoint:iW,[U4]:ED},{conditions:[{[l8]:gY,[p8]:[uY,"us-east-2"]}],endpoint:iW,[U4]:ED},{conditions:[{[l8]:gY,[p8]:[uY,"us-west-1"]}],endpoint:iW,[U4]:ED},{conditions:[{[l8]:gY,[p8]:[uY,"us-west-2"]}],endpoint:iW,[U4]:ED},{endpoint:{url:vcA,properties:{authSchemes:[{name:icA,signingName:ncA,signingRegion:"{Region}"}]},headers:WK},[U4]:ED}],[U4]:ZL},{conditions:dcA,rules:[{conditions:ccA,error:"Invalid Configuration: FIPS and custom endpoint are not supported",[U4]:$a},{conditions:lcA,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",[U4]:$a},{endpoint:{url:scA,properties:WK,headers:WK},[U4]:ED}],[U4]:ZL},{conditions:[fcA],rules:[{conditions:[hcA],rules:[{conditions:[tcA,ecA],rules:[{conditions:[{[l8]:Rk,[p8]:[X90,ucA]},mcA],rules:[{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:WK,headers:WK},[U4]:ED}],[U4]:ZL},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",[U4]:$a}],[U4]:ZL},{conditions:ccA,rules:[{conditions:[{[l8]:Rk,[p8]:[ucA,X90]}],rules:[{conditions:[{[l8]:gY,[p8]:[{[l8]:C90,[p8]:[AlA,"name"]},"aws-us-gov"]}],endpoint:{url:"https://sts.{Region}.amazonaws.com",properties:WK,headers:WK},[U4]:ED},{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dnsSuffix}",properties:WK,headers:WK},[U4]:ED}],[U4]:ZL},{error:"FIPS is enabled but this partition does not support FIPS",[U4]:$a}],[U4]:ZL},{conditions:lcA,rules:[{conditions:[mcA],rules:[{endpoint:{url:"https://sts.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:WK,headers:WK},[U4]:ED}],[U4]:ZL},{error:"DualStack is enabled but this partition does not support DualStack",[U4]:$a}],[U4]:ZL},gcA,{endpoint:{url:vcA,properties:WK,headers:WK},[U4]:ED}],[U4]:ZL}],[U4]:ZL},{error:"Invalid Configuration: Missing Region",[U4]:$a}]};BlA.ruleSet=VtQ});
var DmA=E((QmA)=>{Object.defineProperty(QmA,"__esModule",{value:!0});QmA.createGetRequest=EaQ;QmA.getCredentials=UaQ;var SB0=Q9(),KaQ=YK(),HaQ=w8(),zaQ=ry();function EaQ(A){return new KaQ.HttpRequest({protocol:A.protocol,hostname:A.hostname,port:Number(A.port),path:A.pathname,query:Array.from(A.searchParams.entries()).reduce((B,[Q,D])=>{return B[Q]=D,B},{}),fragment:A.hash})}async function UaQ(A,B){let D=await zaQ.sdkStreamMixin(A.body).transformToString();if(A.statusCode===200){let Z=JSON.parse(D);if(typeof Z.AccessKeyId!=="string"||typeof Z.SecretAccessKey!=="string"||typeof Z.Token!=="string"||typeof Z.Expiration!=="string")throw new SB0.CredentialsProviderError("HTTP credential provider response not of the required format, an object matching: { AccessKeyId: string, SecretAccessKey: string, Token: string, Expiration: string(rfc3339) }",{logger:B});return{accessKeyId:Z.AccessKeyId,secretAccessKey:Z.SecretAccessKey,sessionToken:Z.Token,expiration:HaQ.parseRfc3339DateTime(Z.Expiration)}}if(A.statusCode>=400&&A.statusCode<500){let Z={};try{Z=JSON.parse(D)}catch(G){}throw Object.assign(new SB0.CredentialsProviderError(`Server responded with status: ${A.statusCode}`,{logger:B}),{Code:Z.Code,Message:Z.Message})}throw new SB0.CredentialsProviderError(`Server responded with status: ${A.statusCode}`,{logger:B})}});
var DxA=E((BxA)=>{Object.defineProperty(BxA,"__esModule",{value:!0});BxA.ruleSet=void 0;var o_A="required",$z="fn",qz="argv",on="ref",m_A=!0,d_A="isSet",tQ1="booleanEquals",sn="error",rn="endpoint",WT="tree",rA0="PartitionResult",oA0="getAttr",c_A={[o_A]:!1,type:"String"},l_A={[o_A]:!0,default:!1,type:"Boolean"},p_A={[on]:"Endpoint"},t_A={[$z]:tQ1,[qz]:[{[on]:"UseFIPS"},!0]},e_A={[$z]:tQ1,[qz]:[{[on]:"UseDualStack"},!0]},wz={},i_A={[$z]:oA0,[qz]:[{[on]:rA0},"supportsFIPS"]},AxA={[on]:rA0},n_A={[$z]:tQ1,[qz]:[!0,{[$z]:oA0,[qz]:[AxA,"supportsDualStack"]}]},a_A=[t_A],s_A=[e_A],r_A=[{[on]:"Region"}],shQ={version:"1.0",parameters:{Region:c_A,UseDualStack:l_A,UseFIPS:l_A,Endpoint:c_A},rules:[{conditions:[{[$z]:d_A,[qz]:[p_A]}],rules:[{conditions:a_A,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:sn},{conditions:s_A,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:sn},{endpoint:{url:p_A,properties:wz,headers:wz},type:rn}],type:WT},{conditions:[{[$z]:d_A,[qz]:r_A}],rules:[{conditions:[{[$z]:"aws.partition",[qz]:r_A,assign:rA0}],rules:[{conditions:[t_A,e_A],rules:[{conditions:[{[$z]:tQ1,[qz]:[m_A,i_A]},n_A],rules:[{endpoint:{url:"https://portal.sso-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:wz,headers:wz},type:rn}],type:WT},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:sn}],type:WT},{conditions:a_A,rules:[{conditions:[{[$z]:tQ1,[qz]:[i_A,m_A]}],rules:[{conditions:[{[$z]:"stringEquals",[qz]:[{[$z]:oA0,[qz]:[AxA,"name"]},"aws-us-gov"]}],endpoint:{url:"https://portal.sso.{Region}.amazonaws.com",properties:wz,headers:wz},type:rn},{endpoint:{url:"https://portal.sso-fips.{Region}.{PartitionResult#dnsSuffix}",properties:wz,headers:wz},type:rn}],type:WT},{error:"FIPS is enabled but this partition does not support FIPS",type:sn}],type:WT},{conditions:s_A,rules:[{conditions:[n_A],rules:[{endpoint:{url:"https://portal.sso.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:wz,headers:wz},type:rn}],type:WT},{error:"DualStack is enabled but this partition does not support DualStack",type:sn}],type:WT},{endpoint:{url:"https://portal.sso.{Region}.{PartitionResult#dnsSuffix}",properties:wz,headers:wz},type:rn}],type:WT}],type:WT},{error:"Invalid Configuration: Missing Region",type:sn}]};BxA.ruleSet=shQ});
var EV=E((RI5,$jA)=>{var{defineProperty:mE1,getOwnPropertyDescriptor:s_Q,getOwnPropertyNames:r_Q}=Object,o_Q=Object.prototype.hasOwnProperty,Ck=(A,B)=>mE1(A,"name",{value:B,configurable:!0}),t_Q=(A,B)=>{for(var Q in B)mE1(A,Q,{get:B[Q],enumerable:!0})},e_Q=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of r_Q(B))if(!o_Q.call(A,Z)&&Z!==Q)mE1(A,Z,{get:()=>B[Z],enumerable:!(D=s_Q(B,Z))||D.enumerable})}return A},AxQ=(A)=>e_Q(mE1({},"__esModule",{value:!0}),A),zjA={};t_Q(zjA,{Field:()=>DxQ,Fields:()=>ZxQ,HttpRequest:()=>GxQ,HttpResponse:()=>FxQ,IHttpRequest:()=>EjA.HttpRequest,getHttpHandlerExtensionConfiguration:()=>BxQ,isValidHostname:()=>wjA,resolveHttpHandlerRuntimeConfig:()=>QxQ});$jA.exports=AxQ(zjA);var BxQ=Ck((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),QxQ=Ck((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),EjA=BA0(),DxQ=class{static{Ck(this,"Field")}constructor({name:A,kind:B=EjA.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},ZxQ=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{Ck(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},GxQ=class A{static{Ck(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=UjA(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function UjA(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}Ck(UjA,"cloneQuery");var FxQ=class{static{Ck(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function wjA(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}Ck(wjA,"isValidHostname")});
var EaA=E((HaA)=>{Object.defineProperty(HaA,"__esModule",{value:!0});HaA.fromInstanceMetadata=void 0;var a24=Oz(),s24=TF(),r24=(A)=>{return A?.logger?.debug("@smithy/credential-provider-imds","fromInstanceMetadata"),async()=>s24.fromInstanceMetadata(A)().then((B)=>a24.setCredentialFeature(B,"CREDENTIALS_IMDS","0"))};HaA.fromInstanceMetadata=r24});
var ElA=E((HlA)=>{Object.defineProperty(HlA,"__esModule",{value:!0});HlA.getRuntimeConfig=void 0;var MtQ=uh(),RtQ=MtQ.__importDefault(W90()),H90=UV(),VlA=Ow1(),Tw1=K4(),OtQ=CB(),TtQ=gG(),ClA=u4(),Bg=JD(),KlA=k3(),PtQ=uG(),StQ=sZ(),jtQ=XlA(),ytQ=w8(),ktQ=mG(),_tQ=w8(),xtQ=(A)=>{_tQ.emitWarningIfUnsupportedVersion(process.version);let B=ktQ.resolveDefaultsModeConfig(A),Q=()=>B().then(ytQ.loadConfigsForDefaultMode),D=jtQ.getRuntimeConfig(A);H90.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile,logger:D.logger};return{...D,...A,runtime:"node",defaultsMode:B,authSchemePreference:A?.authSchemePreference??Bg.loadConfig(H90.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS,Z),bodyLengthChecker:A?.bodyLengthChecker??PtQ.calculateBodyLength,defaultUserAgentProvider:A?.defaultUserAgentProvider??VlA.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:RtQ.default.version}),httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(G)=>G.getIdentityProvider("aws.auth#sigv4")||(async(F)=>await A.credentialDefaultProvider(F?.__config||{})()),signer:new H90.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(G)=>G.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new OtQ.NoAuthSigner}],maxAttempts:A?.maxAttempts??Bg.loadConfig(ClA.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??Bg.loadConfig(Tw1.NODE_REGION_CONFIG_OPTIONS,{...Tw1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:KlA.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??Bg.loadConfig({...ClA.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||StQ.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??TtQ.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??KlA.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??Bg.loadConfig(Tw1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??Bg.loadConfig(Tw1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??Bg.loadConfig(VlA.NODE_APP_ID_CONFIG_OPTIONS,Z)}};HlA.getRuntimeConfig=xtQ});
var EvA=E((HvA)=>{Object.defineProperty(HvA,"__esModule",{value:!0});HvA.ruleSet=void 0;var XvA="required",Lz="fn",Mz="argv",Ba="ref",BvA=!0,QvA="isSet",Q41="booleanEquals",en="error",Aa="endpoint",XT="tree",Z20="PartitionResult",G20="getAttr",DvA={[XvA]:!1,type:"String"},ZvA={[XvA]:!0,default:!1,type:"Boolean"},GvA={[Ba]:"Endpoint"},VvA={[Lz]:Q41,[Mz]:[{[Ba]:"UseFIPS"},!0]},CvA={[Lz]:Q41,[Mz]:[{[Ba]:"UseDualStack"},!0]},Nz={},FvA={[Lz]:G20,[Mz]:[{[Ba]:Z20},"supportsFIPS"]},KvA={[Ba]:Z20},IvA={[Lz]:Q41,[Mz]:[!0,{[Lz]:G20,[Mz]:[KvA,"supportsDualStack"]}]},YvA=[VvA],WvA=[CvA],JvA=[{[Ba]:"Region"}],zuQ={version:"1.0",parameters:{Region:DvA,UseDualStack:ZvA,UseFIPS:ZvA,Endpoint:DvA},rules:[{conditions:[{[Lz]:QvA,[Mz]:[GvA]}],rules:[{conditions:YvA,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:en},{conditions:WvA,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:en},{endpoint:{url:GvA,properties:Nz,headers:Nz},type:Aa}],type:XT},{conditions:[{[Lz]:QvA,[Mz]:JvA}],rules:[{conditions:[{[Lz]:"aws.partition",[Mz]:JvA,assign:Z20}],rules:[{conditions:[VvA,CvA],rules:[{conditions:[{[Lz]:Q41,[Mz]:[BvA,FvA]},IvA],rules:[{endpoint:{url:"https://oidc-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:Nz,headers:Nz},type:Aa}],type:XT},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:en}],type:XT},{conditions:YvA,rules:[{conditions:[{[Lz]:Q41,[Mz]:[FvA,BvA]}],rules:[{conditions:[{[Lz]:"stringEquals",[Mz]:[{[Lz]:G20,[Mz]:[KvA,"name"]},"aws-us-gov"]}],endpoint:{url:"https://oidc.{Region}.amazonaws.com",properties:Nz,headers:Nz},type:Aa},{endpoint:{url:"https://oidc-fips.{Region}.{PartitionResult#dnsSuffix}",properties:Nz,headers:Nz},type:Aa}],type:XT},{error:"FIPS is enabled but this partition does not support FIPS",type:en}],type:XT},{conditions:WvA,rules:[{conditions:[IvA],rules:[{endpoint:{url:"https://oidc.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:Nz,headers:Nz},type:Aa}],type:XT},{error:"DualStack is enabled but this partition does not support DualStack",type:en}],type:XT},{endpoint:{url:"https://oidc.{Region}.{PartitionResult#dnsSuffix}",properties:Nz,headers:Nz},type:Aa}],type:XT}],type:XT},{error:"Invalid Configuration: Missing Region",type:en}]};HvA.ruleSet=zuQ});
var F41=E((q20)=>{Object.defineProperty(q20,"__esModule",{value:!0});q20.STSClient=q20.__Client=void 0;var NfA=cQ1(),_dQ=lQ1(),xdQ=pQ1(),LfA=an(),vdQ=K4(),$20=CB(),bdQ=bG(),fdQ=R6(),MfA=u4(),OfA=V6();Object.defineProperty(q20,"__Client",{enumerable:!0,get:function(){return OfA.Client}});var RfA=K20(),hdQ=I41(),gdQ=XfA(),udQ=qfA();class TfA extends OfA.Client{config;constructor(...[A]){let B=gdQ.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=hdQ.resolveClientEndpointParameters(B),D=LfA.resolveUserAgentConfig(Q),Z=MfA.resolveRetryConfig(D),G=vdQ.resolveRegionConfig(Z),F=NfA.resolveHostHeaderConfig(G),I=fdQ.resolveEndpointConfig(F),Y=RfA.resolveHttpAuthSchemeConfig(I),W=udQ.resolveRuntimeExtensions(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(LfA.getUserAgentPlugin(this.config)),this.middlewareStack.use(MfA.getRetryPlugin(this.config)),this.middlewareStack.use(bdQ.getContentLengthPlugin(this.config)),this.middlewareStack.use(NfA.getHostHeaderPlugin(this.config)),this.middlewareStack.use(_dQ.getLoggerPlugin(this.config)),this.middlewareStack.use(xdQ.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use($20.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:RfA.defaultSTSHttpAuthSchemeParametersProvider,identityProviderConfigProvider:async(J)=>new $20.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials})})),this.middlewareStack.use($20.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}}q20.STSClient=TfA});
var FjA=E((GjA)=>{Object.defineProperty(GjA,"__esModule",{value:!0});GjA.propertyProviderChain=GjA.createCredentialChain=void 0;var x_Q=Q9(),v_Q=(...A)=>{let B=-1,D=Object.assign(async(Z)=>{let G=await GjA.propertyProviderChain(...A)(Z);if(!G.expiration&&B!==-1)G.expiration=new Date(Date.now()+B);return G},{expireAfter(Z){if(Z<300000)throw new Error("@aws-sdk/credential-providers - createCredentialChain(...).expireAfter(ms) may not be called with a duration lower than five minutes.");return B=Z,D}});return D};GjA.createCredentialChain=v_Q;var b_Q=(...A)=>async(B)=>{if(A.length===0)throw new x_Q.ProviderError("No providers in chain");let Q;for(let D of A)try{return await D(B)}catch(Z){if(Q=Z,Z?.tryNextLink)continue;throw Z}throw Q};GjA.propertyProviderChain=b_Q});
var FlA=E((ZlA)=>{Object.defineProperty(ZlA,"__esModule",{value:!0});ZlA.defaultEndpointResolver=void 0;var CtQ=K41(),K90=S7(),KtQ=DlA(),HtQ=new K90.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS","UseGlobalEndpoint"]}),ztQ=(A,B={})=>{return HtQ.get(A,()=>K90.resolveEndpoint(KtQ.ruleSet,{endpointParams:A,logger:B.logger}))};ZlA.defaultEndpointResolver=ztQ;K90.customEndpointFunctions.aws=CtQ.awsEndpointFunctions});
var FmA=E((ZmA)=>{Object.defineProperty(ZmA,"__esModule",{value:!0});ZmA.retryWrapper=void 0;var qaQ=(A,B,Q)=>{return async()=>{for(let D=0;D<B;++D)try{return await A()}catch(Z){await new Promise((G)=>setTimeout(G,Q))}return await A()}};ZmA.retryWrapper=qaQ});
var FxA=E((ZxA)=>{Object.defineProperty(ZxA,"__esModule",{value:!0});ZxA.defaultEndpointResolver=void 0;var rhQ=mn(),tA0=S7(),ohQ=DxA(),thQ=new tA0.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),ehQ=(A,B={})=>{return thQ.get(A,()=>tA0.resolveEndpoint(ohQ.ruleSet,{endpointParams:A,logger:B.logger}))};ZxA.defaultEndpointResolver=ehQ;tA0.customEndpointFunctions.aws=rhQ.awsEndpointFunctions});
var GB0=E((zW5,fU1)=>{var{defineProperty:VhA,getOwnPropertyDescriptor:XlQ,getOwnPropertyNames:VlQ}=Object,ClQ=Object.prototype.hasOwnProperty,DB0=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of VlQ(B))if(!ClQ.call(A,Z)&&Z!==Q)VhA(A,Z,{get:()=>B[Z],enumerable:!(D=XlQ(B,Z))||D.enumerable})}return A},ChA=(A,B,Q)=>(DB0(A,B,"default"),Q&&DB0(Q,B,"default")),KlQ=(A)=>DB0(VhA({},"__esModule",{value:!0}),A),ZB0={};fU1.exports=KlQ(ZB0);ChA(ZB0,XhA(),fU1.exports);ChA(ZB0,QB0(),fU1.exports)});
var GfA=E((DfA)=>{Object.defineProperty(DfA,"__esModule",{value:!0});DfA.getRuntimeConfig=void 0;var XdQ=VI(),VdQ=CB(),CdQ=V6(),KdQ=JZ(),BfA=Hk(),QfA=cB(),HdQ=K20(),zdQ=AfA(),EdQ=(A)=>{return{apiVersion:"2011-06-15",base64Decoder:A?.base64Decoder??BfA.fromBase64,base64Encoder:A?.base64Encoder??BfA.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??zdQ.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??HdQ.defaultSTSHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new XdQ.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new VdQ.NoAuthSigner}],logger:A?.logger??new CdQ.NoOpLogger,serviceId:A?.serviceId??"STS",urlParser:A?.urlParser??KdQ.parseUrl,utf8Decoder:A?.utf8Decoder??QfA.fromUtf8,utf8Encoder:A?.utf8Encoder??QfA.toUtf8}};DfA.getRuntimeConfig=EdQ});
var GuA=E((LW5,ZuA)=>{var{defineProperty:mU1,getOwnPropertyDescriptor:CpQ,getOwnPropertyNames:KpQ}=Object,HpQ=Object.prototype.hasOwnProperty,EA=(A,B)=>mU1(A,"name",{value:B,configurable:!0}),zpQ=(A,B)=>{for(var Q in B)mU1(A,Q,{get:B[Q],enumerable:!0})},EpQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of KpQ(B))if(!HpQ.call(A,Z)&&Z!==Q)mU1(A,Z,{get:()=>B[Z],enumerable:!(D=CpQ(B,Z))||D.enumerable})}return A},UpQ=(A)=>EpQ(mU1({},"__esModule",{value:!0}),A),CgA={};zpQ(CgA,{AmbiguousRoleResolutionType:()=>PpQ,CognitoIdentity:()=>DuA,CognitoIdentityClient:()=>CB0,CognitoIdentityServiceException:()=>FK,ConcurrentModificationException:()=>MgA,CreateIdentityPoolCommand:()=>vgA,CredentialsFilterSensitiveLog:()=>OgA,DeleteIdentitiesCommand:()=>bgA,DeleteIdentityPoolCommand:()=>fgA,DescribeIdentityCommand:()=>hgA,DescribeIdentityPoolCommand:()=>ggA,DeveloperUserAlreadyRegisteredException:()=>LgA,ErrorCode:()=>SpQ,ExternalServiceException:()=>qgA,GetCredentialsForIdentityCommand:()=>ugA,GetCredentialsForIdentityInputFilterSensitiveLog:()=>RgA,GetCredentialsForIdentityResponseFilterSensitiveLog:()=>TgA,GetIdCommand:()=>mgA,GetIdInputFilterSensitiveLog:()=>PgA,GetIdentityPoolRolesCommand:()=>dgA,GetOpenIdTokenCommand:()=>cgA,GetOpenIdTokenForDeveloperIdentityCommand:()=>lgA,GetOpenIdTokenForDeveloperIdentityInputFilterSensitiveLog:()=>ygA,GetOpenIdTokenForDeveloperIdentityResponseFilterSensitiveLog:()=>kgA,GetOpenIdTokenInputFilterSensitiveLog:()=>SgA,GetOpenIdTokenResponseFilterSensitiveLog:()=>jgA,GetPrincipalTagAttributeMapCommand:()=>pgA,InternalErrorException:()=>KgA,InvalidIdentityPoolConfigurationException:()=>NgA,InvalidParameterException:()=>HgA,LimitExceededException:()=>zgA,ListIdentitiesCommand:()=>igA,ListIdentityPoolsCommand:()=>KB0,ListTagsForResourceCommand:()=>ngA,LookupDeveloperIdentityCommand:()=>agA,MappingRuleMatchType:()=>jpQ,MergeDeveloperIdentitiesCommand:()=>sgA,NotAuthorizedException:()=>EgA,ResourceConflictException:()=>UgA,ResourceNotFoundException:()=>$gA,RoleMappingType:()=>ypQ,SetIdentityPoolRolesCommand:()=>rgA,SetPrincipalTagAttributeMapCommand:()=>ogA,TagResourceCommand:()=>tgA,TooManyRequestsException:()=>wgA,UnlinkDeveloperIdentityCommand:()=>egA,UnlinkIdentityCommand:()=>AuA,UnlinkIdentityInputFilterSensitiveLog:()=>_gA,UntagResourceCommand:()=>BuA,UpdateIdentityPoolCommand:()=>QuA,__Client:()=>KA.Client,paginateListIdentityPools:()=>miQ});ZuA.exports=UpQ(CgA);var YgA=cQ1(),wpQ=lQ1(),$pQ=pQ1(),WgA=an(),qpQ=K4(),uU1=CB(),NpQ=bG(),HD=R6(),JgA=u4(),XgA=dA0(),LpQ=EA((A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,defaultSigningName:"cognito-identity"})},"resolveClientEndpointParameters"),lD={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}},MpQ=IgA(),VgA=eQ1(),VB0=EV(),KA=V6(),RpQ=EA((A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}},"getHttpAuthExtensionConfiguration"),OpQ=EA((A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}},"resolveHttpAuthRuntimeConfig"),TpQ=EA((A,B)=>{let Q=Object.assign(VgA.getAwsRegionExtensionConfiguration(A),KA.getDefaultExtensionConfiguration(A),VB0.getHttpHandlerExtensionConfiguration(A),RpQ(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,VgA.resolveAwsRegionExtensionConfiguration(Q),KA.resolveDefaultRuntimeConfig(Q),VB0.resolveHttpHandlerRuntimeConfig(Q),OpQ(Q))},"resolveRuntimeExtensions"),CB0=class extends KA.Client{static{EA(this,"CognitoIdentityClient")}config;constructor(...[A]){let B=MpQ.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=LpQ(B),D=WgA.resolveUserAgentConfig(Q),Z=JgA.resolveRetryConfig(D),G=qpQ.resolveRegionConfig(Z),F=YgA.resolveHostHeaderConfig(G),I=HD.resolveEndpointConfig(F),Y=XgA.resolveHttpAuthSchemeConfig(I),W=TpQ(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(WgA.getUserAgentPlugin(this.config)),this.middlewareStack.use(JgA.getRetryPlugin(this.config)),this.middlewareStack.use(NpQ.getContentLengthPlugin(this.config)),this.middlewareStack.use(YgA.getHostHeaderPlugin(this.config)),this.middlewareStack.use(wpQ.getLoggerPlugin(this.config)),this.middlewareStack.use($pQ.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(uU1.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:XgA.defaultCognitoIdentityHttpAuthSchemeParametersProvider,identityProviderConfigProvider:EA(async(J)=>new uU1.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials}),"identityProviderConfigProvider")})),this.middlewareStack.use(uU1.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}},pD=j3(),eZ=VI(),FK=class A extends KA.ServiceException{static{EA(this,"CognitoIdentityServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},PpQ={AUTHENTICATED_ROLE:"AuthenticatedRole",DENY:"Deny"},KgA=class A extends FK{static{EA(this,"InternalErrorException")}name="InternalErrorException";$fault="server";constructor(B){super({name:"InternalErrorException",$fault:"server",...B});Object.setPrototypeOf(this,A.prototype)}},HgA=class A extends FK{static{EA(this,"InvalidParameterException")}name="InvalidParameterException";$fault="client";constructor(B){super({name:"InvalidParameterException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},zgA=class A extends FK{static{EA(this,"LimitExceededException")}name="LimitExceededException";$fault="client";constructor(B){super({name:"LimitExceededException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},EgA=class A extends FK{static{EA(this,"NotAuthorizedException")}name="NotAuthorizedException";$fault="client";constructor(B){super({name:"NotAuthorizedException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},UgA=class A extends FK{static{EA(this,"ResourceConflictException")}name="ResourceConflictException";$fault="client";constructor(B){super({name:"ResourceConflictException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},wgA=class A extends FK{static{EA(this,"TooManyRequestsException")}name="TooManyRequestsException";$fault="client";constructor(B){super({name:"TooManyRequestsException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},SpQ={ACCESS_DENIED:"AccessDenied",INTERNAL_SERVER_ERROR:"InternalServerError"},$gA=class A extends FK{static{EA(this,"ResourceNotFoundException")}name="ResourceNotFoundException";$fault="client";constructor(B){super({name:"ResourceNotFoundException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},qgA=class A extends FK{static{EA(this,"ExternalServiceException")}name="ExternalServiceException";$fault="client";constructor(B){super({name:"ExternalServiceException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},NgA=class A extends FK{static{EA(this,"InvalidIdentityPoolConfigurationException")}name="InvalidIdentityPoolConfigurationException";$fault="client";constructor(B){super({name:"InvalidIdentityPoolConfigurationException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},jpQ={CONTAINS:"Contains",EQUALS:"Equals",NOT_EQUAL:"NotEqual",STARTS_WITH:"StartsWith"},ypQ={RULES:"Rules",TOKEN:"Token"},LgA=class A extends FK{static{EA(this,"DeveloperUserAlreadyRegisteredException")}name="DeveloperUserAlreadyRegisteredException";$fault="client";constructor(B){super({name:"DeveloperUserAlreadyRegisteredException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},MgA=class A extends FK{static{EA(this,"ConcurrentModificationException")}name="ConcurrentModificationException";$fault="client";constructor(B){super({name:"ConcurrentModificationException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},RgA=EA((A)=>({...A,...A.Logins&&{Logins:KA.SENSITIVE_STRING}}),"GetCredentialsForIdentityInputFilterSensitiveLog"),OgA=EA((A)=>({...A,...A.SecretKey&&{SecretKey:KA.SENSITIVE_STRING}}),"CredentialsFilterSensitiveLog"),TgA=EA((A)=>({...A,...A.Credentials&&{Credentials:OgA(A.Credentials)}}),"GetCredentialsForIdentityResponseFilterSensitiveLog"),PgA=EA((A)=>({...A,...A.Logins&&{Logins:KA.SENSITIVE_STRING}}),"GetIdInputFilterSensitiveLog"),SgA=EA((A)=>({...A,...A.Logins&&{Logins:KA.SENSITIVE_STRING}}),"GetOpenIdTokenInputFilterSensitiveLog"),jgA=EA((A)=>({...A,...A.Token&&{Token:KA.SENSITIVE_STRING}}),"GetOpenIdTokenResponseFilterSensitiveLog"),ygA=EA((A)=>({...A,...A.Logins&&{Logins:KA.SENSITIVE_STRING}}),"GetOpenIdTokenForDeveloperIdentityInputFilterSensitiveLog"),kgA=EA((A)=>({...A,...A.Token&&{Token:KA.SENSITIVE_STRING}}),"GetOpenIdTokenForDeveloperIdentityResponseFilterSensitiveLog"),_gA=EA((A)=>({...A,...A.Logins&&{Logins:KA.SENSITIVE_STRING}}),"UnlinkIdentityInputFilterSensitiveLog"),kpQ=EA(async(A,B)=>{let Q=zD("CreateIdentityPool"),D;return D=JSON.stringify(KA._json(A)),nD(B,Q,"/",void 0,D)},"se_CreateIdentityPoolCommand"),_pQ=EA(async(A,B)=>{let Q=zD("DeleteIdentities"),D;return D=JSON.stringify(KA._json(A)),nD(B,Q,"/",void 0,D)},"se_DeleteIdentitiesCommand"),xpQ=EA(async(A,B)=>{let Q=zD("DeleteIdentityPool"),D;return D=JSON.stringify(KA._json(A)),nD(B,Q,"/",void 0,D)},"se_DeleteIdentityPoolCommand"),vpQ=EA(async(A,B)=>{let Q=zD("DescribeIdentity"),D;return D=JSON.stringify(KA._json(A)),nD(B,Q,"/",void 0,D)},"se_DescribeIdentityCommand"),bpQ=EA(async(A,B)=>{let Q=zD("DescribeIdentityPool"),D;return D=JSON.stringify(KA._json(A)),nD(B,Q,"/",void 0,D)},"se_DescribeIdentityPoolCommand"),fpQ=EA(async(A,B)=>{let Q=zD("GetCredentialsForIdentity"),D;return D=JSON.stringify(KA._json(A)),nD(B,Q,"/",void 0,D)},"se_GetCredentialsForIdentityCommand"),hpQ=EA(async(A,B)=>{let Q=zD("GetId"),D;return D=JSON.stringify(KA._json(A)),nD(B,Q,"/",void 0,D)},"se_GetIdCommand"),gpQ=EA(async(A,B)=>{let Q=zD("GetIdentityPoolRoles"),D;return D=JSON.stringify(KA._json(A)),nD(B,Q,"/",void 0,D)},"se_GetIdentityPoolRolesCommand"),upQ=EA(async(A,B)=>{let Q=zD("GetOpenIdToken"),D;return D=JSON.stringify(KA._json(A)),nD(B,Q,"/",void 0,D)},"se_GetOpenIdTokenCommand"),mpQ=EA(async(A,B)=>{let Q=zD("GetOpenIdTokenForDeveloperIdentity"),D;return D=JSON.stringify(KA._json(A)),nD(B,Q,"/",void 0,D)},"se_GetOpenIdTokenForDeveloperIdentityCommand"),dpQ=EA(async(A,B)=>{let Q=zD("GetPrincipalTagAttributeMap"),D;return D=JSON.stringify(KA._json(A)),nD(B,Q,"/",void 0,D)},"se_GetPrincipalTagAttributeMapCommand"),cpQ=EA(async(A,B)=>{let Q=zD("ListIdentities"),D;return D=JSON.stringify(KA._json(A)),nD(B,Q,"/",void 0,D)},"se_ListIdentitiesCommand"),lpQ=EA(async(A,B)=>{let Q=zD("ListIdentityPools"),D;return D=JSON.stringify(KA._json(A)),nD(B,Q,"/",void 0,D)},"se_ListIdentityPoolsCommand"),ppQ=EA(async(A,B)=>{let Q=zD("ListTagsForResource"),D;return D=JSON.stringify(KA._json(A)),nD(B,Q,"/",void 0,D)},"se_ListTagsForResourceCommand"),ipQ=EA(async(A,B)=>{let Q=zD("LookupDeveloperIdentity"),D;return D=JSON.stringify(KA._json(A)),nD(B,Q,"/",void 0,D)},"se_LookupDeveloperIdentityCommand"),npQ=EA(async(A,B)=>{let Q=zD("MergeDeveloperIdentities"),D;return D=JSON.stringify(KA._json(A)),nD(B,Q,"/",void 0,D)},"se_MergeDeveloperIdentitiesCommand"),apQ=EA(async(A,B)=>{let Q=zD("SetIdentityPoolRoles"),D;return D=JSON.stringify(KA._json(A)),nD(B,Q,"/",void 0,D)},"se_SetIdentityPoolRolesCommand"),spQ=EA(async(A,B)=>{let Q=zD("SetPrincipalTagAttributeMap"),D;return D=JSON.stringify(KA._json(A)),nD(B,Q,"/",void 0,D)},"se_SetPrincipalTagAttributeMapCommand"),rpQ=EA(async(A,B)=>{let Q=zD("TagResource"),D;return D=JSON.stringify(KA._json(A)),nD(B,Q,"/",void 0,D)},"se_TagResourceCommand"),opQ=EA(async(A,B)=>{let Q=zD("UnlinkDeveloperIdentity"),D;return D=JSON.stringify(KA._json(A)),nD(B,Q,"/",void 0,D)},"se_UnlinkDeveloperIdentityCommand"),tpQ=EA(async(A,B)=>{let Q=zD("UnlinkIdentity"),D;return D=JSON.stringify(KA._json(A)),nD(B,Q,"/",void 0,D)},"se_UnlinkIdentityCommand"),epQ=EA(async(A,B)=>{let Q=zD("UntagResource"),D;return D=JSON.stringify(KA._json(A)),nD(B,Q,"/",void 0,D)},"se_UntagResourceCommand"),AiQ=EA(async(A,B)=>{let Q=zD("UpdateIdentityPool"),D;return D=JSON.stringify(KA._json(A)),nD(B,Q,"/",void 0,D)},"se_UpdateIdentityPoolCommand"),BiQ=EA(async(A,B)=>{if(A.statusCode>=300)return iD(A,B);let Q=await eZ.parseJsonBody(A.body,B),D={};return D=KA._json(Q),{$metadata:Q8(A),...D}},"de_CreateIdentityPoolCommand"),QiQ=EA(async(A,B)=>{if(A.statusCode>=300)return iD(A,B);let Q=await eZ.parseJsonBody(A.body,B),D={};return D=KA._json(Q),{$metadata:Q8(A),...D}},"de_DeleteIdentitiesCommand"),DiQ=EA(async(A,B)=>{if(A.statusCode>=300)return iD(A,B);return await KA.collectBody(A.body,B),{$metadata:Q8(A)}},"de_DeleteIdentityPoolCommand"),ZiQ=EA(async(A,B)=>{if(A.statusCode>=300)return iD(A,B);let Q=await eZ.parseJsonBody(A.body,B),D={};return D=xgA(Q,B),{$metadata:Q8(A),...D}},"de_DescribeIdentityCommand"),GiQ=EA(async(A,B)=>{if(A.statusCode>=300)return iD(A,B);let Q=await eZ.parseJsonBody(A.body,B),D={};return D=KA._json(Q),{$metadata:Q8(A),...D}},"de_DescribeIdentityPoolCommand"),FiQ=EA(async(A,B)=>{if(A.statusCode>=300)return iD(A,B);let Q=await eZ.parseJsonBody(A.body,B),D={};return D=biQ(Q,B),{$metadata:Q8(A),...D}},"de_GetCredentialsForIdentityCommand"),IiQ=EA(async(A,B)=>{if(A.statusCode>=300)return iD(A,B);let Q=await eZ.parseJsonBody(A.body,B),D={};return D=KA._json(Q),{$metadata:Q8(A),...D}},"de_GetIdCommand"),YiQ=EA(async(A,B)=>{if(A.statusCode>=300)return iD(A,B);let Q=await eZ.parseJsonBody(A.body,B),D={};return D=KA._json(Q),{$metadata:Q8(A),...D}},"de_GetIdentityPoolRolesCommand"),WiQ=EA(async(A,B)=>{if(A.statusCode>=300)return iD(A,B);let Q=await eZ.parseJsonBody(A.body,B),D={};return D=KA._json(Q),{$metadata:Q8(A),...D}},"de_GetOpenIdTokenCommand"),JiQ=EA(async(A,B)=>{if(A.statusCode>=300)return iD(A,B);let Q=await eZ.parseJsonBody(A.body,B),D={};return D=KA._json(Q),{$metadata:Q8(A),...D}},"de_GetOpenIdTokenForDeveloperIdentityCommand"),XiQ=EA(async(A,B)=>{if(A.statusCode>=300)return iD(A,B);let Q=await eZ.parseJsonBody(A.body,B),D={};return D=KA._json(Q),{$metadata:Q8(A),...D}},"de_GetPrincipalTagAttributeMapCommand"),ViQ=EA(async(A,B)=>{if(A.statusCode>=300)return iD(A,B);let Q=await eZ.parseJsonBody(A.body,B),D={};return D=hiQ(Q,B),{$metadata:Q8(A),...D}},"de_ListIdentitiesCommand"),CiQ=EA(async(A,B)=>{if(A.statusCode>=300)return iD(A,B);let Q=await eZ.parseJsonBody(A.body,B),D={};return D=KA._json(Q),{$metadata:Q8(A),...D}},"de_ListIdentityPoolsCommand"),KiQ=EA(async(A,B)=>{if(A.statusCode>=300)return iD(A,B);let Q=await eZ.parseJsonBody(A.body,B),D={};return D=KA._json(Q),{$metadata:Q8(A),...D}},"de_ListTagsForResourceCommand"),HiQ=EA(async(A,B)=>{if(A.statusCode>=300)return iD(A,B);let Q=await eZ.parseJsonBody(A.body,B),D={};return D=KA._json(Q),{$metadata:Q8(A),...D}},"de_LookupDeveloperIdentityCommand"),ziQ=EA(async(A,B)=>{if(A.statusCode>=300)return iD(A,B);let Q=await eZ.parseJsonBody(A.body,B),D={};return D=KA._json(Q),{$metadata:Q8(A),...D}},"de_MergeDeveloperIdentitiesCommand"),EiQ=EA(async(A,B)=>{if(A.statusCode>=300)return iD(A,B);return await KA.collectBody(A.body,B),{$metadata:Q8(A)}},"de_SetIdentityPoolRolesCommand"),UiQ=EA(async(A,B)=>{if(A.statusCode>=300)return iD(A,B);let Q=await eZ.parseJsonBody(A.body,B),D={};return D=KA._json(Q),{$metadata:Q8(A),...D}},"de_SetPrincipalTagAttributeMapCommand"),wiQ=EA(async(A,B)=>{if(A.statusCode>=300)return iD(A,B);let Q=await eZ.parseJsonBody(A.body,B),D={};return D=KA._json(Q),{$metadata:Q8(A),...D}},"de_TagResourceCommand"),$iQ=EA(async(A,B)=>{if(A.statusCode>=300)return iD(A,B);return await KA.collectBody(A.body,B),{$metadata:Q8(A)}},"de_UnlinkDeveloperIdentityCommand"),qiQ=EA(async(A,B)=>{if(A.statusCode>=300)return iD(A,B);return await KA.collectBody(A.body,B),{$metadata:Q8(A)}},"de_UnlinkIdentityCommand"),NiQ=EA(async(A,B)=>{if(A.statusCode>=300)return iD(A,B);let Q=await eZ.parseJsonBody(A.body,B),D={};return D=KA._json(Q),{$metadata:Q8(A),...D}},"de_UntagResourceCommand"),LiQ=EA(async(A,B)=>{if(A.statusCode>=300)return iD(A,B);let Q=await eZ.parseJsonBody(A.body,B),D={};return D=KA._json(Q),{$metadata:Q8(A),...D}},"de_UpdateIdentityPoolCommand"),iD=EA(async(A,B)=>{let Q={...A,body:await eZ.parseJsonErrorBody(A.body,B)},D=eZ.loadRestJsonErrorCode(A,Q.body);switch(D){case"InternalErrorException":case"com.amazonaws.cognitoidentity#InternalErrorException":throw await TiQ(Q,B);case"InvalidParameterException":case"com.amazonaws.cognitoidentity#InvalidParameterException":throw await SiQ(Q,B);case"LimitExceededException":case"com.amazonaws.cognitoidentity#LimitExceededException":throw await jiQ(Q,B);case"NotAuthorizedException":case"com.amazonaws.cognitoidentity#NotAuthorizedException":throw await yiQ(Q,B);case"ResourceConflictException":case"com.amazonaws.cognitoidentity#ResourceConflictException":throw await kiQ(Q,B);case"TooManyRequestsException":case"com.amazonaws.cognitoidentity#TooManyRequestsException":throw await xiQ(Q,B);case"ResourceNotFoundException":case"com.amazonaws.cognitoidentity#ResourceNotFoundException":throw await _iQ(Q,B);case"ExternalServiceException":case"com.amazonaws.cognitoidentity#ExternalServiceException":throw await OiQ(Q,B);case"InvalidIdentityPoolConfigurationException":case"com.amazonaws.cognitoidentity#InvalidIdentityPoolConfigurationException":throw await PiQ(Q,B);case"DeveloperUserAlreadyRegisteredException":case"com.amazonaws.cognitoidentity#DeveloperUserAlreadyRegisteredException":throw await RiQ(Q,B);case"ConcurrentModificationException":case"com.amazonaws.cognitoidentity#ConcurrentModificationException":throw await MiQ(Q,B);default:let Z=Q.body;return giQ({output:A,parsedBody:Z,errorCode:D})}},"de_CommandError"),MiQ=EA(async(A,B)=>{let Q=A.body,D=KA._json(Q),Z=new MgA({$metadata:Q8(A),...D});return KA.decorateServiceException(Z,Q)},"de_ConcurrentModificationExceptionRes"),RiQ=EA(async(A,B)=>{let Q=A.body,D=KA._json(Q),Z=new LgA({$metadata:Q8(A),...D});return KA.decorateServiceException(Z,Q)},"de_DeveloperUserAlreadyRegisteredExceptionRes"),OiQ=EA(async(A,B)=>{let Q=A.body,D=KA._json(Q),Z=new qgA({$metadata:Q8(A),...D});return KA.decorateServiceException(Z,Q)},"de_ExternalServiceExceptionRes"),TiQ=EA(async(A,B)=>{let Q=A.body,D=KA._json(Q),Z=new KgA({$metadata:Q8(A),...D});return KA.decorateServiceException(Z,Q)},"de_InternalErrorExceptionRes"),PiQ=EA(async(A,B)=>{let Q=A.body,D=KA._json(Q),Z=new NgA({$metadata:Q8(A),...D});return KA.decorateServiceException(Z,Q)},"de_InvalidIdentityPoolConfigurationExceptionRes"),SiQ=EA(async(A,B)=>{let Q=A.body,D=KA._json(Q),Z=new HgA({$metadata:Q8(A),...D});return KA.decorateServiceException(Z,Q)},"de_InvalidParameterExceptionRes"),jiQ=EA(async(A,B)=>{let Q=A.body,D=KA._json(Q),Z=new zgA({$metadata:Q8(A),...D});return KA.decorateServiceException(Z,Q)},"de_LimitExceededExceptionRes"),yiQ=EA(async(A,B)=>{let Q=A.body,D=KA._json(Q),Z=new EgA({$metadata:Q8(A),...D});return KA.decorateServiceException(Z,Q)},"de_NotAuthorizedExceptionRes"),kiQ=EA(async(A,B)=>{let Q=A.body,D=KA._json(Q),Z=new UgA({$metadata:Q8(A),...D});return KA.decorateServiceException(Z,Q)},"de_ResourceConflictExceptionRes"),_iQ=EA(async(A,B)=>{let Q=A.body,D=KA._json(Q),Z=new $gA({$metadata:Q8(A),...D});return KA.decorateServiceException(Z,Q)},"de_ResourceNotFoundExceptionRes"),xiQ=EA(async(A,B)=>{let Q=A.body,D=KA._json(Q),Z=new wgA({$metadata:Q8(A),...D});return KA.decorateServiceException(Z,Q)},"de_TooManyRequestsExceptionRes"),viQ=EA((A,B)=>{return KA.take(A,{AccessKeyId:KA.expectString,Expiration:EA((Q)=>KA.expectNonNull(KA.parseEpochTimestamp(KA.expectNumber(Q))),"Expiration"),SecretKey:KA.expectString,SessionToken:KA.expectString})},"de_Credentials"),biQ=EA((A,B)=>{return KA.take(A,{Credentials:EA((Q)=>viQ(Q,B),"Credentials"),IdentityId:KA.expectString})},"de_GetCredentialsForIdentityResponse"),fiQ=EA((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return xgA(D,B)})},"de_IdentitiesList"),xgA=EA((A,B)=>{return KA.take(A,{CreationDate:EA((Q)=>KA.expectNonNull(KA.parseEpochTimestamp(KA.expectNumber(Q))),"CreationDate"),IdentityId:KA.expectString,LastModifiedDate:EA((Q)=>KA.expectNonNull(KA.parseEpochTimestamp(KA.expectNumber(Q))),"LastModifiedDate"),Logins:KA._json})},"de_IdentityDescription"),hiQ=EA((A,B)=>{return KA.take(A,{Identities:EA((Q)=>fiQ(Q,B),"Identities"),IdentityPoolId:KA.expectString,NextToken:KA.expectString})},"de_ListIdentitiesResponse"),Q8=EA((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),giQ=KA.withBaseException(FK),nD=EA(async(A,B,Q,D,Z)=>{let{hostname:G,protocol:F="https",port:I,path:Y}=await A.endpoint(),W={protocol:F,hostname:G,port:I,method:"POST",path:Y.endsWith("/")?Y.slice(0,-1)+Q:Y+Q,headers:B};if(D!==void 0)W.hostname=D;if(Z!==void 0)W.body=Z;return new VB0.HttpRequest(W)},"buildHttpRpcRequest");function zD(A){return{"content-type":"application/x-amz-json-1.1","x-amz-target":`AWSCognitoIdentityService.${A}`}}EA(zD,"sharedHeaders");var vgA=class extends KA.Command.classBuilder().ep(lD).m(function(A,B,Q,D){return[pD.getSerdePlugin(Q,this.serialize,this.deserialize),HD.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","CreateIdentityPool",{}).n("CognitoIdentityClient","CreateIdentityPoolCommand").f(void 0,void 0).ser(kpQ).de(BiQ).build(){static{EA(this,"CreateIdentityPoolCommand")}},bgA=class extends KA.Command.classBuilder().ep(lD).m(function(A,B,Q,D){return[pD.getSerdePlugin(Q,this.serialize,this.deserialize),HD.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","DeleteIdentities",{}).n("CognitoIdentityClient","DeleteIdentitiesCommand").f(void 0,void 0).ser(_pQ).de(QiQ).build(){static{EA(this,"DeleteIdentitiesCommand")}},fgA=class extends KA.Command.classBuilder().ep(lD).m(function(A,B,Q,D){return[pD.getSerdePlugin(Q,this.serialize,this.deserialize),HD.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","DeleteIdentityPool",{}).n("CognitoIdentityClient","DeleteIdentityPoolCommand").f(void 0,void 0).ser(xpQ).de(DiQ).build(){static{EA(this,"DeleteIdentityPoolCommand")}},hgA=class extends KA.Command.classBuilder().ep(lD).m(function(A,B,Q,D){return[pD.getSerdePlugin(Q,this.serialize,this.deserialize),HD.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","DescribeIdentity",{}).n("CognitoIdentityClient","DescribeIdentityCommand").f(void 0,void 0).ser(vpQ).de(ZiQ).build(){static{EA(this,"DescribeIdentityCommand")}},ggA=class extends KA.Command.classBuilder().ep(lD).m(function(A,B,Q,D){return[pD.getSerdePlugin(Q,this.serialize,this.deserialize),HD.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","DescribeIdentityPool",{}).n("CognitoIdentityClient","DescribeIdentityPoolCommand").f(void 0,void 0).ser(bpQ).de(GiQ).build(){static{EA(this,"DescribeIdentityPoolCommand")}},ugA=class extends KA.Command.classBuilder().ep(lD).m(function(A,B,Q,D){return[pD.getSerdePlugin(Q,this.serialize,this.deserialize),HD.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","GetCredentialsForIdentity",{}).n("CognitoIdentityClient","GetCredentialsForIdentityCommand").f(RgA,TgA).ser(fpQ).de(FiQ).build(){static{EA(this,"GetCredentialsForIdentityCommand")}},mgA=class extends KA.Command.classBuilder().ep(lD).m(function(A,B,Q,D){return[pD.getSerdePlugin(Q,this.serialize,this.deserialize),HD.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","GetId",{}).n("CognitoIdentityClient","GetIdCommand").f(PgA,void 0).ser(hpQ).de(IiQ).build(){static{EA(this,"GetIdCommand")}},dgA=class extends KA.Command.classBuilder().ep(lD).m(function(A,B,Q,D){return[pD.getSerdePlugin(Q,this.serialize,this.deserialize),HD.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","GetIdentityPoolRoles",{}).n("CognitoIdentityClient","GetIdentityPoolRolesCommand").f(void 0,void 0).ser(gpQ).de(YiQ).build(){static{EA(this,"GetIdentityPoolRolesCommand")}},cgA=class extends KA.Command.classBuilder().ep(lD).m(function(A,B,Q,D){return[pD.getSerdePlugin(Q,this.serialize,this.deserialize),HD.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","GetOpenIdToken",{}).n("CognitoIdentityClient","GetOpenIdTokenCommand").f(SgA,jgA).ser(upQ).de(WiQ).build(){static{EA(this,"GetOpenIdTokenCommand")}},lgA=class extends KA.Command.classBuilder().ep(lD).m(function(A,B,Q,D){return[pD.getSerdePlugin(Q,this.serialize,this.deserialize),HD.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","GetOpenIdTokenForDeveloperIdentity",{}).n("CognitoIdentityClient","GetOpenIdTokenForDeveloperIdentityCommand").f(ygA,kgA).ser(mpQ).de(JiQ).build(){static{EA(this,"GetOpenIdTokenForDeveloperIdentityCommand")}},pgA=class extends KA.Command.classBuilder().ep(lD).m(function(A,B,Q,D){return[pD.getSerdePlugin(Q,this.serialize,this.deserialize),HD.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","GetPrincipalTagAttributeMap",{}).n("CognitoIdentityClient","GetPrincipalTagAttributeMapCommand").f(void 0,void 0).ser(dpQ).de(XiQ).build(){static{EA(this,"GetPrincipalTagAttributeMapCommand")}},igA=class extends KA.Command.classBuilder().ep(lD).m(function(A,B,Q,D){return[pD.getSerdePlugin(Q,this.serialize,this.deserialize),HD.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","ListIdentities",{}).n("CognitoIdentityClient","ListIdentitiesCommand").f(void 0,void 0).ser(cpQ).de(ViQ).build(){static{EA(this,"ListIdentitiesCommand")}},KB0=class extends KA.Command.classBuilder().ep(lD).m(function(A,B,Q,D){return[pD.getSerdePlugin(Q,this.serialize,this.deserialize),HD.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","ListIdentityPools",{}).n("CognitoIdentityClient","ListIdentityPoolsCommand").f(void 0,void 0).ser(lpQ).de(CiQ).build(){static{EA(this,"ListIdentityPoolsCommand")}},ngA=class extends KA.Command.classBuilder().ep(lD).m(function(A,B,Q,D){return[pD.getSerdePlugin(Q,this.serialize,this.deserialize),HD.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","ListTagsForResource",{}).n("CognitoIdentityClient","ListTagsForResourceCommand").f(void 0,void 0).ser(ppQ).de(KiQ).build(){static{EA(this,"ListTagsForResourceCommand")}},agA=class extends KA.Command.classBuilder().ep(lD).m(function(A,B,Q,D){return[pD.getSerdePlugin(Q,this.serialize,this.deserialize),HD.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","LookupDeveloperIdentity",{}).n("CognitoIdentityClient","LookupDeveloperIdentityCommand").f(void 0,void 0).ser(ipQ).de(HiQ).build(){static{EA(this,"LookupDeveloperIdentityCommand")}},sgA=class extends KA.Command.classBuilder().ep(lD).m(function(A,B,Q,D){return[pD.getSerdePlugin(Q,this.serialize,this.deserialize),HD.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","MergeDeveloperIdentities",{}).n("CognitoIdentityClient","MergeDeveloperIdentitiesCommand").f(void 0,void 0).ser(npQ).de(ziQ).build(){static{EA(this,"MergeDeveloperIdentitiesCommand")}},rgA=class extends KA.Command.classBuilder().ep(lD).m(function(A,B,Q,D){return[pD.getSerdePlugin(Q,this.serialize,this.deserialize),HD.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","SetIdentityPoolRoles",{}).n("CognitoIdentityClient","SetIdentityPoolRolesCommand").f(void 0,void 0).ser(apQ).de(EiQ).build(){static{EA(this,"SetIdentityPoolRolesCommand")}},ogA=class extends KA.Command.classBuilder().ep(lD).m(function(A,B,Q,D){return[pD.getSerdePlugin(Q,this.serialize,this.deserialize),HD.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","SetPrincipalTagAttributeMap",{}).n("CognitoIdentityClient","SetPrincipalTagAttributeMapCommand").f(void 0,void 0).ser(spQ).de(UiQ).build(){static{EA(this,"SetPrincipalTagAttributeMapCommand")}},tgA=class extends KA.Command.classBuilder().ep(lD).m(function(A,B,Q,D){return[pD.getSerdePlugin(Q,this.serialize,this.deserialize),HD.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","TagResource",{}).n("CognitoIdentityClient","TagResourceCommand").f(void 0,void 0).ser(rpQ).de(wiQ).build(){static{EA(this,"TagResourceCommand")}},egA=class extends KA.Command.classBuilder().ep(lD).m(function(A,B,Q,D){return[pD.getSerdePlugin(Q,this.serialize,this.deserialize),HD.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","UnlinkDeveloperIdentity",{}).n("CognitoIdentityClient","UnlinkDeveloperIdentityCommand").f(void 0,void 0).ser(opQ).de($iQ).build(){static{EA(this,"UnlinkDeveloperIdentityCommand")}},AuA=class extends KA.Command.classBuilder().ep(lD).m(function(A,B,Q,D){return[pD.getSerdePlugin(Q,this.serialize,this.deserialize),HD.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","UnlinkIdentity",{}).n("CognitoIdentityClient","UnlinkIdentityCommand").f(_gA,void 0).ser(tpQ).de(qiQ).build(){static{EA(this,"UnlinkIdentityCommand")}},BuA=class extends KA.Command.classBuilder().ep(lD).m(function(A,B,Q,D){return[pD.getSerdePlugin(Q,this.serialize,this.deserialize),HD.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","UntagResource",{}).n("CognitoIdentityClient","UntagResourceCommand").f(void 0,void 0).ser(epQ).de(NiQ).build(){static{EA(this,"UntagResourceCommand")}},QuA=class extends KA.Command.classBuilder().ep(lD).m(function(A,B,Q,D){return[pD.getSerdePlugin(Q,this.serialize,this.deserialize),HD.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","UpdateIdentityPool",{}).n("CognitoIdentityClient","UpdateIdentityPoolCommand").f(void 0,void 0).ser(AiQ).de(LiQ).build(){static{EA(this,"UpdateIdentityPoolCommand")}},uiQ={CreateIdentityPoolCommand:vgA,DeleteIdentitiesCommand:bgA,DeleteIdentityPoolCommand:fgA,DescribeIdentityCommand:hgA,DescribeIdentityPoolCommand:ggA,GetCredentialsForIdentityCommand:ugA,GetIdCommand:mgA,GetIdentityPoolRolesCommand:dgA,GetOpenIdTokenCommand:cgA,GetOpenIdTokenForDeveloperIdentityCommand:lgA,GetPrincipalTagAttributeMapCommand:pgA,ListIdentitiesCommand:igA,ListIdentityPoolsCommand:KB0,ListTagsForResourceCommand:ngA,LookupDeveloperIdentityCommand:agA,MergeDeveloperIdentitiesCommand:sgA,SetIdentityPoolRolesCommand:rgA,SetPrincipalTagAttributeMapCommand:ogA,TagResourceCommand:tgA,UnlinkDeveloperIdentityCommand:egA,UnlinkIdentityCommand:AuA,UntagResourceCommand:BuA,UpdateIdentityPoolCommand:QuA},DuA=class extends CB0{static{EA(this,"CognitoIdentity")}};KA.createAggregatedClient(uiQ,DuA);var miQ=uU1.createPaginator(CB0,KB0,"NextToken","NextToken","MaxResults")});
var Hk=E((iI5,WU1)=>{var{defineProperty:LkA,getOwnPropertyDescriptor:DbQ,getOwnPropertyNames:ZbQ}=Object,GbQ=Object.prototype.hasOwnProperty,$A0=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of ZbQ(B))if(!GbQ.call(A,Z)&&Z!==Q)LkA(A,Z,{get:()=>B[Z],enumerable:!(D=DbQ(B,Z))||D.enumerable})}return A},MkA=(A,B,Q)=>($A0(A,B,"default"),Q&&$A0(Q,B,"default")),FbQ=(A)=>$A0(LkA({},"__esModule",{value:!0}),A),qA0={};WU1.exports=FbQ(qA0);MkA(qA0,wkA(),WU1.exports);MkA(qA0,NkA(),WU1.exports)});
var I41=E((ObA)=>{Object.defineProperty(ObA,"__esModule",{value:!0});ObA.commonParams=ObA.resolveClientEndpointParameters=void 0;var ZdQ=(A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,useGlobalEndpoint:A.useGlobalEndpoint??!1,defaultSigningName:"sts"})};ObA.resolveClientEndpointParameters=ZdQ;ObA.commonParams={UseGlobalEndpoint:{type:"builtInParams",name:"useGlobalEndpoint"},UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}}});
var IgA=E((GgA)=>{Object.defineProperty(GgA,"__esModule",{value:!0});GgA.getRuntimeConfig=void 0;var QpQ=mh(),DpQ=QpQ.__importDefault(W_A()),BgA=VI(),ZpQ=ShA(),QgA=oQ1(),gU1=K4(),GpQ=gG(),DgA=u4(),rh=JD(),ZgA=k3(),FpQ=uG(),IpQ=sZ(),YpQ=AgA(),WpQ=V6(),JpQ=mG(),XpQ=V6(),VpQ=(A)=>{XpQ.emitWarningIfUnsupportedVersion(process.version);let B=JpQ.resolveDefaultsModeConfig(A),Q=()=>B().then(WpQ.loadConfigsForDefaultMode),D=YpQ.getRuntimeConfig(A);BgA.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile,logger:D.logger};return{...D,...A,runtime:"node",defaultsMode:B,authSchemePreference:A?.authSchemePreference??rh.loadConfig(BgA.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS,Z),bodyLengthChecker:A?.bodyLengthChecker??FpQ.calculateBodyLength,credentialDefaultProvider:A?.credentialDefaultProvider??ZpQ.defaultProvider,defaultUserAgentProvider:A?.defaultUserAgentProvider??QgA.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:DpQ.default.version}),maxAttempts:A?.maxAttempts??rh.loadConfig(DgA.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??rh.loadConfig(gU1.NODE_REGION_CONFIG_OPTIONS,{...gU1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:ZgA.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??rh.loadConfig({...DgA.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||IpQ.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??GpQ.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??ZgA.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??rh.loadConfig(gU1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??rh.loadConfig(gU1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??rh.loadConfig(QgA.NODE_APP_ID_CONFIG_OPTIONS,Z)}};GgA.getRuntimeConfig=VpQ});
var Iw1=E((NJ5,vmA)=>{var{defineProperty:Fw1,getOwnPropertyDescriptor:WsQ,getOwnPropertyNames:JsQ}=Object,XsQ=Object.prototype.hasOwnProperty,Gw1=(A,B)=>Fw1(A,"name",{value:B,configurable:!0}),VsQ=(A,B)=>{for(var Q in B)Fw1(A,Q,{get:B[Q],enumerable:!0})},CsQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of JsQ(B))if(!XsQ.call(A,Z)&&Z!==Q)Fw1(A,Z,{get:()=>B[Z],enumerable:!(D=WsQ(B,Z))||D.enumerable})}return A},KsQ=(A)=>CsQ(Fw1({},"__esModule",{value:!0}),A),kmA={};VsQ(kmA,{addRecursionDetectionMiddlewareOptions:()=>xmA,getRecursionDetectionPlugin:()=>UsQ,recursionDetectionMiddleware:()=>_mA});vmA.exports=KsQ(kmA);var HsQ=YK(),kB0="X-Amzn-Trace-Id",zsQ="AWS_LAMBDA_FUNCTION_NAME",EsQ="_X_AMZN_TRACE_ID",_mA=Gw1((A)=>(B)=>async(Q)=>{let{request:D}=Q;if(!HsQ.HttpRequest.isInstance(D)||A.runtime!=="node")return B(Q);let Z=Object.keys(D.headers??{}).find((Y)=>Y.toLowerCase()===kB0.toLowerCase())??kB0;if(D.headers.hasOwnProperty(Z))return B(Q);let G=process.env[zsQ],F=process.env[EsQ],I=Gw1((Y)=>typeof Y==="string"&&Y.length>0,"nonEmptyString");if(I(G)&&I(F))D.headers[kB0]=F;return B({...Q,request:D})},"recursionDetectionMiddleware"),xmA={step:"build",tags:["RECURSION_DETECTION"],name:"recursionDetectionMiddleware",override:!0,priority:"low"},UsQ=Gw1((A)=>({applyToStack:Gw1((B)=>{B.add(_mA(A),xmA)},"applyToStack")}),"getRecursionDetectionPlugin")});
var JQ0=E((fX5,vnA)=>{var{defineProperty:lw1,getOwnPropertyDescriptor:QA4,getOwnPropertyNames:DA4}=Object,ZA4=Object.prototype.hasOwnProperty,o4=(A,B)=>lw1(A,"name",{value:B,configurable:!0}),GA4=(A,B)=>{for(var Q in B)lw1(A,Q,{get:B[Q],enumerable:!0})},FA4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of DA4(B))if(!ZA4.call(A,Z)&&Z!==Q)lw1(A,Z,{get:()=>B[Z],enumerable:!(D=QA4(B,Z))||D.enumerable})}return A},IA4=(A)=>FA4(lw1({},"__esModule",{value:!0}),A),znA={};GA4(znA,{$Command:()=>wnA.Command,AccessDeniedException:()=>$nA,AuthorizationPendingException:()=>qnA,CreateTokenCommand:()=>_nA,CreateTokenRequestFilterSensitiveLog:()=>NnA,CreateTokenResponseFilterSensitiveLog:()=>LnA,ExpiredTokenException:()=>MnA,InternalServerException:()=>RnA,InvalidClientException:()=>OnA,InvalidGrantException:()=>TnA,InvalidRequestException:()=>PnA,InvalidScopeException:()=>SnA,SSOOIDC:()=>xnA,SSOOIDCClient:()=>UnA,SSOOIDCServiceException:()=>JK,SlowDownException:()=>jnA,UnauthorizedClientException:()=>ynA,UnsupportedGrantTypeException:()=>knA,__Client:()=>EnA.Client});vnA.exports=IA4(znA);var WnA=Qw1(),YA4=Zw1(),WA4=Iw1(),JnA=$41(),JA4=K4(),YQ0=CB(),XA4=bG(),VA4=R6(),XnA=u4(),EnA=w8(),VnA=ZQ0(),CA4=o4((A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,defaultSigningName:"sso-oauth"})},"resolveClientEndpointParameters"),KA4={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}},HA4=YnA(),CnA=Sw1(),KnA=YK(),HnA=w8(),zA4=o4((A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}},"getHttpAuthExtensionConfiguration"),EA4=o4((A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}},"resolveHttpAuthRuntimeConfig"),UA4=o4((A,B)=>{let Q=Object.assign(CnA.getAwsRegionExtensionConfiguration(A),HnA.getDefaultExtensionConfiguration(A),KnA.getHttpHandlerExtensionConfiguration(A),zA4(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,CnA.resolveAwsRegionExtensionConfiguration(Q),HnA.resolveDefaultRuntimeConfig(Q),KnA.resolveHttpHandlerRuntimeConfig(Q),EA4(Q))},"resolveRuntimeExtensions"),UnA=class extends EnA.Client{static{o4(this,"SSOOIDCClient")}config;constructor(...[A]){let B=HA4.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=CA4(B),D=JnA.resolveUserAgentConfig(Q),Z=XnA.resolveRetryConfig(D),G=JA4.resolveRegionConfig(Z),F=WnA.resolveHostHeaderConfig(G),I=VA4.resolveEndpointConfig(F),Y=VnA.resolveHttpAuthSchemeConfig(I),W=UA4(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(JnA.getUserAgentPlugin(this.config)),this.middlewareStack.use(XnA.getRetryPlugin(this.config)),this.middlewareStack.use(XA4.getContentLengthPlugin(this.config)),this.middlewareStack.use(WnA.getHostHeaderPlugin(this.config)),this.middlewareStack.use(YA4.getLoggerPlugin(this.config)),this.middlewareStack.use(WA4.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(YQ0.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:VnA.defaultSSOOIDCHttpAuthSchemeParametersProvider,identityProviderConfigProvider:o4(async(J)=>new YQ0.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials}),"identityProviderConfigProvider")})),this.middlewareStack.use(YQ0.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}},wA4=w8(),$A4=R6(),qA4=j3(),wnA=w8(),ba=w8(),NA4=w8(),JK=class A extends NA4.ServiceException{static{o4(this,"SSOOIDCServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},$nA=class A extends JK{static{o4(this,"AccessDeniedException")}name="AccessDeniedException";$fault="client";error;error_description;constructor(B){super({name:"AccessDeniedException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},qnA=class A extends JK{static{o4(this,"AuthorizationPendingException")}name="AuthorizationPendingException";$fault="client";error;error_description;constructor(B){super({name:"AuthorizationPendingException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},NnA=o4((A)=>({...A,...A.clientSecret&&{clientSecret:ba.SENSITIVE_STRING},...A.refreshToken&&{refreshToken:ba.SENSITIVE_STRING},...A.codeVerifier&&{codeVerifier:ba.SENSITIVE_STRING}}),"CreateTokenRequestFilterSensitiveLog"),LnA=o4((A)=>({...A,...A.accessToken&&{accessToken:ba.SENSITIVE_STRING},...A.refreshToken&&{refreshToken:ba.SENSITIVE_STRING},...A.idToken&&{idToken:ba.SENSITIVE_STRING}}),"CreateTokenResponseFilterSensitiveLog"),MnA=class A extends JK{static{o4(this,"ExpiredTokenException")}name="ExpiredTokenException";$fault="client";error;error_description;constructor(B){super({name:"ExpiredTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},RnA=class A extends JK{static{o4(this,"InternalServerException")}name="InternalServerException";$fault="server";error;error_description;constructor(B){super({name:"InternalServerException",$fault:"server",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},OnA=class A extends JK{static{o4(this,"InvalidClientException")}name="InvalidClientException";$fault="client";error;error_description;constructor(B){super({name:"InvalidClientException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},TnA=class A extends JK{static{o4(this,"InvalidGrantException")}name="InvalidGrantException";$fault="client";error;error_description;constructor(B){super({name:"InvalidGrantException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},PnA=class A extends JK{static{o4(this,"InvalidRequestException")}name="InvalidRequestException";$fault="client";error;error_description;constructor(B){super({name:"InvalidRequestException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},SnA=class A extends JK{static{o4(this,"InvalidScopeException")}name="InvalidScopeException";$fault="client";error;error_description;constructor(B){super({name:"InvalidScopeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},jnA=class A extends JK{static{o4(this,"SlowDownException")}name="SlowDownException";$fault="client";error;error_description;constructor(B){super({name:"SlowDownException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},ynA=class A extends JK{static{o4(this,"UnauthorizedClientException")}name="UnauthorizedClientException";$fault="client";error;error_description;constructor(B){super({name:"UnauthorizedClientException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},knA=class A extends JK{static{o4(this,"UnsupportedGrantTypeException")}name="UnsupportedGrantTypeException";$fault="client";error;error_description;constructor(B){super({name:"UnsupportedGrantTypeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},WQ0=UV(),LA4=CB(),wB=w8(),MA4=o4(async(A,B)=>{let Q=LA4.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/token");let Z;return Z=JSON.stringify(wB.take(A,{clientId:[],clientSecret:[],code:[],codeVerifier:[],deviceCode:[],grantType:[],redirectUri:[],refreshToken:[],scope:o4((G)=>wB._json(G),"scope")})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateTokenCommand"),RA4=o4(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return OA4(A,B);let Q=wB.map({$metadata:_z(A)}),D=wB.expectNonNull(wB.expectObject(await WQ0.parseJsonBody(A.body,B)),"body"),Z=wB.take(D,{accessToken:wB.expectString,expiresIn:wB.expectInt32,idToken:wB.expectString,refreshToken:wB.expectString,tokenType:wB.expectString});return Object.assign(Q,Z),Q},"de_CreateTokenCommand"),OA4=o4(async(A,B)=>{let Q={...A,body:await WQ0.parseJsonErrorBody(A.body,B)},D=WQ0.loadRestJsonErrorCode(A,Q.body);switch(D){case"AccessDeniedException":case"com.amazonaws.ssooidc#AccessDeniedException":throw await PA4(Q,B);case"AuthorizationPendingException":case"com.amazonaws.ssooidc#AuthorizationPendingException":throw await SA4(Q,B);case"ExpiredTokenException":case"com.amazonaws.ssooidc#ExpiredTokenException":throw await jA4(Q,B);case"InternalServerException":case"com.amazonaws.ssooidc#InternalServerException":throw await yA4(Q,B);case"InvalidClientException":case"com.amazonaws.ssooidc#InvalidClientException":throw await kA4(Q,B);case"InvalidGrantException":case"com.amazonaws.ssooidc#InvalidGrantException":throw await _A4(Q,B);case"InvalidRequestException":case"com.amazonaws.ssooidc#InvalidRequestException":throw await xA4(Q,B);case"InvalidScopeException":case"com.amazonaws.ssooidc#InvalidScopeException":throw await vA4(Q,B);case"SlowDownException":case"com.amazonaws.ssooidc#SlowDownException":throw await bA4(Q,B);case"UnauthorizedClientException":case"com.amazonaws.ssooidc#UnauthorizedClientException":throw await fA4(Q,B);case"UnsupportedGrantTypeException":case"com.amazonaws.ssooidc#UnsupportedGrantTypeException":throw await hA4(Q,B);default:let Z=Q.body;return TA4({output:A,parsedBody:Z,errorCode:D})}},"de_CommandError"),TA4=wB.withBaseException(JK),PA4=o4(async(A,B)=>{let Q=wB.map({}),D=A.body,Z=wB.take(D,{error:wB.expectString,error_description:wB.expectString});Object.assign(Q,Z);let G=new $nA({$metadata:_z(A),...Q});return wB.decorateServiceException(G,A.body)},"de_AccessDeniedExceptionRes"),SA4=o4(async(A,B)=>{let Q=wB.map({}),D=A.body,Z=wB.take(D,{error:wB.expectString,error_description:wB.expectString});Object.assign(Q,Z);let G=new qnA({$metadata:_z(A),...Q});return wB.decorateServiceException(G,A.body)},"de_AuthorizationPendingExceptionRes"),jA4=o4(async(A,B)=>{let Q=wB.map({}),D=A.body,Z=wB.take(D,{error:wB.expectString,error_description:wB.expectString});Object.assign(Q,Z);let G=new MnA({$metadata:_z(A),...Q});return wB.decorateServiceException(G,A.body)},"de_ExpiredTokenExceptionRes"),yA4=o4(async(A,B)=>{let Q=wB.map({}),D=A.body,Z=wB.take(D,{error:wB.expectString,error_description:wB.expectString});Object.assign(Q,Z);let G=new RnA({$metadata:_z(A),...Q});return wB.decorateServiceException(G,A.body)},"de_InternalServerExceptionRes"),kA4=o4(async(A,B)=>{let Q=wB.map({}),D=A.body,Z=wB.take(D,{error:wB.expectString,error_description:wB.expectString});Object.assign(Q,Z);let G=new OnA({$metadata:_z(A),...Q});return wB.decorateServiceException(G,A.body)},"de_InvalidClientExceptionRes"),_A4=o4(async(A,B)=>{let Q=wB.map({}),D=A.body,Z=wB.take(D,{error:wB.expectString,error_description:wB.expectString});Object.assign(Q,Z);let G=new TnA({$metadata:_z(A),...Q});return wB.decorateServiceException(G,A.body)},"de_InvalidGrantExceptionRes"),xA4=o4(async(A,B)=>{let Q=wB.map({}),D=A.body,Z=wB.take(D,{error:wB.expectString,error_description:wB.expectString});Object.assign(Q,Z);let G=new PnA({$metadata:_z(A),...Q});return wB.decorateServiceException(G,A.body)},"de_InvalidRequestExceptionRes"),vA4=o4(async(A,B)=>{let Q=wB.map({}),D=A.body,Z=wB.take(D,{error:wB.expectString,error_description:wB.expectString});Object.assign(Q,Z);let G=new SnA({$metadata:_z(A),...Q});return wB.decorateServiceException(G,A.body)},"de_InvalidScopeExceptionRes"),bA4=o4(async(A,B)=>{let Q=wB.map({}),D=A.body,Z=wB.take(D,{error:wB.expectString,error_description:wB.expectString});Object.assign(Q,Z);let G=new jnA({$metadata:_z(A),...Q});return wB.decorateServiceException(G,A.body)},"de_SlowDownExceptionRes"),fA4=o4(async(A,B)=>{let Q=wB.map({}),D=A.body,Z=wB.take(D,{error:wB.expectString,error_description:wB.expectString});Object.assign(Q,Z);let G=new ynA({$metadata:_z(A),...Q});return wB.decorateServiceException(G,A.body)},"de_UnauthorizedClientExceptionRes"),hA4=o4(async(A,B)=>{let Q=wB.map({}),D=A.body,Z=wB.take(D,{error:wB.expectString,error_description:wB.expectString});Object.assign(Q,Z);let G=new knA({$metadata:_z(A),...Q});return wB.decorateServiceException(G,A.body)},"de_UnsupportedGrantTypeExceptionRes"),_z=o4((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),_nA=class extends wnA.Command.classBuilder().ep(KA4).m(function(A,B,Q,D){return[qA4.getSerdePlugin(Q,this.serialize,this.deserialize),$A4.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSSOOIDCService","CreateToken",{}).n("SSOOIDCClient","CreateTokenCommand").f(NnA,LnA).ser(MA4).de(RA4).build(){static{o4(this,"CreateTokenCommand")}},gA4={CreateTokenCommand:_nA},xnA=class extends UnA{static{o4(this,"SSOOIDC")}};wA4.createAggregatedClient(gA4,xnA)});
var K20=E((LbA)=>{Object.defineProperty(LbA,"__esModule",{value:!0});LbA.resolveHttpAuthSchemeConfig=LbA.resolveStsAuthConfig=LbA.defaultSTSHttpAuthSchemeProvider=LbA.defaultSTSHttpAuthSchemeParametersProvider=void 0;var amQ=VI(),C20=J5(),smQ=F41(),rmQ=async(A,B,Q)=>{return{operation:C20.getSmithyContext(B).operation,region:await C20.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};LbA.defaultSTSHttpAuthSchemeParametersProvider=rmQ;function omQ(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"sts",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function tmQ(A){return{schemeId:"smithy.api#noAuth"}}var emQ=(A)=>{let B=[];switch(A.operation){case"AssumeRoleWithWebIdentity":{B.push(tmQ(A));break}default:B.push(omQ(A))}return B};LbA.defaultSTSHttpAuthSchemeProvider=emQ;var AdQ=(A)=>Object.assign(A,{stsClientCtor:smQ.STSClient});LbA.resolveStsAuthConfig=AdQ;var BdQ=(A)=>{let B=LbA.resolveStsAuthConfig(A),Q=amQ.resolveAwsSdkSigV4Config(B);return Object.assign(Q,{authSchemePreference:C20.normalizeProvider(A.authSchemePreference??[])})};LbA.resolveHttpAuthSchemeConfig=BdQ});
var K41=E((LJ5,pmA)=>{var{defineProperty:Yw1,getOwnPropertyDescriptor:wsQ,getOwnPropertyNames:$sQ}=Object,qsQ=Object.prototype.hasOwnProperty,Ha=(A,B)=>Yw1(A,"name",{value:B,configurable:!0}),NsQ=(A,B)=>{for(var Q in B)Yw1(A,Q,{get:B[Q],enumerable:!0})},LsQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of $sQ(B))if(!qsQ.call(A,Z)&&Z!==Q)Yw1(A,Z,{get:()=>B[Z],enumerable:!(D=wsQ(B,Z))||D.enumerable})}return A},MsQ=(A)=>LsQ(Yw1({},"__esModule",{value:!0}),A),fmA={};NsQ(fmA,{ConditionObject:()=>B7.ConditionObject,DeprecatedObject:()=>B7.DeprecatedObject,EndpointError:()=>B7.EndpointError,EndpointObject:()=>B7.EndpointObject,EndpointObjectHeaders:()=>B7.EndpointObjectHeaders,EndpointObjectProperties:()=>B7.EndpointObjectProperties,EndpointParams:()=>B7.EndpointParams,EndpointResolverOptions:()=>B7.EndpointResolverOptions,EndpointRuleObject:()=>B7.EndpointRuleObject,ErrorRuleObject:()=>B7.ErrorRuleObject,EvaluateOptions:()=>B7.EvaluateOptions,Expression:()=>B7.Expression,FunctionArgv:()=>B7.FunctionArgv,FunctionObject:()=>B7.FunctionObject,FunctionReturn:()=>B7.FunctionReturn,ParameterObject:()=>B7.ParameterObject,ReferenceObject:()=>B7.ReferenceObject,ReferenceRecord:()=>B7.ReferenceRecord,RuleSetObject:()=>B7.RuleSetObject,RuleSetRules:()=>B7.RuleSetRules,TreeRuleObject:()=>B7.TreeRuleObject,awsEndpointFunctions:()=>lmA,getUserAgentPrefix:()=>PsQ,isIpAddress:()=>B7.isIpAddress,partition:()=>dmA,resolveEndpoint:()=>B7.resolveEndpoint,setPartitionInfo:()=>cmA,useDefaultPartitionInfo:()=>TsQ});pmA.exports=MsQ(fmA);var B7=S7(),hmA=Ha((A,B=!1)=>{if(B){for(let Q of A.split("."))if(!hmA(Q))return!1;return!0}if(!B7.isValidHostLabel(A))return!1;if(A.length<3||A.length>63)return!1;if(A!==A.toLowerCase())return!1;if(B7.isIpAddress(A))return!1;return!0},"isVirtualHostableS3Bucket"),bmA=":",RsQ="/",OsQ=Ha((A)=>{let B=A.split(bmA);if(B.length<6)return null;let[Q,D,Z,G,F,...I]=B;if(Q!=="arn"||D===""||Z===""||I.join(bmA)==="")return null;let Y=I.map((W)=>W.split(RsQ)).flat();return{partition:D,service:Z,region:G,accountId:F,resourceId:Y}},"parseArn"),gmA={partitions:[{id:"aws",outputs:{dnsSuffix:"amazonaws.com",dualStackDnsSuffix:"api.aws",implicitGlobalRegion:"us-east-1",name:"aws",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^(us|eu|ap|sa|ca|me|af|il|mx)\\-\\w+\\-\\d+$",regions:{"af-south-1":{description:"Africa (Cape Town)"},"ap-east-1":{description:"Asia Pacific (Hong Kong)"},"ap-east-2":{description:"Asia Pacific (Taipei)"},"ap-northeast-1":{description:"Asia Pacific (Tokyo)"},"ap-northeast-2":{description:"Asia Pacific (Seoul)"},"ap-northeast-3":{description:"Asia Pacific (Osaka)"},"ap-south-1":{description:"Asia Pacific (Mumbai)"},"ap-south-2":{description:"Asia Pacific (Hyderabad)"},"ap-southeast-1":{description:"Asia Pacific (Singapore)"},"ap-southeast-2":{description:"Asia Pacific (Sydney)"},"ap-southeast-3":{description:"Asia Pacific (Jakarta)"},"ap-southeast-4":{description:"Asia Pacific (Melbourne)"},"ap-southeast-5":{description:"Asia Pacific (Malaysia)"},"ap-southeast-7":{description:"Asia Pacific (Thailand)"},"aws-global":{description:"AWS Standard global region"},"ca-central-1":{description:"Canada (Central)"},"ca-west-1":{description:"Canada West (Calgary)"},"eu-central-1":{description:"Europe (Frankfurt)"},"eu-central-2":{description:"Europe (Zurich)"},"eu-north-1":{description:"Europe (Stockholm)"},"eu-south-1":{description:"Europe (Milan)"},"eu-south-2":{description:"Europe (Spain)"},"eu-west-1":{description:"Europe (Ireland)"},"eu-west-2":{description:"Europe (London)"},"eu-west-3":{description:"Europe (Paris)"},"il-central-1":{description:"Israel (Tel Aviv)"},"me-central-1":{description:"Middle East (UAE)"},"me-south-1":{description:"Middle East (Bahrain)"},"mx-central-1":{description:"Mexico (Central)"},"sa-east-1":{description:"South America (Sao Paulo)"},"us-east-1":{description:"US East (N. Virginia)"},"us-east-2":{description:"US East (Ohio)"},"us-west-1":{description:"US West (N. California)"},"us-west-2":{description:"US West (Oregon)"}}},{id:"aws-cn",outputs:{dnsSuffix:"amazonaws.com.cn",dualStackDnsSuffix:"api.amazonwebservices.com.cn",implicitGlobalRegion:"cn-northwest-1",name:"aws-cn",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^cn\\-\\w+\\-\\d+$",regions:{"aws-cn-global":{description:"AWS China global region"},"cn-north-1":{description:"China (Beijing)"},"cn-northwest-1":{description:"China (Ningxia)"}}},{id:"aws-us-gov",outputs:{dnsSuffix:"amazonaws.com",dualStackDnsSuffix:"api.aws",implicitGlobalRegion:"us-gov-west-1",name:"aws-us-gov",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^us\\-gov\\-\\w+\\-\\d+$",regions:{"aws-us-gov-global":{description:"AWS GovCloud (US) global region"},"us-gov-east-1":{description:"AWS GovCloud (US-East)"},"us-gov-west-1":{description:"AWS GovCloud (US-West)"}}},{id:"aws-iso",outputs:{dnsSuffix:"c2s.ic.gov",dualStackDnsSuffix:"c2s.ic.gov",implicitGlobalRegion:"us-iso-east-1",name:"aws-iso",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-iso\\-\\w+\\-\\d+$",regions:{"aws-iso-global":{description:"AWS ISO (US) global region"},"us-iso-east-1":{description:"US ISO East"},"us-iso-west-1":{description:"US ISO WEST"}}},{id:"aws-iso-b",outputs:{dnsSuffix:"sc2s.sgov.gov",dualStackDnsSuffix:"sc2s.sgov.gov",implicitGlobalRegion:"us-isob-east-1",name:"aws-iso-b",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-isob\\-\\w+\\-\\d+$",regions:{"aws-iso-b-global":{description:"AWS ISOB (US) global region"},"us-isob-east-1":{description:"US ISOB East (Ohio)"}}},{id:"aws-iso-e",outputs:{dnsSuffix:"cloud.adc-e.uk",dualStackDnsSuffix:"cloud.adc-e.uk",implicitGlobalRegion:"eu-isoe-west-1",name:"aws-iso-e",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^eu\\-isoe\\-\\w+\\-\\d+$",regions:{"aws-iso-e-global":{description:"AWS ISOE (Europe) global region"},"eu-isoe-west-1":{description:"EU ISOE West"}}},{id:"aws-iso-f",outputs:{dnsSuffix:"csp.hci.ic.gov",dualStackDnsSuffix:"csp.hci.ic.gov",implicitGlobalRegion:"us-isof-south-1",name:"aws-iso-f",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-isof\\-\\w+\\-\\d+$",regions:{"aws-iso-f-global":{description:"AWS ISOF global region"},"us-isof-east-1":{description:"US ISOF EAST"},"us-isof-south-1":{description:"US ISOF SOUTH"}}},{id:"aws-eusc",outputs:{dnsSuffix:"amazonaws.eu",dualStackDnsSuffix:"amazonaws.eu",implicitGlobalRegion:"eusc-de-east-1",name:"aws-eusc",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^eusc\\-(de)\\-\\w+\\-\\d+$",regions:{"eusc-de-east-1":{description:"EU (Germany)"}}}],version:"1.1"},umA=gmA,mmA="",dmA=Ha((A)=>{let{partitions:B}=umA;for(let D of B){let{regions:Z,outputs:G}=D;for(let[F,I]of Object.entries(Z))if(F===A)return{...G,...I}}for(let D of B){let{regionRegex:Z,outputs:G}=D;if(new RegExp(Z).test(A))return{...G}}let Q=B.find((D)=>D.id==="aws");if(!Q)throw new Error("Provided region was not found in the partition array or regex, and default partition with id 'aws' doesn't exist.");return{...Q.outputs}},"partition"),cmA=Ha((A,B="")=>{umA=A,mmA=B},"setPartitionInfo"),TsQ=Ha(()=>{cmA(gmA,"")},"useDefaultPartitionInfo"),PsQ=Ha(()=>mmA,"getUserAgentPrefix"),lmA={isVirtualHostableS3Bucket:hmA,parseArn:OsQ,partition:dmA};B7.customEndpointFunctions.aws=lmA});
var KaA=E((VaA)=>{Object.defineProperty(VaA,"__esModule",{value:!0});VaA.fromIni=void 0;var i24=UQ0(),n24=(A={})=>i24.fromIni({...A});VaA.fromIni=n24});
var KfA=E((VfA)=>{Object.defineProperty(VfA,"__esModule",{value:!0});VfA.resolveHttpAuthRuntimeConfig=VfA.getHttpAuthExtensionConfiguration=void 0;var SdQ=(A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}};VfA.getHttpAuthExtensionConfiguration=SdQ;var jdQ=(A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}};VfA.resolveHttpAuthRuntimeConfig=jdQ});
var N41=E((RcA)=>{Object.defineProperty(RcA,"__esModule",{value:!0});RcA.commonParams=RcA.resolveClientEndpointParameters=void 0;var toQ=(A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,useGlobalEndpoint:A.useGlobalEndpoint??!1,defaultSigningName:"sts"})};RcA.resolveClientEndpointParameters=toQ;RcA.commonParams={UseGlobalEndpoint:{type:"builtInParams",name:"useGlobalEndpoint"},UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}}});
var N_A=E((q_A)=>{Object.defineProperty(q_A,"__esModule",{value:!0});q_A.createGetRequest=XhQ;q_A.getCredentials=VhQ;var lA0=Q9(),YhQ=EV(),WhQ=V6(),JhQ=ry();function XhQ(A){return new YhQ.HttpRequest({protocol:A.protocol,hostname:A.hostname,port:Number(A.port),path:A.pathname,query:Array.from(A.searchParams.entries()).reduce((B,[Q,D])=>{return B[Q]=D,B},{}),fragment:A.hash})}async function VhQ(A,B){let D=await JhQ.sdkStreamMixin(A.body).transformToString();if(A.statusCode===200){let Z=JSON.parse(D);if(typeof Z.AccessKeyId!=="string"||typeof Z.SecretAccessKey!=="string"||typeof Z.Token!=="string"||typeof Z.Expiration!=="string")throw new lA0.CredentialsProviderError("HTTP credential provider response not of the required format, an object matching: { AccessKeyId: string, SecretAccessKey: string, Token: string, Expiration: string(rfc3339) }",{logger:B});return{accessKeyId:Z.AccessKeyId,secretAccessKey:Z.SecretAccessKey,sessionToken:Z.Token,expiration:WhQ.parseRfc3339DateTime(Z.Expiration)}}if(A.statusCode>=400&&A.statusCode<500){let Z={};try{Z=JSON.parse(D)}catch(G){}throw Object.assign(new lA0.CredentialsProviderError(`Server responded with status: ${A.statusCode}`,{logger:B}),{Code:Z.Code,Message:Z.Message})}throw new lA0.CredentialsProviderError(`Server responded with status: ${A.statusCode}`,{logger:B})}});
var NkA=E(($kA)=>{Object.defineProperty($kA,"__esModule",{value:!0});$kA.toBase64=void 0;var AbQ=YD(),BbQ=cB(),QbQ=(A)=>{let B;if(typeof A==="string")B=BbQ.fromUtf8(A);else B=A;if(typeof B!=="object"||typeof B.byteOffset!=="number"||typeof B.byteLength!=="number")throw new Error("@smithy/util-base64: toBase64 encoder function only accepts string | Uint8Array.");return AbQ.fromArrayBuffer(B.buffer,B.byteOffset,B.byteLength).toString("base64")};$kA.toBase64=QbQ});
var NmA=E(($mA)=>{Object.defineProperty($mA,"__esModule",{value:!0});$mA.fromEnv=void 0;var iaQ=eU1(),naQ=(A)=>iaQ.fromEnv(A);$mA.fromEnv=naQ});
var NpA=E(($X5,M14)=>{M14.exports={name:"@aws-sdk/client-sso",description:"AWS SDK for JavaScript Sso Client for Node.js, Browser and React Native",version:"3.840.0",scripts:{build:"concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'","build:cjs":"node ../../scripts/compilation/inline client-sso","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4",clean:"rimraf ./dist-* && rimraf *.tsbuildinfo","extract:docs":"api-extractor run --local","generate:client":"node ../../scripts/generate-clients/single-service --solo sso"},main:"./dist-cjs/index.js",types:"./dist-types/index.d.ts",module:"./dist-es/index.js",sideEffects:!1,dependencies:{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.840.0","@aws-sdk/middleware-host-header":"3.840.0","@aws-sdk/middleware-logger":"3.840.0","@aws-sdk/middleware-recursion-detection":"3.840.0","@aws-sdk/middleware-user-agent":"3.840.0","@aws-sdk/region-config-resolver":"3.840.0","@aws-sdk/types":"3.840.0","@aws-sdk/util-endpoints":"3.840.0","@aws-sdk/util-user-agent-browser":"3.840.0","@aws-sdk/util-user-agent-node":"3.840.0","@smithy/config-resolver":"^4.1.4","@smithy/core":"^3.6.0","@smithy/fetch-http-handler":"^5.0.4","@smithy/hash-node":"^4.0.4","@smithy/invalid-dependency":"^4.0.4","@smithy/middleware-content-length":"^4.0.4","@smithy/middleware-endpoint":"^4.1.13","@smithy/middleware-retry":"^4.1.14","@smithy/middleware-serde":"^4.0.8","@smithy/middleware-stack":"^4.0.4","@smithy/node-config-provider":"^4.1.3","@smithy/node-http-handler":"^4.0.6","@smithy/protocol-http":"^5.1.2","@smithy/smithy-client":"^4.4.5","@smithy/types":"^4.3.1","@smithy/url-parser":"^4.0.4","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.21","@smithy/util-defaults-mode-node":"^4.0.21","@smithy/util-endpoints":"^3.0.6","@smithy/util-middleware":"^4.0.4","@smithy/util-retry":"^4.0.6","@smithy/util-utf8":"^4.0.0",tslib:"^2.6.2"},devDependencies:{"@tsconfig/node18":"18.2.4","@types/node":"^18.19.69",concurrently:"7.0.0","downlevel-dts":"0.10.1",rimraf:"3.0.2",typescript:"~5.8.3"},engines:{node:">=18.0.0"},typesVersions:{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},files:["dist-*/**"],author:{name:"AWS SDK for JavaScript Team",url:"https://aws.amazon.com/javascript/"},license:"Apache-2.0",browser:{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.browser"},"react-native":{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.native"},homepage:"https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-sso",repository:{type:"git",url:"https://github.com/aws/aws-sdk-js-v3.git",directory:"clients/client-sso"}}});
var NuA=E(($uA)=>{Object.defineProperty($uA,"__esModule",{value:!0});$uA.fromCognitoIdentityPool=void 0;var tiQ=wB0(),eiQ=(A)=>tiQ.fromCognitoIdentityPool({...A});$uA.fromCognitoIdentityPool=eiQ});
var OdA=E((OJ5,RdA)=>{var{defineProperty:Ew1,getOwnPropertyDescriptor:psQ,getOwnPropertyNames:isQ}=Object,nsQ=Object.prototype.hasOwnProperty,hY=(A,B)=>Ew1(A,"name",{value:B,configurable:!0}),asQ=(A,B)=>{for(var Q in B)Ew1(A,Q,{get:B[Q],enumerable:!0})},ssQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of isQ(B))if(!nsQ.call(A,Z)&&Z!==Q)Ew1(A,Z,{get:()=>B[Z],enumerable:!(D=psQ(B,Z))||D.enumerable})}return A},rsQ=(A)=>ssQ(Ew1({},"__esModule",{value:!0}),A),DdA={};asQ(DdA,{ALGORITHM_IDENTIFIER:()=>Xw1,ALGORITHM_IDENTIFIER_V4A:()=>ArQ,ALGORITHM_QUERY_PARAM:()=>ZdA,ALWAYS_UNSIGNABLE_HEADERS:()=>VdA,AMZ_DATE_HEADER:()=>mB0,AMZ_DATE_QUERY_PARAM:()=>fB0,AUTH_HEADER:()=>uB0,CREDENTIAL_QUERY_PARAM:()=>GdA,DATE_HEADER:()=>YdA,EVENT_ALGORITHM_IDENTIFIER:()=>HdA,EXPIRES_QUERY_PARAM:()=>IdA,GENERATED_HEADERS:()=>WdA,HOST_HEADER:()=>tsQ,KEY_TYPE_IDENTIFIER:()=>dB0,MAX_CACHE_SIZE:()=>EdA,MAX_PRESIGNED_TTL:()=>UdA,PROXY_HEADER_PATTERN:()=>CdA,REGION_SET_PARAM:()=>osQ,SEC_HEADER_PATTERN:()=>KdA,SHA256_HEADER:()=>zw1,SIGNATURE_HEADER:()=>JdA,SIGNATURE_QUERY_PARAM:()=>hB0,SIGNED_HEADERS_QUERY_PARAM:()=>FdA,SignatureV4:()=>XrQ,SignatureV4Base:()=>MdA,TOKEN_HEADER:()=>XdA,TOKEN_QUERY_PARAM:()=>gB0,UNSIGNABLE_PATTERNS:()=>esQ,UNSIGNED_PAYLOAD:()=>zdA,clearCredentialCache:()=>QrQ,createScope:()=>Cw1,getCanonicalHeaders:()=>xB0,getCanonicalQuery:()=>LdA,getPayloadHash:()=>Kw1,getSigningKey:()=>wdA,hasHeader:()=>$dA,moveHeadersToQuery:()=>NdA,prepareRequest:()=>bB0,signatureV4aContainer:()=>VrQ});RdA.exports=rsQ(DdA);var emA=cB(),ZdA="X-Amz-Algorithm",GdA="X-Amz-Credential",fB0="X-Amz-Date",FdA="X-Amz-SignedHeaders",IdA="X-Amz-Expires",hB0="X-Amz-Signature",gB0="X-Amz-Security-Token",osQ="X-Amz-Region-Set",uB0="authorization",mB0=fB0.toLowerCase(),YdA="date",WdA=[uB0,mB0,YdA],JdA=hB0.toLowerCase(),zw1="x-amz-content-sha256",XdA=gB0.toLowerCase(),tsQ="host",VdA={authorization:!0,"cache-control":!0,connection:!0,expect:!0,from:!0,"keep-alive":!0,"max-forwards":!0,pragma:!0,referer:!0,te:!0,trailer:!0,"transfer-encoding":!0,upgrade:!0,"user-agent":!0,"x-amzn-trace-id":!0},CdA=/^proxy-/,KdA=/^sec-/,esQ=[/^proxy-/i,/^sec-/i],Xw1="AWS4-HMAC-SHA256",ArQ="AWS4-ECDSA-P256-SHA256",HdA="AWS4-HMAC-SHA256-PAYLOAD",zdA="UNSIGNED-PAYLOAD",EdA=50,dB0="aws4_request",UdA=604800,Lk=ay(),BrQ=cB(),za={},Vw1=[],Cw1=hY((A,B,Q)=>`${A}/${B}/${Q}/${dB0}`,"createScope"),wdA=hY(async(A,B,Q,D,Z)=>{let G=await AdA(A,B.secretAccessKey,B.accessKeyId),F=`${Q}:${D}:${Z}:${Lk.toHex(G)}:${B.sessionToken}`;if(F in za)return za[F];Vw1.push(F);while(Vw1.length>EdA)delete za[Vw1.shift()];let I=`AWS4${B.secretAccessKey}`;for(let Y of[Q,D,Z,dB0])I=await AdA(A,I,Y);return za[F]=I},"getSigningKey"),QrQ=hY(()=>{Vw1.length=0,Object.keys(za).forEach((A)=>{delete za[A]})},"clearCredentialCache"),AdA=hY((A,B,Q)=>{let D=new A(B);return D.update(BrQ.toUint8Array(Q)),D.digest()},"hmac"),xB0=hY(({headers:A},B,Q)=>{let D={};for(let Z of Object.keys(A).sort()){if(A[Z]==null)continue;let G=Z.toLowerCase();if(G in VdA||B?.has(G)||CdA.test(G)||KdA.test(G)){if(!Q||Q&&!Q.has(G))continue}D[G]=A[Z].trim().replace(/\s+/g," ")}return D},"getCanonicalHeaders"),DrQ=amA(),ZrQ=cB(),Kw1=hY(async({headers:A,body:B},Q)=>{for(let D of Object.keys(A))if(D.toLowerCase()===zw1)return A[D];if(B==null)return"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855";else if(typeof B==="string"||ArrayBuffer.isView(B)||DrQ.isArrayBuffer(B)){let D=new Q;return D.update(ZrQ.toUint8Array(B)),Lk.toHex(await D.digest())}return zdA},"getPayloadHash"),BdA=cB(),GrQ=class{static{hY(this,"HeaderFormatter")}format(A){let B=[];for(let Z of Object.keys(A)){let G=BdA.fromUtf8(Z);B.push(Uint8Array.from([G.byteLength]),G,this.formatHeaderValue(A[Z]))}let Q=new Uint8Array(B.reduce((Z,G)=>Z+G.byteLength,0)),D=0;for(let Z of B)Q.set(Z,D),D+=Z.byteLength;return Q}formatHeaderValue(A){switch(A.type){case"boolean":return Uint8Array.from([A.value?0:1]);case"byte":return Uint8Array.from([2,A.value]);case"short":let B=new DataView(new ArrayBuffer(3));return B.setUint8(0,3),B.setInt16(1,A.value,!1),new Uint8Array(B.buffer);case"integer":let Q=new DataView(new ArrayBuffer(5));return Q.setUint8(0,4),Q.setInt32(1,A.value,!1),new Uint8Array(Q.buffer);case"long":let D=new Uint8Array(9);return D[0]=5,D.set(A.value.bytes,1),D;case"binary":let Z=new DataView(new ArrayBuffer(3+A.value.byteLength));Z.setUint8(0,6),Z.setUint16(1,A.value.byteLength,!1);let G=new Uint8Array(Z.buffer);return G.set(A.value,3),G;case"string":let F=BdA.fromUtf8(A.value),I=new DataView(new ArrayBuffer(3+F.byteLength));I.setUint8(0,7),I.setUint16(1,F.byteLength,!1);let Y=new Uint8Array(I.buffer);return Y.set(F,3),Y;case"timestamp":let W=new Uint8Array(9);return W[0]=8,W.set(IrQ.fromNumber(A.value.valueOf()).bytes,1),W;case"uuid":if(!FrQ.test(A.value))throw new Error(`Invalid UUID received: ${A.value}`);let J=new Uint8Array(17);return J[0]=9,J.set(Lk.fromHex(A.value.replace(/\-/g,"")),1),J}}},FrQ=/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/,IrQ=class A{constructor(B){if(this.bytes=B,B.byteLength!==8)throw new Error("Int64 buffers must be exactly 8 bytes")}static{hY(this,"Int64")}static fromNumber(B){if(B>9223372036854776000||B<-9223372036854776000)throw new Error(`${B} is too large (or, if negative, too small) to represent as an Int64`);let Q=new Uint8Array(8);for(let D=7,Z=Math.abs(Math.round(B));D>-1&&Z>0;D--,Z/=256)Q[D]=Z;if(B<0)vB0(Q);return new A(Q)}valueOf(){let B=this.bytes.slice(0),Q=B[0]&128;if(Q)vB0(B);return parseInt(Lk.toHex(B),16)*(Q?-1:1)}toString(){return String(this.valueOf())}};function vB0(A){for(let B=0;B<8;B++)A[B]^=255;for(let B=7;B>-1;B--)if(A[B]++,A[B]!==0)break}hY(vB0,"negate");var $dA=hY((A,B)=>{A=A.toLowerCase();for(let Q of Object.keys(B))if(A===Q.toLowerCase())return!0;return!1},"hasHeader"),qdA=YK(),NdA=hY((A,B={})=>{let{headers:Q,query:D={}}=qdA.HttpRequest.clone(A);for(let Z of Object.keys(Q)){let G=Z.toLowerCase();if(G.slice(0,6)==="x-amz-"&&!B.unhoistableHeaders?.has(G)||B.hoistableHeaders?.has(G))D[Z]=Q[Z],delete Q[Z]}return{...A,headers:Q,query:D}},"moveHeadersToQuery"),bB0=hY((A)=>{A=qdA.HttpRequest.clone(A);for(let B of Object.keys(A.headers))if(WdA.indexOf(B.toLowerCase())>-1)delete A.headers[B];return A},"prepareRequest"),QdA=J5(),YrQ=cB(),Hw1=tmA(),LdA=hY(({query:A={}})=>{let B=[],Q={};for(let D of Object.keys(A)){if(D.toLowerCase()===JdA)continue;let Z=Hw1.escapeUri(D);B.push(Z);let G=A[D];if(typeof G==="string")Q[Z]=`${Z}=${Hw1.escapeUri(G)}`;else if(Array.isArray(G))Q[Z]=G.slice(0).reduce((F,I)=>F.concat([`${Z}=${Hw1.escapeUri(I)}`]),[]).sort().join("&")}return B.sort().map((D)=>Q[D]).filter((D)=>D).join("&")},"getCanonicalQuery"),WrQ=hY((A)=>JrQ(A).toISOString().replace(/\.\d{3}Z$/,"Z"),"iso8601"),JrQ=hY((A)=>{if(typeof A==="number")return new Date(A*1000);if(typeof A==="string"){if(Number(A))return new Date(Number(A)*1000);return new Date(A)}return A},"toDate"),MdA=class{static{hY(this,"SignatureV4Base")}constructor({applyChecksum:A,credentials:B,region:Q,service:D,sha256:Z,uriEscapePath:G=!0}){this.service=D,this.sha256=Z,this.uriEscapePath=G,this.applyChecksum=typeof A==="boolean"?A:!0,this.regionProvider=QdA.normalizeProvider(Q),this.credentialProvider=QdA.normalizeProvider(B)}createCanonicalRequest(A,B,Q){let D=Object.keys(B).sort();return`${A.method}
${this.getCanonicalPath(A)}
${LdA(A)}
${D.map((Z)=>`${Z}:${B[Z]}`).join(`
`)}

${D.join(";")}
${Q}`}async createStringToSign(A,B,Q,D){let Z=new this.sha256;Z.update(YrQ.toUint8Array(Q));let G=await Z.digest();return`${D}
${A}
${B}
${Lk.toHex(G)}`}getCanonicalPath({path:A}){if(this.uriEscapePath){let B=[];for(let Z of A.split("/")){if(Z?.length===0)continue;if(Z===".")continue;if(Z==="..")B.pop();else B.push(Z)}let Q=`${A?.startsWith("/")?"/":""}${B.join("/")}${B.length>0&&A?.endsWith("/")?"/":""}`;return Hw1.escapeUri(Q).replace(/%2F/g,"/")}return A}validateResolvedCredentials(A){if(typeof A!=="object"||typeof A.accessKeyId!=="string"||typeof A.secretAccessKey!=="string")throw new Error("Resolved credential object is not valid")}formatDate(A){let B=WrQ(A).replace(/[\-:]/g,"");return{longDate:B,shortDate:B.slice(0,8)}}getCanonicalHeaderList(A){return Object.keys(A).sort().join(";")}},XrQ=class extends MdA{constructor({applyChecksum:A,credentials:B,region:Q,service:D,sha256:Z,uriEscapePath:G=!0}){super({applyChecksum:A,credentials:B,region:Q,service:D,sha256:Z,uriEscapePath:G});this.headerFormatter=new GrQ}static{hY(this,"SignatureV4")}async presign(A,B={}){let{signingDate:Q=new Date,expiresIn:D=3600,unsignableHeaders:Z,unhoistableHeaders:G,signableHeaders:F,hoistableHeaders:I,signingRegion:Y,signingService:W}=B,J=await this.credentialProvider();this.validateResolvedCredentials(J);let X=Y??await this.regionProvider(),{longDate:V,shortDate:C}=this.formatDate(Q);if(D>UdA)return Promise.reject("Signature version 4 presigned URLs must have an expiration date less than one week in the future");let K=Cw1(C,X,W??this.service),H=NdA(bB0(A),{unhoistableHeaders:G,hoistableHeaders:I});if(J.sessionToken)H.query[gB0]=J.sessionToken;H.query[ZdA]=Xw1,H.query[GdA]=`${J.accessKeyId}/${K}`,H.query[fB0]=V,H.query[IdA]=D.toString(10);let z=xB0(H,Z,F);return H.query[FdA]=this.getCanonicalHeaderList(z),H.query[hB0]=await this.getSignature(V,K,this.getSigningKey(J,X,C,W),this.createCanonicalRequest(H,z,await Kw1(A,this.sha256))),H}async sign(A,B){if(typeof A==="string")return this.signString(A,B);else if(A.headers&&A.payload)return this.signEvent(A,B);else if(A.message)return this.signMessage(A,B);else return this.signRequest(A,B)}async signEvent({headers:A,payload:B},{signingDate:Q=new Date,priorSignature:D,signingRegion:Z,signingService:G}){let F=Z??await this.regionProvider(),{shortDate:I,longDate:Y}=this.formatDate(Q),W=Cw1(I,F,G??this.service),J=await Kw1({headers:{},body:B},this.sha256),X=new this.sha256;X.update(A);let V=Lk.toHex(await X.digest()),C=[HdA,Y,W,D,V,J].join(`
`);return this.signString(C,{signingDate:Q,signingRegion:F,signingService:G})}async signMessage(A,{signingDate:B=new Date,signingRegion:Q,signingService:D}){return this.signEvent({headers:this.headerFormatter.format(A.message.headers),payload:A.message.body},{signingDate:B,signingRegion:Q,signingService:D,priorSignature:A.priorSignature}).then((G)=>{return{message:A.message,signature:G}})}async signString(A,{signingDate:B=new Date,signingRegion:Q,signingService:D}={}){let Z=await this.credentialProvider();this.validateResolvedCredentials(Z);let G=Q??await this.regionProvider(),{shortDate:F}=this.formatDate(B),I=new this.sha256(await this.getSigningKey(Z,G,F,D));return I.update(emA.toUint8Array(A)),Lk.toHex(await I.digest())}async signRequest(A,{signingDate:B=new Date,signableHeaders:Q,unsignableHeaders:D,signingRegion:Z,signingService:G}={}){let F=await this.credentialProvider();this.validateResolvedCredentials(F);let I=Z??await this.regionProvider(),Y=bB0(A),{longDate:W,shortDate:J}=this.formatDate(B),X=Cw1(J,I,G??this.service);if(Y.headers[mB0]=W,F.sessionToken)Y.headers[XdA]=F.sessionToken;let V=await Kw1(Y,this.sha256);if(!$dA(zw1,Y.headers)&&this.applyChecksum)Y.headers[zw1]=V;let C=xB0(Y,D,Q),K=await this.getSignature(W,X,this.getSigningKey(F,I,J,G),this.createCanonicalRequest(Y,C,V));return Y.headers[uB0]=`${Xw1} Credential=${F.accessKeyId}/${X}, SignedHeaders=${this.getCanonicalHeaderList(C)}, Signature=${K}`,Y}async getSignature(A,B,Q,D){let Z=await this.createStringToSign(A,B,D,Xw1),G=new this.sha256(await Q);return G.update(emA.toUint8Array(Z)),Lk.toHex(await G.digest())}getSigningKey(A,B,Q,D){return wdA(this.sha256,A,Q,B,D||this.service)}},VrQ={SignatureV4a:null}});
var OlA=E((MlA)=>{Object.defineProperty(MlA,"__esModule",{value:!0});MlA.resolveHttpAuthRuntimeConfig=MlA.getHttpAuthExtensionConfiguration=void 0;var itQ=(A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}};MlA.getHttpAuthExtensionConfiguration=itQ;var ntQ=(A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}};MlA.resolveHttpAuthRuntimeConfig=ntQ});
var Ow1=E((ZX5,_cA)=>{var{defineProperty:Rw1,getOwnPropertyDescriptor:BtQ,getOwnPropertyNames:QtQ}=Object,DtQ=Object.prototype.hasOwnProperty,Mw1=(A,B)=>Rw1(A,"name",{value:B,configurable:!0}),ZtQ=(A,B)=>{for(var Q in B)Rw1(A,Q,{get:B[Q],enumerable:!0})},GtQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of QtQ(B))if(!DtQ.call(A,Z)&&Z!==Q)Rw1(A,Z,{get:()=>B[Z],enumerable:!(D=BtQ(B,Z))||D.enumerable})}return A},FtQ=(A)=>GtQ(Rw1({},"__esModule",{value:!0}),A),PcA={};ZtQ(PcA,{NODE_APP_ID_CONFIG_OPTIONS:()=>XtQ,UA_APP_ID_ENV_NAME:()=>ycA,UA_APP_ID_INI_NAME:()=>kcA,createDefaultUserAgentProvider:()=>jcA,crtAvailability:()=>ScA,defaultUserAgent:()=>YtQ});_cA.exports=FtQ(PcA);var TcA=J1("os"),J90=J1("process"),ScA={isCrtAvailable:!1},ItQ=Mw1(()=>{if(ScA.isCrtAvailable)return["md/crt-avail"];return null},"isCrtAvailable"),jcA=Mw1(({serviceId:A,clientVersion:B})=>{return async(Q)=>{let D=[["aws-sdk-js",B],["ua","2.1"],[`os/${TcA.platform()}`,TcA.release()],["lang/js"],["md/nodejs",`${J90.versions.node}`]],Z=ItQ();if(Z)D.push(Z);if(A)D.push([`api/${A}`,B]);if(J90.env.AWS_EXECUTION_ENV)D.push([`exec-env/${J90.env.AWS_EXECUTION_ENV}`]);let G=await Q?.userAgentAppId?.();return G?[...D,[`app/${G}`]]:[...D]}},"createDefaultUserAgentProvider"),YtQ=jcA,WtQ=$41(),ycA="AWS_SDK_UA_APP_ID",kcA="sdk_ua_app_id",JtQ="sdk-ua-app-id",XtQ={environmentVariableSelector:Mw1((A)=>A[ycA],"environmentVariableSelector"),configFileSelector:Mw1((A)=>A[kcA]??A[JtQ],"configFileSelector"),default:WtQ.DEFAULT_UA_APP_ID}});
var Oz=E((BJ5,juA)=>{var{defineProperty:pU1,getOwnPropertyDescriptor:QnQ,getOwnPropertyNames:DnQ}=Object,ZnQ=Object.prototype.hasOwnProperty,iU1=(A,B)=>pU1(A,"name",{value:B,configurable:!0}),GnQ=(A,B)=>{for(var Q in B)pU1(A,Q,{get:B[Q],enumerable:!0})},FnQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of DnQ(B))if(!ZnQ.call(A,Z)&&Z!==Q)pU1(A,Z,{get:()=>B[Z],enumerable:!(D=QnQ(B,Z))||D.enumerable})}return A},InQ=(A)=>FnQ(pU1({},"__esModule",{value:!0}),A),OuA={};GnQ(OuA,{emitWarningIfUnsupportedVersion:()=>YnQ,setCredentialFeature:()=>TuA,setFeature:()=>PuA,setTokenFeature:()=>SuA,state:()=>$B0});juA.exports=InQ(OuA);var $B0={warningEmitted:!1},YnQ=iU1((A)=>{if(A&&!$B0.warningEmitted&&parseInt(A.substring(1,A.indexOf(".")))<18)$B0.warningEmitted=!0,process.emitWarning(`NodeDeprecationWarning: The AWS SDK for JavaScript (v3) will
no longer support Node.js 16.x on January 6, 2025.

To continue receiving updates to AWS services, bug fixes, and security
updates please upgrade to a supported Node.js LTS version.

More information can be found at: https://a.co/74kJMmI`)},"emitWarningIfUnsupportedVersion");function TuA(A,B,Q){if(!A.$source)A.$source={};return A.$source[B]=Q,A}iU1(TuA,"setCredentialFeature");function PuA(A,B,Q){if(!A.__aws_sdk_context)A.__aws_sdk_context={features:{}};else if(!A.__aws_sdk_context.features)A.__aws_sdk_context.features={};A.__aws_sdk_context.features[B]=Q}iU1(PuA,"setFeature");function SuA(A,B,Q){if(!A.$source)A.$source={};return A.$source[B]=Q,A}iU1(SuA,"setTokenFeature")});
var PiA=E((RX5,TiA)=>{var{defineProperty:bw1,getOwnPropertyDescriptor:n14,getOwnPropertyNames:a14}=Object,s14=Object.prototype.hasOwnProperty,P6=(A,B)=>bw1(A,"name",{value:B,configurable:!0}),r14=(A,B)=>{for(var Q in B)bw1(A,Q,{get:B[Q],enumerable:!0})},o14=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of a14(B))if(!s14.call(A,Z)&&Z!==Q)bw1(A,Z,{get:()=>B[Z],enumerable:!(D=n14(B,Z))||D.enumerable})}return A},t14=(A)=>o14(bw1({},"__esModule",{value:!0}),A),IiA={};r14(IiA,{GetRoleCredentialsCommand:()=>MiA,GetRoleCredentialsRequestFilterSensitiveLog:()=>ViA,GetRoleCredentialsResponseFilterSensitiveLog:()=>KiA,InvalidRequestException:()=>YiA,ListAccountRolesCommand:()=>BQ0,ListAccountRolesRequestFilterSensitiveLog:()=>HiA,ListAccountsCommand:()=>QQ0,ListAccountsRequestFilterSensitiveLog:()=>ziA,LogoutCommand:()=>RiA,LogoutRequestFilterSensitiveLog:()=>EiA,ResourceNotFoundException:()=>WiA,RoleCredentialsFilterSensitiveLog:()=>CiA,SSO:()=>OiA,SSOClient:()=>hw1,SSOServiceException:()=>ka,TooManyRequestsException:()=>JiA,UnauthorizedException:()=>XiA,__Client:()=>OB.Client,paginateListAccountRoles:()=>M04,paginateListAccounts:()=>R04});TiA.exports=t14(IiA);var BiA=Qw1(),e14=Zw1(),A04=Iw1(),QiA=$41(),B04=K4(),NT=CB(),Q04=bG(),R41=R6(),DiA=u4(),ZiA=o90(),D04=P6((A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,defaultSigningName:"awsssoportal"})},"resolveClientEndpointParameters"),fw1={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}},Z04=AiA(),GiA=Sw1(),FiA=YK(),OB=w8(),G04=P6((A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}},"getHttpAuthExtensionConfiguration"),F04=P6((A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}},"resolveHttpAuthRuntimeConfig"),I04=P6((A,B)=>{let Q=Object.assign(GiA.getAwsRegionExtensionConfiguration(A),OB.getDefaultExtensionConfiguration(A),FiA.getHttpHandlerExtensionConfiguration(A),G04(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,GiA.resolveAwsRegionExtensionConfiguration(Q),OB.resolveDefaultRuntimeConfig(Q),FiA.resolveHttpHandlerRuntimeConfig(Q),F04(Q))},"resolveRuntimeExtensions"),hw1=class extends OB.Client{static{P6(this,"SSOClient")}config;constructor(...[A]){let B=Z04.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=D04(B),D=QiA.resolveUserAgentConfig(Q),Z=DiA.resolveRetryConfig(D),G=B04.resolveRegionConfig(Z),F=BiA.resolveHostHeaderConfig(G),I=R41.resolveEndpointConfig(F),Y=ZiA.resolveHttpAuthSchemeConfig(I),W=I04(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(QiA.getUserAgentPlugin(this.config)),this.middlewareStack.use(DiA.getRetryPlugin(this.config)),this.middlewareStack.use(Q04.getContentLengthPlugin(this.config)),this.middlewareStack.use(BiA.getHostHeaderPlugin(this.config)),this.middlewareStack.use(e14.getLoggerPlugin(this.config)),this.middlewareStack.use(A04.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(NT.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:ZiA.defaultSSOHttpAuthSchemeParametersProvider,identityProviderConfigProvider:P6(async(J)=>new NT.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials}),"identityProviderConfigProvider")})),this.middlewareStack.use(NT.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}},gw1=j3(),ka=class A extends OB.ServiceException{static{P6(this,"SSOServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},YiA=class A extends ka{static{P6(this,"InvalidRequestException")}name="InvalidRequestException";$fault="client";constructor(B){super({name:"InvalidRequestException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},WiA=class A extends ka{static{P6(this,"ResourceNotFoundException")}name="ResourceNotFoundException";$fault="client";constructor(B){super({name:"ResourceNotFoundException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},JiA=class A extends ka{static{P6(this,"TooManyRequestsException")}name="TooManyRequestsException";$fault="client";constructor(B){super({name:"TooManyRequestsException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},XiA=class A extends ka{static{P6(this,"UnauthorizedException")}name="UnauthorizedException";$fault="client";constructor(B){super({name:"UnauthorizedException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},ViA=P6((A)=>({...A,...A.accessToken&&{accessToken:OB.SENSITIVE_STRING}}),"GetRoleCredentialsRequestFilterSensitiveLog"),CiA=P6((A)=>({...A,...A.secretAccessKey&&{secretAccessKey:OB.SENSITIVE_STRING},...A.sessionToken&&{sessionToken:OB.SENSITIVE_STRING}}),"RoleCredentialsFilterSensitiveLog"),KiA=P6((A)=>({...A,...A.roleCredentials&&{roleCredentials:CiA(A.roleCredentials)}}),"GetRoleCredentialsResponseFilterSensitiveLog"),HiA=P6((A)=>({...A,...A.accessToken&&{accessToken:OB.SENSITIVE_STRING}}),"ListAccountRolesRequestFilterSensitiveLog"),ziA=P6((A)=>({...A,...A.accessToken&&{accessToken:OB.SENSITIVE_STRING}}),"ListAccountsRequestFilterSensitiveLog"),EiA=P6((A)=>({...A,...A.accessToken&&{accessToken:OB.SENSITIVE_STRING}}),"LogoutRequestFilterSensitiveLog"),M41=UV(),Y04=P6(async(A,B)=>{let Q=NT.requestBuilder(A,B),D=OB.map({},OB.isSerializableHeaderValue,{[dw1]:A[mw1]});Q.bp("/federation/credentials");let Z=OB.map({[N04]:[,OB.expectNonNull(A[q04],"roleName")],[wiA]:[,OB.expectNonNull(A[UiA],"accountId")]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_GetRoleCredentialsCommand"),W04=P6(async(A,B)=>{let Q=NT.requestBuilder(A,B),D=OB.map({},OB.isSerializableHeaderValue,{[dw1]:A[mw1]});Q.bp("/assignment/roles");let Z=OB.map({[LiA]:[,A[NiA]],[qiA]:[()=>A.maxResults!==void 0,()=>A[$iA].toString()],[wiA]:[,OB.expectNonNull(A[UiA],"accountId")]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListAccountRolesCommand"),J04=P6(async(A,B)=>{let Q=NT.requestBuilder(A,B),D=OB.map({},OB.isSerializableHeaderValue,{[dw1]:A[mw1]});Q.bp("/assignment/accounts");let Z=OB.map({[LiA]:[,A[NiA]],[qiA]:[()=>A.maxResults!==void 0,()=>A[$iA].toString()]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListAccountsCommand"),X04=P6(async(A,B)=>{let Q=NT.requestBuilder(A,B),D=OB.map({},OB.isSerializableHeaderValue,{[dw1]:A[mw1]});Q.bp("/logout");let Z;return Q.m("POST").h(D).b(Z),Q.build()},"se_LogoutCommand"),V04=P6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return uw1(A,B);let Q=OB.map({$metadata:Tk(A)}),D=OB.expectNonNull(OB.expectObject(await M41.parseJsonBody(A.body,B)),"body"),Z=OB.take(D,{roleCredentials:OB._json});return Object.assign(Q,Z),Q},"de_GetRoleCredentialsCommand"),C04=P6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return uw1(A,B);let Q=OB.map({$metadata:Tk(A)}),D=OB.expectNonNull(OB.expectObject(await M41.parseJsonBody(A.body,B)),"body"),Z=OB.take(D,{nextToken:OB.expectString,roleList:OB._json});return Object.assign(Q,Z),Q},"de_ListAccountRolesCommand"),K04=P6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return uw1(A,B);let Q=OB.map({$metadata:Tk(A)}),D=OB.expectNonNull(OB.expectObject(await M41.parseJsonBody(A.body,B)),"body"),Z=OB.take(D,{accountList:OB._json,nextToken:OB.expectString});return Object.assign(Q,Z),Q},"de_ListAccountsCommand"),H04=P6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return uw1(A,B);let Q=OB.map({$metadata:Tk(A)});return await OB.collectBody(A.body,B),Q},"de_LogoutCommand"),uw1=P6(async(A,B)=>{let Q={...A,body:await M41.parseJsonErrorBody(A.body,B)},D=M41.loadRestJsonErrorCode(A,Q.body);switch(D){case"InvalidRequestException":case"com.amazonaws.sso#InvalidRequestException":throw await E04(Q,B);case"ResourceNotFoundException":case"com.amazonaws.sso#ResourceNotFoundException":throw await U04(Q,B);case"TooManyRequestsException":case"com.amazonaws.sso#TooManyRequestsException":throw await w04(Q,B);case"UnauthorizedException":case"com.amazonaws.sso#UnauthorizedException":throw await $04(Q,B);default:let Z=Q.body;return z04({output:A,parsedBody:Z,errorCode:D})}},"de_CommandError"),z04=OB.withBaseException(ka),E04=P6(async(A,B)=>{let Q=OB.map({}),D=A.body,Z=OB.take(D,{message:OB.expectString});Object.assign(Q,Z);let G=new YiA({$metadata:Tk(A),...Q});return OB.decorateServiceException(G,A.body)},"de_InvalidRequestExceptionRes"),U04=P6(async(A,B)=>{let Q=OB.map({}),D=A.body,Z=OB.take(D,{message:OB.expectString});Object.assign(Q,Z);let G=new WiA({$metadata:Tk(A),...Q});return OB.decorateServiceException(G,A.body)},"de_ResourceNotFoundExceptionRes"),w04=P6(async(A,B)=>{let Q=OB.map({}),D=A.body,Z=OB.take(D,{message:OB.expectString});Object.assign(Q,Z);let G=new JiA({$metadata:Tk(A),...Q});return OB.decorateServiceException(G,A.body)},"de_TooManyRequestsExceptionRes"),$04=P6(async(A,B)=>{let Q=OB.map({}),D=A.body,Z=OB.take(D,{message:OB.expectString});Object.assign(Q,Z);let G=new XiA({$metadata:Tk(A),...Q});return OB.decorateServiceException(G,A.body)},"de_UnauthorizedExceptionRes"),Tk=P6((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),UiA="accountId",mw1="accessToken",wiA="account_id",$iA="maxResults",qiA="max_result",NiA="nextToken",LiA="next_token",q04="roleName",N04="role_name",dw1="x-amz-sso_bearer_token",MiA=class extends OB.Command.classBuilder().ep(fw1).m(function(A,B,Q,D){return[gw1.getSerdePlugin(Q,this.serialize,this.deserialize),R41.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","GetRoleCredentials",{}).n("SSOClient","GetRoleCredentialsCommand").f(ViA,KiA).ser(Y04).de(V04).build(){static{P6(this,"GetRoleCredentialsCommand")}},BQ0=class extends OB.Command.classBuilder().ep(fw1).m(function(A,B,Q,D){return[gw1.getSerdePlugin(Q,this.serialize,this.deserialize),R41.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","ListAccountRoles",{}).n("SSOClient","ListAccountRolesCommand").f(HiA,void 0).ser(W04).de(C04).build(){static{P6(this,"ListAccountRolesCommand")}},QQ0=class extends OB.Command.classBuilder().ep(fw1).m(function(A,B,Q,D){return[gw1.getSerdePlugin(Q,this.serialize,this.deserialize),R41.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","ListAccounts",{}).n("SSOClient","ListAccountsCommand").f(ziA,void 0).ser(J04).de(K04).build(){static{P6(this,"ListAccountsCommand")}},RiA=class extends OB.Command.classBuilder().ep(fw1).m(function(A,B,Q,D){return[gw1.getSerdePlugin(Q,this.serialize,this.deserialize),R41.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","Logout",{}).n("SSOClient","LogoutCommand").f(EiA,void 0).ser(X04).de(H04).build(){static{P6(this,"LogoutCommand")}},L04={GetRoleCredentialsCommand:MiA,ListAccountRolesCommand:BQ0,ListAccountsCommand:QQ0,LogoutCommand:RiA},OiA=class extends hw1{static{P6(this,"SSO")}};OB.createAggregatedClient(L04,OiA);var M04=NT.createPaginator(hw1,BQ0,"nextToken","nextToken","maxResults"),R04=NT.createPaginator(hw1,QQ0,"nextToken","nextToken","maxResults")});
var Q20=E((exA)=>{Object.defineProperty(exA,"__esModule",{value:!0});exA.resolveHttpAuthSchemeConfig=exA.defaultSSOOIDCHttpAuthSchemeProvider=exA.defaultSSOOIDCHttpAuthSchemeParametersProvider=void 0;var IuQ=VI(),B20=J5(),YuQ=async(A,B,Q)=>{return{operation:B20.getSmithyContext(B).operation,region:await B20.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};exA.defaultSSOOIDCHttpAuthSchemeParametersProvider=YuQ;function WuQ(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"sso-oauth",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function JuQ(A){return{schemeId:"smithy.api#noAuth"}}var XuQ=(A)=>{let B=[];switch(A.operation){case"CreateToken":{B.push(JuQ(A));break}default:B.push(WuQ(A))}return B};exA.defaultSSOOIDCHttpAuthSchemeProvider=XuQ;var VuQ=(A)=>{let B=IuQ.resolveAwsSdkSigV4Config(A);return Object.assign(B,{authSchemePreference:B20.normalizeProvider(A.authSchemePreference??[])})};exA.resolveHttpAuthSchemeConfig=VuQ});
var QB0=E((AL)=>{var AlQ=AL&&AL.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),BlQ=AL&&AL.__setModuleDefault||(Object.create?function(A,B){Object.defineProperty(A,"default",{enumerable:!0,value:B})}:function(A,B){A.default=B}),QlQ=AL&&AL.__importStar||function(){var A=function(B){return A=Object.getOwnPropertyNames||function(Q){var D=[];for(var Z in Q)if(Object.prototype.hasOwnProperty.call(Q,Z))D[D.length]=Z;return D},A(B)};return function(B){if(B&&B.__esModule)return B;var Q={};if(B!=null){for(var D=A(B),Z=0;Z<D.length;Z++)if(D[Z]!=="default")AlQ(Q,B,D[Z])}return BlQ(Q,B),Q}}();Object.defineProperty(AL,"__esModule",{value:!0});AL.fromWebToken=void 0;var DlQ=(A)=>async(B)=>{A.logger?.debug("@aws-sdk/credential-provider-web-identity - fromWebToken");let{roleArn:Q,roleSessionName:D,webIdentityToken:Z,providerId:G,policyArns:F,policy:I,durationSeconds:Y}=A,{roleAssumerWithWebIdentity:W}=A;if(!W){let{getDefaultRoleAssumerWithWebIdentity:J}=await Promise.resolve().then(()=>QlQ(t20()));W=J({...A.clientConfig,credentialProviderLogger:A.logger,parentClientConfig:{...B?.callerClientConfig,...A.parentClientConfig}},A.clientPlugins)}return W({RoleArn:Q,RoleSessionName:D??`aws-sdk-js-session-${Date.now()}`,WebIdentityToken:Z,ProviderId:G,PolicyArns:F,Policy:I,DurationSeconds:Y})};AL.fromWebToken=DlQ});
var QaA=E((AaA)=>{Object.defineProperty(AaA,"__esModule",{value:!0});AaA.fromTokenFile=void 0;var z24=Oz(),E24=Q9(),U24=J1("fs"),w24=CQ0(),enA="AWS_WEB_IDENTITY_TOKEN_FILE",$24="AWS_ROLE_ARN",q24="AWS_ROLE_SESSION_NAME",N24=(A={})=>async()=>{A.logger?.debug("@aws-sdk/credential-provider-web-identity - fromTokenFile");let B=A?.webIdentityTokenFile??process.env[enA],Q=A?.roleArn??process.env[$24],D=A?.roleSessionName??process.env[q24];if(!B||!Q)throw new E24.CredentialsProviderError("Web identity configuration not specified",{logger:A.logger});let Z=await w24.fromWebToken({...A,webIdentityToken:U24.readFileSync(B,{encoding:"ascii"}),roleArn:Q,roleSessionName:D})();if(B===process.env[enA])z24.setCredentialFeature(Z,"CREDENTIALS_ENV_VARS_STS_WEB_ID_TOKEN","h");return Z};AaA.fromTokenFile=N24});
var Qw1=E(($J5,TmA)=>{var{defineProperty:Bw1,getOwnPropertyDescriptor:aaQ,getOwnPropertyNames:saQ}=Object,raQ=Object.prototype.hasOwnProperty,Aw1=(A,B)=>Bw1(A,"name",{value:B,configurable:!0}),oaQ=(A,B)=>{for(var Q in B)Bw1(A,Q,{get:B[Q],enumerable:!0})},taQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of saQ(B))if(!raQ.call(A,Z)&&Z!==Q)Bw1(A,Z,{get:()=>B[Z],enumerable:!(D=aaQ(B,Z))||D.enumerable})}return A},eaQ=(A)=>taQ(Bw1({},"__esModule",{value:!0}),A),LmA={};oaQ(LmA,{getHostHeaderPlugin:()=>BsQ,hostHeaderMiddleware:()=>RmA,hostHeaderMiddlewareOptions:()=>OmA,resolveHostHeaderConfig:()=>MmA});TmA.exports=eaQ(LmA);var AsQ=YK();function MmA(A){return A}Aw1(MmA,"resolveHostHeaderConfig");var RmA=Aw1((A)=>(B)=>async(Q)=>{if(!AsQ.HttpRequest.isInstance(Q.request))return B(Q);let{request:D}=Q,{handlerProtocol:Z=""}=A.requestHandler.metadata||{};if(Z.indexOf("h2")>=0&&!D.headers[":authority"])delete D.headers.host,D.headers[":authority"]=D.hostname+(D.port?":"+D.port:"");else if(!D.headers.host){let G=D.hostname;if(D.port!=null)G+=`:${D.port}`;D.headers.host=G}return B(Q)},"hostHeaderMiddleware"),OmA={name:"hostHeaderMiddleware",step:"build",priority:"low",tags:["HOST"],override:!0},BsQ=Aw1((A)=>({applyToStack:Aw1((B)=>{B.add(RmA(A),OmA)},"applyToStack")}),"getHostHeaderPlugin")});
var R_A=E((L_A)=>{Object.defineProperty(L_A,"__esModule",{value:!0});L_A.retryWrapper=void 0;var HhQ=(A,B,Q)=>{return async()=>{for(let D=0;D<B;++D)try{return await A()}catch(Z){await new Promise((G)=>setTimeout(G,Q))}return await A()}};L_A.retryWrapper=HhQ});
var RaA=E((sX5,MaA)=>{var{create:o24,defineProperty:_41,getOwnPropertyDescriptor:t24,getOwnPropertyNames:e24,getPrototypeOf:AB4}=Object,BB4=Object.prototype.hasOwnProperty,rw1=(A,B)=>_41(A,"name",{value:B,configurable:!0}),QB4=(A,B)=>{for(var Q in B)_41(A,Q,{get:B[Q],enumerable:!0})},$aA=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of e24(B))if(!BB4.call(A,Z)&&Z!==Q)_41(A,Z,{get:()=>B[Z],enumerable:!(D=t24(B,Z))||D.enumerable})}return A},fa=(A,B,Q)=>(Q=A!=null?o24(AB4(A)):{},$aA(B||!A||!A.__esModule?_41(Q,"default",{value:A,enumerable:!0}):Q,A)),DB4=(A)=>$aA(_41({},"__esModule",{value:!0}),A),qaA={};QB4(qaA,{credentialsTreatedAsExpired:()=>LaA,credentialsWillNeedRefresh:()=>NaA,defaultProvider:()=>FB4});MaA.exports=DB4(qaA);var wQ0=eU1(),ZB4=D3(),Gg=Q9(),UaA="AWS_EC2_METADATA_DISABLED",GB4=rw1(async(A)=>{let{ENV_CMDS_FULL_URI:B,ENV_CMDS_RELATIVE_URI:Q,fromContainerMetadata:D,fromInstanceMetadata:Z}=await Promise.resolve().then(()=>fa(TF()));if(process.env[Q]||process.env[B]){A.logger?.debug("@aws-sdk/credential-provider-node - remoteProvider::fromHttp/fromContainerMetadata");let{fromHttp:G}=await Promise.resolve().then(()=>fa(oU1()));return Gg.chain(G(A),D(A))}if(process.env[UaA]&&process.env[UaA]!=="false")return async()=>{throw new Gg.CredentialsProviderError("EC2 Instance Metadata Service access disabled",{logger:A.logger})};return A.logger?.debug("@aws-sdk/credential-provider-node - remoteProvider::fromInstanceMetadata"),Z(A)},"remoteProvider"),waA=!1,FB4=rw1((A={})=>Gg.memoize(Gg.chain(async()=>{if(A.profile??process.env[ZB4.ENV_PROFILE]){if(process.env[wQ0.ENV_KEY]&&process.env[wQ0.ENV_SECRET]){if(!waA)(A.logger?.warn&&A.logger?.constructor?.name!=="NoOpLogger"?A.logger.warn:console.warn)(`@aws-sdk/credential-provider-node - defaultProvider::fromEnv WARNING:
    Multiple credential sources detected: 
    Both AWS_PROFILE and the pair AWS_ACCESS_KEY_ID/AWS_SECRET_ACCESS_KEY static credentials are set.
    This SDK will proceed with the AWS_PROFILE value.
    
    However, a future version may change this behavior to prefer the ENV static credentials.
    Please ensure that your environment only sets either the AWS_PROFILE or the
    AWS_ACCESS_KEY_ID/AWS_SECRET_ACCESS_KEY pair.
`),waA=!0}throw new Gg.CredentialsProviderError("AWS_PROFILE is set, skipping fromEnv provider.",{logger:A.logger,tryNextLink:!0})}return A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromEnv"),wQ0.fromEnv(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromSSO");let{ssoStartUrl:B,ssoAccountId:Q,ssoRegion:D,ssoRoleName:Z,ssoSession:G}=A;if(!B&&!Q&&!D&&!Z&&!G)throw new Gg.CredentialsProviderError("Skipping SSO provider in default chain (inputs do not include SSO fields).",{logger:A.logger});let{fromSSO:F}=await Promise.resolve().then(()=>fa(aw1()));return F(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromIni");let{fromIni:B}=await Promise.resolve().then(()=>fa(UQ0()));return B(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromProcess");let{fromProcess:B}=await Promise.resolve().then(()=>fa(_w1()));return B(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromTokenFile");let{fromTokenFile:B}=await Promise.resolve().then(()=>fa(j41()));return B(A)()},async()=>{return A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::remoteProvider"),(await GB4(A))()},async()=>{throw new Gg.CredentialsProviderError("Could not load credentials from any providers",{tryNextLink:!1,logger:A.logger})}),LaA,NaA),"defaultProvider"),NaA=rw1((A)=>A?.expiration!==void 0,"credentialsWillNeedRefresh"),LaA=rw1((A)=>A?.expiration!==void 0&&A.expiration.getTime()-Date.now()<300000,"credentialsTreatedAsExpired")});
var RuA=E((LuA)=>{Object.defineProperty(LuA,"__esModule",{value:!0});LuA.fromContainerMetadata=void 0;var AnQ=TF(),BnQ=(A)=>{return A?.logger?.debug("@smithy/credential-provider-imds","fromContainerMetadata"),AnQ.fromContainerMetadata(A)};LuA.fromContainerMetadata=BnQ});
var RvA=E((LvA)=>{Object.defineProperty(LvA,"__esModule",{value:!0});LvA.getRuntimeConfig=void 0;var quQ=VI(),NuQ=CB(),LuQ=V6(),MuQ=JZ(),qvA=Hk(),NvA=cB(),RuQ=Q20(),OuQ=$vA(),TuQ=(A)=>{return{apiVersion:"2019-06-10",base64Decoder:A?.base64Decoder??qvA.fromBase64,base64Encoder:A?.base64Encoder??qvA.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??OuQ.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??RuQ.defaultSSOOIDCHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new quQ.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new NuQ.NoAuthSigner}],logger:A?.logger??new LuQ.NoOpLogger,serviceId:A?.serviceId??"SSO OIDC",urlParser:A?.urlParser??MuQ.parseUrl,utf8Decoder:A?.utf8Decoder??NvA.fromUtf8,utf8Encoder:A?.utf8Encoder??NvA.toUtf8}};LvA.getRuntimeConfig=TuQ});
var RyA=E((vI5,MyA)=>{var{defineProperty:eE1,getOwnPropertyDescriptor:sxQ,getOwnPropertyNames:rxQ}=Object,oxQ=Object.prototype.hasOwnProperty,txQ=(A,B)=>eE1(A,"name",{value:B,configurable:!0}),exQ=(A,B)=>{for(var Q in B)eE1(A,Q,{get:B[Q],enumerable:!0})},AvQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of rxQ(B))if(!oxQ.call(A,Z)&&Z!==Q)eE1(A,Z,{get:()=>B[Z],enumerable:!(D=sxQ(B,Z))||D.enumerable})}return A},BvQ=(A)=>AvQ(eE1({},"__esModule",{value:!0}),A),LyA={};exQ(LyA,{isArrayBuffer:()=>QvQ});MyA.exports=BvQ(LyA);var QvQ=txQ((A)=>typeof ArrayBuffer==="function"&&A instanceof ArrayBuffer||Object.prototype.toString.call(A)==="[object ArrayBuffer]","isArrayBuffer")});
var ShA=E((UW5,PhA)=>{var{create:xlQ,defineProperty:J41,getOwnPropertyDescriptor:vlQ,getOwnPropertyNames:blQ,getPrototypeOf:flQ}=Object,hlQ=Object.prototype.hasOwnProperty,hU1=(A,B)=>J41(A,"name",{value:B,configurable:!0}),glQ=(A,B)=>{for(var Q in B)J41(A,Q,{get:B[Q],enumerable:!0})},MhA=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of blQ(B))if(!hlQ.call(A,Z)&&Z!==Q)J41(A,Z,{get:()=>B[Z],enumerable:!(D=vlQ(B,Z))||D.enumerable})}return A},Va=(A,B,Q)=>(Q=A!=null?xlQ(flQ(A)):{},MhA(B||!A||!A.__esModule?J41(Q,"default",{value:A,enumerable:!0}):Q,A)),ulQ=(A)=>MhA(J41({},"__esModule",{value:!0}),A),RhA={};glQ(RhA,{credentialsTreatedAsExpired:()=>ThA,credentialsWillNeedRefresh:()=>OhA,defaultProvider:()=>clQ});PhA.exports=ulQ(RhA);var YB0=cA0(),mlQ=D3(),sh=Q9(),NhA="AWS_EC2_METADATA_DISABLED",dlQ=hU1(async(A)=>{let{ENV_CMDS_FULL_URI:B,ENV_CMDS_RELATIVE_URI:Q,fromContainerMetadata:D,fromInstanceMetadata:Z}=await Promise.resolve().then(()=>Va(TF()));if(process.env[Q]||process.env[B]){A.logger?.debug("@aws-sdk/credential-provider-node - remoteProvider::fromHttp/fromContainerMetadata");let{fromHttp:G}=await Promise.resolve().then(()=>Va(iA0()));return sh.chain(G(A),D(A))}if(process.env[NhA]&&process.env[NhA]!=="false")return async()=>{throw new sh.CredentialsProviderError("EC2 Instance Metadata Service access disabled",{logger:A.logger})};return A.logger?.debug("@aws-sdk/credential-provider-node - remoteProvider::fromInstanceMetadata"),Z(A)},"remoteProvider"),LhA=!1,clQ=hU1((A={})=>sh.memoize(sh.chain(async()=>{if(A.profile??process.env[mlQ.ENV_PROFILE]){if(process.env[YB0.ENV_KEY]&&process.env[YB0.ENV_SECRET]){if(!LhA)(A.logger?.warn&&A.logger?.constructor?.name!=="NoOpLogger"?A.logger.warn:console.warn)(`@aws-sdk/credential-provider-node - defaultProvider::fromEnv WARNING:
    Multiple credential sources detected: 
    Both AWS_PROFILE and the pair AWS_ACCESS_KEY_ID/AWS_SECRET_ACCESS_KEY static credentials are set.
    This SDK will proceed with the AWS_PROFILE value.
    
    However, a future version may change this behavior to prefer the ENV static credentials.
    Please ensure that your environment only sets either the AWS_PROFILE or the
    AWS_ACCESS_KEY_ID/AWS_SECRET_ACCESS_KEY pair.
`),LhA=!0}throw new sh.CredentialsProviderError("AWS_PROFILE is set, skipping fromEnv provider.",{logger:A.logger,tryNextLink:!0})}return A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromEnv"),YB0.fromEnv(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromSSO");let{ssoStartUrl:B,ssoAccountId:Q,ssoRegion:D,ssoRoleName:Z,ssoSession:G}=A;if(!B&&!Q&&!D&&!Z&&!G)throw new sh.CredentialsProviderError("Skipping SSO provider in default chain (inputs do not include SSO fields).",{logger:A.logger});let{fromSSO:F}=await Promise.resolve().then(()=>Va(V20()));return F(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromIni");let{fromIni:B}=await Promise.resolve().then(()=>Va(qhA()));return B(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromProcess");let{fromProcess:B}=await Promise.resolve().then(()=>Va(BB0()));return B(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromTokenFile");let{fromTokenFile:B}=await Promise.resolve().then(()=>Va(GB0()));return B(A)()},async()=>{return A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::remoteProvider"),(await dlQ(A))()},async()=>{throw new sh.CredentialsProviderError("Could not load credentials from any providers",{tryNextLink:!1,logger:A.logger})}),ThA,OhA),"defaultProvider"),OhA=hU1((A)=>A?.expiration!==void 0,"credentialsWillNeedRefresh"),ThA=hU1((A)=>A?.expiration!==void 0&&A.expiration.getTime()-Date.now()<300000,"credentialsTreatedAsExpired")});
var Sw=E((xI5,NyA)=>{var{defineProperty:oE1,getOwnPropertyDescriptor:dxQ,getOwnPropertyNames:cxQ}=Object,lxQ=Object.prototype.hasOwnProperty,tE1=(A,B)=>oE1(A,"name",{value:B,configurable:!0}),pxQ=(A,B)=>{for(var Q in B)oE1(A,Q,{get:B[Q],enumerable:!0})},ixQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of cxQ(B))if(!lxQ.call(A,Z)&&Z!==Q)oE1(A,Z,{get:()=>B[Z],enumerable:!(D=dxQ(B,Z))||D.enumerable})}return A},nxQ=(A)=>ixQ(oE1({},"__esModule",{value:!0}),A),UyA={};pxQ(UyA,{emitWarningIfUnsupportedVersion:()=>axQ,setCredentialFeature:()=>wyA,setFeature:()=>$yA,setTokenFeature:()=>qyA,state:()=>GA0});NyA.exports=nxQ(UyA);var GA0={warningEmitted:!1},axQ=tE1((A)=>{if(A&&!GA0.warningEmitted&&parseInt(A.substring(1,A.indexOf(".")))<18)GA0.warningEmitted=!0,process.emitWarning(`NodeDeprecationWarning: The AWS SDK for JavaScript (v3) will
no longer support Node.js 16.x on January 6, 2025.

To continue receiving updates to AWS services, bug fixes, and security
updates please upgrade to a supported Node.js LTS version.

More information can be found at: https://a.co/74kJMmI`)},"emitWarningIfUnsupportedVersion");function wyA(A,B,Q){if(!A.$source)A.$source={};return A.$source[B]=Q,A}tE1(wyA,"setCredentialFeature");function $yA(A,B,Q){if(!A.__aws_sdk_context)A.__aws_sdk_context={features:{}};else if(!A.__aws_sdk_context.features)A.__aws_sdk_context.features={};A.__aws_sdk_context.features[B]=Q}tE1($yA,"setFeature");function qyA(A,B,Q){if(!A.$source)A.$source={};return A.$source[B]=Q,A}tE1(qyA,"setTokenFeature")});
var Sw1=E((WX5,LlA)=>{var{defineProperty:Pw1,getOwnPropertyDescriptor:vtQ,getOwnPropertyNames:btQ}=Object,ftQ=Object.prototype.hasOwnProperty,GL=(A,B)=>Pw1(A,"name",{value:B,configurable:!0}),htQ=(A,B)=>{for(var Q in B)Pw1(A,Q,{get:B[Q],enumerable:!0})},gtQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of btQ(B))if(!ftQ.call(A,Z)&&Z!==Q)Pw1(A,Z,{get:()=>B[Z],enumerable:!(D=vtQ(B,Z))||D.enumerable})}return A},utQ=(A)=>gtQ(Pw1({},"__esModule",{value:!0}),A),wlA={};htQ(wlA,{NODE_REGION_CONFIG_FILE_OPTIONS:()=>ltQ,NODE_REGION_CONFIG_OPTIONS:()=>ctQ,REGION_ENV_NAME:()=>$lA,REGION_INI_NAME:()=>qlA,getAwsRegionExtensionConfiguration:()=>mtQ,resolveAwsRegionExtensionConfiguration:()=>dtQ,resolveRegionConfig:()=>ptQ});LlA.exports=utQ(wlA);var mtQ=GL((A)=>{return{setRegion(B){A.region=B},region(){return A.region}}},"getAwsRegionExtensionConfiguration"),dtQ=GL((A)=>{return{region:A.region()}},"resolveAwsRegionExtensionConfiguration"),$lA="AWS_REGION",qlA="region",ctQ={environmentVariableSelector:GL((A)=>A[$lA],"environmentVariableSelector"),configFileSelector:GL((A)=>A[qlA],"configFileSelector"),default:GL(()=>{throw new Error("Region is missing")},"default")},ltQ={preferredFile:"credentials"},NlA=GL((A)=>typeof A==="string"&&(A.startsWith("fips-")||A.endsWith("-fips")),"isFipsRegion"),UlA=GL((A)=>NlA(A)?["fips-aws-global","aws-fips"].includes(A)?"us-east-1":A.replace(/fips-(dkr-|prod-)?|-fips/,""):A,"getRealRegion"),ptQ=GL((A)=>{let{region:B,useFipsEndpoint:Q}=A;if(!B)throw new Error("Region is missing");return Object.assign(A,{region:GL(async()=>{if(typeof B==="string")return UlA(B);let D=await B();return UlA(D)},"region"),useFipsEndpoint:GL(async()=>{let D=typeof B==="string"?B:await B();if(NlA(D))return!0;return typeof Q!=="function"?Promise.resolve(!!Q):Q()},"useFipsEndpoint")})},"resolveRegionConfig")});
var SyA=E((bI5,PyA)=>{var{defineProperty:AU1,getOwnPropertyDescriptor:DvQ,getOwnPropertyNames:ZvQ}=Object,GvQ=Object.prototype.hasOwnProperty,FA0=(A,B)=>AU1(A,"name",{value:B,configurable:!0}),FvQ=(A,B)=>{for(var Q in B)AU1(A,Q,{get:B[Q],enumerable:!0})},IvQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of ZvQ(B))if(!GvQ.call(A,Z)&&Z!==Q)AU1(A,Z,{get:()=>B[Z],enumerable:!(D=DvQ(B,Z))||D.enumerable})}return A},YvQ=(A)=>IvQ(AU1({},"__esModule",{value:!0}),A),OyA={};FvQ(OyA,{escapeUri:()=>TyA,escapeUriPath:()=>JvQ});PyA.exports=YvQ(OyA);var TyA=FA0((A)=>encodeURIComponent(A).replace(/[!'()*]/g,WvQ),"escapeUri"),WvQ=FA0((A)=>`%${A.charCodeAt(0).toString(16).toUpperCase()}`,"hexEncode"),JvQ=FA0((A)=>A.split("/").map(TyA).join("/"),"escapeUriPath")});
var UQ0=E((iX5,XaA)=>{var{create:T24,defineProperty:k41,getOwnPropertyDescriptor:P24,getOwnPropertyNames:S24,getPrototypeOf:j24}=Object,y24=Object.prototype.hasOwnProperty,pG=(A,B)=>k41(A,"name",{value:B,configurable:!0}),k24=(A,B)=>{for(var Q in B)k41(A,Q,{get:B[Q],enumerable:!0})},YaA=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of S24(B))if(!y24.call(A,Z)&&Z!==Q)k41(A,Z,{get:()=>B[Z],enumerable:!(D=P24(B,Z))||D.enumerable})}return A},Pk=(A,B,Q)=>(Q=A!=null?T24(j24(A)):{},YaA(B||!A||!A.__esModule?k41(Q,"default",{value:A,enumerable:!0}):Q,A)),_24=(A)=>YaA(k41({},"__esModule",{value:!0}),A),WaA={};k24(WaA,{fromIni:()=>p24});XaA.exports=_24(WaA);var EQ0=D3(),Sk=Oz(),y41=Q9(),x24=pG((A,B,Q)=>{let D={EcsContainer:pG(async(Z)=>{let{fromHttp:G}=await Promise.resolve().then(()=>Pk(oU1())),{fromContainerMetadata:F}=await Promise.resolve().then(()=>Pk(TF()));return Q?.debug("@aws-sdk/credential-provider-ini - credential_source is EcsContainer"),async()=>y41.chain(G(Z??{}),F(Z))().then(zQ0)},"EcsContainer"),Ec2InstanceMetadata:pG(async(Z)=>{Q?.debug("@aws-sdk/credential-provider-ini - credential_source is Ec2InstanceMetadata");let{fromInstanceMetadata:G}=await Promise.resolve().then(()=>Pk(TF()));return async()=>G(Z)().then(zQ0)},"Ec2InstanceMetadata"),Environment:pG(async(Z)=>{Q?.debug("@aws-sdk/credential-provider-ini - credential_source is Environment");let{fromEnv:G}=await Promise.resolve().then(()=>Pk(eU1()));return async()=>G(Z)().then(zQ0)},"Environment")};if(A in D)return D[A];else throw new y41.CredentialsProviderError(`Unsupported credential source in profile ${B}. Got ${A}, expected EcsContainer or Ec2InstanceMetadata or Environment.`,{logger:Q})},"resolveCredentialSource"),zQ0=pG((A)=>Sk.setCredentialFeature(A,"CREDENTIALS_PROFILE_NAMED_PROVIDER","p"),"setNamedProvider"),v24=pG((A,{profile:B="default",logger:Q}={})=>{return Boolean(A)&&typeof A==="object"&&typeof A.role_arn==="string"&&["undefined","string"].indexOf(typeof A.role_session_name)>-1&&["undefined","string"].indexOf(typeof A.external_id)>-1&&["undefined","string"].indexOf(typeof A.mfa_serial)>-1&&(b24(A,{profile:B,logger:Q})||f24(A,{profile:B,logger:Q}))},"isAssumeRoleProfile"),b24=pG((A,{profile:B,logger:Q})=>{let D=typeof A.source_profile==="string"&&typeof A.credential_source==="undefined";if(D)Q?.debug?.(`    ${B} isAssumeRoleWithSourceProfile source_profile=${A.source_profile}`);return D},"isAssumeRoleWithSourceProfile"),f24=pG((A,{profile:B,logger:Q})=>{let D=typeof A.credential_source==="string"&&typeof A.source_profile==="undefined";if(D)Q?.debug?.(`    ${B} isCredentialSourceProfile credential_source=${A.credential_source}`);return D},"isCredentialSourceProfile"),h24=pG(async(A,B,Q,D={})=>{Q.logger?.debug("@aws-sdk/credential-provider-ini - resolveAssumeRoleCredentials (STS)");let Z=B[A],{source_profile:G,region:F}=Z;if(!Q.roleAssumer){let{getDefaultRoleAssumer:Y}=await Promise.resolve().then(()=>Pk(yw1()));Q.roleAssumer=Y({...Q.clientConfig,credentialProviderLogger:Q.logger,parentClientConfig:{...Q?.parentClientConfig,region:F??Q?.parentClientConfig?.region}},Q.clientPlugins)}if(G&&G in D)throw new y41.CredentialsProviderError(`Detected a cycle attempting to resolve credentials for profile ${EQ0.getProfileName(Q)}. Profiles visited: `+Object.keys(D).join(", "),{logger:Q.logger});Q.logger?.debug(`@aws-sdk/credential-provider-ini - finding credential resolver using ${G?`source_profile=[${G}]`:`profile=[${A}]`}`);let I=G?JaA(G,B,Q,{...D,[G]:!0},GaA(B[G]??{})):(await x24(Z.credential_source,A,Q.logger)(Q))();if(GaA(Z))return I.then((Y)=>Sk.setCredentialFeature(Y,"CREDENTIALS_PROFILE_SOURCE_PROFILE","o"));else{let Y={RoleArn:Z.role_arn,RoleSessionName:Z.role_session_name||`aws-sdk-js-${Date.now()}`,ExternalId:Z.external_id,DurationSeconds:parseInt(Z.duration_seconds||"3600",10)},{mfa_serial:W}=Z;if(W){if(!Q.mfaCodeProvider)throw new y41.CredentialsProviderError(`Profile ${A} requires multi-factor authentication, but no MFA code callback was provided.`,{logger:Q.logger,tryNextLink:!1});Y.SerialNumber=W,Y.TokenCode=await Q.mfaCodeProvider(W)}let J=await I;return Q.roleAssumer(J,Y).then((X)=>Sk.setCredentialFeature(X,"CREDENTIALS_PROFILE_SOURCE_PROFILE","o"))}},"resolveAssumeRoleCredentials"),GaA=pG((A)=>{return!A.role_arn&&!!A.credential_source},"isCredentialSourceWithoutRoleArn"),g24=pG((A)=>Boolean(A)&&typeof A==="object"&&typeof A.credential_process==="string","isProcessProfile"),u24=pG(async(A,B)=>Promise.resolve().then(()=>Pk(_w1())).then(({fromProcess:Q})=>Q({...A,profile:B})().then((D)=>Sk.setCredentialFeature(D,"CREDENTIALS_PROFILE_PROCESS","v"))),"resolveProcessCredentials"),m24=pG(async(A,B,Q={})=>{let{fromSSO:D}=await Promise.resolve().then(()=>Pk(aw1()));return D({profile:A,logger:Q.logger,parentClientConfig:Q.parentClientConfig,clientConfig:Q.clientConfig})().then((Z)=>{if(B.sso_session)return Sk.setCredentialFeature(Z,"CREDENTIALS_PROFILE_SSO","r");else return Sk.setCredentialFeature(Z,"CREDENTIALS_PROFILE_SSO_LEGACY","t")})},"resolveSsoCredentials"),d24=pG((A)=>A&&(typeof A.sso_start_url==="string"||typeof A.sso_account_id==="string"||typeof A.sso_session==="string"||typeof A.sso_region==="string"||typeof A.sso_role_name==="string"),"isSsoProfile"),FaA=pG((A)=>Boolean(A)&&typeof A==="object"&&typeof A.aws_access_key_id==="string"&&typeof A.aws_secret_access_key==="string"&&["undefined","string"].indexOf(typeof A.aws_session_token)>-1&&["undefined","string"].indexOf(typeof A.aws_account_id)>-1,"isStaticCredsProfile"),IaA=pG(async(A,B)=>{B?.logger?.debug("@aws-sdk/credential-provider-ini - resolveStaticCredentials");let Q={accessKeyId:A.aws_access_key_id,secretAccessKey:A.aws_secret_access_key,sessionToken:A.aws_session_token,...A.aws_credential_scope&&{credentialScope:A.aws_credential_scope},...A.aws_account_id&&{accountId:A.aws_account_id}};return Sk.setCredentialFeature(Q,"CREDENTIALS_PROFILE","n")},"resolveStaticCredentials"),c24=pG((A)=>Boolean(A)&&typeof A==="object"&&typeof A.web_identity_token_file==="string"&&typeof A.role_arn==="string"&&["undefined","string"].indexOf(typeof A.role_session_name)>-1,"isWebIdentityProfile"),l24=pG(async(A,B)=>Promise.resolve().then(()=>Pk(j41())).then(({fromTokenFile:Q})=>Q({webIdentityTokenFile:A.web_identity_token_file,roleArn:A.role_arn,roleSessionName:A.role_session_name,roleAssumerWithWebIdentity:B.roleAssumerWithWebIdentity,logger:B.logger,parentClientConfig:B.parentClientConfig})().then((D)=>Sk.setCredentialFeature(D,"CREDENTIALS_PROFILE_STS_WEB_ID_TOKEN","q"))),"resolveWebIdentityCredentials"),JaA=pG(async(A,B,Q,D={},Z=!1)=>{let G=B[A];if(Object.keys(D).length>0&&FaA(G))return IaA(G,Q);if(Z||v24(G,{profile:A,logger:Q.logger}))return h24(A,B,Q,D);if(FaA(G))return IaA(G,Q);if(c24(G))return l24(G,Q);if(g24(G))return u24(Q,A);if(d24(G))return await m24(A,G,Q);throw new y41.CredentialsProviderError(`Could not resolve credentials using profile: [${A}] in configuration/credentials file(s).`,{logger:Q.logger})},"resolveProfileData"),p24=pG((A={})=>async({callerClientConfig:B}={})=>{let Q={...A,parentClientConfig:{...B,...A.parentClientConfig}};Q.logger?.debug("@aws-sdk/credential-provider-ini - fromIni");let D=await EQ0.parseKnownFiles(Q);return JaA(EQ0.getProfileName({profile:A.profile??B?.profile}),D,Q)},"fromIni")});
var UV=E((w41)=>{Object.defineProperty(w41,"__esModule",{value:!0});var Z90=uh();Z90.__exportStar(Oz(),w41);Z90.__exportStar(iB0(),w41);Z90.__exportStar(WcA(),w41)});
var UxA=E((zxA)=>{Object.defineProperty(zxA,"__esModule",{value:!0});zxA.getRuntimeConfig=void 0;var IgQ=mh(),YgQ=IgQ.__importDefault(__A()),VxA=VI(),CxA=oQ1(),$U1=K4(),WgQ=gG(),KxA=u4(),ph=JD(),HxA=k3(),JgQ=uG(),XgQ=sZ(),VgQ=XxA(),CgQ=V6(),KgQ=mG(),HgQ=V6(),zgQ=(A)=>{HgQ.emitWarningIfUnsupportedVersion(process.version);let B=KgQ.resolveDefaultsModeConfig(A),Q=()=>B().then(CgQ.loadConfigsForDefaultMode),D=VgQ.getRuntimeConfig(A);VxA.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile,logger:D.logger};return{...D,...A,runtime:"node",defaultsMode:B,authSchemePreference:A?.authSchemePreference??ph.loadConfig(VxA.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS,Z),bodyLengthChecker:A?.bodyLengthChecker??JgQ.calculateBodyLength,defaultUserAgentProvider:A?.defaultUserAgentProvider??CxA.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:YgQ.default.version}),maxAttempts:A?.maxAttempts??ph.loadConfig(KxA.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??ph.loadConfig($U1.NODE_REGION_CONFIG_OPTIONS,{...$U1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:HxA.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??ph.loadConfig({...KxA.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||XgQ.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??WgQ.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??HxA.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??ph.loadConfig($U1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??ph.loadConfig($U1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??ph.loadConfig(CxA.NODE_APP_ID_CONFIG_OPTIONS,Z)}};zxA.getRuntimeConfig=zgQ});
var V20=E((tY5,NbA)=>{var{defineProperty:kU1,getOwnPropertyDescriptor:umQ,getOwnPropertyNames:zbA}=Object,mmQ=Object.prototype.hasOwnProperty,_U1=(A,B)=>kU1(A,"name",{value:B,configurable:!0}),dmQ=(A,B)=>function Q(){return A&&(B=A[zbA(A)[0]](A=0)),B},EbA=(A,B)=>{for(var Q in B)kU1(A,Q,{get:B[Q],enumerable:!0})},cmQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of zbA(B))if(!mmQ.call(A,Z)&&Z!==Q)kU1(A,Z,{get:()=>B[Z],enumerable:!(D=umQ(B,Z))||D.enumerable})}return A},lmQ=(A)=>cmQ(kU1({},"__esModule",{value:!0}),A),UbA={};EbA(UbA,{GetRoleCredentialsCommand:()=>X20.GetRoleCredentialsCommand,SSOClient:()=>X20.SSOClient});var X20,pmQ=dmQ({"src/loadSso.ts"(){X20=txA()}}),wbA={};EbA(wbA,{fromSSO:()=>nmQ,isSsoProfile:()=>$bA,validateSsoProfile:()=>qbA});NbA.exports=lmQ(wbA);var $bA=_U1((A)=>A&&(typeof A.sso_start_url==="string"||typeof A.sso_account_id==="string"||typeof A.sso_session==="string"||typeof A.sso_region==="string"||typeof A.sso_role_name==="string"),"isSsoProfile"),KbA=Sw(),imQ=CbA(),jw=Q9(),yU1=D3(),G41=!1,HbA=_U1(async({ssoStartUrl:A,ssoSession:B,ssoAccountId:Q,ssoRegion:D,ssoRoleName:Z,ssoClient:G,clientConfig:F,parentClientConfig:I,profile:Y,logger:W})=>{let J,X="To refresh this SSO session run aws sso login with the corresponding profile.";if(B)try{let f=await imQ.fromSso({profile:Y})();J={accessToken:f.token,expiresAt:new Date(f.expiration).toISOString()}}catch(f){throw new jw.CredentialsProviderError(f.message,{tryNextLink:G41,logger:W})}else try{J=await yU1.getSSOTokenFromFile(A)}catch(f){throw new jw.CredentialsProviderError("The SSO session associated with this profile is invalid. To refresh this SSO session run aws sso login with the corresponding profile.",{tryNextLink:G41,logger:W})}if(new Date(J.expiresAt).getTime()-Date.now()<=0)throw new jw.CredentialsProviderError("The SSO session associated with this profile has expired. To refresh this SSO session run aws sso login with the corresponding profile.",{tryNextLink:G41,logger:W});let{accessToken:V}=J,{SSOClient:C,GetRoleCredentialsCommand:K}=await Promise.resolve().then(()=>(pmQ(),UbA)),H=G||new C(Object.assign({},F??{},{logger:F?.logger??I?.logger,region:F?.region??D})),z;try{z=await H.send(new K({accountId:Q,roleName:Z,accessToken:V}))}catch(f){throw new jw.CredentialsProviderError(f,{tryNextLink:G41,logger:W})}let{roleCredentials:{accessKeyId:$,secretAccessKey:L,sessionToken:N,expiration:O,credentialScope:R,accountId:T}={}}=z;if(!$||!L||!N||!O)throw new jw.CredentialsProviderError("SSO returns an invalid temporary credential.",{tryNextLink:G41,logger:W});let j={accessKeyId:$,secretAccessKey:L,sessionToken:N,expiration:new Date(O),...R&&{credentialScope:R},...T&&{accountId:T}};if(B)KbA.setCredentialFeature(j,"CREDENTIALS_SSO","s");else KbA.setCredentialFeature(j,"CREDENTIALS_SSO_LEGACY","u");return j},"resolveSSOCredentials"),qbA=_U1((A,B)=>{let{sso_start_url:Q,sso_account_id:D,sso_region:Z,sso_role_name:G}=A;if(!Q||!D||!Z||!G)throw new jw.CredentialsProviderError(`Profile is configured with invalid SSO credentials. Required parameters "sso_account_id", "sso_region", "sso_role_name", "sso_start_url". Got ${Object.keys(A).join(", ")}
Reference: https://docs.aws.amazon.com/cli/latest/userguide/cli-configure-sso.html`,{tryNextLink:!1,logger:B});return A},"validateSsoProfile"),nmQ=_U1((A={})=>async({callerClientConfig:B}={})=>{A.logger?.debug("@aws-sdk/credential-provider-sso - fromSSO");let{ssoStartUrl:Q,ssoAccountId:D,ssoRegion:Z,ssoRoleName:G,ssoSession:F}=A,{ssoClient:I}=A,Y=yU1.getProfileName({profile:A.profile??B?.profile});if(!Q&&!D&&!Z&&!G&&!F){let J=(await yU1.parseKnownFiles(A))[Y];if(!J)throw new jw.CredentialsProviderError(`Profile ${Y} was not found.`,{logger:A.logger});if(!$bA(J))throw new jw.CredentialsProviderError(`Profile ${Y} is not configured with SSO credentials.`,{logger:A.logger});if(J?.sso_session){let $=(await yU1.loadSsoSessionData(A))[J.sso_session],L=` configurations in profile ${Y} and sso-session ${J.sso_session}`;if(Z&&Z!==$.sso_region)throw new jw.CredentialsProviderError("Conflicting SSO region"+L,{tryNextLink:!1,logger:A.logger});if(Q&&Q!==$.sso_start_url)throw new jw.CredentialsProviderError("Conflicting SSO start_url"+L,{tryNextLink:!1,logger:A.logger});J.sso_region=$.sso_region,J.sso_start_url=$.sso_start_url}let{sso_start_url:X,sso_account_id:V,sso_region:C,sso_role_name:K,sso_session:H}=qbA(J,A.logger);return HbA({ssoStartUrl:X,ssoSession:H,ssoAccountId:V,ssoRegion:C,ssoRoleName:K,ssoClient:I,clientConfig:A.clientConfig,parentClientConfig:A.parentClientConfig,profile:Y})}else if(!Q||!D||!Z||!G)throw new jw.CredentialsProviderError('Incomplete configuration. The fromSSO() argument hash must include "ssoStartUrl", "ssoAccountId", "ssoRegion", "ssoRoleName"',{tryNextLink:!1,logger:A.logger});else return HbA({ssoStartUrl:Q,ssoSession:F,ssoAccountId:D,ssoRegion:Z,ssoRoleName:G,ssoClient:I,clientConfig:A.clientConfig,parentClientConfig:A.parentClientConfig,profile:Y})},"fromSSO")});
var V6=E((nI5,PA0)=>{var{defineProperty:JU1,getOwnPropertyDescriptor:IbQ,getOwnPropertyNames:YbQ}=Object,WbQ=Object.prototype.hasOwnProperty,E8=(A,B)=>JU1(A,"name",{value:B,configurable:!0}),JbQ=(A,B)=>{for(var Q in B)JU1(A,Q,{get:B[Q],enumerable:!0})},LA0=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of YbQ(B))if(!WbQ.call(A,Z)&&Z!==Q)JU1(A,Z,{get:()=>B[Z],enumerable:!(D=IbQ(B,Z))||D.enumerable})}return A},XbQ=(A,B,Q)=>(LA0(A,B,"default"),Q&&LA0(Q,B,"default")),VbQ=(A)=>LA0(JU1({},"__esModule",{value:!0}),A),OA0={};JbQ(OA0,{Client:()=>CbQ,Command:()=>TkA,NoOpLogger:()=>jbQ,SENSITIVE_STRING:()=>HbQ,ServiceException:()=>EbQ,_json:()=>RA0,collectBody:()=>NA0.collectBody,convertMap:()=>ybQ,createAggregatedClient:()=>zbQ,decorateServiceException:()=>PkA,emitWarningIfUnsupportedVersion:()=>qbQ,extendedEncodeURIComponent:()=>NA0.extendedEncodeURIComponent,getArrayIfSingleItem:()=>PbQ,getDefaultClientConfiguration:()=>ObQ,getDefaultExtensionConfiguration:()=>jkA,getValueFromTextNode:()=>ykA,isSerializableHeaderValue:()=>SbQ,loadConfigsForDefaultMode:()=>$bQ,map:()=>TA0,resolveDefaultRuntimeConfig:()=>TbQ,resolvedPath:()=>NA0.resolvedPath,serializeDateTime:()=>fbQ,serializeFloat:()=>bbQ,take:()=>kbQ,throwDefaultError:()=>SkA,withBaseException:()=>UbQ});PA0.exports=VbQ(OA0);var OkA=Mw(),CbQ=class{constructor(A){this.config=A,this.middlewareStack=OkA.constructStack()}static{E8(this,"Client")}send(A,B,Q){let D=typeof B!=="function"?B:void 0,Z=typeof B==="function"?B:Q,G=D===void 0&&this.config.cacheMiddleware===!0,F;if(G){if(!this.handlers)this.handlers=new WeakMap;let I=this.handlers;if(I.has(A.constructor))F=I.get(A.constructor);else F=A.resolveMiddleware(this.middlewareStack,this.config,D),I.set(A.constructor,F)}else delete this.handlers,F=A.resolveMiddleware(this.middlewareStack,this.config,D);if(Z)F(A).then((I)=>Z(null,I.output),(I)=>Z(I)).catch(()=>{});else return F(A).then((I)=>I.output)}destroy(){this.config?.requestHandler?.destroy?.(),delete this.handlers}},NA0=M6(),MA0=BA0(),TkA=class{constructor(){this.middlewareStack=OkA.constructStack()}static{E8(this,"Command")}static classBuilder(){return new KbQ}resolveMiddlewareWithContext(A,B,Q,{middlewareFn:D,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,smithyContext:Y,additionalContext:W,CommandCtor:J}){for(let H of D.bind(this)(J,A,B,Q))this.middlewareStack.use(H);let X=A.concat(this.middlewareStack),{logger:V}=B,C={logger:V,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,[MA0.SMITHY_CONTEXT_KEY]:{commandInstance:this,...Y},...W},{requestHandler:K}=B;return X.resolve((H)=>K.handle(H.request,Q||{}),C)}},KbQ=class{constructor(){this._init=()=>{},this._ep={},this._middlewareFn=()=>[],this._commandName="",this._clientName="",this._additionalContext={},this._smithyContext={},this._inputFilterSensitiveLog=(A)=>A,this._outputFilterSensitiveLog=(A)=>A,this._serializer=null,this._deserializer=null}static{E8(this,"ClassBuilder")}init(A){this._init=A}ep(A){return this._ep=A,this}m(A){return this._middlewareFn=A,this}s(A,B,Q={}){return this._smithyContext={service:A,operation:B,...Q},this}c(A={}){return this._additionalContext=A,this}n(A,B){return this._clientName=A,this._commandName=B,this}f(A=(Q)=>Q,B=(Q)=>Q){return this._inputFilterSensitiveLog=A,this._outputFilterSensitiveLog=B,this}ser(A){return this._serializer=A,this}de(A){return this._deserializer=A,this}sc(A){return this._operationSchema=A,this._smithyContext.operationSchema=A,this}build(){let A=this,B;return B=class extends TkA{constructor(...[Q]){super();this.serialize=A._serializer,this.deserialize=A._deserializer,this.input=Q??{},A._init(this),this.schema=A._operationSchema}static{E8(this,"CommandRef")}static getEndpointParameterInstructions(){return A._ep}resolveMiddleware(Q,D,Z){return this.resolveMiddlewareWithContext(Q,D,Z,{CommandCtor:B,middlewareFn:A._middlewareFn,clientName:A._clientName,commandName:A._commandName,inputFilterSensitiveLog:A._inputFilterSensitiveLog,outputFilterSensitiveLog:A._outputFilterSensitiveLog,smithyContext:A._smithyContext,additionalContext:A._additionalContext})}}}},HbQ="***SensitiveInformation***",zbQ=E8((A,B)=>{for(let Q of Object.keys(A)){let D=A[Q],Z=E8(async function(F,I,Y){let W=new D(F);if(typeof I==="function")this.send(W,I);else if(typeof Y==="function"){if(typeof I!=="object")throw new Error(`Expected http options but got ${typeof I}`);this.send(W,I||{},Y)}else return this.send(W,I)},"methodImpl"),G=(Q[0].toLowerCase()+Q.slice(1)).replace(/Command$/,"");B.prototype[G]=Z}},"createAggregatedClient"),EbQ=class A extends Error{static{E8(this,"ServiceException")}constructor(B){super(B.message);Object.setPrototypeOf(this,Object.getPrototypeOf(this).constructor.prototype),this.name=B.name,this.$fault=B.$fault,this.$metadata=B.$metadata}static isInstance(B){if(!B)return!1;let Q=B;return A.prototype.isPrototypeOf(Q)||Boolean(Q.$fault)&&Boolean(Q.$metadata)&&(Q.$fault==="client"||Q.$fault==="server")}static[Symbol.hasInstance](B){if(!B)return!1;let Q=B;if(this===A)return A.isInstance(B);if(A.isInstance(B)){if(Q.name&&this.name)return this.prototype.isPrototypeOf(B)||Q.name===this.name;return this.prototype.isPrototypeOf(B)}return!1}},PkA=E8((A,B={})=>{Object.entries(B).filter(([,D])=>D!==void 0).forEach(([D,Z])=>{if(A[D]==null||A[D]==="")A[D]=Z});let Q=A.message||A.Message||"UnknownError";return A.message=Q,delete A.Message,A},"decorateServiceException"),SkA=E8(({output:A,parsedBody:B,exceptionCtor:Q,errorCode:D})=>{let Z=wbQ(A),G=Z.httpStatusCode?Z.httpStatusCode+"":void 0,F=new Q({name:B?.code||B?.Code||D||G||"UnknownError",$fault:"client",$metadata:Z});throw PkA(F,B)},"throwDefaultError"),UbQ=E8((A)=>{return({output:B,parsedBody:Q,errorCode:D})=>{SkA({output:B,parsedBody:Q,exceptionCtor:A,errorCode:D})}},"withBaseException"),wbQ=E8((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),$bQ=E8((A)=>{switch(A){case"standard":return{retryMode:"standard",connectionTimeout:3100};case"in-region":return{retryMode:"standard",connectionTimeout:1100};case"cross-region":return{retryMode:"standard",connectionTimeout:3100};case"mobile":return{retryMode:"standard",connectionTimeout:30000};default:return{}}},"loadConfigsForDefaultMode"),RkA=!1,qbQ=E8((A)=>{if(A&&!RkA&&parseInt(A.substring(1,A.indexOf(".")))<16)RkA=!0},"emitWarningIfUnsupportedVersion"),NbQ=E8((A)=>{let B=[];for(let Q in MA0.AlgorithmId){let D=MA0.AlgorithmId[Q];if(A[D]===void 0)continue;B.push({algorithmId:()=>D,checksumConstructor:()=>A[D]})}return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),LbQ=E8((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),MbQ=E8((A)=>{return{setRetryStrategy(B){A.retryStrategy=B},retryStrategy(){return A.retryStrategy}}},"getRetryConfiguration"),RbQ=E8((A)=>{let B={};return B.retryStrategy=A.retryStrategy(),B},"resolveRetryRuntimeConfig"),jkA=E8((A)=>{return Object.assign(NbQ(A),MbQ(A))},"getDefaultExtensionConfiguration"),ObQ=jkA,TbQ=E8((A)=>{return Object.assign(LbQ(A),RbQ(A))},"resolveDefaultRuntimeConfig"),PbQ=E8((A)=>Array.isArray(A)?A:[A],"getArrayIfSingleItem"),ykA=E8((A)=>{for(let Q in A)if(A.hasOwnProperty(Q)&&A[Q]["#text"]!==void 0)A[Q]=A[Q]["#text"];else if(typeof A[Q]==="object"&&A[Q]!==null)A[Q]=ykA(A[Q]);return A},"getValueFromTextNode"),SbQ=E8((A)=>{return A!=null},"isSerializableHeaderValue"),jbQ=class{static{E8(this,"NoOpLogger")}trace(){}debug(){}info(){}warn(){}error(){}};function TA0(A,B,Q){let D,Z,G;if(typeof B==="undefined"&&typeof Q==="undefined")D={},G=A;else if(D=A,typeof B==="function")return Z=B,G=Q,_bQ(D,Z,G);else G=B;for(let F of Object.keys(G)){if(!Array.isArray(G[F])){D[F]=G[F];continue}kkA(D,null,G,F)}return D}E8(TA0,"map");var ybQ=E8((A)=>{let B={};for(let[Q,D]of Object.entries(A||{}))B[Q]=[,D];return B},"convertMap"),kbQ=E8((A,B)=>{let Q={};for(let D in B)kkA(Q,A,B,D);return Q},"take"),_bQ=E8((A,B,Q)=>{return TA0(A,Object.entries(Q).reduce((D,[Z,G])=>{if(Array.isArray(G))D[Z]=G;else if(typeof G==="function")D[Z]=[B,G()];else D[Z]=[B,G];return D},{}))},"mapWithFilter"),kkA=E8((A,B,Q,D)=>{if(B!==null){let F=Q[D];if(typeof F==="function")F=[,F];let[I=xbQ,Y=vbQ,W=D]=F;if(typeof I==="function"&&I(B[W])||typeof I!=="function"&&!!I)A[D]=Y(B[W]);return}let[Z,G]=Q[D];if(typeof G==="function"){let F,I=Z===void 0&&(F=G())!=null,Y=typeof Z==="function"&&!!Z(void 0)||typeof Z!=="function"&&!!Z;if(I)A[D]=F;else if(Y)A[D]=G()}else{let F=Z===void 0&&G!=null,I=typeof Z==="function"&&!!Z(G)||typeof Z!=="function"&&!!Z;if(F||I)A[D]=G}},"applyInstruction"),xbQ=E8((A)=>A!=null,"nonNullish"),vbQ=E8((A)=>A,"pass"),bbQ=E8((A)=>{if(A!==A)return"NaN";switch(A){case 1/0:return"Infinity";case-1/0:return"-Infinity";default:return A}},"serializeFloat"),fbQ=E8((A)=>A.toISOString().replace(".000Z","Z"),"serializeDateTime"),RA0=E8((A)=>{if(A==null)return{};if(Array.isArray(A))return A.filter((B)=>B!=null).map(RA0);if(typeof A==="object"){let B={};for(let Q of Object.keys(A)){if(A[Q]==null)continue;B[Q]=RA0(A[Q])}return B}return A},"_json");XbQ(OA0,X6(),PA0.exports)});
var VI=E((rQ1)=>{Object.defineProperty(rQ1,"__esModule",{value:!0});var hA0=mh();hA0.__exportStar(Sw(),rQ1);hA0.__exportStar(wA0(),rQ1);hA0.__exportStar(akA(),rQ1)});
var W20=E((nY5,GbA)=>{var{defineProperty:jU1,getOwnPropertyDescriptor:huQ,getOwnPropertyNames:guQ}=Object,uuQ=Object.prototype.hasOwnProperty,r4=(A,B)=>jU1(A,"name",{value:B,configurable:!0}),muQ=(A,B)=>{for(var Q in B)jU1(A,Q,{get:B[Q],enumerable:!0})},duQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of guQ(B))if(!uuQ.call(A,Z)&&Z!==Q)jU1(A,Z,{get:()=>B[Z],enumerable:!(D=huQ(B,Z))||D.enumerable})}return A},cuQ=(A)=>duQ(jU1({},"__esModule",{value:!0}),A),uvA={};muQ(uvA,{$Command:()=>cvA.Command,AccessDeniedException:()=>lvA,AuthorizationPendingException:()=>pvA,CreateTokenCommand:()=>DbA,CreateTokenRequestFilterSensitiveLog:()=>ivA,CreateTokenResponseFilterSensitiveLog:()=>nvA,ExpiredTokenException:()=>avA,InternalServerException:()=>svA,InvalidClientException:()=>rvA,InvalidGrantException:()=>ovA,InvalidRequestException:()=>tvA,InvalidScopeException:()=>evA,SSOOIDC:()=>ZbA,SSOOIDCClient:()=>dvA,SSOOIDCServiceException:()=>DK,SlowDownException:()=>AbA,UnauthorizedClientException:()=>BbA,UnsupportedGrantTypeException:()=>QbA,__Client:()=>mvA.Client});GbA.exports=cuQ(uvA);var _vA=cQ1(),luQ=lQ1(),puQ=pQ1(),xvA=an(),iuQ=K4(),I20=CB(),nuQ=bG(),auQ=R6(),vvA=u4(),mvA=V6(),bvA=Q20(),suQ=r4((A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,defaultSigningName:"sso-oauth"})},"resolveClientEndpointParameters"),ruQ={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}},ouQ=kvA(),fvA=eQ1(),hvA=EV(),gvA=V6(),tuQ=r4((A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}},"getHttpAuthExtensionConfiguration"),euQ=r4((A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}},"resolveHttpAuthRuntimeConfig"),AmQ=r4((A,B)=>{let Q=Object.assign(fvA.getAwsRegionExtensionConfiguration(A),gvA.getDefaultExtensionConfiguration(A),hvA.getHttpHandlerExtensionConfiguration(A),tuQ(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,fvA.resolveAwsRegionExtensionConfiguration(Q),gvA.resolveDefaultRuntimeConfig(Q),hvA.resolveHttpHandlerRuntimeConfig(Q),euQ(Q))},"resolveRuntimeExtensions"),dvA=class extends mvA.Client{static{r4(this,"SSOOIDCClient")}config;constructor(...[A]){let B=ouQ.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=suQ(B),D=xvA.resolveUserAgentConfig(Q),Z=vvA.resolveRetryConfig(D),G=iuQ.resolveRegionConfig(Z),F=_vA.resolveHostHeaderConfig(G),I=auQ.resolveEndpointConfig(F),Y=bvA.resolveHttpAuthSchemeConfig(I),W=AmQ(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(xvA.getUserAgentPlugin(this.config)),this.middlewareStack.use(vvA.getRetryPlugin(this.config)),this.middlewareStack.use(nuQ.getContentLengthPlugin(this.config)),this.middlewareStack.use(_vA.getHostHeaderPlugin(this.config)),this.middlewareStack.use(luQ.getLoggerPlugin(this.config)),this.middlewareStack.use(puQ.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(I20.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:bvA.defaultSSOOIDCHttpAuthSchemeParametersProvider,identityProviderConfigProvider:r4(async(J)=>new I20.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials}),"identityProviderConfigProvider")})),this.middlewareStack.use(I20.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}},BmQ=V6(),QmQ=R6(),DmQ=j3(),cvA=V6(),Qa=V6(),ZmQ=V6(),DK=class A extends ZmQ.ServiceException{static{r4(this,"SSOOIDCServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},lvA=class A extends DK{static{r4(this,"AccessDeniedException")}name="AccessDeniedException";$fault="client";error;error_description;constructor(B){super({name:"AccessDeniedException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},pvA=class A extends DK{static{r4(this,"AuthorizationPendingException")}name="AuthorizationPendingException";$fault="client";error;error_description;constructor(B){super({name:"AuthorizationPendingException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},ivA=r4((A)=>({...A,...A.clientSecret&&{clientSecret:Qa.SENSITIVE_STRING},...A.refreshToken&&{refreshToken:Qa.SENSITIVE_STRING},...A.codeVerifier&&{codeVerifier:Qa.SENSITIVE_STRING}}),"CreateTokenRequestFilterSensitiveLog"),nvA=r4((A)=>({...A,...A.accessToken&&{accessToken:Qa.SENSITIVE_STRING},...A.refreshToken&&{refreshToken:Qa.SENSITIVE_STRING},...A.idToken&&{idToken:Qa.SENSITIVE_STRING}}),"CreateTokenResponseFilterSensitiveLog"),avA=class A extends DK{static{r4(this,"ExpiredTokenException")}name="ExpiredTokenException";$fault="client";error;error_description;constructor(B){super({name:"ExpiredTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},svA=class A extends DK{static{r4(this,"InternalServerException")}name="InternalServerException";$fault="server";error;error_description;constructor(B){super({name:"InternalServerException",$fault:"server",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},rvA=class A extends DK{static{r4(this,"InvalidClientException")}name="InvalidClientException";$fault="client";error;error_description;constructor(B){super({name:"InvalidClientException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},ovA=class A extends DK{static{r4(this,"InvalidGrantException")}name="InvalidGrantException";$fault="client";error;error_description;constructor(B){super({name:"InvalidGrantException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},tvA=class A extends DK{static{r4(this,"InvalidRequestException")}name="InvalidRequestException";$fault="client";error;error_description;constructor(B){super({name:"InvalidRequestException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},evA=class A extends DK{static{r4(this,"InvalidScopeException")}name="InvalidScopeException";$fault="client";error;error_description;constructor(B){super({name:"InvalidScopeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},AbA=class A extends DK{static{r4(this,"SlowDownException")}name="SlowDownException";$fault="client";error;error_description;constructor(B){super({name:"SlowDownException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},BbA=class A extends DK{static{r4(this,"UnauthorizedClientException")}name="UnauthorizedClientException";$fault="client";error;error_description;constructor(B){super({name:"UnauthorizedClientException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},QbA=class A extends DK{static{r4(this,"UnsupportedGrantTypeException")}name="UnsupportedGrantTypeException";$fault="client";error;error_description;constructor(B){super({name:"UnsupportedGrantTypeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},Y20=VI(),GmQ=CB(),UB=V6(),FmQ=r4(async(A,B)=>{let Q=GmQ.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/token");let Z;return Z=JSON.stringify(UB.take(A,{clientId:[],clientSecret:[],code:[],codeVerifier:[],deviceCode:[],grantType:[],redirectUri:[],refreshToken:[],scope:r4((G)=>UB._json(G),"scope")})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateTokenCommand"),ImQ=r4(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return YmQ(A,B);let Q=UB.map({$metadata:Rz(A)}),D=UB.expectNonNull(UB.expectObject(await Y20.parseJsonBody(A.body,B)),"body"),Z=UB.take(D,{accessToken:UB.expectString,expiresIn:UB.expectInt32,idToken:UB.expectString,refreshToken:UB.expectString,tokenType:UB.expectString});return Object.assign(Q,Z),Q},"de_CreateTokenCommand"),YmQ=r4(async(A,B)=>{let Q={...A,body:await Y20.parseJsonErrorBody(A.body,B)},D=Y20.loadRestJsonErrorCode(A,Q.body);switch(D){case"AccessDeniedException":case"com.amazonaws.ssooidc#AccessDeniedException":throw await JmQ(Q,B);case"AuthorizationPendingException":case"com.amazonaws.ssooidc#AuthorizationPendingException":throw await XmQ(Q,B);case"ExpiredTokenException":case"com.amazonaws.ssooidc#ExpiredTokenException":throw await VmQ(Q,B);case"InternalServerException":case"com.amazonaws.ssooidc#InternalServerException":throw await CmQ(Q,B);case"InvalidClientException":case"com.amazonaws.ssooidc#InvalidClientException":throw await KmQ(Q,B);case"InvalidGrantException":case"com.amazonaws.ssooidc#InvalidGrantException":throw await HmQ(Q,B);case"InvalidRequestException":case"com.amazonaws.ssooidc#InvalidRequestException":throw await zmQ(Q,B);case"InvalidScopeException":case"com.amazonaws.ssooidc#InvalidScopeException":throw await EmQ(Q,B);case"SlowDownException":case"com.amazonaws.ssooidc#SlowDownException":throw await UmQ(Q,B);case"UnauthorizedClientException":case"com.amazonaws.ssooidc#UnauthorizedClientException":throw await wmQ(Q,B);case"UnsupportedGrantTypeException":case"com.amazonaws.ssooidc#UnsupportedGrantTypeException":throw await $mQ(Q,B);default:let Z=Q.body;return WmQ({output:A,parsedBody:Z,errorCode:D})}},"de_CommandError"),WmQ=UB.withBaseException(DK),JmQ=r4(async(A,B)=>{let Q=UB.map({}),D=A.body,Z=UB.take(D,{error:UB.expectString,error_description:UB.expectString});Object.assign(Q,Z);let G=new lvA({$metadata:Rz(A),...Q});return UB.decorateServiceException(G,A.body)},"de_AccessDeniedExceptionRes"),XmQ=r4(async(A,B)=>{let Q=UB.map({}),D=A.body,Z=UB.take(D,{error:UB.expectString,error_description:UB.expectString});Object.assign(Q,Z);let G=new pvA({$metadata:Rz(A),...Q});return UB.decorateServiceException(G,A.body)},"de_AuthorizationPendingExceptionRes"),VmQ=r4(async(A,B)=>{let Q=UB.map({}),D=A.body,Z=UB.take(D,{error:UB.expectString,error_description:UB.expectString});Object.assign(Q,Z);let G=new avA({$metadata:Rz(A),...Q});return UB.decorateServiceException(G,A.body)},"de_ExpiredTokenExceptionRes"),CmQ=r4(async(A,B)=>{let Q=UB.map({}),D=A.body,Z=UB.take(D,{error:UB.expectString,error_description:UB.expectString});Object.assign(Q,Z);let G=new svA({$metadata:Rz(A),...Q});return UB.decorateServiceException(G,A.body)},"de_InternalServerExceptionRes"),KmQ=r4(async(A,B)=>{let Q=UB.map({}),D=A.body,Z=UB.take(D,{error:UB.expectString,error_description:UB.expectString});Object.assign(Q,Z);let G=new rvA({$metadata:Rz(A),...Q});return UB.decorateServiceException(G,A.body)},"de_InvalidClientExceptionRes"),HmQ=r4(async(A,B)=>{let Q=UB.map({}),D=A.body,Z=UB.take(D,{error:UB.expectString,error_description:UB.expectString});Object.assign(Q,Z);let G=new ovA({$metadata:Rz(A),...Q});return UB.decorateServiceException(G,A.body)},"de_InvalidGrantExceptionRes"),zmQ=r4(async(A,B)=>{let Q=UB.map({}),D=A.body,Z=UB.take(D,{error:UB.expectString,error_description:UB.expectString});Object.assign(Q,Z);let G=new tvA({$metadata:Rz(A),...Q});return UB.decorateServiceException(G,A.body)},"de_InvalidRequestExceptionRes"),EmQ=r4(async(A,B)=>{let Q=UB.map({}),D=A.body,Z=UB.take(D,{error:UB.expectString,error_description:UB.expectString});Object.assign(Q,Z);let G=new evA({$metadata:Rz(A),...Q});return UB.decorateServiceException(G,A.body)},"de_InvalidScopeExceptionRes"),UmQ=r4(async(A,B)=>{let Q=UB.map({}),D=A.body,Z=UB.take(D,{error:UB.expectString,error_description:UB.expectString});Object.assign(Q,Z);let G=new AbA({$metadata:Rz(A),...Q});return UB.decorateServiceException(G,A.body)},"de_SlowDownExceptionRes"),wmQ=r4(async(A,B)=>{let Q=UB.map({}),D=A.body,Z=UB.take(D,{error:UB.expectString,error_description:UB.expectString});Object.assign(Q,Z);let G=new BbA({$metadata:Rz(A),...Q});return UB.decorateServiceException(G,A.body)},"de_UnauthorizedClientExceptionRes"),$mQ=r4(async(A,B)=>{let Q=UB.map({}),D=A.body,Z=UB.take(D,{error:UB.expectString,error_description:UB.expectString});Object.assign(Q,Z);let G=new QbA({$metadata:Rz(A),...Q});return UB.decorateServiceException(G,A.body)},"de_UnsupportedGrantTypeExceptionRes"),Rz=r4((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),DbA=class extends cvA.Command.classBuilder().ep(ruQ).m(function(A,B,Q,D){return[DmQ.getSerdePlugin(Q,this.serialize,this.deserialize),QmQ.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSSOOIDCService","CreateToken",{}).n("SSOOIDCClient","CreateTokenCommand").f(ivA,nvA).ser(FmQ).de(ImQ).build(){static{r4(this,"CreateTokenCommand")}},qmQ={CreateTokenCommand:DbA},ZbA=class extends dvA{static{r4(this,"SSOOIDC")}};BmQ.createAggregatedClient(qmQ,ZbA)});
var W90=E((DX5,AtQ)=>{AtQ.exports={name:"@aws-sdk/nested-clients",version:"3.840.0",description:"Nested clients for AWS SDK packages.",main:"./dist-cjs/index.js",module:"./dist-es/index.js",types:"./dist-types/index.d.ts",scripts:{build:"yarn lint && concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'","build:cjs":"node ../../scripts/compilation/inline nested-clients","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4",clean:"rimraf ./dist-* && rimraf *.tsbuildinfo",lint:"node ../../scripts/validation/submodules-linter.js --pkg nested-clients",test:"yarn g:vitest run","test:watch":"yarn g:vitest watch"},engines:{node:">=18.0.0"},author:{name:"AWS SDK for JavaScript Team",url:"https://aws.amazon.com/javascript/"},license:"Apache-2.0",dependencies:{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.840.0","@aws-sdk/middleware-host-header":"3.840.0","@aws-sdk/middleware-logger":"3.840.0","@aws-sdk/middleware-recursion-detection":"3.840.0","@aws-sdk/middleware-user-agent":"3.840.0","@aws-sdk/region-config-resolver":"3.840.0","@aws-sdk/types":"3.840.0","@aws-sdk/util-endpoints":"3.840.0","@aws-sdk/util-user-agent-browser":"3.840.0","@aws-sdk/util-user-agent-node":"3.840.0","@smithy/config-resolver":"^4.1.4","@smithy/core":"^3.6.0","@smithy/fetch-http-handler":"^5.0.4","@smithy/hash-node":"^4.0.4","@smithy/invalid-dependency":"^4.0.4","@smithy/middleware-content-length":"^4.0.4","@smithy/middleware-endpoint":"^4.1.13","@smithy/middleware-retry":"^4.1.14","@smithy/middleware-serde":"^4.0.8","@smithy/middleware-stack":"^4.0.4","@smithy/node-config-provider":"^4.1.3","@smithy/node-http-handler":"^4.0.6","@smithy/protocol-http":"^5.1.2","@smithy/smithy-client":"^4.4.5","@smithy/types":"^4.3.1","@smithy/url-parser":"^4.0.4","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.21","@smithy/util-defaults-mode-node":"^4.0.21","@smithy/util-endpoints":"^3.0.6","@smithy/util-middleware":"^4.0.4","@smithy/util-retry":"^4.0.6","@smithy/util-utf8":"^4.0.0",tslib:"^2.6.2"},devDependencies:{concurrently:"7.0.0","downlevel-dts":"0.10.1",rimraf:"3.0.2",typescript:"~5.8.3"},typesVersions:{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},files:["./sso-oidc.d.ts","./sso-oidc.js","./sts.d.ts","./sts.js","dist-*/**"],browser:{"./dist-es/submodules/sso-oidc/runtimeConfig":"./dist-es/submodules/sso-oidc/runtimeConfig.browser","./dist-es/submodules/sts/runtimeConfig":"./dist-es/submodules/sts/runtimeConfig.browser"},"react-native":{},homepage:"https://github.com/aws/aws-sdk-js-v3/tree/main/packages/nested-clients",repository:{type:"git",url:"https://github.com/aws/aws-sdk-js-v3.git",directory:"packages/nested-clients"},exports:{"./sso-oidc":{types:"./dist-types/submodules/sso-oidc/index.d.ts",module:"./dist-es/submodules/sso-oidc/index.js",node:"./dist-cjs/submodules/sso-oidc/index.js",import:"./dist-es/submodules/sso-oidc/index.js",require:"./dist-cjs/submodules/sso-oidc/index.js"},"./sts":{types:"./dist-types/submodules/sts/index.d.ts",module:"./dist-es/submodules/sts/index.js",node:"./dist-cjs/submodules/sts/index.js",import:"./dist-es/submodules/sts/index.js",require:"./dist-cjs/submodules/sts/index.js"}}}});
var W_A=E((wY5,ifQ)=>{ifQ.exports={name:"@aws-sdk/client-cognito-identity",description:"AWS SDK for JavaScript Cognito Identity Client for Node.js, Browser and React Native",version:"3.840.0",scripts:{build:"concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'","build:cjs":"node ../../scripts/compilation/inline client-cognito-identity","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4",clean:"rimraf ./dist-* && rimraf *.tsbuildinfo","extract:docs":"api-extractor run --local","generate:client":"node ../../scripts/generate-clients/single-service --solo cognito-identity","test:e2e":"yarn g:vitest run -c vitest.config.e2e.ts --mode development","test:e2e:watch":"yarn g:vitest watch -c vitest.config.e2e.ts"},main:"./dist-cjs/index.js",types:"./dist-types/index.d.ts",module:"./dist-es/index.js",sideEffects:!1,dependencies:{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.840.0","@aws-sdk/credential-provider-node":"3.840.0","@aws-sdk/middleware-host-header":"3.840.0","@aws-sdk/middleware-logger":"3.840.0","@aws-sdk/middleware-recursion-detection":"3.840.0","@aws-sdk/middleware-user-agent":"3.840.0","@aws-sdk/region-config-resolver":"3.840.0","@aws-sdk/types":"3.840.0","@aws-sdk/util-endpoints":"3.840.0","@aws-sdk/util-user-agent-browser":"3.840.0","@aws-sdk/util-user-agent-node":"3.840.0","@smithy/config-resolver":"^4.1.4","@smithy/core":"^3.6.0","@smithy/fetch-http-handler":"^5.0.4","@smithy/hash-node":"^4.0.4","@smithy/invalid-dependency":"^4.0.4","@smithy/middleware-content-length":"^4.0.4","@smithy/middleware-endpoint":"^4.1.13","@smithy/middleware-retry":"^4.1.14","@smithy/middleware-serde":"^4.0.8","@smithy/middleware-stack":"^4.0.4","@smithy/node-config-provider":"^4.1.3","@smithy/node-http-handler":"^4.0.6","@smithy/protocol-http":"^5.1.2","@smithy/smithy-client":"^4.4.5","@smithy/types":"^4.3.1","@smithy/url-parser":"^4.0.4","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.21","@smithy/util-defaults-mode-node":"^4.0.21","@smithy/util-endpoints":"^3.0.6","@smithy/util-middleware":"^4.0.4","@smithy/util-retry":"^4.0.6","@smithy/util-utf8":"^4.0.0",tslib:"^2.6.2"},devDependencies:{"@aws-sdk/client-iam":"3.840.0","@tsconfig/node18":"18.2.4","@types/chai":"^4.2.11","@types/node":"^18.19.69",concurrently:"7.0.0","downlevel-dts":"0.10.1",rimraf:"3.0.2",typescript:"~5.8.3"},engines:{node:">=18.0.0"},typesVersions:{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},files:["dist-*/**"],author:{name:"AWS SDK for JavaScript Team",url:"https://aws.amazon.com/javascript/"},license:"Apache-2.0",browser:{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.browser"},"react-native":{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.native"},homepage:"https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-cognito-identity",repository:{type:"git",url:"https://github.com/aws/aws-sdk-js-v3.git",directory:"clients/client-cognito-identity"}}});
var WcA=E((bJ5,YcA)=>{var{defineProperty:qw1,getOwnPropertyDescriptor:mrQ,getOwnPropertyNames:drQ}=Object,crQ=Object.prototype.hasOwnProperty,D8=(A,B)=>qw1(A,"name",{value:B,configurable:!0}),lrQ=(A,B)=>{for(var Q in B)qw1(A,Q,{get:B[Q],enumerable:!0})},prQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of drQ(B))if(!crQ.call(A,Z)&&Z!==Q)qw1(A,Z,{get:()=>B[Z],enumerable:!(D=mrQ(B,Z))||D.enumerable})}return A},irQ=(A)=>prQ(qw1({},"__esModule",{value:!0}),A),tdA={};lrQ(tdA,{AwsEc2QueryProtocol:()=>qoQ,AwsJson1_0Protocol:()=>FoQ,AwsJson1_1Protocol:()=>IoQ,AwsJsonRpcProtocol:()=>Q90,AwsQueryProtocol:()=>DcA,AwsRestJsonProtocol:()=>WoQ,AwsRestXmlProtocol:()=>ToQ,JsonCodec:()=>B90,JsonShapeDeserializer:()=>BcA,JsonShapeSerializer:()=>QcA,XmlCodec:()=>IcA,XmlShapeDeserializer:()=>D90,XmlShapeSerializer:()=>FcA,_toBool:()=>arQ,_toNum:()=>srQ,_toStr:()=>nrQ,awsExpectUnion:()=>XoQ,loadRestJsonErrorCode:()=>A90,loadRestXmlErrorCode:()=>GcA,parseJsonBody:()=>eB0,parseJsonErrorBody:()=>BoQ,parseXmlBody:()=>ZcA,parseXmlErrorBody:()=>RoQ});YcA.exports=irQ(tdA);var nrQ=D8((A)=>{if(A==null)return A;if(typeof A==="number"||typeof A==="bigint"){let B=new Error(`Received number ${A} where a string was expected.`);return B.name="Warning",console.warn(B),String(A)}if(typeof A==="boolean"){let B=new Error(`Received boolean ${A} where a string was expected.`);return B.name="Warning",console.warn(B),String(A)}return A},"_toStr"),arQ=D8((A)=>{if(A==null)return A;if(typeof A==="string"){let B=A.toLowerCase();if(A!==""&&B!=="false"&&B!=="true"){let Q=new Error(`Received string "${A}" where a boolean was expected.`);Q.name="Warning",console.warn(Q)}return A!==""&&B!=="false"}return A},"_toBool"),srQ=D8((A)=>{if(A==null)return A;if(typeof A==="string"){let B=Number(A);if(B.toString()!==A){let Q=new Error(`Received string "${A}" where a number was expected.`);return Q.name="Warning",console.warn(Q),A}return B}return A},"_toNum"),rrQ=M6(),Ea=_Q(),orQ=PY(),Ag=class{static{D8(this,"SerdeContextConfig")}serdeContext;setSerdeContext(A){this.serdeContext=A}},z41=_Q(),Ua=X6(),trQ=th(),erQ=X6();function edA(A,B,Q){if(Q?.source){let D=Q.source;if(typeof B==="number"){if(B>Number.MAX_SAFE_INTEGER||B<Number.MIN_SAFE_INTEGER||D!==String(B))if(D.includes("."))return new erQ.NumericValue(D,"bigDecimal");else return BigInt(D)}}return B}D8(edA,"jsonReviver");var AoQ=w8(),AcA=D8((A,B)=>AoQ.collectBody(A,B).then((Q)=>B.utf8Encoder(Q)),"collectBodyString"),eB0=D8((A,B)=>AcA(A,B).then((Q)=>{if(Q.length)try{return JSON.parse(Q)}catch(D){if(D?.name==="SyntaxError")Object.defineProperty(D,"$responseBodyText",{value:Q});throw D}return{}}),"parseJsonBody"),BoQ=D8(async(A,B)=>{let Q=await eB0(A,B);return Q.message=Q.message??Q.Message,Q},"parseJsonErrorBody"),A90=D8((A,B)=>{let Q=D8((G,F)=>Object.keys(G).find((I)=>I.toLowerCase()===F.toLowerCase()),"findKey"),D=D8((G)=>{let F=G;if(typeof F==="number")F=F.toString();if(F.indexOf(",")>=0)F=F.split(",")[0];if(F.indexOf(":")>=0)F=F.split(":")[0];if(F.indexOf("#")>=0)F=F.split("#")[1];return F},"sanitizeErrorCode"),Z=Q(A.headers,"x-amzn-errortype");if(Z!==void 0)return D(A.headers[Z]);if(B&&typeof B==="object"){let G=Q(B,"code");if(G&&B[G]!==void 0)return D(B[G]);if(B.__type!==void 0)return D(B.__type)}},"loadRestJsonErrorCode"),BcA=class extends Ag{constructor(A){super();this.settings=A}static{D8(this,"JsonShapeDeserializer")}async read(A,B){return this._read(A,typeof B==="string"?JSON.parse(B,edA):await eB0(B,this.serdeContext))}readObject(A,B){return this._read(A,B)}_read(A,B){let Q=B!==null&&typeof B==="object",D=z41.NormalizedSchema.of(A);if(D.isListSchema()&&Array.isArray(B)){let G=D.getValueSchema(),F=[],I=!!D.getMergedTraits().sparse;for(let Y of B)if(I||Y!=null)F.push(this._read(G,Y));return F}else if(D.isMapSchema()&&Q){let G=D.getValueSchema(),F={},I=!!D.getMergedTraits().sparse;for(let[Y,W]of Object.entries(B))if(I||W!=null)F[Y]=this._read(G,W);return F}else if(D.isStructSchema()&&Q){let G={};for(let[F,I]of D.structIterator()){let Y=this.settings.jsonName?I.getMergedTraits().jsonName??F:F,W=this._read(I,B[Y]);if(W!=null)G[F]=W}return G}if(D.isBlobSchema()&&typeof B==="string")return trQ.fromBase64(B);let Z=D.getMergedTraits().mediaType;if(D.isStringSchema()&&typeof B==="string"&&Z){if(Z==="application/json"||Z.endsWith("+json"))return Ua.LazyJsonString.from(B)}if(D.isTimestampSchema()){let G=this.settings.timestampFormat;switch(G.useTrait?D.getSchema()===z41.SCHEMA.TIMESTAMP_DEFAULT?G.default:D.getSchema()??G.default:G.default){case z41.SCHEMA.TIMESTAMP_DATE_TIME:return Ua.parseRfc3339DateTimeWithOffset(B);case z41.SCHEMA.TIMESTAMP_HTTP_DATE:return Ua.parseRfc7231DateTime(B);case z41.SCHEMA.TIMESTAMP_EPOCH_SECONDS:return Ua.parseEpochTimestamp(B);default:return console.warn("Missing timestamp format, parsing value with Date constructor:",B),new Date(B)}}if(D.isBigIntegerSchema()&&(typeof B==="number"||typeof B==="string"))return BigInt(B);if(D.isBigDecimalSchema()&&B!=null){if(B instanceof Ua.NumericValue)return B;return new Ua.NumericValue(String(B),"bigDecimal")}if(D.isNumericSchema()&&typeof B==="string")switch(B){case"Infinity":return 1/0;case"-Infinity":return-1/0;case"NaN":return NaN}return B}},wa=_Q(),QoQ=X6(),DoQ=X6(),ZoQ=X6(),adA=String.fromCharCode(925),GoQ=class{static{D8(this,"JsonReplacer")}values=new Map;counter=0;stage=0;createReplacer(){if(this.stage===1)throw new Error("@aws-sdk/core/protocols - JsonReplacer already created.");if(this.stage===2)throw new Error("@aws-sdk/core/protocols - JsonReplacer exhausted.");return this.stage=1,(A,B)=>{if(B instanceof ZoQ.NumericValue){let Q=`${adA+NaN+this.counter++}_`+B.string;return this.values.set(`"${Q}"`,B.string),Q}if(typeof B==="bigint"){let Q=B.toString(),D=`${adA+"b"+this.counter++}_`+Q;return this.values.set(`"${D}"`,Q),D}return B}}replaceInJson(A){if(this.stage===0)throw new Error("@aws-sdk/core/protocols - JsonReplacer not created yet.");if(this.stage===2)throw new Error("@aws-sdk/core/protocols - JsonReplacer exhausted.");if(this.stage=2,this.counter===0)return A;for(let[B,Q]of this.values)A=A.replace(B,Q);return A}},QcA=class extends Ag{constructor(A){super();this.settings=A}static{D8(this,"JsonShapeSerializer")}buffer;rootSchema;write(A,B){this.rootSchema=wa.NormalizedSchema.of(A),this.buffer=this._write(this.rootSchema,B)}flush(){if(this.rootSchema?.isStructSchema()||this.rootSchema?.isDocumentSchema()){let A=new GoQ;return A.replaceInJson(JSON.stringify(this.buffer,A.createReplacer(),0))}return this.buffer}_write(A,B,Q){let D=B!==null&&typeof B==="object",Z=wa.NormalizedSchema.of(A);if(Z.isListSchema()&&Array.isArray(B)){let F=Z.getValueSchema(),I=[],Y=!!Z.getMergedTraits().sparse;for(let W of B)if(Y||W!=null)I.push(this._write(F,W));return I}else if(Z.isMapSchema()&&D){let F=Z.getValueSchema(),I={},Y=!!Z.getMergedTraits().sparse;for(let[W,J]of Object.entries(B))if(Y||J!=null)I[W]=this._write(F,J);return I}else if(Z.isStructSchema()&&D){let F={};for(let[I,Y]of Z.structIterator()){let W=this.settings.jsonName?Y.getMergedTraits().jsonName??I:I,J=this._write(Y,B[I],Z);if(J!==void 0)F[W]=J}return F}if(B===null&&Q?.isStructSchema())return;if(Z.isBlobSchema()&&(B instanceof Uint8Array||typeof B==="string")){if(Z===this.rootSchema)return B;if(!this.serdeContext?.base64Encoder)throw new Error("Missing base64Encoder in serdeContext");return this.serdeContext?.base64Encoder(B)}if(Z.isTimestampSchema()&&B instanceof Date){let F=this.settings.timestampFormat;switch(F.useTrait?Z.getSchema()===wa.SCHEMA.TIMESTAMP_DEFAULT?F.default:Z.getSchema()??F.default:F.default){case wa.SCHEMA.TIMESTAMP_DATE_TIME:return B.toISOString().replace(".000Z","Z");case wa.SCHEMA.TIMESTAMP_HTTP_DATE:return QoQ.dateToUtcString(B);case wa.SCHEMA.TIMESTAMP_EPOCH_SECONDS:return B.getTime()/1000;default:return console.warn("Missing timestamp format, using epoch seconds",B),B.getTime()/1000}}if(Z.isNumericSchema()&&typeof B==="number"){if(Math.abs(B)===1/0||isNaN(B))return String(B)}let G=Z.getMergedTraits().mediaType;if(Z.isStringSchema()&&typeof B==="string"&&G){if(G==="application/json"||G.endsWith("+json"))return DoQ.LazyJsonString.from(B)}return B}},B90=class extends Ag{constructor(A){super();this.settings=A}static{D8(this,"JsonCodec")}createSerializer(){let A=new QcA(this.settings);return A.setSerdeContext(this.serdeContext),A}createDeserializer(){let A=new BcA(this.settings);return A.setSerdeContext(this.serdeContext),A}},Q90=class extends rrQ.RpcProtocol{static{D8(this,"AwsJsonRpcProtocol")}serializer;deserializer;codec;constructor({defaultNamespace:A}){super({defaultNamespace:A});this.codec=new B90({timestampFormat:{useTrait:!0,default:Ea.SCHEMA.TIMESTAMP_EPOCH_SECONDS},jsonName:!1}),this.serializer=this.codec.createSerializer(),this.deserializer=this.codec.createDeserializer()}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q);if(!D.path.endsWith("/"))D.path+="/";if(Object.assign(D.headers,{"content-type":`application/x-amz-json-${this.getJsonRpcVersion()}`,"x-amz-target":(this.getJsonRpcVersion()==="1.0"?"JsonRpc10.":"JsonProtocol.")+Ea.NormalizedSchema.of(A).getName()}),Ea.deref(A.input)==="unit"||!D.body)D.body="{}";try{D.headers["content-length"]=String(orQ.calculateBodyLength(D.body))}catch(Z){}return D}getPayloadCodec(){return this.codec}async handleError(A,B,Q,D,Z){let G=A90(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=Ea.TypeRegistry.for(F),W;try{W=Y.getSchema(G)}catch(K){let H=Ea.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(H){let z=H.ctor;throw Object.assign(new z(I),D)}throw new Error(I)}let J=Ea.NormalizedSchema.of(W),X=D.message??D.Message??"Unknown",V=new W.ctor(X);await this.deserializeHttpMessage(W,B,Q,D);let C={};for(let[K,H]of J.structIterator()){let z=H.getMergedTraits().jsonName??K;C[K]=this.codec.createDeserializer().readObject(H,D[z])}throw Object.assign(V,{$metadata:Z,$response:Q,$fault:J.getMergedTraits().error,message:X,...C}),V}},FoQ=class extends Q90{static{D8(this,"AwsJson1_0Protocol")}constructor({defaultNamespace:A}){super({defaultNamespace:A})}getShapeId(){return"aws.protocols#awsJson1_0"}getJsonRpcVersion(){return"1.0"}},IoQ=class extends Q90{static{D8(this,"AwsJson1_1Protocol")}constructor({defaultNamespace:A}){super({defaultNamespace:A})}getShapeId(){return"aws.protocols#awsJson1_1"}getJsonRpcVersion(){return"1.1"}},sB0=M6(),E41=_Q(),YoQ=PY(),WoQ=class extends sB0.HttpBindingProtocol{static{D8(this,"AwsRestJsonProtocol")}serializer;deserializer;codec;constructor({defaultNamespace:A}){super({defaultNamespace:A});let B={timestampFormat:{useTrait:!0,default:E41.SCHEMA.TIMESTAMP_EPOCH_SECONDS},httpBindings:!0,jsonName:!0};this.codec=new B90(B),this.serializer=new sB0.HttpInterceptingShapeSerializer(this.codec.createSerializer(),B),this.deserializer=new sB0.HttpInterceptingShapeDeserializer(this.codec.createDeserializer(),B)}getShapeId(){return"aws.protocols#restJson1"}getPayloadCodec(){return this.codec}setSerdeContext(A){this.codec.setSerdeContext(A),super.setSerdeContext(A)}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q),Z=E41.NormalizedSchema.of(A.input),G=Z.getMemberSchemas();if(!D.headers["content-type"]){let F=Object.values(G).find((I)=>{return!!I.getMergedTraits().httpPayload});if(F){let I=F.getMergedTraits().mediaType;if(I)D.headers["content-type"]=I;else if(F.isStringSchema())D.headers["content-type"]="text/plain";else if(F.isBlobSchema())D.headers["content-type"]="application/octet-stream";else D.headers["content-type"]="application/json"}else if(!Z.isUnitSchema()){if(Object.values(G).find((Y)=>{let{httpQuery:W,httpQueryParams:J,httpHeader:X,httpLabel:V,httpPrefixHeaders:C}=Y.getMergedTraits();return!W&&!J&&!X&&!V&&C===void 0}))D.headers["content-type"]="application/json"}}if(D.headers["content-type"]&&!D.body)D.body="{}";if(D.body)try{D.headers["content-length"]=String(YoQ.calculateBodyLength(D.body))}catch(F){}return D}async handleError(A,B,Q,D,Z){let G=A90(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=E41.TypeRegistry.for(F),W;try{W=Y.getSchema(G)}catch(K){let H=E41.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(H){let z=H.ctor;throw Object.assign(new z(I),D)}throw new Error(I)}let J=E41.NormalizedSchema.of(W),X=D.message??D.Message??"Unknown",V=new W.ctor(X);await this.deserializeHttpMessage(W,B,Q,D);let C={};for(let[K,H]of J.structIterator()){let z=H.getMergedTraits().jsonName??K;C[K]=this.codec.createDeserializer().readObject(H,D[z])}throw Object.assign(V,{$metadata:Z,$response:Q,$fault:J.getMergedTraits().error,message:X,...C}),V}},JoQ=w8(),XoQ=D8((A)=>{if(A==null)return;if(typeof A==="object"&&"__type"in A)delete A.__type;return JoQ.expectUnion(A)},"awsExpectUnion"),rB0=M6(),Mk=_Q(),VoQ=PY(),CoQ=M6(),sdA=_Q(),KoQ=w8(),HoQ=cB(),zoQ=uN(),D90=class extends Ag{constructor(A){super();this.settings=A,this.stringDeserializer=new CoQ.FromStringShapeDeserializer(A)}static{D8(this,"XmlShapeDeserializer")}stringDeserializer;setSerdeContext(A){this.serdeContext=A,this.stringDeserializer.setSerdeContext(A)}read(A,B,Q){let D=sdA.NormalizedSchema.of(A),Z=D.getMemberSchemas();if(D.isStructSchema()&&D.isMemberSchema()&&!!Object.values(Z).find((Y)=>{return!!Y.getMemberTraits().eventPayload})){let Y={},W=Object.keys(Z)[0];if(Z[W].isBlobSchema())Y[W]=B;else Y[W]=this.read(Z[W],B);return Y}let F=(this.serdeContext?.utf8Encoder??HoQ.toUtf8)(B),I=this.parseXml(F);return this.readSchema(A,Q?I[Q]:I)}readSchema(A,B){let Q=sdA.NormalizedSchema.of(A),D=Q.getMergedTraits(),Z=Q.getSchema();if(Q.isListSchema()&&!Array.isArray(B))return this.readSchema(Z,[B]);if(B==null)return B;if(typeof B==="object"){let G=!!D.sparse,F=!!D.xmlFlattened;if(Q.isListSchema()){let Y=Q.getValueSchema(),W=[],J=Y.getMergedTraits().xmlName??"member",X=F?B:(B[0]??B)[J],V=Array.isArray(X)?X:[X];for(let C of V)if(C!=null||G)W.push(this.readSchema(Y,C));return W}let I={};if(Q.isMapSchema()){let Y=Q.getKeySchema(),W=Q.getValueSchema(),J;if(F)J=Array.isArray(B)?B:[B];else J=Array.isArray(B.entry)?B.entry:[B.entry];let X=Y.getMergedTraits().xmlName??"key",V=W.getMergedTraits().xmlName??"value";for(let C of J){let K=C[X],H=C[V];if(H!=null||G)I[K]=this.readSchema(W,H)}return I}if(Q.isStructSchema()){for(let[Y,W]of Q.structIterator()){let J=W.getMergedTraits(),X=!J.httpPayload?W.getMemberTraits().xmlName??Y:J.xmlName??W.getName();if(B[X]!=null)I[Y]=this.readSchema(W,B[X])}return I}if(Q.isDocumentSchema())return B;throw new Error(`@aws-sdk/core/protocols - xml deserializer unhandled schema type for ${Q.getName(!0)}`)}else{if(Q.isListSchema())return[];else if(Q.isMapSchema()||Q.isStructSchema())return{};return this.stringDeserializer.read(Q,B)}}parseXml(A){if(A.length){let B=new zoQ.XMLParser({attributeNamePrefix:"",htmlEntities:!0,ignoreAttributes:!1,ignoreDeclaration:!0,parseTagValue:!1,trimValues:!1,tagValueProcessor:D8((F,I)=>I.trim()===""&&I.includes(`
`)?"":void 0,"tagValueProcessor")});B.addEntity("#xD","\r"),B.addEntity("#10",`
`);let Q;try{Q=B.parse(A,!0)}catch(F){if(F&&typeof F==="object")Object.defineProperty(F,"$responseBodyText",{value:A});throw F}let D="#text",Z=Object.keys(Q)[0],G=Q[Z];if(G[D])G[Z]=G[D],delete G[D];return KoQ.getValueFromTextNode(G)}return{}}},oB0=M6(),$w1=_Q(),EoQ=X6(),UoQ=w8(),woQ=th(),$oQ=class extends Ag{constructor(A){super();this.settings=A}static{D8(this,"QueryShapeSerializer")}buffer;write(A,B,Q=""){if(this.buffer===void 0)this.buffer="";let D=$w1.NormalizedSchema.of(A);if(Q&&!Q.endsWith("."))Q+=".";if(D.isBlobSchema()){if(typeof B==="string"||B instanceof Uint8Array)this.writeKey(Q),this.writeValue((this.serdeContext?.base64Encoder??woQ.toBase64)(B))}else if(D.isBooleanSchema()||D.isNumericSchema()||D.isStringSchema()){if(B!=null)this.writeKey(Q),this.writeValue(String(B))}else if(D.isBigIntegerSchema()){if(B!=null)this.writeKey(Q),this.writeValue(String(B))}else if(D.isBigDecimalSchema()){if(B!=null)this.writeKey(Q),this.writeValue(B instanceof EoQ.NumericValue?B.string:String(B))}else if(D.isTimestampSchema()){if(B instanceof Date)switch(this.writeKey(Q),oB0.determineTimestampFormat(D,this.settings)){case $w1.SCHEMA.TIMESTAMP_DATE_TIME:this.writeValue(B.toISOString().replace(".000Z","Z"));break;case $w1.SCHEMA.TIMESTAMP_HTTP_DATE:this.writeValue(UoQ.dateToUtcString(B));break;case $w1.SCHEMA.TIMESTAMP_EPOCH_SECONDS:this.writeValue(String(B.getTime()/1000));break}}else if(D.isDocumentSchema())throw new Error(`@aws-sdk/core/protocols - QuerySerializer unsupported document type ${D.getName(!0)}`);else if(D.isListSchema()){if(Array.isArray(B))if(B.length===0){if(this.settings.serializeEmptyLists)this.writeKey(Q),this.writeValue("")}else{let Z=D.getValueSchema(),G=this.settings.flattenLists||D.getMergedTraits().xmlFlattened,F=1;for(let I of B){if(I==null)continue;let Y=this.getKey("member",Z.getMergedTraits().xmlName),W=G?`${Q}${F}`:`${Q}${Y}.${F}`;this.write(Z,I,W),++F}}}else if(D.isMapSchema()){if(B&&typeof B==="object"){let Z=D.getKeySchema(),G=D.getValueSchema(),F=D.getMergedTraits().xmlFlattened,I=1;for(let[Y,W]of Object.entries(B)){if(W==null)continue;let J=this.getKey("key",Z.getMergedTraits().xmlName),X=F?`${Q}${I}.${J}`:`${Q}entry.${I}.${J}`,V=this.getKey("value",G.getMergedTraits().xmlName),C=F?`${Q}${I}.${V}`:`${Q}entry.${I}.${V}`;this.write(Z,Y,X),this.write(G,W,C),++I}}}else if(D.isStructSchema()){if(B&&typeof B==="object")for(let[Z,G]of D.structIterator()){if(B[Z]==null)continue;let F=this.getKey(Z,G.getMergedTraits().xmlName),I=`${Q}${F}`;this.write(G,B[Z],I)}}else if(D.isUnitSchema());else throw new Error(`@aws-sdk/core/protocols - QuerySerializer unrecognized schema type ${D.getName(!0)}`)}flush(){if(this.buffer===void 0)throw new Error("@aws-sdk/core/protocols - QuerySerializer cannot flush with nothing written to buffer.");let A=this.buffer;return delete this.buffer,A}getKey(A,B){let Q=B??A;if(this.settings.capitalizeKeys)return Q[0].toUpperCase()+Q.slice(1);return Q}writeKey(A){if(A.endsWith("."))A=A.slice(0,A.length-1);this.buffer+=`&${oB0.extendedEncodeURIComponent(A)}=`}writeValue(A){this.buffer+=oB0.extendedEncodeURIComponent(A)}},DcA=class extends rB0.RpcProtocol{constructor(A){super({defaultNamespace:A.defaultNamespace});this.options=A;let B={timestampFormat:{useTrait:!0,default:Mk.SCHEMA.TIMESTAMP_DATE_TIME},httpBindings:!1,xmlNamespace:A.xmlNamespace,serviceNamespace:A.defaultNamespace,serializeEmptyLists:!0};this.serializer=new $oQ(B),this.deserializer=new D90(B)}static{D8(this,"AwsQueryProtocol")}serializer;deserializer;getShapeId(){return"aws.protocols#awsQuery"}setSerdeContext(A){this.serializer.setSerdeContext(A),this.deserializer.setSerdeContext(A)}getPayloadCodec(){throw new Error("AWSQuery protocol has no payload codec.")}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q);if(!D.path.endsWith("/"))D.path+="/";if(Object.assign(D.headers,{"content-type":"application/x-www-form-urlencoded"}),Mk.deref(A.input)==="unit"||!D.body)D.body="";if(D.body=`Action=${A.name.split("#")[1]}&Version=${this.options.version}`+D.body,D.body.endsWith("&"))D.body=D.body.slice(-1);try{D.headers["content-length"]=String(VoQ.calculateBodyLength(D.body))}catch(Z){}return D}async deserializeResponse(A,B,Q){let D=this.deserializer,Z=Mk.NormalizedSchema.of(A.output),G={};if(Q.statusCode>=300){let W=await rB0.collectBody(Q.body,B);if(W.byteLength>0)Object.assign(G,await D.read(Mk.SCHEMA.DOCUMENT,W));await this.handleError(A,B,Q,G,this.deserializeMetadata(Q))}for(let W in Q.headers){let J=Q.headers[W];delete Q.headers[W],Q.headers[W.toLowerCase()]=J}let F=Z.isStructSchema()&&this.useNestedResult()?A.name.split("#")[1]+"Result":void 0,I=await rB0.collectBody(Q.body,B);if(I.byteLength>0)Object.assign(G,await D.read(Z,I,F));return{$metadata:this.deserializeMetadata(Q),...G}}useNestedResult(){return!0}async handleError(A,B,Q,D,Z){let G=this.loadQueryErrorCode(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=this.loadQueryError(D),W=Mk.TypeRegistry.for(F),J;try{if(J=W.find((H)=>Mk.NormalizedSchema.of(H).getMergedTraits().awsQueryError?.[0]===I),!J)J=W.getSchema(G)}catch(H){let z=Mk.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(z){let $=z.ctor;throw Object.assign(new $(I),Y)}throw new Error(I)}let X=Mk.NormalizedSchema.of(J),V=this.loadQueryErrorMessage(D),C=new J.ctor(V),K={};for(let[H,z]of X.structIterator()){let $=z.getMergedTraits().xmlName??H,L=Y[$]??D[$];K[H]=this.deserializer.readSchema(z,L)}throw Object.assign(C,{$metadata:Z,$response:Q,$fault:X.getMergedTraits().error,message:V,...K}),C}loadQueryErrorCode(A,B){let Q=(B.Errors?.[0]?.Error??B.Errors?.Error??B.Error)?.Code;if(Q!==void 0)return Q;if(A.statusCode==404)return"NotFound"}loadQueryError(A){return A.Errors?.[0]?.Error??A.Errors?.Error??A.Error}loadQueryErrorMessage(A){let B=this.loadQueryError(A);return B?.message??B?.Message??A.message??A.Message??"Unknown"}},qoQ=class extends DcA{constructor(A){super(A);this.options=A;let B={capitalizeKeys:!0,flattenLists:!0,serializeEmptyLists:!1};Object.assign(this.serializer.settings,B)}static{D8(this,"AwsEc2QueryProtocol")}useNestedResult(){return!1}},tB0=M6(),U41=_Q(),NoQ=PY(),LoQ=w8(),MoQ=uN(),ZcA=D8((A,B)=>AcA(A,B).then((Q)=>{if(Q.length){let D=new MoQ.XMLParser({attributeNamePrefix:"",htmlEntities:!0,ignoreAttributes:!1,ignoreDeclaration:!0,parseTagValue:!1,trimValues:!1,tagValueProcessor:D8((Y,W)=>W.trim()===""&&W.includes(`
`)?"":void 0,"tagValueProcessor")});D.addEntity("#xD","\r"),D.addEntity("#10",`
`);let Z;try{Z=D.parse(Q,!0)}catch(Y){if(Y&&typeof Y==="object")Object.defineProperty(Y,"$responseBodyText",{value:Q});throw Y}let G="#text",F=Object.keys(Z)[0],I=Z[F];if(I[G])I[F]=I[G],delete I[G];return LoQ.getValueFromTextNode(I)}return{}}),"parseXmlBody"),RoQ=D8(async(A,B)=>{let Q=await ZcA(A,B);if(Q.Error)Q.Error.message=Q.Error.message??Q.Error.Message;return Q},"parseXmlErrorBody"),GcA=D8((A,B)=>{if(B?.Error?.Code!==void 0)return B.Error.Code;if(B?.Code!==void 0)return B.Code;if(A.statusCode==404)return"NotFound"},"loadRestXmlErrorCode"),QL=HQ1(),eh=_Q(),OoQ=X6(),rdA=w8(),odA=th(),FcA=class extends Ag{constructor(A){super();this.settings=A}static{D8(this,"XmlShapeSerializer")}stringBuffer;byteBuffer;buffer;write(A,B){let Q=eh.NormalizedSchema.of(A);if(Q.isStringSchema()&&typeof B==="string")this.stringBuffer=B;else if(Q.isBlobSchema())this.byteBuffer="byteLength"in B?B:(this.serdeContext?.base64Decoder??odA.fromBase64)(B);else{this.buffer=this.writeStruct(Q,B,void 0);let D=Q.getMergedTraits();if(D.httpPayload&&!D.xmlName)this.buffer.withName(Q.getName())}}flush(){if(this.byteBuffer!==void 0){let B=this.byteBuffer;return delete this.byteBuffer,B}if(this.stringBuffer!==void 0){let B=this.stringBuffer;return delete this.stringBuffer,B}let A=this.buffer;if(this.settings.xmlNamespace){if(!A?.attributes?.xmlns)A.addAttribute("xmlns",this.settings.xmlNamespace)}return delete this.buffer,A.toString()}writeStruct(A,B,Q){let D=A.getMergedTraits(),Z=A.isMemberSchema()&&!D.httpPayload?A.getMemberTraits().xmlName??A.getMemberName():D.xmlName??A.getName();if(!Z||!A.isStructSchema())throw new Error(`@aws-sdk/core/protocols - xml serializer, cannot write struct with empty name or non-struct, schema=${A.getName(!0)}.`);let G=QL.XmlNode.of(Z),[F,I]=this.getXmlnsAttribute(A,Q);if(I)G.addAttribute(F,I);for(let[Y,W]of A.structIterator()){let J=B[Y];if(J!=null){if(W.getMergedTraits().xmlAttribute){G.addAttribute(W.getMergedTraits().xmlName??Y,this.writeSimple(W,J));continue}if(W.isListSchema())this.writeList(W,J,G,I);else if(W.isMapSchema())this.writeMap(W,J,G,I);else if(W.isStructSchema())G.addChildNode(this.writeStruct(W,J,I));else{let X=QL.XmlNode.of(W.getMergedTraits().xmlName??W.getMemberName());this.writeSimpleInto(W,J,X,I),G.addChildNode(X)}}}return G}writeList(A,B,Q,D){if(!A.isMemberSchema())throw new Error(`@aws-sdk/core/protocols - xml serializer, cannot write non-member list: ${A.getName(!0)}`);let Z=A.getMergedTraits(),G=A.getValueSchema(),F=G.getMergedTraits(),I=!!F.sparse,Y=!!Z.xmlFlattened,[W,J]=this.getXmlnsAttribute(A,D),X=D8((V,C)=>{if(G.isListSchema())this.writeList(G,Array.isArray(C)?C:[C],V,J);else if(G.isMapSchema())this.writeMap(G,C,V,J);else if(G.isStructSchema()){let K=this.writeStruct(G,C,J);V.addChildNode(K.withName(Y?Z.xmlName??A.getMemberName():F.xmlName??"member"))}else{let K=QL.XmlNode.of(Y?Z.xmlName??A.getMemberName():F.xmlName??"member");this.writeSimpleInto(G,C,K,J),V.addChildNode(K)}},"writeItem");if(Y){for(let V of B)if(I||V!=null)X(Q,V)}else{let V=QL.XmlNode.of(Z.xmlName??A.getMemberName());if(J)V.addAttribute(W,J);for(let C of B)if(I||C!=null)X(V,C);Q.addChildNode(V)}}writeMap(A,B,Q,D,Z=!1){if(!A.isMemberSchema())throw new Error(`@aws-sdk/core/protocols - xml serializer, cannot write non-member map: ${A.getName(!0)}`);let G=A.getMergedTraits(),F=A.getKeySchema(),Y=F.getMergedTraits().xmlName??"key",W=A.getValueSchema(),J=W.getMergedTraits(),X=J.xmlName??"value",V=!!J.sparse,C=!!G.xmlFlattened,[K,H]=this.getXmlnsAttribute(A,D),z=D8(($,L,N)=>{let O=QL.XmlNode.of(Y,L),[R,T]=this.getXmlnsAttribute(F,H);if(T)O.addAttribute(R,T);$.addChildNode(O);let j=QL.XmlNode.of(X);if(W.isListSchema())this.writeList(W,N,j,H);else if(W.isMapSchema())this.writeMap(W,N,j,H,!0);else if(W.isStructSchema())j=this.writeStruct(W,N,H);else this.writeSimpleInto(W,N,j,H);$.addChildNode(j)},"addKeyValue");if(C){for(let[$,L]of Object.entries(B))if(V||L!=null){let N=QL.XmlNode.of(G.xmlName??A.getMemberName());z(N,$,L),Q.addChildNode(N)}}else{let $;if(!Z){if($=QL.XmlNode.of(G.xmlName??A.getMemberName()),H)$.addAttribute(K,H);Q.addChildNode($)}for(let[L,N]of Object.entries(B))if(V||N!=null){let O=QL.XmlNode.of("entry");z(O,L,N),(Z?Q:$).addChildNode(O)}}}writeSimple(A,B){if(B===null)throw new Error("@aws-sdk/core/protocols - (XML serializer) cannot write null value.");let Q=eh.NormalizedSchema.of(A),D=null;if(B&&typeof B==="object")if(Q.isBlobSchema())D=(this.serdeContext?.base64Encoder??odA.toBase64)(B);else if(Q.isTimestampSchema()&&B instanceof Date){let Z=this.settings.timestampFormat;switch(Z.useTrait?Q.getSchema()===eh.SCHEMA.TIMESTAMP_DEFAULT?Z.default:Q.getSchema()??Z.default:Z.default){case eh.SCHEMA.TIMESTAMP_DATE_TIME:D=B.toISOString().replace(".000Z","Z");break;case eh.SCHEMA.TIMESTAMP_HTTP_DATE:D=rdA.dateToUtcString(B);break;case eh.SCHEMA.TIMESTAMP_EPOCH_SECONDS:D=String(B.getTime()/1000);break;default:console.warn("Missing timestamp format, using http date",B),D=rdA.dateToUtcString(B);break}}else if(Q.isBigDecimalSchema()&&B){if(B instanceof OoQ.NumericValue)return B.string;return String(B)}else if(Q.isMapSchema()||Q.isListSchema())throw new Error("@aws-sdk/core/protocols - xml serializer, cannot call _write() on List/Map schema, call writeList or writeMap() instead.");else throw new Error(`@aws-sdk/core/protocols - xml serializer, unhandled schema type for object value and schema: ${Q.getName(!0)}`);if(Q.isStringSchema()||Q.isBooleanSchema()||Q.isNumericSchema()||Q.isBigIntegerSchema()||Q.isBigDecimalSchema())D=String(B);if(D===null)throw new Error(`Unhandled schema-value pair ${Q.getName(!0)}=${B}`);return D}writeSimpleInto(A,B,Q,D){let Z=this.writeSimple(A,B),G=eh.NormalizedSchema.of(A),F=new QL.XmlText(Z),[I,Y]=this.getXmlnsAttribute(G,D);if(Y)Q.addAttribute(I,Y);Q.addChildNode(F)}getXmlnsAttribute(A,B){let Q=A.getMergedTraits(),[D,Z]=Q.xmlNamespace??[];if(Z&&Z!==B)return[D?`xmlns:${D}`:"xmlns",Z];return[void 0,void 0]}},IcA=class extends Ag{constructor(A){super();this.settings=A}static{D8(this,"XmlCodec")}createSerializer(){let A=new FcA(this.settings);return A.setSerdeContext(this.serdeContext),A}createDeserializer(){let A=new D90(this.settings);return A.setSerdeContext(this.serdeContext),A}},ToQ=class extends tB0.HttpBindingProtocol{static{D8(this,"AwsRestXmlProtocol")}codec;serializer;deserializer;constructor(A){super(A);let B={timestampFormat:{useTrait:!0,default:U41.SCHEMA.TIMESTAMP_DATE_TIME},httpBindings:!0,xmlNamespace:A.xmlNamespace,serviceNamespace:A.defaultNamespace};this.codec=new IcA(B),this.serializer=new tB0.HttpInterceptingShapeSerializer(this.codec.createSerializer(),B),this.deserializer=new tB0.HttpInterceptingShapeDeserializer(this.codec.createDeserializer(),B)}getPayloadCodec(){return this.codec}getShapeId(){return"aws.protocols#restXml"}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q),Z=U41.NormalizedSchema.of(A.input),G=Z.getMemberSchemas();if(D.path=String(D.path).split("/").filter((F)=>{return F!=="{Bucket}"}).join("/")||"/",!D.headers["content-type"]){let F=Object.values(G).find((I)=>{return!!I.getMergedTraits().httpPayload});if(F){let I=F.getMergedTraits().mediaType;if(I)D.headers["content-type"]=I;else if(F.isStringSchema())D.headers["content-type"]="text/plain";else if(F.isBlobSchema())D.headers["content-type"]="application/octet-stream";else D.headers["content-type"]="application/xml"}else if(!Z.isUnitSchema()){if(Object.values(G).find((Y)=>{let{httpQuery:W,httpQueryParams:J,httpHeader:X,httpLabel:V,httpPrefixHeaders:C}=Y.getMergedTraits();return!W&&!J&&!X&&!V&&C===void 0}))D.headers["content-type"]="application/xml"}}if(D.headers["content-type"]==="application/xml"){if(typeof D.body==="string")D.body='<?xml version="1.0" encoding="UTF-8"?>'+D.body}if(D.body)try{D.headers["content-length"]=String(NoQ.calculateBodyLength(D.body))}catch(F){}return D}async deserializeResponse(A,B,Q){return super.deserializeResponse(A,B,Q)}async handleError(A,B,Q,D,Z){let G=GcA(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=U41.TypeRegistry.for(F),W;try{W=Y.getSchema(G)}catch(K){let H=U41.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(H){let z=H.ctor;throw Object.assign(new z(I),D)}throw new Error(I)}let J=U41.NormalizedSchema.of(W),X=D.Error?.message??D.Error?.Message??D.message??D.Message??"Unknown",V=new W.ctor(X);await this.deserializeHttpMessage(W,B,Q,D);let C={};for(let[K,H]of J.structIterator()){let z=H.getMergedTraits().xmlName??K,$=D.Error?.[z]??D[z];C[K]=this.codec.createDeserializer().readSchema(H,$)}throw Object.assign(V,{$metadata:Z,$response:Q,$fault:J.getMergedTraits().error,message:X,...C}),V}}});
var XfA=E((WfA)=>{Object.defineProperty(WfA,"__esModule",{value:!0});WfA.getRuntimeConfig=void 0;var UdQ=mh(),wdQ=UdQ.__importDefault(D20()),w20=VI(),FfA=oQ1(),xU1=K4(),$dQ=CB(),qdQ=gG(),IfA=u4(),ah=JD(),YfA=k3(),NdQ=uG(),LdQ=sZ(),MdQ=GfA(),RdQ=V6(),OdQ=mG(),TdQ=V6(),PdQ=(A)=>{TdQ.emitWarningIfUnsupportedVersion(process.version);let B=OdQ.resolveDefaultsModeConfig(A),Q=()=>B().then(RdQ.loadConfigsForDefaultMode),D=MdQ.getRuntimeConfig(A);w20.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile,logger:D.logger};return{...D,...A,runtime:"node",defaultsMode:B,authSchemePreference:A?.authSchemePreference??ah.loadConfig(w20.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS,Z),bodyLengthChecker:A?.bodyLengthChecker??NdQ.calculateBodyLength,defaultUserAgentProvider:A?.defaultUserAgentProvider??FfA.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:wdQ.default.version}),httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(G)=>G.getIdentityProvider("aws.auth#sigv4")||(async(F)=>await A.credentialDefaultProvider(F?.__config||{})()),signer:new w20.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(G)=>G.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new $dQ.NoAuthSigner}],maxAttempts:A?.maxAttempts??ah.loadConfig(IfA.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??ah.loadConfig(xU1.NODE_REGION_CONFIG_OPTIONS,{...xU1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:YfA.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??ah.loadConfig({...IfA.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||LdQ.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??qdQ.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??YfA.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??ah.loadConfig(xU1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??ah.loadConfig(xU1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??ah.loadConfig(FfA.NODE_APP_ID_CONFIG_OPTIONS,Z)}};WfA.getRuntimeConfig=PdQ});
var XhA=E((WhA)=>{Object.defineProperty(WhA,"__esModule",{value:!0});WhA.fromTokenFile=void 0;var ZlQ=Sw(),GlQ=Q9(),FlQ=J1("fs"),IlQ=QB0(),YhA="AWS_WEB_IDENTITY_TOKEN_FILE",YlQ="AWS_ROLE_ARN",WlQ="AWS_ROLE_SESSION_NAME",JlQ=(A={})=>async()=>{A.logger?.debug("@aws-sdk/credential-provider-web-identity - fromTokenFile");let B=A?.webIdentityTokenFile??process.env[YhA],Q=A?.roleArn??process.env[YlQ],D=A?.roleSessionName??process.env[WlQ];if(!B||!Q)throw new GlQ.CredentialsProviderError("Web identity configuration not specified",{logger:A.logger});let Z=await IlQ.fromWebToken({...A,webIdentityToken:FlQ.readFileSync(B,{encoding:"ascii"}),roleArn:Q,roleSessionName:D})();if(B===process.env[YhA])ZlQ.setCredentialFeature(Z,"CREDENTIALS_ENV_VARS_STS_WEB_ID_TOKEN","h");return Z};WhA.fromTokenFile=JlQ});
var XlA=E((WlA)=>{Object.defineProperty(WlA,"__esModule",{value:!0});WlA.getRuntimeConfig=void 0;var EtQ=UV(),UtQ=CB(),wtQ=w8(),$tQ=JZ(),IlA=th(),YlA=cB(),qtQ=Y90(),NtQ=FlA(),LtQ=(A)=>{return{apiVersion:"2011-06-15",base64Decoder:A?.base64Decoder??IlA.fromBase64,base64Encoder:A?.base64Encoder??IlA.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??NtQ.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??qtQ.defaultSTSHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new EtQ.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new UtQ.NoAuthSigner}],logger:A?.logger??new wtQ.NoOpLogger,serviceId:A?.serviceId??"STS",urlParser:A?.urlParser??$tQ.parseUrl,utf8Decoder:A?.utf8Decoder??YlA.fromUtf8,utf8Encoder:A?.utf8Encoder??YlA.toUtf8}};WlA.getRuntimeConfig=LtQ});
var XmA=E((WmA)=>{Object.defineProperty(WmA,"__esModule",{value:!0});WmA.fromHttp=void 0;var NaQ=uh(),LaQ=Oz(),MaQ=k3(),ImA=Q9(),RaQ=NaQ.__importDefault(J1("fs/promises")),OaQ=_uA(),YmA=DmA(),TaQ=FmA(),PaQ="AWS_CONTAINER_CREDENTIALS_RELATIVE_URI",SaQ="http://*************",jaQ="AWS_CONTAINER_CREDENTIALS_FULL_URI",yaQ="AWS_CONTAINER_AUTHORIZATION_TOKEN_FILE",kaQ="AWS_CONTAINER_AUTHORIZATION_TOKEN",_aQ=(A={})=>{A.logger?.debug("@aws-sdk/credential-provider-http - fromHttp");let B,Q=A.awsContainerCredentialsRelativeUri??process.env[PaQ],D=A.awsContainerCredentialsFullUri??process.env[jaQ],Z=A.awsContainerAuthorizationToken??process.env[kaQ],G=A.awsContainerAuthorizationTokenFile??process.env[yaQ],F=A.logger?.constructor?.name==="NoOpLogger"||!A.logger?console.warn:A.logger.warn;if(Q&&D)F("@aws-sdk/credential-provider-http: you have set both awsContainerCredentialsRelativeUri and awsContainerCredentialsFullUri."),F("awsContainerCredentialsFullUri will take precedence.");if(Z&&G)F("@aws-sdk/credential-provider-http: you have set both awsContainerAuthorizationToken and awsContainerAuthorizationTokenFile."),F("awsContainerAuthorizationToken will take precedence.");if(D)B=D;else if(Q)B=`${SaQ}${Q}`;else throw new ImA.CredentialsProviderError(`No HTTP credential provider host provided.
Set AWS_CONTAINER_CREDENTIALS_FULL_URI or AWS_CONTAINER_CREDENTIALS_RELATIVE_URI.`,{logger:A.logger});let I=new URL(B);OaQ.checkUrl(I,A.logger);let Y=new MaQ.NodeHttpHandler({requestTimeout:A.timeout??1000,connectionTimeout:A.timeout??1000});return TaQ.retryWrapper(async()=>{let W=YmA.createGetRequest(I);if(Z)W.headers.Authorization=Z;else if(G)W.headers.Authorization=(await RaQ.default.readFile(G)).toString();try{let J=await Y.handle(W);return YmA.getCredentials(J.response).then((X)=>LaQ.setCredentialFeature(X,"CREDENTIALS_HTTP","z"))}catch(J){throw new ImA.CredentialsProviderError(String(J),{logger:A.logger})}},A.maxRetries??3,A.timeout??1000)};WmA.fromHttp=_aQ});
var XxA=E((WxA)=>{Object.defineProperty(WxA,"__esModule",{value:!0});WxA.getRuntimeConfig=void 0;var AgQ=VI(),BgQ=CB(),QgQ=V6(),DgQ=JZ(),IxA=Hk(),YxA=cB(),ZgQ=aA0(),GgQ=FxA(),FgQ=(A)=>{return{apiVersion:"2019-06-10",base64Decoder:A?.base64Decoder??IxA.fromBase64,base64Encoder:A?.base64Encoder??IxA.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??GgQ.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??ZgQ.defaultSSOHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new AgQ.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new BgQ.NoAuthSigner}],logger:A?.logger??new QgQ.NoOpLogger,serviceId:A?.serviceId??"SSO",urlParser:A?.urlParser??DgQ.parseUrl,utf8Decoder:A?.utf8Decoder??YxA.fromUtf8,utf8Encoder:A?.utf8Encoder??YxA.toUtf8}};WxA.getRuntimeConfig=FgQ});
var Y90=E((NcA)=>{Object.defineProperty(NcA,"__esModule",{value:!0});NcA.resolveHttpAuthSchemeConfig=NcA.resolveStsAuthConfig=NcA.defaultSTSHttpAuthSchemeProvider=NcA.defaultSTSHttpAuthSchemeParametersProvider=void 0;var doQ=UV(),I90=J5(),coQ=q41(),loQ=async(A,B,Q)=>{return{operation:I90.getSmithyContext(B).operation,region:await I90.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};NcA.defaultSTSHttpAuthSchemeParametersProvider=loQ;function poQ(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"sts",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function ioQ(A){return{schemeId:"smithy.api#noAuth"}}var noQ=(A)=>{let B=[];switch(A.operation){case"AssumeRoleWithWebIdentity":{B.push(ioQ(A));break}default:B.push(poQ(A))}return B};NcA.defaultSTSHttpAuthSchemeProvider=noQ;var aoQ=(A)=>Object.assign(A,{stsClientCtor:coQ.STSClient});NcA.resolveStsAuthConfig=aoQ;var soQ=(A)=>{let B=NcA.resolveStsAuthConfig(A),Q=doQ.resolveAwsSdkSigV4Config(B);return Object.assign(Q,{authSchemePreference:I90.normalizeProvider(A.authSchemePreference??[])})};NcA.resolveHttpAuthSchemeConfig=soQ});
var YK=E((ZJ5,nuA)=>{var{defineProperty:sU1,getOwnPropertyDescriptor:RnQ,getOwnPropertyNames:OnQ}=Object,TnQ=Object.prototype.hasOwnProperty,Nk=(A,B)=>sU1(A,"name",{value:B,configurable:!0}),PnQ=(A,B)=>{for(var Q in B)sU1(A,Q,{get:B[Q],enumerable:!0})},SnQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of OnQ(B))if(!TnQ.call(A,Z)&&Z!==Q)sU1(A,Z,{get:()=>B[Z],enumerable:!(D=RnQ(B,Z))||D.enumerable})}return A},jnQ=(A)=>SnQ(sU1({},"__esModule",{value:!0}),A),cuA={};PnQ(cuA,{Field:()=>_nQ,Fields:()=>xnQ,HttpRequest:()=>vnQ,HttpResponse:()=>bnQ,IHttpRequest:()=>luA.HttpRequest,getHttpHandlerExtensionConfiguration:()=>ynQ,isValidHostname:()=>iuA,resolveHttpHandlerRuntimeConfig:()=>knQ});nuA.exports=jnQ(cuA);var ynQ=Nk((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),knQ=Nk((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),luA=qB0(),_nQ=class{static{Nk(this,"Field")}constructor({name:A,kind:B=luA.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},xnQ=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{Nk(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},vnQ=class A{static{Nk(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=puA(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function puA(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}Nk(puA,"cloneQuery");var bnQ=class{static{Nk(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function iuA(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}Nk(iuA,"isValidHostname")});
var YnA=E((FnA)=>{Object.defineProperty(FnA,"__esModule",{value:!0});FnA.getRuntimeConfig=void 0;var i04=uh(),n04=i04.__importDefault(W90()),QnA=UV(),DnA=Ow1(),cw1=K4(),a04=gG(),ZnA=u4(),Dg=JD(),GnA=k3(),s04=uG(),r04=sZ(),o04=BnA(),t04=w8(),e04=mG(),AA4=w8(),BA4=(A)=>{AA4.emitWarningIfUnsupportedVersion(process.version);let B=e04.resolveDefaultsModeConfig(A),Q=()=>B().then(t04.loadConfigsForDefaultMode),D=o04.getRuntimeConfig(A);QnA.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile,logger:D.logger};return{...D,...A,runtime:"node",defaultsMode:B,authSchemePreference:A?.authSchemePreference??Dg.loadConfig(QnA.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS,Z),bodyLengthChecker:A?.bodyLengthChecker??s04.calculateBodyLength,defaultUserAgentProvider:A?.defaultUserAgentProvider??DnA.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:n04.default.version}),maxAttempts:A?.maxAttempts??Dg.loadConfig(ZnA.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??Dg.loadConfig(cw1.NODE_REGION_CONFIG_OPTIONS,{...cw1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:GnA.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??Dg.loadConfig({...ZnA.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||r04.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??a04.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??GnA.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??Dg.loadConfig(cw1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??Dg.loadConfig(cw1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??Dg.loadConfig(DnA.NODE_APP_ID_CONFIG_OPTIONS,Z)}};FnA.getRuntimeConfig=BA4});
var ZQ0=E((SiA)=>{Object.defineProperty(SiA,"__esModule",{value:!0});SiA.resolveHttpAuthSchemeConfig=SiA.defaultSSOOIDCHttpAuthSchemeProvider=SiA.defaultSSOOIDCHttpAuthSchemeParametersProvider=void 0;var O04=UV(),DQ0=J5(),T04=async(A,B,Q)=>{return{operation:DQ0.getSmithyContext(B).operation,region:await DQ0.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};SiA.defaultSSOOIDCHttpAuthSchemeParametersProvider=T04;function P04(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"sso-oauth",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function S04(A){return{schemeId:"smithy.api#noAuth"}}var j04=(A)=>{let B=[];switch(A.operation){case"CreateToken":{B.push(S04(A));break}default:B.push(P04(A))}return B};SiA.defaultSSOOIDCHttpAuthSchemeProvider=j04;var y04=(A)=>{let B=O04.resolveAwsSdkSigV4Config(A);return Object.assign(B,{authSchemePreference:DQ0.normalizeProvider(A.authSchemePreference??[])})};SiA.resolveHttpAuthSchemeConfig=y04});
var Zw1=E((qJ5,ymA)=>{var{defineProperty:Dw1,getOwnPropertyDescriptor:QsQ,getOwnPropertyNames:DsQ}=Object,ZsQ=Object.prototype.hasOwnProperty,yB0=(A,B)=>Dw1(A,"name",{value:B,configurable:!0}),GsQ=(A,B)=>{for(var Q in B)Dw1(A,Q,{get:B[Q],enumerable:!0})},FsQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of DsQ(B))if(!ZsQ.call(A,Z)&&Z!==Q)Dw1(A,Z,{get:()=>B[Z],enumerable:!(D=QsQ(B,Z))||D.enumerable})}return A},IsQ=(A)=>FsQ(Dw1({},"__esModule",{value:!0}),A),PmA={};GsQ(PmA,{getLoggerPlugin:()=>YsQ,loggerMiddleware:()=>SmA,loggerMiddlewareOptions:()=>jmA});ymA.exports=IsQ(PmA);var SmA=yB0(()=>(A,B)=>async(Q)=>{try{let D=await A(Q),{clientName:Z,commandName:G,logger:F,dynamoDbDocumentClientOptions:I={}}=B,{overrideInputFilterSensitiveLog:Y,overrideOutputFilterSensitiveLog:W}=I,J=Y??B.inputFilterSensitiveLog,X=W??B.outputFilterSensitiveLog,{$metadata:V,...C}=D.output;return F?.info?.({clientName:Z,commandName:G,input:J(Q.input),output:X(C),metadata:V}),D}catch(D){let{clientName:Z,commandName:G,logger:F,dynamoDbDocumentClientOptions:I={}}=B,{overrideInputFilterSensitiveLog:Y}=I,W=Y??B.inputFilterSensitiveLog;throw F?.error?.({clientName:Z,commandName:G,input:W(Q.input),error:D,metadata:D.$metadata}),D}},"loggerMiddleware"),jmA={name:"loggerMiddleware",tags:["LOGGER"],step:"initialize",override:!0},YsQ=yB0((A)=>({applyToStack:yB0((B)=>{B.add(SmA(),jmA)},"applyToStack")}),"getLoggerPlugin")});
var __A=E((TY5,fhQ)=>{fhQ.exports={name:"@aws-sdk/client-sso",description:"AWS SDK for JavaScript Sso Client for Node.js, Browser and React Native",version:"3.840.0",scripts:{build:"concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'","build:cjs":"node ../../scripts/compilation/inline client-sso","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4",clean:"rimraf ./dist-* && rimraf *.tsbuildinfo","extract:docs":"api-extractor run --local","generate:client":"node ../../scripts/generate-clients/single-service --solo sso"},main:"./dist-cjs/index.js",types:"./dist-types/index.d.ts",module:"./dist-es/index.js",sideEffects:!1,dependencies:{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.840.0","@aws-sdk/middleware-host-header":"3.840.0","@aws-sdk/middleware-logger":"3.840.0","@aws-sdk/middleware-recursion-detection":"3.840.0","@aws-sdk/middleware-user-agent":"3.840.0","@aws-sdk/region-config-resolver":"3.840.0","@aws-sdk/types":"3.840.0","@aws-sdk/util-endpoints":"3.840.0","@aws-sdk/util-user-agent-browser":"3.840.0","@aws-sdk/util-user-agent-node":"3.840.0","@smithy/config-resolver":"^4.1.4","@smithy/core":"^3.6.0","@smithy/fetch-http-handler":"^5.0.4","@smithy/hash-node":"^4.0.4","@smithy/invalid-dependency":"^4.0.4","@smithy/middleware-content-length":"^4.0.4","@smithy/middleware-endpoint":"^4.1.13","@smithy/middleware-retry":"^4.1.14","@smithy/middleware-serde":"^4.0.8","@smithy/middleware-stack":"^4.0.4","@smithy/node-config-provider":"^4.1.3","@smithy/node-http-handler":"^4.0.6","@smithy/protocol-http":"^5.1.2","@smithy/smithy-client":"^4.4.5","@smithy/types":"^4.3.1","@smithy/url-parser":"^4.0.4","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.21","@smithy/util-defaults-mode-node":"^4.0.21","@smithy/util-endpoints":"^3.0.6","@smithy/util-middleware":"^4.0.4","@smithy/util-retry":"^4.0.6","@smithy/util-utf8":"^4.0.0",tslib:"^2.6.2"},devDependencies:{"@tsconfig/node18":"18.2.4","@types/node":"^18.19.69",concurrently:"7.0.0","downlevel-dts":"0.10.1",rimraf:"3.0.2",typescript:"~5.8.3"},engines:{node:">=18.0.0"},typesVersions:{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},files:["dist-*/**"],author:{name:"AWS SDK for JavaScript Team",url:"https://aws.amazon.com/javascript/"},license:"Apache-2.0",browser:{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.browser"},"react-native":{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.native"},homepage:"https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-sso",repository:{type:"git",url:"https://github.com/aws/aws-sdk-js-v3.git",directory:"clients/client-sso"}}});
var _aA=E((yaA)=>{Object.defineProperty(yaA,"__esModule",{value:!0});yaA.fromSSO=void 0;var XB4=aw1(),VB4=(A={})=>{return XB4.fromSSO({...A})};yaA.fromSSO=VB4});
var _lA=E((ylA)=>{Object.defineProperty(ylA,"__esModule",{value:!0});ylA.resolveRuntimeExtensions=void 0;var TlA=Sw1(),PlA=YK(),SlA=w8(),jlA=OlA(),stQ=(A,B)=>{let Q=Object.assign(TlA.getAwsRegionExtensionConfiguration(A),SlA.getDefaultExtensionConfiguration(A),PlA.getHttpHandlerExtensionConfiguration(A),jlA.getHttpAuthExtensionConfiguration(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,TlA.resolveAwsRegionExtensionConfiguration(Q),SlA.resolveDefaultRuntimeConfig(Q),PlA.resolveHttpHandlerRuntimeConfig(Q),jlA.resolveHttpAuthRuntimeConfig(Q))};ylA.resolveRuntimeExtensions=stQ});
var _uA=E((yuA)=>{Object.defineProperty(yuA,"__esModule",{value:!0});yuA.checkUrl=void 0;var WnQ=Q9(),JnQ="*************",XnQ="*************3",VnQ="[fd00:ec2::23]",CnQ=(A,B)=>{if(A.protocol==="https:")return;if(A.hostname===JnQ||A.hostname===XnQ||A.hostname===VnQ)return;if(A.hostname.includes("[")){if(A.hostname==="[::1]"||A.hostname==="[0000:0000:0000:0000:0000:0000:0000:0001]")return}else{if(A.hostname==="localhost")return;let Q=A.hostname.split("."),D=(Z)=>{let G=parseInt(Z,10);return 0<=G&&G<=255};if(Q[0]==="127"&&D(Q[1])&&D(Q[2])&&D(Q[3])&&Q.length===4)return}throw new WnQ.CredentialsProviderError(`URL not accepted. It must either be HTTPS or match one of the following:
  - loopback CIDR *********/8 or [::1/128]
  - ECS container host *************
  - EKS container host *************3 or [fd00:ec2::23]`,{logger:B})};yuA.checkUrl=CnQ});
var _w1=E((UX5,wpA)=>{var{defineProperty:kw1,getOwnPropertyDescriptor:G14,getOwnPropertyNames:F14}=Object,I14=Object.prototype.hasOwnProperty,s90=(A,B)=>kw1(A,"name",{value:B,configurable:!0}),Y14=(A,B)=>{for(var Q in B)kw1(A,Q,{get:B[Q],enumerable:!0})},W14=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of F14(B))if(!I14.call(A,Z)&&Z!==Q)kw1(A,Z,{get:()=>B[Z],enumerable:!(D=G14(B,Z))||D.enumerable})}return A},J14=(A)=>W14(kw1({},"__esModule",{value:!0}),A),UpA={};Y14(UpA,{fromProcess:()=>z14});wpA.exports=J14(UpA);var EpA=D3(),a90=Q9(),X14=J1("child_process"),V14=J1("util"),C14=Oz(),K14=s90((A,B,Q)=>{if(B.Version!==1)throw Error(`Profile ${A} credential_process did not return Version 1.`);if(B.AccessKeyId===void 0||B.SecretAccessKey===void 0)throw Error(`Profile ${A} credential_process returned invalid credentials.`);if(B.Expiration){let G=new Date;if(new Date(B.Expiration)<G)throw Error(`Profile ${A} credential_process returned expired credentials.`)}let D=B.AccountId;if(!D&&Q?.[A]?.aws_account_id)D=Q[A].aws_account_id;let Z={accessKeyId:B.AccessKeyId,secretAccessKey:B.SecretAccessKey,...B.SessionToken&&{sessionToken:B.SessionToken},...B.Expiration&&{expiration:new Date(B.Expiration)},...B.CredentialScope&&{credentialScope:B.CredentialScope},...D&&{accountId:D}};return C14.setCredentialFeature(Z,"CREDENTIALS_PROCESS","w"),Z},"getValidatedProcessCredentials"),H14=s90(async(A,B,Q)=>{let D=B[A];if(B[A]){let Z=D.credential_process;if(Z!==void 0){let G=V14.promisify(X14.exec);try{let{stdout:F}=await G(Z),I;try{I=JSON.parse(F.trim())}catch{throw Error(`Profile ${A} credential_process returned invalid JSON.`)}return K14(A,I,B)}catch(F){throw new a90.CredentialsProviderError(F.message,{logger:Q})}}else throw new a90.CredentialsProviderError(`Profile ${A} did not contain credential_process.`,{logger:Q})}else throw new a90.CredentialsProviderError(`Profile ${A} could not be found in shared credentials file.`,{logger:Q})},"resolveProcessCredentials"),z14=s90((A={})=>async({callerClientConfig:B}={})=>{A.logger?.debug("@aws-sdk/credential-provider-process - fromProcess");let Q=await EpA.parseKnownFiles(A);return H14(EpA.getProfileName({profile:A.profile??B?.profile}),Q,A.logger)},"fromProcess")});
var aA0=E((y_A)=>{Object.defineProperty(y_A,"__esModule",{value:!0});y_A.resolveHttpAuthSchemeConfig=y_A.defaultSSOHttpAuthSchemeProvider=y_A.defaultSSOHttpAuthSchemeParametersProvider=void 0;var jhQ=VI(),nA0=J5(),yhQ=async(A,B,Q)=>{return{operation:nA0.getSmithyContext(B).operation,region:await nA0.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};y_A.defaultSSOHttpAuthSchemeParametersProvider=yhQ;function khQ(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"awsssoportal",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function EU1(A){return{schemeId:"smithy.api#noAuth"}}var _hQ=(A)=>{let B=[];switch(A.operation){case"GetRoleCredentials":{B.push(EU1(A));break}case"ListAccountRoles":{B.push(EU1(A));break}case"ListAccounts":{B.push(EU1(A));break}case"Logout":{B.push(EU1(A));break}default:B.push(khQ(A))}return B};y_A.defaultSSOHttpAuthSchemeProvider=_hQ;var xhQ=(A)=>{let B=jhQ.resolveAwsSdkSigV4Config(A);return Object.assign(B,{authSchemePreference:nA0.normalizeProvider(A.authSchemePreference??[])})};y_A.resolveHttpAuthSchemeConfig=xhQ});
var akA=E((eI5,nkA)=>{var{defineProperty:VU1,getOwnPropertyDescriptor:hbQ,getOwnPropertyNames:gbQ}=Object,ubQ=Object.prototype.hasOwnProperty,B8=(A,B)=>VU1(A,"name",{value:B,configurable:!0}),mbQ=(A,B)=>{for(var Q in B)VU1(A,Q,{get:B[Q],enumerable:!0})},dbQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of gbQ(B))if(!ubQ.call(A,Z)&&Z!==Q)VU1(A,Z,{get:()=>B[Z],enumerable:!(D=hbQ(B,Z))||D.enumerable})}return A},cbQ=(A)=>dbQ(VU1({},"__esModule",{value:!0}),A),fkA={};mbQ(fkA,{AwsEc2QueryProtocol:()=>UfQ,AwsJson1_0Protocol:()=>DfQ,AwsJson1_1Protocol:()=>ZfQ,AwsJsonRpcProtocol:()=>bA0,AwsQueryProtocol:()=>dkA,AwsRestJsonProtocol:()=>FfQ,AwsRestXmlProtocol:()=>MfQ,JsonCodec:()=>vA0,JsonShapeDeserializer:()=>ukA,JsonShapeSerializer:()=>mkA,XmlCodec:()=>ikA,XmlShapeDeserializer:()=>fA0,XmlShapeSerializer:()=>pkA,_toBool:()=>pbQ,_toNum:()=>ibQ,_toStr:()=>lbQ,awsExpectUnion:()=>YfQ,loadRestJsonErrorCode:()=>xA0,loadRestXmlErrorCode:()=>lkA,parseJsonBody:()=>_A0,parseJsonErrorBody:()=>tbQ,parseXmlBody:()=>ckA,parseXmlErrorBody:()=>NfQ});nkA.exports=cbQ(fkA);var lbQ=B8((A)=>{if(A==null)return A;if(typeof A==="number"||typeof A==="bigint"){let B=new Error(`Received number ${A} where a string was expected.`);return B.name="Warning",console.warn(B),String(A)}if(typeof A==="boolean"){let B=new Error(`Received boolean ${A} where a string was expected.`);return B.name="Warning",console.warn(B),String(A)}return A},"_toStr"),pbQ=B8((A)=>{if(A==null)return A;if(typeof A==="string"){let B=A.toLowerCase();if(A!==""&&B!=="false"&&B!=="true"){let Q=new Error(`Received string "${A}" where a boolean was expected.`);Q.name="Warning",console.warn(Q)}return A!==""&&B!=="false"}return A},"_toBool"),ibQ=B8((A)=>{if(A==null)return A;if(typeof A==="string"){let B=Number(A);if(B.toString()!==A){let Q=new Error(`Received string "${A}" where a number was expected.`);return Q.name="Warning",console.warn(Q),A}return B}return A},"_toNum"),nbQ=M6(),ln=_Q(),abQ=PY(),lh=class{static{B8(this,"SerdeContextConfig")}serdeContext;setSerdeContext(A){this.serdeContext=A}},nQ1=_Q(),pn=X6(),sbQ=Hk(),rbQ=X6();function hkA(A,B,Q){if(Q?.source){let D=Q.source;if(typeof B==="number"){if(B>Number.MAX_SAFE_INTEGER||B<Number.MIN_SAFE_INTEGER||D!==String(B))if(D.includes("."))return new rbQ.NumericValue(D,"bigDecimal");else return BigInt(D)}}return B}B8(hkA,"jsonReviver");var obQ=V6(),gkA=B8((A,B)=>obQ.collectBody(A,B).then((Q)=>B.utf8Encoder(Q)),"collectBodyString"),_A0=B8((A,B)=>gkA(A,B).then((Q)=>{if(Q.length)try{return JSON.parse(Q)}catch(D){if(D?.name==="SyntaxError")Object.defineProperty(D,"$responseBodyText",{value:Q});throw D}return{}}),"parseJsonBody"),tbQ=B8(async(A,B)=>{let Q=await _A0(A,B);return Q.message=Q.message??Q.Message,Q},"parseJsonErrorBody"),xA0=B8((A,B)=>{let Q=B8((G,F)=>Object.keys(G).find((I)=>I.toLowerCase()===F.toLowerCase()),"findKey"),D=B8((G)=>{let F=G;if(typeof F==="number")F=F.toString();if(F.indexOf(",")>=0)F=F.split(",")[0];if(F.indexOf(":")>=0)F=F.split(":")[0];if(F.indexOf("#")>=0)F=F.split("#")[1];return F},"sanitizeErrorCode"),Z=Q(A.headers,"x-amzn-errortype");if(Z!==void 0)return D(A.headers[Z]);if(B&&typeof B==="object"){let G=Q(B,"code");if(G&&B[G]!==void 0)return D(B[G]);if(B.__type!==void 0)return D(B.__type)}},"loadRestJsonErrorCode"),ukA=class extends lh{constructor(A){super();this.settings=A}static{B8(this,"JsonShapeDeserializer")}async read(A,B){return this._read(A,typeof B==="string"?JSON.parse(B,hkA):await _A0(B,this.serdeContext))}readObject(A,B){return this._read(A,B)}_read(A,B){let Q=B!==null&&typeof B==="object",D=nQ1.NormalizedSchema.of(A);if(D.isListSchema()&&Array.isArray(B)){let G=D.getValueSchema(),F=[],I=!!D.getMergedTraits().sparse;for(let Y of B)if(I||Y!=null)F.push(this._read(G,Y));return F}else if(D.isMapSchema()&&Q){let G=D.getValueSchema(),F={},I=!!D.getMergedTraits().sparse;for(let[Y,W]of Object.entries(B))if(I||W!=null)F[Y]=this._read(G,W);return F}else if(D.isStructSchema()&&Q){let G={};for(let[F,I]of D.structIterator()){let Y=this.settings.jsonName?I.getMergedTraits().jsonName??F:F,W=this._read(I,B[Y]);if(W!=null)G[F]=W}return G}if(D.isBlobSchema()&&typeof B==="string")return sbQ.fromBase64(B);let Z=D.getMergedTraits().mediaType;if(D.isStringSchema()&&typeof B==="string"&&Z){if(Z==="application/json"||Z.endsWith("+json"))return pn.LazyJsonString.from(B)}if(D.isTimestampSchema()){let G=this.settings.timestampFormat;switch(G.useTrait?D.getSchema()===nQ1.SCHEMA.TIMESTAMP_DEFAULT?G.default:D.getSchema()??G.default:G.default){case nQ1.SCHEMA.TIMESTAMP_DATE_TIME:return pn.parseRfc3339DateTimeWithOffset(B);case nQ1.SCHEMA.TIMESTAMP_HTTP_DATE:return pn.parseRfc7231DateTime(B);case nQ1.SCHEMA.TIMESTAMP_EPOCH_SECONDS:return pn.parseEpochTimestamp(B);default:return console.warn("Missing timestamp format, parsing value with Date constructor:",B),new Date(B)}}if(D.isBigIntegerSchema()&&(typeof B==="number"||typeof B==="string"))return BigInt(B);if(D.isBigDecimalSchema()&&B!=null){if(B instanceof pn.NumericValue)return B;return new pn.NumericValue(String(B),"bigDecimal")}if(D.isNumericSchema()&&typeof B==="string")switch(B){case"Infinity":return 1/0;case"-Infinity":return-1/0;case"NaN":return NaN}return B}},nn=_Q(),ebQ=X6(),AfQ=X6(),BfQ=X6(),_kA=String.fromCharCode(925),QfQ=class{static{B8(this,"JsonReplacer")}values=new Map;counter=0;stage=0;createReplacer(){if(this.stage===1)throw new Error("@aws-sdk/core/protocols - JsonReplacer already created.");if(this.stage===2)throw new Error("@aws-sdk/core/protocols - JsonReplacer exhausted.");return this.stage=1,(A,B)=>{if(B instanceof BfQ.NumericValue){let Q=`${_kA+NaN+this.counter++}_`+B.string;return this.values.set(`"${Q}"`,B.string),Q}if(typeof B==="bigint"){let Q=B.toString(),D=`${_kA+"b"+this.counter++}_`+Q;return this.values.set(`"${D}"`,Q),D}return B}}replaceInJson(A){if(this.stage===0)throw new Error("@aws-sdk/core/protocols - JsonReplacer not created yet.");if(this.stage===2)throw new Error("@aws-sdk/core/protocols - JsonReplacer exhausted.");if(this.stage=2,this.counter===0)return A;for(let[B,Q]of this.values)A=A.replace(B,Q);return A}},mkA=class extends lh{constructor(A){super();this.settings=A}static{B8(this,"JsonShapeSerializer")}buffer;rootSchema;write(A,B){this.rootSchema=nn.NormalizedSchema.of(A),this.buffer=this._write(this.rootSchema,B)}flush(){if(this.rootSchema?.isStructSchema()||this.rootSchema?.isDocumentSchema()){let A=new QfQ;return A.replaceInJson(JSON.stringify(this.buffer,A.createReplacer(),0))}return this.buffer}_write(A,B,Q){let D=B!==null&&typeof B==="object",Z=nn.NormalizedSchema.of(A);if(Z.isListSchema()&&Array.isArray(B)){let F=Z.getValueSchema(),I=[],Y=!!Z.getMergedTraits().sparse;for(let W of B)if(Y||W!=null)I.push(this._write(F,W));return I}else if(Z.isMapSchema()&&D){let F=Z.getValueSchema(),I={},Y=!!Z.getMergedTraits().sparse;for(let[W,J]of Object.entries(B))if(Y||J!=null)I[W]=this._write(F,J);return I}else if(Z.isStructSchema()&&D){let F={};for(let[I,Y]of Z.structIterator()){let W=this.settings.jsonName?Y.getMergedTraits().jsonName??I:I,J=this._write(Y,B[I],Z);if(J!==void 0)F[W]=J}return F}if(B===null&&Q?.isStructSchema())return;if(Z.isBlobSchema()&&(B instanceof Uint8Array||typeof B==="string")){if(Z===this.rootSchema)return B;if(!this.serdeContext?.base64Encoder)throw new Error("Missing base64Encoder in serdeContext");return this.serdeContext?.base64Encoder(B)}if(Z.isTimestampSchema()&&B instanceof Date){let F=this.settings.timestampFormat;switch(F.useTrait?Z.getSchema()===nn.SCHEMA.TIMESTAMP_DEFAULT?F.default:Z.getSchema()??F.default:F.default){case nn.SCHEMA.TIMESTAMP_DATE_TIME:return B.toISOString().replace(".000Z","Z");case nn.SCHEMA.TIMESTAMP_HTTP_DATE:return ebQ.dateToUtcString(B);case nn.SCHEMA.TIMESTAMP_EPOCH_SECONDS:return B.getTime()/1000;default:return console.warn("Missing timestamp format, using epoch seconds",B),B.getTime()/1000}}if(Z.isNumericSchema()&&typeof B==="number"){if(Math.abs(B)===1/0||isNaN(B))return String(B)}let G=Z.getMergedTraits().mediaType;if(Z.isStringSchema()&&typeof B==="string"&&G){if(G==="application/json"||G.endsWith("+json"))return AfQ.LazyJsonString.from(B)}return B}},vA0=class extends lh{constructor(A){super();this.settings=A}static{B8(this,"JsonCodec")}createSerializer(){let A=new mkA(this.settings);return A.setSerdeContext(this.serdeContext),A}createDeserializer(){let A=new ukA(this.settings);return A.setSerdeContext(this.serdeContext),A}},bA0=class extends nbQ.RpcProtocol{static{B8(this,"AwsJsonRpcProtocol")}serializer;deserializer;codec;constructor({defaultNamespace:A}){super({defaultNamespace:A});this.codec=new vA0({timestampFormat:{useTrait:!0,default:ln.SCHEMA.TIMESTAMP_EPOCH_SECONDS},jsonName:!1}),this.serializer=this.codec.createSerializer(),this.deserializer=this.codec.createDeserializer()}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q);if(!D.path.endsWith("/"))D.path+="/";if(Object.assign(D.headers,{"content-type":`application/x-amz-json-${this.getJsonRpcVersion()}`,"x-amz-target":(this.getJsonRpcVersion()==="1.0"?"JsonRpc10.":"JsonProtocol.")+ln.NormalizedSchema.of(A).getName()}),ln.deref(A.input)==="unit"||!D.body)D.body="{}";try{D.headers["content-length"]=String(abQ.calculateBodyLength(D.body))}catch(Z){}return D}getPayloadCodec(){return this.codec}async handleError(A,B,Q,D,Z){let G=xA0(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=ln.TypeRegistry.for(F),W;try{W=Y.getSchema(G)}catch(K){let H=ln.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(H){let z=H.ctor;throw Object.assign(new z(I),D)}throw new Error(I)}let J=ln.NormalizedSchema.of(W),X=D.message??D.Message??"Unknown",V=new W.ctor(X);await this.deserializeHttpMessage(W,B,Q,D);let C={};for(let[K,H]of J.structIterator()){let z=H.getMergedTraits().jsonName??K;C[K]=this.codec.createDeserializer().readObject(H,D[z])}throw Object.assign(V,{$metadata:Z,$response:Q,$fault:J.getMergedTraits().error,message:X,...C}),V}},DfQ=class extends bA0{static{B8(this,"AwsJson1_0Protocol")}constructor({defaultNamespace:A}){super({defaultNamespace:A})}getShapeId(){return"aws.protocols#awsJson1_0"}getJsonRpcVersion(){return"1.0"}},ZfQ=class extends bA0{static{B8(this,"AwsJson1_1Protocol")}constructor({defaultNamespace:A}){super({defaultNamespace:A})}getShapeId(){return"aws.protocols#awsJson1_1"}getJsonRpcVersion(){return"1.1"}},SA0=M6(),aQ1=_Q(),GfQ=PY(),FfQ=class extends SA0.HttpBindingProtocol{static{B8(this,"AwsRestJsonProtocol")}serializer;deserializer;codec;constructor({defaultNamespace:A}){super({defaultNamespace:A});let B={timestampFormat:{useTrait:!0,default:aQ1.SCHEMA.TIMESTAMP_EPOCH_SECONDS},httpBindings:!0,jsonName:!0};this.codec=new vA0(B),this.serializer=new SA0.HttpInterceptingShapeSerializer(this.codec.createSerializer(),B),this.deserializer=new SA0.HttpInterceptingShapeDeserializer(this.codec.createDeserializer(),B)}getShapeId(){return"aws.protocols#restJson1"}getPayloadCodec(){return this.codec}setSerdeContext(A){this.codec.setSerdeContext(A),super.setSerdeContext(A)}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q),Z=aQ1.NormalizedSchema.of(A.input),G=Z.getMemberSchemas();if(!D.headers["content-type"]){let F=Object.values(G).find((I)=>{return!!I.getMergedTraits().httpPayload});if(F){let I=F.getMergedTraits().mediaType;if(I)D.headers["content-type"]=I;else if(F.isStringSchema())D.headers["content-type"]="text/plain";else if(F.isBlobSchema())D.headers["content-type"]="application/octet-stream";else D.headers["content-type"]="application/json"}else if(!Z.isUnitSchema()){if(Object.values(G).find((Y)=>{let{httpQuery:W,httpQueryParams:J,httpHeader:X,httpLabel:V,httpPrefixHeaders:C}=Y.getMergedTraits();return!W&&!J&&!X&&!V&&C===void 0}))D.headers["content-type"]="application/json"}}if(D.headers["content-type"]&&!D.body)D.body="{}";if(D.body)try{D.headers["content-length"]=String(GfQ.calculateBodyLength(D.body))}catch(F){}return D}async handleError(A,B,Q,D,Z){let G=xA0(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=aQ1.TypeRegistry.for(F),W;try{W=Y.getSchema(G)}catch(K){let H=aQ1.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(H){let z=H.ctor;throw Object.assign(new z(I),D)}throw new Error(I)}let J=aQ1.NormalizedSchema.of(W),X=D.message??D.Message??"Unknown",V=new W.ctor(X);await this.deserializeHttpMessage(W,B,Q,D);let C={};for(let[K,H]of J.structIterator()){let z=H.getMergedTraits().jsonName??K;C[K]=this.codec.createDeserializer().readObject(H,D[z])}throw Object.assign(V,{$metadata:Z,$response:Q,$fault:J.getMergedTraits().error,message:X,...C}),V}},IfQ=V6(),YfQ=B8((A)=>{if(A==null)return;if(typeof A==="object"&&"__type"in A)delete A.__type;return IfQ.expectUnion(A)},"awsExpectUnion"),jA0=M6(),zk=_Q(),WfQ=PY(),JfQ=M6(),xkA=_Q(),XfQ=V6(),VfQ=cB(),CfQ=uN(),fA0=class extends lh{constructor(A){super();this.settings=A,this.stringDeserializer=new JfQ.FromStringShapeDeserializer(A)}static{B8(this,"XmlShapeDeserializer")}stringDeserializer;setSerdeContext(A){this.serdeContext=A,this.stringDeserializer.setSerdeContext(A)}read(A,B,Q){let D=xkA.NormalizedSchema.of(A),Z=D.getMemberSchemas();if(D.isStructSchema()&&D.isMemberSchema()&&!!Object.values(Z).find((Y)=>{return!!Y.getMemberTraits().eventPayload})){let Y={},W=Object.keys(Z)[0];if(Z[W].isBlobSchema())Y[W]=B;else Y[W]=this.read(Z[W],B);return Y}let F=(this.serdeContext?.utf8Encoder??VfQ.toUtf8)(B),I=this.parseXml(F);return this.readSchema(A,Q?I[Q]:I)}readSchema(A,B){let Q=xkA.NormalizedSchema.of(A),D=Q.getMergedTraits(),Z=Q.getSchema();if(Q.isListSchema()&&!Array.isArray(B))return this.readSchema(Z,[B]);if(B==null)return B;if(typeof B==="object"){let G=!!D.sparse,F=!!D.xmlFlattened;if(Q.isListSchema()){let Y=Q.getValueSchema(),W=[],J=Y.getMergedTraits().xmlName??"member",X=F?B:(B[0]??B)[J],V=Array.isArray(X)?X:[X];for(let C of V)if(C!=null||G)W.push(this.readSchema(Y,C));return W}let I={};if(Q.isMapSchema()){let Y=Q.getKeySchema(),W=Q.getValueSchema(),J;if(F)J=Array.isArray(B)?B:[B];else J=Array.isArray(B.entry)?B.entry:[B.entry];let X=Y.getMergedTraits().xmlName??"key",V=W.getMergedTraits().xmlName??"value";for(let C of J){let K=C[X],H=C[V];if(H!=null||G)I[K]=this.readSchema(W,H)}return I}if(Q.isStructSchema()){for(let[Y,W]of Q.structIterator()){let J=W.getMergedTraits(),X=!J.httpPayload?W.getMemberTraits().xmlName??Y:J.xmlName??W.getName();if(B[X]!=null)I[Y]=this.readSchema(W,B[X])}return I}if(Q.isDocumentSchema())return B;throw new Error(`@aws-sdk/core/protocols - xml deserializer unhandled schema type for ${Q.getName(!0)}`)}else{if(Q.isListSchema())return[];else if(Q.isMapSchema()||Q.isStructSchema())return{};return this.stringDeserializer.read(Q,B)}}parseXml(A){if(A.length){let B=new CfQ.XMLParser({attributeNamePrefix:"",htmlEntities:!0,ignoreAttributes:!1,ignoreDeclaration:!0,parseTagValue:!1,trimValues:!1,tagValueProcessor:B8((F,I)=>I.trim()===""&&I.includes(`
`)?"":void 0,"tagValueProcessor")});B.addEntity("#xD","\r"),B.addEntity("#10",`
`);let Q;try{Q=B.parse(A,!0)}catch(F){if(F&&typeof F==="object")Object.defineProperty(F,"$responseBodyText",{value:A});throw F}let D="#text",Z=Object.keys(Q)[0],G=Q[Z];if(G[D])G[Z]=G[D],delete G[D];return XfQ.getValueFromTextNode(G)}return{}}},yA0=M6(),XU1=_Q(),KfQ=X6(),HfQ=V6(),zfQ=Hk(),EfQ=class extends lh{constructor(A){super();this.settings=A}static{B8(this,"QueryShapeSerializer")}buffer;write(A,B,Q=""){if(this.buffer===void 0)this.buffer="";let D=XU1.NormalizedSchema.of(A);if(Q&&!Q.endsWith("."))Q+=".";if(D.isBlobSchema()){if(typeof B==="string"||B instanceof Uint8Array)this.writeKey(Q),this.writeValue((this.serdeContext?.base64Encoder??zfQ.toBase64)(B))}else if(D.isBooleanSchema()||D.isNumericSchema()||D.isStringSchema()){if(B!=null)this.writeKey(Q),this.writeValue(String(B))}else if(D.isBigIntegerSchema()){if(B!=null)this.writeKey(Q),this.writeValue(String(B))}else if(D.isBigDecimalSchema()){if(B!=null)this.writeKey(Q),this.writeValue(B instanceof KfQ.NumericValue?B.string:String(B))}else if(D.isTimestampSchema()){if(B instanceof Date)switch(this.writeKey(Q),yA0.determineTimestampFormat(D,this.settings)){case XU1.SCHEMA.TIMESTAMP_DATE_TIME:this.writeValue(B.toISOString().replace(".000Z","Z"));break;case XU1.SCHEMA.TIMESTAMP_HTTP_DATE:this.writeValue(HfQ.dateToUtcString(B));break;case XU1.SCHEMA.TIMESTAMP_EPOCH_SECONDS:this.writeValue(String(B.getTime()/1000));break}}else if(D.isDocumentSchema())throw new Error(`@aws-sdk/core/protocols - QuerySerializer unsupported document type ${D.getName(!0)}`);else if(D.isListSchema()){if(Array.isArray(B))if(B.length===0){if(this.settings.serializeEmptyLists)this.writeKey(Q),this.writeValue("")}else{let Z=D.getValueSchema(),G=this.settings.flattenLists||D.getMergedTraits().xmlFlattened,F=1;for(let I of B){if(I==null)continue;let Y=this.getKey("member",Z.getMergedTraits().xmlName),W=G?`${Q}${F}`:`${Q}${Y}.${F}`;this.write(Z,I,W),++F}}}else if(D.isMapSchema()){if(B&&typeof B==="object"){let Z=D.getKeySchema(),G=D.getValueSchema(),F=D.getMergedTraits().xmlFlattened,I=1;for(let[Y,W]of Object.entries(B)){if(W==null)continue;let J=this.getKey("key",Z.getMergedTraits().xmlName),X=F?`${Q}${I}.${J}`:`${Q}entry.${I}.${J}`,V=this.getKey("value",G.getMergedTraits().xmlName),C=F?`${Q}${I}.${V}`:`${Q}entry.${I}.${V}`;this.write(Z,Y,X),this.write(G,W,C),++I}}}else if(D.isStructSchema()){if(B&&typeof B==="object")for(let[Z,G]of D.structIterator()){if(B[Z]==null)continue;let F=this.getKey(Z,G.getMergedTraits().xmlName),I=`${Q}${F}`;this.write(G,B[Z],I)}}else if(D.isUnitSchema());else throw new Error(`@aws-sdk/core/protocols - QuerySerializer unrecognized schema type ${D.getName(!0)}`)}flush(){if(this.buffer===void 0)throw new Error("@aws-sdk/core/protocols - QuerySerializer cannot flush with nothing written to buffer.");let A=this.buffer;return delete this.buffer,A}getKey(A,B){let Q=B??A;if(this.settings.capitalizeKeys)return Q[0].toUpperCase()+Q.slice(1);return Q}writeKey(A){if(A.endsWith("."))A=A.slice(0,A.length-1);this.buffer+=`&${yA0.extendedEncodeURIComponent(A)}=`}writeValue(A){this.buffer+=yA0.extendedEncodeURIComponent(A)}},dkA=class extends jA0.RpcProtocol{constructor(A){super({defaultNamespace:A.defaultNamespace});this.options=A;let B={timestampFormat:{useTrait:!0,default:zk.SCHEMA.TIMESTAMP_DATE_TIME},httpBindings:!1,xmlNamespace:A.xmlNamespace,serviceNamespace:A.defaultNamespace,serializeEmptyLists:!0};this.serializer=new EfQ(B),this.deserializer=new fA0(B)}static{B8(this,"AwsQueryProtocol")}serializer;deserializer;getShapeId(){return"aws.protocols#awsQuery"}setSerdeContext(A){this.serializer.setSerdeContext(A),this.deserializer.setSerdeContext(A)}getPayloadCodec(){throw new Error("AWSQuery protocol has no payload codec.")}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q);if(!D.path.endsWith("/"))D.path+="/";if(Object.assign(D.headers,{"content-type":"application/x-www-form-urlencoded"}),zk.deref(A.input)==="unit"||!D.body)D.body="";if(D.body=`Action=${A.name.split("#")[1]}&Version=${this.options.version}`+D.body,D.body.endsWith("&"))D.body=D.body.slice(-1);try{D.headers["content-length"]=String(WfQ.calculateBodyLength(D.body))}catch(Z){}return D}async deserializeResponse(A,B,Q){let D=this.deserializer,Z=zk.NormalizedSchema.of(A.output),G={};if(Q.statusCode>=300){let W=await jA0.collectBody(Q.body,B);if(W.byteLength>0)Object.assign(G,await D.read(zk.SCHEMA.DOCUMENT,W));await this.handleError(A,B,Q,G,this.deserializeMetadata(Q))}for(let W in Q.headers){let J=Q.headers[W];delete Q.headers[W],Q.headers[W.toLowerCase()]=J}let F=Z.isStructSchema()&&this.useNestedResult()?A.name.split("#")[1]+"Result":void 0,I=await jA0.collectBody(Q.body,B);if(I.byteLength>0)Object.assign(G,await D.read(Z,I,F));return{$metadata:this.deserializeMetadata(Q),...G}}useNestedResult(){return!0}async handleError(A,B,Q,D,Z){let G=this.loadQueryErrorCode(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=this.loadQueryError(D),W=zk.TypeRegistry.for(F),J;try{if(J=W.find((H)=>zk.NormalizedSchema.of(H).getMergedTraits().awsQueryError?.[0]===I),!J)J=W.getSchema(G)}catch(H){let z=zk.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(z){let $=z.ctor;throw Object.assign(new $(I),Y)}throw new Error(I)}let X=zk.NormalizedSchema.of(J),V=this.loadQueryErrorMessage(D),C=new J.ctor(V),K={};for(let[H,z]of X.structIterator()){let $=z.getMergedTraits().xmlName??H,L=Y[$]??D[$];K[H]=this.deserializer.readSchema(z,L)}throw Object.assign(C,{$metadata:Z,$response:Q,$fault:X.getMergedTraits().error,message:V,...K}),C}loadQueryErrorCode(A,B){let Q=(B.Errors?.[0]?.Error??B.Errors?.Error??B.Error)?.Code;if(Q!==void 0)return Q;if(A.statusCode==404)return"NotFound"}loadQueryError(A){return A.Errors?.[0]?.Error??A.Errors?.Error??A.Error}loadQueryErrorMessage(A){let B=this.loadQueryError(A);return B?.message??B?.Message??A.message??A.Message??"Unknown"}},UfQ=class extends dkA{constructor(A){super(A);this.options=A;let B={capitalizeKeys:!0,flattenLists:!0,serializeEmptyLists:!1};Object.assign(this.serializer.settings,B)}static{B8(this,"AwsEc2QueryProtocol")}useNestedResult(){return!1}},kA0=M6(),sQ1=_Q(),wfQ=PY(),$fQ=V6(),qfQ=uN(),ckA=B8((A,B)=>gkA(A,B).then((Q)=>{if(Q.length){let D=new qfQ.XMLParser({attributeNamePrefix:"",htmlEntities:!0,ignoreAttributes:!1,ignoreDeclaration:!0,parseTagValue:!1,trimValues:!1,tagValueProcessor:B8((Y,W)=>W.trim()===""&&W.includes(`
`)?"":void 0,"tagValueProcessor")});D.addEntity("#xD","\r"),D.addEntity("#10",`
`);let Z;try{Z=D.parse(Q,!0)}catch(Y){if(Y&&typeof Y==="object")Object.defineProperty(Y,"$responseBodyText",{value:Q});throw Y}let G="#text",F=Object.keys(Z)[0],I=Z[F];if(I[G])I[F]=I[G],delete I[G];return $fQ.getValueFromTextNode(I)}return{}}),"parseXmlBody"),NfQ=B8(async(A,B)=>{let Q=await ckA(A,B);if(Q.Error)Q.Error.message=Q.Error.message??Q.Error.Message;return Q},"parseXmlErrorBody"),lkA=B8((A,B)=>{if(B?.Error?.Code!==void 0)return B.Error.Code;if(B?.Code!==void 0)return B.Code;if(A.statusCode==404)return"NotFound"},"loadRestXmlErrorCode"),rN=HQ1(),ch=_Q(),LfQ=X6(),vkA=V6(),bkA=Hk(),pkA=class extends lh{constructor(A){super();this.settings=A}static{B8(this,"XmlShapeSerializer")}stringBuffer;byteBuffer;buffer;write(A,B){let Q=ch.NormalizedSchema.of(A);if(Q.isStringSchema()&&typeof B==="string")this.stringBuffer=B;else if(Q.isBlobSchema())this.byteBuffer="byteLength"in B?B:(this.serdeContext?.base64Decoder??bkA.fromBase64)(B);else{this.buffer=this.writeStruct(Q,B,void 0);let D=Q.getMergedTraits();if(D.httpPayload&&!D.xmlName)this.buffer.withName(Q.getName())}}flush(){if(this.byteBuffer!==void 0){let B=this.byteBuffer;return delete this.byteBuffer,B}if(this.stringBuffer!==void 0){let B=this.stringBuffer;return delete this.stringBuffer,B}let A=this.buffer;if(this.settings.xmlNamespace){if(!A?.attributes?.xmlns)A.addAttribute("xmlns",this.settings.xmlNamespace)}return delete this.buffer,A.toString()}writeStruct(A,B,Q){let D=A.getMergedTraits(),Z=A.isMemberSchema()&&!D.httpPayload?A.getMemberTraits().xmlName??A.getMemberName():D.xmlName??A.getName();if(!Z||!A.isStructSchema())throw new Error(`@aws-sdk/core/protocols - xml serializer, cannot write struct with empty name or non-struct, schema=${A.getName(!0)}.`);let G=rN.XmlNode.of(Z),[F,I]=this.getXmlnsAttribute(A,Q);if(I)G.addAttribute(F,I);for(let[Y,W]of A.structIterator()){let J=B[Y];if(J!=null){if(W.getMergedTraits().xmlAttribute){G.addAttribute(W.getMergedTraits().xmlName??Y,this.writeSimple(W,J));continue}if(W.isListSchema())this.writeList(W,J,G,I);else if(W.isMapSchema())this.writeMap(W,J,G,I);else if(W.isStructSchema())G.addChildNode(this.writeStruct(W,J,I));else{let X=rN.XmlNode.of(W.getMergedTraits().xmlName??W.getMemberName());this.writeSimpleInto(W,J,X,I),G.addChildNode(X)}}}return G}writeList(A,B,Q,D){if(!A.isMemberSchema())throw new Error(`@aws-sdk/core/protocols - xml serializer, cannot write non-member list: ${A.getName(!0)}`);let Z=A.getMergedTraits(),G=A.getValueSchema(),F=G.getMergedTraits(),I=!!F.sparse,Y=!!Z.xmlFlattened,[W,J]=this.getXmlnsAttribute(A,D),X=B8((V,C)=>{if(G.isListSchema())this.writeList(G,Array.isArray(C)?C:[C],V,J);else if(G.isMapSchema())this.writeMap(G,C,V,J);else if(G.isStructSchema()){let K=this.writeStruct(G,C,J);V.addChildNode(K.withName(Y?Z.xmlName??A.getMemberName():F.xmlName??"member"))}else{let K=rN.XmlNode.of(Y?Z.xmlName??A.getMemberName():F.xmlName??"member");this.writeSimpleInto(G,C,K,J),V.addChildNode(K)}},"writeItem");if(Y){for(let V of B)if(I||V!=null)X(Q,V)}else{let V=rN.XmlNode.of(Z.xmlName??A.getMemberName());if(J)V.addAttribute(W,J);for(let C of B)if(I||C!=null)X(V,C);Q.addChildNode(V)}}writeMap(A,B,Q,D,Z=!1){if(!A.isMemberSchema())throw new Error(`@aws-sdk/core/protocols - xml serializer, cannot write non-member map: ${A.getName(!0)}`);let G=A.getMergedTraits(),F=A.getKeySchema(),Y=F.getMergedTraits().xmlName??"key",W=A.getValueSchema(),J=W.getMergedTraits(),X=J.xmlName??"value",V=!!J.sparse,C=!!G.xmlFlattened,[K,H]=this.getXmlnsAttribute(A,D),z=B8(($,L,N)=>{let O=rN.XmlNode.of(Y,L),[R,T]=this.getXmlnsAttribute(F,H);if(T)O.addAttribute(R,T);$.addChildNode(O);let j=rN.XmlNode.of(X);if(W.isListSchema())this.writeList(W,N,j,H);else if(W.isMapSchema())this.writeMap(W,N,j,H,!0);else if(W.isStructSchema())j=this.writeStruct(W,N,H);else this.writeSimpleInto(W,N,j,H);$.addChildNode(j)},"addKeyValue");if(C){for(let[$,L]of Object.entries(B))if(V||L!=null){let N=rN.XmlNode.of(G.xmlName??A.getMemberName());z(N,$,L),Q.addChildNode(N)}}else{let $;if(!Z){if($=rN.XmlNode.of(G.xmlName??A.getMemberName()),H)$.addAttribute(K,H);Q.addChildNode($)}for(let[L,N]of Object.entries(B))if(V||N!=null){let O=rN.XmlNode.of("entry");z(O,L,N),(Z?Q:$).addChildNode(O)}}}writeSimple(A,B){if(B===null)throw new Error("@aws-sdk/core/protocols - (XML serializer) cannot write null value.");let Q=ch.NormalizedSchema.of(A),D=null;if(B&&typeof B==="object")if(Q.isBlobSchema())D=(this.serdeContext?.base64Encoder??bkA.toBase64)(B);else if(Q.isTimestampSchema()&&B instanceof Date){let Z=this.settings.timestampFormat;switch(Z.useTrait?Q.getSchema()===ch.SCHEMA.TIMESTAMP_DEFAULT?Z.default:Q.getSchema()??Z.default:Z.default){case ch.SCHEMA.TIMESTAMP_DATE_TIME:D=B.toISOString().replace(".000Z","Z");break;case ch.SCHEMA.TIMESTAMP_HTTP_DATE:D=vkA.dateToUtcString(B);break;case ch.SCHEMA.TIMESTAMP_EPOCH_SECONDS:D=String(B.getTime()/1000);break;default:console.warn("Missing timestamp format, using http date",B),D=vkA.dateToUtcString(B);break}}else if(Q.isBigDecimalSchema()&&B){if(B instanceof LfQ.NumericValue)return B.string;return String(B)}else if(Q.isMapSchema()||Q.isListSchema())throw new Error("@aws-sdk/core/protocols - xml serializer, cannot call _write() on List/Map schema, call writeList or writeMap() instead.");else throw new Error(`@aws-sdk/core/protocols - xml serializer, unhandled schema type for object value and schema: ${Q.getName(!0)}`);if(Q.isStringSchema()||Q.isBooleanSchema()||Q.isNumericSchema()||Q.isBigIntegerSchema()||Q.isBigDecimalSchema())D=String(B);if(D===null)throw new Error(`Unhandled schema-value pair ${Q.getName(!0)}=${B}`);return D}writeSimpleInto(A,B,Q,D){let Z=this.writeSimple(A,B),G=ch.NormalizedSchema.of(A),F=new rN.XmlText(Z),[I,Y]=this.getXmlnsAttribute(G,D);if(Y)Q.addAttribute(I,Y);Q.addChildNode(F)}getXmlnsAttribute(A,B){let Q=A.getMergedTraits(),[D,Z]=Q.xmlNamespace??[];if(Z&&Z!==B)return[D?`xmlns:${D}`:"xmlns",Z];return[void 0,void 0]}},ikA=class extends lh{constructor(A){super();this.settings=A}static{B8(this,"XmlCodec")}createSerializer(){let A=new pkA(this.settings);return A.setSerdeContext(this.serdeContext),A}createDeserializer(){let A=new fA0(this.settings);return A.setSerdeContext(this.serdeContext),A}},MfQ=class extends kA0.HttpBindingProtocol{static{B8(this,"AwsRestXmlProtocol")}codec;serializer;deserializer;constructor(A){super(A);let B={timestampFormat:{useTrait:!0,default:sQ1.SCHEMA.TIMESTAMP_DATE_TIME},httpBindings:!0,xmlNamespace:A.xmlNamespace,serviceNamespace:A.defaultNamespace};this.codec=new ikA(B),this.serializer=new kA0.HttpInterceptingShapeSerializer(this.codec.createSerializer(),B),this.deserializer=new kA0.HttpInterceptingShapeDeserializer(this.codec.createDeserializer(),B)}getPayloadCodec(){return this.codec}getShapeId(){return"aws.protocols#restXml"}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q),Z=sQ1.NormalizedSchema.of(A.input),G=Z.getMemberSchemas();if(D.path=String(D.path).split("/").filter((F)=>{return F!=="{Bucket}"}).join("/")||"/",!D.headers["content-type"]){let F=Object.values(G).find((I)=>{return!!I.getMergedTraits().httpPayload});if(F){let I=F.getMergedTraits().mediaType;if(I)D.headers["content-type"]=I;else if(F.isStringSchema())D.headers["content-type"]="text/plain";else if(F.isBlobSchema())D.headers["content-type"]="application/octet-stream";else D.headers["content-type"]="application/xml"}else if(!Z.isUnitSchema()){if(Object.values(G).find((Y)=>{let{httpQuery:W,httpQueryParams:J,httpHeader:X,httpLabel:V,httpPrefixHeaders:C}=Y.getMergedTraits();return!W&&!J&&!X&&!V&&C===void 0}))D.headers["content-type"]="application/xml"}}if(D.headers["content-type"]==="application/xml"){if(typeof D.body==="string")D.body='<?xml version="1.0" encoding="UTF-8"?>'+D.body}if(D.body)try{D.headers["content-length"]=String(wfQ.calculateBodyLength(D.body))}catch(F){}return D}async deserializeResponse(A,B,Q){return super.deserializeResponse(A,B,Q)}async handleError(A,B,Q,D,Z){let G=lkA(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=sQ1.TypeRegistry.for(F),W;try{W=Y.getSchema(G)}catch(K){let H=sQ1.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(H){let z=H.ctor;throw Object.assign(new z(I),D)}throw new Error(I)}let J=sQ1.NormalizedSchema.of(W),X=D.Error?.message??D.Error?.Message??D.message??D.Message??"Unknown",V=new W.ctor(X);await this.deserializeHttpMessage(W,B,Q,D);let C={};for(let[K,H]of J.structIterator()){let z=H.getMergedTraits().xmlName??K,$=D.Error?.[z]??D[z];C[K]=this.codec.createDeserializer().readSchema(H,$)}throw Object.assign(V,{$metadata:Z,$response:Q,$fault:J.getMergedTraits().error,message:X,...C}),V}}});
var amA=E((MJ5,nmA)=>{var{defineProperty:Ww1,getOwnPropertyDescriptor:SsQ,getOwnPropertyNames:jsQ}=Object,ysQ=Object.prototype.hasOwnProperty,ksQ=(A,B)=>Ww1(A,"name",{value:B,configurable:!0}),_sQ=(A,B)=>{for(var Q in B)Ww1(A,Q,{get:B[Q],enumerable:!0})},xsQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of jsQ(B))if(!ysQ.call(A,Z)&&Z!==Q)Ww1(A,Z,{get:()=>B[Z],enumerable:!(D=SsQ(B,Z))||D.enumerable})}return A},vsQ=(A)=>xsQ(Ww1({},"__esModule",{value:!0}),A),imA={};_sQ(imA,{isArrayBuffer:()=>bsQ});nmA.exports=vsQ(imA);var bsQ=ksQ((A)=>typeof ArrayBuffer==="function"&&A instanceof ArrayBuffer||Object.prototype.toString.call(A)==="[object ArrayBuffer]","isArrayBuffer")});
var an=E((EY5,F_A)=>{var{defineProperty:KU1,getOwnPropertyDescriptor:RfQ,getOwnPropertyNames:OfQ}=Object,TfQ=Object.prototype.hasOwnProperty,YT=(A,B)=>KU1(A,"name",{value:B,configurable:!0}),PfQ=(A,B)=>{for(var Q in B)KU1(A,Q,{get:B[Q],enumerable:!0})},SfQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of OfQ(B))if(!TfQ.call(A,Z)&&Z!==Q)KU1(A,Z,{get:()=>B[Z],enumerable:!(D=RfQ(B,Z))||D.enumerable})}return A},jfQ=(A)=>SfQ(KU1({},"__esModule",{value:!0}),A),tkA={};PfQ(tkA,{DEFAULT_UA_APP_ID:()=>ekA,getUserAgentMiddlewareOptions:()=>G_A,getUserAgentPlugin:()=>hfQ,resolveUserAgentConfig:()=>B_A,userAgentMiddleware:()=>Z_A});F_A.exports=jfQ(tkA);var yfQ=CB(),ekA=void 0;function A_A(A){if(A===void 0)return!0;return typeof A==="string"&&A.length<=50}YT(A_A,"isValidUserAgentAppId");function B_A(A){let B=yfQ.normalizeProvider(A.userAgentAppId??ekA),{customUserAgent:Q}=A;return Object.assign(A,{customUserAgent:typeof Q==="string"?[[Q]]:Q,userAgentAppId:YT(async()=>{let D=await B();if(!A_A(D)){let Z=A.logger?.constructor?.name==="NoOpLogger"||!A.logger?console:A.logger;if(typeof D!=="string")Z?.warn("userAgentAppId must be a string or undefined.");else if(D.length>50)Z?.warn("The provided userAgentAppId exceeds the maximum length of 50 characters.")}return D},"userAgentAppId")})}YT(B_A,"resolveUserAgentConfig");var kfQ=mn(),_fQ=EV(),oN=VI(),xfQ=/\d{12}\.ddb/;async function Q_A(A,B,Q){if(Q.request?.headers?.["smithy-protocol"]==="rpc-v2-cbor")oN.setFeature(A,"PROTOCOL_RPC_V2_CBOR","M");if(typeof B.retryStrategy==="function"){let G=await B.retryStrategy();if(typeof G.acquireInitialRetryToken==="function")if(G.constructor?.name?.includes("Adaptive"))oN.setFeature(A,"RETRY_MODE_ADAPTIVE","F");else oN.setFeature(A,"RETRY_MODE_STANDARD","E");else oN.setFeature(A,"RETRY_MODE_LEGACY","D")}if(typeof B.accountIdEndpointMode==="function"){let G=A.endpointV2;if(String(G?.url?.hostname).match(xfQ))oN.setFeature(A,"ACCOUNT_ID_ENDPOINT","O");switch(await B.accountIdEndpointMode?.()){case"disabled":oN.setFeature(A,"ACCOUNT_ID_MODE_DISABLED","Q");break;case"preferred":oN.setFeature(A,"ACCOUNT_ID_MODE_PREFERRED","P");break;case"required":oN.setFeature(A,"ACCOUNT_ID_MODE_REQUIRED","R");break}}let Z=A.__smithy_context?.selectedHttpAuthScheme?.identity;if(Z?.$source){let G=Z;if(G.accountId)oN.setFeature(A,"RESOLVED_ACCOUNT_ID","T");for(let[F,I]of Object.entries(G.$source??{}))oN.setFeature(A,F,I)}}YT(Q_A,"checkFeatures");var skA="user-agent",gA0="x-amz-user-agent",rkA=" ",uA0="/",vfQ=/[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w]/g,bfQ=/[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w\#]/g,okA="-",ffQ=1024;function D_A(A){let B="";for(let Q in A){let D=A[Q];if(B.length+D.length+1<=ffQ){if(B.length)B+=","+D;else B+=D;continue}break}return B}YT(D_A,"encodeFeatures");var Z_A=YT((A)=>(B,Q)=>async(D)=>{let{request:Z}=D;if(!_fQ.HttpRequest.isInstance(Z))return B(D);let{headers:G}=Z,F=Q?.userAgent?.map(CU1)||[],I=(await A.defaultUserAgentProvider()).map(CU1);await Q_A(Q,A,D);let Y=Q;I.push(`m/${D_A(Object.assign({},Q.__smithy_context?.features,Y.__aws_sdk_context?.features))}`);let W=A?.customUserAgent?.map(CU1)||[],J=await A.userAgentAppId();if(J)I.push(CU1([`app/${J}`]));let X=kfQ.getUserAgentPrefix(),V=(X?[X]:[]).concat([...I,...F,...W]).join(rkA),C=[...I.filter((K)=>K.startsWith("aws-sdk-")),...W].join(rkA);if(A.runtime!=="browser"){if(C)G[gA0]=G[gA0]?`${G[skA]} ${C}`:C;G[skA]=V}else G[gA0]=V;return B({...D,request:Z})},"userAgentMiddleware"),CU1=YT((A)=>{let B=A[0].split(uA0).map((F)=>F.replace(vfQ,okA)).join(uA0),Q=A[1]?.replace(bfQ,okA),D=B.indexOf(uA0),Z=B.substring(0,D),G=B.substring(D+1);if(Z==="api")G=G.toLowerCase();return[Z,G,Q].filter((F)=>F&&F.length>0).reduce((F,I,Y)=>{switch(Y){case 0:return I;case 1:return`${F}/${I}`;default:return`${F}#${I}`}},"")},"escapeUserAgent"),G_A={name:"getUserAgentMiddleware",step:"build",priority:"low",tags:["SET_USER_AGENT","USER_AGENT"],override:!0},hfQ=YT((A)=>({applyToStack:YT((B)=>{B.add(Z_A(A),G_A)},"applyToStack")}),"getUserAgentPlugin")});
var aw1=E((dX5,tnA)=>{var{defineProperty:iw1,getOwnPropertyDescriptor:Z24,getOwnPropertyNames:inA}=Object,G24=Object.prototype.hasOwnProperty,nw1=(A,B)=>iw1(A,"name",{value:B,configurable:!0}),F24=(A,B)=>function Q(){return A&&(B=A[inA(A)[0]](A=0)),B},nnA=(A,B)=>{for(var Q in B)iw1(A,Q,{get:B[Q],enumerable:!0})},I24=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of inA(B))if(!G24.call(A,Z)&&Z!==Q)iw1(A,Z,{get:()=>B[Z],enumerable:!(D=Z24(B,Z))||D.enumerable})}return A},Y24=(A)=>I24(iw1({},"__esModule",{value:!0}),A),anA={};nnA(anA,{GetRoleCredentialsCommand:()=>VQ0.GetRoleCredentialsCommand,SSOClient:()=>VQ0.SSOClient});var VQ0,W24=F24({"src/loadSso.ts"(){VQ0=PiA()}}),snA={};nnA(snA,{fromSSO:()=>X24,isSsoProfile:()=>rnA,validateSsoProfile:()=>onA});tnA.exports=Y24(snA);var rnA=nw1((A)=>A&&(typeof A.sso_start_url==="string"||typeof A.sso_account_id==="string"||typeof A.sso_session==="string"||typeof A.sso_region==="string"||typeof A.sso_role_name==="string"),"isSsoProfile"),lnA=Oz(),J24=cnA(),yw=Q9(),pw1=D3(),S41=!1,pnA=nw1(async({ssoStartUrl:A,ssoSession:B,ssoAccountId:Q,ssoRegion:D,ssoRoleName:Z,ssoClient:G,clientConfig:F,parentClientConfig:I,profile:Y,logger:W})=>{let J,X="To refresh this SSO session run aws sso login with the corresponding profile.";if(B)try{let f=await J24.fromSso({profile:Y})();J={accessToken:f.token,expiresAt:new Date(f.expiration).toISOString()}}catch(f){throw new yw.CredentialsProviderError(f.message,{tryNextLink:S41,logger:W})}else try{J=await pw1.getSSOTokenFromFile(A)}catch(f){throw new yw.CredentialsProviderError("The SSO session associated with this profile is invalid. To refresh this SSO session run aws sso login with the corresponding profile.",{tryNextLink:S41,logger:W})}if(new Date(J.expiresAt).getTime()-Date.now()<=0)throw new yw.CredentialsProviderError("The SSO session associated with this profile has expired. To refresh this SSO session run aws sso login with the corresponding profile.",{tryNextLink:S41,logger:W});let{accessToken:V}=J,{SSOClient:C,GetRoleCredentialsCommand:K}=await Promise.resolve().then(()=>(W24(),anA)),H=G||new C(Object.assign({},F??{},{logger:F?.logger??I?.logger,region:F?.region??D})),z;try{z=await H.send(new K({accountId:Q,roleName:Z,accessToken:V}))}catch(f){throw new yw.CredentialsProviderError(f,{tryNextLink:S41,logger:W})}let{roleCredentials:{accessKeyId:$,secretAccessKey:L,sessionToken:N,expiration:O,credentialScope:R,accountId:T}={}}=z;if(!$||!L||!N||!O)throw new yw.CredentialsProviderError("SSO returns an invalid temporary credential.",{tryNextLink:S41,logger:W});let j={accessKeyId:$,secretAccessKey:L,sessionToken:N,expiration:new Date(O),...R&&{credentialScope:R},...T&&{accountId:T}};if(B)lnA.setCredentialFeature(j,"CREDENTIALS_SSO","s");else lnA.setCredentialFeature(j,"CREDENTIALS_SSO_LEGACY","u");return j},"resolveSSOCredentials"),onA=nw1((A,B)=>{let{sso_start_url:Q,sso_account_id:D,sso_region:Z,sso_role_name:G}=A;if(!Q||!D||!Z||!G)throw new yw.CredentialsProviderError(`Profile is configured with invalid SSO credentials. Required parameters "sso_account_id", "sso_region", "sso_role_name", "sso_start_url". Got ${Object.keys(A).join(", ")}
Reference: https://docs.aws.amazon.com/cli/latest/userguide/cli-configure-sso.html`,{tryNextLink:!1,logger:B});return A},"validateSsoProfile"),X24=nw1((A={})=>async({callerClientConfig:B}={})=>{A.logger?.debug("@aws-sdk/credential-provider-sso - fromSSO");let{ssoStartUrl:Q,ssoAccountId:D,ssoRegion:Z,ssoRoleName:G,ssoSession:F}=A,{ssoClient:I}=A,Y=pw1.getProfileName({profile:A.profile??B?.profile});if(!Q&&!D&&!Z&&!G&&!F){let J=(await pw1.parseKnownFiles(A))[Y];if(!J)throw new yw.CredentialsProviderError(`Profile ${Y} was not found.`,{logger:A.logger});if(!rnA(J))throw new yw.CredentialsProviderError(`Profile ${Y} is not configured with SSO credentials.`,{logger:A.logger});if(J?.sso_session){let $=(await pw1.loadSsoSessionData(A))[J.sso_session],L=` configurations in profile ${Y} and sso-session ${J.sso_session}`;if(Z&&Z!==$.sso_region)throw new yw.CredentialsProviderError("Conflicting SSO region"+L,{tryNextLink:!1,logger:A.logger});if(Q&&Q!==$.sso_start_url)throw new yw.CredentialsProviderError("Conflicting SSO start_url"+L,{tryNextLink:!1,logger:A.logger});J.sso_region=$.sso_region,J.sso_start_url=$.sso_start_url}let{sso_start_url:X,sso_account_id:V,sso_region:C,sso_role_name:K,sso_session:H}=onA(J,A.logger);return pnA({ssoStartUrl:X,ssoSession:H,ssoAccountId:V,ssoRegion:C,ssoRoleName:K,ssoClient:I,clientConfig:A.clientConfig,parentClientConfig:A.parentClientConfig,profile:Y})}else if(!Q||!D||!Z||!G)throw new yw.CredentialsProviderError('Incomplete configuration. The fromSSO() argument hash must include "ssoStartUrl", "ssoAccountId", "ssoRegion", "ssoRoleName"',{tryNextLink:!1,logger:A.logger});else return pnA({ssoStartUrl:Q,ssoSession:F,ssoAccountId:D,ssoRegion:Z,ssoRoleName:G,ssoClient:I,clientConfig:A.clientConfig,parentClientConfig:A.parentClientConfig,profile:Y})},"fromSSO")});
var cA0=E(($Y5,E_A)=>{var{defineProperty:zU1,getOwnPropertyDescriptor:nfQ,getOwnPropertyNames:afQ}=Object,sfQ=Object.prototype.hasOwnProperty,rfQ=(A,B)=>zU1(A,"name",{value:B,configurable:!0}),ofQ=(A,B)=>{for(var Q in B)zU1(A,Q,{get:B[Q],enumerable:!0})},tfQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of afQ(B))if(!sfQ.call(A,Z)&&Z!==Q)zU1(A,Z,{get:()=>B[Z],enumerable:!(D=nfQ(B,Z))||D.enumerable})}return A},efQ=(A)=>tfQ(zU1({},"__esModule",{value:!0}),A),J_A={};ofQ(J_A,{ENV_ACCOUNT_ID:()=>z_A,ENV_CREDENTIAL_SCOPE:()=>H_A,ENV_EXPIRATION:()=>K_A,ENV_KEY:()=>X_A,ENV_SECRET:()=>V_A,ENV_SESSION:()=>C_A,fromEnv:()=>QhQ});E_A.exports=efQ(J_A);var AhQ=Sw(),BhQ=Q9(),X_A="AWS_ACCESS_KEY_ID",V_A="AWS_SECRET_ACCESS_KEY",C_A="AWS_SESSION_TOKEN",K_A="AWS_CREDENTIAL_EXPIRATION",H_A="AWS_CREDENTIAL_SCOPE",z_A="AWS_ACCOUNT_ID",QhQ=rfQ((A)=>async()=>{A?.logger?.debug("@aws-sdk/credential-provider-env - fromEnv");let B=process.env[X_A],Q=process.env[V_A],D=process.env[C_A],Z=process.env[K_A],G=process.env[H_A],F=process.env[z_A];if(B&&Q){let I={accessKeyId:B,secretAccessKey:Q,...D&&{sessionToken:D},...Z&&{expiration:new Date(Z)},...G&&{credentialScope:G},...F&&{accountId:F}};return AhQ.setCredentialFeature(I,"CREDENTIALS_ENV_VARS","g"),I}throw new BhQ.CredentialsProviderError("Unable to find environment variable credentials.",{logger:A?.logger})},"fromEnv")});
var cQ1=E((SI5,RjA)=>{var{defineProperty:cE1,getOwnPropertyDescriptor:IxQ,getOwnPropertyNames:YxQ}=Object,WxQ=Object.prototype.hasOwnProperty,dE1=(A,B)=>cE1(A,"name",{value:B,configurable:!0}),JxQ=(A,B)=>{for(var Q in B)cE1(A,Q,{get:B[Q],enumerable:!0})},XxQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of YxQ(B))if(!WxQ.call(A,Z)&&Z!==Q)cE1(A,Z,{get:()=>B[Z],enumerable:!(D=IxQ(B,Z))||D.enumerable})}return A},VxQ=(A)=>XxQ(cE1({},"__esModule",{value:!0}),A),qjA={};JxQ(qjA,{getHostHeaderPlugin:()=>KxQ,hostHeaderMiddleware:()=>LjA,hostHeaderMiddlewareOptions:()=>MjA,resolveHostHeaderConfig:()=>NjA});RjA.exports=VxQ(qjA);var CxQ=EV();function NjA(A){return A}dE1(NjA,"resolveHostHeaderConfig");var LjA=dE1((A)=>(B)=>async(Q)=>{if(!CxQ.HttpRequest.isInstance(Q.request))return B(Q);let{request:D}=Q,{handlerProtocol:Z=""}=A.requestHandler.metadata||{};if(Z.indexOf("h2")>=0&&!D.headers[":authority"])delete D.headers.host,D.headers[":authority"]=D.hostname+(D.port?":"+D.port:"");else if(!D.headers.host){let G=D.hostname;if(D.port!=null)G+=`:${D.port}`;D.headers.host=G}return B(Q)},"hostHeaderMiddleware"),MjA={name:"hostHeaderMiddleware",step:"build",priority:"low",tags:["HOST"],override:!0},KxQ=dE1((A)=>({applyToStack:dE1((B)=>{B.add(LjA(A),MjA)},"applyToStack")}),"getHostHeaderPlugin")});
var cnA=E((mX5,dnA)=>{var{create:uA4,defineProperty:P41,getOwnPropertyDescriptor:mA4,getOwnPropertyNames:dA4,getPrototypeOf:cA4}=Object,lA4=Object.prototype.hasOwnProperty,MT=(A,B)=>P41(A,"name",{value:B,configurable:!0}),pA4=(A,B)=>{for(var Q in B)P41(A,Q,{get:B[Q],enumerable:!0})},hnA=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of dA4(B))if(!lA4.call(A,Z)&&Z!==Q)P41(A,Z,{get:()=>B[Z],enumerable:!(D=mA4(B,Z))||D.enumerable})}return A},gnA=(A,B,Q)=>(Q=A!=null?uA4(cA4(A)):{},hnA(B||!A||!A.__esModule?P41(Q,"default",{value:A,enumerable:!0}):Q,A)),iA4=(A)=>hnA(P41({},"__esModule",{value:!0}),A),unA={};pA4(unA,{fromEnvSigningName:()=>sA4,fromSso:()=>mnA,fromStatic:()=>Q24,nodeProvider:()=>D24});dnA.exports=iA4(unA);var nA4=Oz(),aA4=iB0(),XK=Q9(),sA4=MT(({logger:A,signingName:B}={})=>async()=>{if(A?.debug?.("@aws-sdk/token-providers - fromEnvSigningName"),!B)throw new XK.TokenProviderError("Please pass 'signingName' to compute environment variable key",{logger:A});let Q=aA4.getBearerTokenEnvKey(B);if(!(Q in process.env))throw new XK.TokenProviderError(`Token not present in '${Q}' environment variable`,{logger:A});let D={token:process.env[Q]};return nA4.setTokenFeature(D,"BEARER_SERVICE_ENV_VARS","3"),D},"fromEnvSigningName"),rA4=300000,XQ0="To refresh this SSO session run 'aws sso login' with the corresponding profile.",oA4=MT(async(A,B={})=>{let{SSOOIDCClient:Q}=await Promise.resolve().then(()=>gnA(JQ0()));return new Q(Object.assign({},B.clientConfig??{},{region:A??B.clientConfig?.region,logger:B.clientConfig?.logger??B.parentClientConfig?.logger}))},"getSsoOidcClient"),tA4=MT(async(A,B,Q={})=>{let{CreateTokenCommand:D}=await Promise.resolve().then(()=>gnA(JQ0()));return(await oA4(B,Q)).send(new D({clientId:A.clientId,clientSecret:A.clientSecret,refreshToken:A.refreshToken,grantType:"refresh_token"}))},"getNewSsoOidcToken"),bnA=MT((A)=>{if(A.expiration&&A.expiration.getTime()<Date.now())throw new XK.TokenProviderError(`Token is expired. ${XQ0}`,!1)},"validateTokenExpiry"),Zg=MT((A,B,Q=!1)=>{if(typeof B==="undefined")throw new XK.TokenProviderError(`Value not present for '${A}' in SSO Token${Q?". Cannot refresh":""}. ${XQ0}`,!1)},"validateTokenKey"),T41=D3(),eA4=J1("fs"),{writeFile:A24}=eA4.promises,B24=MT((A,B)=>{let Q=T41.getSSOTokenFilepath(A),D=JSON.stringify(B,null,2);return A24(Q,D)},"writeSSOTokenToFile"),fnA=new Date(0),mnA=MT((A={})=>async({callerClientConfig:B}={})=>{let Q={...A,parentClientConfig:{...B,...A.parentClientConfig}};Q.logger?.debug("@aws-sdk/token-providers - fromSso");let D=await T41.parseKnownFiles(Q),Z=T41.getProfileName({profile:Q.profile??B?.profile}),G=D[Z];if(!G)throw new XK.TokenProviderError(`Profile '${Z}' could not be found in shared credentials file.`,!1);else if(!G.sso_session)throw new XK.TokenProviderError(`Profile '${Z}' is missing required property 'sso_session'.`);let F=G.sso_session,Y=(await T41.loadSsoSessionData(Q))[F];if(!Y)throw new XK.TokenProviderError(`Sso session '${F}' could not be found in shared credentials file.`,!1);for(let H of["sso_start_url","sso_region"])if(!Y[H])throw new XK.TokenProviderError(`Sso session '${F}' is missing required property '${H}'.`,!1);let{sso_start_url:W,sso_region:J}=Y,X;try{X=await T41.getSSOTokenFromFile(F)}catch(H){throw new XK.TokenProviderError(`The SSO session token associated with profile=${Z} was not found or is invalid. ${XQ0}`,!1)}Zg("accessToken",X.accessToken),Zg("expiresAt",X.expiresAt);let{accessToken:V,expiresAt:C}=X,K={token:V,expiration:new Date(C)};if(K.expiration.getTime()-Date.now()>rA4)return K;if(Date.now()-fnA.getTime()<30000)return bnA(K),K;Zg("clientId",X.clientId,!0),Zg("clientSecret",X.clientSecret,!0),Zg("refreshToken",X.refreshToken,!0);try{fnA.setTime(Date.now());let H=await tA4(X,J,Q);Zg("accessToken",H.accessToken),Zg("expiresIn",H.expiresIn);let z=new Date(Date.now()+H.expiresIn*1000);try{await B24(F,{...X,accessToken:H.accessToken,expiresAt:z.toISOString(),refreshToken:H.refreshToken})}catch($){}return{token:H.accessToken,expiration:z}}catch(H){return bnA(K),K}},"fromSso"),Q24=MT(({token:A,logger:B})=>async()=>{if(B?.debug("@aws-sdk/token-providers - fromStatic"),!A||!A.token)throw new XK.TokenProviderError("Please pass a valid token to fromStatic",!1);return A},"fromStatic"),D24=MT((A={})=>XK.memoize(XK.chain(mnA(A),async()=>{throw new XK.TokenProviderError("Could not load token from any providers",!1)}),(B)=>B.expiration!==void 0&&B.expiration.getTime()-Date.now()<300000,(B)=>B.expiration!==void 0),"nodeProvider")});
var dA0=E((I_A)=>{Object.defineProperty(I_A,"__esModule",{value:!0});I_A.resolveHttpAuthSchemeConfig=I_A.defaultCognitoIdentityHttpAuthSchemeProvider=I_A.defaultCognitoIdentityHttpAuthSchemeParametersProvider=void 0;var gfQ=VI(),mA0=J5(),ufQ=async(A,B,Q)=>{return{operation:mA0.getSmithyContext(B).operation,region:await mA0.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};I_A.defaultCognitoIdentityHttpAuthSchemeParametersProvider=ufQ;function mfQ(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"cognito-identity",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function HU1(A){return{schemeId:"smithy.api#noAuth"}}var dfQ=(A)=>{let B=[];switch(A.operation){case"GetCredentialsForIdentity":{B.push(HU1(A));break}case"GetId":{B.push(HU1(A));break}case"GetOpenIdToken":{B.push(HU1(A));break}case"UnlinkIdentity":{B.push(HU1(A));break}default:B.push(mfQ(A))}return B};I_A.defaultCognitoIdentityHttpAuthSchemeProvider=dfQ;var cfQ=(A)=>{let B=gfQ.resolveAwsSdkSigV4Config(A);return Object.assign(B,{authSchemePreference:mA0.normalizeProvider(A.authSchemePreference??[])})};I_A.resolveHttpAuthSchemeConfig=cfQ});
var ddA=E((udA)=>{Object.defineProperty(udA,"__esModule",{value:!0});udA.fromBase64=void 0;var yrQ=YD(),krQ=/^[A-Za-z0-9+/]*={0,2}$/,_rQ=(A)=>{if(A.length*3%4!==0)throw new TypeError("Incorrect padding on base64 string.");if(!krQ.exec(A))throw new TypeError("Invalid base64 string.");let B=yrQ.fromString(A,"base64");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength)};udA.fromBase64=_rQ});
var dpA=E((upA)=>{Object.defineProperty(upA,"__esModule",{value:!0});upA.defaultEndpointResolver=void 0;var O14=K41(),AQ0=S7(),T14=gpA(),P14=new AQ0.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),S14=(A,B={})=>{return P14.get(A,()=>AQ0.resolveEndpoint(T14.ruleSet,{endpointParams:A,logger:B.logger}))};upA.defaultEndpointResolver=S14;AQ0.customEndpointFunctions.aws=O14.awsEndpointFunctions});
var eQ1=E((_Y5,MxA)=>{var{defineProperty:qU1,getOwnPropertyDescriptor:EgQ,getOwnPropertyNames:UgQ}=Object,wgQ=Object.prototype.hasOwnProperty,tN=(A,B)=>qU1(A,"name",{value:B,configurable:!0}),$gQ=(A,B)=>{for(var Q in B)qU1(A,Q,{get:B[Q],enumerable:!0})},qgQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of UgQ(B))if(!wgQ.call(A,Z)&&Z!==Q)qU1(A,Z,{get:()=>B[Z],enumerable:!(D=EgQ(B,Z))||D.enumerable})}return A},NgQ=(A)=>qgQ(qU1({},"__esModule",{value:!0}),A),$xA={};$gQ($xA,{NODE_REGION_CONFIG_FILE_OPTIONS:()=>OgQ,NODE_REGION_CONFIG_OPTIONS:()=>RgQ,REGION_ENV_NAME:()=>qxA,REGION_INI_NAME:()=>NxA,getAwsRegionExtensionConfiguration:()=>LgQ,resolveAwsRegionExtensionConfiguration:()=>MgQ,resolveRegionConfig:()=>TgQ});MxA.exports=NgQ($xA);var LgQ=tN((A)=>{return{setRegion(B){A.region=B},region(){return A.region}}},"getAwsRegionExtensionConfiguration"),MgQ=tN((A)=>{return{region:A.region()}},"resolveAwsRegionExtensionConfiguration"),qxA="AWS_REGION",NxA="region",RgQ={environmentVariableSelector:tN((A)=>A[qxA],"environmentVariableSelector"),configFileSelector:tN((A)=>A[NxA],"configFileSelector"),default:tN(()=>{throw new Error("Region is missing")},"default")},OgQ={preferredFile:"credentials"},LxA=tN((A)=>typeof A==="string"&&(A.startsWith("fips-")||A.endsWith("-fips")),"isFipsRegion"),wxA=tN((A)=>LxA(A)?["fips-aws-global","aws-fips"].includes(A)?"us-east-1":A.replace(/fips-(dkr-|prod-)?|-fips/,""):A,"getRealRegion"),TgQ=tN((A)=>{let{region:B,useFipsEndpoint:Q}=A;if(!B)throw new Error("Region is missing");return Object.assign(A,{region:tN(async()=>{if(typeof B==="string")return wxA(B);let D=await B();return wxA(D)},"region"),useFipsEndpoint:tN(async()=>{let D=typeof B==="string"?B:await B();if(LxA(D))return!0;return typeof Q!=="function"?Promise.resolve(!!Q):Q()},"useFipsEndpoint")})},"resolveRegionConfig")});
var eU1=E((UJ5,wmA)=>{var{defineProperty:tU1,getOwnPropertyDescriptor:baQ,getOwnPropertyNames:faQ}=Object,haQ=Object.prototype.hasOwnProperty,gaQ=(A,B)=>tU1(A,"name",{value:B,configurable:!0}),uaQ=(A,B)=>{for(var Q in B)tU1(A,Q,{get:B[Q],enumerable:!0})},maQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of faQ(B))if(!haQ.call(A,Z)&&Z!==Q)tU1(A,Z,{get:()=>B[Z],enumerable:!(D=baQ(B,Z))||D.enumerable})}return A},daQ=(A)=>maQ(tU1({},"__esModule",{value:!0}),A),VmA={};uaQ(VmA,{ENV_ACCOUNT_ID:()=>UmA,ENV_CREDENTIAL_SCOPE:()=>EmA,ENV_EXPIRATION:()=>zmA,ENV_KEY:()=>CmA,ENV_SECRET:()=>KmA,ENV_SESSION:()=>HmA,fromEnv:()=>paQ});wmA.exports=daQ(VmA);var caQ=Oz(),laQ=Q9(),CmA="AWS_ACCESS_KEY_ID",KmA="AWS_SECRET_ACCESS_KEY",HmA="AWS_SESSION_TOKEN",zmA="AWS_CREDENTIAL_EXPIRATION",EmA="AWS_CREDENTIAL_SCOPE",UmA="AWS_ACCOUNT_ID",paQ=gaQ((A)=>async()=>{A?.logger?.debug("@aws-sdk/credential-provider-env - fromEnv");let B=process.env[CmA],Q=process.env[KmA],D=process.env[HmA],Z=process.env[zmA],G=process.env[EmA],F=process.env[UmA];if(B&&Q){let I={accessKeyId:B,secretAccessKey:Q,...D&&{sessionToken:D},...Z&&{expiration:new Date(Z)},...G&&{credentialScope:G},...F&&{accountId:F}};return caQ.setCredentialFeature(I,"CREDENTIALS_ENV_VARS","g"),I}throw new laQ.CredentialsProviderError("Unable to find environment variable credentials.",{logger:A?.logger})},"fromEnv")});
var gpA=E((fpA)=>{Object.defineProperty(fpA,"__esModule",{value:!0});fpA.ruleSet=void 0;var _pA="required",Pz="fn",Sz="argv",ya="ref",LpA=!0,MpA="isSet",L41="booleanEquals",Sa="error",ja="endpoint",qT="tree",t90="PartitionResult",e90="getAttr",RpA={[_pA]:!1,type:"String"},OpA={[_pA]:!0,default:!1,type:"Boolean"},TpA={[ya]:"Endpoint"},xpA={[Pz]:L41,[Sz]:[{[ya]:"UseFIPS"},!0]},vpA={[Pz]:L41,[Sz]:[{[ya]:"UseDualStack"},!0]},Tz={},PpA={[Pz]:e90,[Sz]:[{[ya]:t90},"supportsFIPS"]},bpA={[ya]:t90},SpA={[Pz]:L41,[Sz]:[!0,{[Pz]:e90,[Sz]:[bpA,"supportsDualStack"]}]},jpA=[xpA],ypA=[vpA],kpA=[{[ya]:"Region"}],R14={version:"1.0",parameters:{Region:RpA,UseDualStack:OpA,UseFIPS:OpA,Endpoint:RpA},rules:[{conditions:[{[Pz]:MpA,[Sz]:[TpA]}],rules:[{conditions:jpA,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:Sa},{conditions:ypA,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:Sa},{endpoint:{url:TpA,properties:Tz,headers:Tz},type:ja}],type:qT},{conditions:[{[Pz]:MpA,[Sz]:kpA}],rules:[{conditions:[{[Pz]:"aws.partition",[Sz]:kpA,assign:t90}],rules:[{conditions:[xpA,vpA],rules:[{conditions:[{[Pz]:L41,[Sz]:[LpA,PpA]},SpA],rules:[{endpoint:{url:"https://portal.sso-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:Tz,headers:Tz},type:ja}],type:qT},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:Sa}],type:qT},{conditions:jpA,rules:[{conditions:[{[Pz]:L41,[Sz]:[PpA,LpA]}],rules:[{conditions:[{[Pz]:"stringEquals",[Sz]:[{[Pz]:e90,[Sz]:[bpA,"name"]},"aws-us-gov"]}],endpoint:{url:"https://portal.sso.{Region}.amazonaws.com",properties:Tz,headers:Tz},type:ja},{endpoint:{url:"https://portal.sso-fips.{Region}.{PartitionResult#dnsSuffix}",properties:Tz,headers:Tz},type:ja}],type:qT},{error:"FIPS is enabled but this partition does not support FIPS",type:Sa}],type:qT},{conditions:ypA,rules:[{conditions:[SpA],rules:[{endpoint:{url:"https://portal.sso.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:Tz,headers:Tz},type:ja}],type:qT},{error:"DualStack is enabled but this partition does not support DualStack",type:Sa}],type:qT},{endpoint:{url:"https://portal.sso.{Region}.{PartitionResult#dnsSuffix}",properties:Tz,headers:Tz},type:ja}],type:qT}],type:qT},{error:"Invalid Configuration: Missing Region",type:Sa}]};fpA.ruleSet=R14});
var haA=E((IL)=>{var KB4=IL&&IL.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),HB4=IL&&IL.__setModuleDefault||(Object.create?function(A,B){Object.defineProperty(A,"default",{enumerable:!0,value:B})}:function(A,B){A.default=B}),zB4=IL&&IL.__importStar||function(){var A=function(B){return A=Object.getOwnPropertyNames||function(Q){var D=[];for(var Z in Q)if(Object.prototype.hasOwnProperty.call(Q,Z))D[D.length]=Z;return D},A(B)};return function(B){if(B&&B.__esModule)return B;var Q={};if(B!=null){for(var D=A(B),Z=0;Z<D.length;Z++)if(D[Z]!=="default")KB4(Q,B,D[Z])}return HB4(Q,B),Q}}();Object.defineProperty(IL,"__esModule",{value:!0});IL.fromTemporaryCredentials=void 0;var EB4=CB(),baA=Q9(),UB4="us-east-1",wB4=(A,B,Q)=>{let D;return async(Z={})=>{let{callerClientConfig:G}=Z,F=A.clientConfig?.profile??G?.profile,I=A.logger??G?.logger;I?.debug("@aws-sdk/credential-providers - fromTemporaryCredentials (STS)");let Y={...A.params,RoleSessionName:A.params.RoleSessionName??"aws-sdk-js-"+Date.now()};if(Y?.SerialNumber){if(!A.mfaCodeProvider)throw new baA.CredentialsProviderError("Temporary credential requires multi-factor authentication, but no MFA code callback was provided.",{tryNextLink:!1,logger:I});Y.TokenCode=await A.mfaCodeProvider(Y?.SerialNumber)}let{AssumeRoleCommand:W,STSClient:J}=await Promise.resolve().then(()=>zB4(vaA()));if(!D){let V=typeof B==="function"?B():void 0,C=[A.masterCredentials,A.clientConfig?.credentials,void G?.credentials,G?.credentialDefaultProvider?.(),V],K="STS client default credentials";if(C[0])K="options.masterCredentials";else if(C[1])K="options.clientConfig.credentials";else if(C[2])throw K="caller client's credentials",new Error("fromTemporaryCredentials recursion in callerClientConfig.credentials");else if(C[3])K="caller client's credentialDefaultProvider";else if(C[4])K="AWS SDK default credentials";let H=[A.clientConfig?.region,G?.region,await Q?.({profile:F}),UB4],z="default partition's default region";if(H[0])z="options.clientConfig.region";else if(H[1])z="caller client's region";else if(H[2])z="file or env region";let $=[faA(A.clientConfig?.requestHandler),faA(G?.requestHandler)],L="STS default requestHandler";if($[0])L="options.clientConfig.requestHandler";else if($[1])L="caller client's requestHandler";I?.debug?.(`@aws-sdk/credential-providers - fromTemporaryCredentials STS client init with ${z}=${await EB4.normalizeProvider(tw1(H))()}, ${K}, ${L}.`),D=new J({...A.clientConfig,credentials:tw1(C),logger:I,profile:F,region:tw1(H),requestHandler:tw1($)})}if(A.clientPlugins)for(let V of A.clientPlugins)D.middlewareStack.use(V);let{Credentials:X}=await D.send(new W(Y));if(!X||!X.AccessKeyId||!X.SecretAccessKey)throw new baA.CredentialsProviderError(`Invalid response from STS.assumeRole call with role ${Y.RoleArn}`,{logger:I});return{accessKeyId:X.AccessKeyId,secretAccessKey:X.SecretAccessKey,sessionToken:X.SessionToken,expiration:X.Expiration,credentialScope:X.CredentialScope}}};IL.fromTemporaryCredentials=wB4;var faA=(A)=>{return A?.metadata?.handlerProtocol==="h2"?void 0:A},tw1=(A)=>{for(let B of A)if(B!==void 0)return B}});
var iA0=E((pA0)=>{Object.defineProperty(pA0,"__esModule",{value:!0});pA0.fromHttp=void 0;var PhQ=j_A();Object.defineProperty(pA0,"fromHttp",{enumerable:!0,get:function(){return PhQ.fromHttp}})});
var iB0=E((jJ5,gdA)=>{var{defineProperty:Uw1,getOwnPropertyDescriptor:CrQ,getOwnPropertyNames:KrQ}=Object,HrQ=Object.prototype.hasOwnProperty,AG=(A,B)=>Uw1(A,"name",{value:B,configurable:!0}),zrQ=(A,B)=>{for(var Q in B)Uw1(A,Q,{get:B[Q],enumerable:!0})},ErQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of KrQ(B))if(!HrQ.call(A,Z)&&Z!==Q)Uw1(A,Z,{get:()=>B[Z],enumerable:!(D=CrQ(B,Z))||D.enumerable})}return A},UrQ=(A)=>ErQ(Uw1({},"__esModule",{value:!0}),A),xdA={};zrQ(xdA,{AWSSDKSigV4Signer:()=>NrQ,AwsSdkSigV4ASigner:()=>MrQ,AwsSdkSigV4Signer:()=>pB0,NODE_AUTH_SCHEME_PREFERENCE_OPTIONS:()=>RrQ,NODE_SIGV4A_CONFIG_OPTIONS:()=>PrQ,getBearerTokenEnvKey:()=>vdA,resolveAWSSDKSigV4Config:()=>jrQ,resolveAwsSdkSigV4AConfig:()=>TrQ,resolveAwsSdkSigV4Config:()=>bdA,validateSigningProperties:()=>lB0});gdA.exports=UrQ(xdA);var wrQ=YK(),$rQ=YK(),TdA=AG((A)=>$rQ.HttpResponse.isInstance(A)?A.headers?.date??A.headers?.Date:void 0,"getDateHeader"),cB0=AG((A)=>new Date(Date.now()+A),"getSkewCorrectedDate"),qrQ=AG((A,B)=>Math.abs(cB0(B).getTime()-A)>=300000,"isClockSkewed"),PdA=AG((A,B)=>{let Q=Date.parse(A);if(qrQ(Q,B))return Q-Date.now();return B},"getUpdatedSystemClockOffset"),H41=AG((A,B)=>{if(!B)throw new Error(`Property \`${A}\` is not resolved for AWS SDK SigV4Auth`);return B},"throwSigningPropertyError"),lB0=AG(async(A)=>{let B=H41("context",A.context),Q=H41("config",A.config),D=B.endpointV2?.properties?.authSchemes?.[0],G=await H41("signer",Q.signer)(D),F=A?.signingRegion,I=A?.signingRegionSet,Y=A?.signingName;return{config:Q,signer:G,signingRegion:F,signingRegionSet:I,signingName:Y}},"validateSigningProperties"),pB0=class{static{AG(this,"AwsSdkSigV4Signer")}async sign(A,B,Q){if(!wrQ.HttpRequest.isInstance(A))throw new Error("The request is not an instance of `HttpRequest` and cannot be signed");let D=await lB0(Q),{config:Z,signer:G}=D,{signingRegion:F,signingName:I}=D,Y=Q.context;if(Y?.authSchemes?.length??0>1){let[J,X]=Y.authSchemes;if(J?.name==="sigv4a"&&X?.name==="sigv4")F=X?.signingRegion??F,I=X?.signingName??I}return await G.sign(A,{signingDate:cB0(Z.systemClockOffset),signingRegion:F,signingService:I})}errorHandler(A){return(B)=>{let Q=B.ServerTime??TdA(B.$response);if(Q){let D=H41("config",A.config),Z=D.systemClockOffset;if(D.systemClockOffset=PdA(Q,D.systemClockOffset),D.systemClockOffset!==Z&&B.$metadata)B.$metadata.clockSkewCorrected=!0}throw B}}successHandler(A,B){let Q=TdA(A);if(Q){let D=H41("config",B.config);D.systemClockOffset=PdA(Q,D.systemClockOffset)}}},NrQ=pB0,LrQ=YK(),MrQ=class extends pB0{static{AG(this,"AwsSdkSigV4ASigner")}async sign(A,B,Q){if(!LrQ.HttpRequest.isInstance(A))throw new Error("The request is not an instance of `HttpRequest` and cannot be signed");let{config:D,signer:Z,signingRegion:G,signingRegionSet:F,signingName:I}=await lB0(Q),W=(await D.sigv4aSigningRegionSet?.()??F??[G]).join(",");return await Z.sign(A,{signingDate:cB0(D.systemClockOffset),signingRegion:W,signingService:I})}},SdA=AG((A)=>typeof A==="string"&&A.length>0?A.split(",").map((B)=>B.trim()):[],"getArrayForCommaSeparatedString"),vdA=AG((A)=>`AWS_BEARER_TOKEN_${A.replace(/[\s-]/g,"_").toUpperCase()}`,"getBearerTokenEnvKey"),jdA="AWS_AUTH_SCHEME_PREFERENCE",ydA="auth_scheme_preference",RrQ={environmentVariableSelector:AG((A,B)=>{if(B?.signingName){if(vdA(B.signingName)in A)return["httpBearerAuth"]}if(!(jdA in A))return;return SdA(A[jdA])},"environmentVariableSelector"),configFileSelector:AG((A)=>{if(!(ydA in A))return;return SdA(A[ydA])},"configFileSelector"),default:[]},OrQ=CB(),kdA=Q9(),TrQ=AG((A)=>{return A.sigv4aSigningRegionSet=OrQ.normalizeProvider(A.sigv4aSigningRegionSet),A},"resolveAwsSdkSigV4AConfig"),PrQ={environmentVariableSelector(A){if(A.AWS_SIGV4A_SIGNING_REGION_SET)return A.AWS_SIGV4A_SIGNING_REGION_SET.split(",").map((B)=>B.trim());throw new kdA.ProviderError("AWS_SIGV4A_SIGNING_REGION_SET not set in env.",{tryNextLink:!0})},configFileSelector(A){if(A.sigv4a_signing_region_set)return(A.sigv4a_signing_region_set??"").split(",").map((B)=>B.trim());throw new kdA.ProviderError("sigv4a_signing_region_set not set in profile.",{tryNextLink:!0})},default:void 0},SrQ=Oz(),oh=CB(),_dA=OdA(),bdA=AG((A)=>{let B=A.credentials,Q=!!A.credentials,D=void 0;Object.defineProperty(A,"credentials",{set(W){if(W&&W!==B&&W!==D)Q=!0;B=W;let J=fdA(A,{credentials:B,credentialDefaultProvider:A.credentialDefaultProvider}),X=hdA(A,J);if(Q&&!X.attributed)D=AG(async(V)=>X(V).then((C)=>SrQ.setCredentialFeature(C,"CREDENTIALS_CODE","e")),"resolvedCredentials"),D.memoized=X.memoized,D.configBound=X.configBound,D.attributed=!0;else D=X},get(){return D},enumerable:!0,configurable:!0}),A.credentials=B;let{signingEscapePath:Z=!0,systemClockOffset:G=A.systemClockOffset||0,sha256:F}=A,I;if(A.signer)I=oh.normalizeProvider(A.signer);else if(A.regionInfoProvider)I=AG(()=>oh.normalizeProvider(A.region)().then(async(W)=>[await A.regionInfoProvider(W,{useFipsEndpoint:await A.useFipsEndpoint(),useDualstackEndpoint:await A.useDualstackEndpoint()})||{},W]).then(([W,J])=>{let{signingRegion:X,signingService:V}=W;A.signingRegion=A.signingRegion||X||J,A.signingName=A.signingName||V||A.serviceId;let C={...A,credentials:A.credentials,region:A.signingRegion,service:A.signingName,sha256:F,uriEscapePath:Z};return new(A.signerConstructor||_dA.SignatureV4)(C)}),"signer");else I=AG(async(W)=>{W=Object.assign({},{name:"sigv4",signingName:A.signingName||A.defaultSigningName,signingRegion:await oh.normalizeProvider(A.region)(),properties:{}},W);let{signingRegion:J,signingName:X}=W;A.signingRegion=A.signingRegion||J,A.signingName=A.signingName||X||A.serviceId;let V={...A,credentials:A.credentials,region:A.signingRegion,service:A.signingName,sha256:F,uriEscapePath:Z};return new(A.signerConstructor||_dA.SignatureV4)(V)},"signer");return Object.assign(A,{systemClockOffset:G,signingEscapePath:Z,signer:I})},"resolveAwsSdkSigV4Config"),jrQ=bdA;function fdA(A,{credentials:B,credentialDefaultProvider:Q}){let D;if(B)if(!B?.memoized)D=oh.memoizeIdentityProvider(B,oh.isIdentityExpired,oh.doesIdentityRequireRefresh);else D=B;else if(Q)D=oh.normalizeProvider(Q(Object.assign({},A,{parentClientConfig:A})));else D=AG(async()=>{throw new Error("@aws-sdk/core::resolveAwsSdkSigV4Config - `credentials` not provided and no credentialDefaultProvider was configured.")},"credentialsProvider");return D.memoized=!0,D}AG(fdA,"normalizeCredentialProvider");function hdA(A,B){if(B.configBound)return B;let Q=AG(async(D)=>B({...D,callerClientConfig:A}),"fn");return Q.memoized=B.memoized,Q.configBound=!0,Q}AG(hdA,"bindCallerConfig")});
var ihA=E((lhA)=>{Object.defineProperty(lhA,"__esModule",{value:!0});lhA.ruleSet=void 0;var uhA="required",lW="fn",pW="argv",Ka="ref",jhA=!0,yhA="isSet",C41="booleanEquals",Ca="error",BL="endpoint",zT="tree",WB0="PartitionResult",JB0="getAttr",X41="stringEquals",khA={[uhA]:!1,type:"String"},_hA={[uhA]:!0,default:!1,type:"Boolean"},xhA={[Ka]:"Endpoint"},mhA={[lW]:C41,[pW]:[{[Ka]:"UseFIPS"},!0]},dhA={[lW]:C41,[pW]:[{[Ka]:"UseDualStack"},!0]},lG={},V41={[Ka]:"Region"},vhA={[lW]:JB0,[pW]:[{[Ka]:WB0},"supportsFIPS"]},chA={[Ka]:WB0},bhA={[lW]:C41,[pW]:[!0,{[lW]:JB0,[pW]:[chA,"supportsDualStack"]}]},fhA=[mhA],hhA=[dhA],ghA=[V41],llQ={version:"1.0",parameters:{Region:khA,UseDualStack:_hA,UseFIPS:_hA,Endpoint:khA},rules:[{conditions:[{[lW]:yhA,[pW]:[xhA]}],rules:[{conditions:fhA,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:Ca},{conditions:hhA,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:Ca},{endpoint:{url:xhA,properties:lG,headers:lG},type:BL}],type:zT},{conditions:[{[lW]:yhA,[pW]:ghA}],rules:[{conditions:[{[lW]:"aws.partition",[pW]:ghA,assign:WB0}],rules:[{conditions:[mhA,dhA],rules:[{conditions:[{[lW]:C41,[pW]:[jhA,vhA]},bhA],rules:[{conditions:[{[lW]:X41,[pW]:[V41,"us-east-1"]}],endpoint:{url:"https://cognito-identity-fips.us-east-1.amazonaws.com",properties:lG,headers:lG},type:BL},{conditions:[{[lW]:X41,[pW]:[V41,"us-east-2"]}],endpoint:{url:"https://cognito-identity-fips.us-east-2.amazonaws.com",properties:lG,headers:lG},type:BL},{conditions:[{[lW]:X41,[pW]:[V41,"us-west-1"]}],endpoint:{url:"https://cognito-identity-fips.us-west-1.amazonaws.com",properties:lG,headers:lG},type:BL},{conditions:[{[lW]:X41,[pW]:[V41,"us-west-2"]}],endpoint:{url:"https://cognito-identity-fips.us-west-2.amazonaws.com",properties:lG,headers:lG},type:BL},{endpoint:{url:"https://cognito-identity-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:lG,headers:lG},type:BL}],type:zT},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:Ca}],type:zT},{conditions:fhA,rules:[{conditions:[{[lW]:C41,[pW]:[vhA,jhA]}],rules:[{endpoint:{url:"https://cognito-identity-fips.{Region}.{PartitionResult#dnsSuffix}",properties:lG,headers:lG},type:BL}],type:zT},{error:"FIPS is enabled but this partition does not support FIPS",type:Ca}],type:zT},{conditions:hhA,rules:[{conditions:[bhA],rules:[{conditions:[{[lW]:X41,[pW]:["aws",{[lW]:JB0,[pW]:[chA,"name"]}]}],endpoint:{url:"https://cognito-identity.{Region}.amazonaws.com",properties:lG,headers:lG},type:BL},{endpoint:{url:"https://cognito-identity.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:lG,headers:lG},type:BL}],type:zT},{error:"DualStack is enabled but this partition does not support DualStack",type:Ca}],type:zT},{endpoint:{url:"https://cognito-identity.{Region}.{PartitionResult#dnsSuffix}",properties:lG,headers:lG},type:BL}],type:zT}],type:zT},{error:"Invalid Configuration: Missing Region",type:Ca}]};lhA.ruleSet=llQ});
var j41=E((pX5,sw1)=>{var{defineProperty:DaA,getOwnPropertyDescriptor:L24,getOwnPropertyNames:M24}=Object,R24=Object.prototype.hasOwnProperty,KQ0=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of M24(B))if(!R24.call(A,Z)&&Z!==Q)DaA(A,Z,{get:()=>B[Z],enumerable:!(D=L24(B,Z))||D.enumerable})}return A},ZaA=(A,B,Q)=>(KQ0(A,B,"default"),Q&&KQ0(Q,B,"default")),O24=(A)=>KQ0(DaA({},"__esModule",{value:!0}),A),HQ0={};sw1.exports=O24(HQ0);ZaA(HQ0,QaA(),sw1.exports);ZaA(HQ0,CQ0(),sw1.exports)});
var j_A=E((P_A)=>{Object.defineProperty(P_A,"__esModule",{value:!0});P_A.fromHttp=void 0;var zhQ=mh(),EhQ=Sw(),UhQ=k3(),O_A=Q9(),whQ=zhQ.__importDefault(J1("fs/promises")),$hQ=$_A(),T_A=N_A(),qhQ=R_A(),NhQ="AWS_CONTAINER_CREDENTIALS_RELATIVE_URI",LhQ="http://*************",MhQ="AWS_CONTAINER_CREDENTIALS_FULL_URI",RhQ="AWS_CONTAINER_AUTHORIZATION_TOKEN_FILE",OhQ="AWS_CONTAINER_AUTHORIZATION_TOKEN",ThQ=(A={})=>{A.logger?.debug("@aws-sdk/credential-provider-http - fromHttp");let B,Q=A.awsContainerCredentialsRelativeUri??process.env[NhQ],D=A.awsContainerCredentialsFullUri??process.env[MhQ],Z=A.awsContainerAuthorizationToken??process.env[OhQ],G=A.awsContainerAuthorizationTokenFile??process.env[RhQ],F=A.logger?.constructor?.name==="NoOpLogger"||!A.logger?console.warn:A.logger.warn;if(Q&&D)F("@aws-sdk/credential-provider-http: you have set both awsContainerCredentialsRelativeUri and awsContainerCredentialsFullUri."),F("awsContainerCredentialsFullUri will take precedence.");if(Z&&G)F("@aws-sdk/credential-provider-http: you have set both awsContainerAuthorizationToken and awsContainerAuthorizationTokenFile."),F("awsContainerAuthorizationToken will take precedence.");if(D)B=D;else if(Q)B=`${LhQ}${Q}`;else throw new O_A.CredentialsProviderError(`No HTTP credential provider host provided.
Set AWS_CONTAINER_CREDENTIALS_FULL_URI or AWS_CONTAINER_CREDENTIALS_RELATIVE_URI.`,{logger:A.logger});let I=new URL(B);$hQ.checkUrl(I,A.logger);let Y=new UhQ.NodeHttpHandler({requestTimeout:A.timeout??1000,connectionTimeout:A.timeout??1000});return qhQ.retryWrapper(async()=>{let W=T_A.createGetRequest(I);if(Z)W.headers.Authorization=Z;else if(G)W.headers.Authorization=(await whQ.default.readFile(G)).toString();try{let J=await Y.handle(W);return T_A.getCredentials(J.response).then((X)=>EhQ.setCredentialFeature(X,"CREDENTIALS_HTTP","z"))}catch(J){throw new O_A.CredentialsProviderError(String(J),{logger:A.logger})}},A.maxRetries??3,A.timeout??1000)};P_A.fromHttp=ThQ});
var jaA=E((PaA)=>{Object.defineProperty(PaA,"__esModule",{value:!0});PaA.fromProcess=void 0;var WB4=_w1(),JB4=(A)=>WB4.fromProcess(A);PaA.fromProcess=JB4});
var kvA=E((jvA)=>{Object.defineProperty(jvA,"__esModule",{value:!0});jvA.getRuntimeConfig=void 0;var PuQ=mh(),SuQ=PuQ.__importDefault(D20()),OvA=VI(),TvA=oQ1(),SU1=K4(),juQ=gG(),PvA=u4(),ih=JD(),SvA=k3(),yuQ=uG(),kuQ=sZ(),_uQ=RvA(),xuQ=V6(),vuQ=mG(),buQ=V6(),fuQ=(A)=>{buQ.emitWarningIfUnsupportedVersion(process.version);let B=vuQ.resolveDefaultsModeConfig(A),Q=()=>B().then(xuQ.loadConfigsForDefaultMode),D=_uQ.getRuntimeConfig(A);OvA.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile,logger:D.logger};return{...D,...A,runtime:"node",defaultsMode:B,authSchemePreference:A?.authSchemePreference??ih.loadConfig(OvA.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS,Z),bodyLengthChecker:A?.bodyLengthChecker??yuQ.calculateBodyLength,defaultUserAgentProvider:A?.defaultUserAgentProvider??TvA.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:SuQ.default.version}),maxAttempts:A?.maxAttempts??ih.loadConfig(PvA.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??ih.loadConfig(SU1.NODE_REGION_CONFIG_OPTIONS,{...SU1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:SvA.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??ih.loadConfig({...PvA.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||kuQ.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??juQ.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??SvA.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??ih.loadConfig(SU1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??ih.loadConfig(SU1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??ih.loadConfig(TvA.NODE_APP_ID_CONFIG_OPTIONS,Z)}};jvA.getRuntimeConfig=fuQ});
var lQ1=E((jI5,SjA)=>{var{defineProperty:lE1,getOwnPropertyDescriptor:HxQ,getOwnPropertyNames:zxQ}=Object,ExQ=Object.prototype.hasOwnProperty,QA0=(A,B)=>lE1(A,"name",{value:B,configurable:!0}),UxQ=(A,B)=>{for(var Q in B)lE1(A,Q,{get:B[Q],enumerable:!0})},wxQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of zxQ(B))if(!ExQ.call(A,Z)&&Z!==Q)lE1(A,Z,{get:()=>B[Z],enumerable:!(D=HxQ(B,Z))||D.enumerable})}return A},$xQ=(A)=>wxQ(lE1({},"__esModule",{value:!0}),A),OjA={};UxQ(OjA,{getLoggerPlugin:()=>qxQ,loggerMiddleware:()=>TjA,loggerMiddlewareOptions:()=>PjA});SjA.exports=$xQ(OjA);var TjA=QA0(()=>(A,B)=>async(Q)=>{try{let D=await A(Q),{clientName:Z,commandName:G,logger:F,dynamoDbDocumentClientOptions:I={}}=B,{overrideInputFilterSensitiveLog:Y,overrideOutputFilterSensitiveLog:W}=I,J=Y??B.inputFilterSensitiveLog,X=W??B.outputFilterSensitiveLog,{$metadata:V,...C}=D.output;return F?.info?.({clientName:Z,commandName:G,input:J(Q.input),output:X(C),metadata:V}),D}catch(D){let{clientName:Z,commandName:G,logger:F,dynamoDbDocumentClientOptions:I={}}=B,{overrideInputFilterSensitiveLog:Y}=I,W=Y??B.inputFilterSensitiveLog;throw F?.error?.({clientName:Z,commandName:G,input:W(Q.input),error:D,metadata:D.$metadata}),D}},"loggerMiddleware"),PjA={name:"loggerMiddleware",tags:["LOGGER"],step:"initialize",override:!0},qxQ=QA0((A)=>({applyToStack:QA0((B)=>{B.add(TjA(),PjA)},"applyToStack")}),"getLoggerPlugin")});
var laA=E((daA)=>{Object.defineProperty(daA,"__esModule",{value:!0});daA.fromTokenFile=void 0;var RB4=j41(),OB4=(A={})=>RB4.fromTokenFile({...A});daA.fromTokenFile=OB4});
var maA=E((gaA)=>{Object.defineProperty(gaA,"__esModule",{value:!0});gaA.fromTemporaryCredentials=void 0;var $B4=K4(),qB4=JD(),NB4=$Q0(),LB4=haA(),MB4=(A)=>{return LB4.fromTemporaryCredentials(A,NB4.fromNodeProviderChain,async({profile:B=process.env.AWS_PROFILE})=>qB4.loadConfig({environmentVariableSelector:(Q)=>Q.AWS_REGION,configFileSelector:(Q)=>{return Q.region},default:()=>{return}},{...$B4.NODE_REGION_CONFIG_FILE_OPTIONS,profile:B})())};gaA.fromTemporaryCredentials=MB4});
var mh=E((_I5,rE1)=>{var ljA,pjA,ijA,njA,ajA,sjA,rjA,ojA,tjA,ejA,AyA,ByA,QyA,aE1,ZA0,DyA,ZyA,GyA,dn,FyA,IyA,YyA,WyA,JyA,XyA,VyA,CyA,KyA,sE1,HyA,zyA,EyA;(function(A){var B=typeof global==="object"?global:typeof self==="object"?self:typeof this==="object"?this:{};if(typeof define==="function"&&define.amd)define("tslib",["exports"],function(D){A(Q(B,Q(D)))});else if(typeof rE1==="object"&&typeof _I5==="object")A(Q(B,Q(_I5)));else A(Q(B));function Q(D,Z){if(D!==B)if(typeof Object.create==="function")Object.defineProperty(D,"__esModule",{value:!0});else D.__esModule=!0;return function(G,F){return D[G]=Z?Z(G,F):F}}})(function(A){var B=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(G,F){G.__proto__=F}||function(G,F){for(var I in F)if(Object.prototype.hasOwnProperty.call(F,I))G[I]=F[I]};ljA=function(G,F){if(typeof F!=="function"&&F!==null)throw new TypeError("Class extends value "+String(F)+" is not a constructor or null");B(G,F);function I(){this.constructor=G}G.prototype=F===null?Object.create(F):(I.prototype=F.prototype,new I)},pjA=Object.assign||function(G){for(var F,I=1,Y=arguments.length;I<Y;I++){F=arguments[I];for(var W in F)if(Object.prototype.hasOwnProperty.call(F,W))G[W]=F[W]}return G},ijA=function(G,F){var I={};for(var Y in G)if(Object.prototype.hasOwnProperty.call(G,Y)&&F.indexOf(Y)<0)I[Y]=G[Y];if(G!=null&&typeof Object.getOwnPropertySymbols==="function"){for(var W=0,Y=Object.getOwnPropertySymbols(G);W<Y.length;W++)if(F.indexOf(Y[W])<0&&Object.prototype.propertyIsEnumerable.call(G,Y[W]))I[Y[W]]=G[Y[W]]}return I},njA=function(G,F,I,Y){var W=arguments.length,J=W<3?F:Y===null?Y=Object.getOwnPropertyDescriptor(F,I):Y,X;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")J=Reflect.decorate(G,F,I,Y);else for(var V=G.length-1;V>=0;V--)if(X=G[V])J=(W<3?X(J):W>3?X(F,I,J):X(F,I))||J;return W>3&&J&&Object.defineProperty(F,I,J),J},ajA=function(G,F){return function(I,Y){F(I,Y,G)}},sjA=function(G,F,I,Y,W,J){function X(T){if(T!==void 0&&typeof T!=="function")throw new TypeError("Function expected");return T}var V=Y.kind,C=V==="getter"?"get":V==="setter"?"set":"value",K=!F&&G?Y.static?G:G.prototype:null,H=F||(K?Object.getOwnPropertyDescriptor(K,Y.name):{}),z,$=!1;for(var L=I.length-1;L>=0;L--){var N={};for(var O in Y)N[O]=O==="access"?{}:Y[O];for(var O in Y.access)N.access[O]=Y.access[O];N.addInitializer=function(T){if($)throw new TypeError("Cannot add initializers after decoration has completed");J.push(X(T||null))};var R=I[L](V==="accessor"?{get:H.get,set:H.set}:H[C],N);if(V==="accessor"){if(R===void 0)continue;if(R===null||typeof R!=="object")throw new TypeError("Object expected");if(z=X(R.get))H.get=z;if(z=X(R.set))H.set=z;if(z=X(R.init))W.unshift(z)}else if(z=X(R))if(V==="field")W.unshift(z);else H[C]=z}if(K)Object.defineProperty(K,Y.name,H);$=!0},rjA=function(G,F,I){var Y=arguments.length>2;for(var W=0;W<F.length;W++)I=Y?F[W].call(G,I):F[W].call(G);return Y?I:void 0},ojA=function(G){return typeof G==="symbol"?G:"".concat(G)},tjA=function(G,F,I){if(typeof F==="symbol")F=F.description?"[".concat(F.description,"]"):"";return Object.defineProperty(G,"name",{configurable:!0,value:I?"".concat(I," ",F):F})},ejA=function(G,F){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(G,F)},AyA=function(G,F,I,Y){function W(J){return J instanceof I?J:new I(function(X){X(J)})}return new(I||(I=Promise))(function(J,X){function V(H){try{K(Y.next(H))}catch(z){X(z)}}function C(H){try{K(Y.throw(H))}catch(z){X(z)}}function K(H){H.done?J(H.value):W(H.value).then(V,C)}K((Y=Y.apply(G,F||[])).next())})},ByA=function(G,F){var I={label:0,sent:function(){if(J[0]&1)throw J[1];return J[1]},trys:[],ops:[]},Y,W,J,X=Object.create((typeof Iterator==="function"?Iterator:Object).prototype);return X.next=V(0),X.throw=V(1),X.return=V(2),typeof Symbol==="function"&&(X[Symbol.iterator]=function(){return this}),X;function V(K){return function(H){return C([K,H])}}function C(K){if(Y)throw new TypeError("Generator is already executing.");while(X&&(X=0,K[0]&&(I=0)),I)try{if(Y=1,W&&(J=K[0]&2?W.return:K[0]?W.throw||((J=W.return)&&J.call(W),0):W.next)&&!(J=J.call(W,K[1])).done)return J;if(W=0,J)K=[K[0]&2,J.value];switch(K[0]){case 0:case 1:J=K;break;case 4:return I.label++,{value:K[1],done:!1};case 5:I.label++,W=K[1],K=[0];continue;case 7:K=I.ops.pop(),I.trys.pop();continue;default:if((J=I.trys,!(J=J.length>0&&J[J.length-1]))&&(K[0]===6||K[0]===2)){I=0;continue}if(K[0]===3&&(!J||K[1]>J[0]&&K[1]<J[3])){I.label=K[1];break}if(K[0]===6&&I.label<J[1]){I.label=J[1],J=K;break}if(J&&I.label<J[2]){I.label=J[2],I.ops.push(K);break}if(J[2])I.ops.pop();I.trys.pop();continue}K=F.call(G,I)}catch(H){K=[6,H],W=0}finally{Y=J=0}if(K[0]&5)throw K[1];return{value:K[0]?K[1]:void 0,done:!0}}},QyA=function(G,F){for(var I in G)if(I!=="default"&&!Object.prototype.hasOwnProperty.call(F,I))sE1(F,G,I)},sE1=Object.create?function(G,F,I,Y){if(Y===void 0)Y=I;var W=Object.getOwnPropertyDescriptor(F,I);if(!W||("get"in W?!F.__esModule:W.writable||W.configurable))W={enumerable:!0,get:function(){return F[I]}};Object.defineProperty(G,Y,W)}:function(G,F,I,Y){if(Y===void 0)Y=I;G[Y]=F[I]},aE1=function(G){var F=typeof Symbol==="function"&&Symbol.iterator,I=F&&G[F],Y=0;if(I)return I.call(G);if(G&&typeof G.length==="number")return{next:function(){if(G&&Y>=G.length)G=void 0;return{value:G&&G[Y++],done:!G}}};throw new TypeError(F?"Object is not iterable.":"Symbol.iterator is not defined.")},ZA0=function(G,F){var I=typeof Symbol==="function"&&G[Symbol.iterator];if(!I)return G;var Y=I.call(G),W,J=[],X;try{while((F===void 0||F-- >0)&&!(W=Y.next()).done)J.push(W.value)}catch(V){X={error:V}}finally{try{if(W&&!W.done&&(I=Y.return))I.call(Y)}finally{if(X)throw X.error}}return J},DyA=function(){for(var G=[],F=0;F<arguments.length;F++)G=G.concat(ZA0(arguments[F]));return G},ZyA=function(){for(var G=0,F=0,I=arguments.length;F<I;F++)G+=arguments[F].length;for(var Y=Array(G),W=0,F=0;F<I;F++)for(var J=arguments[F],X=0,V=J.length;X<V;X++,W++)Y[W]=J[X];return Y},GyA=function(G,F,I){if(I||arguments.length===2){for(var Y=0,W=F.length,J;Y<W;Y++)if(J||!(Y in F)){if(!J)J=Array.prototype.slice.call(F,0,Y);J[Y]=F[Y]}}return G.concat(J||Array.prototype.slice.call(F))},dn=function(G){return this instanceof dn?(this.v=G,this):new dn(G)},FyA=function(G,F,I){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var Y=I.apply(G,F||[]),W,J=[];return W=Object.create((typeof AsyncIterator==="function"?AsyncIterator:Object).prototype),V("next"),V("throw"),V("return",X),W[Symbol.asyncIterator]=function(){return this},W;function X(L){return function(N){return Promise.resolve(N).then(L,z)}}function V(L,N){if(Y[L]){if(W[L]=function(O){return new Promise(function(R,T){J.push([L,O,R,T])>1||C(L,O)})},N)W[L]=N(W[L])}}function C(L,N){try{K(Y[L](N))}catch(O){$(J[0][3],O)}}function K(L){L.value instanceof dn?Promise.resolve(L.value.v).then(H,z):$(J[0][2],L)}function H(L){C("next",L)}function z(L){C("throw",L)}function $(L,N){if(L(N),J.shift(),J.length)C(J[0][0],J[0][1])}},IyA=function(G){var F,I;return F={},Y("next"),Y("throw",function(W){throw W}),Y("return"),F[Symbol.iterator]=function(){return this},F;function Y(W,J){F[W]=G[W]?function(X){return(I=!I)?{value:dn(G[W](X)),done:!1}:J?J(X):X}:J}},YyA=function(G){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var F=G[Symbol.asyncIterator],I;return F?F.call(G):(G=typeof aE1==="function"?aE1(G):G[Symbol.iterator](),I={},Y("next"),Y("throw"),Y("return"),I[Symbol.asyncIterator]=function(){return this},I);function Y(J){I[J]=G[J]&&function(X){return new Promise(function(V,C){X=G[J](X),W(V,C,X.done,X.value)})}}function W(J,X,V,C){Promise.resolve(C).then(function(K){J({value:K,done:V})},X)}},WyA=function(G,F){if(Object.defineProperty)Object.defineProperty(G,"raw",{value:F});else G.raw=F;return G};var Q=Object.create?function(G,F){Object.defineProperty(G,"default",{enumerable:!0,value:F})}:function(G,F){G.default=F},D=function(G){return D=Object.getOwnPropertyNames||function(F){var I=[];for(var Y in F)if(Object.prototype.hasOwnProperty.call(F,Y))I[I.length]=Y;return I},D(G)};JyA=function(G){if(G&&G.__esModule)return G;var F={};if(G!=null){for(var I=D(G),Y=0;Y<I.length;Y++)if(I[Y]!=="default")sE1(F,G,I[Y])}return Q(F,G),F},XyA=function(G){return G&&G.__esModule?G:{default:G}},VyA=function(G,F,I,Y){if(I==="a"&&!Y)throw new TypeError("Private accessor was defined without a getter");if(typeof F==="function"?G!==F||!Y:!F.has(G))throw new TypeError("Cannot read private member from an object whose class did not declare it");return I==="m"?Y:I==="a"?Y.call(G):Y?Y.value:F.get(G)},CyA=function(G,F,I,Y,W){if(Y==="m")throw new TypeError("Private method is not writable");if(Y==="a"&&!W)throw new TypeError("Private accessor was defined without a setter");if(typeof F==="function"?G!==F||!W:!F.has(G))throw new TypeError("Cannot write private member to an object whose class did not declare it");return Y==="a"?W.call(G,I):W?W.value=I:F.set(G,I),I},KyA=function(G,F){if(F===null||typeof F!=="object"&&typeof F!=="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof G==="function"?F===G:G.has(F)},HyA=function(G,F,I){if(F!==null&&F!==void 0){if(typeof F!=="object"&&typeof F!=="function")throw new TypeError("Object expected.");var Y,W;if(I){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");Y=F[Symbol.asyncDispose]}if(Y===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");if(Y=F[Symbol.dispose],I)W=Y}if(typeof Y!=="function")throw new TypeError("Object not disposable.");if(W)Y=function(){try{W.call(this)}catch(J){return Promise.reject(J)}};G.stack.push({value:F,dispose:Y,async:I})}else if(I)G.stack.push({async:!0});return F};var Z=typeof SuppressedError==="function"?SuppressedError:function(G,F,I){var Y=new Error(I);return Y.name="SuppressedError",Y.error=G,Y.suppressed=F,Y};zyA=function(G){function F(J){G.error=G.hasError?new Z(J,G.error,"An error was suppressed during disposal."):J,G.hasError=!0}var I,Y=0;function W(){while(I=G.stack.pop())try{if(!I.async&&Y===1)return Y=0,G.stack.push(I),Promise.resolve().then(W);if(I.dispose){var J=I.dispose.call(I.value);if(I.async)return Y|=2,Promise.resolve(J).then(W,function(X){return F(X),W()})}else Y|=1}catch(X){F(X)}if(Y===1)return G.hasError?Promise.reject(G.error):Promise.resolve();if(G.hasError)throw G.error}return W()},EyA=function(G,F){if(typeof G==="string"&&/^\.\.?\//.test(G))return G.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,function(I,Y,W,J,X){return Y?F?".jsx":".js":W&&(!J||!X)?I:W+J+"."+X.toLowerCase()+"js"});return G},A("__extends",ljA),A("__assign",pjA),A("__rest",ijA),A("__decorate",njA),A("__param",ajA),A("__esDecorate",sjA),A("__runInitializers",rjA),A("__propKey",ojA),A("__setFunctionName",tjA),A("__metadata",ejA),A("__awaiter",AyA),A("__generator",ByA),A("__exportStar",QyA),A("__createBinding",sE1),A("__values",aE1),A("__read",ZA0),A("__spread",DyA),A("__spreadArrays",ZyA),A("__spreadArray",GyA),A("__await",dn),A("__asyncGenerator",FyA),A("__asyncDelegator",IyA),A("__asyncValues",YyA),A("__makeTemplateObject",WyA),A("__importStar",JyA),A("__importDefault",XyA),A("__classPrivateFieldGet",VyA),A("__classPrivateFieldSet",CyA),A("__classPrivateFieldIn",KyA),A("__addDisposableResource",HyA),A("__disposeResources",zyA),A("__rewriteRelativeImportExtension",EyA)})});
var mn=E((kI5,cjA)=>{var{defineProperty:nE1,getOwnPropertyDescriptor:kxQ,getOwnPropertyNames:_xQ}=Object,xxQ=Object.prototype.hasOwnProperty,un=(A,B)=>nE1(A,"name",{value:B,configurable:!0}),vxQ=(A,B)=>{for(var Q in B)nE1(A,Q,{get:B[Q],enumerable:!0})},bxQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of _xQ(B))if(!xxQ.call(A,Z)&&Z!==Q)nE1(A,Z,{get:()=>B[Z],enumerable:!(D=kxQ(B,Z))||D.enumerable})}return A},fxQ=(A)=>bxQ(nE1({},"__esModule",{value:!0}),A),vjA={};vxQ(vjA,{ConditionObject:()=>A7.ConditionObject,DeprecatedObject:()=>A7.DeprecatedObject,EndpointError:()=>A7.EndpointError,EndpointObject:()=>A7.EndpointObject,EndpointObjectHeaders:()=>A7.EndpointObjectHeaders,EndpointObjectProperties:()=>A7.EndpointObjectProperties,EndpointParams:()=>A7.EndpointParams,EndpointResolverOptions:()=>A7.EndpointResolverOptions,EndpointRuleObject:()=>A7.EndpointRuleObject,ErrorRuleObject:()=>A7.ErrorRuleObject,EvaluateOptions:()=>A7.EvaluateOptions,Expression:()=>A7.Expression,FunctionArgv:()=>A7.FunctionArgv,FunctionObject:()=>A7.FunctionObject,FunctionReturn:()=>A7.FunctionReturn,ParameterObject:()=>A7.ParameterObject,ReferenceObject:()=>A7.ReferenceObject,ReferenceRecord:()=>A7.ReferenceRecord,RuleSetObject:()=>A7.RuleSetObject,RuleSetRules:()=>A7.RuleSetRules,TreeRuleObject:()=>A7.TreeRuleObject,awsEndpointFunctions:()=>djA,getUserAgentPrefix:()=>mxQ,isIpAddress:()=>A7.isIpAddress,partition:()=>ujA,resolveEndpoint:()=>A7.resolveEndpoint,setPartitionInfo:()=>mjA,useDefaultPartitionInfo:()=>uxQ});cjA.exports=fxQ(vjA);var A7=S7(),bjA=un((A,B=!1)=>{if(B){for(let Q of A.split("."))if(!bjA(Q))return!1;return!0}if(!A7.isValidHostLabel(A))return!1;if(A.length<3||A.length>63)return!1;if(A!==A.toLowerCase())return!1;if(A7.isIpAddress(A))return!1;return!0},"isVirtualHostableS3Bucket"),xjA=":",hxQ="/",gxQ=un((A)=>{let B=A.split(xjA);if(B.length<6)return null;let[Q,D,Z,G,F,...I]=B;if(Q!=="arn"||D===""||Z===""||I.join(xjA)==="")return null;let Y=I.map((W)=>W.split(hxQ)).flat();return{partition:D,service:Z,region:G,accountId:F,resourceId:Y}},"parseArn"),fjA={partitions:[{id:"aws",outputs:{dnsSuffix:"amazonaws.com",dualStackDnsSuffix:"api.aws",implicitGlobalRegion:"us-east-1",name:"aws",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^(us|eu|ap|sa|ca|me|af|il|mx)\\-\\w+\\-\\d+$",regions:{"af-south-1":{description:"Africa (Cape Town)"},"ap-east-1":{description:"Asia Pacific (Hong Kong)"},"ap-east-2":{description:"Asia Pacific (Taipei)"},"ap-northeast-1":{description:"Asia Pacific (Tokyo)"},"ap-northeast-2":{description:"Asia Pacific (Seoul)"},"ap-northeast-3":{description:"Asia Pacific (Osaka)"},"ap-south-1":{description:"Asia Pacific (Mumbai)"},"ap-south-2":{description:"Asia Pacific (Hyderabad)"},"ap-southeast-1":{description:"Asia Pacific (Singapore)"},"ap-southeast-2":{description:"Asia Pacific (Sydney)"},"ap-southeast-3":{description:"Asia Pacific (Jakarta)"},"ap-southeast-4":{description:"Asia Pacific (Melbourne)"},"ap-southeast-5":{description:"Asia Pacific (Malaysia)"},"ap-southeast-7":{description:"Asia Pacific (Thailand)"},"aws-global":{description:"AWS Standard global region"},"ca-central-1":{description:"Canada (Central)"},"ca-west-1":{description:"Canada West (Calgary)"},"eu-central-1":{description:"Europe (Frankfurt)"},"eu-central-2":{description:"Europe (Zurich)"},"eu-north-1":{description:"Europe (Stockholm)"},"eu-south-1":{description:"Europe (Milan)"},"eu-south-2":{description:"Europe (Spain)"},"eu-west-1":{description:"Europe (Ireland)"},"eu-west-2":{description:"Europe (London)"},"eu-west-3":{description:"Europe (Paris)"},"il-central-1":{description:"Israel (Tel Aviv)"},"me-central-1":{description:"Middle East (UAE)"},"me-south-1":{description:"Middle East (Bahrain)"},"mx-central-1":{description:"Mexico (Central)"},"sa-east-1":{description:"South America (Sao Paulo)"},"us-east-1":{description:"US East (N. Virginia)"},"us-east-2":{description:"US East (Ohio)"},"us-west-1":{description:"US West (N. California)"},"us-west-2":{description:"US West (Oregon)"}}},{id:"aws-cn",outputs:{dnsSuffix:"amazonaws.com.cn",dualStackDnsSuffix:"api.amazonwebservices.com.cn",implicitGlobalRegion:"cn-northwest-1",name:"aws-cn",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^cn\\-\\w+\\-\\d+$",regions:{"aws-cn-global":{description:"AWS China global region"},"cn-north-1":{description:"China (Beijing)"},"cn-northwest-1":{description:"China (Ningxia)"}}},{id:"aws-us-gov",outputs:{dnsSuffix:"amazonaws.com",dualStackDnsSuffix:"api.aws",implicitGlobalRegion:"us-gov-west-1",name:"aws-us-gov",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^us\\-gov\\-\\w+\\-\\d+$",regions:{"aws-us-gov-global":{description:"AWS GovCloud (US) global region"},"us-gov-east-1":{description:"AWS GovCloud (US-East)"},"us-gov-west-1":{description:"AWS GovCloud (US-West)"}}},{id:"aws-iso",outputs:{dnsSuffix:"c2s.ic.gov",dualStackDnsSuffix:"c2s.ic.gov",implicitGlobalRegion:"us-iso-east-1",name:"aws-iso",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-iso\\-\\w+\\-\\d+$",regions:{"aws-iso-global":{description:"AWS ISO (US) global region"},"us-iso-east-1":{description:"US ISO East"},"us-iso-west-1":{description:"US ISO WEST"}}},{id:"aws-iso-b",outputs:{dnsSuffix:"sc2s.sgov.gov",dualStackDnsSuffix:"sc2s.sgov.gov",implicitGlobalRegion:"us-isob-east-1",name:"aws-iso-b",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-isob\\-\\w+\\-\\d+$",regions:{"aws-iso-b-global":{description:"AWS ISOB (US) global region"},"us-isob-east-1":{description:"US ISOB East (Ohio)"}}},{id:"aws-iso-e",outputs:{dnsSuffix:"cloud.adc-e.uk",dualStackDnsSuffix:"cloud.adc-e.uk",implicitGlobalRegion:"eu-isoe-west-1",name:"aws-iso-e",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^eu\\-isoe\\-\\w+\\-\\d+$",regions:{"aws-iso-e-global":{description:"AWS ISOE (Europe) global region"},"eu-isoe-west-1":{description:"EU ISOE West"}}},{id:"aws-iso-f",outputs:{dnsSuffix:"csp.hci.ic.gov",dualStackDnsSuffix:"csp.hci.ic.gov",implicitGlobalRegion:"us-isof-south-1",name:"aws-iso-f",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-isof\\-\\w+\\-\\d+$",regions:{"aws-iso-f-global":{description:"AWS ISOF global region"},"us-isof-east-1":{description:"US ISOF EAST"},"us-isof-south-1":{description:"US ISOF SOUTH"}}},{id:"aws-eusc",outputs:{dnsSuffix:"amazonaws.eu",dualStackDnsSuffix:"amazonaws.eu",implicitGlobalRegion:"eusc-de-east-1",name:"aws-eusc",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^eusc\\-(de)\\-\\w+\\-\\d+$",regions:{"eusc-de-east-1":{description:"EU (Germany)"}}}],version:"1.1"},hjA=fjA,gjA="",ujA=un((A)=>{let{partitions:B}=hjA;for(let D of B){let{regions:Z,outputs:G}=D;for(let[F,I]of Object.entries(Z))if(F===A)return{...G,...I}}for(let D of B){let{regionRegex:Z,outputs:G}=D;if(new RegExp(Z).test(A))return{...G}}let Q=B.find((D)=>D.id==="aws");if(!Q)throw new Error("Provided region was not found in the partition array or regex, and default partition with id 'aws' doesn't exist.");return{...Q.outputs}},"partition"),mjA=un((A,B="")=>{hjA=A,gjA=B},"setPartitionInfo"),uxQ=un(()=>{mjA(fjA,"")},"useDefaultPartitionInfo"),mxQ=un(()=>gjA,"getUserAgentPrefix"),djA={isVirtualHostableS3Bucket:bjA,parseArn:gxQ,partition:ujA};A7.customEndpointFunctions.aws=djA});
var naA=E((paA)=>{Object.defineProperty(paA,"__esModule",{value:!0});paA.fromWebToken=void 0;var TB4=j41(),PB4=(A)=>TB4.fromWebToken({...A});paA.fromWebToken=PB4});
var niA=E((piA)=>{Object.defineProperty(piA,"__esModule",{value:!0});piA.ruleSet=void 0;var miA="required",yz="fn",kz="argv",va="ref",yiA=!0,kiA="isSet",O41="booleanEquals",_a="error",xa="endpoint",LT="tree",GQ0="PartitionResult",FQ0="getAttr",_iA={[miA]:!1,type:"String"},xiA={[miA]:!0,default:!1,type:"Boolean"},viA={[va]:"Endpoint"},diA={[yz]:O41,[kz]:[{[va]:"UseFIPS"},!0]},ciA={[yz]:O41,[kz]:[{[va]:"UseDualStack"},!0]},jz={},biA={[yz]:FQ0,[kz]:[{[va]:GQ0},"supportsFIPS"]},liA={[va]:GQ0},fiA={[yz]:O41,[kz]:[!0,{[yz]:FQ0,[kz]:[liA,"supportsDualStack"]}]},hiA=[diA],giA=[ciA],uiA=[{[va]:"Region"}],x04={version:"1.0",parameters:{Region:_iA,UseDualStack:xiA,UseFIPS:xiA,Endpoint:_iA},rules:[{conditions:[{[yz]:kiA,[kz]:[viA]}],rules:[{conditions:hiA,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:_a},{conditions:giA,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:_a},{endpoint:{url:viA,properties:jz,headers:jz},type:xa}],type:LT},{conditions:[{[yz]:kiA,[kz]:uiA}],rules:[{conditions:[{[yz]:"aws.partition",[kz]:uiA,assign:GQ0}],rules:[{conditions:[diA,ciA],rules:[{conditions:[{[yz]:O41,[kz]:[yiA,biA]},fiA],rules:[{endpoint:{url:"https://oidc-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:jz,headers:jz},type:xa}],type:LT},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:_a}],type:LT},{conditions:hiA,rules:[{conditions:[{[yz]:O41,[kz]:[biA,yiA]}],rules:[{conditions:[{[yz]:"stringEquals",[kz]:[{[yz]:FQ0,[kz]:[liA,"name"]},"aws-us-gov"]}],endpoint:{url:"https://oidc.{Region}.amazonaws.com",properties:jz,headers:jz},type:xa},{endpoint:{url:"https://oidc-fips.{Region}.{PartitionResult#dnsSuffix}",properties:jz,headers:jz},type:xa}],type:LT},{error:"FIPS is enabled but this partition does not support FIPS",type:_a}],type:LT},{conditions:giA,rules:[{conditions:[fiA],rules:[{endpoint:{url:"https://oidc.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:jz,headers:jz},type:xa}],type:LT},{error:"DualStack is enabled but this partition does not support DualStack",type:_a}],type:LT},{endpoint:{url:"https://oidc.{Region}.{PartitionResult#dnsSuffix}",properties:jz,headers:jz},type:xa}],type:LT}],type:LT},{error:"Invalid Configuration: Missing Region",type:_a}]};piA.ruleSet=x04});
var npA=E((ppA)=>{Object.defineProperty(ppA,"__esModule",{value:!0});ppA.getRuntimeConfig=void 0;var j14=UV(),y14=CB(),k14=w8(),_14=JZ(),cpA=th(),lpA=cB(),x14=o90(),v14=dpA(),b14=(A)=>{return{apiVersion:"2019-06-10",base64Decoder:A?.base64Decoder??cpA.fromBase64,base64Encoder:A?.base64Encoder??cpA.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??v14.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??x14.defaultSSOHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new j14.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new y14.NoAuthSigner}],logger:A?.logger??new k14.NoOpLogger,serviceId:A?.serviceId??"SSO",urlParser:A?.urlParser??_14.parseUrl,utf8Decoder:A?.utf8Decoder??lpA.fromUtf8,utf8Encoder:A?.utf8Encoder??lpA.toUtf8}};ppA.getRuntimeConfig=b14});
var o90=E(($pA)=>{Object.defineProperty($pA,"__esModule",{value:!0});$pA.resolveHttpAuthSchemeConfig=$pA.defaultSSOHttpAuthSchemeProvider=$pA.defaultSSOHttpAuthSchemeParametersProvider=void 0;var E14=UV(),r90=J5(),U14=async(A,B,Q)=>{return{operation:r90.getSmithyContext(B).operation,region:await r90.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};$pA.defaultSSOHttpAuthSchemeParametersProvider=U14;function w14(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"awsssoportal",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function xw1(A){return{schemeId:"smithy.api#noAuth"}}var $14=(A)=>{let B=[];switch(A.operation){case"GetRoleCredentials":{B.push(xw1(A));break}case"ListAccountRoles":{B.push(xw1(A));break}case"ListAccounts":{B.push(xw1(A));break}case"Logout":{B.push(xw1(A));break}default:B.push(w14(A))}return B};$pA.defaultSSOHttpAuthSchemeProvider=$14;var q14=(A)=>{let B=E14.resolveAwsSdkSigV4Config(A);return Object.assign(B,{authSchemePreference:r90.normalizeProvider(A.authSchemePreference??[])})};$pA.resolveHttpAuthSchemeConfig=q14});
var oQ1=E((PY5,u_A)=>{var{defineProperty:wU1,getOwnPropertyDescriptor:hhQ,getOwnPropertyNames:ghQ}=Object,uhQ=Object.prototype.hasOwnProperty,UU1=(A,B)=>wU1(A,"name",{value:B,configurable:!0}),mhQ=(A,B)=>{for(var Q in B)wU1(A,Q,{get:B[Q],enumerable:!0})},dhQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of ghQ(B))if(!uhQ.call(A,Z)&&Z!==Q)wU1(A,Z,{get:()=>B[Z],enumerable:!(D=hhQ(B,Z))||D.enumerable})}return A},chQ=(A)=>dhQ(wU1({},"__esModule",{value:!0}),A),v_A={};mhQ(v_A,{NODE_APP_ID_CONFIG_OPTIONS:()=>ahQ,UA_APP_ID_ENV_NAME:()=>h_A,UA_APP_ID_INI_NAME:()=>g_A,createDefaultUserAgentProvider:()=>f_A,crtAvailability:()=>b_A,defaultUserAgent:()=>phQ});u_A.exports=chQ(v_A);var x_A=J1("os"),sA0=J1("process"),b_A={isCrtAvailable:!1},lhQ=UU1(()=>{if(b_A.isCrtAvailable)return["md/crt-avail"];return null},"isCrtAvailable"),f_A=UU1(({serviceId:A,clientVersion:B})=>{return async(Q)=>{let D=[["aws-sdk-js",B],["ua","2.1"],[`os/${x_A.platform()}`,x_A.release()],["lang/js"],["md/nodejs",`${sA0.versions.node}`]],Z=lhQ();if(Z)D.push(Z);if(A)D.push([`api/${A}`,B]);if(sA0.env.AWS_EXECUTION_ENV)D.push([`exec-env/${sA0.env.AWS_EXECUTION_ENV}`]);let G=await Q?.userAgentAppId?.();return G?[...D,[`app/${G}`]]:[...D]}},"createDefaultUserAgentProvider"),phQ=f_A,ihQ=an(),h_A="AWS_SDK_UA_APP_ID",g_A="sdk_ua_app_id",nhQ="sdk-ua-app-id",ahQ={environmentVariableSelector:UU1((A)=>A[h_A],"environmentVariableSelector"),configFileSelector:UU1((A)=>A[g_A]??A[nhQ],"configFileSelector"),default:ihQ.DEFAULT_UA_APP_ID}});
var oU1=E((jB0)=>{Object.defineProperty(jB0,"__esModule",{value:!0});jB0.fromHttp=void 0;var xaQ=XmA();Object.defineProperty(jB0,"fromHttp",{enumerable:!0,get:function(){return xaQ.fromHttp}})});
var obA=E((sbA)=>{Object.defineProperty(sbA,"__esModule",{value:!0});sbA.ruleSet=void 0;var gbA="required",E4="type",d8="fn",c8="argv",wk="ref",PbA=!1,H20=!0,Uk="booleanEquals",bY="stringEquals",ubA="sigv4",mbA="sts",dbA="us-east-1",KD="endpoint",SbA="https://sts.{Region}.{PartitionResult#dnsSuffix}",eN="tree",Da="error",E20="getAttr",jbA={[gbA]:!1,[E4]:"String"},z20={[gbA]:!0,default:!1,[E4]:"Boolean"},cbA={[wk]:"Endpoint"},ybA={[d8]:"isSet",[c8]:[{[wk]:"Region"}]},fY={[wk]:"Region"},kbA={[d8]:"aws.partition",[c8]:[fY],assign:"PartitionResult"},lbA={[wk]:"UseFIPS"},pbA={[wk]:"UseDualStack"},cW={url:"https://sts.amazonaws.com",properties:{authSchemes:[{name:ubA,signingName:mbA,signingRegion:dbA}]},headers:{}},GK={},_bA={conditions:[{[d8]:bY,[c8]:[fY,"aws-global"]}],[KD]:cW,[E4]:KD},ibA={[d8]:Uk,[c8]:[lbA,!0]},nbA={[d8]:Uk,[c8]:[pbA,!0]},xbA={[d8]:E20,[c8]:[{[wk]:"PartitionResult"},"supportsFIPS"]},abA={[wk]:"PartitionResult"},vbA={[d8]:Uk,[c8]:[!0,{[d8]:E20,[c8]:[abA,"supportsDualStack"]}]},bbA=[{[d8]:"isSet",[c8]:[cbA]}],fbA=[ibA],hbA=[nbA],FdQ={version:"1.0",parameters:{Region:jbA,UseDualStack:z20,UseFIPS:z20,Endpoint:jbA,UseGlobalEndpoint:z20},rules:[{conditions:[{[d8]:Uk,[c8]:[{[wk]:"UseGlobalEndpoint"},H20]},{[d8]:"not",[c8]:bbA},ybA,kbA,{[d8]:Uk,[c8]:[lbA,PbA]},{[d8]:Uk,[c8]:[pbA,PbA]}],rules:[{conditions:[{[d8]:bY,[c8]:[fY,"ap-northeast-1"]}],endpoint:cW,[E4]:KD},{conditions:[{[d8]:bY,[c8]:[fY,"ap-south-1"]}],endpoint:cW,[E4]:KD},{conditions:[{[d8]:bY,[c8]:[fY,"ap-southeast-1"]}],endpoint:cW,[E4]:KD},{conditions:[{[d8]:bY,[c8]:[fY,"ap-southeast-2"]}],endpoint:cW,[E4]:KD},_bA,{conditions:[{[d8]:bY,[c8]:[fY,"ca-central-1"]}],endpoint:cW,[E4]:KD},{conditions:[{[d8]:bY,[c8]:[fY,"eu-central-1"]}],endpoint:cW,[E4]:KD},{conditions:[{[d8]:bY,[c8]:[fY,"eu-north-1"]}],endpoint:cW,[E4]:KD},{conditions:[{[d8]:bY,[c8]:[fY,"eu-west-1"]}],endpoint:cW,[E4]:KD},{conditions:[{[d8]:bY,[c8]:[fY,"eu-west-2"]}],endpoint:cW,[E4]:KD},{conditions:[{[d8]:bY,[c8]:[fY,"eu-west-3"]}],endpoint:cW,[E4]:KD},{conditions:[{[d8]:bY,[c8]:[fY,"sa-east-1"]}],endpoint:cW,[E4]:KD},{conditions:[{[d8]:bY,[c8]:[fY,dbA]}],endpoint:cW,[E4]:KD},{conditions:[{[d8]:bY,[c8]:[fY,"us-east-2"]}],endpoint:cW,[E4]:KD},{conditions:[{[d8]:bY,[c8]:[fY,"us-west-1"]}],endpoint:cW,[E4]:KD},{conditions:[{[d8]:bY,[c8]:[fY,"us-west-2"]}],endpoint:cW,[E4]:KD},{endpoint:{url:SbA,properties:{authSchemes:[{name:ubA,signingName:mbA,signingRegion:"{Region}"}]},headers:GK},[E4]:KD}],[E4]:eN},{conditions:bbA,rules:[{conditions:fbA,error:"Invalid Configuration: FIPS and custom endpoint are not supported",[E4]:Da},{conditions:hbA,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",[E4]:Da},{endpoint:{url:cbA,properties:GK,headers:GK},[E4]:KD}],[E4]:eN},{conditions:[ybA],rules:[{conditions:[kbA],rules:[{conditions:[ibA,nbA],rules:[{conditions:[{[d8]:Uk,[c8]:[H20,xbA]},vbA],rules:[{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:GK,headers:GK},[E4]:KD}],[E4]:eN},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",[E4]:Da}],[E4]:eN},{conditions:fbA,rules:[{conditions:[{[d8]:Uk,[c8]:[xbA,H20]}],rules:[{conditions:[{[d8]:bY,[c8]:[{[d8]:E20,[c8]:[abA,"name"]},"aws-us-gov"]}],endpoint:{url:"https://sts.{Region}.amazonaws.com",properties:GK,headers:GK},[E4]:KD},{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dnsSuffix}",properties:GK,headers:GK},[E4]:KD}],[E4]:eN},{error:"FIPS is enabled but this partition does not support FIPS",[E4]:Da}],[E4]:eN},{conditions:hbA,rules:[{conditions:[vbA],rules:[{endpoint:{url:"https://sts.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:GK,headers:GK},[E4]:KD}],[E4]:eN},{error:"DualStack is enabled but this partition does not support DualStack",[E4]:Da}],[E4]:eN},_bA,{endpoint:{url:SbA,properties:GK,headers:GK},[E4]:KD}],[E4]:eN}],[E4]:eN},{error:"Invalid Configuration: Missing Region",[E4]:Da}]};sbA.ruleSet=FdQ});
var pQ1=E((yI5,_jA)=>{var{defineProperty:iE1,getOwnPropertyDescriptor:NxQ,getOwnPropertyNames:LxQ}=Object,MxQ=Object.prototype.hasOwnProperty,pE1=(A,B)=>iE1(A,"name",{value:B,configurable:!0}),RxQ=(A,B)=>{for(var Q in B)iE1(A,Q,{get:B[Q],enumerable:!0})},OxQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of LxQ(B))if(!MxQ.call(A,Z)&&Z!==Q)iE1(A,Z,{get:()=>B[Z],enumerable:!(D=NxQ(B,Z))||D.enumerable})}return A},TxQ=(A)=>OxQ(iE1({},"__esModule",{value:!0}),A),jjA={};RxQ(jjA,{addRecursionDetectionMiddlewareOptions:()=>kjA,getRecursionDetectionPlugin:()=>yxQ,recursionDetectionMiddleware:()=>yjA});_jA.exports=TxQ(jjA);var PxQ=EV(),DA0="X-Amzn-Trace-Id",SxQ="AWS_LAMBDA_FUNCTION_NAME",jxQ="_X_AMZN_TRACE_ID",yjA=pE1((A)=>(B)=>async(Q)=>{let{request:D}=Q;if(!PxQ.HttpRequest.isInstance(D)||A.runtime!=="node")return B(Q);let Z=Object.keys(D.headers??{}).find((Y)=>Y.toLowerCase()===DA0.toLowerCase())??DA0;if(D.headers.hasOwnProperty(Z))return B(Q);let G=process.env[SxQ],F=process.env[jxQ],I=pE1((Y)=>typeof Y==="string"&&Y.length>0,"nonEmptyString");if(I(G)&&I(F))D.headers[DA0]=F;return B({...Q,request:D})},"recursionDetectionMiddleware"),kjA={step:"build",tags:["RECURSION_DETECTION"],name:"recursionDetectionMiddleware",override:!0,priority:"low"},yxQ=pE1((A)=>({applyToStack:pE1((B)=>{B.add(yjA(A),kjA)},"applyToStack")}),"getRecursionDetectionPlugin")});
var pdA=E((cdA)=>{Object.defineProperty(cdA,"__esModule",{value:!0});cdA.toBase64=void 0;var xrQ=YD(),vrQ=cB(),brQ=(A)=>{let B;if(typeof A==="string")B=vrQ.fromUtf8(A);else B=A;if(typeof B!=="object"||typeof B.byteOffset!=="number"||typeof B.byteLength!=="number")throw new Error("@smithy/util-base64: toBase64 encoder function only accepts string | Uint8Array.");return xrQ.fromArrayBuffer(B.buffer,B.byteOffset,B.byteLength).toString("base64")};cdA.toBase64=brQ});
var q41=E((E90)=>{Object.defineProperty(E90,"__esModule",{value:!0});E90.STSClient=E90.__Client=void 0;var xlA=Qw1(),rtQ=Zw1(),otQ=Iw1(),vlA=$41(),ttQ=K4(),z90=CB(),etQ=bG(),AeQ=R6(),blA=u4(),hlA=w8();Object.defineProperty(E90,"__Client",{enumerable:!0,get:function(){return hlA.Client}});var flA=Y90(),BeQ=N41(),QeQ=ElA(),DeQ=_lA();class glA extends hlA.Client{config;constructor(...[A]){let B=QeQ.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=BeQ.resolveClientEndpointParameters(B),D=vlA.resolveUserAgentConfig(Q),Z=blA.resolveRetryConfig(D),G=ttQ.resolveRegionConfig(Z),F=xlA.resolveHostHeaderConfig(G),I=AeQ.resolveEndpointConfig(F),Y=flA.resolveHttpAuthSchemeConfig(I),W=DeQ.resolveRuntimeExtensions(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(vlA.getUserAgentPlugin(this.config)),this.middlewareStack.use(blA.getRetryPlugin(this.config)),this.middlewareStack.use(etQ.getContentLengthPlugin(this.config)),this.middlewareStack.use(xlA.getHostHeaderPlugin(this.config)),this.middlewareStack.use(rtQ.getLoggerPlugin(this.config)),this.middlewareStack.use(otQ.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(z90.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:flA.defaultSTSHttpAuthSchemeParametersProvider,identityProviderConfigProvider:async(J)=>new z90.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials})})),this.middlewareStack.use(z90.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}}E90.STSClient=glA});
var qB0=E((DJ5,duA)=>{var{defineProperty:nU1,getOwnPropertyDescriptor:KnQ,getOwnPropertyNames:HnQ}=Object,znQ=Object.prototype.hasOwnProperty,aU1=(A,B)=>nU1(A,"name",{value:B,configurable:!0}),EnQ=(A,B)=>{for(var Q in B)nU1(A,Q,{get:B[Q],enumerable:!0})},UnQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of HnQ(B))if(!znQ.call(A,Z)&&Z!==Q)nU1(A,Z,{get:()=>B[Z],enumerable:!(D=KnQ(B,Z))||D.enumerable})}return A},wnQ=(A)=>UnQ(nU1({},"__esModule",{value:!0}),A),xuA={};EnQ(xuA,{AlgorithmId:()=>huA,EndpointURLScheme:()=>fuA,FieldPosition:()=>guA,HttpApiKeyAuthLocation:()=>buA,HttpAuthLocation:()=>vuA,IniSectionType:()=>uuA,RequestHandlerProtocol:()=>muA,SMITHY_CONTEXT_KEY:()=>MnQ,getDefaultClientConfiguration:()=>NnQ,resolveDefaultRuntimeConfig:()=>LnQ});duA.exports=wnQ(xuA);var vuA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(vuA||{}),buA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(buA||{}),fuA=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(fuA||{}),huA=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(huA||{}),$nQ=aU1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),qnQ=aU1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),NnQ=aU1((A)=>{return $nQ(A)},"getDefaultClientConfiguration"),LnQ=aU1((A)=>{return qnQ(A)},"resolveDefaultRuntimeConfig"),guA=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(guA||{}),MnQ="__smithy_context",uuA=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(uuA||{}),muA=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(muA||{})});
var qQ0=E((nW)=>{Object.defineProperty(nW,"__esModule",{value:!0});nW.fromHttp=void 0;var VK=uh();VK.__exportStar(FjA(),nW);VK.__exportStar(wuA(),nW);VK.__exportStar(NuA(),nW);VK.__exportStar(RuA(),nW);var SB4=oU1();Object.defineProperty(nW,"fromHttp",{enumerable:!0,get:function(){return SB4.fromHttp}});VK.__exportStar(NmA(),nW);VK.__exportStar(KaA(),nW);VK.__exportStar(EaA(),nW);VK.__exportStar($Q0(),nW);VK.__exportStar(jaA(),nW);VK.__exportStar(_aA(),nW);VK.__exportStar(maA(),nW);VK.__exportStar(laA(),nW);VK.__exportStar(naA(),nW)});
var qfA=E((wfA)=>{Object.defineProperty(wfA,"__esModule",{value:!0});wfA.resolveRuntimeExtensions=void 0;var HfA=eQ1(),zfA=EV(),EfA=V6(),UfA=KfA(),kdQ=(A,B)=>{let Q=Object.assign(HfA.getAwsRegionExtensionConfiguration(A),EfA.getDefaultExtensionConfiguration(A),zfA.getHttpHandlerExtensionConfiguration(A),UfA.getHttpAuthExtensionConfiguration(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,HfA.resolveAwsRegionExtensionConfiguration(Q),EfA.resolveDefaultRuntimeConfig(Q),zfA.resolveHttpHandlerRuntimeConfig(Q),UfA.resolveHttpAuthRuntimeConfig(Q))};wfA.resolveRuntimeExtensions=kdQ});
var qhA=E((EW5,$hA)=>{var{create:HlQ,defineProperty:W41,getOwnPropertyDescriptor:zlQ,getOwnPropertyNames:ElQ,getPrototypeOf:UlQ}=Object,wlQ=Object.prototype.hasOwnProperty,cG=(A,B)=>W41(A,"name",{value:B,configurable:!0}),$lQ=(A,B)=>{for(var Q in B)W41(A,Q,{get:B[Q],enumerable:!0})},EhA=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of ElQ(B))if(!wlQ.call(A,Z)&&Z!==Q)W41(A,Z,{get:()=>B[Z],enumerable:!(D=zlQ(B,Z))||D.enumerable})}return A},$k=(A,B,Q)=>(Q=A!=null?HlQ(UlQ(A)):{},EhA(B||!A||!A.__esModule?W41(Q,"default",{value:A,enumerable:!0}):Q,A)),qlQ=(A)=>EhA(W41({},"__esModule",{value:!0}),A),UhA={};$lQ(UhA,{fromIni:()=>_lQ});$hA.exports=qlQ(UhA);var IB0=D3(),qk=Sw(),Y41=Q9(),NlQ=cG((A,B,Q)=>{let D={EcsContainer:cG(async(Z)=>{let{fromHttp:G}=await Promise.resolve().then(()=>$k(iA0())),{fromContainerMetadata:F}=await Promise.resolve().then(()=>$k(TF()));return Q?.debug("@aws-sdk/credential-provider-ini - credential_source is EcsContainer"),async()=>Y41.chain(G(Z??{}),F(Z))().then(FB0)},"EcsContainer"),Ec2InstanceMetadata:cG(async(Z)=>{Q?.debug("@aws-sdk/credential-provider-ini - credential_source is Ec2InstanceMetadata");let{fromInstanceMetadata:G}=await Promise.resolve().then(()=>$k(TF()));return async()=>G(Z)().then(FB0)},"Ec2InstanceMetadata"),Environment:cG(async(Z)=>{Q?.debug("@aws-sdk/credential-provider-ini - credential_source is Environment");let{fromEnv:G}=await Promise.resolve().then(()=>$k(cA0()));return async()=>G(Z)().then(FB0)},"Environment")};if(A in D)return D[A];else throw new Y41.CredentialsProviderError(`Unsupported credential source in profile ${B}. Got ${A}, expected EcsContainer or Ec2InstanceMetadata or Environment.`,{logger:Q})},"resolveCredentialSource"),FB0=cG((A)=>qk.setCredentialFeature(A,"CREDENTIALS_PROFILE_NAMED_PROVIDER","p"),"setNamedProvider"),LlQ=cG((A,{profile:B="default",logger:Q}={})=>{return Boolean(A)&&typeof A==="object"&&typeof A.role_arn==="string"&&["undefined","string"].indexOf(typeof A.role_session_name)>-1&&["undefined","string"].indexOf(typeof A.external_id)>-1&&["undefined","string"].indexOf(typeof A.mfa_serial)>-1&&(MlQ(A,{profile:B,logger:Q})||RlQ(A,{profile:B,logger:Q}))},"isAssumeRoleProfile"),MlQ=cG((A,{profile:B,logger:Q})=>{let D=typeof A.source_profile==="string"&&typeof A.credential_source==="undefined";if(D)Q?.debug?.(`    ${B} isAssumeRoleWithSourceProfile source_profile=${A.source_profile}`);return D},"isAssumeRoleWithSourceProfile"),RlQ=cG((A,{profile:B,logger:Q})=>{let D=typeof A.credential_source==="string"&&typeof A.source_profile==="undefined";if(D)Q?.debug?.(`    ${B} isCredentialSourceProfile credential_source=${A.credential_source}`);return D},"isCredentialSourceProfile"),OlQ=cG(async(A,B,Q,D={})=>{Q.logger?.debug("@aws-sdk/credential-provider-ini - resolveAssumeRoleCredentials (STS)");let Z=B[A],{source_profile:G,region:F}=Z;if(!Q.roleAssumer){let{getDefaultRoleAssumer:Y}=await Promise.resolve().then(()=>$k(t20()));Q.roleAssumer=Y({...Q.clientConfig,credentialProviderLogger:Q.logger,parentClientConfig:{...Q?.parentClientConfig,region:F??Q?.parentClientConfig?.region}},Q.clientPlugins)}if(G&&G in D)throw new Y41.CredentialsProviderError(`Detected a cycle attempting to resolve credentials for profile ${IB0.getProfileName(Q)}. Profiles visited: `+Object.keys(D).join(", "),{logger:Q.logger});Q.logger?.debug(`@aws-sdk/credential-provider-ini - finding credential resolver using ${G?`source_profile=[${G}]`:`profile=[${A}]`}`);let I=G?whA(G,B,Q,{...D,[G]:!0},KhA(B[G]??{})):(await NlQ(Z.credential_source,A,Q.logger)(Q))();if(KhA(Z))return I.then((Y)=>qk.setCredentialFeature(Y,"CREDENTIALS_PROFILE_SOURCE_PROFILE","o"));else{let Y={RoleArn:Z.role_arn,RoleSessionName:Z.role_session_name||`aws-sdk-js-${Date.now()}`,ExternalId:Z.external_id,DurationSeconds:parseInt(Z.duration_seconds||"3600",10)},{mfa_serial:W}=Z;if(W){if(!Q.mfaCodeProvider)throw new Y41.CredentialsProviderError(`Profile ${A} requires multi-factor authentication, but no MFA code callback was provided.`,{logger:Q.logger,tryNextLink:!1});Y.SerialNumber=W,Y.TokenCode=await Q.mfaCodeProvider(W)}let J=await I;return Q.roleAssumer(J,Y).then((X)=>qk.setCredentialFeature(X,"CREDENTIALS_PROFILE_SOURCE_PROFILE","o"))}},"resolveAssumeRoleCredentials"),KhA=cG((A)=>{return!A.role_arn&&!!A.credential_source},"isCredentialSourceWithoutRoleArn"),TlQ=cG((A)=>Boolean(A)&&typeof A==="object"&&typeof A.credential_process==="string","isProcessProfile"),PlQ=cG(async(A,B)=>Promise.resolve().then(()=>$k(BB0())).then(({fromProcess:Q})=>Q({...A,profile:B})().then((D)=>qk.setCredentialFeature(D,"CREDENTIALS_PROFILE_PROCESS","v"))),"resolveProcessCredentials"),SlQ=cG(async(A,B,Q={})=>{let{fromSSO:D}=await Promise.resolve().then(()=>$k(V20()));return D({profile:A,logger:Q.logger,parentClientConfig:Q.parentClientConfig,clientConfig:Q.clientConfig})().then((Z)=>{if(B.sso_session)return qk.setCredentialFeature(Z,"CREDENTIALS_PROFILE_SSO","r");else return qk.setCredentialFeature(Z,"CREDENTIALS_PROFILE_SSO_LEGACY","t")})},"resolveSsoCredentials"),jlQ=cG((A)=>A&&(typeof A.sso_start_url==="string"||typeof A.sso_account_id==="string"||typeof A.sso_session==="string"||typeof A.sso_region==="string"||typeof A.sso_role_name==="string"),"isSsoProfile"),HhA=cG((A)=>Boolean(A)&&typeof A==="object"&&typeof A.aws_access_key_id==="string"&&typeof A.aws_secret_access_key==="string"&&["undefined","string"].indexOf(typeof A.aws_session_token)>-1&&["undefined","string"].indexOf(typeof A.aws_account_id)>-1,"isStaticCredsProfile"),zhA=cG(async(A,B)=>{B?.logger?.debug("@aws-sdk/credential-provider-ini - resolveStaticCredentials");let Q={accessKeyId:A.aws_access_key_id,secretAccessKey:A.aws_secret_access_key,sessionToken:A.aws_session_token,...A.aws_credential_scope&&{credentialScope:A.aws_credential_scope},...A.aws_account_id&&{accountId:A.aws_account_id}};return qk.setCredentialFeature(Q,"CREDENTIALS_PROFILE","n")},"resolveStaticCredentials"),ylQ=cG((A)=>Boolean(A)&&typeof A==="object"&&typeof A.web_identity_token_file==="string"&&typeof A.role_arn==="string"&&["undefined","string"].indexOf(typeof A.role_session_name)>-1,"isWebIdentityProfile"),klQ=cG(async(A,B)=>Promise.resolve().then(()=>$k(GB0())).then(({fromTokenFile:Q})=>Q({webIdentityTokenFile:A.web_identity_token_file,roleArn:A.role_arn,roleSessionName:A.role_session_name,roleAssumerWithWebIdentity:B.roleAssumerWithWebIdentity,logger:B.logger,parentClientConfig:B.parentClientConfig})().then((D)=>qk.setCredentialFeature(D,"CREDENTIALS_PROFILE_STS_WEB_ID_TOKEN","q"))),"resolveWebIdentityCredentials"),whA=cG(async(A,B,Q,D={},Z=!1)=>{let G=B[A];if(Object.keys(D).length>0&&HhA(G))return zhA(G,Q);if(Z||LlQ(G,{profile:A,logger:Q.logger}))return OlQ(A,B,Q,D);if(HhA(G))return zhA(G,Q);if(ylQ(G))return klQ(G,Q);if(TlQ(G))return PlQ(Q,A);if(jlQ(G))return await SlQ(A,G,Q);throw new Y41.CredentialsProviderError(`Could not resolve credentials using profile: [${A}] in configuration/credentials file(s).`,{logger:Q.logger})},"resolveProfileData"),_lQ=cG((A={})=>async({callerClientConfig:B}={})=>{let Q={...A,parentClientConfig:{...B,...A.parentClientConfig}};Q.logger?.debug("@aws-sdk/credential-provider-ini - fromIni");let D=await IB0.parseKnownFiles(Q);return whA(IB0.getProfileName({profile:A.profile??B?.profile}),D,Q)},"fromIni")});
var riA=E((aiA)=>{Object.defineProperty(aiA,"__esModule",{value:!0});aiA.defaultEndpointResolver=void 0;var v04=K41(),IQ0=S7(),b04=niA(),f04=new IQ0.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),h04=(A,B={})=>{return f04.get(A,()=>IQ0.resolveEndpoint(b04.ruleSet,{endpointParams:A,logger:B.logger}))};aiA.defaultEndpointResolver=h04;IQ0.customEndpointFunctions.aws=v04.awsEndpointFunctions});
var shA=E((nhA)=>{Object.defineProperty(nhA,"__esModule",{value:!0});nhA.defaultEndpointResolver=void 0;var plQ=mn(),XB0=S7(),ilQ=ihA(),nlQ=new XB0.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),alQ=(A,B={})=>{return nlQ.get(A,()=>XB0.resolveEndpoint(ilQ.ruleSet,{endpointParams:A,logger:B.logger}))};nhA.defaultEndpointResolver=alQ;XB0.customEndpointFunctions.aws=plQ.awsEndpointFunctions});
var t20=E((WW5,o20)=>{var{defineProperty:vU1,getOwnPropertyDescriptor:mdQ,getOwnPropertyNames:ddQ}=Object,cdQ=Object.prototype.hasOwnProperty,Z9=(A,B)=>vU1(A,"name",{value:B,configurable:!0}),ldQ=(A,B)=>{for(var Q in B)vU1(A,Q,{get:B[Q],enumerable:!0})},l20=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of ddQ(B))if(!cdQ.call(A,Z)&&Z!==Q)vU1(A,Z,{get:()=>B[Z],enumerable:!(D=mdQ(B,Z))||D.enumerable})}return A},pdQ=(A,B,Q)=>(l20(A,B,"default"),Q&&l20(Q,B,"default")),idQ=(A)=>l20(vU1({},"__esModule",{value:!0}),A),i20={};ldQ(i20,{AssumeRoleCommand:()=>s20,AssumeRoleResponseFilterSensitiveLog:()=>yfA,AssumeRoleWithWebIdentityCommand:()=>r20,AssumeRoleWithWebIdentityRequestFilterSensitiveLog:()=>hfA,AssumeRoleWithWebIdentityResponseFilterSensitiveLog:()=>gfA,ClientInputEndpointParameters:()=>hcQ.ClientInputEndpointParameters,CredentialsFilterSensitiveLog:()=>n20,ExpiredTokenException:()=>kfA,IDPCommunicationErrorException:()=>ufA,IDPRejectedClaimException:()=>bfA,InvalidIdentityTokenException:()=>ffA,MalformedPolicyDocumentException:()=>_fA,PackedPolicyTooLargeException:()=>xfA,RegionDisabledException:()=>vfA,STS:()=>ofA,STSServiceException:()=>KT,decorateDefaultCredentialProvider:()=>mcQ,getDefaultRoleAssumer:()=>DhA,getDefaultRoleAssumerWithWebIdentity:()=>ZhA});o20.exports=idQ(i20);pdQ(i20,F41(),o20.exports);var ndQ=V6(),adQ=R6(),sdQ=j3(),rdQ=V6(),odQ=I41(),jfA=V6(),tdQ=V6(),KT=class A extends tdQ.ServiceException{static{Z9(this,"STSServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},n20=Z9((A)=>({...A,...A.SecretAccessKey&&{SecretAccessKey:jfA.SENSITIVE_STRING}}),"CredentialsFilterSensitiveLog"),yfA=Z9((A)=>({...A,...A.Credentials&&{Credentials:n20(A.Credentials)}}),"AssumeRoleResponseFilterSensitiveLog"),kfA=class A extends KT{static{Z9(this,"ExpiredTokenException")}name="ExpiredTokenException";$fault="client";constructor(B){super({name:"ExpiredTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},_fA=class A extends KT{static{Z9(this,"MalformedPolicyDocumentException")}name="MalformedPolicyDocumentException";$fault="client";constructor(B){super({name:"MalformedPolicyDocumentException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},xfA=class A extends KT{static{Z9(this,"PackedPolicyTooLargeException")}name="PackedPolicyTooLargeException";$fault="client";constructor(B){super({name:"PackedPolicyTooLargeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},vfA=class A extends KT{static{Z9(this,"RegionDisabledException")}name="RegionDisabledException";$fault="client";constructor(B){super({name:"RegionDisabledException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},bfA=class A extends KT{static{Z9(this,"IDPRejectedClaimException")}name="IDPRejectedClaimException";$fault="client";constructor(B){super({name:"IDPRejectedClaimException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},ffA=class A extends KT{static{Z9(this,"InvalidIdentityTokenException")}name="InvalidIdentityTokenException";$fault="client";constructor(B){super({name:"InvalidIdentityTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},hfA=Z9((A)=>({...A,...A.WebIdentityToken&&{WebIdentityToken:jfA.SENSITIVE_STRING}}),"AssumeRoleWithWebIdentityRequestFilterSensitiveLog"),gfA=Z9((A)=>({...A,...A.Credentials&&{Credentials:n20(A.Credentials)}}),"AssumeRoleWithWebIdentityResponseFilterSensitiveLog"),ufA=class A extends KT{static{Z9(this,"IDPCommunicationErrorException")}name="IDPCommunicationErrorException";$fault="client";constructor(B){super({name:"IDPCommunicationErrorException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},a20=VI(),edQ=EV(),V5=V6(),AcQ=Z9(async(A,B)=>{let Q=ifA,D;return D=rfA({...XcQ(A,B),[afA]:ScQ,[sfA]:nfA}),pfA(B,Q,"/",void 0,D)},"se_AssumeRoleCommand"),BcQ=Z9(async(A,B)=>{let Q=ifA,D;return D=rfA({...VcQ(A,B),[afA]:jcQ,[sfA]:nfA}),pfA(B,Q,"/",void 0,D)},"se_AssumeRoleWithWebIdentityCommand"),QcQ=Z9(async(A,B)=>{if(A.statusCode>=300)return mfA(A,B);let Q=await a20.parseXmlBody(A.body,B),D={};return D=wcQ(Q.AssumeRoleResult,B),{$metadata:HT(A),...D}},"de_AssumeRoleCommand"),DcQ=Z9(async(A,B)=>{if(A.statusCode>=300)return mfA(A,B);let Q=await a20.parseXmlBody(A.body,B),D={};return D=$cQ(Q.AssumeRoleWithWebIdentityResult,B),{$metadata:HT(A),...D}},"de_AssumeRoleWithWebIdentityCommand"),mfA=Z9(async(A,B)=>{let Q={...A,body:await a20.parseXmlErrorBody(A.body,B)},D=ycQ(A,Q.body);switch(D){case"ExpiredTokenException":case"com.amazonaws.sts#ExpiredTokenException":throw await ZcQ(Q,B);case"MalformedPolicyDocument":case"com.amazonaws.sts#MalformedPolicyDocumentException":throw await YcQ(Q,B);case"PackedPolicyTooLarge":case"com.amazonaws.sts#PackedPolicyTooLargeException":throw await WcQ(Q,B);case"RegionDisabledException":case"com.amazonaws.sts#RegionDisabledException":throw await JcQ(Q,B);case"IDPCommunicationError":case"com.amazonaws.sts#IDPCommunicationErrorException":throw await GcQ(Q,B);case"IDPRejectedClaim":case"com.amazonaws.sts#IDPRejectedClaimException":throw await FcQ(Q,B);case"InvalidIdentityToken":case"com.amazonaws.sts#InvalidIdentityTokenException":throw await IcQ(Q,B);default:let Z=Q.body;return PcQ({output:A,parsedBody:Z.Error,errorCode:D})}},"de_CommandError"),ZcQ=Z9(async(A,B)=>{let Q=A.body,D=qcQ(Q.Error,B),Z=new kfA({$metadata:HT(A),...D});return V5.decorateServiceException(Z,Q)},"de_ExpiredTokenExceptionRes"),GcQ=Z9(async(A,B)=>{let Q=A.body,D=NcQ(Q.Error,B),Z=new ufA({$metadata:HT(A),...D});return V5.decorateServiceException(Z,Q)},"de_IDPCommunicationErrorExceptionRes"),FcQ=Z9(async(A,B)=>{let Q=A.body,D=LcQ(Q.Error,B),Z=new bfA({$metadata:HT(A),...D});return V5.decorateServiceException(Z,Q)},"de_IDPRejectedClaimExceptionRes"),IcQ=Z9(async(A,B)=>{let Q=A.body,D=McQ(Q.Error,B),Z=new ffA({$metadata:HT(A),...D});return V5.decorateServiceException(Z,Q)},"de_InvalidIdentityTokenExceptionRes"),YcQ=Z9(async(A,B)=>{let Q=A.body,D=RcQ(Q.Error,B),Z=new _fA({$metadata:HT(A),...D});return V5.decorateServiceException(Z,Q)},"de_MalformedPolicyDocumentExceptionRes"),WcQ=Z9(async(A,B)=>{let Q=A.body,D=OcQ(Q.Error,B),Z=new xfA({$metadata:HT(A),...D});return V5.decorateServiceException(Z,Q)},"de_PackedPolicyTooLargeExceptionRes"),JcQ=Z9(async(A,B)=>{let Q=A.body,D=TcQ(Q.Error,B),Z=new vfA({$metadata:HT(A),...D});return V5.decorateServiceException(Z,Q)},"de_RegionDisabledExceptionRes"),XcQ=Z9((A,B)=>{let Q={};if(A[Ja]!=null)Q[Ja]=A[Ja];if(A[Xa]!=null)Q[Xa]=A[Xa];if(A[Ya]!=null){let D=dfA(A[Ya],B);if(A[Ya]?.length===0)Q.PolicyArns=[];Object.entries(D).forEach(([Z,G])=>{let F=`PolicyArns.${Z}`;Q[F]=G})}if(A[Ia]!=null)Q[Ia]=A[Ia];if(A[Fa]!=null)Q[Fa]=A[Fa];if(A[h20]!=null){let D=UcQ(A[h20],B);if(A[h20]?.length===0)Q.Tags=[];Object.entries(D).forEach(([Z,G])=>{let F=`Tags.${Z}`;Q[F]=G})}if(A[u20]!=null){let D=EcQ(A[u20],B);if(A[u20]?.length===0)Q.TransitiveTagKeys=[];Object.entries(D).forEach(([Z,G])=>{let F=`TransitiveTagKeys.${Z}`;Q[F]=G})}if(A[P20]!=null)Q[P20]=A[P20];if(A[b20]!=null)Q[b20]=A[b20];if(A[g20]!=null)Q[g20]=A[g20];if(A[CT]!=null)Q[CT]=A[CT];if(A[y20]!=null){let D=HcQ(A[y20],B);if(A[y20]?.length===0)Q.ProvidedContexts=[];Object.entries(D).forEach(([Z,G])=>{let F=`ProvidedContexts.${Z}`;Q[F]=G})}return Q},"se_AssumeRoleRequest"),VcQ=Z9((A,B)=>{let Q={};if(A[Ja]!=null)Q[Ja]=A[Ja];if(A[Xa]!=null)Q[Xa]=A[Xa];if(A[d20]!=null)Q[d20]=A[d20];if(A[k20]!=null)Q[k20]=A[k20];if(A[Ya]!=null){let D=dfA(A[Ya],B);if(A[Ya]?.length===0)Q.PolicyArns=[];Object.entries(D).forEach(([Z,G])=>{let F=`PolicyArns.${Z}`;Q[F]=G})}if(A[Ia]!=null)Q[Ia]=A[Ia];if(A[Fa]!=null)Q[Fa]=A[Fa];return Q},"se_AssumeRoleWithWebIdentityRequest"),dfA=Z9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=CcQ(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_policyDescriptorListType"),CcQ=Z9((A,B)=>{let Q={};if(A[c20]!=null)Q[c20]=A[c20];return Q},"se_PolicyDescriptorType"),KcQ=Z9((A,B)=>{let Q={};if(A[j20]!=null)Q[j20]=A[j20];if(A[O20]!=null)Q[O20]=A[O20];return Q},"se_ProvidedContext"),HcQ=Z9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=KcQ(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_ProvidedContextsListType"),zcQ=Z9((A,B)=>{let Q={};if(A[S20]!=null)Q[S20]=A[S20];if(A[m20]!=null)Q[m20]=A[m20];return Q},"se_Tag"),EcQ=Z9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;Q[`member.${D}`]=Z,D++}return Q},"se_tagKeyListType"),UcQ=Z9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=zcQ(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_tagListType"),cfA=Z9((A,B)=>{let Q={};if(A[L20]!=null)Q[L20]=V5.expectString(A[L20]);if(A[M20]!=null)Q[M20]=V5.expectString(A[M20]);return Q},"de_AssumedRoleUser"),wcQ=Z9((A,B)=>{let Q={};if(A[Ga]!=null)Q[Ga]=lfA(A[Ga],B);if(A[Za]!=null)Q[Za]=cfA(A[Za],B);if(A[Wa]!=null)Q[Wa]=V5.strictParseInt32(A[Wa]);if(A[CT]!=null)Q[CT]=V5.expectString(A[CT]);return Q},"de_AssumeRoleResponse"),$cQ=Z9((A,B)=>{let Q={};if(A[Ga]!=null)Q[Ga]=lfA(A[Ga],B);if(A[v20]!=null)Q[v20]=V5.expectString(A[v20]);if(A[Za]!=null)Q[Za]=cfA(A[Za],B);if(A[Wa]!=null)Q[Wa]=V5.strictParseInt32(A[Wa]);if(A[_20]!=null)Q[_20]=V5.expectString(A[_20]);if(A[R20]!=null)Q[R20]=V5.expectString(A[R20]);if(A[CT]!=null)Q[CT]=V5.expectString(A[CT]);return Q},"de_AssumeRoleWithWebIdentityResponse"),lfA=Z9((A,B)=>{let Q={};if(A[N20]!=null)Q[N20]=V5.expectString(A[N20]);if(A[x20]!=null)Q[x20]=V5.expectString(A[x20]);if(A[f20]!=null)Q[f20]=V5.expectString(A[f20]);if(A[T20]!=null)Q[T20]=V5.expectNonNull(V5.parseRfc3339DateTimeWithOffset(A[T20]));return Q},"de_Credentials"),qcQ=Z9((A,B)=>{let Q={};if(A[tZ]!=null)Q[tZ]=V5.expectString(A[tZ]);return Q},"de_ExpiredTokenException"),NcQ=Z9((A,B)=>{let Q={};if(A[tZ]!=null)Q[tZ]=V5.expectString(A[tZ]);return Q},"de_IDPCommunicationErrorException"),LcQ=Z9((A,B)=>{let Q={};if(A[tZ]!=null)Q[tZ]=V5.expectString(A[tZ]);return Q},"de_IDPRejectedClaimException"),McQ=Z9((A,B)=>{let Q={};if(A[tZ]!=null)Q[tZ]=V5.expectString(A[tZ]);return Q},"de_InvalidIdentityTokenException"),RcQ=Z9((A,B)=>{let Q={};if(A[tZ]!=null)Q[tZ]=V5.expectString(A[tZ]);return Q},"de_MalformedPolicyDocumentException"),OcQ=Z9((A,B)=>{let Q={};if(A[tZ]!=null)Q[tZ]=V5.expectString(A[tZ]);return Q},"de_PackedPolicyTooLargeException"),TcQ=Z9((A,B)=>{let Q={};if(A[tZ]!=null)Q[tZ]=V5.expectString(A[tZ]);return Q},"de_RegionDisabledException"),HT=Z9((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),PcQ=V5.withBaseException(KT),pfA=Z9(async(A,B,Q,D,Z)=>{let{hostname:G,protocol:F="https",port:I,path:Y}=await A.endpoint(),W={protocol:F,hostname:G,port:I,method:"POST",path:Y.endsWith("/")?Y.slice(0,-1)+Q:Y+Q,headers:B};if(D!==void 0)W.hostname=D;if(Z!==void 0)W.body=Z;return new edQ.HttpRequest(W)},"buildHttpRpcRequest"),ifA={"content-type":"application/x-www-form-urlencoded"},nfA="2011-06-15",afA="Action",N20="AccessKeyId",ScQ="AssumeRole",L20="AssumedRoleId",Za="AssumedRoleUser",jcQ="AssumeRoleWithWebIdentity",M20="Arn",R20="Audience",Ga="Credentials",O20="ContextAssertion",Fa="DurationSeconds",T20="Expiration",P20="ExternalId",S20="Key",Ia="Policy",Ya="PolicyArns",j20="ProviderArn",y20="ProvidedContexts",k20="ProviderId",Wa="PackedPolicySize",_20="Provider",Ja="RoleArn",Xa="RoleSessionName",x20="SecretAccessKey",v20="SubjectFromWebIdentityToken",CT="SourceIdentity",b20="SerialNumber",f20="SessionToken",h20="Tags",g20="TokenCode",u20="TransitiveTagKeys",sfA="Version",m20="Value",d20="WebIdentityToken",c20="arn",tZ="message",rfA=Z9((A)=>Object.entries(A).map(([B,Q])=>V5.extendedEncodeURIComponent(B)+"="+V5.extendedEncodeURIComponent(Q)).join("&"),"buildFormUrlencodedString"),ycQ=Z9((A,B)=>{if(B.Error?.Code!==void 0)return B.Error.Code;if(A.statusCode==404)return"NotFound"},"loadQueryErrorCode"),s20=class extends rdQ.Command.classBuilder().ep(odQ.commonParams).m(function(A,B,Q,D){return[sdQ.getSerdePlugin(Q,this.serialize,this.deserialize),adQ.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRole",{}).n("STSClient","AssumeRoleCommand").f(void 0,yfA).ser(AcQ).de(QcQ).build(){static{Z9(this,"AssumeRoleCommand")}},kcQ=R6(),_cQ=j3(),xcQ=V6(),vcQ=I41(),r20=class extends xcQ.Command.classBuilder().ep(vcQ.commonParams).m(function(A,B,Q,D){return[_cQ.getSerdePlugin(Q,this.serialize,this.deserialize),kcQ.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRoleWithWebIdentity",{}).n("STSClient","AssumeRoleWithWebIdentityCommand").f(hfA,gfA).ser(BcQ).de(DcQ).build(){static{Z9(this,"AssumeRoleWithWebIdentityCommand")}},bcQ=F41(),fcQ={AssumeRoleCommand:s20,AssumeRoleWithWebIdentityCommand:r20},ofA=class extends bcQ.STSClient{static{Z9(this,"STS")}};ndQ.createAggregatedClient(fcQ,ofA);var hcQ=I41(),p20=Sw(),SfA="us-east-1",tfA=Z9((A)=>{if(typeof A?.Arn==="string"){let B=A.Arn.split(":");if(B.length>4&&B[4]!=="")return B[4]}return},"getAccountIdFromAssumedRoleUser"),efA=Z9(async(A,B,Q)=>{let D=typeof A==="function"?await A():A,Z=typeof B==="function"?await B():B;return Q?.debug?.("@aws-sdk/client-sts::resolveRegion","accepting first of:",`${D} (provider)`,`${Z} (parent client)`,`${SfA} (STS default)`),D??Z??SfA},"resolveRegion"),gcQ=Z9((A,B)=>{let Q,D;return async(Z,G)=>{if(D=Z,!Q){let{logger:J=A?.parentClientConfig?.logger,region:X,requestHandler:V=A?.parentClientConfig?.requestHandler,credentialProviderLogger:C}=A,K=await efA(X,A?.parentClientConfig?.region,C),H=!AhA(V);Q=new B({profile:A?.parentClientConfig?.profile,credentialDefaultProvider:Z9(()=>async()=>D,"credentialDefaultProvider"),region:K,requestHandler:H?V:void 0,logger:J})}let{Credentials:F,AssumedRoleUser:I}=await Q.send(new s20(G));if(!F||!F.AccessKeyId||!F.SecretAccessKey)throw new Error(`Invalid response from STS.assumeRole call with role ${G.RoleArn}`);let Y=tfA(I),W={accessKeyId:F.AccessKeyId,secretAccessKey:F.SecretAccessKey,sessionToken:F.SessionToken,expiration:F.Expiration,...F.CredentialScope&&{credentialScope:F.CredentialScope},...Y&&{accountId:Y}};return p20.setCredentialFeature(W,"CREDENTIALS_STS_ASSUME_ROLE","i"),W}},"getDefaultRoleAssumer"),ucQ=Z9((A,B)=>{let Q;return async(D)=>{if(!Q){let{logger:Y=A?.parentClientConfig?.logger,region:W,requestHandler:J=A?.parentClientConfig?.requestHandler,credentialProviderLogger:X}=A,V=await efA(W,A?.parentClientConfig?.region,X),C=!AhA(J);Q=new B({profile:A?.parentClientConfig?.profile,region:V,requestHandler:C?J:void 0,logger:Y})}let{Credentials:Z,AssumedRoleUser:G}=await Q.send(new r20(D));if(!Z||!Z.AccessKeyId||!Z.SecretAccessKey)throw new Error(`Invalid response from STS.assumeRoleWithWebIdentity call with role ${D.RoleArn}`);let F=tfA(G),I={accessKeyId:Z.AccessKeyId,secretAccessKey:Z.SecretAccessKey,sessionToken:Z.SessionToken,expiration:Z.Expiration,...Z.CredentialScope&&{credentialScope:Z.CredentialScope},...F&&{accountId:F}};if(F)p20.setCredentialFeature(I,"RESOLVED_ACCOUNT_ID","T");return p20.setCredentialFeature(I,"CREDENTIALS_STS_ASSUME_ROLE_WEB_ID","k"),I}},"getDefaultRoleAssumerWithWebIdentity"),AhA=Z9((A)=>{return A?.metadata?.handlerProtocol==="h2"},"isH2"),BhA=F41(),QhA=Z9((A,B)=>{if(!B)return A;else return class Q extends A{static{Z9(this,"CustomizableSTSClient")}constructor(D){super(D);for(let Z of B)this.middlewareStack.use(Z)}}},"getCustomizableStsClientCtor"),DhA=Z9((A={},B)=>gcQ(A,QhA(BhA.STSClient,B)),"getDefaultRoleAssumer"),ZhA=Z9((A={},B)=>ucQ(A,QhA(BhA.STSClient,B)),"getDefaultRoleAssumerWithWebIdentity"),mcQ=Z9((A)=>(B)=>A({roleAssumer:DhA(B),roleAssumerWithWebIdentity:ZhA(B),...B}),"decorateDefaultCredentialProvider")});
var th=E((vJ5,ww1)=>{var{defineProperty:idA,getOwnPropertyDescriptor:frQ,getOwnPropertyNames:hrQ}=Object,grQ=Object.prototype.hasOwnProperty,nB0=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of hrQ(B))if(!grQ.call(A,Z)&&Z!==Q)idA(A,Z,{get:()=>B[Z],enumerable:!(D=frQ(B,Z))||D.enumerable})}return A},ndA=(A,B,Q)=>(nB0(A,B,"default"),Q&&nB0(Q,B,"default")),urQ=(A)=>nB0(idA({},"__esModule",{value:!0}),A),aB0={};ww1.exports=urQ(aB0);ndA(aB0,ddA(),ww1.exports);ndA(aB0,pdA(),ww1.exports)});
var tmA=E((RJ5,omA)=>{var{defineProperty:Jw1,getOwnPropertyDescriptor:fsQ,getOwnPropertyNames:hsQ}=Object,gsQ=Object.prototype.hasOwnProperty,_B0=(A,B)=>Jw1(A,"name",{value:B,configurable:!0}),usQ=(A,B)=>{for(var Q in B)Jw1(A,Q,{get:B[Q],enumerable:!0})},msQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of hsQ(B))if(!gsQ.call(A,Z)&&Z!==Q)Jw1(A,Z,{get:()=>B[Z],enumerable:!(D=fsQ(B,Z))||D.enumerable})}return A},dsQ=(A)=>msQ(Jw1({},"__esModule",{value:!0}),A),smA={};usQ(smA,{escapeUri:()=>rmA,escapeUriPath:()=>lsQ});omA.exports=dsQ(smA);var rmA=_B0((A)=>encodeURIComponent(A).replace(/[!'()*]/g,csQ),"escapeUri"),csQ=_B0((A)=>`%${A.charCodeAt(0).toString(16).toUpperCase()}`,"hexEncode"),lsQ=_B0((A)=>A.split("/").map(rmA).join("/"),"escapeUriPath")});
var txA=E((xY5,oxA)=>{var{defineProperty:NU1,getOwnPropertyDescriptor:PgQ,getOwnPropertyNames:SgQ}=Object,jgQ=Object.prototype.hasOwnProperty,T6=(A,B)=>NU1(A,"name",{value:B,configurable:!0}),ygQ=(A,B)=>{for(var Q in B)NU1(A,Q,{get:B[Q],enumerable:!0})},kgQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of SgQ(B))if(!jgQ.call(A,Z)&&Z!==Q)NU1(A,Z,{get:()=>B[Z],enumerable:!(D=PgQ(B,Z))||D.enumerable})}return A},_gQ=(A)=>kgQ(NU1({},"__esModule",{value:!0}),A),yxA={};ygQ(yxA,{GetRoleCredentialsCommand:()=>axA,GetRoleCredentialsRequestFilterSensitiveLog:()=>bxA,GetRoleCredentialsResponseFilterSensitiveLog:()=>hxA,InvalidRequestException:()=>kxA,ListAccountRolesCommand:()=>eA0,ListAccountRolesRequestFilterSensitiveLog:()=>gxA,ListAccountsCommand:()=>A20,ListAccountsRequestFilterSensitiveLog:()=>uxA,LogoutCommand:()=>sxA,LogoutRequestFilterSensitiveLog:()=>mxA,ResourceNotFoundException:()=>_xA,RoleCredentialsFilterSensitiveLog:()=>fxA,SSO:()=>rxA,SSOClient:()=>MU1,SSOServiceException:()=>tn,TooManyRequestsException:()=>xxA,UnauthorizedException:()=>vxA,__Client:()=>RB.Client,paginateListAccountRoles:()=>GuQ,paginateListAccounts:()=>FuQ});oxA.exports=_gQ(yxA);var RxA=cQ1(),xgQ=lQ1(),vgQ=pQ1(),OxA=an(),bgQ=K4(),JT=CB(),fgQ=bG(),B41=R6(),TxA=u4(),PxA=aA0(),hgQ=T6((A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,defaultSigningName:"awsssoportal"})},"resolveClientEndpointParameters"),LU1={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}},ggQ=UxA(),SxA=eQ1(),jxA=EV(),RB=V6(),ugQ=T6((A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}},"getHttpAuthExtensionConfiguration"),mgQ=T6((A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}},"resolveHttpAuthRuntimeConfig"),dgQ=T6((A,B)=>{let Q=Object.assign(SxA.getAwsRegionExtensionConfiguration(A),RB.getDefaultExtensionConfiguration(A),jxA.getHttpHandlerExtensionConfiguration(A),ugQ(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,SxA.resolveAwsRegionExtensionConfiguration(Q),RB.resolveDefaultRuntimeConfig(Q),jxA.resolveHttpHandlerRuntimeConfig(Q),mgQ(Q))},"resolveRuntimeExtensions"),MU1=class extends RB.Client{static{T6(this,"SSOClient")}config;constructor(...[A]){let B=ggQ.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=hgQ(B),D=OxA.resolveUserAgentConfig(Q),Z=TxA.resolveRetryConfig(D),G=bgQ.resolveRegionConfig(Z),F=RxA.resolveHostHeaderConfig(G),I=B41.resolveEndpointConfig(F),Y=PxA.resolveHttpAuthSchemeConfig(I),W=dgQ(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(OxA.getUserAgentPlugin(this.config)),this.middlewareStack.use(TxA.getRetryPlugin(this.config)),this.middlewareStack.use(fgQ.getContentLengthPlugin(this.config)),this.middlewareStack.use(RxA.getHostHeaderPlugin(this.config)),this.middlewareStack.use(xgQ.getLoggerPlugin(this.config)),this.middlewareStack.use(vgQ.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(JT.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:PxA.defaultSSOHttpAuthSchemeParametersProvider,identityProviderConfigProvider:T6(async(J)=>new JT.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials}),"identityProviderConfigProvider")})),this.middlewareStack.use(JT.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}},RU1=j3(),tn=class A extends RB.ServiceException{static{T6(this,"SSOServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},kxA=class A extends tn{static{T6(this,"InvalidRequestException")}name="InvalidRequestException";$fault="client";constructor(B){super({name:"InvalidRequestException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},_xA=class A extends tn{static{T6(this,"ResourceNotFoundException")}name="ResourceNotFoundException";$fault="client";constructor(B){super({name:"ResourceNotFoundException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},xxA=class A extends tn{static{T6(this,"TooManyRequestsException")}name="TooManyRequestsException";$fault="client";constructor(B){super({name:"TooManyRequestsException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},vxA=class A extends tn{static{T6(this,"UnauthorizedException")}name="UnauthorizedException";$fault="client";constructor(B){super({name:"UnauthorizedException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},bxA=T6((A)=>({...A,...A.accessToken&&{accessToken:RB.SENSITIVE_STRING}}),"GetRoleCredentialsRequestFilterSensitiveLog"),fxA=T6((A)=>({...A,...A.secretAccessKey&&{secretAccessKey:RB.SENSITIVE_STRING},...A.sessionToken&&{sessionToken:RB.SENSITIVE_STRING}}),"RoleCredentialsFilterSensitiveLog"),hxA=T6((A)=>({...A,...A.roleCredentials&&{roleCredentials:fxA(A.roleCredentials)}}),"GetRoleCredentialsResponseFilterSensitiveLog"),gxA=T6((A)=>({...A,...A.accessToken&&{accessToken:RB.SENSITIVE_STRING}}),"ListAccountRolesRequestFilterSensitiveLog"),uxA=T6((A)=>({...A,...A.accessToken&&{accessToken:RB.SENSITIVE_STRING}}),"ListAccountsRequestFilterSensitiveLog"),mxA=T6((A)=>({...A,...A.accessToken&&{accessToken:RB.SENSITIVE_STRING}}),"LogoutRequestFilterSensitiveLog"),A41=VI(),cgQ=T6(async(A,B)=>{let Q=JT.requestBuilder(A,B),D=RB.map({},RB.isSerializableHeaderValue,{[PU1]:A[TU1]});Q.bp("/federation/credentials");let Z=RB.map({[DuQ]:[,RB.expectNonNull(A[QuQ],"roleName")],[cxA]:[,RB.expectNonNull(A[dxA],"accountId")]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_GetRoleCredentialsCommand"),lgQ=T6(async(A,B)=>{let Q=JT.requestBuilder(A,B),D=RB.map({},RB.isSerializableHeaderValue,{[PU1]:A[TU1]});Q.bp("/assignment/roles");let Z=RB.map({[nxA]:[,A[ixA]],[pxA]:[()=>A.maxResults!==void 0,()=>A[lxA].toString()],[cxA]:[,RB.expectNonNull(A[dxA],"accountId")]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListAccountRolesCommand"),pgQ=T6(async(A,B)=>{let Q=JT.requestBuilder(A,B),D=RB.map({},RB.isSerializableHeaderValue,{[PU1]:A[TU1]});Q.bp("/assignment/accounts");let Z=RB.map({[nxA]:[,A[ixA]],[pxA]:[()=>A.maxResults!==void 0,()=>A[lxA].toString()]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListAccountsCommand"),igQ=T6(async(A,B)=>{let Q=JT.requestBuilder(A,B),D=RB.map({},RB.isSerializableHeaderValue,{[PU1]:A[TU1]});Q.bp("/logout");let Z;return Q.m("POST").h(D).b(Z),Q.build()},"se_LogoutCommand"),ngQ=T6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return OU1(A,B);let Q=RB.map({$metadata:Ek(A)}),D=RB.expectNonNull(RB.expectObject(await A41.parseJsonBody(A.body,B)),"body"),Z=RB.take(D,{roleCredentials:RB._json});return Object.assign(Q,Z),Q},"de_GetRoleCredentialsCommand"),agQ=T6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return OU1(A,B);let Q=RB.map({$metadata:Ek(A)}),D=RB.expectNonNull(RB.expectObject(await A41.parseJsonBody(A.body,B)),"body"),Z=RB.take(D,{nextToken:RB.expectString,roleList:RB._json});return Object.assign(Q,Z),Q},"de_ListAccountRolesCommand"),sgQ=T6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return OU1(A,B);let Q=RB.map({$metadata:Ek(A)}),D=RB.expectNonNull(RB.expectObject(await A41.parseJsonBody(A.body,B)),"body"),Z=RB.take(D,{accountList:RB._json,nextToken:RB.expectString});return Object.assign(Q,Z),Q},"de_ListAccountsCommand"),rgQ=T6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return OU1(A,B);let Q=RB.map({$metadata:Ek(A)});return await RB.collectBody(A.body,B),Q},"de_LogoutCommand"),OU1=T6(async(A,B)=>{let Q={...A,body:await A41.parseJsonErrorBody(A.body,B)},D=A41.loadRestJsonErrorCode(A,Q.body);switch(D){case"InvalidRequestException":case"com.amazonaws.sso#InvalidRequestException":throw await tgQ(Q,B);case"ResourceNotFoundException":case"com.amazonaws.sso#ResourceNotFoundException":throw await egQ(Q,B);case"TooManyRequestsException":case"com.amazonaws.sso#TooManyRequestsException":throw await AuQ(Q,B);case"UnauthorizedException":case"com.amazonaws.sso#UnauthorizedException":throw await BuQ(Q,B);default:let Z=Q.body;return ogQ({output:A,parsedBody:Z,errorCode:D})}},"de_CommandError"),ogQ=RB.withBaseException(tn),tgQ=T6(async(A,B)=>{let Q=RB.map({}),D=A.body,Z=RB.take(D,{message:RB.expectString});Object.assign(Q,Z);let G=new kxA({$metadata:Ek(A),...Q});return RB.decorateServiceException(G,A.body)},"de_InvalidRequestExceptionRes"),egQ=T6(async(A,B)=>{let Q=RB.map({}),D=A.body,Z=RB.take(D,{message:RB.expectString});Object.assign(Q,Z);let G=new _xA({$metadata:Ek(A),...Q});return RB.decorateServiceException(G,A.body)},"de_ResourceNotFoundExceptionRes"),AuQ=T6(async(A,B)=>{let Q=RB.map({}),D=A.body,Z=RB.take(D,{message:RB.expectString});Object.assign(Q,Z);let G=new xxA({$metadata:Ek(A),...Q});return RB.decorateServiceException(G,A.body)},"de_TooManyRequestsExceptionRes"),BuQ=T6(async(A,B)=>{let Q=RB.map({}),D=A.body,Z=RB.take(D,{message:RB.expectString});Object.assign(Q,Z);let G=new vxA({$metadata:Ek(A),...Q});return RB.decorateServiceException(G,A.body)},"de_UnauthorizedExceptionRes"),Ek=T6((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),dxA="accountId",TU1="accessToken",cxA="account_id",lxA="maxResults",pxA="max_result",ixA="nextToken",nxA="next_token",QuQ="roleName",DuQ="role_name",PU1="x-amz-sso_bearer_token",axA=class extends RB.Command.classBuilder().ep(LU1).m(function(A,B,Q,D){return[RU1.getSerdePlugin(Q,this.serialize,this.deserialize),B41.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","GetRoleCredentials",{}).n("SSOClient","GetRoleCredentialsCommand").f(bxA,hxA).ser(cgQ).de(ngQ).build(){static{T6(this,"GetRoleCredentialsCommand")}},eA0=class extends RB.Command.classBuilder().ep(LU1).m(function(A,B,Q,D){return[RU1.getSerdePlugin(Q,this.serialize,this.deserialize),B41.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","ListAccountRoles",{}).n("SSOClient","ListAccountRolesCommand").f(gxA,void 0).ser(lgQ).de(agQ).build(){static{T6(this,"ListAccountRolesCommand")}},A20=class extends RB.Command.classBuilder().ep(LU1).m(function(A,B,Q,D){return[RU1.getSerdePlugin(Q,this.serialize,this.deserialize),B41.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","ListAccounts",{}).n("SSOClient","ListAccountsCommand").f(uxA,void 0).ser(pgQ).de(sgQ).build(){static{T6(this,"ListAccountsCommand")}},sxA=class extends RB.Command.classBuilder().ep(LU1).m(function(A,B,Q,D){return[RU1.getSerdePlugin(Q,this.serialize,this.deserialize),B41.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","Logout",{}).n("SSOClient","LogoutCommand").f(mxA,void 0).ser(igQ).de(rgQ).build(){static{T6(this,"LogoutCommand")}},ZuQ={GetRoleCredentialsCommand:axA,ListAccountRolesCommand:eA0,ListAccountsCommand:A20,LogoutCommand:sxA},rxA=class extends MU1{static{T6(this,"SSO")}};RB.createAggregatedClient(ZuQ,rxA);var GuQ=JT.createPaginator(MU1,eA0,"nextToken","nextToken","maxResults"),FuQ=JT.createPaginator(MU1,A20,"nextToken","nextToken","maxResults")});
var uh=E((NI5,hE1)=>{var ySA,kSA,_SA,xSA,vSA,bSA,fSA,hSA,gSA,uSA,mSA,dSA,cSA,bE1,e00,lSA,pSA,iSA,gn,nSA,aSA,sSA,rSA,oSA,tSA,eSA,AjA,BjA,fE1,QjA,DjA,ZjA;(function(A){var B=typeof global==="object"?global:typeof self==="object"?self:typeof this==="object"?this:{};if(typeof define==="function"&&define.amd)define("tslib",["exports"],function(D){A(Q(B,Q(D)))});else if(typeof hE1==="object"&&typeof NI5==="object")A(Q(B,Q(NI5)));else A(Q(B));function Q(D,Z){if(D!==B)if(typeof Object.create==="function")Object.defineProperty(D,"__esModule",{value:!0});else D.__esModule=!0;return function(G,F){return D[G]=Z?Z(G,F):F}}})(function(A){var B=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(G,F){G.__proto__=F}||function(G,F){for(var I in F)if(Object.prototype.hasOwnProperty.call(F,I))G[I]=F[I]};ySA=function(G,F){if(typeof F!=="function"&&F!==null)throw new TypeError("Class extends value "+String(F)+" is not a constructor or null");B(G,F);function I(){this.constructor=G}G.prototype=F===null?Object.create(F):(I.prototype=F.prototype,new I)},kSA=Object.assign||function(G){for(var F,I=1,Y=arguments.length;I<Y;I++){F=arguments[I];for(var W in F)if(Object.prototype.hasOwnProperty.call(F,W))G[W]=F[W]}return G},_SA=function(G,F){var I={};for(var Y in G)if(Object.prototype.hasOwnProperty.call(G,Y)&&F.indexOf(Y)<0)I[Y]=G[Y];if(G!=null&&typeof Object.getOwnPropertySymbols==="function"){for(var W=0,Y=Object.getOwnPropertySymbols(G);W<Y.length;W++)if(F.indexOf(Y[W])<0&&Object.prototype.propertyIsEnumerable.call(G,Y[W]))I[Y[W]]=G[Y[W]]}return I},xSA=function(G,F,I,Y){var W=arguments.length,J=W<3?F:Y===null?Y=Object.getOwnPropertyDescriptor(F,I):Y,X;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")J=Reflect.decorate(G,F,I,Y);else for(var V=G.length-1;V>=0;V--)if(X=G[V])J=(W<3?X(J):W>3?X(F,I,J):X(F,I))||J;return W>3&&J&&Object.defineProperty(F,I,J),J},vSA=function(G,F){return function(I,Y){F(I,Y,G)}},bSA=function(G,F,I,Y,W,J){function X(T){if(T!==void 0&&typeof T!=="function")throw new TypeError("Function expected");return T}var V=Y.kind,C=V==="getter"?"get":V==="setter"?"set":"value",K=!F&&G?Y.static?G:G.prototype:null,H=F||(K?Object.getOwnPropertyDescriptor(K,Y.name):{}),z,$=!1;for(var L=I.length-1;L>=0;L--){var N={};for(var O in Y)N[O]=O==="access"?{}:Y[O];for(var O in Y.access)N.access[O]=Y.access[O];N.addInitializer=function(T){if($)throw new TypeError("Cannot add initializers after decoration has completed");J.push(X(T||null))};var R=I[L](V==="accessor"?{get:H.get,set:H.set}:H[C],N);if(V==="accessor"){if(R===void 0)continue;if(R===null||typeof R!=="object")throw new TypeError("Object expected");if(z=X(R.get))H.get=z;if(z=X(R.set))H.set=z;if(z=X(R.init))W.unshift(z)}else if(z=X(R))if(V==="field")W.unshift(z);else H[C]=z}if(K)Object.defineProperty(K,Y.name,H);$=!0},fSA=function(G,F,I){var Y=arguments.length>2;for(var W=0;W<F.length;W++)I=Y?F[W].call(G,I):F[W].call(G);return Y?I:void 0},hSA=function(G){return typeof G==="symbol"?G:"".concat(G)},gSA=function(G,F,I){if(typeof F==="symbol")F=F.description?"[".concat(F.description,"]"):"";return Object.defineProperty(G,"name",{configurable:!0,value:I?"".concat(I," ",F):F})},uSA=function(G,F){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(G,F)},mSA=function(G,F,I,Y){function W(J){return J instanceof I?J:new I(function(X){X(J)})}return new(I||(I=Promise))(function(J,X){function V(H){try{K(Y.next(H))}catch(z){X(z)}}function C(H){try{K(Y.throw(H))}catch(z){X(z)}}function K(H){H.done?J(H.value):W(H.value).then(V,C)}K((Y=Y.apply(G,F||[])).next())})},dSA=function(G,F){var I={label:0,sent:function(){if(J[0]&1)throw J[1];return J[1]},trys:[],ops:[]},Y,W,J,X=Object.create((typeof Iterator==="function"?Iterator:Object).prototype);return X.next=V(0),X.throw=V(1),X.return=V(2),typeof Symbol==="function"&&(X[Symbol.iterator]=function(){return this}),X;function V(K){return function(H){return C([K,H])}}function C(K){if(Y)throw new TypeError("Generator is already executing.");while(X&&(X=0,K[0]&&(I=0)),I)try{if(Y=1,W&&(J=K[0]&2?W.return:K[0]?W.throw||((J=W.return)&&J.call(W),0):W.next)&&!(J=J.call(W,K[1])).done)return J;if(W=0,J)K=[K[0]&2,J.value];switch(K[0]){case 0:case 1:J=K;break;case 4:return I.label++,{value:K[1],done:!1};case 5:I.label++,W=K[1],K=[0];continue;case 7:K=I.ops.pop(),I.trys.pop();continue;default:if((J=I.trys,!(J=J.length>0&&J[J.length-1]))&&(K[0]===6||K[0]===2)){I=0;continue}if(K[0]===3&&(!J||K[1]>J[0]&&K[1]<J[3])){I.label=K[1];break}if(K[0]===6&&I.label<J[1]){I.label=J[1],J=K;break}if(J&&I.label<J[2]){I.label=J[2],I.ops.push(K);break}if(J[2])I.ops.pop();I.trys.pop();continue}K=F.call(G,I)}catch(H){K=[6,H],W=0}finally{Y=J=0}if(K[0]&5)throw K[1];return{value:K[0]?K[1]:void 0,done:!0}}},cSA=function(G,F){for(var I in G)if(I!=="default"&&!Object.prototype.hasOwnProperty.call(F,I))fE1(F,G,I)},fE1=Object.create?function(G,F,I,Y){if(Y===void 0)Y=I;var W=Object.getOwnPropertyDescriptor(F,I);if(!W||("get"in W?!F.__esModule:W.writable||W.configurable))W={enumerable:!0,get:function(){return F[I]}};Object.defineProperty(G,Y,W)}:function(G,F,I,Y){if(Y===void 0)Y=I;G[Y]=F[I]},bE1=function(G){var F=typeof Symbol==="function"&&Symbol.iterator,I=F&&G[F],Y=0;if(I)return I.call(G);if(G&&typeof G.length==="number")return{next:function(){if(G&&Y>=G.length)G=void 0;return{value:G&&G[Y++],done:!G}}};throw new TypeError(F?"Object is not iterable.":"Symbol.iterator is not defined.")},e00=function(G,F){var I=typeof Symbol==="function"&&G[Symbol.iterator];if(!I)return G;var Y=I.call(G),W,J=[],X;try{while((F===void 0||F-- >0)&&!(W=Y.next()).done)J.push(W.value)}catch(V){X={error:V}}finally{try{if(W&&!W.done&&(I=Y.return))I.call(Y)}finally{if(X)throw X.error}}return J},lSA=function(){for(var G=[],F=0;F<arguments.length;F++)G=G.concat(e00(arguments[F]));return G},pSA=function(){for(var G=0,F=0,I=arguments.length;F<I;F++)G+=arguments[F].length;for(var Y=Array(G),W=0,F=0;F<I;F++)for(var J=arguments[F],X=0,V=J.length;X<V;X++,W++)Y[W]=J[X];return Y},iSA=function(G,F,I){if(I||arguments.length===2){for(var Y=0,W=F.length,J;Y<W;Y++)if(J||!(Y in F)){if(!J)J=Array.prototype.slice.call(F,0,Y);J[Y]=F[Y]}}return G.concat(J||Array.prototype.slice.call(F))},gn=function(G){return this instanceof gn?(this.v=G,this):new gn(G)},nSA=function(G,F,I){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var Y=I.apply(G,F||[]),W,J=[];return W=Object.create((typeof AsyncIterator==="function"?AsyncIterator:Object).prototype),V("next"),V("throw"),V("return",X),W[Symbol.asyncIterator]=function(){return this},W;function X(L){return function(N){return Promise.resolve(N).then(L,z)}}function V(L,N){if(Y[L]){if(W[L]=function(O){return new Promise(function(R,T){J.push([L,O,R,T])>1||C(L,O)})},N)W[L]=N(W[L])}}function C(L,N){try{K(Y[L](N))}catch(O){$(J[0][3],O)}}function K(L){L.value instanceof gn?Promise.resolve(L.value.v).then(H,z):$(J[0][2],L)}function H(L){C("next",L)}function z(L){C("throw",L)}function $(L,N){if(L(N),J.shift(),J.length)C(J[0][0],J[0][1])}},aSA=function(G){var F,I;return F={},Y("next"),Y("throw",function(W){throw W}),Y("return"),F[Symbol.iterator]=function(){return this},F;function Y(W,J){F[W]=G[W]?function(X){return(I=!I)?{value:gn(G[W](X)),done:!1}:J?J(X):X}:J}},sSA=function(G){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var F=G[Symbol.asyncIterator],I;return F?F.call(G):(G=typeof bE1==="function"?bE1(G):G[Symbol.iterator](),I={},Y("next"),Y("throw"),Y("return"),I[Symbol.asyncIterator]=function(){return this},I);function Y(J){I[J]=G[J]&&function(X){return new Promise(function(V,C){X=G[J](X),W(V,C,X.done,X.value)})}}function W(J,X,V,C){Promise.resolve(C).then(function(K){J({value:K,done:V})},X)}},rSA=function(G,F){if(Object.defineProperty)Object.defineProperty(G,"raw",{value:F});else G.raw=F;return G};var Q=Object.create?function(G,F){Object.defineProperty(G,"default",{enumerable:!0,value:F})}:function(G,F){G.default=F},D=function(G){return D=Object.getOwnPropertyNames||function(F){var I=[];for(var Y in F)if(Object.prototype.hasOwnProperty.call(F,Y))I[I.length]=Y;return I},D(G)};oSA=function(G){if(G&&G.__esModule)return G;var F={};if(G!=null){for(var I=D(G),Y=0;Y<I.length;Y++)if(I[Y]!=="default")fE1(F,G,I[Y])}return Q(F,G),F},tSA=function(G){return G&&G.__esModule?G:{default:G}},eSA=function(G,F,I,Y){if(I==="a"&&!Y)throw new TypeError("Private accessor was defined without a getter");if(typeof F==="function"?G!==F||!Y:!F.has(G))throw new TypeError("Cannot read private member from an object whose class did not declare it");return I==="m"?Y:I==="a"?Y.call(G):Y?Y.value:F.get(G)},AjA=function(G,F,I,Y,W){if(Y==="m")throw new TypeError("Private method is not writable");if(Y==="a"&&!W)throw new TypeError("Private accessor was defined without a setter");if(typeof F==="function"?G!==F||!W:!F.has(G))throw new TypeError("Cannot write private member to an object whose class did not declare it");return Y==="a"?W.call(G,I):W?W.value=I:F.set(G,I),I},BjA=function(G,F){if(F===null||typeof F!=="object"&&typeof F!=="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof G==="function"?F===G:G.has(F)},QjA=function(G,F,I){if(F!==null&&F!==void 0){if(typeof F!=="object"&&typeof F!=="function")throw new TypeError("Object expected.");var Y,W;if(I){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");Y=F[Symbol.asyncDispose]}if(Y===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");if(Y=F[Symbol.dispose],I)W=Y}if(typeof Y!=="function")throw new TypeError("Object not disposable.");if(W)Y=function(){try{W.call(this)}catch(J){return Promise.reject(J)}};G.stack.push({value:F,dispose:Y,async:I})}else if(I)G.stack.push({async:!0});return F};var Z=typeof SuppressedError==="function"?SuppressedError:function(G,F,I){var Y=new Error(I);return Y.name="SuppressedError",Y.error=G,Y.suppressed=F,Y};DjA=function(G){function F(J){G.error=G.hasError?new Z(J,G.error,"An error was suppressed during disposal."):J,G.hasError=!0}var I,Y=0;function W(){while(I=G.stack.pop())try{if(!I.async&&Y===1)return Y=0,G.stack.push(I),Promise.resolve().then(W);if(I.dispose){var J=I.dispose.call(I.value);if(I.async)return Y|=2,Promise.resolve(J).then(W,function(X){return F(X),W()})}else Y|=1}catch(X){F(X)}if(Y===1)return G.hasError?Promise.reject(G.error):Promise.resolve();if(G.hasError)throw G.error}return W()},ZjA=function(G,F){if(typeof G==="string"&&/^\.\.?\//.test(G))return G.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,function(I,Y,W,J,X){return Y?F?".jsx":".js":W&&(!J||!X)?I:W+J+"."+X.toLowerCase()+"js"});return G},A("__extends",ySA),A("__assign",kSA),A("__rest",_SA),A("__decorate",xSA),A("__param",vSA),A("__esDecorate",bSA),A("__runInitializers",fSA),A("__propKey",hSA),A("__setFunctionName",gSA),A("__metadata",uSA),A("__awaiter",mSA),A("__generator",dSA),A("__exportStar",cSA),A("__createBinding",fE1),A("__values",bE1),A("__read",e00),A("__spread",lSA),A("__spreadArrays",pSA),A("__spreadArray",iSA),A("__await",gn),A("__asyncGenerator",nSA),A("__asyncDelegator",aSA),A("__asyncValues",sSA),A("__makeTemplateObject",rSA),A("__importStar",oSA),A("__importDefault",tSA),A("__classPrivateFieldGet",eSA),A("__classPrivateFieldSet",AjA),A("__classPrivateFieldIn",BjA),A("__addDisposableResource",QjA),A("__disposeResources",DjA),A("__rewriteRelativeImportExtension",ZjA)})});
var vaA=E((ow1)=>{Object.defineProperty(ow1,"__esModule",{value:!0});ow1.STSClient=ow1.AssumeRoleCommand=void 0;var xaA=yw1();Object.defineProperty(ow1,"AssumeRoleCommand",{enumerable:!0,get:function(){return xaA.AssumeRoleCommand}});Object.defineProperty(ow1,"STSClient",{enumerable:!0,get:function(){return xaA.STSClient}})});
var w8=E((YJ5,PB0)=>{var{defineProperty:rU1,getOwnPropertyDescriptor:fnQ,getOwnPropertyNames:hnQ}=Object,gnQ=Object.prototype.hasOwnProperty,U8=(A,B)=>rU1(A,"name",{value:B,configurable:!0}),unQ=(A,B)=>{for(var Q in B)rU1(A,Q,{get:B[Q],enumerable:!0})},LB0=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of hnQ(B))if(!gnQ.call(A,Z)&&Z!==Q)rU1(A,Z,{get:()=>B[Z],enumerable:!(D=fnQ(B,Z))||D.enumerable})}return A},mnQ=(A,B,Q)=>(LB0(A,B,"default"),Q&&LB0(Q,B,"default")),dnQ=(A)=>LB0(rU1({},"__esModule",{value:!0}),A),OB0={};unQ(OB0,{Client:()=>cnQ,Command:()=>ruA,NoOpLogger:()=>FaQ,SENSITIVE_STRING:()=>pnQ,ServiceException:()=>nnQ,_json:()=>RB0,collectBody:()=>NB0.collectBody,convertMap:()=>IaQ,createAggregatedClient:()=>inQ,decorateServiceException:()=>ouA,emitWarningIfUnsupportedVersion:()=>onQ,extendedEncodeURIComponent:()=>NB0.extendedEncodeURIComponent,getArrayIfSingleItem:()=>ZaQ,getDefaultClientConfiguration:()=>QaQ,getDefaultExtensionConfiguration:()=>euA,getValueFromTextNode:()=>AmA,isSerializableHeaderValue:()=>GaQ,loadConfigsForDefaultMode:()=>rnQ,map:()=>TB0,resolveDefaultRuntimeConfig:()=>DaQ,resolvedPath:()=>NB0.resolvedPath,serializeDateTime:()=>CaQ,serializeFloat:()=>VaQ,take:()=>YaQ,throwDefaultError:()=>tuA,withBaseException:()=>anQ});PB0.exports=dnQ(OB0);var suA=Mw(),cnQ=class{constructor(A){this.config=A,this.middlewareStack=suA.constructStack()}static{U8(this,"Client")}send(A,B,Q){let D=typeof B!=="function"?B:void 0,Z=typeof B==="function"?B:Q,G=D===void 0&&this.config.cacheMiddleware===!0,F;if(G){if(!this.handlers)this.handlers=new WeakMap;let I=this.handlers;if(I.has(A.constructor))F=I.get(A.constructor);else F=A.resolveMiddleware(this.middlewareStack,this.config,D),I.set(A.constructor,F)}else delete this.handlers,F=A.resolveMiddleware(this.middlewareStack,this.config,D);if(Z)F(A).then((I)=>Z(null,I.output),(I)=>Z(I)).catch(()=>{});else return F(A).then((I)=>I.output)}destroy(){this.config?.requestHandler?.destroy?.(),delete this.handlers}},NB0=M6(),MB0=qB0(),ruA=class{constructor(){this.middlewareStack=suA.constructStack()}static{U8(this,"Command")}static classBuilder(){return new lnQ}resolveMiddlewareWithContext(A,B,Q,{middlewareFn:D,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,smithyContext:Y,additionalContext:W,CommandCtor:J}){for(let H of D.bind(this)(J,A,B,Q))this.middlewareStack.use(H);let X=A.concat(this.middlewareStack),{logger:V}=B,C={logger:V,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,[MB0.SMITHY_CONTEXT_KEY]:{commandInstance:this,...Y},...W},{requestHandler:K}=B;return X.resolve((H)=>K.handle(H.request,Q||{}),C)}},lnQ=class{constructor(){this._init=()=>{},this._ep={},this._middlewareFn=()=>[],this._commandName="",this._clientName="",this._additionalContext={},this._smithyContext={},this._inputFilterSensitiveLog=(A)=>A,this._outputFilterSensitiveLog=(A)=>A,this._serializer=null,this._deserializer=null}static{U8(this,"ClassBuilder")}init(A){this._init=A}ep(A){return this._ep=A,this}m(A){return this._middlewareFn=A,this}s(A,B,Q={}){return this._smithyContext={service:A,operation:B,...Q},this}c(A={}){return this._additionalContext=A,this}n(A,B){return this._clientName=A,this._commandName=B,this}f(A=(Q)=>Q,B=(Q)=>Q){return this._inputFilterSensitiveLog=A,this._outputFilterSensitiveLog=B,this}ser(A){return this._serializer=A,this}de(A){return this._deserializer=A,this}sc(A){return this._operationSchema=A,this._smithyContext.operationSchema=A,this}build(){let A=this,B;return B=class extends ruA{constructor(...[Q]){super();this.serialize=A._serializer,this.deserialize=A._deserializer,this.input=Q??{},A._init(this),this.schema=A._operationSchema}static{U8(this,"CommandRef")}static getEndpointParameterInstructions(){return A._ep}resolveMiddleware(Q,D,Z){return this.resolveMiddlewareWithContext(Q,D,Z,{CommandCtor:B,middlewareFn:A._middlewareFn,clientName:A._clientName,commandName:A._commandName,inputFilterSensitiveLog:A._inputFilterSensitiveLog,outputFilterSensitiveLog:A._outputFilterSensitiveLog,smithyContext:A._smithyContext,additionalContext:A._additionalContext})}}}},pnQ="***SensitiveInformation***",inQ=U8((A,B)=>{for(let Q of Object.keys(A)){let D=A[Q],Z=U8(async function(F,I,Y){let W=new D(F);if(typeof I==="function")this.send(W,I);else if(typeof Y==="function"){if(typeof I!=="object")throw new Error(`Expected http options but got ${typeof I}`);this.send(W,I||{},Y)}else return this.send(W,I)},"methodImpl"),G=(Q[0].toLowerCase()+Q.slice(1)).replace(/Command$/,"");B.prototype[G]=Z}},"createAggregatedClient"),nnQ=class A extends Error{static{U8(this,"ServiceException")}constructor(B){super(B.message);Object.setPrototypeOf(this,Object.getPrototypeOf(this).constructor.prototype),this.name=B.name,this.$fault=B.$fault,this.$metadata=B.$metadata}static isInstance(B){if(!B)return!1;let Q=B;return A.prototype.isPrototypeOf(Q)||Boolean(Q.$fault)&&Boolean(Q.$metadata)&&(Q.$fault==="client"||Q.$fault==="server")}static[Symbol.hasInstance](B){if(!B)return!1;let Q=B;if(this===A)return A.isInstance(B);if(A.isInstance(B)){if(Q.name&&this.name)return this.prototype.isPrototypeOf(B)||Q.name===this.name;return this.prototype.isPrototypeOf(B)}return!1}},ouA=U8((A,B={})=>{Object.entries(B).filter(([,D])=>D!==void 0).forEach(([D,Z])=>{if(A[D]==null||A[D]==="")A[D]=Z});let Q=A.message||A.Message||"UnknownError";return A.message=Q,delete A.Message,A},"decorateServiceException"),tuA=U8(({output:A,parsedBody:B,exceptionCtor:Q,errorCode:D})=>{let Z=snQ(A),G=Z.httpStatusCode?Z.httpStatusCode+"":void 0,F=new Q({name:B?.code||B?.Code||D||G||"UnknownError",$fault:"client",$metadata:Z});throw ouA(F,B)},"throwDefaultError"),anQ=U8((A)=>{return({output:B,parsedBody:Q,errorCode:D})=>{tuA({output:B,parsedBody:Q,exceptionCtor:A,errorCode:D})}},"withBaseException"),snQ=U8((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),rnQ=U8((A)=>{switch(A){case"standard":return{retryMode:"standard",connectionTimeout:3100};case"in-region":return{retryMode:"standard",connectionTimeout:1100};case"cross-region":return{retryMode:"standard",connectionTimeout:3100};case"mobile":return{retryMode:"standard",connectionTimeout:30000};default:return{}}},"loadConfigsForDefaultMode"),auA=!1,onQ=U8((A)=>{if(A&&!auA&&parseInt(A.substring(1,A.indexOf(".")))<16)auA=!0},"emitWarningIfUnsupportedVersion"),tnQ=U8((A)=>{let B=[];for(let Q in MB0.AlgorithmId){let D=MB0.AlgorithmId[Q];if(A[D]===void 0)continue;B.push({algorithmId:()=>D,checksumConstructor:()=>A[D]})}return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),enQ=U8((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),AaQ=U8((A)=>{return{setRetryStrategy(B){A.retryStrategy=B},retryStrategy(){return A.retryStrategy}}},"getRetryConfiguration"),BaQ=U8((A)=>{let B={};return B.retryStrategy=A.retryStrategy(),B},"resolveRetryRuntimeConfig"),euA=U8((A)=>{return Object.assign(tnQ(A),AaQ(A))},"getDefaultExtensionConfiguration"),QaQ=euA,DaQ=U8((A)=>{return Object.assign(enQ(A),BaQ(A))},"resolveDefaultRuntimeConfig"),ZaQ=U8((A)=>Array.isArray(A)?A:[A],"getArrayIfSingleItem"),AmA=U8((A)=>{for(let Q in A)if(A.hasOwnProperty(Q)&&A[Q]["#text"]!==void 0)A[Q]=A[Q]["#text"];else if(typeof A[Q]==="object"&&A[Q]!==null)A[Q]=AmA(A[Q]);return A},"getValueFromTextNode"),GaQ=U8((A)=>{return A!=null},"isSerializableHeaderValue"),FaQ=class{static{U8(this,"NoOpLogger")}trace(){}debug(){}info(){}warn(){}error(){}};function TB0(A,B,Q){let D,Z,G;if(typeof B==="undefined"&&typeof Q==="undefined")D={},G=A;else if(D=A,typeof B==="function")return Z=B,G=Q,WaQ(D,Z,G);else G=B;for(let F of Object.keys(G)){if(!Array.isArray(G[F])){D[F]=G[F];continue}BmA(D,null,G,F)}return D}U8(TB0,"map");var IaQ=U8((A)=>{let B={};for(let[Q,D]of Object.entries(A||{}))B[Q]=[,D];return B},"convertMap"),YaQ=U8((A,B)=>{let Q={};for(let D in B)BmA(Q,A,B,D);return Q},"take"),WaQ=U8((A,B,Q)=>{return TB0(A,Object.entries(Q).reduce((D,[Z,G])=>{if(Array.isArray(G))D[Z]=G;else if(typeof G==="function")D[Z]=[B,G()];else D[Z]=[B,G];return D},{}))},"mapWithFilter"),BmA=U8((A,B,Q,D)=>{if(B!==null){let F=Q[D];if(typeof F==="function")F=[,F];let[I=JaQ,Y=XaQ,W=D]=F;if(typeof I==="function"&&I(B[W])||typeof I!=="function"&&!!I)A[D]=Y(B[W]);return}let[Z,G]=Q[D];if(typeof G==="function"){let F,I=Z===void 0&&(F=G())!=null,Y=typeof Z==="function"&&!!Z(void 0)||typeof Z!=="function"&&!!Z;if(I)A[D]=F;else if(Y)A[D]=G()}else{let F=Z===void 0&&G!=null,I=typeof Z==="function"&&!!Z(G)||typeof Z!=="function"&&!!Z;if(F||I)A[D]=G}},"applyInstruction"),JaQ=U8((A)=>A!=null,"nonNullish"),XaQ=U8((A)=>A,"pass"),VaQ=U8((A)=>{if(A!==A)return"NaN";switch(A){case 1/0:return"Infinity";case-1/0:return"-Infinity";default:return A}},"serializeFloat"),CaQ=U8((A)=>A.toISOString().replace(".000Z","Z"),"serializeDateTime"),RB0=U8((A)=>{if(A==null)return{};if(Array.isArray(A))return A.filter((B)=>B!=null).map(RB0);if(typeof A==="object"){let B={};for(let Q of Object.keys(A)){if(A[Q]==null)continue;B[Q]=RB0(A[Q])}return B}return A},"_json");mnQ(OB0,X6(),PB0.exports)});
var wA0=E((mI5,zkA)=>{var{defineProperty:YU1,getOwnPropertyDescriptor:_vQ,getOwnPropertyNames:xvQ}=Object,vvQ=Object.prototype.hasOwnProperty,oZ=(A,B)=>YU1(A,"name",{value:B,configurable:!0}),bvQ=(A,B)=>{for(var Q in B)YU1(A,Q,{get:B[Q],enumerable:!0})},fvQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of xvQ(B))if(!vvQ.call(A,Z)&&Z!==Q)YU1(A,Z,{get:()=>B[Z],enumerable:!(D=_vQ(B,Z))||D.enumerable})}return A},hvQ=(A)=>fvQ(YU1({},"__esModule",{value:!0}),A),XkA={};bvQ(XkA,{AWSSDKSigV4Signer:()=>dvQ,AwsSdkSigV4ASigner:()=>lvQ,AwsSdkSigV4Signer:()=>UA0,NODE_AUTH_SCHEME_PREFERENCE_OPTIONS:()=>pvQ,NODE_SIGV4A_CONFIG_OPTIONS:()=>avQ,getBearerTokenEnvKey:()=>VkA,resolveAWSSDKSigV4Config:()=>rvQ,resolveAwsSdkSigV4AConfig:()=>nvQ,resolveAwsSdkSigV4Config:()=>CkA,validateSigningProperties:()=>EA0});zkA.exports=hvQ(XkA);var gvQ=EV(),uvQ=EV(),ZkA=oZ((A)=>uvQ.HttpResponse.isInstance(A)?A.headers?.date??A.headers?.Date:void 0,"getDateHeader"),zA0=oZ((A)=>new Date(Date.now()+A),"getSkewCorrectedDate"),mvQ=oZ((A,B)=>Math.abs(zA0(B).getTime()-A)>=300000,"isClockSkewed"),GkA=oZ((A,B)=>{let Q=Date.parse(A);if(mvQ(Q,B))return Q-Date.now();return B},"getUpdatedSystemClockOffset"),iQ1=oZ((A,B)=>{if(!B)throw new Error(`Property \`${A}\` is not resolved for AWS SDK SigV4Auth`);return B},"throwSigningPropertyError"),EA0=oZ(async(A)=>{let B=iQ1("context",A.context),Q=iQ1("config",A.config),D=B.endpointV2?.properties?.authSchemes?.[0],G=await iQ1("signer",Q.signer)(D),F=A?.signingRegion,I=A?.signingRegionSet,Y=A?.signingName;return{config:Q,signer:G,signingRegion:F,signingRegionSet:I,signingName:Y}},"validateSigningProperties"),UA0=class{static{oZ(this,"AwsSdkSigV4Signer")}async sign(A,B,Q){if(!gvQ.HttpRequest.isInstance(A))throw new Error("The request is not an instance of `HttpRequest` and cannot be signed");let D=await EA0(Q),{config:Z,signer:G}=D,{signingRegion:F,signingName:I}=D,Y=Q.context;if(Y?.authSchemes?.length??0>1){let[J,X]=Y.authSchemes;if(J?.name==="sigv4a"&&X?.name==="sigv4")F=X?.signingRegion??F,I=X?.signingName??I}return await G.sign(A,{signingDate:zA0(Z.systemClockOffset),signingRegion:F,signingService:I})}errorHandler(A){return(B)=>{let Q=B.ServerTime??ZkA(B.$response);if(Q){let D=iQ1("config",A.config),Z=D.systemClockOffset;if(D.systemClockOffset=GkA(Q,D.systemClockOffset),D.systemClockOffset!==Z&&B.$metadata)B.$metadata.clockSkewCorrected=!0}throw B}}successHandler(A,B){let Q=ZkA(A);if(Q){let D=iQ1("config",B.config);D.systemClockOffset=GkA(Q,D.systemClockOffset)}}},dvQ=UA0,cvQ=EV(),lvQ=class extends UA0{static{oZ(this,"AwsSdkSigV4ASigner")}async sign(A,B,Q){if(!cvQ.HttpRequest.isInstance(A))throw new Error("The request is not an instance of `HttpRequest` and cannot be signed");let{config:D,signer:Z,signingRegion:G,signingRegionSet:F,signingName:I}=await EA0(Q),W=(await D.sigv4aSigningRegionSet?.()??F??[G]).join(",");return await Z.sign(A,{signingDate:zA0(D.systemClockOffset),signingRegion:W,signingService:I})}},FkA=oZ((A)=>typeof A==="string"&&A.length>0?A.split(",").map((B)=>B.trim()):[],"getArrayForCommaSeparatedString"),VkA=oZ((A)=>`AWS_BEARER_TOKEN_${A.replace(/[\s-]/g,"_").toUpperCase()}`,"getBearerTokenEnvKey"),IkA="AWS_AUTH_SCHEME_PREFERENCE",YkA="auth_scheme_preference",pvQ={environmentVariableSelector:oZ((A,B)=>{if(B?.signingName){if(VkA(B.signingName)in A)return["httpBearerAuth"]}if(!(IkA in A))return;return FkA(A[IkA])},"environmentVariableSelector"),configFileSelector:oZ((A)=>{if(!(YkA in A))return;return FkA(A[YkA])},"configFileSelector"),default:[]},ivQ=CB(),WkA=Q9(),nvQ=oZ((A)=>{return A.sigv4aSigningRegionSet=ivQ.normalizeProvider(A.sigv4aSigningRegionSet),A},"resolveAwsSdkSigV4AConfig"),avQ={environmentVariableSelector(A){if(A.AWS_SIGV4A_SIGNING_REGION_SET)return A.AWS_SIGV4A_SIGNING_REGION_SET.split(",").map((B)=>B.trim());throw new WkA.ProviderError("AWS_SIGV4A_SIGNING_REGION_SET not set in env.",{tryNextLink:!0})},configFileSelector(A){if(A.sigv4a_signing_region_set)return(A.sigv4a_signing_region_set??"").split(",").map((B)=>B.trim());throw new WkA.ProviderError("sigv4a_signing_region_set not set in profile.",{tryNextLink:!0})},default:void 0},svQ=Sw(),dh=CB(),JkA=DkA(),CkA=oZ((A)=>{let B=A.credentials,Q=!!A.credentials,D=void 0;Object.defineProperty(A,"credentials",{set(W){if(W&&W!==B&&W!==D)Q=!0;B=W;let J=KkA(A,{credentials:B,credentialDefaultProvider:A.credentialDefaultProvider}),X=HkA(A,J);if(Q&&!X.attributed)D=oZ(async(V)=>X(V).then((C)=>svQ.setCredentialFeature(C,"CREDENTIALS_CODE","e")),"resolvedCredentials"),D.memoized=X.memoized,D.configBound=X.configBound,D.attributed=!0;else D=X},get(){return D},enumerable:!0,configurable:!0}),A.credentials=B;let{signingEscapePath:Z=!0,systemClockOffset:G=A.systemClockOffset||0,sha256:F}=A,I;if(A.signer)I=dh.normalizeProvider(A.signer);else if(A.regionInfoProvider)I=oZ(()=>dh.normalizeProvider(A.region)().then(async(W)=>[await A.regionInfoProvider(W,{useFipsEndpoint:await A.useFipsEndpoint(),useDualstackEndpoint:await A.useDualstackEndpoint()})||{},W]).then(([W,J])=>{let{signingRegion:X,signingService:V}=W;A.signingRegion=A.signingRegion||X||J,A.signingName=A.signingName||V||A.serviceId;let C={...A,credentials:A.credentials,region:A.signingRegion,service:A.signingName,sha256:F,uriEscapePath:Z};return new(A.signerConstructor||JkA.SignatureV4)(C)}),"signer");else I=oZ(async(W)=>{W=Object.assign({},{name:"sigv4",signingName:A.signingName||A.defaultSigningName,signingRegion:await dh.normalizeProvider(A.region)(),properties:{}},W);let{signingRegion:J,signingName:X}=W;A.signingRegion=A.signingRegion||J,A.signingName=A.signingName||X||A.serviceId;let V={...A,credentials:A.credentials,region:A.signingRegion,service:A.signingName,sha256:F,uriEscapePath:Z};return new(A.signerConstructor||JkA.SignatureV4)(V)},"signer");return Object.assign(A,{systemClockOffset:G,signingEscapePath:Z,signer:I})},"resolveAwsSdkSigV4Config"),rvQ=CkA;function KkA(A,{credentials:B,credentialDefaultProvider:Q}){let D;if(B)if(!B?.memoized)D=dh.memoizeIdentityProvider(B,dh.isIdentityExpired,dh.doesIdentityRequireRefresh);else D=B;else if(Q)D=dh.normalizeProvider(Q(Object.assign({},A,{parentClientConfig:A})));else D=oZ(async()=>{throw new Error("@aws-sdk/core::resolveAwsSdkSigV4Config - `credentials` not provided and no credentialDefaultProvider was configured.")},"credentialsProvider");return D.memoized=!0,D}oZ(KkA,"normalizeCredentialProvider");function HkA(A,B){if(B.configBound)return B;let Q=oZ(async(D)=>B({...D,callerClientConfig:A}),"fn");return Q.memoized=B.memoized,Q.configBound=!0,Q}oZ(HkA,"bindCallerConfig")});
var wB0=E((sW5,zuA)=>{var{defineProperty:cU1,getOwnPropertyDescriptor:diQ,getOwnPropertyNames:FuA}=Object,ciQ=Object.prototype.hasOwnProperty,IK=(A,B)=>cU1(A,"name",{value:B,configurable:!0}),liQ=(A,B)=>function Q(){return A&&(B=A[FuA(A)[0]](A=0)),B},IuA=(A,B)=>{for(var Q in B)cU1(A,Q,{get:B[Q],enumerable:!0})},piQ=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of FuA(B))if(!ciQ.call(A,Z)&&Z!==Q)cU1(A,Z,{get:()=>B[Z],enumerable:!(D=diQ(B,Z))||D.enumerable})}return A},iiQ=(A)=>piQ(cU1({},"__esModule",{value:!0}),A),zB0={};IuA(zB0,{CognitoIdentityClient:()=>dU1.CognitoIdentityClient,GetCredentialsForIdentityCommand:()=>dU1.GetCredentialsForIdentityCommand,GetIdCommand:()=>dU1.GetIdCommand});var dU1,YuA=liQ({"src/loadCognitoIdentity.ts"(){dU1=GuA()}}),WuA={};IuA(WuA,{fromCognitoIdentity:()=>UB0,fromCognitoIdentityPool:()=>KuA});zuA.exports=iiQ(WuA);var lU1=Q9();function EB0(A){return Promise.all(Object.keys(A).reduce((B,Q)=>{let D=A[Q];if(typeof D==="string")B.push([Q,D]);else B.push(D().then((Z)=>[Q,Z]));return B},[])).then((B)=>B.reduce((Q,[D,Z])=>{return Q[D]=Z,Q},{}))}IK(EB0,"resolveLogins");function UB0(A){return async(B)=>{A.logger?.debug("@aws-sdk/credential-provider-cognito-identity - fromCognitoIdentity");let{GetCredentialsForIdentityCommand:Q,CognitoIdentityClient:D}=await Promise.resolve().then(()=>(YuA(),zB0)),Z=IK((W)=>A.clientConfig?.[W]??A.parentClientConfig?.[W]??B?.callerClientConfig?.[W],"fromConfigs"),{Credentials:{AccessKeyId:G=JuA(A.logger),Expiration:F,SecretKey:I=VuA(A.logger),SessionToken:Y}=XuA(A.logger)}=await(A.client??new D(Object.assign({},A.clientConfig??{},{region:Z("region"),profile:Z("profile")}))).send(new Q({CustomRoleArn:A.customRoleArn,IdentityId:A.identityId,Logins:A.logins?await EB0(A.logins):void 0}));return{identityId:A.identityId,accessKeyId:G,secretAccessKey:I,sessionToken:Y,expiration:F}}}IK(UB0,"fromCognitoIdentity");function JuA(A){throw new lU1.CredentialsProviderError("Response from Amazon Cognito contained no access key ID",{logger:A})}IK(JuA,"throwOnMissingAccessKeyId");function XuA(A){throw new lU1.CredentialsProviderError("Response from Amazon Cognito contained no credentials",{logger:A})}IK(XuA,"throwOnMissingCredentials");function VuA(A){throw new lU1.CredentialsProviderError("Response from Amazon Cognito contained no secret key",{logger:A})}IK(VuA,"throwOnMissingSecretKey");var HB0="IdentityIds",niQ=class{constructor(A="aws:cognito-identity-ids"){this.dbName=A}static{IK(this,"IndexedDbStorage")}getItem(A){return this.withObjectStore("readonly",(B)=>{let Q=B.get(A);return new Promise((D)=>{Q.onerror=()=>D(null),Q.onsuccess=()=>D(Q.result?Q.result.value:null)})}).catch(()=>null)}removeItem(A){return this.withObjectStore("readwrite",(B)=>{let Q=B.delete(A);return new Promise((D,Z)=>{Q.onerror=()=>Z(Q.error),Q.onsuccess=()=>D()})})}setItem(A,B){return this.withObjectStore("readwrite",(Q)=>{let D=Q.put({id:A,value:B});return new Promise((Z,G)=>{D.onerror=()=>G(D.error),D.onsuccess=()=>Z()})})}getDb(){let A=self.indexedDB.open(this.dbName,1);return new Promise((B,Q)=>{A.onsuccess=()=>{B(A.result)},A.onerror=()=>{Q(A.error)},A.onblocked=()=>{Q(new Error("Unable to access DB"))},A.onupgradeneeded=()=>{let D=A.result;D.onerror=()=>{Q(new Error("Failed to create object store"))},D.createObjectStore(HB0,{keyPath:"id"})}})}withObjectStore(A,B){return this.getDb().then((Q)=>{let D=Q.transaction(HB0,A);return D.oncomplete=()=>Q.close(),new Promise((Z,G)=>{D.onerror=()=>G(D.error),Z(B(D.objectStore(HB0)))}).catch((Z)=>{throw Q.close(),Z})})}},aiQ=class{constructor(A={}){this.store=A}static{IK(this,"InMemoryStorage")}getItem(A){if(A in this.store)return this.store[A];return null}removeItem(A){delete this.store[A]}setItem(A,B){this.store[A]=B}},siQ=new aiQ;function CuA(){if(typeof self==="object"&&self.indexedDB)return new niQ;if(typeof window==="object"&&window.localStorage)return window.localStorage;return siQ}IK(CuA,"localStorage");function KuA({accountId:A,cache:B=CuA(),client:Q,clientConfig:D,customRoleArn:Z,identityPoolId:G,logins:F,userIdentifier:I=!F||Object.keys(F).length===0?"ANONYMOUS":void 0,logger:Y,parentClientConfig:W}){Y?.debug("@aws-sdk/credential-provider-cognito-identity - fromCognitoIdentity");let J=I?`aws:cognito-identity-credentials:${G}:${I}`:void 0,X=IK(async(V)=>{let{GetIdCommand:C,CognitoIdentityClient:K}=await Promise.resolve().then(()=>(YuA(),zB0)),H=IK((L)=>D?.[L]??W?.[L]??V?.callerClientConfig?.[L],"fromConfigs"),z=Q??new K(Object.assign({},D??{},{region:H("region"),profile:H("profile")})),$=J&&await B.getItem(J);if(!$){let{IdentityId:L=HuA(Y)}=await z.send(new C({AccountId:A,IdentityPoolId:G,Logins:F?await EB0(F):void 0}));if($=L,J)Promise.resolve(B.setItem(J,$)).catch(()=>{})}return X=UB0({client:z,customRoleArn:Z,logins:F,identityId:$}),X(V)},"provider");return(V)=>X(V).catch(async(C)=>{if(J)Promise.resolve(B.removeItem(J)).catch(()=>{});throw C})}IK(KuA,"fromCognitoIdentityPool");function HuA(A){throw new lU1.CredentialsProviderError("Response from Amazon Cognito contained no identity ID",{logger:A})}IK(HuA,"throwOnMissingId")});
var wkA=E((EkA)=>{Object.defineProperty(EkA,"__esModule",{value:!0});EkA.fromBase64=void 0;var ovQ=YD(),tvQ=/^[A-Za-z0-9+/]*={0,2}$/,evQ=(A)=>{if(A.length*3%4!==0)throw new TypeError("Incorrect padding on base64 string.");if(!tvQ.exec(A))throw new TypeError("Invalid base64 string.");let B=ovQ.fromString(A,"base64");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength)};EkA.fromBase64=evQ});
var wuA=E((EuA)=>{Object.defineProperty(EuA,"__esModule",{value:!0});EuA.fromCognitoIdentity=void 0;var riQ=wB0(),oiQ=(A)=>riQ.fromCognitoIdentity({...A});EuA.fromCognitoIdentity=oiQ});
var yw1=E((KX5,n90)=>{var{defineProperty:jw1,getOwnPropertyDescriptor:ZeQ,getOwnPropertyNames:GeQ}=Object,FeQ=Object.prototype.hasOwnProperty,G9=(A,B)=>jw1(A,"name",{value:B,configurable:!0}),IeQ=(A,B)=>{for(var Q in B)jw1(A,Q,{get:B[Q],enumerable:!0})},u90=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of GeQ(B))if(!FeQ.call(A,Z)&&Z!==Q)jw1(A,Z,{get:()=>B[Z],enumerable:!(D=ZeQ(B,Z))||D.enumerable})}return A},YeQ=(A,B,Q)=>(u90(A,B,"default"),Q&&u90(Q,B,"default")),WeQ=(A)=>u90(jw1({},"__esModule",{value:!0}),A),d90={};IeQ(d90,{AssumeRoleCommand:()=>p90,AssumeRoleResponseFilterSensitiveLog:()=>clA,AssumeRoleWithWebIdentityCommand:()=>i90,AssumeRoleWithWebIdentityRequestFilterSensitiveLog:()=>rlA,AssumeRoleWithWebIdentityResponseFilterSensitiveLog:()=>olA,ClientInputEndpointParameters:()=>B14.ClientInputEndpointParameters,CredentialsFilterSensitiveLog:()=>c90,ExpiredTokenException:()=>llA,IDPCommunicationErrorException:()=>tlA,IDPRejectedClaimException:()=>alA,InvalidIdentityTokenException:()=>slA,MalformedPolicyDocumentException:()=>plA,PackedPolicyTooLargeException:()=>ilA,RegionDisabledException:()=>nlA,STS:()=>WpA,STSServiceException:()=>wT,decorateDefaultCredentialProvider:()=>Z14,getDefaultRoleAssumer:()=>HpA,getDefaultRoleAssumerWithWebIdentity:()=>zpA});n90.exports=WeQ(d90);YeQ(d90,q41(),n90.exports);var JeQ=w8(),XeQ=R6(),VeQ=j3(),CeQ=w8(),KeQ=N41(),dlA=w8(),HeQ=w8(),wT=class A extends HeQ.ServiceException{static{G9(this,"STSServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},c90=G9((A)=>({...A,...A.SecretAccessKey&&{SecretAccessKey:dlA.SENSITIVE_STRING}}),"CredentialsFilterSensitiveLog"),clA=G9((A)=>({...A,...A.Credentials&&{Credentials:c90(A.Credentials)}}),"AssumeRoleResponseFilterSensitiveLog"),llA=class A extends wT{static{G9(this,"ExpiredTokenException")}name="ExpiredTokenException";$fault="client";constructor(B){super({name:"ExpiredTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},plA=class A extends wT{static{G9(this,"MalformedPolicyDocumentException")}name="MalformedPolicyDocumentException";$fault="client";constructor(B){super({name:"MalformedPolicyDocumentException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},ilA=class A extends wT{static{G9(this,"PackedPolicyTooLargeException")}name="PackedPolicyTooLargeException";$fault="client";constructor(B){super({name:"PackedPolicyTooLargeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},nlA=class A extends wT{static{G9(this,"RegionDisabledException")}name="RegionDisabledException";$fault="client";constructor(B){super({name:"RegionDisabledException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},alA=class A extends wT{static{G9(this,"IDPRejectedClaimException")}name="IDPRejectedClaimException";$fault="client";constructor(B){super({name:"IDPRejectedClaimException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},slA=class A extends wT{static{G9(this,"InvalidIdentityTokenException")}name="InvalidIdentityTokenException";$fault="client";constructor(B){super({name:"InvalidIdentityTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},rlA=G9((A)=>({...A,...A.WebIdentityToken&&{WebIdentityToken:dlA.SENSITIVE_STRING}}),"AssumeRoleWithWebIdentityRequestFilterSensitiveLog"),olA=G9((A)=>({...A,...A.Credentials&&{Credentials:c90(A.Credentials)}}),"AssumeRoleWithWebIdentityResponseFilterSensitiveLog"),tlA=class A extends wT{static{G9(this,"IDPCommunicationErrorException")}name="IDPCommunicationErrorException";$fault="client";constructor(B){super({name:"IDPCommunicationErrorException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},l90=UV(),zeQ=YK(),C5=w8(),EeQ=G9(async(A,B)=>{let Q=ZpA,D;return D=YpA({...PeQ(A,B),[FpA]:ieQ,[IpA]:GpA}),DpA(B,Q,"/",void 0,D)},"se_AssumeRoleCommand"),UeQ=G9(async(A,B)=>{let Q=ZpA,D;return D=YpA({...SeQ(A,B),[FpA]:neQ,[IpA]:GpA}),DpA(B,Q,"/",void 0,D)},"se_AssumeRoleWithWebIdentityCommand"),weQ=G9(async(A,B)=>{if(A.statusCode>=300)return elA(A,B);let Q=await l90.parseXmlBody(A.body,B),D={};return D=beQ(Q.AssumeRoleResult,B),{$metadata:$T(A),...D}},"de_AssumeRoleCommand"),$eQ=G9(async(A,B)=>{if(A.statusCode>=300)return elA(A,B);let Q=await l90.parseXmlBody(A.body,B),D={};return D=feQ(Q.AssumeRoleWithWebIdentityResult,B),{$metadata:$T(A),...D}},"de_AssumeRoleWithWebIdentityCommand"),elA=G9(async(A,B)=>{let Q={...A,body:await l90.parseXmlErrorBody(A.body,B)},D=aeQ(A,Q.body);switch(D){case"ExpiredTokenException":case"com.amazonaws.sts#ExpiredTokenException":throw await qeQ(Q,B);case"MalformedPolicyDocument":case"com.amazonaws.sts#MalformedPolicyDocumentException":throw await ReQ(Q,B);case"PackedPolicyTooLarge":case"com.amazonaws.sts#PackedPolicyTooLargeException":throw await OeQ(Q,B);case"RegionDisabledException":case"com.amazonaws.sts#RegionDisabledException":throw await TeQ(Q,B);case"IDPCommunicationError":case"com.amazonaws.sts#IDPCommunicationErrorException":throw await NeQ(Q,B);case"IDPRejectedClaim":case"com.amazonaws.sts#IDPRejectedClaimException":throw await LeQ(Q,B);case"InvalidIdentityToken":case"com.amazonaws.sts#InvalidIdentityTokenException":throw await MeQ(Q,B);default:let Z=Q.body;return peQ({output:A,parsedBody:Z.Error,errorCode:D})}},"de_CommandError"),qeQ=G9(async(A,B)=>{let Q=A.body,D=heQ(Q.Error,B),Z=new llA({$metadata:$T(A),...D});return C5.decorateServiceException(Z,Q)},"de_ExpiredTokenExceptionRes"),NeQ=G9(async(A,B)=>{let Q=A.body,D=geQ(Q.Error,B),Z=new tlA({$metadata:$T(A),...D});return C5.decorateServiceException(Z,Q)},"de_IDPCommunicationErrorExceptionRes"),LeQ=G9(async(A,B)=>{let Q=A.body,D=ueQ(Q.Error,B),Z=new alA({$metadata:$T(A),...D});return C5.decorateServiceException(Z,Q)},"de_IDPRejectedClaimExceptionRes"),MeQ=G9(async(A,B)=>{let Q=A.body,D=meQ(Q.Error,B),Z=new slA({$metadata:$T(A),...D});return C5.decorateServiceException(Z,Q)},"de_InvalidIdentityTokenExceptionRes"),ReQ=G9(async(A,B)=>{let Q=A.body,D=deQ(Q.Error,B),Z=new plA({$metadata:$T(A),...D});return C5.decorateServiceException(Z,Q)},"de_MalformedPolicyDocumentExceptionRes"),OeQ=G9(async(A,B)=>{let Q=A.body,D=ceQ(Q.Error,B),Z=new ilA({$metadata:$T(A),...D});return C5.decorateServiceException(Z,Q)},"de_PackedPolicyTooLargeExceptionRes"),TeQ=G9(async(A,B)=>{let Q=A.body,D=leQ(Q.Error,B),Z=new nlA({$metadata:$T(A),...D});return C5.decorateServiceException(Z,Q)},"de_RegionDisabledExceptionRes"),PeQ=G9((A,B)=>{let Q={};if(A[Ta]!=null)Q[Ta]=A[Ta];if(A[Pa]!=null)Q[Pa]=A[Pa];if(A[Ra]!=null){let D=ApA(A[Ra],B);if(A[Ra]?.length===0)Q.PolicyArns=[];Object.entries(D).forEach(([Z,G])=>{let F=`PolicyArns.${Z}`;Q[F]=G})}if(A[Ma]!=null)Q[Ma]=A[Ma];if(A[La]!=null)Q[La]=A[La];if(A[x90]!=null){let D=veQ(A[x90],B);if(A[x90]?.length===0)Q.Tags=[];Object.entries(D).forEach(([Z,G])=>{let F=`Tags.${Z}`;Q[F]=G})}if(A[b90]!=null){let D=xeQ(A[b90],B);if(A[b90]?.length===0)Q.TransitiveTagKeys=[];Object.entries(D).forEach(([Z,G])=>{let F=`TransitiveTagKeys.${Z}`;Q[F]=G})}if(A[M90]!=null)Q[M90]=A[M90];if(A[k90]!=null)Q[k90]=A[k90];if(A[v90]!=null)Q[v90]=A[v90];if(A[UT]!=null)Q[UT]=A[UT];if(A[T90]!=null){let D=keQ(A[T90],B);if(A[T90]?.length===0)Q.ProvidedContexts=[];Object.entries(D).forEach(([Z,G])=>{let F=`ProvidedContexts.${Z}`;Q[F]=G})}return Q},"se_AssumeRoleRequest"),SeQ=G9((A,B)=>{let Q={};if(A[Ta]!=null)Q[Ta]=A[Ta];if(A[Pa]!=null)Q[Pa]=A[Pa];if(A[h90]!=null)Q[h90]=A[h90];if(A[P90]!=null)Q[P90]=A[P90];if(A[Ra]!=null){let D=ApA(A[Ra],B);if(A[Ra]?.length===0)Q.PolicyArns=[];Object.entries(D).forEach(([Z,G])=>{let F=`PolicyArns.${Z}`;Q[F]=G})}if(A[Ma]!=null)Q[Ma]=A[Ma];if(A[La]!=null)Q[La]=A[La];return Q},"se_AssumeRoleWithWebIdentityRequest"),ApA=G9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=jeQ(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_policyDescriptorListType"),jeQ=G9((A,B)=>{let Q={};if(A[g90]!=null)Q[g90]=A[g90];return Q},"se_PolicyDescriptorType"),yeQ=G9((A,B)=>{let Q={};if(A[O90]!=null)Q[O90]=A[O90];if(A[N90]!=null)Q[N90]=A[N90];return Q},"se_ProvidedContext"),keQ=G9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=yeQ(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_ProvidedContextsListType"),_eQ=G9((A,B)=>{let Q={};if(A[R90]!=null)Q[R90]=A[R90];if(A[f90]!=null)Q[f90]=A[f90];return Q},"se_Tag"),xeQ=G9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;Q[`member.${D}`]=Z,D++}return Q},"se_tagKeyListType"),veQ=G9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=_eQ(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_tagListType"),BpA=G9((A,B)=>{let Q={};if(A[w90]!=null)Q[w90]=C5.expectString(A[w90]);if(A[$90]!=null)Q[$90]=C5.expectString(A[$90]);return Q},"de_AssumedRoleUser"),beQ=G9((A,B)=>{let Q={};if(A[Na]!=null)Q[Na]=QpA(A[Na],B);if(A[qa]!=null)Q[qa]=BpA(A[qa],B);if(A[Oa]!=null)Q[Oa]=C5.strictParseInt32(A[Oa]);if(A[UT]!=null)Q[UT]=C5.expectString(A[UT]);return Q},"de_AssumeRoleResponse"),feQ=G9((A,B)=>{let Q={};if(A[Na]!=null)Q[Na]=QpA(A[Na],B);if(A[y90]!=null)Q[y90]=C5.expectString(A[y90]);if(A[qa]!=null)Q[qa]=BpA(A[qa],B);if(A[Oa]!=null)Q[Oa]=C5.strictParseInt32(A[Oa]);if(A[S90]!=null)Q[S90]=C5.expectString(A[S90]);if(A[q90]!=null)Q[q90]=C5.expectString(A[q90]);if(A[UT]!=null)Q[UT]=C5.expectString(A[UT]);return Q},"de_AssumeRoleWithWebIdentityResponse"),QpA=G9((A,B)=>{let Q={};if(A[U90]!=null)Q[U90]=C5.expectString(A[U90]);if(A[j90]!=null)Q[j90]=C5.expectString(A[j90]);if(A[_90]!=null)Q[_90]=C5.expectString(A[_90]);if(A[L90]!=null)Q[L90]=C5.expectNonNull(C5.parseRfc3339DateTimeWithOffset(A[L90]));return Q},"de_Credentials"),heQ=G9((A,B)=>{let Q={};if(A[BG]!=null)Q[BG]=C5.expectString(A[BG]);return Q},"de_ExpiredTokenException"),geQ=G9((A,B)=>{let Q={};if(A[BG]!=null)Q[BG]=C5.expectString(A[BG]);return Q},"de_IDPCommunicationErrorException"),ueQ=G9((A,B)=>{let Q={};if(A[BG]!=null)Q[BG]=C5.expectString(A[BG]);return Q},"de_IDPRejectedClaimException"),meQ=G9((A,B)=>{let Q={};if(A[BG]!=null)Q[BG]=C5.expectString(A[BG]);return Q},"de_InvalidIdentityTokenException"),deQ=G9((A,B)=>{let Q={};if(A[BG]!=null)Q[BG]=C5.expectString(A[BG]);return Q},"de_MalformedPolicyDocumentException"),ceQ=G9((A,B)=>{let Q={};if(A[BG]!=null)Q[BG]=C5.expectString(A[BG]);return Q},"de_PackedPolicyTooLargeException"),leQ=G9((A,B)=>{let Q={};if(A[BG]!=null)Q[BG]=C5.expectString(A[BG]);return Q},"de_RegionDisabledException"),$T=G9((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),peQ=C5.withBaseException(wT),DpA=G9(async(A,B,Q,D,Z)=>{let{hostname:G,protocol:F="https",port:I,path:Y}=await A.endpoint(),W={protocol:F,hostname:G,port:I,method:"POST",path:Y.endsWith("/")?Y.slice(0,-1)+Q:Y+Q,headers:B};if(D!==void 0)W.hostname=D;if(Z!==void 0)W.body=Z;return new zeQ.HttpRequest(W)},"buildHttpRpcRequest"),ZpA={"content-type":"application/x-www-form-urlencoded"},GpA="2011-06-15",FpA="Action",U90="AccessKeyId",ieQ="AssumeRole",w90="AssumedRoleId",qa="AssumedRoleUser",neQ="AssumeRoleWithWebIdentity",$90="Arn",q90="Audience",Na="Credentials",N90="ContextAssertion",La="DurationSeconds",L90="Expiration",M90="ExternalId",R90="Key",Ma="Policy",Ra="PolicyArns",O90="ProviderArn",T90="ProvidedContexts",P90="ProviderId",Oa="PackedPolicySize",S90="Provider",Ta="RoleArn",Pa="RoleSessionName",j90="SecretAccessKey",y90="SubjectFromWebIdentityToken",UT="SourceIdentity",k90="SerialNumber",_90="SessionToken",x90="Tags",v90="TokenCode",b90="TransitiveTagKeys",IpA="Version",f90="Value",h90="WebIdentityToken",g90="arn",BG="message",YpA=G9((A)=>Object.entries(A).map(([B,Q])=>C5.extendedEncodeURIComponent(B)+"="+C5.extendedEncodeURIComponent(Q)).join("&"),"buildFormUrlencodedString"),aeQ=G9((A,B)=>{if(B.Error?.Code!==void 0)return B.Error.Code;if(A.statusCode==404)return"NotFound"},"loadQueryErrorCode"),p90=class extends CeQ.Command.classBuilder().ep(KeQ.commonParams).m(function(A,B,Q,D){return[VeQ.getSerdePlugin(Q,this.serialize,this.deserialize),XeQ.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRole",{}).n("STSClient","AssumeRoleCommand").f(void 0,clA).ser(EeQ).de(weQ).build(){static{G9(this,"AssumeRoleCommand")}},seQ=R6(),reQ=j3(),oeQ=w8(),teQ=N41(),i90=class extends oeQ.Command.classBuilder().ep(teQ.commonParams).m(function(A,B,Q,D){return[reQ.getSerdePlugin(Q,this.serialize,this.deserialize),seQ.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRoleWithWebIdentity",{}).n("STSClient","AssumeRoleWithWebIdentityCommand").f(rlA,olA).ser(UeQ).de($eQ).build(){static{G9(this,"AssumeRoleWithWebIdentityCommand")}},eeQ=q41(),A14={AssumeRoleCommand:p90,AssumeRoleWithWebIdentityCommand:i90},WpA=class extends eeQ.STSClient{static{G9(this,"STS")}};JeQ.createAggregatedClient(A14,WpA);var B14=N41(),m90=Oz(),mlA="us-east-1",JpA=G9((A)=>{if(typeof A?.Arn==="string"){let B=A.Arn.split(":");if(B.length>4&&B[4]!=="")return B[4]}return},"getAccountIdFromAssumedRoleUser"),XpA=G9(async(A,B,Q)=>{let D=typeof A==="function"?await A():A,Z=typeof B==="function"?await B():B;return Q?.debug?.("@aws-sdk/client-sts::resolveRegion","accepting first of:",`${D} (provider)`,`${Z} (parent client)`,`${mlA} (STS default)`),D??Z??mlA},"resolveRegion"),Q14=G9((A,B)=>{let Q,D;return async(Z,G)=>{if(D=Z,!Q){let{logger:J=A?.parentClientConfig?.logger,region:X,requestHandler:V=A?.parentClientConfig?.requestHandler,credentialProviderLogger:C}=A,K=await XpA(X,A?.parentClientConfig?.region,C),H=!VpA(V);Q=new B({profile:A?.parentClientConfig?.profile,credentialDefaultProvider:G9(()=>async()=>D,"credentialDefaultProvider"),region:K,requestHandler:H?V:void 0,logger:J})}let{Credentials:F,AssumedRoleUser:I}=await Q.send(new p90(G));if(!F||!F.AccessKeyId||!F.SecretAccessKey)throw new Error(`Invalid response from STS.assumeRole call with role ${G.RoleArn}`);let Y=JpA(I),W={accessKeyId:F.AccessKeyId,secretAccessKey:F.SecretAccessKey,sessionToken:F.SessionToken,expiration:F.Expiration,...F.CredentialScope&&{credentialScope:F.CredentialScope},...Y&&{accountId:Y}};return m90.setCredentialFeature(W,"CREDENTIALS_STS_ASSUME_ROLE","i"),W}},"getDefaultRoleAssumer"),D14=G9((A,B)=>{let Q;return async(D)=>{if(!Q){let{logger:Y=A?.parentClientConfig?.logger,region:W,requestHandler:J=A?.parentClientConfig?.requestHandler,credentialProviderLogger:X}=A,V=await XpA(W,A?.parentClientConfig?.region,X),C=!VpA(J);Q=new B({profile:A?.parentClientConfig?.profile,region:V,requestHandler:C?J:void 0,logger:Y})}let{Credentials:Z,AssumedRoleUser:G}=await Q.send(new i90(D));if(!Z||!Z.AccessKeyId||!Z.SecretAccessKey)throw new Error(`Invalid response from STS.assumeRoleWithWebIdentity call with role ${D.RoleArn}`);let F=JpA(G),I={accessKeyId:Z.AccessKeyId,secretAccessKey:Z.SecretAccessKey,sessionToken:Z.SessionToken,expiration:Z.Expiration,...Z.CredentialScope&&{credentialScope:Z.CredentialScope},...F&&{accountId:F}};if(F)m90.setCredentialFeature(I,"RESOLVED_ACCOUNT_ID","T");return m90.setCredentialFeature(I,"CREDENTIALS_STS_ASSUME_ROLE_WEB_ID","k"),I}},"getDefaultRoleAssumerWithWebIdentity"),VpA=G9((A)=>{return A?.metadata?.handlerProtocol==="h2"},"isH2"),CpA=q41(),KpA=G9((A,B)=>{if(!B)return A;else return class Q extends A{static{G9(this,"CustomizableSTSClient")}constructor(D){super(D);for(let Z of B)this.middlewareStack.use(Z)}}},"getCustomizableStsClientCtor"),HpA=G9((A={},B)=>Q14(A,KpA(CpA.STSClient,B)),"getDefaultRoleAssumer"),zpA=G9((A={},B)=>D14(A,KpA(CpA.STSClient,B)),"getDefaultRoleAssumerWithWebIdentity"),Z14=G9((A)=>(B)=>A({roleAssumer:HpA(B),roleAssumerWithWebIdentity:zpA(B),...B}),"decorateDefaultCredentialProvider")});

// Export all variables
module.exports = {
  $41,
  $Q0,
  $_A,
  $vA,
  AfA,
  AgA,
  AiA,
  BA0,
  BB0,
  BnA,
  CQ0,
  CbA,
  D20,
  DkA,
  DlA,
  DmA,
  DxA,
  EV,
  EaA,
  ElA,
  EvA,
  F41,
  FjA,
  FlA,
  FmA,
  FxA,
  GB0,
  GfA,
  GuA,
  Hk,
  I41,
  IgA,
  Iw1,
  JQ0,
  K20,
  K41,
  KaA,
  KfA,
  N41,
  N_A,
  NkA,
  NmA,
  NpA,
  NuA,
  OdA,
  OlA,
  Ow1,
  Oz,
  PiA,
  Q20,
  QB0,
  QaA,
  Qw1,
  R_A,
  RaA,
  RuA,
  RvA,
  RyA,
  ShA,
  Sw,
  Sw1,
  SyA,
  UQ0,
  UV,
  UxA,
  V20,
  V6,
  VI,
  W20,
  W90,
  W_A,
  WcA,
  XfA,
  XhA,
  XlA,
  XmA,
  XxA,
  Y90,
  YK,
  YnA,
  ZQ0,
  Zw1,
  __A,
  _aA,
  _lA,
  _uA,
  _w1,
  aA0,
  akA,
  amA,
  an,
  aw1,
  cA0,
  cQ1,
  cnA,
  dA0,
  ddA,
  dpA,
  eQ1,
  eU1,
  gpA,
  haA,
  iA0,
  iB0,
  ihA,
  j41,
  j_A,
  jaA,
  kvA,
  lQ1,
  laA,
  maA,
  mh,
  mn,
  naA,
  niA,
  npA,
  o90,
  oQ1,
  oU1,
  obA,
  pQ1,
  pdA,
  q41,
  qB0,
  qQ0,
  qfA,
  qhA,
  riA,
  shA,
  t20,
  th,
  tmA,
  txA,
  uh,
  vaA,
  w8,
  wA0,
  wB0,
  wkA,
  wuA,
  yw1
};
