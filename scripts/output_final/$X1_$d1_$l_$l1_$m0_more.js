// Package extracted with entry point: Rl1
// Contains 205 variables: $X1, $d1, $l, $l1, $m0, Ad1, Ag0, Al0, B21, BJ1... (and 195 more)

var $X1=E((Sd0)=>{var{_optionalChain:wX1}=wA();Object.defineProperty(Sd0,"__esModule",{value:!0});var _G=SQ(),Lf=wA(),EU9=wf();Sd0.ChannelName=void 0;(function(A){A.RequestCreate="undici:request:create";let Q="undici:request:headers";A.RequestEnd=Q;let D="undici:request:error";A.RequestError=D})(Sd0.ChannelName||(Sd0.ChannelName={}));var UU9=(A)=>{return new FV(A)},wU9=_G.defineIntegration(UU9);class FV{static __initStatic(){this.id="Undici"}__init(){this.name=FV.id}__init2(){this._createSpanUrlMap=new Lf.LRUMap(100)}__init3(){this._headersUrlMap=new Lf.LRUMap(100)}constructor(A={}){FV.prototype.__init.call(this),FV.prototype.__init2.call(this),FV.prototype.__init3.call(this),FV.prototype.__init4.call(this),FV.prototype.__init5.call(this),FV.prototype.__init6.call(this),this._options={breadcrumbs:A.breadcrumbs===void 0?!0:A.breadcrumbs,tracing:A.tracing,shouldCreateSpanForRequest:A.shouldCreateSpanForRequest}}setupOnce(A){if(EU9.NODE_VERSION.major<16)return;let B;try{B=J1("diagnostics_channel")}catch(Q){}if(!B||!B.subscribe)return;B.subscribe(Sd0.ChannelName.RequestCreate,this._onRequestCreate),B.subscribe(Sd0.ChannelName.RequestEnd,this._onRequestEnd),B.subscribe(Sd0.ChannelName.RequestError,this._onRequestError)}_shouldCreateSpan(A){if(this._options.tracing===!1||this._options.tracing===void 0&&!_G.hasTracingEnabled())return!1;if(this._options.shouldCreateSpanForRequest===void 0)return!0;let B=this._createSpanUrlMap.get(A);if(B!==void 0)return B;let Q=this._options.shouldCreateSpanForRequest(A);return this._createSpanUrlMap.set(A,Q),Q}__init4(){this._onRequestCreate=(A)=>{if(!wX1([_G.getClient,"call",(J)=>J(),"optionalAccess",(J)=>J.getIntegration,"call",(J)=>J(FV)]))return;let{request:B}=A,Q=B.origin?B.origin.toString()+B.path:B.path,D=_G.getClient();if(!D)return;if(_G.isSentryRequestUrl(Q,D)||B.__sentry_span__!==void 0)return;let Z=D.getOptions(),G=_G.getCurrentScope(),F=_G.getIsolationScope(),I=_G.getActiveSpan(),Y=this._shouldCreateSpan(Q)?qU9(I,B,Q):void 0;if(Y)B.__sentry_span__=Y;if(((J)=>{if(Z.tracePropagationTargets===void 0)return!0;let X=this._headersUrlMap.get(J);if(X!==void 0)return X;let V=Lf.stringMatchesSomePattern(J,Z.tracePropagationTargets);return this._headersUrlMap.set(J,V),V})(Q)){let{traceId:J,spanId:X,sampled:V,dsc:C}={...F.getPropagationContext(),...G.getPropagationContext()},K=Y?_G.spanToTraceHeader(Y):Lf.generateSentryTraceHeader(J,X,V),H=Lf.dynamicSamplingContextToSentryBaggageHeader(C||(Y?_G.getDynamicSamplingContextFromSpan(Y):_G.getDynamicSamplingContextFromClient(J,D,G)));$U9(B,K,H)}}}__init5(){this._onRequestEnd=(A)=>{if(!wX1([_G.getClient,"call",(G)=>G(),"optionalAccess",(G)=>G.getIntegration,"call",(G)=>G(FV)]))return;let{request:B,response:Q}=A,D=B.origin?B.origin.toString()+B.path:B.path;if(_G.isSentryRequestUrl(D,_G.getClient()))return;let Z=B.__sentry_span__;if(Z)_G.setHttpStatus(Z,Q.statusCode),Z.end();if(this._options.breadcrumbs)_G.addBreadcrumb({category:"http",data:{method:B.method,status_code:Q.statusCode,url:D},type:"http"},{event:"response",request:B,response:Q})}}__init6(){this._onRequestError=(A)=>{if(!wX1([_G.getClient,"call",(Z)=>Z(),"optionalAccess",(Z)=>Z.getIntegration,"call",(Z)=>Z(FV)]))return;let{request:B}=A,Q=B.origin?B.origin.toString()+B.path:B.path;if(_G.isSentryRequestUrl(Q,_G.getClient()))return;let D=B.__sentry_span__;if(D)D.setStatus("internal_error"),D.end();if(this._options.breadcrumbs)_G.addBreadcrumb({category:"http",data:{method:B.method,url:Q},level:"error",type:"http"},{event:"error",request:B})}}}FV.__initStatic();function $U9(A,B,Q){let D;if(Array.isArray(A.headers))D=A.headers.some((Z)=>Z==="sentry-trace");else D=A.headers.split(`\r
`).some((G)=>G.startsWith("sentry-trace:"));if(D)return;if(A.addHeader("sentry-trace",B),Q)A.addHeader("baggage",Q)}function qU9(A,B,Q){let D=Lf.parseUrl(Q),Z=B.method||"GET",G={"http.method":Z};if(D.search)G["http.query"]=D.search;if(D.hash)G["http.fragment"]=D.hash;return wX1([A,"optionalAccess",(F)=>F.startChild,"call",(F)=>F({op:"http.client",origin:"auto.http.node.undici",description:`${Z} ${Lf.getSanitizedUrlString(D)}`,data:G})])}Sd0.Undici=FV;Sd0.nativeNodeFetchintegration=wU9});
var $d1=E((Px0)=>{Object.defineProperty(Px0,"__esModule",{value:!0});var Mx0=kW(),Rx0=1000;function Ox0(){return Date.now()/Rx0}function aB9(){let{performance:A}=Mx0.GLOBAL_OBJ;if(!A||!A.now)return Ox0;let B=Date.now()-A.now(),Q=A.timeOrigin==null?B:A.timeOrigin;return()=>{return(Q+A.now())/Rx0}}var Tx0=aB9(),sB9=Tx0;Px0._browserPerformanceTimeOriginMode=void 0;var rB9=(()=>{let{performance:A}=Mx0.GLOBAL_OBJ;if(!A||!A.now){Px0._browserPerformanceTimeOriginMode="none";return}let B=3600000,Q=A.now(),D=Date.now(),Z=A.timeOrigin?Math.abs(A.timeOrigin+Q-D):B,G=Z<B,F=A.timing&&A.timing.navigationStart,Y=typeof F==="number"?Math.abs(F+Q-D):B,W=Y<B;if(G||W)if(Z<=Y)return Px0._browserPerformanceTimeOriginMode="timeOrigin",A.timeOrigin;else return Px0._browserPerformanceTimeOriginMode="navigationStart",F;return Px0._browserPerformanceTimeOriginMode="dateNow",D})();Px0.browserPerformanceTimeOrigin=rB9;Px0.dateTimestampInSeconds=Ox0;Px0.timestampInSeconds=Tx0;Px0.timestampWithMs=sB9});
var $l=E((Mg0)=>{Object.defineProperty(Mg0,"__esModule",{value:!0});var yX9=(A,B,Q)=>{let D,Z;return(G)=>{if(B.value>=0){if(G||Q){if(Z=B.value-(D||0),Z||D===void 0)D=B.value,B.delta=Z,A(B)}}}};Mg0.bindReporter=yX9});
var $l1=E((Wc0)=>{Object.defineProperty(Wc0,"__esModule",{value:!0});var IV=SQ(),Zc0=wA();function Dc0(A){return A&&A.statusCode!==void 0}function mw9(A){return A&&A.error!==void 0}function dw9(A){IV.captureException(A,{mechanism:{type:"hapi",handled:!1,data:{function:"hapiErrorPlugin"}}})}var Gc0={name:"SentryHapiErrorPlugin",version:IV.SDK_VERSION,register:async function(A){A.events.on("request",(Q,D)=>{let Z=IV.getActiveTransaction();if(mw9(D))dw9(D.error);if(Z)Z.setStatus("internal_error"),Z.end()})}},Fc0={name:"SentryHapiTracingPlugin",version:IV.SDK_VERSION,register:async function(A){let B=A;B.ext("onPreHandler",(Q,D)=>{let Z=IV.continueTrace({sentryTrace:Q.headers["sentry-trace"]||void 0,baggage:Q.headers.baggage||void 0},(G)=>{return IV.startTransaction({...G,op:"hapi.request",name:Q.route.path,description:`${Q.route.method} ${Q.path}`})});return IV.getCurrentScope().setSpan(Z),D.continue}),B.ext("onPreResponse",(Q,D)=>{let Z=IV.getActiveTransaction();if(Q.response&&Dc0(Q.response)&&Z){let G=Q.response;G.header("sentry-trace",IV.spanToTraceHeader(Z));let F=Zc0.dynamicSamplingContextToSentryBaggageHeader(IV.getDynamicSamplingContextFromSpan(Z));if(F)G.header("baggage",F)}return D.continue}),B.ext("onPostHandler",(Q,D)=>{let Z=IV.getActiveTransaction();if(Z){if(Q.response&&Dc0(Q.response))IV.setHttpStatus(Z,Q.response.statusCode);Z.end()}return D.continue})}},Ic0="Hapi",cw9=(A={})=>{let B=A.server;return{name:Ic0,setupOnce(){if(!B)return;Zc0.fill(B,"start",(Q)=>{return async function(){return await this.register(Fc0),await this.register(Gc0),Q.apply(this)}})}}},Yc0=IV.defineIntegration(cw9),lw9=IV.convertIntegrationFnToClass(Ic0,Yc0);Wc0.Hapi=lw9;Wc0.hapiErrorPlugin=Gc0;Wc0.hapiIntegration=Yc0;Wc0.hapiTracingPlugin=Fc0});
var $m0=E((wm0)=>{var{_optionalChain:Fz9}=wA();Object.defineProperty(wm0,"__esModule",{value:!0});var Dl1=SQ(),Iz9=J1("async_hooks"),QX1;function Yz9(){if(!QX1)QX1=new Iz9.AsyncLocalStorage;function A(){return QX1.getStore()}function B(D){let Z={};return Dl1.ensureHubOnCarrier(Z,D),Dl1.getHubFromCarrier(Z)}function Q(D,Z){let G=A();if(G&&Fz9([Z,"optionalAccess",(I)=>I.reuseExisting]))return D();let F=B(G);return QX1.run(F,()=>{return D()})}Dl1.setAsyncContextStrategy({getCurrentHub:A,runWithAsyncContext:Q})}wm0.setHooksAsyncContextStrategy=Yz9});
var Ad1=E((j_0)=>{Object.defineProperty(j_0,"__esModule",{value:!0});var vA9=gH(),bA9=tm1(),T_0=kW(),Z21=QO();function fA9(A){Z21.addHandler("fetch",A),Z21.maybeInstrument("fetch",hA9)}function hA9(){if(!bA9.supportsNativeFetch())return;vA9.fill(T_0.GLOBAL_OBJ,"fetch",function(A){return function(...B){let{method:Q,url:D}=S_0(B),Z={args:B,fetchData:{method:Q,url:D},startTimestamp:Date.now()};return Z21.triggerHandlers("fetch",{...Z}),A.apply(T_0.GLOBAL_OBJ,B).then((G)=>{let F={...Z,endTimestamp:Date.now(),response:G};return Z21.triggerHandlers("fetch",F),G},(G)=>{let F={...Z,endTimestamp:Date.now(),error:G};throw Z21.triggerHandlers("fetch",F),G})}})}function em1(A,B){return!!A&&typeof A==="object"&&!!A[B]}function P_0(A){if(typeof A==="string")return A;if(!A)return"";if(em1(A,"url"))return A.url;if(A.toString)return A.toString();return""}function S_0(A){if(A.length===0)return{method:"GET",url:""};if(A.length===2){let[Q,D]=A;return{url:P_0(Q),method:em1(D,"method")?String(D.method).toUpperCase():"GET"}}let B=A[0];return{url:P_0(B),method:em1(B,"method")?String(B.method).toUpperCase():"GET"}}j_0.addFetchInstrumentationHandler=fA9;j_0.parseFetchArgs=S_0});
var Ag0=E((eh0)=>{var{_optionalChain:tU}=wA();Object.defineProperty(eh0,"__esModule",{value:!0});var kc1=SQ(),GV=wA(),bJ1=ZV(),aJ9=mj();class fJ1{static __initStatic(){this.id="Express"}constructor(A={}){this.name=fJ1.id,this._router=A.router||A.app,this._methods=(Array.isArray(A.methods)?A.methods:[]).concat("use")}setupOnce(A,B){if(!this._router){bJ1.DEBUG_BUILD&&GV.logger.error("ExpressIntegration is missing an Express instance");return}if(aJ9.shouldDisableAutoInstrumentation(B)){bJ1.DEBUG_BUILD&&GV.logger.log("Express Integration is skipped because of instrumenter configuration.");return}oJ9(this._router,this._methods),tJ9(this._router)}}fJ1.__initStatic();function rh0(A,B){let Q=A.length;switch(Q){case 2:return function(D,Z){let G=Z.__sentry_transaction;if(G){let F=G.startChild({description:A.name,op:`middleware.express.${B}`,origin:"auto.middleware.express"});Z.once("finish",()=>{F.end()})}return A.call(this,D,Z)};case 3:return function(D,Z,G){let F=Z.__sentry_transaction,I=tU([F,"optionalAccess",(Y)=>Y.startChild,"call",(Y)=>Y({description:A.name,op:`middleware.express.${B}`,origin:"auto.middleware.express"})]);A.call(this,D,Z,function(...Y){tU([I,"optionalAccess",(W)=>W.end,"call",(W)=>W()]),G.call(this,...Y)})};case 4:return function(D,Z,G,F){let I=G.__sentry_transaction,Y=tU([I,"optionalAccess",(W)=>W.startChild,"call",(W)=>W({description:A.name,op:`middleware.express.${B}`,origin:"auto.middleware.express"})]);A.call(this,D,Z,G,function(...W){tU([Y,"optionalAccess",(J)=>J.end,"call",(J)=>J()]),F.call(this,...W)})};default:throw new Error(`Express middleware takes 2-4 arguments. Got: ${Q}`)}}function sJ9(A,B){return A.map((Q)=>{if(typeof Q==="function")return rh0(Q,B);if(Array.isArray(Q))return Q.map((D)=>{if(typeof D==="function")return rh0(D,B);return D});return Q})}function rJ9(A,B){let Q=A[B];return A[B]=function(...D){return Q.call(this,...sJ9(D,B))},A}function oJ9(A,B=[]){B.forEach((Q)=>rJ9(A,Q))}function tJ9(A){let B="settings"in A;if(B&&A._router===void 0&&A.lazyrouter)A.lazyrouter();let Q=B?A._router:A;if(!Q){bJ1.DEBUG_BUILD&&GV.logger.debug("Cannot instrument router for URL Parameterization (did not find a valid router)."),bJ1.DEBUG_BUILD&&GV.logger.debug("Routing instrumentation is currently only supported in Express 4.");return}let D=Object.getPrototypeOf(Q),Z=D.process_params;D.process_params=function G(F,I,Y,W,J){if(!Y._reconstructedRoute)Y._reconstructedRoute="";let{layerRoutePath:X,isRegex:V,isArray:C,numExtraSegments:K}=eJ9(F);if(X||V||C)Y._hasParameters=!0;let H;if(X)H=X;else H=th0(Y.originalUrl,Y._reconstructedRoute,F.path)||"";let z=H.split("/").filter((N)=>N.length>0&&(V||C||!N.includes("*"))).join("/");if(z&&z.length>0)Y._reconstructedRoute+=`/${z}${V?"/":""}`;let $=GV.getNumberOfUrlSegments(GV.stripUrlQueryAndFragment(Y.originalUrl||""))+K,L=GV.getNumberOfUrlSegments(Y._reconstructedRoute);if($===L){if(!Y._hasParameters){if(Y._reconstructedRoute!==Y.originalUrl)Y._reconstructedRoute=Y.originalUrl?GV.stripUrlQueryAndFragment(Y.originalUrl):Y.originalUrl}let N=W.__sentry_transaction,O=N&&kc1.spanToJSON(N).data||{};if(N&&O[kc1.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]!=="custom"){let R=Y._reconstructedRoute||"/",[T,j]=GV.extractPathForTransaction(Y,{path:!0,method:!0,customRoute:R});N.updateName(T),N.setAttribute(kc1.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,j)}}return Z.call(this,F,I,Y,W,J)}}var oh0=(A,B,Q)=>{if(!A||!B||!Q||Object.keys(Q).length===0||tU([Q,"access",(W)=>W[0],"optionalAccess",(W)=>W.offset])===void 0||tU([Q,"access",(W)=>W[0],"optionalAccess",(W)=>W.offset])===null)return;let D=Q.sort((W,J)=>W.offset-J.offset),G=new RegExp(B,`${B.flags}d`).exec(A);if(!G||!G.indices)return;let[,...F]=G.indices;if(F.length!==D.length)return;let I=A,Y=0;return F.forEach((W,J)=>{if(W){let[X,V]=W,C=I.substring(0,X-Y),K=`:${D[J].name}`,H=I.substring(V-Y);I=C+K+H,Y=Y+(V-X-K.length)}}),I};function eJ9(A){let B=tU([A,"access",(F)=>F.route,"optionalAccess",(F)=>F.path]),Q=GV.isRegExp(B),D=Array.isArray(B);if(!B){let[F]=GV.GLOBAL_OBJ.process.versions.node.split(".").map(Number);if(F>=16)B=oh0(A.path,A.regexp,A.keys)}if(!B)return{isRegex:Q,isArray:D,numExtraSegments:0};let Z=D?Math.max(AX9(B)-GV.getNumberOfUrlSegments(A.path||""),0):0;return{layerRoutePath:BX9(D,B),isRegex:Q,isArray:D,numExtraSegments:Z}}function AX9(A){return A.reduce((B,Q)=>{return B+GV.getNumberOfUrlSegments(Q.toString())},0)}function BX9(A,B){if(A)return B.map((Q)=>Q.toString()).join(",");return B&&B.toString()}function th0(A,B,Q){let D=GV.stripUrlQueryAndFragment(A||""),Z=tU([D,"optionalAccess",(Y)=>Y.split,"call",(Y)=>Y("/"),"access",(Y)=>Y.filter,"call",(Y)=>Y((W)=>!!W)]),G=0,F=tU([B,"optionalAccess",(Y)=>Y.split,"call",(Y)=>Y("/"),"access",(Y)=>Y.filter,"call",(Y)=>Y((W)=>!!W),"access",(Y)=>Y.length])||0;return tU([Q,"optionalAccess",(Y)=>Y.split,"call",(Y)=>Y("/"),"access",(Y)=>Y.filter,"call",(Y)=>Y((W)=>{if(tU([Z,"optionalAccess",(J)=>J[F+G]])===W)return G+=1,!0;return!1}),"access",(Y)=>Y.join,"call",(Y)=>Y("/")])}eh0.Express=fJ1;eh0.extractOriginalRoute=oh0;eh0.preventDuplicateSegments=th0});
var Al0=E((ec0)=>{Object.defineProperty(ec0,"__esModule",{value:!0});var rc0=SQ(),sc0=wA(),oc0="RewriteFrames",Wq9=(A={})=>{let B=A.root,Q=A.prefix||"app:///",D=A.iteratee||((F)=>{if(!F.filename)return F;let I=/^[a-zA-Z]:\\/.test(F.filename)||F.filename.includes("\\")&&!F.filename.includes("/"),Y=/^\//.test(F.filename);if(I||Y){let W=I?F.filename.replace(/^[a-zA-Z]:/,"").replace(/\\/g,"/"):F.filename,J=B?sc0.relative(B,W):sc0.basename(W);F.filename=`${Q}${J}`}return F});function Z(F){try{return{...F,exception:{...F.exception,values:F.exception.values.map((I)=>({...I,...I.stacktrace&&{stacktrace:G(I.stacktrace)}}))}}}catch(I){return F}}function G(F){return{...F,frames:F&&F.frames&&F.frames.map((I)=>D(I))}}return{name:oc0,setupOnce(){},processEvent(F){let I=F;if(F.exception&&Array.isArray(F.exception.values))I=Z(I);return I}}},tc0=rc0.defineIntegration(Wq9),Jq9=rc0.convertIntegrationFnToClass(oc0,tc0);ec0.RewriteFrames=Jq9;ec0.rewriteFramesIntegration=tc0});
var B21=E((uk0)=>{Object.defineProperty(uk0,"__esModule",{value:!0});var jW1=hH();function Q19(A,B=0){if(typeof A!=="string"||B===0)return A;return A.length<=B?A:`${A.slice(0,B)}...`}function D19(A,B){let Q=A,D=Q.length;if(D<=150)return Q;if(B>D)B=D;let Z=Math.max(B-60,0);if(Z<5)Z=0;let G=Math.min(Z+140,D);if(G>D-5)G=D;if(G===D)Z=Math.max(G-140,0);if(Q=Q.slice(Z,G),Z>0)Q=`'{snip} ${Q}`;if(G<D)Q+=" {snip}";return Q}function Z19(A,B){if(!Array.isArray(A))return"";let Q=[];for(let D=0;D<A.length;D++){let Z=A[D];try{if(jW1.isVueViewModel(Z))Q.push("[VueViewModel]");else Q.push(String(Z))}catch(G){Q.push("[value cannot be serialized]")}}return Q.join(B)}function gk0(A,B,Q=!1){if(!jW1.isString(A))return!1;if(jW1.isRegExp(B))return B.test(A);if(jW1.isString(B))return Q?A===B:A.includes(B);return!1}function G19(A,B=[],Q=!1){return B.some((D)=>gk0(A,D,Q))}uk0.isMatchingPattern=gk0;uk0.safeJoin=Z19;uk0.snipLine=D19;uk0.stringMatchesSomePattern=G19;uk0.truncate=Q19});
var BJ1=E((bv0)=>{Object.defineProperty(bv0,"__esModule",{value:!0});var fC=wA(),g59=Gl(),kv0=K21(),md1=DJ1(),ud1=QJ1(),u59=BV();function m59(A,B,Q,D,Z,G){let{normalizeDepth:F=3,normalizeMaxBreadth:I=1000}=A,Y={...B,event_id:B.event_id||Q.event_id||fC.uuid4(),timestamp:B.timestamp||fC.dateTimestampInSeconds()},W=Q.integrations||A.integrations.map((z)=>z.name);if(d59(Y,A),c59(Y,W),B.type===void 0)xv0(Y,A.stackParser);let J=p59(D,Q.captureContext);if(Q.mechanism)fC.addExceptionMechanism(Y,Q.mechanism);let X=Z&&Z.getEventProcessors?Z.getEventProcessors():[],V=md1.getGlobalScope().getScopeData();if(G){let z=G.getScopeData();ud1.mergeScopeData(V,z)}if(J){let z=J.getScopeData();ud1.mergeScopeData(V,z)}let C=[...Q.attachments||[],...V.attachments];if(C.length)Q.attachments=C;ud1.applyScopeDataToEvent(Y,V);let K=[...X,...kv0.getGlobalEventProcessors(),...V.eventProcessors];return kv0.notifyEventProcessors(K,Y,Q).then((z)=>{if(z)vv0(z);if(typeof F==="number"&&F>0)return l59(z,F,I);return z})}function d59(A,B){let{environment:Q,release:D,dist:Z,maxValueLength:G=250}=B;if(!("environment"in A))A.environment="environment"in B?Q:g59.DEFAULT_ENVIRONMENT;if(A.release===void 0&&D!==void 0)A.release=D;if(A.dist===void 0&&Z!==void 0)A.dist=Z;if(A.message)A.message=fC.truncate(A.message,G);let F=A.exception&&A.exception.values&&A.exception.values[0];if(F&&F.value)F.value=fC.truncate(F.value,G);let I=A.request;if(I&&I.url)I.url=fC.truncate(I.url,G)}var _v0=new WeakMap;function xv0(A,B){let Q=fC.GLOBAL_OBJ._sentryDebugIds;if(!Q)return;let D,Z=_v0.get(B);if(Z)D=Z;else D=new Map,_v0.set(B,D);let G=Object.keys(Q).reduce((F,I)=>{let Y,W=D.get(I);if(W)Y=W;else Y=B(I),D.set(I,Y);for(let J=Y.length-1;J>=0;J--){let X=Y[J];if(X.filename){F[X.filename]=Q[I];break}}return F},{});try{A.exception.values.forEach((F)=>{F.stacktrace.frames.forEach((I)=>{if(I.filename)I.debug_id=G[I.filename]})})}catch(F){}}function vv0(A){let B={};try{A.exception.values.forEach((D)=>{D.stacktrace.frames.forEach((Z)=>{if(Z.debug_id){if(Z.abs_path)B[Z.abs_path]=Z.debug_id;else if(Z.filename)B[Z.filename]=Z.debug_id;delete Z.debug_id}})})}catch(D){}if(Object.keys(B).length===0)return;A.debug_meta=A.debug_meta||{},A.debug_meta.images=A.debug_meta.images||[];let Q=A.debug_meta.images;Object.keys(B).forEach((D)=>{Q.push({type:"sourcemap",code_file:D,debug_id:B[D]})})}function c59(A,B){if(B.length>0)A.sdk=A.sdk||{},A.sdk.integrations=[...A.sdk.integrations||[],...B]}function l59(A,B,Q){if(!A)return null;let D={...A,...A.breadcrumbs&&{breadcrumbs:A.breadcrumbs.map((Z)=>({...Z,...Z.data&&{data:fC.normalize(Z.data,B,Q)}}))},...A.user&&{user:fC.normalize(A.user,B,Q)},...A.contexts&&{contexts:fC.normalize(A.contexts,B,Q)},...A.extra&&{extra:fC.normalize(A.extra,B,Q)}};if(A.contexts&&A.contexts.trace&&D.contexts){if(D.contexts.trace=A.contexts.trace,A.contexts.trace.data)D.contexts.trace.data=fC.normalize(A.contexts.trace.data,B,Q)}if(A.spans)D.spans=A.spans.map((Z)=>{let G=u59.spanToJSON(Z).data;if(G)Z.data=fC.normalize(G,B,Q);return Z});return D}function p59(A,B){if(!B)return A;let Q=A?A.clone():new md1.Scope;return Q.update(B),Q}function i59(A){if(!A)return;if(n59(A))return{captureContext:A};if(s59(A))return{captureContext:A};return A}function n59(A){return A instanceof md1.Scope||typeof A==="function"}var a59=["user","level","extra","contexts","tags","fingerprint","requestSession","propagationContext"];function s59(A){return Object.keys(A).some((B)=>a59.includes(B))}bv0.applyDebugIds=xv0;bv0.applyDebugMeta=vv0;bv0.parseEventHintOrCaptureContext=i59;bv0.prepareEvent=m59});
var BV=E((yv0)=>{Object.defineProperty(yv0,"__esModule",{value:!0});var gd1=wA(),T59=0,Pv0=1;function P59(A){let{spanId:B,traceId:Q}=A.spanContext(),{data:D,op:Z,parent_span_id:G,status:F,tags:I,origin:Y}=Sv0(A);return gd1.dropUndefinedKeys({data:D,op:Z,parent_span_id:G,span_id:B,status:F,tags:I,trace_id:Q,origin:Y})}function S59(A){let{traceId:B,spanId:Q}=A.spanContext(),D=jv0(A);return gd1.generateSentryTraceHeader(B,Q,D)}function j59(A){if(typeof A==="number")return Tv0(A);if(Array.isArray(A))return A[0]+A[1]/1e9;if(A instanceof Date)return Tv0(A.getTime());return gd1.timestampInSeconds()}function Tv0(A){return A>9999999999?A/1000:A}function Sv0(A){if(y59(A))return A.getSpanJSON();if(typeof A.toJSON==="function")return A.toJSON();return{}}function y59(A){return typeof A.getSpanJSON==="function"}function jv0(A){let{traceFlags:B}=A.spanContext();return Boolean(B&Pv0)}yv0.TRACE_FLAG_NONE=T59;yv0.TRACE_FLAG_SAMPLED=Pv0;yv0.spanIsSampled=jv0;yv0.spanTimeInputToSeconds=j59;yv0.spanToJSON=Sv0;yv0.spanToTraceContext=P59;yv0.spanToTraceHeader=S59});
var Cc0=E((Vc0)=>{Object.defineProperty(Vc0,"__esModule",{value:!0});var Rf=tc1();Vc0.Apollo=Rf.Apollo;Vc0.Express=Rf.Express;Vc0.GraphQL=Rf.GraphQL;Vc0.Mongo=Rf.Mongo;Vc0.Mysql=Rf.Mysql;Vc0.Postgres=Rf.Postgres;Vc0.Prisma=Rf.Prisma});
var Cc1=E((ob0)=>{Object.defineProperty(ob0,"__esModule",{value:!0});var Vc1=wA();function fG9(A,B,Q,D,Z){let G={sent_at:new Date().toISOString()};if(Q&&Q.sdk)G.sdk={name:Q.sdk.name,version:Q.sdk.version};if(!!D&&!!Z)G.dsn=Vc1.dsnToString(Z);if(B)G.trace=Vc1.dropUndefinedKeys(B);let F=hG9(A);return Vc1.createEnvelope(G,[F])}function hG9(A){return[{type:"check_in"},A]}ob0.createCheckInEnvelope=fG9});
var Cd1=E((o_0)=>{Object.defineProperty(o_0,"__esModule",{value:!0});function x29(){let A=typeof WeakSet==="function",B=A?new WeakSet:[];function Q(Z){if(A){if(B.has(Z))return!0;return B.add(Z),!1}for(let G=0;G<B.length;G++)if(B[G]===Z)return!0;return B.push(Z),!1}function D(Z){if(A)B.delete(Z);else for(let G=0;G<B.length;G++)if(B[G]===Z){B.splice(G,1);break}}return[Q,D]}o_0.memoBuilder=x29});
var Cg0=E((Vg0)=>{var{_optionalChain:wl}=wA();Object.defineProperty(Vg0,"__esModule",{value:!0});var y21=wA(),Xg0=ZV(),wX9=mj();class dJ1{static __initStatic(){this.id="GraphQL"}constructor(){this.name=dJ1.id}loadDependency(){return this._module=this._module||y21.loadModule("graphql/execution/execute.js")}setupOnce(A,B){if(wX9.shouldDisableAutoInstrumentation(B)){Xg0.DEBUG_BUILD&&y21.logger.log("GraphQL Integration is skipped because of instrumenter configuration.");return}let Q=this.loadDependency();if(!Q){Xg0.DEBUG_BUILD&&y21.logger.error("GraphQL Integration was unable to require graphql/execution package.");return}y21.fill(Q,"execute",function(D){return function(...Z){let G=B().getScope(),F=G.getSpan(),I=wl([F,"optionalAccess",(W)=>W.startChild,"call",(W)=>W({description:"execute",op:"graphql.execute",origin:"auto.graphql.graphql"})]);wl([G,"optionalAccess",(W)=>W.setSpan,"call",(W)=>W(I)]);let Y=D.call(this,...Z);if(y21.isThenable(Y))return Y.then((W)=>{return wl([I,"optionalAccess",(J)=>J.end,"call",(J)=>J()]),wl([G,"optionalAccess",(J)=>J.setSpan,"call",(J)=>J(F)]),W});return wl([I,"optionalAccess",(W)=>W.end,"call",(W)=>W()]),wl([G,"optionalAccess",(W)=>W.setSpan,"call",(W)=>W(F)]),Y}})}}dJ1.__initStatic();Vg0.GraphQL=dJ1});
var Cl1=E((Cd0)=>{Object.defineProperty(Cd0,"__esModule",{value:!0});var cE9=SQ(),CX1=wA(),Vl1=l21(),lE9=2000;function pE9(A){CX1.consoleSandbox(()=>{console.error(A)});let B=cE9.getClient();if(B===void 0)Vl1.DEBUG_BUILD&&CX1.logger.warn("No NodeClient was defined, we are exiting the process now."),global.process.exit(1);let Q=B.getOptions(),D=Q&&Q.shutdownTimeout&&Q.shutdownTimeout>0&&Q.shutdownTimeout||lE9;B.close(D).then((Z)=>{if(!Z)Vl1.DEBUG_BUILD&&CX1.logger.warn("We reached the timeout for emptying the request buffer, still exiting now!");global.process.exit(1)},(Z)=>{Vl1.DEBUG_BUILD&&CX1.logger.error(Z)})}Cd0.logAndExitProcess=pE9});
var Cv0=E((Vv0)=>{Object.defineProperty(Vv0,"__esModule",{value:!0});var jQ9=Sd1();async function yQ9(A){let B=await jQ9._asyncOptionalChain(A);return B==null?!0:B}Vv0._asyncOptionalChainDelete=yQ9});
var D21=E((q_0)=>{Object.defineProperty(q_0,"__esModule",{value:!0});var l09=gH(),pm1=B21(),p09=kW();function i09(){let A=p09.GLOBAL_OBJ,B=A.crypto||A.msCrypto,Q=()=>Math.random()*16;try{if(B&&B.randomUUID)return B.randomUUID().replace(/-/g,"");if(B&&B.getRandomValues)Q=()=>{let D=new Uint8Array(1);return B.getRandomValues(D),D[0]}}catch(D){}return([1e7]+1000+4000+8000+100000000000).replace(/[018]/g,(D)=>(D^(Q()&15)>>D/4).toString(16))}function $_0(A){return A.exception&&A.exception.values?A.exception.values[0]:void 0}function n09(A){let{message:B,event_id:Q}=A;if(B)return B;let D=$_0(A);if(D){if(D.type&&D.value)return`${D.type}: ${D.value}`;return D.type||D.value||Q||"<unknown>"}return Q||"<unknown>"}function a09(A,B,Q){let D=A.exception=A.exception||{},Z=D.values=D.values||[],G=Z[0]=Z[0]||{};if(!G.value)G.value=B||"";if(!G.type)G.type=Q||"Error"}function s09(A,B){let Q=$_0(A);if(!Q)return;let D={type:"generic",handled:!0},Z=Q.mechanism;if(Q.mechanism={...D,...Z,...B},B&&"data"in B){let G={...Z&&Z.data,...B.data};Q.mechanism.data=G}}var r09=/^(0|[1-9]\d*)\.(0|[1-9]\d*)\.(0|[1-9]\d*)(?:-((?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\.(?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?$/;function o09(A){let B=A.match(r09)||[],Q=parseInt(B[1],10),D=parseInt(B[2],10),Z=parseInt(B[3],10);return{buildmetadata:B[5],major:isNaN(Q)?void 0:Q,minor:isNaN(D)?void 0:D,patch:isNaN(Z)?void 0:Z,prerelease:B[4]}}function t09(A,B,Q=5){if(B.lineno===void 0)return;let D=A.length,Z=Math.max(Math.min(D-1,B.lineno-1),0);B.pre_context=A.slice(Math.max(0,Z-Q),Z).map((G)=>pm1.snipLine(G,0)),B.context_line=pm1.snipLine(A[Math.min(D-1,Z)],B.colno||0),B.post_context=A.slice(Math.min(Z+1,D),Z+1+Q).map((G)=>pm1.snipLine(G,0))}function e09(A){if(A&&A.__sentry_captured__)return!0;try{l09.addNonEnumerableProperty(A,"__sentry_captured__",!0)}catch(B){}return!1}function AA9(A){return Array.isArray(A)?A:[A]}q_0.addContextToFrame=t09;q_0.addExceptionMechanism=s09;q_0.addExceptionTypeValue=a09;q_0.arrayify=AA9;q_0.checkOrSetAlreadyCaught=e09;q_0.getEventDescription=n09;q_0.parseSemver=o09;q_0.uuid4=i09});
var DJ1=E((av0)=>{Object.defineProperty(av0,"__esModule",{value:!0});var AN=wA(),iv0=K21(),H79=Fl(),z79=QJ1(),E79=100,GJ1;class Wl{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext=nv0()}static clone(A){return A?A.clone():new Wl}clone(){let A=new Wl;return A._breadcrumbs=[...this._breadcrumbs],A._tags={...this._tags},A._extra={...this._extra},A._contexts={...this._contexts},A._user=this._user,A._level=this._level,A._span=this._span,A._session=this._session,A._transactionName=this._transactionName,A._fingerprint=this._fingerprint,A._eventProcessors=[...this._eventProcessors],A._requestSession=this._requestSession,A._attachments=[...this._attachments],A._sdkProcessingMetadata={...this._sdkProcessingMetadata},A._propagationContext={...this._propagationContext},A._client=this._client,A}setClient(A){this._client=A}getClient(){return this._client}addScopeListener(A){this._scopeListeners.push(A)}addEventProcessor(A){return this._eventProcessors.push(A),this}setUser(A){if(this._user=A||{email:void 0,id:void 0,ip_address:void 0,segment:void 0,username:void 0},this._session)H79.updateSession(this._session,{user:A});return this._notifyScopeListeners(),this}getUser(){return this._user}getRequestSession(){return this._requestSession}setRequestSession(A){return this._requestSession=A,this}setTags(A){return this._tags={...this._tags,...A},this._notifyScopeListeners(),this}setTag(A,B){return this._tags={...this._tags,[A]:B},this._notifyScopeListeners(),this}setExtras(A){return this._extra={...this._extra,...A},this._notifyScopeListeners(),this}setExtra(A,B){return this._extra={...this._extra,[A]:B},this._notifyScopeListeners(),this}setFingerprint(A){return this._fingerprint=A,this._notifyScopeListeners(),this}setLevel(A){return this._level=A,this._notifyScopeListeners(),this}setTransactionName(A){return this._transactionName=A,this._notifyScopeListeners(),this}setContext(A,B){if(B===null)delete this._contexts[A];else this._contexts[A]=B;return this._notifyScopeListeners(),this}setSpan(A){return this._span=A,this._notifyScopeListeners(),this}getSpan(){return this._span}getTransaction(){let A=this._span;return A&&A.transaction}setSession(A){if(!A)delete this._session;else this._session=A;return this._notifyScopeListeners(),this}getSession(){return this._session}update(A){if(!A)return this;let B=typeof A==="function"?A(this):A;if(B instanceof Wl){let Q=B.getScopeData();if(this._tags={...this._tags,...Q.tags},this._extra={...this._extra,...Q.extra},this._contexts={...this._contexts,...Q.contexts},Q.user&&Object.keys(Q.user).length)this._user=Q.user;if(Q.level)this._level=Q.level;if(Q.fingerprint.length)this._fingerprint=Q.fingerprint;if(B.getRequestSession())this._requestSession=B.getRequestSession();if(Q.propagationContext)this._propagationContext=Q.propagationContext}else if(AN.isPlainObject(B)){let Q=A;if(this._tags={...this._tags,...Q.tags},this._extra={...this._extra,...Q.extra},this._contexts={...this._contexts,...Q.contexts},Q.user)this._user=Q.user;if(Q.level)this._level=Q.level;if(Q.fingerprint)this._fingerprint=Q.fingerprint;if(Q.requestSession)this._requestSession=Q.requestSession;if(Q.propagationContext)this._propagationContext=Q.propagationContext}return this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._requestSession=void 0,this._span=void 0,this._session=void 0,this._notifyScopeListeners(),this._attachments=[],this._propagationContext=nv0(),this}addBreadcrumb(A,B){let Q=typeof B==="number"?B:E79;if(Q<=0)return this;let D={timestamp:AN.dateTimestampInSeconds(),...A},Z=this._breadcrumbs;return Z.push(D),this._breadcrumbs=Z.length>Q?Z.slice(-Q):Z,this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(A){return this._attachments.push(A),this}getAttachments(){return this.getScopeData().attachments}clearAttachments(){return this._attachments=[],this}getScopeData(){let{_breadcrumbs:A,_attachments:B,_contexts:Q,_tags:D,_extra:Z,_user:G,_level:F,_fingerprint:I,_eventProcessors:Y,_propagationContext:W,_sdkProcessingMetadata:J,_transactionName:X,_span:V}=this;return{breadcrumbs:A,attachments:B,contexts:Q,tags:D,extra:Z,user:G,level:F,fingerprint:I||[],eventProcessors:Y,propagationContext:W,sdkProcessingMetadata:J,transactionName:X,span:V}}applyToEvent(A,B={},Q=[]){z79.applyScopeDataToEvent(A,this.getScopeData());let D=[...Q,...iv0.getGlobalEventProcessors(),...this._eventProcessors];return iv0.notifyEventProcessors(D,A,B)}setSDKProcessingMetadata(A){return this._sdkProcessingMetadata={...this._sdkProcessingMetadata,...A},this}setPropagationContext(A){return this._propagationContext=A,this}getPropagationContext(){return this._propagationContext}captureException(A,B){let Q=B&&B.event_id?B.event_id:AN.uuid4();if(!this._client)return AN.logger.warn("No client configured on scope - will not capture exception!"),Q;let D=new Error("Sentry syntheticException");return this._client.captureException(A,{originalException:A,syntheticException:D,...B,event_id:Q},this),Q}captureMessage(A,B,Q){let D=Q&&Q.event_id?Q.event_id:AN.uuid4();if(!this._client)return AN.logger.warn("No client configured on scope - will not capture message!"),D;let Z=new Error(A);return this._client.captureMessage(A,B,{originalException:A,syntheticException:Z,...Q,event_id:D},this),D}captureEvent(A,B){let Q=B&&B.event_id?B.event_id:AN.uuid4();if(!this._client)return AN.logger.warn("No client configured on scope - will not capture event!"),Q;return this._client.captureEvent(A,{...B,event_id:Q},this),Q}_notifyScopeListeners(){if(!this._notifyingListeners)this._notifyingListeners=!0,this._scopeListeners.forEach((A)=>{A(this)}),this._notifyingListeners=!1}}function U79(){if(!GJ1)GJ1=new Wl;return GJ1}function w79(A){GJ1=A}function nv0(){return{traceId:AN.uuid4(),spanId:AN.uuid4().substring(16)}}av0.Scope=Wl;av0.getGlobalScope=U79;av0.setGlobalScope=w79});
var Dc1=E((Ob0)=>{Object.defineProperty(Ob0,"__esModule",{value:!0});var xW=wA(),QV=kG(),$J1=BV(),tD9=zJ1(),eD9=wJ1(),qJ1={idleTimeout:1000,finalTimeout:30000,heartbeatInterval:5000},AZ9="finishReason",Kl=["heartbeatFailed","idleTimeout","documentHidden","finalTimeout","externalFinish","cancelled"];class Qc1 extends tD9.SpanRecorder{constructor(A,B,Q,D){super(D);this._pushActivity=A,this._popActivity=B,this.transactionSpanId=Q}add(A){if(A.spanContext().spanId!==this.transactionSpanId){let B=A.end;if(A.end=(...Q)=>{return this._popActivity(A.spanContext().spanId),B.apply(A,Q)},$J1.spanToJSON(A).timestamp===void 0)this._pushActivity(A.spanContext().spanId)}super.add(A)}}class Rb0 extends eD9.Transaction{constructor(A,B,Q=qJ1.idleTimeout,D=qJ1.finalTimeout,Z=qJ1.heartbeatInterval,G=!1,F=!1){super(A,B);if(this._idleHub=B,this._idleTimeout=Q,this._finalTimeout=D,this._heartbeatInterval=Z,this._onScope=G,this.activities={},this._heartbeatCounter=0,this._finished=!1,this._idleTimeoutCanceledPermanently=!1,this._beforeFinishCallbacks=[],this._finishReason=Kl[4],this._autoFinishAllowed=!F,G)QV.DEBUG_BUILD&&xW.logger.log(`Setting idle transaction on scope. Span ID: ${this.spanContext().spanId}`),B.getScope().setSpan(this);if(!F)this._restartIdleTimeout();setTimeout(()=>{if(!this._finished)this.setStatus("deadline_exceeded"),this._finishReason=Kl[3],this.end()},this._finalTimeout)}end(A){let B=$J1.spanTimeInputToSeconds(A);if(this._finished=!0,this.activities={},this.op==="ui.action.click")this.setAttribute(AZ9,this._finishReason);if(this.spanRecorder){QV.DEBUG_BUILD&&xW.logger.log("[Tracing] finishing IdleTransaction",new Date(B*1000).toISOString(),this.op);for(let Q of this._beforeFinishCallbacks)Q(this,B);this.spanRecorder.spans=this.spanRecorder.spans.filter((Q)=>{if(Q.spanContext().spanId===this.spanContext().spanId)return!0;if(!$J1.spanToJSON(Q).timestamp)Q.setStatus("cancelled"),Q.end(B),QV.DEBUG_BUILD&&xW.logger.log("[Tracing] cancelling span since transaction ended early",JSON.stringify(Q,void 0,2));let{start_timestamp:D,timestamp:Z}=$J1.spanToJSON(Q),G=D&&D<B,F=(this._finalTimeout+this._idleTimeout)/1000,I=Z&&D&&Z-D<F;if(QV.DEBUG_BUILD){let Y=JSON.stringify(Q,void 0,2);if(!G)xW.logger.log("[Tracing] discarding Span since it happened after Transaction was finished",Y);else if(!I)xW.logger.log("[Tracing] discarding Span since it finished after Transaction final timeout",Y)}return G&&I}),QV.DEBUG_BUILD&&xW.logger.log("[Tracing] flushing IdleTransaction")}else QV.DEBUG_BUILD&&xW.logger.log("[Tracing] No active IdleTransaction");if(this._onScope){let Q=this._idleHub.getScope();if(Q.getTransaction()===this)Q.setSpan(void 0)}return super.end(A)}registerBeforeFinishCallback(A){this._beforeFinishCallbacks.push(A)}initSpanRecorder(A){if(!this.spanRecorder){let B=(D)=>{if(this._finished)return;this._pushActivity(D)},Q=(D)=>{if(this._finished)return;this._popActivity(D)};this.spanRecorder=new Qc1(B,Q,this.spanContext().spanId,A),QV.DEBUG_BUILD&&xW.logger.log("Starting heartbeat"),this._pingHeartbeat()}this.spanRecorder.add(this)}cancelIdleTimeout(A,{restartOnChildSpanChange:B}={restartOnChildSpanChange:!0}){if(this._idleTimeoutCanceledPermanently=B===!1,this._idleTimeoutID){if(clearTimeout(this._idleTimeoutID),this._idleTimeoutID=void 0,Object.keys(this.activities).length===0&&this._idleTimeoutCanceledPermanently)this._finishReason=Kl[5],this.end(A)}}setFinishReason(A){this._finishReason=A}sendAutoFinishSignal(){if(!this._autoFinishAllowed)QV.DEBUG_BUILD&&xW.logger.log("[Tracing] Received finish signal for idle transaction."),this._restartIdleTimeout(),this._autoFinishAllowed=!0}_restartIdleTimeout(A){this.cancelIdleTimeout(),this._idleTimeoutID=setTimeout(()=>{if(!this._finished&&Object.keys(this.activities).length===0)this._finishReason=Kl[1],this.end(A)},this._idleTimeout)}_pushActivity(A){this.cancelIdleTimeout(void 0,{restartOnChildSpanChange:!this._idleTimeoutCanceledPermanently}),QV.DEBUG_BUILD&&xW.logger.log(`[Tracing] pushActivity: ${A}`),this.activities[A]=!0,QV.DEBUG_BUILD&&xW.logger.log("[Tracing] new activities count",Object.keys(this.activities).length)}_popActivity(A){if(this.activities[A])QV.DEBUG_BUILD&&xW.logger.log(`[Tracing] popActivity ${A}`),delete this.activities[A],QV.DEBUG_BUILD&&xW.logger.log("[Tracing] new activities count",Object.keys(this.activities).length);if(Object.keys(this.activities).length===0){let B=xW.timestampInSeconds();if(this._idleTimeoutCanceledPermanently){if(this._autoFinishAllowed)this._finishReason=Kl[5],this.end(B)}else this._restartIdleTimeout(B+this._idleTimeout/1000)}}_beat(){if(this._finished)return;let A=Object.keys(this.activities).join("");if(A===this._prevHeartbeatString)this._heartbeatCounter++;else this._heartbeatCounter=1;if(this._prevHeartbeatString=A,this._heartbeatCounter>=3){if(this._autoFinishAllowed)QV.DEBUG_BUILD&&xW.logger.log("[Tracing] Transaction finished because of no change for 3 heart beats"),this.setStatus("deadline_exceeded"),this._finishReason=Kl[0],this.end()}else this._pingHeartbeat()}_pingHeartbeat(){QV.DEBUG_BUILD&&xW.logger.log(`pinging Heartbeat -> current counter: ${this._heartbeatCounter}`),setTimeout(()=>{this._beat()},this._heartbeatInterval)}}Ob0.IdleTransaction=Rb0;Ob0.IdleTransactionSpanRecorder=Qc1;Ob0.TRACING_DEFAULTS=qJ1});
var Dd1=E((y_0)=>{Object.defineProperty(y_0,"__esModule",{value:!0});var Bd1=kW(),Qd1=QO(),fW1=null;function mA9(A){Qd1.addHandler("error",A),Qd1.maybeInstrument("error",dA9)}function dA9(){fW1=Bd1.GLOBAL_OBJ.onerror,Bd1.GLOBAL_OBJ.onerror=function(A,B,Q,D,Z){let G={column:D,error:Z,line:Q,msg:A,url:B};if(Qd1.triggerHandlers("error",G),fW1&&!fW1.__SENTRY_LOADER__)return fW1.apply(this,arguments);return!1},Bd1.GLOBAL_OBJ.onerror.__SENTRY_INSTRUMENTED__=!0}y_0.addGlobalErrorInstrumentationHandler=mA9});
var Df0=E((Qf0)=>{Object.defineProperty(Qf0,"__esModule",{value:!0});var Af0=wA(),T21=O21(),IF9=Uc1(),YF9=N21(),TJ1=R21();class Bf0{constructor(A){if(this._client=A,this._buckets=new Map,this._bucketsTotalWeight=0,this._interval=setInterval(()=>this._flush(),T21.DEFAULT_FLUSH_INTERVAL),this._interval.unref)this._interval.unref();this._flushShift=Math.floor(Math.random()*T21.DEFAULT_FLUSH_INTERVAL/1000),this._forceFlush=!1}add(A,B,Q,D="none",Z={},G=Af0.timestampInSeconds()){let F=Math.floor(G),I=TJ1.sanitizeMetricKey(B),Y=TJ1.sanitizeTags(Z),W=TJ1.sanitizeUnit(D),J=TJ1.getBucketKey(A,I,W,Y),X=this._buckets.get(J),V=X&&A===T21.SET_METRIC_TYPE?X.metric.weight:0;if(X){if(X.metric.add(Q),X.timestamp<F)X.timestamp=F}else X={metric:new IF9.METRIC_MAP[A](Q),timestamp:F,metricType:A,name:I,unit:W,tags:Y},this._buckets.set(J,X);let C=typeof Q==="string"?X.metric.weight-V:Q;if(YF9.updateMetricSummaryOnActiveSpan(A,I,C,W,Z,J),this._bucketsTotalWeight+=X.metric.weight,this._bucketsTotalWeight>=T21.MAX_WEIGHT)this.flush()}flush(){this._forceFlush=!0,this._flush()}close(){this._forceFlush=!0,clearInterval(this._interval),this._flush()}_flush(){if(this._forceFlush){this._forceFlush=!1,this._bucketsTotalWeight=0,this._captureMetrics(this._buckets),this._buckets.clear();return}let A=Math.floor(Af0.timestampInSeconds())-T21.DEFAULT_FLUSH_INTERVAL/1000-this._flushShift,B=new Map;for(let[Q,D]of this._buckets)if(D.timestamp<=A)B.set(Q,D),this._bucketsTotalWeight-=D.metric.weight;for(let[Q]of B)this._buckets.delete(Q);this._captureMetrics(B)}_captureMetrics(A){if(A.size>0&&this._client.captureAggregateMetrics){let B=Array.from(A).map(([,Q])=>Q);this._client.captureAggregateMetrics(B)}}}Qf0.MetricsAggregator=Bf0});
var EX1=E((Ld0)=>{Object.defineProperty(Ld0,"__esModule",{value:!0});var zX1=SQ(),wd0=wA(),AU9=Cl1(),$d0="OnUnhandledRejection",BU9=(A={})=>{let B=A.mode||"warn";return{name:$d0,setupOnce(){},setup(Q){global.process.on("unhandledRejection",Nd0(Q,{mode:B}))}}},qd0=zX1.defineIntegration(BU9),QU9=zX1.convertIntegrationFnToClass($d0,qd0);function Nd0(A,B){return function Q(D,Z){if(zX1.getClient()!==A)return;zX1.captureException(D,{originalException:Z,captureContext:{extra:{unhandledPromiseRejection:!0}},mechanism:{handled:!1,type:"onunhandledrejection"}}),DU9(D,B)}}function DU9(A,B){let Q="This error originated either by throwing inside of an async function without a catch block, or by rejecting a promise which was not handled with .catch(). The promise rejected with the reason:";if(B.mode==="warn")wd0.consoleSandbox(()=>{console.warn(Q),console.error(A&&A.stack?A.stack:A)});else if(B.mode==="strict")wd0.consoleSandbox(()=>{console.warn(Q)}),AU9.logAndExitProcess(A)}Ld0.OnUnhandledRejection=QU9;Ld0.makeUnhandledPromiseHandler=Nd0;Ld0.onUnhandledRejectionIntegration=qd0});
var Ec0=E((zc0)=>{Object.defineProperty(zc0,"__esModule",{value:!0});var Of=SQ(),Tf=wA(),Kc0="CaptureConsole",P$9=(A={})=>{let B=A.levels||Tf.CONSOLE_LEVELS;return{name:Kc0,setupOnce(){},setup(Q){if(!("console"in Tf.GLOBAL_OBJ))return;Tf.addConsoleInstrumentationHandler(({args:D,level:Z})=>{if(Of.getClient()!==Q||!B.includes(Z))return;j$9(D,Z)})}}},Hc0=Of.defineIntegration(P$9),S$9=Of.convertIntegrationFnToClass(Kc0,Hc0);function j$9(A,B){let Q={level:Tf.severityLevelFromString(B),extra:{arguments:A}};Of.withScope((D)=>{if(D.addEventProcessor((F)=>{return F.logger="console",Tf.addExceptionMechanism(F,{handled:!1,type:"console"}),F}),B==="assert"&&A[0]===!1){let F=`Assertion failed: ${Tf.safeJoin(A.slice(1)," ")||"console.assert"}`;D.setExtra("arguments",A.slice(1)),Of.captureMessage(F,Q);return}let Z=A.find((F)=>F instanceof Error);if(B==="error"&&Z){Of.captureException(Z,Q);return}let G=Tf.safeJoin(A," ");Of.captureMessage(G,Q)})}zc0.CaptureConsole=S$9;zc0.captureConsoleIntegration=Hc0});
var FJ1=E((sv0)=>{Object.defineProperty(sv0,"__esModule",{value:!0});var L79="7.120.3";sv0.SDK_VERSION=L79});
var Fc1=E((_b0)=>{Object.defineProperty(_b0,"__esModule",{value:!0});var zl=wA();function RZ9(A,B){if(!B)return A;return A.sdk=A.sdk||{},A.sdk.name=A.sdk.name||B.name,A.sdk.version=A.sdk.version||B.version,A.sdk.integrations=[...A.sdk.integrations||[],...B.integrations||[]],A.sdk.packages=[...A.sdk.packages||[],...B.packages||[]],A}function OZ9(A,B,Q,D){let Z=zl.getSdkMetadataForEnvelopeHeader(Q),G={sent_at:new Date().toISOString(),...Z&&{sdk:Z},...!!D&&B&&{dsn:zl.dsnToString(B)}},F="aggregates"in A?[{type:"sessions"},A]:[{type:"session"},A.toJSON()];return zl.createEnvelope(G,[F])}function TZ9(A,B,Q,D){let Z=zl.getSdkMetadataForEnvelopeHeader(Q),G=A.type&&A.type!=="replay_event"?A.type:"event";RZ9(A,Q&&Q.sdk);let F=zl.createEventEnvelopeHeaders(A,Z,D,B);delete A.sdkProcessingMetadata;let I=[{type:G},A];return zl.createEnvelope(F,[I])}_b0.createEventEnvelope=TZ9;_b0.createSessionEnvelope=OZ9});
var Fd1=E((k_0)=>{Object.defineProperty(k_0,"__esModule",{value:!0});var Zd1=kW(),Gd1=QO(),hW1=null;function lA9(A){Gd1.addHandler("unhandledrejection",A),Gd1.maybeInstrument("unhandledrejection",pA9)}function pA9(){hW1=Zd1.GLOBAL_OBJ.onunhandledrejection,Zd1.GLOBAL_OBJ.onunhandledrejection=function(A){let B=A;if(Gd1.triggerHandlers("unhandledrejection",B),hW1&&!hW1.__SENTRY_LOADER__)return hW1.apply(this,arguments);return!0},Zd1.GLOBAL_OBJ.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}k_0.addGlobalUnhandledRejectionInstrumentationHandler=lA9});
var Fl=E((Ov0)=>{Object.defineProperty(Ov0,"__esModule",{value:!0});var H21=wA();function q59(A){let B=H21.timestampInSeconds(),Q={sid:H21.uuid4(),init:!0,timestamp:B,started:B,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>L59(Q)};if(A)hd1(Q,A);return Q}function hd1(A,B={}){if(B.user){if(!A.ipAddress&&B.user.ip_address)A.ipAddress=B.user.ip_address;if(!A.did&&!B.did)A.did=B.user.id||B.user.email||B.user.username}if(A.timestamp=B.timestamp||H21.timestampInSeconds(),B.abnormal_mechanism)A.abnormal_mechanism=B.abnormal_mechanism;if(B.ignoreDuration)A.ignoreDuration=B.ignoreDuration;if(B.sid)A.sid=B.sid.length===32?B.sid:H21.uuid4();if(B.init!==void 0)A.init=B.init;if(!A.did&&B.did)A.did=`${B.did}`;if(typeof B.started==="number")A.started=B.started;if(A.ignoreDuration)A.duration=void 0;else if(typeof B.duration==="number")A.duration=B.duration;else{let Q=A.timestamp-A.started;A.duration=Q>=0?Q:0}if(B.release)A.release=B.release;if(B.environment)A.environment=B.environment;if(!A.ipAddress&&B.ipAddress)A.ipAddress=B.ipAddress;if(!A.userAgent&&B.userAgent)A.userAgent=B.userAgent;if(typeof B.errors==="number")A.errors=B.errors;if(B.status)A.status=B.status}function N59(A,B){let Q={};if(B)Q={status:B};else if(A.status==="ok")Q={status:"exited"};hd1(A,Q)}function L59(A){return H21.dropUndefinedKeys({sid:`${A.sid}`,init:A.init,started:new Date(A.started*1000).toISOString(),timestamp:new Date(A.timestamp*1000).toISOString(),status:A.status,errors:A.errors,did:typeof A.did==="number"||typeof A.did==="string"?`${A.did}`:void 0,duration:A.duration,abnormal_mechanism:A.abnormal_mechanism,attrs:{release:A.release,environment:A.environment,ip_address:A.ipAddress,user_agent:A.userAgent}})}Ov0.closeSession=N59;Ov0.makeSession=q59;Ov0.updateSession=hd1});
var Fm0=E((Gm0)=>{Object.defineProperty(Gm0,"__esModule",{value:!0});var PH9=wA();function BX1(...A){PH9.logger.log("[https-proxy-agent:parse-proxy-response]",...A)}function SH9(A){return new Promise((B,Q)=>{let D=0,Z=[];function G(){let J=A.read();if(J)W(J);else A.once("readable",G)}function F(){A.removeListener("end",I),A.removeListener("error",Y),A.removeListener("readable",G)}function I(){F(),BX1("onend"),Q(new Error("Proxy connection ended before receiving CONNECT response"))}function Y(J){F(),BX1("onerror %o",J),Q(J)}function W(J){Z.push(J),D+=J.length;let X=Buffer.concat(Z,D),V=X.indexOf(`\r
\r
`);if(V===-1){BX1("have not received end of HTTP headers yet..."),G();return}let C=X.slice(0,V).toString("ascii").split(`\r
`),K=C.shift();if(!K)return A.destroy(),Q(new Error("No header received from proxy CONNECT response"));let H=K.split(" "),z=+H[1],$=H.slice(2).join(" "),L={};for(let N of C){if(!N)continue;let O=N.indexOf(":");if(O===-1)return A.destroy(),Q(new Error(`Invalid header from proxy CONNECT response: "${N}"`));let R=N.slice(0,O).toLowerCase(),T=N.slice(O+1).trimStart(),j=L[R];if(typeof j==="string")L[R]=[j,T];else if(Array.isArray(j))j.push(T);else L[R]=T}BX1("got proxy server response: %o %o",K,L),F(),B({connect:{statusCode:z,statusText:$,headers:L},buffered:X})}A.on("error",Y),A.on("end",I),G()})}Gm0.parseProxyResponse=SH9});
var GX1=E((bm0)=>{var{_optionalChain:qf}=wA();Object.defineProperty(bm0,"__esModule",{value:!0});var $z9=J1("child_process"),Pm0=J1("fs"),cH=J1("os"),qz9=J1("path"),Sm0=J1("util"),jm0=SQ(),ym0=Sm0.promisify(Pm0.readFile),km0=Sm0.promisify(Pm0.readdir),_m0="Context",Nz9=(A={})=>{let B,Q={app:!0,os:!0,device:!0,culture:!0,cloudResource:!0,...A};async function D(G){if(B===void 0)B=Z();let F=Mz9(await B);return G.contexts={...G.contexts,app:{...F.app,...qf([G,"access",(I)=>I.contexts,"optionalAccess",(I)=>I.app])},os:{...F.os,...qf([G,"access",(I)=>I.contexts,"optionalAccess",(I)=>I.os])},device:{...F.device,...qf([G,"access",(I)=>I.contexts,"optionalAccess",(I)=>I.device])},culture:{...F.culture,...qf([G,"access",(I)=>I.contexts,"optionalAccess",(I)=>I.culture])},cloud_resource:{...F.cloud_resource,...qf([G,"access",(I)=>I.contexts,"optionalAccess",(I)=>I.cloud_resource])}},G}async function Z(){let G={};if(Q.os)G.os=await Rz9();if(Q.app)G.app=Tz9();if(Q.device)G.device=vm0(Q.device);if(Q.culture){let F=Oz9();if(F)G.culture=F}if(Q.cloudResource)G.cloud_resource=_z9();return G}return{name:_m0,setupOnce(){},processEvent(G){return D(G)}}},xm0=jm0.defineIntegration(Nz9),Lz9=jm0.convertIntegrationFnToClass(_m0,xm0);function Mz9(A){if(qf([A,"optionalAccess",(B)=>B.app,"optionalAccess",(B)=>B.app_memory]))A.app.app_memory=process.memoryUsage().rss;if(qf([A,"optionalAccess",(B)=>B.device,"optionalAccess",(B)=>B.free_memory]))A.device.free_memory=cH.freemem();return A}async function Rz9(){let A=cH.platform();switch(A){case"darwin":return yz9();case"linux":return kz9();default:return{name:Pz9[A]||A,version:cH.release()}}}function Oz9(){try{if(typeof process.versions.icu!=="string")return;let A=new Date(900000000);if(new Intl.DateTimeFormat("es",{month:"long"}).format(A)==="enero"){let Q=Intl.DateTimeFormat().resolvedOptions();return{locale:Q.locale,timezone:Q.timeZone}}}catch(A){}return}function Tz9(){let A=process.memoryUsage().rss;return{app_start_time:new Date(Date.now()-process.uptime()*1000).toISOString(),app_memory:A}}function vm0(A){let B={},Q;try{Q=cH.uptime&&cH.uptime()}catch(D){}if(typeof Q==="number")B.boot_time=new Date(Date.now()-Q*1000).toISOString();if(B.arch=cH.arch(),A===!0||A.memory)B.memory_size=cH.totalmem(),B.free_memory=cH.freemem();if(A===!0||A.cpu){let D=cH.cpus();if(D&&D.length){let Z=D[0];B.processor_count=D.length,B.cpu_description=Z.model,B.processor_frequency=Z.speed}}return B}var Pz9={aix:"IBM AIX",freebsd:"FreeBSD",openbsd:"OpenBSD",sunos:"SunOS",win32:"Windows"},Sz9=[{name:"fedora-release",distros:["Fedora"]},{name:"redhat-release",distros:["Red Hat Linux","Centos"]},{name:"redhat_version",distros:["Red Hat Linux"]},{name:"SuSE-release",distros:["SUSE Linux"]},{name:"lsb-release",distros:["Ubuntu Linux","Arch Linux"]},{name:"debian_version",distros:["Debian"]},{name:"debian_release",distros:["Debian"]},{name:"arch-release",distros:["Arch Linux"]},{name:"gentoo-release",distros:["Gentoo Linux"]},{name:"novell-release",distros:["SUSE Linux"]},{name:"alpine-release",distros:["Alpine Linux"]}],jz9={alpine:(A)=>A,arch:(A)=>IN(/distrib_release=(.*)/,A),centos:(A)=>IN(/release ([^ ]+)/,A),debian:(A)=>A,fedora:(A)=>IN(/release (..)/,A),mint:(A)=>IN(/distrib_release=(.*)/,A),red:(A)=>IN(/release ([^ ]+)/,A),suse:(A)=>IN(/VERSION = (.*)\n/,A),ubuntu:(A)=>IN(/distrib_release=(.*)/,A)};function IN(A,B){let Q=A.exec(B);return Q?Q[1]:void 0}async function yz9(){let A={kernel_version:cH.release(),name:"Mac OS X",version:`10.${Number(cH.release().split(".")[0])-4}`};try{let B=await new Promise((Q,D)=>{$z9.execFile("/usr/bin/sw_vers",(Z,G)=>{if(Z){D(Z);return}Q(G)})});A.name=IN(/^ProductName:\s+(.*)$/m,B),A.version=IN(/^ProductVersion:\s+(.*)$/m,B),A.build=IN(/^BuildVersion:\s+(.*)$/m,B)}catch(B){}return A}function Tm0(A){return A.split(" ")[0].toLowerCase()}async function kz9(){let A={kernel_version:cH.release(),name:"Linux"};try{let B=await km0("/etc"),Q=Sz9.find((I)=>B.includes(I.name));if(!Q)return A;let D=qz9.join("/etc",Q.name),Z=(await ym0(D,{encoding:"utf-8"})).toLowerCase(),{distros:G}=Q;A.name=G.find((I)=>Z.indexOf(Tm0(I))>=0)||G[0];let F=Tm0(A.name);A.version=jz9[F](Z)}catch(B){}return A}function _z9(){if(process.env.VERCEL)return{"cloud.provider":"vercel","cloud.region":process.env.VERCEL_REGION};else if(process.env.AWS_REGION)return{"cloud.provider":"aws","cloud.region":process.env.AWS_REGION,"cloud.platform":process.env.AWS_EXECUTION_ENV};else if(process.env.GCP_PROJECT)return{"cloud.provider":"gcp"};else if(process.env.ALIYUN_REGION_ID)return{"cloud.provider":"alibaba_cloud","cloud.region":process.env.ALIYUN_REGION_ID};else if(process.env.WEBSITE_SITE_NAME&&process.env.REGION_NAME)return{"cloud.provider":"azure","cloud.region":process.env.REGION_NAME};else if(process.env.IBM_CLOUD_REGION)return{"cloud.provider":"ibm_cloud","cloud.region":process.env.IBM_CLOUD_REGION};else if(process.env.TENCENTCLOUD_REGION)return{"cloud.provider":"tencent_cloud","cloud.region":process.env.TENCENTCLOUD_REGION,"cloud.account.id":process.env.TENCENTCLOUD_APPID,"cloud.availability_zone":process.env.TENCENTCLOUD_ZONE};else if(process.env.NETLIFY)return{"cloud.provider":"netlify"};else if(process.env.FLY_REGION)return{"cloud.provider":"fly.io","cloud.region":process.env.FLY_REGION};else if(process.env.DYNO)return{"cloud.provider":"heroku"};else return}bm0.Context=Lz9;bm0.getDeviceContext=vm0;bm0.nodeContextIntegration=xm0;bm0.readDirAsync=km0;bm0.readFileAsync=ym0});
var Gc1=E((jb0)=>{Object.defineProperty(jb0,"__esModule",{value:!0});var WZ9=wA(),JZ9=kG(),XZ9=eq(),VZ9=BV(),CZ9=JJ1(),KZ9=Dc1(),Sb0=Zc1(),HZ9=wJ1();function zZ9(){let B=this.getScope().getSpan();return B?{"sentry-trace":VZ9.spanToTraceHeader(B)}:{}}function EZ9(A,B){let Q=this.getClient(),D=Q&&Q.getOptions()||{},Z=D.instrumenter||"sentry",G=A.instrumenter||"sentry";if(Z!==G)JZ9.DEBUG_BUILD&&WZ9.logger.error(`A transaction was started with instrumenter=\`${G}\`, but the SDK is configured with the \`${Z}\` instrumenter.
The transaction will not be sampled. Please use the ${Z} instrumentation to start transactions.`),A.sampled=!1;let F=new HZ9.Transaction(A,this);if(F=Sb0.sampleTransaction(F,D,{name:A.name,parentSampled:A.parentSampled,transactionContext:A,attributes:{...A.data,...A.attributes},...B}),F.isRecording())F.initSpanRecorder(D._experiments&&D._experiments.maxSpans);if(Q&&Q.emit)Q.emit("startTransaction",F);return F}function UZ9(A,B,Q,D,Z,G,F,I=!1){let Y=A.getClient(),W=Y&&Y.getOptions()||{},J=new KZ9.IdleTransaction(B,A,Q,D,F,Z,I);if(J=Sb0.sampleTransaction(J,W,{name:B.name,parentSampled:B.parentSampled,transactionContext:B,attributes:{...B.data,...B.attributes},...G}),J.isRecording())J.initSpanRecorder(W._experiments&&W._experiments.maxSpans);if(Y&&Y.emit)Y.emit("startTransaction",J);return J}function wZ9(){let A=XZ9.getMainCarrier();if(!A.__SENTRY__)return;if(A.__SENTRY__.extensions=A.__SENTRY__.extensions||{},!A.__SENTRY__.extensions.startTransaction)A.__SENTRY__.extensions.startTransaction=EZ9;if(!A.__SENTRY__.extensions.traceHeaders)A.__SENTRY__.extensions.traceHeaders=zZ9;CZ9.registerErrorInstrumentation()}jb0.addTracingExtensions=wZ9;jb0.startIdleTransaction=UZ9});
var Gl=E((Lv0)=>{Object.defineProperty(Lv0,"__esModule",{value:!0});var K59="production";Lv0.DEFAULT_ENVIRONMENT=K59});
var Gl0=E((Zl0)=>{Object.defineProperty(Zl0,"__esModule",{value:!0});var Bl0=SQ(),Ql0="SessionTiming",Cq9=()=>{let A=Date.now();return{name:Ql0,setupOnce(){},processEvent(B){let Q=Date.now();return{...B,extra:{...B.extra,["session:start"]:A,["session:duration"]:Q-A,["session:end"]:Q}}}}},Dl0=Bl0.defineIntegration(Cq9),Kq9=Bl0.convertIntegrationFnToClass(Ql0,Dl0);Zl0.SessionTiming=Kq9;Zl0.sessionTimingIntegration=Dl0});
var HJ1=E((Cb0)=>{Object.defineProperty(Cb0,"__esModule",{value:!0});var $21=wA(),XD9=kG(),uj=eq(),VJ1=BV();JJ1();Jl();var VD9=Yf(),Xl=mH(),Ac1=ed1(),Yb0=XJ1();function CD9(A,B,Q=()=>{},D=()=>{}){let Z=uj.getCurrentHub(),G=Xl.getCurrentScope(),F=G.getSpan(),I=KJ1(A),Y=CJ1(Z,{parentSpan:F,spanContext:I,forceTransaction:!1,scope:G});return G.setSpan(Y),Ac1.handleCallbackErrors(()=>B(Y),(W)=>{Y&&Y.setStatus("internal_error"),Q(W,Y)},()=>{Y&&Y.end(),G.setSpan(F),D()})}function Wb0(A,B){let Q=KJ1(A);return uj.runWithAsyncContext(()=>{return Xl.withScope(A.scope,(D)=>{let Z=uj.getCurrentHub(),G=D.getSpan(),I=A.onlyIfParent&&!G?void 0:CJ1(Z,{parentSpan:G,spanContext:Q,forceTransaction:A.forceTransaction,scope:D});return Ac1.handleCallbackErrors(()=>B(I),()=>{if(I){let{status:Y}=VJ1.spanToJSON(I);if(!Y||Y==="ok")I.setStatus("internal_error")}},()=>I&&I.end())})})}var KD9=Wb0;function HD9(A,B){let Q=KJ1(A);return uj.runWithAsyncContext(()=>{return Xl.withScope(A.scope,(D)=>{let Z=uj.getCurrentHub(),G=D.getSpan(),I=A.onlyIfParent&&!G?void 0:CJ1(Z,{parentSpan:G,spanContext:Q,forceTransaction:A.forceTransaction,scope:D});function Y(){I&&I.end()}return Ac1.handleCallbackErrors(()=>B(I,Y),()=>{if(I&&I.isRecording()){let{status:W}=VJ1.spanToJSON(I);if(!W||W==="ok")I.setStatus("internal_error")}})})})}function zD9(A){if(!Yb0.hasTracingEnabled())return;let B=KJ1(A),Q=uj.getCurrentHub(),D=A.scope?A.scope.getSpan():Jb0();if(A.onlyIfParent&&!D)return;let F=(A.scope||Xl.getCurrentScope()).clone();return CJ1(Q,{parentSpan:D,spanContext:B,forceTransaction:A.forceTransaction,scope:F})}function Jb0(){return Xl.getCurrentScope().getSpan()}var ED9=({sentryTrace:A,baggage:B},Q)=>{let D=Xl.getCurrentScope(),{traceparentData:Z,dynamicSamplingContext:G,propagationContext:F}=$21.tracingContextFromHeaders(A,B);if(D.setPropagationContext(F),XD9.DEBUG_BUILD&&Z)$21.logger.log(`[Tracing] Continuing trace ${Z.traceId}.`);let I={...Z,metadata:$21.dropUndefinedKeys({dynamicSamplingContext:G})};if(!Q)return I;return uj.runWithAsyncContext(()=>{return Q(I)})};function CJ1(A,{parentSpan:B,spanContext:Q,forceTransaction:D,scope:Z}){if(!Yb0.hasTracingEnabled())return;let G=uj.getIsolationScope(),F;if(B&&!D)F=B.startChild(Q);else if(B){let I=VD9.getDynamicSamplingContextFromSpan(B),{traceId:Y,spanId:W}=B.spanContext(),J=VJ1.spanIsSampled(B);F=A.startTransaction({traceId:Y,parentSpanId:W,parentSampled:J,...Q,metadata:{dynamicSamplingContext:I,...Q.metadata}})}else{let{traceId:I,dsc:Y,parentSpanId:W,sampled:J}={...G.getPropagationContext(),...Z.getPropagationContext()};F=A.startTransaction({traceId:I,parentSpanId:W,parentSampled:J,...Q,metadata:{dynamicSamplingContext:Y,...Q.metadata}})}return Z.setSpan(F),UD9(F,Z,G),F}function KJ1(A){if(A.startTime){let B={...A};return B.startTimestamp=VJ1.spanTimeInputToSeconds(A.startTime),delete B.startTime,B}return A}var Xb0="_sentryScope",Vb0="_sentryIsolationScope";function UD9(A,B,Q){if(A)$21.addNonEnumerableProperty(A,Vb0,Q),$21.addNonEnumerableProperty(A,Xb0,B)}function wD9(A){return{scope:A[Xb0],isolationScope:A[Vb0]}}Cb0.continueTrace=ED9;Cb0.getActiveSpan=Jb0;Cb0.getCapturedScopesOnSpan=wD9;Cb0.startActiveSpan=KD9;Cb0.startInactiveSpan=zD9;Cb0.startSpan=Wb0;Cb0.startSpanManual=HD9;Cb0.trace=CD9});
var HX1=E((Ud0)=>{Object.defineProperty(Ud0,"__esModule",{value:!0});var KX1=SQ(),nE9=wA(),aE9=l21(),Kd0=Cl1(),Hd0="OnUncaughtException",sE9=(A={})=>{let B={exitEvenIfOtherHandlersAreRegistered:!0,...A};return{name:Hd0,setupOnce(){},setup(Q){global.process.on("uncaughtException",Ed0(Q,B))}}},zd0=KX1.defineIntegration(sE9),rE9=KX1.convertIntegrationFnToClass(Hd0,zd0);function Ed0(A,B){let D=!1,Z=!1,G=!1,F,I=A.getOptions();return Object.assign((Y)=>{let W=Kd0.logAndExitProcess;if(B.onFatalError)W=B.onFatalError;else if(I.onFatalError)W=I.onFatalError;let X=global.process.listeners("uncaughtException").reduce((C,K)=>{if(K.name==="domainUncaughtExceptionClear"||K.tag&&K.tag==="sentry_tracingErrorCallback"||K._errorHandler)return C;else return C+1},0)===0,V=B.exitEvenIfOtherHandlersAreRegistered||X;if(!D){if(F=Y,D=!0,KX1.getClient()===A)KX1.captureException(Y,{originalException:Y,captureContext:{level:"fatal"},mechanism:{handled:!1,type:"onuncaughtexception"}});if(!G&&V)G=!0,W(Y)}else if(V){if(G)aE9.DEBUG_BUILD&&nE9.logger.warn("uncaught exception after calling fatal error shutdown callback - this is bad! forcing shutdown"),Kd0.logAndExitProcess(Y);else if(!Z)Z=!0,setTimeout(()=>{if(!G)G=!0,W(F,Y)},2000)}},{_errorHandler:!0})}Ud0.OnUncaughtException=rE9;Ud0.makeErrorHandler=Ed0;Ud0.onUncaughtExceptionIntegration=zd0});
var Hf=E((yg0)=>{Object.defineProperty(yg0,"__esModule",{value:!0});var iX9=(A,B,Q)=>{try{if(PerformanceObserver.supportedEntryTypes.includes(A)){let D=new PerformanceObserver((Z)=>{B(Z.getEntries())});return D.observe(Object.assign({type:A,buffered:!0},Q||{})),D}}catch(D){}return};yg0.observe=iX9});
var Hl1=E((fd0)=>{var{_optionalChain:TU9}=wA();Object.defineProperty(fd0,"__esModule",{value:!0});var lH=SQ(),Mf=wA(),PU9=Nm0(),SU9=ec1(),jU9=ZX1(),yU9=GX1(),kU9=IX1(),_U9=YX1(),xU9=XX1(),vU9=VX1(),bU9=HX1(),fU9=EX1(),hU9=UX1(),gU9=$X1(),uU9=Kl1(),mU9=Ql1(),_d0=[lH.inboundFiltersIntegration(),lH.functionToStringIntegration(),lH.linkedErrorsIntegration(),lH.requestDataIntegration(),jU9.consoleIntegration(),_U9.httpIntegration(),gU9.nativeNodeFetchintegration(),bU9.onUncaughtExceptionIntegration(),fU9.onUnhandledRejectionIntegration(),kU9.contextLinesIntegration(),xU9.localVariablesIntegration(),yU9.nodeContextIntegration(),vU9.modulesIntegration()];function xd0(A){let B=lH.getMainCarrier(),Q=TU9([B,"access",(D)=>D.__SENTRY__,"optionalAccess",(D)=>D.integrations])||[];return[..._d0,...Q]}function dU9(A={}){if(PU9.setNodeAsyncContextStrategy(),A.defaultIntegrations===void 0)A.defaultIntegrations=xd0();if(A.dsn===void 0&&process.env.SENTRY_DSN)A.dsn=process.env.SENTRY_DSN;let B=process.env.SENTRY_TRACES_SAMPLE_RATE;if(A.tracesSampleRate===void 0&&B){let D=parseFloat(B);if(isFinite(D))A.tracesSampleRate=D}if(A.release===void 0){let D=vd0();if(D!==void 0)A.release=D;else A.autoSessionTracking=!1}if(A.environment===void 0&&process.env.SENTRY_ENVIRONMENT)A.environment=process.env.SENTRY_ENVIRONMENT;if(A.autoSessionTracking===void 0&&A.dsn!==void 0)A.autoSessionTracking=!0;if(A.instrumenter===void 0)A.instrumenter="sentry";let Q={...A,stackParser:Mf.stackParserFromStackParserOptions(A.stackParser||bd0),integrations:lH.getIntegrationsToSetup(A),transport:A.transport||mU9.makeNodeTransport};if(lH.initAndBind(A.clientClass||SU9.NodeClient,Q),A.autoSessionTracking)lU9();if(pU9(),A.spotlight){let D=lH.getClient();if(D&&D.addIntegration){let Z=D.getOptions().integrations;for(let G of Z)D.addIntegration(G);D.addIntegration(hU9.spotlightIntegration({sidecarUrl:typeof A.spotlight==="string"?A.spotlight:void 0}))}}}function cU9(A){if(A===void 0)return!1;let B=A&&A.getOptions();if(B&&B.autoSessionTracking!==void 0)return B.autoSessionTracking;return!1}function vd0(A){if(process.env.SENTRY_RELEASE)return process.env.SENTRY_RELEASE;if(Mf.GLOBAL_OBJ.SENTRY_RELEASE&&Mf.GLOBAL_OBJ.SENTRY_RELEASE.id)return Mf.GLOBAL_OBJ.SENTRY_RELEASE.id;return process.env.GITHUB_SHA||process.env.COMMIT_REF||process.env.VERCEL_GIT_COMMIT_SHA||process.env.VERCEL_GITHUB_COMMIT_SHA||process.env.VERCEL_GITLAB_COMMIT_SHA||process.env.VERCEL_BITBUCKET_COMMIT_SHA||process.env.ZEIT_GITHUB_COMMIT_SHA||process.env.ZEIT_GITLAB_COMMIT_SHA||process.env.ZEIT_BITBUCKET_COMMIT_SHA||process.env.CF_PAGES_COMMIT_SHA||A}var bd0=Mf.createStackParser(Mf.nodeStackLineParser(uU9.createGetModuleFromFilename()));function lU9(){lH.startSession(),process.on("beforeExit",()=>{let A=lH.getIsolationScope().getSession();if(A&&!["exited","crashed"].includes(A.status))lH.endSession()})}function pU9(){let A=(process.env.SENTRY_USE_ENVIRONMENT||"").toLowerCase();if(!["false","n","no","off","0"].includes(A)){let B=process.env.SENTRY_TRACE,Q=process.env.SENTRY_BAGGAGE,D=Mf.propagationContextFromHeaders(B,Q);lH.getCurrentScope().setPropagationContext(D)}}fd0.defaultIntegrations=_d0;fd0.defaultStackParser=bd0;fd0.getDefaultIntegrations=xd0;fd0.getSentryRelease=vd0;fd0.init=dU9;fd0.isAutoSessionTrackingEnabled=cU9});
var I21=E((Ax0)=>{Object.defineProperty(Ax0,"__esModule",{value:!0});var Kd1=hH(),b29=Cd1(),f29=gH(),h29=_W1();function t_0(A,B=100,Q=1/0){try{return nW1("",A,B,Q)}catch(D){return{ERROR:`**non-serializable** (${D})`}}}function e_0(A,B=3,Q=102400){let D=t_0(A,B);if(d29(D)>Q)return e_0(A,B-1,Q);return D}function nW1(A,B,Q=1/0,D=1/0,Z=b29.memoBuilder()){let[G,F]=Z;if(B==null||["number","boolean","string"].includes(typeof B)&&!Kd1.isNaN(B))return B;let I=g29(A,B);if(!I.startsWith("[object "))return I;if(B.__sentry_skip_normalization__)return B;let Y=typeof B.__sentry_override_normalization_depth__==="number"?B.__sentry_override_normalization_depth__:Q;if(Y===0)return I.replace("object ","");if(G(B))return"[Circular ~]";let W=B;if(W&&typeof W.toJSON==="function")try{let C=W.toJSON();return nW1("",C,Y-1,D,Z)}catch(C){}let J=Array.isArray(B)?[]:{},X=0,V=f29.convertToPlainObject(B);for(let C in V){if(!Object.prototype.hasOwnProperty.call(V,C))continue;if(X>=D){J[C]="[MaxProperties ~]";break}let K=V[C];J[C]=nW1(C,K,Y-1,D,Z),X++}return F(B),J}function g29(A,B){try{if(A==="domain"&&B&&typeof B==="object"&&B._events)return"[Domain]";if(A==="domainEmitter")return"[DomainEmitter]";if(typeof global!=="undefined"&&B===global)return"[Global]";if(typeof window!=="undefined"&&B===window)return"[Window]";if(typeof document!=="undefined"&&B===document)return"[Document]";if(Kd1.isVueViewModel(B))return"[VueViewModel]";if(Kd1.isSyntheticEvent(B))return"[SyntheticEvent]";if(typeof B==="number"&&B!==B)return"[NaN]";if(typeof B==="function")return`[Function: ${h29.getFunctionName(B)}]`;if(typeof B==="symbol")return`[${String(B)}]`;if(typeof B==="bigint")return`[BigInt: ${String(B)}]`;let Q=u29(B);if(/^HTML(\w*)Element$/.test(Q))return`[HTMLElement: ${Q}]`;return`[object ${Q}]`}catch(Q){return`**non-serializable** (${Q})`}}function u29(A){let B=Object.getPrototypeOf(A);return B?B.constructor.name:"null prototype"}function m29(A){return~-encodeURI(A).split(/%..|./).length}function d29(A){return m29(JSON.stringify(A))}function c29(A,B){let Q=B.replace(/\\/g,"/").replace(/[|\\{}()[\]^$+*?.]/g,"\\$&"),D=A;try{D=decodeURI(A)}catch(Z){}return D.replace(/\\/g,"/").replace(/webpack:\/?/g,"").replace(new RegExp(`(file://)?/*${Q}/*`,"ig"),"app:///")}Ax0.normalize=t_0;Ax0.normalizeToSize=e_0;Ax0.normalizeUrlToBase=c29;Ax0.walk=nW1});
var IO=E((gb0)=>{Object.defineProperty(gb0,"__esModule",{value:!0});var MJ1=wA(),Wc1=kG(),uZ9=K21(),mZ9=mH(),dZ9=eq(),Jc1=[];function cZ9(A){let B={};return A.forEach((Q)=>{let{name:D}=Q,Z=B[D];if(Z&&!Z.isDefaultInstance&&Q.isDefaultInstance)return;B[D]=Q}),Object.keys(B).map((Q)=>B[Q])}function lZ9(A){let B=A.defaultIntegrations||[],Q=A.integrations;B.forEach((F)=>{F.isDefaultInstance=!0});let D;if(Array.isArray(Q))D=[...B,...Q];else if(typeof Q==="function")D=MJ1.arrayify(Q(B));else D=B;let Z=cZ9(D),G=aZ9(Z,(F)=>F.name==="Debug");if(G!==-1){let[F]=Z.splice(G,1);Z.push(F)}return Z}function pZ9(A,B){let Q={};return B.forEach((D)=>{if(D)hb0(A,D,Q)}),Q}function iZ9(A,B){for(let Q of B)if(Q&&Q.afterAllSetup)Q.afterAllSetup(A)}function hb0(A,B,Q){if(Q[B.name]){Wc1.DEBUG_BUILD&&MJ1.logger.log(`Integration skipped because it was already installed: ${B.name}`);return}if(Q[B.name]=B,Jc1.indexOf(B.name)===-1)B.setupOnce(uZ9.addGlobalEventProcessor,dZ9.getCurrentHub),Jc1.push(B.name);if(B.setup&&typeof B.setup==="function")B.setup(A);if(A.on&&typeof B.preprocessEvent==="function"){let D=B.preprocessEvent.bind(B);A.on("preprocessEvent",(Z,G)=>D(Z,G,A))}if(A.addEventProcessor&&typeof B.processEvent==="function"){let D=B.processEvent.bind(B),Z=Object.assign((G,F)=>D(G,F,A),{id:B.name});A.addEventProcessor(Z)}Wc1.DEBUG_BUILD&&MJ1.logger.log(`Integration installed: ${B.name}`)}function nZ9(A){let B=mZ9.getClient();if(!B||!B.addIntegration){Wc1.DEBUG_BUILD&&MJ1.logger.warn(`Cannot add integration "${A.name}" because no SDK Client is available.`);return}B.addIntegration(A)}function aZ9(A,B){for(let Q=0;Q<A.length;Q++)if(B(A[Q])===!0)return Q;return-1}function sZ9(A,B){return Object.assign(function Q(...D){return B(...D)},{id:A})}function rZ9(A){return A}gb0.addIntegration=nZ9;gb0.afterSetupIntegrations=iZ9;gb0.convertIntegrationFnToClass=sZ9;gb0.defineIntegration=rZ9;gb0.getIntegrationsToSetup=lZ9;gb0.installedIntegrations=Jc1;gb0.setupIntegration=hb0;gb0.setupIntegrations=pZ9});
var IX1=E((mm0)=>{var{_optionalChain:Zl1}=wA();Object.defineProperty(mm0,"__esModule",{value:!0});var gz9=J1("fs"),fm0=SQ(),hm0=wA(),FX1=new hm0.LRUMap(100),uz9=7,gm0="ContextLines";function mz9(A){return new Promise((B,Q)=>{gz9.readFile(A,"utf8",(D,Z)=>{if(D)Q(D);else B(Z)})})}var dz9=(A={})=>{let B=A.frameContextLines!==void 0?A.frameContextLines:uz9;return{name:gm0,setupOnce(){},processEvent(Q){return lz9(Q,B)}}},um0=fm0.defineIntegration(dz9),cz9=fm0.convertIntegrationFnToClass(gm0,um0);async function lz9(A,B){let Q={},D=[];if(B>0&&Zl1([A,"access",(Z)=>Z.exception,"optionalAccess",(Z)=>Z.values]))for(let Z of A.exception.values){if(!Zl1([Z,"access",(G)=>G.stacktrace,"optionalAccess",(G)=>G.frames]))continue;for(let G=Z.stacktrace.frames.length-1;G>=0;G--){let F=Z.stacktrace.frames[G];if(F.filename&&!Q[F.filename]&&!FX1.get(F.filename))D.push(iz9(F.filename)),Q[F.filename]=1}}if(D.length>0)await Promise.all(D);if(B>0&&Zl1([A,"access",(Z)=>Z.exception,"optionalAccess",(Z)=>Z.values])){for(let Z of A.exception.values)if(Z.stacktrace&&Z.stacktrace.frames)await pz9(Z.stacktrace.frames,B)}return A}function pz9(A,B){for(let Q of A)if(Q.filename&&Q.context_line===void 0){let D=FX1.get(Q.filename);if(D)try{hm0.addContextToFrame(D,Q,B)}catch(Z){}}}async function iz9(A){let B=FX1.get(A);if(B===null)return null;if(B!==void 0)return B;let Q=null;try{Q=(await mz9(A)).split(`
`)}catch(D){}return FX1.set(A,Q),Q}mm0.ContextLines=cz9;mm0.contextLinesIntegration=um0});
var Ic1=E((vb0)=>{Object.defineProperty(vb0,"__esModule",{value:!0});var jZ9=wA(),yZ9=mH();class xb0{constructor(A,B){if(this._client=A,this.flushTimeout=60,this._pendingAggregates={},this._isEnabled=!0,this._intervalId=setInterval(()=>this.flush(),this.flushTimeout*1000),this._intervalId.unref)this._intervalId.unref();this._sessionAttrs=B}flush(){let A=this.getSessionAggregates();if(A.aggregates.length===0)return;this._pendingAggregates={},this._client.sendSession(A)}getSessionAggregates(){let A=Object.keys(this._pendingAggregates).map((Q)=>{return this._pendingAggregates[parseInt(Q)]}),B={attrs:this._sessionAttrs,aggregates:A};return jZ9.dropUndefinedKeys(B)}close(){clearInterval(this._intervalId),this._isEnabled=!1,this.flush()}incrementSessionStatusCount(){if(!this._isEnabled)return;let A=yZ9.getCurrentScope(),B=A.getRequestSession();if(B&&B.status)this._incrementSessionStatusCount(B.status,new Date),A.setRequestSession(void 0)}_incrementSessionStatusCount(A,B){let Q=new Date(B).setSeconds(0,0);this._pendingAggregates[Q]=this._pendingAggregates[Q]||{};let D=this._pendingAggregates[Q];if(!D.started)D.started=new Date(Q).toISOString();switch(A){case"errored":return D.errored=(D.errored||0)+1,D.errored;case"ok":return D.exited=(D.exited||0)+1,D.exited;default:return D.crashed=(D.crashed||0)+1,D.crashed}}}vb0.SessionFlusher=xb0});
var Id1=E((__0)=>{Object.defineProperty(__0,"__esModule",{value:!0});var nA9=kW(),gW1=nA9.getGlobalObject();function aA9(){let A=gW1.chrome,B=A&&A.app&&A.app.runtime,Q="history"in gW1&&!!gW1.history.pushState&&!!gW1.history.replaceState;return!B&&Q}__0.supportsHistory=aA9});
var If0=E((Ff0)=>{Object.defineProperty(Ff0,"__esModule",{value:!0});var YO=wA(),JF9=Xc1(),XF9=Cc1(),PJ1=kG(),VF9=mH(),CF9=Df0(),KF9=Ic1(),HF9=Gc1(),zF9=BV(),EF9=Il();Jl();var Zf0=Yf();class Gf0 extends JF9.BaseClient{constructor(A){HF9.addTracingExtensions();super(A);if(A._experiments&&A._experiments.metricsAggregator)this.metricsAggregator=new CF9.MetricsAggregator(this)}eventFromException(A,B){return YO.resolvedSyncPromise(YO.eventFromUnknownInput(VF9.getClient(),this._options.stackParser,A,B))}eventFromMessage(A,B="info",Q){return YO.resolvedSyncPromise(YO.eventFromMessage(this._options.stackParser,A,B,Q,this._options.attachStacktrace))}captureException(A,B,Q){if(this._options.autoSessionTracking&&this._sessionFlusher&&Q){let D=Q.getRequestSession();if(D&&D.status==="ok")D.status="errored"}return super.captureException(A,B,Q)}captureEvent(A,B,Q){if(this._options.autoSessionTracking&&this._sessionFlusher&&Q){if((A.type||"exception")==="exception"&&A.exception&&A.exception.values&&A.exception.values.length>0){let G=Q.getRequestSession();if(G&&G.status==="ok")G.status="errored"}}return super.captureEvent(A,B,Q)}close(A){if(this._sessionFlusher)this._sessionFlusher.close();return super.close(A)}initSessionFlusher(){let{release:A,environment:B}=this._options;if(!A)PJ1.DEBUG_BUILD&&YO.logger.warn("Cannot initialise an instance of SessionFlusher if no release is provided!");else this._sessionFlusher=new KF9.SessionFlusher(this,{release:A,environment:B})}captureCheckIn(A,B,Q){let D="checkInId"in A&&A.checkInId?A.checkInId:YO.uuid4();if(!this._isEnabled())return PJ1.DEBUG_BUILD&&YO.logger.warn("SDK not enabled, will not capture checkin."),D;let Z=this.getOptions(),{release:G,environment:F,tunnel:I}=Z,Y={check_in_id:D,monitor_slug:A.monitorSlug,status:A.status,release:G,environment:F};if("duration"in A)Y.duration=A.duration;if(B)Y.monitor_config={schedule:B.schedule,checkin_margin:B.checkinMargin,max_runtime:B.maxRuntime,timezone:B.timezone};let[W,J]=this._getTraceInfoFromScope(Q);if(J)Y.contexts={trace:J};let X=XF9.createCheckInEnvelope(Y,W,this.getSdkMetadata(),I,this.getDsn());return PJ1.DEBUG_BUILD&&YO.logger.info("Sending checkin:",A.monitorSlug,A.status),this._sendEnvelope(X),D}_captureRequestSession(){if(!this._sessionFlusher)PJ1.DEBUG_BUILD&&YO.logger.warn("Discarded request mode session because autoSessionTracking option was disabled");else this._sessionFlusher.incrementSessionStatusCount()}_prepareEvent(A,B,Q,D){if(this._options.platform)A.platform=A.platform||this._options.platform;if(this._options.runtime)A.contexts={...A.contexts,runtime:(A.contexts||{}).runtime||this._options.runtime};if(this._options.serverName)A.server_name=A.server_name||this._options.serverName;return super._prepareEvent(A,B,Q,D)}_getTraceInfoFromScope(A){if(!A)return[void 0,void 0];let B=A.getSpan();if(B)return[EF9.getRootSpan(B)?Zf0.getDynamicSamplingContextFromSpan(B):void 0,zF9.spanToTraceContext(B)];let{traceId:Q,spanId:D,parentSpanId:Z,dsc:G}=A.getPropagationContext(),F={trace_id:Q,span_id:D,parent_span_id:Z};if(G)return[G,F];return[Zf0.getDynamicSamplingContextFromClient(Q,this,A),F]}}Ff0.ServerRuntimeClient=Gf0});
var Ig0=E((Fg0)=>{var{_optionalChain:dj}=wA();Object.defineProperty(Fg0,"__esModule",{value:!0});var j21=wA(),Gg0=ZV(),JX9=mj(),XX9=["aggregate","bulkWrite","countDocuments","createIndex","createIndexes","deleteMany","deleteOne","distinct","drop","dropIndex","dropIndexes","estimatedDocumentCount","find","findOne","findOneAndDelete","findOneAndReplace","findOneAndUpdate","indexes","indexExists","indexInformation","initializeOrderedBulkOp","insertMany","insertOne","isCapped","mapReduce","options","parallelCollectionScan","rename","replaceOne","stats","updateMany","updateOne"],VX9={bulkWrite:["operations"],countDocuments:["query"],createIndex:["fieldOrSpec"],createIndexes:["indexSpecs"],deleteMany:["filter"],deleteOne:["filter"],distinct:["key","query"],dropIndex:["indexName"],find:["query"],findOne:["query"],findOneAndDelete:["filter"],findOneAndReplace:["filter","replacement"],findOneAndUpdate:["filter","update"],indexExists:["indexes"],insertMany:["docs"],insertOne:["doc"],mapReduce:["map","reduce"],rename:["newName"],replaceOne:["filter","doc"],updateMany:["filter","update"],updateOne:["filter","update"]};function CX9(A){return A&&typeof A==="object"&&A.once&&typeof A.once==="function"}class uJ1{static __initStatic(){this.id="Mongo"}constructor(A={}){this.name=uJ1.id,this._operations=Array.isArray(A.operations)?A.operations:XX9,this._describeOperations="describeOperations"in A?A.describeOperations:!0,this._useMongoose=!!A.useMongoose}loadDependency(){let A=this._useMongoose?"mongoose":"mongodb";return this._module=this._module||j21.loadModule(A)}setupOnce(A,B){if(JX9.shouldDisableAutoInstrumentation(B)){Gg0.DEBUG_BUILD&&j21.logger.log("Mongo Integration is skipped because of instrumenter configuration.");return}let Q=this.loadDependency();if(!Q){let D=this._useMongoose?"mongoose":"mongodb";Gg0.DEBUG_BUILD&&j21.logger.error(`Mongo Integration was unable to require \`${D}\` package.`);return}this._instrumentOperations(Q.Collection,this._operations,B)}_instrumentOperations(A,B,Q){B.forEach((D)=>this._patchOperation(A,D,Q))}_patchOperation(A,B,Q){if(!(B in A.prototype))return;let D=this._getSpanContextFromOperationArguments.bind(this);j21.fill(A.prototype,B,function(Z){return function(...G){let F=G[G.length-1],I=Q(),Y=I.getScope(),W=I.getClient(),J=Y.getSpan(),X=dj([W,"optionalAccess",(C)=>C.getOptions,"call",(C)=>C(),"access",(C)=>C.sendDefaultPii]);if(typeof F!=="function"||B==="mapReduce"&&G.length===2){let C=dj([J,"optionalAccess",(H)=>H.startChild,"call",(H)=>H(D(this,B,G,X))]),K=Z.call(this,...G);if(j21.isThenable(K))return K.then((H)=>{return dj([C,"optionalAccess",(z)=>z.end,"call",(z)=>z()]),H});else if(CX9(K)){let H=K;try{H.once("close",()=>{dj([C,"optionalAccess",(z)=>z.end,"call",(z)=>z()])})}catch(z){dj([C,"optionalAccess",($)=>$.end,"call",($)=>$()])}return H}else return dj([C,"optionalAccess",(H)=>H.end,"call",(H)=>H()]),K}let V=dj([J,"optionalAccess",(C)=>C.startChild,"call",(C)=>C(D(this,B,G.slice(0,-1)))]);return Z.call(this,...G.slice(0,-1),function(C,K){dj([V,"optionalAccess",(H)=>H.end,"call",(H)=>H()]),F(C,K)})}})}_getSpanContextFromOperationArguments(A,B,Q,D=!1){let Z={"db.system":"mongodb","db.name":A.dbName,"db.operation":B,"db.mongodb.collection":A.collectionName},G={op:"db",origin:"auto.db.mongo",description:B,data:Z},F=VX9[B],I=Array.isArray(this._describeOperations)?this._describeOperations.includes(B):this._describeOperations;if(!F||!I||!D)return G;try{if(B==="mapReduce"){let[Y,W]=Q;Z[F[0]]=typeof Y==="string"?Y:Y.name||"<anonymous>",Z[F[1]]=typeof W==="string"?W:W.name||"<anonymous>"}else for(let Y=0;Y<F.length;Y++)Z[`db.mongodb.${F[Y]}`]=JSON.stringify(Q[Y])}catch(Y){}return G}}uJ1.__initStatic();Fg0.Mongo=uJ1});
var Il=E((uv0)=>{Object.defineProperty(uv0,"__esModule",{value:!0});function s39(A){return A.transaction}uv0.getRootSpan=s39});
var Iv0=E((Fv0)=>{Object.defineProperty(Fv0,"__esModule",{value:!0});class Gv0{constructor(A){this._maxSize=A,this._cache=new Map}get size(){return this._cache.size}get(A){let B=this._cache.get(A);if(B===void 0)return;return this._cache.delete(A),this._cache.set(A,B),B}set(A,B){if(this._cache.size>=this._maxSize)this._cache.delete(this._cache.keys().next().value);this._cache.set(A,B)}remove(A){let B=this._cache.get(A);if(B)this._cache.delete(A);return B}clear(){this._cache.clear()}keys(){return Array.from(this._cache.keys())}values(){let A=[];return this._cache.forEach((B)=>A.push(B)),A}}Fv0.LRUMap=Gv0});
var Ix0=E((Fx0)=>{Object.defineProperty(Fx0,"__esModule",{value:!0});function Qx0(A,B){let Q=0;for(let D=A.length-1;D>=0;D--){let Z=A[D];if(Z===".")A.splice(D,1);else if(Z==="..")A.splice(D,1),Q++;else if(Q)A.splice(D,1),Q--}if(B)for(;Q--;Q)A.unshift("..");return A}var a29=/^(\S+:\\|\/?)([\s\S]*?)((?:\.{1,2}|[^/\\]+?|)(\.[^./\\]*|))(?:[/\\]*)$/;function Dx0(A){let B=A.length>1024?`<truncated>${A.slice(-1024)}`:A,Q=a29.exec(B);return Q?Q.slice(1):[]}function Hd1(...A){let B="",Q=!1;for(let D=A.length-1;D>=-1&&!Q;D--){let Z=D>=0?A[D]:"/";if(!Z)continue;B=`${Z}/${B}`,Q=Z.charAt(0)==="/"}return B=Qx0(B.split("/").filter((D)=>!!D),!Q).join("/"),(Q?"/":"")+B||"."}function Bx0(A){let B=0;for(;B<A.length;B++)if(A[B]!=="")break;let Q=A.length-1;for(;Q>=0;Q--)if(A[Q]!=="")break;if(B>Q)return[];return A.slice(B,Q-B+1)}function s29(A,B){A=Hd1(A).slice(1),B=Hd1(B).slice(1);let Q=Bx0(A.split("/")),D=Bx0(B.split("/")),Z=Math.min(Q.length,D.length),G=Z;for(let I=0;I<Z;I++)if(Q[I]!==D[I]){G=I;break}let F=[];for(let I=G;I<Q.length;I++)F.push("..");return F=F.concat(D.slice(G)),F.join("/")}function Zx0(A){let B=Gx0(A),Q=A.slice(-1)==="/",D=Qx0(A.split("/").filter((Z)=>!!Z),!B).join("/");if(!D&&!B)D=".";if(D&&Q)D+="/";return(B?"/":"")+D}function Gx0(A){return A.charAt(0)==="/"}function r29(...A){return Zx0(A.join("/"))}function o29(A){let B=Dx0(A),Q=B[0],D=B[1];if(!Q&&!D)return".";if(D)D=D.slice(0,D.length-1);return Q+D}function t29(A,B){let Q=Dx0(A)[2];if(B&&Q.slice(B.length*-1)===B)Q=Q.slice(0,Q.length-B.length);return Q}Fx0.basename=t29;Fx0.dirname=o29;Fx0.isAbsolute=Gx0;Fx0.join=r29;Fx0.normalizePath=Zx0;Fx0.relative=s29;Fx0.resolve=Hd1});
var JJ1=E((Zb0)=>{Object.defineProperty(Zb0,"__esModule",{value:!0});var sd1=wA(),s79=kG(),r79=WJ1(),Db0=!1;function o79(){if(Db0)return;Db0=!0,sd1.addGlobalErrorInstrumentationHandler(rd1),sd1.addGlobalUnhandledRejectionInstrumentationHandler(rd1)}function rd1(){let A=r79.getActiveTransaction();if(A)s79.DEBUG_BUILD&&sd1.logger.log("[Tracing] Transaction: internal_error -> Global error occured"),A.setStatus("internal_error")}rd1.tag="sentry_tracingErrorCallback";Zb0.registerErrorInstrumentation=o79});
var Jg0=E((Wg0)=>{Object.defineProperty(Wg0,"__esModule",{value:!0});var vc1=SQ(),Yg0=wA(),HX9=ZV(),zX9=mj();function EX9(A){return!!A&&!!A.$use}class mJ1{static __initStatic(){this.id="Prisma"}constructor(A={}){if(this.name=mJ1.id,EX9(A.client)&&!A.client._sentryInstrumented){Yg0.addNonEnumerableProperty(A.client,"_sentryInstrumented",!0);let B={};try{let Q=A.client._engineConfig;if(Q){let{activeProvider:D,clientVersion:Z}=Q;if(D)B["db.system"]=D;if(Z)B["db.prisma.version"]=Z}}catch(Q){}A.client.$use((Q,D)=>{if(zX9.shouldDisableAutoInstrumentation(vc1.getCurrentHub))return D(Q);let{action:Z,model:G}=Q;return vc1.startSpan({name:G?`${G} ${Z}`:Z,onlyIfParent:!0,op:"db.prisma",attributes:{[vc1.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]:"auto.db.prisma"},data:{...B,"db.operation":Z}},()=>D(Q))})}else HX9.DEBUG_BUILD&&Yg0.logger.warn("Unsupported Prisma client provided to PrismaIntegration. Provided client:",A.client)}setupOnce(){}}mJ1.__initStatic();Wg0.Prisma=mJ1});
var Jl=E((Gb0)=>{Object.defineProperty(Gb0,"__esModule",{value:!0});Gb0.SpanStatus=void 0;(function(A){A.Ok="ok";let Q="deadline_exceeded";A.DeadlineExceeded=Q;let D="unauthenticated";A.Unauthenticated=D;let Z="permission_denied";A.PermissionDenied=Z;let G="not_found";A.NotFound=G;let F="resource_exhausted";A.ResourceExhausted=F;let I="invalid_argument";A.InvalidArgument=I;let Y="unimplemented";A.Unimplemented=Y;let W="unavailable";A.Unavailable=W;let J="internal_error";A.InternalError=J;let X="unknown_error";A.UnknownError=X;let V="cancelled";A.Cancelled=V;let C="already_exists";A.AlreadyExists=C;let K="failed_precondition";A.FailedPrecondition=K;let H="aborted";A.Aborted=H;let z="out_of_range";A.OutOfRange=z;let $="data_loss";A.DataLoss=$})(Gb0.SpanStatus||(Gb0.SpanStatus={}));function td1(A){if(A<400&&A>=100)return"ok";if(A>=400&&A<500)switch(A){case 401:return"unauthenticated";case 403:return"permission_denied";case 404:return"not_found";case 409:return"already_exists";case 413:return"failed_precondition";case 429:return"resource_exhausted";default:return"invalid_argument"}if(A>=500&&A<600)switch(A){case 501:return"unimplemented";case 503:return"unavailable";case 504:return"deadline_exceeded";default:return"internal_error"}return"unknown_error"}var e79=td1;function AD9(A,B){A.setTag("http.status_code",String(B)),A.setData("http.response.status_code",B);let Q=td1(B);if(Q!=="unknown_error")A.setStatus(Q)}Gb0.getSpanStatusFromHttpCode=td1;Gb0.setHttpStatus=AD9;Gb0.spanStatusfromHttpCode=e79});
var Jm0=E((Wm0)=>{var{_nullishCoalesce:yH9,_optionalChain:kH9}=wA();Object.defineProperty(Wm0,"__esModule",{value:!0});var d21=J1("net"),Im0=J1("tls"),_H9=J1("url"),xH9=wA(),vH9=Zm0(),bH9=Fm0();function c21(...A){xH9.logger.log("[https-proxy-agent]",...A)}class Al1 extends vH9.Agent{static __initStatic(){this.protocols=["http","https"]}constructor(A,B){super(B);this.options={},this.proxy=typeof A==="string"?new _H9.URL(A):A,this.proxyHeaders=yH9(kH9([B,"optionalAccess",(Z)=>Z.headers]),()=>({})),c21("Creating new HttpsProxyAgent instance: %o",this.proxy.href);let Q=(this.proxy.hostname||this.proxy.host).replace(/^\[|\]$/g,""),D=this.proxy.port?parseInt(this.proxy.port,10):this.proxy.protocol==="https:"?443:80;this.connectOpts={ALPNProtocols:["http/1.1"],...B?Ym0(B,"headers"):null,host:Q,port:D}}async connect(A,B){let{proxy:Q}=this;if(!B.host)throw new TypeError('No "host" provided');let D;if(Q.protocol==="https:"){c21("Creating `tls.Socket`: %o",this.connectOpts);let X=this.connectOpts.servername||this.connectOpts.host;D=Im0.connect({...this.connectOpts,servername:X&&d21.isIP(X)?void 0:X})}else c21("Creating `net.Socket`: %o",this.connectOpts),D=d21.connect(this.connectOpts);let Z=typeof this.proxyHeaders==="function"?this.proxyHeaders():{...this.proxyHeaders},G=d21.isIPv6(B.host)?`[${B.host}]`:B.host,F=`CONNECT ${G}:${B.port} HTTP/1.1\r
`;if(Q.username||Q.password){let X=`${decodeURIComponent(Q.username)}:${decodeURIComponent(Q.password)}`;Z["Proxy-Authorization"]=`Basic ${Buffer.from(X).toString("base64")}`}if(Z.Host=`${G}:${B.port}`,!Z["Proxy-Connection"])Z["Proxy-Connection"]=this.keepAlive?"Keep-Alive":"close";for(let X of Object.keys(Z))F+=`${X}: ${Z[X]}\r
`;let I=bH9.parseProxyResponse(D);D.write(`${F}\r
`);let{connect:Y,buffered:W}=await I;if(A.emit("proxyConnect",Y),this.emit("proxyConnect",Y,A),Y.statusCode===200){if(A.once("socket",fH9),B.secureEndpoint){c21("Upgrading socket connection to TLS");let X=B.servername||B.host;return Im0.connect({...Ym0(B,"host","path","port"),socket:D,servername:d21.isIP(X)?void 0:X})}return D}D.destroy();let J=new d21.Socket({writable:!1});return J.readable=!0,A.once("socket",(X)=>{c21("Replaying proxy buffer for failed request"),X.push(W),X.push(null)}),J}}Al1.__initStatic();function fH9(A){A.resume()}function Ym0(A,...B){let Q={},D;for(D in A)if(!B.includes(D))Q[D]=A[D];return Q}Wm0.HttpsProxyAgent=Al1});
var Jv0=E((Wv0)=>{Object.defineProperty(Wv0,"__esModule",{value:!0});var RQ9=Pd1();async function OQ9(A,B){return RQ9._nullishCoalesce(A,B)}Wv0._asyncNullishCoalesce=OQ9});
var Jx0=E((Wx0)=>{Object.defineProperty(Wx0,"__esModule",{value:!0});var VB9=gm1(),Ed1=zd1();function CB9(A){let B=[];function Q(){return A===void 0||B.length<A}function D(F){return B.splice(B.indexOf(F),1)[0]}function Z(F){if(!Q())return Ed1.rejectedSyncPromise(new VB9.SentryError("Not adding Promise because buffer limit was reached."));let I=F();if(B.indexOf(I)===-1)B.push(I);return I.then(()=>D(I)).then(null,()=>D(I).then(null,()=>{})),I}function G(F){return new Ed1.SyncPromise((I,Y)=>{let W=B.length;if(!W)return I(!0);let J=setTimeout(()=>{if(F&&F>0)I(!1)},F);B.forEach((X)=>{Ed1.resolvedSyncPromise(X).then(()=>{if(!--W)clearTimeout(J),I(!0)},Y)})})}return{$:B,add:Z,drain:G}}Wx0.makePromiseBuffer=CB9});
var K21=E((Rv0)=>{Object.defineProperty(Rv0,"__esModule",{value:!0});var AJ1=wA(),z59=kG();function Mv0(){return AJ1.getGlobalSingleton("globalEventProcessors",()=>[])}function E59(A){Mv0().push(A)}function fd1(A,B,Q,D=0){return new AJ1.SyncPromise((Z,G)=>{let F=A[D];if(B===null||typeof F!=="function")Z(B);else{let I=F({...B},Q);if(z59.DEBUG_BUILD&&F.id&&I===null&&AJ1.logger.log(`Event processor "${F.id}" dropped event`),AJ1.isThenable(I))I.then((Y)=>fd1(A,Y,Q,D+1).then(Z)).then(null,G);else fd1(A,I,Q,D+1).then(Z).then(null,G)}})}Rv0.addGlobalEventProcessor=E59;Rv0.getGlobalEventProcessors=Mv0;Rv0.notifyEventProcessors=fd1});
var Kl1=E((kd0)=>{Object.defineProperty(kd0,"__esModule",{value:!0});var jd0=J1("path"),MU9=wA();function yd0(A){return A.replace(/^[A-Z]:/,"").replace(/\\/g,"/")}function RU9(A=process.argv[1]?MU9.dirname(process.argv[1]):process.cwd(),B=jd0.sep==="\\"){let Q=B?yd0(A):A;return(D)=>{if(!D)return;let Z=B?yd0(D):D,{dir:G,base:F,ext:I}=jd0.posix.parse(Z);if(I===".js"||I===".mjs"||I===".cjs")F=F.slice(0,I.length*-1);if(!G)G=".";let Y=G.lastIndexOf("/node_modules");if(Y>-1)return`${G.slice(Y+14).replace(/\//g,".")}:${F}`;if(G.startsWith(Q)){let W=G.slice(Q.length+1).replace(/\//g,".");if(W)W+=":";return W+=F,W}return F}}kd0.createGetModuleFromFilename=RU9});
var L21=E((zb0)=>{Object.defineProperty(zb0,"__esModule",{value:!0});var xD9="sentry.source",vD9="sentry.sample_rate",bD9="sentry.op",fD9="sentry.origin",hD9="profile_id";zb0.SEMANTIC_ATTRIBUTE_PROFILE_ID=hD9;zb0.SEMANTIC_ATTRIBUTE_SENTRY_OP=bD9;zb0.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN=fD9;zb0.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE=vD9;zb0.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE=xD9});
var LJ1=E((fb0)=>{Object.defineProperty(fb0,"__esModule",{value:!0});var Yc1=wA(),_Z9="7";function bb0(A){let B=A.protocol?`${A.protocol}:`:"",Q=A.port?`:${A.port}`:"";return`${B}//${A.host}${Q}${A.path?`/${A.path}`:""}/api/`}function xZ9(A){return`${bb0(A)}${A.projectId}/envelope/`}function vZ9(A,B){return Yc1.urlEncode({sentry_key:A.publicKey,sentry_version:_Z9,...B&&{sentry_client:`${B.name}/${B.version}`}})}function bZ9(A,B={}){let Q=typeof B==="string"?B:B.tunnel,D=typeof B==="string"||!B._metadata?void 0:B._metadata.sdk;return Q?Q:`${xZ9(A)}?${vZ9(A,D)}`}function fZ9(A,B){let Q=Yc1.makeDsn(A);if(!Q)return"";let D=`${bb0(Q)}embed/error-page/`,Z=`dsn=${Yc1.dsnToString(Q)}`;for(let G in B){if(G==="dsn")continue;if(G==="onClose")continue;if(G==="user"){let F=B.user;if(!F)continue;if(F.name)Z+=`&name=${encodeURIComponent(F.name)}`;if(F.email)Z+=`&email=${encodeURIComponent(F.email)}`}else Z+=`&${encodeURIComponent(G)}=${encodeURIComponent(B[G])}`}return`${D}?${Z}`}fb0.getEnvelopeEndpointWithUrlEncodedAuth=bZ9;fb0.getReportDialogEndpoint=fZ9});
var LX1=E((ld0,pd0)=>{var{_optionalChain:Qw9,_optionalChainDelete:md0}=wA();Object.defineProperty(ld0,"__esModule",{value:!0});var Dw9=J1("url"),HO=SQ(),NX1=wA(),El1=wf(),Zw9=ud0(),Gw9=50,Fw9=5000;function Ul1(A,...B){NX1.logger.log(`[ANR] ${A}`,...B)}function Iw9(){return NX1.GLOBAL_OBJ}function Yw9(){let A=HO.getGlobalScope().getScopeData();return HO.mergeScopeData(A,HO.getIsolationScope().getScopeData()),HO.mergeScopeData(A,HO.getCurrentScope().getScopeData()),A.attachments=[],A.eventProcessors=[],A}function Ww9(){return NX1.dynamicRequire(pd0,"worker_threads")}async function Jw9(A){let B={message:"ANR"},Q={};for(let D of A.getEventProcessors()){if(B===null)break;B=await D(B,Q)}return Qw9([B,"optionalAccess",(D)=>D.contexts])||{}}var dd0="Anr",Xw9=(A={})=>{if(El1.NODE_VERSION.major<16||El1.NODE_VERSION.major===16&&El1.NODE_VERSION.minor<17)throw new Error("ANR detection requires Node 16.17.0 or later");let B,Q,D=Iw9();return D.__SENTRY_GET_SCOPES__=Yw9,{name:dd0,setupOnce(){},startWorker:()=>{if(B)return;if(Q)B=Cw9(Q,A)},stopWorker:()=>{if(B)B.then((Z)=>{Z(),B=void 0})},setup(Z){Q=Z,setImmediate(()=>this.startWorker())}}},cd0=HO.defineIntegration(Xw9),Vw9=HO.convertIntegrationFnToClass(dd0,cd0);async function Cw9(A,B){let Q=A.getDsn();if(!Q)return()=>{};let D=await Jw9(A);md0([D,"access",(J)=>J.app,"optionalAccess",(J)=>delete J.app_memory]),md0([D,"access",(J)=>J.device,"optionalAccess",(J)=>delete J.free_memory]);let Z=A.getOptions(),G=A.getSdkMetadata()||{};if(G.sdk)G.sdk.integrations=Z.integrations.map((J)=>J.name);let F={debug:NX1.logger.isEnabled(),dsn:Q,environment:Z.environment||"production",release:Z.release,dist:Z.dist,sdkMetadata:G,appRootPath:B.appRootPath,pollInterval:B.pollInterval||Gw9,anrThreshold:B.anrThreshold||Fw9,captureStackTrace:!!B.captureStackTrace,staticTags:B.staticTags||{},contexts:D};if(F.captureStackTrace){let J=J1("inspector");if(!J.url())J.open(0)}let{Worker:I}=Ww9(),Y=new I(new Dw9.URL(`data:application/javascript;base64,${Zw9.base64WorkerScript}`),{workerData:F});process.on("exit",()=>{Y.terminate()});let W=setInterval(()=>{try{let J=HO.getCurrentScope().getSession(),X=J?{...J,toJSON:void 0}:void 0;Y.postMessage({session:X})}catch(J){}},F.pollInterval);return W.unref(),Y.on("message",(J)=>{if(J==="session-ended")Ul1("ANR event sent from ANR worker. Clearing session in this thread."),HO.getCurrentScope().setSession(void 0)}),Y.once("error",(J)=>{clearInterval(W),Ul1("ANR worker error",J)}),Y.once("exit",(J)=>{clearInterval(W),Ul1("ANR worker exit",J)}),Y.unref(),()=>{Y.terminate(),clearInterval(W)}}ld0.Anr=Vw9;ld0.anrIntegration=cd0});
var Lx0=E((Nx0)=>{Object.defineProperty(Nx0,"__esModule",{value:!0});var $x0=["fatal","error","warning","log","info","debug"];function lB9(A){return qx0(A)}function qx0(A){return A==="warn"?"warning":$x0.includes(A)?A:"log"}Nx0.severityFromString=lB9;Nx0.severityLevelFromString=qx0;Nx0.validSeverityLevels=$x0});
var Mf0=E((Lf0)=>{Object.defineProperty(Lf0,"__esModule",{value:!0});var Nf0=wA();function mF9(A,B){let Q={sent_at:new Date().toISOString()};if(B)Q.dsn=Nf0.dsnToString(B);let D=A.map(dF9);return Nf0.createEnvelope(Q,D)}function dF9(A){return[{type:"span"},A]}Lf0.createSpanEnvelope=mF9});
var Ml=E((Iu0)=>{Object.defineProperty(Iu0,"__esModule",{value:!0});var eg0=wA(),dV9=ZV(),cV9=vg0(),lV9=hg0(),pV9=ng0(),iV9=rg0(),nV9=Hf(),aV9=tg0(),x21={},rJ1={},Au0,Bu0,Qu0,Du0,Zu0;function sV9(A,B=!1){return v21("cls",A,BC9,Au0,B)}function rV9(A,B=!1){return v21("lcp",A,DC9,Qu0,B)}function oV9(A){return v21("ttfb",A,ZC9,Du0)}function tV9(A){return v21("fid",A,QC9,Bu0)}function eV9(A){return v21("inp",A,GC9,Zu0)}function AC9(A,B){if(Gu0(A,B),!rJ1[A])FC9(A),rJ1[A]=!0;return Fu0(A,B)}function Ll(A,B){let Q=x21[A];if(!Q||!Q.length)return;for(let D of Q)try{D(B)}catch(Z){dV9.DEBUG_BUILD&&eg0.logger.error(`Error while triggering instrumentation handler.
Type: ${A}
Name: ${eg0.getFunctionName(D)}
Error:`,Z)}}function BC9(){return cV9.onCLS((A)=>{Ll("cls",{metric:A}),Au0=A},{reportAllChanges:!0})}function QC9(){return lV9.onFID((A)=>{Ll("fid",{metric:A}),Bu0=A})}function DC9(){return iV9.onLCP((A)=>{Ll("lcp",{metric:A}),Qu0=A})}function ZC9(){return aV9.onTTFB((A)=>{Ll("ttfb",{metric:A}),Du0=A})}function GC9(){return pV9.onINP((A)=>{Ll("inp",{metric:A}),Zu0=A})}function v21(A,B,Q,D,Z=!1){Gu0(A,B);let G;if(!rJ1[A])G=Q(),rJ1[A]=!0;if(D)B({metric:D});return Fu0(A,B,Z?G:void 0)}function FC9(A){let B={};if(A==="event")B.durationThreshold=0;nV9.observe(A,(Q)=>{Ll(A,{entries:Q})},B)}function Gu0(A,B){x21[A]=x21[A]||[],x21[A].push(B)}function Fu0(A,B,Q){return()=>{if(Q)Q();let D=x21[A];if(!D)return;let Z=D.indexOf(B);if(Z!==-1)D.splice(Z,1)}}Iu0.addClsInstrumentationHandler=sV9;Iu0.addFidInstrumentationHandler=tV9;Iu0.addInpInstrumentationHandler=eV9;Iu0.addLcpInstrumentationHandler=rV9;Iu0.addPerformanceInstrumentationHandler=AC9;Iu0.addTtfbInstrumentationHandler=oV9});
var N21=E((Hb0)=>{Object.defineProperty(Hb0,"__esModule",{value:!0});var PD9=wA();kG();JJ1();Jl();var SD9=HJ1(),q21;function Kb0(A){return q21?q21.get(A):void 0}function jD9(A){let B=Kb0(A);if(!B)return;let Q={};for(let[,[D,Z]]of B){if(!Q[D])Q[D]=[];Q[D].push(PD9.dropUndefinedKeys(Z))}return Q}function yD9(A,B,Q,D,Z,G){let F=SD9.getActiveSpan();if(F){let I=Kb0(F)||new Map,Y=`${A}:${B}@${D}`,W=I.get(G);if(W){let[,J]=W;I.set(G,[Y,{min:Math.min(J.min,Q),max:Math.max(J.max,Q),count:J.count+=1,sum:J.sum+=Q,tags:J.tags}])}else I.set(G,[Y,{min:Q,max:Q,count:1,sum:Q,tags:Z}]);if(!q21)q21=new WeakMap;q21.set(F,I)}}Hb0.getMetricSummaryJsonForSpan=jD9;Hb0.updateMetricSummaryOnActiveSpan=yD9});
var Nc0=E((qc0)=>{Object.defineProperty(qc0,"__esModule",{value:!0});var Uc0=SQ(),_$9=wA(),wc0="Debug",x$9=(A={})=>{let B={debugger:!1,stringify:!1,...A};return{name:wc0,setupOnce(){},setup(Q){if(!Q.on)return;Q.on("beforeSendEvent",(D,Z)=>{if(B.debugger)debugger;_$9.consoleSandbox(()=>{if(B.stringify){if(console.log(JSON.stringify(D,null,2)),Z&&Object.keys(Z).length)console.log(JSON.stringify(Z,null,2))}else if(console.log(D),Z&&Object.keys(Z).length)console.log(Z)})})}}},$c0=Uc0.defineIntegration(x$9),v$9=Uc0.convertIntegrationFnToClass(wc0,$c0);qc0.Debug=v$9;qc0.debugIntegration=$c0});
var Nd1=E((kx0)=>{Object.defineProperty(kx0,"__esModule",{value:!0});var B99=rq(),Q99=hH(),D99=oU(),Z99="baggage",qd1="sentry-",jx0=/^sentry-/,yx0=8192;function G99(A){if(!Q99.isString(A)&&!Array.isArray(A))return;let B={};if(Array.isArray(A))B=A.reduce((D,Z)=>{let G=Sx0(Z);for(let F of Object.keys(G))D[F]=G[F];return D},{});else{if(!A)return;B=Sx0(A)}let Q=Object.entries(B).reduce((D,[Z,G])=>{if(Z.match(jx0)){let F=Z.slice(qd1.length);D[F]=G}return D},{});if(Object.keys(Q).length>0)return Q;else return}function F99(A){if(!A)return;let B=Object.entries(A).reduce((Q,[D,Z])=>{if(Z)Q[`${qd1}${D}`]=Z;return Q},{});return I99(B)}function Sx0(A){return A.split(",").map((B)=>B.split("=").map((Q)=>decodeURIComponent(Q.trim()))).reduce((B,[Q,D])=>{return B[Q]=D,B},{})}function I99(A){if(Object.keys(A).length===0)return;return Object.entries(A).reduce((B,[Q,D],Z)=>{let G=`${encodeURIComponent(Q)}=${encodeURIComponent(D)}`,F=Z===0?G:`${B},${G}`;if(F.length>yx0)return B99.DEBUG_BUILD&&D99.logger.warn(`Not adding key: ${Q} with val: ${D} to baggage header due to exceeding baggage size limits.`),B;else return F},"")}kx0.BAGGAGE_HEADER_NAME=Z99;kx0.MAX_BAGGAGE_STRING_LENGTH=yx0;kx0.SENTRY_BAGGAGE_KEY_PREFIX=qd1;kx0.SENTRY_BAGGAGE_KEY_PREFIX_REGEX=jx0;kx0.baggageHeaderToDynamicSamplingContext=G99;kx0.dynamicSamplingContextToSentryBaggageHeader=F99});
var Nl=E((_g0)=>{Object.defineProperty(_g0,"__esModule",{value:!0});var kg0=hC(),aX9=(A,B)=>{let Q=(D)=>{if(D.type==="pagehide"||kg0.WINDOW.document.visibilityState==="hidden"){if(A(D),B)removeEventListener("visibilitychange",Q,!0),removeEventListener("pagehide",Q,!0)}};if(kg0.WINDOW.document)addEventListener("visibilitychange",Q,!0),addEventListener("pagehide",Q,!0)};_g0.onHidden=aX9});
var Nl0=E((ql0)=>{Object.defineProperty(ql0,"__esModule",{value:!0});var El0=SQ(),Ll1=wA(),Nl1=Ll1.GLOBAL_OBJ,fq9=7,Ul0="ContextLines",hq9=(A={})=>{let B=A.frameContextLines!=null?A.frameContextLines:fq9;return{name:Ul0,setupOnce(){},processEvent(Q){return uq9(Q,B)}}},wl0=El0.defineIntegration(hq9),gq9=El0.convertIntegrationFnToClass(Ul0,wl0);function uq9(A,B){let Q=Nl1.document,D=Nl1.location&&Ll1.stripUrlQueryAndFragment(Nl1.location.href);if(!Q||!D)return A;let Z=A.exception&&A.exception.values;if(!Z||!Z.length)return A;let G=Q.documentElement.innerHTML;if(!G)return A;let F=["<!DOCTYPE html>","<html>",...G.split(`
`),"</html>"];return Z.forEach((I)=>{let Y=I.stacktrace;if(Y&&Y.frames)Y.frames=Y.frames.map((W)=>$l0(W,F,D,B))}),A}function $l0(A,B,Q,D){if(A.filename!==Q||!A.lineno||!B.length)return A;return Ll1.addContextToFrame(B,A,D),A}ql0.ContextLines=gq9;ql0.applySourceContextToFrame=$l0;ql0.contextLinesIntegration=wl0});
var Nm0=E((qm0)=>{Object.defineProperty(qm0,"__esModule",{value:!0});var Jz9=wf(),Xz9=Um0(),Vz9=$m0();function Cz9(){if(Jz9.NODE_VERSION.major>=14)Vz9.setHooksAsyncContextStrategy();else Xz9.setDomainAsyncContextStrategy()}qm0.setNodeAsyncContextStrategy=Cz9});
var O21=E((tb0)=>{Object.defineProperty(tb0,"__esModule",{value:!0});var uG9="c",mG9="g",dG9="s",cG9="d",lG9=5000,pG9=1e4,iG9=1e4;tb0.COUNTER_METRIC_TYPE=uG9;tb0.DEFAULT_BROWSER_FLUSH_INTERVAL=lG9;tb0.DEFAULT_FLUSH_INTERVAL=pG9;tb0.DISTRIBUTION_METRIC_TYPE=cG9;tb0.GAUGE_METRIC_TYPE=mG9;tb0.MAX_WEIGHT=iG9;tb0.SET_METRIC_TYPE=dG9});
var Oc1=E((ef0)=>{Object.defineProperty(ef0,"__esModule",{value:!0});var QI=wA(),Vf=kG(),rf0=IO(),EI9=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/],UI9=[/^.*\/healthcheck$/,/^.*\/healthy$/,/^.*\/live$/,/^.*\/ready$/,/^.*\/heartbeat$/,/^.*\/health$/,/^.*\/healthz$/],of0="InboundFilters",wI9=(A={})=>{return{name:of0,setupOnce(){},processEvent(B,Q,D){let Z=D.getOptions(),G=qI9(A,Z);return NI9(B,G)?null:B}}},tf0=rf0.defineIntegration(wI9),$I9=rf0.convertIntegrationFnToClass(of0,tf0);function qI9(A={},B={}){return{allowUrls:[...A.allowUrls||[],...B.allowUrls||[]],denyUrls:[...A.denyUrls||[],...B.denyUrls||[]],ignoreErrors:[...A.ignoreErrors||[],...B.ignoreErrors||[],...A.disableErrorDefaults?[]:EI9],ignoreTransactions:[...A.ignoreTransactions||[],...B.ignoreTransactions||[],...A.disableTransactionDefaults?[]:UI9],ignoreInternal:A.ignoreInternal!==void 0?A.ignoreInternal:!0}}function NI9(A,B){if(B.ignoreInternal&&PI9(A))return Vf.DEBUG_BUILD&&QI.logger.warn(`Event dropped due to being internal Sentry Error.
Event: ${QI.getEventDescription(A)}`),!0;if(LI9(A,B.ignoreErrors))return Vf.DEBUG_BUILD&&QI.logger.warn(`Event dropped due to being matched by \`ignoreErrors\` option.
Event: ${QI.getEventDescription(A)}`),!0;if(MI9(A,B.ignoreTransactions))return Vf.DEBUG_BUILD&&QI.logger.warn(`Event dropped due to being matched by \`ignoreTransactions\` option.
Event: ${QI.getEventDescription(A)}`),!0;if(RI9(A,B.denyUrls))return Vf.DEBUG_BUILD&&QI.logger.warn(`Event dropped due to being matched by \`denyUrls\` option.
Event: ${QI.getEventDescription(A)}.
Url: ${SJ1(A)}`),!0;if(!OI9(A,B.allowUrls))return Vf.DEBUG_BUILD&&QI.logger.warn(`Event dropped due to not being matched by \`allowUrls\` option.
Event: ${QI.getEventDescription(A)}.
Url: ${SJ1(A)}`),!0;return!1}function LI9(A,B){if(A.type||!B||!B.length)return!1;return TI9(A).some((Q)=>QI.stringMatchesSomePattern(Q,B))}function MI9(A,B){if(A.type!=="transaction"||!B||!B.length)return!1;let Q=A.transaction;return Q?QI.stringMatchesSomePattern(Q,B):!1}function RI9(A,B){if(!B||!B.length)return!1;let Q=SJ1(A);return!Q?!1:QI.stringMatchesSomePattern(Q,B)}function OI9(A,B){if(!B||!B.length)return!0;let Q=SJ1(A);return!Q?!0:QI.stringMatchesSomePattern(Q,B)}function TI9(A){let B=[];if(A.message)B.push(A.message);let Q;try{Q=A.exception.values[A.exception.values.length-1]}catch(D){}if(Q){if(Q.value){if(B.push(Q.value),Q.type)B.push(`${Q.type}: ${Q.value}`)}}if(Vf.DEBUG_BUILD&&B.length===0)QI.logger.error(`Could not extract message for event ${QI.getEventDescription(A)}`);return B}function PI9(A){try{return A.exception.values[0].type==="SentryError"}catch(B){}return!1}function SI9(A=[]){for(let B=A.length-1;B>=0;B--){let Q=A[B];if(Q&&Q.filename!=="<anonymous>"&&Q.filename!=="[native code]")return Q.filename||null}return null}function SJ1(A){try{let B;try{B=A.exception.values[0].stacktrace.frames}catch(Q){}return B?SI9(B):null}catch(B){return Vf.DEBUG_BUILD&&QI.logger.error(`Cannot extract url for event ${QI.getEventDescription(A)}`),null}}ef0.InboundFilters=$I9;ef0.inboundFiltersIntegration=tf0});
var Og0=E((Rg0)=>{Object.defineProperty(Rg0,"__esModule",{value:!0});var _X9=()=>{return`v3-${Date.now()}-${Math.floor(Math.random()*8999999999999)+1000000000000}`};Rg0.generateUniqueID=_X9});
var Pc1=E((Jh0)=>{Object.defineProperty(Jh0,"__esModule",{value:!0});var Fh0=wA(),Ih0=IO(),hI9="cause",gI9=5,Yh0="LinkedErrors",uI9=(A={})=>{let B=A.limit||gI9,Q=A.key||hI9;return{name:Yh0,setupOnce(){},preprocessEvent(D,Z,G){let F=G.getOptions();Fh0.applyAggregateErrorsToEvent(Fh0.exceptionFromError,F.stackParser,F.maxValueLength,Q,B,D,Z)}}},Wh0=Ih0.defineIntegration(uI9),mI9=Ih0.convertIntegrationFnToClass(Yh0,Wh0);Jh0.LinkedErrors=mI9;Jh0.linkedErrorsIntegration=Wh0});
var Pd1=E((Yv0)=>{Object.defineProperty(Yv0,"__esModule",{value:!0});function LQ9(A,B){return A!=null?A:B()}Yv0._nullishCoalesce=LQ9});
var QJ1=E((pv0)=>{Object.defineProperty(pv0,"__esModule",{value:!0});var E21=wA(),D79=Yf(),Z79=Il(),lv0=BV();function G79(A,B){let{fingerprint:Q,span:D,breadcrumbs:Z,sdkProcessingMetadata:G}=B;if(I79(A,B),D)J79(A,D);X79(A,Q),Y79(A,Z),W79(A,G)}function F79(A,B){let{extra:Q,tags:D,user:Z,contexts:G,level:F,sdkProcessingMetadata:I,breadcrumbs:Y,fingerprint:W,eventProcessors:J,attachments:X,propagationContext:V,transactionName:C,span:K}=B;if(Yl(A,"extra",Q),Yl(A,"tags",D),Yl(A,"user",Z),Yl(A,"contexts",G),Yl(A,"sdkProcessingMetadata",I),F)A.level=F;if(C)A.transactionName=C;if(K)A.span=K;if(Y.length)A.breadcrumbs=[...A.breadcrumbs,...Y];if(W.length)A.fingerprint=[...A.fingerprint,...W];if(J.length)A.eventProcessors=[...A.eventProcessors,...J];if(X.length)A.attachments=[...A.attachments,...X];A.propagationContext={...A.propagationContext,...V}}function Yl(A,B,Q){if(Q&&Object.keys(Q).length){A[B]={...A[B]};for(let D in Q)if(Object.prototype.hasOwnProperty.call(Q,D))A[B][D]=Q[D]}}function I79(A,B){let{extra:Q,tags:D,user:Z,contexts:G,level:F,transactionName:I}=B,Y=E21.dropUndefinedKeys(Q);if(Y&&Object.keys(Y).length)A.extra={...Y,...A.extra};let W=E21.dropUndefinedKeys(D);if(W&&Object.keys(W).length)A.tags={...W,...A.tags};let J=E21.dropUndefinedKeys(Z);if(J&&Object.keys(J).length)A.user={...J,...A.user};let X=E21.dropUndefinedKeys(G);if(X&&Object.keys(X).length)A.contexts={...X,...A.contexts};if(F)A.level=F;if(I)A.transaction=I}function Y79(A,B){let Q=[...A.breadcrumbs||[],...B];A.breadcrumbs=Q.length?Q:void 0}function W79(A,B){A.sdkProcessingMetadata={...A.sdkProcessingMetadata,...B}}function J79(A,B){A.contexts={trace:lv0.spanToTraceContext(B),...A.contexts};let Q=Z79.getRootSpan(B);if(Q){A.sdkProcessingMetadata={dynamicSamplingContext:D79.getDynamicSamplingContextFromSpan(B),...A.sdkProcessingMetadata};let D=lv0.spanToJSON(Q).description;if(D)A.tags={transaction:D,...A.tags}}}function X79(A,B){if(A.fingerprint=A.fingerprint?E21.arrayify(A.fingerprint):[],B)A.fingerprint=A.fingerprint.concat(B);if(A.fingerprint&&!A.fingerprint.length)delete A.fingerprint}pv0.applyScopeDataToEvent=G79;pv0.mergeAndOverwriteScopeData=Yl;pv0.mergeScopeData=F79});
var QO=E((U_0)=>{Object.defineProperty(U_0,"__esModule",{value:!0});var S09=rq(),j09=oU(),y09=_W1(),Ql={},E_0={};function k09(A,B){Ql[A]=Ql[A]||[],Ql[A].push(B)}function _09(){Object.keys(Ql).forEach((A)=>{Ql[A]=void 0})}function x09(A,B){if(!E_0[A])B(),E_0[A]=!0}function v09(A,B){let Q=A&&Ql[A];if(!Q)return;for(let D of Q)try{D(B)}catch(Z){S09.DEBUG_BUILD&&j09.logger.error(`Error while triggering instrumentation handler.
Type: ${A}
Name: ${y09.getFunctionName(D)}
Error:`,Z)}}U_0.addHandler=k09;U_0.maybeInstrument=x09;U_0.resetInstrumentationHandlers=_09;U_0.triggerHandlers=v09});
var Qc0=E((Bc0)=>{var{_optionalChain:MX1}=wA();Object.defineProperty(Bc0,"__esModule",{value:!0});var DI=SQ(),jl=wA(),Ow9=l21(),RX1=Hl1(),Tw9=wl1(),Ac0=ed0();function Pw9(){return function A(B,Q,D){let Z=MX1([DI.getClient,"call",(J)=>J(),"optionalAccess",(J)=>J.getOptions,"call",(J)=>J()]);if(!Z||Z.instrumenter!=="sentry"||MX1([B,"access",(J)=>J.method,"optionalAccess",(J)=>J.toUpperCase,"call",(J)=>J()])==="OPTIONS"||MX1([B,"access",(J)=>J.method,"optionalAccess",(J)=>J.toUpperCase,"call",(J)=>J()])==="HEAD")return D();let G=B.headers&&jl.isString(B.headers["sentry-trace"])?B.headers["sentry-trace"]:void 0,F=MX1([B,"access",(J)=>J.headers,"optionalAccess",(J)=>J.baggage]);if(!DI.hasTracingEnabled(Z))return D();let[I,Y]=jl.extractPathForTransaction(B,{path:!0,method:!0}),W=DI.continueTrace({sentryTrace:G,baggage:F},(J)=>DI.startTransaction({name:I,op:"http.server",origin:"auto.http.node.tracingHandler",...J,data:{[DI.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]:Y},metadata:{...J.metadata,request:B}},{request:jl.extractRequestData(B)}));DI.getCurrentScope().setSpan(W),Q.__sentry_transaction=W,Q.once("finish",()=>{setImmediate(()=>{jl.addRequestDataToTransaction(W,B),DI.setHttpStatus(W,Q.statusCode),W.end()})}),D()}}function Sw9(A={}){let B;if("include"in A)B={include:A.include};else{let{ip:Q,request:D,transaction:Z,user:G}=A;if(Q||D||Z||G)B={include:jl.dropUndefinedKeys({ip:Q,request:D,transaction:Z,user:G})}}return B}function jw9(A){let B=Sw9(A),Q=DI.getClient();if(Q&&RX1.isAutoSessionTrackingEnabled(Q)){Q.initSessionFlusher();let D=DI.getCurrentScope();if(D.getSession())D.setSession()}return function D(Z,G,F){if(A&&A.flushTimeout&&A.flushTimeout>0){let I=G.end;G.end=function(Y,W,J){DI.flush(A.flushTimeout).then(()=>{I.call(this,Y,W,J)}).then(null,(X)=>{Ow9.DEBUG_BUILD&&jl.logger.error(X),I.call(this,Y,W,J)})}}DI.runWithAsyncContext(()=>{let I=DI.getCurrentScope();I.setSDKProcessingMetadata({request:Z,requestDataOptionsFromExpressHandler:B});let Y=DI.getClient();if(RX1.isAutoSessionTrackingEnabled(Y))I.setRequestSession({status:"ok"});G.once("finish",()=>{let W=DI.getClient();if(RX1.isAutoSessionTrackingEnabled(W))setImmediate(()=>{if(W&&W._captureRequestSession)W._captureRequestSession()})}),F()})}}function yw9(A){let B=A.status||A.statusCode||A.status_code||A.output&&A.output.statusCode;return B?parseInt(B,10):500}function kw9(A){return yw9(A)>=500}function _w9(A){return function B(Q,D,Z,G){if((A&&A.shouldHandleError||kw9)(Q)){DI.withScope((I)=>{I.setSDKProcessingMetadata({request:D});let Y=Z.__sentry_transaction;if(Y&&!DI.getActiveSpan())I.setSpan(Y);let W=DI.getClient();if(W&&RX1.isAutoSessionTrackingEnabled(W)){if(W._sessionFlusher!==void 0){let V=I.getRequestSession();if(V&&V.status!==void 0)V.status="crashed"}}let J=DI.captureException(Q,{mechanism:{type:"middleware",handled:!1}});Z.sentry=J,G(Q)});return}G(Q)}}var xw9=Tw9.trpcMiddleware;Bc0.extractRequestData=Ac0.extractRequestData;Bc0.parseRequest=Ac0.parseRequest;Bc0.errorHandler=_w9;Bc0.requestHandler=jw9;Bc0.tracingHandler=Pw9;Bc0.trpcMiddleware=xw9});
var Qg0=E((Bg0)=>{var{_optionalChain:El}=wA();Object.defineProperty(Bg0,"__esModule",{value:!0});var Ul=wA(),_c1=ZV(),GX9=mj();class hJ1{static __initStatic(){this.id="Postgres"}constructor(A={}){this.name=hJ1.id,this._usePgNative=!!A.usePgNative,this._module=A.module}loadDependency(){return this._module=this._module||Ul.loadModule("pg")}setupOnce(A,B){if(GX9.shouldDisableAutoInstrumentation(B)){_c1.DEBUG_BUILD&&Ul.logger.log("Postgres Integration is skipped because of instrumenter configuration.");return}let Q=this.loadDependency();if(!Q){_c1.DEBUG_BUILD&&Ul.logger.error("Postgres Integration was unable to require `pg` package.");return}let D=this._usePgNative?El([Q,"access",(Z)=>Z.native,"optionalAccess",(Z)=>Z.Client]):Q.Client;if(!D){_c1.DEBUG_BUILD&&Ul.logger.error("Postgres Integration was unable to access 'pg-native' bindings.");return}Ul.fill(D.prototype,"query",function(Z){return function(G,F,I){let W=B().getScope().getSpan(),J={"db.system":"postgresql"};try{if(this.database)J["db.name"]=this.database;if(this.host)J["server.address"]=this.host;if(this.port)J["server.port"]=this.port;if(this.user)J["db.user"]=this.user}catch(C){}let X=El([W,"optionalAccess",(C)=>C.startChild,"call",(C)=>C({description:typeof G==="string"?G:G.text,op:"db",origin:"auto.db.postgres",data:J})]);if(typeof I==="function")return Z.call(this,G,F,function(C,K){El([X,"optionalAccess",(H)=>H.end,"call",(H)=>H()]),I(C,K)});if(typeof F==="function")return Z.call(this,G,function(C,K){El([X,"optionalAccess",(H)=>H.end,"call",(H)=>H()]),F(C,K)});let V=typeof F!=="undefined"?Z.call(this,G,F):Z.call(this,G);if(Ul.isThenable(V))return V.then((C)=>{return El([X,"optionalAccess",(K)=>K.end,"call",(K)=>K()]),C});return El([X,"optionalAccess",(C)=>C.end,"call",(C)=>C()]),V}})}}hJ1.__initStatic();Bg0.Postgres=hJ1});
var Ql1=E((Cm0)=>{var{_nullishCoalesce:Bl1}=wA();Object.defineProperty(Cm0,"__esModule",{value:!0});var gH9=J1("http"),uH9=J1("https"),mH9=J1("stream"),Vm0=J1("url"),dH9=J1("zlib"),Xm0=SQ(),cH9=wA(),lH9=Jm0(),pH9=32768;function iH9(A){return new mH9.Readable({read(){this.push(A),this.push(null)}})}function nH9(A){let B;try{B=new Vm0.URL(A.url)}catch(Y){return cH9.consoleSandbox(()=>{console.warn("[@sentry/node]: Invalid dsn or tunnel option, will not send any events. The tunnel option must be a full URL when used.")}),Xm0.createTransport(A,()=>Promise.resolve({}))}let Q=B.protocol==="https:",D=aH9(B,A.proxy||(Q?process.env.https_proxy:void 0)||process.env.http_proxy),Z=Q?uH9:gH9,G=A.keepAlive===void 0?!1:A.keepAlive,F=D?new lH9.HttpsProxyAgent(D):new Z.Agent({keepAlive:G,maxSockets:30,timeout:2000}),I=sH9(A,Bl1(A.httpModule,()=>Z),F);return Xm0.createTransport(A,I)}function aH9(A,B){let{no_proxy:Q}=process.env;if(Q&&Q.split(",").some((Z)=>A.host.endsWith(Z)||A.hostname.endsWith(Z)))return;else return B}function sH9(A,B,Q){let{hostname:D,pathname:Z,port:G,protocol:F,search:I}=new Vm0.URL(A.url);return function Y(W){return new Promise((J,X)=>{let V=iH9(W.body),C={...A.headers};if(W.body.length>pH9)C["content-encoding"]="gzip",V=V.pipe(dH9.createGzip());let K=B.request({method:"POST",agent:Q,headers:C,hostname:D,path:`${Z}${I}`,port:G,protocol:F,ca:A.caCerts},(H)=>{H.on("data",()=>{}),H.on("end",()=>{}),H.setEncoding("utf8");let z=Bl1(H.headers["retry-after"],()=>null),$=Bl1(H.headers["x-sentry-rate-limits"],()=>null);J({statusCode:H.statusCode,headers:{"retry-after":z,"x-sentry-rate-limits":Array.isArray($)?$[0]:$}})});K.on("error",X),V.pipe(K)})}}Cm0.makeNodeTransport=nH9});
var Qv0=E((Bv0)=>{Object.defineProperty(Bv0,"__esModule",{value:!0});var Od1=hH(),ex0=D21(),IQ9=I21(),YQ9=gH();function Td1(A,B){return A(B.stack||"",1)}function Av0(A,B){let Q={type:B.name||B.constructor.name,value:B.message},D=Td1(A,B);if(D.length)Q.stacktrace={frames:D};return Q}function WQ9(A){if("name"in A&&typeof A.name==="string"){let B=`'${A.name}' captured as exception`;if("message"in A&&typeof A.message==="string")B+=` with message '${A.message}'`;return B}else if("message"in A&&typeof A.message==="string")return A.message;else return`Object captured as exception with keys: ${YQ9.extractExceptionKeysForMessage(A)}`}function JQ9(A,B,Q,D){let Z=typeof A==="function"?A().getClient():A,G=Q,I=D&&D.data&&D.data.mechanism||{handled:!0,type:"generic"},Y;if(!Od1.isError(Q)){if(Od1.isPlainObject(Q)){let J=Z&&Z.getOptions().normalizeDepth;Y={["__serialized__"]:IQ9.normalizeToSize(Q,J)};let X=WQ9(Q);G=D&&D.syntheticException||new Error(X),G.message=X}else G=D&&D.syntheticException||new Error(Q),G.message=Q;I.synthetic=!0}let W={exception:{values:[Av0(B,G)]}};if(Y)W.extra=Y;return ex0.addExceptionTypeValue(W,void 0,void 0),ex0.addExceptionMechanism(W,I),{...W,event_id:D&&D.event_id}}function XQ9(A,B,Q="info",D,Z){let G={event_id:D&&D.event_id,level:Q};if(Z&&D&&D.syntheticException){let F=Td1(A,D.syntheticException);if(F.length)G.exception={values:[{value:B,stacktrace:{frames:F}}]}}if(Od1.isParameterizedString(B)){let{__sentry_template_string__:F,__sentry_template_values__:I}=B;return G.logentry={message:F,params:I},G}return G.message=B,G}Bv0.eventFromMessage=XQ9;Bv0.eventFromUnknownInput=JQ9;Bv0.exceptionFromError=Av0;Bv0.parseStackFrames=Td1});
var R21=E((ub0)=>{Object.defineProperty(ub0,"__esModule",{value:!0});var GG9=wA();function FG9(A,B,Q,D){let Z=Object.entries(GG9.dropUndefinedKeys(D)).sort((G,F)=>G[0].localeCompare(F[0]));return`${A}${B}${Q}${Z}`}function IG9(A){let B=0;for(let Q=0;Q<A.length;Q++){let D=A.charCodeAt(Q);B=(B<<5)-B+D,B&=B}return B>>>0}function YG9(A){let B="";for(let Q of A){let D=Object.entries(Q.tags),Z=D.length>0?`|#${D.map(([G,F])=>`${G}:${F}`).join(",")}`:"";B+=`${Q.name}@${Q.unit}:${Q.metric}|${Q.metricType}${Z}|T${Q.timestamp}
`}return B}function WG9(A){return A.replace(/[^\w]+/gi,"_")}function JG9(A){return A.replace(/[^\w\-.]+/gi,"_")}function XG9(A){return A.replace(/[^\w\-./]+/gi,"")}var VG9=[[`
`,"\\n"],["\r","\\r"],["\t","\\t"],["\\","\\\\"],["|","\\u{7c}"],[",","\\u{2c}"]];function CG9(A){for(let[B,Q]of VG9)if(A===B)return Q;return A}function KG9(A){return[...A].reduce((B,Q)=>B+CG9(Q),"")}function HG9(A){let B={};for(let Q in A)if(Object.prototype.hasOwnProperty.call(A,Q)){let D=XG9(Q);B[D]=KG9(String(A[Q]))}return B}ub0.getBucketKey=FG9;ub0.sanitizeMetricKey=JG9;ub0.sanitizeTags=HG9;ub0.sanitizeUnit=WG9;ub0.serializeMetricBuckets=YG9;ub0.simpleHash=IG9});
var Rd1=E((gx0)=>{Object.defineProperty(gx0,"__esModule",{value:!0});var N99=hm1(),L99=I21(),fx0=gH();function M99(A,B=[]){return[A,B]}function R99(A,B){let[Q,D]=A;return[Q,[...D,B]]}function hx0(A,B){let Q=A[1];for(let D of Q){let Z=D[0].type;if(B(D,Z))return!0}return!1}function O99(A,B){return hx0(A,(Q,D)=>B.includes(D))}function Md1(A,B){return(B||new TextEncoder).encode(A)}function T99(A,B){let[Q,D]=A,Z=JSON.stringify(Q);function G(F){if(typeof Z==="string")Z=typeof F==="string"?Z+F:[Md1(Z,B),F];else Z.push(typeof F==="string"?Md1(F,B):F)}for(let F of D){let[I,Y]=F;if(G(`
${JSON.stringify(I)}
`),typeof Y==="string"||Y instanceof Uint8Array)G(Y);else{let W;try{W=JSON.stringify(Y)}catch(J){W=JSON.stringify(L99.normalize(Y))}G(W)}}return typeof Z==="string"?Z:P99(Z)}function P99(A){let B=A.reduce((Z,G)=>Z+G.length,0),Q=new Uint8Array(B),D=0;for(let Z of A)Q.set(Z,D),D+=Z.length;return Q}function S99(A,B,Q){let D=typeof A==="string"?B.encode(A):A;function Z(Y){let W=D.subarray(0,Y);return D=D.subarray(Y+1),W}function G(){let Y=D.indexOf(10);if(Y<0)Y=D.length;return JSON.parse(Q.decode(Z(Y)))}let F=G(),I=[];while(D.length){let Y=G(),W=typeof Y.length==="number"?Y.length:void 0;I.push([Y,W?Z(W):G()])}return[F,I]}function j99(A,B){let Q=typeof A.data==="string"?Md1(A.data,B):A.data;return[fx0.dropUndefinedKeys({type:"attachment",length:Q.length,filename:A.filename,content_type:A.contentType,attachment_type:A.attachmentType}),Q]}var y99={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",statsd:"metric_bucket"};function k99(A){return y99[A]}function _99(A){if(!A||!A.sdk)return;let{name:B,version:Q}=A.sdk;return{name:B,version:Q}}function x99(A,B,Q,D){let Z=A.sdkProcessingMetadata&&A.sdkProcessingMetadata.dynamicSamplingContext;return{event_id:A.event_id,sent_at:new Date().toISOString(),...B&&{sdk:B},...!!Q&&D&&{dsn:N99.dsnToString(D)},...Z&&{trace:fx0.dropUndefinedKeys({...Z})}}}gx0.addItemToEnvelope=R99;gx0.createAttachmentEnvelopeItem=j99;gx0.createEnvelope=M99;gx0.createEventEnvelopeHeaders=x99;gx0.envelopeContainsItemType=O99;gx0.envelopeItemTypeToDataCategory=k99;gx0.forEachEnvelopeItem=hx0;gx0.getSdkMetadataForEnvelopeHeader=_99;gx0.parseEnvelope=S99;gx0.serializeEnvelope=T99});
var Rl1=E((sl0)=>{Object.defineProperty(sl0,"__esModule",{value:!0});var dB=SQ(),RN9=ru0(),ON9=ec1(),TN9=Ql1(),s21=Hl1(),Ml1=wA(),PN9=gd0(),pl0=Kl1(),SN9=nd0(),jN9=Qc0(),yN9=Xc0(),kN9=Cc0(),aj=_l0(),_N9=ZX1(),xN9=HX1(),vN9=EX1(),bN9=VX1(),fN9=IX1(),hN9=GX1(),gN9=XX1(),uN9=UX1(),mN9=LX1(),il0=$l1(),nl0=$X1(),al0=YX1(),dN9=wl1(),cN9=gl0(),lN9=dl0(),pN9=ll0(),iN9=pl0.createGetModuleFromFilename(),nN9={...dB.Integrations,...yN9,...kN9},aN9={instrumentCron:cN9.instrumentCron,instrumentNodeCron:lN9.instrumentNodeCron,instrumentNodeSchedule:pN9.instrumentNodeSchedule};sl0.Hub=dB.Hub;sl0.SDK_VERSION=dB.SDK_VERSION;sl0.SEMANTIC_ATTRIBUTE_SENTRY_OP=dB.SEMANTIC_ATTRIBUTE_SENTRY_OP;sl0.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN=dB.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN;sl0.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE=dB.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE;sl0.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE=dB.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE;sl0.Scope=dB.Scope;sl0.addBreadcrumb=dB.addBreadcrumb;sl0.addEventProcessor=dB.addEventProcessor;sl0.addGlobalEventProcessor=dB.addGlobalEventProcessor;sl0.addIntegration=dB.addIntegration;sl0.captureCheckIn=dB.captureCheckIn;sl0.captureEvent=dB.captureEvent;sl0.captureException=dB.captureException;sl0.captureMessage=dB.captureMessage;sl0.captureSession=dB.captureSession;sl0.close=dB.close;sl0.configureScope=dB.configureScope;sl0.continueTrace=dB.continueTrace;sl0.createTransport=dB.createTransport;sl0.endSession=dB.endSession;sl0.extractTraceparentData=dB.extractTraceparentData;sl0.flush=dB.flush;sl0.functionToStringIntegration=dB.functionToStringIntegration;sl0.getActiveSpan=dB.getActiveSpan;sl0.getActiveTransaction=dB.getActiveTransaction;sl0.getClient=dB.getClient;sl0.getCurrentHub=dB.getCurrentHub;sl0.getCurrentScope=dB.getCurrentScope;sl0.getGlobalScope=dB.getGlobalScope;sl0.getHubFromCarrier=dB.getHubFromCarrier;sl0.getIsolationScope=dB.getIsolationScope;sl0.getSpanStatusFromHttpCode=dB.getSpanStatusFromHttpCode;sl0.inboundFiltersIntegration=dB.inboundFiltersIntegration;sl0.isInitialized=dB.isInitialized;sl0.lastEventId=dB.lastEventId;sl0.linkedErrorsIntegration=dB.linkedErrorsIntegration;sl0.makeMain=dB.makeMain;sl0.metrics=dB.metrics;sl0.parameterize=dB.parameterize;sl0.requestDataIntegration=dB.requestDataIntegration;sl0.runWithAsyncContext=dB.runWithAsyncContext;sl0.setContext=dB.setContext;sl0.setCurrentClient=dB.setCurrentClient;sl0.setExtra=dB.setExtra;sl0.setExtras=dB.setExtras;sl0.setHttpStatus=dB.setHttpStatus;sl0.setMeasurement=dB.setMeasurement;sl0.setTag=dB.setTag;sl0.setTags=dB.setTags;sl0.setUser=dB.setUser;sl0.spanStatusfromHttpCode=dB.spanStatusfromHttpCode;sl0.startActiveSpan=dB.startActiveSpan;sl0.startInactiveSpan=dB.startInactiveSpan;sl0.startSession=dB.startSession;sl0.startSpan=dB.startSpan;sl0.startSpanManual=dB.startSpanManual;sl0.startTransaction=dB.startTransaction;sl0.trace=dB.trace;sl0.withActiveSpan=dB.withActiveSpan;sl0.withIsolationScope=dB.withIsolationScope;sl0.withMonitor=dB.withMonitor;sl0.withScope=dB.withScope;sl0.autoDiscoverNodePerformanceMonitoringIntegrations=RN9.autoDiscoverNodePerformanceMonitoringIntegrations;sl0.NodeClient=ON9.NodeClient;sl0.makeNodeTransport=TN9.makeNodeTransport;sl0.defaultIntegrations=s21.defaultIntegrations;sl0.defaultStackParser=s21.defaultStackParser;sl0.getDefaultIntegrations=s21.getDefaultIntegrations;sl0.getSentryRelease=s21.getSentryRelease;sl0.init=s21.init;sl0.DEFAULT_USER_INCLUDES=Ml1.DEFAULT_USER_INCLUDES;sl0.addRequestDataToEvent=Ml1.addRequestDataToEvent;sl0.extractRequestData=Ml1.extractRequestData;sl0.deepReadDirSync=PN9.deepReadDirSync;sl0.createGetModuleFromFilename=pl0.createGetModuleFromFilename;sl0.enableAnrDetection=SN9.enableAnrDetection;sl0.Handlers=jN9;sl0.captureConsoleIntegration=aj.captureConsoleIntegration;sl0.debugIntegration=aj.debugIntegration;sl0.dedupeIntegration=aj.dedupeIntegration;sl0.extraErrorDataIntegration=aj.extraErrorDataIntegration;sl0.httpClientIntegration=aj.httpClientIntegration;sl0.reportingObserverIntegration=aj.reportingObserverIntegration;sl0.rewriteFramesIntegration=aj.rewriteFramesIntegration;sl0.sessionTimingIntegration=aj.sessionTimingIntegration;sl0.consoleIntegration=_N9.consoleIntegration;sl0.onUncaughtExceptionIntegration=xN9.onUncaughtExceptionIntegration;sl0.onUnhandledRejectionIntegration=vN9.onUnhandledRejectionIntegration;sl0.modulesIntegration=bN9.modulesIntegration;sl0.contextLinesIntegration=fN9.contextLinesIntegration;sl0.nodeContextIntegration=hN9.nodeContextIntegration;sl0.localVariablesIntegration=gN9.localVariablesIntegration;sl0.spotlightIntegration=uN9.spotlightIntegration;sl0.anrIntegration=mN9.anrIntegration;sl0.hapiErrorPlugin=il0.hapiErrorPlugin;sl0.hapiIntegration=il0.hapiIntegration;sl0.Undici=nl0.Undici;sl0.nativeNodeFetchintegration=nl0.nativeNodeFetchintegration;sl0.Http=al0.Http;sl0.httpIntegration=al0.httpIntegration;sl0.trpcMiddleware=dN9.trpcMiddleware;sl0.Integrations=nN9;sl0.cron=aN9;sl0.getModuleFromFilename=iN9});
var Ru0=E((Mu0)=>{Object.defineProperty(Mu0,"__esModule",{value:!0});var f21=wA(),Lu0=ZV(),h21=hC();function IK9(A,B=!0,Q=!0){if(!h21.WINDOW||!h21.WINDOW.location){Lu0.DEBUG_BUILD&&f21.logger.warn("Could not initialize routing instrumentation due to invalid location");return}let D=h21.WINDOW.location.href,Z;if(B)Z=A({name:h21.WINDOW.location.pathname,startTimestamp:f21.browserPerformanceTimeOrigin?f21.browserPerformanceTimeOrigin/1000:void 0,op:"pageload",origin:"auto.pageload.browser",metadata:{source:"url"}});if(Q)f21.addHistoryInstrumentationHandler(({to:G,from:F})=>{if(F===void 0&&D&&D.indexOf(G)!==-1){D=void 0;return}if(F!==G){if(D=void 0,Z)Lu0.DEBUG_BUILD&&f21.logger.log(`[Tracing] Finishing current transaction with op: ${Z.op}`),Z.end();Z=A({name:h21.WINDOW.location.pathname,op:"navigation",origin:"auto.navigation.browser",metadata:{source:"url"}})}})}Mu0.instrumentRoutingWithDefaults=IK9});
var SQ=E((yc1)=>{Object.defineProperty(yc1,"__esModule",{value:!0});var kh0=Gc1(),_h0=Dc1(),VY9=zJ1(),CY9=wJ1(),xh0=WJ1(),_J1=Jl(),Cf=HJ1(),vh0=Yf(),KY9=kb0(),HY9=Zc1(),P21=L21(),bh0=Fc1(),t3=mH(),QN=eq(),Sc1=Fl(),zY9=Ic1(),jc1=DJ1(),fh0=K21(),hh0=LJ1(),gh0=Xc1(),EY9=If0(),uh0=Xf0(),UY9=zf0(),wY9=wf0(),$Y9=qf0(),qY9=FJ1(),xJ1=IO(),mh0=QJ1(),NY9=BJ1(),LY9=Cc1(),MY9=Mf0(),RY9=XJ1(),OY9=Tf0(),TY9=ed1(),PY9=Sf0(),vJ1=BV(),SY9=Il(),jY9=kf0(),yY9=Gl(),dh0=cf0(),ch0=sf0(),lh0=Oc1(),ph0=Tc1(),ih0=Pc1(),kY9=Vh0(),_Y9=yh0(),xY9=kY9;yc1.addTracingExtensions=kh0.addTracingExtensions;yc1.startIdleTransaction=kh0.startIdleTransaction;yc1.IdleTransaction=_h0.IdleTransaction;yc1.TRACING_DEFAULTS=_h0.TRACING_DEFAULTS;yc1.Span=VY9.Span;yc1.Transaction=CY9.Transaction;yc1.extractTraceparentData=xh0.extractTraceparentData;yc1.getActiveTransaction=xh0.getActiveTransaction;Object.defineProperty(yc1,"SpanStatus",{enumerable:!0,get:()=>_J1.SpanStatus});yc1.getSpanStatusFromHttpCode=_J1.getSpanStatusFromHttpCode;yc1.setHttpStatus=_J1.setHttpStatus;yc1.spanStatusfromHttpCode=_J1.spanStatusfromHttpCode;yc1.continueTrace=Cf.continueTrace;yc1.getActiveSpan=Cf.getActiveSpan;yc1.startActiveSpan=Cf.startActiveSpan;yc1.startInactiveSpan=Cf.startInactiveSpan;yc1.startSpan=Cf.startSpan;yc1.startSpanManual=Cf.startSpanManual;yc1.trace=Cf.trace;yc1.getDynamicSamplingContextFromClient=vh0.getDynamicSamplingContextFromClient;yc1.getDynamicSamplingContextFromSpan=vh0.getDynamicSamplingContextFromSpan;yc1.setMeasurement=KY9.setMeasurement;yc1.isValidSampleRate=HY9.isValidSampleRate;yc1.SEMANTIC_ATTRIBUTE_PROFILE_ID=P21.SEMANTIC_ATTRIBUTE_PROFILE_ID;yc1.SEMANTIC_ATTRIBUTE_SENTRY_OP=P21.SEMANTIC_ATTRIBUTE_SENTRY_OP;yc1.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN=P21.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN;yc1.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE=P21.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE;yc1.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE=P21.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE;yc1.createEventEnvelope=bh0.createEventEnvelope;yc1.createSessionEnvelope=bh0.createSessionEnvelope;yc1.addBreadcrumb=t3.addBreadcrumb;yc1.captureCheckIn=t3.captureCheckIn;yc1.captureEvent=t3.captureEvent;yc1.captureException=t3.captureException;yc1.captureMessage=t3.captureMessage;yc1.captureSession=t3.captureSession;yc1.close=t3.close;yc1.configureScope=t3.configureScope;yc1.endSession=t3.endSession;yc1.flush=t3.flush;yc1.getClient=t3.getClient;yc1.getCurrentScope=t3.getCurrentScope;yc1.isInitialized=t3.isInitialized;yc1.lastEventId=t3.lastEventId;yc1.setContext=t3.setContext;yc1.setExtra=t3.setExtra;yc1.setExtras=t3.setExtras;yc1.setTag=t3.setTag;yc1.setTags=t3.setTags;yc1.setUser=t3.setUser;yc1.startSession=t3.startSession;yc1.startTransaction=t3.startTransaction;yc1.withActiveSpan=t3.withActiveSpan;yc1.withIsolationScope=t3.withIsolationScope;yc1.withMonitor=t3.withMonitor;yc1.withScope=t3.withScope;yc1.Hub=QN.Hub;yc1.ensureHubOnCarrier=QN.ensureHubOnCarrier;yc1.getCurrentHub=QN.getCurrentHub;yc1.getHubFromCarrier=QN.getHubFromCarrier;yc1.getIsolationScope=QN.getIsolationScope;yc1.getMainCarrier=QN.getMainCarrier;yc1.makeMain=QN.makeMain;yc1.runWithAsyncContext=QN.runWithAsyncContext;yc1.setAsyncContextStrategy=QN.setAsyncContextStrategy;yc1.setHubOnCarrier=QN.setHubOnCarrier;yc1.closeSession=Sc1.closeSession;yc1.makeSession=Sc1.makeSession;yc1.updateSession=Sc1.updateSession;yc1.SessionFlusher=zY9.SessionFlusher;yc1.Scope=jc1.Scope;yc1.getGlobalScope=jc1.getGlobalScope;yc1.setGlobalScope=jc1.setGlobalScope;yc1.addGlobalEventProcessor=fh0.addGlobalEventProcessor;yc1.notifyEventProcessors=fh0.notifyEventProcessors;yc1.getEnvelopeEndpointWithUrlEncodedAuth=hh0.getEnvelopeEndpointWithUrlEncodedAuth;yc1.getReportDialogEndpoint=hh0.getReportDialogEndpoint;yc1.BaseClient=gh0.BaseClient;yc1.addEventProcessor=gh0.addEventProcessor;yc1.ServerRuntimeClient=EY9.ServerRuntimeClient;yc1.initAndBind=uh0.initAndBind;yc1.setCurrentClient=uh0.setCurrentClient;yc1.createTransport=UY9.createTransport;yc1.makeOfflineTransport=wY9.makeOfflineTransport;yc1.makeMultiplexedTransport=$Y9.makeMultiplexedTransport;yc1.SDK_VERSION=qY9.SDK_VERSION;yc1.addIntegration=xJ1.addIntegration;yc1.convertIntegrationFnToClass=xJ1.convertIntegrationFnToClass;yc1.defineIntegration=xJ1.defineIntegration;yc1.getIntegrationsToSetup=xJ1.getIntegrationsToSetup;yc1.applyScopeDataToEvent=mh0.applyScopeDataToEvent;yc1.mergeScopeData=mh0.mergeScopeData;yc1.prepareEvent=NY9.prepareEvent;yc1.createCheckInEnvelope=LY9.createCheckInEnvelope;yc1.createSpanEnvelope=MY9.createSpanEnvelope;yc1.hasTracingEnabled=RY9.hasTracingEnabled;yc1.isSentryRequestUrl=OY9.isSentryRequestUrl;yc1.handleCallbackErrors=TY9.handleCallbackErrors;yc1.parameterize=PY9.parameterize;yc1.spanIsSampled=vJ1.spanIsSampled;yc1.spanToJSON=vJ1.spanToJSON;yc1.spanToTraceContext=vJ1.spanToTraceContext;yc1.spanToTraceHeader=vJ1.spanToTraceHeader;yc1.getRootSpan=SY9.getRootSpan;yc1.applySdkMetadata=jY9.applySdkMetadata;yc1.DEFAULT_ENVIRONMENT=yY9.DEFAULT_ENVIRONMENT;yc1.ModuleMetadata=dh0.ModuleMetadata;yc1.moduleMetadataIntegration=dh0.moduleMetadataIntegration;yc1.RequestData=ch0.RequestData;yc1.requestDataIntegration=ch0.requestDataIntegration;yc1.InboundFilters=lh0.InboundFilters;yc1.inboundFiltersIntegration=lh0.inboundFiltersIntegration;yc1.FunctionToString=ph0.FunctionToString;yc1.functionToStringIntegration=ph0.functionToStringIntegration;yc1.LinkedErrors=ih0.LinkedErrors;yc1.linkedErrorsIntegration=ih0.linkedErrorsIntegration;yc1.metrics=_Y9.metrics;yc1.Integrations=xY9});
var Sd1=E((Xv0)=>{Object.defineProperty(Xv0,"__esModule",{value:!0});async function PQ9(A){let B=void 0,Q=A[0],D=1;while(D<A.length){let Z=A[D],G=A[D+1];if(D+=2,(Z==="optionalAccess"||Z==="optionalCall")&&Q==null)return;if(Z==="access"||Z==="optionalAccess")B=Q,Q=await G(Q);else if(Z==="call"||Z==="optionalCall")Q=await G((...F)=>Q.call(B,...F)),B=void 0}return Q}Xv0._asyncOptionalChain=PQ9});
var Sf0=E((Pf0)=>{Object.defineProperty(Pf0,"__esModule",{value:!0});function sF9(A,...B){let Q=new String(String.raw(A,...B));return Q.__sentry_template_string__=A.join("\x00").replace(/%/g,"%%").replace(/\0/g,"%s"),Q.__sentry_template_values__=B,Q}Pf0.parameterize=sF9});
var TX1=E((xl0)=>{Object.defineProperty(xl0,"__esModule",{value:!0});var VN9=[["january","1"],["february","2"],["march","3"],["april","4"],["may","5"],["june","6"],["july","7"],["august","8"],["september","9"],["october","10"],["november","11"],["december","12"],["jan","1"],["feb","2"],["mar","3"],["apr","4"],["may","5"],["jun","6"],["jul","7"],["aug","8"],["sep","9"],["oct","10"],["nov","11"],["dec","12"],["sunday","0"],["monday","1"],["tuesday","2"],["wednesday","3"],["thursday","4"],["friday","5"],["saturday","6"],["sun","0"],["mon","1"],["tue","2"],["wed","3"],["thu","4"],["fri","5"],["sat","6"]];function CN9(A){return VN9.reduce((B,[Q,D])=>B.replace(new RegExp(Q,"gi"),D),A)}xl0.replaceCronNames=CN9});
var Tc1=E((Gh0)=>{Object.defineProperty(Gh0,"__esModule",{value:!0});var kI9=wA(),_I9=mH(),Qh0=IO(),Ah0,Dh0="FunctionToString",Bh0=new WeakMap,xI9=()=>{return{name:Dh0,setupOnce(){Ah0=Function.prototype.toString;try{Function.prototype.toString=function(...A){let B=kI9.getOriginalFunction(this),Q=Bh0.has(_I9.getClient())&&B!==void 0?B:this;return Ah0.apply(Q,A)}}catch(A){}},setup(A){Bh0.set(A,!0)}}},Zh0=Qh0.defineIntegration(xI9),vI9=Qh0.convertIntegrationFnToClass(Dh0,Zh0);Gh0.FunctionToString=vI9;Gh0.functionToStringIntegration=Zh0});
var Tf0=E((Of0)=>{Object.defineProperty(Of0,"__esModule",{value:!0});function lF9(A,B){let Q=B&&nF9(B)?B.getClient():B,D=Q&&Q.getDsn(),Z=Q&&Q.getOptions().tunnel;return iF9(A,D)||pF9(A,Z)}function pF9(A,B){if(!B)return!1;return Rf0(A)===Rf0(B)}function iF9(A,B){return B?A.includes(B.host):!1}function Rf0(A){return A[A.length-1]==="/"?A.slice(0,-1):A}function nF9(A){return A.getClient!==void 0}Of0.isSentryRequestUrl=lF9});
var UX1=E((Pd0)=>{Object.defineProperty(Pd0,"__esModule",{value:!0});var IU9=J1("http"),YU9=J1("url"),Md0=SQ(),Tl=wA(),Rd0="Spotlight",WU9=(A={})=>{let B={sidecarUrl:A.sidecarUrl||"http://localhost:8969/stream"};return{name:Rd0,setupOnce(){},setup(Q){if(typeof process==="object"&&process.env)Tl.logger.warn("[Spotlight] It seems you're not in dev mode. Do you really want to have Spotlight enabled?");XU9(Q,B)}}},Od0=Md0.defineIntegration(WU9),JU9=Md0.convertIntegrationFnToClass(Rd0,Od0);function XU9(A,B){let Q=VU9(B.sidecarUrl);if(!Q)return;let D=0;if(typeof A.on!=="function"){Tl.logger.warn("[Spotlight] Cannot connect to spotlight due to missing method on SDK client (`client.on`)");return}A.on("beforeEnvelope",(Z)=>{if(D>3){Tl.logger.warn("[Spotlight] Disabled Sentry -> Spotlight integration due to too many failed requests");return}let G=Tl.serializeEnvelope(Z),I=Td0()({method:"POST",path:Q.pathname,hostname:Q.hostname,port:Q.port,headers:{"Content-Type":"application/x-sentry-envelope"}},(Y)=>{Y.on("data",()=>{}),Y.on("end",()=>{}),Y.setEncoding("utf8")});I.on("error",()=>{D++,Tl.logger.warn("[Spotlight] Failed to send envelope to Spotlight Sidecar")}),I.write(G),I.end()})}function VU9(A){try{return new YU9.URL(`${A}`)}catch(B){Tl.logger.warn(`[Spotlight] Invalid sidecar URL: ${A}`);return}}function Td0(){let{request:A}=IU9;if(CU9(A))return A.__sentry_original__;return A}function CU9(A){return"__sentry_original__"in A}Pd0.Spotlight=JU9;Pd0.getNativeHttpRequest=Td0;Pd0.spotlightIntegration=Od0});
var Uc1=E((eb0)=>{Object.defineProperty(eb0,"__esModule",{value:!0});var OJ1=O21(),AF9=R21();class Kc1{constructor(A){this._value=A}get weight(){return 1}add(A){this._value+=A}toString(){return`${this._value}`}}class Hc1{constructor(A){this._last=A,this._min=A,this._max=A,this._sum=A,this._count=1}get weight(){return 5}add(A){if(this._last=A,A<this._min)this._min=A;if(A>this._max)this._max=A;this._sum+=A,this._count++}toString(){return`${this._last}:${this._min}:${this._max}:${this._sum}:${this._count}`}}class zc1{constructor(A){this._value=[A]}get weight(){return this._value.length}add(A){this._value.push(A)}toString(){return this._value.join(":")}}class Ec1{constructor(A){this.first=A,this._value=new Set([A])}get weight(){return this._value.size}add(A){this._value.add(A)}toString(){return Array.from(this._value).map((A)=>typeof A==="string"?AF9.simpleHash(A):A).join(":")}}var BF9={[OJ1.COUNTER_METRIC_TYPE]:Kc1,[OJ1.GAUGE_METRIC_TYPE]:Hc1,[OJ1.DISTRIBUTION_METRIC_TYPE]:zc1,[OJ1.SET_METRIC_TYPE]:Ec1};eb0.CounterMetric=Kc1;eb0.DistributionMetric=zc1;eb0.GaugeMetric=Hc1;eb0.METRIC_MAP=BF9;eb0.SetMetric=Ec1});
var Ud1=E((Cx0)=>{Object.defineProperty(Cx0,"__esModule",{value:!0});function EB9(A){if(!A)return{};let B=A.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!B)return{};let Q=B[6]||"",D=B[8]||"";return{host:B[4],path:B[5],protocol:B[2],search:Q,hash:D,relative:B[5]+Q+D}}function UB9(A){return A.split(/[\?#]/,1)[0]}function wB9(A){return A.split(/\\?\//).filter((B)=>B.length>0&&B!==",").length}function $B9(A){let{protocol:B,host:Q,path:D}=A,Z=Q&&Q.replace(/^.*@/,"[filtered]:[filtered]@").replace(/(:80)$/,"").replace(/(:443)$/,"")||"";return`${B?`${B}://`:""}${Z}${D}`}Cx0.getNumberOfUrlSegments=wB9;Cx0.getSanitizedUrlString=$B9;Cx0.parseUrl=EB9;Cx0.stripUrlQueryAndFragment=UB9});
var Ug0=E((Eg0,cj)=>{Object.defineProperty(Eg0,"__esModule",{value:!0});var Kf=wA(),MX9=[()=>{return new(Kf.dynamicRequire(cj,"./apollo")).Apollo},()=>{return new(Kf.dynamicRequire(cj,"./apollo")).Apollo({useNestjs:!0})},()=>{return new(Kf.dynamicRequire(cj,"./graphql")).GraphQL},()=>{return new(Kf.dynamicRequire(cj,"./mongo")).Mongo},()=>{return new(Kf.dynamicRequire(cj,"./mongo")).Mongo({mongoose:!0})},()=>{return new(Kf.dynamicRequire(cj,"./mysql")).Mysql},()=>{return new(Kf.dynamicRequire(cj,"./postgres")).Postgres}];Eg0.lazyLoadedNodePerformanceMonitoringIntegrations=MX9});
var Um0=E((Em0)=>{var{_optionalChain:Az9}=wA();Object.defineProperty(Em0,"__esModule",{value:!0});var Hm0=J1("domain"),$f=SQ();function zm0(){return Hm0.active}function Bz9(){let A=zm0();if(!A)return;return $f.ensureHubOnCarrier(A),$f.getHubFromCarrier(A)}function Qz9(A){let B={};return $f.ensureHubOnCarrier(B,A),$f.getHubFromCarrier(B)}function Dz9(A,B){let Q=zm0();if(Q&&Az9([B,"optionalAccess",(F)=>F.reuseExisting]))return A();let D=Hm0.create(),Z=Q?$f.getHubFromCarrier(Q):void 0,G=Qz9(Z);return $f.setHubOnCarrier(D,G),D.bind(()=>{return A()})()}function Zz9(){$f.setAsyncContextStrategy({getCurrentHub:Bz9,runWithAsyncContext:Dz9})}Em0.setDomainAsyncContextStrategy=Zz9});
var Uv0=E((Ev0)=>{Object.defineProperty(Ev0,"__esModule",{value:!0});function hQ9(A){return A.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}Ev0.escapeStringForRegex=hQ9});
var VX1=E((Vd0)=>{Object.defineProperty(Vd0,"__esModule",{value:!0});var Id0=J1("fs"),Yd0=J1("path"),Wd0=SQ(),Xl1,Jd0="Modules";function bE9(){try{return J1.cache?Object.keys(J1.cache):[]}catch(A){return[]}}function fE9(){let A=J1.main&&J1.main.paths||[],B=bE9(),Q={},D={};return B.forEach((Z)=>{let G=Z,F=()=>{let I=G;if(G=Yd0.dirname(I),!G||I===G||D[I])return;if(A.indexOf(G)<0)return F();let Y=Yd0.join(I,"package.json");if(D[I]=!0,!Id0.existsSync(Y))return F();try{let W=JSON.parse(Id0.readFileSync(Y,"utf8"));Q[W.name]=W.version}catch(W){}};F()}),Q}function hE9(){if(!Xl1)Xl1=fE9();return Xl1}var gE9=()=>{return{name:Jd0,setupOnce(){},processEvent(A){return A.modules={...A.modules,...hE9()},A}}},Xd0=Wd0.defineIntegration(gE9),uE9=Wd0.convertIntegrationFnToClass(Jd0,Xd0);Vd0.Modules=uE9;Vd0.modulesIntegration=Xd0});
var Vd1=E((n_0,iW1)=>{Object.defineProperty(n_0,"__esModule",{value:!0});var M29=Xd1();function R29(){return!M29.isBrowserBundle()&&Object.prototype.toString.call(typeof process!=="undefined"?process:0)==="[object process]"}function pW1(A,B){return A.require(B)}function O29(A){let B;try{B=pW1(iW1,A)}catch(Q){}try{let{cwd:Q}=pW1(iW1,"process");B=pW1(iW1,`${Q()}/node_modules/${A}`)}catch(Q){}return B}n_0.dynamicRequire=pW1;n_0.isNodeEnv=R29;n_0.loadModule=O29});
var Vh0=E((Xh0)=>{Object.defineProperty(Xh0,"__esModule",{value:!0});var lI9=Tc1(),pI9=Oc1(),iI9=Pc1();Xh0.FunctionToString=lI9.FunctionToString;Xh0.InboundFilters=pI9.InboundFilters;Xh0.LinkedErrors=iI9.LinkedErrors});
var Vx0=E((Xx0)=>{Object.defineProperty(Xx0,"__esModule",{value:!0});function HB9(A){let B={},Q=0;while(Q<A.length){let D=A.indexOf("=",Q);if(D===-1)break;let Z=A.indexOf(";",Q);if(Z===-1)Z=A.length;else if(Z<D){Q=A.lastIndexOf(";",D-1)+1;continue}let G=A.slice(Q,D).trim();if(B[G]===void 0){let F=A.slice(D+1,Z).trim();if(F.charCodeAt(0)===34)F=F.slice(1,-1);try{B[G]=F.indexOf("%")!==-1?decodeURIComponent(F):F}catch(I){B[G]=F}}Q=Z+1}return B}Xx0.parseCookie=HB9});
var WJ1=E((Qb0)=>{Object.defineProperty(Qb0,"__esModule",{value:!0});var Bb0=wA(),c79=eq();function l79(A){return(A||c79.getCurrentHub()).getScope().getTransaction()}var p79=Bb0.extractTraceparentData;Qb0.stripUrlQueryAndFragment=Bb0.stripUrlQueryAndFragment;Qb0.extractTraceparentData=p79;Qb0.getActiveTransaction=l79});
var Wd1=E((f_0)=>{Object.defineProperty(f_0,"__esModule",{value:!0});var cW1=hH(),dW1=gH(),B29=kW(),lW1=QO(),Q29=B29.GLOBAL_OBJ,F21="__sentry_xhr_v3__";function D29(A){lW1.addHandler("xhr",A),lW1.maybeInstrument("xhr",b_0)}function b_0(){if(!Q29.XMLHttpRequest)return;let A=XMLHttpRequest.prototype;dW1.fill(A,"open",function(B){return function(...Q){let D=Date.now(),Z=cW1.isString(Q[0])?Q[0].toUpperCase():void 0,G=Z29(Q[1]);if(!Z||!G)return B.apply(this,Q);if(this[F21]={method:Z,url:G,request_headers:{}},Z==="POST"&&G.match(/sentry_key/))this.__sentry_own_request__=!0;let F=()=>{let I=this[F21];if(!I)return;if(this.readyState===4){try{I.status_code=this.status}catch(W){}let Y={args:[Z,G],endTimestamp:Date.now(),startTimestamp:D,xhr:this};lW1.triggerHandlers("xhr",Y)}};if("onreadystatechange"in this&&typeof this.onreadystatechange==="function")dW1.fill(this,"onreadystatechange",function(I){return function(...Y){return F(),I.apply(this,Y)}});else this.addEventListener("readystatechange",F);return dW1.fill(this,"setRequestHeader",function(I){return function(...Y){let[W,J]=Y,X=this[F21];if(X&&cW1.isString(W)&&cW1.isString(J))X.request_headers[W.toLowerCase()]=J;return I.apply(this,Y)}}),B.apply(this,Q)}}),dW1.fill(A,"send",function(B){return function(...Q){let D=this[F21];if(!D)return B.apply(this,Q);if(Q[0]!==void 0)D.body=Q[0];let Z={args:[D.method,D.url],startTimestamp:Date.now(),xhr:this};return lW1.triggerHandlers("xhr",Z),B.apply(this,Q)}})}function Z29(A){if(cW1.isString(A))return A;try{return A.toString()}catch(B){}return}f_0.SENTRY_XHR_DATA_KEY=F21;f_0.addXhrInstrumentationHandler=D29;f_0.instrumentXHR=b_0});
var Wu0=E((Yu0)=>{Object.defineProperty(Yu0,"__esModule",{value:!0});function CC9(A){return typeof A==="number"&&isFinite(A)}function KC9(A,{startTimestamp:B,...Q}){if(B&&A.startTimestamp>B)A.startTimestamp=B;return A.startChild({startTimestamp:B,...Q})}Yu0._startChild=KC9;Yu0.isMeasurementValue=CC9});
var XJ1=E((Ib0)=>{Object.defineProperty(Ib0,"__esModule",{value:!0});var YD9=mH();function WD9(A){if(typeof __SENTRY_TRACING__==="boolean"&&!__SENTRY_TRACING__)return!1;let B=YD9.getClient(),Q=A||B&&B.getOptions();return!!Q&&(Q.enableTracing||("tracesSampleRate"in Q)||("tracesSampler"in Q))}Ib0.hasTracingEnabled=WD9});
var XX1=E((Fd0)=>{Object.defineProperty(Fd0,"__esModule",{value:!0});var Gd0=Zd0(),kE9=Gd0.LocalVariablesSync,_E9=Gd0.localVariablesSyncIntegration;Fd0.LocalVariables=kE9;Fd0.localVariablesIntegration=_E9});
var Xc0=E((Jc0)=>{Object.defineProperty(Jc0,"__esModule",{value:!0});var sw9=ZX1(),rw9=YX1(),ow9=HX1(),tw9=EX1(),ew9=VX1(),A$9=IX1(),B$9=GX1(),Q$9=SQ(),D$9=XX1(),Z$9=$X1(),G$9=UX1(),F$9=LX1(),I$9=$l1();Jc0.Console=sw9.Console;Jc0.Http=rw9.Http;Jc0.OnUncaughtException=ow9.OnUncaughtException;Jc0.OnUnhandledRejection=tw9.OnUnhandledRejection;Jc0.Modules=ew9.Modules;Jc0.ContextLines=A$9.ContextLines;Jc0.Context=B$9.Context;Jc0.RequestData=Q$9.RequestData;Jc0.LocalVariables=D$9.LocalVariables;Jc0.Undici=Z$9.Undici;Jc0.Spotlight=G$9.Spotlight;Jc0.Anr=F$9.Anr;Jc0.Hapi=I$9.Hapi});
var Xc1=E((rb0)=>{Object.defineProperty(rb0,"__esModule",{value:!0});var v8=wA(),OG9=LJ1(),BN=kG(),lb0=Fc1(),TG9=mH(),PG9=eq(),RJ1=IO(),SG9=cb0(),pb0=Fl(),jG9=Yf(),yG9=BJ1(),ib0="Not capturing exception because it's already been captured.";class nb0{constructor(A){if(this._options=A,this._integrations={},this._integrationsInitialized=!1,this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],A.dsn)this._dsn=v8.makeDsn(A.dsn);else BN.DEBUG_BUILD&&v8.logger.warn("No DSN provided, client will not send events.");if(this._dsn){let B=OG9.getEnvelopeEndpointWithUrlEncodedAuth(this._dsn,A);this._transport=A.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...A.transportOptions,url:B})}}captureException(A,B,Q){if(v8.checkOrSetAlreadyCaught(A)){BN.DEBUG_BUILD&&v8.logger.log(ib0);return}let D=B&&B.event_id;return this._process(this.eventFromException(A,B).then((Z)=>this._captureEvent(Z,B,Q)).then((Z)=>{D=Z})),D}captureMessage(A,B,Q,D){let Z=Q&&Q.event_id,G=v8.isParameterizedString(A)?A:String(A),F=v8.isPrimitive(A)?this.eventFromMessage(G,B,Q):this.eventFromException(A,Q);return this._process(F.then((I)=>this._captureEvent(I,Q,D)).then((I)=>{Z=I})),Z}captureEvent(A,B,Q){if(B&&B.originalException&&v8.checkOrSetAlreadyCaught(B.originalException)){BN.DEBUG_BUILD&&v8.logger.log(ib0);return}let D=B&&B.event_id,G=(A.sdkProcessingMetadata||{}).capturedSpanScope;return this._process(this._captureEvent(A,B,G||Q).then((F)=>{D=F})),D}captureSession(A){if(typeof A.release!=="string")BN.DEBUG_BUILD&&v8.logger.warn("Discarded session because of missing or non-string release");else this.sendSession(A),pb0.updateSession(A,{init:!1})}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(A){let B=this._transport;if(B){if(this.metricsAggregator)this.metricsAggregator.flush();return this._isClientDoneProcessing(A).then((Q)=>{return B.flush(A).then((D)=>Q&&D)})}else return v8.resolvedSyncPromise(!0)}close(A){return this.flush(A).then((B)=>{if(this.getOptions().enabled=!1,this.metricsAggregator)this.metricsAggregator.close();return B})}getEventProcessors(){return this._eventProcessors}addEventProcessor(A){this._eventProcessors.push(A)}setupIntegrations(A){if(A&&!this._integrationsInitialized||this._isEnabled()&&!this._integrationsInitialized)this._setupIntegrations()}init(){if(this._isEnabled())this._setupIntegrations()}getIntegrationById(A){return this.getIntegrationByName(A)}getIntegrationByName(A){return this._integrations[A]}getIntegration(A){try{return this._integrations[A.id]||null}catch(B){return BN.DEBUG_BUILD&&v8.logger.warn(`Cannot retrieve integration ${A.id} from the current Client`),null}}addIntegration(A){let B=this._integrations[A.name];if(RJ1.setupIntegration(this,A,this._integrations),!B)RJ1.afterSetupIntegrations(this,[A])}sendEvent(A,B={}){this.emit("beforeSendEvent",A,B);let Q=lb0.createEventEnvelope(A,this._dsn,this._options._metadata,this._options.tunnel);for(let Z of B.attachments||[])Q=v8.addItemToEnvelope(Q,v8.createAttachmentEnvelopeItem(Z,this._options.transportOptions&&this._options.transportOptions.textEncoder));let D=this._sendEnvelope(Q);if(D)D.then((Z)=>this.emit("afterSendEvent",A,Z),null)}sendSession(A){let B=lb0.createSessionEnvelope(A,this._dsn,this._options._metadata,this._options.tunnel);this._sendEnvelope(B)}recordDroppedEvent(A,B,Q){if(this._options.sendClientReports){let D=typeof Q==="number"?Q:1,Z=`${A}:${B}`;BN.DEBUG_BUILD&&v8.logger.log(`Recording outcome: "${Z}"${D>1?` (${D} times)`:""}`),this._outcomes[Z]=(this._outcomes[Z]||0)+D}}captureAggregateMetrics(A){BN.DEBUG_BUILD&&v8.logger.log(`Flushing aggregated metrics, number of metrics: ${A.length}`);let B=SG9.createMetricEnvelope(A,this._dsn,this._options._metadata,this._options.tunnel);this._sendEnvelope(B)}on(A,B){if(!this._hooks[A])this._hooks[A]=[];this._hooks[A].push(B)}emit(A,...B){if(this._hooks[A])this._hooks[A].forEach((Q)=>Q(...B))}_setupIntegrations(){let{integrations:A}=this._options;this._integrations=RJ1.setupIntegrations(this,A),RJ1.afterSetupIntegrations(this,A),this._integrationsInitialized=!0}_updateSessionFromEvent(A,B){let Q=!1,D=!1,Z=B.exception&&B.exception.values;if(Z){D=!0;for(let I of Z){let Y=I.mechanism;if(Y&&Y.handled===!1){Q=!0;break}}}let G=A.status==="ok";if(G&&A.errors===0||G&&Q)pb0.updateSession(A,{...Q&&{status:"crashed"},errors:A.errors||Number(D||Q)}),this.captureSession(A)}_isClientDoneProcessing(A){return new v8.SyncPromise((B)=>{let Q=0,D=1,Z=setInterval(()=>{if(this._numProcessing==0)clearInterval(Z),B(!0);else if(Q+=D,A&&Q>=A)clearInterval(Z),B(!1)},D)})}_isEnabled(){return this.getOptions().enabled!==!1&&this._transport!==void 0}_prepareEvent(A,B,Q,D=PG9.getIsolationScope()){let Z=this.getOptions(),G=Object.keys(this._integrations);if(!B.integrations&&G.length>0)B.integrations=G;return this.emit("preprocessEvent",A,B),yG9.prepareEvent(Z,A,B,Q,this,D).then((F)=>{if(F===null)return F;let I={...D.getPropagationContext(),...Q?Q.getPropagationContext():void 0};if(!(F.contexts&&F.contexts.trace)&&I){let{traceId:W,spanId:J,parentSpanId:X,dsc:V}=I;F.contexts={trace:{trace_id:W,span_id:J,parent_span_id:X},...F.contexts};let C=V?V:jG9.getDynamicSamplingContextFromClient(W,this,Q);F.sdkProcessingMetadata={dynamicSamplingContext:C,...F.sdkProcessingMetadata}}return F})}_captureEvent(A,B={},Q){return this._processEvent(A,B,Q).then((D)=>{return D.event_id},(D)=>{if(BN.DEBUG_BUILD){let Z=D;if(Z.logLevel==="log")v8.logger.log(Z.message);else v8.logger.warn(Z)}return})}_processEvent(A,B,Q){let D=this.getOptions(),{sampleRate:Z}=D,G=sb0(A),F=ab0(A),I=A.type||"error",Y=`before send for type \`${I}\``;if(F&&typeof Z==="number"&&Math.random()>Z)return this.recordDroppedEvent("sample_rate","error",A),v8.rejectedSyncPromise(new v8.SentryError(`Discarding event because it's not included in the random sample (sampling rate = ${Z})`,"log"));let W=I==="replay_event"?"replay":I,X=(A.sdkProcessingMetadata||{}).capturedSpanIsolationScope;return this._prepareEvent(A,B,Q,X).then((V)=>{if(V===null)throw this.recordDroppedEvent("event_processor",W,A),new v8.SentryError("An event processor returned `null`, will not send event.","log");if(B.data&&B.data.__sentry__===!0)return V;let K=_G9(D,V,B);return kG9(K,Y)}).then((V)=>{if(V===null){if(this.recordDroppedEvent("before_send",W,A),G){let z=1+(A.spans||[]).length;this.recordDroppedEvent("before_send","span",z)}throw new v8.SentryError(`${Y} returned \`null\`, will not send event.`,"log")}let C=Q&&Q.getSession();if(!G&&C)this._updateSessionFromEvent(C,V);if(G){let H=V.sdkProcessingMetadata&&V.sdkProcessingMetadata.spanCountBeforeProcessing||0,z=V.spans?V.spans.length:0,$=H-z;if($>0)this.recordDroppedEvent("before_send","span",$)}let K=V.transaction_info;if(G&&K&&V.transaction!==A.transaction)V.transaction_info={...K,source:"custom"};return this.sendEvent(V,B),V}).then(null,(V)=>{if(V instanceof v8.SentryError)throw V;throw this.captureException(V,{data:{__sentry__:!0},originalException:V}),new v8.SentryError(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${V}`)})}_process(A){this._numProcessing++,A.then((B)=>{return this._numProcessing--,B},(B)=>{return this._numProcessing--,B})}_sendEnvelope(A){if(this.emit("beforeEnvelope",A),this._isEnabled()&&this._transport)return this._transport.send(A).then(null,(B)=>{BN.DEBUG_BUILD&&v8.logger.error("Error while sending event:",B)});else BN.DEBUG_BUILD&&v8.logger.error("Transport disabled")}_clearOutcomes(){let A=this._outcomes;return this._outcomes={},Object.keys(A).map((B)=>{let[Q,D]=B.split(":");return{reason:Q,category:D,quantity:A[B]}})}}function kG9(A,B){let Q=`${B} must return \`null\` or a valid event.`;if(v8.isThenable(A))return A.then((D)=>{if(!v8.isPlainObject(D)&&D!==null)throw new v8.SentryError(Q);return D},(D)=>{throw new v8.SentryError(`${B} rejected with ${D}`)});else if(!v8.isPlainObject(A)&&A!==null)throw new v8.SentryError(Q);return A}function _G9(A,B,Q){let{beforeSend:D,beforeSendTransaction:Z}=A;if(ab0(B)&&D)return D(B,Q);if(sb0(B)&&Z){if(B.spans){let G=B.spans.length;B.sdkProcessingMetadata={...B.sdkProcessingMetadata,spanCountBeforeProcessing:G}}return Z(B,Q)}return B}function ab0(A){return A.type===void 0}function sb0(A){return A.type==="transaction"}function xG9(A){let B=TG9.getClient();if(!B||!B.addEventProcessor)return;B.addEventProcessor(A)}rb0.BaseClient=nb0;rb0.addEventProcessor=xG9});
var Xd1=E((i_0)=>{Object.defineProperty(i_0,"__esModule",{value:!0});function $29(){return typeof __SENTRY_BROWSER_BUNDLE__!=="undefined"&&!!__SENTRY_BROWSER_BUNDLE__}function q29(){return"npm"}i_0.getSDKSource=q29;i_0.isBrowserBundle=$29});
var Xf0=E((Jf0)=>{Object.defineProperty(Jf0,"__esModule",{value:!0});var Yf0=wA(),wF9=kG(),$F9=mH(),qF9=eq();function NF9(A,B){if(B.debug===!0)if(wF9.DEBUG_BUILD)Yf0.logger.enable();else Yf0.consoleSandbox(()=>{console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.")});$F9.getCurrentScope().update(B.initialScope);let D=new A(B);Wf0(D),LF9(D)}function Wf0(A){let Q=qF9.getCurrentHub().getStackTop();Q.client=A,Q.scope.setClient(A)}function LF9(A){if(A.init)A.init();else if(A.setupIntegrations)A.setupIntegrations()}Jf0.initAndBind=NF9;Jf0.setCurrentClient=Wf0});
var YX1=E((rm0)=>{var{_optionalChain:Ol}=wA();Object.defineProperty(rm0,"__esModule",{value:!0});var bW=SQ(),mC=wA(),Il1=l21(),IE9=wf(),p21=im0(),YE9=(A={})=>{let{breadcrumbs:B,tracing:Q,shouldCreateSpanForRequest:D}=A,Z={breadcrumbs:B,tracing:Q===!1?!1:mC.dropUndefinedKeys({enableIfHasTracingEnabled:Q===!0?void 0:!0,shouldCreateSpanForRequest:D})};return new Nf(Z)},WE9=bW.defineIntegration(YE9);class Nf{static __initStatic(){this.id="Http"}__init(){this.name=Nf.id}constructor(A={}){Nf.prototype.__init.call(this),this._breadcrumbs=typeof A.breadcrumbs==="undefined"?!0:A.breadcrumbs,this._tracing=!A.tracing?void 0:A.tracing===!0?{}:A.tracing}setupOnce(A,B){let Q=Ol([B,"call",(Y)=>Y(),"access",(Y)=>Y.getClient,"call",(Y)=>Y(),"optionalAccess",(Y)=>Y.getOptions,"call",(Y)=>Y()]),D=am0(this._tracing,Q);if(!this._breadcrumbs&&!D)return;if(Q&&Q.instrumenter!=="sentry"){Il1.DEBUG_BUILD&&mC.logger.log("HTTP Integration is skipped because of instrumenter configuration.");return}let Z=sm0(D,this._tracing,Q),G=Ol([Q,"optionalAccess",(Y)=>Y.tracePropagationTargets])||Ol([this,"access",(Y)=>Y._tracing,"optionalAccess",(Y)=>Y.tracePropagationTargets]),F=J1("http"),I=nm0(F,this._breadcrumbs,Z,G);if(mC.fill(F,"get",I),mC.fill(F,"request",I),IE9.NODE_VERSION.major>8){let Y=J1("https"),W=nm0(Y,this._breadcrumbs,Z,G);mC.fill(Y,"get",W),mC.fill(Y,"request",W)}}}Nf.__initStatic();function nm0(A,B,Q,D){let Z=new mC.LRUMap(100),G=new mC.LRUMap(100),F=(W)=>{if(Q===void 0)return!0;let J=Z.get(W);if(J!==void 0)return J;let X=Q(W);return Z.set(W,X),X},I=(W)=>{if(D===void 0)return!0;let J=G.get(W);if(J!==void 0)return J;let X=mC.stringMatchesSomePattern(W,D);return G.set(W,X),X};function Y(W,J,X,V){if(!bW.getCurrentHub().getIntegration(Nf))return;bW.addBreadcrumb({category:"http",data:{status_code:V&&V.statusCode,...J},type:"http"},{event:W,request:X,response:V})}return function W(J){return function X(...V){let C=p21.normalizeRequestArgs(A,V),K=C[0],H=p21.extractRawUrl(K),z=p21.extractUrl(K),$=bW.getClient();if(bW.isSentryRequestUrl(z,$))return J.apply(A,C);let L=bW.getCurrentScope(),N=bW.getIsolationScope(),O=bW.getActiveSpan(),R=XE9(z,K),T=F(H)?Ol([O,"optionalAccess",(j)=>j.startChild,"call",(j)=>j({op:"http.client",origin:"auto.http.node.http",description:`${R["http.method"]} ${R.url}`,data:R})]):void 0;if($&&I(H)){let{traceId:j,spanId:f,sampled:y,dsc:c}={...N.getPropagationContext(),...L.getPropagationContext()},h=T?bW.spanToTraceHeader(T):mC.generateSentryTraceHeader(j,f,y),a=mC.dynamicSamplingContextToSentryBaggageHeader(c||(T?bW.getDynamicSamplingContextFromSpan(T):bW.getDynamicSamplingContextFromClient(j,$,L)));JE9(K,z,h,a)}else Il1.DEBUG_BUILD&&mC.logger.log(`[Tracing] Not adding sentry-trace header to outgoing request (${z}) due to mismatching tracePropagationTargets option.`);return J.apply(A,C).once("response",function(j){let f=this;if(B)Y("response",R,f,j);if(T){if(j.statusCode)bW.setHttpStatus(T,j.statusCode);T.updateName(p21.cleanSpanDescription(bW.spanToJSON(T).description||"",K,f)||""),T.end()}}).once("error",function(){let j=this;if(B)Y("error",R,j);if(T)bW.setHttpStatus(T,500),T.updateName(p21.cleanSpanDescription(bW.spanToJSON(T).description||"",K,j)||""),T.end()})}}}function JE9(A,B,Q,D){if((A.headers||{})["sentry-trace"])return;Il1.DEBUG_BUILD&&mC.logger.log(`[Tracing] Adding sentry-trace header ${Q} to outgoing request to "${B}": `),A.headers={...A.headers,"sentry-trace":Q,...D&&D.length>0&&{baggage:VE9(A,D)}}}function XE9(A,B){let Q=B.method||"GET",D={url:A,"http.method":Q};if(B.hash)D["http.fragment"]=B.hash.substring(1);if(B.search)D["http.query"]=B.search.substring(1);return D}function VE9(A,B){if(!A.headers||!A.headers.baggage)return B;else if(!B)return A.headers.baggage;else if(Array.isArray(A.headers.baggage))return[...A.headers.baggage,B];return[A.headers.baggage,B]}function am0(A,B){return A===void 0?!1:A.enableIfHasTracingEnabled?bW.hasTracingEnabled(B):!0}function sm0(A,B,Q){return A?Ol([B,"optionalAccess",(Z)=>Z.shouldCreateSpanForRequest])||Ol([Q,"optionalAccess",(Z)=>Z.shouldCreateSpanForRequest]):()=>!1}rm0.Http=Nf;rm0._getShouldCreateSpanForRequest=sm0;rm0._shouldCreateSpans=am0;rm0.httpIntegration=WE9});
var Yd1=E((v_0)=>{Object.defineProperty(v_0,"__esModule",{value:!0});var x_0=gH();rq();oU();var rA9=kW(),oA9=Id1(),mW1=QO(),G21=rA9.GLOBAL_OBJ,uW1;function tA9(A){mW1.addHandler("history",A),mW1.maybeInstrument("history",eA9)}function eA9(){if(!oA9.supportsHistory())return;let A=G21.onpopstate;G21.onpopstate=function(...Q){let D=G21.location.href,Z=uW1;uW1=D;let G={from:Z,to:D};if(mW1.triggerHandlers("history",G),A)try{return A.apply(this,Q)}catch(F){}};function B(Q){return function(...D){let Z=D.length>2?D[2]:void 0;if(Z){let G=uW1,F=String(Z);uW1=F;let I={from:G,to:F};mW1.triggerHandlers("history",I)}return Q.apply(this,D)}}x_0.fill(G21.history,"pushState",B),x_0.fill(G21.history,"replaceState",B)}v_0.addHistoryInstrumentationHandler=tA9});
var Yf=E((cv0)=>{Object.defineProperty(cv0,"__esModule",{value:!0});var o39=wA(),t39=Gl(),mv0=mH(),e39=Il(),pd1=BV();function dv0(A,B,Q){let D=B.getOptions(),{publicKey:Z}=B.getDsn()||{},{segment:G}=Q&&Q.getUser()||{},F=o39.dropUndefinedKeys({environment:D.environment||t39.DEFAULT_ENVIRONMENT,release:D.release,user_segment:G,public_key:Z,trace_id:A});return B.emit&&B.emit("createDsc",F),F}function A79(A){let B=mv0.getClient();if(!B)return{};let Q=dv0(pd1.spanToJSON(A).trace_id||"",B,mv0.getCurrentScope()),D=e39.getRootSpan(A);if(!D)return Q;let Z=D&&D._frozenDynamicSamplingContext;if(Z)return Z;let{sampleRate:G,source:F}=D.metadata;if(G!=null)Q.sample_rate=`${G}`;let I=pd1.spanToJSON(D);if(F&&F!=="url")Q.transaction=I.description;return Q.sampled=String(pd1.spanIsSampled(D)),B.emit&&B.emit("createDsc",Q),Q}cv0.getDynamicSamplingContextFromClient=dv0;cv0.getDynamicSamplingContextFromSpan=A79});
var Yl0=E((Il0)=>{Object.defineProperty(Il0,"__esModule",{value:!0});var Eq9=SQ(),Fl0="Transaction",Uq9=()=>{return{name:Fl0,setupOnce(){},processEvent(A){let B=$q9(A);for(let Q=B.length-1;Q>=0;Q--){let D=B[Q];if(D.in_app===!0){A.transaction=qq9(D);break}}return A}}},wq9=Eq9.convertIntegrationFnToClass(Fl0,Uq9);function $q9(A){let B=A.exception&&A.exception.values&&A.exception.values[0];return B&&B.stacktrace&&B.stacktrace.frames||[]}function qq9(A){return A.module||A.function?`${A.module||"?"}/${A.function||"?"}`:"<unknown>"}Il0.Transaction=wq9});
var ZV=E((nh0)=>{Object.defineProperty(nh0,"__esModule",{value:!0});var lJ9=typeof __SENTRY_DEBUG__==="undefined"||__SENTRY_DEBUG__;nh0.DEBUG_BUILD=lJ9});
var ZX1=E((Om0)=>{Object.defineProperty(Om0,"__esModule",{value:!0});var Hz9=J1("util"),DX1=SQ(),Lm0=wA(),Mm0="Console",zz9=()=>{return{name:Mm0,setupOnce(){},setup(A){Lm0.addConsoleInstrumentationHandler(({args:B,level:Q})=>{if(DX1.getClient()!==A)return;DX1.addBreadcrumb({category:"console",level:Lm0.severityLevelFromString(Q),message:Hz9.format.apply(void 0,B)},{input:[...B],level:Q})})}}},Rm0=DX1.defineIntegration(zz9),Ez9=DX1.convertIntegrationFnToClass(Mm0,Rm0);Om0.Console=Ez9;Om0.consoleIntegration=Rm0});
var Zc1=E((Pb0)=>{Object.defineProperty(Pb0,"__esModule",{value:!0});var Xf=wA(),Hl=kG(),NJ1=L21(),ZZ9=XJ1(),GZ9=BV();function FZ9(A,B,Q){if(!ZZ9.hasTracingEnabled(B))return A.sampled=!1,A;if(A.sampled!==void 0)return A.setAttribute(NJ1.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE,Number(A.sampled)),A;let D;if(typeof B.tracesSampler==="function")D=B.tracesSampler(Q),A.setAttribute(NJ1.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE,Number(D));else if(Q.parentSampled!==void 0)D=Q.parentSampled;else if(typeof B.tracesSampleRate!=="undefined")D=B.tracesSampleRate,A.setAttribute(NJ1.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE,Number(D));else D=1,A.setAttribute(NJ1.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE,D);if(!Tb0(D))return Hl.DEBUG_BUILD&&Xf.logger.warn("[Tracing] Discarding transaction because of invalid sample rate."),A.sampled=!1,A;if(!D)return Hl.DEBUG_BUILD&&Xf.logger.log(`[Tracing] Discarding transaction because ${typeof B.tracesSampler==="function"?"tracesSampler returned 0 or false":"a negative sampling decision was inherited or tracesSampleRate is set to 0"}`),A.sampled=!1,A;if(A.sampled=Math.random()<D,!A.sampled)return Hl.DEBUG_BUILD&&Xf.logger.log(`[Tracing] Discarding transaction because it's not included in the random sample (sampling rate = ${Number(D)})`),A;return Hl.DEBUG_BUILD&&Xf.logger.log(`[Tracing] starting ${A.op} transaction - ${GZ9.spanToJSON(A).description}`),A}function Tb0(A){if(Xf.isNaN(A)||!(typeof A==="number"||typeof A==="boolean"))return Hl.DEBUG_BUILD&&Xf.logger.warn(`[Tracing] Given sample rate is invalid. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(A)} of type ${JSON.stringify(typeof A)}.`),!1;if(A<0||A>1)return Hl.DEBUG_BUILD&&Xf.logger.warn(`[Tracing] Given sample rate is invalid. Sample rate must be between 0 and 1. Got ${A}.`),!1;return!0}Pb0.isValidSampleRate=Tb0;Pb0.sampleTransaction=FZ9});
var Zd0=E((Dd0)=>{var{_optionalChain:FZ}=wA();Object.defineProperty(Dd0,"__esModule",{value:!0});var Wl1=SQ(),WX1=wA(),RE9=wf(),JX1=em0();function Jl1(A){let B=[],Q=!1;function D(F){if(B=[],Q)return;Q=!0,A(F)}B.push(D);function Z(F){B.push(F)}function G(F){let I=B.pop()||D;try{I(F)}catch(Y){D(F)}}return{add:Z,next:G}}class Ad0{constructor(){let{Session:A}=J1("inspector");this._session=new A}configureAndConnect(A,B){this._session.connect(),this._session.on("Debugger.paused",(Q)=>{A(Q,()=>{this._session.post("Debugger.resume")})}),this._session.post("Debugger.enable"),this._session.post("Debugger.setPauseOnExceptions",{state:B?"all":"uncaught"})}setPauseOnExceptions(A){this._session.post("Debugger.setPauseOnExceptions",{state:A?"all":"uncaught"})}getLocalVariables(A,B){this._getProperties(A,(Q)=>{let{add:D,next:Z}=Jl1(B);for(let G of Q)if(FZ([G,"optionalAccess",(F)=>F.value,"optionalAccess",(F)=>F.objectId])&&FZ([G,"optionalAccess",(F)=>F.value,"access",(F)=>F.className])==="Array"){let F=G.value.objectId;D((I)=>this._unrollArray(F,G.name,I,Z))}else if(FZ([G,"optionalAccess",(F)=>F.value,"optionalAccess",(F)=>F.objectId])&&FZ([G,"optionalAccess",(F)=>F.value,"optionalAccess",(F)=>F.className])==="Object"){let F=G.value.objectId;D((I)=>this._unrollObject(F,G.name,I,Z))}else if(FZ([G,"optionalAccess",(F)=>F.value,"optionalAccess",(F)=>F.value])!=null||FZ([G,"optionalAccess",(F)=>F.value,"optionalAccess",(F)=>F.description])!=null)D((F)=>this._unrollOther(G,F,Z));Z({})})}_getProperties(A,B){this._session.post("Runtime.getProperties",{objectId:A,ownProperties:!0},(Q,D)=>{if(Q)B([]);else B(D.result)})}_unrollArray(A,B,Q,D){this._getProperties(A,(Z)=>{Q[B]=Z.filter((G)=>G.name!=="length"&&!isNaN(parseInt(G.name,10))).sort((G,F)=>parseInt(G.name,10)-parseInt(F.name,10)).map((G)=>FZ([G,"optionalAccess",(F)=>F.value,"optionalAccess",(F)=>F.value])),D(Q)})}_unrollObject(A,B,Q,D){this._getProperties(A,(Z)=>{Q[B]=Z.map((G)=>[G.name,FZ([G,"optionalAccess",(F)=>F.value,"optionalAccess",(F)=>F.value])]).reduce((G,[F,I])=>{return G[F]=I,G},{}),D(Q)})}_unrollOther(A,B,Q){if(FZ([A,"optionalAccess",(D)=>D.value,"optionalAccess",(D)=>D.value])!=null)B[A.name]=A.value.value;else if(FZ([A,"optionalAccess",(D)=>D.value,"optionalAccess",(D)=>D.description])!=null&&FZ([A,"optionalAccess",(D)=>D.value,"optionalAccess",(D)=>D.type])!=="function")B[A.name]=`<${A.value.description}>`;Q(B)}}function OE9(){try{return new Ad0}catch(A){return}}var Bd0="LocalVariables",TE9=(A={},B=OE9())=>{let Q=new WX1.LRUMap(20),D,Z=!1;function G(Y,{params:{reason:W,data:J,callFrames:X}},V){if(W!=="exception"&&W!=="promiseRejection"){V();return}FZ([D,"optionalCall",(z)=>z()]);let C=JX1.hashFromStack(Y,FZ([J,"optionalAccess",(z)=>z.description]));if(C==null){V();return}let{add:K,next:H}=Jl1((z)=>{Q.set(C,z),V()});for(let z=0;z<Math.min(X.length,5);z++){let{scopeChain:$,functionName:L,this:N}=X[z],O=$.find((T)=>T.type==="local"),R=N.className==="global"||!N.className?L:`${N.className}.${L}`;if(FZ([O,"optionalAccess",(T)=>T.object,"access",(T)=>T.objectId])===void 0)K((T)=>{T[z]={function:R},H(T)});else{let T=O.object.objectId;K((j)=>FZ([B,"optionalAccess",(f)=>f.getLocalVariables,"call",(f)=>f(T,(y)=>{j[z]={function:R,vars:y},H(j)})]))}}H([])}function F(Y){let W=JX1.hashFrames(FZ([Y,"optionalAccess",(V)=>V.stacktrace,"optionalAccess",(V)=>V.frames]));if(W===void 0)return;let J=Q.remove(W);if(J===void 0)return;let X=(FZ([Y,"access",(V)=>V.stacktrace,"optionalAccess",(V)=>V.frames])||[]).filter((V)=>V.function!=="new Promise");for(let V=0;V<X.length;V++){let C=X.length-V-1;if(!X[C]||!J[V])break;if(J[V].vars===void 0||X[C].in_app===!1||!JX1.functionNamesMatch(X[C].function,J[V].function))continue;X[C].vars=J[V].vars}}function I(Y){for(let W of FZ([Y,"optionalAccess",(J)=>J.exception,"optionalAccess",(J)=>J.values])||[])F(W);return Y}return{name:Bd0,setupOnce(){let Y=Wl1.getClient(),W=FZ([Y,"optionalAccess",(J)=>J.getOptions,"call",(J)=>J()]);if(B&&FZ([W,"optionalAccess",(J)=>J.includeLocalVariables])){if(RE9.NODE_VERSION.major<18){WX1.logger.log("The `LocalVariables` integration is only supported on Node >= v18.");return}let X=A.captureAllExceptions!==!1;if(B.configureAndConnect((V,C)=>G(W.stackParser,V,C),X),X){let V=A.maxExceptionsPerSecond||50;D=JX1.createRateLimiter(V,()=>{WX1.logger.log("Local variables rate-limit lifted."),FZ([B,"optionalAccess",(C)=>C.setPauseOnExceptions,"call",(C)=>C(!0)])},(C)=>{WX1.logger.log(`Local variables rate-limit exceeded. Disabling capturing of caught exceptions for ${C} seconds.`),FZ([B,"optionalAccess",(K)=>K.setPauseOnExceptions,"call",(K)=>K(!1)])})}Z=!0}},processEvent(Y){if(Z)return I(Y);return Y},_getCachedFramesCount(){return Q.size},_getFirstCachedFrame(){return Q.values()[0]}}},Qd0=Wl1.defineIntegration(TE9),PE9=Wl1.convertIntegrationFnToClass(Bd0,Qd0);Dd0.LocalVariablesSync=PE9;Dd0.createCallbackList=Jl1;Dd0.localVariablesSyncIntegration=Qd0});
var Zg0=E((Dg0)=>{var{_optionalChain:IX9}=wA();Object.defineProperty(Dg0,"__esModule",{value:!0});var S21=wA(),xc1=ZV(),YX9=mj();class gJ1{static __initStatic(){this.id="Mysql"}constructor(){this.name=gJ1.id}loadDependency(){return this._module=this._module||S21.loadModule("mysql/lib/Connection.js")}setupOnce(A,B){if(YX9.shouldDisableAutoInstrumentation(B)){xc1.DEBUG_BUILD&&S21.logger.log("Mysql Integration is skipped because of instrumenter configuration.");return}let Q=this.loadDependency();if(!Q){xc1.DEBUG_BUILD&&S21.logger.error("Mysql Integration was unable to require `mysql` package.");return}let D=void 0;try{Q.prototype.connect=new Proxy(Q.prototype.connect,{apply(F,I,Y){if(!D)D=I.config;return F.apply(I,Y)}})}catch(F){xc1.DEBUG_BUILD&&S21.logger.error("Mysql Integration was unable to instrument `mysql` config.")}function Z(){if(!D)return{};return{"server.address":D.host,"server.port":D.port,"db.user":D.user}}function G(F){if(!F)return;let I=Z();Object.keys(I).forEach((Y)=>{F.setAttribute(Y,I[Y])}),F.end()}S21.fill(Q,"createQuery",function(F){return function(I,Y,W){let X=B().getScope().getSpan(),V=IX9([X,"optionalAccess",(K)=>K.startChild,"call",(K)=>K({description:typeof I==="string"?I:I.sql,op:"db",origin:"auto.db.mysql",data:{"db.system":"mysql"}})]);if(typeof W==="function")return F.call(this,I,Y,function(K,H,z){G(V),W(K,H,z)});if(typeof Y==="function")return F.call(this,I,function(K,H,z){G(V),Y(K,H,z)});let C=F.call(this,I,Y);return C.on("end",()=>{G(V)}),C}})}}gJ1.__initStatic();Dg0.Mysql=gJ1});
var Zm0=E((Dm0)=>{var{_nullishCoalesce:Am0}=wA();Object.defineProperty(Dm0,"__esModule",{value:!0});var Bm0=J1("http");J1("https");var FN=Symbol("AgentBaseInternalState");class Qm0 extends Bm0.Agent{constructor(A){super(A);this[FN]={}}isSecureEndpoint(A){if(A){if(typeof A.secureEndpoint==="boolean")return A.secureEndpoint;if(typeof A.protocol==="string")return A.protocol==="https:"}let{stack:B}=new Error;if(typeof B!=="string")return!1;return B.split(`
`).some((Q)=>Q.indexOf("(https.js:")!==-1||Q.indexOf("node:https:")!==-1)}createSocket(A,B,Q){let D={...B,secureEndpoint:this.isSecureEndpoint(B)};Promise.resolve().then(()=>this.connect(A,D)).then((Z)=>{if(Z instanceof Bm0.Agent)return Z.addRequest(A,D);this[FN].currentSocket=Z,super.createSocket(A,B,Q)},Q)}createConnection(){let A=this[FN].currentSocket;if(this[FN].currentSocket=void 0,!A)throw new Error("No socket was returned in the `connect()` function");return A}get defaultPort(){return Am0(this[FN].defaultPort,()=>this.protocol==="https:"?443:80)}set defaultPort(A){if(this[FN])this[FN].defaultPort=A}get protocol(){return Am0(this[FN].protocol,()=>this.isSecureEndpoint()?"https:":"http:")}set protocol(A){if(this[FN])this[FN].protocol=A}}Dm0.Agent=Qm0});
var Zv0=E((Dv0)=>{Object.defineProperty(Dv0,"__esModule",{value:!0});var zQ9=gH(),EQ9=kW1();function UQ9(A,B,Q,D){let Z=A(),G=!1,F=!0;return setInterval(()=>{let I=Z.getTimeMs();if(G===!1&&I>B+Q){if(G=!0,F)D()}if(I<B+Q)G=!1},20),{poll:()=>{Z.reset()},enabled:(I)=>{F=I}}}function wQ9(A,B,Q){let D=B?B.replace(/^file:\/\//,""):void 0,Z=A.location.columnNumber?A.location.columnNumber+1:void 0,G=A.location.lineNumber?A.location.lineNumber+1:void 0;return zQ9.dropUndefinedKeys({filename:D,module:Q(D),function:A.functionName||"?",colno:Z,lineno:G,in_app:D?EQ9.filenameIsInApp(D):void 0})}Dv0.callFrameToStackFrame=wQ9;Dv0.watchdogTimer=UQ9});
var _21=E((Tg0)=>{Object.defineProperty(Tg0,"__esModule",{value:!0});var k21=hC(),vX9=()=>{let A=k21.WINDOW.performance.timing,B=k21.WINDOW.performance.navigation.type,Q={entryType:"navigation",startTime:0,type:B==2?"back_forward":B===1?"reload":"navigate"};for(let D in A)if(D!=="navigationStart"&&D!=="toJSON")Q[D]=Math.max(A[D]-A.navigationStart,0);return Q},bX9=()=>{if(k21.WINDOW.__WEB_VITALS_POLYFILL__)return k21.WINDOW.performance&&(performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]||vX9());else return k21.WINDOW.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]};Tg0.getNavigationEntry=bX9});
var _W1=E((z_0)=>{Object.defineProperty(z_0,"__esModule",{value:!0});var V_0=kW1(),C_0=50,J_0=/\(error: (.*)\)/,X_0=/captureMessage|captureException/;function K_0(...A){let B=A.sort((Q,D)=>Q[0]-D[0]).map((Q)=>Q[1]);return(Q,D=0)=>{let Z=[],G=Q.split(`
`);for(let F=D;F<G.length;F++){let I=G[F];if(I.length>1024)continue;let Y=J_0.test(I)?I.replace(J_0,"$1"):I;if(Y.match(/\S*Error: /))continue;for(let W of B){let J=W(Y);if(J){Z.push(J);break}}if(Z.length>=C_0)break}return H_0(Z)}}function $09(A){if(Array.isArray(A))return K_0(...A);return A}function H_0(A){if(!A.length)return[];let B=Array.from(A);if(/sentryWrapped/.test(B[B.length-1].function||""))B.pop();if(B.reverse(),X_0.test(B[B.length-1].function||"")){if(B.pop(),X_0.test(B[B.length-1].function||""))B.pop()}return B.slice(0,C_0).map((Q)=>({...Q,filename:Q.filename||B[B.length-1].filename,function:Q.function||"?"}))}var mm1="<anonymous>";function q09(A){try{if(!A||typeof A!=="function")return mm1;return A.name||mm1}catch(B){return mm1}}function N09(A){return[90,V_0.node(A)]}z_0.filenameIsInApp=V_0.filenameIsInApp;z_0.createStackParser=K_0;z_0.getFunctionName=q09;z_0.nodeStackLineParser=N09;z_0.stackParserFromStackParserOptions=$09;z_0.stripSentryFramesAndReverse=H_0});
var _c0=E((kc0)=>{Object.defineProperty(kc0,"__esModule",{value:!0});var Oc0=SQ(),u$9=wA(),m$9=i21(),Tc0="Dedupe",d$9=()=>{let A;return{name:Tc0,setupOnce(){},processEvent(B){if(B.type)return B;try{if(Sc0(B,A))return m$9.DEBUG_BUILD&&u$9.logger.warn("Event dropped due to being a duplicate of previously captured event."),null}catch(Q){}return A=B}}},Pc0=Oc0.defineIntegration(d$9),c$9=Oc0.convertIntegrationFnToClass(Tc0,Pc0);function Sc0(A,B){if(!B)return!1;if(l$9(A,B))return!0;if(p$9(A,B))return!0;return!1}function l$9(A,B){let Q=A.message,D=B.message;if(!Q&&!D)return!1;if(Q&&!D||!Q&&D)return!1;if(Q!==D)return!1;if(!yc0(A,B))return!1;if(!jc0(A,B))return!1;return!0}function p$9(A,B){let Q=Mc0(B),D=Mc0(A);if(!Q||!D)return!1;if(Q.type!==D.type||Q.value!==D.value)return!1;if(!yc0(A,B))return!1;if(!jc0(A,B))return!1;return!0}function jc0(A,B){let Q=Rc0(A),D=Rc0(B);if(!Q&&!D)return!0;if(Q&&!D||!Q&&D)return!1;if(Q=Q,D=D,D.length!==Q.length)return!1;for(let Z=0;Z<D.length;Z++){let G=D[Z],F=Q[Z];if(G.filename!==F.filename||G.lineno!==F.lineno||G.colno!==F.colno||G.function!==F.function)return!1}return!0}function yc0(A,B){let Q=A.fingerprint,D=B.fingerprint;if(!Q&&!D)return!0;if(Q&&!D||!Q&&D)return!1;Q=Q,D=D;try{return Q.join("")===D.join("")}catch(Z){return!1}}function Mc0(A){return A.exception&&A.exception.values&&A.exception.values[0]}function Rc0(A){let B=A.exception;if(B)try{return B.values[0].stacktrace.frames}catch(Q){return}return}kc0.Dedupe=c$9;kc0._shouldDropEvent=Sc0;kc0.dedupeIntegration=Pc0});
var _l0=E((kl0)=>{Object.defineProperty(kl0,"__esModule",{value:!0});var Ll0=Ec0(),Ml0=Nc0(),Rl0=_c0(),Ol0=hc0(),lq9=dc0(),Tl0=ac0(),Pl0=Al0(),Sl0=Gl0(),pq9=Yl0(),jl0=zl0(),yl0=Nl0();kl0.CaptureConsole=Ll0.CaptureConsole;kl0.captureConsoleIntegration=Ll0.captureConsoleIntegration;kl0.Debug=Ml0.Debug;kl0.debugIntegration=Ml0.debugIntegration;kl0.Dedupe=Rl0.Dedupe;kl0.dedupeIntegration=Rl0.dedupeIntegration;kl0.ExtraErrorData=Ol0.ExtraErrorData;kl0.extraErrorDataIntegration=Ol0.extraErrorDataIntegration;kl0.Offline=lq9.Offline;kl0.ReportingObserver=Tl0.ReportingObserver;kl0.reportingObserverIntegration=Tl0.reportingObserverIntegration;kl0.RewriteFrames=Pl0.RewriteFrames;kl0.rewriteFramesIntegration=Pl0.rewriteFramesIntegration;kl0.SessionTiming=Sl0.SessionTiming;kl0.sessionTimingIntegration=Sl0.sessionTimingIntegration;kl0.Transaction=pq9.Transaction;kl0.HttpClient=jl0.HttpClient;kl0.httpClientIntegration=jl0.httpClientIntegration;kl0.ContextLines=yl0.ContextLines;kl0.contextLinesIntegration=yl0.contextLinesIntegration});
var aJ1=E((bg0)=>{Object.defineProperty(bg0,"__esModule",{value:!0});var iJ1=hC(),QV9=Nl(),nJ1=-1,DV9=()=>{if(iJ1.WINDOW.document&&iJ1.WINDOW.document.visibilityState)nJ1=iJ1.WINDOW.document.visibilityState==="hidden"&&!iJ1.WINDOW.document.prerendering?0:1/0},ZV9=()=>{QV9.onHidden(({timeStamp:A})=>{nJ1=A},!0)},GV9=()=>{if(nJ1<0)DV9(),ZV9();return{get firstHiddenTime(){return nJ1}}};bg0.getVisibilityWatcher=GV9});
var ac0=E((nc0)=>{Object.defineProperty(nc0,"__esModule",{value:!0});var a21=SQ(),lc0=wA(),Zq9=lc0.GLOBAL_OBJ,pc0="ReportingObserver",cc0=new WeakMap,Gq9=(A={})=>{let B=A.types||["crash","deprecation","intervention"];function Q(D){if(!cc0.has(a21.getClient()))return;for(let Z of D)a21.withScope((G)=>{G.setExtra("url",Z.url);let F=`ReportingObserver [${Z.type}]`,I="No details available";if(Z.body){let Y={};for(let W in Z.body)Y[W]=Z.body[W];if(G.setExtra("body",Y),Z.type==="crash"){let W=Z.body;I=[W.crashId||"",W.reason||""].join(" ").trim()||I}else I=Z.body.message||I}a21.captureMessage(`${F}: ${I}`)})}return{name:pc0,setupOnce(){if(!lc0.supportsReportingObserver())return;new Zq9.ReportingObserver(Q,{buffered:!0,types:B}).observe()},setup(D){cc0.set(D,!0)}}},ic0=a21.defineIntegration(Gq9),Fq9=a21.convertIntegrationFnToClass(pc0,ic0);nc0.ReportingObserver=Fq9;nc0.reportingObserverIntegration=ic0});
var bx0=E((vx0)=>{Object.defineProperty(vx0,"__esModule",{value:!0});var _x0=Nd1(),uH=D21(),xx0=new RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function Ld1(A){if(!A)return;let B=A.match(xx0);if(!B)return;let Q;if(B[3]==="1")Q=!0;else if(B[3]==="0")Q=!1;return{traceId:B[1],parentSampled:Q,parentSpanId:B[2]}}function K99(A,B){let Q=Ld1(A),D=_x0.baggageHeaderToDynamicSamplingContext(B),{traceId:Z,parentSpanId:G,parentSampled:F}=Q||{};if(!Q)return{traceparentData:Q,dynamicSamplingContext:void 0,propagationContext:{traceId:Z||uH.uuid4(),spanId:uH.uuid4().substring(16)}};else return{traceparentData:Q,dynamicSamplingContext:D||{},propagationContext:{traceId:Z||uH.uuid4(),parentSpanId:G||uH.uuid4().substring(16),spanId:uH.uuid4().substring(16),sampled:F,dsc:D||{}}}}function H99(A,B){let Q=Ld1(A),D=_x0.baggageHeaderToDynamicSamplingContext(B),{traceId:Z,parentSpanId:G,parentSampled:F}=Q||{};if(!Q)return{traceId:Z||uH.uuid4(),spanId:uH.uuid4().substring(16)};else return{traceId:Z||uH.uuid4(),parentSpanId:G||uH.uuid4().substring(16),spanId:uH.uuid4().substring(16),sampled:F,dsc:D||{}}}function z99(A=uH.uuid4(),B=uH.uuid4().substring(16),Q){let D="";if(Q!==void 0)D=Q?"-1":"-0";return`${A}-${B}${D}`}vx0.TRACEPARENT_REGEXP=xx0;vx0.extractTraceparentData=Ld1;vx0.generateSentryTraceHeader=z99;vx0.propagationContextFromHeaders=H99;vx0.tracingContextFromHeaders=K99});
var cb0=E((db0)=>{Object.defineProperty(db0,"__esModule",{value:!0});var mb0=wA(),NG9=R21();function LG9(A,B,Q,D){let Z={sent_at:new Date().toISOString()};if(Q&&Q.sdk)Z.sdk={name:Q.sdk.name,version:Q.sdk.version};if(!!D&&B)Z.dsn=mb0.dsnToString(B);let G=MG9(A);return mb0.createEnvelope(Z,[G])}function MG9(A){let B=NG9.serializeMetricBuckets(A);return[{type:"statsd",length:B.length},B]}db0.createMetricEnvelope=LG9});
var cf0=E((df0)=>{Object.defineProperty(df0,"__esModule",{value:!0});var GI9=wA(),gf0=IO(),hf0=ff0(),uf0="ModuleMetadata",FI9=()=>{return{name:uf0,setupOnce(){},setup(A){if(typeof A.on!=="function")return;A.on("beforeEnvelope",(B)=>{GI9.forEachEnvelopeItem(B,(Q,D)=>{if(D==="event"){let Z=Array.isArray(Q)?Q[1]:void 0;if(Z)hf0.stripMetadataFromStackFrames(Z),Q[1]=Z}})})},processEvent(A,B,Q){let D=Q.getOptions().stackParser;return hf0.addMetadataToStackFrames(D,A),A}}},mf0=gf0.defineIntegration(FI9),II9=gf0.convertIntegrationFnToClass(uf0,mf0);df0.ModuleMetadata=II9;df0.moduleMetadataIntegration=mf0});
var cu0=E((du0,m21)=>{Object.defineProperty(du0,"__esModule",{value:!0});var mu0=SQ(),Rl=wA();function SK9(){let A=mu0.getMainCarrier();if(!A.__SENTRY__)return;let B={mongodb(){return new(Rl.dynamicRequire(m21,"./node/integrations/mongo")).Mongo},mongoose(){return new(Rl.dynamicRequire(m21,"./node/integrations/mongo")).Mongo},mysql(){return new(Rl.dynamicRequire(m21,"./node/integrations/mysql")).Mysql},pg(){return new(Rl.dynamicRequire(m21,"./node/integrations/postgres")).Postgres}},Q=Object.keys(B).filter((D)=>!!Rl.loadModule(D)).map((D)=>{try{return B[D]()}catch(Z){return}}).filter((D)=>D);if(Q.length>0)A.__SENTRY__.integrations=[...A.__SENTRY__.integrations||[],...Q]}function jK9(){if(mu0.addTracingExtensions(),Rl.isNodeEnv())SK9()}du0.addExtensionMethods=jK9});
var dc0=E((mc0)=>{Object.defineProperty(mc0,"__esModule",{value:!0});var WN=wA(),Qq9=uc0(),Pf=i21(),nj=WN.GLOBAL_OBJ;class n21{static __initStatic(){this.id="Offline"}constructor(A={}){this.name=n21.id,this.maxStoredEvents=A.maxStoredEvents||30,this.offlineEventStore=Qq9.createInstance({name:"sentry/offlineEventStore"})}setupOnce(A,B){if(this.hub=B(),"addEventListener"in nj)nj.addEventListener("online",()=>{this._sendEvents().catch(()=>{Pf.DEBUG_BUILD&&WN.logger.warn("could not send cached events")})});let Q=(D)=>{if(this.hub&&this.hub.getIntegration(n21)){if("navigator"in nj&&"onLine"in nj.navigator&&!nj.navigator.onLine)return Pf.DEBUG_BUILD&&WN.logger.log("Event dropped due to being a offline - caching instead"),this._cacheEvent(D).then((Z)=>this._enforceMaxEvents()).catch((Z)=>{Pf.DEBUG_BUILD&&WN.logger.warn("could not cache event while offline")}),null}return D};if(Q.id=this.name,A(Q),"navigator"in nj&&"onLine"in nj.navigator&&nj.navigator.onLine)this._sendEvents().catch(()=>{Pf.DEBUG_BUILD&&WN.logger.warn("could not send cached events")})}async _cacheEvent(A){return this.offlineEventStore.setItem(WN.uuid4(),WN.normalize(A))}async _enforceMaxEvents(){let A=[];return this.offlineEventStore.iterate((B,Q,D)=>{A.push({cacheKey:Q,event:B})}).then(()=>this._purgeEvents(A.sort((B,Q)=>(Q.event.timestamp||0)-(B.event.timestamp||0)).slice(this.maxStoredEvents<A.length?this.maxStoredEvents:A.length).map((B)=>B.cacheKey))).catch((B)=>{Pf.DEBUG_BUILD&&WN.logger.warn("could not enforce max events")})}async _purgeEvent(A){return this.offlineEventStore.removeItem(A)}async _purgeEvents(A){return Promise.all(A.map((B)=>this._purgeEvent(B))).then()}async _sendEvents(){return this.offlineEventStore.iterate((A,B,Q)=>{if(this.hub)this.hub.captureEvent(A),this._purgeEvent(B).catch((D)=>{Pf.DEBUG_BUILD&&WN.logger.warn("could not purge event from cache")});else Pf.DEBUG_BUILD&&WN.logger.warn("no hub found - could not send cached event")})}}n21.__initStatic();mc0.Offline=n21});
var dl0=E((ml0)=>{var{_optionalChain:ul0}=wA();Object.defineProperty(ml0,"__esModule",{value:!0});var EN9=SQ(),UN9=TX1();function wN9(A){return new Proxy(A,{get(B,Q){if(Q==="schedule"&&B.schedule)return new Proxy(B.schedule,{apply(D,Z,G){let[F,,I]=G;if(!ul0([I,"optionalAccess",(Y)=>Y.name]))throw new Error('Missing "name" for scheduled job. A name is required for Sentry check-in monitoring.');return EN9.withMonitor(I.name,()=>{return D.apply(Z,G)},{schedule:{type:"crontab",value:UN9.replaceCronNames(F)},timezone:ul0([I,"optionalAccess",(Y)=>Y.timezone])})}});else return B[Q]}})}ml0.instrumentNodeCron=wN9});
var eJ1=E((Nu0)=>{Object.defineProperty(Nu0,"__esModule",{value:!0});var eU=SQ(),Aw=wA(),aC9=nc1(),sC9=Ml(),rC9=hC(),tJ1=["localhost",/^\/(?!\/)/],ac1={traceFetch:!0,traceXHR:!0,enableHTTPTimings:!0,tracingOrigins:tJ1,tracePropagationTargets:tJ1};function oC9(A){let{traceFetch:B,traceXHR:Q,tracePropagationTargets:D,tracingOrigins:Z,shouldCreateSpanForRequest:G,enableHTTPTimings:F}={traceFetch:ac1.traceFetch,traceXHR:ac1.traceXHR,...A},I=typeof G==="function"?G:(J)=>!0,Y=(J)=>wu0(J,D||Z),W={};if(B)Aw.addFetchInstrumentationHandler((J)=>{let X=aC9.instrumentFetchRequest(J,I,Y,W);if(X){let V=qu0(J.fetchData.url),C=V?Aw.parseUrl(V).host:void 0;X.setAttributes({"http.url":V,"server.address":C})}if(F&&X)Eu0(X)});if(Q)Aw.addXhrInstrumentationHandler((J)=>{let X=$u0(J,I,Y,W);if(F&&X)Eu0(X)})}function tC9(A){return A.entryType==="resource"&&"initiatorType"in A&&typeof A.nextHopProtocol==="string"&&(A.initiatorType==="fetch"||A.initiatorType==="xmlhttprequest")}function Eu0(A){let{url:B}=eU.spanToJSON(A).data||{};if(!B||typeof B!=="string")return;let Q=sC9.addPerformanceInstrumentationHandler("resource",({entries:D})=>{D.forEach((Z)=>{if(tC9(Z)&&Z.name.endsWith(B))eC9(Z).forEach((F)=>A.setAttribute(...F)),setTimeout(Q)})})}function Uu0(A){let B="unknown",Q="unknown",D="";for(let Z of A){if(Z==="/"){[B,Q]=A.split("/");break}if(!isNaN(Number(Z))){B=D==="h"?"http":D,Q=A.split(D)[1];break}D+=Z}if(D===A)B=D;return{name:B,version:Q}}function GN(A=0){return((Aw.browserPerformanceTimeOrigin||performance.timeOrigin)+A)/1000}function eC9(A){let{name:B,version:Q}=Uu0(A.nextHopProtocol),D=[];if(D.push(["network.protocol.version",Q],["network.protocol.name",B]),!Aw.browserPerformanceTimeOrigin)return D;return[...D,["http.request.redirect_start",GN(A.redirectStart)],["http.request.fetch_start",GN(A.fetchStart)],["http.request.domain_lookup_start",GN(A.domainLookupStart)],["http.request.domain_lookup_end",GN(A.domainLookupEnd)],["http.request.connect_start",GN(A.connectStart)],["http.request.secure_connection_start",GN(A.secureConnectionStart)],["http.request.connection_end",GN(A.connectEnd)],["http.request.request_start",GN(A.requestStart)],["http.request.response_start",GN(A.responseStart)],["http.request.response_end",GN(A.responseEnd)]]}function wu0(A,B){return Aw.stringMatchesSomePattern(A,B||tJ1)}function $u0(A,B,Q,D){let Z=A.xhr,G=Z&&Z[Aw.SENTRY_XHR_DATA_KEY];if(!eU.hasTracingEnabled()||!Z||Z.__sentry_own_request__||!G)return;let F=B(G.url);if(A.endTimestamp&&F){let C=Z.__sentry_xhr_span_id__;if(!C)return;let K=D[C];if(K&&G.status_code!==void 0)eU.setHttpStatus(K,G.status_code),K.end(),delete D[C];return}let I=eU.getCurrentScope(),Y=eU.getIsolationScope(),W=qu0(G.url),J=W?Aw.parseUrl(W).host:void 0,X=F?eU.startInactiveSpan({name:`${G.method} ${G.url}`,onlyIfParent:!0,attributes:{type:"xhr","http.method":G.method,"http.url":W,url:G.url,"server.address":J,[eU.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]:"auto.http.browser"},op:"http.client"}):void 0;if(X)Z.__sentry_xhr_span_id__=X.spanContext().spanId,D[Z.__sentry_xhr_span_id__]=X;let V=eU.getClient();if(Z.setRequestHeader&&Q(G.url)&&V){let{traceId:C,spanId:K,sampled:H,dsc:z}={...Y.getPropagationContext(),...I.getPropagationContext()},$=X?eU.spanToTraceHeader(X):Aw.generateSentryTraceHeader(C,K,H),L=Aw.dynamicSamplingContextToSentryBaggageHeader(z||(X?eU.getDynamicSamplingContextFromSpan(X):eU.getDynamicSamplingContextFromClient(C,V,I)));AK9(Z,$,L)}return X}function AK9(A,B,Q){try{if(A.setRequestHeader("sentry-trace",B),Q)A.setRequestHeader(Aw.BAGGAGE_HEADER_NAME,Q)}catch(D){}}function qu0(A){try{return new URL(A,rC9.WINDOW.location.origin).href}catch(B){return}}Nu0.DEFAULT_TRACE_PROPAGATION_TARGETS=tJ1;Nu0.defaultRequestInstrumentationOptions=ac1;Nu0.extractNetworkProtocol=Uu0;Nu0.instrumentOutgoingRequests=oC9;Nu0.shouldAttachHeaders=wu0;Nu0.xhrCallback=$u0});
var ec1=E((eu0)=>{Object.defineProperty(eu0,"__esModule",{value:!0});var MH9=J1("os"),RH9=J1("util"),ou0=SQ();class tu0 extends ou0.ServerRuntimeClient{constructor(A){ou0.applySdkMetadata(A,"node"),A.transportOptions={textEncoder:new RH9.TextEncoder,...A.transportOptions};let B={...A,platform:"node",runtime:{name:"node",version:global.process.version},serverName:A.serverName||global.process.env.SENTRY_NAME||MH9.hostname()};super(B)}}eu0.NodeClient=tu0});
var ed0=E((td0)=>{Object.defineProperty(td0,"__esModule",{value:!0});var od0=wA();function Nw9(A,B){return od0.extractRequestData(A,{include:B})}function Lw9(A,B,Q={}){return od0.addRequestDataToEvent(A,B,{include:Q})}td0.extractRequestData=Nw9;td0.parseRequest=Lw9});
var ed1=E((Fb0)=>{Object.defineProperty(Fb0,"__esModule",{value:!0});var ZD9=wA();function GD9(A,B,Q=()=>{}){let D;try{D=A()}catch(Z){throw B(Z),Q(),Z}return FD9(D,B,Q)}function FD9(A,B,Q){if(ZD9.isThenable(A))return A.then((D)=>{return Q(),D},(D)=>{throw B(D),Q(),D});return Q(),A}Fb0.handleCallbackErrors=GD9});
var em0=E((tm0)=>{Object.defineProperty(tm0,"__esModule",{value:!0});function EE9(A,B,Q){let D=0,Z=5,G=0;return setInterval(()=>{if(G===0){if(D>A){if(Z*=2,Q(Z),Z>86400)Z=86400;G=Z}}else if(G-=1,G===0)B();D=0},1000).unref(),()=>{D+=1}}function Yl1(A){return A!==void 0&&(A.length===0||A==="?"||A==="<anonymous>")}function UE9(A,B){return A===B||Yl1(A)&&Yl1(B)}function om0(A){if(A===void 0)return;return A.slice(-10).reduce((B,Q)=>`${B},${Q.function},${Q.lineno},${Q.colno}`,"")}function wE9(A,B){if(B===void 0)return;return om0(A(B,1))}tm0.createRateLimiter=EE9;tm0.functionNamesMatch=UE9;tm0.hashFrames=om0;tm0.hashFromStack=wE9;tm0.isAnonymous=Yl1});
var eq=E((Ab0)=>{Object.defineProperty(Ab0,"__esModule",{value:!0});var mJ=wA(),R79=Gl(),id1=kG(),rv0=DJ1(),nd1=Fl(),O79=FJ1(),IJ1=parseFloat(O79.SDK_VERSION),T79=100;class w21{constructor(A,B,Q,D=IJ1){this._version=D;let Z;if(!B)Z=new rv0.Scope,Z.setClient(A);else Z=B;let G;if(!Q)G=new rv0.Scope,G.setClient(A);else G=Q;if(this._stack=[{scope:Z}],A)this.bindClient(A);this._isolationScope=G}isOlderThan(A){return this._version<A}bindClient(A){let B=this.getStackTop();if(B.client=A,B.scope.setClient(A),A&&A.setupIntegrations)A.setupIntegrations()}pushScope(){let A=this.getScope().clone();return this.getStack().push({client:this.getClient(),scope:A}),A}popScope(){if(this.getStack().length<=1)return!1;return!!this.getStack().pop()}withScope(A){let B=this.pushScope(),Q;try{Q=A(B)}catch(D){throw this.popScope(),D}if(mJ.isThenable(Q))return Q.then((D)=>{return this.popScope(),D},(D)=>{throw this.popScope(),D});return this.popScope(),Q}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStack(){return this._stack}getStackTop(){return this._stack[this._stack.length-1]}captureException(A,B){let Q=this._lastEventId=B&&B.event_id?B.event_id:mJ.uuid4(),D=new Error("Sentry syntheticException");return this.getScope().captureException(A,{originalException:A,syntheticException:D,...B,event_id:Q}),Q}captureMessage(A,B,Q){let D=this._lastEventId=Q&&Q.event_id?Q.event_id:mJ.uuid4(),Z=new Error(A);return this.getScope().captureMessage(A,B,{originalException:A,syntheticException:Z,...Q,event_id:D}),D}captureEvent(A,B){let Q=B&&B.event_id?B.event_id:mJ.uuid4();if(!A.type)this._lastEventId=Q;return this.getScope().captureEvent(A,{...B,event_id:Q}),Q}lastEventId(){return this._lastEventId}addBreadcrumb(A,B){let{scope:Q,client:D}=this.getStackTop();if(!D)return;let{beforeBreadcrumb:Z=null,maxBreadcrumbs:G=T79}=D.getOptions&&D.getOptions()||{};if(G<=0)return;let I={timestamp:mJ.dateTimestampInSeconds(),...A},Y=Z?mJ.consoleSandbox(()=>Z(I,B)):I;if(Y===null)return;if(D.emit)D.emit("beforeAddBreadcrumb",Y,B);Q.addBreadcrumb(Y,G)}setUser(A){this.getScope().setUser(A),this.getIsolationScope().setUser(A)}setTags(A){this.getScope().setTags(A),this.getIsolationScope().setTags(A)}setExtras(A){this.getScope().setExtras(A),this.getIsolationScope().setExtras(A)}setTag(A,B){this.getScope().setTag(A,B),this.getIsolationScope().setTag(A,B)}setExtra(A,B){this.getScope().setExtra(A,B),this.getIsolationScope().setExtra(A,B)}setContext(A,B){this.getScope().setContext(A,B),this.getIsolationScope().setContext(A,B)}configureScope(A){let{scope:B,client:Q}=this.getStackTop();if(Q)A(B)}run(A){let B=ad1(this);try{A(this)}finally{ad1(B)}}getIntegration(A){let B=this.getClient();if(!B)return null;try{return B.getIntegration(A)}catch(Q){return id1.DEBUG_BUILD&&mJ.logger.warn(`Cannot retrieve integration ${A.id} from the current Hub`),null}}startTransaction(A,B){let Q=this._callExtensionMethod("startTransaction",A,B);if(id1.DEBUG_BUILD&&!Q)if(!this.getClient())mJ.logger.warn("Tracing extension 'startTransaction' is missing. You should 'init' the SDK before calling 'startTransaction'");else mJ.logger.warn(`Tracing extension 'startTransaction' has not been added. Call 'addTracingExtensions' before calling 'init':
Sentry.addTracingExtensions();
Sentry.init({...});
`);return Q}traceHeaders(){return this._callExtensionMethod("traceHeaders")}captureSession(A=!1){if(A)return this.endSession();this._sendSessionUpdate()}endSession(){let B=this.getStackTop().scope,Q=B.getSession();if(Q)nd1.closeSession(Q);this._sendSessionUpdate(),B.setSession()}startSession(A){let{scope:B,client:Q}=this.getStackTop(),{release:D,environment:Z=R79.DEFAULT_ENVIRONMENT}=Q&&Q.getOptions()||{},{userAgent:G}=mJ.GLOBAL_OBJ.navigator||{},F=nd1.makeSession({release:D,environment:Z,user:B.getUser(),...G&&{userAgent:G},...A}),I=B.getSession&&B.getSession();if(I&&I.status==="ok")nd1.updateSession(I,{status:"exited"});return this.endSession(),B.setSession(F),F}shouldSendDefaultPii(){let A=this.getClient(),B=A&&A.getOptions();return Boolean(B&&B.sendDefaultPii)}_sendSessionUpdate(){let{scope:A,client:B}=this.getStackTop(),Q=A.getSession();if(Q&&B&&B.captureSession)B.captureSession(Q)}_callExtensionMethod(A,...B){let D=Wf().__SENTRY__;if(D&&D.extensions&&typeof D.extensions[A]==="function")return D.extensions[A].apply(this,B);id1.DEBUG_BUILD&&mJ.logger.warn(`Extension method ${A} couldn't be found, doing nothing.`)}}function Wf(){return mJ.GLOBAL_OBJ.__SENTRY__=mJ.GLOBAL_OBJ.__SENTRY__||{extensions:{},hub:void 0},mJ.GLOBAL_OBJ}function ad1(A){let B=Wf(),Q=U21(B);return YJ1(B,A),Q}function ov0(){let A=Wf();if(A.__SENTRY__&&A.__SENTRY__.acs){let B=A.__SENTRY__.acs.getCurrentHub();if(B)return B}return tv0(A)}function P79(){return ov0().getIsolationScope()}function tv0(A=Wf()){if(!ev0(A)||U21(A).isOlderThan(IJ1))YJ1(A,new w21);return U21(A)}function S79(A,B=tv0()){if(!ev0(A)||U21(A).isOlderThan(IJ1)){let Q=B.getClient(),D=B.getScope(),Z=B.getIsolationScope();YJ1(A,new w21(Q,D.clone(),Z.clone()))}}function j79(A){let B=Wf();B.__SENTRY__=B.__SENTRY__||{},B.__SENTRY__.acs=A}function y79(A,B={}){let Q=Wf();if(Q.__SENTRY__&&Q.__SENTRY__.acs)return Q.__SENTRY__.acs.runWithAsyncContext(A,B);return A()}function ev0(A){return!!(A&&A.__SENTRY__&&A.__SENTRY__.hub)}function U21(A){return mJ.getGlobalSingleton("hub",()=>new w21,A)}function YJ1(A,B){if(!A)return!1;let Q=A.__SENTRY__=A.__SENTRY__||{};return Q.hub=B,!0}Ab0.API_VERSION=IJ1;Ab0.Hub=w21;Ab0.ensureHubOnCarrier=S79;Ab0.getCurrentHub=ov0;Ab0.getHubFromCarrier=U21;Ab0.getIsolationScope=P79;Ab0.getMainCarrier=Wf;Ab0.makeMain=ad1;Ab0.runWithAsyncContext=y79;Ab0.setAsyncContextStrategy=j79;Ab0.setHubOnCarrier=YJ1});
var ff0=E((bf0)=>{Object.defineProperty(bf0,"__esModule",{value:!0});var Mc1=wA(),xf0=new Map,_f0=new Set;function eF9(A){if(!Mc1.GLOBAL_OBJ._sentryModuleMetadata)return;for(let B of Object.keys(Mc1.GLOBAL_OBJ._sentryModuleMetadata)){let Q=Mc1.GLOBAL_OBJ._sentryModuleMetadata[B];if(_f0.has(B))continue;_f0.add(B);let D=A(B);for(let Z of D.reverse())if(Z.filename){xf0.set(Z.filename,Q);break}}}function vf0(A,B){return eF9(A),xf0.get(B)}function AI9(A,B){try{B.exception.values.forEach((Q)=>{if(!Q.stacktrace)return;for(let D of Q.stacktrace.frames||[]){if(!D.filename)continue;let Z=vf0(A,D.filename);if(Z)D.module_metadata=Z}})}catch(Q){}}function BI9(A){try{A.exception.values.forEach((B)=>{if(!B.stacktrace)return;for(let Q of B.stacktrace.frames||[])delete Q.module_metadata})}catch(B){}}bf0.addMetadataToStackFrames=AI9;bf0.getMetadataForUrl=vf0;bf0.stripMetadataFromStackFrames=BI9});
var gH=E((I_0)=>{Object.defineProperty(I_0,"__esModule",{value:!0});var t19=xm1(),e19=rq(),Bl=hH(),A09=oU(),B_0=B21();function B09(A,B,Q){if(!(B in A))return;let D=A[B],Z=Q(D);if(typeof Z==="function")G_0(Z,D);A[B]=Z}function Z_0(A,B,Q){try{Object.defineProperty(A,B,{value:Q,writable:!0,configurable:!0})}catch(D){e19.DEBUG_BUILD&&A09.logger.log(`Failed to add non-enumerable property "${B}" to object`,A)}}function G_0(A,B){try{let Q=B.prototype||{};A.prototype=B.prototype=Q,Z_0(A,"__sentry_original__",B)}catch(Q){}}function Q09(A){return A.__sentry_original__}function D09(A){return Object.keys(A).map((B)=>`${encodeURIComponent(B)}=${encodeURIComponent(A[B])}`).join("&")}function F_0(A){if(Bl.isError(A))return{message:A.message,name:A.name,stack:A.stack,...D_0(A)};else if(Bl.isEvent(A)){let B={type:A.type,target:Q_0(A.target),currentTarget:Q_0(A.currentTarget),...D_0(A)};if(typeof CustomEvent!=="undefined"&&Bl.isInstanceOf(A,CustomEvent))B.detail=A.detail;return B}else return A}function Q_0(A){try{return Bl.isElement(A)?t19.htmlTreeAsString(A):Object.prototype.toString.call(A)}catch(B){return"<unknown>"}}function D_0(A){if(typeof A==="object"&&A!==null){let B={};for(let Q in A)if(Object.prototype.hasOwnProperty.call(A,Q))B[Q]=A[Q];return B}else return{}}function Z09(A,B=40){let Q=Object.keys(F_0(A));if(Q.sort(),!Q.length)return"[object has no keys]";if(Q[0].length>=B)return B_0.truncate(Q[0],B);for(let D=Q.length;D>0;D--){let Z=Q.slice(0,D).join(", ");if(Z.length>B)continue;if(D===Q.length)return Z;return B_0.truncate(Z,B)}return""}function G09(A){return um1(A,new Map)}function um1(A,B){if(F09(A)){let Q=B.get(A);if(Q!==void 0)return Q;let D={};B.set(A,D);for(let Z of Object.keys(A))if(typeof A[Z]!=="undefined")D[Z]=um1(A[Z],B);return D}if(Array.isArray(A)){let Q=B.get(A);if(Q!==void 0)return Q;let D=[];return B.set(A,D),A.forEach((Z)=>{D.push(um1(Z,B))}),D}return A}function F09(A){if(!Bl.isPlainObject(A))return!1;try{let B=Object.getPrototypeOf(A).constructor.name;return!B||B==="Object"}catch(B){return!0}}function I09(A){let B;switch(!0){case(A===void 0||A===null):B=new String(A);break;case(typeof A==="symbol"||typeof A==="bigint"):B=Object(A);break;case Bl.isPrimitive(A):B=new A.constructor(A);break;default:B=A;break}return B}I_0.addNonEnumerableProperty=Z_0;I_0.convertToPlainObject=F_0;I_0.dropUndefinedKeys=G09;I_0.extractExceptionKeysForMessage=Z09;I_0.fill=B09;I_0.getOriginalFunction=Q09;I_0.markFunctionWrapped=G_0;I_0.objectify=I09;I_0.urlEncode=D09});
var gd0=E((hd0)=>{Object.defineProperty(hd0,"__esModule",{value:!0});var qX1=J1("fs"),zl1=J1("path");function tU9(A){let B=zl1.resolve(A);if(!qX1.existsSync(B))throw new Error(`Cannot read contents of ${B}. Directory does not exist.`);if(!qX1.statSync(B).isDirectory())throw new Error(`Cannot read contents of ${B}, because it is not a directory.`);let Q=(D)=>{return qX1.readdirSync(D).reduce((Z,G)=>{let F=zl1.join(D,G);if(qX1.statSync(F).isDirectory())return Z.concat(Q(F));return Z.push(F),Z},[])};return Q(B).map((D)=>zl1.relative(B,D))}hd0.deepReadDirSync=tU9});
var gl0=E((hl0)=>{Object.defineProperty(hl0,"__esModule",{value:!0});var vl0=SQ(),bl0=TX1(),fl0="Automatic instrumentation of CronJob only supports crontab string";function HN9(A,B){let Q=!1;return new Proxy(A,{construct(D,Z){let[G,F,I,Y,W,...J]=Z;if(typeof G!=="string")throw new Error(fl0);if(Q)throw new Error(`A job named '${B}' has already been scheduled`);Q=!0;let X=bl0.replaceCronNames(G);function V(C,K){return vl0.withMonitor(B,()=>{return F(C,K)},{schedule:{type:"crontab",value:X},timezone:W||void 0})}return new D(G,V,I,Y,W,...J)},get(D,Z){if(Z==="from")return(G)=>{let{cronTime:F,onTick:I,timeZone:Y}=G;if(typeof F!=="string")throw new Error(fl0);if(Q)throw new Error(`A job named '${B}' has already been scheduled`);Q=!0;let W=bl0.replaceCronNames(F);return G.onTick=(J,X)=>{return vl0.withMonitor(B,()=>{return I(J,X)},{schedule:{type:"crontab",value:W},timezone:Y||void 0})},D.from(G)};else return D[Z]}})}hl0.instrumentCron=HN9});
var gm1=E((A_0)=>{Object.defineProperty(A_0,"__esModule",{value:!0});class ek0 extends Error{constructor(A,B="warn"){super(A);this.message=A,this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype),this.logLevel=B}}A_0.SentryError=ek0});
var hC=E((wg0)=>{Object.defineProperty(wg0,"__esModule",{value:!0});var OX9=wA(),TX9=OX9.GLOBAL_OBJ;wg0.WINDOW=TX9});
var hH=E((hk0)=>{Object.defineProperty(hk0,"__esModule",{value:!0});var vk0=Object.prototype.toString;function TeB(A){switch(vk0.call(A)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return SW1(A,Error)}}function ec(A,B){return vk0.call(A)===`[object ${B}]`}function PeB(A){return ec(A,"ErrorEvent")}function SeB(A){return ec(A,"DOMError")}function jeB(A){return ec(A,"DOMException")}function yeB(A){return ec(A,"String")}function bk0(A){return typeof A==="object"&&A!==null&&"__sentry_template_string__"in A&&"__sentry_template_values__"in A}function keB(A){return A===null||bk0(A)||typeof A!=="object"&&typeof A!=="function"}function fk0(A){return ec(A,"Object")}function _eB(A){return typeof Event!=="undefined"&&SW1(A,Event)}function xeB(A){return typeof Element!=="undefined"&&SW1(A,Element)}function veB(A){return ec(A,"RegExp")}function beB(A){return Boolean(A&&A.then&&typeof A.then==="function")}function feB(A){return fk0(A)&&"nativeEvent"in A&&"preventDefault"in A&&"stopPropagation"in A}function heB(A){return typeof A==="number"&&A!==A}function SW1(A,B){try{return A instanceof B}catch(Q){return!1}}function geB(A){return!!(typeof A==="object"&&A!==null&&(A.__isVue||A._isVue))}hk0.isDOMError=SeB;hk0.isDOMException=jeB;hk0.isElement=xeB;hk0.isError=TeB;hk0.isErrorEvent=PeB;hk0.isEvent=_eB;hk0.isInstanceOf=SW1;hk0.isNaN=heB;hk0.isParameterizedString=bk0;hk0.isPlainObject=fk0;hk0.isPrimitive=keB;hk0.isRegExp=veB;hk0.isString=yeB;hk0.isSyntheticEvent=feB;hk0.isThenable=beB;hk0.isVueViewModel=geB});
var hc0=E((fc0)=>{Object.defineProperty(fc0,"__esModule",{value:!0});var xc0=SQ(),ij=wA(),s$9=i21(),vc0="ExtraErrorData",r$9=(A={})=>{let B=A.depth||3,Q=A.captureErrorCause||!1;return{name:vc0,setupOnce(){},processEvent(D,Z){return t$9(D,Z,B,Q)}}},bc0=xc0.defineIntegration(r$9),o$9=xc0.convertIntegrationFnToClass(vc0,bc0);function t$9(A,B={},Q,D){if(!B.originalException||!ij.isError(B.originalException))return A;let Z=B.originalException.name||B.originalException.constructor.name,G=e$9(B.originalException,D);if(G){let F={...A.contexts},I=ij.normalize(G,Q);if(ij.isPlainObject(I))ij.addNonEnumerableProperty(I,"__sentry_skip_normalization__",!0),F[Z]=I;return{...A,contexts:F}}return A}function e$9(A,B){try{let Q=["name","message","stack","line","column","fileName","lineNumber","columnNumber","toJSON"],D={};for(let Z of Object.keys(A)){if(Q.indexOf(Z)!==-1)continue;let G=A[Z];D[Z]=ij.isError(G)?G.toString():G}if(B&&A.cause!==void 0)D.cause=ij.isError(A.cause)?A.cause.toString():A.cause;if(typeof A.toJSON==="function"){let Z=A.toJSON();for(let G of Object.keys(Z)){let F=Z[G];D[G]=ij.isError(F)?F.toString():F}}return D}catch(Q){s$9.DEBUG_BUILD&&ij.logger.error("Unable to extract extra data from the Error object:",Q)}return null}fc0.ExtraErrorData=o$9;fc0.extraErrorDataIntegration=bc0});
var hc1=E((Lg0)=>{Object.defineProperty(Lg0,"__esModule",{value:!0});var $g0=SQ(),qg0=wA(),Ng0=ZV(),fc1=hC();function SX9(){if(fc1.WINDOW.document)fc1.WINDOW.document.addEventListener("visibilitychange",()=>{let A=$g0.getActiveTransaction();if(fc1.WINDOW.document.hidden&&A){let{op:Q,status:D}=$g0.spanToJSON(A);if(Ng0.DEBUG_BUILD&&qg0.logger.log(`[Tracing] Transaction: cancelled -> since tab moved to the background, op: ${Q}`),!D)A.setStatus("cancelled");A.setTag("visibilitychange","document.hidden"),A.end()}});else Ng0.DEBUG_BUILD&&qg0.logger.warn("[Tracing] Could not set up background tab detection due to lack of global document")}Lg0.registerBackgroundTabDetection=SX9});
var hg0=E((fg0)=>{Object.defineProperty(fg0,"__esModule",{value:!0});var IV9=$l(),YV9=aJ1(),WV9=ql(),JV9=Hf(),XV9=Nl(),VV9=(A)=>{let B=YV9.getVisibilityWatcher(),Q=WV9.initMetric("FID"),D,Z=(I)=>{if(I.startTime<B.firstHiddenTime)Q.value=I.processingStart-I.startTime,Q.entries.push(I),D(!0)},G=(I)=>{I.forEach(Z)},F=JV9.observe("first-input",G);if(D=IV9.bindReporter(A,Q),F)XV9.onHidden(()=>{G(F.takeRecords()),F.disconnect()},!0)};fg0.onFID=VV9});
var hm1=E((tk0)=>{Object.defineProperty(tk0,"__esModule",{value:!0});var d19=rq(),Q21=oU(),c19=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function l19(A){return A==="http"||A==="https"}function p19(A,B=!1){let{host:Q,path:D,pass:Z,port:G,projectId:F,protocol:I,publicKey:Y}=A;return`${I}://${Y}${B&&Z?`:${Z}`:""}@${Q}${G?`:${G}`:""}/${D?`${D}/`:D}${F}`}function rk0(A){let B=c19.exec(A);if(!B){Q21.consoleSandbox(()=>{console.error(`Invalid Sentry Dsn: ${A}`)});return}let[Q,D,Z="",G,F="",I]=B.slice(1),Y="",W=I,J=W.split("/");if(J.length>1)Y=J.slice(0,-1).join("/"),W=J.pop();if(W){let X=W.match(/^\d+/);if(X)W=X[0]}return ok0({host:G,pass:Z,path:Y,projectId:W,port:F,protocol:Q,publicKey:D})}function ok0(A){return{protocol:A.protocol,publicKey:A.publicKey||"",pass:A.pass||"",host:A.host,port:A.port||"",path:A.path||"",projectId:A.projectId}}function i19(A){if(!d19.DEBUG_BUILD)return!0;let{port:B,projectId:Q,protocol:D}=A;if(["protocol","publicKey","host","projectId"].find((F)=>{if(!A[F])return Q21.logger.error(`Invalid Sentry Dsn: ${F} missing`),!0;return!1}))return!1;if(!Q.match(/^\d+$/))return Q21.logger.error(`Invalid Sentry Dsn: Invalid projectId ${Q}`),!1;if(!l19(D))return Q21.logger.error(`Invalid Sentry Dsn: Invalid protocol ${D}`),!1;if(B&&isNaN(parseInt(B,10)))return Q21.logger.error(`Invalid Sentry Dsn: Invalid port ${B}`),!1;return!0}function n19(A){let B=typeof A==="string"?rk0(A):ok0(A);if(!B||!i19(B))return;return B}tk0.dsnFromString=rk0;tk0.dsnToString=p19;tk0.makeDsn=n19});
var i21=E((Lc0)=>{Object.defineProperty(Lc0,"__esModule",{value:!0});var h$9=typeof __SENTRY_DEBUG__==="undefined"||__SENTRY_DEBUG__;Lc0.DEBUG_BUILD=h$9});
var ic1=E((Ku0)=>{Object.defineProperty(Ku0,"__esModule",{value:!0});var JO=SQ(),P3=wA(),gC=ZV(),zf=Ml(),XO=hC(),EC9=aJ1(),VO=Wu0(),UC9=_21(),wC9=2147483647;function RF(A){return A/1000}function pc1(){return XO.WINDOW&&XO.WINDOW.addEventListener&&XO.WINDOW.performance}var Ju0=0,GZ={},DN,b21;function $C9(){let A=pc1();if(A&&P3.browserPerformanceTimeOrigin){if(A.mark)XO.WINDOW.performance.mark("sentry-tracing-init");let B=OC9(),Q=MC9(),D=RC9(),Z=TC9();return()=>{B(),Q(),D(),Z()}}return()=>{return}}function qC9(){zf.addPerformanceInstrumentationHandler("longtask",({entries:A})=>{for(let B of A){let Q=JO.getActiveTransaction();if(!Q)return;let D=RF(P3.browserPerformanceTimeOrigin+B.startTime),Z=RF(B.duration);Q.startChild({description:"Main UI thread blocked",op:"ui.long-task",origin:"auto.ui.browser.metrics",startTimestamp:D,endTimestamp:D+Z})}})}function NC9(){zf.addPerformanceInstrumentationHandler("event",({entries:A})=>{for(let B of A){let Q=JO.getActiveTransaction();if(!Q)return;if(B.name==="click"){let D=RF(P3.browserPerformanceTimeOrigin+B.startTime),Z=RF(B.duration),G={description:P3.htmlTreeAsString(B.target),op:`ui.interaction.${B.name}`,origin:"auto.ui.browser.metrics",startTimestamp:D,endTimestamp:D+Z},F=P3.getComponentName(B.target);if(F)G.attributes={"ui.component_name":F};Q.startChild(G)}}})}function LC9(A,B){if(pc1()&&P3.browserPerformanceTimeOrigin){let D=PC9(A,B);return()=>{D()}}return()=>{return}}function MC9(){return zf.addClsInstrumentationHandler(({metric:A})=>{let B=A.entries[A.entries.length-1];if(!B)return;gC.DEBUG_BUILD&&P3.logger.log("[Measurements] Adding CLS"),GZ.cls={value:A.value,unit:""},b21=B},!0)}function RC9(){return zf.addLcpInstrumentationHandler(({metric:A})=>{let B=A.entries[A.entries.length-1];if(!B)return;gC.DEBUG_BUILD&&P3.logger.log("[Measurements] Adding LCP"),GZ.lcp={value:A.value,unit:"millisecond"},DN=B},!0)}function OC9(){return zf.addFidInstrumentationHandler(({metric:A})=>{let B=A.entries[A.entries.length-1];if(!B)return;let Q=RF(P3.browserPerformanceTimeOrigin),D=RF(B.startTime);gC.DEBUG_BUILD&&P3.logger.log("[Measurements] Adding FID"),GZ.fid={value:A.value,unit:"millisecond"},GZ["mark.fid"]={value:Q+D,unit:"second"}})}function TC9(){return zf.addTtfbInstrumentationHandler(({metric:A})=>{if(!A.entries[A.entries.length-1])return;gC.DEBUG_BUILD&&P3.logger.log("[Measurements] Adding TTFB"),GZ.ttfb={value:A.value,unit:"millisecond"}})}var Xu0={click:"click",pointerdown:"click",pointerup:"click",mousedown:"click",mouseup:"click",touchstart:"click",touchend:"click",mouseover:"hover",mouseout:"hover",mouseenter:"hover",mouseleave:"hover",pointerover:"hover",pointerout:"hover",pointerenter:"hover",pointerleave:"hover",dragstart:"drag",dragend:"drag",drag:"drag",dragenter:"drag",dragleave:"drag",dragover:"drag",drop:"drag",keydown:"press",keyup:"press",keypress:"press",input:"press"};function PC9(A,B){return zf.addInpInstrumentationHandler(({metric:Q})=>{if(Q.value===void 0)return;let D=Q.entries.find((N)=>N.duration===Q.value&&Xu0[N.name]!==void 0),Z=JO.getClient();if(!D||!Z)return;let G=Xu0[D.name],F=Z.getOptions(),I=RF(P3.browserPerformanceTimeOrigin+D.startTime),Y=RF(Q.value),W=D.interactionId!==void 0?A[D.interactionId]:void 0;if(W===void 0)return;let{routeName:J,parentContext:X,activeTransaction:V,user:C,replayId:K}=W,H=C!==void 0?C.email||C.id||C.ip_address:void 0,z=V!==void 0?V.getProfileId():void 0,$=new JO.Span({startTimestamp:I,endTimestamp:I+Y,op:`ui.interaction.${G}`,name:P3.htmlTreeAsString(D.target),attributes:{release:F.release,environment:F.environment,transaction:J,...H!==void 0&&H!==""?{user:H}:{},...z!==void 0?{profile_id:z}:{},...K!==void 0?{replay_id:K}:{}},exclusiveTime:Q.value,measurements:{inp:{value:Q.value,unit:"millisecond"}}}),L=vC9(X,F,B);if(!L)return;if(Math.random()<L){let N=$?JO.createSpanEnvelope([$],Z.getDsn()):void 0,O=Z&&Z.getTransport();if(O&&N)O.send(N).then(null,(R)=>{gC.DEBUG_BUILD&&P3.logger.error("Error while sending interaction:",R)});return}})}function SC9(A){let B=pc1();if(!B||!XO.WINDOW.performance.getEntries||!P3.browserPerformanceTimeOrigin)return;gC.DEBUG_BUILD&&P3.logger.log("[Tracing] Adding & adjusting spans using Performance API");let Q=RF(P3.browserPerformanceTimeOrigin),D=B.getEntries(),{op:Z,start_timestamp:G}=JO.spanToJSON(A);if(D.slice(Ju0).forEach((F)=>{let I=RF(F.startTime),Y=RF(F.duration);if(A.op==="navigation"&&G&&Q+I<G)return;switch(F.entryType){case"navigation":{jC9(A,F,Q);break}case"mark":case"paint":case"measure":{Vu0(A,F,I,Y,Q);let W=EC9.getVisibilityWatcher(),J=F.startTime<W.firstHiddenTime;if(F.name==="first-paint"&&J)gC.DEBUG_BUILD&&P3.logger.log("[Measurements] Adding FP"),GZ.fp={value:F.startTime,unit:"millisecond"};if(F.name==="first-contentful-paint"&&J)gC.DEBUG_BUILD&&P3.logger.log("[Measurements] Adding FCP"),GZ.fcp={value:F.startTime,unit:"millisecond"};break}case"resource":{Cu0(A,F,F.name,I,Y,Q);break}}}),Ju0=Math.max(D.length-1,0),kC9(A),Z==="pageload"){xC9(GZ),["fcp","fp","lcp"].forEach((I)=>{if(!GZ[I]||!G||Q>=G)return;let Y=GZ[I].value,W=Q+RF(Y),J=Math.abs((W-G)*1000),X=J-Y;gC.DEBUG_BUILD&&P3.logger.log(`[Measurements] Normalized ${I} from ${Y} to ${J} (${X})`),GZ[I].value=J});let F=GZ["mark.fid"];if(F&&GZ.fid)VO._startChild(A,{description:"first input delay",endTimestamp:F.value+RF(GZ.fid.value),op:"ui.action",origin:"auto.ui.browser.metrics",startTimestamp:F.value}),delete GZ["mark.fid"];if(!("fcp"in GZ))delete GZ.cls;Object.keys(GZ).forEach((I)=>{JO.setMeasurement(I,GZ[I].value,GZ[I].unit)}),_C9(A)}DN=void 0,b21=void 0,GZ={}}function Vu0(A,B,Q,D,Z){let G=Z+Q,F=G+D;return VO._startChild(A,{description:B.name,endTimestamp:F,op:B.entryType,origin:"auto.resource.browser.metrics",startTimestamp:G}),G}function jC9(A,B,Q){["unloadEvent","redirect","domContentLoadedEvent","loadEvent","connect"].forEach((D)=>{oJ1(A,B,D,Q)}),oJ1(A,B,"secureConnection",Q,"TLS/SSL","connectEnd"),oJ1(A,B,"fetch",Q,"cache","domainLookupStart"),oJ1(A,B,"domainLookup",Q,"DNS"),yC9(A,B,Q)}function oJ1(A,B,Q,D,Z,G){let F=G?B[G]:B[`${Q}End`],I=B[`${Q}Start`];if(!I||!F)return;VO._startChild(A,{op:"browser",origin:"auto.browser.browser.metrics",description:Z||Q,startTimestamp:D+RF(I),endTimestamp:D+RF(F)})}function yC9(A,B,Q){if(B.responseEnd)VO._startChild(A,{op:"browser",origin:"auto.browser.browser.metrics",description:"request",startTimestamp:Q+RF(B.requestStart),endTimestamp:Q+RF(B.responseEnd)}),VO._startChild(A,{op:"browser",origin:"auto.browser.browser.metrics",description:"response",startTimestamp:Q+RF(B.responseStart),endTimestamp:Q+RF(B.responseEnd)})}function Cu0(A,B,Q,D,Z,G){if(B.initiatorType==="xmlhttprequest"||B.initiatorType==="fetch")return;let F=P3.parseUrl(Q),I={};if(lc1(I,B,"transferSize","http.response_transfer_size"),lc1(I,B,"encodedBodySize","http.response_content_length"),lc1(I,B,"decodedBodySize","http.decoded_response_content_length"),"renderBlockingStatus"in B)I["resource.render_blocking_status"]=B.renderBlockingStatus;if(F.protocol)I["url.scheme"]=F.protocol.split(":").pop();if(F.host)I["server.address"]=F.host;I["url.same_origin"]=Q.includes(XO.WINDOW.location.origin);let Y=G+D,W=Y+Z;VO._startChild(A,{description:Q.replace(XO.WINDOW.location.origin,""),endTimestamp:W,op:B.initiatorType?`resource.${B.initiatorType}`:"resource.other",origin:"auto.resource.browser.metrics",startTimestamp:Y,data:I})}function kC9(A){let B=XO.WINDOW.navigator;if(!B)return;let Q=B.connection;if(Q){if(Q.effectiveType)A.setTag("effectiveConnectionType",Q.effectiveType);if(Q.type)A.setTag("connectionType",Q.type);if(VO.isMeasurementValue(Q.rtt))GZ["connection.rtt"]={value:Q.rtt,unit:"millisecond"}}if(VO.isMeasurementValue(B.deviceMemory))A.setTag("deviceMemory",`${B.deviceMemory} GB`);if(VO.isMeasurementValue(B.hardwareConcurrency))A.setTag("hardwareConcurrency",String(B.hardwareConcurrency))}function _C9(A){if(DN){if(gC.DEBUG_BUILD&&P3.logger.log("[Measurements] Adding LCP Data"),DN.element)A.setTag("lcp.element",P3.htmlTreeAsString(DN.element));if(DN.id)A.setTag("lcp.id",DN.id);if(DN.url)A.setTag("lcp.url",DN.url.trim().slice(0,200));A.setTag("lcp.size",DN.size)}if(b21&&b21.sources)gC.DEBUG_BUILD&&P3.logger.log("[Measurements] Adding CLS Data"),b21.sources.forEach((B,Q)=>A.setTag(`cls.source.${Q+1}`,P3.htmlTreeAsString(B.node)))}function lc1(A,B,Q,D){let Z=B[Q];if(Z!=null&&Z<wC9)A[D]=Z}function xC9(A){let B=UC9.getNavigationEntry();if(!B)return;let{responseStart:Q,requestStart:D}=B;if(D<=Q)gC.DEBUG_BUILD&&P3.logger.log("[Measurements] Adding TTFB Request Time"),A["ttfb.requestTime"]={value:Q-D,unit:"millisecond"}}function vC9(A,B,Q){if(!JO.hasTracingEnabled(B))return!1;let D;if(A!==void 0&&typeof B.tracesSampler==="function")D=B.tracesSampler({transactionContext:A,name:A.name,parentSampled:A.parentSampled,attributes:{...A.data,...A.attributes},location:XO.WINDOW.location});else if(A!==void 0&&A.sampled!==void 0)D=A.sampled;else if(typeof B.tracesSampleRate!=="undefined")D=B.tracesSampleRate;else D=1;if(!JO.isValidSampleRate(D))return gC.DEBUG_BUILD&&P3.logger.warn("[Tracing] Discarding interaction span because of invalid sample rate."),!1;if(D===!0)return Q;else if(D===!1)return 0;return D*Q}Ku0._addMeasureSpans=Vu0;Ku0._addResourceSpans=Cu0;Ku0.addPerformanceEntries=SC9;Ku0.startTrackingINP=LC9;Ku0.startTrackingInteractions=NC9;Ku0.startTrackingLongTasks=qC9;Ku0.startTrackingWebVitals=$C9});
var im0=E((pm0)=>{var{_optionalChain:YN}=wA();Object.defineProperty(pm0,"__esModule",{value:!0});var Gl1=J1("url"),oz9=wf();function tz9(A){let{protocol:B,hostname:Q,port:D}=lm0(A),Z=A.path?A.path:"/";return`${B}//${Q}${D}${Z}`}function cm0(A){let{protocol:B,hostname:Q,port:D}=lm0(A),Z=A.pathname||"/",G=A.auth?ez9(A.auth):"";return`${B}//${G}${Q}${D}${Z}`}function ez9(A){let[B,Q]=A.split(":");return`${B?"[Filtered]":""}:${Q?"[Filtered]":""}@`}function AE9(A,B,Q){if(!A)return A;let[D,Z]=A.split(" ");if(B.host&&!B.protocol)B.protocol=YN([Q,"optionalAccess",(G)=>G.agent,"optionalAccess",(G)=>G.protocol]),Z=cm0(B);if(YN([Z,"optionalAccess",(G)=>G.startsWith,"call",(G)=>G("///")]))Z=Z.slice(2);return`${D} ${Z}`}function Fl1(A){let B={protocol:A.protocol,hostname:typeof A.hostname==="string"&&A.hostname.startsWith("[")?A.hostname.slice(1,-1):A.hostname,hash:A.hash,search:A.search,pathname:A.pathname,path:`${A.pathname||""}${A.search||""}`,href:A.href};if(A.port!=="")B.port=Number(A.port);if(A.username||A.password)B.auth=`${A.username}:${A.password}`;return B}function BE9(A,B){let Q,D;if(typeof B[B.length-1]==="function")Q=B.pop();if(typeof B[0]==="string")D=Fl1(new Gl1.URL(B[0]));else if(B[0]instanceof Gl1.URL)D=Fl1(B[0]);else{D=B[0];try{let Z=new Gl1.URL(D.path||"",`${D.protocol||"http:"}//${D.hostname}`);D={pathname:Z.pathname,search:Z.search,hash:Z.hash,...D}}catch(Z){}}if(B.length===2)D={...D,...B[1]};if(D.protocol===void 0)if(oz9.NODE_VERSION.major>8)D.protocol=YN([YN([A,"optionalAccess",(Z)=>Z.globalAgent]),"optionalAccess",(Z)=>Z.protocol])||YN([D.agent,"optionalAccess",(Z)=>Z.protocol])||YN([D._defaultAgent,"optionalAccess",(Z)=>Z.protocol]);else D.protocol=YN([D.agent,"optionalAccess",(Z)=>Z.protocol])||YN([D._defaultAgent,"optionalAccess",(Z)=>Z.protocol])||YN([YN([A,"optionalAccess",(Z)=>Z.globalAgent]),"optionalAccess",(Z)=>Z.protocol]);if(Q)return[D,Q];else return[D]}function lm0(A){let B=A.protocol||"",Q=A.hostname||A.host||"",D=!A.port||A.port===80||A.port===443||/^(.*):(\d+)$/.test(Q)?"":`:${A.port}`;return{protocol:B,hostname:Q,port:D}}pm0.cleanSpanDescription=AE9;pm0.extractRawUrl=tz9;pm0.extractUrl=cm0;pm0.normalizeRequestArgs=BE9;pm0.urlToOptions=Fl1});
var ix0=E((px0)=>{Object.defineProperty(px0,"__esModule",{value:!0});var dx0=60000;function cx0(A,B=Date.now()){let Q=parseInt(`${A}`,10);if(!isNaN(Q))return Q*1000;let D=Date.parse(`${A}`);if(!isNaN(D))return D-B;return dx0}function lx0(A,B){return A[B]||A.all||0}function s99(A,B,Q=Date.now()){return lx0(A,B)>Q}function r99(A,{statusCode:B,headers:Q},D=Date.now()){let Z={...A},G=Q&&Q["x-sentry-rate-limits"],F=Q&&Q["retry-after"];if(G)for(let I of G.trim().split(",")){let[Y,W,,,J]=I.split(":",5),X=parseInt(Y,10),V=(!isNaN(X)?X:60)*1000;if(!W)Z.all=D+V;else for(let C of W.split(";"))if(C==="metric_bucket"){if(!J||J.split(";").includes("custom"))Z[C]=D+V}else Z[C]=D+V}else if(F)Z.all=D+cx0(F,D);else if(B===429)Z.all=D+60000;return Z}px0.DEFAULT_RETRY_AFTER=dx0;px0.disabledUntil=lx0;px0.isRateLimited=s99;px0.parseRetryAfterHeader=cx0;px0.updateRateLimits=r99});
var jd1=E((Kv0)=>{Object.defineProperty(Kv0,"__esModule",{value:!0});function _Q9(A){let B=void 0,Q=A[0],D=1;while(D<A.length){let Z=A[D],G=A[D+1];if(D+=2,(Z==="optionalAccess"||Z==="optionalCall")&&Q==null)return;if(Z==="access"||Z==="optionalAccess")B=Q,Q=G(Q);else if(Z==="call"||Z==="optionalCall")Q=G((...F)=>Q.call(B,...F)),B=void 0}return Q}Kv0._optionalChain=_Q9});
var kG=E((Nv0)=>{Object.defineProperty(Nv0,"__esModule",{value:!0});var V59=typeof __SENTRY_DEBUG__==="undefined"||__SENTRY_DEBUG__;Nv0.DEBUG_BUILD=V59});
var kW=E((pk0)=>{Object.defineProperty(pk0,"__esModule",{value:!0});function yW1(A){return A&&A.Math==Math?A:void 0}var _m1=typeof globalThis=="object"&&yW1(globalThis)||typeof window=="object"&&yW1(window)||typeof self=="object"&&yW1(self)||typeof global=="object"&&yW1(global)||function(){return this}()||{};function H19(){return _m1}function z19(A,B,Q){let D=Q||_m1,Z=D.__SENTRY__=D.__SENTRY__||{};return Z[A]||(Z[A]=B())}pk0.GLOBAL_OBJ=_m1;pk0.getGlobalObject=H19;pk0.getGlobalSingleton=z19});
var kW1=E((W_0)=>{Object.defineProperty(W_0,"__esModule",{value:!0});function Y_0(A,B=!1){return!(B||A&&!A.startsWith("/")&&!A.match(/^[A-Z]:/)&&!A.startsWith(".")&&!A.match(/^[a-zA-Z]([a-zA-Z0-9.\-+])*:\/\//))&&A!==void 0&&!A.includes("node_modules/")}function E09(A){let B=/^\s*[-]{4,}$/,Q=/at (?:async )?(?:(.+?)\s+\()?(?:(.+):(\d+):(\d+)?|([^)]+))\)?/;return(D)=>{let Z=D.match(Q);if(Z){let G,F,I,Y,W;if(Z[1]){I=Z[1];let V=I.lastIndexOf(".");if(I[V-1]===".")V--;if(V>0){G=I.slice(0,V),F=I.slice(V+1);let C=G.indexOf(".Module");if(C>0)I=I.slice(C+1),G=G.slice(0,C)}Y=void 0}if(F)Y=G,W=F;if(F==="<anonymous>")W=void 0,I=void 0;if(I===void 0)W=W||"<anonymous>",I=Y?`${Y}.${W}`:W;let J=Z[2]&&Z[2].startsWith("file://")?Z[2].slice(7):Z[2],X=Z[5]==="native";if(J&&J.match(/\/[A-Z]:/))J=J.slice(1);if(!J&&Z[5]&&!X)J=Z[5];return{filename:J,module:A?A(J):void 0,function:I,lineno:parseInt(Z[3],10)||void 0,colno:parseInt(Z[4],10)||void 0,in_app:Y_0(J,X)}}if(D.match(B))return{filename:D};return}}W_0.filenameIsInApp=Y_0;W_0.node=E09});
var kb0=E((yb0)=>{Object.defineProperty(yb0,"__esModule",{value:!0});var NZ9=WJ1();function LZ9(A,B,Q){let D=NZ9.getActiveTransaction();if(D)D.setMeasurement(A,B,Q)}yb0.setMeasurement=LZ9});
var kf0=E((yf0)=>{Object.defineProperty(yf0,"__esModule",{value:!0});var jf0=FJ1();function oF9(A,B,Q=[B],D="npm"){let Z=A._metadata||{};if(!Z.sdk)Z.sdk={name:`sentry.javascript.${B}`,packages:Q.map((G)=>({name:`${D}:@sentry/${G}`,version:jf0.SDK_VERSION})),version:jf0.SDK_VERSION};A._metadata=Z}yf0.applySdkMetadata=oF9});
var ku0=E((yu0)=>{Object.defineProperty(yu0,"__esModule",{value:!0});var Bw=SQ(),CO=wA(),lj=ZV(),WK9=hc1(),Ou0=Ml(),g21=ic1(),Pu0=eJ1(),JK9=Ru0(),Uf=hC(),Su0="BrowserTracing",XK9={...Bw.TRACING_DEFAULTS,markBackgroundTransactions:!0,routingInstrumentation:JK9.instrumentRoutingWithDefaults,startTransactionOnLocationChange:!0,startTransactionOnPageLoad:!0,enableLongTask:!0,enableInp:!1,interactionsSampleRate:1,_experiments:{},...Pu0.defaultRequestInstrumentationOptions},Tu0=10;class ju0{constructor(A){if(this.name=Su0,this._hasSetTracePropagationTargets=!1,Bw.addTracingExtensions(),lj.DEBUG_BUILD)this._hasSetTracePropagationTargets=!!(A&&(A.tracePropagationTargets||A.tracingOrigins));if(this.options={...XK9,...A},this.options._experiments.enableLongTask!==void 0)this.options.enableLongTask=this.options._experiments.enableLongTask;if(A&&!A.tracePropagationTargets&&A.tracingOrigins)this.options.tracePropagationTargets=A.tracingOrigins;if(this._collectWebVitals=g21.startTrackingWebVitals(),this._interactionIdToRouteNameMapping={},this.options.enableInp)g21.startTrackingINP(this._interactionIdToRouteNameMapping,this.options.interactionsSampleRate);if(this.options.enableLongTask)g21.startTrackingLongTasks();if(this.options._experiments.enableInteractions)g21.startTrackingInteractions();this._latestRoute={name:void 0,context:void 0}}setupOnce(A,B){this._getCurrentHub=B;let D=B().getClient(),Z=D&&D.getOptions(),{routingInstrumentation:G,startTransactionOnLocationChange:F,startTransactionOnPageLoad:I,markBackgroundTransactions:Y,traceFetch:W,traceXHR:J,shouldCreateSpanForRequest:X,enableHTTPTimings:V,_experiments:C}=this.options,K=Z&&Z.tracePropagationTargets,H=K||this.options.tracePropagationTargets;if(lj.DEBUG_BUILD&&this._hasSetTracePropagationTargets&&K)CO.logger.warn("[Tracing] The `tracePropagationTargets` option was set in the BrowserTracing integration and top level `Sentry.init`. The top level `Sentry.init` value is being used.");if(G((z)=>{let $=this._createRouteTransaction(z);return this.options._experiments.onStartRouteTransaction&&this.options._experiments.onStartRouteTransaction($,z,B),$},I,F),Y)WK9.registerBackgroundTabDetection();if(C.enableInteractions)this._registerInteractionListener();if(this.options.enableInp)this._registerInpInteractionListener();Pu0.instrumentOutgoingRequests({traceFetch:W,traceXHR:J,tracePropagationTargets:H,shouldCreateSpanForRequest:X,enableHTTPTimings:V})}_createRouteTransaction(A){if(!this._getCurrentHub){lj.DEBUG_BUILD&&CO.logger.warn(`[Tracing] Did not create ${A.op} transaction because _getCurrentHub is invalid.`);return}let B=this._getCurrentHub(),{beforeNavigate:Q,idleTimeout:D,finalTimeout:Z,heartbeatInterval:G}=this.options,F=A.op==="pageload",I;if(F){let V=F?sc1("sentry-trace"):"",C=F?sc1("baggage"):void 0,{traceId:K,dsc:H,parentSpanId:z,sampled:$}=CO.propagationContextFromHeaders(V,C);I={traceId:K,parentSpanId:z,parentSampled:$,...A,metadata:{...A.metadata,dynamicSamplingContext:H},trimEnd:!0}}else I={trimEnd:!0,...A};let Y=typeof Q==="function"?Q(I):I,W=Y===void 0?{...I,sampled:!1}:Y;if(W.metadata=W.name!==I.name?{...W.metadata,source:"custom"}:W.metadata,this._latestRoute.name=W.name,this._latestRoute.context=W,W.sampled===!1)lj.DEBUG_BUILD&&CO.logger.log(`[Tracing] Will not send ${W.op} transaction because of beforeNavigate.`);lj.DEBUG_BUILD&&CO.logger.log(`[Tracing] Starting ${W.op} transaction on scope`);let{location:J}=Uf.WINDOW,X=Bw.startIdleTransaction(B,W,D,Z,!0,{location:J},G,F);if(F){if(Uf.WINDOW.document){if(Uf.WINDOW.document.addEventListener("readystatechange",()=>{if(["interactive","complete"].includes(Uf.WINDOW.document.readyState))X.sendAutoFinishSignal()}),["interactive","complete"].includes(Uf.WINDOW.document.readyState))X.sendAutoFinishSignal()}}return X.registerBeforeFinishCallback((V)=>{this._collectWebVitals(),g21.addPerformanceEntries(V)}),X}_registerInteractionListener(){let A,B=()=>{let{idleTimeout:Q,finalTimeout:D,heartbeatInterval:Z}=this.options,G="ui.action.click",F=Bw.getActiveTransaction();if(F&&F.op&&["navigation","pageload"].includes(F.op)){lj.DEBUG_BUILD&&CO.logger.warn("[Tracing] Did not create ui.action.click transaction because a pageload or navigation transaction is in progress.");return}if(A)A.setFinishReason("interactionInterrupted"),A.end(),A=void 0;if(!this._getCurrentHub){lj.DEBUG_BUILD&&CO.logger.warn("[Tracing] Did not create ui.action.click transaction because _getCurrentHub is invalid.");return}if(!this._latestRoute.name){lj.DEBUG_BUILD&&CO.logger.warn("[Tracing] Did not create ui.action.click transaction because _latestRouteName is missing.");return}let I=this._getCurrentHub(),{location:Y}=Uf.WINDOW,W={name:this._latestRoute.name,op:"ui.action.click",trimEnd:!0,data:{[Bw.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]:this._latestRoute.context?VK9(this._latestRoute.context):"url"}};A=Bw.startIdleTransaction(I,W,Q,D,!0,{location:Y},Z)};["click"].forEach((Q)=>{if(Uf.WINDOW.document)addEventListener(Q,B,{once:!1,capture:!0})})}_registerInpInteractionListener(){let A=({entries:B})=>{let Q=Bw.getClient(),D=Q!==void 0&&Q.getIntegrationByName!==void 0?Q.getIntegrationByName("Replay"):void 0,Z=D!==void 0?D.getReplayId():void 0,G=Bw.getActiveTransaction(),F=Bw.getCurrentScope(),I=F!==void 0?F.getUser():void 0;B.forEach((Y)=>{if(CK9(Y)){let W=Y.interactionId;if(W===void 0)return;let J=this._interactionIdToRouteNameMapping[W],X=Y.duration,V=Y.startTime,C=Object.keys(this._interactionIdToRouteNameMapping),K=C.length>0?C.reduce((H,z)=>{return this._interactionIdToRouteNameMapping[H].duration<this._interactionIdToRouteNameMapping[z].duration?H:z}):void 0;if(Y.entryType==="first-input"){if(C.map((z)=>this._interactionIdToRouteNameMapping[z]).some((z)=>{return z.duration===X&&z.startTime===V}))return}if(!W)return;if(J)J.duration=Math.max(J.duration,X);else if(C.length<Tu0||K===void 0||X>this._interactionIdToRouteNameMapping[K].duration){let H=this._latestRoute.name,z=this._latestRoute.context;if(H&&z){if(K&&Object.keys(this._interactionIdToRouteNameMapping).length>=Tu0)delete this._interactionIdToRouteNameMapping[K];this._interactionIdToRouteNameMapping[W]={routeName:H,duration:X,parentContext:z,user:I,activeTransaction:G,replayId:Z,startTime:V}}}}})};Ou0.addPerformanceInstrumentationHandler("event",A),Ou0.addPerformanceInstrumentationHandler("first-input",A)}}function sc1(A){let B=CO.getDomElement(`meta[name=${A}]`);return B?B.getAttribute("content"):void 0}function VK9(A){let B=A.attributes&&A.attributes[Bw.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE],Q=A.data&&A.data[Bw.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE],D=A.metadata&&A.metadata.source;return B||Q||D}function CK9(A){return"duration"in A}yu0.BROWSER_TRACING_INTEGRATION_ID=Su0;yu0.BrowserTracing=ju0;yu0.getMetaContent=sc1});
var l21=E((dm0)=>{Object.defineProperty(dm0,"__esModule",{value:!0});var sz9=typeof __SENTRY_DEBUG__==="undefined"||__SENTRY_DEBUG__;dm0.DEBUG_BUILD=sz9});
var lk0=E((ck0)=>{Object.defineProperty(ck0,"__esModule",{value:!0});var ym1=hH(),X19=B21();function V19(A,B,Q=250,D,Z,G,F){if(!G.exception||!G.exception.values||!F||!ym1.isInstanceOf(F.originalException,Error))return;let I=G.exception.values.length>0?G.exception.values[G.exception.values.length-1]:void 0;if(I)G.exception.values=C19(km1(A,B,Z,F.originalException,D,G.exception.values,I,0),Q)}function km1(A,B,Q,D,Z,G,F,I){if(G.length>=Q+1)return G;let Y=[...G];if(ym1.isInstanceOf(D[Z],Error)){mk0(F,I);let W=A(B,D[Z]),J=Y.length;dk0(W,Z,J,I),Y=km1(A,B,Q,D[Z],Z,[W,...Y],W,J)}if(Array.isArray(D.errors))D.errors.forEach((W,J)=>{if(ym1.isInstanceOf(W,Error)){mk0(F,I);let X=A(B,W),V=Y.length;dk0(X,`errors[${J}]`,V,I),Y=km1(A,B,Q,W,Z,[X,...Y],X,V)}});return Y}function mk0(A,B){A.mechanism=A.mechanism||{type:"generic",handled:!0},A.mechanism={...A.mechanism,...A.type==="AggregateError"&&{is_exception_group:!0},exception_id:B}}function dk0(A,B,Q,D){A.mechanism=A.mechanism||{type:"generic",handled:!0},A.mechanism={...A.mechanism,type:"chained",source:B,exception_id:Q,parent_id:D}}function C19(A,B){return A.map((Q)=>{if(Q.value)Q.value=X19.truncate(Q.value,B);return Q})}ck0.applyAggregateErrorsToEvent=V19});
var ll0=E((cl0)=>{Object.defineProperty(cl0,"__esModule",{value:!0});var qN9=SQ(),NN9=TX1();function LN9(A){return new Proxy(A,{get(B,Q){if(Q==="scheduleJob")return new Proxy(B.scheduleJob,{apply(D,Z,G){let[F,I]=G;if(typeof F!=="string"||typeof I!=="string")throw new Error("Automatic instrumentation of 'node-schedule' requires the first parameter of 'scheduleJob' to be a job name string and the second parameter to be a crontab string");let Y=F,W=I;return qN9.withMonitor(Y,()=>{return D.apply(Z,G)},{schedule:{type:"crontab",value:NN9.replaceCronNames(W)}})}});return B[Q]}})}cl0.instrumentNodeSchedule=LN9});
var lm1=E((w_0)=>{Object.defineProperty(w_0,"__esModule",{value:!0});var dm1=oU(),u09=gH(),xW1=kW(),cm1=QO();function m09(A){cm1.addHandler("console",A),cm1.maybeInstrument("console",d09)}function d09(){if(!("console"in xW1.GLOBAL_OBJ))return;dm1.CONSOLE_LEVELS.forEach(function(A){if(!(A in xW1.GLOBAL_OBJ.console))return;u09.fill(xW1.GLOBAL_OBJ.console,A,function(B){return dm1.originalConsoleMethods[A]=B,function(...Q){let D={args:Q,level:A};cm1.triggerHandlers("console",D);let Z=dm1.originalConsoleMethods[A];Z&&Z.apply(xW1.GLOBAL_OBJ.console,Q)}})})}w_0.addConsoleInstrumentationHandler=m09});
var mH=E((gv0)=>{Object.defineProperty(gv0,"__esModule",{value:!0});var GO=wA(),A39=Gl(),ZJ1=kG(),dZ=eq(),dd1=Fl(),B39=BJ1();function Q39(A,B){return dZ.getCurrentHub().captureException(A,B39.parseEventHintOrCaptureContext(B))}function D39(A,B){let Q=typeof B==="string"?B:void 0,D=typeof B!=="string"?{captureContext:B}:void 0;return dZ.getCurrentHub().captureMessage(A,Q,D)}function Z39(A,B){return dZ.getCurrentHub().captureEvent(A,B)}function G39(A){dZ.getCurrentHub().configureScope(A)}function F39(A,B){dZ.getCurrentHub().addBreadcrumb(A,B)}function I39(A,B){dZ.getCurrentHub().setContext(A,B)}function Y39(A){dZ.getCurrentHub().setExtras(A)}function W39(A,B){dZ.getCurrentHub().setExtra(A,B)}function J39(A){dZ.getCurrentHub().setTags(A)}function X39(A,B){dZ.getCurrentHub().setTag(A,B)}function V39(A){dZ.getCurrentHub().setUser(A)}function fv0(...A){let B=dZ.getCurrentHub();if(A.length===2){let[Q,D]=A;if(!Q)return B.withScope(D);return B.withScope(()=>{return B.getStackTop().scope=Q,D(Q)})}return B.withScope(A[0])}function C39(A){return dZ.runWithAsyncContext(()=>{return A(dZ.getIsolationScope())})}function K39(A,B){return fv0((Q)=>{return Q.setSpan(A),B(Q)})}function H39(A,B){return dZ.getCurrentHub().startTransaction({...A},B)}function cd1(A,B){let Q=z21(),D=If();if(!D)ZJ1.DEBUG_BUILD&&GO.logger.warn("Cannot capture check-in. No client defined.");else if(!D.captureCheckIn)ZJ1.DEBUG_BUILD&&GO.logger.warn("Cannot capture check-in. Client does not support sending check-ins.");else return D.captureCheckIn(A,B,Q);return GO.uuid4()}function z39(A,B,Q){let D=cd1({monitorSlug:A,status:"in_progress"},Q),Z=GO.timestampInSeconds();function G(I){cd1({monitorSlug:A,status:I,checkInId:D,duration:GO.timestampInSeconds()-Z})}let F;try{F=B()}catch(I){throw G("error"),I}if(GO.isThenable(F))Promise.resolve(F).then(()=>{G("ok")},()=>{G("error")});else G("ok");return F}async function E39(A){let B=If();if(B)return B.flush(A);return ZJ1.DEBUG_BUILD&&GO.logger.warn("Cannot flush events. No client defined."),Promise.resolve(!1)}async function U39(A){let B=If();if(B)return B.close(A);return ZJ1.DEBUG_BUILD&&GO.logger.warn("Cannot flush events and disable SDK. No client defined."),Promise.resolve(!1)}function w39(){return dZ.getCurrentHub().lastEventId()}function If(){return dZ.getCurrentHub().getClient()}function $39(){return!!If()}function z21(){return dZ.getCurrentHub().getScope()}function q39(A){let B=If(),Q=dZ.getIsolationScope(),D=z21(),{release:Z,environment:G=A39.DEFAULT_ENVIRONMENT}=B&&B.getOptions()||{},{userAgent:F}=GO.GLOBAL_OBJ.navigator||{},I=dd1.makeSession({release:Z,environment:G,user:D.getUser()||Q.getUser(),...F&&{userAgent:F},...A}),Y=Q.getSession();if(Y&&Y.status==="ok")dd1.updateSession(Y,{status:"exited"});return ld1(),Q.setSession(I),D.setSession(I),I}function ld1(){let A=dZ.getIsolationScope(),B=z21(),Q=B.getSession()||A.getSession();if(Q)dd1.closeSession(Q);hv0(),A.setSession(),B.setSession()}function hv0(){let A=dZ.getIsolationScope(),B=z21(),Q=If(),D=B.getSession()||A.getSession();if(D&&Q&&Q.captureSession)Q.captureSession(D)}function N39(A=!1){if(A){ld1();return}hv0()}gv0.addBreadcrumb=F39;gv0.captureCheckIn=cd1;gv0.captureEvent=Z39;gv0.captureException=Q39;gv0.captureMessage=D39;gv0.captureSession=N39;gv0.close=U39;gv0.configureScope=G39;gv0.endSession=ld1;gv0.flush=E39;gv0.getClient=If;gv0.getCurrentScope=z21;gv0.isInitialized=$39;gv0.lastEventId=w39;gv0.setContext=I39;gv0.setExtra=W39;gv0.setExtras=Y39;gv0.setTag=X39;gv0.setTags=J39;gv0.setUser=V39;gv0.startSession=q39;gv0.startTransaction=H39;gv0.withActiveSpan=K39;gv0.withIsolationScope=C39;gv0.withMonitor=z39;gv0.withScope=fv0});
var mg0=E((ug0)=>{Object.defineProperty(ug0,"__esModule",{value:!0});var KV9=Hf(),gg0=0,gc1=1/0,sJ1=0,HV9=(A)=>{A.forEach((B)=>{if(B.interactionId)gc1=Math.min(gc1,B.interactionId),sJ1=Math.max(sJ1,B.interactionId),gg0=sJ1?(sJ1-gc1)/7+1:0})},uc1,zV9=()=>{return uc1?gg0:performance.interactionCount||0},EV9=()=>{if("interactionCount"in performance||uc1)return;uc1=KV9.observe("event",HV9,{type:"event",buffered:!0,durationThreshold:0})};ug0.getInteractionCount=zV9;ug0.initInteractionCountPolyfill=EV9});
var mj=E((sh0)=>{var{_optionalChain:ah0}=wA();Object.defineProperty(sh0,"__esModule",{value:!0});function iJ9(A){let B=ah0([A,"call",(D)=>D(),"access",(D)=>D.getClient,"call",(D)=>D(),"optionalAccess",(D)=>D.getOptions,"call",(D)=>D()]);return(ah0([B,"optionalAccess",(D)=>D.instrumenter])||"sentry")!=="sentry"}sh0.shouldDisableAutoInstrumentation=iJ9});
var mx0=E((ux0)=>{Object.defineProperty(ux0,"__esModule",{value:!0});var p99=Rd1(),i99=$d1();function n99(A,B,Q){let D=[{type:"client_report"},{timestamp:Q||i99.dateTimestampInSeconds(),discarded_events:A}];return p99.createEnvelope(B?{dsn:B}:{},[D])}ux0.createClientReportEnvelope=n99});
var nc1=E((zu0)=>{Object.defineProperty(zu0,"__esModule",{value:!0});var ZN=SQ(),Ef=wA();function cC9(A,B,Q,D,Z="auto.http.browser"){if(!ZN.hasTracingEnabled()||!A.fetchData)return;let G=B(A.fetchData.url);if(A.endTimestamp&&G){let C=A.fetchData.__span;if(!C)return;let K=D[C];if(K)pC9(K,A),delete D[C];return}let F=ZN.getCurrentScope(),I=ZN.getClient(),{method:Y,url:W}=A.fetchData,J=lC9(W),X=J?Ef.parseUrl(J).host:void 0,V=G?ZN.startInactiveSpan({name:`${Y} ${W}`,onlyIfParent:!0,attributes:{url:W,type:"fetch","http.method":Y,"http.url":J,"server.address":X,[ZN.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]:Z},op:"http.client"}):void 0;if(V)A.fetchData.__span=V.spanContext().spanId,D[V.spanContext().spanId]=V;if(Q(A.fetchData.url)&&I){let C=A.args[0];A.args[1]=A.args[1]||{};let K=A.args[1];K.headers=Hu0(C,I,F,K,V)}return V}function Hu0(A,B,Q,D,Z){let G=Z||Q.getSpan(),F=ZN.getIsolationScope(),{traceId:I,spanId:Y,sampled:W,dsc:J}={...F.getPropagationContext(),...Q.getPropagationContext()},X=G?ZN.spanToTraceHeader(G):Ef.generateSentryTraceHeader(I,Y,W),V=Ef.dynamicSamplingContextToSentryBaggageHeader(J||(G?ZN.getDynamicSamplingContextFromSpan(G):ZN.getDynamicSamplingContextFromClient(I,B,Q))),C=D.headers||(typeof Request!=="undefined"&&Ef.isInstanceOf(A,Request)?A.headers:void 0);if(!C)return{"sentry-trace":X,baggage:V};else if(typeof Headers!=="undefined"&&Ef.isInstanceOf(C,Headers)){let K=new Headers(C);if(K.append("sentry-trace",X),V)K.append(Ef.BAGGAGE_HEADER_NAME,V);return K}else if(Array.isArray(C)){let K=[...C,["sentry-trace",X]];if(V)K.push([Ef.BAGGAGE_HEADER_NAME,V]);return K}else{let K="baggage"in C?C.baggage:void 0,H=[];if(Array.isArray(K))H.push(...K);else if(K)H.push(K);if(V)H.push(V);return{...C,"sentry-trace":X,baggage:H.length>0?H.join(","):void 0}}}function lC9(A){try{return new URL(A).href}catch(B){return}}function pC9(A,B){if(B.response){ZN.setHttpStatus(A,B.response.status);let Q=B.response&&B.response.headers&&B.response.headers.get("content-length");if(Q){let D=parseInt(Q);if(D>0)A.setAttribute("http.response_content_length",D)}}else if(B.error)A.setStatus("internal_error");A.end()}zu0.addTracingHeadersToFetchRequest=Hu0;zu0.instrumentFetchRequest=cC9});
var nd0=E((id0)=>{Object.defineProperty(id0,"__esModule",{value:!0});var zw9=SQ(),Ew9=LX1();function Uw9(A){let B=zw9.getClient();return new Ew9.Anr(A).setup(B),Promise.resolve()}id0.enableAnrDetection=Uw9});
var ng0=E((ig0)=>{Object.defineProperty(ig0,"__esModule",{value:!0});var $V9=$l(),qV9=ql(),NV9=Hf(),LV9=Nl(),lg0=mg0(),pg0=()=>{return lg0.getInteractionCount()},dg0=10,WO=[],mc1={},cg0=(A)=>{let B=WO[WO.length-1],Q=mc1[A.interactionId];if(Q||WO.length<dg0||A.duration>B.latency){if(Q)Q.entries.push(A),Q.latency=Math.max(Q.latency,A.duration);else{let D={id:A.interactionId,latency:A.duration,entries:[A]};mc1[D.id]=D,WO.push(D)}WO.sort((D,Z)=>Z.latency-D.latency),WO.splice(dg0).forEach((D)=>{delete mc1[D.id]})}},MV9=()=>{let A=Math.min(WO.length-1,Math.floor(pg0()/50));return WO[A]},RV9=(A,B)=>{B=B||{},lg0.initInteractionCountPolyfill();let Q=qV9.initMetric("INP"),D,Z=(F)=>{F.forEach((Y)=>{if(Y.interactionId)cg0(Y);if(Y.entryType==="first-input"){if(!WO.some((J)=>{return J.entries.some((X)=>{return Y.duration===X.duration&&Y.startTime===X.startTime})}))cg0(Y)}});let I=MV9();if(I&&I.latency!==Q.value)Q.value=I.latency,Q.entries=I.entries,D()},G=NV9.observe("event",Z,{durationThreshold:B.durationThreshold||40});if(D=$V9.bindReporter(A,Q,B.reportAllChanges),G)G.observe({type:"first-input",buffered:!0}),LV9.onHidden(()=>{if(Z(G.takeRecords()),Q.value<0&&pg0()>0)Q.value=0,Q.entries=[];D(!0)})};ig0.onINP=RV9});
var oU=E((sk0)=>{Object.defineProperty(sk0,"__esModule",{value:!0});var x19=rq(),vm1=kW(),v19="Sentry Logger ",bm1=["debug","info","warn","error","log","assert","trace"],fm1={};function ak0(A){if(!("console"in vm1.GLOBAL_OBJ))return A();let B=vm1.GLOBAL_OBJ.console,Q={},D=Object.keys(fm1);D.forEach((Z)=>{let G=fm1[Z];Q[Z]=B[Z],B[Z]=G});try{return A()}finally{D.forEach((Z)=>{B[Z]=Q[Z]})}}function b19(){let A=!1,B={enable:()=>{A=!0},disable:()=>{A=!1},isEnabled:()=>A};if(x19.DEBUG_BUILD)bm1.forEach((Q)=>{B[Q]=(...D)=>{if(A)ak0(()=>{vm1.GLOBAL_OBJ.console[Q](`${v19}[${Q}]:`,...D)})}});else bm1.forEach((Q)=>{B[Q]=()=>{return}});return B}var f19=b19();sk0.CONSOLE_LEVELS=bm1;sk0.consoleSandbox=ak0;sk0.logger=f19;sk0.originalConsoleMethods=fm1});
var pJ1=E((Pg0)=>{Object.defineProperty(Pg0,"__esModule",{value:!0});var hX9=_21(),gX9=()=>{let A=hX9.getNavigationEntry();return A&&A.activationStart||0};Pg0.getActivationStart=gX9});
var p_0=E((l_0)=>{Object.defineProperty(l_0,"__esModule",{value:!0});var Y29=rq(),W29=oU(),h_0=lm1(),g_0=sm1(),u_0=Ad1(),m_0=Dd1(),d_0=Fd1(),c_0=Yd1(),Jd1=Wd1();function J29(A,B){switch(A){case"console":return h_0.addConsoleInstrumentationHandler(B);case"dom":return g_0.addClickKeypressInstrumentationHandler(B);case"xhr":return Jd1.addXhrInstrumentationHandler(B);case"fetch":return u_0.addFetchInstrumentationHandler(B);case"history":return c_0.addHistoryInstrumentationHandler(B);case"error":return m_0.addGlobalErrorInstrumentationHandler(B);case"unhandledrejection":return d_0.addGlobalUnhandledRejectionInstrumentationHandler(B);default:Y29.DEBUG_BUILD&&W29.logger.warn("unknown instrumentation type:",A)}}l_0.addConsoleInstrumentationHandler=h_0.addConsoleInstrumentationHandler;l_0.addClickKeypressInstrumentationHandler=g_0.addClickKeypressInstrumentationHandler;l_0.addFetchInstrumentationHandler=u_0.addFetchInstrumentationHandler;l_0.addGlobalErrorInstrumentationHandler=m_0.addGlobalErrorInstrumentationHandler;l_0.addGlobalUnhandledRejectionInstrumentationHandler=d_0.addGlobalUnhandledRejectionInstrumentationHandler;l_0.addHistoryInstrumentationHandler=c_0.addHistoryInstrumentationHandler;l_0.SENTRY_XHR_DATA_KEY=Jd1.SENTRY_XHR_DATA_KEY;l_0.addXhrInstrumentationHandler=Jd1.addXhrInstrumentationHandler;l_0.addInstrumentationHandler=J29});
var qf0=E(($f0)=>{Object.defineProperty($f0,"__esModule",{value:!0});var Nc1=wA(),vF9=LJ1();function Lc1(A,B){let Q;return Nc1.forEachEnvelopeItem(A,(D,Z)=>{if(B.includes(Z))Q=Array.isArray(D)?D[1]:void 0;return!!Q}),Q}function bF9(A,B){return(Q)=>{let D=A(Q);return{...D,send:async(Z)=>{let G=Lc1(Z,["event","transaction","profile","replay_event"]);if(G)G.release=B;return D.send(Z)}}}}function fF9(A,B){return Nc1.createEnvelope(B?{...A[0],dsn:B}:A[0],A[1])}function hF9(A,B){return(Q)=>{let D=A(Q),Z=new Map;function G(Y,W){let J=W?`${Y}:${W}`:Y,X=Z.get(J);if(!X){let V=Nc1.dsnFromString(Y);if(!V)return;let C=vF9.getEnvelopeEndpointWithUrlEncodedAuth(V,Q.tunnel);X=W?bF9(A,W)({...Q,url:C}):A({...Q,url:C}),Z.set(J,X)}return[Y,X]}async function F(Y){function W(V){let C=V&&V.length?V:["event"];return Lc1(Y,C)}let J=B({envelope:Y,getEvent:W}).map((V)=>{if(typeof V==="string")return G(V,void 0);else return G(V.dsn,V.release)}).filter((V)=>!!V);if(J.length===0)J.push(["",D]);return(await Promise.all(J.map(([V,C])=>C.send(fF9(Y,V)))))[0]}async function I(Y){let W=[await D.flush(Y)];for(let[,J]of Z)W.push(await J.flush(Y));return W.every((J)=>J)}return{send:F,flush:I}}}$f0.eventFromEnvelope=Lc1;$f0.makeMultiplexedTransport=hF9});
var qh0=E(($h0)=>{Object.defineProperty($h0,"__esModule",{value:!0});var Eh0=IO(),AY9=zh0(),Uh0="MetricsAggregator",BY9=()=>{return{name:Uh0,setupOnce(){},setup(A){A.metricsAggregator=new AY9.BrowserMetricsAggregator(A)}}},wh0=Eh0.defineIntegration(BY9),QY9=Eh0.convertIntegrationFnToClass(Uh0,wh0);$h0.MetricsAggregator=QY9;$h0.metricsAggregatorIntegration=wh0});
var ql=E((jg0)=>{Object.defineProperty(jg0,"__esModule",{value:!0});var Sg0=hC(),mX9=Og0(),dX9=pJ1(),cX9=_21(),lX9=(A,B)=>{let Q=cX9.getNavigationEntry(),D="navigate";if(Q)if(Sg0.WINDOW.document&&Sg0.WINDOW.document.prerendering||dX9.getActivationStart()>0)D="prerender";else D=Q.type.replace(/_/g,"-");return{name:A,value:typeof B==="undefined"?-1:B,rating:"good",delta:0,entries:[],id:mX9.generateUniqueID(),navigationType:D}};jg0.initMetric=lX9});
var r_0=E((s_0)=>{Object.defineProperty(s_0,"__esModule",{value:!0});var j29=Vd1(),a_0=kW();function y29(){return typeof window!=="undefined"&&(!j29.isNodeEnv()||k29())}function k29(){return a_0.GLOBAL_OBJ.process!==void 0&&a_0.GLOBAL_OBJ.process.type==="renderer"}s_0.isBrowser=y29});
var rg0=E((sg0)=>{Object.defineProperty(sg0,"__esModule",{value:!0});var TV9=hC(),PV9=$l(),SV9=pJ1(),jV9=aJ1(),yV9=ql(),kV9=Hf(),_V9=Nl(),ag0={},xV9=(A)=>{let B=jV9.getVisibilityWatcher(),Q=yV9.initMetric("LCP"),D,Z=(F)=>{let I=F[F.length-1];if(I){let Y=Math.max(I.startTime-SV9.getActivationStart(),0);if(Y<B.firstHiddenTime)Q.value=Y,Q.entries=[I],D()}},G=kV9.observe("largest-contentful-paint",Z);if(G){D=PV9.bindReporter(A,Q);let F=()=>{if(!ag0[Q.id])Z(G.takeRecords()),G.disconnect(),ag0[Q.id]=!0,D(!0)};return["keydown","click"].forEach((I)=>{if(TV9.WINDOW.document)addEventListener(I,F,{once:!0,capture:!0})}),_V9.onHidden(F,!0),F}return};sg0.onLCP=xV9});
var rq=E((nk0)=>{Object.defineProperty(nk0,"__esModule",{value:!0});var k19=typeof __SENTRY_DEBUG__==="undefined"||__SENTRY_DEBUG__;nk0.DEBUG_BUILD=k19});
var ru0=E((su0)=>{Object.defineProperty(su0,"__esModule",{value:!0});var $H9=tc1(),qH9=wA();function NH9(){let A=$H9.lazyLoadedNodePerformanceMonitoringIntegrations.map((B)=>{try{return B()}catch(Q){return}}).filter((B)=>!!B);if(A.length===0)qH9.logger.warn("Performance monitoring integrations could not be automatically loaded.");return A.filter((B)=>!!B.loadDependency())}su0.autoDiscoverNodePerformanceMonitoringIntegrations=NH9});
var rx0=E((sx0)=>{Object.defineProperty(sx0,"__esModule",{value:!0});function nx0(A,B,Q){let D=B.match(/([a-z_]+)\.(.*)/i);if(D===null)A[B]=Q;else{let Z=A[D[1]];nx0(Z,D[2],Q)}}function QQ9(A,B,Q={}){return Array.isArray(B)?ax0(A,B,Q):DQ9(A,B,Q)}function ax0(A,B,Q){let D=B.find((Z)=>Z.name===A.name);if(D){for(let[Z,G]of Object.entries(Q))nx0(D,Z,G);return B}return[...B,A]}function DQ9(A,B,Q){return(Z)=>{let G=B(Z);if(A.allowExclusionByUser){if(!G.find((I)=>I.name===A.name))return G}return ax0(A,G,Q)}}sx0.addOrUpdateIntegration=QQ9});
var sf0=E((af0)=>{Object.defineProperty(af0,"__esModule",{value:!0});var lf0=wA(),pf0=IO(),JI9=BV(),Rc1={include:{cookies:!0,data:!0,headers:!0,ip:!1,query_string:!0,url:!0,user:{id:!0,username:!0,email:!0}},transactionNamingScheme:"methodPath"},if0="RequestData",XI9=(A={})=>{let B=lf0.addRequestDataToEvent,Q={...Rc1,...A,include:{method:!0,...Rc1.include,...A.include,user:A.include&&typeof A.include.user==="boolean"?A.include.user:{...Rc1.include.user,...(A.include||{}).user}}};return{name:if0,setupOnce(){},processEvent(D,Z,G){let{transactionNamingScheme:F}=Q,{sdkProcessingMetadata:I={}}=D,Y=I.request;if(!Y)return D;let W=I.requestDataOptionsFromExpressHandler||I.requestDataOptionsFromGCPWrapper||CI9(Q),J=B(D,Y,W);if(D.type==="transaction"||F==="handler")return J;let V=Y._sentryTransaction;if(V){let C=JI9.spanToJSON(V).description||"",K=KI9(G)==="sentry.javascript.nextjs"?C.startsWith("/api"):F!=="path",[H]=lf0.extractPathForTransaction(Y,{path:!0,method:K,customRoute:C});J.transaction=H}return J}}},nf0=pf0.defineIntegration(XI9),VI9=pf0.convertIntegrationFnToClass(if0,nf0);function CI9(A){let{transactionNamingScheme:B,include:{ip:Q,user:D,...Z}}=A,G=[];for(let[I,Y]of Object.entries(Z))if(Y)G.push(I);let F;if(D===void 0)F=!0;else if(typeof D==="boolean")F=D;else{let I=[];for(let[Y,W]of Object.entries(D))if(W)I.push(Y);F=I}return{include:{ip:Q,user:F,request:G.length!==0?G:void 0,transaction:B}}}function KI9(A){try{return A.getOptions()._metadata.sdk.name}catch(B){return}}af0.RequestData=VI9;af0.requestDataIntegration=nf0});
var sm1=E((R_0)=>{Object.defineProperty(R_0,"__esModule",{value:!0});var WA9=D21(),vW1=gH(),JA9=kW(),im1=QO(),Dl=JA9.GLOBAL_OBJ,XA9=1000,N_0,nm1,am1;function VA9(A){im1.addHandler("dom",A),im1.maybeInstrument("dom",M_0)}function M_0(){if(!Dl.document)return;let A=im1.triggerHandlers.bind(null,"dom"),B=L_0(A,!0);Dl.document.addEventListener("click",B,!1),Dl.document.addEventListener("keypress",B,!1),["EventTarget","Node"].forEach((Q)=>{let D=Dl[Q]&&Dl[Q].prototype;if(!D||!D.hasOwnProperty||!D.hasOwnProperty("addEventListener"))return;vW1.fill(D,"addEventListener",function(Z){return function(G,F,I){if(G==="click"||G=="keypress")try{let Y=this,W=Y.__sentry_instrumentation_handlers__=Y.__sentry_instrumentation_handlers__||{},J=W[G]=W[G]||{refCount:0};if(!J.handler){let X=L_0(A);J.handler=X,Z.call(this,G,X,I)}J.refCount++}catch(Y){}return Z.call(this,G,F,I)}}),vW1.fill(D,"removeEventListener",function(Z){return function(G,F,I){if(G==="click"||G=="keypress")try{let Y=this,W=Y.__sentry_instrumentation_handlers__||{},J=W[G];if(J){if(J.refCount--,J.refCount<=0)Z.call(this,G,J.handler,I),J.handler=void 0,delete W[G];if(Object.keys(W).length===0)delete Y.__sentry_instrumentation_handlers__}}catch(Y){}return Z.call(this,G,F,I)}})})}function CA9(A){if(A.type!==nm1)return!1;try{if(!A.target||A.target._sentryId!==am1)return!1}catch(B){}return!0}function KA9(A,B){if(A!=="keypress")return!1;if(!B||!B.tagName)return!0;if(B.tagName==="INPUT"||B.tagName==="TEXTAREA"||B.isContentEditable)return!1;return!0}function L_0(A,B=!1){return(Q)=>{if(!Q||Q._sentryCaptured)return;let D=HA9(Q);if(KA9(Q.type,D))return;if(vW1.addNonEnumerableProperty(Q,"_sentryCaptured",!0),D&&!D._sentryId)vW1.addNonEnumerableProperty(D,"_sentryId",WA9.uuid4());let Z=Q.type==="keypress"?"input":Q.type;if(!CA9(Q))A({event:Q,name:Z,global:B}),nm1=Q.type,am1=D?D._sentryId:void 0;clearTimeout(N_0),N_0=Dl.setTimeout(()=>{am1=void 0,nm1=void 0},XA9)}}function HA9(A){try{return A.target}catch(B){return null}}R_0.addClickKeypressInstrumentationHandler=VA9;R_0.instrumentDOM=M_0});
var tc1=E((au0)=>{Object.defineProperty(au0,"__esModule",{value:!0});var KO=SQ(),lu0=wA(),kK9=Ag0(),_K9=Qg0(),xK9=Zg0(),vK9=Ig0(),bK9=Jg0(),fK9=Cg0(),hK9=zg0(),gK9=Ug0(),pu0=ku0(),oc1=uu0(),iu0=eJ1(),AX1=Ml(),nu0=nc1(),uK9=cu0();au0.IdleTransaction=KO.IdleTransaction;au0.Span=KO.Span;au0.SpanStatus=KO.SpanStatus;au0.Transaction=KO.Transaction;au0.extractTraceparentData=KO.extractTraceparentData;au0.getActiveTransaction=KO.getActiveTransaction;au0.hasTracingEnabled=KO.hasTracingEnabled;au0.spanStatusfromHttpCode=KO.spanStatusfromHttpCode;au0.startIdleTransaction=KO.startIdleTransaction;au0.TRACEPARENT_REGEXP=lu0.TRACEPARENT_REGEXP;au0.stripUrlQueryAndFragment=lu0.stripUrlQueryAndFragment;au0.Express=kK9.Express;au0.Postgres=_K9.Postgres;au0.Mysql=xK9.Mysql;au0.Mongo=vK9.Mongo;au0.Prisma=bK9.Prisma;au0.GraphQL=fK9.GraphQL;au0.Apollo=hK9.Apollo;au0.lazyLoadedNodePerformanceMonitoringIntegrations=gK9.lazyLoadedNodePerformanceMonitoringIntegrations;au0.BROWSER_TRACING_INTEGRATION_ID=pu0.BROWSER_TRACING_INTEGRATION_ID;au0.BrowserTracing=pu0.BrowserTracing;au0.browserTracingIntegration=oc1.browserTracingIntegration;au0.startBrowserTracingNavigationSpan=oc1.startBrowserTracingNavigationSpan;au0.startBrowserTracingPageLoadSpan=oc1.startBrowserTracingPageLoadSpan;au0.defaultRequestInstrumentationOptions=iu0.defaultRequestInstrumentationOptions;au0.instrumentOutgoingRequests=iu0.instrumentOutgoingRequests;au0.addClsInstrumentationHandler=AX1.addClsInstrumentationHandler;au0.addFidInstrumentationHandler=AX1.addFidInstrumentationHandler;au0.addLcpInstrumentationHandler=AX1.addLcpInstrumentationHandler;au0.addPerformanceInstrumentationHandler=AX1.addPerformanceInstrumentationHandler;au0.addTracingHeadersToFetchRequest=nu0.addTracingHeadersToFetchRequest;au0.instrumentFetchRequest=nu0.instrumentFetchRequest;au0.addExtensionMethods=uK9.addExtensionMethods});
var tg0=E((og0)=>{Object.defineProperty(og0,"__esModule",{value:!0});var dc1=hC(),bV9=$l(),fV9=pJ1(),hV9=_21(),gV9=ql(),cc1=(A)=>{if(!dc1.WINDOW.document)return;if(dc1.WINDOW.document.prerendering)addEventListener("prerenderingchange",()=>cc1(A),!0);else if(dc1.WINDOW.document.readyState!=="complete")addEventListener("load",()=>cc1(A),!0);else setTimeout(A,0)},uV9=(A,B)=>{B=B||{};let Q=gV9.initMetric("TTFB"),D=bV9.bindReporter(A,Q,B.reportAllChanges);cc1(()=>{let Z=hV9.getNavigationEntry();if(Z){if(Q.value=Math.max(Z.responseStart-fV9.getActivationStart(),0),Q.value<0||Q.value>performance.now())return;Q.entries=[Z],D(!0)}})};og0.onTTFB=uV9});
var tm1=E((O_0)=>{Object.defineProperty(O_0,"__esModule",{value:!0});var UA9=rq(),wA9=oU(),$A9=kW(),bW1=$A9.getGlobalObject();function qA9(){try{return new ErrorEvent(""),!0}catch(A){return!1}}function NA9(){try{return new DOMError(""),!0}catch(A){return!1}}function LA9(){try{return new DOMException(""),!0}catch(A){return!1}}function om1(){if(!("fetch"in bW1))return!1;try{return new Request("http://www.example.com"),!0}catch(A){return!1}}function rm1(A){return A&&/^function fetch\(\)\s+\{\s+\[native code\]\s+\}$/.test(A.toString())}function MA9(){if(typeof EdgeRuntime==="string")return!0;if(!om1())return!1;if(rm1(bW1.fetch))return!0;let A=!1,B=bW1.document;if(B&&typeof B.createElement==="function")try{let Q=B.createElement("iframe");if(Q.hidden=!0,B.head.appendChild(Q),Q.contentWindow&&Q.contentWindow.fetch)A=rm1(Q.contentWindow.fetch);B.head.removeChild(Q)}catch(Q){UA9.DEBUG_BUILD&&wA9.logger.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",Q)}return A}function RA9(){return"ReportingObserver"in bW1}function OA9(){if(!om1())return!1;try{return new Request("_",{referrerPolicy:"origin"}),!0}catch(A){return!1}}O_0.isNativeFetch=rm1;O_0.supportsDOMError=NA9;O_0.supportsDOMException=LA9;O_0.supportsErrorEvent=qA9;O_0.supportsFetch=om1;O_0.supportsNativeFetch=MA9;O_0.supportsReferrerPolicy=OA9;O_0.supportsReportingObserver=RA9});
var tx0=E((ox0)=>{Object.defineProperty(ox0,"__esModule",{value:!0});function GQ9(A){let B=[],Q={};return{add(D,Z){while(B.length>=A){let G=B.shift();if(G!==void 0)delete Q[G]}if(Q[D])this.delete(D);B.push(D),Q[D]=Z},clear(){Q={},B=[]},get(D){return Q[D]},size(){return B.length},delete(D){if(!Q[D])return!1;delete Q[D];for(let Z=0;Z<B.length;Z++)if(B[Z]===D){B.splice(Z,1);break}return!0}}}ox0.makeFifoCache=GQ9});
var uc0=E((gc0,ql1)=>{/*!
    localForage -- Offline Storage, Improved
    Version 1.10.0
    https://localforage.github.io/localForage
    (c) 2013-2017 Mozilla, Apache License 2.0
*/(function(A){if(typeof gc0==="object"&&typeof ql1!=="undefined")ql1.exports=A();else if(typeof define==="function"&&define.amd)define([],A);else{var B;if(typeof window!=="undefined")B=window;else if(typeof global!=="undefined")B=global;else if(typeof self!=="undefined")B=self;else B=this;B.localforage=A()}})(function(){var A,B,Q;return function D(Z,G,F){function I(J,X){if(!G[J]){if(!Z[J]){var V=J1;if(!X&&V)return V(J,!0);if(Y)return Y(J,!0);var C=new Error("Cannot find module '"+J+"'");throw C.code="MODULE_NOT_FOUND",C}var K=G[J]={exports:{}};Z[J][0].call(K.exports,function(H){var z=Z[J][1][H];return I(z?z:H)},K,K.exports,D,Z,G,F)}return G[J].exports}var Y=J1;for(var W=0;W<F.length;W++)I(F[W]);return I}({1:[function(D,Z,G){(function(F){var I=F.MutationObserver||F.WebKitMutationObserver,Y;if(I){var W=0,J=new I(H),X=F.document.createTextNode("");J.observe(X,{characterData:!0}),Y=function(){X.data=W=++W%2}}else if(!F.setImmediate&&typeof F.MessageChannel!=="undefined"){var V=new F.MessageChannel;V.port1.onmessage=H,Y=function(){V.port2.postMessage(0)}}else if("document"in F&&"onreadystatechange"in F.document.createElement("script"))Y=function(){var $=F.document.createElement("script");$.onreadystatechange=function(){H(),$.onreadystatechange=null,$.parentNode.removeChild($),$=null},F.document.documentElement.appendChild($)};else Y=function(){setTimeout(H,0)};var C,K=[];function H(){C=!0;var $,L,N=K.length;while(N){L=K,K=[],$=-1;while(++$<N)L[$]();N=K.length}C=!1}Z.exports=z;function z($){if(K.push($)===1&&!C)Y()}}).call(this,typeof global!=="undefined"?global:typeof self!=="undefined"?self:typeof window!=="undefined"?window:{})},{}],2:[function(D,Z,G){var F=D(1);function I(){}var Y={},W=["REJECTED"],J=["FULFILLED"],X=["PENDING"];Z.exports=V;function V(T){if(typeof T!=="function")throw new TypeError("resolver must be a function");if(this.state=X,this.queue=[],this.outcome=void 0,T!==I)z(this,T)}V.prototype.catch=function(T){return this.then(null,T)},V.prototype.then=function(T,j){if(typeof T!=="function"&&this.state===J||typeof j!=="function"&&this.state===W)return this;var f=new this.constructor(I);if(this.state!==X){var y=this.state===J?T:j;K(f,y,this.outcome)}else this.queue.push(new C(f,T,j));return f};function C(T,j,f){if(this.promise=T,typeof j==="function")this.onFulfilled=j,this.callFulfilled=this.otherCallFulfilled;if(typeof f==="function")this.onRejected=f,this.callRejected=this.otherCallRejected}C.prototype.callFulfilled=function(T){Y.resolve(this.promise,T)},C.prototype.otherCallFulfilled=function(T){K(this.promise,this.onFulfilled,T)},C.prototype.callRejected=function(T){Y.reject(this.promise,T)},C.prototype.otherCallRejected=function(T){K(this.promise,this.onRejected,T)};function K(T,j,f){F(function(){var y;try{y=j(f)}catch(c){return Y.reject(T,c)}if(y===T)Y.reject(T,new TypeError("Cannot resolve promise with itself"));else Y.resolve(T,y)})}Y.resolve=function(T,j){var f=$(H,j);if(f.status==="error")return Y.reject(T,f.value);var y=f.value;if(y)z(T,y);else{T.state=J,T.outcome=j;var c=-1,h=T.queue.length;while(++c<h)T.queue[c].callFulfilled(j)}return T},Y.reject=function(T,j){T.state=W,T.outcome=j;var f=-1,y=T.queue.length;while(++f<y)T.queue[f].callRejected(j);return T};function H(T){var j=T&&T.then;if(T&&(typeof T==="object"||typeof T==="function")&&typeof j==="function")return function f(){j.apply(T,arguments)}}function z(T,j){var f=!1;function y(n){if(f)return;f=!0,Y.reject(T,n)}function c(n){if(f)return;f=!0,Y.resolve(T,n)}function h(){j(c,y)}var a=$(h);if(a.status==="error")y(a.value)}function $(T,j){var f={};try{f.value=T(j),f.status="success"}catch(y){f.status="error",f.value=y}return f}V.resolve=L;function L(T){if(T instanceof this)return T;return Y.resolve(new this(I),T)}V.reject=N;function N(T){var j=new this(I);return Y.reject(j,T)}V.all=O;function O(T){var j=this;if(Object.prototype.toString.call(T)!=="[object Array]")return this.reject(new TypeError("must be an array"));var f=T.length,y=!1;if(!f)return this.resolve([]);var c=new Array(f),h=0,a=-1,n=new this(I);while(++a<f)v(T[a],a);return n;function v(t,W1){j.resolve(t).then(z1,function(f1){if(!y)y=!0,Y.reject(n,f1)});function z1(f1){if(c[W1]=f1,++h===f&&!y)y=!0,Y.resolve(n,c)}}}V.race=R;function R(T){var j=this;if(Object.prototype.toString.call(T)!=="[object Array]")return this.reject(new TypeError("must be an array"));var f=T.length,y=!1;if(!f)return this.resolve([]);var c=-1,h=new this(I);while(++c<f)a(T[c]);return h;function a(n){j.resolve(n).then(function(v){if(!y)y=!0,Y.resolve(h,v)},function(v){if(!y)y=!0,Y.reject(h,v)})}}},{"1":1}],3:[function(D,Z,G){(function(F){if(typeof F.Promise!=="function")F.Promise=D(2)}).call(this,typeof global!=="undefined"?global:typeof self!=="undefined"?self:typeof window!=="undefined"?window:{})},{"2":2}],4:[function(D,Z,G){var F=typeof Symbol==="function"&&typeof Symbol.iterator==="symbol"?function(U1){return typeof U1}:function(U1){return U1&&typeof Symbol==="function"&&U1.constructor===Symbol&&U1!==Symbol.prototype?"symbol":typeof U1};function I(U1,t1){if(!(U1 instanceof t1))throw new TypeError("Cannot call a class as a function")}function Y(){try{if(typeof indexedDB!=="undefined")return indexedDB;if(typeof webkitIndexedDB!=="undefined")return webkitIndexedDB;if(typeof mozIndexedDB!=="undefined")return mozIndexedDB;if(typeof OIndexedDB!=="undefined")return OIndexedDB;if(typeof msIndexedDB!=="undefined")return msIndexedDB}catch(U1){return}}var W=Y();function J(){try{if(!W||!W.open)return!1;var U1=typeof openDatabase!=="undefined"&&/(Safari|iPhone|iPad|iPod)/.test(navigator.userAgent)&&!/Chrome/.test(navigator.userAgent)&&!/BlackBerry/.test(navigator.platform),t1=typeof fetch==="function"&&fetch.toString().indexOf("[native code")!==-1;return(!U1||t1)&&typeof indexedDB!=="undefined"&&typeof IDBKeyRange!=="undefined"}catch(d1){return!1}}function X(U1,t1){U1=U1||[],t1=t1||{};try{return new Blob(U1,t1)}catch($0){if($0.name!=="TypeError")throw $0;var d1=typeof BlobBuilder!=="undefined"?BlobBuilder:typeof MSBlobBuilder!=="undefined"?MSBlobBuilder:typeof MozBlobBuilder!=="undefined"?MozBlobBuilder:WebKitBlobBuilder,z0=new d1;for(var M0=0;M0<U1.length;M0+=1)z0.append(U1[M0]);return z0.getBlob(t1.type)}}if(typeof Promise==="undefined")D(3);var V=Promise;function C(U1,t1){if(t1)U1.then(function(d1){t1(null,d1)},function(d1){t1(d1)})}function K(U1,t1,d1){if(typeof t1==="function")U1.then(t1);if(typeof d1==="function")U1.catch(d1)}function H(U1){if(typeof U1!=="string")console.warn(U1+" used as a key, but it is not a string."),U1=String(U1);return U1}function z(){if(arguments.length&&typeof arguments[arguments.length-1]==="function")return arguments[arguments.length-1]}var $="local-forage-detect-blob-support",L=void 0,N={},O=Object.prototype.toString,R="readonly",T="readwrite";function j(U1){var t1=U1.length,d1=new ArrayBuffer(t1),z0=new Uint8Array(d1);for(var M0=0;M0<t1;M0++)z0[M0]=U1.charCodeAt(M0);return d1}function f(U1){return new V(function(t1){var d1=U1.transaction($,T),z0=X([""]);d1.objectStore($).put(z0,"key"),d1.onabort=function(M0){M0.preventDefault(),M0.stopPropagation(),t1(!1)},d1.oncomplete=function(){var M0=navigator.userAgent.match(/Chrome\/(\d+)/),$0=navigator.userAgent.match(/Edge\//);t1($0||!M0||parseInt(M0[1],10)>=43)}}).catch(function(){return!1})}function y(U1){if(typeof L==="boolean")return V.resolve(L);return f(U1).then(function(t1){return L=t1,L})}function c(U1){var t1=N[U1.name],d1={};if(d1.promise=new V(function(z0,M0){d1.resolve=z0,d1.reject=M0}),t1.deferredOperations.push(d1),!t1.dbReady)t1.dbReady=d1.promise;else t1.dbReady=t1.dbReady.then(function(){return d1.promise})}function h(U1){var t1=N[U1.name],d1=t1.deferredOperations.pop();if(d1)return d1.resolve(),d1.promise}function a(U1,t1){var d1=N[U1.name],z0=d1.deferredOperations.pop();if(z0)return z0.reject(t1),z0.promise}function n(U1,t1){return new V(function(d1,z0){if(N[U1.name]=N[U1.name]||Q1(),U1.db)if(t1)c(U1),U1.db.close();else return d1(U1.db);var M0=[U1.name];if(t1)M0.push(U1.version);var $0=W.open.apply(W,M0);if(t1)$0.onupgradeneeded=function(AA){var UA=$0.result;try{if(UA.createObjectStore(U1.storeName),AA.oldVersion<=1)UA.createObjectStore($)}catch(VA){if(VA.name==="ConstraintError")console.warn('The database "'+U1.name+'" has been upgraded from version '+AA.oldVersion+" to version "+AA.newVersion+', but the storage "'+U1.storeName+'" already exists.');else throw VA}};$0.onerror=function(AA){AA.preventDefault(),z0($0.error)},$0.onsuccess=function(){var AA=$0.result;AA.onversionchange=function(UA){UA.target.close()},d1(AA),h(U1)}})}function v(U1){return n(U1,!1)}function t(U1){return n(U1,!0)}function W1(U1,t1){if(!U1.db)return!0;var d1=!U1.db.objectStoreNames.contains(U1.storeName),z0=U1.version<U1.db.version,M0=U1.version>U1.db.version;if(z0){if(U1.version!==t1)console.warn('The database "'+U1.name+`" can't be downgraded from version `+U1.db.version+" to version "+U1.version+".");U1.version=U1.db.version}if(M0||d1){if(d1){var $0=U1.db.version+1;if($0>U1.version)U1.version=$0}return!0}return!1}function z1(U1){return new V(function(t1,d1){var z0=new FileReader;z0.onerror=d1,z0.onloadend=function(M0){var $0=btoa(M0.target.result||"");t1({__local_forage_encoded_blob:!0,data:$0,type:U1.type})},z0.readAsBinaryString(U1)})}function f1(U1){var t1=j(atob(U1.data));return X([t1],{type:U1.type})}function G0(U1){return U1&&U1.__local_forage_encoded_blob}function X0(U1){var t1=this,d1=t1._initReady().then(function(){var z0=N[t1._dbInfo.name];if(z0&&z0.dbReady)return z0.dbReady});return K(d1,U1,U1),d1}function g1(U1){c(U1);var t1=N[U1.name],d1=t1.forages;for(var z0=0;z0<d1.length;z0++){var M0=d1[z0];if(M0._dbInfo.db)M0._dbInfo.db.close(),M0._dbInfo.db=null}return U1.db=null,v(U1).then(function($0){if(U1.db=$0,W1(U1))return t(U1);return $0}).then(function($0){U1.db=t1.db=$0;for(var AA=0;AA<d1.length;AA++)d1[AA]._dbInfo.db=$0}).catch(function($0){throw a(U1,$0),$0})}function K1(U1,t1,d1,z0){if(z0===void 0)z0=1;try{var M0=U1.db.transaction(U1.storeName,t1);d1(null,M0)}catch($0){if(z0>0&&(!U1.db||$0.name==="InvalidStateError"||$0.name==="NotFoundError"))return V.resolve().then(function(){if(!U1.db||$0.name==="NotFoundError"&&!U1.db.objectStoreNames.contains(U1.storeName)&&U1.version<=U1.db.version){if(U1.db)U1.version=U1.db.version+1;return t(U1)}}).then(function(){return g1(U1).then(function(){K1(U1,t1,d1,z0-1)})}).catch(d1);d1($0)}}function Q1(){return{forages:[],db:null,dbReady:null,deferredOperations:[]}}function _1(U1){var t1=this,d1={db:null};if(U1)for(var z0 in U1)d1[z0]=U1[z0];var M0=N[d1.name];if(!M0)M0=Q1(),N[d1.name]=M0;if(M0.forages.push(t1),!t1._initReady)t1._initReady=t1.ready,t1.ready=X0;var $0=[];function AA(){return V.resolve()}for(var UA=0;UA<M0.forages.length;UA++){var VA=M0.forages[UA];if(VA!==t1)$0.push(VA._initReady().catch(AA))}var TA=M0.forages.slice(0);return V.all($0).then(function(){return d1.db=M0.db,v(d1)}).then(function(uA){if(d1.db=uA,W1(d1,t1._defaultConfig.version))return t(d1);return uA}).then(function(uA){d1.db=M0.db=uA,t1._dbInfo=d1;for(var W2=0;W2<TA.length;W2++){var w9=TA[W2];if(w9!==t1)w9._dbInfo.db=d1.db,w9._dbInfo.version=d1.version}})}function q1(U1,t1){var d1=this;U1=H(U1);var z0=new V(function(M0,$0){d1.ready().then(function(){K1(d1._dbInfo,R,function(AA,UA){if(AA)return $0(AA);try{var VA=UA.objectStore(d1._dbInfo.storeName),TA=VA.get(U1);TA.onsuccess=function(){var uA=TA.result;if(uA===void 0)uA=null;if(G0(uA))uA=f1(uA);M0(uA)},TA.onerror=function(){$0(TA.error)}}catch(uA){$0(uA)}})}).catch($0)});return C(z0,t1),z0}function B0(U1,t1){var d1=this,z0=new V(function(M0,$0){d1.ready().then(function(){K1(d1._dbInfo,R,function(AA,UA){if(AA)return $0(AA);try{var VA=UA.objectStore(d1._dbInfo.storeName),TA=VA.openCursor(),uA=1;TA.onsuccess=function(){var W2=TA.result;if(W2){var w9=W2.value;if(G0(w9))w9=f1(w9);var OA=U1(w9,W2.key,uA++);if(OA!==void 0)M0(OA);else W2.continue()}else M0()},TA.onerror=function(){$0(TA.error)}}catch(W2){$0(W2)}})}).catch($0)});return C(z0,t1),z0}function K0(U1,t1,d1){var z0=this;U1=H(U1);var M0=new V(function($0,AA){var UA;z0.ready().then(function(){if(UA=z0._dbInfo,O.call(t1)==="[object Blob]")return y(UA.db).then(function(VA){if(VA)return t1;return z1(t1)});return t1}).then(function(VA){K1(z0._dbInfo,T,function(TA,uA){if(TA)return AA(TA);try{var W2=uA.objectStore(z0._dbInfo.storeName);if(VA===null)VA=void 0;var w9=W2.put(VA,U1);uA.oncomplete=function(){if(VA===void 0)VA=null;$0(VA)},uA.onabort=uA.onerror=function(){var OA=w9.error?w9.error:w9.transaction.error;AA(OA)}}catch(OA){AA(OA)}})}).catch(AA)});return C(M0,d1),M0}function s1(U1,t1){var d1=this;U1=H(U1);var z0=new V(function(M0,$0){d1.ready().then(function(){K1(d1._dbInfo,T,function(AA,UA){if(AA)return $0(AA);try{var VA=UA.objectStore(d1._dbInfo.storeName),TA=VA.delete(U1);UA.oncomplete=function(){M0()},UA.onerror=function(){$0(TA.error)},UA.onabort=function(){var uA=TA.error?TA.error:TA.transaction.error;$0(uA)}}catch(uA){$0(uA)}})}).catch($0)});return C(z0,t1),z0}function A1(U1){var t1=this,d1=new V(function(z0,M0){t1.ready().then(function(){K1(t1._dbInfo,T,function($0,AA){if($0)return M0($0);try{var UA=AA.objectStore(t1._dbInfo.storeName),VA=UA.clear();AA.oncomplete=function(){z0()},AA.onabort=AA.onerror=function(){var TA=VA.error?VA.error:VA.transaction.error;M0(TA)}}catch(TA){M0(TA)}})}).catch(M0)});return C(d1,U1),d1}function D1(U1){var t1=this,d1=new V(function(z0,M0){t1.ready().then(function(){K1(t1._dbInfo,R,function($0,AA){if($0)return M0($0);try{var UA=AA.objectStore(t1._dbInfo.storeName),VA=UA.count();VA.onsuccess=function(){z0(VA.result)},VA.onerror=function(){M0(VA.error)}}catch(TA){M0(TA)}})}).catch(M0)});return C(d1,U1),d1}function I1(U1,t1){var d1=this,z0=new V(function(M0,$0){if(U1<0){M0(null);return}d1.ready().then(function(){K1(d1._dbInfo,R,function(AA,UA){if(AA)return $0(AA);try{var VA=UA.objectStore(d1._dbInfo.storeName),TA=!1,uA=VA.openKeyCursor();uA.onsuccess=function(){var W2=uA.result;if(!W2){M0(null);return}if(U1===0)M0(W2.key);else if(!TA)TA=!0,W2.advance(U1);else M0(W2.key)},uA.onerror=function(){$0(uA.error)}}catch(W2){$0(W2)}})}).catch($0)});return C(z0,t1),z0}function E1(U1){var t1=this,d1=new V(function(z0,M0){t1.ready().then(function(){K1(t1._dbInfo,R,function($0,AA){if($0)return M0($0);try{var UA=AA.objectStore(t1._dbInfo.storeName),VA=UA.openKeyCursor(),TA=[];VA.onsuccess=function(){var uA=VA.result;if(!uA){z0(TA);return}TA.push(uA.key),uA.continue()},VA.onerror=function(){M0(VA.error)}}catch(uA){M0(uA)}})}).catch(M0)});return C(d1,U1),d1}function M1(U1,t1){t1=z.apply(this,arguments);var d1=this.config();if(U1=typeof U1!=="function"&&U1||{},!U1.name)U1.name=U1.name||d1.name,U1.storeName=U1.storeName||d1.storeName;var z0=this,M0;if(!U1.name)M0=V.reject("Invalid arguments");else{var $0=U1.name===d1.name&&z0._dbInfo.db,AA=$0?V.resolve(z0._dbInfo.db):v(U1).then(function(UA){var VA=N[U1.name],TA=VA.forages;VA.db=UA;for(var uA=0;uA<TA.length;uA++)TA[uA]._dbInfo.db=UA;return UA});if(!U1.storeName)M0=AA.then(function(UA){c(U1);var VA=N[U1.name],TA=VA.forages;UA.close();for(var uA=0;uA<TA.length;uA++){var W2=TA[uA];W2._dbInfo.db=null}var w9=new V(function(OA,e2){var uB=W.deleteDatabase(U1.name);uB.onerror=function(){var m2=uB.result;if(m2)m2.close();e2(uB.error)},uB.onblocked=function(){console.warn('dropInstance blocked for database "'+U1.name+'" until all open connections are closed')},uB.onsuccess=function(){var m2=uB.result;if(m2)m2.close();OA(m2)}});return w9.then(function(OA){VA.db=OA;for(var e2=0;e2<TA.length;e2++){var uB=TA[e2];h(uB._dbInfo)}}).catch(function(OA){throw(a(U1,OA)||V.resolve()).catch(function(){}),OA})});else M0=AA.then(function(UA){if(!UA.objectStoreNames.contains(U1.storeName))return;var VA=UA.version+1;c(U1);var TA=N[U1.name],uA=TA.forages;UA.close();for(var W2=0;W2<uA.length;W2++){var w9=uA[W2];w9._dbInfo.db=null,w9._dbInfo.version=VA}var OA=new V(function(e2,uB){var m2=W.open(U1.name,VA);m2.onerror=function(cQ){var lQ=m2.result;lQ.close(),uB(cQ)},m2.onupgradeneeded=function(){var cQ=m2.result;cQ.deleteObjectStore(U1.storeName)},m2.onsuccess=function(){var cQ=m2.result;cQ.close(),e2(cQ)}});return OA.then(function(e2){TA.db=e2;for(var uB=0;uB<uA.length;uB++){var m2=uA[uB];m2._dbInfo.db=e2,h(m2._dbInfo)}}).catch(function(e2){throw(a(U1,e2)||V.resolve()).catch(function(){}),e2})})}return C(M0,t1),M0}var B1={_driver:"asyncStorage",_initStorage:_1,_support:J(),iterate:B0,getItem:q1,setItem:K0,removeItem:s1,clear:A1,length:D1,key:I1,keys:E1,dropInstance:M1};function b1(){return typeof openDatabase==="function"}var c1="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n1="~~local_forage_type~",C0=/^~~local_forage_type~([^~]+)~/,W0="__lfsc__:",O0=W0.length,zA="arbf",d0="blob",YA="si08",w2="ui08",$2="uic8",r2="si16",C2="si32",zB="ur16",f6="ui32",kA="fl32",I2="fl64",M2=O0+zA.length,nA=Object.prototype.toString;function aA(U1){var t1=U1.length*0.75,d1=U1.length,z0,M0=0,$0,AA,UA,VA;if(U1[U1.length-1]==="="){if(t1--,U1[U1.length-2]==="=")t1--}var TA=new ArrayBuffer(t1),uA=new Uint8Array(TA);for(z0=0;z0<d1;z0+=4)$0=c1.indexOf(U1[z0]),AA=c1.indexOf(U1[z0+1]),UA=c1.indexOf(U1[z0+2]),VA=c1.indexOf(U1[z0+3]),uA[M0++]=$0<<2|AA>>4,uA[M0++]=(AA&15)<<4|UA>>2,uA[M0++]=(UA&3)<<6|VA&63;return TA}function o2(U1){var t1=new Uint8Array(U1),d1="",z0;for(z0=0;z0<t1.length;z0+=3)d1+=c1[t1[z0]>>2],d1+=c1[(t1[z0]&3)<<4|t1[z0+1]>>4],d1+=c1[(t1[z0+1]&15)<<2|t1[z0+2]>>6],d1+=c1[t1[z0+2]&63];if(t1.length%3===2)d1=d1.substring(0,d1.length-1)+"=";else if(t1.length%3===1)d1=d1.substring(0,d1.length-2)+"==";return d1}function fB(U1,t1){var d1="";if(U1)d1=nA.call(U1);if(U1&&(d1==="[object ArrayBuffer]"||U1.buffer&&nA.call(U1.buffer)==="[object ArrayBuffer]")){var z0,M0=W0;if(U1 instanceof ArrayBuffer)z0=U1,M0+=zA;else if(z0=U1.buffer,d1==="[object Int8Array]")M0+=YA;else if(d1==="[object Uint8Array]")M0+=w2;else if(d1==="[object Uint8ClampedArray]")M0+=$2;else if(d1==="[object Int16Array]")M0+=r2;else if(d1==="[object Uint16Array]")M0+=zB;else if(d1==="[object Int32Array]")M0+=C2;else if(d1==="[object Uint32Array]")M0+=f6;else if(d1==="[object Float32Array]")M0+=kA;else if(d1==="[object Float64Array]")M0+=I2;else t1(new Error("Failed to get type for BinaryArray"));t1(M0+o2(z0))}else if(d1==="[object Blob]"){var $0=new FileReader;$0.onload=function(){var AA=n1+U1.type+"~"+o2(this.result);t1(W0+d0+AA)},$0.readAsArrayBuffer(U1)}else try{t1(JSON.stringify(U1))}catch(AA){console.error("Couldn't convert value into a JSON string: ",U1),t1(null,AA)}}function l6(U1){if(U1.substring(0,O0)!==W0)return JSON.parse(U1);var t1=U1.substring(M2),d1=U1.substring(O0,M2),z0;if(d1===d0&&C0.test(t1)){var M0=t1.match(C0);z0=M0[1],t1=t1.substring(M0[0].length)}var $0=aA(t1);switch(d1){case zA:return $0;case d0:return X([$0],{type:z0});case YA:return new Int8Array($0);case w2:return new Uint8Array($0);case $2:return new Uint8ClampedArray($0);case r2:return new Int16Array($0);case zB:return new Uint16Array($0);case C2:return new Int32Array($0);case f6:return new Uint32Array($0);case kA:return new Float32Array($0);case I2:return new Float64Array($0);default:throw new Error("Unkown type: "+d1)}}var $3={serialize:fB,deserialize:l6,stringToBuffer:aA,bufferToString:o2};function rQ(U1,t1,d1,z0){U1.executeSql("CREATE TABLE IF NOT EXISTS "+t1.storeName+" (id INTEGER PRIMARY KEY, key unique, value)",[],d1,z0)}function tB(U1){var t1=this,d1={db:null};if(U1)for(var z0 in U1)d1[z0]=typeof U1[z0]!=="string"?U1[z0].toString():U1[z0];var M0=new V(function($0,AA){try{d1.db=openDatabase(d1.name,String(d1.version),d1.description,d1.size)}catch(UA){return AA(UA)}d1.db.transaction(function(UA){rQ(UA,d1,function(){t1._dbInfo=d1,$0()},function(VA,TA){AA(TA)})},AA)});return d1.serializer=$3,M0}function $6(U1,t1,d1,z0,M0,$0){U1.executeSql(d1,z0,M0,function(AA,UA){if(UA.code===UA.SYNTAX_ERR)AA.executeSql("SELECT name FROM sqlite_master WHERE type='table' AND name = ?",[t1.storeName],function(VA,TA){if(!TA.rows.length)rQ(VA,t1,function(){VA.executeSql(d1,z0,M0,$0)},$0);else $0(VA,UA)},$0);else $0(AA,UA)},$0)}function j8(U1,t1){var d1=this;U1=H(U1);var z0=new V(function(M0,$0){d1.ready().then(function(){var AA=d1._dbInfo;AA.db.transaction(function(UA){$6(UA,AA,"SELECT * FROM "+AA.storeName+" WHERE key = ? LIMIT 1",[U1],function(VA,TA){var uA=TA.rows.length?TA.rows.item(0).value:null;if(uA)uA=AA.serializer.deserialize(uA);M0(uA)},function(VA,TA){$0(TA)})})}).catch($0)});return C(z0,t1),z0}function R5(U1,t1){var d1=this,z0=new V(function(M0,$0){d1.ready().then(function(){var AA=d1._dbInfo;AA.db.transaction(function(UA){$6(UA,AA,"SELECT * FROM "+AA.storeName,[],function(VA,TA){var uA=TA.rows,W2=uA.length;for(var w9=0;w9<W2;w9++){var OA=uA.item(w9),e2=OA.value;if(e2)e2=AA.serializer.deserialize(e2);if(e2=U1(e2,OA.key,w9+1),e2!==void 0){M0(e2);return}}M0()},function(VA,TA){$0(TA)})})}).catch($0)});return C(z0,t1),z0}function p6(U1,t1,d1,z0){var M0=this;U1=H(U1);var $0=new V(function(AA,UA){M0.ready().then(function(){if(t1===void 0)t1=null;var VA=t1,TA=M0._dbInfo;TA.serializer.serialize(t1,function(uA,W2){if(W2)UA(W2);else TA.db.transaction(function(w9){$6(w9,TA,"INSERT OR REPLACE INTO "+TA.storeName+" (key, value) VALUES (?, ?)",[U1,uA],function(){AA(VA)},function(OA,e2){UA(e2)})},function(w9){if(w9.code===w9.QUOTA_ERR){if(z0>0){AA(p6.apply(M0,[U1,VA,d1,z0-1]));return}UA(w9)}})})}).catch(UA)});return C($0,d1),$0}function h5(U1,t1,d1){return p6.apply(this,[U1,t1,d1,1])}function $7(U1,t1){var d1=this;U1=H(U1);var z0=new V(function(M0,$0){d1.ready().then(function(){var AA=d1._dbInfo;AA.db.transaction(function(UA){$6(UA,AA,"DELETE FROM "+AA.storeName+" WHERE key = ?",[U1],function(){M0()},function(VA,TA){$0(TA)})})}).catch($0)});return C(z0,t1),z0}function l3(U1){var t1=this,d1=new V(function(z0,M0){t1.ready().then(function(){var $0=t1._dbInfo;$0.db.transaction(function(AA){$6(AA,$0,"DELETE FROM "+$0.storeName,[],function(){z0()},function(UA,VA){M0(VA)})})}).catch(M0)});return C(d1,U1),d1}function c7(U1){var t1=this,d1=new V(function(z0,M0){t1.ready().then(function(){var $0=t1._dbInfo;$0.db.transaction(function(AA){$6(AA,$0,"SELECT COUNT(key) as c FROM "+$0.storeName,[],function(UA,VA){var TA=VA.rows.item(0).c;z0(TA)},function(UA,VA){M0(VA)})})}).catch(M0)});return C(d1,U1),d1}function y4(U1,t1){var d1=this,z0=new V(function(M0,$0){d1.ready().then(function(){var AA=d1._dbInfo;AA.db.transaction(function(UA){$6(UA,AA,"SELECT key FROM "+AA.storeName+" WHERE id = ? LIMIT 1",[U1+1],function(VA,TA){var uA=TA.rows.length?TA.rows.item(0).key:null;M0(uA)},function(VA,TA){$0(TA)})})}).catch($0)});return C(z0,t1),z0}function q7(U1){var t1=this,d1=new V(function(z0,M0){t1.ready().then(function(){var $0=t1._dbInfo;$0.db.transaction(function(AA){$6(AA,$0,"SELECT key FROM "+$0.storeName,[],function(UA,VA){var TA=[];for(var uA=0;uA<VA.rows.length;uA++)TA.push(VA.rows.item(uA).key);z0(TA)},function(UA,VA){M0(VA)})})}).catch(M0)});return C(d1,U1),d1}function SZ(U1){return new V(function(t1,d1){U1.transaction(function(z0){z0.executeSql("SELECT name FROM sqlite_master WHERE type='table' AND name <> '__WebKitDatabaseInfoTable__'",[],function(M0,$0){var AA=[];for(var UA=0;UA<$0.rows.length;UA++)AA.push($0.rows.item(UA).name);t1({db:U1,storeNames:AA})},function(M0,$0){d1($0)})},function(z0){d1(z0)})})}function K2(U1,t1){t1=z.apply(this,arguments);var d1=this.config();if(U1=typeof U1!=="function"&&U1||{},!U1.name)U1.name=U1.name||d1.name,U1.storeName=U1.storeName||d1.storeName;var z0=this,M0;if(!U1.name)M0=V.reject("Invalid arguments");else M0=new V(function($0){var AA;if(U1.name===d1.name)AA=z0._dbInfo.db;else AA=openDatabase(U1.name,"","",0);if(!U1.storeName)$0(SZ(AA));else $0({db:AA,storeNames:[U1.storeName]})}).then(function($0){return new V(function(AA,UA){$0.db.transaction(function(VA){function TA(OA){return new V(function(e2,uB){VA.executeSql("DROP TABLE IF EXISTS "+OA,[],function(){e2()},function(m2,cQ){uB(cQ)})})}var uA=[];for(var W2=0,w9=$0.storeNames.length;W2<w9;W2++)uA.push(TA($0.storeNames[W2]));V.all(uA).then(function(){AA()}).catch(function(OA){UA(OA)})},function(VA){UA(VA)})})});return C(M0,t1),M0}var i1={_driver:"webSQLStorage",_initStorage:tB,_support:b1(),iterate:R5,getItem:j8,setItem:h5,removeItem:$7,clear:l3,length:c7,key:y4,keys:q7,dropInstance:K2};function N1(){try{return typeof localStorage!=="undefined"&&"setItem"in localStorage&&!!localStorage.setItem}catch(U1){return!1}}function Q0(U1,t1){var d1=U1.name+"/";if(U1.storeName!==t1.storeName)d1+=U1.storeName+"/";return d1}function h0(){var U1="_localforage_support_test";try{return localStorage.setItem(U1,!0),localStorage.removeItem(U1),!1}catch(t1){return!0}}function i0(){return!h0()||localStorage.length>0}function cA(U1){var t1=this,d1={};if(U1)for(var z0 in U1)d1[z0]=U1[z0];if(d1.keyPrefix=Q0(U1,t1._defaultConfig),!i0())return V.reject();return t1._dbInfo=d1,d1.serializer=$3,V.resolve()}function iB(U1){var t1=this,d1=t1.ready().then(function(){var z0=t1._dbInfo.keyPrefix;for(var M0=localStorage.length-1;M0>=0;M0--){var $0=localStorage.key(M0);if($0.indexOf(z0)===0)localStorage.removeItem($0)}});return C(d1,U1),d1}function h9(U1,t1){var d1=this;U1=H(U1);var z0=d1.ready().then(function(){var M0=d1._dbInfo,$0=localStorage.getItem(M0.keyPrefix+U1);if($0)$0=M0.serializer.deserialize($0);return $0});return C(z0,t1),z0}function BQ(U1,t1){var d1=this,z0=d1.ready().then(function(){var M0=d1._dbInfo,$0=M0.keyPrefix,AA=$0.length,UA=localStorage.length,VA=1;for(var TA=0;TA<UA;TA++){var uA=localStorage.key(TA);if(uA.indexOf($0)!==0)continue;var W2=localStorage.getItem(uA);if(W2)W2=M0.serializer.deserialize(W2);if(W2=U1(W2,uA.substring(AA),VA++),W2!==void 0)return W2}});return C(z0,t1),z0}function V4(U1,t1){var d1=this,z0=d1.ready().then(function(){var M0=d1._dbInfo,$0;try{$0=localStorage.key(U1)}catch(AA){$0=null}if($0)$0=$0.substring(M0.keyPrefix.length);return $0});return C(z0,t1),z0}function z9(U1){var t1=this,d1=t1.ready().then(function(){var z0=t1._dbInfo,M0=localStorage.length,$0=[];for(var AA=0;AA<M0;AA++){var UA=localStorage.key(AA);if(UA.indexOf(z0.keyPrefix)===0)$0.push(UA.substring(z0.keyPrefix.length))}return $0});return C(d1,U1),d1}function M4(U1){var t1=this,d1=t1.keys().then(function(z0){return z0.length});return C(d1,U1),d1}function R4(U1,t1){var d1=this;U1=H(U1);var z0=d1.ready().then(function(){var M0=d1._dbInfo;localStorage.removeItem(M0.keyPrefix+U1)});return C(z0,t1),z0}function dQ(U1,t1,d1){var z0=this;U1=H(U1);var M0=z0.ready().then(function(){if(t1===void 0)t1=null;var $0=t1;return new V(function(AA,UA){var VA=z0._dbInfo;VA.serializer.serialize(t1,function(TA,uA){if(uA)UA(uA);else try{localStorage.setItem(VA.keyPrefix+U1,TA),AA($0)}catch(W2){if(W2.name==="QuotaExceededError"||W2.name==="NS_ERROR_DOM_QUOTA_REACHED")UA(W2);UA(W2)}})})});return C(M0,d1),M0}function t2(U1,t1){if(t1=z.apply(this,arguments),U1=typeof U1!=="function"&&U1||{},!U1.name){var d1=this.config();U1.name=U1.name||d1.name,U1.storeName=U1.storeName||d1.storeName}var z0=this,M0;if(!U1.name)M0=V.reject("Invalid arguments");else M0=new V(function($0){if(!U1.storeName)$0(U1.name+"/");else $0(Q0(U1,z0._defaultConfig))}).then(function($0){for(var AA=localStorage.length-1;AA>=0;AA--){var UA=localStorage.key(AA);if(UA.indexOf($0)===0)localStorage.removeItem(UA)}});return C(M0,t1),M0}var QQ={_driver:"localStorageWrapper",_initStorage:cA,_support:N1(),iterate:BQ,getItem:h9,setItem:dQ,removeItem:R4,clear:iB,length:M4,key:V4,keys:z9,dropInstance:t2},y1=function U1(t1,d1){return t1===d1||typeof t1==="number"&&typeof d1==="number"&&isNaN(t1)&&isNaN(d1)},u1=function U1(t1,d1){var z0=t1.length,M0=0;while(M0<z0){if(y1(t1[M0],d1))return!0;M0++}return!1},N0=Array.isArray||function(U1){return Object.prototype.toString.call(U1)==="[object Array]"},x0={},w0={},v0={INDEXEDDB:B1,WEBSQL:i1,LOCALSTORAGE:QQ},HA=[v0.INDEXEDDB._driver,v0.WEBSQL._driver,v0.LOCALSTORAGE._driver],QA=["dropInstance"],WA=["clear","getItem","iterate","key","keys","length","removeItem","setItem"].concat(QA),e0={description:"",driver:HA.slice(),name:"localforage",size:4980736,storeName:"keyvaluepairs",version:1};function XA(U1,t1){U1[t1]=function(){var d1=arguments;return U1.ready().then(function(){return U1[t1].apply(U1,d1)})}}function hB(){for(var U1=1;U1<arguments.length;U1++){var t1=arguments[U1];if(t1){for(var d1 in t1)if(t1.hasOwnProperty(d1))if(N0(t1[d1]))arguments[0][d1]=t1[d1].slice();else arguments[0][d1]=t1[d1]}}return arguments[0]}var f2=function(){function U1(t1){I(this,U1);for(var d1 in v0)if(v0.hasOwnProperty(d1)){var z0=v0[d1],M0=z0._driver;if(this[d1]=M0,!x0[M0])this.defineDriver(z0)}this._defaultConfig=hB({},e0),this._config=hB({},this._defaultConfig,t1),this._driverSet=null,this._initDriver=null,this._ready=!1,this._dbInfo=null,this._wrapLibraryMethodsWithReady(),this.setDriver(this._config.driver).catch(function(){})}return U1.prototype.config=function t1(d1){if((typeof d1==="undefined"?"undefined":F(d1))==="object"){if(this._ready)return new Error("Can't call config() after localforage has been used.");for(var z0 in d1){if(z0==="storeName")d1[z0]=d1[z0].replace(/\W/g,"_");if(z0==="version"&&typeof d1[z0]!=="number")return new Error("Database version must be a number.");this._config[z0]=d1[z0]}if("driver"in d1&&d1.driver)return this.setDriver(this._config.driver);return!0}else if(typeof d1==="string")return this._config[d1];else return this._config},U1.prototype.defineDriver=function t1(d1,z0,M0){var $0=new V(function(AA,UA){try{var VA=d1._driver,TA=new Error("Custom driver not compliant; see https://mozilla.github.io/localForage/#definedriver");if(!d1._driver){UA(TA);return}var uA=WA.concat("_initStorage");for(var W2=0,w9=uA.length;W2<w9;W2++){var OA=uA[W2],e2=!u1(QA,OA);if((e2||d1[OA])&&typeof d1[OA]!=="function"){UA(TA);return}}var uB=function cQ(){var lQ=function _D(xD){return function(){var DQ=new Error("Method "+xD+" is not implemented by the current driver"),k4=V.reject(DQ);return C(k4,arguments[arguments.length-1]),k4}};for(var Q4=0,p3=QA.length;Q4<p3;Q4++){var Q5=QA[Q4];if(!d1[Q5])d1[Q5]=lQ(Q5)}};uB();var m2=function cQ(lQ){if(x0[VA])console.info("Redefining LocalForage driver: "+VA);x0[VA]=d1,w0[VA]=lQ,AA()};if("_support"in d1)if(d1._support&&typeof d1._support==="function")d1._support().then(m2,UA);else m2(!!d1._support);else m2(!0)}catch(cQ){UA(cQ)}});return K($0,z0,M0),$0},U1.prototype.driver=function t1(){return this._driver||null},U1.prototype.getDriver=function t1(d1,z0,M0){var $0=x0[d1]?V.resolve(x0[d1]):V.reject(new Error("Driver not found."));return K($0,z0,M0),$0},U1.prototype.getSerializer=function t1(d1){var z0=V.resolve($3);return K(z0,d1),z0},U1.prototype.ready=function t1(d1){var z0=this,M0=z0._driverSet.then(function(){if(z0._ready===null)z0._ready=z0._initDriver();return z0._ready});return K(M0,d1,d1),M0},U1.prototype.setDriver=function t1(d1,z0,M0){var $0=this;if(!N0(d1))d1=[d1];var AA=this._getSupportedDrivers(d1);function UA(){$0._config.driver=$0.driver()}function VA(W2){return $0._extend(W2),UA(),$0._ready=$0._initStorage($0._config),$0._ready}function TA(W2){return function(){var w9=0;function OA(){while(w9<W2.length){var e2=W2[w9];return w9++,$0._dbInfo=null,$0._ready=null,$0.getDriver(e2).then(VA).catch(OA)}UA();var uB=new Error("No available storage method found.");return $0._driverSet=V.reject(uB),$0._driverSet}return OA()}}var uA=this._driverSet!==null?this._driverSet.catch(function(){return V.resolve()}):V.resolve();return this._driverSet=uA.then(function(){var W2=AA[0];return $0._dbInfo=null,$0._ready=null,$0.getDriver(W2).then(function(w9){$0._driver=w9._driver,UA(),$0._wrapLibraryMethodsWithReady(),$0._initDriver=TA(AA)})}).catch(function(){UA();var W2=new Error("No available storage method found.");return $0._driverSet=V.reject(W2),$0._driverSet}),K(this._driverSet,z0,M0),this._driverSet},U1.prototype.supports=function t1(d1){return!!w0[d1]},U1.prototype._extend=function t1(d1){hB(this,d1)},U1.prototype._getSupportedDrivers=function t1(d1){var z0=[];for(var M0=0,$0=d1.length;M0<$0;M0++){var AA=d1[M0];if(this.supports(AA))z0.push(AA)}return z0},U1.prototype._wrapLibraryMethodsWithReady=function t1(){for(var d1=0,z0=WA.length;d1<z0;d1++)XA(this,WA[d1])},U1.prototype.createInstance=function t1(d1){return new U1(d1)},U1}(),gB=new f2;Z.exports=gB},{"3":3}]},{},[4])(4)})});
var ud0=E((Aw9)=>{/*! @sentry/node 7.120.3 (5a833b4) | https://github.com/getsentry/sentry-javascript */Aw9.base64WorkerScript="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"});
var uu0=E((gu0)=>{Object.defineProperty(gu0,"__esModule",{value:!0});var cZ=SQ(),dH=wA(),pj=ZV(),EK9=hc1(),_u0=Ml(),u21=ic1(),vu0=eJ1(),uC=hC(),bu0="BrowserTracing",UK9={...cZ.TRACING_DEFAULTS,instrumentNavigation:!0,instrumentPageLoad:!0,markBackgroundSpan:!0,enableLongTask:!0,enableInp:!1,interactionsSampleRate:1,_experiments:{},...vu0.defaultRequestInstrumentationOptions},wK9=(A={})=>{let B=pj.DEBUG_BUILD?!!(A.tracePropagationTargets||A.tracingOrigins):!1;if(cZ.addTracingExtensions(),!A.tracePropagationTargets&&A.tracingOrigins)A.tracePropagationTargets=A.tracingOrigins;let Q={...UK9,...A},D=u21.startTrackingWebVitals(),Z={};if(Q.enableInp)u21.startTrackingINP(Z,Q.interactionsSampleRate);if(Q.enableLongTask)u21.startTrackingLongTasks();if(Q._experiments.enableInteractions)u21.startTrackingInteractions();let G={name:void 0,context:void 0};function F(I){let Y=cZ.getCurrentHub(),{beforeStartSpan:W,idleTimeout:J,finalTimeout:X,heartbeatInterval:V}=Q,C=I.op==="pageload",K;if(C){let L=C?rc1("sentry-trace"):"",N=C?rc1("baggage"):void 0,{traceId:O,dsc:R,parentSpanId:T,sampled:j}=dH.propagationContextFromHeaders(L,N);K={traceId:O,parentSpanId:T,parentSampled:j,...I,metadata:{...I.metadata,dynamicSamplingContext:R},trimEnd:!0}}else K={trimEnd:!0,...I};let H=W?W(K):K;if(H.metadata=H.name!==K.name?{...H.metadata,source:"custom"}:H.metadata,G.name=H.name,G.context=H,H.sampled===!1)pj.DEBUG_BUILD&&dH.logger.log(`[Tracing] Will not send ${H.op} transaction because of beforeNavigate.`);pj.DEBUG_BUILD&&dH.logger.log(`[Tracing] Starting ${H.op} transaction on scope`);let{location:z}=uC.WINDOW,$=cZ.startIdleTransaction(Y,H,J,X,!0,{location:z},V,C);if(C&&uC.WINDOW.document){if(uC.WINDOW.document.addEventListener("readystatechange",()=>{if(["interactive","complete"].includes(uC.WINDOW.document.readyState))$.sendAutoFinishSignal()}),["interactive","complete"].includes(uC.WINDOW.document.readyState))$.sendAutoFinishSignal()}return $.registerBeforeFinishCallback((L)=>{D(),u21.addPerformanceEntries(L)}),$}return{name:bu0,setupOnce:()=>{},afterAllSetup(I){let Y=I.getOptions(),{markBackgroundSpan:W,traceFetch:J,traceXHR:X,shouldCreateSpanForRequest:V,enableHTTPTimings:C,_experiments:K}=Q,H=Y&&Y.tracePropagationTargets,z=H||Q.tracePropagationTargets;if(pj.DEBUG_BUILD&&B&&H)dH.logger.warn("[Tracing] The `tracePropagationTargets` option was set in the BrowserTracing integration and top level `Sentry.init`. The top level `Sentry.init` value is being used.");let $,L=uC.WINDOW.location&&uC.WINDOW.location.href;if(I.on)I.on("startNavigationSpan",(N)=>{if($)pj.DEBUG_BUILD&&dH.logger.log(`[Tracing] Finishing current transaction with op: ${cZ.spanToJSON($).op}`),$.end();$=F({op:"navigation",...N})}),I.on("startPageLoadSpan",(N)=>{if($)pj.DEBUG_BUILD&&dH.logger.log(`[Tracing] Finishing current transaction with op: ${cZ.spanToJSON($).op}`),$.end();$=F({op:"pageload",...N})});if(Q.instrumentPageLoad&&I.emit&&uC.WINDOW.location){let N={name:uC.WINDOW.location.pathname,startTimestamp:dH.browserPerformanceTimeOrigin?dH.browserPerformanceTimeOrigin/1000:void 0,origin:"auto.pageload.browser",attributes:{[cZ.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]:"url"}};fu0(I,N)}if(Q.instrumentNavigation&&I.emit&&uC.WINDOW.location)dH.addHistoryInstrumentationHandler(({to:N,from:O})=>{if(O===void 0&&L&&L.indexOf(N)!==-1){L=void 0;return}if(O!==N){L=void 0;let R={name:uC.WINDOW.location.pathname,origin:"auto.navigation.browser",attributes:{[cZ.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]:"url"}};hu0(I,R)}});if(W)EK9.registerBackgroundTabDetection();if(K.enableInteractions)$K9(Q,G);if(Q.enableInp)NK9(Z,G);vu0.instrumentOutgoingRequests({traceFetch:J,traceXHR:X,tracePropagationTargets:z,shouldCreateSpanForRequest:V,enableHTTPTimings:C})},options:Q}};function fu0(A,B){if(!A.emit)return;A.emit("startPageLoadSpan",B);let Q=cZ.getActiveSpan();return(Q&&cZ.spanToJSON(Q).op)==="pageload"?Q:void 0}function hu0(A,B){if(!A.emit)return;A.emit("startNavigationSpan",B);let Q=cZ.getActiveSpan();return(Q&&cZ.spanToJSON(Q).op)==="navigation"?Q:void 0}function rc1(A){let B=dH.getDomElement(`meta[name=${A}]`);return B?B.getAttribute("content"):void 0}function $K9(A,B){let Q,D=()=>{let{idleTimeout:Z,finalTimeout:G,heartbeatInterval:F}=A,I="ui.action.click",Y=cZ.getActiveTransaction();if(Y&&Y.op&&["navigation","pageload"].includes(Y.op)){pj.DEBUG_BUILD&&dH.logger.warn("[Tracing] Did not create ui.action.click transaction because a pageload or navigation transaction is in progress.");return}if(Q)Q.setFinishReason("interactionInterrupted"),Q.end(),Q=void 0;if(!B.name){pj.DEBUG_BUILD&&dH.logger.warn("[Tracing] Did not create ui.action.click transaction because _latestRouteName is missing.");return}let{location:W}=uC.WINDOW,J={name:B.name,op:"ui.action.click",trimEnd:!0,data:{[cZ.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]:B.context?LK9(B.context):"url"}};Q=cZ.startIdleTransaction(cZ.getCurrentHub(),J,Z,G,!0,{location:W},F)};["click"].forEach((Z)=>{if(uC.WINDOW.document)addEventListener(Z,D,{once:!1,capture:!0})})}function qK9(A){return"duration"in A}var xu0=10;function NK9(A,B){let Q=({entries:D})=>{let Z=cZ.getClient(),G=Z!==void 0&&Z.getIntegrationByName!==void 0?Z.getIntegrationByName("Replay"):void 0,F=G!==void 0?G.getReplayId():void 0,I=cZ.getActiveTransaction(),Y=cZ.getCurrentScope(),W=Y!==void 0?Y.getUser():void 0;D.forEach((J)=>{if(qK9(J)){let X=J.interactionId;if(X===void 0)return;let V=A[X],C=J.duration,K=J.startTime,H=Object.keys(A),z=H.length>0?H.reduce(($,L)=>{return A[$].duration<A[L].duration?$:L}):void 0;if(J.entryType==="first-input"){if(H.map((L)=>A[L]).some((L)=>{return L.duration===C&&L.startTime===K}))return}if(!X)return;if(V)V.duration=Math.max(V.duration,C);else if(H.length<xu0||z===void 0||C>A[z].duration){let{name:$,context:L}=B;if($&&L){if(z&&Object.keys(A).length>=xu0)delete A[z];A[X]={routeName:$,duration:C,parentContext:L,user:W,activeTransaction:I,replayId:F,startTime:K}}}}})};_u0.addPerformanceInstrumentationHandler("event",Q),_u0.addPerformanceInstrumentationHandler("first-input",Q)}function LK9(A){let B=A.attributes&&A.attributes[cZ.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE],Q=A.data&&A.data[cZ.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE],D=A.metadata&&A.metadata.source;return B||Q||D}gu0.BROWSER_TRACING_INTEGRATION_ID=bu0;gu0.browserTracingIntegration=wK9;gu0.getMetaContent=rc1;gu0.startBrowserTracingNavigationSpan=hu0;gu0.startBrowserTracingPageLoadSpan=fu0});
var vg0=E((xg0)=>{Object.defineProperty(xg0,"__esModule",{value:!0});var rX9=$l(),oX9=ql(),tX9=Hf(),eX9=Nl(),AV9=(A,B={})=>{let Q=oX9.initMetric("CLS",0),D,Z=0,G=[],F=(Y)=>{Y.forEach((W)=>{if(!W.hadRecentInput){let J=G[0],X=G[G.length-1];if(Z&&G.length!==0&&W.startTime-X.startTime<1000&&W.startTime-J.startTime<5000)Z+=W.value,G.push(W);else Z=W.value,G=[W];if(Z>Q.value){if(Q.value=Z,Q.entries=G,D)D()}}})},I=tX9.observe("layout-shift",F);if(I){D=rX9.bindReporter(A,Q,B.reportAllChanges);let Y=()=>{F(I.takeRecords()),D(!0)};return eX9.onHidden(Y),Y}return};xg0.onCLS=AV9});
var wA=E((bd1)=>{Object.defineProperty(bd1,"__esModule",{value:!0});var uQ9=lk0(),sW1=xm1(),yd1=hm1(),mQ9=gm1(),kd1=kW(),dQ9=p_0(),_W=hH(),cQ9=r_0(),rW1=oU(),lQ9=Cd1(),hj=D21(),_d1=Vd1(),oW1=I21(),ZO=gH(),Gf=Ix0(),pQ9=Jx0(),Ff=wx0(),xd1=Lx0(),W21=_W1(),J21=B21(),gj=tm1(),vd1=zd1(),X21=$d1(),V21=bx0(),wv0=Xd1(),tq=Rd1(),iQ9=mx0(),C21=ix0(),Zl=Nd1(),tW1=Ud1(),nQ9=rx0(),aQ9=tx0(),eW1=Qv0(),$v0=Zv0(),sQ9=Iv0(),rQ9=Jv0(),oQ9=Sd1(),tQ9=Cv0(),eQ9=Pd1(),A49=jd1(),B49=zv0(),Q49=lm1(),D49=sm1(),qv0=Wd1(),Z49=Ad1(),G49=Yd1(),F49=Dd1(),I49=Fd1(),Y49=QO(),W49=kW1(),J49=Uv0(),X49=Id1();bd1.applyAggregateErrorsToEvent=uQ9.applyAggregateErrorsToEvent;bd1.getComponentName=sW1.getComponentName;bd1.getDomElement=sW1.getDomElement;bd1.getLocationHref=sW1.getLocationHref;bd1.htmlTreeAsString=sW1.htmlTreeAsString;bd1.dsnFromString=yd1.dsnFromString;bd1.dsnToString=yd1.dsnToString;bd1.makeDsn=yd1.makeDsn;bd1.SentryError=mQ9.SentryError;bd1.GLOBAL_OBJ=kd1.GLOBAL_OBJ;bd1.getGlobalObject=kd1.getGlobalObject;bd1.getGlobalSingleton=kd1.getGlobalSingleton;bd1.addInstrumentationHandler=dQ9.addInstrumentationHandler;bd1.isDOMError=_W.isDOMError;bd1.isDOMException=_W.isDOMException;bd1.isElement=_W.isElement;bd1.isError=_W.isError;bd1.isErrorEvent=_W.isErrorEvent;bd1.isEvent=_W.isEvent;bd1.isInstanceOf=_W.isInstanceOf;bd1.isNaN=_W.isNaN;bd1.isParameterizedString=_W.isParameterizedString;bd1.isPlainObject=_W.isPlainObject;bd1.isPrimitive=_W.isPrimitive;bd1.isRegExp=_W.isRegExp;bd1.isString=_W.isString;bd1.isSyntheticEvent=_W.isSyntheticEvent;bd1.isThenable=_W.isThenable;bd1.isVueViewModel=_W.isVueViewModel;bd1.isBrowser=cQ9.isBrowser;bd1.CONSOLE_LEVELS=rW1.CONSOLE_LEVELS;bd1.consoleSandbox=rW1.consoleSandbox;bd1.logger=rW1.logger;bd1.originalConsoleMethods=rW1.originalConsoleMethods;bd1.memoBuilder=lQ9.memoBuilder;bd1.addContextToFrame=hj.addContextToFrame;bd1.addExceptionMechanism=hj.addExceptionMechanism;bd1.addExceptionTypeValue=hj.addExceptionTypeValue;bd1.arrayify=hj.arrayify;bd1.checkOrSetAlreadyCaught=hj.checkOrSetAlreadyCaught;bd1.getEventDescription=hj.getEventDescription;bd1.parseSemver=hj.parseSemver;bd1.uuid4=hj.uuid4;bd1.dynamicRequire=_d1.dynamicRequire;bd1.isNodeEnv=_d1.isNodeEnv;bd1.loadModule=_d1.loadModule;bd1.normalize=oW1.normalize;bd1.normalizeToSize=oW1.normalizeToSize;bd1.normalizeUrlToBase=oW1.normalizeUrlToBase;bd1.walk=oW1.walk;bd1.addNonEnumerableProperty=ZO.addNonEnumerableProperty;bd1.convertToPlainObject=ZO.convertToPlainObject;bd1.dropUndefinedKeys=ZO.dropUndefinedKeys;bd1.extractExceptionKeysForMessage=ZO.extractExceptionKeysForMessage;bd1.fill=ZO.fill;bd1.getOriginalFunction=ZO.getOriginalFunction;bd1.markFunctionWrapped=ZO.markFunctionWrapped;bd1.objectify=ZO.objectify;bd1.urlEncode=ZO.urlEncode;bd1.basename=Gf.basename;bd1.dirname=Gf.dirname;bd1.isAbsolute=Gf.isAbsolute;bd1.join=Gf.join;bd1.normalizePath=Gf.normalizePath;bd1.relative=Gf.relative;bd1.resolve=Gf.resolve;bd1.makePromiseBuffer=pQ9.makePromiseBuffer;bd1.DEFAULT_USER_INCLUDES=Ff.DEFAULT_USER_INCLUDES;bd1.addRequestDataToEvent=Ff.addRequestDataToEvent;bd1.addRequestDataToTransaction=Ff.addRequestDataToTransaction;bd1.extractPathForTransaction=Ff.extractPathForTransaction;bd1.extractRequestData=Ff.extractRequestData;bd1.winterCGHeadersToDict=Ff.winterCGHeadersToDict;bd1.winterCGRequestToRequestData=Ff.winterCGRequestToRequestData;bd1.severityFromString=xd1.severityFromString;bd1.severityLevelFromString=xd1.severityLevelFromString;bd1.validSeverityLevels=xd1.validSeverityLevels;bd1.createStackParser=W21.createStackParser;bd1.getFunctionName=W21.getFunctionName;bd1.nodeStackLineParser=W21.nodeStackLineParser;bd1.stackParserFromStackParserOptions=W21.stackParserFromStackParserOptions;bd1.stripSentryFramesAndReverse=W21.stripSentryFramesAndReverse;bd1.isMatchingPattern=J21.isMatchingPattern;bd1.safeJoin=J21.safeJoin;bd1.snipLine=J21.snipLine;bd1.stringMatchesSomePattern=J21.stringMatchesSomePattern;bd1.truncate=J21.truncate;bd1.isNativeFetch=gj.isNativeFetch;bd1.supportsDOMError=gj.supportsDOMError;bd1.supportsDOMException=gj.supportsDOMException;bd1.supportsErrorEvent=gj.supportsErrorEvent;bd1.supportsFetch=gj.supportsFetch;bd1.supportsNativeFetch=gj.supportsNativeFetch;bd1.supportsReferrerPolicy=gj.supportsReferrerPolicy;bd1.supportsReportingObserver=gj.supportsReportingObserver;bd1.SyncPromise=vd1.SyncPromise;bd1.rejectedSyncPromise=vd1.rejectedSyncPromise;bd1.resolvedSyncPromise=vd1.resolvedSyncPromise;Object.defineProperty(bd1,"_browserPerformanceTimeOriginMode",{enumerable:!0,get:()=>X21._browserPerformanceTimeOriginMode});bd1.browserPerformanceTimeOrigin=X21.browserPerformanceTimeOrigin;bd1.dateTimestampInSeconds=X21.dateTimestampInSeconds;bd1.timestampInSeconds=X21.timestampInSeconds;bd1.timestampWithMs=X21.timestampWithMs;bd1.TRACEPARENT_REGEXP=V21.TRACEPARENT_REGEXP;bd1.extractTraceparentData=V21.extractTraceparentData;bd1.generateSentryTraceHeader=V21.generateSentryTraceHeader;bd1.propagationContextFromHeaders=V21.propagationContextFromHeaders;bd1.tracingContextFromHeaders=V21.tracingContextFromHeaders;bd1.getSDKSource=wv0.getSDKSource;bd1.isBrowserBundle=wv0.isBrowserBundle;bd1.addItemToEnvelope=tq.addItemToEnvelope;bd1.createAttachmentEnvelopeItem=tq.createAttachmentEnvelopeItem;bd1.createEnvelope=tq.createEnvelope;bd1.createEventEnvelopeHeaders=tq.createEventEnvelopeHeaders;bd1.envelopeContainsItemType=tq.envelopeContainsItemType;bd1.envelopeItemTypeToDataCategory=tq.envelopeItemTypeToDataCategory;bd1.forEachEnvelopeItem=tq.forEachEnvelopeItem;bd1.getSdkMetadataForEnvelopeHeader=tq.getSdkMetadataForEnvelopeHeader;bd1.parseEnvelope=tq.parseEnvelope;bd1.serializeEnvelope=tq.serializeEnvelope;bd1.createClientReportEnvelope=iQ9.createClientReportEnvelope;bd1.DEFAULT_RETRY_AFTER=C21.DEFAULT_RETRY_AFTER;bd1.disabledUntil=C21.disabledUntil;bd1.isRateLimited=C21.isRateLimited;bd1.parseRetryAfterHeader=C21.parseRetryAfterHeader;bd1.updateRateLimits=C21.updateRateLimits;bd1.BAGGAGE_HEADER_NAME=Zl.BAGGAGE_HEADER_NAME;bd1.MAX_BAGGAGE_STRING_LENGTH=Zl.MAX_BAGGAGE_STRING_LENGTH;bd1.SENTRY_BAGGAGE_KEY_PREFIX=Zl.SENTRY_BAGGAGE_KEY_PREFIX;bd1.SENTRY_BAGGAGE_KEY_PREFIX_REGEX=Zl.SENTRY_BAGGAGE_KEY_PREFIX_REGEX;bd1.baggageHeaderToDynamicSamplingContext=Zl.baggageHeaderToDynamicSamplingContext;bd1.dynamicSamplingContextToSentryBaggageHeader=Zl.dynamicSamplingContextToSentryBaggageHeader;bd1.getNumberOfUrlSegments=tW1.getNumberOfUrlSegments;bd1.getSanitizedUrlString=tW1.getSanitizedUrlString;bd1.parseUrl=tW1.parseUrl;bd1.stripUrlQueryAndFragment=tW1.stripUrlQueryAndFragment;bd1.addOrUpdateIntegration=nQ9.addOrUpdateIntegration;bd1.makeFifoCache=aQ9.makeFifoCache;bd1.eventFromMessage=eW1.eventFromMessage;bd1.eventFromUnknownInput=eW1.eventFromUnknownInput;bd1.exceptionFromError=eW1.exceptionFromError;bd1.parseStackFrames=eW1.parseStackFrames;bd1.callFrameToStackFrame=$v0.callFrameToStackFrame;bd1.watchdogTimer=$v0.watchdogTimer;bd1.LRUMap=sQ9.LRUMap;bd1._asyncNullishCoalesce=rQ9._asyncNullishCoalesce;bd1._asyncOptionalChain=oQ9._asyncOptionalChain;bd1._asyncOptionalChainDelete=tQ9._asyncOptionalChainDelete;bd1._nullishCoalesce=eQ9._nullishCoalesce;bd1._optionalChain=A49._optionalChain;bd1._optionalChainDelete=B49._optionalChainDelete;bd1.addConsoleInstrumentationHandler=Q49.addConsoleInstrumentationHandler;bd1.addClickKeypressInstrumentationHandler=D49.addClickKeypressInstrumentationHandler;bd1.SENTRY_XHR_DATA_KEY=qv0.SENTRY_XHR_DATA_KEY;bd1.addXhrInstrumentationHandler=qv0.addXhrInstrumentationHandler;bd1.addFetchInstrumentationHandler=Z49.addFetchInstrumentationHandler;bd1.addHistoryInstrumentationHandler=G49.addHistoryInstrumentationHandler;bd1.addGlobalErrorInstrumentationHandler=F49.addGlobalErrorInstrumentationHandler;bd1.addGlobalUnhandledRejectionInstrumentationHandler=I49.addGlobalUnhandledRejectionInstrumentationHandler;bd1.resetInstrumentationHandlers=Y49.resetInstrumentationHandlers;bd1.filenameIsInApp=W49.filenameIsInApp;bd1.escapeStringForRegex=J49.escapeStringForRegex;bd1.supportsHistory=X49.supportsHistory});
var wJ1=E((Mb0)=>{Object.defineProperty(Mb0,"__esModule",{value:!0});var Cl=wA(),EJ1=kG(),aD9=eq(),sD9=N21(),M21=L21(),UJ1=BV(),qb0=Yf(),Nb0=zJ1(),rD9=HJ1();class Lb0 extends Nb0.Span{constructor(A,B){super(A);this._contexts={},this._hub=B||aD9.getCurrentHub(),this._name=A.name||"",this._metadata={...A.metadata},this._trimEnd=A.trimEnd,this.transaction=this;let Q=this._metadata.dynamicSamplingContext;if(Q)this._frozenDynamicSamplingContext={...Q}}get name(){return this._name}set name(A){this.setName(A)}get metadata(){return{source:"custom",spanMetadata:{},...this._metadata,...this._attributes[M21.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]&&{source:this._attributes[M21.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]},...this._attributes[M21.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE]&&{sampleRate:this._attributes[M21.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE]}}}set metadata(A){this._metadata=A}setName(A,B="custom"){this._name=A,this.setAttribute(M21.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,B)}updateName(A){return this._name=A,this}initSpanRecorder(A=1000){if(!this.spanRecorder)this.spanRecorder=new Nb0.SpanRecorder(A);this.spanRecorder.add(this)}setContext(A,B){if(B===null)delete this._contexts[A];else this._contexts[A]=B}setMeasurement(A,B,Q=""){this._measurements[A]={value:B,unit:Q}}setMetadata(A){this._metadata={...this._metadata,...A}}end(A){let B=UJ1.spanTimeInputToSeconds(A),Q=this._finishTransaction(B);if(!Q)return;return this._hub.captureEvent(Q)}toContext(){let A=super.toContext();return Cl.dropUndefinedKeys({...A,name:this._name,trimEnd:this._trimEnd})}updateWithContext(A){return super.updateWithContext(A),this._name=A.name||"",this._trimEnd=A.trimEnd,this}getDynamicSamplingContext(){return qb0.getDynamicSamplingContextFromSpan(this)}setHub(A){this._hub=A}getProfileId(){if(this._contexts!==void 0&&this._contexts.profile!==void 0)return this._contexts.profile.profile_id;return}_finishTransaction(A){if(this._endTime!==void 0)return;if(!this._name)EJ1.DEBUG_BUILD&&Cl.logger.warn("Transaction has no name, falling back to `<unlabeled transaction>`."),this._name="<unlabeled transaction>";super.end(A);let B=this._hub.getClient();if(B&&B.emit)B.emit("finishTransaction",this);if(this._sampled!==!0){if(EJ1.DEBUG_BUILD&&Cl.logger.log("[Tracing] Discarding transaction because its trace was not chosen to be sampled."),B)B.recordDroppedEvent("sample_rate","transaction");return}let Q=this.spanRecorder?this.spanRecorder.spans.filter((W)=>W!==this&&UJ1.spanToJSON(W).timestamp):[];if(this._trimEnd&&Q.length>0){let W=Q.map((J)=>UJ1.spanToJSON(J).timestamp).filter(Boolean);this._endTime=W.reduce((J,X)=>{return J>X?J:X})}let{scope:D,isolationScope:Z}=rD9.getCapturedScopesOnSpan(this),{metadata:G}=this,{source:F}=G,I={contexts:{...this._contexts,trace:UJ1.spanToTraceContext(this)},spans:Q,start_timestamp:this._startTime,tags:this.tags,timestamp:this._endTime,transaction:this._name,type:"transaction",sdkProcessingMetadata:{...G,capturedSpanScope:D,capturedSpanIsolationScope:Z,...Cl.dropUndefinedKeys({dynamicSamplingContext:qb0.getDynamicSamplingContextFromSpan(this)})},_metrics_summary:sD9.getMetricSummaryJsonForSpan(this),...F&&{transaction_info:{source:F}}};if(Object.keys(this._measurements).length>0)EJ1.DEBUG_BUILD&&Cl.logger.log("[Measurements] Adding measurements to transaction",JSON.stringify(this._measurements,void 0,2)),I.measurements=this._measurements;return EJ1.DEBUG_BUILD&&Cl.logger.log(`[Tracing] Finishing ${this.op} transaction: ${this._name}.`),I}}Mb0.Transaction=Lb0});
var wf=E((Km0)=>{Object.defineProperty(Km0,"__esModule",{value:!0});var oH9=wA(),tH9=oH9.parseSemver(process.versions.node);Km0.NODE_VERSION=tH9});
var wf0=E((Uf0)=>{Object.defineProperty(Uf0,"__esModule",{value:!0});var $c1=wA(),SF9=kG(),Ef0=100,qc1=5000,jF9=3600000;function wc1(A,B){SF9.DEBUG_BUILD&&$c1.logger.info(`[Offline]: ${A}`,B)}function yF9(A){return(B)=>{let Q=A(B),D=B.createStore?B.createStore(B):void 0,Z=qc1,G;function F(J,X,V){if($c1.envelopeContainsItemType(J,["replay_event","replay_recording","client_report"]))return!1;if(B.shouldStore)return B.shouldStore(J,X,V);return!0}function I(J){if(!D)return;if(G)clearTimeout(G);if(G=setTimeout(async()=>{G=void 0;let X=await D.pop();if(X)wc1("Attempting to send previously queued event"),W(X).catch((V)=>{wc1("Failed to retry sending",V)})},J),typeof G!=="number"&&G.unref)G.unref()}function Y(){if(G)return;I(Z),Z=Math.min(Z*2,jF9)}async function W(J){try{let X=await Q.send(J),V=Ef0;if(X){if(X.headers&&X.headers["retry-after"])V=$c1.parseRetryAfterHeader(X.headers["retry-after"]);else if((X.statusCode||0)>=400)return X}return I(V),Z=qc1,X}catch(X){if(D&&await F(J,X,Z))return await D.insert(J),Y(),wc1("Error sending. Event queued",X),{};else throw X}}if(B.flushAtStartup)Y();return{send:W,flush:(J)=>Q.flush(J)}}}Uf0.MIN_DELAY=Ef0;Uf0.START_DELAY=qc1;Uf0.makeOfflineTransport=yF9});
var wl1=E((rd0)=>{var{_optionalChain:ad0}=wA();Object.defineProperty(rd0,"__esModule",{value:!0});var Sl=SQ(),sd0=wA();function $w9(A={}){return function({path:B,type:Q,next:D,rawInput:Z}){let G=ad0([Sl.getClient,"call",(W)=>W(),"optionalAccess",(W)=>W.getOptions,"call",(W)=>W()]),F=Sl.getCurrentScope().getTransaction();if(F){F.updateName(`trpc/${B}`),F.setAttribute(Sl.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,"route"),F.op="rpc.server";let W={procedure_type:Q};if(A.attachRpcInput!==void 0?A.attachRpcInput:ad0([G,"optionalAccess",(J)=>J.sendDefaultPii]))W.input=sd0.normalize(Z);F.setContext("trpc",W)}function I(W){if(!W.ok)Sl.captureException(W.error,{mechanism:{handled:!1,data:{function:"trpcMiddleware"}}})}let Y;try{Y=D()}catch(W){throw Sl.captureException(W,{mechanism:{handled:!1,data:{function:"trpcMiddleware"}}}),W}if(sd0.isThenable(Y))Promise.resolve(Y).then((W)=>{I(W)},(W)=>{Sl.captureException(W,{mechanism:{handled:!1,data:{function:"trpcMiddleware"}}})});else I(Y);return Y}}rd0.trpcMiddleware=$w9});
var wx0=E((Ux0)=>{Object.defineProperty(Ux0,"__esModule",{value:!0});var RB9=Vx0(),OB9=rq(),Kx0=hH(),TB9=oU(),PB9=I21(),SB9=Ud1(),jB9={ip:!1,request:!0,transaction:!0,user:!0},yB9=["cookies","data","headers","method","query_string","url"],Hx0=["id","username","email"];function kB9(A,B,Q){if(!A)return;if(!A.metadata.source||A.metadata.source==="url"){let[D,Z]=aW1(B,{path:!0,method:!0});A.updateName(D),A.setMetadata({source:Z})}if(A.setAttribute("url",B.originalUrl||B.url),B.baseUrl)A.setAttribute("baseUrl",B.baseUrl);A.setData("query",zx0(B,Q))}function aW1(A,B={}){let Q=A.method&&A.method.toUpperCase(),D="",Z="url";if(B.customRoute||A.route)D=B.customRoute||`${A.baseUrl||""}${A.route&&A.route.path}`,Z="route";else if(A.originalUrl||A.url)D=SB9.stripUrlQueryAndFragment(A.originalUrl||A.url||"");let G="";if(B.method&&Q)G+=Q;if(B.method&&B.path)G+=" ";if(B.path&&D)G+=D;return[G,Z]}function _B9(A,B){switch(B){case"path":return aW1(A,{path:!0})[0];case"handler":return A.route&&A.route.stack&&A.route.stack[0]&&A.route.stack[0].name||"<anonymous>";case"methodPath":default:{let Q=A._reconstructedRoute?A._reconstructedRoute:void 0;return aW1(A,{path:!0,method:!0,customRoute:Q})[0]}}}function xB9(A,B){let Q={};return(Array.isArray(B)?B:Hx0).forEach((Z)=>{if(A&&Z in A)Q[Z]=A[Z]}),Q}function wd1(A,B){let{include:Q=yB9,deps:D}=B||{},Z={},G=A.headers||{},F=A.method,I=G.host||A.hostname||A.host||"<no host>",Y=A.protocol==="https"||A.socket&&A.socket.encrypted?"https":"http",W=A.originalUrl||A.url||"",J=W.startsWith(Y)?W:`${Y}://${I}${W}`;return Q.forEach((X)=>{switch(X){case"headers":{if(Z.headers=G,!Q.includes("cookies"))delete Z.headers.cookie;break}case"method":{Z.method=F;break}case"url":{Z.url=J;break}case"cookies":{Z.cookies=A.cookies||G.cookie&&RB9.parseCookie(G.cookie)||{};break}case"query_string":{Z.query_string=zx0(A,D);break}case"data":{if(F==="GET"||F==="HEAD")break;if(A.body!==void 0)Z.data=Kx0.isString(A.body)?A.body:JSON.stringify(PB9.normalize(A.body));break}default:if({}.hasOwnProperty.call(A,X))Z[X]=A[X]}}),Z}function vB9(A,B,Q){let D={...jB9,...Q&&Q.include};if(D.request){let Z=Array.isArray(D.request)?wd1(B,{include:D.request,deps:Q&&Q.deps}):wd1(B,{deps:Q&&Q.deps});A.request={...A.request,...Z}}if(D.user){let Z=B.user&&Kx0.isPlainObject(B.user)?xB9(B.user,D.user):{};if(Object.keys(Z).length)A.user={...A.user,...Z}}if(D.ip){let Z=B.ip||B.socket&&B.socket.remoteAddress;if(Z)A.user={...A.user,ip_address:Z}}if(D.transaction&&!A.transaction)A.transaction=_B9(B,D.transaction);return A}function zx0(A,B){let Q=A.originalUrl||A.url||"";if(!Q)return;if(Q.startsWith("/"))Q=`http://dogs.are.great${Q}`;try{return A.query||typeof URL!=="undefined"&&new URL(Q).search.slice(1)||B&&B.url&&B.url.parse(Q).query||void 0}catch(D){return}}function Ex0(A){let B={};try{A.forEach((Q,D)=>{if(typeof Q==="string")B[D]=Q})}catch(Q){OB9.DEBUG_BUILD&&TB9.logger.warn("Sentry failed extracting headers from a request object. If you see this, please file an issue.")}return B}function bB9(A){let B=Ex0(A.headers);return{method:A.method,url:A.url,headers:B}}Ux0.DEFAULT_USER_INCLUDES=Hx0;Ux0.addRequestDataToEvent=vB9;Ux0.addRequestDataToTransaction=kB9;Ux0.extractPathForTransaction=aW1;Ux0.extractRequestData=wd1;Ux0.winterCGHeadersToDict=Ex0;Ux0.winterCGRequestToRequestData=bB9});
var xm1=E((ik0)=>{Object.defineProperty(ik0,"__esModule",{value:!0});var $19=hH(),q19=kW(),Al=q19.getGlobalObject(),N19=80;function L19(A,B={}){if(!A)return"<unknown>";try{let Q=A,D=5,Z=[],G=0,F=0,I=" > ",Y=I.length,W,J=Array.isArray(B)?B:B.keyAttrs,X=!Array.isArray(B)&&B.maxStringLength||N19;while(Q&&G++<D){if(W=M19(Q,J),W==="html"||G>1&&F+Z.length*Y+W.length>=X)break;Z.push(W),F+=W.length,Q=Q.parentNode}return Z.reverse().join(I)}catch(Q){return"<unknown>"}}function M19(A,B){let Q=A,D=[],Z,G,F,I,Y;if(!Q||!Q.tagName)return"";if(Al.HTMLElement){if(Q instanceof HTMLElement&&Q.dataset&&Q.dataset.sentryComponent)return Q.dataset.sentryComponent}D.push(Q.tagName.toLowerCase());let W=B&&B.length?B.filter((X)=>Q.getAttribute(X)).map((X)=>[X,Q.getAttribute(X)]):null;if(W&&W.length)W.forEach((X)=>{D.push(`[${X[0]}="${X[1]}"]`)});else{if(Q.id)D.push(`#${Q.id}`);if(Z=Q.className,Z&&$19.isString(Z)){G=Z.split(/\s+/);for(Y=0;Y<G.length;Y++)D.push(`.${G[Y]}`)}}let J=["aria-label","type","name","title","alt"];for(Y=0;Y<J.length;Y++)if(F=J[Y],I=Q.getAttribute(F),I)D.push(`[${F}="${I}"]`);return D.join("")}function R19(){try{return Al.document.location.href}catch(A){return""}}function O19(A){if(Al.document&&Al.document.querySelector)return Al.document.querySelector(A);return null}function T19(A){if(!Al.HTMLElement)return null;let B=A,Q=5;for(let D=0;D<Q;D++){if(!B)return null;if(B instanceof HTMLElement&&B.dataset.sentryComponent)return B.dataset.sentryComponent;B=B.parentNode}return null}ik0.getComponentName=T19;ik0.getDomElement=O19;ik0.getLocationHref=R19;ik0.htmlTreeAsString=L19});
var yh0=E((jh0)=>{Object.defineProperty(jh0,"__esModule",{value:!0});var Nh0=wA(),Lh0=kG(),Mh0=mH(),GY9=BV(),yJ1=O21(),Rh0=qh0();function kJ1(A,B,Q,D={}){let Z=Mh0.getClient(),G=Mh0.getCurrentScope();if(Z){if(!Z.metricsAggregator){Lh0.DEBUG_BUILD&&Nh0.logger.warn("No metrics aggregator enabled. Please add the MetricsAggregator integration to use metrics APIs");return}let{unit:F,tags:I,timestamp:Y}=D,{release:W,environment:J}=Z.getOptions(),X=G.getTransaction(),V={};if(W)V.release=W;if(J)V.environment=J;if(X)V.transaction=GY9.spanToJSON(X).description||"";Lh0.DEBUG_BUILD&&Nh0.logger.log(`Adding value of ${Q} to ${A} metric ${B}`),Z.metricsAggregator.add(A,B,Q,F,{...V,...I},Y)}}function Oh0(A,B=1,Q){kJ1(yJ1.COUNTER_METRIC_TYPE,A,B,Q)}function Th0(A,B,Q){kJ1(yJ1.DISTRIBUTION_METRIC_TYPE,A,B,Q)}function Ph0(A,B,Q){kJ1(yJ1.SET_METRIC_TYPE,A,B,Q)}function Sh0(A,B,Q){kJ1(yJ1.GAUGE_METRIC_TYPE,A,B,Q)}var FY9={increment:Oh0,distribution:Th0,set:Ph0,gauge:Sh0,MetricsAggregator:Rh0.MetricsAggregator,metricsAggregatorIntegration:Rh0.metricsAggregatorIntegration};jh0.distribution=Th0;jh0.gauge=Sh0;jh0.increment=Oh0;jh0.metrics=FY9;jh0.set=Ph0});
var zJ1=E(($b0)=>{Object.defineProperty($b0,"__esModule",{value:!0});var Jf=wA(),Eb0=kG(),lD9=N21(),FO=L21(),Ub0=Il(),Vl=BV(),pD9=Jl();class wb0{constructor(A=1000){this._maxlen=A,this.spans=[]}add(A){if(this.spans.length>this._maxlen)A.spanRecorder=void 0;else this.spans.push(A)}}class Bc1{constructor(A={}){if(this._traceId=A.traceId||Jf.uuid4(),this._spanId=A.spanId||Jf.uuid4().substring(16),this._startTime=A.startTimestamp||Jf.timestampInSeconds(),this.tags=A.tags?{...A.tags}:{},this.data=A.data?{...A.data}:{},this.instrumenter=A.instrumenter||"sentry",this._attributes={},this.setAttributes({[FO.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]:A.origin||"manual",[FO.SEMANTIC_ATTRIBUTE_SENTRY_OP]:A.op,...A.attributes}),this._name=A.name||A.description,A.parentSpanId)this._parentSpanId=A.parentSpanId;if("sampled"in A)this._sampled=A.sampled;if(A.status)this._status=A.status;if(A.endTimestamp)this._endTime=A.endTimestamp;if(A.exclusiveTime!==void 0)this._exclusiveTime=A.exclusiveTime;this._measurements=A.measurements?{...A.measurements}:{}}get name(){return this._name||""}set name(A){this.updateName(A)}get description(){return this._name}set description(A){this._name=A}get traceId(){return this._traceId}set traceId(A){this._traceId=A}get spanId(){return this._spanId}set spanId(A){this._spanId=A}set parentSpanId(A){this._parentSpanId=A}get parentSpanId(){return this._parentSpanId}get sampled(){return this._sampled}set sampled(A){this._sampled=A}get attributes(){return this._attributes}set attributes(A){this._attributes=A}get startTimestamp(){return this._startTime}set startTimestamp(A){this._startTime=A}get endTimestamp(){return this._endTime}set endTimestamp(A){this._endTime=A}get status(){return this._status}set status(A){this._status=A}get op(){return this._attributes[FO.SEMANTIC_ATTRIBUTE_SENTRY_OP]}set op(A){this.setAttribute(FO.SEMANTIC_ATTRIBUTE_SENTRY_OP,A)}get origin(){return this._attributes[FO.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]}set origin(A){this.setAttribute(FO.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,A)}spanContext(){let{_spanId:A,_traceId:B,_sampled:Q}=this;return{spanId:A,traceId:B,traceFlags:Q?Vl.TRACE_FLAG_SAMPLED:Vl.TRACE_FLAG_NONE}}startChild(A){let B=new Bc1({...A,parentSpanId:this._spanId,sampled:this._sampled,traceId:this._traceId});if(B.spanRecorder=this.spanRecorder,B.spanRecorder)B.spanRecorder.add(B);let Q=Ub0.getRootSpan(this);if(B.transaction=Q,Eb0.DEBUG_BUILD&&Q){let D=A&&A.op||"< unknown op >",Z=Vl.spanToJSON(B).description||"< unknown name >",G=Q.spanContext().spanId,F=`[Tracing] Starting '${D}' span on transaction '${Z}' (${G}).`;Jf.logger.log(F),this._logMessage=F}return B}setTag(A,B){return this.tags={...this.tags,[A]:B},this}setData(A,B){return this.data={...this.data,[A]:B},this}setAttribute(A,B){if(B===void 0)delete this._attributes[A];else this._attributes[A]=B}setAttributes(A){Object.keys(A).forEach((B)=>this.setAttribute(B,A[B]))}setStatus(A){return this._status=A,this}setHttpStatus(A){return pD9.setHttpStatus(this,A),this}setName(A){this.updateName(A)}updateName(A){return this._name=A,this}isSuccess(){return this._status==="ok"}finish(A){return this.end(A)}end(A){if(this._endTime)return;let B=Ub0.getRootSpan(this);if(Eb0.DEBUG_BUILD&&B&&B.spanContext().spanId!==this._spanId){let Q=this._logMessage;if(Q)Jf.logger.log(Q.replace("Starting","Finishing"))}this._endTime=Vl.spanTimeInputToSeconds(A)}toTraceparent(){return Vl.spanToTraceHeader(this)}toContext(){return Jf.dropUndefinedKeys({data:this._getData(),description:this._name,endTimestamp:this._endTime,op:this.op,parentSpanId:this._parentSpanId,sampled:this._sampled,spanId:this._spanId,startTimestamp:this._startTime,status:this._status,tags:this.tags,traceId:this._traceId})}updateWithContext(A){return this.data=A.data||{},this._name=A.name||A.description,this._endTime=A.endTimestamp,this.op=A.op,this._parentSpanId=A.parentSpanId,this._sampled=A.sampled,this._spanId=A.spanId||this._spanId,this._startTime=A.startTimestamp||this._startTime,this._status=A.status,this.tags=A.tags||{},this._traceId=A.traceId||this._traceId,this}getTraceContext(){return Vl.spanToTraceContext(this)}getSpanJSON(){return Jf.dropUndefinedKeys({data:this._getData(),description:this._name,op:this._attributes[FO.SEMANTIC_ATTRIBUTE_SENTRY_OP],parent_span_id:this._parentSpanId,span_id:this._spanId,start_timestamp:this._startTime,status:this._status,tags:Object.keys(this.tags).length>0?this.tags:void 0,timestamp:this._endTime,trace_id:this._traceId,origin:this._attributes[FO.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN],_metrics_summary:lD9.getMetricSummaryJsonForSpan(this),profile_id:this._attributes[FO.SEMANTIC_ATTRIBUTE_PROFILE_ID],exclusive_time:this._exclusiveTime,measurements:Object.keys(this._measurements).length>0?this._measurements:void 0})}isRecording(){return!this._endTime&&!!this._sampled}toJSON(){return this.getSpanJSON()}_getData(){let{data:A,_attributes:B}=this,Q=Object.keys(A).length>0,D=Object.keys(B).length>0;if(!Q&&!D)return;if(Q&&D)return{...A,...B};return Q?A:B}}$b0.Span=Bc1;$b0.SpanRecorder=wb0});
var zd1=E((Yx0)=>{Object.defineProperty(Yx0,"__esModule",{value:!0});var FB9=hH(),DO;(function(A){A[A.PENDING=0]="PENDING";let Q=1;A[A.RESOLVED=Q]="RESOLVED";let D=2;A[A.REJECTED=D]="REJECTED"})(DO||(DO={}));function IB9(A){return new oq((B)=>{B(A)})}function YB9(A){return new oq((B,Q)=>{Q(A)})}class oq{constructor(A){oq.prototype.__init.call(this),oq.prototype.__init2.call(this),oq.prototype.__init3.call(this),oq.prototype.__init4.call(this),this._state=DO.PENDING,this._handlers=[];try{A(this._resolve,this._reject)}catch(B){this._reject(B)}}then(A,B){return new oq((Q,D)=>{this._handlers.push([!1,(Z)=>{if(!A)Q(Z);else try{Q(A(Z))}catch(G){D(G)}},(Z)=>{if(!B)D(Z);else try{Q(B(Z))}catch(G){D(G)}}]),this._executeHandlers()})}catch(A){return this.then((B)=>B,A)}finally(A){return new oq((B,Q)=>{let D,Z;return this.then((G)=>{if(Z=!1,D=G,A)A()},(G)=>{if(Z=!0,D=G,A)A()}).then(()=>{if(Z){Q(D);return}B(D)})})}__init(){this._resolve=(A)=>{this._setResult(DO.RESOLVED,A)}}__init2(){this._reject=(A)=>{this._setResult(DO.REJECTED,A)}}__init3(){this._setResult=(A,B)=>{if(this._state!==DO.PENDING)return;if(FB9.isThenable(B)){B.then(this._resolve,this._reject);return}this._state=A,this._value=B,this._executeHandlers()}}__init4(){this._executeHandlers=()=>{if(this._state===DO.PENDING)return;let A=this._handlers.slice();this._handlers=[],A.forEach((B)=>{if(B[0])return;if(this._state===DO.RESOLVED)B[1](this._value);if(this._state===DO.REJECTED)B[2](this._value);B[0]=!0})}}}Yx0.SyncPromise=oq;Yx0.rejectedSyncPromise=YB9;Yx0.resolvedSyncPromise=IB9});
var zf0=E((Hf0)=>{Object.defineProperty(Hf0,"__esModule",{value:!0});var DV=wA(),Vf0=kG(),Kf0=30;function OF9(A,B,Q=DV.makePromiseBuffer(A.bufferSize||Kf0)){let D={},Z=(F)=>Q.drain(F);function G(F){let I=[];if(DV.forEachEnvelopeItem(F,(X,V)=>{let C=DV.envelopeItemTypeToDataCategory(V);if(DV.isRateLimited(D,C)){let K=Cf0(X,V);A.recordDroppedEvent("ratelimit_backoff",C,K)}else I.push(X)}),I.length===0)return DV.resolvedSyncPromise();let Y=DV.createEnvelope(F[0],I),W=(X)=>{DV.forEachEnvelopeItem(Y,(V,C)=>{let K=Cf0(V,C);A.recordDroppedEvent(X,DV.envelopeItemTypeToDataCategory(C),K)})},J=()=>B({body:DV.serializeEnvelope(Y,A.textEncoder)}).then((X)=>{if(X.statusCode!==void 0&&(X.statusCode<200||X.statusCode>=300))Vf0.DEBUG_BUILD&&DV.logger.warn(`Sentry responded with status code ${X.statusCode} to sent event.`);return D=DV.updateRateLimits(D,X),X},(X)=>{throw W("network_error"),X});return Q.add(J).then((X)=>X,(X)=>{if(X instanceof DV.SentryError)return Vf0.DEBUG_BUILD&&DV.logger.error("Skipped sending event because buffer is full."),W("queue_overflow"),DV.resolvedSyncPromise();else throw X})}return G.__sentry__baseTransport__=!0,{send:G,flush:Z}}function Cf0(A,B){if(B!=="event"&&B!=="transaction")return;return Array.isArray(A)?A[1]:void 0}Hf0.DEFAULT_TRANSPORT_BUFFER_SIZE=Kf0;Hf0.createTransport=OF9});
var zg0=E((Hg0)=>{var{_optionalChain:bc1}=wA();Object.defineProperty(Hg0,"__esModule",{value:!0});var vW=wA(),cJ1=ZV(),qX9=mj();class lJ1{static __initStatic(){this.id="Apollo"}constructor(A={useNestjs:!1}){this.name=lJ1.id,this._useNest=!!A.useNestjs}loadDependency(){if(this._useNest)this._module=this._module||vW.loadModule("@nestjs/graphql");else this._module=this._module||vW.loadModule("apollo-server-core");return this._module}setupOnce(A,B){if(qX9.shouldDisableAutoInstrumentation(B)){cJ1.DEBUG_BUILD&&vW.logger.log("Apollo Integration is skipped because of instrumenter configuration.");return}if(this._useNest){let Q=this.loadDependency();if(!Q){cJ1.DEBUG_BUILD&&vW.logger.error("Apollo-NestJS Integration was unable to require @nestjs/graphql package.");return}vW.fill(Q.GraphQLFactory.prototype,"mergeWithSchema",function(D){return function(...Z){return vW.fill(this.resolversExplorerService,"explore",function(G){return function(){let F=vW.arrayify(G.call(this));return Kg0(F,B)}}),D.call(this,...Z)}})}else{let Q=this.loadDependency();if(!Q){cJ1.DEBUG_BUILD&&vW.logger.error("Apollo Integration was unable to require apollo-server-core package.");return}vW.fill(Q.ApolloServerBase.prototype,"constructSchema",function(D){return function(){if(!this.config.resolvers){if(cJ1.DEBUG_BUILD){if(this.config.schema)vW.logger.warn("Apollo integration is not able to trace `ApolloServer` instances constructed via `schema` property.If you are using NestJS with Apollo, please use `Sentry.Integrations.Apollo({ useNestjs: true })` instead."),vW.logger.warn();else if(this.config.modules)vW.logger.warn("Apollo integration is not able to trace `ApolloServer` instances constructed via `modules` property.");vW.logger.error("Skipping tracing as no resolvers found on the `ApolloServer` instance.")}return D.call(this)}let Z=vW.arrayify(this.config.resolvers);return this.config.resolvers=Kg0(Z,B),D.call(this)}})}}}lJ1.__initStatic();function Kg0(A,B){return A.map((Q)=>{return Object.keys(Q).forEach((D)=>{Object.keys(Q[D]).forEach((Z)=>{if(typeof Q[D][Z]!=="function")return;NX9(Q,D,Z,B)})}),Q})}function NX9(A,B,Q,D){vW.fill(A[B],Q,function(Z){return function(...G){let I=D().getScope().getSpan(),Y=bc1([I,"optionalAccess",(J)=>J.startChild,"call",(J)=>J({description:`${B}.${Q}`,op:"graphql.resolve",origin:"auto.graphql.apollo"})]),W=Z.call(this,...G);if(vW.isThenable(W))return W.then((J)=>{return bc1([Y,"optionalAccess",(X)=>X.end,"call",(X)=>X()]),J});return bc1([Y,"optionalAccess",(J)=>J.end,"call",(J)=>J()]),W}})}Hg0.Apollo=lJ1});
var zh0=E((Hh0)=>{Object.defineProperty(Hh0,"__esModule",{value:!0});var rI9=wA(),Ch0=O21(),oI9=Uc1(),tI9=N21(),jJ1=R21();class Kh0{constructor(A){this._client=A,this._buckets=new Map,this._interval=setInterval(()=>this.flush(),Ch0.DEFAULT_BROWSER_FLUSH_INTERVAL)}add(A,B,Q,D="none",Z={},G=rI9.timestampInSeconds()){let F=Math.floor(G),I=jJ1.sanitizeMetricKey(B),Y=jJ1.sanitizeTags(Z),W=jJ1.sanitizeUnit(D),J=jJ1.getBucketKey(A,I,W,Y),X=this._buckets.get(J),V=X&&A===Ch0.SET_METRIC_TYPE?X.metric.weight:0;if(X){if(X.metric.add(Q),X.timestamp<F)X.timestamp=F}else X={metric:new oI9.METRIC_MAP[A](Q),timestamp:F,metricType:A,name:I,unit:W,tags:Y},this._buckets.set(J,X);let C=typeof Q==="string"?X.metric.weight-V:Q;tI9.updateMetricSummaryOnActiveSpan(A,I,C,W,Z,J)}flush(){if(this._buckets.size===0)return;if(this._client.captureAggregateMetrics){let A=Array.from(this._buckets).map(([,B])=>B);this._client.captureAggregateMetrics(A)}this._buckets.clear()}close(){clearInterval(this._interval),this.flush()}}Hh0.BrowserMetricsAggregator=Kh0});
var zl0=E((Hl0)=>{Object.defineProperty(Hl0,"__esModule",{value:!0});var zO=SQ(),JN=wA(),OX1=i21(),Wl0="HttpClient",Lq9=(A={})=>{let B={failedRequestStatusCodes:[[500,599]],failedRequestTargets:[/.*/],...A};return{name:Wl0,setupOnce(){},setup(Q){kq9(Q,B),_q9(Q,B)}}},Jl0=zO.defineIntegration(Lq9),Mq9=zO.convertIntegrationFnToClass(Wl0,Jl0);function Rq9(A,B,Q,D){if(Vl0(A,Q.status,Q.url)){let Z=xq9(B,D),G,F,I,Y;if(Kl0())[{headers:G,cookies:I},{headers:F,cookies:Y}]=[{cookieHeader:"Cookie",obj:Z},{cookieHeader:"Set-Cookie",obj:Q}].map(({cookieHeader:J,obj:X})=>{let V=Pq9(X.headers),C;try{let K=V[J]||V[J.toLowerCase()]||void 0;if(K)C=Xl0(K)}catch(K){OX1.DEBUG_BUILD&&JN.logger.log(`Could not extract cookies from header ${J}`)}return{headers:V,cookies:C}});let W=Cl0({url:Z.url,method:Z.method,status:Q.status,requestHeaders:G,responseHeaders:F,requestCookies:I,responseCookies:Y});zO.captureEvent(W)}}function Oq9(A,B,Q,D){if(Vl0(A,B.status,B.responseURL)){let Z,G,F;if(Kl0()){try{let Y=B.getResponseHeader("Set-Cookie")||B.getResponseHeader("set-cookie")||void 0;if(Y)G=Xl0(Y)}catch(Y){OX1.DEBUG_BUILD&&JN.logger.log("Could not extract cookies from response headers")}try{F=Sq9(B)}catch(Y){OX1.DEBUG_BUILD&&JN.logger.log("Could not extract headers from response")}Z=D}let I=Cl0({url:B.responseURL,method:Q,status:B.status,requestHeaders:Z,responseHeaders:F,responseCookies:G});zO.captureEvent(I)}}function Tq9(A){if(A){let B=A["Content-Length"]||A["content-length"];if(B)return parseInt(B,10)}return}function Xl0(A){return A.split("; ").reduce((B,Q)=>{let[D,Z]=Q.split("=");return B[D]=Z,B},{})}function Pq9(A){let B={};return A.forEach((Q,D)=>{B[D]=Q}),B}function Sq9(A){let B=A.getAllResponseHeaders();if(!B)return{};return B.split(`\r
`).reduce((Q,D)=>{let[Z,G]=D.split(": ");return Q[Z]=G,Q},{})}function jq9(A,B){return A.some((Q)=>{if(typeof Q==="string")return B.includes(Q);return Q.test(B)})}function yq9(A,B){return A.some((Q)=>{if(typeof Q==="number")return Q===B;return B>=Q[0]&&B<=Q[1]})}function kq9(A,B){if(!JN.supportsNativeFetch())return;JN.addFetchInstrumentationHandler((Q)=>{if(zO.getClient()!==A)return;let{response:D,args:Z}=Q,[G,F]=Z;if(!D)return;Rq9(B,G,D,F)})}function _q9(A,B){if(!("XMLHttpRequest"in JN.GLOBAL_OBJ))return;JN.addXhrInstrumentationHandler((Q)=>{if(zO.getClient()!==A)return;let D=Q.xhr,Z=D[JN.SENTRY_XHR_DATA_KEY];if(!Z)return;let{method:G,request_headers:F}=Z;try{Oq9(B,D,G,F)}catch(I){OX1.DEBUG_BUILD&&JN.logger.warn("Error while extracting response event form XHR response",I)}})}function Vl0(A,B,Q){return yq9(A.failedRequestStatusCodes,B)&&jq9(A.failedRequestTargets,Q)&&!zO.isSentryRequestUrl(Q,zO.getClient())}function Cl0(A){let B=`HTTP Client Error with status code: ${A.status}`,Q={message:B,exception:{values:[{type:"Error",value:B}]},request:{url:A.url,method:A.method,headers:A.requestHeaders,cookies:A.requestCookies},contexts:{response:{status_code:A.status,headers:A.responseHeaders,cookies:A.responseCookies,body_size:Tq9(A.responseHeaders)}}};return JN.addExceptionMechanism(Q,{type:"http.client",handled:!1}),Q}function xq9(A,B){if(!B&&A instanceof Request)return A;if(A instanceof Request&&A.bodyUsed)return A;return new Request(A,B)}function Kl0(){let A=zO.getClient();return A?Boolean(A.getOptions().sendDefaultPii):!1}Hl0.HttpClient=Mq9;Hl0.httpClientIntegration=Jl0});
var zv0=E((Hv0)=>{Object.defineProperty(Hv0,"__esModule",{value:!0});var vQ9=jd1();function bQ9(A){let B=vQ9._optionalChain(A);return B==null?!0:B}Hv0._optionalChainDelete=bQ9});

// Export all variables
module.exports = {
  $X1,
  $d1,
  $l,
  $l1,
  $m0,
  Ad1,
  Ag0,
  Al0,
  B21,
  BJ1,
  BV,
  Cc0,
  Cc1,
  Cd1,
  Cg0,
  Cl1,
  Cv0,
  D21,
  DJ1,
  Dc1,
  Dd1,
  Df0,
  EX1,
  Ec0,
  FJ1,
  Fc1,
  Fd1,
  Fl,
  Fm0,
  GX1,
  Gc1,
  Gl,
  Gl0,
  HJ1,
  HX1,
  Hf,
  Hl1,
  I21,
  IO,
  IX1,
  Ic1,
  Id1,
  If0,
  Ig0,
  Il,
  Iv0,
  Ix0,
  JJ1,
  Jg0,
  Jl,
  Jm0,
  Jv0,
  Jx0,
  K21,
  Kl1,
  L21,
  LJ1,
  LX1,
  Lx0,
  Mf0,
  Ml,
  N21,
  Nc0,
  Nd1,
  Nl,
  Nl0,
  Nm0,
  O21,
  Oc1,
  Og0,
  Pc1,
  Pd1,
  QJ1,
  QO,
  Qc0,
  Qg0,
  Ql1,
  Qv0,
  R21,
  Rd1,
  Rl1,
  Ru0,
  SQ,
  Sd1,
  Sf0,
  TX1,
  Tc1,
  Tf0,
  UX1,
  Uc1,
  Ud1,
  Ug0,
  Um0,
  Uv0,
  VX1,
  Vd1,
  Vh0,
  Vx0,
  WJ1,
  Wd1,
  Wu0,
  XJ1,
  XX1,
  Xc0,
  Xc1,
  Xd1,
  Xf0,
  YX1,
  Yd1,
  Yf,
  Yl0,
  ZV,
  ZX1,
  Zc1,
  Zd0,
  Zg0,
  Zm0,
  Zv0,
  _21,
  _W1,
  _c0,
  _l0,
  aJ1,
  ac0,
  bx0,
  cb0,
  cf0,
  cu0,
  dc0,
  dl0,
  eJ1,
  ec1,
  ed0,
  ed1,
  em0,
  eq,
  ff0,
  gH,
  gd0,
  gl0,
  gm1,
  hC,
  hH,
  hc0,
  hc1,
  hg0,
  hm1,
  i21,
  ic1,
  im0,
  ix0,
  jd1,
  kG,
  kW,
  kW1,
  kb0,
  kf0,
  ku0,
  l21,
  lk0,
  ll0,
  lm1,
  mH,
  mg0,
  mj,
  mx0,
  nc1,
  nd0,
  ng0,
  oU,
  pJ1,
  p_0,
  qf0,
  qh0,
  ql,
  r_0,
  rg0,
  rq,
  ru0,
  rx0,
  sf0,
  sm1,
  tc1,
  tg0,
  tm1,
  tx0,
  uc0,
  ud0,
  uu0,
  vg0,
  wA,
  wJ1,
  wf,
  wf0,
  wl1,
  wx0,
  xm1,
  yh0,
  zJ1,
  zd1,
  zf0,
  zg0,
  zh0,
  zl0,
  zv0
};
