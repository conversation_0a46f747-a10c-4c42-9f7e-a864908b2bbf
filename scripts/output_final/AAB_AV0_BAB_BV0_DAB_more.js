// Package extracted with entry point: DAB
// Contains 26 variables: AAB, AV0, BAB, BV0, DAB, H0B, HP2, NI0, O0B, OI0... (and 16 more)

var AAB=E((t0B)=>{Object.defineProperty(t0B,"__esModule",{value:!0});t0B.BatchLogRecordProcessor=void 0;var hq6=r0B();class o0B extends hq6.BatchLogRecordProcessorBase{onShutdown(){}}t0B.BatchLogRecordProcessor=o0B});
var AV0=E((I0B)=>{Object.defineProperty(I0B,"__esModule",{value:!0});I0B.LogRecord=void 0;var Nq6=VQ(),wt=VQ(),BS1=x3();class F0B{hrTime;hrTimeObserved;spanContext;resource;instrumentationScope;attributes={};_severityText;_severityNumber;_body;totalAttributesCount=0;_isReadonly=!1;_logRecordLimits;set severityText(A){if(this._isLogRecordReadonly())return;this._severityText=A}get severityText(){return this._severityText}set severityNumber(A){if(this._isLogRecordReadonly())return;this._severityNumber=A}get severityNumber(){return this._severityNumber}set body(A){if(this._isLogRecordReadonly())return;this._body=A}get body(){return this._body}get droppedAttributesCount(){return this.totalAttributesCount-Object.keys(this.attributes).length}constructor(A,B,Q){let{timestamp:D,observedTimestamp:Z,severityNumber:G,severityText:F,body:I,attributes:Y={},context:W}=Q,J=Date.now();if(this.hrTime=BS1.timeInputToHrTime(D??J),this.hrTimeObserved=BS1.timeInputToHrTime(Z??J),W){let X=wt.trace.getSpanContext(W);if(X&&wt.isSpanContextValid(X))this.spanContext=X}this.severityNumber=G,this.severityText=F,this.body=I,this.resource=A.resource,this.instrumentationScope=B,this._logRecordLimits=A.logRecordLimits,this.setAttributes(Y)}setAttribute(A,B){if(this._isLogRecordReadonly())return this;if(B===null)return this;if(A.length===0)return wt.diag.warn(`Invalid attribute key: ${A}`),this;if(!BS1.isAttributeValue(B)&&!(typeof B==="object"&&!Array.isArray(B)&&Object.keys(B).length>0))return wt.diag.warn(`Invalid attribute value set for key: ${A}`),this;if(this.totalAttributesCount+=1,Object.keys(this.attributes).length>=this._logRecordLimits.attributeCountLimit&&!Object.prototype.hasOwnProperty.call(this.attributes,A)){if(this.droppedAttributesCount===1)wt.diag.warn("Dropping extra attributes.");return this}if(BS1.isAttributeValue(B))this.attributes[A]=this._truncateToSize(B);else this.attributes[A]=B;return this}setAttributes(A){for(let[B,Q]of Object.entries(A))this.setAttribute(B,Q);return this}setBody(A){return this.body=A,this}setSeverityNumber(A){return this.severityNumber=A,this}setSeverityText(A){return this.severityText=A,this}_makeReadonly(){this._isReadonly=!0}_truncateToSize(A){let B=this._logRecordLimits.attributeValueLengthLimit;if(B<=0)return wt.diag.warn(`Attribute value limit must be positive, got ${B}`),A;if(typeof A==="string")return this._truncateToLimitUtil(A,B);if(Array.isArray(A))return A.map((Q)=>typeof Q==="string"?this._truncateToLimitUtil(Q,B):Q);return A}_truncateToLimitUtil(A,B){if(A.length<=B)return A;return A.substring(0,B)}_isLogRecordReadonly(){if(this._isReadonly)Nq6.diag.warn("Can not execute the operation on emitted log record");return this._isReadonly}}I0B.LogRecord=F0B});
var BAB=E((QV0)=>{Object.defineProperty(QV0,"__esModule",{value:!0});QV0.BatchLogRecordProcessor=void 0;var gq6=AAB();Object.defineProperty(QV0,"BatchLogRecordProcessor",{enumerable:!0,get:function(){return gq6.BatchLogRecordProcessor}})});
var BV0=E((q0B)=>{Object.defineProperty(q0B,"__esModule",{value:!0});q0B.NoopLogRecordProcessor=void 0;class $0B{forceFlush(){return Promise.resolve()}onEmit(A,B){}shutdown(){return Promise.resolve()}}q0B.NoopLogRecordProcessor=$0B});
var DAB=E((RP)=>{Object.defineProperty(RP,"__esModule",{value:!0});RP.BatchLogRecordProcessor=RP.InMemoryLogRecordExporter=RP.SimpleLogRecordProcessor=RP.ConsoleLogRecordExporter=RP.NoopLogRecordProcessor=RP.LogRecord=RP.LoggerProvider=void 0;var cq6=_0B();Object.defineProperty(RP,"LoggerProvider",{enumerable:!0,get:function(){return cq6.LoggerProvider}});var lq6=AV0();Object.defineProperty(RP,"LogRecord",{enumerable:!0,get:function(){return lq6.LogRecord}});var pq6=BV0();Object.defineProperty(RP,"NoopLogRecordProcessor",{enumerable:!0,get:function(){return pq6.NoopLogRecordProcessor}});var iq6=f0B();Object.defineProperty(RP,"ConsoleLogRecordExporter",{enumerable:!0,get:function(){return iq6.ConsoleLogRecordExporter}});var nq6=m0B();Object.defineProperty(RP,"SimpleLogRecordProcessor",{enumerable:!0,get:function(){return nq6.SimpleLogRecordProcessor}});var aq6=i0B();Object.defineProperty(RP,"InMemoryLogRecordExporter",{enumerable:!0,get:function(){return aq6.InMemoryLogRecordExporter}});var sq6=QAB();Object.defineProperty(RP,"BatchLogRecordProcessor",{enumerable:!0,get:function(){return sq6.BatchLogRecordProcessor}})});
var H0B=E((C0B)=>{Object.defineProperty(C0B,"__esModule",{value:!0});C0B.reconfigureLimits=C0B.loadDefaultConfig=void 0;var $t=x3();function Rq6(){return{forceFlushTimeoutMillis:30000,logRecordLimits:{attributeValueLengthLimit:$t.getNumberFromEnv("OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT")??1/0,attributeCountLimit:$t.getNumberFromEnv("OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT")??128},includeTraceContext:!0}}C0B.loadDefaultConfig=Rq6;function Oq6(A){return{attributeCountLimit:A.attributeCountLimit??$t.getNumberFromEnv("OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT")??$t.getNumberFromEnv("OTEL_ATTRIBUTE_COUNT_LIMIT")??128,attributeValueLengthLimit:A.attributeValueLengthLimit??$t.getNumberFromEnv("OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT")??$t.getNumberFromEnv("OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT")??1/0}}C0B.reconfigureLimits=Oq6});
var HP2=E((KP2)=>{Object.defineProperty(KP2,"__esModule",{value:!0});KP2.SeverityNumber=void 0;var nt4;(function(A){A[A.UNSPECIFIED=0]="UNSPECIFIED",A[A.TRACE=1]="TRACE",A[A.TRACE2=2]="TRACE2",A[A.TRACE3=3]="TRACE3",A[A.TRACE4=4]="TRACE4",A[A.DEBUG=5]="DEBUG",A[A.DEBUG2=6]="DEBUG2",A[A.DEBUG3=7]="DEBUG3",A[A.DEBUG4=8]="DEBUG4",A[A.INFO=9]="INFO",A[A.INFO2=10]="INFO2",A[A.INFO3=11]="INFO3",A[A.INFO4=12]="INFO4",A[A.WARN=13]="WARN",A[A.WARN2=14]="WARN2",A[A.WARN3=15]="WARN3",A[A.WARN4=16]="WARN4",A[A.ERROR=17]="ERROR",A[A.ERROR2=18]="ERROR2",A[A.ERROR3=19]="ERROR3",A[A.ERROR4=20]="ERROR4",A[A.FATAL=21]="FATAL",A[A.FATAL2=22]="FATAL2",A[A.FATAL3=23]="FATAL3",A[A.FATAL4=24]="FATAL4"})(nt4=KP2.SeverityNumber||(KP2.SeverityNumber={}))});
var NI0=E((MP2)=>{Object.defineProperty(MP2,"__esModule",{value:!0});MP2.ProxyLoggerProvider=void 0;var tt4=uO1(),et4=qI0();class LP2{getLogger(A,B,Q){var D;return(D=this.getDelegateLogger(A,B,Q))!==null&&D!==void 0?D:new et4.ProxyLogger(this,A,B,Q)}getDelegate(){var A;return(A=this._delegate)!==null&&A!==void 0?A:tt4.NOOP_LOGGER_PROVIDER}setDelegate(A){this._delegate=A}getDelegateLogger(A,B,Q){var D;return(D=this._delegate)===null||D===void 0?void 0:D.getLogger(A,B,Q)}}MP2.ProxyLoggerProvider=LP2});
var O0B=E((M0B)=>{Object.defineProperty(M0B,"__esModule",{value:!0});M0B.LoggerProviderSharedState=void 0;var Sq6=BV0();class L0B{resource;forceFlushTimeoutMillis;logRecordLimits;loggers=new Map;activeProcessor;registeredLogRecordProcessors=[];constructor(A,B,Q){this.resource=A,this.forceFlushTimeoutMillis=B,this.logRecordLimits=Q,this.activeProcessor=new Sq6.NoopLogRecordProcessor}}M0B.LoggerProviderSharedState=L0B});
var OI0=E((VP)=>{Object.defineProperty(VP,"__esModule",{value:!0});VP.logs=VP.ProxyLoggerProvider=VP.ProxyLogger=VP.NoopLoggerProvider=VP.NOOP_LOGGER_PROVIDER=VP.NoopLogger=VP.NOOP_LOGGER=VP.SeverityNumber=void 0;var Je4=HP2();Object.defineProperty(VP,"SeverityNumber",{enumerable:!0,get:function(){return Je4.SeverityNumber}});var hP2=gO1();Object.defineProperty(VP,"NOOP_LOGGER",{enumerable:!0,get:function(){return hP2.NOOP_LOGGER}});Object.defineProperty(VP,"NoopLogger",{enumerable:!0,get:function(){return hP2.NoopLogger}});var gP2=uO1();Object.defineProperty(VP,"NOOP_LOGGER_PROVIDER",{enumerable:!0,get:function(){return gP2.NOOP_LOGGER_PROVIDER}});Object.defineProperty(VP,"NoopLoggerProvider",{enumerable:!0,get:function(){return gP2.NoopLoggerProvider}});var Xe4=qI0();Object.defineProperty(VP,"ProxyLogger",{enumerable:!0,get:function(){return Xe4.ProxyLogger}});var Ve4=NI0();Object.defineProperty(VP,"ProxyLoggerProvider",{enumerable:!0,get:function(){return Ve4.ProxyLoggerProvider}});var Ce4=fP2();VP.logs=Ce4.LogsAPI.getInstance()});
var PP2=E((OP2)=>{Object.defineProperty(OP2,"__esModule",{value:!0});OP2._globalThis=void 0;OP2._globalThis=typeof globalThis==="object"?globalThis:global});
var QAB=E((DV0)=>{Object.defineProperty(DV0,"__esModule",{value:!0});DV0.BatchLogRecordProcessor=void 0;var mq6=BAB();Object.defineProperty(DV0,"BatchLogRecordProcessor",{enumerable:!0,get:function(){return mq6.BatchLogRecordProcessor}})});
var SP2=E((LI0)=>{Object.defineProperty(LI0,"__esModule",{value:!0});LI0._globalThis=void 0;var Ae4=PP2();Object.defineProperty(LI0,"_globalThis",{enumerable:!0,get:function(){return Ae4._globalThis}})});
var V0B=E((J0B)=>{Object.defineProperty(J0B,"__esModule",{value:!0});J0B.Logger=void 0;var Lq6=VQ(),Mq6=AV0();class W0B{instrumentationScope;_sharedState;constructor(A,B){this.instrumentationScope=A,this._sharedState=B}emit(A){let B=A.context||Lq6.context.active(),Q=new Mq6.LogRecord(this._sharedState,this.instrumentationScope,{context:B,...A});this._sharedState.activeProcessor.onEmit(Q,B),Q._makeReadonly()}}J0B.Logger=W0B});
var _0B=E((j0B)=>{Object.defineProperty(j0B,"__esModule",{value:!0});j0B.LoggerProvider=j0B.DEFAULT_LOGGER_NAME=void 0;var Z71=VQ(),jq6=OI0(),yq6=XT1(),T0B=x3(),kq6=V0B(),P0B=H0B(),_q6=w0B(),xq6=O0B();j0B.DEFAULT_LOGGER_NAME="unknown";class S0B{_shutdownOnce;_sharedState;constructor(A={}){let B=T0B.merge({},P0B.loadDefaultConfig(),A),Q=A.resource??yq6.defaultResource();this._sharedState=new xq6.LoggerProviderSharedState(Q,B.forceFlushTimeoutMillis,P0B.reconfigureLimits(B.logRecordLimits)),this._shutdownOnce=new T0B.BindOnceFuture(this._shutdown,this)}getLogger(A,B,Q){if(this._shutdownOnce.isCalled)return Z71.diag.warn("A shutdown LoggerProvider cannot provide a Logger"),jq6.NOOP_LOGGER;if(!A)Z71.diag.warn("Logger requested without instrumentation scope name.");let D=A||j0B.DEFAULT_LOGGER_NAME,Z=`${D}@${B||""}:${Q?.schemaUrl||""}`;if(!this._sharedState.loggers.has(Z))this._sharedState.loggers.set(Z,new kq6.Logger({name:D,version:B,schemaUrl:Q?.schemaUrl},this._sharedState));return this._sharedState.loggers.get(Z)}addLogRecordProcessor(A){if(this._sharedState.registeredLogRecordProcessors.length===0)this._sharedState.activeProcessor.shutdown().catch((B)=>Z71.diag.error("Error while trying to shutdown current log record processor",B));this._sharedState.registeredLogRecordProcessors.push(A),this._sharedState.activeProcessor=new _q6.MultiLogRecordProcessor(this._sharedState.registeredLogRecordProcessors,this._sharedState.forceFlushTimeoutMillis)}forceFlush(){if(this._shutdownOnce.isCalled)return Z71.diag.warn("invalid attempt to force flush after LoggerProvider shutdown"),this._shutdownOnce.promise;return this._sharedState.activeProcessor.forceFlush()}shutdown(){if(this._shutdownOnce.isCalled)return Z71.diag.warn("shutdown may only be called once per LoggerProvider"),this._shutdownOnce.promise;return this._shutdownOnce.call()}_shutdown(){return this._sharedState.activeProcessor.shutdown()}}j0B.LoggerProvider=S0B});
var _P2=E((yP2)=>{Object.defineProperty(yP2,"__esModule",{value:!0});yP2.API_BACKWARDS_COMPATIBILITY_VERSION=yP2.makeGetter=yP2._global=yP2.GLOBAL_LOGS_API_KEY=void 0;var Ze4=jP2();yP2.GLOBAL_LOGS_API_KEY=Symbol.for("io.opentelemetry.js.api.logs");yP2._global=Ze4._globalThis;function Ge4(A,B,Q){return(D)=>D===A?B:Q}yP2.makeGetter=Ge4;yP2.API_BACKWARDS_COMPATIBILITY_VERSION=1});
var f0B=E((v0B)=>{Object.defineProperty(v0B,"__esModule",{value:!0});v0B.ConsoleLogRecordExporter=void 0;var vq6=x3(),bq6=x3();class x0B{export(A,B){this._sendLogRecords(A,B)}shutdown(){return Promise.resolve()}_exportInfo(A){return{resource:{attributes:A.resource.attributes},instrumentationScope:A.instrumentationScope,timestamp:vq6.hrTimeToMicroseconds(A.hrTime),traceId:A.spanContext?.traceId,spanId:A.spanContext?.spanId,traceFlags:A.spanContext?.traceFlags,severityText:A.severityText,severityNumber:A.severityNumber,body:A.body,attributes:A.attributes}}_sendLogRecords(A,B){for(let Q of A)console.dir(this._exportInfo(Q),{depth:3});B?.({code:bq6.ExportResultCode.SUCCESS})}}v0B.ConsoleLogRecordExporter=x0B});
var fP2=E((vP2)=>{Object.defineProperty(vP2,"__esModule",{value:!0});vP2.LogsAPI=void 0;var XE=_P2(),We4=uO1(),xP2=NI0();class RI0{constructor(){this._proxyLoggerProvider=new xP2.ProxyLoggerProvider}static getInstance(){if(!this._instance)this._instance=new RI0;return this._instance}setGlobalLoggerProvider(A){if(XE._global[XE.GLOBAL_LOGS_API_KEY])return this.getLoggerProvider();return XE._global[XE.GLOBAL_LOGS_API_KEY]=XE.makeGetter(XE.API_BACKWARDS_COMPATIBILITY_VERSION,A,We4.NOOP_LOGGER_PROVIDER),this._proxyLoggerProvider.setDelegate(A),A}getLoggerProvider(){var A,B;return(B=(A=XE._global[XE.GLOBAL_LOGS_API_KEY])===null||A===void 0?void 0:A.call(XE._global,XE.API_BACKWARDS_COMPATIBILITY_VERSION))!==null&&B!==void 0?B:this._proxyLoggerProvider}getLogger(A,B,Q){return this.getLoggerProvider().getLogger(A,B,Q)}disable(){delete XE._global[XE.GLOBAL_LOGS_API_KEY],this._proxyLoggerProvider=new xP2.ProxyLoggerProvider}}vP2.LogsAPI=RI0});
var gO1=E((zP2)=>{Object.defineProperty(zP2,"__esModule",{value:!0});zP2.NOOP_LOGGER=zP2.NoopLogger=void 0;class wI0{emit(A){}}zP2.NoopLogger=wI0;zP2.NOOP_LOGGER=new wI0});
var i0B=E((l0B)=>{Object.defineProperty(l0B,"__esModule",{value:!0});l0B.InMemoryLogRecordExporter=void 0;var d0B=x3();class c0B{_finishedLogRecords=[];_stopped=!1;export(A,B){if(this._stopped)return B({code:d0B.ExportResultCode.FAILED,error:new Error("Exporter has been stopped")});this._finishedLogRecords.push(...A),B({code:d0B.ExportResultCode.SUCCESS})}shutdown(){return this._stopped=!0,this.reset(),Promise.resolve()}getFinishedLogRecords(){return this._finishedLogRecords}reset(){this._finishedLogRecords=[]}}l0B.InMemoryLogRecordExporter=c0B});
var jP2=E((MI0)=>{Object.defineProperty(MI0,"__esModule",{value:!0});MI0._globalThis=void 0;var Qe4=SP2();Object.defineProperty(MI0,"_globalThis",{enumerable:!0,get:function(){return Qe4._globalThis}})});
var m0B=E((g0B)=>{Object.defineProperty(g0B,"__esModule",{value:!0});g0B.SimpleLogRecordProcessor=void 0;var qt=x3();class h0B{_exporter;_shutdownOnce;_unresolvedExports;constructor(A){this._exporter=A,this._shutdownOnce=new qt.BindOnceFuture(this._shutdown,this),this._unresolvedExports=new Set}onEmit(A){if(this._shutdownOnce.isCalled)return;let B=()=>qt.internal._export(this._exporter,[A]).then((Q)=>{if(Q.code!==qt.ExportResultCode.SUCCESS)qt.globalErrorHandler(Q.error??new Error(`SimpleLogRecordProcessor: log record export failed (status ${Q})`))}).catch(qt.globalErrorHandler);if(A.resource.asyncAttributesPending){let Q=A.resource.waitForAsyncAttributes?.().then(()=>{return this._unresolvedExports.delete(Q),B()},qt.globalErrorHandler);if(Q!=null)this._unresolvedExports.add(Q)}else B()}async forceFlush(){await Promise.all(Array.from(this._unresolvedExports))}shutdown(){return this._shutdownOnce.call()}_shutdown(){return this._exporter.shutdown()}}g0B.SimpleLogRecordProcessor=h0B});
var qI0=E((qP2)=>{Object.defineProperty(qP2,"__esModule",{value:!0});qP2.ProxyLogger=void 0;var ot4=gO1();class $P2{constructor(A,B,Q,D){this._provider=A,this.name=B,this.version=Q,this.options=D}emit(A){this._getLogger().emit(A)}_getLogger(){if(this._delegate)return this._delegate;let A=this._provider.getDelegateLogger(this.name,this.version,this.options);if(!A)return ot4.NOOP_LOGGER;return this._delegate=A,this._delegate}}qP2.ProxyLogger=$P2});
var r0B=E((a0B)=>{Object.defineProperty(a0B,"__esModule",{value:!0});a0B.BatchLogRecordProcessorBase=void 0;var QS1=x3(),fq6=VQ(),MP=x3();class n0B{_exporter;_maxExportBatchSize;_maxQueueSize;_scheduledDelayMillis;_exportTimeoutMillis;_finishedLogRecords=[];_timer;_shutdownOnce;constructor(A,B){if(this._exporter=A,this._maxExportBatchSize=B?.maxExportBatchSize??QS1.getNumberFromEnv("OTEL_BLRP_MAX_EXPORT_BATCH_SIZE")??512,this._maxQueueSize=B?.maxQueueSize??QS1.getNumberFromEnv("OTEL_BLRP_MAX_QUEUE_SIZE")??2048,this._scheduledDelayMillis=B?.scheduledDelayMillis??QS1.getNumberFromEnv("OTEL_BLRP_SCHEDULE_DELAY")??5000,this._exportTimeoutMillis=B?.exportTimeoutMillis??QS1.getNumberFromEnv("OTEL_BLRP_EXPORT_TIMEOUT")??30000,this._shutdownOnce=new MP.BindOnceFuture(this._shutdown,this),this._maxExportBatchSize>this._maxQueueSize)fq6.diag.warn("BatchLogRecordProcessor: maxExportBatchSize must be smaller or equal to maxQueueSize, setting maxExportBatchSize to match maxQueueSize"),this._maxExportBatchSize=this._maxQueueSize}onEmit(A){if(this._shutdownOnce.isCalled)return;this._addToBuffer(A)}forceFlush(){if(this._shutdownOnce.isCalled)return this._shutdownOnce.promise;return this._flushAll()}shutdown(){return this._shutdownOnce.call()}async _shutdown(){this.onShutdown(),await this._flushAll(),await this._exporter.shutdown()}_addToBuffer(A){if(this._finishedLogRecords.length>=this._maxQueueSize)return;this._finishedLogRecords.push(A),this._maybeStartTimer()}_flushAll(){return new Promise((A,B)=>{let Q=[],D=Math.ceil(this._finishedLogRecords.length/this._maxExportBatchSize);for(let Z=0;Z<D;Z++)Q.push(this._flushOneBatch());Promise.all(Q).then(()=>{A()}).catch(B)})}_flushOneBatch(){if(this._clearTimer(),this._finishedLogRecords.length===0)return Promise.resolve();return new Promise((A,B)=>{MP.callWithTimeout(this._export(this._finishedLogRecords.splice(0,this._maxExportBatchSize)),this._exportTimeoutMillis).then(()=>A()).catch(B)})}_maybeStartTimer(){if(this._timer!==void 0)return;this._timer=setTimeout(()=>{this._flushOneBatch().then(()=>{if(this._finishedLogRecords.length>0)this._clearTimer(),this._maybeStartTimer()}).catch((A)=>{MP.globalErrorHandler(A)})},this._scheduledDelayMillis),MP.unrefTimer(this._timer)}_clearTimer(){if(this._timer!==void 0)clearTimeout(this._timer),this._timer=void 0}_export(A){let B=()=>MP.internal._export(this._exporter,A).then((D)=>{if(D.code!==MP.ExportResultCode.SUCCESS)MP.globalErrorHandler(D.error??new Error(`BatchLogRecordProcessor: log record export failed (status ${D})`))}).catch(MP.globalErrorHandler),Q=A.map((D)=>D.resource).filter((D)=>D.asyncAttributesPending);if(Q.length===0)return B();else return Promise.all(Q.map((D)=>D.waitForAsyncAttributes?.())).then(B,MP.globalErrorHandler)}}a0B.BatchLogRecordProcessorBase=n0B});
var uO1=E((UP2)=>{Object.defineProperty(UP2,"__esModule",{value:!0});UP2.NOOP_LOGGER_PROVIDER=UP2.NoopLoggerProvider=void 0;var st4=gO1();class $I0{getLogger(A,B,Q){return new st4.NoopLogger}}UP2.NoopLoggerProvider=$I0;UP2.NOOP_LOGGER_PROVIDER=new $I0});
var w0B=E((E0B)=>{Object.defineProperty(E0B,"__esModule",{value:!0});E0B.MultiLogRecordProcessor=void 0;var Pq6=x3();class z0B{processors;forceFlushTimeoutMillis;constructor(A,B){this.processors=A,this.forceFlushTimeoutMillis=B}async forceFlush(){let A=this.forceFlushTimeoutMillis;await Promise.all(this.processors.map((B)=>Pq6.callWithTimeout(B.forceFlush(),A)))}onEmit(A,B){this.processors.forEach((Q)=>Q.onEmit(A,B))}async shutdown(){await Promise.all(this.processors.map((A)=>A.shutdown()))}}E0B.MultiLogRecordProcessor=z0B});

// Export all variables
module.exports = {
  AAB,
  AV0,
  BAB,
  BV0,
  DAB,
  H0B,
  HP2,
  NI0,
  O0B,
  OI0,
  PP2,
  QAB,
  SP2,
  V0B,
  _0B,
  _P2,
  f0B,
  fP2,
  gO1,
  i0B,
  jP2,
  m0B,
  qI0,
  r0B,
  uO1,
  w0B
};
