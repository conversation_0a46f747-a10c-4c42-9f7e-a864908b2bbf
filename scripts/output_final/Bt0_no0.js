// Package extracted with entry point: Bt0
// Contains 2 variables: Bt0, no0

var Bt0=E((gr8,At0)=>{var ao0=no0();At0.exports=Hx9;var so0="\x00SLASH"+Math.random()+"\x00",ro0="\x00OPEN"+Math.random()+"\x00",vi1="\x00CLOSE"+Math.random()+"\x00",oo0="\x00COMMA"+Math.random()+"\x00",to0="\x00PERIOD"+Math.random()+"\x00";function xi1(A){return parseInt(A,10)==A?parseInt(A,10):A.charCodeAt(0)}function Cx9(A){return A.split("\\\\").join(so0).split("\\{").join(ro0).split("\\}").join(vi1).split("\\,").join(oo0).split("\\.").join(to0)}function Kx9(A){return A.split(so0).join("\\").split(ro0).join("{").split(vi1).join("}").split(oo0).join(",").split(to0).join(".")}function eo0(A){if(!A)return[""];var B=[],Q=ao0("{","}",A);if(!Q)return A.split(",");var{pre:D,body:Z,post:G}=Q,F=D.split(",");F[F.length-1]+="{"+Z+"}";var I=eo0(G);if(G.length)F[F.length-1]+=I.shift(),F.push.apply(F,I);return B.push.apply(B,F),B}function Hx9(A){if(!A)return[];if(A.substr(0,2)==="{}")A="\\{\\}"+A.substr(2);return kB1(Cx9(A),!0).map(Kx9)}function zx9(A){return"{"+A+"}"}function Ex9(A){return/^-?0\d/.test(A)}function Ux9(A,B){return A<=B}function wx9(A,B){return A>=B}function kB1(A,B){var Q=[],D=ao0("{","}",A);if(!D)return[A];var Z=D.pre,G=D.post.length?kB1(D.post,!1):[""];if(/\$$/.test(D.pre))for(var F=0;F<G.length;F++){var I=Z+"{"+D.body+"}"+G[F];Q.push(I)}else{var Y=/^-?\d+\.\.-?\d+(?:\.\.-?\d+)?$/.test(D.body),W=/^[a-zA-Z]\.\.[a-zA-Z](?:\.\.-?\d+)?$/.test(D.body),J=Y||W,X=D.body.indexOf(",")>=0;if(!J&&!X){if(D.post.match(/,.*\}/))return A=D.pre+"{"+D.body+vi1+D.post,kB1(A);return[A]}var V;if(J)V=D.body.split(/\.\./);else if(V=eo0(D.body),V.length===1){if(V=kB1(V[0],!1).map(zx9),V.length===1)return G.map(function(c){return D.pre+V[0]+c})}var C;if(J){var K=xi1(V[0]),H=xi1(V[1]),z=Math.max(V[0].length,V[1].length),$=V.length==3?Math.abs(xi1(V[2])):1,L=Ux9,N=H<K;if(N)$*=-1,L=wx9;var O=V.some(Ex9);C=[];for(var R=K;L(R,H);R+=$){var T;if(W){if(T=String.fromCharCode(R),T==="\\")T=""}else if(T=String(R),O){var j=z-T.length;if(j>0){var f=new Array(j+1).join("0");if(R<0)T="-"+f+T.slice(1);else T=f+T}}C.push(T)}}else{C=[];for(var y=0;y<V.length;y++)C.push.apply(C,kB1(V[y],!1))}for(var y=0;y<C.length;y++)for(var F=0;F<G.length;F++){var I=Z+C[y]+G[F];if(!B||J||I)Q.push(I)}}return Q}});
var no0=E((hr8,io0)=>{io0.exports=lo0;function lo0(A,B,Q){if(A instanceof RegExp)A=co0(A,Q);if(B instanceof RegExp)B=co0(B,Q);var D=po0(A,B,Q);return D&&{start:D[0],end:D[1],pre:Q.slice(0,D[0]),body:Q.slice(D[0]+A.length,D[1]),post:Q.slice(D[1]+B.length)}}function co0(A,B){var Q=B.match(A);return Q?Q[0]:null}lo0.range=po0;function po0(A,B,Q){var D,Z,G,F,I,Y=Q.indexOf(A),W=Q.indexOf(B,Y+1),J=Y;if(Y>=0&&W>0){if(A===B)return[Y,W];D=[],G=Q.length;while(J>=0&&!I){if(J==Y)D.push(J),Y=Q.indexOf(A,J+1);else if(D.length==1)I=[D.pop(),W];else{if(Z=D.pop(),Z<G)G=Z,F=W;W=Q.indexOf(B,J+1)}J=Y<W&&Y>=0?Y:W}if(D.length)I=[G,F]}return I}});

// Export all variables
module.exports = {
  Bt0,
  no0
};
