// Package extracted with entry point: FH1
// Contains 12 variables: $WA, Do1, FH1, KWA, MWA, NWA, SWA, UWA, VWA, WWA... (and 2 more)

var $WA=E((U7Q)=>{var E7Q=UWA();U7Q.operation=function(A){var B=U7Q.timeouts(A);return new E7Q(B,{forever:A&&A.forever,unref:A&&A.unref,maxRetryTime:A&&A.maxRetryTime})};U7Q.timeouts=function(A){if(A instanceof Array)return[].concat(A);var B={retries:10,factor:2,minTimeout:1000,maxTimeout:1/0,randomize:!1};for(var Q in A)B[Q]=A[Q];if(B.minTimeout>B.maxTimeout)throw new Error("minTimeout is greater than maxTimeout");var D=[];for(var Z=0;Z<B.retries;Z++)D.push(this.createTimeout(Z,B));if(A&&A.forever&&!D.length)D.push(this.createTimeout(Z,B));return D.sort(function(G,F){return G-F}),D};U7Q.createTimeout=function(A,B){var Q=B.randomize?Math.random()+1:1,D=Math.round(Q*B.minTimeout*Math.pow(B.factor,A));return D=Math.min(D,B.maxTimeout),D};U7Q.wrap=function(A,B,Q){if(B instanceof Array)Q=B,B=null;if(!Q){Q=[];for(var D in A)if(typeof A[D]==="function")Q.push(D)}for(var Z=0;Z<Q.length;Z++){var G=Q[Z],F=A[G];A[G]=function I(Y){var W=U7Q.operation(B),J=Array.prototype.slice.call(arguments,1),X=J.pop();J.push(function(V){if(W.retry(V))return;if(V)arguments[0]=W.mainError();X.apply(this,arguments)}),W.attempt(function(){Y.apply(A,J)})}.bind(A,F),A[G].options=B}}});
var Do1=E((t35,Qo1)=>{var dD=J1("fs"),C7Q=WWA(),K7Q=VWA(),H7Q=KWA(),oK1=J1("util"),OY,eK1;if(typeof Symbol==="function"&&typeof Symbol.for==="function")OY=Symbol.for("graceful-fs.queue"),eK1=Symbol.for("graceful-fs.previous");else OY="___graceful-fs.queue",eK1="___graceful-fs.previous";function z7Q(){}function zWA(A,B){Object.defineProperty(A,OY,{get:function(){return B}})}var Eh=z7Q;if(oK1.debuglog)Eh=oK1.debuglog("gfs4");else if(/\bgfs4\b/i.test(process.env.NODE_DEBUG||""))Eh=function(){var A=oK1.format.apply(oK1,arguments);A="GFS4: "+A.split(/\n/).join(`
GFS4: `),console.error(A)};if(!dD[OY]){if(er1=global[OY]||[],zWA(dD,er1),dD.close=function(A){function B(Q,D){return A.call(dD,Q,function(Z){if(!Z)HWA();if(typeof D==="function")D.apply(this,arguments)})}return Object.defineProperty(B,eK1,{value:A}),B}(dD.close),dD.closeSync=function(A){function B(Q){A.apply(dD,arguments),HWA()}return Object.defineProperty(B,eK1,{value:A}),B}(dD.closeSync),/\bgfs4\b/i.test(process.env.NODE_DEBUG||""))process.on("exit",function(){Eh(dD[OY]),J1("assert").equal(dD[OY].length,0)})}var er1;if(!global[OY])zWA(global,dD[OY]);Qo1.exports=Ao1(H7Q(dD));if(process.env.TEST_GRACEFUL_FS_GLOBAL_PATCH&&!dD.__patched)Qo1.exports=Ao1(dD),dD.__patched=!0;function Ao1(A){C7Q(A),A.gracefulify=Ao1,A.createReadStream=R,A.createWriteStream=T;var B=A.readFile;A.readFile=Q;function Q(y,c,h){if(typeof c==="function")h=c,c=null;return a(y,c,h);function a(n,v,t,W1){return B(n,v,function(z1){if(z1&&(z1.code==="EMFILE"||z1.code==="ENFILE"))di([a,[n,v,t],z1,W1||Date.now(),Date.now()]);else if(typeof t==="function")t.apply(this,arguments)})}}var D=A.writeFile;A.writeFile=Z;function Z(y,c,h,a){if(typeof h==="function")a=h,h=null;return n(y,c,h,a);function n(v,t,W1,z1,f1){return D(v,t,W1,function(G0){if(G0&&(G0.code==="EMFILE"||G0.code==="ENFILE"))di([n,[v,t,W1,z1],G0,f1||Date.now(),Date.now()]);else if(typeof z1==="function")z1.apply(this,arguments)})}}var G=A.appendFile;if(G)A.appendFile=F;function F(y,c,h,a){if(typeof h==="function")a=h,h=null;return n(y,c,h,a);function n(v,t,W1,z1,f1){return G(v,t,W1,function(G0){if(G0&&(G0.code==="EMFILE"||G0.code==="ENFILE"))di([n,[v,t,W1,z1],G0,f1||Date.now(),Date.now()]);else if(typeof z1==="function")z1.apply(this,arguments)})}}var I=A.copyFile;if(I)A.copyFile=Y;function Y(y,c,h,a){if(typeof h==="function")a=h,h=0;return n(y,c,h,a);function n(v,t,W1,z1,f1){return I(v,t,W1,function(G0){if(G0&&(G0.code==="EMFILE"||G0.code==="ENFILE"))di([n,[v,t,W1,z1],G0,f1||Date.now(),Date.now()]);else if(typeof z1==="function")z1.apply(this,arguments)})}}var W=A.readdir;A.readdir=X;var J=/^v[0-5]\./;function X(y,c,h){if(typeof c==="function")h=c,c=null;var a=J.test(process.version)?function v(t,W1,z1,f1){return W(t,n(t,W1,z1,f1))}:function v(t,W1,z1,f1){return W(t,W1,n(t,W1,z1,f1))};return a(y,c,h);function n(v,t,W1,z1){return function(f1,G0){if(f1&&(f1.code==="EMFILE"||f1.code==="ENFILE"))di([a,[v,t,W1],f1,z1||Date.now(),Date.now()]);else{if(G0&&G0.sort)G0.sort();if(typeof W1==="function")W1.call(this,f1,G0)}}}}if(process.version.substr(0,4)==="v0.8"){var V=K7Q(A);$=V.ReadStream,N=V.WriteStream}var C=A.ReadStream;if(C)$.prototype=Object.create(C.prototype),$.prototype.open=L;var K=A.WriteStream;if(K)N.prototype=Object.create(K.prototype),N.prototype.open=O;Object.defineProperty(A,"ReadStream",{get:function(){return $},set:function(y){$=y},enumerable:!0,configurable:!0}),Object.defineProperty(A,"WriteStream",{get:function(){return N},set:function(y){N=y},enumerable:!0,configurable:!0});var H=$;Object.defineProperty(A,"FileReadStream",{get:function(){return H},set:function(y){H=y},enumerable:!0,configurable:!0});var z=N;Object.defineProperty(A,"FileWriteStream",{get:function(){return z},set:function(y){z=y},enumerable:!0,configurable:!0});function $(y,c){if(this instanceof $)return C.apply(this,arguments),this;else return $.apply(Object.create($.prototype),arguments)}function L(){var y=this;f(y.path,y.flags,y.mode,function(c,h){if(c){if(y.autoClose)y.destroy();y.emit("error",c)}else y.fd=h,y.emit("open",h),y.read()})}function N(y,c){if(this instanceof N)return K.apply(this,arguments),this;else return N.apply(Object.create(N.prototype),arguments)}function O(){var y=this;f(y.path,y.flags,y.mode,function(c,h){if(c)y.destroy(),y.emit("error",c);else y.fd=h,y.emit("open",h)})}function R(y,c){return new A.ReadStream(y,c)}function T(y,c){return new A.WriteStream(y,c)}var j=A.open;A.open=f;function f(y,c,h,a){if(typeof h==="function")a=h,h=null;return n(y,c,h,a);function n(v,t,W1,z1,f1){return j(v,t,W1,function(G0,X0){if(G0&&(G0.code==="EMFILE"||G0.code==="ENFILE"))di([n,[v,t,W1,z1],G0,f1||Date.now(),Date.now()]);else if(typeof z1==="function")z1.apply(this,arguments)})}}return A}function di(A){Eh("ENQUEUE",A[0].name,A[1]),dD[OY].push(A),Bo1()}var tK1;function HWA(){var A=Date.now();for(var B=0;B<dD[OY].length;++B)if(dD[OY][B].length>2)dD[OY][B][3]=A,dD[OY][B][4]=A;Bo1()}function Bo1(){if(clearTimeout(tK1),tK1=void 0,dD[OY].length===0)return;var A=dD[OY].shift(),B=A[0],Q=A[1],D=A[2],Z=A[3],G=A[4];if(Z===void 0)Eh("RETRY",B.name,Q),B.apply(null,Q);else if(Date.now()-Z>=60000){Eh("TIMEOUT",B.name,Q);var F=Q.pop();if(typeof F==="function")F.call(null,D)}else{var I=Date.now()-G,Y=Math.max(G-Z,1),W=Math.min(Y*1.2,100);if(I>=W)Eh("RETRY",B.name,Q),B.apply(null,Q.concat([Z]));else dD[OY].push(A)}if(tK1===void 0)tK1=setTimeout(Bo1,0)}});
var FH1=E((Z75,my)=>{var pi=SWA(),{toPromise:ZH1,toSync:GH1,toSyncOptions:Co1}=yWA();async function kWA(A,B){let Q=await ZH1(pi.lock)(A,B);return ZH1(Q)}function c7Q(A,B){let Q=GH1(pi.lock)(A,Co1(B));return GH1(Q)}function l7Q(A,B){return ZH1(pi.unlock)(A,B)}function p7Q(A,B){return GH1(pi.unlock)(A,Co1(B))}function i7Q(A,B){return ZH1(pi.check)(A,B)}function n7Q(A,B){return GH1(pi.check)(A,Co1(B))}my.exports=kWA;my.exports.lock=kWA;my.exports.unlock=l7Q;my.exports.lockSync=c7Q;my.exports.unlockSync=p7Q;my.exports.check=i7Q;my.exports.checkSync=n7Q});
var KWA=E((o35,CWA)=>{CWA.exports=V7Q;var X7Q=Object.getPrototypeOf||function(A){return A.__proto__};function V7Q(A){if(A===null||typeof A!=="object")return A;if(A instanceof Object)var B={__proto__:X7Q(A)};else var B=Object.create(null);return Object.getOwnPropertyNames(A).forEach(function(Q){Object.defineProperty(B,Q,Object.getOwnPropertyDescriptor(A,Q))}),B}});
var MWA=E((M7Q,Yo1)=>{var LWA=Symbol();function N7Q(A,B,Q){let D=B[LWA];if(D)return B.stat(A,(G,F)=>{if(G)return Q(G);Q(null,F.mtime,D)});let Z=new Date(Math.ceil(Date.now()/1000)*1000+5);B.utimes(A,Z,Z,(G)=>{if(G)return Q(G);B.stat(A,(F,I)=>{if(F)return Q(F);let Y=I.mtime.getTime()%1000===0?"s":"ms";Object.defineProperty(B,LWA,{value:Y}),Q(null,I.mtime,Y)})})}function L7Q(A){let B=Date.now();if(A==="s")B=Math.ceil(B/1000)*1000;return new Date(B)}M7Q.probe=N7Q;M7Q.getMtime=L7Q});
var NWA=E((Q75,li)=>{var ID=global.process,Uh=function(A){return A&&typeof A==="object"&&typeof A.removeListener==="function"&&typeof A.emit==="function"&&typeof A.reallyExit==="function"&&typeof A.listeners==="function"&&typeof A.kill==="function"&&typeof A.pid==="number"&&typeof A.on==="function"};if(!Uh(ID))li.exports=function(){return function(){}};else{if(Zo1=J1("assert"),wh=qWA(),Go1=/^win/i.test(ID.platform),ci=J1("events"),typeof ci!=="function")ci=ci.EventEmitter;if(ID.__signal_exit_emitter__)OF=ID.__signal_exit_emitter__;else OF=ID.__signal_exit_emitter__=new ci,OF.count=0,OF.emitted={};if(!OF.infinite)OF.setMaxListeners(1/0),OF.infinite=!0;li.exports=function(A,B){if(!Uh(global.process))return function(){};if(Zo1.equal(typeof A,"function","a callback must be provided for exit handler"),$h===!1)BH1();var Q="exit";if(B&&B.alwaysLast)Q="afterexit";var D=function(){if(OF.removeListener(Q,A),OF.listeners("exit").length===0&&OF.listeners("afterexit").length===0)n91()};return OF.on(Q,A),D},n91=function A(){if(!$h||!Uh(global.process))return;$h=!1,wh.forEach(function(B){try{ID.removeListener(B,a91[B])}catch(Q){}}),ID.emit=s91,ID.reallyExit=QH1,OF.count-=1},li.exports.unload=n91,uy=function A(B,Q,D){if(OF.emitted[B])return;OF.emitted[B]=!0,OF.emit(B,Q,D)},a91={},wh.forEach(function(A){a91[A]=function B(){if(!Uh(global.process))return;var Q=ID.listeners(A);if(Q.length===OF.count){if(n91(),uy("exit",null,A),uy("afterexit",null,A),Go1&&A==="SIGHUP")A="SIGINT";ID.kill(ID.pid,A)}}}),li.exports.signals=function(){return wh},$h=!1,BH1=function A(){if($h||!Uh(global.process))return;$h=!0,OF.count+=1,wh=wh.filter(function(B){try{return ID.on(B,a91[B]),!0}catch(Q){return!1}}),ID.emit=Io1,ID.reallyExit=Fo1},li.exports.load=BH1,QH1=ID.reallyExit,Fo1=function A(B){if(!Uh(global.process))return;ID.exitCode=B||0,uy("exit",ID.exitCode,null),uy("afterexit",ID.exitCode,null),QH1.call(ID,ID.exitCode)},s91=ID.emit,Io1=function A(B,Q){if(B==="exit"&&Uh(global.process)){if(Q!==void 0)ID.exitCode=Q;var D=s91.apply(this,arguments);return uy("exit",ID.exitCode,null),uy("afterexit",ID.exitCode,null),D}else return s91.apply(this,arguments)}}var Zo1,wh,Go1,ci,OF,n91,uy,a91,$h,BH1,QH1,Fo1,s91,Io1});
var SWA=E((_7Q,o91)=>{var T7Q=J1("path"),Xo1=Do1(),P7Q=$WA(),S7Q=NWA(),RWA=MWA(),hO={};function r91(A,B){return B.lockfilePath||`${A}.lock`}function Vo1(A,B,Q){if(!B.realpath)return Q(null,T7Q.resolve(A));B.fs.realpath(A,Q)}function Jo1(A,B,Q){let D=r91(A,B);B.fs.mkdir(D,(Z)=>{if(!Z)return RWA.probe(D,B.fs,(G,F,I)=>{if(G)return B.fs.rmdir(D,()=>{}),Q(G);Q(null,F,I)});if(Z.code!=="EEXIST")return Q(Z);if(B.stale<=0)return Q(Object.assign(new Error("Lock file is already being held"),{code:"ELOCKED",file:A}));B.fs.stat(D,(G,F)=>{if(G){if(G.code==="ENOENT")return Jo1(A,{...B,stale:0},Q);return Q(G)}if(!OWA(F,B))return Q(Object.assign(new Error("Lock file is already being held"),{code:"ELOCKED",file:A}));TWA(A,B,(I)=>{if(I)return Q(I);Jo1(A,{...B,stale:0},Q)})})})}function OWA(A,B){return A.mtime.getTime()<Date.now()-B.stale}function TWA(A,B,Q){B.fs.rmdir(r91(A,B),(D)=>{if(D&&D.code!=="ENOENT")return Q(D);Q()})}function DH1(A,B){let Q=hO[A];if(Q.updateTimeout)return;if(Q.updateDelay=Q.updateDelay||B.update,Q.updateTimeout=setTimeout(()=>{Q.updateTimeout=null,B.fs.stat(Q.lockfilePath,(D,Z)=>{let G=Q.lastUpdate+B.stale<Date.now();if(D){if(D.code==="ENOENT"||G)return Wo1(A,Q,Object.assign(D,{code:"ECOMPROMISED"}));return Q.updateDelay=1000,DH1(A,B)}if(Q.mtime.getTime()!==Z.mtime.getTime())return Wo1(A,Q,Object.assign(new Error("Unable to update lock within the stale threshold"),{code:"ECOMPROMISED"}));let I=RWA.getMtime(Q.mtimePrecision);B.fs.utimes(Q.lockfilePath,I,I,(Y)=>{let W=Q.lastUpdate+B.stale<Date.now();if(Q.released)return;if(Y){if(Y.code==="ENOENT"||W)return Wo1(A,Q,Object.assign(Y,{code:"ECOMPROMISED"}));return Q.updateDelay=1000,DH1(A,B)}Q.mtime=I,Q.lastUpdate=Date.now(),Q.updateDelay=null,DH1(A,B)})})},Q.updateDelay),Q.updateTimeout.unref)Q.updateTimeout.unref()}function Wo1(A,B,Q){if(B.released=!0,B.updateTimeout)clearTimeout(B.updateTimeout);if(hO[A]===B)delete hO[A];B.options.onCompromised(Q)}function j7Q(A,B,Q){B={stale:1e4,update:null,realpath:!0,retries:0,fs:Xo1,onCompromised:(D)=>{throw D},...B},B.retries=B.retries||0,B.retries=typeof B.retries==="number"?{retries:B.retries}:B.retries,B.stale=Math.max(B.stale||0,2000),B.update=B.update==null?B.stale/2:B.update||0,B.update=Math.max(Math.min(B.update,B.stale/2),1000),Vo1(A,B,(D,Z)=>{if(D)return Q(D);let G=P7Q.operation(B.retries);G.attempt(()=>{Jo1(Z,B,(F,I,Y)=>{if(G.retry(F))return;if(F)return Q(G.mainError());let W=hO[Z]={lockfilePath:r91(Z,B),mtime:I,mtimePrecision:Y,options:B,lastUpdate:Date.now()};DH1(Z,B),Q(null,(J)=>{if(W.released)return J&&J(Object.assign(new Error("Lock is already released"),{code:"ERELEASED"}));PWA(Z,{...B,realpath:!1},J)})})})})}function PWA(A,B,Q){B={fs:Xo1,realpath:!0,...B},Vo1(A,B,(D,Z)=>{if(D)return Q(D);let G=hO[Z];if(!G)return Q(Object.assign(new Error("Lock is not acquired/owned by you"),{code:"ENOTACQUIRED"}));G.updateTimeout&&clearTimeout(G.updateTimeout),G.released=!0,delete hO[Z],TWA(Z,B,Q)})}function y7Q(A,B,Q){B={stale:1e4,realpath:!0,fs:Xo1,...B},B.stale=Math.max(B.stale||0,2000),Vo1(A,B,(D,Z)=>{if(D)return Q(D);B.fs.stat(r91(Z,B),(G,F)=>{if(G)return G.code==="ENOENT"?Q(null,!1):Q(G);return Q(null,!OWA(F,B))})})}function k7Q(){return hO}S7Q(()=>{for(let A in hO){let B=hO[A].options;try{B.fs.rmdirSync(r91(A,B))}catch(Q){}}});_7Q.lock=j7Q;_7Q.unlock=PWA;_7Q.check=y7Q;_7Q.getLocks=k7Q});
var UWA=E((e35,EWA)=>{function Dz(A,B){if(typeof B==="boolean")B={forever:B};if(this._originalTimeouts=JSON.parse(JSON.stringify(A)),this._timeouts=A,this._options=B||{},this._maxRetryTime=B&&B.maxRetryTime||1/0,this._fn=null,this._errors=[],this._attempts=1,this._operationTimeout=null,this._operationTimeoutCb=null,this._timeout=null,this._operationStart=null,this._options.forever)this._cachedTimeouts=this._timeouts.slice(0)}EWA.exports=Dz;Dz.prototype.reset=function(){this._attempts=1,this._timeouts=this._originalTimeouts};Dz.prototype.stop=function(){if(this._timeout)clearTimeout(this._timeout);this._timeouts=[],this._cachedTimeouts=null};Dz.prototype.retry=function(A){if(this._timeout)clearTimeout(this._timeout);if(!A)return!1;var B=new Date().getTime();if(A&&B-this._operationStart>=this._maxRetryTime)return this._errors.unshift(new Error("RetryOperation timeout occurred")),!1;this._errors.push(A);var Q=this._timeouts.shift();if(Q===void 0)if(this._cachedTimeouts)this._errors.splice(this._errors.length-1,this._errors.length),this._timeouts=this._cachedTimeouts.slice(0),Q=this._timeouts.shift();else return!1;var D=this,Z=setTimeout(function(){if(D._attempts++,D._operationTimeoutCb){if(D._timeout=setTimeout(function(){D._operationTimeoutCb(D._attempts)},D._operationTimeout),D._options.unref)D._timeout.unref()}D._fn(D._attempts)},Q);if(this._options.unref)Z.unref();return!0};Dz.prototype.attempt=function(A,B){if(this._fn=A,B){if(B.timeout)this._operationTimeout=B.timeout;if(B.cb)this._operationTimeoutCb=B.cb}var Q=this;if(this._operationTimeoutCb)this._timeout=setTimeout(function(){Q._operationTimeoutCb()},Q._operationTimeout);this._operationStart=new Date().getTime(),this._fn(this._attempts)};Dz.prototype.try=function(A){console.log("Using RetryOperation.try() is deprecated"),this.attempt(A)};Dz.prototype.start=function(A){console.log("Using RetryOperation.start() is deprecated"),this.attempt(A)};Dz.prototype.start=Dz.prototype.try;Dz.prototype.errors=function(){return this._errors};Dz.prototype.attempts=function(){return this._attempts};Dz.prototype.mainError=function(){if(this._errors.length===0)return null;var A={},B=null,Q=0;for(var D=0;D<this._errors.length;D++){var Z=this._errors[D],G=Z.message,F=(A[G]||0)+1;if(A[G]=F,F>=Q)B=Z,Q=F}return B}});
var VWA=E((r35,XWA)=>{var JWA=J1("stream").Stream;XWA.exports=J7Q;function J7Q(A){return{ReadStream:B,WriteStream:Q};function B(D,Z){if(!(this instanceof B))return new B(D,Z);JWA.call(this);var G=this;this.path=D,this.fd=null,this.readable=!0,this.paused=!1,this.flags="r",this.mode=438,this.bufferSize=65536,Z=Z||{};var F=Object.keys(Z);for(var I=0,Y=F.length;I<Y;I++){var W=F[I];this[W]=Z[W]}if(this.encoding)this.setEncoding(this.encoding);if(this.start!==void 0){if(typeof this.start!=="number")throw TypeError("start must be a Number");if(this.end===void 0)this.end=1/0;else if(typeof this.end!=="number")throw TypeError("end must be a Number");if(this.start>this.end)throw new Error("start must be <= end");this.pos=this.start}if(this.fd!==null){process.nextTick(function(){G._read()});return}A.open(this.path,this.flags,this.mode,function(J,X){if(J){G.emit("error",J),G.readable=!1;return}G.fd=X,G.emit("open",X),G._read()})}function Q(D,Z){if(!(this instanceof Q))return new Q(D,Z);JWA.call(this),this.path=D,this.fd=null,this.writable=!0,this.flags="w",this.encoding="binary",this.mode=438,this.bytesWritten=0,Z=Z||{};var G=Object.keys(Z);for(var F=0,I=G.length;F<I;F++){var Y=G[F];this[Y]=Z[Y]}if(this.start!==void 0){if(typeof this.start!=="number")throw TypeError("start must be a Number");if(this.start<0)throw new Error("start must be >= zero");this.pos=this.start}if(this.busy=!1,this._queue=[],this.fd===null)this._open=A.open,this._queue.push([this._open,this.path,this.flags,this.mode,void 0]),this.flush()}}});
var WWA=E((s35,YWA)=>{var gy=J1("constants"),I7Q=process.cwd,sK1=null,Y7Q=process.env.GRACEFUL_FS_PLATFORM||process.platform;process.cwd=function(){if(!sK1)sK1=I7Q.call(process);return sK1};try{process.cwd()}catch(A){}if(typeof process.chdir==="function"){if(rK1=process.chdir,process.chdir=function(A){sK1=null,rK1.call(process,A)},Object.setPrototypeOf)Object.setPrototypeOf(process.chdir,rK1)}var rK1;YWA.exports=W7Q;function W7Q(A){if(gy.hasOwnProperty("O_SYMLINK")&&process.version.match(/^v0\.6\.[0-2]|^v0\.5\./))B(A);if(!A.lutimes)Q(A);if(A.chown=G(A.chown),A.fchown=G(A.fchown),A.lchown=G(A.lchown),A.chmod=D(A.chmod),A.fchmod=D(A.fchmod),A.lchmod=D(A.lchmod),A.chownSync=F(A.chownSync),A.fchownSync=F(A.fchownSync),A.lchownSync=F(A.lchownSync),A.chmodSync=Z(A.chmodSync),A.fchmodSync=Z(A.fchmodSync),A.lchmodSync=Z(A.lchmodSync),A.stat=I(A.stat),A.fstat=I(A.fstat),A.lstat=I(A.lstat),A.statSync=Y(A.statSync),A.fstatSync=Y(A.fstatSync),A.lstatSync=Y(A.lstatSync),A.chmod&&!A.lchmod)A.lchmod=function(J,X,V){if(V)process.nextTick(V)},A.lchmodSync=function(){};if(A.chown&&!A.lchown)A.lchown=function(J,X,V,C){if(C)process.nextTick(C)},A.lchownSync=function(){};if(Y7Q==="win32")A.rename=typeof A.rename!=="function"?A.rename:function(J){function X(V,C,K){var H=Date.now(),z=0;J(V,C,function $(L){if(L&&(L.code==="EACCES"||L.code==="EPERM"||L.code==="EBUSY")&&Date.now()-H<60000){if(setTimeout(function(){A.stat(C,function(N,O){if(N&&N.code==="ENOENT")J(V,C,$);else K(L)})},z),z<100)z+=10;return}if(K)K(L)})}if(Object.setPrototypeOf)Object.setPrototypeOf(X,J);return X}(A.rename);A.read=typeof A.read!=="function"?A.read:function(J){function X(V,C,K,H,z,$){var L;if($&&typeof $==="function"){var N=0;L=function(O,R,T){if(O&&O.code==="EAGAIN"&&N<10)return N++,J.call(A,V,C,K,H,z,L);$.apply(this,arguments)}}return J.call(A,V,C,K,H,z,L)}if(Object.setPrototypeOf)Object.setPrototypeOf(X,J);return X}(A.read),A.readSync=typeof A.readSync!=="function"?A.readSync:function(J){return function(X,V,C,K,H){var z=0;while(!0)try{return J.call(A,X,V,C,K,H)}catch($){if($.code==="EAGAIN"&&z<10){z++;continue}throw $}}}(A.readSync);function B(J){J.lchmod=function(X,V,C){J.open(X,gy.O_WRONLY|gy.O_SYMLINK,V,function(K,H){if(K){if(C)C(K);return}J.fchmod(H,V,function(z){J.close(H,function($){if(C)C(z||$)})})})},J.lchmodSync=function(X,V){var C=J.openSync(X,gy.O_WRONLY|gy.O_SYMLINK,V),K=!0,H;try{H=J.fchmodSync(C,V),K=!1}finally{if(K)try{J.closeSync(C)}catch(z){}else J.closeSync(C)}return H}}function Q(J){if(gy.hasOwnProperty("O_SYMLINK")&&J.futimes)J.lutimes=function(X,V,C,K){J.open(X,gy.O_SYMLINK,function(H,z){if(H){if(K)K(H);return}J.futimes(z,V,C,function($){J.close(z,function(L){if(K)K($||L)})})})},J.lutimesSync=function(X,V,C){var K=J.openSync(X,gy.O_SYMLINK),H,z=!0;try{H=J.futimesSync(K,V,C),z=!1}finally{if(z)try{J.closeSync(K)}catch($){}else J.closeSync(K)}return H};else if(J.futimes)J.lutimes=function(X,V,C,K){if(K)process.nextTick(K)},J.lutimesSync=function(){}}function D(J){if(!J)return J;return function(X,V,C){return J.call(A,X,V,function(K){if(W(K))K=null;if(C)C.apply(this,arguments)})}}function Z(J){if(!J)return J;return function(X,V){try{return J.call(A,X,V)}catch(C){if(!W(C))throw C}}}function G(J){if(!J)return J;return function(X,V,C,K){return J.call(A,X,V,C,function(H){if(W(H))H=null;if(K)K.apply(this,arguments)})}}function F(J){if(!J)return J;return function(X,V,C){try{return J.call(A,X,V,C)}catch(K){if(!W(K))throw K}}}function I(J){if(!J)return J;return function(X,V,C){if(typeof V==="function")C=V,V=null;function K(H,z){if(z){if(z.uid<0)z.uid+=4294967296;if(z.gid<0)z.gid+=4294967296}if(C)C.apply(this,arguments)}return V?J.call(A,X,V,K):J.call(A,X,K)}}function Y(J){if(!J)return J;return function(X,V){var C=V?J.call(A,X,V):J.call(A,X);if(C){if(C.uid<0)C.uid+=4294967296;if(C.gid<0)C.gid+=4294967296}return C}}function W(J){if(!J)return!0;if(J.code==="ENOSYS")return!0;var X=!process.getuid||process.getuid()!==0;if(X){if(J.code==="EINVAL"||J.code==="EPERM")return!0}return!1}}});
var qWA=E((B75,AH1)=>{AH1.exports=["SIGABRT","SIGALRM","SIGHUP","SIGINT","SIGTERM"];if(process.platform!=="win32")AH1.exports.push("SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT");if(process.platform==="linux")AH1.exports.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT","SIGUNUSED")});
var yWA=E((D75,jWA)=>{var h7Q=Do1();function g7Q(A){let B=["mkdir","realpath","stat","rmdir","utimes"],Q={...A};return B.forEach((D)=>{Q[D]=(...Z)=>{let G=Z.pop(),F;try{F=A[`${D}Sync`](...Z)}catch(I){return G(I)}G(null,F)}}),Q}function u7Q(A){return(...B)=>new Promise((Q,D)=>{B.push((Z,G)=>{if(Z)D(Z);else Q(G)}),A(...B)})}function m7Q(A){return(...B)=>{let Q,D;if(B.push((Z,G)=>{Q=Z,D=G}),A(...B),Q)throw Q;return D}}function d7Q(A){if(A={...A},A.fs=g7Q(A.fs||h7Q),typeof A.retries==="number"&&A.retries>0||A.retries&&typeof A.retries.retries==="number"&&A.retries.retries>0)throw Object.assign(new Error("Cannot use retries with the sync api"),{code:"ESYNC"});return A}jWA.exports={toPromise:u7Q,toSync:m7Q,toSyncOptions:d7Q}});

// Export all variables
module.exports = {
  $WA,
  Do1,
  FH1,
  KWA,
  MWA,
  NWA,
  SWA,
  UWA,
  VWA,
  WWA,
  qWA,
  yWA
};
