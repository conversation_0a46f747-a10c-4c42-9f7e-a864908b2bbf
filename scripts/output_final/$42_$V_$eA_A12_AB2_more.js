// Package extracted with entry point: $42
// Contains 66 variables: $42, $V, $eA, A12, AB2, B61, BA2, C6, CI, CeA... (and 56 more)

var $42=E((vK5,w42)=>{var{defineProperty:Wq1,getOwnPropertyDescriptor:zF4,getOwnPropertyNames:EF4}=Object,UF4=Object.prototype.hasOwnProperty,p=(A,B)=>Wq1(A,"name",{value:B,configurable:!0}),wF4=(A,B)=>{for(var Q in B)Wq1(A,Q,{get:B[Q],enumerable:!0})},$F4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of EF4(B))if(!UF4.call(A,Z)&&Z!==Q)Wq1(A,Z,{get:()=>B[Z],enumerable:!(D=zF4(B,<PERSON>))||D.enumerable})}return A},qF4=(A)=>$F4(Wq1({},"__esModule",{value:!0}),A),kB2={};wF4(kB2,{AccessDeniedException:()=>_B2,AgreementStatus:()=>yF4,ApplicationType:()=>hF4,AttributeType:()=>cF4,AuthorizationStatus:()=>OI4,AutomatedEvaluationConfigFilterSensitiveLog:()=>tB2,AutomatedEvaluationCustomMetricConfigFilterSensitiveLog:()=>rB2,AutomatedEvaluationCustomMetricSource:()=>Qq1,AutomatedEvaluationCustomMetricSourceFilterSensitiveLog:()=>sB2,BatchDeleteEvaluationJobCommand:()=>UQ2,BatchDeleteEvaluationJobErrorFilterSensitiveLog:()=>iB2,BatchDeleteEvaluationJobItemFilterSensitiveLog:()=>nB2,BatchDeleteEvaluationJobRequestFilterSensitiveLog:()=>pB2,BatchDeleteEvaluationJobResponseFilterSensitiveLog:()=>aB2,Bedrock:()=>U42,BedrockClient:()=>MV,BedrockServiceException:()=>fw,ByteContentDocFilterSensitiveLog:()=>G92,CommitmentDuration:()=>LI4,ConflictException:()=>hB2,CreateCustomModelCommand:()=>wQ2,CreateEvaluationJobCommand:()=>$Q2,CreateEvaluationJobRequestFilterSensitiveLog:()=>GQ2,CreateFoundationModelAgreementCommand:()=>qQ2,CreateGuardrailCommand:()=>NQ2,CreateGuardrailRequestFilterSensitiveLog:()=>L92,CreateGuardrailVersionCommand:()=>LQ2,CreateGuardrailVersionRequestFilterSensitiveLog:()=>M92,CreateInferenceProfileCommand:()=>MQ2,CreateInferenceProfileRequestFilterSensitiveLog:()=>u92,CreateMarketplaceModelEndpointCommand:()=>RQ2,CreateModelCopyJobCommand:()=>OQ2,CreateModelCustomizationJobCommand:()=>TQ2,CreateModelCustomizationJobRequestFilterSensitiveLog:()=>o92,CreateModelImportJobCommand:()=>PQ2,CreateModelInvocationJobCommand:()=>SQ2,CreatePromptRouterCommand:()=>jQ2,CreatePromptRouterRequestFilterSensitiveLog:()=>n92,CreateProvisionedModelThroughputCommand:()=>yQ2,CustomMetricDefinitionFilterSensitiveLog:()=>TI4,CustomizationConfig:()=>h60,CustomizationType:()=>_F4,DeleteCustomModelCommand:()=>kQ2,DeleteFoundationModelAgreementCommand:()=>_Q2,DeleteGuardrailCommand:()=>xQ2,DeleteImportedModelCommand:()=>vQ2,DeleteInferenceProfileCommand:()=>bQ2,DeleteMarketplaceModelEndpointCommand:()=>fQ2,DeleteModelInvocationLoggingConfigurationCommand:()=>hQ2,DeletePromptRouterCommand:()=>gQ2,DeleteProvisionedModelThroughputCommand:()=>uQ2,DeregisterMarketplaceModelEndpointCommand:()=>mQ2,EndpointConfig:()=>b60,EntitlementAvailability:()=>SI4,EvaluationBedrockModelFilterSensitiveLog:()=>Q92,EvaluationConfig:()=>Dq1,EvaluationConfigFilterSensitiveLog:()=>t60,EvaluationDatasetFilterSensitiveLog:()=>oB2,EvaluationDatasetLocation:()=>m60,EvaluationDatasetMetricConfigFilterSensitiveLog:()=>o60,EvaluationInferenceConfig:()=>Iq1,EvaluationInferenceConfigFilterSensitiveLog:()=>G80,EvaluationJobStatus:()=>fF4,EvaluationJobType:()=>aF4,EvaluationModelConfig:()=>c60,EvaluationModelConfigFilterSensitiveLog:()=>D92,EvaluationPrecomputedRagSourceConfig:()=>p60,EvaluationTaskType:()=>gF4,EvaluatorModelConfig:()=>d60,ExternalSourceFilterSensitiveLog:()=>F92,ExternalSourceType:()=>mF4,ExternalSourcesGenerationConfigurationFilterSensitiveLog:()=>Z92,ExternalSourcesRetrieveAndGenerateConfigurationFilterSensitiveLog:()=>I92,FineTuningJobStatus:()=>xI4,FoundationModelLifecycleStatus:()=>$I4,GenerationConfigurationFilterSensitiveLog:()=>Y92,GetCustomModelCommand:()=>dQ2,GetCustomModelResponseFilterSensitiveLog:()=>lB2,GetEvaluationJobCommand:()=>cQ2,GetEvaluationJobRequestFilterSensitiveLog:()=>K92,GetEvaluationJobResponseFilterSensitiveLog:()=>FQ2,GetFoundationModelAvailabilityCommand:()=>lQ2,GetFoundationModelCommand:()=>pQ2,GetGuardrailCommand:()=>iQ2,GetGuardrailResponseFilterSensitiveLog:()=>b92,GetImportedModelCommand:()=>nQ2,GetInferenceProfileCommand:()=>aQ2,GetInferenceProfileResponseFilterSensitiveLog:()=>m92,GetMarketplaceModelEndpointCommand:()=>sQ2,GetModelCopyJobCommand:()=>rQ2,GetModelCustomizationJobCommand:()=>oQ2,GetModelCustomizationJobResponseFilterSensitiveLog:()=>t92,GetModelImportJobCommand:()=>tQ2,GetModelInvocationJobCommand:()=>eQ2,GetModelInvocationJobResponseFilterSensitiveLog:()=>l92,GetModelInvocationLoggingConfigurationCommand:()=>A42,GetPromptRouterCommand:()=>B42,GetPromptRouterResponseFilterSensitiveLog:()=>a92,GetProvisionedModelThroughputCommand:()=>Q42,GetUseCaseForModelAccessCommand:()=>D42,GuardrailContentFilterAction:()=>rF4,GuardrailContentFilterConfigFilterSensitiveLog:()=>z92,GuardrailContentFilterFilterSensitiveLog:()=>R92,GuardrailContentFilterType:()=>eF4,GuardrailContentFiltersTierConfigFilterSensitiveLog:()=>E92,GuardrailContentFiltersTierFilterSensitiveLog:()=>O92,GuardrailContentFiltersTierName:()=>AI4,GuardrailContentPolicyConfigFilterSensitiveLog:()=>A80,GuardrailContentPolicyFilterSensitiveLog:()=>T92,GuardrailContextualGroundingAction:()=>BI4,GuardrailContextualGroundingFilterConfigFilterSensitiveLog:()=>U92,GuardrailContextualGroundingFilterFilterSensitiveLog:()=>P92,GuardrailContextualGroundingFilterType:()=>QI4,GuardrailContextualGroundingPolicyConfigFilterSensitiveLog:()=>B80,GuardrailContextualGroundingPolicyFilterSensitiveLog:()=>S92,GuardrailFilterStrength:()=>tF4,GuardrailManagedWordsConfigFilterSensitiveLog:()=>q92,GuardrailManagedWordsFilterSensitiveLog:()=>_92,GuardrailManagedWordsType:()=>WI4,GuardrailModality:()=>oF4,GuardrailPiiEntityType:()=>ZI4,GuardrailSensitiveInformationAction:()=>DI4,GuardrailStatus:()=>JI4,GuardrailSummaryFilterSensitiveLog:()=>f92,GuardrailTopicAction:()=>FI4,GuardrailTopicConfigFilterSensitiveLog:()=>$92,GuardrailTopicFilterSensitiveLog:()=>y92,GuardrailTopicPolicyConfigFilterSensitiveLog:()=>Q80,GuardrailTopicPolicyFilterSensitiveLog:()=>k92,GuardrailTopicType:()=>II4,GuardrailTopicsTierConfigFilterSensitiveLog:()=>w92,GuardrailTopicsTierFilterSensitiveLog:()=>j92,GuardrailTopicsTierName:()=>GI4,GuardrailWordAction:()=>YI4,GuardrailWordConfigFilterSensitiveLog:()=>N92,GuardrailWordFilterSensitiveLog:()=>x92,GuardrailWordPolicyConfigFilterSensitiveLog:()=>D80,GuardrailWordPolicyFilterSensitiveLog:()=>v92,HumanEvaluationConfigFilterSensitiveLog:()=>B92,HumanEvaluationCustomMetricFilterSensitiveLog:()=>eB2,HumanWorkflowConfigFilterSensitiveLog:()=>A92,ImplicitFilterConfigurationFilterSensitiveLog:()=>W92,InferenceProfileModelSource:()=>i60,InferenceProfileStatus:()=>XI4,InferenceProfileSummaryFilterSensitiveLog:()=>d92,InferenceProfileType:()=>VI4,InferenceType:()=>UI4,InternalServerException:()=>xB2,InvocationLogSource:()=>g60,InvocationLogsConfigFilterSensitiveLog:()=>cB2,JobStatusDetails:()=>_I4,KnowledgeBaseConfig:()=>Gq1,KnowledgeBaseConfigFilterSensitiveLog:()=>DQ2,KnowledgeBaseRetrievalConfigurationFilterSensitiveLog:()=>Z80,KnowledgeBaseRetrieveAndGenerateConfigurationFilterSensitiveLog:()=>AQ2,KnowledgeBaseVectorSearchConfigurationFilterSensitiveLog:()=>e92,ListCustomModelsCommand:()=>Y80,ListEvaluationJobsCommand:()=>W80,ListFoundationModelAgreementOffersCommand:()=>Z42,ListFoundationModelsCommand:()=>G42,ListGuardrailsCommand:()=>J80,ListGuardrailsResponseFilterSensitiveLog:()=>h92,ListImportedModelsCommand:()=>X80,ListInferenceProfilesCommand:()=>V80,ListInferenceProfilesResponseFilterSensitiveLog:()=>c92,ListMarketplaceModelEndpointsCommand:()=>C80,ListModelCopyJobsCommand:()=>K80,ListModelCustomizationJobsCommand:()=>H80,ListModelImportJobsCommand:()=>z80,ListModelInvocationJobsCommand:()=>E80,ListModelInvocationJobsResponseFilterSensitiveLog:()=>i92,ListPromptRoutersCommand:()=>U80,ListPromptRoutersResponseFilterSensitiveLog:()=>r92,ListProvisionedModelThroughputsCommand:()=>w80,ListTagsForResourceCommand:()=>F42,MetadataAttributeSchemaFilterSensitiveLog:()=>PI4,MetadataConfigurationForRerankingFilterSensitiveLog:()=>X92,ModelCopyJobStatus:()=>CI4,ModelCustomization:()=>EI4,ModelCustomizationJobStatus:()=>kI4,ModelDataSource:()=>f60,ModelImportJobStatus:()=>KI4,ModelInvocationJobInputDataConfig:()=>n60,ModelInvocationJobOutputDataConfig:()=>a60,ModelInvocationJobStatus:()=>zI4,ModelInvocationJobSummaryFilterSensitiveLog:()=>p92,ModelModality:()=>wI4,ModelStatus:()=>xF4,OfferType:()=>yI4,PerformanceConfigLatency:()=>uF4,PromptRouterStatus:()=>qI4,PromptRouterSummaryFilterSensitiveLog:()=>s92,PromptRouterType:()=>NI4,PromptTemplateFilterSensitiveLog:()=>e60,ProvisionedModelStatus:()=>MI4,PutModelInvocationLoggingConfigurationCommand:()=>I42,PutUseCaseForModelAccessCommand:()=>Y42,QueryTransformationType:()=>dF4,RAGConfig:()=>Fq1,RAGConfigFilterSensitiveLog:()=>ZQ2,RatingScaleItemValue:()=>Bq1,RegionAvailability:()=>jI4,RegisterMarketplaceModelEndpointCommand:()=>W42,RequestMetadataBaseFiltersFilterSensitiveLog:()=>s60,RequestMetadataFilters:()=>u60,RequestMetadataFiltersFilterSensitiveLog:()=>dB2,RerankingMetadataSelectionMode:()=>pF4,RerankingMetadataSelectiveModeConfiguration:()=>l60,RerankingMetadataSelectiveModeConfigurationFilterSensitiveLog:()=>J92,ResourceNotFoundException:()=>vB2,RetrievalFilter:()=>Zq1,RetrievalFilterFilterSensitiveLog:()=>vI4,RetrieveAndGenerateConfigurationFilterSensitiveLog:()=>QQ2,RetrieveAndGenerateType:()=>nF4,RetrieveConfigFilterSensitiveLog:()=>BQ2,S3InputFormat:()=>HI4,SearchType:()=>lF4,ServiceQuotaExceededException:()=>gB2,ServiceUnavailableException:()=>uB2,SortByProvisionedModels:()=>RI4,SortJobsBy:()=>sF4,SortModelsBy:()=>vF4,SortOrder:()=>bF4,Status:()=>kF4,StopEvaluationJobCommand:()=>J42,StopEvaluationJobRequestFilterSensitiveLog:()=>H92,StopModelCustomizationJobCommand:()=>X42,StopModelInvocationJobCommand:()=>V42,TagResourceCommand:()=>C42,ThrottlingException:()=>bB2,TooManyTagsException:()=>mB2,TrainingDataConfigFilterSensitiveLog:()=>Jq1,UntagResourceCommand:()=>K42,UpdateGuardrailCommand:()=>H42,UpdateGuardrailRequestFilterSensitiveLog:()=>g92,UpdateMarketplaceModelEndpointCommand:()=>z42,UpdateProvisionedModelThroughputCommand:()=>E42,ValidationException:()=>fB2,VectorSearchBedrockRerankingConfigurationFilterSensitiveLog:()=>V92,VectorSearchRerankingConfigurationFilterSensitiveLog:()=>C92,VectorSearchRerankingConfigurationType:()=>iF4,__Client:()=>_.Client,paginateListCustomModels:()=>FV4,paginateListEvaluationJobs:()=>IV4,paginateListGuardrails:()=>YV4,paginateListImportedModels:()=>WV4,paginateListInferenceProfiles:()=>JV4,paginateListMarketplaceModelEndpoints:()=>XV4,paginateListModelCopyJobs:()=>VV4,paginateListModelCustomizationJobs:()=>CV4,paginateListModelImportJobs:()=>KV4,paginateListModelInvocationJobs:()=>HV4,paginateListPromptRouters:()=>zV4,paginateListProvisionedModelThroughputs:()=>EV4});w42.exports=qF4(kB2);var YB2=h41(),NF4=g41(),LF4=u41(),WB2=na(),MF4=K4(),N2=CB(),RF4=bG(),qB=R6(),JB2=u4(),XB2=X40(),OF4=p((A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,defaultSigningName:"bedrock"})},"resolveClientEndpointParameters"),PB={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}},TF4=IB2(),VB2=a41(),CB2=$V(),_=C6(),PF4=p((A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D,token:Z}=A;return{setHttpAuthScheme(G){let F=B.findIndex((I)=>I.schemeId===G.schemeId);if(F===-1)B.push(G);else B.splice(F,1,G)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(G){Q=G},httpAuthSchemeProvider(){return Q},setCredentials(G){D=G},credentials(){return D},setToken(G){Z=G},token(){return Z}}},"getHttpAuthExtensionConfiguration"),SF4=p((A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials(),token:A.token()}},"resolveHttpAuthRuntimeConfig"),jF4=p((A,B)=>{let Q=Object.assign(VB2.getAwsRegionExtensionConfiguration(A),_.getDefaultExtensionConfiguration(A),CB2.getHttpHandlerExtensionConfiguration(A),PF4(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,VB2.resolveAwsRegionExtensionConfiguration(Q),_.resolveDefaultRuntimeConfig(Q),CB2.resolveHttpHandlerRuntimeConfig(Q),SF4(Q))},"resolveRuntimeExtensions"),MV=class extends _.Client{static{p(this,"BedrockClient")}config;constructor(...[A]){let B=TF4.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=OF4(B),D=WB2.resolveUserAgentConfig(Q),Z=JB2.resolveRetryConfig(D),G=MF4.resolveRegionConfig(Z),F=YB2.resolveHostHeaderConfig(G),I=qB.resolveEndpointConfig(F),Y=XB2.resolveHttpAuthSchemeConfig(I),W=jF4(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(WB2.getUserAgentPlugin(this.config)),this.middlewareStack.use(JB2.getRetryPlugin(this.config)),this.middlewareStack.use(RF4.getContentLengthPlugin(this.config)),this.middlewareStack.use(YB2.getHostHeaderPlugin(this.config)),this.middlewareStack.use(NF4.getLoggerPlugin(this.config)),this.middlewareStack.use(LF4.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(N2.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:XB2.defaultBedrockHttpAuthSchemeParametersProvider,identityProviderConfigProvider:p(async(J)=>new N2.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials,"smithy.api#httpBearerAuth":J.token}),"identityProviderConfigProvider")})),this.middlewareStack.use(N2.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}},SB=j3(),fw=class A extends _.ServiceException{static{p(this,"BedrockServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},_B2=class A extends fw{static{p(this,"AccessDeniedException")}name="AccessDeniedException";$fault="client";constructor(B){super({name:"AccessDeniedException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},yF4={AVAILABLE:"AVAILABLE",ERROR:"ERROR",NOT_AVAILABLE:"NOT_AVAILABLE",PENDING:"PENDING"},xB2=class A extends fw{static{p(this,"InternalServerException")}name="InternalServerException";$fault="server";constructor(B){super({name:"InternalServerException",$fault:"server",...B});Object.setPrototypeOf(this,A.prototype)}},vB2=class A extends fw{static{p(this,"ResourceNotFoundException")}name="ResourceNotFoundException";$fault="client";constructor(B){super({name:"ResourceNotFoundException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},bB2=class A extends fw{static{p(this,"ThrottlingException")}name="ThrottlingException";$fault="client";constructor(B){super({name:"ThrottlingException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},fB2=class A extends fw{static{p(this,"ValidationException")}name="ValidationException";$fault="client";constructor(B){super({name:"ValidationException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},hB2=class A extends fw{static{p(this,"ConflictException")}name="ConflictException";$fault="client";constructor(B){super({name:"ConflictException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},b60;((A)=>{A.visit=p((B,Q)=>{if(B.sageMaker!==void 0)return Q.sageMaker(B.sageMaker);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(b60||(b60={}));var kF4={INCOMPATIBLE_ENDPOINT:"INCOMPATIBLE_ENDPOINT",REGISTERED:"REGISTERED"},gB2=class A extends fw{static{p(this,"ServiceQuotaExceededException")}name="ServiceQuotaExceededException";$fault="client";constructor(B){super({name:"ServiceQuotaExceededException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},uB2=class A extends fw{static{p(this,"ServiceUnavailableException")}name="ServiceUnavailableException";$fault="server";constructor(B){super({name:"ServiceUnavailableException",$fault:"server",...B});Object.setPrototypeOf(this,A.prototype)}},f60;((A)=>{A.visit=p((B,Q)=>{if(B.s3DataSource!==void 0)return Q.s3DataSource(B.s3DataSource);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(f60||(f60={}));var mB2=class A extends fw{static{p(this,"TooManyTagsException")}name="TooManyTagsException";$fault="client";resourceName;constructor(B){super({name:"TooManyTagsException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.resourceName=B.resourceName}},h60;((A)=>{A.visit=p((B,Q)=>{if(B.distillationConfig!==void 0)return Q.distillationConfig(B.distillationConfig);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(h60||(h60={}));var _F4={CONTINUED_PRE_TRAINING:"CONTINUED_PRE_TRAINING",DISTILLATION:"DISTILLATION",FINE_TUNING:"FINE_TUNING",IMPORTED:"IMPORTED"},xF4={ACTIVE:"Active",CREATING:"Creating",FAILED:"Failed"},g60;((A)=>{A.visit=p((B,Q)=>{if(B.s3Uri!==void 0)return Q.s3Uri(B.s3Uri);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(g60||(g60={}));var u60;((A)=>{A.visit=p((B,Q)=>{if(B.equals!==void 0)return Q.equals(B.equals);if(B.notEquals!==void 0)return Q.notEquals(B.notEquals);if(B.andAll!==void 0)return Q.andAll(B.andAll);if(B.orAll!==void 0)return Q.orAll(B.orAll);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(u60||(u60={}));var vF4={CREATION_TIME:"CreationTime"},bF4={ASCENDING:"Ascending",DESCENDING:"Descending"},fF4={COMPLETED:"Completed",DELETING:"Deleting",FAILED:"Failed",IN_PROGRESS:"InProgress",STOPPED:"Stopped",STOPPING:"Stopping"},hF4={MODEL_EVALUATION:"ModelEvaluation",RAG_EVALUATION:"RagEvaluation"},Bq1;((A)=>{A.visit=p((B,Q)=>{if(B.stringValue!==void 0)return Q.stringValue(B.stringValue);if(B.floatValue!==void 0)return Q.floatValue(B.floatValue);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(Bq1||(Bq1={}));var Qq1;((A)=>{A.visit=p((B,Q)=>{if(B.customMetricDefinition!==void 0)return Q.customMetricDefinition(B.customMetricDefinition);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(Qq1||(Qq1={}));var m60;((A)=>{A.visit=p((B,Q)=>{if(B.s3Uri!==void 0)return Q.s3Uri(B.s3Uri);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(m60||(m60={}));var gF4={CLASSIFICATION:"Classification",CUSTOM:"Custom",GENERATION:"Generation",QUESTION_AND_ANSWER:"QuestionAndAnswer",SUMMARIZATION:"Summarization"},d60;((A)=>{A.visit=p((B,Q)=>{if(B.bedrockEvaluatorModels!==void 0)return Q.bedrockEvaluatorModels(B.bedrockEvaluatorModels);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(d60||(d60={}));var Dq1;((A)=>{A.visit=p((B,Q)=>{if(B.automated!==void 0)return Q.automated(B.automated);if(B.human!==void 0)return Q.human(B.human);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(Dq1||(Dq1={}));var uF4={OPTIMIZED:"optimized",STANDARD:"standard"},c60;((A)=>{A.visit=p((B,Q)=>{if(B.bedrockModel!==void 0)return Q.bedrockModel(B.bedrockModel);if(B.precomputedInferenceSource!==void 0)return Q.precomputedInferenceSource(B.precomputedInferenceSource);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(c60||(c60={}));var mF4={BYTE_CONTENT:"BYTE_CONTENT",S3:"S3"},dF4={QUERY_DECOMPOSITION:"QUERY_DECOMPOSITION"},cF4={BOOLEAN:"BOOLEAN",NUMBER:"NUMBER",STRING:"STRING",STRING_LIST:"STRING_LIST"},lF4={HYBRID:"HYBRID",SEMANTIC:"SEMANTIC"},pF4={ALL:"ALL",SELECTIVE:"SELECTIVE"},l60;((A)=>{A.visit=p((B,Q)=>{if(B.fieldsToInclude!==void 0)return Q.fieldsToInclude(B.fieldsToInclude);if(B.fieldsToExclude!==void 0)return Q.fieldsToExclude(B.fieldsToExclude);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(l60||(l60={}));var iF4={BEDROCK_RERANKING_MODEL:"BEDROCK_RERANKING_MODEL"},nF4={EXTERNAL_SOURCES:"EXTERNAL_SOURCES",KNOWLEDGE_BASE:"KNOWLEDGE_BASE"},p60;((A)=>{A.visit=p((B,Q)=>{if(B.retrieveSourceConfig!==void 0)return Q.retrieveSourceConfig(B.retrieveSourceConfig);if(B.retrieveAndGenerateSourceConfig!==void 0)return Q.retrieveAndGenerateSourceConfig(B.retrieveAndGenerateSourceConfig);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(p60||(p60={}));var aF4={AUTOMATED:"Automated",HUMAN:"Human"},sF4={CREATION_TIME:"CreationTime"},rF4={BLOCK:"BLOCK",NONE:"NONE"},oF4={IMAGE:"IMAGE",TEXT:"TEXT"},tF4={HIGH:"HIGH",LOW:"LOW",MEDIUM:"MEDIUM",NONE:"NONE"},eF4={HATE:"HATE",INSULTS:"INSULTS",MISCONDUCT:"MISCONDUCT",PROMPT_ATTACK:"PROMPT_ATTACK",SEXUAL:"SEXUAL",VIOLENCE:"VIOLENCE"},AI4={CLASSIC:"CLASSIC",STANDARD:"STANDARD"},BI4={BLOCK:"BLOCK",NONE:"NONE"},QI4={GROUNDING:"GROUNDING",RELEVANCE:"RELEVANCE"},DI4={ANONYMIZE:"ANONYMIZE",BLOCK:"BLOCK",NONE:"NONE"},ZI4={ADDRESS:"ADDRESS",AGE:"AGE",AWS_ACCESS_KEY:"AWS_ACCESS_KEY",AWS_SECRET_KEY:"AWS_SECRET_KEY",CA_HEALTH_NUMBER:"CA_HEALTH_NUMBER",CA_SOCIAL_INSURANCE_NUMBER:"CA_SOCIAL_INSURANCE_NUMBER",CREDIT_DEBIT_CARD_CVV:"CREDIT_DEBIT_CARD_CVV",CREDIT_DEBIT_CARD_EXPIRY:"CREDIT_DEBIT_CARD_EXPIRY",CREDIT_DEBIT_CARD_NUMBER:"CREDIT_DEBIT_CARD_NUMBER",DRIVER_ID:"DRIVER_ID",EMAIL:"EMAIL",INTERNATIONAL_BANK_ACCOUNT_NUMBER:"INTERNATIONAL_BANK_ACCOUNT_NUMBER",IP_ADDRESS:"IP_ADDRESS",LICENSE_PLATE:"LICENSE_PLATE",MAC_ADDRESS:"MAC_ADDRESS",NAME:"NAME",PASSWORD:"PASSWORD",PHONE:"PHONE",PIN:"PIN",SWIFT_CODE:"SWIFT_CODE",UK_NATIONAL_HEALTH_SERVICE_NUMBER:"UK_NATIONAL_HEALTH_SERVICE_NUMBER",UK_NATIONAL_INSURANCE_NUMBER:"UK_NATIONAL_INSURANCE_NUMBER",UK_UNIQUE_TAXPAYER_REFERENCE_NUMBER:"UK_UNIQUE_TAXPAYER_REFERENCE_NUMBER",URL:"URL",USERNAME:"USERNAME",US_BANK_ACCOUNT_NUMBER:"US_BANK_ACCOUNT_NUMBER",US_BANK_ROUTING_NUMBER:"US_BANK_ROUTING_NUMBER",US_INDIVIDUAL_TAX_IDENTIFICATION_NUMBER:"US_INDIVIDUAL_TAX_IDENTIFICATION_NUMBER",US_PASSPORT_NUMBER:"US_PASSPORT_NUMBER",US_SOCIAL_SECURITY_NUMBER:"US_SOCIAL_SECURITY_NUMBER",VEHICLE_IDENTIFICATION_NUMBER:"VEHICLE_IDENTIFICATION_NUMBER"},GI4={CLASSIC:"CLASSIC",STANDARD:"STANDARD"},FI4={BLOCK:"BLOCK",NONE:"NONE"},II4={DENY:"DENY"},YI4={BLOCK:"BLOCK",NONE:"NONE"},WI4={PROFANITY:"PROFANITY"},JI4={CREATING:"CREATING",DELETING:"DELETING",FAILED:"FAILED",READY:"READY",UPDATING:"UPDATING",VERSIONING:"VERSIONING"},i60;((A)=>{A.visit=p((B,Q)=>{if(B.copyFrom!==void 0)return Q.copyFrom(B.copyFrom);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(i60||(i60={}));var XI4={ACTIVE:"ACTIVE"},VI4={APPLICATION:"APPLICATION",SYSTEM_DEFINED:"SYSTEM_DEFINED"},CI4={COMPLETED:"Completed",FAILED:"Failed",IN_PROGRESS:"InProgress"},KI4={COMPLETED:"Completed",FAILED:"Failed",IN_PROGRESS:"InProgress"},HI4={JSONL:"JSONL"},n60;((A)=>{A.visit=p((B,Q)=>{if(B.s3InputDataConfig!==void 0)return Q.s3InputDataConfig(B.s3InputDataConfig);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(n60||(n60={}));var a60;((A)=>{A.visit=p((B,Q)=>{if(B.s3OutputDataConfig!==void 0)return Q.s3OutputDataConfig(B.s3OutputDataConfig);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(a60||(a60={}));var zI4={COMPLETED:"Completed",EXPIRED:"Expired",FAILED:"Failed",IN_PROGRESS:"InProgress",PARTIALLY_COMPLETED:"PartiallyCompleted",SCHEDULED:"Scheduled",STOPPED:"Stopped",STOPPING:"Stopping",SUBMITTED:"Submitted",VALIDATING:"Validating"},EI4={CONTINUED_PRE_TRAINING:"CONTINUED_PRE_TRAINING",DISTILLATION:"DISTILLATION",FINE_TUNING:"FINE_TUNING"},UI4={ON_DEMAND:"ON_DEMAND",PROVISIONED:"PROVISIONED"},wI4={EMBEDDING:"EMBEDDING",IMAGE:"IMAGE",TEXT:"TEXT"},$I4={ACTIVE:"ACTIVE",LEGACY:"LEGACY"},qI4={AVAILABLE:"AVAILABLE"},NI4={CUSTOM:"custom",DEFAULT:"default"},LI4={ONE_MONTH:"OneMonth",SIX_MONTHS:"SixMonths"},MI4={CREATING:"Creating",FAILED:"Failed",IN_SERVICE:"InService",UPDATING:"Updating"},RI4={CREATION_TIME:"CreationTime"},OI4={AUTHORIZED:"AUTHORIZED",NOT_AUTHORIZED:"NOT_AUTHORIZED"},s60=p((A)=>({...A,...A.equals&&{equals:_.SENSITIVE_STRING},...A.notEquals&&{notEquals:_.SENSITIVE_STRING}}),"RequestMetadataBaseFiltersFilterSensitiveLog"),dB2=p((A)=>{if(A.equals!==void 0)return{equals:_.SENSITIVE_STRING};if(A.notEquals!==void 0)return{notEquals:_.SENSITIVE_STRING};if(A.andAll!==void 0)return{andAll:A.andAll.map((B)=>s60(B))};if(A.orAll!==void 0)return{orAll:A.orAll.map((B)=>s60(B))};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"RequestMetadataFiltersFilterSensitiveLog"),cB2=p((A)=>({...A,...A.invocationLogSource&&{invocationLogSource:A.invocationLogSource},...A.requestMetadataFilters&&{requestMetadataFilters:dB2(A.requestMetadataFilters)}}),"InvocationLogsConfigFilterSensitiveLog"),Jq1=p((A)=>({...A,...A.invocationLogsConfig&&{invocationLogsConfig:cB2(A.invocationLogsConfig)}}),"TrainingDataConfigFilterSensitiveLog"),lB2=p((A)=>({...A,...A.trainingDataConfig&&{trainingDataConfig:Jq1(A.trainingDataConfig)},...A.customizationConfig&&{customizationConfig:A.customizationConfig}}),"GetCustomModelResponseFilterSensitiveLog"),pB2=p((A)=>({...A,...A.jobIdentifiers&&{jobIdentifiers:_.SENSITIVE_STRING}}),"BatchDeleteEvaluationJobRequestFilterSensitiveLog"),iB2=p((A)=>({...A,...A.jobIdentifier&&{jobIdentifier:_.SENSITIVE_STRING}}),"BatchDeleteEvaluationJobErrorFilterSensitiveLog"),nB2=p((A)=>({...A,...A.jobIdentifier&&{jobIdentifier:_.SENSITIVE_STRING}}),"BatchDeleteEvaluationJobItemFilterSensitiveLog"),aB2=p((A)=>({...A,...A.errors&&{errors:A.errors.map((B)=>iB2(B))},...A.evaluationJobs&&{evaluationJobs:A.evaluationJobs.map((B)=>nB2(B))}}),"BatchDeleteEvaluationJobResponseFilterSensitiveLog"),TI4=p((A)=>({...A,...A.name&&{name:_.SENSITIVE_STRING},...A.ratingScale&&{ratingScale:A.ratingScale.map((B)=>B)}}),"CustomMetricDefinitionFilterSensitiveLog"),sB2=p((A)=>{if(A.customMetricDefinition!==void 0)return{customMetricDefinition:_.SENSITIVE_STRING};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"AutomatedEvaluationCustomMetricSourceFilterSensitiveLog"),rB2=p((A)=>({...A,...A.customMetrics&&{customMetrics:A.customMetrics.map((B)=>sB2(B))}}),"AutomatedEvaluationCustomMetricConfigFilterSensitiveLog"),oB2=p((A)=>({...A,...A.name&&{name:_.SENSITIVE_STRING},...A.datasetLocation&&{datasetLocation:A.datasetLocation}}),"EvaluationDatasetFilterSensitiveLog"),o60=p((A)=>({...A,...A.dataset&&{dataset:oB2(A.dataset)},...A.metricNames&&{metricNames:_.SENSITIVE_STRING}}),"EvaluationDatasetMetricConfigFilterSensitiveLog"),tB2=p((A)=>({...A,...A.datasetMetricConfigs&&{datasetMetricConfigs:A.datasetMetricConfigs.map((B)=>o60(B))},...A.evaluatorModelConfig&&{evaluatorModelConfig:A.evaluatorModelConfig},...A.customMetricConfig&&{customMetricConfig:rB2(A.customMetricConfig)}}),"AutomatedEvaluationConfigFilterSensitiveLog"),eB2=p((A)=>({...A,...A.name&&{name:_.SENSITIVE_STRING},...A.description&&{description:_.SENSITIVE_STRING}}),"HumanEvaluationCustomMetricFilterSensitiveLog"),A92=p((A)=>({...A,...A.instructions&&{instructions:_.SENSITIVE_STRING}}),"HumanWorkflowConfigFilterSensitiveLog"),B92=p((A)=>({...A,...A.humanWorkflowConfig&&{humanWorkflowConfig:A92(A.humanWorkflowConfig)},...A.customMetrics&&{customMetrics:A.customMetrics.map((B)=>eB2(B))},...A.datasetMetricConfigs&&{datasetMetricConfigs:A.datasetMetricConfigs.map((B)=>o60(B))}}),"HumanEvaluationConfigFilterSensitiveLog"),t60=p((A)=>{if(A.automated!==void 0)return{automated:tB2(A.automated)};if(A.human!==void 0)return{human:B92(A.human)};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"EvaluationConfigFilterSensitiveLog"),Q92=p((A)=>({...A,...A.inferenceParams&&{inferenceParams:_.SENSITIVE_STRING}}),"EvaluationBedrockModelFilterSensitiveLog"),D92=p((A)=>{if(A.bedrockModel!==void 0)return{bedrockModel:Q92(A.bedrockModel)};if(A.precomputedInferenceSource!==void 0)return{precomputedInferenceSource:A.precomputedInferenceSource};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"EvaluationModelConfigFilterSensitiveLog"),e60=p((A)=>({...A,...A.textPromptTemplate&&{textPromptTemplate:_.SENSITIVE_STRING}}),"PromptTemplateFilterSensitiveLog"),Z92=p((A)=>({...A,...A.promptTemplate&&{promptTemplate:e60(A.promptTemplate)}}),"ExternalSourcesGenerationConfigurationFilterSensitiveLog"),G92=p((A)=>({...A,...A.identifier&&{identifier:_.SENSITIVE_STRING},...A.data&&{data:_.SENSITIVE_STRING}}),"ByteContentDocFilterSensitiveLog"),F92=p((A)=>({...A,...A.byteContent&&{byteContent:G92(A.byteContent)}}),"ExternalSourceFilterSensitiveLog"),I92=p((A)=>({...A,...A.sources&&{sources:A.sources.map((B)=>F92(B))},...A.generationConfiguration&&{generationConfiguration:Z92(A.generationConfiguration)}}),"ExternalSourcesRetrieveAndGenerateConfigurationFilterSensitiveLog"),Y92=p((A)=>({...A,...A.promptTemplate&&{promptTemplate:e60(A.promptTemplate)}}),"GenerationConfigurationFilterSensitiveLog"),PI4=p((A)=>({...A}),"MetadataAttributeSchemaFilterSensitiveLog"),W92=p((A)=>({...A,...A.metadataAttributes&&{metadataAttributes:_.SENSITIVE_STRING}}),"ImplicitFilterConfigurationFilterSensitiveLog"),J92=p((A)=>{if(A.fieldsToInclude!==void 0)return{fieldsToInclude:_.SENSITIVE_STRING};if(A.fieldsToExclude!==void 0)return{fieldsToExclude:_.SENSITIVE_STRING};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"RerankingMetadataSelectiveModeConfigurationFilterSensitiveLog"),X92=p((A)=>({...A,...A.selectiveModeConfiguration&&{selectiveModeConfiguration:J92(A.selectiveModeConfiguration)}}),"MetadataConfigurationForRerankingFilterSensitiveLog"),V92=p((A)=>({...A,...A.metadataConfiguration&&{metadataConfiguration:X92(A.metadataConfiguration)}}),"VectorSearchBedrockRerankingConfigurationFilterSensitiveLog"),C92=p((A)=>({...A,...A.bedrockRerankingConfiguration&&{bedrockRerankingConfiguration:V92(A.bedrockRerankingConfiguration)}}),"VectorSearchRerankingConfigurationFilterSensitiveLog"),K92=p((A)=>({...A,...A.jobIdentifier&&{jobIdentifier:_.SENSITIVE_STRING}}),"GetEvaluationJobRequestFilterSensitiveLog"),H92=p((A)=>({...A,...A.jobIdentifier&&{jobIdentifier:_.SENSITIVE_STRING}}),"StopEvaluationJobRequestFilterSensitiveLog"),z92=p((A)=>({...A,...A.inputModalities&&{inputModalities:_.SENSITIVE_STRING},...A.outputModalities&&{outputModalities:_.SENSITIVE_STRING},...A.inputAction&&{inputAction:_.SENSITIVE_STRING},...A.outputAction&&{outputAction:_.SENSITIVE_STRING}}),"GuardrailContentFilterConfigFilterSensitiveLog"),E92=p((A)=>({...A,...A.tierName&&{tierName:_.SENSITIVE_STRING}}),"GuardrailContentFiltersTierConfigFilterSensitiveLog"),A80=p((A)=>({...A,...A.filtersConfig&&{filtersConfig:A.filtersConfig.map((B)=>z92(B))},...A.tierConfig&&{tierConfig:E92(A.tierConfig)}}),"GuardrailContentPolicyConfigFilterSensitiveLog"),U92=p((A)=>({...A,...A.action&&{action:_.SENSITIVE_STRING}}),"GuardrailContextualGroundingFilterConfigFilterSensitiveLog"),B80=p((A)=>({...A,...A.filtersConfig&&{filtersConfig:A.filtersConfig.map((B)=>U92(B))}}),"GuardrailContextualGroundingPolicyConfigFilterSensitiveLog"),w92=p((A)=>({...A,...A.tierName&&{tierName:_.SENSITIVE_STRING}}),"GuardrailTopicsTierConfigFilterSensitiveLog"),$92=p((A)=>({...A,...A.name&&{name:_.SENSITIVE_STRING},...A.definition&&{definition:_.SENSITIVE_STRING},...A.examples&&{examples:_.SENSITIVE_STRING},...A.inputAction&&{inputAction:_.SENSITIVE_STRING},...A.outputAction&&{outputAction:_.SENSITIVE_STRING}}),"GuardrailTopicConfigFilterSensitiveLog"),Q80=p((A)=>({...A,...A.topicsConfig&&{topicsConfig:A.topicsConfig.map((B)=>$92(B))},...A.tierConfig&&{tierConfig:w92(A.tierConfig)}}),"GuardrailTopicPolicyConfigFilterSensitiveLog"),q92=p((A)=>({...A,...A.inputAction&&{inputAction:_.SENSITIVE_STRING},...A.outputAction&&{outputAction:_.SENSITIVE_STRING}}),"GuardrailManagedWordsConfigFilterSensitiveLog"),N92=p((A)=>({...A,...A.inputAction&&{inputAction:_.SENSITIVE_STRING},...A.outputAction&&{outputAction:_.SENSITIVE_STRING}}),"GuardrailWordConfigFilterSensitiveLog"),D80=p((A)=>({...A,...A.wordsConfig&&{wordsConfig:A.wordsConfig.map((B)=>N92(B))},...A.managedWordListsConfig&&{managedWordListsConfig:A.managedWordListsConfig.map((B)=>q92(B))}}),"GuardrailWordPolicyConfigFilterSensitiveLog"),L92=p((A)=>({...A,...A.name&&{name:_.SENSITIVE_STRING},...A.description&&{description:_.SENSITIVE_STRING},...A.topicPolicyConfig&&{topicPolicyConfig:Q80(A.topicPolicyConfig)},...A.contentPolicyConfig&&{contentPolicyConfig:A80(A.contentPolicyConfig)},...A.wordPolicyConfig&&{wordPolicyConfig:D80(A.wordPolicyConfig)},...A.contextualGroundingPolicyConfig&&{contextualGroundingPolicyConfig:B80(A.contextualGroundingPolicyConfig)},...A.blockedInputMessaging&&{blockedInputMessaging:_.SENSITIVE_STRING},...A.blockedOutputsMessaging&&{blockedOutputsMessaging:_.SENSITIVE_STRING}}),"CreateGuardrailRequestFilterSensitiveLog"),M92=p((A)=>({...A,...A.description&&{description:_.SENSITIVE_STRING}}),"CreateGuardrailVersionRequestFilterSensitiveLog"),R92=p((A)=>({...A,...A.inputModalities&&{inputModalities:_.SENSITIVE_STRING},...A.outputModalities&&{outputModalities:_.SENSITIVE_STRING},...A.inputAction&&{inputAction:_.SENSITIVE_STRING},...A.outputAction&&{outputAction:_.SENSITIVE_STRING}}),"GuardrailContentFilterFilterSensitiveLog"),O92=p((A)=>({...A,...A.tierName&&{tierName:_.SENSITIVE_STRING}}),"GuardrailContentFiltersTierFilterSensitiveLog"),T92=p((A)=>({...A,...A.filters&&{filters:A.filters.map((B)=>R92(B))},...A.tier&&{tier:O92(A.tier)}}),"GuardrailContentPolicyFilterSensitiveLog"),P92=p((A)=>({...A,...A.action&&{action:_.SENSITIVE_STRING}}),"GuardrailContextualGroundingFilterFilterSensitiveLog"),S92=p((A)=>({...A,...A.filters&&{filters:A.filters.map((B)=>P92(B))}}),"GuardrailContextualGroundingPolicyFilterSensitiveLog"),j92=p((A)=>({...A,...A.tierName&&{tierName:_.SENSITIVE_STRING}}),"GuardrailTopicsTierFilterSensitiveLog"),y92=p((A)=>({...A,...A.name&&{name:_.SENSITIVE_STRING},...A.definition&&{definition:_.SENSITIVE_STRING},...A.examples&&{examples:_.SENSITIVE_STRING},...A.inputAction&&{inputAction:_.SENSITIVE_STRING},...A.outputAction&&{outputAction:_.SENSITIVE_STRING}}),"GuardrailTopicFilterSensitiveLog"),k92=p((A)=>({...A,...A.topics&&{topics:A.topics.map((B)=>y92(B))},...A.tier&&{tier:j92(A.tier)}}),"GuardrailTopicPolicyFilterSensitiveLog"),_92=p((A)=>({...A,...A.inputAction&&{inputAction:_.SENSITIVE_STRING},...A.outputAction&&{outputAction:_.SENSITIVE_STRING}}),"GuardrailManagedWordsFilterSensitiveLog"),x92=p((A)=>({...A,...A.inputAction&&{inputAction:_.SENSITIVE_STRING},...A.outputAction&&{outputAction:_.SENSITIVE_STRING}}),"GuardrailWordFilterSensitiveLog"),v92=p((A)=>({...A,...A.words&&{words:A.words.map((B)=>x92(B))},...A.managedWordLists&&{managedWordLists:A.managedWordLists.map((B)=>_92(B))}}),"GuardrailWordPolicyFilterSensitiveLog"),b92=p((A)=>({...A,...A.name&&{name:_.SENSITIVE_STRING},...A.description&&{description:_.SENSITIVE_STRING},...A.topicPolicy&&{topicPolicy:k92(A.topicPolicy)},...A.contentPolicy&&{contentPolicy:T92(A.contentPolicy)},...A.wordPolicy&&{wordPolicy:v92(A.wordPolicy)},...A.contextualGroundingPolicy&&{contextualGroundingPolicy:S92(A.contextualGroundingPolicy)},...A.statusReasons&&{statusReasons:_.SENSITIVE_STRING},...A.failureRecommendations&&{failureRecommendations:_.SENSITIVE_STRING},...A.blockedInputMessaging&&{blockedInputMessaging:_.SENSITIVE_STRING},...A.blockedOutputsMessaging&&{blockedOutputsMessaging:_.SENSITIVE_STRING}}),"GetGuardrailResponseFilterSensitiveLog"),f92=p((A)=>({...A,...A.name&&{name:_.SENSITIVE_STRING},...A.description&&{description:_.SENSITIVE_STRING}}),"GuardrailSummaryFilterSensitiveLog"),h92=p((A)=>({...A,...A.guardrails&&{guardrails:A.guardrails.map((B)=>f92(B))}}),"ListGuardrailsResponseFilterSensitiveLog"),g92=p((A)=>({...A,...A.name&&{name:_.SENSITIVE_STRING},...A.description&&{description:_.SENSITIVE_STRING},...A.topicPolicyConfig&&{topicPolicyConfig:Q80(A.topicPolicyConfig)},...A.contentPolicyConfig&&{contentPolicyConfig:A80(A.contentPolicyConfig)},...A.wordPolicyConfig&&{wordPolicyConfig:D80(A.wordPolicyConfig)},...A.contextualGroundingPolicyConfig&&{contextualGroundingPolicyConfig:B80(A.contextualGroundingPolicyConfig)},...A.blockedInputMessaging&&{blockedInputMessaging:_.SENSITIVE_STRING},...A.blockedOutputsMessaging&&{blockedOutputsMessaging:_.SENSITIVE_STRING}}),"UpdateGuardrailRequestFilterSensitiveLog"),u92=p((A)=>({...A,...A.description&&{description:_.SENSITIVE_STRING},...A.modelSource&&{modelSource:A.modelSource}}),"CreateInferenceProfileRequestFilterSensitiveLog"),m92=p((A)=>({...A,...A.description&&{description:_.SENSITIVE_STRING}}),"GetInferenceProfileResponseFilterSensitiveLog"),d92=p((A)=>({...A,...A.description&&{description:_.SENSITIVE_STRING}}),"InferenceProfileSummaryFilterSensitiveLog"),c92=p((A)=>({...A,...A.inferenceProfileSummaries&&{inferenceProfileSummaries:A.inferenceProfileSummaries.map((B)=>d92(B))}}),"ListInferenceProfilesResponseFilterSensitiveLog"),l92=p((A)=>({...A,...A.message&&{message:_.SENSITIVE_STRING},...A.inputDataConfig&&{inputDataConfig:A.inputDataConfig},...A.outputDataConfig&&{outputDataConfig:A.outputDataConfig}}),"GetModelInvocationJobResponseFilterSensitiveLog"),p92=p((A)=>({...A,...A.message&&{message:_.SENSITIVE_STRING},...A.inputDataConfig&&{inputDataConfig:A.inputDataConfig},...A.outputDataConfig&&{outputDataConfig:A.outputDataConfig}}),"ModelInvocationJobSummaryFilterSensitiveLog"),i92=p((A)=>({...A,...A.invocationJobSummaries&&{invocationJobSummaries:A.invocationJobSummaries.map((B)=>p92(B))}}),"ListModelInvocationJobsResponseFilterSensitiveLog"),n92=p((A)=>({...A,...A.description&&{description:_.SENSITIVE_STRING}}),"CreatePromptRouterRequestFilterSensitiveLog"),a92=p((A)=>({...A,...A.description&&{description:_.SENSITIVE_STRING}}),"GetPromptRouterResponseFilterSensitiveLog"),s92=p((A)=>({...A,...A.description&&{description:_.SENSITIVE_STRING}}),"PromptRouterSummaryFilterSensitiveLog"),r92=p((A)=>({...A,...A.promptRouterSummaries&&{promptRouterSummaries:A.promptRouterSummaries.map((B)=>s92(B))}}),"ListPromptRoutersResponseFilterSensitiveLog"),VB=CI(),cz=RQ1(),SI4={AVAILABLE:"AVAILABLE",NOT_AVAILABLE:"NOT_AVAILABLE"},jI4={AVAILABLE:"AVAILABLE",NOT_AVAILABLE:"NOT_AVAILABLE"},yI4={ALL:"ALL",PUBLIC:"PUBLIC"},kI4={COMPLETED:"Completed",FAILED:"Failed",IN_PROGRESS:"InProgress",STOPPED:"Stopped",STOPPING:"Stopping"},_I4={COMPLETED:"Completed",FAILED:"Failed",IN_PROGRESS:"InProgress",NOT_STARTED:"NotStarted",STOPPED:"Stopped",STOPPING:"Stopping"},xI4={COMPLETED:"Completed",FAILED:"Failed",IN_PROGRESS:"InProgress",STOPPED:"Stopped",STOPPING:"Stopping"},Zq1;((A)=>{A.visit=p((B,Q)=>{if(B.equals!==void 0)return Q.equals(B.equals);if(B.notEquals!==void 0)return Q.notEquals(B.notEquals);if(B.greaterThan!==void 0)return Q.greaterThan(B.greaterThan);if(B.greaterThanOrEquals!==void 0)return Q.greaterThanOrEquals(B.greaterThanOrEquals);if(B.lessThan!==void 0)return Q.lessThan(B.lessThan);if(B.lessThanOrEquals!==void 0)return Q.lessThanOrEquals(B.lessThanOrEquals);if(B.in!==void 0)return Q.in(B.in);if(B.notIn!==void 0)return Q.notIn(B.notIn);if(B.startsWith!==void 0)return Q.startsWith(B.startsWith);if(B.listContains!==void 0)return Q.listContains(B.listContains);if(B.stringContains!==void 0)return Q.stringContains(B.stringContains);if(B.andAll!==void 0)return Q.andAll(B.andAll);if(B.orAll!==void 0)return Q.orAll(B.orAll);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(Zq1||(Zq1={}));var Gq1;((A)=>{A.visit=p((B,Q)=>{if(B.retrieveConfig!==void 0)return Q.retrieveConfig(B.retrieveConfig);if(B.retrieveAndGenerateConfig!==void 0)return Q.retrieveAndGenerateConfig(B.retrieveAndGenerateConfig);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(Gq1||(Gq1={}));var Fq1;((A)=>{A.visit=p((B,Q)=>{if(B.knowledgeBaseConfig!==void 0)return Q.knowledgeBaseConfig(B.knowledgeBaseConfig);if(B.precomputedRagSourceConfig!==void 0)return Q.precomputedRagSourceConfig(B.precomputedRagSourceConfig);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(Fq1||(Fq1={}));var Iq1;((A)=>{A.visit=p((B,Q)=>{if(B.models!==void 0)return Q.models(B.models);if(B.ragConfigs!==void 0)return Q.ragConfigs(B.ragConfigs);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(Iq1||(Iq1={}));var o92=p((A)=>({...A,...A.trainingDataConfig&&{trainingDataConfig:Jq1(A.trainingDataConfig)},...A.customizationConfig&&{customizationConfig:A.customizationConfig}}),"CreateModelCustomizationJobRequestFilterSensitiveLog"),t92=p((A)=>({...A,...A.trainingDataConfig&&{trainingDataConfig:Jq1(A.trainingDataConfig)},...A.customizationConfig&&{customizationConfig:A.customizationConfig}}),"GetModelCustomizationJobResponseFilterSensitiveLog"),vI4=p((A)=>{if(A.equals!==void 0)return{equals:A.equals};if(A.notEquals!==void 0)return{notEquals:A.notEquals};if(A.greaterThan!==void 0)return{greaterThan:A.greaterThan};if(A.greaterThanOrEquals!==void 0)return{greaterThanOrEquals:A.greaterThanOrEquals};if(A.lessThan!==void 0)return{lessThan:A.lessThan};if(A.lessThanOrEquals!==void 0)return{lessThanOrEquals:A.lessThanOrEquals};if(A.in!==void 0)return{in:A.in};if(A.notIn!==void 0)return{notIn:A.notIn};if(A.startsWith!==void 0)return{startsWith:A.startsWith};if(A.listContains!==void 0)return{listContains:A.listContains};if(A.stringContains!==void 0)return{stringContains:A.stringContains};if(A.andAll!==void 0)return{andAll:_.SENSITIVE_STRING};if(A.orAll!==void 0)return{orAll:_.SENSITIVE_STRING};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"RetrievalFilterFilterSensitiveLog"),e92=p((A)=>({...A,...A.filter&&{filter:_.SENSITIVE_STRING},...A.implicitFilterConfiguration&&{implicitFilterConfiguration:W92(A.implicitFilterConfiguration)},...A.rerankingConfiguration&&{rerankingConfiguration:C92(A.rerankingConfiguration)}}),"KnowledgeBaseVectorSearchConfigurationFilterSensitiveLog"),Z80=p((A)=>({...A,...A.vectorSearchConfiguration&&{vectorSearchConfiguration:e92(A.vectorSearchConfiguration)}}),"KnowledgeBaseRetrievalConfigurationFilterSensitiveLog"),AQ2=p((A)=>({...A,...A.retrievalConfiguration&&{retrievalConfiguration:Z80(A.retrievalConfiguration)},...A.generationConfiguration&&{generationConfiguration:Y92(A.generationConfiguration)}}),"KnowledgeBaseRetrieveAndGenerateConfigurationFilterSensitiveLog"),BQ2=p((A)=>({...A,...A.knowledgeBaseRetrievalConfiguration&&{knowledgeBaseRetrievalConfiguration:Z80(A.knowledgeBaseRetrievalConfiguration)}}),"RetrieveConfigFilterSensitiveLog"),QQ2=p((A)=>({...A,...A.knowledgeBaseConfiguration&&{knowledgeBaseConfiguration:AQ2(A.knowledgeBaseConfiguration)},...A.externalSourcesConfiguration&&{externalSourcesConfiguration:I92(A.externalSourcesConfiguration)}}),"RetrieveAndGenerateConfigurationFilterSensitiveLog"),DQ2=p((A)=>{if(A.retrieveConfig!==void 0)return{retrieveConfig:BQ2(A.retrieveConfig)};if(A.retrieveAndGenerateConfig!==void 0)return{retrieveAndGenerateConfig:QQ2(A.retrieveAndGenerateConfig)};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"KnowledgeBaseConfigFilterSensitiveLog"),ZQ2=p((A)=>{if(A.knowledgeBaseConfig!==void 0)return{knowledgeBaseConfig:DQ2(A.knowledgeBaseConfig)};if(A.precomputedRagSourceConfig!==void 0)return{precomputedRagSourceConfig:A.precomputedRagSourceConfig};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"RAGConfigFilterSensitiveLog"),G80=p((A)=>{if(A.models!==void 0)return{models:A.models.map((B)=>D92(B))};if(A.ragConfigs!==void 0)return{ragConfigs:A.ragConfigs.map((B)=>ZQ2(B))};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"EvaluationInferenceConfigFilterSensitiveLog"),GQ2=p((A)=>({...A,...A.jobDescription&&{jobDescription:_.SENSITIVE_STRING},...A.evaluationConfig&&{evaluationConfig:t60(A.evaluationConfig)},...A.inferenceConfig&&{inferenceConfig:G80(A.inferenceConfig)}}),"CreateEvaluationJobRequestFilterSensitiveLog"),FQ2=p((A)=>({...A,...A.jobDescription&&{jobDescription:_.SENSITIVE_STRING},...A.evaluationConfig&&{evaluationConfig:t60(A.evaluationConfig)},...A.inferenceConfig&&{inferenceConfig:G80(A.inferenceConfig)}}),"GetEvaluationJobResponseFilterSensitiveLog"),bI4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/evaluation-jobs/batch-delete");let Z;return Z=JSON.stringify(_.take(A,{jobIdentifiers:p((G)=>_._json(G),"jobIdentifiers")})),Q.m("POST").h(D).b(Z),Q.build()},"se_BatchDeleteEvaluationJobCommand"),fI4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/custom-models/create-custom-model");let Z;return Z=JSON.stringify(_.take(A,{clientRequestToken:[!0,(G)=>G??cz.v4()],modelKmsKeyArn:[],modelName:[],modelSourceConfig:p((G)=>_._json(G),"modelSourceConfig"),modelTags:p((G)=>_._json(G),"modelTags"),roleArn:[]})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateCustomModelCommand"),hI4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/evaluation-jobs");let Z;return Z=JSON.stringify(_.take(A,{applicationType:[],clientRequestToken:[!0,(G)=>G??cz.v4()],customerEncryptionKeyId:[],evaluationConfig:p((G)=>LJ4(G,B),"evaluationConfig"),inferenceConfig:p((G)=>MJ4(G,B),"inferenceConfig"),jobDescription:[],jobName:[],jobTags:p((G)=>_._json(G),"jobTags"),outputDataConfig:p((G)=>_._json(G),"outputDataConfig"),roleArn:[]})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateEvaluationJobCommand"),gI4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/create-foundation-model-agreement");let Z;return Z=JSON.stringify(_.take(A,{modelId:[],offerToken:[]})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateFoundationModelAgreementCommand"),uI4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/guardrails");let Z;return Z=JSON.stringify(_.take(A,{blockedInputMessaging:[],blockedOutputsMessaging:[],clientRequestToken:[!0,(G)=>G??cz.v4()],contentPolicyConfig:p((G)=>_._json(G),"contentPolicyConfig"),contextualGroundingPolicyConfig:p((G)=>IQ2(G,B),"contextualGroundingPolicyConfig"),crossRegionConfig:p((G)=>_._json(G),"crossRegionConfig"),description:[],kmsKeyId:[],name:[],sensitiveInformationPolicyConfig:p((G)=>_._json(G),"sensitiveInformationPolicyConfig"),tags:p((G)=>_._json(G),"tags"),topicPolicyConfig:p((G)=>_._json(G),"topicPolicyConfig"),wordPolicyConfig:p((G)=>_._json(G),"wordPolicyConfig")})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateGuardrailCommand"),mI4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/guardrails/{guardrailIdentifier}"),Q.p("guardrailIdentifier",()=>A.guardrailIdentifier,"{guardrailIdentifier}",!1);let Z;return Z=JSON.stringify(_.take(A,{clientRequestToken:[!0,(G)=>G??cz.v4()],description:[]})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateGuardrailVersionCommand"),dI4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/inference-profiles");let Z;return Z=JSON.stringify(_.take(A,{clientRequestToken:[!0,(G)=>G??cz.v4()],description:[],inferenceProfileName:[],modelSource:p((G)=>_._json(G),"modelSource"),tags:p((G)=>_._json(G),"tags")})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateInferenceProfileCommand"),cI4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/marketplace-model/endpoints");let Z;return Z=JSON.stringify(_.take(A,{acceptEula:[],clientRequestToken:[!0,(G)=>G??cz.v4()],endpointConfig:p((G)=>_._json(G),"endpointConfig"),endpointName:[],modelSourceIdentifier:[],tags:p((G)=>_._json(G),"tags")})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateMarketplaceModelEndpointCommand"),lI4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/model-copy-jobs");let Z;return Z=JSON.stringify(_.take(A,{clientRequestToken:[!0,(G)=>G??cz.v4()],modelKmsKeyId:[],sourceModelArn:[],targetModelName:[],targetModelTags:p((G)=>_._json(G),"targetModelTags")})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateModelCopyJobCommand"),pI4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/model-customization-jobs");let Z;return Z=JSON.stringify(_.take(A,{baseModelIdentifier:[],clientRequestToken:[!0,(G)=>G??cz.v4()],customModelKmsKeyId:[],customModelName:[],customModelTags:p((G)=>_._json(G),"customModelTags"),customizationConfig:p((G)=>_._json(G),"customizationConfig"),customizationType:[],hyperParameters:p((G)=>_._json(G),"hyperParameters"),jobName:[],jobTags:p((G)=>_._json(G),"jobTags"),outputDataConfig:p((G)=>_._json(G),"outputDataConfig"),roleArn:[],trainingDataConfig:p((G)=>_._json(G),"trainingDataConfig"),validationDataConfig:p((G)=>_._json(G),"validationDataConfig"),vpcConfig:p((G)=>_._json(G),"vpcConfig")})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateModelCustomizationJobCommand"),iI4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/model-import-jobs");let Z;return Z=JSON.stringify(_.take(A,{clientRequestToken:[],importedModelKmsKeyId:[],importedModelName:[],importedModelTags:p((G)=>_._json(G),"importedModelTags"),jobName:[],jobTags:p((G)=>_._json(G),"jobTags"),modelDataSource:p((G)=>_._json(G),"modelDataSource"),roleArn:[],vpcConfig:p((G)=>_._json(G),"vpcConfig")})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateModelImportJobCommand"),nI4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/model-invocation-job");let Z;return Z=JSON.stringify(_.take(A,{clientRequestToken:[!0,(G)=>G??cz.v4()],inputDataConfig:p((G)=>_._json(G),"inputDataConfig"),jobName:[],modelId:[],outputDataConfig:p((G)=>_._json(G),"outputDataConfig"),roleArn:[],tags:p((G)=>_._json(G),"tags"),timeoutDurationInHours:[],vpcConfig:p((G)=>_._json(G),"vpcConfig")})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateModelInvocationJobCommand"),aI4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/prompt-routers");let Z;return Z=JSON.stringify(_.take(A,{clientRequestToken:[!0,(G)=>G??cz.v4()],description:[],fallbackModel:p((G)=>_._json(G),"fallbackModel"),models:p((G)=>_._json(G),"models"),promptRouterName:[],routingCriteria:p((G)=>cJ4(G,B),"routingCriteria"),tags:p((G)=>_._json(G),"tags")})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreatePromptRouterCommand"),sI4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/provisioned-model-throughput");let Z;return Z=JSON.stringify(_.take(A,{clientRequestToken:[!0,(G)=>G??cz.v4()],commitmentDuration:[],modelId:[],modelUnits:[],provisionedModelName:[],tags:p((G)=>_._json(G),"tags")})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateProvisionedModelThroughputCommand"),rI4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/custom-models/{modelIdentifier}"),Q.p("modelIdentifier",()=>A.modelIdentifier,"{modelIdentifier}",!1);let Z;return Q.m("DELETE").h(D).b(Z),Q.build()},"se_DeleteCustomModelCommand"),oI4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/delete-foundation-model-agreement");let Z;return Z=JSON.stringify(_.take(A,{modelId:[]})),Q.m("POST").h(D).b(Z),Q.build()},"se_DeleteFoundationModelAgreementCommand"),tI4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/guardrails/{guardrailIdentifier}"),Q.p("guardrailIdentifier",()=>A.guardrailIdentifier,"{guardrailIdentifier}",!1);let Z=_.map({[Yq1]:[,A[Yq1]]}),G;return Q.m("DELETE").h(D).q(Z).b(G),Q.build()},"se_DeleteGuardrailCommand"),eI4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/imported-models/{modelIdentifier}"),Q.p("modelIdentifier",()=>A.modelIdentifier,"{modelIdentifier}",!1);let Z;return Q.m("DELETE").h(D).b(Z),Q.build()},"se_DeleteImportedModelCommand"),AY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/inference-profiles/{inferenceProfileIdentifier}"),Q.p("inferenceProfileIdentifier",()=>A.inferenceProfileIdentifier,"{inferenceProfileIdentifier}",!1);let Z;return Q.m("DELETE").h(D).b(Z),Q.build()},"se_DeleteInferenceProfileCommand"),BY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/marketplace-model/endpoints/{endpointArn}"),Q.p("endpointArn",()=>A.endpointArn,"{endpointArn}",!1);let Z;return Q.m("DELETE").h(D).b(Z),Q.build()},"se_DeleteMarketplaceModelEndpointCommand"),QY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/logging/modelinvocations");let Z;return Q.m("DELETE").h(D).b(Z),Q.build()},"se_DeleteModelInvocationLoggingConfigurationCommand"),DY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/prompt-routers/{promptRouterArn}"),Q.p("promptRouterArn",()=>A.promptRouterArn,"{promptRouterArn}",!1);let Z;return Q.m("DELETE").h(D).b(Z),Q.build()},"se_DeletePromptRouterCommand"),ZY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/provisioned-model-throughput/{provisionedModelId}"),Q.p("provisionedModelId",()=>A.provisionedModelId,"{provisionedModelId}",!1);let Z;return Q.m("DELETE").h(D).b(Z),Q.build()},"se_DeleteProvisionedModelThroughputCommand"),GY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/marketplace-model/endpoints/{endpointArn}/registration"),Q.p("endpointArn",()=>A.endpointArn,"{endpointArn}",!1);let Z;return Q.m("DELETE").h(D).b(Z),Q.build()},"se_DeregisterMarketplaceModelEndpointCommand"),FY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/custom-models/{modelIdentifier}"),Q.p("modelIdentifier",()=>A.modelIdentifier,"{modelIdentifier}",!1);let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetCustomModelCommand"),IY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/evaluation-jobs/{jobIdentifier}"),Q.p("jobIdentifier",()=>A.jobIdentifier,"{jobIdentifier}",!1);let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetEvaluationJobCommand"),YY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/foundation-models/{modelIdentifier}"),Q.p("modelIdentifier",()=>A.modelIdentifier,"{modelIdentifier}",!1);let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetFoundationModelCommand"),WY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/foundation-model-availability/{modelId}"),Q.p("modelId",()=>A.modelId,"{modelId}",!1);let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetFoundationModelAvailabilityCommand"),JY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/guardrails/{guardrailIdentifier}"),Q.p("guardrailIdentifier",()=>A.guardrailIdentifier,"{guardrailIdentifier}",!1);let Z=_.map({[Yq1]:[,A[Yq1]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_GetGuardrailCommand"),XY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/imported-models/{modelIdentifier}"),Q.p("modelIdentifier",()=>A.modelIdentifier,"{modelIdentifier}",!1);let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetImportedModelCommand"),VY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/inference-profiles/{inferenceProfileIdentifier}"),Q.p("inferenceProfileIdentifier",()=>A.inferenceProfileIdentifier,"{inferenceProfileIdentifier}",!1);let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetInferenceProfileCommand"),CY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/marketplace-model/endpoints/{endpointArn}"),Q.p("endpointArn",()=>A.endpointArn,"{endpointArn}",!1);let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetMarketplaceModelEndpointCommand"),KY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/model-copy-jobs/{jobArn}"),Q.p("jobArn",()=>A.jobArn,"{jobArn}",!1);let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetModelCopyJobCommand"),HY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/model-customization-jobs/{jobIdentifier}"),Q.p("jobIdentifier",()=>A.jobIdentifier,"{jobIdentifier}",!1);let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetModelCustomizationJobCommand"),zY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/model-import-jobs/{jobIdentifier}"),Q.p("jobIdentifier",()=>A.jobIdentifier,"{jobIdentifier}",!1);let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetModelImportJobCommand"),EY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/model-invocation-job/{jobIdentifier}"),Q.p("jobIdentifier",()=>A.jobIdentifier,"{jobIdentifier}",!1);let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetModelInvocationJobCommand"),UY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/logging/modelinvocations");let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetModelInvocationLoggingConfigurationCommand"),wY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/prompt-routers/{promptRouterArn}"),Q.p("promptRouterArn",()=>A.promptRouterArn,"{promptRouterArn}",!1);let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetPromptRouterCommand"),$Y4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/provisioned-model-throughput/{provisionedModelId}"),Q.p("provisionedModelId",()=>A.provisionedModelId,"{provisionedModelId}",!1);let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetProvisionedModelThroughputCommand"),qY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/use-case-for-model-access");let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetUseCaseForModelAccessCommand"),NY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/custom-models");let Z=_.map({[NV]:[()=>A.creationTimeBefore!==void 0,()=>_.serializeDateTime(A[NV]).toString()],[qV]:[()=>A.creationTimeAfter!==void 0,()=>_.serializeDateTime(A[qV]).toString()],[LV]:[,A[LV]],[wB2]:[,A[wB2]],[NB2]:[,A[NB2]],[wD]:[()=>A.maxResults!==void 0,()=>A[wD].toString()],[$D]:[,A[$D]],[sW]:[,A[sW]],[rW]:[,A[rW]],[MB2]:[()=>A.isOwned!==void 0,()=>A[MB2].toString()],[OB2]:[,A[OB2]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListCustomModelsCommand"),LY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/evaluation-jobs");let Z=_.map({[qV]:[()=>A.creationTimeAfter!==void 0,()=>_.serializeDateTime(A[qV]).toString()],[NV]:[()=>A.creationTimeBefore!==void 0,()=>_.serializeDateTime(A[NV]).toString()],[dz]:[,A[dz]],[zB2]:[,A[zB2]],[LV]:[,A[LV]],[wD]:[()=>A.maxResults!==void 0,()=>A[wD].toString()],[$D]:[,A[$D]],[sW]:[,A[sW]],[rW]:[,A[rW]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListEvaluationJobsCommand"),MY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/list-foundation-model-agreement-offers/{modelId}"),Q.p("modelId",()=>A.modelId,"{modelId}",!1);let Z=_.map({[TB2]:[,A[TB2]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListFoundationModelAgreementOffersCommand"),RY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/foundation-models");let Z=_.map({[qB2]:[,A[qB2]],[EB2]:[,A[EB2]],[$B2]:[,A[$B2]],[UB2]:[,A[UB2]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListFoundationModelsCommand"),OY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/guardrails");let Z=_.map({[LB2]:[,A[LB2]],[wD]:[()=>A.maxResults!==void 0,()=>A[wD].toString()],[$D]:[,A[$D]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListGuardrailsCommand"),TY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/imported-models");let Z=_.map({[NV]:[()=>A.creationTimeBefore!==void 0,()=>_.serializeDateTime(A[NV]).toString()],[qV]:[()=>A.creationTimeAfter!==void 0,()=>_.serializeDateTime(A[qV]).toString()],[LV]:[,A[LV]],[wD]:[()=>A.maxResults!==void 0,()=>A[wD].toString()],[$D]:[,A[$D]],[sW]:[,A[sW]],[rW]:[,A[rW]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListImportedModelsCommand"),PY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/inference-profiles");let Z=_.map({[wD]:[()=>A.maxResults!==void 0,()=>A[wD].toString()],[$D]:[,A[$D]],[r60]:[,A[DV4]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListInferenceProfilesCommand"),SY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/marketplace-model/endpoints");let Z=_.map({[wD]:[()=>A.maxResults!==void 0,()=>A[wD].toString()],[$D]:[,A[$D]],[BV4]:[,A[AV4]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListMarketplaceModelEndpointsCommand"),jY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/model-copy-jobs");let Z=_.map({[qV]:[()=>A.creationTimeAfter!==void 0,()=>_.serializeDateTime(A[qV]).toString()],[NV]:[()=>A.creationTimeBefore!==void 0,()=>_.serializeDateTime(A[NV]).toString()],[dz]:[,A[dz]],[PB2]:[,A[PB2]],[SB2]:[,A[SB2]],[QV4]:[,A[ZV4]],[wD]:[()=>A.maxResults!==void 0,()=>A[wD].toString()],[$D]:[,A[$D]],[sW]:[,A[sW]],[rW]:[,A[rW]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListModelCopyJobsCommand"),yY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/model-customization-jobs");let Z=_.map({[qV]:[()=>A.creationTimeAfter!==void 0,()=>_.serializeDateTime(A[qV]).toString()],[NV]:[()=>A.creationTimeBefore!==void 0,()=>_.serializeDateTime(A[NV]).toString()],[dz]:[,A[dz]],[LV]:[,A[LV]],[wD]:[()=>A.maxResults!==void 0,()=>A[wD].toString()],[$D]:[,A[$D]],[sW]:[,A[sW]],[rW]:[,A[rW]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListModelCustomizationJobsCommand"),kY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/model-import-jobs");let Z=_.map({[qV]:[()=>A.creationTimeAfter!==void 0,()=>_.serializeDateTime(A[qV]).toString()],[NV]:[()=>A.creationTimeBefore!==void 0,()=>_.serializeDateTime(A[NV]).toString()],[dz]:[,A[dz]],[LV]:[,A[LV]],[wD]:[()=>A.maxResults!==void 0,()=>A[wD].toString()],[$D]:[,A[$D]],[sW]:[,A[sW]],[rW]:[,A[rW]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListModelImportJobsCommand"),_Y4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/model-invocation-jobs");let Z=_.map({[jB2]:[()=>A.submitTimeAfter!==void 0,()=>_.serializeDateTime(A[jB2]).toString()],[yB2]:[()=>A.submitTimeBefore!==void 0,()=>_.serializeDateTime(A[yB2]).toString()],[dz]:[,A[dz]],[LV]:[,A[LV]],[wD]:[()=>A.maxResults!==void 0,()=>A[wD].toString()],[$D]:[,A[$D]],[sW]:[,A[sW]],[rW]:[,A[rW]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListModelInvocationJobsCommand"),xY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/prompt-routers");let Z=_.map({[wD]:[()=>A.maxResults!==void 0,()=>A[wD].toString()],[$D]:[,A[$D]],[r60]:[,A[r60]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListPromptRoutersCommand"),vY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/provisioned-model-throughputs");let Z=_.map({[qV]:[()=>A.creationTimeAfter!==void 0,()=>_.serializeDateTime(A[qV]).toString()],[NV]:[()=>A.creationTimeBefore!==void 0,()=>_.serializeDateTime(A[NV]).toString()],[dz]:[,A[dz]],[RB2]:[,A[RB2]],[LV]:[,A[LV]],[wD]:[()=>A.maxResults!==void 0,()=>A[wD].toString()],[$D]:[,A[$D]],[sW]:[,A[sW]],[rW]:[,A[rW]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListProvisionedModelThroughputsCommand"),bY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/listTagsForResource");let Z;return Z=JSON.stringify(_.take(A,{resourceARN:[]})),Q.m("POST").h(D).b(Z),Q.build()},"se_ListTagsForResourceCommand"),fY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/logging/modelinvocations");let Z;return Z=JSON.stringify(_.take(A,{loggingConfig:p((G)=>_._json(G),"loggingConfig")})),Q.m("PUT").h(D).b(Z),Q.build()},"se_PutModelInvocationLoggingConfigurationCommand"),hY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/use-case-for-model-access");let Z;return Z=JSON.stringify(_.take(A,{formData:p((G)=>B.base64Encoder(G),"formData")})),Q.m("POST").h(D).b(Z),Q.build()},"se_PutUseCaseForModelAccessCommand"),gY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/marketplace-model/endpoints/{endpointIdentifier}/registration"),Q.p("endpointIdentifier",()=>A.endpointIdentifier,"{endpointIdentifier}",!1);let Z;return Z=JSON.stringify(_.take(A,{modelSourceIdentifier:[]})),Q.m("POST").h(D).b(Z),Q.build()},"se_RegisterMarketplaceModelEndpointCommand"),uY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/evaluation-job/{jobIdentifier}/stop"),Q.p("jobIdentifier",()=>A.jobIdentifier,"{jobIdentifier}",!1);let Z;return Q.m("POST").h(D).b(Z),Q.build()},"se_StopEvaluationJobCommand"),mY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/model-customization-jobs/{jobIdentifier}/stop"),Q.p("jobIdentifier",()=>A.jobIdentifier,"{jobIdentifier}",!1);let Z;return Q.m("POST").h(D).b(Z),Q.build()},"se_StopModelCustomizationJobCommand"),dY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={};Q.bp("/model-invocation-job/{jobIdentifier}/stop"),Q.p("jobIdentifier",()=>A.jobIdentifier,"{jobIdentifier}",!1);let Z;return Q.m("POST").h(D).b(Z),Q.build()},"se_StopModelInvocationJobCommand"),cY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/tagResource");let Z;return Z=JSON.stringify(_.take(A,{resourceARN:[],tags:p((G)=>_._json(G),"tags")})),Q.m("POST").h(D).b(Z),Q.build()},"se_TagResourceCommand"),lY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/untagResource");let Z;return Z=JSON.stringify(_.take(A,{resourceARN:[],tagKeys:p((G)=>_._json(G),"tagKeys")})),Q.m("POST").h(D).b(Z),Q.build()},"se_UntagResourceCommand"),pY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/guardrails/{guardrailIdentifier}"),Q.p("guardrailIdentifier",()=>A.guardrailIdentifier,"{guardrailIdentifier}",!1);let Z;return Z=JSON.stringify(_.take(A,{blockedInputMessaging:[],blockedOutputsMessaging:[],contentPolicyConfig:p((G)=>_._json(G),"contentPolicyConfig"),contextualGroundingPolicyConfig:p((G)=>IQ2(G,B),"contextualGroundingPolicyConfig"),crossRegionConfig:p((G)=>_._json(G),"crossRegionConfig"),description:[],kmsKeyId:[],name:[],sensitiveInformationPolicyConfig:p((G)=>_._json(G),"sensitiveInformationPolicyConfig"),topicPolicyConfig:p((G)=>_._json(G),"topicPolicyConfig"),wordPolicyConfig:p((G)=>_._json(G),"wordPolicyConfig")})),Q.m("PUT").h(D).b(Z),Q.build()},"se_UpdateGuardrailCommand"),iY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/marketplace-model/endpoints/{endpointArn}"),Q.p("endpointArn",()=>A.endpointArn,"{endpointArn}",!1);let Z;return Z=JSON.stringify(_.take(A,{clientRequestToken:[!0,(G)=>G??cz.v4()],endpointConfig:p((G)=>_._json(G),"endpointConfig")})),Q.m("PATCH").h(D).b(Z),Q.build()},"se_UpdateMarketplaceModelEndpointCommand"),nY4=p(async(A,B)=>{let Q=N2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/provisioned-model-throughput/{provisionedModelId}"),Q.p("provisionedModelId",()=>A.provisionedModelId,"{provisionedModelId}",!1);let Z;return Z=JSON.stringify(_.take(A,{desiredModelId:[],desiredProvisionedModelName:[]})),Q.m("PATCH").h(D).b(Z),Q.build()},"se_UpdateProvisionedModelThroughputCommand"),aY4=p(async(A,B)=>{if(A.statusCode!==202&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{errors:_._json,evaluationJobs:_._json});return Object.assign(Q,Z),Q},"de_BatchDeleteEvaluationJobCommand"),sY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{modelArn:_.expectString});return Object.assign(Q,Z),Q},"de_CreateCustomModelCommand"),rY4=p(async(A,B)=>{if(A.statusCode!==202&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{jobArn:_.expectString});return Object.assign(Q,Z),Q},"de_CreateEvaluationJobCommand"),oY4=p(async(A,B)=>{if(A.statusCode!==202&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{modelId:_.expectString});return Object.assign(Q,Z),Q},"de_CreateFoundationModelAgreementCommand"),tY4=p(async(A,B)=>{if(A.statusCode!==202&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{createdAt:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"createdAt"),guardrailArn:_.expectString,guardrailId:_.expectString,version:_.expectString});return Object.assign(Q,Z),Q},"de_CreateGuardrailCommand"),eY4=p(async(A,B)=>{if(A.statusCode!==202&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{guardrailId:_.expectString,version:_.expectString});return Object.assign(Q,Z),Q},"de_CreateGuardrailVersionCommand"),AW4=p(async(A,B)=>{if(A.statusCode!==201&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{inferenceProfileArn:_.expectString,status:_.expectString});return Object.assign(Q,Z),Q},"de_CreateInferenceProfileCommand"),BW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{marketplaceModelEndpoint:p((G)=>Xq1(G,B),"marketplaceModelEndpoint")});return Object.assign(Q,Z),Q},"de_CreateMarketplaceModelEndpointCommand"),QW4=p(async(A,B)=>{if(A.statusCode!==201&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{jobArn:_.expectString});return Object.assign(Q,Z),Q},"de_CreateModelCopyJobCommand"),DW4=p(async(A,B)=>{if(A.statusCode!==201&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{jobArn:_.expectString});return Object.assign(Q,Z),Q},"de_CreateModelCustomizationJobCommand"),ZW4=p(async(A,B)=>{if(A.statusCode!==201&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{jobArn:_.expectString});return Object.assign(Q,Z),Q},"de_CreateModelImportJobCommand"),GW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{jobArn:_.expectString});return Object.assign(Q,Z),Q},"de_CreateModelInvocationJobCommand"),FW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{promptRouterArn:_.expectString});return Object.assign(Q,Z),Q},"de_CreatePromptRouterCommand"),IW4=p(async(A,B)=>{if(A.statusCode!==201&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{provisionedModelArn:_.expectString});return Object.assign(Q,Z),Q},"de_CreateProvisionedModelThroughputCommand"),YW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)});return await _.collectBody(A.body,B),Q},"de_DeleteCustomModelCommand"),WW4=p(async(A,B)=>{if(A.statusCode!==202&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)});return await _.collectBody(A.body,B),Q},"de_DeleteFoundationModelAgreementCommand"),JW4=p(async(A,B)=>{if(A.statusCode!==202&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)});return await _.collectBody(A.body,B),Q},"de_DeleteGuardrailCommand"),XW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)});return await _.collectBody(A.body,B),Q},"de_DeleteImportedModelCommand"),VW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)});return await _.collectBody(A.body,B),Q},"de_DeleteInferenceProfileCommand"),CW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)});return await _.collectBody(A.body,B),Q},"de_DeleteMarketplaceModelEndpointCommand"),KW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)});return await _.collectBody(A.body,B),Q},"de_DeleteModelInvocationLoggingConfigurationCommand"),HW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)});return await _.collectBody(A.body,B),Q},"de_DeletePromptRouterCommand"),zW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)});return await _.collectBody(A.body,B),Q},"de_DeleteProvisionedModelThroughputCommand"),EW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)});return await _.collectBody(A.body,B),Q},"de_DeregisterMarketplaceModelEndpointCommand"),UW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{baseModelArn:_.expectString,creationTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"creationTime"),customizationConfig:p((G)=>_._json(VB.awsExpectUnion(G)),"customizationConfig"),customizationType:_.expectString,failureMessage:_.expectString,hyperParameters:_._json,jobArn:_.expectString,jobName:_.expectString,modelArn:_.expectString,modelKmsKeyArn:_.expectString,modelName:_.expectString,modelStatus:_.expectString,outputDataConfig:_._json,trainingDataConfig:_._json,trainingMetrics:p((G)=>zQ2(G,B),"trainingMetrics"),validationDataConfig:_._json,validationMetrics:p((G)=>EQ2(G,B),"validationMetrics")});return Object.assign(Q,Z),Q},"de_GetCustomModelCommand"),wW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{applicationType:_.expectString,creationTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"creationTime"),customerEncryptionKeyId:_.expectString,evaluationConfig:p((G)=>ZX4(VB.awsExpectUnion(G),B),"evaluationConfig"),failureMessages:_._json,inferenceConfig:p((G)=>GX4(VB.awsExpectUnion(G),B),"inferenceConfig"),jobArn:_.expectString,jobDescription:_.expectString,jobName:_.expectString,jobType:_.expectString,lastModifiedTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"lastModifiedTime"),outputDataConfig:_._json,roleArn:_.expectString,status:_.expectString});return Object.assign(Q,Z),Q},"de_GetEvaluationJobCommand"),$W4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{modelDetails:_._json});return Object.assign(Q,Z),Q},"de_GetFoundationModelCommand"),qW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{agreementAvailability:_._json,authorizationStatus:_.expectString,entitlementAvailability:_.expectString,modelId:_.expectString,regionAvailability:_.expectString});return Object.assign(Q,Z),Q},"de_GetFoundationModelAvailabilityCommand"),NW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{blockedInputMessaging:_.expectString,blockedOutputsMessaging:_.expectString,contentPolicy:_._json,contextualGroundingPolicy:p((G)=>zX4(G,B),"contextualGroundingPolicy"),createdAt:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"createdAt"),crossRegionDetails:_._json,description:_.expectString,failureRecommendations:_._json,guardrailArn:_.expectString,guardrailId:_.expectString,kmsKeyArn:_.expectString,name:_.expectString,sensitiveInformationPolicy:_._json,status:_.expectString,statusReasons:_._json,topicPolicy:_._json,updatedAt:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"updatedAt"),version:_.expectString,wordPolicy:_._json});return Object.assign(Q,Z),Q},"de_GetGuardrailCommand"),LW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{creationTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"creationTime"),customModelUnits:_._json,instructSupported:_.expectBoolean,jobArn:_.expectString,jobName:_.expectString,modelArchitecture:_.expectString,modelArn:_.expectString,modelDataSource:p((G)=>_._json(VB.awsExpectUnion(G)),"modelDataSource"),modelKmsKeyArn:_.expectString,modelName:_.expectString});return Object.assign(Q,Z),Q},"de_GetImportedModelCommand"),MW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{createdAt:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"createdAt"),description:_.expectString,inferenceProfileArn:_.expectString,inferenceProfileId:_.expectString,inferenceProfileName:_.expectString,models:_._json,status:_.expectString,type:_.expectString,updatedAt:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"updatedAt")});return Object.assign(Q,Z),Q},"de_GetInferenceProfileCommand"),RW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{marketplaceModelEndpoint:p((G)=>Xq1(G,B),"marketplaceModelEndpoint")});return Object.assign(Q,Z),Q},"de_GetMarketplaceModelEndpointCommand"),OW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{creationTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"creationTime"),failureMessage:_.expectString,jobArn:_.expectString,sourceAccountId:_.expectString,sourceModelArn:_.expectString,sourceModelName:_.expectString,status:_.expectString,targetModelArn:_.expectString,targetModelKmsKeyArn:_.expectString,targetModelName:_.expectString,targetModelTags:_._json});return Object.assign(Q,Z),Q},"de_GetModelCopyJobCommand"),TW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{baseModelArn:_.expectString,clientRequestToken:_.expectString,creationTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"creationTime"),customizationConfig:p((G)=>_._json(VB.awsExpectUnion(G)),"customizationConfig"),customizationType:_.expectString,endTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"endTime"),failureMessage:_.expectString,hyperParameters:_._json,jobArn:_.expectString,jobName:_.expectString,lastModifiedTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"lastModifiedTime"),outputDataConfig:_._json,outputModelArn:_.expectString,outputModelKmsKeyArn:_.expectString,outputModelName:_.expectString,roleArn:_.expectString,status:_.expectString,statusDetails:p((G)=>HQ2(G,B),"statusDetails"),trainingDataConfig:_._json,trainingMetrics:p((G)=>zQ2(G,B),"trainingMetrics"),validationDataConfig:_._json,validationMetrics:p((G)=>EQ2(G,B),"validationMetrics"),vpcConfig:_._json});return Object.assign(Q,Z),Q},"de_GetModelCustomizationJobCommand"),PW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{creationTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"creationTime"),endTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"endTime"),failureMessage:_.expectString,importedModelArn:_.expectString,importedModelKmsKeyArn:_.expectString,importedModelName:_.expectString,jobArn:_.expectString,jobName:_.expectString,lastModifiedTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"lastModifiedTime"),modelDataSource:p((G)=>_._json(VB.awsExpectUnion(G)),"modelDataSource"),roleArn:_.expectString,status:_.expectString,vpcConfig:_._json});return Object.assign(Q,Z),Q},"de_GetModelImportJobCommand"),SW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{clientRequestToken:_.expectString,endTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"endTime"),inputDataConfig:p((G)=>_._json(VB.awsExpectUnion(G)),"inputDataConfig"),jobArn:_.expectString,jobExpirationTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"jobExpirationTime"),jobName:_.expectString,lastModifiedTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"lastModifiedTime"),message:_.expectString,modelId:_.expectString,outputDataConfig:p((G)=>_._json(VB.awsExpectUnion(G)),"outputDataConfig"),roleArn:_.expectString,status:_.expectString,submitTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"submitTime"),timeoutDurationInHours:_.expectInt32,vpcConfig:_._json});return Object.assign(Q,Z),Q},"de_GetModelInvocationJobCommand"),jW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{loggingConfig:_._json});return Object.assign(Q,Z),Q},"de_GetModelInvocationLoggingConfigurationCommand"),yW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{createdAt:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"createdAt"),description:_.expectString,fallbackModel:_._json,models:_._json,promptRouterArn:_.expectString,promptRouterName:_.expectString,routingCriteria:p((G)=>KQ2(G,B),"routingCriteria"),status:_.expectString,type:_.expectString,updatedAt:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"updatedAt")});return Object.assign(Q,Z),Q},"de_GetPromptRouterCommand"),kW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{commitmentDuration:_.expectString,commitmentExpirationTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"commitmentExpirationTime"),creationTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"creationTime"),desiredModelArn:_.expectString,desiredModelUnits:_.expectInt32,failureMessage:_.expectString,foundationModelArn:_.expectString,lastModifiedTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"lastModifiedTime"),modelArn:_.expectString,modelUnits:_.expectInt32,provisionedModelArn:_.expectString,provisionedModelName:_.expectString,status:_.expectString});return Object.assign(Q,Z),Q},"de_GetProvisionedModelThroughputCommand"),_W4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{formData:B.base64Decoder});return Object.assign(Q,Z),Q},"de_GetUseCaseForModelAccessCommand"),xW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{modelSummaries:p((G)=>QX4(G,B),"modelSummaries"),nextToken:_.expectString});return Object.assign(Q,Z),Q},"de_ListCustomModelsCommand"),vW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{jobSummaries:p((G)=>FX4(G,B),"jobSummaries"),nextToken:_.expectString});return Object.assign(Q,Z),Q},"de_ListEvaluationJobsCommand"),bW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{modelId:_.expectString,offers:_._json});return Object.assign(Q,Z),Q},"de_ListFoundationModelAgreementOffersCommand"),fW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{modelSummaries:_._json});return Object.assign(Q,Z),Q},"de_ListFoundationModelsCommand"),hW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{guardrails:p((G)=>EX4(G,B),"guardrails"),nextToken:_.expectString});return Object.assign(Q,Z),Q},"de_ListGuardrailsCommand"),gW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{modelSummaries:p((G)=>$X4(G,B),"modelSummaries"),nextToken:_.expectString});return Object.assign(Q,Z),Q},"de_ListImportedModelsCommand"),uW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{inferenceProfileSummaries:p((G)=>qX4(G,B),"inferenceProfileSummaries"),nextToken:_.expectString});return Object.assign(Q,Z),Q},"de_ListInferenceProfilesCommand"),mW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{marketplaceModelEndpoints:p((G)=>OX4(G,B),"marketplaceModelEndpoints"),nextToken:_.expectString});return Object.assign(Q,Z),Q},"de_ListMarketplaceModelEndpointsCommand"),dW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{modelCopyJobSummaries:p((G)=>PX4(G,B),"modelCopyJobSummaries"),nextToken:_.expectString});return Object.assign(Q,Z),Q},"de_ListModelCopyJobsCommand"),cW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{modelCustomizationJobSummaries:p((G)=>jX4(G,B),"modelCustomizationJobSummaries"),nextToken:_.expectString});return Object.assign(Q,Z),Q},"de_ListModelCustomizationJobsCommand"),lW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{modelImportJobSummaries:p((G)=>kX4(G,B),"modelImportJobSummaries"),nextToken:_.expectString});return Object.assign(Q,Z),Q},"de_ListModelImportJobsCommand"),pW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{invocationJobSummaries:p((G)=>xX4(G,B),"invocationJobSummaries"),nextToken:_.expectString});return Object.assign(Q,Z),Q},"de_ListModelInvocationJobsCommand"),iW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{nextToken:_.expectString,promptRouterSummaries:p((G)=>bX4(G,B),"promptRouterSummaries")});return Object.assign(Q,Z),Q},"de_ListPromptRoutersCommand"),nW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{nextToken:_.expectString,provisionedModelSummaries:p((G)=>hX4(G,B),"provisionedModelSummaries")});return Object.assign(Q,Z),Q},"de_ListProvisionedModelThroughputsCommand"),aW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{tags:_._json});return Object.assign(Q,Z),Q},"de_ListTagsForResourceCommand"),sW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)});return await _.collectBody(A.body,B),Q},"de_PutModelInvocationLoggingConfigurationCommand"),rW4=p(async(A,B)=>{if(A.statusCode!==201&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)});return await _.collectBody(A.body,B),Q},"de_PutUseCaseForModelAccessCommand"),oW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{marketplaceModelEndpoint:p((G)=>Xq1(G,B),"marketplaceModelEndpoint")});return Object.assign(Q,Z),Q},"de_RegisterMarketplaceModelEndpointCommand"),tW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)});return await _.collectBody(A.body,B),Q},"de_StopEvaluationJobCommand"),eW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)});return await _.collectBody(A.body,B),Q},"de_StopModelCustomizationJobCommand"),AJ4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)});return await _.collectBody(A.body,B),Q},"de_StopModelInvocationJobCommand"),BJ4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)});return await _.collectBody(A.body,B),Q},"de_TagResourceCommand"),QJ4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)});return await _.collectBody(A.body,B),Q},"de_UntagResourceCommand"),DJ4=p(async(A,B)=>{if(A.statusCode!==202&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{guardrailArn:_.expectString,guardrailId:_.expectString,updatedAt:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"updatedAt"),version:_.expectString});return Object.assign(Q,Z),Q},"de_UpdateGuardrailCommand"),ZJ4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)}),D=_.expectNonNull(_.expectObject(await VB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{marketplaceModelEndpoint:p((G)=>Xq1(G,B),"marketplaceModelEndpoint")});return Object.assign(Q,Z),Q},"de_UpdateMarketplaceModelEndpointCommand"),GJ4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:l2(A)});return await _.collectBody(A.body,B),Q},"de_UpdateProvisionedModelThroughputCommand"),jB=p(async(A,B)=>{let Q={...A,body:await VB.parseJsonErrorBody(A.body,B)},D=VB.loadRestJsonErrorCode(A,Q.body);switch(D){case"AccessDeniedException":case"com.amazonaws.bedrock#AccessDeniedException":throw await IJ4(Q,B);case"ConflictException":case"com.amazonaws.bedrock#ConflictException":throw await YJ4(Q,B);case"InternalServerException":case"com.amazonaws.bedrock#InternalServerException":throw await WJ4(Q,B);case"ResourceNotFoundException":case"com.amazonaws.bedrock#ResourceNotFoundException":throw await JJ4(Q,B);case"ThrottlingException":case"com.amazonaws.bedrock#ThrottlingException":throw await CJ4(Q,B);case"ValidationException":case"com.amazonaws.bedrock#ValidationException":throw await HJ4(Q,B);case"ServiceQuotaExceededException":case"com.amazonaws.bedrock#ServiceQuotaExceededException":throw await XJ4(Q,B);case"TooManyTagsException":case"com.amazonaws.bedrock#TooManyTagsException":throw await KJ4(Q,B);case"ServiceUnavailableException":case"com.amazonaws.bedrock#ServiceUnavailableException":throw await VJ4(Q,B);default:let Z=Q.body;return FJ4({output:A,parsedBody:Z,errorCode:D})}},"de_CommandError"),FJ4=_.withBaseException(fw),IJ4=p(async(A,B)=>{let Q=_.map({}),D=A.body,Z=_.take(D,{message:_.expectString});Object.assign(Q,Z);let G=new _B2({$metadata:l2(A),...Q});return _.decorateServiceException(G,A.body)},"de_AccessDeniedExceptionRes"),YJ4=p(async(A,B)=>{let Q=_.map({}),D=A.body,Z=_.take(D,{message:_.expectString});Object.assign(Q,Z);let G=new hB2({$metadata:l2(A),...Q});return _.decorateServiceException(G,A.body)},"de_ConflictExceptionRes"),WJ4=p(async(A,B)=>{let Q=_.map({}),D=A.body,Z=_.take(D,{message:_.expectString});Object.assign(Q,Z);let G=new xB2({$metadata:l2(A),...Q});return _.decorateServiceException(G,A.body)},"de_InternalServerExceptionRes"),JJ4=p(async(A,B)=>{let Q=_.map({}),D=A.body,Z=_.take(D,{message:_.expectString});Object.assign(Q,Z);let G=new vB2({$metadata:l2(A),...Q});return _.decorateServiceException(G,A.body)},"de_ResourceNotFoundExceptionRes"),XJ4=p(async(A,B)=>{let Q=_.map({}),D=A.body,Z=_.take(D,{message:_.expectString});Object.assign(Q,Z);let G=new gB2({$metadata:l2(A),...Q});return _.decorateServiceException(G,A.body)},"de_ServiceQuotaExceededExceptionRes"),VJ4=p(async(A,B)=>{let Q=_.map({}),D=A.body,Z=_.take(D,{message:_.expectString});Object.assign(Q,Z);let G=new uB2({$metadata:l2(A),...Q});return _.decorateServiceException(G,A.body)},"de_ServiceUnavailableExceptionRes"),CJ4=p(async(A,B)=>{let Q=_.map({}),D=A.body,Z=_.take(D,{message:_.expectString});Object.assign(Q,Z);let G=new bB2({$metadata:l2(A),...Q});return _.decorateServiceException(G,A.body)},"de_ThrottlingExceptionRes"),KJ4=p(async(A,B)=>{let Q=_.map({}),D=A.body,Z=_.take(D,{message:_.expectString,resourceName:_.expectString});Object.assign(Q,Z);let G=new mB2({$metadata:l2(A),...Q});return _.decorateServiceException(G,A.body)},"de_TooManyTagsExceptionRes"),HJ4=p(async(A,B)=>{let Q=_.map({}),D=A.body,Z=_.take(D,{message:_.expectString});Object.assign(Q,Z);let G=new fB2({$metadata:l2(A),...Q});return _.decorateServiceException(G,A.body)},"de_ValidationExceptionRes"),F80=p((A,B)=>{return Object.entries(A).reduce((Q,[D,Z])=>{if(Z===null)return Q;return Q[D]=zJ4(Z,B),Q},{})},"se_AdditionalModelRequestFields"),zJ4=p((A,B)=>{return A},"se_AdditionalModelRequestFieldsValue"),EJ4=p((A,B)=>{return _.take(A,{customMetricConfig:p((Q)=>UJ4(Q,B),"customMetricConfig"),datasetMetricConfigs:_._json,evaluatorModelConfig:_._json})},"se_AutomatedEvaluationConfig"),UJ4=p((A,B)=>{return _.take(A,{customMetrics:p((Q)=>wJ4(Q,B),"customMetrics"),evaluatorModelConfig:_._json})},"se_AutomatedEvaluationCustomMetricConfig"),wJ4=p((A,B)=>{return A.filter((Q)=>Q!=null).map((Q)=>{return $J4(Q,B)})},"se_AutomatedEvaluationCustomMetrics"),$J4=p((A,B)=>{return Qq1.visit(A,{customMetricDefinition:p((Q)=>({customMetricDefinition:NJ4(Q,B)}),"customMetricDefinition"),_:p((Q,D)=>({[Q]:D}),"_")})},"se_AutomatedEvaluationCustomMetricSource"),qJ4=p((A,B)=>{return _.take(A,{contentType:[],data:B.base64Encoder,identifier:[]})},"se_ByteContentDoc"),NJ4=p((A,B)=>{return _.take(A,{instructions:[],name:[],ratingScale:p((Q)=>hJ4(Q,B),"ratingScale")})},"se_CustomMetricDefinition"),LJ4=p((A,B)=>{return Dq1.visit(A,{automated:p((Q)=>({automated:EJ4(Q,B)}),"automated"),human:p((Q)=>({human:_._json(Q)}),"human"),_:p((Q,D)=>({[Q]:D}),"_")})},"se_EvaluationConfig"),MJ4=p((A,B)=>{return Iq1.visit(A,{models:p((Q)=>({models:_._json(Q)}),"models"),ragConfigs:p((Q)=>({ragConfigs:fJ4(Q,B)}),"ragConfigs"),_:p((Q,D)=>({[Q]:D}),"_")})},"se_EvaluationInferenceConfig"),RJ4=p((A,B)=>{return _.take(A,{byteContent:p((Q)=>qJ4(Q,B),"byteContent"),s3Location:_._json,sourceType:[]})},"se_ExternalSource"),OJ4=p((A,B)=>{return A.filter((Q)=>Q!=null).map((Q)=>{return RJ4(Q,B)})},"se_ExternalSources"),TJ4=p((A,B)=>{return _.take(A,{additionalModelRequestFields:p((Q)=>F80(Q,B),"additionalModelRequestFields"),guardrailConfiguration:_._json,kbInferenceConfig:p((Q)=>YQ2(Q,B),"kbInferenceConfig"),promptTemplate:_._json})},"se_ExternalSourcesGenerationConfiguration"),PJ4=p((A,B)=>{return _.take(A,{generationConfiguration:p((Q)=>TJ4(Q,B),"generationConfiguration"),modelArn:[],sources:p((Q)=>OJ4(Q,B),"sources")})},"se_ExternalSourcesRetrieveAndGenerateConfiguration"),vw=p((A,B)=>{return _.take(A,{key:[],value:p((Q)=>SJ4(Q,B),"value")})},"se_FilterAttribute"),SJ4=p((A,B)=>{return A},"se_FilterValue"),jJ4=p((A,B)=>{return _.take(A,{additionalModelRequestFields:p((Q)=>F80(Q,B),"additionalModelRequestFields"),guardrailConfiguration:_._json,kbInferenceConfig:p((Q)=>YQ2(Q,B),"kbInferenceConfig"),promptTemplate:_._json})},"se_GenerationConfiguration"),yJ4=p((A,B)=>{return _.take(A,{action:[],enabled:[],threshold:_.serializeFloat,type:[]})},"se_GuardrailContextualGroundingFilterConfig"),kJ4=p((A,B)=>{return A.filter((Q)=>Q!=null).map((Q)=>{return yJ4(Q,B)})},"se_GuardrailContextualGroundingFiltersConfig"),IQ2=p((A,B)=>{return _.take(A,{filtersConfig:p((Q)=>kJ4(Q,B),"filtersConfig")})},"se_GuardrailContextualGroundingPolicyConfig"),YQ2=p((A,B)=>{return _.take(A,{textInferenceConfig:p((Q)=>lJ4(Q,B),"textInferenceConfig")})},"se_KbInferenceConfig"),_J4=p((A,B)=>{return Gq1.visit(A,{retrieveAndGenerateConfig:p((Q)=>({retrieveAndGenerateConfig:mJ4(Q,B)}),"retrieveAndGenerateConfig"),retrieveConfig:p((Q)=>({retrieveConfig:dJ4(Q,B)}),"retrieveConfig"),_:p((Q,D)=>({[Q]:D}),"_")})},"se_KnowledgeBaseConfig"),WQ2=p((A,B)=>{return _.take(A,{vectorSearchConfiguration:p((Q)=>vJ4(Q,B),"vectorSearchConfiguration")})},"se_KnowledgeBaseRetrievalConfiguration"),xJ4=p((A,B)=>{return _.take(A,{generationConfiguration:p((Q)=>jJ4(Q,B),"generationConfiguration"),knowledgeBaseId:[],modelArn:[],orchestrationConfiguration:_._json,retrievalConfiguration:p((Q)=>WQ2(Q,B),"retrievalConfiguration")})},"se_KnowledgeBaseRetrieveAndGenerateConfiguration"),vJ4=p((A,B)=>{return _.take(A,{filter:p((Q)=>JQ2(Q,B),"filter"),implicitFilterConfiguration:_._json,numberOfResults:[],overrideSearchType:[],rerankingConfiguration:p((Q)=>nJ4(Q,B),"rerankingConfiguration")})},"se_KnowledgeBaseVectorSearchConfiguration"),bJ4=p((A,B)=>{return Fq1.visit(A,{knowledgeBaseConfig:p((Q)=>({knowledgeBaseConfig:_J4(Q,B)}),"knowledgeBaseConfig"),precomputedRagSourceConfig:p((Q)=>({precomputedRagSourceConfig:_._json(Q)}),"precomputedRagSourceConfig"),_:p((Q,D)=>({[Q]:D}),"_")})},"se_RAGConfig"),fJ4=p((A,B)=>{return A.filter((Q)=>Q!=null).map((Q)=>{return bJ4(Q,B)})},"se_RagConfigs"),hJ4=p((A,B)=>{return A.filter((Q)=>Q!=null).map((Q)=>{return gJ4(Q,B)})},"se_RatingScale"),gJ4=p((A,B)=>{return _.take(A,{definition:[],value:p((Q)=>uJ4(Q,B),"value")})},"se_RatingScaleItem"),uJ4=p((A,B)=>{return Bq1.visit(A,{floatValue:p((Q)=>({floatValue:_.serializeFloat(Q)}),"floatValue"),stringValue:p((Q)=>({stringValue:Q}),"stringValue"),_:p((Q,D)=>({[Q]:D}),"_")})},"se_RatingScaleItemValue"),JQ2=p((A,B)=>{return Zq1.visit(A,{andAll:p((Q)=>({andAll:KB2(Q,B)}),"andAll"),equals:p((Q)=>({equals:vw(Q,B)}),"equals"),greaterThan:p((Q)=>({greaterThan:vw(Q,B)}),"greaterThan"),greaterThanOrEquals:p((Q)=>({greaterThanOrEquals:vw(Q,B)}),"greaterThanOrEquals"),in:p((Q)=>({in:vw(Q,B)}),"in"),lessThan:p((Q)=>({lessThan:vw(Q,B)}),"lessThan"),lessThanOrEquals:p((Q)=>({lessThanOrEquals:vw(Q,B)}),"lessThanOrEquals"),listContains:p((Q)=>({listContains:vw(Q,B)}),"listContains"),notEquals:p((Q)=>({notEquals:vw(Q,B)}),"notEquals"),notIn:p((Q)=>({notIn:vw(Q,B)}),"notIn"),orAll:p((Q)=>({orAll:KB2(Q,B)}),"orAll"),startsWith:p((Q)=>({startsWith:vw(Q,B)}),"startsWith"),stringContains:p((Q)=>({stringContains:vw(Q,B)}),"stringContains"),_:p((Q,D)=>({[Q]:D}),"_")})},"se_RetrievalFilter"),KB2=p((A,B)=>{return A.filter((Q)=>Q!=null).map((Q)=>{return JQ2(Q,B)})},"se_RetrievalFilterList"),mJ4=p((A,B)=>{return _.take(A,{externalSourcesConfiguration:p((Q)=>PJ4(Q,B),"externalSourcesConfiguration"),knowledgeBaseConfiguration:p((Q)=>xJ4(Q,B),"knowledgeBaseConfiguration"),type:[]})},"se_RetrieveAndGenerateConfiguration"),dJ4=p((A,B)=>{return _.take(A,{knowledgeBaseId:[],knowledgeBaseRetrievalConfiguration:p((Q)=>WQ2(Q,B),"knowledgeBaseRetrievalConfiguration")})},"se_RetrieveConfig"),cJ4=p((A,B)=>{return _.take(A,{responseQualityDifference:_.serializeFloat})},"se_RoutingCriteria"),lJ4=p((A,B)=>{return _.take(A,{maxTokens:[],stopSequences:_._json,temperature:_.serializeFloat,topP:_.serializeFloat})},"se_TextInferenceConfig"),pJ4=p((A,B)=>{return _.take(A,{metadataConfiguration:_._json,modelConfiguration:p((Q)=>iJ4(Q,B),"modelConfiguration"),numberOfRerankedResults:[]})},"se_VectorSearchBedrockRerankingConfiguration"),iJ4=p((A,B)=>{return _.take(A,{additionalModelRequestFields:p((Q)=>F80(Q,B),"additionalModelRequestFields"),modelArn:[]})},"se_VectorSearchBedrockRerankingModelConfiguration"),nJ4=p((A,B)=>{return _.take(A,{bedrockRerankingConfiguration:p((Q)=>pJ4(Q,B),"bedrockRerankingConfiguration"),type:[]})},"se_VectorSearchRerankingConfiguration"),I80=p((A,B)=>{return Object.entries(A).reduce((Q,[D,Z])=>{if(Z===null)return Q;return Q[D]=aJ4(Z,B),Q},{})},"de_AdditionalModelRequestFields"),aJ4=p((A,B)=>{return A},"de_AdditionalModelRequestFieldsValue"),sJ4=p((A,B)=>{return _.take(A,{customMetricConfig:p((Q)=>rJ4(Q,B),"customMetricConfig"),datasetMetricConfigs:_._json,evaluatorModelConfig:p((Q)=>_._json(VB.awsExpectUnion(Q)),"evaluatorModelConfig")})},"de_AutomatedEvaluationConfig"),rJ4=p((A,B)=>{return _.take(A,{customMetrics:p((Q)=>oJ4(Q,B),"customMetrics"),evaluatorModelConfig:_._json})},"de_AutomatedEvaluationCustomMetricConfig"),oJ4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return tJ4(VB.awsExpectUnion(D),B)})},"de_AutomatedEvaluationCustomMetrics"),tJ4=p((A,B)=>{if(A.customMetricDefinition!=null)return{customMetricDefinition:AX4(A.customMetricDefinition,B)};return{$unknown:Object.entries(A)[0]}},"de_AutomatedEvaluationCustomMetricSource"),eJ4=p((A,B)=>{return _.take(A,{contentType:_.expectString,data:B.base64Decoder,identifier:_.expectString})},"de_ByteContentDoc"),AX4=p((A,B)=>{return _.take(A,{instructions:_.expectString,name:_.expectString,ratingScale:p((Q)=>dX4(Q,B),"ratingScale")})},"de_CustomMetricDefinition"),BX4=p((A,B)=>{return _.take(A,{baseModelArn:_.expectString,baseModelName:_.expectString,creationTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"creationTime"),customizationType:_.expectString,modelArn:_.expectString,modelName:_.expectString,modelStatus:_.expectString,ownerAccountId:_.expectString})},"de_CustomModelSummary"),QX4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return BX4(D,B)})},"de_CustomModelSummaryList"),DX4=p((A,B)=>{return _.take(A,{creationTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"creationTime"),lastModifiedTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"lastModifiedTime"),status:_.expectString})},"de_DataProcessingDetails"),ZX4=p((A,B)=>{if(A.automated!=null)return{automated:sJ4(A.automated,B)};if(A.human!=null)return{human:_._json(A.human)};return{$unknown:Object.entries(A)[0]}},"de_EvaluationConfig"),GX4=p((A,B)=>{if(A.models!=null)return{models:_._json(A.models)};if(A.ragConfigs!=null)return{ragConfigs:mX4(A.ragConfigs,B)};return{$unknown:Object.entries(A)[0]}},"de_EvaluationInferenceConfig"),FX4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return IX4(D,B)})},"de_EvaluationSummaries"),IX4=p((A,B)=>{return _.take(A,{applicationType:_.expectString,creationTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"creationTime"),customMetricsEvaluatorModelIdentifiers:_._json,evaluationTaskTypes:_._json,evaluatorModelIdentifiers:_._json,inferenceConfigSummary:_._json,jobArn:_.expectString,jobName:_.expectString,jobType:_.expectString,modelIdentifiers:_._json,ragIdentifiers:_._json,status:_.expectString})},"de_EvaluationSummary"),YX4=p((A,B)=>{return _.take(A,{byteContent:p((Q)=>eJ4(Q,B),"byteContent"),s3Location:_._json,sourceType:_.expectString})},"de_ExternalSource"),WX4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return YX4(D,B)})},"de_ExternalSources"),JX4=p((A,B)=>{return _.take(A,{additionalModelRequestFields:p((Q)=>I80(Q,B),"additionalModelRequestFields"),guardrailConfiguration:_._json,kbInferenceConfig:p((Q)=>XQ2(Q,B),"kbInferenceConfig"),promptTemplate:_._json})},"de_ExternalSourcesGenerationConfiguration"),XX4=p((A,B)=>{return _.take(A,{generationConfiguration:p((Q)=>JX4(Q,B),"generationConfiguration"),modelArn:_.expectString,sources:p((Q)=>WX4(Q,B),"sources")})},"de_ExternalSourcesRetrieveAndGenerateConfiguration"),bw=p((A,B)=>{return _.take(A,{key:_.expectString,value:p((Q)=>VX4(Q,B),"value")})},"de_FilterAttribute"),VX4=p((A,B)=>{return A},"de_FilterValue"),CX4=p((A,B)=>{return _.take(A,{additionalModelRequestFields:p((Q)=>I80(Q,B),"additionalModelRequestFields"),guardrailConfiguration:_._json,kbInferenceConfig:p((Q)=>XQ2(Q,B),"kbInferenceConfig"),promptTemplate:_._json})},"de_GenerationConfiguration"),KX4=p((A,B)=>{return _.take(A,{action:_.expectString,enabled:_.expectBoolean,threshold:_.limitedParseDouble,type:_.expectString})},"de_GuardrailContextualGroundingFilter"),HX4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return KX4(D,B)})},"de_GuardrailContextualGroundingFilters"),zX4=p((A,B)=>{return _.take(A,{filters:p((Q)=>HX4(Q,B),"filters")})},"de_GuardrailContextualGroundingPolicy"),EX4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return UX4(D,B)})},"de_GuardrailSummaries"),UX4=p((A,B)=>{return _.take(A,{arn:_.expectString,createdAt:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"createdAt"),crossRegionDetails:_._json,description:_.expectString,id:_.expectString,name:_.expectString,status:_.expectString,updatedAt:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"updatedAt"),version:_.expectString})},"de_GuardrailSummary"),wX4=p((A,B)=>{return _.take(A,{creationTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"creationTime"),instructSupported:_.expectBoolean,modelArchitecture:_.expectString,modelArn:_.expectString,modelName:_.expectString})},"de_ImportedModelSummary"),$X4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return wX4(D,B)})},"de_ImportedModelSummaryList"),qX4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return NX4(D,B)})},"de_InferenceProfileSummaries"),NX4=p((A,B)=>{return _.take(A,{createdAt:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"createdAt"),description:_.expectString,inferenceProfileArn:_.expectString,inferenceProfileId:_.expectString,inferenceProfileName:_.expectString,models:_._json,status:_.expectString,type:_.expectString,updatedAt:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"updatedAt")})},"de_InferenceProfileSummary"),XQ2=p((A,B)=>{return _.take(A,{textInferenceConfig:p((Q)=>nX4(Q,B),"textInferenceConfig")})},"de_KbInferenceConfig"),LX4=p((A,B)=>{if(A.retrieveAndGenerateConfig!=null)return{retrieveAndGenerateConfig:pX4(A.retrieveAndGenerateConfig,B)};if(A.retrieveConfig!=null)return{retrieveConfig:iX4(A.retrieveConfig,B)};return{$unknown:Object.entries(A)[0]}},"de_KnowledgeBaseConfig"),VQ2=p((A,B)=>{return _.take(A,{vectorSearchConfiguration:p((Q)=>RX4(Q,B),"vectorSearchConfiguration")})},"de_KnowledgeBaseRetrievalConfiguration"),MX4=p((A,B)=>{return _.take(A,{generationConfiguration:p((Q)=>CX4(Q,B),"generationConfiguration"),knowledgeBaseId:_.expectString,modelArn:_.expectString,orchestrationConfiguration:_._json,retrievalConfiguration:p((Q)=>VQ2(Q,B),"retrievalConfiguration")})},"de_KnowledgeBaseRetrieveAndGenerateConfiguration"),RX4=p((A,B)=>{return _.take(A,{filter:p((Q)=>CQ2(VB.awsExpectUnion(Q),B),"filter"),implicitFilterConfiguration:_._json,numberOfResults:_.expectInt32,overrideSearchType:_.expectString,rerankingConfiguration:p((Q)=>eX4(Q,B),"rerankingConfiguration")})},"de_KnowledgeBaseVectorSearchConfiguration"),Xq1=p((A,B)=>{return _.take(A,{createdAt:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"createdAt"),endpointArn:_.expectString,endpointConfig:p((Q)=>_._json(VB.awsExpectUnion(Q)),"endpointConfig"),endpointStatus:_.expectString,endpointStatusMessage:_.expectString,modelSourceIdentifier:_.expectString,status:_.expectString,statusMessage:_.expectString,updatedAt:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"updatedAt")})},"de_MarketplaceModelEndpoint"),OX4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return TX4(D,B)})},"de_MarketplaceModelEndpointSummaries"),TX4=p((A,B)=>{return _.take(A,{createdAt:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"createdAt"),endpointArn:_.expectString,modelSourceIdentifier:_.expectString,status:_.expectString,statusMessage:_.expectString,updatedAt:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"updatedAt")})},"de_MarketplaceModelEndpointSummary"),PX4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return SX4(D,B)})},"de_ModelCopyJobSummaries"),SX4=p((A,B)=>{return _.take(A,{creationTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"creationTime"),failureMessage:_.expectString,jobArn:_.expectString,sourceAccountId:_.expectString,sourceModelArn:_.expectString,sourceModelName:_.expectString,status:_.expectString,targetModelArn:_.expectString,targetModelKmsKeyArn:_.expectString,targetModelName:_.expectString,targetModelTags:_._json})},"de_ModelCopyJobSummary"),jX4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return yX4(D,B)})},"de_ModelCustomizationJobSummaries"),yX4=p((A,B)=>{return _.take(A,{baseModelArn:_.expectString,creationTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"creationTime"),customModelArn:_.expectString,customModelName:_.expectString,customizationType:_.expectString,endTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"endTime"),jobArn:_.expectString,jobName:_.expectString,lastModifiedTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"lastModifiedTime"),status:_.expectString,statusDetails:p((Q)=>HQ2(Q,B),"statusDetails")})},"de_ModelCustomizationJobSummary"),kX4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return _X4(D,B)})},"de_ModelImportJobSummaries"),_X4=p((A,B)=>{return _.take(A,{creationTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"creationTime"),endTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"endTime"),importedModelArn:_.expectString,importedModelName:_.expectString,jobArn:_.expectString,jobName:_.expectString,lastModifiedTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"lastModifiedTime"),status:_.expectString})},"de_ModelImportJobSummary"),xX4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return vX4(D,B)})},"de_ModelInvocationJobSummaries"),vX4=p((A,B)=>{return _.take(A,{clientRequestToken:_.expectString,endTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"endTime"),inputDataConfig:p((Q)=>_._json(VB.awsExpectUnion(Q)),"inputDataConfig"),jobArn:_.expectString,jobExpirationTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"jobExpirationTime"),jobName:_.expectString,lastModifiedTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"lastModifiedTime"),message:_.expectString,modelId:_.expectString,outputDataConfig:p((Q)=>_._json(VB.awsExpectUnion(Q)),"outputDataConfig"),roleArn:_.expectString,status:_.expectString,submitTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"submitTime"),timeoutDurationInHours:_.expectInt32,vpcConfig:_._json})},"de_ModelInvocationJobSummary"),bX4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return fX4(D,B)})},"de_PromptRouterSummaries"),fX4=p((A,B)=>{return _.take(A,{createdAt:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"createdAt"),description:_.expectString,fallbackModel:_._json,models:_._json,promptRouterArn:_.expectString,promptRouterName:_.expectString,routingCriteria:p((Q)=>KQ2(Q,B),"routingCriteria"),status:_.expectString,type:_.expectString,updatedAt:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"updatedAt")})},"de_PromptRouterSummary"),hX4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return gX4(D,B)})},"de_ProvisionedModelSummaries"),gX4=p((A,B)=>{return _.take(A,{commitmentDuration:_.expectString,commitmentExpirationTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"commitmentExpirationTime"),creationTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"creationTime"),desiredModelArn:_.expectString,desiredModelUnits:_.expectInt32,foundationModelArn:_.expectString,lastModifiedTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"lastModifiedTime"),modelArn:_.expectString,modelUnits:_.expectInt32,provisionedModelArn:_.expectString,provisionedModelName:_.expectString,status:_.expectString})},"de_ProvisionedModelSummary"),uX4=p((A,B)=>{if(A.knowledgeBaseConfig!=null)return{knowledgeBaseConfig:LX4(VB.awsExpectUnion(A.knowledgeBaseConfig),B)};if(A.precomputedRagSourceConfig!=null)return{precomputedRagSourceConfig:_._json(VB.awsExpectUnion(A.precomputedRagSourceConfig))};return{$unknown:Object.entries(A)[0]}},"de_RAGConfig"),mX4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return uX4(VB.awsExpectUnion(D),B)})},"de_RagConfigs"),dX4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return cX4(D,B)})},"de_RatingScale"),cX4=p((A,B)=>{return _.take(A,{definition:_.expectString,value:p((Q)=>lX4(VB.awsExpectUnion(Q),B),"value")})},"de_RatingScaleItem"),lX4=p((A,B)=>{if(_.limitedParseFloat32(A.floatValue)!==void 0)return{floatValue:_.limitedParseFloat32(A.floatValue)};if(_.expectString(A.stringValue)!==void 0)return{stringValue:_.expectString(A.stringValue)};return{$unknown:Object.entries(A)[0]}},"de_RatingScaleItemValue"),CQ2=p((A,B)=>{if(A.andAll!=null)return{andAll:HB2(A.andAll,B)};if(A.equals!=null)return{equals:bw(A.equals,B)};if(A.greaterThan!=null)return{greaterThan:bw(A.greaterThan,B)};if(A.greaterThanOrEquals!=null)return{greaterThanOrEquals:bw(A.greaterThanOrEquals,B)};if(A.in!=null)return{in:bw(A.in,B)};if(A.lessThan!=null)return{lessThan:bw(A.lessThan,B)};if(A.lessThanOrEquals!=null)return{lessThanOrEquals:bw(A.lessThanOrEquals,B)};if(A.listContains!=null)return{listContains:bw(A.listContains,B)};if(A.notEquals!=null)return{notEquals:bw(A.notEquals,B)};if(A.notIn!=null)return{notIn:bw(A.notIn,B)};if(A.orAll!=null)return{orAll:HB2(A.orAll,B)};if(A.startsWith!=null)return{startsWith:bw(A.startsWith,B)};if(A.stringContains!=null)return{stringContains:bw(A.stringContains,B)};return{$unknown:Object.entries(A)[0]}},"de_RetrievalFilter"),HB2=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return CQ2(VB.awsExpectUnion(D),B)})},"de_RetrievalFilterList"),pX4=p((A,B)=>{return _.take(A,{externalSourcesConfiguration:p((Q)=>XX4(Q,B),"externalSourcesConfiguration"),knowledgeBaseConfiguration:p((Q)=>MX4(Q,B),"knowledgeBaseConfiguration"),type:_.expectString})},"de_RetrieveAndGenerateConfiguration"),iX4=p((A,B)=>{return _.take(A,{knowledgeBaseId:_.expectString,knowledgeBaseRetrievalConfiguration:p((Q)=>VQ2(Q,B),"knowledgeBaseRetrievalConfiguration")})},"de_RetrieveConfig"),KQ2=p((A,B)=>{return _.take(A,{responseQualityDifference:_.limitedParseDouble})},"de_RoutingCriteria"),HQ2=p((A,B)=>{return _.take(A,{dataProcessingDetails:p((Q)=>DX4(Q,B),"dataProcessingDetails"),trainingDetails:p((Q)=>aX4(Q,B),"trainingDetails"),validationDetails:p((Q)=>sX4(Q,B),"validationDetails")})},"de_StatusDetails"),nX4=p((A,B)=>{return _.take(A,{maxTokens:_.expectInt32,stopSequences:_._json,temperature:_.limitedParseFloat32,topP:_.limitedParseFloat32})},"de_TextInferenceConfig"),aX4=p((A,B)=>{return _.take(A,{creationTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"creationTime"),lastModifiedTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"lastModifiedTime"),status:_.expectString})},"de_TrainingDetails"),zQ2=p((A,B)=>{return _.take(A,{trainingLoss:_.limitedParseFloat32})},"de_TrainingMetrics"),sX4=p((A,B)=>{return _.take(A,{creationTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"creationTime"),lastModifiedTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"lastModifiedTime"),status:_.expectString})},"de_ValidationDetails"),EQ2=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return rX4(D,B)})},"de_ValidationMetrics"),rX4=p((A,B)=>{return _.take(A,{validationLoss:_.limitedParseFloat32})},"de_ValidatorMetric"),oX4=p((A,B)=>{return _.take(A,{metadataConfiguration:_._json,modelConfiguration:p((Q)=>tX4(Q,B),"modelConfiguration"),numberOfRerankedResults:_.expectInt32})},"de_VectorSearchBedrockRerankingConfiguration"),tX4=p((A,B)=>{return _.take(A,{additionalModelRequestFields:p((Q)=>I80(Q,B),"additionalModelRequestFields"),modelArn:_.expectString})},"de_VectorSearchBedrockRerankingModelConfiguration"),eX4=p((A,B)=>{return _.take(A,{bedrockRerankingConfiguration:p((Q)=>oX4(Q,B),"bedrockRerankingConfiguration"),type:_.expectString})},"de_VectorSearchRerankingConfiguration"),l2=p((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),zB2="applicationTypeEquals",EB2="byCustomizationType",UB2="byInferenceType",wB2="baseModelArnEquals",$B2="byOutputModality",qB2="byProvider",qV="creationTimeAfter",NV="creationTimeBefore",NB2="foundationModelArnEquals",LB2="guardrailIdentifier",Yq1="guardrailVersion",MB2="isOwned",RB2="modelArnEquals",wD="maxResults",OB2="modelStatus",AV4="modelSourceEquals",BV4="modelSourceIdentifier",LV="nameContains",$D="nextToken",QV4="outputModelNameContains",TB2="offerType",PB2="sourceAccountEquals",sW="sortBy",dz="statusEquals",SB2="sourceModelArnEquals",rW="sortOrder",jB2="submitTimeAfter",yB2="submitTimeBefore",r60="type",DV4="typeEquals",ZV4="targetModelNameContains",UQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","BatchDeleteEvaluationJob",{}).n("BedrockClient","BatchDeleteEvaluationJobCommand").f(pB2,aB2).ser(bI4).de(aY4).build(){static{p(this,"BatchDeleteEvaluationJobCommand")}},wQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","CreateCustomModel",{}).n("BedrockClient","CreateCustomModelCommand").f(void 0,void 0).ser(fI4).de(sY4).build(){static{p(this,"CreateCustomModelCommand")}},$Q2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","CreateEvaluationJob",{}).n("BedrockClient","CreateEvaluationJobCommand").f(GQ2,void 0).ser(hI4).de(rY4).build(){static{p(this,"CreateEvaluationJobCommand")}},qQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","CreateFoundationModelAgreement",{}).n("BedrockClient","CreateFoundationModelAgreementCommand").f(void 0,void 0).ser(gI4).de(oY4).build(){static{p(this,"CreateFoundationModelAgreementCommand")}},NQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","CreateGuardrail",{}).n("BedrockClient","CreateGuardrailCommand").f(L92,void 0).ser(uI4).de(tY4).build(){static{p(this,"CreateGuardrailCommand")}},LQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","CreateGuardrailVersion",{}).n("BedrockClient","CreateGuardrailVersionCommand").f(M92,void 0).ser(mI4).de(eY4).build(){static{p(this,"CreateGuardrailVersionCommand")}},MQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","CreateInferenceProfile",{}).n("BedrockClient","CreateInferenceProfileCommand").f(u92,void 0).ser(dI4).de(AW4).build(){static{p(this,"CreateInferenceProfileCommand")}},RQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","CreateMarketplaceModelEndpoint",{}).n("BedrockClient","CreateMarketplaceModelEndpointCommand").f(void 0,void 0).ser(cI4).de(BW4).build(){static{p(this,"CreateMarketplaceModelEndpointCommand")}},OQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","CreateModelCopyJob",{}).n("BedrockClient","CreateModelCopyJobCommand").f(void 0,void 0).ser(lI4).de(QW4).build(){static{p(this,"CreateModelCopyJobCommand")}},TQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","CreateModelCustomizationJob",{}).n("BedrockClient","CreateModelCustomizationJobCommand").f(o92,void 0).ser(pI4).de(DW4).build(){static{p(this,"CreateModelCustomizationJobCommand")}},PQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","CreateModelImportJob",{}).n("BedrockClient","CreateModelImportJobCommand").f(void 0,void 0).ser(iI4).de(ZW4).build(){static{p(this,"CreateModelImportJobCommand")}},SQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","CreateModelInvocationJob",{}).n("BedrockClient","CreateModelInvocationJobCommand").f(void 0,void 0).ser(nI4).de(GW4).build(){static{p(this,"CreateModelInvocationJobCommand")}},jQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","CreatePromptRouter",{}).n("BedrockClient","CreatePromptRouterCommand").f(n92,void 0).ser(aI4).de(FW4).build(){static{p(this,"CreatePromptRouterCommand")}},yQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","CreateProvisionedModelThroughput",{}).n("BedrockClient","CreateProvisionedModelThroughputCommand").f(void 0,void 0).ser(sI4).de(IW4).build(){static{p(this,"CreateProvisionedModelThroughputCommand")}},kQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","DeleteCustomModel",{}).n("BedrockClient","DeleteCustomModelCommand").f(void 0,void 0).ser(rI4).de(YW4).build(){static{p(this,"DeleteCustomModelCommand")}},_Q2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","DeleteFoundationModelAgreement",{}).n("BedrockClient","DeleteFoundationModelAgreementCommand").f(void 0,void 0).ser(oI4).de(WW4).build(){static{p(this,"DeleteFoundationModelAgreementCommand")}},xQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","DeleteGuardrail",{}).n("BedrockClient","DeleteGuardrailCommand").f(void 0,void 0).ser(tI4).de(JW4).build(){static{p(this,"DeleteGuardrailCommand")}},vQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","DeleteImportedModel",{}).n("BedrockClient","DeleteImportedModelCommand").f(void 0,void 0).ser(eI4).de(XW4).build(){static{p(this,"DeleteImportedModelCommand")}},bQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","DeleteInferenceProfile",{}).n("BedrockClient","DeleteInferenceProfileCommand").f(void 0,void 0).ser(AY4).de(VW4).build(){static{p(this,"DeleteInferenceProfileCommand")}},fQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","DeleteMarketplaceModelEndpoint",{}).n("BedrockClient","DeleteMarketplaceModelEndpointCommand").f(void 0,void 0).ser(BY4).de(CW4).build(){static{p(this,"DeleteMarketplaceModelEndpointCommand")}},hQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","DeleteModelInvocationLoggingConfiguration",{}).n("BedrockClient","DeleteModelInvocationLoggingConfigurationCommand").f(void 0,void 0).ser(QY4).de(KW4).build(){static{p(this,"DeleteModelInvocationLoggingConfigurationCommand")}},gQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","DeletePromptRouter",{}).n("BedrockClient","DeletePromptRouterCommand").f(void 0,void 0).ser(DY4).de(HW4).build(){static{p(this,"DeletePromptRouterCommand")}},uQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","DeleteProvisionedModelThroughput",{}).n("BedrockClient","DeleteProvisionedModelThroughputCommand").f(void 0,void 0).ser(ZY4).de(zW4).build(){static{p(this,"DeleteProvisionedModelThroughputCommand")}},mQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","DeregisterMarketplaceModelEndpoint",{}).n("BedrockClient","DeregisterMarketplaceModelEndpointCommand").f(void 0,void 0).ser(GY4).de(EW4).build(){static{p(this,"DeregisterMarketplaceModelEndpointCommand")}},dQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetCustomModel",{}).n("BedrockClient","GetCustomModelCommand").f(void 0,lB2).ser(FY4).de(UW4).build(){static{p(this,"GetCustomModelCommand")}},cQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetEvaluationJob",{}).n("BedrockClient","GetEvaluationJobCommand").f(K92,FQ2).ser(IY4).de(wW4).build(){static{p(this,"GetEvaluationJobCommand")}},lQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetFoundationModelAvailability",{}).n("BedrockClient","GetFoundationModelAvailabilityCommand").f(void 0,void 0).ser(WY4).de(qW4).build(){static{p(this,"GetFoundationModelAvailabilityCommand")}},pQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetFoundationModel",{}).n("BedrockClient","GetFoundationModelCommand").f(void 0,void 0).ser(YY4).de($W4).build(){static{p(this,"GetFoundationModelCommand")}},iQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetGuardrail",{}).n("BedrockClient","GetGuardrailCommand").f(void 0,b92).ser(JY4).de(NW4).build(){static{p(this,"GetGuardrailCommand")}},nQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetImportedModel",{}).n("BedrockClient","GetImportedModelCommand").f(void 0,void 0).ser(XY4).de(LW4).build(){static{p(this,"GetImportedModelCommand")}},aQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetInferenceProfile",{}).n("BedrockClient","GetInferenceProfileCommand").f(void 0,m92).ser(VY4).de(MW4).build(){static{p(this,"GetInferenceProfileCommand")}},sQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetMarketplaceModelEndpoint",{}).n("BedrockClient","GetMarketplaceModelEndpointCommand").f(void 0,void 0).ser(CY4).de(RW4).build(){static{p(this,"GetMarketplaceModelEndpointCommand")}},rQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetModelCopyJob",{}).n("BedrockClient","GetModelCopyJobCommand").f(void 0,void 0).ser(KY4).de(OW4).build(){static{p(this,"GetModelCopyJobCommand")}},oQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetModelCustomizationJob",{}).n("BedrockClient","GetModelCustomizationJobCommand").f(void 0,t92).ser(HY4).de(TW4).build(){static{p(this,"GetModelCustomizationJobCommand")}},tQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetModelImportJob",{}).n("BedrockClient","GetModelImportJobCommand").f(void 0,void 0).ser(zY4).de(PW4).build(){static{p(this,"GetModelImportJobCommand")}},eQ2=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetModelInvocationJob",{}).n("BedrockClient","GetModelInvocationJobCommand").f(void 0,l92).ser(EY4).de(SW4).build(){static{p(this,"GetModelInvocationJobCommand")}},A42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetModelInvocationLoggingConfiguration",{}).n("BedrockClient","GetModelInvocationLoggingConfigurationCommand").f(void 0,void 0).ser(UY4).de(jW4).build(){static{p(this,"GetModelInvocationLoggingConfigurationCommand")}},B42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetPromptRouter",{}).n("BedrockClient","GetPromptRouterCommand").f(void 0,a92).ser(wY4).de(yW4).build(){static{p(this,"GetPromptRouterCommand")}},Q42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetProvisionedModelThroughput",{}).n("BedrockClient","GetProvisionedModelThroughputCommand").f(void 0,void 0).ser($Y4).de(kW4).build(){static{p(this,"GetProvisionedModelThroughputCommand")}},D42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetUseCaseForModelAccess",{}).n("BedrockClient","GetUseCaseForModelAccessCommand").f(void 0,void 0).ser(qY4).de(_W4).build(){static{p(this,"GetUseCaseForModelAccessCommand")}},Y80=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","ListCustomModels",{}).n("BedrockClient","ListCustomModelsCommand").f(void 0,void 0).ser(NY4).de(xW4).build(){static{p(this,"ListCustomModelsCommand")}},W80=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","ListEvaluationJobs",{}).n("BedrockClient","ListEvaluationJobsCommand").f(void 0,void 0).ser(LY4).de(vW4).build(){static{p(this,"ListEvaluationJobsCommand")}},Z42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","ListFoundationModelAgreementOffers",{}).n("BedrockClient","ListFoundationModelAgreementOffersCommand").f(void 0,void 0).ser(MY4).de(bW4).build(){static{p(this,"ListFoundationModelAgreementOffersCommand")}},G42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","ListFoundationModels",{}).n("BedrockClient","ListFoundationModelsCommand").f(void 0,void 0).ser(RY4).de(fW4).build(){static{p(this,"ListFoundationModelsCommand")}},J80=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","ListGuardrails",{}).n("BedrockClient","ListGuardrailsCommand").f(void 0,h92).ser(OY4).de(hW4).build(){static{p(this,"ListGuardrailsCommand")}},X80=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","ListImportedModels",{}).n("BedrockClient","ListImportedModelsCommand").f(void 0,void 0).ser(TY4).de(gW4).build(){static{p(this,"ListImportedModelsCommand")}},V80=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","ListInferenceProfiles",{}).n("BedrockClient","ListInferenceProfilesCommand").f(void 0,c92).ser(PY4).de(uW4).build(){static{p(this,"ListInferenceProfilesCommand")}},C80=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","ListMarketplaceModelEndpoints",{}).n("BedrockClient","ListMarketplaceModelEndpointsCommand").f(void 0,void 0).ser(SY4).de(mW4).build(){static{p(this,"ListMarketplaceModelEndpointsCommand")}},K80=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","ListModelCopyJobs",{}).n("BedrockClient","ListModelCopyJobsCommand").f(void 0,void 0).ser(jY4).de(dW4).build(){static{p(this,"ListModelCopyJobsCommand")}},H80=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","ListModelCustomizationJobs",{}).n("BedrockClient","ListModelCustomizationJobsCommand").f(void 0,void 0).ser(yY4).de(cW4).build(){static{p(this,"ListModelCustomizationJobsCommand")}},z80=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","ListModelImportJobs",{}).n("BedrockClient","ListModelImportJobsCommand").f(void 0,void 0).ser(kY4).de(lW4).build(){static{p(this,"ListModelImportJobsCommand")}},E80=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","ListModelInvocationJobs",{}).n("BedrockClient","ListModelInvocationJobsCommand").f(void 0,i92).ser(_Y4).de(pW4).build(){static{p(this,"ListModelInvocationJobsCommand")}},U80=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","ListPromptRouters",{}).n("BedrockClient","ListPromptRoutersCommand").f(void 0,r92).ser(xY4).de(iW4).build(){static{p(this,"ListPromptRoutersCommand")}},w80=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","ListProvisionedModelThroughputs",{}).n("BedrockClient","ListProvisionedModelThroughputsCommand").f(void 0,void 0).ser(vY4).de(nW4).build(){static{p(this,"ListProvisionedModelThroughputsCommand")}},F42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","ListTagsForResource",{}).n("BedrockClient","ListTagsForResourceCommand").f(void 0,void 0).ser(bY4).de(aW4).build(){static{p(this,"ListTagsForResourceCommand")}},I42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","PutModelInvocationLoggingConfiguration",{}).n("BedrockClient","PutModelInvocationLoggingConfigurationCommand").f(void 0,void 0).ser(fY4).de(sW4).build(){static{p(this,"PutModelInvocationLoggingConfigurationCommand")}},Y42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","PutUseCaseForModelAccess",{}).n("BedrockClient","PutUseCaseForModelAccessCommand").f(void 0,void 0).ser(hY4).de(rW4).build(){static{p(this,"PutUseCaseForModelAccessCommand")}},W42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","RegisterMarketplaceModelEndpoint",{}).n("BedrockClient","RegisterMarketplaceModelEndpointCommand").f(void 0,void 0).ser(gY4).de(oW4).build(){static{p(this,"RegisterMarketplaceModelEndpointCommand")}},J42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","StopEvaluationJob",{}).n("BedrockClient","StopEvaluationJobCommand").f(H92,void 0).ser(uY4).de(tW4).build(){static{p(this,"StopEvaluationJobCommand")}},X42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","StopModelCustomizationJob",{}).n("BedrockClient","StopModelCustomizationJobCommand").f(void 0,void 0).ser(mY4).de(eW4).build(){static{p(this,"StopModelCustomizationJobCommand")}},V42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","StopModelInvocationJob",{}).n("BedrockClient","StopModelInvocationJobCommand").f(void 0,void 0).ser(dY4).de(AJ4).build(){static{p(this,"StopModelInvocationJobCommand")}},C42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","TagResource",{}).n("BedrockClient","TagResourceCommand").f(void 0,void 0).ser(cY4).de(BJ4).build(){static{p(this,"TagResourceCommand")}},K42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","UntagResource",{}).n("BedrockClient","UntagResourceCommand").f(void 0,void 0).ser(lY4).de(QJ4).build(){static{p(this,"UntagResourceCommand")}},H42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","UpdateGuardrail",{}).n("BedrockClient","UpdateGuardrailCommand").f(g92,void 0).ser(pY4).de(DJ4).build(){static{p(this,"UpdateGuardrailCommand")}},z42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","UpdateMarketplaceModelEndpoint",{}).n("BedrockClient","UpdateMarketplaceModelEndpointCommand").f(void 0,void 0).ser(iY4).de(ZJ4).build(){static{p(this,"UpdateMarketplaceModelEndpointCommand")}},E42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),qB.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","UpdateProvisionedModelThroughput",{}).n("BedrockClient","UpdateProvisionedModelThroughputCommand").f(void 0,void 0).ser(nY4).de(GJ4).build(){static{p(this,"UpdateProvisionedModelThroughputCommand")}},GV4={BatchDeleteEvaluationJobCommand:UQ2,CreateCustomModelCommand:wQ2,CreateEvaluationJobCommand:$Q2,CreateFoundationModelAgreementCommand:qQ2,CreateGuardrailCommand:NQ2,CreateGuardrailVersionCommand:LQ2,CreateInferenceProfileCommand:MQ2,CreateMarketplaceModelEndpointCommand:RQ2,CreateModelCopyJobCommand:OQ2,CreateModelCustomizationJobCommand:TQ2,CreateModelImportJobCommand:PQ2,CreateModelInvocationJobCommand:SQ2,CreatePromptRouterCommand:jQ2,CreateProvisionedModelThroughputCommand:yQ2,DeleteCustomModelCommand:kQ2,DeleteFoundationModelAgreementCommand:_Q2,DeleteGuardrailCommand:xQ2,DeleteImportedModelCommand:vQ2,DeleteInferenceProfileCommand:bQ2,DeleteMarketplaceModelEndpointCommand:fQ2,DeleteModelInvocationLoggingConfigurationCommand:hQ2,DeletePromptRouterCommand:gQ2,DeleteProvisionedModelThroughputCommand:uQ2,DeregisterMarketplaceModelEndpointCommand:mQ2,GetCustomModelCommand:dQ2,GetEvaluationJobCommand:cQ2,GetFoundationModelCommand:pQ2,GetFoundationModelAvailabilityCommand:lQ2,GetGuardrailCommand:iQ2,GetImportedModelCommand:nQ2,GetInferenceProfileCommand:aQ2,GetMarketplaceModelEndpointCommand:sQ2,GetModelCopyJobCommand:rQ2,GetModelCustomizationJobCommand:oQ2,GetModelImportJobCommand:tQ2,GetModelInvocationJobCommand:eQ2,GetModelInvocationLoggingConfigurationCommand:A42,GetPromptRouterCommand:B42,GetProvisionedModelThroughputCommand:Q42,GetUseCaseForModelAccessCommand:D42,ListCustomModelsCommand:Y80,ListEvaluationJobsCommand:W80,ListFoundationModelAgreementOffersCommand:Z42,ListFoundationModelsCommand:G42,ListGuardrailsCommand:J80,ListImportedModelsCommand:X80,ListInferenceProfilesCommand:V80,ListMarketplaceModelEndpointsCommand:C80,ListModelCopyJobsCommand:K80,ListModelCustomizationJobsCommand:H80,ListModelImportJobsCommand:z80,ListModelInvocationJobsCommand:E80,ListPromptRoutersCommand:U80,ListProvisionedModelThroughputsCommand:w80,ListTagsForResourceCommand:F42,PutModelInvocationLoggingConfigurationCommand:I42,PutUseCaseForModelAccessCommand:Y42,RegisterMarketplaceModelEndpointCommand:W42,StopEvaluationJobCommand:J42,StopModelCustomizationJobCommand:X42,StopModelInvocationJobCommand:V42,TagResourceCommand:C42,UntagResourceCommand:K42,UpdateGuardrailCommand:H42,UpdateMarketplaceModelEndpointCommand:z42,UpdateProvisionedModelThroughputCommand:E42},U42=class extends MV{static{p(this,"Bedrock")}};_.createAggregatedClient(GV4,U42);var FV4=N2.createPaginator(MV,Y80,"nextToken","nextToken","maxResults"),IV4=N2.createPaginator(MV,W80,"nextToken","nextToken","maxResults"),YV4=N2.createPaginator(MV,J80,"nextToken","nextToken","maxResults"),WV4=N2.createPaginator(MV,X80,"nextToken","nextToken","maxResults"),JV4=N2.createPaginator(MV,V80,"nextToken","nextToken","maxResults"),XV4=N2.createPaginator(MV,C80,"nextToken","nextToken","maxResults"),VV4=N2.createPaginator(MV,K80,"nextToken","nextToken","maxResults"),CV4=N2.createPaginator(MV,H80,"nextToken","nextToken","maxResults"),KV4=N2.createPaginator(MV,z80,"nextToken","nextToken","maxResults"),HV4=N2.createPaginator(MV,E80,"nextToken","nextToken","maxResults"),zV4=N2.createPaginator(MV,U80,"nextToken","nextToken","maxResults"),EV4=N2.createPaginator(MV,w80,"nextToken","nextToken","maxResults")});
var $V=E((fV5,NsA)=>{var{defineProperty:D$1,getOwnPropertyDescriptor:rB4,getOwnPropertyNames:oB4}=Object,tB4=Object.prototype.hasOwnProperty,jk=(A,B)=>D$1(A,"name",{value:B,configurable:!0}),eB4=(A,B)=>{for(var Q in B)D$1(A,Q,{get:B[Q],enumerable:!0})},A94=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of oB4(B))if(!tB4.call(A,Z)&&Z!==Q)D$1(A,Z,{get:()=>B[Z],enumerable:!(D=rB4(B,Z))||D.enumerable})}return A},B94=(A)=>A94(D$1({},"__esModule",{value:!0}),A),UsA={};eB4(UsA,{Field:()=>Z94,Fields:()=>G94,HttpRequest:()=>F94,HttpResponse:()=>I94,IHttpRequest:()=>wsA.HttpRequest,getHttpHandlerExtensionConfiguration:()=>Q94,isValidHostname:()=>qsA,resolveHttpHandlerRuntimeConfig:()=>D94});NsA.exports=B94(UsA);var Q94=jk((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),D94=jk((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),wsA=LQ0(),Z94=class{static{jk(this,"Field")}constructor({name:A,kind:B=wsA.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},G94=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{jk(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},F94=class A{static{jk(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=$sA(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function $sA(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}jk($sA,"cloneQuery");var I94=class{static{jk(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function qsA(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}jk(qsA,"isValidHostname")});
var $eA=E((UeA)=>{Object.defineProperty(UeA,"__esModule",{value:!0});UeA.getRuntimeConfig=void 0;var W54=Jg(),J54=W54.__importDefault(vtA()),KeA=CI(),HeA=i41(),v$1=K4(),X54=gG(),zeA=u4(),Kg=JD(),EeA=k3(),V54=uG(),C54=sZ(),K54=CeA(),H54=C6(),z54=mG(),E54=C6(),U54=(A)=>{E54.emitWarningIfUnsupportedVersion(process.version);let B=z54.resolveDefaultsModeConfig(A),Q=()=>B().then(H54.loadConfigsForDefaultMode),D=K54.getRuntimeConfig(A);KeA.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile,logger:D.logger};return{...D,...A,runtime:"node",defaultsMode:B,authSchemePreference:A?.authSchemePreference??Kg.loadConfig(KeA.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS,Z),bodyLengthChecker:A?.bodyLengthChecker??V54.calculateBodyLength,defaultUserAgentProvider:A?.defaultUserAgentProvider??HeA.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:J54.default.version}),maxAttempts:A?.maxAttempts??Kg.loadConfig(zeA.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??Kg.loadConfig(v$1.NODE_REGION_CONFIG_OPTIONS,{...v$1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:EeA.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??Kg.loadConfig({...zeA.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||C54.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??X54.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??EeA.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??Kg.loadConfig(v$1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??Kg.loadConfig(v$1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??Kg.loadConfig(HeA.NODE_APP_ID_CONFIG_OPTIONS,Z)}};UeA.getRuntimeConfig=U54});
var A12=E((iC5,eeA)=>{var{defineProperty:f$1,getOwnPropertyDescriptor:j54,getOwnPropertyNames:y54}=Object,k54=Object.prototype.hasOwnProperty,S6=(A,B)=>f$1(A,"name",{value:B,configurable:!0}),_54=(A,B)=>{for(var Q in B)f$1(A,Q,{get:B[Q],enumerable:!0})},x54=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of y54(B))if(!k54.call(A,Z)&&Z!==Q)f$1(A,Z,{get:()=>B[Z],enumerable:!(D=j54(B,Z))||D.enumerable})}return A},v54=(A)=>x54(f$1({},"__esModule",{value:!0}),A),_eA={};_54(_eA,{GetRoleCredentialsCommand:()=>reA,GetRoleCredentialsRequestFilterSensitiveLog:()=>heA,GetRoleCredentialsResponseFilterSensitiveLog:()=>ueA,InvalidRequestException:()=>xeA,ListAccountRolesCommand:()=>N40,ListAccountRolesRequestFilterSensitiveLog:()=>meA,ListAccountsCommand:()=>L40,ListAccountsRequestFilterSensitiveLog:()=>deA,LogoutCommand:()=>oeA,LogoutRequestFilterSensitiveLog:()=>ceA,ResourceNotFoundException:()=>veA,RoleCredentialsFilterSensitiveLog:()=>geA,SSO:()=>teA,SSOClient:()=>g$1,SSOServiceException:()=>oa,TooManyRequestsException:()=>beA,UnauthorizedException:()=>feA,__Client:()=>TB.Client,paginateListAccountRoles:()=>I34,paginateListAccounts:()=>Y34});eeA.exports=v54(_eA);var TeA=h41(),b54=g41(),f54=u41(),PeA=na(),h54=K4(),ST=CB(),g54=bG(),r41=R6(),SeA=u4(),jeA=E40(),u54=S6((A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,defaultSigningName:"awsssoportal"})},"resolveClientEndpointParameters"),h$1={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}},m54=$eA(),yeA=a41(),keA=$V(),TB=C6(),d54=S6((A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}},"getHttpAuthExtensionConfiguration"),c54=S6((A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}},"resolveHttpAuthRuntimeConfig"),l54=S6((A,B)=>{let Q=Object.assign(yeA.getAwsRegionExtensionConfiguration(A),TB.getDefaultExtensionConfiguration(A),keA.getHttpHandlerExtensionConfiguration(A),d54(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,yeA.resolveAwsRegionExtensionConfiguration(Q),TB.resolveDefaultRuntimeConfig(Q),keA.resolveHttpHandlerRuntimeConfig(Q),c54(Q))},"resolveRuntimeExtensions"),g$1=class extends TB.Client{static{S6(this,"SSOClient")}config;constructor(...[A]){let B=m54.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=u54(B),D=PeA.resolveUserAgentConfig(Q),Z=SeA.resolveRetryConfig(D),G=h54.resolveRegionConfig(Z),F=TeA.resolveHostHeaderConfig(G),I=r41.resolveEndpointConfig(F),Y=jeA.resolveHttpAuthSchemeConfig(I),W=l54(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(PeA.getUserAgentPlugin(this.config)),this.middlewareStack.use(SeA.getRetryPlugin(this.config)),this.middlewareStack.use(g54.getContentLengthPlugin(this.config)),this.middlewareStack.use(TeA.getHostHeaderPlugin(this.config)),this.middlewareStack.use(b54.getLoggerPlugin(this.config)),this.middlewareStack.use(f54.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(ST.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:jeA.defaultSSOHttpAuthSchemeParametersProvider,identityProviderConfigProvider:S6(async(J)=>new ST.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials}),"identityProviderConfigProvider")})),this.middlewareStack.use(ST.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}},u$1=j3(),oa=class A extends TB.ServiceException{static{S6(this,"SSOServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},xeA=class A extends oa{static{S6(this,"InvalidRequestException")}name="InvalidRequestException";$fault="client";constructor(B){super({name:"InvalidRequestException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},veA=class A extends oa{static{S6(this,"ResourceNotFoundException")}name="ResourceNotFoundException";$fault="client";constructor(B){super({name:"ResourceNotFoundException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},beA=class A extends oa{static{S6(this,"TooManyRequestsException")}name="TooManyRequestsException";$fault="client";constructor(B){super({name:"TooManyRequestsException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},feA=class A extends oa{static{S6(this,"UnauthorizedException")}name="UnauthorizedException";$fault="client";constructor(B){super({name:"UnauthorizedException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},heA=S6((A)=>({...A,...A.accessToken&&{accessToken:TB.SENSITIVE_STRING}}),"GetRoleCredentialsRequestFilterSensitiveLog"),geA=S6((A)=>({...A,...A.secretAccessKey&&{secretAccessKey:TB.SENSITIVE_STRING},...A.sessionToken&&{sessionToken:TB.SENSITIVE_STRING}}),"RoleCredentialsFilterSensitiveLog"),ueA=S6((A)=>({...A,...A.roleCredentials&&{roleCredentials:geA(A.roleCredentials)}}),"GetRoleCredentialsResponseFilterSensitiveLog"),meA=S6((A)=>({...A,...A.accessToken&&{accessToken:TB.SENSITIVE_STRING}}),"ListAccountRolesRequestFilterSensitiveLog"),deA=S6((A)=>({...A,...A.accessToken&&{accessToken:TB.SENSITIVE_STRING}}),"ListAccountsRequestFilterSensitiveLog"),ceA=S6((A)=>({...A,...A.accessToken&&{accessToken:TB.SENSITIVE_STRING}}),"LogoutRequestFilterSensitiveLog"),s41=CI(),p54=S6(async(A,B)=>{let Q=ST.requestBuilder(A,B),D=TB.map({},TB.isSerializableHeaderValue,{[c$1]:A[d$1]});Q.bp("/federation/credentials");let Z=TB.map({[G34]:[,TB.expectNonNull(A[Z34],"roleName")],[peA]:[,TB.expectNonNull(A[leA],"accountId")]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_GetRoleCredentialsCommand"),i54=S6(async(A,B)=>{let Q=ST.requestBuilder(A,B),D=TB.map({},TB.isSerializableHeaderValue,{[c$1]:A[d$1]});Q.bp("/assignment/roles");let Z=TB.map({[seA]:[,A[aeA]],[neA]:[()=>A.maxResults!==void 0,()=>A[ieA].toString()],[peA]:[,TB.expectNonNull(A[leA],"accountId")]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListAccountRolesCommand"),n54=S6(async(A,B)=>{let Q=ST.requestBuilder(A,B),D=TB.map({},TB.isSerializableHeaderValue,{[c$1]:A[d$1]});Q.bp("/assignment/accounts");let Z=TB.map({[seA]:[,A[aeA]],[neA]:[()=>A.maxResults!==void 0,()=>A[ieA].toString()]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListAccountsCommand"),a54=S6(async(A,B)=>{let Q=ST.requestBuilder(A,B),D=TB.map({},TB.isSerializableHeaderValue,{[c$1]:A[d$1]});Q.bp("/logout");let Z;return Q.m("POST").h(D).b(Z),Q.build()},"se_LogoutCommand"),s54=S6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return m$1(A,B);let Q=TB.map({$metadata:xk(A)}),D=TB.expectNonNull(TB.expectObject(await s41.parseJsonBody(A.body,B)),"body"),Z=TB.take(D,{roleCredentials:TB._json});return Object.assign(Q,Z),Q},"de_GetRoleCredentialsCommand"),r54=S6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return m$1(A,B);let Q=TB.map({$metadata:xk(A)}),D=TB.expectNonNull(TB.expectObject(await s41.parseJsonBody(A.body,B)),"body"),Z=TB.take(D,{nextToken:TB.expectString,roleList:TB._json});return Object.assign(Q,Z),Q},"de_ListAccountRolesCommand"),o54=S6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return m$1(A,B);let Q=TB.map({$metadata:xk(A)}),D=TB.expectNonNull(TB.expectObject(await s41.parseJsonBody(A.body,B)),"body"),Z=TB.take(D,{accountList:TB._json,nextToken:TB.expectString});return Object.assign(Q,Z),Q},"de_ListAccountsCommand"),t54=S6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return m$1(A,B);let Q=TB.map({$metadata:xk(A)});return await TB.collectBody(A.body,B),Q},"de_LogoutCommand"),m$1=S6(async(A,B)=>{let Q={...A,body:await s41.parseJsonErrorBody(A.body,B)},D=s41.loadRestJsonErrorCode(A,Q.body);switch(D){case"InvalidRequestException":case"com.amazonaws.sso#InvalidRequestException":throw await A34(Q,B);case"ResourceNotFoundException":case"com.amazonaws.sso#ResourceNotFoundException":throw await B34(Q,B);case"TooManyRequestsException":case"com.amazonaws.sso#TooManyRequestsException":throw await Q34(Q,B);case"UnauthorizedException":case"com.amazonaws.sso#UnauthorizedException":throw await D34(Q,B);default:let Z=Q.body;return e54({output:A,parsedBody:Z,errorCode:D})}},"de_CommandError"),e54=TB.withBaseException(oa),A34=S6(async(A,B)=>{let Q=TB.map({}),D=A.body,Z=TB.take(D,{message:TB.expectString});Object.assign(Q,Z);let G=new xeA({$metadata:xk(A),...Q});return TB.decorateServiceException(G,A.body)},"de_InvalidRequestExceptionRes"),B34=S6(async(A,B)=>{let Q=TB.map({}),D=A.body,Z=TB.take(D,{message:TB.expectString});Object.assign(Q,Z);let G=new veA({$metadata:xk(A),...Q});return TB.decorateServiceException(G,A.body)},"de_ResourceNotFoundExceptionRes"),Q34=S6(async(A,B)=>{let Q=TB.map({}),D=A.body,Z=TB.take(D,{message:TB.expectString});Object.assign(Q,Z);let G=new beA({$metadata:xk(A),...Q});return TB.decorateServiceException(G,A.body)},"de_TooManyRequestsExceptionRes"),D34=S6(async(A,B)=>{let Q=TB.map({}),D=A.body,Z=TB.take(D,{message:TB.expectString});Object.assign(Q,Z);let G=new feA({$metadata:xk(A),...Q});return TB.decorateServiceException(G,A.body)},"de_UnauthorizedExceptionRes"),xk=S6((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),leA="accountId",d$1="accessToken",peA="account_id",ieA="maxResults",neA="max_result",aeA="nextToken",seA="next_token",Z34="roleName",G34="role_name",c$1="x-amz-sso_bearer_token",reA=class extends TB.Command.classBuilder().ep(h$1).m(function(A,B,Q,D){return[u$1.getSerdePlugin(Q,this.serialize,this.deserialize),r41.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","GetRoleCredentials",{}).n("SSOClient","GetRoleCredentialsCommand").f(heA,ueA).ser(p54).de(s54).build(){static{S6(this,"GetRoleCredentialsCommand")}},N40=class extends TB.Command.classBuilder().ep(h$1).m(function(A,B,Q,D){return[u$1.getSerdePlugin(Q,this.serialize,this.deserialize),r41.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","ListAccountRoles",{}).n("SSOClient","ListAccountRolesCommand").f(meA,void 0).ser(i54).de(r54).build(){static{S6(this,"ListAccountRolesCommand")}},L40=class extends TB.Command.classBuilder().ep(h$1).m(function(A,B,Q,D){return[u$1.getSerdePlugin(Q,this.serialize,this.deserialize),r41.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","ListAccounts",{}).n("SSOClient","ListAccountsCommand").f(deA,void 0).ser(n54).de(o54).build(){static{S6(this,"ListAccountsCommand")}},oeA=class extends TB.Command.classBuilder().ep(h$1).m(function(A,B,Q,D){return[u$1.getSerdePlugin(Q,this.serialize,this.deserialize),r41.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","Logout",{}).n("SSOClient","LogoutCommand").f(ceA,void 0).ser(a54).de(t54).build(){static{S6(this,"LogoutCommand")}},F34={GetRoleCredentialsCommand:reA,ListAccountRolesCommand:N40,ListAccountsCommand:L40,LogoutCommand:oeA},teA=class extends g$1{static{S6(this,"SSO")}};TB.createAggregatedClient(F34,teA);var I34=ST.createPaginator(g$1,N40,"nextToken","nextToken","maxResults"),Y34=ST.createPaginator(g$1,L40,"nextToken","nextToken","maxResults")});
var AB2=E((t22)=>{Object.defineProperty(t22,"__esModule",{value:!0});t22.getRuntimeConfig=void 0;var oG4=CI(),tG4=CB(),eG4=C6(),AF4=JZ(),r22=kk(),o22=cB(),BF4=X40(),QF4=s22(),DF4=(A)=>{return{apiVersion:"2023-04-20",base64Decoder:A?.base64Decoder??r22.fromBase64,base64Encoder:A?.base64Encoder??r22.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??QF4.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??BF4.defaultBedrockHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new oG4.AwsSdkSigV4Signer},{schemeId:"smithy.api#httpBearerAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#httpBearerAuth"),signer:new tG4.HttpBearerAuthSigner}],logger:A?.logger??new eG4.NoOpLogger,serviceId:A?.serviceId??"Bedrock",urlParser:A?.urlParser??AF4.parseUrl,utf8Decoder:A?.utf8Decoder??o22.fromUtf8,utf8Encoder:A?.utf8Encoder??o22.toUtf8}};t22.getRuntimeConfig=DF4});
var B61=E((p40)=>{Object.defineProperty(p40,"__esModule",{value:!0});p40.STSClient=p40.__Client=void 0;var LA2=h41(),vD4=g41(),bD4=u41(),MA2=na(),fD4=K4(),l40=CB(),hD4=bG(),gD4=R6(),RA2=u4(),TA2=C6();Object.defineProperty(p40,"__Client",{enumerable:!0,get:function(){return TA2.Client}});var OA2=h40(),uD4=Q61(),mD4=VA2(),dD4=NA2();class PA2 extends TA2.Client{config;constructor(...[A]){let B=mD4.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=uD4.resolveClientEndpointParameters(B),D=MA2.resolveUserAgentConfig(Q),Z=RA2.resolveRetryConfig(D),G=fD4.resolveRegionConfig(Z),F=LA2.resolveHostHeaderConfig(G),I=gD4.resolveEndpointConfig(F),Y=OA2.resolveHttpAuthSchemeConfig(I),W=dD4.resolveRuntimeExtensions(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(MA2.getUserAgentPlugin(this.config)),this.middlewareStack.use(RA2.getRetryPlugin(this.config)),this.middlewareStack.use(hD4.getContentLengthPlugin(this.config)),this.middlewareStack.use(LA2.getHostHeaderPlugin(this.config)),this.middlewareStack.use(vD4.getLoggerPlugin(this.config)),this.middlewareStack.use(bD4.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(l40.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:OA2.defaultSTSHttpAuthSchemeParametersProvider,identityProviderConfigProvider:async(J)=>new l40.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials})})),this.middlewareStack.use(l40.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}}p40.STSClient=PA2});
var BA2=E((e02)=>{Object.defineProperty(e02,"__esModule",{value:!0});e02.defaultEndpointResolver=void 0;var WD4=ma(),d40=S7(),JD4=t02(),XD4=new d40.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS","UseGlobalEndpoint"]}),VD4=(A,B={})=>{return XD4.get(A,()=>d40.resolveEndpoint(JD4.ruleSet,{endpointParams:A,logger:B.logger}))};e02.defaultEndpointResolver=VD4;d40.customEndpointFunctions.aws=WD4.awsEndpointFunctions});
var C6=E((GC5,rQ0)=>{var{defineProperty:O$1,getOwnPropertyDescriptor:Y44,getOwnPropertyNames:W44}=Object,J44=Object.prototype.hasOwnProperty,$8=(A,B)=>O$1(A,"name",{value:B,configurable:!0}),X44=(A,B)=>{for(var Q in B)O$1(A,Q,{get:B[Q],enumerable:!0})},pQ0=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of W44(B))if(!J44.call(A,Z)&&Z!==Q)O$1(A,Z,{get:()=>B[Z],enumerable:!(D=Y44(B,Z))||D.enumerable})}return A},V44=(A,B,Q)=>(pQ0(A,B,"default"),Q&&pQ0(Q,B,"default")),C44=(A)=>pQ0(O$1({},"__esModule",{value:!0}),A),aQ0={};X44(aQ0,{Client:()=>K44,Command:()=>SoA,NoOpLogger:()=>y44,SENSITIVE_STRING:()=>z44,ServiceException:()=>U44,_json:()=>nQ0,collectBody:()=>lQ0.collectBody,convertMap:()=>k44,createAggregatedClient:()=>E44,decorateServiceException:()=>joA,emitWarningIfUnsupportedVersion:()=>N44,extendedEncodeURIComponent:()=>lQ0.extendedEncodeURIComponent,getArrayIfSingleItem:()=>S44,getDefaultClientConfiguration:()=>T44,getDefaultExtensionConfiguration:()=>koA,getValueFromTextNode:()=>_oA,isSerializableHeaderValue:()=>j44,loadConfigsForDefaultMode:()=>q44,map:()=>sQ0,resolveDefaultRuntimeConfig:()=>P44,resolvedPath:()=>lQ0.resolvedPath,serializeDateTime:()=>h44,serializeFloat:()=>f44,take:()=>_44,throwDefaultError:()=>yoA,withBaseException:()=>w44});rQ0.exports=C44(aQ0);var PoA=Mw(),K44=class{constructor(A){this.config=A,this.middlewareStack=PoA.constructStack()}static{$8(this,"Client")}send(A,B,Q){let D=typeof B!=="function"?B:void 0,Z=typeof B==="function"?B:Q,G=D===void 0&&this.config.cacheMiddleware===!0,F;if(G){if(!this.handlers)this.handlers=new WeakMap;let I=this.handlers;if(I.has(A.constructor))F=I.get(A.constructor);else F=A.resolveMiddleware(this.middlewareStack,this.config,D),I.set(A.constructor,F)}else delete this.handlers,F=A.resolveMiddleware(this.middlewareStack,this.config,D);if(Z)F(A).then((I)=>Z(null,I.output),(I)=>Z(I)).catch(()=>{});else return F(A).then((I)=>I.output)}destroy(){this.config?.requestHandler?.destroy?.(),delete this.handlers}},lQ0=M6(),iQ0=LQ0(),SoA=class{constructor(){this.middlewareStack=PoA.constructStack()}static{$8(this,"Command")}static classBuilder(){return new H44}resolveMiddlewareWithContext(A,B,Q,{middlewareFn:D,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,smithyContext:Y,additionalContext:W,CommandCtor:J}){for(let H of D.bind(this)(J,A,B,Q))this.middlewareStack.use(H);let X=A.concat(this.middlewareStack),{logger:V}=B,C={logger:V,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,[iQ0.SMITHY_CONTEXT_KEY]:{commandInstance:this,...Y},...W},{requestHandler:K}=B;return X.resolve((H)=>K.handle(H.request,Q||{}),C)}},H44=class{constructor(){this._init=()=>{},this._ep={},this._middlewareFn=()=>[],this._commandName="",this._clientName="",this._additionalContext={},this._smithyContext={},this._inputFilterSensitiveLog=(A)=>A,this._outputFilterSensitiveLog=(A)=>A,this._serializer=null,this._deserializer=null}static{$8(this,"ClassBuilder")}init(A){this._init=A}ep(A){return this._ep=A,this}m(A){return this._middlewareFn=A,this}s(A,B,Q={}){return this._smithyContext={service:A,operation:B,...Q},this}c(A={}){return this._additionalContext=A,this}n(A,B){return this._clientName=A,this._commandName=B,this}f(A=(Q)=>Q,B=(Q)=>Q){return this._inputFilterSensitiveLog=A,this._outputFilterSensitiveLog=B,this}ser(A){return this._serializer=A,this}de(A){return this._deserializer=A,this}sc(A){return this._operationSchema=A,this._smithyContext.operationSchema=A,this}build(){let A=this,B;return B=class extends SoA{constructor(...[Q]){super();this.serialize=A._serializer,this.deserialize=A._deserializer,this.input=Q??{},A._init(this),this.schema=A._operationSchema}static{$8(this,"CommandRef")}static getEndpointParameterInstructions(){return A._ep}resolveMiddleware(Q,D,Z){return this.resolveMiddlewareWithContext(Q,D,Z,{CommandCtor:B,middlewareFn:A._middlewareFn,clientName:A._clientName,commandName:A._commandName,inputFilterSensitiveLog:A._inputFilterSensitiveLog,outputFilterSensitiveLog:A._outputFilterSensitiveLog,smithyContext:A._smithyContext,additionalContext:A._additionalContext})}}}},z44="***SensitiveInformation***",E44=$8((A,B)=>{for(let Q of Object.keys(A)){let D=A[Q],Z=$8(async function(F,I,Y){let W=new D(F);if(typeof I==="function")this.send(W,I);else if(typeof Y==="function"){if(typeof I!=="object")throw new Error(`Expected http options but got ${typeof I}`);this.send(W,I||{},Y)}else return this.send(W,I)},"methodImpl"),G=(Q[0].toLowerCase()+Q.slice(1)).replace(/Command$/,"");B.prototype[G]=Z}},"createAggregatedClient"),U44=class A extends Error{static{$8(this,"ServiceException")}constructor(B){super(B.message);Object.setPrototypeOf(this,Object.getPrototypeOf(this).constructor.prototype),this.name=B.name,this.$fault=B.$fault,this.$metadata=B.$metadata}static isInstance(B){if(!B)return!1;let Q=B;return A.prototype.isPrototypeOf(Q)||Boolean(Q.$fault)&&Boolean(Q.$metadata)&&(Q.$fault==="client"||Q.$fault==="server")}static[Symbol.hasInstance](B){if(!B)return!1;let Q=B;if(this===A)return A.isInstance(B);if(A.isInstance(B)){if(Q.name&&this.name)return this.prototype.isPrototypeOf(B)||Q.name===this.name;return this.prototype.isPrototypeOf(B)}return!1}},joA=$8((A,B={})=>{Object.entries(B).filter(([,D])=>D!==void 0).forEach(([D,Z])=>{if(A[D]==null||A[D]==="")A[D]=Z});let Q=A.message||A.Message||"UnknownError";return A.message=Q,delete A.Message,A},"decorateServiceException"),yoA=$8(({output:A,parsedBody:B,exceptionCtor:Q,errorCode:D})=>{let Z=$44(A),G=Z.httpStatusCode?Z.httpStatusCode+"":void 0,F=new Q({name:B?.code||B?.Code||D||G||"UnknownError",$fault:"client",$metadata:Z});throw joA(F,B)},"throwDefaultError"),w44=$8((A)=>{return({output:B,parsedBody:Q,errorCode:D})=>{yoA({output:B,parsedBody:Q,exceptionCtor:A,errorCode:D})}},"withBaseException"),$44=$8((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),q44=$8((A)=>{switch(A){case"standard":return{retryMode:"standard",connectionTimeout:3100};case"in-region":return{retryMode:"standard",connectionTimeout:1100};case"cross-region":return{retryMode:"standard",connectionTimeout:3100};case"mobile":return{retryMode:"standard",connectionTimeout:30000};default:return{}}},"loadConfigsForDefaultMode"),ToA=!1,N44=$8((A)=>{if(A&&!ToA&&parseInt(A.substring(1,A.indexOf(".")))<16)ToA=!0},"emitWarningIfUnsupportedVersion"),L44=$8((A)=>{let B=[];for(let Q in iQ0.AlgorithmId){let D=iQ0.AlgorithmId[Q];if(A[D]===void 0)continue;B.push({algorithmId:()=>D,checksumConstructor:()=>A[D]})}return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),M44=$8((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),R44=$8((A)=>{return{setRetryStrategy(B){A.retryStrategy=B},retryStrategy(){return A.retryStrategy}}},"getRetryConfiguration"),O44=$8((A)=>{let B={};return B.retryStrategy=A.retryStrategy(),B},"resolveRetryRuntimeConfig"),koA=$8((A)=>{return Object.assign(L44(A),R44(A))},"getDefaultExtensionConfiguration"),T44=koA,P44=$8((A)=>{return Object.assign(M44(A),O44(A))},"resolveDefaultRuntimeConfig"),S44=$8((A)=>Array.isArray(A)?A:[A],"getArrayIfSingleItem"),_oA=$8((A)=>{for(let Q in A)if(A.hasOwnProperty(Q)&&A[Q]["#text"]!==void 0)A[Q]=A[Q]["#text"];else if(typeof A[Q]==="object"&&A[Q]!==null)A[Q]=_oA(A[Q]);return A},"getValueFromTextNode"),j44=$8((A)=>{return A!=null},"isSerializableHeaderValue"),y44=class{static{$8(this,"NoOpLogger")}trace(){}debug(){}info(){}warn(){}error(){}};function sQ0(A,B,Q){let D,Z,G;if(typeof B==="undefined"&&typeof Q==="undefined")D={},G=A;else if(D=A,typeof B==="function")return Z=B,G=Q,x44(D,Z,G);else G=B;for(let F of Object.keys(G)){if(!Array.isArray(G[F])){D[F]=G[F];continue}xoA(D,null,G,F)}return D}$8(sQ0,"map");var k44=$8((A)=>{let B={};for(let[Q,D]of Object.entries(A||{}))B[Q]=[,D];return B},"convertMap"),_44=$8((A,B)=>{let Q={};for(let D in B)xoA(Q,A,B,D);return Q},"take"),x44=$8((A,B,Q)=>{return sQ0(A,Object.entries(Q).reduce((D,[Z,G])=>{if(Array.isArray(G))D[Z]=G;else if(typeof G==="function")D[Z]=[B,G()];else D[Z]=[B,G];return D},{}))},"mapWithFilter"),xoA=$8((A,B,Q,D)=>{if(B!==null){let F=Q[D];if(typeof F==="function")F=[,F];let[I=v44,Y=b44,W=D]=F;if(typeof I==="function"&&I(B[W])||typeof I!=="function"&&!!I)A[D]=Y(B[W]);return}let[Z,G]=Q[D];if(typeof G==="function"){let F,I=Z===void 0&&(F=G())!=null,Y=typeof Z==="function"&&!!Z(void 0)||typeof Z!=="function"&&!!Z;if(I)A[D]=F;else if(Y)A[D]=G()}else{let F=Z===void 0&&G!=null,I=typeof Z==="function"&&!!Z(G)||typeof Z!=="function"&&!!Z;if(F||I)A[D]=G}},"applyInstruction"),v44=$8((A)=>A!=null,"nonNullish"),b44=$8((A)=>A,"pass"),f44=$8((A)=>{if(A!==A)return"NaN";switch(A){case 1/0:return"Infinity";case-1/0:return"-Infinity";default:return A}},"serializeFloat"),h44=$8((A)=>A.toISOString().replace(".000Z","Z"),"serializeDateTime"),nQ0=$8((A)=>{if(A==null)return{};if(Array.isArray(A))return A.filter((B)=>B!=null).map(nQ0);if(typeof A==="object"){let B={};for(let Q of Object.keys(A)){if(A[Q]==null)continue;B[Q]=nQ0(A[Q])}return B}return A},"_json");V44(aQ0,X6(),rQ0.exports)});
var CI=E((p41)=>{Object.defineProperty(p41,"__esModule",{value:!0});var F40=Jg();F40.__exportStar(_w(),p41);F40.__exportStar(mQ0(),p41);F40.__exportStar(roA(),p41)});
var CeA=E((XeA)=>{Object.defineProperty(XeA,"__esModule",{value:!0});XeA.getRuntimeConfig=void 0;var Q54=CI(),D54=CB(),Z54=C6(),G54=JZ(),WeA=kk(),JeA=cB(),F54=E40(),I54=YeA(),Y54=(A)=>{return{apiVersion:"2019-06-10",base64Decoder:A?.base64Decoder??WeA.fromBase64,base64Encoder:A?.base64Encoder??WeA.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??I54.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??F54.defaultSSOHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new Q54.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new D54.NoAuthSigner}],logger:A?.logger??new Z54.NoOpLogger,serviceId:A?.serviceId??"SSO",urlParser:A?.urlParser??G54.parseUrl,utf8Decoder:A?.utf8Decoder??JeA.fromUtf8,utf8Encoder:A?.utf8Encoder??JeA.toUtf8}};XeA.getRuntimeConfig=Y54});
var E40=E((_tA)=>{Object.defineProperty(_tA,"__esModule",{value:!0});_tA.resolveHttpAuthSchemeConfig=_tA.defaultSSOHttpAuthSchemeProvider=_tA.defaultSSOHttpAuthSchemeParametersProvider=void 0;var k84=CI(),z40=J5(),_84=async(A,B,Q)=>{return{operation:z40.getSmithyContext(B).operation,region:await z40.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};_tA.defaultSSOHttpAuthSchemeParametersProvider=_84;function x84(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"awsssoportal",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function k$1(A){return{schemeId:"smithy.api#noAuth"}}var v84=(A)=>{let B=[];switch(A.operation){case"GetRoleCredentials":{B.push(k$1(A));break}case"ListAccountRoles":{B.push(k$1(A));break}case"ListAccounts":{B.push(k$1(A));break}case"Logout":{B.push(k$1(A));break}default:B.push(x84(A))}return B};_tA.defaultSSOHttpAuthSchemeProvider=v84;var b84=(A)=>{let B=k84.resolveAwsSdkSigV4Config(A);return Object.assign(B,{authSchemePreference:z40.normalizeProvider(A.authSchemePreference??[])})};_tA.resolveHttpAuthSchemeConfig=b84});
var FA2=E((ZA2)=>{Object.defineProperty(ZA2,"__esModule",{value:!0});ZA2.getRuntimeConfig=void 0;var CD4=CI(),KD4=CB(),HD4=C6(),zD4=JZ(),QA2=kk(),DA2=cB(),ED4=h40(),UD4=BA2(),wD4=(A)=>{return{apiVersion:"2011-06-15",base64Decoder:A?.base64Decoder??QA2.fromBase64,base64Encoder:A?.base64Encoder??QA2.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??UD4.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??ED4.defaultSTSHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new CD4.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new KD4.NoAuthSigner}],logger:A?.logger??new HD4.NoOpLogger,serviceId:A?.serviceId??"STS",urlParser:A?.urlParser??zD4.parseUrl,utf8Decoder:A?.utf8Decoder??DA2.fromUtf8,utf8Encoder:A?.utf8Encoder??DA2.toUtf8}};ZA2.getRuntimeConfig=wD4});
var GeA=E((DeA)=>{Object.defineProperty(DeA,"__esModule",{value:!0});DeA.ruleSet=void 0;var etA="required",bz="fn",fz="argv",ra="ref",ctA=!0,ltA="isSet",n41="booleanEquals",aa="error",sa="endpoint",PT="tree",w40="PartitionResult",$40="getAttr",ptA={[etA]:!1,type:"String"},itA={[etA]:!0,default:!1,type:"Boolean"},ntA={[ra]:"Endpoint"},AeA={[bz]:n41,[fz]:[{[ra]:"UseFIPS"},!0]},BeA={[bz]:n41,[fz]:[{[ra]:"UseDualStack"},!0]},vz={},atA={[bz]:$40,[fz]:[{[ra]:w40},"supportsFIPS"]},QeA={[ra]:w40},stA={[bz]:n41,[fz]:[!0,{[bz]:$40,[fz]:[QeA,"supportsDualStack"]}]},rtA=[AeA],otA=[BeA],ttA=[{[ra]:"Region"}],o84={version:"1.0",parameters:{Region:ptA,UseDualStack:itA,UseFIPS:itA,Endpoint:ptA},rules:[{conditions:[{[bz]:ltA,[fz]:[ntA]}],rules:[{conditions:rtA,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:aa},{conditions:otA,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:aa},{endpoint:{url:ntA,properties:vz,headers:vz},type:sa}],type:PT},{conditions:[{[bz]:ltA,[fz]:ttA}],rules:[{conditions:[{[bz]:"aws.partition",[fz]:ttA,assign:w40}],rules:[{conditions:[AeA,BeA],rules:[{conditions:[{[bz]:n41,[fz]:[ctA,atA]},stA],rules:[{endpoint:{url:"https://portal.sso-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:vz,headers:vz},type:sa}],type:PT},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:aa}],type:PT},{conditions:rtA,rules:[{conditions:[{[bz]:n41,[fz]:[atA,ctA]}],rules:[{conditions:[{[bz]:"stringEquals",[fz]:[{[bz]:$40,[fz]:[QeA,"name"]},"aws-us-gov"]}],endpoint:{url:"https://portal.sso.{Region}.amazonaws.com",properties:vz,headers:vz},type:sa},{endpoint:{url:"https://portal.sso-fips.{Region}.{PartitionResult#dnsSuffix}",properties:vz,headers:vz},type:sa}],type:PT},{error:"FIPS is enabled but this partition does not support FIPS",type:aa}],type:PT},{conditions:otA,rules:[{conditions:[stA],rules:[{endpoint:{url:"https://portal.sso.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:vz,headers:vz},type:sa}],type:PT},{error:"DualStack is enabled but this partition does not support DualStack",type:aa}],type:PT},{endpoint:{url:"https://portal.sso.{Region}.{PartitionResult#dnsSuffix}",properties:vz,headers:vz},type:sa}],type:PT}],type:PT},{error:"Invalid Configuration: Missing Region",type:aa}]};DeA.ruleSet=o84});
var GoA=E((sV5,ZoA)=>{var{defineProperty:L$1,getOwnPropertyDescriptor:VQ4,getOwnPropertyNames:CQ4}=Object,KQ4=Object.prototype.hasOwnProperty,dY=(A,B)=>L$1(A,"name",{value:B,configurable:!0}),HQ4=(A,B)=>{for(var Q in B)L$1(A,Q,{get:B[Q],enumerable:!0})},zQ4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of CQ4(B))if(!KQ4.call(A,Z)&&Z!==Q)L$1(A,Z,{get:()=>B[Z],enumerable:!(D=VQ4(B,Z))||D.enumerable})}return A},EQ4=(A)=>zQ4(L$1({},"__esModule",{value:!0}),A),brA={};HQ4(brA,{ALGORITHM_IDENTIFIER:()=>E$1,ALGORITHM_IDENTIFIER_V4A:()=>qQ4,ALGORITHM_QUERY_PARAM:()=>frA,ALWAYS_UNSIGNABLE_HEADERS:()=>prA,AMZ_DATE_HEADER:()=>bQ0,AMZ_DATE_QUERY_PARAM:()=>kQ0,AUTH_HEADER:()=>vQ0,CREDENTIAL_QUERY_PARAM:()=>hrA,DATE_HEADER:()=>mrA,EVENT_ALGORITHM_IDENTIFIER:()=>arA,EXPIRES_QUERY_PARAM:()=>urA,GENERATED_HEADERS:()=>drA,HOST_HEADER:()=>wQ4,KEY_TYPE_IDENTIFIER:()=>fQ0,MAX_CACHE_SIZE:()=>rrA,MAX_PRESIGNED_TTL:()=>orA,PROXY_HEADER_PATTERN:()=>irA,REGION_SET_PARAM:()=>UQ4,SEC_HEADER_PATTERN:()=>nrA,SHA256_HEADER:()=>N$1,SIGNATURE_HEADER:()=>crA,SIGNATURE_QUERY_PARAM:()=>_Q0,SIGNED_HEADERS_QUERY_PARAM:()=>grA,SignatureV4:()=>kQ4,SignatureV4Base:()=>DoA,TOKEN_HEADER:()=>lrA,TOKEN_QUERY_PARAM:()=>xQ0,UNSIGNABLE_PATTERNS:()=>$Q4,UNSIGNED_PAYLOAD:()=>srA,clearCredentialCache:()=>LQ4,createScope:()=>w$1,getCanonicalHeaders:()=>SQ0,getCanonicalQuery:()=>QoA,getPayloadHash:()=>$$1,getSigningKey:()=>trA,hasHeader:()=>erA,moveHeadersToQuery:()=>BoA,prepareRequest:()=>yQ0,signatureV4aContainer:()=>_Q4});ZoA.exports=EQ4(brA);var krA=cB(),frA="X-Amz-Algorithm",hrA="X-Amz-Credential",kQ0="X-Amz-Date",grA="X-Amz-SignedHeaders",urA="X-Amz-Expires",_Q0="X-Amz-Signature",xQ0="X-Amz-Security-Token",UQ4="X-Amz-Region-Set",vQ0="authorization",bQ0=kQ0.toLowerCase(),mrA="date",drA=[vQ0,bQ0,mrA],crA=_Q0.toLowerCase(),N$1="x-amz-content-sha256",lrA=xQ0.toLowerCase(),wQ4="host",prA={authorization:!0,"cache-control":!0,connection:!0,expect:!0,from:!0,"keep-alive":!0,"max-forwards":!0,pragma:!0,referer:!0,te:!0,trailer:!0,"transfer-encoding":!0,upgrade:!0,"user-agent":!0,"x-amzn-trace-id":!0},irA=/^proxy-/,nrA=/^sec-/,$Q4=[/^proxy-/i,/^sec-/i],E$1="AWS4-HMAC-SHA256",qQ4="AWS4-ECDSA-P256-SHA256",arA="AWS4-HMAC-SHA256-PAYLOAD",srA="UNSIGNED-PAYLOAD",rrA=50,fQ0="aws4_request",orA=604800,yk=ay(),NQ4=cB(),ca={},U$1=[],w$1=dY((A,B,Q)=>`${A}/${B}/${Q}/${fQ0}`,"createScope"),trA=dY(async(A,B,Q,D,Z)=>{let G=await _rA(A,B.secretAccessKey,B.accessKeyId),F=`${Q}:${D}:${Z}:${yk.toHex(G)}:${B.sessionToken}`;if(F in ca)return ca[F];U$1.push(F);while(U$1.length>rrA)delete ca[U$1.shift()];let I=`AWS4${B.secretAccessKey}`;for(let Y of[Q,D,Z,fQ0])I=await _rA(A,I,Y);return ca[F]=I},"getSigningKey"),LQ4=dY(()=>{U$1.length=0,Object.keys(ca).forEach((A)=>{delete ca[A]})},"clearCredentialCache"),_rA=dY((A,B,Q)=>{let D=new A(B);return D.update(NQ4.toUint8Array(Q)),D.digest()},"hmac"),SQ0=dY(({headers:A},B,Q)=>{let D={};for(let Z of Object.keys(A).sort()){if(A[Z]==null)continue;let G=Z.toLowerCase();if(G in prA||B?.has(G)||irA.test(G)||nrA.test(G)){if(!Q||Q&&!Q.has(G))continue}D[G]=A[Z].trim().replace(/\s+/g," ")}return D},"getCanonicalHeaders"),MQ4=TrA(),RQ4=cB(),$$1=dY(async({headers:A,body:B},Q)=>{for(let D of Object.keys(A))if(D.toLowerCase()===N$1)return A[D];if(B==null)return"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855";else if(typeof B==="string"||ArrayBuffer.isView(B)||MQ4.isArrayBuffer(B)){let D=new Q;return D.update(RQ4.toUint8Array(B)),yk.toHex(await D.digest())}return srA},"getPayloadHash"),xrA=cB(),OQ4=class{static{dY(this,"HeaderFormatter")}format(A){let B=[];for(let Z of Object.keys(A)){let G=xrA.fromUtf8(Z);B.push(Uint8Array.from([G.byteLength]),G,this.formatHeaderValue(A[Z]))}let Q=new Uint8Array(B.reduce((Z,G)=>Z+G.byteLength,0)),D=0;for(let Z of B)Q.set(Z,D),D+=Z.byteLength;return Q}formatHeaderValue(A){switch(A.type){case"boolean":return Uint8Array.from([A.value?0:1]);case"byte":return Uint8Array.from([2,A.value]);case"short":let B=new DataView(new ArrayBuffer(3));return B.setUint8(0,3),B.setInt16(1,A.value,!1),new Uint8Array(B.buffer);case"integer":let Q=new DataView(new ArrayBuffer(5));return Q.setUint8(0,4),Q.setInt32(1,A.value,!1),new Uint8Array(Q.buffer);case"long":let D=new Uint8Array(9);return D[0]=5,D.set(A.value.bytes,1),D;case"binary":let Z=new DataView(new ArrayBuffer(3+A.value.byteLength));Z.setUint8(0,6),Z.setUint16(1,A.value.byteLength,!1);let G=new Uint8Array(Z.buffer);return G.set(A.value,3),G;case"string":let F=xrA.fromUtf8(A.value),I=new DataView(new ArrayBuffer(3+F.byteLength));I.setUint8(0,7),I.setUint16(1,F.byteLength,!1);let Y=new Uint8Array(I.buffer);return Y.set(F,3),Y;case"timestamp":let W=new Uint8Array(9);return W[0]=8,W.set(PQ4.fromNumber(A.value.valueOf()).bytes,1),W;case"uuid":if(!TQ4.test(A.value))throw new Error(`Invalid UUID received: ${A.value}`);let J=new Uint8Array(17);return J[0]=9,J.set(yk.fromHex(A.value.replace(/\-/g,"")),1),J}}},TQ4=/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/,PQ4=class A{constructor(B){if(this.bytes=B,B.byteLength!==8)throw new Error("Int64 buffers must be exactly 8 bytes")}static{dY(this,"Int64")}static fromNumber(B){if(B>9223372036854776000||B<-9223372036854776000)throw new Error(`${B} is too large (or, if negative, too small) to represent as an Int64`);let Q=new Uint8Array(8);for(let D=7,Z=Math.abs(Math.round(B));D>-1&&Z>0;D--,Z/=256)Q[D]=Z;if(B<0)jQ0(Q);return new A(Q)}valueOf(){let B=this.bytes.slice(0),Q=B[0]&128;if(Q)jQ0(B);return parseInt(yk.toHex(B),16)*(Q?-1:1)}toString(){return String(this.valueOf())}};function jQ0(A){for(let B=0;B<8;B++)A[B]^=255;for(let B=7;B>-1;B--)if(A[B]++,A[B]!==0)break}dY(jQ0,"negate");var erA=dY((A,B)=>{A=A.toLowerCase();for(let Q of Object.keys(B))if(A===Q.toLowerCase())return!0;return!1},"hasHeader"),AoA=$V(),BoA=dY((A,B={})=>{let{headers:Q,query:D={}}=AoA.HttpRequest.clone(A);for(let Z of Object.keys(Q)){let G=Z.toLowerCase();if(G.slice(0,6)==="x-amz-"&&!B.unhoistableHeaders?.has(G)||B.hoistableHeaders?.has(G))D[Z]=Q[Z],delete Q[Z]}return{...A,headers:Q,query:D}},"moveHeadersToQuery"),yQ0=dY((A)=>{A=AoA.HttpRequest.clone(A);for(let B of Object.keys(A.headers))if(drA.indexOf(B.toLowerCase())>-1)delete A.headers[B];return A},"prepareRequest"),vrA=J5(),SQ4=cB(),q$1=yrA(),QoA=dY(({query:A={}})=>{let B=[],Q={};for(let D of Object.keys(A)){if(D.toLowerCase()===crA)continue;let Z=q$1.escapeUri(D);B.push(Z);let G=A[D];if(typeof G==="string")Q[Z]=`${Z}=${q$1.escapeUri(G)}`;else if(Array.isArray(G))Q[Z]=G.slice(0).reduce((F,I)=>F.concat([`${Z}=${q$1.escapeUri(I)}`]),[]).sort().join("&")}return B.sort().map((D)=>Q[D]).filter((D)=>D).join("&")},"getCanonicalQuery"),jQ4=dY((A)=>yQ4(A).toISOString().replace(/\.\d{3}Z$/,"Z"),"iso8601"),yQ4=dY((A)=>{if(typeof A==="number")return new Date(A*1000);if(typeof A==="string"){if(Number(A))return new Date(Number(A)*1000);return new Date(A)}return A},"toDate"),DoA=class{static{dY(this,"SignatureV4Base")}constructor({applyChecksum:A,credentials:B,region:Q,service:D,sha256:Z,uriEscapePath:G=!0}){this.service=D,this.sha256=Z,this.uriEscapePath=G,this.applyChecksum=typeof A==="boolean"?A:!0,this.regionProvider=vrA.normalizeProvider(Q),this.credentialProvider=vrA.normalizeProvider(B)}createCanonicalRequest(A,B,Q){let D=Object.keys(B).sort();return`${A.method}
${this.getCanonicalPath(A)}
${QoA(A)}
${D.map((Z)=>`${Z}:${B[Z]}`).join(`
`)}

${D.join(";")}
${Q}`}async createStringToSign(A,B,Q,D){let Z=new this.sha256;Z.update(SQ4.toUint8Array(Q));let G=await Z.digest();return`${D}
${A}
${B}
${yk.toHex(G)}`}getCanonicalPath({path:A}){if(this.uriEscapePath){let B=[];for(let Z of A.split("/")){if(Z?.length===0)continue;if(Z===".")continue;if(Z==="..")B.pop();else B.push(Z)}let Q=`${A?.startsWith("/")?"/":""}${B.join("/")}${B.length>0&&A?.endsWith("/")?"/":""}`;return q$1.escapeUri(Q).replace(/%2F/g,"/")}return A}validateResolvedCredentials(A){if(typeof A!=="object"||typeof A.accessKeyId!=="string"||typeof A.secretAccessKey!=="string")throw new Error("Resolved credential object is not valid")}formatDate(A){let B=jQ4(A).replace(/[\-:]/g,"");return{longDate:B,shortDate:B.slice(0,8)}}getCanonicalHeaderList(A){return Object.keys(A).sort().join(";")}},kQ4=class extends DoA{constructor({applyChecksum:A,credentials:B,region:Q,service:D,sha256:Z,uriEscapePath:G=!0}){super({applyChecksum:A,credentials:B,region:Q,service:D,sha256:Z,uriEscapePath:G});this.headerFormatter=new OQ4}static{dY(this,"SignatureV4")}async presign(A,B={}){let{signingDate:Q=new Date,expiresIn:D=3600,unsignableHeaders:Z,unhoistableHeaders:G,signableHeaders:F,hoistableHeaders:I,signingRegion:Y,signingService:W}=B,J=await this.credentialProvider();this.validateResolvedCredentials(J);let X=Y??await this.regionProvider(),{longDate:V,shortDate:C}=this.formatDate(Q);if(D>orA)return Promise.reject("Signature version 4 presigned URLs must have an expiration date less than one week in the future");let K=w$1(C,X,W??this.service),H=BoA(yQ0(A),{unhoistableHeaders:G,hoistableHeaders:I});if(J.sessionToken)H.query[xQ0]=J.sessionToken;H.query[frA]=E$1,H.query[hrA]=`${J.accessKeyId}/${K}`,H.query[kQ0]=V,H.query[urA]=D.toString(10);let z=SQ0(H,Z,F);return H.query[grA]=this.getCanonicalHeaderList(z),H.query[_Q0]=await this.getSignature(V,K,this.getSigningKey(J,X,C,W),this.createCanonicalRequest(H,z,await $$1(A,this.sha256))),H}async sign(A,B){if(typeof A==="string")return this.signString(A,B);else if(A.headers&&A.payload)return this.signEvent(A,B);else if(A.message)return this.signMessage(A,B);else return this.signRequest(A,B)}async signEvent({headers:A,payload:B},{signingDate:Q=new Date,priorSignature:D,signingRegion:Z,signingService:G}){let F=Z??await this.regionProvider(),{shortDate:I,longDate:Y}=this.formatDate(Q),W=w$1(I,F,G??this.service),J=await $$1({headers:{},body:B},this.sha256),X=new this.sha256;X.update(A);let V=yk.toHex(await X.digest()),C=[arA,Y,W,D,V,J].join(`
`);return this.signString(C,{signingDate:Q,signingRegion:F,signingService:G})}async signMessage(A,{signingDate:B=new Date,signingRegion:Q,signingService:D}){return this.signEvent({headers:this.headerFormatter.format(A.message.headers),payload:A.message.body},{signingDate:B,signingRegion:Q,signingService:D,priorSignature:A.priorSignature}).then((G)=>{return{message:A.message,signature:G}})}async signString(A,{signingDate:B=new Date,signingRegion:Q,signingService:D}={}){let Z=await this.credentialProvider();this.validateResolvedCredentials(Z);let G=Q??await this.regionProvider(),{shortDate:F}=this.formatDate(B),I=new this.sha256(await this.getSigningKey(Z,G,F,D));return I.update(krA.toUint8Array(A)),yk.toHex(await I.digest())}async signRequest(A,{signingDate:B=new Date,signableHeaders:Q,unsignableHeaders:D,signingRegion:Z,signingService:G}={}){let F=await this.credentialProvider();this.validateResolvedCredentials(F);let I=Z??await this.regionProvider(),Y=yQ0(A),{longDate:W,shortDate:J}=this.formatDate(B),X=w$1(J,I,G??this.service);if(Y.headers[bQ0]=W,F.sessionToken)Y.headers[lrA]=F.sessionToken;let V=await $$1(Y,this.sha256);if(!erA(N$1,Y.headers)&&this.applyChecksum)Y.headers[N$1]=V;let C=SQ0(Y,D,Q),K=await this.getSignature(W,X,this.getSigningKey(F,I,J,G),this.createCanonicalRequest(Y,C,V));return Y.headers[vQ0]=`${E$1} Credential=${F.accessKeyId}/${X}, SignedHeaders=${this.getCanonicalHeaderList(C)}, Signature=${K}`,Y}async getSignature(A,B,Q,D){let Z=await this.createStringToSign(A,B,D,E$1),G=new this.sha256(await Q);return G.update(krA.toUint8Array(Z)),yk.toHex(await G.digest())}getSigningKey(A,B,Q,D){return trA(this.sha256,A,Q,B,D||this.service)}},_Q4={SignatureV4a:null}});
var H40=E((K40)=>{Object.defineProperty(K40,"__esModule",{value:!0});K40.fromHttp=void 0;var j84=ktA();Object.defineProperty(K40,"fromHttp",{enumerable:!0,get:function(){return j84.fromHttp}})});
var HA2=E((CA2)=>{Object.defineProperty(CA2,"__esModule",{value:!0});CA2.resolveHttpAuthRuntimeConfig=CA2.getHttpAuthExtensionConfiguration=void 0;var yD4=(A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}};CA2.getHttpAuthExtensionConfiguration=yD4;var kD4=(A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}};CA2.resolveHttpAuthRuntimeConfig=kD4});
var IB2=E((GB2)=>{Object.defineProperty(GB2,"__esModule",{value:!0});GB2.getRuntimeConfig=void 0;var ZF4=Jg(),GF4=ZF4.__importDefault(XtA()),v60=CI(),FF4=j22(),BB2=x40(),QB2=i41(),Aq1=K4(),IF4=CB(),YF4=gG(),DB2=u4(),wg=JD(),ZB2=k3(),WF4=uG(),JF4=sZ(),XF4=AB2(),VF4=C6(),CF4=mG(),KF4=C6(),HF4=(A)=>{KF4.emitWarningIfUnsupportedVersion(process.version);let B=CF4.resolveDefaultsModeConfig(A),Q=()=>B().then(VF4.loadConfigsForDefaultMode),D=XF4.getRuntimeConfig(A);v60.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile,logger:D.logger,signingName:"bedrock"};return{...D,...A,runtime:"node",defaultsMode:B,authSchemePreference:A?.authSchemePreference??wg.loadConfig(v60.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS,Z),bodyLengthChecker:A?.bodyLengthChecker??WF4.calculateBodyLength,credentialDefaultProvider:A?.credentialDefaultProvider??FF4.defaultProvider,defaultUserAgentProvider:A?.defaultUserAgentProvider??QB2.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:GF4.default.version}),httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(G)=>G.getIdentityProvider("aws.auth#sigv4"),signer:new v60.AwsSdkSigV4Signer},{schemeId:"smithy.api#httpBearerAuth",identityProvider:(G)=>G.getIdentityProvider("smithy.api#httpBearerAuth")||(async(F)=>{try{return await BB2.fromEnvSigningName({signingName:"bedrock"})()}catch(I){return await BB2.nodeProvider(F)(F)}}),signer:new IF4.HttpBearerAuthSigner}],maxAttempts:A?.maxAttempts??wg.loadConfig(DB2.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??wg.loadConfig(Aq1.NODE_REGION_CONFIG_OPTIONS,{...Aq1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:ZB2.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??wg.loadConfig({...DB2.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||JF4.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??YF4.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??ZB2.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??wg.loadConfig(Aq1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??wg.loadConfig(Aq1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??wg.loadConfig(QB2.NODE_APP_ID_CONFIG_OPTIONS,Z)}};GB2.getRuntimeConfig=HF4});
var Jg=E((pV5,V$1)=>{var isA,nsA,asA,ssA,rsA,osA,tsA,esA,ArA,BrA,QrA,DrA,ZrA,J$1,OQ0,GrA,FrA,IrA,da,YrA,WrA,JrA,XrA,VrA,CrA,KrA,HrA,zrA,X$1,ErA,UrA,wrA;(function(A){var B=typeof global==="object"?global:typeof self==="object"?self:typeof this==="object"?this:{};if(typeof define==="function"&&define.amd)define("tslib",["exports"],function(D){A(Q(B,Q(D)))});else if(typeof V$1==="object"&&typeof pV5==="object")A(Q(B,Q(pV5)));else A(Q(B));function Q(D,Z){if(D!==B)if(typeof Object.create==="function")Object.defineProperty(D,"__esModule",{value:!0});else D.__esModule=!0;return function(G,F){return D[G]=Z?Z(G,F):F}}})(function(A){var B=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(G,F){G.__proto__=F}||function(G,F){for(var I in F)if(Object.prototype.hasOwnProperty.call(F,I))G[I]=F[I]};isA=function(G,F){if(typeof F!=="function"&&F!==null)throw new TypeError("Class extends value "+String(F)+" is not a constructor or null");B(G,F);function I(){this.constructor=G}G.prototype=F===null?Object.create(F):(I.prototype=F.prototype,new I)},nsA=Object.assign||function(G){for(var F,I=1,Y=arguments.length;I<Y;I++){F=arguments[I];for(var W in F)if(Object.prototype.hasOwnProperty.call(F,W))G[W]=F[W]}return G},asA=function(G,F){var I={};for(var Y in G)if(Object.prototype.hasOwnProperty.call(G,Y)&&F.indexOf(Y)<0)I[Y]=G[Y];if(G!=null&&typeof Object.getOwnPropertySymbols==="function"){for(var W=0,Y=Object.getOwnPropertySymbols(G);W<Y.length;W++)if(F.indexOf(Y[W])<0&&Object.prototype.propertyIsEnumerable.call(G,Y[W]))I[Y[W]]=G[Y[W]]}return I},ssA=function(G,F,I,Y){var W=arguments.length,J=W<3?F:Y===null?Y=Object.getOwnPropertyDescriptor(F,I):Y,X;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")J=Reflect.decorate(G,F,I,Y);else for(var V=G.length-1;V>=0;V--)if(X=G[V])J=(W<3?X(J):W>3?X(F,I,J):X(F,I))||J;return W>3&&J&&Object.defineProperty(F,I,J),J},rsA=function(G,F){return function(I,Y){F(I,Y,G)}},osA=function(G,F,I,Y,W,J){function X(T){if(T!==void 0&&typeof T!=="function")throw new TypeError("Function expected");return T}var V=Y.kind,C=V==="getter"?"get":V==="setter"?"set":"value",K=!F&&G?Y.static?G:G.prototype:null,H=F||(K?Object.getOwnPropertyDescriptor(K,Y.name):{}),z,$=!1;for(var L=I.length-1;L>=0;L--){var N={};for(var O in Y)N[O]=O==="access"?{}:Y[O];for(var O in Y.access)N.access[O]=Y.access[O];N.addInitializer=function(T){if($)throw new TypeError("Cannot add initializers after decoration has completed");J.push(X(T||null))};var R=I[L](V==="accessor"?{get:H.get,set:H.set}:H[C],N);if(V==="accessor"){if(R===void 0)continue;if(R===null||typeof R!=="object")throw new TypeError("Object expected");if(z=X(R.get))H.get=z;if(z=X(R.set))H.set=z;if(z=X(R.init))W.unshift(z)}else if(z=X(R))if(V==="field")W.unshift(z);else H[C]=z}if(K)Object.defineProperty(K,Y.name,H);$=!0},tsA=function(G,F,I){var Y=arguments.length>2;for(var W=0;W<F.length;W++)I=Y?F[W].call(G,I):F[W].call(G);return Y?I:void 0},esA=function(G){return typeof G==="symbol"?G:"".concat(G)},ArA=function(G,F,I){if(typeof F==="symbol")F=F.description?"[".concat(F.description,"]"):"";return Object.defineProperty(G,"name",{configurable:!0,value:I?"".concat(I," ",F):F})},BrA=function(G,F){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(G,F)},QrA=function(G,F,I,Y){function W(J){return J instanceof I?J:new I(function(X){X(J)})}return new(I||(I=Promise))(function(J,X){function V(H){try{K(Y.next(H))}catch(z){X(z)}}function C(H){try{K(Y.throw(H))}catch(z){X(z)}}function K(H){H.done?J(H.value):W(H.value).then(V,C)}K((Y=Y.apply(G,F||[])).next())})},DrA=function(G,F){var I={label:0,sent:function(){if(J[0]&1)throw J[1];return J[1]},trys:[],ops:[]},Y,W,J,X=Object.create((typeof Iterator==="function"?Iterator:Object).prototype);return X.next=V(0),X.throw=V(1),X.return=V(2),typeof Symbol==="function"&&(X[Symbol.iterator]=function(){return this}),X;function V(K){return function(H){return C([K,H])}}function C(K){if(Y)throw new TypeError("Generator is already executing.");while(X&&(X=0,K[0]&&(I=0)),I)try{if(Y=1,W&&(J=K[0]&2?W.return:K[0]?W.throw||((J=W.return)&&J.call(W),0):W.next)&&!(J=J.call(W,K[1])).done)return J;if(W=0,J)K=[K[0]&2,J.value];switch(K[0]){case 0:case 1:J=K;break;case 4:return I.label++,{value:K[1],done:!1};case 5:I.label++,W=K[1],K=[0];continue;case 7:K=I.ops.pop(),I.trys.pop();continue;default:if((J=I.trys,!(J=J.length>0&&J[J.length-1]))&&(K[0]===6||K[0]===2)){I=0;continue}if(K[0]===3&&(!J||K[1]>J[0]&&K[1]<J[3])){I.label=K[1];break}if(K[0]===6&&I.label<J[1]){I.label=J[1],J=K;break}if(J&&I.label<J[2]){I.label=J[2],I.ops.push(K);break}if(J[2])I.ops.pop();I.trys.pop();continue}K=F.call(G,I)}catch(H){K=[6,H],W=0}finally{Y=J=0}if(K[0]&5)throw K[1];return{value:K[0]?K[1]:void 0,done:!0}}},ZrA=function(G,F){for(var I in G)if(I!=="default"&&!Object.prototype.hasOwnProperty.call(F,I))X$1(F,G,I)},X$1=Object.create?function(G,F,I,Y){if(Y===void 0)Y=I;var W=Object.getOwnPropertyDescriptor(F,I);if(!W||("get"in W?!F.__esModule:W.writable||W.configurable))W={enumerable:!0,get:function(){return F[I]}};Object.defineProperty(G,Y,W)}:function(G,F,I,Y){if(Y===void 0)Y=I;G[Y]=F[I]},J$1=function(G){var F=typeof Symbol==="function"&&Symbol.iterator,I=F&&G[F],Y=0;if(I)return I.call(G);if(G&&typeof G.length==="number")return{next:function(){if(G&&Y>=G.length)G=void 0;return{value:G&&G[Y++],done:!G}}};throw new TypeError(F?"Object is not iterable.":"Symbol.iterator is not defined.")},OQ0=function(G,F){var I=typeof Symbol==="function"&&G[Symbol.iterator];if(!I)return G;var Y=I.call(G),W,J=[],X;try{while((F===void 0||F-- >0)&&!(W=Y.next()).done)J.push(W.value)}catch(V){X={error:V}}finally{try{if(W&&!W.done&&(I=Y.return))I.call(Y)}finally{if(X)throw X.error}}return J},GrA=function(){for(var G=[],F=0;F<arguments.length;F++)G=G.concat(OQ0(arguments[F]));return G},FrA=function(){for(var G=0,F=0,I=arguments.length;F<I;F++)G+=arguments[F].length;for(var Y=Array(G),W=0,F=0;F<I;F++)for(var J=arguments[F],X=0,V=J.length;X<V;X++,W++)Y[W]=J[X];return Y},IrA=function(G,F,I){if(I||arguments.length===2){for(var Y=0,W=F.length,J;Y<W;Y++)if(J||!(Y in F)){if(!J)J=Array.prototype.slice.call(F,0,Y);J[Y]=F[Y]}}return G.concat(J||Array.prototype.slice.call(F))},da=function(G){return this instanceof da?(this.v=G,this):new da(G)},YrA=function(G,F,I){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var Y=I.apply(G,F||[]),W,J=[];return W=Object.create((typeof AsyncIterator==="function"?AsyncIterator:Object).prototype),V("next"),V("throw"),V("return",X),W[Symbol.asyncIterator]=function(){return this},W;function X(L){return function(N){return Promise.resolve(N).then(L,z)}}function V(L,N){if(Y[L]){if(W[L]=function(O){return new Promise(function(R,T){J.push([L,O,R,T])>1||C(L,O)})},N)W[L]=N(W[L])}}function C(L,N){try{K(Y[L](N))}catch(O){$(J[0][3],O)}}function K(L){L.value instanceof da?Promise.resolve(L.value.v).then(H,z):$(J[0][2],L)}function H(L){C("next",L)}function z(L){C("throw",L)}function $(L,N){if(L(N),J.shift(),J.length)C(J[0][0],J[0][1])}},WrA=function(G){var F,I;return F={},Y("next"),Y("throw",function(W){throw W}),Y("return"),F[Symbol.iterator]=function(){return this},F;function Y(W,J){F[W]=G[W]?function(X){return(I=!I)?{value:da(G[W](X)),done:!1}:J?J(X):X}:J}},JrA=function(G){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var F=G[Symbol.asyncIterator],I;return F?F.call(G):(G=typeof J$1==="function"?J$1(G):G[Symbol.iterator](),I={},Y("next"),Y("throw"),Y("return"),I[Symbol.asyncIterator]=function(){return this},I);function Y(J){I[J]=G[J]&&function(X){return new Promise(function(V,C){X=G[J](X),W(V,C,X.done,X.value)})}}function W(J,X,V,C){Promise.resolve(C).then(function(K){J({value:K,done:V})},X)}},XrA=function(G,F){if(Object.defineProperty)Object.defineProperty(G,"raw",{value:F});else G.raw=F;return G};var Q=Object.create?function(G,F){Object.defineProperty(G,"default",{enumerable:!0,value:F})}:function(G,F){G.default=F},D=function(G){return D=Object.getOwnPropertyNames||function(F){var I=[];for(var Y in F)if(Object.prototype.hasOwnProperty.call(F,Y))I[I.length]=Y;return I},D(G)};VrA=function(G){if(G&&G.__esModule)return G;var F={};if(G!=null){for(var I=D(G),Y=0;Y<I.length;Y++)if(I[Y]!=="default")X$1(F,G,I[Y])}return Q(F,G),F},CrA=function(G){return G&&G.__esModule?G:{default:G}},KrA=function(G,F,I,Y){if(I==="a"&&!Y)throw new TypeError("Private accessor was defined without a getter");if(typeof F==="function"?G!==F||!Y:!F.has(G))throw new TypeError("Cannot read private member from an object whose class did not declare it");return I==="m"?Y:I==="a"?Y.call(G):Y?Y.value:F.get(G)},HrA=function(G,F,I,Y,W){if(Y==="m")throw new TypeError("Private method is not writable");if(Y==="a"&&!W)throw new TypeError("Private accessor was defined without a setter");if(typeof F==="function"?G!==F||!W:!F.has(G))throw new TypeError("Cannot write private member to an object whose class did not declare it");return Y==="a"?W.call(G,I):W?W.value=I:F.set(G,I),I},zrA=function(G,F){if(F===null||typeof F!=="object"&&typeof F!=="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof G==="function"?F===G:G.has(F)},ErA=function(G,F,I){if(F!==null&&F!==void 0){if(typeof F!=="object"&&typeof F!=="function")throw new TypeError("Object expected.");var Y,W;if(I){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");Y=F[Symbol.asyncDispose]}if(Y===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");if(Y=F[Symbol.dispose],I)W=Y}if(typeof Y!=="function")throw new TypeError("Object not disposable.");if(W)Y=function(){try{W.call(this)}catch(J){return Promise.reject(J)}};G.stack.push({value:F,dispose:Y,async:I})}else if(I)G.stack.push({async:!0});return F};var Z=typeof SuppressedError==="function"?SuppressedError:function(G,F,I){var Y=new Error(I);return Y.name="SuppressedError",Y.error=G,Y.suppressed=F,Y};UrA=function(G){function F(J){G.error=G.hasError?new Z(J,G.error,"An error was suppressed during disposal."):J,G.hasError=!0}var I,Y=0;function W(){while(I=G.stack.pop())try{if(!I.async&&Y===1)return Y=0,G.stack.push(I),Promise.resolve().then(W);if(I.dispose){var J=I.dispose.call(I.value);if(I.async)return Y|=2,Promise.resolve(J).then(W,function(X){return F(X),W()})}else Y|=1}catch(X){F(X)}if(Y===1)return G.hasError?Promise.reject(G.error):Promise.resolve();if(G.hasError)throw G.error}return W()},wrA=function(G,F){if(typeof G==="string"&&/^\.\.?\//.test(G))return G.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,function(I,Y,W,J,X){return Y?F?".jsx":".js":W&&(!J||!X)?I:W+J+"."+X.toLowerCase()+"js"});return G},A("__extends",isA),A("__assign",nsA),A("__rest",asA),A("__decorate",ssA),A("__param",rsA),A("__esDecorate",osA),A("__runInitializers",tsA),A("__propKey",esA),A("__setFunctionName",ArA),A("__metadata",BrA),A("__awaiter",QrA),A("__generator",DrA),A("__exportStar",ZrA),A("__createBinding",X$1),A("__values",J$1),A("__read",OQ0),A("__spread",GrA),A("__spreadArrays",FrA),A("__spreadArray",IrA),A("__await",da),A("__asyncGenerator",YrA),A("__asyncDelegator",WrA),A("__asyncValues",JrA),A("__makeTemplateObject",XrA),A("__importStar",VrA),A("__importDefault",CrA),A("__classPrivateFieldGet",KrA),A("__classPrivateFieldSet",HrA),A("__classPrivateFieldIn",zrA),A("__addDisposableResource",ErA),A("__disposeResources",UrA),A("__rewriteRelativeImportExtension",wrA)})});
var LQ0=E((bV5,EsA)=>{var{defineProperty:B$1,getOwnPropertyDescriptor:gB4,getOwnPropertyNames:uB4}=Object,mB4=Object.prototype.hasOwnProperty,Q$1=(A,B)=>B$1(A,"name",{value:B,configurable:!0}),dB4=(A,B)=>{for(var Q in B)B$1(A,Q,{get:B[Q],enumerable:!0})},cB4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of uB4(B))if(!mB4.call(A,Z)&&Z!==Q)B$1(A,Z,{get:()=>B[Z],enumerable:!(D=gB4(B,Z))||D.enumerable})}return A},lB4=(A)=>cB4(B$1({},"__esModule",{value:!0}),A),WsA={};dB4(WsA,{AlgorithmId:()=>CsA,EndpointURLScheme:()=>VsA,FieldPosition:()=>KsA,HttpApiKeyAuthLocation:()=>XsA,HttpAuthLocation:()=>JsA,IniSectionType:()=>HsA,RequestHandlerProtocol:()=>zsA,SMITHY_CONTEXT_KEY:()=>sB4,getDefaultClientConfiguration:()=>nB4,resolveDefaultRuntimeConfig:()=>aB4});EsA.exports=lB4(WsA);var JsA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(JsA||{}),XsA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(XsA||{}),VsA=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(VsA||{}),CsA=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(CsA||{}),pB4=Q$1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),iB4=Q$1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),nB4=Q$1((A)=>{return pB4(A)},"getDefaultClientConfiguration"),aB4=Q$1((A)=>{return iB4(A)},"resolveDefaultRuntimeConfig"),KsA=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(KsA||{}),sB4="__smithy_context",HsA=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(HsA||{}),zsA=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(zsA||{})});
var MoA=E((NoA)=>{Object.defineProperty(NoA,"__esModule",{value:!0});NoA.toBase64=void 0;var B44=YD(),Q44=cB(),D44=(A)=>{let B;if(typeof A==="string")B=Q44.fromUtf8(A);else B=A;if(typeof B!=="object"||typeof B.byteOffset!=="number"||typeof B.byteLength!=="number")throw new Error("@smithy/util-base64: toBase64 encoder function only accepts string | Uint8Array.");return B44.fromArrayBuffer(B.buffer,B.byteOffset,B.byteLength).toString("base64")};NoA.toBase64=D44});
var MtA=E((LtA)=>{Object.defineProperty(LtA,"__esModule",{value:!0});LtA.createGetRequest=C84;LtA.getCredentials=K84;var C40=Q9(),J84=$V(),X84=C6(),V84=ry();function C84(A){return new J84.HttpRequest({protocol:A.protocol,hostname:A.hostname,port:Number(A.port),path:A.pathname,query:Array.from(A.searchParams.entries()).reduce((B,[Q,D])=>{return B[Q]=D,B},{}),fragment:A.hash})}async function K84(A,B){let D=await V84.sdkStreamMixin(A.body).transformToString();if(A.statusCode===200){let Z=JSON.parse(D);if(typeof Z.AccessKeyId!=="string"||typeof Z.SecretAccessKey!=="string"||typeof Z.Token!=="string"||typeof Z.Expiration!=="string")throw new C40.CredentialsProviderError("HTTP credential provider response not of the required format, an object matching: { AccessKeyId: string, SecretAccessKey: string, Token: string, Expiration: string(rfc3339) }",{logger:B});return{accessKeyId:Z.AccessKeyId,secretAccessKey:Z.SecretAccessKey,sessionToken:Z.Token,expiration:X84.parseRfc3339DateTime(Z.Expiration)}}if(A.statusCode>=400&&A.statusCode<500){let Z={};try{Z=JSON.parse(D)}catch(G){}throw Object.assign(new C40.CredentialsProviderError(`Server responded with status: ${A.statusCode}`,{logger:B}),{Code:Z.Code,Message:Z.Message})}throw new C40.CredentialsProviderError(`Server responded with status: ${A.statusCode}`,{logger:B})}});
var N12=E(($12)=>{Object.defineProperty($12,"__esModule",{value:!0});$12.defaultEndpointResolver=void 0;var w34=ma(),S40=S7(),$34=w12(),q34=new S40.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),N34=(A,B={})=>{return q34.get(A,()=>S40.resolveEndpoint($34.ruleSet,{endpointParams:A,logger:B.logger}))};$12.defaultEndpointResolver=N34;S40.customEndpointFunctions.aws=w34.awsEndpointFunctions});
var N22=E((SK5,q22)=>{var{create:EG4,defineProperty:Z61,getOwnPropertyDescriptor:UG4,getOwnPropertyNames:wG4,getPrototypeOf:$G4}=Object,qG4=Object.prototype.hasOwnProperty,iG=(A,B)=>Z61(A,"name",{value:B,configurable:!0}),NG4=(A,B)=>{for(var Q in B)Z61(A,Q,{get:B[Q],enumerable:!0})},U22=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of wG4(B))if(!qG4.call(A,Z)&&Z!==Q)Z61(A,Z,{get:()=>B[Z],enumerable:!(D=UG4(B,Z))||D.enumerable})}return A},fk=(A,B,Q)=>(Q=A!=null?EG4($G4(A)):{},U22(B||!A||!A.__esModule?Z61(Q,"default",{value:A,enumerable:!0}):Q,A)),LG4=(A)=>U22(Z61({},"__esModule",{value:!0}),A),w22={};NG4(w22,{fromIni:()=>vG4});q22.exports=LG4(w22);var y60=D3(),hk=_w(),D61=Q9(),MG4=iG((A,B,Q)=>{let D={EcsContainer:iG(async(Z)=>{let{fromHttp:G}=await Promise.resolve().then(()=>fk(H40())),{fromContainerMetadata:F}=await Promise.resolve().then(()=>fk(TF()));return Q?.debug("@aws-sdk/credential-provider-ini - credential_source is EcsContainer"),async()=>D61.chain(G(Z??{}),F(Z))().then(j60)},"EcsContainer"),Ec2InstanceMetadata:iG(async(Z)=>{Q?.debug("@aws-sdk/credential-provider-ini - credential_source is Ec2InstanceMetadata");let{fromInstanceMetadata:G}=await Promise.resolve().then(()=>fk(TF()));return async()=>G(Z)().then(j60)},"Ec2InstanceMetadata"),Environment:iG(async(Z)=>{Q?.debug("@aws-sdk/credential-provider-ini - credential_source is Environment");let{fromEnv:G}=await Promise.resolve().then(()=>fk(V40()));return async()=>G(Z)().then(j60)},"Environment")};if(A in D)return D[A];else throw new D61.CredentialsProviderError(`Unsupported credential source in profile ${B}. Got ${A}, expected EcsContainer or Ec2InstanceMetadata or Environment.`,{logger:Q})},"resolveCredentialSource"),j60=iG((A)=>hk.setCredentialFeature(A,"CREDENTIALS_PROFILE_NAMED_PROVIDER","p"),"setNamedProvider"),RG4=iG((A,{profile:B="default",logger:Q}={})=>{return Boolean(A)&&typeof A==="object"&&typeof A.role_arn==="string"&&["undefined","string"].indexOf(typeof A.role_session_name)>-1&&["undefined","string"].indexOf(typeof A.external_id)>-1&&["undefined","string"].indexOf(typeof A.mfa_serial)>-1&&(OG4(A,{profile:B,logger:Q})||TG4(A,{profile:B,logger:Q}))},"isAssumeRoleProfile"),OG4=iG((A,{profile:B,logger:Q})=>{let D=typeof A.source_profile==="string"&&typeof A.credential_source==="undefined";if(D)Q?.debug?.(`    ${B} isAssumeRoleWithSourceProfile source_profile=${A.source_profile}`);return D},"isAssumeRoleWithSourceProfile"),TG4=iG((A,{profile:B,logger:Q})=>{let D=typeof A.credential_source==="string"&&typeof A.source_profile==="undefined";if(D)Q?.debug?.(`    ${B} isCredentialSourceProfile credential_source=${A.credential_source}`);return D},"isCredentialSourceProfile"),PG4=iG(async(A,B,Q,D={})=>{Q.logger?.debug("@aws-sdk/credential-provider-ini - resolveAssumeRoleCredentials (STS)");let Z=B[A],{source_profile:G,region:F}=Z;if(!Q.roleAssumer){let{getDefaultRoleAssumer:Y}=await Promise.resolve().then(()=>fk(N60()));Q.roleAssumer=Y({...Q.clientConfig,credentialProviderLogger:Q.logger,parentClientConfig:{...Q?.parentClientConfig,region:F??Q?.parentClientConfig?.region}},Q.clientPlugins)}if(G&&G in D)throw new D61.CredentialsProviderError(`Detected a cycle attempting to resolve credentials for profile ${y60.getProfileName(Q)}. Profiles visited: `+Object.keys(D).join(", "),{logger:Q.logger});Q.logger?.debug(`@aws-sdk/credential-provider-ini - finding credential resolver using ${G?`source_profile=[${G}]`:`profile=[${A}]`}`);let I=G?$22(G,B,Q,{...D,[G]:!0},H22(B[G]??{})):(await MG4(Z.credential_source,A,Q.logger)(Q))();if(H22(Z))return I.then((Y)=>hk.setCredentialFeature(Y,"CREDENTIALS_PROFILE_SOURCE_PROFILE","o"));else{let Y={RoleArn:Z.role_arn,RoleSessionName:Z.role_session_name||`aws-sdk-js-${Date.now()}`,ExternalId:Z.external_id,DurationSeconds:parseInt(Z.duration_seconds||"3600",10)},{mfa_serial:W}=Z;if(W){if(!Q.mfaCodeProvider)throw new D61.CredentialsProviderError(`Profile ${A} requires multi-factor authentication, but no MFA code callback was provided.`,{logger:Q.logger,tryNextLink:!1});Y.SerialNumber=W,Y.TokenCode=await Q.mfaCodeProvider(W)}let J=await I;return Q.roleAssumer(J,Y).then((X)=>hk.setCredentialFeature(X,"CREDENTIALS_PROFILE_SOURCE_PROFILE","o"))}},"resolveAssumeRoleCredentials"),H22=iG((A)=>{return!A.role_arn&&!!A.credential_source},"isCredentialSourceWithoutRoleArn"),SG4=iG((A)=>Boolean(A)&&typeof A==="object"&&typeof A.credential_process==="string","isProcessProfile"),jG4=iG(async(A,B)=>Promise.resolve().then(()=>fk(R60())).then(({fromProcess:Q})=>Q({...A,profile:B})().then((D)=>hk.setCredentialFeature(D,"CREDENTIALS_PROFILE_PROCESS","v"))),"resolveProcessCredentials"),yG4=iG(async(A,B,Q={})=>{let{fromSSO:D}=await Promise.resolve().then(()=>fk(b40()));return D({profile:A,logger:Q.logger,parentClientConfig:Q.parentClientConfig,clientConfig:Q.clientConfig})().then((Z)=>{if(B.sso_session)return hk.setCredentialFeature(Z,"CREDENTIALS_PROFILE_SSO","r");else return hk.setCredentialFeature(Z,"CREDENTIALS_PROFILE_SSO_LEGACY","t")})},"resolveSsoCredentials"),kG4=iG((A)=>A&&(typeof A.sso_start_url==="string"||typeof A.sso_account_id==="string"||typeof A.sso_session==="string"||typeof A.sso_region==="string"||typeof A.sso_role_name==="string"),"isSsoProfile"),z22=iG((A)=>Boolean(A)&&typeof A==="object"&&typeof A.aws_access_key_id==="string"&&typeof A.aws_secret_access_key==="string"&&["undefined","string"].indexOf(typeof A.aws_session_token)>-1&&["undefined","string"].indexOf(typeof A.aws_account_id)>-1,"isStaticCredsProfile"),E22=iG(async(A,B)=>{B?.logger?.debug("@aws-sdk/credential-provider-ini - resolveStaticCredentials");let Q={accessKeyId:A.aws_access_key_id,secretAccessKey:A.aws_secret_access_key,sessionToken:A.aws_session_token,...A.aws_credential_scope&&{credentialScope:A.aws_credential_scope},...A.aws_account_id&&{accountId:A.aws_account_id}};return hk.setCredentialFeature(Q,"CREDENTIALS_PROFILE","n")},"resolveStaticCredentials"),_G4=iG((A)=>Boolean(A)&&typeof A==="object"&&typeof A.web_identity_token_file==="string"&&typeof A.role_arn==="string"&&["undefined","string"].indexOf(typeof A.role_session_name)>-1,"isWebIdentityProfile"),xG4=iG(async(A,B)=>Promise.resolve().then(()=>fk(S60())).then(({fromTokenFile:Q})=>Q({webIdentityTokenFile:A.web_identity_token_file,roleArn:A.role_arn,roleSessionName:A.role_session_name,roleAssumerWithWebIdentity:B.roleAssumerWithWebIdentity,logger:B.logger,parentClientConfig:B.parentClientConfig})().then((D)=>hk.setCredentialFeature(D,"CREDENTIALS_PROFILE_STS_WEB_ID_TOKEN","q"))),"resolveWebIdentityCredentials"),$22=iG(async(A,B,Q,D={},Z=!1)=>{let G=B[A];if(Object.keys(D).length>0&&z22(G))return E22(G,Q);if(Z||RG4(G,{profile:A,logger:Q.logger}))return PG4(A,B,Q,D);if(z22(G))return E22(G,Q);if(_G4(G))return xG4(G,Q);if(SG4(G))return jG4(Q,A);if(kG4(G))return await yG4(A,G,Q);throw new D61.CredentialsProviderError(`Could not resolve credentials using profile: [${A}] in configuration/credentials file(s).`,{logger:Q.logger})},"resolveProfileData"),vG4=iG((A={})=>async({callerClientConfig:B}={})=>{let Q={...A,parentClientConfig:{...B,...A.parentClientConfig}};Q.logger?.debug("@aws-sdk/credential-provider-ini - fromIni");let D=await y60.parseKnownFiles(Q);return $22(y60.getProfileName({profile:A.profile??B?.profile}),D,Q)},"fromIni")});
var N60=E((qK5,q60)=>{var{defineProperty:r$1,getOwnPropertyDescriptor:cD4,getOwnPropertyNames:lD4}=Object,pD4=Object.prototype.hasOwnProperty,I9=(A,B)=>r$1(A,"name",{value:B,configurable:!0}),iD4=(A,B)=>{for(var Q in B)r$1(A,Q,{get:B[Q],enumerable:!0})},K60=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of lD4(B))if(!pD4.call(A,Z)&&Z!==Q)r$1(A,Z,{get:()=>B[Z],enumerable:!(D=cD4(B,Z))||D.enumerable})}return A},nD4=(A,B,Q)=>(K60(A,B,"default"),Q&&K60(Q,B,"default")),aD4=(A)=>K60(r$1({},"__esModule",{value:!0}),A),z60={};iD4(z60,{AssumeRoleCommand:()=>w60,AssumeRoleResponseFilterSensitiveLog:()=>kA2,AssumeRoleWithWebIdentityCommand:()=>$60,AssumeRoleWithWebIdentityRequestFilterSensitiveLog:()=>gA2,AssumeRoleWithWebIdentityResponseFilterSensitiveLog:()=>uA2,ClientInputEndpointParameters:()=>uZ4.ClientInputEndpointParameters,CredentialsFilterSensitiveLog:()=>E60,ExpiredTokenException:()=>_A2,IDPCommunicationErrorException:()=>mA2,IDPRejectedClaimException:()=>fA2,InvalidIdentityTokenException:()=>hA2,MalformedPolicyDocumentException:()=>xA2,PackedPolicyTooLargeException:()=>vA2,RegionDisabledException:()=>bA2,STS:()=>tA2,STSServiceException:()=>_T,decorateDefaultCredentialProvider:()=>cZ4,getDefaultRoleAssumer:()=>Z22,getDefaultRoleAssumerWithWebIdentity:()=>G22});q60.exports=aD4(z60);nD4(z60,B61(),q60.exports);var sD4=C6(),rD4=R6(),oD4=j3(),tD4=C6(),eD4=Q61(),yA2=C6(),AZ4=C6(),_T=class A extends AZ4.ServiceException{static{I9(this,"STSServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},E60=I9((A)=>({...A,...A.SecretAccessKey&&{SecretAccessKey:yA2.SENSITIVE_STRING}}),"CredentialsFilterSensitiveLog"),kA2=I9((A)=>({...A,...A.Credentials&&{Credentials:E60(A.Credentials)}}),"AssumeRoleResponseFilterSensitiveLog"),_A2=class A extends _T{static{I9(this,"ExpiredTokenException")}name="ExpiredTokenException";$fault="client";constructor(B){super({name:"ExpiredTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},xA2=class A extends _T{static{I9(this,"MalformedPolicyDocumentException")}name="MalformedPolicyDocumentException";$fault="client";constructor(B){super({name:"MalformedPolicyDocumentException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},vA2=class A extends _T{static{I9(this,"PackedPolicyTooLargeException")}name="PackedPolicyTooLargeException";$fault="client";constructor(B){super({name:"PackedPolicyTooLargeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},bA2=class A extends _T{static{I9(this,"RegionDisabledException")}name="RegionDisabledException";$fault="client";constructor(B){super({name:"RegionDisabledException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},fA2=class A extends _T{static{I9(this,"IDPRejectedClaimException")}name="IDPRejectedClaimException";$fault="client";constructor(B){super({name:"IDPRejectedClaimException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},hA2=class A extends _T{static{I9(this,"InvalidIdentityTokenException")}name="InvalidIdentityTokenException";$fault="client";constructor(B){super({name:"InvalidIdentityTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},gA2=I9((A)=>({...A,...A.WebIdentityToken&&{WebIdentityToken:yA2.SENSITIVE_STRING}}),"AssumeRoleWithWebIdentityRequestFilterSensitiveLog"),uA2=I9((A)=>({...A,...A.Credentials&&{Credentials:E60(A.Credentials)}}),"AssumeRoleWithWebIdentityResponseFilterSensitiveLog"),mA2=class A extends _T{static{I9(this,"IDPCommunicationErrorException")}name="IDPCommunicationErrorException";$fault="client";constructor(B){super({name:"IDPCommunicationErrorException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},U60=CI(),BZ4=$V(),K5=C6(),QZ4=I9(async(A,B)=>{let Q=nA2,D;return D=oA2({...CZ4(A,B),[sA2]:yZ4,[rA2]:aA2}),iA2(B,Q,"/",void 0,D)},"se_AssumeRoleCommand"),DZ4=I9(async(A,B)=>{let Q=nA2,D;return D=oA2({...KZ4(A,B),[sA2]:kZ4,[rA2]:aA2}),iA2(B,Q,"/",void 0,D)},"se_AssumeRoleWithWebIdentityCommand"),ZZ4=I9(async(A,B)=>{if(A.statusCode>=300)return dA2(A,B);let Q=await U60.parseXmlBody(A.body,B),D={};return D=qZ4(Q.AssumeRoleResult,B),{$metadata:xT(A),...D}},"de_AssumeRoleCommand"),GZ4=I9(async(A,B)=>{if(A.statusCode>=300)return dA2(A,B);let Q=await U60.parseXmlBody(A.body,B),D={};return D=NZ4(Q.AssumeRoleWithWebIdentityResult,B),{$metadata:xT(A),...D}},"de_AssumeRoleWithWebIdentityCommand"),dA2=I9(async(A,B)=>{let Q={...A,body:await U60.parseXmlErrorBody(A.body,B)},D=_Z4(A,Q.body);switch(D){case"ExpiredTokenException":case"com.amazonaws.sts#ExpiredTokenException":throw await FZ4(Q,B);case"MalformedPolicyDocument":case"com.amazonaws.sts#MalformedPolicyDocumentException":throw await JZ4(Q,B);case"PackedPolicyTooLarge":case"com.amazonaws.sts#PackedPolicyTooLargeException":throw await XZ4(Q,B);case"RegionDisabledException":case"com.amazonaws.sts#RegionDisabledException":throw await VZ4(Q,B);case"IDPCommunicationError":case"com.amazonaws.sts#IDPCommunicationErrorException":throw await IZ4(Q,B);case"IDPRejectedClaim":case"com.amazonaws.sts#IDPRejectedClaimException":throw await YZ4(Q,B);case"InvalidIdentityToken":case"com.amazonaws.sts#InvalidIdentityTokenException":throw await WZ4(Q,B);default:let Z=Q.body;return jZ4({output:A,parsedBody:Z.Error,errorCode:D})}},"de_CommandError"),FZ4=I9(async(A,B)=>{let Q=A.body,D=LZ4(Q.Error,B),Z=new _A2({$metadata:xT(A),...D});return K5.decorateServiceException(Z,Q)},"de_ExpiredTokenExceptionRes"),IZ4=I9(async(A,B)=>{let Q=A.body,D=MZ4(Q.Error,B),Z=new mA2({$metadata:xT(A),...D});return K5.decorateServiceException(Z,Q)},"de_IDPCommunicationErrorExceptionRes"),YZ4=I9(async(A,B)=>{let Q=A.body,D=RZ4(Q.Error,B),Z=new fA2({$metadata:xT(A),...D});return K5.decorateServiceException(Z,Q)},"de_IDPRejectedClaimExceptionRes"),WZ4=I9(async(A,B)=>{let Q=A.body,D=OZ4(Q.Error,B),Z=new hA2({$metadata:xT(A),...D});return K5.decorateServiceException(Z,Q)},"de_InvalidIdentityTokenExceptionRes"),JZ4=I9(async(A,B)=>{let Q=A.body,D=TZ4(Q.Error,B),Z=new xA2({$metadata:xT(A),...D});return K5.decorateServiceException(Z,Q)},"de_MalformedPolicyDocumentExceptionRes"),XZ4=I9(async(A,B)=>{let Q=A.body,D=PZ4(Q.Error,B),Z=new vA2({$metadata:xT(A),...D});return K5.decorateServiceException(Z,Q)},"de_PackedPolicyTooLargeExceptionRes"),VZ4=I9(async(A,B)=>{let Q=A.body,D=SZ4(Q.Error,B),Z=new bA2({$metadata:xT(A),...D});return K5.decorateServiceException(Z,Q)},"de_RegionDisabledExceptionRes"),CZ4=I9((A,B)=>{let Q={};if(A[Ws]!=null)Q[Ws]=A[Ws];if(A[Js]!=null)Q[Js]=A[Js];if(A[Is]!=null){let D=cA2(A[Is],B);if(A[Is]?.length===0)Q.PolicyArns=[];Object.entries(D).forEach(([Z,G])=>{let F=`PolicyArns.${Z}`;Q[F]=G})}if(A[Fs]!=null)Q[Fs]=A[Fs];if(A[Gs]!=null)Q[Gs]=A[Gs];if(A[Y60]!=null){let D=$Z4(A[Y60],B);if(A[Y60]?.length===0)Q.Tags=[];Object.entries(D).forEach(([Z,G])=>{let F=`Tags.${Z}`;Q[F]=G})}if(A[J60]!=null){let D=wZ4(A[J60],B);if(A[J60]?.length===0)Q.TransitiveTagKeys=[];Object.entries(D).forEach(([Z,G])=>{let F=`TransitiveTagKeys.${Z}`;Q[F]=G})}if(A[t40]!=null)Q[t40]=A[t40];if(A[F60]!=null)Q[F60]=A[F60];if(A[W60]!=null)Q[W60]=A[W60];if(A[kT]!=null)Q[kT]=A[kT];if(A[B60]!=null){let D=EZ4(A[B60],B);if(A[B60]?.length===0)Q.ProvidedContexts=[];Object.entries(D).forEach(([Z,G])=>{let F=`ProvidedContexts.${Z}`;Q[F]=G})}return Q},"se_AssumeRoleRequest"),KZ4=I9((A,B)=>{let Q={};if(A[Ws]!=null)Q[Ws]=A[Ws];if(A[Js]!=null)Q[Js]=A[Js];if(A[V60]!=null)Q[V60]=A[V60];if(A[Q60]!=null)Q[Q60]=A[Q60];if(A[Is]!=null){let D=cA2(A[Is],B);if(A[Is]?.length===0)Q.PolicyArns=[];Object.entries(D).forEach(([Z,G])=>{let F=`PolicyArns.${Z}`;Q[F]=G})}if(A[Fs]!=null)Q[Fs]=A[Fs];if(A[Gs]!=null)Q[Gs]=A[Gs];return Q},"se_AssumeRoleWithWebIdentityRequest"),cA2=I9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=HZ4(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_policyDescriptorListType"),HZ4=I9((A,B)=>{let Q={};if(A[C60]!=null)Q[C60]=A[C60];return Q},"se_PolicyDescriptorType"),zZ4=I9((A,B)=>{let Q={};if(A[A60]!=null)Q[A60]=A[A60];if(A[r40]!=null)Q[r40]=A[r40];return Q},"se_ProvidedContext"),EZ4=I9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=zZ4(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_ProvidedContextsListType"),UZ4=I9((A,B)=>{let Q={};if(A[e40]!=null)Q[e40]=A[e40];if(A[X60]!=null)Q[X60]=A[X60];return Q},"se_Tag"),wZ4=I9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;Q[`member.${D}`]=Z,D++}return Q},"se_tagKeyListType"),$Z4=I9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=UZ4(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_tagListType"),lA2=I9((A,B)=>{let Q={};if(A[n40]!=null)Q[n40]=K5.expectString(A[n40]);if(A[a40]!=null)Q[a40]=K5.expectString(A[a40]);return Q},"de_AssumedRoleUser"),qZ4=I9((A,B)=>{let Q={};if(A[Zs]!=null)Q[Zs]=pA2(A[Zs],B);if(A[Ds]!=null)Q[Ds]=lA2(A[Ds],B);if(A[Ys]!=null)Q[Ys]=K5.strictParseInt32(A[Ys]);if(A[kT]!=null)Q[kT]=K5.expectString(A[kT]);return Q},"de_AssumeRoleResponse"),NZ4=I9((A,B)=>{let Q={};if(A[Zs]!=null)Q[Zs]=pA2(A[Zs],B);if(A[G60]!=null)Q[G60]=K5.expectString(A[G60]);if(A[Ds]!=null)Q[Ds]=lA2(A[Ds],B);if(A[Ys]!=null)Q[Ys]=K5.strictParseInt32(A[Ys]);if(A[D60]!=null)Q[D60]=K5.expectString(A[D60]);if(A[s40]!=null)Q[s40]=K5.expectString(A[s40]);if(A[kT]!=null)Q[kT]=K5.expectString(A[kT]);return Q},"de_AssumeRoleWithWebIdentityResponse"),pA2=I9((A,B)=>{let Q={};if(A[i40]!=null)Q[i40]=K5.expectString(A[i40]);if(A[Z60]!=null)Q[Z60]=K5.expectString(A[Z60]);if(A[I60]!=null)Q[I60]=K5.expectString(A[I60]);if(A[o40]!=null)Q[o40]=K5.expectNonNull(K5.parseRfc3339DateTimeWithOffset(A[o40]));return Q},"de_Credentials"),LZ4=I9((A,B)=>{let Q={};if(A[DG]!=null)Q[DG]=K5.expectString(A[DG]);return Q},"de_ExpiredTokenException"),MZ4=I9((A,B)=>{let Q={};if(A[DG]!=null)Q[DG]=K5.expectString(A[DG]);return Q},"de_IDPCommunicationErrorException"),RZ4=I9((A,B)=>{let Q={};if(A[DG]!=null)Q[DG]=K5.expectString(A[DG]);return Q},"de_IDPRejectedClaimException"),OZ4=I9((A,B)=>{let Q={};if(A[DG]!=null)Q[DG]=K5.expectString(A[DG]);return Q},"de_InvalidIdentityTokenException"),TZ4=I9((A,B)=>{let Q={};if(A[DG]!=null)Q[DG]=K5.expectString(A[DG]);return Q},"de_MalformedPolicyDocumentException"),PZ4=I9((A,B)=>{let Q={};if(A[DG]!=null)Q[DG]=K5.expectString(A[DG]);return Q},"de_PackedPolicyTooLargeException"),SZ4=I9((A,B)=>{let Q={};if(A[DG]!=null)Q[DG]=K5.expectString(A[DG]);return Q},"de_RegionDisabledException"),xT=I9((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),jZ4=K5.withBaseException(_T),iA2=I9(async(A,B,Q,D,Z)=>{let{hostname:G,protocol:F="https",port:I,path:Y}=await A.endpoint(),W={protocol:F,hostname:G,port:I,method:"POST",path:Y.endsWith("/")?Y.slice(0,-1)+Q:Y+Q,headers:B};if(D!==void 0)W.hostname=D;if(Z!==void 0)W.body=Z;return new BZ4.HttpRequest(W)},"buildHttpRpcRequest"),nA2={"content-type":"application/x-www-form-urlencoded"},aA2="2011-06-15",sA2="Action",i40="AccessKeyId",yZ4="AssumeRole",n40="AssumedRoleId",Ds="AssumedRoleUser",kZ4="AssumeRoleWithWebIdentity",a40="Arn",s40="Audience",Zs="Credentials",r40="ContextAssertion",Gs="DurationSeconds",o40="Expiration",t40="ExternalId",e40="Key",Fs="Policy",Is="PolicyArns",A60="ProviderArn",B60="ProvidedContexts",Q60="ProviderId",Ys="PackedPolicySize",D60="Provider",Ws="RoleArn",Js="RoleSessionName",Z60="SecretAccessKey",G60="SubjectFromWebIdentityToken",kT="SourceIdentity",F60="SerialNumber",I60="SessionToken",Y60="Tags",W60="TokenCode",J60="TransitiveTagKeys",rA2="Version",X60="Value",V60="WebIdentityToken",C60="arn",DG="message",oA2=I9((A)=>Object.entries(A).map(([B,Q])=>K5.extendedEncodeURIComponent(B)+"="+K5.extendedEncodeURIComponent(Q)).join("&"),"buildFormUrlencodedString"),_Z4=I9((A,B)=>{if(B.Error?.Code!==void 0)return B.Error.Code;if(A.statusCode==404)return"NotFound"},"loadQueryErrorCode"),w60=class extends tD4.Command.classBuilder().ep(eD4.commonParams).m(function(A,B,Q,D){return[oD4.getSerdePlugin(Q,this.serialize,this.deserialize),rD4.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRole",{}).n("STSClient","AssumeRoleCommand").f(void 0,kA2).ser(QZ4).de(ZZ4).build(){static{I9(this,"AssumeRoleCommand")}},xZ4=R6(),vZ4=j3(),bZ4=C6(),fZ4=Q61(),$60=class extends bZ4.Command.classBuilder().ep(fZ4.commonParams).m(function(A,B,Q,D){return[vZ4.getSerdePlugin(Q,this.serialize,this.deserialize),xZ4.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRoleWithWebIdentity",{}).n("STSClient","AssumeRoleWithWebIdentityCommand").f(gA2,uA2).ser(DZ4).de(GZ4).build(){static{I9(this,"AssumeRoleWithWebIdentityCommand")}},hZ4=B61(),gZ4={AssumeRoleCommand:w60,AssumeRoleWithWebIdentityCommand:$60},tA2=class extends hZ4.STSClient{static{I9(this,"STS")}};sD4.createAggregatedClient(gZ4,tA2);var uZ4=Q61(),H60=_w(),jA2="us-east-1",eA2=I9((A)=>{if(typeof A?.Arn==="string"){let B=A.Arn.split(":");if(B.length>4&&B[4]!=="")return B[4]}return},"getAccountIdFromAssumedRoleUser"),A22=I9(async(A,B,Q)=>{let D=typeof A==="function"?await A():A,Z=typeof B==="function"?await B():B;return Q?.debug?.("@aws-sdk/client-sts::resolveRegion","accepting first of:",`${D} (provider)`,`${Z} (parent client)`,`${jA2} (STS default)`),D??Z??jA2},"resolveRegion"),mZ4=I9((A,B)=>{let Q,D;return async(Z,G)=>{if(D=Z,!Q){let{logger:J=A?.parentClientConfig?.logger,region:X,requestHandler:V=A?.parentClientConfig?.requestHandler,credentialProviderLogger:C}=A,K=await A22(X,A?.parentClientConfig?.region,C),H=!B22(V);Q=new B({profile:A?.parentClientConfig?.profile,credentialDefaultProvider:I9(()=>async()=>D,"credentialDefaultProvider"),region:K,requestHandler:H?V:void 0,logger:J})}let{Credentials:F,AssumedRoleUser:I}=await Q.send(new w60(G));if(!F||!F.AccessKeyId||!F.SecretAccessKey)throw new Error(`Invalid response from STS.assumeRole call with role ${G.RoleArn}`);let Y=eA2(I),W={accessKeyId:F.AccessKeyId,secretAccessKey:F.SecretAccessKey,sessionToken:F.SessionToken,expiration:F.Expiration,...F.CredentialScope&&{credentialScope:F.CredentialScope},...Y&&{accountId:Y}};return H60.setCredentialFeature(W,"CREDENTIALS_STS_ASSUME_ROLE","i"),W}},"getDefaultRoleAssumer"),dZ4=I9((A,B)=>{let Q;return async(D)=>{if(!Q){let{logger:Y=A?.parentClientConfig?.logger,region:W,requestHandler:J=A?.parentClientConfig?.requestHandler,credentialProviderLogger:X}=A,V=await A22(W,A?.parentClientConfig?.region,X),C=!B22(J);Q=new B({profile:A?.parentClientConfig?.profile,region:V,requestHandler:C?J:void 0,logger:Y})}let{Credentials:Z,AssumedRoleUser:G}=await Q.send(new $60(D));if(!Z||!Z.AccessKeyId||!Z.SecretAccessKey)throw new Error(`Invalid response from STS.assumeRoleWithWebIdentity call with role ${D.RoleArn}`);let F=eA2(G),I={accessKeyId:Z.AccessKeyId,secretAccessKey:Z.SecretAccessKey,sessionToken:Z.SessionToken,expiration:Z.Expiration,...Z.CredentialScope&&{credentialScope:Z.CredentialScope},...F&&{accountId:F}};if(F)H60.setCredentialFeature(I,"RESOLVED_ACCOUNT_ID","T");return H60.setCredentialFeature(I,"CREDENTIALS_STS_ASSUME_ROLE_WEB_ID","k"),I}},"getDefaultRoleAssumerWithWebIdentity"),B22=I9((A)=>{return A?.metadata?.handlerProtocol==="h2"},"isH2"),Q22=B61(),D22=I9((A,B)=>{if(!B)return A;else return class Q extends A{static{I9(this,"CustomizableSTSClient")}constructor(D){super(D);for(let Z of B)this.middlewareStack.use(Z)}}},"getCustomizableStsClientCtor"),Z22=I9((A={},B)=>mZ4(A,D22(Q22.STSClient,B)),"getDefaultRoleAssumer"),G22=I9((A={},B)=>dZ4(A,D22(Q22.STSClient,B)),"getDefaultRoleAssumerWithWebIdentity"),cZ4=I9((A)=>(B)=>A({roleAssumer:Z22(B),roleAssumerWithWebIdentity:G22(B),...B}),"decorateDefaultCredentialProvider")});
var NA2=E(($A2)=>{Object.defineProperty($A2,"__esModule",{value:!0});$A2.resolveRuntimeExtensions=void 0;var zA2=a41(),EA2=$V(),UA2=C6(),wA2=HA2(),xD4=(A,B)=>{let Q=Object.assign(zA2.getAwsRegionExtensionConfiguration(A),UA2.getDefaultExtensionConfiguration(A),EA2.getHttpHandlerExtensionConfiguration(A),wA2.getHttpAuthExtensionConfiguration(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,zA2.resolveAwsRegionExtensionConfiguration(Q),UA2.resolveDefaultRuntimeConfig(Q),EA2.resolveHttpHandlerRuntimeConfig(Q),wA2.resolveHttpAuthRuntimeConfig(Q))};$A2.resolveRuntimeExtensions=xD4});
var NtA=E(($tA)=>{Object.defineProperty($tA,"__esModule",{value:!0});$tA.checkUrl=void 0;var G84=Q9(),F84="*************",I84="*************3",Y84="[fd00:ec2::23]",W84=(A,B)=>{if(A.protocol==="https:")return;if(A.hostname===F84||A.hostname===I84||A.hostname===Y84)return;if(A.hostname.includes("[")){if(A.hostname==="[::1]"||A.hostname==="[0000:0000:0000:0000:0000:0000:0000:0001]")return}else{if(A.hostname==="localhost")return;let Q=A.hostname.split("."),D=(Z)=>{let G=parseInt(Z,10);return 0<=G&&G<=255};if(Q[0]==="127"&&D(Q[1])&&D(Q[2])&&D(Q[3])&&Q.length===4)return}throw new G84.CredentialsProviderError(`URL not accepted. It must either be HTTPS or match one of the following:
  - loopback CIDR *********/8 or [::1/128]
  - ECS container host *************
  - EKS container host *************3 or [fd00:ec2::23]`,{logger:B})};$tA.checkUrl=W84});
var O40=E((AK5,E34)=>{E34.exports={name:"@aws-sdk/nested-clients",version:"3.840.0",description:"Nested clients for AWS SDK packages.",main:"./dist-cjs/index.js",module:"./dist-es/index.js",types:"./dist-types/index.d.ts",scripts:{build:"yarn lint && concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'","build:cjs":"node ../../scripts/compilation/inline nested-clients","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4",clean:"rimraf ./dist-* && rimraf *.tsbuildinfo",lint:"node ../../scripts/validation/submodules-linter.js --pkg nested-clients",test:"yarn g:vitest run","test:watch":"yarn g:vitest watch"},engines:{node:">=18.0.0"},author:{name:"AWS SDK for JavaScript Team",url:"https://aws.amazon.com/javascript/"},license:"Apache-2.0",dependencies:{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.840.0","@aws-sdk/middleware-host-header":"3.840.0","@aws-sdk/middleware-logger":"3.840.0","@aws-sdk/middleware-recursion-detection":"3.840.0","@aws-sdk/middleware-user-agent":"3.840.0","@aws-sdk/region-config-resolver":"3.840.0","@aws-sdk/types":"3.840.0","@aws-sdk/util-endpoints":"3.840.0","@aws-sdk/util-user-agent-browser":"3.840.0","@aws-sdk/util-user-agent-node":"3.840.0","@smithy/config-resolver":"^4.1.4","@smithy/core":"^3.6.0","@smithy/fetch-http-handler":"^5.0.4","@smithy/hash-node":"^4.0.4","@smithy/invalid-dependency":"^4.0.4","@smithy/middleware-content-length":"^4.0.4","@smithy/middleware-endpoint":"^4.1.13","@smithy/middleware-retry":"^4.1.14","@smithy/middleware-serde":"^4.0.8","@smithy/middleware-stack":"^4.0.4","@smithy/node-config-provider":"^4.1.3","@smithy/node-http-handler":"^4.0.6","@smithy/protocol-http":"^5.1.2","@smithy/smithy-client":"^4.4.5","@smithy/types":"^4.3.1","@smithy/url-parser":"^4.0.4","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.21","@smithy/util-defaults-mode-node":"^4.0.21","@smithy/util-endpoints":"^3.0.6","@smithy/util-middleware":"^4.0.4","@smithy/util-retry":"^4.0.6","@smithy/util-utf8":"^4.0.0",tslib:"^2.6.2"},devDependencies:{concurrently:"7.0.0","downlevel-dts":"0.10.1",rimraf:"3.0.2",typescript:"~5.8.3"},typesVersions:{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},files:["./sso-oidc.d.ts","./sso-oidc.js","./sts.d.ts","./sts.js","dist-*/**"],browser:{"./dist-es/submodules/sso-oidc/runtimeConfig":"./dist-es/submodules/sso-oidc/runtimeConfig.browser","./dist-es/submodules/sts/runtimeConfig":"./dist-es/submodules/sts/runtimeConfig.browser"},"react-native":{},homepage:"https://github.com/aws/aws-sdk-js-v3/tree/main/packages/nested-clients",repository:{type:"git",url:"https://github.com/aws/aws-sdk-js-v3.git",directory:"packages/nested-clients"},exports:{"./sso-oidc":{types:"./dist-types/submodules/sso-oidc/index.d.ts",module:"./dist-es/submodules/sso-oidc/index.js",node:"./dist-cjs/submodules/sso-oidc/index.js",import:"./dist-es/submodules/sso-oidc/index.js",require:"./dist-cjs/submodules/sso-oidc/index.js"},"./sts":{types:"./dist-types/submodules/sts/index.d.ts",module:"./dist-es/submodules/sts/index.js",node:"./dist-cjs/submodules/sts/index.js",import:"./dist-es/submodules/sts/index.js",require:"./dist-cjs/submodules/sts/index.js"}}}});
var O60=E((VL)=>{var QG4=VL&&VL.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),DG4=VL&&VL.__setModuleDefault||(Object.create?function(A,B){Object.defineProperty(A,"default",{enumerable:!0,value:B})}:function(A,B){A.default=B}),ZG4=VL&&VL.__importStar||function(){var A=function(B){return A=Object.getOwnPropertyNames||function(Q){var D=[];for(var Z in Q)if(Object.prototype.hasOwnProperty.call(Q,Z))D[D.length]=Z;return D},A(B)};return function(B){if(B&&B.__esModule)return B;var Q={};if(B!=null){for(var D=A(B),Z=0;Z<D.length;Z++)if(D[Z]!=="default")QG4(Q,B,D[Z])}return DG4(Q,B),Q}}();Object.defineProperty(VL,"__esModule",{value:!0});VL.fromWebToken=void 0;var GG4=(A)=>async(B)=>{A.logger?.debug("@aws-sdk/credential-provider-web-identity - fromWebToken");let{roleArn:Q,roleSessionName:D,webIdentityToken:Z,providerId:G,policyArns:F,policy:I,durationSeconds:Y}=A,{roleAssumerWithWebIdentity:W}=A;if(!W){let{getDefaultRoleAssumerWithWebIdentity:J}=await Promise.resolve().then(()=>ZG4(N60()));W=J({...A.clientConfig,credentialProviderLogger:A.logger,parentClientConfig:{...B?.callerClientConfig,...A.parentClientConfig}},A.clientPlugins)}return W({RoleArn:Q,RoleSessionName:D??`aws-sdk-js-session-${Date.now()}`,WebIdentityToken:Z,ProviderId:G,PolicyArns:F,Policy:I,DurationSeconds:Y})};VL.fromWebToken=GG4});
var Q61=E((T02)=>{Object.defineProperty(T02,"__esModule",{value:!0});T02.commonParams=T02.resolveClientEndpointParameters=void 0;var FD4=(A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,useGlobalEndpoint:A.useGlobalEndpoint??!1,defaultSigningName:"sts"})};T02.resolveClientEndpointParameters=FD4;T02.commonParams={UseGlobalEndpoint:{type:"builtInParams",name:"useGlobalEndpoint"},UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}}});
var R40=E((B12)=>{Object.defineProperty(B12,"__esModule",{value:!0});B12.resolveHttpAuthSchemeConfig=B12.defaultSSOOIDCHttpAuthSchemeProvider=B12.defaultSSOOIDCHttpAuthSchemeParametersProvider=void 0;var W34=CI(),M40=J5(),J34=async(A,B,Q)=>{return{operation:M40.getSmithyContext(B).operation,region:await M40.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};B12.defaultSSOOIDCHttpAuthSchemeParametersProvider=J34;function X34(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"sso-oauth",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function V34(A){return{schemeId:"smithy.api#noAuth"}}var C34=(A)=>{let B=[];switch(A.operation){case"CreateToken":{B.push(V34(A));break}default:B.push(X34(A))}return B};B12.defaultSSOOIDCHttpAuthSchemeProvider=C34;var K34=(A)=>{let B=W34.resolveAwsSdkSigV4Config(A);return Object.assign(B,{authSchemePreference:M40.normalizeProvider(A.authSchemePreference??[])})};B12.resolveHttpAuthSchemeConfig=K34});
var R60=E((RK5,Y22)=>{var{defineProperty:o$1,getOwnPropertyDescriptor:lZ4,getOwnPropertyNames:pZ4}=Object,iZ4=Object.prototype.hasOwnProperty,M60=(A,B)=>o$1(A,"name",{value:B,configurable:!0}),nZ4=(A,B)=>{for(var Q in B)o$1(A,Q,{get:B[Q],enumerable:!0})},aZ4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of pZ4(B))if(!iZ4.call(A,Z)&&Z!==Q)o$1(A,Z,{get:()=>B[Z],enumerable:!(D=lZ4(B,Z))||D.enumerable})}return A},sZ4=(A)=>aZ4(o$1({},"__esModule",{value:!0}),A),I22={};nZ4(I22,{fromProcess:()=>BG4});Y22.exports=sZ4(I22);var F22=D3(),L60=Q9(),rZ4=J1("child_process"),oZ4=J1("util"),tZ4=_w(),eZ4=M60((A,B,Q)=>{if(B.Version!==1)throw Error(`Profile ${A} credential_process did not return Version 1.`);if(B.AccessKeyId===void 0||B.SecretAccessKey===void 0)throw Error(`Profile ${A} credential_process returned invalid credentials.`);if(B.Expiration){let G=new Date;if(new Date(B.Expiration)<G)throw Error(`Profile ${A} credential_process returned expired credentials.`)}let D=B.AccountId;if(!D&&Q?.[A]?.aws_account_id)D=Q[A].aws_account_id;let Z={accessKeyId:B.AccessKeyId,secretAccessKey:B.SecretAccessKey,...B.SessionToken&&{sessionToken:B.SessionToken},...B.Expiration&&{expiration:new Date(B.Expiration)},...B.CredentialScope&&{credentialScope:B.CredentialScope},...D&&{accountId:D}};return tZ4.setCredentialFeature(Z,"CREDENTIALS_PROCESS","w"),Z},"getValidatedProcessCredentials"),AG4=M60(async(A,B,Q)=>{let D=B[A];if(B[A]){let Z=D.credential_process;if(Z!==void 0){let G=oZ4.promisify(rZ4.exec);try{let{stdout:F}=await G(Z),I;try{I=JSON.parse(F.trim())}catch{throw Error(`Profile ${A} credential_process returned invalid JSON.`)}return eZ4(A,I,B)}catch(F){throw new L60.CredentialsProviderError(F.message,{logger:Q})}}else throw new L60.CredentialsProviderError(`Profile ${A} did not contain credential_process.`,{logger:Q})}else throw new L60.CredentialsProviderError(`Profile ${A} could not be found in shared credentials file.`,{logger:Q})},"resolveProcessCredentials"),BG4=M60((A={})=>async({callerClientConfig:B}={})=>{A.logger?.debug("@aws-sdk/credential-provider-process - fromProcess");let Q=await F22.parseKnownFiles(A);return AG4(F22.getProfileName({profile:A.profile??B?.profile}),Q,A.logger)},"fromProcess")});
var S60=E((PK5,t$1)=>{var{defineProperty:C22,getOwnPropertyDescriptor:CG4,getOwnPropertyNames:KG4}=Object,HG4=Object.prototype.hasOwnProperty,T60=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of KG4(B))if(!HG4.call(A,Z)&&Z!==Q)C22(A,Z,{get:()=>B[Z],enumerable:!(D=CG4(B,Z))||D.enumerable})}return A},K22=(A,B,Q)=>(T60(A,B,"default"),Q&&T60(Q,B,"default")),zG4=(A)=>T60(C22({},"__esModule",{value:!0}),A),P60={};t$1.exports=zG4(P60);K22(P60,V22(),t$1.exports);K22(P60,O60(),t$1.exports)});
var T12=E((R12)=>{Object.defineProperty(R12,"__esModule",{value:!0});R12.getRuntimeConfig=void 0;var L34=CI(),M34=CB(),R34=C6(),O34=JZ(),L12=kk(),M12=cB(),T34=R40(),P34=N12(),S34=(A)=>{return{apiVersion:"2019-06-10",base64Decoder:A?.base64Decoder??L12.fromBase64,base64Encoder:A?.base64Encoder??L12.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??P34.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??T34.defaultSSOOIDCHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new L34.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new M34.NoAuthSigner}],logger:A?.logger??new R34.NoOpLogger,serviceId:A?.serviceId??"SSO OIDC",urlParser:A?.urlParser??O34.parseUrl,utf8Decoder:A?.utf8Decoder??M12.fromUtf8,utf8Encoder:A?.utf8Encoder??M12.toUtf8}};R12.getRuntimeConfig=S34});
var TrA=E((nV5,OrA)=>{var{defineProperty:H$1,getOwnPropertyDescriptor:r94,getOwnPropertyNames:o94}=Object,t94=Object.prototype.hasOwnProperty,e94=(A,B)=>H$1(A,"name",{value:B,configurable:!0}),AQ4=(A,B)=>{for(var Q in B)H$1(A,Q,{get:B[Q],enumerable:!0})},BQ4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of o94(B))if(!t94.call(A,Z)&&Z!==Q)H$1(A,Z,{get:()=>B[Z],enumerable:!(D=r94(B,Z))||D.enumerable})}return A},QQ4=(A)=>BQ4(H$1({},"__esModule",{value:!0}),A),RrA={};AQ4(RrA,{isArrayBuffer:()=>DQ4});OrA.exports=QQ4(RrA);var DQ4=e94((A)=>typeof ArrayBuffer==="function"&&A instanceof ArrayBuffer||Object.prototype.toString.call(A)==="[object ArrayBuffer]","isArrayBuffer")});
var TtA=E((RtA)=>{Object.defineProperty(RtA,"__esModule",{value:!0});RtA.retryWrapper=void 0;var E84=(A,B,Q)=>{return async()=>{for(let D=0;D<B;++D)try{return await A()}catch(Z){await new Promise((G)=>setTimeout(G,Q))}return await A()}};RtA.retryWrapper=E84});
var V22=E((J22)=>{Object.defineProperty(J22,"__esModule",{value:!0});J22.fromTokenFile=void 0;var FG4=_w(),IG4=Q9(),YG4=J1("fs"),WG4=O60(),W22="AWS_WEB_IDENTITY_TOKEN_FILE",JG4="AWS_ROLE_ARN",XG4="AWS_ROLE_SESSION_NAME",VG4=(A={})=>async()=>{A.logger?.debug("@aws-sdk/credential-provider-web-identity - fromTokenFile");let B=A?.webIdentityTokenFile??process.env[W22],Q=A?.roleArn??process.env[JG4],D=A?.roleSessionName??process.env[XG4];if(!B||!Q)throw new IG4.CredentialsProviderError("Web identity configuration not specified",{logger:A.logger});let Z=await WG4.fromWebToken({...A,webIdentityToken:YG4.readFileSync(B,{encoding:"ascii"}),roleArn:Q,roleSessionName:D})();if(B===process.env[W22])FG4.setCredentialFeature(Z,"CREDENTIALS_ENV_VARS_STS_WEB_ID_TOKEN","h");return Z};J22.fromTokenFile=VG4});
var V40=E((kC5,wtA)=>{var{defineProperty:y$1,getOwnPropertyDescriptor:s64,getOwnPropertyNames:r64}=Object,o64=Object.prototype.hasOwnProperty,t64=(A,B)=>y$1(A,"name",{value:B,configurable:!0}),e64=(A,B)=>{for(var Q in B)y$1(A,Q,{get:B[Q],enumerable:!0})},A84=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of r64(B))if(!o64.call(A,Z)&&Z!==Q)y$1(A,Z,{get:()=>B[Z],enumerable:!(D=s64(B,Z))||D.enumerable})}return A},B84=(A)=>A84(y$1({},"__esModule",{value:!0}),A),VtA={};e64(VtA,{ENV_ACCOUNT_ID:()=>UtA,ENV_CREDENTIAL_SCOPE:()=>EtA,ENV_EXPIRATION:()=>ztA,ENV_KEY:()=>CtA,ENV_SECRET:()=>KtA,ENV_SESSION:()=>HtA,fromEnv:()=>Z84});wtA.exports=B84(VtA);var Q84=_w(),D84=Q9(),CtA="AWS_ACCESS_KEY_ID",KtA="AWS_SECRET_ACCESS_KEY",HtA="AWS_SESSION_TOKEN",ztA="AWS_CREDENTIAL_EXPIRATION",EtA="AWS_CREDENTIAL_SCOPE",UtA="AWS_ACCOUNT_ID",Z84=t64((A)=>async()=>{A?.logger?.debug("@aws-sdk/credential-provider-env - fromEnv");let B=process.env[CtA],Q=process.env[KtA],D=process.env[HtA],Z=process.env[ztA],G=process.env[EtA],F=process.env[UtA];if(B&&Q){let I={accessKeyId:B,secretAccessKey:Q,...D&&{sessionToken:D},...Z&&{expiration:new Date(Z)},...G&&{credentialScope:G},...F&&{accountId:F}};return Q84.setCredentialFeature(I,"CREDENTIALS_ENV_VARS","g"),I}throw new D84.CredentialsProviderError("Unable to find environment variable credentials.",{logger:A?.logger})},"fromEnv")});
var VA2=E((JA2)=>{Object.defineProperty(JA2,"__esModule",{value:!0});JA2.getRuntimeConfig=void 0;var $D4=Jg(),qD4=$D4.__importDefault(O40()),c40=CI(),IA2=i41(),s$1=K4(),ND4=CB(),LD4=gG(),YA2=u4(),Eg=JD(),WA2=k3(),MD4=uG(),RD4=sZ(),OD4=FA2(),TD4=C6(),PD4=mG(),SD4=C6(),jD4=(A)=>{SD4.emitWarningIfUnsupportedVersion(process.version);let B=PD4.resolveDefaultsModeConfig(A),Q=()=>B().then(TD4.loadConfigsForDefaultMode),D=OD4.getRuntimeConfig(A);c40.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile,logger:D.logger};return{...D,...A,runtime:"node",defaultsMode:B,authSchemePreference:A?.authSchemePreference??Eg.loadConfig(c40.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS,Z),bodyLengthChecker:A?.bodyLengthChecker??MD4.calculateBodyLength,defaultUserAgentProvider:A?.defaultUserAgentProvider??IA2.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:qD4.default.version}),httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(G)=>G.getIdentityProvider("aws.auth#sigv4")||(async(F)=>await A.credentialDefaultProvider(F?.__config||{})()),signer:new c40.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(G)=>G.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new ND4.NoAuthSigner}],maxAttempts:A?.maxAttempts??Eg.loadConfig(YA2.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??Eg.loadConfig(s$1.NODE_REGION_CONFIG_OPTIONS,{...s$1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:WA2.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??Eg.loadConfig({...YA2.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||RD4.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??LD4.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??WA2.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??Eg.loadConfig(s$1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??Eg.loadConfig(s$1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??Eg.loadConfig(IA2.NODE_APP_ID_CONFIG_OPTIONS,Z)}};JA2.getRuntimeConfig=jD4});
var X40=E((WtA)=>{Object.defineProperty(WtA,"__esModule",{value:!0});WtA.resolveHttpAuthSchemeConfig=WtA.defaultBedrockHttpAuthSchemeProvider=WtA.defaultBedrockHttpAuthSchemeParametersProvider=void 0;var u64=CI(),W40=CB(),J40=J5(),m64=async(A,B,Q)=>{return{operation:J40.getSmithyContext(B).operation,region:await J40.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};WtA.defaultBedrockHttpAuthSchemeParametersProvider=m64;function d64(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"bedrock",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function c64(A){return{schemeId:"smithy.api#httpBearerAuth",propertiesExtractor:({profile:B,filepath:Q,configFilepath:D,ignoreCache:Z},G)=>({identityProperties:{profile:B,filepath:Q,configFilepath:D,ignoreCache:Z}})}}var l64=(A)=>{let B=[];switch(A.operation){default:B.push(d64(A)),B.push(c64(A))}return B};WtA.defaultBedrockHttpAuthSchemeProvider=l64;var p64=(A)=>{let B=W40.memoizeIdentityProvider(A.token,W40.isIdentityExpired,W40.doesIdentityRequireRefresh),Q=u64.resolveAwsSdkSigV4Config(A);return Object.assign(Q,{authSchemePreference:J40.normalizeProvider(A.authSchemePreference??[]),token:B})};WtA.resolveHttpAuthSchemeConfig=p64});
var XtA=E((yC5,a64)=>{a64.exports={name:"@aws-sdk/client-bedrock",description:"AWS SDK for JavaScript Bedrock Client for Node.js, Browser and React Native",version:"3.840.0",scripts:{build:"concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'","build:cjs":"node ../../scripts/compilation/inline client-bedrock","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4",clean:"rimraf ./dist-* && rimraf *.tsbuildinfo","extract:docs":"api-extractor run --local","generate:client":"node ../../scripts/generate-clients/single-service --solo bedrock"},main:"./dist-cjs/index.js",types:"./dist-types/index.d.ts",module:"./dist-es/index.js",sideEffects:!1,dependencies:{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.840.0","@aws-sdk/credential-provider-node":"3.840.0","@aws-sdk/middleware-host-header":"3.840.0","@aws-sdk/middleware-logger":"3.840.0","@aws-sdk/middleware-recursion-detection":"3.840.0","@aws-sdk/middleware-user-agent":"3.840.0","@aws-sdk/region-config-resolver":"3.840.0","@aws-sdk/token-providers":"3.840.0","@aws-sdk/types":"3.840.0","@aws-sdk/util-endpoints":"3.840.0","@aws-sdk/util-user-agent-browser":"3.840.0","@aws-sdk/util-user-agent-node":"3.840.0","@smithy/config-resolver":"^4.1.4","@smithy/core":"^3.6.0","@smithy/fetch-http-handler":"^5.0.4","@smithy/hash-node":"^4.0.4","@smithy/invalid-dependency":"^4.0.4","@smithy/middleware-content-length":"^4.0.4","@smithy/middleware-endpoint":"^4.1.13","@smithy/middleware-retry":"^4.1.14","@smithy/middleware-serde":"^4.0.8","@smithy/middleware-stack":"^4.0.4","@smithy/node-config-provider":"^4.1.3","@smithy/node-http-handler":"^4.0.6","@smithy/protocol-http":"^5.1.2","@smithy/smithy-client":"^4.4.5","@smithy/types":"^4.3.1","@smithy/url-parser":"^4.0.4","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.21","@smithy/util-defaults-mode-node":"^4.0.21","@smithy/util-endpoints":"^3.0.6","@smithy/util-middleware":"^4.0.4","@smithy/util-retry":"^4.0.6","@smithy/util-utf8":"^4.0.0","@types/uuid":"^9.0.1",tslib:"^2.6.2",uuid:"^9.0.1"},devDependencies:{"@tsconfig/node18":"18.2.4","@types/node":"^18.19.69",concurrently:"7.0.0","downlevel-dts":"0.10.1",rimraf:"3.0.2",typescript:"~5.8.3"},engines:{node:">=18.0.0"},typesVersions:{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},files:["dist-*/**"],author:{name:"AWS SDK for JavaScript Team",url:"https://aws.amazon.com/javascript/"},license:"Apache-2.0",browser:{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.browser"},"react-native":{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.native"},homepage:"https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-bedrock",repository:{type:"git",url:"https://github.com/aws/aws-sdk-js-v3.git",directory:"clients/client-bedrock"}}});
var YeA=E((FeA)=>{Object.defineProperty(FeA,"__esModule",{value:!0});FeA.defaultEndpointResolver=void 0;var t84=ma(),q40=S7(),e84=GeA(),A54=new q40.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),B54=(A,B={})=>{return A54.get(A,()=>q40.resolveEndpoint(e84.ruleSet,{endpointParams:A,logger:B.logger}))};FeA.defaultEndpointResolver=B54;q40.customEndpointFunctions.aws=t84.awsEndpointFunctions});
var _w=E((iV5,MrA)=>{var{defineProperty:C$1,getOwnPropertyDescriptor:c94,getOwnPropertyNames:l94}=Object,p94=Object.prototype.hasOwnProperty,K$1=(A,B)=>C$1(A,"name",{value:B,configurable:!0}),i94=(A,B)=>{for(var Q in B)C$1(A,Q,{get:B[Q],enumerable:!0})},n94=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of l94(B))if(!p94.call(A,Z)&&Z!==Q)C$1(A,Z,{get:()=>B[Z],enumerable:!(D=c94(B,Z))||D.enumerable})}return A},a94=(A)=>n94(C$1({},"__esModule",{value:!0}),A),$rA={};i94($rA,{emitWarningIfUnsupportedVersion:()=>s94,setCredentialFeature:()=>qrA,setFeature:()=>NrA,setTokenFeature:()=>LrA,state:()=>TQ0});MrA.exports=a94($rA);var TQ0={warningEmitted:!1},s94=K$1((A)=>{if(A&&!TQ0.warningEmitted&&parseInt(A.substring(1,A.indexOf(".")))<18)TQ0.warningEmitted=!0,process.emitWarning(`NodeDeprecationWarning: The AWS SDK for JavaScript (v3) will
no longer support Node.js 16.x on January 6, 2025.

To continue receiving updates to AWS services, bug fixes, and security
updates please upgrade to a supported Node.js LTS version.

More information can be found at: https://a.co/74kJMmI`)},"emitWarningIfUnsupportedVersion");function qrA(A,B,Q){if(!A.$source)A.$source={};return A.$source[B]=Q,A}K$1(qrA,"setCredentialFeature");function NrA(A,B,Q){if(!A.__aws_sdk_context)A.__aws_sdk_context={features:{}};else if(!A.__aws_sdk_context.features)A.__aws_sdk_context.features={};A.__aws_sdk_context.features[B]=Q}K$1(NrA,"setFeature");function LrA(A,B,Q){if(!A.$source)A.$source={};return A.$source[B]=Q,A}K$1(LrA,"setTokenFeature")});
var a41=E((pC5,OeA)=>{var{defineProperty:b$1,getOwnPropertyDescriptor:w54,getOwnPropertyNames:$54}=Object,q54=Object.prototype.hasOwnProperty,JL=(A,B)=>b$1(A,"name",{value:B,configurable:!0}),N54=(A,B)=>{for(var Q in B)b$1(A,Q,{get:B[Q],enumerable:!0})},L54=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of $54(B))if(!q54.call(A,Z)&&Z!==Q)b$1(A,Z,{get:()=>B[Z],enumerable:!(D=w54(B,Z))||D.enumerable})}return A},M54=(A)=>L54(b$1({},"__esModule",{value:!0}),A),NeA={};N54(NeA,{NODE_REGION_CONFIG_FILE_OPTIONS:()=>P54,NODE_REGION_CONFIG_OPTIONS:()=>T54,REGION_ENV_NAME:()=>LeA,REGION_INI_NAME:()=>MeA,getAwsRegionExtensionConfiguration:()=>R54,resolveAwsRegionExtensionConfiguration:()=>O54,resolveRegionConfig:()=>S54});OeA.exports=M54(NeA);var R54=JL((A)=>{return{setRegion(B){A.region=B},region(){return A.region}}},"getAwsRegionExtensionConfiguration"),O54=JL((A)=>{return{region:A.region()}},"resolveAwsRegionExtensionConfiguration"),LeA="AWS_REGION",MeA="region",T54={environmentVariableSelector:JL((A)=>A[LeA],"environmentVariableSelector"),configFileSelector:JL((A)=>A[MeA],"configFileSelector"),default:JL(()=>{throw new Error("Region is missing")},"default")},P54={preferredFile:"credentials"},ReA=JL((A)=>typeof A==="string"&&(A.startsWith("fips-")||A.endsWith("-fips")),"isFipsRegion"),qeA=JL((A)=>ReA(A)?["fips-aws-global","aws-fips"].includes(A)?"us-east-1":A.replace(/fips-(dkr-|prod-)?|-fips/,""):A,"getRealRegion"),S54=JL((A)=>{let{region:B,useFipsEndpoint:Q}=A;if(!B)throw new Error("Region is missing");return Object.assign(A,{region:JL(async()=>{if(typeof B==="string")return qeA(B);let D=await B();return qeA(D)},"region"),useFipsEndpoint:JL(async()=>{let D=typeof B==="string"?B:await B();if(ReA(D))return!0;return typeof Q!=="function"?Promise.resolve(!!Q):Q()},"useFipsEndpoint")})},"resolveRegionConfig")});
var b40=E((JK5,L02)=>{var{defineProperty:n$1,getOwnPropertyDescriptor:d74,getOwnPropertyNames:E02}=Object,c74=Object.prototype.hasOwnProperty,a$1=(A,B)=>n$1(A,"name",{value:B,configurable:!0}),l74=(A,B)=>function Q(){return A&&(B=A[E02(A)[0]](A=0)),B},U02=(A,B)=>{for(var Q in B)n$1(A,Q,{get:B[Q],enumerable:!0})},p74=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of E02(B))if(!c74.call(A,Z)&&Z!==Q)n$1(A,Z,{get:()=>B[Z],enumerable:!(D=d74(B,Z))||D.enumerable})}return A},i74=(A)=>p74(n$1({},"__esModule",{value:!0}),A),w02={};U02(w02,{GetRoleCredentialsCommand:()=>v40.GetRoleCredentialsCommand,SSOClient:()=>v40.SSOClient});var v40,n74=l74({"src/loadSso.ts"(){v40=A12()}}),$02={};U02($02,{fromSSO:()=>s74,isSsoProfile:()=>q02,validateSsoProfile:()=>N02});L02.exports=i74($02);var q02=a$1((A)=>A&&(typeof A.sso_start_url==="string"||typeof A.sso_account_id==="string"||typeof A.sso_session==="string"||typeof A.sso_region==="string"||typeof A.sso_role_name==="string"),"isSsoProfile"),H02=_w(),a74=x40(),xw=Q9(),i$1=D3(),A61=!1,z02=a$1(async({ssoStartUrl:A,ssoSession:B,ssoAccountId:Q,ssoRegion:D,ssoRoleName:Z,ssoClient:G,clientConfig:F,parentClientConfig:I,profile:Y,logger:W})=>{let J,X="To refresh this SSO session run aws sso login with the corresponding profile.";if(B)try{let f=await a74.fromSso({profile:Y})();J={accessToken:f.token,expiresAt:new Date(f.expiration).toISOString()}}catch(f){throw new xw.CredentialsProviderError(f.message,{tryNextLink:A61,logger:W})}else try{J=await i$1.getSSOTokenFromFile(A)}catch(f){throw new xw.CredentialsProviderError("The SSO session associated with this profile is invalid. To refresh this SSO session run aws sso login with the corresponding profile.",{tryNextLink:A61,logger:W})}if(new Date(J.expiresAt).getTime()-Date.now()<=0)throw new xw.CredentialsProviderError("The SSO session associated with this profile has expired. To refresh this SSO session run aws sso login with the corresponding profile.",{tryNextLink:A61,logger:W});let{accessToken:V}=J,{SSOClient:C,GetRoleCredentialsCommand:K}=await Promise.resolve().then(()=>(n74(),w02)),H=G||new C(Object.assign({},F??{},{logger:F?.logger??I?.logger,region:F?.region??D})),z;try{z=await H.send(new K({accountId:Q,roleName:Z,accessToken:V}))}catch(f){throw new xw.CredentialsProviderError(f,{tryNextLink:A61,logger:W})}let{roleCredentials:{accessKeyId:$,secretAccessKey:L,sessionToken:N,expiration:O,credentialScope:R,accountId:T}={}}=z;if(!$||!L||!N||!O)throw new xw.CredentialsProviderError("SSO returns an invalid temporary credential.",{tryNextLink:A61,logger:W});let j={accessKeyId:$,secretAccessKey:L,sessionToken:N,expiration:new Date(O),...R&&{credentialScope:R},...T&&{accountId:T}};if(B)H02.setCredentialFeature(j,"CREDENTIALS_SSO","s");else H02.setCredentialFeature(j,"CREDENTIALS_SSO_LEGACY","u");return j},"resolveSSOCredentials"),N02=a$1((A,B)=>{let{sso_start_url:Q,sso_account_id:D,sso_region:Z,sso_role_name:G}=A;if(!Q||!D||!Z||!G)throw new xw.CredentialsProviderError(`Profile is configured with invalid SSO credentials. Required parameters "sso_account_id", "sso_region", "sso_role_name", "sso_start_url". Got ${Object.keys(A).join(", ")}
Reference: https://docs.aws.amazon.com/cli/latest/userguide/cli-configure-sso.html`,{tryNextLink:!1,logger:B});return A},"validateSsoProfile"),s74=a$1((A={})=>async({callerClientConfig:B}={})=>{A.logger?.debug("@aws-sdk/credential-provider-sso - fromSSO");let{ssoStartUrl:Q,ssoAccountId:D,ssoRegion:Z,ssoRoleName:G,ssoSession:F}=A,{ssoClient:I}=A,Y=i$1.getProfileName({profile:A.profile??B?.profile});if(!Q&&!D&&!Z&&!G&&!F){let J=(await i$1.parseKnownFiles(A))[Y];if(!J)throw new xw.CredentialsProviderError(`Profile ${Y} was not found.`,{logger:A.logger});if(!q02(J))throw new xw.CredentialsProviderError(`Profile ${Y} is not configured with SSO credentials.`,{logger:A.logger});if(J?.sso_session){let $=(await i$1.loadSsoSessionData(A))[J.sso_session],L=` configurations in profile ${Y} and sso-session ${J.sso_session}`;if(Z&&Z!==$.sso_region)throw new xw.CredentialsProviderError("Conflicting SSO region"+L,{tryNextLink:!1,logger:A.logger});if(Q&&Q!==$.sso_start_url)throw new xw.CredentialsProviderError("Conflicting SSO start_url"+L,{tryNextLink:!1,logger:A.logger});J.sso_region=$.sso_region,J.sso_start_url=$.sso_start_url}let{sso_start_url:X,sso_account_id:V,sso_region:C,sso_role_name:K,sso_session:H}=N02(J,A.logger);return z02({ssoStartUrl:X,ssoSession:H,ssoAccountId:V,ssoRegion:C,ssoRoleName:K,ssoClient:I,clientConfig:A.clientConfig,parentClientConfig:A.parentClientConfig,profile:Y})}else if(!Q||!D||!Z||!G)throw new xw.CredentialsProviderError('Incomplete configuration. The fromSSO() argument hash must include "ssoStartUrl", "ssoAccountId", "ssoRegion", "ssoRoleName"',{tryNextLink:!1,logger:A.logger});else return z02({ssoStartUrl:Q,ssoSession:F,ssoAccountId:D,ssoRegion:Z,ssoRoleName:G,ssoClient:I,clientConfig:A.clientConfig,parentClientConfig:A.parentClientConfig,profile:Y})},"fromSSO")});
var g41=E((dV5,ysA)=>{var{defineProperty:F$1,getOwnPropertyDescriptor:z94,getOwnPropertyNames:E94}=Object,U94=Object.prototype.hasOwnProperty,MQ0=(A,B)=>F$1(A,"name",{value:B,configurable:!0}),w94=(A,B)=>{for(var Q in B)F$1(A,Q,{get:B[Q],enumerable:!0})},$94=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of E94(B))if(!U94.call(A,Z)&&Z!==Q)F$1(A,Z,{get:()=>B[Z],enumerable:!(D=z94(B,Z))||D.enumerable})}return A},q94=(A)=>$94(F$1({},"__esModule",{value:!0}),A),PsA={};w94(PsA,{getLoggerPlugin:()=>N94,loggerMiddleware:()=>SsA,loggerMiddlewareOptions:()=>jsA});ysA.exports=q94(PsA);var SsA=MQ0(()=>(A,B)=>async(Q)=>{try{let D=await A(Q),{clientName:Z,commandName:G,logger:F,dynamoDbDocumentClientOptions:I={}}=B,{overrideInputFilterSensitiveLog:Y,overrideOutputFilterSensitiveLog:W}=I,J=Y??B.inputFilterSensitiveLog,X=W??B.outputFilterSensitiveLog,{$metadata:V,...C}=D.output;return F?.info?.({clientName:Z,commandName:G,input:J(Q.input),output:X(C),metadata:V}),D}catch(D){let{clientName:Z,commandName:G,logger:F,dynamoDbDocumentClientOptions:I={}}=B,{overrideInputFilterSensitiveLog:Y}=I,W=Y??B.inputFilterSensitiveLog;throw F?.error?.({clientName:Z,commandName:G,input:W(Q.input),error:D,metadata:D.$metadata}),D}},"loggerMiddleware"),jsA={name:"loggerMiddleware",tags:["LOGGER"],step:"initialize",override:!0},N94=MQ0((A)=>({applyToStack:MQ0((B)=>{B.add(SsA(),jsA)},"applyToStack")}),"getLoggerPlugin")});
var h40=E((M02)=>{Object.defineProperty(M02,"__esModule",{value:!0});M02.resolveHttpAuthSchemeConfig=M02.resolveStsAuthConfig=M02.defaultSTSHttpAuthSchemeProvider=M02.defaultSTSHttpAuthSchemeParametersProvider=void 0;var r74=CI(),f40=J5(),o74=B61(),t74=async(A,B,Q)=>{return{operation:f40.getSmithyContext(B).operation,region:await f40.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};M02.defaultSTSHttpAuthSchemeParametersProvider=t74;function e74(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"sts",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function AD4(A){return{schemeId:"smithy.api#noAuth"}}var BD4=(A)=>{let B=[];switch(A.operation){case"AssumeRoleWithWebIdentity":{B.push(AD4(A));break}default:B.push(e74(A))}return B};M02.defaultSTSHttpAuthSchemeProvider=BD4;var QD4=(A)=>Object.assign(A,{stsClientCtor:o74.STSClient});M02.resolveStsAuthConfig=QD4;var DD4=(A)=>{let B=M02.resolveStsAuthConfig(A),Q=r74.resolveAwsSdkSigV4Config(B);return Object.assign(Q,{authSchemePreference:f40.normalizeProvider(A.authSchemePreference??[])})};M02.resolveHttpAuthSchemeConfig=DD4});
var h41=E((mV5,TsA)=>{var{defineProperty:G$1,getOwnPropertyDescriptor:Y94,getOwnPropertyNames:W94}=Object,J94=Object.prototype.hasOwnProperty,Z$1=(A,B)=>G$1(A,"name",{value:B,configurable:!0}),X94=(A,B)=>{for(var Q in B)G$1(A,Q,{get:B[Q],enumerable:!0})},V94=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of W94(B))if(!J94.call(A,Z)&&Z!==Q)G$1(A,Z,{get:()=>B[Z],enumerable:!(D=Y94(B,Z))||D.enumerable})}return A},C94=(A)=>V94(G$1({},"__esModule",{value:!0}),A),LsA={};X94(LsA,{getHostHeaderPlugin:()=>H94,hostHeaderMiddleware:()=>RsA,hostHeaderMiddlewareOptions:()=>OsA,resolveHostHeaderConfig:()=>MsA});TsA.exports=C94(LsA);var K94=$V();function MsA(A){return A}Z$1(MsA,"resolveHostHeaderConfig");var RsA=Z$1((A)=>(B)=>async(Q)=>{if(!K94.HttpRequest.isInstance(Q.request))return B(Q);let{request:D}=Q,{handlerProtocol:Z=""}=A.requestHandler.metadata||{};if(Z.indexOf("h2")>=0&&!D.headers[":authority"])delete D.headers.host,D.headers[":authority"]=D.hostname+(D.port?":"+D.port:"");else if(!D.headers.host){let G=D.hostname;if(D.port!=null)G+=`:${D.port}`;D.headers.host=G}return B(Q)},"hostHeaderMiddleware"),OsA={name:"hostHeaderMiddleware",step:"build",priority:"low",tags:["HOST"],override:!0},H94=Z$1((A)=>({applyToStack:Z$1((B)=>{B.add(RsA(A),OsA)},"applyToStack")}),"getHostHeaderPlugin")});
var i22=E((l22)=>{Object.defineProperty(l22,"__esModule",{value:!0});l22.ruleSet=void 0;var m22="required",KL="fn",HL="argv",Cs="ref",y22=!0,k22="isSet",I61="booleanEquals",Vs="error",F61="endpoint",eJ="tree",_60="PartitionResult",_22={[m22]:!1,type:"String"},x22={[m22]:!0,default:!1,type:"Boolean"},v22={[Cs]:"Endpoint"},d22={[KL]:I61,[HL]:[{[Cs]:"UseFIPS"},!0]},c22={[KL]:I61,[HL]:[{[Cs]:"UseDualStack"},!0]},CL={},b22={[KL]:"getAttr",[HL]:[{[Cs]:_60},"supportsFIPS"]},f22={[KL]:I61,[HL]:[!0,{[KL]:"getAttr",[HL]:[{[Cs]:_60},"supportsDualStack"]}]},h22=[d22],g22=[c22],u22=[{[Cs]:"Region"}],iG4={version:"1.0",parameters:{Region:_22,UseDualStack:x22,UseFIPS:x22,Endpoint:_22},rules:[{conditions:[{[KL]:k22,[HL]:[v22]}],rules:[{conditions:h22,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:Vs},{rules:[{conditions:g22,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:Vs},{endpoint:{url:v22,properties:CL,headers:CL},type:F61}],type:eJ}],type:eJ},{rules:[{conditions:[{[KL]:k22,[HL]:u22}],rules:[{conditions:[{[KL]:"aws.partition",[HL]:u22,assign:_60}],rules:[{conditions:[d22,c22],rules:[{conditions:[{[KL]:I61,[HL]:[y22,b22]},f22],rules:[{rules:[{endpoint:{url:"https://bedrock-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:CL,headers:CL},type:F61}],type:eJ}],type:eJ},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:Vs}],type:eJ},{conditions:h22,rules:[{conditions:[{[KL]:I61,[HL]:[b22,y22]}],rules:[{rules:[{endpoint:{url:"https://bedrock-fips.{Region}.{PartitionResult#dnsSuffix}",properties:CL,headers:CL},type:F61}],type:eJ}],type:eJ},{error:"FIPS is enabled but this partition does not support FIPS",type:Vs}],type:eJ},{conditions:g22,rules:[{conditions:[f22],rules:[{rules:[{endpoint:{url:"https://bedrock.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:CL,headers:CL},type:F61}],type:eJ}],type:eJ},{error:"DualStack is enabled but this partition does not support DualStack",type:Vs}],type:eJ},{rules:[{endpoint:{url:"https://bedrock.{Region}.{PartitionResult#dnsSuffix}",properties:CL,headers:CL},type:F61}],type:eJ}],type:eJ}],type:eJ},{error:"Invalid Configuration: Missing Region",type:Vs}],type:eJ}]};l22.ruleSet=iG4});
var i41=E((uC5,dtA)=>{var{defineProperty:x$1,getOwnPropertyDescriptor:u84,getOwnPropertyNames:m84}=Object,d84=Object.prototype.hasOwnProperty,_$1=(A,B)=>x$1(A,"name",{value:B,configurable:!0}),c84=(A,B)=>{for(var Q in B)x$1(A,Q,{get:B[Q],enumerable:!0})},l84=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of m84(B))if(!d84.call(A,Z)&&Z!==Q)x$1(A,Z,{get:()=>B[Z],enumerable:!(D=u84(B,Z))||D.enumerable})}return A},p84=(A)=>l84(x$1({},"__esModule",{value:!0}),A),ftA={};c84(ftA,{NODE_APP_ID_CONFIG_OPTIONS:()=>r84,UA_APP_ID_ENV_NAME:()=>utA,UA_APP_ID_INI_NAME:()=>mtA,createDefaultUserAgentProvider:()=>gtA,crtAvailability:()=>htA,defaultUserAgent:()=>n84});dtA.exports=p84(ftA);var btA=J1("os"),U40=J1("process"),htA={isCrtAvailable:!1},i84=_$1(()=>{if(htA.isCrtAvailable)return["md/crt-avail"];return null},"isCrtAvailable"),gtA=_$1(({serviceId:A,clientVersion:B})=>{return async(Q)=>{let D=[["aws-sdk-js",B],["ua","2.1"],[`os/${btA.platform()}`,btA.release()],["lang/js"],["md/nodejs",`${U40.versions.node}`]],Z=i84();if(Z)D.push(Z);if(A)D.push([`api/${A}`,B]);if(U40.env.AWS_EXECUTION_ENV)D.push([`exec-env/${U40.env.AWS_EXECUTION_ENV}`]);let G=await Q?.userAgentAppId?.();return G?[...D,[`app/${G}`]]:[...D]}},"createDefaultUserAgentProvider"),n84=gtA,a84=na(),utA="AWS_SDK_UA_APP_ID",mtA="sdk_ua_app_id",s84="sdk-ua-app-id",r84={environmentVariableSelector:_$1((A)=>A[utA],"environmentVariableSelector"),configFileSelector:_$1((A)=>A[mtA]??A[s84],"configFileSelector"),default:a84.DEFAULT_UA_APP_ID}});
var j22=E((jK5,S22)=>{var{create:bG4,defineProperty:G61,getOwnPropertyDescriptor:fG4,getOwnPropertyNames:hG4,getPrototypeOf:gG4}=Object,uG4=Object.prototype.hasOwnProperty,e$1=(A,B)=>G61(A,"name",{value:B,configurable:!0}),mG4=(A,B)=>{for(var Q in B)G61(A,Q,{get:B[Q],enumerable:!0})},R22=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of hG4(B))if(!uG4.call(A,Z)&&Z!==Q)G61(A,Z,{get:()=>B[Z],enumerable:!(D=fG4(B,Z))||D.enumerable})}return A},Xs=(A,B,Q)=>(Q=A!=null?bG4(gG4(A)):{},R22(B||!A||!A.__esModule?G61(Q,"default",{value:A,enumerable:!0}):Q,A)),dG4=(A)=>R22(G61({},"__esModule",{value:!0}),A),O22={};mG4(O22,{credentialsTreatedAsExpired:()=>P22,credentialsWillNeedRefresh:()=>T22,defaultProvider:()=>pG4});S22.exports=dG4(O22);var k60=V40(),cG4=D3(),Ug=Q9(),L22="AWS_EC2_METADATA_DISABLED",lG4=e$1(async(A)=>{let{ENV_CMDS_FULL_URI:B,ENV_CMDS_RELATIVE_URI:Q,fromContainerMetadata:D,fromInstanceMetadata:Z}=await Promise.resolve().then(()=>Xs(TF()));if(process.env[Q]||process.env[B]){A.logger?.debug("@aws-sdk/credential-provider-node - remoteProvider::fromHttp/fromContainerMetadata");let{fromHttp:G}=await Promise.resolve().then(()=>Xs(H40()));return Ug.chain(G(A),D(A))}if(process.env[L22]&&process.env[L22]!=="false")return async()=>{throw new Ug.CredentialsProviderError("EC2 Instance Metadata Service access disabled",{logger:A.logger})};return A.logger?.debug("@aws-sdk/credential-provider-node - remoteProvider::fromInstanceMetadata"),Z(A)},"remoteProvider"),M22=!1,pG4=e$1((A={})=>Ug.memoize(Ug.chain(async()=>{if(A.profile??process.env[cG4.ENV_PROFILE]){if(process.env[k60.ENV_KEY]&&process.env[k60.ENV_SECRET]){if(!M22)(A.logger?.warn&&A.logger?.constructor?.name!=="NoOpLogger"?A.logger.warn:console.warn)(`@aws-sdk/credential-provider-node - defaultProvider::fromEnv WARNING:
    Multiple credential sources detected: 
    Both AWS_PROFILE and the pair AWS_ACCESS_KEY_ID/AWS_SECRET_ACCESS_KEY static credentials are set.
    This SDK will proceed with the AWS_PROFILE value.
    
    However, a future version may change this behavior to prefer the ENV static credentials.
    Please ensure that your environment only sets either the AWS_PROFILE or the
    AWS_ACCESS_KEY_ID/AWS_SECRET_ACCESS_KEY pair.
`),M22=!0}throw new Ug.CredentialsProviderError("AWS_PROFILE is set, skipping fromEnv provider.",{logger:A.logger,tryNextLink:!0})}return A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromEnv"),k60.fromEnv(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromSSO");let{ssoStartUrl:B,ssoAccountId:Q,ssoRegion:D,ssoRoleName:Z,ssoSession:G}=A;if(!B&&!Q&&!D&&!Z&&!G)throw new Ug.CredentialsProviderError("Skipping SSO provider in default chain (inputs do not include SSO fields).",{logger:A.logger});let{fromSSO:F}=await Promise.resolve().then(()=>Xs(b40()));return F(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromIni");let{fromIni:B}=await Promise.resolve().then(()=>Xs(N22()));return B(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromProcess");let{fromProcess:B}=await Promise.resolve().then(()=>Xs(R60()));return B(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromTokenFile");let{fromTokenFile:B}=await Promise.resolve().then(()=>Xs(S60()));return B(A)()},async()=>{return A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::remoteProvider"),(await lG4(A))()},async()=>{throw new Ug.CredentialsProviderError("Could not load credentials from any providers",{tryNextLink:!1,logger:A.logger})}),P22,T22),"defaultProvider"),T22=e$1((A)=>A?.expiration!==void 0,"credentialsWillNeedRefresh"),P22=e$1((A)=>A?.expiration!==void 0&&A.expiration.getTime()-Date.now()<300000,"credentialsTreatedAsExpired")});
var k40=E((GK5,I02)=>{var{defineProperty:p$1,getOwnPropertyDescriptor:u34,getOwnPropertyNames:m34}=Object,d34=Object.prototype.hasOwnProperty,t4=(A,B)=>p$1(A,"name",{value:B,configurable:!0}),c34=(A,B)=>{for(var Q in B)p$1(A,Q,{get:B[Q],enumerable:!0})},l34=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of m34(B))if(!d34.call(A,Z)&&Z!==Q)p$1(A,Z,{get:()=>B[Z],enumerable:!(D=u34(B,Z))||D.enumerable})}return A},p34=(A)=>l34(p$1({},"__esModule",{value:!0}),A),d12={};c34(d12,{$Command:()=>p12.Command,AccessDeniedException:()=>i12,AuthorizationPendingException:()=>n12,CreateTokenCommand:()=>G02,CreateTokenRequestFilterSensitiveLog:()=>a12,CreateTokenResponseFilterSensitiveLog:()=>s12,ExpiredTokenException:()=>r12,InternalServerException:()=>o12,InvalidClientException:()=>t12,InvalidGrantException:()=>e12,InvalidRequestException:()=>A02,InvalidScopeException:()=>B02,SSOOIDC:()=>F02,SSOOIDCClient:()=>l12,SSOOIDCServiceException:()=>KK,SlowDownException:()=>Q02,UnauthorizedClientException:()=>D02,UnsupportedGrantTypeException:()=>Z02,__Client:()=>c12.Client});I02.exports=p34(d12);var v12=h41(),i34=g41(),n34=u41(),b12=na(),a34=K4(),j40=CB(),s34=bG(),r34=R6(),f12=u4(),c12=C6(),h12=R40(),o34=t4((A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,defaultSigningName:"sso-oauth"})},"resolveClientEndpointParameters"),t34={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}},e34=x12(),g12=a41(),u12=$V(),m12=C6(),A74=t4((A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}},"getHttpAuthExtensionConfiguration"),B74=t4((A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}},"resolveHttpAuthRuntimeConfig"),Q74=t4((A,B)=>{let Q=Object.assign(g12.getAwsRegionExtensionConfiguration(A),m12.getDefaultExtensionConfiguration(A),u12.getHttpHandlerExtensionConfiguration(A),A74(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,g12.resolveAwsRegionExtensionConfiguration(Q),m12.resolveDefaultRuntimeConfig(Q),u12.resolveHttpHandlerRuntimeConfig(Q),B74(Q))},"resolveRuntimeExtensions"),l12=class extends c12.Client{static{t4(this,"SSOOIDCClient")}config;constructor(...[A]){let B=e34.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=o34(B),D=b12.resolveUserAgentConfig(Q),Z=f12.resolveRetryConfig(D),G=a34.resolveRegionConfig(Z),F=v12.resolveHostHeaderConfig(G),I=r34.resolveEndpointConfig(F),Y=h12.resolveHttpAuthSchemeConfig(I),W=Q74(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(b12.getUserAgentPlugin(this.config)),this.middlewareStack.use(f12.getRetryPlugin(this.config)),this.middlewareStack.use(s34.getContentLengthPlugin(this.config)),this.middlewareStack.use(v12.getHostHeaderPlugin(this.config)),this.middlewareStack.use(i34.getLoggerPlugin(this.config)),this.middlewareStack.use(n34.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(j40.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:h12.defaultSSOOIDCHttpAuthSchemeParametersProvider,identityProviderConfigProvider:t4(async(J)=>new j40.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials}),"identityProviderConfigProvider")})),this.middlewareStack.use(j40.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}},D74=C6(),Z74=R6(),G74=j3(),p12=C6(),Bs=C6(),F74=C6(),KK=class A extends F74.ServiceException{static{t4(this,"SSOOIDCServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},i12=class A extends KK{static{t4(this,"AccessDeniedException")}name="AccessDeniedException";$fault="client";error;error_description;constructor(B){super({name:"AccessDeniedException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},n12=class A extends KK{static{t4(this,"AuthorizationPendingException")}name="AuthorizationPendingException";$fault="client";error;error_description;constructor(B){super({name:"AuthorizationPendingException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},a12=t4((A)=>({...A,...A.clientSecret&&{clientSecret:Bs.SENSITIVE_STRING},...A.refreshToken&&{refreshToken:Bs.SENSITIVE_STRING},...A.codeVerifier&&{codeVerifier:Bs.SENSITIVE_STRING}}),"CreateTokenRequestFilterSensitiveLog"),s12=t4((A)=>({...A,...A.accessToken&&{accessToken:Bs.SENSITIVE_STRING},...A.refreshToken&&{refreshToken:Bs.SENSITIVE_STRING},...A.idToken&&{idToken:Bs.SENSITIVE_STRING}}),"CreateTokenResponseFilterSensitiveLog"),r12=class A extends KK{static{t4(this,"ExpiredTokenException")}name="ExpiredTokenException";$fault="client";error;error_description;constructor(B){super({name:"ExpiredTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},o12=class A extends KK{static{t4(this,"InternalServerException")}name="InternalServerException";$fault="server";error;error_description;constructor(B){super({name:"InternalServerException",$fault:"server",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},t12=class A extends KK{static{t4(this,"InvalidClientException")}name="InvalidClientException";$fault="client";error;error_description;constructor(B){super({name:"InvalidClientException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},e12=class A extends KK{static{t4(this,"InvalidGrantException")}name="InvalidGrantException";$fault="client";error;error_description;constructor(B){super({name:"InvalidGrantException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},A02=class A extends KK{static{t4(this,"InvalidRequestException")}name="InvalidRequestException";$fault="client";error;error_description;constructor(B){super({name:"InvalidRequestException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},B02=class A extends KK{static{t4(this,"InvalidScopeException")}name="InvalidScopeException";$fault="client";error;error_description;constructor(B){super({name:"InvalidScopeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},Q02=class A extends KK{static{t4(this,"SlowDownException")}name="SlowDownException";$fault="client";error;error_description;constructor(B){super({name:"SlowDownException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},D02=class A extends KK{static{t4(this,"UnauthorizedClientException")}name="UnauthorizedClientException";$fault="client";error;error_description;constructor(B){super({name:"UnauthorizedClientException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},Z02=class A extends KK{static{t4(this,"UnsupportedGrantTypeException")}name="UnsupportedGrantTypeException";$fault="client";error;error_description;constructor(B){super({name:"UnsupportedGrantTypeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},y40=CI(),I74=CB(),$B=C6(),Y74=t4(async(A,B)=>{let Q=I74.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/token");let Z;return Z=JSON.stringify($B.take(A,{clientId:[],clientSecret:[],code:[],codeVerifier:[],deviceCode:[],grantType:[],redirectUri:[],refreshToken:[],scope:t4((G)=>$B._json(G),"scope")})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateTokenCommand"),W74=t4(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return J74(A,B);let Q=$B.map({$metadata:mz(A)}),D=$B.expectNonNull($B.expectObject(await y40.parseJsonBody(A.body,B)),"body"),Z=$B.take(D,{accessToken:$B.expectString,expiresIn:$B.expectInt32,idToken:$B.expectString,refreshToken:$B.expectString,tokenType:$B.expectString});return Object.assign(Q,Z),Q},"de_CreateTokenCommand"),J74=t4(async(A,B)=>{let Q={...A,body:await y40.parseJsonErrorBody(A.body,B)},D=y40.loadRestJsonErrorCode(A,Q.body);switch(D){case"AccessDeniedException":case"com.amazonaws.ssooidc#AccessDeniedException":throw await V74(Q,B);case"AuthorizationPendingException":case"com.amazonaws.ssooidc#AuthorizationPendingException":throw await C74(Q,B);case"ExpiredTokenException":case"com.amazonaws.ssooidc#ExpiredTokenException":throw await K74(Q,B);case"InternalServerException":case"com.amazonaws.ssooidc#InternalServerException":throw await H74(Q,B);case"InvalidClientException":case"com.amazonaws.ssooidc#InvalidClientException":throw await z74(Q,B);case"InvalidGrantException":case"com.amazonaws.ssooidc#InvalidGrantException":throw await E74(Q,B);case"InvalidRequestException":case"com.amazonaws.ssooidc#InvalidRequestException":throw await U74(Q,B);case"InvalidScopeException":case"com.amazonaws.ssooidc#InvalidScopeException":throw await w74(Q,B);case"SlowDownException":case"com.amazonaws.ssooidc#SlowDownException":throw await $74(Q,B);case"UnauthorizedClientException":case"com.amazonaws.ssooidc#UnauthorizedClientException":throw await q74(Q,B);case"UnsupportedGrantTypeException":case"com.amazonaws.ssooidc#UnsupportedGrantTypeException":throw await N74(Q,B);default:let Z=Q.body;return X74({output:A,parsedBody:Z,errorCode:D})}},"de_CommandError"),X74=$B.withBaseException(KK),V74=t4(async(A,B)=>{let Q=$B.map({}),D=A.body,Z=$B.take(D,{error:$B.expectString,error_description:$B.expectString});Object.assign(Q,Z);let G=new i12({$metadata:mz(A),...Q});return $B.decorateServiceException(G,A.body)},"de_AccessDeniedExceptionRes"),C74=t4(async(A,B)=>{let Q=$B.map({}),D=A.body,Z=$B.take(D,{error:$B.expectString,error_description:$B.expectString});Object.assign(Q,Z);let G=new n12({$metadata:mz(A),...Q});return $B.decorateServiceException(G,A.body)},"de_AuthorizationPendingExceptionRes"),K74=t4(async(A,B)=>{let Q=$B.map({}),D=A.body,Z=$B.take(D,{error:$B.expectString,error_description:$B.expectString});Object.assign(Q,Z);let G=new r12({$metadata:mz(A),...Q});return $B.decorateServiceException(G,A.body)},"de_ExpiredTokenExceptionRes"),H74=t4(async(A,B)=>{let Q=$B.map({}),D=A.body,Z=$B.take(D,{error:$B.expectString,error_description:$B.expectString});Object.assign(Q,Z);let G=new o12({$metadata:mz(A),...Q});return $B.decorateServiceException(G,A.body)},"de_InternalServerExceptionRes"),z74=t4(async(A,B)=>{let Q=$B.map({}),D=A.body,Z=$B.take(D,{error:$B.expectString,error_description:$B.expectString});Object.assign(Q,Z);let G=new t12({$metadata:mz(A),...Q});return $B.decorateServiceException(G,A.body)},"de_InvalidClientExceptionRes"),E74=t4(async(A,B)=>{let Q=$B.map({}),D=A.body,Z=$B.take(D,{error:$B.expectString,error_description:$B.expectString});Object.assign(Q,Z);let G=new e12({$metadata:mz(A),...Q});return $B.decorateServiceException(G,A.body)},"de_InvalidGrantExceptionRes"),U74=t4(async(A,B)=>{let Q=$B.map({}),D=A.body,Z=$B.take(D,{error:$B.expectString,error_description:$B.expectString});Object.assign(Q,Z);let G=new A02({$metadata:mz(A),...Q});return $B.decorateServiceException(G,A.body)},"de_InvalidRequestExceptionRes"),w74=t4(async(A,B)=>{let Q=$B.map({}),D=A.body,Z=$B.take(D,{error:$B.expectString,error_description:$B.expectString});Object.assign(Q,Z);let G=new B02({$metadata:mz(A),...Q});return $B.decorateServiceException(G,A.body)},"de_InvalidScopeExceptionRes"),$74=t4(async(A,B)=>{let Q=$B.map({}),D=A.body,Z=$B.take(D,{error:$B.expectString,error_description:$B.expectString});Object.assign(Q,Z);let G=new Q02({$metadata:mz(A),...Q});return $B.decorateServiceException(G,A.body)},"de_SlowDownExceptionRes"),q74=t4(async(A,B)=>{let Q=$B.map({}),D=A.body,Z=$B.take(D,{error:$B.expectString,error_description:$B.expectString});Object.assign(Q,Z);let G=new D02({$metadata:mz(A),...Q});return $B.decorateServiceException(G,A.body)},"de_UnauthorizedClientExceptionRes"),N74=t4(async(A,B)=>{let Q=$B.map({}),D=A.body,Z=$B.take(D,{error:$B.expectString,error_description:$B.expectString});Object.assign(Q,Z);let G=new Z02({$metadata:mz(A),...Q});return $B.decorateServiceException(G,A.body)},"de_UnsupportedGrantTypeExceptionRes"),mz=t4((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),G02=class extends p12.Command.classBuilder().ep(t34).m(function(A,B,Q,D){return[G74.getSerdePlugin(Q,this.serialize,this.deserialize),Z74.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSSOOIDCService","CreateToken",{}).n("SSOOIDCClient","CreateTokenCommand").f(a12,s12).ser(Y74).de(W74).build(){static{t4(this,"CreateTokenCommand")}},L74={CreateTokenCommand:G02},F02=class extends l12{static{t4(this,"SSOOIDC")}};D74.createAggregatedClient(L74,F02)});
var kk=E((ZC5,R$1)=>{var{defineProperty:RoA,getOwnPropertyDescriptor:Z44,getOwnPropertyNames:G44}=Object,F44=Object.prototype.hasOwnProperty,dQ0=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of G44(B))if(!F44.call(A,Z)&&Z!==Q)RoA(A,Z,{get:()=>B[Z],enumerable:!(D=Z44(B,Z))||D.enumerable})}return A},OoA=(A,B,Q)=>(dQ0(A,B,"default"),Q&&dQ0(Q,B,"default")),I44=(A)=>dQ0(RoA({},"__esModule",{value:!0}),A),cQ0={};R$1.exports=I44(cQ0);OoA(cQ0,qoA(),R$1.exports);OoA(cQ0,MoA(),R$1.exports)});
var ktA=E((jtA)=>{Object.defineProperty(jtA,"__esModule",{value:!0});jtA.fromHttp=void 0;var U84=Jg(),w84=_w(),$84=k3(),PtA=Q9(),q84=U84.__importDefault(J1("fs/promises")),N84=NtA(),StA=MtA(),L84=TtA(),M84="AWS_CONTAINER_CREDENTIALS_RELATIVE_URI",R84="http://*************",O84="AWS_CONTAINER_CREDENTIALS_FULL_URI",T84="AWS_CONTAINER_AUTHORIZATION_TOKEN_FILE",P84="AWS_CONTAINER_AUTHORIZATION_TOKEN",S84=(A={})=>{A.logger?.debug("@aws-sdk/credential-provider-http - fromHttp");let B,Q=A.awsContainerCredentialsRelativeUri??process.env[M84],D=A.awsContainerCredentialsFullUri??process.env[O84],Z=A.awsContainerAuthorizationToken??process.env[P84],G=A.awsContainerAuthorizationTokenFile??process.env[T84],F=A.logger?.constructor?.name==="NoOpLogger"||!A.logger?console.warn:A.logger.warn;if(Q&&D)F("@aws-sdk/credential-provider-http: you have set both awsContainerCredentialsRelativeUri and awsContainerCredentialsFullUri."),F("awsContainerCredentialsFullUri will take precedence.");if(Z&&G)F("@aws-sdk/credential-provider-http: you have set both awsContainerAuthorizationToken and awsContainerAuthorizationTokenFile."),F("awsContainerAuthorizationToken will take precedence.");if(D)B=D;else if(Q)B=`${R84}${Q}`;else throw new PtA.CredentialsProviderError(`No HTTP credential provider host provided.
Set AWS_CONTAINER_CREDENTIALS_FULL_URI or AWS_CONTAINER_CREDENTIALS_RELATIVE_URI.`,{logger:A.logger});let I=new URL(B);N84.checkUrl(I,A.logger);let Y=new $84.NodeHttpHandler({requestTimeout:A.timeout??1000,connectionTimeout:A.timeout??1000});return L84.retryWrapper(async()=>{let W=StA.createGetRequest(I);if(Z)W.headers.Authorization=Z;else if(G)W.headers.Authorization=(await q84.default.readFile(G)).toString();try{let J=await Y.handle(W);return StA.getCredentials(J.response).then((X)=>w84.setCredentialFeature(X,"CREDENTIALS_HTTP","z"))}catch(J){throw new PtA.CredentialsProviderError(String(J),{logger:A.logger})}},A.maxRetries??3,A.timeout??1000)};jtA.fromHttp=S84});
var mQ0=E((eV5,UoA)=>{var{defineProperty:M$1,getOwnPropertyDescriptor:xQ4,getOwnPropertyNames:vQ4}=Object,bQ4=Object.prototype.hasOwnProperty,QG=(A,B)=>M$1(A,"name",{value:B,configurable:!0}),fQ4=(A,B)=>{for(var Q in B)M$1(A,Q,{get:B[Q],enumerable:!0})},hQ4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of vQ4(B))if(!bQ4.call(A,Z)&&Z!==Q)M$1(A,Z,{get:()=>B[Z],enumerable:!(D=xQ4(B,Z))||D.enumerable})}return A},gQ4=(A)=>hQ4(M$1({},"__esModule",{value:!0}),A),CoA={};fQ4(CoA,{AWSSDKSigV4Signer:()=>cQ4,AwsSdkSigV4ASigner:()=>pQ4,AwsSdkSigV4Signer:()=>uQ0,NODE_AUTH_SCHEME_PREFERENCE_OPTIONS:()=>iQ4,NODE_SIGV4A_CONFIG_OPTIONS:()=>sQ4,getBearerTokenEnvKey:()=>KoA,resolveAWSSDKSigV4Config:()=>oQ4,resolveAwsSdkSigV4AConfig:()=>aQ4,resolveAwsSdkSigV4Config:()=>HoA,validateSigningProperties:()=>gQ0});UoA.exports=gQ4(CoA);var uQ4=$V(),mQ4=$V(),FoA=QG((A)=>mQ4.HttpResponse.isInstance(A)?A.headers?.date??A.headers?.Date:void 0,"getDateHeader"),hQ0=QG((A)=>new Date(Date.now()+A),"getSkewCorrectedDate"),dQ4=QG((A,B)=>Math.abs(hQ0(B).getTime()-A)>=300000,"isClockSkewed"),IoA=QG((A,B)=>{let Q=Date.parse(A);if(dQ4(Q,B))return Q-Date.now();return B},"getUpdatedSystemClockOffset"),m41=QG((A,B)=>{if(!B)throw new Error(`Property \`${A}\` is not resolved for AWS SDK SigV4Auth`);return B},"throwSigningPropertyError"),gQ0=QG(async(A)=>{let B=m41("context",A.context),Q=m41("config",A.config),D=B.endpointV2?.properties?.authSchemes?.[0],G=await m41("signer",Q.signer)(D),F=A?.signingRegion,I=A?.signingRegionSet,Y=A?.signingName;return{config:Q,signer:G,signingRegion:F,signingRegionSet:I,signingName:Y}},"validateSigningProperties"),uQ0=class{static{QG(this,"AwsSdkSigV4Signer")}async sign(A,B,Q){if(!uQ4.HttpRequest.isInstance(A))throw new Error("The request is not an instance of `HttpRequest` and cannot be signed");let D=await gQ0(Q),{config:Z,signer:G}=D,{signingRegion:F,signingName:I}=D,Y=Q.context;if(Y?.authSchemes?.length??0>1){let[J,X]=Y.authSchemes;if(J?.name==="sigv4a"&&X?.name==="sigv4")F=X?.signingRegion??F,I=X?.signingName??I}return await G.sign(A,{signingDate:hQ0(Z.systemClockOffset),signingRegion:F,signingService:I})}errorHandler(A){return(B)=>{let Q=B.ServerTime??FoA(B.$response);if(Q){let D=m41("config",A.config),Z=D.systemClockOffset;if(D.systemClockOffset=IoA(Q,D.systemClockOffset),D.systemClockOffset!==Z&&B.$metadata)B.$metadata.clockSkewCorrected=!0}throw B}}successHandler(A,B){let Q=FoA(A);if(Q){let D=m41("config",B.config);D.systemClockOffset=IoA(Q,D.systemClockOffset)}}},cQ4=uQ0,lQ4=$V(),pQ4=class extends uQ0{static{QG(this,"AwsSdkSigV4ASigner")}async sign(A,B,Q){if(!lQ4.HttpRequest.isInstance(A))throw new Error("The request is not an instance of `HttpRequest` and cannot be signed");let{config:D,signer:Z,signingRegion:G,signingRegionSet:F,signingName:I}=await gQ0(Q),W=(await D.sigv4aSigningRegionSet?.()??F??[G]).join(",");return await Z.sign(A,{signingDate:hQ0(D.systemClockOffset),signingRegion:W,signingService:I})}},YoA=QG((A)=>typeof A==="string"&&A.length>0?A.split(",").map((B)=>B.trim()):[],"getArrayForCommaSeparatedString"),KoA=QG((A)=>`AWS_BEARER_TOKEN_${A.replace(/[\s-]/g,"_").toUpperCase()}`,"getBearerTokenEnvKey"),WoA="AWS_AUTH_SCHEME_PREFERENCE",JoA="auth_scheme_preference",iQ4={environmentVariableSelector:QG((A,B)=>{if(B?.signingName){if(KoA(B.signingName)in A)return["httpBearerAuth"]}if(!(WoA in A))return;return YoA(A[WoA])},"environmentVariableSelector"),configFileSelector:QG((A)=>{if(!(JoA in A))return;return YoA(A[JoA])},"configFileSelector"),default:[]},nQ4=CB(),XoA=Q9(),aQ4=QG((A)=>{return A.sigv4aSigningRegionSet=nQ4.normalizeProvider(A.sigv4aSigningRegionSet),A},"resolveAwsSdkSigV4AConfig"),sQ4={environmentVariableSelector(A){if(A.AWS_SIGV4A_SIGNING_REGION_SET)return A.AWS_SIGV4A_SIGNING_REGION_SET.split(",").map((B)=>B.trim());throw new XoA.ProviderError("AWS_SIGV4A_SIGNING_REGION_SET not set in env.",{tryNextLink:!0})},configFileSelector(A){if(A.sigv4a_signing_region_set)return(A.sigv4a_signing_region_set??"").split(",").map((B)=>B.trim());throw new XoA.ProviderError("sigv4a_signing_region_set not set in profile.",{tryNextLink:!0})},default:void 0},rQ4=_w(),Xg=CB(),VoA=GoA(),HoA=QG((A)=>{let B=A.credentials,Q=!!A.credentials,D=void 0;Object.defineProperty(A,"credentials",{set(W){if(W&&W!==B&&W!==D)Q=!0;B=W;let J=zoA(A,{credentials:B,credentialDefaultProvider:A.credentialDefaultProvider}),X=EoA(A,J);if(Q&&!X.attributed)D=QG(async(V)=>X(V).then((C)=>rQ4.setCredentialFeature(C,"CREDENTIALS_CODE","e")),"resolvedCredentials"),D.memoized=X.memoized,D.configBound=X.configBound,D.attributed=!0;else D=X},get(){return D},enumerable:!0,configurable:!0}),A.credentials=B;let{signingEscapePath:Z=!0,systemClockOffset:G=A.systemClockOffset||0,sha256:F}=A,I;if(A.signer)I=Xg.normalizeProvider(A.signer);else if(A.regionInfoProvider)I=QG(()=>Xg.normalizeProvider(A.region)().then(async(W)=>[await A.regionInfoProvider(W,{useFipsEndpoint:await A.useFipsEndpoint(),useDualstackEndpoint:await A.useDualstackEndpoint()})||{},W]).then(([W,J])=>{let{signingRegion:X,signingService:V}=W;A.signingRegion=A.signingRegion||X||J,A.signingName=A.signingName||V||A.serviceId;let C={...A,credentials:A.credentials,region:A.signingRegion,service:A.signingName,sha256:F,uriEscapePath:Z};return new(A.signerConstructor||VoA.SignatureV4)(C)}),"signer");else I=QG(async(W)=>{W=Object.assign({},{name:"sigv4",signingName:A.signingName||A.defaultSigningName,signingRegion:await Xg.normalizeProvider(A.region)(),properties:{}},W);let{signingRegion:J,signingName:X}=W;A.signingRegion=A.signingRegion||J,A.signingName=A.signingName||X||A.serviceId;let V={...A,credentials:A.credentials,region:A.signingRegion,service:A.signingName,sha256:F,uriEscapePath:Z};return new(A.signerConstructor||VoA.SignatureV4)(V)},"signer");return Object.assign(A,{systemClockOffset:G,signingEscapePath:Z,signer:I})},"resolveAwsSdkSigV4Config"),oQ4=HoA;function zoA(A,{credentials:B,credentialDefaultProvider:Q}){let D;if(B)if(!B?.memoized)D=Xg.memoizeIdentityProvider(B,Xg.isIdentityExpired,Xg.doesIdentityRequireRefresh);else D=B;else if(Q)D=Xg.normalizeProvider(Q(Object.assign({},A,{parentClientConfig:A})));else D=QG(async()=>{throw new Error("@aws-sdk/core::resolveAwsSdkSigV4Config - `credentials` not provided and no credentialDefaultProvider was configured.")},"credentialsProvider");return D.memoized=!0,D}QG(zoA,"normalizeCredentialProvider");function EoA(A,B){if(B.configBound)return B;let Q=QG(async(D)=>B({...D,callerClientConfig:A}),"fn");return Q.memoized=B.memoized,Q.configBound=!0,Q}QG(EoA,"bindCallerConfig")});
var ma=E((lV5,psA)=>{var{defineProperty:W$1,getOwnPropertyDescriptor:_94,getOwnPropertyNames:x94}=Object,v94=Object.prototype.hasOwnProperty,ua=(A,B)=>W$1(A,"name",{value:B,configurable:!0}),b94=(A,B)=>{for(var Q in B)W$1(A,Q,{get:B[Q],enumerable:!0})},f94=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of x94(B))if(!v94.call(A,Z)&&Z!==Q)W$1(A,Z,{get:()=>B[Z],enumerable:!(D=_94(B,Z))||D.enumerable})}return A},h94=(A)=>f94(W$1({},"__esModule",{value:!0}),A),fsA={};b94(fsA,{ConditionObject:()=>Q7.ConditionObject,DeprecatedObject:()=>Q7.DeprecatedObject,EndpointError:()=>Q7.EndpointError,EndpointObject:()=>Q7.EndpointObject,EndpointObjectHeaders:()=>Q7.EndpointObjectHeaders,EndpointObjectProperties:()=>Q7.EndpointObjectProperties,EndpointParams:()=>Q7.EndpointParams,EndpointResolverOptions:()=>Q7.EndpointResolverOptions,EndpointRuleObject:()=>Q7.EndpointRuleObject,ErrorRuleObject:()=>Q7.ErrorRuleObject,EvaluateOptions:()=>Q7.EvaluateOptions,Expression:()=>Q7.Expression,FunctionArgv:()=>Q7.FunctionArgv,FunctionObject:()=>Q7.FunctionObject,FunctionReturn:()=>Q7.FunctionReturn,ParameterObject:()=>Q7.ParameterObject,ReferenceObject:()=>Q7.ReferenceObject,ReferenceRecord:()=>Q7.ReferenceRecord,RuleSetObject:()=>Q7.RuleSetObject,RuleSetRules:()=>Q7.RuleSetRules,TreeRuleObject:()=>Q7.TreeRuleObject,awsEndpointFunctions:()=>lsA,getUserAgentPrefix:()=>d94,isIpAddress:()=>Q7.isIpAddress,partition:()=>dsA,resolveEndpoint:()=>Q7.resolveEndpoint,setPartitionInfo:()=>csA,useDefaultPartitionInfo:()=>m94});psA.exports=h94(fsA);var Q7=S7(),hsA=ua((A,B=!1)=>{if(B){for(let Q of A.split("."))if(!hsA(Q))return!1;return!0}if(!Q7.isValidHostLabel(A))return!1;if(A.length<3||A.length>63)return!1;if(A!==A.toLowerCase())return!1;if(Q7.isIpAddress(A))return!1;return!0},"isVirtualHostableS3Bucket"),bsA=":",g94="/",u94=ua((A)=>{let B=A.split(bsA);if(B.length<6)return null;let[Q,D,Z,G,F,...I]=B;if(Q!=="arn"||D===""||Z===""||I.join(bsA)==="")return null;let Y=I.map((W)=>W.split(g94)).flat();return{partition:D,service:Z,region:G,accountId:F,resourceId:Y}},"parseArn"),gsA={partitions:[{id:"aws",outputs:{dnsSuffix:"amazonaws.com",dualStackDnsSuffix:"api.aws",implicitGlobalRegion:"us-east-1",name:"aws",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^(us|eu|ap|sa|ca|me|af|il|mx)\\-\\w+\\-\\d+$",regions:{"af-south-1":{description:"Africa (Cape Town)"},"ap-east-1":{description:"Asia Pacific (Hong Kong)"},"ap-east-2":{description:"Asia Pacific (Taipei)"},"ap-northeast-1":{description:"Asia Pacific (Tokyo)"},"ap-northeast-2":{description:"Asia Pacific (Seoul)"},"ap-northeast-3":{description:"Asia Pacific (Osaka)"},"ap-south-1":{description:"Asia Pacific (Mumbai)"},"ap-south-2":{description:"Asia Pacific (Hyderabad)"},"ap-southeast-1":{description:"Asia Pacific (Singapore)"},"ap-southeast-2":{description:"Asia Pacific (Sydney)"},"ap-southeast-3":{description:"Asia Pacific (Jakarta)"},"ap-southeast-4":{description:"Asia Pacific (Melbourne)"},"ap-southeast-5":{description:"Asia Pacific (Malaysia)"},"ap-southeast-7":{description:"Asia Pacific (Thailand)"},"aws-global":{description:"AWS Standard global region"},"ca-central-1":{description:"Canada (Central)"},"ca-west-1":{description:"Canada West (Calgary)"},"eu-central-1":{description:"Europe (Frankfurt)"},"eu-central-2":{description:"Europe (Zurich)"},"eu-north-1":{description:"Europe (Stockholm)"},"eu-south-1":{description:"Europe (Milan)"},"eu-south-2":{description:"Europe (Spain)"},"eu-west-1":{description:"Europe (Ireland)"},"eu-west-2":{description:"Europe (London)"},"eu-west-3":{description:"Europe (Paris)"},"il-central-1":{description:"Israel (Tel Aviv)"},"me-central-1":{description:"Middle East (UAE)"},"me-south-1":{description:"Middle East (Bahrain)"},"mx-central-1":{description:"Mexico (Central)"},"sa-east-1":{description:"South America (Sao Paulo)"},"us-east-1":{description:"US East (N. Virginia)"},"us-east-2":{description:"US East (Ohio)"},"us-west-1":{description:"US West (N. California)"},"us-west-2":{description:"US West (Oregon)"}}},{id:"aws-cn",outputs:{dnsSuffix:"amazonaws.com.cn",dualStackDnsSuffix:"api.amazonwebservices.com.cn",implicitGlobalRegion:"cn-northwest-1",name:"aws-cn",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^cn\\-\\w+\\-\\d+$",regions:{"aws-cn-global":{description:"AWS China global region"},"cn-north-1":{description:"China (Beijing)"},"cn-northwest-1":{description:"China (Ningxia)"}}},{id:"aws-us-gov",outputs:{dnsSuffix:"amazonaws.com",dualStackDnsSuffix:"api.aws",implicitGlobalRegion:"us-gov-west-1",name:"aws-us-gov",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^us\\-gov\\-\\w+\\-\\d+$",regions:{"aws-us-gov-global":{description:"AWS GovCloud (US) global region"},"us-gov-east-1":{description:"AWS GovCloud (US-East)"},"us-gov-west-1":{description:"AWS GovCloud (US-West)"}}},{id:"aws-iso",outputs:{dnsSuffix:"c2s.ic.gov",dualStackDnsSuffix:"c2s.ic.gov",implicitGlobalRegion:"us-iso-east-1",name:"aws-iso",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-iso\\-\\w+\\-\\d+$",regions:{"aws-iso-global":{description:"AWS ISO (US) global region"},"us-iso-east-1":{description:"US ISO East"},"us-iso-west-1":{description:"US ISO WEST"}}},{id:"aws-iso-b",outputs:{dnsSuffix:"sc2s.sgov.gov",dualStackDnsSuffix:"sc2s.sgov.gov",implicitGlobalRegion:"us-isob-east-1",name:"aws-iso-b",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-isob\\-\\w+\\-\\d+$",regions:{"aws-iso-b-global":{description:"AWS ISOB (US) global region"},"us-isob-east-1":{description:"US ISOB East (Ohio)"}}},{id:"aws-iso-e",outputs:{dnsSuffix:"cloud.adc-e.uk",dualStackDnsSuffix:"cloud.adc-e.uk",implicitGlobalRegion:"eu-isoe-west-1",name:"aws-iso-e",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^eu\\-isoe\\-\\w+\\-\\d+$",regions:{"aws-iso-e-global":{description:"AWS ISOE (Europe) global region"},"eu-isoe-west-1":{description:"EU ISOE West"}}},{id:"aws-iso-f",outputs:{dnsSuffix:"csp.hci.ic.gov",dualStackDnsSuffix:"csp.hci.ic.gov",implicitGlobalRegion:"us-isof-south-1",name:"aws-iso-f",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-isof\\-\\w+\\-\\d+$",regions:{"aws-iso-f-global":{description:"AWS ISOF global region"},"us-isof-east-1":{description:"US ISOF EAST"},"us-isof-south-1":{description:"US ISOF SOUTH"}}},{id:"aws-eusc",outputs:{dnsSuffix:"amazonaws.eu",dualStackDnsSuffix:"amazonaws.eu",implicitGlobalRegion:"eusc-de-east-1",name:"aws-eusc",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^eusc\\-(de)\\-\\w+\\-\\d+$",regions:{"eusc-de-east-1":{description:"EU (Germany)"}}}],version:"1.1"},usA=gsA,msA="",dsA=ua((A)=>{let{partitions:B}=usA;for(let D of B){let{regions:Z,outputs:G}=D;for(let[F,I]of Object.entries(Z))if(F===A)return{...G,...I}}for(let D of B){let{regionRegex:Z,outputs:G}=D;if(new RegExp(Z).test(A))return{...G}}let Q=B.find((D)=>D.id==="aws");if(!Q)throw new Error("Provided region was not found in the partition array or regex, and default partition with id 'aws' doesn't exist.");return{...Q.outputs}},"partition"),csA=ua((A,B="")=>{usA=A,msA=B},"setPartitionInfo"),m94=ua(()=>{csA(gsA,"")},"useDefaultPartitionInfo"),d94=ua(()=>msA,"getUserAgentPrefix"),lsA={isVirtualHostableS3Bucket:hsA,parseArn:u94,partition:dsA};Q7.customEndpointFunctions.aws=lsA});
var na=E((SC5,YtA)=>{var{defineProperty:j$1,getOwnPropertyDescriptor:O64,getOwnPropertyNames:T64}=Object,P64=Object.prototype.hasOwnProperty,TT=(A,B)=>j$1(A,"name",{value:B,configurable:!0}),S64=(A,B)=>{for(var Q in B)j$1(A,Q,{get:B[Q],enumerable:!0})},j64=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of T64(B))if(!P64.call(A,Z)&&Z!==Q)j$1(A,Z,{get:()=>B[Z],enumerable:!(D=O64(B,Z))||D.enumerable})}return A},y64=(A)=>j64(j$1({},"__esModule",{value:!0}),A),AtA={};S64(AtA,{DEFAULT_UA_APP_ID:()=>BtA,getUserAgentMiddlewareOptions:()=>ItA,getUserAgentPlugin:()=>g64,resolveUserAgentConfig:()=>DtA,userAgentMiddleware:()=>FtA});YtA.exports=y64(AtA);var k64=CB(),BtA=void 0;function QtA(A){if(A===void 0)return!0;return typeof A==="string"&&A.length<=50}TT(QtA,"isValidUserAgentAppId");function DtA(A){let B=k64.normalizeProvider(A.userAgentAppId??BtA),{customUserAgent:Q}=A;return Object.assign(A,{customUserAgent:typeof Q==="string"?[[Q]]:Q,userAgentAppId:TT(async()=>{let D=await B();if(!QtA(D)){let Z=A.logger?.constructor?.name==="NoOpLogger"||!A.logger?console:A.logger;if(typeof D!=="string")Z?.warn("userAgentAppId must be a string or undefined.");else if(D.length>50)Z?.warn("The provided userAgentAppId exceeds the maximum length of 50 characters.")}return D},"userAgentAppId")})}TT(DtA,"resolveUserAgentConfig");var _64=ma(),x64=$V(),WL=CI(),v64=/\d{12}\.ddb/;async function ZtA(A,B,Q){if(Q.request?.headers?.["smithy-protocol"]==="rpc-v2-cbor")WL.setFeature(A,"PROTOCOL_RPC_V2_CBOR","M");if(typeof B.retryStrategy==="function"){let G=await B.retryStrategy();if(typeof G.acquireInitialRetryToken==="function")if(G.constructor?.name?.includes("Adaptive"))WL.setFeature(A,"RETRY_MODE_ADAPTIVE","F");else WL.setFeature(A,"RETRY_MODE_STANDARD","E");else WL.setFeature(A,"RETRY_MODE_LEGACY","D")}if(typeof B.accountIdEndpointMode==="function"){let G=A.endpointV2;if(String(G?.url?.hostname).match(v64))WL.setFeature(A,"ACCOUNT_ID_ENDPOINT","O");switch(await B.accountIdEndpointMode?.()){case"disabled":WL.setFeature(A,"ACCOUNT_ID_MODE_DISABLED","Q");break;case"preferred":WL.setFeature(A,"ACCOUNT_ID_MODE_PREFERRED","P");break;case"required":WL.setFeature(A,"ACCOUNT_ID_MODE_REQUIRED","R");break}}let Z=A.__smithy_context?.selectedHttpAuthScheme?.identity;if(Z?.$source){let G=Z;if(G.accountId)WL.setFeature(A,"RESOLVED_ACCOUNT_ID","T");for(let[F,I]of Object.entries(G.$source??{}))WL.setFeature(A,F,I)}}TT(ZtA,"checkFeatures");var ooA="user-agent",I40="x-amz-user-agent",toA=" ",Y40="/",b64=/[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w]/g,f64=/[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w\#]/g,eoA="-",h64=1024;function GtA(A){let B="";for(let Q in A){let D=A[Q];if(B.length+D.length+1<=h64){if(B.length)B+=","+D;else B+=D;continue}break}return B}TT(GtA,"encodeFeatures");var FtA=TT((A)=>(B,Q)=>async(D)=>{let{request:Z}=D;if(!x64.HttpRequest.isInstance(Z))return B(D);let{headers:G}=Z,F=Q?.userAgent?.map(S$1)||[],I=(await A.defaultUserAgentProvider()).map(S$1);await ZtA(Q,A,D);let Y=Q;I.push(`m/${GtA(Object.assign({},Q.__smithy_context?.features,Y.__aws_sdk_context?.features))}`);let W=A?.customUserAgent?.map(S$1)||[],J=await A.userAgentAppId();if(J)I.push(S$1([`app/${J}`]));let X=_64.getUserAgentPrefix(),V=(X?[X]:[]).concat([...I,...F,...W]).join(toA),C=[...I.filter((K)=>K.startsWith("aws-sdk-")),...W].join(toA);if(A.runtime!=="browser"){if(C)G[I40]=G[I40]?`${G[ooA]} ${C}`:C;G[ooA]=V}else G[I40]=V;return B({...D,request:Z})},"userAgentMiddleware"),S$1=TT((A)=>{let B=A[0].split(Y40).map((F)=>F.replace(b64,eoA)).join(Y40),Q=A[1]?.replace(f64,eoA),D=B.indexOf(Y40),Z=B.substring(0,D),G=B.substring(D+1);if(Z==="api")G=G.toLowerCase();return[Z,G,Q].filter((F)=>F&&F.length>0).reduce((F,I,Y)=>{switch(Y){case 0:return I;case 1:return`${F}/${I}`;default:return`${F}#${I}`}},"")},"escapeUserAgent"),ItA={name:"getUserAgentMiddleware",step:"build",priority:"low",tags:["SET_USER_AGENT","USER_AGENT"],override:!0},g64=TT((A)=>({applyToStack:TT((B)=>{B.add(FtA(A),ItA)},"applyToStack")}),"getUserAgentPlugin")});
var qoA=E((woA)=>{Object.defineProperty(woA,"__esModule",{value:!0});woA.fromBase64=void 0;var tQ4=YD(),eQ4=/^[A-Za-z0-9+/]*={0,2}$/,A44=(A)=>{if(A.length*3%4!==0)throw new TypeError("Incorrect padding on base64 string.");if(!eQ4.exec(A))throw new TypeError("Invalid base64 string.");let B=tQ4.fromString(A,"base64");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength)};woA.fromBase64=A44});
var roA=E((XC5,soA)=>{var{defineProperty:P$1,getOwnPropertyDescriptor:g44,getOwnPropertyNames:u44}=Object,m44=Object.prototype.hasOwnProperty,Z8=(A,B)=>P$1(A,"name",{value:B,configurable:!0}),d44=(A,B)=>{for(var Q in B)P$1(A,Q,{get:B[Q],enumerable:!0})},c44=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of u44(B))if(!m44.call(A,Z)&&Z!==Q)P$1(A,Z,{get:()=>B[Z],enumerable:!(D=g44(B,Z))||D.enumerable})}return A},l44=(A)=>c44(P$1({},"__esModule",{value:!0}),A),goA={};d44(goA,{AwsEc2QueryProtocol:()=>w64,AwsJson1_0Protocol:()=>Z64,AwsJson1_1Protocol:()=>G64,AwsJsonRpcProtocol:()=>Z40,AwsQueryProtocol:()=>loA,AwsRestJsonProtocol:()=>I64,AwsRestXmlProtocol:()=>R64,JsonCodec:()=>D40,JsonShapeDeserializer:()=>doA,JsonShapeSerializer:()=>coA,XmlCodec:()=>aoA,XmlShapeDeserializer:()=>G40,XmlShapeSerializer:()=>noA,_toBool:()=>i44,_toNum:()=>n44,_toStr:()=>p44,awsExpectUnion:()=>W64,loadRestJsonErrorCode:()=>Q40,loadRestXmlErrorCode:()=>ioA,parseJsonBody:()=>B40,parseJsonErrorBody:()=>e44,parseXmlBody:()=>poA,parseXmlErrorBody:()=>L64});soA.exports=l44(goA);var p44=Z8((A)=>{if(A==null)return A;if(typeof A==="number"||typeof A==="bigint"){let B=new Error(`Received number ${A} where a string was expected.`);return B.name="Warning",console.warn(B),String(A)}if(typeof A==="boolean"){let B=new Error(`Received boolean ${A} where a string was expected.`);return B.name="Warning",console.warn(B),String(A)}return A},"_toStr"),i44=Z8((A)=>{if(A==null)return A;if(typeof A==="string"){let B=A.toLowerCase();if(A!==""&&B!=="false"&&B!=="true"){let Q=new Error(`Received string "${A}" where a boolean was expected.`);Q.name="Warning",console.warn(Q)}return A!==""&&B!=="false"}return A},"_toBool"),n44=Z8((A)=>{if(A==null)return A;if(typeof A==="string"){let B=Number(A);if(B.toString()!==A){let Q=new Error(`Received string "${A}" where a number was expected.`);return Q.name="Warning",console.warn(Q),A}return B}return A},"_toNum"),a44=M6(),la=_Q(),s44=PY(),Cg=class{static{Z8(this,"SerdeContextConfig")}serdeContext;setSerdeContext(A){this.serdeContext=A}},d41=_Q(),pa=X6(),r44=kk(),o44=X6();function uoA(A,B,Q){if(Q?.source){let D=Q.source;if(typeof B==="number"){if(B>Number.MAX_SAFE_INTEGER||B<Number.MIN_SAFE_INTEGER||D!==String(B))if(D.includes("."))return new o44.NumericValue(D,"bigDecimal");else return BigInt(D)}}return B}Z8(uoA,"jsonReviver");var t44=C6(),moA=Z8((A,B)=>t44.collectBody(A,B).then((Q)=>B.utf8Encoder(Q)),"collectBodyString"),B40=Z8((A,B)=>moA(A,B).then((Q)=>{if(Q.length)try{return JSON.parse(Q)}catch(D){if(D?.name==="SyntaxError")Object.defineProperty(D,"$responseBodyText",{value:Q});throw D}return{}}),"parseJsonBody"),e44=Z8(async(A,B)=>{let Q=await B40(A,B);return Q.message=Q.message??Q.Message,Q},"parseJsonErrorBody"),Q40=Z8((A,B)=>{let Q=Z8((G,F)=>Object.keys(G).find((I)=>I.toLowerCase()===F.toLowerCase()),"findKey"),D=Z8((G)=>{let F=G;if(typeof F==="number")F=F.toString();if(F.indexOf(",")>=0)F=F.split(",")[0];if(F.indexOf(":")>=0)F=F.split(":")[0];if(F.indexOf("#")>=0)F=F.split("#")[1];return F},"sanitizeErrorCode"),Z=Q(A.headers,"x-amzn-errortype");if(Z!==void 0)return D(A.headers[Z]);if(B&&typeof B==="object"){let G=Q(B,"code");if(G&&B[G]!==void 0)return D(B[G]);if(B.__type!==void 0)return D(B.__type)}},"loadRestJsonErrorCode"),doA=class extends Cg{constructor(A){super();this.settings=A}static{Z8(this,"JsonShapeDeserializer")}async read(A,B){return this._read(A,typeof B==="string"?JSON.parse(B,uoA):await B40(B,this.serdeContext))}readObject(A,B){return this._read(A,B)}_read(A,B){let Q=B!==null&&typeof B==="object",D=d41.NormalizedSchema.of(A);if(D.isListSchema()&&Array.isArray(B)){let G=D.getValueSchema(),F=[],I=!!D.getMergedTraits().sparse;for(let Y of B)if(I||Y!=null)F.push(this._read(G,Y));return F}else if(D.isMapSchema()&&Q){let G=D.getValueSchema(),F={},I=!!D.getMergedTraits().sparse;for(let[Y,W]of Object.entries(B))if(I||W!=null)F[Y]=this._read(G,W);return F}else if(D.isStructSchema()&&Q){let G={};for(let[F,I]of D.structIterator()){let Y=this.settings.jsonName?I.getMergedTraits().jsonName??F:F,W=this._read(I,B[Y]);if(W!=null)G[F]=W}return G}if(D.isBlobSchema()&&typeof B==="string")return r44.fromBase64(B);let Z=D.getMergedTraits().mediaType;if(D.isStringSchema()&&typeof B==="string"&&Z){if(Z==="application/json"||Z.endsWith("+json"))return pa.LazyJsonString.from(B)}if(D.isTimestampSchema()){let G=this.settings.timestampFormat;switch(G.useTrait?D.getSchema()===d41.SCHEMA.TIMESTAMP_DEFAULT?G.default:D.getSchema()??G.default:G.default){case d41.SCHEMA.TIMESTAMP_DATE_TIME:return pa.parseRfc3339DateTimeWithOffset(B);case d41.SCHEMA.TIMESTAMP_HTTP_DATE:return pa.parseRfc7231DateTime(B);case d41.SCHEMA.TIMESTAMP_EPOCH_SECONDS:return pa.parseEpochTimestamp(B);default:return console.warn("Missing timestamp format, parsing value with Date constructor:",B),new Date(B)}}if(D.isBigIntegerSchema()&&(typeof B==="number"||typeof B==="string"))return BigInt(B);if(D.isBigDecimalSchema()&&B!=null){if(B instanceof pa.NumericValue)return B;return new pa.NumericValue(String(B),"bigDecimal")}if(D.isNumericSchema()&&typeof B==="string")switch(B){case"Infinity":return 1/0;case"-Infinity":return-1/0;case"NaN":return NaN}return B}},ia=_Q(),A64=X6(),B64=X6(),Q64=X6(),voA=String.fromCharCode(925),D64=class{static{Z8(this,"JsonReplacer")}values=new Map;counter=0;stage=0;createReplacer(){if(this.stage===1)throw new Error("@aws-sdk/core/protocols - JsonReplacer already created.");if(this.stage===2)throw new Error("@aws-sdk/core/protocols - JsonReplacer exhausted.");return this.stage=1,(A,B)=>{if(B instanceof Q64.NumericValue){let Q=`${voA+NaN+this.counter++}_`+B.string;return this.values.set(`"${Q}"`,B.string),Q}if(typeof B==="bigint"){let Q=B.toString(),D=`${voA+"b"+this.counter++}_`+Q;return this.values.set(`"${D}"`,Q),D}return B}}replaceInJson(A){if(this.stage===0)throw new Error("@aws-sdk/core/protocols - JsonReplacer not created yet.");if(this.stage===2)throw new Error("@aws-sdk/core/protocols - JsonReplacer exhausted.");if(this.stage=2,this.counter===0)return A;for(let[B,Q]of this.values)A=A.replace(B,Q);return A}},coA=class extends Cg{constructor(A){super();this.settings=A}static{Z8(this,"JsonShapeSerializer")}buffer;rootSchema;write(A,B){this.rootSchema=ia.NormalizedSchema.of(A),this.buffer=this._write(this.rootSchema,B)}flush(){if(this.rootSchema?.isStructSchema()||this.rootSchema?.isDocumentSchema()){let A=new D64;return A.replaceInJson(JSON.stringify(this.buffer,A.createReplacer(),0))}return this.buffer}_write(A,B,Q){let D=B!==null&&typeof B==="object",Z=ia.NormalizedSchema.of(A);if(Z.isListSchema()&&Array.isArray(B)){let F=Z.getValueSchema(),I=[],Y=!!Z.getMergedTraits().sparse;for(let W of B)if(Y||W!=null)I.push(this._write(F,W));return I}else if(Z.isMapSchema()&&D){let F=Z.getValueSchema(),I={},Y=!!Z.getMergedTraits().sparse;for(let[W,J]of Object.entries(B))if(Y||J!=null)I[W]=this._write(F,J);return I}else if(Z.isStructSchema()&&D){let F={};for(let[I,Y]of Z.structIterator()){let W=this.settings.jsonName?Y.getMergedTraits().jsonName??I:I,J=this._write(Y,B[I],Z);if(J!==void 0)F[W]=J}return F}if(B===null&&Q?.isStructSchema())return;if(Z.isBlobSchema()&&(B instanceof Uint8Array||typeof B==="string")){if(Z===this.rootSchema)return B;if(!this.serdeContext?.base64Encoder)throw new Error("Missing base64Encoder in serdeContext");return this.serdeContext?.base64Encoder(B)}if(Z.isTimestampSchema()&&B instanceof Date){let F=this.settings.timestampFormat;switch(F.useTrait?Z.getSchema()===ia.SCHEMA.TIMESTAMP_DEFAULT?F.default:Z.getSchema()??F.default:F.default){case ia.SCHEMA.TIMESTAMP_DATE_TIME:return B.toISOString().replace(".000Z","Z");case ia.SCHEMA.TIMESTAMP_HTTP_DATE:return A64.dateToUtcString(B);case ia.SCHEMA.TIMESTAMP_EPOCH_SECONDS:return B.getTime()/1000;default:return console.warn("Missing timestamp format, using epoch seconds",B),B.getTime()/1000}}if(Z.isNumericSchema()&&typeof B==="number"){if(Math.abs(B)===1/0||isNaN(B))return String(B)}let G=Z.getMergedTraits().mediaType;if(Z.isStringSchema()&&typeof B==="string"&&G){if(G==="application/json"||G.endsWith("+json"))return B64.LazyJsonString.from(B)}return B}},D40=class extends Cg{constructor(A){super();this.settings=A}static{Z8(this,"JsonCodec")}createSerializer(){let A=new coA(this.settings);return A.setSerdeContext(this.serdeContext),A}createDeserializer(){let A=new doA(this.settings);return A.setSerdeContext(this.serdeContext),A}},Z40=class extends a44.RpcProtocol{static{Z8(this,"AwsJsonRpcProtocol")}serializer;deserializer;codec;constructor({defaultNamespace:A}){super({defaultNamespace:A});this.codec=new D40({timestampFormat:{useTrait:!0,default:la.SCHEMA.TIMESTAMP_EPOCH_SECONDS},jsonName:!1}),this.serializer=this.codec.createSerializer(),this.deserializer=this.codec.createDeserializer()}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q);if(!D.path.endsWith("/"))D.path+="/";if(Object.assign(D.headers,{"content-type":`application/x-amz-json-${this.getJsonRpcVersion()}`,"x-amz-target":(this.getJsonRpcVersion()==="1.0"?"JsonRpc10.":"JsonProtocol.")+la.NormalizedSchema.of(A).getName()}),la.deref(A.input)==="unit"||!D.body)D.body="{}";try{D.headers["content-length"]=String(s44.calculateBodyLength(D.body))}catch(Z){}return D}getPayloadCodec(){return this.codec}async handleError(A,B,Q,D,Z){let G=Q40(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=la.TypeRegistry.for(F),W;try{W=Y.getSchema(G)}catch(K){let H=la.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(H){let z=H.ctor;throw Object.assign(new z(I),D)}throw new Error(I)}let J=la.NormalizedSchema.of(W),X=D.message??D.Message??"Unknown",V=new W.ctor(X);await this.deserializeHttpMessage(W,B,Q,D);let C={};for(let[K,H]of J.structIterator()){let z=H.getMergedTraits().jsonName??K;C[K]=this.codec.createDeserializer().readObject(H,D[z])}throw Object.assign(V,{$metadata:Z,$response:Q,$fault:J.getMergedTraits().error,message:X,...C}),V}},Z64=class extends Z40{static{Z8(this,"AwsJson1_0Protocol")}constructor({defaultNamespace:A}){super({defaultNamespace:A})}getShapeId(){return"aws.protocols#awsJson1_0"}getJsonRpcVersion(){return"1.0"}},G64=class extends Z40{static{Z8(this,"AwsJson1_1Protocol")}constructor({defaultNamespace:A}){super({defaultNamespace:A})}getShapeId(){return"aws.protocols#awsJson1_1"}getJsonRpcVersion(){return"1.1"}},oQ0=M6(),c41=_Q(),F64=PY(),I64=class extends oQ0.HttpBindingProtocol{static{Z8(this,"AwsRestJsonProtocol")}serializer;deserializer;codec;constructor({defaultNamespace:A}){super({defaultNamespace:A});let B={timestampFormat:{useTrait:!0,default:c41.SCHEMA.TIMESTAMP_EPOCH_SECONDS},httpBindings:!0,jsonName:!0};this.codec=new D40(B),this.serializer=new oQ0.HttpInterceptingShapeSerializer(this.codec.createSerializer(),B),this.deserializer=new oQ0.HttpInterceptingShapeDeserializer(this.codec.createDeserializer(),B)}getShapeId(){return"aws.protocols#restJson1"}getPayloadCodec(){return this.codec}setSerdeContext(A){this.codec.setSerdeContext(A),super.setSerdeContext(A)}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q),Z=c41.NormalizedSchema.of(A.input),G=Z.getMemberSchemas();if(!D.headers["content-type"]){let F=Object.values(G).find((I)=>{return!!I.getMergedTraits().httpPayload});if(F){let I=F.getMergedTraits().mediaType;if(I)D.headers["content-type"]=I;else if(F.isStringSchema())D.headers["content-type"]="text/plain";else if(F.isBlobSchema())D.headers["content-type"]="application/octet-stream";else D.headers["content-type"]="application/json"}else if(!Z.isUnitSchema()){if(Object.values(G).find((Y)=>{let{httpQuery:W,httpQueryParams:J,httpHeader:X,httpLabel:V,httpPrefixHeaders:C}=Y.getMergedTraits();return!W&&!J&&!X&&!V&&C===void 0}))D.headers["content-type"]="application/json"}}if(D.headers["content-type"]&&!D.body)D.body="{}";if(D.body)try{D.headers["content-length"]=String(F64.calculateBodyLength(D.body))}catch(F){}return D}async handleError(A,B,Q,D,Z){let G=Q40(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=c41.TypeRegistry.for(F),W;try{W=Y.getSchema(G)}catch(K){let H=c41.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(H){let z=H.ctor;throw Object.assign(new z(I),D)}throw new Error(I)}let J=c41.NormalizedSchema.of(W),X=D.message??D.Message??"Unknown",V=new W.ctor(X);await this.deserializeHttpMessage(W,B,Q,D);let C={};for(let[K,H]of J.structIterator()){let z=H.getMergedTraits().jsonName??K;C[K]=this.codec.createDeserializer().readObject(H,D[z])}throw Object.assign(V,{$metadata:Z,$response:Q,$fault:J.getMergedTraits().error,message:X,...C}),V}},Y64=C6(),W64=Z8((A)=>{if(A==null)return;if(typeof A==="object"&&"__type"in A)delete A.__type;return Y64.expectUnion(A)},"awsExpectUnion"),tQ0=M6(),_k=_Q(),J64=PY(),X64=M6(),boA=_Q(),V64=C6(),C64=cB(),K64=uN(),G40=class extends Cg{constructor(A){super();this.settings=A,this.stringDeserializer=new X64.FromStringShapeDeserializer(A)}static{Z8(this,"XmlShapeDeserializer")}stringDeserializer;setSerdeContext(A){this.serdeContext=A,this.stringDeserializer.setSerdeContext(A)}read(A,B,Q){let D=boA.NormalizedSchema.of(A),Z=D.getMemberSchemas();if(D.isStructSchema()&&D.isMemberSchema()&&!!Object.values(Z).find((Y)=>{return!!Y.getMemberTraits().eventPayload})){let Y={},W=Object.keys(Z)[0];if(Z[W].isBlobSchema())Y[W]=B;else Y[W]=this.read(Z[W],B);return Y}let F=(this.serdeContext?.utf8Encoder??C64.toUtf8)(B),I=this.parseXml(F);return this.readSchema(A,Q?I[Q]:I)}readSchema(A,B){let Q=boA.NormalizedSchema.of(A),D=Q.getMergedTraits(),Z=Q.getSchema();if(Q.isListSchema()&&!Array.isArray(B))return this.readSchema(Z,[B]);if(B==null)return B;if(typeof B==="object"){let G=!!D.sparse,F=!!D.xmlFlattened;if(Q.isListSchema()){let Y=Q.getValueSchema(),W=[],J=Y.getMergedTraits().xmlName??"member",X=F?B:(B[0]??B)[J],V=Array.isArray(X)?X:[X];for(let C of V)if(C!=null||G)W.push(this.readSchema(Y,C));return W}let I={};if(Q.isMapSchema()){let Y=Q.getKeySchema(),W=Q.getValueSchema(),J;if(F)J=Array.isArray(B)?B:[B];else J=Array.isArray(B.entry)?B.entry:[B.entry];let X=Y.getMergedTraits().xmlName??"key",V=W.getMergedTraits().xmlName??"value";for(let C of J){let K=C[X],H=C[V];if(H!=null||G)I[K]=this.readSchema(W,H)}return I}if(Q.isStructSchema()){for(let[Y,W]of Q.structIterator()){let J=W.getMergedTraits(),X=!J.httpPayload?W.getMemberTraits().xmlName??Y:J.xmlName??W.getName();if(B[X]!=null)I[Y]=this.readSchema(W,B[X])}return I}if(Q.isDocumentSchema())return B;throw new Error(`@aws-sdk/core/protocols - xml deserializer unhandled schema type for ${Q.getName(!0)}`)}else{if(Q.isListSchema())return[];else if(Q.isMapSchema()||Q.isStructSchema())return{};return this.stringDeserializer.read(Q,B)}}parseXml(A){if(A.length){let B=new K64.XMLParser({attributeNamePrefix:"",htmlEntities:!0,ignoreAttributes:!1,ignoreDeclaration:!0,parseTagValue:!1,trimValues:!1,tagValueProcessor:Z8((F,I)=>I.trim()===""&&I.includes(`
`)?"":void 0,"tagValueProcessor")});B.addEntity("#xD","\r"),B.addEntity("#10",`
`);let Q;try{Q=B.parse(A,!0)}catch(F){if(F&&typeof F==="object")Object.defineProperty(F,"$responseBodyText",{value:A});throw F}let D="#text",Z=Object.keys(Q)[0],G=Q[Z];if(G[D])G[Z]=G[D],delete G[D];return V64.getValueFromTextNode(G)}return{}}},eQ0=M6(),T$1=_Q(),H64=X6(),z64=C6(),E64=kk(),U64=class extends Cg{constructor(A){super();this.settings=A}static{Z8(this,"QueryShapeSerializer")}buffer;write(A,B,Q=""){if(this.buffer===void 0)this.buffer="";let D=T$1.NormalizedSchema.of(A);if(Q&&!Q.endsWith("."))Q+=".";if(D.isBlobSchema()){if(typeof B==="string"||B instanceof Uint8Array)this.writeKey(Q),this.writeValue((this.serdeContext?.base64Encoder??E64.toBase64)(B))}else if(D.isBooleanSchema()||D.isNumericSchema()||D.isStringSchema()){if(B!=null)this.writeKey(Q),this.writeValue(String(B))}else if(D.isBigIntegerSchema()){if(B!=null)this.writeKey(Q),this.writeValue(String(B))}else if(D.isBigDecimalSchema()){if(B!=null)this.writeKey(Q),this.writeValue(B instanceof H64.NumericValue?B.string:String(B))}else if(D.isTimestampSchema()){if(B instanceof Date)switch(this.writeKey(Q),eQ0.determineTimestampFormat(D,this.settings)){case T$1.SCHEMA.TIMESTAMP_DATE_TIME:this.writeValue(B.toISOString().replace(".000Z","Z"));break;case T$1.SCHEMA.TIMESTAMP_HTTP_DATE:this.writeValue(z64.dateToUtcString(B));break;case T$1.SCHEMA.TIMESTAMP_EPOCH_SECONDS:this.writeValue(String(B.getTime()/1000));break}}else if(D.isDocumentSchema())throw new Error(`@aws-sdk/core/protocols - QuerySerializer unsupported document type ${D.getName(!0)}`);else if(D.isListSchema()){if(Array.isArray(B))if(B.length===0){if(this.settings.serializeEmptyLists)this.writeKey(Q),this.writeValue("")}else{let Z=D.getValueSchema(),G=this.settings.flattenLists||D.getMergedTraits().xmlFlattened,F=1;for(let I of B){if(I==null)continue;let Y=this.getKey("member",Z.getMergedTraits().xmlName),W=G?`${Q}${F}`:`${Q}${Y}.${F}`;this.write(Z,I,W),++F}}}else if(D.isMapSchema()){if(B&&typeof B==="object"){let Z=D.getKeySchema(),G=D.getValueSchema(),F=D.getMergedTraits().xmlFlattened,I=1;for(let[Y,W]of Object.entries(B)){if(W==null)continue;let J=this.getKey("key",Z.getMergedTraits().xmlName),X=F?`${Q}${I}.${J}`:`${Q}entry.${I}.${J}`,V=this.getKey("value",G.getMergedTraits().xmlName),C=F?`${Q}${I}.${V}`:`${Q}entry.${I}.${V}`;this.write(Z,Y,X),this.write(G,W,C),++I}}}else if(D.isStructSchema()){if(B&&typeof B==="object")for(let[Z,G]of D.structIterator()){if(B[Z]==null)continue;let F=this.getKey(Z,G.getMergedTraits().xmlName),I=`${Q}${F}`;this.write(G,B[Z],I)}}else if(D.isUnitSchema());else throw new Error(`@aws-sdk/core/protocols - QuerySerializer unrecognized schema type ${D.getName(!0)}`)}flush(){if(this.buffer===void 0)throw new Error("@aws-sdk/core/protocols - QuerySerializer cannot flush with nothing written to buffer.");let A=this.buffer;return delete this.buffer,A}getKey(A,B){let Q=B??A;if(this.settings.capitalizeKeys)return Q[0].toUpperCase()+Q.slice(1);return Q}writeKey(A){if(A.endsWith("."))A=A.slice(0,A.length-1);this.buffer+=`&${eQ0.extendedEncodeURIComponent(A)}=`}writeValue(A){this.buffer+=eQ0.extendedEncodeURIComponent(A)}},loA=class extends tQ0.RpcProtocol{constructor(A){super({defaultNamespace:A.defaultNamespace});this.options=A;let B={timestampFormat:{useTrait:!0,default:_k.SCHEMA.TIMESTAMP_DATE_TIME},httpBindings:!1,xmlNamespace:A.xmlNamespace,serviceNamespace:A.defaultNamespace,serializeEmptyLists:!0};this.serializer=new U64(B),this.deserializer=new G40(B)}static{Z8(this,"AwsQueryProtocol")}serializer;deserializer;getShapeId(){return"aws.protocols#awsQuery"}setSerdeContext(A){this.serializer.setSerdeContext(A),this.deserializer.setSerdeContext(A)}getPayloadCodec(){throw new Error("AWSQuery protocol has no payload codec.")}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q);if(!D.path.endsWith("/"))D.path+="/";if(Object.assign(D.headers,{"content-type":"application/x-www-form-urlencoded"}),_k.deref(A.input)==="unit"||!D.body)D.body="";if(D.body=`Action=${A.name.split("#")[1]}&Version=${this.options.version}`+D.body,D.body.endsWith("&"))D.body=D.body.slice(-1);try{D.headers["content-length"]=String(J64.calculateBodyLength(D.body))}catch(Z){}return D}async deserializeResponse(A,B,Q){let D=this.deserializer,Z=_k.NormalizedSchema.of(A.output),G={};if(Q.statusCode>=300){let W=await tQ0.collectBody(Q.body,B);if(W.byteLength>0)Object.assign(G,await D.read(_k.SCHEMA.DOCUMENT,W));await this.handleError(A,B,Q,G,this.deserializeMetadata(Q))}for(let W in Q.headers){let J=Q.headers[W];delete Q.headers[W],Q.headers[W.toLowerCase()]=J}let F=Z.isStructSchema()&&this.useNestedResult()?A.name.split("#")[1]+"Result":void 0,I=await tQ0.collectBody(Q.body,B);if(I.byteLength>0)Object.assign(G,await D.read(Z,I,F));return{$metadata:this.deserializeMetadata(Q),...G}}useNestedResult(){return!0}async handleError(A,B,Q,D,Z){let G=this.loadQueryErrorCode(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=this.loadQueryError(D),W=_k.TypeRegistry.for(F),J;try{if(J=W.find((H)=>_k.NormalizedSchema.of(H).getMergedTraits().awsQueryError?.[0]===I),!J)J=W.getSchema(G)}catch(H){let z=_k.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(z){let $=z.ctor;throw Object.assign(new $(I),Y)}throw new Error(I)}let X=_k.NormalizedSchema.of(J),V=this.loadQueryErrorMessage(D),C=new J.ctor(V),K={};for(let[H,z]of X.structIterator()){let $=z.getMergedTraits().xmlName??H,L=Y[$]??D[$];K[H]=this.deserializer.readSchema(z,L)}throw Object.assign(C,{$metadata:Z,$response:Q,$fault:X.getMergedTraits().error,message:V,...K}),C}loadQueryErrorCode(A,B){let Q=(B.Errors?.[0]?.Error??B.Errors?.Error??B.Error)?.Code;if(Q!==void 0)return Q;if(A.statusCode==404)return"NotFound"}loadQueryError(A){return A.Errors?.[0]?.Error??A.Errors?.Error??A.Error}loadQueryErrorMessage(A){let B=this.loadQueryError(A);return B?.message??B?.Message??A.message??A.Message??"Unknown"}},w64=class extends loA{constructor(A){super(A);this.options=A;let B={capitalizeKeys:!0,flattenLists:!0,serializeEmptyLists:!1};Object.assign(this.serializer.settings,B)}static{Z8(this,"AwsEc2QueryProtocol")}useNestedResult(){return!1}},A40=M6(),l41=_Q(),$64=PY(),q64=C6(),N64=uN(),poA=Z8((A,B)=>moA(A,B).then((Q)=>{if(Q.length){let D=new N64.XMLParser({attributeNamePrefix:"",htmlEntities:!0,ignoreAttributes:!1,ignoreDeclaration:!0,parseTagValue:!1,trimValues:!1,tagValueProcessor:Z8((Y,W)=>W.trim()===""&&W.includes(`
`)?"":void 0,"tagValueProcessor")});D.addEntity("#xD","\r"),D.addEntity("#10",`
`);let Z;try{Z=D.parse(Q,!0)}catch(Y){if(Y&&typeof Y==="object")Object.defineProperty(Y,"$responseBodyText",{value:Q});throw Y}let G="#text",F=Object.keys(Z)[0],I=Z[F];if(I[G])I[F]=I[G],delete I[G];return q64.getValueFromTextNode(I)}return{}}),"parseXmlBody"),L64=Z8(async(A,B)=>{let Q=await poA(A,B);if(Q.Error)Q.Error.message=Q.Error.message??Q.Error.Message;return Q},"parseXmlErrorBody"),ioA=Z8((A,B)=>{if(B?.Error?.Code!==void 0)return B.Error.Code;if(B?.Code!==void 0)return B.Code;if(A.statusCode==404)return"NotFound"},"loadRestXmlErrorCode"),YL=HQ1(),Vg=_Q(),M64=X6(),foA=C6(),hoA=kk(),noA=class extends Cg{constructor(A){super();this.settings=A}static{Z8(this,"XmlShapeSerializer")}stringBuffer;byteBuffer;buffer;write(A,B){let Q=Vg.NormalizedSchema.of(A);if(Q.isStringSchema()&&typeof B==="string")this.stringBuffer=B;else if(Q.isBlobSchema())this.byteBuffer="byteLength"in B?B:(this.serdeContext?.base64Decoder??hoA.fromBase64)(B);else{this.buffer=this.writeStruct(Q,B,void 0);let D=Q.getMergedTraits();if(D.httpPayload&&!D.xmlName)this.buffer.withName(Q.getName())}}flush(){if(this.byteBuffer!==void 0){let B=this.byteBuffer;return delete this.byteBuffer,B}if(this.stringBuffer!==void 0){let B=this.stringBuffer;return delete this.stringBuffer,B}let A=this.buffer;if(this.settings.xmlNamespace){if(!A?.attributes?.xmlns)A.addAttribute("xmlns",this.settings.xmlNamespace)}return delete this.buffer,A.toString()}writeStruct(A,B,Q){let D=A.getMergedTraits(),Z=A.isMemberSchema()&&!D.httpPayload?A.getMemberTraits().xmlName??A.getMemberName():D.xmlName??A.getName();if(!Z||!A.isStructSchema())throw new Error(`@aws-sdk/core/protocols - xml serializer, cannot write struct with empty name or non-struct, schema=${A.getName(!0)}.`);let G=YL.XmlNode.of(Z),[F,I]=this.getXmlnsAttribute(A,Q);if(I)G.addAttribute(F,I);for(let[Y,W]of A.structIterator()){let J=B[Y];if(J!=null){if(W.getMergedTraits().xmlAttribute){G.addAttribute(W.getMergedTraits().xmlName??Y,this.writeSimple(W,J));continue}if(W.isListSchema())this.writeList(W,J,G,I);else if(W.isMapSchema())this.writeMap(W,J,G,I);else if(W.isStructSchema())G.addChildNode(this.writeStruct(W,J,I));else{let X=YL.XmlNode.of(W.getMergedTraits().xmlName??W.getMemberName());this.writeSimpleInto(W,J,X,I),G.addChildNode(X)}}}return G}writeList(A,B,Q,D){if(!A.isMemberSchema())throw new Error(`@aws-sdk/core/protocols - xml serializer, cannot write non-member list: ${A.getName(!0)}`);let Z=A.getMergedTraits(),G=A.getValueSchema(),F=G.getMergedTraits(),I=!!F.sparse,Y=!!Z.xmlFlattened,[W,J]=this.getXmlnsAttribute(A,D),X=Z8((V,C)=>{if(G.isListSchema())this.writeList(G,Array.isArray(C)?C:[C],V,J);else if(G.isMapSchema())this.writeMap(G,C,V,J);else if(G.isStructSchema()){let K=this.writeStruct(G,C,J);V.addChildNode(K.withName(Y?Z.xmlName??A.getMemberName():F.xmlName??"member"))}else{let K=YL.XmlNode.of(Y?Z.xmlName??A.getMemberName():F.xmlName??"member");this.writeSimpleInto(G,C,K,J),V.addChildNode(K)}},"writeItem");if(Y){for(let V of B)if(I||V!=null)X(Q,V)}else{let V=YL.XmlNode.of(Z.xmlName??A.getMemberName());if(J)V.addAttribute(W,J);for(let C of B)if(I||C!=null)X(V,C);Q.addChildNode(V)}}writeMap(A,B,Q,D,Z=!1){if(!A.isMemberSchema())throw new Error(`@aws-sdk/core/protocols - xml serializer, cannot write non-member map: ${A.getName(!0)}`);let G=A.getMergedTraits(),F=A.getKeySchema(),Y=F.getMergedTraits().xmlName??"key",W=A.getValueSchema(),J=W.getMergedTraits(),X=J.xmlName??"value",V=!!J.sparse,C=!!G.xmlFlattened,[K,H]=this.getXmlnsAttribute(A,D),z=Z8(($,L,N)=>{let O=YL.XmlNode.of(Y,L),[R,T]=this.getXmlnsAttribute(F,H);if(T)O.addAttribute(R,T);$.addChildNode(O);let j=YL.XmlNode.of(X);if(W.isListSchema())this.writeList(W,N,j,H);else if(W.isMapSchema())this.writeMap(W,N,j,H,!0);else if(W.isStructSchema())j=this.writeStruct(W,N,H);else this.writeSimpleInto(W,N,j,H);$.addChildNode(j)},"addKeyValue");if(C){for(let[$,L]of Object.entries(B))if(V||L!=null){let N=YL.XmlNode.of(G.xmlName??A.getMemberName());z(N,$,L),Q.addChildNode(N)}}else{let $;if(!Z){if($=YL.XmlNode.of(G.xmlName??A.getMemberName()),H)$.addAttribute(K,H);Q.addChildNode($)}for(let[L,N]of Object.entries(B))if(V||N!=null){let O=YL.XmlNode.of("entry");z(O,L,N),(Z?Q:$).addChildNode(O)}}}writeSimple(A,B){if(B===null)throw new Error("@aws-sdk/core/protocols - (XML serializer) cannot write null value.");let Q=Vg.NormalizedSchema.of(A),D=null;if(B&&typeof B==="object")if(Q.isBlobSchema())D=(this.serdeContext?.base64Encoder??hoA.toBase64)(B);else if(Q.isTimestampSchema()&&B instanceof Date){let Z=this.settings.timestampFormat;switch(Z.useTrait?Q.getSchema()===Vg.SCHEMA.TIMESTAMP_DEFAULT?Z.default:Q.getSchema()??Z.default:Z.default){case Vg.SCHEMA.TIMESTAMP_DATE_TIME:D=B.toISOString().replace(".000Z","Z");break;case Vg.SCHEMA.TIMESTAMP_HTTP_DATE:D=foA.dateToUtcString(B);break;case Vg.SCHEMA.TIMESTAMP_EPOCH_SECONDS:D=String(B.getTime()/1000);break;default:console.warn("Missing timestamp format, using http date",B),D=foA.dateToUtcString(B);break}}else if(Q.isBigDecimalSchema()&&B){if(B instanceof M64.NumericValue)return B.string;return String(B)}else if(Q.isMapSchema()||Q.isListSchema())throw new Error("@aws-sdk/core/protocols - xml serializer, cannot call _write() on List/Map schema, call writeList or writeMap() instead.");else throw new Error(`@aws-sdk/core/protocols - xml serializer, unhandled schema type for object value and schema: ${Q.getName(!0)}`);if(Q.isStringSchema()||Q.isBooleanSchema()||Q.isNumericSchema()||Q.isBigIntegerSchema()||Q.isBigDecimalSchema())D=String(B);if(D===null)throw new Error(`Unhandled schema-value pair ${Q.getName(!0)}=${B}`);return D}writeSimpleInto(A,B,Q,D){let Z=this.writeSimple(A,B),G=Vg.NormalizedSchema.of(A),F=new YL.XmlText(Z),[I,Y]=this.getXmlnsAttribute(G,D);if(Y)Q.addAttribute(I,Y);Q.addChildNode(F)}getXmlnsAttribute(A,B){let Q=A.getMergedTraits(),[D,Z]=Q.xmlNamespace??[];if(Z&&Z!==B)return[D?`xmlns:${D}`:"xmlns",Z];return[void 0,void 0]}},aoA=class extends Cg{constructor(A){super();this.settings=A}static{Z8(this,"XmlCodec")}createSerializer(){let A=new noA(this.settings);return A.setSerdeContext(this.serdeContext),A}createDeserializer(){let A=new G40(this.settings);return A.setSerdeContext(this.serdeContext),A}},R64=class extends A40.HttpBindingProtocol{static{Z8(this,"AwsRestXmlProtocol")}codec;serializer;deserializer;constructor(A){super(A);let B={timestampFormat:{useTrait:!0,default:l41.SCHEMA.TIMESTAMP_DATE_TIME},httpBindings:!0,xmlNamespace:A.xmlNamespace,serviceNamespace:A.defaultNamespace};this.codec=new aoA(B),this.serializer=new A40.HttpInterceptingShapeSerializer(this.codec.createSerializer(),B),this.deserializer=new A40.HttpInterceptingShapeDeserializer(this.codec.createDeserializer(),B)}getPayloadCodec(){return this.codec}getShapeId(){return"aws.protocols#restXml"}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q),Z=l41.NormalizedSchema.of(A.input),G=Z.getMemberSchemas();if(D.path=String(D.path).split("/").filter((F)=>{return F!=="{Bucket}"}).join("/")||"/",!D.headers["content-type"]){let F=Object.values(G).find((I)=>{return!!I.getMergedTraits().httpPayload});if(F){let I=F.getMergedTraits().mediaType;if(I)D.headers["content-type"]=I;else if(F.isStringSchema())D.headers["content-type"]="text/plain";else if(F.isBlobSchema())D.headers["content-type"]="application/octet-stream";else D.headers["content-type"]="application/xml"}else if(!Z.isUnitSchema()){if(Object.values(G).find((Y)=>{let{httpQuery:W,httpQueryParams:J,httpHeader:X,httpLabel:V,httpPrefixHeaders:C}=Y.getMergedTraits();return!W&&!J&&!X&&!V&&C===void 0}))D.headers["content-type"]="application/xml"}}if(D.headers["content-type"]==="application/xml"){if(typeof D.body==="string")D.body='<?xml version="1.0" encoding="UTF-8"?>'+D.body}if(D.body)try{D.headers["content-length"]=String($64.calculateBodyLength(D.body))}catch(F){}return D}async deserializeResponse(A,B,Q){return super.deserializeResponse(A,B,Q)}async handleError(A,B,Q,D,Z){let G=ioA(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=l41.TypeRegistry.for(F),W;try{W=Y.getSchema(G)}catch(K){let H=l41.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(H){let z=H.ctor;throw Object.assign(new z(I),D)}throw new Error(I)}let J=l41.NormalizedSchema.of(W),X=D.Error?.message??D.Error?.Message??D.message??D.Message??"Unknown",V=new W.ctor(X);await this.deserializeHttpMessage(W,B,Q,D);let C={};for(let[K,H]of J.structIterator()){let z=H.getMergedTraits().xmlName??K,$=D.Error?.[z]??D[z];C[K]=this.codec.createDeserializer().readSchema(H,$)}throw Object.assign(V,{$metadata:Z,$response:Q,$fault:J.getMergedTraits().error,message:X,...C}),V}}});
var s22=E((n22)=>{Object.defineProperty(n22,"__esModule",{value:!0});n22.defaultEndpointResolver=void 0;var nG4=ma(),x60=S7(),aG4=i22(),sG4=new x60.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),rG4=(A,B={})=>{return sG4.get(A,()=>x60.resolveEndpoint(aG4.ruleSet,{endpointParams:A,logger:B.logger}))};n22.defaultEndpointResolver=rG4;x60.customEndpointFunctions.aws=nG4.awsEndpointFunctions});
var t02=E((r02)=>{Object.defineProperty(r02,"__esModule",{value:!0});r02.ruleSet=void 0;var u02="required",w4="type",i8="fn",n8="argv",bk="ref",S02=!1,g40=!0,vk="booleanEquals",cY="stringEquals",m02="sigv4",d02="sts",c02="us-east-1",UD="endpoint",j02="https://sts.{Region}.{PartitionResult#dnsSuffix}",XL="tree",Qs="error",m40="getAttr",y02={[u02]:!1,[w4]:"String"},u40={[u02]:!0,default:!1,[w4]:"Boolean"},l02={[bk]:"Endpoint"},k02={[i8]:"isSet",[n8]:[{[bk]:"Region"}]},lY={[bk]:"Region"},_02={[i8]:"aws.partition",[n8]:[lY],assign:"PartitionResult"},p02={[bk]:"UseFIPS"},i02={[bk]:"UseDualStack"},aW={url:"https://sts.amazonaws.com",properties:{authSchemes:[{name:m02,signingName:d02,signingRegion:c02}]},headers:{}},zK={},x02={conditions:[{[i8]:cY,[n8]:[lY,"aws-global"]}],[UD]:aW,[w4]:UD},n02={[i8]:vk,[n8]:[p02,!0]},a02={[i8]:vk,[n8]:[i02,!0]},v02={[i8]:m40,[n8]:[{[bk]:"PartitionResult"},"supportsFIPS"]},s02={[bk]:"PartitionResult"},b02={[i8]:vk,[n8]:[!0,{[i8]:m40,[n8]:[s02,"supportsDualStack"]}]},f02=[{[i8]:"isSet",[n8]:[l02]}],h02=[n02],g02=[a02],YD4={version:"1.0",parameters:{Region:y02,UseDualStack:u40,UseFIPS:u40,Endpoint:y02,UseGlobalEndpoint:u40},rules:[{conditions:[{[i8]:vk,[n8]:[{[bk]:"UseGlobalEndpoint"},g40]},{[i8]:"not",[n8]:f02},k02,_02,{[i8]:vk,[n8]:[p02,S02]},{[i8]:vk,[n8]:[i02,S02]}],rules:[{conditions:[{[i8]:cY,[n8]:[lY,"ap-northeast-1"]}],endpoint:aW,[w4]:UD},{conditions:[{[i8]:cY,[n8]:[lY,"ap-south-1"]}],endpoint:aW,[w4]:UD},{conditions:[{[i8]:cY,[n8]:[lY,"ap-southeast-1"]}],endpoint:aW,[w4]:UD},{conditions:[{[i8]:cY,[n8]:[lY,"ap-southeast-2"]}],endpoint:aW,[w4]:UD},x02,{conditions:[{[i8]:cY,[n8]:[lY,"ca-central-1"]}],endpoint:aW,[w4]:UD},{conditions:[{[i8]:cY,[n8]:[lY,"eu-central-1"]}],endpoint:aW,[w4]:UD},{conditions:[{[i8]:cY,[n8]:[lY,"eu-north-1"]}],endpoint:aW,[w4]:UD},{conditions:[{[i8]:cY,[n8]:[lY,"eu-west-1"]}],endpoint:aW,[w4]:UD},{conditions:[{[i8]:cY,[n8]:[lY,"eu-west-2"]}],endpoint:aW,[w4]:UD},{conditions:[{[i8]:cY,[n8]:[lY,"eu-west-3"]}],endpoint:aW,[w4]:UD},{conditions:[{[i8]:cY,[n8]:[lY,"sa-east-1"]}],endpoint:aW,[w4]:UD},{conditions:[{[i8]:cY,[n8]:[lY,c02]}],endpoint:aW,[w4]:UD},{conditions:[{[i8]:cY,[n8]:[lY,"us-east-2"]}],endpoint:aW,[w4]:UD},{conditions:[{[i8]:cY,[n8]:[lY,"us-west-1"]}],endpoint:aW,[w4]:UD},{conditions:[{[i8]:cY,[n8]:[lY,"us-west-2"]}],endpoint:aW,[w4]:UD},{endpoint:{url:j02,properties:{authSchemes:[{name:m02,signingName:d02,signingRegion:"{Region}"}]},headers:zK},[w4]:UD}],[w4]:XL},{conditions:f02,rules:[{conditions:h02,error:"Invalid Configuration: FIPS and custom endpoint are not supported",[w4]:Qs},{conditions:g02,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",[w4]:Qs},{endpoint:{url:l02,properties:zK,headers:zK},[w4]:UD}],[w4]:XL},{conditions:[k02],rules:[{conditions:[_02],rules:[{conditions:[n02,a02],rules:[{conditions:[{[i8]:vk,[n8]:[g40,v02]},b02],rules:[{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:zK,headers:zK},[w4]:UD}],[w4]:XL},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",[w4]:Qs}],[w4]:XL},{conditions:h02,rules:[{conditions:[{[i8]:vk,[n8]:[v02,g40]}],rules:[{conditions:[{[i8]:cY,[n8]:[{[i8]:m40,[n8]:[s02,"name"]},"aws-us-gov"]}],endpoint:{url:"https://sts.{Region}.amazonaws.com",properties:zK,headers:zK},[w4]:UD},{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dnsSuffix}",properties:zK,headers:zK},[w4]:UD}],[w4]:XL},{error:"FIPS is enabled but this partition does not support FIPS",[w4]:Qs}],[w4]:XL},{conditions:g02,rules:[{conditions:[b02],rules:[{endpoint:{url:"https://sts.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:zK,headers:zK},[w4]:UD}],[w4]:XL},{error:"DualStack is enabled but this partition does not support DualStack",[w4]:Qs}],[w4]:XL},x02,{endpoint:{url:j02,properties:zK,headers:zK},[w4]:UD}],[w4]:XL}],[w4]:XL},{error:"Invalid Configuration: Missing Region",[w4]:Qs}]};r02.ruleSet=YD4});
var u41=E((cV5,vsA)=>{var{defineProperty:Y$1,getOwnPropertyDescriptor:L94,getOwnPropertyNames:M94}=Object,R94=Object.prototype.hasOwnProperty,I$1=(A,B)=>Y$1(A,"name",{value:B,configurable:!0}),O94=(A,B)=>{for(var Q in B)Y$1(A,Q,{get:B[Q],enumerable:!0})},T94=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of M94(B))if(!R94.call(A,Z)&&Z!==Q)Y$1(A,Z,{get:()=>B[Z],enumerable:!(D=L94(B,Z))||D.enumerable})}return A},P94=(A)=>T94(Y$1({},"__esModule",{value:!0}),A),ksA={};O94(ksA,{addRecursionDetectionMiddlewareOptions:()=>xsA,getRecursionDetectionPlugin:()=>k94,recursionDetectionMiddleware:()=>_sA});vsA.exports=P94(ksA);var S94=$V(),RQ0="X-Amzn-Trace-Id",j94="AWS_LAMBDA_FUNCTION_NAME",y94="_X_AMZN_TRACE_ID",_sA=I$1((A)=>(B)=>async(Q)=>{let{request:D}=Q;if(!S94.HttpRequest.isInstance(D)||A.runtime!=="node")return B(Q);let Z=Object.keys(D.headers??{}).find((Y)=>Y.toLowerCase()===RQ0.toLowerCase())??RQ0;if(D.headers.hasOwnProperty(Z))return B(Q);let G=process.env[j94],F=process.env[y94],I=I$1((Y)=>typeof Y==="string"&&Y.length>0,"nonEmptyString");if(I(G)&&I(F))D.headers[RQ0]=F;return B({...Q,request:D})},"recursionDetectionMiddleware"),xsA={step:"build",tags:["RECURSION_DETECTION"],name:"recursionDetectionMiddleware",override:!0,priority:"low"},k94=I$1((A)=>({applyToStack:I$1((B)=>{B.add(_sA(A),xsA)},"applyToStack")}),"getRecursionDetectionPlugin")});
var vtA=E((gC5,g84)=>{g84.exports={name:"@aws-sdk/client-sso",description:"AWS SDK for JavaScript Sso Client for Node.js, Browser and React Native",version:"3.840.0",scripts:{build:"concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'","build:cjs":"node ../../scripts/compilation/inline client-sso","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4",clean:"rimraf ./dist-* && rimraf *.tsbuildinfo","extract:docs":"api-extractor run --local","generate:client":"node ../../scripts/generate-clients/single-service --solo sso"},main:"./dist-cjs/index.js",types:"./dist-types/index.d.ts",module:"./dist-es/index.js",sideEffects:!1,dependencies:{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.840.0","@aws-sdk/middleware-host-header":"3.840.0","@aws-sdk/middleware-logger":"3.840.0","@aws-sdk/middleware-recursion-detection":"3.840.0","@aws-sdk/middleware-user-agent":"3.840.0","@aws-sdk/region-config-resolver":"3.840.0","@aws-sdk/types":"3.840.0","@aws-sdk/util-endpoints":"3.840.0","@aws-sdk/util-user-agent-browser":"3.840.0","@aws-sdk/util-user-agent-node":"3.840.0","@smithy/config-resolver":"^4.1.4","@smithy/core":"^3.6.0","@smithy/fetch-http-handler":"^5.0.4","@smithy/hash-node":"^4.0.4","@smithy/invalid-dependency":"^4.0.4","@smithy/middleware-content-length":"^4.0.4","@smithy/middleware-endpoint":"^4.1.13","@smithy/middleware-retry":"^4.1.14","@smithy/middleware-serde":"^4.0.8","@smithy/middleware-stack":"^4.0.4","@smithy/node-config-provider":"^4.1.3","@smithy/node-http-handler":"^4.0.6","@smithy/protocol-http":"^5.1.2","@smithy/smithy-client":"^4.4.5","@smithy/types":"^4.3.1","@smithy/url-parser":"^4.0.4","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.21","@smithy/util-defaults-mode-node":"^4.0.21","@smithy/util-endpoints":"^3.0.6","@smithy/util-middleware":"^4.0.4","@smithy/util-retry":"^4.0.6","@smithy/util-utf8":"^4.0.0",tslib:"^2.6.2"},devDependencies:{"@tsconfig/node18":"18.2.4","@types/node":"^18.19.69",concurrently:"7.0.0","downlevel-dts":"0.10.1",rimraf:"3.0.2",typescript:"~5.8.3"},engines:{node:">=18.0.0"},typesVersions:{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},files:["dist-*/**"],author:{name:"AWS SDK for JavaScript Team",url:"https://aws.amazon.com/javascript/"},license:"Apache-2.0",browser:{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.browser"},"react-native":{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.native"},homepage:"https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-sso",repository:{type:"git",url:"https://github.com/aws/aws-sdk-js-v3.git",directory:"clients/client-sso"}}});
var w12=E((E12)=>{Object.defineProperty(E12,"__esModule",{value:!0});E12.ruleSet=void 0;var C12="required",gz="fn",uz="argv",As="ref",D12=!0,Z12="isSet",o41="booleanEquals",ta="error",ea="endpoint",jT="tree",T40="PartitionResult",P40="getAttr",G12={[C12]:!1,type:"String"},F12={[C12]:!0,default:!1,type:"Boolean"},I12={[As]:"Endpoint"},K12={[gz]:o41,[uz]:[{[As]:"UseFIPS"},!0]},H12={[gz]:o41,[uz]:[{[As]:"UseDualStack"},!0]},hz={},Y12={[gz]:P40,[uz]:[{[As]:T40},"supportsFIPS"]},z12={[As]:T40},W12={[gz]:o41,[uz]:[!0,{[gz]:P40,[uz]:[z12,"supportsDualStack"]}]},J12=[K12],X12=[H12],V12=[{[As]:"Region"}],U34={version:"1.0",parameters:{Region:G12,UseDualStack:F12,UseFIPS:F12,Endpoint:G12},rules:[{conditions:[{[gz]:Z12,[uz]:[I12]}],rules:[{conditions:J12,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:ta},{conditions:X12,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:ta},{endpoint:{url:I12,properties:hz,headers:hz},type:ea}],type:jT},{conditions:[{[gz]:Z12,[uz]:V12}],rules:[{conditions:[{[gz]:"aws.partition",[uz]:V12,assign:T40}],rules:[{conditions:[K12,H12],rules:[{conditions:[{[gz]:o41,[uz]:[D12,Y12]},W12],rules:[{endpoint:{url:"https://oidc-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:hz,headers:hz},type:ea}],type:jT},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:ta}],type:jT},{conditions:J12,rules:[{conditions:[{[gz]:o41,[uz]:[Y12,D12]}],rules:[{conditions:[{[gz]:"stringEquals",[uz]:[{[gz]:P40,[uz]:[z12,"name"]},"aws-us-gov"]}],endpoint:{url:"https://oidc.{Region}.amazonaws.com",properties:hz,headers:hz},type:ea},{endpoint:{url:"https://oidc-fips.{Region}.{PartitionResult#dnsSuffix}",properties:hz,headers:hz},type:ea}],type:jT},{error:"FIPS is enabled but this partition does not support FIPS",type:ta}],type:jT},{conditions:X12,rules:[{conditions:[W12],rules:[{endpoint:{url:"https://oidc.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:hz,headers:hz},type:ea}],type:jT},{error:"DualStack is enabled but this partition does not support DualStack",type:ta}],type:jT},{endpoint:{url:"https://oidc.{Region}.{PartitionResult#dnsSuffix}",properties:hz,headers:hz},type:ea}],type:jT}],type:jT},{error:"Invalid Configuration: Missing Region",type:ta}]};E12.ruleSet=U34});
var x12=E((k12)=>{Object.defineProperty(k12,"__esModule",{value:!0});k12.getRuntimeConfig=void 0;var j34=Jg(),y34=j34.__importDefault(O40()),P12=CI(),S12=i41(),l$1=K4(),k34=gG(),j12=u4(),Hg=JD(),y12=k3(),_34=uG(),x34=sZ(),v34=T12(),b34=C6(),f34=mG(),h34=C6(),g34=(A)=>{h34.emitWarningIfUnsupportedVersion(process.version);let B=f34.resolveDefaultsModeConfig(A),Q=()=>B().then(b34.loadConfigsForDefaultMode),D=v34.getRuntimeConfig(A);P12.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile,logger:D.logger};return{...D,...A,runtime:"node",defaultsMode:B,authSchemePreference:A?.authSchemePreference??Hg.loadConfig(P12.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS,Z),bodyLengthChecker:A?.bodyLengthChecker??_34.calculateBodyLength,defaultUserAgentProvider:A?.defaultUserAgentProvider??S12.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:y34.default.version}),maxAttempts:A?.maxAttempts??Hg.loadConfig(j12.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??Hg.loadConfig(l$1.NODE_REGION_CONFIG_OPTIONS,{...l$1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:y12.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??Hg.loadConfig({...j12.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||x34.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??k34.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??y12.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??Hg.loadConfig(l$1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??Hg.loadConfig(l$1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??Hg.loadConfig(S12.NODE_APP_ID_CONFIG_OPTIONS,Z)}};k12.getRuntimeConfig=g34});
var x40=E((WK5,K02)=>{var{create:M74,defineProperty:e41,getOwnPropertyDescriptor:R74,getOwnPropertyNames:O74,getPrototypeOf:T74}=Object,P74=Object.prototype.hasOwnProperty,yT=(A,B)=>e41(A,"name",{value:B,configurable:!0}),S74=(A,B)=>{for(var Q in B)e41(A,Q,{get:B[Q],enumerable:!0})},J02=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of O74(B))if(!P74.call(A,Z)&&Z!==Q)e41(A,Z,{get:()=>B[Z],enumerable:!(D=R74(B,Z))||D.enumerable})}return A},X02=(A,B,Q)=>(Q=A!=null?M74(T74(A)):{},J02(B||!A||!A.__esModule?e41(Q,"default",{value:A,enumerable:!0}):Q,A)),j74=(A)=>J02(e41({},"__esModule",{value:!0}),A),V02={};S74(V02,{fromEnvSigningName:()=>_74,fromSso:()=>C02,fromStatic:()=>u74,nodeProvider:()=>m74});K02.exports=j74(V02);var y74=_w(),k74=mQ0(),HK=Q9(),_74=yT(({logger:A,signingName:B}={})=>async()=>{if(A?.debug?.("@aws-sdk/token-providers - fromEnvSigningName"),!B)throw new HK.TokenProviderError("Please pass 'signingName' to compute environment variable key",{logger:A});let Q=k74.getBearerTokenEnvKey(B);if(!(Q in process.env))throw new HK.TokenProviderError(`Token not present in '${Q}' environment variable`,{logger:A});let D={token:process.env[Q]};return y74.setTokenFeature(D,"BEARER_SERVICE_ENV_VARS","3"),D},"fromEnvSigningName"),x74=300000,_40="To refresh this SSO session run 'aws sso login' with the corresponding profile.",v74=yT(async(A,B={})=>{let{SSOOIDCClient:Q}=await Promise.resolve().then(()=>X02(k40()));return new Q(Object.assign({},B.clientConfig??{},{region:A??B.clientConfig?.region,logger:B.clientConfig?.logger??B.parentClientConfig?.logger}))},"getSsoOidcClient"),b74=yT(async(A,B,Q={})=>{let{CreateTokenCommand:D}=await Promise.resolve().then(()=>X02(k40()));return(await v74(B,Q)).send(new D({clientId:A.clientId,clientSecret:A.clientSecret,refreshToken:A.refreshToken,grantType:"refresh_token"}))},"getNewSsoOidcToken"),Y02=yT((A)=>{if(A.expiration&&A.expiration.getTime()<Date.now())throw new HK.TokenProviderError(`Token is expired. ${_40}`,!1)},"validateTokenExpiry"),zg=yT((A,B,Q=!1)=>{if(typeof B==="undefined")throw new HK.TokenProviderError(`Value not present for '${A}' in SSO Token${Q?". Cannot refresh":""}. ${_40}`,!1)},"validateTokenKey"),t41=D3(),f74=J1("fs"),{writeFile:h74}=f74.promises,g74=yT((A,B)=>{let Q=t41.getSSOTokenFilepath(A),D=JSON.stringify(B,null,2);return h74(Q,D)},"writeSSOTokenToFile"),W02=new Date(0),C02=yT((A={})=>async({callerClientConfig:B}={})=>{let Q={...A,parentClientConfig:{...B,...A.parentClientConfig}};Q.logger?.debug("@aws-sdk/token-providers - fromSso");let D=await t41.parseKnownFiles(Q),Z=t41.getProfileName({profile:Q.profile??B?.profile}),G=D[Z];if(!G)throw new HK.TokenProviderError(`Profile '${Z}' could not be found in shared credentials file.`,!1);else if(!G.sso_session)throw new HK.TokenProviderError(`Profile '${Z}' is missing required property 'sso_session'.`);let F=G.sso_session,Y=(await t41.loadSsoSessionData(Q))[F];if(!Y)throw new HK.TokenProviderError(`Sso session '${F}' could not be found in shared credentials file.`,!1);for(let H of["sso_start_url","sso_region"])if(!Y[H])throw new HK.TokenProviderError(`Sso session '${F}' is missing required property '${H}'.`,!1);let{sso_start_url:W,sso_region:J}=Y,X;try{X=await t41.getSSOTokenFromFile(F)}catch(H){throw new HK.TokenProviderError(`The SSO session token associated with profile=${Z} was not found or is invalid. ${_40}`,!1)}zg("accessToken",X.accessToken),zg("expiresAt",X.expiresAt);let{accessToken:V,expiresAt:C}=X,K={token:V,expiration:new Date(C)};if(K.expiration.getTime()-Date.now()>x74)return K;if(Date.now()-W02.getTime()<30000)return Y02(K),K;zg("clientId",X.clientId,!0),zg("clientSecret",X.clientSecret,!0),zg("refreshToken",X.refreshToken,!0);try{W02.setTime(Date.now());let H=await b74(X,J,Q);zg("accessToken",H.accessToken),zg("expiresIn",H.expiresIn);let z=new Date(Date.now()+H.expiresIn*1000);try{await g74(F,{...X,accessToken:H.accessToken,expiresAt:z.toISOString(),refreshToken:H.refreshToken})}catch($){}return{token:H.accessToken,expiration:z}}catch(H){return Y02(K),K}},"fromSso"),u74=yT(({token:A,logger:B})=>async()=>{if(B?.debug("@aws-sdk/token-providers - fromStatic"),!A||!A.token)throw new HK.TokenProviderError("Please pass a valid token to fromStatic",!1);return A},"fromStatic"),m74=yT((A={})=>HK.memoize(HK.chain(C02(A),async()=>{throw new HK.TokenProviderError("Could not load token from any providers",!1)}),(B)=>B.expiration!==void 0&&B.expiration.getTime()-Date.now()<300000,(B)=>B.expiration!==void 0),"nodeProvider")});
var yrA=E((aV5,jrA)=>{var{defineProperty:z$1,getOwnPropertyDescriptor:ZQ4,getOwnPropertyNames:GQ4}=Object,FQ4=Object.prototype.hasOwnProperty,PQ0=(A,B)=>z$1(A,"name",{value:B,configurable:!0}),IQ4=(A,B)=>{for(var Q in B)z$1(A,Q,{get:B[Q],enumerable:!0})},YQ4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of GQ4(B))if(!FQ4.call(A,Z)&&Z!==Q)z$1(A,Z,{get:()=>B[Z],enumerable:!(D=ZQ4(B,Z))||D.enumerable})}return A},WQ4=(A)=>YQ4(z$1({},"__esModule",{value:!0}),A),PrA={};IQ4(PrA,{escapeUri:()=>SrA,escapeUriPath:()=>XQ4});jrA.exports=WQ4(PrA);var SrA=PQ0((A)=>encodeURIComponent(A).replace(/[!'()*]/g,JQ4),"escapeUri"),JQ4=PQ0((A)=>`%${A.charCodeAt(0).toString(16).toUpperCase()}`,"hexEncode"),XQ4=PQ0((A)=>A.split("/").map(SrA).join("/"),"escapeUriPath")});

// Export all variables
module.exports = {
  $42,
  $V,
  $eA,
  A12,
  AB2,
  B61,
  BA2,
  C6,
  CI,
  CeA,
  E40,
  FA2,
  GeA,
  GoA,
  H40,
  HA2,
  IB2,
  Jg,
  LQ0,
  MoA,
  MtA,
  N12,
  N22,
  N60,
  NA2,
  NtA,
  O40,
  O60,
  Q61,
  R40,
  R60,
  S60,
  T12,
  TrA,
  TtA,
  V22,
  V40,
  VA2,
  X40,
  XtA,
  YeA,
  _w,
  a41,
  b40,
  g41,
  h40,
  h41,
  i22,
  i41,
  j22,
  k40,
  kk,
  ktA,
  mQ0,
  ma,
  na,
  qoA,
  roA,
  s22,
  t02,
  u41,
  vtA,
  w12,
  x12,
  x40,
  yrA
};
