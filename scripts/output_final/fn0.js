// Package extracted with entry point: fn0
// Contains 1 variables: fn0

var fn0=E((mP9)=>{var bP9=J1("url").parse,fP9={ftp:21,gopher:70,http:80,https:443,ws:80,wss:443},hP9=String.prototype.endsWith||function(A){return A.length<=this.length&&this.indexOf(A,this.length-A.length)!==-1};function gP9(A){var B=typeof A==="string"?bP9(A):A||{},Q=B.protocol,D=B.host,Z=B.port;if(typeof D!=="string"||!D||typeof Q!=="string")return"";if(Q=Q.split(":",1)[0],D=D.replace(/:\d*$/,""),Z=parseInt(Z)||fP9[Q]||0,!uP9(D,Z))return"";var G=dl("npm_config_"+Q+"_proxy")||dl(Q+"_proxy")||dl("npm_config_proxy")||dl("all_proxy");if(G&&G.indexOf("://")===-1)G=Q+"://"+G;return G}function uP9(A,B){var Q=(dl("npm_config_no_proxy")||dl("no_proxy")).toLowerCase();if(!Q)return!0;if(Q==="*")return!1;return Q.split(/[,\s]/).every(function(D){if(!D)return!0;var Z=D.match(/^(.+):(\d+)$/),G=Z?Z[1]:D,F=Z?parseInt(Z[2]):0;if(F&&F!==B)return!0;if(!/^[.*]/.test(G))return A!==G;if(G.charAt(0)==="*")G=G.slice(1);return!hP9.call(A,G)})}function dl(A){return process.env[A.toLowerCase()]||process.env[A.toUpperCase()]||""}mP9.getProxyForUrl=gP9});

// Export all variables
module.exports = {
  fn0
};
