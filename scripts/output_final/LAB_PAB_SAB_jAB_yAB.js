// Package extracted with entry point: yAB
// Contains 5 variables: LAB, PAB, SAB, jAB, yAB

var LAB=E((qAB)=>{Object.defineProperty(qAB,"__esModule",{value:!0});qAB.VERSION=void 0;qAB.VERSION="0.200.0"});
var PAB=E((OAB)=>{Object.defineProperty(OAB,"__esModule",{value:!0});OAB.OTLPLogExporter=void 0;var JN6=xu(),XN6=fu(),VN6=LAB(),MAB=co();class RAB extends JN6.OTLPExporterBase{constructor(A={}){super(MAB.createOtlpHttpExportDelegate(MAB.convertLegacyHttpOptions(A,"LOGS","v1/logs",{"User-Agent":`OTel-OTLP-Exporter-JavaScript/${VN6.VERSION}`,"Content-Type":"application/json"}),XN6.JsonLogsSerializer))}}OAB.OTLPLogExporter=RAB});
var SAB=E((YV0)=>{Object.defineProperty(YV0,"__esModule",{value:!0});YV0.OTLPLogExporter=void 0;var CN6=PAB();Object.defineProperty(YV0,"OTLPLogExporter",{enumerable:!0,get:function(){return CN6.OTLPLogExporter}})});
var jAB=E((WV0)=>{Object.defineProperty(WV0,"__esModule",{value:!0});WV0.OTLPLogExporter=void 0;var HN6=SAB();Object.defineProperty(WV0,"OTLPLogExporter",{enumerable:!0,get:function(){return HN6.OTLPLogExporter}})});
var yAB=E((JV0)=>{Object.defineProperty(JV0,"__esModule",{value:!0});JV0.OTLPLogExporter=void 0;var EN6=jAB();Object.defineProperty(JV0,"OTLPLogExporter",{enumerable:!0,get:function(){return EN6.OTLPLogExporter}})});

// Export all variables
module.exports = {
  LAB,
  PAB,
  SAB,
  jAB,
  yAB
};
