// Package extracted with entry point: G2B
// Contains 2 variables: G2B, Q2B

var G2B=E((ql5,Z2B)=>{var dN6=Q2B(),Rt=YB1();function D2B(A){if(/^\d{3,4}$/.test(A)){let Q=/(\d{1,2})(\d{2})/.exec(A);return{major:0,minor:parseInt(Q[1],10),patch:parseInt(Q[2],10)}}let B=(A||"").split(".").map((Q)=>parseInt(Q,10));return{major:B[0],minor:B[1],patch:B[2]}}function wV0(A){let{env:B}=process;if("FORCE_HYPERLINK"in B)return!(B.FORCE_HYPERLINK.length>0&&parseInt(B.FORCE_HYPERLINK,10)===0);if(Rt("no-hyperlink")||Rt("no-hyperlinks")||Rt("hyperlink=false")||Rt("hyperlink=never"))return!1;if(Rt("hyperlink=true")||Rt("hyperlink=always"))return!0;if("NETLIFY"in B)return!0;if(!dN6.supportsColor(A))return!1;if(A&&!A.isTTY)return!1;if(process.platform==="win32")return!1;if("CI"in B)return!1;if("TEAMCITY_VERSION"in B)return!1;if("TERM_PROGRAM"in B){let Q=D2B(B.TERM_PROGRAM_VERSION);switch(B.TERM_PROGRAM){case"iTerm.app":if(Q.major===3)return Q.minor>=1;return Q.major>3;case"WezTerm":return Q.major>=20200620;case"vscode":return Q.major>1||Q.major===1&&Q.minor>=72}}if("VTE_VERSION"in B){if(B.VTE_VERSION==="0.50.0")return!1;let Q=D2B(B.VTE_VERSION);return Q.major>0||Q.minor>=50}return!1}Z2B.exports={supportsHyperlink:wV0,stdout:wV0(process.stdout),stderr:wV0(process.stderr)}});
var Q2B=E(($l5,B2B)=>{var uN6=J1("os"),A2B=J1("tty"),ME=YB1(),{env:NI}=process,Jx;if(ME("no-color")||ME("no-colors")||ME("color=false")||ME("color=never"))Jx=0;else if(ME("color")||ME("colors")||ME("color=true")||ME("color=always"))Jx=1;if("FORCE_COLOR"in NI)if(NI.FORCE_COLOR==="true")Jx=1;else if(NI.FORCE_COLOR==="false")Jx=0;else Jx=NI.FORCE_COLOR.length===0?1:Math.min(parseInt(NI.FORCE_COLOR,10),3);function EV0(A){if(A===0)return!1;return{level:A,hasBasic:!0,has256:A>=2,has16m:A>=3}}function UV0(A,B){if(Jx===0)return 0;if(ME("color=16m")||ME("color=full")||ME("color=truecolor"))return 3;if(ME("color=256"))return 2;if(A&&!B&&Jx===void 0)return 0;let Q=Jx||0;if(NI.TERM==="dumb")return Q;if(process.platform==="win32"){let D=uN6.release().split(".");if(Number(D[0])>=10&&Number(D[2])>=10586)return Number(D[2])>=14931?3:2;return 1}if("CI"in NI){if(["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some((D)=>(D in NI))||NI.CI_NAME==="codeship")return 1;return Q}if("TEAMCITY_VERSION"in NI)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(NI.TEAMCITY_VERSION)?1:0;if(NI.COLORTERM==="truecolor")return 3;if("TERM_PROGRAM"in NI){let D=parseInt((NI.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(NI.TERM_PROGRAM){case"iTerm.app":return D>=3?3:2;case"Apple_Terminal":return 2}}if(/-256(color)?$/i.test(NI.TERM))return 2;if(/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(NI.TERM))return 1;if("COLORTERM"in NI)return 1;return Q}function mN6(A){let B=UV0(A,A&&A.isTTY);return EV0(B)}B2B.exports={supportsColor:mN6,stdout:EV0(UV0(!0,A2B.isatty(1))),stderr:EV0(UV0(!0,A2B.isatty(2)))}});

// Export all variables
module.exports = {
  G2B,
  Q2B
};
