// Package extracted with entry point: _6B
// Contains 1 variables: _6B

var _6B=E((Sn5,k6B)=>{var tP6="Expected a function",j6B=NaN,eP6="[object Symbol]",AS6=/^\s+|\s+$/g,BS6=/^[-+]0x[0-9a-f]+$/i,QS6=/^0b[01]+$/i,DS6=/^0o[0-7]+$/i,ZS6=parseInt,GS6=typeof global=="object"&&global&&global.Object===Object&&global,FS6=typeof self=="object"&&self&&self.Object===Object&&self,IS6=GS6||FS6||Function("return this")(),YS6=Object.prototype,WS6=YS6.toString,JS6=Math.max,XS6=Math.min,kC0=function(){return IS6.Date.now()};function VS6(A,B,Q){var D,Z,G,F,I,Y,W=0,J=!1,X=!1,V=!0;if(typeof A!="function")throw new TypeError(tP6);if(B=y6B(B)||0,_C0(Q))J=!!Q.leading,X="maxWait"in Q,G=X?JS6(y6B(Q.maxWait)||0,B):G,V="trailing"in Q?!!Q.trailing:V;function C(T){var j=D,f=Z;return D=Z=void 0,W=T,F=A.apply(f,j),F}function K(T){return W=T,I=setTimeout($,B),J?C(T):F}function H(T){var j=T-Y,f=T-W,y=B-j;return X?XS6(y,G-f):y}function z(T){var j=T-Y,f=T-W;return Y===void 0||j>=B||j<0||X&&f>=G}function $(){var T=kC0();if(z(T))return L(T);I=setTimeout($,H(T))}function L(T){if(I=void 0,V&&D)return C(T);return D=Z=void 0,F}function N(){if(I!==void 0)clearTimeout(I);W=0,D=Y=Z=I=void 0}function O(){return I===void 0?F:L(kC0())}function R(){var T=kC0(),j=z(T);if(D=arguments,Z=this,Y=T,j){if(I===void 0)return K(Y);if(X)return I=setTimeout($,B),C(Y)}if(I===void 0)I=setTimeout($,B);return F}return R.cancel=N,R.flush=O,R}function _C0(A){var B=typeof A;return!!A&&(B=="object"||B=="function")}function CS6(A){return!!A&&typeof A=="object"}function KS6(A){return typeof A=="symbol"||CS6(A)&&WS6.call(A)==eP6}function y6B(A){if(typeof A=="number")return A;if(KS6(A))return j6B;if(_C0(A)){var B=typeof A.valueOf=="function"?A.valueOf():A;A=_C0(B)?B+"":B}if(typeof A!="string")return A===0?A:+A;A=A.replace(AS6,"");var Q=QS6.test(A);return Q||DS6.test(A)?ZS6(A.slice(2),Q?2:8):BS6.test(A)?j6B:+A}k6B.exports=VS6});

// Export all variables
module.exports = {
  _6B
};
