// Package extracted with entry point: G0B
// Contains 3 variables: G0B, Z0B, tX0

var G0B=E((AS1)=>{Object.defineProperty(AS1,"__esModule",{value:!0});AS1.PrometheusSerializer=AS1.PrometheusExporter=void 0;var wq6=Z0B();Object.defineProperty(AS1,"PrometheusExporter",{enumerable:!0,get:function(){return wq6.PrometheusExporter}});var $q6=tX0();Object.defineProperty(AS1,"PrometheusSerializer",{enumerable:!0,get:function(){return $q6.PrometheusSerializer}})});
var Z0B=E((Q0B)=>{Object.defineProperty(Q0B,"__esModule",{value:!0});Q0B.PrometheusExporter=void 0;var D71=VQ(),Hq6=x3(),eX0=m_(),zq6=J1("http"),Eq6=tX0(),Uq6=J1("url");class Ix extends eX0.MetricReader{static DEFAULT_OPTIONS={host:void 0,port:9464,endpoint:"/metrics",prefix:"",appendTimestamp:!1,withResourceConstantLabels:void 0};_host;_port;_baseUrl;_endpoint;_server;_prefix;_appendTimestamp;_serializer;_startServerPromise;constructor(A={},B=()=>{}){super({aggregationSelector:(D)=>{return{type:eX0.AggregationType.DEFAULT}},aggregationTemporalitySelector:(D)=>eX0.AggregationTemporality.CUMULATIVE,metricProducers:A.metricProducers});this._host=A.host||process.env.OTEL_EXPORTER_PROMETHEUS_HOST||Ix.DEFAULT_OPTIONS.host,this._port=A.port||Number(process.env.OTEL_EXPORTER_PROMETHEUS_PORT)||Ix.DEFAULT_OPTIONS.port,this._prefix=A.prefix||Ix.DEFAULT_OPTIONS.prefix,this._appendTimestamp=typeof A.appendTimestamp==="boolean"?A.appendTimestamp:Ix.DEFAULT_OPTIONS.appendTimestamp;let Q=A.withResourceConstantLabels||Ix.DEFAULT_OPTIONS.withResourceConstantLabels;if(this._server=zq6.createServer(this._requestHandler).unref(),this._serializer=new Eq6.PrometheusSerializer(this._prefix,this._appendTimestamp,Q),this._baseUrl=`http://${this._host}:${this._port}/`,this._endpoint=(A.endpoint||Ix.DEFAULT_OPTIONS.endpoint).replace(/^([^/])/,"/$1"),A.preventServerStart!==!0)this.startServer().then(B,(D)=>{D71.diag.error(D),B(D)});else if(B)queueMicrotask(B)}async onForceFlush(){}onShutdown(){return this.stopServer()}stopServer(){if(!this._server)return D71.diag.debug("Prometheus stopServer() was called but server was never started."),Promise.resolve();else return new Promise((A)=>{this._server.close((B)=>{if(!B)D71.diag.debug("Prometheus exporter was stopped");else if(B.code!=="ERR_SERVER_NOT_RUNNING")Hq6.globalErrorHandler(B);A()})})}startServer(){return this._startServerPromise??=new Promise((A,B)=>{this._server.once("error",B),this._server.listen({port:this._port,host:this._host},()=>{D71.diag.debug(`Prometheus exporter server started: ${this._host}:${this._port}/${this._endpoint}`),A()})}),this._startServerPromise}getMetricsRequestHandler(A,B){this._exportMetrics(B)}_requestHandler=(A,B)=>{if(A.url!=null&&new Uq6.URL(A.url,this._baseUrl).pathname===this._endpoint)this._exportMetrics(B);else this._notFound(B)};_exportMetrics=(A)=>{A.statusCode=200,A.setHeader("content-type","text/plain"),this.collect().then((B)=>{let{resourceMetrics:Q,errors:D}=B;if(D.length)D71.diag.error("PrometheusExporter: metrics collection errors",...D);A.end(this._serializer.serialize(Q))},(B)=>{A.end(`# failed to export metrics: ${B}`)})};_notFound=(A)=>{A.statusCode=404,A.end()}}Q0B.PrometheusExporter=Ix});
var tX0=E((A0B)=>{Object.defineProperty(A0B,"__esModule",{value:!0});A0B.PrometheusSerializer=void 0;var Wq6=VQ(),ou=m_(),o1B=x3();function eP1(A){return A.replace(/\\/g,"\\\\").replace(/\n/g,"\\n")}function t1B(A=""){if(typeof A!=="string")A=JSON.stringify(A);return eP1(A).replace(/"/g,"\\\"")}var Jq6=/[^a-z0-9_]/gi,Xq6=/_{2,}/g;function oX0(A){return A.replace(Jq6,"_").replace(Xq6,"_")}function rX0(A,B){if(!A.endsWith("_total")&&B.dataPointType===ou.DataPointType.SUM&&B.isMonotonic)A=A+"_total";return A}function Vq6(A){if(A===1/0)return"+Inf";else if(A===-1/0)return"-Inf";else return`${A}`}function Cq6(A){switch(A.dataPointType){case ou.DataPointType.SUM:if(A.isMonotonic)return"counter";return"gauge";case ou.DataPointType.GAUGE:return"gauge";case ou.DataPointType.HISTOGRAM:return"histogram";default:return"untyped"}}function tP1(A,B,Q,D,Z){let G=!1,F="";for(let[I,Y]of Object.entries(B)){let W=oX0(I);G=!0,F+=`${F.length>0?",":""}${W}="${t1B(Y)}"`}if(Z)for(let[I,Y]of Object.entries(Z)){let W=oX0(I);G=!0,F+=`${F.length>0?",":""}${W}="${t1B(Y)}"`}if(G)A+=`{${F}}`;return`${A} ${Vq6(Q)}${D!==void 0?" "+String(D):""}
`}var Kq6="# no registered metrics";class e1B{_prefix;_appendTimestamp;_additionalAttributes;_withResourceConstantLabels;constructor(A,B=!1,Q){if(A)this._prefix=A+"_";this._appendTimestamp=B,this._withResourceConstantLabels=Q}serialize(A){let B="";this._additionalAttributes=this._filterResourceConstantLabels(A.resource.attributes,this._withResourceConstantLabels);for(let Q of A.scopeMetrics)B+=this._serializeScopeMetrics(Q);if(B==="")B+=Kq6;return this._serializeResource(A.resource)+B}_filterResourceConstantLabels(A,B){if(B){let Q={};for(let[D,Z]of Object.entries(A))if(D.match(B))Q[D]=Z;return Q}return}_serializeScopeMetrics(A){let B="";for(let Q of A.metrics)B+=this._serializeMetricData(Q)+`
`;return B}_serializeMetricData(A){let B=oX0(eP1(A.descriptor.name));if(this._prefix)B=`${this._prefix}${B}`;let Q=A.dataPointType;B=rX0(B,A);let D=`# HELP ${B} ${eP1(A.descriptor.description||"description missing")}`,Z=A.descriptor.unit?`
# UNIT ${B} ${eP1(A.descriptor.unit)}`:"",G=`# TYPE ${B} ${Cq6(A)}`,F="";switch(Q){case ou.DataPointType.SUM:case ou.DataPointType.GAUGE:{F=A.dataPoints.map((I)=>this._serializeSingularDataPoint(B,A,I)).join("");break}case ou.DataPointType.HISTOGRAM:{F=A.dataPoints.map((I)=>this._serializeHistogramDataPoint(B,A,I)).join("");break}default:Wq6.diag.error(`Unrecognizable DataPointType: ${Q} for metric "${B}"`)}return`${D}${Z}
${G}
${F}`.trim()}_serializeSingularDataPoint(A,B,Q){let D="";A=rX0(A,B);let{value:Z,attributes:G}=Q,F=o1B.hrTimeToMilliseconds(Q.endTime);return D+=tP1(A,G,Z,this._appendTimestamp?F:void 0,this._additionalAttributes),D}_serializeHistogramDataPoint(A,B,Q){let D="";A=rX0(A,B);let{attributes:Z,value:G}=Q,F=o1B.hrTimeToMilliseconds(Q.endTime);for(let J of["count","sum"]){let X=G[J];if(X!=null)D+=tP1(A+"_"+J,Z,X,this._appendTimestamp?F:void 0,this._additionalAttributes)}let I=0,Y=G.buckets.counts.entries(),W=!1;for(let[J,X]of Y){I+=X;let V=G.buckets.boundaries[J];if(V===void 0&&W)break;if(V===1/0)W=!0;D+=tP1(A+"_bucket",Z,I,this._appendTimestamp?F:void 0,Object.assign({},this._additionalAttributes??{},{le:V===void 0||V===1/0?"+Inf":String(V)}))}return D}_serializeResource(A){return`# HELP target_info Target metadata
# TYPE target_info gauge
${tP1("target_info",A.attributes,1).trim()}
`}}A0B.PrometheusSerializer=e1B});

// Export all variables
module.exports = {
  G0B,
  Z0B,
  tX0
};
