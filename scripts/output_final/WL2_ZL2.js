// Package extracted with entry point: WL2
// Contains 2 variables: WL2, ZL2

var WL2=E((kM5,YL2)=>{var Ba4=J1("events"),rR1=J1("http"),{Duplex:yM5}=J1("stream"),{createHash:Qa4}=J1("crypto"),GL2=fG0(),Gu=M51(),Da4=ZL2(),Za4=sR1(),{GUID:Ga4,kWebSocket:Fa4}=FP(),Ia4=/^[+/0-9A-Za-z]{22}==$/;class IL2 extends Ba4{constructor(A,B){super();if(A={allowSynchronousEvents:!0,autoPong:!0,maxPayload:104857600,skipUTF8Validation:!1,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,WebSocket:Za4,...A},A.port==null&&!A.server&&!A.noServer||A.port!=null&&(A.server||A.noServer)||A.server&&A.noServer)throw new TypeError('One and only one of the "port", "server", or "noServer" options must be specified');if(A.port!=null)this._server=rR1.createServer((Q,D)=>{let Z=rR1.STATUS_CODES[426];D.writeHead(426,{"Content-Length":Z.length,"Content-Type":"text/plain"}),D.end(Z)}),this._server.listen(A.port,A.host,A.backlog,B);else if(A.server)this._server=A.server;if(this._server){let Q=this.emit.bind(this,"connection");this._removeListeners=Ya4(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(D,Z,G)=>{this.handleUpgrade(D,Z,G,Q)}})}if(A.perMessageDeflate===!0)A.perMessageDeflate={};if(A.clientTracking)this.clients=new Set,this._shouldEmitClose=!1;this.options=A,this._state=0}address(){if(this.options.noServer)throw new Error('The server is operating in "noServer" mode');if(!this._server)return null;return this._server.address()}close(A){if(this._state===2){if(A)this.once("close",()=>{A(new Error("The server is not running"))});process.nextTick(P51,this);return}if(A)this.once("close",A);if(this._state===1)return;if(this._state=1,this.options.noServer||this.options.server){if(this._server)this._removeListeners(),this._removeListeners=this._server=null;if(this.clients)if(!this.clients.size)process.nextTick(P51,this);else this._shouldEmitClose=!0;else process.nextTick(P51,this)}else{let B=this._server;this._removeListeners(),this._removeListeners=this._server=null,B.close(()=>{P51(this)})}}shouldHandle(A){if(this.options.path){let B=A.url.indexOf("?");if((B!==-1?A.url.slice(0,B):A.url)!==this.options.path)return!1}return!0}handleUpgrade(A,B,Q,D){B.on("error",FL2);let Z=A.headers["sec-websocket-key"],G=A.headers.upgrade,F=+A.headers["sec-websocket-version"];if(A.method!=="GET"){Fu(this,A,B,405,"Invalid HTTP method");return}if(G===void 0||G.toLowerCase()!=="websocket"){Fu(this,A,B,400,"Invalid Upgrade header");return}if(Z===void 0||!Ia4.test(Z)){Fu(this,A,B,400,"Missing or invalid Sec-WebSocket-Key header");return}if(F!==8&&F!==13){Fu(this,A,B,400,"Missing or invalid Sec-WebSocket-Version header");return}if(!this.shouldHandle(A)){S51(B,400);return}let I=A.headers["sec-websocket-protocol"],Y=new Set;if(I!==void 0)try{Y=Da4.parse(I)}catch(X){Fu(this,A,B,400,"Invalid Sec-WebSocket-Protocol header");return}let W=A.headers["sec-websocket-extensions"],J={};if(this.options.perMessageDeflate&&W!==void 0){let X=new Gu(this.options.perMessageDeflate,!0,this.options.maxPayload);try{let V=GL2.parse(W);if(V[Gu.extensionName])X.accept(V[Gu.extensionName]),J[Gu.extensionName]=X}catch(V){Fu(this,A,B,400,"Invalid or unacceptable Sec-WebSocket-Extensions header");return}}if(this.options.verifyClient){let X={origin:A.headers[`${F===8?"sec-websocket-origin":"origin"}`],secure:!!(A.socket.authorized||A.socket.encrypted),req:A};if(this.options.verifyClient.length===2){this.options.verifyClient(X,(V,C,K,H)=>{if(!V)return S51(B,C||401,K,H);this.completeUpgrade(J,Z,Y,A,B,Q,D)});return}if(!this.options.verifyClient(X))return S51(B,401)}this.completeUpgrade(J,Z,Y,A,B,Q,D)}completeUpgrade(A,B,Q,D,Z,G,F){if(!Z.readable||!Z.writable)return Z.destroy();if(Z[Fa4])throw new Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");if(this._state>0)return S51(Z,503);let Y=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${Qa4("sha1").update(B+Ga4).digest("base64")}`],W=new this.options.WebSocket(null,void 0,this.options);if(Q.size){let J=this.options.handleProtocols?this.options.handleProtocols(Q,D):Q.values().next().value;if(J)Y.push(`Sec-WebSocket-Protocol: ${J}`),W._protocol=J}if(A[Gu.extensionName]){let J=A[Gu.extensionName].params,X=GL2.format({[Gu.extensionName]:[J]});Y.push(`Sec-WebSocket-Extensions: ${X}`),W._extensions=A}if(this.emit("headers",Y,D),Z.write(Y.concat(`\r
`).join(`\r
`)),Z.removeListener("error",FL2),W.setSocket(Z,G,{allowSynchronousEvents:this.options.allowSynchronousEvents,maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation}),this.clients)this.clients.add(W),W.on("close",()=>{if(this.clients.delete(W),this._shouldEmitClose&&!this.clients.size)process.nextTick(P51,this)});F(W,D)}}YL2.exports=IL2;function Ya4(A,B){for(let Q of Object.keys(B))A.on(Q,B[Q]);return function Q(){for(let D of Object.keys(B))A.removeListener(D,B[D])}}function P51(A){A._state=2,A.emit("close")}function FL2(){this.destroy()}function S51(A,B,Q,D){Q=Q||rR1.STATUS_CODES[B],D={Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(Q),...D},A.once("finish",A.destroy),A.end(`HTTP/1.1 ${B} ${rR1.STATUS_CODES[B]}\r
`+Object.keys(D).map((Z)=>`${Z}: ${D[Z]}`).join(`\r
`)+`\r
\r
`+Q)}function Fu(A,B,Q,D,Z){if(A.listenerCount("wsClientError")){let G=new Error(Z);Error.captureStackTrace(G,Fu),A.emit("wsClientError",G,Q,B)}else S51(Q,D,Z)}});
var ZL2=E((jM5,DL2)=>{var{tokenChars:en4}=Jo();function Aa4(A){let B=new Set,Q=-1,D=-1,Z=0;for(Z;Z<A.length;Z++){let F=A.charCodeAt(Z);if(D===-1&&en4[F]===1){if(Q===-1)Q=Z}else if(Z!==0&&(F===32||F===9)){if(D===-1&&Q!==-1)D=Z}else if(F===44){if(Q===-1)throw new SyntaxError(`Unexpected character at index ${Z}`);if(D===-1)D=Z;let I=A.slice(Q,D);if(B.has(I))throw new SyntaxError(`The "${I}" subprotocol is duplicated`);B.add(I),Q=D=-1}else throw new SyntaxError(`Unexpected character at index ${Z}`)}if(Q===-1||D!==-1)throw new SyntaxError("Unexpected end of input");let G=A.slice(Q,Z);if(B.has(G))throw new SyntaxError(`The "${G}" subprotocol is duplicated`);return B.add(G),B}DL2.exports={parse:Aa4}});

// Export all variables
module.exports = {
  WL2,
  ZL2
};
