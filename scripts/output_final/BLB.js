// Package extracted with entry point: BLB
// Contains 1 variables: BLB

var BLB=E((C53,ALB)=>{var eNB=J1("child_process"),rNB=eNB.spawn,PQ8=eNB.exec;ALB.exports=function(A,B,Q){if(typeof B==="function"&&Q===void 0)Q=B,B=void 0;if(A=parseInt(A),Number.isNaN(A))if(Q)return Q(new Error("pid must be a number"));else throw new Error("pid must be a number");var D={},Z={};switch(D[A]=[],Z[A]=1,process.platform){case"win32":PQ8("taskkill /pid "+A+" /T /F",Q);break;case"darwin":W$0(A,D,Z,function(G){return rNB("pgrep",["-P",G])},function(){oNB(D,B,Q)});break;default:W$0(A,D,Z,function(G){return rNB("ps",["-o","pid","--no-headers","--ppid",G])},function(){oNB(D,B,Q)});break}};function oNB(A,B,Q){var D={};try{Object.keys(A).forEach(function(Z){if(A[Z].forEach(function(G){if(!D[G])tNB(G,B),D[G]=1}),!D[Z])tNB(Z,B),D[Z]=1})}catch(Z){if(Q)return Q(Z);else throw Z}if(Q)return Q()}function tNB(A,B){try{process.kill(parseInt(A,10),B)}catch(Q){if(Q.code!=="ESRCH")throw Q}}function W$0(A,B,Q,D,Z){var G=D(A),F="";G.stdout.on("data",function(W){var W=W.toString("ascii");F+=W});var I=function(Y){if(delete Q[A],Y!=0){if(Object.keys(Q).length==0)Z();return}F.match(/\d+/g).forEach(function(W){W=parseInt(W,10),B[A].push(W),B[W]=[],Q[W]=1,W$0(W,B,Q,D,Z)})};G.on("close",I)}});

// Export all variables
module.exports = {
  BLB
};
