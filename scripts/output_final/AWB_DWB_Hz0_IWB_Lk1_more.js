// Package extracted with entry point: YJB
// Contains 19 variables: AWB, DWB, Hz0, IWB, Lk1, NYB, PYB, TWB, Uz0, XWB... (and 9 more)

var AWB=E((g03,eYB)=>{var{defineProperty:Sk1,getOwnPropertyDescriptor:su6,getOwnPropertyNames:ru6}=Object,ou6=Object.prototype.hasOwnProperty,wz0=(A,B)=>Sk1(A,"name",{value:B,configurable:!0}),tu6=(A,B)=>{for(var Q in B)Sk1(A,Q,{get:B[Q],enumerable:!0})},eu6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of ru6(B))if(!ou6.call(A,Z)&&Z!==Q)Sk1(A,Z,{get:()=>B[Z],enumerable:!(D=su6(B,Z))||D.enumerable})}return A},Am6=(A)=>eu6(Sk1({},"__esModule",{value:!0}),A),rYB={};tu6(rYB,{fromUtf8:()=>tYB,toUint8Array:()=>Bm6,toUtf8:()=>Qm6});eYB.exports=Am6(rYB);var oYB=Uz0(),tYB=wz0((A)=>{let B=oYB.fromString(A,"utf8");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength/Uint8Array.BYTES_PER_ELEMENT)},"fromUtf8"),Bm6=wz0((A)=>{if(typeof A==="string")return tYB(A);if(ArrayBuffer.isView(A))return new Uint8Array(A.buffer,A.byteOffset,A.byteLength/Uint8Array.BYTES_PER_ELEMENT);return new Uint8Array(A)},"toUint8Array"),Qm6=wz0((A)=>{if(typeof A==="string")return A;if(typeof A!=="object"||typeof A.byteOffset!=="number"||typeof A.byteLength!=="number")throw new Error("@smithy/util-utf8: toUtf8 encoder function only accepts string | Uint8Array.");return oYB.fromArrayBuffer(A.buffer,A.byteOffset,A.byteLength).toString("utf8")},"toUtf8")});
var DWB=E((BWB)=>{Object.defineProperty(BWB,"__esModule",{value:!0});BWB.getAwsChunkedEncodingStream=void 0;var Dm6=J1("stream"),Zm6=(A,B)=>{let{base64Encoder:Q,bodyLengthChecker:D,checksumAlgorithmFn:Z,checksumLocationName:G,streamHasher:F}=B,I=Q!==void 0&&Z!==void 0&&G!==void 0&&F!==void 0,Y=I?F(Z,A):void 0,W=new Dm6.Readable({read:()=>{}});return A.on("data",(J)=>{let X=D(J)||0;W.push(`${X.toString(16)}\r
`),W.push(J),W.push(`\r
`)}),A.on("end",async()=>{if(W.push(`0\r
`),I){let J=Q(await Y);W.push(`${G}:${J}\r
`),W.push(`\r
`)}W.push(null)}),W};BWB.getAwsChunkedEncodingStream=Zm6});
var Hz0=E((v03,Rk1)=>{var{defineProperty:fYB,getOwnPropertyDescriptor:Nu6,getOwnPropertyNames:Lu6}=Object,Mu6=Object.prototype.hasOwnProperty,Cz0=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Lu6(B))if(!Mu6.call(A,Z)&&Z!==Q)fYB(A,Z,{get:()=>B[Z],enumerable:!(D=Nu6(B,Z))||D.enumerable})}return A},hYB=(A,B,Q)=>(Cz0(A,B,"default"),Q&&Cz0(Q,B,"default")),Ru6=(A)=>Cz0(fYB({},"__esModule",{value:!0}),A),Kz0={};Rk1.exports=Ru6(Kz0);hYB(Kz0,PYB(),Rk1.exports);hYB(Kz0,bYB(),Rk1.exports)});
var IWB=E((m03,FWB)=>{var{defineProperty:jk1,getOwnPropertyDescriptor:Gm6,getOwnPropertyNames:Fm6}=Object,Im6=Object.prototype.hasOwnProperty,$z0=(A,B)=>jk1(A,"name",{value:B,configurable:!0}),Ym6=(A,B)=>{for(var Q in B)jk1(A,Q,{get:B[Q],enumerable:!0})},Wm6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Fm6(B))if(!Im6.call(A,Z)&&Z!==Q)jk1(A,Z,{get:()=>B[Z],enumerable:!(D=Gm6(B,Z))||D.enumerable})}return A},Jm6=(A)=>Wm6(jk1({},"__esModule",{value:!0}),A),ZWB={};Ym6(ZWB,{escapeUri:()=>GWB,escapeUriPath:()=>Vm6});FWB.exports=Jm6(ZWB);var GWB=$z0((A)=>encodeURIComponent(A).replace(/[!'()*]/g,Xm6),"escapeUri"),Xm6=$z0((A)=>`%${A.charCodeAt(0).toString(16).toUpperCase()}`,"hexEncode"),Vm6=$z0((A)=>A.split("/").map(GWB).join("/"),"escapeUriPath")});
var Lk1=E((y03,RYB)=>{var{defineProperty:Nk1,getOwnPropertyDescriptor:eg6,getOwnPropertyNames:Au6}=Object,Bu6=Object.prototype.hasOwnProperty,LYB=(A,B)=>Nk1(A,"name",{value:B,configurable:!0}),Qu6=(A,B)=>{for(var Q in B)Nk1(A,Q,{get:B[Q],enumerable:!0})},Du6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Au6(B))if(!Bu6.call(A,Z)&&Z!==Q)Nk1(A,Z,{get:()=>B[Z],enumerable:!(D=eg6(B,Z))||D.enumerable})}return A},Zu6=(A)=>Du6(Nk1({},"__esModule",{value:!0}),A),MYB={};Qu6(MYB,{fromArrayBuffer:()=>Fu6,fromString:()=>Iu6});RYB.exports=Zu6(MYB);var Gu6=NYB(),Xz0=J1("buffer"),Fu6=LYB((A,B=0,Q=A.byteLength-B)=>{if(!Gu6.isArrayBuffer(A))throw new TypeError(`The "input" argument must be ArrayBuffer. Received type ${typeof A} (${A})`);return Xz0.Buffer.from(A,B,Q)},"fromArrayBuffer"),Iu6=LYB((A,B)=>{if(typeof A!=="string")throw new TypeError(`The "input" argument must be of type string. Received type ${typeof A} (${A})`);return B?Xz0.Buffer.from(A,B):Xz0.Buffer.from(A)},"fromString")});
var NYB=E((j03,qYB)=>{var{defineProperty:qk1,getOwnPropertyDescriptor:pg6,getOwnPropertyNames:ig6}=Object,ng6=Object.prototype.hasOwnProperty,ag6=(A,B)=>qk1(A,"name",{value:B,configurable:!0}),sg6=(A,B)=>{for(var Q in B)qk1(A,Q,{get:B[Q],enumerable:!0})},rg6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of ig6(B))if(!ng6.call(A,Z)&&Z!==Q)qk1(A,Z,{get:()=>B[Z],enumerable:!(D=pg6(B,Z))||D.enumerable})}return A},og6=(A)=>rg6(qk1({},"__esModule",{value:!0}),A),$YB={};sg6($YB,{isArrayBuffer:()=>tg6});qYB.exports=og6($YB);var tg6=ag6((A)=>typeof ArrayBuffer==="function"&&A instanceof ArrayBuffer||Object.prototype.toString.call(A)==="[object ArrayBuffer]","isArrayBuffer")});
var PYB=E((OYB)=>{Object.defineProperty(OYB,"__esModule",{value:!0});OYB.fromBase64=void 0;var Yu6=Lk1(),Wu6=/^[A-Za-z0-9+/]*={0,2}$/,Ju6=(A)=>{if(A.length*3%4!==0)throw new TypeError("Incorrect padding on base64 string.");if(!Wu6.exec(A))throw new TypeError("Invalid base64 string.");let B=Yu6.fromString(A,"base64");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength)};OYB.fromBase64=Ju6});
var TWB=E((c03,OWB)=>{var{create:$m6,defineProperty:dD1,getOwnPropertyDescriptor:qm6,getOwnPropertyNames:Nm6,getPrototypeOf:Lm6}=Object,Mm6=Object.prototype.hasOwnProperty,RI=(A,B)=>dD1(A,"name",{value:B,configurable:!0}),Rm6=(A,B)=>{for(var Q in B)dD1(A,Q,{get:B[Q],enumerable:!0})},KWB=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Nm6(B))if(!Mm6.call(A,Z)&&Z!==Q)dD1(A,Z,{get:()=>B[Z],enumerable:!(D=qm6(B,Z))||D.enumerable})}return A},Om6=(A,B,Q)=>(Q=A!=null?$m6(Lm6(A)):{},KWB(B||!A||!A.__esModule?dD1(Q,"default",{value:A,enumerable:!0}):Q,A)),Tm6=(A)=>KWB(dD1({},"__esModule",{value:!0}),A),HWB={};Rm6(HWB,{DEFAULT_REQUEST_TIMEOUT:()=>km6,NodeHttp2Handler:()=>fm6,NodeHttpHandler:()=>_m6,streamCollector:()=>gm6});OWB.exports=Tm6(HWB);var zWB=pH0(),EWB=XWB(),Nz0=J1("http"),Lz0=J1("https"),Pm6=["ECONNRESET","EPIPE","ETIMEDOUT"],UWB=RI((A)=>{let B={};for(let Q of Object.keys(A)){let D=A[Q];B[Q]=Array.isArray(D)?D.join(","):D}return B},"getTransformedHeaders"),Sm6=RI((A,B,Q=0)=>{if(!Q)return;let D=setTimeout(()=>{A.destroy(),B(Object.assign(new Error(`Socket timed out without establishing a connection within ${Q} ms`),{name:"TimeoutError"}))},Q);A.on("socket",(Z)=>{if(Z.connecting)Z.on("connect",()=>{clearTimeout(D)});else clearTimeout(D)})},"setConnectionTimeout"),jm6=RI((A,{keepAlive:B,keepAliveMsecs:Q})=>{if(B!==!0)return;A.on("socket",(D)=>{D.setKeepAlive(B,Q||0)})},"setSocketKeepAlive"),ym6=RI((A,B,Q=0)=>{A.setTimeout(Q,()=>{A.destroy(),B(Object.assign(new Error(`Connection timed out after ${Q} ms`),{name:"TimeoutError"}))})},"setSocketTimeout"),wWB=J1("stream"),VWB=1000;async function Mz0(A,B,Q=VWB){let D=B.headers??{},Z=D.Expect||D.expect,G=-1,F=!1;if(Z==="100-continue")await Promise.race([new Promise((I)=>{G=Number(setTimeout(I,Math.max(VWB,Q)))}),new Promise((I)=>{A.on("continue",()=>{clearTimeout(G),I()}),A.on("error",()=>{F=!0,clearTimeout(G),I()})})]);if(!F)$WB(A,B.body)}RI(Mz0,"writeRequestBody");function $WB(A,B){if(B instanceof wWB.Readable){B.pipe(A);return}if(B){if(Buffer.isBuffer(B)||typeof B==="string"){A.end(B);return}let Q=B;if(typeof Q==="object"&&Q.buffer&&typeof Q.byteOffset==="number"&&typeof Q.byteLength==="number"){A.end(Buffer.from(Q.buffer,Q.byteOffset,Q.byteLength));return}A.end(Buffer.from(B));return}A.end()}RI($WB,"writeBody");var km6=0,qWB=class A{constructor(B){this.socketWarningTimestamp=0,this.metadata={handlerProtocol:"http/1.1"},this.configProvider=new Promise((Q,D)=>{if(typeof B==="function")B().then((Z)=>{Q(this.resolveDefaultConfig(Z))}).catch(D);else Q(this.resolveDefaultConfig(B))})}static create(B){if(typeof(B==null?void 0:B.handle)==="function")return B;return new A(B)}static checkSocketUsage(B,Q){var D,Z;let{sockets:G,requests:F,maxSockets:I}=B;if(typeof I!=="number"||I===1/0)return Q;let Y=15000;if(Date.now()-Y<Q)return Q;if(G&&F)for(let W in G){let J=((D=G[W])==null?void 0:D.length)??0,X=((Z=F[W])==null?void 0:Z.length)??0;if(J>=I&&X>=2*I)return console.warn("@smithy/node-http-handler:WARN",`socket usage at capacity=${J} and ${X} additional requests are enqueued.`,"See https://docs.aws.amazon.com/sdk-for-javascript/v3/developer-guide/node-configuring-maxsockets.html","or increase socketAcquisitionWarningTimeout=(millis) in the NodeHttpHandler config."),Date.now()}return Q}resolveDefaultConfig(B){let{requestTimeout:Q,connectionTimeout:D,socketTimeout:Z,httpAgent:G,httpsAgent:F}=B||{},I=!0,Y=50;return{connectionTimeout:D,requestTimeout:Q??Z,httpAgent:(()=>{if(G instanceof Nz0.Agent||typeof(G==null?void 0:G.destroy)==="function")return G;return new Nz0.Agent({keepAlive:!0,maxSockets:50,...G})})(),httpsAgent:(()=>{if(F instanceof Lz0.Agent||typeof(F==null?void 0:F.destroy)==="function")return F;return new Lz0.Agent({keepAlive:!0,maxSockets:50,...F})})()}}destroy(){var B,Q,D,Z;(Q=(B=this.config)==null?void 0:B.httpAgent)==null||Q.destroy(),(Z=(D=this.config)==null?void 0:D.httpsAgent)==null||Z.destroy()}async handle(B,{abortSignal:Q}={}){if(!this.config)this.config=await this.configProvider;let D;return new Promise((Z,G)=>{let F=void 0,I=RI(async(L)=>{await F,clearTimeout(D),Z(L)},"resolve"),Y=RI(async(L)=>{await F,G(L)},"reject");if(!this.config)throw new Error("Node HTTP request handler config is not resolved");if(Q==null?void 0:Q.aborted){let L=new Error("Request aborted");L.name="AbortError",Y(L);return}let W=B.protocol==="https:",J=W?this.config.httpsAgent:this.config.httpAgent;D=setTimeout(()=>{this.socketWarningTimestamp=A.checkSocketUsage(J,this.socketWarningTimestamp)},this.config.socketAcquisitionWarningTimeout??(this.config.requestTimeout??2000)+(this.config.connectionTimeout??1000));let X=EWB.buildQueryString(B.query||{}),V=void 0;if(B.username!=null||B.password!=null){let L=B.username??"",N=B.password??"";V=`${L}:${N}`}let C=B.path;if(X)C+=`?${X}`;if(B.fragment)C+=`#${B.fragment}`;let K={headers:B.headers,host:B.hostname,method:B.method,path:C,port:B.port,agent:J,auth:V},z=(W?Lz0.request:Nz0.request)(K,(L)=>{let N=new zWB.HttpResponse({statusCode:L.statusCode||-1,reason:L.statusMessage,headers:UWB(L.headers),body:L});I({response:N})});if(z.on("error",(L)=>{if(Pm6.includes(L.code))Y(Object.assign(L,{name:"TimeoutError"}));else Y(L)}),Sm6(z,Y,this.config.connectionTimeout),ym6(z,Y,this.config.requestTimeout),Q)Q.onabort=()=>{z.abort();let L=new Error("Request aborted");L.name="AbortError",Y(L)};let $=K.agent;if(typeof $==="object"&&"keepAlive"in $)jm6(z,{keepAlive:$.keepAlive,keepAliveMsecs:$.keepAliveMsecs});F=Mz0(z,B,this.config.requestTimeout).catch(G)})}updateHttpClientConfig(B,Q){this.config=void 0,this.configProvider=this.configProvider.then((D)=>{return{...D,[B]:Q}})}httpHandlerConfigs(){return this.config??{}}};RI(qWB,"NodeHttpHandler");var _m6=qWB,CWB=J1("http2"),xm6=Om6(J1("http2")),NWB=class A{constructor(B){this.sessions=[],this.sessions=B??[]}poll(){if(this.sessions.length>0)return this.sessions.shift()}offerLast(B){this.sessions.push(B)}contains(B){return this.sessions.includes(B)}remove(B){this.sessions=this.sessions.filter((Q)=>Q!==B)}[Symbol.iterator](){return this.sessions[Symbol.iterator]()}destroy(B){for(let Q of this.sessions)if(Q===B){if(!Q.destroyed)Q.destroy()}}};RI(NWB,"NodeHttp2ConnectionPool");var vm6=NWB,LWB=class A{constructor(B){if(this.sessionCache=new Map,this.config=B,this.config.maxConcurrency&&this.config.maxConcurrency<=0)throw new RangeError("maxConcurrency must be greater than zero.")}lease(B,Q){let D=this.getUrlString(B),Z=this.sessionCache.get(D);if(Z){let Y=Z.poll();if(Y&&!this.config.disableConcurrency)return Y}let G=xm6.default.connect(D);if(this.config.maxConcurrency)G.settings({maxConcurrentStreams:this.config.maxConcurrency},(Y)=>{if(Y)throw new Error("Fail to set maxConcurrentStreams to "+this.config.maxConcurrency+"when creating new session for "+B.destination.toString())});G.unref();let F=RI(()=>{G.destroy(),this.deleteSession(D,G)},"destroySessionCb");if(G.on("goaway",F),G.on("error",F),G.on("frameError",F),G.on("close",()=>this.deleteSession(D,G)),Q.requestTimeout)G.setTimeout(Q.requestTimeout,F);let I=this.sessionCache.get(D)||new vm6;return I.offerLast(G),this.sessionCache.set(D,I),G}deleteSession(B,Q){let D=this.sessionCache.get(B);if(!D)return;if(!D.contains(Q))return;D.remove(Q),this.sessionCache.set(B,D)}release(B,Q){var D;let Z=this.getUrlString(B);(D=this.sessionCache.get(Z))==null||D.offerLast(Q)}destroy(){for(let[B,Q]of this.sessionCache){for(let D of Q){if(!D.destroyed)D.destroy();Q.remove(D)}this.sessionCache.delete(B)}}setMaxConcurrentStreams(B){if(this.config.maxConcurrency&&this.config.maxConcurrency<=0)throw new RangeError("maxConcurrentStreams must be greater than zero.");this.config.maxConcurrency=B}setDisableConcurrentStreams(B){this.config.disableConcurrency=B}getUrlString(B){return B.destination.toString()}};RI(LWB,"NodeHttp2ConnectionManager");var bm6=LWB,MWB=class A{constructor(B){this.metadata={handlerProtocol:"h2"},this.connectionManager=new bm6({}),this.configProvider=new Promise((Q,D)=>{if(typeof B==="function")B().then((Z)=>{Q(Z||{})}).catch(D);else Q(B||{})})}static create(B){if(typeof(B==null?void 0:B.handle)==="function")return B;return new A(B)}destroy(){this.connectionManager.destroy()}async handle(B,{abortSignal:Q}={}){if(!this.config){if(this.config=await this.configProvider,this.connectionManager.setDisableConcurrentStreams(this.config.disableConcurrentStreams||!1),this.config.maxConcurrentStreams)this.connectionManager.setMaxConcurrentStreams(this.config.maxConcurrentStreams)}let{requestTimeout:D,disableConcurrentStreams:Z}=this.config;return new Promise((G,F)=>{var I;let Y=!1,W=void 0,J=RI(async(y)=>{await W,G(y)},"resolve"),X=RI(async(y)=>{await W,F(y)},"reject");if(Q==null?void 0:Q.aborted){Y=!0;let y=new Error("Request aborted");y.name="AbortError",X(y);return}let{hostname:V,method:C,port:K,protocol:H,query:z}=B,$="";if(B.username!=null||B.password!=null){let y=B.username??"",c=B.password??"";$=`${y}:${c}@`}let L=`${H}//${$}${V}${K?`:${K}`:""}`,N={destination:new URL(L)},O=this.connectionManager.lease(N,{requestTimeout:(I=this.config)==null?void 0:I.sessionTimeout,disableConcurrentStreams:Z||!1}),R=RI((y)=>{if(Z)this.destroySession(O);Y=!0,X(y)},"rejectWithDestroy"),T=EWB.buildQueryString(z||{}),j=B.path;if(T)j+=`?${T}`;if(B.fragment)j+=`#${B.fragment}`;let f=O.request({...B.headers,[CWB.constants.HTTP2_HEADER_PATH]:j,[CWB.constants.HTTP2_HEADER_METHOD]:C});if(O.ref(),f.on("response",(y)=>{let c=new zWB.HttpResponse({statusCode:y[":status"]||-1,headers:UWB(y),body:f});if(Y=!0,J({response:c}),Z)O.close(),this.connectionManager.deleteSession(L,O)}),D)f.setTimeout(D,()=>{f.close();let y=new Error(`Stream timed out because of no activity for ${D} ms`);y.name="TimeoutError",R(y)});if(Q)Q.onabort=()=>{f.close();let y=new Error("Request aborted");y.name="AbortError",R(y)};f.on("frameError",(y,c,h)=>{R(new Error(`Frame type id ${y} in stream id ${h} has failed with code ${c}.`))}),f.on("error",R),f.on("aborted",()=>{R(new Error(`HTTP/2 stream is abnormally aborted in mid-communication with result code ${f.rstCode}.`))}),f.on("close",()=>{if(O.unref(),Z)O.destroy();if(!Y)R(new Error("Unexpected error: http2 request did not get a response"))}),W=Mz0(f,B,D)})}updateHttpClientConfig(B,Q){this.config=void 0,this.configProvider=this.configProvider.then((D)=>{return{...D,[B]:Q}})}httpHandlerConfigs(){return this.config??{}}destroySession(B){if(!B.destroyed)B.destroy()}};RI(MWB,"NodeHttp2Handler");var fm6=MWB,RWB=class A extends wWB.Writable{constructor(){super(...arguments);this.bufferedBytes=[]}_write(B,Q,D){this.bufferedBytes.push(B),D()}};RI(RWB,"Collector");var hm6=RWB,gm6=RI((A)=>new Promise((B,Q)=>{let D=new hm6;A.pipe(D),A.on("error",(Z)=>{D.end(),Q(Z)}),D.on("error",Q),D.on("finish",function(){let Z=new Uint8Array(Buffer.concat(this.bufferedBytes));B(Z)})}),"streamCollector")});
var Uz0=E((h03,sYB)=>{var{defineProperty:Pk1,getOwnPropertyDescriptor:uu6,getOwnPropertyNames:mu6}=Object,du6=Object.prototype.hasOwnProperty,nYB=(A,B)=>Pk1(A,"name",{value:B,configurable:!0}),cu6=(A,B)=>{for(var Q in B)Pk1(A,Q,{get:B[Q],enumerable:!0})},lu6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of mu6(B))if(!du6.call(A,Z)&&Z!==Q)Pk1(A,Z,{get:()=>B[Z],enumerable:!(D=uu6(B,Z))||D.enumerable})}return A},pu6=(A)=>lu6(Pk1({},"__esModule",{value:!0}),A),aYB={};cu6(aYB,{fromArrayBuffer:()=>nu6,fromString:()=>au6});sYB.exports=pu6(aYB);var iu6=iYB(),Ez0=J1("buffer"),nu6=nYB((A,B=0,Q=A.byteLength-B)=>{if(!iu6.isArrayBuffer(A))throw new TypeError(`The "input" argument must be ArrayBuffer. Received type ${typeof A} (${A})`);return Ez0.Buffer.from(A,B,Q)},"fromArrayBuffer"),au6=nYB((A,B)=>{if(typeof A!=="string")throw new TypeError(`The "input" argument must be of type string. Received type ${typeof A} (${A})`);return B?Ez0.Buffer.from(A,B):Ez0.Buffer.from(A)},"fromString")});
var XWB=E((d03,JWB)=>{var{defineProperty:yk1,getOwnPropertyDescriptor:Cm6,getOwnPropertyNames:Km6}=Object,Hm6=Object.prototype.hasOwnProperty,zm6=(A,B)=>yk1(A,"name",{value:B,configurable:!0}),Em6=(A,B)=>{for(var Q in B)yk1(A,Q,{get:B[Q],enumerable:!0})},Um6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Km6(B))if(!Hm6.call(A,Z)&&Z!==Q)yk1(A,Z,{get:()=>B[Z],enumerable:!(D=Cm6(B,Z))||D.enumerable})}return A},wm6=(A)=>Um6(yk1({},"__esModule",{value:!0}),A),YWB={};Em6(YWB,{buildQueryString:()=>WWB});JWB.exports=wm6(YWB);var qz0=IWB();function WWB(A){let B=[];for(let Q of Object.keys(A).sort()){let D=A[Q];if(Q=qz0.escapeUri(Q),Array.isArray(D))for(let Z=0,G=D.length;Z<G;Z++)B.push(`${Q}=${qz0.escapeUri(D[Z])}`);else{let Z=Q;if(D||typeof D==="string")Z+=`=${qz0.escapeUri(D)}`;B.push(Z)}}return B.join("&")}zm6(WWB,"buildQueryString")});
var YJB=E((i03,IJB)=>{var{defineProperty:hk1,getOwnPropertyDescriptor:sm6,getOwnPropertyNames:rm6}=Object,om6=Object.prototype.hasOwnProperty,_2=(A,B)=>hk1(A,"name",{value:B,configurable:!0}),tm6=(A,B)=>{for(var Q in B)hk1(A,Q,{get:B[Q],enumerable:!0})},em6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of rm6(B))if(!om6.call(A,Z)&&Z!==Q)hk1(A,Z,{get:()=>B[Z],enumerable:!(D=sm6(B,Z))||D.enumerable})}return A},Ad6=(A)=>em6(hk1({},"__esModule",{value:!0}),A),uWB={};tm6(uWB,{Client:()=>Qd6,Command:()=>pWB,LazyJsonString:()=>td6,NoOpLogger:()=>Bd6,SENSITIVE_STRING:()=>Gd6,ServiceException:()=>ud6,StringWrapper:()=>nD1,_json:()=>vz0,collectBody:()=>Dd6,convertMap:()=>ed6,createAggregatedClient:()=>Fd6,dateToUtcString:()=>tWB,decorateServiceException:()=>AJB,emitWarningIfUnsupportedVersion:()=>ld6,expectBoolean:()=>Yd6,expectByte:()=>xz0,expectFloat32:()=>vk1,expectInt:()=>Jd6,expectInt32:()=>kz0,expectLong:()=>pD1,expectNonNull:()=>Vd6,expectNumber:()=>lD1,expectObject:()=>nWB,expectShort:()=>_z0,expectString:()=>Cd6,expectUnion:()=>Kd6,extendedEncodeURIComponent:()=>fk1,getArrayIfSingleItem:()=>od6,getDefaultClientConfiguration:()=>sd6,getDefaultExtensionConfiguration:()=>QJB,getValueFromTextNode:()=>DJB,handleFloat:()=>Ed6,limitedParseDouble:()=>hz0,limitedParseFloat:()=>Ud6,limitedParseFloat32:()=>wd6,loadConfigsForDefaultMode:()=>cd6,logger:()=>iD1,map:()=>uz0,parseBoolean:()=>Id6,parseEpochTimestamp:()=>yd6,parseRfc3339DateTime:()=>Md6,parseRfc3339DateTimeWithOffset:()=>Od6,parseRfc7231DateTime:()=>jd6,resolveDefaultRuntimeConfig:()=>rd6,resolvedPath:()=>Zc6,serializeFloat:()=>Gc6,splitEvery:()=>FJB,strictParseByte:()=>oWB,strictParseDouble:()=>fz0,strictParseFloat:()=>Hd6,strictParseFloat32:()=>aWB,strictParseInt:()=>$d6,strictParseInt32:()=>qd6,strictParseLong:()=>rWB,strictParseShort:()=>Ne,take:()=>Ac6,throwDefaultError:()=>BJB,withBaseException:()=>md6});IJB.exports=Ad6(uWB);var mWB=class A{trace(){}debug(){}info(){}warn(){}error(){}};_2(mWB,"NoOpLogger");var Bd6=mWB,dWB=cYB(),cWB=class A{constructor(B){this.middlewareStack=dWB.constructStack(),this.config=B}send(B,Q,D){let Z=typeof Q!=="function"?Q:void 0,G=typeof Q==="function"?Q:D,F=B.resolveMiddleware(this.middlewareStack,this.config,Z);if(G)F(B).then((I)=>G(null,I.output),(I)=>G(I)).catch(()=>{});else return F(B).then((I)=>I.output)}destroy(){if(this.config.requestHandler.destroy)this.config.requestHandler.destroy()}};_2(cWB,"Client");var Qd6=cWB,Sz0=hWB(),Dd6=_2(async(A=new Uint8Array,B)=>{if(A instanceof Uint8Array)return Sz0.Uint8ArrayBlobAdapter.mutate(A);if(!A)return Sz0.Uint8ArrayBlobAdapter.mutate(new Uint8Array);let Q=B.streamCollector(A);return Sz0.Uint8ArrayBlobAdapter.mutate(await Q)},"collectBody"),yz0=lH0(),lWB=class A{constructor(){this.middlewareStack=dWB.constructStack()}static classBuilder(){return new Zd6}resolveMiddlewareWithContext(B,Q,D,{middlewareFn:Z,clientName:G,commandName:F,inputFilterSensitiveLog:I,outputFilterSensitiveLog:Y,smithyContext:W,additionalContext:J,CommandCtor:X}){for(let z of Z.bind(this)(X,B,Q,D))this.middlewareStack.use(z);let V=B.concat(this.middlewareStack),{logger:C}=Q,K={logger:C,clientName:G,commandName:F,inputFilterSensitiveLog:I,outputFilterSensitiveLog:Y,[yz0.SMITHY_CONTEXT_KEY]:{...W},...J},{requestHandler:H}=Q;return V.resolve((z)=>H.handle(z.request,D||{}),K)}};_2(lWB,"Command");var pWB=lWB,iWB=class A{constructor(){this._init=()=>{},this._ep={},this._middlewareFn=()=>[],this._commandName="",this._clientName="",this._additionalContext={},this._smithyContext={},this._inputFilterSensitiveLog=(B)=>B,this._outputFilterSensitiveLog=(B)=>B,this._serializer=null,this._deserializer=null}init(B){this._init=B}ep(B){return this._ep=B,this}m(B){return this._middlewareFn=B,this}s(B,Q,D={}){return this._smithyContext={service:B,operation:Q,...D},this}c(B={}){return this._additionalContext=B,this}n(B,Q){return this._clientName=B,this._commandName=Q,this}f(B=(D)=>D,Q=(D)=>D){return this._inputFilterSensitiveLog=B,this._outputFilterSensitiveLog=Q,this}ser(B){return this._serializer=B,this}de(B){return this._deserializer=B,this}build(){var B;let Q=this,D;return D=(B=class extends pWB{constructor(...[Z]){super();this.serialize=Q._serializer,this.deserialize=Q._deserializer,this.input=Z??{},Q._init(this)}static getEndpointParameterInstructions(){return Q._ep}resolveMiddleware(Z,G,F){return this.resolveMiddlewareWithContext(Z,G,F,{CommandCtor:D,middlewareFn:Q._middlewareFn,clientName:Q._clientName,commandName:Q._commandName,inputFilterSensitiveLog:Q._inputFilterSensitiveLog,outputFilterSensitiveLog:Q._outputFilterSensitiveLog,smithyContext:Q._smithyContext,additionalContext:Q._additionalContext})}},_2(B,"CommandRef"),B)}};_2(iWB,"ClassBuilder");var Zd6=iWB,Gd6="***SensitiveInformation***",Fd6=_2((A,B)=>{for(let Q of Object.keys(A)){let D=A[Q],Z=_2(async function(F,I,Y){let W=new D(F);if(typeof I==="function")this.send(W,I);else if(typeof Y==="function"){if(typeof I!=="object")throw new Error(`Expected http options but got ${typeof I}`);this.send(W,I||{},Y)}else return this.send(W,I)},"methodImpl"),G=(Q[0].toLowerCase()+Q.slice(1)).replace(/Command$/,"");B.prototype[G]=Z}},"createAggregatedClient"),Id6=_2((A)=>{switch(A){case"true":return!0;case"false":return!1;default:throw new Error(`Unable to parse boolean value "${A}"`)}},"parseBoolean"),Yd6=_2((A)=>{if(A===null||A===void 0)return;if(typeof A==="number"){if(A===0||A===1)iD1.warn(bk1(`Expected boolean, got ${typeof A}: ${A}`));if(A===0)return!1;if(A===1)return!0}if(typeof A==="string"){let B=A.toLowerCase();if(B==="false"||B==="true")iD1.warn(bk1(`Expected boolean, got ${typeof A}: ${A}`));if(B==="false")return!1;if(B==="true")return!0}if(typeof A==="boolean")return A;throw new TypeError(`Expected boolean, got ${typeof A}: ${A}`)},"expectBoolean"),lD1=_2((A)=>{if(A===null||A===void 0)return;if(typeof A==="string"){let B=parseFloat(A);if(!Number.isNaN(B)){if(String(B)!==String(A))iD1.warn(bk1(`Expected number but observed string: ${A}`));return B}}if(typeof A==="number")return A;throw new TypeError(`Expected number, got ${typeof A}: ${A}`)},"expectNumber"),Wd6=Math.ceil(340282346638528860000000000000000000000),vk1=_2((A)=>{let B=lD1(A);if(B!==void 0&&!Number.isNaN(B)&&B!==1/0&&B!==-1/0){if(Math.abs(B)>Wd6)throw new TypeError(`Expected 32-bit float, got ${A}`)}return B},"expectFloat32"),pD1=_2((A)=>{if(A===null||A===void 0)return;if(Number.isInteger(A)&&!Number.isNaN(A))return A;throw new TypeError(`Expected integer, got ${typeof A}: ${A}`)},"expectLong"),Jd6=pD1,kz0=_2((A)=>bz0(A,32),"expectInt32"),_z0=_2((A)=>bz0(A,16),"expectShort"),xz0=_2((A)=>bz0(A,8),"expectByte"),bz0=_2((A,B)=>{let Q=pD1(A);if(Q!==void 0&&Xd6(Q,B)!==Q)throw new TypeError(`Expected ${B}-bit integer, got ${A}`);return Q},"expectSizedInt"),Xd6=_2((A,B)=>{switch(B){case 32:return Int32Array.of(A)[0];case 16:return Int16Array.of(A)[0];case 8:return Int8Array.of(A)[0]}},"castInt"),Vd6=_2((A,B)=>{if(A===null||A===void 0){if(B)throw new TypeError(`Expected a non-null value for ${B}`);throw new TypeError("Expected a non-null value")}return A},"expectNonNull"),nWB=_2((A)=>{if(A===null||A===void 0)return;if(typeof A==="object"&&!Array.isArray(A))return A;let B=Array.isArray(A)?"array":typeof A;throw new TypeError(`Expected object, got ${B}: ${A}`)},"expectObject"),Cd6=_2((A)=>{if(A===null||A===void 0)return;if(typeof A==="string")return A;if(["boolean","number","bigint"].includes(typeof A))return iD1.warn(bk1(`Expected string, got ${typeof A}: ${A}`)),String(A);throw new TypeError(`Expected string, got ${typeof A}: ${A}`)},"expectString"),Kd6=_2((A)=>{if(A===null||A===void 0)return;let B=nWB(A),Q=Object.entries(B).filter(([,D])=>D!=null).map(([D])=>D);if(Q.length===0)throw new TypeError("Unions must have exactly one non-null member. None were found.");if(Q.length>1)throw new TypeError(`Unions must have exactly one non-null member. Keys ${Q} were not null.`);return B},"expectUnion"),fz0=_2((A)=>{if(typeof A=="string")return lD1(Me(A));return lD1(A)},"strictParseDouble"),Hd6=fz0,aWB=_2((A)=>{if(typeof A=="string")return vk1(Me(A));return vk1(A)},"strictParseFloat32"),zd6=/(-?(?:0|[1-9]\d*)(?:\.\d+)?(?:[eE][+-]?\d+)?)|(-?Infinity)|(NaN)/g,Me=_2((A)=>{let B=A.match(zd6);if(B===null||B[0].length!==A.length)throw new TypeError("Expected real number, got implicit NaN");return parseFloat(A)},"parseNumber"),hz0=_2((A)=>{if(typeof A=="string")return sWB(A);return lD1(A)},"limitedParseDouble"),Ed6=hz0,Ud6=hz0,wd6=_2((A)=>{if(typeof A=="string")return sWB(A);return vk1(A)},"limitedParseFloat32"),sWB=_2((A)=>{switch(A){case"NaN":return NaN;case"Infinity":return 1/0;case"-Infinity":return-1/0;default:throw new Error(`Unable to parse float value: ${A}`)}},"parseFloatString"),rWB=_2((A)=>{if(typeof A==="string")return pD1(Me(A));return pD1(A)},"strictParseLong"),$d6=rWB,qd6=_2((A)=>{if(typeof A==="string")return kz0(Me(A));return kz0(A)},"strictParseInt32"),Ne=_2((A)=>{if(typeof A==="string")return _z0(Me(A));return _z0(A)},"strictParseShort"),oWB=_2((A)=>{if(typeof A==="string")return xz0(Me(A));return xz0(A)},"strictParseByte"),bk1=_2((A)=>{return String(new TypeError(A).stack||A).split(`
`).slice(0,5).filter((B)=>!B.includes("stackTraceWarning")).join(`
`)},"stackTraceWarning"),iD1={warn:console.warn},Nd6=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],gz0=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function tWB(A){let B=A.getUTCFullYear(),Q=A.getUTCMonth(),D=A.getUTCDay(),Z=A.getUTCDate(),G=A.getUTCHours(),F=A.getUTCMinutes(),I=A.getUTCSeconds(),Y=Z<10?`0${Z}`:`${Z}`,W=G<10?`0${G}`:`${G}`,J=F<10?`0${F}`:`${F}`,X=I<10?`0${I}`:`${I}`;return`${Nd6[D]}, ${Y} ${gz0[Q]} ${B} ${W}:${J}:${X} GMT`}_2(tWB,"dateToUtcString");var Ld6=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?[zZ]$/),Md6=_2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-3339 date-times must be expressed as strings");let B=Ld6.exec(A);if(!B)throw new TypeError("Invalid RFC-3339 date-time value");let[Q,D,Z,G,F,I,Y,W]=B,J=Ne(Le(D)),X=PM(Z,"month",1,12),V=PM(G,"day",1,31);return cD1(J,X,V,{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W})},"parseRfc3339DateTime"),Rd6=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?(([-+]\d{2}\:\d{2})|[zZ])$/),Od6=_2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-3339 date-times must be expressed as strings");let B=Rd6.exec(A);if(!B)throw new TypeError("Invalid RFC-3339 date-time value");let[Q,D,Z,G,F,I,Y,W,J]=B,X=Ne(Le(D)),V=PM(Z,"month",1,12),C=PM(G,"day",1,31),K=cD1(X,V,C,{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W});if(J.toUpperCase()!="Z")K.setTime(K.getTime()-gd6(J));return K},"parseRfc3339DateTimeWithOffset"),Td6=new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d{2}) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/),Pd6=new RegExp(/^(?:Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d{2})-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? GMT$/),Sd6=new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( [1-9]|\d{2}) (\d{1,2}):(\d{2}):(\d{2})(?:\.(\d+))? (\d{4})$/),jd6=_2((A)=>{if(A===null||A===void 0)return;if(typeof A!=="string")throw new TypeError("RFC-7231 date-times must be expressed as strings");let B=Td6.exec(A);if(B){let[Q,D,Z,G,F,I,Y,W]=B;return cD1(Ne(Le(G)),jz0(Z),PM(D,"day",1,31),{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W})}if(B=Pd6.exec(A),B){let[Q,D,Z,G,F,I,Y,W]=B;return xd6(cD1(kd6(G),jz0(Z),PM(D,"day",1,31),{hours:F,minutes:I,seconds:Y,fractionalMilliseconds:W}))}if(B=Sd6.exec(A),B){let[Q,D,Z,G,F,I,Y,W]=B;return cD1(Ne(Le(W)),jz0(D),PM(Z.trimLeft(),"day",1,31),{hours:G,minutes:F,seconds:I,fractionalMilliseconds:Y})}throw new TypeError("Invalid RFC-7231 date-time value")},"parseRfc7231DateTime"),yd6=_2((A)=>{if(A===null||A===void 0)return;let B;if(typeof A==="number")B=A;else if(typeof A==="string")B=fz0(A);else throw new TypeError("Epoch timestamps must be expressed as floating point numbers or their string representation");if(Number.isNaN(B)||B===1/0||B===-1/0)throw new TypeError("Epoch timestamps must be valid, non-Infinite, non-NaN numerics");return new Date(Math.round(B*1000))},"parseEpochTimestamp"),cD1=_2((A,B,Q,D)=>{let Z=B-1;return bd6(A,Z,Q),new Date(Date.UTC(A,Z,Q,PM(D.hours,"hour",0,23),PM(D.minutes,"minute",0,59),PM(D.seconds,"seconds",0,60),hd6(D.fractionalMilliseconds)))},"buildDate"),kd6=_2((A)=>{let B=new Date().getUTCFullYear(),Q=Math.floor(B/100)*100+Ne(Le(A));if(Q<B)return Q+100;return Q},"parseTwoDigitYear"),_d6=1576800000000,xd6=_2((A)=>{if(A.getTime()-new Date().getTime()>_d6)return new Date(Date.UTC(A.getUTCFullYear()-100,A.getUTCMonth(),A.getUTCDate(),A.getUTCHours(),A.getUTCMinutes(),A.getUTCSeconds(),A.getUTCMilliseconds()));return A},"adjustRfc850Year"),jz0=_2((A)=>{let B=gz0.indexOf(A);if(B<0)throw new TypeError(`Invalid month: ${A}`);return B+1},"parseMonthByShortName"),vd6=[31,28,31,30,31,30,31,31,30,31,30,31],bd6=_2((A,B,Q)=>{let D=vd6[B];if(B===1&&fd6(A))D=29;if(Q>D)throw new TypeError(`Invalid day for ${gz0[B]} in ${A}: ${Q}`)},"validateDayOfMonth"),fd6=_2((A)=>{return A%4===0&&(A%100!==0||A%400===0)},"isLeapYear"),PM=_2((A,B,Q,D)=>{let Z=oWB(Le(A));if(Z<Q||Z>D)throw new TypeError(`${B} must be between ${Q} and ${D}, inclusive`);return Z},"parseDateValue"),hd6=_2((A)=>{if(A===null||A===void 0)return 0;return aWB("0."+A)*1000},"parseMilliseconds"),gd6=_2((A)=>{let B=A[0],Q=1;if(B=="+")Q=1;else if(B=="-")Q=-1;else throw new TypeError(`Offset direction, ${B}, must be "+" or "-"`);let D=Number(A.substring(1,3)),Z=Number(A.substring(4,6));return Q*(D*60+Z)*60*1000},"parseOffsetToMilliseconds"),Le=_2((A)=>{let B=0;while(B<A.length-1&&A.charAt(B)==="0")B++;if(B===0)return A;return A.slice(B)},"stripLeadingZeroes"),eWB=class A extends Error{constructor(B){super(B.message);Object.setPrototypeOf(this,A.prototype),this.name=B.name,this.$fault=B.$fault,this.$metadata=B.$metadata}};_2(eWB,"ServiceException");var ud6=eWB,AJB=_2((A,B={})=>{Object.entries(B).filter(([,D])=>D!==void 0).forEach(([D,Z])=>{if(A[D]==null||A[D]==="")A[D]=Z});let Q=A.message||A.Message||"UnknownError";return A.message=Q,delete A.Message,A},"decorateServiceException"),BJB=_2(({output:A,parsedBody:B,exceptionCtor:Q,errorCode:D})=>{let Z=dd6(A),G=Z.httpStatusCode?Z.httpStatusCode+"":void 0,F=new Q({name:(B==null?void 0:B.code)||(B==null?void 0:B.Code)||D||G||"UnknownError",$fault:"client",$metadata:Z});throw AJB(F,B)},"throwDefaultError"),md6=_2((A)=>{return({output:B,parsedBody:Q,errorCode:D})=>{BJB({output:B,parsedBody:Q,exceptionCtor:A,errorCode:D})}},"withBaseException"),dd6=_2((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),cd6=_2((A)=>{switch(A){case"standard":return{retryMode:"standard",connectionTimeout:3100};case"in-region":return{retryMode:"standard",connectionTimeout:1100};case"cross-region":return{retryMode:"standard",connectionTimeout:3100};case"mobile":return{retryMode:"standard",connectionTimeout:30000};default:return{}}},"loadConfigsForDefaultMode"),gWB=!1,ld6=_2((A)=>{if(A&&!gWB&&parseInt(A.substring(1,A.indexOf(".")))<14)gWB=!0},"emitWarningIfUnsupportedVersion"),pd6=_2((A)=>{let B=[];for(let Q in yz0.AlgorithmId){let D=yz0.AlgorithmId[Q];if(A[D]===void 0)continue;B.push({algorithmId:()=>D,checksumConstructor:()=>A[D]})}return{_checksumAlgorithms:B,addChecksumAlgorithm(Q){this._checksumAlgorithms.push(Q)},checksumAlgorithms(){return this._checksumAlgorithms}}},"getChecksumConfiguration"),id6=_2((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),nd6=_2((A)=>{let B=A.retryStrategy;return{setRetryStrategy(Q){B=Q},retryStrategy(){return B}}},"getRetryConfiguration"),ad6=_2((A)=>{let B={};return B.retryStrategy=A.retryStrategy(),B},"resolveRetryRuntimeConfig"),QJB=_2((A)=>{return{...pd6(A),...nd6(A)}},"getDefaultExtensionConfiguration"),sd6=QJB,rd6=_2((A)=>{return{...id6(A),...ad6(A)}},"resolveDefaultRuntimeConfig");function fk1(A){return encodeURIComponent(A).replace(/[!'()*]/g,function(B){return"%"+B.charCodeAt(0).toString(16).toUpperCase()})}_2(fk1,"extendedEncodeURIComponent");var od6=_2((A)=>Array.isArray(A)?A:[A],"getArrayIfSingleItem"),DJB=_2((A)=>{for(let Q in A)if(A.hasOwnProperty(Q)&&A[Q]["#text"]!==void 0)A[Q]=A[Q]["#text"];else if(typeof A[Q]==="object"&&A[Q]!==null)A[Q]=DJB(A[Q]);return A},"getValueFromTextNode"),nD1=_2(function(){let A=Object.getPrototypeOf(this).constructor,Q=new(Function.bind.apply(String,[null,...arguments]));return Object.setPrototypeOf(Q,A.prototype),Q},"StringWrapper");nD1.prototype=Object.create(String.prototype,{constructor:{value:nD1,enumerable:!1,writable:!0,configurable:!0}});Object.setPrototypeOf(nD1,String);var ZJB=class A extends nD1{deserializeJSON(){return JSON.parse(super.toString())}toJSON(){return super.toString()}static fromObject(B){if(B instanceof A)return B;else if(B instanceof String||typeof B==="string")return new A(B);return new A(JSON.stringify(B))}};_2(ZJB,"LazyJsonString");var td6=ZJB;function uz0(A,B,Q){let D,Z,G;if(typeof B==="undefined"&&typeof Q==="undefined")D={},G=A;else if(D=A,typeof B==="function")return Z=B,G=Q,Bc6(D,Z,G);else G=B;for(let F of Object.keys(G)){if(!Array.isArray(G[F])){D[F]=G[F];continue}GJB(D,null,G,F)}return D}_2(uz0,"map");var ed6=_2((A)=>{let B={};for(let[Q,D]of Object.entries(A||{}))B[Q]=[,D];return B},"convertMap"),Ac6=_2((A,B)=>{let Q={};for(let D in B)GJB(Q,A,B,D);return Q},"take"),Bc6=_2((A,B,Q)=>{return uz0(A,Object.entries(Q).reduce((D,[Z,G])=>{if(Array.isArray(G))D[Z]=G;else if(typeof G==="function")D[Z]=[B,G()];else D[Z]=[B,G];return D},{}))},"mapWithFilter"),GJB=_2((A,B,Q,D)=>{if(B!==null){let F=Q[D];if(typeof F==="function")F=[,F];let[I=Qc6,Y=Dc6,W=D]=F;if(typeof I==="function"&&I(B[W])||typeof I!=="function"&&!!I)A[D]=Y(B[W]);return}let[Z,G]=Q[D];if(typeof G==="function"){let F,I=Z===void 0&&(F=G())!=null,Y=typeof Z==="function"&&!!Z(void 0)||typeof Z!=="function"&&!!Z;if(I)A[D]=F;else if(Y)A[D]=G()}else{let F=Z===void 0&&G!=null,I=typeof Z==="function"&&!!Z(G)||typeof Z!=="function"&&!!Z;if(F||I)A[D]=G}},"applyInstruction"),Qc6=_2((A)=>A!=null,"nonNullish"),Dc6=_2((A)=>A,"pass"),Zc6=_2((A,B,Q,D,Z,G)=>{if(B!=null&&B[Q]!==void 0){let F=D();if(F.length<=0)throw new Error("Empty value provided for input HTTP label: "+Q+".");A=A.replace(Z,G?F.split("/").map((I)=>fk1(I)).join("/"):fk1(F))}else throw new Error("No value provided for input HTTP label: "+Q+".");return A},"resolvedPath"),Gc6=_2((A)=>{if(A!==A)return"NaN";switch(A){case 1/0:return"Infinity";case-1/0:return"-Infinity";default:return A}},"serializeFloat"),vz0=_2((A)=>{if(A==null)return{};if(Array.isArray(A))return A.filter((B)=>B!=null).map(vz0);if(typeof A==="object"){let B={};for(let Q of Object.keys(A)){if(A[Q]==null)continue;B[Q]=vz0(A[Q])}return B}return A},"_json");function FJB(A,B,Q){if(Q<=0||!Number.isInteger(Q))throw new Error("Invalid number of delimiters ("+Q+") for splitEvery.");let D=A.split(B);if(Q===1)return D;let Z=[],G="";for(let F=0;F<D.length;F++){if(G==="")G=D[F];else G+=B+D[F];if((F+1)%Q===0)Z.push(G),G=""}if(G!=="")Z.push(G);return Z}_2(FJB,"splitEvery")});
var _YB=E((_03,kYB)=>{var{defineProperty:Mk1,getOwnPropertyDescriptor:Xu6,getOwnPropertyNames:Vu6}=Object,Cu6=Object.prototype.hasOwnProperty,Vz0=(A,B)=>Mk1(A,"name",{value:B,configurable:!0}),Ku6=(A,B)=>{for(var Q in B)Mk1(A,Q,{get:B[Q],enumerable:!0})},Hu6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Vu6(B))if(!Cu6.call(A,Z)&&Z!==Q)Mk1(A,Z,{get:()=>B[Z],enumerable:!(D=Xu6(B,Z))||D.enumerable})}return A},zu6=(A)=>Hu6(Mk1({},"__esModule",{value:!0}),A),SYB={};Ku6(SYB,{fromUtf8:()=>yYB,toUint8Array:()=>Eu6,toUtf8:()=>Uu6});kYB.exports=zu6(SYB);var jYB=Lk1(),yYB=Vz0((A)=>{let B=jYB.fromString(A,"utf8");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength/Uint8Array.BYTES_PER_ELEMENT)},"fromUtf8"),Eu6=Vz0((A)=>{if(typeof A==="string")return yYB(A);if(ArrayBuffer.isView(A))return new Uint8Array(A.buffer,A.byteOffset,A.byteLength/Uint8Array.BYTES_PER_ELEMENT);return new Uint8Array(A)},"toUint8Array"),Uu6=Vz0((A)=>{if(typeof A==="string")return A;if(typeof A!=="object"||typeof A.byteOffset!=="number"||typeof A.byteLength!=="number")throw new Error("@smithy/util-utf8: toUtf8 encoder function only accepts string | Uint8Array.");return jYB.fromArrayBuffer(A.buffer,A.byteOffset,A.byteLength).toString("utf8")},"toUtf8")});
var bYB=E((xYB)=>{Object.defineProperty(xYB,"__esModule",{value:!0});xYB.toBase64=void 0;var wu6=Lk1(),$u6=_YB(),qu6=(A)=>{let B;if(typeof A==="string")B=$u6.fromUtf8(A);else B=A;if(typeof B!=="object"||typeof B.byteOffset!=="number"||typeof B.byteLength!=="number")throw new Error("@smithy/util-base64: toBase64 encoder function only accepts string | Uint8Array.");return wu6.fromArrayBuffer(B.buffer,B.byteOffset,B.byteLength).toString("base64")};xYB.toBase64=qu6});
var cYB=E((b03,dYB)=>{var{defineProperty:Ok1,getOwnPropertyDescriptor:Ou6,getOwnPropertyNames:Tu6}=Object,Pu6=Object.prototype.hasOwnProperty,bE=(A,B)=>Ok1(A,"name",{value:B,configurable:!0}),Su6=(A,B)=>{for(var Q in B)Ok1(A,Q,{get:B[Q],enumerable:!0})},ju6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Tu6(B))if(!Pu6.call(A,Z)&&Z!==Q)Ok1(A,Z,{get:()=>B[Z],enumerable:!(D=Ou6(B,Z))||D.enumerable})}return A},yu6=(A)=>ju6(Ok1({},"__esModule",{value:!0}),A),mYB={};Su6(mYB,{constructStack:()=>zz0});dYB.exports=yu6(mYB);var zm=bE((A,B)=>{let Q=[];if(A)Q.push(A);if(B)for(let D of B)Q.push(D);return Q},"getAllAliases"),hx=bE((A,B)=>{return`${A||"anonymous"}${B&&B.length>0?` (a.k.a. ${B.join(",")})`:""}`},"getMiddlewareNameWithAliases"),zz0=bE(()=>{let A=[],B=[],Q=!1,D=new Set,Z=bE((X)=>X.sort((V,C)=>gYB[C.step]-gYB[V.step]||uYB[C.priority||"normal"]-uYB[V.priority||"normal"]),"sort"),G=bE((X)=>{let V=!1,C=bE((K)=>{let H=zm(K.name,K.aliases);if(H.includes(X)){V=!0;for(let z of H)D.delete(z);return!1}return!0},"filterCb");return A=A.filter(C),B=B.filter(C),V},"removeByName"),F=bE((X)=>{let V=!1,C=bE((K)=>{if(K.middleware===X){V=!0;for(let H of zm(K.name,K.aliases))D.delete(H);return!1}return!0},"filterCb");return A=A.filter(C),B=B.filter(C),V},"removeByReference"),I=bE((X)=>{var V;return A.forEach((C)=>{X.add(C.middleware,{...C})}),B.forEach((C)=>{X.addRelativeTo(C.middleware,{...C})}),(V=X.identifyOnResolve)==null||V.call(X,J.identifyOnResolve()),X},"cloneTo"),Y=bE((X)=>{let V=[];return X.before.forEach((C)=>{if(C.before.length===0&&C.after.length===0)V.push(C);else V.push(...Y(C))}),V.push(X),X.after.reverse().forEach((C)=>{if(C.before.length===0&&C.after.length===0)V.push(C);else V.push(...Y(C))}),V},"expandRelativeMiddlewareList"),W=bE((X=!1)=>{let V=[],C=[],K={};return A.forEach((z)=>{let $={...z,before:[],after:[]};for(let L of zm($.name,$.aliases))K[L]=$;V.push($)}),B.forEach((z)=>{let $={...z,before:[],after:[]};for(let L of zm($.name,$.aliases))K[L]=$;C.push($)}),C.forEach((z)=>{if(z.toMiddleware){let $=K[z.toMiddleware];if($===void 0){if(X)return;throw new Error(`${z.toMiddleware} is not found when adding ${hx(z.name,z.aliases)} middleware ${z.relation} ${z.toMiddleware}`)}if(z.relation==="after")$.after.push(z);if(z.relation==="before")$.before.push(z)}}),Z(V).map(Y).reduce((z,$)=>{return z.push(...$),z},[])},"getMiddlewareList"),J={add:(X,V={})=>{let{name:C,override:K,aliases:H}=V,z={step:"initialize",priority:"normal",middleware:X,...V},$=zm(C,H);if($.length>0){if($.some((L)=>D.has(L))){if(!K)throw new Error(`Duplicate middleware name '${hx(C,H)}'`);for(let L of $){let N=A.findIndex((R)=>{var T;return R.name===L||((T=R.aliases)==null?void 0:T.some((j)=>j===L))});if(N===-1)continue;let O=A[N];if(O.step!==z.step||z.priority!==O.priority)throw new Error(`"${hx(O.name,O.aliases)}" middleware with ${O.priority} priority in ${O.step} step cannot be overridden by "${hx(C,H)}" middleware with ${z.priority} priority in ${z.step} step.`);A.splice(N,1)}}for(let L of $)D.add(L)}A.push(z)},addRelativeTo:(X,V)=>{let{name:C,override:K,aliases:H}=V,z={middleware:X,...V},$=zm(C,H);if($.length>0){if($.some((L)=>D.has(L))){if(!K)throw new Error(`Duplicate middleware name '${hx(C,H)}'`);for(let L of $){let N=B.findIndex((R)=>{var T;return R.name===L||((T=R.aliases)==null?void 0:T.some((j)=>j===L))});if(N===-1)continue;let O=B[N];if(O.toMiddleware!==z.toMiddleware||O.relation!==z.relation)throw new Error(`"${hx(O.name,O.aliases)}" middleware ${O.relation} "${O.toMiddleware}" middleware cannot be overridden by "${hx(C,H)}" middleware ${z.relation} "${z.toMiddleware}" middleware.`);B.splice(N,1)}}for(let L of $)D.add(L)}B.push(z)},clone:()=>I(zz0()),use:(X)=>{X.applyToStack(J)},remove:(X)=>{if(typeof X==="string")return G(X);else return F(X)},removeByTag:(X)=>{let V=!1,C=bE((K)=>{let{tags:H,name:z,aliases:$}=K;if(H&&H.includes(X)){let L=zm(z,$);for(let N of L)D.delete(N);return V=!0,!1}return!0},"filterCb");return A=A.filter(C),B=B.filter(C),V},concat:(X)=>{var V;let C=I(zz0());return C.use(X),C.identifyOnResolve(Q||C.identifyOnResolve()||(((V=X.identifyOnResolve)==null?void 0:V.call(X))??!1)),C},applyToStack:I,identify:()=>{return W(!0).map((X)=>{let V=X.step??X.relation+" "+X.toMiddleware;return hx(X.name,X.aliases)+" - "+V})},identifyOnResolve(X){if(typeof X==="boolean")Q=X;return Q},resolve:(X,V)=>{for(let C of W().map((K)=>K.middleware).reverse())X=C(X,V);if(Q)console.log(J.identify());return X}};return J},"constructStack"),gYB={initialize:5,serialize:4,build:3,finalizeRequest:2,deserialize:1},uYB={high:3,normal:2,low:1}});
var hWB=E((p03,xk1)=>{var{defineProperty:kk1,getOwnPropertyDescriptor:lm6,getOwnPropertyNames:pm6}=Object,im6=Object.prototype.hasOwnProperty,Pz0=(A,B)=>kk1(A,"name",{value:B,configurable:!0}),nm6=(A,B)=>{for(var Q in B)kk1(A,Q,{get:B[Q],enumerable:!0})},Oz0=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of pm6(B))if(!im6.call(A,Z)&&Z!==Q)kk1(A,Z,{get:()=>B[Z],enumerable:!(D=lm6(B,Z))||D.enumerable})}return A},kWB=(A,B,Q)=>(Oz0(A,B,"default"),Q&&Oz0(Q,B,"default")),am6=(A)=>Oz0(kk1({},"__esModule",{value:!0}),A),_k1={};nm6(_k1,{Uint8ArrayBlobAdapter:()=>Tz0});xk1.exports=am6(_k1);var _WB=Hz0(),xWB=AWB();function vWB(A,B="utf-8"){if(B==="base64")return _WB.toBase64(A);return xWB.toUtf8(A)}Pz0(vWB,"transformToString");function bWB(A,B){if(B==="base64")return Tz0.mutate(_WB.fromBase64(A));return Tz0.mutate(xWB.fromUtf8(A))}Pz0(bWB,"transformFromString");var fWB=class A extends Uint8Array{static fromString(B,Q="utf-8"){switch(typeof B){case"string":return bWB(B,Q);default:throw new Error(`Unsupported conversion from ${typeof B} to Uint8ArrayBlobAdapter.`)}}static mutate(B){return Object.setPrototypeOf(B,A.prototype),B}transformToString(B="utf-8"){return vWB(this,B)}};Pz0(fWB,"Uint8ArrayBlobAdapter");var Tz0=fWB;kWB(_k1,DWB(),xk1.exports);kWB(_k1,yWB(),xk1.exports)});
var iYB=E((f03,pYB)=>{var{defineProperty:Tk1,getOwnPropertyDescriptor:ku6,getOwnPropertyNames:_u6}=Object,xu6=Object.prototype.hasOwnProperty,vu6=(A,B)=>Tk1(A,"name",{value:B,configurable:!0}),bu6=(A,B)=>{for(var Q in B)Tk1(A,Q,{get:B[Q],enumerable:!0})},fu6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of _u6(B))if(!xu6.call(A,Z)&&Z!==Q)Tk1(A,Z,{get:()=>B[Z],enumerable:!(D=ku6(B,Z))||D.enumerable})}return A},hu6=(A)=>fu6(Tk1({},"__esModule",{value:!0}),A),lYB={};bu6(lYB,{isArrayBuffer:()=>gu6});pYB.exports=hu6(lYB);var gu6=vu6((A)=>typeof ArrayBuffer==="function"&&A instanceof ArrayBuffer||Object.prototype.toString.call(A)==="[object ArrayBuffer]","isArrayBuffer")});
var lH0=E((D03,gGB)=>{var{defineProperty:oy1,getOwnPropertyDescriptor:Mb6,getOwnPropertyNames:Rb6}=Object,Ob6=Object.prototype.hasOwnProperty,ty1=(A,B)=>oy1(A,"name",{value:B,configurable:!0}),Tb6=(A,B)=>{for(var Q in B)oy1(A,Q,{get:B[Q],enumerable:!0})},Pb6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Rb6(B))if(!Ob6.call(A,Z)&&Z!==Q)oy1(A,Z,{get:()=>B[Z],enumerable:!(D=Mb6(B,Z))||D.enumerable})}return A},Sb6=(A)=>Pb6(oy1({},"__esModule",{value:!0}),A),yGB={};Tb6(yGB,{AlgorithmId:()=>vGB,EndpointURLScheme:()=>xGB,FieldPosition:()=>bGB,HttpApiKeyAuthLocation:()=>_GB,HttpAuthLocation:()=>kGB,IniSectionType:()=>fGB,RequestHandlerProtocol:()=>hGB,SMITHY_CONTEXT_KEY:()=>xb6,getDefaultClientConfiguration:()=>kb6,resolveDefaultRuntimeConfig:()=>_b6});gGB.exports=Sb6(yGB);var kGB=((A)=>{return A.HEADER="header",A.QUERY="query",A})(kGB||{}),_GB=((A)=>{return A.HEADER="header",A.QUERY="query",A})(_GB||{}),xGB=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(xGB||{}),vGB=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(vGB||{}),jb6=ty1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{_checksumAlgorithms:B,addChecksumAlgorithm(Q){this._checksumAlgorithms.push(Q)},checksumAlgorithms(){return this._checksumAlgorithms}}},"getChecksumConfiguration"),yb6=ty1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),kb6=ty1((A)=>{return{...jb6(A)}},"getDefaultClientConfiguration"),_b6=ty1((A)=>{return{...yb6(A)}},"resolveDefaultRuntimeConfig"),bGB=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(bGB||{}),xb6="__smithy_context",fGB=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(fGB||{}),hGB=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(hGB||{})});
var pH0=E((Z03,nGB)=>{var{defineProperty:ey1,getOwnPropertyDescriptor:vb6,getOwnPropertyNames:bb6}=Object,fb6=Object.prototype.hasOwnProperty,vx=(A,B)=>ey1(A,"name",{value:B,configurable:!0}),hb6=(A,B)=>{for(var Q in B)ey1(A,Q,{get:B[Q],enumerable:!0})},gb6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of bb6(B))if(!fb6.call(A,Z)&&Z!==Q)ey1(A,Z,{get:()=>B[Z],enumerable:!(D=vb6(B,Z))||D.enumerable})}return A},ub6=(A)=>gb6(ey1({},"__esModule",{value:!0}),A),uGB={};hb6(uGB,{Field:()=>lb6,Fields:()=>pb6,HttpRequest:()=>ib6,HttpResponse:()=>nb6,getHttpHandlerExtensionConfiguration:()=>mb6,isValidHostname:()=>iGB,resolveHttpHandlerRuntimeConfig:()=>db6});nGB.exports=ub6(uGB);var mb6=vx((A)=>{let B=A.httpHandler;return{setHttpHandler(Q){B=Q},httpHandler(){return B},updateHttpClientConfig(Q,D){B.updateHttpClientConfig(Q,D)},httpHandlerConfigs(){return B.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),db6=vx((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),cb6=lH0(),mGB=class A{constructor({name:B,kind:Q=cb6.FieldPosition.HEADER,values:D=[]}){this.name=B,this.kind=Q,this.values=D}add(B){this.values.push(B)}set(B){this.values=B}remove(B){this.values=this.values.filter((Q)=>Q!==B)}toString(){return this.values.map((B)=>B.includes(",")||B.includes(" ")?`"${B}"`:B).join(", ")}get(){return this.values}};vx(mGB,"Field");var lb6=mGB,dGB=class A{constructor({fields:B=[],encoding:Q="utf-8"}){this.entries={},B.forEach(this.setField.bind(this)),this.encoding=Q}setField(B){this.entries[B.name.toLowerCase()]=B}getField(B){return this.entries[B.toLowerCase()]}removeField(B){delete this.entries[B.toLowerCase()]}getByType(B){return Object.values(this.entries).filter((Q)=>Q.kind===B)}};vx(dGB,"Fields");var pb6=dGB,cGB=class A{constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){let B=new A({...this,headers:{...this.headers}});if(B.query)B.query=lGB(B.query);return B}};vx(cGB,"HttpRequest");var ib6=cGB;function lGB(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}vx(lGB,"cloneQuery");var pGB=class A{constructor(B){this.statusCode=B.statusCode,this.reason=B.reason,this.headers=B.headers||{},this.body=B.body}static isInstance(B){if(!B)return!1;let Q=B;return typeof Q.statusCode==="number"&&typeof Q.headers==="object"}};vx(pGB,"HttpResponse");var nb6=pGB;function iGB(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}vx(iGB,"isValidHostname")});
var yWB=E((SWB)=>{Object.defineProperty(SWB,"__esModule",{value:!0});SWB.sdkStreamMixin=void 0;var um6=TWB(),mm6=Uz0(),Rz0=J1("stream"),dm6=J1("util"),PWB="The stream has already been transformed.",cm6=(A)=>{var B,Q;if(!(A instanceof Rz0.Readable)){let G=((Q=(B=A===null||A===void 0?void 0:A.__proto__)===null||B===void 0?void 0:B.constructor)===null||Q===void 0?void 0:Q.name)||A;throw new Error(`Unexpected stream implementation, expect Stream.Readable instance, got ${G}`)}let D=!1,Z=async()=>{if(D)throw new Error(PWB);return D=!0,await um6.streamCollector(A)};return Object.assign(A,{transformToByteArray:Z,transformToString:async(G)=>{let F=await Z();if(G===void 0||Buffer.isEncoding(G))return mm6.fromArrayBuffer(F.buffer,F.byteOffset,F.byteLength).toString(G);else return new dm6.TextDecoder(G).decode(F)},transformToWebStream:()=>{if(D)throw new Error(PWB);if(A.readableFlowing!==null)throw new Error("The stream has been consumed by other callbacks.");if(typeof Rz0.Readable.toWeb!=="function")throw new Error("Readable.toWeb() is not supported. Please make sure you are using Node.js >= 17.0.0, or polyfill is available.");return D=!0,Rz0.Readable.toWeb(A)}})};SWB.sdkStreamMixin=cm6});

// Export all variables
module.exports = {
  AWB,
  DWB,
  Hz0,
  IWB,
  Lk1,
  NYB,
  PYB,
  TWB,
  Uz0,
  XWB,
  YJB,
  _YB,
  bYB,
  cYB,
  hWB,
  iYB,
  lH0,
  pH0,
  yWB
};
