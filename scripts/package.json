{"name": "bundle-analyzer", "version": "1.0.0", "description": "智能JavaScript bundle分析工具，能够识别和剥离npm包，生成清晰的依赖树", "main": "main.js", "bin": {"bundle-analyzer": "./main.js"}, "scripts": {"start": "node main.js", "test": "node test.js", "analyze": "node main.js", "help": "node main.js --help"}, "keywords": ["bundle", "analyzer", "webpack", "rollup", "npm", "dependency", "ast", "javascript", "refactor", "code-analysis"], "author": "Bundle Analyzer Team", "license": "MIT", "dependencies": {"@babel/parser": "^7.23.0", "@babel/traverse": "^7.23.0", "@babel/generator": "^7.23.0", "@babel/types": "^7.23.0"}, "devDependencies": {}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/bundle-analyzer.git"}, "bugs": {"url": "https://github.com/your-org/bundle-analyzer/issues"}, "homepage": "https://github.com/your-org/bundle-analyzer#readme"}