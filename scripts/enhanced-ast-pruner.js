#!/usr/bin/env node

// Filename: scripts/enhanced-ast-pruner.js
const fs = require('fs');
const path = require('path');
const parser = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const generate = require('@babel/generator').default;
const t = require('@babel/types');

// 加载外部指纹库
function loadFingerprintDatabase() {
    try {
        const fingerprintPath = path.resolve(__dirname, '../npm-finger2.cjs');
        if (!fs.existsSync(fingerprintPath)) {
            console.error(`错误: 指纹库文件未找到: ${fingerprintPath}`);
            process.exit(1);
        }
        
        delete require.cache[require.resolve(fingerprintPath)];
        const fingerprints = require(fingerprintPath);
        
        console.log(`✅ 成功加载指纹库，包含 ${Object.keys(fingerprints).length} 个NPM包指纹`);
        return fingerprints;
    } catch (error) {
        console.error(`❌ 加载指纹库失败: ${error.message}`);
        process.exit(1);
    }
}

class EnhancedNpmPackageStripper {
    constructor(sourceCode, fingerprintDatabase) {
        this.sourceCode = sourceCode;
        this.NPM_FINGERPRINTS = fingerprintDatabase;
        this.ast = null;

        // 核心数据结构
        this.moduleRegistry = new Map();        // 所有模块的元信息
        this.dependencyGraph = new Map();       // 模块间依赖关系图
        this.libraryComponents = new Map();     // 识别的库组件集合
        this.removalQueue = new Set();          // 待移除的模块集合
        this.preservedModules = new Set();      // 保留的业务模块

        // 统计信息
        this.stats = {
            totalModules: 0,
            identifiedLibraries: 0,
            removedModules: 0,
            preservedModules: 0
        };
    }

    // 主执行流程 (带内存监控)
    async run() {
        console.log('🚀 启动增强版AST剪枝器...\n');
        
        // 内存监控
        this.logMemoryUsage('启动时');
        
        // 阶段1: 解析并建立模块注册表
        console.log('阶段 1: 构建完整模块注册表...');
        this.parseAstIfNeeded();
        this.buildModuleRegistry();
        this.logMemoryUsage('模块注册表构建后');
        
        // 阶段2: 构建模块依赖关系图
        console.log('\n阶段 2: 分析模块依赖关系...');
        this.buildDependencyGraph();
        this.logMemoryUsage('依赖关系图构建后');
        
        // 阶段3: 智能npm包识别
        console.log('\n阶段 3: 增强npm包识别...');
        await this.enhancedLibraryDetection();
        this.logMemoryUsage('库识别后');
        
        // 阶段4: 递归依赖链追踪
        console.log('\n阶段 4: 递归追踪库依赖链...');
        this.recursiveLibraryDependencyTracking();
        this.logMemoryUsage('依赖追踪后');
        
        // 内存清理
        this.performMemoryCleanup();
        
        // 阶段5: 智能保留决策
        console.log('\n阶段 5: 智能保留决策...');
        this.intelligentPreservationDecision();
        this.logMemoryUsage('保留决策后');
        
        // 阶段6: 执行剪枝
        console.log('\n阶段 6: 执行完整剪枝...');
        this.executeCompletePruning();
        this.logMemoryUsage('剪枝后');
        
        // 阶段7: 生成净化代码
        console.log('\n阶段 7: 生成净化代码...');
        const cleanedCode = this.generateCleanedCode();
        this.logMemoryUsage('代码生成后');
        
        // 生成报告
        const report = this.generateDetailedReport();
        
        return { cleanedCode, report };
    }

    // 内存使用监控
    logMemoryUsage(stage) {
        const usage = process.memoryUsage();
        const heapUsed = (usage.heapUsed / 1024 / 1024).toFixed(2);
        const heapTotal = (usage.heapTotal / 1024 / 1024).toFixed(2);
        const external = (usage.external / 1024 / 1024).toFixed(2);
        
        console.log(`  💾 [${stage}] 内存使用: ${heapUsed}MB / ${heapTotal}MB (外部: ${external}MB)`);
        
        // 内存警告
        if (usage.heapUsed > 2 * 1024 * 1024 * 1024) { // > 2GB
            console.warn(`  ⚠️  内存使用过高，建议增加 --max-old-space-size 参数`);
        }
    }

    // 执行内存清理
    performMemoryCleanup() {
        console.log('  🧹 执行内存清理...');
        
        // 清理不必要的引用
        for (const [name, moduleInfo] of this.moduleRegistry.entries()) {
            if (moduleInfo.shouldRemove) {
                // 清理AST引用以释放内存
                if (moduleInfo.astPath) {
                    moduleInfo.astPath = null;
                }
                if (moduleInfo.astNode) {
                    moduleInfo.astNode = null;
                }
                if (moduleInfo.moduleFunction) {
                    moduleInfo.moduleFunction = null;
                }
                // 保留必要信息
                moduleInfo.sourceCode = null; // 清理源码缓存
            }
        }
        
        // 强制垃圾回收
        if (global.gc) {
            const beforeGC = process.memoryUsage().heapUsed;
            global.gc();
            const afterGC = process.memoryUsage().heapUsed;
            const freed = (beforeGC - afterGC) / 1024 / 1024;
            console.log(`  🗑️  垃圾回收释放: ${freed.toFixed(2)}MB`);
        }
    }

    // AST解析
    parseAstIfNeeded() {
        if (this.ast) return;
        
        console.log('正在解析AST...');
        const parserOptions = {
            sourceType: 'module',
            plugins: ['importMeta'],
            ranges: true,       // 需要ranges来获取精确位置
            tokens: false,
            attachComments: false
        };

        try {
            this.ast = parser.parse(this.sourceCode, parserOptions);
            console.log('✅ AST解析完成');
        } catch (e) {
            console.error("❌ AST解析失败", e);
            throw e;
        }
    }

    // 阶段1: 构建完整模块注册表
    buildModuleRegistry() {
        let moduleCount = 0;
        const modulePattern = /^[a-zA-Z_$][a-zA-Z0-9_$]*$/; // 有效的变量名模式

        traverse(this.ast, {
            VariableDeclarator: (path) => {
                const { node } = path;
                
                // 识别模块定义模式: var xxx = z((param1, param2) => { ... })
                if (t.isIdentifier(node.id) &&
                    node.init &&
                    t.isCallExpression(node.init) &&
                    t.isIdentifier(node.init.callee) &&
                    ['z', '__webpack_require__', 'require'].includes(node.init.callee.name)) {
                    
                    const moduleName = node.id.name;
                    const moduleFunction = node.init.arguments[0];
                    
                    if (modulePattern.test(moduleName) && moduleFunction) {
                        const moduleInfo = {
                            name: moduleName,
                            astPath: path,
                            astNode: node,
                            moduleFunction: moduleFunction,
                            sourceRange: this.getSourceRange(node),
                            sourceCode: this.getSourceSlice(node),
                            size: this.calculateModuleSize(node),
                            dependencies: new Set(),
                            isDependency: false,
                            isLibrary: false,
                            libraryInfo: null,
                            shouldRemove: false
                        };
                        
                        this.moduleRegistry.set(moduleName, moduleInfo);
                        moduleCount++;
                    }
                }
            }
        });

        this.stats.totalModules = moduleCount;
        console.log(`  → 发现 ${moduleCount} 个模块定义`);
    }

    // 阶段2: 构建模块依赖关系图
    buildDependencyGraph() {
        console.log('  分析模块间调用关系...');
        
        for (const [moduleName, moduleInfo] of this.moduleRegistry.entries()) {
            const dependencies = this.extractModuleDependencies(moduleInfo);
            moduleInfo.dependencies = dependencies;
            this.dependencyGraph.set(moduleName, dependencies);
        }

        // 统计依赖信息
        let totalDependencies = 0;
        for (const deps of this.dependencyGraph.values()) {
            totalDependencies += deps.size;
        }
        console.log(`  → 分析了 ${this.dependencyGraph.size} 个模块，发现 ${totalDependencies} 个依赖关系`);
    }

    // 提取单个模块的依赖关系
    extractModuleDependencies(moduleInfo) {
        const dependencies = new Set();
        const allModuleNames = new Set(this.moduleRegistry.keys());
        
        if (!moduleInfo.moduleFunction) return dependencies;

        try {
            // 🔧 修复: 使用astPath来获取正确的scope和parentPath
            const modulePath = moduleInfo.astPath;
            const functionExpression = modulePath.get('init.arguments.0');
            
            if (functionExpression && functionExpression.isFunction()) {
                // 在模块函数内查找对其他模块的引用
                functionExpression.traverse({
                    // 直接调用: moduleX()
                    CallExpression(path) {
                        if (t.isIdentifier(path.node.callee)) {
                            const calledName = path.node.callee.name;
                            if (allModuleNames.has(calledName)) {
                                dependencies.add(calledName);
                            }
                        }
                    },
                    
                    // 成员访问: moduleX.method()
                    MemberExpression(path) {
                        let object = path.node.object;
                        while (t.isMemberExpression(object)) {
                            object = object.object;
                        }
                        if (t.isIdentifier(object) && allModuleNames.has(object.name)) {
                            dependencies.add(object.name);
                        }
                    },
                    
                    // 变量引用: var x = moduleY
                    Identifier(path) {
                        if (path.isReferencedIdentifier()) {
                            const refName = path.node.name;
                            if (allModuleNames.has(refName)) {
                                dependencies.add(refName);
                            }
                        }
                    }
                });
            } else {
                // 🔧 备用方案: 如果无法获取path，使用字符串匹配
                console.warn(`  ⚠️  无法遍历模块 ${moduleInfo.name}，使用备用检测方法`);
                dependencies = this.extractDependenciesViaStringAnalysis(moduleInfo, allModuleNames);
            }
        } catch (error) {
            console.warn(`  ⚠️  分析模块 ${moduleInfo.name} 依赖时出错: ${error.message}`);
            // 🔧 备用方案: 使用字符串分析
            return this.extractDependenciesViaStringAnalysis(moduleInfo, allModuleNames);
        }

        return dependencies;
    }

    // 备用依赖提取方法 - 基于字符串分析
    extractDependenciesViaStringAnalysis(moduleInfo, allModuleNames) {
        const dependencies = new Set();
        const sourceCode = moduleInfo.sourceCode;
        
        // 使用正则表达式匹配模块引用
        for (const moduleName of allModuleNames) {
            if (moduleName === moduleInfo.name) continue; // 跳过自己
            
            // 匹配各种调用模式
            const patterns = [
                new RegExp(`\\b${moduleName}\\s*\\(`, 'g'),        // 函数调用
                new RegExp(`\\b${moduleName}\\.\\w+`, 'g'),        // 成员访问
                new RegExp(`\\b${moduleName}\\b(?!\\s*[=:])`, 'g'), // 变量引用
            ];
            
            for (const pattern of patterns) {
                if (pattern.test(sourceCode)) {
                    dependencies.add(moduleName);
                    break; // 找到一种模式就足够了
                }
            }
        }
        
        return dependencies;
    }

    // 阶段3: 增强npm包识别
    async enhancedLibraryDetection() {
        console.log('  开始多维度库识别...');
        
        for (const [moduleName, moduleInfo] of this.moduleRegistry.entries()) {
            const detectionResults = {
                fingerprintScore: 0,
                patternScore: 0,
                structureScore: 0,
                totalScore: 0,
                identifiedAs: null
            };

            // 1. 指纹匹配 (继承原有逻辑)
            detectionResults.fingerprintScore = this.fingerprintMatching(moduleInfo);
            
            // 2. 代码模式识别
            detectionResults.patternScore = this.detectLibraryPatterns(moduleInfo);
            
            // 3. 结构特征分析
            detectionResults.structureScore = this.analyzeStructureFeatures(moduleInfo);
            
            // 计算总分
            detectionResults.totalScore = 
                detectionResults.fingerprintScore * 0.5 +   // 指纹权重50%
                detectionResults.patternScore * 0.3 +       // 模式权重30%
                detectionResults.structureScore * 0.2;      // 结构权重20%

            // 判断是否为库
            if (detectionResults.totalScore > 25) {  // 降低阈值，提高检出率
                moduleInfo.isLibrary = true;
                moduleInfo.libraryInfo = detectionResults;
                this.libraryComponents.set(moduleName, detectionResults);
                
                console.log(`  📚 识别库模块: ${moduleName} → ${detectionResults.identifiedAs || 'Unknown'} (分数: ${detectionResults.totalScore.toFixed(1)})`);
                this.stats.identifiedLibraries++;
            }
        }
    }

    // 指纹匹配 (改进版)
    fingerprintMatching(moduleInfo) {
        let bestMatch = { name: null, score: 0 };
        const sourceSlice = moduleInfo.sourceCode;
        
        for (const [npmName, fingerprint] of Object.entries(this.NPM_FINGERPRINTS)) {
            let currentScore = 0;
            
            // 字符串匹配
            for (const fpString of fingerprint.strings || []) {
                if (fpString instanceof RegExp) {
                    if (sourceSlice.match(fpString)) currentScore += 15;
                } else if (sourceSlice.includes(fpString)) {
                    currentScore += 10;
                }
            }
            
            // 导出匹配
            for (const fpExport of fingerprint.exports || []) {
                if (sourceSlice.includes(fpExport)) currentScore += 5;
            }
            
            if (currentScore > bestMatch.score) {
                bestMatch = { name: npmName, score: currentScore };
            }
        }
        
        if (bestMatch.score > 0) {
            moduleInfo.libraryInfo = { identifiedAs: bestMatch.name };
        }
        
        return bestMatch.score;
    }

    // 代码模式识别
    detectLibraryPatterns(moduleInfo) {
        const sourceCode = moduleInfo.sourceCode;
        let score = 0;

        // 常见库模式
        const libraryPatterns = [
            /exports\.default\s*=/, // ES6 default export
            /module\.exports\s*=/, // CommonJS export
            /Object\.defineProperty\(exports/, // Property definition
            /\/\*[\s\S]*?@license[\s\S]*?\*\//, // License comments
            /\/\*[\s\S]*?Copyright[\s\S]*?\*\//, // Copyright
            /function\s+[A-Z][a-zA-Z]*\s*\(/, // Constructor functions
            /\.prototype\.[a-zA-Z]+\s*=/, // Prototype assignments
            /typeof\s+exports/, // Export type checks
            /typeof\s+module/, // Module type checks
            /function\s*\([^)]*exports[^)]*\)/, // Export parameters
        ];

        for (const pattern of libraryPatterns) {
            if (pattern.test(sourceCode)) {
                score += 5;
            }
        }

        // 检查代码复杂度
        const complexity = this.calculateComplexity(moduleInfo.moduleFunction);
        if (complexity > 100) score += 10; // 高复杂度倾向于库代码

        return score;
    }

    // 结构特征分析
    analyzeStructureFeatures(moduleInfo) {
        let score = 0;
        
        // 分析模块大小
        const size = moduleInfo.size;
        if (size > 1000) score += 15;  // 大型模块倾向于库
        else if (size > 500) score += 10;
        else if (size > 100) score += 5;
        
        // 分析依赖数量
        const depCount = moduleInfo.dependencies.size;
        if (depCount === 0) score += 10; // 无依赖倾向于库
        else if (depCount < 3) score += 5;
        
        return score;
    }

    // 阶段4: 递归依赖链追踪 (带循环检测)
    recursiveLibraryDependencyTracking() {
        console.log('  追踪库模块的完整依赖链...');
        
        const globalVisited = new Set();
        const processing = new Set(); // 正在处理的模块，用于检测循环
        const libraryModules = new Set();
        const maxDepth = 50; // 最大递归深度限制
        
        // 收集所有已识别的库模块
        for (const [moduleName, moduleInfo] of this.moduleRegistry.entries()) {
            if (moduleInfo.isLibrary) {
                libraryModules.add(moduleName);
            }
        }
        
        console.log(`  → 发现 ${libraryModules.size} 个库模块作为起点`);
        
        // 递归追踪每个库的依赖
        let processedCount = 0;
        for (const libraryModule of libraryModules) {
            if (!globalVisited.has(libraryModule)) {
                console.log(`  → [${++processedCount}/${libraryModules.size}] 追踪库: ${libraryModule}`);
                const result = this.markLibraryDependenciesRecursive(
                    libraryModule, 
                    globalVisited, 
                    processing, 
                    new Set(),
                    0, 
                    maxDepth
                );
                
                if (result.hasCircular) {
                    console.log(`  ⚠️  检测到循环依赖链涉及: ${Array.from(result.circularChain).join(' → ')}`);
                }
            }
        }
        
        console.log(`  → 标记了 ${this.removalQueue.size} 个模块用于移除`);
        console.log(`  → 检测到 ${globalVisited.size} 个相关模块`);
    }

    // 递归标记库的依赖 (增强版循环检测)
    markLibraryDependenciesRecursive(moduleName, globalVisited, processing, currentPath, depth, maxDepth) {
        const result = { hasCircular: false, circularChain: new Set() };
        
        // 🔧 深度限制检查
        if (depth > maxDepth) {
            console.warn(`  ⚠️  达到最大递归深度 ${maxDepth}，停止分析: ${moduleName}`);
            return result;
        }
        
        // 🔧 循环依赖检测
        if (processing.has(moduleName)) {
            console.log(`  🔄 检测到循环依赖: ${moduleName} (当前路径: ${Array.from(currentPath).join(' → ')} → ${moduleName})`);
            result.hasCircular = true;
            result.circularChain = new Set([...currentPath, moduleName]);
            return result;
        }
        
        // 🔧 已处理检查
        if (globalVisited.has(moduleName)) {
            return result;
        }
        
        const moduleInfo = this.moduleRegistry.get(moduleName);
        if (!moduleInfo) {
            console.warn(`  ⚠️  模块不存在: ${moduleName}`);
            return result;
        }
        
        // 标记为正在处理
        processing.add(moduleName);
        currentPath.add(moduleName);
        globalVisited.add(moduleName);
        
        // 标记为待移除
        moduleInfo.shouldRemove = true;
        this.removalQueue.add(moduleName);
        
        // 🔧 依赖数量限制检查
        const dependencies = Array.from(moduleInfo.dependencies);
        if (dependencies.length > 100) {
            console.warn(`  ⚠️  模块 ${moduleName} 依赖过多 (${dependencies.length})，仅处理前100个`);
            dependencies.splice(100);
        }
        
        // 递归处理依赖
        for (const depName of dependencies) {
            const depInfo = this.moduleRegistry.get(depName);
            if (depInfo && !this.shouldPreserveModule(depInfo)) {
                const depResult = this.markLibraryDependenciesRecursive(
                    depName, 
                    globalVisited, 
                    processing, 
                    new Set(currentPath),  // 传递路径副本
                    depth + 1, 
                    maxDepth
                );
                
                if (depResult.hasCircular) {
                    result.hasCircular = true;
                    depResult.circularChain.forEach(node => result.circularChain.add(node));
                }
            }
        }
        
        // 清理状态
        processing.delete(moduleName);
        currentPath.delete(moduleName);
        
        return result;
    }

    // 阶段5: 智能保留决策
    intelligentPreservationDecision() {
        console.log('  分析业务逻辑模块...');
        
        for (const [moduleName, moduleInfo] of this.moduleRegistry.entries()) {
            if (!moduleInfo.shouldRemove && !moduleInfo.isLibrary) {
                if (this.isBusinessLogicModule(moduleInfo)) {
                    this.preservedModules.add(moduleName);
                    moduleInfo.shouldRemove = false;
                    
                    // 保护其关键依赖
                    this.protectCriticalDependencies(moduleName, new Set());
                }
            }
        }
        
        this.stats.preservedModules = this.preservedModules.size;
        this.stats.removedModules = this.removalQueue.size;
        
        console.log(`  → 保留 ${this.stats.preservedModules} 个业务模块`);
        console.log(`  → 移除 ${this.stats.removedModules} 个库/依赖模块`);
    }

    // 判断是否为业务逻辑模块
    isBusinessLogicModule(moduleInfo) {
        const sourceCode = moduleInfo.sourceCode;
        
        // 业务逻辑指标
        const businessIndicators = [
            /process\.argv/, // 命令行处理
            /console\.log/, // 日志输出
            /fs\.readFile/, // 文件操作
            /require\s*\(\s*['"]\./, // 相对路径引用
            /module\.exports.*function/, // 自定义导出函数
        ];
        
        let businessScore = 0;
        for (const indicator of businessIndicators) {
            if (indicator.test(sourceCode)) {
                businessScore += 10;
            }
        }
        
        // 非库特征
        const nonLibraryFeatures = [
            moduleInfo.size < 500,  // 相对较小
            moduleInfo.dependencies.size > 2,  // 有多个依赖
            !sourceCode.includes('Copyright'),  // 无版权信息
            !sourceCode.includes('license'),   // 无许可证
        ];
        
        const nonLibScore = nonLibraryFeatures.filter(Boolean).length * 5;
        
        return (businessScore + nonLibScore) > 15;
    }

    // 保护关键依赖 (带循环检测)
    protectCriticalDependencies(moduleName, visited, depth = 0) {
        const maxDepth = 20; // 限制递归深度
        
        // 🔧 循环依赖检测
        if (visited.has(moduleName)) {
            console.log(`  🔄 保护依赖时检测到循环: ${moduleName}`);
            return;
        }
        
        // 🔧 深度限制
        if (depth > maxDepth) {
            console.warn(`  ⚠️  保护依赖达到最大深度 ${maxDepth}，停止: ${moduleName}`);
            return;
        }
        
        visited.add(moduleName);
        
        const moduleInfo = this.moduleRegistry.get(moduleName);
        if (!moduleInfo) {
            visited.delete(moduleName);
            return;
        }
        
        // 限制依赖数量防止爆炸
        const dependencies = Array.from(moduleInfo.dependencies).slice(0, 50);
        
        for (const depName of dependencies) {
            const depInfo = this.moduleRegistry.get(depName);
            if (depInfo && depInfo.shouldRemove && !depInfo.isLibrary) {
                // 这是一个被错误标记的依赖，保护它
                console.log(`  🛡️  保护依赖: ${depName} (被 ${moduleName} 需要)`);
                depInfo.shouldRemove = false;
                this.removalQueue.delete(depName);
                this.preservedModules.add(depName);
                
                // 递归保护（传递visited副本）
                this.protectCriticalDependencies(depName, new Set(visited), depth + 1);
            }
        }
        
        visited.delete(moduleName);
    }

    // 阶段6: 执行完整剪枝
    executeCompletePruning() {
        console.log('  执行AST剪枝...');
        
        const self = this;
        let removedCount = 0;
        
        // 过滤AST body
        if (this.ast.body && Array.isArray(this.ast.body)) {
            this.ast.body = this.ast.body.filter(node => {
                if (t.isVariableDeclaration(node)) {
                    // 过滤变量声明中的模块
                    node.declarations = node.declarations.filter(decl => {
                        if (t.isIdentifier(decl.id)) {
                            const moduleName = decl.id.name;
                            const moduleInfo = self.moduleRegistry.get(moduleName);
                            
                            if (moduleInfo && moduleInfo.shouldRemove) {
                                console.log(`  🗑️  移除: ${moduleName} ${moduleInfo.isLibrary ? '(库)' : '(依赖)'}`);
                                removedCount++;
                                return false;
                            }
                        }
                        return true;
                    });
                    
                    // 如果所有声明都被移除，移除整个变量声明
                    return node.declarations.length > 0;
                }
                return true;
            });
        }
        
        console.log(`  ✅ 成功移除 ${removedCount} 个模块`);
    }

    // 生成净化代码
    generateCleanedCode() {
        const { code } = generate(this.ast, {
            comments: true,
            retainLines: false,
            concise: true
        });
        return code;
    }

    // 辅助方法
    shouldPreserveModule(moduleInfo) {
        return this.preservedModules.has(moduleInfo.name) || 
               this.isBusinessLogicModule(moduleInfo);
    }

    getSourceRange(node) {
        if (node.start !== undefined && node.end !== undefined) {
            return { start: node.start, end: node.end };
        }
        return null;
    }

    getSourceSlice(node) {
        const range = this.getSourceRange(node);
        if (range) {
            return this.sourceCode.substring(range.start, range.end);
        }
        return '';
    }

    calculateModuleSize(node) {
        const sourceSlice = this.getSourceSlice(node);
        return sourceSlice.split('\n').length;
    }

    calculateComplexity(functionNode) {
        let complexity = 0;
        if (!functionNode) return 0;
        
        try {
            // 🔧 修复: 使用简单的节点计数而不是traverse
            const countComplexNodes = (node) => {
                if (!node || typeof node !== 'object') return;
                
                // 增加复杂度的节点类型
                if (node.type === 'IfStatement' || 
                    node.type === 'WhileStatement' || 
                    node.type === 'ForStatement' || 
                    node.type === 'SwitchCase' || 
                    node.type === 'CatchClause' || 
                    node.type === 'FunctionExpression' || 
                    node.type === 'ArrowFunctionExpression') {
                    complexity++;
                }
                
                // 递归处理子节点
                for (const key in node) {
                    if (key === 'parent' || key === 'loc' || key === 'range') continue;
                    const child = node[key];
                    if (Array.isArray(child)) {
                        child.forEach(countComplexNodes);
                    } else if (child && typeof child === 'object' && child.type) {
                        countComplexNodes(child);
                    }
                }
            };
            
            countComplexNodes(functionNode);
        } catch (error) {
            console.warn(`计算复杂度时出错: ${error.message}`);
            // 备用方案：基于源码行数估算
            const sourceCode = generate(functionNode).code;
            complexity = Math.floor(sourceCode.split('\n').length / 10);
        }
        
        return complexity;
    }

    // 生成详细报告
    generateDetailedReport() {
        const libraryDetails = {};
        for (const [moduleName, detectionResult] of this.libraryComponents.entries()) {
            const moduleInfo = this.moduleRegistry.get(moduleName);
            libraryDetails[moduleName] = {
                identifiedAs: detectionResult.identifiedAs,
                confidence: detectionResult.totalScore,
                size: moduleInfo.size,
                dependencies: Array.from(moduleInfo.dependencies)
            };
        }

        return {
            summary: {
                ...this.stats,
                compressionRatio: ((this.stats.removedModules / this.stats.totalModules) * 100).toFixed(1) + '%'
            },
            detailedAnalysis: {
                libraryModules: libraryDetails,
                preservedModules: Array.from(this.preservedModules),
                removalQueue: Array.from(this.removalQueue)
            },
            dependencyGraph: Object.fromEntries(
                Array.from(this.dependencyGraph.entries()).map(([name, deps]) => [
                    name, Array.from(deps)
                ])
            )
        };
    }
}

// 主执行逻辑 (增强版)
function main() {
    console.log("🚀 增强版AST剪枝器启动...\n");

    const args = process.argv.slice(2);
    if (args.length !== 1) {
        console.error("用法: node enhanced-ast-pruner.js <源文件路径.js>");
        console.error("提示: 使用 --max-old-space-size=8192 --expose-gc 增加内存和启用垃圾回收");
        process.exit(1);
    }

    const sourceFilePath = path.resolve(args[0]);
    if (!fs.existsSync(sourceFilePath)) {
        console.error(`错误: 文件未找到于 ${sourceFilePath}`);
        process.exit(1);
    }

    // 确保输出目录存在
    const outputDir = path.resolve(__dirname, '../output');
    if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
        console.log(`✅ 创建输出目录: ${outputDir}`);
    }

    console.log(`正在读取源文件: ${sourceFilePath}`);
    
    try {
        const stats = fs.statSync(sourceFilePath);
        const fileSizeMB = stats.size / (1024 * 1024);
        console.log(`文件大小: ${fileSizeMB.toFixed(2)} MB`);

        // 内存建议
        if (fileSizeMB > 10) {
            console.warn(`⚠️  大文件检测 (${fileSizeMB.toFixed(2)} MB)，建议使用：`);
            console.warn(`   node --max-old-space-size=8192 --expose-gc scripts/enhanced-ast-pruner.js ${args[0]}`);
        }

        const sourceCode = fs.readFileSync(sourceFilePath, 'utf8');
        const fingerprintDatabase = loadFingerprintDatabase();
        const stripper = new EnhancedNpmPackageStripper(sourceCode, fingerprintDatabase);
        
        // 设置进程异常处理
        process.on('uncaughtException', (error) => {
            console.error('\n❌ 发生未捕获异常:', error.message);
            if (error.message.includes('heap out of memory')) {
                console.error('💡 内存不足，请使用: --max-old-space-size=8192');
            }
            process.exit(1);
        });
        
        stripper.run().then(result => {
            if (result) {
                const { cleanedCode, report } = result;
                const baseName = path.basename(sourceFilePath, '.js');

                const cleanedFilePath = path.join(outputDir, `${baseName}.enhanced.pruned.js`);
                const reportFilePath = path.join(outputDir, `${baseName}.enhanced.report.json`);

                fs.writeFileSync(cleanedFilePath, cleanedCode, 'utf8');
                fs.writeFileSync(reportFilePath, JSON.stringify(report, null, 2), 'utf8');

                console.log('\n✅ 处理完成!');
                console.log('='.repeat(60));
                console.log(`📄 净化后代码: ${cleanedFilePath}`);
                console.log(`📊 详细报告: ${reportFilePath}`);
                console.log(`🎯 压缩率: ${report.summary.compressionRatio}`);
                console.log(`📚 移除库数: ${report.summary.identifiedLibraries}`);
                console.log(`🔧 保留模块: ${report.summary.preservedModules}`);
                
                // 循环依赖统计
                if (report.circularDependencies && report.circularDependencies.length > 0) {
                    console.log(`🔄 检测到 ${report.circularDependencies.length} 个循环依赖`);
                }
            }
        }).catch(error => {
            console.error("\n❌ 处理过程中发生错误:", error.stack);
            
            if (error.message.includes('heap out of memory')) {
                console.error("\n🔧 内存不足解决方案:");
                console.error("1. 使用 --max-old-space-size=8192 增加内存限制");
                console.error("2. 使用 --expose-gc 启用手动垃圾回收");
                console.error("3. 考虑将大文件拆分为更小的模块");
            }
            
            process.exit(1);
        });
    } catch(error) {
        console.error("\n❌ 文件读取错误:", error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = EnhancedNpmPackageStripper;