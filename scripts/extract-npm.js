/**
 * @file production-grade-isolate.js
 * @description A production-grade tool to isolate code clusters from large JavaScript bundles.
 *              This version correctly handles all known patterns including property assignments,
 *              class inheritance, and special packer/bundler helper functions.
 * <AUTHOR> Senior Frontend Architect (Finalized Version 9.1 - Final & Complete)
 */

const fs = require('fs/promises');
const path = require('path');
const parser = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const generator = require('@babel/generator').default;
const t = require('@babel/types');

// Configuration for packer-specific helper functions
const PACKER_HELPERS = {
    DEFINE_PROPERTIES: 'Mj' // The name of the function that acts like Object.defineProperties
};

/**
 * Main orchestrator for the entire isolation process.
 */
async function isolateDependencies(inputFilePath, entryPointIdentifier, outputDir) {
    console.log('🚀 Starting Finalized Dependency Isolation Process (v9.1 - Final)...');

    try {
        // --- Phase 1: Parsing & Validation ---
        console.log(`[1/7] Parsing input file: ${inputFilePath}`);
        const sourceCode = await fs.readFile(inputFilePath, 'utf-8');
        const sourceCodeSizeMB = (sourceCode.length / 1024 / 1024).toFixed(2);
        console.log(`  File size: ${sourceCodeSizeMB} MB`);
        
        if (sourceCodeSizeMB > 5) {
             console.warn(`⚠️  This is a large file. If you encounter a 'heap out of memory' error, please run the script with more memory:`);
             console.warn(`   node --max-old-space-size=8192 ${path.basename(__filename)} ...`);
        }
        const ast = await parseWithFallback(sourceCode);

        console.log('[2/7] Building dependency graph...');
        const { dependencyGraph, topLevelDeclarations, attachmentStatements, stats } = buildDependencyGraph(ast);
        console.log(`  Found ${stats.totalNodes} top-level entities, including ${stats.totalAttachments} attachments.`);
        
        console.log(`[3/7] Validating entry point: "${entryPointIdentifier}"`);
        if (!dependencyGraph.has(entryPointIdentifier)) {
            const availableNodes = [...dependencyGraph.keys()];
            const suggestion = availableNodes.find(node => node.toLowerCase().includes(entryPointIdentifier.toLowerCase())) || availableNodes.find(node => entryPointIdentifier.toLowerCase().includes(node.toLowerCase()));
            let errorMessage = `Entry point "${entryPointIdentifier}" not found.`;
            if (suggestion) { errorMessage += ` Did you mean "${suggestion}"?`; } else { errorMessage += ` Available nodes include: ${availableNodes.slice(0, 10).join(', ')}${availableNodes.length > 10 ? '...' : ''}`; }
            throw new Error(errorMessage);
        }

        // --- Phase 2: Analysis ---
        console.log('[4/7] Identifying code cluster...');
        const nodesToIsolate = getReachableNodesIterative(dependencyGraph, entryPointIdentifier);
        console.log(`  Identified ${nodesToIsolate.size} nodes to be isolated.`);

        console.log('[5/7] Analyzing module boundaries...');
        const { entryBoundary, exitBoundary, externalDepsBoundary } = analyzeBoundaries(dependencyGraph, nodesToIsolate, topLevelDeclarations);
        
        if (exitBoundary.size > 0) {
            await handleUnsafeIsolation(exitBoundary, entryBoundary, entryPointIdentifier, outputDir);
            throw new Error('Unsafe isolation detected. Aborting.');
        }
        console.log('✅ Boundary analysis passed.');

        // --- Phase 3: Generation ---
        console.log('[6/7] Generating comprehensive analysis files...');
        await generateAnalysisFiles(dependencyGraph, nodesToIsolate, entryBoundary, externalDepsBoundary, entryPointIdentifier, outputDir);
        
        console.log('[7/7] Generating final output code bundles...');
        await generateFinalCode(ast, topLevelDeclarations, attachmentStatements, nodesToIsolate, entryBoundary, externalDepsBoundary, entryPointIdentifier, outputDir);

        console.log(`\n🎉 Process completed successfully!`);
        console.log(`Find your files in: ${path.resolve(outputDir)}`);
        
    } catch (error) {
        console.error(`\n💥 Process failed: ${error.message}`);
        process.exit(1);
    }
}

/**
 * Builds the dependency graph using a correct, single-pass traversal strategy.
 */
function buildDependencyGraph(ast) {
    const dependencyGraph = new Map();
    const topLevelDeclarations = new Map();
    const attachmentStatements = new Map();
    let totalAttachments = 0;

    traverse(ast, {
        Program(programPath) {
            const topLevelScope = programPath.scope;

            // Pass 1: Collect all top-level declarations and attachments.
            programPath.get('body').forEach(statementPath => {
                const declaredNames = Object.keys(t.getBindingIdentifiers(statementPath.node));
                declaredNames.forEach(name => {
                    if (!topLevelDeclarations.has(name)) {
                        topLevelDeclarations.set(name, statementPath.node);
                    }
                });

                if (statementPath.isExpressionStatement()) {
                    const expression = statementPath.get('expression');
                    let targetName = null;
                    if (expression.isCallExpression() && expression.get('callee').isIdentifier({ name: PACKER_HELPERS.DEFINE_PROPERTIES })) {
                        const firstArg = expression.get('arguments.0');
                        if (firstArg && firstArg.isIdentifier()) targetName = firstArg.node.name;
                    } else if (expression.isAssignmentExpression()) {
                        let current = expression.get('left');
                        while (current.isAssignmentExpression()) current = current.get('left');
                        if (current.isMemberExpression() && t.isIdentifier(current.node.object)) targetName = current.node.object.name;
                    }
                    if (targetName) {
                        if (!attachmentStatements.has(targetName)) attachmentStatements.set(targetName, []);
                        attachmentStatements.get(targetName).push(statementPath.node);
                        totalAttachments++;
                    }
                }
            });

            // Pass 2: Analyze dependencies for each entity (declaration + attachments).
            topLevelDeclarations.forEach((declarationNode, name) => {
                const dependencies = new Set();
                const attachments = attachmentStatements.get(name) || [];
                const allNodesForEntity = [declarationNode, ...attachments];

                allNodesForEntity.forEach(node => {
                    const nodePath = programPath.get('body').find(p => p.node === node);
                    if (!nodePath) return;

                    const visitor = {
                        ReferencedIdentifier(path) {
                            const refName = path.node.name;
                            if (refName === name || refName === PACKER_HELPERS.DEFINE_PROPERTIES) {
                                return;
                            }
                            const binding = path.scope.getBinding(refName);
                            if (binding && binding.scope === topLevelScope) {
                                dependencies.add(refName);
                            }
                        }
                    };
                    
                    nodePath.traverse(visitor);
                });
                dependencyGraph.set(name, dependencies);
            });
            programPath.stop();
        }
    });

    const totalEdges = [...dependencyGraph.values()].reduce((sum, deps) => sum + deps.size, 0);
    return { dependencyGraph, topLevelDeclarations, attachmentStatements, stats: { totalNodes: topLevelDeclarations.size, totalAttachments, totalEdges } };
}

/**
 * Attempts to parse the source code with a series of fallback configurations.
 */
async function parseWithFallback(sourceCode) {
    const parseOptionsList = [
        { sourceType: 'script', allowReturnOutsideFunction: true, plugins: ['jsx', 'typescript', 'decorators-legacy', 'dynamicImport'] },
        { sourceType: 'module', allowReturnOutsideFunction: true, plugins: ['jsx', 'typescript'] },
        { sourceType: 'script', plugins: [] }
    ];
    for (let i = 0; i < parseOptionsList.length; i++) {
        try {
            console.log(`  Attempting to parse with config ${i + 1}/${parseOptionsList.length}...`);
            return parser.parse(sourceCode, parseOptionsList[i]);
        } catch (error) {
            console.warn(`  Parse attempt ${i + 1} failed: ${error.message.split('\n')[0]}`);
            if (i === parseOptionsList.length - 1) {
                throw new Error(`All parsing attempts failed. Last error: ${error.message}`);
            }
        }
    }
}

/**
 * Iterative graph traversal to find all reachable nodes, preventing stack overflow.
 */
function getReachableNodesIterative(graph, startNode) {
    const reachable = new Set();
    const stack = [startNode];
    while (stack.length > 0) {
        const currentNode = stack.pop();
        if (!currentNode || reachable.has(currentNode)) continue;
        reachable.add(currentNode);
        const dependencies = graph.get(currentNode);
        if (dependencies) {
            dependencies.forEach(dep => {
                if (!reachable.has(dep)) stack.push(dep);
            });
        }
    }
    return reachable;
}

/**
 * Boundary analysis that correctly identifies require-based dependencies.
 */
function analyzeBoundaries(graph, nodesToIsolate, topLevelDeclarations) {
    const entryBoundary = new Set();
    const exitBoundary = new Set();
    const externalDepsBoundary = new Map();

    const allDependenciesOfIsolatedSet = new Set();
    nodesToIsolate.forEach(node => {
        graph.get(node)?.forEach(dep => allDependenciesOfIsolatedSet.add(dep));
    });

    allDependenciesOfIsolatedSet.forEach(dep => {
        if (!nodesToIsolate.has(dep)) {
            const declarationNode = topLevelDeclarations.get(dep);
            let isRequireDep = false;
            if (declarationNode && t.isVariableDeclaration(declarationNode)) {
                 const declarator = declarationNode.declarations.find(d => 
                    Object.keys(t.getBindingIdentifiers(d.id)).includes(dep)
                 );
                 if (declarator && t.isCallExpression(declarator.init) && t.isIdentifier(declarator.init.callee, { name: 'require' })) {
                    if(t.isStringLiteral(declarator.init.arguments[0])) {
                        const source = declarator.init.arguments[0].value;
                        if (!externalDepsBoundary.has(source)) externalDepsBoundary.set(source, new Set());
                        externalDepsBoundary.get(source).add(dep);
                        isRequireDep = true;
                    }
                 }
            }
            if (!isRequireDep) {
                exitBoundary.add(dep);
            }
        }
    });
    
    graph.forEach((dependencies, node) => {
        if (!nodesToIsolate.has(node)) {
            dependencies.forEach(dep => {
                if (nodesToIsolate.has(dep)) entryBoundary.add(dep);
            });
        }
    });

    return { entryBoundary, exitBoundary, externalDepsBoundary };
}

/**
 * Generates all analysis-related JSON files.
 */
async function generateAnalysisFiles(dependencyGraph, nodesToIsolate, entryBoundary, externalDepsBoundary, entryPoint, outputDir) {
    await fs.mkdir(outputDir, { recursive: true });
    const subgraph = {};
    nodesToIsolate.forEach(node => {
        subgraph[node] = [...(dependencyGraph.get(node) || [])].filter(dep => nodesToIsolate.has(dep)).sort();
    });
    await fs.writeFile(path.join(outputDir, `${entryPoint}.dependencies.json`), JSON.stringify(subgraph, null, 2));
    
    const summary = {
        metadata: { entryPoint, timestamp: new Date().toISOString() },
        isolation: {
            totalNodesInCluster: nodesToIsolate.size,
            totalNodesInOriginal: dependencyGraph.size,
            isolationRatio: `${((nodesToIsolate.size / dependencyGraph.size) * 100).toFixed(1)}%`,
            entryBoundarySize: entryBoundary.size,
            entryBoundaryNodes: [...entryBoundary].sort()
        },
        externalDependencies: Object.fromEntries([...externalDepsBoundary.entries()].map(([src, vars]) => [src, [...vars].sort()])),
        statistics: analyzeGraphStatistics(dependencyGraph),
        dependencyLayers: analyzeDependencyLayers(dependencyGraph, entryPoint, nodesToIsolate),
        nodesList: [...nodesToIsolate].sort()
    };
    await fs.writeFile(path.join(outputDir, `${entryPoint}.summary.json`), JSON.stringify(summary, null, 2));
    console.log(`  Analysis files saved: *.dependencies.json, *.summary.json`);
}

/**
 * Generates the final JavaScript code bundles, now including attachment statements.
 */
async function generateFinalCode(ast, declarations, attachments, nodesToIsolate, entryBoundary, externalDeps, entryPoint, outputDir) {
    const isolatedBundleName = `${entryPoint}.isolated.js`;
    const cleanedMainName = `main.cleaned.from.${entryPoint}.js`;
    
    const isolatedCode = generateIsolatedBundle(declarations, attachments, nodesToIsolate, entryBoundary, externalDeps, `Isolated bundle for '${entryPoint}'`);
    await fs.writeFile(path.join(outputDir, isolatedBundleName), isolatedCode);
    console.log(`  ✅ Isolated bundle: ${isolatedBundleName} (${Math.round(isolatedCode.length / 1024)}KB)`);
    
    const allNodesToRemove = new Set();
    nodesToIsolate.forEach(name => {
        if (declarations.has(name)) allNodesToRemove.add(declarations.get(name));
        if (attachments.has(name)) attachments.get(name).forEach(node => allNodesToRemove.add(node));
    });
    
    const cleanedCode = generateCleanedMain(ast, allNodesToRemove, entryBoundary, `./${isolatedBundleName}`);
    await fs.writeFile(path.join(outputDir, cleanedMainName), cleanedCode);
    console.log(`  ✅ Cleaned main file: ${cleanedMainName} (${Math.round(cleanedCode.length / 1024)}KB)`);
}

/**
 * Generates the isolated bundle, including declarations and their attachments.
 */
function generateIsolatedBundle(declarations, attachments, nodesToIsolate, entryBoundary, externalDeps, banner) {
    const nodesForBundle = new Set();

    externalDeps.forEach((variables, source) => {
        nodesForBundle.add(t.variableDeclaration('const', [t.variableDeclarator(t.objectPattern([...variables].sort().map(name => t.objectProperty(t.identifier(name),t.identifier(name),false,true))),t.callExpression(t.identifier('require'),[t.stringLiteral(source)]))]));
    });

    nodesToIsolate.forEach(name => {
        if (declarations.has(name)) nodesForBundle.add(declarations.get(name));
        if (attachments.has(name)) attachments.get(name).forEach(node => nodesForBundle.add(node));
    });
    
    const sortedNodes = [...nodesForBundle].sort((a, b) => (a.start || 0) - (b.start || 0));
    
    if (entryBoundary.size > 0) {
        sortedNodes.push(t.expressionStatement(t.assignmentExpression('=',t.memberExpression(t.identifier('module'),t.identifier('exports')),t.objectExpression([...entryBoundary].sort().map(name=>t.objectProperty(t.identifier(name),t.identifier(name),false,true))))));
    }

    const file = t.file(t.program(sortedNodes));
    return `/**\n * ${banner}\n */\n\n${generator(file, { comments: true, compact: false, minified: false }).code}`;
}

/**
 * Generates the cleaned main file, accepting a Set of nodes to remove.
 */
function generateCleanedMain(ast, nodesToRemove, entryBoundary, importPath) {
    const newBody = [];
    if (entryBoundary.size > 0) {
        newBody.push(t.variableDeclaration('const', [t.variableDeclarator(t.objectPattern([...entryBoundary].sort().map(name=>t.objectProperty(t.identifier(name),t.identifier(name),false,true))), t.callExpression(t.identifier('require'), [t.stringLiteral(importPath)]))]));
    }
    ast.program.body.forEach(node => {
        if (!nodesToRemove.has(node)) {
            newBody.push(node);
        }
    });
    const file = t.file(t.program(newBody));
    return `/**\n * Cleaned Main File\n */\n\n${generator(file, { comments: true, compact: false, minified: false }).code}`;
}

/**
 * Handles the unsafe isolation case by logging details and writing a report.
 */
async function handleUnsafeIsolation(exitBoundary, entryBoundary, entryPoint, outputDir) {
    console.error('❌ CRITICAL RISK: Unsafe Isolation Detected');
    console.error('The code to be isolated has dependencies on non-require-based main code.');
    const exitBoundaryArray = [...exitBoundary].sort();
    exitBoundaryArray.forEach(dep => console.error(`  - Isolated code needs '${dep}' from the main code.`));
    
    const riskReport = {
        status: 'UNSAFE',
        timestamp: new Date().toISOString(),
        entryPoint,
        reason: 'Reverse dependencies detected on non-module code.',
        exitBoundary: exitBoundaryArray,
        entryBoundary: [...entryBoundary].sort(),
        recommendation: 'Manual analysis required. The isolated code is tightly coupled with the main application logic.'
    };
    
    await fs.mkdir(outputDir, { recursive: true });
    const riskReportPath = path.join(outputDir, `${entryPoint}.risk-report.json`);
    await fs.writeFile(riskReportPath, JSON.stringify(riskReport, null, 2));
    console.error(`\nDetailed risk report saved to: ${riskReportPath}`);
}

/**
 * Checks if an identifier is a common JS built-in or environment global.
 */
function isBuiltinIdentifier(name) {
    const builtins = new Set(['console','window','document','global','process','Buffer','require','module','exports','__dirname','__filename','setTimeout','setInterval','clearTimeout','clearInterval','setImmediate','clearImmediate','JSON','Math','Date','Array','Object','String','Number','Boolean','Promise','Symbol','Map','Set','WeakMap','WeakSet','Proxy','Reflect','RegExp','Error','TypeError','ReferenceError','SyntaxError','parseInt','parseFloat','isNaN','isFinite','encodeURI','decodeURI','encodeURIComponent','decodeURIComponent','escape','unescape','undefined','null','Infinity','NaN','globalThis']);
    return builtins.has(name);
}

/**
 * Computes various statistics about the dependency graph.
 */
function analyzeGraphStatistics(graph) {
    const dependencyCounts = Object.fromEntries([...graph.entries()].map(([node, deps]) => [node, deps.size]));
    const totalDeps = [...graph.values()].reduce((sum, deps) => sum + deps.size, 0);
    const maxDepsEntry = Object.entries(dependencyCounts).reduce((max, entry) => entry[1] > max[1] ? entry : max, ['', -1]);
    return {
        averageDependenciesPerNode: (totalDeps / (graph.size || 1)).toFixed(2),
        maxDependencies: maxDepsEntry[1],
        nodeWithMaxDependencies: maxDepsEntry[0],
        top10MostDependedOn: Object.entries(dependencyCounts)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 10)
            .map(([node, count]) => ({ node, dependencies: count }))
    };
}

/**
 * Analyzes the dependency graph to structure nodes into layers.
 */
function analyzeDependencyLayers(graph, entryPoint, nodesToIsolate) {
    const layers = [];
    const processed = new Set();
    let currentLayer = new Set([entryPoint]);
    while (currentLayer.size > 0 && layers.length < 50) {
        const layerArray = [...currentLayer].sort();
        layers.push({ level: layers.length, nodes: layerArray, count: layerArray.length });
        const nextLayer = new Set();
        currentLayer.forEach(node => {
            processed.add(node);
            const deps = graph.get(node) || new Set();
            deps.forEach(dep => {
                if (nodesToIsolate.has(dep) && !processed.has(dep)) {
                    nextLayer.add(dep);
                }
            });
        });
        currentLayer = nextLayer;
    }
    return layers;
}

// --- Main Execution Logic ---
const [,, entryPoint] = process.argv;
const inputFile = './output/main.cleaned.from.nr1.js'; // Defaulting for your use case
const outputDir = './output'; // Defaulting for your use case
if (!inputFile || !entryPoint || !outputDir) {
    console.log('\nUsage: node production-grade-isolate.js <inputFile> <entryPointIdentifier> <outputDir>');
    console.log('Example: node production-grade-isolate.js ./bundle.js x5 ./output\n');
    process.exit(1);
}
isolateDependencies(inputFile, entryPoint, outputDir);