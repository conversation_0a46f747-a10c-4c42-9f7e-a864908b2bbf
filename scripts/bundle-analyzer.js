#!/usr/bin/env node

// Filename: scripts/bundle-analyzer.js
const fs = require('fs');
const path = require('path');
const parser = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const generate = require('@babel/generator').default;
const t = require('@babel/types');

// 加载外部指纹库
function loadFingerprintDatabase() {
    try {
        const fingerprintPath = path.resolve(__dirname, '../npm-finger2.cjs');
        if (!fs.existsSync(fingerprintPath)) {
            console.error(`错误: 指纹库文件未找到: ${fingerprintPath}`);
            process.exit(1);
        }
        
        // 清除require缓存以确保获取最新版本
        delete require.cache[require.resolve(fingerprintPath)];
        const fingerprints = require(fingerprintPath);
        
        console.log(`✅ 成功加载指纹库，包含 ${Object.keys(fingerprints).length} 个NPM包指纹`);
        return fingerprints;
    } catch (error) {
        console.error(`❌ 加载指纹库失败: ${error.message}`);
        process.exit(1);
    }
}

class NpmPackageStripper {
    constructor(sourceCode, fingerprintDatabase) {
        this.sourceCode = sourceCode;
        this.NPM_FINGERPRINTS = fingerprintDatabase;
        this.ast = null; // 延迟解析

        this.topLevelEntities = {
            modules: new Map(),
            functions: new Map(),
            classes: new Map(),
            variables: new Map(),
            expressions: [],
        };
        this.moduleFingerprints = new Map();
        this.entityDependencies = new Map();
        this.entryPoints = [];
    }

    // 延迟AST解析，带内存优化选项
    parseAstIfNeeded() {
        if (this.ast) return;
        
        console.log('正在解析AST，这可能需要一些时间...');
        
        // 内存优化的解析选项
        const parserOptions = {
            sourceType: 'module',
            plugins: ['importMeta'],
            ranges: false,      // 禁用ranges减少内存
            tokens: false,      // 禁用token减少内存
            attachComments: false // 禁用注释附加减少内存
        };

        try {
            this.ast = parser.parse(this.sourceCode, parserOptions);
            console.log('✅ AST解析完成。');
        } catch (e) {
            console.error("AST解析失败。", e);
            throw e;
        }
    }

    run() {
        console.log('阶段 1: 编目所有顶层实体...');
        this.catalogTopLevelEntities();
        console.log(`  -> 发现 ${this.topLevelEntities.modules.size} 个模块, ${this.topLevelEntities.functions.size} 个函数, ${this.topLevelEntities.classes.size} 个类, ${this.topLevelEntities.variables.size} 个变量。`);

        console.log('\n阶段 2: 匹配NPM指纹...');
        this.identifyModulesByFingerprint();

        console.log('\n阶段 3: 构建全面的依赖关系图...');
        this.buildComprehensiveDependencyGraph();

        console.log('\n阶段 4: 查找应用入口点...');
        this.findEntryPoints();

        // 内存优化：拆分阶段5
        console.log('\n阶段 5a: 生成依赖地图 (基于原始结构)...');
        const dependencyMap = this.generateDependencyMap();

        console.log('\n阶段 5b: 原地修改AST以剪枝库...');
        this.pruneAstInPlace();

        console.log('\n阶段 5c: 从修改后的AST生成净化源码...');
        const { code: cleanedCode } = generate(this.ast, { comments: true, retainLines: false, concise: true });

        console.log('\n阶段 6: 生成最终报告...');
        const report = this.generateReport(dependencyMap);

        return { cleanedCode, report };
    }

    // --- 阶段 1: 编目所有顶层实体 (健壮版) ---
    catalogTopLevelEntities() {
        this.parseAstIfNeeded();
        
        try {
        // 使用更高效的遍历器配置
        const visitorConfig = {
            Program: (path) => {
                const bodyPaths = path.get('body');
                
                // 分批处理以减少内存压力
                const batchSize = 100;
                for (let i = 0; i < bodyPaths.length; i += batchSize) {
                    const batch = bodyPaths.slice(i, i + batchSize);
                    
                    batch.forEach((nodePath, index) => {
                        try {
                            const node = nodePath.node;
                            if (t.isVariableDeclaration(node)) {
                                node.declarations.forEach(decl => {
                                    if (t.isIdentifier(decl.id)) {
                                        const name = decl.id.name;
                                        if (decl.init && t.isCallExpression(decl.init) && t.isIdentifier(decl.init.callee, { name: 'z' })) {
                                            // 不存储sourceSlice以减少内存
                                            this.topLevelEntities.modules.set(name, { path: nodePath });
                                        } else {
                                            this.topLevelEntities.variables.set(name, { path: nodePath });
                                        }
                                    }
                                });
                            } else if (t.isFunctionDeclaration(node) && node.id) {
                                this.topLevelEntities.functions.set(node.id.name, { path: nodePath });
                            } else if (t.isClassDeclaration(node) && node.id) {
                                this.topLevelEntities.classes.set(node.id.name, { path: nodePath });
                            } else if (t.isExpressionStatement(node)) {
                                this.topLevelEntities.expressions.push({ path: nodePath });
                            }
                        } catch (nodeError) {
                            console.warn(`处理第 ${i + index} 个顶层节点时出错: ${nodeError.message}`);
                        }
                    });
                    
                    // 强制垃圾回收（如果可用）
                    if (global.gc && i % (batchSize * 5) === 0) {
                        global.gc();
                    }
                }
                path.stop();
            }
        };
            
            traverse(this.ast, visitorConfig);
        } catch (traverseError) {
            console.error(`编目顶层实体失败: ${traverseError.message}`);
            throw traverseError;
        }
    }

    // --- 阶段 2: 指纹匹配并立即剪枝 ---
    identifyModulesByFingerprint() {
        for (const [moduleVarName, { path }] of this.topLevelEntities.modules.entries()) {
            let bestMatch = { name: null, score: 0 };
            
            // 动态获取sourceSlice以减少内存占用
            const sourceSlice = this.getNodeSourceSlice(path.node);
            
            for (const [npmName, fingerprint] of Object.entries(this.NPM_FINGERPRINTS)) {
                let currentScore = 0;
                const signatureIdentifiers = new Set();
                
                // 使用限制性遍历避免深度递归
                path.traverse({
                    Identifier(p) { 
                        if (p.key === 'key' || p.key === 'property') {
                            signatureIdentifiers.add(p.node.name); 
                        }
                    }
                });
                
                for (const fpString of fingerprint.strings) {
                    if (fpString instanceof RegExp) { 
                        if (sourceSlice.match(fpString)) currentScore += 15; 
                    }
                    else if (sourceSlice.includes(fpString)) { 
                        currentScore += 10; 
                    }
                }
                for (const fpExport of fingerprint.exports) {
                    if (signatureIdentifiers.has(fpExport)) currentScore += 5;
                }
                if (currentScore > bestMatch.score) bestMatch = { name: npmName, score: currentScore };
            }
            if (bestMatch.score > 20) {
                this.moduleFingerprints.set(moduleVarName, { identifiedAs: bestMatch.name, confidence: bestMatch.score });
                console.log(`  -> 模块 '${moduleVarName}' 高度疑似 '${bestMatch.name}' (置信度: ${bestMatch.score})`);
                
                // 🔥 关键优化：立即剪枝已识别的库模块
                this.pruneLibraryModuleImmediately(path, bestMatch, moduleVarName);
            }
        }
    }

    // 立即剪枝库模块，完全移除而不是清空
    pruneLibraryModuleImmediately(path, libInfo, moduleName) {
        try {
            const declarator = path.node.declarations[0];
            if (declarator && declarator.init && t.isCallExpression(declarator.init)) {
                const originalFunction = declarator.init.arguments[0];
                
                if (t.isFunctionExpression(originalFunction)) {
                    // 计算原始大小
                    const originalSize = this.calculateModuleSize(originalFunction);
                    
                    console.log(`🗑️  标记移除: ${moduleName} → ${libInfo.name} (${originalSize} lines)`);
                    
                    // 🔥 关键修复：标记整个变量声明需要移除，而不是仅清空函数体
                    path.node._shouldRemove = true;
                    
                    // 强制垃圾回收（如果可用）
                    if (global.gc) {
                        global.gc();
                    }
                }
            }
        } catch (error) {
            console.warn(`剪枝模块 ${moduleName} 时出错: ${error.message}`);
        }
    }

    // 辅助方法：动态获取节点源码片段
    getNodeSourceSlice(node) {
        if (node.start && node.end) {
            return this.sourceCode.substring(node.start, node.end);
        }
        return '';
    }

    // --- 阶段 3: 全面依赖图构建 (内存优化版) ---
    buildComprehensiveDependencyGraph() {
        const allEntities = new Map([
            ...this.topLevelEntities.modules, ...this.topLevelEntities.functions,
            ...this.topLevelEntities.classes, ...this.topLevelEntities.variables,
        ]);
        const allEntityNames = new Set(allEntities.keys());

        // 分批处理实体以减少内存压力
        const entityBatches = Array.from(allEntities.entries());
        const batchSize = 50;

        for (let i = 0; i < entityBatches.length; i += batchSize) {
            const batch = entityBatches.slice(i, i + batchSize);
            
            batch.forEach(([name, data]) => {
                // 🔥 关键优化：跳过已识别的库模块，避免深度分析
                if (this.moduleFingerprints.has(name)) {
                    console.log(`⏩ 跳过库模块依赖分析: ${name} (${this.moduleFingerprints.get(name).identifiedAs})`);
                    this.entityDependencies.set(name, []); // 设置为空依赖
                    return;
                }
                
                this.analyzeEntityDependencies(name, data, allEntityNames);
            });

            // 定期垃圾回收
            if (global.gc && i % (batchSize * 2) === 0) {
                global.gc();
                console.log(`🧹 已处理 ${i + batchSize}/${entityBatches.length} 个实体`);
            }
        }

        // 处理表达式，但跳过库模块相关的表达式
        this.topLevelEntities.expressions.forEach((data, index) => {
            const name = `_top_level_expression_${index}_`;
            this.analyzeEntityDependencies(name, data, allEntityNames);
        });
    }

    // 提取的实体依赖分析方法，保持完整逻辑
    analyzeEntityDependencies(name, { path }, allEntityNames) {
        const dependencies = new Set();
        const recordDependency = (depName) => {
            if (depName !== name && allEntityNames.has(depName)) dependencies.add(depName);
        };

        // 完整的访问器，保持原有分析准确性
        const visitor = {
            // 通用引用
            Identifier(p) { if (p.isReferencedIdentifier()) recordDependency(p.node.name); },

            // 函数/类调用
            CallExpression(p) { if (t.isIdentifier(p.node.callee)) recordDependency(p.node.callee.name); },
            NewExpression(p) { if (t.isIdentifier(p.node.callee)) recordDependency(p.node.callee.name); },

            // 成员访问
            MemberExpression(p) {
                let object = p.get('object');
                while (object.isMemberExpression()) object = object.get('object');
                if (object.isIdentifier()) recordDependency(object.node.name);
            },

            // 赋值和表达式
            AssignmentExpression(p) { if (t.isIdentifier(p.node.right)) recordDependency(p.node.right.name); },
            ConditionalExpression(p) {
                if (t.isIdentifier(p.node.consequent)) recordDependency(p.node.consequent.name);
                if (t.isIdentifier(p.node.alternate)) recordDependency(p.node.alternate.name);
            },
            LogicalExpression(p) {
                if (t.isIdentifier(p.node.left)) recordDependency(p.node.left.name);
                if (t.isIdentifier(p.node.right)) recordDependency(p.node.right.name);
            },

            // 数据结构
            ObjectProperty(p) { if (t.isIdentifier(p.node.value)) recordDependency(p.node.value.name); },
            ArrayExpression(p) { p.get('elements').forEach(elemPath => { if (elemPath.isIdentifier()) recordDependency(elemPath.node.name); }); },
            SpreadElement(p) { if (t.isIdentifier(p.node.argument)) recordDependency(p.node.argument.name); },
            TemplateLiteral(p) { p.get('expressions').forEach(exprPath => { if (exprPath.isIdentifier()) recordDependency(exprPath.node.name); }); },

            // 控制流
            ReturnStatement(p) { if (p.node.argument && t.isIdentifier(p.node.argument)) recordDependency(p.node.argument.name); },
            ThrowStatement(p) { if (t.isIdentifier(p.node.argument)) recordDependency(p.node.argument.name); },
            SwitchStatement(p) { if (t.isIdentifier(p.node.discriminant)) recordDependency(p.node.discriminant.name); },
            SwitchCase(p) { if (p.node.test && t.isIdentifier(p.node.test)) recordDependency(p.node.test.name); },
            ForOfStatement(p) { if (t.isIdentifier(p.node.right)) recordDependency(p.node.right.name); },
            ForInStatement(p) { if (t.isIdentifier(p.node.right)) recordDependency(p.node.right.name); },

            // 类和模块系统
            ClassDeclaration(p) { if (p.node.superClass && t.isIdentifier(p.node.superClass)) recordDependency(p.node.superClass.name); },
            ClassExpression(p) { if (p.node.superClass && t.isIdentifier(p.node.superClass)) recordDependency(p.node.superClass.name); },
            ExportDefaultDeclaration(p) { if (t.isIdentifier(p.node.declaration)) recordDependency(p.node.declaration.name); }
        };

        // 处理函数参数默认值
        const processParams = (nodePath) => {
            try {
                if (nodePath.node && nodePath.node.params) {
                    nodePath.node.params.forEach(param => {
                        if (t.isAssignmentPattern(param) && t.isIdentifier(param.right)) {
                            recordDependency(param.right.name);
                        }
                    });
                }
            } catch (e) {
                // 忽略参数处理错误，但保持静默
            }
        };

        try {
            if (path.isFunction() || path.isClass()) {
                processParams(path);
            }

            path.traverse(visitor);
            this.entityDependencies.set(name, Array.from(dependencies));
        } catch (error) {
            console.warn(`分析实体 ${name} 依赖时出错: ${error.message}`);
            this.entityDependencies.set(name, []);
        }
    }

    // --- 阶段 4: 查找入口点 (增强版) ---
    findEntryPoints() {
        const allDeclaredEntities = new Set([...this.topLevelEntities.modules.keys(), ...this.topLevelEntities.functions.keys(), ...this.topLevelEntities.classes.keys(), ...this.topLevelEntities.variables.keys()]);
        const allDependencies = new Set();
        for (const deps of this.entityDependencies.values()) { deps.forEach(dep => allDependencies.add(dep)); }

        const expressionEntries = [];
        this.topLevelEntities.expressions.forEach((_, index) => {
            const expressionDeps = this.entityDependencies.get(`_top_level_expression_${index}_`);
            if (expressionDeps && expressionDeps.length > 0) expressionEntries.push(...expressionDeps);
        });
        if (expressionEntries.length > 0) {
            const uniqueEntries = [...new Set(expressionEntries)].filter(name => !this.moduleFingerprints.has(name));
            console.log(`  -> 发现 ${uniqueEntries.length} 个由顶层表达式驱动的入口点: ${uniqueEntries.join(', ')}`);
            this.entryPoints = uniqueEntries;
            return;
        }

        const unreferencedEntries = [...allDeclaredEntities].filter(name => !allDependencies.has(name));
        const filteredEntryPoints = unreferencedEntries.filter(name => !this.moduleFingerprints.has(name));
        if (filteredEntryPoints.length > 0) {
            console.log(`  -> 发现 ${filteredEntryPoints.length} 个潜在入口点 (未被依赖的实体): ${filteredEntryPoints.join(', ')}`);
            this.entryPoints = filteredEntryPoints;
            return;
        }

        console.warn("  -> 警告: 使用备用入口点识别策略 (所有非库模块)。");
        this.entryPoints = [...this.topLevelEntities.modules.keys()].filter(name => !this.moduleFingerprints.has(name));
    }

    // --- 新增: 阶段5a - 超级优化依赖地图生成（内存安全版）---
    generateDependencyMap() {
        console.log(`📊 构建 ${this.entryPoints.length} 个入口点的依赖树...`);
        
        // 🔥 更激进的内存优化：减少最大深度和缓存大小
        this.globalNodeCache = new Map();
        this.referenceCount = new Map();
        // 🔥 关键修复：全局已访问集合，防止跨入口点的循环依赖
        this.globalVisited = new Set();
        const dependencyMap = {};
        
        // 内存监控
        let initialMemory = process.memoryUsage().heapUsed / 1024 / 1024;
        console.log(`📊 初始内存使用: ${initialMemory.toFixed(2)} MB`);
        
        // 第一步：预计算所有实体的依赖（一次性）
        console.log(`🔧 预计算依赖关系...`);
        this.precomputeAllDependencies();
        
        // 🔥 新策略：分批处理入口点，每批处理后立即清理
        const BATCH_SIZE = 10; // 减小批次大小
        const totalBatches = Math.ceil(this.entryPoints.length / BATCH_SIZE);
        
        for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
            const startIdx = batchIndex * BATCH_SIZE;
            const endIdx = Math.min(startIdx + BATCH_SIZE, this.entryPoints.length);
            const batch = this.entryPoints.slice(startIdx, endIdx);
            
            console.log(`🏭 处理批次 ${batchIndex + 1}/${totalBatches} (入口点 ${startIdx + 1}-${endIdx})`);
            
            for (let i = 0; i < batch.length; i++) {
                const entry = batch[i];
                const globalIndex = startIdx + i + 1;
                console.log(`📊 [${globalIndex}/${this.entryPoints.length}] 构建入口点依赖树: ${entry}`);
                
                // 🔍 诊断：检查当前实体的依赖复杂度
                const entityDeps = this.entityDependencies.get(entry) || [];
                console.log(`🔍 实体 ${entry} 直接依赖数量: ${entityDeps.length}`);
                
                // 🔍 检查是否为已知的问题实体
                if (entry === 'lJ1' || entry === 'g2' || entry === 'Rc0') {
                    console.log(`⚠️  检测到可能的问题实体: ${entry}，依赖: [${entityDeps.slice(0, 10).join(', ')}${entityDeps.length > 10 ? '...' : ''}]`);
                }
                
                try {
                    const memBefore = process.memoryUsage().heapUsed / 1024 / 1024;
                    dependencyMap[entry] = this.buildTreeNodeSuperCached(entry, new Set(), 0);
                    const memAfter = process.memoryUsage().heapUsed / 1024 / 1024;
                    const memDelta = memAfter - memBefore;
                    
                    if (memDelta > 100) { // 如果单个实体增加超过100MB内存
                        console.log(`🚨 实体 ${entry} 内存增长异常: +${memDelta.toFixed(2)} MB`);
                    }
                    
                    // 每个入口点处理后检查内存
                    if (globalIndex % 5 === 0) {
                        const currentMemory = process.memoryUsage().heapUsed / 1024 / 1024;
                        console.log(`🧠 内存使用: ${currentMemory.toFixed(2)} MB`);
                        
                        // 如果内存使用过高，强制垃圾回收
                        if (currentMemory > 6000) { // 6GB 阈值
                            console.log(`⚠️  内存使用过高，执行垃圾回收...`);
                            if (global.gc) {
                                global.gc();
                                const afterGC = process.memoryUsage().heapUsed / 1024 / 1024;
                                console.log(`🧹 垃圾回收后内存: ${afterGC.toFixed(2)} MB`);
                            }
                        }
                    }
                } catch (error) {
                    console.error(`❌ 处理入口点 ${entry} 时出错: ${error.message}`);
                    dependencyMap[entry] = { 'status': 'error', 'message': error.message };
                }
            }
            
            // 每批处理完后立即清理缓存
            console.log(`🧹 批次 ${batchIndex + 1} 完成，清理缓存...`);
            this.globalNodeCache.clear();
            this.referenceCount.clear();
            // 🔥 重要：不清理globalVisited，保持跨批次的循环依赖检测
            
            if (global.gc) {
                global.gc();
            }
        }
        
        // 最终清理
        this.globalNodeCache = null;
        this.referenceCount = null;
        this.globalVisited = null;
        
        const finalMemory = process.memoryUsage().heapUsed / 1024 / 1024;
        console.log(`📊 最终内存使用: ${finalMemory.toFixed(2)} MB`);
        
        return dependencyMap;
    }

    // 预计算所有依赖关系，转换为更紧凑的格式（解决AST引用内存泄漏）
    precomputeAllDependencies() {
        console.log('🔧 预计算依赖关系并释放AST引用...');
        this.compactDeps = new Map();
        
        for (const [entityName, deps] of this.entityDependencies.entries()) {
            // 只保留必要信息，减少内存占用
            this.compactDeps.set(entityName, {
                deps: deps,
                isLib: this.moduleFingerprints.has(entityName),
                libInfo: this.moduleFingerprints.get(entityName),
                type: this.topLevelEntities.modules.has(entityName) ? 'module' : 'other'
            });
        }
        
        // 🔥 关键修复：不清理AST引用，保留给后续剪枝使用
        console.log('⚠️  保留AST引用以便后续剪枝操作...');
        
        // 强制垃圾回收
        if (global.gc) {
            const memBefore = process.memoryUsage().heapUsed / 1024 / 1024;
            global.gc();
            const memAfter = process.memoryUsage().heapUsed / 1024 / 1024;
            console.log(`🧹 依赖预计算后内存: ${memBefore.toFixed(2)} MB → ${memAfter.toFixed(2)} MB`);
        }
    }

    // 超级缓存版本的树构建（带递归深度限制和内存优化）
    buildTreeNodeSuperCached(entityName, visited, depth = 0) {
        // 🔍 诊断：记录深度较深的递归调用
        if (depth > 15) {
            console.log(`🔍 深度 ${depth}: 分析实体 ${entityName}`);
        }
        
        // 🔥 更严格的递归深度限制，防止内存溢出
        const MAX_DEPTH = 50; // 大幅减少最大深度
        if (depth > MAX_DEPTH) {
            console.warn(`⚠️  递归深度超过限制 (${MAX_DEPTH})，停止深入分析: ${entityName}`);
            return { 'status': 'max_depth_reached', 'depth': depth };
        }

        // 🔥 关键修复：检查全局已访问集合，防止跨入口点循环依赖
        if (this.globalVisited && this.globalVisited.has(entityName)) {
            console.log(`🔄 检测到全局循环依赖: ${entityName}`);
            return { 'status': 'global_circular_dependency', 'entity': entityName };
        }

        // 检查全局缓存（仅在深度较浅时使用缓存）
        if (depth < 10 && this.globalNodeCache && this.globalNodeCache.has(entityName) && !visited.has(entityName)) {
            // 增加引用计数
            if (this.referenceCount) {
                this.referenceCount.set(entityName, (this.referenceCount.get(entityName) || 0) + 1);
            }
            return this.globalNodeCache.get(entityName);
        }

        const entityInfo = this.compactDeps.get(entityName);
        if (!entityInfo) {
            const unknownNode = { 'status': 'unknown_entity' };
            return unknownNode;
        }

        // 库节点处理
        if (entityInfo.isLib) {
            const libNode = { 
                'type': 'library', 
                'package': entityInfo.libInfo.identifiedAs, 
                'status': 'pruned' 
            };
            return libNode;
        }
        
        if (visited.has(entityName)) {
            console.log(`🔄 检测到局部循环依赖: ${entityName}`);
            return { 'status': `circular_dependency_to: ${entityName}` };
        }

        visited.add(entityName);
        // 🔥 同时添加到全局已访问集合
        if (this.globalVisited) {
            this.globalVisited.add(entityName);
        }

        const dependencies = entityInfo.deps || [];
        
        // 🔥 限制依赖数量，防止内存爆炸
        const MAX_DEPS = 30; // 进一步减少依赖数量限制
        const limitedDeps = dependencies.slice(0, MAX_DEPS);
        if (dependencies.length > MAX_DEPS) {
            console.warn(`⚠️  实体 ${entityName} 依赖过多 (${dependencies.length})，仅处理前 ${MAX_DEPS} 个`);
        }
        
        const node = {
            type: entityInfo.type,
            dependencies: {},
            status: limitedDeps.length > 0 ? 'application_logic_node' : 'application_logic_leaf'
        };
        
        // 递归构建依赖（传递深度参数）
        for (const depName of limitedDeps) {
            try {
                node.dependencies[depName] = this.buildTreeNodeSuperCached(depName, visited, depth + 1);
            } catch (error) {
                console.warn(`处理依赖 ${depName} 时出错: ${error.message}`);
                node.dependencies[depName] = { 'status': 'error', 'message': error.message };
            }
        }

        visited.delete(entityName);
        
        // 🔥 更严格的缓存策略：只缓存叶子节点且深度较浅的节点
        if (this.globalNodeCache && depth < 5 && (limitedDeps.length === 0 || (this.referenceCount && (this.referenceCount.get(entityName) || 0) > 3))) {
            this.globalNodeCache.set(entityName, node);
        }
        
        return node;
    }

    // 激进的缓存清理，只保留高价值缓存项（内存安全版）
    aggressiveCacheCleanup() {
        if (!this.globalNodeCache || !this.referenceCount) {
            return;
        }
        
        const keepThreshold = 5; // 提高引用次数阈值
        let cleanedCount = 0;
        
        for (const [key, refCount] of this.referenceCount.entries()) {
            if (refCount < keepThreshold && this.globalNodeCache.has(key)) {
                this.globalNodeCache.delete(key);
                cleanedCount++;
            }
        }
        
        this.referenceCount.clear();
        console.log(`🧹 清理了 ${cleanedCount} 个低价值缓存项`);
        
        // 如果缓存仍然过大，强制清理
        if (this.globalNodeCache.size > 1000) {
            console.log(`⚠️  缓存过大 (${this.globalNodeCache.size})，执行强制清理`);
            this.globalNodeCache.clear();
        }
    }

    // --- 新增: 阶段5b - 原地修改AST，真正移除npm包 ---
    pruneAstInPlace() {
        const self = this;
        const libraryModuleNames = new Set(this.moduleFingerprints.keys());
        
        // 🔥 更安全的策略：分两个独立的遍历来避免路径状态混乱
        
        // 第一遍：只替换库调用，不移除节点
        traverse(this.ast, {
            CallExpression(path) {
                const callee = path.get('callee');
                if (callee.isIdentifier()) {
                    const libInfo = self.moduleFingerprints.get(callee.node.name);
                    if (libInfo) {
                        console.log(`🔧 替换库调用: ${callee.node.name}() → require('${libInfo.identifiedAs}')`);
                        path.replaceWith(
                            t.callExpression(t.identifier('require'), [t.stringLiteral(libInfo.identifiedAs)])
                        );
                        t.addComment(path.node, "leading", ` [Replaced] `, true);
                    }
                }
            }
        });
        
        // 第二遍：移除库模块定义（使用过滤而不是遍历中移除）
        console.log(`🗑️  开始移除 ${libraryModuleNames.size} 个库模块...`);
        
        if (!this.ast || !this.ast.body) {
            console.error('❌ AST或AST.body不存在');
            console.log('AST结构:', this.ast ? Object.keys(this.ast) : 'AST is null');
            return;
        }
        
        console.log('🔍 AST.body类型:', typeof this.ast.body, 'Array.isArray:', Array.isArray(this.ast.body));
        console.log('🔍 AST.body长度/类型:', this.ast.body.length || typeof this.ast.body);
        
        const originalBody = Array.isArray(this.ast.body) ? this.ast.body : [this.ast.body];
        const filteredBody = [];
        let removedCount = 0;
        
        for (const node of originalBody) {
            let shouldKeep = true;
            
            if (t.isVariableDeclaration(node)) {
                const filteredDeclarations = [];
                
                for (const declaration of node.declarations) {
                    if (t.isIdentifier(declaration.id) &&
                        declaration.init &&
                        t.isCallExpression(declaration.init) &&
                        t.isIdentifier(declaration.init.callee, { name: 'z' }) &&
                        libraryModuleNames.has(declaration.id.name)) {
                        
                        const libInfo = self.moduleFingerprints.get(declaration.id.name);
                        const originalSize = self.calculateModuleSize(declaration.init.arguments[0]);
                        console.log(`🗑️  移除库模块: ${declaration.id.name} → ${libInfo.identifiedAs} (${originalSize} lines)`);
                        removedCount++;
                        // 不添加到filteredDeclarations，即移除
                    } else {
                        filteredDeclarations.push(declaration);
                    }
                }
                
                // 如果还有声明保留，更新节点；否则不保留整个变量声明
                if (filteredDeclarations.length > 0) {
                    node.declarations = filteredDeclarations;
                    filteredBody.push(node);
                } else {
                    shouldKeep = false;
                }
            } else {
                filteredBody.push(node);
            }
        }
        
        // 直接替换AST的body
        this.ast.body = filteredBody;
        
        console.log(`✅ 成功移除 ${removedCount} 个库模块，保留 ${filteredBody.length} 个顶层语句`);
    }

    // 注意：pruneLibraryModule 方法已被移除，因为我们现在完全移除库模块声明而不是仅剪枝内容

    // 计算模块原始大小
    calculateModuleSize(functionNode) {
        if (functionNode.start && functionNode.end) {
            const content = this.sourceCode.substring(functionNode.start, functionNode.end);
            return content.split('\n').length;
        }
        return 'unknown';
    }

    // --- 优化: `buildTreeNode` 保持完整分析但优化内存 ---
    buildTreeNode(entityName, visited) {
        const libInfo = this.moduleFingerprints.get(entityName);
        if (libInfo) return { 'type': 'library', 'package': libInfo.identifiedAs, 'status': 'pruned' };
        if (visited.has(entityName)) return { 'status': `circular_dependency_to: ${entityName}` };

        visited.add(entityName); // 在进入递归前添加

        const dependencies = this.entityDependencies.get(entityName) || [];
        const node = {
            type: this.topLevelEntities.modules.has(entityName) ? 'module' : 'function/class/variable',
            dependencies: {},
            status: dependencies.length > 0 ? 'application_logic_node' : 'application_logic_leaf'
        };
        
        // 保持完整的依赖分析，但优化内存使用
        for (const depName of dependencies) {
            // 传递同一个set，避免创建新的Set对象
            node.dependencies[depName] = this.buildTreeNode(depName, visited);
        }

        visited.delete(entityName); // 在当前分支返回后移除，实现回溯
        return node;
    }

    // --- 阶段 6: 生成报告 ---
    generateReport(dependencyMap) {
        const businessLogicModules = [...this.topLevelEntities.modules.keys()].filter(name => !this.moduleFingerprints.has(name));
        const identifiedLibraries = new Map();
        for (const [moduleVarName, lib] of this.moduleFingerprints.entries()) {
            if (!identifiedLibraries.has(lib.identifiedAs)) identifiedLibraries.set(lib.identifiedAs, []);
            identifiedLibraries.get(lib.identifiedAs).push(moduleVarName);
        }
        return {
            summary: {
                totalModules: this.topLevelEntities.modules.size,
                totalFunctions: this.topLevelEntities.functions.size,
                totalClasses: this.topLevelEntities.classes.size,
                identifiedLibrariesCount: identifiedLibraries.size,
                businessLogicModulesCount: businessLogicModules.length,
                entryPoints: this.entryPoints,
            },
            dependencyMap,
            identifiedLibraries: Object.fromEntries(identifiedLibraries),
            applicationEntities: {
                modules: businessLogicModules,
                functions: [...this.topLevelEntities.functions.keys()],
                classes: [...this.topLevelEntities.classes.keys()],
                variables: [...this.topLevelEntities.variables.keys()],
            },
        };
    }
}

// --- 主执行逻辑 ---
function main() {
    console.log("分析器启动...");

    // 内存优化提示
    console.warn("\n💡 提示: 如果遇到内存溢出 (heap out of memory) 错误, 请尝试使用以下命令增加Node.js可用内存:");
    console.warn("   node --max-old-space-size=8192 --expose-gc scripts/bundle-analyzer.js <your-file.js>\n");

    const args = process.argv.slice(2);
    if (args.length !== 1) {
        console.error("用法: node bundle-analyzer.js <源文件路径.js>");
        process.exit(1);
    }

    const sourceFilePath = path.resolve(args[0]);
    if (!fs.existsSync(sourceFilePath)) {
        console.error(`错误: 文件未找到于 ${sourceFilePath}`);
        process.exit(1);
    }

    // 确保输出目录存在
    const outputDir = path.resolve(__dirname, '../output');
    if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
        console.log(`✅ 创建输出目录: ${outputDir}`);
    }

    console.log(`正在读取源文件: ${sourceFilePath}\n`);
    
    try {
        // 检查文件大小并警告
        const stats = fs.statSync(sourceFilePath);
        const fileSizeMB = stats.size / (1024 * 1024);
        console.log(`文件大小: ${fileSizeMB.toFixed(2)} MB`);
        
        if (fileSizeMB > 5) {
            console.warn(`⚠️  警告: 文件较大 (${fileSizeMB.toFixed(2)} MB)，建议使用 --max-old-space-size 参数增加内存`);
        }

        // 检查可用内存
        const memUsage = process.memoryUsage();
        console.log(`初始内存使用: ${(memUsage.heapUsed / 1024 / 1024).toFixed(2)} MB`);
        
        const sourceCode = fs.readFileSync(sourceFilePath, 'utf8');
        const fingerprintDatabase = loadFingerprintDatabase();
        const stripper = new NpmPackageStripper(sourceCode, fingerprintDatabase);
        const result = stripper.run();

        if (result) {
            const { cleanedCode, report } = result;
            const baseName = path.basename(sourceFilePath, '.js');

            const cleanedFilePath = path.join(outputDir, `${baseName}.pruned.js`);
            const reportFilePath = path.join(outputDir, `${baseName}.report.json`);

            fs.writeFileSync(cleanedFilePath, cleanedCode, 'utf8');
            fs.writeFileSync(reportFilePath, JSON.stringify(report, null, 2), 'utf8');

            // 最终内存使用统计
            const finalMemUsage = process.memoryUsage();
            console.log(`最终内存使用: ${(finalMemUsage.heapUsed / 1024 / 1024).toFixed(2)} MB`);

            console.log('\n✅ 处理完成!');
            console.log('='.repeat(50));
            console.log(`📄 净化后的代码: ${cleanedFilePath}`);
            console.log(`📊 分析报告: ${reportFilePath}`);
        }
    } catch(error) {
        console.error("\n❌ 处理过程中发生严重错误:", error.stack);
        
        // 内存不足错误的特殊处理
        if (error.message.includes('heap out of memory')) {
            console.error("\n🔧 内存不足解决方案:");
            console.error("1. 使用 --max-old-space-size=8192 增加内存限制");
            console.error("2. 使用 --expose-gc 启用手动垃圾回收");
            console.error("3. 考虑将大文件拆分为更小的模块");
        }
        
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}
