// Package extracted with entry point: no0

var no0=E((wi8,MC1)=>{var dy9=po0(),LC1=new WeakMap,io0=(A,B={})=>{if(typeof A!=="function")throw new TypeError("Expected a function");let Q,D=0,Z=A.displayName||A.name||"<anonymous>",G=function(...F){if(LC1.set(G,++D),D===1)Q=A.apply(this,F),A=null;else if(B.throw===!0)throw new Error(`Function \`${Z}\` can only be called once`);return Q};return dy9(G,A),LC1.set(G,D),G};MC1.exports=io0;MC1.exports.default=io0;MC1.exports.callCount=(A)=>{if(!LC1.has(A))throw new Error(`The given function \`${A.name}\` is not wrapped by the \`onetime\` package`);return LC1.get(A)}});
var po0=E((Ui8,Zi1)=>{var lo0=(A,B)=>{for(let Q of Reflect.ownKeys(B))Object.defineProperty(A,Q,Object.getOwnPropertyDescriptor(B,Q));return A};Zi1.exports=lo0;Zi1.exports.default=lo0});

module.exports = no0;
