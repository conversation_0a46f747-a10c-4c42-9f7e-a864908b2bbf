// Package extracted with entry point: bt2

var bt2=E((jP1)=>{Object.defineProperty(jP1,"__esModule",{value:!0});jP1.PrometheusSerializer=jP1.PrometheusExporter=void 0;var iz6=vt2();Object.defineProperty(jP1,"PrometheusExporter",{enumerable:!0,get:function(){return iz6.PrometheusExporter}});var nz6=vJ0();Object.defineProperty(jP1,"PrometheusSerializer",{enumerable:!0,get:function(){return nz6.PrometheusSerializer}})});
var vJ0=E((kt2)=>{Object.defineProperty(kt2,"__esModule",{value:!0});kt2.PrometheusSerializer=void 0;var bz6=ZQ(),lu=f_(),Pt2=y3();function SP1(A){return A.replace(/\\/g,"\\\\").replace(/\n/g,"\\n")}function St2(A=""){if(typeof A!=="string")A=JSON.stringify(A);return SP1(A).replace(/"/g,"\\\"")}var fz6=/[^a-z0-9_]/gi,hz6=/_{2,}/g;function xJ0(A){return A.replace(fz6,"_").replace(hz6,"_")}function _J0(A,B){if(!A.endsWith("_total")&&B.dataPointType===lu.DataPointType.SUM&&B.isMonotonic)A=A+"_total";return A}function gz6(A){if(A===1/0)return"+Inf";else if(A===-1/0)return"-Inf";else return`${A}`}function uz6(A){switch(A.dataPointType){case lu.DataPointType.SUM:if(A.isMonotonic)return"counter";return"gauge";case lu.DataPointType.GAUGE:return"gauge";case lu.DataPointType.HISTOGRAM:return"histogram";default:return"untyped"}}function PP1(A,B,Q,D,Z){let G=!1,F="";for(let[I,Y]of Object.entries(B)){let W=xJ0(I);G=!0,F+=`${F.length>0?",":""}${W}="${St2(Y)}"`}if(Z)for(let[I,Y]of Object.entries(Z)){let W=xJ0(I);G=!0,F+=`${F.length>0?",":""}${W}="${St2(Y)}"`}if(G)A+=`{${F}}`;return`${A} ${gz6(Q)}${D!==void 0?" "+String(D):""}
`}var mz6="# no registered metrics";class jt2{_prefix;_appendTimestamp;_additionalAttributes;_withResourceConstantLabels;constructor(A,B=!1,Q){if(A)this._prefix=A+"_";this._appendTimestamp=B,this._withResourceConstantLabels=Q}serialize(A){let B="";this._additionalAttributes=this._filterResourceConstantLabels(A.resource.attributes,this._withResourceConstantLabels);for(let Q of A.scopeMetrics)B+=this._serializeScopeMetrics(Q);if(B==="")B+=mz6;return this._serializeResource(A.resource)+B}_filterResourceConstantLabels(A,B){if(B){let Q={};for(let[D,Z]of Object.entries(A))if(D.match(B))Q[D]=Z;return Q}return}_serializeScopeMetrics(A){let B="";for(let Q of A.metrics)B+=this._serializeMetricData(Q)+`
`;return B}_serializeMetricData(A){let B=xJ0(SP1(A.descriptor.name));if(this._prefix)B=`${this._prefix}${B}`;let Q=A.dataPointType;B=_J0(B,A);let D=`# HELP ${B} ${SP1(A.descriptor.description||"description missing")}`,Z=A.descriptor.unit?`
# UNIT ${B} ${SP1(A.descriptor.unit)}`:"",G=`# TYPE ${B} ${uz6(A)}`,F="";switch(Q){case lu.DataPointType.SUM:case lu.DataPointType.GAUGE:{F=A.dataPoints.map((I)=>this._serializeSingularDataPoint(B,A,I)).join("");break}case lu.DataPointType.HISTOGRAM:{F=A.dataPoints.map((I)=>this._serializeHistogramDataPoint(B,A,I)).join("");break}default:bz6.diag.error(`Unrecognizable DataPointType: ${Q} for metric "${B}"`)}return`${D}${Z}
${G}
${F}`.trim()}_serializeSingularDataPoint(A,B,Q){let D="";A=_J0(A,B);let{value:Z,attributes:G}=Q,F=Pt2.hrTimeToMilliseconds(Q.endTime);return D+=PP1(A,G,Z,this._appendTimestamp?F:void 0,this._additionalAttributes),D}_serializeHistogramDataPoint(A,B,Q){let D="";A=_J0(A,B);let{attributes:Z,value:G}=Q,F=Pt2.hrTimeToMilliseconds(Q.endTime);for(let J of["count","sum"]){let X=G[J];if(X!=null)D+=PP1(A+"_"+J,Z,X,this._appendTimestamp?F:void 0,this._additionalAttributes)}let I=0,Y=G.buckets.counts.entries(),W=!1;for(let[J,X]of Y){I+=X;let V=G.buckets.boundaries[J];if(V===void 0&&W)break;if(V===1/0)W=!0;D+=PP1(A+"_bucket",Z,I,this._appendTimestamp?F:void 0,Object.assign({},this._additionalAttributes??{},{le:V===void 0||V===1/0?"+Inf":String(V)}))}return D}_serializeResource(A){return`# HELP target_info Target metadata
# TYPE target_info gauge
${PP1("target_info",A.attributes,1).trim()}
`}}kt2.PrometheusSerializer=jt2});
var vt2=E((_t2)=>{Object.defineProperty(_t2,"__esModule",{value:!0});_t2.PrometheusExporter=void 0;var o31=ZQ(),dz6=y3(),bJ0=f_(),cz6=J1("http"),lz6=vJ0(),pz6=J1("url");class Dx extends bJ0.MetricReader{static DEFAULT_OPTIONS={host:void 0,port:9464,endpoint:"/metrics",prefix:"",appendTimestamp:!1,withResourceConstantLabels:void 0};_host;_port;_baseUrl;_endpoint;_server;_prefix;_appendTimestamp;_serializer;_startServerPromise;constructor(A={},B=()=>{}){super({aggregationSelector:(D)=>{return{type:bJ0.AggregationType.DEFAULT}},aggregationTemporalitySelector:(D)=>bJ0.AggregationTemporality.CUMULATIVE,metricProducers:A.metricProducers});this._host=A.host||process.env.OTEL_EXPORTER_PROMETHEUS_HOST||Dx.DEFAULT_OPTIONS.host,this._port=A.port||Number(process.env.OTEL_EXPORTER_PROMETHEUS_PORT)||Dx.DEFAULT_OPTIONS.port,this._prefix=A.prefix||Dx.DEFAULT_OPTIONS.prefix,this._appendTimestamp=typeof A.appendTimestamp==="boolean"?A.appendTimestamp:Dx.DEFAULT_OPTIONS.appendTimestamp;let Q=A.withResourceConstantLabels||Dx.DEFAULT_OPTIONS.withResourceConstantLabels;if(this._server=cz6.createServer(this._requestHandler).unref(),this._serializer=new lz6.PrometheusSerializer(this._prefix,this._appendTimestamp,Q),this._baseUrl=`http://${this._host}:${this._port}/`,this._endpoint=(A.endpoint||Dx.DEFAULT_OPTIONS.endpoint).replace(/^([^/])/,"/$1"),A.preventServerStart!==!0)this.startServer().then(B,(D)=>{o31.diag.error(D),B(D)});else if(B)queueMicrotask(B)}async onForceFlush(){}onShutdown(){return this.stopServer()}stopServer(){if(!this._server)return o31.diag.debug("Prometheus stopServer() was called but server was never started."),Promise.resolve();else return new Promise((A)=>{this._server.close((B)=>{if(!B)o31.diag.debug("Prometheus exporter was stopped");else if(B.code!=="ERR_SERVER_NOT_RUNNING")dz6.globalErrorHandler(B);A()})})}startServer(){return this._startServerPromise??=new Promise((A,B)=>{this._server.once("error",B),this._server.listen({port:this._port,host:this._host},()=>{o31.diag.debug(`Prometheus exporter server started: ${this._host}:${this._port}/${this._endpoint}`),A()})}),this._startServerPromise}getMetricsRequestHandler(A,B){this._exportMetrics(B)}_requestHandler=(A,B)=>{if(A.url!=null&&new pz6.URL(A.url,this._baseUrl).pathname===this._endpoint)this._exportMetrics(B);else this._notFound(B)};_exportMetrics=(A)=>{A.statusCode=200,A.setHeader("content-type","text/plain"),this.collect().then((B)=>{let{resourceMetrics:Q,errors:D}=B;if(D.length)o31.diag.error("PrometheusExporter: metrics collection errors",...D);A.end(this._serializer.serialize(Q))},(B)=>{A.end(`# failed to export metrics: ${B}`)})};_notFound=(A)=>{A.statusCode=404,A.end()}}_t2.PrometheusExporter=Dx});

module.exports = bt2;
