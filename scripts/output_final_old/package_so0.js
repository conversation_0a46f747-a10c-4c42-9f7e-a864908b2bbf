// Package extracted with entry point: so0

var ao0=E(($i8,RC1)=>{RC1.exports=["SIGABRT","SIGALRM","SIGHUP","SIGINT","SIGTERM"];if(process.platform!=="win32")RC1.exports.push("SIGV<PERSON><PERSON><PERSON>","SIGXCP<PERSON>","SIG<PERSON><PERSON><PERSON>","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT");if(process.platform==="linux")RC1.exports.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT","SIGUNUSED")});
var so0=E((qi8,qp)=>{var s7=global.process,af=function(A){return A&&typeof A==="object"&&typeof A.removeListener==="function"&&typeof A.emit==="function"&&typeof A.reallyExit==="function"&&typeof A.listeners==="function"&&typeof A.kill==="function"&&typeof A.pid==="number"&&typeof A.on==="function"};if(!af(s7))qp.exports=function(){return function(){}};else{if(Gi1=J1("assert"),sf=ao0(),Fi1=/^win/i.test(s7.platform),$p=J1("events"),typeof $p!=="function")$p=$p.EventEmitter;if(s7.__signal_exit_emitter__)EF=s7.__signal_exit_emitter__;else EF=s7.__signal_exit_emitter__=new $p,EF.count=0,EF.emitted={};if(!EF.infinite)EF.setMaxListeners(1/0),EF.infinite=!0;qp.exports=function(A,B){if(!af(global.process))return function(){};if(Gi1.equal(typeof A,"function","a callback must be provided for exit handler"),rf===!1)OC1();var Q="exit";if(B&&B.alwaysLast)Q="afterexit";var D=function(){if(EF.removeListener(Q,A),EF.listeners("exit").length===0&&EF.listeners("afterexit").length===0)Q91()};return EF.on(Q,A),D},Q91=function A(){if(!rf||!af(global.process))return;rf=!1,sf.forEach(function(B){try{s7.removeListener(B,D91[B])}catch(Q){}}),s7.emit=Z91,s7.reallyExit=TC1,EF.count-=1},qp.exports.unload=Q91,Yk=function A(B,Q,D){if(EF.emitted[B])return;EF.emitted[B]=!0,EF.emit(B,Q,D)},D91={},sf.forEach(function(A){D91[A]=function B(){if(!af(global.process))return;var Q=s7.listeners(A);if(Q.length===EF.count){if(Q91(),Yk("exit",null,A),Yk("afterexit",null,A),Fi1&&A==="SIGHUP")A="SIGINT";s7.kill(s7.pid,A)}}}),qp.exports.signals=function(){return sf},rf=!1,OC1=function A(){if(rf||!af(global.process))return;rf=!0,EF.count+=1,sf=sf.filter(function(B){try{return s7.on(B,D91[B]),!0}catch(Q){return!1}}),s7.emit=Yi1,s7.reallyExit=Ii1},qp.exports.load=OC1,TC1=s7.reallyExit,Ii1=function A(B){if(!af(global.process))return;s7.exitCode=B||0,Yk("exit",s7.exitCode,null),Yk("afterexit",s7.exitCode,null),TC1.call(s7,s7.exitCode)},Z91=s7.emit,Yi1=function A(B,Q){if(B==="exit"&&af(global.process)){if(Q!==void 0)s7.exitCode=Q;var D=Z91.apply(this,arguments);return Yk("exit",s7.exitCode,null),Yk("afterexit",s7.exitCode,null),D}else return Z91.apply(this,arguments)}}var Gi1,sf,Fi1,$p,EF,Q91,Yk,D91,rf,OC1,TC1,Ii1,Z91,Yi1});

module.exports = so0;
