// Package extracted with entry point: iUB

var EUB=E((h13,zUB)=>{var WZ1=OU0();function N08(A,B){if(A=WZ1.trimRight(A),A[A.length-1]!==";")A+=";";var Q=A.length,D=!1,Z=0,G=0,F="";function I(){if(!D){var J=WZ1.trim(A.slice(Z,G)),X=J.indexOf(":");if(X!==-1){var V=WZ1.trim(J.slice(0,X)),C=WZ1.trim(J.slice(X+1));if(V){var K=B(Z,F.length,V,C,J);if(K)F+=K+"; "}}}Z=G+1}for(;G<Q;G++){var Y=A[G];if(Y==="/"&&A[G+1]==="*"){var W=A.indexOf("*/",G+2);if(W===-1)break;G=W+1,Z=G+1,D=!1}else if(Y==="(")D=!0;else if(Y===")")D=!1;else if(Y===";")if(D);else I();else if(Y===`
`)I()}return WZ1.trim(F)}zUB.exports=N08});
var OU0=E((f13,HUB)=>{HUB.exports={indexOf:function(A,B){var Q,D;if(Array.prototype.indexOf)return A.indexOf(B);for(Q=0,D=A.length;Q<D;Q++)if(A[Q]===B)return Q;return-1},forEach:function(A,B,Q){var D,Z;if(Array.prototype.forEach)return A.forEach(B,Q);for(D=0,Z=A.length;D<Z;D++)B.call(Q,A[D],D,A)},trim:function(A){if(String.prototype.trim)return A.trim();return A.replace(/(^\s*)|(\s*$)/g,"")},trimRight:function(A){if(String.prototype.trimRight)return A.trimRight();return A.replace(/(\s*$)/g,"")}}});
var PU0=E((l08)=>{var O08=m_1().FilterCSS,T08=m_1().getDefaultWhiteList,l_1=d_1();function TUB(){return{a:["target","href","title"],abbr:["title"],address:[],area:["shape","coords","href","alt"],article:[],aside:[],audio:["autoplay","controls","crossorigin","loop","muted","preload","src"],b:[],bdi:["dir"],bdo:["dir"],big:[],blockquote:["cite"],br:[],caption:[],center:[],cite:[],code:[],col:["align","valign","span","width"],colgroup:["align","valign","span","width"],dd:[],del:["datetime"],details:["open"],div:[],dl:[],dt:[],em:[],figcaption:[],figure:[],font:["color","size","face"],footer:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],header:[],hr:[],i:[],img:["src","alt","title","width","height","loading"],ins:["datetime"],kbd:[],li:[],mark:[],nav:[],ol:[],p:[],pre:[],s:[],section:[],small:[],span:[],sub:[],summary:[],sup:[],strong:[],strike:[],table:["width","border","align","valign"],tbody:["align","valign"],td:["width","rowspan","colspan","align","valign"],tfoot:["align","valign"],th:["width","rowspan","colspan","align","valign"],thead:["align","valign"],tr:["rowspan","align","valign"],tt:[],u:[],ul:[],video:["autoplay","controls","crossorigin","loop","muted","playsinline","poster","preload","src","height","width"]}}var PUB=new O08;function P08(A,B,Q){}function S08(A,B,Q){}function j08(A,B,Q){}function k08(A,B,Q){}function SUB(A){return A.replace(_08,"&lt;").replace(x08,"&gt;")}function y08(A,B,Q,D){if(Q=vUB(Q),B==="href"||B==="src"){if(Q=l_1.trim(Q),Q==="#")return"#";if(!(Q.substr(0,7)==="http://"||Q.substr(0,8)==="https://"||Q.substr(0,7)==="mailto:"||Q.substr(0,4)==="tel:"||Q.substr(0,11)==="data:image/"||Q.substr(0,6)==="ftp://"||Q.substr(0,2)==="./"||Q.substr(0,3)==="../"||Q[0]==="#"||Q[0]==="/"))return""}else if(B==="background"){if(c_1.lastIndex=0,c_1.test(Q))return""}else if(B==="style"){if(RUB.lastIndex=0,RUB.test(Q))return"";if(OUB.lastIndex=0,OUB.test(Q)){if(c_1.lastIndex=0,c_1.test(Q))return""}if(D!==!1)D=D||PUB,Q=D.process(Q)}return Q=bUB(Q),Q}var _08=/</g,x08=/>/g,v08=/"/g,b08=/&quot;/g,f08=/&#([a-zA-Z0-9]*);?/gim,h08=/&colon;?/gim,g08=/&newline;?/gim,c_1=/((j\s*a\s*v\s*a|v\s*b|l\s*i\s*v\s*e)\s*s\s*c\s*r\s*i\s*p\s*t\s*|m\s*o\s*c\s*h\s*a):/gi,RUB=/e\s*x\s*p\s*r\s*e\s*s\s*s\s*i\s*o\s*n\s*\(.*/gi,OUB=/u\s*r\s*l\s*\(.*/gi;function jUB(A){return A.replace(v08,"&quot;")}function kUB(A){return A.replace(b08,'"')}function yUB(A){return A.replace(f08,function B(Q,D){return D[0]==="x"||D[0]==="X"?String.fromCharCode(parseInt(D.substr(1),16)):String.fromCharCode(parseInt(D,10))})}function _UB(A){return A.replace(h08,":").replace(g08," ")}function xUB(A){var B="";for(var Q=0,D=A.length;Q<D;Q++)B+=A.charCodeAt(Q)<32?" ":A.charAt(Q);return l_1.trim(B)}function vUB(A){return A=kUB(A),A=yUB(A),A=_UB(A),A=xUB(A),A}function bUB(A){return A=jUB(A),A=SUB(A),A}function u08(){return""}function m08(A,B){if(typeof B!=="function")B=function(){};var Q=!Array.isArray(A);function D(F){if(Q)return!0;return l_1.indexOf(A,F)!==-1}var Z=[],G=!1;return{onIgnoreTag:function(F,I,Y){if(D(F))if(Y.isClosing){var W="[/removed]",J=Y.position+W.length;return Z.push([G!==!1?G:Y.position,J]),G=!1,W}else{if(!G)G=Y.position;return"[removed]"}else return B(F,I,Y)},remove:function(F){var I="",Y=0;return l_1.forEach(Z,function(W){I+=F.slice(Y,W[0]),Y=W[1]}),I+=F.slice(Y),I}}}function d08(A){var B="",Q=0;while(Q<A.length){var D=A.indexOf("<!--",Q);if(D===-1){B+=A.slice(Q);break}B+=A.slice(Q,D);var Z=A.indexOf("-->",D);if(Z===-1)break;Q=Z+3}return B}function c08(A){var B=A.split("");return B=B.filter(function(Q){var D=Q.charCodeAt(0);if(D===127)return!1;if(D<=31){if(D===10||D===13)return!0;return!1}return!0}),B.join("")}l08.whiteList=TUB();l08.getDefaultWhiteList=TUB;l08.onTag=P08;l08.onIgnoreTag=S08;l08.onTagAttr=j08;l08.onIgnoreTagAttr=k08;l08.safeAttrValue=y08;l08.escapeHtml=SUB;l08.escapeQuote=jUB;l08.unescapeQuote=kUB;l08.escapeHtmlEntities=yUB;l08.escapeDangerHtml5Entities=_UB;l08.clearNonPrintableCharacter=xUB;l08.friendlyAttrValue=vUB;l08.escapeAttrValue=bUB;l08.onIgnoreTagStripAll=u08;l08.StripTagBody=m08;l08.stripCommentTag=d08;l08.stripBlankChar=c08;l08.attributeWrapSign='"';l08.cssFilter=PUB;l08.getDefaultCSSWhiteList=T08});
var RU0=E((z08)=>{function KUB(){var A={};return A["align-content"]=!1,A["align-items"]=!1,A["align-self"]=!1,A["alignment-adjust"]=!1,A["alignment-baseline"]=!1,A.all=!1,A["anchor-point"]=!1,A.animation=!1,A["animation-delay"]=!1,A["animation-direction"]=!1,A["animation-duration"]=!1,A["animation-fill-mode"]=!1,A["animation-iteration-count"]=!1,A["animation-name"]=!1,A["animation-play-state"]=!1,A["animation-timing-function"]=!1,A.azimuth=!1,A["backface-visibility"]=!1,A.background=!0,A["background-attachment"]=!0,A["background-clip"]=!0,A["background-color"]=!0,A["background-image"]=!0,A["background-origin"]=!0,A["background-position"]=!0,A["background-repeat"]=!0,A["background-size"]=!0,A["baseline-shift"]=!1,A.binding=!1,A.bleed=!1,A["bookmark-label"]=!1,A["bookmark-level"]=!1,A["bookmark-state"]=!1,A.border=!0,A["border-bottom"]=!0,A["border-bottom-color"]=!0,A["border-bottom-left-radius"]=!0,A["border-bottom-right-radius"]=!0,A["border-bottom-style"]=!0,A["border-bottom-width"]=!0,A["border-collapse"]=!0,A["border-color"]=!0,A["border-image"]=!0,A["border-image-outset"]=!0,A["border-image-repeat"]=!0,A["border-image-slice"]=!0,A["border-image-source"]=!0,A["border-image-width"]=!0,A["border-left"]=!0,A["border-left-color"]=!0,A["border-left-style"]=!0,A["border-left-width"]=!0,A["border-radius"]=!0,A["border-right"]=!0,A["border-right-color"]=!0,A["border-right-style"]=!0,A["border-right-width"]=!0,A["border-spacing"]=!0,A["border-style"]=!0,A["border-top"]=!0,A["border-top-color"]=!0,A["border-top-left-radius"]=!0,A["border-top-right-radius"]=!0,A["border-top-style"]=!0,A["border-top-width"]=!0,A["border-width"]=!0,A.bottom=!1,A["box-decoration-break"]=!0,A["box-shadow"]=!0,A["box-sizing"]=!0,A["box-snap"]=!0,A["box-suppress"]=!0,A["break-after"]=!0,A["break-before"]=!0,A["break-inside"]=!0,A["caption-side"]=!1,A.chains=!1,A.clear=!0,A.clip=!1,A["clip-path"]=!1,A["clip-rule"]=!1,A.color=!0,A["color-interpolation-filters"]=!0,A["column-count"]=!1,A["column-fill"]=!1,A["column-gap"]=!1,A["column-rule"]=!1,A["column-rule-color"]=!1,A["column-rule-style"]=!1,A["column-rule-width"]=!1,A["column-span"]=!1,A["column-width"]=!1,A.columns=!1,A.contain=!1,A.content=!1,A["counter-increment"]=!1,A["counter-reset"]=!1,A["counter-set"]=!1,A.crop=!1,A.cue=!1,A["cue-after"]=!1,A["cue-before"]=!1,A.cursor=!1,A.direction=!1,A.display=!0,A["display-inside"]=!0,A["display-list"]=!0,A["display-outside"]=!0,A["dominant-baseline"]=!1,A.elevation=!1,A["empty-cells"]=!1,A.filter=!1,A.flex=!1,A["flex-basis"]=!1,A["flex-direction"]=!1,A["flex-flow"]=!1,A["flex-grow"]=!1,A["flex-shrink"]=!1,A["flex-wrap"]=!1,A.float=!1,A["float-offset"]=!1,A["flood-color"]=!1,A["flood-opacity"]=!1,A["flow-from"]=!1,A["flow-into"]=!1,A.font=!0,A["font-family"]=!0,A["font-feature-settings"]=!0,A["font-kerning"]=!0,A["font-language-override"]=!0,A["font-size"]=!0,A["font-size-adjust"]=!0,A["font-stretch"]=!0,A["font-style"]=!0,A["font-synthesis"]=!0,A["font-variant"]=!0,A["font-variant-alternates"]=!0,A["font-variant-caps"]=!0,A["font-variant-east-asian"]=!0,A["font-variant-ligatures"]=!0,A["font-variant-numeric"]=!0,A["font-variant-position"]=!0,A["font-weight"]=!0,A.grid=!1,A["grid-area"]=!1,A["grid-auto-columns"]=!1,A["grid-auto-flow"]=!1,A["grid-auto-rows"]=!1,A["grid-column"]=!1,A["grid-column-end"]=!1,A["grid-column-start"]=!1,A["grid-row"]=!1,A["grid-row-end"]=!1,A["grid-row-start"]=!1,A["grid-template"]=!1,A["grid-template-areas"]=!1,A["grid-template-columns"]=!1,A["grid-template-rows"]=!1,A["hanging-punctuation"]=!1,A.height=!0,A.hyphens=!1,A.icon=!1,A["image-orientation"]=!1,A["image-resolution"]=!1,A["ime-mode"]=!1,A["initial-letters"]=!1,A["inline-box-align"]=!1,A["justify-content"]=!1,A["justify-items"]=!1,A["justify-self"]=!1,A.left=!1,A["letter-spacing"]=!0,A["lighting-color"]=!0,A["line-box-contain"]=!1,A["line-break"]=!1,A["line-grid"]=!1,A["line-height"]=!1,A["line-snap"]=!1,A["line-stacking"]=!1,A["line-stacking-ruby"]=!1,A["line-stacking-shift"]=!1,A["line-stacking-strategy"]=!1,A["list-style"]=!0,A["list-style-image"]=!0,A["list-style-position"]=!0,A["list-style-type"]=!0,A.margin=!0,A["margin-bottom"]=!0,A["margin-left"]=!0,A["margin-right"]=!0,A["margin-top"]=!0,A["marker-offset"]=!1,A["marker-side"]=!1,A.marks=!1,A.mask=!1,A["mask-box"]=!1,A["mask-box-outset"]=!1,A["mask-box-repeat"]=!1,A["mask-box-slice"]=!1,A["mask-box-source"]=!1,A["mask-box-width"]=!1,A["mask-clip"]=!1,A["mask-image"]=!1,A["mask-origin"]=!1,A["mask-position"]=!1,A["mask-repeat"]=!1,A["mask-size"]=!1,A["mask-source-type"]=!1,A["mask-type"]=!1,A["max-height"]=!0,A["max-lines"]=!1,A["max-width"]=!0,A["min-height"]=!0,A["min-width"]=!0,A["move-to"]=!1,A["nav-down"]=!1,A["nav-index"]=!1,A["nav-left"]=!1,A["nav-right"]=!1,A["nav-up"]=!1,A["object-fit"]=!1,A["object-position"]=!1,A.opacity=!1,A.order=!1,A.orphans=!1,A.outline=!1,A["outline-color"]=!1,A["outline-offset"]=!1,A["outline-style"]=!1,A["outline-width"]=!1,A.overflow=!1,A["overflow-wrap"]=!1,A["overflow-x"]=!1,A["overflow-y"]=!1,A.padding=!0,A["padding-bottom"]=!0,A["padding-left"]=!0,A["padding-right"]=!0,A["padding-top"]=!0,A.page=!1,A["page-break-after"]=!1,A["page-break-before"]=!1,A["page-break-inside"]=!1,A["page-policy"]=!1,A.pause=!1,A["pause-after"]=!1,A["pause-before"]=!1,A.perspective=!1,A["perspective-origin"]=!1,A.pitch=!1,A["pitch-range"]=!1,A["play-during"]=!1,A.position=!1,A["presentation-level"]=!1,A.quotes=!1,A["region-fragment"]=!1,A.resize=!1,A.rest=!1,A["rest-after"]=!1,A["rest-before"]=!1,A.richness=!1,A.right=!1,A.rotation=!1,A["rotation-point"]=!1,A["ruby-align"]=!1,A["ruby-merge"]=!1,A["ruby-position"]=!1,A["shape-image-threshold"]=!1,A["shape-outside"]=!1,A["shape-margin"]=!1,A.size=!1,A.speak=!1,A["speak-as"]=!1,A["speak-header"]=!1,A["speak-numeral"]=!1,A["speak-punctuation"]=!1,A["speech-rate"]=!1,A.stress=!1,A["string-set"]=!1,A["tab-size"]=!1,A["table-layout"]=!1,A["text-align"]=!0,A["text-align-last"]=!0,A["text-combine-upright"]=!0,A["text-decoration"]=!0,A["text-decoration-color"]=!0,A["text-decoration-line"]=!0,A["text-decoration-skip"]=!0,A["text-decoration-style"]=!0,A["text-emphasis"]=!0,A["text-emphasis-color"]=!0,A["text-emphasis-position"]=!0,A["text-emphasis-style"]=!0,A["text-height"]=!0,A["text-indent"]=!0,A["text-justify"]=!0,A["text-orientation"]=!0,A["text-overflow"]=!0,A["text-shadow"]=!0,A["text-space-collapse"]=!0,A["text-transform"]=!0,A["text-underline-position"]=!0,A["text-wrap"]=!0,A.top=!1,A.transform=!1,A["transform-origin"]=!1,A["transform-style"]=!1,A.transition=!1,A["transition-delay"]=!1,A["transition-duration"]=!1,A["transition-property"]=!1,A["transition-timing-function"]=!1,A["unicode-bidi"]=!1,A["vertical-align"]=!1,A.visibility=!1,A["voice-balance"]=!1,A["voice-duration"]=!1,A["voice-family"]=!1,A["voice-pitch"]=!1,A["voice-range"]=!1,A["voice-rate"]=!1,A["voice-stress"]=!1,A["voice-volume"]=!1,A.volume=!1,A["white-space"]=!1,A.widows=!1,A.width=!0,A["will-change"]=!1,A["word-break"]=!0,A["word-spacing"]=!0,A["word-wrap"]=!0,A["wrap-flow"]=!1,A["wrap-through"]=!1,A["writing-mode"]=!1,A["z-index"]=!1,A}function V08(A,B,Q){}function C08(A,B,Q){}var K08=/javascript\s*\:/img;function H08(A,B){if(K08.test(B))return"";return B}z08.whiteList=KUB();z08.getDefaultWhiteList=KUB;z08.onAttr=V08;z08.onIgnoreAttr=C08;z08.safeAttrValue=H08});
var SU0=E((NA8)=>{var rx=d_1();function CA8(A){var B=rx.spaceIndex(A),Q;if(B===-1)Q=A.slice(1,-1);else Q=A.slice(1,B+1);if(Q=rx.trim(Q).toLowerCase(),Q.slice(0,1)==="/")Q=Q.slice(1);if(Q.slice(-1)==="/")Q=Q.slice(0,-1);return Q}function KA8(A){return A.slice(0,2)==="</"}function HA8(A,B,Q){var D="",Z=0,G=!1,F=!1,I=0,Y=A.length,W="",J="";A:for(I=0;I<Y;I++){var X=A.charAt(I);if(G===!1){if(X==="<"){G=I;continue}}else if(F===!1){if(X==="<"){D+=Q(A.slice(Z,I)),G=I,Z=I;continue}if(X===">"||I===Y-1){D+=Q(A.slice(Z,G)),J=A.slice(G,I+1),W=CA8(J),D+=B(G,D.length,W,J,KA8(J)),Z=I+1,G=!1;continue}if(X==='"'||X==="'"){var V=1,C=A.charAt(I-V);while(C.trim()===""||C==="="){if(C==="="){F=X;continue A}C=A.charAt(I-++V)}}}else if(X===F){F=!1;continue}}if(Z<Y)D+=Q(A.substr(Z));return D}var zA8=/[^a-zA-Z0-9\\_:.-]/gim;function EA8(A,B){var Q=0,D=0,Z=[],G=!1,F=A.length;function I(V,C){if(V=rx.trim(V),V=V.replace(zA8,"").toLowerCase(),V.length<1)return;var K=B(V,C||"");if(K)Z.push(K)}for(var Y=0;Y<F;Y++){var W=A.charAt(Y),J,X;if(G===!1&&W==="="){G=A.slice(Q,Y),Q=Y+1,D=A.charAt(Q)==='"'||A.charAt(Q)==="'"?Q:wA8(A,Y+1);continue}if(G!==!1){if(Y===D)if(X=A.indexOf(W,Y+1),X===-1)break;else{J=rx.trim(A.slice(D+1,X)),I(G,J),G=!1,Y=X,Q=Y+1;continue}}if(/\s|\n|\t/.test(W))if(A=A.replace(/\s|\n|\t/g," "),G===!1)if(X=UA8(A,Y),X===-1){J=rx.trim(A.slice(Q,Y)),I(J),G=!1,Q=Y+1;continue}else{Y=X-1;continue}else if(X=$A8(A,Y-1),X===-1){J=rx.trim(A.slice(Q,Y)),J=fUB(J),I(G,J),G=!1,Q=Y+1;continue}else continue}if(Q<A.length)if(G===!1)I(A.slice(Q));else I(G,fUB(rx.trim(A.slice(Q))));return rx.trim(Z.join(" "))}function UA8(A,B){for(;B<A.length;B++){var Q=A[B];if(Q===" ")continue;if(Q==="=")return B;return-1}}function wA8(A,B){for(;B<A.length;B++){var Q=A[B];if(Q===" ")continue;if(Q==="'"||Q==='"')return B;return-1}}function $A8(A,B){for(;B>0;B--){var Q=A[B];if(Q===" ")continue;if(Q==="=")return B;return-1}}function qA8(A){if(A[0]==='"'&&A[A.length-1]==='"'||A[0]==="'"&&A[A.length-1]==="'")return!0;else return!1}function fUB(A){if(qA8(A))return A.substr(1,A.length-2);else return A}NA8.parseTag=HA8;NA8.parseAttr=EA8});
var d_1=E((m13,MUB)=>{MUB.exports={indexOf:function(A,B){var Q,D;if(Array.prototype.indexOf)return A.indexOf(B);for(Q=0,D=A.length;Q<D;Q++)if(A[Q]===B)return Q;return-1},forEach:function(A,B,Q){var D,Z;if(Array.prototype.forEach)return A.forEach(B,Q);for(D=0,Z=A.length;D<Z;D++)B.call(Q,A[D],D,A)},trim:function(A){if(String.prototype.trim)return A.trim();return A.replace(/(^\s*)|(\s*$)/g,"")},spaceIndex:function(A){var B=/\s|\n|\t/,Q=B.exec(A);return Q?Q.index:-1}}});
var iUB=E((Q11,n_1)=>{var dUB=PU0(),cUB=SU0(),lUB=mUB();function pUB(A,B){var Q=new lUB(B);return Q.process(A)}Q11=n_1.exports=pUB;Q11.filterXSS=pUB;Q11.FilterXSS=lUB;(function(){for(var A in dUB)Q11[A]=dUB[A];for(var B in cUB)Q11[B]=cUB[B]})();if(typeof window!=="undefined")window.filterXSS=n_1.exports;function kA8(){return typeof self!=="undefined"&&typeof DedicatedWorkerGlobalScope!=="undefined"&&self instanceof DedicatedWorkerGlobalScope}if(kA8())self.filterXSS=n_1.exports});
var mUB=E((l13,uUB)=>{var RA8=m_1().FilterCSS,uE=PU0(),hUB=SU0(),OA8=hUB.parseTag,TA8=hUB.parseAttr,i_1=d_1();function p_1(A){return A===void 0||A===null}function PA8(A){var B=i_1.spaceIndex(A);if(B===-1)return{html:"",closing:A[A.length-2]==="/"};A=i_1.trim(A.slice(B+1,-1));var Q=A[A.length-1]==="/";if(Q)A=i_1.trim(A.slice(0,-1));return{html:A,closing:Q}}function SA8(A){var B={};for(var Q in A)B[Q]=A[Q];return B}function jA8(A){var B={};for(var Q in A)if(Array.isArray(A[Q]))B[Q.toLowerCase()]=A[Q].map(function(D){return D.toLowerCase()});else B[Q.toLowerCase()]=A[Q];return B}function gUB(A){if(A=SA8(A||{}),A.stripIgnoreTag){if(A.onIgnoreTag)console.error('Notes: cannot use these two options "stripIgnoreTag" and "onIgnoreTag" at the same time');A.onIgnoreTag=uE.onIgnoreTagStripAll}if(A.whiteList||A.allowList)A.whiteList=jA8(A.whiteList||A.allowList);else A.whiteList=uE.whiteList;if(this.attributeWrapSign=A.singleQuotedAttributeValue===!0?"'":uE.attributeWrapSign,A.onTag=A.onTag||uE.onTag,A.onTagAttr=A.onTagAttr||uE.onTagAttr,A.onIgnoreTag=A.onIgnoreTag||uE.onIgnoreTag,A.onIgnoreTagAttr=A.onIgnoreTagAttr||uE.onIgnoreTagAttr,A.safeAttrValue=A.safeAttrValue||uE.safeAttrValue,A.escapeHtml=A.escapeHtml||uE.escapeHtml,this.options=A,A.css===!1)this.cssFilter=!1;else A.css=A.css||{},this.cssFilter=new RA8(A.css)}gUB.prototype.process=function(A){if(A=A||"",A=A.toString(),!A)return"";var B=this,Q=B.options,D=Q.whiteList,Z=Q.onTag,G=Q.onIgnoreTag,F=Q.onTagAttr,I=Q.onIgnoreTagAttr,Y=Q.safeAttrValue,W=Q.escapeHtml,J=B.attributeWrapSign,X=B.cssFilter;if(Q.stripBlankChar)A=uE.stripBlankChar(A);if(!Q.allowCommentTag)A=uE.stripCommentTag(A);var V=!1;if(Q.stripIgnoreTagBody)V=uE.StripTagBody(Q.stripIgnoreTagBody,G),G=V.onIgnoreTag;var C=OA8(A,function(K,H,z,$,L){var N={sourcePosition:K,position:H,isClosing:L,isWhite:Object.prototype.hasOwnProperty.call(D,z)},O=Z(z,$,N);if(!p_1(O))return O;if(N.isWhite){if(N.isClosing)return"</"+z+">";var R=PA8($),T=D[z],j=TA8(R.html,function(f,k){var c=i_1.indexOf(T,f)!==-1,h=F(z,f,k,c);if(!p_1(h))return h;if(c)if(k=Y(z,f,k,X),k)return f+"="+J+k+J;else return f;else{if(h=I(z,f,k,c),!p_1(h))return h;return}});if($="<"+z,j)$+=" "+j;if(R.closing)$+=" /";return $+=">",$}else{if(O=G(z,$,N),!p_1(O))return O;return W($)}},W);if(V)C=V.remove(C);return C};uUB.exports=gUB});
var m_1=E((u_1,TU0)=>{var NUB=RU0(),LUB=qUB();function R08(A,B){var Q=new LUB(B);return Q.process(A)}u_1=TU0.exports=R08;u_1.FilterCSS=LUB;for(g_1 in NUB)u_1[g_1]=NUB[g_1];var g_1;if(typeof window!=="undefined")window.filterCSS=TU0.exports});
var qUB=E((u13,$UB)=>{var h_1=RU0(),L08=EUB(),g13=OU0();function UUB(A){return A===void 0||A===null}function M08(A){var B={};for(var Q in A)B[Q]=A[Q];return B}function wUB(A){A=M08(A||{}),A.whiteList=A.whiteList||h_1.whiteList,A.onAttr=A.onAttr||h_1.onAttr,A.onIgnoreAttr=A.onIgnoreAttr||h_1.onIgnoreAttr,A.safeAttrValue=A.safeAttrValue||h_1.safeAttrValue,this.options=A}wUB.prototype.process=function(A){if(A=A||"",A=A.toString(),!A)return"";var B=this,Q=B.options,D=Q.whiteList,Z=Q.onAttr,G=Q.onIgnoreAttr,F=Q.safeAttrValue,I=L08(A,function(Y,W,J,X,V){var C=D[J],K=!1;if(C===!0)K=C;else if(typeof C==="function")K=C(X);else if(C instanceof RegExp)K=C.test(X);if(K!==!0)K=!1;if(X=F(J,X),!X)return;var H={position:W,sourcePosition:Y,source:V,isWhite:K};if(K){var z=Z(J,X,H);if(UUB(z))return J+":"+X;else return z}else{var z=G(J,X,H);if(!UUB(z))return z}});return I};$UB.exports=wUB});

module.exports = iUB;
