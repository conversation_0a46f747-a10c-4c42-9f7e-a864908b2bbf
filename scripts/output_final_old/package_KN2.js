// Package extracted with entry point: KN2

var KN2=E((_q5,CN2)=>{var Ip4=function A(B){return Yp4(B)&&!Wp4(B)};function Yp4(A){return!!A&&typeof A==="object"}function Wp4(A){var B=Object.prototype.toString.call(A);return B==="[object RegExp]"||B==="[object Date]"||Vp4(A)}var Jp4=typeof Symbol==="function"&&Symbol.for,Xp4=Jp4?Symbol.for("react.element"):60103;function Vp4(A){return A.$$typeof===Xp4}function Cp4(A){return Array.isArray(A)?[]:{}}function P51(A,B){return B.clone!==!1&&B.isMergeableObject(A)?Uo(Cp4(A),A,B):A}function Kp4(A,B,Q){return A.concat(B).map(function(D){return P51(D,Q)})}function Hp4(A,B){if(!B.customMerge)return Uo;var Q=B.customMerge(A);return typeof Q==="function"?Q:Uo}function zp4(A){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(A).filter(function(B){return Object.propertyIsEnumerable.call(A,B)}):[]}function XN2(A){return Object.keys(A).concat(zp4(A))}function VN2(A,B){try{return B in A}catch(Q){return!1}}function Ep4(A,B){return VN2(A,B)&&!(Object.hasOwnProperty.call(A,B)&&Object.propertyIsEnumerable.call(A,B))}function Up4(A,B,Q){var D={};if(Q.isMergeableObject(A))XN2(A).forEach(function(Z){D[Z]=P51(A[Z],Q)});return XN2(B).forEach(function(Z){if(Ep4(A,Z))return;if(VN2(A,Z)&&Q.isMergeableObject(B[Z]))D[Z]=Hp4(Z,Q)(A[Z],B[Z],Q);else D[Z]=P51(B[Z],Q)}),D}function Uo(A,B,Q){Q=Q||{},Q.arrayMerge=Q.arrayMerge||Kp4,Q.isMergeableObject=Q.isMergeableObject||Ip4,Q.cloneUnlessOtherwiseSpecified=P51;var D=Array.isArray(B),Z=Array.isArray(A),G=D===Z;if(!G)return P51(B,Q);else if(D)return Q.arrayMerge(A,B,Q);else return Up4(A,B,Q)}Uo.all=function A(B,Q){if(!Array.isArray(B))throw new Error("first argument should be an array");return B.reduce(function(D,Z){return Uo(D,Z,Q)},{})};var wp4=Uo;CN2.exports=wp4});

module.exports = KN2;
