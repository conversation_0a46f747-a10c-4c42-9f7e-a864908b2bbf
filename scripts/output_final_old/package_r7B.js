// Package extracted with entry point: r7B

var H7B=E((ja5,K7B)=>{var{defineProperty:Xk1,getOwnPropertyDescriptor:Jy6,getOwnPropertyNames:Xy6}=Object,Vy6=Object.prototype.hasOwnProperty,V7B=(A,B)=>Xk1(A,"name",{value:B,configurable:!0}),Cy6=(A,B)=>{for(var Q in B)Xk1(A,Q,{get:B[Q],enumerable:!0})},Ky6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Xy6(B))if(!Vy6.call(A,Z)&&Z!==Q)Xk1(A,Z,{get:()=>B[Z],enumerable:!(D=Jy6(B,Z))||D.enumerable})}return A},Hy6=(A)=>Ky6(Xk1({},"__esModule",{value:!0}),A),C7B={};Cy6(C7B,{fromArrayBuffer:()=>Ey6,fromString:()=>Uy6});K7B.exports=Hy6(C7B);var zy6=HK0(),zK0=J1("buffer"),Ey6=V7B((A,B=0,Q=A.byteLength-B)=>{if(!zy6.isArrayBuffer(A))throw new TypeError(`The "input" argument must be ArrayBuffer. Received type ${typeof A} (${A})`);return zK0.Buffer.from(A,B,Q)},"fromArrayBuffer"),Uy6=V7B((A,B)=>{if(typeof A!=="string")throw new TypeError(`The "input" argument must be of type string. Received type ${typeof A} (${A})`);return B?zK0.Buffer.from(A,B):zK0.Buffer.from(A)},"fromString")});
var HK0=E((Sa5,X7B)=>{var{defineProperty:Jk1,getOwnPropertyDescriptor:Qy6,getOwnPropertyNames:Dy6}=Object,Zy6=Object.prototype.hasOwnProperty,Gy6=(A,B)=>Jk1(A,"name",{value:B,configurable:!0}),Fy6=(A,B)=>{for(var Q in B)Jk1(A,Q,{get:B[Q],enumerable:!0})},Iy6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Dy6(B))if(!Zy6.call(A,Z)&&Z!==Q)Jk1(A,Z,{get:()=>B[Z],enumerable:!(D=Qy6(B,Z))||D.enumerable})}return A},Yy6=(A)=>Iy6(Jk1({},"__esModule",{value:!0}),A),J7B={};Fy6(J7B,{isArrayBuffer:()=>Wy6});X7B.exports=Yy6(J7B);var Wy6=Gy6((A)=>typeof ArrayBuffer==="function"&&A instanceof ArrayBuffer||Object.prototype.toString.call(A)==="[object ArrayBuffer]","isArrayBuffer")});
var O7B=E((ya5,R7B)=>{var{defineProperty:Ck1,getOwnPropertyDescriptor:Ty6,getOwnPropertyNames:Py6}=Object,Sy6=Object.prototype.hasOwnProperty,$7B=(A,B)=>Ck1(A,"name",{value:B,configurable:!0}),jy6=(A,B)=>{for(var Q in B)Ck1(A,Q,{get:B[Q],enumerable:!0})},ky6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of Py6(B))if(!Sy6.call(A,Z)&&Z!==Q)Ck1(A,Z,{get:()=>B[Z],enumerable:!(D=Ty6(B,Z))||D.enumerable})}return A},yy6=(A)=>ky6(Ck1({},"__esModule",{value:!0}),A),q7B={};jy6(q7B,{fromHex:()=>L7B,toHex:()=>M7B});R7B.exports=yy6(q7B);var N7B={},UK0={};for(let A=0;A<256;A++){let B=A.toString(16).toLowerCase();if(B.length===1)B=`0${B}`;N7B[A]=B,UK0[B]=A}function L7B(A){if(A.length%2!==0)throw new Error("Hex encoded strings must have an even number length");let B=new Uint8Array(A.length/2);for(let Q=0;Q<A.length;Q+=2){let D=A.slice(Q,Q+2).toLowerCase();if(D in UK0)B[Q/2]=UK0[D];else throw new Error(`Cannot decode unrecognized sequence ${D} as hexadecimal`)}return B}$7B(L7B,"fromHex");function M7B(A){let B="";for(let Q=0;Q<A.byteLength;Q++)B+=N7B[A[Q]];return B}$7B(M7B,"toHex")});
var W7B=E((Pa5,Y7B)=>{var{defineProperty:Wk1,getOwnPropertyDescriptor:ak6,getOwnPropertyNames:sk6}=Object,rk6=Object.prototype.hasOwnProperty,F7B=(A,B)=>Wk1(A,"name",{value:B,configurable:!0}),ok6=(A,B)=>{for(var Q in B)Wk1(A,Q,{get:B[Q],enumerable:!0})},tk6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of sk6(B))if(!rk6.call(A,Z)&&Z!==Q)Wk1(A,Z,{get:()=>B[Z],enumerable:!(D=ak6(B,Z))||D.enumerable})}return A},ek6=(A)=>tk6(Wk1({},"__esModule",{value:!0}),A),I7B={};ok6(I7B,{getSmithyContext:()=>Ay6,normalizeProvider:()=>By6});Y7B.exports=ek6(I7B);var G7B=Z7B(),Ay6=F7B((A)=>A[G7B.SMITHY_CONTEXT_KEY]||(A[G7B.SMITHY_CONTEXT_KEY]={}),"getSmithyContext"),By6=F7B((A)=>{if(typeof A==="function")return A;let B=Promise.resolve(A);return()=>B},"normalizeProvider")});
var Z7B=E((Ta5,D7B)=>{var{defineProperty:Ik1,getOwnPropertyDescriptor:fk6,getOwnPropertyNames:hk6}=Object,gk6=Object.prototype.hasOwnProperty,Yk1=(A,B)=>Ik1(A,"name",{value:B,configurable:!0}),uk6=(A,B)=>{for(var Q in B)Ik1(A,Q,{get:B[Q],enumerable:!0})},mk6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of hk6(B))if(!gk6.call(A,Z)&&Z!==Q)Ik1(A,Z,{get:()=>B[Z],enumerable:!(D=fk6(B,Z))||D.enumerable})}return A},dk6=(A)=>mk6(Ik1({},"__esModule",{value:!0}),A),s3B={};uk6(s3B,{AlgorithmId:()=>e3B,EndpointURLScheme:()=>t3B,FieldPosition:()=>A7B,HttpApiKeyAuthLocation:()=>o3B,HttpAuthLocation:()=>r3B,IniSectionType:()=>B7B,RequestHandlerProtocol:()=>Q7B,SMITHY_CONTEXT_KEY:()=>nk6,getDefaultClientConfiguration:()=>pk6,resolveDefaultRuntimeConfig:()=>ik6});D7B.exports=dk6(s3B);var r3B=((A)=>{return A.HEADER="header",A.QUERY="query",A})(r3B||{}),o3B=((A)=>{return A.HEADER="header",A.QUERY="query",A})(o3B||{}),t3B=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(t3B||{}),e3B=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(e3B||{}),ck6=Yk1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{_checksumAlgorithms:B,addChecksumAlgorithm(Q){this._checksumAlgorithms.push(Q)},checksumAlgorithms(){return this._checksumAlgorithms}}},"getChecksumConfiguration"),lk6=Yk1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),pk6=Yk1((A)=>{return{...ck6(A)}},"getDefaultClientConfiguration"),ik6=Yk1((A)=>{return{...lk6(A)}},"resolveDefaultRuntimeConfig"),A7B=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(A7B||{}),nk6="__smithy_context",B7B=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(B7B||{}),Q7B=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(Q7B||{})});
var j7B=E((_a5,S7B)=>{var{defineProperty:Kk1,getOwnPropertyDescriptor:_y6,getOwnPropertyNames:xy6}=Object,vy6=Object.prototype.hasOwnProperty,wK0=(A,B)=>Kk1(A,"name",{value:B,configurable:!0}),by6=(A,B)=>{for(var Q in B)Kk1(A,Q,{get:B[Q],enumerable:!0})},fy6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of xy6(B))if(!vy6.call(A,Z)&&Z!==Q)Kk1(A,Z,{get:()=>B[Z],enumerable:!(D=_y6(B,Z))||D.enumerable})}return A},hy6=(A)=>fy6(Kk1({},"__esModule",{value:!0}),A),T7B={};by6(T7B,{escapeUri:()=>P7B,escapeUriPath:()=>uy6});S7B.exports=hy6(T7B);var P7B=wK0((A)=>encodeURIComponent(A).replace(/[!'()*]/g,gy6),"escapeUri"),gy6=wK0((A)=>`%${A.charCodeAt(0).toString(16).toUpperCase()}`,"hexEncode"),uy6=wK0((A)=>A.split("/").map(P7B).join("/"),"escapeUriPath")});
var r7B=E((xa5,s7B)=>{var{defineProperty:wk1,getOwnPropertyDescriptor:my6,getOwnPropertyNames:dy6}=Object,cy6=Object.prototype.hasOwnProperty,pG=(A,B)=>wk1(A,"name",{value:B,configurable:!0}),ly6=(A,B)=>{for(var Q in B)wk1(A,Q,{get:B[Q],enumerable:!0})},py6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of dy6(B))if(!cy6.call(A,Z)&&Z!==Q)wk1(A,Z,{get:()=>B[Z],enumerable:!(D=my6(B,Z))||D.enumerable})}return A},iy6=(A)=>py6(wk1({},"__esModule",{value:!0}),A),v7B={};ly6(v7B,{SignatureV4:()=>w_6,clearCredentialCache:()=>W_6,createScope:()=>Ek1,getCanonicalHeaders:()=>LK0,getCanonicalQuery:()=>c7B,getPayloadHash:()=>Uk1,getSigningKey:()=>d7B,moveHeadersToQuery:()=>n7B,prepareRequest:()=>RK0});s7B.exports=iy6(v7B);var k7B=W7B(),$K0=wD1(),ny6="X-Amz-Algorithm",ay6="X-Amz-Credential",b7B="X-Amz-Date",sy6="X-Amz-SignedHeaders",ry6="X-Amz-Expires",f7B="X-Amz-Signature",h7B="X-Amz-Security-Token",g7B="authorization",u7B=b7B.toLowerCase(),oy6="date",ty6=[g7B,u7B,oy6],ey6=f7B.toLowerCase(),NK0="x-amz-content-sha256",A_6=h7B.toLowerCase(),B_6={authorization:!0,"cache-control":!0,connection:!0,expect:!0,from:!0,"keep-alive":!0,"max-forwards":!0,pragma:!0,referer:!0,te:!0,trailer:!0,"transfer-encoding":!0,upgrade:!0,"user-agent":!0,"x-amzn-trace-id":!0},Q_6=/^proxy-/,D_6=/^sec-/,qK0="AWS4-HMAC-SHA256",Z_6="AWS4-HMAC-SHA256-PAYLOAD",G_6="UNSIGNED-PAYLOAD",F_6=50,m7B="aws4_request",I_6=604800,Sx=O7B(),Y_6=wD1(),We={},zk1=[],Ek1=pG((A,B,Q)=>`${A}/${B}/${Q}/${m7B}`,"createScope"),d7B=pG(async(A,B,Q,D,Z)=>{let G=await y7B(A,B.secretAccessKey,B.accessKeyId),F=`${Q}:${D}:${Z}:${Sx.toHex(G)}:${B.sessionToken}`;if(F in We)return We[F];zk1.push(F);while(zk1.length>F_6)delete We[zk1.shift()];let I=`AWS4${B.secretAccessKey}`;for(let Y of[Q,D,Z,m7B])I=await y7B(A,I,Y);return We[F]=I},"getSigningKey"),W_6=pG(()=>{zk1.length=0,Object.keys(We).forEach((A)=>{delete We[A]})},"clearCredentialCache"),y7B=pG((A,B,Q)=>{let D=new A(B);return D.update(Y_6.toUint8Array(Q)),D.digest()},"hmac"),LK0=pG(({headers:A},B,Q)=>{let D={};for(let Z of Object.keys(A).sort()){if(A[Z]==null)continue;let G=Z.toLowerCase();if(G in B_6||(B==null?void 0:B.has(G))||Q_6.test(G)||D_6.test(G)){if(!Q||Q&&!Q.has(G))continue}D[G]=A[Z].trim().replace(/\s+/g," ")}return D},"getCanonicalHeaders"),$D1=j7B(),c7B=pG(({query:A={}})=>{let B=[],Q={};for(let D of Object.keys(A).sort()){if(D.toLowerCase()===ey6)continue;B.push(D);let Z=A[D];if(typeof Z==="string")Q[D]=`${$D1.escapeUri(D)}=${$D1.escapeUri(Z)}`;else if(Array.isArray(Z))Q[D]=Z.slice(0).reduce((G,F)=>G.concat([`${$D1.escapeUri(D)}=${$D1.escapeUri(F)}`]),[]).sort().join("&")}return B.map((D)=>Q[D]).filter((D)=>D).join("&")},"getCanonicalQuery"),J_6=HK0(),X_6=wD1(),Uk1=pG(async({headers:A,body:B},Q)=>{for(let D of Object.keys(A))if(D.toLowerCase()===NK0)return A[D];if(B==null)return"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855";else if(typeof B==="string"||ArrayBuffer.isView(B)||J_6.isArrayBuffer(B)){let D=new Q;return D.update(X_6.toUint8Array(B)),Sx.toHex(await D.digest())}return G_6},"getPayloadHash"),_7B=wD1(),l7B=class A{format(B){let Q=[];for(let G of Object.keys(B)){let F=_7B.fromUtf8(G);Q.push(Uint8Array.from([F.byteLength]),F,this.formatHeaderValue(B[G]))}let D=new Uint8Array(Q.reduce((G,F)=>G+F.byteLength,0)),Z=0;for(let G of Q)D.set(G,Z),Z+=G.byteLength;return D}formatHeaderValue(B){switch(B.type){case"boolean":return Uint8Array.from([B.value?0:1]);case"byte":return Uint8Array.from([2,B.value]);case"short":let Q=new DataView(new ArrayBuffer(3));return Q.setUint8(0,3),Q.setInt16(1,B.value,!1),new Uint8Array(Q.buffer);case"integer":let D=new DataView(new ArrayBuffer(5));return D.setUint8(0,4),D.setInt32(1,B.value,!1),new Uint8Array(D.buffer);case"long":let Z=new Uint8Array(9);return Z[0]=5,Z.set(B.value.bytes,1),Z;case"binary":let G=new DataView(new ArrayBuffer(3+B.value.byteLength));G.setUint8(0,6),G.setUint16(1,B.value.byteLength,!1);let F=new Uint8Array(G.buffer);return F.set(B.value,3),F;case"string":let I=_7B.fromUtf8(B.value),Y=new DataView(new ArrayBuffer(3+I.byteLength));Y.setUint8(0,7),Y.setUint16(1,I.byteLength,!1);let W=new Uint8Array(Y.buffer);return W.set(I,3),W;case"timestamp":let J=new Uint8Array(9);return J[0]=8,J.set(K_6.fromNumber(B.value.valueOf()).bytes,1),J;case"uuid":if(!C_6.test(B.value))throw new Error(`Invalid UUID received: ${B.value}`);let X=new Uint8Array(17);return X[0]=9,X.set(Sx.fromHex(B.value.replace(/\-/g,"")),1),X}}};pG(l7B,"HeaderFormatter");var V_6=l7B,C_6=/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/,p7B=class A{constructor(B){if(this.bytes=B,B.byteLength!==8)throw new Error("Int64 buffers must be exactly 8 bytes")}static fromNumber(B){if(B>9223372036854776000||B<-9223372036854776000)throw new Error(`${B} is too large (or, if negative, too small) to represent as an Int64`);let Q=new Uint8Array(8);for(let D=7,Z=Math.abs(Math.round(B));D>-1&&Z>0;D--,Z/=256)Q[D]=Z;if(B<0)MK0(Q);return new A(Q)}valueOf(){let B=this.bytes.slice(0),Q=B[0]&128;if(Q)MK0(B);return parseInt(Sx.toHex(B),16)*(Q?-1:1)}toString(){return String(this.valueOf())}};pG(p7B,"Int64");var K_6=p7B;function MK0(A){for(let B=0;B<8;B++)A[B]^=255;for(let B=7;B>-1;B--)if(A[B]++,A[B]!==0)break}pG(MK0,"negate");var H_6=pG((A,B)=>{A=A.toLowerCase();for(let Q of Object.keys(B))if(A===Q.toLowerCase())return!0;return!1},"hasHeader"),i7B=pG(({headers:A,query:B,...Q})=>({...Q,headers:{...A},query:B?z_6(B):void 0}),"cloneRequest"),z_6=pG((A)=>Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{}),"cloneQuery"),n7B=pG((A,B={})=>{var Q;let{headers:D,query:Z={}}=typeof A.clone==="function"?A.clone():i7B(A);for(let G of Object.keys(D)){let F=G.toLowerCase();if(F.slice(0,6)==="x-amz-"&&!((Q=B.unhoistableHeaders)==null?void 0:Q.has(F)))Z[G]=D[G],delete D[G]}return{...A,headers:D,query:Z}},"moveHeadersToQuery"),RK0=pG((A)=>{A=typeof A.clone==="function"?A.clone():i7B(A);for(let B of Object.keys(A.headers))if(ty6.indexOf(B.toLowerCase())>-1)delete A.headers[B];return A},"prepareRequest"),E_6=pG((A)=>U_6(A).toISOString().replace(/\.\d{3}Z$/,"Z"),"iso8601"),U_6=pG((A)=>{if(typeof A==="number")return new Date(A*1000);if(typeof A==="string"){if(Number(A))return new Date(Number(A)*1000);return new Date(A)}return A},"toDate"),a7B=class A{constructor({applyChecksum:B,credentials:Q,region:D,service:Z,sha256:G,uriEscapePath:F=!0}){this.headerFormatter=new V_6,this.service=Z,this.sha256=G,this.uriEscapePath=F,this.applyChecksum=typeof B==="boolean"?B:!0,this.regionProvider=k7B.normalizeProvider(D),this.credentialProvider=k7B.normalizeProvider(Q)}async presign(B,Q={}){let{signingDate:D=new Date,expiresIn:Z=3600,unsignableHeaders:G,unhoistableHeaders:F,signableHeaders:I,signingRegion:Y,signingService:W}=Q,J=await this.credentialProvider();this.validateResolvedCredentials(J);let X=Y??await this.regionProvider(),{longDate:V,shortDate:C}=Hk1(D);if(Z>I_6)return Promise.reject("Signature version 4 presigned URLs must have an expiration date less than one week in the future");let K=Ek1(C,X,W??this.service),H=n7B(RK0(B),{unhoistableHeaders:F});if(J.sessionToken)H.query[h7B]=J.sessionToken;H.query[ny6]=qK0,H.query[ay6]=`${J.accessKeyId}/${K}`,H.query[b7B]=V,H.query[ry6]=Z.toString(10);let z=LK0(H,G,I);return H.query[sy6]=x7B(z),H.query[f7B]=await this.getSignature(V,K,this.getSigningKey(J,X,C,W),this.createCanonicalRequest(H,z,await Uk1(B,this.sha256))),H}async sign(B,Q){if(typeof B==="string")return this.signString(B,Q);else if(B.headers&&B.payload)return this.signEvent(B,Q);else if(B.message)return this.signMessage(B,Q);else return this.signRequest(B,Q)}async signEvent({headers:B,payload:Q},{signingDate:D=new Date,priorSignature:Z,signingRegion:G,signingService:F}){let I=G??await this.regionProvider(),{shortDate:Y,longDate:W}=Hk1(D),J=Ek1(Y,I,F??this.service),X=await Uk1({headers:{},body:Q},this.sha256),V=new this.sha256;V.update(B);let C=Sx.toHex(await V.digest()),K=[Z_6,W,J,Z,C,X].join(`
`);return this.signString(K,{signingDate:D,signingRegion:I,signingService:F})}async signMessage(B,{signingDate:Q=new Date,signingRegion:D,signingService:Z}){return this.signEvent({headers:this.headerFormatter.format(B.message.headers),payload:B.message.body},{signingDate:Q,signingRegion:D,signingService:Z,priorSignature:B.priorSignature}).then((F)=>{return{message:B.message,signature:F}})}async signString(B,{signingDate:Q=new Date,signingRegion:D,signingService:Z}={}){let G=await this.credentialProvider();this.validateResolvedCredentials(G);let F=D??await this.regionProvider(),{shortDate:I}=Hk1(Q),Y=new this.sha256(await this.getSigningKey(G,F,I,Z));return Y.update($K0.toUint8Array(B)),Sx.toHex(await Y.digest())}async signRequest(B,{signingDate:Q=new Date,signableHeaders:D,unsignableHeaders:Z,signingRegion:G,signingService:F}={}){let I=await this.credentialProvider();this.validateResolvedCredentials(I);let Y=G??await this.regionProvider(),W=RK0(B),{longDate:J,shortDate:X}=Hk1(Q),V=Ek1(X,Y,F??this.service);if(W.headers[u7B]=J,I.sessionToken)W.headers[A_6]=I.sessionToken;let C=await Uk1(W,this.sha256);if(!H_6(NK0,W.headers)&&this.applyChecksum)W.headers[NK0]=C;let K=LK0(W,Z,D),H=await this.getSignature(J,V,this.getSigningKey(I,Y,X,F),this.createCanonicalRequest(W,K,C));return W.headers[g7B]=`${qK0} Credential=${I.accessKeyId}/${V}, SignedHeaders=${x7B(K)}, Signature=${H}`,W}createCanonicalRequest(B,Q,D){let Z=Object.keys(Q).sort();return`${B.method}
${this.getCanonicalPath(B)}
${c7B(B)}
${Z.map((G)=>`${G}:${Q[G]}`).join(`
`)}

${Z.join(";")}
${D}`}async createStringToSign(B,Q,D){let Z=new this.sha256;Z.update($K0.toUint8Array(D));let G=await Z.digest();return`${qK0}
${B}
${Q}
${Sx.toHex(G)}`}getCanonicalPath({path:B}){if(this.uriEscapePath){let Q=[];for(let G of B.split("/")){if((G==null?void 0:G.length)===0)continue;if(G===".")continue;if(G==="..")Q.pop();else Q.push(G)}let D=`${(B==null?void 0:B.startsWith("/"))?"/":""}${Q.join("/")}${Q.length>0&&(B==null?void 0:B.endsWith("/"))?"/":""}`;return $D1.escapeUri(D).replace(/%2F/g,"/")}return B}async getSignature(B,Q,D,Z){let G=await this.createStringToSign(B,Q,Z),F=new this.sha256(await D);return F.update($K0.toUint8Array(G)),Sx.toHex(await F.digest())}getSigningKey(B,Q,D,Z){return d7B(this.sha256,B,D,Q,Z||this.service)}validateResolvedCredentials(B){if(typeof B!=="object"||typeof B.accessKeyId!=="string"||typeof B.secretAccessKey!=="string")throw new Error("Resolved credential object is not valid")}};pG(a7B,"SignatureV4");var w_6=a7B,Hk1=pG((A)=>{let B=E_6(A).replace(/[\-:]/g,"");return{longDate:B,shortDate:B.slice(0,8)}},"formatDate"),x7B=pG((A)=>Object.keys(A).sort().join(";"),"getCanonicalHeaderList")});
var wD1=E((ka5,w7B)=>{var{defineProperty:Vk1,getOwnPropertyDescriptor:wy6,getOwnPropertyNames:$y6}=Object,qy6=Object.prototype.hasOwnProperty,EK0=(A,B)=>Vk1(A,"name",{value:B,configurable:!0}),Ny6=(A,B)=>{for(var Q in B)Vk1(A,Q,{get:B[Q],enumerable:!0})},Ly6=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of $y6(B))if(!qy6.call(A,Z)&&Z!==Q)Vk1(A,Z,{get:()=>B[Z],enumerable:!(D=wy6(B,Z))||D.enumerable})}return A},My6=(A)=>Ly6(Vk1({},"__esModule",{value:!0}),A),z7B={};Ny6(z7B,{fromUtf8:()=>U7B,toUint8Array:()=>Ry6,toUtf8:()=>Oy6});w7B.exports=My6(z7B);var E7B=H7B(),U7B=EK0((A)=>{let B=E7B.fromString(A,"utf8");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength/Uint8Array.BYTES_PER_ELEMENT)},"fromUtf8"),Ry6=EK0((A)=>{if(typeof A==="string")return U7B(A);if(ArrayBuffer.isView(A))return new Uint8Array(A.buffer,A.byteOffset,A.byteLength/Uint8Array.BYTES_PER_ELEMENT);return new Uint8Array(A)},"toUint8Array"),Oy6=EK0((A)=>{if(typeof A==="string")return A;if(typeof A!=="object"||typeof A.byteOffset!=="number"||typeof A.byteLength!=="number")throw new Error("@smithy/util-utf8: toUtf8 encoder function only accepts string | Uint8Array.");return E7B.fromArrayBuffer(A.buffer,A.byteOffset,A.byteLength).toString("utf8")},"toUtf8")});

module.exports = r7B;
