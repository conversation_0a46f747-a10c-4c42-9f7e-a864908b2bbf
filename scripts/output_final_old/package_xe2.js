// Package extracted with entry point: xe2

var De2=E((Be2)=>{Object.defineProperty(Be2,"__esModule",{value:!0});Be2.LoggerProviderSharedState=void 0;var QE6=hJ0();class Ae2{resource;forceFlushTimeoutMillis;logRecordLimits;loggers=new Map;activeProcessor;registeredLogRecordProcessors=[];constructor(A,B,Q){this.resource=A,this.forceFlushTimeoutMillis=B,this.logRecordLimits=Q,this.activeProcessor=new QE6.NoopLogRecordProcessor}}Be2.LoggerProviderSharedState=Ae2});
var FO2=E((FF0)=>{Object.defineProperty(FF0,"__esModule",{value:!0});FF0._globalThis=void 0;var Ta4=GO2();Object.defineProperty(FF0,"_globalThis",{enumerable:!0,get:function(){return Ta4._globalThis}})});
var GF0=E((BO2)=>{Object.defineProperty(BO2,"__esModule",{value:!0});BO2.ProxyLoggerProvider=void 0;var Ra4=zO1(),Oa4=ZF0();class AO2{getLogger(A,B,Q){var D;return(D=this.getDelegateLogger(A,B,Q))!==null&&D!==void 0?D:new Oa4.ProxyLogger(this,A,B,Q)}getDelegate(){var A;return(A=this._delegate)!==null&&A!==void 0?A:Ra4.NOOP_LOGGER_PROVIDER}setDelegate(A){this._delegate=A}getDelegateLogger(A,B,Q){var D;return(D=this._delegate)===null||D===void 0?void 0:D.getLogger(A,B,Q)}}BO2.ProxyLoggerProvider=AO2});
var GO2=E((DO2)=>{Object.defineProperty(DO2,"__esModule",{value:!0});DO2._globalThis=void 0;DO2._globalThis=typeof globalThis==="object"?globalThis:global});
var HO1=E((nR2)=>{Object.defineProperty(nR2,"__esModule",{value:!0});nR2.NOOP_LOGGER=nR2.NoopLogger=void 0;class QF0{emit(A){}}nR2.NoopLogger=QF0;nR2.NOOP_LOGGER=new QF0});
var IO2=E((IF0)=>{Object.defineProperty(IF0,"__esModule",{value:!0});IF0._globalThis=void 0;var Sa4=FO2();Object.defineProperty(IF0,"_globalThis",{enumerable:!0,get:function(){return Sa4._globalThis}})});
var JO2=E((YO2)=>{Object.defineProperty(YO2,"__esModule",{value:!0});YO2.API_BACKWARDS_COMPATIBILITY_VERSION=YO2.makeGetter=YO2._global=YO2.GLOBAL_LOGS_API_KEY=void 0;var ka4=IO2();YO2.GLOBAL_LOGS_API_KEY=Symbol.for("io.opentelemetry.js.api.logs");YO2._global=ka4._globalThis;function ya4(A,B,Q){return(D)=>D===A?B:Q}YO2.makeGetter=ya4;YO2.API_BACKWARDS_COMPATIBILITY_VERSION=1});
var Je2=E((Ie2)=>{Object.defineProperty(Ie2,"__esModule",{value:!0});Ie2.LoggerProvider=Ie2.DEFAULT_LOGGER_NAME=void 0;var t31=ZQ(),DE6=WF0(),ZE6=mO1(),Ze2=y3(),GE6=ct2(),Ge2=it2(),FE6=rt2(),IE6=De2();Ie2.DEFAULT_LOGGER_NAME="unknown";class Fe2{_shutdownOnce;_sharedState;constructor(A={}){let B=Ze2.merge({},Ge2.loadDefaultConfig(),A),Q=A.resource??ZE6.defaultResource();this._sharedState=new IE6.LoggerProviderSharedState(Q,B.forceFlushTimeoutMillis,Ge2.reconfigureLimits(B.logRecordLimits)),this._shutdownOnce=new Ze2.BindOnceFuture(this._shutdown,this)}getLogger(A,B,Q){if(this._shutdownOnce.isCalled)return t31.diag.warn("A shutdown LoggerProvider cannot provide a Logger"),DE6.NOOP_LOGGER;if(!A)t31.diag.warn("Logger requested without instrumentation scope name.");let D=A||Ie2.DEFAULT_LOGGER_NAME,Z=`${D}@${B||""}:${Q?.schemaUrl||""}`;if(!this._sharedState.loggers.has(Z))this._sharedState.loggers.set(Z,new GE6.Logger({name:D,version:B,schemaUrl:Q?.schemaUrl},this._sharedState));return this._sharedState.loggers.get(Z)}addLogRecordProcessor(A){if(this._sharedState.registeredLogRecordProcessors.length===0)this._sharedState.activeProcessor.shutdown().catch((B)=>t31.diag.error("Error while trying to shutdown current log record processor",B));this._sharedState.registeredLogRecordProcessors.push(A),this._sharedState.activeProcessor=new FE6.MultiLogRecordProcessor(this._sharedState.registeredLogRecordProcessors,this._sharedState.forceFlushTimeoutMillis)}forceFlush(){if(this._shutdownOnce.isCalled)return t31.diag.warn("invalid attempt to force flush after LoggerProvider shutdown"),this._shutdownOnce.promise;return this._sharedState.activeProcessor.forceFlush()}shutdown(){if(this._shutdownOnce.isCalled)return t31.diag.warn("shutdown may only be called once per LoggerProvider"),this._shutdownOnce.promise;return this._shutdownOnce.call()}_shutdown(){return this._sharedState.activeProcessor.shutdown()}}Ie2.LoggerProvider=Fe2});
var KO2=E((VO2)=>{Object.defineProperty(VO2,"__esModule",{value:!0});VO2.LogsAPI=void 0;var FE=JO2(),ba4=zO1(),XO2=GF0();class YF0{constructor(){this._proxyLoggerProvider=new XO2.ProxyLoggerProvider}static getInstance(){if(!this._instance)this._instance=new YF0;return this._instance}setGlobalLoggerProvider(A){if(FE._global[FE.GLOBAL_LOGS_API_KEY])return this.getLoggerProvider();return FE._global[FE.GLOBAL_LOGS_API_KEY]=FE.makeGetter(FE.API_BACKWARDS_COMPATIBILITY_VERSION,A,ba4.NOOP_LOGGER_PROVIDER),this._proxyLoggerProvider.setDelegate(A),A}getLoggerProvider(){var A,B;return(B=(A=FE._global[FE.GLOBAL_LOGS_API_KEY])===null||A===void 0?void 0:A.call(FE._global,FE.API_BACKWARDS_COMPATIBILITY_VERSION))!==null&&B!==void 0?B:this._proxyLoggerProvider}getLogger(A,B,Q){return this.getLoggerProvider().getLogger(A,B,Q)}disable(){delete FE._global[FE.GLOBAL_LOGS_API_KEY],this._proxyLoggerProvider=new XO2.ProxyLoggerProvider}}VO2.LogsAPI=YF0});
var Ke2=E((Ve2)=>{Object.defineProperty(Ve2,"__esModule",{value:!0});Ve2.ConsoleLogRecordExporter=void 0;var YE6=y3(),WE6=y3();class Xe2{export(A,B){this._sendLogRecords(A,B)}shutdown(){return Promise.resolve()}_exportInfo(A){return{resource:{attributes:A.resource.attributes},instrumentationScope:A.instrumentationScope,timestamp:YE6.hrTimeToMicroseconds(A.hrTime),traceId:A.spanContext?.traceId,spanId:A.spanContext?.spanId,traceFlags:A.spanContext?.traceFlags,severityText:A.severityText,severityNumber:A.severityNumber,body:A.body,attributes:A.attributes}}_sendLogRecords(A,B){for(let Q of A)console.dir(this._exportInfo(Q),{depth:3});B?.({code:WE6.ExportResultCode.SUCCESS})}}Ve2.ConsoleLogRecordExporter=Xe2});
var Le2=E((qe2)=>{Object.defineProperty(qe2,"__esModule",{value:!0});qe2.InMemoryLogRecordExporter=void 0;var we2=y3();class $e2{_finishedLogRecords=[];_stopped=!1;export(A,B){if(this._stopped)return B({code:we2.ExportResultCode.FAILED,error:new Error("Exporter has been stopped")});this._finishedLogRecords.push(...A),B({code:we2.ExportResultCode.SUCCESS})}shutdown(){return this._stopped=!0,this.reset(),Promise.resolve()}getFinishedLogRecords(){return this._finishedLogRecords}reset(){this._finishedLogRecords=[]}}qe2.InMemoryLogRecordExporter=$e2});
var Te2=E((Re2)=>{Object.defineProperty(Re2,"__esModule",{value:!0});Re2.BatchLogRecordProcessorBase=void 0;var yP1=y3(),JE6=ZQ(),zP=y3();class Me2{_exporter;_maxExportBatchSize;_maxQueueSize;_scheduledDelayMillis;_exportTimeoutMillis;_finishedLogRecords=[];_timer;_shutdownOnce;constructor(A,B){if(this._exporter=A,this._maxExportBatchSize=B?.maxExportBatchSize??yP1.getNumberFromEnv("OTEL_BLRP_MAX_EXPORT_BATCH_SIZE")??512,this._maxQueueSize=B?.maxQueueSize??yP1.getNumberFromEnv("OTEL_BLRP_MAX_QUEUE_SIZE")??2048,this._scheduledDelayMillis=B?.scheduledDelayMillis??yP1.getNumberFromEnv("OTEL_BLRP_SCHEDULE_DELAY")??5000,this._exportTimeoutMillis=B?.exportTimeoutMillis??yP1.getNumberFromEnv("OTEL_BLRP_EXPORT_TIMEOUT")??30000,this._shutdownOnce=new zP.BindOnceFuture(this._shutdown,this),this._maxExportBatchSize>this._maxQueueSize)JE6.diag.warn("BatchLogRecordProcessor: maxExportBatchSize must be smaller or equal to maxQueueSize, setting maxExportBatchSize to match maxQueueSize"),this._maxExportBatchSize=this._maxQueueSize}onEmit(A){if(this._shutdownOnce.isCalled)return;this._addToBuffer(A)}forceFlush(){if(this._shutdownOnce.isCalled)return this._shutdownOnce.promise;return this._flushAll()}shutdown(){return this._shutdownOnce.call()}async _shutdown(){this.onShutdown(),await this._flushAll(),await this._exporter.shutdown()}_addToBuffer(A){if(this._finishedLogRecords.length>=this._maxQueueSize)return;this._finishedLogRecords.push(A),this._maybeStartTimer()}_flushAll(){return new Promise((A,B)=>{let Q=[],D=Math.ceil(this._finishedLogRecords.length/this._maxExportBatchSize);for(let Z=0;Z<D;Z++)Q.push(this._flushOneBatch());Promise.all(Q).then(()=>{A()}).catch(B)})}_flushOneBatch(){if(this._clearTimer(),this._finishedLogRecords.length===0)return Promise.resolve();return new Promise((A,B)=>{zP.callWithTimeout(this._export(this._finishedLogRecords.splice(0,this._maxExportBatchSize)),this._exportTimeoutMillis).then(()=>A()).catch(B)})}_maybeStartTimer(){if(this._timer!==void 0)return;this._timer=setTimeout(()=>{this._flushOneBatch().then(()=>{if(this._finishedLogRecords.length>0)this._clearTimer(),this._maybeStartTimer()}).catch((A)=>{zP.globalErrorHandler(A)})},this._scheduledDelayMillis),zP.unrefTimer(this._timer)}_clearTimer(){if(this._timer!==void 0)clearTimeout(this._timer),this._timer=void 0}_export(A){let B=()=>zP.internal._export(this._exporter,A).then((D)=>{if(D.code!==zP.ExportResultCode.SUCCESS)zP.globalErrorHandler(D.error??new Error(`BatchLogRecordProcessor: log record export failed (status ${D})`))}).catch(zP.globalErrorHandler),Q=A.map((D)=>D.resource).filter((D)=>D.asyncAttributesPending);if(Q.length===0)return B();else return Promise.all(Q.map((D)=>D.waitForAsyncAttributes?.())).then(B,zP.globalErrorHandler)}}Re2.BatchLogRecordProcessorBase=Me2});
var Ue2=E((ze2)=>{Object.defineProperty(ze2,"__esModule",{value:!0});ze2.SimpleLogRecordProcessor=void 0;var wt=y3();class He2{_exporter;_shutdownOnce;_unresolvedExports;constructor(A){this._exporter=A,this._shutdownOnce=new wt.BindOnceFuture(this._shutdown,this),this._unresolvedExports=new Set}onEmit(A){if(this._shutdownOnce.isCalled)return;let B=()=>wt.internal._export(this._exporter,[A]).then((Q)=>{if(Q.code!==wt.ExportResultCode.SUCCESS)wt.globalErrorHandler(Q.error??new Error(`SimpleLogRecordProcessor: log record export failed (status ${Q})`))}).catch(wt.globalErrorHandler);if(A.resource.asyncAttributesPending){let Q=A.resource.waitForAsyncAttributes?.().then(()=>{return this._unresolvedExports.delete(Q),B()},wt.globalErrorHandler);if(Q!=null)this._unresolvedExports.add(Q)}else B()}async forceFlush(){await Promise.all(Array.from(this._unresolvedExports))}shutdown(){return this._shutdownOnce.call()}_shutdown(){return this._exporter.shutdown()}}ze2.SimpleLogRecordProcessor=He2});
var WF0=E((ZP)=>{Object.defineProperty(ZP,"__esModule",{value:!0});ZP.logs=ZP.ProxyLoggerProvider=ZP.ProxyLogger=ZP.NoopLoggerProvider=ZP.NOOP_LOGGER_PROVIDER=ZP.NoopLogger=ZP.NOOP_LOGGER=ZP.SeverityNumber=void 0;var fa4=iR2();Object.defineProperty(ZP,"SeverityNumber",{enumerable:!0,get:function(){return fa4.SeverityNumber}});var HO2=HO1();Object.defineProperty(ZP,"NOOP_LOGGER",{enumerable:!0,get:function(){return HO2.NOOP_LOGGER}});Object.defineProperty(ZP,"NoopLogger",{enumerable:!0,get:function(){return HO2.NoopLogger}});var zO2=zO1();Object.defineProperty(ZP,"NOOP_LOGGER_PROVIDER",{enumerable:!0,get:function(){return zO2.NOOP_LOGGER_PROVIDER}});Object.defineProperty(ZP,"NoopLoggerProvider",{enumerable:!0,get:function(){return zO2.NoopLoggerProvider}});var ha4=ZF0();Object.defineProperty(ZP,"ProxyLogger",{enumerable:!0,get:function(){return ha4.ProxyLogger}});var ga4=GF0();Object.defineProperty(ZP,"ProxyLoggerProvider",{enumerable:!0,get:function(){return ga4.ProxyLoggerProvider}});var ua4=KO2();ZP.logs=ua4.LogsAPI.getInstance()});
var ZF0=E((tR2)=>{Object.defineProperty(tR2,"__esModule",{value:!0});tR2.ProxyLogger=void 0;var Ma4=HO1();class oR2{constructor(A,B,Q,D){this._provider=A,this.name=B,this.version=Q,this.options=D}emit(A){this._getLogger().emit(A)}_getLogger(){if(this._delegate)return this._delegate;let A=this._provider.getDelegateLogger(this.name,this.version,this.options);if(!A)return Ma4.NOOP_LOGGER;return this._delegate=A,this._delegate}}tR2.ProxyLogger=oR2});
var _e2=E((uJ0)=>{Object.defineProperty(uJ0,"__esModule",{value:!0});uJ0.BatchLogRecordProcessor=void 0;var KE6=ye2();Object.defineProperty(uJ0,"BatchLogRecordProcessor",{enumerable:!0,get:function(){return KE6.BatchLogRecordProcessor}})});
var ct2=E((mt2)=>{Object.defineProperty(mt2,"__esModule",{value:!0});mt2.Logger=void 0;var rz6=ZQ(),oz6=fJ0();class ut2{instrumentationScope;_sharedState;constructor(A,B){this.instrumentationScope=A,this._sharedState=B}emit(A){let B=A.context||rz6.context.active(),Q=new oz6.LogRecord(this._sharedState,this.instrumentationScope,{context:B,...A});this._sharedState.activeProcessor.onEmit(Q,B),Q._makeReadonly()}}mt2.Logger=ut2});
var fJ0=E((ht2)=>{Object.defineProperty(ht2,"__esModule",{value:!0});ht2.LogRecord=void 0;var sz6=ZQ(),Et=ZQ(),kP1=y3();class ft2{hrTime;hrTimeObserved;spanContext;resource;instrumentationScope;attributes={};_severityText;_severityNumber;_body;totalAttributesCount=0;_isReadonly=!1;_logRecordLimits;set severityText(A){if(this._isLogRecordReadonly())return;this._severityText=A}get severityText(){return this._severityText}set severityNumber(A){if(this._isLogRecordReadonly())return;this._severityNumber=A}get severityNumber(){return this._severityNumber}set body(A){if(this._isLogRecordReadonly())return;this._body=A}get body(){return this._body}get droppedAttributesCount(){return this.totalAttributesCount-Object.keys(this.attributes).length}constructor(A,B,Q){let{timestamp:D,observedTimestamp:Z,severityNumber:G,severityText:F,body:I,attributes:Y={},context:W}=Q,J=Date.now();if(this.hrTime=kP1.timeInputToHrTime(D??J),this.hrTimeObserved=kP1.timeInputToHrTime(Z??J),W){let X=Et.trace.getSpanContext(W);if(X&&Et.isSpanContextValid(X))this.spanContext=X}this.severityNumber=G,this.severityText=F,this.body=I,this.resource=A.resource,this.instrumentationScope=B,this._logRecordLimits=A.logRecordLimits,this.setAttributes(Y)}setAttribute(A,B){if(this._isLogRecordReadonly())return this;if(B===null)return this;if(A.length===0)return Et.diag.warn(`Invalid attribute key: ${A}`),this;if(!kP1.isAttributeValue(B)&&!(typeof B==="object"&&!Array.isArray(B)&&Object.keys(B).length>0))return Et.diag.warn(`Invalid attribute value set for key: ${A}`),this;if(this.totalAttributesCount+=1,Object.keys(this.attributes).length>=this._logRecordLimits.attributeCountLimit&&!Object.prototype.hasOwnProperty.call(this.attributes,A)){if(this.droppedAttributesCount===1)Et.diag.warn("Dropping extra attributes.");return this}if(kP1.isAttributeValue(B))this.attributes[A]=this._truncateToSize(B);else this.attributes[A]=B;return this}setAttributes(A){for(let[B,Q]of Object.entries(A))this.setAttribute(B,Q);return this}setBody(A){return this.body=A,this}setSeverityNumber(A){return this.severityNumber=A,this}setSeverityText(A){return this.severityText=A,this}_makeReadonly(){this._isReadonly=!0}_truncateToSize(A){let B=this._logRecordLimits.attributeValueLengthLimit;if(B<=0)return Et.diag.warn(`Attribute value limit must be positive, got ${B}`),A;if(typeof A==="string")return this._truncateToLimitUtil(A,B);if(Array.isArray(A))return A.map((Q)=>typeof Q==="string"?this._truncateToLimitUtil(Q,B):Q);return A}_truncateToLimitUtil(A,B){if(A.length<=B)return A;return A.substring(0,B)}_isLogRecordReadonly(){if(this._isReadonly)sz6.diag.warn("Can not execute the operation on emitted log record");return this._isReadonly}}ht2.LogRecord=ft2});
var hJ0=E((tt2)=>{Object.defineProperty(tt2,"__esModule",{value:!0});tt2.NoopLogRecordProcessor=void 0;class ot2{forceFlush(){return Promise.resolve()}onEmit(A,B){}shutdown(){return Promise.resolve()}}tt2.NoopLogRecordProcessor=ot2});
var iR2=E((pR2)=>{Object.defineProperty(pR2,"__esModule",{value:!0});pR2.SeverityNumber=void 0;var $a4;(function(A){A[A.UNSPECIFIED=0]="UNSPECIFIED",A[A.TRACE=1]="TRACE",A[A.TRACE2=2]="TRACE2",A[A.TRACE3=3]="TRACE3",A[A.TRACE4=4]="TRACE4",A[A.DEBUG=5]="DEBUG",A[A.DEBUG2=6]="DEBUG2",A[A.DEBUG3=7]="DEBUG3",A[A.DEBUG4=8]="DEBUG4",A[A.INFO=9]="INFO",A[A.INFO2=10]="INFO2",A[A.INFO3=11]="INFO3",A[A.INFO4=12]="INFO4",A[A.WARN=13]="WARN",A[A.WARN2=14]="WARN2",A[A.WARN3=15]="WARN3",A[A.WARN4=16]="WARN4",A[A.ERROR=17]="ERROR",A[A.ERROR2=18]="ERROR2",A[A.ERROR3=19]="ERROR3",A[A.ERROR4=20]="ERROR4",A[A.FATAL=21]="FATAL",A[A.FATAL2=22]="FATAL2",A[A.FATAL3=23]="FATAL3",A[A.FATAL4=24]="FATAL4"})($a4=pR2.SeverityNumber||(pR2.SeverityNumber={}))});
var it2=E((lt2)=>{Object.defineProperty(lt2,"__esModule",{value:!0});lt2.reconfigureLimits=lt2.loadDefaultConfig=void 0;var Ut=y3();function tz6(){return{forceFlushTimeoutMillis:30000,logRecordLimits:{attributeValueLengthLimit:Ut.getNumberFromEnv("OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT")??1/0,attributeCountLimit:Ut.getNumberFromEnv("OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT")??128},includeTraceContext:!0}}lt2.loadDefaultConfig=tz6;function ez6(A){return{attributeCountLimit:A.attributeCountLimit??Ut.getNumberFromEnv("OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT")??Ut.getNumberFromEnv("OTEL_ATTRIBUTE_COUNT_LIMIT")??128,attributeValueLengthLimit:A.attributeValueLengthLimit??Ut.getNumberFromEnv("OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT")??Ut.getNumberFromEnv("OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT")??1/0}}lt2.reconfigureLimits=ez6});
var ke2=E((Se2)=>{Object.defineProperty(Se2,"__esModule",{value:!0});Se2.BatchLogRecordProcessor=void 0;var XE6=Te2();class Pe2 extends XE6.BatchLogRecordProcessorBase{onShutdown(){}}Se2.BatchLogRecordProcessor=Pe2});
var rt2=E((at2)=>{Object.defineProperty(at2,"__esModule",{value:!0});at2.MultiLogRecordProcessor=void 0;var BE6=y3();class nt2{processors;forceFlushTimeoutMillis;constructor(A,B){this.processors=A,this.forceFlushTimeoutMillis=B}async forceFlush(){let A=this.forceFlushTimeoutMillis;await Promise.all(this.processors.map((B)=>BE6.callWithTimeout(B.forceFlush(),A)))}onEmit(A,B){this.processors.forEach((Q)=>Q.onEmit(A,B))}async shutdown(){await Promise.all(this.processors.map((A)=>A.shutdown()))}}at2.MultiLogRecordProcessor=nt2});
var xe2=E((EP)=>{Object.defineProperty(EP,"__esModule",{value:!0});EP.BatchLogRecordProcessor=EP.InMemoryLogRecordExporter=EP.SimpleLogRecordProcessor=EP.ConsoleLogRecordExporter=EP.NoopLogRecordProcessor=EP.LogRecord=EP.LoggerProvider=void 0;var zE6=Je2();Object.defineProperty(EP,"LoggerProvider",{enumerable:!0,get:function(){return zE6.LoggerProvider}});var EE6=fJ0();Object.defineProperty(EP,"LogRecord",{enumerable:!0,get:function(){return EE6.LogRecord}});var UE6=hJ0();Object.defineProperty(EP,"NoopLogRecordProcessor",{enumerable:!0,get:function(){return UE6.NoopLogRecordProcessor}});var wE6=Ke2();Object.defineProperty(EP,"ConsoleLogRecordExporter",{enumerable:!0,get:function(){return wE6.ConsoleLogRecordExporter}});var $E6=Ue2();Object.defineProperty(EP,"SimpleLogRecordProcessor",{enumerable:!0,get:function(){return $E6.SimpleLogRecordProcessor}});var qE6=Le2();Object.defineProperty(EP,"InMemoryLogRecordExporter",{enumerable:!0,get:function(){return qE6.InMemoryLogRecordExporter}});var NE6=_e2();Object.defineProperty(EP,"BatchLogRecordProcessor",{enumerable:!0,get:function(){return NE6.BatchLogRecordProcessor}})});
var ye2=E((gJ0)=>{Object.defineProperty(gJ0,"__esModule",{value:!0});gJ0.BatchLogRecordProcessor=void 0;var VE6=ke2();Object.defineProperty(gJ0,"BatchLogRecordProcessor",{enumerable:!0,get:function(){return VE6.BatchLogRecordProcessor}})});
var zO1=E((sR2)=>{Object.defineProperty(sR2,"__esModule",{value:!0});sR2.NOOP_LOGGER_PROVIDER=sR2.NoopLoggerProvider=void 0;var Na4=HO1();class DF0{getLogger(A,B,Q){return new Na4.NoopLogger}}sR2.NoopLoggerProvider=DF0;sR2.NOOP_LOGGER_PROVIDER=new DF0});

module.exports = xe2;
