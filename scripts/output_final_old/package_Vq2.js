// Package extracted with entry point: Vq2

var Vq2=E((lw5,Xq2)=>{var Jq2=J1("child_process"),Iq2=Jq2.spawn,Cl4=Jq2.exec;Xq2.exports=function(A,B,Q){if(typeof B==="function"&&Q===void 0)Q=B,B=void 0;if(A=parseInt(A),Number.isNaN(A))if(Q)return Q(new Error("pid must be a number"));else throw new Error("pid must be a number");var D={},Z={};switch(D[A]=[],Z[A]=1,process.platform){case"win32":Cl4("taskkill /pid "+A+" /T /F",Q);break;case"darwin":uZ0(A,D,Z,function(G){return Iq2("pgrep",["-P",G])},function(){Yq2(D,B,Q)});break;default:uZ0(A,D,Z,function(G){return Iq2("ps",["-o","pid","--no-headers","--ppid",G])},function(){Yq2(D,B,Q)});break}};function Yq2(A,B,Q){var D={};try{Object.keys(A).forEach(function(Z){if(A[Z].forEach(function(G){if(!D[G])Wq2(G,B),D[G]=1}),!D[Z])Wq2(Z,B),D[Z]=1})}catch(Z){if(Q)return Q(Z);else throw Z}if(Q)return Q()}function Wq2(A,B){try{process.kill(parseInt(A,10),B)}catch(Q){if(Q.code!=="ESRCH")throw Q}}function uZ0(A,B,Q,D,Z){var G=D(A),F="";G.stdout.on("data",function(W){var W=W.toString("ascii");F+=W});var I=function(Y){if(delete Q[A],Y!=0){if(Object.keys(Q).length==0)Z();return}F.match(/\d+/g).forEach(function(W){W=parseInt(W,10),B[A].push(W),B[W]=[],Q[W]=1,uZ0(W,B,Q,D,Z)})};G.on("close",I)}});

module.exports = Vq2;
