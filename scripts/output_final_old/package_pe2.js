// Package extracted with entry point: pe2

var ce2=E((mJ0)=>{Object.defineProperty(mJ0,"__esModule",{value:!0});mJ0.OTLPLogExporter=void 0;var TE6=de2();Object.defineProperty(mJ0,"OTLPLogExporter",{enumerable:!0,get:function(){return TE6.OTLPLogExporter}})});
var de2=E((ue2)=>{Object.defineProperty(ue2,"__esModule",{value:!0});ue2.OTLPLogExporter=void 0;var ME6=Tu(),RE6=ju(),he2=uo(),OE6=fe2();class ge2 extends ME6.OTLPExporterBase{constructor(A={}){super(he2.createOtlpHttpExportDelegate(he2.convertLegacyHttpOptions(A,"LOGS","v1/logs",{"User-Agent":`OTel-OTLP-Exporter-JavaScript/${OE6.VERSION}`,"Content-Type":"application/x-protobuf"}),RE6.ProtobufLogsSerializer))}}ue2.OTLPLogExporter=ge2});
var fe2=E((ve2)=>{Object.defineProperty(ve2,"__esModule",{value:!0});ve2.VERSION=void 0;ve2.VERSION="0.200.0"});
var le2=E((dJ0)=>{Object.defineProperty(dJ0,"__esModule",{value:!0});dJ0.OTLPLogExporter=void 0;var SE6=ce2();Object.defineProperty(dJ0,"OTLPLogExporter",{enumerable:!0,get:function(){return SE6.OTLPLogExporter}})});
var pe2=E((cJ0)=>{Object.defineProperty(cJ0,"__esModule",{value:!0});cJ0.OTLPLogExporter=void 0;var kE6=le2();Object.defineProperty(cJ0,"OTLPLogExporter",{enumerable:!0,get:function(){return kE6.OTLPLogExporter}})});

module.exports = pe2;
