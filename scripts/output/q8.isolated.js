/**
 * Isolated bundle for 'q8'
 */

var jcB = Object.create;
var {
  getPrototypeOf: ycB,
  defineProperty: Ou1,
  getOwnPropertyNames: kcB
} = Object;
var _cB = Object.prototype.hasOwnProperty;
var F1 = (A, B, Q) => {
  Q = A != null ? jcB(ycB(A)) : {};
  let D = B || !A || !A.__esModule ? Ou1(Q, "default", {
    value: A,
    enumerable: !0
  }) : Q;
  for (let Z of kcB(A)) if (!_cB.call(D, Z)) Ou1(D, Z, {
    get: () => A[Z],
    enumerable: !0
  });
  return D;
};
const {
  Cy0,
  Dm1,
  Hy0,
  My0,
  Ny0,
  Py0,
  Sy0,
  Yy0,
  _y0,
  dy0,
  gy0,
  vy0,
  yy0
} = require("./Cy0_Dm1_Hy0_My0_Ny0_more.js");
const {
  wk0
} = require("./wk0.js");
const {
  $X1,
  $d1,
  $l,
  $l1,
  $m0,
  Ad1,
  Ag0,
  Al0,
  B21,
  BJ1,
  BV,
  Cc0,
  Cc1,
  Cd1,
  Cg0,
  Cl1,
  Cv0,
  D21,
  DJ1,
  Dc1,
  Dd1,
  Df0,
  EX1,
  Ec0,
  FJ1,
  Fc1,
  Fd1,
  Fl,
  Fm0,
  GX1,
  Gc1,
  Gl,
  Gl0,
  HJ1,
  HX1,
  Hf,
  Hl1,
  I21,
  IO,
  IX1,
  Ic1,
  Id1,
  If0,
  Ig0,
  Il,
  Iv0,
  Ix0,
  JJ1,
  Jg0,
  Jl,
  Jm0,
  Jv0,
  Jx0,
  K21,
  Kl1,
  L21,
  LJ1,
  LX1,
  Lx0,
  Mf0,
  Ml,
  N21,
  Nc0,
  Nd1,
  Nl,
  Nl0,
  Nm0,
  O21,
  Oc1,
  Og0,
  Pc1,
  Pd1,
  QJ1,
  QO,
  Qc0,
  Qg0,
  Ql1,
  Qv0,
  R21,
  Rd1,
  Rl1,
  Ru0,
  SQ,
  Sd1,
  Sf0,
  TX1,
  Tc1,
  Tf0,
  UX1,
  Uc1,
  Ud1,
  Ug0,
  Um0,
  Uv0,
  VX1,
  Vd1,
  Vh0,
  Vx0,
  WJ1,
  Wd1,
  Wu0,
  XJ1,
  XX1,
  Xc0,
  Xc1,
  Xd1,
  Xf0,
  YX1,
  Yd1,
  Yf,
  Yl0,
  ZV,
  ZX1,
  Zc1,
  Zd0,
  Zg0,
  Zm0,
  Zv0,
  _21,
  _W1,
  _c0,
  _l0,
  aJ1,
  ac0,
  bx0,
  cb0,
  cf0,
  cu0,
  dc0,
  dl0,
  eJ1,
  ec1,
  ed0,
  ed1,
  em0,
  eq,
  ff0,
  gH,
  gd0,
  gl0,
  gm1,
  hC,
  hH,
  hc0,
  hc1,
  hg0,
  hm1,
  i21,
  ic1,
  im0,
  ix0,
  jd1,
  kG,
  kW,
  kW1,
  kb0,
  kf0,
  ku0,
  l21,
  lk0,
  ll0,
  lm1,
  mH,
  mg0,
  mj,
  mx0,
  nc1,
  nd0,
  ng0,
  oU,
  pJ1,
  p_0,
  qf0,
  qh0,
  ql,
  r_0,
  rg0,
  rq,
  ru0,
  rx0,
  sf0,
  sm1,
  tc1,
  tg0,
  tm1,
  tx0,
  uc0,
  ud0,
  uu0,
  vg0,
  wA,
  wJ1,
  wf,
  wf0,
  wl1,
  wx0,
  xm1,
  yh0,
  zJ1,
  zd1,
  zf0,
  zg0,
  zh0,
  zl0,
  zv0
} = require("./$X1_$d1_$l_$l1_$m0_more.js");
const {
  $p0,
  AB1,
  Ai0,
  An0,
  Ci0,
  Ei0,
  En0,
  Fi0,
  Gn0,
  Hi0,
  Kn0,
  Li0,
  Mp0,
  Qi0,
  Ri0,
  Rp0,
  Ti0,
  Tp0,
  Vn0,
  Wi0,
  Xi0,
  Zi0,
  ai0,
  al1,
  bl1,
  cl1,
  cp0,
  dl1,
  fX1,
  fl1,
  gi0,
  gl1,
  hl1,
  ii0,
  il1,
  ji0,
  kp0,
  li0,
  ll1,
  ml1,
  np0,
  pl1,
  qi0,
  qn0,
  sp0,
  tp0,
  uX1,
  ul1,
  vl1,
  wi0,
  wn0,
  xi0,
  xp0
} = require("./$p0_AB1_Ai0_An0_Ci0_more.js");
const {
  fn0
} = require("./fn0.js");
const {
  Cp1,
  Da0,
  WB1,
  YB1,
  dn0,
  gn0,
  in0,
  rn0,
  tn0
} = require("./Cp1_Da0_WB1_YB1_dn0_more.js");
const {
  $B1,
  $V1,
  Bo0,
  Br0,
  By,
  Co0,
  Di1,
  Eo0,
  Fi1,
  Gi1,
  Go0,
  Ho0,
  JV1,
  Ki1,
  MV1,
  NV1,
  PV1,
  RB1,
  Sr0,
  UO,
  Vi1,
  WV1,
  Wi1,
  Xo0,
  Yi1,
  Yr0,
  _r0,
  ar0,
  as0,
  dr0,
  el,
  fW,
  gf,
  hr0,
  hs0,
  ir0,
  jV1,
  or0,
  qB1,
  qs0,
  rs0,
  sp1,
  tp1,
  ts0,
  uf,
  ur0,
  us0,
  vr0,
  wi1,
  yr0
} = require("./$B1_$V1_Bo0_Br0_By_more.js");
const {
  $1A,
  $C1,
  $a1,
  $s1,
  A1A,
  A91,
  AAA,
  ABA,
  Aa1,
  As1,
  B91,
  Ba1,
  Bs1,
  C2A,
  Ca1,
  Ce0,
  Cs1,
  Cw,
  D1A,
  D91,
  Da1,
  Dn1,
  Ds1,
  E6A,
  EC1,
  EY,
  Ea1,
  Es1,
  FB,
  Fa1,
  Fs1,
  G2A,
  GAA,
  Ga1,
  Gs1,
  Gy,
  HC1,
  HY,
  Ha1,
  Hs1,
  I2A,
  I4,
  IC1,
  Ia1,
  Is1,
  J1A,
  JC1,
  Ja1,
  Js1,
  Jy,
  KC1,
  Ka1,
  Ks1,
  L0A,
  L6A,
  LC1,
  LO,
  La1,
  Ln1,
  Lp,
  Ls1,
  M1A,
  M2A,
  MC1,
  MO,
  Ma1,
  Mn1,
  Ms1,
  N6A,
  NC1,
  NN,
  Na1,
  Nn1,
  Np,
  Ns1,
  O0A,
  OC1,
  OO,
  Oa1,
  On1,
  Os1,
  PAA,
  PC1,
  Q2A,
  Q3,
  Q91,
  QBA,
  Qa1,
  Qn1,
  Qs1,
  R1A,
  RAA,
  RC1,
  RO,
  Ra1,
  Rn1,
  S0A,
  S1A,
  SC1,
  Sa1,
  TC1,
  TO,
  Ta1,
  U9,
  UC1,
  Ua1,
  V1A,
  VC1,
  VV,
  Va1,
  Vn1,
  Vs1,
  Vy,
  WC1,
  Wa1,
  Ws1,
  X2A,
  XC1,
  XV,
  Xa1,
  Xn1,
  Xs1,
  Y1A,
  YAA,
  Ya1,
  Ys1,
  Yy,
  Z91,
  Za1,
  Zs1,
  _C1,
  _a1,
  _n1,
  _p,
  aa1,
  an1,
  b1A,
  bC1,
  ba1,
  bn1,
  cBA,
  ca1,
  cn1,
  cp,
  da1,
  dn1,
  dp,
  eB1,
  ea1,
  ei1,
  en1,
  ep,
  fC1,
  fa1,
  g1A,
  ga1,
  gn1,
  h0A,
  ha1,
  hn1,
  ia1,
  in1,
  jC1,
  ja1,
  jn1,
  jp,
  k1A,
  kC1,
  ka1,
  kn1,
  l2A,
  la1,
  ln1,
  m5,
  ma1,
  mp,
  na1,
  ne0,
  nn1,
  o2A,
  oB1,
  of,
  on1,
  op,
  pC,
  pa1,
  pn1,
  q6A,
  qC1,
  qa1,
  qn1,
  qs1,
  rB1,
  ra1,
  rn1,
  rp,
  sB1,
  sa1,
  sp,
  tB1,
  ta1,
  tf,
  ua1,
  ue0,
  un1,
  v2A,
  vC1,
  va1,
  vn1,
  wC1,
  wa1,
  wn1,
  ws1,
  xC1,
  xa1,
  xn1,
  y1A,
  yC1,
  ya1,
  yn1,
  z1A,
  z2A,
  zC1,
  zY,
  za1,
  zn1,
  zs1
} = require("./$1A_$C1_$a1_$s1_A1A_more.js");
const {
  $WA,
  Do1,
  FH1,
  KWA,
  MWA,
  NWA,
  SWA,
  UWA,
  VWA,
  WWA,
  qWA,
  yWA
} = require("./$WA_Do1_FH1_KWA_MWA_more.js");
const {
  $EA,
  $e1,
  $wA,
  ANA,
  AQ1,
  At1,
  BCA,
  BVA,
  Bn,
  BqA,
  C10,
  CB,
  Ce1,
  D00,
  D10,
  D3,
  De1,
  ECA,
  EEA,
  ELA,
  EOA,
  EVA,
  Ee1,
  Eo1,
  F00,
  FKA,
  GCA,
  GXA,
  Ge1,
  H10,
  HMA,
  HQ1,
  In,
  IqA,
  J10,
  J5,
  JD,
  JZ,
  Je1,
  JwA,
  K$A,
  K4,
  KLA,
  KOA,
  Ko1,
  LQ1,
  Lo1,
  M6,
  MEA,
  MJA,
  MNA,
  MPA,
  MQ1,
  Mh,
  Mo1,
  Mw,
  NEA,
  NLA,
  NOA,
  ONA,
  Oz1,
  PEA,
  POA,
  PQ1,
  PTA,
  PY,
  Pt1,
  Q00,
  Q9,
  R6,
  RQ1,
  RUA,
  RVA,
  S7,
  SCA,
  SNA,
  SXA,
  SqA,
  T$A,
  TF,
  TQ1,
  TzA,
  U$A,
  UXA,
  Ue1,
  Ve1,
  WCA,
  WI,
  WKA,
  X6,
  XVA,
  XXA,
  YD,
  YUA,
  Yn,
  Yz,
  Z10,
  ZqA,
  Zz,
  _CA,
  _EA,
  _Q,
  a$A,
  ay,
  b$A,
  bG,
  be1,
  c$A,
  cB,
  cXA,
  cwA,
  dMA,
  e10,
  e91,
  eHA,
  fMA,
  fNA,
  fwA,
  g4,
  gCA,
  gG,
  gQ1,
  hEA,
  hJA,
  hOA,
  hQ1,
  hTA,
  iKA,
  iTA,
  iUA,
  iXA,
  ie1,
  ii,
  izA,
  j3,
  jEA,
  jRA,
  jSA,
  k3,
  kXA,
  kqA,
  lJA,
  le1,
  mG,
  mPA,
  o$A,
  oXA,
  py,
  qVA,
  qh,
  ry,
  sHA,
  sJ,
  sMA,
  sVA,
  sXA,
  sZ,
  szA,
  t91,
  tUA,
  ty,
  u$A,
  u4,
  uEA,
  uG,
  uN,
  ut1,
  vCA,
  vLA,
  ve1,
  wPA,
  xMA,
  xNA,
  xPA,
  xXA,
  yOA,
  yPA,
  yQ1,
  zPA
} = require("./$EA_$e1_$wA_ANA_AQ1_more.js");
const {
  $41,
  $Q0,
  $_A,
  $vA,
  AfA,
  AgA,
  AiA,
  BA0,
  BB0,
  BnA,
  CQ0,
  CbA,
  D20,
  DkA,
  DlA,
  DmA,
  DxA,
  EV,
  EaA,
  ElA,
  EvA,
  F41,
  FjA,
  FlA,
  FmA,
  FxA,
  GB0,
  GfA,
  GuA,
  Hk,
  I41,
  IgA,
  Iw1,
  JQ0,
  K20,
  K41,
  KaA,
  KfA,
  N41,
  N_A,
  NkA,
  NmA,
  NpA,
  NuA,
  OdA,
  OlA,
  Ow1,
  Oz,
  PiA,
  Q20,
  QB0,
  QaA,
  Qw1,
  R_A,
  RaA,
  RuA,
  RvA,
  RyA,
  ShA,
  Sw,
  Sw1,
  SyA,
  UQ0,
  UV,
  UxA,
  V20,
  V6,
  VI,
  W20,
  W90,
  W_A,
  WcA,
  XfA,
  XhA,
  XlA,
  XmA,
  XxA,
  Y90,
  YK,
  YnA,
  ZQ0,
  Zw1,
  __A,
  _aA,
  _lA,
  _uA,
  _w1,
  aA0,
  akA,
  amA,
  an,
  aw1,
  cA0,
  cQ1,
  cnA,
  dA0,
  ddA,
  dpA,
  eQ1,
  eU1,
  gpA,
  haA,
  iA0,
  iB0,
  ihA,
  j41,
  j_A,
  jaA,
  kvA,
  lQ1,
  laA,
  maA,
  mh,
  mn,
  naA,
  niA,
  npA,
  o90,
  oQ1,
  oU1,
  obA,
  pQ1,
  pdA,
  q41,
  qB0,
  qQ0,
  qfA,
  qhA,
  riA,
  shA,
  t20,
  th,
  tmA,
  txA,
  uh,
  vaA,
  w8,
  wA0,
  wB0,
  wkA,
  wuA,
  yw1
} = require("./$41_$Q0_$_A_$vA_AfA_more.js");
const {
  $42,
  $V,
  $eA,
  A12,
  AB2,
  B61,
  BA2,
  C6,
  CI,
  CeA,
  E40,
  FA2,
  GeA,
  GoA,
  H40,
  HA2,
  IB2,
  Jg,
  LQ0,
  MoA,
  MtA,
  N12,
  N22,
  N60,
  NA2,
  NtA,
  O40,
  O60,
  Q61,
  R40,
  R60,
  S60,
  T12,
  TrA,
  TtA,
  V22,
  V40,
  VA2,
  X40,
  XtA,
  YeA,
  _w,
  a41,
  b40,
  g41,
  h40,
  h41,
  i22,
  i41,
  j22,
  k40,
  kk,
  ktA,
  mQ0,
  ma,
  na,
  qoA,
  roA,
  s22,
  t02,
  u41,
  vtA,
  w12,
  x12,
  x40,
  yrA
} = require("./$42_$V_$eA_A12_AB2_more.js");
const {
  Aq2,
  t$2,
  w1
} = require("./Aq2_t$2_w1.js");
const {
  uq2
} = require("./uq2.js");
const {
  lq2
} = require("./lq2.js");
const {
  QN2
} = require("./QN2.js");
const {
  $L2,
  UL2
} = require("./$L2_UL2.js");
const {
  bL2,
  xL2
} = require("./bL2_xL2.js");
const {
  fL2,
  hL2
} = require("./fL2_hL2.js");
const {
  BM2,
  GM2
} = require("./BM2_GM2.js");
const {
  AE0,
  AI2,
  AVB,
  AZ1,
  B82,
  BJ2,
  BW2,
  BZ2,
  C61,
  C70,
  CXB,
  DKB,
  DN1,
  DVB,
  E30,
  E82,
  EVB,
  F70,
  FI2,
  FVB,
  G30,
  G70,
  H61,
  HCB,
  I62,
  J32,
  J62,
  K30,
  K32,
  K61,
  KI,
  KXB,
  M80,
  MF2,
  MJB,
  N50,
  N61,
  N72,
  N80,
  OZ2,
  PVB,
  Q30,
  Q70,
  R62,
  Rs,
  S61,
  T61,
  TF2,
  U52,
  UCB,
  UG2,
  UJB,
  UZ2,
  V52,
  V61,
  VD2,
  VVB,
  W70,
  WVB,
  X82,
  XD2,
  XZ,
  Y30,
  Z30,
  _JB,
  _Y2,
  aY2,
  az0,
  b82,
  bJB,
  c80,
  d32,
  d50,
  d82,
  dz0,
  ek1,
  fCB,
  g80,
  gVB,
  hY2,
  iF2,
  iI2,
  jY2,
  kZ2,
  l62,
  lZ2,
  m61,
  n52,
  n62,
  oCB,
  pCB,
  pW2,
  pXB,
  pY2,
  pz0,
  qCB,
  qZ2,
  r62,
  sD2,
  sF2,
  sXB,
  t32,
  tD2,
  tz0,
  u61,
  uCB,
  uN1,
  w30,
  w50,
  ws,
  xHB,
  xVB,
  zL
} = require("./AE0_AI2_AVB_AZ1_B82_more.js");
const {
  $$,
  $EB,
  $zB,
  BwB,
  CZ1,
  EJ2,
  EUB,
  F$B,
  FU0,
  FUB,
  Fw0,
  GU0,
  H70,
  HEB,
  HZ1,
  IEB,
  Iw0,
  KUB,
  LU0,
  Lw0,
  LzB,
  NEB,
  NU0,
  Nw0,
  OU0,
  Ow0,
  PzB,
  QqB,
  RU0,
  Rm,
  RqB,
  TUB,
  Tx1,
  UzB,
  VUB,
  WU0,
  Ww0,
  XJ2,
  XqB,
  Xw0,
  YEB,
  Yw0,
  Zw0,
  _U0,
  _UB,
  aUB,
  bEB,
  bU0,
  cU0,
  d_1,
  dqB,
  eU0,
  fEB,
  gqB,
  hM,
  hw0,
  iEB,
  iU0,
  i_1,
  iqB,
  lE0,
  lU0,
  lUB,
  l_1,
  m$B,
  ne,
  oE0,
  oU0,
  p$B,
  pU0,
  p_1,
  qzB,
  rU0,
  rwB,
  rx,
  se,
  tx,
  uEB,
  uU0,
  uUB,
  vqB,
  wEB,
  xEB,
  xUB,
  xw0,
  xwB,
  yqB,
  z$B,
  zJ2
} = require("./$$_$EB_$zB_BwB_CZ1_more.js");
import { cwd as Nj0 } from "process";
import { randomUUID as Lj0 } from "crypto";
var vcB = typeof global == "object" && global && global.Object === Object && global,
  HY1 = vcB;
var bcB = typeof self == "object" && self && self.Object === Object && self,
  fcB = HY1 || bcB || Function("return this")(),
  yG = fcB;
var hcB = yG.Symbol,
  AI = hcB;
var ET0 = Object.prototype,
  gcB = ET0.hasOwnProperty,
  ucB = ET0.toString,
  uA1 = AI ? AI.toStringTag : void 0;
function mcB(A) {
  var B = gcB.call(A, uA1),
    Q = A[uA1];
  try {
    A[uA1] = void 0;
    var D = !0;
  } catch (G) {}
  var Z = ucB.call(A);
  if (D) if (B) A[uA1] = Q;else delete A[uA1];
  return Z;
}
var UT0 = mcB;
var dcB = Object.prototype,
  ccB = dcB.toString;
function lcB(A) {
  return ccB.call(A);
}
var wT0 = lcB;
var pcB = "[object Null]",
  icB = "[object Undefined]",
  $T0 = AI ? AI.toStringTag : void 0;
function ncB(A) {
  if (A == null) return A === void 0 ? icB : pcB;
  return $T0 && $T0 in Object(A) ? UT0(A) : wT0(A);
}
var bC = ncB;
function acB(A) {
  return A != null && typeof A == "object";
}
var MF = acB;
var scB = "[object Symbol]";
function rcB(A) {
  return typeof A == "symbol" || MF(A) && bC(A) == scB;
}
var Rj = rcB;
function ocB(A, B) {
  var Q = -1,
    D = A == null ? 0 : A.length,
    Z = Array(D);
  while (++Q < D) Z[Q] = B(A[Q], Q, A);
  return Z;
}
var Rc = ocB;
var tcB = Array.isArray,
  x8 = tcB;
var ecB = 1 / 0,
  qT0 = AI ? AI.prototype : void 0,
  NT0 = qT0 ? qT0.toString : void 0;
function LT0(A) {
  if (typeof A == "string") return A;
  if (x8(A)) return Rc(A, LT0) + "";
  if (Rj(A)) return NT0 ? NT0.call(A) : "";
  var B = A + "";
  return B == "0" && 1 / A == -ecB ? "-0" : B;
}
var MT0 = LT0;
function ZlB(A) {
  var B = typeof A;
  return A != null && (B == "object" || B == "function");
}
var T3 = ZlB;
function ClB(A) {
  return A;
}
var Oc = ClB;
var KlB = "[object AsyncFunction]",
  HlB = "[object Function]",
  zlB = "[object GeneratorFunction]",
  ElB = "[object Proxy]";
function UlB(A) {
  if (!T3(A)) return !1;
  var B = bC(A);
  return B == HlB || B == zlB || B == KlB || B == ElB;
}
var Tc = UlB;
var wlB = yG["__core-js_shared__"],
  zY1 = wlB;
var kT0 = function () {
  var A = /[^.]+$/.exec(zY1 && zY1.keys && zY1.keys.IE_PROTO || "");
  return A ? "Symbol(src)_1." + A : "";
}();
function $lB(A) {
  return !!kT0 && kT0 in A;
}
var _T0 = $lB;
var qlB = Function.prototype,
  NlB = qlB.toString;
function LlB(A) {
  if (A != null) {
    try {
      return NlB.call(A);
    } catch (B) {}
    try {
      return A + "";
    } catch (B) {}
  }
  return "";
}
var tR = LlB;
var MlB = /[\\^$.*+?()[\]{}|]/g,
  RlB = /^\[object .+?Constructor\]$/,
  OlB = Function.prototype,
  TlB = Object.prototype,
  PlB = OlB.toString,
  SlB = TlB.hasOwnProperty,
  jlB = RegExp("^" + PlB.call(SlB).replace(MlB, "\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g, "$1.*?") + "$");
function ylB(A) {
  if (!T3(A) || _T0(A)) return !1;
  var B = Tc(A) ? jlB : RlB;
  return B.test(tR(A));
}
var xT0 = ylB;
function klB(A, B) {
  return A == null ? void 0 : A[B];
}
var vT0 = klB;
function _lB(A, B) {
  var Q = vT0(A, B);
  return xT0(Q) ? Q : void 0;
}
var AV = _lB;
var xlB = AV(yG, "WeakMap"),
  EY1 = xlB;
var bT0 = Object.create,
  vlB = function () {
    function A() {}
    return function (B) {
      if (!T3(B)) return {};
      if (bT0) return bT0(B);
      A.prototype = B;
      var Q = new A();
      return A.prototype = void 0, Q;
    };
  }(),
  fT0 = vlB;
function blB(A, B, Q) {
  switch (Q.length) {
    case 0:
      return A.call(B);
    case 1:
      return A.call(B, Q[0]);
    case 2:
      return A.call(B, Q[0], Q[1]);
    case 3:
      return A.call(B, Q[0], Q[1], Q[2]);
  }
  return A.apply(B, Q);
}
var hT0 = blB;
function hlB(A, B) {
  var Q = -1,
    D = A.length;
  B || (B = Array(D));
  while (++Q < D) B[Q] = A[Q];
  return B;
}
var Pc = hlB;
var glB = 800,
  ulB = 16,
  mlB = Date.now;
function dlB(A) {
  var B = 0,
    Q = 0;
  return function () {
    var D = mlB(),
      Z = ulB - (D - Q);
    if (Q = D, Z > 0) {
      if (++B >= glB) return arguments[0];
    } else B = 0;
    return A.apply(void 0, arguments);
  };
}
var uT0 = dlB;
function clB(A) {
  return function () {
    return A;
  };
}
var mT0 = clB;
var llB = function () {
    try {
      var A = AV(Object, "defineProperty");
      return A({}, "", {}), A;
    } catch (B) {}
  }(),
  Sc = llB;
var plB = !Sc ? Oc : function (A, B) {
    return Sc(A, "toString", {
      configurable: !0,
      enumerable: !1,
      value: mT0(B),
      writable: !0
    });
  },
  dT0 = plB;
var ilB = uT0(dT0),
  UY1 = ilB;
function nlB(A, B) {
  var Q = -1,
    D = A == null ? 0 : A.length;
  while (++Q < D) if (B(A[Q], Q, A) === !1) break;
  return A;
}
var cT0 = nlB;
var elB = 9007199254740991,
  ApB = /^(?:0|[1-9]\d*)$/;
function BpB(A, B) {
  var Q = typeof A;
  return B = B == null ? elB : B, !!B && (Q == "number" || Q != "symbol" && ApB.test(A)) && A > -1 && A % 1 == 0 && A < B;
}
var Oj = BpB;
function QpB(A, B, Q) {
  if (B == "__proto__" && Sc) Sc(A, B, {
    configurable: !0,
    enumerable: !0,
    value: Q,
    writable: !0
  });else A[B] = Q;
}
var Tj = QpB;
function DpB(A, B) {
  return A === B || A !== A && B !== B;
}
var mq = DpB;
var ZpB = Object.prototype,
  GpB = ZpB.hasOwnProperty;
function FpB(A, B, Q) {
  var D = A[B];
  if (!(GpB.call(A, B) && mq(D, Q)) || Q === void 0 && !(B in A)) Tj(A, B, Q);
}
var Pj = FpB;
function IpB(A, B, Q, D) {
  var Z = !Q;
  Q || (Q = {});
  var G = -1,
    F = B.length;
  while (++G < F) {
    var I = B[G],
      Y = D ? D(Q[I], A[I], I, Q, A) : void 0;
    if (Y === void 0) Y = A[I];
    if (Z) Tj(Q, I, Y);else Pj(Q, I, Y);
  }
  return Q;
}
var xH = IpB;
var sT0 = Math.max;
function YpB(A, B, Q) {
  return B = sT0(B === void 0 ? A.length - 1 : B, 0), function () {
    var D = arguments,
      Z = -1,
      G = sT0(D.length - B, 0),
      F = Array(G);
    while (++Z < G) F[Z] = D[B + Z];
    Z = -1;
    var I = Array(B + 1);
    while (++Z < B) I[Z] = D[Z];
    return I[B] = Q(F), hT0(A, this, I);
  };
}
var wY1 = YpB;
function WpB(A, B) {
  return UY1(wY1(A, B, Oc), A + "");
}
var rT0 = WpB;
var JpB = 9007199254740991;
function XpB(A) {
  return typeof A == "number" && A > -1 && A % 1 == 0 && A <= JpB;
}
var jc = XpB;
function VpB(A) {
  return A != null && jc(A.length) && !Tc(A);
}
var dq = VpB;
function CpB(A, B, Q) {
  if (!T3(Q)) return !1;
  var D = typeof B;
  if (D == "number" ? dq(Q) && Oj(B, Q.length) : D == "string" && B in Q) return mq(Q[B], A);
  return !1;
}
var $Y1 = CpB;
function KpB(A) {
  return rT0(function (B, Q) {
    var D = -1,
      Z = Q.length,
      G = Z > 1 ? Q[Z - 1] : void 0,
      F = Z > 2 ? Q[2] : void 0;
    if (G = A.length > 3 && typeof G == "function" ? (Z--, G) : void 0, F && $Y1(Q[0], Q[1], F)) G = Z < 3 ? void 0 : G, Z = 1;
    B = Object(B);
    while (++D < Z) {
      var I = Q[D];
      if (I) A(B, I, D, G);
    }
    return B;
  });
}
var oT0 = KpB;
var HpB = Object.prototype;
function zpB(A) {
  var B = A && A.constructor,
    Q = typeof B == "function" && B.prototype || HpB;
  return A === Q;
}
var yc = zpB;
function EpB(A, B) {
  var Q = -1,
    D = Array(A);
  while (++Q < A) D[Q] = B(Q);
  return D;
}
var tT0 = EpB;
var UpB = "[object Arguments]";
function wpB(A) {
  return MF(A) && bC(A) == UpB;
}
var Tu1 = wpB;
var eT0 = Object.prototype,
  $pB = eT0.hasOwnProperty,
  qpB = eT0.propertyIsEnumerable,
  NpB = Tu1(function () {
    return arguments;
  }()) ? Tu1 : function (A) {
    return MF(A) && $pB.call(A, "callee") && !qpB.call(A, "callee");
  },
  eR = NpB;
var NY1 = {};
function LpB() {
  return !1;
}
var AP0 = LpB;
var DP0 = typeof NY1 == "object" && NY1 && !NY1.nodeType && NY1,
  BP0 = DP0 && typeof qY1 == "object" && qY1 && !qY1.nodeType && qY1,
  MpB = BP0 && BP0.exports === DP0,
  QP0 = MpB ? yG.Buffer : void 0,
  RpB = QP0 ? QP0.isBuffer : void 0,
  OpB = RpB || AP0,
  cq = OpB;
var TpB = "[object Arguments]",
  PpB = "[object Array]",
  SpB = "[object Boolean]",
  jpB = "[object Date]",
  ypB = "[object Error]",
  kpB = "[object Function]",
  _pB = "[object Map]",
  xpB = "[object Number]",
  vpB = "[object Object]",
  bpB = "[object RegExp]",
  fpB = "[object Set]",
  hpB = "[object String]",
  gpB = "[object WeakMap]",
  upB = "[object ArrayBuffer]",
  mpB = "[object DataView]",
  dpB = "[object Float32Array]",
  cpB = "[object Float64Array]",
  lpB = "[object Int8Array]",
  ppB = "[object Int16Array]",
  ipB = "[object Int32Array]",
  npB = "[object Uint8Array]",
  apB = "[object Uint8ClampedArray]",
  spB = "[object Uint16Array]",
  rpB = "[object Uint32Array]",
  O7 = {};
O7[dpB] = O7[cpB] = O7[lpB] = O7[ppB] = O7[ipB] = O7[npB] = O7[apB] = O7[spB] = O7[rpB] = !0;
O7[TpB] = O7[PpB] = O7[upB] = O7[SpB] = O7[mpB] = O7[jpB] = O7[ypB] = O7[kpB] = O7[_pB] = O7[xpB] = O7[vpB] = O7[bpB] = O7[fpB] = O7[hpB] = O7[gpB] = !1;
function opB(A) {
  return MF(A) && jc(A.length) && !!O7[bC(A)];
}
var ZP0 = opB;
function tpB(A) {
  return function (B) {
    return A(B);
  };
}
var kc = tpB;
var MY1 = {};
var GP0 = typeof MY1 == "object" && MY1 && !MY1.nodeType && MY1,
  mA1 = GP0 && typeof LY1 == "object" && LY1 && !LY1.nodeType && LY1,
  epB = mA1 && mA1.exports === GP0,
  Pu1 = epB && HY1.process,
  AiB = function () {
    try {
      var A = mA1 && mA1.require && mA1.require("util").types;
      if (A) return A;
      return Pu1 && Pu1.binding && Pu1.binding("util");
    } catch (B) {}
  }(),
  lq = AiB;
var FP0 = lq && lq.isTypedArray,
  BiB = FP0 ? kc(FP0) : ZP0,
  _c = BiB;
var QiB = Object.prototype,
  DiB = QiB.hasOwnProperty;
function ZiB(A, B) {
  var Q = x8(A),
    D = !Q && eR(A),
    Z = !Q && !D && cq(A),
    G = !Q && !D && !Z && _c(A),
    F = Q || D || Z || G,
    I = F ? tT0(A.length, String) : [],
    Y = I.length;
  for (var W in A) if ((B || DiB.call(A, W)) && !(F && (W == "length" || Z && (W == "offset" || W == "parent") || G && (W == "buffer" || W == "byteLength" || W == "byteOffset") || Oj(W, Y)))) I.push(W);
  return I;
}
var RY1 = ZiB;
function GiB(A, B) {
  return function (Q) {
    return A(B(Q));
  };
}
var OY1 = GiB;
var FiB = OY1(Object.keys, Object),
  IP0 = FiB;
var IiB = Object.prototype,
  YiB = IiB.hasOwnProperty;
function WiB(A) {
  if (!yc(A)) return IP0(A);
  var B = [];
  for (var Q in Object(A)) if (YiB.call(A, Q) && Q != "constructor") B.push(Q);
  return B;
}
var YP0 = WiB;
function JiB(A) {
  return dq(A) ? RY1(A) : YP0(A);
}
var vH = JiB;
function XiB(A) {
  var B = [];
  if (A != null) for (var Q in Object(A)) B.push(Q);
  return B;
}
var WP0 = XiB;
var ViB = Object.prototype,
  CiB = ViB.hasOwnProperty;
function KiB(A) {
  if (!T3(A)) return WP0(A);
  var B = yc(A),
    Q = [];
  for (var D in A) if (!(D == "constructor" && (B || !CiB.call(A, D)))) Q.push(D);
  return Q;
}
var JP0 = KiB;
function HiB(A) {
  return dq(A) ? RY1(A, !0) : JP0(A);
}
var pq = HiB;
var ziB = /\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,
  EiB = /^\w*$/;
function UiB(A, B) {
  if (x8(A)) return !1;
  var Q = typeof A;
  if (Q == "number" || Q == "symbol" || Q == "boolean" || A == null || Rj(A)) return !0;
  return EiB.test(A) || !ziB.test(A) || B != null && A in Object(B);
}
var xc = UiB;
function bc(A) {
  var B = -1,
    Q = A == null ? 0 : A.length;
  this.clear();
  while (++B < Q) {
    var D = A[B];
    this.set(D[0], D[1]);
  }
}
var jj = bc;
var giB = AV(yG, "Map"),
  yj = giB;
function fc(A) {
  var B = -1,
    Q = A == null ? 0 : A.length;
  this.clear();
  while (++B < Q) {
    var D = A[B];
    this.set(D[0], D[1]);
  }
}
var eb = fc;
var niB = "Expected a function";
function ju1(A, B) {
  if (typeof A != "function" || B != null && typeof B != "function") throw new TypeError(niB);
  var Q = function () {
    var D = arguments,
      Z = B ? B.apply(this, D) : D[0],
      G = Q.cache;
    if (G.has(Z)) return G.get(Z);
    var F = A.apply(this, D);
    return Q.cache = G.set(Z, F) || G, F;
  };
  return Q.cache = new (ju1.Cache || eb)(), Q;
}
ju1.Cache = eb;
var SA = ju1;
var aiB = 500;
function siB(A) {
  var B = SA(A, function (D) {
      if (Q.size === aiB) Q.clear();
      return D;
    }),
    Q = B.cache;
  return B;
}
var TP0 = siB;
var riB = /[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,
  oiB = /\\(\\)?/g,
  tiB = TP0(function (A) {
    var B = [];
    if (A.charCodeAt(0) === 46) B.push("");
    return A.replace(riB, function (Q, D, Z, G) {
      B.push(Z ? G.replace(oiB, "$1") : D || Q);
    }), B;
  }),
  PP0 = tiB;
function eiB(A) {
  return A == null ? "" : MT0(A);
}
var hc = eiB;
function AnB(A, B) {
  if (x8(A)) return A;
  return xc(A, B) ? [A] : PP0(hc(A));
}
var bH = AnB;
var BnB = 1 / 0;
function QnB(A) {
  if (typeof A == "string" || Rj(A)) return A;
  var B = A + "";
  return B == "0" && 1 / A == -BnB ? "-0" : B;
}
var fH = QnB;
function DnB(A, B) {
  B = bH(B, A);
  var Q = 0,
    D = B.length;
  while (A != null && Q < D) A = A[fH(B[Q++])];
  return Q && Q == D ? A : void 0;
}
var _j = DnB;
function ZnB(A, B, Q) {
  var D = A == null ? void 0 : _j(A, B);
  return D === void 0 ? Q : D;
}
var SP0 = ZnB;
function GnB(A, B) {
  var Q = -1,
    D = B.length,
    Z = A.length;
  while (++Q < D) A[Z + Q] = B[Q];
  return A;
}
var gc = GnB;
var WnB = OY1(Object.getPrototypeOf, Object),
  uc = WnB;
var JnB = "[object Object]",
  XnB = Function.prototype,
  VnB = Object.prototype,
  vP0 = XnB.toString,
  CnB = VnB.hasOwnProperty,
  KnB = vP0.call(Object);
function HnB(A) {
  if (!MF(A) || bC(A) != JnB) return !1;
  var B = uc(A);
  if (B === null) return !0;
  var Q = CnB.call(B, "constructor") && B.constructor;
  return typeof Q == "function" && Q instanceof Q && vP0.call(Q) == KnB;
}
var Af = HnB;
function mc(A) {
  var B = this.__data__ = new jj(A);
  this.size = B.size;
}
var iq = mc;
function tnB(A, B) {
  return A && xH(B, vH(B), A);
}
var eP0 = tnB;
function enB(A, B) {
  return A && xH(B, pq(B), A);
}
var AS0 = enB;
var kY1 = {};
var ZS0 = typeof kY1 == "object" && kY1 && !kY1.nodeType && kY1,
  BS0 = ZS0 && typeof yY1 == "object" && yY1 && !yY1.nodeType && yY1,
  AaB = BS0 && BS0.exports === ZS0,
  QS0 = AaB ? yG.Buffer : void 0,
  DS0 = QS0 ? QS0.allocUnsafe : void 0;
function BaB(A, B) {
  if (B) return A.slice();
  var Q = A.length,
    D = DS0 ? DS0(Q) : new A.constructor(Q);
  return A.copy(D), D;
}
var dA1 = BaB;
function QaB(A, B) {
  var Q = -1,
    D = A == null ? 0 : A.length,
    Z = 0,
    G = [];
  while (++Q < D) {
    var F = A[Q];
    if (B(F, Q, A)) G[Z++] = F;
  }
  return G;
}
var _Y1 = QaB;
function DaB() {
  return [];
}
var xY1 = DaB;
var ZaB = Object.prototype,
  GaB = ZaB.propertyIsEnumerable,
  GS0 = Object.getOwnPropertySymbols,
  FaB = !GS0 ? xY1 : function (A) {
    if (A == null) return [];
    return A = Object(A), _Y1(GS0(A), function (B) {
      return GaB.call(A, B);
    });
  },
  dc = FaB;
function IaB(A, B) {
  return xH(A, dc(A), B);
}
var FS0 = IaB;
var YaB = Object.getOwnPropertySymbols,
  WaB = !YaB ? xY1 : function (A) {
    var B = [];
    while (A) gc(B, dc(A)), A = uc(A);
    return B;
  },
  vY1 = WaB;
function JaB(A, B) {
  return xH(A, vY1(A), B);
}
var IS0 = JaB;
function XaB(A, B, Q) {
  var D = B(A);
  return x8(A) ? D : gc(D, Q(A));
}
var bY1 = XaB;
function VaB(A) {
  return bY1(A, vH, dc);
}
var cA1 = VaB;
function CaB(A) {
  return bY1(A, pq, vY1);
}
var fY1 = CaB;
var KaB = AV(yG, "DataView"),
  hY1 = KaB;
var HaB = AV(yG, "Promise"),
  gY1 = HaB;
var zaB = AV(yG, "Set"),
  xj = zaB;
var YS0 = "[object Map]",
  EaB = "[object Object]",
  WS0 = "[object Promise]",
  JS0 = "[object Set]",
  XS0 = "[object WeakMap]",
  VS0 = "[object DataView]",
  UaB = tR(hY1),
  waB = tR(yj),
  $aB = tR(gY1),
  qaB = tR(xj),
  NaB = tR(EY1),
  Bf = bC;
var BO = Bf;
var LaB = Object.prototype,
  MaB = LaB.hasOwnProperty;
function RaB(A) {
  var B = A.length,
    Q = new A.constructor(B);
  if (B && typeof A[0] == "string" && MaB.call(A, "index")) Q.index = A.index, Q.input = A.input;
  return Q;
}
var CS0 = RaB;
var OaB = yG.Uint8Array,
  cc = OaB;
function TaB(A) {
  var B = new A.constructor(A.byteLength);
  return new cc(B).set(new cc(A)), B;
}
var lc = TaB;
function PaB(A, B) {
  var Q = B ? lc(A.buffer) : A.buffer;
  return new A.constructor(Q, A.byteOffset, A.byteLength);
}
var KS0 = PaB;
var SaB = /\w*$/;
function jaB(A) {
  var B = new A.constructor(A.source, SaB.exec(A));
  return B.lastIndex = A.lastIndex, B;
}
var HS0 = jaB;
var zS0 = AI ? AI.prototype : void 0,
  ES0 = zS0 ? zS0.valueOf : void 0;
function yaB(A) {
  return ES0 ? Object(ES0.call(A)) : {};
}
var US0 = yaB;
function kaB(A, B) {
  var Q = B ? lc(A.buffer) : A.buffer;
  return new A.constructor(Q, A.byteOffset, A.length);
}
var uY1 = kaB;
var _aB = "[object Boolean]",
  xaB = "[object Date]",
  vaB = "[object Map]",
  baB = "[object Number]",
  faB = "[object RegExp]",
  haB = "[object Set]",
  gaB = "[object String]",
  uaB = "[object Symbol]",
  maB = "[object ArrayBuffer]",
  daB = "[object DataView]",
  caB = "[object Float32Array]",
  laB = "[object Float64Array]",
  paB = "[object Int8Array]",
  iaB = "[object Int16Array]",
  naB = "[object Int32Array]",
  aaB = "[object Uint8Array]",
  saB = "[object Uint8ClampedArray]",
  raB = "[object Uint16Array]",
  oaB = "[object Uint32Array]";
function taB(A, B, Q) {
  var D = A.constructor;
  switch (B) {
    case maB:
      return lc(A);
    case _aB:
    case xaB:
      return new D(+A);
    case daB:
      return KS0(A, Q);
    case caB:
    case laB:
    case paB:
    case iaB:
    case naB:
    case aaB:
    case saB:
    case raB:
    case oaB:
      return uY1(A, Q);
    case vaB:
      return new D();
    case baB:
    case gaB:
      return new D(A);
    case faB:
      return HS0(A);
    case haB:
      return new D();
    case uaB:
      return US0(A);
  }
}
var wS0 = taB;
function eaB(A) {
  return typeof A.constructor == "function" && !yc(A) ? fT0(uc(A)) : {};
}
var mY1 = eaB;
var AsB = "[object Map]";
function BsB(A) {
  return MF(A) && BO(A) == AsB;
}
var $S0 = BsB;
var qS0 = lq && lq.isMap,
  QsB = qS0 ? kc(qS0) : $S0,
  NS0 = QsB;
var DsB = "[object Set]";
function ZsB(A) {
  return MF(A) && BO(A) == DsB;
}
var LS0 = ZsB;
var MS0 = lq && lq.isSet,
  GsB = MS0 ? kc(MS0) : LS0,
  RS0 = GsB;
var FsB = 1,
  IsB = 2,
  YsB = 4,
  OS0 = "[object Arguments]",
  WsB = "[object Array]",
  JsB = "[object Boolean]",
  XsB = "[object Date]",
  VsB = "[object Error]",
  TS0 = "[object Function]",
  CsB = "[object GeneratorFunction]",
  KsB = "[object Map]",
  HsB = "[object Number]",
  PS0 = "[object Object]",
  zsB = "[object RegExp]",
  EsB = "[object Set]",
  UsB = "[object String]",
  wsB = "[object Symbol]",
  $sB = "[object WeakMap]",
  qsB = "[object ArrayBuffer]",
  NsB = "[object DataView]",
  LsB = "[object Float32Array]",
  MsB = "[object Float64Array]",
  RsB = "[object Int8Array]",
  OsB = "[object Int16Array]",
  TsB = "[object Int32Array]",
  PsB = "[object Uint8Array]",
  SsB = "[object Uint8ClampedArray]",
  jsB = "[object Uint16Array]",
  ysB = "[object Uint32Array]",
  o3 = {};
o3[OS0] = o3[WsB] = o3[qsB] = o3[NsB] = o3[JsB] = o3[XsB] = o3[LsB] = o3[MsB] = o3[RsB] = o3[OsB] = o3[TsB] = o3[KsB] = o3[HsB] = o3[PS0] = o3[zsB] = o3[EsB] = o3[UsB] = o3[wsB] = o3[PsB] = o3[SsB] = o3[jsB] = o3[ysB] = !0;
o3[VsB] = o3[TS0] = o3[$sB] = !1;
function dY1(A, B, Q, D, Z, G) {
  var F,
    I = B & FsB,
    Y = B & IsB,
    W = B & YsB;
  if (Q) F = Z ? Q(A, D, Z, G) : Q(A);
  if (F !== void 0) return F;
  if (!T3(A)) return A;
  var J = x8(A);
  if (J) {
    if (F = CS0(A), !I) return Pc(A, F);
  } else {
    var X = BO(A),
      V = X == TS0 || X == CsB;
    if (cq(A)) return dA1(A, I);
    if (X == PS0 || X == OS0 || V && !Z) {
      if (F = Y || V ? {} : mY1(A), !I) return Y ? IS0(A, AS0(F, A)) : FS0(A, eP0(F, A));
    } else {
      if (!o3[X]) return Z ? A : {};
      F = wS0(A, X, I);
    }
  }
  G || (G = new iq());
  var C = G.get(A);
  if (C) return C;
  if (G.set(A, F), RS0(A)) A.forEach(function (z) {
    F.add(dY1(z, B, Q, z, A, G));
  });else if (NS0(A)) A.forEach(function (z, $) {
    F.set($, dY1(z, B, Q, $, A, G));
  });
  var K = W ? Y ? fY1 : cA1 : Y ? pq : vH,
    H = J ? void 0 : K(A);
  return cT0(H || A, function (z, $) {
    if (H) $ = z, z = A[$];
    Pj(F, $, dY1(z, B, Q, $, A, G));
  }), F;
}
var cY1 = dY1;
var ksB = 1,
  _sB = 4;
function xsB(A) {
  return cY1(A, ksB | _sB);
}
var lA1 = xsB;
function lY1(A) {
  var B = -1,
    Q = A == null ? 0 : A.length;
  this.__data__ = new eb();
  while (++B < Q) this.add(A[B]);
}
var pY1 = lY1;
function hsB(A, B) {
  var Q = -1,
    D = A == null ? 0 : A.length;
  while (++Q < D) if (B(A[Q], Q, A)) return !0;
  return !1;
}
var yS0 = hsB;
function gsB(A, B) {
  return A.has(B);
}
var iY1 = gsB;
var usB = 1,
  msB = 2;
function dsB(A, B, Q, D, Z, G) {
  var F = Q & usB,
    I = A.length,
    Y = B.length;
  if (I != Y && !(F && Y > I)) return !1;
  var W = G.get(A),
    J = G.get(B);
  if (W && J) return W == B && J == A;
  var X = -1,
    V = !0,
    C = Q & msB ? new pY1() : void 0;
  G.set(A, B), G.set(B, A);
  while (++X < I) {
    var K = A[X],
      H = B[X];
    if (D) var z = F ? D(H, K, X, B, A, G) : D(K, H, X, A, B, G);
    if (z !== void 0) {
      if (z) continue;
      V = !1;
      break;
    }
    if (C) {
      if (!yS0(B, function ($, L) {
        if (!iY1(C, L) && (K === $ || Z(K, $, Q, D, G))) return C.push(L);
      })) {
        V = !1;
        break;
      }
    } else if (!(K === H || Z(K, H, Q, D, G))) {
      V = !1;
      break;
    }
  }
  return G.delete(A), G.delete(B), V;
}
var nY1 = dsB;
function csB(A) {
  var B = -1,
    Q = Array(A.size);
  return A.forEach(function (D, Z) {
    Q[++B] = [Z, D];
  }), Q;
}
var kS0 = csB;
function lsB(A) {
  var B = -1,
    Q = Array(A.size);
  return A.forEach(function (D) {
    Q[++B] = D;
  }), Q;
}
var pc = lsB;
var psB = 1,
  isB = 2,
  nsB = "[object Boolean]",
  asB = "[object Date]",
  ssB = "[object Error]",
  rsB = "[object Map]",
  osB = "[object Number]",
  tsB = "[object RegExp]",
  esB = "[object Set]",
  ArB = "[object String]",
  BrB = "[object Symbol]",
  QrB = "[object ArrayBuffer]",
  DrB = "[object DataView]",
  _S0 = AI ? AI.prototype : void 0,
  xu1 = _S0 ? _S0.valueOf : void 0;
function ZrB(A, B, Q, D, Z, G, F) {
  switch (Q) {
    case DrB:
      if (A.byteLength != B.byteLength || A.byteOffset != B.byteOffset) return !1;
      A = A.buffer, B = B.buffer;
    case QrB:
      if (A.byteLength != B.byteLength || !G(new cc(A), new cc(B))) return !1;
      return !0;
    case nsB:
    case asB:
    case osB:
      return mq(+A, +B);
    case ssB:
      return A.name == B.name && A.message == B.message;
    case tsB:
    case ArB:
      return A == B + "";
    case rsB:
      var I = kS0;
    case esB:
      var Y = D & psB;
      if (I || (I = pc), A.size != B.size && !Y) return !1;
      var W = F.get(A);
      if (W) return W == B;
      D |= isB, F.set(A, B);
      var J = nY1(I(A), I(B), D, Z, G, F);
      return F.delete(A), J;
    case BrB:
      if (xu1) return xu1.call(A) == xu1.call(B);
  }
  return !1;
}
var xS0 = ZrB;
var GrB = 1,
  FrB = Object.prototype,
  IrB = FrB.hasOwnProperty;
function YrB(A, B, Q, D, Z, G) {
  var F = Q & GrB,
    I = cA1(A),
    Y = I.length,
    W = cA1(B),
    J = W.length;
  if (Y != J && !F) return !1;
  var X = Y;
  while (X--) {
    var V = I[X];
    if (!(F ? V in B : IrB.call(B, V))) return !1;
  }
  var C = G.get(A),
    K = G.get(B);
  if (C && K) return C == B && K == A;
  var H = !0;
  G.set(A, B), G.set(B, A);
  var z = F;
  while (++X < Y) {
    V = I[X];
    var $ = A[V],
      L = B[V];
    if (D) var N = F ? D(L, $, V, B, A, G) : D($, L, V, A, B, G);
    if (!(N === void 0 ? $ === L || Z($, L, Q, D, G) : N)) {
      H = !1;
      break;
    }
    z || (z = V == "constructor");
  }
  if (H && !z) {
    var O = A.constructor,
      R = B.constructor;
    if (O != R && "constructor" in A && "constructor" in B && !(typeof O == "function" && O instanceof O && typeof R == "function" && R instanceof R)) H = !1;
  }
  return G.delete(A), G.delete(B), H;
}
var vS0 = YrB;
var WrB = 1,
  bS0 = "[object Arguments]",
  fS0 = "[object Array]",
  aY1 = "[object Object]",
  JrB = Object.prototype,
  hS0 = JrB.hasOwnProperty;
function XrB(A, B, Q, D, Z, G) {
  var F = x8(A),
    I = x8(B),
    Y = F ? fS0 : BO(A),
    W = I ? fS0 : BO(B);
  Y = Y == bS0 ? aY1 : Y, W = W == bS0 ? aY1 : W;
  var J = Y == aY1,
    X = W == aY1,
    V = Y == W;
  if (V && cq(A)) {
    if (!cq(B)) return !1;
    F = !0, J = !1;
  }
  if (V && !J) return G || (G = new iq()), F || _c(A) ? nY1(A, B, Q, D, Z, G) : xS0(A, B, Y, Q, D, Z, G);
  if (!(Q & WrB)) {
    var C = J && hS0.call(A, "__wrapped__"),
      K = X && hS0.call(B, "__wrapped__");
    if (C || K) {
      var H = C ? A.value() : A,
        z = K ? B.value() : B;
      return G || (G = new iq()), Z(H, z, Q, D, G);
    }
  }
  if (!V) return !1;
  return G || (G = new iq()), vS0(A, B, Q, D, Z, G);
}
var gS0 = XrB;
function uS0(A, B, Q, D, Z) {
  if (A === B) return !0;
  if (A == null || B == null || !MF(A) && !MF(B)) return A !== A && B !== B;
  return gS0(A, B, Q, D, uS0, Z);
}
var ic = uS0;
var VrB = 1,
  CrB = 2;
function KrB(A, B, Q, D) {
  var Z = Q.length,
    G = Z,
    F = !D;
  if (A == null) return !G;
  A = Object(A);
  while (Z--) {
    var I = Q[Z];
    if (F && I[2] ? I[1] !== A[I[0]] : !(I[0] in A)) return !1;
  }
  while (++Z < G) {
    I = Q[Z];
    var Y = I[0],
      W = A[Y],
      J = I[1];
    if (F && I[2]) {
      if (W === void 0 && !(Y in A)) return !1;
    } else {
      var X = new iq();
      if (D) var V = D(W, J, Y, A, B, X);
      if (!(V === void 0 ? ic(J, W, VrB | CrB, D, X) : V)) return !1;
    }
  }
  return !0;
}
var mS0 = KrB;
function HrB(A) {
  return A === A && !T3(A);
}
var sY1 = HrB;
function zrB(A) {
  var B = vH(A),
    Q = B.length;
  while (Q--) {
    var D = B[Q],
      Z = A[D];
    B[Q] = [D, Z, sY1(Z)];
  }
  return B;
}
var dS0 = zrB;
function ErB(A, B) {
  return function (Q) {
    if (Q == null) return !1;
    return Q[A] === B && (B !== void 0 || A in Object(Q));
  };
}
var rY1 = ErB;
function UrB(A) {
  var B = dS0(A);
  if (B.length == 1 && B[0][2]) return rY1(B[0][0], B[0][1]);
  return function (Q) {
    return Q === A || mS0(Q, A, B);
  };
}
var cS0 = UrB;
function wrB(A, B) {
  return A != null && B in Object(A);
}
var lS0 = wrB;
function $rB(A, B, Q) {
  B = bH(B, A);
  var D = -1,
    Z = B.length,
    G = !1;
  while (++D < Z) {
    var F = fH(B[D]);
    if (!(G = A != null && Q(A, F))) break;
    A = A[F];
  }
  if (G || ++D != Z) return G;
  return Z = A == null ? 0 : A.length, !!Z && jc(Z) && Oj(F, Z) && (x8(A) || eR(A));
}
var pS0 = $rB;
function qrB(A, B) {
  return A != null && pS0(A, B, lS0);
}
var oY1 = qrB;
var NrB = 1,
  LrB = 2;
function MrB(A, B) {
  if (xc(A) && sY1(B)) return rY1(fH(A), B);
  return function (Q) {
    var D = SP0(Q, A);
    return D === void 0 && D === B ? oY1(Q, A) : ic(B, D, NrB | LrB);
  };
}
var iS0 = MrB;
function RrB(A) {
  return function (B) {
    return B == null ? void 0 : B[A];
  };
}
var nS0 = RrB;
function OrB(A) {
  return function (B) {
    return _j(B, A);
  };
}
var aS0 = OrB;
function TrB(A) {
  return xc(A) ? nS0(fH(A)) : aS0(A);
}
var sS0 = TrB;
function PrB(A) {
  if (typeof A == "function") return A;
  if (A == null) return Oc;
  if (typeof A == "object") return x8(A) ? iS0(A[0], A[1]) : cS0(A);
  return sS0(A);
}
var nq = PrB;
function jrB(A) {
  return function (B, Q, D) {
    var Z = -1,
      G = Object(B),
      F = D(B),
      I = F.length;
    while (I--) {
      var Y = F[A ? I : ++Z];
      if (Q(G[Y], Y, G) === !1) break;
    }
    return B;
  };
}
var oS0 = jrB;
var yrB = oS0(),
  tY1 = yrB;
function krB(A, B) {
  return A && tY1(A, B, vH);
}
var eY1 = krB;
function frB(A, B, Q) {
  if (Q !== void 0 && !mq(A[B], Q) || Q === void 0 && !(B in A)) Tj(A, B, Q);
}
var pA1 = frB;
function hrB(A) {
  return MF(A) && dq(A);
}
var Bj0 = hrB;
function grB(A, B) {
  if (B === "constructor" && typeof A[B] === "function") return;
  if (B == "__proto__") return;
  return A[B];
}
var iA1 = grB;
function urB(A) {
  return xH(A, pq(A));
}
var Qj0 = urB;
function mrB(A, B, Q, D, Z, G, F) {
  var I = iA1(A, Q),
    Y = iA1(B, Q),
    W = F.get(Y);
  if (W) {
    pA1(A, Q, W);
    return;
  }
  var J = G ? G(I, Y, Q + "", A, B, F) : void 0,
    X = J === void 0;
  if (X) {
    var V = x8(Y),
      C = !V && cq(Y),
      K = !V && !C && _c(Y);
    if (J = Y, V || C || K) {
      if (x8(I)) J = I;else if (Bj0(I)) J = Pc(I);else if (C) X = !1, J = dA1(Y, !0);else if (K) X = !1, J = uY1(Y, !0);else J = [];
    } else if (Af(Y) || eR(Y)) {
      if (J = I, eR(I)) J = Qj0(I);else if (!T3(I) || Tc(I)) J = mY1(Y);
    } else X = !1;
  }
  if (X) F.set(Y, J), Z(J, Y, D, G, F), F.delete(Y);
  pA1(A, Q, J);
}
var Dj0 = mrB;
function Zj0(A, B, Q, D, Z) {
  if (A === B) return;
  tY1(B, function (G, F) {
    if (Z || (Z = new iq()), T3(G)) Dj0(A, B, F, Q, Zj0, D, Z);else {
      var I = D ? D(iA1(A, F), G, F + "", A, B, Z) : void 0;
      if (I === void 0) I = G;
      pA1(A, F, I);
    }
  }, pq);
}
var Gj0 = Zj0;
var drB = oT0(function (A, B, Q, D) {
    Gj0(A, B, Q, D);
  }),
  BW1 = drB;
function rrB(A, B) {
  var Q = {};
  return B = nq(B, 3), eY1(A, function (D, Z, G) {
    Tj(Q, Z, B(D, Z, G));
  }), Q;
}
var vj = rrB;
function yoB() {
  return {
    originalCwd: Nj0(),
    totalCostUSD: 0,
    totalAPIDuration: 0,
    totalAPIDurationWithoutRetries: 0,
    startTime: Date.now(),
    lastInteractionTime: Date.now(),
    totalLinesAdded: 0,
    totalLinesRemoved: 0,
    hasUnknownModelCost: !1,
    cwd: Nj0(),
    modelUsage: {},
    mainLoopModelOverride: void 0,
    maxRateLimitFallbackActive: !1,
    initialMainLoopModel: null,
    modelStrings: null,
    isNonInteractiveSession: !0,
    isInteractive: !1,
    clientType: "cli",
    flagSettingsPath: void 0,
    meter: null,
    sessionCounter: null,
    locCounter: null,
    prCounter: null,
    commitCounter: null,
    costCounter: null,
    tokenCounter: null,
    codeEditToolDecisionCounter: null,
    activeTimeCounter: null,
    sessionId: Lj0(),
    loggerProvider: null,
    eventLogger: null,
    agentColorMap: new Map(),
    agentColorIndex: 0,
    backgroundShells: new Map(),
    backgroundShellCounter: 0,
    backgroundShellSubscribers: new Set()
  };
}
var i2 = yoB();
function B9() {
  return i2.sessionId;
}
function x9() {
  return i2.originalCwd;
}
function Oj0() {
  return i2.cwd;
}
function aA1() {
  return i2.mainLoopModelOverride;
}
function nc() {
  return i2.maxRateLimitFallbackActive;
}
function KW1() {
  return i2.modelStrings;
}
function cu1(A) {
  i2.modelStrings = A;
}
function sc() {
  return i2.isNonInteractiveSession;
}
function aj0() {
  return i2.isInteractive;
}
function rj0() {
  return i2.clientType;
}
function nu1() {
  return i2.flagSettingsPath;
}
import { resolve as N$2, dirname as L$2, normalize as Wp4, join as B51 } from "path";
var kk0 = F1(Dm1(), 1);
import { Buffer as NeB } from "node:buffer";
import LeB from "node:path";
import Tm1 from "node:child_process";
import PW1 from "node:process";
function Zm1(A) {
  let B = typeof A === "string" ? `
` : `
`.charCodeAt(),
    Q = typeof A === "string" ? "\r" : "\r".charCodeAt();
  if (A[A.length - 1] === B) A = A.slice(0, -1);
  if (A[A.length - 1] === Q) A = A.slice(0, -1);
  return A;
}
import EW1 from "node:process";
import oA1 from "node:path";
import { fileURLToPath as py0 } from "node:url";
function zW1(A = {}) {
  let {
    env: B = process.env,
    platform: Q = process.platform
  } = A;
  if (Q !== "win32") return "PATH";
  return Object.keys(B).reverse().find(D => D.toUpperCase() === "PATH") || "Path";
}
var ItB = ({
    cwd: A = EW1.cwd(),
    path: B = EW1.env[zW1()],
    preferLocal: Q = !0,
    execPath: D = EW1.execPath,
    addExecPath: Z = !0
  } = {}) => {
    let G = A instanceof URL ? py0(A) : A,
      F = oA1.resolve(G),
      I = [];
    if (Q) YtB(I, F);
    if (Z) WtB(I, D, F);
    return [...I, B].join(oA1.delimiter);
  },
  YtB = (A, B) => {
    let Q;
    while (Q !== B) A.push(oA1.join(B, "node_modules/.bin")), Q = B, B = oA1.resolve(B, "..");
  },
  WtB = (A, B, Q) => {
    let D = B instanceof URL ? py0(B) : B;
    A.push(oA1.resolve(Q, D, ".."));
  },
  iy0 = ({
    env: A = EW1.env,
    ...B
  } = {}) => {
    A = {
      ...A
    };
    let Q = zW1({
      env: A
    });
    return B.path = A[Q], A[Q] = ItB(B), A;
  };
var JtB = (A, B, Q, D) => {
    if (Q === "length" || Q === "prototype") return;
    if (Q === "arguments" || Q === "caller") return;
    let Z = Object.getOwnPropertyDescriptor(A, Q),
      G = Object.getOwnPropertyDescriptor(B, Q);
    if (!XtB(Z, G) && D) return;
    Object.defineProperty(A, Q, G);
  },
  XtB = function (A, B) {
    return A === void 0 || A.configurable || A.writable === B.writable && A.enumerable === B.enumerable && A.configurable === B.configurable && (A.writable || A.value === B.value);
  },
  VtB = (A, B) => {
    let Q = Object.getPrototypeOf(B);
    if (Q === Object.getPrototypeOf(A)) return;
    Object.setPrototypeOf(A, Q);
  },
  CtB = (A, B) => `/* Wrapped ${A}*/
${B}`,
  KtB = Object.getOwnPropertyDescriptor(Function.prototype, "toString"),
  HtB = Object.getOwnPropertyDescriptor(Function.prototype.toString, "name"),
  ztB = (A, B, Q) => {
    let D = Q === "" ? "" : `with ${Q.trim()}() `,
      Z = CtB.bind(null, D, B.toString());
    Object.defineProperty(Z, "name", HtB), Object.defineProperty(A, "toString", {
      ...KtB,
      value: Z
    });
  };
function Gm1(A, B, {
  ignoreNonConfigurable: Q = !1
} = {}) {
  let {
    name: D
  } = A;
  for (let Z of Reflect.ownKeys(B)) JtB(A, B, Z, Q);
  return VtB(A, B), ztB(A, B, D), A;
}
var UW1 = new WeakMap(),
  ny0 = (A, B = {}) => {
    if (typeof A !== "function") throw new TypeError("Expected a function");
    let Q,
      D = 0,
      Z = A.displayName || A.name || "<anonymous>",
      G = function (...F) {
        if (UW1.set(G, ++D), D === 1) Q = A.apply(this, F), A = null;else if (B.throw === !0) throw new Error(`Function \`${Z}\` can only be called once`);
        return Q;
      };
    return Gm1(G, A), UW1.set(G, D), G;
  };
ny0.callCount = A => {
  if (!UW1.has(A)) throw new Error(`The given function \`${A.name}\` is not wrapped by the \`onetime\` package`);
  return UW1.get(A);
};
var ay0 = ny0;
import OtB from "node:process";
import { constants as $tB } from "node:os";
var sy0 = () => {
    let A = Fm1 - ry0 + 1;
    return Array.from({
      length: A
    }, EtB);
  },
  EtB = (A, B) => ({
    name: `SIGRT${B + 1}`,
    number: ry0 + B,
    action: "terminate",
    description: "Application-specific signal (realtime)",
    standard: "posix"
  }),
  ry0 = 34,
  Fm1 = 64;
import { constants as UtB } from "node:os";
var oy0 = [{
  name: "SIGHUP",
  number: 1,
  action: "terminate",
  description: "Terminal closed",
  standard: "posix"
}, {
  name: "SIGINT",
  number: 2,
  action: "terminate",
  description: "User interruption with CTRL-C",
  standard: "ansi"
}, {
  name: "SIGQUIT",
  number: 3,
  action: "core",
  description: "User interruption with CTRL-\\",
  standard: "posix"
}, {
  name: "SIGILL",
  number: 4,
  action: "core",
  description: "Invalid machine instruction",
  standard: "ansi"
}, {
  name: "SIGTRAP",
  number: 5,
  action: "core",
  description: "Debugger breakpoint",
  standard: "posix"
}, {
  name: "SIGABRT",
  number: 6,
  action: "core",
  description: "Aborted",
  standard: "ansi"
}, {
  name: "SIGIOT",
  number: 6,
  action: "core",
  description: "Aborted",
  standard: "bsd"
}, {
  name: "SIGBUS",
  number: 7,
  action: "core",
  description: "Bus error due to misaligned, non-existing address or paging error",
  standard: "bsd"
}, {
  name: "SIGEMT",
  number: 7,
  action: "terminate",
  description: "Command should be emulated but is not implemented",
  standard: "other"
}, {
  name: "SIGFPE",
  number: 8,
  action: "core",
  description: "Floating point arithmetic error",
  standard: "ansi"
}, {
  name: "SIGKILL",
  number: 9,
  action: "terminate",
  description: "Forced termination",
  standard: "posix",
  forced: !0
}, {
  name: "SIGUSR1",
  number: 10,
  action: "terminate",
  description: "Application-specific signal",
  standard: "posix"
}, {
  name: "SIGSEGV",
  number: 11,
  action: "core",
  description: "Segmentation fault",
  standard: "ansi"
}, {
  name: "SIGUSR2",
  number: 12,
  action: "terminate",
  description: "Application-specific signal",
  standard: "posix"
}, {
  name: "SIGPIPE",
  number: 13,
  action: "terminate",
  description: "Broken pipe or socket",
  standard: "posix"
}, {
  name: "SIGALRM",
  number: 14,
  action: "terminate",
  description: "Timeout or timer",
  standard: "posix"
}, {
  name: "SIGTERM",
  number: 15,
  action: "terminate",
  description: "Termination",
  standard: "ansi"
}, {
  name: "SIGSTKFLT",
  number: 16,
  action: "terminate",
  description: "Stack is empty or overflowed",
  standard: "other"
}, {
  name: "SIGCHLD",
  number: 17,
  action: "ignore",
  description: "Child process terminated, paused or unpaused",
  standard: "posix"
}, {
  name: "SIGCLD",
  number: 17,
  action: "ignore",
  description: "Child process terminated, paused or unpaused",
  standard: "other"
}, {
  name: "SIGCONT",
  number: 18,
  action: "unpause",
  description: "Unpaused",
  standard: "posix",
  forced: !0
}, {
  name: "SIGSTOP",
  number: 19,
  action: "pause",
  description: "Paused",
  standard: "posix",
  forced: !0
}, {
  name: "SIGTSTP",
  number: 20,
  action: "pause",
  description: 'Paused using CTRL-Z or "suspend"',
  standard: "posix"
}, {
  name: "SIGTTIN",
  number: 21,
  action: "pause",
  description: "Background process cannot read terminal input",
  standard: "posix"
}, {
  name: "SIGBREAK",
  number: 21,
  action: "terminate",
  description: "User interruption with CTRL-BREAK",
  standard: "other"
}, {
  name: "SIGTTOU",
  number: 22,
  action: "pause",
  description: "Background process cannot write to terminal output",
  standard: "posix"
}, {
  name: "SIGURG",
  number: 23,
  action: "ignore",
  description: "Socket received out-of-band data",
  standard: "bsd"
}, {
  name: "SIGXCPU",
  number: 24,
  action: "core",
  description: "Process timed out",
  standard: "bsd"
}, {
  name: "SIGXFSZ",
  number: 25,
  action: "core",
  description: "File too big",
  standard: "bsd"
}, {
  name: "SIGVTALRM",
  number: 26,
  action: "terminate",
  description: "Timeout or timer",
  standard: "bsd"
}, {
  name: "SIGPROF",
  number: 27,
  action: "terminate",
  description: "Timeout or timer",
  standard: "bsd"
}, {
  name: "SIGWINCH",
  number: 28,
  action: "ignore",
  description: "Terminal window size changed",
  standard: "bsd"
}, {
  name: "SIGIO",
  number: 29,
  action: "terminate",
  description: "I/O is available",
  standard: "other"
}, {
  name: "SIGPOLL",
  number: 29,
  action: "terminate",
  description: "Watched event",
  standard: "other"
}, {
  name: "SIGINFO",
  number: 29,
  action: "ignore",
  description: "Request for process information",
  standard: "other"
}, {
  name: "SIGPWR",
  number: 30,
  action: "terminate",
  description: "Device running out of power",
  standard: "systemv"
}, {
  name: "SIGSYS",
  number: 31,
  action: "core",
  description: "Invalid system call",
  standard: "other"
}, {
  name: "SIGUNUSED",
  number: 31,
  action: "terminate",
  description: "Invalid system call",
  standard: "other"
}];
var Im1 = () => {
    let A = sy0();
    return [...oy0, ...A].map(wtB);
  },
  wtB = ({
    name: A,
    number: B,
    description: Q,
    action: D,
    forced: Z = !1,
    standard: G
  }) => {
    let {
        signals: {
          [A]: F
        }
      } = UtB,
      I = F !== void 0;
    return {
      name: A,
      number: I ? F : B,
      description: Q,
      supported: I,
      action: D,
      forced: Z,
      standard: G
    };
  };
var qtB = () => {
    let A = Im1();
    return Object.fromEntries(A.map(NtB));
  },
  NtB = ({
    name: A,
    number: B,
    description: Q,
    supported: D,
    action: Z,
    forced: G,
    standard: F
  }) => [A, {
    name: A,
    number: B,
    description: Q,
    supported: D,
    action: Z,
    forced: G,
    standard: F
  }],
  ty0 = qtB(),
  LtB = () => {
    let A = Im1(),
      B = Fm1 + 1,
      Q = Array.from({
        length: B
      }, (D, Z) => MtB(Z, A));
    return Object.assign({}, ...Q);
  },
  MtB = (A, B) => {
    let Q = RtB(A, B);
    if (Q === void 0) return {};
    let {
      name: D,
      description: Z,
      supported: G,
      action: F,
      forced: I,
      standard: Y
    } = Q;
    return {
      [A]: {
        name: D,
        number: A,
        description: Z,
        supported: G,
        action: F,
        forced: I,
        standard: Y
      }
    };
  },
  RtB = (A, B) => {
    let Q = B.find(({
      name: D
    }) => $tB.signals[D] === A);
    if (Q !== void 0) return Q;
    return B.find(D => D.number === A);
  },
  yh8 = LtB();
var TtB = ({
    timedOut: A,
    timeout: B,
    errorCode: Q,
    signal: D,
    signalDescription: Z,
    exitCode: G,
    isCanceled: F
  }) => {
    if (A) return `timed out after ${B} milliseconds`;
    if (F) return "was canceled";
    if (Q !== void 0) return `failed with ${Q}`;
    if (D !== void 0) return `was killed with ${D} (${Z})`;
    if (G !== void 0) return `failed with exit code ${G}`;
    return "failed";
  },
  tA1 = ({
    stdout: A,
    stderr: B,
    all: Q,
    error: D,
    signal: Z,
    exitCode: G,
    command: F,
    escapedCommand: I,
    timedOut: Y,
    isCanceled: W,
    killed: J,
    parsed: {
      options: {
        timeout: X,
        cwd: V = OtB.cwd()
      }
    }
  }) => {
    G = G === null ? void 0 : G, Z = Z === null ? void 0 : Z;
    let C = Z === void 0 ? void 0 : ty0[Z].description,
      K = D && D.code,
      z = `Command ${TtB({
        timedOut: Y,
        timeout: X,
        errorCode: K,
        signal: Z,
        signalDescription: C,
        exitCode: G,
        isCanceled: W
      })}: ${F}`,
      $ = Object.prototype.toString.call(D) === "[object Error]",
      L = $ ? `${z}
${D.message}` : z,
      N = [L, B, A].filter(Boolean).join(`
`);
    if ($) D.originalMessage = D.message, D.message = N;else D = new Error(N);
    if (D.shortMessage = L, D.command = F, D.escapedCommand = I, D.exitCode = G, D.signal = Z, D.signalDescription = C, D.stdout = A, D.stderr = B, D.cwd = V, Q !== void 0) D.all = Q;
    if ("bufferedData" in D) delete D.bufferedData;
    return D.failed = !0, D.timedOut = Boolean(Y), D.isCanceled = W, D.killed = J && !Y, D;
  };
var wW1 = ["stdin", "stdout", "stderr"],
  PtB = A => wW1.some(B => A[B] !== void 0),
  ey0 = A => {
    if (!A) return;
    let {
      stdio: B
    } = A;
    if (B === void 0) return wW1.map(D => A[D]);
    if (PtB(A)) throw new Error(`It's not possible to provide \`stdio\` in combination with one of ${wW1.map(D => `\`${D}\``).join(", ")}`);
    if (typeof B === "string") return B;
    if (!Array.isArray(B)) throw new TypeError(`Expected \`stdio\` to be of type \`string\` or \`Array\`, got \`${typeof B}\``);
    let Q = Math.max(B.length, wW1.length);
    return Array.from({
      length: Q
    }, (D, Z) => B[Z]);
  };
import ytB from "node:os";
var Zf = [];
var $W1 = A => !!A && typeof A === "object" && typeof A.removeListener === "function" && typeof A.emit === "function" && typeof A.reallyExit === "function" && typeof A.listeners === "function" && typeof A.kill === "function" && typeof A.pid === "number" && typeof A.on === "function",
  Ym1 = Symbol.for("signal-exit emitter"),
  Wm1 = globalThis,
  StB = Object.defineProperty.bind(Object);
class Ak0 {
  emitted = {
    afterExit: !1,
    exit: !1
  };
  listeners = {
    afterExit: [],
    exit: []
  };
  count = 0;
  id = Math.random();
  constructor() {
    if (Wm1[Ym1]) return Wm1[Ym1];
    StB(Wm1, Ym1, {
      value: this,
      writable: !1,
      enumerable: !1,
      configurable: !1
    });
  }
  on(A, B) {
    this.listeners[A].push(B);
  }
  removeListener(A, B) {
    let Q = this.listeners[A],
      D = Q.indexOf(B);
    if (D === -1) return;
    if (D === 0 && Q.length === 1) Q.length = 0;else Q.splice(D, 1);
  }
  emit(A, B, Q) {
    if (this.emitted[A]) return !1;
    this.emitted[A] = !0;
    let D = !1;
    for (let Z of this.listeners[A]) D = Z(B, Q) === !0 || D;
    if (A === "exit") D = this.emit("afterExit", B, Q) || D;
    return D;
  }
}
class Xm1 {}
var jtB = A => {
  return {
    onExit(B, Q) {
      return A.onExit(B, Q);
    },
    load() {
      return A.load();
    },
    unload() {
      return A.unload();
    }
  };
};
class Bk0 extends Xm1 {
  onExit() {
    return () => {};
  }
  load() {}
  unload() {}
}
class Qk0 extends Xm1 {
  #A = Jm1.platform === "win32" ? "SIGINT" : "SIGHUP";
  #B = new Ak0();
  #Q;
  #D;
  #Z;
  #Y = {};
  #G = !1;
  constructor(A) {
    super();
    this.#Q = A, this.#Y = {};
    for (let B of Zf) this.#Y[B] = () => {
      let Q = this.#Q.listeners(B),
        {
          count: D
        } = this.#B,
        Z = A;
      if (typeof Z.__signal_exit_emitter__ === "object" && typeof Z.__signal_exit_emitter__.count === "number") D += Z.__signal_exit_emitter__.count;
      if (Q.length === D) {
        this.unload();
        let G = this.#B.emit("exit", null, B),
          F = B === "SIGHUP" ? this.#A : B;
        if (!G) A.kill(A.pid, F);
      }
    };
    this.#Z = A.reallyExit, this.#D = A.emit;
  }
  onExit(A, B) {
    if (!$W1(this.#Q)) return () => {};
    if (this.#G === !1) this.load();
    let Q = B?.alwaysLast ? "afterExit" : "exit";
    return this.#B.on(Q, A), () => {
      if (this.#B.removeListener(Q, A), this.#B.listeners.exit.length === 0 && this.#B.listeners.afterExit.length === 0) this.unload();
    };
  }
  load() {
    if (this.#G) return;
    this.#G = !0, this.#B.count += 1;
    for (let A of Zf) try {
      let B = this.#Y[A];
      if (B) this.#Q.on(A, B);
    } catch (B) {}
    this.#Q.emit = (A, ...B) => {
      return this.#W(A, ...B);
    }, this.#Q.reallyExit = A => {
      return this.#J(A);
    };
  }
  unload() {
    if (!this.#G) return;
    this.#G = !1, Zf.forEach(A => {
      let B = this.#Y[A];
      if (!B) throw new Error("Listener not defined for signal: " + A);
      try {
        this.#Q.removeListener(A, B);
      } catch (Q) {}
    }), this.#Q.emit = this.#D, this.#Q.reallyExit = this.#Z, this.#B.count -= 1;
  }
  #J(A) {
    if (!$W1(this.#Q)) return 0;
    return this.#Q.exitCode = A || 0, this.#B.emit("exit", this.#Q.exitCode, null), this.#Z.call(this.#Q, this.#Q.exitCode);
  }
  #W(A, ...B) {
    let Q = this.#D;
    if (A === "exit" && $W1(this.#Q)) {
      if (typeof B[0] === "number") this.#Q.exitCode = B[0];
      let D = Q.call(this.#Q, A, ...B);
      return this.#B.emit("exit", this.#Q.exitCode, null), D;
    } else return Q.call(this.#Q, A, ...B);
  }
}
var Jm1 = globalThis.process,
  {
    onExit: qW1,
    load: gh8,
    unload: uh8
  } = jtB($W1(Jm1) ? new Qk0(Jm1) : new Bk0());
var ktB = 5000,
  Dk0 = (A, B = "SIGTERM", Q = {}) => {
    let D = A(B);
    return _tB(A, B, Q, D), D;
  },
  _tB = (A, B, Q, D) => {
    if (!xtB(B, Q, D)) return;
    let Z = btB(Q),
      G = setTimeout(() => {
        A("SIGKILL");
      }, Z);
    if (G.unref) G.unref();
  },
  xtB = (A, {
    forceKillAfterTimeout: B
  }, Q) => vtB(A) && B !== !1 && Q,
  vtB = A => A === ytB.constants.signals.SIGTERM || typeof A === "string" && A.toUpperCase() === "SIGTERM",
  btB = ({
    forceKillAfterTimeout: A = !0
  }) => {
    if (A === !0) return ktB;
    if (!Number.isFinite(A) || A < 0) throw new TypeError(`Expected the \`forceKillAfterTimeout\` option to be a non-negative integer, got \`${A}\` (${typeof A})`);
    return A;
  },
  Zk0 = (A, B) => {
    if (A.kill()) B.isCanceled = !0;
  },
  ftB = (A, B, Q) => {
    A.kill(B), Q(Object.assign(new Error("Timed out"), {
      timedOut: !0,
      signal: B
    }));
  },
  Gk0 = (A, {
    timeout: B,
    killSignal: Q = "SIGTERM"
  }, D) => {
    if (B === 0 || B === void 0) return D;
    let Z,
      G = new Promise((I, Y) => {
        Z = setTimeout(() => {
          ftB(A, Q, Y);
        }, B);
      }),
      F = D.finally(() => {
        clearTimeout(Z);
      });
    return Promise.race([G, F]);
  },
  Fk0 = ({
    timeout: A
  }) => {
    if (A !== void 0 && (!Number.isFinite(A) || A < 0)) throw new TypeError(`Expected the \`timeout\` option to be a non-negative integer, got \`${A}\` (${typeof A})`);
  },
  Ik0 = async (A, {
    cleanup: B,
    detached: Q
  }, D) => {
    if (!B || Q) return D;
    let Z = qW1(() => {
      A.kill();
    });
    return D.finally(() => {
      Z();
    });
  };
import { createWriteStream as htB } from "node:fs";
import { ChildProcess as gtB } from "node:child_process";
function NW1(A) {
  return A !== null && typeof A === "object" && typeof A.pipe === "function";
}
function Vm1(A) {
  return NW1(A) && A.writable !== !1 && typeof A._write === "function" && typeof A._writableState === "object";
}
var utB = A => A instanceof gtB && typeof A.then === "function",
  Cm1 = (A, B, Q) => {
    if (typeof Q === "string") return A[B].pipe(htB(Q)), A;
    if (Vm1(Q)) return A[B].pipe(Q), A;
    if (!utB(Q)) throw new TypeError("The second argument must be a string, a stream or an Execa child process.");
    if (!Vm1(Q.stdin)) throw new TypeError("The target child process's stdin must be available.");
    return A[B].pipe(Q.stdin), Q;
  },
  Yk0 = A => {
    if (A.stdout !== null) A.pipeStdout = Cm1.bind(void 0, A, "stdout");
    if (A.stderr !== null) A.pipeStderr = Cm1.bind(void 0, A, "stderr");
    if (A.all !== void 0) A.pipeAll = Cm1.bind(void 0, A, "all");
  };
import { createReadStream as GeB, readFileSync as FeB } from "node:fs";
import { setTimeout as IeB } from "node:timers/promises";
var eA1 = async (A, {
    init: B,
    convertChunk: Q,
    getSize: D,
    truncateChunk: Z,
    addChunk: G,
    getFinalChunk: F,
    finalize: I
  }, {
    maxBuffer: Y = Number.POSITIVE_INFINITY
  } = {}) => {
    if (!dtB(A)) throw new Error("The first argument must be a Readable, a ReadableStream, or an async iterable.");
    let W = B();
    W.length = 0;
    try {
      for await (let J of A) {
        let X = ctB(J),
          V = Q[X](J, W);
        Xk0({
          convertedChunk: V,
          state: W,
          getSize: D,
          truncateChunk: Z,
          addChunk: G,
          maxBuffer: Y
        });
      }
      return mtB({
        state: W,
        convertChunk: Q,
        getSize: D,
        truncateChunk: Z,
        addChunk: G,
        getFinalChunk: F,
        maxBuffer: Y
      }), I(W);
    } catch (J) {
      throw J.bufferedData = I(W), J;
    }
  },
  mtB = ({
    state: A,
    getSize: B,
    truncateChunk: Q,
    addChunk: D,
    getFinalChunk: Z,
    maxBuffer: G
  }) => {
    let F = Z(A);
    if (F !== void 0) Xk0({
      convertedChunk: F,
      state: A,
      getSize: B,
      truncateChunk: Q,
      addChunk: D,
      maxBuffer: G
    });
  },
  Xk0 = ({
    convertedChunk: A,
    state: B,
    getSize: Q,
    truncateChunk: D,
    addChunk: Z,
    maxBuffer: G
  }) => {
    let F = Q(A),
      I = B.length + F;
    if (I <= G) {
      Wk0(A, B, Z, I);
      return;
    }
    let Y = D(A, G - B.length);
    if (Y !== void 0) Wk0(Y, B, Z, G);
    throw new Km1();
  },
  Wk0 = (A, B, Q, D) => {
    B.contents = Q(A, B, D), B.length = D;
  },
  dtB = A => typeof A === "object" && A !== null && typeof A[Symbol.asyncIterator] === "function",
  ctB = A => {
    let B = typeof A;
    if (B === "string") return "string";
    if (B !== "object" || A === null) return "others";
    if (globalThis.Buffer?.isBuffer(A)) return "buffer";
    let Q = Jk0.call(A);
    if (Q === "[object ArrayBuffer]") return "arrayBuffer";
    if (Q === "[object DataView]") return "dataView";
    if (Number.isInteger(A.byteLength) && Number.isInteger(A.byteOffset) && Jk0.call(A.buffer) === "[object ArrayBuffer]") return "typedArray";
    return "others";
  },
  {
    toString: Jk0
  } = Object.prototype;
class Km1 extends Error {
  name = "MaxBufferError";
  constructor() {
    super("maxBuffer exceeded");
  }
}
var Hm1 = A => A,
  zm1 = () => {
    return;
  },
  Em1 = ({
    contents: A
  }) => A,
  LW1 = A => {
    throw new Error(`Streams in object mode are not supported: ${String(A)}`);
  },
  MW1 = A => A.length;
async function Um1(A, B) {
  return eA1(A, ttB, B);
}
var ltB = () => ({
    contents: new ArrayBuffer(0)
  }),
  ptB = A => itB.encode(A),
  itB = new TextEncoder(),
  Vk0 = A => new Uint8Array(A),
  Ck0 = A => new Uint8Array(A.buffer, A.byteOffset, A.byteLength),
  ntB = (A, B) => A.slice(0, B),
  atB = (A, {
    contents: B,
    length: Q
  }, D) => {
    let Z = zk0() ? rtB(B, D) : stB(B, D);
    return new Uint8Array(Z).set(A, Q), Z;
  },
  stB = (A, B) => {
    if (B <= A.byteLength) return A;
    let Q = new ArrayBuffer(Hk0(B));
    return new Uint8Array(Q).set(new Uint8Array(A), 0), Q;
  },
  rtB = (A, B) => {
    if (B <= A.maxByteLength) return A.resize(B), A;
    let Q = new ArrayBuffer(B, {
      maxByteLength: Hk0(B)
    });
    return new Uint8Array(Q).set(new Uint8Array(A), 0), Q;
  },
  Hk0 = A => Kk0 ** Math.ceil(Math.log(A) / Math.log(Kk0)),
  Kk0 = 2,
  otB = ({
    contents: A,
    length: B
  }) => zk0() ? A : A.slice(0, B),
  zk0 = () => "resize" in ArrayBuffer.prototype,
  ttB = {
    init: ltB,
    convertChunk: {
      string: ptB,
      buffer: Vk0,
      arrayBuffer: Vk0,
      dataView: Ck0,
      typedArray: Ck0,
      others: LW1
    },
    getSize: MW1,
    truncateChunk: ntB,
    addChunk: atB,
    getFinalChunk: zm1,
    finalize: otB
  };
async function RW1(A, B) {
  if (!("Buffer" in globalThis)) throw new Error("getStreamAsBuffer() is only supported in Node.js");
  try {
    return Ek0(await Um1(A, B));
  } catch (Q) {
    if (Q.bufferedData !== void 0) Q.bufferedData = Ek0(Q.bufferedData);
    throw Q;
  }
}
var Ek0 = A => globalThis.Buffer.from(A);
async function wm1(A, B) {
  return eA1(A, DeB, B);
}
var etB = () => ({
    contents: "",
    textDecoder: new TextDecoder()
  }),
  OW1 = (A, {
    textDecoder: B
  }) => B.decode(A, {
    stream: !0
  }),
  AeB = (A, {
    contents: B
  }) => B + A,
  BeB = (A, B) => A.slice(0, B),
  QeB = ({
    textDecoder: A
  }) => {
    let B = A.decode();
    return B === "" ? void 0 : B;
  },
  DeB = {
    init: etB,
    convertChunk: {
      string: Hm1,
      buffer: OW1,
      arrayBuffer: OW1,
      dataView: OW1,
      typedArray: OW1,
      others: LW1
    },
    getSize: MW1,
    truncateChunk: BeB,
    addChunk: AeB,
    getFinalChunk: QeB,
    finalize: Em1
  };
var $k0 = F1(wk0(), 1),
  qk0 = A => {
    if (A !== void 0) throw new TypeError("The `input` and `inputFile` options cannot be both set.");
  },
  YeB = ({
    input: A,
    inputFile: B
  }) => {
    if (typeof B !== "string") return A;
    return qk0(A), FeB(B);
  },
  Nk0 = A => {
    let B = YeB(A);
    if (NW1(B)) throw new TypeError("The `input` option cannot be a stream in sync mode");
    return B;
  },
  WeB = ({
    input: A,
    inputFile: B
  }) => {
    if (typeof B !== "string") return A;
    return qk0(A), GeB(B);
  },
  Lk0 = (A, B) => {
    let Q = WeB(B);
    if (Q === void 0) return;
    if (NW1(Q)) Q.pipe(A.stdin);else A.stdin.end(Q);
  },
  Mk0 = (A, {
    all: B
  }) => {
    if (!B || !A.stdout && !A.stderr) return;
    let Q = $k0.default();
    if (A.stdout) Q.add(A.stdout);
    if (A.stderr) Q.add(A.stderr);
    return Q;
  },
  $m1 = async (A, B) => {
    if (!A || B === void 0) return;
    await IeB(0), A.destroy();
    try {
      return await B;
    } catch (Q) {
      return Q.bufferedData;
    }
  },
  qm1 = (A, {
    encoding: B,
    buffer: Q,
    maxBuffer: D
  }) => {
    if (!A || !Q) return;
    if (B === "utf8" || B === "utf-8") return wm1(A, {
      maxBuffer: D
    });
    if (B === null || B === "buffer") return RW1(A, {
      maxBuffer: D
    });
    return JeB(A, D, B);
  },
  JeB = async (A, B, Q) => {
    return (await RW1(A, {
      maxBuffer: B
    })).toString(Q);
  },
  Rk0 = async ({
    stdout: A,
    stderr: B,
    all: Q
  }, {
    encoding: D,
    buffer: Z,
    maxBuffer: G
  }, F) => {
    let I = qm1(A, {
        encoding: D,
        buffer: Z,
        maxBuffer: G
      }),
      Y = qm1(B, {
        encoding: D,
        buffer: Z,
        maxBuffer: G
      }),
      W = qm1(Q, {
        encoding: D,
        buffer: Z,
        maxBuffer: G * 2
      });
    try {
      return await Promise.all([F, I, Y, W]);
    } catch (J) {
      return Promise.all([{
        error: J,
        signal: J.signal,
        timedOut: J.timedOut
      }, $m1(A, I), $m1(B, Y), $m1(Q, W)]);
    }
  };
var XeB = (async () => {})().constructor.prototype,
  VeB = ["then", "catch", "finally"].map(A => [A, Reflect.getOwnPropertyDescriptor(XeB, A)]),
  Nm1 = (A, B) => {
    for (let [Q, D] of VeB) {
      let Z = typeof B === "function" ? (...G) => Reflect.apply(D.value, B(), G) : D.value.bind(B);
      Reflect.defineProperty(A, Q, {
        ...D,
        value: Z
      });
    }
  },
  Ok0 = A => new Promise((B, Q) => {
    if (A.on("exit", (D, Z) => {
      B({
        exitCode: D,
        signal: Z
      });
    }), A.on("error", D => {
      Q(D);
    }), A.stdin) A.stdin.on("error", D => {
      Q(D);
    });
  });
var Sk0 = (A, B = []) => {
    if (!Array.isArray(B)) return [A];
    return [A, ...B];
  },
  HeB = /^[\w.-]+$/,
  zeB = A => {
    if (typeof A !== "string" || HeB.test(A)) return A;
    return `"${A.replaceAll('"', "\\\"")}"`;
  },
  Lm1 = (A, B) => Sk0(A, B).join(" "),
  Mm1 = (A, B) => Sk0(A, B).map(Q => zeB(Q)).join(" "),
  EeB = / +/g;
import { debuglog as weB } from "node:util";
import $eB from "node:process";
var jk0 = weB("execa").enabled,
  TW1 = (A, B) => String(A).padStart(B, "0"),
  qeB = () => {
    let A = new Date();
    return `${TW1(A.getHours(), 2)}:${TW1(A.getMinutes(), 2)}:${TW1(A.getSeconds(), 2)}.${TW1(A.getMilliseconds(), 3)}`;
  },
  Om1 = (A, {
    verbose: B
  }) => {
    if (!B) return;
    $eB.stderr.write(`[${qeB()}] ${A}
`);
  };
var MeB = 1e8,
  ReB = ({
    env: A,
    extendEnv: B,
    preferLocal: Q,
    localDir: D,
    execPath: Z
  }) => {
    let G = B ? {
      ...PW1.env,
      ...A
    } : A;
    if (Q) return iy0({
      env: G,
      cwd: D,
      execPath: Z
    });
    return G;
  },
  _k0 = (A, B, Q = {}) => {
    let D = kk0.default._parse(A, B, Q);
    if (A = D.command, B = D.args, Q = D.options, Q = {
      maxBuffer: MeB,
      buffer: !0,
      stripFinalNewline: !0,
      extendEnv: !0,
      preferLocal: !1,
      localDir: Q.cwd || PW1.cwd(),
      execPath: PW1.execPath,
      encoding: "utf8",
      reject: !0,
      cleanup: !0,
      all: !1,
      windowsHide: !0,
      verbose: jk0,
      ...Q
    }, Q.env = ReB(Q), Q.stdio = ey0(Q), PW1.platform === "win32" && LeB.basename(A, ".exe") === "cmd") B.unshift("/q");
    return {
      file: A,
      args: B,
      options: Q,
      parsed: D
    };
  },
  A21 = (A, B, Q) => {
    if (typeof B !== "string" && !NeB.isBuffer(B)) return Q === void 0 ? void 0 : "";
    if (A.stripFinalNewline) return Zm1(B);
    return B;
  };
function Pm1(A, B, Q) {
  let D = _k0(A, B, Q),
    Z = Lm1(A, B),
    G = Mm1(A, B);
  Om1(G, D.options), Fk0(D.options);
  let F;
  try {
    F = Tm1.spawn(D.file, D.args, D.options);
  } catch (C) {
    let K = new Tm1.ChildProcess(),
      H = Promise.reject(tA1({
        error: C,
        stdout: "",
        stderr: "",
        all: "",
        command: Z,
        escapedCommand: G,
        parsed: D,
        timedOut: !1,
        isCanceled: !1,
        killed: !1
      }));
    return Nm1(K, H), K;
  }
  let I = Ok0(F),
    Y = Gk0(F, D.options, I),
    W = Ik0(F, D.options, Y),
    J = {
      isCanceled: !1
    };
  F.kill = Dk0.bind(null, F.kill.bind(F)), F.cancel = Zk0.bind(null, F, J);
  let V = ay0(async () => {
    let [{
        error: C,
        exitCode: K,
        signal: H,
        timedOut: z
      }, $, L, N] = await Rk0(F, D.options, W),
      O = A21(D.options, $),
      R = A21(D.options, L),
      T = A21(D.options, N);
    if (C || K !== 0 || H !== null) {
      let j = tA1({
        error: C,
        exitCode: K,
        signal: H,
        stdout: O,
        stderr: R,
        all: T,
        command: Z,
        escapedCommand: G,
        parsed: D,
        timedOut: z,
        isCanceled: J.isCanceled || (D.options.signal ? D.options.signal.aborted : !1),
        killed: F.killed
      });
      if (!D.options.reject) return j;
      throw j;
    }
    return {
      command: Z,
      escapedCommand: G,
      exitCode: 0,
      stdout: O,
      stderr: R,
      all: T,
      failed: !1,
      timedOut: !1,
      isCanceled: !1,
      killed: !1
    };
  });
  return Lk0(F, D.options), F.all = Mk0(F, D.options), Yk0(F), Nm1(F, V), F;
}
function Sm1(A, B, Q) {
  let D = _k0(A, B, Q),
    Z = Lm1(A, B),
    G = Mm1(A, B);
  Om1(G, D.options);
  let F = Nk0(D.options),
    I;
  try {
    I = Tm1.spawnSync(D.file, D.args, {
      ...D.options,
      input: F
    });
  } catch (J) {
    throw tA1({
      error: J,
      stdout: "",
      stderr: "",
      all: "",
      command: Z,
      escapedCommand: G,
      parsed: D,
      timedOut: !1,
      isCanceled: !1,
      killed: !1
    });
  }
  let Y = A21(D.options, I.stdout, I.error),
    W = A21(D.options, I.stderr, I.error);
  if (I.error || I.status !== 0 || I.signal !== null) {
    let J = tA1({
      stdout: Y,
      stderr: W,
      error: I.error,
      signal: I.signal,
      exitCode: I.status,
      command: Z,
      escapedCommand: G,
      parsed: D,
      timedOut: I.error && I.error.code === "ETIMEDOUT",
      isCanceled: !1,
      killed: I.signal !== null
    });
    if (!D.options.reject) return J;
    throw J;
  }
  return {
    command: Z,
    escapedCommand: G,
    exitCode: 0,
    stdout: Y,
    stderr: W,
    failed: !1,
    timedOut: !1,
    isCanceled: !1,
    killed: !1
  };
}
function jm1() {
  return Oj0();
}
function a0() {
  try {
    return jm1();
  } catch {
    return x9();
  }
}
import { dirname as al4, join as nZ0 } from "path";
var uL = F1(Rl1(), 1);
var yl = SA(() => {
  let A = kl(),
    B = E0();
  return {
    customIDs: {
      sessionId: B9()
    },
    userID: A,
    appVersion: {
      ISSUES_EXPLAINER: "report the issue at https://github.com/anthropics/claude-code/issues",
      PACKAGE_URL: "@anthropic-ai/claude-code",
      README_URL: "https://docs.anthropic.com/s/claude-code",
      VERSION: "1.0.72"
    }.VERSION,
    email: nM9(),
    custom: {
      userType: "external",
      organizationUuid: B.oauthAccount?.organizationUuid,
      accountUuid: B.oauthAccount?.accountUuid,
      ...(process.env.GITHUB_ACTIONS === "true" && {
        githubActor: process.env.GITHUB_ACTOR,
        githubActorId: process.env.GITHUB_ACTOR_ID,
        githubRepositoryId: process.env.GITHUB_REPOSITORY_ID,
        githubRepositoryOwner: process.env.GITHUB_REPOSITORY_OWNER,
        githubRepositoryOwnerId: process.env.GITHUB_REPOSITORY_OWNER_ID
      })
    }
  };
});
function nM9() {
  return;
}
var rl0 = "https://<EMAIL>/****************",
  ol0 = "client-RRNS7R65EAtReO5XA4xDC3eU6ZdJQi6lLEP6b5j32Me";
function o21(A, B) {
  return function Q() {
    return A.apply(B, arguments);
  };
}
var {
    toString: iR9
  } = Object.prototype,
  {
    getPrototypeOf: kl1
  } = Object,
  kX1 = (A => B => {
    let Q = iR9.call(B);
    return A[Q] || (A[Q] = Q.slice(8, -1).toLowerCase());
  })(Object.create(null)),
  Qw = A => {
    return A = A.toLowerCase(), B => kX1(B) === A;
  },
  _X1 = A => B => typeof B === A,
  {
    isArray: xl
  } = Array,
  t21 = _X1("undefined");
function nR9(A) {
  return A !== null && !t21(A) && A.constructor !== null && !t21(A.constructor) && dC(A.constructor.isBuffer) && A.constructor.isBuffer(A);
}
var Jp0 = Qw("ArrayBuffer");
function aR9(A) {
  let B;
  if (typeof ArrayBuffer !== "undefined" && ArrayBuffer.isView) B = ArrayBuffer.isView(A);else B = A && A.buffer && Jp0(A.buffer);
  return B;
}
var sR9 = _X1("string"),
  dC = _X1("function"),
  Xp0 = _X1("number"),
  xX1 = A => A !== null && typeof A === "object",
  rR9 = A => A === !0 || A === !1,
  yX1 = A => {
    if (kX1(A) !== "object") return !1;
    let B = kl1(A);
    return (B === null || B === Object.prototype || Object.getPrototypeOf(B) === null) && !(Symbol.toStringTag in A) && !(Symbol.iterator in A);
  },
  oR9 = Qw("Date"),
  tR9 = Qw("File"),
  eR9 = Qw("Blob"),
  AO9 = Qw("FileList"),
  BO9 = A => xX1(A) && dC(A.pipe),
  QO9 = A => {
    let B;
    return A && (typeof FormData === "function" && A instanceof FormData || dC(A.append) && ((B = kX1(A)) === "formdata" || B === "object" && dC(A.toString) && A.toString() === "[object FormData]"));
  },
  DO9 = Qw("URLSearchParams"),
  [ZO9, GO9, FO9, IO9] = ["ReadableStream", "Request", "Response", "Headers"].map(Qw),
  YO9 = A => A.trim ? A.trim() : A.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g, "");
function e21(A, B, {
  allOwnKeys: Q = !1
} = {}) {
  if (A === null || typeof A === "undefined") return;
  let D, Z;
  if (typeof A !== "object") A = [A];
  if (xl(A)) for (D = 0, Z = A.length; D < Z; D++) B.call(null, A[D], D, A);else {
    let G = Q ? Object.getOwnPropertyNames(A) : Object.keys(A),
      F = G.length,
      I;
    for (D = 0; D < F; D++) I = G[D], B.call(null, A[I], I, A);
  }
}
function Vp0(A, B) {
  B = B.toLowerCase();
  let Q = Object.keys(A),
    D = Q.length,
    Z;
  while (D-- > 0) if (Z = Q[D], B === Z.toLowerCase()) return Z;
  return null;
}
var Sf = (() => {
    if (typeof globalThis !== "undefined") return globalThis;
    return typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : global;
  })(),
  Cp0 = A => !t21(A) && A !== Sf;
function yl1() {
  let {
      caseless: A
    } = Cp0(this) && this || {},
    B = {},
    Q = (D, Z) => {
      let G = A && Vp0(B, Z) || Z;
      if (yX1(B[G]) && yX1(D)) B[G] = yl1(B[G], D);else if (yX1(D)) B[G] = yl1({}, D);else if (xl(D)) B[G] = D.slice();else B[G] = D;
    };
  for (let D = 0, Z = arguments.length; D < Z; D++) arguments[D] && e21(arguments[D], Q);
  return B;
}
var WO9 = (A, B, Q, {
    allOwnKeys: D
  } = {}) => {
    return e21(B, (Z, G) => {
      if (Q && dC(Z)) A[G] = o21(Z, Q);else A[G] = Z;
    }, {
      allOwnKeys: D
    }), A;
  },
  JO9 = A => {
    if (A.charCodeAt(0) === 65279) A = A.slice(1);
    return A;
  },
  XO9 = (A, B, Q, D) => {
    A.prototype = Object.create(B.prototype, D), A.prototype.constructor = A, Object.defineProperty(A, "super", {
      value: B.prototype
    }), Q && Object.assign(A.prototype, Q);
  },
  VO9 = (A, B, Q, D) => {
    let Z,
      G,
      F,
      I = {};
    if (B = B || {}, A == null) return B;
    do {
      Z = Object.getOwnPropertyNames(A), G = Z.length;
      while (G-- > 0) if (F = Z[G], (!D || D(F, A, B)) && !I[F]) B[F] = A[F], I[F] = !0;
      A = Q !== !1 && kl1(A);
    } while (A && (!Q || Q(A, B)) && A !== Object.prototype);
    return B;
  },
  CO9 = (A, B, Q) => {
    if (A = String(A), Q === void 0 || Q > A.length) Q = A.length;
    Q -= B.length;
    let D = A.indexOf(B, Q);
    return D !== -1 && D === Q;
  },
  KO9 = A => {
    if (!A) return null;
    if (xl(A)) return A;
    let B = A.length;
    if (!Xp0(B)) return null;
    let Q = new Array(B);
    while (B-- > 0) Q[B] = A[B];
    return Q;
  },
  HO9 = (A => {
    return B => {
      return A && B instanceof A;
    };
  })(typeof Uint8Array !== "undefined" && kl1(Uint8Array)),
  zO9 = (A, B) => {
    let D = (A && A[Symbol.iterator]).call(A),
      Z;
    while ((Z = D.next()) && !Z.done) {
      let G = Z.value;
      B.call(A, G[0], G[1]);
    }
  },
  EO9 = (A, B) => {
    let Q,
      D = [];
    while ((Q = A.exec(B)) !== null) D.push(Q);
    return D;
  },
  UO9 = Qw("HTMLFormElement"),
  wO9 = A => {
    return A.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g, function B(Q, D, Z) {
      return D.toUpperCase() + Z;
    });
  },
  Wp0 = (({
    hasOwnProperty: A
  }) => (B, Q) => A.call(B, Q))(Object.prototype),
  $O9 = Qw("RegExp"),
  Kp0 = (A, B) => {
    let Q = Object.getOwnPropertyDescriptors(A),
      D = {};
    e21(Q, (Z, G) => {
      let F;
      if ((F = B(Z, G, A)) !== !1) D[G] = F || Z;
    }), Object.defineProperties(A, D);
  },
  qO9 = A => {
    Kp0(A, (B, Q) => {
      if (dC(A) && ["arguments", "caller", "callee"].indexOf(Q) !== -1) return !1;
      let D = A[Q];
      if (!dC(D)) return;
      if (B.enumerable = !1, "writable" in B) {
        B.writable = !1;
        return;
      }
      if (!B.set) B.set = () => {
        throw Error("Can not rewrite read-only method '" + Q + "'");
      };
    });
  },
  NO9 = (A, B) => {
    let Q = {},
      D = Z => {
        Z.forEach(G => {
          Q[G] = !0;
        });
      };
    return xl(A) ? D(A) : D(String(A).split(B)), Q;
  },
  LO9 = () => {},
  MO9 = (A, B) => {
    return A != null && Number.isFinite(A = +A) ? A : B;
  };
function RO9(A) {
  return !!(A && dC(A.append) && A[Symbol.toStringTag] === "FormData" && A[Symbol.iterator]);
}
var OO9 = A => {
    let B = new Array(10),
      Q = (D, Z) => {
        if (xX1(D)) {
          if (B.indexOf(D) >= 0) return;
          if (!("toJSON" in D)) {
            B[Z] = D;
            let G = xl(D) ? [] : {};
            return e21(D, (F, I) => {
              let Y = Q(F, Z + 1);
              !t21(Y) && (G[I] = Y);
            }), B[Z] = void 0, G;
          }
        }
        return D;
      };
    return Q(A, 0);
  },
  TO9 = Qw("AsyncFunction"),
  PO9 = A => A && (xX1(A) || dC(A)) && dC(A.then) && dC(A.catch),
  Hp0 = ((A, B) => {
    if (A) return setImmediate;
    return B ? ((Q, D) => {
      return Sf.addEventListener("message", ({
        source: Z,
        data: G
      }) => {
        if (Z === Sf && G === Q) D.length && D.shift()();
      }, !1), Z => {
        D.push(Z), Sf.postMessage(Q, "*");
      };
    })(`axios@${Math.random()}`, []) : Q => setTimeout(Q);
  })(typeof setImmediate === "function", dC(Sf.postMessage)),
  SO9 = typeof queueMicrotask !== "undefined" ? queueMicrotask.bind(Sf) : typeof process !== "undefined" && process.nextTick || Hp0,
  R0 = {
    isArray: xl,
    isArrayBuffer: Jp0,
    isBuffer: nR9,
    isFormData: QO9,
    isArrayBufferView: aR9,
    isString: sR9,
    isNumber: Xp0,
    isBoolean: rR9,
    isObject: xX1,
    isPlainObject: yX1,
    isReadableStream: ZO9,
    isRequest: GO9,
    isResponse: FO9,
    isHeaders: IO9,
    isUndefined: t21,
    isDate: oR9,
    isFile: tR9,
    isBlob: eR9,
    isRegExp: $O9,
    isFunction: dC,
    isStream: BO9,
    isURLSearchParams: DO9,
    isTypedArray: HO9,
    isFileList: AO9,
    forEach: e21,
    merge: yl1,
    extend: WO9,
    trim: YO9,
    stripBOM: JO9,
    inherits: XO9,
    toFlatObject: VO9,
    kindOf: kX1,
    kindOfTest: Qw,
    endsWith: CO9,
    toArray: KO9,
    forEachEntry: zO9,
    matchAll: EO9,
    isHTMLForm: UO9,
    hasOwnProperty: Wp0,
    hasOwnProp: Wp0,
    reduceDescriptors: Kp0,
    freezeMethods: qO9,
    toObjectSet: NO9,
    toCamelCase: wO9,
    noop: LO9,
    toFiniteNumber: MO9,
    findKey: Vp0,
    global: Sf,
    isContextDefined: Cp0,
    isSpecCompliantForm: RO9,
    toJSONObject: OO9,
    isAsyncFn: TO9,
    isThenable: PO9,
    setImmediate: Hp0,
    asap: SO9
  };
function vl(A, B, Q, D, Z) {
  if (Error.call(this), Error.captureStackTrace) Error.captureStackTrace(this, this.constructor);else this.stack = new Error().stack;
  if (this.message = A, this.name = "AxiosError", B && (this.code = B), Q && (this.config = Q), D && (this.request = D), Z) this.response = Z, this.status = Z.status ? Z.status : null;
}
var zp0 = vl.prototype,
  Ep0 = {};
vl.from = (A, B, Q, D, Z, G) => {
  let F = Object.create(zp0);
  return R0.toFlatObject(A, F, function I(Y) {
    return Y !== Error.prototype;
  }, I => {
    return I !== "isAxiosError";
  }), vl.call(F, A.message, B, Q, D, Z), F.cause = A, F.name = A.name, G && Object.assign(F, G), F;
};
var c2 = vl;
var Nn0 = F1(qn0(), 1),
  lX1 = Nn0.default;
function Qp1(A) {
  return R0.isPlainObject(A) || R0.isArray(A);
}
function Mn0(A) {
  return R0.endsWith(A, "[]") ? A.slice(0, -2) : A;
}
function Ln0(A, B, Q) {
  if (!A) return B;
  return A.concat(B).map(function D(Z, G) {
    return Z = Mn0(Z), !Q && G ? "[" + Z + "]" : Z;
  }).join(Q ? "." : "");
}
function EP9(A) {
  return R0.isArray(A) && !A.some(Qp1);
}
var UP9 = R0.toFlatObject(R0, {}, null, function A(B) {
  return /^is[A-Z]/.test(B);
});
function wP9(A, B, Q) {
  if (!R0.isObject(A)) throw new TypeError("target must be an object");
  B = B || new (lX1 || FormData)(), Q = R0.toFlatObject(Q, {
    metaTokens: !0,
    dots: !1,
    indexes: !1
  }, !1, function K(H, z) {
    return !R0.isUndefined(z[H]);
  });
  let D = Q.metaTokens,
    Z = Q.visitor || J,
    G = Q.dots,
    F = Q.indexes,
    Y = (Q.Blob || typeof Blob !== "undefined" && Blob) && R0.isSpecCompliantForm(B);
  if (!R0.isFunction(Z)) throw new TypeError("visitor must be a function");
  function W(K) {
    if (K === null) return "";
    if (R0.isDate(K)) return K.toISOString();
    if (!Y && R0.isBlob(K)) throw new c2("Blob is not supported. Use a Buffer instead.");
    if (R0.isArrayBuffer(K) || R0.isTypedArray(K)) return Y && typeof Blob === "function" ? new Blob([K]) : Buffer.from(K);
    return K;
  }
  function J(K, H, z) {
    let $ = K;
    if (K && !z && typeof K === "object") {
      if (R0.endsWith(H, "{}")) H = D ? H : H.slice(0, -2), K = JSON.stringify(K);else if (R0.isArray(K) && EP9(K) || (R0.isFileList(K) || R0.endsWith(H, "[]")) && ($ = R0.toArray(K))) return H = Mn0(H), $.forEach(function L(N, O) {
        !(R0.isUndefined(N) || N === null) && B.append(F === !0 ? Ln0([H], O, G) : F === null ? H : H + "[]", W(N));
      }), !1;
    }
    if (Qp1(K)) return !0;
    return B.append(Ln0(z, H, G), W(K)), !1;
  }
  let X = [],
    V = Object.assign(UP9, {
      defaultVisitor: J,
      convertValue: W,
      isVisitable: Qp1
    });
  function C(K, H) {
    if (R0.isUndefined(K)) return;
    if (X.indexOf(K) !== -1) throw Error("Circular reference detected in " + H.join("."));
    X.push(K), R0.forEach(K, function z($, L) {
      if ((!(R0.isUndefined($) || $ === null) && Z.call(B, $, R0.isString(L) ? L.trim() : L, H, V)) === !0) C($, H ? H.concat(L) : [L]);
    }), X.pop();
  }
  if (!R0.isObject(A)) throw new TypeError("data must be an object");
  return C(A), B;
}
var sj = wP9;
function On0(A, B) {
  this._pairs = [], A && sj(A, this, B);
}
var Pn0 = On0;
function $P9(A) {
  return encodeURIComponent(A).replace(/%3A/gi, ":").replace(/%24/g, "$").replace(/%2C/gi, ",").replace(/%20/g, "+").replace(/%5B/gi, "[").replace(/%5D/gi, "]");
}
function yf(A, B, Q) {
  if (!B) return A;
  let D = Q && Q.encode || $P9;
  if (R0.isFunction(Q)) Q = {
    serialize: Q
  };
  let Z = Q && Q.serialize,
    G;
  if (Z) G = Z(B, Q);else G = R0.isURLSearchParams(B) ? B.toString() : new Pn0(B, Q).toString(D);
  if (G) {
    let F = A.indexOf("#");
    if (F !== -1) A = A.slice(0, F);
    A += (A.indexOf("?") === -1 ? "?" : "&") + G;
  }
  return A;
}
class Sn0 {
  constructor() {
    this.handlers = [];
  }
  use(A, B, Q) {
    return this.handlers.push({
      fulfilled: A,
      rejected: B,
      synchronous: Q ? Q.synchronous : !1,
      runWhen: Q ? Q.runWhen : null
    }), this.handlers.length - 1;
  }
  eject(A) {
    if (this.handlers[A]) this.handlers[A] = null;
  }
  clear() {
    if (this.handlers) this.handlers = [];
  }
  forEach(A) {
    R0.forEach(this.handlers, function B(Q) {
      if (Q !== null) A(Q);
    });
  }
}
var Dp1 = Sn0;
var ul = {
  silentJSONParsing: !0,
  forcedJSONParsing: !0,
  clarifyTimeoutError: !1
};
import NP9 from "crypto";
import qP9 from "url";
var jn0 = qP9.URLSearchParams;
var Zp1 = "abcdefghijklmnopqrstuvwxyz",
  yn0 = "0123456789",
  kn0 = {
    DIGIT: yn0,
    ALPHA: Zp1,
    ALPHA_DIGIT: Zp1 + Zp1.toUpperCase() + yn0
  },
  LP9 = (A = 16, B = kn0.ALPHA_DIGIT) => {
    let Q = "",
      {
        length: D
      } = B,
      Z = new Uint32Array(A);
    NP9.randomFillSync(Z);
    for (let G = 0; G < A; G++) Q += B[Z[G] % D];
    return Q;
  },
  _n0 = {
    isNode: !0,
    classes: {
      URLSearchParams: jn0,
      FormData: lX1,
      Blob: typeof Blob !== "undefined" && Blob || null
    },
    ALPHABET: kn0,
    generateString: LP9,
    protocols: ["http", "https", "file", "data"]
  };
var Ip1 = {};
var b8 = {
  ...Ip1,
  ..._n0
};
function Yp1(A, B) {
  return sj(A, new b8.classes.URLSearchParams(), Object.assign({
    visitor: function (Q, D, Z, G) {
      if (b8.isNode && R0.isBuffer(Q)) return this.append(D, Q.toString("base64")), !1;
      return G.defaultVisitor.apply(this, arguments);
    }
  }, B));
}
function TP9(A) {
  return R0.matchAll(/\w+|\[(\w*)]/g, A).map(B => {
    return B[0] === "[]" ? "" : B[1] || B[0];
  });
}
function PP9(A) {
  let B = {},
    Q = Object.keys(A),
    D,
    Z = Q.length,
    G;
  for (D = 0; D < Z; D++) G = Q[D], B[G] = A[G];
  return B;
}
function SP9(A) {
  function B(Q, D, Z, G) {
    let F = Q[G++];
    if (F === "__proto__") return !0;
    let I = Number.isFinite(+F),
      Y = G >= Q.length;
    if (F = !F && R0.isArray(Z) ? Z.length : F, Y) {
      if (R0.hasOwnProp(Z, F)) Z[F] = [Z[F], D];else Z[F] = D;
      return !I;
    }
    if (!Z[F] || !R0.isObject(Z[F])) Z[F] = [];
    if (B(Q, D, Z[F], G) && R0.isArray(Z[F])) Z[F] = PP9(Z[F]);
    return !I;
  }
  if (R0.isFormData(A) && R0.isFunction(A.entries)) {
    let Q = {};
    return R0.forEachEntry(A, (D, Z) => {
      B(TP9(D), Z, Q, 0);
    }), Q;
  }
  return null;
}
var pX1 = SP9;
function jP9(A, B, Q) {
  if (R0.isString(A)) try {
    return (B || JSON.parse)(A), R0.trim(A);
  } catch (D) {
    if (D.name !== "SyntaxError") throw D;
  }
  return (Q || JSON.stringify)(A);
}
var Wp1 = {
  transitional: ul,
  adapter: ["xhr", "http", "fetch"],
  transformRequest: [function A(B, Q) {
    let D = Q.getContentType() || "",
      Z = D.indexOf("application/json") > -1,
      G = R0.isObject(B);
    if (G && R0.isHTMLForm(B)) B = new FormData(B);
    if (R0.isFormData(B)) return Z ? JSON.stringify(pX1(B)) : B;
    if (R0.isArrayBuffer(B) || R0.isBuffer(B) || R0.isStream(B) || R0.isFile(B) || R0.isBlob(B) || R0.isReadableStream(B)) return B;
    if (R0.isArrayBufferView(B)) return B.buffer;
    if (R0.isURLSearchParams(B)) return Q.setContentType("application/x-www-form-urlencoded;charset=utf-8", !1), B.toString();
    let I;
    if (G) {
      if (D.indexOf("application/x-www-form-urlencoded") > -1) return Yp1(B, this.formSerializer).toString();
      if ((I = R0.isFileList(B)) || D.indexOf("multipart/form-data") > -1) {
        let Y = this.env && this.env.FormData;
        return sj(I ? {
          "files[]": B
        } : B, Y && new Y(), this.formSerializer);
      }
    }
    if (G || Z) return Q.setContentType("application/json", !1), jP9(B);
    return B;
  }],
  transformResponse: [function A(B) {
    let Q = this.transitional || Wp1.transitional,
      D = Q && Q.forcedJSONParsing,
      Z = this.responseType === "json";
    if (R0.isResponse(B) || R0.isReadableStream(B)) return B;
    if (B && R0.isString(B) && (D && !this.responseType || Z)) {
      let F = !(Q && Q.silentJSONParsing) && Z;
      try {
        return JSON.parse(B);
      } catch (I) {
        if (F) {
          if (I.name === "SyntaxError") throw c2.from(I, c2.ERR_BAD_RESPONSE, this, null, this.response);
          throw I;
        }
      }
    }
    return B;
  }],
  timeout: 0,
  xsrfCookieName: "XSRF-TOKEN",
  xsrfHeaderName: "X-XSRF-TOKEN",
  maxContentLength: -1,
  maxBodyLength: -1,
  env: {
    FormData: b8.classes.FormData,
    Blob: b8.classes.Blob
  },
  validateStatus: function A(B) {
    return B >= 200 && B < 300;
  },
  headers: {
    common: {
      Accept: "application/json, text/plain, */*",
      "Content-Type": void 0
    }
  }
};
var ml = Wp1;
var yP9 = R0.toObjectSet(["age", "authorization", "content-length", "content-type", "etag", "expires", "from", "host", "if-modified-since", "if-unmodified-since", "last-modified", "location", "max-forwards", "proxy-authorization", "referer", "retry-after", "user-agent"]),
  xn0 = A => {
    let B = {},
      Q,
      D,
      Z;
    return A && A.split(`
`).forEach(function G(F) {
      if (Z = F.indexOf(":"), Q = F.substring(0, Z).trim().toLowerCase(), D = F.substring(Z + 1).trim(), !Q || B[Q] && yP9[Q]) return;
      if (Q === "set-cookie") {
        if (B[Q]) B[Q].push(D);else B[Q] = [D];
      } else B[Q] = B[Q] ? B[Q] + ", " + D : D;
    }), B;
  };
var vn0 = Symbol("internals");
function ZB1(A) {
  return A && String(A).trim().toLowerCase();
}
function iX1(A) {
  if (A === !1 || A == null) return A;
  return R0.isArray(A) ? A.map(iX1) : String(A);
}
function kP9(A) {
  let B = Object.create(null),
    Q = /([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g,
    D;
  while (D = Q.exec(A)) B[D[1]] = D[2];
  return B;
}
var _P9 = A => /^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(A.trim());
function Jp1(A, B, Q, D, Z) {
  if (R0.isFunction(D)) return D.call(this, B, Q);
  if (Z) B = Q;
  if (!R0.isString(B)) return;
  if (R0.isString(D)) return B.indexOf(D) !== -1;
  if (R0.isRegExp(D)) return D.test(B);
}
function xP9(A) {
  return A.trim().toLowerCase().replace(/([a-z\d])(\w*)/g, (B, Q, D) => {
    return Q.toUpperCase() + D;
  });
}
function vP9(A, B) {
  let Q = R0.toCamelCase(" " + B);
  ["get", "set", "has"].forEach(D => {
    Object.defineProperty(A, D + Q, {
      value: function (Z, G, F) {
        return this[D].call(this, B, Z, G, F);
      },
      configurable: !0
    });
  });
}
class GB1 {
  constructor(A) {
    A && this.set(A);
  }
  set(A, B, Q) {
    let D = this;
    function Z(F, I, Y) {
      let W = ZB1(I);
      if (!W) throw new Error("header name must be a non-empty string");
      let J = R0.findKey(D, W);
      if (!J || D[J] === void 0 || Y === !0 || Y === void 0 && D[J] !== !1) D[J || I] = iX1(F);
    }
    let G = (F, I) => R0.forEach(F, (Y, W) => Z(Y, W, I));
    if (R0.isPlainObject(A) || A instanceof this.constructor) G(A, B);else if (R0.isString(A) && (A = A.trim()) && !_P9(A)) G(xn0(A), B);else if (R0.isHeaders(A)) for (let [F, I] of A.entries()) Z(I, F, Q);else A != null && Z(B, A, Q);
    return this;
  }
  get(A, B) {
    if (A = ZB1(A), A) {
      let Q = R0.findKey(this, A);
      if (Q) {
        let D = this[Q];
        if (!B) return D;
        if (B === !0) return kP9(D);
        if (R0.isFunction(B)) return B.call(this, D, Q);
        if (R0.isRegExp(B)) return B.exec(D);
        throw new TypeError("parser must be boolean|regexp|function");
      }
    }
  }
  has(A, B) {
    if (A = ZB1(A), A) {
      let Q = R0.findKey(this, A);
      return !!(Q && this[Q] !== void 0 && (!B || Jp1(this, this[Q], Q, B)));
    }
    return !1;
  }
  delete(A, B) {
    let Q = this,
      D = !1;
    function Z(G) {
      if (G = ZB1(G), G) {
        let F = R0.findKey(Q, G);
        if (F && (!B || Jp1(Q, Q[F], F, B))) delete Q[F], D = !0;
      }
    }
    if (R0.isArray(A)) A.forEach(Z);else Z(A);
    return D;
  }
  clear(A) {
    let B = Object.keys(this),
      Q = B.length,
      D = !1;
    while (Q--) {
      let Z = B[Q];
      if (!A || Jp1(this, this[Z], Z, A, !0)) delete this[Z], D = !0;
    }
    return D;
  }
  normalize(A) {
    let B = this,
      Q = {};
    return R0.forEach(this, (D, Z) => {
      let G = R0.findKey(Q, Z);
      if (G) {
        B[G] = iX1(D), delete B[Z];
        return;
      }
      let F = A ? xP9(Z) : String(Z).trim();
      if (F !== Z) delete B[Z];
      B[F] = iX1(D), Q[F] = !0;
    }), this;
  }
  concat(...A) {
    return this.constructor.concat(this, ...A);
  }
  toJSON(A) {
    let B = Object.create(null);
    return R0.forEach(this, (Q, D) => {
      Q != null && Q !== !1 && (B[D] = A && R0.isArray(Q) ? Q.join(", ") : Q);
    }), B;
  }
  [Symbol.iterator]() {
    return Object.entries(this.toJSON())[Symbol.iterator]();
  }
  toString() {
    return Object.entries(this.toJSON()).map(([A, B]) => A + ": " + B).join(`
`);
  }
  get [Symbol.toStringTag]() {
    return "AxiosHeaders";
  }
  static from(A) {
    return A instanceof this ? A : new this(A);
  }
  static concat(A, ...B) {
    let Q = new this(A);
    return B.forEach(D => Q.set(D)), Q;
  }
  static accessor(A) {
    let Q = (this[vn0] = this[vn0] = {
        accessors: {}
      }).accessors,
      D = this.prototype;
    function Z(G) {
      let F = ZB1(G);
      if (!Q[F]) vP9(D, G), Q[F] = !0;
    }
    return R0.isArray(A) ? A.forEach(Z) : Z(A), this;
  }
}
var hD = GB1;
function FB1(A, B) {
  let Q = this || ml,
    D = B || Q,
    Z = hD.from(D.headers),
    G = D.data;
  return R0.forEach(A, function F(I) {
    G = I.call(Q, G, Z.normalize(), B ? B.status : void 0);
  }), Z.normalize(), G;
}
function IB1(A) {
  return !!(A && A.__CANCEL__);
}
function bn0(A, B, Q) {
  c2.call(this, A == null ? "canceled" : A, c2.ERR_CANCELED, B, Q), this.name = "CanceledError";
}
var cC = bn0;
function XN(A, B, Q) {
  let D = Q.config.validateStatus;
  if (!Q.status || !D || D(Q.status)) A(Q);else B(new c2("Request failed with status code " + Q.status, [c2.ERR_BAD_REQUEST, c2.ERR_BAD_RESPONSE][Math.floor(Q.status / 100) - 4], Q.config, Q.request, Q));
}
function Xp1(A) {
  return /^([a-z][a-z\d+\-.]*:)?\/\//i.test(A);
}
function Vp1(A, B) {
  return B ? A.replace(/\/?\/$/, "") + "/" + B.replace(/^\/+/, "") : A;
}
function kf(A, B, Q) {
  let D = !Xp1(B);
  if (A && (D || Q == !1)) return Vp1(A, B);
  return B;
}
var Ua0 = F1(fn0(), 1),
  wa0 = F1(Da0(), 1);
import oS9 from "http";
import tS9 from "https";
import eS9 from "util";
import oj from "zlib";
var ff = "1.8.4";
function KB1(A) {
  let B = /^([-+\w]{1,25})(:?\/\/|:)/.exec(A);
  return B && B[1] || "";
}
var hS9 = /^(?:([^;]+);)?(?:[^;]+;)?(base64|),([\s\S]*)$/;
function Pp1(A, B, Q) {
  let D = Q && Q.Blob || b8.classes.Blob,
    Z = KB1(A);
  if (B === void 0 && D) B = !0;
  if (Z === "data") {
    A = Z.length ? A.slice(Z.length + 1) : A;
    let G = hS9.exec(A);
    if (!G) throw new c2("Invalid URL", c2.ERR_INVALID_URL);
    let F = G[1],
      I = G[2],
      Y = G[3],
      W = Buffer.from(decodeURIComponent(Y), I ? "base64" : "utf8");
    if (B) {
      if (!D) throw new c2("Blob is not supported", c2.ERR_NOT_SUPPORT);
      return new D([W], {
        type: F
      });
    }
    return W;
  }
  throw new c2("Unsupported protocol " + Z, c2.ERR_NOT_SUPPORT);
}
import al from "stream";
import gS9 from "stream";
var Sp1 = Symbol("internals");
class Za0 extends gS9.Transform {
  constructor(A) {
    A = R0.toFlatObject(A, {
      maxRate: 0,
      chunkSize: 65536,
      minChunkSize: 100,
      timeWindow: 500,
      ticksRate: 2,
      samplesCount: 15
    }, null, (Q, D) => {
      return !R0.isUndefined(D[Q]);
    });
    super({
      readableHighWaterMark: A.chunkSize
    });
    let B = this[Sp1] = {
      timeWindow: A.timeWindow,
      chunkSize: A.chunkSize,
      maxRate: A.maxRate,
      minChunkSize: A.minChunkSize,
      bytesSeen: 0,
      isCaptured: !1,
      notifiedBytesLoaded: 0,
      ts: Date.now(),
      bytes: 0,
      onReadCallback: null
    };
    this.on("newListener", Q => {
      if (Q === "progress") {
        if (!B.isCaptured) B.isCaptured = !0;
      }
    });
  }
  _read(A) {
    let B = this[Sp1];
    if (B.onReadCallback) B.onReadCallback();
    return super._read(A);
  }
  _transform(A, B, Q) {
    let D = this[Sp1],
      Z = D.maxRate,
      G = this.readableHighWaterMark,
      F = D.timeWindow,
      I = 1000 / F,
      Y = Z / I,
      W = D.minChunkSize !== !1 ? Math.max(D.minChunkSize, Y * 0.01) : 0,
      J = (V, C) => {
        let K = Buffer.byteLength(V);
        if (D.bytesSeen += K, D.bytes += K, D.isCaptured && this.emit("progress", D.bytesSeen), this.push(V)) process.nextTick(C);else D.onReadCallback = () => {
          D.onReadCallback = null, process.nextTick(C);
        };
      },
      X = (V, C) => {
        let K = Buffer.byteLength(V),
          H = null,
          z = G,
          $,
          L = 0;
        if (Z) {
          let N = Date.now();
          if (!D.ts || (L = N - D.ts) >= F) D.ts = N, $ = Y - D.bytes, D.bytes = $ < 0 ? -$ : 0, L = 0;
          $ = Y - D.bytes;
        }
        if (Z) {
          if ($ <= 0) return setTimeout(() => {
            C(null, V);
          }, F - L);
          if ($ < z) z = $;
        }
        if (z && K > z && K - z > W) H = V.subarray(z), V = V.subarray(0, z);
        J(V, H ? () => {
          process.nextTick(C, null, H);
        } : C);
      };
    X(A, function V(C, K) {
      if (C) return Q(C);
      if (K) X(K, V);else Q(null);
    });
  }
}
var jp1 = Za0;
import { EventEmitter as Aj9 } from "events";
import mS9 from "util";
import { Readable as dS9 } from "stream";
var {
    asyncIterator: Ga0
  } = Symbol,
  uS9 = async function* (A) {
    if (A.stream) yield* A.stream();else if (A.arrayBuffer) yield await A.arrayBuffer();else if (A[Ga0]) yield* A[Ga0]();else yield A;
  },
  eX1 = uS9;
var cS9 = b8.ALPHABET.ALPHA_DIGIT + "-_",
  HB1 = typeof TextEncoder === "function" ? new TextEncoder() : new mS9.TextEncoder(),
  rj = `\r
`,
  lS9 = HB1.encode(rj),
  pS9 = 2;
class Fa0 {
  constructor(A, B) {
    let {
        escapeName: Q
      } = this.constructor,
      D = R0.isString(B),
      Z = `Content-Disposition: form-data; name="${Q(A)}"${!D && B.name ? `; filename="${Q(B.name)}"` : ""}${rj}`;
    if (D) B = HB1.encode(String(B).replace(/\r?\n|\r\n?/g, rj));else Z += `Content-Type: ${B.type || "application/octet-stream"}${rj}`;
    this.headers = HB1.encode(Z + rj), this.contentLength = D ? B.byteLength : B.size, this.size = this.headers.byteLength + this.contentLength + pS9, this.name = A, this.value = B;
  }
  async *encode() {
    yield this.headers;
    let {
      value: A
    } = this;
    if (R0.isTypedArray(A)) yield A;else yield* eX1(A);
    yield lS9;
  }
  static escapeName(A) {
    return String(A).replace(/[\r\n"]/g, B => ({
      "\r": "%0D",
      "\n": "%0A",
      '"': "%22"
    })[B]);
  }
}
var iS9 = (A, B, Q) => {
    let {
      tag: D = "form-data-boundary",
      size: Z = 25,
      boundary: G = D + "-" + b8.generateString(Z, cS9)
    } = Q || {};
    if (!R0.isFormData(A)) throw TypeError("FormData instance required");
    if (G.length < 1 || G.length > 70) throw Error("boundary must be 10-70 characters long");
    let F = HB1.encode("--" + G + rj),
      I = HB1.encode("--" + G + "--" + rj + rj),
      Y = I.byteLength,
      W = Array.from(A.entries()).map(([X, V]) => {
        let C = new Fa0(X, V);
        return Y += C.size, C;
      });
    Y += F.byteLength * W.length, Y = R0.toFiniteNumber(Y);
    let J = {
      "Content-Type": `multipart/form-data; boundary=${G}`
    };
    if (Number.isFinite(Y)) J["Content-Length"] = Y;
    return B && B(J), dS9.from(async function* () {
      for (let X of W) yield F, yield* X.encode();
      yield I;
    }());
  },
  Ia0 = iS9;
import nS9 from "stream";
class Ya0 extends nS9.Transform {
  __transform(A, B, Q) {
    this.push(A), Q();
  }
  _transform(A, B, Q) {
    if (A.length !== 0) {
      if (this._transform = this.__transform, A[0] !== 120) {
        let D = Buffer.alloc(2);
        D[0] = 120, D[1] = 156, this.push(D, B);
      }
    }
    this.__transform(A, B, Q);
  }
}
var Wa0 = Ya0;
var aS9 = (A, B) => {
    return R0.isAsyncFn(A) ? function (...Q) {
      let D = Q.pop();
      A.apply(this, Q).then(Z => {
        try {
          B ? D(null, ...B(Z)) : D(null, Z);
        } catch (G) {
          D(G);
        }
      }, D);
    } : A;
  },
  Ja0 = aS9;
function sS9(A, B) {
  A = A || 10;
  let Q = new Array(A),
    D = new Array(A),
    Z = 0,
    G = 0,
    F;
  return B = B !== void 0 ? B : 1000, function I(Y) {
    let W = Date.now(),
      J = D[G];
    if (!F) F = W;
    Q[Z] = Y, D[Z] = W;
    let X = G,
      V = 0;
    while (X !== Z) V += Q[X++], X = X % A;
    if (Z = (Z + 1) % A, Z === G) G = (G + 1) % A;
    if (W - F < B) return;
    let C = J && W - J;
    return C ? Math.round(V * 1000 / C) : void 0;
  };
}
var Xa0 = sS9;
function rS9(A, B) {
  let Q = 0,
    D = 1000 / B,
    Z,
    G,
    F = (W, J = Date.now()) => {
      if (Q = J, Z = null, G) clearTimeout(G), G = null;
      A.apply(null, W);
    };
  return [(...W) => {
    let J = Date.now(),
      X = J - Q;
    if (X >= D) F(W, J);else if (Z = W, !G) G = setTimeout(() => {
      G = null, F(Z);
    }, D - X);
  }, () => Z && F(Z)];
}
var Va0 = rS9;
var EO = (A, B, Q = 3) => {
    let D = 0,
      Z = Xa0(50, 250);
    return Va0(G => {
      let F = G.loaded,
        I = G.lengthComputable ? G.total : void 0,
        Y = F - D,
        W = Z(Y),
        J = F <= I;
      D = F;
      let X = {
        loaded: F,
        total: I,
        progress: I ? F / I : void 0,
        bytes: Y,
        rate: W ? W : void 0,
        estimated: W && I && J ? (I - F) / W : void 0,
        event: G,
        lengthComputable: I != null,
        [B ? "download" : "upload"]: !0
      };
      A(X);
    }, Q);
  },
  il = (A, B) => {
    let Q = A != null;
    return [D => B[0]({
      lengthComputable: Q,
      total: A,
      loaded: D
    }), B[1]];
  },
  nl = A => (...B) => R0.asap(() => A(...B));
var Ca0 = {
    flush: oj.constants.Z_SYNC_FLUSH,
    finishFlush: oj.constants.Z_SYNC_FLUSH
  },
  Bj9 = {
    flush: oj.constants.BROTLI_OPERATION_FLUSH,
    finishFlush: oj.constants.BROTLI_OPERATION_FLUSH
  },
  Ka0 = R0.isFunction(oj.createBrotliDecompress),
  {
    http: Qj9,
    https: Dj9
  } = wa0.default,
  Zj9 = /https:?/,
  Ha0 = b8.protocols.map(A => {
    return A + ":";
  }),
  za0 = (A, [B, Q]) => {
    return A.on("end", Q).on("error", Q), B;
  };
function Gj9(A, B) {
  if (A.beforeRedirects.proxy) A.beforeRedirects.proxy(A);
  if (A.beforeRedirects.config) A.beforeRedirects.config(A, B);
}
function $a0(A, B, Q) {
  let D = B;
  if (!D && D !== !1) {
    let Z = Ua0.default.getProxyForUrl(Q);
    if (Z) D = new URL(Z);
  }
  if (D) {
    if (D.username) D.auth = (D.username || "") + ":" + (D.password || "");
    if (D.auth) {
      if (D.auth.username || D.auth.password) D.auth = (D.auth.username || "") + ":" + (D.auth.password || "");
      let G = Buffer.from(D.auth, "utf8").toString("base64");
      A.headers["Proxy-Authorization"] = "Basic " + G;
    }
    A.headers.host = A.hostname + (A.port ? ":" + A.port : "");
    let Z = D.hostname || D.host;
    if (A.hostname = Z, A.host = Z, A.port = D.port, A.path = Q, D.protocol) A.protocol = D.protocol.includes(":") ? D.protocol : `${D.protocol}:`;
  }
  A.beforeRedirects.proxy = function Z(G) {
    $a0(G, B, G.href);
  };
}
var Fj9 = typeof process !== "undefined" && R0.kindOf(process) === "process",
  Ij9 = A => {
    return new Promise((B, Q) => {
      let D,
        Z,
        G = (Y, W) => {
          if (Z) return;
          Z = !0, D && D(Y, W);
        },
        F = Y => {
          G(Y), B(Y);
        },
        I = Y => {
          G(Y, !0), Q(Y);
        };
      A(F, I, Y => D = Y).catch(I);
    });
  },
  Yj9 = ({
    address: A,
    family: B
  }) => {
    if (!R0.isString(A)) throw TypeError("address must be a string");
    return {
      address: A,
      family: B || (A.indexOf(".") < 0 ? 6 : 4)
    };
  },
  Ea0 = (A, B) => Yj9(R0.isObject(A) ? A : {
    address: A,
    family: B
  }),
  qa0 = Fj9 && function A(B) {
    return Ij9(async function Q(D, Z, G) {
      let {
          data: F,
          lookup: I,
          family: Y
        } = B,
        {
          responseType: W,
          responseEncoding: J
        } = B,
        X = B.method.toUpperCase(),
        V,
        C = !1,
        K;
      if (I) {
        let z1 = Ja0(I, f1 => R0.isArray(f1) ? f1 : [f1]);
        I = (f1, G0, X0) => {
          z1(f1, G0, (g1, K1, Q1) => {
            if (g1) return X0(g1);
            let _1 = R0.isArray(K1) ? K1.map(q1 => Ea0(q1)) : [Ea0(K1, Q1)];
            G0.all ? X0(g1, _1) : X0(g1, _1[0].address, _1[0].family);
          });
        };
      }
      let H = new Aj9(),
        z = () => {
          if (B.cancelToken) B.cancelToken.unsubscribe($);
          if (B.signal) B.signal.removeEventListener("abort", $);
          H.removeAllListeners();
        };
      G((z1, f1) => {
        if (V = !0, f1) C = !0, z();
      });
      function $(z1) {
        H.emit("abort", !z1 || z1.type ? new cC(null, B, K) : z1);
      }
      if (H.once("abort", Z), B.cancelToken || B.signal) {
        if (B.cancelToken && B.cancelToken.subscribe($), B.signal) B.signal.aborted ? $() : B.signal.addEventListener("abort", $);
      }
      let L = kf(B.baseURL, B.url, B.allowAbsoluteUrls),
        N = new URL(L, b8.hasBrowserEnv ? b8.origin : void 0),
        O = N.protocol || Ha0[0];
      if (O === "data:") {
        let z1;
        if (X !== "GET") return XN(D, Z, {
          status: 405,
          statusText: "method not allowed",
          headers: {},
          config: B
        });
        try {
          z1 = Pp1(B.url, W === "blob", {
            Blob: B.env && B.env.Blob
          });
        } catch (f1) {
          throw c2.from(f1, c2.ERR_BAD_REQUEST, B);
        }
        if (W === "text") {
          if (z1 = z1.toString(J), !J || J === "utf8") z1 = R0.stripBOM(z1);
        } else if (W === "stream") z1 = al.Readable.from(z1);
        return XN(D, Z, {
          data: z1,
          status: 200,
          statusText: "OK",
          headers: new hD(),
          config: B
        });
      }
      if (Ha0.indexOf(O) === -1) return Z(new c2("Unsupported protocol " + O, c2.ERR_BAD_REQUEST, B));
      let R = hD.from(B.headers).normalize();
      R.set("User-Agent", "axios/" + ff, !1);
      let {
          onUploadProgress: T,
          onDownloadProgress: j
        } = B,
        f = B.maxRate,
        y = void 0,
        c = void 0;
      if (R0.isSpecCompliantForm(F)) {
        let z1 = R.getContentType(/boundary=([-_\w\d]{10,70})/i);
        F = Ia0(F, f1 => {
          R.set(f1);
        }, {
          tag: `axios-${ff}-boundary`,
          boundary: z1 && z1[1] || void 0
        });
      } else if (R0.isFormData(F) && R0.isFunction(F.getHeaders)) {
        if (R.set(F.getHeaders()), !R.hasContentLength()) try {
          let z1 = await eS9.promisify(F.getLength).call(F);
          Number.isFinite(z1) && z1 >= 0 && R.setContentLength(z1);
        } catch (z1) {}
      } else if (R0.isBlob(F) || R0.isFile(F)) F.size && R.setContentType(F.type || "application/octet-stream"), R.setContentLength(F.size || 0), F = al.Readable.from(eX1(F));else if (F && !R0.isStream(F)) {
        if (Buffer.isBuffer(F)) ;else if (R0.isArrayBuffer(F)) F = Buffer.from(new Uint8Array(F));else if (R0.isString(F)) F = Buffer.from(F, "utf-8");else return Z(new c2("Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream", c2.ERR_BAD_REQUEST, B));
        if (R.setContentLength(F.length, !1), B.maxBodyLength > -1 && F.length > B.maxBodyLength) return Z(new c2("Request body larger than maxBodyLength limit", c2.ERR_BAD_REQUEST, B));
      }
      let h = R0.toFiniteNumber(R.getContentLength());
      if (R0.isArray(f)) y = f[0], c = f[1];else y = c = f;
      if (F && (T || y)) {
        if (!R0.isStream(F)) F = al.Readable.from(F, {
          objectMode: !1
        });
        F = al.pipeline([F, new jp1({
          maxRate: R0.toFiniteNumber(y)
        })], R0.noop), T && F.on("progress", za0(F, il(h, EO(nl(T), !1, 3))));
      }
      let a = void 0;
      if (B.auth) {
        let z1 = B.auth.username || "",
          f1 = B.auth.password || "";
        a = z1 + ":" + f1;
      }
      if (!a && N.username) {
        let {
          username: z1,
          password: f1
        } = N;
        a = z1 + ":" + f1;
      }
      a && R.delete("authorization");
      let n;
      try {
        n = yf(N.pathname + N.search, B.params, B.paramsSerializer).replace(/^\?/, "");
      } catch (z1) {
        let f1 = new Error(z1.message);
        return f1.config = B, f1.url = B.url, f1.exists = !0, Z(f1);
      }
      R.set("Accept-Encoding", "gzip, compress, deflate" + (Ka0 ? ", br" : ""), !1);
      let v = {
        path: n,
        method: X,
        headers: R.toJSON(),
        agents: {
          http: B.httpAgent,
          https: B.httpsAgent
        },
        auth: a,
        protocol: O,
        family: Y,
        beforeRedirect: Gj9,
        beforeRedirects: {}
      };
      if (!R0.isUndefined(I) && (v.lookup = I), B.socketPath) v.socketPath = B.socketPath;else v.hostname = N.hostname.startsWith("[") ? N.hostname.slice(1, -1) : N.hostname, v.port = N.port, $a0(v, B.proxy, O + "//" + N.hostname + (N.port ? ":" + N.port : "") + v.path);
      let t,
        W1 = Zj9.test(v.protocol);
      if (v.agent = W1 ? B.httpsAgent : B.httpAgent, B.transport) t = B.transport;else if (B.maxRedirects === 0) t = W1 ? tS9 : oS9;else {
        if (B.maxRedirects) v.maxRedirects = B.maxRedirects;
        if (B.beforeRedirect) v.beforeRedirects.config = B.beforeRedirect;
        t = W1 ? Dj9 : Qj9;
      }
      if (B.maxBodyLength > -1) v.maxBodyLength = B.maxBodyLength;else v.maxBodyLength = 1 / 0;
      if (B.insecureHTTPParser) v.insecureHTTPParser = B.insecureHTTPParser;
      if (K = t.request(v, function z1(f1) {
        if (K.destroyed) return;
        let G0 = [f1],
          X0 = +f1.headers["content-length"];
        if (j || c) {
          let q1 = new jp1({
            maxRate: R0.toFiniteNumber(c)
          });
          j && q1.on("progress", za0(q1, il(X0, EO(nl(j), !0, 3)))), G0.push(q1);
        }
        let g1 = f1,
          K1 = f1.req || K;
        if (B.decompress !== !1 && f1.headers["content-encoding"]) {
          if (X === "HEAD" || f1.statusCode === 204) delete f1.headers["content-encoding"];
          switch ((f1.headers["content-encoding"] || "").toLowerCase()) {
            case "gzip":
            case "x-gzip":
            case "compress":
            case "x-compress":
              G0.push(oj.createUnzip(Ca0)), delete f1.headers["content-encoding"];
              break;
            case "deflate":
              G0.push(new Wa0()), G0.push(oj.createUnzip(Ca0)), delete f1.headers["content-encoding"];
              break;
            case "br":
              if (Ka0) G0.push(oj.createBrotliDecompress(Bj9)), delete f1.headers["content-encoding"];
          }
        }
        g1 = G0.length > 1 ? al.pipeline(G0, R0.noop) : G0[0];
        let Q1 = al.finished(g1, () => {
            Q1(), z();
          }),
          _1 = {
            status: f1.statusCode,
            statusText: f1.statusMessage,
            headers: new hD(f1.headers),
            config: B,
            request: K1
          };
        if (W === "stream") _1.data = g1, XN(D, Z, _1);else {
          let q1 = [],
            B0 = 0;
          g1.on("data", function K0(s1) {
            if (q1.push(s1), B0 += s1.length, B.maxContentLength > -1 && B0 > B.maxContentLength) C = !0, g1.destroy(), Z(new c2("maxContentLength size of " + B.maxContentLength + " exceeded", c2.ERR_BAD_RESPONSE, B, K1));
          }), g1.on("aborted", function K0() {
            if (C) return;
            let s1 = new c2("stream has been aborted", c2.ERR_BAD_RESPONSE, B, K1);
            g1.destroy(s1), Z(s1);
          }), g1.on("error", function K0(s1) {
            if (K.destroyed) return;
            Z(c2.from(s1, null, B, K1));
          }), g1.on("end", function K0() {
            try {
              let s1 = q1.length === 1 ? q1[0] : Buffer.concat(q1);
              if (W !== "arraybuffer") {
                if (s1 = s1.toString(J), !J || J === "utf8") s1 = R0.stripBOM(s1);
              }
              _1.data = s1;
            } catch (s1) {
              return Z(c2.from(s1, null, B, _1.request, _1));
            }
            XN(D, Z, _1);
          });
        }
        H.once("abort", q1 => {
          if (!g1.destroyed) g1.emit("error", q1), g1.destroy();
        });
      }), H.once("abort", z1 => {
        Z(z1), K.destroy(z1);
      }), K.on("error", function z1(f1) {
        Z(c2.from(f1, null, B, K));
      }), K.on("socket", function z1(f1) {
        f1.setKeepAlive(!0, 60000);
      }), B.timeout) {
        let z1 = parseInt(B.timeout, 10);
        if (Number.isNaN(z1)) {
          Z(new c2("error trying to parse `config.timeout` to int", c2.ERR_BAD_OPTION_VALUE, B, K));
          return;
        }
        K.setTimeout(z1, function f1() {
          if (V) return;
          let G0 = B.timeout ? "timeout of " + B.timeout + "ms exceeded" : "timeout exceeded",
            X0 = B.transitional || ul;
          if (B.timeoutErrorMessage) G0 = B.timeoutErrorMessage;
          Z(new c2(G0, X0.clarifyTimeoutError ? c2.ETIMEDOUT : c2.ECONNABORTED, B, K)), $();
        });
      }
      if (R0.isStream(F)) {
        let z1 = !1,
          f1 = !1;
        F.on("end", () => {
          z1 = !0;
        }), F.once("error", G0 => {
          f1 = !0, K.destroy(G0);
        }), F.on("close", () => {
          if (!z1 && !f1) $(new cC("Request stream has been aborted", B, K));
        }), F.pipe(K);
      } else K.end(F);
    });
  };
var Na0 = b8.hasStandardBrowserEnv ? ((A, B) => Q => {
  return Q = new URL(Q, b8.origin), A.protocol === Q.protocol && A.host === Q.host && (B || A.port === Q.port);
})(new URL(b8.origin), b8.navigator && /(msie|trident)/i.test(b8.navigator.userAgent)) : () => !0;
var La0 = b8.hasStandardBrowserEnv ? {
  write(A, B, Q, D, Z, G) {
    let F = [A + "=" + encodeURIComponent(B)];
    R0.isNumber(Q) && F.push("expires=" + new Date(Q).toGMTString()), R0.isString(D) && F.push("path=" + D), R0.isString(Z) && F.push("domain=" + Z), G === !0 && F.push("secure"), document.cookie = F.join("; ");
  },
  read(A) {
    let B = document.cookie.match(new RegExp("(^|;\\s*)(" + A + ")=([^;]*)"));
    return B ? decodeURIComponent(B[3]) : null;
  },
  remove(A) {
    this.write(A, "", Date.now() - 86400000);
  }
} : {
  write() {},
  read() {
    return null;
  },
  remove() {}
};
var Ma0 = A => A instanceof hD ? {
  ...A
} : A;
function Zw(A, B) {
  B = B || {};
  let Q = {};
  function D(W, J, X, V) {
    if (R0.isPlainObject(W) && R0.isPlainObject(J)) return R0.merge.call({
      caseless: V
    }, W, J);else if (R0.isPlainObject(J)) return R0.merge({}, J);else if (R0.isArray(J)) return J.slice();
    return J;
  }
  function Z(W, J, X, V) {
    if (!R0.isUndefined(J)) return D(W, J, X, V);else if (!R0.isUndefined(W)) return D(void 0, W, X, V);
  }
  function G(W, J) {
    if (!R0.isUndefined(J)) return D(void 0, J);
  }
  function F(W, J) {
    if (!R0.isUndefined(J)) return D(void 0, J);else if (!R0.isUndefined(W)) return D(void 0, W);
  }
  function I(W, J, X) {
    if (X in B) return D(W, J);else if (X in A) return D(void 0, W);
  }
  let Y = {
    url: G,
    method: G,
    data: G,
    baseURL: F,
    transformRequest: F,
    transformResponse: F,
    paramsSerializer: F,
    timeout: F,
    timeoutMessage: F,
    withCredentials: F,
    withXSRFToken: F,
    adapter: F,
    responseType: F,
    xsrfCookieName: F,
    xsrfHeaderName: F,
    onUploadProgress: F,
    onDownloadProgress: F,
    decompress: F,
    maxContentLength: F,
    maxBodyLength: F,
    beforeRedirect: F,
    transport: F,
    httpAgent: F,
    httpsAgent: F,
    cancelToken: F,
    socketPath: F,
    responseEncoding: F,
    validateStatus: I,
    headers: (W, J, X) => Z(Ma0(W), Ma0(J), X, !0)
  };
  return R0.forEach(Object.keys(Object.assign({}, A, B)), function W(J) {
    let X = Y[J] || Z,
      V = X(A[J], B[J], J);
    R0.isUndefined(V) && X !== I || (Q[J] = V);
  }), Q;
}
var AV1 = A => {
  let B = Zw({}, A),
    {
      data: Q,
      withXSRFToken: D,
      xsrfHeaderName: Z,
      xsrfCookieName: G,
      headers: F,
      auth: I
    } = B;
  if (B.headers = F = hD.from(F), B.url = yf(kf(B.baseURL, B.url, B.allowAbsoluteUrls), A.params, A.paramsSerializer), I) F.set("Authorization", "Basic " + btoa((I.username || "") + ":" + (I.password ? unescape(encodeURIComponent(I.password)) : "")));
  let Y;
  if (R0.isFormData(Q)) {
    if (b8.hasStandardBrowserEnv || b8.hasStandardBrowserWebWorkerEnv) F.setContentType(void 0);else if ((Y = F.getContentType()) !== !1) {
      let [W, ...J] = Y ? Y.split(";").map(X => X.trim()).filter(Boolean) : [];
      F.setContentType([W || "multipart/form-data", ...J].join("; "));
    }
  }
  if (b8.hasStandardBrowserEnv) {
    if (D && R0.isFunction(D) && (D = D(B)), D || D !== !1 && Na0(B.url)) {
      let W = Z && G && La0.read(G);
      if (W) F.set(Z, W);
    }
  }
  return B;
};
var Wj9 = typeof XMLHttpRequest !== "undefined",
  Ra0 = Wj9 && function (A) {
    return new Promise(function B(Q, D) {
      let Z = AV1(A),
        G = Z.data,
        F = hD.from(Z.headers).normalize(),
        {
          responseType: I,
          onUploadProgress: Y,
          onDownloadProgress: W
        } = Z,
        J,
        X,
        V,
        C,
        K;
      function H() {
        C && C(), K && K(), Z.cancelToken && Z.cancelToken.unsubscribe(J), Z.signal && Z.signal.removeEventListener("abort", J);
      }
      let z = new XMLHttpRequest();
      z.open(Z.method.toUpperCase(), Z.url, !0), z.timeout = Z.timeout;
      function $() {
        if (!z) return;
        let N = hD.from("getAllResponseHeaders" in z && z.getAllResponseHeaders()),
          R = {
            data: !I || I === "text" || I === "json" ? z.responseText : z.response,
            status: z.status,
            statusText: z.statusText,
            headers: N,
            config: A,
            request: z
          };
        XN(function T(j) {
          Q(j), H();
        }, function T(j) {
          D(j), H();
        }, R), z = null;
      }
      if ("onloadend" in z) z.onloadend = $;else z.onreadystatechange = function N() {
        if (!z || z.readyState !== 4) return;
        if (z.status === 0 && !(z.responseURL && z.responseURL.indexOf("file:") === 0)) return;
        setTimeout($);
      };
      if (z.onabort = function N() {
        if (!z) return;
        D(new c2("Request aborted", c2.ECONNABORTED, A, z)), z = null;
      }, z.onerror = function N() {
        D(new c2("Network Error", c2.ERR_NETWORK, A, z)), z = null;
      }, z.ontimeout = function N() {
        let O = Z.timeout ? "timeout of " + Z.timeout + "ms exceeded" : "timeout exceeded",
          R = Z.transitional || ul;
        if (Z.timeoutErrorMessage) O = Z.timeoutErrorMessage;
        D(new c2(O, R.clarifyTimeoutError ? c2.ETIMEDOUT : c2.ECONNABORTED, A, z)), z = null;
      }, G === void 0 && F.setContentType(null), "setRequestHeader" in z) R0.forEach(F.toJSON(), function N(O, R) {
        z.setRequestHeader(R, O);
      });
      if (!R0.isUndefined(Z.withCredentials)) z.withCredentials = !!Z.withCredentials;
      if (I && I !== "json") z.responseType = Z.responseType;
      if (W) [V, K] = EO(W, !0), z.addEventListener("progress", V);
      if (Y && z.upload) [X, C] = EO(Y), z.upload.addEventListener("progress", X), z.upload.addEventListener("loadend", C);
      if (Z.cancelToken || Z.signal) {
        if (J = N => {
          if (!z) return;
          D(!N || N.type ? new cC(null, A, z) : N), z.abort(), z = null;
        }, Z.cancelToken && Z.cancelToken.subscribe(J), Z.signal) Z.signal.aborted ? J() : Z.signal.addEventListener("abort", J);
      }
      let L = KB1(Z.url);
      if (L && b8.protocols.indexOf(L) === -1) {
        D(new c2("Unsupported protocol " + L + ":", c2.ERR_BAD_REQUEST, A));
        return;
      }
      z.send(G || null);
    });
  };
var Jj9 = (A, B) => {
    let {
      length: Q
    } = A = A ? A.filter(Boolean) : [];
    if (B || Q) {
      let D = new AbortController(),
        Z,
        G = function (W) {
          if (!Z) {
            Z = !0, I();
            let J = W instanceof Error ? W : this.reason;
            D.abort(J instanceof c2 ? J : new cC(J instanceof Error ? J.message : J));
          }
        },
        F = B && setTimeout(() => {
          F = null, G(new c2(`timeout ${B} of ms exceeded`, c2.ETIMEDOUT));
        }, B),
        I = () => {
          if (A) F && clearTimeout(F), F = null, A.forEach(W => {
            W.unsubscribe ? W.unsubscribe(G) : W.removeEventListener("abort", G);
          }), A = null;
        };
      A.forEach(W => W.addEventListener("abort", G));
      let {
        signal: Y
      } = D;
      return Y.unsubscribe = () => R0.asap(I), Y;
    }
  },
  Oa0 = Jj9;
var Xj9 = function* (A, B) {
    let Q = A.byteLength;
    if (!B || Q < B) {
      yield A;
      return;
    }
    let D = 0,
      Z;
    while (D < Q) Z = D + B, yield A.slice(D, Z), D = Z;
  },
  Vj9 = async function* (A, B) {
    for await (let Q of Cj9(A)) yield* Xj9(Q, B);
  },
  Cj9 = async function* (A) {
    if (A[Symbol.asyncIterator]) {
      yield* A;
      return;
    }
    let B = A.getReader();
    try {
      for (;;) {
        let {
          done: Q,
          value: D
        } = await B.read();
        if (Q) break;
        yield D;
      }
    } finally {
      await B.cancel();
    }
  },
  yp1 = (A, B, Q, D) => {
    let Z = Vj9(A, B),
      G = 0,
      F,
      I = Y => {
        if (!F) F = !0, D && D(Y);
      };
    return new ReadableStream({
      async pull(Y) {
        try {
          let {
            done: W,
            value: J
          } = await Z.next();
          if (W) {
            I(), Y.close();
            return;
          }
          let X = J.byteLength;
          if (Q) {
            let V = G += X;
            Q(V);
          }
          Y.enqueue(new Uint8Array(J));
        } catch (W) {
          throw I(W), W;
        }
      },
      cancel(Y) {
        return I(Y), Z.return();
      }
    }, {
      highWaterMark: 2
    });
  };
var QV1 = typeof fetch === "function" && typeof Request === "function" && typeof Response === "function",
  Pa0 = QV1 && typeof ReadableStream === "function",
  Kj9 = QV1 && (typeof TextEncoder === "function" ? (A => B => A.encode(B))(new TextEncoder()) : async A => new Uint8Array(await new Response(A).arrayBuffer())),
  Sa0 = (A, ...B) => {
    try {
      return !!A(...B);
    } catch (Q) {
      return !1;
    }
  },
  Hj9 = Pa0 && Sa0(() => {
    let A = !1,
      B = new Request(b8.origin, {
        body: new ReadableStream(),
        method: "POST",
        get duplex() {
          return A = !0, "half";
        }
      }).headers.has("Content-Type");
    return A && !B;
  }),
  Ta0 = 65536,
  kp1 = Pa0 && Sa0(() => R0.isReadableStream(new Response("").body)),
  BV1 = {
    stream: kp1 && (A => A.body)
  };
var zj9 = async A => {
    if (A == null) return 0;
    if (R0.isBlob(A)) return A.size;
    if (R0.isSpecCompliantForm(A)) return (await new Request(b8.origin, {
      method: "POST",
      body: A
    }).arrayBuffer()).byteLength;
    if (R0.isArrayBufferView(A) || R0.isArrayBuffer(A)) return A.byteLength;
    if (R0.isURLSearchParams(A)) A = A + "";
    if (R0.isString(A)) return (await Kj9(A)).byteLength;
  },
  Ej9 = async (A, B) => {
    let Q = R0.toFiniteNumber(A.getContentLength());
    return Q == null ? zj9(B) : Q;
  },
  ja0 = QV1 && (async A => {
    let {
      url: B,
      method: Q,
      data: D,
      signal: Z,
      cancelToken: G,
      timeout: F,
      onDownloadProgress: I,
      onUploadProgress: Y,
      responseType: W,
      headers: J,
      withCredentials: X = "same-origin",
      fetchOptions: V
    } = AV1(A);
    W = W ? (W + "").toLowerCase() : "text";
    let C = Oa0([Z, G && G.toAbortSignal()], F),
      K,
      H = C && C.unsubscribe && (() => {
        C.unsubscribe();
      }),
      z;
    try {
      if (Y && Hj9 && Q !== "get" && Q !== "head" && (z = await Ej9(J, D)) !== 0) {
        let R = new Request(B, {
            method: "POST",
            body: D,
            duplex: "half"
          }),
          T;
        if (R0.isFormData(D) && (T = R.headers.get("content-type"))) J.setContentType(T);
        if (R.body) {
          let [j, f] = il(z, EO(nl(Y)));
          D = yp1(R.body, Ta0, j, f);
        }
      }
      if (!R0.isString(X)) X = X ? "include" : "omit";
      let $ = "credentials" in Request.prototype;
      K = new Request(B, {
        ...V,
        signal: C,
        method: Q.toUpperCase(),
        headers: J.normalize().toJSON(),
        body: D,
        duplex: "half",
        credentials: $ ? X : void 0
      });
      let L = await fetch(K),
        N = kp1 && (W === "stream" || W === "response");
      if (kp1 && (I || N && H)) {
        let R = {};
        ["status", "statusText", "headers"].forEach(y => {
          R[y] = L[y];
        });
        let T = R0.toFiniteNumber(L.headers.get("content-length")),
          [j, f] = I && il(T, EO(nl(I), !0)) || [];
        L = new Response(yp1(L.body, Ta0, j, () => {
          f && f(), H && H();
        }), R);
      }
      W = W || "text";
      let O = await BV1[R0.findKey(BV1, W) || "text"](L, A);
      return !N && H && H(), await new Promise((R, T) => {
        XN(R, T, {
          data: O,
          headers: hD.from(L.headers),
          status: L.status,
          statusText: L.statusText,
          config: A,
          request: K
        });
      });
    } catch ($) {
      if (H && H(), $ && $.name === "TypeError" && /fetch/i.test($.message)) throw Object.assign(new c2("Network Error", c2.ERR_NETWORK, A, K), {
        cause: $.cause || $
      });
      throw c2.from($, $ && $.code, A, K);
    }
  });
var _p1 = {
  http: qa0,
  xhr: Ra0,
  fetch: ja0
};
var ya0 = A => `- ${A}`,
  Uj9 = A => R0.isFunction(A) || A === null || A === !1,
  DV1 = {
    getAdapter: A => {
      A = R0.isArray(A) ? A : [A];
      let {
          length: B
        } = A,
        Q,
        D,
        Z = {};
      for (let G = 0; G < B; G++) {
        Q = A[G];
        let F;
        if (D = Q, !Uj9(Q)) {
          if (D = _p1[(F = String(Q)).toLowerCase()], D === void 0) throw new c2(`Unknown adapter '${F}'`);
        }
        if (D) break;
        Z[F || "#" + G] = D;
      }
      if (!D) {
        let G = Object.entries(Z).map(([I, Y]) => `adapter ${I} ` + (Y === !1 ? "is not supported by the environment" : "is not available in the build")),
          F = B ? G.length > 1 ? `since :
` + G.map(ya0).join(`
`) : " " + ya0(G[0]) : "as no adapter specified";
        throw new c2("There is no suitable adapter to dispatch the request " + F, "ERR_NOT_SUPPORT");
      }
      return D;
    },
    adapters: _p1
  };
function xp1(A) {
  if (A.cancelToken) A.cancelToken.throwIfRequested();
  if (A.signal && A.signal.aborted) throw new cC(null, A);
}
function ZV1(A) {
  if (xp1(A), A.headers = hD.from(A.headers), A.data = FB1.call(A, A.transformRequest), ["post", "put", "patch"].indexOf(A.method) !== -1) A.headers.setContentType("application/x-www-form-urlencoded", !1);
  return DV1.getAdapter(A.adapter || ml.adapter)(A).then(function Q(D) {
    return xp1(A), D.data = FB1.call(A, A.transformResponse, D), D.headers = hD.from(D.headers), D;
  }, function Q(D) {
    if (!IB1(D)) {
      if (xp1(A), D && D.response) D.response.data = FB1.call(A, A.transformResponse, D.response), D.response.headers = hD.from(D.response.headers);
    }
    return Promise.reject(D);
  });
}
var GV1 = {};
var ka0 = {};
GV1.transitional = function A(B, Q, D) {
  function Z(G, F) {
    return "[Axios v" + ff + "] Transitional option '" + G + "'" + F + (D ? ". " + D : "");
  }
  return (G, F, I) => {
    if (B === !1) throw new c2(Z(F, " has been removed" + (Q ? " in " + Q : "")), c2.ERR_DEPRECATED);
    if (Q && !ka0[F]) ka0[F] = !0, console.warn(Z(F, " has been deprecated since v" + Q + " and will be removed in the near future"));
    return B ? B(G, F, I) : !0;
  };
};
GV1.spelling = function A(B) {
  return (Q, D) => {
    return console.warn(`${D} is likely a misspelling of ${B}`), !0;
  };
};
function wj9(A, B, Q) {
  if (typeof A !== "object") throw new c2("options must be an object", c2.ERR_BAD_OPTION_VALUE);
  let D = Object.keys(A),
    Z = D.length;
  while (Z-- > 0) {
    let G = D[Z],
      F = B[G];
    if (F) {
      let I = A[G],
        Y = I === void 0 || F(I, G, A);
      if (Y !== !0) throw new c2("option " + G + " must be " + Y, c2.ERR_BAD_OPTION_VALUE);
      continue;
    }
    if (Q !== !0) throw new c2("Unknown option " + G, c2.ERR_BAD_OPTION);
  }
}
var zB1 = {
  assertOptions: wj9,
  validators: GV1
};
var VN = zB1.validators;
class EB1 {
  constructor(A) {
    this.defaults = A, this.interceptors = {
      request: new Dp1(),
      response: new Dp1()
    };
  }
  async request(A, B) {
    try {
      return await this._request(A, B);
    } catch (Q) {
      if (Q instanceof Error) {
        let D = {};
        Error.captureStackTrace ? Error.captureStackTrace(D) : D = new Error();
        let Z = D.stack ? D.stack.replace(/^.+\n/, "") : "";
        try {
          if (!Q.stack) Q.stack = Z;else if (Z && !String(Q.stack).endsWith(Z.replace(/^.+\n.+\n/, ""))) Q.stack += `
` + Z;
        } catch (G) {}
      }
      throw Q;
    }
  }
  _request(A, B) {
    if (typeof A === "string") B = B || {}, B.url = A;else B = A || {};
    B = Zw(this.defaults, B);
    let {
      transitional: Q,
      paramsSerializer: D,
      headers: Z
    } = B;
    if (Q !== void 0) zB1.assertOptions(Q, {
      silentJSONParsing: VN.transitional(VN.boolean),
      forcedJSONParsing: VN.transitional(VN.boolean),
      clarifyTimeoutError: VN.transitional(VN.boolean)
    }, !1);
    if (D != null) if (R0.isFunction(D)) B.paramsSerializer = {
      serialize: D
    };else zB1.assertOptions(D, {
      encode: VN.function,
      serialize: VN.function
    }, !0);
    if (B.allowAbsoluteUrls !== void 0) ;else if (this.defaults.allowAbsoluteUrls !== void 0) B.allowAbsoluteUrls = this.defaults.allowAbsoluteUrls;else B.allowAbsoluteUrls = !0;
    zB1.assertOptions(B, {
      baseUrl: VN.spelling("baseURL"),
      withXsrfToken: VN.spelling("withXSRFToken")
    }, !0), B.method = (B.method || this.defaults.method || "get").toLowerCase();
    let G = Z && R0.merge(Z.common, Z[B.method]);
    Z && R0.forEach(["delete", "get", "head", "post", "put", "patch", "common"], C => {
      delete Z[C];
    }), B.headers = hD.concat(G, Z);
    let F = [],
      I = !0;
    this.interceptors.request.forEach(function C(K) {
      if (typeof K.runWhen === "function" && K.runWhen(B) === !1) return;
      I = I && K.synchronous, F.unshift(K.fulfilled, K.rejected);
    });
    let Y = [];
    this.interceptors.response.forEach(function C(K) {
      Y.push(K.fulfilled, K.rejected);
    });
    let W,
      J = 0,
      X;
    if (!I) {
      let C = [ZV1.bind(this), void 0];
      C.unshift.apply(C, F), C.push.apply(C, Y), X = C.length, W = Promise.resolve(B);
      while (J < X) W = W.then(C[J++], C[J++]);
      return W;
    }
    X = F.length;
    let V = B;
    J = 0;
    while (J < X) {
      let C = F[J++],
        K = F[J++];
      try {
        V = C(V);
      } catch (H) {
        K.call(this, H);
        break;
      }
    }
    try {
      W = ZV1.call(this, V);
    } catch (C) {
      return Promise.reject(C);
    }
    J = 0, X = Y.length;
    while (J < X) W = W.then(Y[J++], Y[J++]);
    return W;
  }
  getUri(A) {
    A = Zw(this.defaults, A);
    let B = kf(A.baseURL, A.url, A.allowAbsoluteUrls);
    return yf(B, A.params, A.paramsSerializer);
  }
}
var UB1 = EB1;
class vp1 {
  constructor(A) {
    if (typeof A !== "function") throw new TypeError("executor must be a function.");
    let B;
    this.promise = new Promise(function D(Z) {
      B = Z;
    });
    let Q = this;
    this.promise.then(D => {
      if (!Q._listeners) return;
      let Z = Q._listeners.length;
      while (Z-- > 0) Q._listeners[Z](D);
      Q._listeners = null;
    }), this.promise.then = D => {
      let Z,
        G = new Promise(F => {
          Q.subscribe(F), Z = F;
        }).then(D);
      return G.cancel = function F() {
        Q.unsubscribe(Z);
      }, G;
    }, A(function D(Z, G, F) {
      if (Q.reason) return;
      Q.reason = new cC(Z, G, F), B(Q.reason);
    });
  }
  throwIfRequested() {
    if (this.reason) throw this.reason;
  }
  subscribe(A) {
    if (this.reason) {
      A(this.reason);
      return;
    }
    if (this._listeners) this._listeners.push(A);else this._listeners = [A];
  }
  unsubscribe(A) {
    if (!this._listeners) return;
    let B = this._listeners.indexOf(A);
    if (B !== -1) this._listeners.splice(B, 1);
  }
  toAbortSignal() {
    let A = new AbortController(),
      B = Q => {
        A.abort(Q);
      };
    return this.subscribe(B), A.signal.unsubscribe = () => this.unsubscribe(B), A.signal;
  }
  static source() {
    let A;
    return {
      token: new vp1(function Q(D) {
        A = D;
      }),
      cancel: A
    };
  }
}
var _a0 = vp1;
function bp1(A) {
  return function B(Q) {
    return A.apply(null, Q);
  };
}
function fp1(A) {
  return R0.isObject(A) && A.isAxiosError === !0;
}
var hp1 = {
  Continue: 100,
  SwitchingProtocols: 101,
  Processing: 102,
  EarlyHints: 103,
  Ok: 200,
  Created: 201,
  Accepted: 202,
  NonAuthoritativeInformation: 203,
  NoContent: 204,
  ResetContent: 205,
  PartialContent: 206,
  MultiStatus: 207,
  AlreadyReported: 208,
  ImUsed: 226,
  MultipleChoices: 300,
  MovedPermanently: 301,
  Found: 302,
  SeeOther: 303,
  NotModified: 304,
  UseProxy: 305,
  Unused: 306,
  TemporaryRedirect: 307,
  PermanentRedirect: 308,
  BadRequest: 400,
  Unauthorized: 401,
  PaymentRequired: 402,
  Forbidden: 403,
  NotFound: 404,
  MethodNotAllowed: 405,
  NotAcceptable: 406,
  ProxyAuthenticationRequired: 407,
  RequestTimeout: 408,
  Conflict: 409,
  Gone: 410,
  LengthRequired: 411,
  PreconditionFailed: 412,
  PayloadTooLarge: 413,
  UriTooLong: 414,
  UnsupportedMediaType: 415,
  RangeNotSatisfiable: 416,
  ExpectationFailed: 417,
  ImATeapot: 418,
  MisdirectedRequest: 421,
  UnprocessableEntity: 422,
  Locked: 423,
  FailedDependency: 424,
  TooEarly: 425,
  UpgradeRequired: 426,
  PreconditionRequired: 428,
  TooManyRequests: 429,
  RequestHeaderFieldsTooLarge: 431,
  UnavailableForLegalReasons: 451,
  InternalServerError: 500,
  NotImplemented: 501,
  BadGateway: 502,
  ServiceUnavailable: 503,
  GatewayTimeout: 504,
  HttpVersionNotSupported: 505,
  VariantAlsoNegotiates: 506,
  InsufficientStorage: 507,
  LoopDetected: 508,
  NotExtended: 510,
  NetworkAuthenticationRequired: 511
};
var xa0 = hp1;
function va0(A) {
  let B = new UB1(A),
    Q = o21(UB1.prototype.request, B);
  return R0.extend(Q, UB1.prototype, B, {
    allOwnKeys: !0
  }), R0.extend(Q, B, null, {
    allOwnKeys: !0
  }), Q.create = function D(Z) {
    return va0(Zw(A, Z));
  }, Q;
}
var xG = va0(ml);
xG.Axios = UB1;
xG.CanceledError = cC;
xG.CancelToken = _a0;
xG.isCancel = IB1;
xG.VERSION = ff;
xG.toFormData = sj;
xG.AxiosError = c2;
xG.Cancel = xG.CanceledError;
xG.all = function A(B) {
  return Promise.all(B);
};
xG.spread = bp1;
xG.isAxiosError = fp1;
xG.mergeConfig = Zw;
xG.AxiosHeaders = hD;
xG.formToJSON = A => pX1(R0.isHTMLForm(A) ? new FormData(A) : A);
xG.getAdapter = DV1.getAdapter;
xG.HttpStatusCode = xa0;
xG.default = xG;
var v9 = xG;
var mM1 = F1(Eo0(), 1);
import { createHash as gl4 } from "crypto";
import * as PB1 from "path";
import { existsSync as qo0, mkdirSync as v_9, readdirSync as b_9, readFileSync as f_9, writeFileSync as h_9, unlinkSync as g_9 } from "fs";
import { join as __9 } from "path";
import { homedir as x_9 } from "os";
function YQ() {
  return process.env.CLAUDE_CONFIG_DIR ?? __9(x_9(), ".claude");
}
function gQ(A) {
  if (!A) return !1;
  let B = A.toLowerCase().trim();
  return ["1", "true", "yes", "on"].includes(B);
}
function Jp() {
  return process.env.AWS_REGION || process.env.AWS_DEFAULT_REGION || "us-east-1";
}
function Xp() {
  return PB1.join(YQ(), "statsig");
}
class Li1 {
  cache = new Map();
  ready = !1;
  constructor() {
    try {
      if (!qo0(Xp())) v_9(Xp(), {
        recursive: !0
      });
      let A = b_9(Xp());
      for (let B of A) {
        let Q = decodeURIComponent(B),
          D = f_9(PB1.join(Xp(), B), "utf8");
        this.cache.set(Q, D);
      }
      this.ready = !0;
    } catch (A) {
      T1(A), this.ready = !0;
    }
  }
  isReady() {
    return this.ready;
  }
  isReadyResolver() {
    return this.ready ? Promise.resolve() : null;
  }
  getProviderName() {
    return "FileSystemStorageProvider";
  }
  getItem(A) {
    return this.cache.get(A) ?? null;
  }
  setItem(A, B) {
    this.cache.set(A, B);
    try {
      let Q = encodeURIComponent(A);
      h_9(PB1.join(Xp(), Q), B, "utf8");
    } catch (Q) {
      T1(Q);
    }
  }
  removeItem(A) {
    this.cache.delete(A);
    let B = encodeURIComponent(A),
      Q = PB1.join(Xp(), B);
    if (!qo0(Q)) return;
    try {
      g_9(Q);
    } catch (D) {
      T1(D);
    }
  }
  getAllKeys() {
    return Array.from(this.cache.keys());
  }
}
var No0 = "claude-code-20250219",
  fV1 = "interleaved-thinking-2025-05-14",
  Lo0 = "fine-grained-tool-streaming-2025-05-14";
import * as LQ from "fs";
import { stat as u_9 } from "fs/promises";
function WV(A, B) {
  if (!A.existsSync(B)) return {
    resolvedPath: B,
    isSymlink: !1
  };
  try {
    let Q = A.realpathSync(B);
    return {
      resolvedPath: Q,
      isSymlink: Q !== B
    };
  } catch (Q) {
    return {
      resolvedPath: B,
      isSymlink: !1
    };
  }
}
var m_9 = {
    accessSync(A, B) {
      LQ.accessSync(A, B);
    },
    cwd() {
      return process.cwd();
    },
    chmodSync(A, B) {
      LQ.chmodSync(A, B);
    },
    existsSync(A) {
      return LQ.existsSync(A);
    },
    async stat(A) {
      return u_9(A);
    },
    statSync(A) {
      return LQ.statSync(A);
    },
    readFileSync(A, B) {
      return LQ.readFileSync(A, {
        encoding: B.encoding
      });
    },
    readFileBytesSync(A) {
      return LQ.readFileSync(A);
    },
    readSync(A, B) {
      let Q = void 0;
      try {
        Q = LQ.openSync(A, "r");
        let D = Buffer.alloc(B.length),
          Z = LQ.readSync(Q, D, 0, B.length, 0);
        return {
          buffer: D,
          bytesRead: Z
        };
      } finally {
        if (Q) LQ.closeSync(Q);
      }
    },
    writeFileSync(A, B, Q) {
      if (!Q.flush) {
        LQ.writeFileSync(A, B, {
          encoding: Q.encoding
        });
        return;
      }
      let D;
      try {
        D = LQ.openSync(A, "w"), LQ.writeFileSync(D, B, {
          encoding: Q.encoding
        }), LQ.fsyncSync(D);
      } finally {
        if (D) LQ.closeSync(D);
      }
    },
    appendFileSync(A, B) {
      LQ.appendFileSync(A, B);
    },
    copyFileSync(A, B) {
      LQ.copyFileSync(A, B);
    },
    unlinkSync(A) {
      LQ.unlinkSync(A);
    },
    renameSync(A, B) {
      LQ.renameSync(A, B);
    },
    symlinkSync(A, B) {
      LQ.symlinkSync(A, B);
    },
    readlinkSync(A) {
      return LQ.readlinkSync(A);
    },
    realpathSync(A) {
      return LQ.realpathSync(A);
    },
    mkdirSync(A) {
      if (!LQ.existsSync(A)) LQ.mkdirSync(A, {
        recursive: !0
      });
    },
    readdirSync(A) {
      return LQ.readdirSync(A, {
        withFileTypes: !0
      });
    },
    readdirStringSync(A) {
      return LQ.readdirSync(A);
    },
    isDirEmptySync(A) {
      return this.readdirSync(A).length === 0;
    },
    rmdirSync(A) {
      LQ.rmdirSync(A);
    },
    rmSync(A, B) {
      LQ.rmSync(A, B);
    }
  },
  d_9 = m_9;
function x1() {
  return d_9;
}
var Mi1 = ["macos", "wsl"],
  P9 = SA(() => {
    try {
      if (process.platform === "darwin") return "macos";
      if (process.platform === "win32") return "windows";
      if (process.platform === "linux") {
        try {
          let A = x1().readFileSync("/proc/version", {
            encoding: "utf8"
          });
          if (A.toLowerCase().includes("microsoft") || A.toLowerCase().includes("wsl")) return "wsl";
        } catch (A) {
          T1(A instanceof Error ? A : new Error(String(A)));
        }
        return "linux";
      }
      return "unknown";
    } catch (A) {
      return T1(A instanceof Error ? A : new Error(String(A))), "unknown";
    }
  }),
  SB1 = SA(() => {
    if (process.platform !== "linux") return;
    try {
      let A = x1().readFileSync("/proc/version", {
          encoding: "utf8"
        }),
        B = A.match(/WSL(\d+)/i);
      if (B && B[1]) return B[1];
      if (A.toLowerCase().includes("microsoft")) return "1";
      return;
    } catch (A) {
      T1(A instanceof Error ? A : new Error(String(A)));
      return;
    }
  }),
  Ro0 = P9() !== "windows";
var hV1 = "user:inference",
  c_9 = "org:create_api_key",
  Vp = "oauth-2025-04-20",
  Oo0 = {
    REDIRECT_PORT: P9() === "windows" ? 45454 : 54545,
    SCOPES: [c_9, "user:profile", hV1]
  },
  l_9 = {
    ...Oo0,
    BASE_API_URL: "https://api.anthropic.com",
    CONSOLE_AUTHORIZE_URL: "https://console.anthropic.com/oauth/authorize",
    CLAUDE_AI_AUTHORIZE_URL: "https://claude.ai/oauth/authorize",
    TOKEN_URL: "https://console.anthropic.com/v1/oauth/token",
    API_KEY_URL: "https://api.anthropic.com/api/oauth/claude_cli/create_api_key",
    ROLES_URL: "https://api.anthropic.com/api/oauth/claude_cli/roles",
    CONSOLE_SUCCESS_URL: "https://console.anthropic.com/buy_credits?returnUrl=/oauth/code/success%3Fapp%3Dclaude-code",
    CLAUDEAI_SUCCESS_URL: "https://console.anthropic.com/oauth/code/success?app=claude-code",
    MANUAL_REDIRECT_URL: "https://console.anthropic.com/oauth/code/callback",
    CLIENT_ID: "9d1c250a-e61b-44d9-88ed-5944d1962f5e"
  };
import { dirname as oYA, join as d91, resolve as u91 } from "path";
var tYA = F1(Rl1(), 1);
import Ri1 from "node:process";
import r_9 from "node:os";
import jo0 from "node:tty";
function iH(A, B = globalThis.Deno ? globalThis.Deno.args : Ri1.argv) {
  let Q = A.startsWith("-") ? "" : A.length === 1 ? "-" : "--",
    D = B.indexOf(Q + A),
    Z = B.indexOf("--");
  return D !== -1 && (Z === -1 || D < Z);
}
var {
    env: YZ
  } = Ri1,
  gV1;
function o_9() {
  if ("FORCE_COLOR" in YZ) {
    if (YZ.FORCE_COLOR === "true") return 1;
    if (YZ.FORCE_COLOR === "false") return 0;
    return YZ.FORCE_COLOR.length === 0 ? 1 : Math.min(Number.parseInt(YZ.FORCE_COLOR, 10), 3);
  }
}
function t_9(A) {
  if (A === 0) return !1;
  return {
    level: A,
    hasBasic: !0,
    has256: A >= 2,
    has16m: A >= 3
  };
}
function e_9(A, {
  streamIsTTY: B,
  sniffFlags: Q = !0
} = {}) {
  let D = o_9();
  if (D !== void 0) gV1 = D;
  let Z = Q ? gV1 : D;
  if (Z === 0) return 0;
  if (Q) {
    if (iH("color=16m") || iH("color=full") || iH("color=truecolor")) return 3;
    if (iH("color=256")) return 2;
  }
  if ("TF_BUILD" in YZ && "AGENT_NAME" in YZ) return 1;
  if (A && !B && Z === void 0) return 0;
  let G = Z || 0;
  if (YZ.TERM === "dumb") return G;
  if (Ri1.platform === "win32") {
    let F = r_9.release().split(".");
    if (Number(F[0]) >= 10 && Number(F[2]) >= 10586) return Number(F[2]) >= 14931 ? 3 : 2;
    return 1;
  }
  if ("CI" in YZ) {
    if (["GITHUB_ACTIONS", "GITEA_ACTIONS", "CIRCLECI"].some(F => F in YZ)) return 3;
    if (["TRAVIS", "APPVEYOR", "GITLAB_CI", "BUILDKITE", "DRONE"].some(F => F in YZ) || YZ.CI_NAME === "codeship") return 1;
    return G;
  }
  if ("TEAMCITY_VERSION" in YZ) return /^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(YZ.TEAMCITY_VERSION) ? 1 : 0;
  if (YZ.COLORTERM === "truecolor") return 3;
  if (YZ.TERM === "xterm-kitty") return 3;
  if ("TERM_PROGRAM" in YZ) {
    let F = Number.parseInt((YZ.TERM_PROGRAM_VERSION || "").split(".")[0], 10);
    switch (YZ.TERM_PROGRAM) {
      case "iTerm.app":
        return F >= 3 ? 3 : 2;
      case "Apple_Terminal":
        return 2;
    }
  }
  if (/-256(color)?$/i.test(YZ.TERM)) return 2;
  if (/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(YZ.TERM)) return 1;
  if ("COLORTERM" in YZ) return 1;
  return G;
}
function yo0(A, B = {}) {
  let Q = e_9(A, {
    streamIsTTY: A && A.isTTY,
    ...B
  });
  return t_9(Q);
}
var Ax9 = {
    stdout: yo0({
      isTTY: jo0.isatty(1)
    }),
    stderr: yo0({
      isTTY: jo0.isatty(2)
    })
  },
  ko0 = Ax9;
var {
    stdout: vo0,
    stderr: bo0
  } = ko0,
  Oi1 = Symbol("GENERATOR"),
  Cp = Symbol("STYLER"),
  jB1 = Symbol("IS_EMPTY"),
  fo0 = ["ansi", "ansi", "ansi256", "ansi16m"],
  Kp = Object.create(null),
  Bx9 = (A, B = {}) => {
    if (B.level && !(Number.isInteger(B.level) && B.level >= 0 && B.level <= 3)) throw new Error("The `level` option should be an integer from 0 to 3");
    let Q = vo0 ? vo0.level : 0;
    A.level = B.level === void 0 ? Q : B.level;
  };
var Qx9 = A => {
  let B = (...Q) => Q.join(" ");
  return Bx9(B, A), Object.setPrototypeOf(B, yB1.prototype), B;
};
function yB1(A) {
  return Qx9(A);
}
var Fx9 = yB1(),
  Rr8 = yB1({
    level: bo0 ? bo0.level : 0
  });
var A0 = Fx9;
function vG(A) {
  for (let B = 0; B < A.length; B += 2000) process.stdout.write(A.substring(B, B + 2000));
}
function ho0(A) {
  for (let B = 0; B < A.length; B += 2000) process.stderr.write(A.substring(B, B + 2000));
}
var mV1 = SA(() => {
    return process.argv.includes("--debug") || process.argv.includes("-d") || Si1();
  }),
  Si1 = SA(() => {
    return process.argv.includes("--debug-to-stderr") || process.argv.includes("-d2e");
  }),
  ji1 = !1;
function I0(A) {
  if (!mV1()) return;
  if (ji1 && A.includes(`
`)) A = JSON.stringify(A);
  let B = `[DEBUG] ${A.trim()}`;
  if (Si1()) console.error(B);else console.log(A0.dim(B));
}
function R2(A) {
  if (!mV1()) return;
  if (ji1 && A.includes(`
`)) A = JSON.stringify(A);
  let B = `[ERROR] ${A.trim()}
`;
  if (Si1()) ho0(B);else vG(A0.red(B));
}
import { setMaxListeners as Ix9 } from "events";
var Yx9 = 50,
  Wx9 = 500;
function f4(A = Yx9) {
  let B = new AbortController();
  return Ix9(A, B.signal), B;
}
import { isAbsolute as T91, resolve as P91, resolve as q85, relative as bIA, sep as R5Q, basename as Ur1, dirname as $r1, extname as wr1, join as xi } from "path";
import gD from "node:path";
import do0 from "node:os";
import yi1 from "node:process";
var Qy = do0.homedir(),
  ki1 = do0.tmpdir(),
  {
    env: Hp
  } = yi1,
  Jx9 = A => {
    let B = gD.join(Qy, "Library");
    return {
      data: gD.join(B, "Application Support", A),
      config: gD.join(B, "Preferences", A),
      cache: gD.join(B, "Caches", A),
      log: gD.join(B, "Logs", A),
      temp: gD.join(ki1, A)
    };
  },
  Xx9 = A => {
    let B = Hp.APPDATA || gD.join(Qy, "AppData", "Roaming"),
      Q = Hp.LOCALAPPDATA || gD.join(Qy, "AppData", "Local");
    return {
      data: gD.join(Q, A, "Data"),
      config: gD.join(B, A, "Config"),
      cache: gD.join(Q, A, "Cache"),
      log: gD.join(Q, A, "Log"),
      temp: gD.join(ki1, A)
    };
  },
  Vx9 = A => {
    let B = gD.basename(Qy);
    return {
      data: gD.join(Hp.XDG_DATA_HOME || gD.join(Qy, ".local", "share"), A),
      config: gD.join(Hp.XDG_CONFIG_HOME || gD.join(Qy, ".config"), A),
      cache: gD.join(Hp.XDG_CACHE_HOME || gD.join(Qy, ".cache"), A),
      log: gD.join(Hp.XDG_STATE_HOME || gD.join(Qy, ".local", "state"), A),
      temp: gD.join(ki1, B, A)
    };
  };
function _i1(A, {
  suffix: B = "nodejs"
} = {}) {
  if (typeof A !== "string") throw new TypeError(`Expected a string, got ${typeof A}`);
  if (B) A += `-${B}`;
  if (yi1.platform === "darwin") return Jx9(A);
  if (yi1.platform === "win32") return Xx9(A);
  return Vx9(A);
}
class Bh extends Error {
  filePath;
  defaultConfig;
  constructor(A, B, Q) {
    super(A);
    this.name = "ConfigParseError", this.filePath = B, this.defaultConfig = Q;
  }
}
var zw = ["userSettings", "projectSettings", "localSettings", "flagSettings", "policySettings"],
  YIA = "https://json.schemastore.org/claude-code-settings.json";
function uD() {
  return process.env.CLAUDE_CODE_USE_BEDROCK ? "bedrock" : process.env.CLAUDE_CODE_USE_VERTEX ? "vertex" : "firstParty";
}
var g = {};
var Xr1 = g.enum(["local", "user", "project", "dynamic"]),
  m65 = g.enum(["stdio", "sse", "sse-ide", "http"]),
  Vr1 = g.object({
    type: g.literal("stdio").optional(),
    command: g.string().min(1, "Command cannot be empty"),
    args: g.array(g.string()).default([]),
    env: g.record(g.string()).optional()
  }),
  J5Q = g.object({
    type: g.literal("sse"),
    url: g.string(),
    headers: g.record(g.string()).optional()
  }),
  X5Q = g.object({
    type: g.literal("sse-ide"),
    url: g.string(),
    ideName: g.string(),
    ideRunningInWindows: g.boolean().optional()
  }),
  V5Q = g.object({
    type: g.literal("ws-ide"),
    url: g.string(),
    ideName: g.string(),
    authToken: g.string().optional(),
    ideRunningInWindows: g.boolean().optional()
  }),
  C5Q = g.object({
    type: g.literal("http"),
    url: g.string(),
    headers: g.record(g.string()).optional()
  }),
  Cr1 = g.union([Vr1, J5Q, X5Q, V5Q, C5Q]);
var LIA = g.object({
  mcpServers: g.record(g.string(), Cr1)
});
function UK1(A) {
  switch (A) {
    case "cliArg":
      return "CLI argument";
    case "command":
      return "command configuration";
    case "localSettings":
      return "project local settings";
    case "projectSettings":
      return "project settings";
    case "policySettings":
      return "policy settings";
    case "userSettings":
      return "user settings";
    case "flagSettings":
      return "flag settings";
  }
}
function UY(A) {
  try {
    let Q = x1(),
      {
        resolvedPath: D
      } = WV(Q, A),
      {
        buffer: Z,
        bytesRead: G
      } = Q.readSync(D, {
        length: 4096
      });
    if (G >= 2) {
      if (Z[0] === 255 && Z[1] === 254) return "utf16le";
    }
    if (G >= 3 && Z[0] === 239 && Z[1] === 187 && Z[2] === 191) return "utf8";
    return Z.slice(0, G).toString("utf8").length > 0 ? "utf8" : "ascii";
  } catch (Q) {
    return T1(Q), "utf8";
  }
}
function LY(A) {
  let B = x1(),
    {
      resolvedPath: Q,
      isSymlink: D
    } = WV(B, A);
  if (D) I0(`Reading through symlink: ${A} -> ${Q}`);
  let Z = UY(Q);
  return B.readFileSync(Q, {
    encoding: Z
  }).replaceAll(`\r
`, `
`);
}
function kN(A, B, Q = {
  encoding: "utf-8"
}) {
  let D = x1(),
    Z = A;
  if (D.existsSync(A)) try {
    let F = D.readlinkSync(A);
    Z = T91(F) ? F : P91($r1(A), F), I0(`Writing through symlink: ${A} -> ${Z}`);
  } catch (F) {
    Z = A;
  }
  let G = `${Z}.tmp.${process.pid}.${Date.now()}`;
  try {
    I0(`Writing to temp file: ${G}`);
    let F;
    if (D.existsSync(Z)) F = D.statSync(Z).mode, I0(`Preserving file permissions: ${F.toString(8)}`);
    if (D.writeFileSync(G, B, {
      encoding: Q.encoding,
      flush: !0
    }), I0(`Temp file written successfully, size: ${B.length} bytes`), F !== void 0) D.chmodSync(G, F), I0("Applied original permissions to temp file");
    I0(`Renaming ${G} to ${Z}`), D.renameSync(G, Z), I0(`File ${Z} written atomically`);
  } catch (F) {
    R2(`Failed to write file atomically: ${F}`), T1(F), C1("tengu_atomic_write_error", {});
    try {
      if (D.existsSync(G)) I0(`Cleaning up temp file: ${G}`), D.unlinkSync(G);
    } catch (I) {
      R2(`Failed to clean up temp file: ${I}`);
    }
    I0(`Falling back to non-atomic write for ${Z}`);
    try {
      D.writeFileSync(Z, B, {
        encoding: Q.encoding,
        flush: !0
      }), I0(`File ${Z} written successfully with non-atomic fallback`);
    } catch (I) {
      throw R2(`Non-atomic write also failed: ${I}`), I;
    }
  }
}
var RK1 = _i1("claude-cli");
function OK1(A) {
  return A.replace(/[^a-zA-Z0-9]/g, "-");
}
var _N = {
  baseLogs: () => xi(RK1.cache, OK1(x1().cwd())),
  errors: () => xi(RK1.cache, OK1(x1().cwd()), "errors"),
  messages: () => xi(RK1.cache, OK1(x1().cwd()), "messages"),
  mcpLogs: A => xi(RK1.cache, OK1(x1().cwd()), `mcp-logs-${A}`)
};
var T7 = SA((A, B = !0) => {
  if (!A) return null;
  try {
    return JSON.parse(A);
  } catch (Q) {
    if (B) T1(Q);
    return null;
  }
});
var BYA = ["PreToolUse", "PostToolUse", "Notification", "UserPromptSubmit", "SessionStart", "Stop", "SubagentStop", "PreCompact"];
var _K1 = ["acceptEdits", "bypassPermissions", "default", "plan"];
var b5Q = g.record(g.coerce.string());
function IYA() {
  return g.string();
}
var f5Q = g.object({
    allow: g.array(IYA()).optional().describe("List of permission rules for allowed operations"),
    deny: g.array(IYA()).optional().describe("List of permission rules for denied operations"),
    defaultMode: g.enum(_K1).optional().describe("Default permission mode when Claude Code needs access"),
    disableBypassPermissionsMode: g.enum(["disable"]).optional().describe("Disable the ability to bypass permission prompts"),
    additionalDirectories: g.array(g.string()).optional().describe("Additional directories to include in the permission scope")
  }).passthrough(),
  h5Q = g.object({
    type: g.literal("command").describe('Hook type (currently only "command" is supported)'),
    command: g.string().describe("Shell command to execute"),
    timeout: g.number().positive().optional().describe("Timeout in seconds for this specific command")
  }),
  g5Q = g.object({
    matcher: g.string().optional().describe('String pattern to match (e.g. tool names like "Write")'),
    hooks: g.array(h5Q).describe("List of hooks to execute when the matcher matches")
  }),
  u5Q = g.record(g.enum(BYA), g.array(g5Q)),
  YYA = g.object({
    $schema: g.literal(YIA).optional().describe("JSON Schema reference for Claude Code settings"),
    apiKeyHelper: g.string().optional().describe("Path to a script that outputs authentication values"),
    awsCredentialExport: g.string().optional().describe("Path to a script that exports AWS credentials"),
    awsAuthRefresh: g.string().optional().describe("Path to a script that refreshes AWS authentication"),
    cleanupPeriodDays: g.number().nonnegative().int().optional().describe("Number of days to retain chat transcripts (0 to disable cleanup)"),
    env: b5Q.optional().describe("Environment variables to set for Claude Code sessions"),
    includeCoAuthoredBy: g.boolean().optional().describe("Whether to include Claude's co-authored by attribution in commits and PRs (defaults to true)"),
    permissions: f5Q.optional().describe("Tool usage permissions configuration"),
    model: g.string().optional().describe("Override the default model used by Claude Code"),
    enableAllProjectMcpServers: g.boolean().optional().describe("Whether to automatically approve all MCP servers in the project"),
    enabledMcpjsonServers: g.array(g.string()).optional().describe("List of approved MCP servers from .mcp.json"),
    disabledMcpjsonServers: g.array(g.string()).optional().describe("List of rejected MCP servers from .mcp.json"),
    hooks: u5Q.optional().describe("Custom commands to run before/after tool executions"),
    disableAllHooks: g.boolean().optional().describe("Disable all hooks and statusLine execution"),
    statusLine: g.object({
      type: g.literal("command"),
      command: g.string(),
      padding: g.number().optional()
    }).optional().describe("Custom status line display configuration"),
    learnMode: g.boolean().optional().describe("Enable learn mode for educational responses"),
    forceLoginMethod: g.enum(["claudeai", "console"]).optional().describe('Force a specific login method: "claudeai" for Claude Pro/Max, "console" for Console billing'),
    otelHeadersHelper: g.string().optional().describe("Path to a script that outputs OpenTelemetry headers"),
    outputMode: g.string().optional().describe("Controls the output style for assistant responses")
  }).passthrough();
var l5Q = [{
    matches: A => A.path === "permissions.defaultMode" && A.code === "invalid_enum_value",
    tip: {
      suggestion: 'Valid modes: "acceptEdits" (ask before file changes), "plan" (analysis only), "bypassPermissions" (auto-accept all), or "default" (standard behavior)',
      docLink: "https://docs.anthropic.com/en/docs/claude-code/iam#permission-modes"
    }
  }, {
    matches: A => A.path === "apiKeyHelper" && A.code === "invalid_type",
    tip: {
      suggestion: 'Provide a shell command that outputs your API key to stdout. The script should output only the API key. Example: "/bin/generate_temp_api_key.sh"'
    }
  }, {
    matches: A => A.path === "cleanupPeriodDays" && A.code === "too_small" && A.expected === "0",
    tip: {
      suggestion: "Must be 0 or greater. Use 0 to disable automatic cleanup and keep chat transcripts forever, or set a positive number for days to retain (default is 30 days)"
    }
  }, {
    matches: A => A.path.startsWith("env.") && A.code === "invalid_type",
    tip: {
      suggestion: 'Environment variables must be strings. Wrap numbers and booleans in quotes. Example: "DEBUG": "true", "PORT": "3000"',
      docLink: "https://docs.anthropic.com/en/docs/claude-code/settings#environment-variables"
    }
  }, {
    matches: A => (A.path === "permissions.allow" || A.path === "permissions.deny") && A.code === "invalid_type" && A.expected === "array",
    tip: {
      suggestion: 'Permission rules must be in an array. Format: ["Tool(specifier)"]. Examples: ["Bash(npm run build)", "Edit(docs/**)", "Read(~/.zshrc)"]. Use * for wildcards.'
    }
  }, {
    matches: A => A.path.includes("hooks") && A.code === "invalid_type",
    tip: {
      suggestion: 'Hooks use a new format with matchers. Example: {"PostToolUse": [{"matcher": {"tools": ["BashTool"]}, "hooks": [{"type": "command", "command": "echo Done"}]}]}'
    }
  }, {
    matches: A => A.code === "invalid_type" && A.expected === "boolean",
    tip: {
      suggestion: 'Use true or false without quotes. Example: "includeCoAuthoredBy": true'
    }
  }, {
    matches: A => A.code === "unrecognized_keys",
    tip: {
      suggestion: "Check for typos or refer to the documentation for valid fields",
      docLink: "https://docs.anthropic.com/en/docs/claude-code/settings"
    }
  }, {
    matches: A => A.code === "invalid_enum_value" && A.enumValues !== void 0,
    tip: {
      suggestion: void 0
    }
  }, {
    matches: A => A.code === "invalid_type" && A.expected === "object" && A.received === null && A.path === "",
    tip: {
      suggestion: "Check for missing commas, unmatched brackets, or trailing commas. Use a JSON validator to identify the exact syntax error."
    }
  }, {
    matches: A => A.path === "permissions.additionalDirectories" && A.code === "invalid_type",
    tip: {
      suggestion: 'Must be an array of directory paths. Example: ["~/projects", "/tmp/workspace"]. You can also use --add-dir flag or /add-dir command',
      docLink: "https://docs.anthropic.com/en/docs/claude-code/iam#working-directories"
    }
  }],
  p5Q = {
    permissions: "https://docs.anthropic.com/en/docs/claude-code/iam#configuring-permissions",
    env: "https://docs.anthropic.com/en/docs/claude-code/settings#environment-variables",
    hooks: "https://docs.anthropic.com/en/docs/claude-code/hooks"
  };
function HYA(A) {
  let B = l5Q.find(D => D.matches(A));
  if (!B) return null;
  let Q = {
    ...B.tip
  };
  if (A.code === "invalid_enum_value" && A.enumValues && !Q.suggestion) Q.suggestion = `Valid values: ${A.enumValues.map(D => `"${D}"`).join(", ")}`;
  if (!Q.docLink && A.path) {
    let D = A.path.split(".")[0];
    if (D) Q.docLink = p5Q[D];
  }
  return Q;
}
function zYA(A) {
  return A.code === "invalid_type";
}
function EYA(A) {
  return A.code === "invalid_literal";
}
function UYA(A) {
  return A.code === "invalid_enum_value";
}
function i5Q(A) {
  return A.code === "unrecognized_keys";
}
function wYA(A) {
  return A.code === "too_small";
}
function $YA(A, B) {
  return A.issues.map(Q => {
    let D = Q.path.join("."),
      Z = Q.message,
      G,
      F,
      I,
      Y;
    if (UYA(Q)) F = Q.options.map(J => String(J)), Y = Q.received;else if (EYA(Q)) I = String(Q.expected), Y = Q.received;else if (zYA(Q)) I = Q.expected, Y = Q.received;else if (wYA(Q)) I = String(Q.minimum);else if (Q.code === "custom" && "params" in Q) Y = Q.params.received;
    let W = HYA({
      path: D,
      code: Q.code,
      expected: I,
      received: Y,
      enumValues: F,
      message: Q.message,
      value: Y
    });
    if (EYA(Q)) G = `"${Q.expected}"`, Z = `"${Q.received}" is not valid. Expected: ${G}`;else if (UYA(Q)) G = F?.map(J => `"${J}"`).join(", "), Z = `"${Q.received}" is not valid. Expected one of: ${G}`;else if (zYA(Q)) {
      if (Q.expected === "object" && Q.received === "null" && D === "") Z = "Invalid or malformed JSON";else Z = `Expected ${Q.expected}, but received ${Q.received}`;
    } else if (i5Q(Q)) {
      let J = Q.keys.join(", ");
      Z = `Unrecognized field${Q.keys.length > 1 ? "s" : ""}: ${J}`;
    } else if (wYA(Q)) Z = `Number must be greater than or equal to ${Q.minimum}`, G = String(Q.minimum);
    return {
      file: B,
      path: D,
      message: Z,
      expected: G,
      invalidValue: Y,
      suggestion: W?.suggestion,
      docLink: W?.docLink
    };
  });
}
import { join as qYA } from "path";
function vr1(A, B) {
  if (!A) return {};
  let Q = {};
  for (let [D, Z] of Object.entries(A)) Q[D] = {
    ...Z,
    scope: B
  };
  return Q;
}
function n5Q(A) {
  let B = [];
  function Q(Z) {
    return Z.replace(/\$\{([^}]+)\}/g, (G, F) => {
      let [I, Y] = F.split(":-"),
        W = process.env[I];
      if (W !== void 0) return W;
      if (Y !== void 0) return Y;
      return B.push(I), G;
    });
  }
  let D;
  switch (A.type) {
    case void 0:
    case "stdio":
      {
        let Z = A;
        D = {
          ...Z,
          command: Q(Z.command),
          args: Z.args.map(Q),
          env: Z.env ? vj(Z.env, Q) : void 0
        };
        break;
      }
    case "sse":
    case "http":
      {
        let Z = A;
        D = {
          ...Z,
          url: Q(Z.url),
          headers: Z.headers ? vj(Z.headers, Q) : void 0
        };
        break;
      }
    case "sse-ide":
    case "ws-ide":
      D = A;
      break;
  }
  return {
    expanded: D,
    missingVars: [...new Set(B)]
  };
}
function pZ(A) {
  switch (A) {
    case "project":
      {
        let B = qYA(a0(), ".mcp.json");
        if (!x1().existsSync(B)) return {
          servers: {},
          errors: []
        };
        let {
          config: D,
          errors: Z
        } = fr1({
          filePath: B,
          expandVars: !0,
          scope: "project"
        });
        return {
          servers: vr1(D?.mcpServers, A),
          errors: Z
        };
      }
    case "user":
      {
        let B = E0().mcpServers;
        if (!B) return {
          servers: {},
          errors: []
        };
        let {
          config: Q,
          errors: D
        } = b91({
          configObject: {
            mcpServers: B
          },
          expandVars: !0,
          scope: "user"
        });
        return {
          servers: vr1(Q?.mcpServers, A),
          errors: D
        };
      }
    case "local":
      {
        let B = t9().mcpServers;
        if (!B) return {
          servers: {},
          errors: []
        };
        let {
          config: Q,
          errors: D
        } = b91({
          configObject: {
            mcpServers: B
          },
          expandVars: !0,
          scope: "local"
        });
        return {
          servers: vr1(Q?.mcpServers, A),
          errors: D
        };
      }
  }
}
function b91(A) {
  let {
      configObject: B,
      expandVars: Q,
      scope: D,
      filePath: Z
    } = A,
    G = LIA.safeParse(B);
  if (!G.success) return {
    config: null,
    errors: G.error.issues.map(Y => ({
      ...(Z && {
        file: Z
      }),
      path: Y.path.join("."),
      message: "Does not adhere to MCP server configuration schema",
      mcpErrorMetadata: {
        scope: D,
        severity: "fatal"
      }
    }))
  };
  let F = [],
    I = {};
  for (let [Y, W] of Object.entries(G.data.mcpServers)) {
    let J = W;
    if (Q) {
      let {
        expanded: X,
        missingVars: V
      } = n5Q(W);
      if (V.length > 0) F.push({
        ...(Z && {
          file: Z
        }),
        path: `mcpServers.${Y}`,
        message: `Missing environment variables: ${V.join(", ")}`,
        suggestion: `Set the following environment variables: ${V.join(", ")}`,
        mcpErrorMetadata: {
          scope: D,
          serverName: Y,
          severity: "warning"
        }
      });
      J = X;
    }
    if (P9() === "windows" && (!J.type || J.type === "stdio") && (J.command === "npx" || J.command.endsWith("\\npx") || J.command.endsWith("/npx"))) F.push({
      ...(Z && {
        file: Z
      }),
      path: `mcpServers.${Y}`,
      message: "Windows requires 'cmd /c' wrapper to execute npx",
      suggestion: 'Change command to "cmd" with args ["/c", "npx", ...]. See: https://docs.anthropic.com/en/docs/claude-code/mcp#configure-mcp-servers',
      mcpErrorMetadata: {
        scope: D,
        serverName: Y,
        severity: "warning"
      }
    });
    I[Y] = J;
  }
  return {
    config: {
      mcpServers: I
    },
    errors: F
  };
}
function fr1(A) {
  let {
      filePath: B,
      expandVars: Q,
      scope: D
    } = A,
    Z = x1();
  if (!Z.existsSync(B)) return {
    config: null,
    errors: [{
      file: B,
      path: "",
      message: `MCP config file not found: ${B}`,
      suggestion: "Check that the file path is correct",
      mcpErrorMetadata: {
        scope: D,
        severity: "fatal"
      }
    }]
  };
  let G;
  try {
    G = Z.readFileSync(B, {
      encoding: "utf8"
    });
  } catch (I) {
    return {
      config: null,
      errors: [{
        file: B,
        path: "",
        message: `Failed to read file: ${I}`,
        suggestion: "Check file permissions and ensure the file exists",
        mcpErrorMetadata: {
          scope: D,
          severity: "fatal"
        }
      }]
    };
  }
  let F = T7(G);
  if (!F) return {
    config: null,
    errors: [{
      file: B,
      path: "",
      message: "MCP config is not a valid JSON",
      suggestion: "Fix the JSON syntax errors in the file",
      mcpErrorMetadata: {
        scope: D,
        severity: "fatal"
      }
    }]
  };
  return b91({
    configObject: F,
    expandVars: Q,
    scope: D,
    filePath: B
  });
}
var m91 = null;
function ar1() {
  switch (P9()) {
    case "macos":
      return "/Library/Application Support/ClaudeCode";
    case "windows":
      return "C:\\ProgramData\\ClaudeCode";
    default:
      return "/etc/claude-code";
  }
}
function o3Q() {
  return d91(ar1(), "managed-settings.json");
}
function t3Q(A) {
  if (A.length === 0) return "unknown";
  let B = A[0];
  if (!B) return "unknown";
  if (B.path.length > 0) return B.path.join(".");
  return "unknown";
}
function e3Q(A, B) {
  let Q = new Error("Invalid settings"),
    D = t3Q(B.issues);
  tYA.withScope(Z => {
    if (A) Z.setTag("settings_source", UK1(A));
    Z.setTag("invalid_key", D), Z.setContext("validation_error", {
      errorMessage: B.message,
      issues: B.issues
    }), T1(Q);
  }), I0(`Invalid settings in ${A || "unknown"} source - key: ${D}, error: ${B.message}`);
}
function A7Q(A, B) {
  if (typeof A === "object" && A && "code" in A && A.code === "ENOENT") I0(`Broken symlink or missing file encountered for settings.json at path: ${B}`);else T1(A instanceof Error ? A : new Error(String(A)));
}
function eYA(A, B) {
  let Q = x1();
  if (!Q.existsSync(A)) return {
    settings: null,
    errors: []
  };
  try {
    let {
        resolvedPath: D
      } = WV(Q, A),
      Z = LY(D);
    if (Z.trim() === "") return {
      settings: {},
      errors: []
    };
    let G = T7(Z),
      F = YYA.safeParse(G);
    if (!F.success) return e3Q(B, F.error), {
      settings: null,
      errors: $YA(F.error, A)
    };
    return {
      settings: F.data,
      errors: []
    };
  } catch (D) {
    return A7Q(D, A), {
      settings: null,
      errors: []
    };
  }
}
function MK1(A) {
  switch (A) {
    case "userSettings":
      return u91(YQ());
    case "policySettings":
    case "projectSettings":
    case "localSettings":
      return u91(x9());
    case "flagSettings":
      {
        let B = nu1();
        return B ? oYA(u91(B)) : u91(x9());
      }
  }
}
function bO(A) {
  switch (A) {
    case "userSettings":
      return d91(MK1(A), "settings.json");
    case "projectSettings":
    case "localSettings":
      return d91(MK1(A), c91(A));
    case "policySettings":
      return o3Q();
    case "flagSettings":
      return nu1();
  }
}
function c91(A) {
  switch (A) {
    case "projectSettings":
      return d91(".claude", "settings.json");
    case "localSettings":
      return d91(".claude", "settings.local.json");
  }
}
function B7Q(A, B) {
  let Q = [...A, ...B];
  return Array.from(new Set(Q));
}
function Q7Q() {
  let A = {},
    B = [],
    Q = new Set(),
    D = new Set();
  for (let G of zw) {
    let F = bO(G);
    if (!F) continue;
    let I = u91(F);
    if (D.has(I)) continue;
    D.add(I);
    let {
      settings: Y,
      errors: W
    } = eYA(F, G);
    for (let J of W) {
      let X = `${J.file}:${J.path}:${J.message}`;
      if (!Q.has(X)) Q.add(X), B.push(J);
    }
    if (Y) A = BW1(A, Y, (J, X) => {
      if (Array.isArray(J) && Array.isArray(X)) return B7Q(J, X);
      return;
    });
  }
  let Z = ["user", "project", "local"];
  return B.push(...Z.flatMap(G => pZ(G).errors)), {
    settings: A,
    errors: B
  };
}
function C9() {
  let {
    settings: A
  } = vN();
  return A || {};
}
function vN() {
  if (m91 !== null) return m91;
  return m91 = Q7Q(), m91;
}
function sr1(A, B = 300000) {
  let Q = new Map(),
    D = (...Z) => {
      let G = JSON.stringify(Z),
        F = Q.get(G),
        I = Date.now();
      if (!F) Q.set(G, {
        value: A(...Z),
        timestamp: I,
        refreshing: !1
      });
      if (F && I - F.timestamp > B && !F.refreshing) return F.refreshing = !0, Promise.resolve().then(() => {
        let Y = A(...Z);
        Q.set(G, {
          value: Y,
          timestamp: Date.now(),
          refreshing: !1
        });
      }).catch(Y => {
        T1(Y instanceof Error ? Y : new Error(String(Y)));
        let W = Q.get(G);
        if (W) W.refreshing = !1;
      }), F.value;
      return Q.get(G).value;
    };
  return D.cache = {
    clear: () => Q.clear()
  }, D;
}
import { createHash as D7Q } from "crypto";
function p91(A = "") {
  let B = YQ(),
    D = !process.env.CLAUDE_CONFIG_DIR ? "" : `-${D7Q("sha256").update(B).digest("hex").substring(0, 8)}`;
  return `Claude Code${A}${D}`;
}
function BWA() {
  let A = p91("-credentials");
  return {
    name: "keychain",
    read() {
      try {
        let B = GD(`security find-generic-password -a $USER -w -s "${A}"`);
        if (B) return JSON.parse(B);
      } catch (B) {
        return null;
      }
      return null;
    },
    update(B) {
      try {
        let D = JSON.stringify(B).replace(/"/g, "\\\""),
          Z = `security add-generic-password -U -a $USER -s "${A}" -w "${D}"`;
        return GD(Z), {
          success: !0
        };
      } catch (Q) {
        return {
          success: !1
        };
      }
    },
    delete() {
      try {
        return GD(`security delete-generic-password -a $USER -s "${A}"`), !0;
      } catch (B) {
        return !1;
      }
    }
  };
}
import { join as Z7Q } from "path";
function rr1() {
  let A = YQ(),
    B = ".credentials.json",
    Q = Z7Q(A, ".credentials.json");
  return {
    name: "plaintext",
    read() {
      if (x1().existsSync(Q)) try {
        let D = x1().readFileSync(Q, {
          encoding: "utf8"
        });
        return JSON.parse(D);
      } catch (D) {
        return null;
      }
      return null;
    },
    update(D) {
      try {
        if (!x1().existsSync(A)) x1().mkdirSync(A);
        return x1().writeFileSync(Q, JSON.stringify(D), {
          encoding: "utf8",
          flush: !1
        }), x1().chmodSync(Q, 384), {
          success: !0,
          warning: "Warning: Storing credentials in plaintext."
        };
      } catch (Z) {
        return {
          success: !1
        };
      }
    },
    delete() {
      if (x1().existsSync(Q)) try {
        return x1().unlinkSync(Q), !0;
      } catch (D) {
        return !1;
      }
      return !0;
    }
  };
}
function G7Q(A, B) {
  return {
    name: `${A.name}-with-${B.name}-fallback`,
    read() {
      let Q = A.read();
      if (Q !== null && Q !== void 0) return Q;
      return B.read() || {};
    },
    update(Q) {
      let D = A.read(),
        Z = A.update(Q);
      if (Z.success) {
        if (D === null) B.delete();
        return Z;
      }
      let G = B.update(Q);
      if (G.success) return {
        success: !0,
        warning: G.warning
      };
      return {
        success: !1
      };
    },
    delete() {
      let Q = A.delete(),
        D = B.delete();
      return Q || D;
    }
  };
}
function tC() {
  if (process.platform === "darwin") {
    let A = BWA(),
      B = rr1();
    return G7Q(A, B);
  }
  return rr1();
}
function hy(A) {
  return Boolean(A?.includes(hV1));
}
var ew1 = F1(jSA(), 1),
  aaA = F1(qQ0(), 1);
function raA(A) {
  if (!A || typeof A !== "object") return !1;
  let B = A;
  if (!B.Credentials || typeof B.Credentials !== "object") return !1;
  let Q = B.Credentials;
  return typeof Q.AccessKeyId === "string" && typeof Q.SecretAccessKey === "string" && typeof Q.SessionToken === "string" && Q.AccessKeyId.length > 0 && Q.SecretAccessKey.length > 0 && Q.SessionToken.length > 0;
}
var NQ0 = async () => {
  await new ew1.STSClient().send(new ew1.GetCallerIdentityCommand({}));
};
async function oaA() {
  try {
    I0("Clearing AWS credential provider cache"), await aaA.fromIni({
      ignoreCache: !0
    })(), I0("AWS credential provider cache refreshed");
  } catch (A) {
    I0("Failed to clear AWS credential cache (this is expected if no credentials are configured)");
  }
}
import { exec as yB4 } from "child_process";
var kB4 = 300000;
function xz() {
  let A = process.env.CLAUDE_CODE_USE_BEDROCK || process.env.CLAUDE_CODE_USE_VERTEX,
    Q = (C9() || {}).apiKeyHelper,
    D = process.env.ANTHROPIC_AUTH_TOKEN || Q,
    {
      source: Z
    } = tJ(sc());
  return !(A || D || Z === "ANTHROPIC_API_KEY" || Z === "apiKeyHelper");
}
function tJ(A) {
  if (A && process.env.ANTHROPIC_API_KEY) return {
    key: process.env.ANTHROPIC_API_KEY,
    source: "ANTHROPIC_API_KEY"
  };
  if (gQ(!1)) {
    if (!process.env.ANTHROPIC_API_KEY && !process.env.CLAUDE_CODE_OAUTH_TOKEN) throw new Error("ANTHROPIC_API_KEY or CLAUDE_CODE_OAUTH_TOKEN env var is required");
    if (process.env.ANTHROPIC_API_KEY) return {
      key: process.env.ANTHROPIC_API_KEY,
      source: "ANTHROPIC_API_KEY"
    };
    return {
      key: null,
      source: "none"
    };
  }
  if (process.env.ANTHROPIC_API_KEY && E0().customApiKeyResponses?.approved?.includes(CK(process.env.ANTHROPIC_API_KEY))) return {
    key: process.env.ANTHROPIC_API_KEY,
    source: "ANTHROPIC_API_KEY"
  };
  let B = Ig();
  if (B) return {
    key: B,
    source: "apiKeyHelper"
  };
  let Q = v41();
  if (Q) return Q;
  return {
    key: null,
    source: "none"
  };
}
function _B4() {
  let A = process.env.CLAUDE_CODE_API_KEY_HELPER_TTL_MS;
  if (A) {
    let B = parseInt(A, 10);
    if (!Number.isNaN(B) && B >= 0) return B;
    R2(`Found CLAUDE_CODE_API_KEY_HELPER_TTL_MS env var, but it was not a valid number. Got ${A}`);
  }
  return kB4;
}
var Ig = sr1(() => {
  let B = (C9() || {}).apiKeyHelper;
  if (!B) return null;
  try {
    let Q = GD(B)?.toString().trim();
    if (!Q) throw new Error("apiKeyHelper did not return a valid value");
    return Q;
  } catch (Q) {
    let D = A0.red("Error getting API key from apiKeyHelper (in settings or ~/.claude.json):");
    if (Q instanceof Error && "stderr" in Q) console.error(D, String(Q.stderr));else if (Q instanceof Error) console.error(D, Q.message);else console.error(D, Q);
    return " ";
  }
}, _B4());
var xB4 = 3600000;
async function vB4() {
  let A = C9()?.awsAuthRefresh;
  if (!A) return !1;
  try {
    return I0("Fetching AWS caller identity for AWS auth refresh command"), await NQ0(), I0("Fetched AWS caller identity, skipping AWS auth refresh command"), !1;
  } catch {
    return I0("Running AWS auth refresh command"), new Promise(B => {
      let Q = yB4(A);
      Q.stdout.on("data", D => {
        console.log(D);
      }), Q.stderr.on("data", D => {
        console.error(D);
      }), Q.on("close", D => {
        if (D === 0) I0("AWS auth refresh completed successfully"), B(!0);else {
          let Z = A0.red("Error running awsAuthRefresh (in settings or ~/.claude.json):");
          console.error(Z), B(!1);
        }
      });
    });
  }
}
async function bB4() {
  let A = C9()?.awsCredentialExport;
  if (!A) return null;
  try {
    return I0("Fetching AWS caller identity for credential export command"), await NQ0(), I0("Fetched AWS caller identity, skipping AWS credential export command"), null;
  } catch {
    try {
      I0("Running AWS credential export command");
      let B = GD(A)?.toString().trim();
      if (!B) throw new Error("awsCredentialExport did not return a valid value");
      let Q = JSON.parse(B);
      if (!raA(Q)) throw new Error("awsCredentialExport did not return valid AWS STS output structure");
      return I0("AWS credentials retrieved from awsCredentialExport"), {
        accessKeyId: Q.Credentials.AccessKeyId,
        secretAccessKey: Q.Credentials.SecretAccessKey,
        sessionToken: Q.Credentials.SessionToken
      };
    } catch (B) {
      let Q = A0.red("Error getting AWS credentials from awsCredentialExport (in settings or ~/.claude.json):");
      if (B instanceof Error && "stderr" in B) console.error(Q, String(B.stderr));else if (B instanceof Error) console.error(Q, B.message);else console.error(Q, B);
      return null;
    }
  }
}
var x41 = sr1(async () => {
  let A = await vB4(),
    B = await bB4();
  if (A || B) await oaA();
  return B;
}, xB4);
function CK(A) {
  return A.slice(-20);
}
var v41 = SA(() => {
  if (process.platform === "darwin") {
    let B = p91();
    try {
      let Q = GD(`security find-generic-password -a $USER -w -s "${B}"`);
      if (Q) return {
        key: Q,
        source: "/login managed key"
      };
    } catch (Q) {
      T1(Q);
    }
  }
  let A = E0();
  if (!A.primaryApiKey) return null;
  return {
    key: A.primaryApiKey,
    source: "/login managed key"
  };
});
var FD = SA(() => {
  if (process.env.CLAUDE_CODE_OAUTH_TOKEN) return {
    accessToken: process.env.CLAUDE_CODE_OAUTH_TOKEN,
    refreshToken: null,
    expiresAt: null,
    scopes: ["user:inference"],
    subscriptionType: null
  };
  try {
    let Q = tC().read()?.claudeAiOauth;
    if (!Q?.accessToken) return null;
    if (!Q.subscriptionType) {
      let D = Q.isMax === !1 ? "pro" : "max";
      return {
        ...Q,
        subscriptionType: D
      };
    }
    return Q;
  } catch (A) {
    return T1(A), null;
  }
});
function F9() {
  if (!xz()) return !1;
  return hy(FD()?.scopes);
}
function mY() {
  let A = FsA();
  return A === "max" || A === "enterprise" || A === "team";
}
function FsA() {
  if (!xz()) return null;
  let A = FD();
  if (!A) return null;
  return A.subscriptionType ?? null;
}
function hB4(A) {
  let B = uD();
  if (B === "bedrock") return !1;else if (B === "firstParty") return !A.includes("claude-3-");else return A.includes("claude-opus-4") || A.includes("claude-sonnet-4");
}
var wV = SA(A => {
  let B = [],
    Q = A.includes("haiku");
  if (!Q) B.push(No0);
  if (F9()) B.push(Vp);
  if (!gQ(process.env.DISABLE_INTERLEAVED_THINKING) && hB4(A)) B.push(fV1);
  if (uD() === "firstParty" && !gQ(process.env.CLAUDE_CODE_DISABLE_FINE_GRAINED_TOOL_STREAMING)) B.push(Lo0);
  if (gQ(process.env.USE_API_CONTEXT_MANAGEMENT), process.env.ANTHROPIC_BETAS && !Q) B.push(...process.env.ANTHROPIC_BETAS.split(",").map(Z => Z.trim()).filter(Boolean));
  return B;
});
var Yg = {
    firstParty: "claude-3-7-sonnet-20250219",
    bedrock: "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
    vertex: "claude-3-7-sonnet@20250219"
  },
  Wg = {
    firstParty: "claude-3-5-sonnet-20241022",
    bedrock: "anthropic.claude-3-5-sonnet-20241022-v2:0",
    vertex: "claude-3-5-sonnet-v2@20241022"
  },
  f41 = {
    firstParty: "claude-3-5-haiku-20241022",
    bedrock: "us.anthropic.claude-3-5-haiku-20241022-v1:0",
    vertex: "claude-3-5-haiku@20241022"
  },
  kw = {
    firstParty: "claude-sonnet-4-20250514",
    bedrock: "us.anthropic.claude-sonnet-4-20250514-v1:0",
    vertex: "claude-sonnet-4@20250514"
  };
var RT = {
    firstParty: "claude-opus-4-20250514",
    bedrock: "us.anthropic.claude-opus-4-20250514-v1:0",
    vertex: "claude-opus-4@20250514"
  },
  OT = {
    firstParty: "claude-opus-4-1-20250805",
    bedrock: "us.anthropic.claude-opus-4-1-20250805-v1:0",
    vertex: "claude-opus-4-1@20250805"
  };
var xM1 = F1($42(), 1);
var fw2 = F1(C70(), 1),
  hw2 = F1(k3(), 1);
var kM1 = F1(H70(), 1);
function a81() {
  return process.env.https_proxy || process.env.HTTPS_PROXY || process.env.http_proxy || process.env.HTTP_PROXY;
}
function dw2() {
  let A = a81();
  if (!A) return {};
  let B = new kM1.default.HttpsProxyAgent(A),
    Q = new hw2.NodeHttpHandler({
      httpAgent: B,
      httpsAgent: B
    });
  return {
    requestHandler: Q,
    credentials: fw2.defaultProvider({
      clientConfig: {
        requestHandler: Q
      }
    })
  };
}
var cw2 = SA(async function () {
  let A = Jp(),
    B = await x41(),
    Q = {
      region: A,
      ...dw2()
    };
  if (B) Q.credentials = {
    accessKeyId: B.accessKeyId,
    secretAccessKey: B.secretAccessKey,
    sessionToken: B.sessionToken
  };
  let D = new xM1.BedrockClient(Q),
    Z = new xM1.ListInferenceProfilesCommand();
  try {
    return ((await D.send(Z)).inferenceProfileSummaries || []).filter(W => W.inferenceProfileId?.includes("anthropic")).map(W => W.inferenceProfileId).filter(Boolean);
  } catch (G) {
    throw T1(G), G;
  }
});
function sg(A, B) {
  return A.find(Q => Q.includes(B)) ?? null;
}
function vM1(A) {
  let B = [],
    Q = !1;
  async function D() {
    if (Q) return;
    if (B.length === 0) return;
    Q = !0;
    while (B.length > 0) {
      let {
        args: Z,
        resolve: G,
        reject: F,
        context: I
      } = B.shift();
      try {
        let Y = await A.apply(I, Z);
        G(Y);
      } catch (Y) {
        F(Y);
      }
    }
    if (Q = !1, B.length > 0) D();
  }
  return function (...Z) {
    return new Promise((G, F) => {
      B.push({
        args: Z,
        resolve: G,
        reject: F,
        context: this
      }), D();
    });
  };
}
function bM1(A) {
  return {
    haiku35: f41[A],
    sonnet35: Wg[A],
    sonnet37: Yg[A],
    sonnet40: kw[A],
    opus40: RT[A],
    opus41: OT[A]
  };
}
async function _l4() {
  let A;
  try {
    A = await cw2();
  } catch (I) {
    return T1(I), bM1("bedrock");
  }
  if (!A?.length) return bM1("bedrock");
  let B = sg(A, "claude-3-5-haiku-20241022"),
    Q = sg(A, "claude-3-5-sonnet-20241022"),
    D = sg(A, "claude-3-7-sonnet-20250219"),
    Z = sg(A, "claude-sonnet-4-20250514"),
    G = sg(A, "claude-opus-4-20250514"),
    F = sg(A, "claude-opus-4-1-20250805");
  return {
    haiku35: B || f41.bedrock,
    sonnet35: Q || Wg.bedrock,
    sonnet37: D || Yg.bedrock,
    sonnet40: Z || kw.bedrock,
    opus40: G || RT.bedrock,
    opus41: F || OT.bedrock
  };
}
var xl4 = vM1(async () => {
  if (KW1() !== null) return;
  try {
    let A = await _l4();
    cu1(A);
  } catch (A) {
    T1(A);
  }
});
function vl4() {
  if (KW1() !== null) return;
  if (uD() !== "bedrock") {
    cu1(bM1(uD()));
    return;
  }
  xl4();
}
function EI() {
  let A = KW1();
  if (A === null) return vl4(), bM1(uD());
  return A;
}
var lw2 = ["sonnet", "opus", "haiku", ...[]],
  bl4 = kw,
  pw2 = bl4.firstParty,
  s81 = [...lw2, "inherit"],
  uZ0 = "sonnet";
function or() {
  let A,
    B = aA1();
  if (B !== void 0) A = B;else {
    let Q = C9() || {};
    A = process.env.ANTHROPIC_MODEL || Q.model || void 0;
  }
  if (F9() && !mY() && A?.includes("opus")) return;
  return A;
}
function IG() {
  let A = or();
  if (A !== void 0 && A !== null) return E_(A);
  if (A === null && nc()) return QP();
  return r81();
}
function iw2() {
  if (uD() === "bedrock") return EI().sonnet37;
  return EI().sonnet40;
}
function r81(A = {}) {
  let {
    forDisplay: B = !1
  } = A;
  if (mY()) return EI().opus41;
  return iw2();
}
function QP() {
  return iw2();
}
function sw2(A) {
  return lw2.includes(A);
}
function E_(A) {
  let B = A.toLowerCase().trim();
  if (sw2(B)) switch (B) {
    case "sonnet":
      return EI().sonnet40;
    case "opus":
      return EI().opus41;
    case "haiku":
      return EI().haiku35;
  }
  return B;
}
var ew2 = {},
  eg = null,
  A$2 = SA(() => {
    if (process.env.CLAUDE_CODE_USE_BEDROCK || process.env.CLAUDE_CODE_USE_VERTEX || process.env.DISABLE_TELEMETRY || process.env.CLAUDE_CODE_DISABLE_NONESSENTIAL_TRAFFIC) return null;
    let A = yl(),
      B = {
        networkConfig: {
          api: "https://statsig.anthropic.com/v1/"
        },
        environment: {
          tier: ["test", "dev"].includes("production") ? "development" : "production"
        },
        includeCurrentPageUrlWithEvents: !1,
        logLevel: mM1.LogLevel.None,
        storageProvider: new Li1(),
        customUserCacheKeyFunc: (D, Z) => {
          return gl4("sha1").update(D).update(Z.userID || "").digest("hex").slice(0, 10);
        }
      };
    eg = new mM1.StatsigClient(ol0, A, B), eg.on("error", () => {
      v9.head("https://api.anthropic.com/api/hello").catch(() => {});
    });
    let Q = eg.initializeAsync().then(() => {});
    return process.on("beforeExit", async () => {
      await eg?.flush();
    }), process.on("exit", () => {
      eg?.flush();
    }), {
      client: eg,
      initialized: Q
    };
  }),
  Au = SA(async () => {
    let A = A$2();
    if (!A) return null;
    return await A.initialized, A.client;
  });
async function ul4(A, B) {
  if (process.env.CLAUDE_CODE_USE_BEDROCK || process.env.CLAUDE_CODE_USE_VERTEX || process.env.DISABLE_TELEMETRY || process.env.CLAUDE_CODE_DISABLE_NONESSENTIAL_TRAFFIC) return;
  try {
    let Q = B.model ? String(B.model) : IG(),
      D = wV(Q),
      [Z, G, F] = await Promise.all([Au(), MA.getPackageManagers(), MA.getRuntimes()]);
    if (!Z) return;
    let I = {
        ...B,
        model: Q,
        sessionId: B9(),
        userType: "external",
        ...(D.length > 0 ? {
          betas: D.join(",")
        } : {}),
        env: JSON.stringify({
          platform: MA.platform,
          nodeVersion: MA.nodeVersion,
          terminal: MA.terminal,
          packageManagers: G.join(","),
          runtimes: F.join(","),
          isRunningWithBun: MA.isRunningWithBun(),
          isCi: gQ(!1),
          isClaubbit: process.env.CLAUBBIT === "true",
          isGithubAction: process.env.GITHUB_ACTIONS === "true",
          isClaudeCodeAction: process.env.CLAUDE_CODE_ACTION === "1" || process.env.CLAUDE_CODE_ACTION === "true",
          isClaudeAiAuth: F9(),
          version: {
            ISSUES_EXPLAINER: "report the issue at https://github.com/anthropics/claude-code/issues",
            PACKAGE_URL: "@anthropic-ai/claude-code",
            README_URL: "https://docs.anthropic.com/s/claude-code",
            VERSION: "1.0.72"
          }.VERSION,
          ...(process.env.GITHUB_ACTIONS === "true" && {
            githubEventName: process.env.GITHUB_EVENT_NAME,
            githubActionsRunnerEnvironment: process.env.RUNNER_ENVIRONMENT,
            githubActionsRunnerOs: process.env.RUNNER_OS
          }),
          ...(SB1() && {
            wslVersion: SB1()
          })
        }),
        entrypoint: process.env.CLAUDE_CODE_ENTRYPOINT,
        isInteractive: String(aj0()),
        clientType: rj0(),
        ...void 0,
        sweBenchRunId: process.env.SWE_BENCH_RUN_ID || "",
        sweBenchInstanceId: process.env.SWE_BENCH_INSTANCE_ID || "",
        sweBenchTaskId: process.env.SWE_BENCH_TASK_ID || ""
      },
      Y = {
        eventName: A,
        metadata: I
      };
    Z.logEvent(Y), await Z.flush();
  } catch (Q) {}
}
function C1(A, B) {
  ul4(A, B);
}
function Q$2() {
  return {
    ...ew2
  };
}
function cM1(A) {
  try {
    let B = yl();
    uL.setTags({
      platform: MA.platform,
      terminal: MA.terminal,
      userType: "external",
      ...Q$2()
    }), uL.setExtras({
      sessionId: B9(),
      isCI: MA.isCI,
      isTest: !1,
      packageVersion: {
        ISSUES_EXPLAINER: "report the issue at https://github.com/anthropics/claude-code/issues",
        PACKAGE_URL: "@anthropic-ai/claude-code",
        README_URL: "https://docs.anthropic.com/s/claude-code",
        VERSION: "1.0.72"
      }.VERSION
    }), uL.setUser({
      id: B.userID,
      email: B.email
    }), uL.captureException(A);
  } catch {}
}
var iM1 = [],
  sl4 = 100;
function rl4(A) {
  return A.toISOString().replace(/[:.]/g, "-");
}
var aZ0 = rl4(new Date());
function ol4() {
  return nZ0(_N.errors(), aZ0 + ".txt");
}
var iZ0 = !1;
function T1(A) {
  if (iZ0) return;
  iZ0 = !0;
  try {
    if (process.env.CLAUDE_CODE_USE_BEDROCK || process.env.CLAUDE_CODE_USE_VERTEX || process.env.DISABLE_ERROR_REPORTING || process.env.CLAUDE_CODE_DISABLE_NONESSENTIAL_TRAFFIC) return;
    if (gQ(!1)) console.error(A);
    let B = A.stack || A.message,
      Q = {
        error: B,
        timestamp: new Date().toISOString()
      };
    if (iM1.length >= sl4) iM1.shift();
    iM1.push(Q), tl4(ol4(), {
      error: B
    });
  } catch {} finally {
    iZ0 = !1;
  }
  cM1(A);
}
function tl4(A, B) {
  return;
}
var e81 = 1000,
  A51 = 60;
function X2(A, B, Q = {
  timeout: 10 * A51 * e81,
  preserveOutputOnError: !0,
  useCwd: !0
}) {
  return j5(A, B, {
    abortSignal: Q.abortSignal,
    timeout: Q.timeout,
    preserveOutputOnError: Q.preserveOutputOnError,
    cwd: Q.useCwd ? a0() : void 0,
    env: Q.env
  });
}
function j5(A, B, Q = {
  timeout: 10 * A51 * e81,
  preserveOutputOnError: !0,
  maxBuffer: 1e6
}) {
  let {
    abortSignal: D,
    timeout: Z = 10 * A51 * e81,
    preserveOutputOnError: G = !0,
    cwd: F,
    env: I
  } = Q;
  return new Promise(Y => {
    Pm1(A, B, {
      maxBuffer: Q.maxBuffer,
      signal: D,
      timeout: Z,
      cwd: F,
      env: I,
      reject: !1
    }).then(W => {
      if (W.failed) {
        if (G) {
          let J = W.exitCode ?? 1;
          Y({
            stdout: W.stdout || "",
            stderr: W.stderr || "",
            code: J,
            error: typeof W.signal === "string" ? W.signal : String(J)
          });
        } else Y({
          stdout: "",
          stderr: "",
          code: W.exitCode ?? 1
        });
      } else Y({
        stdout: W.stdout,
        stderr: W.stderr,
        code: 0
      });
    }).catch(W => {
      T1(W), Y({
        stdout: "",
        stderr: "",
        code: 1
      });
    });
  });
}
function GD(A, B, Q = 10 * A51 * e81) {
  let D;
  if (B === void 0) D = {};else if (B instanceof AbortSignal) D = {
    abortSignal: B,
    timeout: Q
  };else D = B;
  let {
    abortSignal: Z,
    timeout: G = 10 * A51 * e81
  } = D;
  Z?.throwIfAborted();
  try {
    let F = Sm1(A, {
      env: process.env,
      maxBuffer: 1e6,
      timeout: G,
      cwd: a0(),
      stdio: ["ignore", "pipe", "pipe"],
      shell: !0,
      reject: !1
    });
    if (!F.stdout) return null;
    return F.stdout.trim() || null;
  } catch {
    return null;
  }
}
function nM1(A) {
  try {
    let B = String(A),
      Q = process.platform === "win32" ? `powershell.exe -NoProfile -Command "(Get-CimInstance Win32_Process -Filter \\"ProcessId=${B}\\").ParentProcessId"` : `ps -o ppid= -p ${B}`,
      D = GD(Q, {
        timeout: 1000
      });
    return D ? D.trim() : null;
  } catch {
    return null;
  }
}
function U$2(A) {
  try {
    let B = String(A),
      Q = process.platform === "win32" ? `powershell.exe -NoProfile -Command "(Get-CimInstance Win32_Process -Filter \\"ProcessId=${B}\\").CommandLine"` : `ps -o command= -p ${B}`,
      D = GD(Q, {
        timeout: 1000
      });
    return D ? D.trim() : null;
  } catch {
    return null;
  }
}
import { join as rZ0 } from "path";
import { homedir as Ap4 } from "os";
var oZ0 = F1(Os1(), 1);
import { constants as w$2 } from "fs";
function NY() {
  if (x1().existsSync(rZ0(YQ(), ".config.json"))) return rZ0(YQ(), ".config.json");
  return rZ0(process.env.CLAUDE_CONFIG_DIR || Ap4(), ".claude.json");
}
var Bp4 = SA(async () => {
    let {
      code: A
    } = await X2("test", ["-f", "/.dockerenv"]);
    if (A !== 0) return !1;
    return process.platform === "linux";
  }),
  Qp4 = SA(async () => {
    try {
      let A = f4(),
        B = setTimeout(() => A.abort(), 1000);
      return await v9.head("http://1.1.1.1", {
        signal: A.signal
      }), clearTimeout(B), !0;
    } catch {
      return !1;
    }
  });
async function Bo(A) {
  try {
    let {
      cmd: B
    } = oZ0.findActualExecutable(A, []);
    try {
      return x1().accessSync(B, w$2.F_OK | w$2.X_OK), !0;
    } catch {
      return !1;
    }
  } catch {
    return !1;
  }
}
var Dp4 = SA(async () => {
    let A = [];
    if (await Bo("npm")) A.push("npm");
    if (await Bo("yarn")) A.push("yarn");
    if (await Bo("pnpm")) A.push("pnpm");
    return A;
  }),
  Zp4 = SA(async () => {
    let A = [];
    if (await Bo("bun")) A.push("bun");
    if (await Bo("deno")) A.push("deno");
    if (await Bo("node")) A.push("node");
    return A;
  }),
  Gp4 = SA(() => {
    if (process.versions.bun !== void 0 || process.env.BUN_INSTALL !== void 0) return !0;
    return !1;
  }),
  $$2 = SA(() => {
    try {
      return x1().existsSync("/proc/sys/fs/binfmt_misc/WSLInterop");
    } catch (A) {
      return !1;
    }
  }),
  Fp4 = SA(() => {
    try {
      if (!$$2()) return !1;
      let {
        cmd: A
      } = oZ0.findActualExecutable("npm", []);
      return A.startsWith("/mnt/c/");
    } catch (A) {
      return !1;
    }
  }),
  q$2 = ["pycharm", "intellij", "webstorm", "phpstorm", "rubymine", "clion", "goland", "rider", "datagrip", "appcode", "dataspell", "aqua", "gateway", "fleet", "jetbrains", "androidstudio"],
  Ip4 = SA(() => {
    if (process.platform === "darwin") return null;
    try {
      let B = process.pid.toString();
      for (let Q = 0; Q < 10; Q++) {
        let D = U$2(B);
        if (D) {
          let G = D.toLowerCase();
          for (let F of q$2) if (G.includes(F)) return F;
        }
        let Z = nM1(B);
        if (!Z || Z === "0" || Z === B) break;
        B = Z;
      }
    } catch {}
    return null;
  });
function Yp4() {
  if (process.env.CURSOR_TRACE_ID) return "cursor";
  if (process.env.VSCODE_GIT_ASKPASS_MAIN?.includes("/.cursor-server/")) return "cursor";
  if (process.env.VSCODE_GIT_ASKPASS_MAIN?.includes("/.windsurf-server/")) return "windsurf";
  let A = process.env.__CFBundleIdentifier?.toLowerCase();
  if (A?.includes("vscodium")) return "codium";
  if (A?.includes("windsurf")) return "windsurf";
  if (A?.includes("com.google.android.studio")) return "androidstudio";
  if (A) {
    for (let B of q$2) if (A.includes(B)) return B;
  }
  if (process.env.VisualStudioVersion) return "visualstudio";
  if (process.env.TERMINAL_EMULATOR === "JetBrains-JediTerm") {
    if (process.platform === "darwin") return "pycharm";
    return Ip4() || "pycharm";
  }
  if (process.env.TERM === "xterm-ghostty") return "ghostty";
  if (process.env.TERM?.includes("kitty")) return "kitty";
  if (process.env.TERM_PROGRAM) return process.env.TERM_PROGRAM;
  if (process.env.STY) return "screen";
  if (process.env.KONSOLE_VERSION) return "konsole";
  if (process.env.GNOME_TERMINAL_SERVICE) return "gnome-terminal";
  if (process.env.XTERM_VERSION) return "xterm";
  if (process.env.VTE_VERSION) return "vte-based";
  if (process.env.TERMINATOR_UUID) return "terminator";
  if (process.env.KITTY_WINDOW_ID) return "kitty";
  if (process.env.ALACRITTY_LOG) return "alacritty";
  if (process.env.TILIX_ID) return "tilix";
  if (process.env.WT_SESSION) return "windows-terminal";
  if (process.env.SESSIONNAME && process.env.TERM === "cygwin") return "cygwin";
  if (process.env.MSYSTEM) return process.env.MSYSTEM.toLowerCase();
  if (process.env.ConEmuTask) return "conemu";
  if (process.env.WSL_DISTRO_NAME) return `wsl-${process.env.WSL_DISTRO_NAME}`;
  if (process.env.SSH_CONNECTION || process.env.SSH_CLIENT || process.env.SSH_TTY) return "ssh-session";
  if (process.env.TERM) {
    let B = process.env.TERM;
    if (B.includes("alacritty")) return "alacritty";
    if (B.includes("rxvt")) return "rxvt";
    if (B.includes("termite")) return "termite";
    return process.env.TERM;
  }
  if (!process.stdout.isTTY) return "non-interactive";
  return null;
}
var MA = {
  getIsDocker: Bp4,
  hasInternetAccess: Qp4,
  isCI: gQ(!1),
  platform: ["win32", "darwin"].includes(process.platform) ? process.platform : "linux",
  nodeVersion: process.version,
  terminal: Yp4(),
  getPackageManagers: Dp4,
  getRuntimes: Zp4,
  isRunningWithBun: Gp4,
  isWslEnvironment: $$2,
  isNpmFromWindowsPath: Fp4
};
import { randomBytes as Jp4 } from "crypto";
var M$2 = F1(FH1(), 1);
import { execSync as Xp4 } from "child_process";
var GP = {
    allowedTools: [],
    history: [],
    mcpContextUris: [],
    mcpServers: {},
    enabledMcpjsonServers: [],
    disabledMcpjsonServers: [],
    hasTrustDialogAccepted: !1,
    hasTrustDialogHooksAccepted: !1,
    ignorePatterns: [],
    projectOnboardingSeenCount: 0,
    hasClaudeMdExternalIncludesApproved: !1,
    hasClaudeMdExternalIncludesWarningShown: !1
  },
  xV = {
    numStartups: 0,
    installMethod: void 0,
    autoUpdates: void 0,
    theme: "dark",
    preferredNotifChannel: "auto",
    verbose: !1,
    editorMode: "normal",
    autoCompactEnabled: !0,
    hasSeenTasksHint: !1,
    queuedCommandUpHintCount: 0,
    diffTool: "auto",
    customApiKeyResponses: {
      approved: [],
      rejected: []
    },
    env: {},
    tipsHistory: {},
    memoryUsageCount: 0,
    promptQueueUseCount: 0,
    todoFeatureEnabled: !0,
    messageIdleNotifThresholdMs: 60000,
    autoConnectIde: !1,
    autoInstallIdeExtension: !0,
    checkpointingEnabled: !0
  },
  Q51 = ["apiKeyHelper", "installMethod", "autoUpdates", "theme", "verbose", "preferredNotifChannel", "shiftEnterKeyBindingInstalled", "editorMode", "hasUsedBackslashReturn", "supervisorMode", "autoCompactEnabled", "diffTool", "env", "tipsHistory", "todoFeatureEnabled", "messageIdleNotifThresholdMs", "autoConnectIde", "autoInstallIdeExtension", "checkpointingEnabled"];
function pA(A) {
  try {
    S$2(NY(), xV, B => ({
      ...A,
      projects: B.projects
    })), ZP.config = null, ZP.mtime = 0;
  } catch (B) {
    R2(`Failed to save config with lock: ${B}`), P$2(NY(), {
      ...A,
      projects: U_(NY(), xV).projects
    }, xV), ZP.config = null, ZP.mtime = 0;
  }
}
var ZP = {
  config: null,
  mtime: 0
};
function tZ0(A) {
  if (A.installMethod !== void 0) return A;
  let B = "unknown",
    Q = A.autoUpdates ?? !0;
  switch (A.autoUpdaterStatus) {
    case "migrated":
      B = "local";
      break;
    case "installed":
      B = "native";
      break;
    case "disabled":
      Q = !1;
      break;
    case "enabled":
    case "no_permissions":
    case "not_configured":
      B = "global";
      break;
    case void 0:
      break;
  }
  return {
    ...A,
    installMethod: B,
    autoUpdates: Q
  };
}
function E0() {
  try {
    let A = x1().existsSync(NY()) ? x1().statSync(NY()) : null;
    if (ZP.config && A) {
      if (A.mtimeMs <= ZP.mtime) return ZP.config;
    }
    let B = tZ0(U_(NY(), xV));
    if (A) ZP = {
      config: B,
      mtime: A.mtimeMs
    };else ZP = {
      config: B,
      mtime: Date.now()
    };
    return tZ0(B);
  } catch {
    return tZ0(U_(NY(), xV));
  }
}
function P$2(A, B, Q) {
  let D = L$2(A),
    Z = x1();
  if (!Z.existsSync(D)) Z.mkdirSync(D);
  let G = Object.fromEntries(Object.entries(B).filter(([F, I]) => JSON.stringify(I) !== JSON.stringify(Q[F])));
  kN(A, JSON.stringify(G, null, 2));
}
function S$2(A, B, Q) {
  let D = L$2(A),
    Z = x1();
  if (!Z.existsSync(D)) Z.mkdirSync(D);
  let G;
  try {
    let F = `${A}.lock`,
      I = Date.now();
    if (G = M$2.lockSync(A, {
      lockfilePath: F
    }), Date.now() - I > 100) I0("Lock acquisition took longer than expected - another Claude instance may be running");
    let W = U_(A, B),
      J = Q(W),
      X = Object.fromEntries(Object.entries(J).filter(([V, C]) => JSON.stringify(C) !== JSON.stringify(B[V])));
    if (Z.existsSync(A)) try {
      let V = `${A}.backup`;
      Z.copyFileSync(A, V);
    } catch (V) {
      R2(`Failed to backup config: ${V}`);
    }
    kN(A, JSON.stringify(X, null, 2));
  } finally {
    if (G) G();
  }
}
var eZ0 = !1;
function U_(A, B, Q) {
  if (!eZ0) throw new Error("Config accessed before allowed.");
  let D = x1();
  if (!D.existsSync(A)) {
    let Z = `${A}.backup`;
    if (D.existsSync(Z)) process.stdout.write(`
Claude configuration file not found at: ${A}
A backup file exists at: ${Z}
You can manually restore it by running: cp "${Z}" "${A}"

`);
    return lA1(B);
  }
  try {
    let Z = D.readFileSync(A, {
      encoding: "utf-8"
    });
    try {
      let G = JSON.parse(Z);
      return {
        ...lA1(B),
        ...G
      };
    } catch (G) {
      let F = G instanceof Error ? G.message : String(G);
      throw new Bh(F, A, B);
    }
  } catch (Z) {
    if (Z instanceof Bh && Q) throw Z;
    if (Z instanceof Bh) {
      R2(`Config file corrupted, resetting to defaults: ${Z.message}`), T1(Z), process.stdout.write(`
Claude configuration file at ${A} is corrupted: ${Z.message}
`);
      let G = `${A}.corrupted.${Date.now()}`;
      try {
        D.copyFileSync(A, G), R2(`Corrupted config backed up to: ${G}`);
      } catch {}
      let F = `${A}.backup`;
      if (process.stdout.write(`
Claude configuration file at ${A} is corrupted
The corrupted file has been backed up to: ${G}
`), D.existsSync(F)) process.stdout.write(`A backup file exists at: ${F}
You can manually restore it by running: cp "${F}" "${A}"

`);else process.stdout.write(`
`);
    }
    return lA1(B);
  }
}
var jK1 = SA(() => {
  let A = x9();
  try {
    return Wp4(Xp4("git rev-parse --show-toplevel", {
      cwd: A,
      encoding: "utf8",
      stdio: ["pipe", "pipe", "ignore"]
    }).trim());
  } catch {
    return N$2(A);
  }
});
function t9() {
  let A = jK1(),
    B = U_(NY(), xV);
  if (!B.projects) return GP;
  let Q = B.projects[A] ?? GP;
  if (typeof Q.allowedTools === "string") Q.allowedTools = T7(Q.allowedTools) ?? [];
  return Q;
}
function kl() {
  let A = E0();
  if (A.userID) return A.userID;
  let B = Jp4(32).toString("hex");
  return pA({
    ...A,
    userID: B
  }), B;
}
import { Stream as Es4 } from "node:stream";
var UM2 = F1(w1(), 1);
function f$2(A, B, {
  signal: Q,
  edges: D
} = {}) {
  let Z = void 0,
    G = null,
    F = D != null && D.includes("leading"),
    I = D == null || D.includes("trailing"),
    Y = () => {
      if (G !== null) A.apply(Z, G), Z = void 0, G = null;
    },
    W = () => {
      if (I) Y();
      C();
    },
    J = null,
    X = () => {
      if (J != null) clearTimeout(J);
      J = setTimeout(() => {
        J = null, W();
      }, B);
    },
    V = () => {
      if (J !== null) clearTimeout(J), J = null;
    },
    C = () => {
      V(), Z = void 0, G = null;
    },
    K = () => {
      V(), Y();
    },
    H = function (...z) {
      if (Q?.aborted) return;
      Z = this, G = z;
      let $ = J == null;
      if (X(), F && $) Y();
    };
  return H.schedule = X, H.cancel = C, H.flush = K, Q?.addEventListener("abort", C, {
    once: !0
  }), H;
}
function h$2(A, B = 0, Q = {}) {
  if (typeof Q !== "object") Q = {};
  let {
      signal: D,
      leading: Z = !1,
      trailing: G = !0,
      maxWait: F
    } = Q,
    I = Array(2);
  if (Z) I[0] = "leading";
  if (G) I[1] = "trailing";
  let Y = void 0,
    W = null,
    J = f$2(function (...C) {
      Y = A.apply(this, C), W = null;
    }, B, {
      signal: D,
      edges: I
    }),
    X = function (...C) {
      if (F != null) {
        if (W === null) W = Date.now();else if (Date.now() - W >= F) return Y = A.apply(this, C), W = Date.now(), J.cancel(), J.schedule(), Y;
      }
      return J.apply(this, C), Y;
    },
    V = () => {
      return J.flush(), Y;
    };
  return X.cancel = J.cancel, X.flush = V, X;
}
function oM1(A, B = 0, Q = {}) {
  if (typeof Q !== "object") Q = {};
  let {
    leading: D = !0,
    trailing: Z = !0,
    signal: G
  } = Q;
  return h$2(A, B, {
    leading: D,
    trailing: Z,
    signal: G,
    maxWait: B
  });
}
var w_ = {};
var lp4 = A => {
  let B = new Set();
  do for (let Q of Reflect.ownKeys(A)) B.add([A, Q]); while ((A = Reflect.getPrototypeOf(A)) && A !== Object.prototype);
  return B;
};
function GG0(A, {
  include: B,
  exclude: Q
} = {}) {
  let D = Z => {
    let G = F => typeof F === "string" ? Z === F : F.test(Z);
    if (B) return B.some(G);
    if (Q) return !Q.some(G);
    return !0;
  };
  for (let [Z, G] of lp4(A.constructor.prototype)) {
    if (G === "constructor" || !D(G)) continue;
    let F = Reflect.getOwnPropertyDescriptor(Z, G);
    if (F && typeof F.value === "function") A[G] = A[G].bind(A);
  }
  return A;
}
import { PassThrough as c$2 } from "node:stream";
var l$2 = ["assert", "count", "countReset", "debug", "dir", "dirxml", "error", "group", "groupCollapsed", "groupEnd", "info", "log", "table", "time", "timeEnd", "timeLog", "trace", "warn"],
  FG0 = {},
  pp4 = A => {
    let B = new c$2(),
      Q = new c$2();
    B.write = Z => {
      A("stdout", Z);
    }, Q.write = Z => {
      A("stderr", Z);
    };
    let D = new console.Console(B, Q);
    for (let Z of l$2) FG0[Z] = console[Z], console[Z] = D[Z];
    return () => {
      for (let Z of l$2) console[Z] = FG0[Z];
      FG0 = {};
    };
  },
  p$2 = pp4;
var zL2 = F1(Aq2(), 1);
var UG0 = 16;
var p2 = {},
  FR1 = p2.ALIGN_AUTO = 0,
  J51 = p2.ALIGN_FLEX_START = 1,
  X51 = p2.ALIGN_CENTER = 2,
  V51 = p2.ALIGN_FLEX_END = 3,
  IR1 = p2.ALIGN_STRETCH = 4,
  Bq2 = p2.ALIGN_BASELINE = 5,
  Qq2 = p2.ALIGN_SPACE_BETWEEN = 6,
  Dq2 = p2.ALIGN_SPACE_AROUND = 7,
  Zq2 = p2.DIMENSION_WIDTH = 0,
  Gq2 = p2.DIMENSION_HEIGHT = 1,
  Fq2 = p2.DIRECTION_INHERIT = 0,
  Iq2 = p2.DIRECTION_LTR = 1,
  Yq2 = p2.DIRECTION_RTL = 2,
  Fo = p2.DISPLAY_FLEX = 0,
  q_ = p2.DISPLAY_NONE = 1,
  cL = p2.EDGE_LEFT = 0,
  N_ = p2.EDGE_TOP = 1,
  lL = p2.EDGE_RIGHT = 2,
  L_ = p2.EDGE_BOTTOM = 3,
  YR1 = p2.EDGE_START = 4,
  WR1 = p2.EDGE_END = 5,
  C51 = p2.EDGE_HORIZONTAL = 6,
  K51 = p2.EDGE_VERTICAL = 7,
  H51 = p2.EDGE_ALL = 8,
  Wq2 = p2.EXPERIMENTAL_FEATURE_WEB_FLEX_BASIS = 0,
  Jq2 = p2.EXPERIMENTAL_FEATURE_ABSOLUTE_PERCENTAGE_AGAINST_PADDING_EDGE = 1,
  Xq2 = p2.EXPERIMENTAL_FEATURE_FIX_ABSOLUTE_TRAILING_COLUMN_MARGIN = 2,
  JR1 = p2.FLEX_DIRECTION_COLUMN = 0,
  XR1 = p2.FLEX_DIRECTION_COLUMN_REVERSE = 1,
  VR1 = p2.FLEX_DIRECTION_ROW = 2,
  CR1 = p2.FLEX_DIRECTION_ROW_REVERSE = 3,
  KR1 = p2.GUTTER_COLUMN = 0,
  HR1 = p2.GUTTER_ROW = 1,
  zR1 = p2.GUTTER_ALL = 2,
  ER1 = p2.JUSTIFY_FLEX_START = 0,
  UR1 = p2.JUSTIFY_CENTER = 1,
  wR1 = p2.JUSTIFY_FLEX_END = 2,
  $R1 = p2.JUSTIFY_SPACE_BETWEEN = 3,
  qR1 = p2.JUSTIFY_SPACE_AROUND = 4,
  NR1 = p2.JUSTIFY_SPACE_EVENLY = 5,
  Vq2 = p2.LOG_LEVEL_ERROR = 0,
  Cq2 = p2.LOG_LEVEL_WARN = 1,
  Kq2 = p2.LOG_LEVEL_INFO = 2,
  Hq2 = p2.LOG_LEVEL_DEBUG = 3,
  zq2 = p2.LOG_LEVEL_VERBOSE = 4,
  Eq2 = p2.LOG_LEVEL_FATAL = 5,
  Uq2 = p2.MEASURE_MODE_UNDEFINED = 0,
  wq2 = p2.MEASURE_MODE_EXACTLY = 1,
  $q2 = p2.MEASURE_MODE_AT_MOST = 2,
  qq2 = p2.NODE_TYPE_DEFAULT = 0,
  Nq2 = p2.NODE_TYPE_TEXT = 1,
  Lq2 = p2.OVERFLOW_VISIBLE = 0,
  Mq2 = p2.OVERFLOW_HIDDEN = 1,
  Rq2 = p2.OVERFLOW_SCROLL = 2,
  Oq2 = p2.POSITION_TYPE_STATIC = 0,
  LR1 = p2.POSITION_TYPE_RELATIVE = 1,
  MR1 = p2.POSITION_TYPE_ABSOLUTE = 2,
  Tq2 = p2.PRINT_OPTIONS_LAYOUT = 1,
  Pq2 = p2.PRINT_OPTIONS_STYLE = 2,
  Sq2 = p2.PRINT_OPTIONS_CHILDREN = 4,
  jq2 = p2.UNIT_UNDEFINED = 0,
  yq2 = p2.UNIT_POINT = 1,
  kq2 = p2.UNIT_PERCENT = 2,
  _q2 = p2.UNIT_AUTO = 3,
  RR1 = p2.WRAP_NO_WRAP = 0,
  OR1 = p2.WRAP_WRAP = 1,
  TR1 = p2.WRAP_WRAP_REVERSE = 2;
var xq2 = A => {
  function B(Z, G, F) {
    let I = Z[G];
    Z[G] = function (...Y) {
      return F.call(this, I, ...Y);
    };
  }
  for (let Z of ["setPosition", "setMargin", "setFlexBasis", "setWidth", "setHeight", "setMinWidth", "setMinHeight", "setMaxWidth", "setMaxHeight", "setPadding"]) {
    let G = {
      [p2.UNIT_POINT]: A.Node.prototype[Z],
      [p2.UNIT_PERCENT]: A.Node.prototype[`${Z}Percent`],
      [p2.UNIT_AUTO]: A.Node.prototype[`${Z}Auto`]
    };
    B(A.Node.prototype, Z, function (F, ...I) {
      let Y,
        W,
        J = I.pop();
      if (J === "auto") Y = p2.UNIT_AUTO, W = void 0;else if (typeof J == "object") Y = J.unit, W = J.valueOf();else if (Y = typeof J == "string" && J.endsWith("%") ? p2.UNIT_PERCENT : p2.UNIT_POINT, W = parseFloat(J), !Number.isNaN(J) && Number.isNaN(W)) throw Error(`Invalid value ${J} for ${Z}`);
      if (!G[Y]) throw Error(`Failed to execute "${Z}": Unsupported unit '${J}'`);
      return W !== void 0 ? G[Y].call(this, ...I, W) : G[Y].call(this, ...I);
    });
  }
  function Q(Z) {
    return A.MeasureCallback.implement({
      measure: (...G) => {
        let {
          width: F,
          height: I
        } = Z(...G);
        return {
          width: F ?? NaN,
          height: I ?? NaN
        };
      }
    });
  }
  function D(Z) {
    return A.DirtiedCallback.implement({
      dirtied: Z
    });
  }
  return B(A.Node.prototype, "setMeasureFunc", function (Z, G) {
    return G ? Z.call(this, Q(G)) : this.unsetMeasureFunc();
  }), B(A.Node.prototype, "setDirtiedFunc", function (Z, G) {
    Z.call(this, D(G));
  }), B(A.Config.prototype, "free", function () {
    A.Config.destroy(this);
  }), B(A.Node, "create", (Z, G) => G ? A.Node.createWithConfig(G) : A.Node.createDefault()), B(A.Node.prototype, "free", function () {
    A.Node.destroy(this);
  }), B(A.Node.prototype, "freeRecursive", function () {
    for (let Z = 0, G = this.getChildCount(); Z < G; ++Z) this.getChild(0).freeRecursive();
    this.free();
  }), B(A.Node.prototype, "calculateLayout", function (Z, G = NaN, F = NaN, I = p2.DIRECTION_LTR) {
    return Z.call(this, G, F, I);
  }), {
    Config: A.Config,
    Node: A.Node,
    ...p2
  };
};
var Vi4 = (() => {
  var A = typeof document != "undefined" && document.currentScript ? document.currentScript.src : void 0;
  return function (B = {}) {
    Y || (Y = B !== void 0 ? B : {}), Y.ready = new Promise(function (y1, u1) {
      W = y1, J = u1;
    });
    var Q,
      D,
      Z = Object.assign({}, Y),
      G = "";
    typeof document != "undefined" && document.currentScript && (G = document.currentScript.src), A && (G = A), G = G.indexOf("blob:") !== 0 ? G.substr(0, G.replace(/[?#].*/, "").lastIndexOf("/") + 1) : "";
    var F = console.log.bind(console),
      I = console.warn.bind(console);
    Object.assign(Y, Z), Z = null, typeof WebAssembly != "object" && n("no native wasm support detected");
    var Y,
      W,
      J,
      X,
      V = !1;
    function C(y1, u1, N0) {
      N0 = u1 + N0;
      for (var x0 = ""; !(u1 >= N0);) {
        var w0 = y1[u1++];
        if (!w0) break;
        if (128 & w0) {
          var v0 = 63 & y1[u1++];
          if ((224 & w0) == 192) x0 += String.fromCharCode((31 & w0) << 6 | v0);else {
            var HA = 63 & y1[u1++];
            65536 > (w0 = (240 & w0) == 224 ? (15 & w0) << 12 | v0 << 6 | HA : (7 & w0) << 18 | v0 << 12 | HA << 6 | 63 & y1[u1++]) ? x0 += String.fromCharCode(w0) : (w0 -= 65536, x0 += String.fromCharCode(55296 | w0 >> 10, 56320 | 1023 & w0));
          }
        } else x0 += String.fromCharCode(w0);
      }
      return x0;
    }
    function K() {
      var y1 = X.buffer;
      Y.HEAP8 = H = new Int8Array(y1), Y.HEAP16 = $ = new Int16Array(y1), Y.HEAP32 = N = new Int32Array(y1), Y.HEAPU8 = z = new Uint8Array(y1), Y.HEAPU16 = L = new Uint16Array(y1), Y.HEAPU32 = O = new Uint32Array(y1), Y.HEAPF32 = R = new Float32Array(y1), Y.HEAPF64 = T = new Float64Array(y1);
    }
    var H,
      z,
      $,
      L,
      N,
      O,
      R,
      T,
      j,
      f = [],
      y = [],
      c = [],
      h = 0,
      a = null;
    function n(y1) {
      throw I(y1 = "Aborted(" + y1 + ")"), V = !0, J(y1 = new WebAssembly.RuntimeError(y1 + ". Build with -sASSERTIONS for more info.")), y1;
    }
    function v() {
      return Q.startsWith("data:application/octet-stream;base64,");
    }
    function t() {
      try {
        throw "both async and sync fetching of the wasm failed";
      } catch (y1) {
        n(y1);
      }
    }
    function W1(y1) {
      for (; 0 < y1.length;) y1.shift()(Y);
    }
    function z1(y1) {
      if (y1 === void 0) return "_unknown";
      var u1 = (y1 = y1.replace(/[^a-zA-Z0-9_]/g, "$")).charCodeAt(0);
      return 48 <= u1 && 57 >= u1 ? "_" + y1 : y1;
    }
    function f1(y1, u1) {
      return y1 = z1(y1), function () {
        return u1.apply(this, arguments);
      };
    }
    Q = "yoga.wasm", v() || (Q = G + Q);
    var G0 = [{}, {
        value: void 0
      }, {
        value: null
      }, {
        value: !0
      }, {
        value: !1
      }],
      X0 = [];
    function g1(y1) {
      var u1 = Error,
        N0 = f1(y1, function (x0) {
          this.name = y1, this.message = x0, (x0 = Error(x0).stack) !== void 0 && (this.stack = this.toString() + `
` + x0.replace(/^Error(:[^\n]*)?\n/, ""));
        });
      return N0.prototype = Object.create(u1.prototype), N0.prototype.constructor = N0, N0.prototype.toString = function () {
        return this.message === void 0 ? this.name : this.name + ": " + this.message;
      }, N0;
    }
    var K1 = void 0;
    function Q1(y1) {
      throw new K1(y1);
    }
    var _1 = y1 => (y1 || Q1("Cannot use deleted val. handle = " + y1), G0[y1].value),
      q1 = y1 => {
        switch (y1) {
          case void 0:
            return 1;
          case null:
            return 2;
          case !0:
            return 3;
          case !1:
            return 4;
          default:
            var u1 = X0.length ? X0.pop() : G0.length;
            return G0[u1] = {
              fa: 1,
              value: y1
            }, u1;
        }
      },
      B0 = void 0,
      K0 = void 0;
    function s1(y1) {
      for (var u1 = ""; z[y1];) u1 += K0[z[y1++]];
      return u1;
    }
    var A1 = [];
    function D1() {
      for (; A1.length;) {
        var y1 = A1.pop();
        y1.L.Z = !1, y1.delete();
      }
    }
    var I1 = void 0,
      E1 = {};
    function M1(y1, u1) {
      for (u1 === void 0 && Q1("ptr should not be undefined"); y1.P;) u1 = y1.aa(u1), y1 = y1.P;
      return u1;
    }
    var B1 = {};
    function b1(y1) {
      var u1 = s1(y1 = R4(y1));
      return t2(y1), u1;
    }
    function c1(y1, u1) {
      var N0 = B1[y1];
      return N0 === void 0 && Q1(u1 + " has unknown type " + b1(y1)), N0;
    }
    function n1() {}
    var C0 = !1;
    function W0(y1) {
      --y1.count.value, y1.count.value === 0 && (y1.S ? y1.T.V(y1.S) : y1.O.M.V(y1.N));
    }
    var O0 = {},
      zA = void 0;
    function d0(y1) {
      throw new zA(y1);
    }
    function YA(y1, u1) {
      return u1.O && u1.N || d0("makeClassHandle requires ptr and ptrType"), !!u1.T != !!u1.S && d0("Both smartPtrType and smartPtr must be specified"), u1.count = {
        value: 1
      }, w2(Object.create(y1, {
        L: {
          value: u1
        }
      }));
    }
    function w2(y1) {
      return typeof FinalizationRegistry == "undefined" ? (w2 = u1 => u1, y1) : (C0 = new FinalizationRegistry(u1 => {
        W0(u1.L);
      }), w2 = u1 => {
        var N0 = u1.L;
        return N0.S && C0.register(u1, {
          L: N0
        }, u1), u1;
      }, n1 = u1 => {
        C0.unregister(u1);
      }, w2(y1));
    }
    var $2 = {};
    function r2(y1) {
      for (; y1.length;) {
        var u1 = y1.pop();
        y1.pop()(u1);
      }
    }
    function C2(y1) {
      return this.fromWireType(N[y1 >> 2]);
    }
    var zB = {},
      f6 = {};
    function kA(y1, u1, N0) {
      function x0(QA) {
        (QA = N0(QA)).length !== y1.length && d0("Mismatched type converter count");
        for (var WA = 0; WA < y1.length; ++WA) M2(y1[WA], QA[WA]);
      }
      y1.forEach(function (QA) {
        f6[QA] = u1;
      });
      var w0 = Array(u1.length),
        v0 = [],
        HA = 0;
      u1.forEach((QA, WA) => {
        B1.hasOwnProperty(QA) ? w0[WA] = B1[QA] : (v0.push(QA), zB.hasOwnProperty(QA) || (zB[QA] = []), zB[QA].push(() => {
          w0[WA] = B1[QA], ++HA === v0.length && x0(w0);
        }));
      }), v0.length === 0 && x0(w0);
    }
    function I2(y1) {
      switch (y1) {
        case 1:
          return 0;
        case 2:
          return 1;
        case 4:
          return 2;
        case 8:
          return 3;
        default:
          throw TypeError("Unknown type size: " + y1);
      }
    }
    function M2(y1, u1, N0 = {}) {
      if (!("argPackAdvance" in u1)) throw TypeError("registerType registeredInstance requires argPackAdvance");
      var x0 = u1.name;
      if (y1 || Q1('type "' + x0 + '" must have a positive integer typeid pointer'), B1.hasOwnProperty(y1)) {
        if (N0.ta) return;
        Q1("Cannot register type '" + x0 + "' twice");
      }
      B1[y1] = u1, delete f6[y1], zB.hasOwnProperty(y1) && (u1 = zB[y1], delete zB[y1], u1.forEach(w0 => w0()));
    }
    function nA(y1) {
      Q1(y1.L.O.M.name + " instance already deleted");
    }
    function aA() {}
    function o2(y1, u1, N0) {
      if (y1[u1].R === void 0) {
        var x0 = y1[u1];
        y1[u1] = function () {
          return y1[u1].R.hasOwnProperty(arguments.length) || Q1("Function '" + N0 + "' called with an invalid number of arguments (" + arguments.length + ") - expects one of (" + y1[u1].R + ")!"), y1[u1].R[arguments.length].apply(this, arguments);
        }, y1[u1].R = [], y1[u1].R[x0.Y] = x0;
      }
    }
    function fB(y1, u1, N0, x0, w0, v0, HA, QA) {
      this.name = y1, this.constructor = u1, this.W = N0, this.V = x0, this.P = w0, this.oa = v0, this.aa = HA, this.ma = QA, this.ia = [];
    }
    function l6(y1, u1, N0) {
      for (; u1 !== N0;) u1.aa || Q1("Expected null or instance of " + N0.name + ", got an instance of " + u1.name), y1 = u1.aa(y1), u1 = u1.P;
      return y1;
    }
    function $3(y1, u1) {
      return u1 === null ? (this.da && Q1("null is not a valid " + this.name), 0) : (u1.L || Q1('Cannot pass "' + q7(u1) + '" as a ' + this.name), u1.L.N || Q1("Cannot pass deleted object as a pointer of type " + this.name), l6(u1.L.N, u1.L.O.M, this.M));
    }
    function rQ(y1, u1) {
      if (u1 === null) {
        if (this.da && Q1("null is not a valid " + this.name), this.ca) {
          var N0 = this.ea();
          return y1 !== null && y1.push(this.V, N0), N0;
        }
        return 0;
      }
      if (u1.L || Q1('Cannot pass "' + q7(u1) + '" as a ' + this.name), u1.L.N || Q1("Cannot pass deleted object as a pointer of type " + this.name), !this.ba && u1.L.O.ba && Q1("Cannot convert argument of type " + (u1.L.T ? u1.L.T.name : u1.L.O.name) + " to parameter type " + this.name), N0 = l6(u1.L.N, u1.L.O.M, this.M), this.ca) switch (u1.L.S === void 0 && Q1("Passing raw pointer to smart pointer is illegal"), this.Aa) {
        case 0:
          u1.L.T === this ? N0 = u1.L.S : Q1("Cannot convert argument of type " + (u1.L.T ? u1.L.T.name : u1.L.O.name) + " to parameter type " + this.name);
          break;
        case 1:
          N0 = u1.L.S;
          break;
        case 2:
          if (u1.L.T === this) N0 = u1.L.S;else {
            var x0 = u1.clone();
            N0 = this.wa(N0, q1(function () {
              x0.delete();
            })), y1 !== null && y1.push(this.V, N0);
          }
          break;
        default:
          Q1("Unsupporting sharing policy");
      }
      return N0;
    }
    function tB(y1, u1) {
      return u1 === null ? (this.da && Q1("null is not a valid " + this.name), 0) : (u1.L || Q1('Cannot pass "' + q7(u1) + '" as a ' + this.name), u1.L.N || Q1("Cannot pass deleted object as a pointer of type " + this.name), u1.L.O.ba && Q1("Cannot convert argument of type " + u1.L.O.name + " to parameter type " + this.name), l6(u1.L.N, u1.L.O.M, this.M));
    }
    function $6(y1, u1, N0, x0) {
      this.name = y1, this.M = u1, this.da = N0, this.ba = x0, this.ca = !1, this.V = this.wa = this.ea = this.ja = this.Aa = this.va = void 0, u1.P !== void 0 ? this.toWireType = rQ : (this.toWireType = x0 ? $3 : tB, this.U = null);
    }
    var j8 = [];
    function R5(y1) {
      var u1 = j8[y1];
      return u1 || (y1 >= j8.length && (j8.length = y1 + 1), j8[y1] = u1 = j.get(y1)), u1;
    }
    function p6(y1, u1) {
      var N0,
        x0,
        w0 = (y1 = s1(y1)).includes("j") ? (N0 = y1, x0 = [], function () {
          if (x0.length = 0, Object.assign(x0, arguments), N0.includes("j")) {
            var v0 = Y["dynCall_" + N0];
            v0 = x0 && x0.length ? v0.apply(null, [u1].concat(x0)) : v0.call(null, u1);
          } else v0 = R5(u1).apply(null, x0);
          return v0;
        }) : R5(u1);
      return typeof w0 != "function" && Q1("unknown function pointer with signature " + y1 + ": " + u1), w0;
    }
    var h5 = void 0;
    function $7(y1, u1) {
      var N0 = [],
        x0 = {};
      throw u1.forEach(function w0(v0) {
        x0[v0] || B1[v0] || (f6[v0] ? f6[v0].forEach(w0) : (N0.push(v0), x0[v0] = !0));
      }), new h5(y1 + ": " + N0.map(b1).join([", "]));
    }
    function l3(y1, u1, N0, x0, w0) {
      var v0 = u1.length;
      2 > v0 && Q1("argTypes array size mismatch! Must at least get return value and 'this' types!");
      var HA = u1[1] !== null && N0 !== null,
        QA = !1;
      for (N0 = 1; N0 < u1.length; ++N0) if (u1[N0] !== null && u1[N0].U === void 0) {
        QA = !0;
        break;
      }
      var WA = u1[0].name !== "void",
        e0 = v0 - 2,
        XA = Array(e0),
        hB = [],
        f2 = [];
      return function () {
        if (arguments.length !== e0 && Q1("function " + y1 + " called with " + arguments.length + " arguments, expected " + e0 + " args!"), f2.length = 0, hB.length = HA ? 2 : 1, hB[0] = w0, HA) {
          var gB = u1[1].toWireType(f2, this);
          hB[1] = gB;
        }
        for (var U1 = 0; U1 < e0; ++U1) XA[U1] = u1[U1 + 2].toWireType(f2, arguments[U1]), hB.push(XA[U1]);
        if (U1 = x0.apply(null, hB), QA) r2(f2);else for (var t1 = HA ? 1 : 2; t1 < u1.length; t1++) {
          var d1 = t1 === 1 ? gB : XA[t1 - 2];
          u1[t1].U !== null && u1[t1].U(d1);
        }
        return WA ? u1[0].fromWireType(U1) : void 0;
      };
    }
    function c7(y1, u1) {
      for (var N0 = [], x0 = 0; x0 < y1; x0++) N0.push(O[u1 + 4 * x0 >> 2]);
      return N0;
    }
    function y4(y1) {
      4 < y1 && --G0[y1].fa == 0 && (G0[y1] = void 0, X0.push(y1));
    }
    function q7(y1) {
      if (y1 === null) return "null";
      var u1 = typeof y1;
      return u1 === "object" || u1 === "array" || u1 === "function" ? y1.toString() : "" + y1;
    }
    function SZ(y1, u1) {
      for (var N0 = "", x0 = 0; !(x0 >= u1 / 2); ++x0) {
        var w0 = $[y1 + 2 * x0 >> 1];
        if (w0 == 0) break;
        N0 += String.fromCharCode(w0);
      }
      return N0;
    }
    function K2(y1, u1, N0) {
      if (N0 === void 0 && (N0 = 2147483647), 2 > N0) return 0;
      N0 -= 2;
      var x0 = u1;
      N0 = N0 < 2 * y1.length ? N0 / 2 : y1.length;
      for (var w0 = 0; w0 < N0; ++w0) $[u1 >> 1] = y1.charCodeAt(w0), u1 += 2;
      return $[u1 >> 1] = 0, u1 - x0;
    }
    function i1(y1) {
      return 2 * y1.length;
    }
    function N1(y1, u1) {
      for (var N0 = 0, x0 = ""; !(N0 >= u1 / 4);) {
        var w0 = N[y1 + 4 * N0 >> 2];
        if (w0 == 0) break;
        ++N0, 65536 <= w0 ? (w0 -= 65536, x0 += String.fromCharCode(55296 | w0 >> 10, 56320 | 1023 & w0)) : x0 += String.fromCharCode(w0);
      }
      return x0;
    }
    function Q0(y1, u1, N0) {
      if (N0 === void 0 && (N0 = 2147483647), 4 > N0) return 0;
      var x0 = u1;
      N0 = x0 + N0 - 4;
      for (var w0 = 0; w0 < y1.length; ++w0) {
        var v0 = y1.charCodeAt(w0);
        if (55296 <= v0 && 57343 >= v0 && (v0 = 65536 + ((1023 & v0) << 10) | 1023 & y1.charCodeAt(++w0)), N[u1 >> 2] = v0, (u1 += 4) + 4 > N0) break;
      }
      return N[u1 >> 2] = 0, u1 - x0;
    }
    function h0(y1) {
      for (var u1 = 0, N0 = 0; N0 < y1.length; ++N0) {
        var x0 = y1.charCodeAt(N0);
        55296 <= x0 && 57343 >= x0 && ++N0, u1 += 4;
      }
      return u1;
    }
    var i0 = {};
    function cA(y1) {
      var u1 = i0[y1];
      return u1 === void 0 ? s1(y1) : u1;
    }
    var iB = [],
      h9 = [],
      BQ = [null, [], []];
    K1 = Y.BindingError = g1("BindingError"), Y.count_emval_handles = function () {
      for (var y1 = 0, u1 = 5; u1 < G0.length; ++u1) G0[u1] !== void 0 && ++y1;
      return y1;
    }, Y.get_first_emval = function () {
      for (var y1 = 5; y1 < G0.length; ++y1) if (G0[y1] !== void 0) return G0[y1];
      return null;
    }, B0 = Y.PureVirtualError = g1("PureVirtualError");
    for (var V4 = Array(256), z9 = 0; 256 > z9; ++z9) V4[z9] = String.fromCharCode(z9);
    K0 = V4, Y.getInheritedInstanceCount = function () {
      return Object.keys(E1).length;
    }, Y.getLiveInheritedInstances = function () {
      var y1,
        u1 = [];
      for (y1 in E1) E1.hasOwnProperty(y1) && u1.push(E1[y1]);
      return u1;
    }, Y.flushPendingDeletes = D1, Y.setDelayFunction = function (y1) {
      I1 = y1, A1.length && I1 && I1(D1);
    }, zA = Y.InternalError = g1("InternalError"), aA.prototype.isAliasOf = function (y1) {
      if (!(this instanceof aA && y1 instanceof aA)) return !1;
      var u1 = this.L.O.M,
        N0 = this.L.N,
        x0 = y1.L.O.M;
      for (y1 = y1.L.N; u1.P;) N0 = u1.aa(N0), u1 = u1.P;
      for (; x0.P;) y1 = x0.aa(y1), x0 = x0.P;
      return u1 === x0 && N0 === y1;
    }, aA.prototype.clone = function () {
      if (this.L.N || nA(this), this.L.$) return this.L.count.value += 1, this;
      var y1 = w2,
        u1 = Object,
        N0 = u1.create,
        x0 = Object.getPrototypeOf(this),
        w0 = this.L;
      return y1 = y1(N0.call(u1, x0, {
        L: {
          value: {
            count: w0.count,
            Z: w0.Z,
            $: w0.$,
            N: w0.N,
            O: w0.O,
            S: w0.S,
            T: w0.T
          }
        }
      })), y1.L.count.value += 1, y1.L.Z = !1, y1;
    }, aA.prototype.delete = function () {
      this.L.N || nA(this), this.L.Z && !this.L.$ && Q1("Object already scheduled for deletion"), n1(this), W0(this.L), this.L.$ || (this.L.S = void 0, this.L.N = void 0);
    }, aA.prototype.isDeleted = function () {
      return !this.L.N;
    }, aA.prototype.deleteLater = function () {
      return this.L.N || nA(this), this.L.Z && !this.L.$ && Q1("Object already scheduled for deletion"), A1.push(this), A1.length === 1 && I1 && I1(D1), this.L.Z = !0, this;
    }, $6.prototype.pa = function (y1) {
      return this.ja && (y1 = this.ja(y1)), y1;
    }, $6.prototype.ga = function (y1) {
      this.V && this.V(y1);
    }, $6.prototype.argPackAdvance = 8, $6.prototype.readValueFromPointer = C2, $6.prototype.deleteObject = function (y1) {
      y1 !== null && y1.delete();
    }, $6.prototype.fromWireType = function (y1) {
      function u1() {
        return this.ca ? YA(this.M.W, {
          O: this.va,
          N: x0,
          T: this,
          S: y1
        }) : YA(this.M.W, {
          O: this,
          N: y1
        });
      }
      var N0,
        x0 = this.pa(y1);
      if (!x0) return this.ga(y1), null;
      var w0 = E1[M1(this.M, x0)];
      if (w0 !== void 0) return w0.L.count.value === 0 ? (w0.L.N = x0, w0.L.S = y1, w0.clone()) : (w0 = w0.clone(), this.ga(y1), w0);
      if (!(w0 = O0[w0 = this.M.oa(x0)])) return u1.call(this);
      w0 = this.ba ? w0.ka : w0.pointerType;
      var v0 = function HA(QA, WA, e0) {
        return WA === e0 ? QA : e0.P === void 0 ? null : (QA = HA(QA, WA, e0.P)) === null ? null : e0.ma(QA);
      }(x0, this.M, w0.M);
      return v0 === null ? u1.call(this) : this.ca ? YA(w0.M.W, {
        O: w0,
        N: v0,
        T: this,
        S: y1
      }) : YA(w0.M.W, {
        O: w0,
        N: v0
      });
    }, h5 = Y.UnboundTypeError = g1("UnboundTypeError");
    var M4 = {
      q: function (y1, u1, N0) {
        y1 = s1(y1), u1 = c1(u1, "wrapper"), N0 = _1(N0);
        var x0 = [].slice,
          w0 = u1.M,
          v0 = w0.W,
          HA = w0.P.W,
          QA = w0.P.constructor;
        for (var WA in y1 = f1(y1, function () {
          w0.P.ia.forEach(function (e0) {
            if (this[e0] === HA[e0]) throw new B0("Pure virtual function " + e0 + " must be implemented in JavaScript");
          }.bind(this)), Object.defineProperty(this, "__parent", {
            value: v0
          }), this.__construct.apply(this, x0.call(arguments));
        }), v0.__construct = function () {
          this === v0 && Q1("Pass correct 'this' to __construct");
          var e0 = QA.implement.apply(void 0, [this].concat(x0.call(arguments)));
          n1(e0);
          var XA = e0.L;
          e0.notifyOnDestruction(), XA.$ = !0, Object.defineProperties(this, {
            L: {
              value: XA
            }
          }), w2(this), e0 = M1(w0, e0 = XA.N), E1.hasOwnProperty(e0) ? Q1("Tried to register registered instance: " + e0) : E1[e0] = this;
        }, v0.__destruct = function () {
          this === v0 && Q1("Pass correct 'this' to __destruct"), n1(this);
          var e0 = this.L.N;
          e0 = M1(w0, e0), E1.hasOwnProperty(e0) ? delete E1[e0] : Q1("Tried to unregister unregistered instance: " + e0);
        }, y1.prototype = Object.create(v0), N0) y1.prototype[WA] = N0[WA];
        return q1(y1);
      },
      l: function (y1) {
        var u1 = $2[y1];
        delete $2[y1];
        var {
          ea: N0,
          V: x0,
          ha: w0
        } = u1;
        kA([y1], w0.map(v0 => v0.sa).concat(w0.map(v0 => v0.ya)), v0 => {
          var HA = {};
          return w0.forEach((QA, WA) => {
            var e0 = v0[WA],
              XA = QA.qa,
              hB = QA.ra,
              f2 = v0[WA + w0.length],
              gB = QA.xa,
              U1 = QA.za;
            HA[QA.na] = {
              read: t1 => e0.fromWireType(XA(hB, t1)),
              write: (t1, d1) => {
                var z0 = [];
                gB(U1, t1, f2.toWireType(z0, d1)), r2(z0);
              }
            };
          }), [{
            name: u1.name,
            fromWireType: function (QA) {
              var WA,
                e0 = {};
              for (WA in HA) e0[WA] = HA[WA].read(QA);
              return x0(QA), e0;
            },
            toWireType: function (QA, WA) {
              for (var e0 in HA) if (!(e0 in WA)) throw TypeError('Missing field:  "' + e0 + '"');
              var XA = N0();
              for (e0 in HA) HA[e0].write(XA, WA[e0]);
              return QA !== null && QA.push(x0, XA), XA;
            },
            argPackAdvance: 8,
            readValueFromPointer: C2,
            U: x0
          }];
        });
      },
      v: function () {},
      B: function (y1, u1, N0, x0, w0) {
        var v0 = I2(N0);
        M2(y1, {
          name: u1 = s1(u1),
          fromWireType: function (HA) {
            return !!HA;
          },
          toWireType: function (HA, QA) {
            return QA ? x0 : w0;
          },
          argPackAdvance: 8,
          readValueFromPointer: function (HA) {
            if (N0 === 1) var QA = H;else if (N0 === 2) QA = $;else if (N0 === 4) QA = N;else throw TypeError("Unknown boolean type size: " + u1);
            return this.fromWireType(QA[HA >> v0]);
          },
          U: null
        });
      },
      h: function (y1, u1, N0, x0, w0, v0, HA, QA, WA, e0, XA, hB, f2) {
        XA = s1(XA), v0 = p6(w0, v0), QA && (QA = p6(HA, QA)), e0 && (e0 = p6(WA, e0)), f2 = p6(hB, f2);
        var gB,
          U1 = z1(XA);
        gB = function () {
          $7("Cannot construct " + XA + " due to unbound types", [x0]);
        }, Y.hasOwnProperty(U1) ? (Q1("Cannot register public name '" + U1 + "' twice"), o2(Y, U1, U1), Y.hasOwnProperty(void 0) && Q1("Cannot register multiple overloads of a function with the same number of arguments (undefined)!"), Y[U1].R[void 0] = gB) : Y[U1] = gB, kA([y1, u1, N0], x0 ? [x0] : [], function (t1) {
          if (t1 = t1[0], x0) var d1,
            z0 = t1.M,
            M0 = z0.W;else M0 = aA.prototype;
          t1 = f1(U1, function () {
            if (Object.getPrototypeOf(this) !== $0) throw new K1("Use 'new' to construct " + XA);
            if (AA.X === void 0) throw new K1(XA + " has no accessible constructor");
            var VA = AA.X[arguments.length];
            if (VA === void 0) throw new K1("Tried to invoke ctor of " + XA + " with invalid number of parameters (" + arguments.length + ") - expected (" + Object.keys(AA.X).toString() + ") parameters instead!");
            return VA.apply(this, arguments);
          });
          var $0 = Object.create(M0, {
            constructor: {
              value: t1
            }
          });
          t1.prototype = $0;
          var AA = new fB(XA, t1, $0, f2, z0, v0, QA, e0);
          z0 = new $6(XA, AA, !0, !1), M0 = new $6(XA + "*", AA, !1, !1);
          var UA = new $6(XA + " const*", AA, !1, !0);
          return O0[y1] = {
            pointerType: M0,
            ka: UA
          }, d1 = t1, Y.hasOwnProperty(U1) || d0("Replacing nonexistant public symbol"), Y[U1] = d1, Y[U1].Y = void 0, [z0, M0, UA];
        });
      },
      d: function (y1, u1, N0, x0, w0, v0, HA) {
        var QA = c7(N0, x0);
        u1 = s1(u1), v0 = p6(w0, v0), kA([], [y1], function (WA) {
          function e0() {
            $7("Cannot call " + XA + " due to unbound types", QA);
          }
          var XA = (WA = WA[0]).name + "." + u1;
          u1.startsWith("@@") && (u1 = Symbol[u1.substring(2)]);
          var hB = WA.M.constructor;
          return hB[u1] === void 0 ? (e0.Y = N0 - 1, hB[u1] = e0) : (o2(hB, u1, XA), hB[u1].R[N0 - 1] = e0), kA([], QA, function (f2) {
            return f2 = l3(XA, [f2[0], null].concat(f2.slice(1)), null, v0, HA), hB[u1].R === void 0 ? (f2.Y = N0 - 1, hB[u1] = f2) : hB[u1].R[N0 - 1] = f2, [];
          }), [];
        });
      },
      p: function (y1, u1, N0, x0, w0, v0) {
        0 < u1 || n();
        var HA = c7(u1, N0);
        w0 = p6(x0, w0), kA([], [y1], function (QA) {
          var WA = "constructor " + (QA = QA[0]).name;
          if (QA.M.X === void 0 && (QA.M.X = []), QA.M.X[u1 - 1] !== void 0) throw new K1("Cannot register multiple constructors with identical number of parameters (" + (u1 - 1) + ") for class '" + QA.name + "'! Overload resolution is currently only performed using the parameter count, not actual type info!");
          return QA.M.X[u1 - 1] = () => {
            $7("Cannot construct " + QA.name + " due to unbound types", HA);
          }, kA([], HA, function (e0) {
            return e0.splice(1, 0, null), QA.M.X[u1 - 1] = l3(WA, e0, null, w0, v0), [];
          }), [];
        });
      },
      a: function (y1, u1, N0, x0, w0, v0, HA, QA) {
        var WA = c7(N0, x0);
        u1 = s1(u1), v0 = p6(w0, v0), kA([], [y1], function (e0) {
          function XA() {
            $7("Cannot call " + hB + " due to unbound types", WA);
          }
          var hB = (e0 = e0[0]).name + "." + u1;
          u1.startsWith("@@") && (u1 = Symbol[u1.substring(2)]), QA && e0.M.ia.push(u1);
          var f2 = e0.M.W,
            gB = f2[u1];
          return gB === void 0 || gB.R === void 0 && gB.className !== e0.name && gB.Y === N0 - 2 ? (XA.Y = N0 - 2, XA.className = e0.name, f2[u1] = XA) : (o2(f2, u1, hB), f2[u1].R[N0 - 2] = XA), kA([], WA, function (U1) {
            return U1 = l3(hB, U1, e0, v0, HA), f2[u1].R === void 0 ? (U1.Y = N0 - 2, f2[u1] = U1) : f2[u1].R[N0 - 2] = U1, [];
          }), [];
        });
      },
      A: function (y1, u1) {
        M2(y1, {
          name: u1 = s1(u1),
          fromWireType: function (N0) {
            var x0 = _1(N0);
            return y4(N0), x0;
          },
          toWireType: function (N0, x0) {
            return q1(x0);
          },
          argPackAdvance: 8,
          readValueFromPointer: C2,
          U: null
        });
      },
      n: function (y1, u1, N0) {
        N0 = I2(N0), M2(y1, {
          name: u1 = s1(u1),
          fromWireType: function (x0) {
            return x0;
          },
          toWireType: function (x0, w0) {
            return w0;
          },
          argPackAdvance: 8,
          readValueFromPointer: function (x0, w0) {
            switch (w0) {
              case 2:
                return function (v0) {
                  return this.fromWireType(R[v0 >> 2]);
                };
              case 3:
                return function (v0) {
                  return this.fromWireType(T[v0 >> 3]);
                };
              default:
                throw TypeError("Unknown float type: " + x0);
            }
          }(u1, N0),
          U: null
        });
      },
      e: function (y1, u1, N0, x0, w0) {
        u1 = s1(u1), w0 === -1 && (w0 = 4294967295), w0 = I2(N0);
        var v0 = QA => QA;
        if (x0 === 0) {
          var HA = 32 - 8 * N0;
          v0 = QA => QA << HA >>> HA;
        }
        N0 = u1.includes("unsigned") ? function (QA, WA) {
          return WA >>> 0;
        } : function (QA, WA) {
          return WA;
        }, M2(y1, {
          name: u1,
          fromWireType: v0,
          toWireType: N0,
          argPackAdvance: 8,
          readValueFromPointer: function (QA, WA, e0) {
            switch (WA) {
              case 0:
                return e0 ? function (XA) {
                  return H[XA];
                } : function (XA) {
                  return z[XA];
                };
              case 1:
                return e0 ? function (XA) {
                  return $[XA >> 1];
                } : function (XA) {
                  return L[XA >> 1];
                };
              case 2:
                return e0 ? function (XA) {
                  return N[XA >> 2];
                } : function (XA) {
                  return O[XA >> 2];
                };
              default:
                throw TypeError("Unknown integer type: " + QA);
            }
          }(u1, w0, x0 !== 0),
          U: null
        });
      },
      b: function (y1, u1, N0) {
        function x0(v0) {
          v0 >>= 2;
          var HA = O;
          return new w0(HA.buffer, HA[v0 + 1], HA[v0]);
        }
        var w0 = [Int8Array, Uint8Array, Int16Array, Uint16Array, Int32Array, Uint32Array, Float32Array, Float64Array][u1];
        M2(y1, {
          name: N0 = s1(N0),
          fromWireType: x0,
          argPackAdvance: 8,
          readValueFromPointer: x0
        }, {
          ta: !0
        });
      },
      o: function (y1, u1) {
        var N0 = (u1 = s1(u1)) === "std::string";
        M2(y1, {
          name: u1,
          fromWireType: function (x0) {
            var w0 = O[x0 >> 2],
              v0 = x0 + 4;
            if (N0) for (var HA = v0, QA = 0; QA <= w0; ++QA) {
              var WA = v0 + QA;
              if (QA == w0 || z[WA] == 0) {
                if (HA = HA ? C(z, HA, WA - HA) : "", e0 === void 0) var e0 = HA;else e0 += "\x00" + HA;
                HA = WA + 1;
              }
            } else {
              for (QA = 0, e0 = Array(w0); QA < w0; ++QA) e0[QA] = String.fromCharCode(z[v0 + QA]);
              e0 = e0.join("");
            }
            return t2(x0), e0;
          },
          toWireType: function (x0, w0) {
            w0 instanceof ArrayBuffer && (w0 = new Uint8Array(w0));
            var v0,
              HA = typeof w0 == "string";
            if (HA || w0 instanceof Uint8Array || w0 instanceof Uint8ClampedArray || w0 instanceof Int8Array || Q1("Cannot pass non-string to std::string"), N0 && HA) {
              var QA = 0;
              for (v0 = 0; v0 < w0.length; ++v0) {
                var WA = w0.charCodeAt(v0);
                127 >= WA ? QA++ : 2047 >= WA ? QA += 2 : 55296 <= WA && 57343 >= WA ? (QA += 4, ++v0) : QA += 3;
              }
              v0 = QA;
            } else v0 = w0.length;
            if (WA = (QA = dQ(4 + v0 + 1)) + 4, O[QA >> 2] = v0, N0 && HA) {
              if (HA = WA, WA = v0 + 1, v0 = z, 0 < WA) {
                WA = HA + WA - 1;
                for (var e0 = 0; e0 < w0.length; ++e0) {
                  var XA = w0.charCodeAt(e0);
                  if (55296 <= XA && 57343 >= XA && (XA = 65536 + ((1023 & XA) << 10) | 1023 & w0.charCodeAt(++e0)), 127 >= XA) {
                    if (HA >= WA) break;
                    v0[HA++] = XA;
                  } else {
                    if (2047 >= XA) {
                      if (HA + 1 >= WA) break;
                      v0[HA++] = 192 | XA >> 6;
                    } else {
                      if (65535 >= XA) {
                        if (HA + 2 >= WA) break;
                        v0[HA++] = 224 | XA >> 12;
                      } else {
                        if (HA + 3 >= WA) break;
                        v0[HA++] = 240 | XA >> 18, v0[HA++] = 128 | XA >> 12 & 63;
                      }
                      v0[HA++] = 128 | XA >> 6 & 63;
                    }
                    v0[HA++] = 128 | 63 & XA;
                  }
                }
                v0[HA] = 0;
              }
            } else if (HA) for (HA = 0; HA < v0; ++HA) 255 < (e0 = w0.charCodeAt(HA)) && (t2(WA), Q1("String has UTF-16 code units that do not fit in 8 bits")), z[WA + HA] = e0;else for (HA = 0; HA < v0; ++HA) z[WA + HA] = w0[HA];
            return x0 !== null && x0.push(t2, QA), QA;
          },
          argPackAdvance: 8,
          readValueFromPointer: C2,
          U: function (x0) {
            t2(x0);
          }
        });
      },
      k: function (y1, u1, N0) {
        if (N0 = s1(N0), u1 === 2) var x0 = SZ,
          w0 = K2,
          v0 = i1,
          HA = () => L,
          QA = 1;else u1 === 4 && (x0 = N1, w0 = Q0, v0 = h0, HA = () => O, QA = 2);
        M2(y1, {
          name: N0,
          fromWireType: function (WA) {
            for (var e0, XA = O[WA >> 2], hB = HA(), f2 = WA + 4, gB = 0; gB <= XA; ++gB) {
              var U1 = WA + 4 + gB * u1;
              (gB == XA || hB[U1 >> QA] == 0) && (f2 = x0(f2, U1 - f2), e0 === void 0 ? e0 = f2 : e0 += "\x00" + f2, f2 = U1 + u1);
            }
            return t2(WA), e0;
          },
          toWireType: function (WA, e0) {
            typeof e0 != "string" && Q1("Cannot pass non-string to C++ string type " + N0);
            var XA = v0(e0),
              hB = dQ(4 + XA + u1);
            return O[hB >> 2] = XA >> QA, w0(e0, hB + 4, XA + u1), WA !== null && WA.push(t2, hB), hB;
          },
          argPackAdvance: 8,
          readValueFromPointer: C2,
          U: function (WA) {
            t2(WA);
          }
        });
      },
      m: function (y1, u1, N0, x0, w0, v0) {
        $2[y1] = {
          name: s1(u1),
          ea: p6(N0, x0),
          V: p6(w0, v0),
          ha: []
        };
      },
      c: function (y1, u1, N0, x0, w0, v0, HA, QA, WA, e0) {
        $2[y1].ha.push({
          na: s1(u1),
          sa: N0,
          qa: p6(x0, w0),
          ra: v0,
          ya: HA,
          xa: p6(QA, WA),
          za: e0
        });
      },
      C: function (y1, u1) {
        M2(y1, {
          ua: !0,
          name: u1 = s1(u1),
          argPackAdvance: 0,
          fromWireType: function () {},
          toWireType: function () {}
        });
      },
      t: function (y1, u1, N0, x0, w0) {
        y1 = iB[y1], u1 = _1(u1), N0 = cA(N0);
        var v0 = [];
        return O[x0 >> 2] = q1(v0), y1(u1, N0, v0, w0);
      },
      j: function (y1, u1, N0, x0) {
        y1 = iB[y1], y1(u1 = _1(u1), N0 = cA(N0), null, x0);
      },
      f: y4,
      g: function (y1, u1) {
        var N0,
          x0,
          w0 = function (WA, e0) {
            for (var XA = Array(WA), hB = 0; hB < WA; ++hB) XA[hB] = c1(O[e0 + 4 * hB >> 2], "parameter " + hB);
            return XA;
          }(y1, u1),
          v0 = w0[0],
          HA = h9[u1 = v0.name + "_$" + w0.slice(1).map(function (WA) {
            return WA.name;
          }).join("_") + "$"];
        if (HA !== void 0) return HA;
        var QA = Array(y1 - 1);
        return N0 = (WA, e0, XA, hB) => {
          for (var f2 = 0, gB = 0; gB < y1 - 1; ++gB) QA[gB] = w0[gB + 1].readValueFromPointer(hB + f2), f2 += w0[gB + 1].argPackAdvance;
          for (gB = 0, WA = WA[e0].apply(WA, QA); gB < y1 - 1; ++gB) w0[gB + 1].la && w0[gB + 1].la(QA[gB]);
          if (!v0.ua) return v0.toWireType(XA, WA);
        }, x0 = iB.length, iB.push(N0), HA = x0, h9[u1] = HA;
      },
      r: function (y1) {
        4 < y1 && (G0[y1].fa += 1);
      },
      s: function (y1) {
        r2(_1(y1)), y4(y1);
      },
      i: function () {
        n("");
      },
      x: function (y1, u1, N0) {
        z.copyWithin(y1, u1, u1 + N0);
      },
      w: function (y1) {
        var u1 = z.length;
        if (2147483648 < (y1 >>>= 0)) return !1;
        for (var N0 = 1; 4 >= N0; N0 *= 2) {
          var x0 = u1 * (1 + 0.2 / N0);
          x0 = Math.min(x0, y1 + 100663296);
          var w0 = Math,
            v0 = w0.min;
          x0 = Math.max(y1, x0), x0 += (65536 - x0 % 65536) % 65536;
          A: {
            var HA = X.buffer;
            try {
              X.grow(v0.call(w0, 2147483648, x0) - HA.byteLength + 65535 >>> 16), K();
              var QA = 1;
              break A;
            } catch (WA) {}
            QA = void 0;
          }
          if (QA) return !0;
        }
        return !1;
      },
      z: function () {
        return 52;
      },
      u: function () {
        return 70;
      },
      y: function (y1, u1, N0, x0) {
        for (var w0 = 0, v0 = 0; v0 < N0; v0++) {
          var HA = O[u1 >> 2],
            QA = O[u1 + 4 >> 2];
          u1 += 8;
          for (var WA = 0; WA < QA; WA++) {
            var e0 = z[HA + WA],
              XA = BQ[y1];
            e0 === 0 || e0 === 10 ? ((y1 === 1 ? F : I)(C(XA, 0)), XA.length = 0) : XA.push(e0);
          }
          w0 += QA;
        }
        return O[x0 >> 2] = w0, 0;
      }
    };
    (function () {
      function y1(w0) {
        Y.asm = w0.exports, X = Y.asm.D, K(), j = Y.asm.I, y.unshift(Y.asm.E), --h == 0 && a && (w0 = a, a = null, w0());
      }
      function u1(w0) {
        y1(w0.instance);
      }
      function N0(w0) {
        return (typeof fetch == "function" ? fetch(Q, {
          credentials: "same-origin"
        }).then(function (v0) {
          if (!v0.ok) throw "failed to load wasm binary file at '" + Q + "'";
          return v0.arrayBuffer();
        }).catch(function () {
          return t();
        }) : Promise.resolve().then(function () {
          return t();
        })).then(function (v0) {
          return WebAssembly.instantiate(v0, x0);
        }).then(function (v0) {
          return v0;
        }).then(w0, function (v0) {
          I("failed to asynchronously prepare wasm: " + v0), n(v0);
        });
      }
      var x0 = {
        a: M4
      };
      if (h++, Y.instantiateWasm) try {
        return Y.instantiateWasm(x0, y1);
      } catch (w0) {
        I("Module.instantiateWasm callback failed with error: " + w0), J(w0);
      }
      (typeof WebAssembly.instantiateStreaming != "function" || v() || typeof fetch != "function" ? N0(u1) : fetch(Q, {
        credentials: "same-origin"
      }).then(function (w0) {
        return WebAssembly.instantiateStreaming(w0, x0).then(u1, function (v0) {
          return I("wasm streaming compile failed: " + v0), I("falling back to ArrayBuffer instantiation"), N0(u1);
        });
      })).catch(J);
    })();
    var R4 = Y.___getTypeName = function () {
      return (R4 = Y.___getTypeName = Y.asm.F).apply(null, arguments);
    };
    function dQ() {
      return (dQ = Y.asm.H).apply(null, arguments);
    }
    function t2() {
      return (t2 = Y.asm.J).apply(null, arguments);
    }
    function QQ() {
      0 < h || (W1(f), 0 < h || D || (D = !0, Y.calledRun = !0, V || (W1(y), W(Y), W1(c))));
    }
    return Y.__embind_initialize_bindings = function () {
      return (Y.__embind_initialize_bindings = Y.asm.G).apply(null, arguments);
    }, Y.dynCall_jiji = function () {
      return (Y.dynCall_jiji = Y.asm.K).apply(null, arguments);
    }, a = function y1() {
      D || QQ(), D || (a = y1);
    }, QQ(), B.ready;
  };
})();
async function vq2(A) {
  let B = await Vi4({
    instantiateWasm(Q, D) {
      WebAssembly.instantiate(A, Q).then(Z => {
        Z instanceof WebAssembly.Instance ? D(Z) : D(Z.instance);
      });
    }
  });
  return xq2(B);
}
import { readFile as Ci4 } from "node:fs/promises";
import { createRequire as Ki4 } from "node:module";
var SR1 = await vq2(await Ci4(Ki4(import.meta.url).resolve("./yoga.wasm")));
function wG0({
  onlyFirst: A = !1
} = {}) {
  let Q = ["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?(?:\\u0007|\\u001B\\u005C|\\u009C))", "(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-nq-uy=><~]))"].join("|");
  return new RegExp(Q, A ? void 0 : "g");
}
var Hi4 = wG0();
function rG(A) {
  if (typeof A !== "string") throw new TypeError(`Expected a \`string\`, got \`${typeof A}\``);
  return A.replace(Hi4, "");
}
function bq2(A) {
  return A === 161 || A === 164 || A === 167 || A === 168 || A === 170 || A === 173 || A === 174 || A >= 176 && A <= 180 || A >= 182 && A <= 186 || A >= 188 && A <= 191 || A === 198 || A === 208 || A === 215 || A === 216 || A >= 222 && A <= 225 || A === 230 || A >= 232 && A <= 234 || A === 236 || A === 237 || A === 240 || A === 242 || A === 243 || A >= 247 && A <= 250 || A === 252 || A === 254 || A === 257 || A === 273 || A === 275 || A === 283 || A === 294 || A === 295 || A === 299 || A >= 305 && A <= 307 || A === 312 || A >= 319 && A <= 322 || A === 324 || A >= 328 && A <= 331 || A === 333 || A === 338 || A === 339 || A === 358 || A === 359 || A === 363 || A === 462 || A === 464 || A === 466 || A === 468 || A === 470 || A === 472 || A === 474 || A === 476 || A === 593 || A === 609 || A === 708 || A === 711 || A >= 713 && A <= 715 || A === 717 || A === 720 || A >= 728 && A <= 731 || A === 733 || A === 735 || A >= 768 && A <= 879 || A >= 913 && A <= 929 || A >= 931 && A <= 937 || A >= 945 && A <= 961 || A >= 963 && A <= 969 || A === 1025 || A >= 1040 && A <= 1103 || A === 1105 || A === 8208 || A >= 8211 && A <= 8214 || A === 8216 || A === 8217 || A === 8220 || A === 8221 || A >= 8224 && A <= 8226 || A >= 8228 && A <= 8231 || A === 8240 || A === 8242 || A === 8243 || A === 8245 || A === 8251 || A === 8254 || A === 8308 || A === 8319 || A >= 8321 && A <= 8324 || A === 8364 || A === 8451 || A === 8453 || A === 8457 || A === 8467 || A === 8470 || A === 8481 || A === 8482 || A === 8486 || A === 8491 || A === 8531 || A === 8532 || A >= 8539 && A <= 8542 || A >= 8544 && A <= 8555 || A >= 8560 && A <= 8569 || A === 8585 || A >= 8592 && A <= 8601 || A === 8632 || A === 8633 || A === 8658 || A === 8660 || A === 8679 || A === 8704 || A === 8706 || A === 8707 || A === 8711 || A === 8712 || A === 8715 || A === 8719 || A === 8721 || A === 8725 || A === 8730 || A >= 8733 && A <= 8736 || A === 8739 || A === 8741 || A >= 8743 && A <= 8748 || A === 8750 || A >= 8756 && A <= 8759 || A === 8764 || A === 8765 || A === 8776 || A === 8780 || A === 8786 || A === 8800 || A === 8801 || A >= 8804 && A <= 8807 || A === 8810 || A === 8811 || A === 8814 || A === 8815 || A === 8834 || A === 8835 || A === 8838 || A === 8839 || A === 8853 || A === 8857 || A === 8869 || A === 8895 || A === 8978 || A >= 9312 && A <= 9449 || A >= 9451 && A <= 9547 || A >= 9552 && A <= 9587 || A >= 9600 && A <= 9615 || A >= 9618 && A <= 9621 || A === 9632 || A === 9633 || A >= 9635 && A <= 9641 || A === 9650 || A === 9651 || A === 9654 || A === 9655 || A === 9660 || A === 9661 || A === 9664 || A === 9665 || A >= 9670 && A <= 9672 || A === 9675 || A >= 9678 && A <= 9681 || A >= 9698 && A <= 9701 || A === 9711 || A === 9733 || A === 9734 || A === 9737 || A === 9742 || A === 9743 || A === 9756 || A === 9758 || A === 9792 || A === 9794 || A === 9824 || A === 9825 || A >= 9827 && A <= 9829 || A >= 9831 && A <= 9834 || A === 9836 || A === 9837 || A === 9839 || A === 9886 || A === 9887 || A === 9919 || A >= 9926 && A <= 9933 || A >= 9935 && A <= 9939 || A >= 9941 && A <= 9953 || A === 9955 || A === 9960 || A === 9961 || A >= 9963 && A <= 9969 || A === 9972 || A >= 9974 && A <= 9977 || A === 9979 || A === 9980 || A === 9982 || A === 9983 || A === 10045 || A >= 10102 && A <= 10111 || A >= 11094 && A <= 11097 || A >= 12872 && A <= 12879 || A >= 57344 && A <= 63743 || A >= 65024 && A <= 65039 || A === 65533 || A >= 127232 && A <= 127242 || A >= 127248 && A <= 127277 || A >= 127280 && A <= 127337 || A >= 127344 && A <= 127373 || A === 127375 || A === 127376 || A >= 127387 && A <= 127404 || A >= 917760 && A <= 917999 || A >= 983040 && A <= 1048573 || A >= 1048576 && A <= 1114109;
}
function fq2(A) {
  return A === 12288 || A >= 65281 && A <= 65376 || A >= 65504 && A <= 65510;
}
function hq2(A) {
  return A >= 4352 && A <= 4447 || A === 8986 || A === 8987 || A === 9001 || A === 9002 || A >= 9193 && A <= 9196 || A === 9200 || A === 9203 || A === 9725 || A === 9726 || A === 9748 || A === 9749 || A >= 9776 && A <= 9783 || A >= 9800 && A <= 9811 || A === 9855 || A >= 9866 && A <= 9871 || A === 9875 || A === 9889 || A === 9898 || A === 9899 || A === 9917 || A === 9918 || A === 9924 || A === 9925 || A === 9934 || A === 9940 || A === 9962 || A === 9970 || A === 9971 || A === 9973 || A === 9978 || A === 9981 || A === 9989 || A === 9994 || A === 9995 || A === 10024 || A === 10060 || A === 10062 || A >= 10067 && A <= 10069 || A === 10071 || A >= 10133 && A <= 10135 || A === 10160 || A === 10175 || A === 11035 || A === 11036 || A === 11088 || A === 11093 || A >= 11904 && A <= 11929 || A >= 11931 && A <= 12019 || A >= 12032 && A <= 12245 || A >= 12272 && A <= 12287 || A >= 12289 && A <= 12350 || A >= 12353 && A <= 12438 || A >= 12441 && A <= 12543 || A >= 12549 && A <= 12591 || A >= 12593 && A <= 12686 || A >= 12688 && A <= 12773 || A >= 12783 && A <= 12830 || A >= 12832 && A <= 12871 || A >= 12880 && A <= 42124 || A >= 42128 && A <= 42182 || A >= 43360 && A <= 43388 || A >= 44032 && A <= 55203 || A >= 63744 && A <= 64255 || A >= 65040 && A <= 65049 || A >= 65072 && A <= 65106 || A >= 65108 && A <= 65126 || A >= 65128 && A <= 65131 || A >= 94176 && A <= 94180 || A === 94192 || A === 94193 || A >= 94208 && A <= 100343 || A >= 100352 && A <= 101589 || A >= 101631 && A <= 101640 || A >= 110576 && A <= 110579 || A >= 110581 && A <= 110587 || A === 110589 || A === 110590 || A >= 110592 && A <= 110882 || A === 110898 || A >= 110928 && A <= 110930 || A === 110933 || A >= 110948 && A <= 110951 || A >= 110960 && A <= 111355 || A >= 119552 && A <= 119638 || A >= 119648 && A <= 119670 || A === 126980 || A === 127183 || A === 127374 || A >= 127377 && A <= 127386 || A >= 127488 && A <= 127490 || A >= 127504 && A <= 127547 || A >= 127552 && A <= 127560 || A === 127568 || A === 127569 || A >= 127584 && A <= 127589 || A >= 127744 && A <= 127776 || A >= 127789 && A <= 127797 || A >= 127799 && A <= 127868 || A >= 127870 && A <= 127891 || A >= 127904 && A <= 127946 || A >= 127951 && A <= 127955 || A >= 127968 && A <= 127984 || A === 127988 || A >= 127992 && A <= 128062 || A === 128064 || A >= 128066 && A <= 128252 || A >= 128255 && A <= 128317 || A >= 128331 && A <= 128334 || A >= 128336 && A <= 128359 || A === 128378 || A === 128405 || A === 128406 || A === 128420 || A >= 128507 && A <= 128591 || A >= 128640 && A <= 128709 || A === 128716 || A >= 128720 && A <= 128722 || A >= 128725 && A <= 128727 || A >= 128732 && A <= 128735 || A === 128747 || A === 128748 || A >= 128756 && A <= 128764 || A >= 128992 && A <= 129003 || A === 129008 || A >= 129292 && A <= 129338 || A >= 129340 && A <= 129349 || A >= 129351 && A <= 129535 || A >= 129648 && A <= 129660 || A >= 129664 && A <= 129673 || A >= 129679 && A <= 129734 || A >= 129742 && A <= 129756 || A >= 129759 && A <= 129769 || A >= 129776 && A <= 129784 || A >= 131072 && A <= 196605 || A >= 196608 && A <= 262141;
}
function zi4(A) {
  if (!Number.isSafeInteger(A)) throw new TypeError(`Expected a code point, got \`${typeof A}\`.`);
}
function M_(A, {
  ambiguousAsWide: B = !1
} = {}) {
  if (zi4(A), fq2(A) || hq2(A) || B && bq2(A)) return 2;
  return 1;
}
var mq2 = F1(uq2(), 1),
  Ei4 = new Intl.Segmenter(),
  Ui4 = /^\p{Default_Ignorable_Code_Point}$/u;
function z51(A, B = {}) {
  if (typeof A !== "string" || A.length === 0) return 0;
  let {
    ambiguousIsNarrow: Q = !0,
    countAnsiEscapeCodes: D = !1
  } = B;
  if (!D) A = rG(A);
  if (A.length === 0) return 0;
  let Z = 0,
    G = {
      ambiguousAsWide: !Q
    };
  for (let {
    segment: F
  } of Ei4.segment(A)) {
    let I = F.codePointAt(0);
    if (I <= 31 || I >= 127 && I <= 159) continue;
    if (I >= 8203 && I <= 8207 || I === 65279) continue;
    if (I >= 768 && I <= 879 || I >= 6832 && I <= 6911 || I >= 7616 && I <= 7679 || I >= 8400 && I <= 8447 || I >= 65056 && I <= 65071) continue;
    if (I >= 55296 && I <= 57343) continue;
    if (I >= 65024 && I <= 65039) continue;
    if (Ui4.test(F)) continue;
    if (mq2.default().test(F)) {
      Z += 2;
      continue;
    }
    Z += M_(I, G);
  }
  return Z;
}
function Io(A) {
  let B = 0;
  for (let Q of A.split(`
`)) B = Math.max(B, z51(Q));
  return B;
}
var dq2 = {},
  wi4 = A => {
    if (A.length === 0) return {
      width: 0,
      height: 0
    };
    let B = dq2[A];
    if (B) return B;
    let Q = Io(A),
      D = A.split(`
`).length;
    return dq2[A] = {
      width: Q,
      height: D
    }, {
      width: Q,
      height: D
    };
  },
  $G0 = wi4;
var pq2 = F1(lq2(), 1),
  $i4 = new Intl.Segmenter(),
  qi4 = /^\p{Default_Ignorable_Code_Point}$/u;
function Qu(A, B = {}) {
  if (typeof A !== "string" || A.length === 0) return 0;
  let {
    ambiguousIsNarrow: Q = !0,
    countAnsiEscapeCodes: D = !1
  } = B;
  if (!D) A = rG(A);
  if (A.length === 0) return 0;
  let Z = 0,
    G = {
      ambiguousAsWide: !Q
    };
  for (let {
    segment: F
  } of $i4.segment(A)) {
    let I = F.codePointAt(0);
    if (I <= 31 || I >= 127 && I <= 159) continue;
    if (I >= 8203 && I <= 8207 || I === 65279) continue;
    if (I >= 768 && I <= 879 || I >= 6832 && I <= 6911 || I >= 7616 && I <= 7679 || I >= 8400 && I <= 8447 || I >= 65056 && I <= 65071) continue;
    if (I >= 55296 && I <= 57343) continue;
    if (I >= 65024 && I <= 65039) continue;
    if (qi4.test(F)) continue;
    if (pq2.default().test(F)) {
      Z += 2;
      continue;
    }
    Z += M_(I, G);
  }
  return Z;
}
var iq2 = (A = 0) => B => `\x1B[${B + A}m`,
  nq2 = (A = 0) => B => `\x1B[${38 + A};5;${B}m`,
  aq2 = (A = 0) => (B, Q, D) => `\x1B[${38 + A};2;${B};${Q};${D}m`,
  LD = {
    modifier: {
      reset: [0, 0],
      bold: [1, 22],
      dim: [2, 22],
      italic: [3, 23],
      underline: [4, 24],
      overline: [53, 55],
      inverse: [7, 27],
      hidden: [8, 28],
      strikethrough: [9, 29]
    },
    color: {
      black: [30, 39],
      red: [31, 39],
      green: [32, 39],
      yellow: [33, 39],
      blue: [34, 39],
      magenta: [35, 39],
      cyan: [36, 39],
      white: [37, 39],
      blackBright: [90, 39],
      gray: [90, 39],
      grey: [90, 39],
      redBright: [91, 39],
      greenBright: [92, 39],
      yellowBright: [93, 39],
      blueBright: [94, 39],
      magentaBright: [95, 39],
      cyanBright: [96, 39],
      whiteBright: [97, 39]
    },
    bgColor: {
      bgBlack: [40, 49],
      bgRed: [41, 49],
      bgGreen: [42, 49],
      bgYellow: [43, 49],
      bgBlue: [44, 49],
      bgMagenta: [45, 49],
      bgCyan: [46, 49],
      bgWhite: [47, 49],
      bgBlackBright: [100, 49],
      bgGray: [100, 49],
      bgGrey: [100, 49],
      bgRedBright: [101, 49],
      bgGreenBright: [102, 49],
      bgYellowBright: [103, 49],
      bgBlueBright: [104, 49],
      bgMagentaBright: [105, 49],
      bgCyanBright: [106, 49],
      bgWhiteBright: [107, 49]
    }
  },
  dL5 = Object.keys(LD.modifier),
  Ni4 = Object.keys(LD.color),
  Li4 = Object.keys(LD.bgColor),
  cL5 = [...Ni4, ...Li4];
function Mi4() {
  let A = new Map();
  for (let [B, Q] of Object.entries(LD)) {
    for (let [D, Z] of Object.entries(Q)) LD[D] = {
      open: `\x1B[${Z[0]}m`,
      close: `\x1B[${Z[1]}m`
    }, Q[D] = LD[D], A.set(Z[0], Z[1]);
    Object.defineProperty(LD, B, {
      value: Q,
      enumerable: !1
    });
  }
  return Object.defineProperty(LD, "codes", {
    value: A,
    enumerable: !1
  }), LD.color.close = "\x1B[39m", LD.bgColor.close = "\x1B[49m", LD.color.ansi = iq2(), LD.color.ansi256 = nq2(), LD.color.ansi16m = aq2(), LD.bgColor.ansi = iq2(10), LD.bgColor.ansi256 = nq2(10), LD.bgColor.ansi16m = aq2(10), Object.defineProperties(LD, {
    rgbToAnsi256: {
      value: (B, Q, D) => {
        if (B === Q && Q === D) {
          if (B < 8) return 16;
          if (B > 248) return 231;
          return Math.round((B - 8) / 247 * 24) + 232;
        }
        return 16 + 36 * Math.round(B / 255 * 5) + 6 * Math.round(Q / 255 * 5) + Math.round(D / 255 * 5);
      },
      enumerable: !1
    },
    hexToRgb: {
      value: B => {
        let Q = /[a-f\d]{6}|[a-f\d]{3}/i.exec(B.toString(16));
        if (!Q) return [0, 0, 0];
        let [D] = Q;
        if (D.length === 3) D = [...D].map(G => G + G).join("");
        let Z = Number.parseInt(D, 16);
        return [Z >> 16 & 255, Z >> 8 & 255, Z & 255];
      },
      enumerable: !1
    },
    hexToAnsi256: {
      value: B => LD.rgbToAnsi256(...LD.hexToRgb(B)),
      enumerable: !1
    },
    ansi256ToAnsi: {
      value: B => {
        if (B < 8) return 30 + B;
        if (B < 16) return 90 + (B - 8);
        let Q, D, Z;
        if (B >= 232) Q = ((B - 232) * 10 + 8) / 255, D = Q, Z = Q;else {
          B -= 16;
          let I = B % 36;
          Q = Math.floor(B / 36) / 5, D = Math.floor(I / 6) / 5, Z = I % 6 / 5;
        }
        let G = Math.max(Q, D, Z) * 2;
        if (G === 0) return 30;
        let F = 30 + (Math.round(Z) << 2 | Math.round(D) << 1 | Math.round(Q));
        if (G === 2) F += 60;
        return F;
      },
      enumerable: !1
    },
    rgbToAnsi: {
      value: (B, Q, D) => LD.ansi256ToAnsi(LD.rgbToAnsi256(B, Q, D)),
      enumerable: !1
    },
    hexToAnsi: {
      value: B => LD.ansi256ToAnsi(LD.hexToAnsi256(B)),
      enumerable: !1
    }
  }), LD;
}
var Ri4 = Mi4(),
  MD = Ri4;
var yR1 = new Set(["\x1B", ""]),
  Oi4 = 39,
  NG0 = "\x07",
  oq2 = "[",
  Ti4 = "]",
  tq2 = "m",
  jR1 = `${Ti4}8;;`,
  sq2 = A => `${yR1.values().next().value}${oq2}${A}${tq2}`,
  rq2 = A => `${yR1.values().next().value}${jR1}${A}${NG0}`,
  Pi4 = A => A.split(" ").map(B => Qu(B)),
  qG0 = (A, B, Q) => {
    let D = [...B],
      Z = !1,
      G = !1,
      F = Qu(rG(A.at(-1)));
    for (let [I, Y] of D.entries()) {
      let W = Qu(Y);
      if (F + W <= Q) A[A.length - 1] += Y;else A.push(Y), F = 0;
      if (yR1.has(Y)) Z = !0, G = D.slice(I + 1, I + 1 + jR1.length).join("") === jR1;
      if (Z) {
        if (G) {
          if (Y === NG0) Z = !1, G = !1;
        } else if (Y === tq2) Z = !1;
        continue;
      }
      if (F += W, F === Q && I < D.length - 1) A.push(""), F = 0;
    }
    if (!F && A.at(-1).length > 0 && A.length > 1) A[A.length - 2] += A.pop();
  },
  Si4 = A => {
    let B = A.split(" "),
      Q = B.length;
    while (Q > 0) {
      if (Qu(B[Q - 1]) > 0) break;
      Q--;
    }
    if (Q === B.length) return A;
    return B.slice(0, Q).join(" ") + B.slice(Q).join("");
  },
  ji4 = (A, B, Q = {}) => {
    if (Q.trim !== !1 && A.trim() === "") return "";
    let D = "",
      Z,
      G,
      F = Pi4(A),
      I = [""];
    for (let [X, V] of A.split(" ").entries()) {
      if (Q.trim !== !1) I[I.length - 1] = I.at(-1).trimStart();
      let C = Qu(I.at(-1));
      if (X !== 0) {
        if (C >= B && (Q.wordWrap === !1 || Q.trim === !1)) I.push(""), C = 0;
        if (C > 0 || Q.trim === !1) I[I.length - 1] += " ", C++;
      }
      if (Q.hard && F[X] > B) {
        let K = B - C,
          H = 1 + Math.floor((F[X] - K - 1) / B);
        if (Math.floor((F[X] - 1) / B) < H) I.push("");
        qG0(I, V, B);
        continue;
      }
      if (C + F[X] > B && C > 0 && F[X] > 0) {
        if (Q.wordWrap === !1 && C < B) {
          qG0(I, V, B);
          continue;
        }
        I.push("");
      }
      if (C + F[X] > B && Q.wordWrap === !1) {
        qG0(I, V, B);
        continue;
      }
      I[I.length - 1] += V;
    }
    if (Q.trim !== !1) I = I.map(X => Si4(X));
    let Y = I.join(`
`),
      W = [...Y],
      J = 0;
    for (let [X, V] of W.entries()) {
      if (D += V, yR1.has(V)) {
        let {
          groups: K
        } = new RegExp(`(?:\\${oq2}(?<code>\\d+)m|\\${jR1}(?<uri>.*)${NG0})`).exec(Y.slice(J)) || {
          groups: {}
        };
        if (K.code !== void 0) {
          let H = Number.parseFloat(K.code);
          Z = H === Oi4 ? void 0 : H;
        } else if (K.uri !== void 0) G = K.uri.length === 0 ? void 0 : K.uri;
      }
      let C = MD.codes.get(Number(Z));
      if (W[X + 1] === `
`) {
        if (G) D += rq2("");
        if (Z && C) D += sq2(C);
      } else if (V === `
`) {
        if (Z && C) D += sq2(Z);
        if (G) D += rq2(G);
      }
      J += V.length;
    }
    return D;
  };
function E51(A, B, Q) {
  return String(A).normalize().replaceAll(`\r
`, `
`).split(`
`).map(D => ji4(D, B, Q)).join(`
`);
}
function U51(A) {
  if (!Number.isInteger(A)) return !1;
  return A >= 4352 && (A <= 4447 || A === 9001 || A === 9002 || 11904 <= A && A <= 12871 && A !== 12351 || 12880 <= A && A <= 19903 || 19968 <= A && A <= 42182 || 43360 <= A && A <= 43388 || 44032 <= A && A <= 55203 || 63744 <= A && A <= 64255 || 65040 <= A && A <= 65049 || 65072 <= A && A <= 65131 || 65281 <= A && A <= 65376 || 65504 <= A && A <= 65510 || 110592 <= A && A <= 110593 || 127488 <= A && A <= 127569 || 131072 <= A && A <= 262141);
}
var yi4 = /^[\uD800-\uDBFF][\uDC00-\uDFFF]$/,
  AN2 = ["\x1B", ""],
  kR1 = A => `${AN2[0]}[${A}m`,
  eq2 = (A, B, Q) => {
    let D = [];
    A = [...A];
    for (let Z of A) {
      let G = Z;
      if (Z.includes(";")) Z = Z.split(";")[0][0] + "0";
      let F = MD.codes.get(Number.parseInt(Z, 10));
      if (F) {
        let I = A.indexOf(F.toString());
        if (I === -1) D.push(kR1(B ? F : G));else A.splice(I, 1);
      } else if (B) {
        D.push(kR1(0));
        break;
      } else D.push(kR1(G));
    }
    if (B) {
      if (D = D.filter((Z, G) => D.indexOf(Z) === G), Q !== void 0) {
        let Z = kR1(MD.codes.get(Number.parseInt(Q, 10)));
        D = D.reduce((G, F) => F === Z ? [F, ...G] : [...G, F], []);
      }
    }
    return D.join("");
  };
function pL(A, B, Q) {
  let D = [...A],
    Z = [],
    G = typeof Q === "number" ? Q : D.length,
    F = !1,
    I,
    Y = 0,
    W = "";
  for (let [J, X] of D.entries()) {
    let V = !1;
    if (AN2.includes(X)) {
      let C = /\d[^m]*/.exec(A.slice(J, J + 18));
      if (I = C && C.length > 0 ? C[0] : void 0, Y < G) {
        if (F = !0, I !== void 0) Z.push(I);
      }
    } else if (F && X === "m") F = !1, V = !0;
    if (!F && !V) Y++;
    if (!yi4.test(X) && U51(X.codePointAt())) {
      if (Y++, typeof Q !== "number") G++;
    }
    if (Y > B && Y <= G) W += X;else if (Y === B && !F && I !== void 0) W = eq2(Z);else if (Y >= G) {
      W += eq2(Z, !0, I);
      break;
    }
  }
  return W;
}
var DN2 = F1(QN2(), 1),
  ki4 = new Intl.Segmenter(),
  _i4 = /^\p{Default_Ignorable_Code_Point}$/u;
function Yo(A, B = {}) {
  if (typeof A !== "string" || A.length === 0) return 0;
  let {
    ambiguousIsNarrow: Q = !0,
    countAnsiEscapeCodes: D = !1
  } = B;
  if (!D) A = rG(A);
  if (A.length === 0) return 0;
  let Z = 0,
    G = {
      ambiguousAsWide: !Q
    };
  for (let {
    segment: F
  } of ki4.segment(A)) {
    let I = F.codePointAt(0);
    if (I <= 31 || I >= 127 && I <= 159) continue;
    if (I >= 8203 && I <= 8207 || I === 65279) continue;
    if (I >= 768 && I <= 879 || I >= 6832 && I <= 6911 || I >= 7616 && I <= 7679 || I >= 8400 && I <= 8447 || I >= 65056 && I <= 65071) continue;
    if (I >= 55296 && I <= 57343) continue;
    if (I >= 65024 && I <= 65039) continue;
    if (_i4.test(F)) continue;
    if (DN2.default().test(F)) {
      Z += 2;
      continue;
    }
    Z += M_(I, G);
  }
  return Z;
}
function _R1(A, B, Q) {
  if (A.charAt(B) === " ") return B;
  let D = Q ? 1 : -1;
  for (let Z = 0; Z <= 3; Z++) {
    let G = B + Z * D;
    if (A.charAt(G) === " ") return G;
  }
  return B;
}
function LG0(A, B, Q = {}) {
  let {
      position: D = "end",
      space: Z = !1,
      preferTruncationOnSpace: G = !1
    } = Q,
    {
      truncationCharacter: F = "…"
    } = Q;
  if (typeof A !== "string") throw new TypeError(`Expected \`input\` to be a string, got ${typeof A}`);
  if (typeof B !== "number") throw new TypeError(`Expected \`columns\` to be a number, got ${typeof B}`);
  if (B < 1) return "";
  if (B === 1) return F;
  let I = Yo(A);
  if (I <= B) return A;
  if (D === "start") {
    if (G) {
      let Y = _R1(A, I - B + 1, !0);
      return F + pL(A, Y, I).trim();
    }
    if (Z === !0) F += " ";
    return F + pL(A, I - B + Yo(F), I);
  }
  if (D === "middle") {
    if (Z === !0) F = ` ${F} `;
    let Y = Math.floor(B / 2);
    if (G) {
      let W = _R1(A, Y),
        J = _R1(A, I - (B - Y) + 1, !0);
      return pL(A, 0, W) + F + pL(A, J, I).trim();
    }
    return pL(A, 0, Y) + F + pL(A, I - (B - Y) + Yo(F), I);
  }
  if (D === "end") {
    if (G) {
      let Y = _R1(A, B - 1);
      return pL(A, 0, Y) + F;
    }
    if (Z === !0) F = ` ${F}`;
    return pL(A, 0, B - Yo(F)) + F;
  }
  throw new Error(`Expected \`options.position\` to be either \`start\`, \`middle\` or \`end\`, got ${D}`);
}
var ZN2 = {},
  xi4 = (A, B, Q) => {
    let D = A + String(B) + String(Q),
      Z = ZN2[D];
    if (Z) return Z;
    let G = A;
    if (Q === "wrap") G = E51(A, B, {
      trim: !1,
      hard: !0
    });
    if (Q.startsWith("truncate")) {
      let F = "end";
      if (Q === "truncate-middle") F = "middle";
      if (Q === "truncate-start") F = "start";
      G = LG0(A, B, {
        position: F
      });
    }
    return ZN2[D] = G, G;
  },
  Wo = xi4;
var GN2 = A => {
    let B = "";
    for (let Q = 0; Q < A.childNodes.length; Q++) {
      let D = A.childNodes[Q];
      if (D === void 0) continue;
      let Z = "";
      if (D.nodeName === "#text") Z = D.nodeValue;else {
        if (D.nodeName === "ink-text" || D.nodeName === "ink-virtual-text") Z = GN2(D);
        if (Z.length > 0 && typeof D.internal_transform === "function") Z = D.internal_transform(Z, Q);
      }
      B += Z;
    }
    return B;
  },
  xR1 = GN2;
var vR1 = A => {
    let B = {
      nodeName: A,
      style: {},
      attributes: {},
      childNodes: [],
      parentNode: void 0,
      yogaNode: A === "ink-virtual-text" ? void 0 : SR1.Node.create()
    };
    if (A === "ink-text") B.yogaNode?.setMeasureFunc(vi4.bind(null, B));
    return B;
  },
  bR1 = (A, B) => {
    if (B.parentNode) w51(B.parentNode, B);
    if (B.parentNode = A, A.childNodes.push(B), B.yogaNode) A.yogaNode?.insertChild(B.yogaNode, A.yogaNode.getChildCount());
    if (A.nodeName === "ink-text" || A.nodeName === "ink-virtual-text") fR1(A);
  },
  MG0 = (A, B, Q) => {
    if (B.parentNode) w51(B.parentNode, B);
    B.parentNode = A;
    let D = A.childNodes.indexOf(Q);
    if (D >= 0) {
      if (A.childNodes.splice(D, 0, B), B.yogaNode) A.yogaNode?.insertChild(B.yogaNode, D);
      return;
    }
    if (A.childNodes.push(B), B.yogaNode) A.yogaNode?.insertChild(B.yogaNode, A.yogaNode.getChildCount());
    if (A.nodeName === "ink-text" || A.nodeName === "ink-virtual-text") fR1(A);
  },
  w51 = (A, B) => {
    if (B.yogaNode) B.parentNode?.yogaNode?.removeChild(B.yogaNode);
    B.parentNode = void 0;
    let Q = A.childNodes.indexOf(B);
    if (Q >= 0) A.childNodes.splice(Q, 1);
    if (A.nodeName === "ink-text" || A.nodeName === "ink-virtual-text") fR1(A);
  },
  RG0 = (A, B, Q) => {
    A.attributes[B] = Q;
  },
  OG0 = (A, B) => {
    A.style = B;
  },
  FN2 = A => {
    let B = {
      nodeName: "#text",
      nodeValue: A,
      yogaNode: void 0,
      parentNode: void 0,
      style: {}
    };
    return $51(B, A), B;
  },
  vi4 = function (A, B) {
    let Q = A.nodeName === "#text" ? A.nodeValue : xR1(A),
      D = $G0(Q);
    if (D.width <= B) return D;
    if (D.width >= 1 && B > 0 && B < 1) return D;
    let Z = A.style?.textWrap ?? "wrap",
      G = Wo(Q, B, Z);
    return $G0(G);
  },
  IN2 = A => {
    if (!A?.parentNode) return;
    return A.yogaNode ?? IN2(A.parentNode);
  },
  fR1 = A => {
    IN2(A)?.markDirty();
  },
  $51 = (A, B) => {
    if (typeof B !== "string") B = String(B);
    A.nodeValue = B, fR1(A);
  };
var fi4 = (A, B) => {
    if ("position" in B) A.setPositionType(B.position === "absolute" ? MR1 : LR1);
  },
  hi4 = (A, B) => {
    if ("margin" in B) A.setMargin(H51, B.margin ?? 0);
    if ("marginX" in B) A.setMargin(C51, B.marginX ?? 0);
    if ("marginY" in B) A.setMargin(K51, B.marginY ?? 0);
    if ("marginLeft" in B) A.setMargin(YR1, B.marginLeft || 0);
    if ("marginRight" in B) A.setMargin(WR1, B.marginRight || 0);
    if ("marginTop" in B) A.setMargin(N_, B.marginTop || 0);
    if ("marginBottom" in B) A.setMargin(L_, B.marginBottom || 0);
  },
  gi4 = (A, B) => {
    if ("padding" in B) A.setPadding(H51, B.padding ?? 0);
    if ("paddingX" in B) A.setPadding(C51, B.paddingX ?? 0);
    if ("paddingY" in B) A.setPadding(K51, B.paddingY ?? 0);
    if ("paddingLeft" in B) A.setPadding(cL, B.paddingLeft || 0);
    if ("paddingRight" in B) A.setPadding(lL, B.paddingRight || 0);
    if ("paddingTop" in B) A.setPadding(N_, B.paddingTop || 0);
    if ("paddingBottom" in B) A.setPadding(L_, B.paddingBottom || 0);
  },
  ui4 = (A, B) => {
    if ("flexGrow" in B) A.setFlexGrow(B.flexGrow ?? 0);
    if ("flexShrink" in B) A.setFlexShrink(typeof B.flexShrink === "number" ? B.flexShrink : 1);
    if ("flexWrap" in B) {
      if (B.flexWrap === "nowrap") A.setFlexWrap(RR1);
      if (B.flexWrap === "wrap") A.setFlexWrap(OR1);
      if (B.flexWrap === "wrap-reverse") A.setFlexWrap(TR1);
    }
    if ("flexDirection" in B) {
      if (B.flexDirection === "row") A.setFlexDirection(VR1);
      if (B.flexDirection === "row-reverse") A.setFlexDirection(CR1);
      if (B.flexDirection === "column") A.setFlexDirection(JR1);
      if (B.flexDirection === "column-reverse") A.setFlexDirection(XR1);
    }
    if ("flexBasis" in B) if (typeof B.flexBasis === "number") A.setFlexBasis(B.flexBasis);else if (typeof B.flexBasis === "string") A.setFlexBasisPercent(Number.parseInt(B.flexBasis, 10));else A.setFlexBasis(Number.NaN);
    if ("alignItems" in B) {
      if (B.alignItems === "stretch" || !B.alignItems) A.setAlignItems(IR1);
      if (B.alignItems === "flex-start") A.setAlignItems(J51);
      if (B.alignItems === "center") A.setAlignItems(X51);
      if (B.alignItems === "flex-end") A.setAlignItems(V51);
    }
    if ("alignSelf" in B) {
      if (B.alignSelf === "auto" || !B.alignSelf) A.setAlignSelf(FR1);
      if (B.alignSelf === "flex-start") A.setAlignSelf(J51);
      if (B.alignSelf === "center") A.setAlignSelf(X51);
      if (B.alignSelf === "flex-end") A.setAlignSelf(V51);
    }
    if ("justifyContent" in B) {
      if (B.justifyContent === "flex-start" || !B.justifyContent) A.setJustifyContent(ER1);
      if (B.justifyContent === "center") A.setJustifyContent(UR1);
      if (B.justifyContent === "flex-end") A.setJustifyContent(wR1);
      if (B.justifyContent === "space-between") A.setJustifyContent($R1);
      if (B.justifyContent === "space-around") A.setJustifyContent(qR1);
      if (B.justifyContent === "space-evenly") A.setJustifyContent(NR1);
    }
  },
  mi4 = (A, B) => {
    if ("width" in B) if (typeof B.width === "number") A.setWidth(B.width);else if (typeof B.width === "string") A.setWidthPercent(Number.parseInt(B.width, 10));else A.setWidthAuto();
    if ("height" in B) if (typeof B.height === "number") A.setHeight(B.height);else if (typeof B.height === "string") A.setHeightPercent(Number.parseInt(B.height, 10));else A.setHeightAuto();
    if ("minWidth" in B) if (typeof B.minWidth === "string") A.setMinWidthPercent(Number.parseInt(B.minWidth, 10));else A.setMinWidth(B.minWidth ?? 0);
    if ("minHeight" in B) if (typeof B.minHeight === "string") A.setMinHeightPercent(Number.parseInt(B.minHeight, 10));else A.setMinHeight(B.minHeight ?? 0);
  },
  di4 = (A, B) => {
    if ("display" in B) A.setDisplay(B.display === "flex" ? Fo : q_);
  },
  ci4 = (A, B) => {
    if ("borderStyle" in B) {
      let Q = B.borderStyle ? 1 : 0;
      if (B.borderTop !== !1) A.setBorder(N_, Q);
      if (B.borderBottom !== !1) A.setBorder(L_, Q);
      if (B.borderLeft !== !1) A.setBorder(cL, Q);
      if (B.borderRight !== !1) A.setBorder(lL, Q);
    }
  },
  li4 = (A, B) => {
    if ("gap" in B) A.setGap(zR1, B.gap ?? 0);
    if ("columnGap" in B) A.setGap(KR1, B.columnGap ?? 0);
    if ("rowGap" in B) A.setGap(HR1, B.rowGap ?? 0);
  },
  pi4 = (A, B = {}) => {
    fi4(A, B), hi4(A, B), gi4(A, B), ui4(A, B), mi4(A, B), di4(A, B), ci4(A, B), li4(A, B);
  },
  TG0 = pi4;
var KL2 = (A, B) => {
    if (A === B) return;
    if (!A) return B;
    let Q = {},
      D = !1;
    for (let Z of Object.keys(A)) if (B ? !Object.hasOwn(B, Z) : !0) Q[Z] = void 0, D = !0;
    if (B) {
      for (let Z of Object.keys(B)) if (B[Z] !== A[Z]) Q[Z] = B[Z], D = !0;
    }
    return D ? Q : void 0;
  },
  HL2 = A => {
    A?.unsetMeasureFunc(), A?.freeRecursive();
  },
  Iu = zL2.default({
    getRootHostContext: () => ({
      isInsideText: !1
    }),
    prepareForCommit: () => null,
    preparePortalMount: () => null,
    clearContainer: () => !1,
    resetAfterCommit(A) {
      if (typeof A.onComputeLayout === "function") A.onComputeLayout();
      if (A.isStaticDirty) {
        if (A.isStaticDirty = !1, typeof A.onImmediateRender === "function") A.onImmediateRender();
        return;
      }
      if (typeof A.onRender === "function") A.onRender();
    },
    getChildHostContext(A, B) {
      let Q = A.isInsideText,
        D = B === "ink-text" || B === "ink-virtual-text";
      if (Q === D) return A;
      return {
        isInsideText: D
      };
    },
    shouldSetTextContent: () => !1,
    createInstance(A, B, Q, D) {
      if (D.isInsideText && A === "ink-box") throw new Error("<Box> can’t be nested inside <Text> component");
      let Z = A === "ink-text" && D.isInsideText ? "ink-virtual-text" : A,
        G = vR1(Z);
      for (let [F, I] of Object.entries(B)) {
        if (F === "children") continue;
        if (F === "style") {
          if (OG0(G, I), G.yogaNode) TG0(G.yogaNode, I);
          continue;
        }
        if (F === "internal_transform") {
          G.internal_transform = I;
          continue;
        }
        if (F === "internal_static") {
          G.internal_static = !0;
          continue;
        }
        RG0(G, F, I);
      }
      return G;
    },
    createTextInstance(A, B, Q) {
      if (!Q.isInsideText) throw new Error(`Text string "${A}" must be rendered inside <Text> component`);
      return FN2(A);
    },
    resetTextContent() {},
    hideTextInstance(A) {
      $51(A, "");
    },
    unhideTextInstance(A, B) {
      $51(A, B);
    },
    getPublicInstance: A => A,
    hideInstance(A) {
      A.yogaNode?.setDisplay(q_);
    },
    unhideInstance(A) {
      A.yogaNode?.setDisplay(Fo);
    },
    appendInitialChild: bR1,
    appendChild: bR1,
    insertBefore: MG0,
    finalizeInitialChildren(A, B, Q, D) {
      if (A.internal_static) D.isStaticDirty = !0, D.staticNode = A;
      return !1;
    },
    isPrimaryRenderer: !0,
    supportsMutation: !0,
    supportsPersistence: !1,
    supportsHydration: !1,
    scheduleTimeout: setTimeout,
    cancelTimeout: clearTimeout,
    noTimeout: -1,
    getCurrentEventPriority: () => UG0,
    beforeActiveInstanceBlur() {},
    afterActiveInstanceBlur() {},
    detachDeletedInstance() {},
    getInstanceFromNode: () => null,
    prepareScopeUpdate() {},
    getInstanceFromScope: () => null,
    appendChildToContainer: bR1,
    insertInContainerBefore: MG0,
    removeChildFromContainer(A, B) {
      w51(A, B), HL2(B.yogaNode);
    },
    prepareUpdate(A, B, Q, D, Z) {
      if (A.internal_static) Z.isStaticDirty = !0;
      let G = KL2(Q, D),
        F = KL2(Q.style, D.style);
      if (!G && !F) return null;
      return {
        props: G,
        style: F
      };
    },
    commitUpdate(A, {
      props: B,
      style: Q
    }) {
      if (B) for (let [D, Z] of Object.entries(B)) {
        if (D === "style") {
          OG0(A, Z);
          continue;
        }
        if (D === "internal_transform") {
          A.internal_transform = Z;
          continue;
        }
        if (D === "internal_static") {
          A.internal_static = !0;
          continue;
        }
        RG0(A, D, Z);
      }
      if (Q && A.yogaNode) TG0(A.yogaNode, Q);
    },
    commitTextUpdate(A, B, Q) {
      $51(A, Q);
    },
    removeChild(A, B) {
      w51(A, B), HL2(B.yogaNode);
    }
  });
function cG0(A, B = 1, Q = {}) {
  let {
    indent: D = " ",
    includeEmptyLines: Z = !1
  } = Q;
  if (typeof A !== "string") throw new TypeError(`Expected \`input\` to be a \`string\`, got \`${typeof A}\``);
  if (typeof B !== "number") throw new TypeError(`Expected \`count\` to be a \`number\`, got \`${typeof B}\``);
  if (B < 0) throw new RangeError(`Expected \`count\` to be at least 0, got \`${B}\``);
  if (typeof D !== "string") throw new TypeError(`Expected \`options.indent\` to be a \`string\`, got \`${typeof D}\``);
  if (B === 0) return A;
  let G = Z ? /^/gm : /^(?!\s*$)/gm;
  return A.replace(G, D.repeat(B));
}
var Ka4 = A => {
    return A.getComputedWidth() - A.getComputedPadding(cL) - A.getComputedPadding(lL) - A.getComputedBorder(cL) - A.getComputedBorder(lL);
  },
  EL2 = Ka4;
var NL2 = F1($L2(), 1);
var za4 = {
    autoAccept: "rgb(135,0,255)",
    bashBorder: "rgb(255,0,135)",
    claude: "rgb(215,119,87)",
    permission: "rgb(87,105,247)",
    planMode: "rgb(0,102,102)",
    ide: "rgb(71,130,200)",
    secondaryBorder: "rgb(153,153,153)",
    text: "rgb(0,0,0)",
    inverseText: "rgb(255,255,255)",
    secondaryText: "rgb(102,102,102)",
    suggestion: "rgb(87,105,247)",
    remember: "rgb(0,0,255)",
    success: "rgb(44,122,57)",
    error: "rgb(171,43,63)",
    warning: "rgb(150,108,30)",
    diffAdded: "rgb(105,219,124)",
    diffRemoved: "rgb(255,168,180)",
    diffAddedDimmed: "rgb(199,225,203)",
    diffRemovedDimmed: "rgb(253,210,216)",
    diffAddedWord: "rgb(47,157,68)",
    diffRemovedWord: "rgb(209,69,75)",
    diffAddedWordDimmed: "rgb(144,194,156)",
    diffRemovedWordDimmed: "rgb(232,165,173)",
    red: "rgb(220,38,38)",
    blue: "rgb(37,99,235)",
    green: "rgb(22,163,74)",
    yellow: "rgb(202,138,4)",
    purple: "rgb(147,51,234)",
    orange: "rgb(234,88,12)",
    pink: "rgb(219,39,119)",
    cyan: "rgb(8,145,178)"
  },
  Ea4 = {
    autoAccept: "#cd00cd",
    bashBorder: "#cd00cd",
    claude: "#cdcd00",
    permission: "#0000ee",
    planMode: "#00cdcd",
    ide: "#4782C8",
    secondaryBorder: "#e5e5e5",
    text: "#000000",
    inverseText: "#ffffff",
    secondaryText: "#7f7f7f",
    suggestion: "#0000ee",
    remember: "#0000ee",
    success: "#00cd00",
    error: "#cd0000",
    warning: "#cdcd00",
    diffAdded: "#00cd00",
    diffRemoved: "#cd0000",
    diffAddedDimmed: "#00cd00",
    diffRemovedDimmed: "#cd0000",
    diffAddedWord: "#00ff00",
    diffRemovedWord: "#ff0000",
    diffAddedWordDimmed: "#00cd00",
    diffRemovedWordDimmed: "#cd0000",
    red: "#cd0000",
    blue: "#0000cd",
    green: "#00cd00",
    yellow: "#cdcd00",
    purple: "#cd00cd",
    orange: "#cd8500",
    pink: "#ff1493",
    cyan: "#00cdcd"
  },
  Ua4 = {
    autoAccept: "#ff00ff",
    bashBorder: "#ff00ff",
    claude: "#cdcd00",
    permission: "#5c5cff",
    planMode: "#00ffff",
    ide: "#4782c8",
    secondaryBorder: "#e5e5e5",
    text: "#ffffff",
    inverseText: "#000000",
    secondaryText: "#e5e5e5",
    suggestion: "#5c5cff",
    remember: "#5c5cff",
    success: "#00ff00",
    error: "#ff0000",
    warning: "#ffff00",
    diffAdded: "#00cd00",
    diffRemoved: "#cd0000",
    diffAddedDimmed: "#00cd00",
    diffRemovedDimmed: "#cd0000",
    diffAddedWord: "#00ff00",
    diffRemovedWord: "#ff0000",
    diffAddedWordDimmed: "#00cd00",
    diffRemovedWordDimmed: "#cd0000",
    red: "#ff0000",
    blue: "#0000ff",
    green: "#00ff00",
    yellow: "#ffff00",
    purple: "#ff00ff",
    orange: "#ff8700",
    pink: "#ff69b4",
    cyan: "#00ffff"
  },
  wa4 = {
    autoAccept: "rgb(135,0,255)",
    bashBorder: "rgb(0,102,204)",
    claude: "rgb(255,153,51)",
    permission: "rgb(51,102,255)",
    planMode: "rgb(51,102,102)",
    ide: "rgb(71,130,200)",
    secondaryBorder: "rgb(153,153,153)",
    text: "rgb(0,0,0)",
    inverseText: "rgb(255,255,255)",
    secondaryText: "rgb(102,102,102)",
    suggestion: "rgb(51,102,255)",
    remember: "rgb(51,102,255)",
    success: "rgb(0,102,153)",
    error: "rgb(204,0,0)",
    warning: "rgb(255,153,0)",
    diffAdded: "rgb(153,204,255)",
    diffRemoved: "rgb(255,204,204)",
    diffAddedDimmed: "rgb(209,231,253)",
    diffRemovedDimmed: "rgb(255,233,233)",
    diffAddedWord: "rgb(51,102,204)",
    diffRemovedWord: "rgb(153,51,51)",
    diffAddedWordDimmed: "rgb(102,153,204)",
    diffRemovedWordDimmed: "rgb(204,153,153)",
    red: "rgb(204,0,0)",
    blue: "rgb(0,102,204)",
    green: "rgb(0,204,0)",
    yellow: "rgb(255,204,0)",
    purple: "rgb(128,0,128)",
    orange: "rgb(255,128,0)",
    pink: "rgb(255,102,178)",
    cyan: "rgb(0,178,178)"
  },
  $a4 = {
    autoAccept: "rgb(175,135,255)",
    bashBorder: "rgb(253,93,177)",
    claude: "rgb(215,119,87)",
    permission: "rgb(177,185,249)",
    planMode: "rgb(72,150,140)",
    ide: "rgb(71,130,200)",
    secondaryBorder: "rgb(136,136,136)",
    text: "rgb(255,255,255)",
    inverseText: "rgb(0,0,0)",
    secondaryText: "rgb(153,153,153)",
    suggestion: "rgb(177,185,249)",
    remember: "rgb(177,185,249)",
    success: "rgb(78,186,101)",
    error: "rgb(255,107,128)",
    warning: "rgb(255,193,7)",
    diffAdded: "rgb(34,92,43)",
    diffRemoved: "rgb(122,41,54)",
    diffAddedDimmed: "rgb(71,88,74)",
    diffRemovedDimmed: "rgb(105,72,77)",
    diffAddedWord: "rgb(56,166,96)",
    diffRemovedWord: "rgb(179,89,107)",
    diffAddedWordDimmed: "rgb(46,107,58)",
    diffRemovedWordDimmed: "rgb(139,57,69)",
    red: "rgb(220,38,38)",
    blue: "rgb(37,99,235)",
    green: "rgb(22,163,74)",
    yellow: "rgb(202,138,4)",
    purple: "rgb(147,51,234)",
    orange: "rgb(234,88,12)",
    pink: "rgb(219,39,119)",
    cyan: "rgb(8,145,178)"
  },
  qa4 = {
    autoAccept: "rgb(175,135,255)",
    bashBorder: "rgb(51,153,255)",
    claude: "rgb(255,153,51)",
    permission: "rgb(153,204,255)",
    planMode: "rgb(102,153,153)",
    ide: "rgb(71,130,200)",
    secondaryBorder: "rgb(136,136,136)",
    text: "rgb(255,255,255)",
    inverseText: "rgb(0,0,0)",
    secondaryText: "rgb(153,153,153)",
    suggestion: "rgb(153,204,255)",
    remember: "rgb(153,204,255)",
    success: "rgb(51,153,255)",
    error: "rgb(255,102,102)",
    warning: "rgb(255,204,0)",
    diffAdded: "rgb(0,68,102)",
    diffRemoved: "rgb(102,0,0)",
    diffAddedDimmed: "rgb(62,81,91)",
    diffRemovedDimmed: "rgb(62,44,44)",
    diffAddedWord: "rgb(0,119,179)",
    diffRemovedWord: "rgb(179,0,0)",
    diffAddedWordDimmed: "rgb(26,99,128)",
    diffRemovedWordDimmed: "rgb(128,21,21)",
    red: "rgb(255,102,102)",
    blue: "rgb(102,178,255)",
    green: "rgb(102,255,102)",
    yellow: "rgb(255,255,102)",
    purple: "rgb(178,102,255)",
    orange: "rgb(255,178,102)",
    pink: "rgb(255,153,204)",
    cyan: "rgb(102,204,204)"
  };
function qL2(A) {
  switch (A) {
    case "light":
      return za4;
    case "light-ansi":
      return Ea4;
    case "dark-ansi":
      return Ua4;
    case "light-daltonized":
      return wa4;
    case "dark-daltonized":
      return qa4;
    default:
      return $a4;
  }
}
var Na4 = /^rgb\(\s?(\d+),\s?(\d+),\s?(\d+)\s?\)$/,
  La4 = /^ansi256\(\s?(\d+)\s?\)$/,
  Ma4 = (A, B, Q) => {
    if (!B) return A;
    if (B.startsWith("#")) return Q === "foreground" ? A0.hex(B)(A) : A0.bgHex(B)(A);
    if (B.startsWith("ansi256")) {
      let D = La4.exec(B);
      if (!D) return A;
      let Z = Number(D[1]);
      return Q === "foreground" ? A0.ansi256(Z)(A) : A0.bgAnsi256(Z)(A);
    }
    if (B.startsWith("rgb")) {
      let D = Na4.exec(B);
      if (!D) return A;
      let Z = Number(D[1]),
        G = Number(D[2]),
        F = Number(D[3]);
      return Q === "foreground" ? A0.rgb(Z, G, F)(A) : A0.bgRgb(Z, G, F)(A);
    }
    return A;
  };
function lB(A, B, Q = "foreground") {
  return D => Ma4(D, A ? qL2(B)[A] : void 0, Q);
}
var Ra4 = (A, B, Q, D, Z) => {
    if (Q.style.borderStyle) {
      let G = Q.yogaNode.getComputedWidth(),
        F = Q.yogaNode.getComputedHeight(),
        I = typeof Q.style.borderStyle === "string" ? NL2.default[Q.style.borderStyle] : Q.style.borderStyle,
        Y = Q.style.borderTopColor ?? Q.style.borderColor,
        W = Q.style.borderBottomColor ?? Q.style.borderColor,
        J = Q.style.borderLeftColor ?? Q.style.borderColor,
        X = Q.style.borderRightColor ?? Q.style.borderColor,
        V = Q.style.borderTopDimColor ?? Q.style.borderDimColor,
        C = Q.style.borderBottomDimColor ?? Q.style.borderDimColor,
        K = Q.style.borderLeftDimColor ?? Q.style.borderDimColor,
        H = Q.style.borderRightDimColor ?? Q.style.borderDimColor,
        z = Q.style.borderTop !== !1,
        $ = Q.style.borderBottom !== !1,
        L = Q.style.borderLeft !== !1,
        N = Q.style.borderRight !== !1,
        O = G - (L ? 1 : 0) - (N ? 1 : 0),
        R = z ? lB(Y, Z)((L ? I.topLeft : "") + I.top.repeat(O) + (N ? I.topRight : "")) : void 0;
      if (z && V) R = A0.dim(R);
      let T = F;
      if (z) T -= 1;
      if ($) T -= 1;
      let j = (lB(J, Z)(I.left) + `
`).repeat(T);
      if (K) j = A0.dim(j);
      let f = (lB(X, Z)(I.right) + `
`).repeat(T);
      if (H) f = A0.dim(f);
      let y = $ ? lB(W, Z)((L ? I.bottomLeft : "") + I.bottom.repeat(O) + (N ? I.bottomRight : "")) : void 0;
      if ($ && C) y = A0.dim(y);
      let c = z ? 1 : 0;
      if (R) D.write(A, B, R, {
        transformers: []
      });
      if (L) D.write(A, B + c, j, {
        transformers: []
      });
      if (N) D.write(A + G - 1, B + c, f, {
        transformers: []
      });
      if (y) D.write(A, B + F - 1, y, {
        transformers: []
      });
    }
  },
  LL2 = Ra4;
var Oa4 = (A, B) => {
    let Q = A.childNodes[0]?.yogaNode;
    if (Q) {
      let D = Q.getComputedLeft(),
        Z = Q.getComputedTop();
      B = `
`.repeat(Z) + cG0(B, D);
    }
    return B;
  },
  ML2 = (A, B, {
    offsetX: Q = 0,
    offsetY: D = 0,
    transformers: Z = [],
    skipStaticElements: G,
    theme: F
  }) => {
    if (G && A.internal_static) return;
    let {
      yogaNode: I
    } = A;
    if (I) {
      if (I.getDisplay() === q_) return;
      let Y = Q + I.getComputedLeft(),
        W = D + I.getComputedTop(),
        J = Z;
      if (typeof A.internal_transform === "function") J = [A.internal_transform, ...Z];
      if (A.nodeName === "ink-text") {
        let V = xR1(A);
        if (V.length > 0) {
          let C = Io(V),
            K = EL2(I);
          if (C > K) {
            let H = A.style.textWrap ?? "wrap";
            V = Wo(V, K, H);
          }
          V = Oa4(A, V), B.write(Y, W, V, {
            transformers: J
          });
        }
        return;
      }
      let X = !1;
      if (A.nodeName === "ink-box") {
        LL2(Y, W, A, B, F);
        let V = A.style.overflowX === "hidden" || A.style.overflow === "hidden",
          C = A.style.overflowY === "hidden" || A.style.overflow === "hidden";
        if (V || C) {
          let K = V ? Y + I.getComputedBorder(cL) : void 0,
            H = V ? Y + I.getComputedWidth() - I.getComputedBorder(lL) : void 0,
            z = C ? W + I.getComputedBorder(N_) : void 0,
            $ = C ? W + I.getComputedHeight() - I.getComputedBorder(L_) : void 0;
          B.clip({
            x1: K,
            x2: H,
            y1: z,
            y2: $
          }), X = !0;
        }
      }
      if (A.nodeName === "ink-root" || A.nodeName === "ink-box") {
        for (let V of A.childNodes) ML2(V, B, {
          offsetX: Y,
          offsetY: W,
          transformers: J,
          skipStaticElements: G,
          theme: F
        });
        if (X) B.unclip();
      }
    }
  },
  pG0 = ML2;
function iG0(A) {
  if (!Number.isInteger(A)) return !1;
  return M_(A) === 2;
}
var Ta4 = new Set([27, 155]),
  Pa4 = "0".codePointAt(0),
  Sa4 = "9".codePointAt(0),
  aG0 = new Set(),
  nG0 = new Map();
function ja4(A) {
  if (aG0.has(A)) return A;
  if (nG0.has(A)) return nG0.get(A);
  if (A = A.slice(2), A.includes(";")) A = A[0] + "0";
  let B = MD.codes.get(Number.parseInt(A, 10));
  if (B) return MD.color.ansi(B);
  return MD.reset.open;
}
function ya4(A) {
  for (let B = 0; B < A.length; B++) {
    let Q = A.codePointAt(B);
    if (Q >= Pa4 && Q <= Sa4) return B;
  }
  return -1;
}
function ka4(A, B) {
  A = A.slice(B, B + 19);
  let Q = ya4(A);
  if (Q !== -1) {
    let D = A.indexOf("m", Q);
    if (D === -1) D = A.length;
    return A.slice(0, D + 1);
  }
}
function _a4(A, B = Number.POSITIVE_INFINITY) {
  let Q = [],
    D = 0,
    Z = 0;
  while (D < A.length) {
    let G = A.codePointAt(D);
    if (Ta4.has(G)) {
      let Y = ka4(A, D);
      if (Y) {
        Q.push({
          type: "ansi",
          code: Y,
          endCode: ja4(Y)
        }), D += Y.length;
        continue;
      }
    }
    let F = iG0(G),
      I = String.fromCodePoint(G);
    if (Q.push({
      type: "character",
      value: I,
      isFullWidth: F
    }), D += I.length, Z += F ? 2 : I.length, Z >= B) break;
  }
  return Q;
}
function RL2(A) {
  let B = [];
  for (let Q of A) if (Q.code === MD.reset.open) B = [];else if (aG0.has(Q.code)) B = B.filter(D => D.endCode !== Q.code);else B = B.filter(D => D.endCode !== Q.endCode), B.push(Q);
  return B;
}
function xa4(A) {
  return RL2(A).map(({
    endCode: D
  }) => D).reverse().join("");
}
function sG0(A, B, Q) {
  let D = _a4(A, Q),
    Z = [],
    G = 0,
    F = "",
    I = !1;
  for (let Y of D) {
    if (Q !== void 0 && G >= Q) break;
    if (Y.type === "ansi") {
      if (Z.push(Y), I) F += Y.code;
    } else {
      if (!I && G >= B) I = !0, Z = RL2(Z), F = Z.map(({
        code: W
      }) => W).join("");
      if (I) F += Y.value;
      G += Y.isFullWidth ? 2 : Y.value.length;
    }
  }
  return F += xa4(Z), F;
}
var OL2 = new Set([27, 155]),
  BO1 = new Set(),
  rG0 = new Map();
var QO1 = "\x1B]8;;",
  oG0 = QO1.split("").map(A => A.charCodeAt(0)),
  TL2 = "\x07",
  JR5 = TL2.charCodeAt(0),
  va4 = `\x1B]8;;${TL2}`;
function PL2(A) {
  if (BO1.has(A)) return A;
  if (rG0.has(A)) return rG0.get(A);
  if (A.startsWith(QO1)) return va4;
  if (A = A.slice(2), A.includes(";")) A = A[0] + "0";
  let B = MD.codes.get(parseInt(A, 10));
  if (B) return MD.color.ansi(B);else return MD.reset.open;
}
function j51(A) {
  return A.map(B => B.code).join("");
}
function tG0(A) {
  return DO1([], A);
}
function DO1(A, B) {
  let Q = [...A];
  for (let D of B) if (D.code === MD.reset.open) Q = [];else if (BO1.has(D.code)) Q = Q.filter(Z => Z.endCode !== D.code);else Q = Q.filter(Z => Z.endCode !== D.endCode), Q.push(D);
  return Q;
}
function eG0(A) {
  return tG0(A).reverse().map(B => ({
    ...B,
    code: B.endCode
  }));
}
function ZO1(A, B) {
  let Q = new Set(B.map(Z => Z.endCode)),
    D = new Set(A.map(Z => Z.code));
  return [...eG0(A.filter(Z => !Q.has(Z.endCode))), ...B.filter(Z => !D.has(Z.code))];
}
function SL2(A) {
  let B = [],
    Q = [];
  for (let D of A) if (D.type === "ansi") B = DO1(B, [D]);else if (D.type === "char") Q.push({
    ...D,
    styles: [...B]
  });
  return Q;
}
function jL2(A) {
  let B = "";
  for (let Q = 0; Q < A.length; Q++) {
    let D = A[Q];
    if (Q === 0) B += j51(D.styles);else B += j51(ZO1(A[Q - 1].styles, D.styles));
    if (B += D.value, Q === A.length - 1) B += j51(ZO1(D.styles, []));
  }
  return B;
}
function ba4(A) {
  for (let B = 0; B < A.length; B++) {
    let Q = A.charCodeAt(B);
    if (Q >= 48 && Q <= 57) return B;
  }
  return -1;
}
function fa4(A, B) {
  A = A.slice(B);
  for (let D = 1; D < oG0.length; D++) if (A.charCodeAt(D) !== oG0[D]) return;
  let Q = A.indexOf("\x07", QO1.length);
  if (Q === -1) return;
  return A.slice(0, Q + 1);
}
function ha4(A, B) {
  A = A.slice(B, B + 19);
  let Q = ba4(A);
  if (Q !== -1) {
    let D = A.indexOf("m", Q);
    if (D === -1) D = A.length;
    return A.slice(0, D + 1);
  }
}
function yL2(A, B = Number.POSITIVE_INFINITY) {
  let Q = [],
    D = 0,
    Z = 0;
  while (D < A.length) {
    let G = A.codePointAt(D);
    if (OL2.has(G)) {
      let Y = fa4(A, D) || ha4(A, D);
      if (Y) {
        Q.push({
          type: "ansi",
          code: Y,
          endCode: PL2(Y)
        }), D += Y.length;
        continue;
      }
    }
    let F = U51(G),
      I = String.fromCodePoint(G);
    if (Q.push({
      type: "char",
      value: I,
      fullWidth: F
    }), D += I.length, Z += F ? 2 : I.length, Z >= B) break;
  }
  return Q;
}
class y51 {
  width;
  height;
  operations = [];
  charCache = {};
  styledCharsToStringCache = {};
  constructor(A) {
    let {
      width: B,
      height: Q
    } = A;
    this.width = B, this.height = Q;
  }
  write(A, B, Q, D) {
    let {
      transformers: Z
    } = D;
    if (!Q) return;
    this.operations.push({
      type: "write",
      x: A,
      y: B,
      text: Q,
      transformers: Z
    });
  }
  clip(A) {
    this.operations.push({
      type: "clip",
      clip: A
    });
  }
  unclip() {
    this.operations.push({
      type: "unclip"
    });
  }
  get() {
    let A = [];
    for (let D = 0; D < this.height; D++) {
      let Z = [];
      for (let G = 0; G < this.width; G++) Z.push({
        type: "char",
        value: " ",
        fullWidth: !1,
        styles: []
      });
      A.push(Z);
    }
    let B = [];
    for (let D of this.operations) {
      if (D.type === "clip") B.push(D.clip);
      if (D.type === "unclip") B.pop();
      if (D.type === "write") {
        let {
            text: Z,
            transformers: G
          } = D,
          {
            x: F,
            y: I
          } = D,
          Y = Z.split(`
`),
          W = B.at(-1);
        if (W) {
          let X = typeof W?.x1 === "number" && typeof W?.x2 === "number",
            V = typeof W?.y1 === "number" && typeof W?.y2 === "number";
          if (X) {
            let C = Io(Z);
            if (F + C < W.x1 || F > W.x2) continue;
          }
          if (V) {
            let C = Y.length;
            if (I + C < W.y1 || I > W.y2) continue;
          }
          if (X) {
            if (Y = Y.map(C => {
              let K = F < W.x1 ? W.x1 - F : 0,
                H = z51(C),
                z = F + H > W.x2 ? W.x2 - F : H;
              return sG0(C, K, z);
            }), F < W.x1) F = W.x1;
          }
          if (V) {
            let C = I < W.y1 ? W.y1 - I : 0,
              K = Y.length,
              H = I + K > W.y2 ? W.y2 - I : K;
            if (Y = Y.slice(C, H), I < W.y1) I = W.y1;
          }
        }
        let J = 0;
        for (let [X, V] of Y.entries()) {
          let C = A[I + J];
          if (!C) continue;
          for (let z of G) V = z(V, X);
          if (!this.charCache.hasOwnProperty(V)) this.charCache[V] = SL2(yL2(V));
          let K = this.charCache[V],
            H = F;
          for (let z of K) {
            C[H] = z;
            let $ = z.fullWidth || z.value.length > 1;
            if ($) C[H + 1] = {
              type: "char",
              value: "",
              fullWidth: !1,
              styles: z.styles
            };
            H += $ ? 2 : 1;
          }
          J++;
        }
      }
    }
    return {
      output: A.map(D => {
        let Z = D.filter(F => F !== void 0),
          G = JSON.stringify(Z);
        if (!this.styledCharsToStringCache.hasOwnProperty(G)) {
          let F = jL2(Z).trimEnd();
          this.styledCharsToStringCache[G] = F;
        }
        return this.styledCharsToStringCache[G];
      }).join(`
`),
      height: A.length
    };
  }
}
var ga4 = (A, B) => {
    if (A.yogaNode) {
      let Q = new y51({
        width: A.yogaNode.getComputedWidth(),
        height: A.yogaNode.getComputedHeight()
      });
      pG0(A, Q, {
        skipStaticElements: !0,
        theme: B
      });
      let D;
      if (A.staticNode?.yogaNode) D = new y51({
        width: A.staticNode.yogaNode.getComputedWidth(),
        height: A.staticNode.yogaNode.getComputedHeight()
      }), pG0(A.staticNode, D, {
        skipStaticElements: !1,
        theme: B
      });
      let {
        output: Z,
        height: G
      } = Q.get();
      return {
        output: Z,
        outputHeight: G,
        staticOutput: D ? `${D.get().output}
` : ""
      };
    }
    return {
      output: "",
      outputHeight: 0,
      staticOutput: ""
    };
  },
  kL2 = ga4;
import dL2 from "node:process";
var gL2 = F1(bL2(), 1),
  uL2 = F1(hL2(), 1);
import ma4 from "node:process";
var da4 = gL2.default(() => {
    uL2.default(() => {
      ma4.stderr.write("\x1B[?25h");
    }, {
      alwaysLast: !0
    });
  }),
  mL2 = da4;
var JO1 = !1,
  Eo = {};
Eo.show = (A = dL2.stderr) => {
  if (!A.isTTY) return;
  JO1 = !1, A.write("\x1B[?25h");
};
Eo.hide = (A = dL2.stderr) => {
  if (!A.isTTY) return;
  mL2(), JO1 = !0, A.write("\x1B[?25l");
};
Eo.toggle = (A, B) => {
  if (A !== void 0) JO1 = A;
  if (JO1) Eo.show(B);else Eo.hide(B);
};
var y_ = Eo;
var ca4 = (A, {
    showCursor: B = !1
  } = {}) => {
    let Q = 0,
      D = "",
      Z = !1,
      G = F => {
        if (!B && !Z) y_.hide(), Z = !0;
        let I = F + `
`;
        if (I === D) return;
        D = I, A.write(w_.eraseLines(Q) + I), Q = I.split(`
`).length;
      };
    return G.clear = () => {
      A.write(w_.eraseLines(Q)), D = "", Q = 0;
    }, G.updateLineCount = F => {
      Q = F.split(`
`).length;
    }, G.resetLineCount = () => {
      Q = 0;
    }, G.done = () => {
      if (D = "", Q = 0, !B) y_.show(), Z = !1;
    }, G;
  },
  la4 = {
    create: ca4
  },
  cL2 = la4;
var pa4 = new Map(),
  Xu = pa4;
var nL = F1(w1(), 1);
import { EventEmitter as Vs4 } from "node:events";
var lL2 = F1(w1(), 1),
  pL2 = lL2.createContext({
    exit() {}
  });
pL2.displayName = "InternalAppContext";
var GF0 = pL2;
var iL2 = F1(w1(), 1);
import { EventEmitter as ia4 } from "node:events";
var nL2 = iL2.createContext({
  stdin: process.stdin,
  internal_eventEmitter: new ia4(),
  setRawMode() {},
  isRawModeSupported: !1,
  internal_exitOnCtrlC: !0,
  internal_resetLineCount() {}
});
nL2.displayName = "InternalStdinContext";
var XO1 = nL2;
var aL2 = F1(w1(), 1),
  sL2 = aL2.createContext({
    stdout: process.stdout,
    write() {}
  });
sL2.displayName = "InternalStdoutContext";
var FF0 = sL2;
var rL2 = F1(w1(), 1),
  oL2 = rL2.createContext({
    stderr: process.stderr,
    write() {}
  });
oL2.displayName = "InternalStderrContext";
var IF0 = oL2;
var tL2 = F1(w1(), 1),
  eL2 = tL2.createContext({
    activeId: void 0,
    add() {},
    remove() {},
    activate() {},
    deactivate() {},
    enableFocus() {},
    disableFocus() {},
    focusNext() {},
    focusPrevious() {},
    focus() {}
  });
eL2.displayName = "InternalFocusContext";
var VO1 = eL2;
var oG = F1(w1(), 1),
  KF0 = F1(GM2(), 1);
import * as HO1 from "node:fs";
import { cwd as XM2 } from "node:process";
var ea4 = (A, B = 2) => {
    return A.replace(/^\t+/gm, Q => " ".repeat(Q.length * B));
  },
  FM2 = ea4;
var As4 = (A, B) => {
    let Q = [],
      D = A - B,
      Z = A + B;
    for (let G = D; G <= Z; G++) Q.push(G);
    return Q;
  },
  Bs4 = (A, B, Q = {}) => {
    var D;
    if (typeof A !== "string") throw new TypeError("Source code is missing.");
    if (!B || B < 1) throw new TypeError("Line number must start from `1`.");
    let Z = FM2(A).split(/\r?\n/);
    if (B > Z.length) return;
    return As4(B, (D = Q.around) !== null && D !== void 0 ? D : 3).filter(G => Z[G - 1] !== void 0).map(G => ({
      line: G,
      value: Z[G - 1]
    }));
  },
  IM2 = Bs4;
var CO1 = F1(w1(), 1),
  WF0 = CO1.forwardRef(({
    children: A,
    ...B
  }, Q) => {
    return CO1.default.createElement("ink-box", {
      ref: Q,
      style: {
        ...B,
        overflowX: B.overflowX ?? B.overflow ?? "visible",
        overflowY: B.overflowY ?? B.overflow ?? "visible"
      }
    }, A);
  });
WF0.displayName = "Box";
WF0.defaultProps = {
  flexWrap: "nowrap",
  flexDirection: "row",
  flexGrow: 0,
  flexShrink: 1
};
var x = WF0;
var YM2 = F1(w1(), 1);
var KO1 = F1(w1(), 1),
  k_ = F1(w1(), 1);
var XF0 = k_.createContext({
  theme: null,
  setTheme: A => A,
  setPreviewTheme: A => A,
  savePreview: () => {},
  currentTheme: null
});
function VF0({
  children: A,
  initialState: B
}) {
  let [Q, D] = k_.useState(B),
    [Z, G] = k_.useState(null),
    F = KO1.useMemo(() => ({
      theme: Q,
      setTheme: I => {
        pA({
          ...E0(),
          theme: I
        }), D(I), JF0(I), G(null);
      },
      setPreviewTheme: I => {
        G(I), JF0(I);
      },
      savePreview: () => {
        if (Z !== null) pA({
          ...E0(),
          theme: Z
        }), D(Z), G(null);
      },
      currentTheme: Z ?? Q
    }), [Q, Z]);
  return KO1.default.createElement(XF0.Provider, {
    value: F
  }, A);
}
function sB() {
  let {
    currentTheme: A,
    setTheme: B
  } = k_.useContext(XF0);
  return [A, B];
}
function P({
  color: A,
  backgroundColor: B,
  dimColor: Q = !1,
  bold: D = !1,
  italic: Z = !1,
  underline: G = !1,
  strikethrough: F = !1,
  inverse: I = !1,
  wrap: Y = "wrap",
  children: W
}) {
  let [J] = sB();
  if (W === void 0 || W === null) return null;
  return YM2.default.createElement("ink-text", {
    style: {
      flexGrow: 0,
      flexShrink: 1,
      flexDirection: "row",
      textWrap: Y
    },
    internal_transform: V => {
      if (Q) V = A0.dim(V);
      if (A) V = lB(A, J)(V);
      if (B) V = lB(B, J, "background")(V);
      if (D) V = A0.bold(V);
      if (Z) V = A0.italic(V);
      if (G) V = A0.underline(V);
      if (F) V = A0.strikethrough(V);
      if (I) V = A0.inverse(V);
      return V;
    }
  }, W);
}
var WM2 = A => {
    return A?.replace(`file://${XM2()}/`, "");
  },
  JM2 = new KF0.default({
    cwd: XM2(),
    internals: KF0.default.nodeInternals()
  });
function HF0({
  error: A
}) {
  let B = A.stack ? A.stack.split(`
`).slice(1) : void 0,
    Q = B ? JM2.parseLine(B[0]) : void 0,
    D = WM2(Q?.file),
    Z,
    G = 0;
  if (D && Q?.line && HO1.existsSync(D)) {
    let F = HO1.readFileSync(D, "utf8");
    if (Z = IM2(F, Q.line), Z) for (let {
      line: I
    } of Z) G = Math.max(G, String(I).length);
  }
  return oG.default.createElement(x, {
    flexDirection: "column",
    padding: 1
  }, oG.default.createElement(x, null, oG.default.createElement(P, {
    backgroundColor: "error",
    color: "text"
  }, " ", "ERROR", " "), oG.default.createElement(P, null, " ", A.message)), Q && D && oG.default.createElement(x, {
    marginTop: 1
  }, oG.default.createElement(P, {
    dimColor: !0
  }, D, ":", Q.line, ":", Q.column)), Q && Z && oG.default.createElement(x, {
    marginTop: 1,
    flexDirection: "column"
  }, Z.map(({
    line: F,
    value: I
  }) => oG.default.createElement(x, {
    key: F
  }, oG.default.createElement(x, {
    width: G + 1
  }, oG.default.createElement(P, {
    dimColor: F !== Q.line,
    backgroundColor: F === Q.line ? "error" : void 0,
    color: F === Q.line ? "text" : void 0
  }, String(F).padStart(G, " "), ":")), oG.default.createElement(P, {
    key: F,
    backgroundColor: F === Q.line ? "error" : void 0,
    color: F === Q.line ? "text" : void 0
  }, " " + I)))), A.stack && oG.default.createElement(x, {
    marginTop: 1,
    flexDirection: "column"
  }, A.stack.split(`
`).slice(1).map(F => {
    let I = JM2.parseLine(F);
    if (!I) return oG.default.createElement(x, {
      key: F
    }, oG.default.createElement(P, {
      dimColor: !0
    }, "- "), oG.default.createElement(P, {
      dimColor: !0,
      bold: !0
    }, F));
    return oG.default.createElement(x, {
      key: F
    }, oG.default.createElement(P, {
      dimColor: !0
    }, "- "), oG.default.createElement(P, {
      dimColor: !0,
      bold: !0
    }, I.function), oG.default.createElement(P, {
      dimColor: !0,
      color: "secondaryText"
    }, " ", "(", WM2(I.file) ?? "", ":", I.line, ":", I.column, ")"));
  })));
}
import { Buffer as Qs4 } from "node:buffer";
var Ds4 = /^(?:\x1b)([a-zA-Z0-9])$/,
  Zs4 = /^(?:\x1b+)(O|N|\[|\[\[)(?:(\d+)(?:;(\d+))?([~^$])|(?:1;)?(\d+)?([a-zA-Z]))/,
  Gs4 = "\x1B[200~",
  zO1 = "\x1B[201~";
function Fs4(A) {
  return {
    name: "",
    fn: !1,
    ctrl: !1,
    meta: !1,
    shift: !1,
    option: !1,
    sequence: A,
    raw: A,
    isPasted: !0
  };
}
var Is4 = new RegExp("^(.*?)(" + ["\\x1b\\][0-9]*(?:;[^\\x07\\x1b]*)*(?:\\x07|\\x1b\\\\)", "\\x1bP[^\\x1b]*\\x1b\\\\", "\\x1b\\[[0-9]*(?:;[0-9]*)*[A-Za-z~]", "\\x1bO[A-Za-z]", "\\x1b[\\x00-\\x7F]", "\\x1b\\x1b", "$"].map(A => `(?:${A})`).join("|") + ")", "s"),
  Ys4 = new RegExp("(.*?)(" + ["\\x1b\\][0-9]*(?:;[^\\x07\\x1b]*)*$", "\\x1bP[^\\x1b]*$", "\\x1b\\[[0-9]*(?:;[0-9]*)*$", "\\x1bO$", "\\x1b$", "$"].map(A => `(?:${A})`).join("|") + ")", "s"),
  CM2 = {
    mode: "NORMAL",
    incomplete: ""
  };
function Ws4(A) {
  if (Qs4.isBuffer(A)) {
    if (A[0] > 127 && A[1] === void 0) return A[0] -= 128, "\x1B" + String(A);else return String(A);
  } else if (A !== void 0 && typeof A !== "string") return String(A);else if (!A) return "";else return A;
}
function KM2(A, B = "") {
  let Q = B === null,
    D = Q ? "" : Ws4(B);
  if (A.mode === "IN_PASTE") {
    if ((A.incomplete.slice(-zO1.length + 1) + D).indexOf(zO1) === -1) return [[], {
      ...A,
      incomplete: A.incomplete + D
    }];
  }
  let Z = A.incomplete + D,
    G = {
      ...A,
      incomplete: ""
    },
    F = [],
    I = {
      NORMAL: () => {
        let Y = Is4.exec(Z);
        Z = Z.substring(Y[0].length);
        let W = Y[1];
        if (!Y[2] && !Q) {
          let J = Ys4.exec(W);
          G.incomplete = J[2], W = J[1];
        }
        if (W) F.push(VM2(W));
        if (Y[2] === Gs4) G.mode = "IN_PASTE";else if (Y[2]) F.push(VM2(Y[2]));
      },
      IN_PASTE: () => {
        let Y = Z.indexOf(zO1);
        if (Y === -1) {
          if (!Q) {
            G.incomplete = Z, Z = "";
            return;
          }
          Y = Z.length;
        }
        let W = Z.substring(0, Y);
        if (W) F.push(Fs4(W));
        Z = Z.substring(Y + zO1.length), G.mode = "NORMAL";
      }
    };
  while (Z) I[G.mode]();
  return [F, G];
}
var HM2 = {
    OP: "f1",
    OQ: "f2",
    OR: "f3",
    OS: "f4",
    "[11~": "f1",
    "[12~": "f2",
    "[13~": "f3",
    "[14~": "f4",
    "[[A": "f1",
    "[[B": "f2",
    "[[C": "f3",
    "[[D": "f4",
    "[[E": "f5",
    "[15~": "f5",
    "[17~": "f6",
    "[18~": "f7",
    "[19~": "f8",
    "[20~": "f9",
    "[21~": "f10",
    "[23~": "f11",
    "[24~": "f12",
    "[A": "up",
    "[B": "down",
    "[C": "right",
    "[D": "left",
    "[E": "clear",
    "[F": "end",
    "[H": "home",
    OA: "up",
    OB: "down",
    OC: "right",
    OD: "left",
    OE: "clear",
    OF: "end",
    OH: "home",
    "[1~": "home",
    "[2~": "insert",
    "[3~": "delete",
    "[4~": "end",
    "[5~": "pageup",
    "[6~": "pagedown",
    "[[5~": "pageup",
    "[[6~": "pagedown",
    "[7~": "home",
    "[8~": "end",
    "[a": "up",
    "[b": "down",
    "[c": "right",
    "[d": "left",
    "[e": "clear",
    "[2$": "insert",
    "[3$": "delete",
    "[5$": "pageup",
    "[6$": "pagedown",
    "[7$": "home",
    "[8$": "end",
    Oa: "up",
    Ob: "down",
    Oc: "right",
    Od: "left",
    Oe: "clear",
    "[2^": "insert",
    "[3^": "delete",
    "[5^": "pageup",
    "[6^": "pagedown",
    "[7^": "home",
    "[8^": "end",
    "[Z": "tab"
  },
  zM2 = [...Object.values(HM2), "backspace"],
  Js4 = A => {
    return ["[a", "[b", "[c", "[d", "[e", "[2$", "[3$", "[5$", "[6$", "[7$", "[8$", "[Z"].includes(A);
  },
  Xs4 = A => {
    return ["Oa", "Ob", "Oc", "Od", "Oe", "[2^", "[3^", "[5^", "[6^", "[7^", "[8^"].includes(A);
  },
  VM2 = (A = "") => {
    let B,
      Q = {
        name: "",
        fn: !1,
        ctrl: !1,
        meta: !1,
        shift: !1,
        option: !1,
        sequence: A,
        raw: A,
        isPasted: !1
      };
    if (Q.sequence = Q.sequence || A || Q.name, A === "\r") Q.raw = void 0, Q.name = "return";else if (A === `
`) Q.name = "enter";else if (A === "\t") Q.name = "tab";else if (A === "\b" || A === "\x1B\b") Q.name = "backspace", Q.meta = A.charAt(0) === "\x1B";else if (A === "" || A === "\x1B") Q.name = "backspace", Q.meta = A.charAt(0) === "\x1B";else if (A === "\x1B" || A === "\x1B\x1B") Q.name = "escape", Q.meta = A.length === 2;else if (A === " " || A === "\x1B ") Q.name = "space", Q.meta = A.length === 2;else if (A === "\x1F") Q.name = "_", Q.ctrl = !0;else if (A <= "\x1A" && A.length === 1) Q.name = String.fromCharCode(A.charCodeAt(0) + 97 - 1), Q.ctrl = !0;else if (A.length === 1 && A >= "0" && A <= "9") Q.name = "number";else if (A.length === 1 && A >= "a" && A <= "z") Q.name = A;else if (A.length === 1 && A >= "A" && A <= "Z") Q.name = A.toLowerCase(), Q.shift = !0;else if (B = Ds4.exec(A)) Q.meta = !0, Q.shift = /^[A-Z]$/.test(B[1]);else if (B = Zs4.exec(A)) {
      let D = [...A];
      if (D[0] === "\x1B" && D[1] === "\x1B") Q.option = !0;
      let Z = [B[1], B[2], B[4], B[6]].filter(Boolean).join(""),
        G = (B[3] || B[5] || 1) - 1;
      Q.ctrl = !!(G & 4), Q.meta = !!(G & 10), Q.shift = !!(G & 1), Q.code = Z, Q.name = HM2[Z], Q.shift = Js4(Z) || Q.shift, Q.ctrl = Xs4(Z) || Q.ctrl;
    }
    if (Q.raw === "\x1Bb") Q.meta = !0, Q.name = "left";else if (Q.raw === "\x1Bf") Q.meta = !0, Q.name = "right";
    switch (A) {
      case "\x1B[1~":
        return {
          name: "home",
          ctrl: !1,
          meta: !1,
          shift: !1,
          option: !1,
          fn: !1,
          sequence: A,
          raw: A,
          isPasted: !1
        };
      case "\x1B[4~":
        return {
          name: "end",
          ctrl: !1,
          meta: !1,
          shift: !1,
          option: !1,
          fn: !1,
          sequence: A,
          raw: A,
          isPasted: !1
        };
      case "\x1B[5~":
        return {
          name: "pageup",
          ctrl: !1,
          meta: !1,
          shift: !1,
          option: !1,
          fn: !1,
          sequence: A,
          raw: A,
          isPasted: !1
        };
      case "\x1B[6~":
        return {
          name: "pagedown",
          ctrl: !1,
          meta: !1,
          shift: !1,
          option: !1,
          fn: !1,
          sequence: A,
          raw: A,
          isPasted: !1
        };
      case "\x1B[1;5D":
        return {
          name: "left",
          ctrl: !0,
          meta: !1,
          shift: !1,
          option: !1,
          fn: !1,
          sequence: A,
          raw: A,
          isPasted: !1
        };
      case "\x1B[1;5C":
        return {
          name: "right",
          ctrl: !0,
          meta: !1,
          shift: !1,
          option: !1,
          fn: !1,
          sequence: A,
          raw: A,
          isPasted: !1
        };
      case "\x1B[1~":
        return {
          name: "left",
          ctrl: !0,
          fn: !0,
          meta: !1,
          shift: !1,
          option: !1,
          sequence: A,
          raw: A,
          isPasted: !1
        };
      case "\x1B[4~":
        return {
          name: "right",
          ctrl: !0,
          fn: !0,
          meta: !1,
          shift: !1,
          option: !1,
          sequence: A,
          raw: A,
          isPasted: !1
        };
    }
    return Q;
  };
var Cs4 = "\t",
  Ks4 = "\x1B[Z",
  Hs4 = "\x1B",
  zs4 = process.platform !== "win32";
class EO1 extends nL.PureComponent {
  static displayName = "InternalApp";
  static getDerivedStateFromError(A) {
    return {
      error: A
    };
  }
  state = {
    isFocusEnabled: !0,
    activeFocusId: void 0,
    focusables: [],
    error: void 0
  };
  rawModeEnabledCount = 0;
  internal_eventEmitter = new Vs4();
  keyParseState = CM2;
  incompleteEscapeTimer = null;
  NORMAL_TIMEOUT = 50;
  PASTE_TIMEOUT = 500;
  isRawModeSupported() {
    return this.props.stdin.isTTY;
  }
  render() {
    return nL.default.createElement(GF0.Provider, {
      value: {
        exit: this.handleExit
      }
    }, nL.default.createElement(VF0, {
      initialState: this.props.initialTheme
    }, nL.default.createElement(XO1.Provider, {
      value: {
        stdin: this.props.stdin,
        setRawMode: this.handleSetRawMode,
        isRawModeSupported: this.isRawModeSupported(),
        internal_exitOnCtrlC: this.props.exitOnCtrlC,
        internal_eventEmitter: this.internal_eventEmitter,
        internal_resetLineCount: this.props.resetLineCount
      }
    }, nL.default.createElement(FF0.Provider, {
      value: {
        stdout: this.props.stdout,
        write: this.props.writeToStdout
      }
    }, nL.default.createElement(IF0.Provider, {
      value: {
        stderr: this.props.stderr,
        write: this.props.writeToStderr
      }
    }, nL.default.createElement(VO1.Provider, {
      value: {
        activeId: this.state.activeFocusId,
        add: this.addFocusable,
        remove: this.removeFocusable,
        activate: this.activateFocusable,
        deactivate: this.deactivateFocusable,
        enableFocus: this.enableFocus,
        disableFocus: this.disableFocus,
        focusNext: this.focusNext,
        focusPrevious: this.focusPrevious,
        focus: this.focus
      }
    }, this.state.error ? nL.default.createElement(HF0, {
      error: this.state.error
    }) : this.props.children))))));
  }
  componentDidMount() {
    y_.hide(this.props.stdout);
  }
  componentWillUnmount() {
    if (y_.show(this.props.stdout), this.incompleteEscapeTimer) clearTimeout(this.incompleteEscapeTimer), this.incompleteEscapeTimer = null;
    if (this.isRawModeSupported()) this.handleSetRawMode(!1);
  }
  componentDidCatch(A) {
    this.handleExit(A);
  }
  handleSetRawMode = A => {
    let {
      stdin: B
    } = this.props;
    if (!this.isRawModeSupported()) if (B === process.stdin) throw new Error(`Raw mode is not supported on the current process.stdin, which Ink uses as input stream by default.
Read about how to prevent this error on https://github.com/vadimdemedes/ink/#israwmodesupported`);else throw new Error(`Raw mode is not supported on the stdin provided to Ink.
Read about how to prevent this error on https://github.com/vadimdemedes/ink/#israwmodesupported`);
    if (B.setEncoding("utf8"), A) {
      if (this.rawModeEnabledCount === 0) B.ref(), B.setRawMode(!0), B.addListener("readable", this.handleReadable), this.props.stdout.write("\x1B[?2004h");
      this.rawModeEnabledCount++;
      return;
    }
    if (--this.rawModeEnabledCount === 0) this.props.stdout.write("\x1B[?2004l"), B.setRawMode(!1), B.removeListener("readable", this.handleReadable), B.unref();
  };
  flushIncomplete = () => {
    if (this.incompleteEscapeTimer = null, !this.keyParseState.incomplete) return;
    this.processInput(null);
  };
  processInput = A => {
    let [B, Q] = KM2(this.keyParseState, A);
    this.keyParseState = Q;
    for (let D of B) this.handleInput(D.sequence), this.internal_eventEmitter.emit("input", D);
    if (this.keyParseState.incomplete) {
      if (this.incompleteEscapeTimer) clearTimeout(this.incompleteEscapeTimer);
      this.incompleteEscapeTimer = setTimeout(this.flushIncomplete, this.keyParseState.mode === "IN_PASTE" ? this.PASTE_TIMEOUT : this.NORMAL_TIMEOUT);
    }
  };
  handleReadable = () => {
    let A;
    while ((A = this.props.stdin.read()) !== null) this.processInput(A);
  };
  handleInput = A => {
    if (A === "\x03" && this.props.exitOnCtrlC) this.handleExit();
    if (A === "\x1A" && zs4) this.handleSuspend();
    if (A === Hs4 && this.state.activeFocusId) this.setState({
      activeFocusId: void 0
    });
    if (this.state.isFocusEnabled && this.state.focusables.length > 0) {
      if (A === Cs4) this.focusNext();
      if (A === Ks4) this.focusPrevious();
    }
  };
  handleExit = A => {
    if (this.isRawModeSupported()) this.handleSetRawMode(!1);
    this.props.onExit(A);
  };
  handleSuspend = () => {
    if (!this.isRawModeSupported()) return;
    let A = this.rawModeEnabledCount;
    while (this.rawModeEnabledCount > 0) this.handleSetRawMode(!1);
    y_.show(this.props.stdout), this.internal_eventEmitter.emit("suspend");
    let B = () => {
      for (let Q = 0; Q < A; Q++) if (this.isRawModeSupported()) this.handleSetRawMode(!0);
      y_.hide(this.props.stdout), this.internal_eventEmitter.emit("resume"), process.removeListener("SIGCONT", B);
    };
    process.on("SIGCONT", B), process.kill(process.pid, "SIGSTOP");
  };
  enableFocus = () => {
    this.setState({
      isFocusEnabled: !0
    });
  };
  disableFocus = () => {
    this.setState({
      isFocusEnabled: !1
    });
  };
  focus = A => {
    this.setState(B => {
      if (!B.focusables.some(D => D?.id === A)) return B;
      return {
        activeFocusId: A
      };
    });
  };
  focusNext = () => {
    this.setState(A => {
      let B = A.focusables.find(D => D.isActive)?.id;
      return {
        activeFocusId: this.findNextFocusable(A) ?? B
      };
    });
  };
  focusPrevious = () => {
    this.setState(A => {
      let B = A.focusables.findLast(D => D.isActive)?.id;
      return {
        activeFocusId: this.findPreviousFocusable(A) ?? B
      };
    });
  };
  addFocusable = (A, {
    autoFocus: B
  }) => {
    this.setState(Q => {
      let D = Q.activeFocusId;
      if (!D && B) D = A;
      return {
        activeFocusId: D,
        focusables: [...Q.focusables, {
          id: A,
          isActive: !0
        }]
      };
    });
  };
  removeFocusable = A => {
    this.setState(B => ({
      activeFocusId: B.activeFocusId === A ? void 0 : B.activeFocusId,
      focusables: B.focusables.filter(Q => {
        return Q.id !== A;
      })
    }));
  };
  activateFocusable = A => {
    this.setState(B => ({
      focusables: B.focusables.map(Q => {
        if (Q.id !== A) return Q;
        return {
          id: A,
          isActive: !0
        };
      })
    }));
  };
  deactivateFocusable = A => {
    this.setState(B => ({
      activeFocusId: B.activeFocusId === A ? void 0 : B.activeFocusId,
      focusables: B.focusables.map(Q => {
        if (Q.id !== A) return Q;
        return {
          id: A,
          isActive: !1
        };
      })
    }));
  };
  findNextFocusable = A => {
    let B = A.focusables.findIndex(Q => {
      return Q.id === A.activeFocusId;
    });
    for (let Q = B + 1; Q < A.focusables.length; Q++) {
      let D = A.focusables[Q];
      if (D?.isActive) return D.id;
    }
    return;
  };
  findPreviousFocusable = A => {
    let B = A.focusables.findIndex(Q => {
      return Q.id === A.activeFocusId;
    });
    for (let Q = B - 1; Q >= 0; Q--) {
      let D = A.focusables[Q];
      if (D?.isActive) return D.id;
    }
    return;
  };
}
var Vu = Boolean(!1),
  EM2 = () => {};
class UO1 {
  options;
  log;
  throttledLog;
  isUnmounted;
  lastOutput;
  lastOutputHeight;
  container;
  rootNode = null;
  fullStaticOutput;
  exitPromise;
  restoreConsole;
  unsubscribeResize;
  constructor(A) {
    this.options = A;
    if (GG0(this), this.log = cL2.create(A.stdout), this.throttledLog = A.debug ? this.log : oM1(this.log, void 0, {
      leading: !0,
      trailing: !0
    }), this.isUnmounted = !1, this.lastOutput = "", this.lastOutputHeight = 0, this.fullStaticOutput = "", this.unsubscribeExit = qW1(this.unmount, {
      alwaysLast: !1
    }), A.patchConsole) this.patchConsole();
    if (!Vu) A.stdout.on("resize", this.resized), this.unsubscribeResize = () => {
      A.stdout.off("resize", this.resized);
    };
    if (this.rootNode = vR1("ink-root"), this.rootNode.onComputeLayout = this.calculateLayout, this.rootNode.onRender = A.debug ? this.onRender : oM1(this.onRender, 32, {
      leading: !0,
      trailing: !0
    }), this.rootNode.onImmediateRender = this.onRender, this.container = Iu.createContainer(this.rootNode, 0, null, !1, null, "id", () => {}, null), process.env.DEV === "true") Iu.injectIntoDevTools({
      bundleType: 0,
      version: "16.13.1",
      rendererPackageName: "ink"
    });
  }
  resized = () => {
    this.calculateLayout(), this.onRender(!0);
  };
  resolveExitPromise = () => {};
  rejectExitPromise = () => {};
  unsubscribeExit = () => {};
  calculateLayout = () => {
    let A = this.options.stdout.columns || 80;
    if (!this.rootNode) return;
    this.rootNode.yogaNode.setWidth(A), this.rootNode.yogaNode.calculateLayout(void 0, void 0, SR1.DIRECTION_LTR);
  };
  setTheme(A) {
    this.options.theme = A;
  }
  onRender(A = !1) {
    if (this.isUnmounted) return;
    if (!this.rootNode) return;
    let {
        output: B,
        outputHeight: Q,
        staticOutput: D
      } = kL2(this.rootNode, this.options.theme),
      Z = D && D !== `
`;
    if (this.options.debug) {
      if (Z) this.fullStaticOutput += D;
      this.options.stdout.write(this.fullStaticOutput + B);
      return;
    }
    if (Vu) {
      if (Z) this.options.stdout.write(D);
      this.lastOutput = B, this.lastOutputHeight = Q;
      return;
    }
    if (Z) this.fullStaticOutput += D;
    if (Q >= this.options.stdout.rows || this.lastOutputHeight >= this.options.stdout.rows) {
      if (this.options.onFlicker) this.options.onFlicker(Q, this.options.stdout.rows);
      this.options.stdout.write(w_.clearTerminal + this.fullStaticOutput + B + `
`), this.lastOutput = B, this.lastOutputHeight = Q, this.log.updateLineCount(B + `
`);
      return;
    }
    if (A) {
      this.options.stdout.write(w_.clearTerminal + this.fullStaticOutput + B + `
`), this.lastOutput = B, this.lastOutputHeight = Q, this.log.updateLineCount(B + `
`);
      return;
    }
    if (Z) this.log.clear(), this.options.stdout.write(D), this.throttledLog(B);
    if (!Z && B !== this.lastOutput) this.throttledLog(B);
    this.lastOutput = B, this.lastOutputHeight = Q;
  }
  render(A) {
    let B = UM2.default.createElement(EO1, {
      initialTheme: this.options.theme,
      stdin: this.options.stdin,
      stdout: this.options.stdout,
      stderr: this.options.stderr,
      writeToStdout: this.writeToStdout,
      writeToStderr: this.writeToStderr,
      exitOnCtrlC: this.options.exitOnCtrlC,
      onExit: this.unmount,
      resetLineCount: this.resetLineCount
    }, A);
    Iu.updateContainer(B, this.container, null, EM2);
  }
  writeToStdout(A) {
    if (this.isUnmounted) return;
    if (this.options.debug) {
      this.options.stdout.write(A + this.fullStaticOutput + this.lastOutput);
      return;
    }
    if (Vu) {
      this.options.stdout.write(A);
      return;
    }
    this.log.clear(), this.options.stdout.write(A), this.log(this.lastOutput);
  }
  writeToStderr(A) {
    if (this.isUnmounted) return;
    if (this.options.debug) {
      this.options.stderr.write(A), this.options.stdout.write(this.fullStaticOutput + this.lastOutput);
      return;
    }
    if (Vu) {
      this.options.stderr.write(A);
      return;
    }
    this.log.clear(), this.options.stderr.write(A), this.log(this.lastOutput);
  }
  unmount(A) {
    if (this.isUnmounted) return;
    if (this.calculateLayout(), this.onRender(), this.unsubscribeExit(), typeof this.restoreConsole === "function") this.restoreConsole();
    if (typeof this.unsubscribeResize === "function") this.unsubscribeResize();
    if (Vu) this.options.stdout.write(this.lastOutput + `
`);else if (!this.options.debug) this.log.done();
    if (this.isUnmounted = !0, Iu.updateContainer(null, this.container, null, EM2), Xu.delete(this.options.stdout), A instanceof Error) this.rejectExitPromise(A);else this.resolveExitPromise();
  }
  async waitUntilExit() {
    return this.exitPromise ||= new Promise((A, B) => {
      this.resolveExitPromise = A, this.rejectExitPromise = B;
    }), this.exitPromise;
  }
  clear() {
    if (!Vu && !this.options.debug) this.log.clear();
  }
  resetLineCount() {
    if (!Vu && !this.options.debug) this.log.resetLineCount();
  }
  patchConsole() {
    if (this.options.debug) return;
    this.restoreConsole = p$2((A, B) => {
      if (A === "stdout") this.writeToStdout(B);
      if (A === "stderr") {
        if (!B.startsWith("The above error occurred")) this.writeToStderr(B);
      }
    });
  }
}
function JF0(A) {
  Xu.forEach(B => {
    B.setTheme(A);
  });
}
var Us4 = (A, B) => {
    let Q = ws4(B),
      D = {
        stdout: process.stdout,
        stdin: process.stdin,
        stderr: process.stderr,
        debug: !1,
        exitOnCtrlC: !0,
        patchConsole: !0,
        theme: Q.theme ?? E0().theme,
        ...Q
      },
      Z = $s4(D.stdout, () => new UO1(D));
    return Z.render(A), {
      rerender: Z.render,
      unmount() {
        Z.unmount();
      },
      waitUntilExit: Z.waitUntilExit,
      cleanup: () => Xu.delete(D.stdout),
      clear: Z.clear
    };
  },
  q8 = Us4,
  ws4 = (A = {}) => {
    if (A instanceof Es4) return {
      stdout: A,
      stdin: process.stdin
    };
    return A;
  },
  $s4 = (A, B) => {
    let Q = Xu.get(A);
    if (!Q) Q = B(), Xu.set(A, Q);
    return Q;
  };
module.exports = {
  $W1,
  $Y1,
  $r1,
  $tB,
  A$2,
  A0,
  AI,
  AS0,
  AV,
  Af,
  ArB,
  Au,
  B9,
  BO,
  BW1,
  Bh,
  Bk0,
  BrB,
  C1,
  C5Q,
  C9,
  CK,
  CS0,
  Cr1,
  CsB,
  Dm1,
  DrB,
  Ds4,
  E0,
  E51,
  EI,
  EY1,
  E_,
  F1,
  F9,
  FD,
  FH1,
  FS0,
  Fm1,
  FsA,
  FsB,
  GD,
  GP,
  HM2,
  HeB,
  I0,
  IG,
  IS0,
  Ig,
  Im1,
  IsB,
  Iu,
  J5Q,
  Jm1,
  Jp,
  Js4,
  LD,
  LY,
  Li4,
  Lj0,
  LtB,
  MA,
  MK1,
  MtB,
  N$2,
  NS0,
  NY,
  Ni4,
  NtB,
  OS0,
  OT,
  Oj,
  On0,
  Oo0,
  Os1,
  Ou1,
  P,
  P$2,
  P9,
  P91,
  PS0,
  Pc,
  Pj,
  Pm1,
  QO1,
  QP,
  Qk0,
  QrB,
  R2,
  RS0,
  RT,
  Rc,
  Rj,
  RtB,
  S$2,
  SA,
  SB1,
  Sk0,
  Sm1,
  T1,
  T3,
  T7,
  T91,
  TL2,
  TS0,
  UK1,
  UY,
  UY1,
  U_,
  V5Q,
  Vp,
  Vr1,
  WV,
  Wg,
  Wo,
  X2,
  X5Q,
  XF0,
  XO1,
  Xs4,
  YM2,
  YQ,
  Yg,
  YsB,
  Zs4,
  _K1,
  _N,
  _Y1,
  _j,
  a0,
  a81,
  aA1,
  aZ0,
  ar1,
  asB,
  b91,
  bC,
  bH,
  bO,
  bl4,
  bo0,
  c91,
  cA1,
  cM1,
  cT0,
  cY1,
  c_9,
  cc,
  cq,
  dA1,
  dY1,
  dq,
  eP0,
  eR,
  eY1,
  eYA,
  eZ0,
  eb,
  esB,
  ew2,
  f4,
  f41,
  fH,
  fV1,
  fY1,
  fr1,
  g,
  gQ,
  gY1,
  gc,
  hV1,
  hY1,
  hc,
  hy,
  i2,
  iM1,
  iY1,
  ic,
  iq,
  isB,
  j5,
  jK1,
  jj,
  jm1,
  jtB,
  kM1,
  kN,
  kS0,
  k_,
  kl,
  ko0,
  kw,
  lB,
  lw2,
  mV1,
  mY,
  mY1,
  mq,
  nM1,
  nY1,
  nZ0,
  nc,
  nq,
  nsB,
  o3,
  oY1,
  oYA,
  or,
  osB,
  p2,
  p91,
  pA,
  pY1,
  pZ,
  pc,
  pq,
  psB,
  q8,
  qQ0,
  qYA,
  qtB,
  r81,
  rG,
  rsB,
  sB,
  sc,
  ssB,
  sw2,
  t9,
  tC,
  tJ,
  tR,
  tsB,
  uD,
  uL,
  v41,
  v9,
  vG,
  vH,
  vM1,
  vN,
  vj,
  vl,
  vo0,
  w1,
  wS0,
  wV,
  wY1,
  x,
  x1,
  x41,
  x8,
  x9,
  xH,
  xV,
  xi,
  xj,
  xu1,
  xz,
  yB1,
  yj,
  yl,
  zeB,
  zw
};