/**
 * Isolated bundle for 'x5'
 */

function JN0() {
  return {
    async: !1,
    breaks: !1,
    extensions: null,
    gfm: !0,
    hooks: null,
    pedantic: !1,
    renderer: null,
    silent: !1,
    tokenizer: null,
    walkTokens: null
  };
}
var em = JN0();
function QSB(A) {
  em = A;
}
var UG1 = {
  exec: () => null
};
function r5(A, B = "") {
  let Q = typeof A === "string" ? A : A.source,
    D = {
      replace: (Z, G) => {
        let F = typeof G === "string" ? G : G.source;
        return F = F.replace($X.caret, "$1"), Q = Q.replace(Z, F), D;
      },
      getRegex: () => {
        return new RegExp(Q, B);
      }
    };
  return D;
}
var $X = {
    codeRemoveIndent: /^(?: {1,4}| {0,3}\t)/gm,
    outputLinkReplace: /\\([\[\]])/g,
    indentCodeCompensation: /^(\s+)(?:```)/,
    beginningSpace: /^\s+/,
    endingHash: /#$/,
    startingSpaceChar: /^ /,
    endingSpaceChar: / $/,
    nonSpaceChar: /[^ ]/,
    newLineCharGlobal: /\n/g,
    tabCharGlobal: /\t/g,
    multipleSpaceGlobal: /\s+/g,
    blankLine: /^[ \t]*$/,
    doubleBlankLine: /\n[ \t]*\n[ \t]*$/,
    blockquoteStart: /^ {0,3}>/,
    blockquoteSetextReplace: /\n {0,3}((?:=+|-+) *)(?=\n|$)/g,
    blockquoteSetextReplace2: /^ {0,3}>[ \t]?/gm,
    listReplaceTabs: /^\t+/,
    listReplaceNesting: /^ {1,4}(?=( {4})*[^ ])/g,
    listIsTask: /^\[[ xX]\] /,
    listReplaceTask: /^\[[ xX]\] +/,
    anyLine: /\n.*\n/,
    hrefBrackets: /^<(.*)>$/,
    tableDelimiter: /[:|]/,
    tableAlignChars: /^\||\| *$/g,
    tableRowBlankLine: /\n[ \t]*$/,
    tableAlignRight: /^ *-+: *$/,
    tableAlignCenter: /^ *:-+: *$/,
    tableAlignLeft: /^ *:-+ *$/,
    startATag: /^<a /i,
    endATag: /^<\/a>/i,
    startPreScriptTag: /^<(pre|code|kbd|script)(\s|>)/i,
    endPreScriptTag: /^<\/(pre|code|kbd|script)(\s|>)/i,
    startAngleBracket: /^</,
    endAngleBracket: />$/,
    pedanticHrefTitle: /^([^'"]*[^\s])\s+(['"])(.*)\2/,
    unicodeAlphaNumeric: /[\p{L}\p{N}]/u,
    escapeTest: /[&<>"']/,
    escapeReplace: /[&<>"']/g,
    escapeTestNoEncode: /[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,
    escapeReplaceNoEncode: /[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,
    unescapeTest: /&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,
    caret: /(^|[^\[])\^/g,
    percentDecode: /%25/g,
    findPipe: /\|/g,
    splitPipe: / \|/,
    slashPipe: /\\\|/g,
    carriageReturn: /\r\n|\r/g,
    spaceLine: /^ +$/gm,
    notSpaceStart: /^\S*/,
    endingNewline: /\n$/,
    listItemRegex: A => new RegExp(`^( {0,3}${A})((?:[	 ][^\\n]*)?(?:\\n|$))`),
    nextBulletRegex: A => new RegExp(`^ {0,${Math.min(3, A - 1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),
    hrRegex: A => new RegExp(`^ {0,${Math.min(3, A - 1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),
    fencesBeginRegex: A => new RegExp(`^ {0,${Math.min(3, A - 1)}}(?:\`\`\`|~~~)`),
    headingBeginRegex: A => new RegExp(`^ {0,${Math.min(3, A - 1)}}#`),
    htmlBeginRegex: A => new RegExp(`^ {0,${Math.min(3, A - 1)}}<(?:[a-z].*>|!--)`, "i")
  },
  DI8 = /^(?:[ \t]*(?:\n|$))+/,
  ZI8 = /^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,
  GI8 = /^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,
  NG1 = /^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,
  FI8 = /^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,
  DSB = /(?:[*+-]|\d{1,9}[.)])/,
  ZSB = r5(/^(?!bull |blockCode|fences|blockquote|heading|html)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g, DSB).replace(/blockCode/g, /(?: {4}| {0,3}\t)/).replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g, / {0,3}>/).replace(/heading/g, / {0,3}#{1,6}/).replace(/html/g, / {0,3}<[^\n>]+>\n/).getRegex(),
  XN0 = /^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,
  II8 = /^[^\n]+/,
  VN0 = /(?!\s*\])(?:\\.|[^\[\]\\])+/,
  YI8 = r5(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label", VN0).replace("title", /(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),
  WI8 = r5(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g, DSB).getRegex(),
  Ib1 = "address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",
  CN0 = /<!--(?:-?>|[\s\S]*?(?:-->|$))/,
  JI8 = r5("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))", "i").replace("comment", CN0).replace("tag", Ib1).replace("attribute", / +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),
  GSB = r5(XN0).replace("hr", NG1).replace("heading", " {0,3}#{1,6}(?:\\s|$)").replace("|lheading", "").replace("|table", "").replace("blockquote", " {0,3}>").replace("fences", " {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list", " {0,3}(?:[*+-]|1[.)]) ").replace("html", "</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag", Ib1).getRegex(),
  XI8 = r5(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph", GSB).getRegex(),
  KN0 = {
    blockquote: XI8,
    code: ZI8,
    def: YI8,
    fences: GI8,
    heading: FI8,
    hr: NG1,
    html: JI8,
    lheading: ZSB,
    list: WI8,
    newline: DI8,
    paragraph: GSB,
    table: UG1,
    text: II8
  },
  oPB = r5("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr", NG1).replace("heading", " {0,3}#{1,6}(?:\\s|$)").replace("blockquote", " {0,3}>").replace("code", "(?: {4}| {0,3}	)[^\\n]").replace("fences", " {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list", " {0,3}(?:[*+-]|1[.)]) ").replace("html", "</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag", Ib1).getRegex(),
  VI8 = {
    ...KN0,
    table: oPB,
    paragraph: r5(XN0).replace("hr", NG1).replace("heading", " {0,3}#{1,6}(?:\\s|$)").replace("|lheading", "").replace("table", oPB).replace("blockquote", " {0,3}>").replace("fences", " {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list", " {0,3}(?:[*+-]|1[.)]) ").replace("html", "</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag", Ib1).getRegex()
  },
  CI8 = {
    ...KN0,
    html: r5(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment", CN0).replace(/tag/g, "(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),
    def: /^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,
    heading: /^(#{1,6})(.*)(?:\n+|$)/,
    fences: UG1,
    lheading: /^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,
    paragraph: r5(XN0).replace("hr", NG1).replace("heading", ` *#{1,6} *[^
]`).replace("lheading", ZSB).replace("|table", "").replace("blockquote", " {0,3}>").replace("|fences", "").replace("|list", "").replace("|html", "").replace("|tag", "").getRegex()
  },
  KI8 = /^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,
  HI8 = /^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,
  FSB = /^( {2,}|\\)\n(?!\s*$)/,
  zI8 = /^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,
  Yb1 = /[\p{P}\p{S}]/u,
  HN0 = /[\s\p{P}\p{S}]/u,
  ISB = /[^\s\p{P}\p{S}]/u,
  EI8 = r5(/^((?![*_])punctSpace)/, "u").replace(/punctSpace/g, HN0).getRegex(),
  YSB = /(?!~)[\p{P}\p{S}]/u,
  UI8 = /(?!~)[\s\p{P}\p{S}]/u,
  wI8 = /(?:[^\s\p{P}\p{S}]|~)/u,
  $I8 = /\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,
  WSB = /^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,
  qI8 = r5(WSB, "u").replace(/punct/g, Yb1).getRegex(),
  NI8 = r5(WSB, "u").replace(/punct/g, YSB).getRegex(),
  JSB = "^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",
  LI8 = r5(JSB, "gu").replace(/notPunctSpace/g, ISB).replace(/punctSpace/g, HN0).replace(/punct/g, Yb1).getRegex(),
  MI8 = r5(JSB, "gu").replace(/notPunctSpace/g, wI8).replace(/punctSpace/g, UI8).replace(/punct/g, YSB).getRegex(),
  RI8 = r5("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)", "gu").replace(/notPunctSpace/g, ISB).replace(/punctSpace/g, HN0).replace(/punct/g, Yb1).getRegex(),
  OI8 = r5(/\\(punct)/, "gu").replace(/punct/g, Yb1).getRegex(),
  TI8 = r5(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme", /[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email", /[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),
  PI8 = r5(CN0).replace("(?:-->|$)", "-->").getRegex(),
  SI8 = r5("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment", PI8).replace("attribute", /\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),
  Fb1 = /(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,
  jI8 = r5(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label", Fb1).replace("href", /<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title", /"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),
  XSB = r5(/^!?\[(label)\]\[(ref)\]/).replace("label", Fb1).replace("ref", VN0).getRegex(),
  VSB = r5(/^!?\[(ref)\](?:\[\])?/).replace("ref", VN0).getRegex(),
  yI8 = r5("reflink|nolink(?!\\()", "g").replace("reflink", XSB).replace("nolink", VSB).getRegex(),
  zN0 = {
    _backpedal: UG1,
    anyPunctuation: OI8,
    autolink: TI8,
    blockSkip: $I8,
    br: FSB,
    code: HI8,
    del: UG1,
    emStrongLDelim: qI8,
    emStrongRDelimAst: LI8,
    emStrongRDelimUnd: RI8,
    escape: KI8,
    link: jI8,
    nolink: VSB,
    punctuation: EI8,
    reflink: XSB,
    reflinkSearch: yI8,
    tag: SI8,
    text: zI8,
    url: UG1
  },
  kI8 = {
    ...zN0,
    link: r5(/^!?\[(label)\]\((.*?)\)/).replace("label", Fb1).getRegex(),
    reflink: r5(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label", Fb1).getRegex()
  },
  WN0 = {
    ...zN0,
    emStrongRDelimAst: MI8,
    emStrongLDelim: NI8,
    url: r5(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/, "i").replace("email", /[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),
    _backpedal: /(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,
    del: /^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,
    text: /^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/
  },
  _I8 = {
    ...WN0,
    br: r5(FSB).replace("{2,}", "*").getRegex(),
    text: r5(WN0.text).replace("\\b_", "\\b_| {2,}\\n").replace(/\{2,\}/g, "*").getRegex()
  },
  Gb1 = {
    normal: KN0,
    gfm: VI8,
    pedantic: CI8
  },
  zG1 = {
    normal: zN0,
    gfm: WN0,
    breaks: _I8,
    pedantic: kI8
  },
  xI8 = {
    "&": "&amp;",
    "<": "&lt;",
    ">": "&gt;",
    '"': "&quot;",
    "'": "&#39;"
  },
  tPB = A => xI8[A];
function pM(A, B) {
  if (B) {
    if ($X.escapeTest.test(A)) return A.replace($X.escapeReplace, tPB);
  } else if ($X.escapeTestNoEncode.test(A)) return A.replace($X.escapeReplaceNoEncode, tPB);
  return A;
}
function ePB(A) {
  try {
    A = encodeURI(A).replace($X.percentDecode, "%");
  } catch {
    return null;
  }
  return A;
}
function ASB(A, B) {
  let Q = A.replace($X.findPipe, (G, F, I) => {
      let Y = !1,
        W = F;
      while (--W >= 0 && I[W] === "\\") Y = !Y;
      if (Y) return "|";else return " |";
    }),
    D = Q.split($X.splitPipe),
    Z = 0;
  if (!D[0].trim()) D.shift();
  if (D.length > 0 && !D.at(-1)?.trim()) D.pop();
  if (B) if (D.length > B) D.splice(B);else while (D.length < B) D.push("");
  for (; Z < D.length; Z++) D[Z] = D[Z].trim().replace($X.slashPipe, "|");
  return D;
}
function EG1(A, B, Q) {
  let D = A.length;
  if (D === 0) return "";
  let Z = 0;
  while (Z < D) if (A.charAt(D - Z - 1) === B) Z++;else break;
  return A.slice(0, D - Z);
}
function vI8(A, B) {
  if (A.indexOf(B[1]) === -1) return -1;
  let Q = 0;
  for (let D = 0; D < A.length; D++) if (A[D] === "\\") D++;else if (A[D] === B[0]) Q++;else if (A[D] === B[1]) {
    if (Q--, Q < 0) return D;
  }
  return -1;
}
function BSB(A, B, Q, D, Z) {
  let G = B.href,
    F = B.title || null,
    I = A[1].replace(Z.other.outputLinkReplace, "$1");
  if (A[0].charAt(0) !== "!") {
    D.state.inLink = !0;
    let Y = {
      type: "link",
      raw: Q,
      href: G,
      title: F,
      text: I,
      tokens: D.inlineTokens(I)
    };
    return D.state.inLink = !1, Y;
  }
  return {
    type: "image",
    raw: Q,
    href: G,
    title: F,
    text: I
  };
}
function bI8(A, B, Q) {
  let D = A.match(Q.other.indentCodeCompensation);
  if (D === null) return B;
  let Z = D[1];
  return B.split(`
`).map(G => {
    let F = G.match(Q.other.beginningSpace);
    if (F === null) return G;
    let [I] = F;
    if (I.length >= Z.length) return G.slice(Z.length);
    return G;
  }).join(`
`);
}
class $G1 {
  options;
  rules;
  lexer;
  constructor(A) {
    this.options = A || em;
  }
  space(A) {
    let B = this.rules.block.newline.exec(A);
    if (B && B[0].length > 0) return {
      type: "space",
      raw: B[0]
    };
  }
  code(A) {
    let B = this.rules.block.code.exec(A);
    if (B) {
      let Q = B[0].replace(this.rules.other.codeRemoveIndent, "");
      return {
        type: "code",
        raw: B[0],
        codeBlockStyle: "indented",
        text: !this.options.pedantic ? EG1(Q, `
`) : Q
      };
    }
  }
  fences(A) {
    let B = this.rules.block.fences.exec(A);
    if (B) {
      let Q = B[0],
        D = bI8(Q, B[3] || "", this.rules);
      return {
        type: "code",
        raw: Q,
        lang: B[2] ? B[2].trim().replace(this.rules.inline.anyPunctuation, "$1") : B[2],
        text: D
      };
    }
  }
  heading(A) {
    let B = this.rules.block.heading.exec(A);
    if (B) {
      let Q = B[2].trim();
      if (this.rules.other.endingHash.test(Q)) {
        let D = EG1(Q, "#");
        if (this.options.pedantic) Q = D.trim();else if (!D || this.rules.other.endingSpaceChar.test(D)) Q = D.trim();
      }
      return {
        type: "heading",
        raw: B[0],
        depth: B[1].length,
        text: Q,
        tokens: this.lexer.inline(Q)
      };
    }
  }
  hr(A) {
    let B = this.rules.block.hr.exec(A);
    if (B) return {
      type: "hr",
      raw: EG1(B[0], `
`)
    };
  }
  blockquote(A) {
    let B = this.rules.block.blockquote.exec(A);
    if (B) {
      let Q = EG1(B[0], `
`).split(`
`),
        D = "",
        Z = "",
        G = [];
      while (Q.length > 0) {
        let F = !1,
          I = [],
          Y;
        for (Y = 0; Y < Q.length; Y++) if (this.rules.other.blockquoteStart.test(Q[Y])) I.push(Q[Y]), F = !0;else if (!F) I.push(Q[Y]);else break;
        Q = Q.slice(Y);
        let W = I.join(`
`),
          J = W.replace(this.rules.other.blockquoteSetextReplace, `
    $1`).replace(this.rules.other.blockquoteSetextReplace2, "");
        D = D ? `${D}
${W}` : W, Z = Z ? `${Z}
${J}` : J;
        let X = this.lexer.state.top;
        if (this.lexer.state.top = !0, this.lexer.blockTokens(J, G, !0), this.lexer.state.top = X, Q.length === 0) break;
        let V = G.at(-1);
        if (V?.type === "code") break;else if (V?.type === "blockquote") {
          let C = V,
            K = C.raw + `
` + Q.join(`
`),
            H = this.blockquote(K);
          G[G.length - 1] = H, D = D.substring(0, D.length - C.raw.length) + H.raw, Z = Z.substring(0, Z.length - C.text.length) + H.text;
          break;
        } else if (V?.type === "list") {
          let C = V,
            K = C.raw + `
` + Q.join(`
`),
            H = this.list(K);
          G[G.length - 1] = H, D = D.substring(0, D.length - V.raw.length) + H.raw, Z = Z.substring(0, Z.length - C.raw.length) + H.raw, Q = K.substring(G.at(-1).raw.length).split(`
`);
          continue;
        }
      }
      return {
        type: "blockquote",
        raw: D,
        tokens: G,
        text: Z
      };
    }
  }
  list(A) {
    let B = this.rules.block.list.exec(A);
    if (B) {
      let Q = B[1].trim(),
        D = Q.length > 1,
        Z = {
          type: "list",
          raw: "",
          ordered: D,
          start: D ? +Q.slice(0, -1) : "",
          loose: !1,
          items: []
        };
      if (Q = D ? `\\d{1,9}\\${Q.slice(-1)}` : `\\${Q}`, this.options.pedantic) Q = D ? Q : "[*+-]";
      let G = this.rules.other.listItemRegex(Q),
        F = !1;
      while (A) {
        let Y = !1,
          W = "",
          J = "";
        if (!(B = G.exec(A))) break;
        if (this.rules.block.hr.test(A)) break;
        W = B[0], A = A.substring(W.length);
        let X = B[2].split(`
`, 1)[0].replace(this.rules.other.listReplaceTabs, $ => " ".repeat(3 * $.length)),
          V = A.split(`
`, 1)[0],
          C = !X.trim(),
          K = 0;
        if (this.options.pedantic) K = 2, J = X.trimStart();else if (C) K = B[1].length + 1;else K = B[2].search(this.rules.other.nonSpaceChar), K = K > 4 ? 1 : K, J = X.slice(K), K += B[1].length;
        if (C && this.rules.other.blankLine.test(V)) W += V + `
`, A = A.substring(V.length + 1), Y = !0;
        if (!Y) {
          let $ = this.rules.other.nextBulletRegex(K),
            L = this.rules.other.hrRegex(K),
            N = this.rules.other.fencesBeginRegex(K),
            O = this.rules.other.headingBeginRegex(K),
            R = this.rules.other.htmlBeginRegex(K);
          while (A) {
            let T = A.split(`
`, 1)[0],
              j;
            if (V = T, this.options.pedantic) V = V.replace(this.rules.other.listReplaceNesting, "  "), j = V;else j = V.replace(this.rules.other.tabCharGlobal, "    ");
            if (N.test(V)) break;
            if (O.test(V)) break;
            if (R.test(V)) break;
            if ($.test(V)) break;
            if (L.test(V)) break;
            if (j.search(this.rules.other.nonSpaceChar) >= K || !V.trim()) J += `
` + j.slice(K);else {
              if (C) break;
              if (X.replace(this.rules.other.tabCharGlobal, "    ").search(this.rules.other.nonSpaceChar) >= 4) break;
              if (N.test(X)) break;
              if (O.test(X)) break;
              if (L.test(X)) break;
              J += `
` + V;
            }
            if (!C && !V.trim()) C = !0;
            W += T + `
`, A = A.substring(T.length + 1), X = j.slice(K);
          }
        }
        if (!Z.loose) {
          if (F) Z.loose = !0;else if (this.rules.other.doubleBlankLine.test(W)) F = !0;
        }
        let H = null,
          z;
        if (this.options.gfm) {
          if (H = this.rules.other.listIsTask.exec(J), H) z = H[0] !== "[ ] ", J = J.replace(this.rules.other.listReplaceTask, "");
        }
        Z.items.push({
          type: "list_item",
          raw: W,
          task: !!H,
          checked: z,
          loose: !1,
          text: J,
          tokens: []
        }), Z.raw += W;
      }
      let I = Z.items.at(-1);
      if (I) I.raw = I.raw.trimEnd(), I.text = I.text.trimEnd();else return;
      Z.raw = Z.raw.trimEnd();
      for (let Y = 0; Y < Z.items.length; Y++) if (this.lexer.state.top = !1, Z.items[Y].tokens = this.lexer.blockTokens(Z.items[Y].text, []), !Z.loose) {
        let W = Z.items[Y].tokens.filter(X => X.type === "space"),
          J = W.length > 0 && W.some(X => this.rules.other.anyLine.test(X.raw));
        Z.loose = J;
      }
      if (Z.loose) for (let Y = 0; Y < Z.items.length; Y++) Z.items[Y].loose = !0;
      return Z;
    }
  }
  html(A) {
    let B = this.rules.block.html.exec(A);
    if (B) return {
      type: "html",
      block: !0,
      raw: B[0],
      pre: B[1] === "pre" || B[1] === "script" || B[1] === "style",
      text: B[0]
    };
  }
  def(A) {
    let B = this.rules.block.def.exec(A);
    if (B) {
      let Q = B[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal, " "),
        D = B[2] ? B[2].replace(this.rules.other.hrefBrackets, "$1").replace(this.rules.inline.anyPunctuation, "$1") : "",
        Z = B[3] ? B[3].substring(1, B[3].length - 1).replace(this.rules.inline.anyPunctuation, "$1") : B[3];
      return {
        type: "def",
        tag: Q,
        raw: B[0],
        href: D,
        title: Z
      };
    }
  }
  table(A) {
    let B = this.rules.block.table.exec(A);
    if (!B) return;
    if (!this.rules.other.tableDelimiter.test(B[2])) return;
    let Q = ASB(B[1]),
      D = B[2].replace(this.rules.other.tableAlignChars, "").split("|"),
      Z = B[3]?.trim() ? B[3].replace(this.rules.other.tableRowBlankLine, "").split(`
`) : [],
      G = {
        type: "table",
        raw: B[0],
        header: [],
        align: [],
        rows: []
      };
    if (Q.length !== D.length) return;
    for (let F of D) if (this.rules.other.tableAlignRight.test(F)) G.align.push("right");else if (this.rules.other.tableAlignCenter.test(F)) G.align.push("center");else if (this.rules.other.tableAlignLeft.test(F)) G.align.push("left");else G.align.push(null);
    for (let F = 0; F < Q.length; F++) G.header.push({
      text: Q[F],
      tokens: this.lexer.inline(Q[F]),
      header: !0,
      align: G.align[F]
    });
    for (let F of Z) G.rows.push(ASB(F, G.header.length).map((I, Y) => {
      return {
        text: I,
        tokens: this.lexer.inline(I),
        header: !1,
        align: G.align[Y]
      };
    }));
    return G;
  }
  lheading(A) {
    let B = this.rules.block.lheading.exec(A);
    if (B) return {
      type: "heading",
      raw: B[0],
      depth: B[2].charAt(0) === "=" ? 1 : 2,
      text: B[1],
      tokens: this.lexer.inline(B[1])
    };
  }
  paragraph(A) {
    let B = this.rules.block.paragraph.exec(A);
    if (B) {
      let Q = B[1].charAt(B[1].length - 1) === `
` ? B[1].slice(0, -1) : B[1];
      return {
        type: "paragraph",
        raw: B[0],
        text: Q,
        tokens: this.lexer.inline(Q)
      };
    }
  }
  text(A) {
    let B = this.rules.block.text.exec(A);
    if (B) return {
      type: "text",
      raw: B[0],
      text: B[0],
      tokens: this.lexer.inline(B[0])
    };
  }
  escape(A) {
    let B = this.rules.inline.escape.exec(A);
    if (B) return {
      type: "escape",
      raw: B[0],
      text: B[1]
    };
  }
  tag(A) {
    let B = this.rules.inline.tag.exec(A);
    if (B) {
      if (!this.lexer.state.inLink && this.rules.other.startATag.test(B[0])) this.lexer.state.inLink = !0;else if (this.lexer.state.inLink && this.rules.other.endATag.test(B[0])) this.lexer.state.inLink = !1;
      if (!this.lexer.state.inRawBlock && this.rules.other.startPreScriptTag.test(B[0])) this.lexer.state.inRawBlock = !0;else if (this.lexer.state.inRawBlock && this.rules.other.endPreScriptTag.test(B[0])) this.lexer.state.inRawBlock = !1;
      return {
        type: "html",
        raw: B[0],
        inLink: this.lexer.state.inLink,
        inRawBlock: this.lexer.state.inRawBlock,
        block: !1,
        text: B[0]
      };
    }
  }
  link(A) {
    let B = this.rules.inline.link.exec(A);
    if (B) {
      let Q = B[2].trim();
      if (!this.options.pedantic && this.rules.other.startAngleBracket.test(Q)) {
        if (!this.rules.other.endAngleBracket.test(Q)) return;
        let G = EG1(Q.slice(0, -1), "\\");
        if ((Q.length - G.length) % 2 === 0) return;
      } else {
        let G = vI8(B[2], "()");
        if (G > -1) {
          let I = (B[0].indexOf("!") === 0 ? 5 : 4) + B[1].length + G;
          B[2] = B[2].substring(0, G), B[0] = B[0].substring(0, I).trim(), B[3] = "";
        }
      }
      let D = B[2],
        Z = "";
      if (this.options.pedantic) {
        let G = this.rules.other.pedanticHrefTitle.exec(D);
        if (G) D = G[1], Z = G[3];
      } else Z = B[3] ? B[3].slice(1, -1) : "";
      if (D = D.trim(), this.rules.other.startAngleBracket.test(D)) if (this.options.pedantic && !this.rules.other.endAngleBracket.test(Q)) D = D.slice(1);else D = D.slice(1, -1);
      return BSB(B, {
        href: D ? D.replace(this.rules.inline.anyPunctuation, "$1") : D,
        title: Z ? Z.replace(this.rules.inline.anyPunctuation, "$1") : Z
      }, B[0], this.lexer, this.rules);
    }
  }
  reflink(A, B) {
    let Q;
    if ((Q = this.rules.inline.reflink.exec(A)) || (Q = this.rules.inline.nolink.exec(A))) {
      let D = (Q[2] || Q[1]).replace(this.rules.other.multipleSpaceGlobal, " "),
        Z = B[D.toLowerCase()];
      if (!Z) {
        let G = Q[0].charAt(0);
        return {
          type: "text",
          raw: G,
          text: G
        };
      }
      return BSB(Q, Z, Q[0], this.lexer, this.rules);
    }
  }
  emStrong(A, B, Q = "") {
    let D = this.rules.inline.emStrongLDelim.exec(A);
    if (!D) return;
    if (D[3] && Q.match(this.rules.other.unicodeAlphaNumeric)) return;
    if (!(D[1] || D[2]) || !Q || this.rules.inline.punctuation.exec(Q)) {
      let G = [...D[0]].length - 1,
        F,
        I,
        Y = G,
        W = 0,
        J = D[0][0] === "*" ? this.rules.inline.emStrongRDelimAst : this.rules.inline.emStrongRDelimUnd;
      J.lastIndex = 0, B = B.slice(-1 * A.length + G);
      while ((D = J.exec(B)) != null) {
        if (F = D[1] || D[2] || D[3] || D[4] || D[5] || D[6], !F) continue;
        if (I = [...F].length, D[3] || D[4]) {
          Y += I;
          continue;
        } else if (D[5] || D[6]) {
          if (G % 3 && !((G + I) % 3)) {
            W += I;
            continue;
          }
        }
        if (Y -= I, Y > 0) continue;
        I = Math.min(I, I + Y + W);
        let X = [...D[0]][0].length,
          V = A.slice(0, G + D.index + X + I);
        if (Math.min(G, I) % 2) {
          let K = V.slice(1, -1);
          return {
            type: "em",
            raw: V,
            text: K,
            tokens: this.lexer.inlineTokens(K)
          };
        }
        let C = V.slice(2, -2);
        return {
          type: "strong",
          raw: V,
          text: C,
          tokens: this.lexer.inlineTokens(C)
        };
      }
    }
  }
  codespan(A) {
    let B = this.rules.inline.code.exec(A);
    if (B) {
      let Q = B[2].replace(this.rules.other.newLineCharGlobal, " "),
        D = this.rules.other.nonSpaceChar.test(Q),
        Z = this.rules.other.startingSpaceChar.test(Q) && this.rules.other.endingSpaceChar.test(Q);
      if (D && Z) Q = Q.substring(1, Q.length - 1);
      return {
        type: "codespan",
        raw: B[0],
        text: Q
      };
    }
  }
  br(A) {
    let B = this.rules.inline.br.exec(A);
    if (B) return {
      type: "br",
      raw: B[0]
    };
  }
  del(A) {
    let B = this.rules.inline.del.exec(A);
    if (B) return {
      type: "del",
      raw: B[0],
      text: B[2],
      tokens: this.lexer.inlineTokens(B[2])
    };
  }
  autolink(A) {
    let B = this.rules.inline.autolink.exec(A);
    if (B) {
      let Q, D;
      if (B[2] === "@") Q = B[1], D = "mailto:" + Q;else Q = B[1], D = Q;
      return {
        type: "link",
        raw: B[0],
        text: Q,
        href: D,
        tokens: [{
          type: "text",
          raw: Q,
          text: Q
        }]
      };
    }
  }
  url(A) {
    let B;
    if (B = this.rules.inline.url.exec(A)) {
      let Q, D;
      if (B[2] === "@") Q = B[0], D = "mailto:" + Q;else {
        let Z;
        do Z = B[0], B[0] = this.rules.inline._backpedal.exec(B[0])?.[0] ?? ""; while (Z !== B[0]);
        if (Q = B[0], B[1] === "www.") D = "http://" + B[0];else D = B[0];
      }
      return {
        type: "link",
        raw: B[0],
        text: Q,
        href: D,
        tokens: [{
          type: "text",
          raw: Q,
          text: Q
        }]
      };
    }
  }
  inlineText(A) {
    let B = this.rules.inline.text.exec(A);
    if (B) {
      let Q = this.lexer.state.inRawBlock;
      return {
        type: "text",
        raw: B[0],
        text: B[0],
        escaped: Q
      };
    }
  }
}
class IC {
  tokens;
  options;
  state;
  tokenizer;
  inlineQueue;
  constructor(A) {
    this.tokens = [], this.tokens.links = Object.create(null), this.options = A || em, this.options.tokenizer = this.options.tokenizer || new $G1(), this.tokenizer = this.options.tokenizer, this.tokenizer.options = this.options, this.tokenizer.lexer = this, this.inlineQueue = [], this.state = {
      inLink: !1,
      inRawBlock: !1,
      top: !0
    };
    let B = {
      other: $X,
      block: Gb1.normal,
      inline: zG1.normal
    };
    if (this.options.pedantic) B.block = Gb1.pedantic, B.inline = zG1.pedantic;else if (this.options.gfm) if (B.block = Gb1.gfm, this.options.breaks) B.inline = zG1.breaks;else B.inline = zG1.gfm;
    this.tokenizer.rules = B;
  }
  static get rules() {
    return {
      block: Gb1,
      inline: zG1
    };
  }
  static lex(A, B) {
    return new IC(B).lex(A);
  }
  static lexInline(A, B) {
    return new IC(B).inlineTokens(A);
  }
  lex(A) {
    A = A.replace($X.carriageReturn, `
`), this.blockTokens(A, this.tokens);
    for (let B = 0; B < this.inlineQueue.length; B++) {
      let Q = this.inlineQueue[B];
      this.inlineTokens(Q.src, Q.tokens);
    }
    return this.inlineQueue = [], this.tokens;
  }
  blockTokens(A, B = [], Q = !1) {
    if (this.options.pedantic) A = A.replace($X.tabCharGlobal, "    ").replace($X.spaceLine, "");
    while (A) {
      let D;
      if (this.options.extensions?.block?.some(G => {
        if (D = G.call({
          lexer: this
        }, A, B)) return A = A.substring(D.raw.length), B.push(D), !0;
        return !1;
      })) continue;
      if (D = this.tokenizer.space(A)) {
        A = A.substring(D.raw.length);
        let G = B.at(-1);
        if (D.raw.length === 1 && G !== void 0) G.raw += `
`;else B.push(D);
        continue;
      }
      if (D = this.tokenizer.code(A)) {
        A = A.substring(D.raw.length);
        let G = B.at(-1);
        if (G?.type === "paragraph" || G?.type === "text") G.raw += `
` + D.raw, G.text += `
` + D.text, this.inlineQueue.at(-1).src = G.text;else B.push(D);
        continue;
      }
      if (D = this.tokenizer.fences(A)) {
        A = A.substring(D.raw.length), B.push(D);
        continue;
      }
      if (D = this.tokenizer.heading(A)) {
        A = A.substring(D.raw.length), B.push(D);
        continue;
      }
      if (D = this.tokenizer.hr(A)) {
        A = A.substring(D.raw.length), B.push(D);
        continue;
      }
      if (D = this.tokenizer.blockquote(A)) {
        A = A.substring(D.raw.length), B.push(D);
        continue;
      }
      if (D = this.tokenizer.list(A)) {
        A = A.substring(D.raw.length), B.push(D);
        continue;
      }
      if (D = this.tokenizer.html(A)) {
        A = A.substring(D.raw.length), B.push(D);
        continue;
      }
      if (D = this.tokenizer.def(A)) {
        A = A.substring(D.raw.length);
        let G = B.at(-1);
        if (G?.type === "paragraph" || G?.type === "text") G.raw += `
` + D.raw, G.text += `
` + D.raw, this.inlineQueue.at(-1).src = G.text;else if (!this.tokens.links[D.tag]) this.tokens.links[D.tag] = {
          href: D.href,
          title: D.title
        };
        continue;
      }
      if (D = this.tokenizer.table(A)) {
        A = A.substring(D.raw.length), B.push(D);
        continue;
      }
      if (D = this.tokenizer.lheading(A)) {
        A = A.substring(D.raw.length), B.push(D);
        continue;
      }
      let Z = A;
      if (this.options.extensions?.startBlock) {
        let G = 1 / 0,
          F = A.slice(1),
          I;
        if (this.options.extensions.startBlock.forEach(Y => {
          if (I = Y.call({
            lexer: this
          }, F), typeof I === "number" && I >= 0) G = Math.min(G, I);
        }), G < 1 / 0 && G >= 0) Z = A.substring(0, G + 1);
      }
      if (this.state.top && (D = this.tokenizer.paragraph(Z))) {
        let G = B.at(-1);
        if (Q && G?.type === "paragraph") G.raw += `
` + D.raw, G.text += `
` + D.text, this.inlineQueue.pop(), this.inlineQueue.at(-1).src = G.text;else B.push(D);
        Q = Z.length !== A.length, A = A.substring(D.raw.length);
        continue;
      }
      if (D = this.tokenizer.text(A)) {
        A = A.substring(D.raw.length);
        let G = B.at(-1);
        if (G?.type === "text") G.raw += `
` + D.raw, G.text += `
` + D.text, this.inlineQueue.pop(), this.inlineQueue.at(-1).src = G.text;else B.push(D);
        continue;
      }
      if (A) {
        let G = "Infinite loop on byte: " + A.charCodeAt(0);
        if (this.options.silent) {
          console.error(G);
          break;
        } else throw new Error(G);
      }
    }
    return this.state.top = !0, B;
  }
  inline(A, B = []) {
    return this.inlineQueue.push({
      src: A,
      tokens: B
    }), B;
  }
  inlineTokens(A, B = []) {
    let Q = A,
      D = null;
    if (this.tokens.links) {
      let F = Object.keys(this.tokens.links);
      if (F.length > 0) {
        while ((D = this.tokenizer.rules.inline.reflinkSearch.exec(Q)) != null) if (F.includes(D[0].slice(D[0].lastIndexOf("[") + 1, -1))) Q = Q.slice(0, D.index) + "[" + "a".repeat(D[0].length - 2) + "]" + Q.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex);
      }
    }
    while ((D = this.tokenizer.rules.inline.blockSkip.exec(Q)) != null) Q = Q.slice(0, D.index) + "[" + "a".repeat(D[0].length - 2) + "]" + Q.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);
    while ((D = this.tokenizer.rules.inline.anyPunctuation.exec(Q)) != null) Q = Q.slice(0, D.index) + "++" + Q.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);
    let Z = !1,
      G = "";
    while (A) {
      if (!Z) G = "";
      Z = !1;
      let F;
      if (this.options.extensions?.inline?.some(Y => {
        if (F = Y.call({
          lexer: this
        }, A, B)) return A = A.substring(F.raw.length), B.push(F), !0;
        return !1;
      })) continue;
      if (F = this.tokenizer.escape(A)) {
        A = A.substring(F.raw.length), B.push(F);
        continue;
      }
      if (F = this.tokenizer.tag(A)) {
        A = A.substring(F.raw.length), B.push(F);
        continue;
      }
      if (F = this.tokenizer.link(A)) {
        A = A.substring(F.raw.length), B.push(F);
        continue;
      }
      if (F = this.tokenizer.reflink(A, this.tokens.links)) {
        A = A.substring(F.raw.length);
        let Y = B.at(-1);
        if (F.type === "text" && Y?.type === "text") Y.raw += F.raw, Y.text += F.text;else B.push(F);
        continue;
      }
      if (F = this.tokenizer.emStrong(A, Q, G)) {
        A = A.substring(F.raw.length), B.push(F);
        continue;
      }
      if (F = this.tokenizer.codespan(A)) {
        A = A.substring(F.raw.length), B.push(F);
        continue;
      }
      if (F = this.tokenizer.br(A)) {
        A = A.substring(F.raw.length), B.push(F);
        continue;
      }
      if (F = this.tokenizer.del(A)) {
        A = A.substring(F.raw.length), B.push(F);
        continue;
      }
      if (F = this.tokenizer.autolink(A)) {
        A = A.substring(F.raw.length), B.push(F);
        continue;
      }
      if (!this.state.inLink && (F = this.tokenizer.url(A))) {
        A = A.substring(F.raw.length), B.push(F);
        continue;
      }
      let I = A;
      if (this.options.extensions?.startInline) {
        let Y = 1 / 0,
          W = A.slice(1),
          J;
        if (this.options.extensions.startInline.forEach(X => {
          if (J = X.call({
            lexer: this
          }, W), typeof J === "number" && J >= 0) Y = Math.min(Y, J);
        }), Y < 1 / 0 && Y >= 0) I = A.substring(0, Y + 1);
      }
      if (F = this.tokenizer.inlineText(I)) {
        if (A = A.substring(F.raw.length), F.raw.slice(-1) !== "_") G = F.raw.slice(-1);
        Z = !0;
        let Y = B.at(-1);
        if (Y?.type === "text") Y.raw += F.raw, Y.text += F.text;else B.push(F);
        continue;
      }
      if (A) {
        let Y = "Infinite loop on byte: " + A.charCodeAt(0);
        if (this.options.silent) {
          console.error(Y);
          break;
        } else throw new Error(Y);
      }
    }
    return B;
  }
}
class qG1 {
  options;
  parser;
  constructor(A) {
    this.options = A || em;
  }
  space(A) {
    return "";
  }
  code({
    text: A,
    lang: B,
    escaped: Q
  }) {
    let D = (B || "").match($X.notSpaceStart)?.[0],
      Z = A.replace($X.endingNewline, "") + `
`;
    if (!D) return "<pre><code>" + (Q ? Z : pM(Z, !0)) + `</code></pre>
`;
    return '<pre><code class="language-' + pM(D) + '">' + (Q ? Z : pM(Z, !0)) + `</code></pre>
`;
  }
  blockquote({
    tokens: A
  }) {
    return `<blockquote>
${this.parser.parse(A)}</blockquote>
`;
  }
  html({
    text: A
  }) {
    return A;
  }
  heading({
    tokens: A,
    depth: B
  }) {
    return `<h${B}>${this.parser.parseInline(A)}</h${B}>
`;
  }
  hr(A) {
    return `<hr>
`;
  }
  list(A) {
    let {
        ordered: B,
        start: Q
      } = A,
      D = "";
    for (let F = 0; F < A.items.length; F++) {
      let I = A.items[F];
      D += this.listitem(I);
    }
    let Z = B ? "ol" : "ul",
      G = B && Q !== 1 ? ' start="' + Q + '"' : "";
    return "<" + Z + G + `>
` + D + "</" + Z + `>
`;
  }
  listitem(A) {
    let B = "";
    if (A.task) {
      let Q = this.checkbox({
        checked: !!A.checked
      });
      if (A.loose) {
        if (A.tokens[0]?.type === "paragraph") {
          if (A.tokens[0].text = Q + " " + A.tokens[0].text, A.tokens[0].tokens && A.tokens[0].tokens.length > 0 && A.tokens[0].tokens[0].type === "text") A.tokens[0].tokens[0].text = Q + " " + pM(A.tokens[0].tokens[0].text), A.tokens[0].tokens[0].escaped = !0;
        } else A.tokens.unshift({
          type: "text",
          raw: Q + " ",
          text: Q + " ",
          escaped: !0
        });
      } else B += Q + " ";
    }
    return B += this.parser.parse(A.tokens, !!A.loose), `<li>${B}</li>
`;
  }
  checkbox({
    checked: A
  }) {
    return "<input " + (A ? 'checked="" ' : "") + 'disabled="" type="checkbox">';
  }
  paragraph({
    tokens: A
  }) {
    return `<p>${this.parser.parseInline(A)}</p>
`;
  }
  table(A) {
    let B = "",
      Q = "";
    for (let Z = 0; Z < A.header.length; Z++) Q += this.tablecell(A.header[Z]);
    B += this.tablerow({
      text: Q
    });
    let D = "";
    for (let Z = 0; Z < A.rows.length; Z++) {
      let G = A.rows[Z];
      Q = "";
      for (let F = 0; F < G.length; F++) Q += this.tablecell(G[F]);
      D += this.tablerow({
        text: Q
      });
    }
    if (D) D = `<tbody>${D}</tbody>`;
    return `<table>
<thead>
` + B + `</thead>
` + D + `</table>
`;
  }
  tablerow({
    text: A
  }) {
    return `<tr>
${A}</tr>
`;
  }
  tablecell(A) {
    let B = this.parser.parseInline(A.tokens),
      Q = A.header ? "th" : "td";
    return (A.align ? `<${Q} align="${A.align}">` : `<${Q}>`) + B + `</${Q}>
`;
  }
  strong({
    tokens: A
  }) {
    return `<strong>${this.parser.parseInline(A)}</strong>`;
  }
  em({
    tokens: A
  }) {
    return `<em>${this.parser.parseInline(A)}</em>`;
  }
  codespan({
    text: A
  }) {
    return `<code>${pM(A, !0)}</code>`;
  }
  br(A) {
    return "<br>";
  }
  del({
    tokens: A
  }) {
    return `<del>${this.parser.parseInline(A)}</del>`;
  }
  link({
    href: A,
    title: B,
    tokens: Q
  }) {
    let D = this.parser.parseInline(Q),
      Z = ePB(A);
    if (Z === null) return D;
    A = Z;
    let G = '<a href="' + A + '"';
    if (B) G += ' title="' + pM(B) + '"';
    return G += ">" + D + "</a>", G;
  }
  image({
    href: A,
    title: B,
    text: Q
  }) {
    let D = ePB(A);
    if (D === null) return pM(Q);
    A = D;
    let Z = `<img src="${A}" alt="${Q}"`;
    if (B) Z += ` title="${pM(B)}"`;
    return Z += ">", Z;
  }
  text(A) {
    return "tokens" in A && A.tokens ? this.parser.parseInline(A.tokens) : "escaped" in A && A.escaped ? A.text : pM(A.text);
  }
}
class Wb1 {
  strong({
    text: A
  }) {
    return A;
  }
  em({
    text: A
  }) {
    return A;
  }
  codespan({
    text: A
  }) {
    return A;
  }
  del({
    text: A
  }) {
    return A;
  }
  html({
    text: A
  }) {
    return A;
  }
  text({
    text: A
  }) {
    return A;
  }
  link({
    text: A
  }) {
    return "" + A;
  }
  image({
    text: A
  }) {
    return "" + A;
  }
  br() {
    return "";
  }
}
class eE {
  options;
  renderer;
  textRenderer;
  constructor(A) {
    this.options = A || em, this.options.renderer = this.options.renderer || new qG1(), this.renderer = this.options.renderer, this.renderer.options = this.options, this.renderer.parser = this, this.textRenderer = new Wb1();
  }
  static parse(A, B) {
    return new eE(B).parse(A);
  }
  static parseInline(A, B) {
    return new eE(B).parseInline(A);
  }
  parse(A, B = !0) {
    let Q = "";
    for (let D = 0; D < A.length; D++) {
      let Z = A[D];
      if (this.options.extensions?.renderers?.[Z.type]) {
        let F = Z,
          I = this.options.extensions.renderers[F.type].call({
            parser: this
          }, F);
        if (I !== !1 || !["space", "hr", "heading", "code", "table", "blockquote", "list", "html", "paragraph", "text"].includes(F.type)) {
          Q += I || "";
          continue;
        }
      }
      let G = Z;
      switch (G.type) {
        case "space":
          {
            Q += this.renderer.space(G);
            continue;
          }
        case "hr":
          {
            Q += this.renderer.hr(G);
            continue;
          }
        case "heading":
          {
            Q += this.renderer.heading(G);
            continue;
          }
        case "code":
          {
            Q += this.renderer.code(G);
            continue;
          }
        case "table":
          {
            Q += this.renderer.table(G);
            continue;
          }
        case "blockquote":
          {
            Q += this.renderer.blockquote(G);
            continue;
          }
        case "list":
          {
            Q += this.renderer.list(G);
            continue;
          }
        case "html":
          {
            Q += this.renderer.html(G);
            continue;
          }
        case "paragraph":
          {
            Q += this.renderer.paragraph(G);
            continue;
          }
        case "text":
          {
            let F = G,
              I = this.renderer.text(F);
            while (D + 1 < A.length && A[D + 1].type === "text") F = A[++D], I += `
` + this.renderer.text(F);
            if (B) Q += this.renderer.paragraph({
              type: "paragraph",
              raw: I,
              text: I,
              tokens: [{
                type: "text",
                raw: I,
                text: I,
                escaped: !0
              }]
            });else Q += I;
            continue;
          }
        default:
          {
            let F = 'Token with "' + G.type + '" type was not found.';
            if (this.options.silent) return console.error(F), "";else throw new Error(F);
          }
      }
    }
    return Q;
  }
  parseInline(A, B = this.renderer) {
    let Q = "";
    for (let D = 0; D < A.length; D++) {
      let Z = A[D];
      if (this.options.extensions?.renderers?.[Z.type]) {
        let F = this.options.extensions.renderers[Z.type].call({
          parser: this
        }, Z);
        if (F !== !1 || !["escape", "html", "link", "image", "strong", "em", "codespan", "br", "del", "text"].includes(Z.type)) {
          Q += F || "";
          continue;
        }
      }
      let G = Z;
      switch (G.type) {
        case "escape":
          {
            Q += B.text(G);
            break;
          }
        case "html":
          {
            Q += B.html(G);
            break;
          }
        case "link":
          {
            Q += B.link(G);
            break;
          }
        case "image":
          {
            Q += B.image(G);
            break;
          }
        case "strong":
          {
            Q += B.strong(G);
            break;
          }
        case "em":
          {
            Q += B.em(G);
            break;
          }
        case "codespan":
          {
            Q += B.codespan(G);
            break;
          }
        case "br":
          {
            Q += B.br(G);
            break;
          }
        case "del":
          {
            Q += B.del(G);
            break;
          }
        case "text":
          {
            Q += B.text(G);
            break;
          }
        default:
          {
            let F = 'Token with "' + G.type + '" type was not found.';
            if (this.options.silent) return console.error(F), "";else throw new Error(F);
          }
      }
    }
    return Q;
  }
}
class wG1 {
  options;
  block;
  constructor(A) {
    this.options = A || em;
  }
  static passThroughHooks = new Set(["preprocess", "postprocess", "processAllTokens"]);
  preprocess(A) {
    return A;
  }
  postprocess(A) {
    return A;
  }
  processAllTokens(A) {
    return A;
  }
  provideLexer() {
    return this.block ? IC.lex : IC.lexInline;
  }
  provideParser() {
    return this.block ? eE.parse : eE.parseInline;
  }
}
class CSB {
  defaults = JN0();
  options = this.setOptions;
  parse = this.parseMarkdown(!0);
  parseInline = this.parseMarkdown(!1);
  Parser = eE;
  Renderer = qG1;
  TextRenderer = Wb1;
  Lexer = IC;
  Tokenizer = $G1;
  Hooks = wG1;
  constructor(...A) {
    this.use(...A);
  }
  walkTokens(A, B) {
    let Q = [];
    for (let D of A) switch (Q = Q.concat(B.call(this, D)), D.type) {
      case "table":
        {
          let Z = D;
          for (let G of Z.header) Q = Q.concat(this.walkTokens(G.tokens, B));
          for (let G of Z.rows) for (let F of G) Q = Q.concat(this.walkTokens(F.tokens, B));
          break;
        }
      case "list":
        {
          let Z = D;
          Q = Q.concat(this.walkTokens(Z.items, B));
          break;
        }
      default:
        {
          let Z = D;
          if (this.defaults.extensions?.childTokens?.[Z.type]) this.defaults.extensions.childTokens[Z.type].forEach(G => {
            let F = Z[G].flat(1 / 0);
            Q = Q.concat(this.walkTokens(F, B));
          });else if (Z.tokens) Q = Q.concat(this.walkTokens(Z.tokens, B));
        }
    }
    return Q;
  }
  use(...A) {
    let B = this.defaults.extensions || {
      renderers: {},
      childTokens: {}
    };
    return A.forEach(Q => {
      let D = {
        ...Q
      };
      if (D.async = this.defaults.async || D.async || !1, Q.extensions) Q.extensions.forEach(Z => {
        if (!Z.name) throw new Error("extension name required");
        if ("renderer" in Z) {
          let G = B.renderers[Z.name];
          if (G) B.renderers[Z.name] = function (...F) {
            let I = Z.renderer.apply(this, F);
            if (I === !1) I = G.apply(this, F);
            return I;
          };else B.renderers[Z.name] = Z.renderer;
        }
        if ("tokenizer" in Z) {
          if (!Z.level || Z.level !== "block" && Z.level !== "inline") throw new Error("extension level must be 'block' or 'inline'");
          let G = B[Z.level];
          if (G) G.unshift(Z.tokenizer);else B[Z.level] = [Z.tokenizer];
          if (Z.start) {
            if (Z.level === "block") {
              if (B.startBlock) B.startBlock.push(Z.start);else B.startBlock = [Z.start];
            } else if (Z.level === "inline") if (B.startInline) B.startInline.push(Z.start);else B.startInline = [Z.start];
          }
        }
        if ("childTokens" in Z && Z.childTokens) B.childTokens[Z.name] = Z.childTokens;
      }), D.extensions = B;
      if (Q.renderer) {
        let Z = this.defaults.renderer || new qG1(this.defaults);
        for (let G in Q.renderer) {
          if (!(G in Z)) throw new Error(`renderer '${G}' does not exist`);
          if (["options", "parser"].includes(G)) continue;
          let F = G,
            I = Q.renderer[F],
            Y = Z[F];
          Z[F] = (...W) => {
            let J = I.apply(Z, W);
            if (J === !1) J = Y.apply(Z, W);
            return J || "";
          };
        }
        D.renderer = Z;
      }
      if (Q.tokenizer) {
        let Z = this.defaults.tokenizer || new $G1(this.defaults);
        for (let G in Q.tokenizer) {
          if (!(G in Z)) throw new Error(`tokenizer '${G}' does not exist`);
          if (["options", "rules", "lexer"].includes(G)) continue;
          let F = G,
            I = Q.tokenizer[F],
            Y = Z[F];
          Z[F] = (...W) => {
            let J = I.apply(Z, W);
            if (J === !1) J = Y.apply(Z, W);
            return J;
          };
        }
        D.tokenizer = Z;
      }
      if (Q.hooks) {
        let Z = this.defaults.hooks || new wG1();
        for (let G in Q.hooks) {
          if (!(G in Z)) throw new Error(`hook '${G}' does not exist`);
          if (["options", "block"].includes(G)) continue;
          let F = G,
            I = Q.hooks[F],
            Y = Z[F];
          if (wG1.passThroughHooks.has(G)) Z[F] = W => {
            if (this.defaults.async) return Promise.resolve(I.call(Z, W)).then(X => {
              return Y.call(Z, X);
            });
            let J = I.call(Z, W);
            return Y.call(Z, J);
          };else Z[F] = (...W) => {
            let J = I.apply(Z, W);
            if (J === !1) J = Y.apply(Z, W);
            return J;
          };
        }
        D.hooks = Z;
      }
      if (Q.walkTokens) {
        let Z = this.defaults.walkTokens,
          G = Q.walkTokens;
        D.walkTokens = function (F) {
          let I = [];
          if (I.push(G.call(this, F)), Z) I = I.concat(Z.call(this, F));
          return I;
        };
      }
      this.defaults = {
        ...this.defaults,
        ...D
      };
    }), this;
  }
  setOptions(A) {
    return this.defaults = {
      ...this.defaults,
      ...A
    }, this;
  }
  lexer(A, B) {
    return IC.lex(A, B ?? this.defaults);
  }
  parser(A, B) {
    return eE.parse(A, B ?? this.defaults);
  }
  parseMarkdown(A) {
    return (Q, D) => {
      let Z = {
          ...D
        },
        G = {
          ...this.defaults,
          ...Z
        },
        F = this.onError(!!G.silent, !!G.async);
      if (this.defaults.async === !0 && Z.async === !1) return F(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));
      if (typeof Q === "undefined" || Q === null) return F(new Error("marked(): input parameter is undefined or null"));
      if (typeof Q !== "string") return F(new Error("marked(): input parameter is of type " + Object.prototype.toString.call(Q) + ", string expected"));
      if (G.hooks) G.hooks.options = G, G.hooks.block = A;
      let I = G.hooks ? G.hooks.provideLexer() : A ? IC.lex : IC.lexInline,
        Y = G.hooks ? G.hooks.provideParser() : A ? eE.parse : eE.parseInline;
      if (G.async) return Promise.resolve(G.hooks ? G.hooks.preprocess(Q) : Q).then(W => I(W, G)).then(W => G.hooks ? G.hooks.processAllTokens(W) : W).then(W => G.walkTokens ? Promise.all(this.walkTokens(W, G.walkTokens)).then(() => W) : W).then(W => Y(W, G)).then(W => G.hooks ? G.hooks.postprocess(W) : W).catch(F);
      try {
        if (G.hooks) Q = G.hooks.preprocess(Q);
        let W = I(Q, G);
        if (G.hooks) W = G.hooks.processAllTokens(W);
        if (G.walkTokens) this.walkTokens(W, G.walkTokens);
        let J = Y(W, G);
        if (G.hooks) J = G.hooks.postprocess(J);
        return J;
      } catch (W) {
        return F(W);
      }
    };
  }
  onError(A, B) {
    return Q => {
      if (Q.message += `
Please report this to https://github.com/markedjs/marked.`, A) {
        let D = "<p>An error occurred:</p><pre>" + pM(Q.message + "", !0) + "</pre>";
        if (B) return Promise.resolve(D);
        return D;
      }
      if (B) return Promise.reject(Q);
      throw Q;
    };
  }
}
var tm = new CSB();
function x5(A, B) {
  return tm.parse(A, B);
}
x5.options = x5.setOptions = function (A) {
  return tm.setOptions(A), x5.defaults = tm.defaults, QSB(x5.defaults), x5;
};
x5.getDefaults = JN0;
x5.defaults = em;
x5.use = function (...A) {
  return tm.use(...A), x5.defaults = tm.defaults, QSB(x5.defaults), x5;
};
x5.walkTokens = function (A, B) {
  return tm.walkTokens(A, B);
};
x5.parseInline = tm.parseInline;
x5.Parser = eE;
x5.parser = eE.parse;
x5.Renderer = qG1;
x5.TextRenderer = Wb1;
x5.Lexer = IC;
x5.lexer = IC.lex;
x5.Tokenizer = $G1;
x5.Hooks = wG1;
x5.parse = x5;
module.exports = {
  IC,
  eE,
  x5
};