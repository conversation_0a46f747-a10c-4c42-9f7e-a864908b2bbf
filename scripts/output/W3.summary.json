{"metadata": {"entryPoint": "W3", "timestamp": "2025-08-10T11:24:48.861Z"}, "isolation": {"totalNodesInCluster": 89, "totalNodesInOriginal": 5506, "isolationRatio": "1.6%", "entryBoundarySize": 16, "entryBoundaryNodes": ["BD1", "Mx", "Nj1", "Q6", "W3", "YX", "Ym", "_F", "aC0", "bP", "j9", "o71", "t8", "vP", "xA", "zQ"]}, "externalDependencies": {}, "statistics": {"averageDependenciesPerNode": "3.24", "maxDependencies": 80, "nodeWithMaxDependencies": "ZA1", "top10MostDependedOn": [{"node": "ZA1", "dependencies": 80}, {"node": "ER8", "dependencies": 79}, {"node": "co5", "dependencies": 45}, {"node": "lo5", "dependencies": 45}, {"node": "po5", "dependencies": 45}, {"node": "io5", "dependencies": 45}, {"node": "no5", "dependencies": 45}, {"node": "ao5", "dependencies": 45}, {"node": "nf", "dependencies": 44}, {"node": "dj6", "dependencies": 44}]}, "dependencyLayers": [{"level": 0, "nodes": ["W3"], "count": 1}, {"level": 1, "nodes": ["A8B", "B8B", "F8B", "FK0", "G8B", "I8B", "IK0", "Im", "Lx", "Nj1", "Pj1", "Q6", "Tj1", "UD1", "W8B", "WJ", "Y8B", "_F", "a71", "bP", "c71", "d71", "fP", "hC0", "i71", "j9", "l71", "lC0", "lj1", "m71", "n71", "o6B", "p71", "qj1", "r71", "s71", "t6B", "t8", "v8B", "vP", "xA", "zQ"], "count": 42}, {"level": 2, "nodes": ["$8B", "AD1", "BD1", "H8B", "Lj1", "Mj1", "Rj1", "WJ", "Ym", "Z8B", "_S6", "a71", "bP", "c71", "dC0", "e6B", "gC0", "i71", "iS6", "l71", "m71", "mS6", "n71", "nC0", "nS6", "p71", "pC0", "r71", "rS6", "s71", "sC0", "yS6"], "count": 32}, {"level": 3, "nodes": ["$8B", "D8B", "H8B", "Lj1", "Q8B", "YX", "_S6", "aC0", "aS6", "e71", "gC0", "kS6", "mS6", "pC0", "uC0", "w8B", "xS6"], "count": 17}, {"level": 4, "nodes": ["D8B", "Mx", "Q8B", "e71", "hS6", "kS6", "o71", "t71", "xS6"], "count": 9}, {"level": 5, "nodes": ["J8B", "K8B", "Mx", "V8B", "cC0", "fK", "fS6", "gS6", "hK", "o71", "t71"], "count": 11}, {"level": 6, "nodes": ["C8B", "J8B", "V8B", "X8B", "cC0", "fK", "fS6", "hK", "uS6"], "count": 9}, {"level": 7, "nodes": ["X8B"], "count": 1}], "nodesList": ["$8B", "A8B", "AD1", "B8B", "BD1", "C8B", "D8B", "F8B", "FK0", "G8B", "H8B", "I8B", "IK0", "Im", "J8B", "K8B", "Lj1", "Lx", "Mj1", "Mx", "Nj1", "Pj1", "Q6", "Q8B", "Rj1", "Tj1", "UD1", "V8B", "W3", "W8B", "WJ", "X8B", "Y8B", "YX", "Ym", "Z8B", "_F", "_S6", "a71", "aC0", "aS6", "bP", "c71", "cC0", "d71", "dC0", "e6B", "e71", "fK", "fP", "fS6", "gC0", "gS6", "hC0", "hK", "hS6", "i71", "iS6", "j9", "kS6", "l71", "lC0", "lj1", "m71", "mS6", "n71", "nC0", "nS6", "o6B", "o71", "p71", "pC0", "qj1", "r71", "rS6", "s71", "sC0", "t6B", "t71", "t8", "uC0", "uS6", "v8B", "vP", "w8B", "xA", "xS6", "yS6", "zQ"]}