/**
 * Isolated bundle for 'K48'
 */

var Q48 = Object.defineProperty,
  p0 = (A, B) => Q48(A, "name", {
    value: B,
    configurable: !0
  }),
  LLB = 2,
  H9 = 4,
  H$0 = 4 * H9,
  AC = 5 * H9,
  mM = 2 * H9,
  bZ1 = 2 * H9 + 2 * mM,
  Qv = {
    row: 0,
    column: 0
  },
  Dv = Symbol("INTERNAL");
function H11(A) {
  if (A !== Dv) throw new Error("Illegal constructor");
}
function vZ1(A) {
  return !!A && typeof A.row === "number" && typeof A.column === "number";
}
function RLB(A) {
  a1 = A;
}
var a1,
  D48 = class {
    static {
      p0(this, "LookaheadIterator");
    }
    [0] = 0;
    language;
    constructor(A, B, Q) {
      H11(A), this[0] = B, this.language = Q;
    }
    get currentTypeId() {
      return a1._ts_lookahead_iterator_current_symbol(this[0]);
    }
    get currentType() {
      return this.language.types[this.currentTypeId] || "ERROR";
    }
    delete() {
      a1._ts_lookahead_iterator_delete(this[0]), this[0] = 0;
    }
    reset(A, B) {
      if (a1._ts_lookahead_iterator_reset(this[0], A[0], B)) return this.language = A, !0;
      return !1;
    }
    resetState(A) {
      return Boolean(a1._ts_lookahead_iterator_reset_state(this[0], A));
    }
    [Symbol.iterator]() {
      return {
        next: p0(() => {
          if (a1._ts_lookahead_iterator_next(this[0])) return {
            done: !1,
            value: this.currentType
          };
          return {
            done: !0,
            value: ""
          };
        }, "next")
      };
    }
  };
function w$0(A, B, Q, D) {
  let Z = Q - B,
    G = A.textCallback(B, D);
  if (G) {
    B += G.length;
    while (B < Q) {
      let F = A.textCallback(B, D);
      if (F && F.length > 0) B += F.length, G += F;else break;
    }
    if (B > Q) G = G.slice(0, Z);
  }
  return G ?? "";
}
var Z48 = class A {
    static {
      p0(this, "Tree");
    }
    [0] = 0;
    textCallback;
    language;
    constructor(B, Q, D, Z) {
      H11(B), this[0] = Q, this.language = D, this.textCallback = Z;
    }
    copy() {
      let B = a1._ts_tree_copy(this[0]);
      return new A(Dv, B, this.language, this.textCallback);
    }
    delete() {
      a1._ts_tree_delete(this[0]), this[0] = 0;
    }
    get rootNode() {
      return a1._ts_tree_root_node_wasm(this[0]), TD(this);
    }
    rootNodeWithOffset(B, Q) {
      let D = xB + AC;
      return a1.setValue(D, B, "i32"), lE(D + H9, Q), a1._ts_tree_root_node_with_offset_wasm(this[0]), TD(this);
    }
    edit(B) {
      TLB(B), a1._ts_tree_edit_wasm(this[0]);
    }
    walk() {
      return this.rootNode.walk();
    }
    getChangedRanges(B) {
      if (!(B instanceof A)) throw new TypeError("Argument must be a Tree");
      a1._ts_tree_get_changed_ranges_wasm(this[0], B[0]);
      let Q = a1.getValue(xB, "i32"),
        D = a1.getValue(xB + H9, "i32"),
        Z = new Array(Q);
      if (Q > 0) {
        let G = D;
        for (let F = 0; F < Q; F++) Z[F] = ox1(G), G += bZ1;
        a1._free(D);
      }
      return Z;
    }
    getIncludedRanges() {
      a1._ts_tree_included_ranges_wasm(this[0]);
      let B = a1.getValue(xB, "i32"),
        Q = a1.getValue(xB + H9, "i32"),
        D = new Array(B);
      if (B > 0) {
        let Z = Q;
        for (let G = 0; G < B; G++) D[G] = ox1(Z), Z += bZ1;
        a1._free(Q);
      }
      return D;
    }
  },
  G48 = class A {
    static {
      p0(this, "TreeCursor");
    }
    [0] = 0;
    [1] = 0;
    [2] = 0;
    [3] = 0;
    tree;
    constructor(B, Q) {
      H11(B), this.tree = Q, cK(this);
    }
    copy() {
      let B = new A(Dv, this.tree);
      return a1._ts_tree_cursor_copy_wasm(this.tree[0]), cK(B), B;
    }
    delete() {
      h3(this), a1._ts_tree_cursor_delete_wasm(this.tree[0]), this[0] = this[1] = this[2] = 0;
    }
    get currentNode() {
      return h3(this), a1._ts_tree_cursor_current_node_wasm(this.tree[0]), TD(this.tree);
    }
    get currentFieldId() {
      return h3(this), a1._ts_tree_cursor_current_field_id_wasm(this.tree[0]);
    }
    get currentFieldName() {
      return this.tree.language.fields[this.currentFieldId];
    }
    get currentDepth() {
      return h3(this), a1._ts_tree_cursor_current_depth_wasm(this.tree[0]);
    }
    get currentDescendantIndex() {
      return h3(this), a1._ts_tree_cursor_current_descendant_index_wasm(this.tree[0]);
    }
    get nodeType() {
      return this.tree.language.types[this.nodeTypeId] || "ERROR";
    }
    get nodeTypeId() {
      return h3(this), a1._ts_tree_cursor_current_node_type_id_wasm(this.tree[0]);
    }
    get nodeStateId() {
      return h3(this), a1._ts_tree_cursor_current_node_state_id_wasm(this.tree[0]);
    }
    get nodeId() {
      return h3(this), a1._ts_tree_cursor_current_node_id_wasm(this.tree[0]);
    }
    get nodeIsNamed() {
      return h3(this), a1._ts_tree_cursor_current_node_is_named_wasm(this.tree[0]) === 1;
    }
    get nodeIsMissing() {
      return h3(this), a1._ts_tree_cursor_current_node_is_missing_wasm(this.tree[0]) === 1;
    }
    get nodeText() {
      h3(this);
      let B = a1._ts_tree_cursor_start_index_wasm(this.tree[0]),
        Q = a1._ts_tree_cursor_end_index_wasm(this.tree[0]);
      a1._ts_tree_cursor_start_position_wasm(this.tree[0]);
      let D = Sm(xB);
      return w$0(this.tree, B, Q, D);
    }
    get startPosition() {
      return h3(this), a1._ts_tree_cursor_start_position_wasm(this.tree[0]), Sm(xB);
    }
    get endPosition() {
      return h3(this), a1._ts_tree_cursor_end_position_wasm(this.tree[0]), Sm(xB);
    }
    get startIndex() {
      return h3(this), a1._ts_tree_cursor_start_index_wasm(this.tree[0]);
    }
    get endIndex() {
      return h3(this), a1._ts_tree_cursor_end_index_wasm(this.tree[0]);
    }
    gotoFirstChild() {
      h3(this);
      let B = a1._ts_tree_cursor_goto_first_child_wasm(this.tree[0]);
      return cK(this), B === 1;
    }
    gotoLastChild() {
      h3(this);
      let B = a1._ts_tree_cursor_goto_last_child_wasm(this.tree[0]);
      return cK(this), B === 1;
    }
    gotoParent() {
      h3(this);
      let B = a1._ts_tree_cursor_goto_parent_wasm(this.tree[0]);
      return cK(this), B === 1;
    }
    gotoNextSibling() {
      h3(this);
      let B = a1._ts_tree_cursor_goto_next_sibling_wasm(this.tree[0]);
      return cK(this), B === 1;
    }
    gotoPreviousSibling() {
      h3(this);
      let B = a1._ts_tree_cursor_goto_previous_sibling_wasm(this.tree[0]);
      return cK(this), B === 1;
    }
    gotoDescendant(B) {
      h3(this), a1._ts_tree_cursor_goto_descendant_wasm(this.tree[0], B), cK(this);
    }
    gotoFirstChildForIndex(B) {
      h3(this), a1.setValue(xB + H$0, B, "i32");
      let Q = a1._ts_tree_cursor_goto_first_child_for_index_wasm(this.tree[0]);
      return cK(this), Q === 1;
    }
    gotoFirstChildForPosition(B) {
      h3(this), lE(xB + H$0, B);
      let Q = a1._ts_tree_cursor_goto_first_child_for_position_wasm(this.tree[0]);
      return cK(this), Q === 1;
    }
    reset(B) {
      A4(B), h3(this, xB + AC), a1._ts_tree_cursor_reset_wasm(this.tree[0]), cK(this);
    }
    resetTo(B) {
      h3(this, xB), h3(B, xB + H$0), a1._ts_tree_cursor_reset_to_wasm(this.tree[0], B.tree[0]), cK(this);
    }
  },
  F48 = class {
    static {
      p0(this, "Node");
    }
    [0] = 0;
    _children;
    _namedChildren;
    constructor(A, {
      id: B,
      tree: Q,
      startIndex: D,
      startPosition: Z,
      other: G
    }) {
      H11(A), this[0] = G, this.id = B, this.tree = Q, this.startIndex = D, this.startPosition = Z;
    }
    id;
    startIndex;
    startPosition;
    tree;
    get typeId() {
      return A4(this), a1._ts_node_symbol_wasm(this.tree[0]);
    }
    get grammarId() {
      return A4(this), a1._ts_node_grammar_symbol_wasm(this.tree[0]);
    }
    get type() {
      return this.tree.language.types[this.typeId] || "ERROR";
    }
    get grammarType() {
      return this.tree.language.types[this.grammarId] || "ERROR";
    }
    get isNamed() {
      return A4(this), a1._ts_node_is_named_wasm(this.tree[0]) === 1;
    }
    get isExtra() {
      return A4(this), a1._ts_node_is_extra_wasm(this.tree[0]) === 1;
    }
    get isError() {
      return A4(this), a1._ts_node_is_error_wasm(this.tree[0]) === 1;
    }
    get isMissing() {
      return A4(this), a1._ts_node_is_missing_wasm(this.tree[0]) === 1;
    }
    get hasChanges() {
      return A4(this), a1._ts_node_has_changes_wasm(this.tree[0]) === 1;
    }
    get hasError() {
      return A4(this), a1._ts_node_has_error_wasm(this.tree[0]) === 1;
    }
    get endIndex() {
      return A4(this), a1._ts_node_end_index_wasm(this.tree[0]);
    }
    get endPosition() {
      return A4(this), a1._ts_node_end_point_wasm(this.tree[0]), Sm(xB);
    }
    get text() {
      return w$0(this.tree, this.startIndex, this.endIndex, this.startPosition);
    }
    get parseState() {
      return A4(this), a1._ts_node_parse_state_wasm(this.tree[0]);
    }
    get nextParseState() {
      return A4(this), a1._ts_node_next_parse_state_wasm(this.tree[0]);
    }
    equals(A) {
      return this.tree === A.tree && this.id === A.id;
    }
    child(A) {
      return A4(this), a1._ts_node_child_wasm(this.tree[0], A), TD(this.tree);
    }
    namedChild(A) {
      return A4(this), a1._ts_node_named_child_wasm(this.tree[0], A), TD(this.tree);
    }
    childForFieldId(A) {
      return A4(this), a1._ts_node_child_by_field_id_wasm(this.tree[0], A), TD(this.tree);
    }
    childForFieldName(A) {
      let B = this.tree.language.fields.indexOf(A);
      if (B !== -1) return this.childForFieldId(B);
      return null;
    }
    fieldNameForChild(A) {
      A4(this);
      let B = a1._ts_node_field_name_for_child_wasm(this.tree[0], A);
      if (!B) return null;
      return a1.AsciiToString(B);
    }
    fieldNameForNamedChild(A) {
      A4(this);
      let B = a1._ts_node_field_name_for_named_child_wasm(this.tree[0], A);
      if (!B) return null;
      return a1.AsciiToString(B);
    }
    childrenForFieldName(A) {
      let B = this.tree.language.fields.indexOf(A);
      if (B !== -1 && B !== 0) return this.childrenForFieldId(B);
      return [];
    }
    childrenForFieldId(A) {
      A4(this), a1._ts_node_children_by_field_id_wasm(this.tree[0], A);
      let B = a1.getValue(xB, "i32"),
        Q = a1.getValue(xB + H9, "i32"),
        D = new Array(B);
      if (B > 0) {
        let Z = Q;
        for (let G = 0; G < B; G++) D[G] = TD(this.tree, Z), Z += AC;
        a1._free(Q);
      }
      return D;
    }
    firstChildForIndex(A) {
      A4(this);
      let B = xB + AC;
      return a1.setValue(B, A, "i32"), a1._ts_node_first_child_for_byte_wasm(this.tree[0]), TD(this.tree);
    }
    firstNamedChildForIndex(A) {
      A4(this);
      let B = xB + AC;
      return a1.setValue(B, A, "i32"), a1._ts_node_first_named_child_for_byte_wasm(this.tree[0]), TD(this.tree);
    }
    get childCount() {
      return A4(this), a1._ts_node_child_count_wasm(this.tree[0]);
    }
    get namedChildCount() {
      return A4(this), a1._ts_node_named_child_count_wasm(this.tree[0]);
    }
    get firstChild() {
      return this.child(0);
    }
    get firstNamedChild() {
      return this.namedChild(0);
    }
    get lastChild() {
      return this.child(this.childCount - 1);
    }
    get lastNamedChild() {
      return this.namedChild(this.namedChildCount - 1);
    }
    get children() {
      if (!this._children) {
        A4(this), a1._ts_node_children_wasm(this.tree[0]);
        let A = a1.getValue(xB, "i32"),
          B = a1.getValue(xB + H9, "i32");
        if (this._children = new Array(A), A > 0) {
          let Q = B;
          for (let D = 0; D < A; D++) this._children[D] = TD(this.tree, Q), Q += AC;
          a1._free(B);
        }
      }
      return this._children;
    }
    get namedChildren() {
      if (!this._namedChildren) {
        A4(this), a1._ts_node_named_children_wasm(this.tree[0]);
        let A = a1.getValue(xB, "i32"),
          B = a1.getValue(xB + H9, "i32");
        if (this._namedChildren = new Array(A), A > 0) {
          let Q = B;
          for (let D = 0; D < A; D++) this._namedChildren[D] = TD(this.tree, Q), Q += AC;
          a1._free(B);
        }
      }
      return this._namedChildren;
    }
    descendantsOfType(A, B = Qv, Q = Qv) {
      if (!Array.isArray(A)) A = [A];
      let D = [],
        Z = this.tree.language.types;
      for (let W of A) if (W == "ERROR") D.push(65535);
      for (let W = 0, J = Z.length; W < J; W++) if (A.includes(Z[W])) D.push(W);
      let G = a1._malloc(H9 * D.length);
      for (let W = 0, J = D.length; W < J; W++) a1.setValue(G + W * H9, D[W], "i32");
      A4(this), a1._ts_node_descendants_of_type_wasm(this.tree[0], G, D.length, B.row, B.column, Q.row, Q.column);
      let F = a1.getValue(xB, "i32"),
        I = a1.getValue(xB + H9, "i32"),
        Y = new Array(F);
      if (F > 0) {
        let W = I;
        for (let J = 0; J < F; J++) Y[J] = TD(this.tree, W), W += AC;
      }
      return a1._free(I), a1._free(G), Y;
    }
    get nextSibling() {
      return A4(this), a1._ts_node_next_sibling_wasm(this.tree[0]), TD(this.tree);
    }
    get previousSibling() {
      return A4(this), a1._ts_node_prev_sibling_wasm(this.tree[0]), TD(this.tree);
    }
    get nextNamedSibling() {
      return A4(this), a1._ts_node_next_named_sibling_wasm(this.tree[0]), TD(this.tree);
    }
    get previousNamedSibling() {
      return A4(this), a1._ts_node_prev_named_sibling_wasm(this.tree[0]), TD(this.tree);
    }
    get descendantCount() {
      return A4(this), a1._ts_node_descendant_count_wasm(this.tree[0]);
    }
    get parent() {
      return A4(this), a1._ts_node_parent_wasm(this.tree[0]), TD(this.tree);
    }
    childWithDescendant(A) {
      return A4(this), A4(A, 1), a1._ts_node_child_with_descendant_wasm(this.tree[0]), TD(this.tree);
    }
    descendantForIndex(A, B = A) {
      if (typeof A !== "number" || typeof B !== "number") throw new Error("Arguments must be numbers");
      A4(this);
      let Q = xB + AC;
      return a1.setValue(Q, A, "i32"), a1.setValue(Q + H9, B, "i32"), a1._ts_node_descendant_for_index_wasm(this.tree[0]), TD(this.tree);
    }
    namedDescendantForIndex(A, B = A) {
      if (typeof A !== "number" || typeof B !== "number") throw new Error("Arguments must be numbers");
      A4(this);
      let Q = xB + AC;
      return a1.setValue(Q, A, "i32"), a1.setValue(Q + H9, B, "i32"), a1._ts_node_named_descendant_for_index_wasm(this.tree[0]), TD(this.tree);
    }
    descendantForPosition(A, B = A) {
      if (!vZ1(A) || !vZ1(B)) throw new Error("Arguments must be {row, column} objects");
      A4(this);
      let Q = xB + AC;
      return lE(Q, A), lE(Q + mM, B), a1._ts_node_descendant_for_position_wasm(this.tree[0]), TD(this.tree);
    }
    namedDescendantForPosition(A, B = A) {
      if (!vZ1(A) || !vZ1(B)) throw new Error("Arguments must be {row, column} objects");
      A4(this);
      let Q = xB + AC;
      return lE(Q, A), lE(Q + mM, B), a1._ts_node_named_descendant_for_position_wasm(this.tree[0]), TD(this.tree);
    }
    walk() {
      return A4(this), a1._ts_tree_cursor_new_wasm(this.tree[0]), new G48(Dv, this.tree);
    }
    edit(A) {
      if (this.startIndex >= A.oldEndIndex) {
        this.startIndex = A.newEndIndex + (this.startIndex - A.oldEndIndex);
        let B, Q;
        if (this.startPosition.row > A.oldEndPosition.row) B = this.startPosition.row - A.oldEndPosition.row, Q = this.startPosition.column;else if (B = 0, Q = this.startPosition.column, this.startPosition.column >= A.oldEndPosition.column) Q = this.startPosition.column - A.oldEndPosition.column;
        if (B > 0) this.startPosition.row += B, this.startPosition.column = Q;else this.startPosition.column += Q;
      } else if (this.startIndex > A.startIndex) this.startIndex = A.newEndIndex, this.startPosition.row = A.newEndPosition.row, this.startPosition.column = A.newEndPosition.column;
    }
    toString() {
      A4(this);
      let A = a1._ts_node_to_string_wasm(this.tree[0]),
        B = a1.AsciiToString(A);
      return a1._free(A), B;
    }
  };
function U$0(A, B, Q, D, Z) {
  for (let G = 0, F = Z.length; G < F; G++) {
    let I = a1.getValue(Q, "i32");
    Q += H9;
    let Y = TD(B, Q);
    Q += AC, Z[G] = {
      patternIndex: D,
      name: A.captureNames[I],
      node: Y
    };
  }
  return Q;
}
function A4(A, B = 0) {
  let Q = xB + B * AC;
  a1.setValue(Q, A.id, "i32"), Q += H9, a1.setValue(Q, A.startIndex, "i32"), Q += H9, a1.setValue(Q, A.startPosition.row, "i32"), Q += H9, a1.setValue(Q, A.startPosition.column, "i32"), Q += H9, a1.setValue(Q, A[0], "i32");
}
function TD(A, B = xB) {
  let Q = a1.getValue(B, "i32");
  if (B += H9, Q === 0) return null;
  let D = a1.getValue(B, "i32");
  B += H9;
  let Z = a1.getValue(B, "i32");
  B += H9;
  let G = a1.getValue(B, "i32");
  B += H9;
  let F = a1.getValue(B, "i32");
  return new F48(Dv, {
    id: Q,
    tree: A,
    startIndex: D,
    startPosition: {
      row: Z,
      column: G
    },
    other: F
  });
}
function h3(A, B = xB) {
  a1.setValue(B + 0 * H9, A[0], "i32"), a1.setValue(B + 1 * H9, A[1], "i32"), a1.setValue(B + 2 * H9, A[2], "i32"), a1.setValue(B + 3 * H9, A[3], "i32");
}
function cK(A) {
  A[0] = a1.getValue(xB + 0 * H9, "i32"), A[1] = a1.getValue(xB + 1 * H9, "i32"), A[2] = a1.getValue(xB + 2 * H9, "i32"), A[3] = a1.getValue(xB + 3 * H9, "i32");
}
function lE(A, B) {
  a1.setValue(A, B.row, "i32"), a1.setValue(A + H9, B.column, "i32");
}
function Sm(A) {
  return {
    row: a1.getValue(A, "i32") >>> 0,
    column: a1.getValue(A + H9, "i32") >>> 0
  };
}
function OLB(A, B) {
  lE(A, B.startPosition), A += mM, lE(A, B.endPosition), A += mM, a1.setValue(A, B.startIndex, "i32"), A += H9, a1.setValue(A, B.endIndex, "i32"), A += H9;
}
function ox1(A) {
  let B = {};
  return B.startPosition = Sm(A), A += mM, B.endPosition = Sm(A), A += mM, B.startIndex = a1.getValue(A, "i32") >>> 0, A += H9, B.endIndex = a1.getValue(A, "i32") >>> 0, B;
}
function TLB(A, B = xB) {
  lE(B, A.startPosition), B += mM, lE(B, A.oldEndPosition), B += mM, lE(B, A.newEndPosition), B += mM, a1.setValue(B, A.startIndex, "i32"), B += H9, a1.setValue(B, A.oldEndIndex, "i32"), B += H9, a1.setValue(B, A.newEndIndex, "i32"), B += H9;
}
function PLB(A) {
  let B = {};
  return B.major_version = a1.getValue(A, "i32"), A += H9, B.minor_version = a1.getValue(A, "i32"), A += H9, B.field_count = a1.getValue(A, "i32"), B;
}
var I48 = 1,
  Y48 = 2,
  W48 = /[\w-]+/g,
  H33 = {
    Zero: 0,
    ZeroOrOne: 1,
    ZeroOrMore: 2,
    One: 3,
    OneOrMore: 4
  },
  MLB = p0(A => A.type === "capture", "isCaptureStep"),
  $$0 = p0(A => A.type === "string", "isStringStep"),
  k$ = {
    Syntax: 1,
    NodeName: 2,
    FieldName: 3,
    CaptureName: 4,
    PatternStructure: 5
  },
  xZ1 = class A extends Error {
    constructor(B, Q, D, Z) {
      super(A.formatMessage(B, Q));
      this.kind = B, this.info = Q, this.index = D, this.length = Z, this.name = "QueryError";
    }
    static {
      p0(this, "QueryError");
    }
    static formatMessage(B, Q) {
      switch (B) {
        case k$.NodeName:
          return `Bad node name '${Q.word}'`;
        case k$.FieldName:
          return `Bad field name '${Q.word}'`;
        case k$.CaptureName:
          return `Bad capture name @${Q.word}`;
        case k$.PatternStructure:
          return `Bad pattern structure at offset ${Q.suffix}`;
        case k$.Syntax:
          return `Bad syntax at offset ${Q.suffix}`;
      }
    }
  };
function SLB(A, B, Q, D) {
  if (A.length !== 3) throw new Error(`Wrong number of arguments to \`#${Q}\` predicate. Expected 2, got ${A.length - 1}`);
  if (!MLB(A[1])) throw new Error(`First argument of \`#${Q}\` predicate must be a capture. Got "${A[1].value}"`);
  let Z = Q === "eq?" || Q === "any-eq?",
    G = !Q.startsWith("any-");
  if (MLB(A[2])) {
    let F = A[1].name,
      I = A[2].name;
    D[B].push(Y => {
      let W = [],
        J = [];
      for (let V of Y) {
        if (V.name === F) W.push(V.node);
        if (V.name === I) J.push(V.node);
      }
      let X = p0((V, C, K) => {
        return K ? V.text === C.text : V.text !== C.text;
      }, "compare");
      return G ? W.every(V => J.some(C => X(V, C, Z))) : W.some(V => J.some(C => X(V, C, Z)));
    });
  } else {
    let F = A[1].name,
      I = A[2].value,
      Y = p0(J => J.text === I, "matches"),
      W = p0(J => J.text !== I, "doesNotMatch");
    D[B].push(J => {
      let X = [];
      for (let C of J) if (C.name === F) X.push(C.node);
      let V = Z ? Y : W;
      return G ? X.every(V) : X.some(V);
    });
  }
}
function jLB(A, B, Q, D) {
  if (A.length !== 3) throw new Error(`Wrong number of arguments to \`#${Q}\` predicate. Expected 2, got ${A.length - 1}.`);
  if (A[1].type !== "capture") throw new Error(`First argument of \`#${Q}\` predicate must be a capture. Got "${A[1].value}".`);
  if (A[2].type !== "string") throw new Error(`Second argument of \`#${Q}\` predicate must be a string. Got @${A[2].name}.`);
  let Z = Q === "match?" || Q === "any-match?",
    G = !Q.startsWith("any-"),
    F = A[1].name,
    I = new RegExp(A[2].value);
  D[B].push(Y => {
    let W = [];
    for (let X of Y) if (X.name === F) W.push(X.node.text);
    let J = p0((X, V) => {
      return V ? I.test(X) : !I.test(X);
    }, "test");
    if (W.length === 0) return !Z;
    return G ? W.every(X => J(X, Z)) : W.some(X => J(X, Z));
  });
}
function yLB(A, B, Q, D) {
  if (A.length < 2) throw new Error(`Wrong number of arguments to \`#${Q}\` predicate. Expected at least 1. Got ${A.length - 1}.`);
  if (A[1].type !== "capture") throw new Error(`First argument of \`#${Q}\` predicate must be a capture. Got "${A[1].value}".`);
  let Z = Q === "any-of?",
    G = A[1].name,
    F = A.slice(2);
  if (!F.every($$0)) throw new Error(`Arguments to \`#${Q}\` predicate must be strings.".`);
  let I = F.map(Y => Y.value);
  D[B].push(Y => {
    let W = [];
    for (let J of Y) if (J.name === G) W.push(J.node.text);
    if (W.length === 0) return !Z;
    return W.every(J => I.includes(J)) === Z;
  });
}
function kLB(A, B, Q, D, Z) {
  if (A.length < 2 || A.length > 3) throw new Error(`Wrong number of arguments to \`#${Q}\` predicate. Expected 1 or 2. Got ${A.length - 1}.`);
  if (!A.every($$0)) throw new Error(`Arguments to \`#${Q}\` predicate must be strings.".`);
  let G = Q === "is?" ? D : Z;
  if (!G[B]) G[B] = {};
  G[B][A[1].value] = A[2]?.value ?? null;
}
function _LB(A, B, Q) {
  if (A.length < 2 || A.length > 3) throw new Error(`Wrong number of arguments to \`#set!\` predicate. Expected 1 or 2. Got ${A.length - 1}.`);
  if (!A.every($$0)) throw new Error('Arguments to `#set!` predicate must be strings.".');
  if (!Q[B]) Q[B] = {};
  Q[B][A[1].value] = A[2]?.value ?? null;
}
function xLB(A, B, Q, D, Z, G, F, I, Y, W, J) {
  if (B === I48) {
    let X = D[Q];
    G.push({
      type: "capture",
      name: X
    });
  } else if (B === Y48) G.push({
    type: "string",
    value: Z[Q]
  });else if (G.length > 0) {
    if (G[0].type !== "string") throw new Error("Predicates must begin with a literal value");
    let X = G[0].value;
    switch (X) {
      case "any-not-eq?":
      case "not-eq?":
      case "any-eq?":
      case "eq?":
        SLB(G, A, X, F);
        break;
      case "any-not-match?":
      case "not-match?":
      case "any-match?":
      case "match?":
        jLB(G, A, X, F);
        break;
      case "not-any-of?":
      case "any-of?":
        yLB(G, A, X, F);
        break;
      case "is?":
      case "is-not?":
        kLB(G, A, X, W, J);
        break;
      case "set!":
        _LB(G, A, Y);
        break;
      default:
        I[A].push({
          operator: X,
          operands: G.slice(1)
        });
    }
    G.length = 0;
  }
}
var J48 = class {
    static {
      p0(this, "Query");
    }
    [0] = 0;
    exceededMatchLimit;
    textPredicates;
    captureNames;
    captureQuantifiers;
    predicates;
    setProperties;
    assertedProperties;
    refutedProperties;
    matchLimit;
    constructor(A, B) {
      let Q = a1.lengthBytesUTF8(B),
        D = a1._malloc(Q + 1);
      a1.stringToUTF8(B, D, Q + 1);
      let Z = a1._ts_query_new(A[0], D, Q, xB, xB + H9);
      if (!Z) {
        let z = a1.getValue(xB + H9, "i32"),
          $ = a1.getValue(xB, "i32"),
          L = a1.UTF8ToString(D, $).length,
          N = B.slice(L, L + 100).split(`
`)[0],
          O = N.match(W48)?.[0] ?? "";
        switch (a1._free(D), z) {
          case k$.Syntax:
            throw new xZ1(k$.Syntax, {
              suffix: `${L}: '${N}'...`
            }, L, 0);
          case k$.NodeName:
            throw new xZ1(z, {
              word: O
            }, L, O.length);
          case k$.FieldName:
            throw new xZ1(z, {
              word: O
            }, L, O.length);
          case k$.CaptureName:
            throw new xZ1(z, {
              word: O
            }, L, O.length);
          case k$.PatternStructure:
            throw new xZ1(z, {
              suffix: `${L}: '${N}'...`
            }, L, 0);
        }
      }
      let G = a1._ts_query_string_count(Z),
        F = a1._ts_query_capture_count(Z),
        I = a1._ts_query_pattern_count(Z),
        Y = new Array(F),
        W = new Array(I),
        J = new Array(G);
      for (let z = 0; z < F; z++) {
        let $ = a1._ts_query_capture_name_for_id(Z, z, xB),
          L = a1.getValue(xB, "i32");
        Y[z] = a1.UTF8ToString($, L);
      }
      for (let z = 0; z < I; z++) {
        let $ = new Array(F);
        for (let L = 0; L < F; L++) {
          let N = a1._ts_query_capture_quantifier_for_id(Z, z, L);
          $[L] = N;
        }
        W[z] = $;
      }
      for (let z = 0; z < G; z++) {
        let $ = a1._ts_query_string_value_for_id(Z, z, xB),
          L = a1.getValue(xB, "i32");
        J[z] = a1.UTF8ToString($, L);
      }
      let X = new Array(I),
        V = new Array(I),
        C = new Array(I),
        K = new Array(I),
        H = new Array(I);
      for (let z = 0; z < I; z++) {
        let $ = a1._ts_query_predicates_for_pattern(Z, z, xB),
          L = a1.getValue(xB, "i32");
        K[z] = [], H[z] = [];
        let N = new Array(),
          O = $;
        for (let R = 0; R < L; R++) {
          let T = a1.getValue(O, "i32");
          O += H9;
          let j = a1.getValue(O, "i32");
          O += H9, xLB(z, T, j, Y, J, N, H, K, X, V, C);
        }
        Object.freeze(H[z]), Object.freeze(K[z]), Object.freeze(X[z]), Object.freeze(V[z]), Object.freeze(C[z]);
      }
      a1._free(D), this[0] = Z, this.captureNames = Y, this.captureQuantifiers = W, this.textPredicates = H, this.predicates = K, this.setProperties = X, this.assertedProperties = V, this.refutedProperties = C, this.exceededMatchLimit = !1;
    }
    delete() {
      a1._ts_query_delete(this[0]), this[0] = 0;
    }
    matches(A, B = {}) {
      let Q = B.startPosition ?? Qv,
        D = B.endPosition ?? Qv,
        Z = B.startIndex ?? 0,
        G = B.endIndex ?? 0,
        F = B.matchLimit ?? 4294967295,
        I = B.maxStartDepth ?? 4294967295,
        Y = B.timeoutMicros ?? 0,
        W = B.progressCallback;
      if (typeof F !== "number") throw new Error("Arguments must be numbers");
      if (this.matchLimit = F, G !== 0 && Z > G) throw new Error("`startIndex` cannot be greater than `endIndex`");
      if (D !== Qv && (Q.row > D.row || Q.row === D.row && Q.column > D.column)) throw new Error("`startPosition` cannot be greater than `endPosition`");
      if (W) a1.currentQueryProgressCallback = W;
      A4(A), a1._ts_query_matches_wasm(this[0], A.tree[0], Q.row, Q.column, D.row, D.column, Z, G, F, I, Y);
      let J = a1.getValue(xB, "i32"),
        X = a1.getValue(xB + H9, "i32"),
        V = a1.getValue(xB + 2 * H9, "i32"),
        C = new Array(J);
      this.exceededMatchLimit = Boolean(V);
      let K = 0,
        H = X;
      for (let z = 0; z < J; z++) {
        let $ = a1.getValue(H, "i32");
        H += H9;
        let L = a1.getValue(H, "i32");
        H += H9;
        let N = new Array(L);
        if (H = U$0(this, A.tree, H, $, N), this.textPredicates[$].every(O => O(N))) {
          C[K] = {
            pattern: $,
            patternIndex: $,
            captures: N
          };
          let O = this.setProperties[$];
          C[K].setProperties = O;
          let R = this.assertedProperties[$];
          C[K].assertedProperties = R;
          let T = this.refutedProperties[$];
          C[K].refutedProperties = T, K++;
        }
      }
      return C.length = K, a1._free(X), a1.currentQueryProgressCallback = null, C;
    }
    captures(A, B = {}) {
      let Q = B.startPosition ?? Qv,
        D = B.endPosition ?? Qv,
        Z = B.startIndex ?? 0,
        G = B.endIndex ?? 0,
        F = B.matchLimit ?? 4294967295,
        I = B.maxStartDepth ?? 4294967295,
        Y = B.timeoutMicros ?? 0,
        W = B.progressCallback;
      if (typeof F !== "number") throw new Error("Arguments must be numbers");
      if (this.matchLimit = F, G !== 0 && Z > G) throw new Error("`startIndex` cannot be greater than `endIndex`");
      if (D !== Qv && (Q.row > D.row || Q.row === D.row && Q.column > D.column)) throw new Error("`startPosition` cannot be greater than `endPosition`");
      if (W) a1.currentQueryProgressCallback = W;
      A4(A), a1._ts_query_captures_wasm(this[0], A.tree[0], Q.row, Q.column, D.row, D.column, Z, G, F, I, Y);
      let J = a1.getValue(xB, "i32"),
        X = a1.getValue(xB + H9, "i32"),
        V = a1.getValue(xB + 2 * H9, "i32"),
        C = new Array();
      this.exceededMatchLimit = Boolean(V);
      let K = new Array(),
        H = X;
      for (let z = 0; z < J; z++) {
        let $ = a1.getValue(H, "i32");
        H += H9;
        let L = a1.getValue(H, "i32");
        H += H9;
        let N = a1.getValue(H, "i32");
        if (H += H9, K.length = L, H = U$0(this, A.tree, H, $, K), this.textPredicates[$].every(O => O(K))) {
          let O = K[N],
            R = this.setProperties[$];
          O.setProperties = R;
          let T = this.assertedProperties[$];
          O.assertedProperties = T;
          let j = this.refutedProperties[$];
          O.refutedProperties = j, C.push(O);
        }
      }
      return a1._free(X), a1.currentQueryProgressCallback = null, C;
    }
    predicatesForPattern(A) {
      return this.predicates[A];
    }
    disableCapture(A) {
      let B = a1.lengthBytesUTF8(A),
        Q = a1._malloc(B + 1);
      a1.stringToUTF8(A, Q, B + 1), a1._ts_query_disable_capture(this[0], Q, B), a1._free(Q);
    }
    disablePattern(A) {
      if (A >= this.predicates.length) throw new Error(`Pattern index is ${A} but the pattern count is ${this.predicates.length}`);
      a1._ts_query_disable_pattern(this[0], A);
    }
    didExceedMatchLimit() {
      return this.exceededMatchLimit;
    }
    startIndexForPattern(A) {
      if (A >= this.predicates.length) throw new Error(`Pattern index is ${A} but the pattern count is ${this.predicates.length}`);
      return a1._ts_query_start_byte_for_pattern(this[0], A);
    }
    endIndexForPattern(A) {
      if (A >= this.predicates.length) throw new Error(`Pattern index is ${A} but the pattern count is ${this.predicates.length}`);
      return a1._ts_query_end_byte_for_pattern(this[0], A);
    }
    patternCount() {
      return a1._ts_query_pattern_count(this[0]);
    }
    captureIndexForName(A) {
      return this.captureNames.indexOf(A);
    }
    isPatternRooted(A) {
      return a1._ts_query_is_pattern_rooted(this[0], A) === 1;
    }
    isPatternNonLocal(A) {
      return a1._ts_query_is_pattern_non_local(this[0], A) === 1;
    }
    isPatternGuaranteedAtStep(A) {
      return a1._ts_query_is_pattern_guaranteed_at_step(this[0], A) === 1;
    }
  },
  X48 = /^tree_sitter_\w+$/,
  vLB = class A {
    static {
      p0(this, "Language");
    }
    [0] = 0;
    types;
    fields;
    constructor(B, Q) {
      H11(B), this[0] = Q, this.types = new Array(a1._ts_language_symbol_count(this[0]));
      for (let D = 0, Z = this.types.length; D < Z; D++) if (a1._ts_language_symbol_type(this[0], D) < 2) this.types[D] = a1.UTF8ToString(a1._ts_language_symbol_name(this[0], D));
      this.fields = new Array(a1._ts_language_field_count(this[0]) + 1);
      for (let D = 0, Z = this.fields.length; D < Z; D++) {
        let G = a1._ts_language_field_name_for_id(this[0], D);
        if (G !== 0) this.fields[D] = a1.UTF8ToString(G);else this.fields[D] = null;
      }
    }
    get name() {
      let B = a1._ts_language_name(this[0]);
      if (B === 0) return null;
      return a1.UTF8ToString(B);
    }
    get version() {
      return a1._ts_language_version(this[0]);
    }
    get abiVersion() {
      return a1._ts_language_abi_version(this[0]);
    }
    get metadata() {
      a1._ts_language_metadata(this[0]);
      let B = a1.getValue(xB, "i32"),
        Q = a1.getValue(xB + H9, "i32");
      if (B === 0) return null;
      return PLB(Q);
    }
    get fieldCount() {
      return this.fields.length - 1;
    }
    get stateCount() {
      return a1._ts_language_state_count(this[0]);
    }
    fieldIdForName(B) {
      let Q = this.fields.indexOf(B);
      return Q !== -1 ? Q : null;
    }
    fieldNameForId(B) {
      return this.fields[B] ?? null;
    }
    idForNodeType(B, Q) {
      let D = a1.lengthBytesUTF8(B),
        Z = a1._malloc(D + 1);
      a1.stringToUTF8(B, Z, D + 1);
      let G = a1._ts_language_symbol_for_name(this[0], Z, D, Q ? 1 : 0);
      return a1._free(Z), G || null;
    }
    get nodeTypeCount() {
      return a1._ts_language_symbol_count(this[0]);
    }
    nodeTypeForId(B) {
      let Q = a1._ts_language_symbol_name(this[0], B);
      return Q ? a1.UTF8ToString(Q) : null;
    }
    nodeTypeIsNamed(B) {
      return a1._ts_language_type_is_named_wasm(this[0], B) ? !0 : !1;
    }
    nodeTypeIsVisible(B) {
      return a1._ts_language_type_is_visible_wasm(this[0], B) ? !0 : !1;
    }
    get supertypes() {
      a1._ts_language_supertypes_wasm(this[0]);
      let B = a1.getValue(xB, "i32"),
        Q = a1.getValue(xB + H9, "i32"),
        D = new Array(B);
      if (B > 0) {
        let Z = Q;
        for (let G = 0; G < B; G++) D[G] = a1.getValue(Z, "i16"), Z += LLB;
      }
      return D;
    }
    subtypes(B) {
      a1._ts_language_subtypes_wasm(this[0], B);
      let Q = a1.getValue(xB, "i32"),
        D = a1.getValue(xB + H9, "i32"),
        Z = new Array(Q);
      if (Q > 0) {
        let G = D;
        for (let F = 0; F < Q; F++) Z[F] = a1.getValue(G, "i16"), G += LLB;
      }
      return Z;
    }
    nextState(B, Q) {
      return a1._ts_language_next_state(this[0], B, Q);
    }
    lookaheadIterator(B) {
      let Q = a1._ts_lookahead_iterator_new(this[0], B);
      if (Q) return new D48(Dv, Q, this);
      return null;
    }
    query(B) {
      return console.warn("Language.query is deprecated. Use new Query(language, source) instead."), new J48(this, B);
    }
    static async load(B) {
      let Q;
      if (B instanceof Uint8Array) Q = Promise.resolve(B);else if (globalThis.process?.versions.node) Q = (await import("fs/promises")).readFile(B);else Q = fetch(B).then(I => I.arrayBuffer().then(Y => {
        if (I.ok) return new Uint8Array(Y);else {
          let W = new TextDecoder("utf-8").decode(Y);
          throw new Error(`Language.load failed with status ${I.status}.

${W}`);
        }
      }));
      let D = await a1.loadWebAssemblyModule(await Q, {
          loadAsync: !0
        }),
        Z = Object.keys(D),
        G = Z.find(I => X48.test(I) && !I.includes("external_scanner_"));
      if (!G) throw console.log(`Couldn't find language function in WASM file. Symbols:
${JSON.stringify(Z, null, 2)}`), new Error("Language.load failed: no language function found in WASM file");
      let F = D[G]();
      return new A(Dv, F);
    }
  },
  V48 = (() => {
    var _scriptName = import.meta.url;
    return async function (moduleArg = {}) {
      var moduleRtn,
        Module = moduleArg,
        readyPromiseResolve,
        readyPromiseReject,
        readyPromise = new Promise((A, B) => {
          readyPromiseResolve = A, readyPromiseReject = B;
        }),
        ENVIRONMENT_IS_WEB = typeof window == "object",
        ENVIRONMENT_IS_WORKER = typeof WorkerGlobalScope != "undefined",
        ENVIRONMENT_IS_NODE = typeof process == "object" && typeof process.versions == "object" && typeof process.versions.node == "string" && process.type != "renderer",
        ENVIRONMENT_IS_SHELL = !ENVIRONMENT_IS_WEB && !ENVIRONMENT_IS_NODE && !ENVIRONMENT_IS_WORKER;
      if (ENVIRONMENT_IS_NODE) {
        let {
          createRequire: A
        } = await import("module");
        var require = A(import.meta.url);
      }
      Module.currentQueryProgressCallback = null, Module.currentProgressCallback = null, Module.currentLogCallback = null, Module.currentParseCallback = null;
      var moduleOverrides = Object.assign({}, Module),
        arguments_ = [],
        thisProgram = "./this.program",
        quit_ = p0((A, B) => {
          throw B;
        }, "quit_"),
        scriptDirectory = "";
      function locateFile(A) {
        if (Module.locateFile) return Module.locateFile(A, scriptDirectory);
        return scriptDirectory + A;
      }
      p0(locateFile, "locateFile");
      var readAsync, readBinary;
      if (ENVIRONMENT_IS_NODE) {
        var fs = require("fs"),
          nodePath = require("path");
        if (!import.meta.url.startsWith("data:")) scriptDirectory = nodePath.dirname(require("url").fileURLToPath(import.meta.url)) + "/";
        if (readBinary = p0(A => {
          A = isFileURI(A) ? new URL(A) : A;
          var B = fs.readFileSync(A);
          return B;
        }, "readBinary"), readAsync = p0(async (A, B = !0) => {
          A = isFileURI(A) ? new URL(A) : A;
          var Q = fs.readFileSync(A, B ? void 0 : "utf8");
          return Q;
        }, "readAsync"), !Module.thisProgram && process.argv.length > 1) thisProgram = process.argv[1].replace(/\\/g, "/");
        arguments_ = process.argv.slice(2), quit_ = p0((A, B) => {
          throw process.exitCode = A, B;
        }, "quit_");
      } else if (ENVIRONMENT_IS_WEB || ENVIRONMENT_IS_WORKER) {
        if (ENVIRONMENT_IS_WORKER) scriptDirectory = self.location.href;else if (typeof document != "undefined" && document.currentScript) scriptDirectory = document.currentScript.src;
        if (_scriptName) scriptDirectory = _scriptName;
        if (scriptDirectory.startsWith("blob:")) scriptDirectory = "";else scriptDirectory = scriptDirectory.slice(0, scriptDirectory.replace(/[?#].*/, "").lastIndexOf("/") + 1);
        {
          if (ENVIRONMENT_IS_WORKER) readBinary = p0(A => {
            var B = new XMLHttpRequest();
            return B.open("GET", A, !1), B.responseType = "arraybuffer", B.send(null), new Uint8Array(B.response);
          }, "readBinary");
          readAsync = p0(async A => {
            if (isFileURI(A)) return new Promise((Q, D) => {
              var Z = new XMLHttpRequest();
              Z.open("GET", A, !0), Z.responseType = "arraybuffer", Z.onload = () => {
                if (Z.status == 200 || Z.status == 0 && Z.response) {
                  Q(Z.response);
                  return;
                }
                D(Z.status);
              }, Z.onerror = D, Z.send(null);
            });
            var B = await fetch(A, {
              credentials: "same-origin"
            });
            if (B.ok) return B.arrayBuffer();
            throw new Error(B.status + " : " + B.url);
          }, "readAsync");
        }
      }
      var out = Module.print || console.log.bind(console),
        err = Module.printErr || console.error.bind(console);
      if (Object.assign(Module, moduleOverrides), moduleOverrides = null, Module.arguments) arguments_ = Module.arguments;
      if (Module.thisProgram) thisProgram = Module.thisProgram;
      var dynamicLibraries = Module.dynamicLibraries || [],
        wasmBinary = Module.wasmBinary,
        wasmMemory,
        ABORT = !1,
        EXITSTATUS;
      function assert(A, B) {
        if (!A) abort(B);
      }
      p0(assert, "assert");
      var HEAP,
        HEAP8,
        HEAPU8,
        HEAP16,
        HEAPU16,
        HEAP32,
        HEAPU32,
        HEAPF32,
        HEAP64,
        HEAPU64,
        HEAPF64,
        HEAP_DATA_VIEW,
        runtimeInitialized = !1,
        isFileURI = p0(A => A.startsWith("file://"), "isFileURI");
      function updateMemoryViews() {
        var A = wasmMemory.buffer;
        Module.HEAP_DATA_VIEW = HEAP_DATA_VIEW = new DataView(A), Module.HEAP8 = HEAP8 = new Int8Array(A), Module.HEAP16 = HEAP16 = new Int16Array(A), Module.HEAPU8 = HEAPU8 = new Uint8Array(A), Module.HEAPU16 = HEAPU16 = new Uint16Array(A), Module.HEAP32 = HEAP32 = new Int32Array(A), Module.HEAPU32 = HEAPU32 = new Uint32Array(A), Module.HEAPF32 = HEAPF32 = new Float32Array(A), Module.HEAPF64 = HEAPF64 = new Float64Array(A), Module.HEAP64 = HEAP64 = new BigInt64Array(A), Module.HEAPU64 = HEAPU64 = new BigUint64Array(A);
      }
      if (p0(updateMemoryViews, "updateMemoryViews"), Module.wasmMemory) wasmMemory = Module.wasmMemory;else {
        var INITIAL_MEMORY = Module.INITIAL_MEMORY || 33554432;
        wasmMemory = new WebAssembly.Memory({
          initial: INITIAL_MEMORY / 65536,
          maximum: 32768
        });
      }
      updateMemoryViews();
      var __RELOC_FUNCS__ = [];
      function preRun() {
        if (Module.preRun) {
          if (typeof Module.preRun == "function") Module.preRun = [Module.preRun];
          while (Module.preRun.length) addOnPreRun(Module.preRun.shift());
        }
        callRuntimeCallbacks(onPreRuns);
      }
      p0(preRun, "preRun");
      function initRuntime() {
        runtimeInitialized = !0, callRuntimeCallbacks(__RELOC_FUNCS__), wasmExports.__wasm_call_ctors(), callRuntimeCallbacks(onPostCtors);
      }
      p0(initRuntime, "initRuntime");
      function preMain() {}
      p0(preMain, "preMain");
      function postRun() {
        if (Module.postRun) {
          if (typeof Module.postRun == "function") Module.postRun = [Module.postRun];
          while (Module.postRun.length) addOnPostRun(Module.postRun.shift());
        }
        callRuntimeCallbacks(onPostRuns);
      }
      p0(postRun, "postRun");
      var runDependencies = 0,
        dependenciesFulfilled = null;
      function getUniqueRunDependency(A) {
        return A;
      }
      p0(getUniqueRunDependency, "getUniqueRunDependency");
      function addRunDependency(A) {
        runDependencies++, Module.monitorRunDependencies?.(runDependencies);
      }
      p0(addRunDependency, "addRunDependency");
      function removeRunDependency(A) {
        if (runDependencies--, Module.monitorRunDependencies?.(runDependencies), runDependencies == 0) {
          if (dependenciesFulfilled) {
            var B = dependenciesFulfilled;
            dependenciesFulfilled = null, B();
          }
        }
      }
      p0(removeRunDependency, "removeRunDependency");
      function abort(A) {
        Module.onAbort?.(A), A = "Aborted(" + A + ")", err(A), ABORT = !0, A += ". Build with -sASSERTIONS for more info.";
        var B = new WebAssembly.RuntimeError(A);
        throw readyPromiseReject(B), B;
      }
      p0(abort, "abort");
      var wasmBinaryFile;
      function findWasmBinary() {
        if (Module.locateFile) return locateFile("tree-sitter.wasm");
        return new URL("tree-sitter.wasm", import.meta.url).href;
      }
      p0(findWasmBinary, "findWasmBinary");
      function getBinarySync(A) {
        if (A == wasmBinaryFile && wasmBinary) return new Uint8Array(wasmBinary);
        if (readBinary) return readBinary(A);
        throw "both async and sync fetching of the wasm failed";
      }
      p0(getBinarySync, "getBinarySync");
      async function getWasmBinary(A) {
        if (!wasmBinary) try {
          var B = await readAsync(A);
          return new Uint8Array(B);
        } catch {}
        return getBinarySync(A);
      }
      p0(getWasmBinary, "getWasmBinary");
      async function instantiateArrayBuffer(A, B) {
        try {
          var Q = await getWasmBinary(A),
            D = await WebAssembly.instantiate(Q, B);
          return D;
        } catch (Z) {
          err(`failed to asynchronously prepare wasm: ${Z}`), abort(Z);
        }
      }
      p0(instantiateArrayBuffer, "instantiateArrayBuffer");
      async function instantiateAsync(A, B, Q) {
        if (!A && typeof WebAssembly.instantiateStreaming == "function" && !isFileURI(B) && !ENVIRONMENT_IS_NODE) try {
          var D = fetch(B, {
              credentials: "same-origin"
            }),
            Z = await WebAssembly.instantiateStreaming(D, Q);
          return Z;
        } catch (G) {
          err(`wasm streaming compile failed: ${G}`), err("falling back to ArrayBuffer instantiation");
        }
        return instantiateArrayBuffer(B, Q);
      }
      p0(instantiateAsync, "instantiateAsync");
      function getWasmImports() {
        return {
          env: wasmImports,
          wasi_snapshot_preview1: wasmImports,
          "GOT.mem": new Proxy(wasmImports, GOTHandler),
          "GOT.func": new Proxy(wasmImports, GOTHandler)
        };
      }
      p0(getWasmImports, "getWasmImports");
      async function createWasm() {
        function A(G, F) {
          wasmExports = G.exports, wasmExports = relocateExports(wasmExports, 1024);
          var I = getDylinkMetadata(F);
          if (I.neededDynlibs) dynamicLibraries = I.neededDynlibs.concat(dynamicLibraries);
          return mergeLibSymbols(wasmExports, "main"), LDSO.init(), loadDylibs(), __RELOC_FUNCS__.push(wasmExports.__wasm_apply_data_relocs), removeRunDependency("wasm-instantiate"), wasmExports;
        }
        p0(A, "receiveInstance"), addRunDependency("wasm-instantiate");
        function B(G) {
          return A(G.instance, G.module);
        }
        p0(B, "receiveInstantiationResult");
        var Q = getWasmImports();
        if (Module.instantiateWasm) return new Promise((G, F) => {
          Module.instantiateWasm(Q, (I, Y) => {
            A(I, Y), G(I.exports);
          });
        });
        wasmBinaryFile ??= findWasmBinary();
        try {
          var D = await instantiateAsync(wasmBinary, wasmBinaryFile, Q),
            Z = B(D);
          return Z;
        } catch (G) {
          return readyPromiseReject(G), Promise.reject(G);
        }
      }
      p0(createWasm, "createWasm");
      var ASM_CONSTS = {};
      class ExitStatus {
        static {
          p0(this, "ExitStatus");
        }
        name = "ExitStatus";
        constructor(A) {
          this.message = `Program terminated with exit(${A})`, this.status = A;
        }
      }
      var GOT = {},
        currentModuleWeakSymbols = new Set([]),
        GOTHandler = {
          get(A, B) {
            var Q = GOT[B];
            if (!Q) Q = GOT[B] = new WebAssembly.Global({
              value: "i32",
              mutable: !0
            });
            if (!currentModuleWeakSymbols.has(B)) Q.required = !0;
            return Q;
          }
        },
        LE_HEAP_LOAD_F32 = p0(A => HEAP_DATA_VIEW.getFloat32(A, !0), "LE_HEAP_LOAD_F32"),
        LE_HEAP_LOAD_F64 = p0(A => HEAP_DATA_VIEW.getFloat64(A, !0), "LE_HEAP_LOAD_F64"),
        LE_HEAP_LOAD_I16 = p0(A => HEAP_DATA_VIEW.getInt16(A, !0), "LE_HEAP_LOAD_I16"),
        LE_HEAP_LOAD_I32 = p0(A => HEAP_DATA_VIEW.getInt32(A, !0), "LE_HEAP_LOAD_I32"),
        LE_HEAP_LOAD_U16 = p0(A => HEAP_DATA_VIEW.getUint16(A, !0), "LE_HEAP_LOAD_U16"),
        LE_HEAP_LOAD_U32 = p0(A => HEAP_DATA_VIEW.getUint32(A, !0), "LE_HEAP_LOAD_U32"),
        LE_HEAP_STORE_F32 = p0((A, B) => HEAP_DATA_VIEW.setFloat32(A, B, !0), "LE_HEAP_STORE_F32"),
        LE_HEAP_STORE_F64 = p0((A, B) => HEAP_DATA_VIEW.setFloat64(A, B, !0), "LE_HEAP_STORE_F64"),
        LE_HEAP_STORE_I16 = p0((A, B) => HEAP_DATA_VIEW.setInt16(A, B, !0), "LE_HEAP_STORE_I16"),
        LE_HEAP_STORE_I32 = p0((A, B) => HEAP_DATA_VIEW.setInt32(A, B, !0), "LE_HEAP_STORE_I32"),
        LE_HEAP_STORE_U16 = p0((A, B) => HEAP_DATA_VIEW.setUint16(A, B, !0), "LE_HEAP_STORE_U16"),
        LE_HEAP_STORE_U32 = p0((A, B) => HEAP_DATA_VIEW.setUint32(A, B, !0), "LE_HEAP_STORE_U32"),
        callRuntimeCallbacks = p0(A => {
          while (A.length > 0) A.shift()(Module);
        }, "callRuntimeCallbacks"),
        onPostRuns = [],
        addOnPostRun = p0(A => onPostRuns.unshift(A), "addOnPostRun"),
        onPreRuns = [],
        addOnPreRun = p0(A => onPreRuns.unshift(A), "addOnPreRun"),
        UTF8Decoder = typeof TextDecoder != "undefined" ? new TextDecoder() : void 0,
        UTF8ArrayToString = p0((A, B = 0, Q = NaN) => {
          var D = B + Q,
            Z = B;
          while (A[Z] && !(Z >= D)) ++Z;
          if (Z - B > 16 && A.buffer && UTF8Decoder) return UTF8Decoder.decode(A.subarray(B, Z));
          var G = "";
          while (B < Z) {
            var F = A[B++];
            if (!(F & 128)) {
              G += String.fromCharCode(F);
              continue;
            }
            var I = A[B++] & 63;
            if ((F & 224) == 192) {
              G += String.fromCharCode((F & 31) << 6 | I);
              continue;
            }
            var Y = A[B++] & 63;
            if ((F & 240) == 224) F = (F & 15) << 12 | I << 6 | Y;else F = (F & 7) << 18 | I << 12 | Y << 6 | A[B++] & 63;
            if (F < 65536) G += String.fromCharCode(F);else {
              var W = F - 65536;
              G += String.fromCharCode(55296 | W >> 10, 56320 | W & 1023);
            }
          }
          return G;
        }, "UTF8ArrayToString"),
        getDylinkMetadata = p0(A => {
          var B = 0,
            Q = 0;
          function D() {
            return A[B++];
          }
          p0(D, "getU8");
          function Z() {
            var n = 0,
              v = 1;
            while (!0) {
              var t = A[B++];
              if (n += (t & 127) * v, v *= 128, !(t & 128)) break;
            }
            return n;
          }
          p0(Z, "getLEB");
          function G() {
            var n = Z();
            return B += n, UTF8ArrayToString(A, B - n, n);
          }
          p0(G, "getString");
          function F(n, v) {
            if (n) throw new Error(v);
          }
          p0(F, "failIf");
          var I = "dylink.0";
          if (A instanceof WebAssembly.Module) {
            var Y = WebAssembly.Module.customSections(A, I);
            if (Y.length === 0) I = "dylink", Y = WebAssembly.Module.customSections(A, I);
            F(Y.length === 0, "need dylink section"), A = new Uint8Array(Y[0]), Q = A.length;
          } else {
            var W = new Uint32Array(new Uint8Array(A.subarray(0, 24)).buffer),
              J = W[0] == 1836278016 || W[0] == 6386541;
            F(!J, "need to see wasm magic number"), F(A[8] !== 0, "need the dylink section to be first"), B = 9;
            var X = Z();
            Q = B + X, I = G();
          }
          var V = {
            neededDynlibs: [],
            tlsExports: new Set(),
            weakImports: new Set()
          };
          if (I == "dylink") {
            V.memorySize = Z(), V.memoryAlign = Z(), V.tableSize = Z(), V.tableAlign = Z();
            var C = Z();
            for (var K = 0; K < C; ++K) {
              var H = G();
              V.neededDynlibs.push(H);
            }
          } else {
            F(I !== "dylink.0");
            var z = 1,
              $ = 2,
              L = 3,
              N = 4,
              O = 256,
              R = 3,
              T = 1;
            while (B < Q) {
              var j = D(),
                f = Z();
              if (j === z) V.memorySize = Z(), V.memoryAlign = Z(), V.tableSize = Z(), V.tableAlign = Z();else if (j === $) {
                var C = Z();
                for (var K = 0; K < C; ++K) H = G(), V.neededDynlibs.push(H);
              } else if (j === L) {
                var y = Z();
                while (y--) {
                  var c = G(),
                    h = Z();
                  if (h & O) V.tlsExports.add(c);
                }
              } else if (j === N) {
                var y = Z();
                while (y--) {
                  var a = G(),
                    c = G(),
                    h = Z();
                  if ((h & R) == T) V.weakImports.add(c);
                }
              } else B += f;
            }
          }
          return V;
        }, "getDylinkMetadata");
      function getValue(A, B = "i8") {
        if (B.endsWith("*")) B = "*";
        switch (B) {
          case "i1":
            return HEAP8[A];
          case "i8":
            return HEAP8[A];
          case "i16":
            return LE_HEAP_LOAD_I16((A >> 1) * 2);
          case "i32":
            return LE_HEAP_LOAD_I32((A >> 2) * 4);
          case "i64":
            return HEAP64[A >> 3];
          case "float":
            return LE_HEAP_LOAD_F32((A >> 2) * 4);
          case "double":
            return LE_HEAP_LOAD_F64((A >> 3) * 8);
          case "*":
            return LE_HEAP_LOAD_U32((A >> 2) * 4);
          default:
            abort(`invalid type for getValue: ${B}`);
        }
      }
      p0(getValue, "getValue");
      var newDSO = p0((A, B, Q) => {
          var D = {
            refcount: 1 / 0,
            name: A,
            exports: Q,
            global: !0
          };
          if (LDSO.loadedLibsByName[A] = D, B != null) LDSO.loadedLibsByHandle[B] = D;
          return D;
        }, "newDSO"),
        LDSO = {
          loadedLibsByName: {},
          loadedLibsByHandle: {},
          init() {
            newDSO("__main__", 0, wasmImports);
          }
        },
        ___heap_base = 78224,
        alignMemory = p0((A, B) => Math.ceil(A / B) * B, "alignMemory"),
        getMemory = p0(A => {
          if (runtimeInitialized) return _calloc(A, 1);
          var B = ___heap_base,
            Q = B + alignMemory(A, 16);
          return ___heap_base = Q, GOT.__heap_base.value = Q, B;
        }, "getMemory"),
        isInternalSym = p0(A => ["__cpp_exception", "__c_longjmp", "__wasm_apply_data_relocs", "__dso_handle", "__tls_size", "__tls_align", "__set_stack_limits", "_emscripten_tls_init", "__wasm_init_tls", "__wasm_call_ctors", "__start_em_asm", "__stop_em_asm", "__start_em_js", "__stop_em_js"].includes(A) || A.startsWith("__em_js__"), "isInternalSym"),
        uleb128Encode = p0((A, B) => {
          if (A < 128) B.push(A);else B.push(A % 128 | 128, A >> 7);
        }, "uleb128Encode"),
        sigToWasmTypes = p0(A => {
          var B = {
              i: "i32",
              j: "i64",
              f: "f32",
              d: "f64",
              e: "externref",
              p: "i32"
            },
            Q = {
              parameters: [],
              results: A[0] == "v" ? [] : [B[A[0]]]
            };
          for (var D = 1; D < A.length; ++D) Q.parameters.push(B[A[D]]);
          return Q;
        }, "sigToWasmTypes"),
        generateFuncType = p0((A, B) => {
          var Q = A.slice(0, 1),
            D = A.slice(1),
            Z = {
              i: 127,
              p: 127,
              j: 126,
              f: 125,
              d: 124,
              e: 111
            };
          B.push(96), uleb128Encode(D.length, B);
          for (var G = 0; G < D.length; ++G) B.push(Z[D[G]]);
          if (Q == "v") B.push(0);else B.push(1, Z[Q]);
        }, "generateFuncType"),
        convertJsFunctionToWasm = p0((A, B) => {
          if (typeof WebAssembly.Function == "function") return new WebAssembly.Function(sigToWasmTypes(B), A);
          var Q = [1];
          generateFuncType(B, Q);
          var D = [0, 97, 115, 109, 1, 0, 0, 0, 1];
          uleb128Encode(Q.length, D), D.push(...Q), D.push(2, 7, 1, 1, 101, 1, 102, 0, 0, 7, 5, 1, 1, 102, 0, 0);
          var Z = new WebAssembly.Module(new Uint8Array(D)),
            G = new WebAssembly.Instance(Z, {
              e: {
                f: A
              }
            }),
            F = G.exports.f;
          return F;
        }, "convertJsFunctionToWasm"),
        wasmTableMirror = [],
        wasmTable = new WebAssembly.Table({
          initial: 31,
          element: "anyfunc"
        }),
        getWasmTableEntry = p0(A => {
          var B = wasmTableMirror[A];
          if (!B) {
            if (A >= wasmTableMirror.length) wasmTableMirror.length = A + 1;
            wasmTableMirror[A] = B = wasmTable.get(A);
          }
          return B;
        }, "getWasmTableEntry"),
        updateTableMap = p0((A, B) => {
          if (functionsInTableMap) for (var Q = A; Q < A + B; Q++) {
            var D = getWasmTableEntry(Q);
            if (D) functionsInTableMap.set(D, Q);
          }
        }, "updateTableMap"),
        functionsInTableMap,
        getFunctionAddress = p0(A => {
          if (!functionsInTableMap) functionsInTableMap = new WeakMap(), updateTableMap(0, wasmTable.length);
          return functionsInTableMap.get(A) || 0;
        }, "getFunctionAddress"),
        freeTableIndexes = [],
        getEmptyTableSlot = p0(() => {
          if (freeTableIndexes.length) return freeTableIndexes.pop();
          try {
            wasmTable.grow(1);
          } catch (A) {
            if (!(A instanceof RangeError)) throw A;
            throw "Unable to grow wasm table. Set ALLOW_TABLE_GROWTH.";
          }
          return wasmTable.length - 1;
        }, "getEmptyTableSlot"),
        setWasmTableEntry = p0((A, B) => {
          wasmTable.set(A, B), wasmTableMirror[A] = wasmTable.get(A);
        }, "setWasmTableEntry"),
        addFunction = p0((A, B) => {
          var Q = getFunctionAddress(A);
          if (Q) return Q;
          var D = getEmptyTableSlot();
          try {
            setWasmTableEntry(D, A);
          } catch (G) {
            if (!(G instanceof TypeError)) throw G;
            var Z = convertJsFunctionToWasm(A, B);
            setWasmTableEntry(D, Z);
          }
          return functionsInTableMap.set(A, D), D;
        }, "addFunction"),
        updateGOT = p0((A, B) => {
          for (var Q in A) {
            if (isInternalSym(Q)) continue;
            var D = A[Q];
            if (GOT[Q] ||= new WebAssembly.Global({
              value: "i32",
              mutable: !0
            }), B || GOT[Q].value == 0) if (typeof D == "function") GOT[Q].value = addFunction(D);else if (typeof D == "number") GOT[Q].value = D;else err(`unhandled export type for '${Q}': ${typeof D}`);
          }
        }, "updateGOT"),
        relocateExports = p0((A, B, Q) => {
          var D = {};
          for (var Z in A) {
            var G = A[Z];
            if (typeof G == "object") G = G.value;
            if (typeof G == "number") G += B;
            D[Z] = G;
          }
          return updateGOT(D, Q), D;
        }, "relocateExports"),
        isSymbolDefined = p0(A => {
          var B = wasmImports[A];
          if (!B || B.stub) return !1;
          return !0;
        }, "isSymbolDefined"),
        dynCall = p0((A, B, Q = []) => {
          var D = getWasmTableEntry(B)(...Q);
          return D;
        }, "dynCall"),
        stackSave = p0(() => _emscripten_stack_get_current(), "stackSave"),
        stackRestore = p0(A => __emscripten_stack_restore(A), "stackRestore"),
        createInvokeFunction = p0(A => (B, ...Q) => {
          var D = stackSave();
          try {
            return dynCall(A, B, Q);
          } catch (Z) {
            if (stackRestore(D), Z !== Z + 0) throw Z;
            if (_setThrew(1, 0), A[0] == "j") return 0n;
          }
        }, "createInvokeFunction"),
        resolveGlobalSymbol = p0((A, B = !1) => {
          var Q;
          if (isSymbolDefined(A)) Q = wasmImports[A];else if (A.startsWith("invoke_")) Q = wasmImports[A] = createInvokeFunction(A.split("_")[1]);
          return {
            sym: Q,
            name: A
          };
        }, "resolveGlobalSymbol"),
        onPostCtors = [],
        addOnPostCtor = p0(A => onPostCtors.unshift(A), "addOnPostCtor"),
        UTF8ToString = p0((A, B) => A ? UTF8ArrayToString(HEAPU8, A, B) : "", "UTF8ToString"),
        loadWebAssemblyModule = p0((binary, flags, libName, localScope, handle) => {
          var metadata = getDylinkMetadata(binary);
          currentModuleWeakSymbols = metadata.weakImports;
          function loadModule() {
            var memAlign = Math.pow(2, metadata.memoryAlign),
              memoryBase = metadata.memorySize ? alignMemory(getMemory(metadata.memorySize + memAlign), memAlign) : 0,
              tableBase = metadata.tableSize ? wasmTable.length : 0;
            if (handle) HEAP8[handle + 8] = 1, LE_HEAP_STORE_U32((handle + 12 >> 2) * 4, memoryBase), LE_HEAP_STORE_I32((handle + 16 >> 2) * 4, metadata.memorySize), LE_HEAP_STORE_U32((handle + 20 >> 2) * 4, tableBase), LE_HEAP_STORE_I32((handle + 24 >> 2) * 4, metadata.tableSize);
            if (metadata.tableSize) wasmTable.grow(metadata.tableSize);
            var moduleExports;
            function resolveSymbol(A) {
              var B = resolveGlobalSymbol(A).sym;
              if (!B && localScope) B = localScope[A];
              if (!B) B = moduleExports[A];
              return B;
            }
            p0(resolveSymbol, "resolveSymbol");
            var proxyHandler = {
                get(A, B) {
                  switch (B) {
                    case "__memory_base":
                      return memoryBase;
                    case "__table_base":
                      return tableBase;
                  }
                  if (B in wasmImports && !wasmImports[B].stub) {
                    var Q = wasmImports[B];
                    return Q;
                  }
                  if (!(B in A)) {
                    var D;
                    A[B] = (...Z) => {
                      return D ||= resolveSymbol(B), D(...Z);
                    };
                  }
                  return A[B];
                }
              },
              proxy = new Proxy({}, proxyHandler),
              info = {
                "GOT.mem": new Proxy({}, GOTHandler),
                "GOT.func": new Proxy({}, GOTHandler),
                env: proxy,
                wasi_snapshot_preview1: proxy
              };
            function postInstantiation(module, instance) {
              if (updateTableMap(tableBase, metadata.tableSize), moduleExports = relocateExports(instance.exports, memoryBase), !flags.allowUndefined) reportUndefinedSymbols();
              function addEmAsm(addr, body) {
                var args = [],
                  arity = 0;
                for (; arity < 16; arity++) if (body.indexOf("$" + arity) != -1) args.push("$" + arity);else break;
                args = args.join(",");
                var func = `(${args}) => { ${body} };`;
                ASM_CONSTS[start] = eval(func);
              }
              if (p0(addEmAsm, "addEmAsm"), "__start_em_asm" in moduleExports) {
                var {
                  __start_em_asm: start,
                  __stop_em_asm: stop
                } = moduleExports;
                while (start < stop) {
                  var jsString = UTF8ToString(start);
                  addEmAsm(start, jsString), start = HEAPU8.indexOf(0, start) + 1;
                }
              }
              function addEmJs(name, cSig, body) {
                var jsArgs = [];
                if (cSig = cSig.slice(1, -1), cSig != "void") {
                  cSig = cSig.split(",");
                  for (var i in cSig) {
                    var jsArg = cSig[i].split(" ").pop();
                    jsArgs.push(jsArg.replace("*", ""));
                  }
                }
                var func = `(${jsArgs}) => ${body};`;
                moduleExports[name] = eval(func);
              }
              p0(addEmJs, "addEmJs");
              for (var name in moduleExports) if (name.startsWith("__em_js__")) {
                var start = moduleExports[name],
                  jsString = UTF8ToString(start),
                  parts = jsString.split("<::>");
                addEmJs(name.replace("__em_js__", ""), parts[0], parts[1]), delete moduleExports[name];
              }
              var applyRelocs = moduleExports.__wasm_apply_data_relocs;
              if (applyRelocs) if (runtimeInitialized) applyRelocs();else __RELOC_FUNCS__.push(applyRelocs);
              var init = moduleExports.__wasm_call_ctors;
              if (init) if (runtimeInitialized) init();else addOnPostCtor(init);
              return moduleExports;
            }
            if (p0(postInstantiation, "postInstantiation"), flags.loadAsync) {
              if (binary instanceof WebAssembly.Module) {
                var instance = new WebAssembly.Instance(binary, info);
                return Promise.resolve(postInstantiation(binary, instance));
              }
              return WebAssembly.instantiate(binary, info).then(A => postInstantiation(A.module, A.instance));
            }
            var module = binary instanceof WebAssembly.Module ? binary : new WebAssembly.Module(binary),
              instance = new WebAssembly.Instance(module, info);
            return postInstantiation(module, instance);
          }
          if (p0(loadModule, "loadModule"), flags.loadAsync) return metadata.neededDynlibs.reduce((A, B) => A.then(() => loadDynamicLibrary(B, flags, localScope)), Promise.resolve()).then(loadModule);
          return metadata.neededDynlibs.forEach(A => loadDynamicLibrary(A, flags, localScope)), loadModule();
        }, "loadWebAssemblyModule"),
        mergeLibSymbols = p0((A, B) => {
          for (var [Q, D] of Object.entries(A)) {
            let Z = p0(F => {
              if (!isSymbolDefined(F)) wasmImports[F] = D;
            }, "setImport");
            Z(Q);
            let G = "__main_argc_argv";
            if (Q == "main") Z(G);
            if (Q == G) Z("main");
          }
        }, "mergeLibSymbols"),
        asyncLoad = p0(async A => {
          var B = await readAsync(A);
          return new Uint8Array(B);
        }, "asyncLoad");
      function loadDynamicLibrary(A, B = {
        global: !0,
        nodelete: !0
      }, Q, D) {
        var Z = LDSO.loadedLibsByName[A];
        if (Z) {
          if (!B.global) {
            if (Q) Object.assign(Q, Z.exports);
          } else if (!Z.global) Z.global = !0, mergeLibSymbols(Z.exports, A);
          if (B.nodelete && Z.refcount !== 1 / 0) Z.refcount = 1 / 0;
          if (Z.refcount++, D) LDSO.loadedLibsByHandle[D] = Z;
          return B.loadAsync ? Promise.resolve(!0) : !0;
        }
        Z = newDSO(A, D, "loading"), Z.refcount = B.nodelete ? 1 / 0 : 1, Z.global = B.global;
        function G() {
          if (D) {
            var Y = LE_HEAP_LOAD_U32((D + 28 >> 2) * 4),
              W = LE_HEAP_LOAD_U32((D + 32 >> 2) * 4);
            if (Y && W) {
              var J = HEAP8.slice(Y, Y + W);
              return B.loadAsync ? Promise.resolve(J) : J;
            }
          }
          var X = locateFile(A);
          if (B.loadAsync) return asyncLoad(X);
          if (!readBinary) throw new Error(`${X}: file not found, and synchronous loading of external files is not available`);
          return readBinary(X);
        }
        p0(G, "loadLibData");
        function F() {
          if (B.loadAsync) return G().then(Y => loadWebAssemblyModule(Y, B, A, Q, D));
          return loadWebAssemblyModule(G(), B, A, Q, D);
        }
        p0(F, "getExports");
        function I(Y) {
          if (Z.global) mergeLibSymbols(Y, A);else if (Q) Object.assign(Q, Y);
          Z.exports = Y;
        }
        if (p0(I, "moduleLoaded"), B.loadAsync) return F().then(Y => {
          return I(Y), !0;
        });
        return I(F()), !0;
      }
      p0(loadDynamicLibrary, "loadDynamicLibrary");
      var reportUndefinedSymbols = p0(() => {
          for (var [A, B] of Object.entries(GOT)) if (B.value == 0) {
            var Q = resolveGlobalSymbol(A, !0).sym;
            if (!Q && !B.required) continue;
            if (typeof Q == "function") B.value = addFunction(Q, Q.sig);else if (typeof Q == "number") B.value = Q;else throw new Error(`bad export type for '${A}': ${typeof Q}`);
          }
        }, "reportUndefinedSymbols"),
        loadDylibs = p0(() => {
          if (!dynamicLibraries.length) {
            reportUndefinedSymbols();
            return;
          }
          addRunDependency("loadDylibs"), dynamicLibraries.reduce((A, B) => A.then(() => loadDynamicLibrary(B, {
            loadAsync: !0,
            global: !0,
            nodelete: !0,
            allowUndefined: !0
          })), Promise.resolve()).then(() => {
            reportUndefinedSymbols(), removeRunDependency("loadDylibs");
          });
        }, "loadDylibs"),
        noExitRuntime = Module.noExitRuntime || !0;
      function setValue(A, B, Q = "i8") {
        if (Q.endsWith("*")) Q = "*";
        switch (Q) {
          case "i1":
            HEAP8[A] = B;
            break;
          case "i8":
            HEAP8[A] = B;
            break;
          case "i16":
            LE_HEAP_STORE_I16((A >> 1) * 2, B);
            break;
          case "i32":
            LE_HEAP_STORE_I32((A >> 2) * 4, B);
            break;
          case "i64":
            HEAP64[A >> 3] = BigInt(B);
            break;
          case "float":
            LE_HEAP_STORE_F32((A >> 2) * 4, B);
            break;
          case "double":
            LE_HEAP_STORE_F64((A >> 3) * 8, B);
            break;
          case "*":
            LE_HEAP_STORE_U32((A >> 2) * 4, B);
            break;
          default:
            abort(`invalid type for setValue: ${Q}`);
        }
      }
      p0(setValue, "setValue");
      var ___memory_base = new WebAssembly.Global({
          value: "i32",
          mutable: !1
        }, 1024),
        ___stack_pointer = new WebAssembly.Global({
          value: "i32",
          mutable: !0
        }, 78224),
        ___table_base = new WebAssembly.Global({
          value: "i32",
          mutable: !1
        }, 1),
        __abort_js = p0(() => abort(""), "__abort_js");
      __abort_js.sig = "v";
      var _emscripten_get_now = p0(() => performance.now(), "_emscripten_get_now");
      _emscripten_get_now.sig = "d";
      var _emscripten_date_now = p0(() => Date.now(), "_emscripten_date_now");
      _emscripten_date_now.sig = "d";
      var nowIsMonotonic = 1,
        checkWasiClock = p0(A => A >= 0 && A <= 3, "checkWasiClock"),
        INT53_MAX = 9007199254740992,
        INT53_MIN = -9007199254740992,
        bigintToI53Checked = p0(A => A < INT53_MIN || A > INT53_MAX ? NaN : Number(A), "bigintToI53Checked");
      function _clock_time_get(A, B, Q) {
        if (B = bigintToI53Checked(B), !checkWasiClock(A)) return 28;
        var D;
        if (A === 0) D = _emscripten_date_now();else if (nowIsMonotonic) D = _emscripten_get_now();else return 52;
        var Z = Math.round(D * 1000 * 1000);
        return HEAP64[Q >> 3] = BigInt(Z), 0;
      }
      p0(_clock_time_get, "_clock_time_get"), _clock_time_get.sig = "iijp";
      var getHeapMax = p0(() => 2147483648, "getHeapMax"),
        growMemory = p0(A => {
          var B = wasmMemory.buffer,
            Q = (A - B.byteLength + 65535) / 65536 | 0;
          try {
            return wasmMemory.grow(Q), updateMemoryViews(), 1;
          } catch (D) {}
        }, "growMemory"),
        _emscripten_resize_heap = p0(A => {
          var B = HEAPU8.length;
          A >>>= 0;
          var Q = getHeapMax();
          if (A > Q) return !1;
          for (var D = 1; D <= 4; D *= 2) {
            var Z = B * (1 + 0.2 / D);
            Z = Math.min(Z, A + 100663296);
            var G = Math.min(Q, alignMemory(Math.max(A, Z), 65536)),
              F = growMemory(G);
            if (F) return !0;
          }
          return !1;
        }, "_emscripten_resize_heap");
      _emscripten_resize_heap.sig = "ip";
      var _fd_close = p0(A => 52, "_fd_close");
      _fd_close.sig = "ii";
      function _fd_seek(A, B, Q, D) {
        return B = bigintToI53Checked(B), 70;
      }
      p0(_fd_seek, "_fd_seek"), _fd_seek.sig = "iijip";
      var printCharBuffers = [null, [], []],
        printChar = p0((A, B) => {
          var Q = printCharBuffers[A];
          if (B === 0 || B === 10) (A === 1 ? out : err)(UTF8ArrayToString(Q)), Q.length = 0;else Q.push(B);
        }, "printChar"),
        flush_NO_FILESYSTEM = p0(() => {
          if (printCharBuffers[1].length) printChar(1, 10);
          if (printCharBuffers[2].length) printChar(2, 10);
        }, "flush_NO_FILESYSTEM"),
        SYSCALLS = {
          varargs: void 0,
          getStr(A) {
            var B = UTF8ToString(A);
            return B;
          }
        },
        _fd_write = p0((A, B, Q, D) => {
          var Z = 0;
          for (var G = 0; G < Q; G++) {
            var F = LE_HEAP_LOAD_U32((B >> 2) * 4),
              I = LE_HEAP_LOAD_U32((B + 4 >> 2) * 4);
            B += 8;
            for (var Y = 0; Y < I; Y++) printChar(A, HEAPU8[F + Y]);
            Z += I;
          }
          return LE_HEAP_STORE_U32((D >> 2) * 4, Z), 0;
        }, "_fd_write");
      _fd_write.sig = "iippp";
      function _tree_sitter_log_callback(A, B) {
        if (Module.currentLogCallback) {
          let Q = UTF8ToString(B);
          Module.currentLogCallback(Q, A !== 0);
        }
      }
      p0(_tree_sitter_log_callback, "_tree_sitter_log_callback");
      function _tree_sitter_parse_callback(A, B, Q, D, Z) {
        let F = Module.currentParseCallback(B, {
          row: Q,
          column: D
        });
        if (typeof F === "string") setValue(Z, F.length, "i32"), stringToUTF16(F, A, 10240);else setValue(Z, 0, "i32");
      }
      p0(_tree_sitter_parse_callback, "_tree_sitter_parse_callback");
      function _tree_sitter_progress_callback(A, B) {
        if (Module.currentProgressCallback) return Module.currentProgressCallback({
          currentOffset: A,
          hasError: B
        });
        return !1;
      }
      p0(_tree_sitter_progress_callback, "_tree_sitter_progress_callback");
      function _tree_sitter_query_progress_callback(A) {
        if (Module.currentQueryProgressCallback) return Module.currentQueryProgressCallback({
          currentOffset: A
        });
        return !1;
      }
      p0(_tree_sitter_query_progress_callback, "_tree_sitter_query_progress_callback");
      var runtimeKeepaliveCounter = 0,
        keepRuntimeAlive = p0(() => noExitRuntime || runtimeKeepaliveCounter > 0, "keepRuntimeAlive"),
        _proc_exit = p0(A => {
          if (EXITSTATUS = A, !keepRuntimeAlive()) Module.onExit?.(A), ABORT = !0;
          quit_(A, new ExitStatus(A));
        }, "_proc_exit");
      _proc_exit.sig = "vi";
      var exitJS = p0((A, B) => {
          EXITSTATUS = A, _proc_exit(A);
        }, "exitJS"),
        handleException = p0(A => {
          if (A instanceof ExitStatus || A == "unwind") return EXITSTATUS;
          quit_(1, A);
        }, "handleException"),
        lengthBytesUTF8 = p0(A => {
          var B = 0;
          for (var Q = 0; Q < A.length; ++Q) {
            var D = A.charCodeAt(Q);
            if (D <= 127) B++;else if (D <= 2047) B += 2;else if (D >= 55296 && D <= 57343) B += 4, ++Q;else B += 3;
          }
          return B;
        }, "lengthBytesUTF8"),
        stringToUTF8Array = p0((A, B, Q, D) => {
          if (!(D > 0)) return 0;
          var Z = Q,
            G = Q + D - 1;
          for (var F = 0; F < A.length; ++F) {
            var I = A.charCodeAt(F);
            if (I >= 55296 && I <= 57343) {
              var Y = A.charCodeAt(++F);
              I = 65536 + ((I & 1023) << 10) | Y & 1023;
            }
            if (I <= 127) {
              if (Q >= G) break;
              B[Q++] = I;
            } else if (I <= 2047) {
              if (Q + 1 >= G) break;
              B[Q++] = 192 | I >> 6, B[Q++] = 128 | I & 63;
            } else if (I <= 65535) {
              if (Q + 2 >= G) break;
              B[Q++] = 224 | I >> 12, B[Q++] = 128 | I >> 6 & 63, B[Q++] = 128 | I & 63;
            } else {
              if (Q + 3 >= G) break;
              B[Q++] = 240 | I >> 18, B[Q++] = 128 | I >> 12 & 63, B[Q++] = 128 | I >> 6 & 63, B[Q++] = 128 | I & 63;
            }
          }
          return B[Q] = 0, Q - Z;
        }, "stringToUTF8Array"),
        stringToUTF8 = p0((A, B, Q) => stringToUTF8Array(A, HEAPU8, B, Q), "stringToUTF8"),
        stackAlloc = p0(A => __emscripten_stack_alloc(A), "stackAlloc"),
        stringToUTF8OnStack = p0(A => {
          var B = lengthBytesUTF8(A) + 1,
            Q = stackAlloc(B);
          return stringToUTF8(A, Q, B), Q;
        }, "stringToUTF8OnStack"),
        AsciiToString = p0(A => {
          var B = "";
          while (!0) {
            var Q = HEAPU8[A++];
            if (!Q) return B;
            B += String.fromCharCode(Q);
          }
        }, "AsciiToString"),
        stringToUTF16 = p0((A, B, Q) => {
          if (Q ??= 2147483647, Q < 2) return 0;
          Q -= 2;
          var D = B,
            Z = Q < A.length * 2 ? Q / 2 : A.length;
          for (var G = 0; G < Z; ++G) {
            var F = A.charCodeAt(G);
            LE_HEAP_STORE_I16((B >> 1) * 2, F), B += 2;
          }
          return LE_HEAP_STORE_I16((B >> 1) * 2, 0), B - D;
        }, "stringToUTF16"),
        wasmImports = {
          __heap_base: ___heap_base,
          __indirect_function_table: wasmTable,
          __memory_base: ___memory_base,
          __stack_pointer: ___stack_pointer,
          __table_base: ___table_base,
          _abort_js: __abort_js,
          clock_time_get: _clock_time_get,
          emscripten_resize_heap: _emscripten_resize_heap,
          fd_close: _fd_close,
          fd_seek: _fd_seek,
          fd_write: _fd_write,
          memory: wasmMemory,
          tree_sitter_log_callback: _tree_sitter_log_callback,
          tree_sitter_parse_callback: _tree_sitter_parse_callback,
          tree_sitter_progress_callback: _tree_sitter_progress_callback,
          tree_sitter_query_progress_callback: _tree_sitter_query_progress_callback
        },
        wasmExports = await createWasm(),
        ___wasm_call_ctors = wasmExports.__wasm_call_ctors,
        _malloc = Module._malloc = wasmExports.malloc,
        _calloc = Module._calloc = wasmExports.calloc,
        _realloc = Module._realloc = wasmExports.realloc,
        _free = Module._free = wasmExports.free,
        _memcmp = Module._memcmp = wasmExports.memcmp,
        _ts_language_symbol_count = Module._ts_language_symbol_count = wasmExports.ts_language_symbol_count,
        _ts_language_state_count = Module._ts_language_state_count = wasmExports.ts_language_state_count,
        _ts_language_version = Module._ts_language_version = wasmExports.ts_language_version,
        _ts_language_abi_version = Module._ts_language_abi_version = wasmExports.ts_language_abi_version,
        _ts_language_metadata = Module._ts_language_metadata = wasmExports.ts_language_metadata,
        _ts_language_name = Module._ts_language_name = wasmExports.ts_language_name,
        _ts_language_field_count = Module._ts_language_field_count = wasmExports.ts_language_field_count,
        _ts_language_next_state = Module._ts_language_next_state = wasmExports.ts_language_next_state,
        _ts_language_symbol_name = Module._ts_language_symbol_name = wasmExports.ts_language_symbol_name,
        _ts_language_symbol_for_name = Module._ts_language_symbol_for_name = wasmExports.ts_language_symbol_for_name,
        _strncmp = Module._strncmp = wasmExports.strncmp,
        _ts_language_symbol_type = Module._ts_language_symbol_type = wasmExports.ts_language_symbol_type,
        _ts_language_field_name_for_id = Module._ts_language_field_name_for_id = wasmExports.ts_language_field_name_for_id,
        _ts_lookahead_iterator_new = Module._ts_lookahead_iterator_new = wasmExports.ts_lookahead_iterator_new,
        _ts_lookahead_iterator_delete = Module._ts_lookahead_iterator_delete = wasmExports.ts_lookahead_iterator_delete,
        _ts_lookahead_iterator_reset_state = Module._ts_lookahead_iterator_reset_state = wasmExports.ts_lookahead_iterator_reset_state,
        _ts_lookahead_iterator_reset = Module._ts_lookahead_iterator_reset = wasmExports.ts_lookahead_iterator_reset,
        _ts_lookahead_iterator_next = Module._ts_lookahead_iterator_next = wasmExports.ts_lookahead_iterator_next,
        _ts_lookahead_iterator_current_symbol = Module._ts_lookahead_iterator_current_symbol = wasmExports.ts_lookahead_iterator_current_symbol,
        _ts_parser_delete = Module._ts_parser_delete = wasmExports.ts_parser_delete,
        _ts_parser_reset = Module._ts_parser_reset = wasmExports.ts_parser_reset,
        _ts_parser_set_language = Module._ts_parser_set_language = wasmExports.ts_parser_set_language,
        _ts_parser_timeout_micros = Module._ts_parser_timeout_micros = wasmExports.ts_parser_timeout_micros,
        _ts_parser_set_timeout_micros = Module._ts_parser_set_timeout_micros = wasmExports.ts_parser_set_timeout_micros,
        _ts_parser_set_included_ranges = Module._ts_parser_set_included_ranges = wasmExports.ts_parser_set_included_ranges,
        _ts_query_new = Module._ts_query_new = wasmExports.ts_query_new,
        _ts_query_delete = Module._ts_query_delete = wasmExports.ts_query_delete,
        _iswspace = Module._iswspace = wasmExports.iswspace,
        _iswalnum = Module._iswalnum = wasmExports.iswalnum,
        _ts_query_pattern_count = Module._ts_query_pattern_count = wasmExports.ts_query_pattern_count,
        _ts_query_capture_count = Module._ts_query_capture_count = wasmExports.ts_query_capture_count,
        _ts_query_string_count = Module._ts_query_string_count = wasmExports.ts_query_string_count,
        _ts_query_capture_name_for_id = Module._ts_query_capture_name_for_id = wasmExports.ts_query_capture_name_for_id,
        _ts_query_capture_quantifier_for_id = Module._ts_query_capture_quantifier_for_id = wasmExports.ts_query_capture_quantifier_for_id,
        _ts_query_string_value_for_id = Module._ts_query_string_value_for_id = wasmExports.ts_query_string_value_for_id,
        _ts_query_predicates_for_pattern = Module._ts_query_predicates_for_pattern = wasmExports.ts_query_predicates_for_pattern,
        _ts_query_start_byte_for_pattern = Module._ts_query_start_byte_for_pattern = wasmExports.ts_query_start_byte_for_pattern,
        _ts_query_end_byte_for_pattern = Module._ts_query_end_byte_for_pattern = wasmExports.ts_query_end_byte_for_pattern,
        _ts_query_is_pattern_rooted = Module._ts_query_is_pattern_rooted = wasmExports.ts_query_is_pattern_rooted,
        _ts_query_is_pattern_non_local = Module._ts_query_is_pattern_non_local = wasmExports.ts_query_is_pattern_non_local,
        _ts_query_is_pattern_guaranteed_at_step = Module._ts_query_is_pattern_guaranteed_at_step = wasmExports.ts_query_is_pattern_guaranteed_at_step,
        _ts_query_disable_capture = Module._ts_query_disable_capture = wasmExports.ts_query_disable_capture,
        _ts_query_disable_pattern = Module._ts_query_disable_pattern = wasmExports.ts_query_disable_pattern,
        _ts_tree_copy = Module._ts_tree_copy = wasmExports.ts_tree_copy,
        _ts_tree_delete = Module._ts_tree_delete = wasmExports.ts_tree_delete,
        _ts_init = Module._ts_init = wasmExports.ts_init,
        _ts_parser_new_wasm = Module._ts_parser_new_wasm = wasmExports.ts_parser_new_wasm,
        _ts_parser_enable_logger_wasm = Module._ts_parser_enable_logger_wasm = wasmExports.ts_parser_enable_logger_wasm,
        _ts_parser_parse_wasm = Module._ts_parser_parse_wasm = wasmExports.ts_parser_parse_wasm,
        _ts_parser_included_ranges_wasm = Module._ts_parser_included_ranges_wasm = wasmExports.ts_parser_included_ranges_wasm,
        _ts_language_type_is_named_wasm = Module._ts_language_type_is_named_wasm = wasmExports.ts_language_type_is_named_wasm,
        _ts_language_type_is_visible_wasm = Module._ts_language_type_is_visible_wasm = wasmExports.ts_language_type_is_visible_wasm,
        _ts_language_supertypes_wasm = Module._ts_language_supertypes_wasm = wasmExports.ts_language_supertypes_wasm,
        _ts_language_subtypes_wasm = Module._ts_language_subtypes_wasm = wasmExports.ts_language_subtypes_wasm,
        _ts_tree_root_node_wasm = Module._ts_tree_root_node_wasm = wasmExports.ts_tree_root_node_wasm,
        _ts_tree_root_node_with_offset_wasm = Module._ts_tree_root_node_with_offset_wasm = wasmExports.ts_tree_root_node_with_offset_wasm,
        _ts_tree_edit_wasm = Module._ts_tree_edit_wasm = wasmExports.ts_tree_edit_wasm,
        _ts_tree_included_ranges_wasm = Module._ts_tree_included_ranges_wasm = wasmExports.ts_tree_included_ranges_wasm,
        _ts_tree_get_changed_ranges_wasm = Module._ts_tree_get_changed_ranges_wasm = wasmExports.ts_tree_get_changed_ranges_wasm,
        _ts_tree_cursor_new_wasm = Module._ts_tree_cursor_new_wasm = wasmExports.ts_tree_cursor_new_wasm,
        _ts_tree_cursor_copy_wasm = Module._ts_tree_cursor_copy_wasm = wasmExports.ts_tree_cursor_copy_wasm,
        _ts_tree_cursor_delete_wasm = Module._ts_tree_cursor_delete_wasm = wasmExports.ts_tree_cursor_delete_wasm,
        _ts_tree_cursor_reset_wasm = Module._ts_tree_cursor_reset_wasm = wasmExports.ts_tree_cursor_reset_wasm,
        _ts_tree_cursor_reset_to_wasm = Module._ts_tree_cursor_reset_to_wasm = wasmExports.ts_tree_cursor_reset_to_wasm,
        _ts_tree_cursor_goto_first_child_wasm = Module._ts_tree_cursor_goto_first_child_wasm = wasmExports.ts_tree_cursor_goto_first_child_wasm,
        _ts_tree_cursor_goto_last_child_wasm = Module._ts_tree_cursor_goto_last_child_wasm = wasmExports.ts_tree_cursor_goto_last_child_wasm,
        _ts_tree_cursor_goto_first_child_for_index_wasm = Module._ts_tree_cursor_goto_first_child_for_index_wasm = wasmExports.ts_tree_cursor_goto_first_child_for_index_wasm,
        _ts_tree_cursor_goto_first_child_for_position_wasm = Module._ts_tree_cursor_goto_first_child_for_position_wasm = wasmExports.ts_tree_cursor_goto_first_child_for_position_wasm,
        _ts_tree_cursor_goto_next_sibling_wasm = Module._ts_tree_cursor_goto_next_sibling_wasm = wasmExports.ts_tree_cursor_goto_next_sibling_wasm,
        _ts_tree_cursor_goto_previous_sibling_wasm = Module._ts_tree_cursor_goto_previous_sibling_wasm = wasmExports.ts_tree_cursor_goto_previous_sibling_wasm,
        _ts_tree_cursor_goto_descendant_wasm = Module._ts_tree_cursor_goto_descendant_wasm = wasmExports.ts_tree_cursor_goto_descendant_wasm,
        _ts_tree_cursor_goto_parent_wasm = Module._ts_tree_cursor_goto_parent_wasm = wasmExports.ts_tree_cursor_goto_parent_wasm,
        _ts_tree_cursor_current_node_type_id_wasm = Module._ts_tree_cursor_current_node_type_id_wasm = wasmExports.ts_tree_cursor_current_node_type_id_wasm,
        _ts_tree_cursor_current_node_state_id_wasm = Module._ts_tree_cursor_current_node_state_id_wasm = wasmExports.ts_tree_cursor_current_node_state_id_wasm,
        _ts_tree_cursor_current_node_is_named_wasm = Module._ts_tree_cursor_current_node_is_named_wasm = wasmExports.ts_tree_cursor_current_node_is_named_wasm,
        _ts_tree_cursor_current_node_is_missing_wasm = Module._ts_tree_cursor_current_node_is_missing_wasm = wasmExports.ts_tree_cursor_current_node_is_missing_wasm,
        _ts_tree_cursor_current_node_id_wasm = Module._ts_tree_cursor_current_node_id_wasm = wasmExports.ts_tree_cursor_current_node_id_wasm,
        _ts_tree_cursor_start_position_wasm = Module._ts_tree_cursor_start_position_wasm = wasmExports.ts_tree_cursor_start_position_wasm,
        _ts_tree_cursor_end_position_wasm = Module._ts_tree_cursor_end_position_wasm = wasmExports.ts_tree_cursor_end_position_wasm,
        _ts_tree_cursor_start_index_wasm = Module._ts_tree_cursor_start_index_wasm = wasmExports.ts_tree_cursor_start_index_wasm,
        _ts_tree_cursor_end_index_wasm = Module._ts_tree_cursor_end_index_wasm = wasmExports.ts_tree_cursor_end_index_wasm,
        _ts_tree_cursor_current_field_id_wasm = Module._ts_tree_cursor_current_field_id_wasm = wasmExports.ts_tree_cursor_current_field_id_wasm,
        _ts_tree_cursor_current_depth_wasm = Module._ts_tree_cursor_current_depth_wasm = wasmExports.ts_tree_cursor_current_depth_wasm,
        _ts_tree_cursor_current_descendant_index_wasm = Module._ts_tree_cursor_current_descendant_index_wasm = wasmExports.ts_tree_cursor_current_descendant_index_wasm,
        _ts_tree_cursor_current_node_wasm = Module._ts_tree_cursor_current_node_wasm = wasmExports.ts_tree_cursor_current_node_wasm,
        _ts_node_symbol_wasm = Module._ts_node_symbol_wasm = wasmExports.ts_node_symbol_wasm,
        _ts_node_field_name_for_child_wasm = Module._ts_node_field_name_for_child_wasm = wasmExports.ts_node_field_name_for_child_wasm,
        _ts_node_field_name_for_named_child_wasm = Module._ts_node_field_name_for_named_child_wasm = wasmExports.ts_node_field_name_for_named_child_wasm,
        _ts_node_children_by_field_id_wasm = Module._ts_node_children_by_field_id_wasm = wasmExports.ts_node_children_by_field_id_wasm,
        _ts_node_first_child_for_byte_wasm = Module._ts_node_first_child_for_byte_wasm = wasmExports.ts_node_first_child_for_byte_wasm,
        _ts_node_first_named_child_for_byte_wasm = Module._ts_node_first_named_child_for_byte_wasm = wasmExports.ts_node_first_named_child_for_byte_wasm,
        _ts_node_grammar_symbol_wasm = Module._ts_node_grammar_symbol_wasm = wasmExports.ts_node_grammar_symbol_wasm,
        _ts_node_child_count_wasm = Module._ts_node_child_count_wasm = wasmExports.ts_node_child_count_wasm,
        _ts_node_named_child_count_wasm = Module._ts_node_named_child_count_wasm = wasmExports.ts_node_named_child_count_wasm,
        _ts_node_child_wasm = Module._ts_node_child_wasm = wasmExports.ts_node_child_wasm,
        _ts_node_named_child_wasm = Module._ts_node_named_child_wasm = wasmExports.ts_node_named_child_wasm,
        _ts_node_child_by_field_id_wasm = Module._ts_node_child_by_field_id_wasm = wasmExports.ts_node_child_by_field_id_wasm,
        _ts_node_next_sibling_wasm = Module._ts_node_next_sibling_wasm = wasmExports.ts_node_next_sibling_wasm,
        _ts_node_prev_sibling_wasm = Module._ts_node_prev_sibling_wasm = wasmExports.ts_node_prev_sibling_wasm,
        _ts_node_next_named_sibling_wasm = Module._ts_node_next_named_sibling_wasm = wasmExports.ts_node_next_named_sibling_wasm,
        _ts_node_prev_named_sibling_wasm = Module._ts_node_prev_named_sibling_wasm = wasmExports.ts_node_prev_named_sibling_wasm,
        _ts_node_descendant_count_wasm = Module._ts_node_descendant_count_wasm = wasmExports.ts_node_descendant_count_wasm,
        _ts_node_parent_wasm = Module._ts_node_parent_wasm = wasmExports.ts_node_parent_wasm,
        _ts_node_child_with_descendant_wasm = Module._ts_node_child_with_descendant_wasm = wasmExports.ts_node_child_with_descendant_wasm,
        _ts_node_descendant_for_index_wasm = Module._ts_node_descendant_for_index_wasm = wasmExports.ts_node_descendant_for_index_wasm,
        _ts_node_named_descendant_for_index_wasm = Module._ts_node_named_descendant_for_index_wasm = wasmExports.ts_node_named_descendant_for_index_wasm,
        _ts_node_descendant_for_position_wasm = Module._ts_node_descendant_for_position_wasm = wasmExports.ts_node_descendant_for_position_wasm,
        _ts_node_named_descendant_for_position_wasm = Module._ts_node_named_descendant_for_position_wasm = wasmExports.ts_node_named_descendant_for_position_wasm,
        _ts_node_start_point_wasm = Module._ts_node_start_point_wasm = wasmExports.ts_node_start_point_wasm,
        _ts_node_end_point_wasm = Module._ts_node_end_point_wasm = wasmExports.ts_node_end_point_wasm,
        _ts_node_start_index_wasm = Module._ts_node_start_index_wasm = wasmExports.ts_node_start_index_wasm,
        _ts_node_end_index_wasm = Module._ts_node_end_index_wasm = wasmExports.ts_node_end_index_wasm,
        _ts_node_to_string_wasm = Module._ts_node_to_string_wasm = wasmExports.ts_node_to_string_wasm,
        _ts_node_children_wasm = Module._ts_node_children_wasm = wasmExports.ts_node_children_wasm,
        _ts_node_named_children_wasm = Module._ts_node_named_children_wasm = wasmExports.ts_node_named_children_wasm,
        _ts_node_descendants_of_type_wasm = Module._ts_node_descendants_of_type_wasm = wasmExports.ts_node_descendants_of_type_wasm,
        _ts_node_is_named_wasm = Module._ts_node_is_named_wasm = wasmExports.ts_node_is_named_wasm,
        _ts_node_has_changes_wasm = Module._ts_node_has_changes_wasm = wasmExports.ts_node_has_changes_wasm,
        _ts_node_has_error_wasm = Module._ts_node_has_error_wasm = wasmExports.ts_node_has_error_wasm,
        _ts_node_is_error_wasm = Module._ts_node_is_error_wasm = wasmExports.ts_node_is_error_wasm,
        _ts_node_is_missing_wasm = Module._ts_node_is_missing_wasm = wasmExports.ts_node_is_missing_wasm,
        _ts_node_is_extra_wasm = Module._ts_node_is_extra_wasm = wasmExports.ts_node_is_extra_wasm,
        _ts_node_parse_state_wasm = Module._ts_node_parse_state_wasm = wasmExports.ts_node_parse_state_wasm,
        _ts_node_next_parse_state_wasm = Module._ts_node_next_parse_state_wasm = wasmExports.ts_node_next_parse_state_wasm,
        _ts_query_matches_wasm = Module._ts_query_matches_wasm = wasmExports.ts_query_matches_wasm,
        _ts_query_captures_wasm = Module._ts_query_captures_wasm = wasmExports.ts_query_captures_wasm,
        _memset = Module._memset = wasmExports.memset,
        _memcpy = Module._memcpy = wasmExports.memcpy,
        _memmove = Module._memmove = wasmExports.memmove,
        _iswalpha = Module._iswalpha = wasmExports.iswalpha,
        _iswblank = Module._iswblank = wasmExports.iswblank,
        _iswdigit = Module._iswdigit = wasmExports.iswdigit,
        _iswlower = Module._iswlower = wasmExports.iswlower,
        _iswupper = Module._iswupper = wasmExports.iswupper,
        _iswxdigit = Module._iswxdigit = wasmExports.iswxdigit,
        _memchr = Module._memchr = wasmExports.memchr,
        _strlen = Module._strlen = wasmExports.strlen,
        _strcmp = Module._strcmp = wasmExports.strcmp,
        _strncat = Module._strncat = wasmExports.strncat,
        _strncpy = Module._strncpy = wasmExports.strncpy,
        _towlower = Module._towlower = wasmExports.towlower,
        _towupper = Module._towupper = wasmExports.towupper,
        _setThrew = wasmExports.setThrew,
        __emscripten_stack_restore = wasmExports._emscripten_stack_restore,
        __emscripten_stack_alloc = wasmExports._emscripten_stack_alloc,
        _emscripten_stack_get_current = wasmExports.emscripten_stack_get_current,
        ___wasm_apply_data_relocs = wasmExports.__wasm_apply_data_relocs;
      Module.setValue = setValue, Module.getValue = getValue, Module.UTF8ToString = UTF8ToString, Module.stringToUTF8 = stringToUTF8, Module.lengthBytesUTF8 = lengthBytesUTF8, Module.AsciiToString = AsciiToString, Module.stringToUTF16 = stringToUTF16, Module.loadWebAssemblyModule = loadWebAssemblyModule;
      function callMain(A = []) {
        var B = resolveGlobalSymbol("main").sym;
        if (!B) return;
        A.unshift(thisProgram);
        var Q = A.length,
          D = stackAlloc((Q + 1) * 4),
          Z = D;
        A.forEach(F => {
          LE_HEAP_STORE_U32((Z >> 2) * 4, stringToUTF8OnStack(F)), Z += 4;
        }), LE_HEAP_STORE_U32((Z >> 2) * 4, 0);
        try {
          var G = B(Q, D);
          return exitJS(G, !0), G;
        } catch (F) {
          return handleException(F);
        }
      }
      p0(callMain, "callMain");
      function run(A = arguments_) {
        if (runDependencies > 0) {
          dependenciesFulfilled = run;
          return;
        }
        if (preRun(), runDependencies > 0) {
          dependenciesFulfilled = run;
          return;
        }
        function B() {
          if (Module.calledRun = !0, ABORT) return;
          initRuntime(), preMain(), readyPromiseResolve(Module), Module.onRuntimeInitialized?.();
          var Q = Module.noInitialRun;
          if (!Q) callMain(A);
          postRun();
        }
        if (p0(B, "doRun"), Module.setStatus) Module.setStatus("Running..."), setTimeout(() => {
          setTimeout(() => Module.setStatus(""), 1), B();
        }, 1);else B();
      }
      if (p0(run, "run"), Module.preInit) {
        if (typeof Module.preInit == "function") Module.preInit = [Module.preInit];
        while (Module.preInit.length > 0) Module.preInit.pop()();
      }
      return run(), moduleRtn = readyPromise, moduleRtn;
    };
  })(),
  C48 = V48,
  rx1 = null;
async function bLB(A) {
  if (!rx1) rx1 = await C48(A);
  return rx1;
}
function fLB() {
  return !!rx1;
}
var xB,
  z$0,
  E$0,
  K48 = class {
    static {
      p0(this, "Parser");
    }
    [0] = 0;
    [1] = 0;
    logCallback = null;
    language = null;
    static async init(A) {
      RLB(await bLB(A)), xB = a1._ts_init(), z$0 = a1.getValue(xB, "i32"), E$0 = a1.getValue(xB + H9, "i32");
    }
    constructor() {
      this.initialize();
    }
    initialize() {
      if (!fLB()) throw new Error("cannot construct a Parser before calling `init()`");
      a1._ts_parser_new_wasm(), this[0] = a1.getValue(xB, "i32"), this[1] = a1.getValue(xB + H9, "i32");
    }
    delete() {
      a1._ts_parser_delete(this[0]), a1._free(this[1]), this[0] = 0, this[1] = 0;
    }
    setLanguage(A) {
      let B;
      if (!A) B = 0, this.language = null;else if (A.constructor === vLB) {
        B = A[0];
        let Q = a1._ts_language_version(B);
        if (Q < E$0 || z$0 < Q) throw new Error(`Incompatible language version ${Q}. Compatibility range ${E$0} through ${z$0}.`);
        this.language = A;
      } else throw new Error("Argument must be a Language");
      return a1._ts_parser_set_language(this[0], B), this;
    }
    parse(A, B, Q) {
      if (typeof A === "string") a1.currentParseCallback = I => A.slice(I);else if (typeof A === "function") a1.currentParseCallback = A;else throw new Error("Argument must be a string or a function");
      if (Q?.progressCallback) a1.currentProgressCallback = Q.progressCallback;else a1.currentProgressCallback = null;
      if (this.logCallback) a1.currentLogCallback = this.logCallback, a1._ts_parser_enable_logger_wasm(this[0], 1);else a1.currentLogCallback = null, a1._ts_parser_enable_logger_wasm(this[0], 0);
      let D = 0,
        Z = 0;
      if (Q?.includedRanges) {
        D = Q.includedRanges.length, Z = a1._calloc(D, bZ1);
        let I = Z;
        for (let Y = 0; Y < D; Y++) OLB(I, Q.includedRanges[Y]), I += bZ1;
      }
      let G = a1._ts_parser_parse_wasm(this[0], this[1], B ? B[0] : 0, Z, D);
      if (!G) return a1.currentParseCallback = null, a1.currentLogCallback = null, a1.currentProgressCallback = null, null;
      if (!this.language) throw new Error("Parser must have a language to parse");
      let F = new Z48(Dv, G, this.language, a1.currentParseCallback);
      return a1.currentParseCallback = null, a1.currentLogCallback = null, a1.currentProgressCallback = null, F;
    }
    reset() {
      a1._ts_parser_reset(this[0]);
    }
    getIncludedRanges() {
      a1._ts_parser_included_ranges_wasm(this[0]);
      let A = a1.getValue(xB, "i32"),
        B = a1.getValue(xB + H9, "i32"),
        Q = new Array(A);
      if (A > 0) {
        let D = B;
        for (let Z = 0; Z < A; Z++) Q[Z] = ox1(D), D += bZ1;
        a1._free(B);
      }
      return Q;
    }
    getTimeoutMicros() {
      return a1._ts_parser_timeout_micros(this[0]);
    }
    setTimeoutMicros(A) {
      a1._ts_parser_set_timeout_micros(this[0], 0, A);
    }
    setLogger(A) {
      if (!A) this.logCallback = null;else if (typeof A !== "function") throw new Error("Logger callback must be a function");else this.logCallback = A;
      return this;
    }
    getLogger() {
      return this.logCallback;
    }
  };
module.exports = {
  k$,
  p0
};