/**
 * Isolated bundle for 'eK0'
 */

var B7B = F1(Dm1(), 1);
import Ry1 from "node:process";
import { PassThrough as Ok6 } from "node:stream";
class PD1 {
  append(A) {
    this._buffer = this._buffer ? Buffer.concat([this._buffer, A]) : A;
  }
  readMessage() {
    if (!this._buffer) return null;
    let A = this._buffer.indexOf(`
`);
    if (A === -1) return null;
    let B = this._buffer.toString("utf8", 0, A).replace(/\r$/, "");
    return this._buffer = this._buffer.subarray(A + 1), Rk6(B);
  }
  clear() {
    this._buffer = void 0;
  }
}
function Rk6(A) {
  return OM.parse(JSON.parse(A));
}
function My1(A) {
  return JSON.stringify(A) + `
`;
}
var Tk6 = Ry1.platform === "win32" ? ["APPDATA", "HOMEDRIVE", "HOMEPATH", "LOCALAPPDATA", "PATH", "PROCESSOR_ARCHITECTURE", "SYSTEMDRIVE", "SYSTEMROOT", "TEMP", "USERNAME", "USERPROFILE", "PROGRAMFILES"] : ["HOME", "LOGNAME", "PATH", "SHELL", "TERM", "USER"];
function Pk6() {
  let A = {};
  for (let B of Tk6) {
    let Q = Ry1.env[B];
    if (Q === void 0) continue;
    if (Q.startsWith("()")) continue;
    A[B] = Q;
  }
  return A;
}
class eK0 {
  constructor(A) {
    if (this._abortController = new AbortController(), this._readBuffer = new PD1(), this._stderrStream = null, this._serverParams = A, A.stderr === "pipe" || A.stderr === "overlapped") this._stderrStream = new Ok6();
  }
  async start() {
    if (this._process) throw new Error("StdioClientTransport already started! If using Client class, note that connect() calls start() automatically.");
    return new Promise((A, B) => {
      var Q, D, Z, G, F, I;
      if (this._process = B7B.default(this._serverParams.command, (Q = this._serverParams.args) !== null && Q !== void 0 ? Q : [], {
        env: (D = this._serverParams.env) !== null && D !== void 0 ? D : Pk6(),
        stdio: ["pipe", "pipe", (Z = this._serverParams.stderr) !== null && Z !== void 0 ? Z : "inherit"],
        shell: !1,
        signal: this._abortController.signal,
        windowsHide: Ry1.platform === "win32" && Sk6(),
        cwd: this._serverParams.cwd
      }), this._process.on("error", Y => {
        var W, J;
        if (Y.name === "AbortError") {
          (W = this.onclose) === null || W === void 0 || W.call(this);
          return;
        }
        B(Y), (J = this.onerror) === null || J === void 0 || J.call(this, Y);
      }), this._process.on("spawn", () => {
        A();
      }), this._process.on("close", Y => {
        var W;
        this._process = void 0, (W = this.onclose) === null || W === void 0 || W.call(this);
      }), (G = this._process.stdin) === null || G === void 0 || G.on("error", Y => {
        var W;
        (W = this.onerror) === null || W === void 0 || W.call(this, Y);
      }), (F = this._process.stdout) === null || F === void 0 || F.on("data", Y => {
        this._readBuffer.append(Y), this.processReadBuffer();
      }), (I = this._process.stdout) === null || I === void 0 || I.on("error", Y => {
        var W;
        (W = this.onerror) === null || W === void 0 || W.call(this, Y);
      }), this._stderrStream && this._process.stderr) this._process.stderr.pipe(this._stderrStream);
    });
  }
  get stderr() {
    var A, B;
    if (this._stderrStream) return this._stderrStream;
    return (B = (A = this._process) === null || A === void 0 ? void 0 : A.stderr) !== null && B !== void 0 ? B : null;
  }
  get pid() {
    var A, B;
    return (B = (A = this._process) === null || A === void 0 ? void 0 : A.pid) !== null && B !== void 0 ? B : null;
  }
  processReadBuffer() {
    var A, B;
    while (!0) try {
      let Q = this._readBuffer.readMessage();
      if (Q === null) break;
      (A = this.onmessage) === null || A === void 0 || A.call(this, Q);
    } catch (Q) {
      (B = this.onerror) === null || B === void 0 || B.call(this, Q);
    }
  }
  async close() {
    this._abortController.abort(), this._process = void 0, this._readBuffer.clear();
  }
  send(A) {
    return new Promise(B => {
      var Q;
      if (!((Q = this._process) === null || Q === void 0 ? void 0 : Q.stdin)) throw new Error("Not connected");
      let D = My1(A);
      if (this._process.stdin.write(D)) B();else this._process.stdin.once("drain", B);
    });
  }
}
function Sk6() {
  return "type" in Ry1;
}
module.exports = {
  My1,
  PD1,
  eK0
};