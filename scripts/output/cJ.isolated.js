/**
 * Isolated bundle for 'cJ'
 */

/**
 * Cleaned Main File
 */

const {
  $W1,
  $Y1,
  $r1,
  $tB,
  A$2,
  A0,
  AI,
  AS0,
  AV,
  Af,
  ArB,
  Au,
  B9,
  BO,
  BW1,
  Bh,
  Bk0,
  BrB,
  C1,
  C5Q,
  C9,
  CK,
  CS0,
  Cr1,
  CsB,
  Dm1,
  DrB,
  Ds4,
  E0,
  E51,
  EI,
  EY1,
  E_,
  F1,
  F9,
  FD,
  FH1,
  FS0,
  Fm1,
  FsA,
  FsB,
  GD,
  GP,
  HM2,
  HeB,
  I0,
  IG,
  IS0,
  Ig,
  Im1,
  IsB,
  Iu,
  J5Q,
  Jm1,
  Jp,
  Js4,
  LD,
  LY,
  Li4,
  Lj0,
  LtB,
  MA,
  MK1,
  MtB,
  N$2,
  NS0,
  NY,
  Ni4,
  NtB,
  OS0,
  OT,
  Oj,
  On0,
  Oo0,
  Os1,
  Ou1,
  P,
  P$2,
  P9,
  P91,
  PS0,
  Pc,
  Pj,
  Pm1,
  QO1,
  QP,
  Qk0,
  QrB,
  R2,
  RS0,
  RT,
  Rc,
  Rj,
  RtB,
  S$2,
  SA,
  SB1,
  Sk0,
  Sm1,
  T1,
  T3,
  T7,
  T91,
  TL2,
  TS0,
  UK1,
  UY,
  UY1,
  U_,
  V5Q,
  Vp,
  Vr1,
  WV,
  Wg,
  Wo,
  X2,
  X5Q,
  XF0,
  XO1,
  Xs4,
  YM2,
  YQ,
  Yg,
  YsB,
  Zs4,
  _K1,
  _N,
  _Y1,
  _j,
  a0,
  a81,
  aA1,
  aZ0,
  ar1,
  asB,
  b91,
  bC,
  bH,
  bO,
  bl4,
  bo0,
  c91,
  cA1,
  cM1,
  cT0,
  cY1,
  c_9,
  cc,
  cq,
  dA1,
  dY1,
  dq,
  eP0,
  eR,
  eY1,
  eYA,
  eZ0,
  eb,
  esB,
  ew2,
  f4,
  f41,
  fH,
  fV1,
  fY1,
  fr1,
  g,
  gQ,
  gY1,
  gc,
  hV1,
  hY1,
  hc,
  hy,
  i2,
  iM1,
  iY1,
  ic,
  iq,
  isB,
  j5,
  jK1,
  jj,
  jm1,
  jtB,
  kM1,
  kN,
  kS0,
  k_,
  kl,
  ko0,
  kw,
  lB,
  lw2,
  mV1,
  mY,
  mY1,
  mq,
  nM1,
  nY1,
  nZ0,
  nc,
  nq,
  nsB,
  o3,
  oY1,
  oYA,
  or,
  osB,
  p2,
  p91,
  pA,
  pY1,
  pZ,
  pc,
  pq,
  psB,
  q8,
  qQ0,
  qYA,
  qtB,
  r81,
  rG,
  rsB,
  sB,
  sc,
  ssB,
  sw2,
  t9,
  tC,
  tJ,
  tR,
  tsB,
  uD,
  uL,
  v41,
  v9,
  vG,
  vH,
  vM1,
  vN,
  vj,
  vl,
  vo0,
  w1,
  wS0,
  wV,
  wY1,
  x,
  x1,
  x41,
  x8,
  x9,
  xH,
  xV,
  xi,
  xj,
  xu1,
  xz,
  yB1,
  yj,
  yl,
  zeB,
  zw
} = require("./q8.isolated.js");
// (c) Anthropic PBC. All rights reserved. Use is subject to Anthropic's Commercial Terms of Service (https://www.anthropic.com/legal/commercial-terms).

// Version: 1.0.72

const {
  Bt0,
  no0
} = require("./Bt0_no0.js");
var Wt0 = F1(Bt0(), 1);
var _B1 = A => {
  if (typeof A !== "string") throw new TypeError("invalid pattern");
  if (A.length > 65536) throw new TypeError("pattern is too long");
};
var $x9 = {
    "[:alnum:]": ["\\p{L}\\p{Nl}\\p{Nd}", !0],
    "[:alpha:]": ["\\p{L}\\p{Nl}", !0],
    "[:ascii:]": ["\\x00-\\x7f", !1],
    "[:blank:]": ["\\p{Zs}\\t", !0],
    "[:cntrl:]": ["\\p{Cc}", !0],
    "[:digit:]": ["\\p{Nd}", !0],
    "[:graph:]": ["\\p{Z}\\p{C}", !0, !0],
    "[:lower:]": ["\\p{Ll}", !0],
    "[:print:]": ["\\p{C}", !0],
    "[:punct:]": ["\\p{P}", !0],
    "[:space:]": ["\\p{Z}\\t\\r\\n\\v\\f", !0],
    "[:upper:]": ["\\p{Lu}", !0],
    "[:word:]": ["\\p{L}\\p{Nl}\\p{Nd}\\p{Pc}", !0],
    "[:xdigit:]": ["A-Fa-f0-9", !1]
  },
  xB1 = A => A.replace(/[[\]\\-]/g, "\\$&"),
  qx9 = A => A.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, "\\$&"),
  Qt0 = A => A.join(""),
  Dt0 = (A, B) => {
    let Q = B;
    if (A.charAt(Q) !== "[") throw new Error("not in a brace expression");
    let D = [],
      Z = [],
      G = Q + 1,
      F = !1,
      I = !1,
      Y = !1,
      W = !1,
      J = Q,
      X = "";
    A: while (G < A.length) {
      let H = A.charAt(G);
      if ((H === "!" || H === "^") && G === Q + 1) {
        W = !0, G++;
        continue;
      }
      if (H === "]" && F && !Y) {
        J = G + 1;
        break;
      }
      if (F = !0, H === "\\") {
        if (!Y) {
          Y = !0, G++;
          continue;
        }
      }
      if (H === "[" && !Y) {
        for (let [z, [$, L, N]] of Object.entries($x9)) if (A.startsWith(z, G)) {
          if (X) return ["$.", !1, A.length - Q, !0];
          if (G += z.length, N) Z.push($);else D.push($);
          I = I || L;
          continue A;
        }
      }
      if (Y = !1, X) {
        if (H > X) D.push(xB1(X) + "-" + xB1(H));else if (H === X) D.push(xB1(H));
        X = "", G++;
        continue;
      }
      if (A.startsWith("-]", G + 1)) {
        D.push(xB1(H + "-")), G += 2;
        continue;
      }
      if (A.startsWith("-", G + 1)) {
        X = H, G += 2;
        continue;
      }
      D.push(xB1(H)), G++;
    }
    if (J < G) return ["", !1, 0, !1];
    if (!D.length && !Z.length) return ["$.", !1, A.length - Q, !0];
    if (Z.length === 0 && D.length === 1 && /^\\?.$/.test(D[0]) && !W) {
      let H = D[0].length === 2 ? D[0].slice(-1) : D[0];
      return [qx9(H), !1, J - Q, !1];
    }
    let V = "[" + (W ? "^" : "") + Qt0(D) + "]",
      C = "[" + (W ? "" : "^") + Qt0(Z) + "]";
    return [D.length && Z.length ? "(" + V + "|" + C + ")" : D.length ? V : C, I, J - Q, !0];
  };
var Iw = (A, {
  windowsPathsNoEscape: B = !1
} = {}) => {
  return B ? A.replace(/\[([^\/\\])\]/g, "$1") : A.replace(/((?!\\).|^)\[([^\/\\])\]/g, "$1$2").replace(/\\([^\/])/g, "$1");
};
var Nx9 = new Set(["!", "?", "+", "*", "@"]),
  Zt0 = A => Nx9.has(A),
  Lx9 = "(?!(?:^|/)\\.\\.?(?:$|/))",
  dV1 = "(?!\\.)",
  Mx9 = new Set(["[", "."]),
  Rx9 = new Set(["..", "."]),
  Ox9 = new Set("().*{}+?[]^$\\!"),
  Tx9 = A => A.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, "\\$&"),
  bi1 = "[^/]",
  Gt0 = bi1 + "*?",
  Ft0 = bi1 + "+?";
class CY {
  type;
  #A;
  #B;
  #Q = !1;
  #D = [];
  #Z;
  #Y;
  #G;
  #J = !1;
  #W;
  #X;
  #I = !1;
  constructor(A, B, Q = {}) {
    if (this.type = A, A) this.#B = !0;
    if (this.#Z = B, this.#A = this.#Z ? this.#Z.#A : this, this.#W = this.#A === this ? Q : this.#A.#W, this.#G = this.#A === this ? [] : this.#A.#G, A === "!" && !this.#A.#J) this.#G.push(this);
    this.#Y = this.#Z ? this.#Z.#D.length : 0;
  }
  get hasMagic() {
    if (this.#B !== void 0) return this.#B;
    for (let A of this.#D) {
      if (typeof A === "string") continue;
      if (A.type || A.hasMagic) return this.#B = !0;
    }
    return this.#B;
  }
  toString() {
    if (this.#X !== void 0) return this.#X;
    if (!this.type) return this.#X = this.#D.map(A => String(A)).join("");else return this.#X = this.type + "(" + this.#D.map(A => String(A)).join("|") + ")";
  }
  #E() {
    if (this !== this.#A) throw new Error("should only call on root");
    if (this.#J) return this;
    this.toString(), this.#J = !0;
    let A;
    while (A = this.#G.pop()) {
      if (A.type !== "!") continue;
      let B = A,
        Q = B.#Z;
      while (Q) {
        for (let D = B.#Y + 1; !Q.type && D < Q.#D.length; D++) for (let Z of A.#D) {
          if (typeof Z === "string") throw new Error("string part in extglob AST??");
          Z.copyIn(Q.#D[D]);
        }
        B = Q, Q = B.#Z;
      }
    }
    return this;
  }
  push(...A) {
    for (let B of A) {
      if (B === "") continue;
      if (typeof B !== "string" && !(B instanceof CY && B.#Z === this)) throw new Error("invalid part: " + B);
      this.#D.push(B);
    }
  }
  toJSON() {
    let A = this.type === null ? this.#D.slice().map(B => typeof B === "string" ? B : B.toJSON()) : [this.type, ...this.#D.map(B => B.toJSON())];
    if (this.isStart() && !this.type) A.unshift([]);
    if (this.isEnd() && (this === this.#A || this.#A.#J && this.#Z?.type === "!")) A.push({});
    return A;
  }
  isStart() {
    if (this.#A === this) return !0;
    if (!this.#Z?.isStart()) return !1;
    if (this.#Y === 0) return !0;
    let A = this.#Z;
    for (let B = 0; B < this.#Y; B++) {
      let Q = A.#D[B];
      if (!(Q instanceof CY && Q.type === "!")) return !1;
    }
    return !0;
  }
  isEnd() {
    if (this.#A === this) return !0;
    if (this.#Z?.type === "!") return !0;
    if (!this.#Z?.isEnd()) return !1;
    if (!this.type) return this.#Z?.isEnd();
    let A = this.#Z ? this.#Z.#D.length : 0;
    return this.#Y === A - 1;
  }
  copyIn(A) {
    if (typeof A === "string") this.push(A);else this.push(A.clone(this));
  }
  clone(A) {
    let B = new CY(this.type, A);
    for (let Q of this.#D) B.copyIn(Q);
    return B;
  }
  static #U(A, B, Q, D) {
    let Z = !1,
      G = !1,
      F = -1,
      I = !1;
    if (B.type === null) {
      let V = Q,
        C = "";
      while (V < A.length) {
        let K = A.charAt(V++);
        if (Z || K === "\\") {
          Z = !Z, C += K;
          continue;
        }
        if (G) {
          if (V === F + 1) {
            if (K === "^" || K === "!") I = !0;
          } else if (K === "]" && !(V === F + 2 && I)) G = !1;
          C += K;
          continue;
        } else if (K === "[") {
          G = !0, F = V, I = !1, C += K;
          continue;
        }
        if (!D.noext && Zt0(K) && A.charAt(V) === "(") {
          B.push(C), C = "";
          let H = new CY(K, B);
          V = CY.#U(A, H, V, D), B.push(H);
          continue;
        }
        C += K;
      }
      return B.push(C), V;
    }
    let Y = Q + 1,
      W = new CY(null, B),
      J = [],
      X = "";
    while (Y < A.length) {
      let V = A.charAt(Y++);
      if (Z || V === "\\") {
        Z = !Z, X += V;
        continue;
      }
      if (G) {
        if (Y === F + 1) {
          if (V === "^" || V === "!") I = !0;
        } else if (V === "]" && !(Y === F + 2 && I)) G = !1;
        X += V;
        continue;
      } else if (V === "[") {
        G = !0, F = Y, I = !1, X += V;
        continue;
      }
      if (Zt0(V) && A.charAt(Y) === "(") {
        W.push(X), X = "";
        let C = new CY(V, W);
        W.push(C), Y = CY.#U(A, C, Y, D);
        continue;
      }
      if (V === "|") {
        W.push(X), X = "", J.push(W), W = new CY(null, B);
        continue;
      }
      if (V === ")") {
        if (X === "" && B.#D.length === 0) B.#I = !0;
        return W.push(X), X = "", B.push(...J, W), Y;
      }
      X += V;
    }
    return B.type = null, B.#B = void 0, B.#D = [A.substring(Q - 1)], Y;
  }
  static fromGlob(A, B = {}) {
    let Q = new CY(null, void 0, B);
    return CY.#U(A, Q, 0, B), Q;
  }
  toMMPattern() {
    if (this !== this.#A) return this.#A.toMMPattern();
    let A = this.toString(),
      [B, Q, D, Z] = this.toRegExpSource();
    if (!(D || this.#B || this.#W.nocase && !this.#W.nocaseMagicOnly && A.toUpperCase() !== A.toLowerCase())) return Q;
    let F = (this.#W.nocase ? "i" : "") + (Z ? "u" : "");
    return Object.assign(new RegExp(`^${B}$`, F), {
      _src: B,
      _glob: A
    });
  }
  get options() {
    return this.#W;
  }
  toRegExpSource(A) {
    let B = A ?? !!this.#W.dot;
    if (this.#A === this) this.#E();
    if (!this.type) {
      let I = this.isStart() && this.isEnd(),
        Y = this.#D.map(V => {
          let [C, K, H, z] = typeof V === "string" ? CY.#C(V, this.#B, I) : V.toRegExpSource(A);
          return this.#B = this.#B || H, this.#Q = this.#Q || z, C;
        }).join(""),
        W = "";
      if (this.isStart()) {
        if (typeof this.#D[0] === "string") {
          if (!(this.#D.length === 1 && Rx9.has(this.#D[0]))) {
            let C = Mx9,
              K = B && C.has(Y.charAt(0)) || Y.startsWith("\\.") && C.has(Y.charAt(2)) || Y.startsWith("\\.\\.") && C.has(Y.charAt(4)),
              H = !B && !A && C.has(Y.charAt(0));
            W = K ? Lx9 : H ? dV1 : "";
          }
        }
      }
      let J = "";
      if (this.isEnd() && this.#A.#J && this.#Z?.type === "!") J = "(?:$|\\/)";
      return [W + Y + J, Iw(Y), this.#B = !!this.#B, this.#Q];
    }
    let Q = this.type === "*" || this.type === "+",
      D = this.type === "!" ? "(?:(?!(?:" : "(?:",
      Z = this.#K(B);
    if (this.isStart() && this.isEnd() && !Z && this.type !== "!") {
      let I = this.toString();
      return this.#D = [I], this.type = null, this.#B = void 0, [I, Iw(this.toString()), !1, !1];
    }
    let G = !Q || A || B || !dV1 ? "" : this.#K(!0);
    if (G === Z) G = "";
    if (G) Z = `(?:${Z})(?:${G})*?`;
    let F = "";
    if (this.type === "!" && this.#I) F = (this.isStart() && !B ? dV1 : "") + Ft0;else {
      let I = this.type === "!" ? "))" + (this.isStart() && !B && !A ? dV1 : "") + Gt0 + ")" : this.type === "@" ? ")" : this.type === "?" ? ")?" : this.type === "+" && G ? ")" : this.type === "*" && G ? ")?" : `)${this.type}`;
      F = D + Z + I;
    }
    return [F, Iw(Z), this.#B = !!this.#B, this.#Q];
  }
  #K(A) {
    return this.#D.map(B => {
      if (typeof B === "string") throw new Error("string type in extglob ast??");
      let [Q, D, Z, G] = B.toRegExpSource(A);
      return this.#Q = this.#Q || G, Q;
    }).filter(B => !(this.isStart() && this.isEnd()) || !!B).join("|");
  }
  static #C(A, B, Q = !1) {
    let D = !1,
      Z = "",
      G = !1;
    for (let F = 0; F < A.length; F++) {
      let I = A.charAt(F);
      if (D) {
        D = !1, Z += (Ox9.has(I) ? "\\" : "") + I;
        continue;
      }
      if (I === "\\") {
        if (F === A.length - 1) Z += "\\\\";else D = !0;
        continue;
      }
      if (I === "[") {
        let [Y, W, J, X] = Dt0(A, F);
        if (J) {
          Z += Y, G = G || W, F += J - 1, B = B || X;
          continue;
        }
      }
      if (I === "*") {
        if (Q && A === "*") Z += Ft0;else Z += Gt0;
        B = !0;
        continue;
      }
      if (I === "?") {
        Z += bi1, B = !0;
        continue;
      }
      Z += Tx9(I);
    }
    return [Z, Iw(A), !!B, G];
  }
}
var zp = (A, {
  windowsPathsNoEscape: B = !1
} = {}) => {
  return B ? A.replace(/[?*()[\]]/g, "[$&]") : A.replace(/[?*()[\]\\]/g, "\\$&");
};
var cJ = (A, B, Q = {}) => {
    if (_B1(B), !Q.nocomment && B.charAt(0) === "#") return !1;
    return new aH(B, Q).match(A);
  },
  Px9 = /^\*+([^+@!?\*\[\(]*)$/,
  Sx9 = A => B => !B.startsWith(".") && B.endsWith(A),
  jx9 = A => B => B.endsWith(A),
  yx9 = A => {
    return A = A.toLowerCase(), B => !B.startsWith(".") && B.toLowerCase().endsWith(A);
  },
  kx9 = A => {
    return A = A.toLowerCase(), B => B.toLowerCase().endsWith(A);
  },
  _x9 = /^\*+\.\*+$/,
  xx9 = A => !A.startsWith(".") && A.includes("."),
  vx9 = A => A !== "." && A !== ".." && A.includes("."),
  bx9 = /^\.\*+$/,
  fx9 = A => A !== "." && A !== ".." && A.startsWith("."),
  hx9 = /^\*+$/,
  gx9 = A => A.length !== 0 && !A.startsWith("."),
  ux9 = A => A.length !== 0 && A !== "." && A !== "..",
  mx9 = /^\?+([^+@!?\*\[\(]*)?$/,
  dx9 = ([A, B = ""]) => {
    let Q = Jt0([A]);
    if (!B) return Q;
    return B = B.toLowerCase(), D => Q(D) && D.toLowerCase().endsWith(B);
  },
  cx9 = ([A, B = ""]) => {
    let Q = Xt0([A]);
    if (!B) return Q;
    return B = B.toLowerCase(), D => Q(D) && D.toLowerCase().endsWith(B);
  },
  lx9 = ([A, B = ""]) => {
    let Q = Xt0([A]);
    return !B ? Q : D => Q(D) && D.endsWith(B);
  },
  px9 = ([A, B = ""]) => {
    let Q = Jt0([A]);
    return !B ? Q : D => Q(D) && D.endsWith(B);
  },
  Jt0 = ([A]) => {
    let B = A.length;
    return Q => Q.length === B && !Q.startsWith(".");
  },
  Xt0 = ([A]) => {
    let B = A.length;
    return Q => Q.length === B && Q !== "." && Q !== "..";
  },
  Vt0 = typeof process === "object" && process ? typeof process.env === "object" && process.env && process.env.__MINIMATCH_TESTING_PLATFORM__ || process.platform : "posix",
  It0 = {
    win32: {
      sep: "\\"
    },
    posix: {
      sep: "/"
    }
  },
  ix9 = Vt0 === "win32" ? It0.win32.sep : It0.posix.sep;
cJ.sep = ix9;
var KY = Symbol("globstar **");
cJ.GLOBSTAR = KY;
var nx9 = "[^/]",
  ax9 = nx9 + "*?",
  sx9 = "(?:(?!(?:\\/|^)(?:\\.{1,2})($|\\/)).)*?",
  rx9 = "(?:(?!(?:\\/|^)\\.).)*?",
  ox9 = (A, B = {}) => Q => cJ(Q, A, B);
cJ.filter = ox9;
var nH = (A, B = {}) => Object.assign({}, A, B),
  tx9 = A => {
    if (!A || typeof A !== "object" || !Object.keys(A).length) return cJ;
    let B = cJ;
    return Object.assign((D, Z, G = {}) => B(D, Z, nH(A, G)), {
      Minimatch: class D extends B.Minimatch {
        constructor(Z, G = {}) {
          super(Z, nH(A, G));
        }
        static defaults(Z) {
          return B.defaults(nH(A, Z)).Minimatch;
        }
      },
      AST: class D extends B.AST {
        constructor(Z, G, F = {}) {
          super(Z, G, nH(A, F));
        }
        static fromGlob(Z, G = {}) {
          return B.AST.fromGlob(Z, nH(A, G));
        }
      },
      unescape: (D, Z = {}) => B.unescape(D, nH(A, Z)),
      escape: (D, Z = {}) => B.escape(D, nH(A, Z)),
      filter: (D, Z = {}) => B.filter(D, nH(A, Z)),
      defaults: D => B.defaults(nH(A, D)),
      makeRe: (D, Z = {}) => B.makeRe(D, nH(A, Z)),
      braceExpand: (D, Z = {}) => B.braceExpand(D, nH(A, Z)),
      match: (D, Z, G = {}) => B.match(D, Z, nH(A, G)),
      sep: B.sep,
      GLOBSTAR: KY
    });
  };
cJ.defaults = tx9;
var Ct0 = (A, B = {}) => {
  if (_B1(A), B.nobrace || !/\{(?:(?!\{).)*\}/.test(A)) return [A];
  return Wt0.default(A);
};
cJ.braceExpand = Ct0;
var ex9 = (A, B = {}) => new aH(A, B).makeRe();
cJ.makeRe = ex9;
var Av9 = (A, B, Q = {}) => {
  let D = new aH(B, Q);
  if (A = A.filter(Z => D.match(Z)), D.options.nonull && !A.length) A.push(B);
  return A;
};
cJ.match = Av9;
var Yt0 = /[?*]|[+@!]\(.*?\)|\[|\]/,
  Bv9 = A => A.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, "\\$&");
class aH {
  options;
  set;
  pattern;
  windowsPathsNoEscape;
  nonegate;
  negate;
  comment;
  empty;
  preserveMultipleSlashes;
  partial;
  globSet;
  globParts;
  nocase;
  isWindows;
  platform;
  windowsNoMagicRoot;
  regexp;
  constructor(A, B = {}) {
    if (_B1(A), B = B || {}, this.options = B, this.pattern = A, this.platform = B.platform || Vt0, this.isWindows = this.platform === "win32", this.windowsPathsNoEscape = !!B.windowsPathsNoEscape || B.allowWindowsEscape === !1, this.windowsPathsNoEscape) this.pattern = this.pattern.replace(/\\/g, "/");
    this.preserveMultipleSlashes = !!B.preserveMultipleSlashes, this.regexp = null, this.negate = !1, this.nonegate = !!B.nonegate, this.comment = !1, this.empty = !1, this.partial = !!B.partial, this.nocase = !!this.options.nocase, this.windowsNoMagicRoot = B.windowsNoMagicRoot !== void 0 ? B.windowsNoMagicRoot : !!(this.isWindows && this.nocase), this.globSet = [], this.globParts = [], this.set = [], this.make();
  }
  hasMagic() {
    if (this.options.magicalBraces && this.set.length > 1) return !0;
    for (let A of this.set) for (let B of A) if (typeof B !== "string") return !0;
    return !1;
  }
  debug(...A) {}
  make() {
    let A = this.pattern,
      B = this.options;
    if (!B.nocomment && A.charAt(0) === "#") {
      this.comment = !0;
      return;
    }
    if (!A) {
      this.empty = !0;
      return;
    }
    if (this.parseNegate(), this.globSet = [...new Set(this.braceExpand())], B.debug) this.debug = (...Z) => console.error(...Z);
    this.debug(this.pattern, this.globSet);
    let Q = this.globSet.map(Z => this.slashSplit(Z));
    this.globParts = this.preprocess(Q), this.debug(this.pattern, this.globParts);
    let D = this.globParts.map((Z, G, F) => {
      if (this.isWindows && this.windowsNoMagicRoot) {
        let I = Z[0] === "" && Z[1] === "" && (Z[2] === "?" || !Yt0.test(Z[2])) && !Yt0.test(Z[3]),
          Y = /^[a-z]:/i.test(Z[0]);
        if (I) return [...Z.slice(0, 4), ...Z.slice(4).map(W => this.parse(W))];else if (Y) return [Z[0], ...Z.slice(1).map(W => this.parse(W))];
      }
      return Z.map(I => this.parse(I));
    });
    if (this.debug(this.pattern, D), this.set = D.filter(Z => Z.indexOf(!1) === -1), this.isWindows) for (let Z = 0; Z < this.set.length; Z++) {
      let G = this.set[Z];
      if (G[0] === "" && G[1] === "" && this.globParts[Z][2] === "?" && typeof G[3] === "string" && /^[a-z]:$/i.test(G[3])) G[2] = "?";
    }
    this.debug(this.pattern, this.set);
  }
  preprocess(A) {
    if (this.options.noglobstar) {
      for (let Q = 0; Q < A.length; Q++) for (let D = 0; D < A[Q].length; D++) if (A[Q][D] === "**") A[Q][D] = "*";
    }
    let {
      optimizationLevel: B = 1
    } = this.options;
    if (B >= 2) A = this.firstPhasePreProcess(A), A = this.secondPhasePreProcess(A);else if (B >= 1) A = this.levelOneOptimize(A);else A = this.adjascentGlobstarOptimize(A);
    return A;
  }
  adjascentGlobstarOptimize(A) {
    return A.map(B => {
      let Q = -1;
      while ((Q = B.indexOf("**", Q + 1)) !== -1) {
        let D = Q;
        while (B[D + 1] === "**") D++;
        if (D !== Q) B.splice(Q, D - Q);
      }
      return B;
    });
  }
  levelOneOptimize(A) {
    return A.map(B => {
      return B = B.reduce((Q, D) => {
        let Z = Q[Q.length - 1];
        if (D === "**" && Z === "**") return Q;
        if (D === "..") {
          if (Z && Z !== ".." && Z !== "." && Z !== "**") return Q.pop(), Q;
        }
        return Q.push(D), Q;
      }, []), B.length === 0 ? [""] : B;
    });
  }
  levelTwoFileOptimize(A) {
    if (!Array.isArray(A)) A = this.slashSplit(A);
    let B = !1;
    do {
      if (B = !1, !this.preserveMultipleSlashes) {
        for (let D = 1; D < A.length - 1; D++) {
          let Z = A[D];
          if (D === 1 && Z === "" && A[0] === "") continue;
          if (Z === "." || Z === "") B = !0, A.splice(D, 1), D--;
        }
        if (A[0] === "." && A.length === 2 && (A[1] === "." || A[1] === "")) B = !0, A.pop();
      }
      let Q = 0;
      while ((Q = A.indexOf("..", Q + 1)) !== -1) {
        let D = A[Q - 1];
        if (D && D !== "." && D !== ".." && D !== "**") B = !0, A.splice(Q - 1, 2), Q -= 2;
      }
    } while (B);
    return A.length === 0 ? [""] : A;
  }
  firstPhasePreProcess(A) {
    let B = !1;
    do {
      B = !1;
      for (let Q of A) {
        let D = -1;
        while ((D = Q.indexOf("**", D + 1)) !== -1) {
          let G = D;
          while (Q[G + 1] === "**") G++;
          if (G > D) Q.splice(D + 1, G - D);
          let F = Q[D + 1],
            I = Q[D + 2],
            Y = Q[D + 3];
          if (F !== "..") continue;
          if (!I || I === "." || I === ".." || !Y || Y === "." || Y === "..") continue;
          B = !0, Q.splice(D, 1);
          let W = Q.slice(0);
          W[D] = "**", A.push(W), D--;
        }
        if (!this.preserveMultipleSlashes) {
          for (let G = 1; G < Q.length - 1; G++) {
            let F = Q[G];
            if (G === 1 && F === "" && Q[0] === "") continue;
            if (F === "." || F === "") B = !0, Q.splice(G, 1), G--;
          }
          if (Q[0] === "." && Q.length === 2 && (Q[1] === "." || Q[1] === "")) B = !0, Q.pop();
        }
        let Z = 0;
        while ((Z = Q.indexOf("..", Z + 1)) !== -1) {
          let G = Q[Z - 1];
          if (G && G !== "." && G !== ".." && G !== "**") {
            B = !0;
            let I = Z === 1 && Q[Z + 1] === "**" ? ["."] : [];
            if (Q.splice(Z - 1, 2, ...I), Q.length === 0) Q.push("");
            Z -= 2;
          }
        }
      }
    } while (B);
    return A;
  }
  secondPhasePreProcess(A) {
    for (let B = 0; B < A.length - 1; B++) for (let Q = B + 1; Q < A.length; Q++) {
      let D = this.partsMatch(A[B], A[Q], !this.preserveMultipleSlashes);
      if (D) {
        A[B] = [], A[Q] = D;
        break;
      }
    }
    return A.filter(B => B.length);
  }
  partsMatch(A, B, Q = !1) {
    let D = 0,
      Z = 0,
      G = [],
      F = "";
    while (D < A.length && Z < B.length) if (A[D] === B[Z]) G.push(F === "b" ? B[Z] : A[D]), D++, Z++;else if (Q && A[D] === "**" && B[Z] === A[D + 1]) G.push(A[D]), D++;else if (Q && B[Z] === "**" && A[D] === B[Z + 1]) G.push(B[Z]), Z++;else if (A[D] === "*" && B[Z] && (this.options.dot || !B[Z].startsWith(".")) && B[Z] !== "**") {
      if (F === "b") return !1;
      F = "a", G.push(A[D]), D++, Z++;
    } else if (B[Z] === "*" && A[D] && (this.options.dot || !A[D].startsWith(".")) && A[D] !== "**") {
      if (F === "a") return !1;
      F = "b", G.push(B[Z]), D++, Z++;
    } else return !1;
    return A.length === B.length && G;
  }
  parseNegate() {
    if (this.nonegate) return;
    let A = this.pattern,
      B = !1,
      Q = 0;
    for (let D = 0; D < A.length && A.charAt(D) === "!"; D++) B = !B, Q++;
    if (Q) this.pattern = A.slice(Q);
    this.negate = B;
  }
  matchOne(A, B, Q = !1) {
    let D = this.options;
    if (this.isWindows) {
      let K = typeof A[0] === "string" && /^[a-z]:$/i.test(A[0]),
        H = !K && A[0] === "" && A[1] === "" && A[2] === "?" && /^[a-z]:$/i.test(A[3]),
        z = typeof B[0] === "string" && /^[a-z]:$/i.test(B[0]),
        $ = !z && B[0] === "" && B[1] === "" && B[2] === "?" && typeof B[3] === "string" && /^[a-z]:$/i.test(B[3]),
        L = H ? 3 : K ? 0 : void 0,
        N = $ ? 3 : z ? 0 : void 0;
      if (typeof L === "number" && typeof N === "number") {
        let [O, R] = [A[L], B[N]];
        if (O.toLowerCase() === R.toLowerCase()) {
          if (B[N] = O, N > L) B = B.slice(N);else if (L > N) A = A.slice(L);
        }
      }
    }
    let {
      optimizationLevel: Z = 1
    } = this.options;
    if (Z >= 2) A = this.levelTwoFileOptimize(A);
    this.debug("matchOne", this, {
      file: A,
      pattern: B
    }), this.debug("matchOne", A.length, B.length);
    for (var G = 0, F = 0, I = A.length, Y = B.length; G < I && F < Y; G++, F++) {
      this.debug("matchOne loop");
      var W = B[F],
        J = A[G];
      if (this.debug(B, W, J), W === !1) return !1;
      if (W === KY) {
        this.debug("GLOBSTAR", [B, W, J]);
        var X = G,
          V = F + 1;
        if (V === Y) {
          this.debug("** at the end");
          for (; G < I; G++) if (A[G] === "." || A[G] === ".." || !D.dot && A[G].charAt(0) === ".") return !1;
          return !0;
        }
        while (X < I) {
          var C = A[X];
          if (this.debug(`
globstar while`, A, X, B, V, C), this.matchOne(A.slice(X), B.slice(V), Q)) return this.debug("globstar found match!", X, I, C), !0;else {
            if (C === "." || C === ".." || !D.dot && C.charAt(0) === ".") {
              this.debug("dot detected!", A, X, B, V);
              break;
            }
            this.debug("globstar swallow a segment, and continue"), X++;
          }
        }
        if (Q) {
          if (this.debug(`
>>> no match, partial?`, A, X, B, V), X === I) return !0;
        }
        return !1;
      }
      let K;
      if (typeof W === "string") K = J === W, this.debug("string match", W, J, K);else K = W.test(J), this.debug("pattern match", W, J, K);
      if (!K) return !1;
    }
    if (G === I && F === Y) return !0;else if (G === I) return Q;else if (F === Y) return G === I - 1 && A[G] === "";else throw new Error("wtf?");
  }
  braceExpand() {
    return Ct0(this.pattern, this.options);
  }
  parse(A) {
    _B1(A);
    let B = this.options;
    if (A === "**") return KY;
    if (A === "") return "";
    let Q,
      D = null;
    if (Q = A.match(hx9)) D = B.dot ? ux9 : gx9;else if (Q = A.match(Px9)) D = (B.nocase ? B.dot ? kx9 : yx9 : B.dot ? jx9 : Sx9)(Q[1]);else if (Q = A.match(mx9)) D = (B.nocase ? B.dot ? cx9 : dx9 : B.dot ? lx9 : px9)(Q);else if (Q = A.match(_x9)) D = B.dot ? vx9 : xx9;else if (Q = A.match(bx9)) D = fx9;
    let Z = CY.fromGlob(A, this.options).toMMPattern();
    if (D && typeof Z === "object") Reflect.defineProperty(Z, "test", {
      value: D
    });
    return Z;
  }
  makeRe() {
    if (this.regexp || this.regexp === !1) return this.regexp;
    let A = this.set;
    if (!A.length) return this.regexp = !1, this.regexp;
    let B = this.options,
      Q = B.noglobstar ? ax9 : B.dot ? sx9 : rx9,
      D = new Set(B.nocase ? ["i"] : []),
      Z = A.map(I => {
        let Y = I.map(W => {
          if (W instanceof RegExp) for (let J of W.flags.split("")) D.add(J);
          return typeof W === "string" ? Bv9(W) : W === KY ? KY : W._src;
        });
        return Y.forEach((W, J) => {
          let X = Y[J + 1],
            V = Y[J - 1];
          if (W !== KY || V === KY) return;
          if (V === void 0) {
            if (X !== void 0 && X !== KY) Y[J + 1] = "(?:\\/|" + Q + "\\/)?" + X;else Y[J] = Q;
          } else if (X === void 0) Y[J - 1] = V + "(?:\\/|" + Q + ")?";else if (X !== KY) Y[J - 1] = V + "(?:\\/|\\/" + Q + "\\/)" + X, Y[J + 1] = KY;
        }), Y.filter(W => W !== KY).join("/");
      }).join("|"),
      [G, F] = A.length > 1 ? ["(?:", ")"] : ["", ""];
    if (Z = "^" + G + Z + F + "$", this.negate) Z = "^(?!" + Z + ").+$";
    try {
      this.regexp = new RegExp(Z, [...D].join(""));
    } catch (I) {
      this.regexp = !1;
    }
    return this.regexp;
  }
  slashSplit(A) {
    if (this.preserveMultipleSlashes) return A.split("/");else if (this.isWindows && /^\/\/[^\/]+/.test(A)) return ["", ...A.split(/\/+/)];else return A.split(/\/+/);
  }
  match(A, B = this.partial) {
    if (this.debug("match", A, this.pattern), this.comment) return !1;
    if (this.empty) return A === "";
    if (A === "/" && B) return !0;
    let Q = this.options;
    if (this.isWindows) A = A.split("\\").join("/");
    let D = this.slashSplit(A);
    this.debug(this.pattern, "split", D);
    let Z = this.set;
    this.debug(this.pattern, "set", Z);
    let G = D[D.length - 1];
    if (!G) for (let F = D.length - 2; !G && F >= 0; F--) G = D[F];
    for (let F = 0; F < Z.length; F++) {
      let I = Z[F],
        Y = D;
      if (Q.matchBase && I.length === 1) Y = [G];
      if (this.matchOne(Y, I, B)) {
        if (Q.flipNegate) return !0;
        return !this.negate;
      }
    }
    if (Q.flipNegate) return !1;
    return this.negate;
  }
  static defaults(A) {
    return cJ.defaults(A).Minimatch;
  }
}
cJ.AST = CY;
cJ.Minimatch = aH;
cJ.escape = zp;
cJ.unescape = Iw;
module.exports = {
  F1,
  Iw,
  KY,
  aH,
  zp
};