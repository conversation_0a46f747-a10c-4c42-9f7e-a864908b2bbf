/**
 * Isolated bundle for 'W3'
 */

function zQ(A, B, Q, D, Z) {
  if (D === "m") throw new TypeError("Private method is not writable");
  if (D === "a" && !Z) throw new TypeError("Private accessor was defined without a setter");
  if (typeof B === "function" ? A !== B || !Z : !B.has(A)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
  return D === "a" ? Z.call(A, Q) : Z ? Z.value = Q : B.set(A, Q), Q;
}
function xA(A, B, Q, D) {
  if (Q === "a" && !D) throw new TypeError("Private accessor was defined without a getter");
  if (typeof B === "function" ? A !== B || !D : !B.has(A)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
  return Q === "m" ? D : Q === "a" ? D.call(A) : D ? D.value : B.get(A);
}
var hC0 = function () {
  let {
    crypto: A
  } = globalThis;
  if (A?.randomUUID) return hC0 = A.randomUUID.bind(A), A.randomUUID();
  let B = new Uint8Array(1),
    Q = A ? () => A.getRandomValues(B)[0] : () => Math.random() * 255 & 255;
  return "10000000-1000-4000-8000-100000000000".replace(/[018]/g, D => (+D ^ Q() & 15 >> +D / 4).toString(16));
};
function vP(A) {
  return typeof A === "object" && A !== null && ("name" in A && A.name === "AbortError" || "message" in A && String(A.message).includes("FetchRequestCanceledException"));
}
var m71 = A => {
  if (A instanceof Error) return A;
  if (typeof A === "object" && A !== null) {
    try {
      if (Object.prototype.toString.call(A) === "[object Error]") {
        let B = new Error(A.message, A.cause ? {
          cause: A.cause
        } : {});
        if (A.stack) B.stack = A.stack;
        if (A.cause && !B.cause) B.cause = A.cause;
        if (A.name) B.name = A.name;
        return B;
      }
    } catch {}
    try {
      return new Error(JSON.stringify(A));
    } catch {}
  }
  return new Error(A);
};
class j9 extends Error {}
class Q6 extends j9 {
  constructor(A, B, Q, D) {
    super(`${Q6.makeMessage(A, B, Q)}`);
    this.status = A, this.headers = D, this.requestID = D?.get("request-id"), this.error = B;
  }
  static makeMessage(A, B, Q) {
    let D = B?.message ? typeof B.message === "string" ? B.message : JSON.stringify(B.message) : B ? JSON.stringify(B) : Q;
    if (A && D) return `${A} ${D}`;
    if (A) return `${A} status code (no body)`;
    if (D) return D;
    return "(no status code or body)";
  }
  static generate(A, B, Q, D) {
    if (!A || !D) return new bP({
      message: Q,
      cause: m71(B)
    });
    let Z = B;
    if (A === 400) return new c71(A, Z, Q, D);
    if (A === 401) return new l71(A, Z, Q, D);
    if (A === 403) return new p71(A, Z, Q, D);
    if (A === 404) return new i71(A, Z, Q, D);
    if (A === 409) return new n71(A, Z, Q, D);
    if (A === 422) return new a71(A, Z, Q, D);
    if (A === 429) return new s71(A, Z, Q, D);
    if (A >= 500) return new r71(A, Z, Q, D);
    return new Q6(A, Z, Q, D);
  }
}
class _F extends Q6 {
  constructor({
    message: A
  } = {}) {
    super(void 0, void 0, A || "Request was aborted.", void 0);
  }
}
class bP extends Q6 {
  constructor({
    message: A,
    cause: B
  }) {
    super(void 0, void 0, A || "Connection error.", void 0);
    if (B) this.cause = B;
  }
}
class d71 extends bP {
  constructor({
    message: A
  } = {}) {
    super({
      message: A ?? "Request timed out."
    });
  }
}
class c71 extends Q6 {}
class l71 extends Q6 {}
class p71 extends Q6 {}
class i71 extends Q6 {}
class n71 extends Q6 {}
class a71 extends Q6 {}
class s71 extends Q6 {}
class r71 extends Q6 {}
var yS6 = /^[a-z][a-z0-9+.-]*:/i,
  o6B = A => {
    return yS6.test(A);
  },
  gC0 = A => (gC0 = Array.isArray, gC0(A)),
  uC0 = gC0;
function t6B(A) {
  if (!A) return !0;
  for (let B in A) return !1;
  return !0;
}
function e6B(A, B) {
  return Object.prototype.hasOwnProperty.call(A, B);
}
var A8B = (A, B) => {
  if (typeof B !== "number" || !Number.isInteger(B)) throw new j9(`${A} must be an integer`);
  if (B < 0) throw new j9(`${A} must be a positive integer`);
  return B;
};
var qj1 = A => {
  try {
    return JSON.parse(A);
  } catch (B) {
    return;
  }
};
var B8B = A => new Promise(B => setTimeout(B, A));
var Lx = "0.55.1";
var G8B = () => {
  return typeof window !== "undefined" && typeof window.document !== "undefined" && typeof navigator !== "undefined";
};
function kS6() {
  if (typeof Deno !== "undefined" && Deno.build != null) return "deno";
  if (typeof EdgeRuntime !== "undefined") return "edge";
  if (Object.prototype.toString.call(typeof globalThis.process !== "undefined" ? globalThis.process : 0) === "[object process]") return "node";
  return "unknown";
}
var _S6 = () => {
  let A = kS6();
  if (A === "deno") return {
    "X-Stainless-Lang": "js",
    "X-Stainless-Package-Version": Lx,
    "X-Stainless-OS": D8B(Deno.build.os),
    "X-Stainless-Arch": Q8B(Deno.build.arch),
    "X-Stainless-Runtime": "deno",
    "X-Stainless-Runtime-Version": typeof Deno.version === "string" ? Deno.version : Deno.version?.deno ?? "unknown"
  };
  if (typeof EdgeRuntime !== "undefined") return {
    "X-Stainless-Lang": "js",
    "X-Stainless-Package-Version": Lx,
    "X-Stainless-OS": "Unknown",
    "X-Stainless-Arch": `other:${EdgeRuntime}`,
    "X-Stainless-Runtime": "edge",
    "X-Stainless-Runtime-Version": globalThis.process.version
  };
  if (A === "node") return {
    "X-Stainless-Lang": "js",
    "X-Stainless-Package-Version": Lx,
    "X-Stainless-OS": D8B(globalThis.process.platform ?? "unknown"),
    "X-Stainless-Arch": Q8B(globalThis.process.arch ?? "unknown"),
    "X-Stainless-Runtime": "node",
    "X-Stainless-Runtime-Version": globalThis.process.version ?? "unknown"
  };
  let B = xS6();
  if (B) return {
    "X-Stainless-Lang": "js",
    "X-Stainless-Package-Version": Lx,
    "X-Stainless-OS": "Unknown",
    "X-Stainless-Arch": "unknown",
    "X-Stainless-Runtime": `browser:${B.browser}`,
    "X-Stainless-Runtime-Version": B.version
  };
  return {
    "X-Stainless-Lang": "js",
    "X-Stainless-Package-Version": Lx,
    "X-Stainless-OS": "Unknown",
    "X-Stainless-Arch": "unknown",
    "X-Stainless-Runtime": "unknown",
    "X-Stainless-Runtime-Version": "unknown"
  };
};
function xS6() {
  if (typeof navigator === "undefined" || !navigator) return null;
  let A = [{
    key: "edge",
    pattern: /Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/
  }, {
    key: "ie",
    pattern: /MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/
  }, {
    key: "ie",
    pattern: /Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/
  }, {
    key: "chrome",
    pattern: /Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/
  }, {
    key: "firefox",
    pattern: /Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/
  }, {
    key: "safari",
    pattern: /(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/
  }];
  for (let {
    key: B,
    pattern: Q
  } of A) {
    let D = Q.exec(navigator.userAgent);
    if (D) {
      let Z = D[1] || 0,
        G = D[2] || 0,
        F = D[3] || 0;
      return {
        browser: B,
        version: `${Z}.${G}.${F}`
      };
    }
  }
  return null;
}
var Q8B = A => {
    if (A === "x32") return "x32";
    if (A === "x86_64" || A === "x64") return "x64";
    if (A === "arm") return "arm";
    if (A === "aarch64" || A === "arm64") return "arm64";
    if (A) return `other:${A}`;
    return "unknown";
  },
  D8B = A => {
    if (A = A.toLowerCase(), A.includes("ios")) return "iOS";
    if (A === "android") return "Android";
    if (A === "darwin") return "MacOS";
    if (A === "win32") return "Windows";
    if (A === "freebsd") return "FreeBSD";
    if (A === "openbsd") return "OpenBSD";
    if (A === "linux") return "Linux";
    if (A) return `Other:${A}`;
    return "Unknown";
  },
  Z8B,
  F8B = () => {
    return Z8B ?? (Z8B = _S6());
  };
function I8B() {
  if (typeof fetch !== "undefined") return fetch;
  throw new Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new Anthropic({ fetch })` or polyfill the global, `globalThis.fetch = fetch`");
}
function dC0(...A) {
  let B = globalThis.ReadableStream;
  if (typeof B === "undefined") throw new Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");
  return new B(...A);
}
function Nj1(A) {
  let B = Symbol.asyncIterator in A ? A[Symbol.asyncIterator]() : A[Symbol.iterator]();
  return dC0({
    start() {},
    async pull(Q) {
      let {
        done: D,
        value: Z
      } = await B.next();
      if (D) Q.close();else Q.enqueue(Z);
    },
    async cancel() {
      await B.return?.();
    }
  });
}
function o71(A) {
  if (A[Symbol.asyncIterator]) return A;
  let B = A.getReader();
  return {
    async next() {
      try {
        let Q = await B.read();
        if (Q?.done) B.releaseLock();
        return Q;
      } catch (Q) {
        throw B.releaseLock(), Q;
      }
    },
    async return() {
      let Q = B.cancel();
      return B.releaseLock(), await Q, {
        done: !0,
        value: void 0
      };
    },
    [Symbol.asyncIterator]() {
      return this;
    }
  };
}
async function Y8B(A) {
  if (A === null || typeof A !== "object") return;
  if (A[Symbol.asyncIterator]) {
    await A[Symbol.asyncIterator]().return?.();
    return;
  }
  let B = A.getReader(),
    Q = B.cancel();
  B.releaseLock(), await Q;
}
var W8B = ({
  headers: A,
  body: B
}) => {
  return {
    bodyHeaders: {
      "content-type": "application/json"
    },
    body: JSON.stringify(B)
  };
};
function V8B(A) {
  let B = 0;
  for (let Z of A) B += Z.length;
  let Q = new Uint8Array(B),
    D = 0;
  for (let Z of A) Q.set(Z, D), D += Z.length;
  return Q;
}
var J8B;
function t71(A) {
  let B;
  return (J8B ?? (B = new globalThis.TextEncoder(), J8B = B.encode.bind(B)))(A);
}
var X8B;
function cC0(A) {
  let B;
  return (X8B ?? (B = new globalThis.TextDecoder(), X8B = B.decode.bind(B)))(A);
}
var fK, hK;
class Mx {
  constructor() {
    fK.set(this, void 0), hK.set(this, void 0), zQ(this, fK, new Uint8Array(), "f"), zQ(this, hK, null, "f");
  }
  decode(A) {
    if (A == null) return [];
    let B = A instanceof ArrayBuffer ? new Uint8Array(A) : typeof A === "string" ? t71(A) : A;
    zQ(this, fK, V8B([xA(this, fK, "f"), B]), "f");
    let Q = [],
      D;
    while ((D = fS6(xA(this, fK, "f"), xA(this, hK, "f"))) != null) {
      if (D.carriage && xA(this, hK, "f") == null) {
        zQ(this, hK, D.index, "f");
        continue;
      }
      if (xA(this, hK, "f") != null && (D.index !== xA(this, hK, "f") + 1 || D.carriage)) {
        Q.push(cC0(xA(this, fK, "f").subarray(0, xA(this, hK, "f") - 1))), zQ(this, fK, xA(this, fK, "f").subarray(xA(this, hK, "f")), "f"), zQ(this, hK, null, "f");
        continue;
      }
      let Z = xA(this, hK, "f") !== null ? D.preceding - 1 : D.preceding,
        G = cC0(xA(this, fK, "f").subarray(0, Z));
      Q.push(G), zQ(this, fK, xA(this, fK, "f").subarray(D.index), "f"), zQ(this, hK, null, "f");
    }
    return Q;
  }
  flush() {
    if (!xA(this, fK, "f").length) return [];
    return this.decode(`
`);
  }
}
Mx.NEWLINE_CHARS = new Set([`
`, "\r"]);
Mx.NEWLINE_REGEXP = /\r\n|[\n\r]/g;
function fS6(A, B) {
  for (let Z = B ?? 0; Z < A.length; Z++) {
    if (A[Z] === 10) return {
      preceding: Z,
      index: Z + 1,
      carriage: !1
    };
    if (A[Z] === 13) return {
      preceding: Z,
      index: Z + 1,
      carriage: !0
    };
  }
  return null;
}
function C8B(A) {
  for (let D = 0; D < A.length - 1; D++) {
    if (A[D] === 10 && A[D + 1] === 10) return D + 2;
    if (A[D] === 13 && A[D + 1] === 13) return D + 2;
    if (A[D] === 13 && A[D + 1] === 10 && D + 3 < A.length && A[D + 2] === 13 && A[D + 3] === 10) return D + 4;
  }
  return -1;
}
class YX {
  constructor(A, B) {
    this.iterator = A, this.controller = B;
  }
  static fromSSEResponse(A, B) {
    let Q = !1;
    async function* D() {
      if (Q) throw new j9("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");
      Q = !0;
      let Z = !1;
      try {
        for await (let G of hS6(A, B)) {
          if (G.event === "completion") try {
            yield JSON.parse(G.data);
          } catch (F) {
            throw console.error("Could not parse message into JSON:", G.data), console.error("From chunk:", G.raw), F;
          }
          if (G.event === "message_start" || G.event === "message_delta" || G.event === "message_stop" || G.event === "content_block_start" || G.event === "content_block_delta" || G.event === "content_block_stop") try {
            yield JSON.parse(G.data);
          } catch (F) {
            throw console.error("Could not parse message into JSON:", G.data), console.error("From chunk:", G.raw), F;
          }
          if (G.event === "ping") continue;
          if (G.event === "error") throw new Q6(void 0, qj1(G.data) ?? G.data, void 0, A.headers);
        }
        Z = !0;
      } catch (G) {
        if (vP(G)) return;
        throw G;
      } finally {
        if (!Z) B.abort();
      }
    }
    return new YX(D, B);
  }
  static fromReadableStream(A, B) {
    let Q = !1;
    async function* D() {
      let G = new Mx(),
        F = o71(A);
      for await (let I of F) for (let Y of G.decode(I)) yield Y;
      for (let I of G.flush()) yield I;
    }
    async function* Z() {
      if (Q) throw new j9("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");
      Q = !0;
      let G = !1;
      try {
        for await (let F of D()) {
          if (G) continue;
          if (F) yield JSON.parse(F);
        }
        G = !0;
      } catch (F) {
        if (vP(F)) return;
        throw F;
      } finally {
        if (!G) B.abort();
      }
    }
    return new YX(Z, B);
  }
  [Symbol.asyncIterator]() {
    return this.iterator();
  }
  tee() {
    let A = [],
      B = [],
      Q = this.iterator(),
      D = Z => {
        return {
          next: () => {
            if (Z.length === 0) {
              let G = Q.next();
              A.push(G), B.push(G);
            }
            return Z.shift();
          }
        };
      };
    return [new YX(() => D(A), this.controller), new YX(() => D(B), this.controller)];
  }
  toReadableStream() {
    let A = this,
      B;
    return dC0({
      async start() {
        B = A[Symbol.asyncIterator]();
      },
      async pull(Q) {
        try {
          let {
            value: D,
            done: Z
          } = await B.next();
          if (Z) return Q.close();
          let G = t71(JSON.stringify(D) + `
`);
          Q.enqueue(G);
        } catch (D) {
          Q.error(D);
        }
      },
      async cancel() {
        await B.return?.();
      }
    });
  }
}
async function* hS6(A, B) {
  if (!A.body) {
    if (B.abort(), typeof globalThis.navigator !== "undefined" && globalThis.navigator.product === "ReactNative") throw new j9("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api");
    throw new j9("Attempted to iterate over a response with no body");
  }
  let Q = new K8B(),
    D = new Mx(),
    Z = o71(A.body);
  for await (let G of gS6(Z)) for (let F of D.decode(G)) {
    let I = Q.decode(F);
    if (I) yield I;
  }
  for (let G of D.flush()) {
    let F = Q.decode(G);
    if (F) yield F;
  }
}
async function* gS6(A) {
  let B = new Uint8Array();
  for await (let Q of A) {
    if (Q == null) continue;
    let D = Q instanceof ArrayBuffer ? new Uint8Array(Q) : typeof Q === "string" ? t71(Q) : Q,
      Z = new Uint8Array(B.length + D.length);
    Z.set(B), Z.set(D, B.length), B = Z;
    let G;
    while ((G = C8B(B)) !== -1) yield B.slice(0, G), B = B.slice(G);
  }
  if (B.length > 0) yield B;
}
class K8B {
  constructor() {
    this.event = null, this.data = [], this.chunks = [];
  }
  decode(A) {
    if (A.endsWith("\r")) A = A.substring(0, A.length - 1);
    if (!A) {
      if (!this.event && !this.data.length) return null;
      let Z = {
        event: this.event,
        data: this.data.join(`
`),
        raw: this.chunks
      };
      return this.event = null, this.data = [], this.chunks = [], Z;
    }
    if (this.chunks.push(A), A.startsWith(":")) return null;
    let [B, Q, D] = uS6(A, ":");
    if (D.startsWith(" ")) D = D.substring(1);
    if (B === "event") this.event = D;else if (B === "data") this.data.push(D);
    return null;
  }
}
function uS6(A, B) {
  let Q = A.indexOf(B);
  if (Q !== -1) return [A.substring(0, Q), B, A.substring(Q + B.length)];
  return [A, "", ""];
}
var Mj1 = {
    off: 0,
    error: 200,
    warn: 300,
    info: 400,
    debug: 500
  },
  lC0 = (A, B, Q) => {
    if (!A) return;
    if (e6B(Mj1, A)) return A;
    WJ(Q).warn(`${B} was set to ${JSON.stringify(A)}, expected one of ${JSON.stringify(Object.keys(Mj1))}`);
    return;
  };
function e71() {}
function Lj1(A, B, Q) {
  if (!B || Mj1[A] > Mj1[Q]) return e71;else return B[A].bind(B);
}
var mS6 = {
    error: e71,
    warn: e71,
    info: e71,
    debug: e71
  },
  H8B = new WeakMap();
function WJ(A) {
  let B = A.logger,
    Q = A.logLevel ?? "off";
  if (!B) return mS6;
  let D = H8B.get(B);
  if (D && D[0] === Q) return D[1];
  let Z = {
    error: Lj1("error", B, Q),
    warn: Lj1("warn", B, Q),
    info: Lj1("info", B, Q),
    debug: Lj1("debug", B, Q)
  };
  return H8B.set(B, [Q, Z]), Z;
}
var fP = A => {
  if (A.options) A.options = {
    ...A.options
  }, delete A.options.headers;
  if (A.headers) A.headers = Object.fromEntries((A.headers instanceof Headers ? [...A.headers] : Object.entries(A.headers)).map(([B, Q]) => [B, B.toLowerCase() === "x-api-key" || B.toLowerCase() === "authorization" || B.toLowerCase() === "cookie" || B.toLowerCase() === "set-cookie" ? "***" : Q]));
  if ("retryOfRequestLogID" in A) {
    if (A.retryOfRequestLogID) A.retryOf = A.retryOfRequestLogID;
    delete A.retryOfRequestLogID;
  }
  return A;
};
async function Rj1(A, B) {
  let {
      response: Q,
      requestLogID: D,
      retryOfRequestLogID: Z,
      startTime: G
    } = B,
    F = await (async () => {
      if (B.options.stream) {
        if (WJ(A).debug("response", Q.status, Q.url, Q.headers, Q.body), B.options.__streamClass) return B.options.__streamClass.fromSSEResponse(Q, B.controller);
        return YX.fromSSEResponse(Q, B.controller);
      }
      if (Q.status === 204) return null;
      if (B.options.__binaryResponse) return Q;
      let Y = Q.headers.get("content-type")?.split(";")[0]?.trim();
      if (Y?.includes("application/json") || Y?.endsWith("+json")) {
        let X = await Q.json();
        return pC0(X, Q);
      }
      return await Q.text();
    })();
  return WJ(A).debug(`[${D}] response parsed`, fP({
    retryOfRequestLogID: Z,
    url: Q.url,
    status: Q.status,
    body: F,
    durationMs: Date.now() - G
  })), F;
}
function pC0(A, B) {
  if (!A || typeof A !== "object" || Array.isArray(A)) return A;
  return Object.defineProperty(A, "_request_id", {
    value: B.headers.get("request-id"),
    enumerable: !1
  });
}
var AD1;
class Im extends Promise {
  constructor(A, B, Q = Rj1) {
    super(D => {
      D(null);
    });
    this.responsePromise = B, this.parseResponse = Q, AD1.set(this, void 0), zQ(this, AD1, A, "f");
  }
  _thenUnwrap(A) {
    return new Im(xA(this, AD1, "f"), this.responsePromise, async (B, Q) => pC0(A(await this.parseResponse(B, Q), Q), Q.response));
  }
  asResponse() {
    return this.responsePromise.then(A => A.response);
  }
  async withResponse() {
    let [A, B] = await Promise.all([this.parse(), this.asResponse()]);
    return {
      data: A,
      response: B,
      request_id: B.headers.get("request-id")
    };
  }
  parse() {
    if (!this.parsedPromise) this.parsedPromise = this.responsePromise.then(A => this.parseResponse(xA(this, AD1, "f"), A));
    return this.parsedPromise;
  }
  then(A, B) {
    return this.parse().then(A, B);
  }
  catch(A) {
    return this.parse().catch(A);
  }
  finally(A) {
    return this.parse().finally(A);
  }
}
class Tj1 extends Im {
  constructor(A, B, Q) {
    super(A, B, async (D, Z) => new Q(D, Z.response, await Rj1(D, Z), Z.options));
  }
  async *[Symbol.asyncIterator]() {
    let A = await this;
    for await (let B of A) yield B;
  }
}
var nC0 = () => {
  if (typeof File === "undefined") {
    let {
        process: A
      } = globalThis,
      B = typeof A?.versions?.node === "string" && parseInt(A.versions.node.split(".")) < 20;
    throw new Error("`File` is not defined as a global, which is required for file uploads." + (B ? " Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`." : ""));
  }
};
function Ym(A, B, Q) {
  return nC0(), new File(A, B ?? "unknown_file", Q);
}
function BD1(A) {
  return (typeof A === "object" && A !== null && ("name" in A && A.name && String(A.name) || "url" in A && A.url && String(A.url) || "filename" in A && A.filename && String(A.filename) || "path" in A && A.path && String(A.path)) || "").split(/[\\/]/).pop() || void 0;
}
var aC0 = A => A != null && typeof A === "object" && typeof A[Symbol.asyncIterator] === "function";
var w8B = A => A != null && typeof A === "object" && typeof A.size === "number" && typeof A.type === "string" && typeof A.text === "function" && typeof A.slice === "function" && typeof A.arrayBuffer === "function",
  iS6 = A => A != null && typeof A === "object" && typeof A.name === "string" && typeof A.lastModified === "number" && w8B(A),
  nS6 = A => A != null && typeof A === "object" && typeof A.url === "string" && typeof A.blob === "function";
async function Pj1(A, B, Q) {
  if (nC0(), A = await A, B || (B = BD1(A)), iS6(A)) {
    if (A instanceof File && B == null && Q == null) return A;
    return Ym([await A.arrayBuffer()], B ?? A.name, {
      type: A.type,
      lastModified: A.lastModified,
      ...Q
    });
  }
  if (nS6(A)) {
    let Z = await A.blob();
    return B || (B = new URL(A.url).pathname.split(/[\\/]/).pop()), Ym(await sC0(Z), B, Q);
  }
  let D = await sC0(A);
  if (!Q?.type) {
    let Z = D.find(G => typeof G === "object" && "type" in G && G.type);
    if (typeof Z === "string") Q = {
      ...Q,
      type: Z
    };
  }
  return Ym(D, B, Q);
}
async function sC0(A) {
  let B = [];
  if (typeof A === "string" || ArrayBuffer.isView(A) || A instanceof ArrayBuffer) B.push(A);else if (w8B(A)) B.push(A instanceof Blob ? A : await A.arrayBuffer());else if (aC0(A)) for await (let Q of A) B.push(...(await sC0(Q)));else {
    let Q = A?.constructor?.name;
    throw new Error(`Unexpected data type: ${typeof A}${Q ? `; constructor: ${Q}` : ""}${aS6(A)}`);
  }
  return B;
}
function aS6(A) {
  if (typeof A !== "object" || A === null) return "";
  return `; props: [${Object.getOwnPropertyNames(A).map(Q => `"${Q}"`).join(", ")}]`;
}
var $8B = Symbol.for("brand.privateNullableHeaders");
function* rS6(A) {
  if (!A) return;
  if ($8B in A) {
    let {
      values: D,
      nulls: Z
    } = A;
    yield* D.entries();
    for (let G of Z) yield [G, null];
    return;
  }
  let B = !1,
    Q;
  if (A instanceof Headers) Q = A.entries();else if (uC0(A)) Q = A;else B = !0, Q = Object.entries(A ?? {});
  for (let D of Q) {
    let Z = D[0];
    if (typeof Z !== "string") throw new TypeError("expected header name to be a string");
    let G = uC0(D[1]) ? D[1] : [D[1]],
      F = !1;
    for (let I of G) {
      if (I === void 0) continue;
      if (B && !F) F = !0, yield [Z, null];
      yield [Z, I];
    }
  }
}
var t8 = A => {
  let B = new Headers(),
    Q = new Set();
  for (let D of A) {
    let Z = new Set();
    for (let [G, F] of rS6(D)) {
      let I = G.toLowerCase();
      if (!Z.has(I)) B.delete(G), Z.add(I);
      if (F === null) B.delete(G), Q.add(I);else B.append(G, F), Q.delete(I);
    }
  }
  return {
    [$8B]: !0,
    values: B,
    nulls: Q
  };
};
var UD1 = A => {
  if (typeof globalThis.process !== "undefined") return globalThis.process.env?.[A]?.trim() ?? void 0;
  if (typeof globalThis.Deno !== "undefined") return globalThis.Deno.env?.get?.(A)?.trim();
  return;
};
var FK0, IK0, lj1, v8B;
class W3 {
  constructor({
    baseURL: A = UD1("ANTHROPIC_BASE_URL"),
    apiKey: B = UD1("ANTHROPIC_API_KEY") ?? null,
    authToken: Q = UD1("ANTHROPIC_AUTH_TOKEN") ?? null,
    ...D
  } = {}) {
    FK0.add(this), lj1.set(this, void 0);
    let Z = {
      apiKey: B,
      authToken: Q,
      ...D,
      baseURL: A || "https://api.anthropic.com"
    };
    if (!Z.dangerouslyAllowBrowser && G8B()) throw new j9(`It looks like you're running in a browser-like environment.

This is disabled by default, as it risks exposing your secret API credentials to attackers.
If you understand the risks and have appropriate mitigations in place,
you can set the \`dangerouslyAllowBrowser\` option to \`true\`, e.g.,

new Anthropic({ apiKey, dangerouslyAllowBrowser: true });
`);
    this.baseURL = Z.baseURL, this.timeout = Z.timeout ?? IK0.DEFAULT_TIMEOUT, this.logger = Z.logger ?? console;
    let G = "warn";
    this.logLevel = G, this.logLevel = lC0(Z.logLevel, "ClientOptions.logLevel", this) ?? lC0(UD1("ANTHROPIC_LOG"), "process.env['ANTHROPIC_LOG']", this) ?? G, this.fetchOptions = Z.fetchOptions, this.maxRetries = Z.maxRetries ?? 2, this.fetch = Z.fetch ?? I8B(), zQ(this, lj1, W8B, "f"), this._options = Z, this.apiKey = B, this.authToken = Q;
  }
  withOptions(A) {
    return new this.constructor({
      ...this._options,
      baseURL: this.baseURL,
      maxRetries: this.maxRetries,
      timeout: this.timeout,
      logger: this.logger,
      logLevel: this.logLevel,
      fetch: this.fetch,
      fetchOptions: this.fetchOptions,
      apiKey: this.apiKey,
      authToken: this.authToken,
      ...A
    });
  }
  defaultQuery() {
    return this._options.defaultQuery;
  }
  validateHeaders({
    values: A,
    nulls: B
  }) {
    if (this.apiKey && A.get("x-api-key")) return;
    if (B.has("x-api-key")) return;
    if (this.authToken && A.get("authorization")) return;
    if (B.has("authorization")) return;
    throw new Error('Could not resolve authentication method. Expected either apiKey or authToken to be set. Or for one of the "X-Api-Key" or "Authorization" headers to be explicitly omitted');
  }
  authHeaders(A) {
    return t8([this.apiKeyAuth(A), this.bearerAuth(A)]);
  }
  apiKeyAuth(A) {
    if (this.apiKey == null) return;
    return t8([{
      "X-Api-Key": this.apiKey
    }]);
  }
  bearerAuth(A) {
    if (this.authToken == null) return;
    return t8([{
      Authorization: `Bearer ${this.authToken}`
    }]);
  }
  stringifyQuery(A) {
    return Object.entries(A).filter(([B, Q]) => typeof Q !== "undefined").map(([B, Q]) => {
      if (typeof Q === "string" || typeof Q === "number" || typeof Q === "boolean") return `${encodeURIComponent(B)}=${encodeURIComponent(Q)}`;
      if (Q === null) return `${encodeURIComponent(B)}=`;
      throw new j9(`Cannot stringify type ${typeof Q}; Expected string, number, boolean, or null. If you need to pass nested query parameters, you can manually encode them, e.g. { query: { 'foo[key1]': value1, 'foo[key2]': value2 } }, and please open a GitHub issue requesting better support for your use case.`);
    }).join("&");
  }
  getUserAgent() {
    return `${this.constructor.name}/JS ${Lx}`;
  }
  defaultIdempotencyKey() {
    return `stainless-node-retry-${hC0()}`;
  }
  makeStatusError(A, B, Q, D) {
    return Q6.generate(A, B, Q, D);
  }
  buildURL(A, B, Q) {
    let D = !xA(this, FK0, "m", v8B).call(this) && Q || this.baseURL,
      Z = o6B(A) ? new URL(A) : new URL(D + (D.endsWith("/") && A.startsWith("/") ? A.slice(1) : A)),
      G = this.defaultQuery();
    if (!t6B(G)) B = {
      ...G,
      ...B
    };
    if (typeof B === "object" && B && !Array.isArray(B)) Z.search = this.stringifyQuery(B);
    return Z.toString();
  }
  _calculateNonstreamingTimeout(A) {
    if (3600 * A / 128000 > 600) throw new j9("Streaming is strongly recommended for operations that may take longer than 10 minutes. See https://github.com/anthropics/anthropic-sdk-typescript#streaming-responses for more details");
    return 600000;
  }
  async prepareOptions(A) {}
  async prepareRequest(A, {
    url: B,
    options: Q
  }) {}
  get(A, B) {
    return this.methodRequest("get", A, B);
  }
  post(A, B) {
    return this.methodRequest("post", A, B);
  }
  patch(A, B) {
    return this.methodRequest("patch", A, B);
  }
  put(A, B) {
    return this.methodRequest("put", A, B);
  }
  delete(A, B) {
    return this.methodRequest("delete", A, B);
  }
  methodRequest(A, B, Q) {
    return this.request(Promise.resolve(Q).then(D => {
      return {
        method: A,
        path: B,
        ...D
      };
    }));
  }
  request(A, B = null) {
    return new Im(this, this.makeRequest(A, B, void 0));
  }
  async makeRequest(A, B, Q) {
    let D = await A,
      Z = D.maxRetries ?? this.maxRetries;
    if (B == null) B = Z;
    await this.prepareOptions(D);
    let {
      req: G,
      url: F,
      timeout: I
    } = this.buildRequest(D, {
      retryCount: Z - B
    });
    await this.prepareRequest(G, {
      url: F,
      options: D
    });
    let Y = "log_" + (Math.random() * 16777216 | 0).toString(16).padStart(6, "0"),
      W = Q === void 0 ? "" : `, retryOf: ${Q}`,
      J = Date.now();
    if (WJ(this).debug(`[${Y}] sending request`, fP({
      retryOfRequestLogID: Q,
      method: D.method,
      url: F,
      options: D,
      headers: G.headers
    })), D.signal?.aborted) throw new _F();
    let X = new AbortController(),
      V = await this.fetchWithTimeout(F, G, I, X).catch(m71),
      C = Date.now();
    if (V instanceof Error) {
      let z = `retrying, ${B} attempts remaining`;
      if (D.signal?.aborted) throw new _F();
      let $ = vP(V) || /timed? ?out/i.test(String(V) + ("cause" in V ? String(V.cause) : ""));
      if (B) return WJ(this).info(`[${Y}] connection ${$ ? "timed out" : "failed"} - ${z}`), WJ(this).debug(`[${Y}] connection ${$ ? "timed out" : "failed"} (${z})`, fP({
        retryOfRequestLogID: Q,
        url: F,
        durationMs: C - J,
        message: V.message
      })), this.retryRequest(D, B, Q ?? Y);
      if (WJ(this).info(`[${Y}] connection ${$ ? "timed out" : "failed"} - error; no more retries left`), WJ(this).debug(`[${Y}] connection ${$ ? "timed out" : "failed"} (error; no more retries left)`, fP({
        retryOfRequestLogID: Q,
        url: F,
        durationMs: C - J,
        message: V.message
      })), $) throw new d71();
      throw new bP({
        cause: V
      });
    }
    let K = [...V.headers.entries()].filter(([z]) => z === "request-id").map(([z, $]) => ", " + z + ": " + JSON.stringify($)).join(""),
      H = `[${Y}${W}${K}] ${G.method} ${F} ${V.ok ? "succeeded" : "failed"} with status ${V.status} in ${C - J}ms`;
    if (!V.ok) {
      let z = this.shouldRetry(V);
      if (B && z) {
        let T = `retrying, ${B} attempts remaining`;
        return await Y8B(V.body), WJ(this).info(`${H} - ${T}`), WJ(this).debug(`[${Y}] response error (${T})`, fP({
          retryOfRequestLogID: Q,
          url: V.url,
          status: V.status,
          headers: V.headers,
          durationMs: C - J
        })), this.retryRequest(D, B, Q ?? Y, V.headers);
      }
      let $ = z ? "error; no more retries left" : "error; not retryable";
      WJ(this).info(`${H} - ${$}`);
      let L = await V.text().catch(T => m71(T).message),
        N = qj1(L),
        O = N ? void 0 : L;
      throw WJ(this).debug(`[${Y}] response error (${$})`, fP({
        retryOfRequestLogID: Q,
        url: V.url,
        status: V.status,
        headers: V.headers,
        message: O,
        durationMs: Date.now() - J
      })), this.makeStatusError(V.status, N, O, V.headers);
    }
    return WJ(this).info(H), WJ(this).debug(`[${Y}] response start`, fP({
      retryOfRequestLogID: Q,
      url: V.url,
      status: V.status,
      headers: V.headers,
      durationMs: C - J
    })), {
      response: V,
      options: D,
      controller: X,
      requestLogID: Y,
      retryOfRequestLogID: Q,
      startTime: J
    };
  }
  getAPIList(A, B, Q) {
    return this.requestAPIList(B, {
      method: "get",
      path: A,
      ...Q
    });
  }
  requestAPIList(A, B) {
    let Q = this.makeRequest(B, null, void 0);
    return new Tj1(this, Q, A);
  }
  async fetchWithTimeout(A, B, Q, D) {
    let {
      signal: Z,
      method: G,
      ...F
    } = B || {};
    if (Z) Z.addEventListener("abort", () => D.abort());
    let I = setTimeout(() => D.abort(), Q),
      Y = globalThis.ReadableStream && F.body instanceof globalThis.ReadableStream || typeof F.body === "object" && F.body !== null && Symbol.asyncIterator in F.body,
      W = {
        signal: D.signal,
        ...(Y ? {
          duplex: "half"
        } : {}),
        method: "GET",
        ...F
      };
    if (G) W.method = G.toUpperCase();
    try {
      return await this.fetch.call(void 0, A, W);
    } finally {
      clearTimeout(I);
    }
  }
  shouldRetry(A) {
    let B = A.headers.get("x-should-retry");
    if (B === "true") return !0;
    if (B === "false") return !1;
    if (A.status === 408) return !0;
    if (A.status === 409) return !0;
    if (A.status === 429) return !0;
    if (A.status >= 500) return !0;
    return !1;
  }
  async retryRequest(A, B, Q, D) {
    let Z,
      G = D?.get("retry-after-ms");
    if (G) {
      let I = parseFloat(G);
      if (!Number.isNaN(I)) Z = I;
    }
    let F = D?.get("retry-after");
    if (F && !Z) {
      let I = parseFloat(F);
      if (!Number.isNaN(I)) Z = I * 1000;else Z = Date.parse(F) - Date.now();
    }
    if (!(Z && 0 <= Z && Z < 60000)) {
      let I = A.maxRetries ?? this.maxRetries;
      Z = this.calculateDefaultRetryTimeoutMillis(B, I);
    }
    return await B8B(Z), this.makeRequest(A, B - 1, Q);
  }
  calculateDefaultRetryTimeoutMillis(A, B) {
    let Z = B - A,
      G = Math.min(0.5 * Math.pow(2, Z), 8),
      F = 1 - Math.random() * 0.25;
    return G * F * 1000;
  }
  calculateNonstreamingTimeout(A, B) {
    if (3600000 * A / 128000 > 600000 || B != null && A > B) throw new j9("Streaming is strongly recommended for operations that may token longer than 10 minutes. See https://github.com/anthropics/anthropic-sdk-typescript#long-requests for more details");
    return 600000;
  }
  buildRequest(A, {
    retryCount: B = 0
  } = {}) {
    let Q = {
        ...A
      },
      {
        method: D,
        path: Z,
        query: G,
        defaultBaseURL: F
      } = Q,
      I = this.buildURL(Z, G, F);
    if ("timeout" in Q) A8B("timeout", Q.timeout);
    Q.timeout = Q.timeout ?? this.timeout;
    let {
        bodyHeaders: Y,
        body: W
      } = this.buildBody({
        options: Q
      }),
      J = this.buildHeaders({
        options: A,
        method: D,
        bodyHeaders: Y,
        retryCount: B
      });
    return {
      req: {
        method: D,
        headers: J,
        ...(Q.signal && {
          signal: Q.signal
        }),
        ...(globalThis.ReadableStream && W instanceof globalThis.ReadableStream && {
          duplex: "half"
        }),
        ...(W && {
          body: W
        }),
        ...(this.fetchOptions ?? {}),
        ...(Q.fetchOptions ?? {})
      },
      url: I,
      timeout: Q.timeout
    };
  }
  buildHeaders({
    options: A,
    method: B,
    bodyHeaders: Q,
    retryCount: D
  }) {
    let Z = {};
    if (this.idempotencyHeader && B !== "get") {
      if (!A.idempotencyKey) A.idempotencyKey = this.defaultIdempotencyKey();
      Z[this.idempotencyHeader] = A.idempotencyKey;
    }
    let G = t8([Z, {
      Accept: "application/json",
      "User-Agent": this.getUserAgent(),
      "X-Stainless-Retry-Count": String(D),
      ...(A.timeout ? {
        "X-Stainless-Timeout": String(Math.trunc(A.timeout / 1000))
      } : {}),
      ...F8B(),
      ...(this._options.dangerouslyAllowBrowser ? {
        "anthropic-dangerous-direct-browser-access": "true"
      } : void 0),
      "anthropic-version": "2023-06-01"
    }, this.authHeaders(A), this._options.defaultHeaders, Q, A.headers]);
    return this.validateHeaders(G), G.values;
  }
  buildBody({
    options: {
      body: A,
      headers: B
    }
  }) {
    if (!A) return {
      bodyHeaders: void 0,
      body: void 0
    };
    let Q = t8([B]);
    if (ArrayBuffer.isView(A) || A instanceof ArrayBuffer || A instanceof DataView || typeof A === "string" && Q.values.has("content-type") || A instanceof Blob || A instanceof FormData || A instanceof URLSearchParams || globalThis.ReadableStream && A instanceof globalThis.ReadableStream) return {
      bodyHeaders: void 0,
      body: A
    };else if (typeof A === "object" && (Symbol.asyncIterator in A || Symbol.iterator in A && "next" in A && typeof A.next === "function")) return {
      bodyHeaders: void 0,
      body: Nj1(A)
    };else return xA(this, lj1, "f").call(this, {
      body: A,
      headers: Q
    });
  }
}
W3.Anthropic = IK0;
W3.HUMAN_PROMPT = `

Human:`;
W3.AI_PROMPT = `

Assistant:`;
W3.DEFAULT_TIMEOUT = 600000;
W3.AnthropicError = j9;
W3.APIError = Q6;
W3.APIConnectionError = bP;
W3.APIConnectionTimeoutError = d71;
W3.APIUserAbortError = _F;
W3.NotFoundError = i71;
W3.ConflictError = n71;
W3.RateLimitError = s71;
W3.BadRequestError = c71;
W3.AuthenticationError = l71;
W3.InternalServerError = r71;
W3.PermissionDeniedError = p71;
W3.UnprocessableEntityError = a71;
W3.toFile = Pj1;
module.exports = {
  BD1,
  Mx,
  Nj1,
  Q6,
  W3,
  YX,
  Ym,
  _F,
  aC0,
  bP,
  j9,
  o71,
  t8,
  vP,
  xA,
  zQ
};