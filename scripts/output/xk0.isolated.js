/**
 * Isolated bundle for 'xk0'
 */

import { <PERSON><PERSON><PERSON> as <PERSON><PERSON> } from "node:buffer";
import { ChildProcess as Ke<PERSON> } from "node:child_process";
var Tk0 = A => {
    let B = typeof A;
    if (B === "string") return A;
    if (B === "number") return String(A);
    if (B === "object" && A !== null && !(A instanceof KeB) && "stdout" in A) {
      let Q = typeof A.stdout;
      if (Q === "string") return A.stdout;
      if (CeB.isBuffer(A.stdout)) return A.stdout.toString();
      throw new TypeError(`Unexpected "${Q}" stdout in template expression`);
    }
    throw new TypeError(`Unexpected "${B}" in template expression`);
  },
  Pk0 = (A, B, Q) => Q || A.length === 0 || B.length === 0 ? [...A, ...B] : [...A.slice(0, -1), `${A.at(-1)}${B[0]}`, ...B.slice(1)],
  UeB = ({
    templates: A,
    expressions: B,
    tokens: Q,
    index: D,
    template: Z
  }) => {
    let G = Z ?? A.raw[D],
      F = G.split(EeB).filter(Boolean),
      I = Pk0(Q, F, G.startsWith(" "));
    if (D === B.length) return I;
    let Y = B[D],
      W = Array.isArray(Y) ? Y.map(J => Tk0(J)) : [Tk0(Y)];
    return Pk0(I, W, G.endsWith(" "));
  },
  Rm1 = (A, B) => {
    let Q = [];
    for (let [D, Z] of A.entries()) Q = UeB({
      templates: A,
      expressions: B,
      tokens: Q,
      index: D,
      template: Z
    });
    return Q;
  };
var OeB = ({
    input: A,
    inputFile: B,
    stdio: Q
  }) => A === void 0 && B === void 0 && Q === void 0 ? {
    stdin: "inherit"
  } : {},
  yk0 = (A = {}) => ({
    preferLocal: !0,
    ...OeB(A),
    ...A
  });
function xk0(A) {
  function B(Q, ...D) {
    if (!Array.isArray(Q)) return xk0({
      ...A,
      ...Q
    });
    let [Z, ...G] = Rm1(Q, D);
    return Pm1(Z, G, yk0(A));
  }
  return B.sync = (Q, ...D) => {
    if (!Array.isArray(Q)) throw new TypeError("Please use $(options).sync`command` instead of $.sync(options)`command`.");
    let [Z, ...G] = Rm1(Q, D);
    return Sm1(Z, G, yk0(A));
  }, B;
}
module.exports = {
  xk0
};