{"metadata": {"entryPoint": "K48", "timestamp": "2025-08-10T11:39:09.164Z"}, "isolation": {"totalNodesInCluster": 55, "totalNodesInOriginal": 5283, "isolationRatio": "1.0%", "entryBoundarySize": 2, "entryBoundaryNodes": ["k$", "p0"]}, "externalDependencies": {}, "statistics": {"averageDependenciesPerNode": "3.02", "maxDependencies": 80, "nodeWithMaxDependencies": "ZA1", "top10MostDependedOn": [{"node": "ZA1", "dependencies": 80}, {"node": "ER8", "dependencies": 79}, {"node": "co5", "dependencies": 45}, {"node": "lo5", "dependencies": 45}, {"node": "po5", "dependencies": 45}, {"node": "io5", "dependencies": 45}, {"node": "no5", "dependencies": 45}, {"node": "ao5", "dependencies": 45}, {"node": "dj6", "dependencies": 44}, {"node": "cj6", "dependencies": 44}]}, "dependencyLayers": [{"level": 0, "nodes": ["K48"], "count": 1}, {"level": 1, "nodes": ["Dv", "E$0", "H9", "OLB", "RLB", "Z48", "a1", "bLB", "bZ1", "fLB", "ox1", "p0", "vLB", "xB", "z$0"], "count": 15}, {"level": 2, "nodes": ["A4", "AC", "C48", "D48", "Dv", "E$0", "G48", "H$0", "H11", "H9", "J48", "LLB", "OLB", "PLB", "Q48", "Qv", "Sm", "TD", "TLB", "U$0", "V48", "W48", "X48", "Z48", "bZ1", "cK", "fLB", "h3", "k$", "lE", "mM", "ox1", "rx1", "vLB", "vZ1", "w$0", "xLB", "xZ1", "z$0"], "count": 39}, {"level": 3, "nodes": ["A4", "AC", "D48", "F48", "G48", "H$0", "H11", "I48", "J48", "LLB", "PLB", "Qv", "SLB", "Sm", "TD", "TLB", "U$0", "V48", "W48", "X48", "Y48", "_LB", "cK", "h3", "jLB", "k$", "kLB", "lE", "mM", "vZ1", "w$0", "xLB", "xZ1", "yLB"], "count": 34}, {"level": 4, "nodes": ["$$0", "F48", "I48", "MLB", "SLB", "Y48", "_LB", "jLB", "kLB", "yLB"], "count": 10}, {"level": 5, "nodes": ["$$0", "MLB"], "count": 2}], "nodesList": ["$$0", "A4", "AC", "C48", "D48", "Dv", "E$0", "F48", "G48", "H$0", "H11", "H9", "I48", "J48", "K48", "LLB", "MLB", "OLB", "PLB", "Q48", "Qv", "RLB", "SLB", "Sm", "TD", "TLB", "U$0", "V48", "W48", "X48", "Y48", "Z48", "_LB", "a1", "bLB", "bZ1", "cK", "fLB", "h3", "jLB", "k$", "kLB", "lE", "mM", "ox1", "p0", "rx1", "vLB", "vZ1", "w$0", "xB", "xLB", "xZ1", "yLB", "z$0"]}