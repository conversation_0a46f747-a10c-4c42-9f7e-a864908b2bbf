/**
 * Isolated bundle for 'DC1'
 */

/**
 * Cleaned Main File
 */

const {
  Dy,
  Ht0,
  Yw,
  fi1,
  vB1
} = require("./Yw.isolated.js");
/**
 * Cleaned Main File
 */

/**
 * Cleaned Main File
 */

const {
  F1,
  Iw,
  KY,
  aH,
  zp
} = require("./cJ.isolated.js");
/**
 * Cleaned Main File
 */

import { fileURLToPath as _v9 } from "node:url";
import { posix as Cv9, win32 as ni1 } from "node:path";
import { fileURLToPath as Kv9 } from "node:url";
import { lstatSync as Hv9, readdir as zv9, readdirSync as Ev9, readlinkSync as Uv9, realpathSync as wv9 } from "fs";
import * as $v9 from "node:fs";
import { lstat as Nv9, readdir as Lv9, readlink as Mv9, realpath as Rv9 } from "node:fs/promises";
import { EventEmitter as ci1 } from "node:events";
import Nt0 from "node:stream";
import { StringDecoder as Dv9 } from "node:string_decoder";
var Ut0 = typeof process === "object" && process ? process : {
    stdout: null,
    stderr: null
  },
  Zv9 = A => !!A && typeof A === "object" && (A instanceof nf || A instanceof Nt0 || Gv9(A) || Fv9(A)),
  Gv9 = A => !!A && typeof A === "object" && A instanceof ci1 && typeof A.pipe === "function" && A.pipe !== Nt0.Writable.prototype.pipe,
  Fv9 = A => !!A && typeof A === "object" && A instanceof ci1 && typeof A.write === "function" && typeof A.end === "function",
  qO = Symbol("EOF"),
  NO = Symbol("maybeEmitEnd"),
  Zy = Symbol("emittedEnd"),
  lV1 = Symbol("emittingEnd"),
  bB1 = Symbol("emittedError"),
  pV1 = Symbol("closed"),
  wt0 = Symbol("read"),
  iV1 = Symbol("flush"),
  $t0 = Symbol("flushChunk"),
  Ww = Symbol("encoding"),
  wp = Symbol("decoder"),
  FI = Symbol("flowing"),
  fB1 = Symbol("paused"),
  $p = Symbol("resume"),
  II = Symbol("buffer"),
  lJ = Symbol("pipes"),
  YI = Symbol("bufferLength"),
  hi1 = Symbol("bufferPush"),
  nV1 = Symbol("bufferShift"),
  hW = Symbol("objectMode"),
  lZ = Symbol("destroyed"),
  gi1 = Symbol("error"),
  ui1 = Symbol("emitData"),
  qt0 = Symbol("emitEnd"),
  mi1 = Symbol("emitEnd2"),
  HN = Symbol("async"),
  di1 = Symbol("abort"),
  aV1 = Symbol("aborted"),
  hB1 = Symbol("signal"),
  pf = Symbol("dataListeners"),
  lC = Symbol("discarded"),
  gB1 = A => Promise.resolve().then(A),
  Iv9 = A => A(),
  Yv9 = A => A === "end" || A === "finish" || A === "prefinish",
  Wv9 = A => A instanceof ArrayBuffer || !!A && typeof A === "object" && A.constructor && A.constructor.name === "ArrayBuffer" && A.byteLength >= 0,
  Jv9 = A => !Buffer.isBuffer(A) && ArrayBuffer.isView(A);
class li1 {
  src;
  dest;
  opts;
  ondrain;
  constructor(A, B, Q) {
    this.src = A, this.dest = B, this.opts = Q, this.ondrain = () => A[$p](), this.dest.on("drain", this.ondrain);
  }
  unpipe() {
    this.dest.removeListener("drain", this.ondrain);
  }
  proxyErrors(A) {}
  end() {
    if (this.unpipe(), this.opts.end) this.dest.end();
  }
}
class Lt0 extends li1 {
  unpipe() {
    this.src.removeListener("error", this.proxyErrors), super.unpipe();
  }
  constructor(A, B, Q) {
    super(A, B, Q);
    this.proxyErrors = D => B.emit("error", D), A.on("error", this.proxyErrors);
  }
}
var Xv9 = A => !!A.objectMode,
  Vv9 = A => !A.objectMode && !!A.encoding && A.encoding !== "buffer";
class nf extends ci1 {
  [FI] = !1;
  [fB1] = !1;
  [lJ] = [];
  [II] = [];
  [hW];
  [Ww];
  [HN];
  [wp];
  [qO] = !1;
  [Zy] = !1;
  [lV1] = !1;
  [pV1] = !1;
  [bB1] = null;
  [YI] = 0;
  [lZ] = !1;
  [hB1];
  [aV1] = !1;
  [pf] = 0;
  [lC] = !1;
  writable = !0;
  readable = !0;
  constructor(...A) {
    let B = A[0] || {};
    super();
    if (B.objectMode && typeof B.encoding === "string") throw new TypeError("Encoding and objectMode may not be used together");
    if (Xv9(B)) this[hW] = !0, this[Ww] = null;else if (Vv9(B)) this[Ww] = B.encoding, this[hW] = !1;else this[hW] = !1, this[Ww] = null;
    if (this[HN] = !!B.async, this[wp] = this[Ww] ? new Dv9(this[Ww]) : null, B && B.debugExposeBuffer === !0) Object.defineProperty(this, "buffer", {
      get: () => this[II]
    });
    if (B && B.debugExposePipes === !0) Object.defineProperty(this, "pipes", {
      get: () => this[lJ]
    });
    let {
      signal: Q
    } = B;
    if (Q) if (this[hB1] = Q, Q.aborted) this[di1]();else Q.addEventListener("abort", () => this[di1]());
  }
  get bufferLength() {
    return this[YI];
  }
  get encoding() {
    return this[Ww];
  }
  set encoding(A) {
    throw new Error("Encoding must be set at instantiation time");
  }
  setEncoding(A) {
    throw new Error("Encoding must be set at instantiation time");
  }
  get objectMode() {
    return this[hW];
  }
  set objectMode(A) {
    throw new Error("objectMode must be set at instantiation time");
  }
  get ["async"]() {
    return this[HN];
  }
  set ["async"](A) {
    this[HN] = this[HN] || !!A;
  }
  [di1]() {
    this[aV1] = !0, this.emit("abort", this[hB1]?.reason), this.destroy(this[hB1]?.reason);
  }
  get aborted() {
    return this[aV1];
  }
  set aborted(A) {}
  write(A, B, Q) {
    if (this[aV1]) return !1;
    if (this[qO]) throw new Error("write after end");
    if (this[lZ]) return this.emit("error", Object.assign(new Error("Cannot call write after a stream was destroyed"), {
      code: "ERR_STREAM_DESTROYED"
    })), !0;
    if (typeof B === "function") Q = B, B = "utf8";
    if (!B) B = "utf8";
    let D = this[HN] ? gB1 : Iv9;
    if (!this[hW] && !Buffer.isBuffer(A)) {
      if (Jv9(A)) A = Buffer.from(A.buffer, A.byteOffset, A.byteLength);else if (Wv9(A)) A = Buffer.from(A);else if (typeof A !== "string") throw new Error("Non-contiguous data written to non-objectMode stream");
    }
    if (this[hW]) {
      if (this[FI] && this[YI] !== 0) this[iV1](!0);
      if (this[FI]) this.emit("data", A);else this[hi1](A);
      if (this[YI] !== 0) this.emit("readable");
      if (Q) D(Q);
      return this[FI];
    }
    if (!A.length) {
      if (this[YI] !== 0) this.emit("readable");
      if (Q) D(Q);
      return this[FI];
    }
    if (typeof A === "string" && !(B === this[Ww] && !this[wp]?.lastNeed)) A = Buffer.from(A, B);
    if (Buffer.isBuffer(A) && this[Ww]) A = this[wp].write(A);
    if (this[FI] && this[YI] !== 0) this[iV1](!0);
    if (this[FI]) this.emit("data", A);else this[hi1](A);
    if (this[YI] !== 0) this.emit("readable");
    if (Q) D(Q);
    return this[FI];
  }
  read(A) {
    if (this[lZ]) return null;
    if (this[lC] = !1, this[YI] === 0 || A === 0 || A && A > this[YI]) return this[NO](), null;
    if (this[hW]) A = null;
    if (this[II].length > 1 && !this[hW]) this[II] = [this[Ww] ? this[II].join("") : Buffer.concat(this[II], this[YI])];
    let B = this[wt0](A || null, this[II][0]);
    return this[NO](), B;
  }
  [wt0](A, B) {
    if (this[hW]) this[nV1]();else {
      let Q = B;
      if (A === Q.length || A === null) this[nV1]();else if (typeof Q === "string") this[II][0] = Q.slice(A), B = Q.slice(0, A), this[YI] -= A;else this[II][0] = Q.subarray(A), B = Q.subarray(0, A), this[YI] -= A;
    }
    if (this.emit("data", B), !this[II].length && !this[qO]) this.emit("drain");
    return B;
  }
  end(A, B, Q) {
    if (typeof A === "function") Q = A, A = void 0;
    if (typeof B === "function") Q = B, B = "utf8";
    if (A !== void 0) this.write(A, B);
    if (Q) this.once("end", Q);
    if (this[qO] = !0, this.writable = !1, this[FI] || !this[fB1]) this[NO]();
    return this;
  }
  [$p]() {
    if (this[lZ]) return;
    if (!this[pf] && !this[lJ].length) this[lC] = !0;
    if (this[fB1] = !1, this[FI] = !0, this.emit("resume"), this[II].length) this[iV1]();else if (this[qO]) this[NO]();else this.emit("drain");
  }
  resume() {
    return this[$p]();
  }
  pause() {
    this[FI] = !1, this[fB1] = !0, this[lC] = !1;
  }
  get destroyed() {
    return this[lZ];
  }
  get flowing() {
    return this[FI];
  }
  get paused() {
    return this[fB1];
  }
  [hi1](A) {
    if (this[hW]) this[YI] += 1;else this[YI] += A.length;
    this[II].push(A);
  }
  [nV1]() {
    if (this[hW]) this[YI] -= 1;else this[YI] -= this[II][0].length;
    return this[II].shift();
  }
  [iV1](A = !1) {
    do ; while (this[$t0](this[nV1]()) && this[II].length);
    if (!A && !this[II].length && !this[qO]) this.emit("drain");
  }
  [$t0](A) {
    return this.emit("data", A), this[FI];
  }
  pipe(A, B) {
    if (this[lZ]) return A;
    this[lC] = !1;
    let Q = this[Zy];
    if (B = B || {}, A === Ut0.stdout || A === Ut0.stderr) B.end = !1;else B.end = B.end !== !1;
    if (B.proxyErrors = !!B.proxyErrors, Q) {
      if (B.end) A.end();
    } else if (this[lJ].push(!B.proxyErrors ? new li1(this, A, B) : new Lt0(this, A, B)), this[HN]) gB1(() => this[$p]());else this[$p]();
    return A;
  }
  unpipe(A) {
    let B = this[lJ].find(Q => Q.dest === A);
    if (B) {
      if (this[lJ].length === 1) {
        if (this[FI] && this[pf] === 0) this[FI] = !1;
        this[lJ] = [];
      } else this[lJ].splice(this[lJ].indexOf(B), 1);
      B.unpipe();
    }
  }
  addListener(A, B) {
    return this.on(A, B);
  }
  on(A, B) {
    let Q = super.on(A, B);
    if (A === "data") {
      if (this[lC] = !1, this[pf]++, !this[lJ].length && !this[FI]) this[$p]();
    } else if (A === "readable" && this[YI] !== 0) super.emit("readable");else if (Yv9(A) && this[Zy]) super.emit(A), this.removeAllListeners(A);else if (A === "error" && this[bB1]) {
      let D = B;
      if (this[HN]) gB1(() => D.call(this, this[bB1]));else D.call(this, this[bB1]);
    }
    return Q;
  }
  removeListener(A, B) {
    return this.off(A, B);
  }
  off(A, B) {
    let Q = super.off(A, B);
    if (A === "data") {
      if (this[pf] = this.listeners("data").length, this[pf] === 0 && !this[lC] && !this[lJ].length) this[FI] = !1;
    }
    return Q;
  }
  removeAllListeners(A) {
    let B = super.removeAllListeners(A);
    if (A === "data" || A === void 0) {
      if (this[pf] = 0, !this[lC] && !this[lJ].length) this[FI] = !1;
    }
    return B;
  }
  get emittedEnd() {
    return this[Zy];
  }
  [NO]() {
    if (!this[lV1] && !this[Zy] && !this[lZ] && this[II].length === 0 && this[qO]) {
      if (this[lV1] = !0, this.emit("end"), this.emit("prefinish"), this.emit("finish"), this[pV1]) this.emit("close");
      this[lV1] = !1;
    }
  }
  emit(A, ...B) {
    let Q = B[0];
    if (A !== "error" && A !== "close" && A !== lZ && this[lZ]) return !1;else if (A === "data") return !this[hW] && !Q ? !1 : this[HN] ? (gB1(() => this[ui1](Q)), !0) : this[ui1](Q);else if (A === "end") return this[qt0]();else if (A === "close") {
      if (this[pV1] = !0, !this[Zy] && !this[lZ]) return !1;
      let Z = super.emit("close");
      return this.removeAllListeners("close"), Z;
    } else if (A === "error") {
      this[bB1] = Q, super.emit(gi1, Q);
      let Z = !this[hB1] || this.listeners("error").length ? super.emit("error", Q) : !1;
      return this[NO](), Z;
    } else if (A === "resume") {
      let Z = super.emit("resume");
      return this[NO](), Z;
    } else if (A === "finish" || A === "prefinish") {
      let Z = super.emit(A);
      return this.removeAllListeners(A), Z;
    }
    let D = super.emit(A, ...B);
    return this[NO](), D;
  }
  [ui1](A) {
    for (let Q of this[lJ]) if (Q.dest.write(A) === !1) this.pause();
    let B = this[lC] ? !1 : super.emit("data", A);
    return this[NO](), B;
  }
  [qt0]() {
    if (this[Zy]) return !1;
    return this[Zy] = !0, this.readable = !1, this[HN] ? (gB1(() => this[mi1]()), !0) : this[mi1]();
  }
  [mi1]() {
    if (this[wp]) {
      let B = this[wp].end();
      if (B) {
        for (let Q of this[lJ]) Q.dest.write(B);
        if (!this[lC]) super.emit("data", B);
      }
    }
    for (let B of this[lJ]) B.end();
    let A = super.emit("end");
    return this.removeAllListeners("end"), A;
  }
  async collect() {
    let A = Object.assign([], {
      dataLength: 0
    });
    if (!this[hW]) A.dataLength = 0;
    let B = this.promise();
    return this.on("data", Q => {
      if (A.push(Q), !this[hW]) A.dataLength += Q.length;
    }), await B, A;
  }
  async concat() {
    if (this[hW]) throw new Error("cannot concat in objectMode");
    let A = await this.collect();
    return this[Ww] ? A.join("") : Buffer.concat(A, A.dataLength);
  }
  async promise() {
    return new Promise((A, B) => {
      this.on(lZ, () => B(new Error("stream destroyed"))), this.on("error", Q => B(Q)), this.on("end", () => A());
    });
  }
  [Symbol.asyncIterator]() {
    this[lC] = !1;
    let A = !1,
      B = async () => {
        return this.pause(), A = !0, {
          value: void 0,
          done: !0
        };
      };
    return {
      next: () => {
        if (A) return B();
        let D = this.read();
        if (D !== null) return Promise.resolve({
          done: !1,
          value: D
        });
        if (this[qO]) return B();
        let Z,
          G,
          F = J => {
            this.off("data", I), this.off("end", Y), this.off(lZ, W), B(), G(J);
          },
          I = J => {
            this.off("error", F), this.off("end", Y), this.off(lZ, W), this.pause(), Z({
              value: J,
              done: !!this[qO]
            });
          },
          Y = () => {
            this.off("error", F), this.off("data", I), this.off(lZ, W), B(), Z({
              done: !0,
              value: void 0
            });
          },
          W = () => F(new Error("stream destroyed"));
        return new Promise((J, X) => {
          G = X, Z = J, this.once(lZ, W), this.once("error", F), this.once("end", Y), this.once("data", I);
        });
      },
      throw: B,
      return: B,
      [Symbol.asyncIterator]() {
        return this;
      }
    };
  }
  [Symbol.iterator]() {
    this[lC] = !1;
    let A = !1,
      B = () => {
        return this.pause(), this.off(gi1, B), this.off(lZ, B), this.off("end", B), A = !0, {
          done: !0,
          value: void 0
        };
      },
      Q = () => {
        if (A) return B();
        let D = this.read();
        return D === null ? B() : {
          done: !1,
          value: D
        };
      };
    return this.once("end", B), this.once(gi1, B), this.once(lZ, B), {
      next: Q,
      throw: B,
      return: B,
      [Symbol.iterator]() {
        return this;
      }
    };
  }
  destroy(A) {
    if (this[lZ]) {
      if (A) this.emit("error", A);else this.emit(lZ);
      return this;
    }
    this[lZ] = !0, this[lC] = !0, this[II].length = 0, this[YI] = 0;
    let B = this;
    if (typeof B.close === "function" && !this[pV1]) B.close();
    if (A) this.emit("error", A);else this.emit(lZ);
    return this;
  }
  static get isStream() {
    return Zv9;
  }
}
var qv9 = wv9.native,
  mB1 = {
    lstatSync: Hv9,
    readdir: zv9,
    readdirSync: Ev9,
    readlinkSync: Uv9,
    realpathSync: qv9,
    promises: {
      lstat: Nv9,
      readdir: Lv9,
      readlink: Mv9,
      realpath: Rv9
    }
  },
  Pt0 = A => !A || A === mB1 || A === $v9 ? mB1 : {
    ...mB1,
    ...A,
    promises: {
      ...mB1.promises,
      ...(A.promises || {})
    }
  },
  St0 = /^\\\\\?\\([a-z]:)\\?$/i,
  Ov9 = A => A.replace(/\//g, "\\").replace(St0, "$1\\"),
  Tv9 = /[\\\/]/,
  rH = 0,
  jt0 = 1,
  yt0 = 2,
  zN = 4,
  kt0 = 6,
  _t0 = 8,
  af = 10,
  xt0 = 12,
  sH = 15,
  uB1 = ~sH,
  pi1 = 16,
  Mt0 = 32,
  dB1 = 64,
  Jw = 128,
  sV1 = 256,
  oV1 = 512,
  Rt0 = dB1 | Jw | oV1,
  Pv9 = 1023,
  ii1 = A => A.isFile() ? _t0 : A.isDirectory() ? zN : A.isSymbolicLink() ? af : A.isCharacterDevice() ? yt0 : A.isBlockDevice() ? kt0 : A.isSocket() ? xt0 : A.isFIFO() ? jt0 : rH,
  Ot0 = new Map(),
  cB1 = A => {
    let B = Ot0.get(A);
    if (B) return B;
    let Q = A.normalize("NFKD");
    return Ot0.set(A, Q), Q;
  },
  Tt0 = new Map(),
  rV1 = A => {
    let B = Tt0.get(A);
    if (B) return B;
    let Q = cB1(A.toLowerCase());
    return Tt0.set(A, Q), Q;
  };
class ai1 extends Yw {
  constructor() {
    super({
      max: 256
    });
  }
}
class vt0 extends Yw {
  constructor(A = 16384) {
    super({
      maxSize: A,
      sizeCalculation: B => B.length + 1
    });
  }
}
var bt0 = Symbol("PathScurry setAsCwd");
class pJ {
  name;
  root;
  roots;
  parent;
  nocase;
  isCWD = !1;
  #A;
  #B;
  get dev() {
    return this.#B;
  }
  #Q;
  get mode() {
    return this.#Q;
  }
  #D;
  get nlink() {
    return this.#D;
  }
  #Z;
  get uid() {
    return this.#Z;
  }
  #Y;
  get gid() {
    return this.#Y;
  }
  #G;
  get rdev() {
    return this.#G;
  }
  #J;
  get blksize() {
    return this.#J;
  }
  #W;
  get ino() {
    return this.#W;
  }
  #X;
  get size() {
    return this.#X;
  }
  #I;
  get blocks() {
    return this.#I;
  }
  #E;
  get atimeMs() {
    return this.#E;
  }
  #U;
  get mtimeMs() {
    return this.#U;
  }
  #K;
  get ctimeMs() {
    return this.#K;
  }
  #C;
  get birthtimeMs() {
    return this.#C;
  }
  #L;
  get atime() {
    return this.#L;
  }
  #z;
  get mtime() {
    return this.#z;
  }
  #M;
  get ctime() {
    return this.#M;
  }
  #R;
  get birthtime() {
    return this.#R;
  }
  #$;
  #q;
  #N;
  #H;
  #S;
  #O;
  #F;
  #k;
  #w;
  #j;
  get parentPath() {
    return (this.parent || this).fullpath();
  }
  get path() {
    return this.parentPath;
  }
  constructor(A, B = rH, Q, D, Z, G, F) {
    if (this.name = A, this.#$ = Z ? rV1(A) : cB1(A), this.#F = B & Pv9, this.nocase = Z, this.roots = D, this.root = Q || this, this.#k = G, this.#N = F.fullpath, this.#S = F.relative, this.#O = F.relativePosix, this.parent = F.parent, this.parent) this.#A = this.parent.#A;else this.#A = Pt0(F.fs);
  }
  depth() {
    if (this.#q !== void 0) return this.#q;
    if (!this.parent) return this.#q = 0;
    return this.#q = this.parent.depth() + 1;
  }
  childrenCache() {
    return this.#k;
  }
  resolve(A) {
    if (!A) return this;
    let B = this.getRootString(A),
      D = A.substring(B.length).split(this.splitSep);
    return B ? this.getRoot(B).#_(D) : this.#_(D);
  }
  #_(A) {
    let B = this;
    for (let Q of A) B = B.child(Q);
    return B;
  }
  children() {
    let A = this.#k.get(this);
    if (A) return A;
    let B = Object.assign([], {
      provisional: 0
    });
    return this.#k.set(this, B), this.#F &= ~pi1, B;
  }
  child(A, B) {
    if (A === "" || A === ".") return this;
    if (A === "..") return this.parent || this;
    let Q = this.children(),
      D = this.nocase ? rV1(A) : cB1(A);
    for (let I of Q) if (I.#$ === D) return I;
    let Z = this.parent ? this.sep : "",
      G = this.#N ? this.#N + Z + A : void 0,
      F = this.newChild(A, rH, {
        ...B,
        parent: this,
        fullpath: G
      });
    if (!this.canReaddir()) F.#F |= Jw;
    return Q.push(F), F;
  }
  relative() {
    if (this.isCWD) return "";
    if (this.#S !== void 0) return this.#S;
    let A = this.name,
      B = this.parent;
    if (!B) return this.#S = this.name;
    let Q = B.relative();
    return Q + (!Q || !B.parent ? "" : this.sep) + A;
  }
  relativePosix() {
    if (this.sep === "/") return this.relative();
    if (this.isCWD) return "";
    if (this.#O !== void 0) return this.#O;
    let A = this.name,
      B = this.parent;
    if (!B) return this.#O = this.fullpathPosix();
    let Q = B.relativePosix();
    return Q + (!Q || !B.parent ? "" : "/") + A;
  }
  fullpath() {
    if (this.#N !== void 0) return this.#N;
    let A = this.name,
      B = this.parent;
    if (!B) return this.#N = this.name;
    let D = B.fullpath() + (!B.parent ? "" : this.sep) + A;
    return this.#N = D;
  }
  fullpathPosix() {
    if (this.#H !== void 0) return this.#H;
    if (this.sep === "/") return this.#H = this.fullpath();
    if (!this.parent) {
      let D = this.fullpath().replace(/\\/g, "/");
      if (/^[a-z]:\//i.test(D)) return this.#H = `//?/${D}`;else return this.#H = D;
    }
    let A = this.parent,
      B = A.fullpathPosix(),
      Q = B + (!B || !A.parent ? "" : "/") + this.name;
    return this.#H = Q;
  }
  isUnknown() {
    return (this.#F & sH) === rH;
  }
  isType(A) {
    return this[`is${A}`]();
  }
  getType() {
    return this.isUnknown() ? "Unknown" : this.isDirectory() ? "Directory" : this.isFile() ? "File" : this.isSymbolicLink() ? "SymbolicLink" : this.isFIFO() ? "FIFO" : this.isCharacterDevice() ? "CharacterDevice" : this.isBlockDevice() ? "BlockDevice" : this.isSocket() ? "Socket" : "Unknown";
  }
  isFile() {
    return (this.#F & sH) === _t0;
  }
  isDirectory() {
    return (this.#F & sH) === zN;
  }
  isCharacterDevice() {
    return (this.#F & sH) === yt0;
  }
  isBlockDevice() {
    return (this.#F & sH) === kt0;
  }
  isFIFO() {
    return (this.#F & sH) === jt0;
  }
  isSocket() {
    return (this.#F & sH) === xt0;
  }
  isSymbolicLink() {
    return (this.#F & af) === af;
  }
  lstatCached() {
    return this.#F & Mt0 ? this : void 0;
  }
  readlinkCached() {
    return this.#w;
  }
  realpathCached() {
    return this.#j;
  }
  readdirCached() {
    let A = this.children();
    return A.slice(0, A.provisional);
  }
  canReadlink() {
    if (this.#w) return !0;
    if (!this.parent) return !1;
    let A = this.#F & sH;
    return !(A !== rH && A !== af || this.#F & sV1 || this.#F & Jw);
  }
  calledReaddir() {
    return !!(this.#F & pi1);
  }
  isENOENT() {
    return !!(this.#F & Jw);
  }
  isNamed(A) {
    return !this.nocase ? this.#$ === cB1(A) : this.#$ === rV1(A);
  }
  async readlink() {
    let A = this.#w;
    if (A) return A;
    if (!this.canReadlink()) return;
    if (!this.parent) return;
    try {
      let B = await this.#A.promises.readlink(this.fullpath()),
        Q = (await this.parent.realpath())?.resolve(B);
      if (Q) return this.#w = Q;
    } catch (B) {
      this.#V(B.code);
      return;
    }
  }
  readlinkSync() {
    let A = this.#w;
    if (A) return A;
    if (!this.canReadlink()) return;
    if (!this.parent) return;
    try {
      let B = this.#A.readlinkSync(this.fullpath()),
        Q = this.parent.realpathSync()?.resolve(B);
      if (Q) return this.#w = Q;
    } catch (B) {
      this.#V(B.code);
      return;
    }
  }
  #x(A) {
    this.#F |= pi1;
    for (let B = A.provisional; B < A.length; B++) {
      let Q = A[B];
      if (Q) Q.#v();
    }
  }
  #v() {
    if (this.#F & Jw) return;
    this.#F = (this.#F | Jw) & uB1, this.#T();
  }
  #T() {
    let A = this.children();
    A.provisional = 0;
    for (let B of A) B.#v();
  }
  #P() {
    this.#F |= oV1, this.#b();
  }
  #b() {
    if (this.#F & dB1) return;
    let A = this.#F;
    if ((A & sH) === zN) A &= uB1;
    this.#F = A | dB1, this.#T();
  }
  #f(A = "") {
    if (A === "ENOTDIR" || A === "EPERM") this.#b();else if (A === "ENOENT") this.#v();else this.children().provisional = 0;
  }
  #h(A = "") {
    if (A === "ENOTDIR") this.parent.#b();else if (A === "ENOENT") this.#v();
  }
  #V(A = "") {
    let B = this.#F;
    if (B |= sV1, A === "ENOENT") B |= Jw;
    if (A === "EINVAL" || A === "UNKNOWN") B &= uB1;
    if (this.#F = B, A === "ENOTDIR" && this.parent) this.parent.#b();
  }
  #g(A, B) {
    return this.#y(A, B) || this.#u(A, B);
  }
  #u(A, B) {
    let Q = ii1(A),
      D = this.newChild(A.name, Q, {
        parent: this
      }),
      Z = D.#F & sH;
    if (Z !== zN && Z !== af && Z !== rH) D.#F |= dB1;
    return B.unshift(D), B.provisional++, D;
  }
  #y(A, B) {
    for (let Q = B.provisional; Q < B.length; Q++) {
      let D = B[Q];
      if ((this.nocase ? rV1(A.name) : cB1(A.name)) !== D.#$) continue;
      return this.#m(A, D, Q, B);
    }
  }
  #m(A, B, Q, D) {
    let Z = B.name;
    if (B.#F = B.#F & uB1 | ii1(A), Z !== A.name) B.name = A.name;
    if (Q !== D.provisional) {
      if (Q === D.length - 1) D.pop();else D.splice(Q, 1);
      D.unshift(B);
    }
    return D.provisional++, B;
  }
  async lstat() {
    if ((this.#F & Jw) === 0) try {
      return this.#p(await this.#A.promises.lstat(this.fullpath())), this;
    } catch (A) {
      this.#h(A.code);
    }
  }
  lstatSync() {
    if ((this.#F & Jw) === 0) try {
      return this.#p(this.#A.lstatSync(this.fullpath())), this;
    } catch (A) {
      this.#h(A.code);
    }
  }
  #p(A) {
    let {
      atime: B,
      atimeMs: Q,
      birthtime: D,
      birthtimeMs: Z,
      blksize: G,
      blocks: F,
      ctime: I,
      ctimeMs: Y,
      dev: W,
      gid: J,
      ino: X,
      mode: V,
      mtime: C,
      mtimeMs: K,
      nlink: H,
      rdev: z,
      size: $,
      uid: L
    } = A;
    this.#L = B, this.#E = Q, this.#R = D, this.#C = Z, this.#J = G, this.#I = F, this.#M = I, this.#K = Y, this.#B = W, this.#Y = J, this.#W = X, this.#Q = V, this.#z = C, this.#U = K, this.#D = H, this.#G = z, this.#X = $, this.#Z = L;
    let N = ii1(A);
    if (this.#F = this.#F & uB1 | N | Mt0, N !== rH && N !== zN && N !== af) this.#F |= dB1;
  }
  #c = [];
  #l = !1;
  #i(A) {
    this.#l = !1;
    let B = this.#c.slice();
    this.#c.length = 0, B.forEach(Q => Q(null, A));
  }
  readdirCB(A, B = !1) {
    if (!this.canReaddir()) {
      if (B) A(null, []);else queueMicrotask(() => A(null, []));
      return;
    }
    let Q = this.children();
    if (this.calledReaddir()) {
      let Z = Q.slice(0, Q.provisional);
      if (B) A(null, Z);else queueMicrotask(() => A(null, Z));
      return;
    }
    if (this.#c.push(A), this.#l) return;
    this.#l = !0;
    let D = this.fullpath();
    this.#A.readdir(D, {
      withFileTypes: !0
    }, (Z, G) => {
      if (Z) this.#f(Z.code), Q.provisional = 0;else {
        for (let F of G) this.#g(F, Q);
        this.#x(Q);
      }
      this.#i(Q.slice(0, Q.provisional));
      return;
    });
  }
  #d;
  async readdir() {
    if (!this.canReaddir()) return [];
    let A = this.children();
    if (this.calledReaddir()) return A.slice(0, A.provisional);
    let B = this.fullpath();
    if (this.#d) await this.#d;else {
      let Q = () => {};
      this.#d = new Promise(D => Q = D);
      try {
        for (let D of await this.#A.promises.readdir(B, {
          withFileTypes: !0
        })) this.#g(D, A);
        this.#x(A);
      } catch (D) {
        this.#f(D.code), A.provisional = 0;
      }
      this.#d = void 0, Q();
    }
    return A.slice(0, A.provisional);
  }
  readdirSync() {
    if (!this.canReaddir()) return [];
    let A = this.children();
    if (this.calledReaddir()) return A.slice(0, A.provisional);
    let B = this.fullpath();
    try {
      for (let Q of this.#A.readdirSync(B, {
        withFileTypes: !0
      })) this.#g(Q, A);
      this.#x(A);
    } catch (Q) {
      this.#f(Q.code), A.provisional = 0;
    }
    return A.slice(0, A.provisional);
  }
  canReaddir() {
    if (this.#F & Rt0) return !1;
    let A = sH & this.#F;
    if (!(A === rH || A === zN || A === af)) return !1;
    return !0;
  }
  shouldWalk(A, B) {
    return (this.#F & zN) === zN && !(this.#F & Rt0) && !A.has(this) && (!B || B(this));
  }
  async realpath() {
    if (this.#j) return this.#j;
    if ((oV1 | sV1 | Jw) & this.#F) return;
    try {
      let A = await this.#A.promises.realpath(this.fullpath());
      return this.#j = this.resolve(A);
    } catch (A) {
      this.#P();
    }
  }
  realpathSync() {
    if (this.#j) return this.#j;
    if ((oV1 | sV1 | Jw) & this.#F) return;
    try {
      let A = this.#A.realpathSync(this.fullpath());
      return this.#j = this.resolve(A);
    } catch (A) {
      this.#P();
    }
  }
  [bt0](A) {
    if (A === this) return;
    A.isCWD = !1, this.isCWD = !0;
    let B = new Set([]),
      Q = [],
      D = this;
    while (D && D.parent) B.add(D), D.#S = Q.join(this.sep), D.#O = Q.join("/"), D = D.parent, Q.push("..");
    D = A;
    while (D && D.parent && !B.has(D)) D.#S = void 0, D.#O = void 0, D = D.parent;
  }
}
class tV1 extends pJ {
  sep = "\\";
  splitSep = Tv9;
  constructor(A, B = rH, Q, D, Z, G, F) {
    super(A, B, Q, D, Z, G, F);
  }
  newChild(A, B = rH, Q = {}) {
    return new tV1(A, B, this.root, this.roots, this.nocase, this.childrenCache(), Q);
  }
  getRootString(A) {
    return ni1.parse(A).root;
  }
  getRoot(A) {
    if (A = Ov9(A.toUpperCase()), A === this.root.name) return this.root;
    for (let [B, Q] of Object.entries(this.roots)) if (this.sameRoot(A, B)) return this.roots[A] = Q;
    return this.roots[A] = new lB1(A, this).root;
  }
  sameRoot(A, B = this.root.name) {
    return A = A.toUpperCase().replace(/\//g, "\\").replace(St0, "$1\\"), A === B;
  }
}
class eV1 extends pJ {
  splitSep = "/";
  sep = "/";
  constructor(A, B = rH, Q, D, Z, G, F) {
    super(A, B, Q, D, Z, G, F);
  }
  getRootString(A) {
    return A.startsWith("/") ? "/" : "";
  }
  getRoot(A) {
    return this.root;
  }
  newChild(A, B = rH, Q = {}) {
    return new eV1(A, B, this.root, this.roots, this.nocase, this.childrenCache(), Q);
  }
}
class si1 {
  root;
  rootPath;
  roots;
  cwd;
  #A;
  #B;
  #Q;
  nocase;
  #D;
  constructor(A = process.cwd(), B, Q, {
    nocase: D,
    childrenCacheSize: Z = 16384,
    fs: G = mB1
  } = {}) {
    if (this.#D = Pt0(G), A instanceof URL || A.startsWith("file://")) A = Kv9(A);
    let F = B.resolve(A);
    this.roots = Object.create(null), this.rootPath = this.parseRootPath(F), this.#A = new ai1(), this.#B = new ai1(), this.#Q = new vt0(Z);
    let I = F.substring(this.rootPath.length).split(Q);
    if (I.length === 1 && !I[0]) I.pop();
    if (D === void 0) throw new TypeError("must provide nocase setting to PathScurryBase ctor");
    this.nocase = D, this.root = this.newRoot(this.#D), this.roots[this.rootPath] = this.root;
    let Y = this.root,
      W = I.length - 1,
      J = B.sep,
      X = this.rootPath,
      V = !1;
    for (let C of I) {
      let K = W--;
      Y = Y.child(C, {
        relative: new Array(K).fill("..").join(J),
        relativePosix: new Array(K).fill("..").join("/"),
        fullpath: X += (V ? "" : J) + C
      }), V = !0;
    }
    this.cwd = Y;
  }
  depth(A = this.cwd) {
    if (typeof A === "string") A = this.cwd.resolve(A);
    return A.depth();
  }
  childrenCache() {
    return this.#Q;
  }
  resolve(...A) {
    let B = "";
    for (let Z = A.length - 1; Z >= 0; Z--) {
      let G = A[Z];
      if (!G || G === ".") continue;
      if (B = B ? `${G}/${B}` : G, this.isAbsolute(G)) break;
    }
    let Q = this.#A.get(B);
    if (Q !== void 0) return Q;
    let D = this.cwd.resolve(B).fullpath();
    return this.#A.set(B, D), D;
  }
  resolvePosix(...A) {
    let B = "";
    for (let Z = A.length - 1; Z >= 0; Z--) {
      let G = A[Z];
      if (!G || G === ".") continue;
      if (B = B ? `${G}/${B}` : G, this.isAbsolute(G)) break;
    }
    let Q = this.#B.get(B);
    if (Q !== void 0) return Q;
    let D = this.cwd.resolve(B).fullpathPosix();
    return this.#B.set(B, D), D;
  }
  relative(A = this.cwd) {
    if (typeof A === "string") A = this.cwd.resolve(A);
    return A.relative();
  }
  relativePosix(A = this.cwd) {
    if (typeof A === "string") A = this.cwd.resolve(A);
    return A.relativePosix();
  }
  basename(A = this.cwd) {
    if (typeof A === "string") A = this.cwd.resolve(A);
    return A.name;
  }
  dirname(A = this.cwd) {
    if (typeof A === "string") A = this.cwd.resolve(A);
    return (A.parent || A).fullpath();
  }
  async readdir(A = this.cwd, B = {
    withFileTypes: !0
  }) {
    if (typeof A === "string") A = this.cwd.resolve(A);else if (!(A instanceof pJ)) B = A, A = this.cwd;
    let {
      withFileTypes: Q
    } = B;
    if (!A.canReaddir()) return [];else {
      let D = await A.readdir();
      return Q ? D : D.map(Z => Z.name);
    }
  }
  readdirSync(A = this.cwd, B = {
    withFileTypes: !0
  }) {
    if (typeof A === "string") A = this.cwd.resolve(A);else if (!(A instanceof pJ)) B = A, A = this.cwd;
    let {
      withFileTypes: Q = !0
    } = B;
    if (!A.canReaddir()) return [];else if (Q) return A.readdirSync();else return A.readdirSync().map(D => D.name);
  }
  async lstat(A = this.cwd) {
    if (typeof A === "string") A = this.cwd.resolve(A);
    return A.lstat();
  }
  lstatSync(A = this.cwd) {
    if (typeof A === "string") A = this.cwd.resolve(A);
    return A.lstatSync();
  }
  async readlink(A = this.cwd, {
    withFileTypes: B
  } = {
    withFileTypes: !1
  }) {
    if (typeof A === "string") A = this.cwd.resolve(A);else if (!(A instanceof pJ)) B = A.withFileTypes, A = this.cwd;
    let Q = await A.readlink();
    return B ? Q : Q?.fullpath();
  }
  readlinkSync(A = this.cwd, {
    withFileTypes: B
  } = {
    withFileTypes: !1
  }) {
    if (typeof A === "string") A = this.cwd.resolve(A);else if (!(A instanceof pJ)) B = A.withFileTypes, A = this.cwd;
    let Q = A.readlinkSync();
    return B ? Q : Q?.fullpath();
  }
  async realpath(A = this.cwd, {
    withFileTypes: B
  } = {
    withFileTypes: !1
  }) {
    if (typeof A === "string") A = this.cwd.resolve(A);else if (!(A instanceof pJ)) B = A.withFileTypes, A = this.cwd;
    let Q = await A.realpath();
    return B ? Q : Q?.fullpath();
  }
  realpathSync(A = this.cwd, {
    withFileTypes: B
  } = {
    withFileTypes: !1
  }) {
    if (typeof A === "string") A = this.cwd.resolve(A);else if (!(A instanceof pJ)) B = A.withFileTypes, A = this.cwd;
    let Q = A.realpathSync();
    return B ? Q : Q?.fullpath();
  }
  async walk(A = this.cwd, B = {}) {
    if (typeof A === "string") A = this.cwd.resolve(A);else if (!(A instanceof pJ)) B = A, A = this.cwd;
    let {
        withFileTypes: Q = !0,
        follow: D = !1,
        filter: Z,
        walkFilter: G
      } = B,
      F = [];
    if (!Z || Z(A)) F.push(Q ? A : A.fullpath());
    let I = new Set(),
      Y = (J, X) => {
        I.add(J), J.readdirCB((V, C) => {
          if (V) return X(V);
          let K = C.length;
          if (!K) return X();
          let H = () => {
            if (--K === 0) X();
          };
          for (let z of C) {
            if (!Z || Z(z)) F.push(Q ? z : z.fullpath());
            if (D && z.isSymbolicLink()) z.realpath().then($ => $?.isUnknown() ? $.lstat() : $).then($ => $?.shouldWalk(I, G) ? Y($, H) : H());else if (z.shouldWalk(I, G)) Y(z, H);else H();
          }
        }, !0);
      },
      W = A;
    return new Promise((J, X) => {
      Y(W, V => {
        if (V) return X(V);
        J(F);
      });
    });
  }
  walkSync(A = this.cwd, B = {}) {
    if (typeof A === "string") A = this.cwd.resolve(A);else if (!(A instanceof pJ)) B = A, A = this.cwd;
    let {
        withFileTypes: Q = !0,
        follow: D = !1,
        filter: Z,
        walkFilter: G
      } = B,
      F = [];
    if (!Z || Z(A)) F.push(Q ? A : A.fullpath());
    let I = new Set([A]);
    for (let Y of I) {
      let W = Y.readdirSync();
      for (let J of W) {
        if (!Z || Z(J)) F.push(Q ? J : J.fullpath());
        let X = J;
        if (J.isSymbolicLink()) {
          if (!(D && (X = J.realpathSync()))) continue;
          if (X.isUnknown()) X.lstatSync();
        }
        if (X.shouldWalk(I, G)) I.add(X);
      }
    }
    return F;
  }
  [Symbol.asyncIterator]() {
    return this.iterate();
  }
  iterate(A = this.cwd, B = {}) {
    if (typeof A === "string") A = this.cwd.resolve(A);else if (!(A instanceof pJ)) B = A, A = this.cwd;
    return this.stream(A, B)[Symbol.asyncIterator]();
  }
  [Symbol.iterator]() {
    return this.iterateSync();
  }
  *iterateSync(A = this.cwd, B = {}) {
    if (typeof A === "string") A = this.cwd.resolve(A);else if (!(A instanceof pJ)) B = A, A = this.cwd;
    let {
      withFileTypes: Q = !0,
      follow: D = !1,
      filter: Z,
      walkFilter: G
    } = B;
    if (!Z || Z(A)) yield Q ? A : A.fullpath();
    let F = new Set([A]);
    for (let I of F) {
      let Y = I.readdirSync();
      for (let W of Y) {
        if (!Z || Z(W)) yield Q ? W : W.fullpath();
        let J = W;
        if (W.isSymbolicLink()) {
          if (!(D && (J = W.realpathSync()))) continue;
          if (J.isUnknown()) J.lstatSync();
        }
        if (J.shouldWalk(F, G)) F.add(J);
      }
    }
  }
  stream(A = this.cwd, B = {}) {
    if (typeof A === "string") A = this.cwd.resolve(A);else if (!(A instanceof pJ)) B = A, A = this.cwd;
    let {
        withFileTypes: Q = !0,
        follow: D = !1,
        filter: Z,
        walkFilter: G
      } = B,
      F = new nf({
        objectMode: !0
      });
    if (!Z || Z(A)) F.write(Q ? A : A.fullpath());
    let I = new Set(),
      Y = [A],
      W = 0,
      J = () => {
        let X = !1;
        while (!X) {
          let V = Y.shift();
          if (!V) {
            if (W === 0) F.end();
            return;
          }
          W++, I.add(V);
          let C = (H, z, $ = !1) => {
              if (H) return F.emit("error", H);
              if (D && !$) {
                let L = [];
                for (let N of z) if (N.isSymbolicLink()) L.push(N.realpath().then(O => O?.isUnknown() ? O.lstat() : O));
                if (L.length) {
                  Promise.all(L).then(() => C(null, z, !0));
                  return;
                }
              }
              for (let L of z) if (L && (!Z || Z(L))) {
                if (!F.write(Q ? L : L.fullpath())) X = !0;
              }
              W--;
              for (let L of z) {
                let N = L.realpathCached() || L;
                if (N.shouldWalk(I, G)) Y.push(N);
              }
              if (X && !F.flowing) F.once("drain", J);else if (!K) J();
            },
            K = !0;
          V.readdirCB(C, !0), K = !1;
        }
      };
    return J(), F;
  }
  streamSync(A = this.cwd, B = {}) {
    if (typeof A === "string") A = this.cwd.resolve(A);else if (!(A instanceof pJ)) B = A, A = this.cwd;
    let {
        withFileTypes: Q = !0,
        follow: D = !1,
        filter: Z,
        walkFilter: G
      } = B,
      F = new nf({
        objectMode: !0
      }),
      I = new Set();
    if (!Z || Z(A)) F.write(Q ? A : A.fullpath());
    let Y = [A],
      W = 0,
      J = () => {
        let X = !1;
        while (!X) {
          let V = Y.shift();
          if (!V) {
            if (W === 0) F.end();
            return;
          }
          W++, I.add(V);
          let C = V.readdirSync();
          for (let K of C) if (!Z || Z(K)) {
            if (!F.write(Q ? K : K.fullpath())) X = !0;
          }
          W--;
          for (let K of C) {
            let H = K;
            if (K.isSymbolicLink()) {
              if (!(D && (H = K.realpathSync()))) continue;
              if (H.isUnknown()) H.lstatSync();
            }
            if (H.shouldWalk(I, G)) Y.push(H);
          }
        }
        if (X && !F.flowing) F.once("drain", J);
      };
    return J(), F;
  }
  chdir(A = this.cwd) {
    let B = this.cwd;
    this.cwd = typeof A === "string" ? this.cwd.resolve(A) : A, this.cwd[bt0](B);
  }
}
class lB1 extends si1 {
  sep = "\\";
  constructor(A = process.cwd(), B = {}) {
    let {
      nocase: Q = !0
    } = B;
    super(A, ni1, "\\", {
      ...B,
      nocase: Q
    });
    this.nocase = Q;
    for (let D = this.cwd; D; D = D.parent) D.nocase = this.nocase;
  }
  parseRootPath(A) {
    return ni1.parse(A).root.toUpperCase();
  }
  newRoot(A) {
    return new tV1(this.rootPath, zN, void 0, this.roots, this.nocase, this.childrenCache(), {
      fs: A
    });
  }
  isAbsolute(A) {
    return A.startsWith("/") || A.startsWith("\\") || /^[a-z]:(\/|\\)/i.test(A);
  }
}
class pB1 extends si1 {
  sep = "/";
  constructor(A = process.cwd(), B = {}) {
    let {
      nocase: Q = !1
    } = B;
    super(A, Cv9, "/", {
      ...B,
      nocase: Q
    });
    this.nocase = Q;
  }
  parseRootPath(A) {
    return "/";
  }
  newRoot(A) {
    return new eV1(this.rootPath, zN, void 0, this.roots, this.nocase, this.childrenCache(), {
      fs: A
    });
  }
  isAbsolute(A) {
    return A.startsWith("/");
  }
}
class AC1 extends pB1 {
  constructor(A = process.cwd(), B = {}) {
    let {
      nocase: Q = !0
    } = B;
    super(A, {
      ...B,
      nocase: Q
    });
  }
}
var Co8 = process.platform === "win32" ? tV1 : eV1,
  ft0 = process.platform === "win32" ? lB1 : process.platform === "darwin" ? AC1 : pB1;
var Sv9 = A => A.length >= 1,
  jv9 = A => A.length >= 1;
class qp {
  #A;
  #B;
  #Q;
  length;
  #D;
  #Z;
  #Y;
  #G;
  #J;
  #W;
  #X = !0;
  constructor(A, B, Q, D) {
    if (!Sv9(A)) throw new TypeError("empty pattern list");
    if (!jv9(B)) throw new TypeError("empty glob list");
    if (B.length !== A.length) throw new TypeError("mismatched pattern list and glob list lengths");
    if (this.length = A.length, Q < 0 || Q >= this.length) throw new TypeError("index out of range");
    if (this.#A = A, this.#B = B, this.#Q = Q, this.#D = D, this.#Q === 0) {
      if (this.isUNC()) {
        let [Z, G, F, I, ...Y] = this.#A,
          [W, J, X, V, ...C] = this.#B;
        if (Y[0] === "") Y.shift(), C.shift();
        let K = [Z, G, F, I, ""].join("/"),
          H = [W, J, X, V, ""].join("/");
        this.#A = [K, ...Y], this.#B = [H, ...C], this.length = this.#A.length;
      } else if (this.isDrive() || this.isAbsolute()) {
        let [Z, ...G] = this.#A,
          [F, ...I] = this.#B;
        if (G[0] === "") G.shift(), I.shift();
        let Y = Z + "/",
          W = F + "/";
        this.#A = [Y, ...G], this.#B = [W, ...I], this.length = this.#A.length;
      }
    }
  }
  pattern() {
    return this.#A[this.#Q];
  }
  isString() {
    return typeof this.#A[this.#Q] === "string";
  }
  isGlobstar() {
    return this.#A[this.#Q] === KY;
  }
  isRegExp() {
    return this.#A[this.#Q] instanceof RegExp;
  }
  globString() {
    return this.#Y = this.#Y || (this.#Q === 0 ? this.isAbsolute() ? this.#B[0] + this.#B.slice(1).join("/") : this.#B.join("/") : this.#B.slice(this.#Q).join("/"));
  }
  hasMore() {
    return this.length > this.#Q + 1;
  }
  rest() {
    if (this.#Z !== void 0) return this.#Z;
    if (!this.hasMore()) return this.#Z = null;
    return this.#Z = new qp(this.#A, this.#B, this.#Q + 1, this.#D), this.#Z.#W = this.#W, this.#Z.#J = this.#J, this.#Z.#G = this.#G, this.#Z;
  }
  isUNC() {
    let A = this.#A;
    return this.#J !== void 0 ? this.#J : this.#J = this.#D === "win32" && this.#Q === 0 && A[0] === "" && A[1] === "" && typeof A[2] === "string" && !!A[2] && typeof A[3] === "string" && !!A[3];
  }
  isDrive() {
    let A = this.#A;
    return this.#G !== void 0 ? this.#G : this.#G = this.#D === "win32" && this.#Q === 0 && this.length > 1 && typeof A[0] === "string" && /^[a-z]:$/i.test(A[0]);
  }
  isAbsolute() {
    let A = this.#A;
    return this.#W !== void 0 ? this.#W : this.#W = A[0] === "" && A.length > 1 || this.isDrive() || this.isUNC();
  }
  root() {
    let A = this.#A[0];
    return typeof A === "string" && this.isAbsolute() && this.#Q === 0 ? A : "";
  }
  checkFollowGlobstar() {
    return !(this.#Q === 0 || !this.isGlobstar() || !this.#X);
  }
  markFollowGlobstar() {
    if (this.#Q === 0 || !this.isGlobstar() || !this.#X) return !1;
    return this.#X = !1, !0;
  }
}
var yv9 = typeof process === "object" && process && typeof process.platform === "string" ? process.platform : "linux";
class iB1 {
  relative;
  relativeChildren;
  absolute;
  absoluteChildren;
  platform;
  mmopts;
  constructor(A, {
    nobrace: B,
    nocase: Q,
    noext: D,
    noglobstar: Z,
    platform: G = yv9
  }) {
    this.relative = [], this.absolute = [], this.relativeChildren = [], this.absoluteChildren = [], this.platform = G, this.mmopts = {
      dot: !0,
      nobrace: B,
      nocase: Q,
      noext: D,
      noglobstar: Z,
      optimizationLevel: 2,
      platform: G,
      nocomment: !0,
      nonegate: !0
    };
    for (let F of A) this.add(F);
  }
  add(A) {
    let B = new aH(A, this.mmopts);
    for (let Q = 0; Q < B.set.length; Q++) {
      let D = B.set[Q],
        Z = B.globParts[Q];
      if (!D || !Z) throw new Error("invalid pattern object");
      while (D[0] === "." && Z[0] === ".") D.shift(), Z.shift();
      let G = new qp(D, Z, 0, this.platform),
        F = new aH(G.globString(), this.mmopts),
        I = Z[Z.length - 1] === "**",
        Y = G.isAbsolute();
      if (Y) this.absolute.push(F);else this.relative.push(F);
      if (I) if (Y) this.absoluteChildren.push(F);else this.relativeChildren.push(F);
    }
  }
  ignored(A) {
    let B = A.fullpath(),
      Q = `${B}/`,
      D = A.relative() || ".",
      Z = `${D}/`;
    for (let G of this.relative) if (G.match(D) || G.match(Z)) return !0;
    for (let G of this.absolute) if (G.match(B) || G.match(Q)) return !0;
    return !1;
  }
  childrenIgnored(A) {
    let B = A.fullpath() + "/",
      Q = (A.relative() || ".") + "/";
    for (let D of this.relativeChildren) if (D.match(Q)) return !0;
    for (let D of this.absoluteChildren) if (D.match(B)) return !0;
    return !1;
  }
}
class ri1 {
  store;
  constructor(A = new Map()) {
    this.store = A;
  }
  copy() {
    return new ri1(new Map(this.store));
  }
  hasWalked(A, B) {
    return this.store.get(A.fullpath())?.has(B.globString());
  }
  storeWalked(A, B) {
    let Q = A.fullpath(),
      D = this.store.get(Q);
    if (D) D.add(B.globString());else this.store.set(Q, new Set([B.globString()]));
  }
}
class ht0 {
  store = new Map();
  add(A, B, Q) {
    let D = (B ? 2 : 0) | (Q ? 1 : 0),
      Z = this.store.get(A);
    this.store.set(A, Z === void 0 ? D : D & Z);
  }
  entries() {
    return [...this.store.entries()].map(([A, B]) => [A, !!(B & 2), !!(B & 1)]);
  }
}
class gt0 {
  store = new Map();
  add(A, B) {
    if (!A.canReaddir()) return;
    let Q = this.store.get(A);
    if (Q) {
      if (!Q.find(D => D.globString() === B.globString())) Q.push(B);
    } else this.store.set(A, [B]);
  }
  get(A) {
    let B = this.store.get(A);
    if (!B) throw new Error("attempting to walk unknown path");
    return B;
  }
  entries() {
    return this.keys().map(A => [A, this.store.get(A)]);
  }
  keys() {
    return [...this.store.keys()].filter(A => A.canReaddir());
  }
}
class nB1 {
  hasWalkedCache;
  matches = new ht0();
  subwalks = new gt0();
  patterns;
  follow;
  dot;
  opts;
  constructor(A, B) {
    this.opts = A, this.follow = !!A.follow, this.dot = !!A.dot, this.hasWalkedCache = B ? B.copy() : new ri1();
  }
  processPatterns(A, B) {
    this.patterns = B;
    let Q = B.map(D => [A, D]);
    for (let [D, Z] of Q) {
      this.hasWalkedCache.storeWalked(D, Z);
      let G = Z.root(),
        F = Z.isAbsolute() && this.opts.absolute !== !1;
      if (G) {
        D = D.resolve(G === "/" && this.opts.root !== void 0 ? this.opts.root : G);
        let J = Z.rest();
        if (!J) {
          this.matches.add(D, !0, !1);
          continue;
        } else Z = J;
      }
      if (D.isENOENT()) continue;
      let I,
        Y,
        W = !1;
      while (typeof (I = Z.pattern()) === "string" && (Y = Z.rest())) D = D.resolve(I), Z = Y, W = !0;
      if (I = Z.pattern(), Y = Z.rest(), W) {
        if (this.hasWalkedCache.hasWalked(D, Z)) continue;
        this.hasWalkedCache.storeWalked(D, Z);
      }
      if (typeof I === "string") {
        let J = I === ".." || I === "" || I === ".";
        this.matches.add(D.resolve(I), F, J);
        continue;
      } else if (I === KY) {
        if (!D.isSymbolicLink() || this.follow || Z.checkFollowGlobstar()) this.subwalks.add(D, Z);
        let J = Y?.pattern(),
          X = Y?.rest();
        if (!Y || (J === "" || J === ".") && !X) this.matches.add(D, F, J === "" || J === ".");else if (J === "..") {
          let V = D.parent || D;
          if (!X) this.matches.add(V, F, !0);else if (!this.hasWalkedCache.hasWalked(V, X)) this.subwalks.add(V, X);
        }
      } else if (I instanceof RegExp) this.subwalks.add(D, Z);
    }
    return this;
  }
  subwalkTargets() {
    return this.subwalks.keys();
  }
  child() {
    return new nB1(this.opts, this.hasWalkedCache);
  }
  filterEntries(A, B) {
    let Q = this.subwalks.get(A),
      D = this.child();
    for (let Z of B) for (let G of Q) {
      let F = G.isAbsolute(),
        I = G.pattern(),
        Y = G.rest();
      if (I === KY) D.testGlobstar(Z, G, Y, F);else if (I instanceof RegExp) D.testRegExp(Z, I, Y, F);else D.testString(Z, I, Y, F);
    }
    return D;
  }
  testGlobstar(A, B, Q, D) {
    if (this.dot || !A.name.startsWith(".")) {
      if (!B.hasMore()) this.matches.add(A, D, !1);
      if (A.canReaddir()) {
        if (this.follow || !A.isSymbolicLink()) this.subwalks.add(A, B);else if (A.isSymbolicLink()) {
          if (Q && B.checkFollowGlobstar()) this.subwalks.add(A, Q);else if (B.markFollowGlobstar()) this.subwalks.add(A, B);
        }
      }
    }
    if (Q) {
      let Z = Q.pattern();
      if (typeof Z === "string" && Z !== ".." && Z !== "" && Z !== ".") this.testString(A, Z, Q.rest(), D);else if (Z === "..") {
        let G = A.parent || A;
        this.subwalks.add(G, Q);
      } else if (Z instanceof RegExp) this.testRegExp(A, Z, Q.rest(), D);
    }
  }
  testRegExp(A, B, Q, D) {
    if (!B.test(A.name)) return;
    if (!Q) this.matches.add(A, D, !1);else this.subwalks.add(A, Q);
  }
  testString(A, B, Q, D) {
    if (!A.isNamed(B)) return;
    if (!Q) this.matches.add(A, D, !1);else this.subwalks.add(A, Q);
  }
}
var kv9 = (A, B) => typeof A === "string" ? new iB1([A], B) : Array.isArray(A) ? new iB1(A, B) : A;
class oi1 {
  path;
  patterns;
  opts;
  seen = new Set();
  paused = !1;
  aborted = !1;
  #A = [];
  #B;
  #Q;
  signal;
  maxDepth;
  includeChildMatches;
  constructor(A, B, Q) {
    if (this.patterns = A, this.path = B, this.opts = Q, this.#Q = !Q.posix && Q.platform === "win32" ? "\\" : "/", this.includeChildMatches = Q.includeChildMatches !== !1, Q.ignore || !this.includeChildMatches) {
      if (this.#B = kv9(Q.ignore ?? [], Q), !this.includeChildMatches && typeof this.#B.add !== "function") throw new Error("cannot ignore child matches, ignore lacks add() method.");
    }
    if (this.maxDepth = Q.maxDepth || 1 / 0, Q.signal) this.signal = Q.signal, this.signal.addEventListener("abort", () => {
      this.#A.length = 0;
    });
  }
  #D(A) {
    return this.seen.has(A) || !!this.#B?.ignored?.(A);
  }
  #Z(A) {
    return !!this.#B?.childrenIgnored?.(A);
  }
  pause() {
    this.paused = !0;
  }
  resume() {
    if (this.signal?.aborted) return;
    this.paused = !1;
    let A = void 0;
    while (!this.paused && (A = this.#A.shift())) A();
  }
  onResume(A) {
    if (this.signal?.aborted) return;
    if (!this.paused) A();else this.#A.push(A);
  }
  async matchCheck(A, B) {
    if (B && this.opts.nodir) return;
    let Q;
    if (this.opts.realpath) {
      if (Q = A.realpathCached() || (await A.realpath()), !Q) return;
      A = Q;
    }
    let Z = A.isUnknown() || this.opts.stat ? await A.lstat() : A;
    if (this.opts.follow && this.opts.nodir && Z?.isSymbolicLink()) {
      let G = await Z.realpath();
      if (G && (G.isUnknown() || this.opts.stat)) await G.lstat();
    }
    return this.matchCheckTest(Z, B);
  }
  matchCheckTest(A, B) {
    return A && (this.maxDepth === 1 / 0 || A.depth() <= this.maxDepth) && (!B || A.canReaddir()) && (!this.opts.nodir || !A.isDirectory()) && (!this.opts.nodir || !this.opts.follow || !A.isSymbolicLink() || !A.realpathCached()?.isDirectory()) && !this.#D(A) ? A : void 0;
  }
  matchCheckSync(A, B) {
    if (B && this.opts.nodir) return;
    let Q;
    if (this.opts.realpath) {
      if (Q = A.realpathCached() || A.realpathSync(), !Q) return;
      A = Q;
    }
    let Z = A.isUnknown() || this.opts.stat ? A.lstatSync() : A;
    if (this.opts.follow && this.opts.nodir && Z?.isSymbolicLink()) {
      let G = Z.realpathSync();
      if (G && (G?.isUnknown() || this.opts.stat)) G.lstatSync();
    }
    return this.matchCheckTest(Z, B);
  }
  matchFinish(A, B) {
    if (this.#D(A)) return;
    if (!this.includeChildMatches && this.#B?.add) {
      let Z = `${A.relativePosix()}/**`;
      this.#B.add(Z);
    }
    let Q = this.opts.absolute === void 0 ? B : this.opts.absolute;
    this.seen.add(A);
    let D = this.opts.mark && A.isDirectory() ? this.#Q : "";
    if (this.opts.withFileTypes) this.matchEmit(A);else if (Q) {
      let Z = this.opts.posix ? A.fullpathPosix() : A.fullpath();
      this.matchEmit(Z + D);
    } else {
      let Z = this.opts.posix ? A.relativePosix() : A.relative(),
        G = this.opts.dotRelative && !Z.startsWith(".." + this.#Q) ? "." + this.#Q : "";
      this.matchEmit(!Z ? "." + D : G + Z + D);
    }
  }
  async match(A, B, Q) {
    let D = await this.matchCheck(A, Q);
    if (D) this.matchFinish(D, B);
  }
  matchSync(A, B, Q) {
    let D = this.matchCheckSync(A, Q);
    if (D) this.matchFinish(D, B);
  }
  walkCB(A, B, Q) {
    if (this.signal?.aborted) Q();
    this.walkCB2(A, B, new nB1(this.opts), Q);
  }
  walkCB2(A, B, Q, D) {
    if (this.#Z(A)) return D();
    if (this.signal?.aborted) D();
    if (this.paused) {
      this.onResume(() => this.walkCB2(A, B, Q, D));
      return;
    }
    Q.processPatterns(A, B);
    let Z = 1,
      G = () => {
        if (--Z === 0) D();
      };
    for (let [F, I, Y] of Q.matches.entries()) {
      if (this.#D(F)) continue;
      Z++, this.match(F, I, Y).then(() => G());
    }
    for (let F of Q.subwalkTargets()) {
      if (this.maxDepth !== 1 / 0 && F.depth() >= this.maxDepth) continue;
      Z++;
      let I = F.readdirCached();
      if (F.calledReaddir()) this.walkCB3(F, I, Q, G);else F.readdirCB((Y, W) => this.walkCB3(F, W, Q, G), !0);
    }
    G();
  }
  walkCB3(A, B, Q, D) {
    Q = Q.filterEntries(A, B);
    let Z = 1,
      G = () => {
        if (--Z === 0) D();
      };
    for (let [F, I, Y] of Q.matches.entries()) {
      if (this.#D(F)) continue;
      Z++, this.match(F, I, Y).then(() => G());
    }
    for (let [F, I] of Q.subwalks.entries()) Z++, this.walkCB2(F, I, Q.child(), G);
    G();
  }
  walkCBSync(A, B, Q) {
    if (this.signal?.aborted) Q();
    this.walkCB2Sync(A, B, new nB1(this.opts), Q);
  }
  walkCB2Sync(A, B, Q, D) {
    if (this.#Z(A)) return D();
    if (this.signal?.aborted) D();
    if (this.paused) {
      this.onResume(() => this.walkCB2Sync(A, B, Q, D));
      return;
    }
    Q.processPatterns(A, B);
    let Z = 1,
      G = () => {
        if (--Z === 0) D();
      };
    for (let [F, I, Y] of Q.matches.entries()) {
      if (this.#D(F)) continue;
      this.matchSync(F, I, Y);
    }
    for (let F of Q.subwalkTargets()) {
      if (this.maxDepth !== 1 / 0 && F.depth() >= this.maxDepth) continue;
      Z++;
      let I = F.readdirSync();
      this.walkCB3Sync(F, I, Q, G);
    }
    G();
  }
  walkCB3Sync(A, B, Q, D) {
    Q = Q.filterEntries(A, B);
    let Z = 1,
      G = () => {
        if (--Z === 0) D();
      };
    for (let [F, I, Y] of Q.matches.entries()) {
      if (this.#D(F)) continue;
      this.matchSync(F, I, Y);
    }
    for (let [F, I] of Q.subwalks.entries()) Z++, this.walkCB2Sync(F, I, Q.child(), G);
    G();
  }
}
class BC1 extends oi1 {
  matches = new Set();
  constructor(A, B, Q) {
    super(A, B, Q);
  }
  matchEmit(A) {
    this.matches.add(A);
  }
  async walk() {
    if (this.signal?.aborted) throw this.signal.reason;
    if (this.path.isUnknown()) await this.path.lstat();
    return await new Promise((A, B) => {
      this.walkCB(this.path, this.patterns, () => {
        if (this.signal?.aborted) B(this.signal.reason);else A(this.matches);
      });
    }), this.matches;
  }
  walkSync() {
    if (this.signal?.aborted) throw this.signal.reason;
    if (this.path.isUnknown()) this.path.lstatSync();
    return this.walkCBSync(this.path, this.patterns, () => {
      if (this.signal?.aborted) throw this.signal.reason;
    }), this.matches;
  }
}
class QC1 extends oi1 {
  results;
  constructor(A, B, Q) {
    super(A, B, Q);
    this.results = new nf({
      signal: this.signal,
      objectMode: !0
    }), this.results.on("drain", () => this.resume()), this.results.on("resume", () => this.resume());
  }
  matchEmit(A) {
    if (this.results.write(A), !this.results.flowing) this.pause();
  }
  stream() {
    let A = this.path;
    if (A.isUnknown()) A.lstat().then(() => {
      this.walkCB(A, this.patterns, () => this.results.end());
    });else this.walkCB(A, this.patterns, () => this.results.end());
    return this.results;
  }
  streamSync() {
    if (this.path.isUnknown()) this.path.lstatSync();
    return this.walkCBSync(this.path, this.patterns, () => this.results.end()), this.results;
  }
}
var xv9 = typeof process === "object" && process && typeof process.platform === "string" ? process.platform : "linux";
class EN {
  absolute;
  cwd;
  root;
  dot;
  dotRelative;
  follow;
  ignore;
  magicalBraces;
  mark;
  matchBase;
  maxDepth;
  nobrace;
  nocase;
  nodir;
  noext;
  noglobstar;
  pattern;
  platform;
  realpath;
  scurry;
  stat;
  signal;
  windowsPathsNoEscape;
  withFileTypes;
  includeChildMatches;
  opts;
  patterns;
  constructor(A, B) {
    if (!B) throw new TypeError("glob options required");
    if (this.withFileTypes = !!B.withFileTypes, this.signal = B.signal, this.follow = !!B.follow, this.dot = !!B.dot, this.dotRelative = !!B.dotRelative, this.nodir = !!B.nodir, this.mark = !!B.mark, !B.cwd) this.cwd = "";else if (B.cwd instanceof URL || B.cwd.startsWith("file://")) B.cwd = _v9(B.cwd);
    if (this.cwd = B.cwd || "", this.root = B.root, this.magicalBraces = !!B.magicalBraces, this.nobrace = !!B.nobrace, this.noext = !!B.noext, this.realpath = !!B.realpath, this.absolute = B.absolute, this.includeChildMatches = B.includeChildMatches !== !1, this.noglobstar = !!B.noglobstar, this.matchBase = !!B.matchBase, this.maxDepth = typeof B.maxDepth === "number" ? B.maxDepth : 1 / 0, this.stat = !!B.stat, this.ignore = B.ignore, this.withFileTypes && this.absolute !== void 0) throw new Error("cannot set absolute and withFileTypes:true");
    if (typeof A === "string") A = [A];
    if (this.windowsPathsNoEscape = !!B.windowsPathsNoEscape || B.allowWindowsEscape === !1, this.windowsPathsNoEscape) A = A.map(I => I.replace(/\\/g, "/"));
    if (this.matchBase) {
      if (B.noglobstar) throw new TypeError("base matching requires globstar");
      A = A.map(I => I.includes("/") ? I : `./**/${I}`);
    }
    if (this.pattern = A, this.platform = B.platform || xv9, this.opts = {
      ...B,
      platform: this.platform
    }, B.scurry) {
      if (this.scurry = B.scurry, B.nocase !== void 0 && B.nocase !== B.scurry.nocase) throw new Error("nocase option contradicts provided scurry option");
    } else {
      let I = B.platform === "win32" ? lB1 : B.platform === "darwin" ? AC1 : B.platform ? pB1 : ft0;
      this.scurry = new I(this.cwd, {
        nocase: B.nocase,
        fs: B.fs
      });
    }
    this.nocase = this.scurry.nocase;
    let Q = this.platform === "darwin" || this.platform === "win32",
      D = {
        ...B,
        dot: this.dot,
        matchBase: this.matchBase,
        nobrace: this.nobrace,
        nocase: this.nocase,
        nocaseMagicOnly: Q,
        nocomment: !0,
        noext: this.noext,
        nonegate: !0,
        optimizationLevel: 2,
        platform: this.platform,
        windowsPathsNoEscape: this.windowsPathsNoEscape,
        debug: !!this.opts.debug
      },
      Z = this.pattern.map(I => new aH(I, D)),
      [G, F] = Z.reduce((I, Y) => {
        return I[0].push(...Y.set), I[1].push(...Y.globParts), I;
      }, [[], []]);
    this.patterns = G.map((I, Y) => {
      let W = F[Y];
      if (!W) throw new Error("invalid pattern object");
      return new qp(I, W, 0, this.platform);
    });
  }
  async walk() {
    return [...(await new BC1(this.patterns, this.scurry.cwd, {
      ...this.opts,
      maxDepth: this.maxDepth !== 1 / 0 ? this.maxDepth + this.scurry.cwd.depth() : 1 / 0,
      platform: this.platform,
      nocase: this.nocase,
      includeChildMatches: this.includeChildMatches
    }).walk())];
  }
  walkSync() {
    return [...new BC1(this.patterns, this.scurry.cwd, {
      ...this.opts,
      maxDepth: this.maxDepth !== 1 / 0 ? this.maxDepth + this.scurry.cwd.depth() : 1 / 0,
      platform: this.platform,
      nocase: this.nocase,
      includeChildMatches: this.includeChildMatches
    }).walkSync()];
  }
  stream() {
    return new QC1(this.patterns, this.scurry.cwd, {
      ...this.opts,
      maxDepth: this.maxDepth !== 1 / 0 ? this.maxDepth + this.scurry.cwd.depth() : 1 / 0,
      platform: this.platform,
      nocase: this.nocase,
      includeChildMatches: this.includeChildMatches
    }).stream();
  }
  streamSync() {
    return new QC1(this.patterns, this.scurry.cwd, {
      ...this.opts,
      maxDepth: this.maxDepth !== 1 / 0 ? this.maxDepth + this.scurry.cwd.depth() : 1 / 0,
      platform: this.platform,
      nocase: this.nocase,
      includeChildMatches: this.includeChildMatches
    }).streamSync();
  }
  iterateSync() {
    return this.streamSync()[Symbol.iterator]();
  }
  [Symbol.iterator]() {
    return this.iterateSync();
  }
  iterate() {
    return this.stream()[Symbol.asyncIterator]();
  }
  [Symbol.asyncIterator]() {
    return this.iterate();
  }
}
var ti1 = (A, B = {}) => {
  if (!Array.isArray(A)) A = [A];
  for (let Q of A) if (new aH(Q, B).hasMagic()) return !0;
  return !1;
};
function ZC1(A, B = {}) {
  return new EN(A, B).streamSync();
}
function mt0(A, B = {}) {
  return new EN(A, B).stream();
}
function dt0(A, B = {}) {
  return new EN(A, B).walkSync();
}
async function ut0(A, B = {}) {
  return new EN(A, B).walk();
}
function GC1(A, B = {}) {
  return new EN(A, B).iterateSync();
}
function ct0(A, B = {}) {
  return new EN(A, B).iterate();
}
var vv9 = ZC1,
  bv9 = Object.assign(mt0, {
    sync: ZC1
  }),
  fv9 = GC1,
  hv9 = Object.assign(ct0, {
    sync: GC1
  }),
  gv9 = Object.assign(dt0, {
    stream: ZC1,
    iterate: GC1
  }),
  DC1 = Object.assign(ut0, {
    glob: ut0,
    globSync: dt0,
    sync: gv9,
    globStream: mt0,
    stream: bv9,
    globStreamSync: ZC1,
    streamSync: vv9,
    globIterate: ct0,
    iterate: hv9,
    globIterateSync: GC1,
    iterateSync: fv9,
    Glob: EN,
    hasMagic: ti1,
    escape: zp,
    unescape: Iw
  });
DC1.glob = DC1;
module.exports = {
  AC1,
  DC1,
  Yw,
  eV1,
  lB1,
  pB1,
  tV1
};