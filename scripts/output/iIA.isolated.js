/**
 * Isolated bundle for 'iIA'
 */

function j91(A, B = !1) {
  let Q = A.length,
    D = 0,
    Z = "",
    G = 0,
    F = 16,
    I = 0,
    Y = 0,
    W = 0,
    J = 0,
    X = 0;
  function V(N, O) {
    let R = 0,
      T = 0;
    while (R < N || !O) {
      let j = A.charCodeAt(D);
      if (j >= 48 && j <= 57) T = T * 16 + j - 48;else if (j >= 65 && j <= 70) T = T * 16 + j - 65 + 10;else if (j >= 97 && j <= 102) T = T * 16 + j - 97 + 10;else break;
      D++, R++;
    }
    if (R < N) T = -1;
    return T;
  }
  function C(N) {
    D = N, Z = "", G = 0, F = 16, X = 0;
  }
  function K() {
    let N = D;
    if (A.charCodeAt(D) === 48) D++;else {
      D++;
      while (D < A.length && bi(A.charCodeAt(D))) D++;
    }
    if (D < A.length && A.charCodeAt(D) === 46) if (D++, D < A.length && bi(A.charCodeAt(D))) {
      D++;
      while (D < A.length && bi(A.charCodeAt(D))) D++;
    } else return X = 3, A.substring(N, D);
    let O = D;
    if (D < A.length && (A.charCodeAt(D) === 69 || A.charCodeAt(D) === 101)) {
      if (D++, D < A.length && A.charCodeAt(D) === 43 || A.charCodeAt(D) === 45) D++;
      if (D < A.length && bi(A.charCodeAt(D))) {
        D++;
        while (D < A.length && bi(A.charCodeAt(D))) D++;
        O = D;
      } else X = 3;
    }
    return A.substring(N, O);
  }
  function H() {
    let N = "",
      O = D;
    while (!0) {
      if (D >= Q) {
        N += A.substring(O, D), X = 2;
        break;
      }
      let R = A.charCodeAt(D);
      if (R === 34) {
        N += A.substring(O, D), D++;
        break;
      }
      if (R === 92) {
        if (N += A.substring(O, D), D++, D >= Q) {
          X = 2;
          break;
        }
        switch (A.charCodeAt(D++)) {
          case 34:
            N += '"';
            break;
          case 92:
            N += "\\";
            break;
          case 47:
            N += "/";
            break;
          case 98:
            N += "\b";
            break;
          case 102:
            N += "\f";
            break;
          case 110:
            N += `
`;
            break;
          case 114:
            N += "\r";
            break;
          case 116:
            N += "\t";
            break;
          case 117:
            let j = V(4, !0);
            if (j >= 0) N += String.fromCharCode(j);else X = 4;
            break;
          default:
            X = 5;
        }
        O = D;
        continue;
      }
      if (R >= 0 && R <= 31) if (S91(R)) {
        N += A.substring(O, D), X = 2;
        break;
      } else X = 6;
      D++;
    }
    return N;
  }
  function z() {
    if (Z = "", X = 0, G = D, Y = I, J = W, D >= Q) return G = Q, F = 17;
    let N = A.charCodeAt(D);
    if (Lr1(N)) {
      do D++, Z += String.fromCharCode(N), N = A.charCodeAt(D); while (Lr1(N));
      return F = 15;
    }
    if (S91(N)) {
      if (D++, Z += String.fromCharCode(N), N === 13 && A.charCodeAt(D) === 10) D++, Z += `
`;
      return I++, W = D, F = 14;
    }
    switch (N) {
      case 123:
        return D++, F = 1;
      case 125:
        return D++, F = 2;
      case 91:
        return D++, F = 3;
      case 93:
        return D++, F = 4;
      case 58:
        return D++, F = 6;
      case 44:
        return D++, F = 5;
      case 34:
        return D++, Z = H(), F = 10;
      case 47:
        let O = D - 1;
        if (A.charCodeAt(D + 1) === 47) {
          D += 2;
          while (D < Q) {
            if (S91(A.charCodeAt(D))) break;
            D++;
          }
          return Z = A.substring(O, D), F = 12;
        }
        if (A.charCodeAt(D + 1) === 42) {
          D += 2;
          let R = Q - 1,
            T = !1;
          while (D < R) {
            let j = A.charCodeAt(D);
            if (j === 42 && A.charCodeAt(D + 1) === 47) {
              D += 2, T = !0;
              break;
            }
            if (D++, S91(j)) {
              if (j === 13 && A.charCodeAt(D) === 10) D++;
              I++, W = D;
            }
          }
          if (!T) D++, X = 1;
          return Z = A.substring(O, D), F = 13;
        }
        return Z += String.fromCharCode(N), D++, F = 16;
      case 45:
        if (Z += String.fromCharCode(N), D++, D === Q || !bi(A.charCodeAt(D))) return F = 16;
      case 48:
      case 49:
      case 50:
      case 51:
      case 52:
      case 53:
      case 54:
      case 55:
      case 56:
      case 57:
        return Z += K(), F = 11;
      default:
        while (D < Q && $(N)) D++, N = A.charCodeAt(D);
        if (G !== D) {
          switch (Z = A.substring(G, D), Z) {
            case "true":
              return F = 8;
            case "false":
              return F = 9;
            case "null":
              return F = 7;
          }
          return F = 16;
        }
        return Z += String.fromCharCode(N), D++, F = 16;
    }
  }
  function $(N) {
    if (Lr1(N) || S91(N)) return !1;
    switch (N) {
      case 125:
      case 93:
      case 123:
      case 91:
      case 34:
      case 58:
      case 44:
      case 47:
        return !1;
    }
    return !0;
  }
  function L() {
    let N;
    do N = z(); while (N >= 12 && N <= 15);
    return N;
  }
  return {
    setPosition: C,
    getPosition: () => D,
    scan: B ? L : z,
    getToken: () => F,
    getTokenValue: () => Z,
    getTokenOffset: () => G,
    getTokenLength: () => D - G,
    getTokenStartLine: () => Y,
    getTokenStartCharacter: () => G - J,
    getTokenError: () => X
  };
}
function Lr1(A) {
  return A === 32 || A === 9;
}
function S91(A) {
  return A === 10 || A === 13;
}
function bi(A) {
  return A >= 48 && A <= 57;
}
var k91;
function iIA(A, B = [], Q = k91.DEFAULT) {
  let D = null,
    Z = [],
    G = [];
  function F(Y) {
    if (Array.isArray(Z)) Z.push(Y);else if (D !== null) Z[D] = Y;
  }
  return Tr1(A, {
    onObjectBegin: () => {
      let Y = {};
      F(Y), G.push(Z), Z = Y, D = null;
    },
    onObjectProperty: Y => {
      D = Y;
    },
    onObjectEnd: () => {
      Z = G.pop();
    },
    onArrayBegin: () => {
      let Y = [];
      F(Y), G.push(Z), Z = Y, D = null;
    },
    onArrayEnd: () => {
      Z = G.pop();
    },
    onLiteralValue: F,
    onError: (Y, W, J) => {
      B.push({
        error: Y,
        offset: W,
        length: J
      });
    }
  }, Q), Z[0];
}
function Tr1(A, B, Q = k91.DEFAULT) {
  let D = j91(A, !1),
    Z = [];
  function G(a) {
    return a ? () => a(D.getTokenOffset(), D.getTokenLength(), D.getTokenStartLine(), D.getTokenStartCharacter()) : () => !0;
  }
  function F(a) {
    return a ? () => a(D.getTokenOffset(), D.getTokenLength(), D.getTokenStartLine(), D.getTokenStartCharacter(), () => Z.slice()) : () => !0;
  }
  function I(a) {
    return a ? n => a(n, D.getTokenOffset(), D.getTokenLength(), D.getTokenStartLine(), D.getTokenStartCharacter()) : () => !0;
  }
  function Y(a) {
    return a ? n => a(n, D.getTokenOffset(), D.getTokenLength(), D.getTokenStartLine(), D.getTokenStartCharacter(), () => Z.slice()) : () => !0;
  }
  let W = F(B.onObjectBegin),
    J = Y(B.onObjectProperty),
    X = G(B.onObjectEnd),
    V = F(B.onArrayBegin),
    C = G(B.onArrayEnd),
    K = Y(B.onLiteralValue),
    H = I(B.onSeparator),
    z = G(B.onComment),
    $ = I(B.onError),
    L = Q && Q.disallowComments,
    N = Q && Q.allowTrailingComma;
  function O() {
    while (!0) {
      let a = D.scan();
      switch (D.getTokenError()) {
        case 4:
          R(14);
          break;
        case 5:
          R(15);
          break;
        case 3:
          R(13);
          break;
        case 1:
          if (!L) R(11);
          break;
        case 2:
          R(12);
          break;
        case 6:
          R(16);
          break;
      }
      switch (a) {
        case 12:
        case 13:
          if (L) R(10);else z();
          break;
        case 16:
          R(1);
          break;
        case 15:
        case 14:
          break;
        default:
          return a;
      }
    }
  }
  function R(a, n = [], v = []) {
    if ($(a), n.length + v.length > 0) {
      let t = D.getToken();
      while (t !== 17) {
        if (n.indexOf(t) !== -1) {
          O();
          break;
        } else if (v.indexOf(t) !== -1) break;
        t = O();
      }
    }
  }
  function T(a) {
    let n = D.getTokenValue();
    if (a) K(n);else J(n), Z.push(n);
    return O(), !0;
  }
  function j() {
    switch (D.getToken()) {
      case 11:
        let a = D.getTokenValue(),
          n = Number(a);
        if (isNaN(n)) R(2), n = 0;
        K(n);
        break;
      case 7:
        K(null);
        break;
      case 8:
        K(!0);
        break;
      case 9:
        K(!1);
        break;
      default:
        return !1;
    }
    return O(), !0;
  }
  function f() {
    if (D.getToken() !== 10) return R(3, [], [2, 5]), !1;
    if (T(!1), D.getToken() === 6) {
      if (H(":"), O(), !h()) R(4, [], [2, 5]);
    } else R(5, [], [2, 5]);
    return Z.pop(), !0;
  }
  function y() {
    W(), O();
    let a = !1;
    while (D.getToken() !== 2 && D.getToken() !== 17) {
      if (D.getToken() === 5) {
        if (!a) R(4, [], []);
        if (H(","), O(), D.getToken() === 2 && N) break;
      } else if (a) R(6, [], []);
      if (!f()) R(4, [], [2, 5]);
      a = !0;
    }
    if (X(), D.getToken() !== 2) R(7, [2], []);else O();
    return !0;
  }
  function c() {
    V(), O();
    let a = !0,
      n = !1;
    while (D.getToken() !== 4 && D.getToken() !== 17) {
      if (D.getToken() === 5) {
        if (!n) R(4, [], []);
        if (H(","), O(), D.getToken() === 4 && N) break;
      } else if (n) R(6, [], []);
      if (a) Z.push(0), a = !1;else Z[Z.length - 1]++;
      if (!h()) R(4, [], [4, 5]);
      n = !0;
    }
    if (C(), !a) Z.pop();
    if (D.getToken() !== 4) R(8, [4], []);else O();
    return !0;
  }
  function h() {
    switch (D.getToken()) {
      case 3:
        return c();
      case 1:
        return y();
      case 10:
        return T(!0);
      default:
        return j();
    }
  }
  if (O(), D.getToken() === 17) {
    if (Q.allowEmptyContent) return !0;
    return R(4, [], []), !1;
  }
  if (!h()) return R(4, [], []), !1;
  if (D.getToken() !== 17) R(9, [], []);
  return !0;
}
module.exports = {
  Tr1,
  iIA,
  j91,
  k91
};