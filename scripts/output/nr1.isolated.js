/**
 * Isolated bundle for 'nr1'
 */

import { stat as L3Q } from "fs";
import { stat as M3Q, readdir as R3Q } from "fs/promises";
import { EventEmitter as O3Q } from "events";
import * as J6 from "path";
import { stat as a5Q, lstat as LYA, readdir as s5Q, realpath as r5Q } from "node:fs/promises";
import { Readable as o5Q } from "node:stream";
import { resolve as MYA, relative as t5Q, join as e5Q, sep as A3Q } from "node:path";
var oC = {
    FILE_TYPE: "files",
    DIR_TYPE: "directories",
    FILE_DIR_TYPE: "files_directories",
    EVERYTHING_TYPE: "all"
  },
  hr1 = {
    root: ".",
    fileFilter: A => !0,
    directoryFilter: A => !0,
    type: oC.FILE_TYPE,
    lstat: !1,
    depth: 2147483648,
    alwaysStat: !1,
    highWaterMark: 4096
  };
var PYA = "READDIRP_RECURSIVE_ERROR",
  B3Q = new Set(["ENOENT", "EPERM", "EACCES", "ELOOP", PYA]),
  RYA = [oC.DIR_TYPE, oC.EVERYTHING_TYPE, oC.FILE_DIR_TYPE, oC.FILE_TYPE],
  Q3Q = new Set([oC.DIR_TYPE, oC.EVERYTHING_TYPE, oC.FILE_DIR_TYPE]),
  D3Q = new Set([oC.EVERYTHING_TYPE, oC.FILE_DIR_TYPE, oC.FILE_TYPE]),
  Z3Q = A => B3Q.has(A.code),
  G3Q = process.platform === "win32",
  OYA = A => !0,
  TYA = A => {
    if (A === void 0) return OYA;
    if (typeof A === "function") return A;
    if (typeof A === "string") {
      let B = A.trim();
      return Q => Q.basename === B;
    }
    if (Array.isArray(A)) {
      let B = A.map(Q => Q.trim());
      return Q => B.some(D => Q.basename === D);
    }
    return OYA;
  };
class SYA extends o5Q {
  constructor(A = {}) {
    super({
      objectMode: !0,
      autoDestroy: !0,
      highWaterMark: A.highWaterMark
    });
    let B = {
        ...hr1,
        ...A
      },
      {
        root: Q,
        type: D
      } = B;
    this._fileFilter = TYA(B.fileFilter), this._directoryFilter = TYA(B.directoryFilter);
    let Z = B.lstat ? LYA : a5Q;
    if (G3Q) this._stat = G => Z(G, {
      bigint: !0
    });else this._stat = Z;
    this._maxDepth = B.depth ?? hr1.depth, this._wantsDir = D ? Q3Q.has(D) : !1, this._wantsFile = D ? D3Q.has(D) : !1, this._wantsEverything = D === oC.EVERYTHING_TYPE, this._root = MYA(Q), this._isDirent = !B.alwaysStat, this._statsProp = this._isDirent ? "dirent" : "stats", this._rdOptions = {
      encoding: "utf8",
      withFileTypes: this._isDirent
    }, this.parents = [this._exploreDir(Q, 1)], this.reading = !1, this.parent = void 0;
  }
  async _read(A) {
    if (this.reading) return;
    this.reading = !0;
    try {
      while (!this.destroyed && A > 0) {
        let B = this.parent,
          Q = B && B.files;
        if (Q && Q.length > 0) {
          let {
              path: D,
              depth: Z
            } = B,
            G = Q.splice(0, A).map(I => this._formatEntry(I, D)),
            F = await Promise.all(G);
          for (let I of F) {
            if (!I) continue;
            if (this.destroyed) return;
            let Y = await this._getEntryType(I);
            if (Y === "directory" && this._directoryFilter(I)) {
              if (Z <= this._maxDepth) this.parents.push(this._exploreDir(I.fullPath, Z + 1));
              if (this._wantsDir) this.push(I), A--;
            } else if ((Y === "file" || this._includeAsFile(I)) && this._fileFilter(I)) {
              if (this._wantsFile) this.push(I), A--;
            }
          }
        } else {
          let D = this.parents.pop();
          if (!D) {
            this.push(null);
            break;
          }
          if (this.parent = await D, this.destroyed) return;
        }
      }
    } catch (B) {
      this.destroy(B);
    } finally {
      this.reading = !1;
    }
  }
  async _exploreDir(A, B) {
    let Q;
    try {
      Q = await s5Q(A, this._rdOptions);
    } catch (D) {
      this._onError(D);
    }
    return {
      files: Q,
      depth: B,
      path: A
    };
  }
  async _formatEntry(A, B) {
    let Q,
      D = this._isDirent ? A.name : A;
    try {
      let Z = MYA(e5Q(B, D));
      Q = {
        path: t5Q(this._root, Z),
        fullPath: Z,
        basename: D
      }, Q[this._statsProp] = this._isDirent ? A : await this._stat(Z);
    } catch (Z) {
      this._onError(Z);
      return;
    }
    return Q;
  }
  _onError(A) {
    if (Z3Q(A) && !this.destroyed) this.emit("warn", A);else this.destroy(A);
  }
  async _getEntryType(A) {
    if (!A && this._statsProp in A) return "";
    let B = A[this._statsProp];
    if (B.isFile()) return "file";
    if (B.isDirectory()) return "directory";
    if (B && B.isSymbolicLink()) {
      let Q = A.fullPath;
      try {
        let D = await r5Q(Q),
          Z = await LYA(D);
        if (Z.isFile()) return "file";
        if (Z.isDirectory()) {
          let G = D.length;
          if (Q.startsWith(D) && Q.substr(G, 1) === A3Q) {
            let F = new Error(`Circular symlink detected: "${Q}" points to "${D}"`);
            return F.code = PYA, this._onError(F);
          }
          return "directory";
        }
      } catch (D) {
        return this._onError(D), "";
      }
    }
  }
  _includeAsFile(A) {
    let B = A && A[this._statsProp];
    return B && this._wantsEverything && !B.isDirectory();
  }
}
function jYA(A, B = {}) {
  let Q = B.entryType || B.type;
  if (Q === "both") Q = oC.FILE_DIR_TYPE;
  if (Q) B.type = Q;
  if (!A) throw new Error("readdirp: root argument is required. Usage: readdirp(root, options)");else if (typeof A !== "string") throw new TypeError("readdirp: root argument must be a string. Usage: readdirp(root, options)");else if (Q && !RYA.includes(Q)) throw new Error(`readdirp: Invalid type passed. Use one of ${RYA.join(", ")}`);
  return B.root = A, new SYA(B);
}
import { watchFile as F3Q, unwatchFile as yYA, watch as I3Q } from "fs";
import { open as Y3Q, stat as _YA, lstat as W3Q, realpath as gr1 } from "fs/promises";
import * as ZD from "path";
import { type as J3Q } from "os";
var X3Q = "data",
  dr1 = "end",
  xYA = "close",
  uK1 = () => {};
var mK1 = process.platform,
  cr1 = mK1 === "win32",
  V3Q = mK1 === "darwin",
  C3Q = mK1 === "linux",
  K3Q = mK1 === "freebsd",
  vYA = J3Q() === "OS400",
  P7 = {
    ALL: "all",
    READY: "ready",
    ADD: "add",
    CHANGE: "change",
    ADD_DIR: "addDir",
    UNLINK: "unlink",
    UNLINK_DIR: "unlinkDir",
    RAW: "raw",
    ERROR: "error"
  },
  Lw = P7,
  H3Q = "watch",
  z3Q = {
    lstat: W3Q,
    stat: _YA
  },
  zh = "listeners",
  fK1 = "errHandlers",
  ui = "rawEmitters",
  E3Q = [zh, fK1, ui],
  U3Q = new Set(["3dm", "3ds", "3g2", "3gp", "7z", "a", "aac", "adp", "afdesign", "afphoto", "afpub", "ai", "aif", "aiff", "alz", "ape", "apk", "appimage", "ar", "arj", "asf", "au", "avi", "bak", "baml", "bh", "bin", "bk", "bmp", "btif", "bz2", "bzip2", "cab", "caf", "cgm", "class", "cmx", "cpio", "cr2", "cur", "dat", "dcm", "deb", "dex", "djvu", "dll", "dmg", "dng", "doc", "docm", "docx", "dot", "dotm", "dra", "DS_Store", "dsk", "dts", "dtshd", "dvb", "dwg", "dxf", "ecelp4800", "ecelp7470", "ecelp9600", "egg", "eol", "eot", "epub", "exe", "f4v", "fbs", "fh", "fla", "flac", "flatpak", "fli", "flv", "fpx", "fst", "fvt", "g3", "gh", "gif", "graffle", "gz", "gzip", "h261", "h263", "h264", "icns", "ico", "ief", "img", "ipa", "iso", "jar", "jpeg", "jpg", "jpgv", "jpm", "jxr", "key", "ktx", "lha", "lib", "lvp", "lz", "lzh", "lzma", "lzo", "m3u", "m4a", "m4v", "mar", "mdi", "mht", "mid", "midi", "mj2", "mka", "mkv", "mmr", "mng", "mobi", "mov", "movie", "mp3", "mp4", "mp4a", "mpeg", "mpg", "mpga", "mxu", "nef", "npx", "numbers", "nupkg", "o", "odp", "ods", "odt", "oga", "ogg", "ogv", "otf", "ott", "pages", "pbm", "pcx", "pdb", "pdf", "pea", "pgm", "pic", "png", "pnm", "pot", "potm", "potx", "ppa", "ppam", "ppm", "pps", "ppsm", "ppsx", "ppt", "pptm", "pptx", "psd", "pya", "pyc", "pyo", "pyv", "qt", "rar", "ras", "raw", "resources", "rgb", "rip", "rlc", "rmf", "rmvb", "rpm", "rtf", "rz", "s3m", "s7z", "scpt", "sgi", "shar", "snap", "sil", "sketch", "slk", "smv", "snk", "so", "stl", "suo", "sub", "swf", "tar", "tbz", "tbz2", "tga", "tgz", "thmx", "tif", "tiff", "tlz", "ttc", "ttf", "txz", "udf", "uvh", "uvi", "uvm", "uvp", "uvs", "uvu", "viv", "vob", "war", "wav", "wax", "wbmp", "wdp", "weba", "webm", "webp", "whl", "wim", "wm", "wma", "wmv", "wmx", "woff", "woff2", "wrm", "wvx", "xbm", "xif", "xla", "xlam", "xls", "xlsb", "xlsm", "xlsx", "xlt", "xltm", "xltx", "xm", "xmind", "xpi", "xpm", "xwd", "xz", "z", "zip", "zipx"]),
  w3Q = A => U3Q.has(ZD.extname(A).slice(1).toLowerCase()),
  mr1 = (A, B) => {
    if (A instanceof Set) A.forEach(B);else B(A);
  },
  f91 = (A, B, Q) => {
    let D = A[B];
    if (!(D instanceof Set)) A[B] = D = new Set([D]);
    D.add(Q);
  },
  $3Q = A => B => {
    let Q = A[B];
    if (Q instanceof Set) Q.clear();else delete A[B];
  },
  h91 = (A, B, Q) => {
    let D = A[B];
    if (D instanceof Set) D.delete(Q);else if (D === Q) delete A[B];
  },
  bYA = A => A instanceof Set ? A.size === 0 : !A,
  hK1 = new Map();
function kYA(A, B, Q, D, Z) {
  let G = (F, I) => {
    if (Q(A), Z(F, I, {
      watchedPath: A
    }), I && A !== I) gK1(ZD.resolve(A, I), zh, ZD.join(A, I));
  };
  try {
    return I3Q(A, {
      persistent: B.persistent
    }, G);
  } catch (F) {
    D(F);
    return;
  }
}
var gK1 = (A, B, Q, D, Z) => {
    let G = hK1.get(A);
    if (!G) return;
    mr1(G[B], F => {
      F(Q, D, Z);
    });
  },
  q3Q = (A, B, Q, D) => {
    let {
        listener: Z,
        errHandler: G,
        rawEmitter: F
      } = D,
      I = hK1.get(B),
      Y;
    if (!Q.persistent) {
      if (Y = kYA(A, Q, Z, G, F), !Y) return;
      return Y.close.bind(Y);
    }
    if (I) f91(I, zh, Z), f91(I, fK1, G), f91(I, ui, F);else {
      if (Y = kYA(A, Q, gK1.bind(null, B, zh), G, gK1.bind(null, B, ui)), !Y) return;
      Y.on(Lw.ERROR, async W => {
        let J = gK1.bind(null, B, fK1);
        if (I) I.watcherUnusable = !0;
        if (cr1 && W.code === "EPERM") try {
          await (await Y3Q(A, "r")).close(), J(W);
        } catch (X) {} else J(W);
      }), I = {
        listeners: Z,
        errHandlers: G,
        rawEmitters: F,
        watcher: Y
      }, hK1.set(B, I);
    }
    return () => {
      if (h91(I, zh, Z), h91(I, fK1, G), h91(I, ui, F), bYA(I.listeners)) I.watcher.close(), hK1.delete(B), E3Q.forEach($3Q(I)), I.watcher = void 0, Object.freeze(I);
    };
  },
  ur1 = new Map(),
  N3Q = (A, B, Q, D) => {
    let {
        listener: Z,
        rawEmitter: G
      } = D,
      F = ur1.get(B),
      I = F && F.options;
    if (I && (I.persistent < Q.persistent || I.interval > Q.interval)) yYA(B), F = void 0;
    if (F) f91(F, zh, Z), f91(F, ui, G);else F = {
      listeners: Z,
      rawEmitters: G,
      options: Q,
      watcher: F3Q(B, Q, (Y, W) => {
        mr1(F.rawEmitters, X => {
          X(Lw.CHANGE, B, {
            curr: Y,
            prev: W
          });
        });
        let J = Y.mtimeMs;
        if (Y.size !== W.size || J > W.mtimeMs || J === 0) mr1(F.listeners, X => X(A, Y));
      })
    }, ur1.set(B, F);
    return () => {
      if (h91(F, zh, Z), h91(F, ui, G), bYA(F.listeners)) ur1.delete(B), yYA(B), F.options = F.watcher = void 0, Object.freeze(F);
    };
  };
class lr1 {
  constructor(A) {
    this.fsw = A, this._boundHandleError = B => A._handleError(B);
  }
  _watchWithNodeFs(A, B) {
    let Q = this.fsw.options,
      D = ZD.dirname(A),
      Z = ZD.basename(A);
    this.fsw._getWatchedDir(D).add(Z);
    let F = ZD.resolve(A),
      I = {
        persistent: Q.persistent
      };
    if (!B) B = uK1;
    let Y;
    if (Q.usePolling) {
      let W = Q.interval !== Q.binaryInterval;
      I.interval = W && w3Q(Z) ? Q.binaryInterval : Q.interval, Y = N3Q(A, F, I, {
        listener: B,
        rawEmitter: this.fsw._emitRaw
      });
    } else Y = q3Q(A, F, I, {
      listener: B,
      errHandler: this._boundHandleError,
      rawEmitter: this.fsw._emitRaw
    });
    return Y;
  }
  _handleFile(A, B, Q) {
    if (this.fsw.closed) return;
    let D = ZD.dirname(A),
      Z = ZD.basename(A),
      G = this.fsw._getWatchedDir(D),
      F = B;
    if (G.has(Z)) return;
    let I = async (W, J) => {
        if (!this.fsw._throttle(H3Q, A, 5)) return;
        if (!J || J.mtimeMs === 0) try {
          let X = await _YA(A);
          if (this.fsw.closed) return;
          let {
            atimeMs: V,
            mtimeMs: C
          } = X;
          if (!V || V <= C || C !== F.mtimeMs) this.fsw._emit(Lw.CHANGE, A, X);
          if ((V3Q || C3Q || K3Q) && F.ino !== X.ino) {
            this.fsw._closeFile(W), F = X;
            let K = this._watchWithNodeFs(A, I);
            if (K) this.fsw._addPathCloser(W, K);
          } else F = X;
        } catch (X) {
          this.fsw._remove(D, Z);
        } else if (G.has(Z)) {
          let {
            atimeMs: X,
            mtimeMs: V
          } = J;
          if (!X || X <= V || V !== F.mtimeMs) this.fsw._emit(Lw.CHANGE, A, J);
          F = J;
        }
      },
      Y = this._watchWithNodeFs(A, I);
    if (!(Q && this.fsw.options.ignoreInitial) && this.fsw._isntIgnored(A)) {
      if (!this.fsw._throttle(Lw.ADD, A, 0)) return;
      this.fsw._emit(Lw.ADD, A, B);
    }
    return Y;
  }
  async _handleSymlink(A, B, Q, D) {
    if (this.fsw.closed) return;
    let Z = A.fullPath,
      G = this.fsw._getWatchedDir(B);
    if (!this.fsw.options.followSymlinks) {
      this.fsw._incrReadyCount();
      let F;
      try {
        F = await gr1(Q);
      } catch (I) {
        return this.fsw._emitReady(), !0;
      }
      if (this.fsw.closed) return;
      if (G.has(D)) {
        if (this.fsw._symlinkPaths.get(Z) !== F) this.fsw._symlinkPaths.set(Z, F), this.fsw._emit(Lw.CHANGE, Q, A.stats);
      } else G.add(D), this.fsw._symlinkPaths.set(Z, F), this.fsw._emit(Lw.ADD, Q, A.stats);
      return this.fsw._emitReady(), !0;
    }
    if (this.fsw._symlinkPaths.has(Z)) return !0;
    this.fsw._symlinkPaths.set(Z, !0);
  }
  _handleRead(A, B, Q, D, Z, G, F) {
    if (A = ZD.join(A, ""), F = this.fsw._throttle("readdir", A, 1000), !F) return;
    let I = this.fsw._getWatchedDir(Q.path),
      Y = new Set(),
      W = this.fsw._readdirp(A, {
        fileFilter: J => Q.filterPath(J),
        directoryFilter: J => Q.filterDir(J)
      });
    if (!W) return;
    return W.on(X3Q, async J => {
      if (this.fsw.closed) {
        W = void 0;
        return;
      }
      let X = J.path,
        V = ZD.join(A, X);
      if (Y.add(X), J.stats.isSymbolicLink() && (await this._handleSymlink(J, A, V, X))) return;
      if (this.fsw.closed) {
        W = void 0;
        return;
      }
      if (X === D || !D && !I.has(X)) this.fsw._incrReadyCount(), V = ZD.join(Z, ZD.relative(Z, V)), this._addToNodeFs(V, B, Q, G + 1);
    }).on(Lw.ERROR, this._boundHandleError), new Promise((J, X) => {
      if (!W) return X();
      W.once(dr1, () => {
        if (this.fsw.closed) {
          W = void 0;
          return;
        }
        let V = F ? F.clear() : !1;
        if (J(void 0), I.getChildren().filter(C => {
          return C !== A && !Y.has(C);
        }).forEach(C => {
          this.fsw._remove(A, C);
        }), W = void 0, V) this._handleRead(A, !1, Q, D, Z, G, F);
      });
    });
  }
  async _handleDir(A, B, Q, D, Z, G, F) {
    let I = this.fsw._getWatchedDir(ZD.dirname(A)),
      Y = I.has(ZD.basename(A));
    if (!(Q && this.fsw.options.ignoreInitial) && !Z && !Y) this.fsw._emit(Lw.ADD_DIR, A, B);
    I.add(ZD.basename(A)), this.fsw._getWatchedDir(A);
    let W,
      J,
      X = this.fsw.options.depth;
    if ((X == null || D <= X) && !this.fsw._symlinkPaths.has(F)) {
      if (!Z) {
        if (await this._handleRead(A, Q, G, Z, A, D, W), this.fsw.closed) return;
      }
      J = this._watchWithNodeFs(A, (V, C) => {
        if (C && C.mtimeMs === 0) return;
        this._handleRead(V, !1, G, Z, A, D, W);
      });
    }
    return J;
  }
  async _addToNodeFs(A, B, Q, D, Z) {
    let G = this.fsw._emitReady;
    if (this.fsw._isIgnored(A) || this.fsw.closed) return G(), !1;
    let F = this.fsw._getWatchHelpers(A);
    if (Q) F.filterPath = I => Q.filterPath(I), F.filterDir = I => Q.filterDir(I);
    try {
      let I = await z3Q[F.statMethod](F.watchPath);
      if (this.fsw.closed) return;
      if (this.fsw._isIgnored(F.watchPath, I)) return G(), !1;
      let Y = this.fsw.options.followSymlinks,
        W;
      if (I.isDirectory()) {
        let J = ZD.resolve(A),
          X = Y ? await gr1(A) : A;
        if (this.fsw.closed) return;
        if (W = await this._handleDir(F.watchPath, I, B, D, Z, F, X), this.fsw.closed) return;
        if (J !== X && X !== void 0) this.fsw._symlinkPaths.set(J, X);
      } else if (I.isSymbolicLink()) {
        let J = Y ? await gr1(A) : A;
        if (this.fsw.closed) return;
        let X = ZD.dirname(F.watchPath);
        if (this.fsw._getWatchedDir(X).add(F.watchPath), this.fsw._emit(Lw.ADD, F.watchPath, I), W = await this._handleDir(X, I, B, D, A, F, J), this.fsw.closed) return;
        if (J !== void 0) this.fsw._symlinkPaths.set(ZD.resolve(A), J);
      } else W = this._handleFile(F.watchPath, I, B);
      if (G(), W) this.fsw._addPathCloser(A, W);
      return !1;
    } catch (I) {
      if (this.fsw._handleError(I)) return G(), A;
    }
  }
} /*! chokidar - MIT License (c) 2012 Paul Miller (paulmillr.com) */
var pr1 = "/",
  T3Q = "//",
  dYA = ".",
  P3Q = "..",
  S3Q = "string",
  j3Q = /\\/g,
  fYA = /\/\//,
  y3Q = /\..*\.(sw[px])$|~$|\.subl.*\.tmp/,
  k3Q = /^\.[/\\]/;
function dK1(A) {
  return Array.isArray(A) ? A : [A];
}
var ir1 = A => typeof A === "object" && A !== null && !(A instanceof RegExp);
function _3Q(A) {
  if (typeof A === "function") return A;
  if (typeof A === "string") return B => A === B;
  if (A instanceof RegExp) return B => A.test(B);
  if (typeof A === "object" && A !== null) return B => {
    if (A.path === B) return !0;
    if (A.recursive) {
      let Q = J6.relative(A.path, B);
      if (!Q) return !1;
      return !Q.startsWith("..") && !J6.isAbsolute(Q);
    }
    return !1;
  };
  return () => !1;
}
function x3Q(A) {
  if (typeof A !== "string") throw new Error("string expected");
  A = J6.normalize(A), A = A.replace(/\\/g, "/");
  let B = !1;
  if (A.startsWith("//")) B = !0;
  let Q = /\/\//;
  while (A.match(Q)) A = A.replace(Q, "/");
  if (B) A = "/" + A;
  return A;
}
function hYA(A, B, Q) {
  let D = x3Q(B);
  for (let Z = 0; Z < A.length; Z++) {
    let G = A[Z];
    if (G(D, Q)) return !0;
  }
  return !1;
}
function v3Q(A, B) {
  if (A == null) throw new TypeError("anymatch: specify first argument");
  let D = dK1(A).map(Z => _3Q(Z));
  if (B == null) return (Z, G) => {
    return hYA(D, Z, G);
  };
  return hYA(D, B);
}
var gYA = A => {
    let B = dK1(A).flat();
    if (!B.every(Q => typeof Q === S3Q)) throw new TypeError(`Non-string provided as watch path: ${B}`);
    return B.map(cYA);
  },
  uYA = A => {
    let B = A.replace(j3Q, pr1),
      Q = !1;
    if (B.startsWith(T3Q)) Q = !0;
    while (B.match(fYA)) B = B.replace(fYA, pr1);
    if (Q) B = pr1 + B;
    return B;
  },
  cYA = A => uYA(J6.normalize(uYA(A))),
  mYA = (A = "") => B => {
    if (typeof B === "string") return cYA(J6.isAbsolute(B) ? B : J6.join(A, B));else return B;
  },
  b3Q = (A, B) => {
    if (J6.isAbsolute(A)) return A;
    return J6.join(B, A);
  },
  f3Q = Object.freeze(new Set());
class lYA {
  constructor(A, B) {
    this.path = A, this._removeWatcher = B, this.items = new Set();
  }
  add(A) {
    let {
      items: B
    } = this;
    if (!B) return;
    if (A !== dYA && A !== P3Q) B.add(A);
  }
  async remove(A) {
    let {
      items: B
    } = this;
    if (!B) return;
    if (B.delete(A), B.size > 0) return;
    let Q = this.path;
    try {
      await R3Q(Q);
    } catch (D) {
      if (this._removeWatcher) this._removeWatcher(J6.dirname(Q), J6.basename(Q));
    }
  }
  has(A) {
    let {
      items: B
    } = this;
    if (!B) return;
    return B.has(A);
  }
  getChildren() {
    let {
      items: A
    } = this;
    if (!A) return [];
    return [...A.values()];
  }
  dispose() {
    this.items.clear(), this.path = "", this._removeWatcher = uK1, this.items = f3Q, Object.freeze(this);
  }
}
var h3Q = "stat",
  g3Q = "lstat";
class pYA {
  constructor(A, B, Q) {
    this.fsw = Q;
    let D = A;
    this.path = A = A.replace(k3Q, ""), this.watchPath = D, this.fullWatchPath = J6.resolve(D), this.dirParts = [], this.dirParts.forEach(Z => {
      if (Z.length > 1) Z.pop();
    }), this.followSymlinks = B, this.statMethod = B ? h3Q : g3Q;
  }
  entryPath(A) {
    return J6.join(this.watchPath, J6.relative(this.watchPath, A.fullPath));
  }
  filterPath(A) {
    let {
      stats: B
    } = A;
    if (B && B.isSymbolicLink()) return this.filterDir(A);
    let Q = this.entryPath(A);
    return this.fsw._isntIgnored(Q, B) && this.fsw._hasReadPermissions(B);
  }
  filterDir(A) {
    return this.fsw._isntIgnored(this.entryPath(A), A.stats);
  }
}
class nr1 extends O3Q {
  constructor(A = {}) {
    super();
    this.closed = !1, this._closers = new Map(), this._ignoredPaths = new Set(), this._throttled = new Map(), this._streams = new Set(), this._symlinkPaths = new Map(), this._watched = new Map(), this._pendingWrites = new Map(), this._pendingUnlinks = new Map(), this._readyCount = 0, this._readyEmitted = !1;
    let B = A.awaitWriteFinish,
      Q = {
        stabilityThreshold: 2000,
        pollInterval: 100
      },
      D = {
        persistent: !0,
        ignoreInitial: !1,
        ignorePermissionErrors: !1,
        interval: 100,
        binaryInterval: 300,
        followSymlinks: !0,
        usePolling: !1,
        atomic: !0,
        ...A,
        ignored: A.ignored ? dK1(A.ignored) : dK1([]),
        awaitWriteFinish: B === !0 ? Q : typeof B === "object" ? {
          ...Q,
          ...B
        } : !1
      };
    if (vYA) D.usePolling = !0;
    if (D.atomic === void 0) D.atomic = !D.usePolling;
    let Z = process.env.CHOKIDAR_USEPOLLING;
    if (Z !== void 0) {
      let I = Z.toLowerCase();
      if (I === "false" || I === "0") D.usePolling = !1;else if (I === "true" || I === "1") D.usePolling = !0;else D.usePolling = !!I;
    }
    let G = process.env.CHOKIDAR_INTERVAL;
    if (G) D.interval = Number.parseInt(G, 10);
    let F = 0;
    this._emitReady = () => {
      if (F++, F >= this._readyCount) this._emitReady = uK1, this._readyEmitted = !0, process.nextTick(() => this.emit(P7.READY));
    }, this._emitRaw = (...I) => this.emit(P7.RAW, ...I), this._boundRemove = this._remove.bind(this), this.options = D, this._nodeFsHandler = new lr1(this), Object.freeze(D);
  }
  _addIgnoredPath(A) {
    if (ir1(A)) {
      for (let B of this._ignoredPaths) if (ir1(B) && B.path === A.path && B.recursive === A.recursive) return;
    }
    this._ignoredPaths.add(A);
  }
  _removeIgnoredPath(A) {
    if (this._ignoredPaths.delete(A), typeof A === "string") {
      for (let B of this._ignoredPaths) if (ir1(B) && B.path === A) this._ignoredPaths.delete(B);
    }
  }
  add(A, B, Q) {
    let {
      cwd: D
    } = this.options;
    this.closed = !1, this._closePromise = void 0;
    let Z = gYA(A);
    if (D) Z = Z.map(G => {
      return b3Q(G, D);
    });
    if (Z.forEach(G => {
      this._removeIgnoredPath(G);
    }), this._userIgnored = void 0, !this._readyCount) this._readyCount = 0;
    return this._readyCount += Z.length, Promise.all(Z.map(async G => {
      let F = await this._nodeFsHandler._addToNodeFs(G, !Q, void 0, 0, B);
      if (F) this._emitReady();
      return F;
    })).then(G => {
      if (this.closed) return;
      G.forEach(F => {
        if (F) this.add(J6.dirname(F), J6.basename(B || F));
      });
    }), this;
  }
  unwatch(A) {
    if (this.closed) return this;
    let B = gYA(A),
      {
        cwd: Q
      } = this.options;
    return B.forEach(D => {
      if (!J6.isAbsolute(D) && !this._closers.has(D)) {
        if (Q) D = J6.join(Q, D);
        D = J6.resolve(D);
      }
      if (this._closePath(D), this._addIgnoredPath(D), this._watched.has(D)) this._addIgnoredPath({
        path: D,
        recursive: !0
      });
      this._userIgnored = void 0;
    }), this;
  }
  close() {
    if (this._closePromise) return this._closePromise;
    this.closed = !0, this.removeAllListeners();
    let A = [];
    return this._closers.forEach(B => B.forEach(Q => {
      let D = Q();
      if (D instanceof Promise) A.push(D);
    })), this._streams.forEach(B => B.destroy()), this._userIgnored = void 0, this._readyCount = 0, this._readyEmitted = !1, this._watched.forEach(B => B.dispose()), this._closers.clear(), this._watched.clear(), this._streams.clear(), this._symlinkPaths.clear(), this._throttled.clear(), this._closePromise = A.length ? Promise.all(A).then(() => {
      return;
    }) : Promise.resolve(), this._closePromise;
  }
  getWatched() {
    let A = {};
    return this._watched.forEach((B, Q) => {
      let Z = (this.options.cwd ? J6.relative(this.options.cwd, Q) : Q) || dYA;
      A[Z] = B.getChildren().sort();
    }), A;
  }
  emitWithAll(A, B) {
    if (this.emit(A, ...B), A !== P7.ERROR) this.emit(P7.ALL, A, ...B);
  }
  async _emit(A, B, Q) {
    if (this.closed) return;
    let D = this.options;
    if (cr1) B = J6.normalize(B);
    if (D.cwd) B = J6.relative(D.cwd, B);
    let Z = [B];
    if (Q != null) Z.push(Q);
    let G = D.awaitWriteFinish,
      F;
    if (G && (F = this._pendingWrites.get(B))) return F.lastChange = new Date(), this;
    if (D.atomic) {
      if (A === P7.UNLINK) return this._pendingUnlinks.set(B, [A, ...Z]), setTimeout(() => {
        this._pendingUnlinks.forEach((I, Y) => {
          this.emit(...I), this.emit(P7.ALL, ...I), this._pendingUnlinks.delete(Y);
        });
      }, typeof D.atomic === "number" ? D.atomic : 100), this;
      if (A === P7.ADD && this._pendingUnlinks.has(B)) A = P7.CHANGE, this._pendingUnlinks.delete(B);
    }
    if (G && (A === P7.ADD || A === P7.CHANGE) && this._readyEmitted) {
      let I = (Y, W) => {
        if (Y) A = P7.ERROR, Z[0] = Y, this.emitWithAll(A, Z);else if (W) {
          if (Z.length > 1) Z[1] = W;else Z.push(W);
          this.emitWithAll(A, Z);
        }
      };
      return this._awaitWriteFinish(B, G.stabilityThreshold, A, I), this;
    }
    if (A === P7.CHANGE) {
      if (!this._throttle(P7.CHANGE, B, 50)) return this;
    }
    if (D.alwaysStat && Q === void 0 && (A === P7.ADD || A === P7.ADD_DIR || A === P7.CHANGE)) {
      let I = D.cwd ? J6.join(D.cwd, B) : B,
        Y;
      try {
        Y = await M3Q(I);
      } catch (W) {}
      if (!Y || this.closed) return;
      Z.push(Y);
    }
    return this.emitWithAll(A, Z), this;
  }
  _handleError(A) {
    let B = A && A.code;
    if (A && B !== "ENOENT" && B !== "ENOTDIR" && (!this.options.ignorePermissionErrors || B !== "EPERM" && B !== "EACCES")) this.emit(P7.ERROR, A);
    return A || this.closed;
  }
  _throttle(A, B, Q) {
    if (!this._throttled.has(A)) this._throttled.set(A, new Map());
    let D = this._throttled.get(A);
    if (!D) throw new Error("invalid throttle");
    let Z = D.get(B);
    if (Z) return Z.count++, !1;
    let G,
      F = () => {
        let Y = D.get(B),
          W = Y ? Y.count : 0;
        if (D.delete(B), clearTimeout(G), Y) clearTimeout(Y.timeoutObject);
        return W;
      };
    G = setTimeout(F, Q);
    let I = {
      timeoutObject: G,
      clear: F,
      count: 0
    };
    return D.set(B, I), I;
  }
  _incrReadyCount() {
    return this._readyCount++;
  }
  _awaitWriteFinish(A, B, Q, D) {
    let Z = this.options.awaitWriteFinish;
    if (typeof Z !== "object") return;
    let G = Z.pollInterval,
      F,
      I = A;
    if (this.options.cwd && !J6.isAbsolute(A)) I = J6.join(this.options.cwd, A);
    let Y = new Date(),
      W = this._pendingWrites;
    function J(X) {
      L3Q(I, (V, C) => {
        if (V || !W.has(A)) {
          if (V && V.code !== "ENOENT") D(V);
          return;
        }
        let K = Number(new Date());
        if (X && C.size !== X.size) W.get(A).lastChange = K;
        let H = W.get(A);
        if (K - H.lastChange >= B) W.delete(A), D(void 0, C);else F = setTimeout(J, G, C);
      });
    }
    if (!W.has(A)) W.set(A, {
      lastChange: Y,
      cancelWait: () => {
        return W.delete(A), clearTimeout(F), Q;
      }
    }), F = setTimeout(J, G);
  }
  _isIgnored(A, B) {
    if (this.options.atomic && y3Q.test(A)) return !0;
    if (!this._userIgnored) {
      let {
          cwd: Q
        } = this.options,
        Z = (this.options.ignored || []).map(mYA(Q)),
        F = [...[...this._ignoredPaths].map(mYA(Q)), ...Z];
      this._userIgnored = v3Q(F, void 0);
    }
    return this._userIgnored(A, B);
  }
  _isntIgnored(A, B) {
    return !this._isIgnored(A, B);
  }
  _getWatchHelpers(A) {
    return new pYA(A, this.options.followSymlinks, this);
  }
  _getWatchedDir(A) {
    let B = J6.resolve(A);
    if (!this._watched.has(B)) this._watched.set(B, new lYA(B, this._boundRemove));
    return this._watched.get(B);
  }
  _hasReadPermissions(A) {
    if (this.options.ignorePermissionErrors) return !0;
    return Boolean(Number(A.mode) & 256);
  }
  _remove(A, B, Q) {
    let D = J6.join(A, B),
      Z = J6.resolve(D);
    if (Q = Q != null ? Q : this._watched.has(D) || this._watched.has(Z), !this._throttle("remove", D, 100)) return;
    if (!Q && this._watched.size === 1) this.add(A, B, !0);
    this._getWatchedDir(D).getChildren().forEach(X => this._remove(D, X));
    let I = this._getWatchedDir(A),
      Y = I.has(B);
    if (I.remove(B), this._symlinkPaths.has(Z)) this._symlinkPaths.delete(Z);
    let W = D;
    if (this.options.cwd) W = J6.relative(this.options.cwd, D);
    if (this.options.awaitWriteFinish && this._pendingWrites.has(W)) {
      if (this._pendingWrites.get(W).cancelWait() === P7.ADD) return;
    }
    this._watched.delete(D), this._watched.delete(Z);
    let J = Q ? P7.UNLINK_DIR : P7.UNLINK;
    if (Y && !this._isIgnored(D)) this._emit(J, D);
    this._closePath(D);
  }
  _closePath(A) {
    this._closeFile(A);
    let B = J6.dirname(A);
    this._getWatchedDir(B).remove(J6.basename(A));
  }
  _closeFile(A) {
    let B = this._closers.get(A);
    if (!B) return;
    B.forEach(Q => Q()), this._closers.delete(A);
  }
  _addPathCloser(A, B) {
    if (!B) return;
    let Q = this._closers.get(A);
    if (!Q) Q = [], this._closers.set(A, Q);
    Q.push(B);
  }
  _readdirp(A, B) {
    if (this.closed) return;
    let Q = {
        type: P7.ALL,
        alwaysStat: !0,
        lstat: !0,
        ...B,
        depth: 0
      },
      D = jYA(A, Q);
    return this._streams.add(D), D.once(xYA, () => {
      D = void 0;
    }), D.once(dr1, () => {
      if (D) this._streams.delete(D), D = void 0;
    }), D;
  }
}
module.exports = {
  nr1
};