/**
 * Isolated bundle for 'BT0'
 */

var idB = F1(oK0(), 1);
class BT0 extends OD1 {
  constructor(A, B) {
    var Q;
    super(B);
    this._serverInfo = A, this._capabilities = (Q = B === null || B === void 0 ? void 0 : B.capabilities) !== null && Q !== void 0 ? Q : {}, this._instructions = B === null || B === void 0 ? void 0 : B.instructions, this.setRequestHandler(CK0, D => this._oninitialize(D)), this.setNotificationHandler(Ay1, () => {
      var D;
      return (D = this.oninitialized) === null || D === void 0 ? void 0 : D.call(this);
    });
  }
  registerCapabilities(A) {
    if (this.transport) throw new Error("Cannot register capabilities after connecting to transport");
    this._capabilities = Gy1(this._capabilities, A);
  }
  assertCapabilityForMethod(A) {
    var B, Q, D;
    switch (A) {
      case "sampling/createMessage":
        if (!((B = this._clientCapabilities) === null || B === void 0 ? void 0 : B.sampling)) throw new Error(`Client does not support sampling (required for ${A})`);
        break;
      case "elicitation/create":
        if (!((Q = this._clientCapabilities) === null || Q === void 0 ? void 0 : Q.elicitation)) throw new Error(`Client does not support elicitation (required for ${A})`);
        break;
      case "roots/list":
        if (!((D = this._clientCapabilities) === null || D === void 0 ? void 0 : D.roots)) throw new Error(`Client does not support listing roots (required for ${A})`);
        break;
      case "ping":
        break;
    }
  }
  assertNotificationCapability(A) {
    switch (A) {
      case "notifications/message":
        if (!this._capabilities.logging) throw new Error(`Server does not support logging (required for ${A})`);
        break;
      case "notifications/resources/updated":
      case "notifications/resources/list_changed":
        if (!this._capabilities.resources) throw new Error(`Server does not support notifying about resources (required for ${A})`);
        break;
      case "notifications/tools/list_changed":
        if (!this._capabilities.tools) throw new Error(`Server does not support notifying of tool list changes (required for ${A})`);
        break;
      case "notifications/prompts/list_changed":
        if (!this._capabilities.prompts) throw new Error(`Server does not support notifying of prompt list changes (required for ${A})`);
        break;
      case "notifications/cancelled":
        break;
      case "notifications/progress":
        break;
    }
  }
  assertRequestHandlerCapability(A) {
    switch (A) {
      case "sampling/createMessage":
        if (!this._capabilities.sampling) throw new Error(`Server does not support sampling (required for ${A})`);
        break;
      case "logging/setLevel":
        if (!this._capabilities.logging) throw new Error(`Server does not support logging (required for ${A})`);
        break;
      case "prompts/get":
      case "prompts/list":
        if (!this._capabilities.prompts) throw new Error(`Server does not support prompts (required for ${A})`);
        break;
      case "resources/list":
      case "resources/templates/list":
      case "resources/read":
        if (!this._capabilities.resources) throw new Error(`Server does not support resources (required for ${A})`);
        break;
      case "tools/call":
      case "tools/list":
        if (!this._capabilities.tools) throw new Error(`Server does not support tools (required for ${A})`);
        break;
      case "ping":
      case "initialize":
        break;
    }
  }
  async _oninitialize(A) {
    let B = A.params.protocolVersion;
    return this._clientCapabilities = A.params.capabilities, this._clientVersion = A.params.clientInfo, {
      protocolVersion: sj1.includes(B) ? B : Px,
      capabilities: this.getCapabilities(),
      serverInfo: this._serverInfo,
      ...(this._instructions && {
        instructions: this._instructions
      })
    };
  }
  getClientCapabilities() {
    return this._clientCapabilities;
  }
  getClientVersion() {
    return this._clientVersion;
  }
  getCapabilities() {
    return this._capabilities;
  }
  async ping() {
    return this.request({
      method: "ping"
    }, mP);
  }
  async createMessage(A, B) {
    return this.request({
      method: "sampling/createMessage",
      params: A
    }, NK0, B);
  }
  async elicitInput(A, B) {
    let Q = await this.request({
      method: "elicitation/create",
      params: A
    }, LK0, B);
    if (Q.action === "accept" && Q.content) try {
      let D = new idB.default(),
        Z = D.compile(A.requestedSchema);
      if (!Z(Q.content)) throw new XX(JX.InvalidParams, `Elicitation response content does not match requested schema: ${D.errorsText(Z.errors)}`);
    } catch (D) {
      if (D instanceof XX) throw D;
      throw new XX(JX.InternalError, `Error validating elicitation response: ${D}`);
    }
    return Q;
  }
  async listRoots(A, B) {
    return this.request({
      method: "roots/list",
      params: A
    }, OK0, B);
  }
  async sendLoggingMessage(A) {
    return this.notification({
      method: "notifications/message",
      params: A
    });
  }
  async sendResourceUpdated(A) {
    return this.notification({
      method: "notifications/resources/updated",
      params: A
    });
  }
  async sendResourceListChanged() {
    return this.notification({
      method: "notifications/resources/list_changed"
    });
  }
  async sendToolListChanged() {
    return this.notification({
      method: "notifications/tools/list_changed"
    });
  }
  async sendPromptListChanged() {
    return this.notification({
      method: "notifications/prompts/list_changed"
    });
  }
}
module.exports = {
  BT0
};