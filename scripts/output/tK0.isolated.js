/**
 * Isolated bundle for 'tK0'
 */

const {
  B3B,
  B5B,
  C3B,
  D3B,
  E3B,
  G3B,
  H3B,
  Hy1,
  Iy1,
  J5B,
  Jm,
  Ky1,
  L3B,
  L5B,
  R3B,
  R5B,
  T3B,
  X3B,
  Z5B,
  _3B,
  aK0,
  b3B,
  c3B,
  c5B,
  cK0,
  e5B,
  f5B,
  g5B,
  h3B,
  iK0,
  j3B,
  l3B,
  lK0,
  m3B,
  m5B,
  mK0,
  n5B,
  o5B,
  oK0,
  p5B,
  pK0,
  q3B,
  s5B,
  uK0,
  w3B,
  xK0
} = require("./B3B_B5B_C3B_D3B_E3B_more.js");
var Px = "2025-06-18";
var sj1 = [Px, "2025-03-26", "2024-11-05", "2024-10-07"],
  rj1 = "2.0",
  h8B = g.union([g.string(), g.number().int()]),
  g8B = g.string(),
  Fj6 = g.object({
    progressToken: g.optional(h8B)
  }).passthrough(),
  yE = g.object({
    _meta: g.optional(Fj6)
  }).passthrough(),
  aV = g.object({
    method: g.string(),
    params: g.optional(yE)
  }),
  $D1 = g.object({
    _meta: g.optional(g.object({}).passthrough())
  }).passthrough(),
  RM = g.object({
    method: g.string(),
    params: g.optional($D1)
  }),
  kE = g.object({
    _meta: g.optional(g.object({}).passthrough())
  }).passthrough(),
  oj1 = g.union([g.string(), g.number().int()]),
  u8B = g.object({
    jsonrpc: g.literal(rj1),
    id: oj1
  }).merge(aV).strict(),
  tj1 = A => u8B.safeParse(A).success,
  m8B = g.object({
    jsonrpc: g.literal(rj1)
  }).merge(RM).strict(),
  d8B = A => m8B.safeParse(A).success,
  c8B = g.object({
    jsonrpc: g.literal(rj1),
    id: oj1,
    result: kE
  }).strict(),
  qD1 = A => c8B.safeParse(A).success,
  JX;
var l8B = g.object({
    jsonrpc: g.literal(rj1),
    id: oj1,
    error: g.object({
      code: g.number().int(),
      message: g.string(),
      data: g.optional(g.unknown())
    })
  }).strict(),
  p8B = A => l8B.safeParse(A).success,
  OM = g.union([u8B, m8B, c8B, l8B]),
  mP = kE.strict(),
  ej1 = RM.extend({
    method: g.literal("notifications/cancelled"),
    params: $D1.extend({
      requestId: oj1,
      reason: g.string().optional()
    })
  }),
  ND1 = g.object({
    name: g.string(),
    title: g.optional(g.string())
  }).passthrough(),
  i8B = ND1.extend({
    version: g.string()
  }),
  Ij6 = g.object({
    experimental: g.optional(g.object({}).passthrough()),
    sampling: g.optional(g.object({}).passthrough()),
    elicitation: g.optional(g.object({}).passthrough()),
    roots: g.optional(g.object({
      listChanged: g.optional(g.boolean())
    }).passthrough())
  }).passthrough(),
  CK0 = aV.extend({
    method: g.literal("initialize"),
    params: yE.extend({
      protocolVersion: g.string(),
      capabilities: Ij6,
      clientInfo: i8B
    })
  });
var Yj6 = g.object({
    experimental: g.optional(g.object({}).passthrough()),
    logging: g.optional(g.object({}).passthrough()),
    completions: g.optional(g.object({}).passthrough()),
    prompts: g.optional(g.object({
      listChanged: g.optional(g.boolean())
    }).passthrough()),
    resources: g.optional(g.object({
      subscribe: g.optional(g.boolean()),
      listChanged: g.optional(g.boolean())
    }).passthrough()),
    tools: g.optional(g.object({
      listChanged: g.optional(g.boolean())
    }).passthrough())
  }).passthrough(),
  KK0 = kE.extend({
    protocolVersion: g.string(),
    capabilities: Yj6,
    serverInfo: i8B,
    instructions: g.optional(g.string())
  }),
  Ay1 = RM.extend({
    method: g.literal("notifications/initialized")
  }),
  n8B = A => Ay1.safeParse(A).success,
  By1 = aV.extend({
    method: g.literal("ping")
  }),
  Wj6 = g.object({
    progress: g.number(),
    total: g.optional(g.number()),
    message: g.optional(g.string())
  }).passthrough(),
  Qy1 = RM.extend({
    method: g.literal("notifications/progress"),
    params: $D1.merge(Wj6).extend({
      progressToken: h8B
    })
  }),
  Dy1 = aV.extend({
    params: yE.extend({
      cursor: g.optional(g8B)
    }).optional()
  }),
  Zy1 = kE.extend({
    nextCursor: g.optional(g8B)
  }),
  a8B = g.object({
    uri: g.string(),
    mimeType: g.optional(g.string()),
    _meta: g.optional(g.object({}).passthrough())
  }).passthrough(),
  s8B = a8B.extend({
    text: g.string()
  }),
  r8B = a8B.extend({
    blob: g.string().base64()
  }),
  o8B = ND1.extend({
    uri: g.string(),
    description: g.optional(g.string()),
    mimeType: g.optional(g.string()),
    _meta: g.optional(g.object({}).passthrough())
  }),
  Jj6 = ND1.extend({
    uriTemplate: g.string(),
    description: g.optional(g.string()),
    mimeType: g.optional(g.string()),
    _meta: g.optional(g.object({}).passthrough())
  }),
  Xj6 = Dy1.extend({
    method: g.literal("resources/list")
  }),
  Wm = Zy1.extend({
    resources: g.array(o8B)
  }),
  Vj6 = Dy1.extend({
    method: g.literal("resources/templates/list")
  }),
  HK0 = Zy1.extend({
    resourceTemplates: g.array(Jj6)
  }),
  Cj6 = aV.extend({
    method: g.literal("resources/read"),
    params: yE.extend({
      uri: g.string()
    })
  }),
  LD1 = kE.extend({
    contents: g.array(g.union([s8B, r8B]))
  }),
  Kj6 = RM.extend({
    method: g.literal("notifications/resources/list_changed")
  }),
  Hj6 = aV.extend({
    method: g.literal("resources/subscribe"),
    params: yE.extend({
      uri: g.string()
    })
  }),
  zj6 = aV.extend({
    method: g.literal("resources/unsubscribe"),
    params: yE.extend({
      uri: g.string()
    })
  }),
  Ej6 = RM.extend({
    method: g.literal("notifications/resources/updated"),
    params: $D1.extend({
      uri: g.string()
    })
  }),
  Uj6 = g.object({
    name: g.string(),
    description: g.optional(g.string()),
    required: g.optional(g.boolean())
  }).passthrough(),
  wj6 = ND1.extend({
    description: g.optional(g.string()),
    arguments: g.optional(g.array(Uj6)),
    _meta: g.optional(g.object({}).passthrough())
  }),
  $j6 = Dy1.extend({
    method: g.literal("prompts/list")
  }),
  MD1 = Zy1.extend({
    prompts: g.array(wj6)
  }),
  qj6 = aV.extend({
    method: g.literal("prompts/get"),
    params: yE.extend({
      name: g.string(),
      arguments: g.optional(g.record(g.string()))
    })
  }),
  zK0 = g.object({
    type: g.literal("text"),
    text: g.string(),
    _meta: g.optional(g.object({}).passthrough())
  }).passthrough(),
  EK0 = g.object({
    type: g.literal("image"),
    data: g.string().base64(),
    mimeType: g.string(),
    _meta: g.optional(g.object({}).passthrough())
  }).passthrough(),
  UK0 = g.object({
    type: g.literal("audio"),
    data: g.string().base64(),
    mimeType: g.string(),
    _meta: g.optional(g.object({}).passthrough())
  }).passthrough(),
  Nj6 = g.object({
    type: g.literal("resource"),
    resource: g.union([s8B, r8B]),
    _meta: g.optional(g.object({}).passthrough())
  }).passthrough(),
  Lj6 = o8B.extend({
    type: g.literal("resource_link")
  }),
  t8B = g.union([zK0, EK0, UK0, Lj6, Nj6]),
  Mj6 = g.object({
    role: g.enum(["user", "assistant"]),
    content: t8B
  }).passthrough(),
  wK0 = kE.extend({
    description: g.optional(g.string()),
    messages: g.array(Mj6)
  }),
  Rj6 = RM.extend({
    method: g.literal("notifications/prompts/list_changed")
  }),
  Oj6 = g.object({
    title: g.optional(g.string()),
    readOnlyHint: g.optional(g.boolean()),
    destructiveHint: g.optional(g.boolean()),
    idempotentHint: g.optional(g.boolean()),
    openWorldHint: g.optional(g.boolean())
  }).passthrough(),
  Tj6 = ND1.extend({
    description: g.optional(g.string()),
    inputSchema: g.object({
      type: g.literal("object"),
      properties: g.optional(g.object({}).passthrough()),
      required: g.optional(g.array(g.string()))
    }).passthrough(),
    outputSchema: g.optional(g.object({
      type: g.literal("object"),
      properties: g.optional(g.object({}).passthrough()),
      required: g.optional(g.array(g.string()))
    }).passthrough()),
    annotations: g.optional(Oj6),
    _meta: g.optional(g.object({}).passthrough())
  }),
  $K0 = Dy1.extend({
    method: g.literal("tools/list")
  }),
  RD1 = Zy1.extend({
    tools: g.array(Tj6)
  }),
  Ze = kE.extend({
    content: g.array(t8B).default([]),
    structuredContent: g.object({}).passthrough().optional(),
    isError: g.optional(g.boolean())
  }),
  do5 = Ze.or(kE.extend({
    toolResult: g.unknown()
  })),
  qK0 = aV.extend({
    method: g.literal("tools/call"),
    params: yE.extend({
      name: g.string(),
      arguments: g.optional(g.record(g.unknown()))
    })
  }),
  Pj6 = RM.extend({
    method: g.literal("notifications/tools/list_changed")
  }),
  e8B = g.enum(["debug", "info", "notice", "warning", "error", "critical", "alert", "emergency"]),
  Sj6 = aV.extend({
    method: g.literal("logging/setLevel"),
    params: yE.extend({
      level: e8B
    })
  }),
  jj6 = RM.extend({
    method: g.literal("notifications/message"),
    params: $D1.extend({
      level: e8B,
      logger: g.optional(g.string()),
      data: g.unknown()
    })
  }),
  yj6 = g.object({
    name: g.string().optional()
  }).passthrough(),
  kj6 = g.object({
    hints: g.optional(g.array(yj6)),
    costPriority: g.optional(g.number().min(0).max(1)),
    speedPriority: g.optional(g.number().min(0).max(1)),
    intelligencePriority: g.optional(g.number().min(0).max(1))
  }).passthrough(),
  _j6 = g.object({
    role: g.enum(["user", "assistant"]),
    content: g.union([zK0, EK0, UK0])
  }).passthrough(),
  xj6 = aV.extend({
    method: g.literal("sampling/createMessage"),
    params: yE.extend({
      messages: g.array(_j6),
      systemPrompt: g.optional(g.string()),
      includeContext: g.optional(g.enum(["none", "thisServer", "allServers"])),
      temperature: g.optional(g.number()),
      maxTokens: g.number().int(),
      stopSequences: g.optional(g.array(g.string())),
      metadata: g.optional(g.object({}).passthrough()),
      modelPreferences: g.optional(kj6)
    })
  }),
  NK0 = kE.extend({
    model: g.string(),
    stopReason: g.optional(g.enum(["endTurn", "stopSequence", "maxTokens"]).or(g.string())),
    role: g.enum(["user", "assistant"]),
    content: g.discriminatedUnion("type", [zK0, EK0, UK0])
  }),
  vj6 = g.object({
    type: g.literal("boolean"),
    title: g.optional(g.string()),
    description: g.optional(g.string()),
    default: g.optional(g.boolean())
  }).passthrough(),
  bj6 = g.object({
    type: g.literal("string"),
    title: g.optional(g.string()),
    description: g.optional(g.string()),
    minLength: g.optional(g.number()),
    maxLength: g.optional(g.number()),
    format: g.optional(g.enum(["email", "uri", "date", "date-time"]))
  }).passthrough(),
  fj6 = g.object({
    type: g.enum(["number", "integer"]),
    title: g.optional(g.string()),
    description: g.optional(g.string()),
    minimum: g.optional(g.number()),
    maximum: g.optional(g.number())
  }).passthrough(),
  hj6 = g.object({
    type: g.literal("string"),
    title: g.optional(g.string()),
    description: g.optional(g.string()),
    enum: g.array(g.string()),
    enumNames: g.optional(g.array(g.string()))
  }).passthrough(),
  gj6 = g.union([vj6, bj6, fj6, hj6]),
  uj6 = aV.extend({
    method: g.literal("elicitation/create"),
    params: yE.extend({
      message: g.string(),
      requestedSchema: g.object({
        type: g.literal("object"),
        properties: g.record(g.string(), gj6),
        required: g.optional(g.array(g.string()))
      }).passthrough()
    })
  }),
  LK0 = kE.extend({
    action: g.enum(["accept", "decline", "cancel"]),
    content: g.optional(g.record(g.string(), g.unknown()))
  }),
  mj6 = g.object({
    type: g.literal("ref/resource"),
    uri: g.string()
  }).passthrough();
var dj6 = g.object({
    type: g.literal("ref/prompt"),
    name: g.string()
  }).passthrough(),
  cj6 = aV.extend({
    method: g.literal("completion/complete"),
    params: yE.extend({
      ref: g.union([dj6, mj6]),
      argument: g.object({
        name: g.string(),
        value: g.string()
      }).passthrough(),
      context: g.optional(g.object({
        arguments: g.optional(g.record(g.string(), g.string()))
      }))
    })
  }),
  MK0 = kE.extend({
    completion: g.object({
      values: g.array(g.string()).max(100),
      total: g.optional(g.number().int()),
      hasMore: g.optional(g.boolean())
    }).passthrough()
  }),
  lj6 = g.object({
    uri: g.string().startsWith("file://"),
    name: g.optional(g.string()),
    _meta: g.optional(g.object({}).passthrough())
  }).passthrough(),
  RK0 = aV.extend({
    method: g.literal("roots/list")
  }),
  OK0 = kE.extend({
    roots: g.array(lj6)
  }),
  pj6 = RM.extend({
    method: g.literal("notifications/roots/list_changed")
  }),
  co5 = g.union([By1, CK0, cj6, Sj6, qj6, $j6, Xj6, Vj6, Cj6, Hj6, zj6, qK0, $K0]),
  lo5 = g.union([ej1, Qy1, Ay1, pj6]),
  po5 = g.union([mP, NK0, LK0, OK0]),
  io5 = g.union([By1, xj6, uj6, RK0]),
  no5 = g.union([ej1, Qy1, jj6, Ej6, Kj6, Pj6, Rj6]),
  ao5 = g.union([mP, KK0, MK0, wK0, MD1, Wm, HK0, LD1, Ze, RD1]);
class XX extends Error {
  constructor(A, B, Q) {
    super(`MCP error ${A}: ${B}`);
    this.code = A, this.data = Q, this.name = "McpError";
  }
}
var ij6 = 60000;
class OD1 {
  constructor(A) {
    this._options = A, this._requestMessageId = 0, this._requestHandlers = new Map(), this._requestHandlerAbortControllers = new Map(), this._notificationHandlers = new Map(), this._responseHandlers = new Map(), this._progressHandlers = new Map(), this._timeoutInfo = new Map(), this.setNotificationHandler(ej1, B => {
      let Q = this._requestHandlerAbortControllers.get(B.params.requestId);
      Q === null || Q === void 0 || Q.abort(B.params.reason);
    }), this.setNotificationHandler(Qy1, B => {
      this._onprogress(B);
    }), this.setRequestHandler(By1, B => ({}));
  }
  _setupTimeout(A, B, Q, D, Z = !1) {
    this._timeoutInfo.set(A, {
      timeoutId: setTimeout(D, B),
      startTime: Date.now(),
      timeout: B,
      maxTotalTimeout: Q,
      resetTimeoutOnProgress: Z,
      onTimeout: D
    });
  }
  _resetTimeout(A) {
    let B = this._timeoutInfo.get(A);
    if (!B) return !1;
    let Q = Date.now() - B.startTime;
    if (B.maxTotalTimeout && Q >= B.maxTotalTimeout) throw this._timeoutInfo.delete(A), new XX(JX.RequestTimeout, "Maximum total timeout exceeded", {
      maxTotalTimeout: B.maxTotalTimeout,
      totalElapsed: Q
    });
    return clearTimeout(B.timeoutId), B.timeoutId = setTimeout(B.onTimeout, B.timeout), !0;
  }
  _cleanupTimeout(A) {
    let B = this._timeoutInfo.get(A);
    if (B) clearTimeout(B.timeoutId), this._timeoutInfo.delete(A);
  }
  async connect(A) {
    var B, Q, D;
    this._transport = A;
    let Z = (B = this.transport) === null || B === void 0 ? void 0 : B.onclose;
    this._transport.onclose = () => {
      Z === null || Z === void 0 || Z(), this._onclose();
    };
    let G = (Q = this.transport) === null || Q === void 0 ? void 0 : Q.onerror;
    this._transport.onerror = I => {
      G === null || G === void 0 || G(I), this._onerror(I);
    };
    let F = (D = this._transport) === null || D === void 0 ? void 0 : D.onmessage;
    this._transport.onmessage = (I, Y) => {
      if (F === null || F === void 0 || F(I, Y), qD1(I) || p8B(I)) this._onresponse(I);else if (tj1(I)) this._onrequest(I, Y);else if (d8B(I)) this._onnotification(I);else this._onerror(new Error(`Unknown message type: ${JSON.stringify(I)}`));
    }, await this._transport.start();
  }
  _onclose() {
    var A;
    let B = this._responseHandlers;
    this._responseHandlers = new Map(), this._progressHandlers.clear(), this._transport = void 0, (A = this.onclose) === null || A === void 0 || A.call(this);
    let Q = new XX(JX.ConnectionClosed, "Connection closed");
    for (let D of B.values()) D(Q);
  }
  _onerror(A) {
    var B;
    (B = this.onerror) === null || B === void 0 || B.call(this, A);
  }
  _onnotification(A) {
    var B;
    let Q = (B = this._notificationHandlers.get(A.method)) !== null && B !== void 0 ? B : this.fallbackNotificationHandler;
    if (Q === void 0) return;
    Promise.resolve().then(() => Q(A)).catch(D => this._onerror(new Error(`Uncaught error in notification handler: ${D}`)));
  }
  _onrequest(A, B) {
    var Q, D, Z, G;
    let F = (Q = this._requestHandlers.get(A.method)) !== null && Q !== void 0 ? Q : this.fallbackRequestHandler;
    if (F === void 0) {
      (D = this._transport) === null || D === void 0 || D.send({
        jsonrpc: "2.0",
        id: A.id,
        error: {
          code: JX.MethodNotFound,
          message: "Method not found"
        }
      }).catch(W => this._onerror(new Error(`Failed to send an error response: ${W}`)));
      return;
    }
    let I = new AbortController();
    this._requestHandlerAbortControllers.set(A.id, I);
    let Y = {
      signal: I.signal,
      sessionId: (Z = this._transport) === null || Z === void 0 ? void 0 : Z.sessionId,
      _meta: (G = A.params) === null || G === void 0 ? void 0 : G._meta,
      sendNotification: W => this.notification(W, {
        relatedRequestId: A.id
      }),
      sendRequest: (W, J, X) => this.request(W, J, {
        ...X,
        relatedRequestId: A.id
      }),
      authInfo: B === null || B === void 0 ? void 0 : B.authInfo,
      requestId: A.id,
      requestInfo: B === null || B === void 0 ? void 0 : B.requestInfo
    };
    Promise.resolve().then(() => F(A, Y)).then(W => {
      var J;
      if (I.signal.aborted) return;
      return (J = this._transport) === null || J === void 0 ? void 0 : J.send({
        result: W,
        jsonrpc: "2.0",
        id: A.id
      });
    }, W => {
      var J, X;
      if (I.signal.aborted) return;
      return (J = this._transport) === null || J === void 0 ? void 0 : J.send({
        jsonrpc: "2.0",
        id: A.id,
        error: {
          code: Number.isSafeInteger(W.code) ? W.code : JX.InternalError,
          message: (X = W.message) !== null && X !== void 0 ? X : "Internal error"
        }
      });
    }).catch(W => this._onerror(new Error(`Failed to send response: ${W}`))).finally(() => {
      this._requestHandlerAbortControllers.delete(A.id);
    });
  }
  _onprogress(A) {
    let {
        progressToken: B,
        ...Q
      } = A.params,
      D = Number(B),
      Z = this._progressHandlers.get(D);
    if (!Z) {
      this._onerror(new Error(`Received a progress notification for an unknown token: ${JSON.stringify(A)}`));
      return;
    }
    let G = this._responseHandlers.get(D),
      F = this._timeoutInfo.get(D);
    if (F && G && F.resetTimeoutOnProgress) try {
      this._resetTimeout(D);
    } catch (I) {
      G(I);
      return;
    }
    Z(Q);
  }
  _onresponse(A) {
    let B = Number(A.id),
      Q = this._responseHandlers.get(B);
    if (Q === void 0) {
      this._onerror(new Error(`Received a response for an unknown message ID: ${JSON.stringify(A)}`));
      return;
    }
    if (this._responseHandlers.delete(B), this._progressHandlers.delete(B), this._cleanupTimeout(B), qD1(A)) Q(A);else {
      let D = new XX(A.error.code, A.error.message, A.error.data);
      Q(D);
    }
  }
  get transport() {
    return this._transport;
  }
  async close() {
    var A;
    await ((A = this._transport) === null || A === void 0 ? void 0 : A.close());
  }
  request(A, B, Q) {
    let {
      relatedRequestId: D,
      resumptionToken: Z,
      onresumptiontoken: G
    } = Q !== null && Q !== void 0 ? Q : {};
    return new Promise((F, I) => {
      var Y, W, J, X, V, C;
      if (!this._transport) {
        I(new Error("Not connected"));
        return;
      }
      if (((Y = this._options) === null || Y === void 0 ? void 0 : Y.enforceStrictCapabilities) === !0) this.assertCapabilityForMethod(A.method);
      (W = Q === null || Q === void 0 ? void 0 : Q.signal) === null || W === void 0 || W.throwIfAborted();
      let K = this._requestMessageId++,
        H = {
          ...A,
          jsonrpc: "2.0",
          id: K
        };
      if (Q === null || Q === void 0 ? void 0 : Q.onprogress) this._progressHandlers.set(K, Q.onprogress), H.params = {
        ...A.params,
        _meta: {
          ...(((J = A.params) === null || J === void 0 ? void 0 : J._meta) || {}),
          progressToken: K
        }
      };
      let z = N => {
        var O;
        this._responseHandlers.delete(K), this._progressHandlers.delete(K), this._cleanupTimeout(K), (O = this._transport) === null || O === void 0 || O.send({
          jsonrpc: "2.0",
          method: "notifications/cancelled",
          params: {
            requestId: K,
            reason: String(N)
          }
        }, {
          relatedRequestId: D,
          resumptionToken: Z,
          onresumptiontoken: G
        }).catch(R => this._onerror(new Error(`Failed to send cancellation: ${R}`))), I(N);
      };
      this._responseHandlers.set(K, N => {
        var O;
        if ((O = Q === null || Q === void 0 ? void 0 : Q.signal) === null || O === void 0 ? void 0 : O.aborted) return;
        if (N instanceof Error) return I(N);
        try {
          let R = B.parse(N.result);
          F(R);
        } catch (R) {
          I(R);
        }
      }), (X = Q === null || Q === void 0 ? void 0 : Q.signal) === null || X === void 0 || X.addEventListener("abort", () => {
        var N;
        z((N = Q === null || Q === void 0 ? void 0 : Q.signal) === null || N === void 0 ? void 0 : N.reason);
      });
      let $ = (V = Q === null || Q === void 0 ? void 0 : Q.timeout) !== null && V !== void 0 ? V : ij6,
        L = () => z(new XX(JX.RequestTimeout, "Request timed out", {
          timeout: $
        }));
      this._setupTimeout(K, $, Q === null || Q === void 0 ? void 0 : Q.maxTotalTimeout, L, (C = Q === null || Q === void 0 ? void 0 : Q.resetTimeoutOnProgress) !== null && C !== void 0 ? C : !1), this._transport.send(H, {
        relatedRequestId: D,
        resumptionToken: Z,
        onresumptiontoken: G
      }).catch(N => {
        this._cleanupTimeout(K), I(N);
      });
    });
  }
  async notification(A, B) {
    if (!this._transport) throw new Error("Not connected");
    this.assertNotificationCapability(A.method);
    let Q = {
      ...A,
      jsonrpc: "2.0"
    };
    await this._transport.send(Q, B);
  }
  setRequestHandler(A, B) {
    let Q = A.shape.method.value;
    this.assertRequestHandlerCapability(Q), this._requestHandlers.set(Q, (D, Z) => {
      return Promise.resolve(B(A.parse(D), Z));
    });
  }
  removeRequestHandler(A) {
    this._requestHandlers.delete(A);
  }
  assertCanSetRequestHandler(A) {
    if (this._requestHandlers.has(A)) throw new Error(`A request handler for ${A} already exists, which would be overridden`);
  }
  setNotificationHandler(A, B) {
    this._notificationHandlers.set(A.shape.method.value, Q => Promise.resolve(B(A.parse(Q))));
  }
  removeNotificationHandler(A) {
    this._notificationHandlers.delete(A);
  }
}
function Gy1(A, B) {
  return Object.entries(B).reduce((Q, [D, Z]) => {
    if (Z && typeof Z === "object") Q[D] = Q[D] ? {
      ...Q[D],
      ...Z
    } : Z;else Q[D] = Z;
    return Q;
  }, {
    ...A
  });
}
var A7B = F1(oK0(), 1);
class tK0 extends OD1 {
  constructor(A, B) {
    var Q;
    super(B);
    this._clientInfo = A, this._cachedToolOutputValidators = new Map(), this._capabilities = (Q = B === null || B === void 0 ? void 0 : B.capabilities) !== null && Q !== void 0 ? Q : {}, this._ajv = new A7B.default();
  }
  registerCapabilities(A) {
    if (this.transport) throw new Error("Cannot register capabilities after connecting to transport");
    this._capabilities = Gy1(this._capabilities, A);
  }
  assertCapability(A, B) {
    var Q;
    if (!((Q = this._serverCapabilities) === null || Q === void 0 ? void 0 : Q[A])) throw new Error(`Server does not support ${A} (required for ${B})`);
  }
  async connect(A, B) {
    if (await super.connect(A), A.sessionId !== void 0) return;
    try {
      let Q = await this.request({
        method: "initialize",
        params: {
          protocolVersion: Px,
          capabilities: this._capabilities,
          clientInfo: this._clientInfo
        }
      }, KK0, B);
      if (Q === void 0) throw new Error(`Server sent invalid initialize result: ${Q}`);
      if (!sj1.includes(Q.protocolVersion)) throw new Error(`Server's protocol version is not supported: ${Q.protocolVersion}`);
      if (this._serverCapabilities = Q.capabilities, this._serverVersion = Q.serverInfo, A.setProtocolVersion) A.setProtocolVersion(Q.protocolVersion);
      this._instructions = Q.instructions, await this.notification({
        method: "notifications/initialized"
      });
    } catch (Q) {
      throw this.close(), Q;
    }
  }
  getServerCapabilities() {
    return this._serverCapabilities;
  }
  getServerVersion() {
    return this._serverVersion;
  }
  getInstructions() {
    return this._instructions;
  }
  assertCapabilityForMethod(A) {
    var B, Q, D, Z, G;
    switch (A) {
      case "logging/setLevel":
        if (!((B = this._serverCapabilities) === null || B === void 0 ? void 0 : B.logging)) throw new Error(`Server does not support logging (required for ${A})`);
        break;
      case "prompts/get":
      case "prompts/list":
        if (!((Q = this._serverCapabilities) === null || Q === void 0 ? void 0 : Q.prompts)) throw new Error(`Server does not support prompts (required for ${A})`);
        break;
      case "resources/list":
      case "resources/templates/list":
      case "resources/read":
      case "resources/subscribe":
      case "resources/unsubscribe":
        if (!((D = this._serverCapabilities) === null || D === void 0 ? void 0 : D.resources)) throw new Error(`Server does not support resources (required for ${A})`);
        if (A === "resources/subscribe" && !this._serverCapabilities.resources.subscribe) throw new Error(`Server does not support resource subscriptions (required for ${A})`);
        break;
      case "tools/call":
      case "tools/list":
        if (!((Z = this._serverCapabilities) === null || Z === void 0 ? void 0 : Z.tools)) throw new Error(`Server does not support tools (required for ${A})`);
        break;
      case "completion/complete":
        if (!((G = this._serverCapabilities) === null || G === void 0 ? void 0 : G.completions)) throw new Error(`Server does not support completions (required for ${A})`);
        break;
      case "initialize":
        break;
      case "ping":
        break;
    }
  }
  assertNotificationCapability(A) {
    var B;
    switch (A) {
      case "notifications/roots/list_changed":
        if (!((B = this._capabilities.roots) === null || B === void 0 ? void 0 : B.listChanged)) throw new Error(`Client does not support roots list changed notifications (required for ${A})`);
        break;
      case "notifications/initialized":
        break;
      case "notifications/cancelled":
        break;
      case "notifications/progress":
        break;
    }
  }
  assertRequestHandlerCapability(A) {
    switch (A) {
      case "sampling/createMessage":
        if (!this._capabilities.sampling) throw new Error(`Client does not support sampling capability (required for ${A})`);
        break;
      case "elicitation/create":
        if (!this._capabilities.elicitation) throw new Error(`Client does not support elicitation capability (required for ${A})`);
        break;
      case "roots/list":
        if (!this._capabilities.roots) throw new Error(`Client does not support roots capability (required for ${A})`);
        break;
      case "ping":
        break;
    }
  }
  async ping(A) {
    return this.request({
      method: "ping"
    }, mP, A);
  }
  async complete(A, B) {
    return this.request({
      method: "completion/complete",
      params: A
    }, MK0, B);
  }
  async setLoggingLevel(A, B) {
    return this.request({
      method: "logging/setLevel",
      params: {
        level: A
      }
    }, mP, B);
  }
  async getPrompt(A, B) {
    return this.request({
      method: "prompts/get",
      params: A
    }, wK0, B);
  }
  async listPrompts(A, B) {
    return this.request({
      method: "prompts/list",
      params: A
    }, MD1, B);
  }
  async listResources(A, B) {
    return this.request({
      method: "resources/list",
      params: A
    }, Wm, B);
  }
  async listResourceTemplates(A, B) {
    return this.request({
      method: "resources/templates/list",
      params: A
    }, HK0, B);
  }
  async readResource(A, B) {
    return this.request({
      method: "resources/read",
      params: A
    }, LD1, B);
  }
  async subscribeResource(A, B) {
    return this.request({
      method: "resources/subscribe",
      params: A
    }, mP, B);
  }
  async unsubscribeResource(A, B) {
    return this.request({
      method: "resources/unsubscribe",
      params: A
    }, mP, B);
  }
  async callTool(A, B = Ze, Q) {
    let D = await this.request({
        method: "tools/call",
        params: A
      }, B, Q),
      Z = this.getToolOutputValidator(A.name);
    if (Z) {
      if (!D.structuredContent && !D.isError) throw new XX(JX.InvalidRequest, `Tool ${A.name} has an output schema but did not return structured content`);
      if (D.structuredContent) try {
        if (!Z(D.structuredContent)) throw new XX(JX.InvalidParams, `Structured content does not match the tool's output schema: ${this._ajv.errorsText(Z.errors)}`);
      } catch (G) {
        if (G instanceof XX) throw G;
        throw new XX(JX.InvalidParams, `Failed to validate structured content: ${G instanceof Error ? G.message : String(G)}`);
      }
    }
    return D;
  }
  cacheToolOutputSchemas(A) {
    this._cachedToolOutputValidators.clear();
    for (let B of A) if (B.outputSchema) try {
      let Q = this._ajv.compile(B.outputSchema);
      this._cachedToolOutputValidators.set(B.name, Q);
    } catch (Q) {}
  }
  getToolOutputValidator(A) {
    return this._cachedToolOutputValidators.get(A);
  }
  async listTools(A, B) {
    let Q = await this.request({
      method: "tools/list",
      params: A
    }, RD1, B);
    return this.cacheToolOutputSchemas(Q.tools), Q;
  }
  async sendRootsListChanged() {
    return this.notification({
      method: "notifications/roots/list_changed"
    });
  }
}
module.exports = {
  $D1,
  $K0,
  $j6,
  Ay1,
  By1,
  CK0,
  Cj6,
  Dy1,
  EK0,
  Ej6,
  Gy1,
  HK0,
  Hj6,
  Ij6,
  JX,
  Jj6,
  KK0,
  Kj6,
  LD1,
  LK0,
  Lj6,
  MD1,
  MK0,
  Mj6,
  ND1,
  NK0,
  Nj6,
  OD1,
  OK0,
  Oj6,
  Pj6,
  Px,
  Qy1,
  RD1,
  RK0,
  RM,
  Rj6,
  Sj6,
  Tj6,
  UK0,
  Uj6,
  Vj6,
  Wj6,
  Wm,
  XX,
  Xj6,
  Yj6,
  Ze,
  Zy1,
  _j6,
  a8B,
  aV,
  bj6,
  c8B,
  cj6,
  dj6,
  e8B,
  ej1,
  fj6,
  g8B,
  gj6,
  h8B,
  hj6,
  i8B,
  jj6,
  kE,
  kj6,
  l8B,
  lj6,
  m8B,
  mP,
  mj6,
  o8B,
  oK0,
  oj1,
  pj6,
  qD1,
  qK0,
  qj6,
  r8B,
  rj1,
  s8B,
  sj1,
  t8B,
  tK0,
  tj1,
  u8B,
  uj6,
  vj6,
  wK0,
  wj6,
  xj6,
  yE,
  yj6,
  zK0,
  zj6
};