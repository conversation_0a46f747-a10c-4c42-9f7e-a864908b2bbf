/**
 * Isolated bundle for 'g'
 */

var g = {};
Mj(g, {
  void: () => m8Q,
  util: () => W6,
  unknown: () => g8Q,
  union: () => VK1,
  undefined: () => b8Q,
  tuple: () => p8Q,
  transformer: () => e8Q,
  symbol: () => v8Q,
  string: () => WQ,
  strictObject: () => d8Q,
  setErrorMap: () => F8Q,
  set: () => n8Q,
  record: () => Pi,
  quotelessJson: () => Z8Q,
  promise: () => t8Q,
  preprocess: () => Q5Q,
  pipeline: () => D5Q,
  ostring: () => Z5Q,
  optional: () => A5Q,
  onumber: () => G5Q,
  oboolean: () => F5Q,
  objectUtil: () => Yr1,
  object: () => aC,
  number: () => Ih,
  nullable: () => B5Q,
  null: () => f8Q,
  never: () => u8Q,
  nativeEnum: () => o8Q,
  nan: () => k8Q,
  map: () => i8Q,
  makeIssue: () => z91,
  literal: () => r8Q,
  lazy: () => s8Q,
  late: () => j8Q,
  isValid: () => Sy,
  isDirty: () => WK1,
  isAsync: () => Ki,
  isAborted: () => YK1,
  intersection: () => l8Q,
  instanceof: () => y8Q,
  getParsedType: () => PN,
  getErrorMap: () => Ci,
  function: () => a8Q,
  enum: () => Si,
  effect: () => e8Q,
  discriminatedUnion: () => c8Q,
  defaultErrorMap: () => yO,
  datetimeRegex: () => $IA,
  date: () => x8Q,
  custom: () => NIA,
  coerce: () => I5Q,
  boolean: () => yN,
  bigint: () => _8Q,
  array: () => qw,
  any: () => h8Q,
  addIssueToContext: () => g2,
  ZodVoid: () => U91,
  ZodUnknown: () => jy,
  ZodUnion: () => $i,
  ZodUndefined: () => Ui,
  ZodType: () => h4,
  ZodTuple: () => jN,
  ZodTransformer: () => $w,
  ZodSymbol: () => E91,
  ZodString: () => Ew,
  ZodSet: () => Gh,
  ZodSchema: () => h4,
  ZodRecord: () => w91,
  ZodReadonly: () => Ti,
  ZodPromise: () => Fh,
  ZodPipeline: () => N91,
  ZodParsedType: () => z2,
  ZodOptional: () => nC,
  ZodObject: () => mD,
  ZodNumber: () => yy,
  ZodNullable: () => _O,
  ZodNull: () => wi,
  ZodNever: () => SN,
  ZodNativeEnum: () => Mi,
  ZodNaN: () => q91,
  ZodMap: () => $91,
  ZodLiteral: () => Li,
  ZodLazy: () => Ni,
  ZodIssueCode: () => sA,
  ZodIntersection: () => qi,
  ZodFunction: () => zi,
  ZodFirstPartyTypeKind: () => eA,
  ZodError: () => CV,
  ZodEnum: () => _y,
  ZodEffects: () => $w,
  ZodDiscriminatedUnion: () => JK1,
  ZodDefault: () => Ri,
  ZodDate: () => Dh,
  ZodCatch: () => Oi,
  ZodBranded: () => XK1,
  ZodBoolean: () => Ei,
  ZodBigInt: () => ky,
  ZodArray: () => Uw,
  ZodAny: () => Zh,
  Schema: () => h4,
  ParseStatus: () => qY,
  OK: () => gW,
  NEVER: () => Y5Q,
  INVALID: () => e9,
  EMPTY_PATH: () => I8Q,
  DIRTY: () => Qh,
  BRAND: () => S8Q
});
var W6;
var Yr1;
var z2 = W6.arrayToEnum(["string", "nan", "number", "integer", "float", "boolean", "date", "bigint", "symbol", "function", "undefined", "null", "array", "object", "unknown", "promise", "void", "never", "map", "set"]),
  PN = A => {
    switch (typeof A) {
      case "undefined":
        return z2.undefined;
      case "string":
        return z2.string;
      case "number":
        return Number.isNaN(A) ? z2.nan : z2.number;
      case "boolean":
        return z2.boolean;
      case "function":
        return z2.function;
      case "bigint":
        return z2.bigint;
      case "symbol":
        return z2.symbol;
      case "object":
        if (Array.isArray(A)) return z2.array;
        if (A === null) return z2.null;
        if (A.then && typeof A.then === "function" && A.catch && typeof A.catch === "function") return z2.promise;
        if (typeof Map !== "undefined" && A instanceof Map) return z2.map;
        if (typeof Set !== "undefined" && A instanceof Set) return z2.set;
        if (typeof Date !== "undefined" && A instanceof Date) return z2.date;
        return z2.object;
      default:
        return z2.unknown;
    }
  };
var sA = W6.arrayToEnum(["invalid_type", "invalid_literal", "custom", "invalid_union", "invalid_union_discriminator", "invalid_enum_value", "unrecognized_keys", "invalid_arguments", "invalid_return_type", "invalid_date", "invalid_string", "too_small", "too_big", "invalid_intersection_types", "not_multiple_of", "not_finite"]),
  Z8Q = A => {
    return JSON.stringify(A, null, 2).replace(/"([^"]+)":/g, "$1:");
  };
class CV extends Error {
  get errors() {
    return this.issues;
  }
  constructor(A) {
    super();
    this.issues = [], this.addIssue = Q => {
      this.issues = [...this.issues, Q];
    }, this.addIssues = (Q = []) => {
      this.issues = [...this.issues, ...Q];
    };
    let B = new.target.prototype;
    if (Object.setPrototypeOf) Object.setPrototypeOf(this, B);else this.__proto__ = B;
    this.name = "ZodError", this.issues = A;
  }
  format(A) {
    let B = A || function (Z) {
        return Z.message;
      },
      Q = {
        _errors: []
      },
      D = Z => {
        for (let G of Z.issues) if (G.code === "invalid_union") G.unionErrors.map(D);else if (G.code === "invalid_return_type") D(G.returnTypeError);else if (G.code === "invalid_arguments") D(G.argumentsError);else if (G.path.length === 0) Q._errors.push(B(G));else {
          let F = Q,
            I = 0;
          while (I < G.path.length) {
            let Y = G.path[I];
            if (I !== G.path.length - 1) F[Y] = F[Y] || {
              _errors: []
            };else F[Y] = F[Y] || {
              _errors: []
            }, F[Y]._errors.push(B(G));
            F = F[Y], I++;
          }
        }
      };
    return D(this), Q;
  }
  static assert(A) {
    if (!(A instanceof CV)) throw new Error(`Not a ZodError: ${A}`);
  }
  toString() {
    return this.message;
  }
  get message() {
    return JSON.stringify(this.issues, W6.jsonStringifyReplacer, 2);
  }
  get isEmpty() {
    return this.issues.length === 0;
  }
  flatten(A = B => B.message) {
    let B = {},
      Q = [];
    for (let D of this.issues) if (D.path.length > 0) {
      let Z = D.path[0];
      B[Z] = B[Z] || [], B[Z].push(A(D));
    } else Q.push(A(D));
    return {
      formErrors: Q,
      fieldErrors: B
    };
  }
  get formErrors() {
    return this.flatten();
  }
}
CV.create = A => {
  return new CV(A);
};
var G8Q = (A, B) => {
    let Q;
    switch (A.code) {
      case sA.invalid_type:
        if (A.received === z2.undefined) Q = "Required";else Q = `Expected ${A.expected}, received ${A.received}`;
        break;
      case sA.invalid_literal:
        Q = `Invalid literal value, expected ${JSON.stringify(A.expected, W6.jsonStringifyReplacer)}`;
        break;
      case sA.unrecognized_keys:
        Q = `Unrecognized key(s) in object: ${W6.joinValues(A.keys, ", ")}`;
        break;
      case sA.invalid_union:
        Q = "Invalid input";
        break;
      case sA.invalid_union_discriminator:
        Q = `Invalid discriminator value. Expected ${W6.joinValues(A.options)}`;
        break;
      case sA.invalid_enum_value:
        Q = `Invalid enum value. Expected ${W6.joinValues(A.options)}, received '${A.received}'`;
        break;
      case sA.invalid_arguments:
        Q = "Invalid function arguments";
        break;
      case sA.invalid_return_type:
        Q = "Invalid function return type";
        break;
      case sA.invalid_date:
        Q = "Invalid date";
        break;
      case sA.invalid_string:
        if (typeof A.validation === "object") {
          if ("includes" in A.validation) {
            if (Q = `Invalid input: must include "${A.validation.includes}"`, typeof A.validation.position === "number") Q = `${Q} at one or more positions greater than or equal to ${A.validation.position}`;
          } else if ("startsWith" in A.validation) Q = `Invalid input: must start with "${A.validation.startsWith}"`;else if ("endsWith" in A.validation) Q = `Invalid input: must end with "${A.validation.endsWith}"`;else W6.assertNever(A.validation);
        } else if (A.validation !== "regex") Q = `Invalid ${A.validation}`;else Q = "Invalid";
        break;
      case sA.too_small:
        if (A.type === "array") Q = `Array must contain ${A.exact ? "exactly" : A.inclusive ? "at least" : "more than"} ${A.minimum} element(s)`;else if (A.type === "string") Q = `String must contain ${A.exact ? "exactly" : A.inclusive ? "at least" : "over"} ${A.minimum} character(s)`;else if (A.type === "number") Q = `Number must be ${A.exact ? "exactly equal to " : A.inclusive ? "greater than or equal to " : "greater than "}${A.minimum}`;else if (A.type === "bigint") Q = `Number must be ${A.exact ? "exactly equal to " : A.inclusive ? "greater than or equal to " : "greater than "}${A.minimum}`;else if (A.type === "date") Q = `Date must be ${A.exact ? "exactly equal to " : A.inclusive ? "greater than or equal to " : "greater than "}${new Date(Number(A.minimum))}`;else Q = "Invalid input";
        break;
      case sA.too_big:
        if (A.type === "array") Q = `Array must contain ${A.exact ? "exactly" : A.inclusive ? "at most" : "less than"} ${A.maximum} element(s)`;else if (A.type === "string") Q = `String must contain ${A.exact ? "exactly" : A.inclusive ? "at most" : "under"} ${A.maximum} character(s)`;else if (A.type === "number") Q = `Number must be ${A.exact ? "exactly" : A.inclusive ? "less than or equal to" : "less than"} ${A.maximum}`;else if (A.type === "bigint") Q = `BigInt must be ${A.exact ? "exactly" : A.inclusive ? "less than or equal to" : "less than"} ${A.maximum}`;else if (A.type === "date") Q = `Date must be ${A.exact ? "exactly" : A.inclusive ? "smaller than or equal to" : "smaller than"} ${new Date(Number(A.maximum))}`;else Q = "Invalid input";
        break;
      case sA.custom:
        Q = "Invalid input";
        break;
      case sA.invalid_intersection_types:
        Q = "Intersection results could not be merged";
        break;
      case sA.not_multiple_of:
        Q = `Number must be a multiple of ${A.multipleOf}`;
        break;
      case sA.not_finite:
        Q = "Number must be finite";
        break;
      default:
        Q = B.defaultError, W6.assertNever(A);
    }
    return {
      message: Q
    };
  },
  yO = G8Q;
var HIA = yO;
function F8Q(A) {
  HIA = A;
}
function Ci() {
  return HIA;
}
var z91 = A => {
    let {
        data: B,
        path: Q,
        errorMaps: D,
        issueData: Z
      } = A,
      G = [...Q, ...(Z.path || [])],
      F = {
        ...Z,
        path: G
      };
    if (Z.message !== void 0) return {
      ...Z,
      path: G,
      message: Z.message
    };
    let I = "",
      Y = D.filter(W => !!W).slice().reverse();
    for (let W of Y) I = W(F, {
      data: B,
      defaultError: I
    }).message;
    return {
      ...Z,
      path: G,
      message: I
    };
  },
  I8Q = [];
function g2(A, B) {
  let Q = Ci(),
    D = z91({
      issueData: B,
      data: A.data,
      path: A.path,
      errorMaps: [A.common.contextualErrorMap, A.schemaErrorMap, Q, Q === yO ? void 0 : yO].filter(Z => !!Z)
    });
  A.common.issues.push(D);
}
class qY {
  constructor() {
    this.value = "valid";
  }
  dirty() {
    if (this.value === "valid") this.value = "dirty";
  }
  abort() {
    if (this.value !== "aborted") this.value = "aborted";
  }
  static mergeArray(A, B) {
    let Q = [];
    for (let D of B) {
      if (D.status === "aborted") return e9;
      if (D.status === "dirty") A.dirty();
      Q.push(D.value);
    }
    return {
      status: A.value,
      value: Q
    };
  }
  static async mergeObjectAsync(A, B) {
    let Q = [];
    for (let D of B) {
      let Z = await D.key,
        G = await D.value;
      Q.push({
        key: Z,
        value: G
      });
    }
    return qY.mergeObjectSync(A, Q);
  }
  static mergeObjectSync(A, B) {
    let Q = {};
    for (let D of B) {
      let {
        key: Z,
        value: G
      } = D;
      if (Z.status === "aborted") return e9;
      if (G.status === "aborted") return e9;
      if (Z.status === "dirty") A.dirty();
      if (G.status === "dirty") A.dirty();
      if (Z.value !== "__proto__" && (typeof G.value !== "undefined" || D.alwaysSet)) Q[Z.value] = G.value;
    }
    return {
      status: A.value,
      value: Q
    };
  }
}
var e9 = Object.freeze({
    status: "aborted"
  }),
  Qh = A => ({
    status: "dirty",
    value: A
  }),
  gW = A => ({
    status: "valid",
    value: A
  }),
  YK1 = A => A.status === "aborted",
  WK1 = A => A.status === "dirty",
  Sy = A => A.status === "valid",
  Ki = A => typeof Promise !== "undefined" && A instanceof Promise;
var V9;
class ww {
  constructor(A, B, Q, D) {
    this._cachedPath = [], this.parent = A, this.data = B, this._path = Q, this._key = D;
  }
  get path() {
    if (!this._cachedPath.length) if (Array.isArray(this._key)) this._cachedPath.push(...this._path, ...this._key);else this._cachedPath.push(...this._path, this._key);
    return this._cachedPath;
  }
}
var zIA = (A, B) => {
  if (Sy(B)) return {
    success: !0,
    data: B.value
  };else {
    if (!A.common.issues.length) throw new Error("Validation failed but no issues detected.");
    return {
      success: !1,
      get error() {
        if (this._error) return this._error;
        let Q = new CV(A.common.issues);
        return this._error = Q, this._error;
      }
    };
  }
};
function Y4(A) {
  if (!A) return {};
  let {
    errorMap: B,
    invalid_type_error: Q,
    required_error: D,
    description: Z
  } = A;
  if (B && (Q || D)) throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);
  if (B) return {
    errorMap: B,
    description: Z
  };
  return {
    errorMap: (F, I) => {
      let {
        message: Y
      } = A;
      if (F.code === "invalid_enum_value") return {
        message: Y ?? I.defaultError
      };
      if (typeof I.data === "undefined") return {
        message: Y ?? D ?? I.defaultError
      };
      if (F.code !== "invalid_type") return {
        message: I.defaultError
      };
      return {
        message: Y ?? Q ?? I.defaultError
      };
    },
    description: Z
  };
}
class h4 {
  get description() {
    return this._def.description;
  }
  _getType(A) {
    return PN(A.data);
  }
  _getOrReturnCtx(A, B) {
    return B || {
      common: A.parent.common,
      data: A.data,
      parsedType: PN(A.data),
      schemaErrorMap: this._def.errorMap,
      path: A.path,
      parent: A.parent
    };
  }
  _processInputParams(A) {
    return {
      status: new qY(),
      ctx: {
        common: A.parent.common,
        data: A.data,
        parsedType: PN(A.data),
        schemaErrorMap: this._def.errorMap,
        path: A.path,
        parent: A.parent
      }
    };
  }
  _parseSync(A) {
    let B = this._parse(A);
    if (Ki(B)) throw new Error("Synchronous parse encountered promise.");
    return B;
  }
  _parseAsync(A) {
    let B = this._parse(A);
    return Promise.resolve(B);
  }
  parse(A, B) {
    let Q = this.safeParse(A, B);
    if (Q.success) return Q.data;
    throw Q.error;
  }
  safeParse(A, B) {
    let Q = {
        common: {
          issues: [],
          async: B?.async ?? !1,
          contextualErrorMap: B?.errorMap
        },
        path: B?.path || [],
        schemaErrorMap: this._def.errorMap,
        parent: null,
        data: A,
        parsedType: PN(A)
      },
      D = this._parseSync({
        data: A,
        path: Q.path,
        parent: Q
      });
    return zIA(Q, D);
  }
  "~validate"(A) {
    let B = {
      common: {
        issues: [],
        async: !!this["~standard"].async
      },
      path: [],
      schemaErrorMap: this._def.errorMap,
      parent: null,
      data: A,
      parsedType: PN(A)
    };
    if (!this["~standard"].async) try {
      let Q = this._parseSync({
        data: A,
        path: [],
        parent: B
      });
      return Sy(Q) ? {
        value: Q.value
      } : {
        issues: B.common.issues
      };
    } catch (Q) {
      if (Q?.message?.toLowerCase()?.includes("encountered")) this["~standard"].async = !0;
      B.common = {
        issues: [],
        async: !0
      };
    }
    return this._parseAsync({
      data: A,
      path: [],
      parent: B
    }).then(Q => Sy(Q) ? {
      value: Q.value
    } : {
      issues: B.common.issues
    });
  }
  async parseAsync(A, B) {
    let Q = await this.safeParseAsync(A, B);
    if (Q.success) return Q.data;
    throw Q.error;
  }
  async safeParseAsync(A, B) {
    let Q = {
        common: {
          issues: [],
          contextualErrorMap: B?.errorMap,
          async: !0
        },
        path: B?.path || [],
        schemaErrorMap: this._def.errorMap,
        parent: null,
        data: A,
        parsedType: PN(A)
      },
      D = this._parse({
        data: A,
        path: Q.path,
        parent: Q
      }),
      Z = await (Ki(D) ? D : Promise.resolve(D));
    return zIA(Q, Z);
  }
  refine(A, B) {
    let Q = D => {
      if (typeof B === "string" || typeof B === "undefined") return {
        message: B
      };else if (typeof B === "function") return B(D);else return B;
    };
    return this._refinement((D, Z) => {
      let G = A(D),
        F = () => Z.addIssue({
          code: sA.custom,
          ...Q(D)
        });
      if (typeof Promise !== "undefined" && G instanceof Promise) return G.then(I => {
        if (!I) return F(), !1;else return !0;
      });
      if (!G) return F(), !1;else return !0;
    });
  }
  refinement(A, B) {
    return this._refinement((Q, D) => {
      if (!A(Q)) return D.addIssue(typeof B === "function" ? B(Q, D) : B), !1;else return !0;
    });
  }
  _refinement(A) {
    return new $w({
      schema: this,
      typeName: eA.ZodEffects,
      effect: {
        type: "refinement",
        refinement: A
      }
    });
  }
  superRefine(A) {
    return this._refinement(A);
  }
  constructor(A) {
    this.spa = this.safeParseAsync, this._def = A, this.parse = this.parse.bind(this), this.safeParse = this.safeParse.bind(this), this.parseAsync = this.parseAsync.bind(this), this.safeParseAsync = this.safeParseAsync.bind(this), this.spa = this.spa.bind(this), this.refine = this.refine.bind(this), this.refinement = this.refinement.bind(this), this.superRefine = this.superRefine.bind(this), this.optional = this.optional.bind(this), this.nullable = this.nullable.bind(this), this.nullish = this.nullish.bind(this), this.array = this.array.bind(this), this.promise = this.promise.bind(this), this.or = this.or.bind(this), this.and = this.and.bind(this), this.transform = this.transform.bind(this), this.brand = this.brand.bind(this), this.default = this.default.bind(this), this.catch = this.catch.bind(this), this.describe = this.describe.bind(this), this.pipe = this.pipe.bind(this), this.readonly = this.readonly.bind(this), this.isNullable = this.isNullable.bind(this), this.isOptional = this.isOptional.bind(this), this["~standard"] = {
      version: 1,
      vendor: "zod",
      validate: B => this["~validate"](B)
    };
  }
  optional() {
    return nC.create(this, this._def);
  }
  nullable() {
    return _O.create(this, this._def);
  }
  nullish() {
    return this.nullable().optional();
  }
  array() {
    return Uw.create(this);
  }
  promise() {
    return Fh.create(this, this._def);
  }
  or(A) {
    return $i.create([this, A], this._def);
  }
  and(A) {
    return qi.create(this, A, this._def);
  }
  transform(A) {
    return new $w({
      ...Y4(this._def),
      schema: this,
      typeName: eA.ZodEffects,
      effect: {
        type: "transform",
        transform: A
      }
    });
  }
  default(A) {
    let B = typeof A === "function" ? A : () => A;
    return new Ri({
      ...Y4(this._def),
      innerType: this,
      defaultValue: B,
      typeName: eA.ZodDefault
    });
  }
  brand() {
    return new XK1({
      typeName: eA.ZodBranded,
      type: this,
      ...Y4(this._def)
    });
  }
  catch(A) {
    let B = typeof A === "function" ? A : () => A;
    return new Oi({
      ...Y4(this._def),
      innerType: this,
      catchValue: B,
      typeName: eA.ZodCatch
    });
  }
  describe(A) {
    return new this.constructor({
      ...this._def,
      description: A
    });
  }
  pipe(A) {
    return N91.create(this, A);
  }
  readonly() {
    return Ti.create(this);
  }
  isOptional() {
    return this.safeParse(void 0).success;
  }
  isNullable() {
    return this.safeParse(null).success;
  }
}
var Y8Q = /^c[^\s-]{8,}$/i,
  W8Q = /^[0-9a-z]+$/,
  J8Q = /^[0-9A-HJKMNP-TV-Z]{26}$/i,
  X8Q = /^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,
  V8Q = /^[a-z0-9_-]{21}$/i,
  C8Q = /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,
  K8Q = /^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,
  H8Q = /^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,
  z8Q = "^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$",
  Wr1,
  E8Q = /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,
  U8Q = /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,
  w8Q = /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,
  $8Q = /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,
  q8Q = /^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,
  N8Q = /^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,
  UIA = "((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",
  L8Q = new RegExp(`^${UIA}$`);
function wIA(A) {
  let B = "[0-5]\\d";
  if (A.precision) B = `${B}\\.\\d{${A.precision}}`;else if (A.precision == null) B = `${B}(\\.\\d+)?`;
  let Q = A.precision ? "+" : "?";
  return `([01]\\d|2[0-3]):[0-5]\\d(:${B})${Q}`;
}
function M8Q(A) {
  return new RegExp(`^${wIA(A)}$`);
}
function $IA(A) {
  let B = `${UIA}T${wIA(A)}`,
    Q = [];
  if (Q.push(A.local ? "Z?" : "Z"), A.offset) Q.push("([+-]\\d{2}:?\\d{2})");
  return B = `${B}(${Q.join("|")})`, new RegExp(`^${B}$`);
}
function R8Q(A, B) {
  if ((B === "v4" || !B) && E8Q.test(A)) return !0;
  if ((B === "v6" || !B) && w8Q.test(A)) return !0;
  return !1;
}
function O8Q(A, B) {
  if (!C8Q.test(A)) return !1;
  try {
    let [Q] = A.split(".");
    if (!Q) return !1;
    let D = Q.replace(/-/g, "+").replace(/_/g, "/").padEnd(Q.length + (4 - Q.length % 4) % 4, "="),
      Z = JSON.parse(atob(D));
    if (typeof Z !== "object" || Z === null) return !1;
    if ("typ" in Z && Z?.typ !== "JWT") return !1;
    if (!Z.alg) return !1;
    if (B && Z.alg !== B) return !1;
    return !0;
  } catch {
    return !1;
  }
}
function T8Q(A, B) {
  if ((B === "v4" || !B) && U8Q.test(A)) return !0;
  if ((B === "v6" || !B) && $8Q.test(A)) return !0;
  return !1;
}
class Ew extends h4 {
  _parse(A) {
    if (this._def.coerce) A.data = String(A.data);
    if (this._getType(A) !== z2.string) {
      let Z = this._getOrReturnCtx(A);
      return g2(Z, {
        code: sA.invalid_type,
        expected: z2.string,
        received: Z.parsedType
      }), e9;
    }
    let Q = new qY(),
      D = void 0;
    for (let Z of this._def.checks) if (Z.kind === "min") {
      if (A.data.length < Z.value) D = this._getOrReturnCtx(A, D), g2(D, {
        code: sA.too_small,
        minimum: Z.value,
        type: "string",
        inclusive: !0,
        exact: !1,
        message: Z.message
      }), Q.dirty();
    } else if (Z.kind === "max") {
      if (A.data.length > Z.value) D = this._getOrReturnCtx(A, D), g2(D, {
        code: sA.too_big,
        maximum: Z.value,
        type: "string",
        inclusive: !0,
        exact: !1,
        message: Z.message
      }), Q.dirty();
    } else if (Z.kind === "length") {
      let G = A.data.length > Z.value,
        F = A.data.length < Z.value;
      if (G || F) {
        if (D = this._getOrReturnCtx(A, D), G) g2(D, {
          code: sA.too_big,
          maximum: Z.value,
          type: "string",
          inclusive: !0,
          exact: !0,
          message: Z.message
        });else if (F) g2(D, {
          code: sA.too_small,
          minimum: Z.value,
          type: "string",
          inclusive: !0,
          exact: !0,
          message: Z.message
        });
        Q.dirty();
      }
    } else if (Z.kind === "email") {
      if (!H8Q.test(A.data)) D = this._getOrReturnCtx(A, D), g2(D, {
        validation: "email",
        code: sA.invalid_string,
        message: Z.message
      }), Q.dirty();
    } else if (Z.kind === "emoji") {
      if (!Wr1) Wr1 = new RegExp(z8Q, "u");
      if (!Wr1.test(A.data)) D = this._getOrReturnCtx(A, D), g2(D, {
        validation: "emoji",
        code: sA.invalid_string,
        message: Z.message
      }), Q.dirty();
    } else if (Z.kind === "uuid") {
      if (!X8Q.test(A.data)) D = this._getOrReturnCtx(A, D), g2(D, {
        validation: "uuid",
        code: sA.invalid_string,
        message: Z.message
      }), Q.dirty();
    } else if (Z.kind === "nanoid") {
      if (!V8Q.test(A.data)) D = this._getOrReturnCtx(A, D), g2(D, {
        validation: "nanoid",
        code: sA.invalid_string,
        message: Z.message
      }), Q.dirty();
    } else if (Z.kind === "cuid") {
      if (!Y8Q.test(A.data)) D = this._getOrReturnCtx(A, D), g2(D, {
        validation: "cuid",
        code: sA.invalid_string,
        message: Z.message
      }), Q.dirty();
    } else if (Z.kind === "cuid2") {
      if (!W8Q.test(A.data)) D = this._getOrReturnCtx(A, D), g2(D, {
        validation: "cuid2",
        code: sA.invalid_string,
        message: Z.message
      }), Q.dirty();
    } else if (Z.kind === "ulid") {
      if (!J8Q.test(A.data)) D = this._getOrReturnCtx(A, D), g2(D, {
        validation: "ulid",
        code: sA.invalid_string,
        message: Z.message
      }), Q.dirty();
    } else if (Z.kind === "url") try {
      new URL(A.data);
    } catch {
      D = this._getOrReturnCtx(A, D), g2(D, {
        validation: "url",
        code: sA.invalid_string,
        message: Z.message
      }), Q.dirty();
    } else if (Z.kind === "regex") {
      if (Z.regex.lastIndex = 0, !Z.regex.test(A.data)) D = this._getOrReturnCtx(A, D), g2(D, {
        validation: "regex",
        code: sA.invalid_string,
        message: Z.message
      }), Q.dirty();
    } else if (Z.kind === "trim") A.data = A.data.trim();else if (Z.kind === "includes") {
      if (!A.data.includes(Z.value, Z.position)) D = this._getOrReturnCtx(A, D), g2(D, {
        code: sA.invalid_string,
        validation: {
          includes: Z.value,
          position: Z.position
        },
        message: Z.message
      }), Q.dirty();
    } else if (Z.kind === "toLowerCase") A.data = A.data.toLowerCase();else if (Z.kind === "toUpperCase") A.data = A.data.toUpperCase();else if (Z.kind === "startsWith") {
      if (!A.data.startsWith(Z.value)) D = this._getOrReturnCtx(A, D), g2(D, {
        code: sA.invalid_string,
        validation: {
          startsWith: Z.value
        },
        message: Z.message
      }), Q.dirty();
    } else if (Z.kind === "endsWith") {
      if (!A.data.endsWith(Z.value)) D = this._getOrReturnCtx(A, D), g2(D, {
        code: sA.invalid_string,
        validation: {
          endsWith: Z.value
        },
        message: Z.message
      }), Q.dirty();
    } else if (Z.kind === "datetime") {
      if (!$IA(Z).test(A.data)) D = this._getOrReturnCtx(A, D), g2(D, {
        code: sA.invalid_string,
        validation: "datetime",
        message: Z.message
      }), Q.dirty();
    } else if (Z.kind === "date") {
      if (!L8Q.test(A.data)) D = this._getOrReturnCtx(A, D), g2(D, {
        code: sA.invalid_string,
        validation: "date",
        message: Z.message
      }), Q.dirty();
    } else if (Z.kind === "time") {
      if (!M8Q(Z).test(A.data)) D = this._getOrReturnCtx(A, D), g2(D, {
        code: sA.invalid_string,
        validation: "time",
        message: Z.message
      }), Q.dirty();
    } else if (Z.kind === "duration") {
      if (!K8Q.test(A.data)) D = this._getOrReturnCtx(A, D), g2(D, {
        validation: "duration",
        code: sA.invalid_string,
        message: Z.message
      }), Q.dirty();
    } else if (Z.kind === "ip") {
      if (!R8Q(A.data, Z.version)) D = this._getOrReturnCtx(A, D), g2(D, {
        validation: "ip",
        code: sA.invalid_string,
        message: Z.message
      }), Q.dirty();
    } else if (Z.kind === "jwt") {
      if (!O8Q(A.data, Z.alg)) D = this._getOrReturnCtx(A, D), g2(D, {
        validation: "jwt",
        code: sA.invalid_string,
        message: Z.message
      }), Q.dirty();
    } else if (Z.kind === "cidr") {
      if (!T8Q(A.data, Z.version)) D = this._getOrReturnCtx(A, D), g2(D, {
        validation: "cidr",
        code: sA.invalid_string,
        message: Z.message
      }), Q.dirty();
    } else if (Z.kind === "base64") {
      if (!q8Q.test(A.data)) D = this._getOrReturnCtx(A, D), g2(D, {
        validation: "base64",
        code: sA.invalid_string,
        message: Z.message
      }), Q.dirty();
    } else if (Z.kind === "base64url") {
      if (!N8Q.test(A.data)) D = this._getOrReturnCtx(A, D), g2(D, {
        validation: "base64url",
        code: sA.invalid_string,
        message: Z.message
      }), Q.dirty();
    } else W6.assertNever(Z);
    return {
      status: Q.value,
      value: A.data
    };
  }
  _regex(A, B, Q) {
    return this.refinement(D => A.test(D), {
      validation: B,
      code: sA.invalid_string,
      ...V9.errToObj(Q)
    });
  }
  _addCheck(A) {
    return new Ew({
      ...this._def,
      checks: [...this._def.checks, A]
    });
  }
  email(A) {
    return this._addCheck({
      kind: "email",
      ...V9.errToObj(A)
    });
  }
  url(A) {
    return this._addCheck({
      kind: "url",
      ...V9.errToObj(A)
    });
  }
  emoji(A) {
    return this._addCheck({
      kind: "emoji",
      ...V9.errToObj(A)
    });
  }
  uuid(A) {
    return this._addCheck({
      kind: "uuid",
      ...V9.errToObj(A)
    });
  }
  nanoid(A) {
    return this._addCheck({
      kind: "nanoid",
      ...V9.errToObj(A)
    });
  }
  cuid(A) {
    return this._addCheck({
      kind: "cuid",
      ...V9.errToObj(A)
    });
  }
  cuid2(A) {
    return this._addCheck({
      kind: "cuid2",
      ...V9.errToObj(A)
    });
  }
  ulid(A) {
    return this._addCheck({
      kind: "ulid",
      ...V9.errToObj(A)
    });
  }
  base64(A) {
    return this._addCheck({
      kind: "base64",
      ...V9.errToObj(A)
    });
  }
  base64url(A) {
    return this._addCheck({
      kind: "base64url",
      ...V9.errToObj(A)
    });
  }
  jwt(A) {
    return this._addCheck({
      kind: "jwt",
      ...V9.errToObj(A)
    });
  }
  ip(A) {
    return this._addCheck({
      kind: "ip",
      ...V9.errToObj(A)
    });
  }
  cidr(A) {
    return this._addCheck({
      kind: "cidr",
      ...V9.errToObj(A)
    });
  }
  datetime(A) {
    if (typeof A === "string") return this._addCheck({
      kind: "datetime",
      precision: null,
      offset: !1,
      local: !1,
      message: A
    });
    return this._addCheck({
      kind: "datetime",
      precision: typeof A?.precision === "undefined" ? null : A?.precision,
      offset: A?.offset ?? !1,
      local: A?.local ?? !1,
      ...V9.errToObj(A?.message)
    });
  }
  date(A) {
    return this._addCheck({
      kind: "date",
      message: A
    });
  }
  time(A) {
    if (typeof A === "string") return this._addCheck({
      kind: "time",
      precision: null,
      message: A
    });
    return this._addCheck({
      kind: "time",
      precision: typeof A?.precision === "undefined" ? null : A?.precision,
      ...V9.errToObj(A?.message)
    });
  }
  duration(A) {
    return this._addCheck({
      kind: "duration",
      ...V9.errToObj(A)
    });
  }
  regex(A, B) {
    return this._addCheck({
      kind: "regex",
      regex: A,
      ...V9.errToObj(B)
    });
  }
  includes(A, B) {
    return this._addCheck({
      kind: "includes",
      value: A,
      position: B?.position,
      ...V9.errToObj(B?.message)
    });
  }
  startsWith(A, B) {
    return this._addCheck({
      kind: "startsWith",
      value: A,
      ...V9.errToObj(B)
    });
  }
  endsWith(A, B) {
    return this._addCheck({
      kind: "endsWith",
      value: A,
      ...V9.errToObj(B)
    });
  }
  min(A, B) {
    return this._addCheck({
      kind: "min",
      value: A,
      ...V9.errToObj(B)
    });
  }
  max(A, B) {
    return this._addCheck({
      kind: "max",
      value: A,
      ...V9.errToObj(B)
    });
  }
  length(A, B) {
    return this._addCheck({
      kind: "length",
      value: A,
      ...V9.errToObj(B)
    });
  }
  nonempty(A) {
    return this.min(1, V9.errToObj(A));
  }
  trim() {
    return new Ew({
      ...this._def,
      checks: [...this._def.checks, {
        kind: "trim"
      }]
    });
  }
  toLowerCase() {
    return new Ew({
      ...this._def,
      checks: [...this._def.checks, {
        kind: "toLowerCase"
      }]
    });
  }
  toUpperCase() {
    return new Ew({
      ...this._def,
      checks: [...this._def.checks, {
        kind: "toUpperCase"
      }]
    });
  }
  get isDatetime() {
    return !!this._def.checks.find(A => A.kind === "datetime");
  }
  get isDate() {
    return !!this._def.checks.find(A => A.kind === "date");
  }
  get isTime() {
    return !!this._def.checks.find(A => A.kind === "time");
  }
  get isDuration() {
    return !!this._def.checks.find(A => A.kind === "duration");
  }
  get isEmail() {
    return !!this._def.checks.find(A => A.kind === "email");
  }
  get isURL() {
    return !!this._def.checks.find(A => A.kind === "url");
  }
  get isEmoji() {
    return !!this._def.checks.find(A => A.kind === "emoji");
  }
  get isUUID() {
    return !!this._def.checks.find(A => A.kind === "uuid");
  }
  get isNANOID() {
    return !!this._def.checks.find(A => A.kind === "nanoid");
  }
  get isCUID() {
    return !!this._def.checks.find(A => A.kind === "cuid");
  }
  get isCUID2() {
    return !!this._def.checks.find(A => A.kind === "cuid2");
  }
  get isULID() {
    return !!this._def.checks.find(A => A.kind === "ulid");
  }
  get isIP() {
    return !!this._def.checks.find(A => A.kind === "ip");
  }
  get isCIDR() {
    return !!this._def.checks.find(A => A.kind === "cidr");
  }
  get isBase64() {
    return !!this._def.checks.find(A => A.kind === "base64");
  }
  get isBase64url() {
    return !!this._def.checks.find(A => A.kind === "base64url");
  }
  get minLength() {
    let A = null;
    for (let B of this._def.checks) if (B.kind === "min") {
      if (A === null || B.value > A) A = B.value;
    }
    return A;
  }
  get maxLength() {
    let A = null;
    for (let B of this._def.checks) if (B.kind === "max") {
      if (A === null || B.value < A) A = B.value;
    }
    return A;
  }
}
Ew.create = A => {
  return new Ew({
    checks: [],
    typeName: eA.ZodString,
    coerce: A?.coerce ?? !1,
    ...Y4(A)
  });
};
function P8Q(A, B) {
  let Q = (A.toString().split(".")[1] || "").length,
    D = (B.toString().split(".")[1] || "").length,
    Z = Q > D ? Q : D,
    G = Number.parseInt(A.toFixed(Z).replace(".", "")),
    F = Number.parseInt(B.toFixed(Z).replace(".", ""));
  return G % F / 10 ** Z;
}
class yy extends h4 {
  constructor() {
    super(...arguments);
    this.min = this.gte, this.max = this.lte, this.step = this.multipleOf;
  }
  _parse(A) {
    if (this._def.coerce) A.data = Number(A.data);
    if (this._getType(A) !== z2.number) {
      let Z = this._getOrReturnCtx(A);
      return g2(Z, {
        code: sA.invalid_type,
        expected: z2.number,
        received: Z.parsedType
      }), e9;
    }
    let Q = void 0,
      D = new qY();
    for (let Z of this._def.checks) if (Z.kind === "int") {
      if (!W6.isInteger(A.data)) Q = this._getOrReturnCtx(A, Q), g2(Q, {
        code: sA.invalid_type,
        expected: "integer",
        received: "float",
        message: Z.message
      }), D.dirty();
    } else if (Z.kind === "min") {
      if (Z.inclusive ? A.data < Z.value : A.data <= Z.value) Q = this._getOrReturnCtx(A, Q), g2(Q, {
        code: sA.too_small,
        minimum: Z.value,
        type: "number",
        inclusive: Z.inclusive,
        exact: !1,
        message: Z.message
      }), D.dirty();
    } else if (Z.kind === "max") {
      if (Z.inclusive ? A.data > Z.value : A.data >= Z.value) Q = this._getOrReturnCtx(A, Q), g2(Q, {
        code: sA.too_big,
        maximum: Z.value,
        type: "number",
        inclusive: Z.inclusive,
        exact: !1,
        message: Z.message
      }), D.dirty();
    } else if (Z.kind === "multipleOf") {
      if (P8Q(A.data, Z.value) !== 0) Q = this._getOrReturnCtx(A, Q), g2(Q, {
        code: sA.not_multiple_of,
        multipleOf: Z.value,
        message: Z.message
      }), D.dirty();
    } else if (Z.kind === "finite") {
      if (!Number.isFinite(A.data)) Q = this._getOrReturnCtx(A, Q), g2(Q, {
        code: sA.not_finite,
        message: Z.message
      }), D.dirty();
    } else W6.assertNever(Z);
    return {
      status: D.value,
      value: A.data
    };
  }
  gte(A, B) {
    return this.setLimit("min", A, !0, V9.toString(B));
  }
  gt(A, B) {
    return this.setLimit("min", A, !1, V9.toString(B));
  }
  lte(A, B) {
    return this.setLimit("max", A, !0, V9.toString(B));
  }
  lt(A, B) {
    return this.setLimit("max", A, !1, V9.toString(B));
  }
  setLimit(A, B, Q, D) {
    return new yy({
      ...this._def,
      checks: [...this._def.checks, {
        kind: A,
        value: B,
        inclusive: Q,
        message: V9.toString(D)
      }]
    });
  }
  _addCheck(A) {
    return new yy({
      ...this._def,
      checks: [...this._def.checks, A]
    });
  }
  int(A) {
    return this._addCheck({
      kind: "int",
      message: V9.toString(A)
    });
  }
  positive(A) {
    return this._addCheck({
      kind: "min",
      value: 0,
      inclusive: !1,
      message: V9.toString(A)
    });
  }
  negative(A) {
    return this._addCheck({
      kind: "max",
      value: 0,
      inclusive: !1,
      message: V9.toString(A)
    });
  }
  nonpositive(A) {
    return this._addCheck({
      kind: "max",
      value: 0,
      inclusive: !0,
      message: V9.toString(A)
    });
  }
  nonnegative(A) {
    return this._addCheck({
      kind: "min",
      value: 0,
      inclusive: !0,
      message: V9.toString(A)
    });
  }
  multipleOf(A, B) {
    return this._addCheck({
      kind: "multipleOf",
      value: A,
      message: V9.toString(B)
    });
  }
  finite(A) {
    return this._addCheck({
      kind: "finite",
      message: V9.toString(A)
    });
  }
  safe(A) {
    return this._addCheck({
      kind: "min",
      inclusive: !0,
      value: Number.MIN_SAFE_INTEGER,
      message: V9.toString(A)
    })._addCheck({
      kind: "max",
      inclusive: !0,
      value: Number.MAX_SAFE_INTEGER,
      message: V9.toString(A)
    });
  }
  get minValue() {
    let A = null;
    for (let B of this._def.checks) if (B.kind === "min") {
      if (A === null || B.value > A) A = B.value;
    }
    return A;
  }
  get maxValue() {
    let A = null;
    for (let B of this._def.checks) if (B.kind === "max") {
      if (A === null || B.value < A) A = B.value;
    }
    return A;
  }
  get isInt() {
    return !!this._def.checks.find(A => A.kind === "int" || A.kind === "multipleOf" && W6.isInteger(A.value));
  }
  get isFinite() {
    let A = null,
      B = null;
    for (let Q of this._def.checks) if (Q.kind === "finite" || Q.kind === "int" || Q.kind === "multipleOf") return !0;else if (Q.kind === "min") {
      if (B === null || Q.value > B) B = Q.value;
    } else if (Q.kind === "max") {
      if (A === null || Q.value < A) A = Q.value;
    }
    return Number.isFinite(B) && Number.isFinite(A);
  }
}
yy.create = A => {
  return new yy({
    checks: [],
    typeName: eA.ZodNumber,
    coerce: A?.coerce || !1,
    ...Y4(A)
  });
};
class ky extends h4 {
  constructor() {
    super(...arguments);
    this.min = this.gte, this.max = this.lte;
  }
  _parse(A) {
    if (this._def.coerce) try {
      A.data = BigInt(A.data);
    } catch {
      return this._getInvalidInput(A);
    }
    if (this._getType(A) !== z2.bigint) return this._getInvalidInput(A);
    let Q = void 0,
      D = new qY();
    for (let Z of this._def.checks) if (Z.kind === "min") {
      if (Z.inclusive ? A.data < Z.value : A.data <= Z.value) Q = this._getOrReturnCtx(A, Q), g2(Q, {
        code: sA.too_small,
        type: "bigint",
        minimum: Z.value,
        inclusive: Z.inclusive,
        message: Z.message
      }), D.dirty();
    } else if (Z.kind === "max") {
      if (Z.inclusive ? A.data > Z.value : A.data >= Z.value) Q = this._getOrReturnCtx(A, Q), g2(Q, {
        code: sA.too_big,
        type: "bigint",
        maximum: Z.value,
        inclusive: Z.inclusive,
        message: Z.message
      }), D.dirty();
    } else if (Z.kind === "multipleOf") {
      if (A.data % Z.value !== BigInt(0)) Q = this._getOrReturnCtx(A, Q), g2(Q, {
        code: sA.not_multiple_of,
        multipleOf: Z.value,
        message: Z.message
      }), D.dirty();
    } else W6.assertNever(Z);
    return {
      status: D.value,
      value: A.data
    };
  }
  _getInvalidInput(A) {
    let B = this._getOrReturnCtx(A);
    return g2(B, {
      code: sA.invalid_type,
      expected: z2.bigint,
      received: B.parsedType
    }), e9;
  }
  gte(A, B) {
    return this.setLimit("min", A, !0, V9.toString(B));
  }
  gt(A, B) {
    return this.setLimit("min", A, !1, V9.toString(B));
  }
  lte(A, B) {
    return this.setLimit("max", A, !0, V9.toString(B));
  }
  lt(A, B) {
    return this.setLimit("max", A, !1, V9.toString(B));
  }
  setLimit(A, B, Q, D) {
    return new ky({
      ...this._def,
      checks: [...this._def.checks, {
        kind: A,
        value: B,
        inclusive: Q,
        message: V9.toString(D)
      }]
    });
  }
  _addCheck(A) {
    return new ky({
      ...this._def,
      checks: [...this._def.checks, A]
    });
  }
  positive(A) {
    return this._addCheck({
      kind: "min",
      value: BigInt(0),
      inclusive: !1,
      message: V9.toString(A)
    });
  }
  negative(A) {
    return this._addCheck({
      kind: "max",
      value: BigInt(0),
      inclusive: !1,
      message: V9.toString(A)
    });
  }
  nonpositive(A) {
    return this._addCheck({
      kind: "max",
      value: BigInt(0),
      inclusive: !0,
      message: V9.toString(A)
    });
  }
  nonnegative(A) {
    return this._addCheck({
      kind: "min",
      value: BigInt(0),
      inclusive: !0,
      message: V9.toString(A)
    });
  }
  multipleOf(A, B) {
    return this._addCheck({
      kind: "multipleOf",
      value: A,
      message: V9.toString(B)
    });
  }
  get minValue() {
    let A = null;
    for (let B of this._def.checks) if (B.kind === "min") {
      if (A === null || B.value > A) A = B.value;
    }
    return A;
  }
  get maxValue() {
    let A = null;
    for (let B of this._def.checks) if (B.kind === "max") {
      if (A === null || B.value < A) A = B.value;
    }
    return A;
  }
}
ky.create = A => {
  return new ky({
    checks: [],
    typeName: eA.ZodBigInt,
    coerce: A?.coerce ?? !1,
    ...Y4(A)
  });
};
class Ei extends h4 {
  _parse(A) {
    if (this._def.coerce) A.data = Boolean(A.data);
    if (this._getType(A) !== z2.boolean) {
      let Q = this._getOrReturnCtx(A);
      return g2(Q, {
        code: sA.invalid_type,
        expected: z2.boolean,
        received: Q.parsedType
      }), e9;
    }
    return gW(A.data);
  }
}
Ei.create = A => {
  return new Ei({
    typeName: eA.ZodBoolean,
    coerce: A?.coerce || !1,
    ...Y4(A)
  });
};
class Dh extends h4 {
  _parse(A) {
    if (this._def.coerce) A.data = new Date(A.data);
    if (this._getType(A) !== z2.date) {
      let Z = this._getOrReturnCtx(A);
      return g2(Z, {
        code: sA.invalid_type,
        expected: z2.date,
        received: Z.parsedType
      }), e9;
    }
    if (Number.isNaN(A.data.getTime())) {
      let Z = this._getOrReturnCtx(A);
      return g2(Z, {
        code: sA.invalid_date
      }), e9;
    }
    let Q = new qY(),
      D = void 0;
    for (let Z of this._def.checks) if (Z.kind === "min") {
      if (A.data.getTime() < Z.value) D = this._getOrReturnCtx(A, D), g2(D, {
        code: sA.too_small,
        message: Z.message,
        inclusive: !0,
        exact: !1,
        minimum: Z.value,
        type: "date"
      }), Q.dirty();
    } else if (Z.kind === "max") {
      if (A.data.getTime() > Z.value) D = this._getOrReturnCtx(A, D), g2(D, {
        code: sA.too_big,
        message: Z.message,
        inclusive: !0,
        exact: !1,
        maximum: Z.value,
        type: "date"
      }), Q.dirty();
    } else W6.assertNever(Z);
    return {
      status: Q.value,
      value: new Date(A.data.getTime())
    };
  }
  _addCheck(A) {
    return new Dh({
      ...this._def,
      checks: [...this._def.checks, A]
    });
  }
  min(A, B) {
    return this._addCheck({
      kind: "min",
      value: A.getTime(),
      message: V9.toString(B)
    });
  }
  max(A, B) {
    return this._addCheck({
      kind: "max",
      value: A.getTime(),
      message: V9.toString(B)
    });
  }
  get minDate() {
    let A = null;
    for (let B of this._def.checks) if (B.kind === "min") {
      if (A === null || B.value > A) A = B.value;
    }
    return A != null ? new Date(A) : null;
  }
  get maxDate() {
    let A = null;
    for (let B of this._def.checks) if (B.kind === "max") {
      if (A === null || B.value < A) A = B.value;
    }
    return A != null ? new Date(A) : null;
  }
}
Dh.create = A => {
  return new Dh({
    checks: [],
    coerce: A?.coerce || !1,
    typeName: eA.ZodDate,
    ...Y4(A)
  });
};
class E91 extends h4 {
  _parse(A) {
    if (this._getType(A) !== z2.symbol) {
      let Q = this._getOrReturnCtx(A);
      return g2(Q, {
        code: sA.invalid_type,
        expected: z2.symbol,
        received: Q.parsedType
      }), e9;
    }
    return gW(A.data);
  }
}
E91.create = A => {
  return new E91({
    typeName: eA.ZodSymbol,
    ...Y4(A)
  });
};
class Ui extends h4 {
  _parse(A) {
    if (this._getType(A) !== z2.undefined) {
      let Q = this._getOrReturnCtx(A);
      return g2(Q, {
        code: sA.invalid_type,
        expected: z2.undefined,
        received: Q.parsedType
      }), e9;
    }
    return gW(A.data);
  }
}
Ui.create = A => {
  return new Ui({
    typeName: eA.ZodUndefined,
    ...Y4(A)
  });
};
class wi extends h4 {
  _parse(A) {
    if (this._getType(A) !== z2.null) {
      let Q = this._getOrReturnCtx(A);
      return g2(Q, {
        code: sA.invalid_type,
        expected: z2.null,
        received: Q.parsedType
      }), e9;
    }
    return gW(A.data);
  }
}
wi.create = A => {
  return new wi({
    typeName: eA.ZodNull,
    ...Y4(A)
  });
};
class Zh extends h4 {
  constructor() {
    super(...arguments);
    this._any = !0;
  }
  _parse(A) {
    return gW(A.data);
  }
}
Zh.create = A => {
  return new Zh({
    typeName: eA.ZodAny,
    ...Y4(A)
  });
};
class jy extends h4 {
  constructor() {
    super(...arguments);
    this._unknown = !0;
  }
  _parse(A) {
    return gW(A.data);
  }
}
jy.create = A => {
  return new jy({
    typeName: eA.ZodUnknown,
    ...Y4(A)
  });
};
class SN extends h4 {
  _parse(A) {
    let B = this._getOrReturnCtx(A);
    return g2(B, {
      code: sA.invalid_type,
      expected: z2.never,
      received: B.parsedType
    }), e9;
  }
}
SN.create = A => {
  return new SN({
    typeName: eA.ZodNever,
    ...Y4(A)
  });
};
class U91 extends h4 {
  _parse(A) {
    if (this._getType(A) !== z2.undefined) {
      let Q = this._getOrReturnCtx(A);
      return g2(Q, {
        code: sA.invalid_type,
        expected: z2.void,
        received: Q.parsedType
      }), e9;
    }
    return gW(A.data);
  }
}
U91.create = A => {
  return new U91({
    typeName: eA.ZodVoid,
    ...Y4(A)
  });
};
class Uw extends h4 {
  _parse(A) {
    let {
        ctx: B,
        status: Q
      } = this._processInputParams(A),
      D = this._def;
    if (B.parsedType !== z2.array) return g2(B, {
      code: sA.invalid_type,
      expected: z2.array,
      received: B.parsedType
    }), e9;
    if (D.exactLength !== null) {
      let G = B.data.length > D.exactLength.value,
        F = B.data.length < D.exactLength.value;
      if (G || F) g2(B, {
        code: G ? sA.too_big : sA.too_small,
        minimum: F ? D.exactLength.value : void 0,
        maximum: G ? D.exactLength.value : void 0,
        type: "array",
        inclusive: !0,
        exact: !0,
        message: D.exactLength.message
      }), Q.dirty();
    }
    if (D.minLength !== null) {
      if (B.data.length < D.minLength.value) g2(B, {
        code: sA.too_small,
        minimum: D.minLength.value,
        type: "array",
        inclusive: !0,
        exact: !1,
        message: D.minLength.message
      }), Q.dirty();
    }
    if (D.maxLength !== null) {
      if (B.data.length > D.maxLength.value) g2(B, {
        code: sA.too_big,
        maximum: D.maxLength.value,
        type: "array",
        inclusive: !0,
        exact: !1,
        message: D.maxLength.message
      }), Q.dirty();
    }
    if (B.common.async) return Promise.all([...B.data].map((G, F) => {
      return D.type._parseAsync(new ww(B, G, B.path, F));
    })).then(G => {
      return qY.mergeArray(Q, G);
    });
    let Z = [...B.data].map((G, F) => {
      return D.type._parseSync(new ww(B, G, B.path, F));
    });
    return qY.mergeArray(Q, Z);
  }
  get element() {
    return this._def.type;
  }
  min(A, B) {
    return new Uw({
      ...this._def,
      minLength: {
        value: A,
        message: V9.toString(B)
      }
    });
  }
  max(A, B) {
    return new Uw({
      ...this._def,
      maxLength: {
        value: A,
        message: V9.toString(B)
      }
    });
  }
  length(A, B) {
    return new Uw({
      ...this._def,
      exactLength: {
        value: A,
        message: V9.toString(B)
      }
    });
  }
  nonempty(A) {
    return this.min(1, A);
  }
}
Uw.create = (A, B) => {
  return new Uw({
    type: A,
    minLength: null,
    maxLength: null,
    exactLength: null,
    typeName: eA.ZodArray,
    ...Y4(B)
  });
};
function Hi(A) {
  if (A instanceof mD) {
    let B = {};
    for (let Q in A.shape) {
      let D = A.shape[Q];
      B[Q] = nC.create(Hi(D));
    }
    return new mD({
      ...A._def,
      shape: () => B
    });
  } else if (A instanceof Uw) return new Uw({
    ...A._def,
    type: Hi(A.element)
  });else if (A instanceof nC) return nC.create(Hi(A.unwrap()));else if (A instanceof _O) return _O.create(Hi(A.unwrap()));else if (A instanceof jN) return jN.create(A.items.map(B => Hi(B)));else return A;
}
class mD extends h4 {
  constructor() {
    super(...arguments);
    this._cached = null, this.nonstrict = this.passthrough, this.augment = this.extend;
  }
  _getCached() {
    if (this._cached !== null) return this._cached;
    let A = this._def.shape(),
      B = W6.objectKeys(A);
    return this._cached = {
      shape: A,
      keys: B
    }, this._cached;
  }
  _parse(A) {
    if (this._getType(A) !== z2.object) {
      let Y = this._getOrReturnCtx(A);
      return g2(Y, {
        code: sA.invalid_type,
        expected: z2.object,
        received: Y.parsedType
      }), e9;
    }
    let {
        status: Q,
        ctx: D
      } = this._processInputParams(A),
      {
        shape: Z,
        keys: G
      } = this._getCached(),
      F = [];
    if (!(this._def.catchall instanceof SN && this._def.unknownKeys === "strip")) {
      for (let Y in D.data) if (!G.includes(Y)) F.push(Y);
    }
    let I = [];
    for (let Y of G) {
      let W = Z[Y],
        J = D.data[Y];
      I.push({
        key: {
          status: "valid",
          value: Y
        },
        value: W._parse(new ww(D, J, D.path, Y)),
        alwaysSet: Y in D.data
      });
    }
    if (this._def.catchall instanceof SN) {
      let Y = this._def.unknownKeys;
      if (Y === "passthrough") for (let W of F) I.push({
        key: {
          status: "valid",
          value: W
        },
        value: {
          status: "valid",
          value: D.data[W]
        }
      });else if (Y === "strict") {
        if (F.length > 0) g2(D, {
          code: sA.unrecognized_keys,
          keys: F
        }), Q.dirty();
      } else if (Y === "strip") ;else throw new Error("Internal ZodObject error: invalid unknownKeys value.");
    } else {
      let Y = this._def.catchall;
      for (let W of F) {
        let J = D.data[W];
        I.push({
          key: {
            status: "valid",
            value: W
          },
          value: Y._parse(new ww(D, J, D.path, W)),
          alwaysSet: W in D.data
        });
      }
    }
    if (D.common.async) return Promise.resolve().then(async () => {
      let Y = [];
      for (let W of I) {
        let J = await W.key,
          X = await W.value;
        Y.push({
          key: J,
          value: X,
          alwaysSet: W.alwaysSet
        });
      }
      return Y;
    }).then(Y => {
      return qY.mergeObjectSync(Q, Y);
    });else return qY.mergeObjectSync(Q, I);
  }
  get shape() {
    return this._def.shape();
  }
  strict(A) {
    return V9.errToObj, new mD({
      ...this._def,
      unknownKeys: "strict",
      ...(A !== void 0 ? {
        errorMap: (B, Q) => {
          let D = this._def.errorMap?.(B, Q).message ?? Q.defaultError;
          if (B.code === "unrecognized_keys") return {
            message: V9.errToObj(A).message ?? D
          };
          return {
            message: D
          };
        }
      } : {})
    });
  }
  strip() {
    return new mD({
      ...this._def,
      unknownKeys: "strip"
    });
  }
  passthrough() {
    return new mD({
      ...this._def,
      unknownKeys: "passthrough"
    });
  }
  extend(A) {
    return new mD({
      ...this._def,
      shape: () => ({
        ...this._def.shape(),
        ...A
      })
    });
  }
  merge(A) {
    return new mD({
      unknownKeys: A._def.unknownKeys,
      catchall: A._def.catchall,
      shape: () => ({
        ...this._def.shape(),
        ...A._def.shape()
      }),
      typeName: eA.ZodObject
    });
  }
  setKey(A, B) {
    return this.augment({
      [A]: B
    });
  }
  catchall(A) {
    return new mD({
      ...this._def,
      catchall: A
    });
  }
  pick(A) {
    let B = {};
    for (let Q of W6.objectKeys(A)) if (A[Q] && this.shape[Q]) B[Q] = this.shape[Q];
    return new mD({
      ...this._def,
      shape: () => B
    });
  }
  omit(A) {
    let B = {};
    for (let Q of W6.objectKeys(this.shape)) if (!A[Q]) B[Q] = this.shape[Q];
    return new mD({
      ...this._def,
      shape: () => B
    });
  }
  deepPartial() {
    return Hi(this);
  }
  partial(A) {
    let B = {};
    for (let Q of W6.objectKeys(this.shape)) {
      let D = this.shape[Q];
      if (A && !A[Q]) B[Q] = D;else B[Q] = D.optional();
    }
    return new mD({
      ...this._def,
      shape: () => B
    });
  }
  required(A) {
    let B = {};
    for (let Q of W6.objectKeys(this.shape)) if (A && !A[Q]) B[Q] = this.shape[Q];else {
      let Z = this.shape[Q];
      while (Z instanceof nC) Z = Z._def.innerType;
      B[Q] = Z;
    }
    return new mD({
      ...this._def,
      shape: () => B
    });
  }
  keyof() {
    return qIA(W6.objectKeys(this.shape));
  }
}
mD.create = (A, B) => {
  return new mD({
    shape: () => A,
    unknownKeys: "strip",
    catchall: SN.create(),
    typeName: eA.ZodObject,
    ...Y4(B)
  });
};
mD.strictCreate = (A, B) => {
  return new mD({
    shape: () => A,
    unknownKeys: "strict",
    catchall: SN.create(),
    typeName: eA.ZodObject,
    ...Y4(B)
  });
};
mD.lazycreate = (A, B) => {
  return new mD({
    shape: A,
    unknownKeys: "strip",
    catchall: SN.create(),
    typeName: eA.ZodObject,
    ...Y4(B)
  });
};
class $i extends h4 {
  _parse(A) {
    let {
        ctx: B
      } = this._processInputParams(A),
      Q = this._def.options;
    function D(Z) {
      for (let F of Z) if (F.result.status === "valid") return F.result;
      for (let F of Z) if (F.result.status === "dirty") return B.common.issues.push(...F.ctx.common.issues), F.result;
      let G = Z.map(F => new CV(F.ctx.common.issues));
      return g2(B, {
        code: sA.invalid_union,
        unionErrors: G
      }), e9;
    }
    if (B.common.async) return Promise.all(Q.map(async Z => {
      let G = {
        ...B,
        common: {
          ...B.common,
          issues: []
        },
        parent: null
      };
      return {
        result: await Z._parseAsync({
          data: B.data,
          path: B.path,
          parent: G
        }),
        ctx: G
      };
    })).then(D);else {
      let Z = void 0,
        G = [];
      for (let I of Q) {
        let Y = {
            ...B,
            common: {
              ...B.common,
              issues: []
            },
            parent: null
          },
          W = I._parseSync({
            data: B.data,
            path: B.path,
            parent: Y
          });
        if (W.status === "valid") return W;else if (W.status === "dirty" && !Z) Z = {
          result: W,
          ctx: Y
        };
        if (Y.common.issues.length) G.push(Y.common.issues);
      }
      if (Z) return B.common.issues.push(...Z.ctx.common.issues), Z.result;
      let F = G.map(I => new CV(I));
      return g2(B, {
        code: sA.invalid_union,
        unionErrors: F
      }), e9;
    }
  }
  get options() {
    return this._def.options;
  }
}
$i.create = (A, B) => {
  return new $i({
    options: A,
    typeName: eA.ZodUnion,
    ...Y4(B)
  });
};
var kO = A => {
  if (A instanceof Ni) return kO(A.schema);else if (A instanceof $w) return kO(A.innerType());else if (A instanceof Li) return [A.value];else if (A instanceof _y) return A.options;else if (A instanceof Mi) return W6.objectValues(A.enum);else if (A instanceof Ri) return kO(A._def.innerType);else if (A instanceof Ui) return [void 0];else if (A instanceof wi) return [null];else if (A instanceof nC) return [void 0, ...kO(A.unwrap())];else if (A instanceof _O) return [null, ...kO(A.unwrap())];else if (A instanceof XK1) return kO(A.unwrap());else if (A instanceof Ti) return kO(A.unwrap());else if (A instanceof Oi) return kO(A._def.innerType);else return [];
};
class JK1 extends h4 {
  _parse(A) {
    let {
      ctx: B
    } = this._processInputParams(A);
    if (B.parsedType !== z2.object) return g2(B, {
      code: sA.invalid_type,
      expected: z2.object,
      received: B.parsedType
    }), e9;
    let Q = this.discriminator,
      D = B.data[Q],
      Z = this.optionsMap.get(D);
    if (!Z) return g2(B, {
      code: sA.invalid_union_discriminator,
      options: Array.from(this.optionsMap.keys()),
      path: [Q]
    }), e9;
    if (B.common.async) return Z._parseAsync({
      data: B.data,
      path: B.path,
      parent: B
    });else return Z._parseSync({
      data: B.data,
      path: B.path,
      parent: B
    });
  }
  get discriminator() {
    return this._def.discriminator;
  }
  get options() {
    return this._def.options;
  }
  get optionsMap() {
    return this._def.optionsMap;
  }
  static create(A, B, Q) {
    let D = new Map();
    for (let Z of B) {
      let G = kO(Z.shape[A]);
      if (!G.length) throw new Error(`A discriminator value for key \`${A}\` could not be extracted from all schema options`);
      for (let F of G) {
        if (D.has(F)) throw new Error(`Discriminator property ${String(A)} has duplicate value ${String(F)}`);
        D.set(F, Z);
      }
    }
    return new JK1({
      typeName: eA.ZodDiscriminatedUnion,
      discriminator: A,
      options: B,
      optionsMap: D,
      ...Y4(Q)
    });
  }
}
function Jr1(A, B) {
  let Q = PN(A),
    D = PN(B);
  if (A === B) return {
    valid: !0,
    data: A
  };else if (Q === z2.object && D === z2.object) {
    let Z = W6.objectKeys(B),
      G = W6.objectKeys(A).filter(I => Z.indexOf(I) !== -1),
      F = {
        ...A,
        ...B
      };
    for (let I of G) {
      let Y = Jr1(A[I], B[I]);
      if (!Y.valid) return {
        valid: !1
      };
      F[I] = Y.data;
    }
    return {
      valid: !0,
      data: F
    };
  } else if (Q === z2.array && D === z2.array) {
    if (A.length !== B.length) return {
      valid: !1
    };
    let Z = [];
    for (let G = 0; G < A.length; G++) {
      let F = A[G],
        I = B[G],
        Y = Jr1(F, I);
      if (!Y.valid) return {
        valid: !1
      };
      Z.push(Y.data);
    }
    return {
      valid: !0,
      data: Z
    };
  } else if (Q === z2.date && D === z2.date && +A === +B) return {
    valid: !0,
    data: A
  };else return {
    valid: !1
  };
}
class qi extends h4 {
  _parse(A) {
    let {
        status: B,
        ctx: Q
      } = this._processInputParams(A),
      D = (Z, G) => {
        if (YK1(Z) || YK1(G)) return e9;
        let F = Jr1(Z.value, G.value);
        if (!F.valid) return g2(Q, {
          code: sA.invalid_intersection_types
        }), e9;
        if (WK1(Z) || WK1(G)) B.dirty();
        return {
          status: B.value,
          value: F.data
        };
      };
    if (Q.common.async) return Promise.all([this._def.left._parseAsync({
      data: Q.data,
      path: Q.path,
      parent: Q
    }), this._def.right._parseAsync({
      data: Q.data,
      path: Q.path,
      parent: Q
    })]).then(([Z, G]) => D(Z, G));else return D(this._def.left._parseSync({
      data: Q.data,
      path: Q.path,
      parent: Q
    }), this._def.right._parseSync({
      data: Q.data,
      path: Q.path,
      parent: Q
    }));
  }
}
qi.create = (A, B, Q) => {
  return new qi({
    left: A,
    right: B,
    typeName: eA.ZodIntersection,
    ...Y4(Q)
  });
};
class jN extends h4 {
  _parse(A) {
    let {
      status: B,
      ctx: Q
    } = this._processInputParams(A);
    if (Q.parsedType !== z2.array) return g2(Q, {
      code: sA.invalid_type,
      expected: z2.array,
      received: Q.parsedType
    }), e9;
    if (Q.data.length < this._def.items.length) return g2(Q, {
      code: sA.too_small,
      minimum: this._def.items.length,
      inclusive: !0,
      exact: !1,
      type: "array"
    }), e9;
    if (!this._def.rest && Q.data.length > this._def.items.length) g2(Q, {
      code: sA.too_big,
      maximum: this._def.items.length,
      inclusive: !0,
      exact: !1,
      type: "array"
    }), B.dirty();
    let Z = [...Q.data].map((G, F) => {
      let I = this._def.items[F] || this._def.rest;
      if (!I) return null;
      return I._parse(new ww(Q, G, Q.path, F));
    }).filter(G => !!G);
    if (Q.common.async) return Promise.all(Z).then(G => {
      return qY.mergeArray(B, G);
    });else return qY.mergeArray(B, Z);
  }
  get items() {
    return this._def.items;
  }
  rest(A) {
    return new jN({
      ...this._def,
      rest: A
    });
  }
}
jN.create = (A, B) => {
  if (!Array.isArray(A)) throw new Error("You must pass an array of schemas to z.tuple([ ... ])");
  return new jN({
    items: A,
    typeName: eA.ZodTuple,
    rest: null,
    ...Y4(B)
  });
};
class w91 extends h4 {
  get keySchema() {
    return this._def.keyType;
  }
  get valueSchema() {
    return this._def.valueType;
  }
  _parse(A) {
    let {
      status: B,
      ctx: Q
    } = this._processInputParams(A);
    if (Q.parsedType !== z2.object) return g2(Q, {
      code: sA.invalid_type,
      expected: z2.object,
      received: Q.parsedType
    }), e9;
    let D = [],
      Z = this._def.keyType,
      G = this._def.valueType;
    for (let F in Q.data) D.push({
      key: Z._parse(new ww(Q, F, Q.path, F)),
      value: G._parse(new ww(Q, Q.data[F], Q.path, F)),
      alwaysSet: F in Q.data
    });
    if (Q.common.async) return qY.mergeObjectAsync(B, D);else return qY.mergeObjectSync(B, D);
  }
  get element() {
    return this._def.valueType;
  }
  static create(A, B, Q) {
    if (B instanceof h4) return new w91({
      keyType: A,
      valueType: B,
      typeName: eA.ZodRecord,
      ...Y4(Q)
    });
    return new w91({
      keyType: Ew.create(),
      valueType: A,
      typeName: eA.ZodRecord,
      ...Y4(B)
    });
  }
}
class $91 extends h4 {
  get keySchema() {
    return this._def.keyType;
  }
  get valueSchema() {
    return this._def.valueType;
  }
  _parse(A) {
    let {
      status: B,
      ctx: Q
    } = this._processInputParams(A);
    if (Q.parsedType !== z2.map) return g2(Q, {
      code: sA.invalid_type,
      expected: z2.map,
      received: Q.parsedType
    }), e9;
    let D = this._def.keyType,
      Z = this._def.valueType,
      G = [...Q.data.entries()].map(([F, I], Y) => {
        return {
          key: D._parse(new ww(Q, F, Q.path, [Y, "key"])),
          value: Z._parse(new ww(Q, I, Q.path, [Y, "value"]))
        };
      });
    if (Q.common.async) {
      let F = new Map();
      return Promise.resolve().then(async () => {
        for (let I of G) {
          let Y = await I.key,
            W = await I.value;
          if (Y.status === "aborted" || W.status === "aborted") return e9;
          if (Y.status === "dirty" || W.status === "dirty") B.dirty();
          F.set(Y.value, W.value);
        }
        return {
          status: B.value,
          value: F
        };
      });
    } else {
      let F = new Map();
      for (let I of G) {
        let {
          key: Y,
          value: W
        } = I;
        if (Y.status === "aborted" || W.status === "aborted") return e9;
        if (Y.status === "dirty" || W.status === "dirty") B.dirty();
        F.set(Y.value, W.value);
      }
      return {
        status: B.value,
        value: F
      };
    }
  }
}
$91.create = (A, B, Q) => {
  return new $91({
    valueType: B,
    keyType: A,
    typeName: eA.ZodMap,
    ...Y4(Q)
  });
};
class Gh extends h4 {
  _parse(A) {
    let {
      status: B,
      ctx: Q
    } = this._processInputParams(A);
    if (Q.parsedType !== z2.set) return g2(Q, {
      code: sA.invalid_type,
      expected: z2.set,
      received: Q.parsedType
    }), e9;
    let D = this._def;
    if (D.minSize !== null) {
      if (Q.data.size < D.minSize.value) g2(Q, {
        code: sA.too_small,
        minimum: D.minSize.value,
        type: "set",
        inclusive: !0,
        exact: !1,
        message: D.minSize.message
      }), B.dirty();
    }
    if (D.maxSize !== null) {
      if (Q.data.size > D.maxSize.value) g2(Q, {
        code: sA.too_big,
        maximum: D.maxSize.value,
        type: "set",
        inclusive: !0,
        exact: !1,
        message: D.maxSize.message
      }), B.dirty();
    }
    let Z = this._def.valueType;
    function G(I) {
      let Y = new Set();
      for (let W of I) {
        if (W.status === "aborted") return e9;
        if (W.status === "dirty") B.dirty();
        Y.add(W.value);
      }
      return {
        status: B.value,
        value: Y
      };
    }
    let F = [...Q.data.values()].map((I, Y) => Z._parse(new ww(Q, I, Q.path, Y)));
    if (Q.common.async) return Promise.all(F).then(I => G(I));else return G(F);
  }
  min(A, B) {
    return new Gh({
      ...this._def,
      minSize: {
        value: A,
        message: V9.toString(B)
      }
    });
  }
  max(A, B) {
    return new Gh({
      ...this._def,
      maxSize: {
        value: A,
        message: V9.toString(B)
      }
    });
  }
  size(A, B) {
    return this.min(A, B).max(A, B);
  }
  nonempty(A) {
    return this.min(1, A);
  }
}
Gh.create = (A, B) => {
  return new Gh({
    valueType: A,
    minSize: null,
    maxSize: null,
    typeName: eA.ZodSet,
    ...Y4(B)
  });
};
class zi extends h4 {
  constructor() {
    super(...arguments);
    this.validate = this.implement;
  }
  _parse(A) {
    let {
      ctx: B
    } = this._processInputParams(A);
    if (B.parsedType !== z2.function) return g2(B, {
      code: sA.invalid_type,
      expected: z2.function,
      received: B.parsedType
    }), e9;
    function Q(F, I) {
      return z91({
        data: F,
        path: B.path,
        errorMaps: [B.common.contextualErrorMap, B.schemaErrorMap, Ci(), yO].filter(Y => !!Y),
        issueData: {
          code: sA.invalid_arguments,
          argumentsError: I
        }
      });
    }
    function D(F, I) {
      return z91({
        data: F,
        path: B.path,
        errorMaps: [B.common.contextualErrorMap, B.schemaErrorMap, Ci(), yO].filter(Y => !!Y),
        issueData: {
          code: sA.invalid_return_type,
          returnTypeError: I
        }
      });
    }
    let Z = {
        errorMap: B.common.contextualErrorMap
      },
      G = B.data;
    if (this._def.returns instanceof Fh) {
      let F = this;
      return gW(async function (...I) {
        let Y = new CV([]),
          W = await F._def.args.parseAsync(I, Z).catch(V => {
            throw Y.addIssue(Q(I, V)), Y;
          }),
          J = await Reflect.apply(G, this, W);
        return await F._def.returns._def.type.parseAsync(J, Z).catch(V => {
          throw Y.addIssue(D(J, V)), Y;
        });
      });
    } else {
      let F = this;
      return gW(function (...I) {
        let Y = F._def.args.safeParse(I, Z);
        if (!Y.success) throw new CV([Q(I, Y.error)]);
        let W = Reflect.apply(G, this, Y.data),
          J = F._def.returns.safeParse(W, Z);
        if (!J.success) throw new CV([D(W, J.error)]);
        return J.data;
      });
    }
  }
  parameters() {
    return this._def.args;
  }
  returnType() {
    return this._def.returns;
  }
  args(...A) {
    return new zi({
      ...this._def,
      args: jN.create(A).rest(jy.create())
    });
  }
  returns(A) {
    return new zi({
      ...this._def,
      returns: A
    });
  }
  implement(A) {
    return this.parse(A);
  }
  strictImplement(A) {
    return this.parse(A);
  }
  static create(A, B, Q) {
    return new zi({
      args: A ? A : jN.create([]).rest(jy.create()),
      returns: B || jy.create(),
      typeName: eA.ZodFunction,
      ...Y4(Q)
    });
  }
}
class Ni extends h4 {
  get schema() {
    return this._def.getter();
  }
  _parse(A) {
    let {
      ctx: B
    } = this._processInputParams(A);
    return this._def.getter()._parse({
      data: B.data,
      path: B.path,
      parent: B
    });
  }
}
Ni.create = (A, B) => {
  return new Ni({
    getter: A,
    typeName: eA.ZodLazy,
    ...Y4(B)
  });
};
class Li extends h4 {
  _parse(A) {
    if (A.data !== this._def.value) {
      let B = this._getOrReturnCtx(A);
      return g2(B, {
        received: B.data,
        code: sA.invalid_literal,
        expected: this._def.value
      }), e9;
    }
    return {
      status: "valid",
      value: A.data
    };
  }
  get value() {
    return this._def.value;
  }
}
Li.create = (A, B) => {
  return new Li({
    value: A,
    typeName: eA.ZodLiteral,
    ...Y4(B)
  });
};
function qIA(A, B) {
  return new _y({
    values: A,
    typeName: eA.ZodEnum,
    ...Y4(B)
  });
}
class _y extends h4 {
  _parse(A) {
    if (typeof A.data !== "string") {
      let B = this._getOrReturnCtx(A),
        Q = this._def.values;
      return g2(B, {
        expected: W6.joinValues(Q),
        received: B.parsedType,
        code: sA.invalid_type
      }), e9;
    }
    if (!this._cache) this._cache = new Set(this._def.values);
    if (!this._cache.has(A.data)) {
      let B = this._getOrReturnCtx(A),
        Q = this._def.values;
      return g2(B, {
        received: B.data,
        code: sA.invalid_enum_value,
        options: Q
      }), e9;
    }
    return gW(A.data);
  }
  get options() {
    return this._def.values;
  }
  get enum() {
    let A = {};
    for (let B of this._def.values) A[B] = B;
    return A;
  }
  get Values() {
    let A = {};
    for (let B of this._def.values) A[B] = B;
    return A;
  }
  get Enum() {
    let A = {};
    for (let B of this._def.values) A[B] = B;
    return A;
  }
  extract(A, B = this._def) {
    return _y.create(A, {
      ...this._def,
      ...B
    });
  }
  exclude(A, B = this._def) {
    return _y.create(this.options.filter(Q => !A.includes(Q)), {
      ...this._def,
      ...B
    });
  }
}
_y.create = qIA;
class Mi extends h4 {
  _parse(A) {
    let B = W6.getValidEnumValues(this._def.values),
      Q = this._getOrReturnCtx(A);
    if (Q.parsedType !== z2.string && Q.parsedType !== z2.number) {
      let D = W6.objectValues(B);
      return g2(Q, {
        expected: W6.joinValues(D),
        received: Q.parsedType,
        code: sA.invalid_type
      }), e9;
    }
    if (!this._cache) this._cache = new Set(W6.getValidEnumValues(this._def.values));
    if (!this._cache.has(A.data)) {
      let D = W6.objectValues(B);
      return g2(Q, {
        received: Q.data,
        code: sA.invalid_enum_value,
        options: D
      }), e9;
    }
    return gW(A.data);
  }
  get enum() {
    return this._def.values;
  }
}
Mi.create = (A, B) => {
  return new Mi({
    values: A,
    typeName: eA.ZodNativeEnum,
    ...Y4(B)
  });
};
class Fh extends h4 {
  unwrap() {
    return this._def.type;
  }
  _parse(A) {
    let {
      ctx: B
    } = this._processInputParams(A);
    if (B.parsedType !== z2.promise && B.common.async === !1) return g2(B, {
      code: sA.invalid_type,
      expected: z2.promise,
      received: B.parsedType
    }), e9;
    let Q = B.parsedType === z2.promise ? B.data : Promise.resolve(B.data);
    return gW(Q.then(D => {
      return this._def.type.parseAsync(D, {
        path: B.path,
        errorMap: B.common.contextualErrorMap
      });
    }));
  }
}
Fh.create = (A, B) => {
  return new Fh({
    type: A,
    typeName: eA.ZodPromise,
    ...Y4(B)
  });
};
class $w extends h4 {
  innerType() {
    return this._def.schema;
  }
  sourceType() {
    return this._def.schema._def.typeName === eA.ZodEffects ? this._def.schema.sourceType() : this._def.schema;
  }
  _parse(A) {
    let {
        status: B,
        ctx: Q
      } = this._processInputParams(A),
      D = this._def.effect || null,
      Z = {
        addIssue: G => {
          if (g2(Q, G), G.fatal) B.abort();else B.dirty();
        },
        get path() {
          return Q.path;
        }
      };
    if (Z.addIssue = Z.addIssue.bind(Z), D.type === "preprocess") {
      let G = D.transform(Q.data, Z);
      if (Q.common.async) return Promise.resolve(G).then(async F => {
        if (B.value === "aborted") return e9;
        let I = await this._def.schema._parseAsync({
          data: F,
          path: Q.path,
          parent: Q
        });
        if (I.status === "aborted") return e9;
        if (I.status === "dirty") return Qh(I.value);
        if (B.value === "dirty") return Qh(I.value);
        return I;
      });else {
        if (B.value === "aborted") return e9;
        let F = this._def.schema._parseSync({
          data: G,
          path: Q.path,
          parent: Q
        });
        if (F.status === "aborted") return e9;
        if (F.status === "dirty") return Qh(F.value);
        if (B.value === "dirty") return Qh(F.value);
        return F;
      }
    }
    if (D.type === "refinement") {
      let G = F => {
        let I = D.refinement(F, Z);
        if (Q.common.async) return Promise.resolve(I);
        if (I instanceof Promise) throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");
        return F;
      };
      if (Q.common.async === !1) {
        let F = this._def.schema._parseSync({
          data: Q.data,
          path: Q.path,
          parent: Q
        });
        if (F.status === "aborted") return e9;
        if (F.status === "dirty") B.dirty();
        return G(F.value), {
          status: B.value,
          value: F.value
        };
      } else return this._def.schema._parseAsync({
        data: Q.data,
        path: Q.path,
        parent: Q
      }).then(F => {
        if (F.status === "aborted") return e9;
        if (F.status === "dirty") B.dirty();
        return G(F.value).then(() => {
          return {
            status: B.value,
            value: F.value
          };
        });
      });
    }
    if (D.type === "transform") if (Q.common.async === !1) {
      let G = this._def.schema._parseSync({
        data: Q.data,
        path: Q.path,
        parent: Q
      });
      if (!Sy(G)) return e9;
      let F = D.transform(G.value, Z);
      if (F instanceof Promise) throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");
      return {
        status: B.value,
        value: F
      };
    } else return this._def.schema._parseAsync({
      data: Q.data,
      path: Q.path,
      parent: Q
    }).then(G => {
      if (!Sy(G)) return e9;
      return Promise.resolve(D.transform(G.value, Z)).then(F => ({
        status: B.value,
        value: F
      }));
    });
    W6.assertNever(D);
  }
}
$w.create = (A, B, Q) => {
  return new $w({
    schema: A,
    typeName: eA.ZodEffects,
    effect: B,
    ...Y4(Q)
  });
};
$w.createWithPreprocess = (A, B, Q) => {
  return new $w({
    schema: B,
    effect: {
      type: "preprocess",
      transform: A
    },
    typeName: eA.ZodEffects,
    ...Y4(Q)
  });
};
class nC extends h4 {
  _parse(A) {
    if (this._getType(A) === z2.undefined) return gW(void 0);
    return this._def.innerType._parse(A);
  }
  unwrap() {
    return this._def.innerType;
  }
}
nC.create = (A, B) => {
  return new nC({
    innerType: A,
    typeName: eA.ZodOptional,
    ...Y4(B)
  });
};
class _O extends h4 {
  _parse(A) {
    if (this._getType(A) === z2.null) return gW(null);
    return this._def.innerType._parse(A);
  }
  unwrap() {
    return this._def.innerType;
  }
}
_O.create = (A, B) => {
  return new _O({
    innerType: A,
    typeName: eA.ZodNullable,
    ...Y4(B)
  });
};
class Ri extends h4 {
  _parse(A) {
    let {
        ctx: B
      } = this._processInputParams(A),
      Q = B.data;
    if (B.parsedType === z2.undefined) Q = this._def.defaultValue();
    return this._def.innerType._parse({
      data: Q,
      path: B.path,
      parent: B
    });
  }
  removeDefault() {
    return this._def.innerType;
  }
}
Ri.create = (A, B) => {
  return new Ri({
    innerType: A,
    typeName: eA.ZodDefault,
    defaultValue: typeof B.default === "function" ? B.default : () => B.default,
    ...Y4(B)
  });
};
class Oi extends h4 {
  _parse(A) {
    let {
        ctx: B
      } = this._processInputParams(A),
      Q = {
        ...B,
        common: {
          ...B.common,
          issues: []
        }
      },
      D = this._def.innerType._parse({
        data: Q.data,
        path: Q.path,
        parent: {
          ...Q
        }
      });
    if (Ki(D)) return D.then(Z => {
      return {
        status: "valid",
        value: Z.status === "valid" ? Z.value : this._def.catchValue({
          get error() {
            return new CV(Q.common.issues);
          },
          input: Q.data
        })
      };
    });else return {
      status: "valid",
      value: D.status === "valid" ? D.value : this._def.catchValue({
        get error() {
          return new CV(Q.common.issues);
        },
        input: Q.data
      })
    };
  }
  removeCatch() {
    return this._def.innerType;
  }
}
Oi.create = (A, B) => {
  return new Oi({
    innerType: A,
    typeName: eA.ZodCatch,
    catchValue: typeof B.catch === "function" ? B.catch : () => B.catch,
    ...Y4(B)
  });
};
class q91 extends h4 {
  _parse(A) {
    if (this._getType(A) !== z2.nan) {
      let Q = this._getOrReturnCtx(A);
      return g2(Q, {
        code: sA.invalid_type,
        expected: z2.nan,
        received: Q.parsedType
      }), e9;
    }
    return {
      status: "valid",
      value: A.data
    };
  }
}
q91.create = A => {
  return new q91({
    typeName: eA.ZodNaN,
    ...Y4(A)
  });
};
var S8Q = Symbol("zod_brand");
class XK1 extends h4 {
  _parse(A) {
    let {
        ctx: B
      } = this._processInputParams(A),
      Q = B.data;
    return this._def.type._parse({
      data: Q,
      path: B.path,
      parent: B
    });
  }
  unwrap() {
    return this._def.type;
  }
}
class N91 extends h4 {
  _parse(A) {
    let {
      status: B,
      ctx: Q
    } = this._processInputParams(A);
    if (Q.common.async) return (async () => {
      let Z = await this._def.in._parseAsync({
        data: Q.data,
        path: Q.path,
        parent: Q
      });
      if (Z.status === "aborted") return e9;
      if (Z.status === "dirty") return B.dirty(), Qh(Z.value);else return this._def.out._parseAsync({
        data: Z.value,
        path: Q.path,
        parent: Q
      });
    })();else {
      let D = this._def.in._parseSync({
        data: Q.data,
        path: Q.path,
        parent: Q
      });
      if (D.status === "aborted") return e9;
      if (D.status === "dirty") return B.dirty(), {
        status: "dirty",
        value: D.value
      };else return this._def.out._parseSync({
        data: D.value,
        path: Q.path,
        parent: Q
      });
    }
  }
  static create(A, B) {
    return new N91({
      in: A,
      out: B,
      typeName: eA.ZodPipeline
    });
  }
}
class Ti extends h4 {
  _parse(A) {
    let B = this._def.innerType._parse(A),
      Q = D => {
        if (Sy(D)) D.value = Object.freeze(D.value);
        return D;
      };
    return Ki(B) ? B.then(D => Q(D)) : Q(B);
  }
  unwrap() {
    return this._def.innerType;
  }
}
Ti.create = (A, B) => {
  return new Ti({
    innerType: A,
    typeName: eA.ZodReadonly,
    ...Y4(B)
  });
};
function EIA(A, B) {
  let Q = typeof A === "function" ? A(B) : typeof A === "string" ? {
    message: A
  } : A;
  return typeof Q === "string" ? {
    message: Q
  } : Q;
}
function NIA(A, B = {}, Q) {
  if (A) return Zh.create().superRefine((D, Z) => {
    let G = A(D);
    if (G instanceof Promise) return G.then(F => {
      if (!F) {
        let I = EIA(B, D),
          Y = I.fatal ?? Q ?? !0;
        Z.addIssue({
          code: "custom",
          ...I,
          fatal: Y
        });
      }
    });
    if (!G) {
      let F = EIA(B, D),
        I = F.fatal ?? Q ?? !0;
      Z.addIssue({
        code: "custom",
        ...F,
        fatal: I
      });
    }
    return;
  });
  return Zh.create();
}
var j8Q = {
    object: mD.lazycreate
  },
  eA;
var y8Q = (A, B = {
    message: `Input not instance of ${A.name}`
  }) => NIA(Q => Q instanceof A, B),
  WQ = Ew.create,
  Ih = yy.create,
  k8Q = q91.create,
  _8Q = ky.create,
  yN = Ei.create,
  x8Q = Dh.create,
  v8Q = E91.create,
  b8Q = Ui.create,
  f8Q = wi.create,
  h8Q = Zh.create,
  g8Q = jy.create,
  u8Q = SN.create,
  m8Q = U91.create,
  qw = Uw.create,
  aC = mD.create,
  d8Q = mD.strictCreate,
  VK1 = $i.create,
  c8Q = JK1.create,
  l8Q = qi.create,
  p8Q = jN.create,
  Pi = w91.create,
  i8Q = $91.create,
  n8Q = Gh.create,
  a8Q = zi.create,
  s8Q = Ni.create,
  r8Q = Li.create,
  Si = _y.create,
  o8Q = Mi.create,
  t8Q = Fh.create,
  e8Q = $w.create,
  A5Q = nC.create,
  B5Q = _O.create,
  Q5Q = $w.createWithPreprocess,
  D5Q = N91.create,
  Z5Q = () => WQ().optional(),
  G5Q = () => Ih().optional(),
  F5Q = () => yN().optional(),
  I5Q = {
    string: A => Ew.create({
      ...A,
      coerce: !0
    }),
    number: A => yy.create({
      ...A,
      coerce: !0
    }),
    boolean: A => Ei.create({
      ...A,
      coerce: !0
    }),
    bigint: A => ky.create({
      ...A,
      coerce: !0
    }),
    date: A => Dh.create({
      ...A,
      coerce: !0
    })
  };
var Y5Q = e9;
module.exports = {
  Ih,
  Pi,
  Si,
  VK1,
  WQ,
  aC,
  eA,
  g,
  nC,
  qw,
  yN
};