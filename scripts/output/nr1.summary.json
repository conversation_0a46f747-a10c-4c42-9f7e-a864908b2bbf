{"metadata": {"entryPoint": "nr1", "timestamp": "2025-08-10T12:42:03.658Z"}, "isolation": {"totalNodesInCluster": 94, "totalNodesInOriginal": 4863, "isolationRatio": "1.9%", "entryBoundarySize": 1, "entryBoundaryNodes": ["nr1"]}, "externalDependencies": {}, "statistics": {"averageDependenciesPerNode": "2.12", "maxDependencies": 80, "nodeWithMaxDependencies": "ZA1", "top10MostDependedOn": [{"node": "ZA1", "dependencies": 80}, {"node": "ER8", "dependencies": 79}, {"node": "RRB", "dependencies": 40}, {"node": "Ol3", "dependencies": 40}, {"node": "oF1", "dependencies": 40}, {"node": "KX3", "dependencies": 39}, {"node": "wO0", "dependencies": 39}, {"node": "xq8", "dependencies": 39}, {"node": "HuB", "dependencies": 39}, {"node": "kh1", "dependencies": 39}]}, "dependencyLayers": [{"level": 0, "nodes": ["nr1"], "count": 1}, {"level": 1, "nodes": ["J6", "L3Q", "M3Q", "O3Q", "P7", "b3Q", "cr1", "dK1", "dYA", "dr1", "gYA", "ir1", "jYA", "lYA", "lr1", "mYA", "pYA", "uK1", "v3Q", "vYA", "xYA", "y3Q"], "count": 22}, {"level": 2, "nodes": ["C3Q", "H3Q", "J3Q", "J6", "K3Q", "Lw", "N3Q", "P3Q", "P7", "R3Q", "RYA", "S3Q", "SYA", "T3Q", "U3Q", "V3Q", "W3Q", "X3Q", "ZD", "_3Q", "_YA", "cYA", "dr1", "f3Q", "fK1", "fYA", "g3Q", "gr1", "h3Q", "hYA", "j3Q", "k3Q", "mK1", "oC", "pr1", "q3Q", "uYA", "ui", "w3Q", "z3Q", "zh"], "count": 41}, {"level": 3, "nodes": ["$3Q", "A3Q", "B3Q", "D3Q", "E3Q", "F3Q", "G3Q", "J3Q", "LYA", "Lw", "MYA", "OYA", "PYA", "Q3Q", "T3Q", "TYA", "U3Q", "W3Q", "Y3Q", "Z3Q", "ZD", "_YA", "a5Q", "bYA", "e5Q", "f91", "fK1", "fYA", "gK1", "h91", "hK1", "hr1", "j3Q", "kYA", "mr1", "o5Q", "pr1", "r5Q", "s5Q", "t5Q", "uYA", "ui", "ur1", "x3Q", "yYA", "zh"], "count": 46}, {"level": 4, "nodes": ["$3Q", "B3Q", "E3Q", "F3Q", "I3Q", "OYA", "Y3Q", "bYA", "gK1", "h91", "ur1", "yYA"], "count": 12}], "nodesList": ["$3Q", "A3Q", "B3Q", "C3Q", "D3Q", "E3Q", "F3Q", "G3Q", "H3Q", "I3Q", "J3Q", "J6", "K3Q", "L3Q", "LYA", "Lw", "M3Q", "MYA", "N3Q", "O3Q", "OYA", "P3Q", "P7", "PYA", "Q3Q", "R3Q", "RYA", "S3Q", "SYA", "T3Q", "TYA", "U3Q", "V3Q", "W3Q", "X3Q", "Y3Q", "Z3Q", "ZD", "_3Q", "_YA", "a5Q", "b3Q", "bYA", "cYA", "cr1", "dK1", "dYA", "dr1", "e5Q", "f3Q", "f91", "fK1", "fYA", "g3Q", "gK1", "gYA", "gr1", "h3Q", "h91", "hK1", "hYA", "hr1", "ir1", "j3Q", "jYA", "k3Q", "kYA", "lYA", "lr1", "mK1", "mYA", "mr1", "nr1", "o5Q", "oC", "pYA", "pr1", "q3Q", "r5Q", "s5Q", "t5Q", "uK1", "uYA", "ui", "ur1", "v3Q", "vYA", "w3Q", "x3Q", "xYA", "y3Q", "yYA", "z3Q", "zh"]}