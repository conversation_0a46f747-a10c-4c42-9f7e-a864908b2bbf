/**
 * Isolated bundle for 'EH0'
 */

/**
 * Cleaned Main File
 */

const {
  HH0,
  Oy1,
  Px,
  Sy1,
  XH0,
  Z7B,
  _D1,
  fk6,
  hk6,
  uK,
  z$
} = require("./Sy1.isolated.js");
/**
 * Cleaned Main File
 */

class zH0 extends TransformStream {
  constructor({
    onError: A,
    onRetry: B,
    onComment: Q
  } = {}) {
    let D;
    super({
      start(Z) {
        D = Oy1({
          onEvent: G => {
            Z.enqueue(G);
          },
          onError(G) {
            A === "terminate" ? Z.error(G) : typeof A == "function" && A(G);
          },
          onRetry: B,
          onComment: Q
        });
      },
      transform(Z) {
        D.feed(Z);
      }
    });
  }
}
var nk6 = {
  initialReconnectionDelay: 1000,
  maxReconnectionDelay: 30000,
  reconnectionDelayGrowFactor: 1.5,
  maxRetries: 2
};
class jy1 extends Error {
  constructor(A, B) {
    super(`Streamable HTTP error: ${B}`);
    this.code = A;
  }
}
class EH0 {
  constructor(A, B) {
    var Q;
    this._url = A, this._resourceMetadataUrl = void 0, this._requestInit = B === null || B === void 0 ? void 0 : B.requestInit, this._authProvider = B === null || B === void 0 ? void 0 : B.authProvider, this._fetch = B === null || B === void 0 ? void 0 : B.fetch, this._sessionId = B === null || B === void 0 ? void 0 : B.sessionId, this._reconnectionOptions = (Q = B === null || B === void 0 ? void 0 : B.reconnectionOptions) !== null && Q !== void 0 ? Q : nk6;
  }
  async _authThenStart() {
    var A;
    if (!this._authProvider) throw new uK("No auth provider");
    let B;
    try {
      B = await z$(this._authProvider, {
        serverUrl: this._url,
        resourceMetadataUrl: this._resourceMetadataUrl
      });
    } catch (Q) {
      throw (A = this.onerror) === null || A === void 0 || A.call(this, Q), Q;
    }
    if (B !== "AUTHORIZED") throw new uK();
    return await this._startOrAuthSse({
      resumptionToken: void 0
    });
  }
  async _commonHeaders() {
    var A;
    let B = {};
    if (this._authProvider) {
      let D = await this._authProvider.tokens();
      if (D) B.Authorization = `Bearer ${D.access_token}`;
    }
    if (this._sessionId) B["mcp-session-id"] = this._sessionId;
    if (this._protocolVersion) B["mcp-protocol-version"] = this._protocolVersion;
    let Q = this._normalizeHeaders((A = this._requestInit) === null || A === void 0 ? void 0 : A.headers);
    return new Headers({
      ...B,
      ...Q
    });
  }
  async _startOrAuthSse(A) {
    var B, Q, D;
    let {
      resumptionToken: Z
    } = A;
    try {
      let G = await this._commonHeaders();
      if (G.set("Accept", "text/event-stream"), Z) G.set("last-event-id", Z);
      let F = await ((B = this._fetch) !== null && B !== void 0 ? B : fetch)(this._url, {
        method: "GET",
        headers: G,
        signal: (Q = this._abortController) === null || Q === void 0 ? void 0 : Q.signal
      });
      if (!F.ok) {
        if (F.status === 401 && this._authProvider) return await this._authThenStart();
        if (F.status === 405) return;
        throw new jy1(F.status, `Failed to open SSE stream: ${F.statusText}`);
      }
      this._handleSseStream(F.body, A);
    } catch (G) {
      throw (D = this.onerror) === null || D === void 0 || D.call(this, G), G;
    }
  }
  _getNextReconnectionDelay(A) {
    let B = this._reconnectionOptions.initialReconnectionDelay,
      Q = this._reconnectionOptions.reconnectionDelayGrowFactor,
      D = this._reconnectionOptions.maxReconnectionDelay;
    return Math.min(B * Math.pow(Q, A), D);
  }
  _normalizeHeaders(A) {
    if (!A) return {};
    if (A instanceof Headers) return Object.fromEntries(A.entries());
    if (Array.isArray(A)) return Object.fromEntries(A);
    return {
      ...A
    };
  }
  _scheduleReconnection(A, B = 0) {
    var Q;
    let D = this._reconnectionOptions.maxRetries;
    if (D > 0 && B >= D) {
      (Q = this.onerror) === null || Q === void 0 || Q.call(this, new Error(`Maximum reconnection attempts (${D}) exceeded.`));
      return;
    }
    let Z = this._getNextReconnectionDelay(B);
    setTimeout(() => {
      this._startOrAuthSse(A).catch(G => {
        var F;
        (F = this.onerror) === null || F === void 0 || F.call(this, new Error(`Failed to reconnect SSE stream: ${G instanceof Error ? G.message : String(G)}`)), this._scheduleReconnection(A, B + 1);
      });
    }, Z);
  }
  _handleSseStream(A, B) {
    if (!A) return;
    let {
        onresumptiontoken: Q,
        replayMessageId: D
      } = B,
      Z;
    (async () => {
      var F, I, Y, W;
      try {
        let J = A.pipeThrough(new TextDecoderStream()).pipeThrough(new zH0()).getReader();
        while (!0) {
          let {
            value: X,
            done: V
          } = await J.read();
          if (V) break;
          if (X.id) Z = X.id, Q === null || Q === void 0 || Q(X.id);
          if (!X.event || X.event === "message") try {
            let C = OM.parse(JSON.parse(X.data));
            if (D !== void 0 && qD1(C)) C.id = D;
            (F = this.onmessage) === null || F === void 0 || F.call(this, C);
          } catch (C) {
            (I = this.onerror) === null || I === void 0 || I.call(this, C);
          }
        }
      } catch (J) {
        if ((Y = this.onerror) === null || Y === void 0 || Y.call(this, new Error(`SSE stream disconnected: ${J}`)), this._abortController && !this._abortController.signal.aborted) {
          if (Z !== void 0) try {
            this._scheduleReconnection({
              resumptionToken: Z,
              onresumptiontoken: Q,
              replayMessageId: D
            }, 0);
          } catch (X) {
            (W = this.onerror) === null || W === void 0 || W.call(this, new Error(`Failed to reconnect: ${X instanceof Error ? X.message : String(X)}`));
          }
        }
      }
    })();
  }
  async start() {
    if (this._abortController) throw new Error("StreamableHTTPClientTransport already started! If using Client class, note that connect() calls start() automatically.");
    this._abortController = new AbortController();
  }
  async finishAuth(A) {
    if (!this._authProvider) throw new uK("No auth provider");
    if ((await z$(this._authProvider, {
      serverUrl: this._url,
      authorizationCode: A,
      resourceMetadataUrl: this._resourceMetadataUrl
    })) !== "AUTHORIZED") throw new uK("Failed to authorize");
  }
  async close() {
    var A, B;
    (A = this._abortController) === null || A === void 0 || A.abort(), (B = this.onclose) === null || B === void 0 || B.call(this);
  }
  async send(A, B) {
    var Q, D, Z, G;
    try {
      let {
        resumptionToken: F,
        onresumptiontoken: I
      } = B || {};
      if (F) {
        this._startOrAuthSse({
          resumptionToken: F,
          replayMessageId: tj1(A) ? A.id : void 0
        }).catch(H => {
          var z;
          return (z = this.onerror) === null || z === void 0 ? void 0 : z.call(this, H);
        });
        return;
      }
      let Y = await this._commonHeaders();
      Y.set("content-type", "application/json"), Y.set("accept", "application/json, text/event-stream");
      let W = {
          ...this._requestInit,
          method: "POST",
          headers: Y,
          body: JSON.stringify(A),
          signal: (Q = this._abortController) === null || Q === void 0 ? void 0 : Q.signal
        },
        J = await ((D = this._fetch) !== null && D !== void 0 ? D : fetch)(this._url, W),
        X = J.headers.get("mcp-session-id");
      if (X) this._sessionId = X;
      if (!J.ok) {
        if (J.status === 401 && this._authProvider) {
          if (this._resourceMetadataUrl = _D1(J), (await z$(this._authProvider, {
            serverUrl: this._url,
            resourceMetadataUrl: this._resourceMetadataUrl
          })) !== "AUTHORIZED") throw new uK();
          return this.send(A);
        }
        let H = await J.text().catch(() => null);
        throw new Error(`Error POSTing to endpoint (HTTP ${J.status}): ${H}`);
      }
      if (J.status === 202) {
        if (n8B(A)) this._startOrAuthSse({
          resumptionToken: void 0
        }).catch(H => {
          var z;
          return (z = this.onerror) === null || z === void 0 ? void 0 : z.call(this, H);
        });
        return;
      }
      let C = (Array.isArray(A) ? A : [A]).filter(H => "method" in H && "id" in H && H.id !== void 0).length > 0,
        K = J.headers.get("content-type");
      if (C) if (K === null || K === void 0 ? void 0 : K.includes("text/event-stream")) this._handleSseStream(J.body, {
        onresumptiontoken: I
      });else if (K === null || K === void 0 ? void 0 : K.includes("application/json")) {
        let H = await J.json(),
          z = Array.isArray(H) ? H.map($ => OM.parse($)) : [OM.parse(H)];
        for (let $ of z) (Z = this.onmessage) === null || Z === void 0 || Z.call(this, $);
      } else throw new jy1(-1, `Unexpected content type: ${K}`);
    } catch (F) {
      throw (G = this.onerror) === null || G === void 0 || G.call(this, F), F;
    }
  }
  get sessionId() {
    return this._sessionId;
  }
  async terminateSession() {
    var A, B, Q;
    if (!this._sessionId) return;
    try {
      let D = await this._commonHeaders(),
        Z = {
          ...this._requestInit,
          method: "DELETE",
          headers: D,
          signal: (A = this._abortController) === null || A === void 0 ? void 0 : A.signal
        },
        G = await ((B = this._fetch) !== null && B !== void 0 ? B : fetch)(this._url, Z);
      if (!G.ok && G.status !== 405) throw new jy1(G.status, `Failed to terminate session: ${G.statusText}`);
      this._sessionId = void 0;
    } catch (D) {
      throw (Q = this.onerror) === null || Q === void 0 || Q.call(this, D), D;
    }
  }
  setProtocolVersion(A) {
    this._protocolVersion = A;
  }
  get protocolVersion() {
    return this._protocolVersion;
  }
}
module.exports = {
  EH0,
  z$
};