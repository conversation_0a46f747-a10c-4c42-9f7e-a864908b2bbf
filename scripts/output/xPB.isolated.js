/**
 * Isolated bundle for 'xPB'
 */

function xPB(A) {
  let B = null,
    Q = [];
  async function D() {
    if (!B) B = (async () => {
      let {
          processImage: G
        } = jF8("./image-processor.node"),
        F = await G(A);
      for (let I of Q) I(F);
      return F;
    })();
    return B;
  }
  let Z = {
    async metadata() {
      return (await D()).metadata();
    },
    resize(G, F, I) {
      return Q.push(Y => {
        Y.resize(G, F, I);
      }), Z;
    },
    jpeg(G) {
      return Q.push(F => {
        F.jpeg(G?.quality);
      }), Z;
    },
    png(G) {
      return Q.push(F => {
        F.png(G);
      }), Z;
    },
    webp(G) {
      return Q.push(F => {
        F.webp(G?.quality);
      }), Z;
    },
    async toBuffer() {
      return (await D()).toBuffer();
    }
  };
  return Z;
}
var jF8, yF8;
module.exports = {
  jF8,
  xPB
};