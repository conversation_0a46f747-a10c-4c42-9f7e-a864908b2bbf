/**
 * Claude Code - MCP (Model Context Protocol) System
 * 
 * 重构来源: cli-format-all.js 第309900-315741行 MCP协议实现
 * 重构时间: 2025-01-19
 * 
 * MCP系统的主入口，整合客户端、管理器和诊断功能
 * 使用官方MCP TypeScript SDK替换内置实现
 */

// 导入MCP模块
const { MCPClientWrapper, MCP_CLIENT_STATES, MCP_TRANSPORT_TYPES } = require('./mcp-client');
const { MCPManager, createMCPManager, getGlobalMCPManager } = require('./mcp-manager');
const { MCPDiagnosticsManager, getGlobalDiagnosticsManager, DIAGNOSTIC_SEVERITY } = require('./mcp-diagnostics');

// 导入官方MCP SDK类型（用于类型检查和文档）
// const { Client } = require('@modelcontextprotocol/sdk/client/index.js');
// const { StdioClientTransport } = require('@modelcontextprotocol/sdk/client/stdio.js');
// const { SSEClientTransport } = require('@modelcontextprotocol/sdk/client/sse.js');

/**
 * MCP系统配置选项
 */
const MCP_CONFIG_DEFAULTS = {
  timeout: 60000,
  retryAttempts: 3,
  retryDelay: 1000,
  enableDiagnostics: true,
  enableLogging: true
};

/**
 * 统一MCP系统类
 * 整合所有MCP功能的高级接口
 */
class MCPSystem {
  constructor(options = {}) {
    this.options = {
      ...MCP_CONFIG_DEFAULTS,
      ...options
    };
    
    this.manager = createMCPManager();
    this.diagnostics = getGlobalDiagnosticsManager();
    this.isInitialized = false;
  }

  /**
   * 初始化MCP系统
   * 
   * @param {Object} config - MCP服务器配置
   * @param {boolean} skipDisabled - 是否跳过禁用的服务器
   * @returns {Promise<void>}
   */
  async initialize(config = null, skipDisabled = false) {
    try {
      // 初始化管理器
      await this.manager.initialize(config, skipDisabled);
      
      // 如果启用诊断，初始化诊断管理器
      if (this.options.enableDiagnostics) {
        const ideClient = this.manager.getClient('ide');
        if (ideClient && ideClient.isConnected()) {
          this.diagnostics.initialize(ideClient);
        }
      }
      
      this.isInitialized = true;
      
    } catch (error) {
      throw new Error(`Failed to initialize MCP system: ${error.message}`);
    }
  }

  /**
   * 关闭MCP系统
   * 
   * @returns {Promise<void>}
   */
  async shutdown() {
    try {
      await this.manager.shutdown();
      await this.diagnostics.shutdown();
      this.isInitialized = false;
    } catch (error) {
      throw new Error(`Failed to shutdown MCP system: ${error.message}`);
    }
  }

  /**
   * 调用工具
   * 
   * @param {string} serverName - 服务器名称
   * @param {string} toolName - 工具名称
   * @param {Object} args - 工具参数
   * @param {Object} options - 调用选项
   * @returns {Promise<any>} 工具结果
   */
  async callTool(serverName, toolName, args = {}, options = {}) {
    if (!this.isInitialized) {
      throw new Error('MCP system not initialized');
    }

    const {
      signal = null,
      isNonInteractive = false,
      timeout = this.options.timeout
    } = options;

    return await this.manager.callTool(serverName, toolName, args, signal, isNonInteractive);
  }

  /**
   * 获取所有可用工具
   * 
   * @param {string} serverName - 服务器名称（可选）
   * @returns {Array} 工具列表
   */
  getAvailableTools(serverName = null) {
    return this.manager.getTools(serverName);
  }

  /**
   * 获取所有可用资源
   * 
   * @param {string} serverName - 服务器名称（可选）
   * @returns {Array} 资源列表
   */
  getAvailableResources(serverName = null) {
    return this.manager.getResources(serverName);
  }

  /**
   * 获取所有可用提示
   * 
   * @param {string} serverName - 服务器名称（可选）
   * @returns {Array} 提示列表
   */
  getAvailablePrompts(serverName = null) {
    return this.manager.getPrompts(serverName);
  }

  /**
   * 读取资源
   * 
   * @param {string} serverName - 服务器名称
   * @param {string} uri - 资源URI
   * @returns {Promise<any>} 资源内容
   */
  async readResource(serverName, uri) {
    if (!this.isInitialized) {
      throw new Error('MCP system not initialized');
    }

    return await this.manager.readResource(serverName, uri);
  }

  /**
   * 获取提示
   * 
   * @param {string} serverName - 服务器名称
   * @param {string} name - 提示名称
   * @param {Object} arguments - 提示参数
   * @returns {Promise<any>} 提示内容
   */
  async getPrompt(serverName, name, arguments = {}) {
    if (!this.isInitialized) {
      throw new Error('MCP system not initialized');
    }

    return await this.manager.getPrompt(serverName, name, arguments);
  }

  /**
   * 获取新的诊断信息
   * 
   * @returns {Promise<Array>} 新的诊断信息列表
   */
  async getNewDiagnostics() {
    if (!this.options.enableDiagnostics) {
      return [];
    }

    return await this.diagnostics.getNewDiagnostics();
  }

  /**
   * 文件编辑前设置诊断基线
   * 
   * @param {string} filePath - 文件路径
   * @returns {Promise<void>}
   */
  async beforeFileEdited(filePath) {
    if (!this.options.enableDiagnostics) {
      return;
    }

    await this.diagnostics.beforeFileEdited(filePath);
  }

  /**
   * 确保文件已打开
   * 
   * @param {string} filePath - 文件路径
   * @returns {Promise<void>}
   */
  async ensureFileOpened(filePath) {
    if (!this.options.enableDiagnostics) {
      return;
    }

    await this.diagnostics.ensureFileOpened(filePath);
  }

  /**
   * 获取服务器状态
   * 
   * @param {string} serverName - 服务器名称（可选）
   * @returns {Object} 服务器状态信息
   */
  getServerStatus(serverName = null) {
    if (serverName) {
      return this.manager.getServerInfo(serverName);
    }
    return this.manager.getAllServerInfo();
  }

  /**
   * 重新连接服务器
   * 
   * @param {string} serverName - 服务器名称
   * @returns {Promise<void>}
   */
  async reconnectServer(serverName) {
    if (!this.isInitialized) {
      throw new Error('MCP system not initialized');
    }

    await this.manager.reconnectServer(serverName);
  }

  /**
   * 获取系统统计信息
   * 
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      manager: this.manager.getStats(),
      diagnostics: this.diagnostics.getStats(),
      isInitialized: this.isInitialized,
      options: this.options
    };
  }

  /**
   * 检查系统是否就绪
   * 
   * @returns {boolean} 是否就绪
   */
  isReady() {
    return this.isInitialized && this.manager.isReady();
  }

  /**
   * 监听系统事件
   * 
   * @param {string} event - 事件名称
   * @param {Function} listener - 事件监听器
   */
  on(event, listener) {
    this.manager.on(event, listener);
  }

  /**
   * 移除事件监听器
   * 
   * @param {string} event - 事件名称
   * @param {Function} listener - 事件监听器
   */
  off(event, listener) {
    this.manager.off(event, listener);
  }

  /**
   * 触发事件
   * 
   * @param {string} event - 事件名称
   * @param {...any} args - 事件参数
   */
  emit(event, ...args) {
    this.manager.emit(event, ...args);
  }
}

/**
 * 创建MCP系统实例
 * 
 * @param {Object} options - 系统选项
 * @returns {MCPSystem} MCP系统实例
 */
function createMCPSystem(options = {}) {
  return new MCPSystem(options);
}

// 全局MCP系统实例
let globalMCPSystem = null;

/**
 * 获取全局MCP系统实例
 * 
 * @param {Object} options - 系统选项（仅在首次创建时使用）
 * @returns {MCPSystem} 全局MCP系统实例
 */
function getGlobalMCPSystem(options = {}) {
  if (!globalMCPSystem) {
    globalMCPSystem = createMCPSystem(options);
  }
  return globalMCPSystem;
}

/**
 * MCP工具函数集合
 */
const MCPUtils = {
  // 格式化诊断摘要
  formatDiagnosticsSummary: MCPDiagnosticsManager.formatDiagnosticsSummary,
  
  // 获取严重程度符号
  getSeveritySymbol: MCPDiagnosticsManager.getSeveritySymbol,
  
  // 验证传输类型
  isValidTransportType: (type) => Object.values(MCP_TRANSPORT_TYPES).includes(type),
  
  // 验证客户端状态
  isValidClientState: (state) => Object.values(MCP_CLIENT_STATES).includes(state),
  
  // 创建工具调用选项
  createCallOptions: (options = {}) => ({
    signal: null,
    isNonInteractive: false,
    timeout: MCP_CONFIG_DEFAULTS.timeout,
    ...options
  }),
  
  // 解析服务器配置
  parseServerConfig: (config) => {
    if (!config || typeof config !== 'object') {
      throw new Error('Invalid server config');
    }
    
    const type = config.type || MCP_TRANSPORT_TYPES.STDIO;
    
    if (!MCPUtils.isValidTransportType(type)) {
      throw new Error(`Invalid transport type: ${type}`);
    }
    
    return {
      type,
      ...config
    };
  }
};

module.exports = {
  // 主要类和函数
  MCPSystem,
  createMCPSystem,
  getGlobalMCPSystem,
  
  // 核心组件
  MCPClientWrapper,
  MCPManager,
  MCPDiagnosticsManager,
  
  // 管理器实例获取函数
  createMCPManager,
  getGlobalMCPManager,
  getGlobalDiagnosticsManager,
  
  // 工具函数
  MCPUtils,
  
  // 常量
  MCP_CLIENT_STATES,
  MCP_TRANSPORT_TYPES,
  DIAGNOSTIC_SEVERITY,
  MCP_CONFIG_DEFAULTS
};
