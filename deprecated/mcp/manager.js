/**
 * MCP (Model Context Protocol) 管理器
 * 处理MCP服务器的配置和管理
 * 对应原始代码中的MCP相关函数
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

const VALID_SCOPES = ['local', 'user', 'project'];
const VALID_TRANSPORTS = ['stdio', 'sse', 'http'];

/**
 * 验证配置范围
 * 原始函数名: hl
 */
function validateScope(scope) {
    if (!VALID_SCOPES.includes(scope)) {
        throw new Error(`Invalid scope: ${scope}. Must be one of: ${VALID_SCOPES.join(', ')}`);
    }
    return scope;
}

/**
 * 获取MCP配置文件路径
 */
function getMcpConfigPath(scope) {
    switch (scope) {
        case 'local':
            return path.join(process.cwd(), '.mcp.json');
        case 'user':
            return path.join(os.homedir(), '.claude-code', 'mcp.json');
        case 'project':
            return path.join(process.cwd(), '.mcp.json');
        default:
            throw new Error(`Invalid scope: ${scope}`);
    }
}

/**
 * 读取MCP配置
 */
function readMcpConfig(scope) {
    const configPath = getMcpConfigPath(scope);
    
    try {
        if (fs.existsSync(configPath)) {
            const content = fs.readFileSync(configPath, 'utf8');
            const config = JSON.parse(content);
            return config.mcpServers || {};
        }
    } catch (error) {
        console.warn(`Warning: Failed to read MCP config from ${configPath}:`, error.message);
    }
    
    return {};
}

/**
 * 写入MCP配置
 */
function writeMcpConfig(servers, scope) {
    const configPath = getMcpConfigPath(scope);
    const configDir = path.dirname(configPath);
    
    try {
        // 确保配置目录存在
        if (!fs.existsSync(configDir)) {
            fs.mkdirSync(configDir, { recursive: true });
        }
        
        // 读取现有配置
        let config = {};
        if (fs.existsSync(configPath)) {
            const content = fs.readFileSync(configPath, 'utf8');
            config = JSON.parse(content);
        }
        
        // 更新MCP服务器配置
        config.mcpServers = servers;
        
        fs.writeFileSync(configPath, JSON.stringify(config, null, 2), 'utf8');
    } catch (error) {
        throw new Error(`Failed to write MCP config to ${configPath}: ${error.message}`);
    }
}

/**
 * 保存服务器配置
 * 原始函数名: SP
 */
function saveServerConfig(name, config, scope) {
    validateScope(scope);
    
    const servers = readMcpConfig(scope);
    servers[name] = config;
    writeMcpConfig(servers, scope);
}

/**
 * 移除服务器配置
 */
function removeServerConfig(name, scope = null) {
    if (scope) {
        // 从指定scope移除
        validateScope(scope);
        const servers = readMcpConfig(scope);
        if (servers[name]) {
            delete servers[name];
            writeMcpConfig(servers, scope);
            return true;
        }
        return false;
    } else {
        // 从所有scope中查找并移除
        for (const s of VALID_SCOPES) {
            const servers = readMcpConfig(s);
            if (servers[name]) {
                delete servers[name];
                writeMcpConfig(servers, s);
                return true;
            }
        }
        return false;
    }
}

/**
 * 列出所有服务器
 * 原始函数名: UV
 */
function listServers() {
    const allServers = {};
    
    // 合并所有scope的配置，用户级别覆盖全局，本地覆盖用户
    for (const scope of ['user', 'local', 'project']) {
        const servers = readMcpConfig(scope);
        Object.assign(allServers, servers);
    }
    
    return allServers;
}

/**
 * 获取特定服务器配置
 */
function getServerConfig(name) {
    const allServers = listServers();
    return allServers[name] || null;
}

/**
 * 解析环境变量
 * 原始函数名: cc0
 */
function parseEnvironmentVariables(envArray) {
    if (!envArray || !Array.isArray(envArray)) {
        return {};
    }
    
    const env = {};
    for (const envVar of envArray) {
        const [key, ...valueParts] = envVar.split('=');
        if (key && valueParts.length > 0) {
            env[key] = valueParts.join('=');
        }
    }
    
    return env;
}

/**
 * 解析JSON配置
 * 原始函数名: S4
 */
function parseJsonConfig(jsonString) {
    try {
        const config = JSON.parse(jsonString);
        
        // 验证配置格式
        if (typeof config !== 'object' || config === null) {
            throw new Error('Configuration must be an object');
        }
        
        // 验证必需字段
        if (config.type === 'stdio') {
            if (!config.command) {
                throw new Error('stdio servers must have a command');
            }
        } else if (config.type === 'sse' || config.type === 'http') {
            if (!config.url) {
                throw new Error('sse and http servers must have a url');
            }
        }
        
        return config;
    } catch (error) {
        throw new Error(`Invalid JSON configuration: ${error.message}`);
    }
}

/**
 * 从Claude Desktop导入配置
 * 原始函数名: vQB
 */
function importFromClaudeDesktop() {
    // TODO: 实现从Claude Desktop配置文件导入的逻辑
    // 这需要读取Claude Desktop的配置文件位置
    
    const claudeDesktopPaths = [
        path.join(os.homedir(), 'Library', 'Application Support', 'Claude', 'claude_desktop_config.json'), // macOS
        path.join(os.homedir(), '.config', 'Claude', 'claude_desktop_config.json'), // Linux
        // Windows路径可能不同
    ];
    
    for (const configPath of claudeDesktopPaths) {
        try {
            if (fs.existsSync(configPath)) {
                const content = fs.readFileSync(configPath, 'utf8');
                const config = JSON.parse(content);
                return config.mcpServers || {};
            }
        } catch (error) {
            console.warn(`Warning: Failed to read Claude Desktop config from ${configPath}:`, error.message);
        }
    }
    
    return {};
}

/**
 * 重置项目选择
 */
function resetProjectChoices() {
    // TODO: 实现重置项目级别MCP服务器选择的逻辑
    // 原始函数涉及到 D9(), S6() 等函数
    
    console.log('Resetting project-scoped MCP server choices...');
    
    // 这里需要清除项目级别的启用/禁用服务器列表
    const projectConfigPath = getMcpConfigPath('project');
    
    try {
        if (fs.existsSync(projectConfigPath)) {
            const content = fs.readFileSync(projectConfigPath, 'utf8');
            const config = JSON.parse(content);
            
            // 重置相关字段
            config.enabledMcpjsonServers = [];
            config.disabledMcpjsonServers = [];
            config.enableAllProjectMcpServers = false;
            
            fs.writeFileSync(projectConfigPath, JSON.stringify(config, null, 2), 'utf8');
        }
    } catch (error) {
        console.warn('Warning: Failed to reset project choices:', error.message);
    }
}

module.exports = {
    validateScope,
    saveServerConfig,
    removeServerConfig,
    listServers,
    getServerConfig,
    parseEnvironmentVariables,
    parseJsonConfig,
    importFromClaudeDesktop,
    resetProjectChoices,
    readMcpConfig,
    writeMcpConfig
};
