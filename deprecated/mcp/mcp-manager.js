/**
 * Claude Code - MCP Manager
 * 
 * 重构来源: cli-format-all.js 第314000-315741行 MCP管理相关函数
 * 重构时间: 2025-01-19
 * 
 * MCP客户端管理器，处理多个MCP服务器的连接和管理
 */

// 导入MCP客户端
const { MCPClientWrapper, MCP_CLIENT_STATES, MCP_TRANSPORT_TYPES } = require('./mcp-client');

// Node.js 内置模块
const { EventEmitter } = require('events');

// TODO_REFACTOR: 以下是未重构的依赖，需要后续替换

// TODO_REFACTOR: 查找UV配置获取函数定义
const getConfig = () => {
  throw new Error('Config function not yet refactored (original: UV)');
};

// TODO_REFACTOR: 查找E1事件追踪函数定义
const trackEvent = (eventName, data) => {
  throw new Error('Event tracking function not yet refactored (original: E1)');
};

// TODO_REFACTOR: 查找_2日志函数定义
const logInfo = (server, message) => {
  throw new Error('Info log function not yet refactored (original: _2)');
};

// TODO_REFACTOR: 查找lI错误日志函数定义
const logError = (server, message) => {
  throw new Error('Error log function not yet refactored (original: lI)');
};

// TODO_REFACTOR: 查找v1错误处理函数定义
const handleError = (error) => {
  throw new Error('Error handler function not yet refactored (original: v1)');
};

/**
 * MCP管理器类
 * 重构自: 第314000-315741行的MCP管理相关函数
 */
class MCPManager extends EventEmitter {
  constructor() {
    super();
    this.clients = new Map();
    this.tools = new Map();
    this.resources = new Map();
    this.prompts = new Map();
    this.isInitialized = false;
  }

  /**
   * 初始化MCP管理器
   * 
   * @param {Object} config - 配置对象
   * @param {boolean} skipDisabled - 是否跳过禁用的服务器
   * @returns {Promise<void>}
   */
  async initialize(config = null, skipDisabled = false) {
    try {
      const mcpConfig = config || getConfig();
      
      if (!mcpConfig || Object.keys(mcpConfig).length === 0) {
        logInfo('mcp-manager', 'No MCP servers configured');
        this.isInitialized = true;
        return;
      }

      const serverEntries = Object.entries(mcpConfig);
      const stats = {
        totalServers: serverEntries.length,
        stdioCount: serverEntries.filter(([, config]) => config.type === MCP_TRANSPORT_TYPES.STDIO).length,
        sseCount: serverEntries.filter(([, config]) => config.type === MCP_TRANSPORT_TYPES.SSE).length,
        httpCount: serverEntries.filter(([, config]) => config.type === MCP_TRANSPORT_TYPES.HTTP).length,
        sseIdeCount: serverEntries.filter(([, config]) => config.type === MCP_TRANSPORT_TYPES.SSE_IDE).length
      };

      // 并行连接所有服务器
      const connectionPromises = serverEntries.map(([name, serverConfig]) => 
        this._connectServer(name, serverConfig, stats)
      );

      await Promise.allSettled(connectionPromises);
      
      this.isInitialized = true;
      this.emit('initialized', this.getStats());
      
      logInfo('mcp-manager', `MCP Manager initialized with ${this.clients.size} servers`);
      
    } catch (error) {
      handleError(error);
      throw error;
    }
  }

  /**
   * 连接单个服务器
   * 
   * @param {string} name - 服务器名称
   * @param {Object} config - 服务器配置
   * @param {Object} stats - 统计信息
   * @returns {Promise<void>}
   * @private
   */
  async _connectServer(name, config, stats) {
    try {
      logInfo(name, `Connecting to MCP server: ${name}`);
      
      const client = new MCPClientWrapper(name, config);
      
      // 监听客户端事件
      client.on('stateChange', (state) => {
        this.emit('clientStateChange', { name, state });
      });
      
      client.on('error', (error) => {
        logError(name, `Client error: ${error.message}`);
        this.emit('clientError', { name, error });
      });

      // 连接客户端
      await client.connect();
      
      // 存储客户端
      this.clients.set(name, client);
      
      // 加载工具、资源和提示
      await this._loadClientCapabilities(name, client);
      
      // 发送连接成功事件
      trackEvent('tengu_mcp_server_connection_success', {
        serverName: name,
        transport: config.type,
        ...stats
      });
      
      logInfo(name, `Successfully connected to MCP server: ${name}`);
      
    } catch (error) {
      logError(name, `Failed to connect to MCP server: ${error.message}`);
      
      // 发送连接失败事件
      trackEvent('tengu_mcp_server_connection_failed', {
        serverName: name,
        transport: config.type,
        error: error.message,
        ...stats
      });
      
      // 不抛出错误，允许其他服务器继续连接
    }
  }

  /**
   * 加载客户端能力
   * 
   * @param {string} name - 服务器名称
   * @param {MCPClientWrapper} client - 客户端实例
   * @returns {Promise<void>}
   * @private
   */
  async _loadClientCapabilities(name, client) {
    try {
      // 加载工具
      const tools = await client.listTools();
      tools.forEach(tool => {
        this.tools.set(`${name}:${tool.name}`, {
          serverName: name,
          client,
          ...tool
        });
      });

      // 加载资源
      const resources = await client.listResources();
      resources.forEach(resource => {
        this.resources.set(`${name}:${resource.uri}`, {
          serverName: name,
          client,
          ...resource
        });
      });

      // 加载提示
      const prompts = await client.listPrompts();
      prompts.forEach(prompt => {
        this.prompts.set(`${name}:${prompt.name}`, {
          serverName: name,
          client,
          ...prompt
        });
      });

    } catch (error) {
      logError(name, `Failed to load capabilities: ${error.message}`);
    }
  }

  /**
   * 断开所有连接
   * 
   * @returns {Promise<void>}
   */
  async shutdown() {
    try {
      const disconnectPromises = Array.from(this.clients.values()).map(client => 
        client.disconnect().catch(error => {
          logError(client.name, `Error during shutdown: ${error.message}`);
        })
      );

      await Promise.allSettled(disconnectPromises);
      
      this.clients.clear();
      this.tools.clear();
      this.resources.clear();
      this.prompts.clear();
      this.isInitialized = false;
      
      this.emit('shutdown');
      logInfo('mcp-manager', 'MCP Manager shutdown complete');
      
    } catch (error) {
      handleError(error);
    }
  }

  /**
   * 获取客户端
   * 
   * @param {string} name - 服务器名称
   * @returns {MCPClientWrapper|null} 客户端实例
   */
  getClient(name) {
    return this.clients.get(name) || null;
  }

  /**
   * 获取所有客户端
   * 
   * @returns {Array<MCPClientWrapper>} 客户端列表
   */
  getAllClients() {
    return Array.from(this.clients.values());
  }

  /**
   * 获取已连接的客户端
   * 
   * @returns {Array<MCPClientWrapper>} 已连接的客户端列表
   */
  getConnectedClients() {
    return this.getAllClients().filter(client => client.isConnected());
  }

  /**
   * 调用工具
   * 
   * @param {string} serverName - 服务器名称
   * @param {string} toolName - 工具名称
   * @param {Object} args - 工具参数
   * @param {AbortSignal} signal - 中止信号
   * @param {boolean} isNonInteractive - 是否为非交互模式
   * @returns {Promise<any>} 工具结果
   */
  async callTool(serverName, toolName, args = {}, signal = null, isNonInteractive = false) {
    const client = this.getClient(serverName);
    if (!client) {
      throw new Error(`MCP server not found: ${serverName}`);
    }

    return await client.callTool(toolName, args, signal, isNonInteractive);
  }

  /**
   * 获取工具列表
   * 
   * @param {string} serverName - 服务器名称（可选）
   * @returns {Array} 工具列表
   */
  getTools(serverName = null) {
    if (serverName) {
      return Array.from(this.tools.values()).filter(tool => tool.serverName === serverName);
    }
    return Array.from(this.tools.values());
  }

  /**
   * 获取资源列表
   * 
   * @param {string} serverName - 服务器名称（可选）
   * @returns {Array} 资源列表
   */
  getResources(serverName = null) {
    if (serverName) {
      return Array.from(this.resources.values()).filter(resource => resource.serverName === serverName);
    }
    return Array.from(this.resources.values());
  }

  /**
   * 获取提示列表
   * 
   * @param {string} serverName - 服务器名称（可选）
   * @returns {Array} 提示列表
   */
  getPrompts(serverName = null) {
    if (serverName) {
      return Array.from(this.prompts.values()).filter(prompt => prompt.serverName === serverName);
    }
    return Array.from(this.prompts.values());
  }

  /**
   * 读取资源
   * 
   * @param {string} serverName - 服务器名称
   * @param {string} uri - 资源URI
   * @returns {Promise<any>} 资源内容
   */
  async readResource(serverName, uri) {
    const client = this.getClient(serverName);
    if (!client) {
      throw new Error(`MCP server not found: ${serverName}`);
    }

    return await client.readResource(uri);
  }

  /**
   * 获取提示
   * 
   * @param {string} serverName - 服务器名称
   * @param {string} name - 提示名称
   * @param {Object} arguments - 提示参数
   * @returns {Promise<any>} 提示内容
   */
  async getPrompt(serverName, name, arguments = {}) {
    const client = this.getClient(serverName);
    if (!client) {
      throw new Error(`MCP server not found: ${serverName}`);
    }

    return await client.getPrompt(name, arguments);
  }

  /**
   * 重新连接服务器
   * 
   * @param {string} name - 服务器名称
   * @returns {Promise<void>}
   */
  async reconnectServer(name) {
    const client = this.getClient(name);
    if (client) {
      await client.disconnect();
      this.clients.delete(name);
    }

    const config = getConfig();
    const serverConfig = config[name];
    
    if (!serverConfig) {
      throw new Error(`Server configuration not found: ${name}`);
    }

    await this._connectServer(name, serverConfig, { totalServers: 1 });
  }

  /**
   * 获取统计信息
   * 
   * @returns {Object} 统计信息
   */
  getStats() {
    const clients = this.getAllClients();
    const connectedClients = this.getConnectedClients();
    
    return {
      totalServers: this.clients.size,
      connectedServers: connectedClients.length,
      totalTools: this.tools.size,
      totalResources: this.resources.size,
      totalPrompts: this.prompts.size,
      serverStates: Object.fromEntries(
        clients.map(client => [client.name, client.getState()])
      ),
      isInitialized: this.isInitialized
    };
  }

  /**
   * 检查是否已初始化
   * 
   * @returns {boolean} 是否已初始化
   */
  isReady() {
    return this.isInitialized;
  }

  /**
   * 获取服务器信息
   * 
   * @param {string} name - 服务器名称
   * @returns {Object|null} 服务器信息
   */
  getServerInfo(name) {
    const client = this.getClient(name);
    return client ? client.getServerInfo() : null;
  }

  /**
   * 获取所有服务器信息
   * 
   * @returns {Array} 服务器信息列表
   */
  getAllServerInfo() {
    return this.getAllClients().map(client => client.getServerInfo());
  }
}

/**
 * 创建MCP管理器实例
 * 
 * @returns {MCPManager} MCP管理器实例
 */
function createMCPManager() {
  return new MCPManager();
}

// 全局MCP管理器实例
let globalMCPManager = null;

/**
 * 获取全局MCP管理器实例
 * 
 * @returns {MCPManager} 全局MCP管理器实例
 */
function getGlobalMCPManager() {
  if (!globalMCPManager) {
    globalMCPManager = createMCPManager();
  }
  return globalMCPManager;
}

module.exports = {
  MCPManager,
  createMCPManager,
  getGlobalMCPManager,
  MCP_CLIENT_STATES,
  MCP_TRANSPORT_TYPES
};
