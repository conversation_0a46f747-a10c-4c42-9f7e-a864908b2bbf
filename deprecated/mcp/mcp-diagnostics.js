/**
 * Claude Code - MCP Diagnostics Manager
 * 
 * 重构来源: cli-format-all.js 第315563-315741行 MCP诊断管理
 * 重构时间: 2025-01-19
 * 
 * MCP诊断管理器，处理代码诊断和错误检测
 */

// TODO_REFACTOR: 以下是未重构的依赖，需要后续替换

// TODO_REFACTOR: 查找v1错误处理函数定义
const handleError = (error) => {
  throw new Error('Error handler function not yet refactored (original: v1)');
};

// TODO_REFACTOR: 查找VW客户端获取函数定义
const getClient = (name) => {
  throw new Error('Get client function not yet refactored (original: VW)');
};

// TODO_REFACTOR: 查找Jw工具调用函数定义
const callTool = async (toolName, args, client, isNonInteractive) => {
  throw new Error('Call tool function not yet refactored (original: Jw)');
};

// TODO_REFACTOR: 查找G0图标定义
const icons = {
  cross: '✗',
  warning: '⚠',
  info: 'ℹ',
  star: '★',
  bullet: '•'
};

/**
 * 诊断严重程度枚举
 */
const DIAGNOSTIC_SEVERITY = {
  ERROR: 'Error',
  WARNING: 'Warning',
  INFO: 'Info',
  HINT: 'Hint'
};

/**
 * MCP诊断管理器类
 * 重构自: 第315563-315741行的hE类
 */
class MCPDiagnosticsManager {
  constructor() {
    this.baseline = new Map();
    this.initialized = false;
    this.mcpClient = null;
    this.lastProcessedTimestamps = new Map();
    this.lastDiagnosticsByUri = new Map();
    this.rightFileDiagnosticsState = new Map();
  }

  /**
   * 获取单例实例
   * 
   * @returns {MCPDiagnosticsManager} 单例实例
   */
  static getInstance() {
    if (!MCPDiagnosticsManager.instance) {
      MCPDiagnosticsManager.instance = new MCPDiagnosticsManager();
    }
    return MCPDiagnosticsManager.instance;
  }

  /**
   * 初始化诊断管理器
   * 
   * @param {Object} mcpClient - MCP客户端
   */
  initialize(mcpClient) {
    if (this.initialized) {
      return;
    }

    this.mcpClient = mcpClient;
    this.initialized = true;

    // 如果客户端已连接，设置诊断变更通知处理器
    if (this.mcpClient && this.mcpClient.type === 'connected') {
      this._setupDiagnosticNotifications();
    }
  }

  /**
   * 设置诊断变更通知处理器
   * 
   * @private
   */
  _setupDiagnosticNotifications() {
    // 使用官方MCP SDK的通知处理器
    const notificationSchema = {
      method: 'diagnostics_changed',
      params: {
        uri: 'string'
      }
    };

    this.mcpClient.client.setNotificationHandler(notificationSchema, async (notification) => {
      const { uri } = notification.params;
      this.handleDiagnosticChange(uri);
    });
  }

  /**
   * 关闭诊断管理器
   * 
   * @returns {Promise<void>}
   */
  async shutdown() {
    this.initialized = false;
    this.baseline.clear();
    this.lastProcessedTimestamps.clear();
    this.lastDiagnosticsByUri.clear();
    this.rightFileDiagnosticsState.clear();
    this.mcpClient = null;
  }

  /**
   * 重置诊断状态
   */
  reset() {
    this.baseline.clear();
    this.rightFileDiagnosticsState.clear();
    this.lastProcessedTimestamps.clear();
    this.lastDiagnosticsByUri.clear();
  }

  /**
   * 标准化文件URI
   * 
   * @param {string} uri - 文件URI
   * @returns {string} 标准化的文件路径
   */
  normalizeFileUri(uri) {
    const prefixes = ['file://', '_claude_fs_right:', '_claude_fs_left:'];
    
    for (const prefix of prefixes) {
      if (uri.startsWith(prefix)) {
        return uri.slice(prefix.length);
      }
    }
    
    return uri;
  }

  /**
   * 确保文件已打开
   * 
   * @param {string} filePath - 文件路径
   * @returns {Promise<void>}
   */
  async ensureFileOpened(filePath) {
    if (!this.initialized || !this.mcpClient || this.mcpClient.type !== 'connected') {
      return;
    }

    try {
      await callTool('openFile', {
        filePath,
        preview: false,
        startText: '',
        endText: '',
        selectToEndOfLine: false,
        makeFrontmost: false
      }, this.mcpClient, false);
    } catch (error) {
      handleError(error);
    }
  }

  /**
   * 文件编辑前的诊断基线设置
   * 
   * @param {string} filePath - 文件路径
   * @returns {Promise<void>}
   */
  async beforeFileEdited(filePath) {
    if (!this.initialized || !this.mcpClient || this.mcpClient.type !== 'connected') {
      return;
    }

    const timestamp = Date.now();

    try {
      const result = await callTool('getDiagnostics', {
        uri: `file://${filePath}`
      }, this.mcpClient, false);

      const diagnostics = this.parseDiagnosticResult(result);
      const firstDiagnostic = diagnostics[0];

      if (firstDiagnostic) {
        const normalizedPath = this.normalizeFileUri(firstDiagnostic.uri);
        
        if (filePath !== normalizedPath) {
          handleError(new Error(`Diagnostics file path mismatch: expected ${filePath}, got ${firstDiagnostic.uri})`));
          return;
        }

        this.baseline.set(filePath, firstDiagnostic.diagnostics);
        this.lastProcessedTimestamps.set(filePath, timestamp);
      } else {
        this.baseline.set(filePath, []);
        this.lastProcessedTimestamps.set(filePath, timestamp);
      }
    } catch (error) {
      // 静默处理错误，不影响编辑流程
    }
  }

  /**
   * 获取新的诊断信息
   * 
   * @returns {Promise<Array>} 新的诊断信息列表
   */
  async getNewDiagnostics() {
    if (!this.initialized || !this.mcpClient || this.mcpClient.type !== 'connected') {
      return [];
    }

    let allDiagnostics = [];

    try {
      const result = await callTool('getDiagnostics', {}, this.mcpClient, false);
      allDiagnostics = this.parseDiagnosticResult(result);
    } catch (error) {
      return [];
    }

    // 过滤出有基线的文件诊断
    const leftFileDiagnostics = allDiagnostics
      .filter(diagnostic => this.baseline.has(this.normalizeFileUri(diagnostic.uri)))
      .filter(diagnostic => diagnostic.uri.startsWith('file://'));

    // 处理右侧文件诊断
    const rightFileDiagnosticsMap = new Map();
    allDiagnostics
      .filter(diagnostic => this.baseline.has(this.normalizeFileUri(diagnostic.uri)))
      .filter(diagnostic => diagnostic.uri.startsWith('_claude_fs_right:'))
      .forEach(diagnostic => {
        rightFileDiagnosticsMap.set(this.normalizeFileUri(diagnostic.uri), diagnostic);
      });

    const newDiagnostics = [];

    for (const diagnostic of leftFileDiagnostics) {
      const normalizedPath = this.normalizeFileUri(diagnostic.uri);
      const baseline = this.baseline.get(normalizedPath) || [];
      const rightDiagnostic = rightFileDiagnosticsMap.get(normalizedPath);
      
      let currentDiagnostic = diagnostic;

      // 如果有右侧诊断，检查是否需要更新
      if (rightDiagnostic) {
        const lastRightState = this.rightFileDiagnosticsState.get(normalizedPath);
        
        if (!lastRightState || !this.areDiagnosticArraysEqual(lastRightState, rightDiagnostic.diagnostics)) {
          currentDiagnostic = rightDiagnostic;
        }
        
        this.rightFileDiagnosticsState.set(normalizedPath, rightDiagnostic.diagnostics);
      }

      // 找出新的诊断（不在基线中的）
      const newDiagnosticItems = currentDiagnostic.diagnostics.filter(item => 
        !baseline.some(baselineItem => this.areDiagnosticsEqual(item, baselineItem))
      );

      if (newDiagnosticItems.length > 0) {
        newDiagnostics.push({
          uri: diagnostic.uri,
          diagnostics: newDiagnosticItems
        });
      }

      // 更新基线
      this.baseline.set(normalizedPath, currentDiagnostic.diagnostics);
    }

    return newDiagnostics;
  }

  /**
   * 解析诊断结果
   * 
   * @param {any} result - 诊断结果
   * @returns {Array} 解析后的诊断列表
   */
  parseDiagnosticResult(result) {
    if (Array.isArray(result)) {
      const textResult = result.find(item => item.type === 'text');
      if (textResult && 'text' in textResult) {
        return JSON.parse(textResult.text);
      }
    }
    return [];
  }

  /**
   * 比较两个诊断是否相等
   * 
   * @param {Object} a - 诊断A
   * @param {Object} b - 诊断B
   * @returns {boolean} 是否相等
   */
  areDiagnosticsEqual(a, b) {
    return (
      a.message === b.message &&
      a.severity === b.severity &&
      a.source === b.source &&
      a.code === b.code &&
      a.range.start.line === b.range.start.line &&
      a.range.start.character === b.range.start.character &&
      a.range.end.line === b.range.end.line &&
      a.range.end.character === b.range.end.character
    );
  }

  /**
   * 比较两个诊断数组是否相等
   * 
   * @param {Array} a - 诊断数组A
   * @param {Array} b - 诊断数组B
   * @returns {boolean} 是否相等
   */
  areDiagnosticArraysEqual(a, b) {
    if (a.length !== b.length) {
      return false;
    }

    return (
      a.every(itemA => b.some(itemB => this.areDiagnosticsEqual(itemA, itemB))) &&
      b.every(itemB => a.some(itemA => this.areDiagnosticsEqual(itemA, itemB)))
    );
  }

  /**
   * 检查是否为代码检查工具的诊断
   * 
   * @param {Object} diagnostic - 诊断对象
   * @returns {boolean} 是否为代码检查工具诊断
   */
  isLinterDiagnostic(diagnostic) {
    const linterSources = [
      'eslint', 'eslint-plugin', 'tslint', 'prettier', 'stylelint', 'jshint',
      'standardjs', 'xo', 'rome', 'biome', 'deno-lint', 'rubocop', 'pylint',
      'flake8', 'black', 'ruff', 'clippy', 'rustfmt', 'golangci-lint',
      'gofmt', 'swiftlint', 'detekt', 'ktlint', 'checkstyle', 'pmd',
      'sonarqube', 'sonarjs'
    ];

    if (!diagnostic.source) {
      return false;
    }

    const source = diagnostic.source.toLowerCase();
    return linterSources.some(linter => source.includes(linter));
  }

  /**
   * 处理诊断变更
   * 
   * @param {string} uri - 文件URI
   */
  handleDiagnosticChange(uri) {
    // 可以在这里添加诊断变更的处理逻辑
    // 例如：通知UI更新、触发重新分析等
  }

  /**
   * 处理查询开始
   * 
   * @param {string} serverName - 服务器名称
   * @returns {Promise<void>}
   */
  async handleQueryStart(serverName) {
    if (!this.initialized) {
      const client = getClient(serverName);
      if (client) {
        this.initialize(client);
      }
    } else {
      this.reset();
    }
  }

  /**
   * 格式化诊断摘要
   * 
   * @param {Array} diagnostics - 诊断列表
   * @returns {string} 格式化的诊断摘要
   */
  static formatDiagnosticsSummary(diagnostics) {
    return diagnostics.map(diagnostic => {
      const fileName = diagnostic.uri.split('/').pop() || diagnostic.uri;
      const diagnosticItems = diagnostic.diagnostics.map(item => {
        const symbol = MCPDiagnosticsManager.getSeveritySymbol(item.severity);
        const location = `[Line ${item.range.start.line + 1}:${item.range.start.character + 1}]`;
        const code = item.code ? ` [${item.code}]` : '';
        const source = item.source ? ` (${item.source})` : '';
        
        return `  ${symbol} ${location} ${item.message}${code}${source}`;
      }).join('\n');

      return `${fileName}:\n${diagnosticItems}`;
    }).join('\n\n');
  }

  /**
   * 获取严重程度对应的符号
   * 
   * @param {string} severity - 严重程度
   * @returns {string} 对应的符号
   */
  static getSeveritySymbol(severity) {
    const symbols = {
      [DIAGNOSTIC_SEVERITY.ERROR]: icons.cross,
      [DIAGNOSTIC_SEVERITY.WARNING]: icons.warning,
      [DIAGNOSTIC_SEVERITY.INFO]: icons.info,
      [DIAGNOSTIC_SEVERITY.HINT]: icons.star
    };

    return symbols[severity] || icons.bullet;
  }

  /**
   * 获取诊断统计信息
   * 
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      initialized: this.initialized,
      baselineFiles: this.baseline.size,
      processedTimestamps: this.lastProcessedTimestamps.size,
      rightFileStates: this.rightFileDiagnosticsState.size,
      hasClient: !!this.mcpClient
    };
  }
}

// 设置静态属性
MCPDiagnosticsManager.instance = null;

/**
 * 获取全局诊断管理器实例
 * 
 * @returns {MCPDiagnosticsManager} 全局诊断管理器实例
 */
function getGlobalDiagnosticsManager() {
  return MCPDiagnosticsManager.getInstance();
}

module.exports = {
  MCPDiagnosticsManager,
  getGlobalDiagnosticsManager,
  DIAGNOSTIC_SEVERITY
};
