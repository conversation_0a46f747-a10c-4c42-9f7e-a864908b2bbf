/**
 * Claude Code - MCP Client Implementation
 * 
 * 重构来源: cli-format-all.js 第309900-315741行 MCP协议实现
 * 重构时间: 2025-01-19
 * 
 * 使用官方MCP TypeScript SDK替换内置实现
 * 参考: https://github.com/modelcontextprotocol/typescript-sdk
 */

// 使用官方MCP SDK
const { Client } = require('@modelcontextprotocol/sdk/client/index.js');
const { StdioClientTransport } = require('@modelcontextprotocol/sdk/client/stdio.js');
const { SSEClientTransport } = require('@modelcontextprotocol/sdk/client/sse.js');

// Node.js 内置模块
const { spawn } = require('child_process');
const { EventEmitter } = require('events');

// TODO_REFACTOR: 以下是未重构的依赖，需要后续替换

// TODO_REFACTOR: 查找v1错误处理函数定义
const handleError = (error) => {
  throw new Error('Error handler function not yet refactored (original: v1)');
};

// TODO_REFACTOR: 查找_2日志函数定义
const logInfo = (server, message) => {
  throw new Error('Info log function not yet refactored (original: _2)');
};

// TODO_REFACTOR: 查找lI错误日志函数定义
const logError = (server, message) => {
  throw new Error('Error log function not yet refactored (original: lI)');
};

// TODO_REFACTOR: 查找E1事件追踪函数定义
const trackEvent = (eventName, data) => {
  throw new Error('Event tracking function not yet refactored (original: E1)');
};

// TODO_REFACTOR: 查找gs6超时获取函数定义
const getTimeout = () => {
  throw new Error('Timeout function not yet refactored (original: gs6)');
};

// TODO_REFACTOR: 查找n6A工具结果处理函数定义
const handleToolResult = (result, toolName, isNonInteractive) => {
  throw new Error('Tool result handler function not yet refactored (original: n6A)');
};

/**
 * MCP客户端状态枚举
 */
const MCP_CLIENT_STATES = {
  DISCONNECTED: 'disconnected',
  CONNECTING: 'connecting',
  CONNECTED: 'connected',
  ERROR: 'error',
  NEEDS_AUTH: 'needs-auth'
};

/**
 * MCP传输类型枚举
 */
const MCP_TRANSPORT_TYPES = {
  STDIO: 'stdio',
  SSE: 'sse',
  HTTP: 'http',
  SSE_IDE: 'sse-ide'
};

/**
 * MCP客户端包装器类
 * 重构自: 第309900-315741行的MCP协议实现
 */
class MCPClientWrapper extends EventEmitter {
  constructor(name, config) {
    super();
    this.name = name;
    this.config = config;
    this.client = null;
    this.transport = null;
    this.state = MCP_CLIENT_STATES.DISCONNECTED;
    this.process = null;
    this.capabilities = null;
    this.tools = [];
    this.resources = [];
    this.prompts = [];
  }

  /**
   * 连接到MCP服务器
   * 
   * @returns {Promise<void>}
   */
  async connect() {
    try {
      this.state = MCP_CLIENT_STATES.CONNECTING;
      this.emit('stateChange', this.state);

      // 创建传输层
      this.transport = await this._createTransport();
      
      // 创建客户端
      this.client = new Client({
        name: 'claude-code',
        version: '1.0.0'
      }, {
        capabilities: {
          roots: {
            listChanged: true
          },
          sampling: {}
        }
      });

      // 连接客户端
      await this.client.connect(this.transport);
      
      this.state = MCP_CLIENT_STATES.CONNECTED;
      this.emit('stateChange', this.state);
      
      // 获取服务器能力
      this.capabilities = this.client.getServerCapabilities();
      
      // 加载工具、资源和提示
      await this._loadServerCapabilities();
      
      logInfo(this.name, 'MCP client connected successfully');
      
    } catch (error) {
      this.state = MCP_CLIENT_STATES.ERROR;
      this.emit('stateChange', this.state);
      this.emit('error', error);
      
      logError(this.name, `Failed to connect: ${error.message}`);
      throw error;
    }
  }

  /**
   * 断开连接
   * 
   * @returns {Promise<void>}
   */
  async disconnect() {
    try {
      if (this.client) {
        await this.client.close();
        this.client = null;
      }
      
      if (this.transport) {
        await this.transport.close();
        this.transport = null;
      }
      
      if (this.process) {
        this.process.kill();
        this.process = null;
      }
      
      this.state = MCP_CLIENT_STATES.DISCONNECTED;
      this.emit('stateChange', this.state);
      
      logInfo(this.name, 'MCP client disconnected');
      
    } catch (error) {
      logError(this.name, `Error during disconnect: ${error.message}`);
    }
  }

  /**
   * 调用工具
   * 
   * @param {string} toolName - 工具名称
   * @param {Object} args - 工具参数
   * @param {AbortSignal} signal - 中止信号
   * @param {boolean} isNonInteractive - 是否为非交互模式
   * @returns {Promise<any>} 工具结果
   */
  async callTool(toolName, args = {}, signal = null, isNonInteractive = false) {
    if (!this.client || this.state !== MCP_CLIENT_STATES.CONNECTED) {
      throw new Error('MCP client not connected');
    }

    try {
      logInfo(this.name, `Calling MCP tool: ${toolName}`);
      
      const result = await this.client.callTool({
        name: toolName,
        arguments: args
      }, {
        signal,
        timeout: getTimeout()
      });

      if (result.isError) {
        const errorMessage = this._extractErrorMessage(result);
        logError(this.name, errorMessage);
        throw new Error(errorMessage);
      }

      logInfo(this.name, `Tool call succeeded: ${JSON.stringify(result)}`);
      
      // 处理工具结果
      let processedResult;
      if ('toolResult' in result) {
        processedResult = String(result.toolResult);
      } else if ('content' in result && Array.isArray(result.content)) {
        processedResult = this._processContent(result.content);
      } else {
        throw new Error(`Unexpected response format from tool ${toolName}`);
      }

      // 处理工具结果（如果不是IDE工具）
      if (this.name !== 'ide') {
        await handleToolResult(processedResult, toolName, isNonInteractive);
      }

      return processedResult;

    } catch (error) {
      if (error.name === 'AbortError') {
        throw error;
      }
      
      logError(this.name, `Tool call failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取工具列表
   * 
   * @returns {Promise<Array>} 工具列表
   */
  async listTools() {
    if (!this.client || this.state !== MCP_CLIENT_STATES.CONNECTED) {
      throw new Error('MCP client not connected');
    }

    try {
      const result = await this.client.listTools();
      this.tools = result.tools || [];
      return this.tools;
    } catch (error) {
      logError(this.name, `Failed to list tools: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取资源列表
   * 
   * @returns {Promise<Array>} 资源列表
   */
  async listResources() {
    if (!this.client || this.state !== MCP_CLIENT_STATES.CONNECTED) {
      throw new Error('MCP client not connected');
    }

    try {
      const result = await this.client.listResources();
      this.resources = result.resources || [];
      return this.resources;
    } catch (error) {
      logError(this.name, `Failed to list resources: ${error.message}`);
      throw error;
    }
  }

  /**
   * 读取资源
   * 
   * @param {string} uri - 资源URI
   * @returns {Promise<any>} 资源内容
   */
  async readResource(uri) {
    if (!this.client || this.state !== MCP_CLIENT_STATES.CONNECTED) {
      throw new Error('MCP client not connected');
    }

    try {
      const result = await this.client.readResource({ uri });
      return result.contents;
    } catch (error) {
      logError(this.name, `Failed to read resource: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取提示列表
   * 
   * @returns {Promise<Array>} 提示列表
   */
  async listPrompts() {
    if (!this.client || this.state !== MCP_CLIENT_STATES.CONNECTED) {
      throw new Error('MCP client not connected');
    }

    try {
      const result = await this.client.listPrompts();
      this.prompts = result.prompts || [];
      return this.prompts;
    } catch (error) {
      logError(this.name, `Failed to list prompts: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取提示
   * 
   * @param {string} name - 提示名称
   * @param {Object} arguments - 提示参数
   * @returns {Promise<any>} 提示内容
   */
  async getPrompt(name, arguments = {}) {
    if (!this.client || this.state !== MCP_CLIENT_STATES.CONNECTED) {
      throw new Error('MCP client not connected');
    }

    try {
      const result = await this.client.getPrompt({ name, arguments });
      return result;
    } catch (error) {
      logError(this.name, `Failed to get prompt: ${error.message}`);
      throw error;
    }
  }

  /**
   * 创建传输层
   * 
   * @returns {Promise<Transport>} 传输层实例
   * @private
   */
  async _createTransport() {
    switch (this.config.type) {
      case MCP_TRANSPORT_TYPES.STDIO:
        return this._createStdioTransport();
      
      case MCP_TRANSPORT_TYPES.SSE:
      case MCP_TRANSPORT_TYPES.SSE_IDE:
        return this._createSSETransport();
      
      default:
        throw new Error(`Unsupported transport type: ${this.config.type}`);
    }
  }

  /**
   * 创建STDIO传输层
   * 
   * @returns {Promise<StdioClientTransport>} STDIO传输层
   * @private
   */
  async _createStdioTransport() {
    const args = Array.isArray(this.config.args) ? this.config.args : [];
    const env = { ...process.env, ...this.config.env };

    this.process = spawn(this.config.command, args, {
      stdio: ['pipe', 'pipe', 'pipe'],
      env
    });

    // 监听进程错误
    this.process.on('error', (error) => {
      logError(this.name, `Process error: ${error.message}`);
      this.emit('error', error);
    });

    this.process.on('exit', (code) => {
      logInfo(this.name, `Process exited with code: ${code}`);
      this.state = MCP_CLIENT_STATES.DISCONNECTED;
      this.emit('stateChange', this.state);
    });

    // 监听stderr输出
    this.process.stderr?.on('data', (data) => {
      const message = data.toString().trim();
      if (message) {
        logError(this.name, `Process stderr: ${message}`);
      }
    });

    return new StdioClientTransport({
      readable: this.process.stdout,
      writable: this.process.stdin
    });
  }

  /**
   * 创建SSE传输层
   * 
   * @returns {Promise<SSEClientTransport>} SSE传输层
   * @private
   */
  async _createSSETransport() {
    const transport = new SSEClientTransport(this.config.url, {
      headers: this.config.headers
    });

    return transport;
  }

  /**
   * 加载服务器能力
   * 
   * @returns {Promise<void>}
   * @private
   */
  async _loadServerCapabilities() {
    const promises = [];

    // 加载工具
    if (this.capabilities?.tools) {
      promises.push(this.listTools().catch(error => {
        logError(this.name, `Failed to load tools: ${error.message}`);
      }));
    }

    // 加载资源
    if (this.capabilities?.resources) {
      promises.push(this.listResources().catch(error => {
        logError(this.name, `Failed to load resources: ${error.message}`);
      }));
    }

    // 加载提示
    if (this.capabilities?.prompts) {
      promises.push(this.listPrompts().catch(error => {
        logError(this.name, `Failed to load prompts: ${error.message}`);
      }));
    }

    await Promise.all(promises);
  }

  /**
   * 提取错误消息
   * 
   * @param {Object} result - 错误结果
   * @returns {string} 错误消息
   * @private
   */
  _extractErrorMessage(result) {
    if ('content' in result && Array.isArray(result.content) && result.content.length > 0) {
      const firstContent = result.content[0];
      if (firstContent && typeof firstContent === 'object' && 'text' in firstContent) {
        return firstContent.text;
      }
    }
    
    if ('error' in result) {
      return String(result.error);
    }
    
    return 'Unknown error';
  }

  /**
   * 处理内容数组
   * 
   * @param {Array} content - 内容数组
   * @returns {Array} 处理后的内容
   * @private
   */
  _processContent(content) {
    return content.map(item => this._processContentItem(item)).flat();
  }

  /**
   * 处理单个内容项
   * 
   * @param {Object} item - 内容项
   * @returns {Array} 处理后的内容项
   * @private
   */
  _processContentItem(item) {
    switch (item.type) {
      case 'text':
        return [{
          type: 'text',
          text: item.text
        }];
      
      case 'image':
        return [{
          type: 'image',
          source: {
            data: String(item.data),
            media_type: item.mimeType || 'image/jpeg',
            type: 'base64'
          }
        }];
      
      case 'resource':
        return this._processResourceContent(item);
      
      case 'resource_link':
        return this._processResourceLink(item);
      
      default:
        return [];
    }
  }

  /**
   * 处理资源内容
   * 
   * @param {Object} item - 资源项
   * @returns {Array} 处理后的内容
   * @private
   */
  _processResourceContent(item) {
    const resource = item.resource;
    const prefix = `[Resource from ${this.name} at ${resource.uri}] `;
    
    if ('text' in resource) {
      return [{
        type: 'text',
        text: `${prefix}${resource.text}`
      }];
    } else if ('blob' in resource) {
      // 检查是否为图片类型
      const imageTypes = new Set(['image/jpeg', 'image/png', 'image/gif', 'image/webp']);
      
      if (imageTypes.has(resource.mimeType)) {
        const result = [];
        if (prefix) {
          result.push({
            type: 'text',
            text: prefix
          });
        }
        result.push({
          type: 'image',
          source: {
            data: resource.blob,
            media_type: resource.mimeType || 'image/jpeg',
            type: 'base64'
          }
        });
        return result;
      } else {
        return [{
          type: 'text',
          text: `${prefix}Base64 data (${resource.mimeType || 'unknown type'}) ${resource.blob}`
        }];
      }
    }
    
    return [];
  }

  /**
   * 处理资源链接
   * 
   * @param {Object} item - 资源链接项
   * @returns {Array} 处理后的内容
   * @private
   */
  _processResourceLink(item) {
    let text = `[Resource link: ${item.name}] ${item.uri}`;
    if (item.description) {
      text += ` (${item.description})`;
    }
    
    return [{
      type: 'text',
      text
    }];
  }

  /**
   * 获取客户端状态
   * 
   * @returns {string} 客户端状态
   */
  getState() {
    return this.state;
  }

  /**
   * 检查是否已连接
   * 
   * @returns {boolean} 是否已连接
   */
  isConnected() {
    return this.state === MCP_CLIENT_STATES.CONNECTED;
  }

  /**
   * 获取服务器信息
   * 
   * @returns {Object} 服务器信息
   */
  getServerInfo() {
    return {
      name: this.name,
      config: this.config,
      state: this.state,
      capabilities: this.capabilities,
      toolsCount: this.tools.length,
      resourcesCount: this.resources.length,
      promptsCount: this.prompts.length
    };
  }
}

module.exports = {
  MCPClientWrapper,
  MCP_CLIENT_STATES,
  MCP_TRANSPORT_TYPES
};
