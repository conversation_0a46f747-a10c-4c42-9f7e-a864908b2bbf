/**
 * Claude Code - Code Highlighter Component
 * 
 * 重构来源: cli-format-all.js 第315747-315769行 代码高亮组件
 * 重构时间: 2025-01-19
 * 
 * 代码语法高亮组件，支持多种编程语言
 */

// React相关
const React = require('react');
const { useMemo } = React;

// TODO_REFACTOR: 以下是未重构的依赖，需要后续替换

// TODO_REFACTOR: 查找u4A语法高亮库定义
const highlightLibrary = {
  supportsLanguage: (language) => {
    throw new Error('Highlight library not yet refactored (original: al.supportsLanguage)');
  },
  highlight: (code, options) => {
    throw new Error('Highlight library not yet refactored (original: al.highlight)');
  }
};

// TODO_REFACTOR: 查找Xh文本清理函数定义
const cleanText = (text) => {
  throw new Error('Text cleaning function not yet refactored (original: Xh)');
};

// TODO_REFACTOR: 查找v1错误处理函数定义
const handleError = (error) => {
  throw new Error('Error handler function not yet refactored (original: v1)');
};

// TODO_REFACTOR: 查找S基础组件定义
const BaseComponent = (props) => {
  throw new Error('Base component not yet refactored (original: S)');
};

/**
 * 支持的编程语言列表
 */
const SUPPORTED_LANGUAGES = [
  'javascript', 'typescript', 'python', 'java', 'cpp', 'c', 'csharp',
  'go', 'rust', 'php', 'ruby', 'swift', 'kotlin', 'scala', 'clojure',
  'haskell', 'erlang', 'elixir', 'dart', 'lua', 'perl', 'r', 'matlab',
  'sql', 'html', 'css', 'scss', 'sass', 'less', 'xml', 'json', 'yaml',
  'toml', 'ini', 'dockerfile', 'bash', 'shell', 'powershell', 'batch',
  'makefile', 'cmake', 'gradle', 'maven', 'markdown', 'latex', 'tex'
];

/**
 * 语言别名映射
 */
const LANGUAGE_ALIASES = {
  'js': 'javascript',
  'ts': 'typescript',
  'py': 'python',
  'rb': 'ruby',
  'cs': 'csharp',
  'c++': 'cpp',
  'cc': 'cpp',
  'cxx': 'cpp',
  'hpp': 'cpp',
  'h': 'c',
  'sh': 'bash',
  'zsh': 'bash',
  'fish': 'bash',
  'ps1': 'powershell',
  'cmd': 'batch',
  'bat': 'batch',
  'yml': 'yaml',
  'md': 'markdown',
  'htm': 'html'
};

/**
 * 代码高亮组件
 * 重构自: 第315747-315769行的NW函数
 * 
 * @param {Object} props - 组件属性
 * @param {string} props.code - 要高亮的代码
 * @param {string} props.language - 编程语言
 * @param {Object} props.options - 高亮选项
 * @returns {React.Element} 高亮后的代码组件
 */
function CodeHighlighter({ code, language, options = {} }) {
  const highlightedCode = useMemo(() => {
    const cleanedCode = cleanText(code);
    const normalizedLanguage = normalizeLanguage(language);
    
    try {
      if (highlightLibrary.supportsLanguage(normalizedLanguage)) {
        return highlightLibrary.highlight(cleanedCode, {
          language: normalizedLanguage,
          ...options
        });
      } else {
        handleError(new Error(
          `Language not supported while highlighting code, falling back to markdown: ${normalizedLanguage}`
        ));
        return highlightLibrary.highlight(cleanedCode, {
          language: 'markdown',
          ...options
        });
      }
    } catch (error) {
      if (error instanceof Error && error.message.includes('Unknown language')) {
        handleError(new Error(
          `Language not supported while highlighting code, falling back to markdown: ${error.message}`
        ));
        return highlightLibrary.highlight(cleanedCode, {
          language: 'markdown',
          ...options
        });
      }
      throw error;
    }
  }, [code, language, options]);

  return React.createElement(BaseComponent, null, highlightedCode);
}

/**
 * 标准化语言名称
 * 
 * @param {string} language - 原始语言名称
 * @returns {string} 标准化后的语言名称
 */
function normalizeLanguage(language) {
  if (!language) {
    return 'text';
  }
  
  const lowercased = language.toLowerCase();
  return LANGUAGE_ALIASES[lowercased] || lowercased;
}

/**
 * 检查语言是否支持
 * 
 * @param {string} language - 语言名称
 * @returns {boolean} 是否支持
 */
function isLanguageSupported(language) {
  const normalized = normalizeLanguage(language);
  return SUPPORTED_LANGUAGES.includes(normalized) || highlightLibrary.supportsLanguage(normalized);
}

/**
 * 获取语言的显示名称
 * 
 * @param {string} language - 语言名称
 * @returns {string} 显示名称
 */
function getLanguageDisplayName(language) {
  const languageNames = {
    'javascript': 'JavaScript',
    'typescript': 'TypeScript',
    'python': 'Python',
    'java': 'Java',
    'cpp': 'C++',
    'c': 'C',
    'csharp': 'C#',
    'go': 'Go',
    'rust': 'Rust',
    'php': 'PHP',
    'ruby': 'Ruby',
    'swift': 'Swift',
    'kotlin': 'Kotlin',
    'scala': 'Scala',
    'clojure': 'Clojure',
    'haskell': 'Haskell',
    'erlang': 'Erlang',
    'elixir': 'Elixir',
    'dart': 'Dart',
    'lua': 'Lua',
    'perl': 'Perl',
    'r': 'R',
    'matlab': 'MATLAB',
    'sql': 'SQL',
    'html': 'HTML',
    'css': 'CSS',
    'scss': 'SCSS',
    'sass': 'Sass',
    'less': 'Less',
    'xml': 'XML',
    'json': 'JSON',
    'yaml': 'YAML',
    'toml': 'TOML',
    'ini': 'INI',
    'dockerfile': 'Dockerfile',
    'bash': 'Bash',
    'shell': 'Shell',
    'powershell': 'PowerShell',
    'batch': 'Batch',
    'makefile': 'Makefile',
    'cmake': 'CMake',
    'gradle': 'Gradle',
    'maven': 'Maven',
    'markdown': 'Markdown',
    'latex': 'LaTeX',
    'tex': 'TeX'
  };
  
  const normalized = normalizeLanguage(language);
  return languageNames[normalized] || normalized.charAt(0).toUpperCase() + normalized.slice(1);
}

/**
 * 代码块组件
 * 
 * @param {Object} props - 组件属性
 * @param {string} props.code - 代码内容
 * @param {string} props.language - 编程语言
 * @param {boolean} props.showLineNumbers - 是否显示行号
 * @param {boolean} props.showLanguage - 是否显示语言标签
 * @param {Object} props.style - 自定义样式
 * @returns {React.Element} 代码块组件
 */
function CodeBlock({ 
  code, 
  language, 
  showLineNumbers = false, 
  showLanguage = true,
  style = {},
  ...props 
}) {
  const displayLanguage = getLanguageDisplayName(language);
  
  return React.createElement('div', {
    className: 'code-block',
    style: {
      border: '1px solid #e1e4e8',
      borderRadius: '6px',
      overflow: 'hidden',
      ...style
    },
    ...props
  }, [
    showLanguage && React.createElement('div', {
      key: 'header',
      className: 'code-block-header',
      style: {
        backgroundColor: '#f6f8fa',
        padding: '8px 12px',
        borderBottom: '1px solid #e1e4e8',
        fontSize: '12px',
        fontWeight: 'bold',
        color: '#586069'
      }
    }, displayLanguage),
    
    React.createElement('div', {
      key: 'content',
      className: 'code-block-content',
      style: {
        padding: '12px',
        backgroundColor: '#ffffff',
        overflow: 'auto'
      }
    }, React.createElement(CodeHighlighter, {
      code,
      language,
      options: {
        showLineNumbers
      }
    }))
  ]);
}

/**
 * 内联代码组件
 * 
 * @param {Object} props - 组件属性
 * @param {string} props.code - 代码内容
 * @param {string} props.language - 编程语言（可选）
 * @returns {React.Element} 内联代码组件
 */
function InlineCode({ code, language, ...props }) {
  return React.createElement('code', {
    className: 'inline-code',
    style: {
      backgroundColor: '#f6f8fa',
      padding: '2px 4px',
      borderRadius: '3px',
      fontSize: '85%',
      fontFamily: 'SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace'
    },
    ...props
  }, language ? React.createElement(CodeHighlighter, { code, language }) : code);
}

/**
 * 代码高亮工具函数
 */
const CodeHighlightUtils = {
  // 标准化语言名称
  normalizeLanguage,
  
  // 检查语言支持
  isLanguageSupported,
  
  // 获取显示名称
  getLanguageDisplayName,
  
  // 获取支持的语言列表
  getSupportedLanguages: () => [...SUPPORTED_LANGUAGES],
  
  // 获取语言别名
  getLanguageAliases: () => ({ ...LANGUAGE_ALIASES }),
  
  // 从文件扩展名推断语言
  inferLanguageFromExtension: (filename) => {
    if (!filename) return 'text';
    
    const ext = filename.split('.').pop()?.toLowerCase();
    if (!ext) return 'text';
    
    const extensionMap = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'py': 'python',
      'pyw': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'cxx': 'cpp',
      'cc': 'cpp',
      'c': 'c',
      'h': 'c',
      'hpp': 'cpp',
      'cs': 'csharp',
      'go': 'go',
      'rs': 'rust',
      'php': 'php',
      'rb': 'ruby',
      'swift': 'swift',
      'kt': 'kotlin',
      'scala': 'scala',
      'clj': 'clojure',
      'hs': 'haskell',
      'erl': 'erlang',
      'ex': 'elixir',
      'dart': 'dart',
      'lua': 'lua',
      'pl': 'perl',
      'r': 'r',
      'm': 'matlab',
      'sql': 'sql',
      'html': 'html',
      'htm': 'html',
      'css': 'css',
      'scss': 'scss',
      'sass': 'sass',
      'less': 'less',
      'xml': 'xml',
      'json': 'json',
      'yaml': 'yaml',
      'yml': 'yaml',
      'toml': 'toml',
      'ini': 'ini',
      'sh': 'bash',
      'bash': 'bash',
      'zsh': 'bash',
      'fish': 'bash',
      'ps1': 'powershell',
      'cmd': 'batch',
      'bat': 'batch',
      'md': 'markdown',
      'tex': 'latex',
      'dockerfile': 'dockerfile'
    };
    
    return extensionMap[ext] || 'text';
  }
};

module.exports = {
  CodeHighlighter,
  CodeBlock,
  InlineCode,
  CodeHighlightUtils,
  SUPPORTED_LANGUAGES,
  LANGUAGE_ALIASES
};
