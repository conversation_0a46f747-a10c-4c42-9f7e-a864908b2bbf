/**
 * Claude Code - Image Processor
 * 
 * 重构来源: cli-format-all.js 第315771-315793行 图像处理功能
 * 重构时间: 2025-01-19
 * 
 * 图像处理和优化功能，支持多种图像格式
 */

// TODO_REFACTOR: 以下是未重构的依赖，需要后续替换

// TODO_REFACTOR: 查找bz平台检测函数定义
const isPlatformSupported = () => {
  throw new Error('Platform detection function not yet refactored (original: bz)');
};

// TODO_REFACTOR: 查找qp2和Np2原生图像处理器定义
const nativeImageProcessor = {
  load: async () => {
    throw new Error('Native image processor not yet refactored (original: qp2, Np2)');
  }
};

// TODO_REFACTOR: 查找lE1 Sharp库定义
const sharpLibrary = {
  load: async () => {
    throw new Error('Sharp library not yet refactored (original: lE1)');
  }
};

/**
 * 支持的图像格式
 */
const SUPPORTED_IMAGE_FORMATS = [
  'jpeg', 'jpg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'tif', 'svg', 'ico'
];

/**
 * 图像MIME类型映射
 */
const IMAGE_MIME_TYPES = {
  'jpeg': 'image/jpeg',
  'jpg': 'image/jpeg',
  'png': 'image/png',
  'gif': 'image/gif',
  'webp': 'image/webp',
  'bmp': 'image/bmp',
  'tiff': 'image/tiff',
  'tif': 'image/tiff',
  'svg': 'image/svg+xml',
  'ico': 'image/x-icon'
};

/**
 * 图像处理器缓存
 */
let imageProcessorCache = null;

/**
 * 获取图像处理器
 * 重构自: 第315772-315793行的$p2函数
 * 
 * @returns {Promise<Object>} 图像处理器实例
 */
async function getImageProcessor() {
  if (imageProcessorCache) {
    return imageProcessorCache.default;
  }

  // 优先尝试使用原生图像处理器
  if (isPlatformSupported()) {
    try {
      const nativeProcessor = await nativeImageProcessor.load();
      const processor = nativeProcessor.sharp || nativeProcessor.default;
      
      imageProcessorCache = {
        default: processor
      };
      
      return processor;
    } catch (error) {
      console.warn('Native image processor not available, falling back to sharp');
    }
  }

  // 回退到Sharp库
  try {
    const sharpModule = await sharpLibrary.load();
    const processor = sharpModule?.default || sharpModule;
    
    imageProcessorCache = {
      default: processor
    };
    
    return processor;
  } catch (error) {
    throw new Error(`Failed to load image processor: ${error.message}`);
  }
}

/**
 * 图像处理选项
 */
const DEFAULT_PROCESSING_OPTIONS = {
  quality: 80,
  progressive: true,
  optimizeScans: true,
  mozjpeg: true
};

/**
 * 图像处理器类
 */
class ImageProcessor {
  constructor() {
    this.processor = null;
    this.initialized = false;
  }

  /**
   * 初始化图像处理器
   * 
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) {
      return;
    }

    this.processor = await getImageProcessor();
    this.initialized = true;
  }

  /**
   * 处理图像
   * 
   * @param {Buffer|string} input - 输入图像数据或路径
   * @param {Object} options - 处理选项
   * @returns {Promise<Buffer>} 处理后的图像数据
   */
  async processImage(input, options = {}) {
    await this.initialize();

    const processingOptions = {
      ...DEFAULT_PROCESSING_OPTIONS,
      ...options
    };

    let sharp = this.processor(input);

    // 调整大小
    if (options.width || options.height) {
      sharp = sharp.resize(options.width, options.height, {
        fit: options.fit || 'inside',
        withoutEnlargement: options.withoutEnlargement !== false
      });
    }

    // 格式转换
    if (options.format) {
      switch (options.format.toLowerCase()) {
        case 'jpeg':
        case 'jpg':
          sharp = sharp.jpeg({
            quality: processingOptions.quality,
            progressive: processingOptions.progressive,
            optimizeScans: processingOptions.optimizeScans,
            mozjpeg: processingOptions.mozjpeg
          });
          break;
        
        case 'png':
          sharp = sharp.png({
            quality: processingOptions.quality,
            progressive: processingOptions.progressive
          });
          break;
        
        case 'webp':
          sharp = sharp.webp({
            quality: processingOptions.quality
          });
          break;
        
        default:
          throw new Error(`Unsupported output format: ${options.format}`);
      }
    }

    return await sharp.toBuffer();
  }

  /**
   * 获取图像信息
   * 
   * @param {Buffer|string} input - 输入图像数据或路径
   * @returns {Promise<Object>} 图像信息
   */
  async getImageInfo(input) {
    await this.initialize();

    const metadata = await this.processor(input).metadata();
    
    return {
      width: metadata.width,
      height: metadata.height,
      format: metadata.format,
      size: metadata.size,
      density: metadata.density,
      hasAlpha: metadata.hasAlpha,
      channels: metadata.channels,
      colorspace: metadata.space
    };
  }

  /**
   * 压缩图像
   * 
   * @param {Buffer|string} input - 输入图像数据或路径
   * @param {Object} options - 压缩选项
   * @returns {Promise<Buffer>} 压缩后的图像数据
   */
  async compressImage(input, options = {}) {
    const compressionOptions = {
      quality: options.quality || 80,
      format: options.format || 'jpeg',
      ...options
    };

    return await this.processImage(input, compressionOptions);
  }

  /**
   * 调整图像大小
   * 
   * @param {Buffer|string} input - 输入图像数据或路径
   * @param {number} width - 目标宽度
   * @param {number} height - 目标高度
   * @param {Object} options - 调整选项
   * @returns {Promise<Buffer>} 调整后的图像数据
   */
  async resizeImage(input, width, height, options = {}) {
    return await this.processImage(input, {
      width,
      height,
      ...options
    });
  }

  /**
   * 转换图像格式
   * 
   * @param {Buffer|string} input - 输入图像数据或路径
   * @param {string} format - 目标格式
   * @param {Object} options - 转换选项
   * @returns {Promise<Buffer>} 转换后的图像数据
   */
  async convertFormat(input, format, options = {}) {
    return await this.processImage(input, {
      format,
      ...options
    });
  }

  /**
   * 生成缩略图
   * 
   * @param {Buffer|string} input - 输入图像数据或路径
   * @param {number} size - 缩略图大小
   * @param {Object} options - 生成选项
   * @returns {Promise<Buffer>} 缩略图数据
   */
  async generateThumbnail(input, size = 150, options = {}) {
    return await this.processImage(input, {
      width: size,
      height: size,
      fit: 'cover',
      format: 'jpeg',
      quality: 70,
      ...options
    });
  }
}

/**
 * 图像工具函数
 */
const ImageUtils = {
  /**
   * 检查是否为图像文件
   * 
   * @param {string} filename - 文件名
   * @returns {boolean} 是否为图像文件
   */
  isImageFile(filename) {
    if (!filename) return false;
    
    const ext = filename.split('.').pop()?.toLowerCase();
    return ext ? SUPPORTED_IMAGE_FORMATS.includes(ext) : false;
  },

  /**
   * 获取图像MIME类型
   * 
   * @param {string} filename - 文件名
   * @returns {string} MIME类型
   */
  getImageMimeType(filename) {
    if (!filename) return 'application/octet-stream';
    
    const ext = filename.split('.').pop()?.toLowerCase();
    return ext ? IMAGE_MIME_TYPES[ext] || 'application/octet-stream' : 'application/octet-stream';
  },

  /**
   * 检查是否为Base64图像数据
   * 
   * @param {string} data - 数据字符串
   * @returns {boolean} 是否为Base64图像数据
   */
  isBase64Image(data) {
    return /^data:image\/[a-z0-9.+_-]+;base64,/i.test(data);
  },

  /**
   * 解析Base64图像数据
   * 
   * @param {string} data - Base64数据字符串
   * @returns {Object} 解析结果
   */
  parseBase64Image(data) {
    const match = data.match(/^data:image\/([a-z0-9.+_-]+);base64,(.*)$/i);
    
    if (!match) {
      throw new Error('Invalid base64 image data');
    }

    return {
      mimeType: `image/${match[1]}`,
      format: match[1],
      data: Buffer.from(match[2], 'base64')
    };
  },

  /**
   * 创建Base64图像数据URL
   * 
   * @param {Buffer} buffer - 图像数据
   * @param {string} format - 图像格式
   * @returns {string} Base64数据URL
   */
  createBase64DataUrl(buffer, format = 'jpeg') {
    const mimeType = IMAGE_MIME_TYPES[format] || 'image/jpeg';
    const base64 = buffer.toString('base64');
    return `data:${mimeType};base64,${base64}`;
  },

  /**
   * 计算图像文件大小
   * 
   * @param {Buffer} buffer - 图像数据
   * @returns {Object} 文件大小信息
   */
  calculateImageSize(buffer) {
    const bytes = buffer.length;
    const kb = bytes / 1024;
    const mb = kb / 1024;

    return {
      bytes,
      kb: Math.round(kb * 100) / 100,
      mb: Math.round(mb * 100) / 100,
      formatted: mb >= 1 ? `${Math.round(mb * 100) / 100} MB` : `${Math.round(kb * 100) / 100} KB`
    };
  }
};

// 创建全局图像处理器实例
const globalImageProcessor = new ImageProcessor();

/**
 * 获取全局图像处理器实例
 * 
 * @returns {ImageProcessor} 图像处理器实例
 */
function getGlobalImageProcessor() {
  return globalImageProcessor;
}

module.exports = {
  ImageProcessor,
  getImageProcessor,
  getGlobalImageProcessor,
  ImageUtils,
  SUPPORTED_IMAGE_FORMATS,
  IMAGE_MIME_TYPES,
  DEFAULT_PROCESSING_OPTIONS
};
