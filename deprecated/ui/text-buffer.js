/**
 * 文本缓冲区管理
 * 从原始文件第349926-349994行提取
 * 提供文本输入的撤销/重做功能和缓冲区管理
 */

const React = require('react');

/**
 * 文本缓冲区Hook
 * 原始函数: o9B({maxBufferSize: A, debounceMs: B})
 * 提供文本输入的历史记录和撤销功能
 */
function useTextBuffer({ maxBufferSize = 50, debounceMs = 1000 }) {
    const [buffer, setBuffer] = React.useState([]);
    const [currentIndex, setCurrentIndex] = React.useState(-1);
    const lastUpdateTime = React.useRef(0);
    const debounceTimer = React.useRef(null);
    
    /**
     * 推送文本到缓冲区
     * 原始变量: Y
     */
    const pushToBuffer = React.useCallback((text, cursorOffset, pastedContents = {}) => {
        const now = Date.now();
        
        // 清除现有的防抖定时器
        if (debounceTimer.current) {
            clearTimeout(debounceTimer.current);
            debounceTimer.current = null;
        }
        
        // 如果距离上次更新时间太短，延迟执行
        if (now - lastUpdateTime.current < debounceMs) {
            debounceTimer.current = setTimeout(() => {
                pushToBuffer(text, cursorOffset, pastedContents);
            }, debounceMs);
            return;
        }
        
        lastUpdateTime.current = now;
        
        setBuffer((prevBuffer) => {
            // 如果有当前索引，只保留到当前索引的历史
            const trimmedBuffer = currentIndex >= 0 ? prevBuffer.slice(0, currentIndex + 1) : prevBuffer;
            const lastEntry = trimmedBuffer[trimmedBuffer.length - 1];
            
            // 如果文本相同，不添加新条目
            if (lastEntry && lastEntry.text === text) {
                return trimmedBuffer;
            }
            
            // 添加新条目
            const newEntry = {
                text: text,
                cursorOffset: cursorOffset,
                pastedContents: pastedContents,
                timestamp: now
            };
            
            const newBuffer = [...trimmedBuffer, newEntry];
            
            // 限制缓冲区大小
            if (newBuffer.length > maxBufferSize) {
                return newBuffer.slice(-maxBufferSize);
            }
            
            return newBuffer;
        });
        
        setCurrentIndex((prevIndex) => {
            const newIndex = prevIndex >= 0 ? prevIndex + 1 : buffer.length;
            return Math.min(newIndex, maxBufferSize - 1);
        });
    }, [debounceMs, maxBufferSize, currentIndex, buffer.length]);
    
    /**
     * 撤销操作
     * 原始变量: W
     */
    const undo = React.useCallback(() => {
        if (currentIndex < 0 || buffer.length === 0) {
            return;
        }
        
        const newIndex = Math.max(0, currentIndex - 1);
        const entry = buffer[newIndex];
        
        if (entry) {
            setCurrentIndex(newIndex);
            return entry;
        }
        
        return;
    }, [buffer, currentIndex]);
    
    /**
     * 清空缓冲区
     * 原始变量: J
     */
    const clearBuffer = React.useCallback(() => {
        setBuffer([]);
        setCurrentIndex(-1);
        lastUpdateTime.current = 0;
        
        if (debounceTimer.current) {
            clearTimeout(debounceTimer.current);
            debounceTimer.current = null;
        }
    }, []);
    
    // 是否可以撤销
    const canUndo = currentIndex > 0 && buffer.length > 1;
    
    return {
        pushToBuffer,
        undo,
        canUndo,
        clearBuffer,
        buffer,
        currentIndex
    };
}

/**
 * 文本截断和占位符处理
 * 原始函数: zZ4(A, B)
 */
function handleTextTruncation(text, pastedContents) {
    const existingIds = Object.keys(pastedContents).map(Number);
    const nextId = existingIds.length > 0 ? Math.max(...existingIds) + 1 : 1;
    
    // 这里需要实现实际的文本截断逻辑
    // 原始代码调用了 eE2(A, I) 函数
    const { truncatedText, placeholderContent } = truncateText(text, nextId);
    
    if (!placeholderContent) {
        return;
    }
    
    const newPastedContents = {
        ...pastedContents,
        [nextId]: {
            id: nextId,
            type: "text",
            content: placeholderContent
        }
    };
    
    return {
        newInput: truncatedText,
        newPastedContents: newPastedContents
    };
}

/**
 * 文本截断实现
 * 这是 eE2(A, I) 函数的实现
 */
function truncateText(text, placeholderId) {
    const MAX_TEXT_LENGTH = 10000; // 最大文本长度
    
    if (text.length <= MAX_TEXT_LENGTH) {
        return {
            truncatedText: text,
            placeholderContent: null
        };
    }
    
    // 截断文本并创建占位符
    const truncatedText = text.substring(0, MAX_TEXT_LENGTH);
    const remainingText = text.substring(MAX_TEXT_LENGTH);
    
    const placeholder = `[TRUNCATED_${placeholderId}]`;
    
    return {
        truncatedText: truncatedText + placeholder,
        placeholderContent: remainingText
    };
}

/**
 * 获取下一个可用的ID
 */
function getNextAvailableId(pastedContents) {
    const existingIds = Object.keys(pastedContents).map(Number);
    if (existingIds.length === 0) {
        return 1;
    }
    return Math.max(...existingIds) + 1;
}

/**
 * 文本缓冲区组件
 */
function TextBufferProvider({ children, maxBufferSize = 50, debounceMs = 1000 }) {
    const textBuffer = useTextBuffer({ maxBufferSize, debounceMs });
    
    return React.createElement(TextBufferContext.Provider, {
        value: textBuffer
    }, children);
}

/**
 * 文本缓冲区上下文
 */
const TextBufferContext = React.createContext(null);

/**
 * 使用文本缓冲区上下文
 */
function useTextBufferContext() {
    const context = React.useContext(TextBufferContext);
    if (!context) {
        throw new Error('useTextBufferContext must be used within a TextBufferProvider');
    }
    return context;
}

module.exports = {
    useTextBuffer,
    handleTextTruncation,
    truncateText,
    getNextAvailableId,
    TextBufferProvider,
    TextBufferContext,
    useTextBufferContext
};
