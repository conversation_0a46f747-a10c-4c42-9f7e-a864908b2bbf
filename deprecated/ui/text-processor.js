/**
 * Claude Code - Text Processor
 * 
 * 重构来源: cli-format-all.js 第315796-315838行 文本处理功能
 * 重构时间: 2025-01-19
 * 
 * 文本处理、截断和格式化功能
 */

// TODO_REFACTOR: 以下是未重构的依赖，需要后续替换

// TODO_REFACTOR: 查找qE1文本长度限制获取函数定义
const getTextLengthLimit = () => {
  throw new Error('Text length limit function not yet refactored (original: qE1)');
};

/**
 * 默认配置
 */
const DEFAULT_CONFIG = {
  maxTextLength: 50000,
  maxLines: 1000,
  truncationSuffix: '... [truncated] ...',
  lineBreak: '\n'
};

/**
 * 文本处理选项
 */
const TEXT_PROCESSING_OPTIONS = {
  preserveWhitespace: false,
  trimLines: true,
  removeEmptyLines: false,
  normalizeLineBreaks: true,
  maxLineLength: null
};

/**
 * 移除文本开头和结尾的空行
 * 重构自: 第315796-315809行的m21函数
 * 
 * @param {string} text - 输入文本
 * @returns {string} 处理后的文本
 */
function trimEmptyLines(text) {
  if (!text || typeof text !== 'string') {
    return '';
  }

  const lines = text.split('\n');
  let startIndex = 0;
  
  // 找到第一个非空行
  while (startIndex < lines.length && lines[startIndex]?.trim() === '') {
    startIndex++;
  }
  
  let endIndex = lines.length - 1;
  
  // 找到最后一个非空行
  while (endIndex >= 0 && lines[endIndex]?.trim() === '') {
    endIndex--;
  }
  
  // 如果所有行都是空的
  if (startIndex > endIndex) {
    return '';
  }
  
  return lines.slice(startIndex, endIndex + 1).join('\n');
}

/**
 * 处理文本内容并截断
 * 重构自: 第315810-315838行的bP函数
 * 
 * @param {string} text - 输入文本
 * @param {Object} options - 处理选项
 * @returns {Object} 处理结果
 */
function processAndTruncateText(text, options = {}) {
  const config = {
    ...DEFAULT_CONFIG,
    ...options
  };

  if (!text || typeof text !== 'string') {
    return {
      totalLines: 0,
      truncatedContent: '',
      isImage: false,
      isTruncated: false,
      originalLength: 0,
      truncatedLength: 0
    };
  }

  // 检查是否为Base64图像数据
  const isBase64Image = /^data:image\/[a-z0-9.+_-]+;base64,/i.test(text);
  if (isBase64Image) {
    return {
      totalLines: 1,
      truncatedContent: text,
      isImage: true,
      isTruncated: false,
      originalLength: text.length,
      truncatedLength: text.length
    };
  }

  const originalLength = text.length;
  const maxLength = config.maxTextLength || getTextLengthLimit();
  
  // 如果文本长度在限制内，直接返回
  if (originalLength <= maxLength) {
    const lines = text.split('\n');
    return {
      totalLines: lines.length,
      truncatedContent: text,
      isImage: false,
      isTruncated: false,
      originalLength,
      truncatedLength: originalLength
    };
  }

  // 截断文本
  const truncatedText = text.slice(0, maxLength);
  const remainingText = text.slice(maxLength);
  const remainingLines = remainingText.split('\n').length;
  
  const truncatedContent = `${truncatedText}\n\n... [${remainingLines} lines truncated] ...`;
  
  return {
    totalLines: text.split('\n').length,
    truncatedContent,
    isImage: false,
    isTruncated: true,
    originalLength,
    truncatedLength: truncatedContent.length,
    truncatedLines: remainingLines
  };
}

/**
 * 文本处理器类
 */
class TextProcessor {
  constructor(options = {}) {
    this.config = {
      ...DEFAULT_CONFIG,
      ...options
    };
  }

  /**
   * 处理文本
   * 
   * @param {string} text - 输入文本
   * @param {Object} options - 处理选项
   * @returns {string} 处理后的文本
   */
  processText(text, options = {}) {
    if (!text || typeof text !== 'string') {
      return '';
    }

    const processingOptions = {
      ...TEXT_PROCESSING_OPTIONS,
      ...options
    };

    let processedText = text;

    // 标准化换行符
    if (processingOptions.normalizeLineBreaks) {
      processedText = processedText.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
    }

    // 处理行
    if (processingOptions.trimLines || processingOptions.removeEmptyLines) {
      const lines = processedText.split('\n');
      const processedLines = lines
        .map(line => processingOptions.trimLines ? line.trim() : line)
        .filter(line => processingOptions.removeEmptyLines ? line.length > 0 : true);
      
      processedText = processedLines.join('\n');
    }

    // 移除开头和结尾的空行
    if (!processingOptions.preserveWhitespace) {
      processedText = trimEmptyLines(processedText);
    }

    // 限制行长度
    if (processingOptions.maxLineLength) {
      const lines = processedText.split('\n');
      const wrappedLines = lines.map(line => 
        this.wrapLine(line, processingOptions.maxLineLength)
      ).flat();
      processedText = wrappedLines.join('\n');
    }

    return processedText;
  }

  /**
   * 截断文本
   * 
   * @param {string} text - 输入文本
   * @param {Object} options - 截断选项
   * @returns {Object} 截断结果
   */
  truncateText(text, options = {}) {
    return processAndTruncateText(text, {
      ...this.config,
      ...options
    });
  }

  /**
   * 包装长行
   * 
   * @param {string} line - 输入行
   * @param {number} maxLength - 最大长度
   * @returns {Array<string>} 包装后的行数组
   */
  wrapLine(line, maxLength) {
    if (line.length <= maxLength) {
      return [line];
    }

    const words = line.split(' ');
    const wrappedLines = [];
    let currentLine = '';

    for (const word of words) {
      if (currentLine.length + word.length + 1 <= maxLength) {
        currentLine += (currentLine ? ' ' : '') + word;
      } else {
        if (currentLine) {
          wrappedLines.push(currentLine);
        }
        
        // 如果单词本身就超过最大长度，强制截断
        if (word.length > maxLength) {
          let remainingWord = word;
          while (remainingWord.length > maxLength) {
            wrappedLines.push(remainingWord.slice(0, maxLength));
            remainingWord = remainingWord.slice(maxLength);
          }
          currentLine = remainingWord;
        } else {
          currentLine = word;
        }
      }
    }

    if (currentLine) {
      wrappedLines.push(currentLine);
    }

    return wrappedLines;
  }

  /**
   * 格式化文本大小
   * 
   * @param {number} bytes - 字节数
   * @returns {string} 格式化的大小字符串
   */
  formatTextSize(bytes) {
    if (bytes < 1024) {
      return `${bytes} bytes`;
    } else if (bytes < 1024 * 1024) {
      return `${Math.round(bytes / 1024 * 100) / 100} KB`;
    } else {
      return `${Math.round(bytes / (1024 * 1024) * 100) / 100} MB`;
    }
  }

  /**
   * 计算文本统计信息
   * 
   * @param {string} text - 输入文本
   * @returns {Object} 统计信息
   */
  getTextStats(text) {
    if (!text || typeof text !== 'string') {
      return {
        characters: 0,
        charactersNoSpaces: 0,
        words: 0,
        lines: 0,
        paragraphs: 0,
        bytes: 0
      };
    }

    const lines = text.split('\n');
    const words = text.trim().split(/\s+/).filter(word => word.length > 0);
    const paragraphs = text.split(/\n\s*\n/).filter(para => para.trim().length > 0);

    return {
      characters: text.length,
      charactersNoSpaces: text.replace(/\s/g, '').length,
      words: words.length,
      lines: lines.length,
      paragraphs: paragraphs.length,
      bytes: Buffer.byteLength(text, 'utf8')
    };
  }

  /**
   * 提取文本摘要
   * 
   * @param {string} text - 输入文本
   * @param {number} maxLength - 最大长度
   * @returns {string} 文本摘要
   */
  extractSummary(text, maxLength = 200) {
    if (!text || typeof text !== 'string') {
      return '';
    }

    const trimmed = text.trim();
    if (trimmed.length <= maxLength) {
      return trimmed;
    }

    // 尝试在句子边界截断
    const sentences = trimmed.split(/[.!?]+/);
    let summary = '';
    
    for (const sentence of sentences) {
      const trimmedSentence = sentence.trim();
      if (summary.length + trimmedSentence.length + 1 <= maxLength) {
        summary += (summary ? '. ' : '') + trimmedSentence;
      } else {
        break;
      }
    }

    // 如果没有找到合适的句子边界，直接截断
    if (!summary) {
      summary = trimmed.slice(0, maxLength - 3);
    }

    return summary + (trimmed.length > summary.length ? '...' : '');
  }
}

/**
 * 文本工具函数
 */
const TextUtils = {
  // 移除空行
  trimEmptyLines,
  
  // 处理和截断文本
  processAndTruncateText,
  
  // 检查是否为Base64图像
  isBase64Image: (text) => /^data:image\/[a-z0-9.+_-]+;base64,/i.test(text),
  
  // 标准化换行符
  normalizeLineBreaks: (text) => text.replace(/\r\n/g, '\n').replace(/\r/g, '\n'),
  
  // 计算行数
  countLines: (text) => text ? text.split('\n').length : 0,
  
  // 计算单词数
  countWords: (text) => text ? text.trim().split(/\s+/).filter(word => word.length > 0).length : 0,
  
  // 转义HTML
  escapeHtml: (text) => {
    const htmlEscapes = {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#39;'
    };
    return text.replace(/[&<>"']/g, char => htmlEscapes[char]);
  },
  
  // 反转义HTML
  unescapeHtml: (text) => {
    const htmlUnescapes = {
      '&amp;': '&',
      '&lt;': '<',
      '&gt;': '>',
      '&quot;': '"',
      '&#39;': "'"
    };
    return text.replace(/&(?:amp|lt|gt|quot|#39);/g, entity => htmlUnescapes[entity]);
  }
};

// 创建全局文本处理器实例
const globalTextProcessor = new TextProcessor();

/**
 * 获取全局文本处理器实例
 * 
 * @returns {TextProcessor} 文本处理器实例
 */
function getGlobalTextProcessor() {
  return globalTextProcessor;
}

module.exports = {
  TextProcessor,
  getGlobalTextProcessor,
  TextUtils,
  trimEmptyLines,
  processAndTruncateText,
  DEFAULT_CONFIG,
  TEXT_PROCESSING_OPTIONS
};
