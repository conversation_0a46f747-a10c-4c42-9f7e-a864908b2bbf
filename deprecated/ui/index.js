/**
 * Claude Code - UI Components System
 * 
 * 重构来源: cli-format-all.js 第315741-331313行 UI组件和处理功能
 * 重构时间: 2025-01-19
 * 
 * UI组件系统的主入口，整合代码高亮、图像处理和文本处理功能
 */

// 导入UI模块
const {
  CodeHighlighter,
  CodeBlock,
  InlineCode,
  CodeHighlightUtils,
  SUPPORTED_LANGUAGES,
  LANGUAGE_ALIASES
} = require('./code-highlighter');

const {
  ImageProcessor,
  getImageProcessor,
  getGlobalImageProcessor,
  ImageUtils,
  SUPPORTED_IMAGE_FORMATS,
  IMAGE_MIME_TYPES,
  DEFAULT_PROCESSING_OPTIONS
} = require('./image-processor');

const {
  TextProcessor,
  getGlobalTextProcessor,
  TextUtils,
  trimEmptyLines,
  processAndTruncateText,
  DEFAULT_CONFIG as TEXT_DEFAULT_CONFIG,
  TEXT_PROCESSING_OPTIONS
} = require('./text-processor');

// React相关（如果需要）
const React = require('react');

/**
 * UI系统配置
 */
const UI_CONFIG = {
  codeHighlight: {
    theme: 'default',
    showLineNumbers: false,
    tabSize: 2,
    wrapLines: true
  },
  imageProcessing: {
    maxWidth: 1920,
    maxHeight: 1080,
    quality: 80,
    format: 'jpeg'
  },
  textProcessing: {
    maxLength: 50000,
    maxLines: 1000,
    preserveFormatting: true
  }
};

/**
 * 统一UI系统类
 * 整合所有UI处理功能的高级接口
 */
class UISystem {
  constructor(options = {}) {
    this.config = {
      ...UI_CONFIG,
      ...options
    };
    
    this.codeHighlighter = null;
    this.imageProcessor = getGlobalImageProcessor();
    this.textProcessor = getGlobalTextProcessor();
    this.isInitialized = false;
  }

  /**
   * 初始化UI系统
   * 
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      // 初始化图像处理器
      await this.imageProcessor.initialize();
      
      this.isInitialized = true;
    } catch (error) {
      throw new Error(`Failed to initialize UI system: ${error.message}`);
    }
  }

  /**
   * 处理代码高亮
   * 
   * @param {string} code - 代码内容
   * @param {string} language - 编程语言
   * @param {Object} options - 高亮选项
   * @returns {React.Element} 高亮后的代码组件
   */
  highlightCode(code, language, options = {}) {
    const highlightOptions = {
      ...this.config.codeHighlight,
      ...options
    };

    return React.createElement(CodeHighlighter, {
      code,
      language,
      options: highlightOptions
    });
  }

  /**
   * 创建代码块组件
   * 
   * @param {string} code - 代码内容
   * @param {string} language - 编程语言
   * @param {Object} options - 组件选项
   * @returns {React.Element} 代码块组件
   */
  createCodeBlock(code, language, options = {}) {
    const blockOptions = {
      ...this.config.codeHighlight,
      ...options
    };

    return React.createElement(CodeBlock, {
      code,
      language,
      ...blockOptions
    });
  }

  /**
   * 创建内联代码组件
   * 
   * @param {string} code - 代码内容
   * @param {string} language - 编程语言（可选）
   * @param {Object} options - 组件选项
   * @returns {React.Element} 内联代码组件
   */
  createInlineCode(code, language, options = {}) {
    return React.createElement(InlineCode, {
      code,
      language,
      ...options
    });
  }

  /**
   * 处理图像
   * 
   * @param {Buffer|string} input - 输入图像数据或路径
   * @param {Object} options - 处理选项
   * @returns {Promise<Buffer>} 处理后的图像数据
   */
  async processImage(input, options = {}) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const processingOptions = {
      ...this.config.imageProcessing,
      ...options
    };

    return await this.imageProcessor.processImage(input, processingOptions);
  }

  /**
   * 获取图像信息
   * 
   * @param {Buffer|string} input - 输入图像数据或路径
   * @returns {Promise<Object>} 图像信息
   */
  async getImageInfo(input) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    return await this.imageProcessor.getImageInfo(input);
  }

  /**
   * 压缩图像
   * 
   * @param {Buffer|string} input - 输入图像数据或路径
   * @param {Object} options - 压缩选项
   * @returns {Promise<Buffer>} 压缩后的图像数据
   */
  async compressImage(input, options = {}) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    return await this.imageProcessor.compressImage(input, options);
  }

  /**
   * 处理文本
   * 
   * @param {string} text - 输入文本
   * @param {Object} options - 处理选项
   * @returns {string} 处理后的文本
   */
  processText(text, options = {}) {
    const processingOptions = {
      ...this.config.textProcessing,
      ...options
    };

    return this.textProcessor.processText(text, processingOptions);
  }

  /**
   * 截断文本
   * 
   * @param {string} text - 输入文本
   * @param {Object} options - 截断选项
   * @returns {Object} 截断结果
   */
  truncateText(text, options = {}) {
    const truncationOptions = {
      ...this.config.textProcessing,
      ...options
    };

    return this.textProcessor.truncateText(text, truncationOptions);
  }

  /**
   * 获取文本统计信息
   * 
   * @param {string} text - 输入文本
   * @returns {Object} 统计信息
   */
  getTextStats(text) {
    return this.textProcessor.getTextStats(text);
  }

  /**
   * 提取文本摘要
   * 
   * @param {string} text - 输入文本
   * @param {number} maxLength - 最大长度
   * @returns {string} 文本摘要
   */
  extractTextSummary(text, maxLength = 200) {
    return this.textProcessor.extractSummary(text, maxLength);
  }

  /**
   * 检查文件类型
   * 
   * @param {string} filename - 文件名
   * @returns {Object} 文件类型信息
   */
  getFileTypeInfo(filename) {
    return {
      isImage: ImageUtils.isImageFile(filename),
      isCode: CodeHighlightUtils.isLanguageSupported(
        CodeHighlightUtils.inferLanguageFromExtension(filename)
      ),
      language: CodeHighlightUtils.inferLanguageFromExtension(filename),
      mimeType: ImageUtils.getImageMimeType(filename)
    };
  }

  /**
   * 获取系统统计信息
   * 
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      isInitialized: this.isInitialized,
      config: this.config,
      supportedLanguages: SUPPORTED_LANGUAGES.length,
      supportedImageFormats: SUPPORTED_IMAGE_FORMATS.length
    };
  }

  /**
   * 重置系统
   */
  reset() {
    this.isInitialized = false;
    this.imageProcessor = getGlobalImageProcessor();
    this.textProcessor = getGlobalTextProcessor();
  }
}

/**
 * 创建UI系统实例
 * 
 * @param {Object} options - 系统选项
 * @returns {UISystem} UI系统实例
 */
function createUISystem(options = {}) {
  return new UISystem(options);
}

// 全局UI系统实例
let globalUISystem = null;

/**
 * 获取全局UI系统实例
 * 
 * @param {Object} options - 系统选项（仅在首次创建时使用）
 * @returns {UISystem} 全局UI系统实例
 */
function getGlobalUISystem(options = {}) {
  if (!globalUISystem) {
    globalUISystem = createUISystem(options);
  }
  return globalUISystem;
}

/**
 * UI工具函数集合
 */
const UIUtils = {
  // 代码相关
  ...CodeHighlightUtils,
  
  // 图像相关
  ...ImageUtils,
  
  // 文本相关
  ...TextUtils,
  
  // 文件类型检测
  getFileType: (filename) => {
    const system = getGlobalUISystem();
    return system.getFileTypeInfo(filename);
  },
  
  // 快速处理函数
  quickHighlight: (code, language) => {
    const system = getGlobalUISystem();
    return system.highlightCode(code, language);
  },
  
  quickTruncate: (text, maxLength = 1000) => {
    const system = getGlobalUISystem();
    return system.truncateText(text, { maxTextLength: maxLength });
  },
  
  quickImageInfo: async (input) => {
    const system = getGlobalUISystem();
    await system.initialize();
    return await system.getImageInfo(input);
  }
};

module.exports = {
  // 主要类和函数
  UISystem,
  createUISystem,
  getGlobalUISystem,
  
  // 核心组件
  CodeHighlighter,
  CodeBlock,
  InlineCode,
  ImageProcessor,
  TextProcessor,
  
  // 工具函数
  UIUtils,
  
  // 处理器获取函数
  getImageProcessor,
  getGlobalImageProcessor,
  getGlobalTextProcessor,
  
  // 工具函数
  trimEmptyLines,
  processAndTruncateText,
  
  // 常量
  SUPPORTED_LANGUAGES,
  LANGUAGE_ALIASES,
  SUPPORTED_IMAGE_FORMATS,
  IMAGE_MIME_TYPES,
  UI_CONFIG,
  DEFAULT_PROCESSING_OPTIONS,
  TEXT_DEFAULT_CONFIG,
  TEXT_PROCESSING_OPTIONS
};
