/**
 * MCP (Model Context Protocol) 服务器管理命令
 * 从原始文件第354881-355142行重构而来
 */

const { trackEvent } = require('../../analytics/events');
const { getDefaultState } = require('../../core/state');
const {
    validateScope,
    saveServerConfig,
    removeServerConfig,
    listServers,
    getServerConfig,
    parseEnvironmentVariables,
    parseJsonConfig,
    importFromClaudeDesktop,
    resetProjectChoices
} = require('../../mcp/manager');

/**
 * 设置MCP相关的子命令
 * 原始位置: 第354881-355142行
 */
function setupMcpCommands(mcpCommand) {
    
    // serve命令 - 启动MCP服务器
    // 原始位置: 第354882行
    mcpCommand
        .command('serve')
        .description('Start the MCP server')
        .helpOption('-h, --help', 'Display help for command')
        .option('-d, --debug', 'Enable debug mode', () => true)
        .option('--verbose', 'Override verbose mode setting from config', () => true)
        .action(async (options) => {
            const state = getDefaultState();
            trackEvent('tengu_mcp_start', {});
            
            if (!isValidMcpConfig(state)) {
                console.error('Invalid MCP configuration');
                process.exit(1);
            }
            
            // TODO: 实现MCP服务器启动逻辑
            console.log('Starting MCP server...');
            // 原始代码中有复杂的服务器启动逻辑
        });

    // add命令 - 添加MCP服务器
    // 原始位置: 第354897行
    mcpCommand
        .command('add <n> <commandOrUrl> [args...]')
        .description('Add a server')
        .option('-s, --scope <scope>', 'Configuration scope (local, user, or project)', 'local')
        .option('-t, --transport <transport>', 'Transport type (stdio, sse, http)', 'stdio')
        .option('-e, --env <env...>', 'Set environment variables (e.g. -e KEY=value)')
        .option('-H, --header <header...>', 'Set HTTP headers for SSE and HTTP transports (e.g. -H "X-Api-Key: abc123" -H "X-Custom: value")')
        .helpOption('-h, --help', 'Display help for command')
        .action(async (name, commandOrUrl, args, options) => {
            if (!name) {
                console.error('Error: Server name is required.');
                console.error('Usage: claude mcp add <n> <command> [args...]');
                process.exit(1);
            }
            
            if (!commandOrUrl) {
                console.error('Error: Command is required when server name is provided.');
                console.error('Usage: claude mcp add <n> <command> [args...]');
                process.exit(1);
            }

            try {
                const scope = validateScope(options.scope);
                const transport = options.transport;
                
                trackEvent('tengu_mcp_add', {
                    type: transport,
                    scope: scope,
                    source: 'command',
                    transport: transport
                });

                if (transport === 'sse') {
                    // SSE服务器配置
                    const headers = parseHeaders(options.header);
                    saveServerConfig(name, {
                        type: 'sse',
                        url: commandOrUrl,
                        headers: headers
                    }, scope);
                    console.log(`Added SSE MCP server ${name} with URL: ${commandOrUrl} to ${scope} config`);
                } else if (transport === 'http') {
                    // HTTP服务器配置
                    const headers = parseHeaders(options.header);
                    saveServerConfig(name, {
                        type: 'http',
                        url: commandOrUrl,
                        headers: headers
                    }, scope);
                    console.log(`Added HTTP MCP server ${name} with URL: ${commandOrUrl} to ${scope} config`);
                } else {
                    // 默认stdio配置
                    const env = parseEnvironmentVariables(options.env);
                    saveServerConfig(name, {
                        type: 'stdio',
                        command: commandOrUrl,
                        args: args || [],
                        env: env
                    }, scope);
                    console.log(`Added stdio MCP server ${name} with command: ${commandOrUrl} ${(args || []).join(' ')} to ${scope} config`);
                }
                
                process.exit(0);
            } catch (error) {
                console.error(error.message);
                process.exit(1);
            }
        });

    // remove命令 - 移除MCP服务器
    // 原始位置: 第354958行
    mcpCommand
        .command('remove <n>')
        .description('Remove an MCP server')
        .option('-s, --scope <scope>', 'Configuration scope (local, user, or project) - if not specified, removes from whichever scope it exists in')
        .helpOption('-h, --help', 'Display help for command')
        .action(async (name, options) => {
            try {
                if (options.scope) {
                    const scope = validateScope(options.scope);
                    removeServerConfig(name, scope);
                    console.log(`Removed MCP server ${name} from ${scope} config`);
                } else {
                    // 从所有scope中查找并移除
                    const removed = removeServerConfig(name);
                    if (removed) {
                        console.log(`Removed MCP server ${name}`);
                    } else {
                        console.log(`MCP server ${name} not found`);
                    }
                }
                process.exit(0);
            } catch (error) {
                console.error(error.message);
                process.exit(1);
            }
        });

    // list命令 - 列出MCP服务器
    // 原始位置: 第355022行
    mcpCommand
        .command('list')
        .description('List configured MCP servers')
        .helpOption('-h, --help', 'Display help for command')
        .action(async () => {
            trackEvent('tengu_mcp_list', {});
            const servers = listServers();
            
            if (Object.keys(servers).length === 0) {
                console.log('No MCP servers configured');
                process.exit(0);
            }

            for (const [name, config] of Object.entries(servers)) {
                if (config.type === 'sse') {
                    console.log(`${name}: ${config.url} (SSE)`);
                } else if (config.type === 'http') {
                    console.log(`${name}: ${config.url} (HTTP)`);
                } else if (!config.type || config.type === 'stdio') {
                    const args = Array.isArray(config.args) ? config.args : [];
                    console.log(`${name}: ${config.command} ${args.join(' ')}`);
                }
            }
            process.exit(0);
        });

    // get命令 - 获取MCP服务器详情
    // 原始位置: 第355040行
    mcpCommand
        .command('get <n>')
        .description('Get details about an MCP server')
        .helpOption('-h, --help', 'Display help for command')
        .action(async (name) => {
            trackEvent('tengu_mcp_get', { name: name });
            
            const config = getServerConfig(name);
            if (!config) {
                console.log(`MCP server ${name} not found`);
                process.exit(1);
            }

            console.log(`Server: ${name}`);
            if (config.type === 'sse') {
                console.log('  Type: sse');
                console.log(`  URL: ${config.url}`);
                if (config.headers) {
                    console.log('  Headers:');
                    for (const [key, value] of Object.entries(config.headers)) {
                        console.log(`    ${key}: ${value}`);
                    }
                }
            } else if (config.type === 'http') {
                console.log('  Type: http');
                console.log(`  URL: ${config.url}`);
                if (config.headers) {
                    console.log('  Headers:');
                    for (const [key, value] of Object.entries(config.headers)) {
                        console.log(`    ${key}: ${value}`);
                    }
                }
            } else if (config.type === 'stdio') {
                console.log('  Type: stdio');
                console.log(`  Command: ${config.command}`);
                const args = Array.isArray(config.args) ? config.args : [];
                console.log(`  Args: ${args.join(' ')}`);
                if (config.env) {
                    console.log('  Environment:');
                    for (const [key, value] of Object.entries(config.env)) {
                        console.log(`    ${key}=${value}`);
                    }
                }
            }
            process.exit(0);
        });

    // 其他命令...
    // add-json, add-from-claude-desktop, reset-project-choices
    // 由于篇幅限制，这些命令将在后续添加
}

/**
 * 解析HTTP头部
 */
function parseHeaders(headers) {
    if (!headers) return {};
    
    const result = {};
    for (const header of headers) {
        const [key, value] = header.split(':').map(s => s.trim());
        if (key && value) {
            result[key] = value;
        }
    }
    return result;
}

/**
 * 验证MCP配置
 */
function isValidMcpConfig(state) {
    // TODO: 实现MCP配置验证逻辑
    return true;
}

module.exports = {
    setupMcpCommands
};
