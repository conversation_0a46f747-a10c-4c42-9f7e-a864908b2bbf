/**
 * 配置管理命令
 * 从原始文件第354835-354880行重构而来
 */

const { setup } = require('../../core/setup');
const { getDefaultState } = require('../../core/state');
const {
    getConfigValue,
    setConfigValue,
    removeConfigValue,
    listAllConfig,
    addToConfigArray,
    isConfigArray
} = require('../../config/manager');

/**
 * 设置配置相关的子命令
 * 原始位置: 第354835-354880行
 */
function setupConfigCommands(configCommand) {
    
    // get命令 - 获取配置值
    // 原始位置: 第354836行
    configCommand
        .command('get <key>')
        .description('Get a config value')
        .option('-g, --global', 'Use global config')
        .helpOption('-h, --help', 'Display help for command')
        .action(async (key, options) => {
            await setup(getDefaultState(), "default", false, false, undefined);
            console.log(getConfigValue(key, options.global ?? false));
            process.exit(0);
        });

    // set命令 - 设置配置值
    // 原始位置: 第354842行
    configCommand
        .command('set <key> <value>')
        .description('Set a config value')
        .option('-g, --global', 'Use global config')
        .helpOption('-h, --help', 'Display help for command')
        .action(async (key, value, options) => {
            await setup(getDefaultState(), "default", false, false, undefined);
            setConfigValue(key, value, options.global ?? false);
            console.log(`Set ${key} = ${value}`);
            process.exit(0);
        });

    // remove命令 - 移除配置值
    // 原始位置: 第354849行
    configCommand
        .command('remove <key> [values...]')
        .alias('rm')
        .description('Remove a config value or items from a config array')
        .option('-g, --global', 'Use global config')
        .helpOption('-h, --help', 'Display help for command')
        .action(async (key, values, options) => {
            await setup(getDefaultState(), "default", false, false, undefined);
            
            if (isConfigArray(key, options.global ?? false) && values && values.length > 0) {
                // 从数组中移除特定值
                removeConfigValue(key, values, options.global ?? false);
                console.log(`Removed values from ${key}: ${values.join(', ')}`);
            } else {
                // 移除整个配置项
                removeConfigValue(key, null, options.global ?? false);
                console.log(`Removed ${key}`);
            }
            process.exit(0);
        });

    // list命令 - 列出所有配置
    // 原始位置: 第354864行
    configCommand
        .command('list')
        .alias('ls')
        .description('List all config values')
        .option('-g, --global', 'Use global config', false)
        .helpOption('-h, --help', 'Display help for command')
        .action(async (options) => {
            await setup(getDefaultState(), "default", false, false, undefined);
            console.log(JSON.stringify(listAllConfig(options.global ?? false), null, 2));
            process.exit(0);
        });

    // add命令 - 向配置数组添加值
    // 原始位置: 第354870行
    configCommand
        .command('add <key> <values...>')
        .description('Add items to a config array (space or comma separated)')
        .option('-g, --global', 'Use global config')
        .helpOption('-h, --help', 'Display help for command')
        .action(async (key, values, options) => {
            await setup(getDefaultState(), "default", false, false, undefined);
            
            // 处理逗号分隔的值
            const processedValues = values
                .flatMap(value => value.includes(',') ? value.split(',') : value)
                .map(value => value.trim())
                .filter(value => value.length > 0);
            
            addToConfigArray(key, processedValues, options.global ?? false);
            console.log(`Added to ${key}: ${processedValues.join(', ')}`);
            process.exit(0);
        });
}



module.exports = {
    setupConfigCommands
};
