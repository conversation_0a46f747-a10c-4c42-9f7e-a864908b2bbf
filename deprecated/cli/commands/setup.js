/**
 * 设置相关命令
 * 从原始文件第355143-355181行重构而来
 */

const { render } = require('ink');
const React = require('react');
const chalk = require('chalk');

const { trackEvent } = require('../../analytics/events');
const { setup } = require('../../core/setup');
const { isLocalInstallation, checkAuthConfig } = require('../../utils/system');

/**
 * 设置主要的设置相关命令
 * 原始位置: 第355143-355181行
 */
function setupMainCommands(program) {
    
    // migrate-installer命令 - 迁移安装器
    // 原始位置: 第355143行
    program
        .command('migrate-installer')
        .description('Migrate from global npm installation to local installation')
        .helpOption('-h, --help', 'Display help for command')
        .action(async () => {
            if (isLocalInstallation()) {
                console.log('Already running from local installation. No migration needed.');
                process.exit(0);
            }
            
            trackEvent('tengu_migrate_installer_command', {});
            
            await new Promise((resolve) => {
                const { waitUntilExit } = render(
                    React.createElement('div', null, 
                        React.createElement(MigrationComponent, null)
                    )
                );
                
                waitUntilExit().then(() => {
                    resolve();
                });
            });
            
            process.exit(0);
        });

    // setup-token命令 - 设置长期认证令牌
    // 原始位置: 第355159行
    program
        .command('setup-token')
        .description('Set up a long-lived authentication token (requires Claude subscription)')
        .helpOption('-h, --help', 'Display help for command')
        .action(async () => {
            trackEvent('tengu_setup_token_command', {});
            
            // 检查现有认证配置
            await setup(); // 原始: await a5()
            
            if (!checkAuthConfig()) {
                process.stderr.write(chalk.yellow(
                    'Warning: You already have authentication configured via environment variable or API key helper.\n'
                ));
                process.stderr.write(chalk.yellow(
                    'The setup-token command will create a new OAuth token which you can use instead.\n'
                ));
            }

            await new Promise((resolve) => {
                const { unmount } = render(
                    React.createElement(TokenSetupComponent, {
                        onDone: () => {
                            unmount();
                            resolve();
                        },
                        mode: 'setup-token',
                        startingMessage: 'This will guide you through long-lived (1-year) auth token setup for your Claude account. Claude subscription required.'
                    })
                );
            });
            
            process.exit(0);
        });

    // doctor命令 - 检查系统健康状态
    // 原始位置: 第355182行
    program
        .command('doctor')
        .description('Check the health of your Claude Code auto-updater')
        .helpOption('-h, --help', 'Display help for command')
        .action(async () => {
            trackEvent('tengu_doctor_command', {});
            
            await new Promise((resolve) => {
                const { unmount } = render(
                    React.createElement('div', null,
                        React.createElement(DoctorComponent, {
                            onDone: () => {
                                unmount();
                                resolve();
                            }
                        })
                    ),
                    {
                        exitOnCtrlC: false
                    }
                );
            });
            
            process.exit(0);
        });
}

/**
 * 迁移组件 - 处理安装器迁移的UI
 * 原始位置: 第355149行的React组件
 */
function MigrationComponent() {
    // TODO: 实现迁移UI组件
    // 原始代码: React.createElement(_p, null)
    return React.createElement('div', null, 
        React.createElement('text', null, 'Migrating installer...')
    );
}

/**
 * 令牌设置组件 - 处理OAuth令牌设置的UI
 * 原始位置: 第355168行的React组件
 */
function TokenSetupComponent({ onDone, mode, startingMessage }) {
    // TODO: 实现令牌设置UI组件
    // 原始代码: React.createElement(Hp, { onDone, mode, startingMessage })
    return React.createElement('div', null,
        React.createElement('text', null, startingMessage),
        React.createElement('text', null, '\nSetting up authentication token...')
    );
}

/**
 * 诊断组件 - 处理系统诊断的UI
 * 原始位置: 第355185行的React组件
 */
function DoctorComponent({ onDone }) {
    // TODO: 实现诊断UI组件
    // 原始代码: React.createElement(cq1, { onDone })
    return React.createElement('div', null,
        React.createElement('text', null, 'Running system diagnostics...')
    );
}

module.exports = {
    setupMainCommands
};
