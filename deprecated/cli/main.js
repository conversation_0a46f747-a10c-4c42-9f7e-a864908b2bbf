/**
 * Claude Code - Main CLI Entry Point
 * 
 * 重构来源: cli-format-all.js 第354575-355224行 (649行)
 * 重构时间: 2025-01-19
 * 
 * 这是Claude Code的主要CLI命令定义和处理逻辑
 */

// 第三方依赖 (已确认)
const { Command, Option } = require('commander');
const chalk = require('chalk');

// TODO_REFACTOR: 以下是未重构的依赖，需要后续替换

// 系统初始化函数 - 重构自第354400-354406行的sF4函数
const performSystemInitialization = () => {
  // TODO_REFACTOR: 查找Q6B API密钥迁移函数定义
  // Q6B(); // API密钥迁移

  // TODO_REFACTOR: 查找I6B环境变量迁移函数定义
  // I6B(); // 环境变量迁移

  // TODO_REFACTOR: 查找D6B本地安装迁移函数定义
  // D6B(); // 本地安装迁移

  // TODO_REFACTOR: 查找G6B MCP配置迁移函数定义
  // G6B(); // MCP配置迁移

  // TODO_REFACTOR: 查找ErA项目设置迁移函数定义
  // ErA(); // 项目设置迁移

  console.log('System initialization completed');
};

// 应用初始化函数 - 重构自第354501-354540行的rF4函数
const initializeApp = async () => {
  // 执行系统初始化
  performSystemInitialization();
  // 处理ripgrep特殊情况
  if (process.argv[2] === "--ripgrep") {
    const args = process.argv.slice(3);
    // TODO_REFACTOR: 查找NX2 ripgrep函数定义
    console.log('Ripgrep execution placeholder');
    process.exit(0);
  }

  // 设置入口点环境变量
  if (!process.env.CLAUDE_CODE_ENTRYPOINT) {
    process.env.CLAUDE_CODE_ENTRYPOINT = "cli";
  }

  // 设置进程事件监听器
  process.on("exit", () => {
    cleanup(); // 使用已定义的cleanup函数
  });

  process.on("SIGINT", () => {
    process.exit(0);
  });

  // 检测打印模式
  const args = process.argv.slice(2);
  const isPrintMode = args.includes("-p") || args.includes("--print") || !process.stdout.isTTY;

  // TODO_REFACTOR: 查找_FA和yFA函数定义
  // _FA(isPrintMode);  // 设置打印模式
  // yFA(!isPrintMode); // 设置交互模式

  // 检测运行环境
  const entrypoint = (() => {
    if (process.env.GITHUB_ACTIONS === "true") return "github-action";
    if (process.env.CLAUDE_CODE_ENTRYPOINT === "sdk-ts") return "sdk-typescript";
    if (process.env.CLAUDE_CODE_ENTRYPOINT === "sdk-cli") return "sdk-cli";
    return "cli";
  })();

  // TODO_REFACTOR: 查找fFA函数定义
  // fFA(entrypoint); // 设置入口点

  // TODO_REFACTOR: 查找wX2函数定义
  // const initResult = wX2(); // 初始化系统
  // if (initResult instanceof Promise) {
  //   await initResult;
  // }

  // TODO_REFACTOR: 查找vc0函数定义
  // vc0().catch((error) => {
  //   console.error(`Shell snapshot cleanup failed: ${error}`);
  // });

  // 设置进程标题
  process.title = "claude";

  // 注意: eF4函数的功能已经重构为createCompleteCLI()函数
  // 不需要在这里调用，main()函数会调用createCompleteCLI()
};

// 产品名称常量 - 重构自第354577行的I2常量
const PRODUCT_NAME = 'Claude Code';

// TODO_REFACTOR: 查找QD1权限模式数组定义  
const PERMISSION_MODES = ['default', 'strict', 'permissive']; // 临时占位符

// 应用设置函数 - 重构自第354407-354500行的US函数
const setupApp = async (workingDir, mode, print, interactive, sessionId) => {
  // 检查Node.js版本
  const nodeVersion = process.version.match(/^v(\d+)\./)?.[1];
  if (!nodeVersion || parseInt(nodeVersion) < 18) {
    console.error(chalk.bold.red("Error: Claude Code requires Node.js version 18 or higher."));
    process.exit(1);
  }

  // 设置会话ID
  if (sessionId) {
    // TODO_REFACTOR: 查找FFA函数定义
    // FFA(sessionId); // 设置会话ID
  }

  // TODO_REFACTOR: 查找T8A和E6函数定义
  // T8A(); // 初始化遥测
  // if (E6(false)) { // 检查CI环境
  //   console.warn("Running in CI environment - interactive features are limited");
  // }

  // TODO_REFACTOR: 查找dE2和oE1函数定义 - iTerm2和Terminal.app设置恢复
  // const itermStatus = dE2();
  // if (itermStatus.status === "restored") {
  //   console.log(chalk.yellow("Detected an interrupted iTerm2 setup. Your original settings have been restored. You may need to restart iTerm2 for the changes to take effect."));
  // } else if (itermStatus.status === "failed") {
  //   console.error(chalk.red(`Failed to restore iTerm2 settings. Please manually restore your original settings with: defaults import com.googlecode.iterm2 ${itermStatus.backupPath}.`));
  // }

  const printMode = print ?? false;

  // TODO_REFACTOR: 查找各种初始化函数定义
  // uX(workingDir);  // 设置工作目录
  // _F();            // 初始化功能
  // nQB();           // 初始化查询
  // sQB();           // 初始化设置
  // lo2();           // 初始化本地选项
  // Ac0();           // 初始化访问控制
  // lL1();           // 初始化本地
  // eL1(printMode);  // 初始化错误处理
  // AY();            // 初始化应用
  // iE();            // 初始化交互
  // ey();            // 初始化事件
  // ej();            // 初始化作业
  // Oa1();           // 初始化选项
  // HE1([], qB());   // 初始化处理器
  // fi2();           // 初始化文件
  // AIA();           // 初始化AI
  // ko2().catch(console.error); // 初始化内核选项
  // DH2();           // 初始化数据处理

  // 权限检查
  if (mode === "bypassPermissions") {
    if (process.platform !== "win32" && typeof process.getuid === "function" && process.getuid() === 0 && !process.env.IS_SANDBOX) {
      console.error("--dangerously-skip-permissions cannot be used with root/sudo privileges for security reasons");
      process.exit(1);
    }
  }

  // TODO_REFACTOR: 查找D9配置获取函数定义
  // const config = D9(); // 获取配置
  // 处理会话统计和反馈...

  console.log('App setup completed');
};

// 获取当前工作目录函数 - 重构自第351810行的zS (process.cwd)
const getCurrentWorkingDirectory = () => {
  return process.cwd();
};

// 遥测事件记录函数 - 重构自第298748-298750行的E1函数
const trackEvent = async (eventName, properties) => {
  // 检查是否禁用遥测
  if (process.env.CLAUDE_CODE_USE_BEDROCK ||
      process.env.CLAUDE_CODE_USE_VERTEX ||
      process.env.DISABLE_TELEMETRY ||
      process.env.CLAUDE_CODE_DISABLE_NONESSENTIAL_TRAFFIC) {
    return;
  }

  try {
    // TODO_REFACTOR: 查找ey函数定义 - 获取遥测客户端
    // const telemetryClient = await ey();
    // if (!telemetryClient) return;

    // TODO_REFACTOR: 查找M7和mY函数定义 - 获取模型信息
    // const model = properties.model ? String(properties.model) : M7();
    // const modelInfo = mY(model);

    // TODO_REFACTOR: 查找qB函数定义 - 获取会话ID
    // const sessionId = qB();

    const eventData = {
      ...properties,
      // model,
      // sessionId,
      userType: "external",
      // ...modelInfo.length > 0 ? { betas: modelInfo.join(",") } : {}
    };

    // TODO_REFACTOR: 查找遥测客户端的logEvent方法
    // telemetryClient.logEvent(eventData);
    // await telemetryClient.flush();

    console.log(`[Telemetry] ${eventName}:`, eventData);
  } catch (error) {
    // 遥测错误不应该影响主要功能
    console.debug('Telemetry error:', error);
  }
};

// UUID验证函数 - 重构自第298844-298848行的hJ函数
const validateUUID = (uuid) => {
  if (typeof uuid !== "string") {
    return null;
  }

  // UUID正则表达式 - 重构自第298843行的zvQ常量
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;

  return uuidRegex.test(uuid) ? uuid : null;
};

// 非交互式会话检查函数 - 重构自第276315-276317行的a61函数
const isNonInteractiveSession = () => {
  // TODO_REFACTOR: 查找IB全局状态对象定义
  // return IB.isNonInteractiveSession;

  // 临时实现：检查是否为非交互式环境
  return !process.stdout.isTTY ||
         process.env.CI === 'true' ||
         process.env.GITHUB_ACTIONS === 'true' ||
         process.env.NODE_ENV === 'test';
};

// 设置屏幕函数 - 重构自第354302-354382行的iF4函数
const showSetupScreens = async (permissionMode) => {
  // 检查是否跳过设置屏幕
  // TODO_REFACTOR: 查找E6函数定义 - 检查CI环境
  // if (E6(false) || process.env.IS_DEMO) {
  //   return false;
  // }

  if (process.env.IS_DEMO) {
    return false;
  }

  // TODO_REFACTOR: 查找JA函数定义 - 获取应用配置
  // const config = JA();
  let hasShownOnboarding = false;

  // 检查是否需要显示主题和入门设置
  // if (!config.theme || !config.hasCompletedOnboarding) {
  //   hasShownOnboarding = true;
  //   // TODO_REFACTOR: 查找a5, z4, w5, Xa2等UI组件
  //   // await showOnboardingScreen();
  // }

  // 检查API密钥设置
  if (process.env.ANTHROPIC_API_KEY) {
    // TODO_REFACTOR: 查找RJ和PX1函数定义 - API密钥处理
    // const truncatedKey = RJ(process.env.ANTHROPIC_API_KEY);
    // if (PX1(truncatedKey) === "new") {
    //   await showApiKeyScreen(truncatedKey);
    // }
  }

  // 权限设置屏幕
  if (permissionMode !== "bypassPermissions" && process.env.CLAUBBIT !== "true") {
    // TODO_REFACTOR: 查找sc0函数定义 - 检查权限配置
    // if (!sc0()) {
    //   await showPermissionSetupScreen();
    // }

    // TODO_REFACTOR: 查找Lq函数定义 - 检查权限错误
    // const {errors} = Lq();
    // if (errors.length === 0) {
    //   await tQB(); // 权限测试
    // }

    // TODO_REFACTOR: 查找Ei2函数定义 - 检查是否需要显示权限屏幕
    // if (await Ei2()) {
    //   await showPermissionWarningScreen();
    // }
  }

  // 绕过权限模式确认
  if (permissionMode === "bypassPermissions") {
    // TODO_REFACTOR: 查找JA函数定义 - 检查绕过权限模式是否已接受
    // if (!JA().bypassPermissionsModeAccepted) {
    //   await showBypassPermissionsConfirmation();
    // }
  }

  console.log('Setup screens completed');
  return hasShownOnboarding;
};

// TODO_REFACTOR: 查找L$1登录函数定义
const performLogin = () => {
  throw new Error('Login function not yet refactored (original: L$1)');
};

// TODO_REFACTOR: 查找ao2工具权限上下文函数定义
const createToolPermissionContext = (options) => {
  throw new Error('Tool permission context function not yet refactored (original: ao2)');
};

// TODO_REFACTOR: 查找tF4输入处理函数定义
const processInput = async (input, format) => {
  throw new Error('Input processing function not yet refactored (original: tF4)');
};

/**
 * 创建主CLI程序
 * 重构自: 第354575-355224行
 */
async function createMainCLI() {
  // 初始化应用
  initializeApp();
  
  // 创建主命令
  const program = new Command();
  
  // 配置主命令
  program
    .name('claude')
    .description(`${PRODUCT_NAME} - starts an interactive session by default, use -p/--print for non-interactive output`)
    .argument('[prompt]', 'Your prompt', String)
    .helpOption('-h, --help', 'Display help for command')
    
    // 基础选项
    .option('-d, --debug', 'Enable debug mode', () => true)
    .option('--verbose', 'Override verbose mode setting from config', () => true)
    .option('-p, --print', 'Print response and exit (useful for pipes)', () => true)
    
    // 输出格式选项
    .addOption(new Option('--output-format <format>', 
      'Output format (only works with --print): "text" (default), "json" (single result), or "stream-json" (realtime streaming)')
      .choices(['text', 'json', 'stream-json']))
    
    // 输入格式选项  
    .addOption(new Option('--input-format <format>',
      'Input format (only works with --print): "text" (default), or "stream-json" (realtime streaming input)')
      .choices(['text', 'stream-json']))
    
    // MCP和权限选项
    .option('--mcp-debug', '[DEPRECATED. Use --debug instead] Enable MCP debug mode (shows MCP server errors)', () => true)
    .option('--dangerously-skip-permissions', 'Bypass all permission checks. Recommended only for sandboxes with no internet access.', () => true)
    
    // 高级选项
    .addOption(new Option('--max-turns <turns>',
      'Maximum number of agentic turns in non-interactive mode. This will early exit the conversation after the specified number of turns. (only works with --print)')
      .argParser(Number)
      .hideHelp())
    
    // 工具控制选项
    .option('--allowedTools <tools...>', 'Comma or space-separated list of tool names to allow (e.g. "Bash(git:*) Edit")')
    .option('--disallowedTools <tools...>', 'Comma or space-separated list of tool names to deny (e.g. "Bash(git:*) Edit")')
    .option('--mcp-config <file or string>', 'Load MCP servers from a JSON file or string')
    
    // 隐藏选项
    .addOption(new Option('--permission-prompt-tool <tool>',
      'MCP tool to use for permission prompts (only works with --print)')
      .argParser(String)
      .hideHelp())
    
    .addOption(new Option('--system-prompt <prompt>',
      'System prompt to use for the session  (only works with --print)')
      .argParser(String)
      .hideHelp())
    
    .addOption(new Option('--append-system-prompt <prompt>',
      'Append a system prompt to the default system prompt')
      .argParser(String))
    
    // 权限模式选项
    .addOption(new Option('--permission-mode <mode>',
      'Permission mode to use for the session')
      .argParser(String)
      .choices(PERMISSION_MODES))
    
    // 会话控制选项
    .option('-c, --continue', 'Continue the most recent conversation', () => true)
    .option('-r, --resume [sessionId]', 'Resume a conversation - provide a session ID or interactively select a conversation to resume', (value) => value || true)
    
    // 模型选项
    .option('--model <model>', 'Model for the current session. Provide an alias for the latest model (e.g. \'sonnet\' or \'opus\') or a model\'s full name (e.g. \'claude-sonnet-4-20250514\').')
    .option('--fallback-model <model>', 'Enable automatic fallback to specified model when default model is overloaded (only works with --print)')
    
    // 其他选项
    .option('--add-dir <directories...>', 'Additional directories to allow tool access to')
    .option('--ide', 'Automatically connect to IDE on startup if exactly one valid IDE is available', () => true)
    .option('--strict-mcp-config', 'Only use MCP servers from --mcp-config, ignoring all other MCP configurations', () => true)
    .option('--session-id <uuid>', 'Use a specific session ID for the conversation (must be a valid UUID)')
    
    // 主命令处理逻辑
    .action(async (prompt, options) => {
      await handleMainCommand(prompt, options);
    });

  return program;
}

/**
 * 主命令处理函数
 * 重构自: 第354578-354834行的action函数
 */
async function handleMainCommand(prompt, options) {
  // 解构选项
  const {
    debug = false,
    verbose = false,
    print,
    dangerouslySkipPermissions,
    allowedTools = [],
    disallowedTools = [],
    mcpConfig,
    outputFormat,
    inputFormat,
    permissionMode,
    addDir = [],
    fallbackModel,
    ide = false,
    sessionId
  } = options;
  
  const strictMcpConfig = options.strictMcpConfig || false;
  
  // 验证session ID
  if (sessionId) {
    if (options.continue || options.resume) {
      process.stderr.write(chalk.red(`Error: --session-id cannot be used with --continue or --resume.\n`));
      process.exit(1);
    }
    
    const validatedSessionId = validateUUID(sessionId);
    if (!validatedSessionId) {
      process.stderr.write(chalk.red(`Error: Invalid session ID. Must be a valid UUID.\n`));
      process.exit(1);
    }
    
    // TODO_REFACTOR: 查找Bc0会话检查函数定义
    // if (isSessionInUse(validatedSessionId)) {
    //   process.stderr.write(chalk.red(`Error: Session ID ${validatedSessionId} is already in use.\n`));
    //   process.exit(1);
    // }
  }
  
  // 检查认证状态
  const authenticated = isAuthenticated();
  
  // 验证fallback model
  if (fallbackModel && options.model && fallbackModel === options.model) {
    process.stderr.write(chalk.red(`Error: Fallback model cannot be the same as the main model. Please specify a different model for --fallback-model.\n`));
    process.exit(1);
  }
  
  // TODO_REFACTOR: 查找no2权限模式处理函数定义
  const permissionModeResolved = permissionMode; // 临时占位符
  
  // 处理MCP配置
  let dynamicMcpConfig;
  if (mcpConfig) {
    try {
      // TODO_REFACTOR: 查找MCP配置处理逻辑
      console.log('MCP config processing placeholder');
    } catch (error) {
      console.error(`Error: ${error instanceof Error ? error.message : String(error)}`);
      process.exit(1);
    }
  }
  
  // 检查认证和显示设置屏幕
  if (!authenticated) {
    const setupResult = await showSetupScreens(permissionModeResolved);
    if (setupResult && prompt?.trim().toLowerCase() === '/login') {
      prompt = '';
    }
    if (!setupResult) {
      performLogin();
    }
  }
  
  // 创建工具权限上下文
  const { toolPermissionContext, warnings } = createToolPermissionContext({
    allowedToolsCli: allowedTools,
    disallowedToolsCli: disallowedTools,
    permissionMode: permissionModeResolved,
    addDirs: addDir
  });
  
  // 显示警告
  warnings.forEach(warning => {
    console.error(warning);
  });
  
  // 验证输入格式
  if (inputFormat && inputFormat !== 'text' && inputFormat !== 'stream-json') {
    console.error(`Error: Invalid input format "${inputFormat}".`);
    process.exit(1);
  }
  
  if (inputFormat === 'stream-json' && outputFormat !== 'stream-json') {
    console.error('Error: --input-format=stream-json requires output-format=stream-json.');
    process.exit(1);
  }
  
  // 处理输入
  const processedInput = await processInput(prompt || '', inputFormat ?? 'text');
  
  // TODO_REFACTOR: 继续处理剩余的主命令逻辑
  console.log('Main command processing placeholder - more logic to be refactored');
}

/**
 * 完整的CLI程序创建函数
 * 重构自: 第354575-355224行的完整CLI系统
 */
async function createCompleteCLI() {
  // 创建主程序
  const program = await createMainCLI();

  // 添加子命令
  const { createConfigCommand } = require('./commands/config');
  const { createMCPCommand } = require('./commands/mcp');
  const { createSystemCommands } = require('./commands/system');

  // 注册子命令
  createConfigCommand(program);
  createMCPCommand(program);
  createSystemCommands(program);

  // 设置版本信息
  // TODO_REFACTOR: 查找版本常量定义
  const VERSION = '1.0.53'; // 临时占位符
  program.version(`${VERSION} (${PRODUCT_NAME})`, '-v, --version', 'Output the version number');

  // 解析命令行参数
  await program.parseAsync(process.argv);

  return program;
}

/**
 * 清理函数
 * 重构自: 第355219-355221行的AY4函数
 */
function cleanup() {
  // TODO_REFACTOR: 查找l_1常量定义
  const CURSOR_RESTORE = ''; // 临时占位符

  const output = process.stderr.isTTY ? process.stderr :
                 process.stdout.isTTY ? process.stdout : undefined;

  output?.write(`\x1B[?25h${CURSOR_RESTORE}`);
}

/**
 * 主入口函数
 * 重构自: 第355222-355224行的导出和调用
 */
async function main() {
  // 执行应用初始化
  await initializeApp();

  try {
    await createCompleteCLI();
  } catch (error) {
    console.error('CLI Error:', error);
    process.exit(1);
  }
}

// 导出函数 (重构自第355223行)
module.exports = {
  createMainCLI,
  handleMainCommand,
  createCompleteCLI,
  cleanup,
  main,
  // 原始导出映射
  showSetupScreens: showSetupScreens, // 原始: iF4
  setup: setupApp,                    // 原始: US
  completeOnboarding: null            // 原始: pF4 - TODO_REFACTOR
};

// 如果直接运行此文件，执行main函数
if (require.main === module) {
  main().catch(console.error);
}
