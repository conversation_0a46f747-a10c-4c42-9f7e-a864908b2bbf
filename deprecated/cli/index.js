/**
 * Claude Code CLI 主入口
 * 从原始文件第354800-355224行重构而来
 */

const { Command } = require('commander');
const chalk = require('chalk');

// 导入子模块
const configCommands = require('./commands/config');
const mcpCommands = require('./commands/mcp');
const setupCommands = require('./commands/setup');
const updateCommands = require('./commands/update');

// 导入核心功能
const { setup, showSetupScreens, completeOnboarding } = require('../core/setup');
const { trackEvent } = require('../analytics/events');

/**
 * 创建主CLI程序
 * 原始函数位置: 第354800行附近
 */
function createCLIProgram() {
    const program = new Command();
    
    // 设置程序基本信息
    program
        .name('claude')
        .description('Claude Code - AI-powered coding assistant')
        .version('1.0.53', '-v, --version', 'Output the version number');

    // 添加config子命令
    // 原始位置: 第354835行 let B = A.command("config")
    const configCommand = program
        .command('config')
        .description('Manage configuration (eg. claude config set -g theme dark)')
        .helpOption('-h, --help', 'Display help for command');
    
    configCommands.setupConfigCommands(configCommand);

    // 添加mcp子命令  
    // 原始位置: 第354881行 let Q = A.command("mcp")
    const mcpCommand = program
        .command('mcp')
        .description('Configure and manage MCP servers')
        .helpOption('-h, --help', 'Display help for command');
    
    mcpCommands.setupMcpCommands(mcpCommand);

    // 添加其他主要命令
    setupCommands.setupMainCommands(program);
    updateCommands.setupUpdateCommands(program);

    return program;
}

/**
 * 运行CLI程序
 * 原始位置: 第355216行 await A.parseAsync(process.argv)
 */
async function runCLI() {
    try {
        const program = createCLIProgram();
        await program.parseAsync(process.argv);
        return program;
    } catch (error) {
        console.error(chalk.red('Error:'), error.message);
        process.exit(1);
    }
}

/**
 * 清理函数
 * 原始位置: 第355219-355221行
 */
function cleanup() {
    const tty = process.stderr.isTTY ? process.stderr : 
                process.stdout.isTTY ? process.stdout : undefined;
    tty?.write(`\x1B[?25h`); // 显示光标
}

// 导出主要功能
module.exports = {
    createCLIProgram,
    runCLI,
    cleanup,
    // 重新导出核心功能，保持与原始导出一致
    // 原始位置: 第355223行 export {iF4 as showSetupScreens, US as setup, pF4 as completeOnboarding}
    showSetupScreens,
    setup,
    completeOnboarding
};
