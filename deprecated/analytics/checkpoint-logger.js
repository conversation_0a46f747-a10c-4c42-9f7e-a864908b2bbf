/**
 * 检查点日志记录
 * 处理检查点相关的日志记录和分析
 */

const fs = require('fs');
const path = require('path');
const os = require('os');
const { createDirectory } = require('../utils/file-system');
const { logInfo, logError } = require('../utils/logger');

// 检查点日志目录
const CHECKPOINT_LOG_DIR = path.join(os.homedir(), '.claude-code', 'checkpoints');
const CHECKPOINT_LOG_FILE = path.join(CHECKPOINT_LOG_DIR, 'checkpoints.log');

/**
 * 确保检查点日志目录存在
 */
function ensureCheckpointLogDirectory() {
    try {
        createDirectory(CHECKPOINT_LOG_DIR);
    } catch (error) {
        logError('Failed to create checkpoint log directory:', error);
    }
}

/**
 * 保存检查点到日志
 * 原始函数: Fa1(checkpoint)
 */
async function saveCheckpointToLog(checkpoint) {
    try {
        ensureCheckpointLogDirectory();
        
        const logEntry = {
            timestamp: new Date().toISOString(),
            checkpoint: {
                commit: checkpoint.commit,
                timestamp: checkpoint.timestamp,
                label: checkpoint.label
            },
            type: 'checkpoint_created'
        };
        
        const logLine = JSON.stringify(logEntry) + '\n';
        fs.appendFileSync(CHECKPOINT_LOG_FILE, logLine, 'utf8');
        
        logInfo(`Checkpoint logged: ${checkpoint.label} (${checkpoint.commit.substring(0, 8)})`);
    } catch (error) {
        logError('Failed to save checkpoint to log:', error);
    }
}

/**
 * 读取检查点日志
 */
function readCheckpointLog() {
    try {
        if (!fs.existsSync(CHECKPOINT_LOG_FILE)) {
            return [];
        }
        
        const content = fs.readFileSync(CHECKPOINT_LOG_FILE, 'utf8');
        const lines = content.trim().split('\n').filter(line => line.trim());
        
        const checkpoints = [];
        for (const line of lines) {
            try {
                const entry = JSON.parse(line);
                if (entry.type === 'checkpoint_created' && entry.checkpoint) {
                    checkpoints.push({
                        ...entry.checkpoint,
                        timestamp: new Date(entry.checkpoint.timestamp),
                        loggedAt: new Date(entry.timestamp)
                    });
                }
            } catch (parseError) {
                logError('Failed to parse checkpoint log entry:', parseError);
            }
        }
        
        return checkpoints.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
    } catch (error) {
        logError('Failed to read checkpoint log:', error);
        return [];
    }
}

/**
 * 获取检查点统计信息
 */
function getCheckpointStats() {
    try {
        const checkpoints = readCheckpointLog();
        
        if (checkpoints.length === 0) {
            return {
                total: 0,
                lastCheckpoint: null,
                averageInterval: 0,
                oldestCheckpoint: null
            };
        }
        
        const now = Date.now();
        const lastCheckpoint = checkpoints[0];
        const oldestCheckpoint = checkpoints[checkpoints.length - 1];
        
        // 计算平均间隔
        let totalInterval = 0;
        for (let i = 0; i < checkpoints.length - 1; i++) {
            const interval = checkpoints[i].timestamp.getTime() - checkpoints[i + 1].timestamp.getTime();
            totalInterval += interval;
        }
        
        const averageInterval = checkpoints.length > 1 ? totalInterval / (checkpoints.length - 1) : 0;
        
        return {
            total: checkpoints.length,
            lastCheckpoint: {
                commit: lastCheckpoint.commit,
                label: lastCheckpoint.label,
                timestamp: lastCheckpoint.timestamp,
                timeSince: now - lastCheckpoint.timestamp.getTime()
            },
            oldestCheckpoint: {
                commit: oldestCheckpoint.commit,
                label: oldestCheckpoint.label,
                timestamp: oldestCheckpoint.timestamp,
                timeSince: now - oldestCheckpoint.timestamp.getTime()
            },
            averageInterval: Math.round(averageInterval / 1000 / 60) // 转换为分钟
        };
    } catch (error) {
        logError('Failed to get checkpoint stats:', error);
        return {
            total: 0,
            lastCheckpoint: null,
            averageInterval: 0,
            oldestCheckpoint: null
        };
    }
}

/**
 * 清理旧的检查点日志
 */
function cleanupOldCheckpointLogs(daysToKeep = 30) {
    try {
        const checkpoints = readCheckpointLog();
        const cutoffTime = Date.now() - (daysToKeep * 24 * 60 * 60 * 1000);
        
        const recentCheckpoints = checkpoints.filter(checkpoint => 
            checkpoint.timestamp.getTime() > cutoffTime
        );
        
        if (recentCheckpoints.length < checkpoints.length) {
            // 重写日志文件，只保留最近的检查点
            const logEntries = recentCheckpoints.map(checkpoint => {
                const logEntry = {
                    timestamp: checkpoint.loggedAt ? checkpoint.loggedAt.toISOString() : new Date().toISOString(),
                    checkpoint: {
                        commit: checkpoint.commit,
                        timestamp: checkpoint.timestamp,
                        label: checkpoint.label
                    },
                    type: 'checkpoint_created'
                };
                return JSON.stringify(logEntry);
            });
            
            fs.writeFileSync(CHECKPOINT_LOG_FILE, logEntries.join('\n') + '\n', 'utf8');
            
            const removedCount = checkpoints.length - recentCheckpoints.length;
            logInfo(`Cleaned up ${removedCount} old checkpoint log entries`);
        }
    } catch (error) {
        logError('Failed to cleanup old checkpoint logs:', error);
    }
}

/**
 * 导出检查点日志
 */
function exportCheckpointLog(outputPath) {
    try {
        const checkpoints = readCheckpointLog();
        
        const exportData = {
            exportedAt: new Date().toISOString(),
            version: '1.0',
            checkpoints: checkpoints.map(checkpoint => ({
                commit: checkpoint.commit,
                label: checkpoint.label,
                timestamp: checkpoint.timestamp.toISOString(),
                loggedAt: checkpoint.loggedAt ? checkpoint.loggedAt.toISOString() : null
            }))
        };
        
        fs.writeFileSync(outputPath, JSON.stringify(exportData, null, 2), 'utf8');
        logInfo(`Checkpoint log exported to: ${outputPath}`);
        
        return true;
    } catch (error) {
        logError('Failed to export checkpoint log:', error);
        return false;
    }
}

/**
 * 导入检查点日志
 */
function importCheckpointLog(inputPath) {
    try {
        if (!fs.existsSync(inputPath)) {
            throw new Error(`Import file not found: ${inputPath}`);
        }
        
        const content = fs.readFileSync(inputPath, 'utf8');
        const importData = JSON.parse(content);
        
        if (!importData.checkpoints || !Array.isArray(importData.checkpoints)) {
            throw new Error('Invalid import data format');
        }
        
        ensureCheckpointLogDirectory();
        
        // 追加导入的检查点到日志
        for (const checkpoint of importData.checkpoints) {
            const logEntry = {
                timestamp: new Date().toISOString(),
                checkpoint: {
                    commit: checkpoint.commit,
                    timestamp: checkpoint.timestamp,
                    label: checkpoint.label
                },
                type: 'checkpoint_created'
            };
            
            const logLine = JSON.stringify(logEntry) + '\n';
            fs.appendFileSync(CHECKPOINT_LOG_FILE, logLine, 'utf8');
        }
        
        logInfo(`Imported ${importData.checkpoints.length} checkpoints from: ${inputPath}`);
        return true;
    } catch (error) {
        logError('Failed to import checkpoint log:', error);
        return false;
    }
}

module.exports = {
    saveCheckpointToLog,
    readCheckpointLog,
    getCheckpointStats,
    cleanupOldCheckpointLogs,
    exportCheckpointLog,
    importCheckpointLog,
    CHECKPOINT_LOG_DIR,
    CHECKPOINT_LOG_FILE
};
