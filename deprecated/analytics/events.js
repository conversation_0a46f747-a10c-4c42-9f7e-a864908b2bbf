/**
 * 事件追踪模块
 * 处理分析事件的发送和记录
 * 对应原始代码中的事件追踪函数
 */

/**
 * 追踪事件
 * 原始函数名: E1
 */
function trackEvent(eventName, properties = {}) {
    try {
        // 添加通用属性
        const eventData = {
            event: eventName,
            properties: {
                ...properties,
                timestamp: Date.now(),
                version: '1.0.53',
                platform: process.platform,
                nodeVersion: process.version
            }
        };
        
        // 在开发模式下打印事件
        if (process.env.DEBUG || process.env.NODE_ENV === 'development') {
            console.log('Event tracked:', JSON.stringify(eventData, null, 2));
        }
        
        // TODO: 实现实际的事件发送逻辑
        // 这里应该发送到分析服务器
        sendEventToAnalytics(eventData);
        
    } catch (error) {
        // 事件追踪失败不应该影响主要功能
        console.warn('Failed to track event:', error.message);
    }
}

/**
 * 发送事件到分析服务器
 */
async function sendEventToAnalytics(eventData) {
    // TODO: 实现实际的网络请求
    // 这里应该使用axios或其他HTTP客户端发送到Anthropic的分析服务器
    
    // 暂时只是记录到本地日志
    logEventLocally(eventData);
}

/**
 * 本地记录事件
 */
function logEventLocally(eventData) {
    const fs = require('fs');
    const path = require('path');
    const os = require('os');
    
    try {
        const logDir = path.join(os.homedir(), '.claude-code', 'logs');
        const logFile = path.join(logDir, 'events.log');
        
        // 确保日志目录存在
        if (!fs.existsSync(logDir)) {
            fs.mkdirSync(logDir, { recursive: true });
        }
        
        // 追加事件到日志文件
        const logEntry = JSON.stringify(eventData) + '\n';
        fs.appendFileSync(logFile, logEntry, 'utf8');
        
    } catch (error) {
        console.warn('Failed to log event locally:', error.message);
    }
}

/**
 * 批量追踪事件
 */
function trackEvents(events) {
    for (const event of events) {
        if (event.name && typeof event.name === 'string') {
            trackEvent(event.name, event.properties || {});
        }
    }
}

/**
 * 追踪用户操作事件
 */
function trackUserAction(action, context = {}) {
    trackEvent('user_action', {
        action: action,
        context: context
    });
}

/**
 * 追踪错误事件
 */
function trackError(error, context = {}) {
    trackEvent('error', {
        error: error.message,
        stack: error.stack,
        context: context
    });
}

/**
 * 追踪性能事件
 */
function trackPerformance(operation, duration, metadata = {}) {
    trackEvent('performance', {
        operation: operation,
        duration: duration,
        metadata: metadata
    });
}

module.exports = {
    trackEvent,
    trackEvents,
    trackUserAction,
    trackError,
    trackPerformance
};
