/**
 * Claude Code - Application Initialization System
 * 
 * 重构来源: cli-format-all.js 第354384-354574行 (190行)
 * 重构时间: 2025-01-19
 * 
 * 应用启动、初始化和设置系统
 */

// Node.js 内置模块
const fs = require('fs');

// 第三方依赖
const chalk = require('chalk');

// TODO_REFACTOR: 以下是未重构的依赖，需要后续替换

// TODO_REFACTOR: 查找JA应用状态获取函数定义
const getAppState = () => {
  throw new Error('App state function not yet refactored (original: JA)');
};

// TODO_REFACTOR: 查找b0应用状态设置函数定义
const setAppState = (state) => {
  throw new Error('App state setter function not yet refactored (original: b0)');
};

// TODO_REFACTOR: 查找$FA计数器函数定义
const getCounter = () => {
  throw new Error('Counter function not yet refactored (original: $FA)');
};

// TODO_REFACTOR: 查找lz Git检测函数定义
const isGitRepository = async () => {
  throw new Error('Git detection function not yet refactored (original: lz)');
};

// TODO_REFACTOR: 查找Mr工作树计数函数定义
const getWorktreeCount = async () => {
  throw new Error('Worktree count function not yet refactored (original: Mr)');
};

// TODO_REFACTOR: 查找E1遥测记录函数定义
const trackEvent = (eventName, properties) => {
  throw new Error('Track event function not yet refactored (original: E1)');
};

// TODO_REFACTOR: 查找Q6B初始化函数定义
const initializeQ6B = () => {
  throw new Error('Q6B initialization function not yet refactored (original: Q6B)');
};

// TODO_REFACTOR: 查找I6B初始化函数定义
const initializeI6B = () => {
  throw new Error('I6B initialization function not yet refactored (original: I6B)');
};

// TODO_REFACTOR: 查找D6B初始化函数定义
const initializeD6B = () => {
  throw new Error('D6B initialization function not yet refactored (original: D6B)');
};

// TODO_REFACTOR: 查找G6B初始化函数定义
const initializeG6B = () => {
  throw new Error('G6B initialization function not yet refactored (original: G6B)');
};

// TODO_REFACTOR: 查找ErA初始化函数定义
const initializeErA = () => {
  throw new Error('ErA initialization function not yet refactored (original: ErA)');
};

// TODO_REFACTOR: 查找FFA会话ID设置函数定义
const setSessionId = (sessionId) => {
  throw new Error('Session ID setter function not yet refactored (original: FFA)');
};

// TODO_REFACTOR: 查找T8A初始化函数定义
const initializeT8A = () => {
  throw new Error('T8A initialization function not yet refactored (original: T8A)');
};

// TODO_REFACTOR: 查找E6 CI环境检测函数定义
const isCIEnvironment = (flag) => {
  throw new Error('CI environment detection function not yet refactored (original: E6)');
};

// TODO_REFACTOR: 查找YA颜色工具定义
const colorUtils = chalk; // 临时占位符

// TODO_REFACTOR: 查找dE2 iTerm2恢复函数定义
const restoreITerm2Settings = () => {
  throw new Error('iTerm2 restore function not yet refactored (original: dE2)');
};

// TODO_REFACTOR: 查找oE1 Terminal.app恢复函数定义
const restoreTerminalAppSettings = async () => {
  throw new Error('Terminal.app restore function not yet refactored (original: oE1)');
};

// TODO_REFACTOR: 查找v1错误处理函数定义
const handleError = (error) => {
  throw new Error('Error handler function not yet refactored (original: v1)');
};

// TODO_REFACTOR: 查找D9会话状态获取函数定义
const getSessionState = () => {
  throw new Error('Session state function not yet refactored (original: D9)');
};

// TODO_REFACTOR: 查找S6会话状态设置函数定义
const setSessionState = (state) => {
  throw new Error('Session state setter function not yet refactored (original: S6)');
};

// TODO_REFACTOR: 查找NX2 ripgrep函数定义
const executeRipgrep = (args) => {
  throw new Error('Ripgrep function not yet refactored (original: NX2)');
};

// TODO_REFACTOR: 查找AY4清理函数定义
const cleanup = () => {
  throw new Error('Cleanup function not yet refactored (original: AY4)');
};

// TODO_REFACTOR: 查找_FA设置函数定义
const setPrintMode = (isPrint) => {
  throw new Error('Print mode setter function not yet refactored (original: _FA)');
};

// TODO_REFACTOR: 查找yFA设置函数定义
const setInteractiveMode = (isInteractive) => {
  throw new Error('Interactive mode setter function not yet refactored (original: yFA)');
};

// TODO_REFACTOR: 查找fFA设置函数定义
const setEntrypoint = (entrypoint) => {
  throw new Error('Entrypoint setter function not yet refactored (original: fFA)');
};

// TODO_REFACTOR: 查找wX2初始化函数定义
const initializeWX2 = () => {
  throw new Error('WX2 initialization function not yet refactored (original: wX2)');
};

// TODO_REFACTOR: 查找vc0清理函数定义
const cleanupShellSnapshot = async () => {
  throw new Error('Shell snapshot cleanup function not yet refactored (original: vc0)');
};

// TODO_REFACTOR: 查找dF4文件打开函数定义
const openFile = (path, mode) => {
  throw new Error('File open function not yet refactored (original: dF4)');
};

// TODO_REFACTOR: 查找mF4流构造函数定义
const createStream = (fd) => {
  throw new Error('Stream constructor function not yet refactored (original: mF4)');
};

/**
 * 增加启动计数并记录遥测
 * 重构自: 第354384-354392行的nF4函数
 */
function incrementStartupCount() {
  const appState = getAppState();
  setAppState({
    ...appState,
    numStartups: (appState.numStartups ?? 0) + 1
  });
  
  recordStartupTelemetry();
  
  const counter = getCounter();
  counter?.add(1);
}

/**
 * 记录启动遥测
 * 重构自: 第354393-354399行的aF4函数
 */
async function recordStartupTelemetry() {
  const [isGit, worktreeCount] = await Promise.all([
    isGitRepository(),
    getWorktreeCount()
  ]);
  
  trackEvent("tengu_startup_telemetry", {
    is_git: isGit,
    worktree_count: worktreeCount
  });
}

/**
 * 应用初始化
 * 重构自: 第354400-354406行的sF4函数
 */
function initializeApp() {
  initializeQ6B();
  initializeI6B();
  initializeD6B();
  initializeG6B();
  initializeErA();
}

/**
 * 主设置函数
 * 重构自: 第354407-354500行的US函数
 */
async function setupApp(workingDir, permissionMode, isPrint, isInteractive, sessionId) {
  // 检查Node.js版本
  const nodeVersion = process.version.match(/^v(\d+)\./)?.[1];
  if (!nodeVersion || parseInt(nodeVersion) < 18) {
    console.error(colorUtils.bold.red("Error: Claude Code requires Node.js version 18 or higher."));
    process.exit(1);
  }
  
  // 设置会话ID
  if (sessionId) {
    setSessionId(sessionId);
  }
  
  // 初始化系统
  initializeT8A();
  
  if (isCIEnvironment(false)) {
    console.warn("Running in CI environment - interactive features are limited");
  }
  
  // 恢复终端设置
  const iterm2Status = restoreITerm2Settings();
  if (iterm2Status.status === "restored") {
    console.log(colorUtils.yellow("Detected an interrupted iTerm2 setup. Your original settings have been restored. You may need to restart iTerm2 for the changes to take effect."));
  } else if (iterm2Status.status === "failed") {
    console.error(colorUtils.red(`Failed to restore iTerm2 settings. Please manually restore your original settings with: defaults import com.googlecode.iterm2 ${iterm2Status.backupPath}.`));
  }
  
  try {
    const terminalStatus = await restoreTerminalAppSettings();
    if (terminalStatus.status === "restored") {
      console.log(colorUtils.yellow("Detected an interrupted Terminal.app setup. Your original settings have been restored. You may need to restart Terminal.app for the changes to take effect."));
    } else if (terminalStatus.status === "failed") {
      console.error(colorUtils.red(`Failed to restore Terminal.app settings. Please manually restore your original settings with: defaults import com.apple.Terminal ${terminalStatus.backupPath}.`));
    }
  } catch (error) {
    handleError(error instanceof Error ? error : new Error(String(error)));
  }
  
  const printMode = isPrint ?? false;
  
  // TODO_REFACTOR: 大量的初始化函数调用需要后续重构
  // 这里包含了很多系统初始化调用，需要逐个重构
  console.log('System initialization - many functions need refactoring');
  
  // 权限检查
  if (permissionMode === "bypassPermissions") {
    if (process.platform !== "win32" && 
        typeof process.getuid === "function" && 
        process.getuid() === 0 && 
        !process.env.IS_SANDBOX) {
      console.error("--dangerously-skip-permissions cannot be used with root/sudo privileges for security reasons");
      process.exit(1);
    }
  }
  
  // 处理会话状态和遥测
  const sessionState = getSessionState();
  if (sessionState.lastCost !== undefined && sessionState.lastDuration !== undefined) {
    trackEvent("tengu_exit", {
      last_session_cost: sessionState.lastCost,
      last_session_api_duration: sessionState.lastAPIDuration,
      last_session_duration: sessionState.lastDuration,
      last_session_lines_added: sessionState.lastLinesAdded,
      last_session_lines_removed: sessionState.lastLinesRemoved,
      last_session_total_input_tokens: sessionState.lastTotalInputTokens,
      last_session_total_output_tokens: sessionState.lastTotalOutputTokens,
      last_session_total_cache_creation_input_tokens: sessionState.lastTotalCacheCreationInputTokens,
      last_session_total_cache_read_input_tokens: sessionState.lastTotalCacheReadInputTokens,
      last_session_id: sessionState.lastSessionId
    });
    
    // 清理会话状态
    setSessionState({
      ...sessionState,
      lastCost: undefined,
      lastAPIDuration: undefined,
      lastDuration: undefined,
      lastLinesAdded: undefined,
      lastLinesRemoved: undefined,
      lastTotalInputTokens: undefined,
      lastTotalOutputTokens: undefined,
      lastTotalCacheCreationInputTokens: undefined,
      lastTotalCacheReadInputTokens: undefined,
      lastSessionId: undefined
    });
  }
  
  // 处理待处理的退出反馈
  if (sessionState.pendingExitFeedback) {
    const feedback = sessionState.pendingExitFeedback;
    trackEvent("tengu_exit_feedback", {
      feedback_choice: feedback.feedbackChoice,
      feedback_details: feedback.feedbackDetails,
      last_session_id: feedback.sessionId,
      model: feedback.model
    });
    
    setSessionState({
      ...sessionState,
      pendingExitFeedback: undefined
    });
  }
}

/**
 * 主初始化函数
 * 重构自: 第354501-354540行的rF4函数
 */
async function initializeMain() {
  // 处理ripgrep特殊情况
  if (process.argv[2] === "--ripgrep") {
    const args = process.argv.slice(3);
    process.exit(executeRipgrep(args));
  }
  
  // 设置入口点环境变量
  if (!process.env.CLAUDE_CODE_ENTRYPOINT) {
    process.env.CLAUDE_CODE_ENTRYPOINT = "cli";
  }
  
  // 设置进程事件处理
  process.on("exit", () => {
    cleanup();
  });
  
  process.on("SIGINT", () => {
    process.exit(0);
  });
  
  // 确定模式
  const args = process.argv.slice(2);
  const isPrint = args.includes("-p") || args.includes("--print") || !process.stdout.isTTY;
  
  setPrintMode(isPrint);
  setInteractiveMode(!isPrint);
  
  // 确定入口点类型
  const entrypoint = (() => {
    if (process.env.GITHUB_ACTIONS === "true") {
      return "github-action";
    }
    if (process.env.CLAUDE_CODE_ENTRYPOINT === "sdk-ts") {
      return "sdk-typescript";
    }
    if (process.env.CLAUDE_CODE_ENTRYPOINT === "sdk-cli") {
      return "sdk-cli";
    }
    return "cli";
  })();
  
  setEntrypoint(entrypoint);
  
  // 初始化系统
  const initResult = initializeWX2();
  if (initResult instanceof Promise) {
    await initResult;
  }
  
  // 清理Shell快照
  cleanupShellSnapshot().catch((error) => {
    handleError(new Error(`Shell snapshot cleanup failed: ${error}`));
  });
  
  // 设置进程标题
  process.title = "claude";
  
  // TODO_REFACTOR: 查找eF4主CLI函数定义
  // await startMainCLI();
}

/**
 * 创建渲染选项
 * 重构自: 第354541-354561行的oF4函数
 */
function createRenderOptions(exitOnCtrlC) {
  const options = {
    exitOnCtrlC: exitOnCtrlC,
    onFlicker() {
      trackEvent("tengu_flicker", {});
    }
  };
  
  // 处理非TTY输入
  if (!process.stdin.isTTY && !isCIEnvironment(false) && !process.argv.includes("mcp")) {
    if (process.platform !== "win32") {
      try {
        const fd = openFile("/dev/tty", "r");
        options.stdin = createStream(fd);
      } catch (error) {
        handleError(error);
      }
    }
  }
  
  return options;
}

/**
 * 处理输入
 * 重构自: 第354562-354573行的tF4函数
 */
async function processInput(prompt, inputFormat) {
  if (!process.stdin.isTTY && !process.argv.includes("mcp")) {
    if (inputFormat === "stream-json") {
      return process.stdin;
    }
    
    let input = "";
    for await (const chunk of process.stdin) {
      input += chunk;
    }
    
    return [prompt, input].filter(Boolean).join('\n');
  }
  
  return prompt;
}

module.exports = {
  incrementStartupCount,
  recordStartupTelemetry,
  initializeApp,
  setupApp,
  initializeMain,
  createRenderOptions,
  processInput
};
