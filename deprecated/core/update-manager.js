/**
 * 更新管理器
 * 从原始文件第330048-330150行提取
 * 处理版本更新、变更日志获取和发布说明
 */

const axios = require('axios');
const semver = require('semver');

const { logError } = require('../utils/logger');
const { getAppState, saveAppState } = require('./state-manager');

// 常量定义
const MAX_RELEASE_NOTES = 5; // 原始: qQ4
const CHANGELOG_URL = "https://github.com/anthropics/claude-code/blob/main/CHANGELOG.md";
const CHANGELOG_RAW_URL = "https://raw.githubusercontent.com/anthropics/claude-code/refs/heads/main/CHANGELOG.md";

// 应用信息常量
const APP_INFO = {
    ISSUES_EXPLAINER: "report the issue at https://github.com/anthropics/claude-code/issues",
    PACKAGE_URL: "@anthropic-ai/claude-code",
    README_URL: "https://docs.anthropic.com/s/claude-code",
    VERSION: "1.0.53"
};

/**
 * 获取变更日志
 * 原始函数: P3A()
 */
async function fetchChangelog() {
    try {
        const response = await axios.get(CHANGELOG_RAW_URL);
        
        if (response.status === 200) {
            const currentState = getAppState();
            saveAppState({
                ...currentState,
                cachedChangelog: response.data,
                changelogLastFetched: Date.now()
            });
        }
    } catch (error) {
        logError(error instanceof Error ? error : new Error("Failed to fetch changelog"));
    }
}

/**
 * 获取缓存的变更日志
 * 原始函数: mB1()
 */
function getCachedChangelog() {
    return getAppState().cachedChangelog ?? "";
}

/**
 * 解析变更日志
 * 原始函数: Vo2(A)
 */
function parseChangelog(changelogText) {
    try {
        if (!changelogText) {
            return {};
        }
        
        const versions = {};
        const sections = changelogText.split(/^## /gm).slice(1);
        
        for (const section of sections) {
            const lines = section.trim().split('\n');
            if (lines.length === 0) {
                continue;
            }
            
            const header = lines[0];
            if (!header) {
                continue;
            }
            
            // 提取版本号（格式：版本号 - 日期）
            const version = header.split(' - ')[0]?.trim() || "";
            if (!version) {
                continue;
            }
            
            // 提取变更项目（以 "- " 开头的行）
            const changes = lines
                .slice(1)
                .filter(line => line.trim().startsWith("- "))
                .map(line => line.trim().substring(2).trim())
                .filter(Boolean);
            
            if (changes.length > 0) {
                versions[version] = changes;
            }
        }
        
        return versions;
    } catch (error) {
        logError(error instanceof Error ? error : new Error("Failed to parse changelog"));
        return {};
    }
}

/**
 * 获取发布说明
 * 原始函数: LQ4(A, B, Q=mB1())
 */
function getReleaseNotes(currentVersion, previousVersion, changelog = getCachedChangelog()) {
    try {
        const parsedChangelog = parseChangelog(changelog);
        const current = semver.coerce(currentVersion);
        const previous = previousVersion ? semver.coerce(previousVersion) : null;
        
        if (!previous || (current && semver.gt(current, previous, { loose: true }))) {
            return Object.entries(parsedChangelog)
                .filter(([version]) => !previous || semver.gt(version, previous, { loose: true }))
                .sort(([a], [b]) => semver.gt(a, b, { loose: true }) ? -1 : 1)
                .flatMap(([version, changes]) => changes)
                .filter(Boolean)
                .slice(0, MAX_RELEASE_NOTES);
        }
    } catch (error) {
        logError(error instanceof Error ? error : new Error("Failed to get release notes"));
        return [];
    }
    
    return [];
}

/**
 * 获取所有发布说明
 * 原始函数: S3A(A=mB1())
 */
function getAllReleaseNotes(changelog = getCachedChangelog()) {
    try {
        const parsedChangelog = parseChangelog(changelog);
        
        return Object.keys(parsedChangelog)
            .sort((a, b) => semver.gt(a, b, { loose: true }) ? 1 : -1)
            .map(version => {
                const changes = parsedChangelog[version];
                if (!changes || changes.length === 0) {
                    return null;
                }
                
                const validChanges = changes.filter(Boolean);
                if (validChanges.length === 0) {
                    return null;
                }
                
                return [version, validChanges];
            })
            .filter(item => item !== null);
    } catch (error) {
        logError(error instanceof Error ? error : new Error("Failed to get release notes"));
        return [];
    }
}

/**
 * 检查更新并获取发布说明
 * 原始函数: dB1(A, B={...}.VERSION)
 */
function checkForUpdatesAndReleaseNotes(previousVersion, currentVersion = APP_INFO.VERSION) {
    // 如果版本不同或没有缓存的变更日志，则获取最新的变更日志
    if (previousVersion !== currentVersion || !getCachedChangelog()) {
        fetchChangelog().catch(error => 
            logError(error instanceof Error ? error : new Error("Failed to fetch changelog"))
        );
    }
    
    const releaseNotes = getReleaseNotes(currentVersion, previousVersion);
    
    return {
        hasReleaseNotes: releaseNotes.length > 0,
        releaseNotes: releaseNotes
    };
}

/**
 * 格式化发布说明
 * 原始函数: Ko2(A)
 */
function formatReleaseNotes(releaseNotesData) {
    return releaseNotesData.map(([version, changes]) => {
        const header = `Version ${version}:`;
        const changesList = changes.map(change => `• ${change}`).join('\n');
        
        return `${header}\n${changesList}`;
    }).join('\n\n');
}

/**
 * 检查是否有新版本
 */
async function checkForNewVersion() {
    try {
        // 这里应该调用实际的版本检查API
        // 暂时返回模拟数据
        const response = await axios.get('https://api.github.com/repos/anthropics/claude-code/releases/latest');
        
        if (response.status === 200) {
            const latestVersion = response.data.tag_name.replace(/^v/, '');
            const currentVersion = APP_INFO.VERSION;
            
            const hasUpdate = semver.gt(latestVersion, currentVersion, { loose: true });
            
            return {
                hasUpdate,
                currentVersion,
                latestVersion,
                releaseUrl: response.data.html_url,
                releaseNotes: response.data.body
            };
        }
    } catch (error) {
        logError(error instanceof Error ? error : new Error("Failed to check for new version"));
    }
    
    return {
        hasUpdate: false,
        currentVersion: APP_INFO.VERSION,
        latestVersion: APP_INFO.VERSION,
        releaseUrl: null,
        releaseNotes: null
    };
}

/**
 * 下载并安装更新
 */
async function downloadAndInstallUpdate(version) {
    try {
        // 这里应该实现实际的下载和安装逻辑
        console.log(`Downloading and installing version ${version}...`);
        
        // 模拟下载过程
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        console.log(`Successfully installed version ${version}`);
        return true;
    } catch (error) {
        logError(error instanceof Error ? error : new Error("Failed to download and install update"));
        return false;
    }
}

/**
 * 获取更新状态
 */
function getUpdateStatus() {
    const state = getAppState();
    return {
        lastChecked: state.lastUpdateCheck || null,
        lastFetched: state.changelogLastFetched || null,
        hasCache: !!state.cachedChangelog,
        currentVersion: APP_INFO.VERSION
    };
}

module.exports = {
    fetchChangelog,
    getCachedChangelog,
    parseChangelog,
    getReleaseNotes,
    getAllReleaseNotes,
    checkForUpdatesAndReleaseNotes,
    formatReleaseNotes,
    checkForNewVersion,
    downloadAndInstallUpdate,
    getUpdateStatus,
    APP_INFO,
    CHANGELOG_URL,
    CHANGELOG_RAW_URL
};
