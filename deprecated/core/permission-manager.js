/**
 * 权限管理器
 * 从原始文件第295037-295100行提取
 * 处理文件访问权限、读写控制和忽略模式
 */

const path = require('path');
const { parsePatternRoot } = require('../utils/file-system');
const { getProjectSettings } = require('./project-settings');

/**
 * 权限类型枚举
 */
const PERMISSION_TYPES = {
    READ: 'read',
    EDIT: 'edit'
};

/**
 * 权限动作枚举
 */
const PERMISSION_ACTIONS = {
    ALLOW: 'allow',
    DENY: 'deny'
};

/**
 * 获取忽略模式映射
 * 原始函数: Jh(A)
 */
function getIgnorePatterns(context) {
    // 获取基于权限的模式映射
    const permissionPatterns = getPermissionPatterns(context, PERMISSION_TYPES.READ, PERMISSION_ACTIONS.DENY);
    const patternMap = new Map();
    
    // 转换权限模式为简单的模式列表
    for (const [root, patternDetails] of permissionPatterns.entries()) {
        patternMap.set(root, Array.from(patternDetails.keys()));
    }
    
    // 添加项目设置中的忽略模式
    const projectSettings = getProjectSettings();
    const ignorePatterns = projectSettings.ignorePatterns;
    
    if (ignorePatterns && ignorePatterns.length > 0) {
        for (const pattern of ignorePatterns) {
            const { relativePattern, root } = parsePatternRoot(pattern, "projectSettings");
            
            let existingPatterns = patternMap.get(root);
            if (existingPatterns === undefined) {
                existingPatterns = [relativePattern];
                patternMap.set(root, existingPatterns);
            } else {
                existingPatterns.push(relativePattern);
            }
        }
    }
    
    return patternMap;
}

/**
 * 获取权限模式映射
 * 原始函数: TrA(A, B, Q)
 */
function getPermissionPatterns(context, permissionType, defaultAction) {
    // 获取权限配置
    const permissionConfig = getPermissionConfig(permissionType);
    
    // 解析权限规则
    const rules = parsePermissionRules(context, permissionConfig, defaultAction);
    const patternMap = new Map();
    
    for (const [pattern, ruleDetails] of rules.entries()) {
        const { relativePattern, root } = parsePatternRoot(pattern, ruleDetails.source);
        
        let rootPatterns = patternMap.get(root);
        if (rootPatterns === undefined) {
            rootPatterns = new Map();
            patternMap.set(root, rootPatterns);
        }
        
        rootPatterns.set(relativePattern, ruleDetails);
    }
    
    return patternMap;
}

/**
 * 获取权限配置
 * 根据权限类型返回相应的配置
 */
function getPermissionConfig(permissionType) {
    switch (permissionType) {
        case PERMISSION_TYPES.EDIT:
            return getEditPermissionConfig(); // 原始: wq
        case PERMISSION_TYPES.READ:
            return getReadPermissionConfig(); // 原始: qF
        default:
            throw new Error(`Unknown permission type: ${permissionType}`);
    }
}

/**
 * 获取编辑权限配置
 * 原始变量: wq
 */
function getEditPermissionConfig() {
    return {
        allowPatterns: [
            "**/*.js",
            "**/*.ts",
            "**/*.jsx",
            "**/*.tsx",
            "**/*.py",
            "**/*.java",
            "**/*.cpp",
            "**/*.c",
            "**/*.h",
            "**/*.cs",
            "**/*.php",
            "**/*.rb",
            "**/*.go",
            "**/*.rs",
            "**/*.swift",
            "**/*.kt",
            "**/*.scala",
            "**/*.clj",
            "**/*.hs",
            "**/*.ml",
            "**/*.fs",
            "**/*.elm",
            "**/*.dart",
            "**/*.lua",
            "**/*.r",
            "**/*.m",
            "**/*.pl",
            "**/*.sh",
            "**/*.bash",
            "**/*.zsh",
            "**/*.fish",
            "**/*.ps1",
            "**/*.bat",
            "**/*.cmd",
            "**/*.html",
            "**/*.htm",
            "**/*.xml",
            "**/*.css",
            "**/*.scss",
            "**/*.sass",
            "**/*.less",
            "**/*.json",
            "**/*.yaml",
            "**/*.yml",
            "**/*.toml",
            "**/*.ini",
            "**/*.cfg",
            "**/*.conf",
            "**/*.md",
            "**/*.txt",
            "**/*.rst",
            "**/*.tex",
            "**/*.sql",
            "**/*.graphql",
            "**/*.proto",
            "**/*.thrift"
        ],
        denyPatterns: [
            "**/node_modules/**",
            "**/.git/**",
            "**/dist/**",
            "**/build/**",
            "**/*.min.js",
            "**/*.min.css",
            "**/vendor/**",
            "**/.vscode/**",
            "**/.idea/**",
            "**/coverage/**",
            "**/.nyc_output/**",
            "**/logs/**",
            "**/*.log"
        ]
    };
}

/**
 * 获取读取权限配置
 * 原始变量: qF
 */
function getReadPermissionConfig() {
    const editConfig = getEditPermissionConfig();
    
    return {
        allowPatterns: [
            ...editConfig.allowPatterns,
            "**/*.pdf",
            "**/*.doc",
            "**/*.docx",
            "**/*.xls",
            "**/*.xlsx",
            "**/*.ppt",
            "**/*.pptx",
            "**/*.rtf",
            "**/*.odt",
            "**/*.ods",
            "**/*.odp",
            "**/README*",
            "**/CHANGELOG*",
            "**/LICENSE*",
            "**/CONTRIBUTING*",
            "**/.gitignore",
            "**/.gitattributes",
            "**/package.json",
            "**/package-lock.json",
            "**/yarn.lock",
            "**/Cargo.toml",
            "**/Cargo.lock",
            "**/requirements.txt",
            "**/Pipfile",
            "**/Pipfile.lock",
            "**/pom.xml",
            "**/build.gradle",
            "**/Makefile",
            "**/CMakeLists.txt",
            "**/Dockerfile",
            "**/.dockerignore"
        ],
        denyPatterns: editConfig.denyPatterns
    };
}

/**
 * 解析权限规则
 * 原始函数: ix1(A, I, Q)
 */
function parsePermissionRules(context, permissionConfig, defaultAction) {
    const rules = new Map();
    
    // 处理允许模式
    if (permissionConfig.allowPatterns) {
        for (const pattern of permissionConfig.allowPatterns) {
            rules.set(pattern, {
                action: PERMISSION_ACTIONS.ALLOW,
                source: context,
                type: 'pattern'
            });
        }
    }
    
    // 处理拒绝模式
    if (permissionConfig.denyPatterns) {
        for (const pattern of permissionConfig.denyPatterns) {
            rules.set(pattern, {
                action: PERMISSION_ACTIONS.DENY,
                source: context,
                type: 'pattern'
            });
        }
    }
    
    return rules;
}

/**
 * 检查文件权限
 * 原始函数: JD1(A, B, Q, I)
 */
function checkFilePermission(filePath, context, permissionType, defaultAction) {
    // 规范化文件路径
    const normalizedPath = path.normalize(filePath);
    
    // 获取权限模式
    const permissionPatterns = getPermissionPatterns(context, permissionType, defaultAction);
    
    // 检查文件是否匹配任何权限规则
    for (const [root, patterns] of permissionPatterns.entries()) {
        for (const [pattern, ruleDetails] of patterns.entries()) {
            if (matchesPattern(normalizedPath, pattern, root)) {
                return {
                    allowed: ruleDetails.action === PERMISSION_ACTIONS.ALLOW,
                    rule: ruleDetails,
                    pattern: pattern,
                    root: root
                };
            }
        }
    }
    
    // 如果没有匹配的规则，使用默认动作
    return {
        allowed: defaultAction === PERMISSION_ACTIONS.ALLOW,
        rule: null,
        pattern: null,
        root: null
    };
}

/**
 * 检查模式匹配
 */
function matchesPattern(filePath, pattern, root) {
    // 这里应该实现glob模式匹配
    // 简化实现，实际应该使用minimatch或类似库
    
    const fullPattern = root ? path.join(root, pattern) : pattern;
    
    // 简单的通配符匹配
    if (pattern.includes('**')) {
        const regexPattern = pattern
            .replace(/\*\*/g, '.*')
            .replace(/\*/g, '[^/]*')
            .replace(/\?/g, '[^/]');
        
        const regex = new RegExp(`^${regexPattern}$`);
        return regex.test(filePath);
    }
    
    return filePath.includes(pattern.replace(/\*/g, ''));
}

/**
 * 检查是否可以读取文件
 */
function canReadFile(filePath, context = 'default') {
    const result = checkFilePermission(filePath, context, PERMISSION_TYPES.READ, PERMISSION_ACTIONS.ALLOW);
    return result.allowed;
}

/**
 * 检查是否可以编辑文件
 */
function canEditFile(filePath, context = 'default') {
    const result = checkFilePermission(filePath, context, PERMISSION_TYPES.EDIT, PERMISSION_ACTIONS.DENY);
    return result.allowed;
}

/**
 * 获取文件权限详情
 */
function getFilePermissions(filePath, context = 'default') {
    const readPermission = checkFilePermission(filePath, context, PERMISSION_TYPES.READ, PERMISSION_ACTIONS.ALLOW);
    const editPermission = checkFilePermission(filePath, context, PERMISSION_TYPES.EDIT, PERMISSION_ACTIONS.DENY);
    
    return {
        canRead: readPermission.allowed,
        canEdit: editPermission.allowed,
        readRule: readPermission.rule,
        editRule: editPermission.rule
    };
}

module.exports = {
    getIgnorePatterns,
    getPermissionPatterns,
    checkFilePermission,
    canReadFile,
    canEditFile,
    getFilePermissions,
    PERMISSION_TYPES,
    PERMISSION_ACTIONS
};
