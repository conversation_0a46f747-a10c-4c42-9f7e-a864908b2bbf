/**
 * 状态管理器
 * 处理应用的全局状态持久化和管理
 * 对应原始代码中的状态管理函数
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

const { createDirectory } = require('../utils/file-system');

// 状态文件路径
const STATE_DIR = path.join(os.homedir(), '.claude-code');
const STATE_FILE = path.join(STATE_DIR, 'state.json');

// 默认状态
const DEFAULT_STATE = {
    version: '1.0.53',
    initialized: false,
    firstRun: true,
    lastUpdateCheck: null,
    changelogLastFetched: null,
    cachedChangelog: null,
    auth: {
        configured: false,
        type: null,
        lastValidated: null
    },
    mcp: {
        servers: {},
        enabled: true,
        lastSync: null
    },
    ui: {
        theme: 'auto',
        verbose: false,
        vimMode: false
    },
    analytics: {
        enabled: true,
        userId: null,
        sessionId: null
    },
    checkpoints: [],
    projects: {},
    settings: {
        autoUpdate: true,
        telemetry: true,
        debugMode: false
    }
};

let cachedState = null;

/**
 * 获取应用状态
 * 原始函数: JA()
 */
function getAppState() {
    if (cachedState) {
        return cachedState;
    }
    
    try {
        if (fs.existsSync(STATE_FILE)) {
            const content = fs.readFileSync(STATE_FILE, 'utf8');
            const state = JSON.parse(content);
            
            // 合并默认状态以确保所有字段都存在
            cachedState = mergeWithDefaults(state, DEFAULT_STATE);
            return cachedState;
        }
    } catch (error) {
        console.warn('Failed to read state file:', error.message);
    }
    
    // 返回默认状态
    cachedState = { ...DEFAULT_STATE };
    return cachedState;
}

/**
 * 保存应用状态
 * 原始函数: b0(state)
 */
function saveAppState(state) {
    try {
        // 确保状态目录存在
        createDirectory(STATE_DIR);
        
        // 更新缓存
        cachedState = { ...state };
        
        // 写入文件
        fs.writeFileSync(STATE_FILE, JSON.stringify(state, null, 2), 'utf8');
        
        return true;
    } catch (error) {
        console.error('Failed to save state:', error.message);
        return false;
    }
}

/**
 * 更新应用状态（部分更新）
 */
function updateAppState(updates) {
    const currentState = getAppState();
    const newState = deepMerge(currentState, updates);
    return saveAppState(newState);
}

/**
 * 重置应用状态
 */
function resetAppState() {
    cachedState = null;
    
    try {
        if (fs.existsSync(STATE_FILE)) {
            fs.unlinkSync(STATE_FILE);
        }
        return true;
    } catch (error) {
        console.error('Failed to reset state:', error.message);
        return false;
    }
}

/**
 * 获取特定状态字段
 */
function getStateField(fieldPath) {
    const state = getAppState();
    return getNestedValue(state, fieldPath);
}

/**
 * 设置特定状态字段
 */
function setStateField(fieldPath, value) {
    const currentState = getAppState();
    const newState = setNestedValue(currentState, fieldPath, value);
    return saveAppState(newState);
}

/**
 * 检查是否为首次运行
 */
function isFirstRun() {
    const state = getAppState();
    return state.firstRun === true;
}

/**
 * 标记首次运行完成
 */
function markFirstRunComplete() {
    return updateAppState({
        firstRun: false,
        initialized: true
    });
}

/**
 * 获取认证状态
 */
function getAuthState() {
    const state = getAppState();
    return state.auth || DEFAULT_STATE.auth;
}

/**
 * 更新认证状态
 */
function updateAuthState(authUpdates) {
    return updateAppState({
        auth: {
            ...getAuthState(),
            ...authUpdates,
            lastValidated: Date.now()
        }
    });
}

/**
 * 获取MCP状态
 */
function getMcpState() {
    const state = getAppState();
    return state.mcp || DEFAULT_STATE.mcp;
}

/**
 * 更新MCP状态
 */
function updateMcpState(mcpUpdates) {
    return updateAppState({
        mcp: {
            ...getMcpState(),
            ...mcpUpdates,
            lastSync: Date.now()
        }
    });
}

/**
 * 获取UI设置
 */
function getUISettings() {
    const state = getAppState();
    return state.ui || DEFAULT_STATE.ui;
}

/**
 * 更新UI设置
 */
function updateUISettings(uiUpdates) {
    return updateAppState({
        ui: {
            ...getUISettings(),
            ...uiUpdates
        }
    });
}

/**
 * 深度合并对象
 */
function deepMerge(target, source) {
    const result = { ...target };
    
    for (const key in source) {
        if (source.hasOwnProperty(key)) {
            if (isObject(source[key]) && isObject(target[key])) {
                result[key] = deepMerge(target[key], source[key]);
            } else {
                result[key] = source[key];
            }
        }
    }
    
    return result;
}

/**
 * 合并默认值
 */
function mergeWithDefaults(state, defaults) {
    return deepMerge(defaults, state);
}

/**
 * 获取嵌套值
 */
function getNestedValue(obj, path) {
    const keys = path.split('.');
    let current = obj;
    
    for (const key of keys) {
        if (current && typeof current === 'object' && key in current) {
            current = current[key];
        } else {
            return undefined;
        }
    }
    
    return current;
}

/**
 * 设置嵌套值
 */
function setNestedValue(obj, path, value) {
    const keys = path.split('.');
    const result = { ...obj };
    let current = result;
    
    for (let i = 0; i < keys.length - 1; i++) {
        const key = keys[i];
        if (!current[key] || typeof current[key] !== 'object') {
            current[key] = {};
        } else {
            current[key] = { ...current[key] };
        }
        current = current[key];
    }
    
    current[keys[keys.length - 1]] = value;
    return result;
}

/**
 * 检查是否为对象
 */
function isObject(value) {
    return value !== null && typeof value === 'object' && !Array.isArray(value);
}

/**
 * 获取状态文件信息
 */
function getStateFileInfo() {
    try {
        if (fs.existsSync(STATE_FILE)) {
            const stats = fs.statSync(STATE_FILE);
            return {
                exists: true,
                size: stats.size,
                modified: stats.mtime,
                path: STATE_FILE
            };
        }
    } catch (error) {
        // 忽略错误
    }
    
    return {
        exists: false,
        size: 0,
        modified: null,
        path: STATE_FILE
    };
}

module.exports = {
    getAppState,
    saveAppState,
    updateAppState,
    resetAppState,
    getStateField,
    setStateField,
    isFirstRun,
    markFirstRunComplete,
    getAuthState,
    updateAuthState,
    getMcpState,
    updateMcpState,
    getUISettings,
    updateUISettings,
    getStateFileInfo,
    DEFAULT_STATE,
    STATE_FILE,
    STATE_DIR
};
