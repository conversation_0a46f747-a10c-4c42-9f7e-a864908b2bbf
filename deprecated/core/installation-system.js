/**
 * Claude Code - Installation and Setup System
 * 
 * 重构来源: cli-format-all.js 第354095-354574行 (479行)
 * 重构时间: 2025-01-19
 * 
 * 安装组件、设置流程和应用初始化系统
 */

// Node.js 内置模块
const fs = require('fs');

// 第三方依赖
const React = require('react');
const chalk = require('chalk');

// TODO_REFACTOR: 以下是未重构的依赖，需要后续替换

// TODO_REFACTOR: 查找KQ React库定义
const ReactLib = React; // 临时占位符

// TODO_REFACTOR: 查找b布局组件定义
const Box = ({ children, ...props }) => {
  throw new Error('Box component not yet refactored (original: b)');
};

// TODO_REFACTOR: 查找S文本组件定义
const Text = ({ children, ...props }) => {
  throw new Error('Text component not yet refactored (original: S)');
};

// TODO_REFACTOR: 查找G0图标定义
const Icons = {
  warning: '⚠',
  tick: '✓',
  cross: '✗'
};

// TODO_REFACTOR: 查找SA日志函数定义
const logDebug = (message) => {
  throw new Error('Debug log function not yet refactored (original: SA)');
};

// TODO_REFACTOR: 查找I9错误日志函数定义
const logError = (message) => {
  throw new Error('Error log function not yet refactored (original: I9)');
};

// TODO_REFACTOR: 查找E1遥测记录函数定义
const trackEvent = (eventName, properties) => {
  throw new Error('Track event function not yet refactored (original: E1)');
};

// TODO_REFACTOR: 查找bF4清理安装函数定义
const { cleanupInstallations } = require('./update-system');

// TODO_REFACTOR: 查找po2 Shell别名清理函数定义
const cleanupShellAliases = () => {
  throw new Error('Shell alias cleanup function not yet refactored (original: po2)');
};

// TODO_REFACTOR: 查找gp安装最新版本函数定义
const installLatest = async (force, target, forceReinstall) => {
  throw new Error('Install latest function not yet refactored (original: gp)');
};

// TODO_REFACTOR: 查找bp设置启动器函数定义
const setupLauncher = async (force) => {
  throw new Error('Setup launcher function not yet refactored (original: bp)');
};

// TODO_REFACTOR: 查找vF4获取安装路径函数定义
const { getInstallationPath } = require('./update-system');

// TODO_REFACTOR: 查找z4 React渲染函数定义
const renderReactComponent = (component, options) => {
  throw new Error('React render function not yet refactored (original: z4)');
};

// TODO_REFACTOR: 查找JA应用状态获取函数定义
const getAppState = () => {
  throw new Error('App state function not yet refactored (original: JA)');
};

// TODO_REFACTOR: 查找b0应用状态设置函数定义
const setAppState = (state) => {
  throw new Error('App state setter function not yet refactored (original: b0)');
};

// TODO_REFACTOR: 查找E6 CI环境检测函数定义
const isCIEnvironment = (flag) => {
  throw new Error('CI environment detection function not yet refactored (original: E6)');
};

// TODO_REFACTOR: 查找a5认证设置函数定义
const setupAuthentication = async () => {
  throw new Error('Setup authentication function not yet refactored (original: a5)');
};

// 版本信息常量
const VERSION_INFO = {
  ISSUES_EXPLAINER: "report the issue at https://github.com/anthropics/claude-code/issues",
  PACKAGE_URL: "@anthropic-ai/claude-code",
  README_URL: "https://docs.anthropic.com/s/claude-code",
  VERSION: "1.0.53"
};

/**
 * 设置消息组件
 * 重构自: 第354095-354110行的K6B组件
 */
function SetupMessagesComponent({ messages }) {
  if (messages.length === 0) {
    return null;
  }
  
  return ReactLib.createElement(Box, {
    flexDirection: "column",
    gap: 0,
    marginBottom: 1
  }, 
    ReactLib.createElement(Box, null, 
      ReactLib.createElement(Text, {
        color: "warning"
      }, Icons.warning, " Setup notes:")
    ), 
    messages.map((message, index) => 
      ReactLib.createElement(Box, {
        key: index,
        marginLeft: 2
      }, 
        ReactLib.createElement(Text, {
          color: "secondaryText"
        }, "• ", message)
      )
    )
  );
}

/**
 * 安装组件
 * 重构自: 第354111-354257行的hF4组件
 */
function InstallationComponent({ onDone, force, target }) {
  const [state, setState] = ReactLib.useState({
    type: "checking"
  });
  
  ReactLib.useEffect(() => {
    async function performInstallation() {
      try {
        logDebug(`Install: Starting installation process (force=${force}, target=${target})`);
        
        // 清理npm安装
        setState({
          type: "cleaning-npm"
        });
        
        const { removed, errors } = await cleanupInstallations();
        
        if (removed > 0) {
          logDebug(`Cleaned up ${removed} npm installation(s)`);
        }
        
        if (errors.length > 0) {
          logDebug(`Cleanup warnings: ${errors.join(", ")}`);
        }
        
        // 清理Shell别名
        const shellCleanup = cleanupShellAliases();
        if (shellCleanup.length > 0) {
          logDebug(`Shell alias cleanup: ${shellCleanup.join("; ")}`);
        }
        
        // 安装最新版本
        setState({
          type: "installing",
          version: target || "stable"
        });
        
        logDebug(`Install: Calling installLatest(force=true, target=${target}, forceReinstall=${force})`);
        
        const installResult = await installLatest(true, target, force);
        
        logDebug(`Install: installLatest returned version=${installResult.latestVersion}, wasUpdated=${installResult.wasUpdated}`);
        
        if (!installResult.latestVersion) {
          logError("Install: Failed to retrieve version information during install");
        }
        
        if (!installResult.wasUpdated) {
          logDebug("Install: Already up to date");
        }
        
        // 设置启动器
        setState({
          type: "setting-up"
        });
        
        const setupMessages = await setupLauncher(true);
        
        logDebug(`Install: Setup launcher completed with ${setupMessages.length} messages`);
        
        if (setupMessages.length > 0) {
          setupMessages.forEach((message) => 
            logDebug(`Install: Setup message: ${message}`)
          );
        }
        
        // 记录遥测
        trackEvent("claude_install_command", {
          has_version: installResult.latestVersion ? 1 : 0,
          forced: force ? 1 : 0
        });
        
        // 显示结果
        if (setupMessages.length > 0) {
          setState({
            type: "set-up",
            messages: setupMessages
          });
          
          setTimeout(() => {
            setState({
              type: "success",
              version: installResult.latestVersion || "current",
              setupMessages: setupMessages
            });
          }, 2000);
        } else {
          logDebug("Install: Shell PATH already configured");
          setState({
            type: "success",
            version: installResult.latestVersion || "current"
          });
        }
        
      } catch (error) {
        logError(`Install command failed: ${error}`);
        setState({
          type: "error",
          message: error instanceof Error ? error.message : String(error)
        });
      }
    }
    
    performInstallation();
  }, [force, target]);
  
  // 自动完成处理
  ReactLib.useEffect(() => {
    if (state.type === "success") {
      setTimeout(() => {
        onDone();
      }, 2000);
    } else if (state.type === "error") {
      setTimeout(() => {
        onDone();
      }, 3000);
    }
  }, [state, onDone]);
  
  // 渲染UI
  return ReactLib.createElement(Box, {
    flexDirection: "column",
    marginTop: 1
  }, 
    // 检查状态
    state.type === "checking" && ReactLib.createElement(Text, {
      color: "claude"
    }, "Checking installation status..."),
    
    // 清理状态
    state.type === "cleaning-npm" && ReactLib.createElement(Text, {
      color: "warning"
    }, "Cleaning up old npm installations..."),
    
    // 安装状态
    state.type === "installing" && ReactLib.createElement(Text, {
      color: "claude"
    }, "Installing Claude Code native build ", state.version, "..."),
    
    // 设置状态
    state.type === "setting-up" && ReactLib.createElement(Text, {
      color: "claude"
    }, "Setting up launcher and shell integration..."),
    
    // 设置完成状态
    state.type === "set-up" && ReactLib.createElement(SetupMessagesComponent, {
      messages: state.messages
    }),
    
    // 成功状态
    state.type === "success" && ReactLib.createElement(Box, {
      flexDirection: "column",
      gap: 1
    }, 
      state.setupMessages && ReactLib.createElement(SetupMessagesComponent, {
        messages: state.setupMessages
      }),
      ReactLib.createElement(Box, null, 
        ReactLib.createElement(Text, {
          color: "success"
        }, Icons.tick, " "),
        ReactLib.createElement(Text, {
          color: "success",
          bold: true
        }, "Claude Code successfully installed!")
      ),
      ReactLib.createElement(Box, {
        marginLeft: 2,
        flexDirection: "column",
        gap: 1
      }, 
        state.version !== "current" && ReactLib.createElement(Box, null, 
          ReactLib.createElement(Text, {
            color: "secondaryText"
          }, "Version: "),
          ReactLib.createElement(Text, {
            color: "claude"
          }, state.version)
        ),
        ReactLib.createElement(Box, null, 
          ReactLib.createElement(Text, {
            color: "secondaryText"
          }, "Location: "),
          ReactLib.createElement(Text, {
            color: "text"
          }, getInstallationPath())
        )
      ),
      ReactLib.createElement(Box, {
        marginLeft: 2,
        flexDirection: "column",
        gap: 1
      }, 
        ReactLib.createElement(Box, {
          marginTop: 1
        }, 
          ReactLib.createElement(Text, {
            color: "secondaryText"
          }, "Next: Run "),
          ReactLib.createElement(Text, {
            color: "claude",
            bold: true
          }, "claude --help"),
          ReactLib.createElement(Text, {
            color: "secondaryText"
          }, " to get started")
        )
      )
    ),
    
    // 错误状态
    state.type === "error" && ReactLib.createElement(Box, {
      flexDirection: "column",
      gap: 1
    }, 
      ReactLib.createElement(Box, null, 
        ReactLib.createElement(Text, {
          color: "error"
        }, Icons.cross, " "),
        ReactLib.createElement(Text, {
          color: "error"
        }, "Installation failed")
      ),
      ReactLib.createElement(Text, {
        color: "error"
      }, state.message),
      ReactLib.createElement(Box, {
        marginTop: 1
      }, 
        ReactLib.createElement(Text, {
          color: "secondaryText"
        }, "Try running with --force to override checks")
      )
    )
  );
}

/**
 * 安装命令对象
 * 重构自: 第354258-354276行的z6B对象
 */
const installCommand = {
  type: "local-jsx",
  name: "install",
  description: "Install Claude Code native build",
  argumentHint: "[options]",
  async call(callback, context, args) {
    const force = args.includes("--force");
    const target = args.filter((arg) => !arg.startsWith("--"))[0];
    
    const { unmount } = renderReactComponent(
      ReactLib.createElement(InstallationComponent, {
        onDone: () => {
          unmount();
          callback();
        },
        force: force,
        target: target
      })
    );
  }
};

/**
 * 检查调试器状态
 * 重构自: 第354278-354288行的uF4函数
 */
function isDebuggerAttached() {
  const hasInspectArg = process.execArgv.some((arg) =>
    /--inspect(-brk)?|--debug(-brk)?/.test(arg)
  );

  const hasInspectEnv = process.env.NODE_OPTIONS &&
    /--inspect(-brk)?|--debug(-brk)?/.test(process.env.NODE_OPTIONS);

  try {
    return !!global.require("inspector").url() || hasInspectArg || hasInspectEnv;
  } catch {
    return hasInspectArg || hasInspectEnv;
  }
}

/**
 * 完成入门流程
 * 重构自: 第354289-354301行的pF4函数
 */
function completeOnboarding() {
  const appState = getAppState();
  setAppState({
    ...appState,
    hasCompletedOnboarding: true,
    lastOnboardingVersion: VERSION_INFO.VERSION
  });
}

/**
 * 显示设置屏幕
 * 重构自: 第354302-354383行的iF4函数
 */
async function showSetupScreens(permissionMode) {
  if (isCIEnvironment(false) || process.env.IS_DEMO) {
    return false;
  }

  const appState = getAppState();
  let hasShownSetup = false;

  // 检查是否需要显示入门流程
  if (!appState.theme || !appState.hasCompletedOnboarding) {
    hasShownSetup = true;
    await setupAuthentication();

    await new Promise((resolve) => {
      // TODO_REFACTOR: 查找w5包装组件定义
      // TODO_REFACTOR: 查找O3 React库定义
      // TODO_REFACTOR: 查找Xa2入门组件定义
      // TODO_REFACTOR: 查找Dx状态变更函数定义

      const { unmount } = renderReactComponent(
        // 入门组件占位符
        null, // TODO_REFACTOR: 实际的入门组件
        {
          exitOnCtrlC: false
        }
      );

      // 模拟入门完成
      setTimeout(() => {
        completeOnboarding();
        setupAuthentication();
        unmount();
        resolve();
      }, 1000);
    });
  }

  // 检查API密钥设置
  if (process.env.ANTHROPIC_API_KEY) {
    // TODO_REFACTOR: 查找RJ API密钥处理函数定义
    // TODO_REFACTOR: 查找PX1 API密钥状态检查函数定义
    // TODO_REFACTOR: 查找oq1 API密钥组件定义

    console.log('API key setup - placeholder');
  }

  // 权限模式处理
  if (permissionMode !== "bypassPermissions" && process.env.CLAUBBIT !== "true") {
    // TODO_REFACTOR: 查找sc0权限检查函数定义
    // TODO_REFACTOR: 查找gQB权限组件定义
    // TODO_REFACTOR: 查找Lq错误检查函数定义
    // TODO_REFACTOR: 查找tQB权限设置函数定义
    // TODO_REFACTOR: 查找Ei2权限检查函数定义
    // TODO_REFACTOR: 查找uq1权限组件定义

    console.log('Permission setup - placeholder');
  }

  // 绕过权限模式处理
  if (permissionMode === "bypassPermissions" && !getAppState().bypassPermissionsModeAccepted) {
    await new Promise((resolve) => {
      // TODO_REFACTOR: 查找eQB绕过权限组件定义

      const { unmount } = renderReactComponent(
        null, // TODO_REFACTOR: 实际的绕过权限组件
        {}
      );

      // 模拟接受
      setTimeout(() => {
        unmount();
        resolve();
      }, 1000);
    });
  }

  return hasShownSetup;
}

module.exports = {
  SetupMessagesComponent,
  InstallationComponent,
  installCommand,
  VERSION_INFO,
  isDebuggerAttached,
  completeOnboarding,
  showSetupScreens
};
