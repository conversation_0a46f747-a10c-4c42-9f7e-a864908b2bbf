/**
 * 项目设置管理
 * 处理项目级别的配置和设置
 * 对应原始代码中的项目设置函数
 */

const fs = require('fs');
const path = require('path');
const { getCurrentWorkingDirectory, readFile, writeFile, fileExists } = require('../utils/file-system');

// 项目设置文件名
const PROJECT_SETTINGS_FILE = '.claude-project.json';
const LEGACY_SETTINGS_FILE = '.claude.json';

// 默认项目设置
const DEFAULT_PROJECT_SETTINGS = {
    version: "1.0",
    name: "",
    description: "",
    ignorePatterns: [
        "node_modules/**",
        ".git/**",
        "dist/**",
        "build/**",
        "coverage/**",
        "*.log",
        ".DS_Store",
        "Thumbs.db"
    ],
    includePatterns: [
        "**/*.js",
        "**/*.ts",
        "**/*.jsx",
        "**/*.tsx",
        "**/*.py",
        "**/*.java",
        "**/*.cpp",
        "**/*.c",
        "**/*.h",
        "**/*.cs",
        "**/*.php",
        "**/*.rb",
        "**/*.go",
        "**/*.rs",
        "**/*.swift",
        "**/*.kt",
        "**/*.html",
        "**/*.css",
        "**/*.json",
        "**/*.md",
        "**/*.txt",
        "**/*.yaml",
        "**/*.yml"
    ],
    maxFileSize: 1048576, // 1MB
    maxFiles: 1000,
    enableMcp: true,
    mcpServers: {},
    customInstructions: "",
    features: {
        codeGeneration: true,
        codeReview: true,
        documentation: true,
        testing: true,
        refactoring: true
    },
    permissions: {
        allowFileCreation: true,
        allowFileModification: true,
        allowFileDeletion: false,
        allowDirectoryCreation: true,
        allowShellCommands: false,
        allowNetworkAccess: false
    }
};

let cachedSettings = null;
let cachedProjectRoot = null;

/**
 * 获取项目根目录
 */
function getProjectRoot() {
    if (cachedProjectRoot) {
        return cachedProjectRoot;
    }
    
    let currentDir = getCurrentWorkingDirectory();
    
    // 向上查找项目设置文件
    while (currentDir !== path.dirname(currentDir)) {
        const settingsPath = path.join(currentDir, PROJECT_SETTINGS_FILE);
        const legacyPath = path.join(currentDir, LEGACY_SETTINGS_FILE);
        
        if (fileExists(settingsPath) || fileExists(legacyPath)) {
            cachedProjectRoot = currentDir;
            return currentDir;
        }
        
        // 检查是否有Git仓库
        if (fileExists(path.join(currentDir, '.git'))) {
            cachedProjectRoot = currentDir;
            return currentDir;
        }
        
        currentDir = path.dirname(currentDir);
    }
    
    // 如果没有找到项目根目录，使用当前工作目录
    cachedProjectRoot = getCurrentWorkingDirectory();
    return cachedProjectRoot;
}

/**
 * 获取项目设置文件路径
 */
function getProjectSettingsPath() {
    const projectRoot = getProjectRoot();
    const modernPath = path.join(projectRoot, PROJECT_SETTINGS_FILE);
    const legacyPath = path.join(projectRoot, LEGACY_SETTINGS_FILE);
    
    // 优先使用现代设置文件
    if (fileExists(modernPath)) {
        return modernPath;
    }
    
    // 检查是否有旧版设置文件
    if (fileExists(legacyPath)) {
        return legacyPath;
    }
    
    // 返回现代设置文件路径（用于创建新文件）
    return modernPath;
}

/**
 * 获取项目设置
 * 原始函数: D9()
 */
function getProjectSettings() {
    if (cachedSettings) {
        return cachedSettings;
    }
    
    try {
        const settingsPath = getProjectSettingsPath();
        
        if (fileExists(settingsPath)) {
            const content = readFile(settingsPath);
            const settings = JSON.parse(content);
            
            // 合并默认设置
            cachedSettings = mergeWithDefaults(settings, DEFAULT_PROJECT_SETTINGS);
            return cachedSettings;
        }
    } catch (error) {
        console.warn('Failed to read project settings:', error.message);
    }
    
    // 返回默认设置
    cachedSettings = { ...DEFAULT_PROJECT_SETTINGS };
    return cachedSettings;
}

/**
 * 保存项目设置
 */
function saveProjectSettings(settings) {
    try {
        const settingsPath = getProjectSettingsPath();
        
        // 更新缓存
        cachedSettings = { ...settings };
        
        // 写入文件
        writeFile(settingsPath, JSON.stringify(settings, null, 2));
        
        return true;
    } catch (error) {
        console.error('Failed to save project settings:', error.message);
        return false;
    }
}

/**
 * 更新项目设置（部分更新）
 */
function updateProjectSettings(updates) {
    const currentSettings = getProjectSettings();
    const newSettings = deepMerge(currentSettings, updates);
    return saveProjectSettings(newSettings);
}

/**
 * 初始化项目设置
 */
function initializeProjectSettings(options = {}) {
    const projectRoot = getProjectRoot();
    const settingsPath = path.join(projectRoot, PROJECT_SETTINGS_FILE);
    
    // 如果设置文件已存在，不覆盖
    if (fileExists(settingsPath)) {
        console.log('Project settings already exist');
        return false;
    }
    
    // 创建初始设置
    const initialSettings = {
        ...DEFAULT_PROJECT_SETTINGS,
        name: options.name || path.basename(projectRoot),
        description: options.description || "",
        ...options
    };
    
    return saveProjectSettings(initialSettings);
}

/**
 * 检查是否有项目设置
 */
function hasProjectSettings() {
    const settingsPath = getProjectSettingsPath();
    return fileExists(settingsPath);
}

/**
 * 获取项目信息
 */
function getProjectInfo() {
    const settings = getProjectSettings();
    const projectRoot = getProjectRoot();
    
    return {
        name: settings.name || path.basename(projectRoot),
        description: settings.description || "",
        root: projectRoot,
        version: settings.version || "1.0",
        hasSettings: hasProjectSettings()
    };
}

/**
 * 验证项目设置
 */
function validateProjectSettings(settings) {
    const errors = [];
    
    // 检查必需字段
    if (!settings.version) {
        errors.push('Missing version field');
    }
    
    // 检查忽略模式
    if (settings.ignorePatterns && !Array.isArray(settings.ignorePatterns)) {
        errors.push('ignorePatterns must be an array');
    }
    
    // 检查包含模式
    if (settings.includePatterns && !Array.isArray(settings.includePatterns)) {
        errors.push('includePatterns must be an array');
    }
    
    // 检查文件大小限制
    if (settings.maxFileSize && (typeof settings.maxFileSize !== 'number' || settings.maxFileSize <= 0)) {
        errors.push('maxFileSize must be a positive number');
    }
    
    // 检查文件数量限制
    if (settings.maxFiles && (typeof settings.maxFiles !== 'number' || settings.maxFiles <= 0)) {
        errors.push('maxFiles must be a positive number');
    }
    
    return {
        valid: errors.length === 0,
        errors: errors
    };
}

/**
 * 重置项目设置缓存
 */
function resetProjectSettingsCache() {
    cachedSettings = null;
    cachedProjectRoot = null;
}

/**
 * 深度合并对象
 */
function deepMerge(target, source) {
    const result = { ...target };
    
    for (const key in source) {
        if (source.hasOwnProperty(key)) {
            if (isObject(source[key]) && isObject(target[key])) {
                result[key] = deepMerge(target[key], source[key]);
            } else {
                result[key] = source[key];
            }
        }
    }
    
    return result;
}

/**
 * 合并默认值
 */
function mergeWithDefaults(settings, defaults) {
    return deepMerge(defaults, settings);
}

/**
 * 检查是否为对象
 */
function isObject(value) {
    return value !== null && typeof value === 'object' && !Array.isArray(value);
}

module.exports = {
    getProjectRoot,
    getProjectSettings,
    saveProjectSettings,
    updateProjectSettings,
    initializeProjectSettings,
    hasProjectSettings,
    getProjectInfo,
    validateProjectSettings,
    resetProjectSettingsCache,
    DEFAULT_PROJECT_SETTINGS,
    PROJECT_SETTINGS_FILE,
    LEGACY_SETTINGS_FILE
};
