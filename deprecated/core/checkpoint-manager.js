/**
 * 检查点管理系统
 * 从原始文件第349997-350162行提取
 * 提供Git集成的检查点功能，用于代码版本控制
 */

const { join } = require('path');
const { createHash } = require('crypto');
const fs = require('fs');
const { execSync } = require('child_process');

const { getCurrentWorkingDirectory } = require('../utils/file-system');
const { executeCommand } = require('../utils/process');
const { logError } = require('../utils/logger');
const { saveCheckpointToLog } = require('../analytics/checkpoint-logger');

// 最大缓冲区大小，用于Git命令
const MAX_BUFFER_SIZE = 3000000;

// Git忽略模式，用于检查点系统
const DEFAULT_IGNORE_PATTERNS = [
    ".git/", ".parcel-cache/", ".pytest_cache/", ".nuxt/", ".sass-cache/", 
    ".claude/", "__pycache__/", "node_modules/", "pycache/",
    // 媒体文件
    "*.3gp", "*.avif", "*.gif", "*.png", "*.psd", "*.aac", "*.aiff", "*.asf", 
    "*.avi", "*.bmp", "*.divx", "*.flac", "*.heic", "*.ico", "*.jpg", "*.jpeg", 
    "*.m4a", "*.m4v", "*.mkv", "*.mov", "*.mp3", "*.mp4", "*.mpeg", "*.mpg", 
    "*.ogg", "*.opus", "*.raw", "*.rm", "*.rmvb", "*.tiff", "*.tif", "*.vob", 
    "*.wav", "*.webm", "*.webp", "*.wma", "*.wmv",
    // 系统文件
    "*.DS_Store", "*.cache", "*.crdownload", "*.dmp", "*.dump", "*.eslintcache", 
    "*.pyc", "*.pyo", "*.swo", "*.swp", "*.Thumbs.db",
    // 压缩文件
    "*.zip", "*.tar", "*.gz", "*.rar", "*.7z", "*.iso", "*.bin", "*.exe", 
    "*.dll", "*.so", "*.dylib", "*.dat", "*.dmg", "*.msi",
    // 数据库文件
    "*.arrow", "*.accdb", "*.aof", "*.avro", "*.bson", "*.db", "*.dbf", 
    "*.dmp", "*.frm", "*.ibd", "*.mdb", "*.myd", "*.myi", "*.orc", "*.parquet", 
    "*.pdb", "*.rdb", "*.sqlite",
    // GIS文件
    "*.shp", "*.shx", "*.sbn", "*.sbx", "*.gdb", "*.gpkg", "*.kmz", "*.dem", 
    "*.img", "*.ecw", "*.las", "*.laz", "*.mxd", "*.qgs", "*.grd",
    // CAD文件
    "*.dwg", "*.dxf"
];

/**
 * 检查点管理器类
 * 原始类名: Hi
 */
class CheckpointManager {
    static instance = null;
    
    constructor() {
        this.status = "uninitialized";
        this.checkpoints = [];
        this.shadowRepoPath = null;
    }
    
    /**
     * 获取单例实例
     */
    static getInstance() {
        if (!CheckpointManager.instance) {
            CheckpointManager.instance = new CheckpointManager();
        }
        return CheckpointManager.instance;
    }
    
    /**
     * 初始化检查点系统
     * 原始方法: initialize()
     */
    async initialize() {
        if (this.status !== "uninitialized") {
            return;
        }
        
        this.status = "initializing";
        
        try {
            const workingDir = getCurrentWorkingDirectory();
            const dirHash = createHash("sha256").update(workingDir).digest("hex");
            const checkpointDir = join(workingDir, ".claude", "checkpoints", dirHash);
            
            // 确保检查点目录存在
            if (!fs.existsSync(checkpointDir)) {
                fs.mkdirSync(checkpointDir, { recursive: true });
            }
            
            this.shadowRepoPath = checkpointDir;
            const gitDir = join(checkpointDir, ".git");
            
            if (!fs.existsSync(gitDir)) {
                // 初始化新的Git仓库
                const { code: initCode } = await executeCommand("git", ["init"], {
                    cwd: checkpointDir
                });
                
                if (initCode !== 0) {
                    throw new Error("Failed to initialize checkpointing (init)");
                }
                
                // 设置Git忽略规则
                await this.setupGitIgnore(gitDir, workingDir);
                
                // 配置工作树
                const { code: configCode } = await executeCommand("git", ["config", "--local", "core.worktree", workingDir], {
                    cwd: checkpointDir
                });
                
                if (configCode !== 0) {
                    throw new Error("Failed to initialize checkpointing (config)");
                }
                
                // 添加所有文件并创建初始提交
                await executeCommand("git", ["add", "--all", "--ignore-errors"], {
                    cwd: checkpointDir
                });
                
                const { code: commitCode } = await executeCommand("git", ["commit", "-m", "Initial checkpoint", "--allow-empty"], {
                    cwd: checkpointDir,
                    maxBuffer: MAX_BUFFER_SIZE
                });
                
                if (commitCode !== 0) {
                    throw new Error("Failed to initialize checkpointing (commit)");
                }
            } else {
                // 验证现有仓库
                await this.setupGitIgnore(gitDir, workingDir);
                
                await executeCommand("git", ["add", "--all", "--ignore-errors"], {
                    cwd: checkpointDir
                });
                
                const { code: verifyCode } = await executeCommand("git", ["commit", "-m", "Initialization check", "--allow-empty"], {
                    cwd: checkpointDir,
                    maxBuffer: MAX_BUFFER_SIZE
                });
                
                if (verifyCode !== 0) {
                    throw new Error("Failed to initialize checkpointing (verify)");
                }
            }
            
            this.status = "initialized";
        } catch (error) {
            this.status = "error";
            logError(error);
            throw error;
        }
    }
    
    /**
     * 保存检查点
     * 原始方法: saveCheckpoint(A="Checkpoint")
     */
    async saveCheckpoint(label = "Checkpoint") {
        if (this.status !== "initialized" || !this.shadowRepoPath) {
            throw new Error("Checkpointing not initialized");
        }
        
        try {
            // 添加所有更改
            await executeCommand("git", ["add", "--all", "--ignore-errors"], {
                cwd: this.shadowRepoPath
            });
            
            // 创建提交
            const { code: commitCode } = await executeCommand("git", ["commit", "-m", label, "--allow-empty"], {
                cwd: this.shadowRepoPath
            });
            
            if (commitCode !== 0) {
                throw new Error("Failed to create backup checkpoint commit");
            }
            
            // 获取提交哈希
            const { stdout: commitHash, code: hashCode } = await executeCommand("git", ["rev-parse", "HEAD"], {
                cwd: this.shadowRepoPath
            });
            
            if (hashCode !== 0) {
                throw new Error("Failed to create restore checkpoint commit");
            }
            
            // 创建检查点对象
            const checkpoint = {
                commit: commitHash.trim(),
                timestamp: new Date(),
                label: label
            };
            
            // 保存到内存和日志
            this.checkpoints.push(checkpoint);
            await saveCheckpointToLog(checkpoint);
            
            return checkpoint;
        } catch (error) {
            logError(error);
            throw error;
        }
    }
    
    /**
     * 恢复检查点
     * 原始方法: restoreCheckpoint(A)
     */
    async restoreCheckpoint(commitHash) {
        if (this.status !== "initialized" || !this.shadowRepoPath) {
            throw new Error("Checkpointing not initialized");
        }
        
        try {
            // 创建备份检查点
            const backupCheckpoint = await this.saveCheckpoint(`Backup checkpoint (before restoring to ${commitHash.substring(0, 9)})`);
            
            // 获取当前HEAD
            const { stdout: currentHead, code: headCode } = await executeCommand("git", ["rev-parse", "HEAD"], {
                cwd: this.shadowRepoPath
            });
            
            if (headCode !== 0) {
                throw new Error("Failed to create backup checkpoint before restoring");
            }
            
            const currentCommit = currentHead.trim();
            
            // 恢复到指定检查点
            await executeCommand("git", ["revert", "--no-commit", `${commitHash}..${currentCommit}`], {
                cwd: this.shadowRepoPath
            });
            
            await executeCommand("git", ["commit", "-m", `Restore to checkpoint ${commitHash}`, "--allow-empty"], {
                cwd: this.shadowRepoPath,
                maxBuffer: MAX_BUFFER_SIZE
            });
            
            return backupCheckpoint;
        } catch (error) {
            logError(error);
            throw error;
        }
    }
    
    /**
     * 获取状态
     */
    getStatus() {
        return this.status;
    }
    
    /**
     * 获取检查点列表（按时间倒序）
     */
    getCheckpoints() {
        return this.checkpoints.slice().reverse();
    }
    
    /**
     * 从日志加载检查点
     */
    async loadCheckpointsFromLog(logData) {
        const checkpoints = logData.checkpoints;
        if (!checkpoints) {
            return;
        }
        
        this.checkpoints = checkpoints.sort((a, b) => 
            a.timestamp.getTime() - b.timestamp.getTime()
        );
    }
    
    /**
     * 保存检查点到日志
     */
    async saveCheckpointsToLog() {
        for (const checkpoint of this.checkpoints) {
            await saveCheckpointToLog(checkpoint);
        }
    }
    
    /**
     * 重置检查点管理器
     */
    reset() {
        this.status = "uninitialized";
        this.checkpoints = [];
        this.shadowRepoPath = null;
    }
    
    /**
     * 设置Git忽略规则
     * 原始函数: t9B(A, B)
     */
    async setupGitIgnore(gitDir, workingDir) {
        const lfsPatterns = await this.getLfsPatterns(workingDir);
        const allPatterns = DEFAULT_IGNORE_PATTERNS.concat(lfsPatterns);
        
        const infoDir = join(gitDir, "info");
        const excludeFile = join(gitDir, "info", "exclude");
        
        if (!fs.existsSync(infoDir)) {
            fs.mkdirSync(infoDir, { recursive: true });
        }
        
        fs.writeFileSync(excludeFile, allPatterns.join('\n'), {
            encoding: 'utf8',
            flush: true
        });
    }
    
    /**
     * 获取LFS模式
     * 原始函数: EZ4(A)
     */
    async getLfsPatterns(workingDir) {
        try {
            const gitAttributesPath = join(workingDir, ".gitattributes");
            
            if (fs.existsSync(gitAttributesPath)) {
                const content = fs.readFileSync(gitAttributesPath, { encoding: 'utf8' });
                return content
                    .split('\n')
                    .filter(line => line.includes('filter=lfs'))
                    .map(line => line.split(' ')[0]?.trim() || '')
                    .filter(pattern => pattern.length > 0);
            }
        } catch (error) {
            // 忽略错误，返回空数组
        }
        
        return [];
    }
}

// 导出单例实例
const checkpointManager = CheckpointManager.getInstance();

module.exports = {
    CheckpointManager,
    checkpointManager,
    DEFAULT_IGNORE_PATTERNS,
    MAX_BUFFER_SIZE
};
