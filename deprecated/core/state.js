/**
 * 状态管理模块
 * 处理应用的全局状态
 */

const { getMergedConfig } = require('../config/manager');

let globalState = null;

/**
 * 获取默认状态
 * 原始函数名: zS
 */
function getDefaultState() {
    if (globalState) {
        return globalState;
    }
    
    try {
        const config = getMergedConfig();
        
        globalState = {
            version: '1.0.53',
            initialized: true,
            config: config,
            timestamp: Date.now(),
            auth: {
                configured: false,
                type: null
            },
            mcp: {
                servers: {},
                enabled: true
            }
        };
        
        return globalState;
    } catch (error) {
        console.warn('Failed to get default state:', error.message);
        
        globalState = {
            version: '1.0.53',
            initialized: false,
            config: {},
            timestamp: Date.now(),
            auth: {
                configured: false,
                type: null
            },
            mcp: {
                servers: {},
                enabled: false
            }
        };
        
        return globalState;
    }
}

/**
 * 更新全局状态
 */
function updateState(updates) {
    if (!globalState) {
        globalState = getDefaultState();
    }
    
    globalState = {
        ...globalState,
        ...updates,
        timestamp: Date.now()
    };
    
    return globalState;
}

/**
 * 重置状态
 */
function resetState() {
    globalState = null;
    return getDefaultState();
}

module.exports = {
    getDefaultState,
    updateState,
    resetState
};
