/**
 * 配置管理器
 * 处理用户配置的读写操作
 * 对应原始代码中的配置相关函数
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

/**
 * 获取配置文件路径
 */
function getConfigPath(isGlobal = false) {
    if (isGlobal) {
        // 全局配置路径
        return path.join(os.homedir(), '.claude-code', 'config.json');
    } else {
        // 本地配置路径
        return path.join(process.cwd(), '.claude-code.json');
    }
}

/**
 * 读取配置文件
 */
function readConfig(isGlobal = false) {
    const configPath = getConfigPath(isGlobal);
    
    try {
        if (fs.existsSync(configPath)) {
            const content = fs.readFileSync(configPath, 'utf8');
            return JSON.parse(content);
        }
    } catch (error) {
        console.warn(`Warning: Failed to read config from ${configPath}:`, error.message);
    }
    
    return {};
}

/**
 * 写入配置文件
 */
function writeConfig(config, isGlobal = false) {
    const configPath = getConfigPath(isGlobal);
    const configDir = path.dirname(configPath);
    
    try {
        // 确保配置目录存在
        if (!fs.existsSync(configDir)) {
            fs.mkdirSync(configDir, { recursive: true });
        }
        
        fs.writeFileSync(configPath, JSON.stringify(config, null, 2), 'utf8');
    } catch (error) {
        throw new Error(`Failed to write config to ${configPath}: ${error.message}`);
    }
}

/**
 * 获取配置值
 * 原始函数名: Ql0
 */
function getConfigValue(key, isGlobal = false) {
    const config = readConfig(isGlobal);
    
    // 支持点号分隔的嵌套键
    const keys = key.split('.');
    let value = config;
    
    for (const k of keys) {
        if (value && typeof value === 'object' && k in value) {
            value = value[k];
        } else {
            return undefined;
        }
    }
    
    return value;
}

/**
 * 设置配置值
 * 原始函数名: Il0
 */
function setConfigValue(key, value, isGlobal = false) {
    const config = readConfig(isGlobal);
    
    // 支持点号分隔的嵌套键
    const keys = key.split('.');
    let current = config;
    
    for (let i = 0; i < keys.length - 1; i++) {
        const k = keys[i];
        if (!current[k] || typeof current[k] !== 'object') {
            current[k] = {};
        }
        current = current[k];
    }
    
    current[keys[keys.length - 1]] = value;
    writeConfig(config, isGlobal);
}

/**
 * 移除配置值
 */
function removeConfigValue(key, values = null, isGlobal = false) {
    const config = readConfig(isGlobal);
    
    if (values && Array.isArray(values)) {
        // 从数组中移除特定值
        const currentValue = getConfigValue(key, isGlobal);
        if (Array.isArray(currentValue)) {
            const newValue = currentValue.filter(item => !values.includes(item));
            setConfigValue(key, newValue, isGlobal);
        }
    } else {
        // 移除整个配置项
        const keys = key.split('.');
        let current = config;
        
        for (let i = 0; i < keys.length - 1; i++) {
            const k = keys[i];
            if (!current[k] || typeof current[k] !== 'object') {
                return; // 键不存在
            }
            current = current[k];
        }
        
        delete current[keys[keys.length - 1]];
        writeConfig(config, isGlobal);
    }
}

/**
 * 列出所有配置
 * 原始函数名: Gl0
 */
function listAllConfig(isGlobal = false) {
    return readConfig(isGlobal);
}

/**
 * 检查配置是否为数组
 * 原始函数名: Bd
 */
function isConfigArray(key, isGlobal = false) {
    const value = getConfigValue(key, isGlobal);
    return Array.isArray(value);
}

/**
 * 向配置数组添加值
 */
function addToConfigArray(key, values, isGlobal = false) {
    let currentValue = getConfigValue(key, isGlobal);
    
    if (!Array.isArray(currentValue)) {
        currentValue = [];
    }
    
    // 添加新值，避免重复
    for (const value of values) {
        if (!currentValue.includes(value)) {
            currentValue.push(value);
        }
    }
    
    setConfigValue(key, currentValue, isGlobal);
}

/**
 * 获取合并的配置（本地配置覆盖全局配置）
 */
function getMergedConfig() {
    const globalConfig = readConfig(true);
    const localConfig = readConfig(false);
    
    return { ...globalConfig, ...localConfig };
}

module.exports = {
    getConfigValue,
    setConfigValue,
    removeConfigValue,
    listAllConfig,
    isConfigArray,
    addToConfigArray,
    getMergedConfig,
    readConfig,
    writeConfig
};
