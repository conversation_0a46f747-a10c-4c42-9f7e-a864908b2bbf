/**
 * Claude Code - Configuration System
 * 
 * 重构来源: cli-format-all.js 第339376-353247行 配置管理相关功能
 * 重构时间: 2025-01-19
 * 
 * 配置系统的主入口，整合配置管理和检查点管理功能
 */

// 导入配置模块
const {
  ConfigManager,
  createConfigManager,
  getGlobalConfigManager,
  getIDEIntegrationStatus,
  getMCPServersStatus,
  getMemoryStatus,
  getWorkingDirectoryStatus,
  getInstallationStatus,
  CONFIG_ITEM_TYPES,
  CONFIG_SECTIONS
} = require('./config-manager');

const {
  CheckpointManager,
  getGlobalCheckpointManager,
  CHECKPOINT_STATUS,
  CHECKPOINT_TYPES
} = require('./checkpoint-manager');

/**
 * 统一配置系统类
 * 整合配置管理和检查点管理功能
 */
class ConfigSystem {
  constructor(options = {}) {
    this.options = {
      enableCheckpoints: options.enableCheckpoints !== false,
      autoBackup: options.autoBackup !== false,
      maxCheckpoints: options.maxCheckpoints || 100,
      ...options
    };
    
    this.configManager = createConfigManager();
    this.checkpointManager = this.options.enableCheckpoints ? getGlobalCheckpointManager() : null;
    this.isInitialized = false;
  }

  /**
   * 初始化配置系统
   * 
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      // 初始化检查点管理器（如果启用）
      if (this.checkpointManager) {
        await this.checkpointManager.initialize();
      }
      
      this.isInitialized = true;
    } catch (error) {
      throw new Error(`Failed to initialize config system: ${error.message}`);
    }
  }

  /**
   * 获取系统状态
   * 
   * @param {Object} context - 应用上下文
   * @param {Array} mcpClients - MCP客户端列表
   * @param {Object} ideStatus - IDE状态
   * @returns {Promise<Array>} 状态部分列表
   */
  async getSystemStatus(context, mcpClients = [], ideStatus = null) {
    return await this.configManager.getSystemStatus(context, mcpClients, ideStatus);
  }

  /**
   * 获取配置值
   * 
   * @param {string} key - 配置键
   * @param {any} defaultValue - 默认值
   * @returns {any} 配置值
   */
  getConfig(key, defaultValue = null) {
    return this.configManager.getConfig(key, defaultValue);
  }

  /**
   * 设置配置值
   * 
   * @param {string} key - 配置键
   * @param {any} value - 配置值
   * @returns {Promise<void>}
   */
  async setConfig(key, value) {
    // 如果启用自动备份，先创建检查点
    if (this.options.autoBackup && this.checkpointManager) {
      await this.createCheckpoint(`Config change: ${key}`, CHECKPOINT_TYPES.AUTO);
    }
    
    this.configManager.setConfig(key, value);
  }

  /**
   * 更新配置
   * 
   * @param {Object} newConfig - 新配置
   * @returns {Promise<void>}
   */
  async updateConfig(newConfig) {
    // 如果启用自动备份，先创建检查点
    if (this.options.autoBackup && this.checkpointManager) {
      await this.createCheckpoint('Config update', CHECKPOINT_TYPES.AUTO);
    }
    
    this.configManager.updateConfig(newConfig);
  }

  /**
   * 重置配置
   * 
   * @returns {Promise<void>}
   */
  async resetConfig() {
    // 如果启用自动备份，先创建检查点
    if (this.options.autoBackup && this.checkpointManager) {
      await this.createCheckpoint('Config reset', CHECKPOINT_TYPES.BACKUP);
    }
    
    this.configManager.resetConfig();
  }

  /**
   * 创建检查点
   * 
   * @param {string} label - 检查点标签
   * @param {string} type - 检查点类型
   * @returns {Promise<Object|null>} 检查点对象
   */
  async createCheckpoint(label = 'Manual checkpoint', type = CHECKPOINT_TYPES.MANUAL) {
    if (!this.checkpointManager) {
      return null;
    }

    if (!this.isInitialized) {
      await this.initialize();
    }

    return await this.checkpointManager.saveCheckpoint(label, type);
  }

  /**
   * 恢复检查点
   * 
   * @param {string} commitHash - 提交哈希
   * @returns {Promise<Object|null>} 恢复结果
   */
  async restoreCheckpoint(commitHash) {
    if (!this.checkpointManager) {
      throw new Error('Checkpoints not enabled');
    }

    if (!this.isInitialized) {
      await this.initialize();
    }

    return await this.checkpointManager.restoreCheckpoint(commitHash);
  }

  /**
   * 获取检查点列表
   * 
   * @param {number} limit - 限制数量
   * @returns {Array} 检查点列表
   */
  getCheckpoints(limit = 50) {
    if (!this.checkpointManager) {
      return [];
    }

    return this.checkpointManager.getCheckpoints(limit);
  }

  /**
   * 获取检查点详情
   * 
   * @param {string} commitHash - 提交哈希
   * @returns {Promise<Object|null>} 检查点详情
   */
  async getCheckpointDetails(commitHash) {
    if (!this.checkpointManager) {
      return null;
    }

    return await this.checkpointManager.getCheckpointDetails(commitHash);
  }

  /**
   * 删除检查点
   * 
   * @param {string} commitHash - 提交哈希
   * @returns {Promise<boolean>} 是否成功删除
   */
  async deleteCheckpoint(commitHash) {
    if (!this.checkpointManager) {
      return false;
    }

    return await this.checkpointManager.deleteCheckpoint(commitHash);
  }

  /**
   * 清理旧检查点
   * 
   * @param {number} maxAge - 最大年龄（天）
   * @returns {Promise<number>} 清理的检查点数量
   */
  async cleanupOldCheckpoints(maxAge = 30) {
    if (!this.checkpointManager) {
      return 0;
    }

    return await this.checkpointManager.cleanupOldCheckpoints(maxAge);
  }

  /**
   * 获取系统统计信息
   * 
   * @returns {Object} 统计信息
   */
  getStats() {
    const stats = {
      isInitialized: this.isInitialized,
      options: this.options,
      config: this.configManager.getStats()
    };

    if (this.checkpointManager) {
      stats.checkpoints = this.checkpointManager.getStatus();
    }

    return stats;
  }

  /**
   * 检查系统是否就绪
   * 
   * @returns {boolean} 是否就绪
   */
  isReady() {
    return this.isInitialized;
  }

  /**
   * 重置系统
   */
  reset() {
    this.isInitialized = false;
    this.configManager.resetConfig();
    
    if (this.checkpointManager) {
      this.checkpointManager.reset();
    }
  }
}

/**
 * 创建配置系统实例
 * 
 * @param {Object} options - 系统选项
 * @returns {ConfigSystem} 配置系统实例
 */
function createConfigSystem(options = {}) {
  return new ConfigSystem(options);
}

// 全局配置系统实例
let globalConfigSystem = null;

/**
 * 获取全局配置系统实例
 * 
 * @param {Object} options - 系统选项（仅在首次创建时使用）
 * @returns {ConfigSystem} 全局配置系统实例
 */
function getGlobalConfigSystem(options = {}) {
  if (!globalConfigSystem) {
    globalConfigSystem = createConfigSystem(options);
  }
  return globalConfigSystem;
}

/**
 * 配置工具函数集合
 */
const ConfigUtils = {
  // 状态获取函数
  getIDEIntegrationStatus,
  getMCPServersStatus,
  getMemoryStatus,
  getWorkingDirectoryStatus,
  getInstallationStatus,
  
  // 验证函数
  validateConfigKey: (key) => {
    return typeof key === 'string' && key.length > 0;
  },
  
  validateConfigValue: (value) => {
    return value !== undefined;
  },
  
  // 格式化函数
  formatConfigItem: (item) => {
    return {
      label: item.label || 'Unknown',
      type: item.type || CONFIG_ITEM_TYPES.INFO,
      timestamp: new Date().toISOString()
    };
  },
  
  // 检查点工具
  generateCheckpointLabel: (action, context = '') => {
    const timestamp = new Date().toISOString().slice(0, 19);
    return `${action}${context ? ` (${context})` : ''} - ${timestamp}`;
  },
  
  isValidCommitHash: (hash) => {
    return typeof hash === 'string' && /^[a-f0-9]{7,40}$/i.test(hash);
  }
};

module.exports = {
  // 主要类和函数
  ConfigSystem,
  createConfigSystem,
  getGlobalConfigSystem,
  
  // 核心组件
  ConfigManager,
  CheckpointManager,
  
  // 管理器获取函数
  createConfigManager,
  getGlobalConfigManager,
  getGlobalCheckpointManager,
  
  // 状态获取函数
  getIDEIntegrationStatus,
  getMCPServersStatus,
  getMemoryStatus,
  getWorkingDirectoryStatus,
  getInstallationStatus,
  
  // 工具函数
  ConfigUtils,
  
  // 常量
  CONFIG_ITEM_TYPES,
  CONFIG_SECTIONS,
  CHECKPOINT_STATUS,
  CHECKPOINT_TYPES
};
