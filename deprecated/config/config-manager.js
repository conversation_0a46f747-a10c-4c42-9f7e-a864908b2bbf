/**
 * Claude Code - Configuration Manager
 * 
 * 重构来源: cli-format-all.js 第339376-353247行 配置管理相关功能
 * 重构时间: 2025-01-19
 * 
 * 配置管理系统，处理应用配置、状态显示和设置管理
 */

// Node.js 内置模块
const { createHash } = require('crypto');
const { join } = require('path');

// TODO_REFACTOR: 以下是未重构的依赖，需要后续替换

// TODO_REFACTOR: 查找oJ平台检测函数定义
const isPlatformSupported = () => {
  throw new Error('Platform detection function not yet refactored (original: oJ)');
};

// TODO_REFACTOR: 查找iA终端信息定义
const terminalInfo = {
  terminal: 'vscode'
};

// TODO_REFACTOR: 查找S01终端名称获取函数定义
const getTerminalName = (terminal) => {
  throw new Error('Terminal name function not yet refactored (original: S01)');
};

// TODO_REFACTOR: 查找hZ JetBrains检测函数定义
const isJetBrains = () => {
  throw new Error('JetBrains detection function not yet refactored (original: hZ)');
};

// TODO_REFACTOR: 查找cA当前目录获取函数定义
const getCurrentDirectory = () => {
  throw new Error('Current directory function not yet refactored (original: cA)');
};

// TODO_REFACTOR: 查找bp安装信息获取函数定义
const getInstallationInfo = async () => {
  throw new Error('Installation info function not yet refactored (original: bp)');
};

// TODO_REFACTOR: 查找GB1大文件获取函数定义
const getLargeFiles = () => {
  throw new Error('Large files function not yet refactored (original: GB1)');
};

// TODO_REFACTOR: 查找eF函数定义
const getMemoryFiles = () => {
  throw new Error('Memory files function not yet refactored (original: eF)');
};

// TODO_REFACTOR: 查找e$ULTRACLAUDE文件获取函数定义
const getUltraClaudeFile = () => {
  throw new Error('UltraClaude file function not yet refactored (original: e$)');
};

// TODO_REFACTOR: 查找ED1文件类型获取函数定义
const getFileType = (path) => {
  throw new Error('File type function not yet refactored (original: ED1)');
};

// TODO_REFACTOR: 查找CI数字格式化函数定义
const formatNumber = (num) => {
  throw new Error('Number formatting function not yet refactored (original: CI)');
};

// TODO_REFACTOR: 查找DB1和Ip常量定义
const LARGE_FILE_THRESHOLD = 100000;
const ULTRACLAUD_FILE_LIMIT = 200000;

/**
 * 配置项类型枚举
 */
const CONFIG_ITEM_TYPES = {
  CHECK: 'check',
  ERROR: 'error',
  INFO: 'info',
  WARNING: 'warning'
};

/**
 * 配置部分枚举
 */
const CONFIG_SECTIONS = {
  IDE_INTEGRATION: 'ide_integration',
  MCP_SERVERS: 'mcp_servers',
  MEMORY: 'memory',
  WORKING_DIRECTORY: 'working_directory',
  INSTALLATION: 'installation'
};

/**
 * IDE集成状态管理
 * 重构自: 第339377-339429行的GI4函数
 * 
 * @param {Array} mcpClients - MCP客户端列表
 * @param {Object} ideStatus - IDE安装状态
 * @returns {Object|null} IDE集成状态信息
 */
function getIDEIntegrationStatus(mcpClients = [], ideStatus = null) {
  if (!isPlatformSupported() || !terminalInfo.terminal) {
    return null;
  }

  const ideClient = mcpClients?.find(client => client.name === 'ide');
  const terminalName = getTerminalName(terminalInfo.terminal);
  const items = [];

  // 检查IDE连接状态
  if (ideClient) {
    if (ideClient.type === 'connected') {
      items.push({
        label: `Connected to ${terminalName} extension`,
        type: CONFIG_ITEM_TYPES.CHECK
      });
    } else {
      items.push({
        label: `Not connected to ${terminalName}`,
        type: CONFIG_ITEM_TYPES.ERROR
      });
    }
  }

  // 检查IDE安装状态
  if (ideStatus && ideStatus.installed) {
    if (ideStatus && ideClient && ideClient.type === 'connected' && 
        ideStatus.installedVersion !== ideClient.serverInfo?.version) {
      items.push({
        label: `Installed ${terminalName} extension version ${ideStatus.installedVersion} (server version: ${ideClient.serverInfo?.version})`,
        type: CONFIG_ITEM_TYPES.INFO
      });
    } else if (isJetBrains() && ideClient?.type !== 'connected') {
      items.push({
        label: `Installed ${terminalName} plugin but connection is not established.\nPlease restart your IDE or try installing from https://docs.anthropic.com/s/claude-code-jetbrains`,
        type: CONFIG_ITEM_TYPES.INFO
      });
    } else {
      items.push({
        label: `Installed ${terminalName} extension`,
        type: CONFIG_ITEM_TYPES.CHECK
      });
    }
  }

  // 检查安装错误
  if (ideStatus && ideStatus.error) {
    if (isJetBrains()) {
      items.push({
        label: `Error installing ${terminalName} plugin: ${ideStatus.error}\nPlease restart your IDE or try installing from https://docs.anthropic.com/s/claude-code-jetbrains`,
        type: CONFIG_ITEM_TYPES.ERROR
      });
    } else {
      items.push({
        label: `Error installing ${terminalName} extension: ${ideStatus.error}\nPlease restart your IDE and try again.`,
        type: CONFIG_ITEM_TYPES.ERROR
      });
    }
  }

  return {
    title: 'IDE Integration',
    command: '/config',
    items
  };
}

/**
 * MCP服务器状态管理
 * 重构自: 第339430-339446行的ZI4函数
 * 
 * @param {Array} mcpClients - MCP客户端列表
 * @returns {Object|null} MCP服务器状态信息
 */
function getMCPServersStatus(mcpClients = []) {
  const items = [];
  
  mcpClients
    .filter(client => client.name !== 'ide')
    .forEach(client => {
      items.push({
        label: client.name,
        type: client.type === 'failed' ? CONFIG_ITEM_TYPES.ERROR : 
              client.type === 'pending' ? CONFIG_ITEM_TYPES.INFO : 
              CONFIG_ITEM_TYPES.CHECK
      });
    });

  if (items.length === 0) {
    return null;
  }

  return {
    title: 'MCP servers',
    command: '/mcp',
    items
  };
}

/**
 * 内存使用状态管理
 * 重构自: 第339447-339475行的FI4函数
 * 
 * @param {Object} context - 应用上下文
 * @returns {Object|null} 内存使用状态信息
 */
function getMemoryStatus(context) {
  const largeFiles = getLargeFiles();
  const memoryFiles = getMemoryFiles();
  const ultraClaudeFile = getUltraClaudeFile();

  if (memoryFiles.length === 0 && largeFiles.length === 0 && !ultraClaudeFile) {
    return null;
  }

  const items = [];

  // 检查大文件
  largeFiles.forEach(file => {
    const fileType = getFileType(file.path);
    items.push({
      label: `Large ${fileType} will impact performance (${formatNumber(file.content.length)} chars > ${formatNumber(LARGE_FILE_THRESHOLD)})`,
      type: CONFIG_ITEM_TYPES.ERROR
    });
  });

  // 检查ULTRACLAUDE.md文件
  if (ultraClaudeFile && ultraClaudeFile.content.length > ULTRACLAUD_FILE_LIMIT) {
    items.push({
      label: `ULTRACLAUDE.md file exceeds ${formatNumber(ULTRACLAUD_FILE_LIMIT)} characters (${formatNumber(ultraClaudeFile.content.length)} chars)`,
      type: CONFIG_ITEM_TYPES.ERROR
    });
  }

  return {
    title: 'Memory',
    command: '/memory',
    items,
    content: null // TODO: 添加内存组件
  };
}

/**
 * 工作目录状态管理
 * 重构自: 第339476-339488行的YI4函数
 * 
 * @returns {Object} 工作目录状态信息
 */
function getWorkingDirectoryStatus() {
  const items = [];
  const currentDir = getCurrentDirectory();

  items.push({
    label: currentDir,
    type: CONFIG_ITEM_TYPES.INFO
  });

  return {
    title: 'Working Directory',
    command: '',
    items
  };
}

/**
 * 安装状态管理
 * 重构自: 第339489-339500行的WI4函数
 * 
 * @returns {Promise<Object|null>} 安装状态信息
 */
async function getInstallationStatus() {
  const installationInfo = await getInstallationInfo();
  
  if (installationInfo.length === 0) {
    return null;
  }

  return {
    title: 'Installation',
    command: '',
    items: installationInfo.map(info => ({
      label: info,
      type: CONFIG_ITEM_TYPES.INFO
    }))
  };
}

/**
 * 配置管理器类
 */
class ConfigManager {
  constructor() {
    this.config = {};
    this.statusCache = new Map();
    this.cacheTimeout = 5000; // 5秒缓存
  }

  /**
   * 获取完整的系统状态
   * 
   * @param {Object} context - 应用上下文
   * @param {Array} mcpClients - MCP客户端列表
   * @param {Object} ideStatus - IDE状态
   * @returns {Promise<Array>} 状态部分列表
   */
  async getSystemStatus(context, mcpClients = [], ideStatus = null) {
    const sections = [];

    // IDE集成状态
    const ideIntegration = getIDEIntegrationStatus(mcpClients, ideStatus);
    if (ideIntegration) {
      sections.push(ideIntegration);
    }

    // MCP服务器状态
    const mcpServers = getMCPServersStatus(mcpClients);
    if (mcpServers) {
      sections.push(mcpServers);
    }

    // 内存状态
    const memory = getMemoryStatus(context);
    if (memory) {
      sections.push(memory);
    }

    // 工作目录状态
    const workingDirectory = getWorkingDirectoryStatus();
    sections.push(workingDirectory);

    // 安装状态
    const installation = await getInstallationStatus();
    if (installation) {
      sections.push(installation);
    }

    return sections;
  }

  /**
   * 获取缓存的状态
   * 
   * @param {string} key - 缓存键
   * @returns {any} 缓存的值
   */
  getCachedStatus(key) {
    const cached = this.statusCache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.value;
    }
    return null;
  }

  /**
   * 设置状态缓存
   * 
   * @param {string} key - 缓存键
   * @param {any} value - 缓存值
   */
  setCachedStatus(key, value) {
    this.statusCache.set(key, {
      value,
      timestamp: Date.now()
    });
  }

  /**
   * 清除状态缓存
   * 
   * @param {string} key - 缓存键（可选）
   */
  clearStatusCache(key = null) {
    if (key) {
      this.statusCache.delete(key);
    } else {
      this.statusCache.clear();
    }
  }

  /**
   * 获取配置值
   * 
   * @param {string} key - 配置键
   * @param {any} defaultValue - 默认值
   * @returns {any} 配置值
   */
  getConfig(key, defaultValue = null) {
    return this.config[key] ?? defaultValue;
  }

  /**
   * 设置配置值
   * 
   * @param {string} key - 配置键
   * @param {any} value - 配置值
   */
  setConfig(key, value) {
    this.config[key] = value;
  }

  /**
   * 更新配置
   * 
   * @param {Object} newConfig - 新配置
   */
  updateConfig(newConfig) {
    this.config = {
      ...this.config,
      ...newConfig
    };
  }

  /**
   * 重置配置
   */
  resetConfig() {
    this.config = {};
    this.clearStatusCache();
  }

  /**
   * 获取配置统计信息
   * 
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      configKeys: Object.keys(this.config).length,
      cacheEntries: this.statusCache.size,
      cacheTimeout: this.cacheTimeout
    };
  }
}

/**
 * 创建配置管理器实例
 * 
 * @returns {ConfigManager} 配置管理器实例
 */
function createConfigManager() {
  return new ConfigManager();
}

// 全局配置管理器实例
let globalConfigManager = null;

/**
 * 获取全局配置管理器实例
 * 
 * @returns {ConfigManager} 全局配置管理器实例
 */
function getGlobalConfigManager() {
  if (!globalConfigManager) {
    globalConfigManager = createConfigManager();
  }
  return globalConfigManager;
}

module.exports = {
  // 主要类和函数
  ConfigManager,
  createConfigManager,
  getGlobalConfigManager,
  
  // 状态获取函数
  getIDEIntegrationStatus,
  getMCPServersStatus,
  getMemoryStatus,
  getWorkingDirectoryStatus,
  getInstallationStatus,
  
  // 常量
  CONFIG_ITEM_TYPES,
  CONFIG_SECTIONS
};
