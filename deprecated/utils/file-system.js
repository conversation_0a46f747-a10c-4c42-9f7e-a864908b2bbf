/**
 * 文件系统工具
 * 从原始文件第295001-295100行等区域提取
 * 提供文件路径处理、权限管理等功能
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

/**
 * 获取当前工作目录
 * 原始函数: cA()
 */
function getCurrentWorkingDirectory() {
    return process.cwd();
}

/**
 * 获取用户主目录
 * 原始函数: CN9()
 */
function getUserHomeDirectory() {
    return os.homedir();
}

/**
 * 获取项目设置根目录
 * 原始函数: VN9(B)
 */
function getProjectSettingsRoot(context) {
    switch (context) {
        case "projectSettings":
            return getCurrentWorkingDirectory();
        case "userSettings":
            return getUserHomeDirectory();
        default:
            return getCurrentWorkingDirectory();
    }
}

/**
 * 规范化路径模式
 * 原始函数: nx1(B)
 */
function normalizePattern(pattern) {
    // 移除开头的路径分隔符
    if (pattern.startsWith(path.sep)) {
        return pattern.slice(1);
    }
    return pattern;
}

/**
 * 解析模式根路径和相对模式
 * 原始函数: OrA(A, B)
 */
function parsePatternRoot(pattern, context) {
    const sep = path.sep;
    
    if (pattern.startsWith(`${sep}${sep}`)) {
        // 绝对路径模式 (//pattern)
        return {
            relativePattern: pattern.slice(1),
            root: sep
        };
    } else if (pattern.startsWith(`~${sep}`)) {
        // 用户主目录模式 (~pattern)
        return {
            relativePattern: pattern.slice(1),
            root: getUserHomeDirectory()
        };
    } else if (pattern.startsWith(sep)) {
        // 项目根目录模式 (/pattern)
        return {
            relativePattern: pattern,
            root: getProjectSettingsRoot(context)
        };
    }
    
    // 相对路径模式
    return {
        relativePattern: pattern,
        root: null
    };
}

/**
 * 构建模式路径
 * 原始函数: KN9({patternRoot: A, pattern: B, rootPath: Q})
 */
function buildPatternPath({ patternRoot, pattern, rootPath }) {
    const fullPath = path.join(patternRoot, pattern);
    
    if (patternRoot === rootPath) {
        return normalizePattern(pattern);
    } else if (fullPath.startsWith(`${rootPath}${path.sep}`)) {
        const relativePath = fullPath.slice(rootPath.length);
        return normalizePattern(relativePath);
    } else {
        const relativePath = path.relative(rootPath, patternRoot);
        
        if (!relativePath || relativePath.startsWith(`..${path.sep}`) || relativePath === "..") {
            return null;
        } else {
            const combinedPath = path.join(relativePath, pattern);
            return normalizePattern(combinedPath);
        }
    }
}

/**
 * 收集所有模式
 * 原始函数: CD1(A, B)
 */
function collectPatterns(patternMap, rootPath) {
    const patterns = new Set(patternMap.get(null) ?? []);
    
    for (const [patternRoot, patternList] of patternMap.entries()) {
        if (patternRoot === null) {
            continue;
        }
        
        for (const pattern of patternList) {
            const resolvedPattern = buildPatternPath({
                patternRoot: patternRoot,
                pattern: pattern,
                rootPath: rootPath
            });
            
            if (resolvedPattern) {
                patterns.add(resolvedPattern);
            }
        }
    }
    
    return Array.from(patterns);
}

/**
 * 检查文件是否存在
 */
function fileExists(filePath) {
    try {
        return fs.existsSync(filePath);
    } catch (error) {
        return false;
    }
}

/**
 * 检查路径是否为目录
 */
function isDirectory(filePath) {
    try {
        const stats = fs.statSync(filePath);
        return stats.isDirectory();
    } catch (error) {
        return false;
    }
}

/**
 * 检查路径是否为文件
 */
function isFile(filePath) {
    try {
        const stats = fs.statSync(filePath);
        return stats.isFile();
    } catch (error) {
        return false;
    }
}

/**
 * 创建目录（递归）
 */
function createDirectory(dirPath) {
    try {
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
        }
        return true;
    } catch (error) {
        console.error(`Failed to create directory ${dirPath}:`, error.message);
        return false;
    }
}

/**
 * 读取文件内容
 */
function readFile(filePath, encoding = 'utf8') {
    try {
        return fs.readFileSync(filePath, { encoding });
    } catch (error) {
        throw new Error(`Failed to read file ${filePath}: ${error.message}`);
    }
}

/**
 * 写入文件内容
 */
function writeFile(filePath, content, encoding = 'utf8') {
    try {
        // 确保目录存在
        const dirPath = path.dirname(filePath);
        createDirectory(dirPath);
        
        fs.writeFileSync(filePath, content, { encoding });
        return true;
    } catch (error) {
        throw new Error(`Failed to write file ${filePath}: ${error.message}`);
    }
}

/**
 * 删除文件
 */
function deleteFile(filePath) {
    try {
        if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
        }
        return true;
    } catch (error) {
        throw new Error(`Failed to delete file ${filePath}: ${error.message}`);
    }
}

/**
 * 删除目录（递归）
 */
function deleteDirectory(dirPath) {
    try {
        if (fs.existsSync(dirPath)) {
            fs.rmSync(dirPath, { recursive: true, force: true });
        }
        return true;
    } catch (error) {
        throw new Error(`Failed to delete directory ${dirPath}: ${error.message}`);
    }
}

/**
 * 获取文件统计信息
 */
function getFileStats(filePath) {
    try {
        return fs.statSync(filePath);
    } catch (error) {
        throw new Error(`Failed to get stats for ${filePath}: ${error.message}`);
    }
}

/**
 * 列出目录内容
 */
function listDirectory(dirPath) {
    try {
        return fs.readdirSync(dirPath);
    } catch (error) {
        throw new Error(`Failed to list directory ${dirPath}: ${error.message}`);
    }
}

/**
 * 复制文件
 */
function copyFile(sourcePath, targetPath) {
    try {
        // 确保目标目录存在
        const targetDir = path.dirname(targetPath);
        createDirectory(targetDir);
        
        fs.copyFileSync(sourcePath, targetPath);
        return true;
    } catch (error) {
        throw new Error(`Failed to copy file from ${sourcePath} to ${targetPath}: ${error.message}`);
    }
}

/**
 * 移动/重命名文件
 */
function moveFile(sourcePath, targetPath) {
    try {
        // 确保目标目录存在
        const targetDir = path.dirname(targetPath);
        createDirectory(targetDir);
        
        fs.renameSync(sourcePath, targetPath);
        return true;
    } catch (error) {
        throw new Error(`Failed to move file from ${sourcePath} to ${targetPath}: ${error.message}`);
    }
}

module.exports = {
    getCurrentWorkingDirectory,
    getUserHomeDirectory,
    getProjectSettingsRoot,
    normalizePattern,
    parsePatternRoot,
    buildPatternPath,
    collectPatterns,
    fileExists,
    isDirectory,
    isFile,
    createDirectory,
    readFile,
    writeFile,
    deleteFile,
    deleteDirectory,
    getFileStats,
    listDirectory,
    copyFile,
    moveFile
};
