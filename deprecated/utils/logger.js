/**
 * 日志工具
 * 提供统一的日志记录功能
 */

const fs = require('fs');
const path = require('path');
const os = require('os');
const { createDirectory } = require('./file-system');

// 日志级别
const LOG_LEVELS = {
    ERROR: 0,
    WARN: 1,
    INFO: 2,
    DEBUG: 3
};

// 日志配置
const LOG_CONFIG = {
    level: process.env.LOG_LEVEL || 'INFO',
    enableConsole: true,
    enableFile: true,
    maxFileSize: 10 * 1024 * 1024, // 10MB
    maxFiles: 5
};

// 日志目录
const LOG_DIR = path.join(os.homedir(), '.claude-code', 'logs');
const LOG_FILE = path.join(LOG_DIR, 'claude-code.log');

/**
 * 确保日志目录存在
 */
function ensureLogDirectory() {
    try {
        createDirectory(LOG_DIR);
    } catch (error) {
        console.warn('Failed to create log directory:', error.message);
    }
}

/**
 * 格式化日志消息
 */
function formatLogMessage(level, message, data = null) {
    const timestamp = new Date().toISOString();
    const pid = process.pid;
    
    let formattedMessage = `[${timestamp}] [${pid}] [${level}] ${message}`;
    
    if (data) {
        if (data instanceof Error) {
            formattedMessage += `\n${data.stack}`;
        } else if (typeof data === 'object') {
            formattedMessage += `\n${JSON.stringify(data, null, 2)}`;
        } else {
            formattedMessage += ` ${data}`;
        }
    }
    
    return formattedMessage;
}

/**
 * 写入日志文件
 */
function writeToFile(message) {
    if (!LOG_CONFIG.enableFile) {
        return;
    }
    
    try {
        ensureLogDirectory();
        
        // 检查文件大小，如果太大则轮转
        if (fs.existsSync(LOG_FILE)) {
            const stats = fs.statSync(LOG_FILE);
            if (stats.size > LOG_CONFIG.maxFileSize) {
                rotateLogFile();
            }
        }
        
        fs.appendFileSync(LOG_FILE, message + '\n', 'utf8');
    } catch (error) {
        console.warn('Failed to write to log file:', error.message);
    }
}

/**
 * 轮转日志文件
 */
function rotateLogFile() {
    try {
        // 移动现有日志文件
        for (let i = LOG_CONFIG.maxFiles - 1; i > 0; i--) {
            const oldFile = `${LOG_FILE}.${i}`;
            const newFile = `${LOG_FILE}.${i + 1}`;
            
            if (fs.existsSync(oldFile)) {
                if (i === LOG_CONFIG.maxFiles - 1) {
                    fs.unlinkSync(oldFile); // 删除最老的文件
                } else {
                    fs.renameSync(oldFile, newFile);
                }
            }
        }
        
        // 移动当前日志文件
        if (fs.existsSync(LOG_FILE)) {
            fs.renameSync(LOG_FILE, `${LOG_FILE}.1`);
        }
    } catch (error) {
        console.warn('Failed to rotate log file:', error.message);
    }
}

/**
 * 通用日志函数
 */
function log(level, message, data = null) {
    const levelValue = LOG_LEVELS[level.toUpperCase()];
    const configLevelValue = LOG_LEVELS[LOG_CONFIG.level.toUpperCase()];
    
    if (levelValue > configLevelValue) {
        return; // 日志级别不够，跳过
    }
    
    const formattedMessage = formatLogMessage(level, message, data);
    
    // 输出到控制台
    if (LOG_CONFIG.enableConsole) {
        switch (level.toUpperCase()) {
            case 'ERROR':
                console.error(formattedMessage);
                break;
            case 'WARN':
                console.warn(formattedMessage);
                break;
            case 'DEBUG':
                console.debug(formattedMessage);
                break;
            default:
                console.log(formattedMessage);
        }
    }
    
    // 写入文件
    writeToFile(formattedMessage);
}

/**
 * 错误日志
 * 原始函数: v1(error)
 */
function logError(error, context = null) {
    if (error instanceof Error) {
        log('ERROR', error.message, error);
    } else {
        log('ERROR', String(error), context);
    }
}

/**
 * 警告日志
 */
function logWarn(message, data = null) {
    log('WARN', message, data);
}

/**
 * 信息日志
 */
function logInfo(message, data = null) {
    log('INFO', message, data);
}

/**
 * 调试日志
 */
function logDebug(message, data = null) {
    log('DEBUG', message, data);
}

/**
 * 性能日志
 */
function logPerformance(operation, startTime, metadata = {}) {
    const duration = Date.now() - startTime;
    logInfo(`Performance: ${operation} took ${duration}ms`, metadata);
}

/**
 * 设置日志级别
 */
function setLogLevel(level) {
    if (LOG_LEVELS.hasOwnProperty(level.toUpperCase())) {
        LOG_CONFIG.level = level.toUpperCase();
    } else {
        logWarn(`Invalid log level: ${level}`);
    }
}

/**
 * 启用/禁用控制台日志
 */
function setConsoleLogging(enabled) {
    LOG_CONFIG.enableConsole = enabled;
}

/**
 * 启用/禁用文件日志
 */
function setFileLogging(enabled) {
    LOG_CONFIG.enableFile = enabled;
}

/**
 * 获取日志配置
 */
function getLogConfig() {
    return { ...LOG_CONFIG };
}

/**
 * 清理旧日志文件
 */
function cleanupOldLogs(daysToKeep = 7) {
    try {
        const cutoffTime = Date.now() - (daysToKeep * 24 * 60 * 60 * 1000);
        
        if (fs.existsSync(LOG_DIR)) {
            const files = fs.readdirSync(LOG_DIR);
            
            for (const file of files) {
                const filePath = path.join(LOG_DIR, file);
                const stats = fs.statSync(filePath);
                
                if (stats.mtime.getTime() < cutoffTime) {
                    fs.unlinkSync(filePath);
                    logInfo(`Cleaned up old log file: ${file}`);
                }
            }
        }
    } catch (error) {
        logWarn('Failed to cleanup old logs:', error.message);
    }
}

module.exports = {
    log,
    logError,
    logWarn,
    logInfo,
    logDebug,
    logPerformance,
    setLogLevel,
    setConsoleLogging,
    setFileLogging,
    getLogConfig,
    cleanupOldLogs,
    LOG_LEVELS,
    LOG_DIR,
    LOG_FILE
};
