/**
 * 系统工具函数
 * 处理系统相关的检查和操作
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

/**
 * 检查是否为本地安装
 * 原始函数名: nP
 */
function isLocalInstallation() {
    try {
        // 检查是否在node_modules中运行
        const currentPath = process.argv[1] || __filename;
        return currentPath.includes('node_modules');
    } catch (error) {
        console.warn('Failed to check installation type:', error.message);
        return false;
    }
}

/**
 * 检查认证配置
 * 原始函数名: zX
 */
function checkAuthConfig() {
    // 检查环境变量
    if (process.env.ANTHROPIC_API_KEY || process.env.CLAUDE_API_KEY) {
        return false; // 已有环境变量配置
    }
    
    // 检查配置文件中的认证信息
    try {
        const configPath = path.join(os.homedir(), '.claude-code', 'auth.json');
        if (fs.existsSync(configPath)) {
            const authConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
            return !authConfig.apiKey && !authConfig.token;
        }
    } catch (error) {
        console.warn('Failed to check auth config:', error.message);
    }
    
    return true; // 需要配置认证
}

/**
 * 获取系统信息
 */
function getSystemInfo() {
    return {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version,
        osVersion: os.release(),
        totalMemory: os.totalmem(),
        freeMemory: os.freemem(),
        cpus: os.cpus().length,
        uptime: os.uptime()
    };
}

/**
 * 检查网络连接
 */
async function checkNetworkConnection() {
    const { spawn } = require('child_process');
    
    return new Promise((resolve) => {
        const ping = spawn('ping', ['-c', '1', '8.8.8.8']);
        
        ping.on('close', (code) => {
            resolve(code === 0);
        });
        
        ping.on('error', () => {
            resolve(false);
        });
        
        // 超时处理
        setTimeout(() => {
            ping.kill();
            resolve(false);
        }, 5000);
    });
}

/**
 * 获取可用磁盘空间
 */
function getDiskSpace() {
    try {
        const stats = fs.statSync(os.homedir());
        return {
            available: true,
            // TODO: 实现实际的磁盘空间检查
            free: 'unknown',
            total: 'unknown'
        };
    } catch (error) {
        return {
            available: false,
            error: error.message
        };
    }
}

/**
 * 检查必要的目录权限
 */
function checkDirectoryPermissions() {
    const directories = [
        os.homedir(),
        path.join(os.homedir(), '.claude-code'),
        process.cwd()
    ];
    
    const results = {};
    
    for (const dir of directories) {
        try {
            fs.accessSync(dir, fs.constants.R_OK | fs.constants.W_OK);
            results[dir] = { readable: true, writable: true };
        } catch (error) {
            results[dir] = { 
                readable: false, 
                writable: false, 
                error: error.message 
            };
        }
    }
    
    return results;
}

/**
 * 获取环境变量信息
 */
function getEnvironmentInfo() {
    return {
        nodeEnv: process.env.NODE_ENV,
        path: process.env.PATH,
        home: process.env.HOME || process.env.USERPROFILE,
        shell: process.env.SHELL,
        term: process.env.TERM,
        // 不包含敏感信息
        hasAnthropicKey: !!process.env.ANTHROPIC_API_KEY,
        hasClaudeKey: !!process.env.CLAUDE_API_KEY
    };
}

/**
 * 运行系统诊断
 */
async function runSystemDiagnostics() {
    console.log('Running system diagnostics...');
    
    const diagnostics = {
        system: getSystemInfo(),
        installation: {
            isLocal: isLocalInstallation(),
            authConfigured: !checkAuthConfig()
        },
        network: await checkNetworkConnection(),
        disk: getDiskSpace(),
        permissions: checkDirectoryPermissions(),
        environment: getEnvironmentInfo(),
        timestamp: new Date().toISOString()
    };
    
    return diagnostics;
}

module.exports = {
    isLocalInstallation,
    checkAuthConfig,
    getSystemInfo,
    checkNetworkConnection,
    getDiskSpace,
    checkDirectoryPermissions,
    getEnvironmentInfo,
    runSystemDiagnostics
};
