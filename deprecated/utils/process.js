/**
 * 进程执行工具
 * 从原始文件中提取的进程管理和命令执行功能
 */

const { spawn, exec, execSync } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

/**
 * 执行命令并返回结果
 * 原始函数: I8(command, args, options)
 */
async function executeCommand(command, args = [], options = {}) {
    return new Promise((resolve, reject) => {
        const child = spawn(command, args, {
            stdio: ['pipe', 'pipe', 'pipe'],
            ...options
        });
        
        let stdout = '';
        let stderr = '';
        
        if (child.stdout) {
            child.stdout.on('data', (data) => {
                stdout += data.toString();
            });
        }
        
        if (child.stderr) {
            child.stderr.on('data', (data) => {
                stderr += data.toString();
            });
        }
        
        child.on('close', (code) => {
            resolve({
                code,
                stdout: stdout.trim(),
                stderr: stderr.trim()
            });
        });
        
        child.on('error', (error) => {
            reject(error);
        });
        
        // 处理超时
        if (options.timeout) {
            setTimeout(() => {
                child.kill('SIGTERM');
                reject(new Error(`Command timeout after ${options.timeout}ms`));
            }, options.timeout);
        }
    });
}

/**
 * 同步执行命令
 * 原始函数: Ma1(command, options)
 */
function executeCommandSync(command, options = {}) {
    try {
        return execSync(command, {
            encoding: 'utf8',
            ...options
        });
    } catch (error) {
        throw new Error(`Command failed: ${command}\n${error.message}`);
    }
}

/**
 * 异步执行命令（简化版）
 */
async function executeCommandAsync(command, options = {}) {
    try {
        const { stdout, stderr } = await execAsync(command, options);
        return {
            stdout: stdout.trim(),
            stderr: stderr.trim(),
            success: true
        };
    } catch (error) {
        return {
            stdout: '',
            stderr: error.message,
            success: false,
            error
        };
    }
}

/**
 * 检查命令是否可执行
 * 原始函数: yc0(A)
 */
function isCommandExecutable(commandPath) {
    try {
        const fs = require('fs');
        fs.accessSync(commandPath, fs.constants.X_OK);
        return true;
    } catch (error) {
        try {
            // 尝试执行版本命令来验证
            executeCommandSync(`${commandPath} --version`, {
                timeout: 1000,
                stdio: 'ignore'
            });
            return true;
        } catch {
            return false;
        }
    }
}

/**
 * 查找命令路径
 */
function findCommandPath(commandName) {
    try {
        return executeCommandSync(`which ${commandName}`, {
            stdio: ['ignore', 'pipe', 'ignore']
        }).toString().trim();
    } catch {
        return null;
    }
}

/**
 * 获取可用的Shell
 * 原始函数: ivQ()
 */
function getAvailableShell() {
    const findCommand = (commandName) => {
        try {
            return executeCommandSync(`which ${commandName}`, {
                stdio: ['ignore', 'pipe', 'ignore']
            }).toString().trim();
        } catch {
            return null;
        }
    };
    
    const currentShell = process.env.SHELL;
    const prefersBash = currentShell && (currentShell.includes('bash') || currentShell.includes('zsh'));
    const isBash = currentShell?.includes('bash');
    
    const zshPath = findCommand('zsh');
    const bashPath = findCommand('bash');
    
    const commonPaths = ['/bin', '/usr/bin', '/usr/local/bin', '/opt/homebrew/bin'];
    const shellCandidates = (isBash ? ['bash', 'zsh'] : ['zsh', 'bash'])
        .flatMap(shell => commonPaths.map(dir => `${dir}/${shell}`));
    
    // 构建候选列表
    const candidates = [];
    
    if (isBash) {
        if (bashPath) candidates.push(bashPath);
        if (zshPath) candidates.push(zshPath);
    } else {
        if (zshPath) candidates.push(zshPath);
        if (bashPath) candidates.push(bashPath);
    }
    
    // 添加通用路径
    candidates.push(...shellCandidates);
    
    // 如果当前Shell可用，优先使用
    if (prefersBash && isCommandExecutable(currentShell)) {
        candidates.unshift(currentShell);
    }
    
    // 查找第一个可用的Shell
    const availableShell = candidates.find(shell => shell && isCommandExecutable(shell));
    
    if (!availableShell) {
        const errorMessage = "No suitable shell found. Claude CLI requires a Posix shell environment. Please ensure you have a valid shell installed and the SHELL environment variable set.";
        throw new Error(errorMessage);
    }
    
    return availableShell;
}

/**
 * 创建Shell快照
 * 原始函数: nvQ(A)
 */
async function createShellSnapshot(shellPath) {
    const shellType = shellPath.includes('zsh') ? 'zsh' : 
                     shellPath.includes('bash') ? 'bash' : 'sh';
    
    console.log(`Creating shell snapshot for ${shellType} (${shellPath})`);
    
    return new Promise(async (resolve) => {
        try {
            const configFile = getShellConfigFile(shellPath);
            
            if (!fileExists(configFile)) {
                console.log(`Shell config file not found: ${configFile}`);
                resolve(undefined);
                return;
            }
            
            // 创建Shell环境快照
            const snapshotScript = createShellSnapshotScript(configFile, shellType);
            
            // 执行快照脚本
            const { stdout, code } = await executeCommand(shellPath, ['-c', snapshotScript], {
                timeout: 10000
            });
            
            if (code === 0) {
                resolve(stdout);
            } else {
                console.warn(`Shell snapshot failed with code ${code}`);
                resolve(undefined);
            }
        } catch (error) {
            console.warn('Shell snapshot creation failed:', error.message);
            resolve(undefined);
        }
    });
}

/**
 * 获取Shell配置文件路径
 * 原始函数: kc0(A)
 */
function getShellConfigFile(shellPath) {
    const homeDir = require('os').homedir();
    
    if (shellPath.includes('zsh')) {
        return require('path').join(homeDir, '.zshrc');
    } else if (shellPath.includes('bash')) {
        return require('path').join(homeDir, '.bashrc');
    } else {
        return require('path').join(homeDir, '.profile');
    }
}

/**
 * 创建Shell快照脚本
 */
function createShellSnapshotScript(configFile, shellType) {
    const { quote } = require('shell-quote');
    const tempFile = `/tmp/claude_shell_snapshot_${Date.now()}`;
    
    let pathValue = '';
    try {
        pathValue = executeCommandSync('echo $PATH', {
            encoding: 'utf8'
        }).trim();
    } catch {
        // 忽略错误
    }
    
    return `SNAPSHOT_FILE=${quote([tempFile])}
source "${configFile}" < /dev/null

# First, create/clear the snapshot file
echo "# Snapshot file" >| "$SNAPSHOT_FILE"

# When this file is sourced, we first unalias to avoid conflicts
echo "# Unset all aliases to avoid conflicts with functions" >> "$SNAPSHOT_FILE"
echo "unalias -a 2>/dev/null || true" >> "$SNAPSHOT_FILE"

echo "# Aliases" >> "$SNAPSHOT_FILE"
# Filter out winpty aliases on Windows to avoid "stdin is not a tty" errors
if [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
  alias | grep -v "='winpty " | sed 's/^alias //g' | sed 's/^/alias -- /' | head -n 1000 >> "$SNAPSHOT_FILE"
else
  alias | sed 's/^alias //g' | sed 's/^/alias -- /' | head -n 1000 >> "$SNAPSHOT_FILE"
fi

# Check if rg is available, if not create an alias to bundled ripgrep
echo "# Check for rg availability" >> "$SNAPSHOT_FILE"
echo "if ! command -v rg >/dev/null 2>&1; then" >> "$SNAPSHOT_FILE"
echo "  alias rg='rg'" >> "$SNAPSHOT_FILE"
echo "fi" >> "$SNAPSHOT_FILE"

# Add PATH to the file
echo "export PATH=${quote([pathValue || ""])}" >> "$SNAPSHOT_FILE"

cat "$SNAPSHOT_FILE"
rm -f "$SNAPSHOT_FILE"
`;
}

/**
 * 检查文件是否存在
 */
function fileExists(filePath) {
    try {
        const fs = require('fs');
        return fs.existsSync(filePath);
    } catch {
        return false;
    }
}

module.exports = {
    executeCommand,
    executeCommandSync,
    executeCommandAsync,
    isCommandExecutable,
    findCommandPath,
    getAvailableShell,
    createShellSnapshot,
    getShellConfigFile,
    createShellSnapshotScript
};
