/**
 * Claude Code - Print Mode Handler
 * 
 * 重构来源: cli-format-all.js 第353551-353761行 (210行)
 * 重构时间: 2025-01-19
 * 
 * 处理--print模式的非交互式AI对话，包括流式输入和输出格式化
 */

// Node.js 内置模块
const { cwd } = require('process');

// TODO_REFACTOR: 以下是未重构的依赖，需要后续替换

// TODO_REFACTOR: 查找E1遥测记录函数定义
const trackEvent = (eventName, properties) => {
  throw new Error('Track event function not yet refactored (original: E1)');
};

// TODO_REFACTOR: 查找ES会话恢复函数定义
const resumeSession = async (sessionId, additionalMessages) => {
  throw new Error('Session resume function not yet refactored (original: ES)');
};

// TODO_REFACTOR: 查找v1错误处理函数定义
const handleError = (error) => {
  throw new Error('Error handler function not yet refactored (original: v1)');
};

// TODO_REFACTOR: 查找hJ UUID验证函数定义
const validateUUID = (uuid) => {
  throw new Error('UUID validation function not yet refactored (original: hJ)');
};

// TODO_REFACTOR: 查找Li2输入解析函数定义
const parseInput = (input) => {
  throw new Error('Input parsing function not yet refactored (original: Li2)');
};

// TODO_REFACTOR: 查找sIA流输入处理类定义
class StreamInputHandler {
  constructor(input) {
    throw new Error('Stream input handler class not yet refactored (original: sIA)');
  }
}

// TODO_REFACTOR: 查找LK输出函数定义
const writeOutput = (text) => {
  throw new Error('Output function not yet refactored (original: LK)');
};

// TODO_REFACTOR: 查找KG最后消息获取函数定义
const getLastMessage = (messages) => {
  throw new Error('Last message function not yet refactored (original: KG)');
};

// TODO_REFACTOR: 查找rIA队列类定义
class MessageQueue {
  constructor() {
    throw new Error('Message queue class not yet refactored (original: rIA)');
  }
  
  enqueue(message) {
    throw new Error('Message queue enqueue method not yet refactored');
  }
  
  done() {
    throw new Error('Message queue done method not yet refactored');
  }
}

// TODO_REFACTOR: 查找C6B消息转换函数定义
const convertMessagesToOutput = (messages) => {
  throw new Error('Message conversion function not yet refactored (original: C6B)');
};

// TODO_REFACTOR: 查找W6B对话引擎函数定义
const { runConversation } = require('./conversation-engine');

/**
 * 处理打印模式的主函数
 * 重构自: 第353551-353670行的X6B函数
 */
async function handlePrintMode(
  input,
  permissionContext,
  mcpClients,
  commands,
  tools,
  mcpTools,
  additionalMessages,
  options
) {
  let initialMessages = [];
  
  // 处理继续模式
  if (options.continue) {
    try {
      trackEvent("tengu_continue_print", {});
      const session = await resumeSession(undefined, additionalMessages.concat(mcpTools));
      if (session) {
        initialMessages = session.messages;
      }
    } catch (error) {
      handleError(error instanceof Error ? error : new Error(String(error)));
      process.exit(1);
    }
  }
  
  // 处理传送模式 (已禁用)
  if (false) {
    if (options.teleport) {
      try {
        // 传送功能已禁用
      } catch (error) {
        // 忽略错误
      }
    }
  }
  
  // 处理恢复模式
  if (options.resume) {
    try {
      trackEvent("tengu_resume_print", {});
      const sessionId = validateUUID(options.resume);
      
      if (!sessionId) {
        process.stderr.write(`Error: --resume requires a valid session ID when used with --print\n`);
        process.stderr.write(`Usage: claude -p --resume <session-id>\n`);
        
        if (typeof options.resume === "string" && !sessionId) {
          process.stderr.write(`Session IDs must be in UUID format (e.g., 550e8400-e29b-41d4-a716-************)\n`);
          process.stderr.write(`Provided value "${options.resume}" is not a valid UUID\n`);
        }
        process.exit(1);
      }
      
      const session = await resumeSession(sessionId, additionalMessages.concat(mcpTools));
      if (!session) {
        process.stderr.write(`No conversation found with session ID: ${sessionId}\n`);
        process.exit(1);
      }
      
      initialMessages = session.messages;
    } catch (error) {
      handleError(error instanceof Error ? error : new Error(String(error)));
      process.stderr.write(`Failed to resume session with --print mode\n`);
      process.exit(1);
    }
  }
  
  // 处理输入
  let processedInput;
  if (typeof input === "string") {
    processedInput = parseInput([JSON.stringify({
      type: "user",
      session_id: "",
      message: {
        role: "user",
        content: input
      },
      parent_tool_use_id: null
    })]);
  } else {
    processedInput = input;
  }
  
  const inputHandler = new StreamInputHandler(processedInput);
  const isResuming = Boolean(validateUUID(options.resume));
  
  // 验证输入
  if (!input && !isResuming) {
    process.stderr.write(`Error: Input must be provided either through stdin or as a prompt argument when using --print\n`);
    process.exit(1);
  }
  
  // 验证流式JSON输出格式
  if (options.outputFormat === "stream-json" && !options.verbose) {
    process.stderr.write(`Error: When using --print, --output-format=stream-json requires --verbose\n`);
    process.exit(1);
  }
  
  // 准备工具列表
  const allTools = [...additionalMessages, ...mcpTools];
  let permissionPromptTool = undefined;
  
  // 处理权限提示工具
  if (options.permissionPromptToolName) {
    permissionPromptTool = mcpTools.find((tool) => tool.name === options.permissionPromptToolName);
    
    if (!permissionPromptTool) {
      process.stderr.write(`Error: MCP tool ${options.permissionPromptToolName} (passed via --permission-prompt-tool) not found. Available MCP tools: ${mcpTools.map((tool) => tool.name).join(", ") || "none"}\n`);
      process.exit(1);
    }
    
    if (!permissionPromptTool.inputJSONSchema) {
      process.stderr.write(`Error: tool ${options.permissionPromptToolName} (passed via --permission-prompt-tool) must be an MCP tool\n`);
      process.exit(1);
    }
    
    // 从工具列表中移除权限提示工具
    allTools = allTools.filter((tool) => tool.name !== options.permissionPromptToolName);
  }
  
  // 运行流式处理
  const results = [];
  for await (const event of runStreamingConversation(
    inputHandler.structuredInput,
    permissionContext,
    mcpClients,
    [...commands, ...tools],
    allTools,
    initialMessages,
    permissionPromptTool,
    options
  )) {
    if (options.outputFormat === "stream-json" && options.verbose) {
      writeOutput(JSON.stringify(event) + '\n');
    }
    
    if (event.type !== "control_response") {
      results.push(event);
    }
  }
  
  // 处理最终结果
  const lastResult = getLastMessage(results);
  if (!lastResult || lastResult.type !== "result") {
    throw new Error("No messages returned");
  }
  
  // 格式化输出
  switch (options.outputFormat) {
    case "json":
      if (options.verbose) {
        writeOutput(JSON.stringify(results) + '\n');
        break;
      }
      writeOutput(JSON.stringify(lastResult) + '\n');
      break;
      
    case "stream-json":
      // 流式输出已在上面处理
      break;
      
    default:
      switch (lastResult.subtype) {
        case "success":
          writeOutput(lastResult.result.endsWith('\n') ? lastResult.result : lastResult.result + '\n');
          break;
          
        case "error_during_execution":
          writeOutput("Execution error");
          break;
          
        case "error_max_turns":
          writeOutput(`Error: Reached max turns (${options.maxTurns})`);
          break;
      }
  }
  
  process.exit(lastResult.is_error ? 1 : 0);
}

/**
 * 运行流式对话处理
 * 重构自: 第353671-353761行的xF4函数
 */
function runStreamingConversation(
  structuredInput,
  permissionContext,
  mcpClients,
  commands,
  tools,
  initialMessages,
  permissionPromptTool,
  options
) {
  let queuedCommands = [];
  const getQueuedCommands = () => queuedCommands;
  const removeQueuedCommands = (commandsToRemove) => {
    queuedCommands = queuedCommands.filter((cmd) => !commandsToRemove.includes(cmd));
  };
  
  let isProcessing = false;
  let isDone = false;
  const messageQueue = new MessageQueue();
  const convertedMessages = convertMessagesToOutput(initialMessages);
  let abortController;
  
  const processCommands = async () => {
    isProcessing = true;
    try {
      while (queuedCommands.length > 0) {
        const command = queuedCommands.shift();
        
        if (command.mode !== "prompt") {
          throw new Error("only prompt commands are supported in streaming mode");
        }
        
        const prompt = command.value;
        abortController = new AbortController();
        
        for await (const event of runConversation({
          commands,
          prompt,
          cwd: cwd(),
          tools,
          permissionContext,
          verbose: options.verbose,
          mcpClients,
          maxTurns: options.maxTurns,
          permissionPromptTool,
          userSpecifiedModel: options.userSpecifiedModel,
          fallbackModel: options.fallbackModel,
          initialMessages: convertedMessages,
          customSystemPrompt: options.systemPrompt,
          appendSystemPrompt: options.appendSystemPrompt,
          getQueuedCommands,
          removeQueuedCommands,
          abortController
        })) {
          // 过滤掉带有parent_tool_use_id的助手和用户消息
          if (!((event.type === "assistant" || event.type === "user") && event.parent_tool_use_id)) {
            convertedMessages.push(event);
          }
          messageQueue.enqueue(event);
        }
      }
    } finally {
      isProcessing = false;
    }
    
    if (isDone) {
      messageQueue.done();
    }
  };
  
  // 返回异步生成器
  return (async function* () {
    // 处理输入流
    (async () => {
      for await (const inputEvent of structuredInput) {
        if (inputEvent.type === "control_request") {
          if (inputEvent.request.subtype === "interrupt") {
            if (abortController) {
              abortController.abort();
            }
            messageQueue.enqueue({
              type: "control_response",
              response: {
                subtype: "success",
                request_id: inputEvent.request_id
              }
            });
          }
          continue;
        }
        
        // 提取消息内容
        let content;
        if (typeof inputEvent.message.content === "string") {
          content = inputEvent.message.content;
        } else {
          if (inputEvent.message.content.length !== 1) {
            process.stderr.write(`Error: Expected message content to have exactly one item, got ${inputEvent.message.content.length}\n`);
            process.exit(1);
          }
          
          if (typeof inputEvent.message.content[0] === "string") {
            content = inputEvent.message.content[0];
          } else if (inputEvent.message.content[0].type === "text") {
            content = inputEvent.message.content[0].text;
          } else {
            process.stderr.write(`Error: Expected message content to be a string or a text block.\n`);
            process.exit(1);
          }
        }
        
        // 添加到队列
        queuedCommands.push({
          mode: "prompt",
          value: content
        });
        
        // 如果没有在处理，开始处理
        if (!isProcessing) {
          processCommands();
        }
      }
      
      isDone = true;
      if (!isProcessing) {
        messageQueue.done();
      }
    })();
    
    // 返回消息队列
    yield* messageQueue;
  })();
}

module.exports = {
  handlePrintMode,
  runStreamingConversation
};
