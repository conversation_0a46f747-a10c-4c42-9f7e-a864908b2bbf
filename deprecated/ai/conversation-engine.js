/**
 * Claude Code - AI Conversation Engine
 * 
 * 重构来源: cli-format-all.js 第353247-353507行 (260行)
 * 重构时间: 2025-01-19
 * 
 * 核心AI对话引擎，处理与Claude API的交互、工具调用和会话管理
 */

// Node.js 内置模块
const { randomUUID } = require('crypto');

// TODO_REFACTOR: 以下是未重构的依赖，需要后续替换

// TODO_REFACTOR: 查找uX工作目录设置函数定义
const setWorkingDirectory = (cwd) => {
  throw new Error('Set working directory function not yet refactored (original: uX)');
};

// TODO_REFACTOR: 查找am模型别名解析函数定义
const resolveModelAlias = (model) => {
  throw new Error('Model alias resolution function not yet refactored (original: am)');
};

// TODO_REFACTOR: 查找M7默认模型获取函数定义
const getDefaultModel = () => {
  throw new Error('Default model function not yet refactored (original: M7)');
};

// TODO_REFACTOR: 查找dp工具和文档准备函数定义
const prepareToolsAndDocs = async (tools, model, additionalDirs, mcpClients) => {
  throw new Error('Tools and docs preparation function not yet refactored (original: dp)');
};

// TODO_REFACTOR: 查找AY系统提示获取函数定义
const getSystemPrompts = async () => {
  throw new Error('System prompts function not yet refactored (original: AY)');
};

// TODO_REFACTOR: 查找iE IDE状态获取函数定义
const getIDEState = async () => {
  throw new Error('IDE state function not yet refactored (original: iE)');
};

// TODO_REFACTOR: 查找TV思考令牌计算函数定义
const calculateThinkingTokens = (messages) => {
  throw new Error('Thinking tokens calculation function not yet refactored (original: TV)');
};

// TODO_REFACTOR: 查找JA应用状态获取函数定义
const getAppState = () => {
  throw new Error('App state function not yet refactored (original: JA)');
};

// TODO_REFACTOR: 查找qB会话ID获取函数定义
const getSessionId = () => {
  throw new Error('Session ID function not yet refactored (original: qB)');
};

// TODO_REFACTOR: 查找pp提示处理函数定义
const processPrompt = async (prompt, type, callback, context, ...args) => {
  throw new Error('Prompt processing function not yet refactored (original: pp)');
};

// TODO_REFACTOR: 查找jR权限检查函数定义
const checkToolPermission = async (tool, input, context, state, toolUseId) => {
  throw new Error('Tool permission check function not yet refactored (original: jR)');
};

// TODO_REFACTOR: 查找F6B权限提示结果映射函数定义
const mapPermissionPromptResult = (result, toolName) => {
  throw new Error('Permission prompt result mapping function not yet refactored (original: F6B)');
};

// TODO_REFACTOR: 查找Z6B权限解析函数定义
const parsePermissionResult = {
  parse: (text) => {
    throw new Error('Permission result parser not yet refactored (original: Z6B)');
  }
};

// TODO_REFACTOR: 查找S4文本提取函数定义
const extractText = (content) => {
  throw new Error('Text extraction function not yet refactored (original: S4)');
};

// TODO_REFACTOR: 查找MF API密钥源获取函数定义
const getAPIKeySource = (flag) => {
  throw new Error('API key source function not yet refactored (original: MF)');
};

// TODO_REFACTOR: 查找Ji使用统计初始化函数定义
const initializeUsageStats = () => {
  throw new Error('Usage stats initialization not yet refactored (original: Ji)');
};

// TODO_REFACTOR: 查找AH主循环函数定义
const runMainLoop = async function* (messages, systemPrompts, tools, ideState, permissionCallback, context, fallbackModel, fallbackModelParam) {
  throw new Error('Main loop function not yet refactored (original: AH)');
};

// TODO_REFACTOR: 查找wX1消息保存函数定义
const saveMessages = async (messages) => {
  throw new Error('Save messages function not yet refactored (original: wX1)');
};

// TODO_REFACTOR: 查找Ax使用统计累加函数定义
const accumulateUsage = (current, delta) => {
  throw new Error('Usage accumulation function not yet refactored (original: Ax)');
};

// TODO_REFACTOR: 查找E_API持续时间获取函数定义
const getAPIDuration = () => {
  throw new Error('API duration function not yet refactored (original: E_)');
};

// TODO_REFACTOR: 查找gN总成本获取函数定义
const getTotalCost = () => {
  throw new Error('Total cost function not yet refactored (original: gN)');
};

// TODO_REFACTOR: 查找KG最后消息获取函数定义
const getLastMessage = (messages) => {
  throw new Error('Last message function not yet refactored (original: KG)');
};

// TODO_REFACTOR: 查找Z3消息转换函数定义
const convertMessages = (messages) => {
  throw new Error('Message conversion function not yet refactored (original: Z3)');
};

/**
 * 核心AI对话引擎
 * 重构自: 第353247-353438行的W6B函数
 * 
 * 这是整个Claude Code的核心函数，处理AI对话的完整流程
 */
async function* runConversation({
  commands,
  permissionContext,
  prompt,
  cwd,
  tools,
  mcpClients,
  verbose = false,
  maxTurns,
  permissionPromptTool,
  initialMessages = [],
  customSystemPrompt,
  appendSystemPrompt,
  userSpecifiedModel,
  fallbackModel,
  getQueuedCommands = () => [],
  removeQueuedCommands = () => {},
  abortController
}) {
  // 设置入口点环境变量
  if (!process.env.CLAUDE_CODE_ENTRYPOINT) {
    process.env.CLAUDE_CODE_ENTRYPOINT = "sdk-cli";
  }
  
  // 设置工作目录
  setWorkingDirectory(cwd);
  
  // 记录开始时间
  const startTime = Date.now();
  
  // 解析模型
  const model = userSpecifiedModel ? resolveModelAlias(userSpecifiedModel) : getDefaultModel();
  
  // 准备工具、系统提示和IDE状态
  const [toolsAndDocs, systemPrompts, ideState] = await Promise.all([
    prepareToolsAndDocs(tools, model, Array.from(permissionContext.additionalWorkingDirectories), mcpClients),
    getSystemPrompts(),
    getIDEState()
  ]);
  
  // 构建完整的系统提示
  const fullSystemPrompts = [
    ...(customSystemPrompt ? [customSystemPrompt] : systemPrompts),
    ...(appendSystemPrompt ? [appendSystemPrompt] : [])
  ];
  
  // 转换初始消息
  const convertedMessages = convertInitialMessages(initialMessages);
  
  // 创建会话上下文
  let sessionContext = {
    messages: convertedMessages,
    setMessages: () => {},
    onChangeAPIKey: () => {},
    options: {
      commands,
      debug: false,
      tools,
      verbose,
      mainLoopModel: model,
      maxThinkingTokens: calculateThinkingTokens(convertedMessages),
      mcpClients,
      mcpResources: {},
      ideInstallationStatus: null,
      isNonInteractiveSession: true,
      theme: getAppState().theme
    },
    getToolPermissionContext: () => permissionContext,
    getQueuedCommands: () => [],
    removeQueuedCommands: () => {},
    abortController: abortController ?? new AbortController(),
    readFileState: {},
    setInProgressToolUseIDs: () => {},
    setToolPermissionContext: () => {},
    agentId: getSessionId()
  };
  
  // 处理提示并添加到消息中
  const processedPrompt = await processPrompt(
    prompt,
    "prompt",
    () => {},
    {
      ...sessionContext,
      messages: convertedMessages
    },
    null,
    null,
    undefined,
    undefined,
    convertedMessages
  );
  
  const allMessages = [...convertedMessages, ...processedPrompt.messages];
  
  // 重新计算思考令牌
  const thinkingTokens = calculateThinkingTokens(allMessages);
  if (thinkingTokens > 0) {
    sessionContext = {
      messages: allMessages,
      setMessages: () => {},
      onChangeAPIKey: () => {},
      options: {
        commands,
        debug: false,
        tools,
        verbose,
        mainLoopModel: model,
        maxThinkingTokens: thinkingTokens,
        mcpClients,
        mcpResources: {},
        ideInstallationStatus: null,
        isNonInteractiveSession: true,
        theme: getAppState().theme
      },
      getToolPermissionContext: () => permissionContext,
      abortController: new AbortController(),
      readFileState: {},
      setToolPermissionContext: () => {},
      getQueuedCommands,
      removeQueuedCommands,
      setInProgressToolUseIDs: () => {},
      agentId: getSessionId()
    };
  }
  
  // 创建权限回调函数
  const permissionCallback = async (tool, input, context, state, toolUseId) => {
    const permissionResult = await checkToolPermission(tool, input, context, state, toolUseId);
    
    if (permissionResult.behavior === "allow" || permissionResult.behavior === "deny") {
      return permissionResult;
    }
    
    // 使用权限提示工具
    if (permissionPromptTool) {
      for await (const result of permissionPromptTool.call({
        tool_name: tool.name,
        input,
        tool_use_id: toolUseId
      }, context, permissionCallback, state)) {
        
        if (result.type !== "result") {
          continue;
        }
        
        if (abortController?.signal.aborted) {
          return {
            behavior: "deny",
            message: "Permission prompt was aborted.",
            decisionReason: {
              type: "permissionPromptTool",
              permissionPromptToolName: tool.name,
              toolResult: result
            },
            ruleSuggestions: null
          };
        }
        
        const mappedResult = permissionPromptTool.mapToolResultToToolResultBlockParam(result.data, "1");
        
        if (!mappedResult.content || 
            !Array.isArray(mappedResult.content) || 
            !mappedResult.content[0] || 
            mappedResult.content[0].type !== "text" || 
            typeof mappedResult.content[0].text !== "string") {
          throw new Error('Permission prompt tool returned an invalid result. Expected a single text block param with type="text" and a string text value.');
        }
        
        return mapPermissionPromptResult(
          parsePermissionResult.parse(extractText(mappedResult.content[0].text)),
          permissionPromptTool.name
        );
      }
    }
    
    return permissionResult;
  };
  
  // 发送初始化事件
  yield {
    type: "system",
    subtype: "init",
    cwd,
    session_id: getSessionId(),
    tools: tools.map((tool) => tool.name),
    mcp_servers: mcpClients.map((client) => ({
      name: client.name,
      status: client.type
    })),
    model,
    permissionMode: permissionContext.mode,
    apiKeySource: getAPIKeySource(true).source
  };
  
  // 初始化使用统计
  let usageStats = initializeUsageStats();
  let turnCount = 0;
  
  // 运行主对话循环
  for await (const event of runMainLoop(
    allMessages,
    fullSystemPrompts,
    toolsAndDocs,
    ideState,
    permissionCallback,
    sessionContext,
    undefined,
    fallbackModel
  )) {
    
    // 更新消息历史
    if (event.type === "assistant" || event.type === "user") {
      allMessages.push(event);
      await saveMessages(allMessages);
    }
    
    // 处理不同类型的事件
    switch (event.type) {
      case "assistant":
      case "progress":
      case "user":
        yield* formatConversationEvent(event);
        break;
        
      case "stream_event":
        if (event.event.type === "message_start") {
          usageStats = accumulateUsage(usageStats, event.event.message.usage);
        }
        if (event.event.type === "message_delta") {
          usageStats = accumulateUsage(usageStats, event.event.usage);
        }
        break;
        
      case "attachment":
      case "stream_request_start":
      case "system":
        break;
    }
    
    // 检查最大轮数限制
    if (event.type === "user" && maxTurns && ++turnCount >= maxTurns) {
      yield {
        type: "result",
        subtype: "error_max_turns",
        duration_ms: Date.now() - startTime,
        duration_api_ms: getAPIDuration(),
        is_error: false,
        num_turns: turnCount,
        session_id: getSessionId(),
        total_cost_usd: getTotalCost(),
        usage: usageStats
      };
      return;
    }
  }
  
  // 检查最后的消息
  const lastMessage = getLastMessage(allMessages);
  if (!lastMessage || lastMessage.type !== "assistant") {
    yield {
      type: "result",
      subtype: "error_during_execution",
      duration_ms: Date.now() - startTime,
      duration_api_ms: getAPIDuration(),
      is_error: false,
      num_turns: turnCount,
      session_id: getSessionId(),
      total_cost_usd: getTotalCost(),
      usage: usageStats
    };
    return;
  }
  
  // 提取最终结果
  const lastContent = getLastMessage(lastMessage.message.content);
  if (lastContent?.type !== "text" && 
      lastContent?.type !== "thinking" && 
      lastContent?.type !== "redacted_thinking") {
    throw new Error(`Expected first content item to be text or thinking, but got ${JSON.stringify(lastMessage.message.content[0], null, 2)}`);
  }
  
  // 发送成功结果
  yield {
    type: "result",
    subtype: "success",
    is_error: Boolean(lastMessage.isApiErrorMessage),
    duration_ms: Date.now() - startTime,
    duration_api_ms: getAPIDuration(),
    num_turns: allMessages.length - 1,
    result: lastContent.type === "text" ? lastContent.text : "",
    session_id: getSessionId(),
    total_cost_usd: getTotalCost(),
    usage: usageStats
  };
}

/**
 * 格式化对话事件
 * 重构自: 第353439-353484行的jF4函数
 */
function* formatConversationEvent(event) {
  switch (event.type) {
    case "assistant":
      for (const message of convertMessages([event])) {
        yield {
          type: "assistant",
          message: message.message,
          parent_tool_use_id: null,
          session_id: getSessionId()
        };
      }
      return;

    case "progress":
      if (event.data.type !== "agent_progress") {
        return;
      }

      for (const message of convertMessages([event.data.message])) {
        switch (message.type) {
          case "assistant":
            yield {
              type: "assistant",
              message: message.message,
              parent_tool_use_id: event.parentToolUseID,
              session_id: getSessionId()
            };
            break;

          case "user":
            yield {
              type: "user",
              message: message.message,
              parent_tool_use_id: event.parentToolUseID,
              session_id: getSessionId()
            };
            break;
        }
      }
      break;

    case "user":
      for (const message of convertMessages([event])) {
        yield {
          type: "user",
          message: message.message,
          parent_tool_use_id: null,
          session_id: getSessionId()
        };
      }
      return;

    default:
      // 未知事件类型，忽略
      break;
  }
}

/**
 * 转换初始消息格式
 * 重构自: 第353485-353507行的J6B函数
 */
function convertInitialMessages(messages) {
  return messages.flatMap((message) => {
    switch (message.type) {
      case "assistant":
        return [{
          type: "assistant",
          message: message.message,
          uuid: randomUUID(),
          timestamp: new Date().toISOString()
        }];

      case "user":
        return [{
          type: "user",
          message: message.message,
          uuid: randomUUID(),
          timestamp: new Date().toISOString()
        }];

      default:
        return [];
    }
  });
}

/**
 * 转换消息为输出格式
 * 重构自: 第353508-353530行的C6B函数 (在后续代码中)
 */
function convertMessagesToOutput(messages) {
  return messages.flatMap((message) => {
    switch (message.type) {
      case "assistant":
        return [{
          type: "assistant",
          message: message.message,
          session_id: getSessionId(),
          parent_tool_use_id: null
        }];

      case "user":
        return [{
          type: "user",
          message: message.message,
          session_id: getSessionId(),
          parent_tool_use_id: null
        }];

      default:
        return [];
    }
  });
}

module.exports = {
  runConversation,
  formatConversationEvent,
  convertInitialMessages,
  convertMessagesToOutput
};
