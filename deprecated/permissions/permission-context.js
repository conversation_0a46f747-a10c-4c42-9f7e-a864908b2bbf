/**
 * Claude Code - Permission Context Management
 * 
 * 重构来源: cli-format-all.js 第332428-332459行 + 相关权限函数
 * 重构时间: 2025-01-19
 * 
 * 权限上下文创建和管理，处理工具权限、目录访问权限等
 */

// Node.js 内置模块
const { resolve } = require('path');

// TODO_REFACTOR: 以下是未重构的依赖，需要后续替换

// TODO_REFACTOR: 查找hp工具解析函数定义
const parseToolList = (tools) => {
  throw new Error('Tool list parsing function not yet refactored (original: hp)');
};

// TODO_REFACTOR: 查找z9工作目录获取函数定义
const getWorkingDirectory = () => {
  throw new Error('Working directory function not yet refactored (original: z9)');
};

// TODO_REFACTOR: 查找LrA权限上下文创建函数定义
const createPermissionContext = (config, rules) => {
  throw new Error('Permission context creation function not yet refactored (original: LrA)');
};

// TODO_REFACTOR: 查找HrA权限规则获取函数定义
const getPermissionRules = () => {
  throw new Error('Permission rules function not yet refactored (original: HrA)');
};

// TODO_REFACTOR: 查找wQ配置获取函数定义
const getConfig = () => {
  throw new Error('Config function not yet refactored (original: wQ)');
};

// TODO_REFACTOR: 查找XA1目录添加函数定义
const addDirectoryToPermissions = (directory, permissionContext) => {
  throw new Error('Add directory function not yet refactored (original: XA1)');
};

// TODO_REFACTOR: 查找VA1警告格式化函数定义
const formatWarning = (validationResult) => {
  throw new Error('Warning formatting function not yet refactored (original: VA1)');
};

/**
 * 权限模式枚举
 */
const PERMISSION_MODES = {
  DEFAULT: 'default',
  STRICT: 'strict', 
  PERMISSIVE: 'permissive',
  BYPASS: 'bypassPermissions'
};

/**
 * 权限源类型
 */
const PERMISSION_SOURCES = {
  CLI_ARG: 'cliArg',
  COMMAND: 'command',
  USER_SETTINGS: 'userSettings',
  PROJECT_SETTINGS: 'projectSettings',
  LOCAL_SETTINGS: 'localSettings',
  POLICY_SETTINGS: 'policySettings'
};

/**
 * 权限行为类型
 */
const PERMISSION_BEHAVIORS = {
  ALLOW: 'allow',
  DENY: 'deny',
  ASK: 'ask'
};

/**
 * 创建工具权限上下文
 * 重构自: 第332428-332459行的ao2函数
 * 
 * @param {Object} options - 权限配置选项
 * @param {Array} options.allowedToolsCli - CLI允许的工具列表
 * @param {Array} options.disallowedToolsCli - CLI禁止的工具列表
 * @param {string} options.permissionMode - 权限模式
 * @param {Array} options.addDirs - 额外的工作目录
 * @returns {Object} 包含工具权限上下文和警告的对象
 */
function createToolPermissionContext({
  allowedToolsCli = [],
  disallowedToolsCli = [],
  permissionMode = PERMISSION_MODES.DEFAULT,
  addDirs = []
}) {
  // 解析工具列表
  const allowedTools = parseToolList(allowedToolsCli);
  const disallowedTools = parseToolList(disallowedToolsCli);
  const warnings = [];
  
  // 创建额外工作目录集合
  const additionalWorkingDirectories = new Set();
  
  // 添加PWD环境变量指定的目录
  const pwdDirectory = process.env.PWD;
  if (pwdDirectory && pwdDirectory !== getWorkingDirectory()) {
    additionalWorkingDirectories.add(pwdDirectory);
  }
  
  // 创建基础权限上下文
  let permissionContext = createPermissionContext({
    mode: permissionMode,
    additionalWorkingDirectories,
    alwaysAllowRules: {
      cliArg: allowedTools
    },
    alwaysDenyRules: {
      cliArg: disallowedTools
    },
    isBypassPermissionsModeAvailable: permissionMode === PERMISSION_MODES.BYPASS
  }, getPermissionRules());
  
  // 获取配置中的额外目录
  const config = getConfig() || {};
  const configDirectories = [
    ...(config.permissions?.additionalDirectories || []),
    ...addDirs
  ];
  
  // 添加配置中的目录到权限上下文
  for (const directory of configDirectories) {
    const result = addDirectoryToPermissions(directory, permissionContext);
    
    if (result.resultType === "success") {
      permissionContext = result.updatedPermissionContext;
    } else if (result.resultType !== "alreadyInWorkingDirectory") {
      warnings.push(formatWarning(result));
    }
  }
  
  return {
    toolPermissionContext: permissionContext,
    warnings
  };
}

/**
 * 检查权限模式是否有效
 * 
 * @param {string} mode - 权限模式
 * @returns {boolean} 是否有效
 */
function isValidPermissionMode(mode) {
  return Object.values(PERMISSION_MODES).includes(mode);
}

/**
 * 获取默认权限模式
 * 
 * @returns {string} 默认权限模式
 */
function getDefaultPermissionMode() {
  return PERMISSION_MODES.DEFAULT;
}

/**
 * 检查是否为绕过权限模式
 * 
 * @param {string} mode - 权限模式
 * @returns {boolean} 是否为绕过权限模式
 */
function isBypassPermissionMode(mode) {
  return mode === PERMISSION_MODES.BYPASS;
}

/**
 * 检查是否为严格权限模式
 * 
 * @param {string} mode - 权限模式
 * @returns {boolean} 是否为严格权限模式
 */
function isStrictPermissionMode(mode) {
  return mode === PERMISSION_MODES.STRICT;
}

/**
 * 获取权限模式描述
 * 
 * @param {string} mode - 权限模式
 * @returns {string} 权限模式描述
 */
function getPermissionModeDescription(mode) {
  switch (mode) {
    case PERMISSION_MODES.DEFAULT:
      return 'Default permission mode with balanced security';
    case PERMISSION_MODES.STRICT:
      return 'Strict permission mode with enhanced security';
    case PERMISSION_MODES.PERMISSIVE:
      return 'Permissive mode with relaxed security checks';
    case PERMISSION_MODES.BYPASS:
      return 'Bypass mode with no permission checks (use with caution)';
    default:
      return 'Unknown permission mode';
  }
}

/**
 * 验证权限上下文配置
 * 
 * @param {Object} context - 权限上下文
 * @returns {Object} 验证结果
 */
function validatePermissionContext(context) {
  const errors = [];
  const warnings = [];
  
  if (!context || typeof context !== 'object') {
    errors.push('Permission context must be an object');
    return { isValid: false, errors, warnings };
  }
  
  // 检查必需的属性
  const requiredProperties = ['mode', 'alwaysAllowRules', 'alwaysDenyRules'];
  for (const prop of requiredProperties) {
    if (!(prop in context)) {
      errors.push(`Missing required property: ${prop}`);
    }
  }
  
  // 检查权限模式
  if (context.mode && !isValidPermissionMode(context.mode)) {
    errors.push(`Invalid permission mode: ${context.mode}`);
  }
  
  // 检查规则结构
  if (context.alwaysAllowRules && typeof context.alwaysAllowRules !== 'object') {
    errors.push('alwaysAllowRules must be an object');
  }
  
  if (context.alwaysDenyRules && typeof context.alwaysDenyRules !== 'object') {
    errors.push('alwaysDenyRules must be an object');
  }
  
  // 检查额外工作目录
  if (context.additionalWorkingDirectories) {
    if (!(context.additionalWorkingDirectories instanceof Set)) {
      warnings.push('additionalWorkingDirectories should be a Set');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 合并权限上下文
 * 
 * @param {Object} baseContext - 基础权限上下文
 * @param {Object} overrideContext - 覆盖权限上下文
 * @returns {Object} 合并后的权限上下文
 */
function mergePermissionContexts(baseContext, overrideContext) {
  if (!baseContext) {
    return overrideContext;
  }
  
  if (!overrideContext) {
    return baseContext;
  }
  
  return {
    ...baseContext,
    ...overrideContext,
    alwaysAllowRules: {
      ...baseContext.alwaysAllowRules,
      ...overrideContext.alwaysAllowRules
    },
    alwaysDenyRules: {
      ...baseContext.alwaysDenyRules,
      ...overrideContext.alwaysDenyRules
    },
    additionalWorkingDirectories: new Set([
      ...(baseContext.additionalWorkingDirectories || []),
      ...(overrideContext.additionalWorkingDirectories || [])
    ])
  };
}

/**
 * 克隆权限上下文
 * 
 * @param {Object} context - 要克隆的权限上下文
 * @returns {Object} 克隆的权限上下文
 */
function clonePermissionContext(context) {
  if (!context) {
    return null;
  }
  
  return {
    ...context,
    alwaysAllowRules: {
      ...context.alwaysAllowRules
    },
    alwaysDenyRules: {
      ...context.alwaysDenyRules
    },
    additionalWorkingDirectories: new Set(context.additionalWorkingDirectories || [])
  };
}

/**
 * 获取权限上下文统计信息
 * 
 * @param {Object} context - 权限上下文
 * @returns {Object} 统计信息
 */
function getPermissionContextStats(context) {
  if (!context) {
    return {
      mode: 'unknown',
      allowRulesCount: 0,
      denyRulesCount: 0,
      additionalDirectoriesCount: 0
    };
  }
  
  const allowRulesCount = Object.values(context.alwaysAllowRules || {})
    .reduce((total, rules) => total + (Array.isArray(rules) ? rules.length : 0), 0);
  
  const denyRulesCount = Object.values(context.alwaysDenyRules || {})
    .reduce((total, rules) => total + (Array.isArray(rules) ? rules.length : 0), 0);
  
  return {
    mode: context.mode || 'unknown',
    allowRulesCount,
    denyRulesCount,
    additionalDirectoriesCount: context.additionalWorkingDirectories?.size || 0
  };
}

module.exports = {
  // 主要函数
  createToolPermissionContext,
  
  // 权限模式管理
  isValidPermissionMode,
  getDefaultPermissionMode,
  isBypassPermissionMode,
  isStrictPermissionMode,
  getPermissionModeDescription,
  
  // 权限上下文操作
  validatePermissionContext,
  mergePermissionContexts,
  clonePermissionContext,
  getPermissionContextStats,
  
  // 常量
  PERMISSION_MODES,
  PERMISSION_SOURCES,
  PERMISSION_BEHAVIORS
};
