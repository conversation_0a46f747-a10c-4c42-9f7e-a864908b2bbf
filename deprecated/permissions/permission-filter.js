/**
 * Claude Code - Permission Filter
 * 
 * 重构来源: cli-format-all.js 第294777-294817行 + 相关权限过滤函数
 * 重构时间: 2025-01-19
 * 
 * 权限规则过滤和工具访问控制
 */

// TODO_REFACTOR: 以下是未重构的依赖，需要后续替换

// TODO_REFACTOR: 查找HrA权限规则获取函数定义
const getPermissionRules = () => {
  throw new Error('Permission rules function not yet refactored (original: HrA)');
};

// TODO_REFACTOR: 查找Ql0配置获取函数定义
const getConfigValue = (key, isGlobal) => {
  throw new Error('Get config value function not yet refactored (original: Ql0)');
};

// TODO_REFACTOR: 查找z9工作目录获取函数定义
const getWorkingDirectory = () => {
  throw new Error('Working directory function not yet refactored (original: z9)');
};

// TODO_REFACTOR: 查找cA当前目录获取函数定义
const getCurrentWorkingDirectory = () => {
  throw new Error('Current working directory function not yet refactored (original: cA)');
};

// TODO_REFACTOR: 查找ic0路径解析函数定义
const resolvePath = (path, relative) => {
  throw new Error('Path resolution function not yet refactored (original: ic0)');
};

// TODO_REFACTOR: 查找FT配置合并函数定义
const mergeConfig = (base, override) => {
  throw new Error('Config merge function not yet refactored (original: FT)');
};

// TODO_REFACTOR: 查找jF配置获取函数定义
const getFullConfig = () => {
  throw new Error('Full config function not yet refactored (original: jF)');
};

// TODO_REFACTOR: 查找QW默认配置定义
const DEFAULT_CONFIG = {}; // 临时占位符

// 导入权限常量
const { PERMISSION_MODES } = require('./permission-context');

/**
 * 权限规则类型
 */
const RULE_TYPES = {
  TOOL_ALLOW: 'toolAllow',
  TOOL_DENY: 'toolDeny',
  DIRECTORY_ALLOW: 'directoryAllow',
  DIRECTORY_DENY: 'directoryDeny'
};

/**
 * 权限规则源
 */
const RULE_SOURCES = {
  CLI: 'cli',
  CONFIG: 'config',
  PROJECT: 'project',
  USER: 'user',
  POLICY: 'policy'
};

/**
 * 根据权限上下文过滤工具
 * 重构自: 第294777-294817行的Fh函数
 * 
 * @param {Object} permissionContext - 权限上下文
 * @returns {Array} 过滤后的权限规则列表
 */
function filterByPermissions(permissionContext) {
  const rules = getPermissionRules();
  const filteredRules = [];
  
  // 如果是绕过权限模式，返回空规则列表
  if (permissionContext.mode === PERMISSION_MODES.BYPASS) {
    return [];
  }
  
  // 处理拒绝规则
  if (permissionContext.alwaysDenyRules) {
    for (const [source, denyRules] of Object.entries(permissionContext.alwaysDenyRules)) {
      if (Array.isArray(denyRules)) {
        for (const rule of denyRules) {
          filteredRules.push({
            type: RULE_TYPES.TOOL_DENY,
            source: source,
            ruleValue: {
              toolName: rule.toolName,
              ruleContent: rule.ruleContent
            }
          });
        }
      }
    }
  }
  
  // 处理允许规则
  if (permissionContext.alwaysAllowRules) {
    for (const [source, allowRules] of Object.entries(permissionContext.alwaysAllowRules)) {
      if (Array.isArray(allowRules)) {
        for (const rule of allowRules) {
          filteredRules.push({
            type: RULE_TYPES.TOOL_ALLOW,
            source: source,
            ruleValue: {
              toolName: rule.toolName,
              ruleContent: rule.ruleContent
            }
          });
        }
      }
    }
  }
  
  return filteredRules;
}

/**
 * 检查项目是否已接受信任对话框
 * 重构自: 第300793-300805行的sc0函数
 * 
 * @returns {boolean} 是否已接受信任对话框
 */
function hasProjectAcceptedTrustDialog() {
  let currentDir = getCurrentWorkingDirectory();
  const config = mergeConfig(getFullConfig(), DEFAULT_CONFIG);
  
  while (true) {
    if (config.projects?.[currentDir]?.hasTrustDialogAccepted) {
      return true;
    }
    
    const parentDir = resolvePath(currentDir, "..");
    if (parentDir === currentDir) {
      break;
    }
    currentDir = parentDir;
  }
  
  return false;
}

/**
 * 检查配置键是否为数组类型
 * 重构自: 第300816-300824行的Bd函数
 * 
 * @param {string} key - 配置键
 * @param {boolean} isGlobal - 是否为全局配置
 * @returns {boolean} 是否为数组类型
 */
function isConfigArray(key, isGlobal) {
  if (isGlobal) {
    const globalConfig = getConfigValue(key, true);
    return key in globalConfig && Array.isArray(globalConfig[key]);
  } else {
    const localConfig = getConfigValue(key, false);
    return key in localConfig && Array.isArray(localConfig);
  }
}

/**
 * 检查配置键是否为对象类型
 * 重构自: 第300825-300835行的VbQ函数
 * 
 * @param {string} key - 配置键
 * @param {boolean} isGlobal - 是否为全局配置
 * @returns {boolean} 是否为对象类型
 */
function isConfigObject(key, isGlobal) {
  if (isConfigArray(key, isGlobal)) {
    return false;
  }
  
  if (isGlobal) {
    const globalConfig = getConfigValue(key, true);
    return key in globalConfig && typeof globalConfig[key] === "object";
  } else {
    const localConfig = getConfigValue(key, false);
    return key in localConfig && typeof localConfig === "object";
  }
}

/**
 * 获取配置示例值
 * 重构自: 第300836-300844行的KbQ函数
 * 
 * @param {string} key - 配置键
 * @param {Array} values - 值数组
 * @returns {Array} 示例值数组
 */
function getConfigExamples(key, values) {
  const uniqueValues = Array.from(new Set(values));
  
  switch (key) {
    case "allowedTools":
      return uniqueValues.length > 0 ? uniqueValues : ["git diff:*"];
    case "ignorePatterns":
      return uniqueValues.length > 0 
        ? uniqueValues.map(value => `Read(${value})`) 
        : ["Read(secrets.env)"];
    default:
      return uniqueValues;
  }
}

/**
 * 创建权限规则
 * 
 * @param {string} type - 规则类型
 * @param {string} source - 规则源
 * @param {Object} ruleValue - 规则值
 * @returns {Object} 权限规则对象
 */
function createPermissionRule(type, source, ruleValue) {
  return {
    type,
    source,
    ruleValue,
    createdAt: new Date().toISOString()
  };
}

/**
 * 验证权限规则
 * 
 * @param {Object} rule - 权限规则
 * @returns {Object} 验证结果
 */
function validatePermissionRule(rule) {
  const errors = [];
  
  if (!rule || typeof rule !== 'object') {
    errors.push('Permission rule must be an object');
    return { isValid: false, errors };
  }
  
  // 检查必需的属性
  const requiredProperties = ['type', 'source', 'ruleValue'];
  for (const prop of requiredProperties) {
    if (!(prop in rule)) {
      errors.push(`Missing required property: ${prop}`);
    }
  }
  
  // 检查规则类型
  if (rule.type && !Object.values(RULE_TYPES).includes(rule.type)) {
    errors.push(`Invalid rule type: ${rule.type}`);
  }
  
  // 检查规则源
  if (rule.source && !Object.values(RULE_SOURCES).includes(rule.source)) {
    errors.push(`Invalid rule source: ${rule.source}`);
  }
  
  // 检查规则值
  if (rule.ruleValue && typeof rule.ruleValue !== 'object') {
    errors.push('Rule value must be an object');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 合并权限规则列表
 * 
 * @param {Array} baseLists - 基础规则列表数组
 * @returns {Array} 合并后的规则列表
 */
function mergePermissionRules(...baseLists) {
  const mergedRules = [];
  const ruleMap = new Map();
  
  for (const ruleList of baseLists) {
    if (!Array.isArray(ruleList)) {
      continue;
    }
    
    for (const rule of ruleList) {
      const validation = validatePermissionRule(rule);
      if (!validation.isValid) {
        continue;
      }
      
      // 创建规则的唯一键
      const ruleKey = `${rule.type}:${rule.source}:${JSON.stringify(rule.ruleValue)}`;
      
      // 避免重复规则
      if (!ruleMap.has(ruleKey)) {
        ruleMap.set(ruleKey, rule);
        mergedRules.push(rule);
      }
    }
  }
  
  return mergedRules;
}

/**
 * 按类型过滤权限规则
 * 
 * @param {Array} rules - 权限规则列表
 * @param {string} type - 规则类型
 * @returns {Array} 过滤后的规则列表
 */
function filterRulesByType(rules, type) {
  return rules.filter(rule => rule.type === type);
}

/**
 * 按源过滤权限规则
 * 
 * @param {Array} rules - 权限规则列表
 * @param {string} source - 规则源
 * @returns {Array} 过滤后的规则列表
 */
function filterRulesBySource(rules, source) {
  return rules.filter(rule => rule.source === source);
}

/**
 * 获取权限规则统计信息
 * 
 * @param {Array} rules - 权限规则列表
 * @returns {Object} 统计信息
 */
function getPermissionRuleStats(rules) {
  const stats = {
    total: rules.length,
    byType: {},
    bySource: {}
  };
  
  for (const rule of rules) {
    // 按类型统计
    if (rule.type) {
      stats.byType[rule.type] = (stats.byType[rule.type] || 0) + 1;
    }
    
    // 按源统计
    if (rule.source) {
      stats.bySource[rule.source] = (stats.bySource[rule.source] || 0) + 1;
    }
  }
  
  return stats;
}

module.exports = {
  // 主要函数
  filterByPermissions,
  hasProjectAcceptedTrustDialog,
  
  // 配置检查
  isConfigArray,
  isConfigObject,
  getConfigExamples,
  
  // 规则管理
  createPermissionRule,
  validatePermissionRule,
  mergePermissionRules,
  filterRulesByType,
  filterRulesBySource,
  getPermissionRuleStats,
  
  // 常量
  RULE_TYPES,
  RULE_SOURCES
};
