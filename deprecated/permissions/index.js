/**
 * Claude Code - Permission Management System
 * 
 * 重构来源: cli-format-all.js 权限管理相关函数集合
 * 重构时间: 2025-01-19
 * 
 * 权限管理系统的主入口，整合权限上下文、检查器和过滤器
 */

// 导入权限模块
const {
  createToolPermissionContext,
  isValidPermissionMode,
  getDefaultPermissionMode,
  isBypassPermissionMode,
  isStrictPermissionMode,
  getPermissionModeDescription,
  validatePermissionContext,
  mergePermissionContexts,
  clonePermissionContext,
  getPermissionContextStats,
  PERMISSION_MODES,
  PERMISSION_SOURCES,
  PERMISSION_BEHAVIORS
} = require('./permission-context');

const {
  checkToolPermission,
  checkMultipleToolPermissions,
  isToolExplicitlyAllowed,
  isToolExplicitlyDenied,
  getToolPermissionStatus,
  validatePermissionResult,
  createPermissionResult,
  createAllow<PERSON>esult,
  createDenyResult,
  createAskResult,
  getPermissionCheckStats,
  PERMISSION_RESULT_TYPES,
  DECISION_REASON_TYPES,
  InterruptError
} = require('./permission-checker');

const {
  filterByPermissions,
  hasProjectAcceptedTrustDialog,
  isConfigArray,
  isConfigObject,
  getConfigExamples,
  createPermissionRule,
  validatePermissionRule,
  mergePermissionRules,
  filterRulesByType,
  filterRulesBySource,
  getPermissionRuleStats,
  RULE_TYPES,
  RULE_SOURCES
} = require('./permission-filter');

/**
 * 权限管理器类
 * 提供完整的权限管理功能
 */
class PermissionManager {
  constructor(options = {}) {
    this.defaultMode = options.defaultMode || getDefaultPermissionMode();
    this.context = null;
    this.rules = [];
  }
  
  /**
   * 初始化权限管理器
   * 
   * @param {Object} options - 初始化选项
   * @returns {Promise<void>}
   */
  async initialize(options = {}) {
    const contextResult = createToolPermissionContext({
      allowedToolsCli: options.allowedTools || [],
      disallowedToolsCli: options.disallowedTools || [],
      permissionMode: options.permissionMode || this.defaultMode,
      addDirs: options.additionalDirectories || []
    });
    
    this.context = contextResult.toolPermissionContext;
    this.warnings = contextResult.warnings;
    
    // 获取过滤后的规则
    this.rules = filterByPermissions(this.context);
  }
  
  /**
   * 检查工具权限
   * 
   * @param {Object} tool - 工具对象
   * @param {Object} input - 工具输入
   * @param {Object} context - 执行上下文
   * @returns {Promise<Object>} 权限检查结果
   */
  async checkPermission(tool, input, context) {
    if (!this.context) {
      throw new Error('Permission manager not initialized');
    }
    
    return await checkToolPermission(tool, input, {
      ...context,
      getToolPermissionContext: () => this.context
    });
  }
  
  /**
   * 批量检查工具权限
   * 
   * @param {Array} tools - 工具列表
   * @param {Object} context - 执行上下文
   * @returns {Promise<Object>} 批量权限检查结果
   */
  async checkMultiplePermissions(tools, context) {
    if (!this.context) {
      throw new Error('Permission manager not initialized');
    }
    
    return await checkMultipleToolPermissions(tools, {
      ...context,
      getToolPermissionContext: () => this.context
    });
  }
  
  /**
   * 获取工具权限状态
   * 
   * @param {Object} tool - 工具对象
   * @returns {string} 权限状态
   */
  getToolStatus(tool) {
    if (!this.context) {
      return 'unknown';
    }
    
    return getToolPermissionStatus(tool, this.context);
  }
  
  /**
   * 添加权限规则
   * 
   * @param {Object} rule - 权限规则
   */
  addRule(rule) {
    const validation = validatePermissionRule(rule);
    if (!validation.isValid) {
      throw new Error(`Invalid permission rule: ${validation.errors.join(', ')}`);
    }
    
    this.rules.push(rule);
  }
  
  /**
   * 移除权限规则
   * 
   * @param {Function} predicate - 过滤函数
   * @returns {number} 移除的规则数量
   */
  removeRules(predicate) {
    const initialLength = this.rules.length;
    this.rules = this.rules.filter(rule => !predicate(rule));
    return initialLength - this.rules.length;
  }
  
  /**
   * 获取权限统计信息
   * 
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      context: this.context ? getPermissionContextStats(this.context) : null,
      rules: getPermissionRuleStats(this.rules),
      warnings: this.warnings ? this.warnings.length : 0
    };
  }
  
  /**
   * 重置权限管理器
   */
  reset() {
    this.context = null;
    this.rules = [];
    this.warnings = [];
  }
  
  /**
   * 克隆权限管理器
   * 
   * @returns {PermissionManager} 克隆的权限管理器
   */
  clone() {
    const cloned = new PermissionManager({ defaultMode: this.defaultMode });
    cloned.context = this.context ? clonePermissionContext(this.context) : null;
    cloned.rules = [...this.rules];
    cloned.warnings = [...(this.warnings || [])];
    return cloned;
  }
}

/**
 * 创建权限管理器实例
 * 
 * @param {Object} options - 创建选项
 * @returns {PermissionManager} 权限管理器实例
 */
function createPermissionManager(options = {}) {
  return new PermissionManager(options);
}

/**
 * 快速权限检查函数
 * 
 * @param {Object} tool - 工具对象
 * @param {Object} input - 工具输入
 * @param {Object} options - 检查选项
 * @returns {Promise<Object>} 权限检查结果
 */
async function quickPermissionCheck(tool, input, options = {}) {
  const manager = createPermissionManager(options);
  await manager.initialize(options);
  
  const context = {
    abortController: options.abortController || new AbortController(),
    getToolPermissionContext: () => manager.context
  };
  
  return await manager.checkPermission(tool, input, context);
}

/**
 * 权限工具函数集合
 */
const PermissionUtils = {
  // 模式相关
  isValidMode: isValidPermissionMode,
  getDefaultMode: getDefaultPermissionMode,
  isBypassMode: isBypassPermissionMode,
  isStrictMode: isStrictPermissionMode,
  getModeDescription: getPermissionModeDescription,
  
  // 上下文相关
  validateContext: validatePermissionContext,
  mergeContexts: mergePermissionContexts,
  cloneContext: clonePermissionContext,
  getContextStats: getPermissionContextStats,
  
  // 结果相关
  validateResult: validatePermissionResult,
  createResult: createPermissionResult,
  createAllowResult,
  createDenyResult,
  createAskResult,
  getCheckStats: getPermissionCheckStats,
  
  // 规则相关
  createRule: createPermissionRule,
  validateRule: validatePermissionRule,
  mergeRules: mergePermissionRules,
  filterRulesByType,
  filterRulesBySource,
  getRuleStats: getPermissionRuleStats,
  
  // 配置相关
  isConfigArray,
  isConfigObject,
  getConfigExamples,
  hasProjectTrust: hasProjectAcceptedTrustDialog
};

module.exports = {
  // 主要类和函数
  PermissionManager,
  createPermissionManager,
  quickPermissionCheck,
  
  // 工具函数
  PermissionUtils,
  
  // 核心函数
  createToolPermissionContext,
  checkToolPermission,
  checkMultipleToolPermissions,
  filterByPermissions,
  
  // 状态检查
  isToolExplicitlyAllowed,
  isToolExplicitlyDenied,
  getToolPermissionStatus,
  
  // 常量
  PERMISSION_MODES,
  PERMISSION_SOURCES,
  PERMISSION_BEHAVIORS,
  PERMISSION_RESULT_TYPES,
  DECISION_REASON_TYPES,
  RULE_TYPES,
  RULE_SOURCES,
  
  // 错误类
  InterruptError
};
