/**
 * Claude Code - Permission Checker
 * 
 * 重构来源: cli-format-all.js 第294818-294871行 + 相关权限检查函数
 * 重构时间: 2025-01-19
 * 
 * 工具权限检查和验证系统
 */

// TODO_REFACTOR: 以下是未重构的依赖，需要后续替换

// TODO_REFACTOR: 查找i3中断错误类定义
class InterruptError extends Error {
  constructor() {
    super('Operation was interrupted');
    this.name = 'InterruptError';
  }
}

// TODO_REFACTOR: 查找YN9拒绝规则查找函数定义
const findDenyRule = (permissionContext, tool) => {
  throw new Error('Find deny rule function not yet refactored (original: YN9)');
};

// TODO_REFACTOR: 查找FN9允许规则查找函数定义
const findAllowRule = (permissionContext, tool) => {
  throw new Error('Find allow rule function not yet refactored (original: FN9)');
};

// TODO_REFACTOR: 查找v1错误处理函数定义
const handleError = (error) => {
  throw new Error('Error handler function not yet refactored (original: v1)');
};

// 导入权限常量
const { PERMISSION_BEHAVIORS, PERMISSION_MODES } = require('./permission-context');

/**
 * 权限检查结果类型
 */
const PERMISSION_RESULT_TYPES = {
  ALLOW: 'allow',
  DENY: 'deny',
  ASK: 'ask'
};

/**
 * 权限决策原因类型
 */
const DECISION_REASON_TYPES = {
  RULE: 'rule',
  MODE: 'mode',
  TOOL_CHECK: 'toolCheck',
  ERROR: 'error'
};

/**
 * 检查工具权限
 * 重构自: 第294818-294871行的jR函数
 * 
 * @param {Object} tool - 工具对象
 * @param {Object} input - 工具输入参数
 * @param {Object} context - 执行上下文
 * @returns {Promise<Object>} 权限检查结果
 */
async function checkToolPermission(tool, input, context) {
  // 检查是否已中断
  if (context.abortController.signal.aborted) {
    throw new InterruptError();
  }
  
  const permissionContext = context.getToolPermissionContext();
  
  // 检查拒绝规则
  const denyRule = findDenyRule(permissionContext, tool);
  if (denyRule) {
    return {
      behavior: PERMISSION_RESULT_TYPES.DENY,
      decisionReason: {
        type: DECISION_REASON_TYPES.RULE,
        rule: denyRule
      },
      ruleSuggestions: null,
      message: `Permission to use ${tool.name} has been denied.`
    };
  }
  
  // 调用工具的权限检查方法
  let toolPermissionResult;
  try {
    const parsedInput = tool.inputSchema.parse(input);
    toolPermissionResult = await tool.checkPermissions(parsedInput, context);
  } catch (error) {
    handleError(error);
    return {
      behavior: PERMISSION_RESULT_TYPES.ASK,
      message: "Error checking permissions"
    };
  }
  
  // 如果工具明确拒绝，返回拒绝结果
  if (toolPermissionResult?.behavior === PERMISSION_RESULT_TYPES.DENY) {
    return toolPermissionResult;
  }
  
  // 检查是否为绕过权限模式
  if (permissionContext.mode === PERMISSION_MODES.BYPASS) {
    return {
      behavior: PERMISSION_RESULT_TYPES.ALLOW,
      updatedInput: input,
      decisionReason: {
        type: DECISION_REASON_TYPES.MODE,
        mode: permissionContext.mode
      }
    };
  }
  
  // 检查允许规则
  const allowRule = findAllowRule(permissionContext, tool);
  if (allowRule) {
    return {
      behavior: PERMISSION_RESULT_TYPES.ALLOW,
      updatedInput: input,
      decisionReason: {
        type: DECISION_REASON_TYPES.RULE,
        rule: allowRule
      }
    };
  }
  
  // 如果工具允许，返回允许结果
  if (toolPermissionResult.behavior === PERMISSION_RESULT_TYPES.ALLOW) {
    return toolPermissionResult;
  }
  
  // 默认情况下需要询问用户
  return {
    ...toolPermissionResult,
    behavior: PERMISSION_RESULT_TYPES.ASK,
    message: `Claude requested permissions to use ${tool.name}, but you haven't granted it yet.`
  };
}

/**
 * 批量检查工具权限
 * 
 * @param {Array} tools - 工具列表
 * @param {Object} context - 执行上下文
 * @returns {Promise<Object>} 批量权限检查结果
 */
async function checkMultipleToolPermissions(tools, context) {
  const results = {};
  const errors = [];
  
  for (const tool of tools) {
    try {
      const result = await checkToolPermission(tool, {}, context);
      results[tool.name] = result;
    } catch (error) {
      errors.push({
        tool: tool.name,
        error: error.message
      });
    }
  }
  
  return {
    results,
    errors,
    hasErrors: errors.length > 0
  };
}

/**
 * 检查工具是否被明确允许
 * 
 * @param {Object} tool - 工具对象
 * @param {Object} permissionContext - 权限上下文
 * @returns {boolean} 是否被明确允许
 */
function isToolExplicitlyAllowed(tool, permissionContext) {
  const allowRule = findAllowRule(permissionContext, tool);
  return allowRule !== null;
}

/**
 * 检查工具是否被明确拒绝
 * 
 * @param {Object} tool - 工具对象
 * @param {Object} permissionContext - 权限上下文
 * @returns {boolean} 是否被明确拒绝
 */
function isToolExplicitlyDenied(tool, permissionContext) {
  const denyRule = findDenyRule(permissionContext, tool);
  return denyRule !== null;
}

/**
 * 获取工具权限状态
 * 
 * @param {Object} tool - 工具对象
 * @param {Object} permissionContext - 权限上下文
 * @returns {string} 权限状态
 */
function getToolPermissionStatus(tool, permissionContext) {
  if (isToolExplicitlyDenied(tool, permissionContext)) {
    return 'denied';
  }
  
  if (isToolExplicitlyAllowed(tool, permissionContext)) {
    return 'allowed';
  }
  
  if (permissionContext.mode === PERMISSION_MODES.BYPASS) {
    return 'bypassed';
  }
  
  return 'unknown';
}

/**
 * 验证权限检查结果
 * 
 * @param {Object} result - 权限检查结果
 * @returns {Object} 验证结果
 */
function validatePermissionResult(result) {
  const errors = [];
  
  if (!result || typeof result !== 'object') {
    errors.push('Permission result must be an object');
    return { isValid: false, errors };
  }
  
  // 检查必需的属性
  if (!result.behavior) {
    errors.push('Permission result must have a behavior property');
  } else if (!Object.values(PERMISSION_RESULT_TYPES).includes(result.behavior)) {
    errors.push(`Invalid permission behavior: ${result.behavior}`);
  }
  
  // 检查消息
  if (result.behavior === PERMISSION_RESULT_TYPES.DENY && !result.message) {
    errors.push('Deny permission result should have a message');
  }
  
  // 检查决策原因
  if (result.decisionReason) {
    if (!result.decisionReason.type) {
      errors.push('Decision reason must have a type');
    } else if (!Object.values(DECISION_REASON_TYPES).includes(result.decisionReason.type)) {
      errors.push(`Invalid decision reason type: ${result.decisionReason.type}`);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 创建权限检查结果
 * 
 * @param {string} behavior - 权限行为
 * @param {Object} options - 其他选项
 * @returns {Object} 权限检查结果
 */
function createPermissionResult(behavior, options = {}) {
  const result = {
    behavior,
    ...options
  };
  
  const validation = validatePermissionResult(result);
  if (!validation.isValid) {
    throw new Error(`Invalid permission result: ${validation.errors.join(', ')}`);
  }
  
  return result;
}

/**
 * 创建允许权限结果
 * 
 * @param {Object} input - 更新后的输入
 * @param {Object} decisionReason - 决策原因
 * @returns {Object} 允许权限结果
 */
function createAllowResult(input, decisionReason) {
  return createPermissionResult(PERMISSION_RESULT_TYPES.ALLOW, {
    updatedInput: input,
    decisionReason
  });
}

/**
 * 创建拒绝权限结果
 * 
 * @param {string} message - 拒绝消息
 * @param {Object} decisionReason - 决策原因
 * @returns {Object} 拒绝权限结果
 */
function createDenyResult(message, decisionReason) {
  return createPermissionResult(PERMISSION_RESULT_TYPES.DENY, {
    message,
    decisionReason,
    ruleSuggestions: null
  });
}

/**
 * 创建询问权限结果
 * 
 * @param {string} message - 询问消息
 * @param {Object} options - 其他选项
 * @returns {Object} 询问权限结果
 */
function createAskResult(message, options = {}) {
  return createPermissionResult(PERMISSION_RESULT_TYPES.ASK, {
    message,
    ...options
  });
}

/**
 * 获取权限检查统计信息
 * 
 * @param {Array} results - 权限检查结果列表
 * @returns {Object} 统计信息
 */
function getPermissionCheckStats(results) {
  const stats = {
    total: results.length,
    allowed: 0,
    denied: 0,
    asked: 0,
    errors: 0
  };
  
  for (const result of results) {
    if (result.behavior === PERMISSION_RESULT_TYPES.ALLOW) {
      stats.allowed++;
    } else if (result.behavior === PERMISSION_RESULT_TYPES.DENY) {
      stats.denied++;
    } else if (result.behavior === PERMISSION_RESULT_TYPES.ASK) {
      stats.asked++;
    } else {
      stats.errors++;
    }
  }
  
  return stats;
}

module.exports = {
  // 主要函数
  checkToolPermission,
  checkMultipleToolPermissions,
  
  // 权限状态检查
  isToolExplicitlyAllowed,
  isToolExplicitlyDenied,
  getToolPermissionStatus,
  
  // 结果验证和创建
  validatePermissionResult,
  createPermissionResult,
  createAllowResult,
  createDenyResult,
  createAskResult,
  
  // 统计信息
  getPermissionCheckStats,
  
  // 常量
  PERMISSION_RESULT_TYPES,
  DECISION_REASON_TYPES,
  
  // 错误类
  InterruptError
};
