/**
 * Claude Code - Tool Registry
 * 
 * 重构来源: cli-format-all.js 第339366-339376行 + 工具定义区域
 * 重构时间: 2025-01-19
 * 
 * 工具注册表，管理所有内置工具的注册和过滤
 */

// 导入已重构的工具
const { taskTool } = require('./task-tool');
const { bashTool } = require('./bash-tool');

// TODO_REFACTOR: 以下是未重构的工具，需要后续替换

// TODO_REFACTOR: 查找PV工具定义
const editTool = {
  name: 'Edit',
  // TODO_REFACTOR: 完整的Edit工具实现
  isEnabled: () => true
};

// TODO_REFACTOR: 查找hf工具定义
const createTool = {
  name: 'Create',
  // TODO_REFACTOR: 完整的Create工具实现
  isEnabled: () => true
};

// TODO_REFACTOR: 查找_w工具定义
const viewTool = {
  name: 'View',
  // TODO_REFACTOR: 完整的View工具实现
  isEnabled: () => true
};

// TODO_REFACTOR: 查找kw工具定义
const listTool = {
  name: 'LS',
  // TODO_REFACTOR: 完整的LS工具实现
  isEnabled: () => true
};

// TODO_REFACTOR: 查找E8工具定义
const searchTool = {
  name: 'Search',
  // TODO_REFACTOR: 完整的Search工具实现
  isEnabled: () => true
};

// TODO_REFACTOR: 查找mD工具定义
const browserTool = {
  name: 'Browser',
  // TODO_REFACTOR: 完整的Browser工具实现
  isEnabled: () => true
};

// TODO_REFACTOR: 查找FL工具定义
const computerTool = {
  name: 'Computer',
  // TODO_REFACTOR: 完整的Computer工具实现
  isEnabled: () => true
};

// TODO_REFACTOR: 查找YC工具定义
const todoTool = {
  name: 'Todo',
  // TODO_REFACTOR: 完整的Todo工具实现
  isEnabled: () => true
};

// TODO_REFACTOR: 查找d21工具定义
const readTool = {
  name: 'Read',
  // TODO_REFACTOR: 完整的Read工具实现
  isEnabled: () => true
};

// TODO_REFACTOR: 查找GS工具定义
const gitTool = {
  name: 'Git',
  // TODO_REFACTOR: 完整的Git工具实现
  isEnabled: () => true
};

// TODO_REFACTOR: 查找bW工具定义
const ideTool = {
  name: 'IDE',
  // TODO_REFACTOR: 完整的IDE工具实现
  isEnabled: () => true
};

// TODO_REFACTOR: 查找kG工具定义
const debugTool = {
  name: 'Debug',
  // TODO_REFACTOR: 完整的Debug工具实现
  isEnabled: () => true
};

// TODO_REFACTOR: 查找e0B工具定义
const exitPlanModeTool = {
  name: 'ExitPlanMode',
  // TODO_REFACTOR: 完整的ExitPlanMode工具实现
  isEnabled: () => true
};

// TODO_REFACTOR: 查找Fh权限过滤函数定义
const filterByPermissions = (permissionContext) => {
  throw new Error('Permission filtering function not yet refactored (original: Fh)');
};

/**
 * 所有内置工具的注册表
 * 重构自: 第339367行的工具数组
 */
const ALL_TOOLS = [
  taskTool,        // Me2 - Task工具
  bashTool,        // l9 - Bash工具
  editTool,        // PV - Edit工具
  createTool,      // hf - Create工具
  viewTool,        // _w - View工具
  listTool,        // kw - LS工具
  searchTool,      // E8 - Search工具
  browserTool,     // mD - Browser工具
  computerTool,    // FL - Computer工具
  todoTool,        // YC - Todo工具
  readTool,        // d21 - Read工具 (条件性包含)
  gitTool,         // GS - Git工具
  ideTool,         // bW - IDE工具
  debugTool,       // kG - Debug工具 (条件性包含)
  exitPlanModeTool // e0B - ExitPlanMode工具
];

/**
 * 获取可用工具列表
 * 重构自: 第339366-339376行的DH函数
 * 
 * @param {Object} permissionContext - 权限上下文
 * @param {boolean} includeDebugTools - 是否包含调试工具
 * @returns {Array} 过滤后的工具列表
 */
function getAvailableTools(permissionContext, includeDebugTools = false) {
  // 基础工具列表
  let tools = [
    taskTool,     // Me2
    bashTool,     // l9
    editTool,     // PV
    createTool,   // hf
    viewTool,     // _w
    listTool,     // kw
    searchTool,   // E8
    browserTool,  // mD
    computerTool, // FL
    todoTool,     // YC
    gitTool,      // GS
    ideTool,      // bW
    exitPlanModeTool // e0B
  ];
  
  // 条件性添加Read工具
  if (!process.env.CLAUDE_CODE_ENABLE_UNIFIED_READ_TOOL) {
    tools.push(readTool); // d21
  }
  
  // 条件性添加Debug工具
  if (includeDebugTools) {
    tools.push(debugTool); // kG
  }
  
  // 根据权限过滤工具
  const permissionFilteredTools = filterByPermissions(permissionContext);
  const filteredTools = tools.filter((tool) => {
    return !permissionFilteredTools.some((rule) => 
      rule.ruleValue.toolName === tool.name && 
      rule.ruleValue.ruleContent === undefined
    );
  });
  
  // 检查工具是否启用
  const enabledFlags = filteredTools.map((tool) => tool.isEnabled());
  const enabledTools = filteredTools.filter((tool, index) => enabledFlags[index]);
  
  return enabledTools;
}

/**
 * 根据名称查找工具
 * 
 * @param {string} toolName - 工具名称
 * @returns {Object|null} 找到的工具对象，如果未找到则返回null
 */
function findToolByName(toolName) {
  return ALL_TOOLS.find((tool) => tool.name === toolName) || null;
}

/**
 * 获取所有工具名称
 * 
 * @returns {Array<string>} 所有工具的名称数组
 */
function getAllToolNames() {
  return ALL_TOOLS.map((tool) => tool.name);
}

/**
 * 检查工具是否存在
 * 
 * @param {string} toolName - 工具名称
 * @returns {boolean} 工具是否存在
 */
function isToolAvailable(toolName) {
  return ALL_TOOLS.some((tool) => tool.name === toolName);
}

/**
 * 获取启用的工具列表
 * 
 * @param {Array} tools - 工具列表
 * @returns {Array} 启用的工具列表
 */
function getEnabledTools(tools = ALL_TOOLS) {
  return tools.filter((tool) => tool.isEnabled());
}

/**
 * 根据类别获取工具
 * 
 * @param {string} category - 工具类别
 * @returns {Array} 该类别的工具列表
 */
function getToolsByCategory(category) {
  switch (category) {
    case 'file':
      return [editTool, createTool, viewTool, listTool, readTool];
    case 'search':
      return [searchTool];
    case 'execution':
      return [bashTool, taskTool];
    case 'development':
      return [gitTool, ideTool, debugTool];
    case 'interaction':
      return [browserTool, computerTool];
    case 'planning':
      return [todoTool, exitPlanModeTool];
    default:
      return [];
  }
}

/**
 * 验证工具配置
 * 
 * @param {Object} tool - 工具对象
 * @returns {boolean} 工具配置是否有效
 */
function validateTool(tool) {
  if (!tool || typeof tool !== 'object') {
    return false;
  }
  
  // 检查必需的属性
  const requiredProperties = ['name', 'isEnabled'];
  for (const prop of requiredProperties) {
    if (!(prop in tool)) {
      return false;
    }
  }
  
  // 检查名称是否为字符串
  if (typeof tool.name !== 'string' || tool.name.length === 0) {
    return false;
  }
  
  // 检查isEnabled是否为函数
  if (typeof tool.isEnabled !== 'function') {
    return false;
  }
  
  return true;
}

/**
 * 注册新工具
 * 
 * @param {Object} tool - 要注册的工具对象
 * @throws {Error} 如果工具配置无效或名称已存在
 */
function registerTool(tool) {
  if (!validateTool(tool)) {
    throw new Error(`Invalid tool configuration: ${JSON.stringify(tool)}`);
  }
  
  if (isToolAvailable(tool.name)) {
    throw new Error(`Tool with name "${tool.name}" already exists`);
  }
  
  ALL_TOOLS.push(tool);
}

/**
 * 注销工具
 * 
 * @param {string} toolName - 要注销的工具名称
 * @returns {boolean} 是否成功注销
 */
function unregisterTool(toolName) {
  const index = ALL_TOOLS.findIndex((tool) => tool.name === toolName);
  if (index !== -1) {
    ALL_TOOLS.splice(index, 1);
    return true;
  }
  return false;
}

/**
 * 获取工具统计信息
 * 
 * @returns {Object} 工具统计信息
 */
function getToolStats() {
  const totalTools = ALL_TOOLS.length;
  const enabledTools = getEnabledTools().length;
  const categories = {
    file: getToolsByCategory('file').length,
    search: getToolsByCategory('search').length,
    execution: getToolsByCategory('execution').length,
    development: getToolsByCategory('development').length,
    interaction: getToolsByCategory('interaction').length,
    planning: getToolsByCategory('planning').length
  };
  
  return {
    total: totalTools,
    enabled: enabledTools,
    disabled: totalTools - enabledTools,
    categories
  };
}

module.exports = {
  // 主要函数
  getAvailableTools,
  findToolByName,
  getAllToolNames,
  isToolAvailable,
  getEnabledTools,
  getToolsByCategory,
  
  // 工具管理
  validateTool,
  registerTool,
  unregisterTool,
  
  // 统计信息
  getToolStats,
  
  // 工具常量
  ALL_TOOLS,
  
  // 已重构的工具
  taskTool,
  bashTool,
  
  // 待重构的工具占位符
  editTool,
  createTool,
  viewTool,
  listTool,
  searchTool,
  browserTool,
  computerTool,
  todoTool,
  readTool,
  gitTool,
  ideTool,
  debugTool,
  exitPlanModeTool
};
