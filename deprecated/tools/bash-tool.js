/**
 * Claude Code - Bash Tool
 * 
 * 重构来源: cli-format-all.js 第331313-331550行 (237行)
 * 重构时间: 2025-01-19
 * 
 * Bash命令执行工具，支持沙盒模式和权限检查
 */

// Node.js 内置模块
const { randomUUID } = require('crypto');

// 第三方依赖
const React = require('react');

// TODO_REFACTOR: 以下是未重构的依赖，需要后续替换

// TODO_REFACTOR: 查找eU工具名称常量定义
const BASH_TOOL_NAME = 'Bash'; // 临时占位符

// TODO_REFACTOR: 查找tX2提示函数定义
const getBashPrompt = async () => {
  throw new Error('Bash prompt function not yet refactored (original: tX2)');
};

// TODO_REFACTOR: 查找xf命令解析函数定义
const parseCommand = (command) => {
  throw new Error('Command parsing function not yet refactored (original: xf)');
};

// TODO_REFACTOR: 查找iQ4危险命令检查函数定义
const isDangerousCommand = (command) => {
  throw new Error('Dangerous command check function not yet refactored (original: iQ4)');
};

// TODO_REFACTOR: 查找lQ4只读命令模式定义
const READ_ONLY_PATTERNS = []; // 临时占位符

// TODO_REFACTOR: 查找LX1沙盒检查函数定义
const isSandboxEnabled = () => {
  throw new Error('Sandbox check function not yet refactored (original: LX1)');
};

// TODO_REFACTOR: 查找dQ4沙盒输入模式定义
const sandboxInputSchema = {}; // 临时占位符

// TODO_REFACTOR: 查找jo2普通输入模式定义
const normalInputSchema = {}; // 临时占位符

// TODO_REFACTOR: 查找u3A权限检查函数定义
const checkBashPermissions = async (input, context) => {
  throw new Error('Bash permissions check function not yet refactored (original: u3A)');
};

// TODO_REFACTOR: 查找b3A输入验证函数定义
const validateBashInput = (input, cwd, workingDir, permissionContext) => {
  throw new Error('Bash input validation function not yet refactored (original: b3A)');
};

// TODO_REFACTOR: 查找cA当前目录获取函数定义
const getCurrentWorkingDirectory = () => {
  throw new Error('Current working directory function not yet refactored (original: cA)');
};

// TODO_REFACTOR: 查找z9工作目录获取函数定义
const getWorkingDirectory = () => {
  throw new Error('Working directory function not yet refactored (original: z9)');
};

// TODO_REFACTOR: 查找_o2最大行数常量定义
const MAX_DISPLAY_LINES = 10; // 临时占位符

// TODO_REFACTOR: 查找m3A最大字符数常量定义
const MAX_DISPLAY_CHARS = 1000; // 临时占位符

// TODO_REFACTOR: 查找S文本组件定义
const Text = ({ children, ...props }) => {
  throw new Error('Text component not yet refactored (original: S)');
};

// TODO_REFACTOR: 查找d6拒绝消息组件定义
const RejectedMessage = () => {
  throw new Error('Rejected message component not yet refactored (original: d6)');
};

// TODO_REFACTOR: 查找U0容器组件定义
const Box = ({ children, ...props }) => {
  throw new Error('Box component not yet refactored (original: U0)');
};

// TODO_REFACTOR: 查找qo2进度组件定义
const ProgressMessage = ({ lastLines, elapsedTimeSeconds, totalLines }) => {
  throw new Error('Progress message component not yet refactored (original: qo2)');
};

// TODO_REFACTOR: 查找yp结果组件定义
const ResultMessage = ({ content, verbose }) => {
  throw new Error('Result message component not yet refactored (original: yp)');
};

// TODO_REFACTOR: 查找cQ错误组件定义
const ErrorMessage = ({ result, verbose }) => {
  throw new Error('Error message component not yet refactored (original: cQ)');
};

// TODO_REFACTOR: 查找nQ4命令执行函数定义
const executeCommand = async function* ({ input, abortController, setToolJSX, isReadOnly }) {
  throw new Error('Command execution function not yet refactored (original: nQ4)');
};

// TODO_REFACTOR: 查找pQ4命令记录函数定义
const logCommandExecution = (command, exitCode) => {
  throw new Error('Command logging function not yet refactored (original: pQ4)');
};

// TODO_REFACTOR: 查找Po2返回码解释函数定义
const interpretReturnCode = (command, code, stdout, stderr) => {
  throw new Error('Return code interpretation function not yet refactored (original: Po2)');
};

// TODO_REFACTOR: 查找P$1分隔符常量定义
const OUTPUT_SEPARATOR = '\n'; // 临时占位符

// TODO_REFACTOR: 查找Iq1权限检查函数定义
const shouldRedactOutput = (permissionContext) => {
  throw new Error('Output redaction check function not yet refactored (original: Iq1)');
};

// TODO_REFACTOR: 查找Qq1输出清理函数定义
const redactSensitiveOutput = (output) => {
  throw new Error('Output redaction function not yet refactored (original: Qq1)');
};

// TODO_REFACTOR: 查找gz错误类定义
class BashExecutionError extends Error {
  constructor(stdout, stderr, code, interrupted) {
    super('Bash execution error');
    this.stdout = stdout;
    this.stderr = stderr;
    this.code = code;
    this.interrupted = interrupted;
  }
}

// TODO_REFACTOR: 查找Lp2文件路径提取函数定义
const extractFilePaths = async (command, output, isNonInteractive) => {
  throw new Error('File path extraction function not yet refactored (original: Lp2)');
};

// TODO_REFACTOR: 查找hQ4绝对路径检查函数定义
const isAbsolutePath = (path) => {
  throw new Error('Absolute path check function not yet refactored (original: hQ4)');
};

// TODO_REFACTOR: 查找uQ4路径解析函数定义
const resolvePath = (basePath, relativePath) => {
  throw new Error('Path resolution function not yet refactored (original: uQ4)');
};

// TODO_REFACTOR: 查找b1文件系统函数定义
const getFileSystem = () => {
  throw new Error('File system function not yet refactored (original: b1)');
};

// TODO_REFACTOR: 查找uI文件读取函数定义
const readFileContent = (path) => {
  throw new Error('File read function not yet refactored (original: uI)');
};

// TODO_REFACTOR: 查找v1错误处理函数定义
const handleError = (error) => {
  throw new Error('Error handler function not yet refactored (original: v1)');
};

// TODO_REFACTOR: 查找E1遥测记录函数定义
const trackEvent = (eventName, properties) => {
  throw new Error('Track event function not yet refactored (original: E1)');
};

// TODO_REFACTOR: 查找bP输出截断函数定义
const truncateOutput = (content) => {
  throw new Error('Output truncation function not yet refactored (original: bP)');
};

// TODO_REFACTOR: 查找m21输出清理函数定义
const cleanOutput = (content) => {
  throw new Error('Output cleaning function not yet refactored (original: m21)');
};

/**
 * Bash工具定义
 * 重构自: 第331313-331550行的l9对象
 */
const bashTool = {
  name: BASH_TOOL_NAME,
  
  /**
   * 获取工具描述
   */
  async description({ description }) {
    return description || "Run shell command";
  },
  
  /**
   * 获取工具提示
   */
  async prompt() {
    return getBashPrompt();
  },
  
  /**
   * 检查并发安全性
   */
  isConcurrencySafe(input) {
    return this.isReadOnly(input);
  },
  
  /**
   * 检查是否为只读操作
   */
  isReadOnly(input) {
    const { command } = input;
    const isSandbox = ("sandbox" in input ? !!input.sandbox : false);
    
    if (isSandbox) {
      return true;
    }
    
    const commandParts = parseCommand(command);
    return commandParts.every((part) => {
      // 检查危险命令
      if (isDangerousCommand(part)) {
        return false;
      }
      
      // 检查只读模式
      for (const pattern of READ_ONLY_PATTERNS) {
        if (pattern.test(part)) {
          return true;
        }
      }
      
      return false;
    });
  },
  
  /**
   * 输入模式定义
   */
  get inputSchema() {
    return isSandboxEnabled() ? sandboxInputSchema : normalInputSchema;
  },
  
  /**
   * 获取用户面向名称
   */
  userFacingName(input) {
    if (!input) {
      return "Bash";
    }
    
    const isSandbox = ("sandbox" in input ? !!input.sandbox : false);
    return isSandbox ? "SandboxedBash" : "Bash";
  },
  
  /**
   * 检查工具是否启用
   */
  isEnabled() {
    return true;
  },
  
  /**
   * 检查权限
   */
  async checkPermissions(input, context) {
    const isSandbox = ("sandbox" in input ? !!input.sandbox : false);
    
    if (isSandbox) {
      return {
        behavior: "allow",
        updatedInput: input
      };
    }
    
    return checkBashPermissions(input, context);
  },
  
  /**
   * 验证输入
   */
  async validateInput(input, context) {
    const validationResult = validateBashInput(
      input,
      getCurrentWorkingDirectory(),
      getWorkingDirectory(),
      context.getToolPermissionContext()
    );
    
    if (validationResult.behavior !== "allow") {
      return {
        result: false,
        message: validationResult.message,
        errorCode: 1
      };
    }
    
    return {
      result: true
    };
  },
  
  /**
   * 渲染工具使用消息
   */
  renderToolUseMessage(input, { verbose }) {
    const { command } = input;
    
    if (!command) {
      return null;
    }
    
    let displayCommand = command;
    
    // 处理特殊的heredoc格式
    if (command.includes(`"$(cat <<'EOF'`)) {
      const match = command.match(/^(.*?)"?\$\(cat <<'EOF'\n([\s\S]*?)\n\s*EOF\n\s*\)"(.*)$/);
      if (match && match[1] && match[2]) {
        const prefix = match[1];
        const content = match[2];
        const suffix = match[3] || "";
        displayCommand = `${prefix.trim()} "${content.trim()}"${suffix.trim()}`;
      }
    }
    
    // 在非详细模式下截断长命令
    if (!verbose) {
      const lines = displayCommand.split('\n');
      const tooManyLines = lines.length > MAX_DISPLAY_LINES;
      const tooLong = displayCommand.length > MAX_DISPLAY_CHARS;
      
      if (tooManyLines || tooLong) {
        let truncated = displayCommand;
        
        if (tooManyLines) {
          truncated = lines.slice(0, MAX_DISPLAY_LINES).join('\n');
        }
        
        if (truncated.length > MAX_DISPLAY_CHARS) {
          truncated = truncated.slice(0, MAX_DISPLAY_CHARS);
        }
        
        return React.createElement(Text, null, truncated.trim(), "…");
      }
    }
    
    return displayCommand;
  },
  
  /**
   * 渲染工具使用被拒绝消息
   */
  renderToolUseRejectedMessage() {
    return React.createElement(RejectedMessage, null);
  },
  
  /**
   * 渲染工具使用进度消息
   */
  renderToolUseProgressMessage(progressMessages) {
    const lastMessage = progressMessages.at(-1);
    
    if (!lastMessage || !lastMessage.data || !lastMessage.data.output) {
      return React.createElement(Box, {
        height: 1
      }, React.createElement(Text, {
        color: "secondaryText"
      }, "Running…"));
    }
    
    const progressData = lastMessage.data;
    return React.createElement(ProgressMessage, {
      lastLines: progressData.output,
      elapsedTimeSeconds: progressData.elapsedTimeSeconds,
      totalLines: progressData.totalLines
    });
  },
  
  /**
   * 渲染工具使用排队消息
   */
  renderToolUseQueuedMessage() {
    return React.createElement(Box, {
      height: 1
    }, React.createElement(Text, {
      color: "secondaryText"
    }, "Waiting…"));
  },
  
  /**
   * 渲染工具结果消息
   */
  renderToolResultMessage(result, progressMessages, { verbose }) {
    return React.createElement(ResultMessage, {
      content: result,
      verbose: verbose
    });
  },
  
  /**
   * 将工具结果映射为工具结果块参数
   */
  mapToolResultToToolResultBlockParam({ interrupted, stdout, stderr, isImage }, toolUseId) {
    // 处理图像结果
    if (isImage) {
      const imageMatch = stdout.trim().match(/^data:([^;]+);base64,(.+)$/);
      if (imageMatch) {
        const mediaType = imageMatch[1];
        const data = imageMatch[2];
        
        return {
          tool_use_id: toolUseId,
          type: "tool_result",
          content: [{
            type: "image",
            source: {
              type: "base64",
              media_type: mediaType || "image/jpeg",
              data: data || ""
            }
          }]
        };
      }
    }
    
    // 处理文本结果
    let cleanStdout = stdout;
    if (stdout) {
      cleanStdout = stdout.replace(/^(\s*\n)+/, "");
      cleanStdout = cleanStdout.trimEnd();
    }
    
    let cleanStderr = stderr.trim();
    
    // 处理中断情况
    if (interrupted) {
      if (stderr) {
        cleanStderr += OUTPUT_SEPARATOR;
      }
      cleanStderr += "<e>Command was aborted before completion</e>";
    }
    
    return {
      tool_use_id: toolUseId,
      type: "tool_result",
      content: [cleanStdout, cleanStderr].filter(Boolean).join('\n'),
      is_error: interrupted
    };
  },
  
  /**
   * 工具调用主函数
   */
  async* call(input, context) {
    let stdout = "";
    let stderr = "";
    let returnCodeInfo;
    let progressCounter = 0;
    let interrupted = false;
    let executionResult;
    
    try {
      // 执行命令
      const commandGenerator = executeCommand({
        input: input,
        abortController: context.abortController,
        setToolJSX: context.setToolJSX,
        isReadOnly: this.isReadOnly(input)
      });
      
      let result;
      do {
        result = await commandGenerator.next();
        if (!result.done) {
          const progressData = result.value;
          yield {
            type: "progress",
            toolUseID: `bash-progress-${progressCounter++}`,
            data: {
              type: "bash_progress",
              output: progressData.output,
              elapsedTimeSeconds: progressData.elapsedTimeSeconds,
              totalLines: progressData.totalLines
            }
          };
        }
      } while (!result.done);
      
      executionResult = result.value;
      
      // 记录命令执行
      logCommandExecution(input.command, executionResult.code);
      
      // 处理输出
      stdout += (executionResult.stdout || "").trimEnd() + OUTPUT_SEPARATOR;
      returnCodeInfo = interpretReturnCode(
        input.command,
        executionResult.code,
        executionResult.stdout || "",
        executionResult.stderr || ""
      );
      
      if (returnCodeInfo.isError) {
        stderr += (executionResult.stderr || "").trimEnd() + OUTPUT_SEPARATOR;
        if (executionResult.code !== 0) {
          stderr += `Exit code ${executionResult.code}`;
        }
      } else {
        stdout += (executionResult.stderr || "").trimEnd() + OUTPUT_SEPARATOR;
      }
      
      // 检查输出清理
      if (shouldRedactOutput(context.getToolPermissionContext())) {
        stderr = redactSensitiveOutput(stderr);
      }
      
      // 如果有错误，抛出异常
      if (returnCodeInfo.isError) {
        throw new BashExecutionError(
          executionResult.stdout,
          executionResult.stderr,
          executionResult.code,
          executionResult.interrupted
        );
      }
      
      interrupted = executionResult.interrupted;
      
    } finally {
      // 清理工具JSX
      if (context.setToolJSX) {
        context.setToolJSX(null);
      }
    }
    
    // 异步处理文件路径提取
    extractFilePaths(input.command, stdout, context.options.isNonInteractiveSession)
      .then((filePaths) => {
        for (const filePath of filePaths) {
          const resolvedPath = isAbsolutePath(filePath) 
            ? filePath 
            : resolvePath(getCurrentWorkingDirectory(), filePath);
          
          try {
            const fs = getFileSystem();
            if (!fs.existsSync(resolvedPath) || !fs.statSync(resolvedPath).isFile()) {
              continue;
            }
            
            context.readFileState[resolvedPath] = {
              content: readFileContent(resolvedPath),
              timestamp: fs.statSync(resolvedPath).mtimeMs
            };
          } catch (error) {
            handleError(error);
          }
        }
        
        // 记录遥测
        trackEvent("tengu_bash_tool_haiku_file_paths_read", {
          filePathsExtracted: filePaths.length,
          readFileStateSize: Object.keys(context.readFileState).length,
          readFileStateValuesCharLength: Object.values(context.readFileState)
            .reduce((total, file) => total + file.content.length, 0)
        });
      });
    
    // 截断输出
    const { truncatedContent: truncatedStdout, isImage } = truncateOutput(cleanOutput(stdout));
    const { truncatedContent: truncatedStderr } = truncateOutput(cleanOutput(stderr));
    
    // 返回结果
    yield {
      type: "result",
      data: {
        stdout: truncatedStdout,
        stderr: truncatedStderr,
        interrupted: interrupted,
        isImage: isImage,
        returnCodeInterpretation: returnCodeInfo?.message,
        backgroundTaskId: executionResult.backgroundTaskId
      }
    };
  },
  
  /**
   * 渲染工具使用错误消息
   */
  renderToolUseErrorMessage(error, { verbose }) {
    return React.createElement(ErrorMessage, {
      result: error,
      verbose: verbose
    });
  }
};

module.exports = {
  bashTool,
  BashExecutionError
};
