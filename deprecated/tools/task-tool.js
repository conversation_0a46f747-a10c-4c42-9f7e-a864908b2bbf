/**
 * Claude Code - Task Tool
 * 
 * 重构来源: cli-format-all.js 第338402-338650行 (248行)
 * 重构时间: 2025-01-19
 * 
 * 任务工具，支持并行代理执行和任务合成
 */

// Node.js 内置模块
const { randomUUID } = require('crypto');

// 第三方依赖
const React = require('react');

// TODO_REFACTOR: 以下是未重构的依赖，需要后续替换

// TODO_REFACTOR: 查找w5A工具名称常量定义
const TASK_TOOL_NAME = 'Task'; // 临时占位符

// TODO_REFACTOR: 查找Le2任务提示函数定义
const getTaskPrompt = async (tools) => {
  throw new Error('Task prompt function not yet refactored (original: Le2)');
};

// TODO_REFACTOR: 查找h44输入模式定义
const taskInputSchema = {}; // 临时占位符

// TODO_REFACTOR: 查找JA应用状态获取函数定义
const getAppState = () => {
  throw new Error('App state function not yet refactored (original: JA)');
};

// TODO_REFACTOR: 查找q5A代理执行函数定义
const runAgent = async function* (prompt, agentIndex, context, parentContext, callContext, options = {}) {
  throw new Error('Agent execution function not yet refactored (original: q5A)');
};

// TODO_REFACTOR: 查找Dp并行执行函数定义
const runInParallel = async function* (generators, concurrency) {
  throw new Error('Parallel execution function not yet refactored (original: Dp)');
};

// TODO_REFACTOR: 查找i3中断错误类定义
class InterruptError extends Error {
  constructor() {
    super('Operation was interrupted');
    this.name = 'InterruptError';
  }
}

// TODO_REFACTOR: 查找u44合成函数定义
const synthesizeResults = (originalPrompt, results) => {
  throw new Error('Result synthesis function not yet refactored (original: u44)');
};

// TODO_REFACTOR: 查找$W消息构造函数定义
const createMessage = (options) => {
  throw new Error('Message constructor function not yet refactored (original: $W)');
};

// TODO_REFACTOR: 查找Z3消息转换函数定义
const convertMessages = (messages) => {
  throw new Error('Message conversion function not yet refactored (original: Z3)');
};

// TODO_REFACTOR: 查找CI令牌格式化函数定义
const formatTokenCount = (count) => {
  throw new Error('Token count formatting function not yet refactored (original: CI)');
};

// TODO_REFACTOR: 查找D$持续时间格式化函数定义
const formatDuration = (milliseconds) => {
  throw new Error('Duration formatting function not yet refactored (original: D$)');
};

// TODO_REFACTOR: 查找P9 React库定义
const ReactLib = React; // 临时占位符

// TODO_REFACTOR: 查找b布局组件定义
const Box = ({ children, ...props }) => {
  throw new Error('Box component not yet refactored (original: b)');
};

// TODO_REFACTOR: 查找U0容器组件定义
const Container = ({ children, ...props }) => {
  throw new Error('Container component not yet refactored (original: U0)');
};

// TODO_REFACTOR: 查找S文本组件定义
const Text = ({ children, ...props }) => {
  throw new Error('Text component not yet refactored (original: S)');
};

// TODO_REFACTOR: 查找DY消息显示组件定义
const MessageDisplay = ({ message, messages, addMargin, tools, verbose, erroredToolUseIDs, inProgressToolUseIDs, resolvedToolUseIDs, progressMessagesForMessage, shouldAnimate, shouldShowDot }) => {
  throw new Error('Message display component not yet refactored (original: DY)');
};

// TODO_REFACTOR: 查找rF主题格式化函数定义
const formatWithTheme = (text, theme) => {
  throw new Error('Theme formatting function not yet refactored (original: rF)');
};

/**
 * Task工具定义
 * 重构自: 第338402-338650行的Me2对象
 */
const taskTool = {
  name: TASK_TOOL_NAME,
  
  /**
   * 获取工具提示
   */
  async prompt({ tools }) {
    return await getTaskPrompt(tools);
  },
  
  /**
   * 获取工具描述
   */
  async description() {
    return "Launch a new task";
  },
  
  /**
   * 输入模式定义
   */
  get inputSchema() {
    return taskInputSchema;
  },
  
  /**
   * 检查是否为只读操作
   */
  isReadOnly() {
    return true;
  },
  
  /**
   * 检查并发安全性
   */
  isConcurrencySafe() {
    return true;
  },
  
  /**
   * 检查工具是否启用
   */
  isEnabled() {
    return true;
  },
  
  /**
   * 获取用户面向名称
   */
  userFacingName() {
    return "Task";
  },
  
  /**
   * 检查权限
   */
  async checkPermissions(input) {
    return {
      behavior: "allow",
      updatedInput: input
    };
  },
  
  /**
   * 工具调用主函数
   */
  async* call(
    { prompt },
    {
      abortController,
      options: { debug, tools, verbose, isNonInteractiveSession },
      getToolPermissionContext,
      readFileState,
      setInProgressToolUseIDs,
      getQueuedCommands,
      removeQueuedCommands
    },
    callContext,
    parentContext
  ) {
    const startTime = Date.now();
    const appState = getAppState();
    
    // 创建代理上下文
    const agentContext = {
      abortController,
      options: {
        debug,
        verbose,
        isNonInteractiveSession: isNonInteractiveSession ?? false
      },
      getToolPermissionContext,
      readFileState,
      setInProgressToolUseIDs,
      getQueuedCommands: appState.parallelTasksCount > 1 ? () => [] : getQueuedCommands,
      removeQueuedCommands: appState.parallelTasksCount > 1 ? () => {} : removeQueuedCommands,
      tools: tools.filter((tool) => tool.name !== TASK_TOOL_NAME)
    };
    
    // 检查是否使用并行模式
    if (appState.parallelTasksCount > 1) {
      // 并行模式执行
      let totalToolUseCount = 0;
      let totalTokens = 0;
      
      // 创建并行代理
      const parallelPrompts = Array(appState.parallelTasksCount)
        .fill(`${prompt}\n\nProvide a thorough and complete analysis.`)
        .map((agentPrompt, index) => 
          runAgent(agentPrompt, index, agentContext, parentContext, callContext)
        );
      
      const agentResults = [];
      
      // 执行并行代理
      for await (const result of runInParallel(parallelPrompts, 10)) {
        if (result.type === "progress") {
          yield result;
        } else if (result.type === "result") {
          agentResults.push(result.data);
          totalToolUseCount += result.data.toolUseCount;
          totalTokens += result.data.tokens;
        }
      }
      
      // 检查中断
      if (abortController.signal.aborted) {
        throw new InterruptError();
      }
      
      // 合成结果
      const synthesisPrompt = synthesizeResults(prompt, agentResults);
      const synthesisAgent = runAgent(synthesisPrompt, 0, agentContext, parentContext, callContext, {
        isSynthesis: true
      });
      
      let synthesisResult = null;
      for await (const result of synthesisAgent) {
        if (result.type === "progress") {
          totalToolUseCount++;
          yield result;
        } else if (result.type === "result") {
          synthesisResult = result.data;
          totalTokens += synthesisResult.tokens;
        }
      }
      
      if (!synthesisResult) {
        throw new Error("Synthesis agent did not return a result");
      }
      
      // 检查中断
      if (abortController.signal.aborted) {
        throw new InterruptError();
      }
      
      // 查找退出计划模式输入
      const exitPlanModeInput = agentResults.find((result) => result.exitPlanModeInput)?.exitPlanModeInput;
      
      // 返回并行结果
      yield {
        type: "result",
        data: {
          content: synthesisResult.content,
          totalDurationMs: Date.now() - startTime,
          totalTokens: totalTokens,
          totalToolUseCount: totalToolUseCount,
          usage: synthesisResult.usage,
          wasInterrupted: abortController.signal.aborted,
          exitPlanModeInput: exitPlanModeInput
        }
      };
      
    } else {
      // 单代理模式执行
      const singleAgent = runAgent(prompt, 0, agentContext, parentContext, callContext);
      let totalToolUseCount = 0;
      let agentResult = null;
      
      for await (const result of singleAgent) {
        if (result.type === "progress") {
          yield result;
        } else if (result.type === "result") {
          agentResult = result.data;
          totalToolUseCount = agentResult.toolUseCount;
        }
      }
      
      // 检查中断
      if (abortController.signal.aborted) {
        throw new InterruptError();
      }
      
      if (!agentResult) {
        throw new Error("Agent did not return a result");
      }
      
      // 返回单代理结果
      yield {
        type: "result",
        data: {
          content: agentResult.content,
          totalDurationMs: Date.now() - startTime,
          totalTokens: agentResult.tokens,
          totalToolUseCount: totalToolUseCount,
          usage: agentResult.usage,
          wasInterrupted: abortController.signal.aborted,
          exitPlanModeInput: agentResult.exitPlanModeInput
        }
      };
    }
  },
  
  /**
   * 将工具结果映射为工具结果块参数
   */
  mapToolResultToToolResultBlockParam(result, toolUseId) {
    // 处理退出计划模式
    if (result.exitPlanModeInput) {
      return {
        tool_use_id: toolUseId,
        type: "tool_result",
        content: [{
          type: "text",
          text: `The agent created a new plan that was approved by the user. Please go ahead and start implementing this plan and use the todo tool if applicable. We are no longer in plan mode and you do not need to use the exit_plan_mode tool.

User-approved plan:` + result.exitPlanModeInput.plan
        }]
      };
    }
    
    return {
      tool_use_id: toolUseId,
      type: "tool_result",
      content: result.content
    };
  },
  
  /**
   * 渲染工具结果消息
   */
  renderToolResultMessage(
    { totalDurationMs, totalToolUseCount, totalTokens, usage },
    progressMessages,
    { tools, verbose }
  ) {
    const appState = getAppState();
    const stats = [
      totalToolUseCount === 1 ? "1 tool use" : `${totalToolUseCount} tool uses`,
      formatTokenCount(totalTokens) + " tokens",
      formatDuration(totalDurationMs)
    ];
    
    const summaryText = appState.parallelTasksCount > 1 
      ? `Done with ${appState.parallelTasksCount} parallel agents (${stats.join(" · ")})`
      : `Done (${stats.join(" · ")})`;
    
    const summaryMessage = createMessage({
      content: summaryText,
      usage: usage
    });
    
    return ReactLib.createElement(Box, {
      flexDirection: "column"
    }, 
      // 详细模式显示所有进度消息
      verbose ? progressMessages.map((message) => 
        ReactLib.createElement(Container, {
          height: 1,
          key: message.uuid
        }, ReactLib.createElement(MessageDisplay, {
          message: message.data.message,
          messages: message.data.normalizedMessages,
          addMargin: false,
          tools: tools,
          verbose: verbose,
          erroredToolUseIDs: new Set(),
          inProgressToolUseIDs: new Set(),
          resolvedToolUseIDs: new Set(),
          progressMessagesForMessage: progressMessages,
          shouldAnimate: false,
          shouldShowDot: false
        }))
      ) : null,
      
      // 显示摘要消息
      ReactLib.createElement(Container, {
        height: 1
      }, ReactLib.createElement(MessageDisplay, {
        message: summaryMessage,
        messages: convertMessages([summaryMessage]),
        addMargin: false,
        tools: tools,
        verbose: verbose,
        erroredToolUseIDs: new Set(),
        inProgressToolUseIDs: new Set(),
        resolvedToolUseIDs: new Set(),
        progressMessagesForMessage: [],
        shouldAnimate: false,
        shouldShowDot: false
      }))
    );
  },
  
  /**
   * 渲染工具使用消息
   */
  renderToolUseMessage({ description, prompt }, { theme, verbose }) {
    if (!description || !prompt) {
      return null;
    }
    
    if (verbose) {
      return `Task: ${description}\n\nPrompt: ${formatWithTheme(prompt, theme)}`;
    }
    
    return description;
  },
  
  /**
   * 渲染工具使用进度消息
   */
  renderToolUseProgressMessage(progressMessages, { tools, verbose }) {
    const appState = getAppState();
    
    if (!progressMessages.length) {
      return ReactLib.createElement(Container, {
        height: 1
      }, ReactLib.createElement(Text, {
        color: "secondaryText"
      }, appState.parallelTasksCount > 1 
        ? `Initializing ${appState.parallelTasksCount} parallel agents…` 
        : "Initializing…"
      ));
    }
    
    // 检查是否有并行代理和合成代理
    const hasParallelAgents = appState.parallelTasksCount > 1 && 
      progressMessages.some((msg) => 
        msg.toolUseID.startsWith("agent_") && msg.toolUseID.includes("_")
      );
    
    const hasSynthesisAgent = appState.parallelTasksCount > 1 && 
      progressMessages.some((msg) => msg.toolUseID.startsWith("synthesis_"));
    
    // 根据代理类型分组消息
    const agentGroups = new Map();
    
    if (hasParallelAgents) {
      for (const message of progressMessages) {
        const agentMatch = message.toolUseID.match(/^agent_(\d+)_/);
        if (agentMatch) {
          const agentIndex = parseInt(agentMatch[1]);
          if (!agentGroups.has(agentIndex)) {
            agentGroups.set(agentIndex, []);
          }
          agentGroups.get(agentIndex).push(message);
        }
      }
    }
    
    // 渲染进度消息
    return ReactLib.createElement(Box, {
      flexDirection: "column"
    }, 
      // 渲染并行代理进度
      Array.from(agentGroups.entries()).map(([agentIndex, messages]) => 
        ReactLib.createElement(Container, {
          key: `agent-${agentIndex}`,
          height: 1
        }, ReactLib.createElement(Text, {
          color: "secondaryText"
        }, `Agent ${agentIndex + 1}: ${messages.length} steps`))
      ),
      
      // 渲染合成代理进度
      hasSynthesisAgent ? ReactLib.createElement(Container, {
        height: 1
      }, ReactLib.createElement(Text, {
        color: "secondaryText"
      }, "Synthesizing results…")) : null
    );
  }
};

module.exports = {
  taskTool,
  InterruptError
};
