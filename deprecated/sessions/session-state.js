/**
 * Claude Code - Session State Management
 * 
 * 重构来源: cli-format-all.js 第301175-301206行 + 会话状态相关函数
 * 重构时间: 2025-01-19
 * 
 * 会话状态的获取、设置和持久化管理
 */

// TODO_REFACTOR: 以下是未重构的依赖，需要后续替换

// TODO_REFACTOR: 查找Al0当前项目路径获取函数定义
const getCurrentProjectPath = () => {
  throw new Error('Current project path function not yet refactored (original: Al0)');
};

// TODO_REFACTOR: 查找FT配置合并函数定义
const mergeConfig = (base, override) => {
  throw new Error('Config merge function not yet refactored (original: FT)');
};

// TODO_REFACTOR: 查找jF配置获取函数定义
const getFullConfig = () => {
  throw new Error('Full config function not yet refactored (original: jF)');
};

// TODO_REFACTOR: 查找QW默认配置定义
const DEFAULT_CONFIG = {}; // 临时占位符

// TODO_REFACTOR: 查找Z$默认会话状态定义
const DEFAULT_SESSION_STATE = {
  allowedTools: [],
  hasCompletedOnboarding: false,
  theme: 'auto',
  numStartups: 0
}; // 临时占位符

// TODO_REFACTOR: 查找S4文本解析函数定义
const parseText = (text) => {
  throw new Error('Text parsing function not yet refactored (original: S4)');
};

// TODO_REFACTOR: 查找tc0配置锁定保存函数定义
const saveConfigWithLock = (configPath, defaultConfig, updateFn) => {
  throw new Error('Config save with lock function not yet refactored (original: tc0)');
};

// TODO_REFACTOR: 查找I9错误日志函数定义
const logError = (message) => {
  throw new Error('Error log function not yet refactored (original: I9)');
};

// TODO_REFACTOR: 查找oc0配置保存函数定义
const saveConfig = (configPath, config, defaultConfig) => {
  throw new Error('Config save function not yet refactored (original: oc0)');
};

/**
 * 会话状态字段枚举
 */
const SESSION_STATE_FIELDS = {
  ALLOWED_TOOLS: 'allowedTools',
  HAS_COMPLETED_ONBOARDING: 'hasCompletedOnboarding',
  THEME: 'theme',
  NUM_STARTUPS: 'numStartups',
  LAST_COST: 'lastCost',
  LAST_DURATION: 'lastDuration',
  LAST_API_DURATION: 'lastAPIDuration',
  LAST_LINES_ADDED: 'lastLinesAdded',
  LAST_LINES_REMOVED: 'lastLinesRemoved',
  LAST_TOTAL_INPUT_TOKENS: 'lastTotalInputTokens',
  LAST_TOTAL_OUTPUT_TOKENS: 'lastTotalOutputTokens',
  LAST_TOTAL_CACHE_CREATION_INPUT_TOKENS: 'lastTotalCacheCreationInputTokens',
  LAST_TOTAL_CACHE_READ_INPUT_TOKENS: 'lastTotalCacheReadInputTokens',
  LAST_SESSION_ID: 'lastSessionId',
  PENDING_EXIT_FEEDBACK: 'pendingExitFeedback',
  ENABLED_MCPJSON_SERVERS: 'enabledMcpjsonServers',
  REJECTED_MCPJSON_SERVERS: 'rejectedMcpjsonServers'
};

/**
 * 获取会话状态
 * 重构自: 第301175-301184行的D9函数
 * 
 * @returns {Object} 当前项目的会话状态
 */
function getSessionState() {
  const projectPath = getCurrentProjectPath();
  const config = mergeConfig(getFullConfig(), DEFAULT_CONFIG);
  
  if (!config.projects) {
    return DEFAULT_SESSION_STATE;
  }
  
  const projectState = config.projects[projectPath] ?? DEFAULT_SESSION_STATE;
  
  // 处理字符串类型的allowedTools
  if (typeof projectState.allowedTools === "string") {
    projectState.allowedTools = parseText(projectState.allowedTools) ?? [];
  }
  
  return projectState;
}

/**
 * 设置会话状态
 * 重构自: 第301185-301206行的S6函数
 * 
 * @param {Object} newState - 新的会话状态
 */
function setSessionState(newState) {
  const projectPath = getCurrentProjectPath();
  
  try {
    // 尝试使用锁定机制保存配置
    saveConfigWithLock(getFullConfig(), DEFAULT_CONFIG, (config) => ({
      ...config,
      projects: {
        ...config.projects,
        [projectPath]: newState
      }
    }));
  } catch (error) {
    // 如果锁定保存失败，使用普通保存
    logError(`Failed to save config with lock: ${error}`);
    
    const currentConfig = mergeConfig(getFullConfig(), DEFAULT_CONFIG);
    saveConfig(getFullConfig(), {
      ...currentConfig,
      projects: {
        ...currentConfig.projects,
        [projectPath]: newState
      }
    }, DEFAULT_CONFIG);
  }
}

/**
 * 更新会话状态的特定字段
 * 
 * @param {string} field - 要更新的字段名
 * @param {any} value - 新值
 */
function updateSessionStateField(field, value) {
  const currentState = getSessionState();
  setSessionState({
    ...currentState,
    [field]: value
  });
}

/**
 * 获取会话状态的特定字段
 * 
 * @param {string} field - 字段名
 * @param {any} defaultValue - 默认值
 * @returns {any} 字段值
 */
function getSessionStateField(field, defaultValue = undefined) {
  const state = getSessionState();
  return state[field] ?? defaultValue;
}

/**
 * 清除会话状态的特定字段
 * 
 * @param {Array<string>} fields - 要清除的字段名列表
 */
function clearSessionStateFields(fields) {
  const currentState = getSessionState();
  const updatedState = { ...currentState };
  
  for (const field of fields) {
    delete updatedState[field];
  }
  
  setSessionState(updatedState);
}

/**
 * 重置会话状态为默认值
 */
function resetSessionState() {
  setSessionState(DEFAULT_SESSION_STATE);
}

/**
 * 合并会话状态
 * 
 * @param {Object} partialState - 部分状态对象
 */
function mergeSessionState(partialState) {
  const currentState = getSessionState();
  setSessionState({
    ...currentState,
    ...partialState
  });
}

/**
 * 检查会话状态字段是否存在
 * 
 * @param {string} field - 字段名
 * @returns {boolean} 字段是否存在
 */
function hasSessionStateField(field) {
  const state = getSessionState();
  return field in state && state[field] !== undefined;
}

/**
 * 获取会话状态的所有字段名
 * 
 * @returns {Array<string>} 字段名列表
 */
function getSessionStateFields() {
  const state = getSessionState();
  return Object.keys(state);
}

/**
 * 验证会话状态对象
 * 
 * @param {Object} state - 要验证的状态对象
 * @returns {Object} 验证结果
 */
function validateSessionState(state) {
  const errors = [];
  const warnings = [];
  
  if (!state || typeof state !== 'object') {
    errors.push('Session state must be an object');
    return { isValid: false, errors, warnings };
  }
  
  // 检查allowedTools字段
  if (state.allowedTools !== undefined) {
    if (!Array.isArray(state.allowedTools) && typeof state.allowedTools !== 'string') {
      errors.push('allowedTools must be an array or string');
    }
  }
  
  // 检查布尔字段
  const booleanFields = ['hasCompletedOnboarding'];
  for (const field of booleanFields) {
    if (state[field] !== undefined && typeof state[field] !== 'boolean') {
      errors.push(`${field} must be a boolean`);
    }
  }
  
  // 检查数字字段
  const numberFields = ['numStartups', 'lastCost', 'lastDuration', 'lastAPIDuration'];
  for (const field of numberFields) {
    if (state[field] !== undefined && typeof state[field] !== 'number') {
      errors.push(`${field} must be a number`);
    }
  }
  
  // 检查字符串字段
  const stringFields = ['theme', 'lastSessionId'];
  for (const field of stringFields) {
    if (state[field] !== undefined && typeof state[field] !== 'string') {
      errors.push(`${field} must be a string`);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 获取会话状态统计信息
 * 
 * @returns {Object} 统计信息
 */
function getSessionStateStats() {
  const state = getSessionState();
  
  return {
    totalFields: Object.keys(state).length,
    hasOnboarding: !!state.hasCompletedOnboarding,
    allowedToolsCount: Array.isArray(state.allowedTools) ? state.allowedTools.length : 0,
    numStartups: state.numStartups || 0,
    theme: state.theme || 'unknown',
    hasLastSession: !!state.lastSessionId,
    hasPendingFeedback: !!state.pendingExitFeedback
  };
}

/**
 * 会话状态管理器类
 */
class SessionStateManager {
  constructor() {
    this.cache = null;
    this.cacheTimestamp = null;
    this.cacheTTL = 5000; // 5秒缓存
  }
  
  /**
   * 获取缓存的会话状态
   * 
   * @returns {Object} 会话状态
   */
  getCachedState() {
    const now = Date.now();
    
    if (this.cache && this.cacheTimestamp && (now - this.cacheTimestamp) < this.cacheTTL) {
      return this.cache;
    }
    
    this.cache = getSessionState();
    this.cacheTimestamp = now;
    
    return this.cache;
  }
  
  /**
   * 设置状态并清除缓存
   * 
   * @param {Object} newState - 新状态
   */
  setState(newState) {
    setSessionState(newState);
    this.clearCache();
  }
  
  /**
   * 更新状态字段并清除缓存
   * 
   * @param {string} field - 字段名
   * @param {any} value - 新值
   */
  updateField(field, value) {
    updateSessionStateField(field, value);
    this.clearCache();
  }
  
  /**
   * 清除缓存
   */
  clearCache() {
    this.cache = null;
    this.cacheTimestamp = null;
  }
  
  /**
   * 获取统计信息
   * 
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      ...getSessionStateStats(),
      cacheHit: !!this.cache,
      cacheAge: this.cacheTimestamp ? Date.now() - this.cacheTimestamp : null
    };
  }
}

/**
 * 创建会话状态管理器实例
 * 
 * @returns {SessionStateManager} 会话状态管理器实例
 */
function createSessionStateManager() {
  return new SessionStateManager();
}

module.exports = {
  // 主要函数
  getSessionState,
  setSessionState,
  updateSessionStateField,
  getSessionStateField,
  clearSessionStateFields,
  resetSessionState,
  mergeSessionState,
  
  // 检查函数
  hasSessionStateField,
  getSessionStateFields,
  validateSessionState,
  getSessionStateStats,
  
  // 会话状态管理器
  SessionStateManager,
  createSessionStateManager,
  
  // 常量
  SESSION_STATE_FIELDS
};
