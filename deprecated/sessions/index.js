/**
 * Claude Code - Session Management System
 * 
 * 重构来源: cli-format-all.js 会话管理相关函数集合
 * 重构时间: 2025-01-19
 * 
 * 会话管理系统的主入口，整合会话管理器和状态管理
 */

// 导入会话模块
const {
  resumeSession,
  saveMessages,
  updateMessages,
  saveCheckpoint,
  createNewSessionFile,
  extractPromptFromMessages,
  cleanMessages,
  SessionManager,
  createSessionManager,
  SESSION_STATES,
  MESSAGE_TYPES
} = require('./session-manager');

const {
  getSessionState,
  setSessionState,
  updateSessionStateField,
  getSessionStateField,
  clearSessionStateFields,
  resetSessionState,
  mergeSessionState,
  hasSessionStateField,
  getSessionStateFields,
  validateSessionState,
  getSessionStateStats,
  SessionStateManager,
  createSessionStateManager,
  SESSION_STATE_FIELDS
} = require('./session-state');

/**
 * 统一会话管理器类
 * 整合会话管理和状态管理功能
 */
class UnifiedSessionManager {
  constructor(options = {}) {
    this.sessionManager = createSessionManager();
    this.stateManager = createSessionStateManager();
    this.options = {
      autoSave: options.autoSave !== false,
      maxHistory: options.maxHistory || 100,
      ...options
    };
  }
  
  /**
   * 初始化会话管理器
   * 
   * @param {Object} options - 初始化选项
   * @returns {Promise<void>}
   */
  async initialize(options = {}) {
    // 创建新的会话文件（如果需要）
    if (options.createNewFile) {
      await createNewSessionFile();
    }
    
    // 加载现有状态
    const currentState = this.stateManager.getCachedState();
    
    // 如果有活动会话，尝试恢复
    if (currentState.lastSessionId && options.autoResume) {
      try {
        const resumedSession = await this.resumeSession(currentState.lastSessionId);
        if (resumedSession) {
          console.log(`Resumed session: ${currentState.lastSessionId}`);
        }
      } catch (error) {
        console.warn(`Failed to resume session ${currentState.lastSessionId}:`, error.message);
      }
    }
  }
  
  /**
   * 创建新会话
   * 
   * @param {Object} options - 会话选项
   * @returns {Object} 新会话对象
   */
  createSession(options = {}) {
    const session = this.sessionManager.createSession(options);
    
    // 更新状态
    this.stateManager.updateField(SESSION_STATE_FIELDS.LAST_SESSION_ID, session.id);
    
    return session;
  }
  
  /**
   * 恢复会话
   * 
   * @param {string} sessionId - 会话ID
   * @param {Array} additionalMessages - 额外消息
   * @returns {Promise<Object|null>} 恢复的会话
   */
  async resumeSession(sessionId, additionalMessages = []) {
    const resumedSession = await resumeSession(sessionId, additionalMessages);
    
    if (resumedSession) {
      // 更新当前会话
      this.sessionManager.currentSession = {
        id: sessionId,
        messages: resumedSession.messages,
        log: resumedSession.log,
        state: SESSION_STATES.ACTIVE,
        resumedAt: new Date().toISOString()
      };
      
      // 更新状态
      this.stateManager.updateField(SESSION_STATE_FIELDS.LAST_SESSION_ID, sessionId);
    }
    
    return resumedSession;
  }
  
  /**
   * 添加消息
   * 
   * @param {Object} message - 消息对象
   * @returns {Promise<string|null>} 消息UUID
   */
  async addMessage(message) {
    // 添加到会话管理器
    this.sessionManager.addMessage(message);
    
    // 如果启用自动保存，保存到数据库
    if (this.options.autoSave) {
      const currentSession = this.sessionManager.getCurrentSession();
      if (currentSession) {
        return await saveMessages(currentSession.messages);
      }
    }
    
    return null;
  }
  
  /**
   * 保存当前会话
   * 
   * @returns {Promise<string|null>} 最后消息的UUID
   */
  async saveCurrentSession() {
    const currentSession = this.sessionManager.getCurrentSession();
    if (currentSession && currentSession.messages.length > 0) {
      return await saveMessages(currentSession.messages);
    }
    return null;
  }
  
  /**
   * 结束当前会话
   * 
   * @param {Object} sessionStats - 会话统计信息
   * @returns {Promise<void>}
   */
  async endSession(sessionStats = {}) {
    const currentSession = this.sessionManager.getCurrentSession();
    
    if (currentSession) {
      // 保存会话统计信息到状态
      if (sessionStats.cost !== undefined) {
        this.stateManager.updateField(SESSION_STATE_FIELDS.LAST_COST, sessionStats.cost);
      }
      
      if (sessionStats.duration !== undefined) {
        this.stateManager.updateField(SESSION_STATE_FIELDS.LAST_DURATION, sessionStats.duration);
      }
      
      if (sessionStats.apiDuration !== undefined) {
        this.stateManager.updateField(SESSION_STATE_FIELDS.LAST_API_DURATION, sessionStats.apiDuration);
      }
      
      if (sessionStats.linesAdded !== undefined) {
        this.stateManager.updateField(SESSION_STATE_FIELDS.LAST_LINES_ADDED, sessionStats.linesAdded);
      }
      
      if (sessionStats.linesRemoved !== undefined) {
        this.stateManager.updateField(SESSION_STATE_FIELDS.LAST_LINES_REMOVED, sessionStats.linesRemoved);
      }
      
      if (sessionStats.totalInputTokens !== undefined) {
        this.stateManager.updateField(SESSION_STATE_FIELDS.LAST_TOTAL_INPUT_TOKENS, sessionStats.totalInputTokens);
      }
      
      if (sessionStats.totalOutputTokens !== undefined) {
        this.stateManager.updateField(SESSION_STATE_FIELDS.LAST_TOTAL_OUTPUT_TOKENS, sessionStats.totalOutputTokens);
      }
      
      if (sessionStats.totalCacheCreationInputTokens !== undefined) {
        this.stateManager.updateField(SESSION_STATE_FIELDS.LAST_TOTAL_CACHE_CREATION_INPUT_TOKENS, sessionStats.totalCacheCreationInputTokens);
      }
      
      if (sessionStats.totalCacheReadInputTokens !== undefined) {
        this.stateManager.updateField(SESSION_STATE_FIELDS.LAST_TOTAL_CACHE_READ_INPUT_TOKENS, sessionStats.totalCacheReadInputTokens);
      }
      
      // 保存最终会话
      await this.saveCurrentSession();
      
      // 结束会话
      this.sessionManager.endSession(SESSION_STATES.COMPLETED);
    }
  }
  
  /**
   * 获取当前会话
   * 
   * @returns {Object|null} 当前会话
   */
  getCurrentSession() {
    return this.sessionManager.getCurrentSession();
  }
  
  /**
   * 获取会话状态
   * 
   * @returns {Object} 会话状态
   */
  getState() {
    return this.stateManager.getCachedState();
  }
  
  /**
   * 更新会话状态
   * 
   * @param {Object} newState - 新状态
   */
  updateState(newState) {
    this.stateManager.setState(newState);
  }
  
  /**
   * 更新状态字段
   * 
   * @param {string} field - 字段名
   * @param {any} value - 新值
   */
  updateStateField(field, value) {
    this.stateManager.updateField(field, value);
  }
  
  /**
   * 清理历史记录
   */
  cleanup() {
    this.sessionManager.cleanupHistory(this.options.maxHistory);
    this.stateManager.clearCache();
  }
  
  /**
   * 获取统计信息
   * 
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      session: this.sessionManager.getStats(),
      state: this.stateManager.getStats(),
      options: this.options
    };
  }
  
  /**
   * 重置所有数据
   */
  reset() {
    this.sessionManager = createSessionManager();
    this.stateManager.clearCache();
  }
}

/**
 * 创建统一会话管理器实例
 * 
 * @param {Object} options - 创建选项
 * @returns {UnifiedSessionManager} 统一会话管理器实例
 */
function createUnifiedSessionManager(options = {}) {
  return new UnifiedSessionManager(options);
}

/**
 * 会话工具函数集合
 */
const SessionUtils = {
  // 消息处理
  extractPrompt: extractPromptFromMessages,
  cleanMessages,
  
  // 状态验证
  validateState: validateSessionState,
  
  // 统计信息
  getStateStats: getSessionStateStats,
  
  // 字段检查
  hasStateField: hasSessionStateField,
  getStateFields: getSessionStateFields
};

module.exports = {
  // 主要类和函数
  UnifiedSessionManager,
  createUnifiedSessionManager,
  
  // 工具函数
  SessionUtils,
  
  // 会话管理
  SessionManager,
  createSessionManager,
  resumeSession,
  saveMessages,
  updateMessages,
  saveCheckpoint,
  createNewSessionFile,
  
  // 状态管理
  SessionStateManager,
  createSessionStateManager,
  getSessionState,
  setSessionState,
  updateSessionStateField,
  getSessionStateField,
  clearSessionStateFields,
  resetSessionState,
  mergeSessionState,
  
  // 工具函数
  extractPromptFromMessages,
  cleanMessages,
  hasSessionStateField,
  getSessionStateFields,
  validateSessionState,
  getSessionStateStats,
  
  // 常量
  SESSION_STATES,
  MESSAGE_TYPES,
  SESSION_STATE_FIELDS
};
