/**
 * Claude Code - Dependencies Management System
 * 
 * 重构来源: cli-format-all.js 第1-294776行 第三方库和依赖管理
 * 重构时间: 2025-01-19
 * 
 * 依赖管理系统的主入口，整合第三方库管理和内部依赖解析
 */

// 导入依赖管理模块
const {
  ThirdPartyLibraryManager,
  createThirdPartyLibraryManager,
  getGlobalThirdPartyManager,
  THIRD_PARTY_LIBRARIES,
  FUNCTION_MAPPINGS
} = require('./third-party-libs');

/**
 * 内部依赖映射表
 * 记录所有需要替换的内部依赖
 */
const INTERNAL_DEPENDENCIES = {
  // 平台检测相关
  'oJ': 'platform.isPlatformSupported',
  'bz': 'platform.isPlatformSupported',
  
  // 终端信息相关
  'iA': 'terminal.terminalInfo',
  'S01': 'terminal.getTerminalName',
  'hZ': 'terminal.isJetBrains',
  
  // 目录和路径相关
  'cA': 'filesystem.getCurrentDirectory',
  'l91': 'filesystem.joinPath',
  
  // 安装信息相关
  'bp': 'installation.getInstallationInfo',
  
  // 文件管理相关
  'GB1': 'files.getLargeFiles',
  'eF': 'files.getMemoryFiles',
  'e$': 'files.getUltraClaudeFile',
  'ED1': 'files.getFileType',
  'b1': 'filesystem.fileSystem',
  
  // 格式化相关
  'CI': 'formatting.formatNumber',
  'Xh': 'formatting.cleanText',
  
  // 错误处理相关
  'v1': 'errors.handleError',
  
  // 哈希和加密相关
  'KZ4': 'crypto.createHash',
  'ND4': 'crypto.createHash',
  'Y6B': 'crypto.randomUUID',
  'gD4': 'crypto.randomUUID',
  'dD4': 'crypto.randomUUID',
  'fIA': 'crypto.randomUUID',
  'GFA': 'crypto.randomUUID',
  
  // 命令执行相关
  'I8': 'process.executeCommand',
  't9B': 'process.configureGit',
  
  // 检查点相关
  'Fa1': 'checkpoints.saveCheckpointToLog',
  
  // 文本处理相关
  'qE1': 'text.getTextLengthLimit',
  'm21': 'text.trimEmptyLines',
  'bP': 'text.processAndTruncateText',
  
  // 图像处理相关
  'qp2': 'images.nativeImageProcessor',
  'Np2': 'images.nativeImageProcessor',
  'lE1': 'images.sharpLibrary',
  '$p2': 'images.getImageProcessor',
  
  // 语法高亮相关
  'u4A': 'highlighting.highlightLibrary',
  'al': 'highlighting.highlightLibrary',
  'NW': 'highlighting.CodeHighlighter',
  
  // UI组件相关
  'S': 'ui.BaseComponent',
  
  // 常量定义
  'DB1': 'constants.LARGE_FILE_THRESHOLD',
  'Ip': 'constants.ULTRACLAUD_FILE_LIMIT',
  'yIA': 'constants.MAX_BUFFER_SIZE'
};

/**
 * 依赖状态枚举
 */
const DEPENDENCY_STATUS = {
  AVAILABLE: 'available',
  PLACEHOLDER: 'placeholder',
  MISSING: 'missing',
  ERROR: 'error'
};

/**
 * 统一依赖管理系统类
 */
class DependencyManager {
  constructor() {
    this.thirdPartyManager = createThirdPartyLibraryManager();
    this.internalDependencies = new Map();
    this.dependencyStatus = new Map();
    this.isInitialized = false;
  }

  /**
   * 初始化依赖管理系统
   * 
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      // 初始化第三方库管理器
      await this.thirdPartyManager.initialize();
      
      // 初始化内部依赖
      this.initializeInternalDependencies();
      
      this.isInitialized = true;
    } catch (error) {
      throw new Error(`Failed to initialize dependency manager: ${error.message}`);
    }
  }

  /**
   * 初始化内部依赖
   */
  initializeInternalDependencies() {
    for (const [originalName, mappingPath] of Object.entries(INTERNAL_DEPENDENCIES)) {
      try {
        // 创建占位符函数
        const placeholder = this.createDependencyPlaceholder(originalName, mappingPath);
        this.internalDependencies.set(originalName, placeholder);
        this.dependencyStatus.set(originalName, DEPENDENCY_STATUS.PLACEHOLDER);
      } catch (error) {
        this.dependencyStatus.set(originalName, DEPENDENCY_STATUS.ERROR);
      }
    }
  }

  /**
   * 创建依赖占位符
   * 
   * @param {string} originalName - 原始名称
   * @param {string} mappingPath - 映射路径
   * @returns {Function} 占位符函数
   */
  createDependencyPlaceholder(originalName, mappingPath) {
    return (...args) => {
      throw new Error(
        `Internal dependency '${originalName}' not yet refactored. ` +
        `Expected mapping: ${mappingPath}. ` +
        `This is a temporary placeholder during the refactoring process.`
      );
    };
  }

  /**
   * 获取第三方库
   * 
   * @param {string} name - 库名称
   * @returns {any} 库对象
   */
  getThirdPartyLibrary(name) {
    if (!this.isInitialized) {
      throw new Error('DependencyManager not initialized');
    }
    
    return this.thirdPartyManager.getLibrary(name);
  }

  /**
   * 获取映射的第三方函数
   * 
   * @param {string} originalName - 原始函数名
   * @returns {Function} 映射的函数
   */
  getMappedThirdPartyFunction(originalName) {
    if (!this.isInitialized) {
      throw new Error('DependencyManager not initialized');
    }
    
    return this.thirdPartyManager.getMappedFunction(originalName);
  }

  /**
   * 获取内部依赖
   * 
   * @param {string} originalName - 原始名称
   * @returns {Function} 依赖函数
   */
  getInternalDependency(originalName) {
    if (!this.isInitialized) {
      throw new Error('DependencyManager not initialized');
    }
    
    return this.internalDependencies.get(originalName);
  }

  /**
   * 替换内部依赖
   * 
   * @param {string} originalName - 原始名称
   * @param {Function} implementation - 实现函数
   */
  replaceInternalDependency(originalName, implementation) {
    if (this.internalDependencies.has(originalName)) {
      this.internalDependencies.set(originalName, implementation);
      this.dependencyStatus.set(originalName, DEPENDENCY_STATUS.AVAILABLE);
    }
  }

  /**
   * 批量替换内部依赖
   * 
   * @param {Object} implementations - 实现映射
   */
  replaceInternalDependencies(implementations) {
    for (const [originalName, implementation] of Object.entries(implementations)) {
      this.replaceInternalDependency(originalName, implementation);
    }
  }

  /**
   * 检查依赖状态
   * 
   * @param {string} name - 依赖名称
   * @returns {string} 依赖状态
   */
  getDependencyStatus(name) {
    return this.dependencyStatus.get(name) || DEPENDENCY_STATUS.MISSING;
  }

  /**
   * 获取所有依赖状态
   * 
   * @returns {Object} 依赖状态映射
   */
  getAllDependencyStatus() {
    const status = {
      thirdParty: {},
      internal: {}
    };

    // 第三方库状态
    for (const name of Object.keys(THIRD_PARTY_LIBRARIES)) {
      status.thirdParty[name] = this.thirdPartyManager.isLibraryAvailable(name) 
        ? DEPENDENCY_STATUS.AVAILABLE 
        : DEPENDENCY_STATUS.PLACEHOLDER;
    }

    // 内部依赖状态
    for (const [name, statusValue] of this.dependencyStatus.entries()) {
      status.internal[name] = statusValue;
    }

    return status;
  }

  /**
   * 生成依赖报告
   * 
   * @returns {Object} 依赖报告
   */
  generateDependencyReport() {
    const allStatus = this.getAllDependencyStatus();
    const thirdPartyStats = this.thirdPartyManager.getStats();
    
    const internalStats = {
      total: Object.keys(INTERNAL_DEPENDENCIES).length,
      available: Array.from(this.dependencyStatus.values())
        .filter(status => status === DEPENDENCY_STATUS.AVAILABLE).length,
      placeholder: Array.from(this.dependencyStatus.values())
        .filter(status => status === DEPENDENCY_STATUS.PLACEHOLDER).length,
      error: Array.from(this.dependencyStatus.values())
        .filter(status => status === DEPENDENCY_STATUS.ERROR).length
    };

    return {
      isInitialized: this.isInitialized,
      thirdParty: {
        ...thirdPartyStats,
        status: allStatus.thirdParty
      },
      internal: {
        ...internalStats,
        status: allStatus.internal
      },
      summary: {
        totalDependencies: thirdPartyStats.totalLibraries + internalStats.total,
        availableDependencies: thirdPartyStats.availableLibraries + internalStats.available,
        placeholderDependencies: (thirdPartyStats.totalLibraries - thirdPartyStats.availableLibraries) + internalStats.placeholder
      }
    };
  }

  /**
   * 生成package.json依赖
   * 
   * @returns {Object} 依赖对象
   */
  generatePackageJsonDependencies() {
    return this.thirdPartyManager.generatePackageJsonDependencies();
  }

  /**
   * 重置依赖管理器
   */
  reset() {
    this.isInitialized = false;
    this.internalDependencies.clear();
    this.dependencyStatus.clear();
  }
}

/**
 * 创建依赖管理器实例
 * 
 * @returns {DependencyManager} 依赖管理器实例
 */
function createDependencyManager() {
  return new DependencyManager();
}

// 全局依赖管理器实例
let globalDependencyManager = null;

/**
 * 获取全局依赖管理器实例
 * 
 * @returns {DependencyManager} 全局依赖管理器实例
 */
function getGlobalDependencyManager() {
  if (!globalDependencyManager) {
    globalDependencyManager = createDependencyManager();
  }
  return globalDependencyManager;
}

/**
 * 依赖工具函数集合
 */
const DependencyUtils = {
  // 检查依赖是否为第三方库
  isThirdPartyDependency: (name) => {
    return Object.keys(THIRD_PARTY_LIBRARIES).includes(name) || 
           Object.keys(FUNCTION_MAPPINGS).includes(name);
  },
  
  // 检查依赖是否为内部依赖
  isInternalDependency: (name) => {
    return Object.keys(INTERNAL_DEPENDENCIES).includes(name);
  },
  
  // 获取依赖映射路径
  getDependencyMapping: (name) => {
    return FUNCTION_MAPPINGS[name] || INTERNAL_DEPENDENCIES[name] || null;
  },
  
  // 生成依赖安装命令
  generateInstallCommands: () => {
    const dependencies = Object.values(THIRD_PARTY_LIBRARIES)
      .filter(config => config.version !== 'builtin')
      .map(config => `${config.package}@${config.version}`)
      .join(' ');
    
    return {
      npm: `npm install ${dependencies}`,
      yarn: `yarn add ${dependencies}`,
      pnpm: `pnpm add ${dependencies}`
    };
  }
};

module.exports = {
  // 主要类和函数
  DependencyManager,
  createDependencyManager,
  getGlobalDependencyManager,
  
  // 第三方库管理
  ThirdPartyLibraryManager,
  getGlobalThirdPartyManager,
  
  // 工具函数
  DependencyUtils,
  
  // 常量
  THIRD_PARTY_LIBRARIES,
  FUNCTION_MAPPINGS,
  INTERNAL_DEPENDENCIES,
  DEPENDENCY_STATUS
};
