/**
 * Claude Code - Third Party Libraries Manager
 * 
 * 重构来源: cli-format-all.js 第1-294776行 第三方库和工具函数
 * 重构时间: 2025-01-19
 * 
 * 第三方库依赖管理，替换内置的第三方库实现
 */

// Node.js 内置模块
const { createRequire } = require('node:module');

/**
 * 第三方库映射表
 * 将内置的第三方库实现替换为npm包
 */
const THIRD_PARTY_LIBRARIES = {
  // Shell命令解析 (第43-57行)
  'shell-quote': {
    package: 'shell-quote',
    version: '^1.8.1',
    description: 'Shell command parsing and escaping'
  },
  
  // 错误堆栈解析 (第950-1048行)
  '@sentry/utils': {
    package: '@sentry/utils',
    version: '^7.0.0',
    description: 'Sentry utilities for error handling'
  },
  
  // 事件处理 (第1050-1092行)
  'eventemitter3': {
    package: 'eventemitter3',
    version: '^5.0.1',
    description: 'High performance EventEmitter'
  },
  
  // HTTP客户端 (第276728-278809行)
  'axios': {
    package: 'axios',
    version: '^1.6.0',
    description: 'Promise based HTTP client'
  },
  
  // 加密和哈希 (第279156行)
  'crypto': {
    package: 'node:crypto',
    version: 'builtin',
    description: 'Node.js built-in crypto module'
  },
  
  // 文件系统操作 (第279199行)
  'fs-extra': {
    package: 'fs-extra',
    version: '^11.0.0',
    description: 'Extended file system operations'
  },
  
  // 路径处理 (第279305行)
  'path': {
    package: 'node:path',
    version: 'builtin',
    description: 'Node.js built-in path module'
  },
  
  // 进程管理 (第279465行)
  'execa': {
    package: 'execa',
    version: '^8.0.1',
    description: 'Process execution with better API'
  },
  
  // 终端颜色 (第279306-279463行)
  'chalk': {
    package: 'chalk',
    version: '^4.1.2',
    description: 'Terminal string styling'
  },
  
  // 命令行参数解析 (第279468-279736行)
  'commander': {
    package: 'commander',
    version: '^11.0.0',
    description: 'Command-line interface framework'
  },
  
  // 流处理 (第279738-280201行)
  'stream': {
    package: 'node:stream',
    version: 'builtin',
    description: 'Node.js built-in stream module'
  },
  
  // WebAssembly (第281797行)
  'yoga-layout': {
    package: 'yoga-layout-prebuilt',
    version: '^1.10.0',
    description: 'Flexbox layout engine'
  },
  
  // 字符串宽度计算 (第281830-281989行)
  'string-width': {
    package: 'string-width',
    version: '^7.0.0',
    description: 'Get the visual width of a string'
  },
  
  // ANSI转义序列 (第281991-283643行)
  'ansi-escapes': {
    package: 'ansi-escapes',
    version: '^6.0.0',
    description: 'ANSI escape codes for manipulating the terminal'
  },
  
  // React (第283736-285038行)
  'react': {
    package: 'react',
    version: '^18.0.0',
    description: 'JavaScript library for building user interfaces'
  },
  
  // 模糊搜索 (第346912-348038行)
  'fuse.js': {
    package: 'fuse.js',
    version: '^7.0.0',
    description: 'Lightweight fuzzy-search library'
  },
  
  // 数据验证 (第353225行)
  'zod': {
    package: 'zod',
    version: '^3.22.0',
    description: 'TypeScript-first schema validation'
  }
};

/**
 * 内置函数到第三方库的映射
 */
const FUNCTION_MAPPINGS = {
  // Shell命令相关
  'vFA': 'shell-quote.quote',
  'cFA': 'shell-quote.parse',
  
  // 错误处理相关
  'RYA': '@sentry/utils.createStackParser',
  'EFB': '@sentry/utils.getFunctionName',
  
  // HTTP请求相关
  'Pa': 'axios.create',
  'R31': 'axios.request',
  
  // 文件系统相关
  'TOA': 'fs-extra.existsSync',
  'TsB': 'fs-extra.mkdirSync',
  'PsB': 'fs-extra.readdirSync',
  'SsB': 'fs-extra.readFileSync',
  '_sB': 'fs-extra.writeFileSync',
  
  // 路径处理相关
  'ha': 'path',
  'ic0': 'path.resolve',
  'ka1': 'path.join',
  'nc0': 'path.dirname',
  
  // 进程执行相关
  'ovQ': 'execa.execFile',
  'tvQ': 'execa.execSync',
  'Ma1': 'execa.execSync',
  'bvQ': 'execa.execFile',
  'gvQ': 'execa.spawn',
  
  // 终端颜色相关
  'NK': 'chalk',
  'YA': 'chalk',
  
  // React相关
  'Oz': 'react',
  'oM': 'react',
  'GH': 'react',
  'Fi': 'react',
  
  // 模糊搜索相关
  'J9B': 'fuse.js.Fuse',
  'QM1': 'fuse.js.FuseIndex',
  
  // 数据验证相关
  'v': 'zod'
};

/**
 * 第三方库管理器类
 */
class ThirdPartyLibraryManager {
  constructor() {
    this.loadedLibraries = new Map();
    this.functionMappings = new Map();
    this.isInitialized = false;
  }

  /**
   * 初始化第三方库管理器
   * 
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      // 加载所有第三方库
      for (const [name, config] of Object.entries(THIRD_PARTY_LIBRARIES)) {
        await this.loadLibrary(name, config);
      }

      // 设置函数映射
      this.setupFunctionMappings();
      
      this.isInitialized = true;
    } catch (error) {
      throw new Error(`Failed to initialize third-party libraries: ${error.message}`);
    }
  }

  /**
   * 加载单个第三方库
   * 
   * @param {string} name - 库名称
   * @param {Object} config - 库配置
   * @returns {Promise<void>}
   */
  async loadLibrary(name, config) {
    try {
      if (config.package.startsWith('node:')) {
        // Node.js 内置模块
        const lib = require(config.package);
        this.loadedLibraries.set(name, lib);
      } else {
        // 第三方npm包
        const lib = require(config.package);
        this.loadedLibraries.set(name, lib);
      }
    } catch (error) {
      console.warn(`Failed to load library ${name}: ${error.message}`);
      // 创建占位符对象
      this.loadedLibraries.set(name, this.createLibraryPlaceholder(name, config));
    }
  }

  /**
   * 创建库的占位符对象
   * 
   * @param {string} name - 库名称
   * @param {Object} config - 库配置
   * @returns {Object} 占位符对象
   */
  createLibraryPlaceholder(name, config) {
    return new Proxy({}, {
      get: (target, prop) => {
        throw new Error(
          `Library ${name} (${config.package}@${config.version}) not available. ` +
          `Please install: npm install ${config.package}`
        );
      }
    });
  }

  /**
   * 设置函数映射
   */
  setupFunctionMappings() {
    for (const [originalName, mappingPath] of Object.entries(FUNCTION_MAPPINGS)) {
      const [libName, ...functionPath] = mappingPath.split('.');
      const lib = this.loadedLibraries.get(libName);
      
      if (lib) {
        let func = lib;
        for (const path of functionPath) {
          func = func[path];
        }
        this.functionMappings.set(originalName, func);
      }
    }
  }

  /**
   * 获取第三方库
   * 
   * @param {string} name - 库名称
   * @returns {any} 库对象
   */
  getLibrary(name) {
    if (!this.isInitialized) {
      throw new Error('ThirdPartyLibraryManager not initialized');
    }
    
    return this.loadedLibraries.get(name);
  }

  /**
   * 获取映射的函数
   * 
   * @param {string} originalName - 原始函数名
   * @returns {Function} 映射的函数
   */
  getMappedFunction(originalName) {
    if (!this.isInitialized) {
      throw new Error('ThirdPartyLibraryManager not initialized');
    }
    
    return this.functionMappings.get(originalName);
  }

  /**
   * 检查库是否可用
   * 
   * @param {string} name - 库名称
   * @returns {boolean} 是否可用
   */
  isLibraryAvailable(name) {
    const lib = this.loadedLibraries.get(name);
    return lib && typeof lib === 'object';
  }

  /**
   * 获取所有已加载的库
   * 
   * @returns {Array<string>} 库名称列表
   */
  getLoadedLibraries() {
    return Array.from(this.loadedLibraries.keys());
  }

  /**
   * 获取库的配置信息
   * 
   * @param {string} name - 库名称
   * @returns {Object|null} 配置信息
   */
  getLibraryConfig(name) {
    return THIRD_PARTY_LIBRARIES[name] || null;
  }

  /**
   * 生成package.json依赖
   * 
   * @returns {Object} 依赖对象
   */
  generatePackageJsonDependencies() {
    const dependencies = {};
    
    for (const [name, config] of Object.entries(THIRD_PARTY_LIBRARIES)) {
      if (config.version !== 'builtin') {
        dependencies[config.package] = config.version;
      }
    }
    
    return dependencies;
  }

  /**
   * 获取统计信息
   * 
   * @returns {Object} 统计信息
   */
  getStats() {
    const totalLibraries = Object.keys(THIRD_PARTY_LIBRARIES).length;
    const loadedLibraries = this.loadedLibraries.size;
    const availableLibraries = Array.from(this.loadedLibraries.keys())
      .filter(name => this.isLibraryAvailable(name)).length;
    
    return {
      isInitialized: this.isInitialized,
      totalLibraries,
      loadedLibraries,
      availableLibraries,
      functionMappings: this.functionMappings.size,
      unavailableLibraries: totalLibraries - availableLibraries
    };
  }
}

/**
 * 创建第三方库管理器实例
 * 
 * @returns {ThirdPartyLibraryManager} 管理器实例
 */
function createThirdPartyLibraryManager() {
  return new ThirdPartyLibraryManager();
}

// 全局第三方库管理器实例
let globalThirdPartyManager = null;

/**
 * 获取全局第三方库管理器实例
 * 
 * @returns {ThirdPartyLibraryManager} 全局管理器实例
 */
function getGlobalThirdPartyManager() {
  if (!globalThirdPartyManager) {
    globalThirdPartyManager = createThirdPartyLibraryManager();
  }
  return globalThirdPartyManager;
}

module.exports = {
  // 主要类和函数
  ThirdPartyLibraryManager,
  createThirdPartyLibraryManager,
  getGlobalThirdPartyManager,
  
  // 配置常量
  THIRD_PARTY_LIBRARIES,
  FUNCTION_MAPPINGS
};
